{"name": "superboss-model-web", "version": "1.4.8", "grayVersion": "0.0.63", "license": "ISC", "description": "superboss model web", "author": "", "keywords": [], "scripts": {"kdzs-start": "cd src/print && yarn start", "start": "npm run dev", "dev": "vite", "dev-https": "env-cmd -f ./envs/.env.dev-https vite", "dev-prod": "env-cmd -f ./envs/.env.dev-prod vite", "d": "env-cmd -f ./envs/.env.dev-local vite", "serve": "vite preview", "dev:ia": "node ./scripts/dev.js", "server": "webpack-dev-server --config config/webpack.config.dev.js", "lint": "eslint --fix --ext .ts --ext .tsx src/", "build-vite": "rimraf dist && cross-env NODE_ENV=development vite build", "build-oss": "env-cmd -f ./envs/.env-oss.prod npm run build", "build-prod": "env-cmd -f ./envs/.env.prod npm run build", "build-test": "env-cmd -f ./envs/.env.test npm run build", "build-gray": "env-cmd -f ./envs/.env.gray npm run build", "build-pre": "env-cmd -f ./envs/.env.pre npm run build", "build-gpre": "env-cmd -f ./envs/.env.gpre npm run build", "build-pre-oss": "env-cmd -f ./envs/.env-oss.pre npm run build", "build-erp-oss": "env-cmd -f ./envs/.env-oss.prod npm run build", "build-erp1": "env-cmd -f ./envs/.env.prod-p1 npm run build", "build-erp2": "env-cmd -f ./envs/.env.prod-p2 npm run build", "build-erpg-oss": "env-cmd -f ./envs/.env-oss.gray npm run build", "build-staging": "env-cmd -f ./envs/.env.staging npm run build", "build": "webpack --config config/webpack.config.prod.js", "build:ia": "node ./scripts/build.js", "commit": "git-cz", "release": "standard-version", "publish": "sh scripts/publish", "publish-gray": "sh scripts/publish_common", "tag": "sh scripts/tag", "gray-tag": "sh scripts/grayTag", "m_t": "sh scripts/merger coopTest", "m_m": "sh scripts/merger pre-master", "merger": "sh scripts/merger", "postinstall": "patch-package", "dev-webpack": "env-cmd -f ./envs/.env.dev npm run server --scripts-prepend-node-path", "dev-print": "env-cmd -f ./envs/.env.print-local npm run server --scripts-prepend-node-path"}, "devDependencies": {"@babel/core": "^7.11.5", "@babel/plugin-proposal-export-namespace-from": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.14.5", "@babel/plugin-proposal-private-methods": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@exyz/glib": "^0.4.4", "@kdzs/compile": "^0.6.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@rollup/plugin-inject": "^5.0.5", "@types/classnames": "^2.2.11", "@types/lodash": "^4.14.165", "@types/qrcode.react": "^1.0.2", "@types/react-highlight-words": "^0.16.4", "@types/react-loadable": "^5.5.4", "@types/react-window": "^1.8.5", "@types/styled-components": "^5.1.4", "@types/webpack-env": "^1.16.0", "@typescript-eslint/eslint-plugin": "4.0.1", "@typescript-eslint/parser": "4.0.1", "@vitejs/plugin-react": "^1.3.0", "@vitejs/plugin-react-refresh": "^1.3.1", "antd-dayjs-vite-plugin": "1.1.4", "autoprefixer": "^9.8.6", "babel-loader": "8.1.0", "babel-plugin-import": "^1.13.3", "babel-plugin-transform-decorators-legacy": "^1.3.5", "cache-loader": "^4.1.0", "chalk": "^4.1.0", "clean-webpack-plugin": "^3.0.0", "commitizen": "^4.3.0", "conventional-changelog-cli": "^2.1.1", "copy-webpack-plugin": "^6.4.1", "core-js": "^3.8.0", "cross-env": "^7.0.3", "css-loader": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "dotenv": "^16.0.1", "eslint": "^7.8.1", "eslint-config-airbnb": "^18.2.0", "eslint-loader": "^4.0.2", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.20.6", "eslint-plugin-react-hooks": "^4.1.0", "eslint-plugin-typescript": "^0.14.0", "execa": "^5.0.0", "file-loader": "^6.1.0", "fs-extra": "^9.0.1", "hard-source-webpack-plugin": "^0.13.1", "html-webpack-plugin": "^4.4.1", "husky": "^9.1.1", "inquirer": "^8.0.0", "less": "^4.1.2", "less-loader": "^6.2.0", "lint-staged": "^10.4.0", "lodash": "^4.17.20", "mini-css-extract-plugin": "^0.11.0", "optimize-css-assets-webpack-plugin": "^5.0.4", "ora": "^5.1.0", "postcss": "^8.2.12", "postcss-flexbugs-fixes": "4.1.0", "postcss-loader": "3.0.0", "postcss-normalize": "8.0.1", "postcss-preset-env": "6.7.0", "postcss-safe-parser": "4.0.1", "react-highlight-words": "^0.18.0", "react-refresh": "^0.13.0", "rimraf": "^3.0.2", "sass": "^1.29.0", "sass-loader": "^10.0.1", "standard-version": "^9.5.0", "style-loader": "^1.3.0", "styled-components": "^5.2.1", "terser-webpack-plugin": "^4.2.0", "tslib": "^2.3.0", "type-fest": "^2.12.2", "typings-for-css-modules-loader": "^1.7.0", "url-loader": "^4.1.0", "vite": "^2.4.2", "vite-plugin-cesium": "^1.2.5", "webpack": "^4.44.1", "webpack-bundle-analyzer": "^4.6.1", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.0", "webpack-merge": "^5.1.3", "webpackbar": "^5.0.0-3"}, "dependencies": {"@ant-design/icons": "^4.6.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@ebay/nice-modal-react": "^1.2.13", "@hot-loader/react-dom": "^17.0.2", "@jiaminghi/data-view-react": "^1.2.5", "@kdzs/print-device": "^2.7.0", "@open-fxg/bixi-react": "^0.1.1", "@open-fxg/bodyguard-react": "^0.0.17-alpha.1", "@raycloud/ads": "^1.1.72", "@raycloud/sass": "^1.0.8", "@sentry/browser": "5.5.0", "@types/jest": "^27.0.1", "@types/js-cookie": "^2.2.6", "@types/node": "^16.7.13", "@types/qs": "^6.9.5", "@types/react": "^17.0.16", "@types/react-dom": "^17.0.9", "@types/react-router-config": "^5.0.1", "@types/react-router-dom": "^5.1.5", "ahooks": "3.8.0", "ali-react-table": "^2.6.1", "ali-react-table-threshold": "^2.6.1", "antd": "4.18.2", "array-move": "^4.0.0", "axios": "^0.20.0", "classnames": "^2.2.6", "dayjs": "1.10.7", "echarts": "^5.4.3", "env-cmd": "^10.1.0", "event-source-polyfill": "^1.0.31", "events": "^3.3.0", "history": "4.10.1", "html2canvas": "^1.4.1", "husky": "^9.1.1", "immutability-helper": "^3.1.1", "jquery": "1.11.0", "js-cookie": "^2.2.1", "localforage": "^1.10.0", "map-age-cleaner": "^0.2.0", "md": "^0.6.0", "md5": "^2.3.0", "mobx": "^6.3.2", "mobx-react": "^7.2.0", "mobx-react-lite": "^4.0.7", "package": "^1.0.1", "patch-package": "^6.4.7", "pinyin-match": "^1.1.9", "prop-types": "^15.8.1", "qrcode-generator": "1.4.4", "qrcode.react": "^1.0.1", "qs": "^6.9.4", "rc-dialog": "^8.5.1", "rc-picker": "^4.8.2", "rc-tooltip": "^5.1.1", "react": "^17.0.2", "react-activation": "^0.12.2", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^14.0.4", "react-dnd-html5-backend": "^14.0.2", "react-dom": "^17.0.2", "react-draggable": "4.4.6", "react-error-overlay": "^6.0.9", "react-hot-loader": "^4.13.0", "react-infinite-scroll-component": "^6.1.0", "react-json-view": "^1.21.3", "react-loadable": "^5.5.0", "react-resizable": "^3.0.4", "react-rnd": "^10.3.4", "react-router-config": "^5.1.1", "react-router-dom": "^5.2.0", "react-sortable-hoc": "^2.0.0", "react-virtualized": "^9.22.5", "react-window": "^1.8.11", "regenerator-runtime": "^0.13.7", "rollup-plugin-copy": "^3.4.0", "socket.io-client": "^4.2.0", "speed-measure-webpack-plugin": "^1.5.0", "ssr-window": "^4.0.2", "swiper": "^9.4.1", "tsscmp": "^1.0.6", "typescript": "^4.0.2", "virtualizedtableforantd4": "1.1.4", "web-vitals": "^2.1.0", "webpack-plugin-serve": "^1.6.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "plugins": [["@babel/plugin-proposal-decorators", {"legacy": true}]]}