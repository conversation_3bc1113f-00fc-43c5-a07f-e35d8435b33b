<!DOCTYPE html>
<html>

<head id="Head1" runat="server">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>订购 | 快递助手ERP</title>
  <link rel="icon" href="./images/favicon.png" />
  <meta name="description" content="快递助手系列产品，淘宝平台打印快递单类工具排名第一，全平台拥有十万商家；极佳的交互设计体验" />
  <meta name="keywords" content="打印发货单,打印快递单,批量发货,淘宝打印工具,快递助手,批量打印快递单" />
  <link rel="stylesheet" type="text/css" href="css/order.css" />
  <!-- <link href="//unpkg.com/layui@2.7.6/dist/css/layui.css" rel="stylesheet"> -->
  <style>
    .order-info-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: none;
    }

    .background-div {
      background: #000;
      opacity: .6;
      width: 100%;
      height: 100%;
    }

    .modal {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;
      background: #fff;
      width: 650px;
      height: 228px;
      border-radius: 4px;
    }

    .order-modal-content {
      height: 111px;
      padding: 38px 25px 0;
    }

    .order-current-modal-content,
    .order-now-modal-content {
      height: 80px;
    }

    .modal-title {
      font-family: "microsoft yahei", "simsun";
      text-indent: 15px;
      height: 34px;
      font-size: 14px;
      font-weight: bold;
      color: #365064;
      line-height: 34px;
      background-color: #ffb21e;
      border-top-left-radius: 2px;
      border-top-right-radius: 2px;
      border-bottom: 1px solid #ee9d00;
      border-top: 1px solid #ffc14c;
      position: relative;
    }

    .modal-close {
      cursor: pointer;
      position: absolute;
      top: -1px;
      right: 0;
      z-index: 9998;
      height: 25px;
      width: 45px;
      display: block;
      background: url(./images/pup_closedbt.gif) 0 0 no-repeat;
    }

    .modal-close:hover {
      background: url(./images/pup_closedbt.gif) 0 -35px no-repeat;
    }

    .modal-footer {
      height: 42px;
      border-top: 1px solid #eaeaea;
      background-color: #f3f3f3;
      border-bottom-left-radius: 2px;
      border-bottom-right-radius: 2px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 0px 20px;
    }

    .table-wrapper {
      width: 600px;
      margin: 0 auto;
      text-align: center;
      border-bottom: 1px solid #dcdcdc;
      border-right: 1px solid #dcdcdc;
    }

    .table-wrapper td {
      border-top: 1px solid #dcdcdc;
      border-left: 1px solid #dcdcdc;
    }

    .th-top {
      background: #E2E2E2;
    }

    .tr-bottom {
      color: #333;
    }

    .order-amount {
      font-size: 20px;
      color: #FF3131;
    }

    .common-modal-button {
      display: inline-block;
      height: 23px;
      line-height: 23px;
      font-size: 12px;
      color: #365064;
      cursor: pointer;
      padding: 0 17px;
      background-color: #FFDB95;
      border: 1px solid #F8C05D;
      border-radius: 2px;
    }

    .confirm-button:hover {
      background-color: #FFCD6D;
    }

    .cancel-button {
      margin-left: 20px;
      background-color: #E4E4E4;
      border: 1px solid #C6C6C6;
    }

    .red-tips {
      color: #FF3131;
      font-weight: bold;
      padding-top: 8px;
    }
  </style>
</head>

<body>
  <span class="u-top"></span>
  <div class="m-header">
    <div class="m-header-c">
      <span><span class="mallName"></span><img src="./images/erpLogo.png" alt="" srcset=""></span>
    </div>
  </div>
  <div class="m-content clearfix">
    <div class="m-content-l">
      <img src="./images/bLogo.png" alt="" srcset="">
      <div class="l-info">
        <div class="l-info-block bbd">
          <p class="l-info-block-p1">公司：</p>
          <p class="l-info-block-p2">杭州其乐融融科技有限公司</p>
        </div>
        <div class="l-info-block bbd">
          <p class="l-info-block-p1">电话：</p>
          <p class="l-info-block-p2">131-0771-2026</p>
        </div>
        <div class="l-info-block">
          <p l-info-block-p1>QQ：</p>
          <p class="l-info-block-p2">800172114</p>
        </div>
        <div class="l-info-block hide">
          <p l-info-block-p1>邮箱：</p>
          <p class="l-info-block-p2 colfd8"><EMAIL></p>
        </div>
      </div>
      <div class="l-notice">
        <p class="l-notice-t">快递助手公告</p>
        <div class="l-notice-c">
          <p class="l-notice-c-p">
            杭州其乐融融科技有限公司，旗下快递助手入驻淘宝、拼多多、抖音、快手、1688等30+电商平台。快递助手ERP由助手团队倾情力作，集多平台多店铺一站式处理、商品、库存、采购等优质功能于一体，为商家提供更全面订单处理解决方案。
          </p>
          <p class="l-notice-c-s">客服在线时间：</p>
          <p class="l-notice-c-t">09:00-23:30，客服不在线时可直接留言阐述问题，我们会尽快给您回复。</p>
        </div>
      </div>
    </div>
    <div class="m-content-r">
      <div class="content-old">
        <p class="r-t">快递助手ERP</p>
        <p class="r-describe">中小商家易上手的轻量级ERP，实现多平台多店铺订单一站式处理：批量/单个打印、订单自动合并、绑定商品关系、退货入库、售后统计、采购单打印等</p>
        <p>
          <span class="r-lab">价&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp格：</span>
          <span class="r-price">
            <span class="level-1 hide">
              <span id="orderPrice">2880.00</span> 元
            </span>
            <span class="level-2 hide">
              <span id="orderPrice">5880.00</span> 元
            </span>
          </span>
        </p>
        <p>
          <span class="r-lab">实&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp付：</span>
          <span class="r-price" style="position:relative;">
            <span class="level-1 hide">
              <span class="r-payment" id="r-payment">2880.00</span> 元
            </span>
            <span class="level-2 hide">
              <span class="r-payment" id="r-payment">5880.00</span> 元
            </span>
          </span>
        </p>
        <ul class="r-versions clearfix">
          <li>收费方式：</li>
          <li name="purchaseVersions" class="versionsLi version-1-select" version="1">标配版</li>
          <li name="purchaseVersions" class="versionsLi version-2-select" version="2">高配版</li>
        </ul>
        <!-- <ul class="r-versions clearfix">
                    <li>收费方式：</li>
                    <li name="purchaseVersions" class="versionsLi mouseClickLi" version="1">周期付费版</li>
                </ul class="r-versions clearfix"> -->
        <ul class="r-versions clearfix mt8">
          <li>周&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp期：</li>
          <li name="purchaseCycle" class="versionsLi mouseClickLi" cycle="2">
            一年
          </li>
        </ul>
        <div class="r-expand">
          <!-- <div class="r-expand-line">单量<span class="r-expand-em">不限制</span></div> -->
          <div class="r-expand-line">
            可绑定店铺数
            <span class="level-1 hide r-expand-em">5个</span>
            <span class="level-2 hide r-expand-em">不限制</span>
          </div>
        </div>
        <div class="r-u">
          <a href="javascript:;" id="purchaseNow" class="u-immediately">立即订购</a>
          <a href="javascript:;" id="goToPurchaseList" class="u-goList">我的订购记录</a>
        </div>
      </div>

      <ul class="m-taps clearfix">
        <li class="active">服务详情</li>
      </ul>
      <div class="m-tap-info" style="margin-bottom: 80px;">
        <div class="g-service clearfix">
          <span class="l">在线客服：
            <div id="BizQQWPA" class="u-serviceqq">
              <a href="https://wpa1.qq.com/dSY8GJNm?_type=wpa&qidian=true" target="_blank"><img src="./images/qqhelp.gif"></a>
            </div>
          </span>
          <span class="l">客服电话：</span>
          <span class="l m-tap-tel">131-0771-2026</span>
          <span class="r">技术/客服在线时间：09:00-23:30（周一至周日）</span>
        </div>
        <img src="./images/productDetail3.png" style="width: 980px;" alt="">
      </div>
    </div>
  </div>


  <div class="order-info-wrapper" id='order-info-wrapper'>
    <div class="background-div"></div>
    <div class="modal">
      <div class="modal-title">
        确认您的订单信息
        <span class="cancel-modal modal-close"></span>
      </div>
      <div class='order-modal-content'>
        <table border="0" cellspacing="0" class="table-wrapper">
          <tr class="th-top">
            <td>版本</td>
            <td>周期</td>
            <td>有效期</td>
            <td>应付金额</td>
          </tr>
          <tr class="tr-bottom">
            <td><span class="order-brand"></span></td>
            <td><span class="order-cylcle">一年</span></td>
            <td><span class="order-date"></span></td>
            <td><span class="order-amount"></span>元</td>
          </tr>
        </table>
        <div class="red-tips">说明：将跳转到支付宝付款页面</div>
        <div class="protocol-wrap">
          <input type="checkbox" class="agree-checkbox" />
          <div>
            已阅读并同意签署
            <span class="link-text protocol-link-text">
              《快递助手ERP 销售协议》
            </span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <span class="common-modal-button confirm-button">去付款</span>
        <span class="common-modal-button cancel-button  cancel-modal">取消</span>
      </div>
    </div>
  </div>

  <div class="order-info-wrapper" id='order-update-info-wrapper'>
    <div class="background-div"></div>
    <div class="modal">
      <div class="modal-title">
        确认您的订单信息
        <span class="cancel-modal modal-close"></span>
      </div>
      <div class='order-current-modal-content'>
        <b style="margin-left: 24px;">当前服务</b>
        <table border="0" cellspacing="0" class="table-wrapper">
          <tr class="th-top">
            <td>版本</td>
            <td>周期</td>
            <td>剩余有效期</td>
            <td>剩余金额</td>
          </tr>
          <tr class="tr-bottom">
            <td><span class="order-brand">标配版</span></td>
            <td><span class="order-cylcle">一年</span></td>
            <td><span class="order-date"></span></td>
            <td><span class="order-amount"></span>元</td>
          </tr>
        </table>
      </div>
      <div class='order-new-modal-content'>
        <b style="margin-left: 24px;">新订服务</b>
        <table border="0" cellspacing="0" class="table-wrapper">
          <tr class="th-top">
            <td>版本</td>
            <td>周期</td>
            <td>有效期</td>
            <td>应付金额</td>
          </tr>
          <tr class="tr-bottom">
            <td><span class="order-brand">高配版</span></td>
            <td><span class="order-cylcle">一年</span></td>
            <td><span class="order-date"></span></td>
            <td><span class="order-amount"></span>元</td>
          </tr>
        </table>
      </div>
      <div class="protocol-wrap">
        <input type="checkbox" class="agree-checkbox" style="margin-left: 24px;" />
        <div>
          已阅读并同意签署
          <span class="link-text protocol-link-text">
            《快递助手ERP 销售协议》
          </span>
        </div>
      </div>
      <div class="modal-footer" style="margin-top: 12px; display: flex; align-items: center;">
        <span style="margin-right: 20px; font-size: 20px; color: #FF3131; font-weight: bold;">实付金额：<span class="real-pay"></span></span>
        <span class="common-modal-button confirm-button">去付款</span>
        <span class="common-modal-button cancel-button  cancel-modal">取消</span>
      </div>
    </div>
  </div>
  <div>
    <div>
      <div id="custom-modal-root">
        <div class="custom-modal-mask-hidden"></div>
        <div class="custom-modal-wrap">
          <div class="custom-modal" style="width: 70vw">
            <div class="custom-modal-content">
              <div class="custom-modal-header">
                <div class="custom-modal-title">快递助手ERP商家服务软件销售及服务合同</div>
              </div>
              <div class="custom-modal-body">
                <iframe src="./protocol.html" frameborder="0" width="100%" height="100%"></iframe>
              </div>
              <div class="custom-modal-footer">
                <button type="button" class="btn btn-default" id="modal-cancel-button">
                  <span>取 消</span>
                </button>
                <button type="button" class="btn btn-primary" id="modal-ok-button">
                  <span>同 意</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
<script src="./js/jquery.min.js"></script>
<script src="./js/layer.js"></script>
<script type="text/javascript">
  (function(win) {
    var state = {
      userId: '',
      type: 1,
      //订购周期 1："七天"，2："一年");
      purchaseCycle: '2',
      //订购版本 0："免费版" 1："付费版");
      purchaseVersions: '1',
      level: '1',
      token: '',
      isLoading: false,
      outTradeNo: '',
      amount: 0,
    }

    var pointInfo = {
      //点位
      mainDialog: {
        point: '12534.33805.33822.38517.38518',
        fm: '8533'
      },
    }

    var setState = function(obj) {
      state = Object.assign({}, state, obj);
    }

    var utils = {
      getQueryString: function(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r !== null) {
          return decodeURIComponent(r[2]);
        }
        return null;
      },

      triggerPoint: function(params) {
        var $body = $('body'),
          $img = $('<img style="position:absolute;bottom:0;left:-100px;width:0;height:0;" />'),
          keyMap = {
            app_type: 'url_tj',
            log_type: 'click',
            point: '',
            _fm: '',
            taobaoNick: ''
          };
        var queryStr;
        params.rad = Math.random();
        queryStr = $.param($.extend({}, keyMap, params));
        $img.attr('src', '//ftj.superboss.cc/tj.jpg?' + queryStr);
        $img.on('load', function() {
          $img.remove();
        });
        $body.append($img);
      }
    }

    var init = {
      initQuery: function() {
        var userId = utils.getQueryString('userId');
        var type = utils.getQueryString('type');
        var token = utils.getQueryString('token');
        var level = utils.getQueryString('level');

        setState({
          userId: userId,
          token: token,
          type: type,
          level,
        });
        if (level === '2') {
          $('.level-2').show();
          $('.version-2-select').addClass('mouseClickLi')
          $('.version-1-select').addClass('disabled')

        } else {
          $('.level-1').show();
          $('.version-1-select').addClass('mouseClickLi')
        }
      },

      initEvent: function() {
        console.log('initEvent');
        $('.version-1-select').click(function() {
          if ($('.version-1-select').hasClass('disabled')) {
            return;
          }
          utils.triggerPoint({
            point: '64661.64676.65443.74938.74939.74940'
          })
          $('.level-1').show();
          $('.level-2').hide();
          $('.version-1-select').addClass('mouseClickLi')
          $('.version-2-select').removeClass('mouseClickLi')
        });

        $('.version-2-select').click(function() {
          utils.triggerPoint({
            point: '64661.64676.65443.74938.74939.74941'
          })
          $('.level-2').show();
          $('.level-1').hide();
          $('.version-1-select').removeClass('mouseClickLi')
          $('.version-2-select').addClass('mouseClickLi')
        });

        $('#purchaseNow').click(function() {
          console.log('purchaseNow', state.userId, state)
          if (!state.userId || state.isLoading) {
            layer.confirm('您好没有登录，请先登录', {
              title: '提示',
              btn: ['登录', '取消'] //按钮
            }, function() {
              location.href = location.origin + '/index.html#/login'
            }, function() {

            });
            return;
          }
          utils.triggerPoint({
            point: '64661.64676.65443.74938.74942.74946'
          })

          setState({
            isLoading: true
          });
          // 标配版改为3 1为老的标配版 只有老标配版续费才是老标配版
          let orderType = $('.version-1-select').hasClass('mouseClickLi') ? 3 : 2;
          if (state.level === "1" && orderType === 3) orderType = 1;


          $.ajax({
            url: '/orderRecord/addOrderRecord',
            type: 'GET',
            data: {
              cycle: state.purchaseCycle,
              level: orderType,
              userId: state.userId,
              fromUserId: state.token || '',
            },
            success: function(rJson) {
              try {
                if (rJson.success) {
                  var data = rJson.data;
                  setState({
                    outTradeNo: data.outTradeNo,
                    amount: data.amount
                  });
                  if (data.orderType === 1 || data.orderType === 3) {
                    $('.agree-checkbox').prop('checked', false);
                    $('#order-info-wrapper').show();
                    $('#order-info-wrapper .order-brand').text(orderType === 2 ? '高配版' : '标配版');
                    $('#order-info-wrapper .order-cylcle').text(data.cycle);
                    $('#order-info-wrapper .order-cylcle').text(data.cycle);
                    $('#order-info-wrapper .order-date').text(data.startTime.substr(0, 10) + ' 至 ' + data.expiryTime.substr(0, 10));
                    $('#order-info-wrapper .order-amount').text(Number(data.amount / 100).toFixed(2));
                  } else if (data.orderType === 2) {
                    $('.agree-checkbox').prop('checked', false);
                    $('#order-update-info-wrapper').show();
                    $('#order-update-info-wrapper .order-current-modal-content .order-cylcle').text(data.cycle);
                    $('#order-update-info-wrapper .order-current-modal-content .order-date').text(data.startTime.substr(0, 10) + ' 至 ' + data.expiryTime.substr(0, 10));
                    $('#order-update-info-wrapper .order-current-modal-content .order-amount').text(Number(data.oldLevelAmount / 100).toFixed(2));
                    $('#order-update-info-wrapper .order-new-modal-content .order-cylcle').text(data.cycle);
                    $('#order-update-info-wrapper .order-new-modal-content .order-date').text(data.startTime.substr(0, 10) + ' 至 ' + data.expiryTime.substr(0, 10));
                    $('#order-update-info-wrapper .order-new-modal-content .order-amount').text(Number(data.newLevelAmount / 100).toFixed(2));
                    $('#order-update-info-wrapper .real-pay').text(Number(data.amount / 100).toFixed(2));
                  }
                } else {
                  layer.msg('订购失败:' + rJson.errorMessage, {
                    offset: '32px'
                  });
                }
              } catch (error) {
                layer.msg('订购异常，请刷新重试，或联系客服处理', {
                  offset: '32px'
                });
                console.log(error)
              }
              setState({
                isLoading: false
              })
            },
            error: function(err) {
              layer.msg(err.message || '订购失败', {
                offset: '32px'
              });
              setState({
                isLoading: false
              });
            }
          });
        });

        $('.confirm-button').click(function() {
          utils.triggerPoint({
            point: '64661.64676.65443.74938.74942.74944'
          })
          // 判断是否已经阅读协议
          const isAgree = $(".agree-checkbox").is(':checked');
          if (isAgree) {
            location.href = '/orderRecord/getAliPayPage?userId=' + state.userId + '&amount=' + state.amount + '&outTradeNo=' + encodeURIComponent(state.outTradeNo);
          } else {
            layer.msg("请阅读并确认签署《快递助手ERP销售协议》后完成支付");
          }
        });

        $('.cancel-modal').click(function() {
          $('#order-info-wrapper').hide();
          $('#order-update-info-wrapper').hide();
        });

        $('#modal-cancel-button').click(function() {
          $('#custom-modal-root').hide();
          $('.agree-checkbox').prop('checked', false);
        })
        $('#modal-ok-button').click(function() {
          $('#custom-modal-root').hide();
          $('.agree-checkbox').prop('checked', true);
        })
        $('.protocol-link-text').click((function() {
          $('#custom-modal-root').show();
        }))
        $('#goToPurchaseList').click(function() {
          window.location.href = `payList.html?userId=${state.userId}&level=${state.level}`;
        })

        //回到顶部
        $('.u-top').on('click', function(event) {
          $('html,body').animate({
            scrollTop: 0
          }, 200);
        });

      },
    }


    win.onload = function() {
      init.initQuery();
      init.initEvent();
    };

  })(window)
</script>

</html>