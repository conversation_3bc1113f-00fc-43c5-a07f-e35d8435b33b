import {
	TradeGetTradeEncryptOutOrderInfoRequest,
	TradeGetTradeEncryptOutOrderInfoResponse,
	TradeBatchSendRequest,
	TradeBatchSendResponse,
	TradeBatchUpdateMemoRequest,
	TradeBatchUpdateMemoResponse,
	TradeBatchUpdatePendingStatusRequest,
	TradeBatchUpdatePendingStatusResponse,
	TradeBatchUpdateTradeFlagRequest,
	TradeBatchUpdateTradeFlagResponse,
	TradePddControlDecryptV1ReceiverInfoRequest,
	TradePddControlDecryptV1ReceiverInfoResponse,
	TradePrintSetGetPrintSetResponse,
	TradePrintSetUpdatePrintSetRequest,
	TradePrintSetUpdatePrintSetResponse,
	TradeGetCountForIndexResponse,
	TradeQueryWarningOrderSummaryResponse,
	TradeQueryWarningCountForShipFailResponse,
	TradeUserAddUserColumnConfigRequest,
	TradeUserAddUserColumnConfigResponse,
	TradeUserGetUserColumnConfigResponse,
	TradeUserUpdateShopSyncConfigResponse,
	TradeCodeResponse,
	TradeUserGetUserCustomSetResponse,
	TradeUserAddUserCustomSetRequest,
	TradeUserAddUserCustomSetResponse,
	TradeExportSaveConfigRequest,
	TradeExportRequest,
	TradeExportGetConfigResponse,
	TradeBatchDecryDataRequest,
	TradeBatchDecryDataResponse,
	TradeManualMergeTradeResponse,
	TradeManualMergeTradeRequest,
	TradeOperateLogRequest,
	TradeOperateLogResponse,
	TradeQueryStockInfoRequest,
	TradeQueryStockInfoResponse,
	TradeUserOperateShopRequest,
	TradeOperateLogAddRequest,
	TradeOperateLogAddResponse,
	TradeInfoBlurRequest,
	TradeInfoBlurResponse,
	TradeTbGetYchSignResponse,
	TradeTbTopOnceTokenResponse,
	ItemFilterSettingSelectResponse,
	ItemFilterSettingSaveRequest,
	ItemFilterSettingSaveResponse,
	TradeTradeIgnoreRequest,
	TradeTradeIgnoreResponse,
	TradeTradeDetailGetRequest,
	TradeTradeDetailGetResponse,
	PrintGetGroupInfoResponse,
	TradeBatchSecretExtendRequest,
	TradeBatchSecretExtendResponse,
	TradeOccupiedOpsRequest,
	TradeOccupiedOpsResponse,
	TradeQueryTradeByExNumberRequest,
	TradeQueryTradeByExNumberResponse,
	TradeBatchUpdateIgnoreRequest,
	TradeBatchUpdateIgnoreResponse,
	TradeDispenseRequest,
	TradeDispenseResponse,
	TradeGetTradeAllotStockNumByTidListResponse,
	TradeGetTradeAllotStockNumByTidListRequest,
	TradeUpdateTradeOccupiedRequest,
	TradeUpdateTradeOccupiedResponse,
	TradeBatchChangeTradeItemRequest,
	TradeBatchChangeTradeItemResponse,
	TradeLogisticsGetLogisticsDetailForErpRequest,
	TradeLogisticsGetLogisticsDetailForErpResponse,
	TradeModifyAddressRequest,
	TradeModifyAddressResponse,
	TradeLogisticsGetLogisticsCompanyInfoByYdNoRequest,
	TradeLogisticsGetLogisticsCompanyInfoByYdNoResponse,
	TradeAsyncLocalItemSnapShotRequest,
	TradeQueryLocalItemSnapShotResultRequest,
	TradeAsyncLocalItemSnapShotResponse,
	TradeQueryLocalItemSnapShotResultResponse,
	TradeBatchUpdateTradeSnapshotRequest,
	TradeBatchUpdateTradeSnapshotResponse,
	ItemItemEditSysItemInfoInTradeRequest,
	ItemItemEditSysItemInfoInTradeResponse,
	TradeBatchResendRequest,
	TradeBatchResendResponse,
	TradeBatchUpdateWarningRequest,
	TradeBatchUpdateWarningResponse,
	TradeGetRedDotMarketResponse,
	TradeQueryTradeNoticeWarnResponse,
	TradeQueryTradeWarningRequest,
	TradeQueryTradeWarningResponse,
	TradeQueryTradeWarningCountResponse,
	TradeQueryTradeWarningCountRequest,
	TradeDeleteTradeWarnTipsCacheResponse,
	TradeGetWarningDetailRequest,
	TradeGetWarningDetailResponse,
	TradeUpdateOrderSerialNumberRequest,
	TradeUpdateOrderSerialNumberResponse,
	TradeBatchUpdateUserQuickNotesResponse,
	TradeGetUserQuickNotesListResponse,
	TradeBatchUpdateUserQuickNotesRequest,
	TradeDeleteUserQuickNotesRequest,
	TradeDeleteUserQuickNotesResponse,
	TradeAddUserQuickNotesResponse,
	TradeAddUserQuickNotesRequest,
	TradeBatchUpdateSysMemoResponse,
	TradeBatchUpdateSysMemoRequest,
	TradeOrderSplitOrderSplitRequest,
	TradeOrderSplitOrderSplitResponse,
	TradeOrderSplitCancelSplitRequest,
	TradeOrderSplitCancelSplitResponse,
	PrintPreCheckPrintLimitResponse,
	PrintPreCheckTradeLimitResponse,
	TradeOrderSplitCloseSystemOrderRequest,
	TradeOrderSplitCloseSystemOrderResponse,
	TradeQueryScanTradeRecordByExNumberResponse,
	TradeUpdateQueryTradeOrderConfigRequest,
	TradeUpdateQueryTradeOrderConfigResponse,
	TradeGetQueryTradeOrderConfigResponse,
	TradeSaveCustomQueryTradeOrderResponse,
	TradeSaveCustomQueryTradeOrderRequest,
	TradeDelCustomQueryTradeOrderRequest,
	TradeDelCustomQueryTradeOrderResponse,
	TradeQueryTradeItemCountRequest,
	TradeQueryTradeItemCountResponse,
	TradeBatchUpdateOrderSerialNumberRequest,
	TradeBatchUpdateOrderSerialNumberResponse,
	TradeUpdateSysMemoPicResponse,
	TradeUpdateSysMemoPicRequest,
	TradeManualSplitMergeTradeRequest, TradeManualSplitMergeTradeResponse,
	TradeLivePrintGetLastTaskV2Request, TradeLivePrintGetLastTaskV2Response,
	TradeLivePrintCloseTaskRequest,
	TradeLivePrintCloseTaskResponse,
	TradeLivePrintCreateTaskRequest,
	TradeLivePrintCreateTaskResponse,
	TradeLivePrintLiveTradeRelateNumStatisticsRequest,
	TradeLivePrintLiveTradeRelateNumStatisticsResponse,
	TradeLivePrintQueryTaskListResponse,
	TradeLivePrintSelectLiveTradeRecordCountRequest,
	TradeLivePrintSelectLiveTradeRecordCountResponse,
	TradeLivePrintSelectLiveTradeRecordWithPageRequest,
	TradeLivePrintSelectLiveTradeRecordWithPageResponse,
	TradeLivePrintUpdateLiveTradePrintStatusRequest,
	TradeLivePrintUpdateLiveTradePrintStatusResponse,
	TradeQueryShopTradeCountRequest,
	TradeQueryShopTradeCountResponse,
	TradeBatchAppendSendRequest, TradeBatchAppendSendResponse,
	TradePrintGetBgKddInfoListByExNumberRequest, TradePrintGetBgKddInfoListByExNumberResponse,
	TradeBatchGetBicOrderCodeRequest, TradeBatchGetBicOrderCodeResponse,
	TradeBatchBindBicOrderCodeRequest, TradeBatchBindBicOrderCodeResponse,
	TradeQuerySendTimeoutAbnormalInfoResponse,
	TradeOrderSplitManualMergeTradeRequest,
	TradeOrderSplitManualMergeTradeResponse
} from '@/types/trade/index';
import {
	FastRefundSelectRefundByPtTidListRequest,
	FastRefundSelectRefundByPtTidListResponse,
	FastRefundFastRefundInStockRequest,
	FastRefundFastRefundInStockResponse
} from '@/types/trade/index/exception';
import { dealUpdateMemoRes, dealUpdateSysMemoRes } from '@/utils/dealHttpResponse';
import axios, { instance } from '@/utils/request';
import { ResponseTypeOfKDZS } from "../../types/schemas/common/index";

/**
* 关闭系统订单
*/
export function TradeOrderSplitCloseSystemOrderApi(data: TradeOrderSplitCloseSystemOrderRequest) {
	return axios.post<unknown, TradeOrderSplitCloseSystemOrderResponse>('/trade/order/split/closeSystemOrder', data).then(res => res.data);
}

/**
* 订单拆分
*/
export function TradeOrderSplitOrderSplitApi(data: TradeOrderSplitOrderSplitRequest) {
	return axios.post<unknown, TradeOrderSplitOrderSplitResponse>('/trade/order/split/orderSplit', data).then(res => res.data);
}

/**
* 订单取消拆分
*/
export function TradeOrderSplitCancelSplitApi(data: TradeOrderSplitCancelSplitRequest) {
	return axios.post<unknown, TradeOrderSplitCancelSplitResponse>('/trade/order/split/cancelSplit', data).then(res => res.data);
}
/**
* 查询当前用户的高级设置
*/
export function TradePrintSetGetPrintSetApi(data: {}) {
	return axios.post<unknown, TradePrintSetGetPrintSetResponse>('/trade/printSet/getPrintSet', data).then(res => res.data);
}

/**
* 查询当前用户的高级设置
*/
export function TradePrintSetGetPrintPlatTypeApi() {
	return axios.post<unknown, TradePrintSetGetPrintSetResponse>('/trade/printSet/getDefaultPlatform', {}).then(res => res.data);
}

/**
* 更新当前用户的高级设置
*/
export function TradePrintSetUpdatePrintSetApi(data: TradePrintSetUpdatePrintSetRequest) {
	return axios.post<unknown, TradePrintSetUpdatePrintSetResponse>('/trade/printSet/updatePrintSet', data).then(res => res);
}

/**
* 更新当前用户的高级设置
*/
export function TradePrintSetUpdateWarningConfigSetApi(data: TradePrintSetUpdatePrintSetRequest) {
	return axios.post<unknown, TradePrintSetUpdatePrintSetResponse>('/trade/printSet/updateWarningConfig', data).then(res => res);
}

// TODO 完善any
/**
* 单号回显
*/
export function getBatchSids(data: any) {
	return axios.post<unknown, TradePrintSetUpdatePrintSetResponse>('/print/center/getBatchSids', data, {
		headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
	}).then(res => res);
}
/**
* 获取模板组详情
*/
export function getGroupInfo(data: any) {
	return axios.post<unknown, PrintGetGroupInfoResponse>('/print/center/group/getGroupInfo', data, {
		headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
	}).then(res => res);
}
/**
* 发货接口
*/
export function TradeBatchSendApi(data: TradeBatchSendRequest) {
	return axios.post<unknown, TradeBatchSendResponse>('/trade/batchSend', data).then(res => res.data);
}

/**
* 解析电话
*/
export function TradePddControlDecryptV1ReceiverPhoneApi(data: TradePddControlDecryptV1ReceiverInfoRequest) {
	return axios.post<unknown, TradePddControlDecryptV1ReceiverInfoResponse>('/trade/pdd/control/decrypt/v1/receiverPhone', data).then(res => res);
}

/**
* 解析收件人姓名
*/
export function TradePddControlDecryptV1ReceiverNameApi(data: TradePddControlDecryptV1ReceiverInfoRequest) {
	return axios.post<unknown, TradePddControlDecryptV1ReceiverInfoResponse>('/trade/pdd/control/decrypt/v1/receiverName', data).then(res => res);
}

/**
* 解析详细地址
*/
export function TradePddControlDecryptV1ReceiverAddressApi(data: TradePddControlDecryptV1ReceiverInfoRequest) {
	return axios.post<unknown, TradePddControlDecryptV1ReceiverInfoResponse>('/trade/pdd/control/decrypt/v1/receiverAddress', data).then(res => res);
}

/**
* 更新店铺到DB
*/
export function TradeUserUpdateShopSyncConfigApi(data: {}) {
	return axios.post<unknown, TradeUserUpdateShopSyncConfigResponse>('/trade/user/updateShopSyncConfig', data).then(res => res.data);
}

/**
* 批量修改备注
*/
export function TradeBatchUpdateMemoApi(data: TradeBatchUpdateMemoRequest) {
	return axios.post<unknown, TradeBatchUpdateMemoResponse>('/trade/batchUpdateMemo', data, {
		headers: {
			splitParams: true
		}
	}).then(res => (res ? dealUpdateMemoRes(res) : res));
}

/**
* 获取列配置
*/
export function TradeUserGetUserColumnConfigApi(data: {}) {
	return axios.post<unknown, TradeUserGetUserColumnConfigResponse>('/trade/user/getUserColumnConfig', data).then(res => res.data);
}

/**
* 更新列配置
*/
export function TradeUserAddUserColumnConfigApi(data: TradeUserAddUserColumnConfigRequest) {
	return axios.post<unknown, TradeUserAddUserColumnConfigResponse>('/trade/user/addUserColumnConfig', data).then(res => res.data);
}
/*
* 提供接口给首页用
*/
export function TradeGetCountForIndexApi(data: {}) {
	return axios.post<unknown, TradeGetCountForIndexResponse>('/trade/getCountForIndex', data).then(res => res.data);
}
/*
* 首页近7天发货失败统计
*/
export function TradeQueryWarningCountForShipFailApi() {
	return axios.get<unknown, TradeQueryWarningCountForShipFailResponse>('/trade/queryTradeWarningCountForShipFail').then(res => res.data);
}

/*
* 订单打印打印异常统计
*/
export function TradeQueryWarningOrderSummaryApi(data: {}) {
	return axios.post<unknown, TradeQueryWarningOrderSummaryResponse>('/trade/queryWarningOrderSummary', data).then(res => res.data);
}

/*
* 查询发货超时订单
*/
export function TradeQuerySendTimeoutAbnormalInfo(data: {}) {
	return axios.post<unknown, TradeQuerySendTimeoutAbnormalInfoResponse>('/trade/querySendTimeoutAbnormalInfo', data).then(res => res.data);
}

/**
* 批量修改订单挂起状态
*/
export function TradeBatchUpdatePendingStatusApi(data: TradeBatchUpdatePendingStatusRequest) {
	return axios.post<unknown, Array<TradeBatchUpdatePendingStatusResponse>>('/trade/batchUpdatePendingStatus', data, {
		headers: {
			splitParams: true,
		}
	}).then(res => res);
}

/**
* 批量修改订单本地标记
*/
export function TradeBatchUpdateTradeFlagApi(data: TradeBatchUpdateTradeFlagRequest) {
	return axios.post<unknown, TradeBatchUpdateTradeFlagResponse>('/trade/batchUpdateTradeFlag', data, {
		headers: {
			splitParams: true,
		}
	}).then(res => (res ? dealUpdateMemoRes(res) : res));
}

/**
* pdd请求code 参考：httpsopen.pinduoduo.comapplicationdocumentapi?id=pdd.cloud.isv.page.code       获取成功返回多多的pageCode,否则返回""
*/
export function TradeCodeApi(data: {}) {
	return axios.post<unknown, TradeCodeResponse>('/trade/code', data).then(res => res.data);
}

/**
* 获取产品内容设置
*/
export function TradeUserGetUserCustomSetApi(data: {}) {
	return axios.post<unknown, TradeUserGetUserCustomSetResponse>('/trade/user/getUserCustomSet', data).then(res => res.data);
}

/**
* 更新产品内容设置
*/
export function TradeUserAddUserCustomSetApi(data: TradeUserAddUserCustomSetRequest) {
	return axios.post<unknown, TradeUserAddUserCustomSetResponse>('/trade/user/addUserCustomSet', data).then(res => res.data);
}

/**
* 手动合单
*/
export function TradeManualMergeTradeApi(data: TradeManualMergeTradeRequest) {
	return axios.post<unknown, TradeManualMergeTradeResponse>('/trade/manualMergeTrade', data).then(res => res.data);
}

/**
* 手动拆分合单
*/
export function TradeManualSplitMergeTradeApi(data: TradeManualSplitMergeTradeRequest) {
	return axios.post<unknown, TradeManualSplitMergeTradeResponse>('/trade/manualSplitMergeTrade', data).then(res => res.data);
}

/**
* 订单导出配置保存
*/
export function TradeExportSaveConfigApi(data: TradeExportSaveConfigRequest) {
	return axios.post<unknown, TradeUserAddUserCustomSetResponse>('/trade/export/saveConfig', data).then(res => res.data);
}

/**
* 订单导出
*/
export function TradeExportApi(data: TradeExportRequest) {
	// return axios.post<unknown, TradePrintSetGetPrintSetResponse>('/trade/export/', data).then(res => res.data);
	return instance({
		method: 'POST',
		responseType: 'blob',
		url: '/trade/export/',
		data,
	});
}

/**
* 订单导出配置查询
*/
export function TradeExportGetConfigApi(data: any) {
	return axios.post<unknown, TradeExportGetConfigResponse>('/trade/export/getConfig', data).then(res => res.data);
}

/**
* 恢复默认配置
*/
export function TradeExportResetApi(data: any) {
	return axios.post<unknown, TradeExportGetConfigResponse>('/trade/export/reset', data).then(res => res.data);
}


/**
* 通过tid获取pttid解密
* !! 由于原有encodeTid被用来做运单号使用，导致解密id不能使用这里掉一次接口交换
*/
export function TradeQueryTradeVoApi(data: {tid:string}) {
	return axios.post<unknown, TradeBatchDecryDataResponse>('/trade/queryTradeVo', data).then(res => res.data);
}

/**
* 解密接口
*/
export function TradeBatchDecryDataApi(data: TradeBatchDecryDataRequest) {
	return axios.post<unknown, TradeBatchDecryDataResponse>('/trade/batchDecryData', data).then(res => res.data);
}

/**
* 1688回流解密接口
*/
export function GetTradeEncryptOutOrderInfo(data: TradeGetTradeEncryptOutOrderInfoRequest) {
	return axios.post<unknown, TradeGetTradeEncryptOutOrderInfoResponse>('/trade/getTradeEncryptOutOrderInfo', data).then(res => res.data);
}


/**
* 解密接口
*/
export function TradeHufuGetSignApi(data) {
	return axios.post('/trade/hufu/getSign', data).then(res => res.data);
}


/**
* 订单操作日志列表
*/
export function TradeOperateLogApi(data: TradeOperateLogRequest) {
	return axios.post<unknown, TradeOperateLogResponse>('/trade/operate/log', data).then(res => res.data);
}

/**
* 获取系统商品库存
*/
export function TradeQueryStockInfoApi(data: TradeQueryStockInfoRequest) {
	return axios.post<unknown, TradeQueryStockInfoResponse>('/trade/queryStockInfo', data).then(res => res.data);
}
/*
* 操作店铺
*/
export function TradeUserOperateShopApi(data: TradeUserOperateShopRequest) {
	return axios.post<unknown, ResponseTypeOfKDZS<any>>('/trade/user/operateShop', data).then(res => res.data);
}
/**
* 添加订单操作日志
*/
export function TradeOperateLogAddApi(data: TradeOperateLogAddRequest) {
	return axios.post<unknown, TradeOperateLogAddResponse>('/trade/operate/log/add', data, {
		headers: {
			splitParams: true,
			showLoading: false,
		}
	}).then(res => res.data);
}

/**
* 敏感信息模糊化
*/
export function TradeInfoBlurApi(data: TradeInfoBlurRequest) {
	return axios.post<unknown, TradeInfoBlurResponse>('/trade/info/blur', data).then(res => res.data);
}

/**
* 生成御城河sign
*/
export function TradeTbGetYchSignApi(data: {taobaoId: string | number}) {
	return axios.post<unknown, TradeTbGetYchSignResponse>('/trade/tbGetYchSign', data, {
		headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
	}).then(res => res.data);
}

/**
* 网关一次性token获取 需要注意的是token 有效时间为5分钟
*/
export function TradeTbTopOnceTokenApi(data: {secToken: string, taobaoId: string}) {
	return axios.post<unknown, TradeTbTopOnceTokenResponse>('/trade/tbTopOnceToken', data, {
		headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
	}).then(res => res.data);
}

/**
* 设置查询
*/
export function ItemFilterSettingSelectApi(data: {}) {
	return axios.post<unknown, ItemFilterSettingSelectResponse>('/itemFilter/setting/select', data).then(res => res.data);
}

/**
* 设置保存
*/
export function ItemFilterSettingSaveApi(data: ItemFilterSettingSaveRequest) {
	return axios.post<unknown, ItemFilterSettingSaveResponse>('/itemFilter/setting/save', data).then(res => res.data);
}
/**
* 订单忽略
*/
export function TradeTradeIgnoreApi(data: TradeTradeIgnoreRequest) {
	return axios.post<unknown, TradeTradeIgnoreResponse>('/trade/tradeIgnore', data).then(res => res.data);
}

/**
* 查询订单详情
*/
export function TradeTradeDetailGetApi(data: TradeTradeDetailGetRequest) {
	return axios.post<unknown, TradeTradeDetailGetResponse>('/trade/tradeDetailGet', data).then(res => res);
}

/**
* 查询订单详情-for-差点
*/
export function TradeTradeDetailGetByPtTidApi(data: TradeTradeDetailGetRequest) {
	return axios.post<unknown, TradeTradeDetailGetResponse>('/trade/tradeDetailGetByPtTid', data).then(res => res);
}


// 下载
export function importExpressExportApi(data: any) {
	return instance({
		method: "post",
		url: `/trade/express/export/tradeExpress`,
		responseType: "blob",
		data,
	});
}

/**
* tb 解密时间延迟接口
*/
export function TradeBatchSecretExtendApi(data: TradeBatchSecretExtendRequest) {
	return axios.post<unknown, TradeBatchSecretExtendResponse>('/trade/batchSecretExtend', data).then(res => res);
}

/**
* 手动预占解除库存操作
*/
export function TradeOccupiedOpsApi(data: TradeOccupiedOpsRequest) {
	return axios.post<unknown, Array<TradeOccupiedOpsResponse>>('/trade/occupied/ops', data, {
		headers: {
			splitParams: true
		}
	}).then(res => res);
}

/**
* 根据快递单号查询订单
*/
export function TradeQueryTradeByExNumberApi(data: TradeQueryTradeByExNumberRequest) {
	return axios.post<unknown, TradeQueryTradeByExNumberResponse>('/trade/print/queryTradeByExNumber', data).then(res => res);
}

/**
* 批量修改空包裹
*/
export function TradeBatchUpdateIgnoreApi(data: TradeBatchUpdateIgnoreRequest) {
	return axios.post<unknown, TradeBatchUpdateIgnoreResponse>('/trade/batchUpdateIgnore', data).then(res => res.data);
}

/**
* 根据快递单号查询订单
*/
export function TradeDispenseApi(data: TradeDispenseRequest) {
	return axios.post<unknown, TradeDispenseResponse>('/trade/dispense', data).then(res => res);
}

/**
* 获取系统商品库存
*/
export function TradeAllotStockNumByTidListApi(data: TradeGetTradeAllotStockNumByTidListRequest) {
	return axios.post<unknown, TradeGetTradeAllotStockNumByTidListResponse>('/trade/getTradeAllotStockNumByTidList', data).then(res => res.data);
}

/**
* 获取系统商品库存
*/
export function UpdateTradeOccupiedApi(data: TradeUpdateTradeOccupiedRequest) {
	return axios.post<unknown, TradeUpdateTradeOccupiedResponse>('/trade/updateTradeOccupied', data).then(res => res.data);
}

/**
* 批量修改订单商品信息
*/
export function TradeBatchChangeTradeItemApi(data: TradeBatchChangeTradeItemRequest) {
	return axios.post<unknown, TradeBatchChangeTradeItemResponse>('/trade/batchChangeTradeItem', data).then(res => res.data);
}

/**
* 批量获取bic订单码信息集合
*/
export function TradeBatchGetBicOrderCodeApi(data: TradeBatchGetBicOrderCodeRequest) {
	return axios.post<unknown, TradeBatchGetBicOrderCodeResponse>('/trade/batchGetBicOrderCode', data).then(res => res.data);
}

/**
* BIC订单发货
*/
export function TradeBatchBindBicOrderCodeApi(data: TradeBatchBindBicOrderCodeRequest) {
	return axios.post<unknown, TradeBatchBindBicOrderCodeResponse>('/trade/batchBindBicOrderCode', data).then(res => res.data);
}

/**
* 获取物流信息
*/
export function GetLogisticsDetailForErpApi(data: TradeLogisticsGetLogisticsDetailForErpRequest) {
	return axios.post<unknown, TradeLogisticsGetLogisticsDetailForErpResponse>('/trade/logistics/getLogisticsDetailForErp', data).then(res => res.data);
}

/**
* 修改地址
*/
export function TradeModifyAddressApi(data: TradeModifyAddressRequest) {
	return axios.post<unknown, TradeModifyAddressResponse>('/trade/modifyAddress', data).then(res => res);
}

/**
 * 物流公司
 */
export function GetLogisticsCompanyInfoByYdNoApi(data: TradeLogisticsGetLogisticsCompanyInfoByYdNoRequest) {
	return axios.post<unknown, TradeLogisticsGetLogisticsCompanyInfoByYdNoResponse>('/trade/logistics/getLogisticsCompanyInfoByYdNo', data, { headers: { hideErrorMessage: true } }).then(res => res.data);
}

/*
* 批量更新订单快照
*/
export function TradeBatchUpdateTradeSnapshotApi(data: TradeBatchUpdateTradeSnapshotRequest) {
	return axios.post<unknown, TradeBatchUpdateTradeSnapshotResponse>('/trade/batchUpdateTradeSnapshot', data, {
		headers: {
			splitParams: true,
			notBatchRequest: true,
			maxSplitNum: 50,
		}
	}).then(res => res as TradeBatchUpdateTradeSnapshotResponse[]);
}

/**
* 异步更新订单本地货品信息
*/
export function TradeAsyncLocalItemSnapShotApi(data: TradeAsyncLocalItemSnapShotRequest) {
	return axios.post<unknown, TradeAsyncLocalItemSnapShotResponse>('/trade/asyncLocalItemSnapShot', data).then(res => res.data);
}

/**
* 异步更新订单本地货品信息进度查询接口
*/
export function TradeQueryLocalItemSnapShotResultApi(data: TradeQueryLocalItemSnapShotResultRequest) {
	return axios.post<unknown, TradeQueryLocalItemSnapShotResultResponse>('/trade/queryLocalItemSnapShotResult', data).then(res => res.data);
}

/**
* 订单页面修改货品信息
*/
export function ItemItemEditSysItemInfoInTradeApi(data: ItemItemEditSysItemInfoInTradeRequest) {
	return axios.post<unknown, ItemItemEditSysItemInfoInTradeResponse>('/item/item/editSysItemInfoInTrade', data).then(res => res.data);
}

/**
* 重新发货接口
*/
export function TradeBatchResendApi(data: TradeBatchResendRequest) {
	return axios.post<unknown, TradeBatchResendResponse>('/trade/batchResend', data).then(res => res.data);
}

/**
* 追加包裹发货接口
*/
export function TradeBatchAppendSendApi(data: TradeBatchAppendSendRequest) {
	return axios.post<unknown, TradeBatchAppendSendResponse>('/trade/batchAppendSend', data).then(res => res.data);
}

/**
* 根据运单号获取底单信息
*/
export function TradePrintGetBgKddInfoListByExNumberApi(data: TradePrintGetBgKddInfoListByExNumberRequest) {
	return axios.post<unknown, TradePrintGetBgKddInfoListByExNumberResponse>('/trade/print/getBgKddInfoListByExNumber', data).then(res => res.data);
}

/**
* 查询异常预警弹窗
*/
export function QueryTradeNoticeWarnApi() {
	return axios.post<unknown, TradeQueryTradeNoticeWarnResponse>('/trade/queryTradeNoticeWarn', {}, {
		headers: {
			hideErrorMessage: true,
		}
	}).then(res => res.data);
}
/**
* 查询异常预警列表
*/
export function QueryTradeWarningApi(data: TradeQueryTradeWarningRequest) {
	return axios.post<unknown, TradeQueryTradeWarningResponse>('/trade/queryTradeWarning', data).then(res => res.data);
}
/**
* 查询异常预警列表总值
*/
export function QueryTradeWarningCountApi(data: TradeQueryTradeWarningCountRequest) {
	return axios.post<unknown, TradeQueryTradeWarningCountResponse>('/trade/queryTradeWarningCount', data).then(res => res.data);
}
/**
* 查询订单异常预警红点标识
*/
export function GetRedDotMarketApi() {
	return axios.post<unknown, TradeGetRedDotMarketResponse>('/trade/getRedDotMarket', {}, {
		headers: {
			hideErrorMessage: true,
		}
	}).then(res => res.data);
}
/**
* 批量更新预警标识
*/
export function BatchUpdateWarningApi(data: TradeBatchUpdateWarningRequest) {
	return axios.post<unknown, TradeBatchUpdateWarningResponse>('/trade/batchUpdateWarning', data).then(res => res.data);
}
/**
* 删除异常预警弹窗缓存
*/
export function DeleteTradeWarnTipsCacheApi() {
	return axios.post<unknown, TradeDeleteTradeWarnTipsCacheResponse>('/trade/deleteTradeWarnTipsCache', {}).then(res => res.data);
}

/**
* 查询异常预警列表详情
*/
export function GetWarningDetailApi(data: TradeGetWarningDetailRequest) {
	return axios.post<unknown, TradeGetWarningDetailResponse>('/trade/getWarningDetail', data).then(res => res.data);
}

/**
* 查询订单异常预警监控设置
*/
export function QueryTradeWarningTaskConfig() {
	return axios.get<unknown, TradeGetWarningDetailResponse>('/trade/queryTradeWarningTaskConfig').then(res => res.data);
}

/**
* 修改订单异常预警监控设置
*/
export function UpdateTradeWarningTaskConfig(data: TradeGetWarningDetailRequest) {
	return axios.post<unknown, TradeGetWarningDetailResponse>('/trade/updateTradeWarningTaskConfig', data).then(res => res.data);
}

/**
* 订单异常预警根据订单查询售后数据 返回订单维度
*/
export function FastRefundSelectRefundByPtTidListApi(data: FastRefundSelectRefundByPtTidListRequest) {
	return axios.post<unknown, FastRefundSelectRefundByPtTidListResponse>('/aftersale/fastRefund/selectRefundByPtTidList', data).then(res => res.data);
}

/**
* 订单异常预警极速退货入库
*/
export function FastRefundFastRefundInStockApi(data: FastRefundFastRefundInStockRequest) {
	return axios.post<unknown, FastRefundFastRefundInStockResponse>('/aftersale/fastRefund/fastRefundInStock', data).then(res => res.data);
}

/**
* 查询旺旺
*/
export function GetWangWangApi({ sellerId, buyerOpenUid }) {
	return axios.get<unknown, unknown>(`/trade/getWangWang?buyerOpenUid=${buyerOpenUid}&sellerId=${sellerId}`, {}).then((res:any) => res.data);
}

/**
* 记录3C类目的商品识别码
*/
export function UpdateOrderSerialNumberApi(data: TradeUpdateOrderSerialNumberRequest) {
	return axios.post<unknown, TradeUpdateOrderSerialNumberResponse>('/trade/updateOrderSerialNumber', data).then(res => res.data);
}


/**
* 查询快捷备注列表
*/
export function GetUserQuickNotesListApi(data) {
	return axios.get<unknown, TradeGetUserQuickNotesListResponse>(`/trade/getUserQuickNotesList?bizType=${data.bizType}`, {}).then(res => res.data);
}


/**
* 批量更新快捷备注顺序
*/
export function BatchUpdateUserQuickNotesApi(data: TradeBatchUpdateUserQuickNotesRequest) {
	return axios.post<unknown, TradeBatchUpdateUserQuickNotesResponse>('/trade/batchUpdateUserQuickNotes', data).then(res => res.data);
}


/**
* 删除快捷备注
*/
export function DeleteUserQuickNotesApi(data: TradeDeleteUserQuickNotesRequest) {
	return axios.post<unknown, TradeDeleteUserQuickNotesResponse>('/trade/deleteUserQuickNotes', data).then(res => res.data);
}


/**
* 新增快捷备注
*/
export function AddUserQuickNotesApi(data: TradeAddUserQuickNotesRequest) {
	return axios.post<unknown, TradeAddUserQuickNotesResponse>('/trade/addUserQuickNotes', data).then(res => res.data);
}


/**
* 批量更新订单线下备注
*/
export function BatchUpdateSysMemoApi(data: TradeBatchUpdateSysMemoRequest) {
	return axios.post<unknown, TradeBatchUpdateSysMemoResponse>('/trade/batchUpdateSysMemo', data, {
		headers: {
			showLoading: data.tidList.length > 1,
			splitParams: true,
			notBatchRequest: true,
			maxSplitNum: 200,
		}
	}).then(res => (res ? dealUpdateSysMemoRes(res) : res));
}

export function UpdateSysMemoPicApi(data: TradeUpdateSysMemoPicRequest) {
	return axios.post<unknown, TradeUpdateSysMemoPicResponse>('/trade/updateSysMemoPic', data).then(res => res.data);
}


/**
* 试用版用户限制累计打单量上限
*/
export function preCheckPrintLimitApi() {
	return axios.get<unknown, PrintPreCheckPrintLimitResponse>('/print/preCheck/printLimit').then(res => res.data);
}

/**
* 用户限制累计打单量上限
*/
export function preCheckTradeLimitApi(data: any) {
	return axios.post<unknown, PrintPreCheckTradeLimitResponse>('/print/preCheck/tradeLimit', data).then(res => res.data);
}

/**
* 按天，查询已消耗单量明细数据
*/
export function tradeLimitQueryDayDetailPageListApi(data: any) {
	return axios.post<unknown>('/trade/userTradeNumLimit/queryDayDetailPageList', data).then(res => res.data);
}

/**
* 按天，查询已消耗单量明细数据铺货
*/
export function queryDayDetailPageList(data: any) {
	return axios.post<unknown>('/trade/use/limit/queryDayDetailPageList', data).then(res => res.data);
}

/**
* 查询已消耗单量明细数据铺货
*/
export function queryDetailPageList(data: any) {
	return axios.post<unknown>('/trade/use/limit/queryDetailPageList', data).then(res => res.data);
}

/**
* 查询已消耗单量明细数据
*/
export function tradeLimitQueryDetailPageListApi(data: any) {
	return axios.post<unknown>('/trade/userTradeNumLimit/queryDetailPageList', data).then(res => res.data);
}

/**
* 验货发货2.0 获取验货记录
*/
export function TradeQueryScanTradeRecordByExNumberApi(data: any) {
	return axios.get<unknown, TradeQueryScanTradeRecordByExNumberResponse>(`/trade/queryScanTradeRecordByExNumber?exNumber=${data.exNumber}`, data).then(res => res.data);
}
export function getQueryTradeOrderConfigApi() {
	return axios.get<unknown, TradeGetQueryTradeOrderConfigResponse>('/trade/getQueryTradeOrderConfig').then(res => res.data);
}
export function updateQueryTradeOrderConfigApi(data: TradeUpdateQueryTradeOrderConfigRequest) {
	return axios.post<unknown, TradeUpdateQueryTradeOrderConfigResponse>('/trade/updateQueryTradeOrderConfig', data).then(res => res.data);
}
// 新增修改当前用户的订单自定义排序
export function saveCustomQueryTradeOrderApi(data: TradeSaveCustomQueryTradeOrderRequest) {
	return axios.post<unknown, TradeSaveCustomQueryTradeOrderResponse>('/trade/saveCustomQueryTradeOrder', data).then(res => res.data);
}
// 删除当前用户的订单自定义排序配置
export function delCustomQueryTradeOrderApi(data: TradeDelCustomQueryTradeOrderRequest) {
	return axios.post<unknown, TradeDelCustomQueryTradeOrderResponse>('/trade/delCustomQueryTradeOrder', data).then(res => res.data);
}
/**
* 查询订单实时商品数量
*/
export function TradeQueryTradeItemCountApi(data: TradeQueryTradeItemCountRequest) {
	return axios.post<unknown, TradeQueryTradeItemCountResponse>('/trade/queryTradeItemCount', data).then(res => res.data);
}

/**
* 批量录入商品识别码
*/
export function TradeBatchUpdateOrderSerialNumberApi(data: TradeBatchUpdateOrderSerialNumberRequest) {
	return axios.post<unknown, TradeBatchUpdateOrderSerialNumberResponse>('/trade/batchUpdateOrderSerialNumber', data, {
		headers: {
			splitParams: true,
			maxSplitNum: 200,
		}
	}).then(res => res);
}
export function getFXGToken(data: { sellerIdList: string[] }) {
	return axios.post<unknown, { result?: number; message?: string; data?: Array<{ encryptToken: string; sellerId: string; isOpenFxgMC: boolean; expireTime?: number }> }>('/trade/tool/fxgBatchMC', data).then(res => res.data);
}

export function getBase64Url(data) {
	return axios.post<unknown, PrintPreCheckPrintLimitResponse>('/trade/oss/image/upload', data, { headers: {
		hideErrorMessage: true,
	} }).then(res => res);
}

/**
* 直播标签打印
*/
// 获取上一次直播打印任务
export function TradeLiveTagLastTaskApi(data: TradeLivePrintGetLastTaskV2Request) {
	return axios.post<unknown, TradeLivePrintGetLastTaskV2Response>('/trade/livePrint/getLastTaskV2', data).then(res => res.data);
}
// 查询直播打印任务下拉列表
export function TradeLiveTagQueryTaskListApi(data) {
	return axios.post<unknown, TradeLivePrintQueryTaskListResponse>('/trade/livePrint/queryTaskList', data).then(res => res.data);
}
// 创建直播打印任务
export function TradeLiveTagCreateTaskApi(data: TradeLivePrintCreateTaskRequest) {
	return axios.post<unknown, TradeLivePrintCreateTaskResponse>('/trade/livePrint/createTask', data).then(res => res.data);
}
// 关闭直播打印任务
export function TradeLiveTagCloseTaskApi(data: TradeLivePrintCloseTaskRequest) {
	return axios.post<unknown, TradeLivePrintCloseTaskResponse>('/trade/livePrint/closeTask', data).then(res => res.data);
}
// 分页查询订单明细列表
export function TradeLiveTagTradeRecordApi(data: TradeLivePrintSelectLiveTradeRecordWithPageRequest) {
	return axios.post<unknown, TradeLivePrintSelectLiveTradeRecordWithPageResponse>('/trade/livePrint/selectLiveTradeRecordWithPage', data).then(res => res.data);
}
// 查询订单明细列表分组count
export function TradeLiveTagTradeRecordCountApi(data: TradeLivePrintSelectLiveTradeRecordCountRequest) {
	return axios.post<unknown, TradeLivePrintSelectLiveTradeRecordCountResponse>('/trade/livePrint/selectLiveTradeRecordCount', data).then(res => res.data);
}
// 更新直播标签打印状态
export function TradeLiveTagUpdatePrintStatusApi(data: TradeLivePrintUpdateLiveTradePrintStatusRequest) {
	return axios.post<unknown, TradeLivePrintUpdateLiveTradePrintStatusResponse>('/trade/livePrint/updateLiveTradePrintStatus', data).then(res => res.data);
}
// 查询直播标签打印任务统计
export function TradeLiveTagTradeStatusCountApi(data: TradeLivePrintLiveTradeRelateNumStatisticsRequest) {
	return axios.post<unknown, TradeLivePrintLiveTradeRelateNumStatisticsResponse>('/trade/livePrint/liveTradeRelateNumStatistics', data).then(res => res.data);
}
/**
* 直播标签打印
*/

// 查询各店铺订单数量
export function TradeQueryShopTradeCountApi(data: TradeQueryShopTradeCountRequest) {
	return axios.post<unknown, TradeQueryShopTradeCountResponse>('/trade/queryShopTradeCount', data).then(res => res.data);
}

/**
* 手工合并拆分订单
*/
export function TradeOrderSplitManualMergeTradeApi(data: TradeOrderSplitManualMergeTradeRequest) {
	return axios.post<unknown, TradeOrderSplitManualMergeTradeResponse>('/trade/order/split/manualMergeTrade', data).then(res => res.data);
}
