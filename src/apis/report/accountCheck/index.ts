import { ResponseTypeOfKDZS } from '@/types/schemas/common';
import { AccountCheckTotalData, TradeExpressExcelImportRequest, 
	TradeQueryExpressCheckInfoPageRequest, 
	TradeQueryExpressCheckInfoPageResponse,
	TradeQueryImportProgressApiRequest
} from '@/types/schemas/report/accountCheck';
import axios, { instance } from '@/utils/request';

/**
* 导入快递对账单excel
*/
export function TradeExpressExcelImportApi(params: TradeExpressExcelImportRequest) {
	const formParams = new FormData();
	for (const key in params) {
		if (Object.prototype.hasOwnProperty.call(params, key)) {
			formParams.append(key, params[key]);
		}
	}
	return axios.post<unknown, ResponseTypeOfKDZS<{
	    queryVersion?: string;
	}>>('/trade/expressExcel/import', formParams).then(res => res.data);
}


/**
* 查询快递对账单 导入进度
*/
export function TradeQueryImportProgressApi(data:TradeQueryImportProgressApiRequest) {
	return axios.post<unknown, ResponseTypeOfKDZS<{
	    queryVersion?: string;
	    progress?: number;
	}>>('/trade/query/importProgress', data).then(res => res);

}


/**
* 根据queryVersion查询快递对账单信息-分页
*/
export function TradeQueryExpressCheckInfoPageApi(data: TradeQueryExpressCheckInfoPageRequest) {
	return axios.post<unknown, TradeQueryExpressCheckInfoPageResponse>('/trade/query/expressCheckInfoPage', data).then(res => res.data);
}


/**
* 查询快递对账单 导入记录
*/
export function TradeQueryExpressImportRecordApi(data: {
	queryVersion: string;
}) {
	return axios.post<unknown, ResponseTypeOfKDZS<AccountCheckTotalData> >('/trade/query/expressImportRecord', data).then(res => res.data);
}

/**
* 下载快递对账单
*/
export function TradeDownExpressCheckRecordApi(data: {
    queryVersion:string
}) {
	return instance({
		method: "post",
		url: `/trade/down/expressCheckRecord`,
		responseType: "blob",
		data,
	});
}

export function SelectSaleProfitSyncResult() {
	return axios.post<unknown, unknown>('/aftersale/selectSaleProfitSyncResult').then(res => res.data);
}

export function SelectStockSyncResult() {
	return axios.post<unknown, unknown>('/aftersale/stockReport/selectStockSyncResult').then(res => res.data);
}
