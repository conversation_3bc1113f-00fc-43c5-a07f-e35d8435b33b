import axios, { instance } from '@/utils/request';
import {
	ItemBatchUpdatePlatformOuterIdsResponse,
	ItemBatchUpdatePlatformOuterIdsRequest,
	ItemItemExcelImportSkuResponse,
	ItemGetImportInfoRequest,
	ItemGetPlatformItemMatchRelationRequest,
	ItemGetPlatformItemMatchRelationResponse,
	ItemImportItemsRequest,
	ItemImportItemsResponse,
	ItemSysItemBatchDeleteRequest,
	ItemSysItemBatchDeleteResponse,
	ItemSysItemCloneRequest,
	ItemSysItemCloneResponse,
	ItemSysItemGenerateOuterIdResponse,
	ItemSysItemSaveRequest,
	ItemSysItemSaveResponse,
	ItemSysSkuDeleteRequest,
	ItemSysSkuGetOperationLogRequest,
	ItemSysSkuGetOperationLogResponse,
	SysItemGetSysItemListRequest,
	SysItemGetSysItemListResponse,
	ItemSysItemListOfItemRelationSysTemItemViewRequest,
	ItemSysItemListOfItemRelationSysTemItemViewResponse,
	ItemSysItemListOfItemRelationPlatformItemViewRequest,
	ItemSysItemListOfItemRelationPlatformItemViewResponse,
	ItemSysItemDeleteItemRelationWithBatchRequest,
	ItemSysItemDeleteItemRelationWithBatchResponse,
	ItemSysItemSaveItemRelationRequest,
	ItemSysItemSaveItemRelationResponse,
	ItemSysItemChangeItemRelationRequest,
	ItemSysItemChangeItemRelationResponse,
	ItemSysItemQueryPlatformItemListWithPageRequest,
	ItemSysItemQueryPlatformItemListWithPageResponse,
	ItemBatchImportItemsRequest,
	ItemBatchImportItemsResponse,
	ItemSysSkuGetSysSkusBySkuOuterIdRequest,
	ItemSysSkuGetSysSkusBySkuOuterIdResponse,
	ItemSysSkuEditSysSkuAliasRequest,
	ItemSysSkuEditSysSkuAliasResponse,
	ItemSysItemEditSysItemAliasRequest,
	ItemSysItemEditSysItemAliasResponse,
	ItemSysItemGetRelationBySysItemRequest,
	ItemSysItemGetRelationBySysItemResponse,
	ItemSysItemUpdateSysItemFormPlatformRequest,
	ItemSysItemUpdateSysItemFormPlatformResponse,
	ItemSysItemGenerateOuterIdsRequest,
	ItemSysItemGenerateOuterIdsResponse,
	ItemSysItemBatchUpdateClassifyRequest,
	ItemSysItemBatchUpdateClassifyResponse,
	ItemSysSkuDeleteCheckRequest,
	ItemSysSkuDeleteCheckResponse,
	ItemSysItemByPlatItemGetSysItemListRequest,
	ItemSysItemByPlatItemGetSysItemListResponse,
	ItemDeleteShopItemsRequest,
	ItemDeleteShopItemsResponse,
	ItemSysItemExcelImportSkuRequest,
	ItemSysItemExcelImportSkuResponse,
	ItemSysItemExcelExportErrorRequest,
	ItemSysSkuExcelExportSkuRequest,
	ItemSysSkuExcelExportSkuResponse,
	ItemSkuUpdateSkuRequest,
	ItemSkuUpdateSkuResponse,
	ItemItemUpdateItemByIdRequest,
	ItemItemUpdateItemByIdResponse,
	ItemSkuBatchDeleteBySkuIdsRequest,
	ItemSkuBatchDeleteBySkuIdsResponse,
	StockGroupPackageRequest,
	StockGroupPackageResponse,
	ItemSysItemGenerateOuterIdRequest,
	SysItemGetSysItemDetailResponse,
	ItemSysSkuExcelGetProgressResponse,
	ItemAsyncGetProgressRequest,
	ItemAsyncGetProgressResponse,
	ItemSysSkuExcelImportCombinationResponse,
	ItemSysSkuExcelImportCombinationRequest,
	ItemItemBatchUpdateSysItemByPlatformRequest,
	ItemItemBatchUpdateSysItemByPlatformResponse,
	StockExportRequest,
	StockImportResponse,
	ItemSysItemEditSysItemAliasWithBatchRequest,
	ItemSysItemEditSysItemAliasWithBatchResponse,
	ItemSysItemUpdateSysSkuInfoWithBatchResponse,
	ItemSysItemUpdateSysSkuInfoWithBatchRequest,
	ItemSysSkuBatchChangeSyncStockFlagRequest,
	ItemSysSkuBatchChangeSyncStockFlagResponse,
	ItemSysSkuGetSysSkuPageListRequest,
	ItemSysSkuGetSysSkuPageListResponse,
	ItemScmSaleBatchUpdateDropShippingItemFlagRequest,
	ItemScmSaleBatchUpdateDropShippingItemFlagResponse,
	ItemTakeGoodsLabelSelectLabelPush2PlatformItemWithPageResponse,
	ItemTakeGoodsLabelSelectLabelPush2PlatformItemWithPageRequest,
	ItemTakeGoodsLabelBindLabelPushPlatformItemResponse,
	ItemTakeGoodsLabelBindLabelPushPlatformItemRequest,
	ItemTakeGoodsLabelUnBindLabelPushPlatformItemRequest,
	ItemTakeGoodsLabelUnBindLabelPushPlatformItemResponse,
	ItemTakeGoodsLabelChangeBindLabelPushPlatformItemRequest,
	ItemTakeGoodsLabelChangeBindLabelPushPlatformItemResponse,
	ItemCommonItemGetCommonItemListRequest,
	ItemCommonItemGetCommonItemListResponse,
	ItemCommonItemSaveOrDelRequest,
	ItemCommonItemSaveOrDelResponse,
	ItemSysItemCreateCombinationBindingRequest,
	ItemSysItemCreateCombinationBindingResponse,
	ItemSysSkuEditSysSkuRequest,
	ItemSysSkuEditSysSkuResponse,
	ItemSysItemUpdateSysSkuInfoWithBatchByPlatformRequest,
	ItemSysItemUpdateSysSkuInfoWithBatchByPlatformResponse,
	ItemSysItemQueryRecommendItemRequest,
	ItemSysItemQueryRecommendItemResponse,
	ItemSysItemRecommendItemMergeRequest,
	ItemSysItemRecommendItemMergeResponse,
	ItemSysItemUserGetItemRelationUserCustomSetResponse,
	ItemSysItemUserAddUserCustomSetRequest,
	ItemSysItemUserAddUserCustomSetResponse,
	ItemSkuSimilaritySearchRequest,
	ItemSkuSimilaritySearchResponse,
	ItemSysItemItemMergeParseCombinationRequest,
	ItemSysItemItemMergeParseCombinationResponse,
	ItemWarehouseSlotGetListRequest, ItemWarehouseSlotGetListResponse,
	ItemWarehouseSlotDeleteRequest, ItemWarehouseSlotDeleteResponse,
	ItemWarehouseSlotSaveRequest, ItemWarehouseSlotSaveResponse,
	ItemSkuUpdVarietySkuOuterIdAndQueryRequest,
	ItemSkuUpdVarietySkuOuterIdAndQueryResponse,
	ItemSysItemBindRelationSysSkuRequest,
	ItemSysItemBindRelationSysSkuResponse
} from "@/types/schemas/warehouse/system";

/**
* 分页查询货品列表
*/
export function SysItemGetSysItemListApi(data: SysItemGetSysItemListRequest) {
	return axios.post<unknown, SysItemGetSysItemListResponse>('/item/sysItem/getSysItemList', data, {
		headers: {
			permissionsource: data.permission_source || ''
		}
	}).then(res => res.data);
}

/**
* 分页查询货品规格列表
*/
export function SysItemGetSysSkuListApi(data: SysItemGetSysItemListRequest) {
	return axios.post<unknown, SysItemGetSysItemListResponse>('/item/sysItem/getSysSkuList', data, {
		headers: {
			permissionsource: data.permission_source || ''
		}
	}).then(res => res.data);
}

/**
* 分页查询货品列表-订单模块
*/
export function TradeGetSysItemListApi(data: SysItemGetSysItemListRequest) {
	return axios.post<unknown, SysItemGetSysItemListResponse>('/trade/getSysItemList', data).then(res => res.data);
}

/**
* 分页查询货品列表-库存模块
*/
export function StockGetSysItemListApi(data: SysItemGetSysItemListRequest) {
	return axios.post<unknown, SysItemGetSysItemListResponse>('/stock/getSysItemList', data).then(res => res.data);
}

/**
* 批量库存同步开关设置
*/
export function ItemSysSkuBatchChangeSyncStockFlagApi(data: ItemSysSkuBatchChangeSyncStockFlagRequest) {
	return axios.post<unknown, ItemSysSkuBatchChangeSyncStockFlagResponse>('/item/sysSku/batchChangeSyncStockFlag', data, {
		headers: {
			permissionsource: data.permission_source || ''
		}
	}).then(res => res.data);
}
/**
* 分页查询货品列表
*/
export function SysItemGetSysItemDetailApi(data: { sysItemId: string, sysSkuId: string }) {
	return axios.post<unknown, SysItemGetSysItemDetailResponse>('/item/sysItem/getSysItemDetail', data).then(res => res.data);
}


/**
* 货品规格删除
*/
export function ItemSysSkuDeleteApi(data: ItemSysSkuDeleteRequest) {
	return axios.post<unknown, ItemSysSkuDeleteRequest>('/item/sysSku/delete', data, {
		headers: {
			permissionsource: data.permission_source || ''
		}
	}).then(res => res.data);
}

/**
* 货品操作日志查询
*/
export function ItemSysSkuGetOperationLogApi(data: ItemSysSkuGetOperationLogRequest) {
	return axios.post<unknown, ItemSysSkuGetOperationLogResponse>('/item/sysSku/getOperationLog', data).then(res => res.data);
}

/**
* 克隆货品
*/
export function ItemSysItemCloneApi(data: ItemSysItemCloneRequest) {
	return axios.post<unknown, ItemSysItemCloneResponse>('/item/sysItem/clone', data).then(res => res.data);
}

/**
* 保存货品
*/
export function ItemSysItemSaveApi(data: ItemSysItemSaveRequest) {
	return axios.post<unknown, ItemSysItemSaveResponse>('/item/sysItem/save', data, {
		headers: {
			permissionsource: data.permission_source || ''
		}
	}).then(res => res.data);
}

/**
* 生成货品规格编码
*/
export function ItemSysItemGenerateOuterIdApi(data: ItemSysItemGenerateOuterIdRequest) {
	return axios.post<unknown, ItemSysItemGenerateOuterIdResponse>('/item/sysItem/generateOuterId', data).then(res => res.data);
}

/**
* 批量删除货品
*/
export function ItemSysItemBatchDeleteApi(data: ItemSysItemBatchDeleteRequest) {
	return axios.post<unknown, ItemSysItemBatchDeleteResponse>('/item/sysSku/deleteWithBatch', data).then(res => res.data);
}

/**
* 批量删除货品
*/
// export function ItemSysItemBatchDeleteApi(data: ItemSysItemBatchDeleteRequest) {
// 	return axios.post<unknown, ItemSysItemBatchDeleteResponse>('/item/sysItem/batchDelete', data).then(res => res.data);
// }

/**
* 导入平台商品
*/
export function ItemImportItemsApi(data: ItemImportItemsRequest) {
	return axios.post<unknown, ItemImportItemsResponse>('/item/importItems', data).then(res => res.data);
}

/**
* 查询导入信息
*/
export function ItemGetImportInfoApi(data: ItemGetImportInfoRequest) {
	return axios.post<unknown, ItemImportItemsResponse>('/item/getImportInfo', data).then(res => res.data);
}
/**
* 查询平台和系统商品的匹配的关联关系
*/
export function ItemGetPlatformItemMatchRelationApi(data: ItemGetPlatformItemMatchRelationRequest) {
	return axios.post<unknown, ItemGetPlatformItemMatchRelationResponse>('/item/getPlatformItemMatchRelation', data).then(res => res.data);
}
/**
 * 货品与商品关系-快递助手货品视角
 */
export function ItemSysItemListOfItemRelationSysTemItemViewApi(data: ItemSysItemListOfItemRelationSysTemItemViewRequest) {
	return axios.post<unknown, ItemSysItemListOfItemRelationSysTemItemViewResponse>('/item/sysItem/listOfItemRelationSysTemItemView', data).then(res => res.data);
}
/**
 * 货品与商品关系-快递助手货品视角-新接口
 */
export function ItemSysItemListOfItemRelationSysTemItemViewSplitApi(data: ItemSysItemListOfItemRelationSysTemItemViewRequest) {
	return axios.post<unknown, ItemSysItemListOfItemRelationSysTemItemViewResponse>('/item/sysItem/listOfItemRelationSysTemItemViewSplit', data).then(res => res.data);
}
/**
 * 货品与商品关系-平台商品视角
 */
export function ItemSysItemListOfItemRelationPlatformItemViewApi(data: ItemSysItemListOfItemRelationPlatformItemViewRequest) {
	return axios.post<unknown, ItemSysItemListOfItemRelationPlatformItemViewResponse>('/item/sysItem/listOfItemRelationPlatformItemView', data).then(res => res.data);
}

// 平台商品视角-新接口
export function listOfItemRelationPlatformItemViewForSplit(data: ItemSysItemListOfItemRelationPlatformItemViewRequest) {
	return axios.post<unknown, ItemSysItemListOfItemRelationPlatformItemViewResponse>('/item/sysItem/listOfItemRelationPlatformItemViewForSplit', data).then(res => res.data);
}

/**
 * 货品与商品关系-平台商品视角-订单
 */
export function TradeListOfItemRelationPlatformItemViewApi(data: ItemSysItemListOfItemRelationPlatformItemViewRequest) {
	return axios.post<unknown, ItemSysItemListOfItemRelationPlatformItemViewResponse>('/trade/listOfItemRelationPlatformItemView', data).then(res => res.data);
}

/**
* 查找常用商品
*/
export function ItemCommonItemGetCommonItemListApi(data: ItemCommonItemGetCommonItemListRequest) {
	return axios.post<unknown, ItemCommonItemGetCommonItemListResponse>('/item/commonItem/getCommonItemList', data).then(res => res.data);
}
/**
* 保存或者取消常用商品
*/
export function ItemCommonItemSaveOrDelApi(data: ItemCommonItemSaveOrDelRequest) {
	return axios.post<unknown, ItemCommonItemSaveOrDelResponse>('/item/commonItem/saveOrDel', data).then(res => res.data);
}
/**
 * 通过商品， 修改货品信息
 */
export function BatchUpdateSysItemByPlatformApi(data: ItemItemBatchUpdateSysItemByPlatformRequest) {
	return axios.post<unknown, ItemItemBatchUpdateSysItemByPlatformResponse>('/item/item/batchUpdateSysItemByPlatformV2', data, {
		headers: {
			splitParams: true,
			maxSplitNum: 200,
			notBatchRequest: true,
		} }).then(res => res);
}

// 这个方法不走通用的分拆方法
export function BatchUpdateSysItemByPlatformApi2(data: ItemItemBatchUpdateSysItemByPlatformRequest) {
	return axios.post<unknown, ItemItemBatchUpdateSysItemByPlatformResponse>('/item/item/batchUpdateSysItemByPlatformV2', data).then(res => res);
}

/**
 * 通过商品， 修改单个货品信息
 */
export function UpdateSysItemByPlatformApi(data: ItemItemBatchUpdateSysItemByPlatformRequest) {
	return axios.post<unknown, ItemItemBatchUpdateSysItemByPlatformResponse>('/item/item/updateSysItemByPlatform', data).then(res => res.data);
}

/**
 * 批量解除商品-货品关联关系
 */

export function ItemSysItemDeleteItemRelationWithBatchApi(data: ItemSysItemDeleteItemRelationWithBatchRequest) {
	return axios.post<unknown, ItemSysItemDeleteItemRelationWithBatchResponse>('/item/sysItem/deleteItemRelationWithBatch', data, {
		headers: {
			notBatchRequest: true,
			splitParams: true,
			permissionsource: data.permission_source || ''
		}
	}).then(res => res.data);
}

/**
 * 关联商品-货品
 */
export function ItemSysItemSaveItemRelationApi(data: ItemSysItemSaveItemRelationRequest) {
	return axios.post<unknown, ItemSysItemSaveItemRelationResponse>('/item/sysItem/saveItemRelation', data).then(res => res.data);
}

/**
* 商品规格绑定货品规格
*/
export function ItemSysItemBindRelationSysSkuApi(data: ItemSysItemBindRelationSysSkuRequest) {
	return axios.post<unknown, ItemSysItemBindRelationSysSkuResponse>('/item/sysItem/bindRelationSysSku', data).then(res => res.data);
}

/**
 * 更换商品-货品关联关系
 */
export function ItemSysItemChangeItemRelationApi(data: ItemSysItemChangeItemRelationRequest) {
	return axios.post<unknown, ItemSysItemChangeItemRelationResponse>('/item/sysItem/changeItemRelation', data).then(res => res.data);
}
/**
 * 分页查询平台商品列表
 */
export function ItemSysItemQueryPlatformItemListWithPageApi(data: ItemSysItemQueryPlatformItemListWithPageRequest) {
	return axios.post<unknown, ItemSysItemQueryPlatformItemListWithPageResponse>('/item/sysItem/queryPlatformItemListWithPage', data).then(res => res.data);
}

/**
 * 分页查询平台商品列表-订单模块
 */
export function TradeQueryPlatformItemListWithPageApi(data: ItemSysItemQueryPlatformItemListWithPageRequest) {
	return axios.post<unknown, ItemSysItemQueryPlatformItemListWithPageResponse>('/trade/queryPlatformItemListWithPage', data).then(res => res.data);
}

/**
* 批量导入商品
*/
export function ItemBatchImportItemsApi(data: ItemBatchImportItemsRequest) {
	return axios.post<unknown, ItemBatchImportItemsResponse>('/item/batchImportItems', data).then(res => res.data);
}

/**
* 批量导入商品-同步
*/
export function ItemSyncBatchImportItemsApi(data: ItemBatchImportItemsRequest) {
	return axios.post<unknown, ItemItemBatchUpdateSysItemByPlatformResponse>('/item/syncBatchImportItems', data, {
		headers: {
			splitParams: true,
			maxSplitNum: 100,
			notBatchRequest: true,
			permissionsource: data.permission_source || ''
		} }).then(res => res);
}

/**
* 根据货品规格编码查询货品规格
*/
export function ItemSysSkuGetSysSkusBySkuOuterIdApi(data: ItemSysSkuGetSysSkusBySkuOuterIdRequest) {
	return axios.post<unknown, ItemSysSkuGetSysSkusBySkuOuterIdResponse>('/item/sysSku/getSysSkusBySkuOuterId', data).then(res => res.data);
}
/**
* 编辑货品简称
*/
export function ItemSysItemEditSysItemAliasdApi(data: ItemSysItemEditSysItemAliasRequest) {
	return axios.post<unknown, ItemSysItemEditSysItemAliasResponse>('/item/sysItem/editSysItemAlias', data).then(res => res.data);
}
/**
* 编辑规格别名
*/
export function ItemSysSkuEditSysSkuAliasdApi(data: ItemSysSkuEditSysSkuAliasRequest) {
	return axios.post<unknown, ItemSysSkuEditSysSkuAliasResponse>('/item/sysSku/editSysSkuAlias', data).then(res => res.data);
}
/**
* 编辑库存看板备注
*/
export function ItemSysSkuEditSysSkuApi(data: ItemSysSkuEditSysSkuRequest) {
	return axios.post<unknown, ItemSysSkuEditSysSkuResponse>('/item/sysSku/editSysSku', data).then(res => res.data);
}
/**
* 根据货品规格id查询绑定的平台商品
*/
export function ItemSysItemGetRelationBySysItemApi(data: ItemSysItemGetRelationBySysItemRequest) {
	return axios.post<unknown, ItemSysItemGetRelationBySysItemResponse>('/item/sysItem/getRelationBySysItem', data).then(res => res.data);
}
/**
* 用平台商品信息更新货品档案
*/
export function ItemSysItemUpdateSysItemFormPlatformApi(data: ItemSysItemUpdateSysItemFormPlatformRequest) {
	return axios.post<unknown, ItemSysItemUpdateSysItemFormPlatformResponse>('/item/sysItem/updateSysItemFormPlatform', data).then(res => res.data);
}

/**
* 批量生成货品规格编码
*/
export function ItemSysItemGenerateOuterIdsApi(data: ItemSysItemGenerateOuterIdsRequest) {
	return axios.post<unknown, ItemSysItemGenerateOuterIdsResponse>('/item/sysItem/generateOuterIds', data).then(res => res.data);
}

/**
* 批量修改货品分类
*/
export function ItemSysItemBatchUpdateClassifyApi(data: ItemSysItemBatchUpdateClassifyRequest) {
	return axios.post<unknown, ItemSysItemBatchUpdateClassifyResponse>('/item/sysItem/batchUpdateClassify', data).then(res => res.data);
}


/**
* 批量编辑货品简称
*/
export function ItemEditSysItemAliasWithBatchApi(data: ItemSysItemEditSysItemAliasWithBatchRequest) {
	return axios.post<unknown, ItemSysItemEditSysItemAliasWithBatchResponse>('/item/sysItem/editSysItemAliasWithBatch', data).then(res => res.data);
}

/**
* 批量修改货品规格信息
*/
export function ItemSysItemUpdateSysSkuInfoWithBatchApi(data: ItemSysItemUpdateSysSkuInfoWithBatchRequest) {
	return axios.post<unknown, ItemSysItemUpdateSysSkuInfoWithBatchResponse>('/item/sysItem/updateSysSkuInfoWithBatch', data, {	headers: {
		permissionsource: data.permission_source || ''
	} }).then(res => res.data);
}


/**
* 货品规格删除校验
*/
export function ItemSysSkuDeleteCheckApi(data: ItemSysSkuDeleteCheckRequest) {
	return axios.post<unknown, ItemSysSkuDeleteCheckResponse>('/item/sysSku/deleteCheck', data).then(res => res.data);
}

/**
* 根据平台商品，查询关联的系统商品
*/
export function ItemSysItemByPlatItemGetSysItemListApi(data: ItemSysItemByPlatItemGetSysItemListRequest) {
	return axios.post<unknown, ItemSysItemByPlatItemGetSysItemListResponse>('/item/sysItem/byPlatItem/getSysItemList', data).then(res => res.data);
}


/**
* 删除指定店铺所有平台商品
*/
export function ItemDeleteShopItemsApi(data: ItemDeleteShopItemsRequest) {
	return axios.post<unknown, ItemDeleteShopItemsResponse>('/item/deleteShopItems', data).then(res => res.data);
}

/**
* 组合商品打包拆包
*/
export function StockGroupPackageApi(data: StockGroupPackageRequest) {
	return axios.post<unknown, StockGroupPackageResponse>('/stock/groupPackage', data).then(res => res.data);
}

/**
* 平台货品Excel导出
*/
export function exportSkuApi(data: ItemSysSkuExcelExportSkuRequest) {
	return instance({
		method: 'POST',
		responseType: 'blob',
		url: '/item/item/excel/exportSku',
		data,
	});
}


/**
* 零库存版 平台货品Excel导出
*/
export function exportZeroSkuApi(data: ItemSysSkuExcelExportSkuRequest) {
	return instance({
		method: 'POST',
		responseType: 'blob',
		url: '/item/item/excel/exportZeroSku',
		data,
	});
}

/**
* 库存导入
*/
export function importStockApi(data: StockImportResponse) {
	return instance({
		method: 'POST',
		headers: {
			'Content-Type': 'multipart/form-data',
			permissionsource: data.permission_source || ''
		},
		url: '/stock/import',
		data,
		transformResponse: (res) => {
			let newRes = JSON.parse(res);
			newRes['isHiddenErrorMessage'] = true;
			return newRes;
		}
	});
}

/**
* 库存导出
*/
export function exportStockApi(data: StockExportRequest) {
	return instance({
		method: 'POST',
		responseType: 'blob',
		url: '/stock/export',
		data,
	});
}

/**
* 平台店铺Excel导入
*/
/**
* Excel导入本地货品
*/
export function importSkuApi(data: ItemItemExcelImportSkuResponse) {
	return instance({
		method: 'POST',
		headers: {
			'Content-Type': 'multipart/form-data',
			permissionsource: data.permission_source || ''
		},
		url: '/item/item/excel/importSku',
		data,
		transformResponse: (res) => {
			let newRes = JSON.parse(res);
			newRes['isHiddenErrorMessage'] = true;
			return newRes;
		}
	});
}

/**
* Excel导入本地货品
*/
export function ItemSysItemExcelImportSkuApi(data: ItemSysItemExcelImportSkuRequest) {
	return instance({
		method: 'POST',
		headers: {
			'Content-Type': 'multipart/form-data',
			permissionsource: 'SYS_ITEM_IMPORT',
		},
		url: '/item/sysItem/excel/importSku',
		data,
		transformResponse: (res) => {
			let newRes = JSON.parse(res);
			newRes['isHiddenErrorMessage'] = true;
			return newRes;
		}
	});
}

/**
* 导出excel导入失败的数据
*/
export function ItemSysItemExcelExportErrorApi(data: ItemSysItemExcelExportErrorRequest) {
	return instance({
		method: 'POST',
		responseType: 'blob',
		url: '/item/sysItem/excel/exportError',
		data,
	});
}

/**
* 本地货品Excel导出
*/
export function ItemSysSkuExcelExportSkuApi(data: ItemSysSkuExcelExportSkuRequest) {
	// return axios.post<unknown, ItemSysSkuExcelExportSkuResponse>('/item/sysSku/excel/exportSku', data).then(res => res.data);
	return instance({
		method: 'POST',
		responseType: 'blob',
		url: '/item/sysSku/excel/exportSku',
		data,
	});
}

/**
* 查询本地货品Excel导入进度
*/
export function ItemSysSkuExcelGetProgressApi(data: {}) {
	return axios.post<unknown, ItemSysSkuExcelGetProgressResponse>('/item/sysSku/excel/getProgress', data).then(res => res.data);
}


/**
 * 查询Excel导入进度
* /item/async/getProgress
*/
export function ItemAsyncGetProgressApi(data: ItemAsyncGetProgressRequest) {
	return axios.post<unknown, ItemAsyncGetProgressResponse>('/item/async/getProgress', data).then(res => res.data);
}

/**
* Excel导入组合货品
*/
export function ItemSysSkuExcelImportCombinationApi(data: ItemSysSkuExcelImportCombinationRequest) {
	// return axios.post<unknown, ItemSysSkuExcelImportCombinationResponse>('/item/sysSku/excel/importCombination', data).then(res => res);
	return instance({
		method: 'POST',
		headers: {
			'Content-Type': 'multipart/form-data',
			permissionsource: 'GROUP_IMPORT'
		},
		url: '/item/sysSku/excel/importCombination',
		data,
		transformResponse: (res) => {
			let newRes = JSON.parse(res);
			newRes['isHiddenErrorMessage'] = true;
			return newRes;
		}
	});
}

/**
* /item/sku/updateSku
*/
export function updateSkuByIdApi(data: ItemSkuUpdateSkuRequest) {
	return axios.post<unknown, ItemSkuUpdateSkuResponse>('/item/sku/updateSku', data).then(res => res.data);
}
/**
* 更新系统平台规格商家编码 然后 根据系统平台规格商家编码 回查 货品规格
*/
export function ItemSkuUpdVarietySkuOuterIdAndQueryApi(data: ItemSkuUpdVarietySkuOuterIdAndQueryRequest) {
	return axios.post<unknown, ItemSkuUpdVarietySkuOuterIdAndQueryResponse>('/item/sku/updVarietySkuOuterIdAndQuery', data).then(res => res.data);
}
/**
* /item/item/updateItemById
*/
export function updateItemByIdApi(data: ItemItemUpdateItemByIdRequest) {
	return axios.post<unknown, ItemItemUpdateItemByIdResponse>('/item/item/updateItemById', data).then(res => res.data);
}

/**
* /item/sku/batchDeleteBySkuIds
*/
export function batchDeleteBySkuIdsApi(data: ItemSkuBatchDeleteBySkuIdsRequest) {
	return axios.post<unknown, ItemSkuBatchDeleteBySkuIdsResponse>('/item/sku/batchDeleteBySkuIds', data, {
		headers: {
			permissionsource: data.permission_source || ''
		}
	}).then(res => res.data);
}
/**
* 批量更新平台编码
*/
export function ItemBatchUpdatePlatformOuterIdsApi(data: ItemBatchUpdatePlatformOuterIdsRequest) {
	return axios.post<unknown, ItemBatchUpdatePlatformOuterIdsResponse>('/item/batchUpdatePlatformOuterIds', data, {
		headers: {
			permissionsource: data.permission_source || ''
		}
	}).then(res => res.data);
}

/**
* 分页查询货品规格
*/
export function ItemSysSkuGetSysSkuPageListApi(data: ItemSysSkuGetSysSkuPageListRequest, cancelKey = '') {
	return axios.post<unknown, ItemSysSkuGetSysSkuPageListResponse>('/item/sysSku/getSysSkuPageList', data, {
		headers: {
			cancelKey
		}
	}).then(res => res.data);
}
/**
* 批量修改分销商代发商品flag
*/
export function ItemScmSaleBatchUpdateDropShippingItemFlagApi(data: ItemScmSaleBatchUpdateDropShippingItemFlagRequest) {
	return axios.post<unknown, ItemScmSaleBatchUpdateDropShippingItemFlagResponse>('/item/scm/sale/batchUpdateDropShippingItemFlag', data).then(res => res.data);
}

/**
* 查询标签推送平台商品信息
*/
export function ItemTakeGoodsLabelSelectLabelPush2PlatformItemWithPageApi(data: ItemTakeGoodsLabelSelectLabelPush2PlatformItemWithPageRequest) {
	return axios.post<unknown, ItemTakeGoodsLabelSelectLabelPush2PlatformItemWithPageResponse>('/item/takeGoodsLabel/selectLabelPush2PlatformItemWithPage', data).then(res => res.data);
}

/**
* 标签推送平台商品信息绑定
*/
export function ItemTakeGoodsLabelBindLabelPushPlatformItemApi(data: ItemTakeGoodsLabelBindLabelPushPlatformItemRequest) {
	return axios.post<unknown, ItemTakeGoodsLabelBindLabelPushPlatformItemResponse>('/item/takeGoodsLabel/bindLabelPushPlatformItem', data).then(res => res.data);
}

/**
* 标签推送平台商品信息更换
*/
export function ItemTakeGoodsLabelChangeBindLabelPushPlatformItemApi(data: ItemTakeGoodsLabelChangeBindLabelPushPlatformItemRequest) {
	return axios.post<unknown, ItemTakeGoodsLabelChangeBindLabelPushPlatformItemResponse>('/item/takeGoodsLabel/changeBindLabelPushPlatformItem', data).then(res => res.data);
}
/**
* 标签推送平台商品信息解绑
*/
export function ItemTakeGoodsLabelUnBindLabelPushPlatformItemApi(data: ItemTakeGoodsLabelUnBindLabelPushPlatformItemRequest) {
	return axios.post<unknown, ItemTakeGoodsLabelUnBindLabelPushPlatformItemResponse>('/item/takeGoodsLabel/unBindLabelPushPlatformItem', data).then(res => res.data);
}
/**
* 创建组合货品并绑定
*/
export function ItemSysItemCreateCombinationBindingApi(data: ItemSysItemCreateCombinationBindingRequest) {
	return axios.post<unknown, ItemSysItemCreateCombinationBindingResponse>('/item/sysItem/createCombinationBinding', data).then(res => res.data);
}

/**
 *	商品-批量填充平台信息
 * @param data
 */
export function ItemUpdateSysSkuInfoWithBatchByPlatform(
	data: ItemSysItemUpdateSysSkuInfoWithBatchByPlatformRequest
) {
	return axios.post<
		unknown,
		ItemSysItemUpdateSysSkuInfoWithBatchByPlatformResponse
	>("/item/sysItem/updateSysSkuInfoWithBatchByPlatform", data, {
		headers: {
			permissionsource: data.code || "",
		},
	});

}

/**
* 同款商品推荐查询
*/
export function ItemSysItemQueryRecommendItemApi(data: ItemSysItemQueryRecommendItemRequest) {
	return axios.post<unknown, ItemSysItemQueryRecommendItemResponse>('/item/sysItem/queryRecommendItem', data, {
		headers: {
			permissionsource: data.code || "",
		},
	}).then(res => res.data);
}

/**
* 商品AI推荐相似款
*/
export function ItemSimilaritySearchApi(data: ItemSysItemQueryRecommendItemRequest) {
	return axios.post<unknown, ItemSysItemQueryRecommendItemResponse>('/item/item/similaritySearch', data, {
		headers: {
			permissionsource: data.code || "",
		},
	}).then(res => res.data);
}

/**
* 商品AI推荐相似款
*/
export function ItemSimilaritySearchGetProgressApi(data: ItemSysItemQueryRecommendItemRequest) {
	return axios.post<unknown, ItemSysItemQueryRecommendItemResponse>('/item/async/getProgress', data, {
		headers: {
			permissionsource: data.code || "",
		},
	}).then(res => res.data);
}

/**
* 同款商品推荐合并
*/
export function ItemSysRecommendItemMergeItemApi(data: ItemSysItemRecommendItemMergeRequest) {
	return axios.post<unknown, ItemSysItemRecommendItemMergeResponse>('/item/sysItem/recommendItemMerge', data, {
		headers: {
			permissionsource: data.code || "",
		},
	}).then(res => res.data);
}

/**
* 手动合并
*/
export function ItemSysRecommendItemMergeHandItemApi(data: ItemSysItemRecommendItemMergeRequest) {
	return axios.post<unknown, ItemSysItemRecommendItemMergeResponse>('/item/sysItem/recommendItemMergeHand', data, {
		headers: {
			permissionsource: data.code || "",
		},
	}).then(res => res.data);
}

/**
* AI搜索相似款
*/
export function ItemSkuSimilaritySearchItemApi(data: ItemSkuSimilaritySearchRequest) {
	return axios.post<unknown, ItemSkuSimilaritySearchResponse>('/item/sku/similaritySearch', data).then(res => res.data);
}

/**
* 获取用户字段设置
*/
export function ItemSysGetItemRelationUserCustomSetApi(data) {
	return axios.post<unknown, ItemSysItemUserGetItemRelationUserCustomSetResponse>('/item/sysItem/user/getItemRelationUserCustomSet', data).then(res => res.data);
}

/**
* 新增更新用户字段设置
*/
export function ItemSysAddUserCustomSetApi(data: ItemSysItemUserAddUserCustomSetRequest) {
	return axios.post<unknown, ItemSysItemUserAddUserCustomSetResponse>('/item/sysItem/user/addUserCustomSet', data).then(res => res.data);
}


/**
* 解析组合装
*/
export function ItemMergeParseCombinationItemApi(data: ItemSysItemItemMergeParseCombinationRequest) {
	return axios.post<unknown, ItemSysItemItemMergeParseCombinationResponse>('/item/sysItem/itemMergeParseCombination', data).then(res => res.data);
}

/**
* 查询所有货位
*/
export function ItemWarehouseSlotGetListApi(data: ItemWarehouseSlotGetListRequest) {
	return axios.post<unknown, ItemWarehouseSlotGetListResponse>('/item/warehouseSlot/getList', data).then(res => res.data);
}

/**
* 删除货位
*/
export function ItemWarehouseSlotDeleteApi(data: ItemWarehouseSlotDeleteRequest) {
	return axios.post<unknown, ItemWarehouseSlotDeleteResponse>('/item/warehouseSlot/delete', data).then(res => res.data);
}

/**
* 保存货位
*/
export function ItemWarehouseSlotSaveApi(data: ItemWarehouseSlotSaveRequest) {
	return axios.post<unknown, ItemWarehouseSlotSaveResponse>('/item/warehouseSlot/save', data).then(res => res.data);
}