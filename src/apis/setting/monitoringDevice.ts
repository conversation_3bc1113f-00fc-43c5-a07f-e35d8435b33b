import axios, { instance } from '@/utils/request';

import {
	IndexMonitoringGetMonitoringDeviceBrandRequest,
	IndexMonitoringGetMonitoringDeviceBrandResponse,
	IndexMonitoringCheckRepeatDeviceNameRequest,
	IndexMonitoringCheckRepeatDeviceNameResponse,
	IndexMonitoringSaveMonitoringDeviceRequest,
	IndexMonitoringSaveMonitoringDeviceResponse,
	IndexMonitoringQueryMonitoringDeviceRequest,
	IndexMonitoringQueryMonitoringDeviceResponse,
	IndexMonitoringEditMonitoringDeviceRequest,
	IndexMonitoringEditMonitoringDeviceResponse,
	IndexMonitoringCheckRepeatCameraNameRequest,
	IndexMonitoringCheckRepeatCameraNameResponse,
	IndexMonitoringSaveDeviceCameraRequest,
	IndexMonitoringSaveDeviceCameraResponse,
	IndexMonitoringQueryDeviceCameraRequest,
	IndexMonitoringQueryDeviceCameraResponse,
	IndexMonitoringEditDeviceCameraRequest,
	IndexMonitoringEditDeviceCameraResponse,
	VideoLogMonitorQueryVideoLogRequest,
	VideoLogMonitorQueryVideoLogResponse
} from '@/types/schemas/setting/monitoringDevice/index';

/**
* 获取监控设备支持的 品牌信息
*/
export function IndexMonitoringGetMonitoringDeviceBrandApi(data: IndexMonitoringGetMonitoringDeviceBrandRequest) {
	return axios.post<unknown, IndexMonitoringGetMonitoringDeviceBrandResponse>('/index/monitoring/getMonitoringDeviceBrand', data).then(res => res.data);
}

/**
* 校验设备名称是否重复
*/
export function IndexMonitoringCheckRepeatDeviceNameApi(data: IndexMonitoringCheckRepeatDeviceNameRequest) {
	return axios.post<unknown, IndexMonitoringCheckRepeatDeviceNameResponse>('/index/monitoring/checkRepeatDeviceName', data, {
		headers: { hideErrorMessage: true } 
	}).then(res => res);
}

/**
* 存储设备
*/
export function IndexMonitoringSaveMonitoringDeviceApi(data: IndexMonitoringSaveMonitoringDeviceRequest) {
	return axios.post<unknown, IndexMonitoringSaveMonitoringDeviceResponse>('/index/monitoring/saveMonitoringDevice', data).then(res => res.data);
}

/**
* 查询存储设备
*/
export function IndexMonitoringQueryMonitoringDeviceApi(data: IndexMonitoringQueryMonitoringDeviceRequest) {
	return axios.post<unknown, IndexMonitoringQueryMonitoringDeviceResponse>('/index/monitoring/queryMonitoringDevice', data).then(res => res.data);
}

/**
* 编辑监控设备
*/
export function IndexMonitoringEditMonitoringDeviceApi(data: IndexMonitoringEditMonitoringDeviceRequest) {
	return axios.post<unknown, IndexMonitoringEditMonitoringDeviceResponse>('/index/monitoring/editMonitoringDevice', data).then(res => res.data);
}

/**
* 校验设备摄像头名称是否重复
*/
export function IndexMonitoringCheckRepeatCameraNameApi(data: IndexMonitoringCheckRepeatCameraNameRequest) {
	return axios.post<unknown, IndexMonitoringCheckRepeatCameraNameResponse>('/index/monitoring/checkRepeatCameraName', data).then(res => res.data);
}

/**
* 存储设备摄像头
*/
export function IndexMonitoringSaveDeviceCameraApi(data: IndexMonitoringSaveDeviceCameraRequest) {
	return axios.post<unknown, IndexMonitoringSaveDeviceCameraResponse>('/index/monitoring/saveDeviceCamera', data).then(res => res.data);
}

/**
* 查询监控设备摄像头
*/
export function IndexMonitoringQueryDeviceCameraApi(data: IndexMonitoringQueryDeviceCameraRequest) {
	return axios.post<unknown, IndexMonitoringQueryDeviceCameraResponse>('/index/monitoring/queryDeviceCamera', data).then(res => res.data);
}

/**
* 编辑监控设备摄像头
*/
export function IndexMonitoringEditDeviceCameraApi(data: IndexMonitoringEditDeviceCameraRequest) {
	return axios.post<unknown, IndexMonitoringEditDeviceCameraResponse>('/index/monitoring/editDeviceCamera', data).then(res => res.data);
}

/**
* 查询视频监控日志
*/
export function VideoLogMonitorQueryVideoLogApi(data: VideoLogMonitorQueryVideoLogRequest) {
	return axios.post<unknown, VideoLogMonitorQueryVideoLogResponse>('/item/videoLog/monitor/queryVideoLog', data).then(res => res.data);
}