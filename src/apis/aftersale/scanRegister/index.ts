import {
	QueryRefundScanRecordListRequest,
	QueryRefundScanRecordListResponse,
	ExportRefundScanRecordListRequest,
	ItemTakeGoodsLabelGenerateRefundLabelRequest,
	ItemTakeGoodsLabelGenerateRefundLabelResponse,
	SelectBatchScanRequest, SelectBatchScanResponse,
	RefundScanRegistrationRequest, RefundScanRegistrationResponse,
	RefundScanRegistrationBatchRequest, RefundScanRegistrationBatchResponse,
	DelBatchScanRequest, DelBatchScanResponse,
	SyncBatchScanRequest, SyncBatchScanResponse,
	BatchScanConfirmRequest, BatchScanConfirmResponse,
	SaveScanItemRequest, SaveScanItemResponse,
	GetProgressRequest, GetProgressResponse, GetRefundExportConfigRequest, GetRefundExportConfigResponse,
	BatchIsPendingRequest, BatchIsPendingResponse,
	SelectReqSelectRefundScanV2Request, SelectReqSelectRefundScanV2Response,
	BatchScanConfirmForSyncRequest, BatchScanConfirmForSyncResponse,
	TradeGetOrderCountGroupByDetailsRequest, TradeGetOrderCountGroupByDetailsResponse
} from '@/types/schemas/aftersale/scanRegister';
import { TradeLogisticsGetLogisticsCompanyInfoByYdNoRequest, TradeLogisticsGetLogisticsCompanyInfoByYdNoResponse } from '@/types/trade/index';
import axios, { instance } from '@/utils/request';

// 售后扫描-查询
export function SelectReqSelectRefundScanApi(data: {}) {
	return axios.post<unknown, {}>('/aftersale/selectReqSelectRefundScan', data).then(res => res.data);
}

// 售后扫描-查询V2
export function SelectReqSelectRefundScanV2Api(data: SelectReqSelectRefundScanV2Request) {
	return axios.post<unknown, SelectReqSelectRefundScanV2Response>('/aftersale/selectReqSelectRefundScanV2', data).then(res => res.data);
}

// 售后扫描-确认收货
export function RefundScanConfirmApi(data: {}) {
	return axios.post<unknown, {}>('/aftersale/refundScanConfirm', data).then(res => res.data);
}

// 售后扫描-自动同意退款
export function RefundScanConfirmAgreeFeeApi(data: {}) {
	return axios.post<unknown, {}>('/aftersale/refundScanConfirmAgreeFee', data).then(res => res.data);
}



/**
* 售后扫描记录查询
*/
export function QueryRefundScanRecordListApi(data: QueryRefundScanRecordListRequest) {
	return axios.post<unknown, QueryRefundScanRecordListResponse>('/aftersale/queryRefundScanRecordList', data).then(res => res.data);
}

/**
* 售后导出配置查询
*/
export function GetRefundExportConfigApi(data: GetRefundExportConfigRequest) {
	return axios.post<unknown, GetRefundExportConfigResponse>('/aftersale/getRefundExportConfig', data).then(res => res.data);
}

/**
* 售后扫描记录导出
*/
export function ExportRefundScanRecordListApi(data: ExportRefundScanRecordListRequest) {
	return instance({
		method: 'POST',
		responseType: 'blob',
		url: '/aftersale//exportRefundScanRecordList',
		data,
	});
}

/**
* 退货标签记录导出
*/
export function ExportRefundLabelApi(data: ExportRefundScanRecordListRequest) {
	return instance({
		method: 'POST',
		responseType: 'blob',
		url: '/item/takeGoodsLabel/exportLabel',
		data,
	});
}

/**
* 生成退货小标签
*/
export function GenerateRefundLabelApi(data: ItemTakeGoodsLabelGenerateRefundLabelRequest) {
	return axios.post<unknown, ItemTakeGoodsLabelGenerateRefundLabelResponse>('/item/takeGoodsLabel/generateRefundLabel', data).then(res => res.data);
}
/**
* 通过物流单号查询物流公司
*/
export function GetLogisticsCompanyInfoByYdNoApi(data: TradeLogisticsGetLogisticsCompanyInfoByYdNoRequest) {
	return instance({
		method: 'POST',
		url: '/trade/logistics/getLogisticsCompanyInfoByYdNo',
		data,
		transformResponse: (res) => {
			let newRes = JSON.parse(res);
			newRes['isHiddenErrorMessage'] = true;
			return newRes;
		}
	});
}

/**
* 单个售后扫描登记
*/
export function RefundScanRegistrationApi(data: RefundScanRegistrationRequest) {
	return axios.post<unknown, RefundScanRegistrationResponse>('/aftersale/refundScanRegistration', data).then(res => res.data);
}

/**
* 批量售后扫描登记
*/
export function RefundScanRegistrationBatchApi(data: RefundScanRegistrationBatchRequest) {
	return axios.post<unknown, RefundScanRegistrationBatchResponse>('/aftersale/refundScanRegistrationBatch', data).then(res => res.data);
}

/**
* 批量售后扫描查询
*/
export function SelectBatchScanApi(data: SelectBatchScanRequest) {
	return axios.post<unknown, SelectBatchScanResponse>('/aftersale/selectBatchScan', data).then(res => res.data);
}

/**
* 批量售后确认收货 （废弃）
*/
export function BatchScanConfirmApi(data: BatchScanConfirmRequest) {
	return axios.post<unknown, BatchScanConfirmResponse>('/aftersale/batchScanConfirm', data).then(res => res.data);
}

/**
* 批量扫描确认收货-同步方式（前端做进度条，收集结果）
*/
export function BatchScanConfirmForSyncApi(data: BatchScanConfirmForSyncRequest) {
	return axios.post<unknown, BatchScanConfirmForSyncResponse>('/aftersale/batchScanConfirmForSync', data).then(res => res.data);
}

/**
* 子订单维度统计，按分组统计数量
*/
export function TradeGetOrderCountGroupByDetailsApi(data: TradeGetOrderCountGroupByDetailsRequest, cancelKey: any) {
	return axios.post<unknown, TradeGetOrderCountGroupByDetailsResponse>('/trade/getOrderCountGroupByDetails', data, {
		headers: {
			'cancelKey': cancelKey,
			hideErrorMessage: true
		}
	}).then(res => res.data);
}



/**
* 批量售后扫描删除
*/
export function DelBatchScanApi(data: DelBatchScanRequest) {
	return axios.post<unknown, DelBatchScanResponse>('/aftersale/delBatchScan', data).then(res => res.data);
}

/**
* 批量售后扫描同步
*/
export function SyncBatchScanApi(data: SyncBatchScanRequest) {
	return axios.post<unknown, SyncBatchScanResponse>('/aftersale/syncBatchScan', data).then(res => res.data);
}

/**
* 编辑保存货品
*/
export function SaveScanItemApi(data: SaveScanItemRequest) {
	return axios.post<unknown, SaveScanItemResponse>('/aftersale/saveScanItem', data).then(res => res.data);
}

/**
* 获取进度 （废弃）
*/
export function GetProgressApi(data: GetProgressRequest) {
	return axios.post<unknown, GetProgressResponse>('/aftersale/getProgress', data).then(res => res.data);
}
/**
* 批量挂起
*/
export function BatchIsPendingApi(data: BatchIsPendingRequest) {
	return axios.post<unknown, BatchIsPendingResponse>('/aftersale/batchIsPending', data).then(res => res.data);
}