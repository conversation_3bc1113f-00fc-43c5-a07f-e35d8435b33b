import axios, { instance } from "@/utils/request";
import {
	SelectRefundListWithPageRequest, SelectRefundListWithPageResponse,
	IgnoreMatchSystemItemExceptionRefundRequest, IgnoreMatchSystemItemExceptionRefundResponse,
	FindSellerRefundForEditRequest, FindSellerRefundForEditResponse,
	ConfirmFinishRequest, ConfirmFinishResponse,
	SaveEditedRefundTradeRequest, SaveEditedRefundTradeResponse,
	CloseRefundRequest, CloseRefundResponse,
	SelectNeedReminderSyncResponse,
	SyncRefundTradeRequest, SyncRefundTradeResponse,
	ReviewRefundTradeRequest, ReviewRefundTradeResponse,
	ReviewRefundTradeV2Request, ReviewRefundTradeV2Response,
	ConfirmReturnItemRequest, ConfirmReturnItemResponse,
	HandleReturnItemRequest, HandleReturnItemResponse,
	QueryRefundAddressRequest, QueryRefundAddressResponse,
	SyncRefundAddressResponse,
	SetDefaultRefundAddressRequest, SetDefaultRefundAddressResponse,
	AgreeRefundFeeRequest, AgreeRefundFeeResponse,
	GetRefuseReasonListRequest, GetRefuseReasonListResponse,
	RefuseRefundFeeRequest, RefuseRefundFeeResponse,
	QueryHasDefaultRefundAddrRequest, QueryHasDefaultRefundAddrResponse,
	CheckAfterAuthorityRequest, CheckAfterAuthorityResponse,
	AgreeRefundGoodsRequest, AgreeRefundGoodsResponse,
	RefuseRefundGoodsRequest, RefuseRefundGoodsResponse,
	CheckHasRefundLogisticsRequest, CheckHasRefundLogisticsResponse,
	FindRdsTradeForBuildRefundRequest, FindRdsTradeForBuildRefundResponse,
	BuildRefundRequest, BuildRefundResponse,
	QueryUserAgConfigRequest, QueryUserAgConfigResponse,
	SaveUserAgConfigRequest, SaveUserAgConfigResponse,
	SavePlatformShopAgConfigRequest, SavePlatformShopAgConfigResponse,
	GetSyncRefundResultRequest, GetSyncRefundResultResponse,
	QueryShopsOfficialAgConfigRequest, QueryShopsOfficialAgConfigResponse,
	BatchAgreeRefundFeeResponse, BatchAgreeRefundFeeRequest,
	BatchQueryHasDefaultRefundAddrResponse, BatchQueryHasDefaultRefundAddrRequest,
	BatchAgreeRefundGoodsRequest, BatchAgreeRefundGoodsResponse,
	BatchConfirmReturnItemRequest, BatchConfirmReturnItemResponse,
	BatchHandleReturnItemRequest, BatchHandleReturnItemResponse,
	UploadExchangeInvoiceNoRequest, UploadExchangeInvoiceNoResponse, 
	AgreeRefundSupplyRequest, AgreeRefundSupplyResponse, 
	RefuseRefundSupplyRequest, RefuseRefundSupplyResponse,
	RefundBindTradeRequest, RefundBindTradeResponse,
	BindTradeQueryRequest, BindTradeQueryResponse,
	QueryPlatformShopAgConfig2Request, QueryPlatformShopAgConfig2Response,
	SelectRefundTabCountRequest, SelectRefundTabCountResponse,
	CreateExchangeTradeRequest, CreateExchangeTradeResponse,
	QueryRefundOpLogRequest, QueryRefundOpLogResponse,
	GetRefundExportConfigRequest, GetRefundExportConfigResponse,
	UpdateRefundExportConfigRequest, UpdateRefundExportConfigResponse,
	ResetRefundExportConfigRequest, ResetRefundExportConfigResponse,
	GetRefundGlobalConfigListRequest, GetRefundGlobalConfigListResponse,
	UpdateRefundGlobalConfigRequest, UpdateRefundGlobalConfigResponse,
	UpdateRequest, UpdateResponse,
	SelectResponse,
	BatchUpdateRefundSystemStatusRequest, BatchUpdateRefundSystemStatusResponse,
	BatchCloseRefundRequest, BatchCloseRefundResponse, CommonNoteEditCommonNoteRequest, CommonNoteEditCommonNoteResponse, CommonNoteSelectByUserResponse, BatchUpdateLocalNoteRequest, BatchUpdateLocalNoteResponse,
	BatchCreateExchangeTradeRequest, BatchCreateExchangeTradeResponse, CreateExchangeTradeAsyncGetRequest, CreateExchangeTradeAsyncGetResponse,
	ScmRefundBatchPushSupplierRequest, ScmRefundBatchPushSupplierResponse,
	ScmRefundBatchPushSupplierAsyncGetRequest, ScmRefundBatchPushSupplierAsyncGetResponse,
	ScmRefundBatchRecallRequest, ScmRefundBatchRecallResponse,
	ScmRefundBatchRecallAsyncGetRequest, ScmRefundBatchRecallAsyncGetResponse,
	ScmRefundBatchConfirmOrCancelRequest, ScmRefundBatchConfirmOrCancelResponse,
	QueryRefundAddressNewRequest, QueryRefundAddressNewResponse,
	GetRegisterParamListRequest, GetRegisterParamListResponse,
	BatchReceiveRegisterRequest, BatchReceiveRegisterResponse,
	SelectOperateLogPageRequest, SelectOperateLogPageResponse,
	SelectOperateLogSummaryRequest, SelectOperateLogSummaryResponse,
	BatchRefuseRefundRequest, BatchRefuseRefundResponse
} from "@/types/schemas/aftersale/trade";

const PATH = "/aftersale";

/**
* 查询售后单
*/
export function SelectRefundListWithPageApi(data: SelectRefundListWithPageRequest) {
	return axios.post<unknown, SelectRefundListWithPageResponse>(`${PATH}/selectRefundListWithPage`, data).then(res => res.data);
}

/**
* 忽略匹配系统商品异常退款单
*/
export function IgnoreMatchSystemItemExceptionRefundApi(data: IgnoreMatchSystemItemExceptionRefundRequest) {
	return axios.post<unknown, IgnoreMatchSystemItemExceptionRefundResponse>(`${PATH}/ignoreMatchSystemItemExceptionRefund`, data).then(res => res.data);
}

/**
* 查询编辑的退款信息
*/
export function FindSellerRefundForEditApi(data: FindSellerRefundForEditRequest) {
	return axios.post<unknown, FindSellerRefundForEditResponse>(`${PATH}/findSellerRefundForEdit`, data).then(res => res.data);
}

/**
* 确认完成
*/
export function ConfirmFinishApi(data: ConfirmFinishRequest) {
	return axios.post<unknown, ConfirmFinishResponse>(`${PATH}/confirmFinish`, data).then(res => res.data);
}

/**
* 保存编辑退款内容
*/
export function SaveEditedRefundTradeApi(data: SaveEditedRefundTradeRequest) {
	return axios.post<unknown, SaveEditedRefundTradeResponse>(`${PATH}/saveEditedRefundTrade`, data).then(res => res.data);
}

/**
* 关闭售后单
*/
export function CloseRefundApi(data: CloseRefundRequest) {
	return axios.post<unknown, CloseRefundResponse>(`${PATH}/closeRefund`, data).then(res => res.data);
}

/**
* 查询是否需要提醒同步
*/
export function SelectNeedReminderSyncApi() {
	return axios.post<unknown, SelectNeedReminderSyncResponse>(`${PATH}/selectNeedReminderSync`).then(res => res.data);
}

/**
* 手动同步退款订单
*/
export function SyncRefundTradeApi(data: SyncRefundTradeRequest) {
	return axios.post<unknown, SyncRefundTradeResponse>(`${PATH}/syncRefundTrade`, data).then(res => res.data);
}

/**
* 审核退款订单 改为使用 v2 的接口
*/
export function ReviewRefundTradeApi(data: ReviewRefundTradeRequest) {
	return axios.post<unknown, ReviewRefundTradeResponse>(`${PATH}/reviewRefundTrade`, data).then(res => res.data);
}

/**
* 批量审核退款订单
*/
export function ReviewRefundTradeV2Api(data: ReviewRefundTradeV2Request) {
	return axios.post<unknown, ReviewRefundTradeV2Response>(`${PATH}/reviewRefundTrade/v2`, data, {
		headers: {
			splitParams: true,
			notBatchRequest: true,
		}
	}).then(res => res);
}

/**
* 确认收到退货
*/
export function ConfirmReturnItemApi(data: ConfirmReturnItemRequest) {
	return axios.post<unknown, ConfirmReturnItemResponse>(`${PATH}/confirmReturnItem`, data).then(res => res.data);
}

/**
* 退货处理
*/
export function HandleReturnItemApi(data: HandleReturnItemRequest) {
	return axios.post<unknown, HandleReturnItemResponse>(`${PATH}/handleReturnItem`, data).then(res => res.data);
}

/**
* 同意退款-暂时单个处理
*/
export function AgreeRefundFeeApi(data: AgreeRefundFeeRequest) {
	// return axios.post<unknown, AgreeRefundFeeResponse>(`${PATH}/agreeRefundFee`, data).then(res => res.data);
	return instance({
		method: 'POST',
		url: `${PATH}/agreeRefundFee`,
		data,
		transformResponse: (res) => {
			let newRes = JSON.parse(res);
			newRes['isHiddenErrorMessage'] = true;
			return newRes;
		}
	});
}

/**
* 获取拒绝原因列表
*/
export function GetRefuseReasonListApi(data: GetRefuseReasonListRequest) {
	return axios.post<unknown, GetRefuseReasonListResponse>(`${PATH}/getRefuseReasonList`, data).then(res => res.data);
}

/**
* 拒绝退款-暂时单个处理
*/
export function RefuseRefundFeeApi(data: RefuseRefundFeeRequest) {
	return axios.post<unknown, RefuseRefundFeeResponse>(`${PATH}/refuseRefundFee`, data).then(res => res.data);
}

/**
* 查询是否设置默认售后地址
*/
export function QueryHasDefaultRefundAddrApi(data: QueryHasDefaultRefundAddrRequest) {
	return axios.post<unknown, QueryHasDefaultRefundAddrResponse>(`${PATH}/queryHasDefaultRefundAddr`, data).then(res => res.data);
}

/**
* 校验售后权限
*/
export function CheckAfterAuthorityApi(data: CheckAfterAuthorityRequest) {
	// return axios.post<unknown, CheckAfterAuthorityResponse>(`${PATH}/checkAfterAuthority`, data).then(res => res.data);
	return instance({
		method: 'POST',
		url: `${PATH}/checkAfterAuthority`,
		data,
		transformResponse: (res) => {
			let newRes = JSON.parse(res);
			newRes['isHiddenErrorMessage'] = true;
			return newRes;
		}
	});
}

/**
* 校验售后权限-批量退款
*/
export function CheckAfterAuthorityForBatchApi(data: CheckAfterAuthorityRequest) {
	return axios.post<unknown, CheckAfterAuthorityResponse>(`${PATH}/checkAfterAuthorityForBatch`, data, { 
		headers: { hideErrorMessage: true } 
	}).then(res => res.data);
}


/**
* 查询退款地址
*/
export function QueryRefundAddressApi(data: QueryRefundAddressRequest) {
	return axios.post<unknown, QueryRefundAddressResponse>(`${PATH}/queryRefundAddress`, data).then(res => res.data);
}

/**
* 同步退款地址
*/
export function SyncRefundAddressApi() {
	return axios.post<unknown, SyncRefundAddressResponse>(`${PATH}/syncRefundAddress`).then(res => res.data);
}

/**
 * 设置默认退货地址
 */
export function SetDefaultRefundAddressApi(data:SetDefaultRefundAddressRequest) {
	return axios.post<unknown, SetDefaultRefundAddressResponse>(`${PATH}/setDefaultRefundAddress`, data).then(res => res.data);
}

/**
* 同意退货-暂时单个处理
*/
export function AgreeRefundGoodsApi(data: AgreeRefundGoodsRequest) {
	return axios.post<unknown, AgreeRefundGoodsResponse>(`${PATH}/agreeRefundGoods`, data).then(res => res.data);
}

/**
* 拒绝退货-暂时单个处理
*/
export function RefuseRefundGoodsApi(data: RefuseRefundGoodsRequest) {
	return axios.post<unknown, RefuseRefundGoodsResponse>(`${PATH}/refuseRefundGoods`, data).then(res => res.data);
}

/**
* 检查是否需要填写退货物流单号
*/
export function CheckHasRefundLogisticsApi(data: CheckHasRefundLogisticsRequest) {
	return axios.post<unknown, CheckHasRefundLogisticsResponse>(`${PATH}/checkHasRefundLogistics`, data).then(res => res.data);
}


/**
* 新建售后-查询rds订单
*/
export function FindRdsTradeForBuildRefundApi(data: FindRdsTradeForBuildRefundRequest) {
	return axios.post<unknown, FindRdsTradeForBuildRefundResponse>(`${PATH}/findRdsTradeForBuildRefund`, data).then(res => res.data);
}

/**
* 新建售后单
*/
export function BuildRefundApi(data: BuildRefundRequest) {
	return axios.post<unknown, BuildRefundResponse>(`${PATH}/buildRefund`, data).then(res => res.data);
}


/**
* 查询用户AG退款配置
*/
export function QueryUserAgConfigApi(data?: QueryUserAgConfigRequest) {
	return axios.post<unknown, QueryUserAgConfigResponse>(`${PATH}/queryUserAgConfig`, data).then(res => res.data);
}

/**
* 查询用户AG退款配置
*/
export function SaveUserAgConfigApi(data: SaveUserAgConfigRequest) {
	return axios.post<unknown, SaveUserAgConfigResponse>(`${PATH}/saveUserAgConfig`, data).then(res => res.data);
}

/**
* 查询已开通店铺AG退款列表
*/
export function QueryPlatformShopAgConfigApi(data?: SavePlatformShopAgConfigRequest) {
	return axios.post<unknown, SavePlatformShopAgConfigResponse>(`${PATH}/queryPlatformShopAgConfig`, data).then(res => res.data);
}


/**
* 查询店铺是否开通官方ag
*/
export function QueryShopsOfficialAgConfigApi(data: QueryShopsOfficialAgConfigRequest) {
	return axios.post<unknown, QueryShopsOfficialAgConfigResponse>(`${PATH}/queryShopsOfficialAgConfig`, data).then(res => res.data);
}

/**
* 保存店铺AG退款配置
*/
export function SavePlatformShopAgConfigApi(data: SavePlatformShopAgConfigRequest) {
	return axios.post<unknown, SavePlatformShopAgConfigResponse>(`${PATH}/savePlatformShopAgConfig`, data).then(res => res.data);
}

/**
* 获取售后同步进度
*/
export function GetSyncRefundResultApi(data: GetSyncRefundResultRequest) {
	return axios.post<unknown, GetSyncRefundResultResponse>(`${PATH}/getSyncRefundResult`, data).then(res => res.data);
}
/**
 * 同意退款-批量处理
 */
export function BatchAgreeRefundFeeApi(data: BatchAgreeRefundFeeRequest) {
	return axios.post<unknown, BatchAgreeRefundFeeResponse>(`${PATH}/batchAgreeRefundFee`, data).then(res => res.data);
}

/**
* 查询是否设置默认售后地址-批量处理
*/
export function BatchQueryHasDefaultRefundAddr(data: BatchQueryHasDefaultRefundAddrRequest) {
	return axios.post<unknown, BatchQueryHasDefaultRefundAddrResponse>(`${PATH}/batchQueryHasDefaultRefundAddr`, data).then(res => res.data);
}

/**
* 查询售后默认地址及分销供应商售后地址 - 批量同意退货和同意退货
*/
export function QueryRefundAddressNewApi(data: QueryRefundAddressNewRequest) {
	return axios.post<unknown, QueryRefundAddressNewResponse>(`${PATH}/queryRefundAddressNew`, data).then(res => res.data);
}

/**
 * 同意退货-批量处理
 */
export function BatchAgreeRefundGoodsApi(data: BatchAgreeRefundGoodsRequest) {
	return axios.post<unknown, BatchAgreeRefundGoodsResponse>(`${PATH}/batchAgreeRefundGoods`, data).then(res => res.data);
}

/**
 * 确认收货-批量处理
 */
export function BatchConfirmReturnItemApi(data: BatchConfirmReturnItemRequest) {
	return axios.post<unknown, BatchConfirmReturnItemResponse>(`${PATH}/batchConfirmReturnItem`, data, {
		headers: {
			splitParams: true,
			notBatchRequest: true,
		}
	}).then(res => res);
}

/**
 * 退货入库-批量处理
 */
export function BatchHandleReturnItemApi(data: BatchHandleReturnItemRequest) {
	return axios.post<unknown, BatchHandleReturnItemResponse>(`${PATH}/batchHandleReturnItem`, data, {
		headers: {
			hideErrorMessage: true,
		}
	}).then(res => res.data);
}

/**
* 填写换货补发运单号
*/
export function UploadExchangeInvoiceNoApi(data: UploadExchangeInvoiceNoRequest) {
	return axios.post<unknown, UploadExchangeInvoiceNoResponse>(`${PATH}/uploadExchangeInvoiceNo`, data).then(res => res.data);
}


/**
* 同意补寄
*/
export function AgreeRefundSupplyApi(data: AgreeRefundSupplyRequest) {
	return axios.post<unknown, AgreeRefundSupplyResponse>(`${PATH}/agreeRefundSupply`, data).then(res => res.data);
}


/**
* 拒绝补寄
*/
export function RefuseRefundSupplyApi(data: RefuseRefundSupplyRequest) {
	return axios.post<unknown, RefuseRefundSupplyResponse>(`${PATH}/refuseRefundSupply`, data).then(res => res.data);
}

/**
* 绑定销售订单
*/
export function RefundBindTradeApi(data: RefundBindTradeRequest) {
	return axios.post<unknown, RefundBindTradeResponse>(`${PATH}/refundBindTrade`, data).then(res => res.data);
}

/**
* 绑定查询销售订单
*/
export function BindTradeQueryApi(data: BindTradeQueryRequest) {
	return instance({
		method: 'POST',
		url: `${PATH}/bindTradeQuery`,
		data,
		transformResponse: (res) => {
			let newRes = JSON.parse(res);
			newRes['isHiddenErrorMessage'] = true;
			return newRes;
		}
	});
}

/**
* 查询自动退款店铺开启状态列表
*/
export function QueryPlatformShopAgConfig2Api(data: QueryPlatformShopAgConfig2Request) {
	return axios.post<unknown, QueryPlatformShopAgConfig2Response>(`${PATH}/queryPlatformShopAgConfig2`, data).then(res => res.data);
}

/**
* 售后列表，快捷查询tab数量统计
*/
export function SelectRefundTabCountApi(data: SelectRefundTabCountRequest) {
	return axios.post<unknown, SelectRefundTabCountResponse>(`${PATH}/selectRefundTabCount`, data).then(res => res.data);
}

/**
* 售后列表，新快捷查询tab数量统计
*/
export function SelectRefundQuickFilterCountApi(data: SelectRefundTabCountRequest) {
	return axios.post<unknown, SelectRefundTabCountResponse>(`${PATH}/selectRefundQuickFilterCount`, data).then(res => res.data);
}

/**
* 售后列表，新快捷查询tab数量统计更新
*/
export function UpdateRefundQuickFilterCountApi(data: SelectRefundTabCountRequest) {
	return axios.post<unknown, SelectRefundTabCountResponse>(`${PATH}/updateRefundQuickFilterCount`, data).then(res => res.data);
}

/**
* 生成换货补发订单
*/
export function CreateExchangeTradeApi(data: CreateExchangeTradeRequest) {
	return axios.post<unknown, CreateExchangeTradeResponse>(`${PATH}/createExchangeTrade`, data).then(res => res.data);
}

/**
* 查询退款op日志
*/
export function QueryRefundOpLogApi(data: QueryRefundOpLogRequest) {
	return axios.post<unknown, QueryRefundOpLogResponse>(`${PATH}/queryRefundOpLog`, data).then(res => res.data);
}

/**
* 售后导出配置查询
*/
export function GetRefundExportConfigApi(data: GetRefundExportConfigRequest) {
	return axios.post<unknown, GetRefundExportConfigResponse>(`${PATH}/getRefundExportConfig`, data).then(res => res.data);
}

/**
* 售后导出配置更新
*/
export function UpdateRefundExportConfigApi(data: UpdateRefundExportConfigRequest) {
	return axios.post<unknown, UpdateRefundExportConfigResponse>(`${PATH}/updateRefundExportConfig`, data).then(res => res.data);
}

/**
* 恢复默认配置
*/
export function ResetRefundExportConfigApi(data: ResetRefundExportConfigRequest) {
	return axios.post<unknown, ResetRefundExportConfigResponse>(`${PATH}/resetRefundExportConfig`, data).then(res => res.data);
}

/**
* 售后全局配置查询
*/
export function GetRefundGlobalConfigListApi(data: GetRefundGlobalConfigListRequest) {
	return axios.post<unknown, GetRefundGlobalConfigListResponse>(`${PATH}/getRefundGlobalConfigList`, data).then(res => res.data);
}
/**
* 售后全局配置更新
*/
export function UpdateRefundGlobalConfigApi(data: UpdateRefundGlobalConfigRequest) {
	return axios.post<unknown, UpdateRefundGlobalConfigResponse>(`${PATH}/updateRefundGlobalConfig`, data).then(res => res.data);
}

/**
* 售后实时配置查询
*/
export function GetRealTimeRefundConfigApi(data) {
	return axios.post<unknown>(`${PATH}/getRealTimeRefundConfig`, data).then(res => res.data);
}

/**
* 查询自动备注设置
*/
export function SelectApi(data: {}) {
	return axios.post<unknown, SelectResponse>(`${PATH}/autoMemo/select`, data).then(res => res.data);
}

/**
* 更新自动备注设置
*/
export function UpdateApi(data: UpdateRequest) {
	return axios.post<unknown, UpdateResponse>(`${PATH}/autoMemo/update`, data).then(res => res.data);
}

/**
* 批量关闭售后单
*/
export function BatchCloseRefundApi(data: BatchCloseRefundRequest) {
	return axios.post<unknown, BatchCloseRefundResponse>(`${PATH}/batchCloseRefund`, data, {
		headers: {
			splitParams: true,
			notBatchRequest: true,
		}
	}).then(res => res);
}

/**
* 标记工单状态
*/
export function BatchUpdateRefundSystemStatusApi(data: BatchUpdateRefundSystemStatusRequest) {
	return axios.post<unknown, BatchUpdateRefundSystemStatusResponse>(`${PATH}/batchUpdateRefundSystemStatus`, data, {
		headers: {
			splitParams: true,
			notBatchRequest: true,
		}
	}).then(res => res);
}


/**
* 更新编辑新增线下备注常量
*/
export function CommonNoteEditCommonNoteApi(data: CommonNoteEditCommonNoteRequest) {
	return axios.post<unknown, CommonNoteEditCommonNoteResponse>(`${PATH}/commonNote/editCommonNote`, data).then(res => res.data);
}


/**
* 查询本地常用备注
*/
export function CommonNoteSelectByUserApi(data: {}) {
	return axios.post<unknown, CommonNoteSelectByUserResponse>(`${PATH}/commonNote/selectByUser`, data).then(res => res.data);
}

/**
* 批量线下备注
*/
export function BatchUpdateLocalNoteApi(data: BatchUpdateLocalNoteRequest) {
	return axios.post<unknown, BatchUpdateLocalNoteResponse>(`${PATH}/batchUpdateLocalNote`, data).then(res => res.data);
}

/**
* 批量生成换货补发订单
*/
export function BatchCreateExchangeTradeApi(data: BatchCreateExchangeTradeRequest) {
	return axios.post<unknown, BatchCreateExchangeTradeResponse>(`${PATH}/batchCreateExchangeTrade`, data).then(res => res.data);
}

/**
* 批量生成换货补发订单-获取结果
*/
export function CreateExchangeTradeAsyncGetApi(data: CreateExchangeTradeAsyncGetRequest) {
	return axios.post<unknown, CreateExchangeTradeAsyncGetResponse>(`${PATH}/createExchangeTradeAsyncGet`, data).then(res => res.data);
}

/**
* 批量推送供应商
*/
export function ScmRefundBatchPushSupplierApi(data: ScmRefundBatchPushSupplierRequest) {
	return axios.post<unknown, ScmRefundBatchPushSupplierResponse>('/aftersale/scmRefund/batchPushSupplier', data).then(res => res.data);
}
/**
* 批量推送供应商-获取结果
*/
export function ScmRefundBatchPushSupplierAsyncGetApi(data: ScmRefundBatchPushSupplierAsyncGetRequest) {
	return axios.post<unknown, ScmRefundBatchPushSupplierAsyncGetResponse>('/aftersale/scmRefund/batchPushSupplierAsyncGet', data).then(res => res.data);
}
/**
* 批量撤回推送
*/
export function ScmRefundBatchRecallApi(data: ScmRefundBatchRecallRequest) {
	return axios.post<unknown, ScmRefundBatchRecallResponse>('/aftersale/scmRefund/batchRecall', data).then(res => res.data);
}
/**
* 批量撤回推送-获取结果
*/
export function ScmRefundBatchRecallAsyncGetApi(data: ScmRefundBatchRecallAsyncGetRequest) {
	return axios.post<unknown, ScmRefundBatchRecallAsyncGetResponse>('/aftersale/scmRefund/batchRecallAsyncGet', data).then(res => res.data);
}
/**
* 批量确认取消确认售后
*/
export function ScmRefundBatchConfirmOrCancelApi(data: ScmRefundBatchConfirmOrCancelRequest) {
	return axios.post<unknown, ScmRefundBatchConfirmOrCancelResponse>('/aftersale/scmRefund/batchConfirmOrCancel', data).then(res => res.data);
}
/**
* 获取拆包登记选项参数
*/
export function GetRegisterParamListApi(data: GetRegisterParamListRequest) {
	return axios.post<unknown, GetRegisterParamListResponse>('/aftersale/getRegisterParamList', data).then(res => res.data);
}

/**
* 拆包登记
*/
export function BatchReceiveRegisterApi(data: BatchReceiveRegisterRequest) {
	return axios.post<unknown, BatchReceiveRegisterResponse>('/aftersale/batchReceiveRegister', data).then(res => res.data);
}

/**
* /selectOperateLogPage
*/
export function SelectOperateLogPageApi(data: SelectOperateLogPageRequest) {
	return axios.post<unknown, SelectOperateLogPageResponse>('/aftersale/selectOperateLogPage', data).then(res => res.data);
}

/**
* /selectOperateLogSummary
*/
export function SelectOperateLogSummaryApi(data: SelectOperateLogSummaryRequest) {
	return axios.post<unknown, SelectOperateLogSummaryResponse>('/aftersale/selectOperateLogSummary', data).then(res => res.data);
}

/**
* 拒绝退款拒绝退货-批量处理
*/
export function BatchRefuseRefundApi(data: BatchRefuseRefundRequest) {
	return axios.post<unknown, BatchRefuseRefundResponse>('/aftersale/batchRefuseRefund', data, {
		headers: {
			hideErrorMessage: true,
		}
	}).then(res => res.data);
}