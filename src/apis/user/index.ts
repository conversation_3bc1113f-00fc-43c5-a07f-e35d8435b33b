import { ResponseTypeOfKDZS } from "../../types/schemas/common/index";
import axios, { instance } from '@/utils/request';
import {
	IndexUserLoginRequest,
	IndexUserLoginResponse,
	IndexUserGetCaptchaResponse,
	IndexUserRegisterRequest,
	IndexUserRegisterResponse,
	IndexUserCheckMobileRequest,
	IndexUserChangePasswordRequest,
	IndexUserChangePasswordResponse,
	IndexUserChooseVersionRecordRequest,
	IndexUserChooseVersionRecordResponse,
	IndexUserSendSmsCodeRequest,
	IndexUserSendSmsCodeResponse,
	PlatformShopGetPlatformShopsRequest,
	PlatformShopGetPlatformShopsResponse,
	SettingGetSystemSettingRequest,
	SettingGetSystemSettingResponse,
	SettingGetUserSettingRequest,
	SettingGetUserSettingResponse,
	SettingSaveSystemSettingRequest,
	SettingSaveSystemSettingResponse,
	SettingSaveUserSettingRequest,
	SettingSaveUserSettingResponse,
	AuthoritySaveSubAccountUserRequest,
	AuthoritySaveSubAccountUserResponse,
	AuthorityGetSubAccountUserListRequest,
	AuthorityGetSubAccountUserListResponse,
	SettingGetSenderSettingRequest,
	SettingGetSenderSettingResponse,
	SettingGetRecipientSettingRequest,
	SettingGetRecipientSettingResponse,
	IndexPlatformShopGetPlatformShopsRequest,
	IndexPlatformShopGetPlatformShopsResponse,
	IndexPlatformShopEditPlatformShopRequest,
	IndexPlatformShopEditPlatformShopResponse,
	IndexSettingGetSenderSettingRequest,
	IndexSettingGetSenderSettingResponse,
	IndexSettingGetInvoicesSenderSettingRequest,
	IndexSettingGetInvoicesSenderSettingResponse,
	IndexSettingEditSenderSettingRequest,
	IndexSettingEditSenderSettingResponse,
	IndexSettingEditInvoicesSenderSettingRequest,
	IndexSettingEditInvoicesSenderSettingResponse,
	IndexSettingEditRecipientSettingRequest,
	IndexSettingEditRecipientSettingResponse,
	IndexUserCheckMobileResponse,
	IndexSettingEditPopupSettingRequest,
	IndexSettingEditPopupSettingResponse,
	OperateShopRequest,
	IndexSaveFeedbackRequest,
	IndexRemarkGetCommonRemarkRequest,
	IndexRemarkGetCommonRemarkResponse,
	IndexRemarkEditCommonRemarkRequest,
	IndexAuthorityGetLoginLogRequest,
	IndexAuthorityGetLoginLogResponse,
	ItemGetShopItemCountRequest,
	ItemGetShopItemCountResponse,
	ItemGetNotImportShopsResponse,
	IndexExpressArriveGetExpressArriveListRequest,
	IndexExpressArriveGetExpressArriveListResponse,
	IndexExpressArriveDeleteExpressArriveByIdResponse,
	IndexExpressArriveDeleteExpressArriveByIdRequest,
	IndexExpressArriveSaveOrEditExpressArriveRequest,
	IndexExpressArriveSaveOrEditExpressArriveResponse,
	IndexExpressArriveCloseOrOpenExpressArriveRequest,
	IndexExpressArriveCloseOrOpenExpressArriveResponse,
	IndexExpressArriveExcelImportAnalysisExpressArriveRequest,
	IndexExpressArriveExcelImportAnalysisExpressArriveResponse,
	IndexNewUserGuideSelectResponse,
	IndexNewUserGuideUpdateRequest,
	IndexNewUserGuideUpdateResponse,
	IndexAuthorityGetAfterSaleAuthoritySetListRequest,
	IndexAuthorityGetAfterSaleAuthoritySetListResponse,
	IndexAuthorityUpdateAfterSaleAuthorityRequest,
	IndexAuthorityUpdateAfterSaleAuthorityResponse,
	PrintCenterExCompanyGetExCompanyAllResponse,
	IndexUserCheckInvitationCodeRequest,
	IndexUserCheckInvitationCodeResponse,
	IndexSystemOperateLogQueryByConditionResponse,
	IndexSystemOperateLogQueryByConditionRequest,
	IndexSystemOperateLogUploadRequest,
	IndexActivityFinishActivityRequest,
	IndexActivityFinishActivityResponse,
	IndexActivityGetActivityInfoRequest,
	IndexActivityGetActivityInfoResponse,
	IndexActivityGetTakeGoodsLabelActivityDataCountResponse,
	LoginCustomShopCreateResponse,
	IndexUserCheckMobileUserTypeResponse,
	ItemSysSkuGetGroupPackageOperationLogResponse,
	IndexUserChangeUserMobileResponse,
	IndexUserChangeUserMobileRequest,
	IndexUserHeartbeatResponse,
	IndexEnvEnvSetRequest,
	IndexEnvEnvSetResponse,
	TradeLogisticsQueryLogisticsWarnIndexCountResponse,
	IndexSettingUpdateMergeMaxSizeRequest,
	IndexSettingUpdateMergeMaxSizeResponse,
	IndexAuthorityGetAuthorityTemplateResponse,
	IndexAuthorityEditSubAccountUserRequest,
	IndexAuthorityEditSubAccountUserResponse,
	IndexPlatformShopGetUnexpiredPlatformShopListResponse,
	IndexAuthoritySelectBranchListRequest,
	IndexAuthoritySelectBranchListResponse,
	IndexAuthoritySelectExpressBranchAuthorityRequest,
	IndexAuthoritySelectExpressBranchAuthorityResponse,
	AuthorityEditSubAccountUserRequest,
	AuthorityEditSubAccountUserResponse,
	IndexAuthorityUpdateUserAddressWaybillRequest,
	IndexAuthorityUpdateUserAddressWaybillResponse,
	TradeSyncAbnormalOrTokenExpiredShopListRequest, TradeSyncAbnormalOrTokenExpiredShopListResponse,
	TradeSellerInfoGetHistorySellerMemosListRequest, TradeSellerInfoGetHistorySellerMemosListResponse,
	TradeSellerInfoGetSellerFlagListRequest, TradeSellerInfoGetSellerFlagListResponse,
	IndexSettingUpdateUserShopRemindRequest, IndexSettingUpdateUserShopRemindResponse,
	IndexPlatformShopEditPlatformShopSortRequest, IndexPlatformShopEditPlatformShopSortResponse,
	IndexSettingUpdateMergeStrategyConfigRequest, IndexSettingUpdateMergeStrategyConfigResponse,
	IndexSettingSelectUserOperateTimeRequest, IndexSettingSelectUserOperateTimeResponse,
	QueryAfterAuthorityUserListRequest, QueryAfterAuthorityUserListResponse,
	PlatformShopPuhuoSetting,
	CategoryTreeRequest,
	CategoryTreeResponse
} from '@/types/schemas/user';
import { getToken } from "@/utils/token";


const PREFIX = '/index';

/**
* 平台店铺 重新授权、店铺绑定调用接口       source        1重新授权       2店铺绑定
*/
export function LoginUserAuthApi(data: any) {
	return axios.post<unknown>('/login/userAuth', data).then(res => res.data);
}
/**
 * 获取用户信息接口
 */
export function getUserInfoApi() {
	return axios.post(`${PREFIX}/user/getUserInfo`, {}, {
		headers: {
			hideErrorMessage: true,
		}
	}).then(res => res.data);
}

/**
 * 获取图形验证码
 */
export function getCaptchaApi() {
	return axios.post<unknown, IndexUserGetCaptchaResponse>(`${PREFIX}/user/getCaptcha`).then(res => res.data);
}
/**
 * 登录
 */
export function userLoginApi(params: IndexUserLoginRequest) {
	return axios.post<unknown, IndexUserLoginResponse>(`${PREFIX}/user/login`, params).then(res => res.data);
}

/**
 * 轮询登录
 */
export function saoUserLoginApi(params: IndexUserLoginRequest) {
	return axios.post<unknown, IndexUserLoginResponse>(`${PREFIX}/user/login`, params, {
		headers: {
			hideErrorMessage: true // 添加这个头部，告诉拦截器不要显示错误消息
		}
	}).then(res => res.data);
}

/**
 * 注册
 */
export function userRegisterApi(params: IndexUserRegisterRequest) {
	return axios.post<unknown, IndexUserRegisterResponse>(`${PREFIX}/user/register`, params).then(res => res.data);
}

/**
 * 修改密码
 */
export function changePasswordApi(params: IndexUserChangePasswordRequest) {
	return axios.post<unknown, IndexUserChangePasswordResponse>(`${PREFIX}/user/changePassword`, params).then(res => res.data);
}

/**
 * 重置密码
 */
export function IndexUserResetPasswordApi(params: IndexUserChangePasswordRequest) {
	return axios.post<unknown, IndexUserChangePasswordResponse>(`${PREFIX}/user/resetPassword`, params).then(res => res.data);
}

/**
 * 发送短信验证码
 */
export function sendSmsCodeApi(params: IndexUserSendSmsCodeRequest) {
	return axios.post<IndexUserSendSmsCodeResponse>(`${PREFIX}/user/sendSmsCode`, params).then(res => res);
}

// 验证type12
export function checkSmsCodeApi(params: IndexUserSendSmsCodeRequest) {
	return axios.post<IndexUserChangeUserMobileResponse>(`/index/user/checkSmsCode`, params).then(res => res);
}

/**
 * 修改用户手机号
 */
export function changeUserMobileApi(params: IndexUserChangeUserMobileRequest) {
	return axios.post<IndexUserChangeUserMobileResponse>(`${PREFIX}/user/changeUserMobile`, params).then(res => res);
}

// /**
//  * 保存修改子账号
//  */
// export function saveSubAccountUserApi(params: AuthoritySaveSubAccountUserRequest) {
// 	return axios.get<AuthoritySaveSubAccountUserResponse>('/authority/saveSubAccountUser', {
// 	}).then(res => res.data);
// }

/**
* 获取店铺
*/
// export function PlatformShopGetPlatformShopsApi(data: PlatformShopGetPlatformShopsRequest) {
// 	return axios.post<unknown, PlatformShopGetPlatformShopsResponse>(`${PREFIX}/platformShop/getPlatformShops`, data).then(res => res.data);
// }

/**
* 获取系统设置
*/
export function SettingGetSystemSettingApi(data: SettingGetSystemSettingRequest) {
	return axios.post<unknown, SettingGetSystemSettingResponse>('/index/setting/getSystemSetting', data).then(res => res.data);
}

/**
* 获取用户设置
*/
export function SettingGetUserSettingApi(data: SettingGetUserSettingRequest) {
	return axios.post<unknown, SettingGetUserSettingResponse>('/index/setting/getUserSetting', data).then(res => res.data);
}

/**
* 添加修改系统设置
*/
export function SettingSaveSystemSettingApi(data: SettingSaveSystemSettingRequest) {
	return axios.post<unknown, SettingSaveSystemSettingResponse>('/index/setting/saveSystemSetting', data).then(res => res.data);
}

/**
* 添加修改用户设置
*/
export function SettingSaveUserSettingApi(data: SettingSaveUserSettingRequest) {
	return axios.post<unknown, SettingSaveUserSettingResponse>('/index/setting/saveUserSetting', data).then(res => res.data);
}

/**
* 设置最大合单数量 （弃用）
*/
export function IndexSettingUpdateMergeMaxSizeApi(data: IndexSettingUpdateMergeMaxSizeRequest) {
	return axios.post<unknown, IndexSettingUpdateMergeMaxSizeResponse>('/index/setting/updateMergeMaxSize', data).then(res => res.data);
}

/**
* 更新合单策略
*/
export function IndexSettingUpdateMergeStrategyConfigApi(data: IndexSettingUpdateMergeStrategyConfigRequest) {
	return axios.post<unknown, IndexSettingUpdateMergeStrategyConfigResponse>('/index/setting/updateMergeStrategyConfig', data).then(res => res);
}

/**
* 查询用户操作时间
*/
export function IndexSettingSelectUserOperateTimeApi(data: IndexSettingSelectUserOperateTimeRequest) {
	return axios.post<unknown, IndexSettingSelectUserOperateTimeResponse>('/index/setting/selectUserOperateTime', data).then(res => res.data);
}

/**
* 保存修改子账号
*/
export function AuthoritySaveSubAccountUserApi(data: AuthoritySaveSubAccountUserRequest) {
	return axios.post<unknown, AuthoritySaveSubAccountUserResponse>('/index/authority/saveSubAccountUser', data).then(res => res.data);
}

/**
* 子账号查询
*/
export function AuthorityGetSubAccountUserListApi(data: AuthorityGetSubAccountUserListRequest) {
	return axios.post<unknown, AuthorityGetSubAccountUserListResponse>('/index/authority/getSubAccountUserList', data).then(res => res.data);
}

/**
* 获取收件人设置(只需要传userId或者什么都不用传)
*/
export function SettingGetRecipientSettingApi(data: SettingGetRecipientSettingRequest) {
	return axios.post<unknown, SettingGetRecipientSettingResponse>('/index/setting/getRecipientSetting', data).then(res => res.data);
}

/**
* 获取店铺
*/
export function IndexPlatformShopGetPlatformShopsApi(data: IndexPlatformShopGetPlatformShopsRequest) {
	return axios.post<unknown, IndexPlatformShopGetPlatformShopsResponse>('/index/platformShop/getPlatformShops', data).then(res => res.data);
}

/**
* 获取用户单量限额信息
*/
export function GetNumLimit() {
	return axios.get<unknown, any>('/trade/use/limit/numLimit').then(res => res.data);
}

/**
* 编辑店铺的排序
*/
export function IndexPlatformShopEditPlatformShopSortApi(data: IndexPlatformShopEditPlatformShopSortRequest) {
	return axios.post<unknown, IndexPlatformShopEditPlatformShopSortResponse>('/index/platformShop/editPlatformShopSort', data).then(res => res.data);
}

/**
* 编辑店铺
*/
export function IndexPlatformShopEditPlatformShopApi(data: IndexPlatformShopEditPlatformShopRequest) {
	return axios.post<unknown, IndexPlatformShopEditPlatformShopResponse>('/index/platformShop/editPlatformShop', data).then(res => res.data);
}


/**
* 获取面单店铺
*/
export function IndexVirtualPlatformShopGetPlatformShopsApi(data: IndexPlatformShopGetPlatformShopsRequest) {
	return axios.post<unknown, IndexPlatformShopGetPlatformShopsResponse>('/index/VirtualPlatformShop/getPlatformShops', data).then(res => res.data);
}

/**
* 编辑面单店铺
*/
export function IndexVirtualPlatformShopEditPlatformShopApi(data: IndexPlatformShopEditPlatformShopRequest) {
	return axios.post<unknown, IndexPlatformShopEditPlatformShopResponse>('/index/VirtualPlatformShop/editPlatformShop', data).then(res => res.data);
}

/**
* 获取快递单发件人设置
*/
export function IndexSettingGetSenderSettingApi(data: IndexSettingGetSenderSettingRequest) {
	return axios.post<unknown, IndexSettingGetSenderSettingResponse>('/index/setting/getSenderSetting', data).then(res => res.data);
}

/**
* 获取发货单发件人设置
*/
export function IndexSettingGetInvoicesSenderSettingApi(data: IndexSettingGetInvoicesSenderSettingRequest) {
	return axios.post<unknown, IndexSettingGetInvoicesSenderSettingResponse>('/index/setting/getInvoicesSenderSetting', data).then(res => res.data);
}

/**
* 添加修改快递单发件人设置
*/
export function IndexSettingEditSenderSettingApi(data: IndexSettingEditSenderSettingRequest) {
	return axios.post<unknown, IndexSettingEditSenderSettingResponse>('/index/setting/editSenderSetting', data).then(res => res.data);
}

/**
* 添加修改发货单发件人设置
*/
export function IndexSettingEditInvoicesSenderSettingApi(data: IndexSettingEditInvoicesSenderSettingRequest) {
	return axios.post<unknown, IndexSettingEditInvoicesSenderSettingResponse>('/index/setting/editInvoicesSenderSetting', data).then(res => res.data);
}

/**
* 添加修改收件人设置
*/
export function IndexSettingEditRecipientSettingApi(data: IndexSettingEditRecipientSettingRequest) {
	return axios.post<unknown, IndexSettingEditRecipientSettingResponse>('/index/setting/editRecipientSetting', data).then(res => res.data);
}

/**
* 发送验证短信
*/
export function IndexPlatformShopSendSmsCodeApi(data: {mobile: string, type?: string}) {
	return axios.post<unknown, ResponseTypeOfKDZS<boolean>>('/index/platformShop/sendSmsCode', data, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }).then(res => res.data);
}

/**
* 获取基础权限模版
*/
export function AuthorityGetAuthorityTemplateApi() {
	return axios.post<unknown, IndexAuthorityGetAuthorityTemplateResponse>('/index/authority/getAuthorityTemplate').then(res => res.data);
}

/**
* 修改密码前校验
*/
export function IndexUserCheckMobileApi(data: IndexUserCheckMobileRequest) {
	return axios.post<unknown, IndexUserCheckMobileResponse>('/index/user/checkMobile', data).then(res => res.data);
}

/**
* 修改密码
*/
export function IndexUserChangePasswordApi(data: IndexUserChangePasswordRequest) {
	return axios.post<unknown, IndexUserChangePasswordResponse>('/index/user/changePassword', data).then(res => res.data);
}

/**
* 统一编辑接口
*/
export function AuthorityEditSubAccountUserApi(data: IndexAuthorityEditSubAccountUserRequest) {
	return axios.post<unknown, IndexAuthorityEditSubAccountUserResponse>('/index/authority/editSubAccountUser', data).then(res => res.data);
}

/**
* 选择版本
*/
export function IndexUserChooseVersionRecordApi(data: IndexUserChooseVersionRecordRequest) {
	return axios.post<unknown, IndexUserChooseVersionRecordResponse>('/index/user/chooseVersionRecord', data).then(res => res.data);
}


/**
* 添加修改弹窗设置
*/
export function IndexSettingEditPopupSettingApi(data: IndexSettingEditPopupSettingRequest) {
	return axios.post<unknown, IndexSettingEditPopupSettingResponse>('/index/setting/editPopupSetting', data).then(res => res.data);
}

/**
* 校验手机号
*/
export function IndexUserCheckRepeatMobileApi(data: {mobile: string, captcha: string}) {
	return axios.post<unknown, ResponseTypeOfKDZS<boolean>>('/index/user/checkRepeatMobile', data, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }).then(res => res.data);
}


/**
* 校验手机是否重复，并返回当前手机号的用户类型
*/
export function checkMobileUserTypeApi(data: {mobile: string, captcha: string}) {
	return axios.post<unknown, IndexUserCheckMobileUserTypeResponse>('/index/user/checkMobileUserType', data, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }).then(r => r.data);
}



/**
* init或更新同步配置
*/
export function InitOrUpdateRefundSyncConfigApi() {
	return axios.post<unknown, ResponseTypeOfKDZS<boolean>>('/aftersale/initOrUpdateRefundSyncConfig').then(res => res.data);
}

/**
* 操作店铺
*/
export function OperateShopApi(data: OperateShopRequest) {
	return axios.post<unknown, ResponseTypeOfKDZS<boolean>>('/aftersale/operateShop', data).then(res => res.data);
}

/**
* 校验用户名称
*/
export function IndexUserCheckRepeatUserNameApi(data: {subUsername: string, captcha: string, }) {
	return axios.post<unknown, ResponseTypeOfKDZS<boolean>>('/index/user/checkRepeatUserName', data, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }).then(res => res.data);
}

/**
* 首页买家售后退款数统计
*/
export function IndexBuyerRefundCountApi() {
	return axios.post<unknown, ResponseTypeOfKDZS<Array<any>>>('/aftersale/indexBuyerRefundCount2', {}).then(res => res.data);
}

/**
* 查询货品异常数量-规格纬度
*/
export function ItemSysSkuGetExceptionCountApi() {
	return axios.post<unknown, ResponseTypeOfKDZS<boolean>>('/item/sysSku/getExceptionCount', {}).then(res => res.data);
}

/**
* 获取公告
*/
export function IndexGetNoticeApi() {
	return axios.post<unknown, ResponseTypeOfKDZS<string>>('/index/getNotice', {}).then(res => res.data);
}

/**
* 添加意见反馈
*/
export function IndexSaveFeedbackApi(data: IndexSaveFeedbackRequest) {
	return axios.post<unknown, ResponseTypeOfKDZS<boolean>>('/index/saveFeedback', data).then(res => res.data);
}

/**
* 查询未初始化总数
*/
export function StockNotInitGetTotalApi() {
	return axios.post<unknown, ResponseTypeOfKDZS<boolean>>('/stock/notInit/getTotal', {}).then(res => res.data);
}

/**
* 查询物流预警首页统计
*/
export function TradeLogisticsQueryLogisticsWarnIndexCountApi(data) {
	return axios.post<unknown, TradeLogisticsQueryLogisticsWarnIndexCountResponse>('/trade/logistics/queryLogisticsWarnIndexCount', data).then(res => res.data);
}

/**
* 校验图形验证码
*/
export function IndexUserCheckCaptchaApi(data: {captcha: string}) {
	return axios.post<unknown, ResponseTypeOfKDZS<boolean>>('/index/user/checkCaptcha', data, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }).then(res => res.data);
}

/**
* 获取常用备注短语
*/
export function IndexRemarkGetCommonRemarkApi(data: IndexRemarkGetCommonRemarkRequest) {
	return axios.post<unknown, IndexRemarkGetCommonRemarkResponse>('/index/remark/getCommonRemark', data).then(res => res.data);
}

/**
* 编辑常用备注短语
*/
export function IndexRemarkEditCommonRemarkApi(data: IndexRemarkEditCommonRemarkRequest) {
	return axios.post<unknown, ResponseTypeOfKDZS<any>>('/index/remark/editCommonRemark', data).then(res => res.data);
}

/**
* 查询订单历史备注
*/
export function TradeSellerInfoGetHistorySellerMemosListApi(data: TradeSellerInfoGetHistorySellerMemosListRequest) {
	return axios.post<unknown, TradeSellerInfoGetHistorySellerMemosListResponse>('/trade/sellerInfo/getHistorySellerMemosList', data).then(res => res.data);
}

/**
* 查询店铺旗帜
*/
export function TradeSellerInfoGetSellerFlagListApi(data: TradeSellerInfoGetSellerFlagListRequest) {
	return axios.post<unknown, TradeSellerInfoGetSellerFlagListResponse>('/trade/sellerInfo/getSellerFlagList', data).then(res => res.data);
}

/**
* 子账号登录日志查询
*/
export function IndexAuthorityGetLoginLogApi(data: IndexAuthorityGetLoginLogRequest) {
	return axios.post<unknown, IndexAuthorityGetLoginLogResponse>('/index/authority/getLoginLog', data).then(res => res.data);
}

/**
* 查询店铺商品数量(在售+仓库)
*/
export function ItemGetShopItemCountApi(data: ItemGetShopItemCountRequest) {
	return axios.post<unknown, ItemGetShopItemCountResponse>('/item/getShopItemCount', data).then(res => res.data);
}

/**
* 查询没有导入平台商品的店铺
*/
export function ItemGetNotImportShopsApi(data = {}) {
	return axios.post<unknown, ItemGetNotImportShopsResponse>('/item/getNotImportShops', data).then(res => res.data);
}


export function IndexUserLoginOut(data = {}) {
	return axios.post<unknown, ItemGetNotImportShopsResponse>('/index/user/loginOut', data, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }).then(res => res.data);
}

/**
* 多条件查询快递可达记录
*/
export function IndexExpressArriveGetExpressArriveListApi(data: IndexExpressArriveGetExpressArriveListRequest) {
	return axios.post<unknown, IndexExpressArriveGetExpressArriveListResponse>('/index/expressArrive/getExpressArriveList', data).then(res => res.data);
}

/**
* 根据id删除
*/
export function IndexExpressArriveDeleteExpressArriveByIdApi(data: IndexExpressArriveDeleteExpressArriveByIdRequest) {
	return axios.post<unknown, IndexExpressArriveDeleteExpressArriveByIdResponse>('/index/expressArrive/deleteExpressArriveById', data).then(res => res.data);
}

// 获取所有的快递公司
export function getExCompanyAll(data: {}) {
	return axios.post<unknown, PrintCenterExCompanyGetExCompanyAllResponse>('/print/center/exCompany/getExCompanyAll', data).then(res => res.data);

}
export function getExCompanyInit() {
	return axios.post<unknown, PrintCenterExCompanyGetExCompanyAllResponse>('/print/center/exCompany/initExCompany').then(res => res.data);
}
/**
* 添加修改 可达记录
*/
export function IndexExpressArriveSaveOrEditExpressArriveApi(data: IndexExpressArriveSaveOrEditExpressArriveRequest) {
	return axios.post<unknown, IndexExpressArriveSaveOrEditExpressArriveResponse>('/index/expressArrive/saveOrEditExpressArrive', data).then(res => res.data);
	// return instance({
	// 	method: 'POST',
	// 	headers: {
	// 		'Content-Type': 'application/x-www-form-urlencoded'
	// 	},
	// 	url: '/index/expressArrive/saveOrEditExpressArrive',
	// 	data,
	// });
}

/**
* 快递可达——打开关闭操作
*/
export function IndexExpressArriveCloseOrOpenExpressArriveApi(data: IndexExpressArriveCloseOrOpenExpressArriveRequest) {
	return axios.post<unknown, IndexExpressArriveCloseOrOpenExpressArriveResponse>('/index/expressArrive/closeOrOpenExpressArrive', data).then(res => res.data);
}

/**
* excel导入分析快递可达区域
*/
export function IndexExpressArriveExcelImportAnalysisExpressArriveApi(data: IndexExpressArriveExcelImportAnalysisExpressArriveRequest) {
	return instance({
		method: 'POST',
		headers: {
			'Content-Type': 'multipart/form-data'
		},
		url: '/index/expressArrive/excel/import/analysisExpressArrive',
		data,
	});

	// return axios.post<unknown, IndexExpressArriveExcelImportAnalysisExpressArriveResponse>('/index/expressArrive/excel/import/analysisExpressArrive', data).then(res => res.data);
}


/**
* /index/newUserGuide/select
*/
export function IndexNewUserGuideSelectApi(data: {}) {
	return axios.post<unknown, IndexNewUserGuideSelectResponse>('/index/newUserGuide/select', data).then(res => res.data);
}

/**
* /index/newUserGuide/update
*/
export function IndexNewUserGuideUpdateApi(data: IndexNewUserGuideUpdateRequest) {
	return axios.post<unknown, IndexNewUserGuideUpdateResponse>('/index/newUserGuide/update', data).then(res => res.data);
}

/**
* 获取售后权限店铺子账号集合
*/
export function IndexAuthorityGetAfterSaleAuthoritySetListApi(data:IndexAuthorityGetAfterSaleAuthoritySetListRequest) {
	return axios.post<unknown, IndexAuthorityGetAfterSaleAuthoritySetListResponse>('/index/authority/getAfterSaleAuthoritySetList', data).then(res => res.data);
}

/**
* 修改子账号售后权限
*/
export function IndexAuthorityUpdateAfterSaleAuthorityApi(data: IndexAuthorityUpdateAfterSaleAuthorityRequest) {
	return axios.post<unknown, IndexAuthorityUpdateAfterSaleAuthorityResponse>('/index/authority/updateAfterSaleAuthority', data).then(res => res.data);
}

export function IndexUserConfigSettingApi(data: any) {
	return axios.post<unknown, any>('/index/setting/getUserConfigSetting', data).then(res => res.data);
}

export function IndexSaveUserConfigSettingApi(data: any) {
	return axios.post<unknown, any>('/index/setting/saveUserConfigSetting', data).then(res => res.data);
}

// 上传系统日志
export function SystemOperateLogUploadApi(data: IndexSystemOperateLogUploadRequest) {
	return axios.post<unknown, any>('/index/systemOperateLog/upload', data).then(res => res.data);
}
// 查询系统日志
export function SystemOperateLogQueryByConditionApi(data: IndexSystemOperateLogQueryByConditionRequest) {
	return axios.post<unknown, IndexSystemOperateLogQueryByConditionResponse>('/index/systemOperateLog/queryByCondition', data).then(res => res.data);
}

/**
* 校验邀请码
*/
export function IndexUserCheckInvitationCodeApi(data: IndexUserCheckInvitationCodeRequest) {
	return axios.post<unknown, IndexUserCheckInvitationCodeResponse>(
		'/index/user/checkInvitationCode',
		data,
		{
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			}
		}
	).then(res => res.data);
}

/**
* 获取活动完成情况
*/
export function IndexActivityGetActivityInfoApi(data: IndexActivityGetActivityInfoRequest) {
	return axios.post<unknown, IndexActivityGetActivityInfoResponse>('/index/activity/getActivityInfo', data).then(res => res.data);
}

/**
* 完成活动
*/
export function IndexActivityFinishActivityApi(data: IndexActivityFinishActivityRequest) {
	return axios.post<unknown, IndexActivityFinishActivityResponse>('/index/activity/finishActivity', data).then(res => res.data);
}

/**
* 获取小标签活动数据
*/
export function IndexActivityGetTakeGoodsLabelActivityDataCountApi(data: {}) {
	return axios.post<unknown, IndexActivityGetTakeGoodsLabelActivityDataCountResponse>('/index/activity/getTakeGoodsLabelActivityDataCount', data).then(res => res.data);
}

/**
* 创建自定义店铺
*/
export function LoginCustomShopCreateApi(data: {}) {
	return axios.post<unknown, LoginCustomShopCreateResponse>('/login/customShopCreate', data, {
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded'
		} }).then(res => res.data);
}

/**
* 组合打包日志数量查询
*/
export function ItemSysSkuGetGroupPackageOperationLogApi() {
	return axios.post<unknown, ItemSysSkuGetGroupPackageOperationLogResponse>('/item/sysSku/getGroupPackageOperationLog', {}).then(res => res.data);
}

/**
* 用户心跳接口
*/
export function IndexUserHeartbeatApi(data: {}) {
	return axios.post<unknown, IndexUserHeartbeatResponse>('/index/user/heartbeat', data).then(res => res.data);
}

/**
* 切换用户域名
*/
export function IndexEnvEnvSetApi(data: IndexEnvEnvSetRequest) {
	return axios.post<unknown, IndexEnvEnvSetResponse>('/index/env/envSet', data).then(res => res.data);
}
/**
 * 查询未过期店铺信息集合
*/
export function QueryShopSizeLimitApi({ userId }) {
	return axios.get<unknown, unknown>(`/index/platformShop/queryShopSizeLimit?userId=${userId}`, {}).then(res => res.data);
}

/**
 * 查询未过期店铺信息集合
*/
export function GetUnexpiredPlatformShopListApi({ userId }) {
	return axios.get<unknown, IndexPlatformShopGetUnexpiredPlatformShopListResponse>(`/index/platformShop/getUnexpiredPlatformShopList?userId=${userId}`, {}).then(res => res.data);
}


/**
 * 查询网点信息集合
*/
export function SelectBranchListApi() {
	return axios.post<unknown, IndexAuthoritySelectBranchListResponse>(`/index/authority/selectBranchListV2`).then(res => res.data);
}


/**
 * 查询已保存的面单网点权限数据
*/
export function SelectExpressBranchAuthorityApi(data: IndexAuthoritySelectExpressBranchAuthorityRequest) {
	return axios.post<unknown, IndexAuthoritySelectExpressBranchAuthorityResponse>(`/index/authority/selectExpressBranchAuthority`, data).then(res => res.data);
}


/**
 * 保存面单网点权限
 */
export function EditSubAccountUserApi(data: AuthorityEditSubAccountUserRequest) {
	return axios.post<unknown, AuthorityEditSubAccountUserResponse>(`/authority/editSubAccountUser`, data).then(res => res.data);
}
/**
 * 更改用户默认网点地址
 */
export function UpdateUserAddressWaybillApi(data: IndexAuthorityUpdateUserAddressWaybillRequest) {
	return axios.post<unknown, IndexAuthorityUpdateUserAddressWaybillResponse>(`/index/authority/updateUserAddressWaybill`, data).then(res => res.data);
}
/**
* 获取用户的token过期的店铺和订单同步异常的店铺
*/
export function TradeSyncAbnormalOrTokenExpiredShopListApi(data: TradeSyncAbnormalOrTokenExpiredShopListRequest) {
	return axios.get<unknown, TradeSyncAbnormalOrTokenExpiredShopListResponse>('/trade/syncAbnormalOrTokenExpiredShopList', data).then(res => res.data);
}

/**
* 不再提醒异常店铺
*/
export function IndexSettingUpdateUserShopRemindApi(data: IndexSettingUpdateUserShopRemindRequest) {
	return axios.post<unknown, IndexSettingUpdateUserShopRemindResponse>('/index/setting/updateUserShopRemind', data).then(res => res.data);
}

export function InitAccountExpressAuthorityApi(data: {isInit: boolean}) {
	return axios.post<unknown, any>(`/index/authority/initAccountExpressAuthority`, data, {
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded;'
		} }).then(res => res.data);
}

export function IndexAsyncGetProgressApi(data) {
	return axios.post<unknown, IndexSettingUpdateUserShopRemindResponse>('/index/async/getProgress', data).then(res => res.data);
}

/**
* 子账号授权列表
*/
export function QueryAfterAuthorityUserListApi(data: QueryAfterAuthorityUserListRequest) {
	return axios.post<unknown, QueryAfterAuthorityUserListResponse>('/aftersale/queryAfterAuthorityUserList', data).then(res => res.data);
}

// 查询平台商品上限信息
export function QueryPlatformItemCountLimitInfo(data) {
	return axios.post<unknown, IndexSettingUpdateUserShopRemindResponse>('/item/item/queryPlatformItemCountLimitInfo', data).then(res => res.data);
}

// 获取小程序扫码;
export function GetQrcode(data) {
	return axios.post<unknown, any>('/index/wx/getQrcode', data).then(res => res.data);
}


/**
 * 验证微信绑定轮询
 */
export function checkWxBindApi(data, token) {
	return axios.post<IndexUserSendSmsCodeResponse>(`/index/user/checkWxBind`, data, {
		headers: {
			qnquerystring: token,
			hideErrorMessage: true // 添加这个头部，告诉拦截器不要显示错误消息
		}
	})
		.then(res => res);
}

// 微信是否绑定
export function GeBindWxMpOpenId() {
	return axios.get<any, any>('/index/user/hasBindWxMpOpenId').then(res => res.data);
}
// 微信解绑
export function UnBindWxMpOpenId() {
	return axios.get<unknown, any>('/index/user/unBindWxMpOpenId').then(res => res.data);
}
export function getUserExpressTemplate(data) {
	return axios.post<unknown, any>('/item/distribute/getUserExpressTemplate', data).then(res => res.data);
}
export function getUserShipAddress(data) {
	return axios.post<unknown, any>('/item/distribute/getUserShipAddress', data).then(res => res.data);
}

export function getPlatformShopPuhuoSetting(data) {
	return axios.post<unknown, PlatformShopPuhuoSetting>('/item/distribute/getPlatformShopPuhuoSetting', data).then(res => res.data);
}
export function getPlatformShopPuhuoSettingList(data) {
	return axios.post<unknown, PlatformShopPuhuoSetting>('/item/distribute/getPlatformShopPuhuoSettingList', data).then(res => res.data);
}

export function updatePlatformPuhuoSetting(data) {
	return axios.post<unknown, any>('/item/distribute/updatePlatformPuhuoSetting', data).then(res => res.data);
}

export function batchPublish(data) {
	return axios.post<unknown, any>('/item/distribute/batchPublish', data).then(res => res.data);
}

/**
 * 获取商品类目树
 */
export function getCategoryTreeApi(data: CategoryTreeRequest, hideErrorMessage?: boolean) {
	return axios.post<unknown, CategoryTreeResponse>('/item/distribute/categoryTree', data, {
		headers: {
			hideErrorMessage, // 添加这个头部，告诉拦截器不要显示错误消息
		}
	}).then(res => res.data);
}
