/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-06-13 09:30:53
 * @Description: 
 */
import { GetDyDefaultAddressRequest, GetDyDefaultAddressResponse, GetTemplateInfoByShowIdRequest, GetTemplateInfoByShowIdResponse } from "@/types/schemas/printCenter";
import axios from "@/utils/request";

/**
 * 获取打印中心版本
 */
export function getPrintCenterVersionApi(platform: string) {
	return axios
		.get<string>(
			"https://public-gateway.kuaidizs.cn/general/print/jsversion",
			{
				params: {
					platform,
				},
			}
		)
		.then((res) => res.data);
}

// 获取模板详情
export function getTemplateInfoByShowIdApi(data: GetTemplateInfoByShowIdRequest) {
	return axios
		.post<unknown, GetTemplateInfoByShowIdResponse>("print/center/modeListshow/getTemplateInfoByShowId", data, {
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded;'
			}
		}).then((res) => res.data);
}

// 获取网点地址
export function getDyDefaultAddressApi(data: GetDyDefaultAddressRequest) {
	return axios
		.get<unknown, GetDyDefaultAddressResponse>("print/center/dyDefaultAddress/getDyDefaultAddress", { params: data }).then((res) => res.data);
}



// 获取控件下载地址
export function getControlDonloadUrl() {
	return axios
		.get<any>("/print/center/elec/getPrintControlUrl", { }).then((res) => res.data);
}

// 获取聚合打印模版组
export function printCenterGetGroupListApi(data) {
	return axios.post<unknown>("print/center/group/getGroupList", data).then((res) => res.data);
}

// 获取聚合打印模版
export function printCenterGetTemplateListApi(data) {
	return axios.post<unknown>("print/center/modeListshow/getTemplateList", data, {
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded;'
		} }).then((res:any) => res.data);
}

// 获取快递公司列表
export function printCenterGetExCompanyAllApi(data) {
	return axios.post<unknown>("print/center/exCompany/getExCompanyAll", data).then((res) => res.data);
}