import React, { useEffect } from 'react';
import { Alert } from 'antd';
import { SelectSaleProfitSyncResult, SelectStockSyncResult } from '@/apis/report/accountCheck';

interface RedTestComponentProps {
type?:string
}

const showList = [
	{
		value: 0,
		type: 'warning',
		label: '温馨提示：报表数据等待更新中，昨日数据存在延迟，请稍后重新查询'
	},
	{
		value: 2,
		type: 'warning',
		label: '温馨提示：报表数据正在更新中，昨日数据存在延迟，请稍后重新查询'
	},
	{
		value: 3,
		type: 'error',
		label: '温馨提示：报表数据更新失败，昨日数据可能异常，请联系客服人员重新同步'
	}
];
const TestComponent: React.FC<RedTestComponentProps> = ({
	type = '1'
}) => {
	const [value, setValue] = React.useState(4);
	useEffect(() => {
		getValue();
	}, [type]);

	const getValue = async() => {
		const res = type == '2' ? await SelectStockSyncResult() : await SelectSaleProfitSyncResult();
		console.log(res, 'resresresresresres');
		if (res) {
			// 状态：0 - 待同步，2 - 同步中，3 - 同步失败，4 - 同步完成
			setValue(res?.status);
		}
	};

	return (
		<div 
			style={ {
				width: '100%',
				marginBottom: '16px'
			} }
		>
			{
				(() => {
					const currentItem = showList.find(item => item.value === value);
					return currentItem ? (
						<Alert message={ currentItem.label } type={ currentItem.type } showIcon />
					) : null;
				})()
			}
		</div>
	);
};

export default TestComponent;