import dayjs, { Dayjs } from "dayjs";
import { local } from "@/libs/db";

export enum DatePickerKey {
   'demo_test' = '__DATE_DEMO_TEST',
   'download_center' = '__DATE_DOWNLOAD_CENTER',
   'trade_search' = '__DATE_TRADE_SEARCH',
   'report_bhd_list' = '__DATE_REPORT_BHD_LIST',
   'report_kdd_log' = '__DATE_REPORT_KDD_LOG',
   'report_print_log' = '__DATE_REPORT_PRINT_LOG',
   'report_ship_log' = '__DATE_REPORT_SHIP_LOG',
   'aftersale_trade' = '__DATE_aftersale_trade',
   'aftersale_orderLog' = '__DATE_aftersale_orderLog',
   'report_account_check'= '__DATE_ACCOUNT_CHECK',
   'dzdList_search' = '__DATE_DZDLIST_SEARCH',
   'goods_tag_search' = '__DATE_GOODS_TAG_SEARCH',
   'label_account_check' = "__DATE_LABEL_ACCOUNT_CHECK",
   'afterSale_scan_record' = "__DATE_AFTERSALE_SCAN_RECORD",
   'logistics_warning' = '__DATE_LOGISTICS_WARNING',
   'pushed_order' = '__DATE_PUSHED_ORDER',
   'distribution_final_statement' = '__DATE_DISTRIBUTION_FINAL_STATEMENT',
   'distribution_order_detail' = '__DATE_DISTRIBUTION_ORDER_DETAIL',
   'exception_warn_modal' = '__DATE_EXCEPTION_WARN_MODAL',
   'express_intercept' = '__DATE_EXPRESS_INTERCEPT',
   'number_shared' = '__DATE_NUMBER_SHARED',
   'aftersale_scanRegister' = '__AFTERSALE_SCAN_REGISTER',
   'warehouse_waveList' = '__DATE_WAREHOUSE_WAVE_LIST',
   'warehouse_migrationLog' = '__DATE_WAREHOUSE_MIGRATION_LOG',
   'setting_monitoringRecord' = '__DATE_SETTING_MONITORING_RECORD',
}

export enum DateQuickChooseType {
	今天 = "today",
	昨天 = "yestoday",
	近一周 = "week",
	近一个月 = "month",
	记住天数 = "remember",
}

export const getCacheDateRange = (
	key: DatePickerKey
): [Dayjs, Dayjs] | null => {
	let rangeDate: [Dayjs, Dayjs] | null = null;
	try {
		if (key) {
			const rangeType = local.get(key);
			console.log(rangeType,'rangeTyperangeTyperangeType')
			if (rangeType === DateQuickChooseType.今天) {
				rangeDate = [dayjs().startOf("day"), dayjs().endOf("day")];
			} else if (rangeType === DateQuickChooseType.昨天) {
				rangeDate = [
					dayjs().subtract(1, "day").startOf("day"), // 昨天的开始时间
					dayjs().subtract(1, "day").endOf("day"), // 昨天的结束时间
				];
			} else if (rangeType === DateQuickChooseType.近一周) {
				rangeDate = [
					dayjs().subtract(6, "d").startOf("day"),
					dayjs().endOf("day"),
				];
			} else if (rangeType === DateQuickChooseType.近一个月) {
				rangeDate = [
					dayjs().subtract(1, "M").startOf("day"),
					dayjs().endOf("day"),
				];
			} else if (
				rangeType &&typeof rangeType === 'string'&&
				rangeType.includes(DateQuickChooseType.记住天数)
			) {
				const diffDays = parseInt(rangeType.split("_")?.[1], 10);
				if (!isNaN(diffDays) && diffDays < 93 && diffDays > 0) {
					rangeDate = [
						dayjs().subtract(diffDays, "d").startOf("day"),
						dayjs().endOf("day"),
					];
				}
			}
		}
	} catch (error) {
		console.error(error);
	}
	return rangeDate;
};
