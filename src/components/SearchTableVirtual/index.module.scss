@use "../../assets/styles/theme.scss"as *;

:global {
	.expansion-icon {
		display: none;
	}
}

.layoutClassName {
	padding-bottom  : 40px;
	background-color: #fff;
}

.topDom {
	position: relative;
}

.stickyTop {
	position  : sticky;
	top       : 91px;
	z-index   : 31;
	background: #fff;
}

.pageWrap {
	:global {
		.ant-pagination {
			border-top: 1px solid #eee;
			margin-top: 16px;
			text-align: right;
			position  : fixed;
			bottom    : 0;
			left      : 0;
			right     : 0;
			background: #fff;
			z-index   : 99;
			padding   : 12px;
		}
	}
}

.innerTable {
	padding: 16px 16px 0px;

	:global {
		.ant-table {
			.ant-table-container {
				border-radius: 0;
			}

			table {
				border-radius: 0;

				>thead>tr:first-child {
					th:first-child {
						border-radius: 0px;
					}

					th:last-child {
						border-radius: 0px;
					}
				}
			}
		}
	}
}

.formRowContainer {
	min-height: 4px;

	:global {
		.ant-divider-horizontal.ant-divider-with-text {
			margin    : 0;
			border-top: 5px;
			border-image: linear-gradient(90deg,
					rgba(0, 216, 247, 0) 0%,
					#00afed 100%) 2 2 2 2;
			position: absolute;
		}

		.ant-row {
			margin-left: 0px !important;
		}
	}

	&::after {
		content   : " ";
		display   : block;
		position  : absolute;
		height    : 8px;
		width     : 100%;
		bottom    : 0;
		left      : 0;
		background: #f0f2f5;
	}
}

.formRow {
	position: relative;
	padding : 16px;
}

.expandContextNoGap,
.expandContext {
	position       : relative;
	display        : flex;
	justify-content: flex-start;
	padding        : 16px 16px 24px;
	overflow-x     : auto;

	&::after {
		content   : " ";
		display   : block;
		position  : absolute;
		height    : 8px;
		width     : 100%;
		bottom    : 0;
		left      : 0;
		background: #f0f2f5;
	}
}

.expandContextNoGap {
	padding-bottom: 16px;

	&::after {
		display: none;
	}
}

.fullWindowScrollStyle {
	:global {
		.ant-table-body {
			max-height: unset !important;
			overflow-y: hidden !important;
		}

		.ant-table-wrapper .ant-table-body {
			max-height: unset !important;
			overflow-y: hidden !important;
		}

		.ant-table-cell-fix-right {
			right: 0 !important;
		}

		.ant-table-sticky-holder {
			z-index: 97;
		}
	}
}

.fullWindowFixedDiv {
	position        : fixed;
	width           : 100%;
	top             : 48px;
	z-index         : 98;
	padding-top     : 8px;
	background-color: #f0f2f5;
	border-bottom   : 1px solid #f0f2f5;

	.formRow {
		padding-bottom: 8px;

		&::after {
			content: none;
		}
	}
}

.templateToggleWrapper {
	position       : absolute;
	bottom         : -10px;
	z-index        : 35;
	font-size      : 12px;
	display        : flex;
	justify-content: center;
	align-items    : center;
	width          : 100%;

	.border {
		width     : 50%;
		height    : 1px;
		background: linear-gradient(to right, #fff, #fd8204);
	}

	.borderRight {
		background: linear-gradient(to left, #fff, #fd8204);
	}

	.expandIcon {
		width          : 50px;
		height         : 22px;
		font-size      : 12px;
		display        : flex;
		align-items    : center;
		justify-content: center;
		// color: $color-primary;
		cursor         : pointer;
	}
}

:global {
	.react-resizable {
		position: relative;
		cursor  : default;
	}

	.react-resizable-handle {
		position         : absolute;
		width            : 0;
		height           : 100%;
		background-repeat: no-repeat;
		background-origin: content-box;
		box-sizing       : border-box;
		bottom           : 0;
		padding          : 0 4px;
		right            : 0;
		cursor           : col-resize;
		border-right     : 2px solid #dadada;
	}
}

:global {
	.search-table-con {
		.high-light {
			color: #e63e35;
		}
	}
}

.table-container {
	display: flex;
	padding: 0 16px;
}

.pagenation-layer {
	z-index: 29;
}

.table-header-sticky {
	position: sticky;
	top     : 148px;
	z-index : 39;
}

.highlighted-row {
	background-color: #ffe7ba !important;

	&>td {
		background-color: #ffe7ba !important;
	}
}

.expandRowUnfold {
	:global {
		.ant-table-cell {
			background-color: #FFE7BA;
			padding         : 0 8px 8px 8px !important;
		}
	}

	&:hover>td {
		background-color: #FFE7BA !important;
	}
}