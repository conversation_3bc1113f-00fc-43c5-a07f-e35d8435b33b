/*
 * @Author: try <EMAIL>
 * @Date: 2024-11-20 17:00:12
 * @Description: 
 */
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import React from 'react';

export function SortableItem(props) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging
	} = useSortable({ id: props.id });
	const { children, ...rest } = props;

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		// backgroundColor: isDragging ? 'red' : 'lightgreen',
		zIndex: isDragging ? 2 : 1,
		margin: '4px 0',
		height: '32px',
		lineHeight: '32px'
	};
	console.log('transition', attributes, listeners, rest);
	return (
		<tr ref={ setNodeRef } style={ style } >
			{React.Children.map(children, (child) => {
				if (child.dataIndex === '"skuPic"') {
					return React.cloneElement(child, {
						additionalProps: { ...listeners, 'data-cypress': 'draggable-handle' },
					});
				}
				return child;
			})}
		</tr>
	);
}