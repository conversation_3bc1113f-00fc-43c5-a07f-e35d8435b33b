import { useCallback, useState } from "react";

const toNumber = (value: unknown): number => {
	if (typeof value === "number") {
		return value; // If already a number, return it directly
	}
	if (typeof value === "string") {
		const parsedValue = parseFloat(value);
		return isNaN(parsedValue) ? 0 : parsedValue; // Return 0 if parsing fails
	}
	return 0; // Default for other types
};

export enum Sort {
	ASC = "ascend",
	DESC = "descend",
}

export enum SortColumn {
	sysSkuName = "sysSkuName",
	skuOuterId = "skuOuterId",
	sysSkuAlias = "sysSkuAlias",
	sysColor = "sysColor",
	sysSize = "sysSize",
	price = "price",
	tagPrice = "tagPrice",
	costPrice = "costPrice",
	weight = "weight",
	itemNo = "itemNo",
	barCode = "barCode",
	supplierId = "supplierId",
	warehouseSlotName = "warehouseSlotName",
}

// Define size order including compatibility with numeric sizes
const sizeOrder: string[] = ["xs", "s", "m", "l", "xl", "xxl", "xxxl", "xxxxl"];

// Get size rank
const getSizeRank = (size: string): number => {
	const lowerCaseSize = size.toLowerCase().trim();

	const match = lowerCaseSize.match(/^(\d+)(xl)$/);
	if (match) {
		const num = parseInt(match[1], 10);
		return sizeOrder.length + num; // Numbers rank after letter sizes
	}

	const index = sizeOrder.indexOf(lowerCaseSize);
	return index === -1 ? sizeOrder.length + 100 : index;
};

// Type guard to check if dataIndex is a key of SortColumn
const isSortColumnKey = (key: string): key is keyof typeof SortColumn => {
	return Object.values(SortColumn).includes(key as SortColumn);
};

/**
 * Sorts an array of data based on the specified column and order.
 *
 * @template T - The type of the data being sorted.
 * @param data - The array of data to sort.
 * @param dataIndex - The key of the data to sort by.
 * @param order - The sorting order, either ascending or descending.
 * @param sortCol - The collection of columns to determine numeric columns.
 * @returns A new array of the sorted data.
 */
const sortData = <T extends Record<string, any>>(
	data: T[],
	dataIndex: keyof T,
	order: Sort,
	sortCol: typeof SortColumn
): T[] => {
	// Check if the column is a numeric column
	const isNumericColumn = [
		sortCol.costPrice,
		sortCol.price,
		sortCol.tagPrice,
		sortCol.weight
	].includes(dataIndex as SortColumn);

	// Check if the column is a size column
	const isSizeColumn = dataIndex === sortCol.sysSize;

	return [...data].sort((a, b) => {
		let aValue = a[dataIndex] || ("" as any);
		let bValue = b[dataIndex] || ("" as any);

		let compareResult = 0;
		if (isNumericColumn) {
			// Treat numeric values as strings for ASCII comparison
			const strA = String(aValue).trim();
			const strB = String(bValue).trim();
			compareResult = asciiCompare(strA, strB);
		} else if (isSizeColumn) {
			compareResult = getSizeRank(aValue as string) - getSizeRank(bValue as string);
		} else {
			// ASCII comparison for other strings
			const aStr = String(aValue).trim();
			const bStr = String(bValue).trim();
			compareResult = asciiCompare(aStr, bStr);
		}

		return order === Sort.ASC ? compareResult : -compareResult;
	});
};

/**
 * Compares two values (strings or numbers) using ASCII values.
 *
 * @param a - The first value to compare.
 * @param b - The second value to compare.
 * @returns A negative number if `a` < `b`, a positive number if `a` > `b`, or 0 if they are equal.
 */
const asciiCompare = (a, b) => {
	// Convert both inputs to strings
	const strA = String(a);
	const strB = String(b);

	// Check if a string contains numeric characters
	const containsNumeric = (str) => /\d/.test(str);

	// Extract the numeric part and the non-numeric part of a string
	const extractParts = (str) => {
		const numericPart = str.replace(/\D/g, '');
		const nonNumericPart = str.replace(/\d/g, '');
		return { numericPart, nonNumericPart };
	};

	// Compare two strings numerically
	const compareNumerically = (strA, strB) => {
		if (isBigInt(strA) || isBigInt(strB)) {
			return BigInt(strA) > BigInt(strB) ? 1 : BigInt(strA) < BigInt(strB) ? -1 : 0;
		} else {
			return Number(strA) - Number(strB);
		}
	};

	// Check if a string represents a BigInt
	const isBigInt = (str) => {
		const trimmedStr = str.trim();
		return /^(\d+)n$/i.test(trimmedStr) || BigInt(trimmedStr).toString() === trimmedStr;
	};

	// If both strings contain numeric characters, compare them numerically
	if (containsNumeric(strA) && containsNumeric(strB)) {
		const partsA = extractParts(strA);
		const partsB = extractParts(strB);

		// Compare non-numeric parts first
		const nonNumericCompare = partsA.nonNumericPart.localeCompare(partsB.nonNumericPart);
		if (nonNumericCompare !== 0) {
			return nonNumericCompare;
		}

		// If non-numeric parts are equal, compare numeric parts
		return compareNumerically(partsA.numericPart, partsB.numericPart);
	}

	// If one string contains numeric characters and the other does not, or if one is BigInt and the other is not
	if (containsNumeric(strA) || containsNumeric(strB)) {
		if (containsNumeric(strA) && !containsNumeric(strB)) {
			return 1;
		} else if (!containsNumeric(strA) && containsNumeric(strB)) {
			return -1;
		}
	}

	// If neither string is numeric, compare them as strings
	return strA.localeCompare(strB);
};



interface SortFlag {
	dataIndex: keyof typeof SortColumn;
	order: Sort;
}

// 定义获取当前数据的函数类型
type GetCurrentDataFn<T> = () => T[];

/**
 * 用于管理数据列表排序状态的自定义钩子。
 *
 * @template T - 数据源中数据项的类型，必须包含 key 属性。
 *
 * @param {SortFlag} [initialSortFlag] - 初始排序配置，包括
 *        要排序的数据索引和排序顺序（升序或降序）。
 *        默认为按 `null` 索引升序排序。
 *
 * @param {T[]} dataSource - 要排序的数据项数组。
 *
 * @param {React.Dispatch<React.SetStateAction<T[]>>} setDataSource - 一个状态设置函数，
 *        用于更新排序后的数据源。
 *
 * @param {GetCurrentDataFn<T>} getCurrentData - 获取当前实际数据的函数，
 *        用于获取包含最新字段值的数据（包括未在表单中注册的字段）。
 *
 * @returns {Object} 包含以下内容的对象：
 * - `sortFlag`: 当前的排序配置，包括数据索引
 *   和排序顺序。
 * - `handleSort`: 一个函数，根据指定的数据索引触发排序。
 *
 * @example
 * const getCurrentData = () => {
 *   // 返回包含最新数据的数组，合并 dataSource 和表单数据
 *   return dataSource.map(item => ({
 *     ...item,
 *     ...getFieldValuesForItem(item.key)
 *   }));
 * };
 * const { sortFlag, handleSort } = useSort<MyDataType>(initialSortFlag, dataSource, setDataSource, getCurrentData);
 *
 * // 在组件中的用法
 * <Button onClick={() => handleSort(SortColumn.price)}>按价格排序</Button>
 */
export const useSort = <T extends { key: string; [key: string]: any }>(
	initialSortFlag: SortFlag = {
		dataIndex: null,
		order: Sort.ASC,
	},
	dataSource: T[],
	setDataSource: React.Dispatch<React.SetStateAction<T[]>>,
	getCurrentData: GetCurrentDataFn<T>
) => {
	const [sortFlag, setSortFlag] = useState<SortFlag>(initialSortFlag);

	// 排序处理逻辑
	const handleSort = useCallback(
		(dataIndex: keyof typeof SortColumn) => {
			if (!isSortColumnKey(dataIndex as string)) {
				return; // 如果 dataIndex 无效，提前返回
			}

			// 使用 getCurrentData 获取当前实际数据，而不是依赖 form.getFieldsValue()
			const currentData = getCurrentData();
			console.log(currentData, "---------------currentData");

			setSortFlag((prevFlag) => {
				const newOrder = prevFlag.dataIndex === dataIndex
					&& prevFlag.order === Sort.ASC
					? Sort.DESC
					: Sort.ASC;
				const updatedSortFlag: SortFlag = {
					dataIndex,
					order: newOrder,
				};

				// 根据 dataIndex 和新排序顺序对数据进行排序
				const sortedData = sortData(
					currentData,
					dataIndex as keyof T,
					newOrder,
					SortColumn
				);

				console.log("排序前数据:", currentData);
				console.log("排序后数据:", sortedData);

				// 从排序后的数据中提取原始数据源项目（保持排序顺序）
				const sortedDataSource = sortedData.map((sortedItem) => {
					// 根据 key 找到原始 dataSource 中对应的项目
					const originalItem = dataSource.find((item) => String((item as any).sysSkuId) === sortedItem.key);
					return originalItem;
				}).filter(Boolean) as T[]; // 过滤掉可能的 undefined 值

				// 可选：如果需要，分配新的排序索引
				sortedDataSource.forEach((item, idx) => {
					(item as any).sort = idx + 1; // 设置排序索引
				});

				setDataSource(sortedDataSource);
				return updatedSortFlag;
			});
		},
		[dataSource, setDataSource, getCurrentData]
	);

	return { sortFlag, handleSort };
};
