import React, { FC, memo, useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { Pagination, Checkbox } from 'antd';
import { useTablePipeline, features } from 'ali-react-table-threshold';
import type { PaginationProps, TableProps } from 'antd';
import { AntdBaseTable } from '../antd.tb';
import { Nav } from '../component/paginationNav';

interface IBasicTable<T = Record<string, any>> extends TableProps<T> {
	pagination: PaginationProps & {
		align?: 'start' | 'center' | 'end';
		bottom?:string
	};
	loading?: boolean;
	rowKey: string | ((record: T) => string);
	sorts?: string[] | '-1';
	defaultSort: string;
	components?: {
		EmptyContent?: React.ComponentType;
	};
	sticky?: {
		stickyTop?: number;
		stickyBottom?: number;
	};
	paddingBottom?: number;
	AUTO_VIRTUAL_THRESHOLD?: number;
}

const BasicTable: FC<IBasicTable> = memo(({
	dataSource = [],
	columns,
	rowKey,
	pagination,
	loading = false,
	onChange,
	sticky,
	summary,
	sorts = [],
	defaultSort,
	components,
	paddingBottom,
	rowSelection,
	AUTO_VIRTUAL_THRESHOLD
}) => {
	const tableRef = useRef(null);
	const [isSortActive, setIsSortActive] = useState(true); // 当前排序激活
	const [yScrollbar, setYScrollbar] = useState(false); // 出现y轴滚动条
	console.log('AUTO_VIRTUAL_THRESHOLD', AUTO_VIRTUAL_THRESHOLD);
	const processedDataSource = useMemo(() => dataSource.map((item) => ({
		...item,
		_uniqueKey: typeof rowKey === 'function' ? rowKey(item) : item[rowKey as string],
	})), [dataSource, rowKey]);

	const pipeline = useTablePipeline({ components: { Checkbox } })
		.input({ dataSource: processedDataSource, columns })
		.primaryKey(typeof rowKey === 'function' ? '_uniqueKey' : rowKey);

	const enhanceColumns = useCallback((cols: any[]) => {
		return cols.map(column => ({
			...column,
			code: column.key || column.dataIndex,
			name: column.title,
			minWidth: column.width ?? 200,
			features: {
				sortable: sorts === '-1' ? true : sorts.includes(column.key ?? column.dataIndex),
			},
			lock: column.fixed === 'left' || column.fixed === 'right',
			children: column.children ? enhanceColumns(column.children) : undefined,
		}));
	}, [sorts]);

	pipeline.mapColumns(enhanceColumns);

	pipeline.use(
		features.sort({
			mode: 'single',
			orders: defaultSort ? ['asc', 'desc', ''] : ['desc', 'asc', ''],
			defaultSorts: [{ code: defaultSort, order: 'desc' }],
			highlightColumnWhenActive: false,
			onChangeSorts: (nextSort) => {
				const order = nextSort[0].order;
				setIsSortActive(Boolean(order));
				handleAllChange(nextSort[0], 'sort');
				setTimeout(() => {
					document.querySelector('path[fill="#23A3FF"]')?.setAttribute('fill', '#FF5733');
				}, 0);
			},
		})
	);

	if (rowSelection) {
		pipeline.use(
			features.multiSelect({
				value: rowSelection.selectedRowKeys,
				checkboxPlacement: "start",
				clickArea: "cell",
				checkboxColumn: { lock: rowSelection?.fixed ?? false },
				onChange(nextValue, key, keys, action) {
					const rows = processedDataSource.filter((s: any) => nextValue.includes(s[rowKey]));
					rowSelection.onChange(nextValue, rows);
					if (action === 'check-all' || action === 'uncheck-all') {
						if (rowSelection?.onSelectAll) {
							rowSelection.onSelectAll(nextValue, processedDataSource);
						}
					} else if (rowSelection.onSelect) {
						const row = processedDataSource?.filter(i => nextValue.includes(i[rowSelection?.selectKey ?? rowKey]));
						rowSelection.onSelect(row, nextValue, row);
					}
				},
			})
		);
	}


	const handleAllChange = useCallback((arg: any, action: 'pagination' | 'sort') => {
		if (action === 'pagination') {
			const [page, pageSize] = arg;
			const newPagination = { ...pagination, current: page, pageSize };
			onChange?.(newPagination, undefined, undefined, { action: 'paginate', currentDataSource: dataSource });
			pagination?.onChange?.(page, pageSize);
		}

		if (action === 'sort') {
			const col = pipeline.getColumns().find(i => i.code === arg.code);
			const newSort = {
				column: col,
				columnKey: arg.code,
				field: arg.code,
				order: arg.order ? `${arg.order}end` : ''
			};
			onChange?.(undefined, undefined, newSort, { action: 'sort', currentDataSource: dataSource });
		}
	}, [pagination, onChange, dataSource, pipeline]);


	// Effect to observe Y scrollbar visibility based on scrollHeight
	useEffect(() => {
		// Create a MutationObserver to detect changes in the document's html element
		const observer = new MutationObserver(() => {
			// Get the current scrollHeight and clientHeight of the html element
			const hasVerticalScroll = document.documentElement.scrollHeight > document.documentElement.clientHeight;
			setYScrollbar(hasVerticalScroll);
		});

		// Observe changes to the document's html element
		observer.observe(document.documentElement, {
			childList: true, // Watch for child additions or removals
			subtree: true, // Watch for changes in any descendants
			attributes: true, // Watch for attribute changes (style, height, etc.)
			attributeFilter: ['style'] // Watch specifically for changes in the style attribute
		});

		// Initial check in case content is already loaded
		const hasVerticalScroll = document.documentElement.scrollHeight > document.documentElement.clientHeight;
		setYScrollbar(hasVerticalScroll);

		setTimeout(() => {
			document.querySelector('path[fill="#23A3FF"]')?.setAttribute('fill', '#FF5733');
		}, 0);

		// Cleanup function to disconnect the observer when the component is unmounted
		return () => observer.disconnect();
	}, []);

	// Calculate bottom padding and sticky bottom
	const paginationExists = pagination && dataSource.length > 0;
	const computedPaddingBottom = paginationExists && yScrollbar ? paddingBottom : 0;
	return (
		<>
			<AntdBaseTable
				{ ...pipeline.getProps() }
				dataSource={ processedDataSource }
				bordered
				isLoading={ loading }
				isStickyHeader={ Boolean(sticky) }
				stickyTop={ sticky?.stickyTop ?? 0 }
				footerDataSource={ summary ? typeof summary === 'function' ? (summary(dataSource) as any[]) : summary : [] }
				paginationStyle={ {
					bottom: computedPaddingBottom,
					originBottom: 0,
				} }
				components={ components }
				scrollBottom={ paginationExists ? 44 : 0 }
				ref={ tableRef }
				isStickyFooter={ false }
			/>
			{pagination && dataSource.length > 0 && (
				<Nav align={ pagination.align } style={ { bottom: pagination.bottom ?? 0 } }>
					<Pagination
						size="small"
						{ ...pagination }
						showTotal={ (total) => `总计 ${total} 项` }
						onChange={ (page, pageSize) => handleAllChange([page, pageSize], 'pagination') }
					/>
				</Nav>
			)}
		</>
	);
});

export default BasicTable;
