import React, { useMemo, useState, useEffect, useRef, useCallback } from "react";
import { Checkbox } from "antd";
import { ColumnType } from "antd/es/table";
import { Resizable } from 'react-resizable';
import cs from 'classnames';
import { useTablePipeline, features } from 'ali-react-table-threshold';
import { debounce, sortBy } from "lodash";
import { AntdBaseTable as TableNode } from './antd.tb';
import { AntdBaseTable as TableNodeSort } from './antd.tb.sort';
import s from './index.module.scss';
import { BaseTableProps as BaseGroupTableProps, ColSetPlaceEnum } from '../SearchTable/BaseCheckGroupTable';
import ColSortSetting from "../SearchTable/ColSortSetting";
import ColSortSettingDrawer from "../SearchTable/ColSortSettingDrawer";
import { ColFixItem, ColSortItem } from "../SearchTable/ColSortSetting/ColSortSettingTable";

export type BaseColumnsType<T> = ColumnType<T> | {
	(dataSource: readonly T[]): ColumnType<T> | ColumnType<T>[]
}

export type BaseColumnsTypes<T> = BaseColumnsType<T>[]
interface TableNodeProps {
	expandWidth?: number; // 根据实际需要调整类型
	paginationStyle?: React.CSSProperties; // 假设 paginationStyle 是 CSS 属性
	isStickyHeader?: boolean;
	reset?: {
		loading?: boolean;
		// 添加其他 reset 属性，如果有的话
	};
	scroll?: { [key: string]: any }; // 根据实际结构定义
	pipeline: {
		getProps: () => { [key: string]: any }; // 根据实际结构定义
	};
	// 其他 props 的类型定义
}

/**
 *@property {boolean} [colRewidth] - 默认值为false或undefine，针对于表格列宽度拖拽是否开启，传入此属性则为不开启
 *@property {boolean} [isStickyHeader] - 默认为false或者undefine，针对于表格表头吸顶，传值则为开启表头吸顶
 *@property {boolean} [enableRowClick] - 默认为false或者undefine，行点击选中
 */
export interface BaseTableProps<T> extends BaseGroupTableProps<T> {
	colRewidth?: boolean
	isStickyHeader?: boolean
	stickyTop?: number
	noGap?:boolean
	enableRowClick?: boolean
}
const isNormalTitle = (item) => item.noDrag || !item.dataIndex || !!item.colFixed;
const ResizableTitle = (props) => {
	const {
		onResize, onResizeStop, onResizeStart, width, minWidth = 30, maxWidth = 300, ...restProps
	} = props;
	return (
		props.drag && (props.title || props.children) ? (
			<Resizable
				width={ width }
				height={ 0 }
				onResize={ onResize }
				onResizeStop={ onResizeStop }
				onResizeStart={ onResizeStart }
				minConstraints={ [minWidth, 0] }
				maxConstraints={ [maxWidth, 0] }
				handle={ <span className="react-resizable-handle" /> }
			>
				<th { ...restProps } />
			</Resizable>
		) : <th { ...props } />
	);
};

function BaseTable<T extends object = any>(props: BaseTableProps<T>, ref) {
	const tableExpandRef = useRef(null); // 获取表格左侧的组件实例
	const initialTopRef = useRef(0);
	const [expandWidth, setExpandWidth] = useState(0); // 存储宽度的 state
	const [widthMap, setWidthMap] = useState({});
	const [colSortList, setColSortList] = useState<
		ColSortItem[] | ColFixItem[]
	>([]);
	const {
		columns,
		expandContext,
		expandContextStyle,
		expandLayoutStyle,
		tableExpandContext,
		pagination,
		hasVt = true,
		rowSelection,
		noPadding,
		tableExtra,
		colRewidth,
		isStickyHeader,
		paginationStyle = {},
		stickyTop,
		footerDataSource,
		hideLayoutClassName = false,
		expandable = {},
		enableRowClick = false,
		...reset
	}: any = props;
	console.log('propsprops', props);
	const { headerColSet, headerColSetId } = props;
	const ColSetInPlace = headerColSet?.inPlace ?? ColSetPlaceEnum.默认;
	const { headerResizeKey, headerSortKey } = useMemo(() => {
		let headerResizeKey = "";
		let headerSortKey = "";
		if (headerColSetId) {
			headerResizeKey = "RESIZE_" + headerColSetId;
			headerSortKey = "SORT_" + headerColSetId;
		} else if (headerColSet) {
			const { resizeId, sortId } = headerColSet || {};
			if (resizeId) {
				headerResizeKey = "RESIZE_" + resizeId;
			}
			if (sortId) {
				headerSortKey = "SORT_" + sortId;
			}
		}
		return { headerResizeKey, headerSortKey };
	}, [headerColSet, headerColSetId]);

	const defaultColSortList: any = useMemo(() => {
		return columns
			?.filter((i) => !isNormalTitle(i))
			.map((i: any, index) => ({
				collection: i?.sortSet?.collection ?? 0,
				index,
				ischecked: i?.sortSet?.ischecked ?? true,
				isedit: i?.sortSet?.isedit ?? true,
				name: i?.sortSet?.name ?? i.title,
				key: i.dataIndex,
			}));
	}, [columns]);

	const defaultColFixList: any = useMemo(() => {
		return columns
			?.filter((i: any) => i.colFixed && i.dataIndex)
			?.map((i: any) => ({
				colFixed: i.colFixed,
				isfixed: i?.sortSet?.isfixed ?? true,
				name: i?.sortSet?.name ?? i.title,
				key: i.dataIndex,
			}));
	}, [columns]);

	const defaultWidthMap = useMemo(() => {
		let map = {};
		columns
			?.filter((i: any) => !isNormalTitle(i))
			.forEach((i: any) => {
				if (map[i.dataIndex]) {
					console.log(`注意：${i.dataIndex}重复`);
				}
				map[i.dataIndex] = i.width;
			});
		return map;
	}, [columns]);

	useEffect(() => {
		let initMap;
		if (headerColSet?.initMap) {
			initMap = headerColSet.initMap;
		} else if (headerResizeKey) {
			try {
				let cache = localStorage.getItem(headerResizeKey);
				if (cache) {
					initMap = JSON.parse(cache);
				}
			} catch (error) {
				console.log("error: ", error);
			}
		}
		if (!initMap && defaultWidthMap) {
			initMap = defaultWidthMap;
		}
		setWidthMap(initMap);
	}, [headerResizeKey, defaultWidthMap, headerColSet?.initMap]);

	useEffect(() => {
		let initList: ColSortItem[] = [];
		if (headerColSet?.initList) {
			initList = headerColSet.initList; // 接口返回的列配置
		} else if (headerSortKey) {
			try {
				let cache = localStorage.getItem(headerSortKey); // 缓存的列配置排序
				if (cache) {
					initList = JSON.parse(cache);
				}
			} catch (error) {
				console.log("error: ", error);
			}
		}
		const initListMap = {};
		initList?.forEach((i) => {
			initListMap[i.key] = i;
		});

		// console.log('%c [ defaultColSortList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', defaultColSortList, defaultColFixList);

		let allDefaultColList = [
			...defaultColSortList,
			...defaultColFixList
		]?.map((i) => {
			if (initListMap[i.key]) {
				return {
					...i,
					...initListMap[i.key],
					name: i.name, // 保存和缓存的名称可能更改了，这里使用最新的
				};
			}
			return i;
		});

		let newList = sortBy(allDefaultColList, (item) => item.index);
		setColSortList(newList);
	}, [
		headerSortKey,
		defaultColSortList,
		defaultColFixList,
		headerColSet?.initList
	]);

	useEffect(() => {
		if (expandable?.expandedRowKeys) {
			setOpenKeys(new Set(expandable.expandedRowKeys));
		}
	}, [expandable]);

	const getColumns = useMemo(() => {
		const getMap = (colSortList: ColSortItem[] | ColFixItem[]) => {
			const rankMap = {};
			const hideMap = {};
			const fixMap = {};
			colSortList?.forEach((i) => {
				if (i.colFixed) {
					fixMap[i.key] = i.isfixed ? i.colFixed : false;
				} else {
					if (!i.ischecked) hideMap[i.key] = true;
					rankMap[i.key] = i.index;
				}
			});
			return { rankMap, hideMap, fixMap };
		};
		const { rankMap, hideMap, fixMap } = getMap(colSortList);
		return columns
			?.reduce((prev, item) => {
				if (typeof item === "function") {
					const _c = item(reset.dataSource);
					if (_c instanceof Array) {
						return prev.concat(..._c);
					}
					return prev.concat(_c);
				}
				return prev.concat({
					align: "left",
					...item,
					fixed: fixMap[item?.dataIndex] ?? item.fixed,
					lock: fixMap[item?.dataIndex] ?? item.fixed,
				});
			}, [])
			.filter((i) => !hideMap[i.dataIndex])
			.sort((a, b) => (headerSortKey ? rankMap[a.dataIndex] - rankMap[b.dataIndex] : 1));
	}, [
		colSortList,
		columns,
		headerColSet,
		widthMap,
		headerResizeKey,
		reset.dataSource,
		headerSortKey
	]);

	const expandStyle = useMemo(() => {
		const style = {
			contentClassName: cs(props.noGap ? s.expandContextNoGap : s.expandContext),
			contentStyle: {},
			layoutClassName: "",
			layoutStyle: {},
		};
		if (typeof expandContextStyle === "string") {
			style.contentClassName = cs(props.noGap ? s.expandContextNoGap : s.expandContext, expandContextStyle);
		} else {
			style.contentStyle = { ...expandContextStyle };
		}
		if (typeof expandLayoutStyle === "string") {
			style.layoutClassName = cs(expandLayoutStyle);
		} else {
			style.layoutStyle = { ...expandLayoutStyle };
		}

		return style;
	}, [expandContextStyle, expandLayoutStyle]);

	const plusComponents: any = useMemo(() => {
		const pc = {
			...(props?.components || {}),
		};
		if (headerResizeKey) {
			if (!pc.header) {
				pc.header = {};
			}
			pc.header.cell = ResizableTitle;
		}
		return pc;
	}, [hasVt, props?.components, headerResizeKey]);

	const expandablePlus: any = expandable;
	if (expandablePlus?.colFixed == "right") {
		let len = getColumns.length;
		if (rowSelection) {
			len += 1;
		}
		expandablePlus.expandIconColumnIndex = len;
		expandablePlus.lock = "right";
	}

	/** 复选框功能 */
	const pipeline = useTablePipeline({ components: { Checkbox } })
		.input({
			dataSource: props.dataSource || [],
			columns: getColumns,
		} as any)
		.primaryKey(rowSelection?.selectKey ?? "rowId");
	if (rowSelection) {
		pipeline.use(
			features.multiSelect({
				value: rowSelection.selectedRowKeys,
				checkboxPlacement: "start",
				clickArea: enableRowClick ? "row" : "cell", // 点击区域
				checkboxColumn: {
					lock: rowSelection?.fixed ?? false,
					width: rowSelection?.columnWidth || 50,
				},
				onChange(nextValue, key, keys, action) {
					const rows = props.dataSource.filter((s: any) => nextValue.includes(s.rowId));
					rowSelection.onChange(nextValue, rows);
					if (action === 'check-all' || action === 'uncheck-all') {
						if (rowSelection?.onSelectAll) {
							rowSelection.onSelectAll(nextValue, props.dataSource);
						}
					} else if (rowSelection.onSelect) {
						const row = props.dataSource?.filter(i => nextValue.includes(i[rowSelection?.selectKey ?? 'rowId']));
						rowSelection.onSelect(row, nextValue, row);
					}
				},
			})
		);
	}
	/** 自定义宽度 */
	const handleResizeStop = debounce((widthMap) => {
		headerColSet?.onResizeChange?.(widthMap);
		localStorage.setItem(headerResizeKey, JSON.stringify(widthMap));
	}, 300);
	const columnSizes = getColumns.map(
		(s) => {
			// 如果自定义宽度小于设置的最小宽度，直接使用最小宽度
			if (widthMap[s.dataIndex] && s.minWidth && widthMap[s.dataIndex] < s.minWidth) {
				return s.minWidth;
			} else {
				return widthMap[s.dataIndex] || s.width || s.minWidth;
			}
		}
	);
	const sizes = rowSelection ? [rowSelection?.columnWidth || 50, ...columnSizes] : columnSizes;
	if (!colRewidth) {
		pipeline.use(
			features.columnResize({
				fallbackSize: 120,
				handleBackground: "#ddd",
				handleHoverBackground: "#aaa",
				handleActiveBackground: "#fd8204",
				sizes,
				onChangeSizes(e) {
					const newWidthMap = { ...widthMap };
					(rowSelection ? [{}, ...getColumns] : [...getColumns]).forEach(
						(col, index) => {
							const newW = e[index];
							if (col.dataIndex) {
								if (!col.minWidth || newW > col.minWidth) {
									col.width = newW;
									newWidthMap[col.dataIndex] = newW;
								} else {
									col.width = col.minWidth;
									newWidthMap[col.dataIndex] = col.minWidth;
								}
							}
						}
					);
					setWidthMap((pre) => {
						return {
							...pre,
							...newWidthMap,
						};
					});
					handleResizeStop(newWidthMap);
				},
			})
		);
	}
	/** 展开 */
	const [openKeys, setOpenKeys]: any = useState(new Set());
	const onExpand = (record, e) => {
		if (openKeys.has(record.rowId)) {
			openKeys.delete(record.rowId);
			setOpenKeys(() => openKeys);
		} else {
			openKeys.add(record.rowId);
			setOpenKeys(() => openKeys);
		}
	};

	/** 操作栏 */
	pipeline
		.use(
			features.rowDetail({
				stopClickEventPropagation: false,
				clickArea: "icon",
				openKeys: Array.from(openKeys),
				hasDetail: () => true,
				renderDetail(row) {
					return (
						<div style={ { margin: "16px -8px 0" } }>
							{expandablePlus?.expandedRowRender?.(row)}
						</div>
					);
				},
			})
		)
		.mapColumns((columns: any) => {
			const optRender = columns[columns.length - 1].render;
			const unfoldRender = expandablePlus?.expandIcon;

			columns.forEach((ele, index) => {
				const optRender = columns[index].render;
				columns[index].render = (_, record, index) => {
					return (
						<>{optRender?.(record?.[ele.dataIndex], record, index)}</>
					);
				};
			});

			const getLastRowValue = (record) => {
				const lastRow = columns[columns.length - 1];
				const columnRowValue = lastRow?.dataIndex ? record?.[lastRow?.dataIndex] : '';
				return columnRowValue;
			};

			if (unfoldRender) {
				// 操作列重写

				columns[columns.length - 1].render = (text, record, index) => {
					return (
						<div
							style={ {
								display: "flex",
								justifyContent: "space-around",
								paddingLeft: "16px",
							} }
						>
							<div>{optRender?.(getLastRowValue(record), record, index)}</div>
							<div>
								{unfoldRender?.({
									expanded: openKeys.has(record.rowId),
									onExpand,
									record,
								})}
							</div>
						</div>
					);
				};
			} else {
				columns[columns.length - 1].render = (text, record, index) => {
					return <>{optRender?.(getLastRowValue(record), record, index)}</>;
				};
			}

			return columns;
		});

	/** 保存列配置 */
	const onSaveSort = (type, newDataSource) => {
		let newSortList: any = [...newDataSource];
		newSortList = newSortList?.filter(item => typeof item.name === 'string'); // 排除掉售后登记这样多个勾选项的
		if (type == "reset") {
			newSortList = [...defaultColSortList, ...defaultColFixList];
			newSortList = newSortList?.filter(item => typeof item.name === 'string');
			setWidthMap({ ...defaultWidthMap });
			headerColSet?.onReset?.(defaultWidthMap, newSortList);
		} else {
			headerColSet?.onSortChange?.(newSortList);
		}
		setColSortList(newSortList);
		localStorage.setItem(headerSortKey, JSON.stringify(newSortList));
	};

	const colSortSettingNode = (
		headerColSet?.useDrawer ? <ColSortSettingDrawer colSortList={ colSortList } onSaveSort={ onSaveSort } /> : <ColSortSetting colSortList={ colSortList } onSaveSort={ onSaveSort } />
	);

	/**
	 *
	 *  当存在表格左侧扩展览时获取宽度传入table用以设置maxWidth
	 */
	const updateExpandWidth = () => {
		if (tableExpandRef.current) {
			const width = tableExpandRef.current.getBoundingClientRect().width;
			setExpandWidth(width);
		}
	};
	useEffect(() => {
		const channel = new MessageChannel();
		channel.port1.onmessage = updateExpandWidth;
		channel.port2.postMessage(null);
		window.addEventListener("resize", updateExpandWidth);
		return () => {
			channel.port1.close();
			window.removeEventListener("resize", updateExpandWidth);
		};
	}, []);

	/**
	 * 动态修改左侧高度
	 */
	useEffect(() => {
		if (tableExpandRef.current) {
			let animationFrameId = null;
			let hasScrolledOrResized = false; // 标志位：是否发生了滚动或调整窗口大小

			// 设置平滑动画效果的函数
			const setSmoothTransition = (element) => {
				element.style.transition = 'max-height 0.3s ease-in-out'; // 设置 max-height 动画过渡
			};

			const updateVisibleHeight = () => {
				if (!hasScrolledOrResized) {
					return; // 如果未发生滚动或调整窗口大小，不执行更新
				}

				if (tableExpandRef.current) {
					const rect = tableExpandRef.current.getBoundingClientRect();

					// 记录初始的 top 值
					if (initialTopRef.current === 0) {
						initialTopRef.current = rect.top;
					}

					const { top: currentTop, bottom: currentBottom } = rect;

					// 计算可视高度并取整
					const visibleHeight = Math.floor(Math.max(0, Math.min(currentBottom, window.innerHeight) - Math.max(0, currentTop))) - 140;

					// 只有当 top 在初始值与 164 之间时才更新高度
					if (currentTop >= 164 && currentTop <= initialTopRef.current) {
						const targetElement = tableExpandRef.current.querySelector('div.ant-collapse-content-box > div > div');

						if (targetElement) {
							// 动态设置 max-height 和应用线性动画
							setSmoothTransition(targetElement); // 添加平滑动画效果

							animationFrameId = requestAnimationFrame(() => {
								targetElement.style.maxHeight = `${visibleHeight}px`; // 设置新的 max-height
							});
						}
					}
				}
			};

			const handleScrollAndResize = () => {
				hasScrolledOrResized = true; // 当发生滚动或调整窗口大小时，将标志位设置为 true
				animationFrameId = requestAnimationFrame(updateVisibleHeight);
			};

			// 添加 scroll 和 resize 事件监听
			window.addEventListener('scroll', handleScrollAndResize);
			window.addEventListener('resize', handleScrollAndResize);

			// 清理函数
			return () => {
				window.removeEventListener('scroll', handleScrollAndResize);
				window.removeEventListener('resize', handleScrollAndResize);

				// 清除未执行的 animation frame
				if (animationFrameId) {
					cancelAnimationFrame(animationFrameId);
				}
			};
		}
	}, []);


	const getSummary = useCallback(() => {
		if (props?.dataSource && footerDataSource) {
			if (typeof footerDataSource === 'function') {
				return	footerDataSource(props?.dataSource);
			}
		} else if (Array.isArray(footerDataSource)) {
			return footerDataSource;
		}
	}, [props?.dataSource, footerDataSource]);



	const TableNDom = props.useDrag ? TableNodeSort : TableNode;
	return (
		<div className={ hideLayoutClassName ? '' : s.layoutClassName }>
			{reset.cardComp}
			{expandContext && colSortSettingNode ? (
				<div className={ s.stickyTop }>
					<div className={ cs("r-flex") }>
						<div
							className={ cs(
								expandStyle.contentClassName,
								"r-flex-1 r-flex"
							) }
							style={ expandStyle.contentStyle }
						>
							<div className="r-flex-1 r-flex">{expandContext}</div>
							{headerSortKey
							&& ColSetPlaceEnum.默认 == ColSetInPlace && (
								<div
									className={ cs(
										"r-flex r-ai-c r-ml-10 r-pd-tb-12"
									) }
								>
									{colSortSettingNode}
								</div>
							)}
						</div>
					</div>
				</div>
			) : null}
			<div
				className={ cs(
					s.innerTable,
					"innerTableChange",
					typeof props?.innerTableStyle === "string"
						? props?.innerTableStyle
						: "",
					isStickyHeader && s['table-header-sticky']
				) }
				style={ {
					...(noPadding ? { padding: 0 } : {}),
					...(typeof props?.innerTableStyle === "object"
						? props?.innerTableStyle
						: {}),
				} }
			>
				{tableExtra || ""}
				<>
					{props?.tableExtraFn?.(
						ColSetPlaceEnum.表格上方 == ColSetInPlace
							? colSortSettingNode
							: ""
					) || ""}
				</>
			</div>
			<div className={ s['table-container'] }>
				{tableExpandContext && <div ref={ tableExpandRef }>{React.cloneElement(tableExpandContext())}</div>}
				<div style={ { flex: 1 } }>
					{props.emptyNode && !props.dataSource?.length ? (
						props.emptyNode
					) : (
						<TableNDom
							className="bordered"
							isLoading={ reset?.loading }
							isStickyHeader={ Boolean(isStickyHeader) }
							stickyTop={ stickyTop || (tableExtra ? 200 : 162) }
							expandWidth={ expandWidth }
							footerDataSource={ getSummary() }
							paginationStyle={ paginationStyle }
							{ ...reset }
							scroll={ { ...(props?.scroll || {}) } }
							components={ plusComponents }
							{ ...pipeline.getProps() }
						/>
					)}
				</div>
			</div>
		</div>
	);
}
export default React.forwardRef(BaseTable);
