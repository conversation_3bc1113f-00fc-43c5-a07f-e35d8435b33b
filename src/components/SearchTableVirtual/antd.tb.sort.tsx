import Icon, { HolderOutlined, MenuOutlined } from "@ant-design/icons";
import { closestCenter, DndContext, DragEndEvent, DragOverEvent, DragOverlay, DragStartEvent, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
	BaseTable,
	BaseTableProps,
	Classes
} from "ali-react-table-threshold";
import { Button, message } from "antd";
import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";
import styled from "styled-components";
import cs from 'classnames';
import Flex from "../Flex";
import { RowContextProps } from "./drag-table";
import { AntEmptyContent, AntLoadingContentWrapper } from "./antd.tb";
import s from './index.module.scss';

interface AntBaseTableProps extends BaseTableProps {
	expandWidth?: number;
	bordered?:boolean
}


const StyledBaseTable = styled(BaseTable)`
	--line-height: 1.5715;
	--font-size: 12px;
	--row-height: 32px;
	--header-row-height: 36px;
	--cell-padding: 8px;

	--lock-shadow: rgba(0, 0, 0, 0.2) 0 0 10px 0px;
	--border-color: #f0f0f0;
	--color: rgba(0, 0, 0, 0.85);
	--bgcolor: white;
	--hover-bgcolor: #fafafa;
	--highlight-bgcolor: #fafafa;
	--header-color: rgba(0, 0, 0, 0.85);
	--header-bgcolor: #fafafa;
	--header-hover-bgcolor: #f5f5f5;
	--header-highlight-bgcolor: #f5f5f5;
	.art-table-cell {
		word-break: break-all;
		border-right:unset!important;
		height: 95px;
	}
	.art-table {
		padding-bottom: 0 !important;
	}
	.art-empty-wrapper{
		margin:24px 0;
	}
	.art-table-header-cell {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		word-break: keep-all;
		overflow-wrap: break-word;
		background: #ebebeb;
		color: rgba(0, 0, 0, 0.85);
		font-weight: bold;
	}
	.art-table-header-row {
		.art-table-header-cell:last-of-type,
		.art-table-header-cell:nth-child(1) {
			.resize-handle {
				display: none;
			}
		}
	}
	.art-table-header-cell {
		.ant-checkbox-wrapper {
			margin-left: -8px;
		}
	}
	.art-lock-shadow-mask {
		z-index: 13;
	}

	.expansion-icon {
		display: none;
	}
	.resize-handle {
		width: 2px;
		right: -1px;
	}
	.art-sticky-scroll {
		display: block !important;
		bottom: -72px!important;
		z-index:9;
	}
	.art-table-body::-webkit-scrollbar {
		display: none;
	}
	.art-table-row {
		#myBaseTable {
			z-index: 15;
		}
	}
	&.dark {
		--lock-shadow: black 0 0px 6px 2px;
		--border-color: #303030;
		--color: rgba(255, 255, 255, 0.65);
		--bgcolor: #141414;
		--hover-bgcolor: #262626;
		--highlight-bgcolor: #262626;
		--header-color: rgba(255, 255, 255, 0.85);
		--header-bgcolor: #1d1d1d;
		--hover-hover-bgcolor: #222;
		--header-highlight-bgcolor: #222;
	}

	tr:not(.no-highlight).highlight > td {
		background-color: #fef7e8;
	}
	&.compact {
		--cell-padding: 12px 8px;
	}

	td {
		transition: background 0.3s;
	}

	th {
		font-weight: 500;
	}

	.${Classes.lockShadowMask} {
		.${Classes.lockShadow} {
			transition: box-shadow 0.3s;
		}
	}
	.highlight,
	.highlighted-row {
		background-color: #ffe7ba !important;
		& > td {
			background-color: #ffe7ba !important;
		}
	}	

	&:not(.bordered) {
		--cell-border-vertical: none;
		--header-cell-border-vertical: none;

		thead > tr.first th {
			border-top: none;
		}
	}
	.art-table-header.no-scrollbar {
		z-index: 99;
	}
	.art-table-footer{
		font-weight: 700;
		overflow-x:hidden;
		tfoot td{
			background-color: #ebebeb !important
		}
}
` as unknown as typeof BaseTable;
export const RowContext = React.createContext<RowContextProps>({
	setActivatorNodeRef: () => { },
	listeners: {},
});
// 拖拽手柄组件 - 整行动画版本
export const DragHandle = ({ record, rowTransformRef, onTransformUpdate, rowIndex } :any) => {
	const { attributes, listeners, setNodeRef, transform, transition, isDragging, } = useSortable({ id: record.sysSkuId });
	// 实时更新transform到ref中，供其他列使用
	const transformString = CSS.Transform.toString(transform);
	const transitionString = isDragging ? 'none' : (transition || 'transform 200ms ease');
	// 使用useEffect确保transform变化时触发更新
	React.useEffect(() => {
		if (transformString && transformString !== 'none') {
			rowTransformRef.current.set(record.sysSkuId, {
				transform: transformString,
				transition: transitionString
			});
		} else {
			rowTransformRef.current.delete(record.sysSkuId);
		}
		onTransformUpdate(); // 触发强制更新
	}, [transformString, transitionString, record.sysSkuId, rowTransformRef, onTransformUpdate]);

	const style = { transform: transformString, transition: transitionString };
	const { setActivatorNodeRef, } = useContext(RowContext);

	return (
		<div
			ref={ setNodeRef }
			style={ {
				display: 'flex',
				alignItems: 'center',
				flexDirection: 'column',
				cursor: 'grab',
				opacity: isDragging ? 0.3 : 1,
				...style,
			} }
			{ ...attributes }
			{ ...listeners }
			title="拖拽排序"
		>
			<div>{rowIndex + 1}</div>
			<div style={ { visibility: 'hidden' } } className="dragHandleIcon">
				<MenuOutlined
					style={ {
						cursor: 'grabbing',
						fontSize: '16px',
					} }
					ref={ setActivatorNodeRef }
					{ ...listeners }
				/>
			</div>
		</div>
	);
};


// 真实完整行的拖拽覆盖层组件 - 完全克隆原行
const DragOverlayRow = ({ record, columns, rowIndex }: { record: any; columns: any[]; rowIndex: number }) => {
	if (!record) return null;
	const totalWidth = 40 + (columns?.reduce((sum, col) => sum + (col.width || 150), 0) || 0);
	// 完全模拟表格行的结构和样式
	console.log('columns', columns);
	return (
		<div style={ {
			display: 'table',
			tableLayout: 'fixed',
			width: `${totalWidth}px`,
			backgroundColor: '#ffffff',
			border: '2px dashed #fd8204',
			height: '95px',
			boxShadow: '0 12px 32px rgba(0, 0, 0, 0.15)',
			opacity: 0.98,
			overflow: 'hidden',
			zIndex: 999
		} }
		>
			<div style={ { display: 'table-row' } }>
				<div key="id" style={ { width: '40px', height: '95px', padding: '8px' } } >
					<div>{rowIndex + 1}</div>
					<div>
						<MenuOutlined style={ { fontSize: '16px', color: '#fd8204' } } />
					</div>
				</div>
				{columns?.map((col, index) => {
					console.log('col', col.dataIndex);
					return (
						<div
							key={ col.dataIndex }
							style={ {
								display: 'table-cell',
								width: `${col.width || 150}px`,
								overflow: 'hidden',
								textOverflow: 'ellipsis',
								whiteSpace: 'nowrap',
								verticalAlign: 'top',
								height: '79px',
							} }
						>
							<div style={ { padding: '8px' } } >
								{col.render(record[col.name], record, rowIndex)}
							</div>
						</div>
					);
				})}
			</div>
		</div>
	);
};

export const AntdBaseTable = React.forwardRef<BaseTable, AntBaseTableProps>(
	(props: any, ref: any) => {
		const [hoveredRowId, setHoveredRowId] = useState(null);
		const [activeRecord, setActiveRecord] = useState<any>(null);
		const [overRecord, setOverRecord] = useState<any>(null);
		const [isDragging, setIsDragging] = useState(false);
		const [, forceUpdate] = useState({}); // 用于强制更新组件
		const rowTransformRef = useRef<Map<string, { transform: string; transition: string }>>(new Map());
		const { dataSource, rowKey } = props;
		// 获取排序项ID列表
		const sortableIds = useMemo(() => dataSource.map(record => record.sysSkuId), [dataSource]);
		  // 配置传感器 - 平衡性能和稳定性
		const sensors = useSensors(
			useSensor(PointerSensor, { activationConstraint: { distance: 8, } }),
			useSensor(KeyboardSensor, { coordinateGetter: sortableKeyboardCoordinates })
		);
		// useEffect(() => {
		//   setTimeout(() => {
		// 	  debugger;
		//   }, 5000);
		// }, []);
		  // 处理拖拽开始
		const handleDragStart = useCallback((event: DragStartEvent) => {
			const { active } = event;
			const activeRecord = dataSource.find(record => record.sysSkuId === active.id);
			console.log('activeRecord', activeRecord);
			setActiveRecord(activeRecord);
			setOverRecord(null); // 清空之前的 over 状态
			setIsDragging(true);
			 // 存储每行的transform状态，用于整行动画同步
			// 清空之前可能残留的transform状态
			rowTransformRef.current.clear();
		}, [dataSource]);

		// 处理拖拽悬停 - 实时更新目标行视觉反馈
		const handleDragOver = useCallback((event: DragOverEvent) => {
			const { over } = event;
			if (over) {
				const overRecord = dataSource.find(record => record.sysSkuId === over.id);
				setOverRecord(overRecord);
			} else {
				setOverRecord(null);
			}
		}, [dataSource]);

		 // 处理拖拽结束 - 性能优化
		const handleDragEnd = useCallback((event: DragEndEvent) => {
			const { active, over } = event;
			// 先清空拖拽状态
			setActiveRecord(null);
			setOverRecord(null);
			setIsDragging(false);
			// 清空所有行的transform状态
			rowTransformRef.current.clear();
			console.log('over', active, over);
			if (!over || active.id === over.id) {
				return;
			}
			if (active.id !== over?.id) {
				const prevState = [...dataSource];
				const activeIndex = prevState.findIndex((record) => record[rowKey as string] === active?.id);
				const overIndex = prevState.findIndex((record) => record[rowKey as string] === over?.id);
				props?.handleDragEnd(arrayMove(prevState, activeIndex, overIndex));
			}
		}, [dataSource]);
		  // 强制更新组件的回调函数
		const handleTransformUpdate = useCallback(() => {
			forceUpdate({}); // 触发重渲染确保transform同步
		}, []);

		console.log('propsprops', props);
		return (
			<DndContext
				sensors={ sensors }
				collisionDetection={ closestCenter }
				onDragStart={ handleDragStart }
				onDragOver={ handleDragOver }
				onDragEnd={ handleDragEnd }
			>
				<SortableContext items={ sortableIds } strategy={ verticalListSortingStrategy }>
					<StyledBaseTable
						ref={ ref }
						{ ...props }
						columns={ [{
							title: '',
							name: 'id',
							width: 40,
							render: (value: number, record: any, rowIndex: number) => {
								// 检查当前行是否正在被拖拽
								const isBeingDragged = isDragging && activeRecord?.sysSkuId === record.sysSkuId;
								// 确保record存在，防止虚拟列表渲染时数据为空
								if (!record || !record.sysSkuId) {
									return null;
								}
								return (
									<div style={ {
										opacity: isBeingDragged ? 0.1 : 1, // 被拖拽行几乎透明，保持最小可见性
										pointerEvents: isBeingDragged ? 'none' as const : 'auto' as const, // 禁用交互
									} }
									>
										<DragHandle 
											record={ record } 
											rowIndex={ rowIndex }
											rowTransformRef={ rowTransformRef }
											onTransformUpdate={ handleTransformUpdate }
										/>
									</div>
								);
							}
						}, ...props.columns?.map(col => {
							return {
								...col,
								render: (value: number, record: any, rowIndex: number) => {
									   // 检查当前行是否正在被拖拽
									const isBeingDragged = isDragging && activeRecord?.sysSkuId === record.sysSkuId;
									// 获取该行的transform状态，实现整行同步动画
									const rowTransform = rowTransformRef.current.get(record.sysSkuId);
									// 确保record存在，防止虚拟列表渲染时数据为空
									if (!record || !record.sysSkuId) {
										return null;
									}
									return (
										<div
											style={ {
												opacity: isBeingDragged ? 0.1 : 1, // 被拖拽行几乎透明，保持最小可见性
												pointerEvents: isBeingDragged ? 'none' as const : 'auto' as const, // 禁用交互
												transform: rowTransform?.transform || 'none',
												transition: rowTransform?.transition || 'none',
											} }
										>
											{col.render(value, record, rowIndex)}
										</div>
									);
								}
							};
						})] }
						style={ {
							width: "1100px",
						} }
						components={ {
							EmptyContent: AntEmptyContent,
							LoadingContentWrapper: AntLoadingContentWrapper,
						} }
						getRowProps={ (r) => {
							console.log('rrrrr', r);
							const isHovered = hoveredRowId === r.sysSkuId;
							return {
								className: cs(
									{
										'hovered-row': isHovered,
									}
								),
								onMouseEnter: () => {
									setHoveredRowId(r.sysSkuId);
									r.isHovered = true;
								},
								onMouseLeave: () => {
									setHoveredRowId(null);
									r.isHovered = false;
								},
							};
						} }
					/>
				</SortableContext>
				<DragOverlay
					dropAnimation={ null }
					style={ { zIndex: 1000 } }
				>
					{activeRecord && (
						<DragOverlayRow 
							record={ activeRecord } 
							columns={ props.columns }
							rowIndex={ dataSource.findIndex(record => record.sysSkuId === activeRecord.sysSkuId) }
						/>
					)}
				</DragOverlay>
			</DndContext>
		);
	}
);
