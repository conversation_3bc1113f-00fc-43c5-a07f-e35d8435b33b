import {
	BaseTable,
	BaseTableProps,
	Classes,
	LoadingContentWrapperProps
} from "ali-react-table-threshold";
import { Spin } from "antd";
import React, { useEffect, useState } from "react";
import styled from "styled-components";
import cs from 'classnames';
import s from './index.module.scss';

interface AntBaseTableProps extends BaseTableProps {
	expandWidth?: number;
	bordered?:boolean
}

const isMacOs = () => /macintosh|mac os x/i.test(navigator.userAgent);

const StyledBaseTable = styled(BaseTable)`
	--line-height: 1.5715;
	--font-size: 12px;
	--row-height: 32px;
	--header-row-height: 36px;
	--cell-padding: 8px;

	--lock-shadow: rgba(0, 0, 0, 0.2) 0 0 10px 0px;
	--border-color: #f0f0f0;
	--color: rgba(0, 0, 0, 0.85);
	--bgcolor: white;
	--hover-bgcolor: #fafafa;
	--highlight-bgcolor: #fafafa;
	--header-color: rgba(0, 0, 0, 0.85);
	--header-bgcolor: #fafafa;
	--header-hover-bgcolor: #f5f5f5;
	--header-highlight-bgcolor: #f5f5f5;
	.art-table-cell {
		word-break: break-all;
	}
	.art-table {
		padding-bottom: ${(props) => {
	const originalPadding = 11; // 原始值
	const additionalPadding = parseFloat(props.paginationStyle?.bottom) || 0; // 获取额外的值，默认0
	return `${originalPadding + additionalPadding}px`; // 相加
}} !important;
	}
	.art-table-header-cell {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		word-break: keep-all;
		overflow-wrap: break-word;
		background: #ebebeb;
		color: rgba(0, 0, 0, 0.85);
		font-weight: bold;
	}
	.art-table-header-row {
		.art-table-header-cell:last-of-type,
		.art-table-header-cell:nth-child(1),
		.art-table-header-cell:nth-child(2) {
			.resize-handle {
				display: none;
			}
		}
	}
	.art-table-header-cell {
		.ant-checkbox-wrapper {
			margin-left: -8px;
		}
	}
	.art-lock-shadow-mask {
		z-index: 13;
	}

	.expansion-icon {
		display: none;
	}
	.resize-handle {
		width: 2px;
		right: -1px;
	}
	.art-sticky-scroll {
		display: block !important;
		bottom: ${(props) => {
	const originalPadding = props?.hidePagination ? 0 : props.paginationStyle?.originBottom ?? 50; // 原始值
	const additionalPadding = parseFloat(props.paginationStyle?.bottom) || 0; // 获取额外的值，默认0
	return props?.scrollBottom ? props?.scrollBottom + 'px' : `${originalPadding + additionalPadding}px`; // 相加
}}!important;
		z-index:9;
	}
	.art-table-body::-webkit-scrollbar {
		display: none;
	}
	.art-table-row {
		#myBaseTable {
			z-index: 15;
		}
	}
	&.dark {
		--lock-shadow: black 0 0px 6px 2px;
		--border-color: #303030;
		--color: rgba(255, 255, 255, 0.65);
		--bgcolor: #141414;
		--hover-bgcolor: #262626;
		--highlight-bgcolor: #262626;
		--header-color: rgba(255, 255, 255, 0.85);
		--header-bgcolor: #1d1d1d;
		--hover-hover-bgcolor: #222;
		--header-highlight-bgcolor: #222;
	}

	tr:not(.no-highlight).highlight > td {
		background-color: #fef7e8;
	}
	&.compact {
		--cell-padding: 12px 8px;
	}

	td {
		transition: background 0.3s;
	}

	th {
		font-weight: 500;
	}

	.${Classes.lockShadowMask} {
		.${Classes.lockShadow} {
			transition: box-shadow 0.3s;
		}
	}
	.highlight,
	.highlighted-row {
		background-color: #ffe7ba !important;
		& > td {
			background-color: #ffe7ba !important;
		}
	}	

	&:not(.bordered) {
		--cell-border-vertical: none;
		--header-cell-border-vertical: none;

		thead > tr.first th {
			border-top: none;
		}
	}
	.art-table-header.no-scrollbar {
		z-index: 99;
	}
	.art-table-footer{
		font-weight: 700;
		overflow-x:hidden;
		tfoot td{
			background-color: #ebebeb !important
		}
}
` as unknown as typeof BaseTable;

export const AntEmptyContent = React.memo(() => (
	<>
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width="64"
			height="41"
			className="ant-empty-img-simple"
			viewBox="0 0 64 41"
		>
			<g fill="none" fillRule="evenodd" transform="translate(0 1)">
				<ellipse
					cx="32"
					cy="33"
					className="ant-empty-img-simple-ellipse"
					rx="32"
					ry="7"
				/>
				<g fillRule="nonzero" className="ant-empty-img-simple-g">
					<path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z" />
					<path
						d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
						className="ant-empty-img-simple-path"
					/>
				</g>
			</g>
		</svg>
		<div
			className="empty-tips"
			style={ { marginTop: 8, color: "rgba(0,0,0,.25)", fontSize: 14 } }
		>
			暂无数据
		</div>
	</>
));

export function AntLoadingContentWrapper({
	children,
	visible,
}: LoadingContentWrapperProps) {
	return (
		<div
			className="ant-loading-content-wrapper"
			style={ { opacity: visible ? 0.6 : undefined } }
		>
			{children}
		</div>
	);
}

function BlockSpin() {
	return <Spin style={ { display: "block" } } />;
}
/** Ant Design 风格的基础表格组件.
 *
 * AntdBaseTable 在 ali-react-table 提供的 BaseTable 基础上定制了默认的表格样式
 * * `className="bordered"` 带边框样式
 * * `className="compact"` 紧凑样式
 * * `className="dark"` 暗色主题
 *
 * 其他样式暂未提供，可以根据需求自行添加~
 * */
export const AntdBaseTable = React.forwardRef<BaseTable, AntBaseTableProps>(
	(props, ref) => {
		const [hasVerticalScroll, setHasVerticalScroll] = useState(false);
		const [hoveredRowId, setHoveredRowId] = useState(null);
		useEffect(() => {
			if (props?.dataSource?.length) {
				const style = window.getComputedStyle(document.body);
				const flag = (style.overflowY === "scroll"
					|| style.overflowY === "auto")
					&& document.body.scrollHeight > document.body.clientHeight;
				setHasVerticalScroll(flag);
			}
		}, [props?.columns?.length, props?.dataSource]);

		const { rowClassName } = props;

		if (ref && ref?.current) {
			ref.current['hasVerticalScroll'] = hasVerticalScroll;
		}
		const { getRowProps: getRowProps1, expandKeys = [], onRow } = props;
		return (
			<StyledBaseTable
				ref={ ref }
				{ ...props }
				className={ cs(props?.bordered && 'bordered', props?.className) }
				style={ {
					maxWidth: `calc(100vw - ${props?.expandWidth ?? 0
					}px - 32px - ${hasVerticalScroll ? 11 : 0}px)`,
					minWidth: `calc(1200px - ${props?.expandWidth ?? 0
					}px - 32px)`,
					width: "100%",
				} }
				getRowProps={ (r, index) => {
					const nativeAttribute = getRowProps1?.(r, index); // 库自带的getRowProps
					const isHovered = hoveredRowId === r.rowId;
					const baseTableConfigOnRow = onRow ? onRow(r, index) : {}; // baseTableConfig 配置的onRow
					return {
						style: {
							position: "relative",
							...nativeAttribute?.style
						},
						className: cs(
							nativeAttribute?.className,
							r.expand ? s['highlighted-row'] : '',
							{
								'hovered-row': isHovered,
								[typeof rowClassName === 'string' ? rowClassName : rowClassName?.(r) ?? '']: !!rowClassName,
							}
						),
						onMouseEnter: () => {
							setHoveredRowId(r.rowId);
							r.isHovered = true;
						},
						onMouseLeave: () => {
							setHoveredRowId(null);
							r.isHovered = false;
						},
						onClick: baseTableConfigOnRow?.onClick ?? nativeAttribute?.onClick
					};
				} }
				components={ {
					EmptyContent: AntEmptyContent,
					LoadingContentWrapper: AntLoadingContentWrapper,
					LoadingIcon: BlockSpin,
					...props.components,
				} }
			/>
		);
	}
);
