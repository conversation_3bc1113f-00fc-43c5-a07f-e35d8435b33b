import { useToggle } from 'ahooks';
import { Input, SelectProps, InputProps, Row, Select, Tooltip } from 'antd';
import Modal from 'antd/lib/modal/Modal';
import React, { ChangeEvent, useEffect, useMemo, useRef, useState } from 'react';
import cs from 'classnames';
import Icon from '@/components/Icon';
import s from './index.module.scss';
import message from '@/components/message';


const { TextArea } = Input;
interface InputArrayMultiProps extends Omit<SelectProps, "onChange" | "value"> {
	value?: string[];
	onChange?: (value:string[]) => void;
	maxInputNum?: number;
	name?: string;
	maxInputLength?: number;
	lengthErrorMsg?: string;
	numErrorMsg?: string;
	enterSearch?: boolean;
}

const InputArrayMulti = (props:InputArrayMultiProps) => {
	const { value, onChange, disabled, maxInputNum = 200, size = "middle", className, maxInputLength, lengthErrorMsg, numErrorMsg, enterSearch = true, ...reset } = props;
	const [visible, { setLeft, setRight }] = useToggle();
	const [inputValue, setInputValue] = useState(props.value || []);
	const [addValue, setAddValue] = useState('');
	const inputRef = useRef(null);
	useEffect(() => {
		setInputValue(value);
	}, [value]);
	const handleOk = () => {
		const validValue = addValue.trim();
		const arr = validValue.length ? validValue.split('\n') : [];
		// 校验长度是否合法
		if (maxInputLength && validValue.length > maxInputLength) {
			if (lengthErrorMsg) {
				message.error(lengthErrorMsg);
			} else {
				message.error(`最多输入${maxInputLength}个字数，请重新输入`);
			}
			return;
		}
		// 校验数量是否合
		if (arr.length > maxInputNum) {
			if (numErrorMsg) {
				message.error(numErrorMsg);
			} else {
				message.error(`当前输入${arr.length}条，最大添加数量${maxInputNum}条`);
			}
			return;
		}
		setLeft();
		setInputValue(arr);
		onChange?.(arr);
	};
	const handleShowModal = () => {
		if (disabled) {
			return;
		}
		setRight();
		setAddValue((inputValue || []).join('\n'));
	};

	const onSelectChange = (value) => {
		setInputValue(value);
		onChange?.(value);
		if (value?.length == 1) {
			setTimeout(() => {
				inputRef?.current?.focus({
					cursor: 'end',
				});
			}, 0);
		}
	};

	const onInputChange = (e:ChangeEvent<HTMLInputElement>) => {
		const { value } = e.target;
		if (value == '') {
			setInputValue([]);
			onChange?.([]);
		} else {
			setInputValue([value]);
			onChange?.([value]);
		}
	};

	let iconSize = 20;
	let inputClassName = s.middleInput;
	if (size == 'small') {
		iconSize = 16;
		inputClassName = s.smallInpput;
	}

	const inputReset = useMemo(() => {
		const typeArr = ['placeholder', 'style', 'maxLength', 'type', 'name', 'id'];
		let obj = {};
		typeArr.forEach(i => {
			if (reset[i]) {
				obj[i] = reset[i];
			}
		});
		return obj;
	}, [reset]);


	return (
		<>
			<Tooltip    
				trigger={ ["focus", "click"] }
				placement="top" 
				title="单个查询默认模糊搜索，如需精确搜索请在字段开头加上@@，批量时默认精确查询"
			>
				{
					value?.length > 1 ? (
						<Select
							mode="tags"
							size={ size }
							style={ { width: '160px' } }
							value={ inputValue }
							onChange={ onSelectChange }
							showArrow
							tokenSeparators={ enterSearch ? [" "] : [] }
							open={ false }
							onInputKeyDown={ e => {
							// 通过key区分 code区分会存在数字键盘上的enter
								if (e.key === 'Enter' && enterSearch) {
									e.stopPropagation();
								}
							} }
							suffixIcon={ (<span onClick={ handleShowModal } ><Icon style={ { color: '#999' } } size={ iconSize } type="bianji" /></span>) }
							options={ [] }
							className={ cs(inputClassName, className) }
							maxTagCount={ 1 }
							maxTagTextLength={ 2 }
							{ ...reset }
						/>
					) : (
						<Input
							ref={ inputRef }
							size={ size }
							value={ inputValue?.[0] || '' }
							onChange={ onInputChange }
							style={ { width: '160px' } }
							{ ...inputReset }
							disabled={ disabled }
							suffix={ (<span onClick={ handleShowModal } ><Icon style={ { color: '#999' } } size={ iconSize } type="bianji" /></span>) }
							className={ cs(inputClassName, className) }
						/>
					)
				}

			</Tooltip>
			{visible ? (
				<Modal
					centered
					visible={ visible }
					width={ 600 }
					maskClosable={ false }
					bodyStyle={ { padding: "10px 24px", height: 314 } }
					onOk={ handleOk }
					onCancel={ () => setLeft() }
					okButtonProps={ { size: 'middle' } }
					cancelButtonProps={ { size: 'middle' } }
					title={ (<>批量添加<span className="k-c-text r-fs-14 r-ml-8">批量添加（换行输入）或从Excel中粘贴复制</span></>) }
				>
					<Row className="r-c-333 r-mb-16 r-fs-16">
						当前最大添加数量{maxInputNum}条
					</Row>
					<Row>
						<TextArea style={ { height: 194 } } value={ addValue } onChange={ (e) => setAddValue(e.target.value) } />
					</Row>
				</Modal>
			) : ''}
		</>
	);
};

export default InputArrayMulti;
