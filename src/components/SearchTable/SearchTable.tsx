/* eslint-disable react/require-default-props */
import React, { CSSProperties, useEffect, useCallback, useImperativeHandle, useMemo, useState, useRef, useLayoutEffect } from "react";
import { Button, Card, Form, FormInstance, Pagination, Col, ButtonProps, Divider } from "antd";
import { useAntdTable } from "ahooks";
import { TablePaginationConfig, TableProps } from "antd/es/table";
import { FormItemProps } from "antd/es/form";
import { RowProps } from "antd/es/row";
import { ColProps } from "antd/es/col";
import cs from 'classnames';
import { observer } from "mobx-react";
import { cloneDeep } from "lodash";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import iconFastScroll from "./iconFastScroll";
import BaseTable, { BaseTableProps } from "./BaseTable";
import FormWidthRow, { RowFormProps } from "./FormWidthRow";
import { defaultResponseProps } from "./interface";
import { defaultResponseAdapter } from "./utils";
import CachePagination from "../CachePagination";
import BaseCheckGroupTable from "./BaseCheckGroupTable";
// import BaseTableWithGroup from "./BaseTableWithGroup";
import KdzsDateRangePicker from "@/components/DateRangeComp/kdzsRangePicker";

import s from './index.module.scss';
import userStore from "@/stores/user";
import { PLAT_HAND } from "@/constants";

interface Result<T> {
	total: number;
	list: T[];
	[k:string]:any;
}

export interface ChangeDataProps<T> {
	total: number
	dataSource: T[]
	pageNo: number
	pageSize: number
}
export interface SearchTableProps<T, R, P> {
	fullWindowFixed?: boolean;
	form: FormInstance;
	baseTableConfig: BaseTableProps<T>;
	fetchData?: (p: P, type?:string) => Promise<R>;
	rowFormConfig?: RowFormProps;
	responseAdapter?: (res: R, params?:any) => Result<T>;
	searchBtnText?: string;
	searchBtnPoint?: string;
	resetBtnText?: string;
	showSearch?: boolean;
	showReset?: boolean;
	searchBtnProps?:ButtonProps;
	showCard?: boolean;
	onChange?: (data: ChangeDataProps<T>) => void;
	additionalFormNode?:React.ReactNode;
	pageSizeId?:string;
	hidePagination?: Boolean,
	autoSearch?:Boolean,
	allowClear?:Boolean,
	extra?:React.ReactNode;
	tableExtra?: React.ReactNode;
	showSearchToggle?: boolean;
	onReset?:()=>void;
	customConditionContent?:React.ReactNode;
	savePresetConditionContent?:React.ReactNode;
}

export interface SearchTableRefProps {
	[x: string]: any;
	submit: () => void;
	reset: () => void;
	refresh: Function;
	getSearchParams: Function;
}

type SearchTableContextProps = {
	submit:() => void,
	reset: () => void,
}

export const SearchTableContext = React.createContext<SearchTableContextProps>({

} as SearchTableContextProps);



function InternalSearchTable<
	T extends object = any,
	R extends object = any,
	P extends object = any
>(props: SearchTableProps<T, R, P>, ref: React.Ref<SearchTableRefProps>) {
	const {
		form,
		baseTableConfig,
		rowFormConfig,
		fetchData,
		responseAdapter,
		searchBtnText = "查询",
		searchBtnPoint = '',
		resetBtnText = "重置",
		showSearch = true,
		showReset = true,
		searchBtnProps,
		showCard = false,
		additionalFormNode,
		pageSizeId,
		hidePagination = false,
		autoSearch = true,
		allowClear,
		extra,
		tableExtra,
		showSearchToggle = false,
		customConditionContent,
		savePresetConditionContent,
		onReset
	} = props;
	// const autoSearch = userStore?.userSetting?.autoSearch;
	const [searchParams, setSearchParams] = useState({});
	const [dataSource, setDataSource] = useState([]);
	const [searchType, setSearchType] = useState('submit');
	const [toggleStatus, setToggleStatus] = useState(false);
	const defaultPagination = useMemo<TablePaginationConfig>(
		() => ({
			total: 0,
			defaultPageSize: pageSizeId ? (Number(localStorage.getItem(pageSizeId)) || 10) : 10,
			defaultCurrent: 1,
			showQuickJumper: true,
			showSizeChanger: true,
			showTotal: total => `共${total}条`
		}),
		[]
	);
	const tableVtRef = useRef(null);
	const [cardHeight, setCardHeight] = useState(0);
	const cardRef = useRef(null);
	useEffect(() => {
		cardRef?.current && setCardHeight(cardRef.current.clientHeight);
		props?.baseTableConfig?.cardHeight && setCardHeight(props?.baseTableConfig?.cardHeight);
	}, []);
	const defaultLayout = useMemo<{
		rowProps: RowProps;
		colProps: ColProps;
	}>(
		() => ({
			rowProps: {
				gutter: 8,
			},
			colProps: {
				xs: 24,
				sm: 12,
				md: 8,
				lg: 6,
				xl: 6,
				xxl: 6,
			},
		}),
		[]
	);

	const initialPageSize = baseTableConfig?.pagination?.defaultPageSize || baseTableConfig?.pagination?.pageSizeOptions?.[0] || 10;

	const initialPagination = {
		...defaultPagination,
		...(baseTableConfig.pagination || {}),
		defaultPageSize: pageSizeId ? (Number(localStorage.getItem(pageSizeId)) || initialPageSize) : initialPageSize, // 优先使用localStorage中的值，没有则使用默认值
	};
	const initialFormLayout = {
		rowProps: rowFormConfig?.rowProps || defaultLayout.rowProps,
		colProps: rowFormConfig?.colProps || defaultLayout.colProps,
	};

	const getTableData = async({ current, pageSize }) => {
		if (!fetchData) {
			return Promise.resolve({});
		}
		let params = {
			pageNo: current,
			pageSize,
			...form.getFieldsValue(),
		};
		if (rowFormConfig?.defaultParams && searchType === 'reset') {
			// 重置时,初始化默认状态
			form.setFieldsValue(rowFormConfig.defaultParams);
			params = { ...params, ...rowFormConfig.defaultParams };
		}
		setSearchParams(params);
		let res:any = { list: [], total: 0 };
		try {
			res = await fetchData(params, searchType);
		} catch (e) { console.log(e); }
		const adapterResponse = responseAdapter ? responseAdapter(res, params) : defaultResponseAdapter(res as defaultResponseProps<T>);
		return Promise.resolve(adapterResponse);
	};
	const { tableProps, loading, search, pagination, refresh } = useAntdTable<any, any>(
		getTableData,
		{
			defaultPageSize: initialPagination.defaultPageSize,
			defaultParams: [{ current: initialPagination.defaultCurrent, pageSize: initialPagination.defaultPageSize }, rowFormConfig?.defaultParams || {}],
			form,
			onSuccess: (data:any, params:any) => {
				if (pageSizeId) {
					localStorage.setItem(pageSizeId, (params?.[0]?.pageSize || 10).toString());
				}
				props.onChange?.({
					pageNo: params?.[0]?.current,
					pageSize: params?.[0]?.pageSize,
					total: data.total,
					dataSource: data.list
				});
			},
			manual: !autoSearch, // 默认非手动
		}
	);

	// useEffect(() => {
	// 	setDataSource(tableProps.dataSource);
	// }, [tableProps.dataSource]);

	// const { submit, reset=()=>{reset()} } = search;

	const reset = useCallback(() => {
		setSearchType('reset');
		onReset?.();
		const ShopData = renderFormRow?.defaultParams?.ShopData || {};
		if (allowClear) {
			const allFormItem = cloneDeep(form.getFieldsValue(true));
			for (let key in allFormItem) {
				if (key === 'ShopData') {
					if (ShopData.plat === PLAT_HAND) {
						allFormItem.ShopData = {
							plat: undefined,
							shopId: undefined
						};
					}
				} else {
					allFormItem[key] = undefined;
				}
			}
			form.setFieldsValue(allFormItem);
		} else {
			search.reset();
		}
	}, [search]);
	const submit = useCallback((e) => {
		e?.preventDefault();
		setSearchType('submit');
		search.submit();
	}, [search]);
	// 获取搜索栏参数
	const getSearchParams = () => {
		return searchParams;
	};
	useImperativeHandle(ref, () => {
		return {
			scrollTo: tableVtRef?.current?.scrollTo,
			scrollToIndex: tableVtRef?.current?.scrollToIndex,
			submit,
			reset,
			getSearchParams,
			refresh: (fc: Function) => {
				fc && fc();
				refresh();
			},
		};
	}, [submit, reset, refresh, getSearchParams, tableVtRef]);

	const renderFormRow = () => {
		if (!rowFormConfig) {
			return;
		}
		const initSubmitStyle = rowFormConfig?.submitStyle ? rowFormConfig?.submitStyle : {
			display: "flex",
			justifyContent: "flex-start",
			marginBottom: 0,
			flex: 1,
		};
		return (
			<FormWidthRow
				{ ...rowFormConfig }
				{ ...initialFormLayout }
				{ ...(showSearch
					? {
						fixedRightCol: (
							<Form.Item
								style={ initSubmitStyle }
								wrapperCol={ { span: 24 } }
							>
								<Col
									className="search-btn-wrap"
									style={ {
										flex: 1,
										display: "flex",
										justifyContent: "flex-start",
										marginBottom: "8px",
										alignItems: 'center',
									} }
								>
									{customConditionContent}
									<Button style={ { marginLeft: '-4px' } } loading={ loading } data-point={ searchBtnPoint } type="primary" htmlType="submit" onClick={ submit } size="small" { ...searchBtnProps }>
										{searchBtnText}
									</Button>
									{
										showReset ? (
											<Button
												size="small"
												onClick={ reset }
												style={ { marginLeft: 8 } }
												{ ...searchBtnProps }
											>
												{resetBtnText}
											</Button>
										) : ""
									}
									{savePresetConditionContent}
									{!!additionalFormNode && additionalFormNode}
								</Col>
							</Form.Item>
						),
					}
					: { fixedRightCol: <></> }) }
			/>
		);
	};
	const TableC = props?.baseTableConfig?.groupId ? BaseCheckGroupTable : BaseTable;
	const PaginationC = props?.baseTableConfig?.cachePgination ? CachePagination : Pagination;

	const cardComp = (
		<div className={ cs(s.formRowContainer, 'formRowContainer') } style={ { position: "relative" } }>
			<div
				className={ cs('r-bg-white', s.formRow, 'rowFormConfigStyle') }
				hidden={ !(!toggleStatus || !showSearchToggle) }
				style={ { ...rowFormConfig?.style, ...baseTableConfig.noPadding ? { paddingLeft: 0 } : {} } }
			>
				{ showCard ? <Card style={ { marginBottom: 20 } } bodyStyle={ { paddingBottom: 0 } }>{renderFormRow()}</Card> : renderFormRow() }
				{ rowFormConfig?.expandNode }
			</div>
			{
				showSearchToggle
					? (
						<div className={ cs(s.templateToggleWrapper, toggleStatus ? "r-pt-12" : "") }>
							<div className={ s.border } />
							<Button size="small" type="primary" className={ s.expandIcon } onClick={ () => { setToggleStatus((prev) => (!prev)); } } >
								{
									toggleStatus
										? (<><span>展开</span><DownOutlined /></>)
										: (<><span>收起</span><UpOutlined /></>)
								}
							</Button>
							<div className={ cs(s.border, s.borderRight) } />
						</div>
					)
					: null
			}
		</div>

	);
	return (
		<SearchTableContext.Provider value={ { submit, reset } }>
			<Form
				onValuesChange={ baseTableConfig.onFieldsChange }
				form={ form }
				// wrapperCol={ { offset: 8 } }
				// labelCol={ { offset: 8 } }
				className="search-table-con"
				size={ rowFormConfig?.size }
				labelAlign="right"
			>
				{rowFormConfig && (
					<>
						{props.fullWindowFixed ? <div ref={ cardRef } className={ s.fullWindowFixedDiv }>{ cardComp }</div> : null}
						{ cardComp }
					</>
				)}

				<div className={ cs(s.tableContainer) } style={ baseTableConfig.tableContainerStyle }>
					{ extra || null }
					<TableC<T>
						ref={ tableVtRef }
						dataSource={ tableProps.dataSource }
						loading={ tableProps.loading }
						{ ...baseTableConfig }
						pagination={ false }
						cardHeight={ cardHeight }
						tableExtra={ tableExtra }
					/>
				</div>
			</Form>
			<div style={ baseTableConfig.paginationWrapStyle } className={ cs('r-pt-2', 'r-pl-16', 'r-pr-16', 'r-pb-14', 'r-hairline--top', 'r-bg-white', baseTableConfig.showSelectedCount ? 'r-flex r-jc-sb r-ai-c' : '') } >
				<div>{baseTableConfig.showSelectedCount}</div>
				{
					!hidePagination && (!baseTableConfig.emptyNode || tableProps.dataSource?.length) ? (
						<PaginationC
							{ ...initialPagination }
							current={ pagination.current }
							pageSize={ pagination.pageSize }
							total={ pagination.total }
							// onChange={ pagination.onChange }
							showQuickJumper
							showSizeChanger
							pageSizeOptions={ [10, 20, 50, 100, 200] }
							style={ { marginTop: 16, textAlign: 'right' } }
							{ ...pagination }
							{ ...(baseTableConfig?.pagination ? baseTableConfig.pagination : {}) }
							onChange={ (page, pageSize) => {
								tableProps.onChange && tableProps.onChange({ current: page, pageSize });
							} }
						/>
					) : null
				}
			</div>
			{ props.fullWindowFixed && tableProps.dataSource?.length ? iconFastScroll() : ''}
		</SearchTableContext.Provider>
	);
}

const SearchTable = React.forwardRef(InternalSearchTable) as <
	T extends object = any,
	R extends object = any,
	P extends object = any
>(
	props: SearchTableProps<T, R, P>& { ref?: React.Ref<SearchTableRefProps> },
) => React.ReactElement;

export default observer(SearchTable);
