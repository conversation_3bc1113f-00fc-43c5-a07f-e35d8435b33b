@use "./var.scss";
@use "./theme.scss";

.ant-select-dropdown {
	width: auto !important;
}

.kdzs-select-selection-item {
	display: block;
	max-width: 140px;
	padding-left: 7px;
	overflow: hidden;
	font-size: 100%;
	line-height: 100%;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.ant-select-sm {
	.kdzs-select-selection-item {
		padding-left: 3px;
	}
}

.kdzs-search-btn {
	padding-right: 25px;
	padding-left: 25px;
	&.ant-btn {
		padding-right: 25px;
		padding-left: 25px;
	}
}

.ellipsis {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.line-clamp-1 {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.line-clamp-2 {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

@mixin triangle($borderWidth: 6px) {
	display: inline-block;
	width: 0;
	height: 0;
	border: $borderWidth solid transparent;
}

.triangle-fill-left {
	@include triangle(6px);
	margin-left: -6px;
	border-right: 6px solid #fff;
}

.triangle-fill-right {
	@include triangle(6px);
	margin-right: -6px;
	border-left: 6px solid #fff;
}

.k-flex {
	display: flex !important;
}

// .kdzs-select-dropdown {
// 	max-height: 450px;
// 	overflow-y: auto !important;
// }

.kdzs-link-text {
	color: #1890ff;
	cursor: pointer;
}
.kdzs-link-text:hover {
	color: #1890ff;
}
.kdzs-link-text-theme {
	color: #fd8305;
	cursor: pointer;
	&:hover {
		color: #fd8305;
	}
}

// 正在加载
.text-loading::after {
	position: absolute;
	display: inline-block;
	margin-left: 2px;
	text-align: left;
	animation: dotty steps(1, end) 0.5s infinite;
	content: "";
}

@keyframes dotty {
	0% {
		content: "";
	}
	25% {
		content: ".";
	}
	50% {
		content: "..";
	}
	75% {
		content: "...";
	}
	100% {
		content: "";
	}
}

// 这个主要用来告知用户"当前页面内容可往下滑，还有更多数据"，以此提升用户体验
.overflow-tip {
	position: relative;
}
.overflow-tip::before {
	position: absolute;
	bottom: 0;
	z-index: 100;
	width: 100%;
	height: 8px;
	box-shadow: inset 0 -10px 8px -8px #d9d9d9;
	opacity: 0.5;
	content: " ";
}

// 分割符
.separator-cn-comma + .separator-cn-comma::before {
	content: "，";
}
.separator-en-comma + .separator-en-comma::before {
	content: ",";
}
.separator-pause-mark + .separator-pause-mark::before {
	content: "、";
}
// label必选标识
.kdzs-label-required {
	position: relative;
	display: inline-flex;
	align-items: center;
}

.kdzs-label-required::before {
	position: absolute;
	left: -10px;
	display: inline-block;
	height: 100%;
	color: #f00;
	font-size: 18px;
	line-height: 25px;
	content: "*";
}

// 分割符
.separator-cn-comma + .separator-cn-comma::before {
	content: "，";
}
.separator-en-comma + .separator-en-comma::before {
	content: ",";
}
.separator-pause-mark + .separator-pause-mark::before {
	content: "、";
}

@keyframes BackgroundGradient {
	0% {
		background-color: #fff;
	}
	100% {
		background-color: rgb(253 129 6 / 20%);
	}
}
.kdzs-table-tr-active {
	transform: translateZ(0);
	animation: BackgroundGradient 0.4s 1;
}


// 高亮显示样式
.high-light {
	color: #e63e35;
}
.high-light-bg{
	.ant-select-selector{
		background-color: #FFF7E6!important;
		border: 1px solid #FFA940!important;
	}
}
.high-light-bg.ant-input,
.high-light-bg.ant-input-affix-wrapper{
	background-color: #FFF7E6!important;
	border: 1px solid #FFA940!important;
}

.high-light-bg.ant-input-affix-wrapper input{
	background-color: #FFF7E6!important;
}

.high-light-bg.ant-picker{
	background-color: #FFF7E6!important;
	border: 1px solid #FFA940!important;
}
.high-placeholder{
	input::placeholder{
		color: #FFC069;
	}
}