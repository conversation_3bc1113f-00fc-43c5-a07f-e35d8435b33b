import React, { ReactNode, useMemo } from "react";
import { Image, Popover, Tooltip } from "antd";
import cs from "classnames";
import { TooltipPlacement } from "antd/lib/tooltip";
import s from "./index.module.scss";
import { DEFAULT_IMG, PLAT_JD, PLAT_XHS, PLAT_YZ } from "@/constants";
import { getPlatformDetailLink } from '@/pages/AfterSale/TradeList/utils';
import userStore from "@/stores/user";
import { getImageThumbnail } from "@/utils/img.scale";

interface WaresInfoProps {
	onlyImg?: boolean;
	imgSize?: number;
	align?: string; // 图片和标题在flex布局下 align-items 属性
	imgUrl?: string;
	skuName?: string | number;
	outerId?: string;
	placement?: TooltipPlacement; // 控制大图出现位置
	isShowHoverName?: boolean;
	isShowHoverStock?: boolean;
	isCheckedStore?: boolean;
	orderInfo?: any;
	hoverImgWidth?: number;
	hoverImgHeight?: number;
	linkDetailId?:string; // 点击图片跳品台商品详情
	linkDetailPlatForm?:string; // 点击图片跳的平台
	isCombination?:number; // 是否组合 1组合
	wareName?: string;
	className?: string;
	imageClassName?: string;
	num?: string | number;
	barCode?: string;
	skuNum?: number;
	isFontBlack?: boolean;
	showNumIid?: boolean;
	noScale?: boolean; // 是否不使用缩放
	banJump?: boolean; // a标签禁止跳转
	previewPicSize?: number; // 预览图大小类型，0小图，1中图，2大图
}
const WaresInfo: React.FC<WaresInfoProps> = (props) => {
	const {
		imgSize = 48,
		onlyImg = false,
		className,
		imageClassName,
		wareName,
		skuName,
		outerId,
		barCode,
		imgUrl,
		placement = "right",
		align = "c",
		num,
		isShowHoverName = false,
		isShowHoverStock = false,
		orderInfo = {},
		hoverImgWidth,
		hoverImgHeight,
		linkDetailId,
		linkDetailPlatForm,
		isCombination,
		skuNum = 0,
		isFontBlack = false,
		banJump = false,
		previewPicSize,
		showNumIid = false,
		noScale = false,
	} = props;

	// 根据设置或传入的预览图大小类型确定预览图大小
	const getPreviewImageSize = () => {
		// 只使用传入的previewPicSize属性，不再寻找全局设置
		// 强制转换为数字类型
		const propValue = previewPicSize !== undefined ? Number(previewPicSize) : 1; // 默认值为1

		// 根据 propValue 返回相应的像素值
		switch (propValue) {
			case 0: return 300;
			case 2: return 800;
			case 1:
			default: return 500;
		}
	};
	// console.log(99, props);

	const getLinkHref = useMemo(() => {
		let id = linkDetailId;
		if ([PLAT_XHS, PLAT_JD].includes(linkDetailPlatForm)) {
			id = orderInfo.skuId;
		}
		if ([PLAT_YZ].includes(linkDetailPlatForm)) {
			id = orderInfo.skuUuid;
		}
		if (banJump) return undefined;
		return getPlatformDetailLink(linkDetailPlatForm, id);
	}, [linkDetailId, linkDetailPlatForm, orderInfo.skuId, orderInfo.skuUuid, banJump]);

	const { alreadyAllotStockNum, sysStockCount, stockPreemptedNum } = orderInfo;

	const popContent: ReactNode = (
		<div>
			<a
				// eslint-disable-next-line no-script-url
				href={ getLinkHref || 'javascript:void(0)' }
				target={ getLinkHref ? '_blank' : '_self' }
				rel="noopener noreferrer"
				className={ cs("r-flex", "r-ai-c") }
			>
				<Image
					width={ hoverImgWidth || getPreviewImageSize() }
					height={ hoverImgHeight || getPreviewImageSize() }
					src={ imgUrl || DEFAULT_IMG }
					preview={ false }
					fallback={ DEFAULT_IMG }
					className={ cs(s['image-container'], 'r-pointer') }
					loading="lazy"
				/>
			</a>

			{isShowHoverName ? (
				<div style={ { width: hoverImgWidth || getPreviewImageSize() } }>
					<div className={ s['hover-item-title'] }>
						{wareName}{wareName ? `_${orderInfo.numIid}` : ""}
					</div>
					<div className={ s['hover-item-sku'] }>
						{skuName} <span className="r-c-333">* {skuNum}</span>
					</div>
				</div>
			) : (
				""
			)}

			{
				isShowHoverStock
				&& userStore.isStockAllocationVersion
				&& !orderInfo.noGoodsLink
					? (
						<div style={ { width: hoverImgWidth || 200 } }>
							<div className={ s['hover-item-stock'] }>
								库存分配：{ alreadyAllotStockNum }/{ orderInfo.num }
							</div>
							{
								stockPreemptedNum ? (
									<div className={ s['hover-item-stock'] }>
										可支配/订单占用/实际总库存：{ sysStockCount - stockPreemptedNum}/{ stockPreemptedNum }/{sysStockCount}
									</div>
								) : null
							}

						</div>
					) : ""
			}
		</div>
	);


	const getExpendDom = () => {
		return (
			<div className={ cs("r-flex-1", "r-ml-8", "r-l-preWrap") }>
				{wareName && <div className={ cs("r-wb-bw") }>{wareName}{wareName && showNumIid && `_${linkDetailId}`}</div>}

				{skuName && (
					<div
						style={ { fontSize: "12px" } }
						className={ cs("r-wb-bw", isFontBlack ? 'r-fc-black-85' : "r-fc-black-45",) }
					>
						{skuName}
					</div>
				)}

				{outerId && (
					<div
						style={ { fontSize: "12px" } }
						className={ cs("r-fc-black-45", "r-wb-bw") }
					>
						{outerId}
					</div>
				)}

				{barCode && (
					<div
						style={ { fontSize: "12px" } }
						className={ cs("r-fc-black-45", "r-wb-bw") }
					>
						{barCode}
					</div>
				)}

				{num && <div className={ cs("r-c-error") }>{num}</div>}
			</div>
		);
	};

	return (
		<div className={ cs("r-flex", `r-ai-${align}`, "r-l-preWrap", className) }>
			<Popover placement={ placement } content={ popContent }>
				<a
					// eslint-disable-next-line no-script-url
					href={ getLinkHref || 'javascript:void(0)' }
					target={ getLinkHref ? '_blank' : '_self' }
					rel="noopener noreferrer"
					className={ cs("r-flex", `r-ai-c`) }
				>
					<Image
						referrerPolicy="no-referrer"
						className={ cs(s[imageClassName]) }
						style={ { border: "1px solid #E5E5E5" } }
						width={ imgSize }
						height={ imgSize }
						src={ getImageThumbnail({
							noScale,
							url: imgUrl,
							width: imgSize,
							height: imgSize,
							quality: 100
						}) || DEFAULT_IMG }
						fallback={ imgUrl || DEFAULT_IMG }
						preview={ false }
						loading="lazy"
					/>
				</a>
			</Popover>

			{!onlyImg ? (
				!isCombination ? (
					getExpendDom()
				) : (
					<div className="r-flex r-ai-c">
						<Tooltip title="组合货品">
							<span className="r-warehouse-combined" style={ { marginRight: 0, marginLeft: 8 } }>组</span>
						</Tooltip>

						{getExpendDom()}
					</div>
				)

			) : (
				""
			)}
		</div>
	);
};
export default WaresInfo;
