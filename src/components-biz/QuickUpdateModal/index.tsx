import { Modal, Checkbox, Space } from "antd";
import React, { useState } from "react";
import { useRequest } from "ahooks";
import { ExclamationCircleOutlined, WarningFilled } from '@ant-design/icons';
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import message from "@/components/message";
import s from './index.module.scss';
import {
	ItemBatchUpdatePlatformOuterIdsApi
} from '@/apis/warehouse/system';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";

export interface IQuickUpdateModalProps {
	visible: boolean,
	list: any,
	onCancel?: (data?: any) => void;
	onSuccess?: (data?: any) => void;
}


const QuickUpdateModal = (props: IQuickUpdateModalProps) => {
	const {
		visible,
		list,
		onSuccess,
		onCancel
	} = props;
	const [updateOuter, setUpdateOuter] = useState(true);
	const [updateSku, setUpdateSku] = useState(true);
	const { runAsync: updateApi, loading: updateLoading } = useRequest(ItemBatchUpdatePlatformOuterIdsApi, {
		manual: true
	});
	const onOk = async() => {
		sendPoint(Pointer.商品_货品与商品关系_平台商品视角_批量更新平台编码);
		const { failUploadOuterIds = [], failUploadSkuOuterIds = [] } = await updateApi({ isUpLoadOuterId: updateOuter ? 1 : 0, isUpLoadSkuOuterId: updateSku ? 1 : 0, itemSkuUpdateDTOList: list });
		if (failUploadOuterIds.length == 0 && failUploadSkuOuterIds.length == 0) {
			message.success('更新成功');
		} else {
			const node1 = failUploadOuterIds?.map(i => i.split(':')).map(([k, ...v]) => <div>商家编码：【{k}】<span className="r-c-error">{v.join(':')}</span></div>);
			const node2 = failUploadSkuOuterIds?.map(i => i.split(':')).map(([k, ...v]) => <div>规格编码：【{k}】<span className="r-c-error">{v.join(':')}</span></div>);
			const content = (
				<div>
					以下商家编码/规格编码更新失败，请修改后重新更新
					<div className="r-mt-10" style={ { maxHeight: '300px', overflow: 'auto' } }>
						{node1}
						{node2}
					</div>
				</div>
			);
			Modal.warning({
				icon: <WarningFilled />,
				title: '提示',
				width: '500px',
				content,
				okText: '确认',
				centered: true,
			});
		}
		onSuccess();
		onCancel();
	};

	return (
		<Modal
			centered
			visible={ visible }
			width={ 455 }
			onCancel={ onCancel }
			destroyOnClose
			maskClosable={ false }
			confirmLoading={ updateLoading }
			onOk={ onOk }
		>
			<div className="r-flex">
				<div className={ s['clear-outerid-circle'] }>
					<ExclamationCircleOutlined />
				</div>
				<div>
					<div className={ s['clear-outerid-title'] }>系统提示</div>
					<div className="r-mt-10 r-mb-16">请选择要上传更新的编码</div>
					<Space direction="vertical">
						<Checkbox checked={ updateOuter } onChange={ (e:CheckboxChangeEvent) => setUpdateOuter(e.target.checked) }>上传更新商家编码</Checkbox>
						<Checkbox checked={ updateSku } onChange={ (e:CheckboxChangeEvent) => setUpdateSku(e.target.checked) }>上传更新规格编码</Checkbox>
					</Space>
					<div className="r-mt-10">
						<span className="r-c-error">注：京东、1688、淘工厂、快团团平台暂不支持商家编码/规格编码上传，抖店、快手暂不支持商家编码上传，小红书将上传至货号，视频号仅支持已上架商品</span>
					</div>
				</div>
			</div>
		</Modal>
	);
};

export default QuickUpdateModal;
