import classNames from "classnames";
import React, { useRef, useEffect, useState, useMemo } from "react";
import { Link } from "react-router-dom";
import { Menu, Modal, notification } from "antd";
import { observer } from "mobx-react";
import _, { debounce } from "lodash";
import dayjs from "dayjs";
import { WarningOutlined } from "@ant-design/icons";
import Icon from "@/components/Icon";
import s from "./index.module.scss";
import { FeedBackForm } from "@/pages/Index/Home/components/feedbackView";
import { MenuType } from "../layouts/HeaderMenu/menuBean";
import memoFn from "@/libs/memorizeFn";
import { tradeStore } from "@/stores";
import { IExceptionWarnSet, WarnStatusEnum, WarningRedPointShowEnum, WarningTipsShowEnum, getWarnSetDefaultVal } from "@/pages/ExceptionWarn/utils";
import { DeleteTradeWarnTipsCacheApi, GetRedDotMarketApi, QueryTradeNoticeWarnApi } from "@/apis/trade";
import { TradeNoticeWarnVo } from "@/types/trade/index";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import userStore from "@/stores/user";
import history from "@/utils/history";
import { local } from "@/libs/db";

interface RightDragButtonsType {
	title: string;
	href?: string;
	icon?: string;
	needRotate?: boolean;
	type: 'SubMenu' | 'ItemGroup' | 'subItem' | 'url' | 'Item';
	funcName?: string;
	children?: RightDragButtonsType[];
}

const notification_key = 'notification_key';
const { SubMenu } = Menu;

const rightDragButtons:RightDragButtonsType[] = [{
	title: "联系客服",
	href: "https://wpa1.qq.com/dSY8GJNm?_type=wpa&qidian=true",
	icon: 'qidianqq',
	type: MenuType.SubMenu,
	children: [{
		title: "联系客服",
		href: "https://wpa1.qq.com/dSY8GJNm?_type=wpa&qidian=true",
		type: MenuType.SubItem
	}],
}, {
	title: "异常预警",
	icon: 'sanjiaotishi',
	funcName: "exceptionWarn",
	type: MenuType.SubMenu,
	children: [{
		title: "异常预警",
		funcName: "exceptionWarn",
		type: MenuType.SubItem
	}],
}, {
	title: "近期更新",
	href: "https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/krkkqd",
	icon: 'jinqigengxin2',
	type: MenuType.SubMenu,
	children: [{
		title: "近期更新",
		href: "https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/krkkqd",
		type: MenuType.SubItem
	}],
}, {
	title: "使用教程",
	href: "https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3?",
	icon: 'shiyongjiaocheng',
	type: MenuType.SubMenu,
	children: [{
		title: "使用教程",
		href: "https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3?",
		type: MenuType.SubItem
	}],
}, {
	title: "意见反馈",
	icon: 'yijianfankui',
	funcName: "feedBack",
	type: MenuType.SubMenu,
	children: [{
		title: "意见反馈",
		funcName: "feedBack",
		type: MenuType.SubItem
	}],
}, {
	title: "收起侧栏",
	icon: 'shouqi1',
	needRotate: true,
	funcName: "collapse",
	type: MenuType.SubMenu,
	children: [{
		title: "收起侧栏",
		funcName: "collapse",
		type: MenuType.SubItem
	}],
}];

enum mouseStatus {
	鼠标按下 = "mousedown",
	鼠标移动 = "mousemove",
	鼠标抬起 = "mouseup",
}
let GlobalIntervalSeconds = 60;
const FloatButton = observer(() => {
	const ref = useRef();
	const [isCollapse, setIsCollapse] = useState(local.get('isCollapse'));
	// 是否在移动中 有移动就就不能点击里面内容
	const [isMoving, setIsMoving] = useState(false);
	// 意见反馈
	const [feedBackVisible, setFeedBackVisible] = useState(false);
	// 悬浮框是否在右边 如果是在左边，箭头需要变换方向
	const [isRight, setIsRight] = useState(true);


	// const [heightY, setHeightY] = useState(0);

	const [redDot, setRedDot] = useState(-1);

	const itemIcon = (item) => (
		<div>
			<Icon
				type={ item.icon }
				className="r-c-white"
				size={ 20 }
				style={ item.needRotate ? (isRight ? { rotate: "0deg" } : { rotate: "180deg" }) : null }
			/>
			{
				item.title == '异常预警' && redDot >= 0
					? <div className={ s.iconNum }> {redDot > 99 ? '99+' : redDot >= 0 ? redDot : null}</div>
					: null
			}
		</div>
	);

	const getNavMenuItems = (menusData: RightDragButtonsType[]) => {
		if (!menusData) {
			return [];
		}
		return menusData.map((item) => {
			let view = null;
			switch (item.type) {
				case MenuType.SubMenu:
					view = (
						<SubMenu
							key={ `${item.icon}_${item.title}` }
							icon={ itemIcon(item) }
							popupClassName={ classNames(s.subItem, s.menuContainer) }
							className={ classNames(s.item, s.borderBottom) }
							style={ { margin: 0 } }
							popupOffset={ [-5, 0] }
							onTitleClick={ () => { handleItem(item); } }
						>
							{getNavMenuItems(item.children)}
						</SubMenu>
					);
					break;
				case MenuType.SubItem:
					view = (
						<Menu.Item
							key={ `${item.icon}_${item.title}` }
							className={ classNames(s.subItem) }
							style={ { backgroundColor: "#fd8204", color: "white", margin: 0, fontWeight: 500, width: 100 } }
							onClick={ () => { handleItem(item); } }
						>
							{item.title}
						</Menu.Item>
					);
					break;
				default:
					view = (
						<Menu.Item key={ `${item.icon}_${item.title}` }>
							<Link to={ item.href }>{item.title}</Link>
						</Menu.Item>
					);
					break;
			}
			return view;
		});
	};

	const func = {
		exceptionWarn() {
			sendPoint(Pointer.订单异常预警_异常预警入口_点击次数);
			history.push('/trade/exceptionWarn');

		},
		feedBack: () => {
			setFeedBackVisible(true);
		},
		collapse: () => {
			setIsCollapse(!isCollapse);
		}

	};
	const handleItem = (item) => {
		if (isMoving) {
			setIsMoving(false);
			return;
		}
		if (item.href) {
			window.open(item.href);
		} else if (item.funcName) {
			func[item.funcName]();
		}
	};
	useEffect(() => {
		local.set('isCollapse', isCollapse || false);
	}, [isCollapse]);
	useEffect(() => {
		const dragBox = document.getElementById('dragButton');
		// 距离顶部高度
		const topHeight = 100;
		let isMouseDown = false;
		let offsetX;
		let offsetY;
		let originRight = 40;
		let originBottom = 400;
		// 初始距离
		let originX = document.body.clientWidth - originRight;
		let originY = document.body.clientHeight - originBottom;

		dragBox.style.transform = `translate(${originX}px, ${originY}px)`;
		// setHeightY(originY);

		const mousemoveFunc = (event) => {
			if (!isMouseDown) return;
			const x = event.clientX - offsetX;
			const y = event.clientY - offsetY;
			dragBox.style.transition = 'transform 0.1s ease-out';
			dragBox.style.transform = `translate(${x}px, ${y}px)`;
			// setHeightY(y);
			setIsMoving(true);
		};
		const mouseupFunc = (event) => {
			isMouseDown = false;
			snapToScreenEdge(dragBox);
			removeEvent([mouseStatus.鼠标移动, mouseStatus.鼠标抬起]);
		};
		const mousedownFunc = (event) => {
			removeEvent([mouseStatus.鼠标移动, mouseStatus.鼠标抬起]);
			// 鼠标移动时更新元素位置
			document.addEventListener(mouseStatus.鼠标移动, mousemoveFunc);
			// 鼠标松开时更新状态并执行吸附逻辑
			document.addEventListener(mouseStatus.鼠标抬起, mouseupFunc);
			isMouseDown = true;
			offsetX = event.clientX - dragBox.getBoundingClientRect().left;
			offsetY = event.clientY - dragBox.getBoundingClientRect().top;
		};
		const removeEvent = (removeList = [mouseStatus.鼠标按下, mouseStatus.鼠标移动, mouseStatus.鼠标抬起]) => {
			removeList.includes(mouseStatus.鼠标按下) && dragBox.removeEventListener(mouseStatus.鼠标按下, mousedownFunc);
			removeList.includes(mouseStatus.鼠标移动) && document.removeEventListener(mouseStatus.鼠标移动, mousemoveFunc);
			removeList.includes(mouseStatus.鼠标抬起) && document.removeEventListener(mouseStatus.鼠标抬起, mouseupFunc);
		};
		// 鼠标按下时记录状态和偏移量
		dragBox.addEventListener(mouseStatus.鼠标按下, mousedownFunc);

		// 吸附到屏幕左侧或右侧的函数
		function snapToScreenEdge(element) {
			const screenWidth = document.body.clientWidth;
			const screenHeight = window.innerHeight;
			const lowestY = screenHeight - dragBox.clientHeight || 0;
			const elementRect = element.getBoundingClientRect();
			let currentY = parseFloat(element.style.transform.split(',')[1] || element.style.top);
			if (currentY < topHeight) currentY = topHeight;
			if (currentY > lowestY) currentY = lowestY;
			if (elementRect.left < screenWidth / 2) {
				element.style.transform = `translate(0px, ${currentY}px)`;
				setIsRight(false);
			} else {
				const targetX = screenWidth - elementRect.width;
				element.style.transform = `translate(${targetX}px, ${currentY}px)`;
				setIsRight(true);
			}
			// setHeightY(currentY);
		}

		// 当浏览器窗口大小调整时，确保拖拽框保持在屏幕内
		window.addEventListener('resize', () => {
			snapToScreenEdge(dragBox);
		});

		window.addEventListener('scroll', function() {
			snapToScreenEdge(dragBox);
		});

		setTimeout(() => {
			snapToScreenEdge(dragBox);
		}, 500);

		return () => {
			removeEvent();
		};
	}, []);

	const clearNoticeWarnCache = debounce(() => {
		DeleteTradeWarnTipsCacheApi();
	}, 1000, { 'leading': true, 'trailing': false });

	const openNotification = (warnNotice: TradeNoticeWarnVo, data: IExceptionWarnSet) => {
		function toHandle(type) {
			return (
				<a onClick={ () => history.push(`/trade/exceptionWarn?warnStatus=${type}`) }>去处理</a>
			);
		}
		const nodeList = data.enableWarnList.map((item, index) => {
			switch (item) {
				case WarnStatusEnum.申请单号未打印:
					if (!warnNotice.appliedAndUnprintedNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.appliedAndUnprintedNum ?? 0} 笔</span>订单 产生<span className="r-c-error">申请单号未打印</span>异常</div>{toHandle(WarnStatusEnum.申请单号未打印)}</div>);
				case WarnStatusEnum.单号打印多次:
					if (!warnNotice.repeatPrintNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.repeatPrintNum ?? 0} 笔</span>订单 产生<span className="r-c-error">单号打印多次</span>异常</div>{toHandle(WarnStatusEnum.单号打印多次)}</div>);
				case WarnStatusEnum.已打印未发货:
					if (!warnNotice.printedAndUnshipNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.printedAndUnshipNum ?? 0} 笔</span>订单 产生<span className="r-c-error">已打印未发货</span>异常</div>{toHandle(WarnStatusEnum.已打印未发货)}</div>);
				case WarnStatusEnum.打印后未发货退款:
					if (!warnNotice.refundWarningNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.refundWarningNum ?? 0} 笔</span>订单 产生<span className="r-c-error">打印后未发货退款</span>异常</div>{toHandle(WarnStatusEnum.打印后未发货退款)}</div>);
				case WarnStatusEnum.打印后已发货退款:
					if (!warnNotice.printedAndShippedRefundNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.printedAndShippedRefundNum ?? 0} 笔</span>订单 产生<span className="r-c-error">打印后已发货退款</span>异常</div>{toHandle(WarnStatusEnum.打印后已发货退款)}</div>);
				case WarnStatusEnum.打印后改地址:
					if (!warnNotice.sendWarningNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.sendWarningNum ?? 0} 笔</span>订单产生<span className="r-c-error">打印后改地址</span>异常</div>{toHandle(WarnStatusEnum.打印后改地址)}</div>);
				case WarnStatusEnum.打印后改备注:
					if (!warnNotice.memoWarningNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.memoWarningNum ?? 0} 笔</span>订单产生<span className="r-c-error">打印后改备注</span>异常</div>{toHandle(WarnStatusEnum.打印后改备注)}</div>);
				case WarnStatusEnum.发货失败:
					if (!warnNotice.shipFailWarningNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.shipFailWarningNum ?? 0} 笔</span>订单产生<span className="r-c-error">发货失败</span>异常</div>{toHandle(WarnStatusEnum.发货失败)}</div>);
				case WarnStatusEnum.揽收前退款:
					if (!warnNotice.preAcceptRefundNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.preAcceptRefundNum ?? 0} 笔</span>订单产生<span className="r-c-error">揽收前退款</span>异常</div>{toHandle(WarnStatusEnum.揽收前退款)}</div>);
				case WarnStatusEnum.即将超时揽收:
					if (!warnNotice.nearTimeoutAcceptNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.nearTimeoutAcceptNum ?? 0} 笔</span>订单 产生<span className="r-c-error">即将超时揽收</span>异常</div>{toHandle(WarnStatusEnum.即将超时揽收)}</div>);
				case WarnStatusEnum.超时未揽收:
					if (!warnNotice.timeoutUnacceptableNum) return '';
					return (<div className="r-flex r-jc-sb" key={ item }><div>当前 <span className="r-c-error">{warnNotice.timeoutUnacceptableNum ?? 0} 笔</span>订单 产生<span className="r-c-error">超时未揽收</span>异常</div>{toHandle(WarnStatusEnum.超时未揽收)}</div>);
				default:
					return '';
			}
		}).filter(Boolean);

		notification.open({
			key: notification_key,
			message: (<strong>异常订单通知</strong>),
			// placement: isRight ? 'topRight' : 'topLeft',
			duration: null,
			description: (
				<>
					<div className="r-flex r-jc-sb">
						<div className="r-c-gray">{dayjs().format("YYYY-MM-DD HH:mm:ss")}</div>
						<div>
							<a className="r-c-primary" onClick={ () => history.push(`/trade/exceptionWarn?openSetting=${Math.ceil(Math.random() * 1000000)}`) }>预警提醒设置</a>
						</div>
					</div>
					<div className="r-mt-10 r-mb-10">
						{nodeList}
					</div>
					{/* <div
						className="r-click"
						onClick={ () => {
							history.push('/trade/exceptionWarn');
							notification.close(notification_key);
							clearNoticeWarnCache();
						} }
					>
						点击查看
					</div> */}
				</>
			),
			onClose() {
				clearNoticeWarnCache();
			},
			icon: <WarningOutlined style={ { color: '#fd8204' } } />,
			style: { top: '30px', width: 420, }
		});
	  };


	const handleRedDotMarket = async(data: IExceptionWarnSet) => {
		let { warningRedPointShow } = data;
		if (warningRedPointShow == WarningRedPointShowEnum.悬浮菜单窗显示红点) {
			const res = await GetRedDotMarketApi();
			let calNum = res.totalNum || 0;
			setRedDot(calNum);
			if (res.intervalSeconds) {
				GlobalIntervalSeconds = +res.intervalSeconds;
			}
		} else {
			setRedDot(-1);
		}
	};

	const handleShowWarnModal = async(data: IExceptionWarnSet) => {
		let { warningTipsShow } = data;
		if (warningTipsShow == WarningTipsShowEnum.不弹出提醒弹窗) {
			return;
		}
		const res = await QueryTradeNoticeWarnApi();
		let calNum = 0;
		data.enableWarnList.forEach(item => {
			calNum += item == WarnStatusEnum.申请单号未打印 ? +res.appliedAndUnprintedNum : 0;
			calNum += item == WarnStatusEnum.单号打印多次 ? +res.repeatPrintNum : 0;
			calNum += item == WarnStatusEnum.已打印未发货 ? +res.printedAndUnshipNum : 0;
			calNum += item == WarnStatusEnum.打印后未发货退款 ? +res.refundWarningNum : 0;
			calNum += item == WarnStatusEnum.打印后已发货退款 ? +res.printedAndShippedRefundNum : 0;
			calNum += item == WarnStatusEnum.打印后改地址 ? +res.sendWarningNum : 0;
			calNum += item == WarnStatusEnum.打印后改备注 ? +res.memoWarningNum : 0;
			calNum += item == WarnStatusEnum.发货失败 ? +res.shipFailWarningNum : 0;
			calNum += item == WarnStatusEnum.揽收前退款 ? +res.preAcceptRefundNum : 0;
			calNum += item == WarnStatusEnum.即将超时揽收 ? +res.nearTimeoutAcceptNum : 0;
			calNum += item == WarnStatusEnum.超时未揽收 ? +res.timeoutUnacceptableNum : 0;
		});
		if (calNum > 0) {
			openNotification({ ...res, calNum }, data);
			if (warningTipsShow == WarningTipsShowEnum.弹出提醒弹窗_隔10秒自动关闭弹窗) {
				setTimeout(() => {
					clearNoticeWarnCache();
					notification.close(notification_key);
				}, 10000);
			}
		} else {
			notification.close(notification_key);
		}
		if (res.intervalSeconds) {
			GlobalIntervalSeconds = +res.intervalSeconds;
		}

	};

	useEffect(() => {
		let timer;
		const clearTimer = () => timer && clearTimeout(timer);
		const fn = async() => {
			if (!userStore?.userInfo?.userId) {
				return;
			}
			clearTimer();
			const setting = await memoFn.getAdvancedSet();
			const data = getWarnSetDefaultVal(setting?.printSetExpandDTO);
			if (!data?.enableWarnList?.length) {
				notification.close(notification_key);
				return;
			}
			if (userStore.userInfo.subUserId && !userStore.userInfo.authorityDetail?.includes('"77"')) {
				return;
			}
			await handleRedDotMarket(data);
			await handleShowWarnModal(data);
			// 只有在页面可见时才设置下次定时器
			if (!document.hidden) {
				timer = setTimeout(() => {
					fn();
				}, GlobalIntervalSeconds * 1000);
			}
		};

		// 页面可见性变化处理函数
		const handleVisibilityChange = () => {
			console.log('handleVisibilityChange', document.hidden);
			if (document.hidden) {
				// 页面不可见时，清除定时器
				clearTimer();
			} else {
				// 页面变为可见时，设置定时器走原有的定时任务逻辑
				timer = setTimeout(() => {
					fn();
				}, GlobalIntervalSeconds * 1000);
			}
		};

		// 添加页面可见性监听
		document.addEventListener('visibilitychange', handleVisibilityChange);

		// 初始调用
		fn();

		return () => {
			clearTimer();
			document.removeEventListener('visibilitychange', handleVisibilityChange);
		};
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [tradeStore.exceptionWarnSet, userStore?.userInfo]);

	const newRightDragButtons = useMemo(() => {
		const isHasAllAuthority = !userStore.userInfo.subUserId;
		const hasAuth = userStore.userInfo.authorityDetail?.includes('"77"');
		if (isHasAllAuthority || hasAuth) {
			return rightDragButtons;
		} else {
			return rightDragButtons.filter(i => i.title !== '异常预警');
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [userStore?.userInfo?.authorityDetail]);

	return (
		<>
			<div className={ s.floatButtonContainer } id="dragButton">
				{
					isCollapse ? (
					// 展开
						<div className={ classNames(s.item) } onClick={ () => { handleItem({ funcName: "collapse" }); } }>
							<span className={ classNames("r-c-white", "r-fs-12", "r-fw-500") }>展开侧栏</span>
						</div>
					)
						: (
							<>
								<Menu
									key="Menu"
									mode="vertical"
									theme="dark"
									style={ { backgroundColor: "#fd8204", margin: 0 } }
									expandIcon={ (<></>) }
								>
									{getNavMenuItems(newRightDragButtons)}
								</Menu>
							</>
						)

				}
			</div>
			<Modal
				centered
				closable
				title="意见反馈"
				maskClosable={ false }
				visible={ feedBackVisible }
				onOk={ () => { console.log('ok'); ref?.current?.onOk(); } }
				onCancel={ () => { setFeedBackVisible(false); } }
			>
				<FeedBackForm ref={ ref } onOk={ () => { setFeedBackVisible(false); } } />
			</Modal>
		</>

	);
});
export default FloatButton;
