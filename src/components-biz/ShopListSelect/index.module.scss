.author-multi-select-wrap,
.shop-multi-select-wrap {
    :global {
        .ant-select-selection-overflow-item-suffix {
            display: none !important;
        }
    }
}

.custom-select-item {
    // line-height: 22px;
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 36px;
    padding: 4px 12px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: normal;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;

    &:hover {
        background-color: #f5f5f5;
    }
}

.select-dropdown-wrap {
    min-width: 200px;
    max-height: 450px;
    overflow-y: auto !important;
}

.select-selection-item {
    display: block;
    max-width: 140px;
    padding-left: 3px;
    overflow: hidden;
    font-size: 100%;
    line-height: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.error-info {
    margin-bottom: 4px;
    line-height: 12px;

    a {
        display: inline-block;
        color: #ff4d4f;
        font-size: 12px;

        &:hover {
            color: #ff4d4f;
            opacity: 0.8;
        }
    }

    .warning-text-3 {
        color: #ee5e01;

        // font-size: 12px;
        &:hover {
            // opacity: 0.8;
            color: #ee5e01;
        }
    }
}