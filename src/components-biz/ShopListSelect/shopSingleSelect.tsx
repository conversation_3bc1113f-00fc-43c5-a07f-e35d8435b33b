import { Checkbox, Input, Select, SelectProps } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import _, { intersection } from 'lodash';
import { useSetState } from 'ahooks';
import { observer } from 'mobx-react';
import { IShopInfo } from '@/types/schemas/user';
import { PLAT_HAND, PLAT_MAP, FuwuMarketOrderUrl, getShopStatus, PLAT_OTHER } from '@/constants';
import { platform } from '@/types/schemas/common';
import { getAllPlats, getShopsByPlat } from './shopListUtils';
import s from './index.module.scss';
import userStore from '@/stores/user';
import { getTradePlatformLabel } from '@/pages/Trade/utils';


const { Option } = Select;

export interface ShopSingleSelectValue {
	plat?: platform,
	shopId?: number | string,
	sellerNick?: string,
	allShopList?: IShopInfo[]
}

interface ShopSingleSelectProps extends Omit<SelectProps<any>, 'onChange' | 'value'> {
	value?: ShopSingleSelectValue;
	onChange?: (value: ShopSingleSelectValue) => void;
	isDisableStop?: boolean; // 暂停店铺 是否disable
	isDisableExpire?: boolean; // 已过期店铺 是否disable
	isHideAllPlat?: boolean; // 是否隐藏所有平台
	isHideAllShop?: boolean; // 是否隐藏所有店铺
	isHasHandPlat?: boolean; // 是否显示手工单平台
	hasDefault?: boolean; // 是否默认选中
	platDisabled?: boolean; // 平台是否disabled
	disabledShop?: boolean; // 店铺是否disabled
	isHasEbillPlat?: boolean; // 是否显示电子面单平台
	defaultChange?: (value: ShopSingleSelectValue) => void; // 默认选中回调
	searchShop?:boolean;// 选择店铺可以筛选
	filterOptions?: any[]; // 只显示这些平台的店铺
	hidePlatforms?: platform[]; // 需要隐藏的平台列表
}

const ShopSingleSelect: React.FC<ShopSingleSelectProps> = ({
	value = {},
	disabledShop,
	isDisableStop = false,
	isDisableExpire = false,
	isHideAllPlat = false,
	platDisabled = false,
	isHideAllShop = false,
	isHasHandPlat = false,
	hasDefault = false,
	isHasEbillPlat = false,
	searchShop = false,
	filterOptions = [],
	hidePlatforms = [],
	onChange,
	defaultChange,
	...reset
}) => {
	const { shopList } = userStore;
	const { plat, shopId } = value;
	const [shopValue, setShopValue] = useState<number | string>();
	const [platValue, setPlatValue] = useState<platform>(plat);
	const [searchInput, setSearchInput] = useState('');
	const [shopInfo, setShopInfo] = useSetState<{
		platList: platform[]
		shopList: IShopInfo[]
	}>({
		platList: [],
		shopList: []
	});

	// 检查平台是否需要隐藏的复合条件判断
	const shouldHidePlatform = (platform: platform) => {
		// 通用平台隐藏逻辑
		if (hidePlatforms?.length && hidePlatforms.includes(platform)) return true;
		return false;
	};

	// 检查店铺是否需要隐藏的复合条件判断
	const shouldHideShop = (shop: IShopInfo) => {
		// 通用店铺隐藏逻辑
		if (hidePlatforms?.length && hidePlatforms.includes(shop.platform)) return true;
		return false;
	};

	useEffect(() => {
		const init = async() => {
			if (hasDefault) {
				let platList = await getAllPlats(isHasHandPlat, isHasEbillPlat);
				
				// 根据 filterOptions 和 hidePlatforms 过滤平台列表
				if (filterOptions?.length) {
					platList = platList.filter(plat => filterOptions.includes(plat));
				}
				platList = platList.filter(platform => !shouldHidePlatform(platform));
				
				let _plats = plat || platList[0];
				let shopList = await getShopsByPlat({ plats: [_plats], hasHand: isHasHandPlat, isHasEbillPlat });
				
				// 过滤店铺列表
				if (filterOptions?.length) {
					shopList = shopList.filter(shop => filterOptions.includes(shop.platform));
				}
				shopList = shopList.filter(shop => !shouldHideShop(shop));

				let firstShopValue = shopList.filter(el => el.status === 1)[0]?.sellerId;
				setShopInfo({ platList, shopList });
				setPlatValue(_plats);
				setShopValue(firstShopValue);
				onChange?.({
					shopId: firstShopValue,
					plat: _plats,
					allShopList: shopList
				});
			} else {
				let platList = await getAllPlats(isHasHandPlat, isHasEbillPlat);
				let shopList = await getShopsByPlat({ hasHand: isHasHandPlat, isHasEbillPlat });
				
				// 根据 filterOptions 和 hidePlatforms 过滤平台列表
				if (filterOptions?.length) {
					platList = platList.filter(plat => filterOptions.includes(plat));
					shopList = shopList.filter(shop => filterOptions.includes(shop.platform));
				}
				platList = platList.filter(platform => !shouldHidePlatform(platform));
				shopList = shopList.filter(shop => !shouldHideShop(shop));
				
				let changeParams: ShopSingleSelectValue = {};
				setShopInfo({ platList, shopList });
				if (platList.length === 1 && !isHasHandPlat) {
					setPlatValue(platList[0]);
					changeParams['plat'] = platList[0];
				}

				if (shopList.length === 1 && !isHasHandPlat) {
					setShopValue(shopList[0].sellerId);
					changeParams['shopId'] = shopList[0].sellerId;
				}
				if (!_.isEmpty(changeParams)) {
					onChange?.({
						...value,
						...changeParams,
						allShopList: shopList
					});
				}
			}
		};
		init();
	}, [shopList]);

	useEffect(() => {
		const _plat = plat === PLAT_HAND ? PLAT_OTHER : plat;
		setPlatValue(_plat);
		setShopValue(shopId);
	}, [plat, shopId]);


	const handlePlatChange = async(plat: platform) => {
		setSearchInput(''); // 修改选择店铺搜索框

		setPlatValue(plat);

		// 根据选中的平台 更新店铺列表 shopList
		const newShopList = await getShopsByPlat({ plats: plat ? [plat] : [], hasHand: isHasHandPlat, isHasEbillPlat });
		
		// 过滤店铺列表
		let filteredShopList = newShopList;
		if (filterOptions?.length) {
			filteredShopList = filteredShopList.filter(shop => filterOptions.includes(shop.platform));
		}
		filteredShopList = filteredShopList.filter(shop => !shouldHideShop(shop));
		
		setShopInfo({
			shopList: filteredShopList,
		});

		// 更新选中的店铺， 如果删除某个平台，需要删除该平台已选中的店铺
		const newShopValue = intersection([shopValue], filteredShopList.map(item => item.sellerId));
		let newShopId = newShopValue[0];
		let _platForm = null;
		let sellerNick = null;
		if (!newShopId && filteredShopList.length === 1) {
			newShopId = filteredShopList[0].sellerId;
			_platForm = filteredShopList[0].platform;
			sellerNick = filteredShopList[0].sellerNick;
		}
		setShopValue(newShopId);
		
		onChange?.({
			...value,
			shopId: newShopId,
			plat: _platForm || plat,
			allShopList: filteredShopList,
			sellerNick,
		});

	};

	const handleShopChange = (shopId: number | string) => {
		setShopValue(shopId);
		let shopItem = shopInfo.shopList.find(item => item.sellerId === shopId);
		const _platform = shopItem?.platform !== PLAT_HAND ? shopItem?.platform : PLAT_OTHER;
		if (_platform) {
			setPlatValue(_platform);
		}
		onChange?.({
			...value,
			shopId,
			sellerNick: shopItem?.sellerNick,
			plat: shopItem?.platform || platValue,
			allShopList: shopInfo.shopList
		});
	};

	const newShopList = useMemo(() => {
		if (searchShop) {
			return shopInfo?.shopList?.filter(v => v.sellerNick?.includes(searchInput));
		} else {
			return shopInfo?.shopList;
		}
		
	}, [shopInfo.shopList, searchInput]);
	
	return (
		<div style={ { display: "flex", alignItems: "center" } }>
			<Select
				placeholder="选择平台"
				optionLabelProp="label"
				style={ { width: '160px' } }
				value={ platValue }
				disabled={ platDisabled }
				getPopupContainer={ trigger => trigger?.parentElement }
				onChange={ handlePlatChange }
				dropdownMatchSelectWidth={ false }
				{ ...reset }
			>
				{!isHideAllPlat && <Option value="" key="allPlat" label="所有平台">所有平台</Option>}
				{shopInfo.platList.map(platform => (
					<Option value={ platform } key={ platform } label={ PLAT_MAP[platform] }> {PLAT_MAP[platform]}</Option>
				))}
			</Select>
			<Select
				disabled={ disabledShop }
				placeholder="选择店铺"
				optionLabelProp="label"
				value={ shopId || shopValue }
				style={ { width: '160px' } }
				onChange={ handleShopChange }
				className="r-ml-8"
				getPopupContainer={ trigger => trigger?.parentElement }
				dropdownMatchSelectWidth={ false }
				{ ...reset }
			>
				{
					searchShop && (
						<div style={ { padding: '5px 12px' } }>
							<Input
								size="small"
								placeholder="搜索店铺"
								allowClear
								value={ searchInput }
								onClick={ (e) => e.stopPropagation() }
								onChange={ (e) => {
									e.stopPropagation();
									setSearchInput(e.target.value); 
								} }
							/>
						</div>
					)
				}
				
				{!isHideAllShop && <Option value="" key="allShop" label="所有店铺">所有店铺</Option>}
				
				{newShopList?.map(shop => (
					<Option disabled={ (shop.status == 0 && isDisableStop) || (shop.status == 2 && isDisableExpire) } value={ shop.sellerId } key={ `${shop.platform}_${shop.sellerId}` } label={ shop.sellerNick }>
						<div className="r-flex r-ai-c">
							<p style={ { width: 16, height: 16, marginRight: 2 } }>{getTradePlatformLabel(shop.platform, 16)}</p>
							<p style={ { height: 16, paddingTop: 1 } }>{ shop.sellerNick}</p>
						</div>
						{getShopStatus(shop).statusText && (
							<div className={ `${s['error-info']} r-mt-6` }>
								<a
									href={ getShopStatus(shop).href }
									target={ getShopStatus(shop).target }
									className={ s[`warning-text-${shop._status}`] }
									style={ { paddingLeft: '35px' } }
									rel="noreferrer"
								>
									{getShopStatus(shop).statusText}
								</a>
							</div>
						)}
					</Option>
				))}
			</Select>
		</div>
	);
};

export default observer(ShopSingleSelect);
