import { Checkbox, Row, Select, SelectProps } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { uniq } from 'lodash';
import { useToggle } from 'ahooks';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { observer } from 'mobx-react';
import { platform } from '@/types/schemas/common';
import { PLAT_MAP, PLAT_HAND, PLAT_OTHER } from '@/constants';
import { getAllPlats } from './shopListUtils';
import s from './index.module.scss';
import CustomizeSmallEmpty from '@/components/CustomizeRenderEmpty/CustomizeSmallEmpty';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import userStore from '@/stores/user';

export interface PlatformMultiSelectValue {
	plats?: platform[];
}

interface PlatformMultiSelectProps extends Omit<SelectProps<any>, 'onChange' | 'value'> {
	value?: PlatformMultiSelectValue;
	onChange?: (value: PlatformMultiSelectValue) => void;
	// 是否显示手工单平台
	isHasHandPlat?: boolean;
	// 是否发送埋点
	isSendPoint?: boolean;
	// 是否显示电子面单平台
	isHasEbillPlat?: boolean;
	// 背景高亮
	bgHighLight?: boolean;
	// 隐藏有赞平台
	hideYz?: boolean;
	// 隐藏得物平台
	hideDw?: boolean;
	// 只显示这些平台的选项
	filterOptions?: platform[];
	// 是否单选模式
	chooseOne?: boolean;
	// 自定义placeholder
	placeholder?: string;
	// 自定义宽度
	width?: number | string;
	dropdownStyle?: React.CSSProperties;
}

/**
 * 平台多选组件
 * 基于ShopMultiSelect组件提取出的平台选择功能
 * 支持多选、全选、过滤等功能
 */
const PlatformMultiSelect: React.FC<PlatformMultiSelectProps> = ({
	value = {},
	onChange,
	bgHighLight = false,
	isHasHandPlat = false,
	isHasEbillPlat = false,
	isSendPoint = false,
	hideYz = false,
	hideDw = false,
	chooseOne = false,
	filterOptions = [],
	placeholder = "选择平台",
	width = 160,
	dropdownStyle = {},
	...reset
}) => {
	const { shopList } = userStore;
	const { plats = [] } = value;
	const [platValue, setPlatValue] = useState<platform[]>(plats);
	const [checkAllPlat, setCheckAllPlat] = useToggle();
	const [platList, setPlatList] = useState<platform[]>([]);

	// 初始化平台值，处理hand平台转换为other平台的逻辑
	useEffect(() => {
		let _plats = [...plats];
		if (_plats.length > 0) {
			// 如果plats中有hand，将hand替换成other
			const hasPlatHand = _plats.indexOf(PLAT_HAND);
			if (hasPlatHand > -1) {
				_plats.splice(hasPlatHand, 1, PLAT_OTHER);
			}
			_plats = uniq(_plats);
			setPlatValue(_plats);
		}
	}, [value]);

	// 初始化平台列表数据
	useEffect(() => {
		const initPlatList = async() => {
			let allPlats = await getAllPlats(isHasHandPlat, isHasEbillPlat);
			
			// 根据filterOptions过滤平台
			if (filterOptions?.length) {
				allPlats = allPlats.filter(plat => filterOptions.includes(plat));
			}
			
			setPlatList(allPlats);
		};
		
		initPlatList();
	}, [isHasHandPlat, isHasEbillPlat, shopList, filterOptions]);

	// 监听选中平台数量变化，更新全选状态
	useEffect(() => {
		if (platValue?.length && platValue?.length === platList?.length) {
			setCheckAllPlat.setRight();
		} else {
			setCheckAllPlat.setLeft();
		}
	}, [platValue?.length, setCheckAllPlat, platList?.length]);

	/**
	 * 处理平台选择变化
	 * 支持单选和多选两种模式
	 */
	const handleChangePlat = (val: platform[]) => {
		// 发送埋点数据
		isSendPoint && sendPoint(Pointer.订单_订单打印_订单查询_查询选项_选择平台);
		
		let newValue: platform[];
		if (chooseOne) {
			// 单选模式：只保留最新选中的平台
			const nowChoose = val.filter(item => !platValue.includes(item));
			newValue = nowChoose;
		} else {
			// 多选模式：保留所有选中的平台
			newValue = val;
		}
		
		setPlatValue(newValue);
		onChange?.({
			plats: newValue,
		});
	};

	/**
	 * 处理全选/取消全选操作
	 */
	const handleSelectAllPlat = (e: CheckboxChangeEvent) => {
		isSendPoint && sendPoint(Pointer.订单_订单打印_订单查询_查询选项_选择平台);
		
		let newValue: platform[];
		const isChecked = e.target.checked;
		if (isChecked) {
			// 全选：选中所有平台
			newValue = [...platList];
		} else {
			// 取消全选：清空选择
			newValue = [];
		}
		
		setPlatValue(newValue);
		onChange?.({
			plats: newValue,
		});
	};

	/**
	 * 渲染选中标签的显示内容
	 * 根据选中状态显示不同的文本
	 */
	const renderTag = (props: any) => {
		if (checkAllPlat && !chooseOne) {
			// 全选状态下显示"所有平台"
			if (platValue?.[0] && platValue?.[0] === props.value) {
				return <span className="kdzs-select-selection-item">所有平台</span>;
			} else {
				return <></>;
			}
		} else if (platValue.length > 1) {
			// 多选状态下显示"多平台"
			if (platValue?.[0] && platValue?.[0] === props.value) {
				return <span className="kdzs-select-selection-item">多平台</span>;
			} else {
				return <></>;
			}
		} else {
			// 单选状态下显示具体平台名称
			return (
				<span 
					className="kdzs-select-selection-item" 
					title={ PLAT_MAP?.[props.value] || '未知' }
				>
					{PLAT_MAP?.[props.value] || '未知'}
				</span>
			);
		}
	};

	/**
	 * 渲染下拉框内容
	 * 包含全选选项和所有平台选项
	 */
	const renderDropdown = () => (
		<div className={ s['select-dropdown-wrap'] } style={ dropdownStyle }>
			{/* 全选选项 - 只在非单选模式下显示 */}
			{platList?.length ? (
				!chooseOne && (
					<Row className={ s['custom-select-item'] }>
						<Checkbox
							value="allPlat"
							className="r-w-full"
							onChange={ handleSelectAllPlat }
							checked={ checkAllPlat }
						>
							所有平台
						</Checkbox>
					</Row>
				)
			) : (
				<CustomizeSmallEmpty />
			)}
			
			{/* 平台选项列表 */}
			<Checkbox.Group 
				className="r-w-full" 
				value={ platValue } 
				onChange={ handleChangePlat }
			>
				{platList.map(platform => {
					// 根据配置隐藏特定平台
					if (
						(hideYz && PLAT_MAP?.[platform]?.includes('有赞')) 
						|| (hideDw && PLAT_MAP?.[platform]?.includes('得物'))
					) {
						return <React.Fragment key={ platform } />;
					}
					
					return (
						<Row key={ platform } className={ s['custom-select-item'] }>
							<Checkbox
								value={ platform }
								className="r-w-full"
							>
								{PLAT_MAP[platform] || '未知'}
							</Checkbox>
						</Row>
					);
				})}
			</Checkbox.Group>
		</div>
	);

	return (
		<Select
			mode="multiple"
			showArrow
			allowClear
			showSearch={ false }
			placeholder={ placeholder }
			optionLabelProp="label"
			style={ { width } }
			value={ platValue }
			className={ `${s['select-plat']} ${platValue?.length && bgHighLight ? 'high-light-bg' : ''}` }
			onChange={ handleChangePlat }
			tagRender={ renderTag }
			dropdownRender={ renderDropdown }
			{ ...reset }
		/>
	);
};

export default observer(PlatformMultiSelect); 