import { Checkbox, Input, Row, Select, SelectProps } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import _, { intersection, uniq } from 'lodash';
import { useSetState, useToggle } from 'ahooks';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { observer } from 'mobx-react';
import Icon from '@/components/Icon';
import { IShopInfo } from '@/types/schemas/user';
import { PLAT_MAP, getShopStatus, PLAT_HAND, PLAT_OTHER } from '@/constants';
import { platform } from '@/types/schemas/common';
import { getAllPlats, getAllShopMap, getShopsByPlat } from './shopListUtils';
import s from './index.module.scss';
import CustomizeSmallEmpty from '@/components/CustomizeRenderEmpty/CustomizeSmallEmpty';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import userStore from '@/stores/user';
import { tradeStore } from '@/stores';
import { local } from '@/libs/db';
import { getTradePlatformLabel } from '@/pages/Trade/utils';

const { Option } = Select;
export interface ShopMultiSelectValue {
	plats?: platform[];
	plat_sellerIds?: string[];
}

interface ShopInfo {
	platList: platform[]
	shopList: IShopInfo[]
}
interface ShopMultiSelectProps extends Omit< SelectProps<any>, 'onChange' | 'value' > {
	value?: ShopMultiSelectValue;
	onChange?: (value: ShopMultiSelectValue) => void;
	isHasHandPlat?: boolean; // 是否显示手工单平台
	isSendPoint?: boolean; // 是否发送埋点
	isHasEbillPlat?: boolean; // 是否显示电子面单平台
	direction?: any; // 布局方向
	bgHighLight?: any; // 背景高亮
	hideYz?: boolean // 是否隐藏有赞
	hideDw?: boolean // 是否隐藏得物
	filterOptions?: any[]; // 只显示这些平台的店铺
	chooseOne?:boolean;// 是否单选
	hidePlatforms?: platform[]; // 需要隐藏的平台列表, 不改变hideYz、hideDw的逻辑
	placeholderArray?: [string, string]; // 自定义placeholder数组 [平台placeholder, 店铺placeholder]
	disabledShopIds?: string[]; // 需要禁止选择的店铺ID列表，格式为 "platform_sellerId"
	disabledText?:string; // 禁用文案
	disabledPlatSelect?: boolean; // 是否禁用平台选择
}

const ShopMultiSelect: React.FC<ShopMultiSelectProps> = ({
	value = {},
	onChange,
	bgHighLight,
	isHasHandPlat = false,
	isHasEbillPlat = false,
	isSendPoint = false,
	direction = 'row', // column | row
	hideYz = false,
	chooseOne = false,
	hideDw = false,
	filterOptions = [],
	placeholderArray,
	hidePlatforms = [],
	disabledShopIds = [], // 新增参数，默认为空数组
	disabledText = '',
	disabledPlatSelect = false, // 是否禁用平台选择
	...reset
}) => {
	const { shopList, getShopToken } = userStore;
	const { tradeAdvanceSetObj } = tradeStore;
	const { plats = [], plat_sellerIds = [] } = value;
	const [shopValue, setShopValue] = useState<string[]>(plat_sellerIds);
	const [platValue, setPlatValue] = useState<platform[]>(plats);
	const [checkAllPlat, setCheckAllPlat] = useToggle();
	const [checkAllShop, setCheckAllShop] = useToggle();
	const shopValueRef = useRef<string[]>();
	const allShopMapRef = useRef<{ [K:string] : IShopInfo}>();
	const [showShortNick, setShowShortNick] = useState<boolean>(false);
	const [searchInput, setSearchInput] = useState('');

	// 获取placeholder，优先使用placeholderArray，否则使用默认值
	const [platPlaceholder, shopPlaceholder] = placeholderArray || ["选择平台", "选择店铺"];

	const [shopInfo, setShopInfo] = useSetState<ShopInfo>({
		platList: [],
		shopList: []
	});

	// 添加原始数据副本
	const [originalShopInfo, setOriginalShopInfo] = useSetState<ShopInfo>({
		platList: [],
		shopList: []
	});
	
	// 检查店铺是否被禁用
	const isShopDisabled = (shop: IShopInfo) => {
		const shopId = `${shop.platform}_${shop.sellerId}`;
		return disabledShopIds.includes(shopId);
	};

	// 检查平台是否需要隐藏的复合条件判断
	const shouldHidePlatform = (platform: platform) => {
	// 保持原有的单个平台隐藏逻辑
		if (hideYz && PLAT_MAP?.[platform]?.includes('有赞')) return true;
		if (hideDw && PLAT_MAP?.[platform]?.includes('得物')) return true;
		// 新增的通用平台隐藏逻辑
		if (hidePlatforms?.length && hidePlatforms.includes(platform)) return true;
		return false;
	};

	// 检查店铺是否需要隐藏的复合条件判断
	const shouldHideShop = (shop: IShopInfo) => {
	// 保持原有的有赞店铺隐藏逻辑
		if (hideYz && shop.platform.includes('yz')) return true;
		if (hideDw && shop.platform.includes('dw')) return true;
		// 新增的通用店铺隐藏逻辑
		if (hidePlatforms?.length && hidePlatforms.includes(shop.platform)) return true;
		return false;
	};

	useEffect(() => {
		// 如果 plats 中有hand 将hand替换成other
		let _plats = [...plats];
		if (_plats.length > 0 || plat_sellerIds.length > 0) {
			const hasPlatHand = _plats.indexOf(PLAT_HAND);
			if (hasPlatHand > -1) {
				_plats.splice(hasPlatHand, 1, PLAT_OTHER);
			}
			_plats = uniq(_plats);
			setPlatValue(_plats);
			setFieldsValue(_plats);
		}
	}, [value]);

	useEffect(() => {
		const _showShortNick = local.get('shopMultiSelect.showShortNick');
		setShowShortNick(_showShortNick);
	}, []);

	const setFieldsValue = (plats:any) => {
		getShopsByPlat({ plats, hasHand: isHasHandPlat, isHasEbillPlat }).then((newShopList) => {
			// 应用filterOptions过滤，确保只显示指定平台的店铺
			let filteredShopList = newShopList;
			if (filterOptions?.length) {
				filteredShopList = newShopList.filter(shop => filterOptions.includes(shop.platform));
			}
			
			setShopInfo({
				shopList: filteredShopList,
			});
			// 过滤掉被禁用的店铺
			const validShopIds = plat_sellerIds.filter(shopId => !disabledShopIds.includes(shopId));
			setShopValue(validShopIds);
		});
	};

	useMemo(async() => {
		allShopMapRef.current = await getAllShopMap({ hasHand: isHasHandPlat, isHasEbillPlat });
	}, [shopList]);

	// 初始化逻辑 - 只在 shopList 变化时执行
	useEffect(() => {
		const init = async() => {
			let platList = await getAllPlats(isHasHandPlat, isHasEbillPlat);
			let shopList = await getShopsByPlat({ plats, hasHand: isHasHandPlat, isHasEbillPlat });

			// 统一在数据阶段进行平台和店铺的过滤
			// 过滤平台数据
			platList = platList.filter(platform => !shouldHidePlatform(platform));
			// 过滤店铺数据
			shopList = shopList.filter(shop => !shouldHideShop(shop));

			// 保存原始数据副本
			setOriginalShopInfo({
				platList,
				shopList
			});

			setShopInfo({
				platList,
				shopList
			});
		};
		init();
	}, [shopList]);

	// 动态过滤逻辑 - 基于原始数据副本进行过滤
	useEffect(() => {
		if (originalShopInfo?.platList?.length > 0 || originalShopInfo?.shopList?.length > 0) {
			let filteredPlatList = originalShopInfo?.platList || [];
			let filteredShopList = originalShopInfo?.shopList || [];
			
			// 应用 filterOptions 过滤
			if (filterOptions?.length) {
				filteredPlatList = originalShopInfo?.platList?.filter(plat => filterOptions.includes(plat)) || [];
				filteredShopList = originalShopInfo?.shopList?.filter(shop => filterOptions.includes(shop.platform)) || [];
			}
			
			setShopInfo({
				platList: filteredPlatList,
				shopList: filteredShopList
			});
		}
	}, [filterOptions?.length, originalShopInfo]);

	useMemo(() => {
		shopValueRef.current = shopValue ? [...shopValue] : [];
	}, [shopValue]);


	const changePlats = (plats:platform[]) => {
		getShopsByPlat({ plats, hasHand: isHasHandPlat, isHasEbillPlat }).then((newShopList) => {
			// 应用 filterOptions 过滤
			let filteredShopList = newShopList;
			if (filterOptions?.length) {
				filteredShopList = newShopList.filter(shop => filterOptions.includes(shop.platform));
			}
			
			// 在changePlats中也需要应用店铺过滤逻辑
			filteredShopList = filteredShopList.filter(shop => !shouldHideShop(shop));

			setShopInfo({
				shopList: filteredShopList,
			});

			// 更新选中的店铺， 如果删除某个平台，需要删除该平台已选中的店铺
			const newShopValue = intersection(shopValueRef.current, filteredShopList.map(item => `${item.platform}_${item.sellerId}`))
				.filter(shopId => !disabledShopIds.includes(shopId)); // 过滤掉被禁用的店铺
			setShopValue(newShopValue);

			onChange?.({
				plats,
				plat_sellerIds: newShopValue,
			});
		});
	};

	useEffect(() => {
		if (platValue?.length && platValue?.length === shopInfo?.platList?.length) {
			setCheckAllPlat.setRight();
		} else {
			setCheckAllPlat.setLeft();
		}
	}, [platValue?.length, setCheckAllPlat, shopInfo?.platList?.length]);


	const handleChangePlat = (val:platform[]) => {
		isSendPoint && sendPoint(Pointer.订单_订单打印_订单查询_查询选项_选择平台);
		const nowChoose = val.filter(item => !platValue.includes(item));
		setPlatValue(chooseOne ? nowChoose : val);
		changePlats(chooseOne ? nowChoose : val);
	};

	const handleSelectAllPlat = (e: CheckboxChangeEvent) => {
		isSendPoint && sendPoint(Pointer.订单_订单打印_订单查询_查询选项_选择平台);
		let newValue:platform[];
		const isChecked = e.target.checked;
		if (isChecked) {
			newValue = [...shopInfo.platList];
		} else {
			newValue = [];
		}
		setPlatValue(newValue);
		changePlats(newValue);
	};

	useEffect(() => {
		// 检查是否所有非禁用的店铺都被选中
		const nonDisabledShops = shopInfo.shopList.filter(shop => !isShopDisabled(shop));
		const nonDisabledShopIds = nonDisabledShops.map(shop => `${shop.platform}_${shop.sellerId}`);
		
		if (shopValue?.length && shopValue?.length === nonDisabledShopIds.length 
			&& nonDisabledShopIds.every(id => shopValue.includes(id))) {
			setCheckAllShop.setRight();
		} else {
			setCheckAllShop.setLeft();
		}
	}, [setCheckAllShop, shopInfo?.shopList, shopValue, disabledShopIds]);


	const handleSelectAllShop = (e: CheckboxChangeEvent) => {
		isSendPoint && sendPoint(Pointer.订单_订单打印_订单查询_查询选项_选择店铺);
		let newValue: string[];
		const isChecked = e.target.checked;
		if (isChecked) {
			// 全选时排除被禁用的店铺
			newValue = shopInfo.shopList
				.filter(shop => !isShopDisabled(shop))
				.map(item => `${item.platform}_${item.sellerId}`);
		} else {
			newValue = [];
		}
		setShopValue(newValue);
		onChange?.({
			...value,
			plat_sellerIds: newValue,
		});
	};

	const handleChangeShop = (val: string[]) => {
		isSendPoint && sendPoint(Pointer.订单_订单打印_订单查询_查询选项_选择店铺);
		// 过滤掉被禁用的店铺
		const filteredVal = val.filter(shopId => !disabledShopIds.includes(shopId));
		const nowChoose = filteredVal.filter(item => !shopValue.includes(item));
		setShopValue(chooseOne ? nowChoose : filteredVal);
		onChange?.({
			...value,
			plat_sellerIds: chooseOne ? nowChoose : filteredVal,
		});
	};

	const showShortNickChange = () => {
		const val = !showShortNick;
		setShowShortNick(val);
		local.set('shopMultiSelect.showShortNick', val);
	};
	// console.log('platValue', platValue);

	/**
	 * 只有开启抖音昵称解密的时候，才请求token
	 */
	useEffect(() => {
		if (tradeAdvanceSetObj?.printSetExpandDTO?.showDyNickName && shopList?.length) {
			getShopToken(shopList);
		}
	}, [shopList, tradeAdvanceSetObj?.printSetExpandDTO?.showDyNickName]);

	return (
		<div className={ s['shop-multi-select-wrap'] } style={ { display: "flex", alignItems: direction === 'column' ? 'flex-start' : "center", flexDirection: direction } } >
			<Select
				mode="multiple"
				showArrow
				allowClear
				showSearch={ false }
				placeholder={ platPlaceholder }
				optionLabelProp="label"
				style={ { width: '160px' } }
				value={ platValue }
				className={ `${s['select-plat']} ${platValue?.length && bgHighLight ? 'high-light-bg' : ''} ` }
				onChange={ handleChangePlat }
				disabled={ disabledPlatSelect }
				tagRender={ (props) => {
					if (checkAllPlat && !chooseOne) {
						if (platValue?.[0] && platValue?.[0] === props.value) {
							return <span className="kdzs-select-selection-item">所有平台</span>;
						} else {
							return <></>;
						}
					} else if (platValue.length > 1) {
						if (platValue?.[0] && platValue?.[0] === props.value) {
							return <span className="kdzs-select-selection-item">多平台</span>;
						} else {
							return <></>;
						}
					} else {
						return (
							<span className="kdzs-select-selection-item" title={ PLAT_MAP?.[props.value] || '未知' }>{PLAT_MAP?.[props.value] || '未知'}</span>
						);
					}
				} }
				dropdownRender={ menu => (
					<div className={ s['select-dropdown-wrap'] }>
						{
							shopInfo?.platList?.length ? (
								!chooseOne && (
									<Row className={ s['custom-select-item'] }>
										<Checkbox
											value="allPlat"
											className="r-w-full"
											onChange={ handleSelectAllPlat }
											checked={ checkAllPlat }
										>所有平台
										</Checkbox>
									</Row>
								)
							) : <CustomizeSmallEmpty />
						}
						<Checkbox.Group className="r-w-full" value={ platValue } onChange={ handleChangePlat }>
							{shopInfo.platList.map(platform => (
								<Row key={ platform } className={ s['custom-select-item'] }>
									<Checkbox
										value={ platform }
										className="r-w-full"
									>  {PLAT_MAP[platform] || '未知'}
									</Checkbox>
								</Row>
							))}
						</Checkbox.Group>
					</div>
				) }
				{ ...reset }
			/>
			{
				direction === 'column' ? null : (
					<span style={ { display: 'inline-block', width: '10px', textAlign: "center" } }>-</span>
				)
			}
			<Select
				mode="multiple"
				showArrow
				allowClear
				showSearch={ false }
				placeholder={ shopPlaceholder }
				style={ { width: '160px' } }
				optionLabelProp="label"
				value={ shopValue }
				onChange={ handleChangeShop }
				className={ `${shopValue?.length && bgHighLight ? 'high-light-bg' : ''} ` }
				tagRender={ (props) => {
					if (checkAllShop && !chooseOne) {
						if (shopValue?.[0] && shopValue?.[0] === props.value) {
							return <span className="kdzs-select-selection-item" >所有店铺</span>;
						} else {
							return <></>;
						}
					} else if (shopValue.length > 1) {
						if (shopValue?.[0] && shopValue?.[0] === props.value) {
							return <span className="kdzs-select-selection-item" >多店铺</span>;
						} else {
							return <></>;
						}
					} else {
						let value = null;
						if (showShortNick) {
							value = allShopMapRef?.current && allShopMapRef.current[props.value]?.sellerAbbreviation;
						} else {
							value = allShopMapRef?.current && allShopMapRef.current[props.value]?.sellerNick;
						}
						return (
							<span title={ value } className="kdzs-select-selection-item" > {value}</span>
						);
					}
				} }
				dropdownRender={ menu => (
					<div className={ s['select-dropdown-wrap'] }>
						<div style={ { padding: '5px 12px' } }>
							<Input
								placeholder="搜索店铺"
								allowClear
								value={ searchInput }
								onChange={ (e) => { setSearchInput(e.target.value); } }
							/>
						</div>
						{ !searchInput ? shopInfo?.shopList?.length ? (
							!chooseOne && (
								<Row className={ `${s['custom-select-item']} r-flex r-ai-c r-jc-sb ` }>
									<Checkbox
										value="allPlat"
										className="r-h-full "
										onChange={ handleSelectAllShop }
										checked={ checkAllShop }
									>
										<p className="r-pt-6">所有店铺</p>
									</Checkbox>
									<p className="r-fs-12 r-c-primary r-pt-8" onClick={ showShortNickChange }>
										{ showShortNick ? '显示店铺名称' : '显示店铺简称'}
										<Icon className="r-ml-4" size={ 12 } type="qiehuan" />
									</p>
								</Row>
							)
						) : <CustomizeSmallEmpty /> : ''}
						<Checkbox.Group className="r-w-full" value={ shopValue } onChange={ handleChangeShop }>
							{shopInfo?.shopList.map(shop => {
								const shopId = `${shop.platform}_${shop.sellerId}`;
								const disabled = isShopDisabled(shop);
								
								return (
									<div
										key={ shopId }
										hidden={ searchInput && !(showShortNick ? shop.sellerAbbreviation : shop.sellerNick).includes(searchInput) }
									>
										<Row className={ `${s['custom-select-item']} r-flex r-ai-c` }>
											<Checkbox
												value={ shopId }
												className="r-w-full r-flex r-ai-c"
												disabled={ disabled }
											>
												<div className="r-flex r-ai-c">
													<p style={ { width: 16, height: 16, marginRight: 2 } }>{getTradePlatformLabel(shop.platform, 16)}</p>
													<p style={ { height: 16, paddingTop: 1 } }>
														{ showShortNick ? shop.sellerAbbreviation : shop.sellerNick}
														{ disabled && disabledText && <span style={ { color: '#FD8204', marginLeft: 4 } }>({disabledText})</span> }
													</p>
												</div>
											</Checkbox>
										</Row>
										{
											getShopStatus(shop).statusText && (
												<div className={ s['error-info'] }>
													<a
														href={ getShopStatus(shop).href }
														target={ getShopStatus(shop).target }
														className={ s[`warning-text-${shop._status}`] }
														style={ { paddingLeft: '35px' } }
														rel="noreferrer"
													>
														{getShopStatus(shop).statusText}
													</a>
												</div>
											)
										}
									</div>
								);
							})}
						</Checkbox.Group>
					</div>
				) }
				{ ...reset }
			/>
		</div>
	);
};

export default observer(ShopMultiSelect);
