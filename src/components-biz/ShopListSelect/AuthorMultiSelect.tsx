import { Checkbox, Input, Row, Select, SelectProps } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { useSetState } from 'ahooks';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { observer } from 'mobx-react';
import userStore from '@/stores/user';
import history from "@/utils/history";
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import s from './index.module.scss';

const { Option } = Select;

export interface AuthorMultiSelectValue {
	authorList?: Array<{
		platform?: string;
		authorId?: string;
		authorName?: string;
	}>;
}

interface AuthorMultiSelectProps extends Omit<SelectProps<any>, 'onChange' | 'value'> {
	value?: AuthorMultiSelectValue;
	onChange?: (value: AuthorMultiSelectValue) => void;
	bgHighLight?: boolean; // 背景高亮
	direction?: any; // 布局方向
	placeholderArray?: [string]; // 自定义placeholder数组
	disabledAuthorIds?: string[]; // 需要禁止选择的达人ID列表，格式为 "platform_authorId"
	dropdownStyle?: React.CSSProperties;
	dropdownOpenChange?: (open: boolean) => void;
	chooseOne?: boolean; // 是否单选
	filterPlatforms?: string[]; // 只显示这些平台的达人
}

interface AuthorInfo {
	id?: number;
	platform?: string;
	authorId?: string;
	authorName?: string;
	liveStatus?: number;
}

const AuthorMultiSelect: React.FC<AuthorMultiSelectProps> = ({
	value = {},
	onChange,
	bgHighLight = false,
	direction = 'row',
	placeholderArray,
	disabledAuthorIds = [],
	dropdownStyle = {},
	dropdownOpenChange,
	chooseOne = false, // 新增参数，默认为false
	filterPlatforms = [], // 新增参数
	...reset
}) => {
	const { authorList = [] } = value;
	const [authorValue, setAuthorValue] = useState<string[]>([]);
	const [checkAllAuthor, setCheckAllAuthor] = useState(false);
	const [searchInput, setSearchInput] = useState('');
	const [dropdownOpen, setDropdownOpen] = useState(false);

	const { authorList: authorListStore } = userStore;

	// 获取placeholder
	const [authorPlaceholder] = placeholderArray || ["选择达人"];

	const [authorInfo, setAuthorInfo] = useSetState<{
		authorList: AuthorInfo[]
	}>({
		authorList: []
	});

	// 检查达人是否被禁用
	const isAuthorDisabled = (author: AuthorInfo) => {
		const authorKey = `${author.platform}_${author.authorId}`;
		return disabledAuthorIds.includes(authorKey) || author?.liveStatus === 1; // 1 表示正在直播
	};

	// 使用 useMemo 计算过滤后的达人列表，避免循环
	const filteredAuthorList = useMemo(() => {
		let result = authorListStore || [];
		
		// 如果设置了过滤平台，则只显示对应平台的达人
		if (filterPlatforms?.length > 0) {
			result = result.filter(author => filterPlatforms.includes(author.platform));
		}
		
		return result;
	}, [authorListStore, filterPlatforms]);

	// 初始化数据 
	useEffect(() => {
		setAuthorInfo({
			authorList: filteredAuthorList
		});
	}, [filteredAuthorList]);

	// 同步外部value到内部状态
	useEffect(() => {
		if (authorList?.length) {
			const authorKeys = authorList.map(author => `${author.platform}_${author.authorId}`);
			// 只有当新的 authorKeys 与当前的 authorValue 不同时才更新
			setAuthorValue(prev => {
				if (prev.length !== authorKeys.length 
					|| !prev.every((key, index) => key === authorKeys[index])) {
					return authorKeys;
				}
				return prev;
			});
		} else {
			setAuthorValue(prev => (prev.length === 0 ? prev : []));
		}
	}, [authorList]);

	// 检查是否全选
	useEffect(() => {
		// 单选模式下不检查全选状态
		if (chooseOne) {
			setCheckAllAuthor(false);
			return;
		}
		
		const availableAuthors = authorInfo.authorList.filter(author => !isAuthorDisabled(author));
		setCheckAllAuthor(
			availableAuthors.length > 0 
			&& authorValue.length === availableAuthors.length
		);
	}, [authorValue.length, authorInfo.authorList, disabledAuthorIds, chooseOne]);

	// 全选/取消全选
	const handleSelectAllAuthor = (e: CheckboxChangeEvent) => {
		// 单选模式下不显示全选功能
		if (chooseOne) return;
		
		const isChecked = e.target.checked;
		let newValue: string[];
		
		if (isChecked) {
			// 全选时排除被禁用的达人
			newValue = authorInfo.authorList
				.filter(author => !isAuthorDisabled(author))
				.map(author => `${author.platform}_${author.authorId}`);
		} else {
			newValue = [];
		}
		
		setAuthorValue(newValue);
		
		// 转换为外部格式
		const selectedAuthors = authorInfo.authorList
			.filter(author => newValue.includes(`${author.platform}_${author.authorId}`))
			.map(author => ({
				platform: author.platform,
				authorId: author.authorId,
				authorName: author.authorName
			}));

		onChange?.({
			authorList: selectedAuthors
		});
	};

	// 选择达人
	const handleChangeAuthor = (val: string[]) => {
		// 过滤掉被禁用的达人
		const filteredVal = val.filter(authorKey => !disabledAuthorIds.includes(authorKey));
		
		// 单选模式下的逻辑
		if (chooseOne) {
			const nowChoose = filteredVal.filter(item => !authorValue.includes(item));
			setAuthorValue(nowChoose);
			
			// 转换为外部格式
			const selectedAuthors = authorInfo.authorList
				.filter(author => nowChoose.includes(`${author.platform}_${author.authorId}`))
				.map(author => ({
					platform: author.platform,
					authorId: author.authorId,
					authorName: author.authorName
				}));

			onChange?.({
				authorList: selectedAuthors
			});
		} else {
			// 多选模式下的原有逻辑
			setAuthorValue(filteredVal);

			// 转换为外部格式
			const selectedAuthors = authorInfo.authorList
				.filter(author => filteredVal.includes(`${author.platform}_${author.authorId}`))
				.map(author => ({
					platform: author.platform,
					authorId: author.authorId,
					authorName: author.authorName
				}));

			onChange?.({
				authorList: selectedAuthors
			});
		}
	};

	// 获取达人状态文本
	const getAuthorStatusText = (author: AuthorInfo) => {
		if (author.liveStatus === 1) {
			return '(直播中)';
		}
		return '';
	};

	// 获取达人显示名称
	const getAuthorDisplayName = (author: AuthorInfo) => {
		return author?.authorName || '未知达人';
	};

	// 跳转到达人管理页面
	const goToAuthorManage = () => {
		// 先关闭下拉选择器
		setDropdownOpen(false);
		// 延迟跳转，确保下拉框完全关闭
		setTimeout(() => {
			history.push('/livestreamerManage');
		}, 100);
	};

	// 自定义空状态组件
	const EmptyAuthorTip = () => (
		<div className="r-flex r-ai-c r-jc-c" style={ { margin: '80px 0' } }>
			<div className="r-c-black45 r-fs-14">
				请前往
				<span 
					className="r-c-primary r-pointer r-ml-4 r-mr-4" 
					onClick={ goToAuthorManage }
				>
					「达人管理」
				</span>
				维护达人信息
			</div>
		</div>
	);

	const handleDropdownVisibleChange = (open: boolean) => {
		setDropdownOpen(open);
		dropdownOpenChange?.(open);
	};

	return (
		<div className={ s['author-multi-select-wrap'] } style={ { display: "flex", alignItems: direction === 'column' ? 'flex-start' : "center", flexDirection: direction } }>
			<Select
				mode="multiple"
				showArrow
				allowClear
				showSearch={ false }
				placeholder={ authorPlaceholder }
				style={ { width: '328px' } }
				optionLabelProp="label"
				value={ authorValue }
				onChange={ handleChangeAuthor }
				open={ dropdownOpen }
				onDropdownVisibleChange={ handleDropdownVisibleChange }
				className={ `${authorValue?.length && bgHighLight ? 'high-light-bg' : ''}` }
				tagRender={ (props) => {
					if (checkAllAuthor && !chooseOne) {
						if (authorValue?.[0] && authorValue?.[0] === props.value) {
							return <span className="kdzs-select-selection-item">所有达人</span>;
						} else {
							return <></>;
						}
					} else if (authorValue.length > 1 && !chooseOne) {
						if (authorValue?.[0] && authorValue?.[0] === props.value) {
							return <span className="kdzs-select-selection-item">多达人</span>;
						} else {
							return <></>;
						}
					} else {
						const author = authorInfo.authorList.find(a => `${a.platform}_${a.authorId}` === props.value);
						const displayName = getAuthorDisplayName(author);
						return (
							<span className="kdzs-select-selection-item" title={ displayName }>
								{displayName}
							</span>
						);
					}
				} }
				dropdownRender={ menu => (
					<div className={ s['select-dropdown-wrap'] } style={ dropdownStyle }>
						<div style={ { padding: '5px 12px' } }>
							<Input
								placeholder="搜索达人名称/达人ID"
								allowClear
								value={ searchInput }
								onChange={ (e) => { setSearchInput(e.target.value); } }
							/>
						</div>
						
						{!searchInput ? authorInfo?.authorList?.length ? (
							!chooseOne && (
								<Row className={ `${s['custom-select-item']} r-flex r-ai-c r-jc-sb` }>
									<Checkbox
										value="allAuthor"
										className="r-w-full"
										onChange={ handleSelectAllAuthor }
										checked={ checkAllAuthor }
									>
										<p className="r-pt-6">所有达人</p>
									</Checkbox>
								</Row>
							)
						) : <EmptyAuthorTip /> : ''}
						
						<Checkbox.Group className="r-w-full" value={ authorValue } onChange={ handleChangeAuthor }>
							{authorInfo?.authorList.map(author => {
								const authorKey = `${author.platform}_${author.authorId}`;
								const disabled = isAuthorDisabled(author);
								const displayName = getAuthorDisplayName(author);
								const statusText = getAuthorStatusText(author);
								
								return (
									<div
										key={ authorKey }
										hidden={ searchInput && (!displayName.includes(searchInput) && !author.authorId?.includes(searchInput)) }
									>
										<Row className={ `${s['custom-select-item']} r-flex r-ai-c` }>
											<Checkbox
												value={ authorKey }
												className="r-w-full r-flex r-ai-c"
												disabled={ disabled }
											>
												<div className="r-flex r-ai-c r-pt-4">
													<PlatformIcon platform={ author.platform } fontSize={ 16 } />
													<span>{displayName}</span>
													<span className="r-c-black45 r-ml-4">({author.authorId})</span>
													{statusText && <span style={ { color: '#FD8204', marginLeft: 4 } }>{statusText}</span>}
												</div>
											</Checkbox>
										</Row>
									</div>
								);
							})}
						</Checkbox.Group>
					</div>
				) }
				{ ...reset }
			/>
		</div>
	);
};

export default observer(AuthorMultiSelect);