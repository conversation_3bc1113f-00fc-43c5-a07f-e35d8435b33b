import React, { Children } from "react";
import {
	USER_TYPE,
	stockVersion,
	zeroStockVersion,
	USER_TYPE_MENU,
	AUTH_USER_TYPE
} from "@/constants";
import Pointer from "@/utils/pointTrack/constants";
import { MenuBean, MenuType } from "./menuBean";

// 用APP_ROOT判断环境是否为测试环境，不再需要在测试分支修改子账号权限id
const isErpTest = process.env?.APP_ROOT?.includes('erptest');
console.log('%c [ 当前环境是否测试环境 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', isErpTest);

// 注意有些权限id 测试环境和线上不一样
export const MenuIdMap = {
	首页: "1",
	订单: "2",
	订单_订单打印: "11",
	订单_后置打印: "153",
	订单_自动发货: "50",
	订单_商品标签: "46",
	订单_验货发货: "75",
	订单_包裹称重: '117',
	订单_直播标签: isErpTest ? '197' : '192', // 测试197  线上192
	订单_达人管理: isErpTest ? '2043' : '2043', // 测试2043  线上2043
	订单_直播打印: isErpTest ? '2042' : '2042', // 直播打印分组
	订单_直播订单明细: isErpTest ? '2044' : '2044', // 直播订单明细
	订单_扣号标签明细: isErpTest ? '2060' : '2060', // 扣号标签明细
	订单_跨境托管: "60",
	订单_订单策略: "207",
	订单_赠品策略: "208",
	订单_订单异常预警: "77",
	库存: "3",
	库存_库存看板: "12",
	库存_到货入库: "13",
	库存_采购入库: "14",
	库存_扫描入库: "72",
	库存_手动出库: "49",
	库存_库存预警阈值: "15",
	库存_出入库日志: "16",
	库存_库存初始化: "17",
	库存_库存同步: "18",
	库存_库存同步日志: "19",
	库存_按货品盘库: "38",
	库存_组合货品打包: "40",
	库存_波次管理: isErpTest ? '209' : '210',
	库存_波次打印: isErpTest ? '210' : '211',
	采购: "4",
	采购_采购单: "20",
	采购_供应商: "21",
	售后: "5",
	售后_售后管理: "22",
	售后_售后扫描登记: "54",
	售后_售后自动化: isErpTest ? '198' : '202', // 测试198  线上202
	售后_退货地址: "23",
	售后_快递拦截: "130",
	商品: "6",
	商品_平台商品管理: "53",
	商品_货品档案: "24",
	商品_组合货品档案: "39",
	商品_货品与商品关系: "25",
	分销: "65",
	分销_我的分销商: "66",
	分销_代发商品管理: "73",
	分销_分销结算_供应商: "74",
	分销_我的供应商: "67",
	分销_分销邀请返现: "149",
	分销_分销结算_分销商: "151",
	分销_结算价设置: "150",
	分销_分销设置: "152",
	报表: "7",
	报表_实时销售大屏: "68",
	报表_销售分析: "129",
	报表_售后分析: isErpTest ? '148' : '131', // 测试148 线上131
	报表_打印记录: "27",
	报表_发货记录: "28",
	报表_底单查询: "29",
	报表_买家售后统计: "30",
	报表_快递对账: "41",
	报表_销售毛利润报表: "42",
	报表_进销存报表: "2038",
	报表_货品出入库账单: "44",
	报表_货品成本报表: "45",
	报表_物流预警: "61",
	备货单: "26",
	店铺: "9",
	档口: "55",
	档口_备货单标签: "56",
	档口_扫描打印: "47",
	档口_爆款标签: "58",
	档口_退款标签重匹配: "71",
	档口_标签点货: "51",
	档口_标签对账: "52",
	档口_标签设置: "48",
	档口_档口设置: "57",
	档口_档口拿货路线: isErpTest ? '154' : '191', // 测试154  线上191
	设置: "10",
	设置_系统设置: "31",
	设置_账号与权限: "32",
	设置_常用发件人: "33",
	设置_常用收件人: "34",
	设置_系统设置二级: "35",
	设置_系统设置二级_用户个性化设置: "36",

	设置_常用备注短语: "37",
	设置_快递可达设置: "62",
	设置_智选快递: "43",
	设置_运费模板设置: "76",
	设置_系统日志: "59",
	设置_仓库: "8",
	设置_平台授权费返还: "63",
	设置_监控设备管理: isErpTest ? "2041" : "2041",
	设置_客户管理及黑名单: "203",
	公共: "69",
	公共_下载中心: "70",
	面单管理: "201",
	商户管理: "99999",
	商户管理_商户查询: "99999",
	铺货配置: '2039',
	商品铺货日志: '2040'
};
	
// 导航菜单（权限控制是否显示菜单项），注意src/routers/PrivateRoute能控制导航菜单的手动跳转权限
export const MenuData: MenuBean[] = [
	{
		name: "首页",
		path: "/",
		id: MenuIdMap["首页"],
		type: MenuType.Item,
		point: Pointer.顶部菜单栏_首页,
		authorize: [
			AUTH_USER_TYPE.常规版零库存版,
			AUTH_USER_TYPE.常规版库存版,
			AUTH_USER_TYPE.默认常规版零库存版,
			AUTH_USER_TYPE.默认常规版库存版
		],
	},
	{
		name: "订单",
		path: "",
		id: MenuIdMap["订单"],
		icon: "dingdandayin",
		children: [
			{
				name: "订单操作",
				children: [
					{
						name: "订单打印",
						icon: "dingdandayin",
						path: "/trade/index",
						id: MenuIdMap["订单_订单打印"],
						point: Pointer["顶部菜单栏_订单_订单打印"],
						desc: {
							name: "订单打印",
							content:
								"聚合多平台的所有店铺订单，可以查询和批量处理订单（打印与发货）",
							icon: "dingdandayin",
						},
						isHot: true
					},
					{
						name: "后置打印",
						icon: "saomiaodayin",
						path: "/trade/postPrint",
						id: MenuIdMap["订单_后置打印"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["后置打印_导航栏点击"],
						desc: {
							name: "后置打印",
							content:
								"适用于先备货配货后打印快递单的场景，在此页面可扫描商品标签/吊牌、货品标签、发货单后核验订单和商品后打印快递单发货",
							icon: "saomiaodayin",
						},
					},
					{
						name: "自动发货",
						icon: "yufahuo",
						path: "/preShip",
						id: MenuIdMap["订单_自动发货"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["顶部菜单栏_订单_自动发货"],
						desc: {
							name: "自动发货",
							content:
								"商家根据实际发货需要，将已打印的订单添加入自动发货,系统可设置定时自动发货或根据运单揽收物流自动进行平台发货",
							icon: "yufahuo",
						},
					},
					{
						name: "验货发货",
						icon: "saomiaofahuo",
						path: "/inspectSend",
						id: MenuIdMap.订单_验货发货,
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["顶部菜单栏_订单_验货发货"],
						desc: {
							name: "验货发货",
							content:
								"通过扫描订单号、商品条码对订单进行验货、验单发货，保障发货时订单与商品精准匹配发货。",
							icon: "saomiaofahuo",
						},
					},
					{
						name: "包裹称重",
						icon: "chengzhongfahuo",
						path: "/packWeight",
						id: MenuIdMap.订单_包裹称重,
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["包裹称重_页面展现"],
						desc: {
							name: "包裹称重",
							content:
								"通过称重器记录并对比包裹的重量，实现对订单发货前的包裹检查，减少错发、漏发和少发等情况。",
							icon: "chengzhongfahuo",
						},
					},
					{
						name: "跨境托管",
						icon: "kuajingtuoguan",
						path: "/trade/foreignManage",
						id: MenuIdMap["订单_跨境托管"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["导航栏_订单_商品标签"],
						desc: {
							name: "跨境托管",
							content:
								"平台推出跨境全托管业务，商家开通后即享跨境电商流量扶持；收到跨境全托管发货单后，只需要发货至中国境内集运仓，无需承担跨境段运费商家查询和管理生成的商品标签，并可以对商品标签进行打印发货",
							icon: "kuajingtuoguan",
						},
					},
					{
						name: "订单策略",
						icon: "zidongzengpincelve",
						path: "/trade/strategy",
						id: MenuIdMap["订单_订单策略"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["导航栏_订单_订单策略"],
						desc: {
							name: "订单策略",
							content:
								"",
							icon: "zidongzengpincelve",
						},
					}
				],
			},
			{
				name: "直播管理",
				isAddRedFont: '功能升级',
				children: [
					{
						name: "直播标签",
						icon: "zhibobiaoqian",
						path: "/liveTag",
						id: MenuIdMap.订单_直播标签,
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["直播标签_页面展现"],
						desc: {
							name: "直播标签",
							content:
								"适用于直播场景中(如盲盒直播)，明确当前实时订单对应具体的线下商品，方便直播结束后线下发货",
							icon: "zhibobiaoqian",
						},
					},
					{
						name: "直播订单明细",
						icon: "zhibodingdanmingxi",
						path: "/liveLog",
						id: MenuIdMap.订单_直播订单明细,
					},
					{
						name: "扣号标签明细",
						icon: "kouhaobiaoqianmingxi",
						path: "/takeNumberLiveLog",
						id: MenuIdMap.订单_扣号标签明细,
					},
					{
						name: "达人管理",
						icon: "darenguanli",
						path: "/livestreamerManage",
						id: MenuIdMap.订单_达人管理,
					}
				]
			},
			{
				name: '波次拣货',
				children: [
					{
						name: "波次管理",
						id: MenuIdMap["库存_波次管理"],
						path: "/warehouse/wave",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "bociguanli",
						desc: {
							name: "波次管理",
							content: "管理波次相关信息",
							icon: "bociguanli",
						}
					}
				],
			},
			{
				name: "订单查看",
				children: [
					{
						name: "订单异常预警",
						icon: "wuliuyujing",
						path: "/trade/exceptionWarn",
						id: MenuIdMap.订单_订单异常预警,
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["订单异常预警_页面展现_展现次数"],
						desc: {
							name: "订单异常预警",
							content:
								"在打印后、揽件前等环节，针对产生售后、改地址、改备注等订单异常行为进行监控预警",
							icon: "wuliuyujing",
						},
					},
					{
						name: "面单管理",
						id: MenuIdMap["面单管理"],
						icon: "beihuodan",
						path: "/settings/LabelManage",
						type: MenuType.Item,
						// point: Pointer.顶部菜单栏_设置_常用备注短语,
						desc: {
							icon: "beihuodan",
							name: "面单管理",
							content: "分享面单记录",
						},
					},
					{
						name: "打印记录",
						id: MenuIdMap["报表_打印记录"],
						path: "/report/printLog",
						icon: "dayinjilu",
						desc: {
							name: "打印记录",
							icon: "dayinjilu",
							content: "记录订单打印的日志，方便查询",
						},
						point: Pointer.顶部菜单栏_报表_打印记录,
					},
					{
						name: "发货记录",
						id: MenuIdMap["报表_发货记录"],
						path: "/report/shipLog",
						icon: "fahuojilu",
						point: Pointer.顶部菜单栏_报表_发货记录,
						desc: {
							name: "发货记录",
							icon: "fahuojilu",
							content: "记录订单发货的日志，方便查询",
						},
					},
					{
						name: "底单查询",
						id: MenuIdMap["报表_底单查询"],
						path: "/report/kddLog",
						icon: "didanchaxun",
						point: Pointer.顶部菜单栏_报表_底单查询,
						desc: {
							name: "底单查询",
							icon: "didanchaxun",
							content:
								"记录绑定店铺快递单号使用记录，支持多种条件组合查询、下载使用记录以及批量重打和回收单号操作。",
						},
					}
				],
			}
		],
	},
	{
		name: "售后",
		path: "",
		id: MenuIdMap["售后"],
		authorize: [
			AUTH_USER_TYPE.常规版零库存版,
			AUTH_USER_TYPE.常规版库存版,
			AUTH_USER_TYPE.分销商版零库存版,
			AUTH_USER_TYPE.默认常规版零库存版,
			AUTH_USER_TYPE.默认常规版库存版,
			AUTH_USER_TYPE.供应商版库存版
		],
		children: [
			{
				name: "售后",
				children: [
					{
						name: "售后管理",
						id: MenuIdMap["售后_售后管理"],
						path: "/aftersale/trade",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.分销商版零库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版,
							AUTH_USER_TYPE.供应商版库存版
						],
						icon: "shouhoudingdan",
						point: Pointer.顶部菜单栏_售后_售后管理,
						desc: {
							icon: "shouhoudingdan",
							name: "售后管理",
							content:
								"对售后单进行退货处理，包括审核操作、确认退货、退货入库",
						},
					},
					{
						name: "快递拦截",
						id: MenuIdMap["售后_快递拦截"],
						path: "/aftersale/expressIntercept",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "kuaidilanjie",
						point: Pointer.快递拦截_页面访问,
						desc: {
							icon: "kuaidilanjie",
							name: "快递拦截",
							content:
								"添加需要快递公司拦截的包裹单号，并记录拦截情况，用于和快递核对",
						},
					},
					{
						name: "售后自动化",
						id: MenuIdMap["售后_售后自动化"],
						path: "/aftersale/automation",
						type: MenuType.Item,
						point: Pointer.顶部菜单栏_售后_售后自动化,
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版,
							AUTH_USER_TYPE.分销商版零库存版,
							AUTH_USER_TYPE.供应商版库存版
						],
						icon: "shouhouzidonghua",
						desc: {
							icon: "shouhouzidonghua",
							name: "售后自动化",
							content:
								"商家在此页面可配置售后自动化策略，通过系统自动化处理，减少售后操作人员售后处理流程，缩短操作时间，降低人员成本，提升买家售后体验",
						},
					},
					{
						name: "退货地址",
						id: MenuIdMap["售后_退货地址"],
						path: "/aftersale/address",
						type: MenuType.Item,
						icon: "tuihuodizhi",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.分销商版零库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版,
							AUTH_USER_TYPE.供应商版库存版
						],
						desc: {
							icon: "tuihuodizhi",
							name: "退货地址",
							content:
								"淘宝/天猫、拼多多、抖音平台售后中显示的买家退货的邮寄地址",
						},
					}
				],
			},
			{
				name: "退货登记",
				children: [
					{
						name: "售后扫描登记",
						id: MenuIdMap["售后_售后扫描登记"],
						path: "/aftersale/scanRegister",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "shouhoudingdan",
						point: Pointer.顶部菜单栏_售后_售后扫描登记,
						desc: {
							icon: "shouhoudingdan",
							name: "售后扫描登记",
							content:
								"商家通过此功能可过扫描退回包裹，系统将自动与退货退款订单进行关联，快速处理售后包裹",
						},
					}
				],
			}
		],
	},
	{
		name: "商品",
		path: "",
		id: MenuIdMap["商品"],
		children: [
			{
				name: "商品资料",
				children: [
					{
						name: "货品档案",
						id: MenuIdMap["商品_货品档案"],
						path: "/warehouse/archives",
						icon: "putonghuopindangan",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer.顶部菜单栏_商品_普通货品档案,
						desc: {
							icon: "putonghuopindangan",
							name: "货品档案",
							content:
								"货品就是库存最小单位（SKU），是库存管理最基础的单元。可以从线上电商平台商品批量导入普通货品档案，也可以手工新建。订单上展示、打印时使用的货品名称等信息来自于此。",
						},
					},
					{
						name: "组合货品档案",
						id: MenuIdMap["商品_组合货品档案"],
						path: "/warehouse/combinedArchives",
						icon: "zuhehuopindangan",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer.顶部菜单栏_商品_组合货品,
						desc: {
							icon: "zuhehuopindangan",
							name: "组合货品档案",
							content:
								"多种货品（SKU）组合为一个套装在电商平台上销售时，在本系统中要建立对应的组合货品，以确保能正确扣减库存。",
						},
					},
					{
						name: "货品与商品关系",
						id: MenuIdMap["商品_货品与商品关系"],
						path: "/warehouse/relation",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "huopinyushangpinguanxi",
						point: Pointer.顶部菜单栏_商品_货品与商品关系,
						desc: {
							name: "货品与商品关系",
							icon: "huopinyushangpinguanxi",
							content:
								"维护快递助手ERP货品档案与电商平台上的商品的关联关系。维护了对应关系后，多个电商平台售卖的同种商品，可以扣减同一个快递助手ERP货品库存。",
						},
					},
					{
						name: "平台商品管理",
						id: MenuIdMap["商品_平台商品管理"],
						path: "/warehouse/platformGoods",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.分销商版零库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "pingtaishangpinguanli",
						point: Pointer.公共_顶部菜单_商品_平台商品管理,
						desc: {
							icon: "putonghuopindangan",
							name: "平台商品管理",
							content:
								"展示线上（电商平台）店铺中的商品名称、ID、编码等，同时可进行商品简称、重量、成本价、市场、档口等信息的修改。",
							content1:
								"展示线上（电商平台）店铺中的商品名称、ID、编码等",
						},
						isAddRedFont: '支持铺货'
					}
				],
			},
			{
				name: "铺货管理",
				children: [
					{
						name: "铺货配置",
						id: MenuIdMap["铺货配置"],
						path: "/warehouse/itemMigration",
						icon: "banjiapuhuopeizhi",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						// point: Pointer.顶部菜单栏_商品_搬家铺货配置,
						desc: {
							icon: "banjiapuhuopeizhi",
							name: "铺货配置",
							content: "配置商品铺货的相关参数和规则",
						},
					},
					{
						name: "商品铺货日志",
						id: MenuIdMap["商品铺货日志"],
						path: "/warehouse/migrationLog",
						icon: "shangpinbanjiarizhi",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						desc: {
							icon: "shangpinbanjiarizhi",
							name: "商品铺货日志",
							content: "记录商品铺货的操作日志，方便查询和追踪",
						},
					}
				],
			}
		],
	},
	{
		name: "库存",
		path: "",
		type: MenuType.SubMenu,
		id: MenuIdMap["库存"],
		authorize: [
			AUTH_USER_TYPE.常规版库存版,
			AUTH_USER_TYPE.供应商版库存版,
			AUTH_USER_TYPE.默认常规版库存版
		],
		children: [
			{
				name: "库存查看",
				children: [
					{
						name: "库存看板",
						id: MenuIdMap["库存_库存看板"],
						path: "/warehouse/stockInfo",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "kucunkanban",
						point: Pointer.顶部菜单栏_库存_库存看板,
						desc: {
							name: "库存看板",
							content:
								"显示本地仓库的货品数量(可用库存、在途库存)，并可以查看货品库存出入库记录",
							icon: "kucunkanban",
						},
					},
					{
						name: "库存预警",
						id: MenuIdMap["库存_库存预警阈值"],
						path: "/warehouse/stockWarnRemind",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "kucunyujingyuzhi",
						point: Pointer.顶部菜单栏_库存_库存预警阈值,
						desc: {
							icon: "kucunyujingyuzhi",
							name: "库存预警",
							content:
								"设置库存警戒值后，系统会根据此设置检测相应货品库存并进行预警",
						},
					},
					{
						name: "出入库日志",
						id: MenuIdMap["库存_出入库日志"],
						path: "/warehouse/stockLog",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "churukurizhi",
						point: Pointer.顶部菜单栏_库存_出入库日志,
						desc: {
							icon: "churukurizhi",
							name: "出入库日志",
							content: "查看所有货品的出入库日志",
						},
					}
				],
			},
			{
				name: "库存操作",
				children: [
					{
						name: "手动出库",
						id: MenuIdMap["库存_手动出库"],
						path: "/warehouse/outputWarehouse",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "caigoudan",
						// point: Pointer.顶部菜单栏_库存_手动出库,
						desc: {
							icon: "caigoudan",
							name: "手动出库",
							content:
								"解决商家直接通过线下渠道出库货品，无需发货，使用出库单直接对库存进行扣减。",
						},
					},
					{
						name: "扫描出入库",
						id: MenuIdMap["库存_扫描入库"],
						path: "/warehouse/scanWarehouse",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "saomiaochuruku",
						point: Pointer.库存_采购入库_扫描入库_展现,
						desc: {
							icon: "saomiaochuruku",
							name: "扫描出入库",
							content:
								"通过扫描自动录入提高货品出入库环节操作效率，标准化您的出入库流程，提升出入库速度",
						},
					},
					{
						name: "到货入库",
						id: MenuIdMap["库存_到货入库"],
						path: "/warehouse/arriveWarehouse",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "daohuoruku",
						point: Pointer.顶部菜单栏_库存_到货入库,
						desc: {
							icon: "daohuoruku",
							name: "到货入库",
							content:
								"按照实际到货情况进行入库操作。对于没有在系统内建立采购单的货品，可使用此功能。如果已经建立过采购单，请使用“采购入库”功能",
						},
					},
					{
						name: "采购入库",
						id: MenuIdMap["库存_采购入库"],
						path: "/warehouse/purchaseWarehouse",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "caigouruku",
						point: Pointer.顶部菜单栏_库存_采购入库,
						desc: {
							icon: "caigouruku",
							name: "采购入库",
							content:
								"按照之前在本系统中新建的采购单进行入库。 支持部分入库",
						},
					},
					{
						name: "按货品盘库",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["库存_按货品盘库"],
						path: "/warehouse/stockCheck",
						type: MenuType.Item,
						icon: "anhuopinpanku",
						point: Pointer.顶部菜单栏_库存_按货品盘库,
						desc: {
							icon: "anhuopinpanku",
							name: "按货品盘库",
							content:
								"核对总库存数与本系统记录当库存数是否一致，如果不一致则在此处纠正本系统中记录当库存数",
						},
					},
					{
						name: "库存初始化",
						id: MenuIdMap["库存_库存初始化"],
						path: "/warehouse/stockInit",
						type: MenuType.Item,
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "kucunchushihua",
						point: Pointer.顶部菜单栏_库存_库存初始化,
						desc: {
							icon: "kucunchushihua",
							name: "库存初始化",
							content:
								"初次使用ERP或者上新货品时需要填写可用库存数、成本，以保证后后续流程能正常进行。一个货品在一个仓库中只能初始化一次",
						},
					}
				],
			},
			{
				name: "库存同步",
				children: [
					{
						name: "库存同步",
						id: MenuIdMap["库存_库存同步"],
						path: "/warehouse/syncStock",
						type: MenuType.Item,
						icon: "kucuntongbu",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer.顶部菜单栏_库存_库存同步,
						desc: {
							icon: "kucuntongbu",
							name: "库存同步",
							content:
								"根据本地实际货品库存更新对应线上商品可售数量",
						},
					},
					{
						name: "库存同步日志",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["库存_库存同步日志"],
						path: "/warehouse/syncStockLog",
						type: MenuType.Item,
						icon: "kucuntongburizhi",
						point: Pointer.顶部菜单栏_库存_库存同步日志,
						desc: {
							icon: "kucuntongburizhi",
							name: "库存同步日志",
							content: "记录库存同步日志，方便查询",
						},
					} 
				],
			}
			

			// {
			// 	name: "组合货品打包",
			// 	authorize: [AUTH_USER_TYPE.常规版库存版, AUTH_USER_TYPE.供应商版库存版],
			// 	id: MenuIdMap["库存_组合货品打包"],
			// 	path: "/warehouse/stockCombinedGoods",
			// 	icon: "zuhehuopindabao",
			// 	point: Pointer.顶部菜单栏_库存_组合货品打包,
			// 	desc: {
			// 		icon: "zuhehuopindabao",
			// 		name: "组合货品打包",
			// 		content:
			// 			"按照组合货品规则将普通货品打包成组合货品。其实就是将子货品库存数量按比例转为组合货品库存数量。组合后子货品库存数减少，组合货品库存数相应增加。",
			// 	},
			// }
		],
	},
	{
		name: "采购",
		path: "",
		id: MenuIdMap["采购"],
		authorize: [
			AUTH_USER_TYPE.常规版库存版,
			AUTH_USER_TYPE.供应商版库存版,
			AUTH_USER_TYPE.默认常规版库存版
		],
		children: [
			{
				name: "采购",
				children: [
					{
						name: "采购单",
						id: MenuIdMap["采购_采购单"],
						path: "/warehouse/purchaseOrder",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						icon: "caigoudan",
						point: Pointer.顶部菜单栏_采购_采购单,
						desc: {
							icon: "caigoudan",
							name: "采购单",
							content:
								"采购单记录了供货商、采购数量、成本、运费等信息。可以将采购单分发给供应商。采购的货品到仓后可以直接依据采购单做“采购入库”",
						},
					},
					{
						name: "供应商",
						id: MenuIdMap["采购_供应商"],
						icon: "gongyingshang",
						path: "/warehouse/supplier",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer.顶部菜单栏_采购_供应商,
						desc: {
							icon: "gongyingshang",
							name: "供应商",
							content:
								"维护供应商信息。新建采购单与到货入库时需要此信息",
						},
					}
				],
			}
		],
	},
	{
		name: "备货单",
		id: MenuIdMap["备货单"],
		path: "/report/bhd",
		icon: "beihuodan",
		desc: {
			name: "备货单",
			icon: "beihuodan",
			content: "按照订单的不同维度统计需要备货的商品",
		},
		authorize: [
			AUTH_USER_TYPE.常规版零库存版,
			AUTH_USER_TYPE.常规版库存版,
			AUTH_USER_TYPE.供应商版库存版,
			AUTH_USER_TYPE.默认常规版零库存版,
			AUTH_USER_TYPE.默认常规版库存版
		],
		point: Pointer.顶部菜单栏_报表_备货单,
	},
	{
		name: "分销",
		path: "",
		id: MenuIdMap["分销"],
		authorize: [
			AUTH_USER_TYPE.常规版零库存版,
			AUTH_USER_TYPE.常规版库存版,
			AUTH_USER_TYPE.分销商版零库存版,
			AUTH_USER_TYPE.供应商版库存版
		],
		children: [
			{
				name: "我是供应商",
				children: [
					{
						name: "我的分销商",
						id: MenuIdMap["分销_我的分销商"],
						path: "/distribution/myDistributor",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版
						],
						icon: "wodefenxiaoshang",
						point: Pointer.我的分销商_页面展现_展现次数,
						desc: {
							name: "我的分销商",
							icon: "wodefenxiaoshang",
							content: "用于显示分销商信息，可维护相关资料",
						},
						itemType: [USER_TYPE_MENU.供应商],
					},
					{
						name: "代发商品管理",
						id: MenuIdMap["分销_代发商品管理"],
						path: "/distribution/DistributeGoodsManage",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版
						],
						icon: "wodegongyingshang",
						// point: Pointer.我的供应商_页面展现_展现次数,
						desc: {
							name: "代发商品管理",
							icon: "wodegongyingshang",
							content: "已推单的分销商品管理，可设置分销价等",
						},
						itemType: [USER_TYPE_MENU.供应商],
					},
					{
						name: "分销结算(供)",
						id: MenuIdMap["分销_分销结算_供应商"],
						path: "/distribution/settlement",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版
						],
						icon: "zhangdanjiesuan",
						point: "",
						desc: {
							name: "分销结算",
							icon: "zhangdanjiesuan",
							content:
								"供应商视角，用于查看分销订单的明细账单及生成结算单",
						},
						itemType: [USER_TYPE_MENU.供应商],
					},
					{
						name: "分销邀请返现",
						id: MenuIdMap["分销_分销邀请返现"],
						path: "/distribution/referralCashback",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版
						],
						isLimit: true,
						icon: "yaoqingfanxian",
						point: Pointer.分销邀请返现_菜单,
						desc: {
							name: "分销邀请返现",
							icon: "yaoqingfanxian",
							content: `供应商邀请分销商可获得现金返现！限时限量！仅此一次！\n\n 活动时间：\n 2024年3月1日-2024年9月30日`,
						},
						itemType: [USER_TYPE_MENU.供应商],
					}
				],
			},
			{
				name: "我是分销商",
				children: [
					{
						name: "我的供应商",
						id: MenuIdMap["分销_我的供应商"],
						path: "/distribution/MySupplier",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.分销商版零库存版
						],
						icon: "wodegongyingshang",
						point: Pointer.我的供应商_页面展现_展现次数,
						desc: {
							name: "我的供应商",
							icon: "wodegongyingshang",
							content: "用于显示供应商信息，可维护相关资料",
						},
						itemType: [USER_TYPE_MENU.分销商],
					},
					{
						name: "结算价设置",
						id: MenuIdMap["分销_结算价设置"],
						path: "/distribution/SettlementSetting",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.分销商版零库存版
						],
						icon: "jiesuanjiashezhi",
						// point: Pointer.我的供应商_页面展现_展现次数,
						desc: {
							name: "结算价设置",
							icon: "wodegongyingshang",
							content: "可对已推单的商品设置结算价格",
						},
						itemType: [USER_TYPE_MENU.分销商],
					},

					{
						name: "分销结算(分)",
						id: MenuIdMap["分销_分销结算_分销商"],
						path: "/distribution/settlementSale",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.分销商版零库存版
						],
						icon: "zhangdanjiesuan",
						point: "",
						desc: {
							name: "分销结算",
							icon: "zhangdanjiesuan",
							content:
								"分销商视角，用于查看分销订单的明细账单及生成结算单",
						},
						itemType: [USER_TYPE_MENU.分销商],
					},

					{
						name: "分销设置",
						id: MenuIdMap["分销_分销设置"],
						path: "/distribution/distributeSetting",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.分销商版零库存版
						],
						// isLimit: true,
						icon: "shezhi",
						point: Pointer.分销邀请返现_菜单,
						desc: {
							name: "分销设置",
							icon: "shezhi",
							content: `用于分销商设置推送给供应商的订单的显示及处理规则`,
						},
						itemType: [USER_TYPE_MENU.分销商],
					}
				],
			}

			// {
			// 	name: '分销账单结算',
			// 	id: '94',
			// 	path: '/distribution/accountCheck',
			// 	icon: 'zhangdanjiesuan',
			// 	point: '',
			// 	desc: {
			// 		name: '分销账单结算',
			// 		icon: 'zhangdanjiesuan',
			// 		content: '用于代发订单财务对账结算',
			// 	}
			// }
		],
	},
	{
		name: "档口",
		path: "/stall/bhd",
		id: MenuIdMap["档口"],
		authorize: [
			AUTH_USER_TYPE.常规版零库存版,
			AUTH_USER_TYPE.常规版库存版,
			AUTH_USER_TYPE.供应商版库存版,
			AUTH_USER_TYPE.默认常规版零库存版,
			AUTH_USER_TYPE.默认常规版库存版
		],
		children: [
			{
				name: "标签处理",
				children: [
					{
						name: "商品标签",
						icon: "shangpinbiaoqian",
						path: "/trade/goodsTag",
						id: MenuIdMap["订单_商品标签"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["导航栏_订单_商品标签"],
						desc: {
							name: "商品标签",
							content:
								"商家查询和管理生成的商品标签，并可以对商品标签进行打印发货",
							icon: "shangpinbiaoqian",
						},
					},
					{
						name: "备货单标签",
						id: MenuIdMap["档口_备货单标签"],
						path: "/stall/bhd",
						icon: "beihuodan",
						desc: {
							name: "备货单标签",
							icon: "beihuodan",
							content: "按照订单的不同维度统计需要备货的商品",
						},
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer.顶部菜单栏_报表_备货单,
					},
					{
						name: "扫描打印",
						icon: "saomiaodayin1",
						path: "/trade/scanPrint",
						id: MenuIdMap["档口_扫描打印"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["导航栏_订单_扫描打印"],
						desc: {
							name: "扫描打印",
							content:
								"商家可在此页面扫描已生成的商品标签进行快递单打印发货",
							icon: "saomiaodayin",
						},
					},
					{
						name: "标签点货",
						icon: "biaoqiandianhuo",
						path: "/trade/labelGoodsCheck",
						id: MenuIdMap["档口_标签点货"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["导航栏_订单_扫描打印"],
						desc: {
							name: "标签点货",
							content:
								"商家使用该功能对到货或拣货的标签进行清点、也可以在每天扫描发货完成后对标签进行盘点和退回档口登记",
							icon: "biaoqiandianhuo",
						},
					},
					{
						name: "退款标签重匹配",
						icon: "tuikuanbiaoqianzhongpipei",
						path: "/trade/refundLabelScanPrint",
						id: MenuIdMap["档口_退款标签重匹配"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["导航栏_订单_退款标签重匹配"],
						desc: {
							name: "退款标签重匹配",
							content:
								"扫描退款标签，系统将查询该标签的商品，重新匹配需要该商品的待发货订单打印发货",
							icon: "saomiaodayin",
						},
					},
					{
						name: "爆款标签",
						icon: "baokuanbiaoqian",
						path: "/trade/hotGoodsScanPrint",
						id: MenuIdMap["档口_爆款标签"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["导航栏_订单_爆款打印"],
						desc: {
							name: "爆款标签",
							content: "商家扫描爆款标签，可批量打印快递单发货",
							icon: "saomiaodayin",
						},
					}
				],
			},
			{
				name: "标签设置",
				children: [
					{
						name: "标签设置",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["档口_标签设置"],
						icon: "biaoqianshezhi",
						path: "/settings/goodsLabel",
						type: MenuType.Item,
						point: Pointer.导航栏_设置_标签设置,
						desc: {
							icon: "biaoqianshezhi",
							name: "标签设置",
							content:
								"商家可在此设置商品标签相关的生成、打印、数据内容配置",
						},
					},
					{
						name: "档口设置",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["档口_档口设置"],
						icon: "dangkoushezhi",
						path: "/stall/setting",
						type: MenuType.Item,
						// point: Pointer.导航栏_设置_标签设置,
						desc: {
							icon: "dangkoushezhi",
							name: "档口设置",
							content:
								"可设置商品所属市场档口供应商信息，标签打印后可根据市场档口排序",
						},
					},
					{
						name: "档口拿货路线",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["档口_档口拿货路线"],
						icon: "dangkouxianlu",
						path: "/stall/stockRoute",
						type: MenuType.Item,
						point: Pointer.档口_档口拿货路线_页面展现,
						desc: {
							icon: "dangkouxianlu",
							name: "档口拿货路线",
							content:
								"商家可在此页面根据实际拿货路线调整市场-档口-供应商排序，在使用备货单、标签打印时可根据此排序进展示和打印",
						},
					}
				],
			},
			{
				name: "标签对账",
				children: [
					{
						name: "标签对账",
						icon: "biaoqianduizhang",
						path: "/trade/labelAccountCheck",
						id: MenuIdMap["档口_标签对账"],
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						point: Pointer["导航栏_订单_扫描打印"],
						desc: {
							name: "标签对账",
							content:
								"商家使用该功能可统计导出拿货标签账单与市场进行对账",
							icon: "biaoqianduizhang",
						},
					}
				],
			}
		],
	},
	{
		name: "报表",
		path: "",
		id: MenuIdMap["报表"],
		authorize: [
			AUTH_USER_TYPE.常规版零库存版,
			AUTH_USER_TYPE.常规版库存版,
			AUTH_USER_TYPE.默认常规版零库存版,
			AUTH_USER_TYPE.默认常规版库存版
		],
		children: [
			{
				name: "实时看板",
				children: [
					{
						name: "实时销售大屏",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_实时销售大屏"],
						icon: "shujutubiao",
						path: "#/largeScreen",
						type: MenuType.Url,
						point: "",
						desc: {
							icon: "shujutubiao",
							name: "实时销售大屏",
							content:
								"实时监控销售数据、物流数据，为您的日常运营提供最准确实时的数据参考。",
						},
					},
					{
						name: "物流预警",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_物流预警"],
						icon: "wuliuyujing",
						path: "/report/logisticsWarning",
						point: Pointer.物流预警_展示,
						desc: {
							icon: "wuliuyujing",
							name: "物流预警",
							content:
								"综合各平台规则，对发货后揽收、运输、派送等各个环节进行监控，及时预警，减少物流风险",
						},
					}
				],
			},
			{
				name: "报表对账",
				children: [
					{
						name: "销售毛利润报表",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_销售毛利润报表"],
						icon: "xiaoshoumaolibaobiao",
						path: "/report/grossProfit",
						point: "",
						desc: {
							icon: "kuaididuizhang",
							name: "销售毛利润报表",
							content:
								"使用此功能商家可统计日\\周\\月维度下各平台、店铺、货品、规格的销量、销售毛利",
						},
					},
					{
						name: "买家售后统计",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_买家售后统计"],
						icon: "maijiashouhoutongji",
						path: "/report/buyerAfterSaleStatistics",
						point: Pointer.顶部菜单栏_报表_买家售后统计,
						desc: {
							icon: "maijiashouhoutongji",
							name: "买家售后统计",
							content: "按不同维度统计买家售后数据",
						},
					},
					{
						name: "货品出入库账单",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_货品出入库账单"],
						icon: "shengchengduizhangdan",
						path: "/report/dzd",
						point: Pointer.顶部菜单栏_报表_货品出库账单,
						desc: {
							icon: "shengchengduizhangdan",
							name: "货品出入库账单",
							content: "统计出入库货品数量，一般用于库存数据核对",
						},
					},
					{
						name: "货品成本报表",
						authorize: [
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_货品成本报表"],
						icon: "huopinchengbenbaobiao",
						path: "/report/hpcb",
						point: Pointer.顶部菜单栏_报表_货品成本报表,
						desc: {
							icon: "huopinchengbenbaobiao",
							name: "货品成本报表",
							content: "统计当前库存货品成本，用于成本核算",
						},
					},
					{
						name: "快递对账",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_快递对账"],
						icon: "kuaididuizhang",
						path: "/report/accountCheck",
						point: Pointer.顶部菜单栏_报表_快递对账,
						desc: {
							icon: "kuaididuizhang",
							name: "快递对账",
							content:
								"使用该功能将您的面单号使用记录与快递公司给的对帐单（Excel文档）进行核对，快速的帮您完成对帐",
						},
					},
					{
						name: "进销存报表",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_进销存报表"],
						icon: "jinxiaocunbaobiao",
						path: "/report/inventory",
						point: "",
						desc: {
							icon: "",
							name: "进销存报表",
							content:
								"",
						},
					}
				],
			},
			{
				name: "数据分析",
				children: [
					{
						name: "销售分析",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_销售分析"],
						icon: "xiaoshoufenxi",
						path: "/report/salesAnalysis",
						point: Pointer.销售分析_页面展现,
						desc: {
							icon: "xiaoshoufenxi",
							name: "销售分析",
							content:
								"可以从日期、平台、店铺、商品等维度查看销售情况",
						},
					},
					{
						name: "售后分析",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["报表_售后分析"],
						icon: "shouhoufenxi",
						path: "/report/afterSalesAnalysis",
						point: Pointer.销售分析_页面展现,
						desc: {
							icon: "shouhoufenxi",
							name: "售后分析",
							content:
								"可以从日期、平台、店铺、商品、售后原因、快递公司、市场档口、供应商等维度查看售后情况",
						},
					}
				],
			}
		],
	},
	{
		name: "店铺",
		path: "/shops",
		type: MenuType.Item,
		id: MenuIdMap["店铺"],
		point: Pointer.顶部菜单栏_店铺,
	},
	{
		name: "设置",
		path: "",
		type: MenuType.SubMenu,
		id: MenuIdMap["设置"],
		children: [
			{
				name: "基础设置",
				children: [
					{
						name: "系统设置",
						id: MenuIdMap["设置_系统设置"],
						// permissionIds: ["31", "35", "36"],
						permissionIds: [
							MenuIdMap.设置_系统设置,
							MenuIdMap.设置_系统设置二级,
							MenuIdMap.设置_系统设置二级_用户个性化设置
						],
						path: "/settings/system",
						icon: "shezhi",
						type: MenuType.Item,
						point: Pointer.顶部菜单栏_设置_系统设置,
						desc: {
							icon: "shezhi",
							name: "系统设置",
							content:
								"可以进行功能设置：1.系统设置，对所有账号都生效；2.用户个性化设置，仅对您的账号生效。",
						},
					},
					{
						name: "账号与权限",
						id: MenuIdMap["设置_账号与权限"],
						path: "/settings/perm",
						type: MenuType.Item,
						icon: "zhanghaoyuquanxian",
						point: Pointer.顶部菜单栏_设置_账号与权限,
						desc: {
							icon: "zhanghaoyuquanxian",
							name: "账号与权限",
							content:
								"主账号可对子账号设置功能权限。平台店铺的子账号可直接通过授权登录，会自动新建erp子账号。",
						},
					},
					{
						name: "仓库",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						path: "/warehouse/entrepot",
						type: MenuType.Item,
						id: MenuIdMap["设置_仓库"],
						point: Pointer.顶部菜单栏_仓库,
						icon: "kucunchushihua",
						desc: {
							icon: "kucunchushihua",
							name: "仓库",
							content: "显示仓库信息",
						},
					},
					{
						name: "智选快递",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["设置_智选快递"],
						icon: "zhixuankuaidi",
						path: "/settings/smartExpress",
						type: MenuType.Item,
						point: Pointer.顶部菜单栏_设置_智选快递,
						desc: {
							icon: "zhixuankuaidi",
							name: "智选快递",
							content: "根据预设条件自动识别订单内容推荐快递公司",
						},
					},
					{
						name: "运费模板设置",
						authorize: [
							AUTH_USER_TYPE.常规版零库存版,
							AUTH_USER_TYPE.常规版库存版,
							AUTH_USER_TYPE.供应商版库存版,
							AUTH_USER_TYPE.默认常规版零库存版,
							AUTH_USER_TYPE.默认常规版库存版
						],
						id: MenuIdMap["设置_运费模板设置"],
						path: "/settings/expressTemplate",
						icon: "yunfeimobanshezhi",
						type: MenuType.Item,
						// point: Pointer.顶部菜单栏_设置_系统设置,
						desc: {
							icon: "yunfeimobanshezhi",
							name: "运费模板设置",
							content:
								"设置好快递模板后，系统将根据实际发货快递进行计算每笔订单运费",
						},
					},

					{
						name: "客户管理及黑名单",
						path: "/settings/customer",
						type: MenuType.Item,
						id: MenuIdMap["设置_客户管理及黑名单"],
						// point: Pointer.顶部菜单栏_设置_客户管理及黑名单,
						icon: "kehuguanli",
						desc: {
							icon: "kehuguanli",
							name: "客户管理及黑名单",
							content: "可查看买家信息并标记黑名单，用于订单标记提醒",
						},
					}
				],
			},
			{
				name: "打单设置",
				children: [
					{
						name: "常用发件人",
						id: MenuIdMap["设置_常用发件人"],
						path: "/settings/sender",
						type: MenuType.Item,
						icon: "changyongfahuoren",
						point: Pointer.顶部菜单栏_设置_常用发件人,
						desc: {
							icon: "changyongfahuoren",
							name: "常用发件人",
							content:
								"可设置“快递单发件人、发货单发件人”，在打印时可供选择。若店铺绑定了发件人，打印时会优先显示绑定的发件人。",
						},
					},
					{
						name: "常用收件人",
						id: MenuIdMap["设置_常用收件人"],
						path: "/settings/recipient",
						icon: "changyongshoujianren",
						type: MenuType.Item,
						point: Pointer.顶部菜单栏_设置_常用收件人,
						desc: {
							icon: "changyongshoujianren",
							name: "常用收件人",
							content: "新建手工单时，可供快捷选择",
						},
					},
					{
						name: "常用备注短语",
						id: MenuIdMap["设置_常用备注短语"],
						icon: "changyongbeizhuduanyu",
						path: "/settings/memo",
						type: MenuType.Item,
						point: Pointer.顶部菜单栏_设置_常用备注短语,
						desc: {
							icon: "changyongbeizhuduanyu",
							name: "常用备注短语",
							content: "修改订单备注时，可供快捷选择",
						},
					},
					{
						name: "快递可达设置",
						id: MenuIdMap["设置_快递可达设置"],
						icon: "kuaidikeda",
						path: "/settings/expressCanUp",
						type: MenuType.Item,
						point: Pointer.顶部菜单栏_设置_快递可达设置,
						desc: {
							icon: "kuaidikeda",
							name: "快递可达设置",
							content:
								"商家可通过提前设置快递的配送区域规则，避开快递公司中不可到达的地区等雷区。",
						},
					}
				],
			},
			{
				name: "其他",
				children: [
					{
						name: "系统日志",
						id: MenuIdMap["设置_系统日志"],
						icon: "beihuodan",
						path: "/settings/systemLog",
						type: MenuType.Item,
						point: Pointer.顶部菜单栏_设置_智选快递,
						desc: {
							icon: "beihuodan",
							name: "系统日志",
							content: "记录软件内的系统操作日志",
						},
					},
					{
						name: "平台授权费返还",
						path: "/settings/AuthorizeFeeReturn",
						type: MenuType.Item,
						id: MenuIdMap["设置_平台授权费返还"],
						point: Pointer.顶部菜单栏_仓库,
						icon: "shouquanfeifanhuan",
						desc: {
							icon: "shouquanfeifanhuan",
							name: "平台授权费返还",
							content: "可申请平台授权费返还并查看进度",
						},
					},
					{
						name: "监控设备管理",
						id: MenuIdMap["设置_监控设备管理"],
						path: "/settings/monitoringDevice",
						type: MenuType.Item,
						icon: "jiankongshebeiguanli",
						point: Pointer.设置_监控设备管理菜单_点击,
						desc: {
							icon: "jiankongshebeiguanli",
							name: "监控设备管理",
							content: "监控设备管理",
						},
					}
				],
			}
		],
	},
	{
		name: "公共",
		path: "",
		id: MenuIdMap["公共"],
		type: MenuType.Item,
		hideInMenu: true,
		children: [
			{
				name: "下载中心",
				hideInMenu: true,
				id: MenuIdMap["公共_下载中心"],
				permissionIds: [MenuIdMap.公共_下载中心],
				path: "/downloadCenter",
				icon: "shezhi",
				type: MenuType.Item,
				point: "",
				desc: {
					icon: "shezhi",
					name: "下载中心",
					content: "下载中心",
				},
			},
			{
				name: "任务中心",
				hideInMenu: true,
				id: MenuIdMap["公共_下载中心"],
				permissionIds: [MenuIdMap.公共_下载中心],
				path: "/taskCenter",
				icon: "renwuzhongxin",
				type: MenuType.Item,
				point: "",
				desc: {
					icon: "renwuzhongxin",
					name: "任务中心",
					content: "任务中心",
				},
			}
		],
	},
	{
		name: "商户管理",
		path: "",
		id: MenuIdMap["商户管理"],
		type: MenuType.Item,
		hideInMenu: true,
		children: [
			{
				name: "商户查询",
				hideInMenu: true,
				id: MenuIdMap["商户管理_商户查询"],
				permissionIds: [MenuIdMap["商户管理_商户查询"]],
				path: "/merchant",
				icon: "shezhi",
				type: MenuType.Item,
				point: "",
				desc: {
					icon: "shezhi",
					name: "商户查询",
					content: "商户查询",
				},
			}
		],
	}
];

const getMenuIdMap = () => {
	let MenuIdMap = {};
	MenuData.forEach((element) => {
		MenuIdMap[element.name] = element.id;
		if (element.children) {
			element.children.forEach((child) => {
				MenuIdMap[`${element.name}_${child.name}`] = child.id;
			});
		}
	});
	console.log(MenuIdMap);
};

// getMenuIdMap();
