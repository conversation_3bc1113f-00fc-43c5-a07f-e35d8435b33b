@use "../../../assets/styles/theme.scss" as *;

.headerMenuContainer {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 999 !important;
	width: 100%;
	min-width: 1140px;

	.TabsRouterList {
		padding-top: 12px;
		margin: 0;
		height: 44px;
		background: #fff;
		user-select: none;

		:global {
			.ant-tabs-nav {
				margin-bottom: 0 !important;
				height: 32px;
			}

			.ant-tabs-tab {
				user-select: none;
				padding: 0 12px 0 0 !important;
			}
		}
	}
}

.headerMenu {
	height: 48px;
	background-color: $color-primary;
	display: flex;
	align-content: center;
	justify-content: space-between;

	.logo {
		display: flex;
		color: #fff;
		align-items: center;
		padding-left: 22px;
		padding-right: 46px;

		a {
			color: #fff;
		}
	}

	.logoIcon {
		font-size: 27px;

		svg {
			// 因为Icon统一为svg 所以logo需要特殊处理宽度
			width: 6.7em !important;
		}
	}
}

.newIcon {
	background: #ff0000;
	width: 30px;
	line-height: 12px;
	font-size: 12px;
	padding: 1px 0 2px;
	z-index: 1;
	border-radius: 8px 8px 8px 0;
	color: #fff;
	text-align: center;
	position: absolute;
	right: -6px;
	top: 4px;
	font-family: PingFangSC-Medium;
}

.nav {
	display: flex;
	color: #fff;
	font-size: 16px;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 0;

	a {
		color: #fff;
	}

	.arrow {
		width: 7px;
		height: 7px;
		border-top: 1px solid #fff;
		border-right: 1px solid #fff;
		transform: rotate(135deg);
		transition: all 0.5s ease 0s;
		margin-left: 5px;
		margin-top: -2px;
	}

	.navItem {
		height: 100%;
		// padding: 0 14px;
		font-weight: 500;
		// display: flex;
		// align-items: center;
		position: relative;

		&.active {
			background: #ff9c36;

			a {
				color: #fff;
			}
		}
	}

	.navItem:hover {
		background: #ff9c36;

		a {
			color: #fff;
		}

		.arrow {
			transform: rotate(315deg);
			/*旋转180度*/
			margin-top: 5px;
		}

		.popUpWrapper {
			display: flex;
			flex-direction: column;
		}
	}

	.linkItem {
		display: flex;
		width: 100%;
		height: 100%;
		align-items: center;
		padding: 0 14px;
		cursor: pointer;
		word-break: keep-all;
	}

	.btnLink {
		padding: 0;
		margin: 0;
		color: rgba(0, 0, 0, 0.65);
	}

	.link {
		width: 100%;
		height: 100%;
		// display: inline-block;
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.popUpWrapper {
		display: none;
		z-index: 99;
		position: absolute;
		top: 48px;
		left: 0;
		min-width: 360px;
		box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
		color: rgba(0, 0, 0, 0.65);
		background-color: #fff;
		font-size: 14px;
		font-weight: normal;
		padding: 16px;
		row-gap: 16px;

		> div {
			.navItemTitle {
				display: flex;
				gap: 8px;
				align-items: center;
				margin-bottom: 6px;
				color: rgba(0, 0, 0, 0.85);
				font-weight: 700;
			}
			.line {
				width: 2px;
				height: 16px;
				opacity: 1;
				background: #fd8204;
				display: inline-block;
			}
		}

		.subTitle {
			min-height: 200px;

			a {
				color: rgba(0, 0, 0, 0.65);
			}

			.urlLink {
				color: rgba(0, 0, 0, 0.65);
			}

			min-width: 148px;
			border-right: 1px solid #e8e8e8;

			li {
				height: 40px;
				line-height: 40px;
				cursor: pointer;
				padding-left: 8px;

				&.active {
					a {
						color: $color-primary;
					}
				}
			}

			li:hover {
				background: #f7e1cc;
				border-right: 2px solid $color-primary;

				a,
				.urlLink {
					color: $color-primary;
				}
			}
		}

		.subTitle1 {
			min-height: 200px;
			width: 312px;
			border-right: 1px solid #e8e8e8;
			padding: 10px 0;

			.subMain {
				padding: 10px 12px;

				.subItemTitle {
					font-size: 16px;
					font-weight: 600;
					color: #333;
					margin-bottom: 6px;
				}

				.subItemList {
					display: flex;
					flex-wrap: wrap;
					align-items: center;
					align-content: flex-start;
					row-gap: 4px;
					column-gap: 14px;
				}
			}

			a {
				color: rgba(0, 0, 0, 0.65);
			}

			.urlLink {
				color: rgba(0, 0, 0, 0.65);
			}

			li {
				min-width: 120px;
				height: 40px;
				line-height: 40px;
				cursor: pointer;
				padding: 0 8px;

				&.active {
					a {
						color: $color-primary;
					}
				}
			}

			li:hover {
				background: #f7e1cc;
				border-bottom: 2px solid $color-primary;

				a,
				.urlLink {
					color: $color-primary;
				}
			}
		}
	}

	.subMenuDesc {
		min-height: 200px;
		width: 239px;

		.descTitle {
			margin-top: 13px;
			height: 20px;
			font-family: PingFangSC-Regular;
			color: $color-primary;
			margin-left: 16px;
			display: flex;
			align-items: center;
		}

		.descContent {
			line-height: normal;
			white-space: pre-line;
		}
	}

	.navItemContainer {
		display: grid;
		grid-template-columns: 1fr 1fr;
		.link {
			color: rgba(0, 0, 0, 0.85) !important;
			position: relative;
			height: 38px;
			&:hover {
				color: #fd8204 !important;
			}
			&.active{
				color: #fd8204 !important;
			}
		}
	}
}

.navRight {
    justify-content: flex-end;
    display: flex;
    margin-left: 8px;
}

.willExpireWrapper {
	width: 100%;
	height: 60px;
	line-height: 60px;
	background: #fff7f4;

	.willExpireBanner {
		width: 1200px;
		margin: 0 auto;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 20px;
		color: rgba(0, 0, 0, 0.85);
		font-weight: 500;

		.colorRed {
			color: #e01515;
			font-weight: 500;
		}

		.fontSize12 {
			font-size: 12px;
		}

		.tipFunc {
			display: flex;
			justify-content: space-around;
			flex-wrap: wrap;
			width: 350px;
			margin: 0 60.5px;

			.tipItem {
				width: 100px;
				font-size: 14px;
				color: rgba(0, 0, 0, 0.85);
				font-weight: 400;
				height: 20px;
				line-height: 20px;
				display: flex;
				align-items: center;
			}
		}
	}
}

.contextMenu {
	.menuItem {
		&:hover {
			background: #f7e1cc;
			color: #ff9c36;
		}
	}
}

.menuNewIcon {
	position: absolute;
	top: -8px;
	right: 2px;
}

.descNewIcon {
	position: absolute;
	top: -20px;
}

.menuLinkNewIcon {
	position: relative;
	top: -8px;
}

.menuLinkLimitIcon {
	position: absolute;
	top: -4px;
	left: 108px;
	font-size: 12px;
	background-color: #ff0000;
	color: #fff;
	border-radius: 10px;
	width: 38px;
	line-height: 18px;
	text-align: center;
}
.supportTag {
	background-color: #ff0000;
	color: white;
	font-size: 12px; 
	line-height: 12px;
	padding: 2px 4px; 
	border-radius: 10px;
	font-weight: bold;
	margin-left: 2px;
	white-space: nowrap;
	display: inline-block;
}
