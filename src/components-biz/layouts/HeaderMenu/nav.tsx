import React, { useEffect, useState, useMemo } from "react";
import { Link, useLocation } from "react-router-dom";
import cs from 'classnames';
import { cloneDeep, intersection } from "lodash";
import { observer } from "mobx-react";
import { runInAction } from "mobx";
import { Modal } from "antd";
import s from "./index.module.scss";
import { MenuData, MenuIdMap } from "./menu";
import { MenuBean, MenuType } from "./menuBean";
import userStore from "@/stores/user/index";
import { useStores } from "@/stores/tool";
import Icon from "@/components/Icon";
import history from "@/utils/history";
import { tradeStore } from "@/stores";
import { USER_TYPE, stockVersion, zeroStockVersion, USER_TYPE_MENU, AUTH_USER_TYPE, authorizeMap } from "@/constants";
import distributionStore from "@/stores/distribution";
import { WHITE_PATH_LIST } from ".";
import { getMd5Str } from "@/utils/util";
import { IndexEnvEnvSetApi } from "@/apis/user";
import message from "@/components/message";
import { getToken } from "@/utils/token";


const MenuItem : React.FC<{
    active: boolean
}> = (props) => {
	return (
		<li
			className={ cs(s.navItem, props.active ? s.active : '') }
		>
			{props.children}
		</li>
	);
};

/**
 * 导航子菜单弹框
 * @param props
 * @returns
 */
interface SubMenuPopUpInf {
	childrenMenu?: MenuBean[];
	desc?: {
		name: string;
		content: string;
		icon?: string;
		content1?: string;
	}
	type?: string | undefined;
    activeSubTab?: string;
	userType?: USER_TYPE;
	version: string;
	versionType: string;
	item?: MenuBean;
	authUserType?:AUTH_USER_TYPE ;
}

const TRADE_PATH_LIST = ['/trade', '/trade/index'];
const HOT_GOODS_PATH = '/trade/hotGoodsScanPrint';
const BHD_PATH = '/report/bhd';
let prevPath = '';
let isSwitching = false;
const envMap = {
	'prod': 'erp',
	'gray': 'erpg',
	'pre': 'erppre',
	'p1': 'erp1',
	'p2': 'erp2'
};

const envMapV2K = {
	'erp': 'prod',
	'erpg': 'gray',
	'erppre': 'pre',
	'erp1': 'p1',
	'erp2': 'p2'
};

export const subRouterMap = {
	[MenuIdMap["订单_订单打印"]]: ['/trade/index', '/trade/pushedOrder'], // 订单打印
	[MenuIdMap["售后_售后管理"]]: ['/aftersale/trade', '/aftersale/distributorTrade', '/aftersale/orderLog'], // 售后管理
	[MenuIdMap["档口_备货单标签"]]: ['/stall/bhd/BhdList', '/stall/bhd/bhdTableSet', '/stall/bhd/BhdHotLabelSet'], // 备货单标签
	[MenuIdMap["备货单"]]: ['/report/bhd/BhdList', '/report/bhd/bhdTableSet'], // 备货单
	[MenuIdMap["报表_货品出入库账单"]]: ['/report/dzd/dzdList', '/report/dzd/dzdList/out', '/report/dzd/history'], // 货品出入库账单
	[MenuIdMap["库存_波次管理"]]: ['/warehouse/wave'], // 波次管理
	[MenuIdMap["设置_监控设备管理"]]: ['/settings/monitoringDevice/ipc', '/settings/monitoringDevice/nvr', '/settings/monitoringDevice/record'], // 监控设备管理
};

// 子路由的匹配规则（用在PrivateRoute权限判断里面）
export const subRouterRuleMap = {
	[MenuIdMap["订单_订单打印"]]: '/trade/:param?', // 订单打印
	[MenuIdMap["售后_售后管理"]]: '/aftersale/:param?', // 售后管理
	[MenuIdMap["档口_备货单标签"]]: '/stall/bhd', // 备货单标签
	[MenuIdMap["备货单"]]: '/report/bhd', // 备货单
	[MenuIdMap["报表_货品出入库账单"]]: '/report/dzd', // 货品出入库账单
	[MenuIdMap["库存_波次管理"]]: '/warehouse/:param?', // 波次管理
	[MenuIdMap["设置_监控设备管理"]]: '/settings/monitoringDevice', // 监控设备管理
};

const Nav:React.FC = observer(() => {
	const store: typeof userStore = useStores('userStore');
	const [menuList, setMenuList] = useState<MenuBean[]>([]);
	const [activeTab, setActiveTab] = useState<string>();
	const [activeSubTab, setActiveSubTab] = useState<string>();
	const [version, setVersion] = useState<string>(null);
	const [versionType, setVersionType] = useState<string>(null);
	const [userType, setUserType] = useState<USER_TYPE>(null);
	const { supplierList, isHasActiveSupplier } = distributionStore;
	const { cacheTabList } = store;
	const location = useLocation();

	// 获取用户版本信息
	const getAuthUserType = useMemo(() => {
		let matchedKey = null; // AUTH_USER_TYPE 是字符串

		if (version !== null && userType !== null) {
			for (const key in authorizeMap) {
				if (authorizeMap[key].version == version && authorizeMap[key].userType == userType) { // 不能用全等，有字符串
					matchedKey = key;
				}
			}

			// console.log('%c [ 用户版本 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', matchedKey, authUserName[matchedKey]);
		}

		return matchedKey;
	}, [version, userType]);

	// 子路由激活导航菜单
	const activeSubRoutes = (menu2: MenuBean, pathName: string) => {
		let list = subRouterMap?.[menu2?.id];
		if (list?.includes(pathName)) {
			return true;
		}
		return false;
	};

	// 导航菜单替换子路由
	const cacheSubRoutes = (cacheTabList, pathName, activeSubMenu, activeMenu) => {
		let item = cacheTabList?.find(item => item.id == activeSubMenu || item.id == activeMenu);
		if (item) {
			item.path = pathName;
		}
	};

	useEffect(() => {
		const fn = async() => {
			const userInfo = await store.getUserInfo();
			if (!userInfo) return;
			const { version, userId = '', versionType, userType, gmtCreated } = userInfo;
			  // 在处理菜单数据前，先确保获取最新的波次管理权限
			  await userStore.getWaveManagePermission();
			setVersion(String(version));
			setVersionType(String(versionType));
			setUserType(userType);
			const isHasAllAuthority = !userInfo.subUserId;
			const authorityDetail = JSON.parse(userInfo.authorityDetail || '[]');
			// console.log('%c [ 子账号权限数组 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', userInfo, authorityDetail);
			setMenuList(dealMenuData(isHasAllAuthority, Array.from(new Set(authorityDetail)), (version), (versionType), userType));
		};
		fn();
	}, [store, getAuthUserType]);

	useEffect(() => {
		const fn = () => {
			let wLocation = window.location;
			if (wLocation.href.indexOf('verifyAuthToken') > -1) {
				// 有拼多多解密授权的token
				let searchArr = wLocation.search.split('&');
				let verifyAuthToken = searchArr[2].split('verifyAuthToken=')[1];
				let mallId = searchArr[1].split('mallId=')[1];
				if (verifyAuthToken && mallId) {
					window.localStorage.setItem(`verifyAuthToken_${mallId}`, verifyAuthToken);
				}
			}
			const authorityDetail = JSON.parse(store.userInfo?.authorityDetail || '[]');
			let hasAuthority = true;
			const pathName = location.pathname;
			let activeMenu = '';
			let activeSubMenu = '';
			let tempMemuTab;
			let documentTitle = "";

			menuList.forEach(menu1 => {
				if (menu1.path === pathName || (menu1.path.includes(BHD_PATH) && pathName.includes(BHD_PATH))) {
					activeMenu = menu1.id;
					tempMemuTab = menu1;
					documentTitle = menu1.name;
				}

				menu1.children?.forEach(menu3 => {
					menu3.children?.forEach(menu2 => {
						if (
							menu2.path === pathName
							|| (((pathName.split("/").length - 1) === 3) && pathName.includes(menu2.path)) // 针对3层路由
							|| (pathName.includes(menu2.path))
							|| activeSubRoutes(menu2, pathName) // subRoutes 也要激活主路由
						) {
							if (!whitePermission(menu2.id)) {
								hasAuthority = false;
							} else {
								if (store.userInfo?.subUserId && !isMenuHasPermission(menu2, authorityDetail)) {
									// 子账号权限判断
									hasAuthority = false;
								}
								// 如果用户访问的是商户查询页面，需要判断userInfo中的invitationCode是否有值
								if (menu2.path === "/merchant") {
									hasAuthority = !!store.userInfo?.invitationCode;
								}
							}
							activeSubMenu = menu2.id;
							activeMenu = menu1.id;
							documentTitle = menu2.name || menu1.name;
							tempMemuTab = menu2;
						}
					});
				});
			});

			document.title = documentTitle ? `${documentTitle} | 快递助手ERP` : '快递助手ERP';

			// 这里不能判断一级菜单的路由，只能判断二级菜单的路由
			if (!hasAuthority) {
				// history.push('/404/1'); // 交给PrivateRoute进行权限判断，不直接跳到404
				document.title = '快递助手ERP';
			}

			setActiveTab(activeMenu);
			setActiveSubTab(activeSubMenu);
			runInAction(() => {
				const notPush = userStore.isDistributorAccount
					&& !WHITE_PATH_LIST.includes(pathName)
					&& supplierList
					&& !isHasActiveSupplier;
				if (notPush) return;

				if (pathName === '/stall/setting') {
					prevPath = pathName;
					return;
				}
				let index = cacheTabList.findIndex(item => item.id === tempMemuTab?.id);
				if (index === -1 && tempMemuTab) {
					cacheTabList.push(tempMemuTab);
				}
				console.time('cacheSubRoutes');
				cacheSubRoutes(cacheTabList, pathName, activeSubMenu, activeMenu); // 订单打印、售后管理、备货单、备货单标签、货品出入库账单
				console.timeEnd('cacheSubRoutes');

				let { list, tempHotGoodsList, tempTradeList, setList, setTempHotGoodsList, setTempTradeList } = tradeStore.tradeListStore;
				// TODO 异步操作导致的list赋值如何解决
				if (TRADE_PATH_LIST.includes(prevPath)) {
					setTempTradeList([...list]);
				} else if (prevPath === HOT_GOODS_PATH) {
					setTempHotGoodsList([...list]);
				}

				if (TRADE_PATH_LIST.includes(pathName)) {
					setList([...tempTradeList]);
				} else if (pathName === HOT_GOODS_PATH) {
					setList([...tempHotGoodsList]);
				}

				prevPath = pathName;
			});
		};
		const switchEnv = userStore.switchEnvObj?.env;
		// 0: 登陆跳转 1：弹框提醒 2: 弹框提醒强制
		const switchType: any = userStore.switchEnvObj?.cutType;
		const content: string = userStore.switchEnvObj?.cutMsg || '检测到软件版本有更新，是否立即更新';
		const currentEnv = envMapV2K[window.location.hostname.split(".")?.[0]];
		// 初始化检测静默切换，定时任务方式检测弹窗提示
		const notFirst = userStore.switchEnvObj?.notFirst;
		console.log('currentEnv', currentEnv, switchEnv);
		const token = getToken();
		const { userId = '', subUserId = '' } = userStore.userInfo;
		console.log('userId', userId);
		// let currentEnv = userStore.userInfo?.env;
		if (switchEnv && switchEnv !== currentEnv && switchType != 0 && userId) {
			// 防止路由多次跳转导致多个弹窗
			// 确认或取消都不再继续提示 直到下次页面刷新
			if (isSwitching) {
				fn();
				return;
			}
			isSwitching = true;
			// !! 初始化检测静默切换；后续跳转检测弹窗
			if (notFirst) {
				Modal.confirm({
					title: '系统提示',
					content,
					okText: '立即更新',
					cancelButtonProps: switchType == 2 ? { style: { display: 'none' } } : {},
					cancelText: '暂不更新',
					onOk: () => {
						IndexEnvEnvSetApi({
							env: switchEnv
						}).then(res => {
							window.location.href = `https://${envMap[switchEnv || 'prod']}.kuaidizs.cn/index.html?token=${token}&userId=${userId}&subUserId=${subUserId}#/authorize`;
						}).catch(() => {
							message.error('切换环境失败，请联系客服处理');
							fn();
						});
					},
					onCancel: () => {
						fn();
					}
				});
			} else {
				window.location.href = `https://${envMap[switchEnv || 'prod']}.kuaidizs.cn/index.html?token=${token}&userId=${userId}&subUserId=${subUserId}#/authorize`;
			}

		} else {
			fn();
		}
	}, [location, menuList]);

	const toUrl = (url) => {
		// const { userId = '', subUserId = "" } = store.userInfo;
		// const _userId = getMd5Str(`${userId}_${subUserId}`);
		// const origin = window.location.origin;
		window.open(`${window.location.href.split("#/")[0]}${url}`);
	};

	const SubMenuPopUp = (props: SubMenuPopUpInf) => {
		const { childrenMenu, desc, activeSubTab, version, versionType, userType, authUserType } = props;
		
		// 默认将一级菜单描述信息放入
		const [selectedKey, updateSelectedKey] = useState<{ desc: { name: string, content: string, content1?: string, icon?: string, isNew?: boolean } } | MenuBean>({ desc: desc || childrenMenu?.[0]?.desc });

		return (
			<div className={ cs(s.popUpWrapper) }>
				{
					childrenMenu.map(i => (
						<div key={ i.name }>
							{i.show ? (
								<>
									<div className={ s.navItemTitle }>
										<span className={ s.line } />
										{i.name}
										{
											i.isAddRedFont && <span className={ s.supportTag } style={ { marginLeft: '-2px' } }>{i.isAddRedFont}</span>
										}
									</div>
									<div className={ cs(s.navItemContainer) }>
										{i?.children?.map(j => {
											return j.enableStatus == 1 ? (j.type == MenuType.Url ? (
												<div key={ j.name } className={ cs(s.link, s.urlLink, 'r-pointer') } onClick={ () => toUrl(j.path) }>
													<Icon style={ { verticalAlign: 'middle' } } type={ j.icon } className="r-pointer r-mr-4" />
													{j.name}
													{j.isNew ? <Icon className={ s.menuNewIcon } size={ 36 } type="new" /> : ""}
												</div>
											) : (
												<Link onClick={ () => updateSelectedKey(j) } className={ cs(activeSubTab === j.id && location.pathname.includes(j.path) ? s.active : '', s.link) } key={ j.name } to={ j.path } target={ j.target || "_self" } data-point={ j.point }>
													<Icon style={ { verticalAlign: 'middle' } } type={ j.icon } className="r-pointer r-mr-4" />
													{j.name}
													{j.isNew ? <Icon className={ s.menuLinkNewIcon } size={ 36 } type="new" /> : ""}
													{j.isHot ? <Icon className="r-ml-4" type="huore" /> : ""}
													{j.isAddRedFont && <span className={ s.supportTag }>{j.isAddRedFont}</span>}
												</Link>
											)) : null;
										})}
									</div>
								</>
							) : null}
						</div>
					))
				}
			</div>
		);

		// return (
		// 	<div className={ cs(s.popUpWrapper) }>
		// 		{
		// 			// 左侧导航
		// 			isHasSubMenu ? (
		// 				isShowItemType() ? showItemTypeDom() : (
		// 					<ul className={ cs(s.subTitle, 'r-h-full') }>
		// 						{console.log(childrenMenu, '------------childrenMenu')}
		// 						{
		// 							childrenMenu?.map((item: any) => {
		// 								return (
		// 									item.enableStatus == 1 ? (
		// 										<li
		// 											onMouseEnter={ () => updateSelectedKey(item) }
		// 											key={ item.name }
		// 											className={ `${activeSubTab === item.id ? s.active : ''} r-relative` }
		// 										>
		// 											{
		// 												item.type == MenuType.Url ? (
		// 													<div className={ cs(s.link, s.urlLink) } onClick={ () => { toUrl(item.path); } }>
		// 														<Icon style={ { verticalAlign: 'middle' } } type={ item.icon } className="r-pointer r-mr-4" />
		// 														{item.name}
		// 														{item.isNew ? <Icon className={ s.menuNewIcon } size={ 36 } type="new" /> : ""}
		// 													</div>
		// 												) : (
		// 													<Link className={ s.link } to={ item.path } target={ item.target || "_self" } data-point={ item.point }>
		// 														<Icon style={ { verticalAlign: 'middle' } } type={ item.icon } className="r-pointer r-mr-4" />
		// 														{item.name}
		// 														{item.isNew ? <Icon className={ s.menuLinkNewIcon } size={ 36 } type="new" /> : ""}
		// 														{item.isLimit && (userType == USER_TYPE.常规版) ? <span className={ s.menuLinkLimitIcon } >限时</span> : ""}

		// 													</Link>
		// 												)
		// 											}
		// 										</li>
		// 									) : null
		// 								);
		// 							})
		// 						}
		// 					</ul>
		// 				)
		// 			) : null
		// 		}
		// 		{/* {
		// 			// 右侧描述
		// 			selectedKey?.desc ? (
		// 				<div className={ cs(s.subMenuDesc) }>
		// 					<div className={ cs(s.descTitle, 'r-lh-16 r-mb-12') }>
		// 						<div className="r-relative">
		// 							<Icon style={ { verticalAlign: 'middel' } } type={ selectedKey.desc.icon } className="r-pointer r-mr-4" />{selectedKey?.desc?.name}
		// 							{selectedKey?.desc?.isNew ? <Icon className={ s.descNewIcon } size={ 36 } type="new" /> : ""}
		// 						</div>
		// 					</div>
		// 					<div className={ cs(s.descContent, 'r-pl-16 r-pr-16 r-fs-14 r-ta-j r-c-333') }>
		// 						{
		// 							showUserTypeContent(selectedKey)
		// 						}
		// 					</div>
		// 				</div>
		// 			) : null
		// 		} */}
		// 	</div>
		// );
	};

	const getNavMenuItems = (menusData: MenuBean[], activeTab:string, activeSubTab:string, version: string, versionType: string, userType: USER_TYPE) => {
		if (!menusData) {
			return [];
		}
		return menusData.map((item) => {

			let view = null;
			if (!item.enableStatus) {
				return view;
			}

			switch (item.type) {
				case MenuType.Url:
					view = (
						<MenuItem key={ item.name } active={ item.id === activeTab }>
							<a href={ item.path } target="_blank" rel="noreferrer" className={ s.linkItem }>
								{item.name}
							</a>
						</MenuItem>
					);
					break;
				default:
					if (item.children?.length) {
						view = (
							<MenuItem key={ item.name } active={ item.id === activeTab }>
								{ item.path ? (
									<Link to={ item.path } className={ s.linkItem } data-point={ item.point }>
										{item.name}
										<i className={ s.arrow } />
										{item.isNew ? <span className={ s.newIcon }>new</span> : ""}
									</Link>
								 ) : (
									 <span className={ s.linkItem }>
										 { item.name }
										 <i className={ s.arrow } />
										{item.isNew ? <span className={ s.newIcon }>new</span> : ""}
									 </span>
								)}
								<SubMenuPopUp type={ item.type } desc={ item.desc } childrenMenu={ item.children } activeSubTab={ activeSubTab } version={ version } versionType={ versionType } userType={ userType } item={ item } authUserType={ getAuthUserType } />
							</MenuItem>
						);
					} else {
						view = (
							<MenuItem key={ item.path } active={ item.id === activeTab }>
								<Link data-point={ item.point } to={ item.path } className={ s.linkItem } >{item.name}</Link>
							</MenuItem>
						);
					}
					break;
			}
			return view;
		});
	};

	const isMenuHasPermission = (menu:MenuBean, authorityDetail:string[]) => {
		return (menu.permissionIds?.length && intersection(authorityDetail, menu.permissionIds)?.length)
				|| authorityDetail.includes(menu.id);
	};

	// 主账号权限判断
	const inAuthorize = (i: MenuBean, version: stockVersion, userType: USER_TYPE) => {
		let authorize:any = i?.authorize;
		if (authorize?.length) {
			// 判断身份
			if (getAuthUserType && authorize.includes(getAuthUserType)) {
				return true;
			} else {
				return false; // 身份不明或不在权限范围
			}
		} else {
			return true; // 没有默认为有权限
		}
	};

	const whitePermission = (id) => {
		// 仅考虑白名单，或者版本权限的 展示逻辑 不考虑主子账号的
		
		// 波次管理菜单特殊处理
		if (id === MenuIdMap["库存_波次管理"]) {
			// 直接调用异步方法获取最新权限值
			const hasPermission = userStore.hasWaveManagePermission;
			return hasPermission; // 有波次管理权限返回 true，否则返回 false
		}
		return true;
	};

	const getAllAuthorityList = (data:any[]) => {
		const enabledItems = {};

		data.forEach(item => {
			if (item.enableStatus === 1) {
				enabledItems[`${item.name}_${item.id}`] = [];
				if (item.children) {
					item.children.forEach(navItem => {
						navItem?.children?.forEach(child => {
							if (child.enableStatus === 1) {
								enabledItems[`${item.name}_${item.id}`].push(`${child.name}_${child.id}`);
							}
						});
					});
				}
			}
		});
		console.log('%c [ 有权限的菜单 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', enabledItems);
	};

	const changeChildrenPath = (item: MenuBean) => {
		let path = item?.path || '';
		// 免费分销商版显示售后单
		// 免费供应商显示分销商的售后单
		// 常规版展示售后单、分销商的售后单
		if (item.id == MenuIdMap["售后_售后管理"]) {
			const { isDistributorAccount, isFreeSupplierAccount, isSupplierAccount, isDefaultAccount } = userStore;
			// 默认版本也显示售后单
			if (isDistributorAccount || isSupplierAccount || isDefaultAccount) {
				path = '/aftersale/trade';
			} else if (isFreeSupplierAccount) {
				path = '/aftersale/distributorTrade';
			}
		}
		return path;
	};

	const dealMenuData = (isHasAllAuthority:boolean, authorityDetail: string[], version: stockVersion, versionType: zeroStockVersion, userType: USER_TYPE) => {
		const menu = cloneDeep(MenuData);
		menu.forEach((item) => {
			if (
				(isMenuHasPermission(item, authorityDetail) || isHasAllAuthority)
				&& inAuthorize(item, version, userType)
				&& !item.hideInMenu
			) {
				item.enableStatus = 1;
			} else {
				item.enableStatus = 0;
			}
			item.children?.forEach(j => {
				let hasPermission = false; // 标志变量，初始为 false

				j.children?.forEach(i => {
					if (
						(isMenuHasPermission(i, authorityDetail) || isHasAllAuthority)
						&& inAuthorize(i, version, userType)
						&& !item.hideInMenu
						// 仅考虑白名单，或者版本权限的 展示逻辑 不考虑主子账号的
						&& whitePermission(i.id)
					) {
						i.enableStatus = 1;
						item.enableStatus = 1; // 这里会导致二级菜单有权限重设一级菜单权限
						hasPermission = true; // 找到符合条件的 i，将标志变量设置为 true
						if (!item.path && i.type !== MenuType.Url) {
							item.path = i.path;
						}
						// 需要改变二级菜单的 path
						item.path = changeChildrenPath(item);
					} else {
						i.enableStatus = 0;
					}
				});

				// 在循环结束后，依据 hasPermission 的值来设置 j.show
				j.show = hasPermission;
			});
		});

		getAllAuthorityList(menu); // 输出有权限的菜单
		return menu;
	};


	return (
		<ul className={ cs(s.nav) }>
			{getNavMenuItems(menuList, activeTab, activeSubTab, version, versionType, userType)}
		</ul>
	);
});

export default Nav;
