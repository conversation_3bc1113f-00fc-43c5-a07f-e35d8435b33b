import { USER_TYPE, stockVersion, zeroStockVersion, USER_TYPE_MENU, AUTH_USER_TYPE } from "@/constants";

export interface MenuBean {
    name: string;
    isNew?: boolean;
	isLimit?: boolean;
    path?: string;
    key?: string;
    type?: MenuType;
	userType?: USER_TYPE[]; // 分销用户类型
    version?: stockVersion[]; // 此功能可以使用的版本
	versionType?: zeroStockVersion[]; // 使用versionType判断，无默认全部显示，有显示满足的版本。
    hideInMenu?: boolean;
    icon?: string;
    target?: string;
    desc?: {
        name: string;
        content: string;
        icon?: string;
        isNew?: boolean;
		isLimit?: boolean;
        content1?: string; // USER_TYPE 1 供应商 & 分销商
	};
	children?: MenuBean[];
    isAddRedFont?:string;
    id?:string;
    permissionIds?: string[];
    enableStatus?:number;
    point?: string;
    isFixed?: boolean;
    itemType?: USER_TYPE_MENU[]; // 供应商、分销商工具分类
	authorize?: AUTH_USER_TYPE[]; // 授权身份
	show?: boolean // nav导航项父title展示
	isHot?:boolean // 是否高频
}

export enum MenuType {
    SubMenu = 'SubMenu',
    ItemGroup = 'ItemGroup',
    Item = 'Item',
    Url = 'url',
    SubItem = 'subItem'
}
