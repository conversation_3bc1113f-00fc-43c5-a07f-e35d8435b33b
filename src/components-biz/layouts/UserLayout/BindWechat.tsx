import React, { useEffect, useState } from "react";
import { Modal, Button, Input, Form } from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import Icon from "@/components/Icon";
import s from "./index.module.scss";
import VerifyPhone from "./VerifyPhone"; // 需要创建这个组件
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { GeBindWxMpOpenId, GetQrcode, UnBindWxMpOpenId, checkWxBindApi } from "@/apis/user";
import message from "@/components/message";
import userStore from "@/stores/user";
import { getToken } from "@/utils/token";

interface BindWechatInf {
    visible: boolean;
    onClose: () => void;
}
const sceneType = {
	'小程序': '1',
	'公众号登录': '2',
	'公众号绑定': '3',
};
const BindWechat: React.FC<BindWechatInf> = (props) => {
	const { visible, onClose } = props;
	const [verifyPhoneVisible, setVerifyPhoneVisible] = useState(false);
	const [isBind, setIsBind] = useState(false);

	const { userInfo } = userStore;

	
	useEffect(() => {
		if (visible) {
			GeBindWxMpOpenId().then((res) => {
				setIsBind(res);
			});
		}		

	}, [visible]);

	
	const handleBindClick = () => {
		// sendPoint(Pointer.基础_个人中心_关联微信_解绑微信);
		sendPoint(Pointer.基础_个人中心_关联微信_绑定微信);
		// 关闭当前弹窗，打开验证手机弹窗
		onClose();
		setVerifyPhoneVisible(true);
	};
	
	const handleUnbindClick = () => {
		Modal.confirm({
			icon: <QuestionCircleOutlined style={ { color: '#FF9500' } } />,
			title: '确定要解绑关联微信吗?',
			content: <div style={ { color: '#FF4D4F', textAlign: 'left' } }>解绑后将无法使用微信登录/验证，请谨慎操作</div>,
			okText: '提交',
			cancelText: '取消',
			centered: true,
			onOk: () => {
				UnBindWxMpOpenId().then((res) => {
					if (res) {
						message.success('解绑成功');
						setIsBind(false);
					}
				});
			}
		});
	};

	const verifyPhoneSuccess = async() => {
		const data = {
			type: sceneType['公众号绑定'],
			userId: userInfo?.userId,
			subUserId: userInfo?.subUserId || 0
		};
		const response = await GetQrcode(data);
		
		if (response) {
			console.log(window.location, 'window.locationwindow.location');
			// 获取当前URL的基础部分（协议+主机名+端口）
			const currentUrl = window.location.href;
			const baseUrl = currentUrl.split('#')[0].split('?')[0]; // 移除hash和查询参数
			const nowToken = getToken();
			console.log(nowToken, 'nowTokennowTokennowToken');
			// 重定向到CheckWxBind页面，使用完整的baseUrl
			window.open(`${baseUrl}#/checkWxBind?wxMpUrl=${encodeURIComponent(response.wxMpUrl)}&interaction=${response.interaction}&token=${nowToken}`);
			
			message.info('请在微信中扫码绑定', 0);
			setVerifyPhoneVisible(false);
		
		}
	};
	
	return (
		<>
			<Modal
				centered
				visible={ visible }
				title="关联微信"
				footer={ [
					<Button key="cancel" onClick={ onClose }>
						取消
					</Button>,
					<Button key="confirm" type="primary" onClick={ onClose }>
						确定
					</Button>
				] }
				onCancel={ onClose }
				maskClosable={ false }
				className={ s.bindWechatModal }
			>
				<div className={ s.bindWechatContent }>
					<div className={ s.wechatIcon }>
						<Icon type="xiaochengxu" style={ { fontSize: 24, color: '#07C160' } } />
					</div>
					<div className={ s.wechatStatus }>
						<div>
							<span>微信:</span> 
							<span style={ { color: 'rgba(0, 0, 0, 0.45)', marginLeft: '5px' } }>{isBind ? "已绑定" : "未绑定"}</span>
						</div>
						{isBind ? (
							<Button type="link" className={ s.bindBtn } onClick={ handleUnbindClick }>解绑</Button>
						) : (
							<Button type="link" className={ s.bindBtn } onClick={ handleBindClick }>绑定</Button>
						)}
					</div>
				</div>
			</Modal>
			
			{/* 验证安全手机弹窗 */}
			<VerifyPhone 
				visible={ verifyPhoneVisible } 
				onClose={ () => setVerifyPhoneVisible(false) }
				onSuccess={ () => verifyPhoneSuccess() }
				type={ 13 }
			/>
		</>
	);
};

export default BindWechat;
