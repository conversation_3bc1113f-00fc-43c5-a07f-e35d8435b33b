$primary: #F5821F;
@use "../../../assets/styles/theme.scss" as *;

.userLoayout {
	display: flex;
	align-items: center;
	position: relative;
	margin-right: 15px;
	height: 100%;
	.photo {
		margin-right: 8px;
		width: 24px;
		height: 24px;
		border-radius: 100%;
		text-align: center;
		background-image: url('../../../assets/image/index/touxinag.png');
		background-size: 100% 100%;
	}

	.userPopUp {
		position: absolute;
		top: 48px;
		left: 0;
		z-index: 99;
		width: 100%;
		// height: 66px;
		border: 1px solid #E4E7ED;
		box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
		background: #FFFFFF;
		li {
			color: #666666;
			&:hover {
				color: #FD8204;
				background: #FFF5EB;
			}
		}

	}

	.mobile {
		min-width: 70px;
		height: 100%;
		display: flex;
		align-items: center;
	}
}

.userLoayout:hover {
	.userPopUp {
		display: block;
	}
}

.arrow{
	width: 6px;
    height: 6px;
    border-top: 1px solid #fff;
    border-right: 1px solid #fff;
    margin-left: 5px;
    margin-top: -2px;
    transform: rotate(135deg);
    display: inline-block;
}


.navLeft{
	display: flex;
	align-items: center;
}
.changePhoneModal {
	width: 600px;
}

:global {
	.resetModal {
        .ant-form-item-control {
            min-height: 50px !important;
        }
        .ant-form-item {
            margin-bottom: 0;
        }
	}
}

.bindWechatModal {
  :global {
    .ant-modal-body {
      padding: 24px;
    }
  }
}

.bindWechatContent {
  display: flex;
  align-items: center;
}

.wechatIcon {
  margin-right: 16px;
}

.wechatStatus {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.bindBtn {
  color: #1890ff !important;
  cursor: pointer;
}

.verifyPhoneModal {
  :global {
    .ant-modal-body {
      padding: 24px;
    }
  }
}

.verifyPhoneForm {
  padding: 0 16px;
}

.phoneInfo {
  margin-bottom: 16px;
  color: #333;
}

.verifyCodeItem {
  margin-bottom: 0;
}

.verifyCodeInput {
  display: flex;
  
  .getCodeBtn {
    margin-left: 8px;
    white-space: nowrap;
  }
}
