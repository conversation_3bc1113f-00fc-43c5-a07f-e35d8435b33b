import React, { useState } from "react";
import { Modal, Button, Input, Form, message, Space, Tooltip } from "antd";
import { useCountDown } from "ahooks";
import _ from "lodash";
import { QuestionCircleOutlined } from "@ant-design/icons";
import userStore from "@/stores/user";
import { checkSmsCode<PERSON>pi, sendSmsCode<PERSON>pi } from "@/apis/user";
import s from "./index.module.scss";
import { dealAccount } from "@/pages/Index/Settings/AuthorizeFeeReturn";

interface VerifyPhoneProps {
    visible: boolean;
    onClose: () => void;
    onSuccess?: () => void; // 添加成功回调
	type?:number;
}

const VerifyPhone: React.FC<VerifyPhoneProps> = (props) => {
	const { visible, onClose, onSuccess, type } = props;
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const [yzmTxt, setYzmTxt] = useState("获取验证码");
	const [targetDate, setTargetDate] = useState<number>();
	const { userInfo } = userStore;
	const [countdown, formattedRes] = useCountDown({
		targetDate,
		onEnd: () => {
			setYzmTxt("重新获取");
		},
	});
	const { seconds } = formattedRes || {};
	// 获取用户手机号，并做脱敏处理
	const userMobile = userInfo?.exportSecurityMobile || '';
	// 获取验证码
	const getSmsCode = async() => {
		const params = {
			mobile: userMobile,
			type,
		};
		try {
			await sendSmsCodeApi(params);
			message.success('验证码已发送');
			setTargetDate(Date.now() + 1000 * 60);
		} catch (error) {
			console.error('获取验证码失败:', error);
			message.error('获取验证码失败，请重试');
		}
	};

	const contactCustomer = () => {
		return (
			<p>如手机号已丢失或弃用，可点击
				<span
					onClick={ () => {
						window.open('https://wpa1.qq.com/dSY8GJNm?_type=wpa&qidian=true', '_blank');
					} }
					className="r-click"
				>联系客服
				</span>，寻求客服帮助
			</p>
		);
	};
	
	// 提交验证
	const handleSubmit = () => {
		console.log(userInfo, 'userInfouserInfouserInfos');

		form.validateFields().then(values => {
			setLoading(true);
			// 这里应该调用验证码验证API
			// 示例：verifyCodeApi(values.verifyCode).then(...)
			const data = {
				mobile: userMobile,
				verifyCode: values.verifyCode,
				type,
			};
			checkSmsCodeApi(data).then(res => {
				onSuccess();
			});
			form.resetFields();
		}).catch(err => {
			console.log('验证失败:', err);
		}).finally(() => {
			setLoading(false);
		});
	};

	const handleCancel = () => {
		form.resetFields();
		onClose();
	};
	
	return (
		<Modal
			centered
			visible={ visible }
			title="验证安全手机"
			footer={ [
				<Button key="cancel" onClick={ handleCancel }>
					取消
				</Button>,
				<Button 
					key="submit" 
					type="primary" 
					loading={ loading }
					onClick={ handleSubmit }
				>
					提交
				</Button>
			] }
			onCancel={ handleCancel }
			maskClosable={ false }
			className={ s.verifyPhoneModal }
		>
			<Form 
				form={ form } 
				className={ s.verifyPhoneForm }
			>
				<div className={ s.phoneInfo }>
					<span>当前手机号：</span>
					<span>{dealAccount(userMobile)}</span>
					<Tooltip title={ contactCustomer() }>
						<QuestionCircleOutlined className="r-c-gray r-ml-8" />
					</Tooltip>
				</div>
				
				<Form.Item 
					label="验证码" 
					required
				>
					<Space>
						<Form.Item
							noStyle
							name="verifyCode"
							rules={ [
								{
									required: true,
									message: "请输入短信验证码",
									len: 6,
									type: "string",
								}
							] }
						>
							<Input
								style={ { height: 32, width: 240 } }
								placeholder="请输入当前手机号短信验证码"
							/>
						</Form.Item>
						<Button
							disabled={ countdown > 0 }
							onClick={ _.throttle(() => getSmsCode(), 2000) }
						>
							{ countdown > 0 ? `${seconds}后重新获取` : yzmTxt }
						</Button>
					</Space>
				</Form.Item>
			</Form>
		</Modal>
	);
};

export default VerifyPhone;
