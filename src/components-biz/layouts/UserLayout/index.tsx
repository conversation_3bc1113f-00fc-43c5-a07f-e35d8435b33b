import React, { useEffect, useMemo, useState } from "react";
import cs from 'classnames';
import { observer } from "mobx-react-lite";
import { Form, Input, Modal, Tooltip } from "antd";
import s from "./index.module.scss";
import userStore from "@/stores/user";
import Icon from "@/components/Icon";
import history from "@/utils/history";
import { IndexUserLoginOut, IndexUserResetPasswordApi } from "@/apis/user";
import { logout } from '@/utils/token';
import { PASSWORD_RULES } from "@/constants";
import { getStrLen } from "@/utils";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import message from "@/components/message";
import ChangePhone from "./ChangePhone";
import BindWechat from "./BindWechat";
import VerifyPhone from "./VerifyPhone";

interface ResetPwdInf {
	visible: boolean;
	onClose: () => void;
}

const ResetPwd:React.FC<ResetPwdInf> = (props) => {
	const { visible, onClose } = props;
	const [resetPwdForm] = Form.useForm();
	const [step, setStep] = useState(0); // 0: 验证手机号, 1: 设置新密码
	const [verifyPhoneVisible, setVerifyPhoneVisible] = useState(false);
	
	const formTailLayout = {
		labelCol: { span: 5 },
		wrapperCol: { span: 16, },
	};
	
	useEffect(() => {
		if (visible) {
			setVerifyPhoneVisible(true);
		}
	}, [visible]);
	
	// 手机验证成功后的回调
	const onVerifySuccess = () => {
		setVerifyPhoneVisible(false);
		setStep(1);
	};
	
	/**
	 * 提交表单
	 * @param
	 */
	const onChangePwd = async() => {
		await resetPwdForm.validateFields();
		const val = resetPwdForm.getFieldsValue();
		val.type = 2;
		const { data } = await IndexUserResetPasswordApi(val);
		if (data) {
			message.success('重置密码成功！即将退出，请重新登录。');
			handleClose();
			logout();
		}
	};
	
	// 关闭弹窗时重置状态
	const handleClose = () => {
		setStep(0);
		resetPwdForm.resetFields();
		setVerifyPhoneVisible(false);
		onClose();
	};
	
	return (
		<>
			<Modal
				centered
				visible={ visible && step === 1 }
				title="重置密码"
				onOk={ onChangePwd }
				onCancel={ handleClose }
				maskClosable={ false }
				className="resetModal"
			>
				<Form
					autoComplete="off"
					form={ resetPwdForm }
					name="resetPwdForm"
					className="resetPwdForm"
					onFinish={ onChangePwd }
					{ ...formTailLayout }
				>
					<Form.Item
						label="新密码"
						name="password"
						rules={ PASSWORD_RULES }
					>
						<Input
							style={ { height: 32, width: 280 } }
							type="password"
							placeholder="请设置密码，8 - 16位密码"
						/>
					</Form.Item>
					<Form.Item
						label="二次确认"
						name="passwordConfirm"
						dependencies={ ['password'] }
						rules={ [PASSWORD_RULES[0], ({ getFieldValue }) => ({
							validator(_, value) {
								if (!value || getFieldValue('password') === value) {
									return Promise.resolve();
								}
								return Promise.reject(new Error('两次输入密码不一致'));
							},
						})] }
					>
						<Input
							type="password"
							style={ { height: 32, width: 280 } }
							placeholder="请再次输入密码"
						/>
					</Form.Item>
				</Form>
			</Modal>
			
			{/* 手机验证弹窗 */}
			<VerifyPhone 
				visible={ verifyPhoneVisible } 
				onClose={ () => {
					setVerifyPhoneVisible(false);
					if (step === 0) {
						handleClose();
					}
				} }
				onSuccess={ onVerifySuccess }
				type={ 12 }
			/>
		</>
	);
};


const UserLayout: React.FC = () => {
	const { userInfo } = userStore;
	const [resetPwdVisible, setResetPwdVisible] = useState<boolean>(false);
	const [changePhoneVisible, setChangePhoneVisible] = useState<boolean>(false);
	const [wechatBindVisible, setWechatBindVisible] = useState<boolean>(false);


	const func = {
		changePhone: () => {
			sendPoint(Pointer.基础_个人中心_修改手机号);
			setChangePhoneVisible(true);
		},
		bindWechat: () => {
			sendPoint(Pointer.基础_个人中心_关联微信); // 需要添加对应的埋点常量
			// 打开微信关联弹窗或跳转到关联页面
			// 这里可以调用微信扫码登录的方法
			setWechatBindVisible(true);
		}
	};

	const handleClick = async(item: any) => {
		if (item.func) {
			func[item.func]();
		} else if (item.path === '/resetPwd') {
			setResetPwdVisible(true);
			sendPoint(Pointer.顶部菜单栏_用户信息_重置密码);
		} else {
			if (item.title === '退出登录') {
				sendPoint(Pointer.顶部菜单栏_用户信息_退出登录);
				await IndexUserLoginOut({});
				logout();
			}
			history.push(item.path);
		}
	};

	const UserInfoJson = useMemo(() => {
		const userItemMap = {
			重置密码: {
				title: '重置密码',
				path: '/resetPwd'
			},
			关联微信: {
				title: '关联微信',
				func: "bindWechat"
			},
			修改手机号: {
				title: '修改手机号',
				func: "changePhone"
			},
			退出登录: {
				title: '退出登录',
				path: '/login'
			}
		};

		const arr = [];
		arr.push(userItemMap.重置密码);
		// 移除子账号判断条件，让所有账号都显示关联微信选项
		arr.push(userItemMap.关联微信);
		if (!userStore?.userInfo?.subUserId) {
			// 只有非子账号才显示修改手机号
			arr.push(userItemMap.修改手机号);
		}
		arr.push(userItemMap.退出登录);
		return arr;
	}, [userStore?.userInfo]);

	return (
		<div className={ s.navLeft }>
			<div className={ cs(s.userLoayout, 'r-pointer') }>
				<div className={ s.photo } />
				<div className={ cs(s.mobile, 'r-c-white') }>
					<Tooltip
						getPopupContainer={ trigger => trigger?.parentElement }
						placement="left"
						title={ getStrLen(userStore?.userInfo?.subUserName || userStore?.userInfo?.userName) > 20 ? (userStore?.userInfo?.subUserName || userStore?.userInfo?.userName) : '' }
					>
						<span style={ { maxWidth: 144 } } className="ellipsis">{ userStore?.userInfo?.subUserName || userStore?.userInfo?.userName}</span>
					</Tooltip>
					<i className={ s.arrow } />
				</div>
				<ul className={ cs(s.userPopUp, 'r-hide r-bg-white') } style={ { height: UserInfoJson.length * 33 + 'px' } }>
					{
						UserInfoJson.map((item: { title: string, path: string }) => (
							<li onClick={ () => handleClick(item) } key={ item.title } className="r-fs-14 c-c-666 r-lh-32 r-pl-22 r-fw-400">
								{item.title}
							</li>
						))
					}
				</ul>
				<ResetPwd visible={ resetPwdVisible } onClose={ () => setResetPwdVisible(false) } />
				<ChangePhone visible={ changePhoneVisible } onClose={ () => setChangePhoneVisible(false) } />
				<BindWechat visible={ wechatBindVisible } onClose={ () => setWechatBindVisible(false) } />
			</div>
		</div>
	);
};
export default observer(UserLayout);
