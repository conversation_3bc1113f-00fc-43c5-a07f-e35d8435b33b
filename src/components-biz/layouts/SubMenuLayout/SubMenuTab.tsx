import React, { memo, useCallback, useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tooltip } from "antd";
import { SyncOutlined, QuestionCircleOutlined, UsergroupAddOutlined } from "@ant-design/icons";
import { Link, useHistory, useLocation } from "react-router-dom";
import { observer } from "mobx-react";
import { number } from "prop-types";
import { debounce } from "lodash"; 
import dayjs from "dayjs";
import s from "./index.module.scss";
import { RouteType } from "@/types/schemas";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { getAutoShipCount } from "@/apis/trade/preShip";
import { SelectOperateLogSummaryApi } from '@/apis/aftersale/trade';
import { getMultiShops, getAllPlats } from "@/components-biz/ShopListSelect/shopListUtils";
import { PLAT_TB, PLAT_TM } from '@/constants';
import event from '@/libs/event';
import userStore from "@/stores/user";
import { EVENT_BUS } from "@/pages/AfterSale/constants";
import WxAppQrCode from '@/components-biz/WxAppQrCode';

const SubMenuTab: React.FC<{
	navStyle?: any;
	autoSendNum?: boolean;
	afterSaleLogNum?: boolean;
	menus: RouteType[];
}> = (props) => {
	const { menus, autoSendNum, afterSaleLogNum } = props;
	const { shopList } = userStore;
	const [activeTab, setActiveTab] = useState<string>();
	const [showAutoSend, setShowAutoSend] = useState(0);
	const [showAfterSaleLogNum, setShowAfterSaleLogNum] = useState(0);
	const [platformList, setPlatformList] = useState([]); // 所有平台

	const history = useHistory();
	const location = useLocation();
	const handleClick = useCallback((e) => {
		setActiveTab(e.key);
	}, []);

	useEffect(() => {
		if (autoSendNum || afterSaleLogNum) {
			getBadgeNum();
		}
	}, []);

	const getBadgeNum = async() => {
		const multiShopS = await getMultiShops({});
		if (autoSendNum) {
			const param = {
				buyerNick: null,
				exId: null,
				multiShopS,
				pageNo: 1,
				pageSize: 10,
				ptTid: null,
				receiveName: null,
				refundStatus: null,
				sameOrderCombination: false,
				sid: null,
				tid: null,
				wlStatus: null
			};
			getAutoShipCount(param).then((e) => {
				setShowAutoSend(Number(e || 0));
			});
		}
		if (afterSaleLogNum) {
			const param = {
				pageSize: 10,
				pageNo: 1,
				multiShopS,
				createTimeStart: dayjs().startOf("day").format('YYYY-MM-DD HH:mm:ss'),
				createTimeEnd: dayjs().endOf("day").format('YYYY-MM-DD HH:mm:ss'),
				opTypeCodeList: [],
				refundIdList: [],
				sidList: [],
				ptTidList: []
			};
			SelectOperateLogSummaryApi(param).then((res) => {
				let total = 0;
				total += res.confirmAndUnRefundNum;
				total += res.refundFailedNum;
				total += res.createExchangeTradeFailedNum;
				total += res.returnStockFailedNum;
				total += res.refundExchangeShipFailedNum;
				// total += res.refundSuccessNum;
				// total += res.confirmSuccessNum;
				setShowAfterSaleLogNum(total);
			});
		}
	};

	useEffect(() => {
		const path = location.pathname;
		const menuItem = menus.find((item) => item.path === path);
		if (!menuItem) {
			history.push(menus[0].path);
		}
		setActiveTab(menuItem?.path || menus[0].path);
	}, [history, location.pathname, menus]);

	// * 代理nav下link和a的跳转逻辑
	const goPageRoute = (menuItem: RouteType) => {
		history.push(menuItem.path);
	};

	// 售后管理显示小程序
	const isShowWxAppCodeAftersale = useMemo(() => {
		return menus.some(menu => ['/aftersale/trade', '/aftersale/distributorTrade', '/aftersale/orderLog']?.includes(menu.path));
	}, [menus]);
	
	// 显示同步售后单
	const isShowSyncAfterSaleBtn = useMemo(() => {
		// 售后管理显示同步售后单
		return location.pathname == '/aftersale/trade';
	}, [menus]);

	// 备货单显示小程序
	const isShowWxAppCodeBhd = useMemo(() => {
		return menus.some(menu => ['/report/bhd/BhdList', '/report/bhd/bhdTableSet']?.includes(menu.path));
	}, [menus]);

	// 备货单标签显示小程序
	const isShowWxAppCodeBhdLabel = useMemo(() => {
		return menus.some(menu => ['/stall/bhd/BhdList', '/stall/bhd/bhdTableSet', '/stall/bhd/BhdHotLabelSet']?.includes(menu.path));
	}, [menus]);

	const isShowWxAppCodePrint = useMemo(() => {
		return menus.some(menu => ['/trade/index']?.includes(menu.path));
	}, [menus]);

	// 监控设备
	const isShowMonitoringDeviceTip = useMemo(() => {
		return menus.some(menu => menu.path.includes('/settings/monitoringDevice'));
	}, [menus]);

	const getPlatsList = async() => {
		const plats = await getAllPlats(false, false);
		setPlatformList(plats);
	};

	const isHasTbShops = useMemo(() => {
		return platformList.includes(PLAT_TB) || platformList.includes(PLAT_TM);
	}, [platformList]);

	// 是否有淘宝天猫平台店铺（没有权限的都被接口过滤掉了）
	useEffect(() => {
		if (isShowSyncAfterSaleBtn) {
			getPlatsList();
		}
	}, [isShowSyncAfterSaleBtn]);

	const handleAuthorizeSubAccounts = debounce(() => {
		event.emit(EVENT_BUS.AUTHORIZE_SUB_ACCOUNTS);
	}, 300); 

	return (
		<div className={ s["sub-nav-container"] } style={ props.navStyle }>
			<div className="r-flex r-ai-c r-jc-sb">
				<div className="r-flex-1">
					<Menu
						mode="horizontal"
						onClick={ handleClick }
						selectedKeys={ [activeTab] }
					>
						{menus.map((item) => (
							<Menu.Item
								key={ item.path }
								onClick={ () => {
									goPageRoute(item);
								} }
							>

								{
									afterSaleLogNum && showAfterSaleLogNum && item.name == "售后单日志" ? (
										<Badge count={ showAfterSaleLogNum } offset={ [10, 0] }>{item.name}</Badge>
									) : item.name
								}
							</Menu.Item>
						))}
					</Menu>
				</div>

				{/* 其他右侧内容都放这里 */}
				<div className="r-flex-shrink r-flex r-ai-c r-gap-8 r-mr-16">
					{
						isShowSyncAfterSaleBtn && (
							<Button 
								icon={ <SyncOutlined /> } 
								onClick={ () => event.emit(EVENT_BUS.SYNC_AFTERSALE) } 
							>
								同步售后单
							</Button>
						)
					}

					{
						isShowSyncAfterSaleBtn && isHasTbShops && (
							<Button onClick={ handleAuthorizeSubAccounts }>
								<div className="r-flex r-ai-c r-gap-6">
									<UsergroupAddOutlined />
									<span>授权子账号</span>
									<Tooltip title="使用场景：如有因退款子账号授权引起的退款失败问题，可在此处进行重新授权" placement="bottom" overlayStyle={ { maxWidth: 276 } }>
										<QuestionCircleOutlined className="r-pointer question-icon-custom" />
									</Tooltip>
								</div>
							</Button>
						)
					}

					{autoSendNum && (
						<div
							onClick={ () => {
								sendPoint(Pointer.订单_自动发货_跳转);
								history.push(`/preShip`);
							} }
							style={ {
								position: "absolute",
								bottom: "12px",
								left: `${(menus?.length || 0) * 110}px`,
								cursor: "pointer",
							} }
						>
							自动发货
							{!!showAutoSend && (
								<span>
									（
									<span style={ { color: "red" } }>
										{showAutoSend}
									</span>
									）
								</span>
							)}
						</div>
					)}

					<div className={ s.wxAppCodeContainer }>
						{isShowWxAppCodeAftersale && (
							<WxAppQrCode 
								pointTrackId={ Pointer.售后_售后管理_小程序处理售后展示 }
								bottomText="轻松一扫，售后信息全掌握"
								mainText="手机处理售后"
							/>
						)}
						{isShowWxAppCodeBhd && (
							<WxAppQrCode 
								pointTrackId={ Pointer.备货单_手机看备货单 }
								bottomText="随时随地，查看备货单数量"
								mainText="手机看备货单"
							/>
						)}
						{isShowWxAppCodeBhdLabel && (
							<WxAppQrCode 
								pointTrackId={ Pointer.备货单标签_手机看备货单 }
								bottomText="随时随地，查看备货单数量"
								mainText="手机看备货单"
							/>
						)}
						{isShowWxAppCodePrint && (
							<WxAppQrCode 
								pointTrackId={ Pointer.手机处理订单 }
								bottomText="支持手工下单/扫快递单发货"
								mainText="手机处理订单"
							/>
						)}
						{isShowMonitoringDeviceTip && (
							<div
								className={ s.monitoringDeviceTip }
								onClick={ () => {
									window.open('https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/glroqagd4kb2gwx8?singleDoc', '_blank');
								} }
							>
								IPC摄像头使用教程
							</div>
						)}


					</div>
				</div>
			</div>
		</div>
	);
};

export default memo(observer(SubMenuTab));
