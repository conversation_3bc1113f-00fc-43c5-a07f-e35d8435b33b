.sub-nav-container {
    background: #FFFFFF;
    width: 100%;
    margin: 8px 0;
    position: relative;

    :global {
        .question-icon-custom {
            color: rgba(0, 0, 0, 0.45);
        }

        // 当按钮处于hover状态时，恢复图标颜色为按钮hover颜色
        .ant-btn:hover .question-icon-custom {
            color: inherit;
        }
    }

    .monitoringDeviceTip{
        padding: 5px 16px;
        color: #1890FF;
        cursor: pointer;
        background: #FFF7E6;
        box-sizing: border-box;
        border: 1px solid #FFC069;
        border-radius: 2px;
        font-size: 14px;
    }
}


.sub-root-container {
    min-height: calc(100vh - 154px);
    background-color: #fff;
}

.wxAppCodeContainer {
    position: relative;
    z-index: 98;
    cursor: default;
}