
import 预占库存 from '@/assets/image/versionControl/预占库存.png';
import 搬家铺货 from '@/assets/image/versionControl/搬家铺货.png';
import 售后扫描登记 from '@/assets/image/versionControl/售后扫描登记.png';
import 数据大屏 from '@/assets/image/versionControl/数据大屏.png';
import 物流预警 from '@/assets/image/versionControl/物流预警.png';
import 有货无货筛选 from '@/assets/image/versionControl/有货无货筛选.png';
import 备货单做商品资料 from '@/assets/image/versionControl/备货单做商品资料.png';
import 备货单添加商品 from '@/assets/image/versionControl/备货单添加商品.png';
import 首页续订 from '@/assets/image/versionControl/首页续订.png';
import 快递拦截 from '@/assets/image/versionControl/快递拦截.png';
import 赠品策略 from '@/assets/image/versionControl/赠品策略.png';
import 智能做商品编码 from '@/assets/image/versionControl/智能做商品编码@2x.png';
import 子账号数量 from '@/assets/image/versionControl/子账号数量@2x.png';
import 抖音买家昵称打印 from '@/assets/image/versionControl/抖音买家昵称打印.png';


import Pointer from '@/utils/pointTrack/constants';

export interface IVersionControlItem {
	title: string,
	id: string,
	icon: string,
	img: any,
	showPoint?: string,
	clickPoint?: string,
}


export enum PageNameControlEnum {
	首页 = "index",
	售后扫描 = "afterSaleScan",
	物流预警 = "logistics",
	预占库存 = "occupied",
	小标签半备货模式 = "takeGoodsLabel",
	数据大屏 = "salesScreen",
	快递拦截 = "logisticsIntercept",
	赠品策略 = 'giftStrategy',
	动态库存预警 = "stockDynamicWarn",
	小程序售后扫描登记 = "afterSaleScanWxapp",
	小程序批量快递登记 = "afterSaleScan",
	商品推荐合并 = "itemRecommendMerge",
	子账号数量 = "subAccountNum",
	售后自动化_系统策略 = "afterSaleAutomationStrategy",
	销售毛利率报表 = "reportSaleProfit",
	搬家铺货 = 'shangjiapuhuo',
	进销存报表 = "reportStock",
	销售分析 = "reportSalesAnalysis",
	售后分析 = "reportRefundAnalysis",
	备货单做商品资料 = "bhdEditItemInfoFlag",
	备货单添加商品 = "bhdAddItemFlag",
	index = "首页",
	afterSaleScan = "售后扫描",
	logistics = "物流预警",
	occupied = "预占库存",
	takeGoodsLabel = "小标签半备货模式",
	salesScreen = "数据大屏",
	logisticsIntercept = "快递拦截",
	stockDynamicWarn = "动态库存预警",
	afterSaleScanWxapp = "小程序售后扫描登记",
	itemRecommendMerge = "商品推荐合并",
	抖音昵称打印 = "nickNamePrint",
	afterSaleAutomationStrategy = "售后自动化_系统策略",
	reportSaleProfit = "销售毛利率报表",
	reportStock = "进销存报表",
	reportSalesAnalysis = "销售分析",
	reportRefundAnalysis = "售后分析",
	bhdEditItemInfoFlag = "备货单做商品资料",
	bhdAddItemFlag = "备货单添加商品",
}


export enum VersionEnum {
	免费版 = 0,
	老标配版 = 1,
	高级版 = 2,
	新标配版 = 3,
}


export enum UserAuthorityStatus {
	关闭 = 2,
	开启 = 1
}

export const VersionControlList: IVersionControlItem[] = [
	{
		title: '升级续订',
		id: PageNameControlEnum.首页,
		icon: "a-1shengjixufei",
		img: 首页续订,
		showPoint: Pointer.版本控制_弹窗_升级续订_展示,
		clickPoint: Pointer.版本控制_弹窗_升级续订_点击
	}, {
		title: '物流预警',
		id: PageNameControlEnum.物流预警,
		icon: "a-2wuliuyujing",
		img: 物流预警,
		showPoint: Pointer.版本控制_弹窗_物流预警_展示,
		clickPoint: Pointer.版本控制_弹窗_物流预警_点击
	}, {
		title: '预占库存',
		id: PageNameControlEnum.预占库存,
		icon: "a-3yuzhankucun",
		img: 预占库存,
		showPoint: Pointer.版本控制_弹窗_预占库存_展示,
		clickPoint: Pointer.版本控制_弹窗_预占库存_点击
	}, {
		title: '有货/无货筛选',
		id: PageNameControlEnum.小标签半备货模式,
		icon: "a-4youhuowuhuoshaixuan",
		img: 有货无货筛选,
		showPoint: Pointer.版本控制_弹窗_有无货筛选_展示,
		clickPoint: Pointer.版本控制_弹窗_有无货筛选_点击
	}, {
		title: '备货单做商品资料',
		id: PageNameControlEnum.备货单做商品资料,
		icon: "beihuodanzuoshangpinziliao",
		img: 备货单做商品资料,
		showPoint: Pointer.报表_备货单_编辑商品_升级弹框_展现,
		clickPoint: Pointer.报表_备货单_编辑商品_升级弹框_点击
	}, {
		title: '备货单添加商品',
		id: PageNameControlEnum.备货单添加商品,
		icon: "pingtaishangpinguanli",
		img: 备货单添加商品,
		showPoint: Pointer.报表_备货单_添加商品_升级弹框_展现,
		clickPoint: Pointer.报表_备货单_添加商品_升级弹框_点击
	}, {
		title: '售后扫描登记',
		id: PageNameControlEnum.售后扫描,
		icon: "a-6shouhousaomiao",
		img: 售后扫描登记,
		showPoint: Pointer.版本控制_弹窗_售后扫描登记_展示,
		clickPoint: Pointer.版本控制_弹窗_售后扫描登记_点击
	}, {
		title: '数据大屏',
		id: PageNameControlEnum.数据大屏,
		icon: "a-5shujudaping",
		img: 数据大屏,
		showPoint: Pointer.版本控制_弹窗_实时扫描大屏_展示,
		clickPoint: Pointer.版本控制_弹窗_实时扫描大屏_点击
	}, {
		title: '铺货管理',
		id: PageNameControlEnum.搬家铺货,
		icon: "banjiapuhuopeizhi",
		img: 搬家铺货,
		// showPoint: Pointer.版本控制_弹窗_升级续订_展示,
		// clickPoint: Pointer.版本控制_弹窗_升级续订_点击
	}, {
		title: '快递拦截',
		id: PageNameControlEnum.快递拦截,
		icon: "kuaidilanjie",
		img: 快递拦截,
		// showPoint: Pointer.版本控制_弹窗_供销代发_展示,
		// clickPoint: Pointer.版本控制_弹窗_供销代发_点击
	}, {
		title: '赠品策略',
		id: PageNameControlEnum.赠品策略,
		icon: "zidongzengpincelve",
		img: 赠品策略,
		// showPoint: Pointer.版本控制_弹窗_供销代发_展示,
		// clickPoint: Pointer.版本控制_弹窗_供销代发_点击
	},
	{
		title: '子账号数量',
		id: PageNameControlEnum.子账号数量,
		icon: "zizhanghaoshuliang",
		img: 子账号数量,
		// showPoint: Pointer.版本控制_弹窗_供销代发_展示,
		// clickPoint: Pointer.版本控制_弹窗_供销代发_点击
	},
	{
		title: '智能做商品编码',
		id: PageNameControlEnum.商品推荐合并,
		icon: "zhinengbianma",
		img: 智能做商品编码,
		// showPoint: Pointer.版本控制_弹窗_供销代发_展示,
		// clickPoint: Pointer.版本控制_弹窗_供销代发_点击
	}, {
		title: '抖音昵称打印',
		id: PageNameControlEnum.抖音昵称打印,
		icon: "douyinmaijianicheng",
		img: 抖音买家昵称打印,
		// showPoint: Pointer.版本控制_弹窗_供销代发_展示,
		// clickPoint: Pointer.版本控制_弹窗_供销代发_点击
	}   
];

// ui出图了之后在VersionControlList加上去 把这里面删掉
export const noTitleList:string[] = [
	PageNameControlEnum.售后自动化_系统策略,
	PageNameControlEnum.售后分析,
	PageNameControlEnum.销售分析
];
