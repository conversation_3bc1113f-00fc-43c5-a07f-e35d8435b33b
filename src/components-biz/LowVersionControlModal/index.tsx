import React, { useEffect, useState, useRef } from "react";
import { observer } from "mobx-react-lite";
import cs from 'classnames';
import { Modal } from "antd";
import s from "./index.module.scss";
import userStore from "@/stores/user";
import { getPayOrderURL } from "@/utils/util";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { TradeDictQueryDictApi } from "@/apis/trade/search";
import { IVersionControlItem, noTitleList, PageNameControlEnum, UserAuthorityStatus, VersionControlList, VersionEnum } from "./constant";
import Icon from "@/components/Icon";
import 首页续订 from '@/assets/image/versionControl/首页续订.png';

// 功能控制 如果tj关闭对应功能 返回true
export const lowVersionLock = async(pageName) => {
	// 高级版不去判断页面功能开关
	const { level } = await userStore.getUserInfo();
	if (level === VersionEnum.高级版) return false;
	if (pageName === 'subAccountNum') return true;
	if (pageName === 'shangjiapuhuo') return true;
	// 抖音昵称不走白名单
	if (pageName == PageNameControlEnum.抖音昵称打印) return true;
	const res = await userStore.getUserAuthoritySet(true); // 是否每次重新请求下
	if (res?.[pageName] === UserAuthorityStatus.关闭) return true;
	return false;
};

const LowVersionControlModal: React.FC<{pageName: string, closable?: boolean, centered?:boolean, onCancel?: Function}> = (props) => {
	const { pageName, closable = false, onCancel, centered = false } = props;
	const [highVersionOutModalShow, setHighVersionOutModalShow] = useState(false);
	const [activeItem, setActiveItem] = useState<IVersionControlItem>(VersionControlList[0]);

	const subTitleRef = useRef<HTMLUListElement>(null);
	const { userInfo } = userStore;

	// 滚动到指定的tab
	const scrollToActiveTab = (itemId: string) => {
		if (!subTitleRef.current) return;
		
		const activeTabElement = subTitleRef.current.querySelector(`[data-item-id="${itemId}"]`) as HTMLElement;
		if (activeTabElement) {
			activeTabElement.scrollIntoView({
				behavior: 'smooth',
				block: 'center'
			});
		}
	};

	useEffect(() => {
		// if (!userAuthoritySetting) return;
		const fn = async() => {
			const { level } = userInfo;
			if ([VersionEnum.免费版, VersionEnum.新标配版, VersionEnum.老标配版].includes(level) && await lowVersionLock(pageName)) {
				setHighVersionOutModalShow(true);
				// 修改这里：先检查是否在 noTitleList 中，如果是则使用首页续订，否则查找对应的功能项
				let _activeItem;
				if (noTitleList.includes(pageName)) {
					// 如果是 noTitleList 中的功能，直接使用升级续订
					_activeItem = VersionControlList.find(item => item.id === PageNameControlEnum.首页);
				} else {
					// 否则查找对应的功能项
					_activeItem = VersionControlList?.find((item) => (pageName === item.id));
				}
				
				if (_activeItem) {
					setActiveItem(_activeItem);
					sendPoint(_activeItem.showPoint);
					// 延迟滚动，确保DOM已渲染
					setTimeout(() => {
						scrollToActiveTab(_activeItem.id);
					}, 100);
				}
			}
		};
		fn();
		return () => {
			setHighVersionOutModalShow(false);
		};
	}, [userInfo]);

	const upGrade = () => {
		sendPoint(Pointer.版本对比_立即升级);
		// if (pageName === 'scanRegister') sendPoint(Pointer.售后扫描登记_标配_升级);
		// if (pageName === 'logisticsWarning') sendPoint(Pointer.物流预警_标配_升级);
		window.open(getPayOrderURL(userInfo?.userId), '_blank');
	};

	const updateSelectedKey = (item) => {
		sendPoint(item.clickPoint);
		setActiveItem({ ...item });
	};

	return (
		<>
			{/* 高配版到期 */}
			<Modal
				visible={ highVersionOutModalShow }
				closable={ closable }
				maskClosable={ false }
				keyboard={ false }
				width={ 780 }
				footer={ null }
				centered={ centered }
				wrapClassName={ s.versionLockWrap }
				className={ s.expireModal }
				maskStyle={ { top: 96 } }
				zIndex={ 990 }
				onCancel={ () => { setHighVersionOutModalShow(false); sendPoint(Pointer.版本控制_弹窗_关闭); onCancel && onCancel(); } }
			>
				<div className={ s.expireModalContainer }>
					<ul className={ cs(s.subTitle) } ref={ subTitleRef }>
						{
							VersionControlList.map((item: any) => {
								return (
									<li
										key={ item.title }
										data-item-id={ item.id }
										className={ `${activeItem.id === item.id ? s.active : ''}` }
									>
										<div className={ cs(s.link, s.urlLink) } onClick={ () => { updateSelectedKey(item); } }>
											<Icon style={ { verticalAlign: 'middle' } } type={ item.icon } className="r-pointer r-mr-4" />
											{item.title}
										</div>
									</li>
								);
							})
						}
					</ul>
					<div className={ s.expireModalContent }>
						<div className={ s.expireModalContentTop } style={ { backgroundImage: `url(${activeItem.img || 首页续订})` } }>
							<div className={ s.close } onClick={ () => {} } />
						</div>
						<div className={ s.expireModalContentBottom } onClick={ () => { upGrade(); } } />
					</div>
				</div>

			</Modal>

		</>

	);
};
export default observer(LowVersionControlModal);
