@use "../../assets/styles/theme.scss" as *;

.versionLockWrap {
	top: 96px;
	:global {
		.ant-modal {
			top: 160px
		}
	}
	
}
.expireModalWrapper_high {
	background-image: url('../../assets/image/notice/高级版_已过期.png');
	height: 369px;
}
.expireModalWrapper {
	.willExpireModalFooter{
		height: 74px;
		position: relative;
		top: 295px;
		text-align: center;

		.footerButton {
			width: 420px;
			font-size: 20px;
			height: 50px;
		}
	}
}
.expireModal {
	display: block;

	:global {
		.ant-modal-content{
			// border-radius: 8px;

		}
		.ant-modal-body{
			padding: 0 !important;
		}
	}
}

.subTitle {
	width: 180px;
	height: 550px;
	margin-bottom: 0;
	background-color: white;
	overflow-y: auto; // 添加垂直滚动

	// 自定义滚动条样式
	&::-webkit-scrollbar {
		width: 6px;
	}
	
	&::-webkit-scrollbar-track {
		background: #f5f5f5;
		border-radius: 3px;
	}
	
	&::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
		
		&:hover {
			background: #a8a8a8;
		}
	}

	a {
		color: rgba(0, 0, 0, 0.65);
	}
	.urlLink {
		color: rgba(0, 0, 0, 0.65);
	}
	.active {
		background: #f7e1cc;
		border-left: 2px solid $color-primary;

		a, .urlLink {
			color: $color-primary;
		}

	}

	li {
		height: 46px;
		line-height: 46px;
		cursor: pointer;
		padding-left: 16px;
		&.active {
			a {
				color: $color-primary;
			}
		}
	}
	li:hover {
		background: #f7e1cc;
	}
}
.expireModalContainer {
	background-color: #F5F5F5;
	display: flex;
	width: 100%;
}
.expireModalContent {
	background: white;
	padding: 8px;
	.expireModalContentTop {
		width: 584px;
		height: 402px;
		margin-bottom: 12px;
		background-position: center 0;
		background-repeat: no-repeat;
		background-size: contain;
		position: relative;
		.close {
			position: absolute;
			top: 0;
			right: 0;
			width: 40px;
			height: 40px;
			// background-color: pink;
		}
	}
	.expireModalContentBottom {
		cursor: pointer;
		height: 116px;
		background-image: url('../../assets/image/versionControl/底部公用.png');
		background-position: center 0;
		background-repeat: no-repeat;
		background-size: contain;
	}
}
