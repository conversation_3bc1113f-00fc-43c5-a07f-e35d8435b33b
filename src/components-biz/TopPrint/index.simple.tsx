import React, { useEffect, useImperativeHandle, useRef, useState, useCallback, useMemo } from "react";
import { Checkbox, Empty, Input, Popover, Select, Spin, Radio, Tooltip } from 'antd';
import cs from "classnames";
import { useRequest } from "ahooks";
import { observer } from 'mobx-react';
import { SearchOutlined } from "@ant-design/icons";
import { intersection, set } from "lodash";
import { VariableSizeList, ListChildComponentProps } from 'react-window';
import useGetState from '@/utils/hooks/useGetState';
import styles from './index.module.scss';
import s from './index.simple.module.scss';
import TopPrintBySku from '@/assets/image/trade/爆款打单-按款.svg';
import { TradeQueryTradeItemCountApi, TradePrintSetUpdatePrintSetApi } from "@/apis/trade";
import { tradeStore } from '@/stores';
import userStore from '@/stores/user';

import { TopPrintSearchItemTopMode, TopPrintSearchModel, TopPrintSearchPerspective } from "@/pages/Trade/constants";
import { PLAT_HAND, DEFAULT_IMG } from "@/constants";
import PlatformIcon from "@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon";
import memoFn from "@/libs/memorizeFn";
import Icon from "@/components/Icon";
import { local } from '@/libs/db';

const topItemShowDefaultValue = {
	simpleItemPerspective: ["1", "21"],
	simpleSysPerspective: ["1"],
};
const topSkuShowDefaultValue = {
	simpleItemPerspective: ["1", "21"],
	simpleSysPerspective: ["1"],
};

const TopPrint = (props) => {
	const { searchParamsMd5, oldSearchParamsMd5, setOldSearchParamsMd5, storeSearchParams } = tradeStore;
	const { isShowZeroStockVersion, userInfo } = userStore;
	const { onChange } = props;
	const mainContainer = useRef(null);
	const [model, setModel] = useState();
	const [topSetting, setTopSetting] = useState<any>({});
	const [visible, setVisible] = useState(false);
	const [itemId, setItemId] = useGetState(() => []);
	const [skuId, setSkuId] = useGetState([]);

	const [itemList, setItemList] = useGetState([]);
	const [skuList, setSkuList] = useGetState([]);
	const [newSkuList, setNewSkuList] = useGetState([]);

	const [itemKeyWords, setItemKeyWords] = useGetState('');
	const [skuKeyWords, setSkuKeyWords] = useGetState('');
	const { runAsync, loading } = useRequest(TradeQueryTradeItemCountApi, { manual: true });

	const tabNum = 2;// tab数量

	const [itemRowHeights, setItemRowHeights] = useState({});
	const [skuRowHeights, setSkuRowHeights] = useState({});
	const itemListRef = useRef(null);
	const skuListRef = useRef(null);
	// 在组件顶部添加排序状态
	const [itemSortByName, setItemSortByName] = useState(false);
	const [skuSortByName, setSkuSortByName] = useState(false);

	useEffect(() => {
		if (userInfo?.userId) {
			const key = `TopPrint_SortByName_${userInfo?.userId}_${userInfo?.subUserId}`;
			const value = local.get(key);
			if (value) {
				setItemSortByName(!!value?.itemSortByName);
				setSkuSortByName(!!value?.skuSortByName);
			}
		}
	}, [userInfo]);

	// 添加一个 ref 来引用下拉框容器
	const dropdownRef = useRef(null);
	const selectRef = useRef(null);

	// 添加点击事件处理函数
	useEffect(() => {
		const handleClickOutside = (event) => {
			// 如果下拉框是打开的，并且点击不在下拉框内也不在 Select 组件内
			if (visible
				&& dropdownRef.current
				&& !dropdownRef.current.contains(event.target)
				&& selectRef.current
				&& !selectRef.current.contains(event.target)) {

				setVisible(false);
				const data = {
					mode: TopPrintSearchModel.MODEL_SKU,
					selectedSkus: skuId.map(s => s?.split('_')?.[1]),
					selectedGoods: itemId,
					perspective: props.perspective,
					type: TopPrintSearchItemTopMode.open
				};
				onChange(data);
			}
		};

		// 添加事件监听
		document.addEventListener('mousedown', handleClickOutside);

		// 清理函数
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [visible, skuId, itemId]);


	// 使用useMemo缓存过滤后的结果
	const filteredItems = useMemo(() => {
		let filtered = itemList?.filter(s => s?.itemTitle?.includes(itemKeyWords)
			|| s?.sysItemAlias?.includes(itemKeyWords)) || [];
		let filterField = "sysItemAlias";
		if (((isShowZeroStockVersion && topSetting?.topItemShow?.simpleItemPerspective?.includes("1"))
			|| (!isShowZeroStockVersion && model === TopPrintSearchPerspective.itemPerspective))) {
			filterField = "itemTitle";
		}
		if (itemSortByName) {
			filtered = [...filtered].sort((a, b) => {
				const nameA = a[filterField];
				const nameB = b[filterField];
				return nameA.localeCompare(nameB);
			});
		}
		return filtered;
	}, [itemList, itemKeyWords, itemSortByName, isShowZeroStockVersion, topSetting, model]);

	const filteredSkus = useMemo(() => {
		let filtered = newSkuList?.filter(s => s?.skuTitle?.includes(skuKeyWords)
			|| s?.sysSkuAlias?.includes(skuKeyWords)) || [];
		let filterField = "sysSkuAlias";
		if (((isShowZeroStockVersion && topSetting?.topSkuShow?.simpleItemPerspective?.includes("1")) || !isShowZeroStockVersion)) {
			filterField = "skuTitle";
		}
		if (skuSortByName) {
			filtered = [...filtered].sort((a, b) => {
				const nameA = a[filterField];
				const nameB = b[filterField];
				return nameA.localeCompare(nameB);
			});
		}
		return filtered;
	}, [newSkuList, skuKeyWords, skuSortByName, isShowZeroStockVersion, topSetting]);

	const getHotItemList = () => {
		const { sysSkuId, sysItemId, skuId: skuId2, itemId: itemId2, ...rest } = storeSearchParams;
		runAsync(rest as any).then(res => {
			setItemList(res?.itemCountList);
			// 重匹配现有选择项
			const oldItemId = [...itemId];
			const oldSkuId = [...skuId];
			if (oldItemId.length) {
				const newItemIds = res?.itemCountList?.map(s => s.itemIds);
				const newSkuId = [];
				const diffItemId = intersection(oldItemId, newItemIds);
				oldSkuId.forEach(id => {
					if (diffItemId.find(s => id.startsWith(s))) {
						newSkuId.push(id);
					}
				});
				// res?.itemCountList.forEach(element => {
				// 	if (!oldItemId.includes(element.itemId)) {
				// 		remove(oldItemId, n => n == element.itemId);
				// 		remove(oldSkuId, n => n.startsWith(element.itemId));
				// 	}
				// });
				setItemId(diffItemId);
				setSkuId(newSkuId);
			}
		});
	};

	useEffect(() => {
		memoFn.getAdvancedSet().then(res => {
			setModel(res?.itemTopType ?? TopPrintSearchPerspective["itemPerspective"]);
			const topItemShow = JSON.parse(res?.printSetExpandDTO.topItemShow);
			const topSkuShow = JSON.parse(res?.printSetExpandDTO.topSkuShow);
			if (!topItemShow.simpleItemPerspective) {
				topItemShow.simpleItemPerspective = topItemShowDefaultValue.simpleItemPerspective;
			}
			if (!topItemShow.simpleSysPerspective) {
				topItemShow.simpleSysPerspective = topItemShowDefaultValue.simpleSysPerspective;
			}
			if (!topSkuShow.simpleItemPerspective) {
				topSkuShow.simpleItemPerspective = topSkuShowDefaultValue.simpleItemPerspective;
			}
			if (!topSkuShow.simpleSysPerspective) {
				topSkuShow.simpleSysPerspective = topSkuShowDefaultValue.simpleSysPerspective;
			}
			setTopSetting({
				...res?.printSetExpandDTO,
				topItemShow,
				topSkuShow,
			});
		});
	}, [visible]);

	useEffect(() => {
		let skuList = [];
		let skuIdList = [];
		if (itemId.length) {
			itemList?.forEach(item => {
				if (itemId.includes(item.itemIds)) {
					const skuCountList = item.skuCountList.map(s => ({
						...s,
						itemId: item.itemIds,
						skuId: `${item.itemIds}_${s.skuIds}`
					}));
					console.log('skuCountList', skuCountList, item.itemIds, skuIdList, skuId);
					skuList.push(...skuCountList);
				}
			});
			skuIdList = skuId.filter(s => skuList.some(sku => s.startsWith(sku.itemId)));
		} else {
			skuIdList = [];
		}
		setSkuList(() => skuList);
		setSkuId(() => skuIdList);
		// 删除下面这行，这样规格名称搜索框的内容就不会在选择不同商品时被重置
		// setSkuKeyWords('');
	}, [itemId]);


	useEffect(() => {
		const data = {
			mode: TopPrintSearchModel.MODEL_SKU,
			selectedSkus: skuId,
			selectedGoods: itemId,
			perspective: props.perspective,
			type: TopPrintSearchItemTopMode.open
		};
		onChange(data);
	}, [itemId, skuId]);

	useEffect(() => {
		const { simpleItemPerspective } = topSetting?.topSkuShow ?? {};
		let skuListCache = [...skuList];
		let skuListTemp = {};
		if (isShowZeroStockVersion && simpleItemPerspective?.includes("2") && simpleItemPerspective?.includes("12")) {
			skuList.forEach(item => {
				if (!skuListTemp[item.sysSkuAlias]) {
					skuListTemp[item.sysSkuAlias] = [];
				}
				skuListTemp[item.sysSkuAlias].push(item);
			});
			skuListCache = Object.keys(skuListTemp).map(item => {
				let skuTemp = skuListTemp[item][0];
				let skuIds = [];
				let totalNum = 0;
				let tradeNum = 0;
				skuListTemp[item]?.forEach(item => {
					skuIds.push(item.skuIds);
					totalNum += item.totalNum;
					tradeNum += item.tradeNum;
				});
				return {
					...skuTemp,
					skuId: `${skuTemp.skuId.split("_")[0]}_${skuIds.join(",")}`,
					totalNum,
					tradeNum
				};
			});
		}
		setNewSkuList(skuListCache);
	}, [topSetting, skuList]);

	useEffect(() => {
		if (storeSearchParams && searchParamsMd5 !== oldSearchParamsMd5 && visible) {
			getHotItemList();
			setOldSearchParamsMd5(searchParamsMd5);
		}
	}, [searchParamsMd5, oldSearchParamsMd5, visible, storeSearchParams]);

	// useEffect(() => {
	// 	getHotItemList();
	// 	setOldSearchParamsMd5(searchParamsMd5);
	// }, []);

	const onChangeItem = e => {
		setItemId(() => e);
	};
	const onChangeSku = e => { setSkuId(() => e); };

	const onSettingChange = async(type, field, e) => {
		let needRefreshData = false;
		const settingCache = await memoFn.getAdvancedSet();
		console.log(type, field, e);
		const curSetting = { ...topSetting };
		if (type === "topItemShow" && field === "simpleItemPerspective") {
			if (["1", "2"].includes(e.target.value)) {
				needRefreshData = true;
				if (e.target.value === "1") {
					curSetting.topItemShow.simpleItemPerspective = curSetting.topItemShow.simpleItemPerspective.filter(item => item !== "2");
				} else {
					curSetting.topItemShow.simpleItemPerspective = curSetting.topItemShow.simpleItemPerspective.filter(item => item !== "1");
				}
				curSetting.topItemShow.simpleItemPerspective.push(e.target.value);
				curSetting.topItemShow.simpleItemPerspective = Array.from(new Set(curSetting.topItemShow.simpleItemPerspective));
			} else if (e.target.checked) {
				curSetting.topItemShow.simpleItemPerspective.push(e.target.value);
				curSetting.topItemShow.simpleItemPerspective = Array.from(new Set(curSetting.topItemShow.simpleItemPerspective));
			} else {
				curSetting.topItemShow.simpleItemPerspective = curSetting.topItemShow.simpleItemPerspective.filter(item => item !== e.target.value);
			}
			if (e.target.value === "12") {
				needRefreshData = true;
			}
		}
		if (type === "topItemShow" && field === "simpleSysPerspective") {
			needRefreshData = true;
			if (e.target.checked) {
				curSetting.topItemShow.simpleSysPerspective.push(e.target.value);
				curSetting.topItemShow.simpleItemPerspective = Array.from(new Set(curSetting.topItemShow.simpleItemPerspective));
			} else {
				curSetting.topItemShow.simpleSysPerspective = curSetting.topItemShow.simpleSysPerspective.filter(item => item !== e.target.value);
			}
		}
		if (type === "topSkuShow" && field === "simpleItemPerspective") {
			needRefreshData = true;
			if (["1", "2"].includes(e.target.value)) {
				if (e.target.value === "1") {
					curSetting.topSkuShow.simpleItemPerspective = curSetting.topSkuShow.simpleItemPerspective.filter(item => item !== "2");
				} else {
					curSetting.topSkuShow.simpleItemPerspective = curSetting.topSkuShow.simpleItemPerspective.filter(item => item !== "1");
				}
				curSetting.topSkuShow.simpleItemPerspective.push(e.target.value);
				curSetting.topSkuShow.simpleItemPerspective = Array.from(new Set(curSetting.topSkuShow.simpleItemPerspective));
			} else if (e.target.checked) {
				curSetting.topSkuShow.simpleItemPerspective.push(e.target.value);
				curSetting.topSkuShow.simpleItemPerspective = Array.from(new Set(curSetting.topSkuShow.simpleItemPerspective));
			} else {
				curSetting.topSkuShow.simpleItemPerspective = curSetting.topSkuShow.simpleItemPerspective.filter(item => item !== e.target.value);
			}
			if (e.target.value === "11") {
				needRefreshData = false;
			}
		}
		if (type === "topSkuShow" && field === "simpleSysPerspective") {
			if (e.target.checked) {
				curSetting.topSkuShow.simpleSysPerspective.push(e.target.value);
				curSetting.topSkuShow.simpleSysPerspective = Array.from(new Set(curSetting.topSkuShow.simpleSysPerspective));
			} else {
				curSetting.topSkuShow.simpleSysPerspective = curSetting.topSkuShow.simpleSysPerspective.filter(item => item !== e.target.value);
			}
		}
		settingCache.printSetExpandDTO = {
			itemTopMode: curSetting.itemTopMode,
			topItemShow: JSON.stringify(curSetting.topItemShow),
			topSkuShow: JSON.stringify(curSetting.topSkuShow),
		};
		await TradePrintSetUpdatePrintSetApi(settingCache);
		memoFn.updateAdvancedSet(settingCache);
		setTopSetting(curSetting);
		if (needRefreshData) {
			getHotItemList();
		}
	};

	const getItemHeight = (index) => {
		return itemRowHeights[index] || 40; // 默认高度
	};

	const getSkuHeight = (index) => {
		return skuRowHeights[index] || 40; // 默认高度
	};

	const setItemRef = useCallback((index, node) => {
		if (node) {
			const height = node.getBoundingClientRect().height;
			setItemRowHeights(prev => {
				// 如果高度没有变化，则不更新状态，避免无限循环
				if (prev[index] === height) {
					return prev;
				}
				const newHeights = { ...prev };
				newHeights[index] = height;
				// 只有当高度变化时，才重置列表
				if (itemListRef.current) {
					itemListRef.current.resetAfterIndex(index);
				}
				return newHeights;
			});
		}
	}, []);

	const setSkuRef = useCallback((index, node) => {
		if (node) {
			const height = node.getBoundingClientRect().height;
			setSkuRowHeights(prev => {
				// 如果高度没有变化，则不更新状态，避免无限循环
				if (prev[index] === height) {
					return prev;
				}
				const newHeights = { ...prev };
				newHeights[index] = height;
				// 只有当高度变化时，才重置列表
				if (skuListRef.current) {
					skuListRef.current.resetAfterIndex(index);
				}
				return newHeights;
			});
		}
	}, []);

	const ItemRow = React.memo(({ index, style }: ListChildComponentProps) => {
		const item = filteredItems[index];
		if (!item) return null;

		return (
			<div
				style={ style }
			>
				<div ref={ node => setItemRef(index, node) }>
					<Checkbox
						key={ item.itemIds }
						value={ item.itemIds }
						checked={ itemId.includes(item.itemIds) }
						onChange={ (e) => {
							const newItemId = [...itemId];
							if (e.target.checked) {
								newItemId.push(item.itemIds);
							} else {
								const index = newItemId.indexOf(item.itemIds);
								if (index > -1) {
									newItemId.splice(index, 1);
								}
							}
							setItemId(newItemId);
						} }
					>
						<div className={ s.item } style={ { display: 'flex' } } onClick={ (e) => e.stopPropagation() }>
							<span style={ { position: 'relative', top: '3px', display: item.platform ? 'block' : 'none' } }>
								{
									item.platform?.toLocaleLowerCase() === PLAT_HAND ? (
										<span >
											<PlatformIcon fontSize={ 16 } platform={ item.platform } />
										</span>
									) : (
										<Popover
											zIndex={ 1060 }
											placement="right"
											content={
												<div>店铺：{item.sellerNick}</div>
											}
										>
											<span>
												<PlatformIcon fontSize={ 16 } platform={ item.platform } />
											</span>
										</Popover>
									)
								}
							</span>
							{
								((isShowZeroStockVersion && topSetting?.topItemShow?.simpleItemPerspective?.includes("1") && topSetting.topItemShow.simpleItemPerspective.includes("11"))
									|| (!isShowZeroStockVersion && model === TopPrintSearchPerspective.itemPerspective && topSetting?.topItemShow?.simpleItemPerspective?.includes("21") && topSetting.topItemShow.simpleItemPerspective.includes("31"))) && (
									<span className="r-mr-8">
										<Popover
											zIndex={ 1060 }
											placement="right"
											content={ (
												<div>
													<img width={ 300 } height={ 300 } src={ item.picPath || DEFAULT_IMG } alt="" style={ { objectFit: 'contain' } } />
												</div>
											) }
										>
											<img width={ 32 } height={ 32 } src={ item.picPath || DEFAULT_IMG } alt="" style={ { cursor: 'pointer' } } />
										</Popover>
									</span>
								)
							}
							<div
								className={ s.selectableText }
								onMouseDown={ (e) => e.stopPropagation() }
								onMouseUp={ (e) => e.stopPropagation() }
							>
								{((isShowZeroStockVersion && topSetting?.topItemShow?.simpleItemPerspective?.includes("1"))
            || (!isShowZeroStockVersion && model === TopPrintSearchPerspective.itemPerspective))
									? `${item.itemTitle}_${item.itemIds}`
									: item.sysItemAlias}
								<span className={ s.numInfo }>（订单{item.tradeNum}/数量{item.totalNum}）</span>
							</div>
						</div>
					</Checkbox>
				</div>
			</div>
		);
	});

	const SkuRow = React.memo(({ index, style }: ListChildComponentProps) => {
		const sku = filteredSkus[index];
		if (!sku) return null;

		return (
			<div style={ style }>
				<div ref={ node => setSkuRef(index, node) }>
					<Checkbox
						key={ `${sku.skuId}` }
						value={ sku.skuId }
						checked={ skuId.includes(sku.skuId) }
						className="r-mt-10"
						onChange={ (e) => {
							const newSkuId = [...skuId];
							if (e.target.checked) {
								newSkuId.push(sku.skuId);
							} else {
								const index = newSkuId.indexOf(sku.skuId);
								if (index > -1) {
									newSkuId.splice(index, 1);
								}
							}
							setSkuId(newSkuId);
						} }
					>
						<div className="r-flex" onClick={ (e) => e.stopPropagation() }>
							{
								((isShowZeroStockVersion && topSetting?.topSkuShow?.simpleItemPerspective?.includes("1") && topSetting?.topSkuShow?.simpleItemPerspective?.includes("11"))
									|| (!isShowZeroStockVersion && model !== TopPrintSearchPerspective.itemPerspective && topSetting.topSkuShow.simpleSysPerspective.includes("11"))
									|| (!isShowZeroStockVersion && model === TopPrintSearchPerspective.itemPerspective && topSetting.topSkuShow.simpleItemPerspective.includes("11"))) && (
									<span className="r-mr-8">
										<Popover
											zIndex={ 1060 }
											placement="right"
											content={ (
												<div>
													<img width={ 300 } height={ 300 } src={ sku.picPath || DEFAULT_IMG } alt="" style={ { objectFit: 'contain' } } />
												</div>
											) }
										>
											<img width={ 32 } height={ 32 } src={ sku.picPath || DEFAULT_IMG } alt="" style={ { cursor: 'pointer' } } />
										</Popover>
									</span>
								)
							}
							<div
								className={ `${s.skuItem} ${s.selectableText}` }
								onMouseDown={ (e) => e.stopPropagation() }
								onMouseUp={ (e) => e.stopPropagation() }
							>
								{
									((isShowZeroStockVersion && topSetting?.topSkuShow?.simpleItemPerspective?.includes("1")) || !isShowZeroStockVersion)
										? sku.skuTitle : sku.sysSkuAlias
								}
								<span className={ s.numInfo }>(订单{sku.tradeNum}/数量{sku.totalNum})</span>
							</div>
						</div>
					</Checkbox>
				</div>
			</div>
		);
	});

	const searchPlaceholder = useMemo(() => {
		let itemPlaceholder = '请输入简称';
		let skuPlaceholder = '请输入规格别名';
		if (((isShowZeroStockVersion && topSetting?.topItemShow?.simpleItemPerspective?.includes("1"))
			|| (!isShowZeroStockVersion && model === TopPrintSearchPerspective.itemPerspective))) {
			itemPlaceholder = "请输入商品名称";
		}
		if (((isShowZeroStockVersion && topSetting?.topSkuShow?.simpleItemPerspective?.includes("1"))
			|| !isShowZeroStockVersion)) {
			skuPlaceholder = "请输入规格名称";
		}
		return { itemPlaceholder, skuPlaceholder };
	}, [isShowZeroStockVersion, topSetting]);

	const dropdownRender = () => {
		if (!itemList.length) {
			return (
				<div
					ref={ dropdownRef }
					className={ s.dropdownContent }
					style={ { justifyContent: 'center', alignItems: 'center' } }
					onClick={ (e) => e.stopPropagation() }
					onMouseDown={ (e) => e.stopPropagation() }
				> <Empty style={ { minHeight: '300px', flexDirection: 'column', display: 'flex', justifyContent: 'center', alignItems: 'center' } } />
				</div>
			);
		}
		return (
			<div
				ref={ dropdownRef }
				className={ s.dropdownContent }
				onClick={ (e) => e.stopPropagation() }
				onMouseDown={ (e) => e.stopPropagation() }
			>
				<div className={ s.itemSelect }>
					<div>
						<div className={ s.inputWrap }>
							<Input
								placeholder={ searchPlaceholder.itemPlaceholder }
								onKeyDown={ e => e.stopPropagation() }
								onChange={ e => setItemKeyWords(e.target.value) }
								size="small"
								className={ s.input }
								suffix={ <SearchOutlined style={ { color: 'rgba(0, 0, 0, 0.65)' } } /> }
							/>
						</div>
						<div className={ s.checkAll }>
							<div className="r-flex r-ai-c">
								<Checkbox checked={ itemId.length === filteredItems.length } onChange={ e => setItemId(e.target.checked ? filteredItems.map(item => item.itemIds) : []) }>全选商品</Checkbox>
								<Tooltip color="#fff" title={ <div style={ { color: '#000' } }>{itemSortByName ? '按相同商品排序' : '按订单数量排序（默认）'}</div> }>
									<div><Icon
										type="paixu"
										style={ { color: itemSortByName ? '#FD8204' : '#666' } }
										onClick={ () => {
											local.set(`TopPrint_SortByName_${userInfo?.userId}_${userInfo?.subUserId}`, {
												itemSortByName: !itemSortByName,
												skuSortByName
											});
											setItemSortByName(!itemSortByName);
										} }
									/>
									</div>
								</Tooltip>
							</div>
							<span>已选择&nbsp;&nbsp;&nbsp;&nbsp;(<span style={ { color: '#FD8204' } }>{ itemId.length}</span>)</span>
						</div>
					</div>
					<div className={ s.select }>
						{filteredItems.length > 0 && (
							<VariableSizeList
								ref={ itemListRef }
								height={ 300 }
								width="100%"
								itemCount={ filteredItems.length }
								itemSize={ getItemHeight }
								overscanCount={ 5 }
							>
								{ItemRow}
							</VariableSizeList>
						)}
					</div>
					{
						isShowZeroStockVersion ? (
							<div className={ s.bottomSelect }>
								<Radio.Group value={ topSetting?.topItemShow?.simpleItemPerspective?.includes("1") ? "1" : "2" } onChange={ (e) => onSettingChange("topItemShow", "simpleItemPerspective", e) }>
									<Radio value="1">按商品名称及ID</Radio>
									<Radio value="2">按简称</Radio>
								</Radio.Group>
								{
									topSetting?.topItemShow?.simpleItemPerspective?.includes("1") && <Checkbox checked={ topSetting?.topItemShow?.simpleItemPerspective?.includes("11") } value="11" onChange={ (e) => onSettingChange("topItemShow", "simpleItemPerspective", e) }>显示商品图片</Checkbox>
								}
								{
									topSetting?.topItemShow?.simpleItemPerspective?.includes("2") && <Checkbox checked={ topSetting?.topItemShow?.simpleItemPerspective?.includes("12") } value="12" onChange={ (e) => onSettingChange("topItemShow", "simpleItemPerspective", e) }>简称相同合并</Checkbox>
								}
							</div>
						) : (
							model === TopPrintSearchPerspective.itemPerspective
								? (
									<div className={ s.bottomSelect }>
										<Radio.Group value={ topSetting?.topItemShow?.simpleItemPerspective?.includes("21") ? "21" : "" }>
											<Radio value="21" >按商品名称及ID</Radio>
										</Radio.Group>
										<Checkbox checked={ topSetting?.topItemShow?.simpleItemPerspective?.includes("31") } value="31" onChange={ (e) => onSettingChange("topItemShow", "simpleItemPerspective", e) }>显示商品图片</Checkbox>
									</div>
								) : (
									<div className={ s.bottomSelect }>
										<Radio.Group value={ topSetting?.topItemShow?.simpleSysPerspective?.includes("1") ? "1" : "" }>
											<Radio value="1">按简称</Radio>
										</Radio.Group>
										<Checkbox checked={ topSetting?.topItemShow?.simpleSysPerspective?.includes("21") } value="21" onChange={ (e) => onSettingChange("topItemShow", "simpleSysPerspective", e) }>简称相同合并</Checkbox>
									</div>
								)
						)
					}

				</div>
				<div className={ s.itemSelect }>
					<div>
						<div className={ s.inputWrap }>
							<Input
								placeholder={ searchPlaceholder.skuPlaceholder }
								onKeyDown={ e => e.stopPropagation() }
								value={ skuKeyWords }
								size="small"
								onChange={ e => setSkuKeyWords(e.target.value) }
								className={ s.input }
								suffix={ <SearchOutlined style={ { color: 'rgba(0, 0, 0, 0.65)' } } /> }
							/>
						</div>
						<div className={ s.checkAll }>
							<div className="r-flex r-ai-c">
								<Checkbox checked={ newSkuList.length && (skuId.length === filteredSkus.length) } onChange={ e => setSkuId(e.target.checked ? filteredSkus.map(item => item.skuId) : []) }>全选规格</Checkbox>
								<Tooltip color="#fff" title={ <div style={ { color: '#000' } }>{skuSortByName ? '按相同商品排序' : '按订单数量排序（默认）'}</div> }>
									<div><Icon
										type="paixu"
										style={ { color: skuSortByName ? '#FD8204' : '#666' } }
										onClick={ () => {
											local.set(`TopPrint_SortByName_${userInfo?.userId}_${userInfo?.subUserId}`, {
												itemSortByName,
												skuSortByName: !skuSortByName
											});
											setSkuSortByName(!skuSortByName);
										} }
									/>
									</div>
								</Tooltip>
							</div>
							<span>已选择&nbsp;&nbsp;&nbsp;&nbsp;(<span style={ { color: '#FD8204' } }>{ skuId.length}</span>)</span>
						</div>
					</div>
					<div className={ s.skuSelect }>
						{filteredSkus.length > 0 && (
							<VariableSizeList
								ref={ skuListRef }
								height={ 300 }
								width="100%"
								itemCount={ filteredSkus.length }
								itemSize={ getSkuHeight }
								overscanCount={ 5 }
								style={ { overflowX: 'hidden' } }
							>
								{SkuRow}
							</VariableSizeList>
						)}
					</div>
					{
						isShowZeroStockVersion ? (
							<div className={ s.bottomSelect }>
								<Radio.Group value={ topSetting?.topSkuShow?.simpleItemPerspective?.includes("1") ? "1" : "2" } onChange={ (e) => onSettingChange("topSkuShow", "simpleItemPerspective", e) }>
									<Radio value="1">按规格名称</Radio>
									<Radio value="2">按规格别名</Radio>
								</Radio.Group>
								{
									topSetting?.topSkuShow?.simpleItemPerspective?.includes("2") && <Checkbox checked={ topSetting?.topSkuShow?.simpleItemPerspective?.includes("12") } value="12" onChange={ (e) => onSettingChange("topSkuShow", "simpleItemPerspective", e) }>别名相同合并</Checkbox>
								}
								{
									topSetting?.topSkuShow?.simpleItemPerspective?.includes("1") && <Checkbox checked={ topSetting?.topSkuShow?.simpleItemPerspective?.includes("11") } value="11" onChange={ (e) => onSettingChange("topSkuShow", "simpleItemPerspective", e) }>显示规格图片</Checkbox>
								}
							</div>
						) : (
							model === TopPrintSearchPerspective.itemPerspective ? (
								<div className={ s.bottomSelect }>
									<Radio.Group value={ topSetting?.topSkuShow?.simpleItemPerspective?.includes("21") ? "21" : "" } onChange={ (e) => onSettingChange("topSkuShow", "simpleItemPerspective", e) }>
										<Radio value="21">按规格名称</Radio>
									</Radio.Group>
									<Checkbox checked={ topSetting?.topSkuShow?.simpleItemPerspective?.includes("11") } value="11" onChange={ (e) => onSettingChange("topSkuShow", "simpleItemPerspective", e) }>显示规格图片</Checkbox>
								</div>
							) : (
								<div className={ s.bottomSelect }>
									<Radio.Group value={ topSetting?.topSkuShow?.simpleSysPerspective?.includes("1") ? "1" : "" } onChange={ (e) => onSettingChange("topSkuShow", "simpleSysPerspective", e) }>
										<Radio value="1">按货品规格编码</Radio>
									</Radio.Group>
									<Checkbox checked={ topSetting?.topSkuShow?.simpleSysPerspective?.includes("11") } value="11" onChange={ (e) => onSettingChange("topSkuShow", "simpleSysPerspective", e) }>显示规格图片</Checkbox>
								</div>
							)
						)
					}
				</div>
			</div>
		);
	};
	const onDropdownVisibleChange = (visible: boolean) => {
		// 只有当点击外部区域时才关闭下拉框
		if (!visible) {
			// setVisible(visible);
			const data = {
				mode: TopPrintSearchModel.MODEL_SKU,
				selectedSkus: skuId.map(s => s?.split('_')?.[1]),
				selectedGoods: itemId,
				perspective: props.perspective,
				type: TopPrintSearchItemTopMode.open
			};
			onChange(data);
			return false;
		} else if (visible) {
			setVisible(visible);
		}
	};
	useImperativeHandle(props.cRef2, () => {
		return {
			resetValue: () => {
				setItemId(() => []);
				setSkuId(() => []);
				setSkuKeyWords('');
				setItemKeyWords('');
				const data = {
					mode: TopPrintSearchModel.MODEL_SKU,
					selectedSkus: [],
					selectedGoods: [],
					perspective: props.perspective,
					type: TopPrintSearchItemTopMode.open
				};
				onChange(data);
			}
		};
	}, []);

	return (
		<div
			className={ cs('r-flex', styles['main-container'], 	styles['model-sku']) }
			ref={ mainContainer }
		>
			<div className={ cs(styles['model-container'], 'r-pointer') } >
				<div className="r-flex r-ai-c">
					<img height={ 20 } className="r-ml-4" alt="" src={ TopPrintBySku } />
				</div>
				<div className="switch-icon" />
			</div>

			<div className="good-select-container" ref={ selectRef }>
				<Select
					open={ visible }
					loading={ loading }
					mode="multiple"
					className="sku-select-simple"
					style={ { width: '300px' } }
					getPopupContainer={ () => mainContainer.current }
					size="small"
					listHeight={ 500 }
					showSearch={ false }
					allowClear
					onClear={ () => {
						setItemId(() => []);
						setSkuId(() => []);

					} }
					onClick={ () => setVisible(true) }
					value={ itemId }
					onChange={ e => {
						setItemId(e);
					} }
					options={ itemList.map(s => ({ label: ((isShowZeroStockVersion && topSetting?.topItemShow?.simpleItemPerspective?.includes("1")) || (!isShowZeroStockVersion && model === TopPrintSearchPerspective.itemPerspective)) ? s.itemTitle : s.sysItemAlias, value: s.itemIds })) }
					showArrow
					optionLabelProp="label"
					maxTagTextLength={ 10 }
					maxTagCount={ 1 }
					placeholder="请选择商品"
					// onDropdownVisibleChange={ onDropdownVisibleChange }
					dropdownRender={ () => (<Spin style={ { width: '100%', height: '200px', lineHeight: '200px' } } spinning={ loading } >{dropdownRender()}</Spin>) }
				/>
			</div>
		</div>
	);
};

export default observer(TopPrint);
