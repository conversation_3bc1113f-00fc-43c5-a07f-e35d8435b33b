
interface OuterIdProps {
	varietyOuterId?: any,
	indexNo?: any,
	outerId?: any,
	numIid?: any,
	itemNo?: any,
}

interface skuOuterIdProps {
	varietySkuOuterId?: any,
	varietyOuterId?: any,
	size?: any,
	color?: any,
	skuOuterId?: any,
	outerId?: any,
	skuId?: any,
	skuName?: any,
	platform?: any,
	numIid?: any,
	relationSystemItem?: any,
}

interface genRuleSettingProps {
	isOuterIdChecked?: boolean, // 商家编码自动生成
	genRule?: any, // 商家编码生成规格
	prefix?: string, // 前缀
	numberMark?: any, // 序号
	numberLabel?: any, // 位数
	initNumber?: any, // 初始序号
	splitMark?: string, // 分隔符
	isMarkAfterOuterId?: boolean, // 商家编码后同时添加
	isSkuIdChecked?: any, // 规格编码自动生成
	genSkuIdRule?: any, // 规格编码生成规格
	splitSkuIdMark?: string, //  规格编码分隔符
	filterLetter?: string, // 过滤分隔符
	isFilterContent?: boolean, // 过滤中英文括号、【】[]、以及中间的内容
	isOnlyUseLetterNumber?: boolean, // 颜色尺码仅用英文+数字
	isOnlyUseNumber?: boolean, // 颜色尺码仅用数字
	isFilterComma?: boolean, // 过滤中英文逗号
	isFilterCnBracket?: boolean, // 【】符号
	isFilterEnBracket?: boolean, // []
}

export const SPECIAL_PLAT = ['tb', 'ali'];

export const outerIdRule = {
	'使用货号': '使用货号',
	'使用宝贝ID': '使用宝贝ID',
	'自定义前缀': '自定义前缀',
	'自定义前缀+序号': '自定义前缀+序号',
	'货号+序号': '货号+序号',
	'原商家编码+序号': '原商家编码+序号',
};

export const skuIdRule = {
	'使用商家编码': '使用商家编码',
	'商家编码+颜色序号+尺码序号': '商家编码+颜色序号+尺码序号',
	'商家编码+SKU序号': '商家编码+SKU序号',
	'商家编码+SKU全部属性': '商家编码+SKU全部属性',
	'商家编码+颜色': '商家编码+颜色',
	'商家编码+颜色+尺码': '商家编码+颜色+尺码',
	'商家编码+尺码': '商家编码+尺码',
	'商家编码+尺码+颜色': '商家编码+尺码+颜色',
	'颜色+尺码': '颜色+尺码',
	'颜色+其它': '颜色+其它',
	'尺码+颜色': '尺码+颜色',
	'原规格编码': '原规格编码',
	'原规格编码+SKU序号': '原规格编码+SKU序号',
	'SKU全部属性': 'SKU全部属性',
	'原规格编码+SKU全部属性': '原规格编码+SKU全部属性',
	'原规格编码+尺码': '原规格编码+尺码',
	'原规格编码+尺码+颜色': '原规格编码+尺码+颜色',
	'原规格编码+颜色+尺码': '原规格编码+颜色+尺码',
	'绑定货品的货品规格编码': '绑定货品的货品规格编码',
};

export const INCLUDE_PREFIX = [outerIdRule.自定义前缀, outerIdRule["自定义前缀+序号"]];
export const INCLUDE_NUMBER = [outerIdRule["自定义前缀+序号"], outerIdRule["货号+序号"], outerIdRule["原商家编码+序号"]];

export const SPLIT_MARK = {
	'无': '无',
	'下划线“_”': '下划线“_”',
	'中划线“-”': '中划线“-”',
	'#': '#',
	'空格': '空格',
};

export const NUMBER_MARK = {
	'默认': '默认',
	'自定义': '自定义'
};

export const NUMBER_LABEL = (() => {
	const number_label = {};
	new Array(9).fill(0).forEach((_, i) => {
		number_label[`${i + 1}位数`] = i + 1;
	});
	return number_label;
})();

export const QUICK_ADD_TEMPLATE = {
	isOuterIdChecked: false, // 商家编码自动生成
	genRule: outerIdRule.使用货号, // 商家编码生成规格
	prefix: '', // 前缀
	numberMark: NUMBER_MARK.默认, // 序号
	numberLabel: NUMBER_LABEL['2位数'], // 位数
	initNumber: '', // 初始序号
	splitMark: SPLIT_MARK.无, // 分隔符
	isMarkAfterOuterId: false, // 商家编码后同时添加

	isSkuIdChecked: true, // 规格编码自动生成
	genSkuIdRule: skuIdRule['商家编码+SKU全部属性'], // 规格编码生成规格
	splitSkuIdMark: SPLIT_MARK.无, // 分隔符
	isFilterContent: false, // 过滤中英文括号、【】[]、以及中间的内容
	isOnlyUseLetterNumber: false, // 颜色尺码仅用英文+数字
	isOnlyUseNumber: false, // 颜色尺码仅用数字
	isFilterComma: false, // 过滤中英文逗号
};

const stringLenNo = (no: number, total: number) => {
	let totalLen = String(total).length;
	if (totalLen < 2) totalLen = 2;
	const diff = totalLen - String(no).length;
	return '000000000'.substring(0, diff) + String(no);
};

class QuickAddAscNumber {

	private QUICK_ADD_MAP = new Map();

	private SIZE_MAP = new Map();

	private COLOR_MAP = new Map();

	private SKUNAME_MAP = new Map();

	public getRuleSplitName = (numIid: string, keyName: string) => {
		return numIid + '_' + keyName;
	}

	public genQuickAddMap = (list: any[], isFromRelationModal = false) => {
		this.QUICK_ADD_MAP.clear();
		list.forEach(item => {
			const childList = isFromRelationModal ? item.skuRelations : item.platformItemSkuList;
			childList?.forEach((pis: { size: any; color: any; skuName: any; }) => {
				if (!this.QUICK_ADD_MAP.has(item.numIid)) this.QUICK_ADD_MAP.set(item.numIid, { size: new Set(), color: new Set(), skuName: new Set() });
				if (pis.size) this.QUICK_ADD_MAP.get(item.numIid).size.add(pis.size);
				if (pis.color) this.QUICK_ADD_MAP.get(item.numIid).color.add(pis.color);
				if (pis.skuName) this.QUICK_ADD_MAP.get(item.numIid).skuName.add(pis.skuName);
			});
		});

		this.SIZE_MAP.clear();
		this.COLOR_MAP.clear();
		this.SKUNAME_MAP.clear();
		this.QUICK_ADD_MAP.forEach((value, key) => {
			const { size, color, skuName } = value;
			[...skuName].forEach((v, i) => {
				this.SKUNAME_MAP.set(this.getRuleSplitName(key, v), stringLenNo(i + 1, skuName.size));
			});
			[...color].forEach((v, i) => {
				this.COLOR_MAP.set(this.getRuleSplitName(key, v), stringLenNo(i + 1, color.size));
			});
			[...size].forEach((v, i) => {
				this.SIZE_MAP.set(this.getRuleSplitName(key, v), stringLenNo(i + 1, size.size));
			});
		});

	}

	get sizeMap() {
		return this.SIZE_MAP;
	}

	get colorMap() {
		return this.COLOR_MAP;
	}

	get skuNameMap() {
		return this.SKUNAME_MAP;
	}
}

const ascNoRuleStore = new QuickAddAscNumber();
export const genQuickAddMap = ascNoRuleStore.genQuickAddMap;


export const getUtils = {
	filterLetter: (str: string, setting: genRuleSettingProps) => {
		if (!str || !setting?.filterLetter) return str;
		
		try {
			// 转义所有正则特殊字符
			const escapedFilter = setting.filterLetter.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
			// 按中文逗号或英文逗号分割
			const filterArr = escapedFilter.split(/，|,/g).filter(Boolean);
			// 如果没有任何过滤字符，直接返回原字符串
			if (!filterArr.length) return str;
			// 创建正则表达式
			const reg = new RegExp(filterArr.join('|'), 'g');
			return str.replace(reg, '');
		} catch (error) {
			console.error('filterLetter error:', error);
			return str;
		}
	},
	// getSkuId: (d: skuOuterIdProps) => {
	// 	let skuId = d.skuId || '';
	// 	if (SPECIAL_PLAT.includes(d.platform) && d.skuId == "0") skuId = d.numIid;
	// 	return skuId;
	// },
	getReplace: (str: string, reg?:any) => {
		// str = str.replace(/（/g, '(').replace(/）/g, ')'); // 针对前中后英括号等情况，将所有中文括号转化为英文括号处理
		if (reg) return str.replace(reg, '');
		return str.replace(/\[.*?\]/g, '').replace(/【.*?】/g, '').replace(/（.*?）/g, '').replace(/\(.*?\)/g, '');
	},
	getCommaReplace: (str: string) => {
		console.log('str: ', str);
		return str.replace(/,|，/g, '');
	},
	getFilterContent: (setting: genRuleSettingProps, d: skuOuterIdProps, keyName: any) => {
		let keyValue = d[keyName] || '';
		if (setting.isFilterContent && keyValue) {
			keyValue = getUtils.getReplace(keyValue);
		}
		if (setting.isFilterEnBracket && keyValue) {
			keyValue = getUtils.getReplace(keyValue, /\[.*?\]/g);
		}
		if (setting.isFilterCnBracket && keyValue) {
			keyValue = getUtils.getReplace(keyValue, /【.*?】/g);
		}
		if (setting.isFilterComma && keyValue) {
			keyValue = getUtils.getCommaReplace(keyValue);
		}

		if (setting.isOnlyUseLetterNumber) {
			keyValue = keyValue.replace(/[^0-9A-Za-z]/g, '');
		}
		if (setting.isOnlyUseNumber) {
			keyValue = keyValue.replace(/[^0-9]/g, '');
		}
		return keyValue;
	},
	getSkuName: (setting: genRuleSettingProps, d: skuOuterIdProps) => {
		return getUtils.getFilterContent(setting, d, 'skuName');
	},
	getColor: (setting: genRuleSettingProps, d: skuOuterIdProps) => {
		return getUtils.getFilterContent(setting, d, 'color');
	},
	getSize: (setting: genRuleSettingProps, d: skuOuterIdProps) => {
		return getUtils.getFilterContent(setting, d, 'size');
	},
	getSplitIcon: (splitMark: string) => {
		switch (splitMark) {
			case SPLIT_MARK.无:
				return '';
			case SPLIT_MARK["下划线“_”"]:
				return '_';
			case SPLIT_MARK["中划线“-”"]:
				return '-';
			case SPLIT_MARK.空格:
				return ' ';
			case SPLIT_MARK["#"]:
				return '#';
			default:
				return '';
		}
	},
	genAscNo: (setting: genRuleSettingProps, dix: number) => {
		let selfno = Number(setting.initNumber) + dix;
		const selfnoLen = String(selfno).length;
		const diff = Number(setting.numberLabel) - selfnoLen;
		let selfnoStr = String(selfno);
		if (diff > 0 && diff < 9) selfnoStr = '000000000'.substring(0, diff) + selfnoStr;
		return selfnoStr;

	},
	getAscendNumber: (setting: genRuleSettingProps, d: OuterIdProps, dix: number) => {
		if (setting.numberMark == NUMBER_MARK.默认) {
			return d.indexNo;
		}
		if (setting.numberMark == NUMBER_MARK.自定义) {
			return getUtils.genAscNo(setting, dix);
		}
	}
};

export const genIndexNo = (data: { pageSize?: number, pageNo?: number, total?: number }, i: number) => {
	const indexNo = (data.pageNo - 1) * data.pageSize + (i + 1);
	return stringLenNo(indexNo, data.total);
};


export const getOuterId = (setting: genRuleSettingProps, d: OuterIdProps, dix: number) => {
	const { numIid = '', itemNo = '', outerId = '' } = d;
	const splitIcon = getUtils.getSplitIcon(setting.splitMark);
	const afterIcon = setting.isMarkAfterOuterId ? splitIcon : '';
	const map = {
		'使用宝贝ID': numIid,
		'使用货号': itemNo,
		'货号': itemNo,
		'原商家编码': outerId,
		'自定义前缀': setting.prefix,
		'序号': getUtils.getAscendNumber(setting, d, dix),
	};
	const resStr = setting.genRule.split('+').map((i: string) => map[i]).join(splitIcon) + afterIcon;
	return resStr;
};

export const getSkuOuterId = (setting: genRuleSettingProps, d: skuOuterIdProps) => {
	const { varietyOuterId = '', skuOuterId = '' } = d;
	// const skuId = getUtils.getSkuId(d);
	const color = getUtils.getColor(setting, d);
	const size = getUtils.getSize(setting, d);
	const skuName = getUtils.getSkuName(setting, d);
	const splitIcon = getUtils.getSplitIcon(setting.splitSkuIdMark);
	console.log('getSkuOuterId: ', setting, d);
	const map = {
		'原规格编码': skuOuterId,
		'SKU全部属性': skuName,
		'商家编码': varietyOuterId,
		'使用商家编码': varietyOuterId,
		'尺码': size,
		'颜色': color,
		'SKU序号': ascNoRuleStore.skuNameMap.get(ascNoRuleStore.getRuleSplitName(d.numIid, d.skuName)),
		'颜色序号': ascNoRuleStore.colorMap.get(ascNoRuleStore.getRuleSplitName(d.numIid, d.color)),
		'尺码序号': ascNoRuleStore.sizeMap.get(ascNoRuleStore.getRuleSplitName(d.numIid, d.size)),
		'其它': skuName.replace(color, ""),
		'绑定货品的货品规格编码': d.relationSystemItem?.skuOuterId || ''
	};
	const resStr = setting.genSkuIdRule.split('+').map((i: string) => map[i]).join(splitIcon);
	return getUtils.filterLetter(resStr, setting);
};
