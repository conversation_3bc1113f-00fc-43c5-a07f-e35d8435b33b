import React, { useEffect, useState } from 'react';
import { Modal, Table, Checkbox, Button, message } from 'antd';
import { observer } from 'mobx-react';
import videoMonitorStore from '@/stores/trade/videoMonitor';
import styles from './index.module.scss';

interface SelectShootingDeviceModalProps {
	visible: boolean;
	onCancel: () => void;
	onConfirm: (selectedDevices: any[]) => void;
	defaultSelectedDevices?: any[]; // 默认选中的设备
}

/**
 * 选择拍摄设备弹框组件
 */
const SelectShootingDeviceModal: React.FC<SelectShootingDeviceModalProps> = ({
	visible,
	onCancel,
	onConfirm,
	defaultSelectedDevices = []
}) => {
	const [loading, setLoading] = useState(false);
	const [deviceList, setDeviceList] = useState<any[]>([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
	const [selectedDevices, setSelectedDevices] = useState<any[]>([]);
    const [nvrDevices, setNvrDevices] = useState<any[]>([]);
    const { 
		checkKdzsPrintComponent, 
		checkKdzsPrintComponentStatus,
		isKdzsPrintComponent,
		sendMessage,
		disconnectWs,
		isSocketConnected,
		connectWs,
        clearMessageCallbacks,
	} = videoMonitorStore;

	// 获取拍摄设备列表
	const fetchDeviceList = async() => {
		try {
			setLoading(true);
			// 使用videoMonitorStore的方法获取摄像头设备列表
			const cameraList = await videoMonitorStore.getDeviceCameraList();
			const deviceList = cameraList || [];
			// 过滤掉禁用的设备
			const enabledDevices = deviceList.filter(item => item.status === 1);
			setDeviceList(enabledDevices);

            // 获取nvr
            const nvrDevices = await videoMonitorStore.getDeviceList();
            setNvrDevices(nvrDevices);
		} catch (error) {
			console.error('获取拍摄设备列表失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 处理选择变化
	const handleSelectionChange = (selectedKeys: React.Key[], selectedRows: any[]) => {
		setSelectedRowKeys(selectedKeys);
		setSelectedDevices(selectedRows);
	};

	// 预览功能
	const handlePreview = async(record: any) => {
        // 先判断有没有安装控件，没有安装则提示下载控件
		await checkKdzsPrintComponentStatus();
        
		const deviceId = record.deviceId;
		const channel = record.cameraChannel;
		
		if (!deviceId || !channel) {
			message.warning('缺少NVR存储设备和摄像头通道');
			return;
		}
		
		// 实现预览功能
		const device = nvrDevices.find(item => item.id == deviceId);
		const { deviceIp, deviceAccountName, deviceAccountPassword, devicePort } = device;
		sendMessage('start_nvr_preview', {
			device_ip: deviceIp,
			device_username: deviceAccountName,
			device_password: deviceAccountPassword,
			device_channel: Number(channel)
		}, (res) => {
			// 根据返回结果更新验证状态
			if (res.msg) {
				message.error(res.msg);
			}
		});
	};

	// 确认选择
	const handleConfirm = () => {
		if (selectedDevices.length === 0) {
			message.warning('请至少选择一个拍摄设备');
			return;
		}
        // 校验是否属于同一个NVR
        const nvrIds = selectedDevices.map(device => device.deviceId);
        const uniqueNvrIds = Array.from(new Set(nvrIds));
        if (uniqueNvrIds.length > 1) {
            message.warning('只能选择绑定了同一个NVR存储设备的摄像头');
            return;
        }

		onConfirm(selectedDevices);
	};

	// 取消选择
	const handleCancel = () => {
		setSelectedRowKeys([]);
		setSelectedDevices([]);
        setNvrDevices([]);
        setDeviceList([]);
		onCancel();
	};

	// 表格列定义
	const columns = [
		{
			title: '设备名称',
			dataIndex: 'deviceName',
			key: 'deviceName',
			width: 200,
			render: (text: string) => text || '未知设备'
		},
		{
			title: '拍摄设备',
			dataIndex: 'cameraName',
			key: 'cameraName',
			width: 200,
			render: (text: string) => text || '未知摄像头'
		},
		{
			title: '操作',
			key: 'action',
			width: 100,
			render: (text: any, record: any) => (
				<Button 
					type="link" 
					size="small"
					onClick={ () => handlePreview(record) }
					style={ { color: '#1890ff', padding: 0 } }
				>
					预览
				</Button>
			)
		}
	];

	// 行选择配置
	const rowSelection = {
		selectedRowKeys,
		onChange: handleSelectionChange,
		getCheckboxProps: (record: any) => ({
			disabled: record.status === 0, // 禁用状态的设备不可选择
		}),
	};

    const cleanup = () => {
		// 清理消息回调，避免内存泄漏
		clearMessageCallbacks();

		// 关闭预览
		sendMessage('close_nvr_preview', {}, (res) => {
			if (res.msg) {
				// message.error(res.msg);
			}
		});
	};

	// 弹框显示时获取数据并设置默认选中
	useEffect(() => {
		if (visible) {
			fetchDeviceList();
		} else{
            cleanup();
        }
	}, [visible]);

    useEffect(() => {
		return () => {
			cleanup();
		};
	}, []);

	// 设置默认选中的设备
	useEffect(() => {
		if (deviceList.length > 0 && defaultSelectedDevices.length > 0) {
			const validDevices = defaultSelectedDevices.filter(defaultDevice => deviceList.find(device => device.id === defaultDevice.id));
			const validKeys = validDevices.map(device => device.id);
			setSelectedRowKeys(validKeys);
			setSelectedDevices(validDevices);
		}
	}, [deviceList, defaultSelectedDevices]);

	return (
		<Modal
			title="选择拍摄设备"
			visible={ visible }
			onCancel={ handleCancel }
			width={ 800 }
			destroyOnClose
			maskClosable={ false }
			closable={ false }
			centered
			footer={ [
				<Button key="cancel" onClick={ handleCancel }>
					取消
				</Button>,
				<Button key="confirm" type="primary" onClick={ handleConfirm }>
					确定
				</Button>
			] }
			className={ styles['select-shooting-device-modal'] }
		>
			<Table
				columns={ columns }
				dataSource={ deviceList }
				rowKey="id"
				loading={ loading }
				rowSelection={ rowSelection }
				pagination={ false }
				scroll={ { y: 'calc(100vh - 500px)' } }
				size="middle"
			/>
		</Modal>
	);
};

export default observer(SelectShootingDeviceModal); 