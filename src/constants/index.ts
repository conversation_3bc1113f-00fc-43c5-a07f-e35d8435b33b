import { MenuBean } from "@/components-biz/layouts/HeaderMenu/menuBean";
import { local } from "@/libs/db";
import 红旗 from '@/assets/mp3/红旗.mp3';
import 黄旗 from '@/assets/mp3/黄旗.mp3';
import 蓝旗 from '@/assets/mp3/蓝旗.mp3';
import 紫旗 from '@/assets/mp3/紫旗.mp3';
import 绿旗 from '@/assets/mp3/绿旗.mp3';
import 橙旗 from '@/assets/mp3/橙旗.mp3';
import 浅蓝旗 from '@/assets/mp3/浅蓝旗.mp3';
import 浅粉旗 from '@/assets/mp3/浅粉旗.mp3';
import 深绿旗 from '@/assets/mp3/深绿旗.mp3';
import 桃红旗 from '@/assets/mp3/桃红旗.mp3';

// 默认图片
// eslint-disable-next-line max-len
export const DEFAULT_IMG = "data:image/png;base64,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";

// 常量列表
export const API_ROOT = process.env.APP_ROOT;

// 打印中心环境
export const PRINT_CENTER_ENV = process.env.APP_PRINT_CENTER_ENV;
export const APP_MOCK = process.env.APP_MOCK;

export const PLAT_TB = 'tb';
export const PLAT_TM = 'tm';
export const PLAT_PDD = 'pdd';
export const PLAT_FXG = 'fxg';
export const PLAT_ALI = 'ali';
// 图标文字 其
export const PLAT_HAND = 'hand';
// 图标文字 手
export const PLAT_HAND_OLD = 'old_hand';
export const PLAT_ZJ = '自建';
export const PLAT_KS = 'ksxd';
export const PLAT_JD = 'jd';
export const PLAT_OTHER = 'other';
export const PLAT_OTHER_XIAO = "other_xiao";
export const PLAT_SPH = 'sph';
export const PLAT_XHS = 'xhs';

export const PLAT_C2M = 'c2m'; // 淘工厂
export const PLAT_YZ = 'yz';// 有赞
export const PLAT_DW = 'dw';// 得物
export const PLAT_KTT = 'ktt';// 快团团
export const PLAT_HAND_SHOP_NAME = "无店铺";

export const DEFAULT_PLAT = PLAT_TB;

export const PLAT_SCM = 'SCM';
export const PLAT_SCMHAND = 'SCMHAND';
export const PLAT_ALL_SCM = [PLAT_SCM, PLAT_SCMHAND];

export const AFTERSALE_HANDORDER = 'EXCHANGE';

export const PLAT_MAP = {
	[PLAT_SCM]: '分销推送',
	[PLAT_SCMHAND]: '分销推送',
	[PLAT_ZJ]: '自建',
	[PLAT_TB]: '淘宝/天猫',
	[PLAT_TM]: '天猫',
	[PLAT_PDD]: '拼多多',
	[PLAT_FXG]: '抖店',
	[PLAT_HAND]: '手工单',
	[PLAT_ALI]: '1688',
	[PLAT_KS]: '快手',
	[PLAT_JD]: '京东',
	[PLAT_SPH]: '视频号',
	[PLAT_XHS]: '小红书',
	[PLAT_C2M]: '淘工厂',
	[PLAT_YZ]: '有赞',
	[PLAT_DW]: '得物',
	[PLAT_KTT]: '快团团',
	[PLAT_OTHER]: '其他',
};
export const PRINT_MAP = {
	[PLAT_TB]: '3',
	[PLAT_TM]: '3',
	[PLAT_C2M]: '3',
	[PLAT_PDD]: '7',
	[PLAT_FXG]: '8',
	[PLAT_ALI]: '3',
	[PLAT_KS]: '9',
	[PLAT_JD]: '5',
	[PLAT_XHS]: '13',
	[PLAT_SPH]: '14',
	[PLAT_YZ]: '15',
	[PLAT_DW]: '17',
};
export const TEMP_MAP = {
	3: PLAT_TB,
	7: PLAT_PDD,
	8: PLAT_FXG,
	9: PLAT_KS,
	5: PLAT_JD,
	13: PLAT_XHS,
	14: PLAT_SPH,
	15: PLAT_YZ,
	16: PLAT_XHS,
	17: PLAT_DW,
};

// 1688回流
export const HL_PLAT = {
	'hl-tx': PLAT_TB,
	'hl-pdd': PLAT_PDD,
	'hl-fxg': PLAT_FXG,
	'hl-jd': PLAT_JD,
	'hl-ksxd': PLAT_KS,
	'hl-xhs': PLAT_XHS,
	'hl-sph': PLAT_SPH,
	'hl-other': PLAT_OTHER
};

export const HL_LABEL_PLAT = {
	'hl-tx': '淘系回流订单',
	'hl-pdd': '拼多多回流订单',
	'hl-fxg': '抖音回流订单',
	'hl-jd': '京东回流订单',
	'hl-ksxd': '快手回流订单',
	'hl-xhs': '小红书回流订单',
	'hl-sph': '微信回流订单',
	'hl-other': '其他回流订单'
};

// * PLAT_KS
export const TEMP_NAME = {
	3: '菜鸟',
	5: '京东',
	7: '拼多多',
	8: '抖音',
	9: '快手',
	13: '小红书',
	14: '视频号',
	16: '小红书(新)',
};

// 平台图标
export const PLAT_ICON_MAP = {
	[PLAT_PDD]: 'pinduoduo',
	[PLAT_TB]: 'taobao',
	[PLAT_TM]: 'tianmao',
	[PLAT_FXG]: 'douyin',
	[PLAT_ALI]: 'a-1688',
	[PLAT_HAND]: 'shougongdingdan',
	// 其字 图标
	[PLAT_OTHER_XIAO]: 'qita-xiao',
	// 其他店铺的图标 用于在店铺页面展示
	[PLAT_OTHER]: 'qita',
	[PLAT_KS]: 'kuaishouxiaodian',
	[PLAT_JD]: 'jingdong',
	[PLAT_SPH]: 'shipinhao',
	[PLAT_XHS]: 'xiaohongshu',
	[PLAT_C2M]: 'taogongchang',
	[PLAT_YZ]: 'youzan',
	[PLAT_DW]: 'dewu',
	[PLAT_KTT]: 'kuaituantuan',
};

// 快递模板图标
export const TEMP_ICON = {
	2: 'wangdian',
	3: 'cainiao',
	5: 'jingdong',
	7: 'pinduoduo',
	8: 'douyin',
	9: 'kuaishouxiaodian',
	13: 'xiaohongshu',
	14: 'shipinhao',
	15: 'youzan',
	16:'xiaohongshu',
	17:'dewu'
};

export enum USER_TYPE {
	默认 = 0, // 常规版无分销功能
	常规版 = 1, // 供应商 && 分销商
	免费分销商 = 2,
	免费版供应商用户 = 3,
}

export enum stockVersion {
	无库存版 = 2,
	库存版 = 1,
}

export enum zeroStockVersion {
	旧版零库存版 = 0,
	新版零库存版 = 1,
}

export const CHINA_ID = 1;


// 分销商、供应商菜单
export enum USER_TYPE_MENU {
	供应商 = 1,
	分销商 = 2,
}

// 不同版本用户
export enum AUTH_USER_TYPE {
	常规版零库存版 = "1",
	常规版库存版 = "2",
	分销商版零库存版 = "3",
	供应商版库存版 = "4",
	默认常规版零库存版 = "12",
	默认常规版库存版 = "22",
}

export const authUserName = {
	"1": "常规版零库存版",
	"2": "常规版库存版",
	"3": "分销商版零库存版",
	"4": "供应商版库存版",
	"12": "默认常规版零库存版", // 无分销
	"22": "默认常规版库存版", // 无分销
};

// 不同版本的字段
export const authorizeMap = {
	[AUTH_USER_TYPE.常规版零库存版]: {
		userType: 1,
		version: 2,
	},
	[AUTH_USER_TYPE.常规版库存版]: {
		userType: 1,
		version: 1,
	},
	[AUTH_USER_TYPE.分销商版零库存版]: {
		userType: 2,
		version: 2,
	},
	[AUTH_USER_TYPE.供应商版库存版]: {
		userType: 3,
		version: 1,
	},
	[AUTH_USER_TYPE.默认常规版零库存版]: {
		userType: 0,
		version: 2,
	},
	[AUTH_USER_TYPE.默认常规版库存版]: {
		userType: 0,
		version: 1,
	},
};


// 这里是老的旗帜，暂时批量修改备注、详情修改备注还是这个
export const flagArr = [
	// { name: '灰旗', color: '#8C8C8C' },
	// { name: '红旗', color: '#F5222D' },
	// { name: '黄旗', color: '#FADB14' },
	// { name: '绿旗', color: '#52C41A' },
	// { name: '蓝旗', color: '#2F54EB' },
	// { name: '紫旗', color: '#B049CF' }
	{ name: '灰旗', color: '#999999' },
	{ name: '红旗', color: '#EA3D3D' },
	{ name: '黄旗', color: '#FFC60B' },
	{ name: '绿旗', color: '#3ACB36' },
	{ name: '蓝旗', color: '#0664F0' },
	{ name: '紫旗', color: '#BA2FFF' }
];

// 得物旗帜
export const dwflagArr = [
	// { name: '灰旗', color: '#8C8C8C' },
	// { name: '红旗', color: '#F5222D' },
	// { name: '黄旗', color: '#FADB14' },
	// { name: '绿旗', color: '#52C41A' },
	// { name: '蓝旗', color: '#2F54EB' },
	// { name: '紫旗', color: '#B049CF' }
	{ name: '灰旗', color: '#999999' },
	{ name: '红旗', color: '#EA3D3D' },
	{ name: '橙旗', color: '#F48804' },
	{ name: '黄旗', color: '#FFC60B' },
	{ name: '绿旗', color: '#3ACB36' },
	{ name: '蓝旗', color: '#0664F0' },
	{ name: '紫旗', color: '#BA2FFF' }
];

// 淘宝订单旗帜
export const allFlagArr = [
	{ name: '灰旗', color: '#999999' }, // 0
	{ name: '红旗', color: '#EA3D3D' },
	{ name: '黄旗', color: '#FFC60B' },
	{ name: '绿旗', color: '#3ACB36' },
	{ name: '蓝旗', color: '#0664F0' },
	{ name: '紫旗', color: '#BA2FFF' },
	{ name: '橙旗', color: '#F48804' },
	{ name: '浅蓝旗', color: '#41B4FA' },
	{ name: '浅粉旗', color: '#E5B6B6' },
	{ name: '深绿旗', color: '#9BA217' },
	{ name: '桃红旗', color: '#E4248E' } // 10
];

// 各旗帜的颜色
export const allFlagColorObj = {
	'灰旗': '#999999',
	'红旗': '#EA3D3D',
	'黄旗': '#FFC60B',
	'绿旗': '#3ACB36',
	'蓝旗': '#0664F0',
	'紫旗': '#BA2FFF',
	'橙旗': '#F48804',
	'浅蓝旗': '#41B4FA',
	'浅粉旗': '#E5B6B6',
	'深绿旗': '#9BA217',
	'桃红旗': '#E4248E',
};

// 查询旗帜
export const BASIC_FLAG_LIST = [
	{ key: '0', value: '灰旗' },
	{ key: '5', value: '红旗' },
	{ key: '6', value: '黄旗' },
	{ key: '7', value: '绿旗' },
	{ key: '8', value: '蓝旗' },
	{ key: '9', value: '紫旗' },
	{ key: '14', value: '橙旗' },
	{ key: '15', value: '浅蓝旗' },
	{ key: '16', value: '浅粉旗' },
	{ key: '17', value: '深绿旗' },
	{ key: '18', value: '桃红旗' }
];

// 自定义留言备注旗帜、扫描打印旗帜不自动打印、自动标记空包设置
export const flagGroup = [
	{ color: allFlagColorObj.灰旗, value: '0', name: '灰', toSellerFlag: '0' },
	{ color: allFlagColorObj.红旗, value: '1', name: '红', toSellerFlag: '5' },
	{ color: allFlagColorObj.黄旗, value: '2', name: '黄', toSellerFlag: '6' },
	{ color: allFlagColorObj.绿旗, value: '3', name: '绿', toSellerFlag: '7' },
	{ color: allFlagColorObj.蓝旗, value: '4', name: '蓝', toSellerFlag: '8' },
	{ color: allFlagColorObj.紫旗, value: '5', name: '紫', toSellerFlag: '9' },
	{ color: allFlagColorObj.橙旗, value: '6', name: '橙色', toSellerFlag: '14' },
	{ color: allFlagColorObj.浅蓝旗, value: '7', name: '浅蓝', toSellerFlag: '15' },
	{ color: allFlagColorObj.浅粉旗, value: '8', name: '浅粉', toSellerFlag: '16' },
	{ color: allFlagColorObj.深绿旗, value: '9', name: '深绿', toSellerFlag: '17' },
	{ color: allFlagColorObj.桃红旗, value: '10', name: '桃红', toSellerFlag: '18' }
];

export const flagName = flagGroup.reduce((a, b) => {
	a[b.value] = b.name;
	return a;
}, {});

export const flagAudio = {
	'1': 红旗,
	'2': 黄旗,
	'3': 绿旗,
	'4': 蓝旗,
	'5': 紫旗,
	'6': 橙旗,
	'7': 浅蓝旗,
	'8': 浅粉旗,
	'9': 深绿旗,
	'10': 桃红旗,
};

// 备份一份颜色，最好不用英文，数字下标对应就完全可以
export const flagColor = ['gray', 'red', 'yellow', 'green', 'blue', 'purple'];
export const allFlagColor = ['gray', 'red', 'yellow', 'green', 'blue', 'purple', 'orange', 'light_blue', 'light_pink', 'dark_green', 'peach_red'];

/**
 * 密码校验规则
 */
export const PASSWORD_RULES: Array<object> = [{
	required: true,
	max: 16,
	min: 8,
	type: 'string',
	validator(_: object, value: string) {
		if (!value) {
			return Promise.reject(new Error('请输入密码'));
		}
		if (value?.length < 8 || value?.length > 16) {
			return Promise.reject(new Error('请输入8-16位'));
		}
		if (/[\s\u4e00-\u9fa5]/.test(value)) {
			return Promise.reject(new Error('只能含大小写字母、数字以及符号（不含空格）'));
		}
		if (/^[A-z]+$/.test(value) && value === value.toLowerCase()) {
			return Promise.reject(new Error('大写字母/小写字母/数字/符号，至少含2种'));
		}
		if (!/(?!^[0-9]+$)(?!^[^A-z0-9]+$)^[^\s\u4e00-\u9fa5]{8,20}$/.test(value)) {
			return Promise.reject(new Error('大写字母/小写字母/数字/符号，至少含2种'));
		}
		return Promise.resolve();
	}
}];

/**
 * 账户名校验规则
 */
export const ACCOUNT_NAME_RULES: Array<object> = [{
	required: true,
	min: 2,
	max: 30,
	type: 'string',
	validator: async(_: object, value: string) => {
		if (!value) {
			return Promise.reject(new Error('请输入手机号或账号名'));
		}
		if (value?.length < 2 || value?.length > 30) {
			return Promise.reject(new Error('请输入2-30位'));
		}
		if (/[\s]/.test(value)) {
			return Promise.reject(new Error('可使用中文、大小写字母、数字、符号（不含空格）'));
		}
		return Promise.resolve();
	}
}];

/**
 * 联系方式校验规则
 */
export const LXFS_RULES: Array<object> = [{
	required: true,
	min: 11,
	max: 13,
	type: 'string',
	validator: async(_: object, value: string) => {
		if (!value) {
			return Promise.reject(new Error('请输入联系方式'));
		}
		if (!(/^1\d{10}$/.test(value) || /^\d{4}-\d{7,8}$/.test(value))) {
			return Promise.reject(new Error('请输入正确的联系方式'));
		}
		return Promise.resolve();
	}
}];


export enum FuwuMarketOrderUrl {
	'tb' = 'https://fuwu.taobao.com/ser/detail.htm?service_code=FW_GOODS-1000274853&tracelog=search&from_key=%E5%8A%A9%E6%89%8Berp',
	'fxg' = 'https://fuwu.jinritemai.com/detail?service_id=21675',
	'pdd' = 'https://fuwu.pinduoduo.com/service-market/service-detail?detailId=64349',
	'ali' = 'https://pc.1688.com/product/detail.htm?productCode=Lbx6VDMLQxxY4Xy8f7oBRa9PVmfqdcPxhHGTnYJ1Wb8%3D&productType=GROUP',
	'ksxd' = 'https://fuwu.kwaixiaodian.com/detail?id=4080039950068',
	'sph' = 'https://store.weixin.qq.com/shop',
	'xhs' = 'https://fuwu.xiaohongshu.com/applicationDetail?appId=08d8b599b6e144cba1f0',
	'c2m' = '',
}

/**
 *
 * @param seller
 * @returns
 * 店铺状态 用于订单页面 和 售后页面 选择店铺选项列表中
 */
export const getShopStatus = (seller: any) => {
	const { _status, tokenStatus, platform } = seller;
	let disable = false;
	let statusText = '';
	let href = "";
	let target = '';
	if (tokenStatus === 0) {
		statusText = '( 店铺授权过期> )';
		href = "#/shops";
		target = "_self";
		disable = true;
	}
	if (_status === 0) {
		statusText = '( 店铺暂停> )';
		href = "#/shops";
		target = "_self";
		// disable = true;
	}
	if (_status === 2) {
		statusText = '( 订购过期> )';
		href = FuwuMarketOrderUrl[platform];
		target = "_blank";
		disable = true;
	}
	if (_status === 3) {
		statusText = '( 订购即将过期> )';
		href = FuwuMarketOrderUrl[platform];
		target = "_blank";
		// disable = true;
	}
	return { statusText, disable, href, target };
};

export const UserLevelMap = {
	'0': '免费版',
	'1': '标配版',
	'2': '高配版',
	'3': '标配版'
};

export enum UserLevelVal {
	免费版 = 0,
	老标配版 = 1,
	高配版 = 2,
	标配版 = 3
}

export enum CommonItemType {
	是 = 1,
	否 = 2,
}

export const isHighVersion = (level: number) => {
	return UserLevelVal.高配版 === level;
};

export const AUTO_REFUND_CONFIG = {
	TB: {
		name: "淘宝", // 平台名称
		toolsName: "AG", // 平台自动退款系统的名称
		toolsUrl: "https://aligenius.taobao.com", // 工具使用说明书
		officialNeedOpen: false, // 平台是否需要打开自动退款开关。目前拼多多没有自动退款开关功能
	},
	PDD: {
		name: "拼多多",
		toolsName: "PG",
		toolsUrl: "https://mms.pinduoduo.com/aftersales/aftersale_auto/home",
		officialNeedOpen: false,
	},
	JD: {
		name: "京东"
	},
	ALI: {
		name: "1688",
		toolsName: "1688退货退款管理",
		toolsUrl: "https://work.1688.com/?spm=a2638g.u_0_1001.framenav.du_0d79e.5e2e1768MvMiFQ&_path_=sellerPro/lvyue/tuikuantuihuoguanli",
		officialNeedOpen: false,
	},
	FXG: {
		name: "抖店",
		toolsName: "抖店售后小助手",
		toolsUrl: "https://fxg.jinritemai.com/login",
		officialNeedOpen: false,
	}
};

export const isViteDevMode = () => process.env.NODE_ENV === 'development' && process.env.DEV_MODE === 'vite';
export const hotGoodsDefaultColumnConfig = [
	{ "key": "index", "ischecked": true, "name": "序号", "isedit": true, "index": 0 },
	{ "key": "buyerNick", "ischecked": true, "name": "昵称", "isedit": false, "index": 1, "width": 147 },
	{ "key": "orderNumber", "ischecked": true, "name": "宝贝数量", "isedit": true, "index": 2, "width": 58 },
	{ "key": "receiverAddress", "ischecked": true, "name": "收货信息", "isedit": false, "index": 3, "width": 200 },
	{ "key": "tradeLabel", "ischecked": true, "name": "订单标识", "isedit": true, "index": 4, "width": 88 },
	{ "key": "expressnum", "ischecked": true, "name": "快递单号", "isedit": false, "index": 5, "width": 187 },
	{ "key": "packageOrders", "ischecked": true, "name": "产品内容", "isedit": true, "index": 6, "width": 300 },
	{ "key": "buyerMessage", "ischecked": true, "name": "留言备注", "isedit": true, "index": 7, "width": 160 },
	{ "key": "ptTid", "ischecked": true, "name": "订单编号", "isedit": true, "index": 8, "width": 191 },
	{ "key": "tid", "ischecked": true, "name": "系统单号", "isedit": true, "index": 9, "width": 191 },
	{ "key": "operate", "ischecked": true, "name": "操作", "isedit": true, "index": 10, "width": 100 }
];

export const HEADER_TABS_TOFIXED_LIST = 'headerTabsToFixedList';

export const addHeaderTabsToFixedList = (toFixedTabs: MenuBean) => {
	local.set(HEADER_TABS_TOFIXED_LIST, Array.from(new Set((local.get(HEADER_TABS_TOFIXED_LIST) || []).concat([toFixedTabs]))));
};

export const removeHeaderTabsToFixedList = (removeFixedTabs: MenuBean) => {
	let toFixedList: MenuBean[] = local.get(HEADER_TABS_TOFIXED_LIST) || [];
	let index = toFixedList.findIndex(item => item.path === removeFixedTabs.path);
	if (index > -1) {
		toFixedList.splice(index, 1);
		local.set(HEADER_TABS_TOFIXED_LIST, toFixedList);
	}
};
export const PRINT_BATCH_ABNORMAL_TRADE_SYSSKUIDLIST = 'printBatch.abnormalTrade.sysSkuIdList';

export const PRINT_BATCH_ABNORMAL_ORDER_SET_IGNORE_KEY = 'printBatch.abnormalOrder.setTempAbnormalIgnoreKey';

export const enum activityIds {
	新手设置 = 1,
	小标签打印任务 = 2,
}

export const TEMPLATE_ZMJ = 'WITH_ZMJ';

// 赠品表现与正常商品相同，仅比商品多一个赠标识的平台列表
export const GIFT_NORMAL_PLATFORM = [PLAT_FXG, PLAT_TB];

// 快手赠品，编辑和勾选逻辑
export const GIFT_SPECIAL_PLATFORM = [PLAT_KS]; // 赠品不支持编辑商品

// 京东整单发货
export const FULL_ORDER_PLATFORM = [PLAT_JD];

// 礼物订单整单发货
export const FULL_PRESENT_ORDER_PLATFORM = [PLAT_TB, PLAT_FXG];

// 平台赠品不参与「快销小标签」设置 允许的平台
export const GIFT_ALLOW_GENERATORTAG_PLATFORM = [PLAT_FXG, PLAT_KS, PLAT_XHS, PLAT_TB];

// 四级地址
export const PLAT_HAS_ADDRESS_TOWN = [PLAT_KS,PLAT_JD];
export const IS_ADDR_TOWN = (plat) => {
	return PLAT_HAS_ADDRESS_TOWN.includes(plat);
};

// 承诺日达
export const APPOINTMENT_ARRIVAL_PLAT = ['SF', 'JD', 'SFKY', 'JDKY'];
