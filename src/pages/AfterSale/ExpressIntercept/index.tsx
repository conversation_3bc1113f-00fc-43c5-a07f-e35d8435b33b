import React, { useEffect, useMemo, useRef, useState, useCallback } from "react";
import { Button, Dropdown, Form, Menu, Modal, Select } from "antd";
import dayjs from "dayjs";
import _, { isEmpty, isNumber, isUndefined } from "lodash";
import cs from "classnames";
import { observer } from "mobx-react";
import { CopyOutlined, ExclamationCircleOutlined, SettingOutlined } from "@ant-design/icons";
import { ColumnsType } from "antd/lib/table";
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import SearchTable from "@/components/SearchTable";
import Pointer from "@/utils/pointTrack/constants";
import Input from "@/components/Input/InputSearch";
import message from "@/components/message";
import ExpressInterceptTopTab from "./components/ExpressInterceptTopTab";
import {
	DatePickerKey,
	getCacheDateRange
} from "@/components/DateRangeComp/kdzsRangePickerUtil";
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import ShopMultiSelect from "@/components-biz/ShopListSelect/shopMultiSelect";
import InputMulti from "@/components/Input/InputMulti";
import event from "@/libs/event";
import {
	EVENT_BUS,
	RefundStatusList,
	InterceptStatusList,
	MarkRefundStatus,
	ExpressInterceptTopItems,
	ExpressInterceptGlobalConfig,
	ExpressStatusList,
	ExpressInterceptTopItemsEnum
} from "./constants";
import styles from "./index.module.scss";
import {
	getMultiShops,
	getMultiShopsWithFilter,
	getShopName,
	isSourceScm,
	isAfterSaleSourceScm
} from "@/components-biz/ShopListSelect/shopListUtils";
import {
	BatchDelLogisticsInterceptApi,
	MarkLogisticsInterceptStatusApi,
	SelectLogisticsInterceptListApi
} from "@/apis/aftersale/expressIntercept";
import CreateExpressModal from "./components/CreateExpressModal";
import { copyToPaste } from "@/utils";
import InterceptConfigModal from "./components/InterceptConfigModal";
import ResultModal from "./components/ResultModal";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import LowVersionControlModal, {
	lowVersionLock
} from "@/components-biz/LowVersionControlModal";
import { PageNameControlEnum } from "@/components-biz/LowVersionControlModal/constant";
import { statusMap } from "@/pages/Report/KddLog/components/ShowLogistic/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { getPlatformTradeLink } from "./utils";
import { SelectLogisticsInterceptListResponse } from "@/types/aftersale/expressIntercept";
import memoFn from "@/libs/memorizeFn";
import ShowLogistic from "@/pages/Report/KddLog/components/ShowLogistic";
import { getInterceptLock } from "../TradeList/utils";
import { ColSetPlaceEnum } from "@/components/SearchTable/BaseCheckGroupTable";
import userStore from "@/stores/user";
import {
	GetRefundGlobalConfigListApi,
	BatchUpdateLocalNoteApi,
	UpdateRefundGlobalConfigApi
} from "@/apis/aftersale/trade";
import { PLAT_KS, PLAT_SCMHAND, flagGroup, PLAT_KTT, PLAT_DW, PLAT_YZ } from "@/constants";
import PlatformIcon from "@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon";
import { getTradeFlag, getTradeFlagTag } from "@/pages/Trade/utils";
import Icon from "@/components/Icon";
import BatchModifyMemoModal from "@/pages/Trade/components/BatchModifyMemoModal";
import OfflineMemoModal, {
	EnumFromPage,
	IBatchOfflineMemoModalProps
} from "@/components-biz/OfflineMemoModal";
import { tradeStore } from "@/stores";
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { OfflineMemoEnum } from "@/pages/AfterSale/TradeList/constants";
import FlagAndMemoSelect from "@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagAndMemoSelect";
import FlagSelect from '@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagSelect';
import { local } from "@/libs/db";
import CountSettingModal from "./components/CountSettingModal";
import { UpdateTypeEnum } from "../TradeList/constants";
import { AFTERSALE_REQUEST_SOURCE } from '@/pages/AfterSale/constants';

type DataItem = SelectLogisticsInterceptListResponse["data"]["list"][number];

const ExpressIntercept = (props) => {
	const { Option } = Select;
	const ref = useRef(null);
	const [form] = Form.useForm();
	const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
	const [selectedRows, setSelectedRows] = useState<object[]>([]);
	const [pageLoaded, setPageLoaded] = useState(false);
	const [activeTabItem, setActiveTabItem] = useState("");
	const [dataSource, setDataSource] = useState<DataItem[]>([]);
	const [missedList, setMissedList] = useState([]);
	const [searchParams, setSearchParams] = useState({});
	const [pagination, setPagination] = useState({});
	const initialRangeTime = getCacheDateRange(
		DatePickerKey.express_intercept
	) || [dayjs().subtract(6, "days").startOf("day"), dayjs().endOf("day")];
	const [createExpressModalVisible, setCreateExpressModalVisible] =		useState(false);
	const [interceptConfigModalVisible, setInterceptConfigModalVisible] =		useState(false);
	const [resultModalVisible, setResultModalVisible] = useState(false);
	const [resultModalTitle, setResultModalTitle] = useState("");
	const [resultModalSuccessList, setResultModalSuccessList] = useState([]);
	const [resultModalFailList, setResultModalFailList] = useState([]);
	const [markIntercepting, setMarkIntercepting] = useState(false);
	const [markInterceptingSuccess, setMarkInterceptingSuccess] =		useState(false);
	const [expressList, setExpressList] = useState([]);
	const {
		setModifyMemoPackage,
		setIsShowBatchModifyMemoModal,
		isShowBatchModifyMemoModal,
		modifyMemoPackage,
	} = tradeStore;
	const [offlineMemoData, setOfflineMemoData] = useState<
		IBatchOfflineMemoModalProps["data"]
	>({ visible: false });
	// 列配置需要的state
	const [initMap, setInitMap] = useState({});
	const [initList, setInitList] = useState<any[]>();
	const [countSetting, setCountSetting] = useState({});
	const [showCountSettingModal, setShowCountSettingModal] = useState(false);
	const [formData, setFormData] = useState({});
	const [operateBtnsDefault, setOperateBtnsDefault] = useState({
		batchMemoBtn: "batchMemo",
	});

	const onUpdateSetting = async(values) => {
		UpdateRefundGlobalConfigApi([
			{
				biz: ExpressInterceptGlobalConfig.数据更新时间配置,
				value: JSON.stringify(values),
			}
		]).then(res => {
			setCountSetting(values);
		}).catch(err => {
			message.error("保存失败");
		}).finally(() => {
			setShowCountSettingModal(false);
		});
	};

	useEffect(() => {
		memoFn.getExpressList().then((res) => {
			setExpressList(res || []);
		});
		const defaultOperateBtns = local.get("expressInterceptOperateBtnsDefault") || {};
		setOperateBtnsDefault((prev => {
			return {
				...prev,
				...defaultOperateBtns,
			};
		}));
	}, []);
	useEffect(() => {
		pageInit(props.location);
	}, [props.location]);

	const pageInit = async(location) => {
		getSearchConditionConfig(location);
	};

	// 高级设置
	const onClickInterceptConfig = () => {
		setInterceptConfigModalVisible(true);
	};
	const interceptConfigModalOnOk = () => {
		setInterceptConfigModalVisible(false);
	};
	const interceptConfigModalOnCancel = () => {
		setInterceptConfigModalVisible(false);
	};

	/** ---------- 批量操作相关 -------------- */
	// 新增拦截快递
	const onCreateExpress = () => {
		sendPoint(Pointer.快递拦截_新增拦截包裹_点击);
		setCreateExpressModalVisible(true);
	};
	const createExpressModalOnOk = () => {
		setCreateExpressModalVisible(false);
		ref?.current?.submit();
	};
	const createExpressModalOnCancel = () => {
		setCreateExpressModalVisible(false);
	};

	// 批量复制
	const batchCopyOnClick = () => {
		sendPoint(Pointer.快递拦截_批量复制单号_点击);

		if (isEmpty(selectedRows)) {
			message.error("请先选择快递");
			return;
		}
		const allExpressNo = selectedRows.map((row) => row.invoiceNo).join(",");
		copyToPaste(allExpressNo);
	};

	const batchDeleteOnClick = () => {
		if (isEmpty(selectedRows)) {
			message.error("请先选择要删除的内容");
			return;
		}
		Modal.confirm({
			centered: true,
			title: "系统提示",
			okText: "确认",
			cancelText: "取消",
			icon: <ExclamationCircleOutlined />,
			content: (
				<div>删除系统将不再记录该单号拦截情况，是否继续删除？</div>
			),
			async onOk() {
				try {
					const ids = selectedRows.map((row) => row.id);
					await BatchDelLogisticsInterceptApi({ ids });
					message.success("删除成功");
					refreshPage();
				} catch (error) {
					// message.error("删除失败：", error.message);
				}
			},
		});
	};

	// 导出单号
	const verifySuccessCb = async() => {
		let params = {
			...searchParams,
		};
		await downloadCenter({
			requestParams: params,
			fileName: "快递拦截单号列表",
			module: ModulesFunctionEnum.快递拦截单号导出,
		});
	};

	const batchExportOnClick = () => {
		sendPoint(Pointer.快递拦截_导出单号_点击);
		if (!dataSource.length) {
			message.warning("暂无可导出的数据");
			return;
		}
		verifySuccessCb();
	};
	/** ------------------------------------ */

	/** ------------- 表格渲染列相关逻辑 -------------- */
	const renderSerialNumber = (_, __, index) => {
		const { pageSize = 10, current = 1 } = pagination || {};
		const baseNumber = pageSize * (current - 1);
		const curSerialNumber = parseInt(baseNumber + index + 1, 10);
		return <span>{curSerialNumber}</span>;
	};

	const renderInvoiceNo = (_, record) => {
		return (
			<div className="r-flex r-word-break">
				<ShowLogistic ydNo={ record["invoiceNo"] }>
					<span className="kdzs-link-text">{record.invoiceNo}</span>
				</ShowLogistic>
				<span hidden={ !record.invoiceNo }>
					<CopyOutlined
						onClick={ () => {
							copyToPaste(record.invoiceNo);
						} }
						className="r-fc-black-65 r-pointer r-ml-4 r-mr-5"
					/>
				</span>
			</div>
		);
	};

	const renderInvoiceDetail = (_, record) => {
		const statusMapInfo = statusMap[record.invoiceStatus];
		if (statusMapInfo) {
			return (
				<div>
					<div>
						【{statusMapInfo?.name}】
						{record.invoiceLastTime
							&& dayjs(record.invoiceLastTime).format(
								"YYYY-MM-DD HH:mm:ss"
							)}
					</div>
					<div>{record.invoiceLogisticsDetail}</div>
				</div>
			);
		}
		return "暂无";
	};

	const renderInterceptDetail = (_, record) => {
		return (
			<div className="r-flex">
				{getInterceptLock(
					record["logisticsInterceptMark"],
					record["interceptInvestorStr"],
					record["interceptStatusDesc"]
				)}
				<div>{record.interceptStatusDesc == "已申请拦截" ? "已发起拦截" : record.interceptStatusDesc}</div>
			</div>
		);
	};

	const renderInterceptApplyTime = (_, record) => {
		if (!record.interceptApplyTime) {
			return;
		}
		return dayjs(record.interceptApplyTime).format("YYYY-MM-DD HH:mm:ss");
	};

	const renderRefundCreated = (_, record) => {
		if (!record.refundCreated) {
			return;
		}
		return dayjs(record.refundCreated).format("YYYY-MM-DD HH:mm:ss");
	};

	const renderRefundId = (_, record) => {
		return (
			<div className="r-flex r-word-break">
				<a
					href={
						getPlatformTradeLink(record["platform"], record).refund
					}
					target="_blank"
					rel="noreferrer"
				>
					<span className="kdzs-link-text">{record.refundId}</span>
				</a>

				<span hidden={ !record.refundId }>
					<CopyOutlined
						onClick={ () => {
							copyToPaste(record.refundId);
						} }
						className="r-fc-black-65 r-pointer r-ml-4 r-mr-5"
					/>
				</span>
			</div>
		);
	};

	const renderTid = (_, record) => {
		return (
			<div className="r-flex r-word-break">
				<a
					href={
						getPlatformTradeLink(record["platform"], record).trade
					}
					target="_blank"
					rel="noreferrer"
				>
					<span className="kdzs-link-text">{record.tid}</span>
				</a>
				<span hidden={ !record.tid }>
					<CopyOutlined
						onClick={ () => {
							copyToPaste(record.tid);
						} }
						className="r-fc-black-65 r-pointer r-ml-4 r-mr-5"
					/>
				</span>
			</div>
		);
	};

	const renderPtTid = (_, record) => {
		return (
			<div className="r-flex r-word-break">
				<a
					href={
						getPlatformTradeLink(record["platform"], record).trade
					}
					target="_blank"
					rel="noreferrer"
				>
					<span className="kdzs-link-text">{record.ptTid}</span>
				</a>
				<span hidden={ !record.ptTid }>
					<CopyOutlined
						onClick={ () => {
							copyToPaste(record.ptTid);
						} }
						className="r-fc-black-65 r-pointer r-ml-4 r-mr-5"
					/>
				</span>
			</div>
		);
	};

	const renderGmtCreate = (_, record) => {
		if (!record.gmtCreate) {
			return;
		}
		return dayjs(record.gmtCreate).format("YYYY-MM-DD HH:mm:ss");
	};

	const renderInvoiceLogisticsName = (_, record) => {
		return (
			<div>
				<div>{record?.invoiceLogisticsName}</div>
				<div>{record?.exName}</div>
			</div>
		);
	};

	// 打开批量修改备注弹框
	const openModifyMemo = (list: any[]) => {
		const packList = list.map((item) => ({
			trades: [
				{
					sellerMemo: item.sellerMemo,
					sellerMemoFlag: item.sellerFlag,
					tid: item.tid,
					ptTid: item?.ptTid || "",
				}
			],
			platform: item.platform,
			source: item.source,
			togetherId: item.tid,
			sellerId: item.sellerId,
			refundStatus: item.refundStatus || "",
			afterSalesFlag: true,
		}));
		setIsShowBatchModifyMemoModal(true);
		setModifyMemoPackage(packList);
	};

	// 编辑备注
	const modifyMemo = (items, index: number) => {
		console.log(items, "modifyMemo");
		if (items?.length < 1) {
			message.info("请先选择");
			return;
		}
		if (items.some(item => item?.isNoTradeMess)) {
			message.info("无主件不可进行备注");
			return;
		}
		openModifyMemo(items);
	};

	// 编辑线下备注
	const modifyOfflineMemo = (items, index) => {
		if (items?.length < 1) {
			message.info("请先选择");
			return;
		}
		setOfflineMemoData({ visible: true, list: items });
	};

	// 编辑线下备注
	const handleOfflineMemoOk = ({ list }) => {
		console.log("handleOfflineMemoOk:", list);
		setOfflineMemoData((prev) => {
			return {
				...prev,
				loading: true,
			};
		});
		const newList = list.map((item) => ({
			...item,
			updateType: UpdateTypeEnum.线下备注,
			requestSource: AFTERSALE_REQUEST_SOURCE.快递拦截,
		}));
		BatchUpdateLocalNoteApi(newList)
			.then((res) => {
				console.log(res);
				setOfflineMemoData({ visible: false, loading: false });
				message.success("编辑成功");
				refreshPage();
			})
			.catch(() => {
				setOfflineMemoData((prev) => ({ ...prev, loading: false }));
			});
	};

	const handleOfflineMemoCancel = () => {
		setOfflineMemoData({ visible: false });
	};

	const columns: ColumnsType<
		SelectLogisticsInterceptListResponse["data"]["list"][number]
	> = [
		{
			title: "序号",
			dataIndex: "serialNumber",
			fixed: "left",
			width: 60,
			render: renderSerialNumber,
		},
		{
			title: "发货快递公司",
			dataIndex: "invoiceLogisticsName",
			width: 200,
			minWidth: 100,
			render: renderInvoiceLogisticsName,
		},
		{
			title: "发货快递单号",
			dataIndex: "invoiceNo",
			render: renderInvoiceNo,
			width: 200,
			minWidth: 100,
		},
		{
			title: "物流详情",
			dataIndex: "invoiceDetail",
			width: 200,
			minWidth: 100,
			render: renderInvoiceDetail,
		},
		{
			title: "拦截状态",
			dataIndex: "interceptStatusDesc",
			width: 100,
			minWidth: 100,
			render: renderInterceptDetail,
		},
		{
			title: "拦截申请时间",
			dataIndex: "interceptApplyTime",
			render: renderInterceptApplyTime,
			width: 150,
			minWidth: 100,
		},
		{
			title: "拦截申请人",
			dataIndex: "interceptApplyPeople",
			width: 100,
			minWidth: 100,
		},
		{
			title: "售后申请时间",
			dataIndex: "refundCreated",
			render: renderRefundCreated,
			width: 150,
			minWidth: 100,
		},
		{
			title: "平台售后状态",
			dataIndex: "refundStatusDesc",
			width: 100,
			minWidth: 100,
			// render: renderSerialNumber
		},
		{
			title: "售后金额",
			dataIndex: "refundFee",
			width: 100,
			minWidth: 100,
			render: (_, record, index) => {
				// 0或者没有退款运费则不展示
				const showRefundFreight = [PLAT_KS].includes(record.platform) && record?.freight;
				return (
					<div>
						<div className={ cs("r-fc-black-65") }>
							{record?.refundFee}
						</div>
						<div
							hidden={ !showRefundFreight }
							className={ cs("r-fc-black-65") }
						>
							退货运费：{record?.freight}
						</div>
					</div>
				);
			},
		},
		{
			title: "店铺",
			dataIndex: "sellerNick",
			width: 150,
			minWidth: 100,
			render: (_, record, index) => {
				return (
					<div>
						<div
							hidden={ record.isNoTradeMess }
							className={ cs(
								"r-fc-black-65",
								"r-wb-bw",
								"r-flex",
								"r-ai-c"
							) }
						>
							<PlatformIcon platform={ record["platform"] } />
							<span>
								{
									userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isAfterSaleSourceScm(record)
										? "****"
										: getShopName({
											plat: record?.platform,
											sellerNick: record?.sellerNick || "",
										})
								}
							</span>
						</div>
					</div>
				);
			},
		},
		{
			title: "备注",
			dataIndex: "sellerMemo",
			width: 140,
			minWidth: 100,
			render: (text, record, index) => {
				// 添加判断是否为京喜订单的逻辑
				const hasJingxi = record.refundTagList?.includes('jxTrade');
				
				return (
					<>
						<div className="r-flex r-fw-w">
							<p className={ `${cs("r-fc-black-65")}` }>
								订单备注：
							</p>
							<p>
								{getTradeFlag(0, null, record.sellerFlag)}
								{getTradeFlagTag(
									record.sellerFlag,
									record?.sellerFlagTag
								)}
								<span className={ cs("r-fc-black-65") }>
									{record["sellerMemo"]}
								</span>
								{!hasJingxi && (
									<span className="r-as-c r-ml-2 r-fc-1890FF">
										{record["sellerMemo"] ? (
											<span
												className="r-pointer"
												onClick={ () => {
													modifyMemo([record], index);
												} }
											>
												<Icon type="bianji" />
											</span>
										) : (
											<span
												className="r-pointer"
												onClick={ () => {
													modifyMemo([record], index);
												} }
											>
												<Icon type="tianjiabeizhu" />
											</span>
										)}
									</span>
								)}
							</p>
						</div>
					</>
				);
			},
		},
		{
			title: "线下备注",
			dataIndex: "localContent",
			width: 140,
			minWidth: 100,
			render: (text, record, index) => {
				return (
					<>
						<div className="r-flex r-fw-w r-mt-4">
							<p className={ `${cs("r-fc-black-65")}` }>
								线下备注：
							</p>
							<p>
								<span className={ cs("r-fc-black-65") }>
									{record["localContent"]}
								</span>
								<span className="r-as-c r-ml-2 r-fc-1890FF">
									{record["localContent"] ? (
										<span
											className="r-pointer"
											onClick={ () => {
												modifyOfflineMemo(
													[record],
													index
												);
											} }
										>
											<Icon type="bianji" />
										</span>
									) : (
										<span
											className="r-pointer"
											onClick={ () => {
												modifyOfflineMemo(
													[record],
													index
												);
											} }
										>
											<Icon type="tianjiabeizhu" />
										</span>
									)}
								</span>
							</p>
						</div>
					</>
				);
			},
		},
		{
			title: "售后单号",
			dataIndex: "refundId",
			render: renderRefundId,
			width: 180,
			minWidth: 100,
		},
		{
			title: "系统单号",
			dataIndex: "tid",
			render: renderTid,
			width: 200,
			minWidth: 100,
		},
		{
			title: "订单编号",
			dataIndex: "ptTid",
			render: renderPtTid,
			width: 200,
			minWidth: 100,
		},
		{
			title: "添加时间",
			dataIndex: "gmtCreate",
			render: renderGmtCreate,
			width: 150,
			minWidth: 100,
		}
	] as any;
	/** ---------------------------------------- */

	// 查询列表数据
	const getProductListApi = async(value, searchType) => {
		console.log(value);
		const {
			rangeTime = initialRangeTime,
			timeType = "create",
			sysItemInclude,
			pageSize = 10,
			pageNo = 1,
			reviewStatus,
			platformInfo,
			timeout = {},
		} = value;
		let params = {
			pageSize,
			pageNo,
			multiShopS: null,
		};
		// 需要特殊处理的搜索项
		const excludeFields = ["rangeTime", "timeType", "platformInfo"];
		const booleanFields = [OfflineMemoEnum.是否有线下备注];
		for (const key in value) {
			if (
				!isUndefined(value[key])
				&& value[key] !== "ALL"
				&& !excludeFields.includes(key)
			) {
				if (value[key]) {
					if (booleanFields.includes(key)) {
						params[key] = value[key] == 1;
					} else {
						// 状态需要转换为number
						params[key] = value[key];
					}
				}
			}
		}
		// 留言备注
		let flagValue;
		let flagSelValue;
		let sellerFlag;
		if (value.flagValue?.includes('_')) {
			flagSelValue = value.flagValue;
		} else {
			flagValue = value.flagValue;
		}
		if (value.sellerFlag?.length > 1) {
			let curFlag: any[] = [];
			flagGroup.forEach(item => {
				if (value.sellerFlag.includes(item.toSellerFlag)) {
					curFlag.push(item.value);
				}
			});
			flagSelValue = curFlag.toString() + '_1';
		} else {
			sellerFlag = value?.sellerFlag?.toString();
		}

		flagValue = flagValue === '-1' ? '10' : flagValue;
		params['flagValue'] = flagValue;
		params['flagSelValue'] = flagSelValue;

		params['sellerMemo'] = value.sellerMemo;
		params['sellerFlag'] = sellerFlag;
		// 申请时间/修改时间
		if (rangeTime) {
			const [startTime, endTime] = rangeTime;
			if (!startTime && !endTime) {
				message.error("查询时间不能为空");
				return;
			}
			if (!startTime) {
				message.error("开始时间不能为空");
				return;
			}
			if (!endTime) {
				message.error("结束时间不能为空");
				return;
			}
			params[`${timeType}TimeStart`] = rangeTime[0].format(
				"YYYY-MM-DD HH:mm:ss"
			);
			params[`${timeType}TimeEnd`] = rangeTime[1].format(
				"YYYY-MM-DD HH:mm:ss"
			);
		}
		if (platformInfo) {
			const { plats, plat_sellerIds } = platformInfo || {};
			let multiShops = await getMultiShopsWithFilter({ plats, plat_sellerIds, hidePlatforms: [PLAT_KTT, PLAT_DW, PLAT_YZ] });
			multiShops && (params["multiShopS"] = multiShops);

			if (!plats?.length && !plat_sellerIds?.length) {
				params["shopIsActualSelect"] = 0; // 页面是否实际勾选店铺查询 0否 1是
			} else {
				params["shopIsActualSelect"] = 1;
			}
		}

		setSearchParams(params);
		return SelectLogisticsInterceptListApi(params);
	};

	const responseAdapter = (data, params) => {
		setSelectedRows([]);
		setSelectedRowKeys([]);
		// 当页数大于2的时候，后端返回的total是0，因为如果每页都加上total，后端的查询性能会降低
		if (data.pageNo == 1 && isNumber(data.total)) {
			event.emit(
				EVENT_BUS.UPDATE_EXPRESS_INTERCEPT_COUNT,
				data.total,
				pageLoaded
			);
		}
		const dataSource = data.list.map((i, index) => ({
			rowId: `${i.id}_${0}_${1}`,
			groupId: `groupId${index}`,
			...i,
		}));
		setPagination({
			current: data.pageNo,
			pageSize: data.pageSize,
		});
		setDataSource(dataSource);
		setMissedList(data?.missedList || []);
		return {
			list: dataSource,
			total: data.total,
		};
	};

	const onFieldsChange = (changedValues, allValues) => {
		// 如果留言备注更改了，清空备注或留言的输入框内容
		if ('flagValue' in changedValues) {
			form.setFieldsValue({
				sellerMemo: ''
			});
		}
		setFormData(allValues);
		if ("interceptTabShowType" in changedValues) {
			const params = {
				timeType: "gmtCreate",
				rangeTime: [dayjs().subtract(countSetting.statisticsDay - 1, "d").startOf('day'), dayjs().endOf('day')],
			};
			if (allValues.interceptTabShowType === "WAIT_INTERCEPT") {
				params.interceptStatusList = [1];
			} else if (allValues.interceptTabShowType === "ALREADY_APPLY_INTERCEPT") {
				params.interceptStatusList = [2];
			} else if (allValues.interceptTabShowType === "INTERCEPT_SUCCESS") {
				params.interceptStatusList = [3];
			} else if (allValues.interceptTabShowType === null) {
				params.interceptStatusList = [];
			}
			form.setFieldsValue({
				...params
			});
			ref?.current?.submit();
			setActiveTabItem(allValues.interceptTabShowType);
		} else {
			setActiveTabItem("");
		}
	};

	const onClickMarkRefundStatus = (type) => {
		if (isEmpty(selectedRows)) {
			message.error("请先选择快递");
			return;
		}
		if (
			[
				MarkRefundStatus.取消标记申请拦截,
				MarkRefundStatus.标记申请拦截
			].includes(type)
		) {
			setMarkIntercepting(true);
		}
		if (
			[
				MarkRefundStatus.标记拦截成功,
				MarkRefundStatus.取消标记拦截成功
			].includes(type)
		) {
			setMarkInterceptingSuccess(true);
		}
		setResultModalTitle(`【${MarkRefundStatus[type] || ""}】操作结果`);
		MarkLogisticsInterceptStatusApi({
			ids: selectedRows.map((i) => i.id),
			handleWay: type,
		})
			.then((res) => {
				const { successNum, batchResults } = res;
				setResultModalSuccessList(new Array(successNum));
				setResultModalFailList(batchResults);
				setResultModalVisible(true);
				ref?.current?.refresh();
			})
			.finally(() => {
				setMarkIntercepting(false);
				setMarkInterceptingSuccess(false);
			});
	};

	const handleSetBtnsDefault = (fieldName, value) => {
		setOperateBtnsDefault(prev => {
			local.set("expressInterceptOperateBtnsDefault", {
				...prev,
				[fieldName]: value
			});
			return {
				...prev,
				[fieldName]: value
			};
		});
	};


	const BatchMemoBtns = useCallback(() => {
		return (
			<Dropdown.Button
				onClick={ _.debounce(() => {
					if (operateBtnsDefault.batchMemoBtn === "batchMemoOutline") {
						modifyOfflineMemo(selectedRows, 0);
					} else {
						modifyMemo(selectedRows, 0);
					}
				}, 500, {
					leading: true,
					trailing: false
				}) }
				className="r-mr-8"
				overlay={ (
					<Menu>
						<Menu.Item
							key="batchMemo"
							onClick={ _.debounce(() => modifyMemo(selectedRows, 0), 500, {
								leading: true,
								trailing: false
							}) }
						>
							<div className="r-flex r-jc-sb r-ai-c">
								<span className="r-mr-8">批量备注</span>
								{
									operateBtnsDefault?.batchMemoBtn === "batchMemo"
										? (
											<Icon
												className="r-c-warning"
												type="guding"
												onClick={ (e) => {
													e.stopPropagation();
												} }
											/>
										)
										: (
											<Icon
												className="r-c-999"
												type="weiguding"
												onClick={ (e) => {
													e.stopPropagation();
													handleSetBtnsDefault("batchMemoBtn", "batchMemo");
												} }
											/>
										)
								}
							</div>
						</Menu.Item>
						<Menu.Item key="batchMemoOutline" onClick={ ({ key }) => modifyOfflineMemo(selectedRows, 0) }>
							<div className="r-flex r-jc-sb r-ai-c">
								<span className="r-mr-8">批量线下备注</span>
								{
									operateBtnsDefault?.batchMemoBtn === "batchMemoOutline"
										? (
											<Icon
												className="r-c-warning"
												type="guding"
												onClick={ (e) => {
													e.stopPropagation();
												} }
											/>
										)
										: (
											<Icon
												className="r-c-999"
												type="weiguding"
												onClick={ (e) => {
													e.stopPropagation();
													handleSetBtnsDefault("batchMemoBtn", "batchMemoOutline");
												} }
											/>
										)
								}
							</div>
						</Menu.Item>
					</Menu>
				) }
				key="batchMemoBtn"
			>
				{ operateBtnsDefault?.batchMemoBtn === "batchMemoOutline" ? "批量线下备注" : "批量备注" }
			</Dropdown.Button>
		);
	}, [operateBtnsDefault, selectedRows]);


	const batchOptBtn = useMemo(() => {
		return (
			<div className={ styles["batch-opt-btn-container"] }>
				<div>
					<Button type="primary" onClick={ onCreateExpress }>
						新增拦截快递
					</Button>
				</div>
				<div>
					<Dropdown.Button
						onClick={ () => {
							sendPoint(Pointer.快递拦截_标记申请拦截_点击);
							onClickMarkRefundStatus(
								MarkRefundStatus.标记申请拦截
							);
						} }
						getPopupContainer={ (el) => el?.parentNode }
						overlayStyle={ { width: "100%" } }
						loading={ markIntercepting }
						overlay={ (
							<Menu
								onClick={ () => {
									sendPoint(
										Pointer.快递拦截_取消标记申请拦截_点击
									);
									onClickMarkRefundStatus(
										MarkRefundStatus.取消标记申请拦截
									);
								} }
							>
								<Menu.Item key="cancelClose">
									取消标记申请拦截
								</Menu.Item>
							</Menu>
						) }
					>
						标记申请拦截
					</Dropdown.Button>
				</div>

				<div>
					<Dropdown.Button
						onClick={ () => {
							sendPoint(Pointer.快递拦截_标记拦截成功_点击);
							onClickMarkRefundStatus(
								MarkRefundStatus.标记拦截成功
							);
						} }
						getPopupContainer={ (el) => el?.parentNode }
						overlayStyle={ { width: "100%" } }
						loading={ markInterceptingSuccess }
						overlay={ (
							<Menu
								onClick={ () => {
									sendPoint(
										Pointer.快递拦截_取消标记拦截成功_点击
									);
									onClickMarkRefundStatus(
										MarkRefundStatus.取消标记拦截成功
									);
								} }
							>
								<Menu.Item key="cancelClose">
									取消标记拦截成功
								</Menu.Item>
							</Menu>
						) }
					>
						标记拦截成功
					</Dropdown.Button>
				</div>
				<div>
					<Button onClick={ batchExportOnClick }>导出单号</Button>
				</div>
				<div>
					<Button onClick={ batchCopyOnClick }>批量复制单号</Button>
				</div>
				<div>
					<Button onClick={ batchDeleteOnClick }>删除拦截记录</Button>
				</div>
				{ BatchMemoBtns() }
			</div>
		);
	}, [
		activeTabItem,
		selectedRows,
		markIntercepting,
		markInterceptingSuccess,
		searchParams,
		dataSource,
		operateBtnsDefault
	]);

	const expandContext = (
		<div
			style={ { width: "100%", padding: "12px 0" } }
			className="r-flex r-ai-c r-jc-sb"
		>
			<div>{batchOptBtn}</div>
			<Button type="default" onClick={ onClickInterceptConfig }>
				快递拦截设置
			</Button>
		</div>
	);

	// 表格搜索项
	const tabList = useMemo(() => {
		return Object.keys(ExpressInterceptTopItems).map((i) => ({
			...ExpressInterceptTopItems[i],
			key: i,
		}));
	}, []);

	const formFieldList = useMemo(() => {
		return [
			{
				name: "",
				style: { marginBottom: 8, alignItems: "center" },
				colProps: {
					span: 24,
				},
				children: (
					<div className="r-flex r-jc-sb">
						<Form.Item noStyle name="interceptTabShowType">
							<ExpressInterceptTopTab
								activeTabItem={ activeTabItem }
								tabList={ tabList }
								pageLoaded={ pageLoaded }
								rangeTime={ form?.getFieldValue("rangeTime") }
								shopIsActualSelect={ searchParams?.shopIsActualSelect }
							/>
						</Form.Item>
						<div>
							<Button
								icon={ <SettingOutlined /> }
								onClick={ (e) => {
									e.stopPropagation();
									setShowCountSettingModal(true);
								} }
							>统计设置
							</Button>
						</div>
					</div>
				),
			},
			{
				name: "timeType",
				initialValue: "gmtCreate",
				children: (
					<Select size="small" style={ { width: 170 } }>
						<Option value="refundApply">售后申请时间</Option>
						<Option value="interceptApply">拦截申请时间</Option>
						<Option value="gmtCreate">添加时间</Option>
					</Select>
				),
			},
			{
				name: "rangeTime",
				initialValue: getCacheDateRange(DatePickerKey.aftersale_trade) || [
					dayjs().subtract(6, "days").startOf("day"),
					dayjs().endOf("day")
				],
				children: (
					<KdzsDateRangePicker1
						style={ { width: "169px" } }
						cacheQuickChoose
						datePickerKey={ DatePickerKey.aftersale_trade }
						useServeTime
					/>
				),
			},
			{
				name: "platformInfo",
				initialValue: "ALL",
				children: (
					<ShopMultiSelect
						hidePlatforms={ [PLAT_KTT, PLAT_DW, PLAT_YZ] }
						isSendPoint
						style={ { width: 169 } }
						size="small"
						isHasHandPlat
					/>
				),
			},
			{
				name: "interceptStatusList",
				initialValue: [],
				children: (
					<Select
						maxTagCount={ 1 }
						size="small"
						style={ { width: 170 } }
						placeholder="拦截状态"
						mode="multiple"
					>
						{" "}
						{InterceptStatusList.map((type) => {
							return (
								<Option key={ type.value } value={ type.value }>
									{type.name}
								</Option>
							);
						})}
					</Select>
				),
			},
			{
				name: "timeOutSearchEnum",
				children: (
					<Select
						size="small"
						style={ { width: 170 } }
						placeholder="拦截超时时间"
					>
						<Option value="ALL">全部</Option>
						<Option value="MORE_THAN_SEVEN_DAY">7天</Option>
						<Option value="MORE_THAN_THREE_DAY">72小时</Option>
						<Option value="MORE_THAN_TWO_DAY">48小时</Option>
						<Option value="MORE_THAN_ONE_DAY">24小时</Option>
					</Select>
				),
			},
			{
				name: "refundStatus",
				children: (
					<Select
						size="small"
						style={ { width: 170 } }
						placeholder="平台退款状态"
					>
						{RefundStatusList.map((type) => (
							<Option key={ type.value } value={ type.value }>
								{type.name}
							</Option>
						))}
					</Select>
				),
			},
			{
				name: "invoiceStatus",
				children: (
					<Select
						size="small"
						style={ { width: 170 } }
						placeholder="物流状态查询"
					>
						{ExpressStatusList.map((type) => (
							<Option key={ type.value } value={ type.value }>
								{type.name}
							</Option>
						))}
					</Select>
				),
			},
			{
				name: "refundId",
				label: "",
				children: (
					<InputMulti
						maxInputNum={ 1000 }
						style={ { width: 170 } }
						placeholder="售后订单编号"
						size="small"
					/>
				),
			},
			{
				name: "interceptUserName",
				label: "",
				children: <Input placeholder="拦截申请人" style={ { width: 170 } } />,
			},
			{
				name: "logisticsCompanyCode",
				label: "",
				children: (
					<Select
						size="small"
						style={ { width: 170 } }
						placeholder="发货快递公司"
					>
						{expressList?.map((express) => (
							<Option key={ express.exCode } value={ express.exCode }>
								{express.exName}
							</Option>
						))}
					</Select>
				),
			},
			{
				name: "invoiceNo",
				label: "",
				children: (
					<InputMulti
						maxInputNum={ 1000 }
						style={ { width: 170 } }
						placeholder="发货运单号"
						size="small"
					/>
				),
			},
			{
				name: "",
				label: "",
				children: (
					<>
						<Form.Item noStyle name="flagValue">
							<FlagAndMemoSelect size="small" style={ { width: 170 } } nodeMessage />
						</Form.Item>
						{
							formData.flagValue == "-1" && (
								<>
									<Form.Item noStyle name="sellerMemo">
										<InputMulti
											placeholder="备注内容"
											maxInputNum={ 50 }
											maxInputLength={ 1000 }
											lengthErrorMsg="备注最多输入1000个字数，请重新输入"
											numErrorMsg="单次查询最多筛选50个备注，请重新输入"
											className="r-ml-8"
											style={ { width: '170px' } }
											size="small"
										/>
									</Form.Item>
									<Form.Item noStyle name="sellerFlag">
										<FlagSelect
											placeholder="旗帜"
											className="r-ml-8"
											style={ { width: '170px' } }
											size="small"
										/>
									</Form.Item>
								</>
							)
						}
					</>
				)
			},
			{
				name: "",
				label: "",
				children: (
					<>
						<Form.Item
							noStyle
							name={ OfflineMemoEnum.是否有线下备注 }
	
						>
							<Select size="small" style={ { width: 170 } } allowClear placeholder="线下备注">
								<Option value="0">无线下备注</Option>
								<Option value="1">有线下备注</Option>
							</Select>
						</Form.Item>
						{
							formData[OfflineMemoEnum.是否有线下备注] == "1" && (
								<Form.Item
									noStyle
									name={ OfflineMemoEnum.线下备注内容 }
								>
									<Input className="r-ml-8" style={ { width: 170, verticalAlign: 'top' } } placeholder="请输入线下备注" />
								</Form.Item>
							)
						}
					</>
				),
			}
		];
	}, [activeTabItem, formData]);

	const rowSelection = {
		columnWidth: 30,
		onChange: (selectedRowKeysAct: number[], selectedRows: []) => {
			console.log(
				"%c [ selectedRowKeysAct ]: ",
				"color: #bf2c9f; background: pink; font-size: 13px;",
				selectedRowKeysAct
			);
			setSelectedRows([...selectedRows]);
			setSelectedRowKeys([...selectedRowKeysAct]);
		},
		renderCell: (checked: boolean, row, index: number, originNode: any) => {
			const { key, value } = {};
			const [rId, i, length] = row.rowId?.split("_");
			const disabledMap = {};
			if (value && value.length && dataSource) {
				value.forEach((v) => {
					if (dataSource[v]) {
						const [id] = dataSource[v]["rowId"]?.split("_");
						if (!disabledMap[id]) disabledMap[id] = [];
						disabledMap[id].push(v);
					}
				});
			}

			let cloneNode = originNode;
			let groupIndex = +row.groupId?.replace("groupId", "") + 1;
			if (
				disabledMap[rId]
				&& Number(length) === disabledMap[rId].length
			) {
				cloneNode = React.cloneElement(originNode, {
					disabled: true,
				});
			}
			return {
				children: <>{cloneNode}</>,
				props: {
					// rowSpan: row.colSpan || 0,
					style: row.colSpan ? {} : { borderTop: 0 },
				},
			};
		},
		getCheckboxProps: (record: any) => {
			return {
				// disabled: key ? value.includes(record['sysSkuList'][0][key]) : false
			};
		},
		selectedRowKeys,
	};
	const onResultModalFinished = () => {
		setResultModalVisible(false);
		setResultModalTitle("");
	};

	// 列宽设置
	const onResizeChange = (v) => {
		setInitMap(v);
		UpdateRefundGlobalConfigApi([
			{
				biz: ExpressInterceptGlobalConfig.快递拦截列宽配置,
				value: JSON.stringify(v),
			}
		]);
	};

	// 列配置设置
	const onSortChange = (v) => {
		setInitList(v);
		UpdateRefundGlobalConfigApi([
			{
				biz: ExpressInterceptGlobalConfig.快递拦截列配置,
				value: JSON.stringify(v),
			}
		]);
	};

	// 列配置恢复默认
	const onSortReset = (width, list) => {
		onResizeChange(width);
		onSortChange(list);
	};

	const getSearchConditionConfig = async(location) => {
		try {
			let res = await GetRefundGlobalConfigListApi({
				bizEnumList: [
					ExpressInterceptGlobalConfig.快递拦截列宽配置,
					ExpressInterceptGlobalConfig.快递拦截列配置,
					ExpressInterceptGlobalConfig.数据更新时间配置
				],
			});

			res.forEach((config) => {
				if (
					config.biz === ExpressInterceptGlobalConfig.快递拦截列宽配置
				) {
					if (config.value) {
						try {
							let cValue = JSON.parse(config.value);
							if (typeof cValue == "object") {
								console.log(
									"%c [ 快递拦截列宽配置 ]: ",
									"color: #bf2c9f; background: pink; font-size: 13px;",
									cValue
								);
								setInitMap(cValue);
							}
						} catch (error) {
							console.log(error);
						}
					}
				} else if (
					config.biz === ExpressInterceptGlobalConfig.快递拦截列配置
				) {
					if (config.value) {
						try {
							let cValue = JSON.parse(config.value);
							if (Array.isArray(cValue)) {
								console.log(
									"%c [ 快递拦截列配置 ]: ",
									"color: #bf2c9f; background: pink; font-size: 13px;",
									cValue
								);
								setInitList(cValue);
							}
						} catch (error) {
							console.log(error);
						}
					}
				} else if (
					config.biz === ExpressInterceptGlobalConfig.数据更新时间配置
				) {
					let setting = { statisticsDay: 7 };
					if (config.value) {
						try {
							let cValue = JSON.parse(config.value);
							setting = cValue;
						} catch (error) {
							console.log(error);
						}
					}
					setCountSetting(setting);
					const tabStatus = location?.state?.tabStatus;
					if (tabStatus) {
						form.setFieldsValue({
							interceptStatusList: [ExpressInterceptTopItemsEnum[tabStatus]],
							timeType: "gmtCreate",
							rangeTime: [dayjs().subtract((countSetting.statisticsDay ?? setting.statisticsDay) - 1, "d").startOf("day"), dayjs().endOf("day")],
							interceptTabShowType: tabStatus,
						});
						setActiveTabItem(tabStatus);
					}
					ref.current?.submit();
				}
			});
		} catch (error) {
			console.log(error);
		}
	};

	// 操作完成 刷新页面
	const refreshPage = () => {
		ref?.current?.refresh(() => {
			console.log("操作完成 刷新页面");
		});
	};

	useEffect(() => {
		const fn = async() => {
			if (await lowVersionLock(PageNameControlEnum.快递拦截)) return;
			setPageLoaded(true);
		};
		fn();
	}, []);

	const onReset = () => {
		setTimeout(() => {
			setFormData(form.getFieldsValue());
		}, 0);
	};

	return (
		<NormalLayout>
			<SearchTable
				pageSizeId="expressInterceptListTable"
				ref={ ref }
				form={ form }
				showSearchToggle
				fetchData={ getProductListApi }
				searchBtnPoint={ Pointer.快递拦截_查询_点击 }
				responseAdapter={ responseAdapter }
				onReset={ onReset }
				autoSearch={ false }
				rowFormConfig={ {
					formList: formFieldList,
					style: {
						background: "#ffffff",
						padding: "16px",
					},
					rowProps: {}, // 表单行配置
					colProps: {}, // 表单列配置
					formItemProps: {},
				} }
				baseTableConfig={ {
					noGap: true,
					innerTableStyle: { paddingTop: 0 },
					rowKey: "rowId",
					// groupId: 'groupId', // 这里不需要
					onFieldsChange,
					columns,
					expandContext,
					expandContextStyle: {
						marginBottom: "0px",
						padding: "0 16px 8px",
					},
					// hasVt: false,
					cachePgination: true,
					pagination: {
						pageSizeOptions: [10, 20, 50, 100, 200],
					},
					// scroll: { x: 1920 },
					rowSelection: {
						type: "checkbox",
						...rowSelection,
						...{
							selectedRowKeys,
						},
					},
					headerColSet: {
						inPlace: ColSetPlaceEnum.默认,
						resizeId: `ExpressIntercept_width_${userStore?.userInfo?.userId}_${userStore?.userInfo?.subUserId}`,
						sortId: `ExpressIntercept_sort_${userStore?.userInfo?.userId}_${userStore?.userInfo?.subUserId}`,
						onResizeChange,
						onSortChange,
						onReset: onSortReset,
						initMap,
						initList,
					},
				} }
			/>
			{createExpressModalVisible && (
				<CreateExpressModal
					onOk={ createExpressModalOnOk }
					onCancel={ createExpressModalOnCancel }
				/>
			)}
			{interceptConfigModalVisible && (
				<InterceptConfigModal
					onOk={ interceptConfigModalOnOk }
					onCancel={ interceptConfigModalOnCancel }
				/>
			)}
			{resultModalVisible && (
				<ResultModal
					successList={ resultModalSuccessList }
					failList={ resultModalFailList }
					title={ resultModalTitle }
					onCancel={ onResultModalFinished }
					onOk={ onResultModalFinished }
				/>
			)}

			<LowVersionControlModal pageName={ PageNameControlEnum.快递拦截 } />

			{/* 订单备注 */}
			{isShowBatchModifyMemoModal ? (
				<BatchModifyMemoModal
					onOk={ () => {
						setIsShowBatchModifyMemoModal(false);
						refreshPage();
					} }
				/>
			) : (
				""
			)}

			{/* 线下备注 */}
			<OfflineMemoModal
				onOk={ handleOfflineMemoOk }
				onCancel={ handleOfflineMemoCancel }
				data={ offlineMemoData }
				fromPage={ EnumFromPage.快递拦截 }
			/>

			{/* 统计设置 */}
			<CountSettingModal visible={ showCountSettingModal } countSetting={ countSetting } onCancel={ () => setShowCountSettingModal(false) } onUpdateSetting={ onUpdateSetting } />
		</NormalLayout>
	);
};

export default observer(ExpressIntercept);
