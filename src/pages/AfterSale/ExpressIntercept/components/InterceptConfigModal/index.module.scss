.modal {
	:global {
		.ant-modal-body {
			padding-top: 12px;
		}
	}
}
.custom-input-number {
	:global {
	  .ant-input-number-input {
		padding: 0 !important;
		line-height: 32px !important;
		height: 32px !important;
		display: flex !important;
		align-items: center !important;
		vertical-align: middle !important;
		padding-left: 5px !important;
	  }
	}
  }
.container {
	display: flex;
	align-items: center;
	.shop-name {
		width: 170px;
		text-align: right;
	}
}
.setting-item {
	&:not(:first-child) {
		border-top: 1px solid #f0f0f0;
		padding-top: 12px;
	}

	.setting-item-title {
		font-weight: bold;
		font-size: 16px;
	}
	.setting-item-content {
		margin-left: 20px;
		margin-top: 10px;
		.setting-item-help {
			color: #262626;
		}
	}
	:global {
		.ant-form-item-explain {
		  position: absolute !important;
		  top: 100% !important;
		  left: 0 !important;
		  width: auto !important; /* 根据需要调整宽度 */
		  margin-top: 2px !important;
		  background-color: white !important;
		  z-index: 10 !important;
		}
		
		/* 防止表单项在验证时改变高度 */
		.ant-form-item {
		  margin-bottom: 0 !important;
		}
		
		/* 确保输入框不会因为错误而改变样式 */
		.ant-form-item-has-error .ant-input-number {
		  margin-bottom: 0 !important;
		}
	}
}

