/* eslint-disable react-hooks/rules-of-hooks */
import { CheckOutlined, CopyOutlined, ExclamationCircleOutlined, QuestionCircleOutlined, SettingOutlined } from '@ant-design/icons';
import NiceModal from '@ebay/nice-modal-react';
import { useReactive, useRequest, useTimeout, useToggle } from "ahooks";
import { Alert, Button, Dropdown, Form, Menu, Modal, Popover, Progress, Tag, Select, Space, Table, Tooltip, InputNumber } from "antd";
import { useForm } from "antd/lib/form/Form";
import { ColumnsType } from 'antd/lib/table';
import cs from 'classnames';
import dayjs, { Dayjs } from "dayjs";
import _, { cloneDeep, isNumber, isUndefined } from "lodash";
import { observer } from 'mobx-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import events from "@/utils/events";
import { calculateTimeDiff, copyToPaste, getMirrorEnum, splitFxgTid } from '@/utils';
import { local } from "@/libs/db";
import {
	BatchCreateExchangeTradeRequest,
	CreateExchangeTradeAsyncGetResponse,
	IBatchModifyMemoDataProps,
	IgnoreMatchSystemItemExceptionRefundRequest,
	ReviewRefundTradeV2Request,
	SelectRefundListWithPageRequest,
	SelectRefundListWithPageResponse
} from "@/types/schemas/aftersale/trade";
import SearchTable from "@/components/SearchTableVirtual";
import { SearchTableRefProps } from "@/components/SearchTableVirtual/SearchTable";
import message from "@/components/message";
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import {
	AgreeRefundSupplyApi,
	BatchCloseRefundApi,
	BatchCreateExchangeTradeApi,
	BatchUpdateLocalNoteApi,
	BatchUpdateRefundSystemStatusApi,
	ConfirmFinishApi,
	CreateExchangeTradeAsyncGetApi,
	GetRefundGlobalConfigListApi,
	ReviewRefundTradeV2Api,
	SelectNeedReminderSyncApi,
	SelectRefundListWithPageApi,
	UpdateRefundGlobalConfigApi
	// GetRegisterParamListApi
} from "@/apis/aftersale/trade";
import s from "./index.module.scss";

import Image from "@/components/Image";
import { getInterceptLock, getPlatformDetailLink } from "@/pages/AfterSale/TradeList/utils";
import useGetState from '@/utils/hooks/useGetState';
import { TradeDictInsertDictApi, TradeDictQueryDictApi } from '@/apis/trade/search';
import { getExCompanyAll } from "@/apis/user";
import BuyerNickComp from "@/components-biz/BuyerNickComp";
import { DistributorSelect } from '@/components-biz/Distribution';
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import OfflineMemoModal, { EnumFromPage, IBatchOfflineMemoModalProps } from '@/components-biz/OfflineMemoModal';
import { getAllPlats, getMultiShops, getMultiShopsWithFilter, getShopName, isSourceScm, isAfterSaleSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import Copy from '@/components/Copy';
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import { DatePickerKey, getCacheDateRange } from "@/components/DateRangeComp/kdzsRangePickerUtil";
import Icon from "@/components/Icon";
import IconHandleRender from "@/components/Icon/custom-icon";
import InputArrayMulti from '@/components/Input/InputArrayMulti';
import InputMulti from '@/components/Input/InputMulti';
import Input from '@/components/Input/InputSearch';
import { ColSetPlaceEnum } from '@/components/SearchTable/BaseCheckGroupTable';
import { PLAT_FXG, PLAT_JD, PLAT_YZ, PLAT_KS, PLAT_MAP, PLAT_TB, PLAT_XHS, flagGroup, PLAT_SCMHAND, PLAT_SPH, PLAT_KTT, PLAT_DW } from '@/constants';
import EnumSelect, { EnumStringSelect } from "@/components/Select/EnumSelect";
import event from '@/libs/event';
import { downloadCenter } from '@/pages/Index/DownloadCenter/utils';
import ShowLogistic from '@/pages/Report/KddLog/components/ShowLogistic';
import BatchModifyMemoModal from '@/pages/Trade/components/BatchModifyMemoModal';
import FlagAndMemoSelect from "@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagAndMemoSelect";
import PresetQueryNavBarCommon from '@/components/PresetQueryNavBarCommon';
import { BizTypeEnum } from '@/components/PresetQueryNavBarCommon/constants';
import FlagSelect from '@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagSelect';
import { getTradeFlag, getTradeFlagTag } from '@/pages/Trade/utils';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import { tradeStore } from '@/stores';
import distributionStore from '@/stores/distribution';
import userStore from '@/stores/user';
import { ModulesFunctionEnum } from '@/types/schemas/setting/download';
import { batchHandleRes } from '@/utils/batchApiRequest';
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import RefundItemType from '../components/RefundItemType';
import { EVENT_BUS, ExpressStatusEnum, EXPRESS_STATUS_OPTIONS, TRADE_LIST_SORT_OPTIONS, TRADE_SORT_CONFIG_KEY } from '../constants';
import AdvancedSettingModal from './components/AdvancedSettingModal';
import UnpackRegisterModal from './components/UnpackRegisterModal';
import AutoMemoModal from './components/AutoMemoModal';
import BatchExportModal from './components/BatchExportModal';
import BatchHandleResModal from './components/BatchHandleResModal';
import { BatchPushSupplierAsync, BatchRecallSupplierAsync, BatchSupplierConfirmModal } from "./components/BatchPushSupplierAsync";
import BatchReturnMoney from './components/BatchReturnMoney';
import BatchRefuseMoney from './components/BatchRefuseMoney';
import BindTradeModal from './components/BindTradeModal';
import CreateNoInfoOrderModal from './components/CreateNoInfoOrderModal';
import CreateOrderModal from './components/CreateOrderModal';
import CustomSearchCondition from './components/CustomSearchCondition';
import EditModal from "./components/EditModal";
import ExpandTable from "./components/ExpandTable";
import GoodsContentSetting from "./components/GoodsContentSetting";
import HandleExceptionModal from "./components/HandleExceptionModal";
import MarkProcessingStatusModal from './components/MarkProcessingStatusModal';
import OptLogModal from './components/OptLogModal';
import RefuseRefundModal from "./components/RefuseRefundModal";
import ReturnGoodsBtn from "./components/ReturnGoodsBtn";
import ReturnMoneyBtn from "./components/ReturnMoneyBtn";
import UnpackRegisterBtn from "./components/UnpackRegisterBtn";
import SyncModal from "./components/SyncModal";
import TooltipText from "./components/TooltipText";
import WriteExpressNoBtn from './components/WriteExpressNoBtn';
import QuickCheckRows from './components/QuickCheckRows';
import QuickFilterSelect from './components/QuickFilterSelect';
import SelectMultiple from './components/SelectMultiple';
import {
	AftersaleGlobalConfig,
	BatchExportType,
	BtnEnum,
	DEFAULT_BUYNICK_INFO_SETTING,
	DEFAULT_PRODUCT_CONTENT_SETTING,
	DEFAULT_ZERO_PRODUCT_CONTENT_SETTING,
	MissedType,
	OfflineMemoEnum,
	PRODUCT_CONTENT_ENUM,
	PRODUCT_CONTENT_KEY_ENUM,
	PRODUCT_SETTING_OBJECT,
	PRODUCT_SETTING_OBJECT_ENUM,
	PRODUCT_SETTING_ZERO_OBJECT,
	REFUND_SYSTEM_STATUS,
	REVERSE_MAPPING,
	UpdateTypeEnum,
	afterSaleOperateType,
	afterSaveActions,
	afterSaveTopAllItem,
	afterSaveTopItemProps,
	afterSaveTopItems,
	listItemName,
	refundSearchConditionDefaultConfig,
	refundSearchConditionDefaultIds,
	goodsStatusOptions,
	supplierOptionsEnum,

	timeTypeOptions_aftersale,
	refundStatusListOptions_aftersale,
	reviewStatusOptions_aftersale,
	refundSystemStatusListOptions_aftersale,
	refundSystemTypeListOptions_aftersale,
	invoiceNoOptions_aftersale,
	tradeStatusOptions_aftersale,
	isNoTradeMessOptions_aftersale,
	timeOutSearchOptions_aftersale,
	refundTagListOptions_aftersale,
	refundExceptionListOptions_aftersale,
	confirmItemStatusOptions_aftersale,
	handleReturnStatusOptions_aftersale,
	applyRefundFeeOptions_aftersale,
	exchangeRefundStatusListOptions_aftersale,
	scmRefundTypeOptions_aftersale,
	distributorIsPushOptions_aftersale,
	supplierIsConfirmOptions_aftersale
} from './constants';
import {
	getGoodsInfoSelectOptions,
	getPlaceholderTips,
	handleGoodsInfoSearchParams,
	itemInfoEnumValue
} from './itemInfoUtils';
import {
	PackExpressStatusEnum,
	RefundSystemTypeEnum,
	afterSaleChannelText,
	afterSaleReason,
	afterSaleTypeNames,
	afterSaleTypeText,
	bizEnumObject,
	checkRefundAction,
	distributorIsPushEnum,
	getListRefundManageStatus,
	getPlatformTradeLink,
	getWarehouseVersion,
	isBeyondInput,
	isPlatErr,
	popErrorModal,
	refundSystemStatusEnum,
	supplierIsConfirmEnum
} from "./utils";
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import BatchModifyLocalMemoPicModal from './components/BatchModifyLocalMemoPicModal';
import AbnormalSubUserModal from "./components/AbnormalSubUserModal";
import { AFTERSALE_REQUEST_SOURCE } from '@/pages/AfterSale/constants';
import { getImageThumbnail } from '@/utils/img.scale';


const { Option } = Select;
let needCloseExpand = true;
export interface SearchTableProps {
	pageNo?: number
	pageSize?: number
	timeType?: string
	rangeTime?: Dayjs
	platform?: string
	sellerNick?: string
	afterSaleType?: string
	refundStage?: string
	refundStatus?: string
	reviewStatus?: string
	idType?: string
	tid?: string
	ptTid?: string
	refundId?: string
	buyerNick?: string
	logisticsSearchType?: number
	sid?: string
	flagValue?: any
	sellerMemo?: any
	sellerFlag?: any
	goodsIncludeStatus?: string
	shortNameIncludingList?: string[]
	skuIncludingList?: string[]
	goodsNotIncludeStatus?: string
	shortNameNotIncludingList?: string[]
	skuNotIncludingList?: string[]
	minRefundFee?: number,
	maxRefundFee?: number,
	logisticsNoList?: string
	refundOrPtTidList?: string
	isQueryContainRefundTag?: number
	[key: string]: any
}

const supplierRoutes = ['/aftersale/distributorTrade']; // 供应商的路由
const DistributorRoutes = ['/aftersale/trade']; // 分销商的路由

//	实现全部关闭详情
const expandEvt = document.createEvent("MouseEvents");
expandEvt.initEvent("click", true, true);

const searchInitValue: Partial<SearchTableProps> = {
	goodsIncludeStatus: '1', // 商品包含
	logisticsSearchType: 1, // 发货/退货物流
	timeType: 'create', // 时间类型
	isQueryContainRefundTag: 1, // 售后标签包含
	applyRefundFeeType: 'applyRefundFee', // 申请售后金额
	supplierIsIncluding: supplierOptionsEnum['供应商(包含)'],
};

const searchFormInitValue = {
	"timeType": 'create', // 时间类型
	applyRefundFeeType: 'applyRefundFee', // 申请售后金额
	supplierIsIncluding: supplierOptionsEnum['供应商(包含)'],
	supplierIncludingList: undefined,
	marketIncludingList: undefined,
	stallIncludingList: undefined,
	"rangeTime": [],
	"platformInfo": {
		"plats": [],
		"plat_sellerIds": []
	},
	"afterSaleTypeList": [],
	"refundStatusList": [],
	"refundReasonList": [],
	"reviewStatus": undefined, // 审核状态
	"refundSystemTypeList": [],
	"refundSystemStatusList": [],
	"refundOrPtTidList": undefined,
	"logisticsSearchType": 1, // 发货/退货物流
	"logisticsNoList": undefined,
	"logisticsCompanyName": undefined,
	"logisticsStatusEnum": [],
	"isQueryContainRefundTag": 1,
	"refundTagList": [],
	"refundExceptionList": [],
	"newGoodsStatusList": [],
	"confirmItemStatusList": [],
	"handleReturnStatusList": [],
	"minRefundFee": undefined,
	"maxRefundFee": undefined,
	"goodsIncludeStatus": '1', // 商品包含
	"shortNameIncludingList": [],
	"skuIncludingList": [],
	"tradeStatusList": [], // tradeStatus 订单状态改为多选
	"flagValue": undefined,
	"needFindLocalContent": undefined,
	"buyerNick": undefined,
	"isNoTradeMess": undefined,
	"timeOutSearch": undefined,
	"exchangeRefundStatusList": [],
	"scmRefundType": undefined,
	"scmSupplierUserIdList": [],
	"distributorIsPush": undefined,
	"supplierIsConfirm": undefined,
	"localContent": undefined
};

const PackExpressStatusMirrorEnum = getMirrorEnum(PackExpressStatusEnum);
getMirrorEnum(refundSystemStatusEnum);
// getMirrorEnum(REFUND_SYSTEM_STATUS);
const TradeList = () => {
	const { isShowZeroStockVersion, userInfo: { level, userId }, isSupplierAccount, isDistributorAccount, isFreeSupplierAccount } = userStore;
	const ref = useRef<SearchTableRefProps>();
	const inputValueRef = useRef<string>('');
	// 在组件内部添加ref
	const batchModifyLocalMemoPicModalRef = useRef(null);
	const presetRef = useRef(null); // 预设查询条件
	const [form] = useForm();
	const { pathname } = useLocation();
	// 所有平台
	const [platformList, setPlatformList] = useState([]);
	// 异常处理弹窗
	const [exceptionModalVisible, setExceptionModalVisible] = useState(false);
	// 异常处理弹窗
	const [editModalVisible, setEditModalVisible] = useState(false);
	// 库存版本状态
	const [warehouseStatus, setWarehouseStatus] = useState(true);
	// 弹窗props
	const [curActionRowData, setCurActionRowData] = useState({});
	// 弹窗类型 editModalType
	const [editModalType, setEditModalType] = useState('');
	// 同步售后订单弹窗
	const [syncModalVisible, setSyncModalVisible] = useState(false);
	// 控制展开行是否展示货品数据
	const [tableExpandList, setTableExpandList] = useState([]);
	// 高级设置弹窗
	const [advancedSettingModalVisible, setAdvancedSettingModalVisible] = useState(false);
	// 京东拆包登记弹窗
	const [unpackRegisterModalVisible, setUnpackRegisterModalVisible] = useState(false);
	const [activeTabItem, setActiveTabItem] = useState('');
	const [refuseRefundModalVisible, setRefuseRefundModalVisible] = useState(false); // 拒绝退款\退货\退货退款弹窗 单个操作保留
	const [companyList, setCompanyList] = useState<any[]>([]);
	const [selectedRows, setSelectedRows] = useState<object[]>([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
	const { setModifyMemoPackage, setIsShowBatchModifyMemoModal, isShowBatchModifyMemoModal, modifyMemoPackage } = tradeStore;

	// 创建售后手工单弹窗
	const [createTradeModalVisible, setCreateTradeModalVisible] = useState(false);

	// 新建无主售后单
	const [createNoInfoOrderVisible, setCreateNoInfoOrderVisible] = useState(false);

	// 设置自动备注
	const [autoMemoModalVisible, setAutoMemoModalVisible] = useState(false);

	// 绑定无主件
	const [bindOrderModalVisible, setBindOrderModalVisible] = useState(false);
	const [pageLoaded, setPageLoaded] = useState(false);

	const [optLogRefundId, setOptLogRefundId] = useState(false);
	const [optLogModalVisible, setOptLogModalVisible] = useState(false);
	const [batchExportModalVisible, setBatchExportModalVisible] = useState(false);
	const [searchParams, setSearchParams] = useState({});

	const [sortValue, setSortValue] = useState(null);
	const [batchHandleResTitle, setBatchHandleResTitle] = useState("");
	const [batchHandleResModalVisible, setBatchHandleResModalVisible] = useState(false);
	const [batchOptRes, setBatchOptRes] = useState({});
	const [missedList, setMissedList] = useState([]);
	const [distributorPushList, setDistributorPushList] = useState([]);
	const [missedType, setMissedType] = useState('');
	const [pageLoading, setPageLoading] = useState(false); // 正在请求中

	/** -------- 自定义条件相关 -----------*/
	const [showAllCondition, { toggle: toggleShowAllCondition, set: setShowAllCondition }] = useToggle();
	const [customSearchConditionModalVisible, setCustomSearchConditionModalVisible] = useState(false);
	const [conditionList, setConditionList] = useState([]);
	const [formFieldList, setFormFieldList] = useState([]);
	const [markProcessingStatusModalVisible, setMarkProcessingStatusModalVisible] = useState(false);
	const [markProcessingRows, setMarkProcessingRows] = useState([]);
	const [isBatchMark, setIsBatchMark] = useState(false);
	const [dataSource, setDataSource] = useState([]);
	const [dataSourceAll, setDataSourceAll] = useState([]);
	const [showAllPresetCondition, setShowAllPresetCondition] = useState<boolean>(false);
	const [advanceSettingConfig, setAdvanceSettingConfig] = useState({});
	const initialRangeTime = getCacheDateRange(DatePickerKey.aftersale_trade) || [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')];
	/** -----------------------------------------*/
	const [offlineMemoData, setOfflineMemoData] = useState<IBatchOfflineMemoModalProps["data"]>({ visible: false });

	const [productContentList, setProductContentList] = useState<string[]>(!isShowZeroStockVersion ? DEFAULT_PRODUCT_CONTENT_SETTING : DEFAULT_ZERO_PRODUCT_CONTENT_SETTING);
	const [buyNickProductContentList, setBuyNickProductContentList] = useState<string[]>(DEFAULT_BUYNICK_INFO_SETTING);


	const [registerParamList, setRegisterParamList] = useState({});

	const isSupplier = supplierRoutes.includes(pathname) || isFreeSupplierAccount; // 能明确的供应商身份的是免费供应商版
	const isDistributor = DistributorRoutes.includes(pathname) || isDistributorAccount; // 能明确的分销商身份的是免费分销商版
	const bizObj = bizEnumObject(isDistributor);

	const [initMap, setInitMap] = useState();
	const [initList, setInitList] = useState();
	const [formData, setFormData] = useState<SearchTableProps>({ ...searchInitValue });
	const needReminderSyncModalRef = useRef(null);
	const [formOptionDefault, setFormOptionDefault] = useState({ ...searchInitValue });
	const [operateBtnsDefault, setOperateBtnsDefault] = useState({
		batchMemoBtn: "batchMemo",
		createAfterSaleBtn: BtnEnum.新建售后单,
	});
	const [hideOrderList, setHideOrderList] = useState([]);
	const [loading, setLoading] = useState(false);

	useEffect(() => {
		// 获取所有平台
		// getPlatsList();
		// setPageLoaded(true);
		// getSortConfig();
		// getSearchConditionConfig(); // 获取列配置、高级设置、自定义查询条件
		// getRegisterParamList();
	}, []);

	useEffect(() => {
		if (isSupplier) {
			distributionStore.initDistributorList();
		}
		if (isDistributor) {
			distributionStore.initSupplierList();
		}
	}, [isSupplier, isDistributor]);

	useEffect(() => {
		if (userId) {
			const defaultOption = local.get(`afterSaleListFormOptionDefault_${userId}`) || {};
			const defaultOperateBtns = local.get(`afterSaleListOperateBtnsDefault_${userId}`) || {};
			setFormOptionDefault((prev) => {
				const data = {
					...prev,
					...defaultOption,
				};
				setFormData({ ...data });
				return { ...data };
			});
			setOperateBtnsDefault((prev => {
				return {
					...prev,
					...defaultOperateBtns,
				};
			}));
			getPlatsList();
			setPageLoaded(true);
			getSortConfig();
			getSearchConditionConfig(); // 获取列配置、高级设置、自定义查询条件
		}
	}, [userId]);

	// 换货/补发手工单创建进度
	const modalProgress = useReactive({
		progress: 0,
		progressVisibility: false,
		progressResultVisibility: false, // 失败列表
		asyncKey: '',
		data: {} as CreateExchangeTradeAsyncGetResponse
	});
	// 添加修改线下备注图片的函数
	const modifyOfflineMemoPic = (item) => {
		// 添加 ref 检查
		if (batchModifyLocalMemoPicModalRef.current) {
			batchModifyLocalMemoPicModalRef.current.open({
			  refundId: item.refundId,
			  sid: item.sid,
			  localMemoPic: Array.isArray(item.localContentPicList)
			   ? item.localContentPicList.join(',')
			    : item.localContentPicList || '',
				requestSource: AFTERSALE_REQUEST_SOURCE.售后列表,
			}, refreshPage);
		  } else {
			console.error('BatchModifyLocalMemoPicModal ref is not initialized');
		  }
	};

	const progressResultColumns = [
		{
			title: '售后单号',
			width: 100,
			render: (value: any, row: any, index: number) => {
				return (
					<div>{row.operationId}</div>
				);
			},
		},
		{
			title: '失败原因',
			width: 200,
			render: (value: any, row: any, index: number) => {
				return (
					<div>{row.errorMessage}</div>
				);
			},
		}
	];

	useEffect(() => {
		event.on(EVENT_BUS.SYNC_AFTERSALE, () => {
			handleBatchAction(BtnEnum.同步售后单);
		});
		return () => {
			event.off(EVENT_BUS.SYNC_AFTERSALE, () => {
				handleBatchAction(BtnEnum.同步售后单);
			});
		};
	});

	// 获取全部快递
	useEffect(() => {
		getExCompanyAll({}).then(res => {
			setCompanyList(res || []);
		});
	}, []);

	useTimeout(async() => {
		let WarehouseVersion = await getWarehouseVersion();
		setWarehouseStatus(WarehouseVersion);
	}, 1000);

	// const getRegisterParamList = async() => {
	// 	const data = await GetRegisterParamListApi({});
	// 	setRegisterParamList(data);
	// };

	// 若售后单已推送至供应商，则以下操作不可处理，需弹窗拦截
	const checkDistributorIsPush = (type: afterSaveTopItemProps | string, row?: any) => {
		let hasError = false;

		const arr = [
			// BtnEnum.审核,
			BtnEnum.确认收货,
			BtnEnum.退货入库,
			"批量线下备注",
			// "供分销备注", // 目前没有
			BtnEnum.新建售后单,
			BtnEnum.新建无主售后单,
			BtnEnum.绑定无主件,
			"提交给供应商", // 需要对应组件里面拦截
			BtnEnum.标记工单状态,
			"关闭订单",
			"取消关闭订单"
		];

		// 单个操作
		const arrSingle = [
			// '取消审核', // 单个
			"异常处理", // 单个
			"编辑", // 单个
			// "审核通过", // 单个
			"关闭售后单", // 单个
			"取消关闭售后单", // 单个
			"标记工单状态", // 单个
			"线下备注" // 单个
		];

		if (row) {
			if (isDistributor && arrSingle.includes(type) && row?.distributorIsPush == 1) {
				hasError = true;
			}
		} else if (isDistributor && selectedRows.length && arr.includes(type) && selectedRows.some(row => row?.distributorIsPush == 1)) {
			hasError = true;
		}

		if (hasError) {
			Modal.error({
				title: '提示',
				width: 600,
				centered: true,
				closable: true,
				okText: '好的',
				content: (
					<div>不支持处理“已推送到供应商”的代发售后单</div>
				)
			});
		}
		return !hasError;
	};

	const getSortConfig = () => {
		TradeDictQueryDictApi({
			userDictEnum: TRADE_SORT_CONFIG_KEY
		}).then(res => {
			if (res.value) {
				try {
					const value = JSON.parse(res.value || "{}");
					setSortValue(value.sortBy);
				} catch (e) {
					console.log(e);
				}
			}
		});
	};

	const saveSortConfig = async(value) => {
		let success = false;
		const params = {
			userDictEnum: TRADE_SORT_CONFIG_KEY,
			value: JSON.stringify({
				sortBy: value
			})
		};
		await TradeDictInsertDictApi(params).then(() => {
			success = true;
		});
		return success;
	};

	const sortTypeOnChange = async(v) => {
		saveSortConfig(v);
		setSortValue(v);
		setTimeout(() => refreshPage());
	};

	// 获取平台
	const getPlatsList = async() => {
		const plats = await getAllPlats(true);
		setPlatformList(plats);
	};

	// 同步售后
	const { data: needReminderSync, run: getSelectNeedReminderSyncApi } = useRequest(SelectNeedReminderSyncApi, {
		manual: true,
		onSuccess: (res) => {
			let resFilter = [];
			if (Array.isArray(res)) {
				resFilter = res.filter(item => [PLAT_YZ, PLAT_KTT, PLAT_DW].includes(item.platform));
			}
			if (resFilter.length > 0) {
				needReminderSyncModalRef.current = Modal.info({
					centered: true,
					title: '订单同步',
					content: (
						<div>
							{
								resFilter.map((item, index) => {
									return (
										<div key={ `${item['sellerNick'] + index}` } className={ cs('r-flex', 'r-ai-c', 'r-jc-sb') }>
											<div className="r-flex">
												<PlatformIcon platform={ item['platform'] } />{item['sellerNick']}
											</div>
											<div className={ cs('r-ml-16', 'r-fc-black-45') }>订单初始化中...</div>
										</div>
									);
								})
							}
						</div>
					),
					onOk() { },
				});
			}
		}
	});


	// 审核订单
	const { run: onReviewRefundTradeApi } = useRequest(ReviewRefundTradeV2Api, {
		manual: true,
		onSuccess: (result) => {
			// 这个接口分批发送的，返回值需要这里处理下
			const res = [];
			result.forEach(i => {
				res.push(...(i?.data || []));
			});
			let failOrders: any = [];

			res.forEach((item) => {
				if (!item.success && item.errorMessage) {
					failOrders.push(item);
				}
			});
			if (failOrders.length > 0) {
				Modal.info({
					centered: true,
					title: '提示',
					okText: '确认',
					icon: <ExclamationCircleOutlined />,
					content: (
						<div>
							<div className="r-fw-600 r-mb-6">由于订单状态发生变化，以下订单审核未完成：</div>
							{
								failOrders.map((item) => {
									return (<div key={ item.operationId }>{`${item.operationId}:${item.errorMessage || ''}`}</div>);
								})
							}
						</div>
					),
					onOk() {
						refreshPage();
					}
				});
			} else {
				message.info('处理完成');
				refreshPage();
			}
		},
		onError: (err) => {
		}
	});

	const openAdvancedSettingModal = () => {
		setAdvancedSettingModalVisible(true);
	};

	const handleBatchOpt = async(type: string) => {
		if (!selectedRows.length) {
			message.error('请选择售后单后操作');
			return;
		}
		// 异常处理订单
		let hasAbnormal = false;
		// 待审核订单
		let hasWaitCheck = false;
		// 待确认订单
		let hasWaitComplete = false;
		// 已完成订单
		let hasComplete = false;
		// let hasTianMaoOrder = false;
		selectedRows.forEach((item) => {
			if (item['exceptionType'] === 1) {
				hasAbnormal = true;
			} else if (item['exceptionType'] !== 1 && !item['reviewStatus']) {
				hasWaitCheck = true;
			} else {
				hasComplete = true;
			}
		});
		if (type === 'check' && hasAbnormal) {
			message.error('请先处理异常');
			return;
		}
		if (type === 'check' && (hasComplete || hasWaitComplete)) {
			message.error('您选择的售后订单中包含已审核订单，请确认后再操作');
			return;
		}
		if (type === 'complete' && (hasComplete || hasWaitCheck)) {
			if (hasWaitCheck) message.error('您选择的售后订单中包含待审核的订单，请确认后再操作');
			else if (hasComplete) message.error('您选择的售后订单中包含已完成的订单，请确认后再操作');
			return;
		}
		const selectedRowKeys = selectedRows.map(i => i.id);
		batchDeal({ type, ids: selectedRowKeys });
	};

	const batchDeal = ({ type, ids, text, typeVal = true }: { type: string, ids: number[], text?: string, typeVal?: boolean }) => {
		if (type === 'check') {
			onCheckTrade(ids, typeVal, text);
		} else {
			onConfirmComplete(ids);
		}
	};

	const batchExport = () => {
		setBatchExportModalVisible(true);
	};

	const batchMarkProcessingStatus = () => {
		if (!selectedRows.length) {
			message.error('请选择售后单后操作');
			return;
		}
		setIsBatchMark(true);
		setMarkProcessingRows(selectedRows);
		setMarkProcessingStatusModalVisible(true);
	};

	const singleMarkProcessingStatus = (record) => {
		setIsBatchMark(false);
		setMarkProcessingRows([record]);
		setMarkProcessingStatusModalVisible(true);
	};


	const autoMemo = () => {
		setAutoMemoModalVisible(true);
	};

	const singleCancelCheck = async(row: any, typeVal: boolean) => {
		// 天猫特殊处理
		try {
			batchDeal({ type: 'check', ids: [row.id], typeVal });
		} catch (error) {
			console.log(error);
		}
	};

	// 批量取消关闭
	const onBatchCancelClose = () => {
		if (!selectedRows.length) {
			message.error("请先选择需要操作订单！");
			return;
		}
		if (!checkDistributorIsPush('取消关闭订单')) {
			return;
		}
		closeOrCancelClose(selectedRows.map(i => i.id), 2, true);
	};

	// 批量关闭
	const onBatchClose = () => {

		if (!selectedRows.length) {
			message.error("请先选择需要操作订单！");
			return;
		}
		if (!checkDistributorIsPush('关闭订单')) {
			return;
		}
		closeOrCancelClose(selectedRows.map(i => i.id), 1, true);
	};

	const batchCloseOptBtn = useCallback(() => {
		return (
			<Dropdown.Button
				className="r-mr-8"
				style={ { verticalAlign: "top" } }
				onClick={ onBatchClose }
				overlay={ (
					<Menu onClick={ ({ key }) => onBatchCancelClose(key) }>
						<Menu.Item key="cancelClose">
							取消关闭
						</Menu.Item>
					</Menu>
				) }
			>关闭工单
			</Dropdown.Button>
		);
	}, [selectedRows, isSupplier, isDistributor]);

	const BatchMemoBtns = useCallback((item) => {
		if (isDistributor) {
			return (
				<Dropdown.Button
					onClick={ _.debounce(() => {
						if (operateBtnsDefault.batchMemoBtn === "batchMemoOutline") {
							onBatchModifyMemo();
						} else {
							handleBatchAction(item.text);
						}
					}, 500, {
						leading: true,
						trailing: false
					}) }
					className="r-mr-8"
					overlay={ (
						<Menu>
							<Menu.Item
								key="cancelClose"
								onClick={ _.debounce(() => handleBatchAction(item.text), 500, {
									leading: true,
									trailing: false
								}) }
							>
								<div className="r-flex r-jc-sb r-ai-c">
									<span className="r-mr-8">批量备注</span>
									{
										operateBtnsDefault?.batchMemoBtn === "batchMemo"
											? (
												<Icon
													className="r-c-warning"
													type="guding"
													onClick={ (e) => {
														e.stopPropagation();
													} }
												/>
											)
											: (
												<Icon
													className="r-c-999"
													type="weiguding"
													onClick={ (e) => {
														e.stopPropagation();
														handleSetBtnsDefault("batchMemoBtn", "batchMemo");
													} }
												/>
											)
									}
								</div>
							</Menu.Item>
							<Menu.Item key="cancelClose" onClick={ ({ key }) => onBatchModifyMemo() }>
								<div className="r-flex r-jc-sb r-ai-c">
									<span className="r-mr-8">批量线下备注</span>
									{
										operateBtnsDefault?.batchMemoBtn === "batchMemoOutline"
											? (
												<Icon
													className="r-c-warning"
													type="guding"
													onClick={ (e) => {
														e.stopPropagation();
													} }
												/>
											)
											: (
												<Icon
													className="r-c-999"
													type="weiguding"
													onClick={ (e) => {
														e.stopPropagation();
														handleSetBtnsDefault("batchMemoBtn", "batchMemoOutline");
													} }
												/>
											)
									}
								</div>
							</Menu.Item>
						</Menu>
					) }
					key={ item.text }
				>
					{ operateBtnsDefault?.batchMemoBtn === "batchMemoOutline" ? "批量线下备注" : "批量备注" }
				</Dropdown.Button>
			);
		} else {
			return (
				<Button
					className={ cs(s.afterSaveItemAction) }
					data-point={ item.point }
					onClick={ _.debounce(() => onBatchModifyMemo(), 500, {
						leading: true,
						trailing: false
					}) }
					key={ item.text }
				>
					批量线下备注
				</Button>
			);
		}
	}, [operateBtnsDefault, selectedRows]);

	const CreateAfterSaleBtns = useCallback((item) => {
		return (
			<Dropdown.Button
				className={ cs(s.afterSaveItemAction, item.className) }
				onClick={ _.debounce(() => {
					if (operateBtnsDefault?.createAfterSaleBtn === BtnEnum.新建无主售后单) {
						sendPoint(afterSaveActions.新建无主售后单.point);
						handleBatchAction(BtnEnum.新建无主售后单);
					} else {
						sendPoint(afterSaveActions.新建售后单.point);
						handleBatchAction(BtnEnum.新建售后单);
					}
				}, 500, {
					leading: true,
					trailing: false
				}) }
				overlay={ (
					<Menu>
						<Menu.Item
							data-point={ afterSaveActions.新建售后单.point }
							key="createAfterSale"
							onClick={ () => handleBatchAction("新建售后单") }
						>

							<div className="r-flex r-jc-sb r-ai-c">
								<span className="r-mr-8">新建售后单</span>
								{
									operateBtnsDefault?.createAfterSaleBtn === BtnEnum.新建售后单
										? (
											<Icon
												className="r-c-warning"
												type="guding"
												onClick={ (e) => {
													e.stopPropagation();
												} }
											/>
										)
										: (
											<Icon
												className="r-c-999"
												type="weiguding"
												onClick={ (e) => {
													e.stopPropagation();
													handleSetBtnsDefault("createAfterSaleBtn", BtnEnum.新建售后单);
												} }
											/>
										)
								}
							</div>
						</Menu.Item>
						<Menu.Item
							data-point={ afterSaveActions.新建无主售后单.point }
							key="createOwnerlessAfterSale"
							onClick={ () => handleBatchAction("新建无主售后单") }
						>
							<div className="r-flex r-jc-sb r-ai-c">
								<span className="r-mr-8">新建无主售后单</span>
								{
									operateBtnsDefault?.createAfterSaleBtn === BtnEnum.新建无主售后单
										? (
											<Icon
												className="r-c-warning"
												type="guding"
												onClick={ (e) => {
													e.stopPropagation();
												} }
											/>
										)
										: (
											<Icon
												className="r-c-999"
												type="weiguding"
												onClick={ (e) => {
													e.stopPropagation();
													handleSetBtnsDefault("createAfterSaleBtn", BtnEnum.新建无主售后单);
												} }
											/>
										)
								}
							</div>
						</Menu.Item>
					</Menu>
				) }
			>
				{ operateBtnsDefault?.createAfterSaleBtn === BtnEnum.新建无主售后单 ? "新建无主售后单" : "新建售后单"}
			</Dropdown.Button>
		);
	}, [operateBtnsDefault]);

	// 确认
	const onBatchSupplierConfirm = () => {
		if (!selectedRows.length) {
			message.error("请先选择需要操作订单！");
			return;
		}

		NiceModal.show(BatchSupplierConfirmModal, { selectedRows, type: "确认", okCb: () => refreshPage() });
	};
	// 取消确认
	const onBatchCancelSupplierConfirm = (key: string) => {
		if (!selectedRows.length) {
			message.error("请先选择需要操作订单！");
			return;
		}

		NiceModal.show(BatchSupplierConfirmModal, { selectedRows, type: "取消确认", okCb: () => refreshPage() });
	};

	const getDistributorBtn = useCallback(() => {
		return (
			<Dropdown.Button
				className="r-mr-8"
				overlay={ (
					<Menu>
						<Menu.Item
							key="BatchRecallSupplierAsync"
						>
							<BatchRecallSupplierAsync selectedRows={ selectedRows } okCb={ () => refreshPage() } />
						</Menu.Item>
					</Menu>
				) }
			>
				<BatchPushSupplierAsync selectedRows={ selectedRows } okCb={ () => refreshPage() } />
			</Dropdown.Button>
		);
	}, [selectedRows, isSupplier, isDistributor]);

	const getSupplierBtn = useCallback(() => (
		<Dropdown.Button
			className="r-mr-8"
			onClick={ onBatchSupplierConfirm }
			overlay={ (
				<Menu onClick={ ({ key }) => onBatchCancelSupplierConfirm(key) }>
					<Menu.Item key="cancelConfirm">
						取消确认
					</Menu.Item>
				</Menu>
			) }
		>确认
		</Dropdown.Button>
	), [selectedRows, isSupplier, isDistributor]);

	const expandNode = useMemo(() => {
		let arr = afterSaveTopAllItem;
		if (activeTabItem) {
			const tabRouter = activeTabItem.split('_');
			arr = afterSaveTopItems[tabRouter[0]][tabRouter[1]]?.actions; // 找出当前快捷tab下显示的功能按钮

			// 已发货仅退款
			if (activeTabItem == 'SEND_ONLY_REFUND') {
				if (advanceSettingConfig?.[AftersaleGlobalConfig.已发货仅退款售后单同步至系统后自动更新公工单类型为拒收退货] == '1') {
					// 已发货仅退款，无库存版本不展示退货入库,库存版确认收货、退货入库都展示
					if (isShowZeroStockVersion) {
						arr = arr.filter(item => item !== '退货入库');
					}
				} else {
					arr = arr.filter(item => !['确认收货', '退货入库'].includes(item));
				}
			}
		}
		// 根据角色过滤
		if (isSupplier) {
			arr = arr.filter(item => {
				return !["同意仅退款", "同意退货", "同意退货退款", "拒绝仅退款", "拒绝退货", "拒绝退货退款", "新建售后单", "新建无主售后单", "绑定无主件", "同步售后单", "设置自动备注", "换货补发订单创建"].includes(item);
			});
		}
		// console.log('%c [ 批量操作按钮 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', arr);

		return arr.map(i => ({
			...afterSaveActions[i],
			text: i
		})).map(item => {

			const type = item?.typeFilterTabs == 'ALL' || item?.typeFilterTabs?.includes(activeTabItem) ? 'primary' : 'default';
			if (item.text === "批量备注") {
				return BatchMemoBtns(item);
			}
			if (item.text === "提交给供应商") {
				return getDistributorBtn();
			}
			if (item.text === "新建售后单") {
				return CreateAfterSaleBtns(item);
			}
			if (item.text === '关闭工单') {
				return batchCloseOptBtn();
			}
			if (item.text === "同意退货退款") {
				return (
					<Dropdown.Button
						type="primary"
						className={ cs(s.afterSaveItemAction, item.className) }
						data-point={ item.point }
						onClick={ _.debounce(() => handleBatchAction(item.text), 500, {
							leading: true,
							trailing: false
						}) }
						overlay={ (
							<Menu>
								<Menu.Item
									key="unpackRegister"
									onClick={ () => handleBatchAction("拆包登记") }
								>
									拆包登记
								</Menu.Item>
							</Menu>
						) }
					>
						同意退货退款
					</Dropdown.Button>
				);
			}
			if (item.text === '换货补发订单创建') {
				return (
					<Tooltip title="因平台限制，拼多多补寄售后单暂不支持自动上传快递单号；如【售后高级设置】中已开启发货后自动上传配置，拼多多补发订单发货后，请手动复制快递单号至店铺后台填写">
						<Button
							className={ cs(s.afterSaveItemAction, item.className) }
							data-point={ item.point }
							onClick={ _.debounce(() => handleBatchAction(item.text), 500, {
								leading: true,
								trailing: false
							}) }
							type={ type }
							key={ item.text }
						>
							{item.text}
						</Button>
					</Tooltip>
				);
			}
			return (
				<Button
					className={ cs(s.afterSaveItemAction, item.className) }
					data-point={ item.point }
					onClick={ _.debounce(() => handleBatchAction(item.text), 500, {
						leading: true,
						trailing: false
					}) }
					type={ type }
					key={ item.text }
				>
					{item.text}
				</Button>
			);
		});
	}, [activeTabItem, selectedRows, isSupplier, isDistributor, isShowZeroStockVersion, advanceSettingConfig, formOptionDefault, operateBtnsDefault]);

	// 标记工单状态
	const markProcessingStatus = (handleWay) => {
		const params = {
			ids: markProcessingRows.map(i => i.id),
			handleWay,
		};
		BatchUpdateRefundSystemStatusApi(params).then((result) => {
			const res = batchHandleRes(result);
			if (isBatchMark) {
				// sendPoint(handleWay == 1 ? Pointer["售后订单_批量操作_关闭订单_点击"] : Pointer["售后订单_批量操作_取消关闭_点击"]);
				// setBatchHandleResTitle(handleWay == 1 ? "批量关闭" : "批量取消关闭");
				setBatchHandleResModalVisible(true);
				setMarkProcessingStatusModalVisible(false);
				setBatchOptRes(res);
			} else if (res?.successNum) {
				// message.success(handleWay == 1 ? "售后单已关闭" : "售后单已取消关闭");
				refreshPage();
			} else {
				const errorMsg = res?.batchResults[0]?.errorMessage;
				errorMsg && message.error(errorMsg);
			}
		}).catch().finally(() => {
			setMarkProcessingStatusModalVisible(false);
		});
	};

	const markProcessingStatusModalOnOk = (value) => {
		markProcessingStatus(value);
	};

	const markProcessingStatusModalOnCancel = () => {
		setMarkProcessingStatusModalVisible(false);
		setIsBatchMark(true);
		setMarkProcessingRows([]);
	};

	// 关闭/批量关闭/取消关闭/批量取消关闭
	const closeOrCancelClose = (idList = [], handleWay: 1 | 2, /** 1-关闭2-取消关闭 */ isBatch?: boolean) => {
		const params = {
			ids: idList,
			handleWay,
		};
		BatchCloseRefundApi(params).then((result) => {
			const res = batchHandleRes(result);
			if (isBatch) {
				sendPoint(handleWay == 1 ? Pointer["售后订单_批量操作_关闭订单_点击"] : Pointer["售后订单_批量操作_取消关闭_点击"]);
				setBatchHandleResTitle(handleWay == 1 ? "批量关闭" : "批量取消关闭");
				setBatchHandleResModalVisible(true);
				setBatchOptRes(res);
			} else if (res?.successNum) {
				message.success(handleWay == 1 ? "售后单已关闭" : "售后单已取消关闭");
				refreshPage();
			} else {
				const errorMsg = res?.batchResults[0]?.errorMessage;
				errorMsg && message.error(errorMsg);
			}
		}, () => {
			if (isBatch) {
				message.error(handleWay == 1 ? "批量关闭失败" : "批量取消关闭失败");
			} else {
				message.error(handleWay == 1 ? "关闭失败" : "取消关闭失败");
			}
		}).catch();
	};

	// 列表页顶部额外的操作内容
	const expandContext = (
		<div
			style={ { width: "100%", padding: "12px 0" } }
			className="r-flex r-ai-c r-jc-sb"
		>
			<div>
				{expandNode}
				{/* {batchCloseOptBtn} */}
				{/* 分销商角色：提交给供应商、撤回代发售后单 */}
				{/* {isDistributor && getDistributorBtn} */}
				{/* 供应商角色：确认、取消确认 */}
				{isSupplier && getSupplierBtn()}
			</div>

			<IconHandleRender
				onClick={ openAdvancedSettingModal }
				type="icon-gaojishezhi"
				hoverType="icon-gaojishezhi_fill"
				text="高级设置"
			/>
		</div>
	);

	const initFormData = () => {
		const searchParams = form.getFieldsValue();
		form.resetFields(["platformInfo"]);
		const initValue = {};
		Object.keys({ ...searchParams, ...formData }).forEach(item => {
			initValue[item] = undefined;
		});
		Object.keys(searchInitValue).forEach(item => {
			initValue[item] = formData[item];
		});
		initValue["platformInfo"] = {
			plats: [],
			plat_sellerIds: []
		};
		return initValue;
	};

	const onReset = () => {
		// form.resetFields();

		presetRef?.current?.reset(); // 取消预设查询条件查询的选中

		setActiveTabItem(""); // 清除快捷筛选

		setTimeout(() => {
			let initValue = initFormData();
			initValue = {
				...initValue,
				refundSystemStatusList: getRefundSystemStatusInitialValue(),
				...formOptionDefault
			};
			form.setFieldsValue({ ...initValue, rangeTime: initialRangeTime });
			setFormData({ ...initValue, rangeTime: initialRangeTime });
		}, 0);
	};

	// 查询列表数据
	const getProductListApi = async(value: SearchTableProps, searchType) => {
		// 这里带上所有的条件= 表单 + 未回显的预设
		const ruleContent = presetRef?.current?.getCurRuleContent || {};
		console.log('%c [ ruleContent ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', ruleContent);

		console.log("getProductListApi -> 查询数据", value, formData);
		value = {
			...ruleContent,
			...value,
			...formData,
		};
		// 校验数据
		if (!verifySearchParams(value)) {
			return;
		}
		// if ((value.minRefundFee && !value.maxRefundFee) || (!value.minRefundFee && value.maxRefundFee)) {
		// 	message.error('请输入完整的申请售后金额区间');
		// 	return;
		// }
		const { rangeTime = initialRangeTime, timeType = "create", pageSize = 10, pageNo = 1, reviewStatus, platformInfo, timeout = {} } = value;
		let params: SelectRefundListWithPageRequest;
		params = {
			queryBySort: sortValue,
			pageSize,
			pageNo,
			multiShopS: null,
			scmSelectRefundSource: isDistributor ? 0 : 1, // 售后列表查询来源,0-正常售后单列表1-分销商的售后单列表
		};
		// 处理即将超时快捷选项的值
		if (timeout.residueAfterSaleTimeSearchEnum) {
			params.residueAfterSaleTimeSearchEnum = timeout.residueAfterSaleTimeSearchEnum;
			params.timeOutType = timeout.timeOutType;
		}
		// 需要特殊处理的搜索项
		const excludeFields = ['rangeTime', 'timeType', "refundOrPtTidList", "logisticsNoList", 'reviewStatus', 'flagValue', 'platformInfo', 'timeout', 'sellerMemo', 'sellerFlag', 'logisticsStatusEnum', 'supplierIncludingList', 'marketIncludingList', 'stallIncludingList'];
		const numberFields = ['refundStatus'];
		const booleanFields = [OfflineMemoEnum.是否有线下备注];
		const splitParamsList = ['refundOrPtTidList', 'logisticsNoList', 'supplierIncludingList', 'marketIncludingList', 'stallIncludingList']; // 需要把字符串转成数组的key
		for (const key in value) {
			if (!isUndefined(value[key]) && value[key] !== 'ALL' && !excludeFields.includes(key)) {
				if (!isUndefined(value[key])) {
					params[key] = value[key];
					// 状态需要转换为number
					if (numberFields.includes(key)) {
						params[key] = Number(value[key]);
					}
					if (booleanFields.includes(key)) {
						params[key] = params[key] == 1;
					}
				}

			}
			if (key === 'itemDealStatus') {
				if (value[key]) {
					params[key] = [value[key]];
				} else {
					params[key] = [];
				}
			}
		}
		// 留言备注
		let flagValue;
		let flagSelValue;
		let sellerFlag;
		if (value.flagValue?.includes('_')) {
			flagSelValue = value.flagValue;
		} else {
			flagValue = value.flagValue;
		}
		if (value.sellerFlag?.length > 1) {
			let curFlag: any[] = [];
			flagGroup.forEach(item => {
				if (value.sellerFlag.includes(item.toSellerFlag)) {
					curFlag.push(item.value);
				}
			});
			flagSelValue = curFlag.toString() + '_1';
		} else {
			sellerFlag = value?.sellerFlag?.toString();
		}

		flagValue = flagValue === '-1' ? '10' : flagValue;
		params['flagValue'] = flagValue;
		params['flagSelValue'] = flagSelValue;

		params['sellerMemo'] = value.sellerMemo;
		params['sellerFlag'] = sellerFlag;

		// 校验留言和备注
		if (isBeyondInput(params?.sellerMemo, 50)) {
			message.error('单次查询最多筛选50个备注，请重新输入');
			return false;
		}
		if (params.sellerMemo && params.sellerMemo.length > 1000) {
			message.error('备注最多输入1000个字数，请重新输入');
			return false;
		}

		// 申请时间/修改时间
		if (rangeTime) {
			const [startTime, endTime] = rangeTime;
			if (!startTime && !endTime) {
				message.error("查询时间不能为空");
				return;
			}
			if (!startTime) {
				message.error("开始时间不能为空");
				return;
			}
			if (!endTime) {
				message.error("结束时间不能为空");
				return;
			}
			params[`${timeType}TimeStart`] = rangeTime[0].format('YYYY-MM-DD HH:mm:ss');
			params[`${timeType}TimeEnd`] = rangeTime[1].format('YYYY-MM-DD HH:mm:ss');
		}
		// 没有平台店铺就没有这些参数
		if (platformInfo) {
			const { plats, plat_sellerIds } = platformInfo || {};
			let multiShops = await getMultiShopsWithFilter({ plats, plat_sellerIds, hidePlatforms: [PLAT_KTT, PLAT_DW, PLAT_YZ] });
			multiShops && (params['multiShopS'] = multiShops);

			// 没有平台店铺
			if (!plats?.length && !plat_sellerIds?.length) {
				params['shopIsActualSelect'] = 0; // 页面是否实际勾选店铺查询 0否 1是
			} else {
				params['shopIsActualSelect'] = 1;
			}
		}


		// 包含和不包含
		handleGoodsInfoSearchParams(value, params);

		// 审核状态
		if (!isUndefined(reviewStatus)) {
			if (reviewStatus !== 'ALL') {
				params['reviewStatus'] = (reviewStatus === 'true');
			}
		}

		// // * 物流状态参数处理
		// params['sidStatusEnum'] = value['sidStatusEnum']?.join(',');
		params['logisticsStatusEnum'] = value['logisticsStatusEnum']?.join(',');

		// 处理需要split的参数
		splitParamsList.forEach(key => {
			if (value[key]?.trim()) {
				params[key] = value[key]?.trim()?.replaceAll("，", ",")?.split(",")?.filter(Boolean);
			} else {
				delete params[key];
			}
		});

		// 如果用户输入了订单编号，系统单号、售后单号，发货单号，退货单号，需要忽略售后工单状态
		if (value.refundOrPtTidList || value.logisticsNoList) {
			params.refundSystemStatusList = null;
		}
		needCloseExpand && onCloseAllExpandTable();
		if (!needCloseExpand) {
			needCloseExpand = true;
		}

		// 申请售后数量 范围值修改
		if (value?.applyRefundFeeType == 'applyRefundNum') {
			params.minRefundItemNum = value.minRefundFee;
			params.maxRefundItemNum = value.maxRefundFee;
			delete params.minRefundFee;
			delete params.maxRefundFee;
		}

		console.log('%c [ getProductListApi -> params ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
		setSearchParams({ ...params });
		setPageLoading(true);
		return SelectRefundListWithPageApi({ ...params });
	};

	// 快捷查询条件点击
	const quickFilterSelectChange = async(key, queryParams) => {
		presetRef?.current?.reset(); // 取消预设查询条件查询的选中

		console.log(key, queryParams, formData, searchParams);
		const initValue = initFormData();
		setTimeout(() => {
			const formValue = key != "" ? { ...initValue, ...queryParams, timeType: "create", rangeTime: [dayjs().subtract(30, 'd').startOf("day"), dayjs().endOf("day")] } : { ...initValue, rangeTime: initialRangeTime, refundSystemStatusList: getRefundSystemStatusInitialValue() };
			form.setFieldsValue(formValue);
			setFormData(formValue);
			ref?.current?.submit();
			setActiveTabItem(key);
		});
	};

	const onFieldsChange = (changedValues, allValues) => {
		console.log("onFieldsChange");
		// 如果留言备注更改了，清空备注或留言的输入框内容
		if (OfflineMemoEnum.是否有线下备注 in changedValues && allValues[OfflineMemoEnum.是否有线下备注] == 0) {
			form.setFieldsValue({ [OfflineMemoEnum.线下备注内容]: "" });
			setFormData((prev) => {
				return {
					...prev,
					[OfflineMemoEnum.线下备注内容]: ""
				};
			});
		}
		if ('flagValue' in changedValues) {
			form.setFieldsValue({
				sellerMemo: ''
			});
			setFormData((prev) => {
				return {
					...prev,
					sellerMemo: ''
				};
			});
		}
		setActiveTabItem('');
		handleFormItemList();
		// if ('refundTabShowType' in changedValues) {
		// 	ref?.current?.submit();
		// }
		// setActiveTabItem(allValues.refundTabShowType);

		setFormData((prev) => {
			const data = {};
			Object.keys({ ...formData, ...allValues }).forEach(item => {
				if (item in changedValues) {
					data[item] = changedValues[item];
				} else if (Object.keys(searchInitValue).includes(item) && !allValues[item]) {
					data[item] = prev[item] || formOptionDefault[item];
				} else {
					data[item] = allValues[item] || formData[item];
				}
			});
			form.setFieldsValue({ ...data });
			return {
				...data,
			};
		});
		console.log('%c [ allValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues, allValues);
	};

	// 同意仅退款、同意退货、同意退货退款、确认收货、退货入库
	const batchAgreeRefundHandle = (operateType) => {
		if (!selectedRows.length) {
			message.error('请选择售后单后操作');
			return;
		}
		events.emit('batchReturnMoney', { list: selectedRows, operateType, afterOk: refreshPage });
	};

	// 拆包登记
	const batchUnpackRegistorHandle = (operateType) => {
		console.log(selectedRows);
		if (!selectedRows.length) {
			message.error('请选择售后单后操作');
			return;
		}

		setUnpackRegisterModalVisible(true);
	};

	// 过滤后只有一个数据执行单个拒绝退款、退货、退货退款
	const singleRefuseHandle = (item, operateType) => {
		setCurActionRowData({ ...item, isRefundGoods: operateType == afterSaleOperateType.拒绝退货, operateType });
		setRefuseRefundModalVisible(true);
	};

	// 批量拒绝仅退款、拒绝退货、拒绝退货退款
	const batchRefuseHandle = (operateType) => {
		if (!selectedRows.length) {
			message.error('请选择售后单后操作');
			return;
		}

		events.emit('batchRefuseMoney', { list: selectedRows, operateType, afterOk: refreshPage, singleRefuse: singleRefuseHandle });


		// if (item['exceptionType'] !== 1 && !item['reviewStatus']) {
		// 	return popErrorModal('您选择的售后订单需先审核通过');
		// }
	};

	// 绑定无主件
	const bindTrade = () => {
		// 必须是未绑定过的无主售后单才支持绑定
		const validRefundList = selectedRows.filter(i => i.isNoTradeMess);
		if (selectedRows.length < 1 || validRefundList.length < 1) {
			message.warning('请选择一个无主售后单再进行绑定');
			return;
		}
		setBindOrderModalVisible(true);
	};

	const handleBatchAction = (type: afterSaveTopItemProps) => {
		// 不支持处理“已推送到供应商”的代发售后单
		if (!checkDistributorIsPush(type)) {
			return;
		}
		if (type === BtnEnum.拆包登记 && selectedRows.some(item => item.platform !== PLAT_JD)) {
			message.warning("仅京东售后单支持拆包登记");
			return;
		}

		// eslint-disable-next-line default-case
		switch (type) {
			case BtnEnum.同意仅退款:
				batchAgreeRefundHandle(afterSaleOperateType.同意仅退款);
				break;
			case BtnEnum.同意退货:
				batchAgreeRefundHandle(afterSaleOperateType.同意退货);
				break;
			case BtnEnum.同意退货退款:
				batchAgreeRefundHandle(afterSaleOperateType.同意退货退款);
				break;
			case BtnEnum.拆包登记:
				batchUnpackRegistorHandle(afterSaleOperateType.拆包登记);
				break;
			case BtnEnum.拒绝仅退款:
				batchRefuseHandle(afterSaleOperateType.拒绝仅退款);
				break;
			case BtnEnum.拒绝退货:
				batchRefuseHandle(afterSaleOperateType.拒绝退货);
				break;
			case BtnEnum.拒绝退货退款:
				batchRefuseHandle(afterSaleOperateType.拒绝退货退款);
				break;
			case BtnEnum.确认收货:
				batchAgreeRefundHandle(afterSaleOperateType.确认收货);
				break;
			case BtnEnum.退货入库:
				batchAgreeRefundHandle(afterSaleOperateType.退货入库);
				break;
			case BtnEnum.换货补发订单创建:
				batchCreateExchangeTrade();
				break;
			case BtnEnum.批量备注:
				batchModifyMemo();
				break;
			case BtnEnum.新建售后单:
				setCreateTradeModalVisible(true);
				break;
			case BtnEnum.新建无主售后单:
				setCreateNoInfoOrderVisible(true);
				break;
			case BtnEnum.绑定无主件:
				bindTrade();
				break;
			case BtnEnum.同步售后单:
				setSyncModalVisible(true);
				break;
			case BtnEnum.审核:
				handleBatchOpt('check');
				break;
			case BtnEnum.批量导出:
				batchExport();
				break;
			case BtnEnum.设置自动备注:
				autoMemo();
				break;
			case BtnEnum.标记工单状态:
				batchMarkProcessingStatus();
				break;
			// case BtnEnum.确认完成:
			// 	handleBatchOpt('complete');
			// 	break;
		}
	};

	const onClickCloseRefund = (row) => {
		sendPoint(Pointer["售后_售后订单_更多_关闭售后订单"]);
		if (row.refundSystemStatus != refundSystemStatusEnum.待处理) {
			Modal.confirm({
				centered: true,
				title: '系统提示',
				okText: '我知道了',
				icon: <ExclamationCircleOutlined />,
				content: (
					<div><div className={ cs('r-c-error') }>售后工单状态为：{refundSystemStatusEnum[row.refundSystemStatus]}。不可关闭</div></div>
				),
				cancelButtonProps: { hidden: true }
			});
			return;
		}
		Modal.confirm({
			centered: true,
			title: '系统提示',
			okText: '确认',
			cancelText: '取消',
			icon: <ExclamationCircleOutlined />,
			content: (
				<div><div>确定要关闭这条售后单吗？</div><div className={ cs('r-c-error') }>注意:仅【待处理】工单且售后商品未处理时可以关闭，关闭后不展示售后工单</div></div>
			),
			onOk() {
				closeOrCancelClose([row.id], 1);
			},
		});
	};

	// 获取不同状态下操作按钮
	const getMoreAction = (row: any) => {
		const detailBtn = (
			<div
				className={ cs('r-pointer', s.table_operate_item) }
				data-point={ Pointer['售后_售后订单_更多_更多_查看订单'] }
				onClick={ () => {
					setEditModalVisible(true);
					setCurActionRowData(row);
					setEditModalType('show');
				} }
			>查看详情
			</div>
		);
		const editBtn = (
			<div
				className={ cs('r-pointer', s.table_operate_item) }
				data-point={ Pointer['售后_售后订单_更多_更多_编辑'] }
				onClick={ () => {
					if (!checkDistributorIsPush('编辑', row)) {
						return;
					}
					setEditModalVisible(true);
					setCurActionRowData(row);
					setEditModalType('edit');
				} }
			>编辑
			</div>
		);
		const checkBtn = (
			<div
				className={ cs('r-pointer', s.table_operate_item) }
				onClick={ () => {
					if (!checkDistributorIsPush('取消审核', row)) {
						return;
					}
					singleCancelCheck(row, false);
					// onCheckTrade(row['id'], false);
				} }
			>取消审核
			</div>
		);
		const refuseRefundBtn = (text: string) => {
			let isRefundGoods = ['拒绝退货', '拒绝换货'].includes(text);
			return (
				<div
					className={ cs('r-pointer', s.table_operate_item) }
					onClick={ () => {
						if (text === "拒绝退款" && row.platform == PLAT_JD) {
							message.warning("京东售后单暂不支持拒绝退款，请前往商家后台操作协商关闭");
							return;
						}
						setCurActionRowData({ ...row, isRefundGoods, text });
						onRefuseRefund(row, text);
					} }
				>{text}
				</div>
			);
		};

		const closeBtn = (
			<div
				className={ cs('r-pointer', s.table_operate_item) }
				onClick={ () => {
					if (!checkDistributorIsPush('关闭售后单', row)) {
						return;
					}
					onClickCloseRefund(row);
				} }
			>关闭售后单
			</div>
		);

		const markProcessingBtn = (
			<div
				className={ cs('r-pointer', s.table_operate_item) }
				onClick={ () => {
					if (!checkDistributorIsPush('标记工单状态', row)) {
						return;
					}
					singleMarkProcessingStatus(row);
				} }
			>标记工单状态
			</div>
		);

		const cancelCloseBtn = (
			<div
				className={ cs('r-pointer', s.table_operate_item) }
				onClick={ () => {
					if (!checkDistributorIsPush('取消关闭售后单', row)) {
						return;
					}
					sendPoint(Pointer["售后_售后订单_更多_取消关闭"]);
					closeOrCancelClose([row.id], 2);
				} }
			>取消关闭
			</div>
		);

		const showOptLogBtn = (
			<div
				className={ cs('r-pointer', s.table_operate_item) }
				onClick={ () => {
					setOptLogModalVisible(true);
					setOptLogRefundId(row.refundId);
				} }
			>售后操作日志
			</div>
		);
		// 退款未完成
		// 根据审核状态、库存版本、异常处理状态、退货退款状态展示 取消审核 或 编辑 按钮
		// console.log('rowrow', row);

		return (
			<div className={ cs(s.table_operate_box) }>
				{detailBtn}
				{row['reviewStatus'] ? checkBtn : ((row['exceptionType'] !== 2 && getListRefundManageStatus(row)) ? editBtn : null)}
				{checkRefundAction(row).refuseMoney && row['refundStatus'] !== 2 ? refuseRefundBtn('拒绝退款') : null}
				{checkRefundAction(row).refuseGoods ? refuseRefundBtn('拒绝退货') : null}
				{checkRefundAction(row).refuseReplacing ? refuseRefundBtn('拒绝换货') : null}
				{checkRefundAction(row).refuseReplacingAgain ? refuseRefundBtn('售后退货拒绝') : null}
				{checkRefundAction(row).refuseReissue ? refuseRefundBtn('拒绝补寄') : null}
				{/* {row['reviewStatus'] ? checkBtn : ((warehouseStatus && row['exceptionType'] !== 2) ? editBtn : null)} */}
				{row.refundSystemStatus == refundSystemStatusEnum.已关闭 ? cancelCloseBtn : closeBtn}
				{markProcessingBtn}
				{showOptLogBtn}
			</div>
		);
		// }
	};

	const getStatusTextColor = (status) => {
		switch (status) {
			case REFUND_SYSTEM_STATUS.处理中:
				return "#fd8106";
			case REFUND_SYSTEM_STATUS.已完成:
				return "#f00";
			case REFUND_SYSTEM_STATUS.待处理:
				return "#67c23a";
			default:
				return " ";
		}
	};

	// 异常状态下的锁标志
	const getExceptionLock = (exceptionType: number) => {
		if (exceptionType !== 1) {
			return null;
		}
		return (
			<Tooltip placement="bottom" title="售后订单对应的销售订单商品没有匹配本地货品，无法匹配售后货品">
				<div style={ { gap: '5px 10px', marginLeft: '4px' } } className="r-fw-w batch_tbtlt_tradeLabel ">
					<span className="r-trade-no-relate" style={ { width: 'auto', whiteSpace: "nowrap" } }>退货商品未绑定</span>
				</div>
			</Tooltip>
		);
	};

	// 确认完成api
	const { run: onConfirmFinishTrade } = useRequest(ConfirmFinishApi, {
		manual: true,
		onSuccess: (res) => {
			message.info("处理完成");
			refreshPage();
		}
	});

	// 操作完成 刷新页面
	const refreshPage = () => {
		ref?.current?.refresh(() => {
			console.log('操作完成 刷新页面');
			needCloseExpand = false;
		});
	};
	// 点击确认完成
	const onConfirmComplete = (id: any) => {
		Modal.confirm({
			centered: true,
			title: '系统提示',
			okText: '确认',
			cancelText: '取消',
			icon: <ExclamationCircleOutlined />,
			content: (
				<div><div>确定要确认完成该售后单吗？</div><div className={ cs('r-c-error') }>注意：完成后此售后将不能再做任何操作</div></div>
			),
			onOk() {
				let params: IgnoreMatchSystemItemExceptionRefundRequest;

				params = {
					ids: [...id]
				};
				onConfirmFinishTrade(params);
			},
			onCancel() {
				console.log('Cancel');
			},
		});
	};
	// 审核操作
	const onCheckTrade = (ids: number[], opType: boolean = true, msg: string = "") => {
		let params: ReviewRefundTradeV2Request = {
			ids,
			result: opType,
			message: msg || undefined
		};
		onReviewRefundTradeApi(params);
	};

	const openModifyMemo = (list: IBatchModifyMemoDataProps[]) => {
		const packList = list.map((item: IBatchModifyMemoDataProps) => ({
			trades: [{
				sellerMemo: item.sellerMemo,
				sellerMemoFlag: item.sellerFlag,
				tid: item.tid,
				ptTid: item?.ptTid || '',
			}],
			platform: item.platform,
			source: item.source,
			togetherId: item.tid,
			sellerId: item.sellerId,
			refundStatus: item.refundStatus || "",
			afterSalesFlag: true
		}));
		setIsShowBatchModifyMemoModal(true);
		setModifyMemoPackage(packList);
	};

	const batchModifyMemo = () => {
		if (!selectedRows.length) {
			message.error('请选择售后单后操作');
			return;
		}

		const hasAbnormal = selectedRows.some((item: IBatchModifyMemoDataProps) => item.exceptionType === 1);

		if (hasAbnormal) {
			message.error('请先处理异常');
			return;
		}

		const hasNoTradeMess = selectedRows.some((item: IBatchModifyMemoDataProps) => item.isNoTradeMess);

		if (hasNoTradeMess) {
			message.error('无主件不可进行备注');
			return;
		}
		openModifyMemo(selectedRows);
	};

	// 换货补发订单创建
	const batchCreateExchangeTrade = async() => {
		if (!selectedRows.length) {
			message.error('请选择售后单后操作');
			return;
		}

		if (modalProgress.asyncKey) {
			message.error('换货补发订单创建中，请稍候再试！');
			return;
		}

		let list = selectedRows.map((trade: any) => +trade.id);

		let params: BatchCreateExchangeTradeRequest = {
			isUpdate: false, // 是否修改收件人信息
			refundInfoIdList: list, // refundInfoId集合
		};
		let res = await BatchCreateExchangeTradeApi(params);

		// 请求进度
		if (res.asyncKey) {
			modalProgress.asyncKey = res.asyncKey;
			modalProgress.progressVisibility = true;

			startCreateExchangeTradeProgress({
				key: modalProgress.asyncKey
			});
		} else {
			message.error('换货补发订单创建失败');
		}

	};

	// 关闭换货补发订单创建
	const onModalProgressCancel = () => {
		// 进度后台还在请求，仅关闭弹框
		modalProgress.progressVisibility = false;
	};

	const resetModalProgress = () => {
		modalProgress.progressVisibility = false;
		modalProgress.progress = 0;
		modalProgress.asyncKey = '';
		modalProgress.progressResultVisibility = false;
		modalProgress.data = {};
	};

	// 复制失败的售后单号
	const batchCopyProgressResult = () => {
		let list = modalProgress.data?.errorResultCollectList || [];
		if (!list.length) {
			message.error('没有可复制内容');
			return;
		}
		const copyIds = list.map(row => row.operationId).join(",");
		copyToPaste(copyIds);
	};

	// 请求换货补发订单创建进度
	const { run: startCreateExchangeTradeProgress, cancel: stopCreateExchangeTradeProgress } = useRequest(CreateExchangeTradeAsyncGetApi, {
		manual: true,
		pollingInterval: 1000,
		onSuccess: (res => {
			const { errorResultCollectList, process, processStatus, errorMessage, successNum, failedNum, asyncKey } = res;
			if (process == '100' || processStatus == 1) { // 执行状态0-进行中1-成功2-失败
				stopCreateExchangeTradeProgress();
				modalProgress.progress = 100;

				setTimeout(() => {
					modalProgress.progressVisibility = false;
					modalProgress.progress = 0;
					modalProgress.asyncKey = '';

					// 显示创建结果失败弹框
					modalProgress.data = res;
					modalProgress.progressResultVisibility = true;

					// 刷新列表
					refreshPage();
				}, 100);

			} else if (process && processStatus == 0) {
				modalProgress.progress = parseInt(process || '0', 10);
			}
		}),
		onError: (err) => {
			stopCreateExchangeTradeProgress();
			resetModalProgress();
		}
	});

	const modifyMemo = (item: IBatchModifyMemoDataProps, index: number) => {
		console.log(item, 'modifyMemo');
		if (item.isNoTradeMess) {
			message.info('无主件不可进行备注');
			return;
		}
		if (item.exceptionType !== 1) {
			sendPoint(Pointer.售后_订单_更新订单留言);
			openModifyMemo([item]);
		} else {
			Modal.warning({
				title: '系统提示',
				content: '该订单已被锁定，不能进行该操作，请先处理异常',
			});
		}
	};

	const writeExpressNoOnFinished = (res) => {
		if (res.success) {
			refreshPage();
		}
	};

	const agreeRefundSupply = async(record) => {
		const { platform, id } = record;
		if (platform === PLAT_FXG) {
			const res = await AgreeRefundSupplyApi({ id });
			refreshPage();
		} else {
			const href = getPlatformTradeLink(record.platform, record).refund;
			window.open(href, '_blank');
		}

	};

	const onBatchModifyMemo = () => {
		if (!selectedRows.length) {
			message.error('请选择售后单后操作');
			return;
		}
		if (!checkDistributorIsPush('批量线下备注')) {
			return;
		}
		setOfflineMemoData({ visible: true, list: selectedRows });
	};

	const modifyOfflineMemo = (item) => {
		if (!checkDistributorIsPush('线下备注', item)) {
			return;
		}
		setOfflineMemoData({ visible: true, list: [item] });
	};

	// 编辑线下备注
	const handleOfflineMemoOk = ({ list }) => {
		console.log("handleOfflineMemoOk:", list);
		setOfflineMemoData((prev) => {
			return {
				...prev,
				loading: true
			};
		});
		const newList = list.map((item) => ({
			...item,
			updateType: UpdateTypeEnum.线下备注,
			requestSource: AFTERSALE_REQUEST_SOURCE.售后列表
		}));
		BatchUpdateLocalNoteApi(newList).then((res) => {
			console.log(res);
			setOfflineMemoData({ visible: false, loading: false });
			message.success("编辑成功");
			refreshPage();
		}).catch(() => {
			setOfflineMemoData(prev => ({ ...prev, loading: false }));
		});
	};

	const handleOfflineMemoCancel = () => {
		setOfflineMemoData({ visible: false });
	};

	const GoodsContentSettingOnOk = (list) => {
		console.log(15, list);
		setProductContentList(list);
		// 转化下value
		const paramsVal = list.map(x => PRODUCT_CONTENT_KEY_ENUM[x]);
		const value = paramsVal ? paramsVal.join(',') : '';
		// 调接口存储
		UpdateRefundGlobalConfigApi([{ biz: bizObj.产品内容设置, value }]);
	};

	const BuyNickInfoContentSettingOnOk = (list) => {
		console.log(list, '999999999999');
		setBuyNickProductContentList(list);
		// 转化下value
		const value = list ? list.join(',') : '';
		// 调接口存储
		UpdateRefundGlobalConfigApi([{ biz: bizObj.售后买家昵称信息设置, value }]);
	};

	const renderGoodsContentColHeader = useMemo(() => (
		<div className="r-flex r-ai-c">
			<div>商品信息</div>
			<span className="r-pointer r-ml-4">
				<GoodsContentSetting onOk={ GoodsContentSettingOnOk } productContentList={ productContentList }>
					<SettingOutlined />
				</GoodsContentSetting>
			</span>
		</div>
	), [productContentList]);

	const renderGoodsInfoHeader = useMemo(() => (
		<div className="r-flex r-ai-c">
			<div>买家昵称</div>
			<span className="r-pointer r-ml-4">
				<GoodsContentSetting buyNick onOk={ BuyNickInfoContentSettingOnOk } productContentList={ buyNickProductContentList }>
					<SettingOutlined />
				</GoodsContentSetting>
			</span>
		</div>
	), [buyNickProductContentList]);

	const renderGoodsInfo = (data, record) => {
		const showImg = productContentList.includes(PRODUCT_CONTENT_ENUM.货品图片) || productContentList.includes(PRODUCT_CONTENT_ENUM.商品图片);
		let imgSrc = data.picPath;
		if (productContentList.includes(PRODUCT_CONTENT_ENUM.货品图片)) {
			imgSrc = data.picUrl;
		}
		const getLinkHref = getPlatformDetailLink(record.platform, data.numIid);
		const goodsInfoItems = [];
		const skuInfoItems = [];
		const productSettings = isShowZeroStockVersion ? PRODUCT_SETTING_ZERO_OBJECT : PRODUCT_SETTING_OBJECT;
		// 根据后端返回数据进行数据展示
		productSettings.forEach(i => {
			i.list.forEach(item => {
				const allowShow = productContentList.includes(item.value) && ![PRODUCT_CONTENT_ENUM.货品图片, PRODUCT_CONTENT_ENUM.商品图片].includes(item.value);
				if (!allowShow) return;
				if (i.key == "good") {
					goodsInfoItems.push(data[item.value] ? <div className="r-mr-5" key={ `${data[item.value]}_${item.value}_goods` }>{data[item.value]}</div> : null);
				} else if (i.key == "sku") {
					skuInfoItems.push(data[item.value] ? <span className="r-mr-5" key={ `${data[item.value]}_${item.value}_sku` }>{data[item.value]}</span> : null);
				}
			});
		});

		const imgSize = productContentList.includes(PRODUCT_CONTENT_ENUM.小图) ? '300px'
			: productContentList.includes(PRODUCT_CONTENT_ENUM.中图) ? '500px'
				: productContentList.includes(PRODUCT_CONTENT_ENUM.大图) ? '800px' : '500px';

		return (
			<div className={ s['goods-container'] } key={ data.id }>
				<div hidden={ !showImg }>
					<Popover
						// overlayClassName={ s['popover'] }
						placement="right"
						content={ <Image src={ imgSrc } style={ { width: imgSize, height: imgSize } } /> }
					>
						<a
							// eslint-disable-next-line no-script-url
							href={ getLinkHref || 'javascript:void(0)' }
							target={ getLinkHref ? '_blank' : '_self' }
							rel="noopener noreferrer"
							className="r-flex r-ai-c"
							onClick={ handleStopClick }
						>
							<Image src={ getImageThumbnail({ noScale: false, url: imgSrc, width: 80, height: 80, quality: 100 }) } />
						</a>
					</Popover>

				</div>
				<div>
					{goodsInfoItems.length > 0 ? (
						<div className="r-flex r-fw-w">
							{goodsInfoItems}
						</div>
					) : null}
					{skuInfoItems.length > 0 ? (
						<div>
							{skuInfoItems}
						</div>
					) : null}
					{data.applyRefundNum && (
						<div className="r-flex r-ai-c">
							<RefundItemType data={ data } />
							{
								data.applyRefundNum > 1 ? (
									<div className="r-c-error r-ml-4" style={ { background: '#FFD591', padding: '0 4px', lineHeight: '20px' } }>x {data.applyRefundNum}</div>
								) : (
									<div className="r-c-error r-ml-4">x {data.applyRefundNum}</div>
								)
							}

						</div>
					)}
				</div>

			</div>
		);
	};

	// 梳理退款数据
	const dealReturnData = useCallback((record) => {
		try {
			const { platformItemContentShowList, systemItemContentShowList } = record;
			const platformItemArr = platformItemContentShowList.map(item => {
				const { relateSystemItemList, applyRefundNum } = item;
				const relateSystemItem = (relateSystemItemList && relateSystemItemList[0]) || {};
				const { applyRefundNum: applyRefundNumItem } = relateSystemItem;
				// 对于商品退货数量判断，优先取关联货品的，关联货品没有则取商品本身
				const applyRefundNumValue = applyRefundNumItem || applyRefundNum;
				return { ...item, ...relateSystemItem, applyRefundNum: applyRefundNumValue };
			});
			return [...platformItemArr, ...systemItemContentShowList];

		} catch (error) {
			return [];
		}
	}, []);

	// 根据角色过滤下列配置数据
	const getFilterColumnConfig = (value: any[]) => {
		let newValue = [];
		// 分销商角色: “是否提交供应商”、“供应商确认状态”、“供应商”
		// 供应商角色：“供应商确认状态”、“分销商”
		if (isDistributor) {
			newValue = value.filter(item => {
				return !['distributorUser'].includes(item?.key);
			});
		} else if (isSupplier) {
			newValue = value.filter(item => {
				return !['distributorIsPush', 'supplierUser'].includes(item?.key);
			});
		}
		return newValue;
	};

	const handleStopClick = (event) => {
		event.stopPropagation();
	};

	// 表格头部配置
	let columns: ColumnsType<SelectRefundListWithPageResponse["data"]["list"][number]> = [
		{
			title: '序号',
			// dataIndex: 'index',
			// colFixed: 'left',
			// sortSet: { isfixed: false },
			key: 'index',
			width: 50,
			render: (text, record, index) => {
				const sort = dataSource.findIndex(r => r.rowId === record.rowId);
				return sort + 1;
			}
		},
		{
			title: renderGoodsInfoHeader,
			sortSet: { name: '买家昵称' },
			key: 'buyerNick',
			width: 200,
			minWidth: 200,
			dataIndex: 'buyerNick',
			render: (text, record, index) => {
				let tooltipText = <></>;
				let validTime = false;
				if (record.timeOut) {
					const timeOut = calculateTimeDiff(record.timeOut);
					if (timeOut) {
						validTime = true;
						tooltipText = (
							<TooltipText className={ cs('r-fc-black-45') } title="超时时间">
								<div className="r-flex r-ai-c">
									<span className="r-flex r-ai-c">
										<Icon type="tixing" style={ { fontSize: 16, color: '#FF0000' } } />
									</span>
									<span style={ { color: "#FF0000", marginLeft: 2, lineHeight: '20px' } }>剩余{timeOut.days}天{timeOut.hours}时{timeOut.minutes}分{timeOut.seconds}秒</span>
								</div>
							</TooltipText>
						);
					}
				}
				return (
					<div>
						<div className={ cs('r-fc-black-65', 'r-wb-bw') } onClick={ handleStopClick }>
							<BuyerNickComp ptTid={ record?.ptTid || '' } tid={ record.tid } encryptuid={ record.buyerOpenUid } platform={ record.platform } buyerNick={ record.buyerNick } sellerId={ record?.sellerId } />
						</div>
						{
							buyNickProductContentList.includes(PRODUCT_SETTING_OBJECT_ENUM.售后单申请时间) && (
								<div className="r-flex r-ai-c r-c-black85">
									<span>申请时间：</span>
									<span>{record['refundCreatedTime']}</span>
								</div>
							)
						}
						{
							buyNickProductContentList.includes(PRODUCT_SETTING_OBJECT_ENUM.售后单修改时间) && (
								<div className="r-flex r-ai-c r-c-black85">
									<span>修改时间：</span>
									<span>{record['refundModifiedTime']}</span>
								</div>
							)
						}
						{
							buyNickProductContentList.includes(PRODUCT_SETTING_OBJECT_ENUM.订单付款时间) && (
								<div className="r-flex r-ai-c r-c-black85">
									<span>订单付款：</span>
									<span>{record['payTime'] ? dayjs(record.payTime).format("YYYY-MM-DD HH:mm:ss") : '-'}</span>
								</div>
							)
						}
						{
							validTime && tooltipText
						}
					</div>
				);
			}
		},
		{
			title: '店铺/订单/售后单号',
			key: 'sellerNick',
			width: 220,
			minWidth: 220,
			dataIndex: 'sellerNick',
			className: cs('table-right-border'),
			render: (text, record: any, index) => {
				// 添加判断是否为京喜订单的逻辑
				const hasJingxi = record.refundTagList?.includes('jxTrade');
				return (
					<div>
						<div hidden={ record.isNoTradeMess } className={ cs('r-fc-black-65', 'r-wb-bw', 'r-flex', 'r-ai-c') }>
							<PlatformIcon platform={ record['platform'] } />
							<span>{!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isAfterSaleSourceScm(record) ? '****' : getShopName({ plat: record?.platform, sellerNick: record?.sellerNick || "" }) }</span>
						</div>


						{
							record.isNoTradeMess ? (
								<div className="r-c-error r-mt-4">无主件</div>
							) : (
								<div className="r-flex r-ai-c r-mt-4 r-c-black85">
									<span>订单：</span>

									<a
										className={ cs('r-fc-1890FF') }
										href={ getPlatformTradeLink(record['platform'], record).trade }
										target="_blank"
										rel="noreferrer"
									>{record['ptTid']}
									</a>
									<CopyOutlined
										hidden={ !record['ptTid'] }
										onClick={ (event) => {
											event.stopPropagation();
											copyToPaste(splitFxgTid(record['ptTid']));
										} }
										className={ cs('r-fc-black-65 r-ml-2', 'r-pointer') }
									/>
								</div>
							)
						}

						<div className="r-flex r-ai-c r-mt-4 r-c-black85">
							<span>售后：</span>

							<a
								className={ cs('r-fc-1890FF') }
								href={ getPlatformTradeLink(record['platform'], record).refund }
								target="_blank"
								rel="noreferrer"
							>{record['refundId']}
							</a>
							<CopyOutlined
								hidden={ !record['refundId'] }
								onClick={ (event) => {
									event.stopPropagation();
									copyToPaste(record['refundId']);
								} }
								className={ cs('r-fc-black-65 r-ml-2', 'r-pointer') }
							/>
						</div>

						{
							record.refundTagList?.map(item => {
								if (item == "speedRefund") {
									return <Tag className="r-mt-8 r-bg-white" style={ { color: "#FD8204", borderColor: "#FD8204" } }>平台极速退款</Tag>;
								} else if (item == "sidDifferentTradeMoreRefund") {
									return (
										<Tooltip title="同一个退货物流单号关联不同订单的多笔售后单">
											<Tag className="r-mt-8 r-bg-white" style={ { color: "#F00", borderColor: "#F00" } }>退货物流异单多售后</Tag>
										</Tooltip>
									);
								} else if (item == "sidSameTradeMoreRefund") {
									return (
										<Tooltip title="同一个退货物流单号关联同一订单的多笔售后单">
											<Tag className="r-mt-8 r-bg-white" style={ { color: "#666", borderColor: "#999" } }>退货物流同单多售后</Tag>
										</Tooltip>
									);
								}
								return null;
							})
						}
						{
							hasJingxi && (
								<Tag className="r-mt-8 r-bg-white" style={ { color: "#FD8204", borderColor: "#FD8204" } }>京喜</Tag>
							)
						}

					</div>
				);
			}
		},
		{
			title: renderGoodsContentColHeader,
			sortSet: { name: '商品信息' },
			key: 'refundItemRecordInfos',
			dataIndex: 'refundItemRecordInfos',
			width: 220,
			minWidth: 220,
			render: (text, record, index) => {
				const platformItemArr = dealReturnData(record);
				return (
					<div>
						{
							platformItemArr.map(x => (renderGoodsInfo(x, record)))
						}
					</div>
				);
			}
		},
		{
			title: '退款金额',
			sortSet: { name: '退款金额' },
			key: 'refundAmount',
			dataIndex: 'refundAmount',
			width: 100,
			minWidth: 100,
			render: (text, record, index) => {
				// 0或者没有退款运费则不展示
				const showRefundFreight = [PLAT_KS].includes(record.platform) && Number(record.freight);
				return (
					<div>
						<div className={ cs('r-fc-black-65') }>退款：{text}</div>
						<div hidden={ !showRefundFreight } className={ cs('r-fc-black-65') }>退货运费：{record.freight}</div>
					</div>
				);
			}
		},
		{
			title: '原订单实付金额',
			key: 'payment',
			width: 100,
			minWidth: 80,
			sortSet: { ischecked: false },
			dataIndex: 'payment',
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-65') }>
						<p>{text}元</p>
						<p>{`(含运费：${record.postFee || 0}元)`}</p>
					</div>
				);
			}
		},
		{
			title: '售后类型',
			key: 'afterSaleType',
			width: 70,
			minWidth: 70,
			dataIndex: 'afterSaleType',
			render: (text, record, index) => {
				return (
					<div style={ { color: '#FF0000' } }>{afterSaleTypeText[text]}</div>
				);
			}
		},
		{
			title: '工单类型',
			key: 'refundSystemType',
			width: 70,
			minWidth: 70,
			sortSet: { ischecked: false },
			dataIndex: 'refundSystemType',
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-65') }>{RefundSystemTypeEnum[text]}</div>
				);
			}
		},
		{
			title: '货物状态',
			key: 'goodsStatus',
			width: 100,
			minWidth: 80,
			sortSet: { ischecked: false },
			dataIndex: 'goodsStatus',
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-65') }>
						<p>{record.newGoodsStatusList?.join(',')}</p>
					</div>
				);
			}
		},
		{
			title: '售后原因',
			key: 'refundReason',
			dataIndex: 'refundReason',
			width: 140,
			minWidth: 140,
			render: (text, record, index) => {
				return (
					<>
						<div>
							售后原因：
							<span className={ cs('r-fc-black-65') }>
								{text}
							</span>
						</div>
						<div>
							<span>售后说明：</span>
							<span className={ cs('r-fc-black-65') }>{record.desc}</span>
						</div>
					</>
				);
			}
		},
		{
			title: '旗帜/卖家备注',
			key: 'sellerMemo',
			dataIndex: 'sellerMemo',
			width: 140,
			minWidth: 140,
			render: (text, record, index) => {
				// 添加判断是否为京喜订单的逻辑
				const hasJingxi = record.refundTagList?.includes('jxTrade');
				return (
					<>
						<div className="r-flex r-fw-w">
							<p className={ `${cs('r-fc-black-65')} r-mt-4` }>卖家备注：</p>
							<p>
								{getTradeFlag(0, null, record.sellerFlag)}
								{getTradeFlagTag(record.sellerFlag, record?.sellerFlagTag)}
								<span className={ cs('r-fc-black-65') }>{record['sellerMemo']}</span>
								{
									(!hasJingxi && isDistributor) && (
										<span className="r-as-c r-ml-2 r-fc-1890FF">
											{record['sellerMemo']
												? <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyMemo(record, index); } }><Icon type="bianji" /></span>
												: <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyMemo(record, index); } }><Icon type="tianjiabeizhu" /></span>}
										</span>
									)
								}

							</p>
						</div>
					</>
				);
			}
		},
		{
			title: '线下备注',
			key: 'localContent',
			dataIndex: 'localContent',
			width: 140,
			minWidth: 140,
			render: (text, record, index) => {
				return (
					<>
						<div className="r-flex r-fw-w r-mt-4">
							<p className={ `${cs('r-fc-black-65')}` }>线下备注：</p>
							<p>
								<span className={ cs('r-fc-black-65') }>{record[listItemName.线下备注]}</span>
								<span className="r-as-c r-ml-2 r-fc-1890FF">
									{record[listItemName.线下备注]
										? <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemo(record, index); } }><Icon type="bianji" /></span>
										: <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemo(record, index); } }><Icon type="tianjiabeizhu" /></span>}
								</span>
							</p>

						</div>
					</>
				);
			}
		},
		{
			title: '线下备注图片',
			key: 'localMemoPic',
			dataIndex: 'localMemoPic',
			width: 140,
			minWidth: 140,
			render: (text, record, index) => {
				const hasImgList = record.localContentPicList && record.localContentPicList.length > 0;
				return (
					<>
						<div className="r-flex r-fw-w r-mt-4">
							<p>
								{hasImgList ? (
									<div className="r-flex r-ai-c">
										{/* 修改为循环展示所有图片，从左到右排列，超出边界才换行 */}
										<div className="r-flex r-ai-c r-fw-w" style={ { maxWidth: '300px', display: 'flex', flexDirection: 'row' } }>
											{record.localContentPicList.map((imgUrl, imgIndex) => (
												<Popover
													key={ imgIndex }
													placement="right"
													content={ <Image src={ imgUrl } style={ { width: '300px' } } /> }
												>
													<div style={ { display: 'inline-block', marginRight: '4px', marginBottom: '4px' } }>
														<Image
															src={ imgUrl }
															width={ 40 }
															height={ 40 }
															style={ { objectFit: 'cover' } }
														/>
													</div>
												</Popover>
											))}
										</div>
										<span className="r-as-c r-ml-2 r-fc-1890FF">
											<span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemoPic(record); } }><Icon type="bianji" /></span>
										</span>
									</div>
								) : (
									<span className="r-as-c r-fc-1890FF">
										<span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemoPic(record); } }><Icon type="tianjiabeizhu" /></span>
									</span>
								)}
							</p>
						</div>
					</>
				);
			}
		},
		{
			title: '状态',
			key: 'refundStage',
			width: 120,
			minWidth: 100,
			dataIndex: 'refundStage',
			render: (text, record, index) => {
				return (
					<div>
						<TooltipText className={ cs('r-fc-black-45') } title="销售订单状态">
							<div className={ cs('r-fc-black-65') }>{record['orderStatusDesc']}</div>
						</TooltipText>
						<TooltipText className={ cs('r-fc-black-45') } title="退款单状态">
							<div className={ cs('r-fc-black-65') }>{record['refundStatusDesc']}</div>
						</TooltipText>
						<TooltipText className={ cs('r-fc-black-45') } title="售后工单状态">
							<div className={ cs('r-fc-black-65') }>
								<span style={ { color: getStatusTextColor(record.refundSystemStatus) } }>{record['refundSystemStatusDesc']}
								</span>
							</div>
						</TooltipText>

					</div>
				);
			}
		},
		{
			title: '售后异常',
			key: 'exceptionType',
			width: 120,
			minWidth: 120,
			dataIndex: 'exceptionType',
			render: (text, record, index) => {
				return (
					<div>
						<div className={ cs('r-fc-black-65', 'r-flex', 'r-ai-c', 'r-lh-20') }>
							{getExceptionLock(record['exceptionType'])}
							{getInterceptLock(record['logisticsInterceptMark'], record["interceptInvestorStr"], record["interceptStatusDesc"])}
						</div>
					</div>
				);
			}
		},
		{
			title: (
				<p>
					退货物流
					<Tooltip title="系统支持自动监控退货快递单号物流状态，并支持查询（高配版本功能）">
						<span className="r-ml-4 r-mr-4">
							<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
						</span>
					</Tooltip>
					<span className="r-c-error">限免</span>
				</p>
			),
			sortSet: { name: '退货物流' },
			key: 'sidStatus',
			dataIndex: 'sidStatus',
			width: 120,
			minWidth: 120,
			render: (text, record, index) => {
				let refundText = '';
				if (record['sid']) {
					refundText = `退：${record['sid']}`;
				}

				const renderExpressStatus = () => {
					let status = PackExpressStatusMirrorEnum[record.sidStatus] || '';
					let subStatus = PackExpressStatusMirrorEnum[record.sidSubStatus] || '';
					if (record.sidStatus === PackExpressStatusMirrorEnum['包裹异常']) {
						return (
							<div className="r-c-error">{status}-{subStatus}</div>
						);
					} else {
						return (
							<Tooltip title={ `${status}-${subStatus}` }>
								<span className="r-c-success">{status}</span>
							</Tooltip>
						);
					}
				};
				return (
					<div>
						<div>
							<ShowLogistic ydNo={ record['sid'] }><span className={ cs('r-fc-black-65') }>{refundText}</span></ShowLogistic>
							{refundText ? <Copy copyData={ record.sid } /> : null}
							{renderExpressStatus()}
						</div>
					</div>
				);
			}
		},
		{
			title: (
				<p>
					发货物流
					<Tooltip title="系统支持自动监控发货快递单号物流状态，并支持查询（高配版本功能）">
						<span className="r-ml-4 r-mr-4">
							<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
						</span>
					</Tooltip>
					<span className="r-c-error">限免</span>
				</p>
			),
			sortSet: { name: '发货物流' },
			key: 'delivery',
			dataIndex: 'delivery',
			width: 120,
			minWidth: 120,
			render: (text, record, index) => {
				let sendText = '';
				if (record['invoiceNo']) {
					sendText = `发：${record['invoiceNo']}`;
				}
				const renderExpressStatus = () => {
					let status = PackExpressStatusMirrorEnum[record.invoiceStatus] || '';
					let subStatus = PackExpressStatusMirrorEnum[record.invoiceSubStatus] || '';

					if (record.invoiceStatus === PackExpressStatusMirrorEnum['包裹异常']) {
						return (
							<div className="r-c-error">{status}-{subStatus}</div>
						);
					} else {
						return (
							<Tooltip title={ `${status}-${subStatus}` }>
								<span className="r-c-success">{status}</span>
							</Tooltip>
						);
					}
				};
				return (
					<div>
						<div onClick={ handleStopClick }>
							<ShowLogistic ydNo={ record['invoiceNo'] }><span className={ cs('r-fc-black-65') }>{sendText}</span></ShowLogistic>
							{sendText ? <Copy copyData={ record.invoiceNo } /> : null}
							{renderExpressStatus()}
						</div>
					</div>
				);
			}
		},
		{
			title: (
				<p>
					包裹签收时间
					<Tooltip title={ (
						<div>
							<p>包裹签收时间包含</p>
							<p>1. 发货包裹拒签时后，商家签收拒签包裹的时间</p>
							<p>2、买家发出退货包裹后，商家签收退货包裹的时间</p>
						</div>
					) }
					>
						<span className="r-ml-4 r-mr-4">
							<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
						</span>
					</Tooltip>
				</p>
			),
			sortSet: { name: '包裹签收时间', ischecked: false },
			key: 'invoiceSignTime',
			dataIndex: 'invoiceSignTime',
			width: 100,
			minWidth: 100,
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-65') }>
						{
							record.invoiceSignTime && <div>发货包裹：{dayjs(record.invoiceSignTime).format("YYYY-MM-DD HH:mm:ss")}</div>
						}
						{
							record.sidSignTime && <div>退货包裹：{dayjs(record.sidSignTime).format("YYYY-MM-DD HH:mm:ss")}</div>

						}
					</div>
				);
			}
		},
		{
			title: '售后渠道',
			sortSet: { ischecked: false },
			key: 'afterSaleChannel',
			dataIndex: 'afterSaleChannel',
			width: 100,
			minWidth: 100,
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-65') }>{afterSaleChannelText[text]}</div>
				);
			}
		},
		{
			title: '是否提交供应商',
			sortSet: { ischecked: true },
			key: 'distributorIsPush',
			dataIndex: 'distributorIsPush',
			width: 100,
			minWidth: 100,
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-65') }>{distributorIsPushEnum[text]}</div>
				);
			}
		},
		{
			title: '供应商确认状态',
			sortSet: { ischecked: true },
			key: 'supplierIsConfirm',
			dataIndex: 'supplierIsConfirm',
			width: 100,
			minWidth: 100,
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-65') }>{supplierIsConfirmEnum[text]}</div>
				);
			}
		},
		{
			title: '供应商',
			sortSet: { ischecked: true },
			key: 'supplierUser',
			dataIndex: 'supplierUser', // 这里的dataIndex还必须要有
			width: 100,
			minWidth: 100,
			render: (text, record, index) => {
				const list = record?.rspScmUserInfoList || [];
				// const name = list?.map(item => item?.supplierRemark || item?.supplierName)?.join('，');
				return list?.length > 0 ? (
					<div className={ cs('r-fc-black-65') }>
						{list?.map((item, index) => {
							return (
								<span key={ item?.supplierName || index }>{item?.supplierName}{item?.supplierRemark ? `(${item?.supplierRemark})` : ''}{index !== list.length - 1 ? ', ' : ''}</span>
							);
						})}
					</div>
				) : '';
			}
		},
		{
			title: '分销商',
			sortSet: { ischecked: true },
			key: 'distributorUser',
			dataIndex: 'distributorUser', // 这里的dataIndex还必须要有
			width: 100,
			minWidth: 100,
			render: (text, record, index) => {
				const list = record?.rspScmUserInfoList || [];
				// const name = list?.map(item => item?.distributorRemark || item?.distributorName)?.join('，');
				return list?.length > 0 ? (
					<div className={ cs('r-fc-black-65') }>
						{list?.map((item, index) => {
							return (
								<span key={ item?.distributorAccount || index }>{item?.distributorAccount}{item?.distributorRemark ? `(${item?.distributorRemark})` : ''}{index !== list.length - 1 ? ', ' : ''}</span>
							);
						})}
					</div>
				) : '';
			}
		},
		{
			title: '操作',
			colFixed: 'right',
			key: 'action',
			align: 'center',
			width: 180,
			minWidth: 180,
			colSpan: 2,
			dataIndex: 'action',
			className: cs('table-left-border'),
			render: (text, record, index) => {
				return (
					<Space direction="vertical">
						{record['exceptionType'] === 1 ? (
							<Button
								type="primary"
								danger
								size="small"
								onClick={ (e) => {
									e.stopPropagation();
									if (!checkDistributorIsPush('异常处理', record)) {
										return;
									}
									setExceptionModalVisible(true);
									setCurActionRowData(record);
								} }
							>异常处理
							</Button>
						) : null}
						{checkRefundAction(record).unpackRegister ? (<UnpackRegisterBtn record={ record } afterOK={ refreshPage } />) : null}
						{checkRefundAction(record).returnMoney ? (<ReturnMoneyBtn record={ record } afterOK={ refreshPage } companyList={ companyList } />) : null}
						{checkRefundAction(record).returnGoods ? (<ReturnGoodsBtn record={ record } afterOK={ refreshPage } />) : null}
						{ // 同意换货
							checkRefundAction(record).acceptReplacing ? (
								[PLAT_FXG, PLAT_TB, PLAT_XHS, PLAT_KS, PLAT_JD, PLAT_SPH].includes(record.platform) ? (
									<ReturnGoodsBtn title="同意换货" record={ record } afterOK={ refreshPage } />
								)
									: (
										<Button
											size="small"
											type="primary"
											onClick={ (e) => {
												e.stopPropagation();
												const href = getPlatformTradeLink(record.platform, record).refund;
												window.open(href, '_blank');
											} }
										>同意换货
										</Button>
									)

							) : null
						}
						{ // 同意补寄
							checkRefundAction(record).acceptReissue ? (
								// <ReturnGoodsBtn title="同意换货" record={ record } afterOK={ refreshPage } />
								<Button
									size="small"
									type="primary"
									onClick={ (e) => {
										e.stopPropagation();
										agreeRefundSupply(record);
									} }
								>同意补寄
								</Button>
							) : null
						}

						{ // 填写换货/补发单号
							checkRefundAction(record).writeExpressNo ? (
								<WriteExpressNoBtn onFinished={ (res) => writeExpressNoOnFinished(res) } data={ record } optName={ RefundSystemTypeEnum[record.refundSystemType] } />
							)
								: null
						}
						{record['exceptionType'] !== 1 && !record['reviewStatus'] ? (
							<Button
								className={ cs('r-btn-52C41A') }
								data-point={ Pointer['售后_售后订单_审核通过'] }
								onClick={ (e) => {
									e.stopPropagation();
									if (!checkDistributorIsPush('审核通过', record)) {
										return;
									}
									singleCancelCheck(record, true);
								} }
								size="small"
							>审核通过
							</Button>
						) : null}

					</Space>
				);
			}
		}
	];
	// 根据角色对表格内容过滤
	columns = getFilterColumnConfig(columns);

	// 拒绝退款/退货操作
	const onRefuseRefund = (row, text) => {
		// 如果是小红书，并且未发货仅退款时，不允许操作拒绝团款
		if (text === "拒绝退款") {
			const stopOpt = [PLAT_XHS].includes(row.platform) && row.orderStatus === "WAIT_SELLER_SEND_GOODS" && row.afterSaleType == afterSaleTypeText.仅退款;
			if (stopOpt) {
				Modal.info({
					title: '系统提示',
					centered: true,
					content: (
						<>
							<div style={ { color: '#555' } }>
								您选择的{PLAT_MAP[row.platform]}订单暂时不支持拒绝退款操作,请前往商家后台操作
							</div>
						</>
					),
				});
				return;
			}
		}
		setRefuseRefundModalVisible(true);
	};

	// 处理展开关闭的数据展示问题
	const onExpandRow = (expanded: boolean, record: any) => {
		let list = cloneDeep(tableExpandList);
		if (record['exceptionType'] === 1) {
			setTableExpandList([]);
			return;
		}
		if (list.includes(record['refundId'])) {
			list = list.filter(item => item !== record['refundId']);
		} else {
			list.push(record['refundId']);
		}
		if (expanded) {
			setTableExpandList(list);
		} else {
			setTableExpandList(list);
		}
	};
	const copyMissedList = () => {
		copyToPaste(missedList.join(","));
	};
	const copyDistributorPushList = () => {
		copyToPaste(distributorPushList.join(","));
	};
	const clearErrorList = () => {
		setMissedList([]);
		setDistributorPushList([]);
	};
	const renderNoMatchTradeList = () => {
		const messageContent = (
			<div className="r-fs-14 r-bold">
				以下<span className="r-bold r-c-error">&nbsp;{missedList.length}&nbsp;</span>个{MissedType[missedType]}未查询到，请确认信息是否填写正确
			</div>
		);
		const description = missedList.map((i, index) => {
			return (
				<span className="r-c-error r-bold">
					{i}{index + 1 === missedList.length ? "" : ","}&nbsp;
				</span>
			);
		});
		const messageContent2 = (
			<div className="r-fs-14 r-bold">
				以下<span className="r-bold r-c-error">&nbsp;{distributorPushList.length}&nbsp;</span>个{MissedType[missedType]}为分销售后单，请前往分销商的售后单页面处理
			</div>
		);
		const description2 = distributorPushList.map((i, index) => {
			return (
				<span className="r-c-error r-bold">
					{i}{index + 1 === distributorPushList.length ? "" : ","}&nbsp;
				</span>
			);
		});
		if (missedList?.length || distributorPushList?.length) {
			return (
				<div className="r-mb-16">
					<Alert
						className={ s["no-match-alert"] }
						style={ { padding: 16 } }
						message=""
						description={ (
							<div className="r-flex r-fd-c">
								{
									missedList?.length > 0 && (
										<div>
											{messageContent}
											<div>
												{description}
												<span className="kdzs-link-text" onClick={ copyMissedList }>
													复制
												</span>
											</div>
										</div>
									)
								}
								{
									distributorPushList?.length > 0 && isDistributor && (
										<div className={ missedList?.length ? 'r-mt-10' : '' }>
											{messageContent2}
											<div>
												{description2}
												<span className="kdzs-link-text" onClick={ copyDistributorPushList }>
													复制
												</span>
											</div>
										</div>
									)
								}
							</div>
						) }
						onClose={ () => clearErrorList() }
						type="warning"
						showIcon
						closable
					/>
				</div>
			);
		}
	};
	const responseAdapter = (data, params) => {
		setPageLoading(false);
		setSelectedRows([]);
		setSelectedRowKeys([]);
		// 当页数大于2的时候，后端返回的total是0，因为如果每页都加上total，后端的查询性能会降低
		if (data.pageNo == 1 && isNumber(data.total) && activeTabItem != "") {
			event.emit(EVENT_BUS.UPDATE_QUANTITY_STATISTICS, data.total, activeTabItem);
		}
		let dataSourceRes = data.list.map((i, index) => ({
			rowId: `${i.id}_${0}_${1}`,
			groupId: `groupId${index}`,
			...i,
		}));
		let showList = [];
		const hideList = [];
		const statusFilter = getRefundSystemStatusInitialValue();
		if (statusFilter?.length > 0) {
			dataSourceRes.forEach(i => {
				if (statusFilter.includes(i.refundSystemStatus)) {
					showList.push(i);
				} else {
					hideList.push(i);
				}
			});
		} else {
			showList = [...dataSourceRes];
		}
		setDataSourceAll(dataSourceRes);
		setDataSource(showList);
		setHideOrderList(hideList);
		setMissedList(data?.missedList || []);
		setDistributorPushList(data?.distributorPushList || []); // 分销售后单-前往分销商的售后页面处理
		setMissedType(data?.tipQueryBackType);
		return {
			list: dataSourceRes,
			total: data.total
		};
	};
	const rowSelection = {
		columnWidth: 30,
		onChange: (selectedRowKeysAct: number[], selectedRows: []) => {
			console.log(1629, selectedRowKeysAct);
			setSelectedRows([...selectedRows]);
			setSelectedRowKeys([...selectedRowKeysAct]);
		},
		renderCell: (checked: boolean, row, index: number, originNode: any) => {
			const { key, value } = {};
			const [rId, i, length] = row.rowId?.split('_');
			const disabledMap = {};
			if (value && value.length && dataSource) {
				value.forEach(v => {
					if (dataSource[v]) {
						const [id] = dataSource[v]['rowId']?.split('_');
						if (!disabledMap[id]) disabledMap[id] = [];
						disabledMap[id].push(v);
					}
				});
			}

			let cloneNode = originNode;
			if (disabledMap[rId] && Number(length) === disabledMap[rId].length) {
				cloneNode = React.cloneElement(originNode, {
					disabled: true
				});
			}
			return {
				children: <>{cloneNode}</>,
				props: {
					// rowSpan: row.colSpan || 0,
					style: row.colSpan ? {} : { borderTop: 0 }
				}
			};
		},
		getCheckboxProps: (record: any) => {
			const { key, value } = {};
			return {
				// disabled: key ? value.includes(record['sysSkuList'][0][key]) : false
			};
		},
		selectedRowKeys
	};
	// 控制表格展开内容的关闭
	const onCloseAllExpandTable = () => {
		// 获取触发事件源
		const eventEle = document.querySelectorAll('.refund_expand_table_close');
		if (eventEle.length > 0) {
			for (let index = 0; index < eventEle.length; index++) {
				eventEle[index].dispatchEvent(expandEvt);// 触发配置事件
			}
			setTableExpandList([]);
		}
	};

	const onBatchExportCancel = () => {
		setBatchExportModalVisible(false);
	};

	const onBatchExportOk = async(config) => {
		// 如果选择的是导出勾选订单，需要判断是否勾选了订单
		let requestParams = {
			latitude: 0,
		};
		const { type } = config;
		if (type === BatchExportType.导出勾选订单) {
			if (!selectedRows?.length) {
				message.error("请先选择需要导出的售后单");
				return;
			} else {
				requestParams["refundId"] = selectedRows.map(i => i.refundId).join(",");
				requestParams["scmSelectRefundSource"] = isDistributor ? 0 : 1; // 售后列表查询来源,0-正常售后单列表1-分销商的售后单列表
			}

		} else if (type === BatchExportType.导出查询结果) {
			requestParams = {
				...requestParams,
				...searchParams
			};
		}
		setBatchExportModalVisible(false);
		await downloadCenter({
			requestParams,
			fileName: isDistributor ? `售后单_${dayjs().format("YYYYMMDDHHmmss")}` : `分销商的售后单_${dayjs().format("YYYYMMDDHHmmss")}`,
			module: ModulesFunctionEnum.售后订单导出
		});


	};

	const tableExtraFn = (sortNode) => {
		return (
			<div className="r-flex r-ai-c r-jc-sb r-mb-16">
				<div>
					{
						hideOrderList?.length > 0 && (
							<div>
								说明：当前页
								<Button
									type="link"
									className="r-c-primary r-pd-0"
									onClick={ () => {
										message.success(`当前页${hideOrderList?.length}个隐藏售后单已展示在列表中`);
										setDataSource(dataSourceAll);
										setHideOrderList([]);
									} }
								>{hideOrderList?.length}
								</Button>
								个售后单已隐藏
							</div>
						)
					}
				</div>
				<div className="r-flex r-ai-c r-jc-fe">
					<span className="r-mr-0">售后单排序：</span>
					<Select
						options={ TRADE_LIST_SORT_OPTIONS.options }
						value={ sortValue }
						onChange={ sortTypeOnChange }
					/>
					<div className="r-ml-10">{sortNode}</div>
				</div>
			</div>
		);
	};
	const tableExtra = (
		<div>
			{renderNoMatchTradeList()}
		</div>

	);

	const RefundButton = (props) => {
		const { value, onChange } = props;

		const changeRefundPlat = (e) => {
			const _val = value ? 0 : 1;
			onChange?.(_val);
			console.log("changeRefundPlat:", form.getFieldsValue());
			ref?.current?.submit();
		};
		return (
			<div className={ cs(s["refund-container"], "r-pointer", s.afterSaleTopTabItem, value ? s.checkedTabItem : '') } onClick={ changeRefundPlat }>
				{value ? <CheckOutlined className="r-mr-4" /> : null}
				平台<span className="r-bold" style={ { color: "#f00" } }>极速退款</span>
			</div>
		);
	};

	const updateAdvanceConfig = (config) => {
		const advanceSettingConfig = {};
		config.forEach(config => {
			advanceSettingConfig[config.biz] = config.value;
		});
		setAdvanceSettingConfig(pre => ({
			...pre,
			...advanceSettingConfig
		}));
	};

	// 快捷tab查询条件
	const invariantConditionList = useMemo(() => {
		const list = [
			{
				label: "",
				name: 'refundTabShowType',
				style: { marginBottom: 8, alignItems: "center" },
				colProps: {
					span: 24
				},
				children: (
					<div>
						{/* 快捷筛选设置 */}
						<QuickFilterSelect loading={ loading } updateAdvanceConfig={ updateAdvanceConfig } activeTabItem={ activeTabItem } quickFilterSelectChange={ quickFilterSelectChange } advanceSettingConfig={ advanceSettingConfig } isShowZeroStockVersion={ isShowZeroStockVersion } isDistributor={ isDistributor } isSupplier={ isSupplier } pageLoaded={ pageLoaded } />
					</div>
				)
			}
		];
		return list;
	}, [advanceSettingConfig, isShowZeroStockVersion, loading, isDistributor, isSupplier, pageLoaded, activeTabItem]);


	/** -------------- 自定义查询条件相关逻辑 ------------ */
	// const itemInfoQueryNotIncludeList = useMemo(() => {
	// 	return getGoodsInfoSelectOptions(userStore.userInfo?.version, false);
	// }, [userStore.userInfo?.version]);

	const itemInfoQueryIncludeList = useMemo(() => {
		return getGoodsInfoSelectOptions(userStore.userInfo?.version, true, true);
	}, [userStore.userInfo?.version]);

	// const itemInfoQueryNotIncludePlaceholder = useMemo(() => {
	// 	return itemInfoQueryNotIncludeList.filter(i => i.value == formData.goodsNotIncludeStatus)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名'];
	// }, [itemInfoQueryNotIncludeList, formData.goodsNotIncludeStatus]);

	const itemInfoQueryIncludePlaceholder = useMemo(() => {
		return itemInfoQueryIncludeList.filter(i => i.value == formData.goodsIncludeStatus)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名'];
	}, [itemInfoQueryIncludeList, formData.goodsIncludeStatus]);

	const itemInfoQueryIncludeMaxNum = useMemo(() => {
		if (!('goodsIncludeStatus' in formData) || formData.goodsIncludeStatus == itemInfoEnumValue.商品包含 || formData.goodsIncludeStatus == itemInfoEnumValue.商品不包含) {
			return [200, 500];
		}
		return [50, 50];
	}, [formData.goodsIncludeStatus, showAllCondition]);

	// const itemInfoQueryNotIncludeMaxNum = useMemo(() => {
	// 	if (!('goodsIncludeStatus' in formData) || formData.goodsIncludeStatus == itemInfoEnumValue.商品不包含) {
	// 		return [200, 500];
	// 	}
	// 	return [50, 50];
	// }, [formData.goodsNotIncludeStatus, showAllCondition]);

	const verifySearchParams = (params: Partial<SearchTableProps>): boolean => {
		let goodsInfoIncludeNum = [itemInfoEnumValue.商品包含, itemInfoEnumValue.商品不包含].includes(params.goodsIncludeStatus) ? [200, 500] : [50, 50];
		const placeholderTips = getPlaceholderTips();
		if (params.shortNameIncludingList?.length > goodsInfoIncludeNum?.[0]) {
			message.error(`同时查询 ${placeholderTips[params.goodsIncludeStatus]?.[0]} 的数量不能超过${goodsInfoIncludeNum?.[0]}个`);
			return false;
		}
		if (params.skuIncludingList?.length > goodsInfoIncludeNum?.[1]) {
			message.error(`同时查询 ${placeholderTips[params.goodsIncludeStatus]?.[1]} 的数量不能超过${goodsInfoIncludeNum?.[1]}个`);
			return false;
		}
		return true;
	};

	const handleRemoveTag = (value, fieldName) => {
		console.log('%c [ value ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
		const oldValue = form.getFieldValue(fieldName) || [];
		const newValues = oldValue.filter((item) => item !== value);

		form.setFieldsValue({
			[fieldName]: newValues
		});
		setFormData(prev => {
			return {
				...prev,
				[fieldName]: newValues
			};
		});
	};

	const handleSetDefault = (fieldName, value) => {
		form.setFieldsValue({
			[fieldName]: value
		});
		setFormData(prev => {
			return {
				...prev,
				[fieldName]: value
			};
		});
		setFormOptionDefault(prev => {
			local.set(`afterSaleListFormOptionDefault_${userId}`, {
				...prev,
				[fieldName]: value
			});
			return {
				...prev,
				[fieldName]: value
			};
		});
	};

	const handleSetBtnsDefault = (fieldName, value) => {
		setOperateBtnsDefault(prev => {
			local.set(`afterSaleListOperateBtnsDefault_${userId}`, {
				...prev,
				[fieldName]: value
			});
			return {
				...prev,
				[fieldName]: value
			};
		});
	};

	const inputRateProps = {
		formatter: (value: any) => {
			return value && value?.split(".")[1]?.length > 2 ? Number(value).toFixed(2) : value;
		},
	};

	const getFormItemByFormName = (name) => {
		switch (name) {
			case 'rangeTime':
				return [{
					name: 'timeType',
					label: "",
					children: (
						<Select className="high-light-bg" size="small" optionLabelProp="label" style={ { width: 160 } }>
							{
								timeTypeOptions_aftersale.map(item => {
									return (
										<Option value={ item.value } label={ item.label } key={ item.label }>
											<div className="r-flex r-jc-sb r-ai-c">
												<span>{item.label}</span>
												{
													formOptionDefault?.timeType === item.value
														? (
															<Icon
																className="r-c-warning"
																type="guding"
																onClick={ (e) => {
																	e.stopPropagation();
																} }
															/>
														)
														: (
															<Icon
																className="r-c-999"
																type="weiguding"
																onClick={ (e) => {
																	e.stopPropagation();
																	handleSetDefault("timeType", item.value);
																} }
															/>
														)
												}
											</div>
										</Option>
									);
								})
							}
						</Select>
					),
				}, {
					name: 'rangeTime',
					label: "",
					initialValue: initialRangeTime,
					children: (
						<KdzsDateRangePicker1 className="high-light-bg" style={ { width: 159 } } cacheQuickChoose datePickerKey={ DatePickerKey.aftersale_trade } useServeTime />
					),
				}];
			case 'platformInfo':
				return [{
					name: 'platformInfo',
					label: "",
					initialValue: 'ALL',
					children: (
						<ShopMultiSelect
							bgHighLight
							isSendPoint
							style={ { width: 159 } }
							size="small"
							isHasHandPlat
							hidePlatforms={ [PLAT_KTT, PLAT_DW, PLAT_YZ] }
						/>
					),
				}];
			case 'afterSaleTypeList':
				return [{
					name: 'afterSaleTypeList',
					label: "",
					initialValue: [],
					children: (
						<SelectMultiple
							className={ formData.afterSaleTypeList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="售后类型"
							options={ afterSaleTypeNames.map(type => {
								return { label: type.name, value: type.value };
							}) }
							fieldName="afterSaleTypeList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case 'refundReasonList':
				return [{
					name: 'refundReasonList',
					label: "",
					initialValue: [],
					children: (
						<SelectMultiple
							className={ formData.refundReasonList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="售后原因"
							options={ afterSaleReason.map(type => {
								return { label: type.name, value: type.value };
							}) }
							fieldName="refundReasonList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case 'refundStatus':
				return [{
					name: 'refundStatusList',
					label: "",
					children: (
						<SelectMultiple
							className={ formData.refundStatusList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="平台退款状态"
							options={ refundStatusListOptions_aftersale }
							fieldName="refundStatusList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case 'tradeStatus':
				return [{
					name: 'tradeStatusList',
					label: "",
					children: (
						<SelectMultiple
							className={ formData.tradeStatusList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="订单状态"
							options={ tradeStatusOptions_aftersale }
							fieldName="tradeStatusList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case 'refundSystemStatusList':
				return [{
					name: 'refundSystemStatusList',
					label: "",
					initialValue: getRefundSystemStatusInitialValue(),
					children: (
						<SelectMultiple
							className={ formData.refundSystemStatusList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="售后工单状态"
							options={ refundSystemStatusListOptions_aftersale }
							fieldName="refundSystemStatusList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case 'goodsStatus':
				return [{
					name: 'newGoodsStatusList',
					label: "",
					children: (
						<SelectMultiple
							className={ formData.newGoodsStatusList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="货物状态"
							options={ goodsStatusOptions }
							fieldName="newGoodsStatusList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case 'confirmItemStatus':
				return [{
					name: 'confirmItemStatusList',
					label: "",
					children: (
						<SelectMultiple
							className={ formData.confirmItemStatusList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="收货状态"
							optionsRender={ (options) => {
								return options.map(item => {
									return (
										<Select.Option value={ item.value } label={ item.label }>
											<div className="r-flex r-ai-c">
												<div>{item.label}</div>
												<div className="r-ml-5">{item.tips}</div>
											</div>
										</Select.Option>
									);
								});
							} }
							optionLabelProp="label"
							options={ [
								{ label: '未收货', value: 0, tips: <Tooltip title="按照实收数量运算，故为0确认收货也算为未收货"><QuestionCircleOutlined className="r-ml-5 r-c-999" /></Tooltip> },
								{ label: '部分收货', value: 1 },
								{ label: '全部收货', value: 2 },
								{ label: '超收', value: 3 }
							] }
							fieldName="confirmItemStatusList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case 'handleReturnStatus':
				return [{
					name: 'handleReturnStatusList',
					label: "",
					children: (
						<SelectMultiple
							className={ formData.handleReturnStatusList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="入库状态"
							options={ handleReturnStatusOptions_aftersale }
							fieldName="handleReturnStatusList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case 'itemDealStatus':
				return [{
					name: 'itemDealStatus',
					label: "",
					children: (
						<Select className={ formData.itemDealStatus ? 'high-light-bg' : '' } size="small" style={ { width: 160 } } placeholder="商品处理状态">
							<Option value="">全部状态</Option>
							<Option value={ 1 }>新创建</Option>
							<Option value={ 2 }>部分退货</Option>
							<Option value={ 3 }>全部退货</Option>
							<Option value={ 4 }>部分处理</Option>
							<Option value={ 5 }>全部处理</Option>
						</Select>
					),
				}];
			case 'reviewStatus':
				return [{
					name: 'reviewStatus',
					label: "",
					children: (
						<Select className={ formData.reviewStatus ? 'high-light-bg' : '' } size="small" style={ { width: 160 } } placeholder="审核状态" optionLabelProp="label" allowClear>
							{
								reviewStatusOptions_aftersale.map(item => {
									return (
										<Option value={ item.value } label={ item.label } key={ item.label }>
											<div className="r-flex r-jc-sb r-ai-c">
												<span>{item.label}</span>
											</div>
										</Option>
									);
								})
							}
						</Select>
					),
				}];
			case 'flagValue':
				if (['-1'].includes(form.getFieldValue('flagValue'))) {
					return [
						{
							name: 'flagValue',
							label: "",
							children: (
								<FlagAndMemoSelect bgHighLight size="small" className={ s['list-flag-item'] } nodeMessage />
							)
						},
						{
							name: 'sellerMemo',
							label: "",
							children: (
								<InputMulti
									className={ formData.sellerMemo ? "high-light-bg" : "" }
									placeholder="备注内容"
									maxInputNum={ 50 }
									maxInputLength={ 1000 }
									lengthErrorMsg="备注最多输入1000个字数，请重新输入"
									numErrorMsg="单次查询最多筛选50个备注，请重新输入"
									style={ { width: '160px' } }
									size="small"
								/>
							)
						},
						{
							name: 'sellerFlag',
							label: "",
							children: (
								<FlagSelect
									className={ formData.sellerFlag ? "high-light-bg" : "" }
									placeholder="旗帜"
									style={ { width: '160px' } }
									size="small"
								/>
							)
						}

					];
				} else {
					return [
						{
							name: 'flagValue',
							label: "",
							children: (
								<FlagAndMemoSelect bgHighLight nodeMessage size="small" className={ s['list-flag-item'] } />
							)
						}

					];
				}
			case 'sysItemInclude': // 商品包含
				return [{
					name: 'sysItemInclude',
					label: "",
					children: (
						<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
							<Form.Item noStyle name="goodsIncludeStatus" >
								<Select
									size="small"
									style={ { flex: 0.6, width: '114px' } }
									onChange={ () => { } }
									optionLabelProp="label"
									dropdownMatchSelectWidth={ false }
									className={ `${(formData?.shortNameIncludingList?.length || formData?.skuIncludingList?.length) ? 'high-light-bg' : ''}` }
								>
									{
										itemInfoQueryIncludeList.map(item => {
											return (
												<Option value={ item.value } label={ item.label }>
													<div className="r-flex r-jc-sb r-ai-c">
														<span>{item.label}</span>
														{
															formOptionDefault?.goodsIncludeStatus === item.value
																? (
																	<Icon
																		className="r-c-warning"
																		type="guding"
																		onClick={ (e) => {
																			e.stopPropagation();
																		} }
																	/>
																) : (
																	<Icon
																		className="r-c-999"
																		type="weiguding"
																		onClick={ (e) => {
																			e.stopPropagation();
																			handleSetDefault("goodsIncludeStatus", item.value);
																		} }
																	/>
																)
														}
													</div>
												</Option>
											);
										})
									}
								</Select>
							</Form.Item>
							{
								itemInfoQueryIncludePlaceholder?.[0] ? (
									<Form.Item noStyle name="shortNameIncludingList">
										<InputArrayMulti
											className={ formData?.shortNameIncludingList?.length ? 'high-light-bg' : '' }
											size="small"
											placeholder={ itemInfoQueryIncludePlaceholder[0] }
											maxInputNum={ itemInfoQueryIncludeMaxNum?.[0] }
											maxTagCount={ 1 }
											maxTagTextLength={ 3 }
											open={ false }
											tokenSeparators={ null }
											style={ { flex: 1, width: 192 } }
										/>
									</Form.Item>
								) : ''
							}
							{
								itemInfoQueryIncludePlaceholder?.[1] ? (
									<Form.Item noStyle name="skuIncludingList" >
										<InputArrayMulti
											className={ formData?.skuIncludingList?.length ? 'high-light-bg' : '' }
											size="small"
											placeholder={ itemInfoQueryIncludePlaceholder[1] }
											maxInputNum={ itemInfoQueryIncludeMaxNum?.[1] }
											maxTagCount={ 1 }
											maxTagTextLength={ 3 }
											open={ false }
											tokenSeparators={ null }
											style={ { flex: 1, width: 192 } }
										/>
									</Form.Item>
								) : ''
							}
						</Input.Group>
					),
				}];

			case 'supplierInclude': // 市场/档口/供应商
				return [{
					name: 'supplierInclude',
					label: "",
					children: (
						<Input.Group compact style={ { display: "flex", alignItems: "center", width: '496px' } }>
							<Form.Item noStyle name="supplierIsIncluding">
								<EnumStringSelect
									className={ `${(formData?.supplierIncludingList || formData?.marketIncludingList || formData?.stallIncludingList) ? 'high-light-bg' : ''}` }
									enum={ supplierOptionsEnum }
									style={ { width: 124 } }
									size="small"
									allowClear={ false }
								/>
							</Form.Item>
							<Form.Item noStyle name="supplierIncludingList">
								<InputMulti
									className={ formData.supplierIncludingList ? "high-light-bg" : "" }
									placeholder="供应商"
									maxInputNum={ 500 }
									style={ { flex: 1 } }
									size="small"
								/>
							</Form.Item>
							<Form.Item noStyle name="marketIncludingList">
								<InputMulti
									className={ formData.marketIncludingList ? "high-light-bg" : "" }
									placeholder="市场"
									maxInputNum={ 500 }
									style={ { flex: 1 } }
									size="small"
								/>
							</Form.Item>
							<Form.Item noStyle name="stallIncludingList">
								<InputMulti
									className={ formData.stallIncludingList ? "high-light-bg" : "" }
									placeholder="档口"
									maxInputNum={ 500 }
									style={ { flex: 1 } }
									size="small"
								/>
							</Form.Item>
						</Input.Group>
					),
				}];
			// case 'sysItemNotInclude': // 商品不包含
			// 	return [{
			// 		name: 'sysItemNotInclude',
			// 		label: "",
			// 		children: (
			// 			<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
			// 				<Form.Item noStyle name="goodsNotIncludeStatus" >
			// 					<Select
			// 						size="small"
			// 						style={ { flex: 0.6 } }
			// 						onChange={ () => { } }
			// 						options={ itemInfoQueryNotIncludeList }
			// 						dropdownMatchSelectWidth={ false }
			// 					/>
			// 				</Form.Item>
			// 				{
			// 					itemInfoQueryNotIncludePlaceholder?.[0] ? (
			// 						<Form.Item noStyle name="shortNameNotIncludingList">
			// 							<InputArrayMulti
			// 								size="small"
			// 								placeholder={ itemInfoQueryNotIncludePlaceholder[0] }
			// 								maxInputNum={ itemInfoQueryNotIncludeMaxNum?.[0] }
			// 								maxTagCount={ 1 }
			// 								maxTagTextLength={ 8 }
			// 								open={ false }
			// 								tokenSeparators={ null }
			// 								style={ { flex: 1, width: 192 } }
			// 							/>
			// 						</Form.Item>
			// 					) : ''
			// 				}
			// 				{
			// 					itemInfoQueryNotIncludePlaceholder?.[1] ? (
			// 						<Form.Item noStyle name="skuNotIncludingList" >
			// 							<InputArrayMulti
			// 								size="small"
			// 								placeholder={ itemInfoQueryNotIncludePlaceholder[1] }
			// 								maxInputNum={ itemInfoQueryNotIncludeMaxNum?.[1] }
			// 								maxTagCount={ 1 }
			// 								maxTagTextLength={ 8 }
			// 								open={ false }
			// 								tokenSeparators={ null }
			// 								style={ { flex: 1, width: 192 } }
			// 							/>
			// 						</Form.Item>
			// 					) : ''
			// 				}
			// 			</Input.Group>
			// 		),
			// 	}];
			// case 'tid':
			// 	return [{
			// 		name: 'tid',
			// 		label: "",
			// 		children: <InputMulti maxInputNum={ 1000 } placeholder="系统单号" size="small" />,
			// 	}];
			// case 'ptTid':
			// 	return [{
			// 		name: 'ptTid',
			// 		label: "",
			// 		children: <InputMulti maxInputNum={ 1000 } placeholder="订单编号" size="small" />,
			// 	}];
			// case 'refundId':
			// 	return [{
			// 		name: 'refundId',
			// 		label: "",
			// 		children: <InputMulti maxInputNum={ 1000 } placeholder="售后单号" size="small" />,
			// 	}];
			case 'refundId':
				return [
					{
						name: 'refundOrPtTidList',
						label: "",
						children: (
							<InputMulti className={ formData.refundOrPtTidList ? 'high-light-bg' : '' } maxInputNum={ 1000 } placeholder="订单/售后单号" size="small" />
						),
					}
				];
			case 'buyerNick':
				return [{
					name: 'buyerNick',
					label: "",
					children: <Input className={ formData.buyerNick ? 'high-light-bg' : '' } placeholder="买家昵称" />,
				}];
			case 'invoiceNo':
				return [{
					name: '',
					label: "",
					children: (
						<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
							<Form.Item noStyle name="logisticsSearchType">
								<Select size="small" optionLabelProp="label" style={ { width: 130 } }>
									{
										invoiceNoOptions_aftersale.map(item => {
											return (
												<Option value={ item.value } label={ item.label }>
													<div className="r-flex r-jc-sb r-ai-c">
														<span>{item.label}</span>
														{
															formOptionDefault?.logisticsSearchType === item.value
																? (
																	<Icon
																		className="r-c-warning"
																		type="guding"
																		onClick={ (e) => {
																			e.stopPropagation();
																		} }
																	/>
																)
																: (
																	<Icon
																		className="r-c-999"
																		type="weiguding"
																		onClick={ (e) => {
																			e.stopPropagation();
																			handleSetDefault("logisticsSearchType", item.value);
																		} }
																	/>
																)
														}
													</div>
												</Option>
											);
										})
									}
								</Select>
							</Form.Item>
							<Form.Item noStyle name="logisticsNoList">
								<InputMulti className={ formData.logisticsNoList ? 'high-light-bg' : '' } style={ { width: 208 } } maxInputNum={ 1000 } placeholder="快递单号" size="small" />
							</Form.Item>
							<Form.Item noStyle name="logisticsCompanyName">
								<Input className={ formData.logisticsCompanyName ? 'high-light-bg' : '' } style={ { width: 150 } } placeholder="快递公司" size="small" />
							</Form.Item>
							<Form.Item noStyle name="logisticsStatusEnum">
								<SelectMultiple
									className={ formData.logisticsStatusEnum?.length > 0 ? 'high-light-bg' : '' }
									placeholder={ <div>物流状态<span className="r-c-error">(限免)</span></div> }
									options={ EXPRESS_STATUS_OPTIONS.options }
									fieldName="logisticsStatusEnum"
									handleRemoveTag={ handleRemoveTag }
									style={ { width: 179 } }
								/>
							</Form.Item>
						</Input.Group>

					),
				}];
			// case 'invoiceNo':
			// 	return [{
			// 		name: 'logisticsNoList',
			// 		label: "",
			// 		children: <InputMulti maxInputNum={ 1000 } placeholder="发货快递单号" size="small" />,
			// 	}];
			// case 'sid':
			// 	return [{
			// 		name: 'sid',
			// 		label: "",
			// 		children: <InputMulti maxInputNum={ 1000 } placeholder="退货快递单号" size="small" />,
			// 	}];
			case 'refundSystemTypeList':
				return [{
					name: 'refundSystemTypeList',
					label: "",
					initialValue: [],
					children: (
						<SelectMultiple
							className={ formData.refundSystemTypeList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="工单类型"
							options={ refundSystemTypeListOptions_aftersale }
							fieldName="refundSystemTypeList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case "isNoTradeMess":
				return [{
					name: 'isNoTradeMess',
					label: "",
					children: (
						<Select className={ formData.isNoTradeMess !== undefined ? 'high-light-bg' : '' } size="small" style={ { width: 160 } } placeholder="是否无主件" optionLabelProp="label" allowClear>
							{
								isNoTradeMessOptions_aftersale.map(item => {
									return (
										<Option value={ item.value } label={ item.label } key={ item.label }>
											<div className="r-flex r-jc-sb r-ai-c">
												<span>{item.label}</span>
											</div>
										</Option>
									);
								})
							}
						</Select>
					),
				}];
			case OfflineMemoEnum.线下备注:
			{
				const offlineMemoList = [{
					name: OfflineMemoEnum.是否有线下备注,
					label: "",
					children: (
						<Select className={ formData[OfflineMemoEnum.是否有线下备注] !== undefined ? "high-light-bg" : "" } size="small" style={ { width: 160 } } allowClear placeholder="线下备注">
							<Option value="0">无线下备注</Option>
							<Option value="1">有线下备注</Option>
						</Select>
					),
				}];
				if (form.getFieldValue(OfflineMemoEnum.是否有线下备注) == 1) {
					offlineMemoList.push({
						name: OfflineMemoEnum.线下备注内容,
						label: "",
						children: (
							<Input className={ formData[OfflineMemoEnum.线下备注内容] ? 'high-light-bg' : '' } placeholder="请输入线下备注" />
						),
					});
				}
				return offlineMemoList;
			}
			case "scmRefundType":
				return [{
					name: 'scmRefundType',
					label: "",
					children: (
						<Select className={ formData.scmRefundType !== undefined ? 'high-light-bg' : '' } size="small" style={ { width: 160 } } placeholder="自发/代发" optionLabelProp="label" allowClear>
							{
								scmRefundTypeOptions_aftersale.map(item => {
									return (
										<Option value={ item.value } label={ item.label } key={ item.label }>
											<div className="r-flex r-jc-sb r-ai-c">
												<span>{item.label}</span>
											</div>
										</Option>
									);
								})
							}
						</Select>
					),
				}];
			case "scmSupplierUserIdList": // 选择供应商
				return [{
					name: 'scmSupplierUserIdList',
					label: "",
					children: <DistributorSelect firstSelectStyle={ { width: '94px' } } style={ { width: '235px' } } roleType="sale" size="small" />
				}];
			case "scmDistributorUserId": // 选择分销商
				return [{
					name: 'scmDistributorUserId',
					label: "",
					children: <DistributorSelect firstSelectStyle={ { width: '94px' } } style={ { width: '235px' } } roleType="supplier" size="small" />
				}];
			// case 'marketStallSupplier':
			// 	return [{
			// 		name: 'marketStallSupplier',
			// 		label: "",
			// 		children: <InputMulti maxInputNum={ 500 } placeholder="市场/档口/供应商" size="small" />,
			// 	}];
			case 'distributorIsPush':
				return [{
					name: 'distributorIsPush',
					label: "",
					children: (
						<Select className={ formData.distributorIsPush !== undefined ? 'high-light-bg' : '' } size="small" style={ { width: 160 } } placeholder="是否提交供应商" optionLabelProp="label" allowClear>
							{
								distributorIsPushOptions_aftersale.map(item => {
									return (
										<Option value={ item.value } label={ item.label } key={ item.label }>
											<div className="r-flex r-jc-sb r-ai-c">
												<span>{item.label}</span>
											</div>
										</Option>
									);
								})
							}
						</Select>
					),
				}];
			case 'supplierIsConfirm':
				return [{
					name: 'supplierIsConfirm',
					label: "",
					children: (
						<Select className={ formData.supplierIsConfirm !== undefined ? 'high-light-bg' : '' } size="small" style={ { width: 160 } } placeholder="供应商确认状态" optionLabelProp="label" allowClear>
							{
								supplierIsConfirmOptions_aftersale.map(item => {
									return (
										<Option value={ item.value } label={ item.label } key={ item.label }>
											<div className="r-flex r-jc-sb r-ai-c">
												<span>{item.label}</span>
											</div>
										</Option>
									);
								})
							}
						</Select>
					),
				}];
			case 'timeOutSearch':
				return [{
					name: 'timeOutSearch',
					label: "",
					children: (
						<Select className={ formData.timeOutSearch ? 'high-light-bg' : '' } size="small" style={ { width: 160 } } placeholder="即将超时" optionLabelProp="label" allowClear>
							{
								timeOutSearchOptions_aftersale.map(item => {
									return (
										<Option value={ item.value } label={ item.label } key={ item.label }>
											<div className="r-flex r-jc-sb r-ai-c">
												<span>{item.label}</span>
											</div>
										</Option>
									);
								})
							}
						</Select>
					),
				}];
			case 'applyRefundFee':
				return [{
					name: 'applyRefundFee',
					label: "",
					children: (
						<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
							<Form.Item noStyle name="applyRefundFeeType">
								<Select size="small" optionLabelProp="label" style={ { width: 131 } }>
									{
										applyRefundFeeOptions_aftersale.map(item => {
											return (
												<Option value={ item.value } label={ item.label }>
													<div className="r-flex r-jc-sb r-ai-c">
														<span>{item.label}</span>
														{
															formOptionDefault?.applyRefundFeeType === item.value
																? (
																	<Icon
																		className="r-c-warning"
																		type="guding"
																		onClick={ (e) => {
																			e.stopPropagation();
																		} }
																	/>
																)
																: (
																	<Icon
																		className="r-c-999"
																		type="weiguding"
																		onClick={ (e) => {
																			e.stopPropagation();
																			handleSetDefault("applyRefundFeeType", item.value);
																		} }
																	/>
																)
														}
													</div>
												</Option>
											);
										})
									}
								</Select>
							</Form.Item>
							<Form.Item noStyle name="minRefundFee">
								<InputNumber className={ formData.minRefundFee ? 'high-light-bg' : '' } size="small" placeholder="最小值" style={ { width: 95, height: 24 } } { ...inputRateProps } min={ 0 } />
							</Form.Item>
							<div className="r-relative" style={ { height: 24, marginLeft: '9px' } }>
								<div className="r-absolute" style={ { top: '50%', transform: 'translate(-7px, -50%)' } }>-</div>
							</div>
							<Form.Item noStyle name="maxRefundFee">
								<InputNumber className={ formData.maxRefundFee ? 'high-light-bg' : '' } size="small" placeholder="最大值" style={ { width: 95, height: 24 } } { ...inputRateProps } min={ 0 } />
							</Form.Item>
						</Input.Group>
					),
				}];
			case 'refundTagList':
				return [{
					name: '',
					label: "",
					children: (
						<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
							<Form.Item noStyle name="isQueryContainRefundTag" >
								<Select size="small" optionLabelProp="label" style={ { width: 149 } }>
									{
										[
											{
												label: "售后标签(包含)",
												value: 1
											},
											{
												label: "售后标签(不包含)",
												value: 0
											}
										].map(item => {
											return (
												<Option value={ item.value } label={ item.label }>
													<div className="r-flex r-jc-sb r-ai-c">
														<span>{item.label}</span>
														{
															formOptionDefault?.isQueryContainRefundTag === item.value
																? (
																	<Icon
																		className="r-c-warning"
																		type="guding"
																		onClick={ (e) => {
																			e.stopPropagation();
																		} }
																	/>
																)
																: (
																	<Icon
																		className="r-c-999"
																		type="weiguding"
																		onClick={ (e) => {
																			e.stopPropagation();
																			handleSetDefault("isQueryContainRefundTag", item.value);
																		} }
																	/>
																)
														}
													</div>
												</Option>
											);
										})
									}
								</Select>
							</Form.Item>
							<Form.Item noStyle name="refundTagList">
								<SelectMultiple
									className={ formData.refundTagList?.length > 0 ? 'high-light-bg' : '' }
									placeholder="售后标签"
									options={ [
										{ label: '平台极速退款', value: 'speedRefund' },
										{ label: '退货物流同单多售后', value: 'sidSameTradeMoreRefund' },
										{ label: '退货物流异单多售后', value: 'sidDifferentTradeMoreRefund' }
									] }
									fieldName="refundTagList"
									handleRemoveTag={ handleRemoveTag }
									style={ { width: 180 } }
								/>
							</Form.Item>
						</Input.Group>

					),
				}];
			case 'refundExceptionList':
				return [{
					name: 'refundExceptionList',
					label: "",
					children: (
						<SelectMultiple
							className={ formData.refundExceptionList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="售后异常"
							options={ isShowZeroStockVersion ? [] : refundExceptionListOptions_aftersale }
							fieldName="refundExceptionList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			case 'exchangeRefundStatusList':
				return [{
					name: 'exchangeRefundStatusList',
					label: "",
					children: (
						<SelectMultiple
							className={ formData.exchangeRefundStatusList?.length > 0 ? 'high-light-bg' : '' }
							placeholder="换货/补寄订单状态"
							options={ exchangeRefundStatusListOptions_aftersale }
							fieldName="exchangeRefundStatusList"
							handleRemoveTag={ handleRemoveTag }
						/>
					),
				}];
			default:
				break;
		}
	};
	const customConditionContent = (
		<div className="r-flex">
			<Button type="link" onClick={ () => toggleShowAllCondition() }>{showAllCondition ? '默认查询条件' : '所有查询条件'}</Button>
			<Button type="link" onClick={ () => setCustomSearchConditionModalVisible(true) }>自定义查询条件</Button>
		</div>
	);

	const customSearchConditionOnOk = (conditionList) => {
		setConditionList(conditionList);
		setShowAllCondition(false);
		setCustomSearchConditionModalVisible(false);
	};

	const customSearchConditionOnCancel = () => {
		setCustomSearchConditionModalVisible(false);
	};

	const getSearchConditionConfig = async() => {
		const disabledConditions = ["rangeTime", "platformInfo", "afterSaleTypeList", "refundStatus"];
		let list = [];
		const advanceSettingConfig = {};
		// 默认的高级设置配置
		const defaultAdvancedSettingKey = [
			bizObj.自动审核,
			bizObj.自动关闭,
			bizObj.自动标记处理中,
			bizObj.自动标记已完成,
			bizObj.换货补发订单发货后自动上传,
			bizObj.隐藏显示设置,
			bizObj.售后订单快捷筛选设置,
			AftersaleGlobalConfig.已发货仅退款售后单同步至系统后自动更新公工单类型为拒收退货
		];
		try {
			let res = await GetRefundGlobalConfigListApi({
				bizEnumList: [
					bizObj.列宽设置,
					bizObj.列显示设置,
					bizObj.定制查询条件,
					bizObj.隐藏显示设置,
					bizObj.产品内容设置,
					...defaultAdvancedSettingKey // 默认的高级设置配置key
				]
			});

			// 初始化默认值由后端配置，前端不处理

			res.forEach(config => {
				// 考虑产品内容初始化需要默认值，区分保存空和初始值为空
				if (config.biz === "refundListProductContentSetting") {
					// 转化
					const valueArr = config?.value ? config?.value?.split(',').filter(value => value).map(x => REVERSE_MAPPING[x]) : [];
					// console.log('config?.value', config?.value?.split(','), config?.value?.split(',').map(x => REVERSE_MAPPING[x]), valueArr, REVERSE_MAPPING);
					// console.log(1232312312321321, valueArr, '-------------------------');
					setProductContentList(valueArr);
				} else if (config.biz === 'refundListInfoShowSet') {
					// 转化
					console.log(config?.value, '999999999999nickArrnickArr');
					const nickArr = config?.value ? config?.value?.split(',') : [];
					// console.log('config?.value', config?.value?.split(','), config?.value?.split(',').map(x => REVERSE_MAPPING[x]), valueArr, REVERSE_MAPPING);
					// console.log(1232312312321321, valueArr, '-------------------------');
					setBuyNickProductContentList(nickArr);
				} else if (config.biz === bizObj.列宽设置) {
					if (config.value) {
						try {
							let cValue = JSON.parse(config.value);
							if (typeof cValue == 'object') {
								setInitMap(cValue);
							}
						} catch (error) {
							console.log(error);
						}
					}
				} else if (config.biz === bizObj.列显示设置) {
					if (config.value) {
						try {
							let cValue = JSON.parse(config.value);

							// 遍历对象数组，查找并替换字段
							cValue.forEach(item => {
								if (item.name === "产品内容") {
									item.name = "商品信息";
								}
								if (item.name === "售后标签") {
									item.name = "售后异常";
								}
							});

							if (Array.isArray(cValue)) {
								console.log('%c [ 接口返回的列配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', cValue);
								cValue = getFilterColumnConfig(cValue); // 从后端拿的数据，需要根据角色过滤下
								setInitList(cValue);
							}
						} catch (error) {
							console.log(error);
						}
					}
				} else {
					advanceSettingConfig[config.biz] = config.value;
					if (config.biz === bizObj.定制查询条件 && config.value) {
						try {
							list = JSON.parse(config.value);

							// 修改下tid 名称，这段代码不能删除
							let tid = list?.find(item => item.condition == 'tid');
							if (tid) {
								tid.conditionName = '系统单号';
							}

							// 字段修改了名称
							let sysItemInclude = list?.find(item => item.condition == 'sysItemInclude');
							if (sysItemInclude) {
								sysItemInclude.conditionName = '商品包含';
							}

							// 配置错了这里修改下，再重新保存
							let ptTid = list?.find(item => item?.condition == 'goodsStatus');
							if (ptTid?.id && ptTid?.id !== refundSearchConditionDefaultIds.货物状态) {
								ptTid.id = refundSearchConditionDefaultIds.货物状态;
								// ptTid.sort = 24;
							}
						// console.log('%c [ 售后订单自定义查询条件配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', list);
						} catch (error) {
							console.log(error);
						}
					}
				}
			});
		} catch (error) {
			console.log(error);
		}

		const defaultList = cloneDeep(refundSearchConditionDefaultConfig);
		if (!list?.length) {
			list = defaultList.sort((a, b) => a.sort - b.sort); // 默认排序
		}
		// 禁止点击
		list.forEach(i => {
			i.disabled = disabledConditions.includes(i.condition);
		});
		// 对比本地看是否有未加配置 有就加上
		// const noItemList = defaultList.filter((item) => (list.findIndex(_listItem => (_listItem.condition == item.condition)) < 0));
		// console.log('%c [ 本地新增的配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', noItemList);
		// if (noItemList.length > 0) {
		// 	list.push(...noItemList);
		// }
		// 本地配置没有的去掉
		list = list.filter(objB => {
			return defaultList.some(objA => objA.condition === objB.condition);
		});
		const noItemList = [];
		// 重新排序
		list = defaultList?.map((item, index) => {
			let defaultItem = list?.find(d => d.condition == item.condition);
			if (defaultItem) {
				return {
					...defaultItem,
					conditionName: item.conditionName, // 使用本地的名称
				};
			} else {
				noItemList.push(item);
			}
		}).filter(Boolean).sort((a, b) => a.sort - b.sort);

		if (noItemList.length > 0) {
			noItemList.forEach((item, index) => {
				item.sort = list.length + index + 1;
			});
			list.push(...noItemList);
		}

		// 条件限制的去掉
		// 分销商角色: 自发/代发售后单、选择供应商
		// 供应商角色：选择分销商
		if (isDistributor) {
			list = list.filter(item => {
				return !['scmDistributorUserId'].includes(item.condition);
			});
		} else if (isSupplier) {
			list = list.filter(item => {
				return !['scmRefundType', 'scmSupplierUserIdList', 'platformInfo', 'isNoTradeMess', 'exchangeRefundStatusList', 'distributorIsPush'].includes(item.condition);
			});
		}

		console.log('%c [ 自定义查询条件配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', list);
		setAdvanceSettingConfig(advanceSettingConfig);
		setConditionList(list);
		setTimeout(() => {
			ref?.current?.submit();
		});
	};

	const handleFormItemList = () => {
		const formFieldList = [];
		conditionList.forEach(condition => {
			if (condition.selected || showAllCondition) {
				const formItem = getFormItemByFormName(condition.condition);
				if (formItem?.length) {
					formFieldList.push(...formItem);
				}
			}
		});
		console.log('formFieldListformFieldList', formFieldList);

		setFormFieldList(formFieldList);
	};

	/** ---------------------------------------------- */

	// 回显售后工单状态表单选项
	const getRefundSystemStatusInitialValue = () => {
		const hideItems = advanceSettingConfig[bizObj.隐藏显示设置];
		let initialValues = [REFUND_SYSTEM_STATUS.待处理, REFUND_SYSTEM_STATUS.处理中, REFUND_SYSTEM_STATUS.已关闭, REFUND_SYSTEM_STATUS.已完成];
		if (hideItems) {
			if (hideItems.includes("1")) {
				initialValues = initialValues.filter(i => i !== REFUND_SYSTEM_STATUS.已关闭);
			}
			if (hideItems.includes("2")) {
				initialValues = initialValues.filter(i => i !== REFUND_SYSTEM_STATUS.已完成);
			}
			return initialValues;
		}
		return [];
	};

	useEffect(() => {
		const refundSystemStatusList = getRefundSystemStatusInitialValue();
		form.setFieldsValue({
			refundSystemStatusList,
			...formOptionDefault
		});
		setFormData((prev) => {
			return {
				...prev,
				refundSystemStatusList,
				...formOptionDefault
			};
		});
	}, [advanceSettingConfig, formOptionDefault]);

	useEffect(() => {
		handleFormItemList();
	}, [conditionList, showAllCondition, itemInfoQueryIncludeMaxNum, itemInfoQueryIncludePlaceholder, isDistributor, isSupplier, formOptionDefault, formData]);

	useEffect(() => {
		return () => {
			stopCreateExchangeTradeProgress();
		};
	}, []);

	const onResizeChange = (v) => {
		setInitMap(v);
		UpdateRefundGlobalConfigApi([{
			biz: bizObj.列宽设置,
			value: JSON.stringify(v)
		}]);
	};

	const onSortChange = (v) => {
		setInitList(v);
		UpdateRefundGlobalConfigApi([{
			biz: bizObj.列显示设置,
			value: JSON.stringify(v)
		}]);
	};

	const onSortReset = (width, list) => {
		onResizeChange(width);
		onSortChange(list);
	};

	const moreActionNodeFn = (record) => (
		<Popover zIndex={ 900 } overlayClassName="after-sale-popover" placement="bottom" content={ getMoreAction(record) } trigger="click">
			<div className={ cs('r-pointer', 'r-fc-F5821F', 'r-mb-10') }>更多</div>
		</Popover>
	);

	const expandableFixed = useMemo(() => {
		let item = initList?.find(i => i.colFixed == 'right');
		return item?.isfixed ?? true;
	}, [initList]);


	// 快速勾选点击
	const moreChoiceOper = (type: string) => {
		console.log(type);

		let quickSelectedRows:any[] = [];
		switch (type) {
			case '全选':
				quickSelectedRows = dataSource?.map(item => item);
				break;
			case '反选':
				quickSelectedRows = dataSource?.filter(item => !selectedRowKeys?.includes(item.rowId));
				break;
			case '取消勾选':
				quickSelectedRows = [];
				break;
			default:
				break;
		}

		setSelectedRowKeys(quickSelectedRows?.map(item => item.rowId) || []);
		setSelectedRows(quickSelectedRows);
	};

	// 分页栏快速勾选
	const getSelectedContent = useCallback(() => {
		let count = selectedRowKeys?.length || 0;
		let refundAmountCount = 0;
		dataSource?.forEach(row => {
			if (selectedRowKeys?.includes(row.rowId) && row.refundAmount) {
				refundAmountCount += (Number?.(row.refundAmount) || 0);
			}
		});
		return (
			<div className={ s.selectedContent }>
				<div className={ s.selectedContentQuickCheck }>
					<QuickCheckRows onTabClick={ (type) => { moreChoiceOper(type); } } />

					<div className={ s.selectedCount }>
						<Icon style={ { fontSize: 24, color: '#FAAD14' } } type="tishi-fill" />

						<div>本页已勾选 <span className="r-c-FF4D4F">{count}</span> 条数据，共计 <span className="r-c-FF4D4F">{refundAmountCount.toFixed(2)}</span> 元</div>
					</div>
				</div>
			</div>
		);
	}, [selectedRowKeys, dataSource]);

	const _onChange = (pagination, filters, sorter, { action }) => {
		// 切换分页需要重置勾选项
		if (action == 'paginate') {
			setSelectedRows([]);
			setSelectedRowKeys([]);
		}
	};

	// 处理预设查询条件信息回填 isSubmit: 是否开启查询 v: ruleContent
	const handlePreConditionInfo = (data:any, isSubmit = true) => {
		console.log(data);
		const initValue = initPresetData();
		let newData = {
			...initValue,
			...data,
		};

		if (newData.logisticsStatusEnum && newData.logisticsStatusEnum?.includes(ExpressStatusEnum.无物流状态)) {
			let logisticsStatusEnum = [];
			newData.logisticsStatusEnum?.forEach(i => {
				if (i === ExpressStatusEnum.无物流状态) {
					logisticsStatusEnum.push(ExpressStatusEnum.无快递单号, ExpressStatusEnum["无物流状态(有单号)"]);
				} else {
					logisticsStatusEnum.push(i);
				}
			});
			newData.logisticsStatusEnum = logisticsStatusEnum;
		}

		console.log('%c [ newData ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', newData, isSubmit);
		handlePresetReset();

		// 这里不需要展开

		setTimeout(() => {
			setFormData(newData);

			form.setFieldsValue(newData);

			if (isSubmit) {
				sendPoint(Pointer.售后_售后订单_预设查询条件使用);
				setTimeout(() => {
					// form.submit(); // 查询会合并当前激活的预设查询条件
					ref?.current?.submit();
				}, 200);
			}
		}, 100);
	};

	// 初始化预设查询条件参数， 和快捷筛选的区分开，保留的参数不一样
	const initPresetData = () => {
		const searchParams = form.getFieldsValue();
		let initValue:any = {
			...searchFormInitValue,
			...formOptionDefault,
			rangeTime: searchParams?.rangeTime || initialRangeTime,
		};

		console.log('%c [ 重置参数 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', initValue);
		return initValue;
	};

	// 取消预设查询条件查询，初始化搜索栏
	const handlePresetReset = () => {
		form.resetFields(); // 售后这里不用这个, 因为没有固定初始化的参数

		setActiveTabItem(""); // 清除快捷筛选

		setTimeout(() => {
			let initValue = initPresetData();
			form.setFieldsValue(initValue); // 预设不重置时间
			setFormData(initValue);
		}, 0);
	};

	// 预设查询条件
	const presetConditionContent = (
		<div>
			<PresetQueryNavBarCommon
				isSearching={ pageLoading }
				handlePreConditionInfo={ handlePreConditionInfo }
				conditionSet={ conditionList }
				handlePresetReset={ handlePresetReset }
				form={ form }
				ref={ presetRef }
				type={ isSupplier ? BizTypeEnum.分销商的售后单查询 : BizTypeEnum.售后单查询 }
			/>
		</div>
	);

	// 保存预设查询条件
	const savePresetQueryCondition = () => {
		sendPoint(Pointer.售后_售后订单_保存为预设条件);
		presetRef?.current?.savePreCondition();
	};

	// 保存为预设条件按钮
	const savePresetConditionContent = (
		<Button
			className="r-ml-8"
			size="small"
			onClick={ () => savePresetQueryCondition() }
		>
			保存为预设条件
		</Button>
	);


	useEffect(() => {
		if (isDistributor) {
			getSelectNeedReminderSyncApi();
		}

		return () => {
			// 关闭烦人的无限弹框
			needReminderSyncModalRef.current?.destroy?.();
			needReminderSyncModalRef.current = null;
		};
	}, [isDistributor]);

	return (
		<NormalLayout className="print-batch-search-con">
			<SearchTable
				pageSizeId="tradeListTable"
				ref={ ref }
				form={ form }
				showSearchToggle
				tableExtra={ tableExtra }
				autoSearch={ false }
				fetchData={ getProductListApi }
				searchBtnPoint={ Pointer['售后_售后订单_查询'] }
				responseAdapter={ responseAdapter }
				customConditionContent={ customConditionContent }
				savePresetConditionContent={ savePresetConditionContent } // 保存为预设条件
				onReset={ onReset }
				loadingChange={ (loading) => {
					setLoading(loading);
				} }
				rowFormConfig={ {
					formList: invariantConditionList.concat(formFieldList),
					style: {
						background: '#ffffff',
						padding: '16px'
					},
					// expandNode,
					rowProps: {}, // 表单行配置
					colProps: {}, // 表单列配置
					formItemProps: {},
					expandNode: presetConditionContent, // 预设查询条件
				} }
				baseTableConfig={ {
					dataSource,
					tableExtraFn,
					headerColSet: {
						inPlace: ColSetPlaceEnum.表格上方,
						resizeId: `AfterSale_width_${userStore?.userInfo?.userId}_${userStore?.userInfo?.subUserId}`,
						sortId: `AfterSale_sort_${userStore?.userInfo?.userId}_${userStore?.userInfo?.subUserId}`,
						onResizeChange,
						onSortChange,
						onReset: onSortReset,
						initMap,
						initList,
						useDrawer: true,
					},
					rowKey: 'rowId',
					// groupId: 'groupId',
					onFieldsChange,
					columns,
					expandContext,
					expandContextStyle: {
						marginBottom: '0px',
						padding: '0 16px 8px'
					},
					// hasVt: false,
					cachePgination: true,
					pagination: {
						pageSizeOptions: [10, 20, 50, 100, 200, 500, 1000],
					},
					showSelectedCount: getSelectedContent(),
					enableRowClick: true, // 是否开启行点击选中，其他事件需要阻止冒泡
					rowSelection: {
						type: 'checkbox',

						...rowSelection,
						...{
							selectedRowKeys
						}
					},
					expandable: {
						colFixed: 'right',
						fixed: expandableFixed,
						expandedRowRender: (record) => {
							return (
								<ExpandTable dataSource={ record['refundItemRecordInfos'] } warehouseStatus={ warehouseStatus } trade={ record } afterOK={ refreshPage } />
							);
						},
						expandIcon: ({ expanded, onExpand, record }) => {
							const expandBtnNode = (
								expanded ? (
									<span
										className={ cs('r-pointer', 'r-fc-F5821F') }
										onClick={ e => { e.stopPropagation(); onExpand(record, e); onExpandRow(expanded, record); } }
									>收起
									</span>
								) : (
									<span
										className={ cs('r-pointer', 'r-fc-F5821F') }
										onClick={ e => {
											e.stopPropagation();
											if (record.refundSystemStatus == refundSystemStatusEnum.已关闭) {
												Modal.confirm({
													centered: true,
													title: '系统提示',
													okText: '我知道了',
													icon: <ExclamationCircleOutlined />,
													content: (
														<div className={ cs('r-c-error') }>售后工单已关闭时不展示售后工单详情</div>
													),
													cancelButtonProps: { hidden: true }
												});
												return;
											}
											onExpand(record, e);
											onExpandRow(expanded, record);
										} }
									>展开
									</span>
								)
							);
							return (
								<div onClick={ e => e.stopPropagation() }>
									{moreActionNodeFn(record)}
									{ record['refundItemRecordInfos']?.length ? expandBtnNode : null}
								</div>
							);
						},
						rowExpandable: (record) => record['exceptionType'] !== 1
					},
					rowClassName: (record) => {
						if (selectedRowKeys.includes(record.rowId)) {
							return 'r-row-checked';
						}
						return 'r-row-default';
					}
				} }
				onChange={ ({ pageNo, pageSize }) => {
					_onChange({ current: pageNo, pageSize }, undefined, undefined, { action: 'paginate' });
				} }
			/>
			{/* 处理异常弹窗 */}
			<HandleExceptionModal
				visible={ exceptionModalVisible }
				afterOK={ refreshPage }
				onClickEdit={ () => {
					setEditModalVisible(true);
					setEditModalType('edit');
				} }
				dataSource={ curActionRowData }
				onCancel={ () => { setExceptionModalVisible(false); } }
			/>

			{/* 批量拒绝弹框 */}
			<BatchRefuseMoney
				isDistributor={ isDistributor }
				onShowEditModal={ (id) => {
					setEditModalVisible(true);
					setEditModalType('show');
					setCurActionRowData(dataSource?.find(item => item.id === id));
				} }
			/>

			{/* 编辑弹窗 */}
			<EditModal
				type={ editModalType }
				afterOK={ refreshPage }
				visible={ editModalVisible }
				dataSource={ curActionRowData }
				onCancel={ () => { setEditModalVisible(false); } }
			/>
			{/* 同步售后订单 */}
			<SyncModal
				visible={ syncModalVisible }
				onCancel={ () => { setSyncModalVisible(false); } }
				afterOK={ refreshPage }
			/>
			{/* 拒绝退款/退货 */}
			<RefuseRefundModal
				visible={ refuseRefundModalVisible }
				dataSource={ curActionRowData }
				afterOK={ refreshPage }
				onClose={ () => { setRefuseRefundModalVisible(false); } }
			/>

			<BatchModifyLocalMemoPicModal ref={ batchModifyLocalMemoPicModalRef } />
			{/* 创建售后订单 */}
			{
				createTradeModalVisible && (
					<CreateOrderModal
						onCancel={ (createNoInfoOrder) => {
							setCreateTradeModalVisible(false);
							if (createNoInfoOrder) {
								setCreateNoInfoOrderVisible(true);
							}
						} }
						onSuccess={ () => {
							setCreateTradeModalVisible(false);
							refreshPage();
						} }
					/>
				)
			}


			{/* 创建无主售后单 */}
			{
				createNoInfoOrderVisible && (
					<CreateNoInfoOrderModal
						onCancel={ () => setCreateNoInfoOrderVisible(false) }
						onSuccess={ () => {
							setCreateNoInfoOrderVisible(false);
							refreshPage();
						} }
					/>

				)

			}
			{/* 设置自动备注 */}
			{autoMemoModalVisible && (
				<AutoMemoModal
					onOk={ () => setAutoMemoModalVisible(false) }
					onCancel={ () => setAutoMemoModalVisible(false) }
				/>
			)}

			{/* 绑定无主件 */}
			{
				bindOrderModalVisible && (
					<BindTradeModal
						refundList={ selectedRows }
						onCancel={ () => setBindOrderModalVisible(false) }
						onSuccess={ () => {
							setBindOrderModalVisible(false);
							refreshPage();
						} }
					/>
				)
			}

			{/* 卖家备注 */}
			{isShowBatchModifyMemoModal ? (
				<BatchModifyMemoModal
					onOk={ () => { setIsShowBatchModifyMemoModal(false); refreshPage(); } }
				/>
			) : ''}

			{/* 线下备注 */}
			<OfflineMemoModal onOk={ handleOfflineMemoOk } onCancel={ handleOfflineMemoCancel } data={ offlineMemoData } fromPage={ EnumFromPage.售后管理 } />

			{/* 高级设置 */}
			<AdvancedSettingModal
				visible={ advancedSettingModalVisible }
				onCancel={ () => setAdvancedSettingModalVisible(false) }
				updateConfig={ updateAdvanceConfig }
				isDistributor={ isDistributor }
				isShowZeroStockVersion={ isShowZeroStockVersion }
			/>

			{/* 京东拆包登记 */}
			<UnpackRegisterModal
				selectedRows={ selectedRows }
				// registerParamList={ registerParamList }
				visible={ unpackRegisterModalVisible }
				onCancel={ () => setUnpackRegisterModalVisible(false) }
				afterOK={ refreshPage }
			/>
			<BatchReturnMoney />

			{/* 售后日志记录 */}
			{
				optLogModalVisible && <OptLogModal refundId={ optLogRefundId } onCancel={ () => setOptLogModalVisible(false) } />
			}

			{
				batchExportModalVisible && <BatchExportModal onCancel={ onBatchExportCancel } onOk={ onBatchExportOk } isDistributor={ isDistributor } />
			}

			{batchHandleResModalVisible
				&& (
					<BatchHandleResModal
						title={ batchHandleResTitle }
						onCancel={ () => setBatchHandleResModalVisible(false) }
						onOk={ (successNum) => {
							setBatchHandleResModalVisible(false);
							if (successNum) {
								refreshPage();
							}
						} }
						batchOptRes={ batchOptRes }
					/>
				)}

			{/* 自定义查询条件设置：去掉平台极速退款、拦截状态 */}
			{customSearchConditionModalVisible && (
				<CustomSearchCondition
					searchConditions={ conditionList.filter(item => !['speedRefundType', 'isIntercept'].includes(item.condition)) }
					onOk={ customSearchConditionOnOk }
					onCancel={ customSearchConditionOnCancel }
					isSupplier={ isSupplier }
					isDistributor={ isDistributor }
				/>
			)}

			{
				markProcessingStatusModalVisible && (
					<MarkProcessingStatusModal
						onCancel={ markProcessingStatusModalOnCancel }
						onOk={ markProcessingStatusModalOnOk }
					/>
				)
			}

			<Modal
				title="换货/补发手工单创建"
				centered
				visible={ !!modalProgress.progress && modalProgress.progressVisibility }
				onOk={ onModalProgressCancel }
				destroyOnClose
				okText="确定"
				closable={ false }
				cancelButtonProps={ { hidden: true } }
			>
				<div className="r-flex r-jc-c">
					<Progress type="circle" percent={ modalProgress?.progress } />
				</div>
			</Modal>

			<Modal
				centered
				title="换货/补发手工单创建"
				visible={ modalProgress.progressResultVisibility }
				onOk={ () => { resetModalProgress(); } }
				width={ 680 }
				destroyOnClose
				okText="我知道了"
				closable={ false }
				cancelButtonProps={ { hidden: true } }
			>
				<div>
					<div className="r-flex r-jc-c r-mb-20">
						<div>成功：<span className="r-fs-20 r-c-success">{modalProgress.data?.successNum || 0}</span> <span className="r-fs-20">笔</span></div>
						<div className="r-ml-20">失败：<span className="r-fs-20 r-c-error">{modalProgress.data?.failedNum || 0}</span> <span className="r-fs-20">笔</span></div>
					</div >

					{
						modalProgress.data?.errorResultCollectList?.length > 0 && (
							<>
								<Table
									rowKey="operationId"
									columns={ progressResultColumns }
									dataSource={ modalProgress.data?.errorResultCollectList }
									pagination={ false }
									size="small"
									scroll={ { y: 290 } }
								/>

								<Button onClick={ batchCopyProgressResult } type="link">复制失败的售后单号</Button>
							</>
						)
					}

				</div>
			</Modal>

			<AbnormalSubUserModal />
		</NormalLayout>
	);
};
export default observer(TradeList);
