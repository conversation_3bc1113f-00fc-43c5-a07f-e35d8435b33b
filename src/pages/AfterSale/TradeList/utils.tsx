import React from "react";
import _, { cloneDeep } from "lodash";
import { RcFile } from "antd/es/upload/interface";
import { Modal, Tooltip } from "antd";
import userStore from "@/stores/user";
import { PLAT_FXG, PLAT_HAND, PLAT_JD, PLAT_KS, PLAT_MAP, PLAT_OTHER, PLAT_PDD, PLAT_SPH, PLAT_TB, PLAT_XHS } from "@/constants";
import { dealPlatAsHandPlat, isSourceHand } from "@/components-biz/ShopListSelect/shopListUtils";
import { platform } from "@/types/schemas/common";
import { REFUND_SYSTEM_STATUS, AftersaleGlobalConfig } from "./constants";

export enum afterSaleTypeText {
	仅退款=1,
	退货退款=2,
	换货=3,
	补差价=4,
	补发货品=5
}

export enum afterRefundStatusEnum {
	等待卖家同意 = 1,
	等待买家退货 = 2,
	等待卖家确认收货 = 3,
	退款成功 = 4,
	退款关闭 = 5,
	卖家拒绝退款 = 6,
	待买家修改 = 7,
	待发出换货商品 = 8,
	换货补寄待收货 = 9,
	其他 = 10
}

export enum refundSystemStatusEnum {
	待处理 = 4,
	已关闭 = 5,
	处理中 = 6,
	已完成 = 7,
}

export enum afterSaleChannelText {
	线上同步=1,
	订单号搜索=2,
	手工录入=3
}

export enum refundStageText {
	未完成=1,
	已完成,
	已关闭
}

// 是否提交供应商
export enum distributorIsPushEnum {
	未提交=0,
	已提交=1,
}

// 供应商确认状态
export enum supplierIsConfirmEnum {
	未确认=0,
	已确认=1,
}

export enum exchangeStatusEnum {
	'未创建' = 0,
	'已创建' = 1,
	'已发货' = 2,
	'已删除' = 3,
	'部分发货' = 4
}

// 售后订单状态
export enum refundItemTypeEnum {
	退款 = 0,
	退货 = 1,
	换货 = 2,
	补发 = 3,
}

// 售后订单获取字典
export const bizEnumObject = (isDistributor:boolean) => {
	let bizEnumList = {
		"列宽设置": AftersaleGlobalConfig.售后订单列宽设置,
		"列显示设置": AftersaleGlobalConfig.售后订单列显示设置,
		"定制查询条件": AftersaleGlobalConfig.售后订单定制查询条件,
		"隐藏显示设置": AftersaleGlobalConfig.售后订单隐藏显示设置,
		"产品内容设置": AftersaleGlobalConfig.售后订单产品内容设置,
		'售后买家昵称信息设置':AftersaleGlobalConfig.售后买家昵称信息设置,
		"自动审核": AftersaleGlobalConfig.售后工单设置,
		"自动关闭": AftersaleGlobalConfig.售后工单自动关闭,
		'赋值卖家备注':AftersaleGlobalConfig.创建换货补发订单时同步售后卖家备注设置,
		'赋值线下备注':AftersaleGlobalConfig.创建换货补发订单时同步售后线下备注设置,
		"自动标记处理中": AftersaleGlobalConfig.自动标记处理中,
		"自动标记已完成": AftersaleGlobalConfig.自动标记已完成,
		"换货补发订单发货后自动上传": AftersaleGlobalConfig.换货补发订单发货后自动上传,
		"售后订单快捷筛选设置": AftersaleGlobalConfig.售后订单快捷筛选设置,
		"销退在途设置": AftersaleGlobalConfig.售后订单高级设置销退在途设置,
	};
	if (!isDistributor) {
		bizEnumList = {
			...bizEnumList,
			"列宽设置": AftersaleGlobalConfig.分销商的售后单列宽设置,
			"列显示设置": AftersaleGlobalConfig.分销商的售后单列显示设置,
			"定制查询条件": AftersaleGlobalConfig.分销商的售后单定制查询条件,
			"隐藏显示设置": AftersaleGlobalConfig.分销商的售后单隐藏显示设置,
			"自动审核": AftersaleGlobalConfig.分销商的售后单自动审核,
			"自动关闭": AftersaleGlobalConfig.分销商的售后单售后工单自动关闭,
			"自动标记处理中": AftersaleGlobalConfig.分销商的售后单自动标记处理中,
			"自动标记已完成": AftersaleGlobalConfig.分销商的售后单自动标记已完成,
			"换货补发订单发货后自动上传": AftersaleGlobalConfig.分销商的售后单换货补发订单发货后自动上传,
			"售后订单快捷筛选设置": AftersaleGlobalConfig.分销商的售后订单快捷筛选设置,
		};
	}
	return bizEnumList;
};


// 判断退货退款按钮是否展示
// 目前已对接平台 淘宝
export const AGREE_PLAT = [PLAT_TB, PLAT_FXG, PLAT_PDD, PLAT_SPH, PLAT_KS, PLAT_XHS, PLAT_JD];

export const changeGoodsPlat = (platform: platform) => {
	return [PLAT_PDD, PLAT_FXG, PLAT_TB, PLAT_XHS, PLAT_KS, PLAT_JD, PLAT_SPH].includes(platform);
};
// 拼多多不展示拒绝退款、拒绝退货按钮；退货退款类型，显示同意退款按钮
export const checkRefundAction = (row:any) => {
	const actionStatus = {
		returnMoney: false,
		returnGoods: false,
		refuseMoney: false,
		refuseGoods: false,
		acceptReplacing: false, // 同意换货
		refuseReplacing: false, // 拒绝换货
		writeExpressNo: false, // 填写换货/补发单号
		acceptReissue: false, // 同意补寄
		refuseReissue: false, // 拒绝补寄
		refuseReplacingAgain: false, // 售后退货拒绝
		createExchangeOrder: false, // 生成换货订单
		createReissueOrder: false, // 生成补发订单
		unpackRegister: false, // 拆包登记
	};

	const {
		afterSaleChannel, // 售后渠道，1 线上同步, 2 手工录入, 3 手工录入+无关联订单
		afterSaleType, // 售后类型
		reviewStatus, // 审核状态
		refundStatus, // 售后平台状态
		platform,
		refundSystemStatus,
		refundStatusDesc,
		exchangeTid,
		refundTagList
	} = row;
	
	// 判断是否为京喜订单
	const hasJingxi = refundTagList?.includes('jxTrade');
	
	// 审核通过或在对接平台里才有接下来的操作
	if (!reviewStatus) {
		return actionStatus;
	}
	
	if (!AGREE_PLAT.includes(platform)) {
		return actionStatus;
	}
	if (afterSaleChannel != afterSaleChannelText.线上同步) {
		return actionStatus;
	}

	// 仅退款
	if (afterSaleType === afterSaleTypeText.仅退款) {
		// 未完成且待买家同意
		if (refundStatus === afterRefundStatusEnum.等待卖家同意) {
			actionStatus.returnMoney = true;
			if (platform !== PLAT_PDD) actionStatus.refuseMoney = true;
		}
	}
	// 退货退款
	if (afterSaleType === afterSaleTypeText.退货退款) {
		// 未完成且待买家同意
		if (refundStatus === afterRefundStatusEnum.等待卖家同意) {
			actionStatus.returnGoods = true;
			if ([PLAT_PDD, PLAT_FXG].includes(platform)) {
				actionStatus.returnMoney = true;
			} else {
				actionStatus.refuseGoods = true;
			}
		}
		// 未完成且等待买家退货、待卖家确认收货
		if ([afterRefundStatusEnum.等待买家退货, afterRefundStatusEnum.等待卖家确认收货].includes(refundStatus)) {
			actionStatus.returnMoney = true;
			if (platform === PLAT_JD && refundStatusDesc !== "待处理") actionStatus.unpackRegister = true;
			if (platform !== PLAT_PDD) actionStatus.refuseMoney = true;
		}
	}
	/**
	 * 同意补寄按钮展现逻辑：审核通过&&待卖家同意
	 */
	if (afterSaleType === afterSaleTypeText.补发货品 && [PLAT_PDD, PLAT_FXG].includes(platform)) {
		if (reviewStatus && refundStatus === afterRefundStatusEnum.等待卖家同意) {
			actionStatus.acceptReissue = true;
			actionStatus.refuseReissue = true;
		}
	}

	/**
	 * 同意换货按钮展现逻辑：审核通过&&待卖家同意
	 */
	if (afterSaleType === afterSaleTypeText.换货 && changeGoodsPlat(platform)) {
		if (reviewStatus && [afterRefundStatusEnum.等待卖家同意].includes(refundStatus)) {
			actionStatus.acceptReplacing = true;
			actionStatus.refuseReplacing = true;
		}
		if ([afterRefundStatusEnum.等待买家退货, afterRefundStatusEnum.等待卖家确认收货].includes(refundStatus)) {
			if (platform === PLAT_JD && refundStatusDesc !== "待处理") actionStatus.unpackRegister = true;
		}
	}
	/**
	 * 填写换货/补发单号按钮展现逻辑：换货订单&&审核通过&&(等待买家退货||等待卖家确认收货 || 待发出换货商品)
	 */
	if ([afterSaleTypeText.换货, afterSaleTypeText.补发货品].includes(afterSaleType) && changeGoodsPlat(platform)) {
		if (reviewStatus
			&& [afterRefundStatusEnum.等待买家退货, afterRefundStatusEnum.等待卖家确认收货, afterRefundStatusEnum.待发出换货商品].includes(refundStatus)) {
			actionStatus.writeExpressNo = true;
			actionStatus.refuseReplacingAgain = true;
		}
	}
	const isFinished = refundSystemStatus == REFUND_SYSTEM_STATUS.已完成;

	// 生成换货订单按钮展现逻辑：不是已完成状态 && 换货订单 && （审核通过 && 已同意换货（） || 已生成换货订单（exchangeTid））
	if (platform !== PLAT_JD && !isFinished && afterSaleTypeText.换货 == afterSaleType && ((reviewStatus
		&& [afterRefundStatusEnum.等待买家退货, afterRefundStatusEnum.等待卖家确认收货, afterRefundStatusEnum.待发出换货商品].includes(refundStatus)) || row.exchangeTid)) {
		actionStatus.createExchangeOrder = true;
	}
	// 生成换货订单按钮展现逻辑：不是已完成状态 && 补发订单 && （审核通过 && 已同意换货（） || 已生成换货订单（exchangeTid））
	if (!isFinished && afterSaleTypeText.补发货品 == afterSaleType && ((reviewStatus
		&& [afterRefundStatusEnum.等待买家退货, afterRefundStatusEnum.等待卖家确认收货, afterRefundStatusEnum.待发出换货商品].includes(refundStatus)) || row.exchangeTid)) {
		actionStatus.createReissueOrder = true;
	}

	// 京喜订单不显示同意退款、同意退货、拒绝退款按钮
	if (hasJingxi) {
        actionStatus.returnMoney = false;
        actionStatus.returnGoods = false;
        actionStatus.refuseMoney = false;
    }
	return actionStatus;
};
export const platsName = _.cloneDeep(PLAT_MAP);

export const refundItemTypeNames = [
	{ value: 0, name: '退款' },
	{ value: 1, name: '退货' }
];

export const refundSystemTypeNames = [
	{ value: 1, name: '仅退款' },
	{ value: 2, name: '拒收退货' },
	{ value: 3, name: '退货退款' },
	{ value: 4, name: '换货' },
	{ value: 5, name: '补发' }
];

export const afterSaleTypeNames = [
	{ value: 1, name: '仅退款' },
	{ value: 2, name: '退货退款' },
	{ value: 3, name: '换货' },
	// { value: 4, name: '补差价' },
	{ value: 5, name: '补发' }

];

export const afterSaleReason = [
	{ value: 1, name: '7天无理由退货' },
	{ value: 2, name: '多拍/拍错/不想要' },
	{ value: 3, name: '效果不好/不喜欢' },
	{ value: 4, name: '商品破损/污渍' },
	{ value: 5, name: '商品少发/漏发/丢件' },
	{ value: 6, name: '尺码不合适' },
	{ value: 7, name: '大小尺寸与商品描述不符' },
	{ value: 8, name: '商品质量问题' },
	{ value: 9, name: '做工粗糙/有瑕疵' },
	{ value: 10, name: '卖家发错货' },
	{ value: 11, name: '快递/物流一直未送到' },
	{ value: 12, name: '与商家协商一致退款' },
	{ value: 14, name: '假冒品牌' },
	{ value: 13, name: '退运费' },
	{ value: 50, name: '其他' }
];


export const refundReasonNames = [
	{ value: '7天无理由退款', name: '7天无理由退款' },
	{ value: '不想要了', name: '不想要了' },
	{ value: '尺码/颜色不符', name: '尺码/颜色不符' },
	{ value: '效果不好/不喜欢', name: '效果不好/不喜欢' },
	{ value: '商品破损/污渍', name: '商品破损/污渍' },
	{ value: '商品质量问题', name: '商品质量问题' },
	{ value: '做工粗糙/有瑕疵', name: '做工粗糙/有瑕疵' },
	{ value: '商品少发/漏发/丢件', name: '商品少发/漏发/丢件' },
	{ value: '卖家发错货', name: '卖家发错货' },
];

// 平台交易详情链接
const platformTradeLink = {
	pdd: {
		trade: 'https://mms.pinduoduo.com/orders/detail?type=0&sn=',
		refund: 'https://mms.pinduoduo.com/aftersales-ssr/detail?id=',
		item: 'https://mobile.yangkeduo.com/goods.html?goods_id='
	},
	tb: {
		trade: 'https://trade.taobao.com/trade/detail/trade_order_detail.htm?biz_order_id=',
		refund: 'https://refund2.taobao.com/dispute/detail.htm?disputeId=',
		item: 'https://item.taobao.com/item.htm?id='
	},
	tm: {
		trade: 'https://trade.taobao.com/trade/detail/trade_order_detail.htm?biz_order_id=',
		refund: 'https://refund2.taobao.com/dispute/detail.htm?disputeId=',
		item: 'https://detail.tmall.com/item.htm?id='
	},
	fxg: {
		trade: 'https://fxg.jinritemai.com/ffa/morder/order/detail?id=',
		refund: 'https://fxg.jinritemai.com/ffa/morder/aftersale/detail-v2?aftersale_id=',
		item: 'https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id='
	},
	ali: {
		trade: 'https://trade.1688.com/order/new_step_order_detail.htm?orderId=',
		refund: 'https://dispute.1688.com/refund/assureRefundDetail.htm?refundId=',
		item: 'https://detail.1688.com/offer/'
	},
	ksxd: {
		trade: 'https://s.kwaixiaodian.com/zone-origin/order/detail?id=',
		refund: 'https://s.kwaixiaodian.com/zone/refund/detail?refer=REFUND_LIST&refundId=',
		item: 'https://app.kwaixiaodian.com/merchant/shop/detail?id='
	},
	jd: {
		trade: 'https://neworder.shop.jd.com/order/orderDetail?orderId=',
		refund: 'https://afs.shop.jd.com/after/refundmentNew_list.action?refundmentApplyQuery.orderId=',
		item: 'https://item.jd.com/'
	},
	sph: {
		trade: 'https://store.weixin.qq.com/shop/order/detail?orderid=',
		refund: 'https://store.weixin.qq.com/shop/aftersale/detail?orderid=',
		item: 'https://store.weixin.qq.com/shop/order/detail?orderid='
	},
	xhs: {
		trade: 'https://ark.xiaohongshu.com/app-order/order/detail/',
		refund: 'https://ark.xiaohongshu.com/app-order/aftersale/detail?returnId=',
		item: 'https://www.xiaohongshu.com/goods-detail/'
	},
	c2m: {
		trade: 'https://trade.taobao.com/trade/detail/trade_order_detail.htm?biz_order_id=',
		refund: 'https://refund2.taobao.com/dispute/detail.htm?disputeId=',
		item: 'https://item.taobao.com/item.htm?id='
	},
	yz: {
		trade: 'https://www.youzan.com/v4/trade/order/detail?orderNo=',
		refund: 'https://www.youzan.com/v4/trade/refund/detail?orderNo=',
		item: 'https://detail.youzan.com/show/goods?alias='
	}
};

// 获取交易详情图片点击跳转各平台详情页的链接
export const getPlatformDetailLink = (platform:string, id:string) => {
	let curLink = platformTradeLink[platform];
	let curMain = curLink ? curLink.item : '';
	let curUrl = '';

	if (curLink && curMain && id) {
		if (platform === 'ali' || platform === 'jd') {
			curUrl = `${curMain}${id}.html`;
		} else {
			curUrl = `${curMain}${id}`;
		}
	}

	return curUrl;
};

export const getPlatformTradeLink = (platform:string, row:any): {
	trade?: string,
	refund?: string,
} => {
	if (platform === "hand") return {};
	let curLink = platformTradeLink[platform];
	if (!curLink) return {};
	let obj = {
		trade: `${curLink.trade}${deleteFxgTidStringA(row)}`,
		refund: `${curLink.refund}${row['refundId']}`
	};

	if (platform === PLAT_PDD) {
		obj.refund += `&orderSn=${row?.['ptTid'] || row['tid']}`;
	}
	return obj;
};

// 退款单处理后就不能在进行编辑操作（取消审核后）
export const getListRefundManageStatus = ({ refundItemRecordInfos = [] }) => {
	let hasEdit = true;
	refundItemRecordInfos.forEach(item => {
		let status = getRefundManageStatus(item);
		if (status !== '新创建') {
			hasEdit = false;
		}
	});

	return hasEdit;
};


/**
 * 计算扩展订单的处理状态
 * @param params applyRefundNum 申请退货数量 hasRefundNum 已收货数量 hasDisposeNum 已处理数量
 */
export const getRefundManageStatus = ({ applyRefundNum = 0, hasRefundNum = 0, hasDisposeNum = 0, refundItemType }, trade?:any) => {
	let status:number;
	const refundStatus = trade?.refundStatus;
	if (applyRefundNum > 0) {

		if (hasDisposeNum === 0) {

			// 新创建
			if (hasRefundNum === 0) {
				status = 0;
			}

			// 部分退货
			if (hasRefundNum > 0 && hasRefundNum < applyRefundNum) {
				status = 1;
			}

			// 全部退货
			if (hasRefundNum === applyRefundNum) {
				status = 2;
			}
		} else if (hasDisposeNum > 0) {

			// 部分处理
			if (hasDisposeNum < applyRefundNum && hasRefundNum > 0) {
				status = 3;
			}

			// 全部处理
			if (hasDisposeNum >= applyRefundNum) {
				status = 4;
			}
		}
		// 如果是换货补寄订单，并且订单状态是“退款成功”，需要返回新创建
		if (refundStatus === afterRefundStatusEnum.退款成功 && [RefundItemTypeEnum.换货, RefundItemTypeEnum.补发].includes(refundItemType)) {
			status = 0;
		}
	}
	if (applyRefundNum === 0) {
		status = 0;
		if (hasDisposeNum > 0) {
			status = 4;
		}
	}

	// * 换货类型
	if (trade?.exchangeTradeStatus && [RefundItemTypeEnum.换货, RefundItemTypeEnum.补发].includes(refundItemType)) {
		return (
			<>
				<div>
					{RefundSystemTypeEnum[trade.refundSystemType]}订单{exchangeStatusEnum[trade.exchangeTradeStatus]}
				</div>
				<div>
					系统单号:{trade.exchangeTid}
				</div>
			</>
		);
	} else {
		enum manageStatus {
			新创建 = 0,
			部分退货,
			全部退货,
			部分处理,
			全部处理,
		}

		return manageStatus[status];
	}

};

export const computeSubtractNum = (num1 = 0, num2 = 0) => {
	return Number(num1) - Number(num2);
};

export const getWarehouseVersion = async() => {
	// false 0库存或库存扣减未开启  true 正常库存版本
	let WarehouseVersion:boolean = false;
	const { version } = await userStore.getUserInfo();
	await userStore.getSystemSetting();
	const { inventoryDeduct } = userStore;
	if (version === 1 && inventoryDeduct === 1) {
		WarehouseVersion = true;
	}

	return WarehouseVersion;
};

// 权限不足提醒(单个退款、单个退款校验售后权限)
export const authModal = (err:any) => {
	if (!err) return false;
	
	const errorMessage = err?.message || err?.errorMessage || '';
	
	if(!errorMessage){
		return;
	}

	Modal.error({
		title: '系统提示',
		centered: true,
		width: 443,
		content: (
			<div>
				{errorMessage}
				<br />
				<a href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/mqo43kx9yvm0kpqq?singleDoc#" target="_blank" rel="noreferrer">点击查看设置教程</a>
			</div>
		),
	});
};

// 根据errorCode区分 （批量退款校验售后权限）
export const authModal2 = (err:any) => {
	console.log('%c [ error ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', err)

	if (!err) return false;
	
	const errorMessage = err?.message || err?.errorMessage || '';
	
	if(!errorMessage){
		return;
	}

	// 错误码分别是 5925、5926、5927
	Modal.error({
		title: '系统提示',
		centered: true,
		width: 443,
		content: (
			<div>
				{errorMessage}
				<br />
				{
					err?.errorCode == 5926 && (
						<a href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/mqo43kx9yvm0kpqq?singleDoc#" target="_blank" rel="noreferrer">点击查看设置教程</a>
					)
				}
			</div>
		),
	});
};

export const popErrorModal = (text: string) => {
	Modal.error({
		title: '系统提示',
		centered: true,
		width: 443,
		content: text,
	});
};

export const isPlatErr = (list) => {
	const isNoTradeMess = list?.some(i => i.isNoTradeMess); // 是否是无主售后单
	// const notAllowPlatform = list?.some(i => (dealPlatAsHandPlat(i.platform) || ['1688'].includes(i.platform) || isSourceHand(i))); // 是否是不支持的平台
	const notAllowPlatform = list?.some(i => (['1688', PLAT_HAND, PLAT_OTHER].includes(i.platform) || isSourceHand(i))); // 是否是不支持的平台
	return notAllowPlatform || isNoTradeMess;
};

export const popPlatErrModal = () => {
	popErrorModal('1688、手工售后单、无主售后单暂不支持批量退货退款处理，请取消勾选重新处理');
};

export const getBase64 = (img: RcFile, callback: (url: string) => void) => {
	const reader = new FileReader();
	reader.addEventListener('load', () => callback(reader.result as string));
	reader.readAsDataURL(img);
};

export const checkHasInputSid = (row?:any) => {
	let hasSid = false;
	// 天猫店铺 退货退款 无sid
	const { sid, platform, afterSaleType, tbSellerType } = row;
	if (AGREE_PLAT.includes(platform) && afterSaleType === 2 && tbSellerType === 1 && !sid) {
		hasSid = true;
	}
	return hasSid;
};

// 删除抖店订单号后缀A
export const deleteFxgTidStringA = (refund:any) => {
	const { ptTid = '', tid, platform } = refund;
	if (platform === 'fxg' && ptTid?.includes('A')) {
		// 去除A
		return ptTid.replace(/A$/, '');
	} else {
		return ptTid;
	}
};

export enum RefundItemTypeEnum {
	退款 = 0,
	退货,
	换货,
	补发,
}

export enum RefundSystemTypeEnum {
	仅退款 = 1,
	拒收退货= 2,
	退货退款= 3,
	换货= 4,
	补发 = 5,
}

export enum PackExpressStatusEnum {
	'WAIT_ACCEPT' = '待揽件',
	'ACCEPT' = '已揽收',
	'TRANSPORT' = '运输中',
	'ON_THE_WAY' = '在途中',
	'SEND_ON' = '转寄',
	'ARRIVE_CITY' = '到达目的城市',
	'DELIVERING' = '派件中',
	'STA_INBOUND' = '已放入快递柜或驿站',
	'AGENT_SIGN' = '已代收',
	'SIGN' = '已签收',
	'STA_SIGN' = '从快递柜或者驿站取出',
	'RETURN_SIGN' = '退签',
	'FAILED' = '包裹异常',
	'TIMEOUT_UNSIGEN' = '超时未签收',
	'REFUSE_SIGN' = '拒收',
	'DELIVER_ABNORMAL' = '派件异常',
	'CANCEL_ORDER' = '取消揽件',
	'STA_TIMEOUT_UNSIGEN' = '快递柜或者驿站超时未取',
	'CONTACT_FAIL' = '无法联系',
	'OVER_AREA' = '超区',
	'RETENTION' = '滞留',
	'ISSUE' = '问题件',
	'RETURN' = '退回',
	'SEND_NO_MESSAGE' = '发货无信息',
	'DAMAGE' = '破损',
}

export const isBeyondInput = (value:string = '', maxNum:number) => {
	value = value.replace(/，/g, ',');
	const len = value.split(',').length;
	return len > maxNum;
};

// 异常状态下的拦标志
export const getInterceptLock = (type:number, interceptInvestorStr: string, interceptStatusDesc:string) => {
	if (type != 1) {
		return null;
	}
	return (
		<Tooltip placement="bottom" title={ `由平台发起快递拦截，拦截的费用由${interceptInvestorStr || ""}出资，当前拦截状态：${interceptStatusDesc || ""}` }>
			<div style={ { gap: '5px 10px', marginLeft: '4px' } } className="r-fw-w batch_tbtlt_tradeLabel ">
				<span className="r-trade-refund">拦</span>
			</div>
		</Tooltip>
	);
};
