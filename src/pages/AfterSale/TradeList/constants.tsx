import React from "react";
import Pointer from "@/utils/pointTrack/constants";
import s from "./index.module.scss";
import { tradeStatus } from "@/pages/Trade/utils";
import { Tooltip } from "antd";

export const enum REFUND_SYSTEM_STATUS {
	待处理 = 4,
	处理中 = 6,
	已完成 = 7,
	已关闭 = 5
}

export const enum afterSaleOperateType {
	同意仅退款 = 'AGREE_ONLY_REFUND',
	同意退货 = 'AGREE_RETURN_GOODS',
	同意退货退款 = 'AGREE_RETURN_GOODS_REFUND',
	拆包登记 = 'UNPACK_REGISTER',
	拒绝仅退款 = 'REFUSE_ONLY_REFUND',
	拒绝退货 = 'REFUSE_RETURN_GOODS',
	拒绝退货退款 = 'REFUSE_RETURN_GOODS_REFUND',
	确认收货 = 'CONFIRM_GOODS',
	退货入库 = 'RETURN_STOCK'
}

export enum listItemName {
	线下备注 = "localContent"
}

// 添加更新类型枚举
export enum UpdateTypeEnum {
	都更新 = 0,         // 都更新
	线下备注 = 1,  // 线下备注
	线下备注图片 = 2 // 线下备注图片
  }

export const enum BtnEnum {
	'同意仅退款' = '同意仅退款',
	'同意退货' = '同意退货',
	'同意退货退款' = '同意退货退款',
	'拒绝仅退款' = '拒绝仅退款',
	'拒绝退货' = '拒绝退货',
	'拒绝退货退款' = '拒绝退货退款',
	'确认收货' = '确认收货',
	'退货入库' = '退货入库',
	'批量备注' = '批量备注',
	'新建售后单' = '新建售后单',
	'同步售后单' = '同步售后单',
	'审核' = '审核',
	// '确认完成'='确认完成',
	'新建无主售后单' = '新建无主售后单',
	'绑定无主件' = '绑定无主件',
	'批量导出' = '批量导出',
	'设置自动备注' = '设置自动备注',
	'标记工单状态' = '标记工单状态',
	'换货补发订单创建' = '换货补发订单创建',
	'拆包登记' = '拆包登记',



}

export const operateTypeToBtnEnum = {
	[afterSaleOperateType.同意仅退款]: BtnEnum.同意仅退款,
	[afterSaleOperateType.同意退货]: BtnEnum.同意退货,
	[afterSaleOperateType.同意退货退款]: BtnEnum.同意退货退款,
	[afterSaleOperateType.拒绝仅退款]: BtnEnum.拒绝仅退款,
	[afterSaleOperateType.拒绝退货]: BtnEnum.拒绝退货,
	[afterSaleOperateType.拒绝退货退款]: BtnEnum.拒绝退货退款,
	[afterSaleOperateType.确认收货]: BtnEnum.确认收货,
	[afterSaleOperateType.退货入库]: BtnEnum.退货入库,
	[afterSaleOperateType.拆包登记]: BtnEnum.拆包登记,
}


export type afterSaveTopItemProps = '同意仅退款' | '同意退货' | '同意退货退款' | '拆包登记' |
	'拒绝仅退款' | '拒绝退货' | '拒绝退货退款' |
	'确认收货' | '退货入库' | '批量备注' |
	'审核' | '关闭工单' | '新建售后单' | '同步售后单' |
	'新建无主售后单' | '绑定无主件' | '批量导出' |
	'设置自动备注' | '标记工单状态' | '换货补发订单创建' | '提交给供应商';

export const afterSaveTopAllItem: afterSaveTopItemProps[] = [
	'审核', '同意仅退款', '同意退货', '同意退货退款', '拒绝仅退款',
	'拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态',
	'关闭工单', '新建售后单', '绑定无主件',,
	'批量导出', '设置自动备注', '提交给供应商'];

export const afterSaveTopItems = {
	pending: {
		waitSellerConfirmHandle: {
			point: Pointer.售后_快捷筛选_平台待商家处理,
			text: "平台待商家处理",
			queryParams: {
				refundStatusList: ['1', '3', '8']
			},
			quantityStatisticsKey: "waitSellerConfirmHandleNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitBuyerConfirmHandle: {
			point: Pointer.售后_快捷筛选_平台待买家处理,
			text: "平台待买家处理",
			queryParams: {
				refundStatusList: ['2', '6', '9']
			},
			quantityStatisticsKey: "waitBuyerConfirmHandleNum", // 数量统计key
			actions: ['审核', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		erpWaitConfirmHandle: {
			point: Pointer.售后_快捷筛选_ERP待处理,
			text: "ERP待处理",
			queryParams: {
				refundSystemStatusList: [REFUND_SYSTEM_STATUS.待处理, REFUND_SYSTEM_STATUS.处理中]
			},
			quantityStatisticsKey: "erpWaitConfirmHandleNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '绑定无主件', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitConfirmItemHandle: {
			point: Pointer.售后_快捷筛选_待收货,
			text: "待收货",
			queryParams: {
				refundSystemStatusList: [REFUND_SYSTEM_STATUS.待处理, REFUND_SYSTEM_STATUS.处理中, REFUND_SYSTEM_STATUS.已完成],
				confirmItemStatusList: [0, 1],
			},
			quantityStatisticsKey: "waitConfirmItemHandleNum", // 数量统计key
			actions: ['审核', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitStockReturnHandle: {
			point: Pointer.售后_快捷筛选_待入库,
			text: "待入库",
			queryParams: {
				refundSystemStatusList: [REFUND_SYSTEM_STATUS.待处理, REFUND_SYSTEM_STATUS.处理中, REFUND_SYSTEM_STATUS.已完成],
				handleReturnStatusList: [0, 1],
			},
			quantityStatisticsKey: "waitStockReturnHandleNum", // 数量统计key
			actions: ['审核', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
	},
	overdue: {
		overdue6Hours: {
			point: Pointer.售后_快捷筛选_6小时内超时,
			text: "6小时内超时",
			queryParams: {
				timeOutSearch: "LESS_THAN_SIX_HOURS",
			},
			quantityStatisticsKey: "overdue6HoursNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		overdue12Hours: {
			point: Pointer.售后_快捷筛选_12小时内超时,
			text: "12小时内超时",
			queryParams: {
				timeOutSearch: "LESS_THAN_TWELVE_HOURS",
			},
			quantityStatisticsKey: "overdue12HoursNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		overdue24Hours: {
			point: Pointer.售后_快捷筛选_24小时内超时,
			text: "24小时内超时",
			queryParams: {
				timeOutSearch: "LESS_THAN_TWENTY_FOUR_HOURS",
			},
			quantityStatisticsKey: "overdue24HoursNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		overdue48Hours: {
			point: Pointer.售后_快捷筛选_48小时内超时,
			text: "48小时内超时",
			queryParams: {
				timeOutSearch: "LESS_THAN_FORTY_EIGHT_HOURS",
			},
			quantityStatisticsKey: "overdue48HoursNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
	},
	unshippedOnlyRefund: {
		all: {
			point: Pointer.售后_快捷筛选_未发货仅退款全部,
			text: "全部",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_SELLER_SEND_GOODS"],
			},
			quantityStatisticsKey: "allUnshippedOnlyRefundNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		onlyRefundWaitRefund: {
			point: Pointer.售后_快捷筛选_未发货仅退款待退款,
			text: "待退款",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_SELLER_SEND_GOODS"],
				refundStatusList: ["1"]
			},
			quantityStatisticsKey: "onlyRefundWaitRefundNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		onlyRefundWaitSpeedRefund: {
			point: Pointer.售后_快捷筛选_未发货仅退款平台极速退款,
			text: "平台极速退款",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_SELLER_SEND_GOODS"],
				refundStatusList: ["4"],
				refundTagList: ["speedRefund"]
			},
			quantityStatisticsKey: "onlyRefundWaitSpeedRefundNum", // 数量统计key
			actions: ['审核', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		}
	},
	shippedOnlyRefund: {
		all: {
			point: Pointer.售后_快捷筛选_已发货仅退款全部,
			text: "全部",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
			},
			quantityStatisticsKey: "allShippedOnlyRefundNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		platformSpeedRefund: {
			point: Pointer.售后_快捷筛选_已发货仅退款平台极速退款,
			text: "平台极速退款",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				refundTagList: ["speedRefund"],
			},
			quantityStatisticsKey: "shippedOnlyRefundPlatformSpeedRefundNum", // 数量统计key
			actions: ['审核', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitRefund: {
			point: Pointer.售后_快捷筛选_已发货仅退款待退款,
			text: "待退款",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				refundStatusList: ["1"],
			},
			quantityStatisticsKey: "shippedOnlyRefundWaitRefundNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitReceive: {
			point: Pointer.售后_快捷筛选_已发货仅退款待揽收,
			text: "待揽收",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS"],
				refundStatusList: ['1', '4', '6', '10'],
				newGoodsStatusList: ['HAS_SEND_WAIT_ACCEPT']
			},
			quantityStatisticsKey: "shippedOnlyRefundWaitReceiveNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		cancelReceive: {
			point: Pointer.售后_快捷筛选_已发货仅退款取消揽收,
			text: "取消揽收",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS"],
				newGoodsStatusList: ['HAS_SEND_CANCEL']
			},
			quantityStatisticsKey: "shippedOnlyRefundCancelReceiveNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		onShipment: {
			point: Pointer.售后_快捷筛选_已发货仅退款发货在途,
			text: "发货在途",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS"],
				refundStatusList: ['1', '4', '6', '10'],
				newGoodsStatusList: ['HAS_SEND_DELIVERING']
			},
			quantityStatisticsKey: "shippedOnlyRefundOnShipmentNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		onReturnShipment: {
			point: Pointer.售后_快捷筛选_已发货仅退款退回在途,
			text: "退回在途",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				newGoodsStatusList: ['HAS_SEND_REFUSE']
			},
			quantityStatisticsKey: "shippedOnlyRefundOnReturnShipmentNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],

		},
		returnSign: {
			point: Pointer.售后_快捷筛选_已发货仅退款退回签收,
			text: "退回签收",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				newGoodsStatusList: ['HAS_SEND_RETURN_SIGN']
			},
			quantityStatisticsKey: "shippedOnlyRefundOnReturnSignNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		returnReceive: {
			point: Pointer.售后_快捷筛选_已发货仅退款退回收货,
			text: "退回收货",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				newGoodsStatusList: ['HAS_SEND_RETURN_SIGN_CONFIRM']
			},
			quantityStatisticsKey: "shippedOnlyRefundOnReturnReceiveNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		buyerSign: {
			point: Pointer.售后_快捷筛选_已发货仅退款买家已签收,
			text: "买家已签收",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				refundStatusList: ['1', '4', '6', '10'],
				newGoodsStatusList: ['HAS_SEND_SIGN']
			},
			quantityStatisticsKey: "shippedOnlyRefundBuyerSignNum", // 数量统计key
			actions: ['审核',"同意仅退款", '拒绝仅退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
	},
	returnAndRefund: {
		all: {
			point: Pointer.售后_快捷筛选_退货退款全部,
			text: "全部",
			queryParams: {
				afterSaleTypeList: [2],
			},
			quantityStatisticsKey: "allReturnAndRefundNum", // 数量统计key
			actions: ['审核', '同意退货', '同意退货退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '绑定无主件', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitAgree: {
			point: Pointer.售后_快捷筛选_退货退款待同意退货,
			text: "待同意退货",
			queryParams: {
				afterSaleTypeList: [2],
				refundStatusList: ["1"],
			},
			quantityStatisticsKey: "returnAndRefundWaitAgreeNum", // 数量统计key
			actions: ['审核', '同意退货', '同意退货退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '绑定无主件', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitBuyerReturn: {
			point: Pointer.售后_快捷筛选_退货退款待买家退货,
			text: "待买家退货",
			queryParams: {
				afterSaleTypeList: [2],
				refundStatusList: ["2"],
			},
			quantityStatisticsKey: "returnAndRefundWaitBuyerReturnNum", // 数量统计key
			actions: ['审核', '同意退货', '同意退货退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '绑定无主件', '批量导出', '设置自动备注', '提交给供应商'],
		},
		buyerHasReturn: {
			point: Pointer.售后_快捷筛选_退货退款买家已退货,
			text: "买家已退货",
			queryParams: {
				afterSaleTypeList: [2],
				refundStatusList: ["3"],
			},
			quantityStatisticsKey: "returnAndRefundBuyerHasReturnNum", // 数量统计key
			actions: ['审核', '同意退货', '同意退货退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '绑定无主件', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitReturnInTransit: {
			point: Pointer.售后_快捷筛选_退货退款退货在途,
			text: "退货在途",
			queryParams: {
				afterSaleTypeList: [2],
				refundStatusList: ["3"],
				newGoodsStatusList: ['GOODS_RETURN_DELIVERING']
			},
			quantityStatisticsKey: "returnAndRefundWaitReturnInTransitNum", // 数量统计key
			actions: ['审核', '同意退货', '同意退货退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '绑定无主件', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitReturnSign: {
			point: Pointer.售后_快捷筛选_退货退款退货签收,
			text: "退货签收",
			queryParams: {
				afterSaleTypeList: [2],
				refundStatusList: ["3"],
				newGoodsStatusList: ['GOODS_RETURN_SIGN']
			},
			quantityStatisticsKey: "returnAndRefundWaitReturnSignNum", // 数量统计key
			actions: ['审核', '同意退货', '同意退货退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '绑定无主件', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitReturnReceive: {
			point: Pointer.售后_快捷筛选_退货退款退货收货,
			text: "退货收货",
			queryParams: {
				afterSaleTypeList: [2],
				refundStatusList: ["3"],
				newGoodsStatusList: ['GOODS_RETURN_SIGN_CONFIRM']
			},
			quantityStatisticsKey: "returnAndRefundWaitReturnReceiveNum", // 数量统计key
			actions: ['审核', '同意退货', '同意退货退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '绑定无主件', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitRefund: {
			point: Pointer.售后_快捷筛选_退货退款待退款,
			text: "待退款",
			queryParams: {
				afterSaleTypeList: [2],
				refundStatusList: ["3"],
				newGoodsStatusList: ['GOODS_RETURN_SIGN', 'GOODS_RETURN_SIGN_CONFIRM']
			},
			quantityStatisticsKey: "returnAndRefundWaitRefundNum", // 数量统计key
			actions: ['审核', '同意退货', '同意退货退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '绑定无主件', '批量导出', '设置自动备注', '提交给供应商'],
		},
	},
	exchangeItemReplenishment: {
		all: {
			point: Pointer.售后_快捷筛选_换货补寄全部,
			text: "全部",
			queryParams: {
				afterSaleTypeList: [3, 5],
			},
			quantityStatisticsKey: "allExchangeItemReplenishmentNum", // 数量统计key
			actions: ['审核', '同意退货', '拒绝退货', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注'],
		},
		waitAgree: {
			point: Pointer.售后_快捷筛选_待同意换货补寄,
			text: "待同意换货/补寄",
			queryParams: {
				afterSaleTypeList: [3, 5],
				refundStatusList: ["1"],
			},
			quantityStatisticsKey: "exchangeItemReplenishmentWaitAgreeNum", // 数量统计key
			actions: ['审核', '同意退货', '拒绝退货', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注'],
		},
		waitBuyerReturn: {
			point: Pointer.售后_快捷筛选_换货待买家退货,
			text: "待买家退货",
			queryParams: {
				afterSaleTypeList: [3],
				refundStatusList: ["2"],
			},
			quantityStatisticsKey: "exchangeItemReplenishmentWaitBuyerReturnNum", // 数量统计key
			actions: ['审核', '同意退货', '拒绝退货', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注'],
		},
		waitReturnInTransit: {
			point: Pointer.售后_快捷筛选_换货退货在途,
			text: "换货退货在途",
			queryParams: {
				afterSaleTypeList: [3],
				newGoodsStatusList: ['HAS_SEND_REFUSE']
			},
			quantityStatisticsKey: "exchangeItemReplenishmentWaitReturnInTransitNum", // 数量统计key
			actions: ['审核', '同意退货', '拒绝退货', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注'],
		},
		waitReturnSign: {
			point: Pointer.售后_快捷筛选_换货退货签收,
			text: "换货退货签收",
			queryParams: {
				afterSaleTypeList: [3],
				newGoodsStatusList: ['GOODS_RETURN_SIGN']
			},
			quantityStatisticsKey: "exchangeItemReplenishmentWaitReturnSignNum", // 数量统计key
			actions: ['审核', '同意退货', '拒绝退货', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注'],
		},
		waitReturnReceive: {
			point: Pointer.售后_快捷筛选_换货退货收货,
			text: "换货退货收货",
			queryParams: {
				afterSaleTypeList: [3],
				newGoodsStatusList: ['GOODS_RETURN_SIGN_CONFIRM']
			},
			quantityStatisticsKey: "exchangeItemReplenishmentWaitReturnReceiveNum", // 数量统计key
			actions: ['审核', '同意退货', '拒绝退货', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注'],
		},
		waitCreate: {
			point: Pointer.售后_快捷筛选_换货补寄单未创建,
			text: "换货/补寄单未创建",
			queryParams: {
				afterSaleTypeList: [3, 5],
				// newGoodsStatusList: ['GOODS_RETURN_SIGN', 'GOODS_RETURN_SIGN_CONFIRM'],
				exchangeRefundStatusList: ['1']
			},
			quantityStatisticsKey: "exchangeItemReplenishmentWaitCreateNum", // 数量统计key
			actions: ['审核', '同意退货', '拒绝退货', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注'],
		},
		waitSend: {
			point: Pointer.售后_快捷筛选_换货补寄单待发出,
			text: "换货/补寄单待发出",
			queryParams: {
				afterSaleTypeList: [3, 5],
				exchangeRefundStatusList: ['2']
			},
			quantityStatisticsKey: "exchangeItemReplenishmentWaitSendNum", // 数量统计key
			actions: ['审核', '同意退货', '拒绝退货', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注'],
		}
	},
	distributorRefund: {
		all: {
			point: Pointer.售后_快捷筛选_分销全部,
			text: "全部",
			queryParams: {
				scmRefundType: "agentSend"
			},
			quantityStatisticsKey: "allDistributorRefundNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注'],
		},
		waitSubmit: {
			point: Pointer.售后_快捷筛选_分销待提交给供应商,
			text: "待提交给供应商",
			queryParams: {
				scmRefundType: "agentSend",
				distributorIsPush: 0
			},
			quantityStatisticsKey: "distributorRefundWaitSubmitNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		waitConfirm: {
			point: Pointer.售后_快捷筛选_分销待供应商确认,
			text: "待供应商确认",
			queryParams: {
				distributorIsPush: 1,
				supplierIsConfirm: 0
			},
			quantityStatisticsKey: "distributorRefundWaitConfirmNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		},
		confirm: {
			point: Pointer.售后_快捷筛选_分销供应商已确认,
			text: "供应商已确认",
			queryParams: {
				distributorIsPush: 1,
				supplierIsConfirm: 1
			},
			quantityStatisticsKey: "distributorRefundConfirmNum", // 数量统计key
			actions: ['审核',"同意仅退款", '同意退货', '同意退货退款', '拒绝仅退款', '拒绝退货', '拒绝退货退款', '确认收货', '退货入库', '换货补发订单创建', '批量备注', '标记工单状态', '关闭工单', '批量导出', '设置自动备注', '提交给供应商'],
		}
	},
	logisticsIntercept: {
		waitIntercept: {
			point: Pointer.售后_快捷筛选_待拦截,
			text: "待拦截",
			quantityStatisticsKey: "logisticsInterceptWaitInterceptNum", // 数量统计key
			actions: [],
		},
		intercepted: {
			point: Pointer.售后_快捷筛选_已发起拦截,
			text: "已拦截",
			quantityStatisticsKey: "logisticsInterceptInterceptedNum", // 数量统计key
			actions: [],
		},
		interceptSuccess: {
			point: Pointer.售后_快捷筛选_拦截成功,
			text: "拦截成功",
			quantityStatisticsKey: "logisticsInterceptInterceptSuccessNum", // 数量统计key
			actions: [],
		}
	},
	// ... 原有其他配置保持不变 ...
    // ... 原有配置 ...
    // 新增分销商相关配置
    scmRefund: {
        waitConfirm: {
			point: Pointer.售后_分销商_快捷筛选_待供应商确认,
            text: "待供应商确认",
            queryParams: {
                distributorIsPush: 1,
                supplierIsConfirm: 0
            },
            quantityStatisticsKey: "distributorRefundWaitConfirmNum",
            actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
        },
        confirm: {
			point: Pointer.售后_分销商_快捷筛选_供应商已确认,
            text: "供应商已确认",
            queryParams: {
                distributorIsPush: 1,
                supplierIsConfirm: 1
            },
            quantityStatisticsKey: "distributorRefundConfirmNum",
            actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
        },
		waitConfirmItemHandle: {
			point: Pointer.售后_分销商_快捷筛选_待收货,
			text: "待收货",
			queryParams: {
				refundSystemStatusList: [REFUND_SYSTEM_STATUS.待处理, REFUND_SYSTEM_STATUS.处理中, REFUND_SYSTEM_STATUS.已完成],
				confirmItemStatusList: [0, 1],
			},
			quantityStatisticsKey: "waitConfirmItemHandleNum", // 数量统计key
			actions: ['审核', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
		waitStockReturnHandle: {
			point: Pointer.售后_分销商_快捷筛选_待入库,
			text: "待入库",
			queryParams: {
				refundSystemStatusList: [REFUND_SYSTEM_STATUS.待处理, REFUND_SYSTEM_STATUS.处理中, REFUND_SYSTEM_STATUS.已完成],
				handleReturnStatusList: [0, 1],
			},
			quantityStatisticsKey: "waitStockReturnHandleNum", // 数量统计key
			actions: ['审核', '确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
    },

    // 更新退货退款分类
    scmReturnAndRefund: {
        all: {
			point: Pointer.售后_分销商_快捷筛选_退货退款全部,
            text: "全部",
            queryParams: {
                afterSaleTypeList: [2],
            },
            quantityStatisticsKey: "allReturnAndRefundNum",
            actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
        },
        waitBuyerReturn: {
			point: Pointer.售后_分销商_快捷筛选_退货退款待买家退货,
            text: "待买家退货",
            queryParams: {
                afterSaleTypeList: [2],
                refundStatusList: ["2"],
            },
            quantityStatisticsKey: "returnAndRefundWaitBuyerReturnNum",
            actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
        },
		buyerHasReturn: {
			point: Pointer.售后_分销商_快捷筛选_退货退款买家已退货,
			text: "买家已退货",
			queryParams: {
				afterSaleTypeList: [2],
				refundStatusList: ["3"],
			},
			quantityStatisticsKey: "returnAndRefundBuyerHasReturnNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
        waitReturnInTransit: {
			point: Pointer.售后_分销商_快捷筛选_退货退款退货在途,
            text: "退货在途",
            queryParams: {
                afterSaleTypeList: [2],
                refundStatusList: ["3"],
                newGoodsStatusList: ['GOODS_RETURN_DELIVERING']
            },
            quantityStatisticsKey: "returnAndRefundWaitReturnInTransitNum",
            actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
        },
        waitReturnSign: {
			point: Pointer.售后_分销商_快捷筛选_退货退款退货签收,
            text: "退货签收",
            queryParams: {
                afterSaleTypeList: [2],
                refundStatusList: ["3"],
                newGoodsStatusList: ['GOODS_RETURN_SIGN']
            },
            quantityStatisticsKey: "returnAndRefundWaitReturnSignNum",
            actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
        },
        waitReturnReceive: {
			point: Pointer.售后_分销商_快捷筛选_退货退款退货收货,
            text: "退货收货",
            queryParams: {
                afterSaleTypeList: [2],
                refundStatusList: ["3"],
                newGoodsStatusList: ['GOODS_RETURN_SIGN_CONFIRM']
            },
            quantityStatisticsKey: "returnAndRefundWaitReturnReceiveNum",
            actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
        }
    },

    // 更新已发货仅退款配置
    scmShippedOnlyRefund: {
        all: {
			point: Pointer.售后_分销商_快捷筛选_已发货仅退款全部,
			text: "全部",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
			},
			quantityStatisticsKey: "allShippedOnlyRefundNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
		platformSpeedRefund: {
			point: Pointer.售后_分销商_快捷筛选_已发货仅退款平台极速退款,
			text: "平台极速退款",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				refundTagList: ["speedRefund"],
			},
			quantityStatisticsKey: "shippedOnlyRefundPlatformSpeedRefundNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
		waitReceive: {
			point: Pointer.售后_分销商_快捷筛选_已发货仅退款待揽收,
			text: "待揽收",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS"],
				refundStatusList: ['1', '4', '6', '10'],
				newGoodsStatusList: ['HAS_SEND_WAIT_ACCEPT']
			},
			quantityStatisticsKey: "shippedOnlyRefundWaitReceiveNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
		cancelReceive: {
			point: Pointer.售后_分销商_快捷筛选_已发货仅退款取消揽收,
			text: "取消揽收",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS"],
				newGoodsStatusList: ['HAS_SEND_CANCEL']
			},
			quantityStatisticsKey: "shippedOnlyRefundCancelReceiveNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
		onShipment: {
			point: Pointer.售后_分销商_快捷筛选_已发货仅退款发货在途,
			text: "发货在途",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS"],
				refundStatusList: ['1', '4', '6', '10'],
				newGoodsStatusList: ['HAS_SEND_DELIVERING']
			},
			quantityStatisticsKey: "shippedOnlyRefundOnShipmentNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
		onReturnShipment: {
			point: Pointer.售后_分销商_快捷筛选_已发货仅退款退回在途,
			text: "退回在途",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS"],
				newGoodsStatusList: ['HAS_SEND_REFUSE']
			},
			quantityStatisticsKey: "shippedOnlyRefundOnReturnShipmentNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],

		},
		returnSign: {
			point: Pointer.售后_分销商_快捷筛选_已发货仅退款退回签收,
			text: "退回签收",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				newGoodsStatusList: ['HAS_SEND_RETURN_SIGN']
			},
			quantityStatisticsKey: "shippedOnlyRefundOnReturnSignNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
		returnReceive: {
			point: Pointer.售后_分销商_快捷筛选_已发货仅退款退回收货,
			text: "退回收货",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				newGoodsStatusList: ['HAS_SEND_RETURN_SIGN_CONFIRM']
			},
			quantityStatisticsKey: "shippedOnlyRefundOnReturnReceiveNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
		buyerSign: {
			point: Pointer.售后_分销商_快捷筛选_已发货仅退款买家已签收,
			text: "买家已签收",
			queryParams: {
				afterSaleTypeList: [1],
				tradeStatusList: ["WAIT_BUYER_CONFIRM_GOODS", "TRADE_FINISHED"],
				refundStatusList: ['1', '4', '6', '10'],
				newGoodsStatusList: ['HAS_SEND_SIGN']
			},
			quantityStatisticsKey: "shippedOnlyRefundBuyerSignNum", // 数量统计key
			actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
		},
    },

    // 更新未发货仅退款配置
    scmUnshippedOnlyRefund: {
        all: {
			point: Pointer.售后_分销商_快捷筛选_未发货仅退款全部,
            text: "全部",
            queryParams: {
                afterSaleTypeList: [1],
                tradeStatusList: ["WAIT_SELLER_SEND_GOODS"],
            },
            quantityStatisticsKey: "allUnshippedOnlyRefundNum",
            actions: ['审核','确认收货', '退货入库', '批量备注', '标记工单状态', '关闭工单', '批量导出'],
        }
    }
};

export const defaultQuickFilterSelectConfig = {
    "pending": [
        {
            "checked": false,
            "edit": true,
            "key": "waitSellerConfirmHandle",
            "name": "平台待商家处理",
            "value": "1"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitBuyerConfirmHandle",
            "name": "平台待买家处理",
            "value": "2"
        },
        {
            "checked": false,
            "edit": true,
            "key": "erpWaitConfirmHandle",
            "name": "ERP待处理",
            "value": "3"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitConfirmItemHandle",
            "name": "待收货",
            "value": "4"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitStockReturnHandle",
            "name": "待入库",
            "value": "5"
        }
    ],
    "overdue": [
        {
            "checked": false,
            "edit": true,
            "key": "overdue6Hours",
            "name": "6小时内超时",
            "value": "6"
        },
        {
            "checked": false,
            "edit": true,
            "key": "overdue12Hours",
            "name": "12小时内超时",
            "value": "12"
        },
        {
            "checked": true,
            "edit": true,
            "key": "overdue24Hours",
            "name": "24小时内超时",
            "value": "24"
        },
        {
            "checked": false,
            "edit": true,
            "key": "overdue48Hours",
            "name": "48小时内超时",
            "value": "48"
        }
    ],
	"unshippedOnlyRefund": [
        {
            "checked": false,
            "edit": true,
            "key": "all",
            "name": "全部",
            "value": "0"
        },
        {
            "checked": true,
            "edit": true,
            "key": "onlyRefundWaitRefund",
            "name": "待退款",
            "value": "1"
        },
        {
            "checked": false,
            "edit": true,
            "key": "onlyRefundWaitSpeedRefund",
            "name": "平台极速退款",
            "value": "2"
        }
    ],
	"shippedOnlyRefund": [
        {
            "checked": false,
            "edit": true,
            "key": "all",
            "name": "全部",
            "value": "0"
        },
        {
            "checked": true,
            "edit": true,
            "key": "platformSpeedRefund",
            "name": "平台极速退款",
            "value": "1"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitRefund",
            "name": "待退款",
            "value": "2"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReceive",
            "name": "待揽收",
            "value": "3"
        },
        {
            "checked": false,
            "edit": true,
            "key": "cancelReceive",
            "name": "取消揽收",
            "value": "4"
        },
        {
            "checked": false,
            "edit": true,
            "key": "onShipment",
            "name": "发货在途",
            "value": "5"
        },
        {
            "checked": false,
            "edit": true,
            "key": "onReturnShipment",
            "name": "退回在途",
            "value": "6"
        },
        {
            "checked": false,
            "edit": true,
            "key": "returnSign",
            "name": "退回签收",
            "value": "7"
        },
        {
            "checked": false,
            "edit": true,
            "key": "returnReceive",
            "name": "退回收货",
            "value": "8"
        },
        {
            "checked": false,
            "edit": true,
            "key": "buyerSign",
            "name": "买家已签收",
            "value": "9"
        }
    ],
	"returnAndRefund": [
        {
            "checked": false,
            "edit": true,
            "key": "all",
            "name": "全部",
            "value": "0"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitAgree",
            "name": "待同意退货",
            "value": "1"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitBuyerReturn",
            "name": "待买家退货",
            "value": "2"
        },
		{
            "checked": true,
            "edit": true,
            "key": "buyerHasReturn",
            "name": "买家已退货",
            "value": "3"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReturnInTransit",
            "name": "退货在途",
            "value": "4"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReturnSign",
            "name": "退货签收",
            "value": "5"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReturnReceive",
            "name": "退货收货",
            "value": "6"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitRefund",
            "name": "待退款",
            "value": "7"
        }
    ],
    "exchangeItemReplenishment": [
        {
            "checked": false,
            "edit": true,
            "key": "all",
            "name": "全部",
            "value": "0"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitAgree",
            "name": "待同意换货/补寄",
            "value": "1"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitBuyerReturn",
            "name": "待买家退货",
            "value": "2"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReturnInTransit",
            "name": "换货退货在途",
            "value": "3"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReturnSign",
            "name": "换货退货签收",
            "value": "4"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReturnReceive",
            "name": "换货退货收货",
            "value": "5"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitCreate",
            "name": "换货/补寄单未创建",
            "value": "6"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitSend",
            "name": "换货/补寄单待发出",
            "value": "7"
        }
    ],
    "distributorRefund": [
        {
            "checked": false,
            "edit": true,
            "key": "all",
            "name": "全部",
            "value": "0"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitSubmit",
            "name": "待提交给供应商",
            "value": "1"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitConfirm",
            "name": "待供应商确认",
            "value": "2"
        },
        {
            "checked": false,
            "edit": true,
            "key": "confirm",
            "name": "供应商已确认",
            "value": "3"
        }
    ],
    "logisticsIntercept": [
        {
            "checked": true,
            "edit": true,
            "key": "waitIntercept",
            "name": "待拦截",
            "value": "1"
        },
        {
            "checked": false,
            "edit": true,
            "key": "intercepted",
            "name": "已拦截",
            "value": "2"
        },
        {
            "checked": false,
            "edit": true,
            "key": "interceptSuccess",
            "name": "拦截成功",
            "value": "3"
        }
    ],
    "dataStatisticsTime": [
        {
            "checked": true,
            "edit": false,
            "key": "dataStatisticsTimeDays",
            "name": "数据统计时间范围",
            "value": "30"
        },
        {
            "checked": true,
            "edit": false,
            "key": "dataStatisticsUpdateTime",
            "name": "数据更新时间",
            "value": "60"
        }
    ],
}

export const defaultScmQuickFilterSelectConfig = {
    "scmRefund": [
        {
            "checked": true,
            "edit": true,
            "key": "waitConfirm",
            "name": "待供应商确认",
            "value": "1"
        },
        {
            "checked": true,
            "edit": true,
            "key": "confirm",
            "name": "供应商已确认",
            "value": "2"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitConfirmItemHandle",
            "name": "待收货",
            "value": "4"
        },
        {
            "checked": true,
            "edit": true,
            "key": "waitStockReturnHandle",
            "name": "待入库",
            "value": "5"
        }
    ],
    "scmUnshippedOnlyRefund": [
        {
            "checked": false,
            "edit": true,
            "key": "all",
            "name": "全部",
            "value": "0"
        }
    ],
	"scmShippedOnlyRefund": [
        {
            "checked": false,
            "edit": true,
            "key": "all",
            "name": "全部",
            "value": "0"
        },
        {
            "checked": false,
            "edit": true,
            "key": "platformSpeedRefund",
            "name": "平台极速退款",
            "value": "1"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReceive",
            "name": "待揽收",
            "value": "2"
        },
        {
            "checked": false,
            "edit": true,
            "key": "cancelReceive",
            "name": "取消揽收",
            "value": "3"
        },
        {
            "checked": false,
            "edit": true,
            "key": "onShipment",
            "name": "发货在途",
            "value": "4"
        },
        {
            "checked": false,
            "edit": true,
            "key": "onReturnShipment",
            "name": "退回在途",
            "value": "5"
        },
        {
            "checked": false,
            "edit": true,
            "key": "returnSign",
            "name": "退回签收",
            "value": "6"
        },
        {
            "checked": false,
            "edit": true,
            "key": "returnReceive",
            "name": "退回收货",
            "value": "7"
        },
        {
            "checked": false,
            "edit": true,
            "key": "buyerSign",
            "name": "买家已签收",
            "value": "8"
        }
    ],
    "scmReturnAndRefund": [
        {
            "checked": false,
            "edit": true,
            "key": "all",
            "name": "全部",
            "value": "0"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitBuyerReturn",
            "name": "待买家退货",
            "value": "1"
        },
		{
            "checked": false,
            "edit": true,
            "key": "buyerHasReturn",
            "name": "买家已退货",
            "value": "2"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReturnInTransit",
            "name": "退货在途",
            "value": "3"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReturnSign",
            "name": "退货签收",
            "value": "4"
        },
        {
            "checked": false,
            "edit": true,
            "key": "waitReturnReceive",
            "name": "退货收货",
            "value": "5"
        }
    ],
    "scmDataStatisticsTime": [
        {
            "checked": true,
            "edit": false,
            "key": "dataStatisticsTimeDays",
            "name": "数据统计时间范围",
            "value": "30"
        },
        {
            "checked": true,
            "edit": false,
            "key": "dataStatisticsUpdateTime",
            "name": "数据更新时间",
            "value": "60"
        }
    ]
}

export const zeroStockQuickFilterSelectHide = ["waitStockReturnHandle"];
// export const afterSaveTopItems = {
// 	NO_SEND_ONLY_REFUND: {
// 		point: Pointer.售后订单_分TAB查询_未发货仅退款_点击,
// 		text: "未发货仅退款",
// 		quantityStatisticsKey: "noSendOnlyRefundNum", // 数量统计key
// 		actions: ["同意仅退款", "拒绝仅退款", "批量备注", "审核", "同步售后单", "批量导出",],
// 	},
// 	SEND_ONLY_REFUND: {
// 		point: Pointer.售后订单_分TAB查询_已发货仅退款_点击,
// 		text: "已发货仅退款",
// 		quantityStatisticsKey: "sendOnlyRefundNum", // 数量统计key
// 		actions: [
// 			"同意仅退款",
// 			"拒绝仅退款",
// 			"确认收货",
// 			"退货入库",
// 			"批量备注",
// 			"审核",
// 			"同步售后单",
// 			"批量导出",
// 		],
// 	},
// 	RETURN_WAIT_HANDLE: {
// 		point: Pointer.售后订单_分TAB查询_退货待处理_点击,
// 		text: "退货待处理",
// 		quantityStatisticsKey: "returnWaitHandleNum", // 数量统计key
// 		actions: [
// 			"同意退货",
// 			"同意退货退款",
// 			"拒绝退货",
// 			"拒绝退货退款",
// 			"批量备注",
// 			"审核",
// 			"同步售后单",
// 			"批量导出",
// 		],
// 	},
// 	BUYER_ALREADY_RETURN: {
// 		point: Pointer.售后订单_分TAB查询_买家已退货_点击,
// 		text: "买家已退货",
// 		quantityStatisticsKey: "buyerAlreadyReturnNum", // 数量统计key
// 		actions: [
// 			"同意退货退款",
// 			"拒绝退货退款",
// 			"批量备注",
// 			"审核",
// 			"同步售后单",
// 			"批量导出",
// 		],
// 	},
// 	STOCK_WAIT_HANDLE: {
// 		point: Pointer.售后订单_分TAB查询_待入库_点击,
// 		text: "待入库",
// 		actions: [
// 			"确认收货",
// 			"退货入库",
// 			"批量备注",
// 			"审核",
// 			"新建售后单",
// 			"同步售后单",
// 			"批量导出",
// 		],
// 	},
// 	EXCHANGE_TRADE_WAIT_CREATE: {
// 		point: "",
// 		text: "换货/补发订单待创建",
// 		quantityStatisticsKey: "exchangeTradeWaitCreateNum", // 数量统计key
// 		actions: [
// 			"审核",
// 			"换货补发订单创建",
// 			"批量备注",
// 			"同步售后单",
// 			"批量导出",
// 			"标记工单状态",
// 			"关闭订单",
// 			"设置自动备注",
// 		],
// 	},
// 	WAIT_SUBMIT_SUPPLIER_USER: {
// 		point: "",
// 		text: "待提交给供应商",
// 		quantityStatisticsKey: "waitDistributorUserPushNum", // 数量统计key
// 		actions: afterSaveTopAllItem, // 全部显示
// 	},
// 	WAIT_SUPPLIER_USER_CONFIRM: {
// 		point: "",
// 		text: "待供应商确认",
// 		quantityStatisticsKey: "waitSupplierUserConfirmNum", // 数量统计key
// 		actions: afterSaveTopAllItem, // 全部显示
// 	},
// 	SUPPLIER_USER_HAS_CONFIRM: {
// 		point: "",
// 		text: "供应商已确认",
// 		quantityStatisticsKey: "supplierUserHasConfirmNum",
// 		actions: afterSaveTopAllItem, // 全部显示
// 	},
// };

export const afterSaveActions = {
	'同意仅退款': {
		typeFilterTabs: 'ALL',
		point: Pointer.售后订单_批量操作_同意仅退款_点击,
	},
	'同意退货': {
		typeFilterTabs: 'ALL',
		point: Pointer.售后订单_批量操作_同意退货_点击,
	},
	'同意退货退款': {
		typeFilterTabs: 'ALL',
		point: Pointer.售后订单_批量操作_同意退货退款_点击,
	},
	'拒绝仅退款': {
		point: Pointer.售后订单_批量操作_拒绝仅退款_点击,
	},
	'拒绝退货': {
		point: Pointer.售后订单_批量操作_拒绝退货_点击,
	},
	'拒绝退货退款': {
		point: Pointer.售后订单_批量操作_拒绝退货退款_点击,
	},
	'确认收货': {
		typeFilterTabs: ['STOCK_WAIT_HANDLE'],
		point: Pointer.售后订单_批量操作_确认收货_点击,
		className: s["custom-color"],
	},
	'退货入库': {
		typeFilterTabs: ['STOCK_WAIT_HANDLE'],
		point: Pointer.售后订单_批量操作_退货入库_点击,
	},
	'换货补发订单创建': {
		typeFilterTabs: ['EXCHANGE_TRADE_WAIT_CREATE'],
		point: '',
		className: s["custom-color"],
	},
	'批量备注': {
		point: Pointer.售后订单_批量操作_批量备注_点击,
	},
	'新建售后单': {
		point: Pointer.售后_新建售后单,
	},
	'新建无主售后单': {
		point: Pointer.售后_新建无主售后单
	},
	'绑定无主件': {
		point: Pointer.售后订单_批量操作_绑定订单_点击,
	},
	'同步售后单': {
		point: Pointer.售后_售后订单_同步售后订单,
	},
	'审核': {
		point: Pointer.售后_批量审核通过,
		className: s["btn_check"]
	},
	'批量导出': {
		point: Pointer.售后_批量导出,
	},
	'设置自动备注': {
		point: Pointer.售后_设置自动备注_点击,
	},
	// '确认完成': {
	// 	point: Pointer.售后_批量确认完成,

	// }
};

export enum BatchExportType {
	导出勾选订单 = 0,
	导出查询结果 = 1
}

export const AftersaleGoodsInTransitConfig = {
	default: [],
	options: [{
		label: "拒收退货",
		value: "1",
	}, {
		label: "退货退款",
		value: "2",
	}, {
		label: "换货",
		value: "3",
	}]
};

export const AftersaleShowConfig = {
	default: [],
	options: [{
		label: "售后工单已关闭",
		value: "1",
	}, {
		label: "售后工单已完成",
		value: "2",
	}]
};

export const AftersaleAutoChecking = {
	default: [],
	options: [{
		label: "未发货仅退款",
		value: "1",
	}, {
		label: "已发货仅退款",
		value: "2",
	}, {
		label: "退货退款",
		value: "3",
	}, {
		label: "换货",
		value: "4",
	}, {
		label: "补发",
		value: "5",
	}]
};

export const PlatformCloseAutoClose = {
	default: [],
	options: [{
		label: "平台售后单已关闭后，自动关闭售后工单",
		value: "1",
	}]
};

export const ExchangeTradeMemoFlag = {
	default: [],
	options: [{
		label: "换货订单",
		value: "1",
	},{
		label: "补发订单",
		value: "2",
	}]
};

export const AutoMarkProcessing = {
	default: [],
	options: [{
		label: "确认收货",
		value: "1",
	}, {
		label: "生成换货/补发手动订单",
		value: "2",
	}]
};
export const AutoMarkProcessingFinished = {
	default: [],
	options: [{
		label: "平台售后单处理完成后",
		value: "1",
	}]
};

export const AutoUploadExchangeInvoiceno = {
	default: [],
	options: [{
		label: <span>换货补发手工单发货后自动上传快递单号
			<Tooltip title="因平台限制，拼多多补寄售后单暂不支持自动上传快递单号；发货后，请手动复制快递单号至店铺后台填写">
				<span style={{color:'#999',borderBottom: '1px dashed rgb(153, 153, 153)'}}>(拼多多补寄售后单暂不支持)</span>
			</Tooltip>
		</span>,
		value: "1",
	}]
};

export const AutoChangeWorkOrderType = {
	default: [],
	options: [{
		label: "开启",
		value: "1",
	}]
};

export enum AftersaleGlobalConfig {
	售后订单列显示设置 = 'refundListColumnConfig',
	售后订单列宽设置 = 'refundListShowColumnSet',
	售后扫描自动入库存配置= "refundScanAutoUpStockConfig",
	售后扫描确认收货自动创建换货手工单配置= "refundScanAutoCreateExchangeOrder",
	批量登记确认收货自动创建换货手工单配置= "batchRegisterAutoCreateExchangeOrder",
	批量登记自动入库配置= "batchRegisterAutoUpStockConfig",
	售后工单设置 = "autoReviewRefundTypeBiz",
	售后工单自动关闭 = "refundAutoCloseConfig",
	创建换货补发订单时同步售后卖家备注设置 = 'createExchangeTradeMemoFlagSet',
	创建换货补发订单时同步售后线下备注设置 = 'createExchangeTradeLocalContentSet',
	自动标记处理中 = "refundMarkHandleIng",
	自动标记已完成 = "refundMarkHandleFinish",
	换货补发订单发货后自动上传 = "autoUploadExchangeInvoiceno",
	售后订单隐藏显示设置 = "refundSelectHideSetting",
	售后订单产品内容设置 = 'refundListProductContentSetting',
	售后买家昵称信息设置 = 'refundListInfoShowSet',
	售后分析指标配置 = "refundAnalysisMetricConfigure",
	售后扫描记录列配置 = "refundScanRecordListColumn",
	售后扫描记录展示字段自定义设置 = "refundScanRecordItemColumn",
	售后订单定制查询条件 = "refundListQueryColumn",
	售后订单快捷筛选设置 = "refundListQuickFilterSet",
	售后订单高级设置销退在途设置 = "stockWaitHandSet",
	分销商的售后单定制查询条件 = "refundListQueryColumnForDistributorRefundList",
	分销商的售后单列显示设置 = "refundListColumnConfigForDistributorRefundList",
	分销商的售后单列宽设置 = "refundListShowColumnSetForDistributorRefundList",
	分销商的售后单隐藏显示设置 = "refundSelectHideSettingForDistributorRefundList",
	分销商的售后单自动审核 = "autoReviewRefundTypeBizForDistributorRefundList",
	分销商的售后单自动标记处理中 = "refundMarkHandleIngForDistributorRefundList",
	分销商的售后单自动标记已完成 = "refundMarkHandleFinishForDistributorRefundList",
	分销商的售后单售后工单自动关闭 = "refundAutoCloseConfigForDistributorRefundList",
	分销商的售后单换货补发订单发货后自动上传 = "autoUploadExchangeInvoicenoForDistributorRefundList",
	已发货仅退款售后单同步至系统后自动更新公工单类型为拒收退货 = "onlyRefundAfterSendChangeWorkOrderType",
	分销商的售后订单快捷筛选设置 = "scmRefundListQuickFilterSet",
}

export const MissedType = {
	1: "订单编号",
	2: "售后单号",
	3: "发货快递单号",
	4: "退货快递单号",
	5: "订单/售后单号",
	6: "快递单号",
};

export enum OfflineMemoEnum {
	线下备注 = "offlineMemo",
	是否有线下备注 = "needFindLocalContent",
	线下备注内容 = "localContent",
}

// 售后订单自定义查询条件默认排序 不动这个值
 export enum refundSearchConditionDefaultIds {
	"时间选择" = 1,
	"平台&店铺" = 2,
	"售后类型" = 3,
	"平台退款状态" = 4,
	"审核状态" = 5,
	"商品处理状态" = 6,
	"售后原因" = 7,
	"订单状态" = 8,
	"留言备注" = 9,
	"商品包含" = 10,
	"买家昵称" = 11,
	"平台极速退款" = 12,
	"工单类型" = 13,
	"售后工单状态" = 14,
	"无主件查询" = 15,
	"系统单号" = 16,
	"售后单号" = 17,
	"发货快递单号" = 18,
	"退货快递单号" = 19,
	"发货物流状态" = 20,
	"退货物流状态" = 21,
	"拦截状态" = 22,
	"线下备注" = 23,
	"订单编号" = 24,
	"商品不包含" = 25,
	"自发/代发售后单" = 26,
	"选择供应商" = 27,
	"选择分销商" = 28,
	"货物状态" = 29,
	"即将超时" = 30,
	"售后标签" = 31,
	"售后异常" = 32,
	"申请售后金额" = 33,
	"换货/补寄订单状态" = 34,
	"是否提交供应商" = 35,
	"供应商确认状态" = 36,
	"收货状态" = 37,
	"入库状态" = 38,
	'市场/档口/供应商' = 39,
}

/**
 * 售后订单自定义查询条件
 * sort 默认排序
 * disabled 禁止点击
 * selected 勾选状态
 */
export const refundSearchConditionDefaultConfig = [
	{
		"condition": "rangeTime",
		"conditionName": "时间选择",
		"id": refundSearchConditionDefaultIds.时间选择,
		"disabled": true,
		"selected": 1,
		"sort": 1
	},
	{
		"condition": "platformInfo",
		"conditionName": "平台&店铺",
		"id": refundSearchConditionDefaultIds["平台&店铺"],
		"disabled": true,
		"selected": 1,
		"sort": 2
	},
	{
		"condition": "afterSaleTypeList",
		"conditionName": "售后类型",
		"id": refundSearchConditionDefaultIds.售后类型,
		"disabled": true,
		"selected": 1,
		"sort": 3
	},
	{
		"condition": "refundStatus",
		"conditionName": "平台退款状态",
		"id": refundSearchConditionDefaultIds.平台退款状态,
		"disabled": true,
		"selected": 1,
		"sort": 4
	},
	{
		"condition": "refundReasonList",
		"conditionName": "售后原因",
		"id": refundSearchConditionDefaultIds.售后原因,
		"selected": 1,
		"sort": 5
	},
	{
		"condition": "reviewStatus",
		"conditionName": "审核状态",
		"id": refundSearchConditionDefaultIds.审核状态,
		"selected": 1,
		"sort": 6
	},
	{
		"condition": "refundSystemTypeList",
		"conditionName": "工单类型",
		"id": refundSearchConditionDefaultIds.工单类型,
		"selected": 1,
		"sort": 7
	},
	{
		"condition": "refundSystemStatusList",
		"conditionName": "售后工单状态",
		"id": refundSearchConditionDefaultIds.售后工单状态,
		"selected": 1,
		"sort": 8
	},
	{
		"condition": "refundId",
		"conditionName": "订单/售后单号",
		"id": refundSearchConditionDefaultIds.售后单号,
		"selected": 1,
		"sort": 9
	},
	{
		"condition": "invoiceNo",
		"conditionName": "发货/退货物流",
		"id": refundSearchConditionDefaultIds.发货物流状态,
		"selected": 1,
		"sort": 10
	},
	{
		"condition": "tradeStatus",
		"conditionName": "订单状态",
		"id": refundSearchConditionDefaultIds.订单状态,
		"selected": 0,
		"sort": 11
	},
	{
		"condition": "flagValue",
		"conditionName": "留言备注",
		"id": refundSearchConditionDefaultIds.留言备注,
		"selected": 0,
		"sort": 12
	},
	{
		"condition": OfflineMemoEnum.线下备注,
		"conditionName": "线下备注",
		"id": refundSearchConditionDefaultIds.线下备注,
		"selected": 0,
		"sort": 13
	},
	{
		"condition": "buyerNick",
		"conditionName": "买家昵称",
		"id": refundSearchConditionDefaultIds.买家昵称,
		"selected": 0,
		"sort": 14
	},
	{
		"condition": "isNoTradeMess",
		"conditionName": "是否无主件",
		"id": refundSearchConditionDefaultIds.无主件查询,
		"selected": 0,
		"sort": 15
	},
	{
		"condition": "timeOutSearch",
		"conditionName": "即将超时",
		"id": refundSearchConditionDefaultIds.即将超时,
		"selected": 0,
		"sort": 16
	},
	{
		"condition": "refundTagList",
		"conditionName": "售后标签",
		"id": refundSearchConditionDefaultIds.售后标签,
		"selected": 0,
		"sort": 17
	},
	{
		"condition": "refundExceptionList",
		"conditionName": "售后异常",
		"id": refundSearchConditionDefaultIds.售后异常,
		"selected": 0,
		"sort": 18
	},
	{
		"condition": "goodsStatus",
		"conditionName": "货物状态",
		"id": refundSearchConditionDefaultIds.货物状态,
		"selected": 0,
		"sort": 19
	},
	{
		"condition": "confirmItemStatus",
		"conditionName": "收货状态",
		"id": refundSearchConditionDefaultIds.收货状态,
		"selected": 0,
		"sort": 20
	},
	{
		"condition": "handleReturnStatus",
		"conditionName": "入库状态",
		"id": refundSearchConditionDefaultIds.入库状态,
		"selected": 0,
		"sort": 21
	},
	// {
	// 	"condition": "itemDealStatus",
	// 	"conditionName": "商品处理状态",
	// 	"id": refundSearchConditionDefaultIds.商品处理状态,
	// 	"selected": 0,
	// 	"sort": 20
	// },
	{
		"condition": "applyRefundFee",
		"conditionName": "申请售后金额",
		"id": refundSearchConditionDefaultIds.申请售后金额,
		"selected": 0,
		"sort": 22
	},
	{
		"condition": "sysItemInclude",
		"conditionName": "商品/货品",
		"id": refundSearchConditionDefaultIds.商品包含,
		"selected": 0,
		"sort": 23
	},
	{
		"condition": "supplierInclude",
		"conditionName": "市场/档口/供应商",
		"id": refundSearchConditionDefaultIds['市场/档口/供应商'],
		"selected": 0,
		"sort": 24
	},
	// {
	// 	"condition": "sysItemNotInclude",
	// 	"conditionName": "商品不包含",
	// 	"id": refundSearchConditionDefaultIds.商品不包含,
	// 	"selected": 0,
	// 	"sort": 23
	// },
	{
		"condition": "exchangeRefundStatusList",
		"conditionName": "换货/补寄订单状态",
		"id": refundSearchConditionDefaultIds["换货/补寄订单状态"],
		"selected": 0,
		"sort": 25
	},
	{
		"condition": "scmRefundType",
		"conditionName": "自发/代发售后单",
		"id": refundSearchConditionDefaultIds["自发/代发售后单"],
		"selected": 0,
		"sort": 26
	},
	{
		"condition": "scmSupplierUserIdList",
		"conditionName": "选择供应商",
		"id": refundSearchConditionDefaultIds.选择供应商,
		"selected": 0,
		"sort": 27
	},
	{
		"condition": "scmDistributorUserId",
		"conditionName": "选择分销商",
		"id": refundSearchConditionDefaultIds.选择分销商,
		"selected": 0,
		"sort": 28,
	},
	{
		"condition": "distributorIsPush",
		"conditionName": "是否提交供应商",
		"id": refundSearchConditionDefaultIds.是否提交供应商,
		"selected": 0,
		"sort": 29,
	},
	{
		"condition": "supplierIsConfirm",
		"conditionName": "供应商确认状态",
		"id": refundSearchConditionDefaultIds.供应商确认状态,
		"selected": 0,
		"sort": 30,
	},
	// {
	// 	"condition": "tid",
	// 	"conditionName": "系统单号",
	// 	"id": refundSearchConditionDefaultIds.系统单号,
	// 	"selected": 1,
	// 	"sort": 16
	// },
	// {
	// 	"condition": "invoiceNo",
	// 	"conditionName": "发货快递单号",
	// 	"id": refundSearchConditionDefaultIds.发货快递单号,
	// 	"selected": 1,
	// 	"sort": 18
	// },
	// {
	// 	"condition": "sid",
	// 	"conditionName": "退货快递单号",
	// 	"id": refundSearchConditionDefaultIds.退货快递单号,
	// 	"selected": 1,
	// 	"sort": 19
	// },
	// {
	// 	"condition": "sidStatusEnum",
	// 	"conditionName": "退货物流状态",
	// 	"id": refundSearchConditionDefaultIds.退货物流状态,
	// 	"selected": 1,
	// 	"sort": 21
	// },
	// {
	// 	"condition": "ptTid",
	// 	"conditionName": "订单编号",
	// 	"id": refundSearchConditionDefaultIds.订单编号,
	// 	"selected": 1,
	// 	"sort": 22
	// },
];

export const enum MARK_PROCESSING_STATUS {
	待处理 = 3,
	处理中 = 4,
	已完成 = 5,
}

export enum PRODUCT_CONTENT_ENUM {
	商品标题 = "title",
	货品简称 = "sysItemAlias",
	商家编码 = "itemOuterId",
	货品编码 = "outerId",
	商品图片 = "picPath",
	货品图片 = "picUrl",
	规格名称 = "skuProperties",
	规格别名 = "sysSkuAlias",
	规格编码 = "itemOuterSkuId",
	货品规格编码 = "outerSkuId",
	市场 = "market",
	档口 = "stall",
	供应商 = "supplierName",
	小图 = 'smallImg',
	中图 = 'midImg',
	大图 = 'bigImg',
}

export enum PRODUCT_CONTENT_KEY_ENUM {
	title = "1",
	itemOuterId = "2",
	picPath = "3",
	skuProperties = "4",
	itemOuterSkuId = "5",

	sysItemAlias = "11",
	outerId = "12",
	picUrl = "13",
	sysSkuAlias = "14",
	outerSkuId = "15",

	supplierName = "16",
	market = "17",
	stall = "18",

	smallImg = "19",
	midImg = "20",
	bigImg = "21",
}

export const REVERSE_MAPPING = {
	"1": "title",
	"2": "itemOuterId",
	"3": "picPath",
	"4": "skuProperties",
	"5": "itemOuterSkuId",
	"11": "sysItemAlias",
	"12": "outerId",
	"13": "picUrl",
	"14": "sysSkuAlias",
	"15": "outerSkuId",

	"16": "supplierName",
	"17": "market",
	"18": "stall",

	'19':'smallImg',
	'20':'midImg',
	'21':'bigImg'
};

// 库存版本
export const LOCAL_AFTERSALE_TRADE_SETTING = "returnLabelAftersaleTradeProductSetting";

export const PRODUCT_SETTING_OBJECT_IMG_SIZE = {
		name:'预览图展示',
		key:'showPicSize',
		list:[{
			label:'小图',
			value:PRODUCT_CONTENT_ENUM.小图,
		},{
			label:'中图',
			value:PRODUCT_CONTENT_ENUM.中图,
		},{
			label:'大图',
			value:PRODUCT_CONTENT_ENUM.大图,
		}]
	}


export const PRODUCT_SETTING_OBJECT = [
	{
		name: "商品展示",
		key: "good",
		list: [{
			label: "商品标题",
			value: PRODUCT_CONTENT_ENUM.商品标题,
			showInStockVersion: true,
			line: true
		}, {
			label: "简称",
			value: PRODUCT_CONTENT_ENUM.货品简称,
			showInStockVersion: true,
			showInZeroStockVersion: false
		}, {
			label: "商家编码",
			value: PRODUCT_CONTENT_ENUM.商家编码,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}, {
			label: "货品编码",
			value: PRODUCT_CONTENT_ENUM.货品编码,
			showInStockVersion: true,
			showInZeroStockVersion: false
		}, {
			label: "商品图片",
			value: PRODUCT_CONTENT_ENUM.商品图片,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}, {
			label: "货品图片",
			value: PRODUCT_CONTENT_ENUM.货品图片,
			showInStockVersion: true,
			showInZeroStockVersion: false
		}]
	}, {
		name: "规格展示",
		key: "sku",
		list: [{
			label: "规格名称",
			value: PRODUCT_CONTENT_ENUM.规格名称,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}, {
			label: "规格别名",
			value: PRODUCT_CONTENT_ENUM.规格别名,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}, {
			label: "规格编码",
			value: PRODUCT_CONTENT_ENUM.规格编码,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}, {
			label: "货品规格编码",
			value: PRODUCT_CONTENT_ENUM.货品规格编码,
			showInStockVersion: true,
			showInZeroStockVersion: false,
		}, {
			label: '供应商',
			value: PRODUCT_CONTENT_ENUM.供应商,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}, {
			label: "市场",
			value: PRODUCT_CONTENT_ENUM.市场,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}, {
			label: "档口",
			value: PRODUCT_CONTENT_ENUM.档口,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}]
	}
];

// 0库存版本
export const LOCAL_AFTERSALE_TRADE_ZERO_SETTING = "returnLabelAftersaleTradeZeroProductSetting";

export const PRODUCT_SETTING_ZERO_OBJECT = [
	{
		name: "商品展示",
		key: "good",
		list: [
			{
				label: "商品标题",
				value: PRODUCT_CONTENT_ENUM.商品标题,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			},
			{
				label: "简称",
				value: PRODUCT_CONTENT_ENUM.货品简称,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			},
			{
				label: "商家编码",
				value: PRODUCT_CONTENT_ENUM.商家编码,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			},
			{
				label: "商品图片",
				value: PRODUCT_CONTENT_ENUM.商品图片,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			},
		],
	},
	{
		name: "规格展示",
		key: "sku",
		list: [
			{
				label: "规格名称",
				value: PRODUCT_CONTENT_ENUM.规格名称,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			},
			{
				label: "规格别名",
				value: PRODUCT_CONTENT_ENUM.规格别名,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			},
			{
				label: "规格编码",
				value: PRODUCT_CONTENT_ENUM.规格编码,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			}, {
				label: '供应商',
				value: PRODUCT_CONTENT_ENUM.供应商,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			}, {
				label: "市场",
				value: PRODUCT_CONTENT_ENUM.市场,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			}, {
				label: "档口",
				value: PRODUCT_CONTENT_ENUM.档口,
				showInStockVersion: true,
				showInZeroStockVersion: true,
			}
		],
	},
];


export enum PRODUCT_SETTING_OBJECT_ENUM {
	售后单申请时间 = 'afterSalesTimeAp',
	售后单修改时间 = 'afterSalesTimeEd',
	订单付款时间 = 'orderPayTime'
}



export const PRODUCT_SETTING_OBJECT_TIMMING = [
	{
		name: "时间展示",
		key: "time",
		list: [{
			label: "售后单申请时间",
			value: PRODUCT_SETTING_OBJECT_ENUM.售后单申请时间,
			showInStockVersion: true,
			showInZeroStockVersion: true,
			line: true
		}, {
			label: "售后单修改时间",
			value: PRODUCT_SETTING_OBJECT_ENUM.售后单修改时间,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}, {
			label: "订单付款时间",
			value: PRODUCT_SETTING_OBJECT_ENUM.订单付款时间,
			showInStockVersion: true,
			showInZeroStockVersion: true,
		}]
	},
];

export const DEFAULT_PRODUCT_CONTENT_SETTING = [PRODUCT_CONTENT_ENUM.货品图片,PRODUCT_CONTENT_ENUM.货品规格编码, PRODUCT_CONTENT_ENUM.商品标题, PRODUCT_CONTENT_ENUM.规格名称,PRODUCT_CONTENT_ENUM.中图];

export const DEFAULT_ZERO_PRODUCT_CONTENT_SETTING = [PRODUCT_CONTENT_ENUM.商品图片,PRODUCT_CONTENT_ENUM.货品规格编码, PRODUCT_CONTENT_ENUM.商品标题, PRODUCT_CONTENT_ENUM.规格名称,PRODUCT_CONTENT_ENUM.中图];

export const DEFAULT_BUYNICK_INFO_SETTING = [
	PRODUCT_SETTING_OBJECT_ENUM.售后单申请时间,
	PRODUCT_SETTING_OBJECT_ENUM.售后单修改时间,
	PRODUCT_SETTING_OBJECT_ENUM.订单付款时间
];

// 时间类型
export const timeTypeOptions_aftersale = [
	{
		label: "申请时间",
		value: "create"
	},
	{
		label: "修改时间",
		value: "modified"
	},
	{
		label: "包裹签收时间",
		value: "sign"
	},
	{
		label: "确认收货时间",
		value: "confirm"
	}
]

// 平台退款状态
export const refundStatusListOptions_aftersale = [
	{ label: '待卖家同意', value: '1' },
	{ label: '待买家退货', value: '2' },
	{ label: '待卖家确认收货', value: '3' },
	{ label: '待发出换货商品', value: '8' },
	{ label: '换货补寄待收货', value: '9' },
	{ label: '退款成功', value: '4' },
	{ label: '退款关闭', value: '5' },
	{ label: '卖家拒绝退款', value: '6' },
	{ label: '待买家修改', value: '7' },
	{ label: '其他', value: '10' }
]
// 审核状态
export const reviewStatusOptions_aftersale = [
	{ label: '全部状态', value: 'ALL' },
	{ label: '未审核', value: 'false' },
	{ label: '已审核', value: 'true' },
]

// 售后工单状态
export const refundSystemStatusListOptions_aftersale = [
	{ label: '待处理', value: REFUND_SYSTEM_STATUS.待处理 },
	{ label: '处理中', value: REFUND_SYSTEM_STATUS.处理中 },
	{ label: '已完成', value: REFUND_SYSTEM_STATUS.已完成 },
	{ label: '已关闭', value: REFUND_SYSTEM_STATUS.已关闭 }
]
// 工单类型
export const refundSystemTypeListOptions_aftersale =[
	{ label: '仅退款', value: 1 },
	{ label: '拒收退货', value: 2 },
	{ label: '退货退款', value: 3 },
	{ label: '换货', value: 4 },
	{ label: '补发', value: 5 }
]
// 订单状态
export const tradeStatusOptions_aftersale =[
	{ label: '全部状态', value: 'ALL_STATUS' },
	{ label: '待发货', value: 'WAIT_SELLER_SEND_GOODS' },
	{ label: '已发货', value: 'WAIT_BUYER_CONFIRM_GOODS' },
	{ label: '交易成功', value: 'TRADE_FINISHED' },
	{ label: '已关闭', value: 'TRADE_CLOSED' }
]
// 是否无主件
export const isNoTradeMessOptions_aftersale =[
	{ label: '全部状态', value: '' },
	{ label: '是', value: 1 },
	{ label: '否', value: 2 },
]
// 即将超时
export const timeOutSearchOptions_aftersale =[
	{ label: '6小时内超时', value: 'LESS_THAN_SIX_HOURS' },
	{ label: '12小时内超时', value: 'LESS_THAN_TWELVE_HOURS' },
	{ label: '24小时内超时', value: 'LESS_THAN_TWENTY_FOUR_HOURS' },
	{ label: '48小时内超时', value: 'LESS_THAN_FORTY_EIGHT_HOURS' },
]
// 售后标签
export const refundTagTypeOptions_aftersale =[
	{ label: '售后标签(包含)', value: 1 },
	{ label: '售后标签(不包含)', value: 0 },
]
// 售后标签
export const refundTagListOptions_aftersale =[
	{ label: '平台极速退款', value: 'speedRefund' },
	{ label: '退货物流同单多售后', value: 'sidSameTradeMoreRefund' },
	{ label: '退货物流异单多售后', value: 'sidDifferentTradeMoreRefund' },
]
// 换货/补寄订单状态
export const exchangeRefundStatusListOptions_aftersale =[
	{ label: '未创建', value: '1' },
	{ label: '已创建货物待发出', value: '2' },
	{ label: '已创建货物已发出', value: '3' },
]
// 自发/代发
export const scmRefundTypeOptions_aftersale =[
	{ label: '全部', value: '' },
	{ label: '自发售后单', value: 'ownSend' },
	{ label: '代发售后单', value: 'agentSend' },
]
// 收货状态
export const confirmItemStatusOptions_aftersale =[
	{ label: '未收货', value: 0 },
	{ label: '部分收货', value: 1 },
	{ label: '全部收货', value: 2 },
	{ label: '超收', value: 3 },
]

// 入库状态
export const handleReturnStatusOptions_aftersale =[
	{ label: '未入库', value: 0 },
	{ label: '部分入库', value: 1 },
	{ label: '全部入库', value: 2 },
	{ label: '超入', value: 3 },
]
// 是否提交供应商
export const distributorIsPushOptions_aftersale =[
	{ label: '未提交', value: 0 },
	{ label: '已提交', value: 1 },
]
// 供应商确认状态
export const supplierIsConfirmOptions_aftersale =[
	{ label: '未确认', value: 0 },
	{ label: '已确认', value: 1 },
]
// 售后异常- 库存版
export const refundExceptionListOptions_aftersale =[
	{ label: '退货商品未绑定', value: 'noBindReturnItem' }
]
// 申请售后类型
export const applyRefundFeeOptions_aftersale =[
	{ label: '申请售后金额', value: 'applyRefundFee' },
	{ label: '申请售后数量', value: 'applyRefundNum' },
]
// 发货/退货物流
export const invoiceNoOptions_aftersale =[
	{
		label: "发货/退货物流",
		value: 1
	},
	{
		label: "退货物流",
		value: 2
	},
	{
		label: "发货物流",
		value: 3
	}
]

// 货物状态
export const goodsStatusOptions = [
	{
		label: "无状态",
		options: [
			{ label: "无状态", value: "NO_STATUS" }
		]
	},
	{
		label: "未发货仅退款",
		options: [
			{ label: "未发货", value: "NO_SENT" }
		]
	},
	{
		label: "已发货仅退款",
		options: [
			{ label: "未揽收", value: "HAS_SEND_WAIT_ACCEPT" },
			{ label: "取消揽收", value: "HAS_SEND_CANCEL" },
			{ label: "发货在途", value: "HAS_SEND_DELIVERING" },
			{ label: "买家已签收", value: "HAS_SEND_SIGN" },
			{ label: "退回在途", value: "HAS_SEND_REFUSE" },
			{ label: "退回签收", value: "HAS_SEND_RETURN_SIGN" },
			{ label: "退回收货", value: "HAS_SEND_RETURN_SIGN_CONFIRM" }
		]
	},
	{
		label: "退货退款/换货",
		options: [
			{ label: "买家未退货", value: "WAIT_BUYER_RETURN_GOODS" },
			{ label: "退货在途", value: "GOODS_RETURN_DELIVERING" },
			{ label: "退货签收", value: "GOODS_RETURN_SIGN" },
			{ label: "退货收货", value: "GOODS_RETURN_SIGN_CONFIRM" }
		]
	}
];

export enum supplierOptionsEnum {
	'供应商(包含)' = "supplierContain",
	// 供应商不包含 = "supplierNotContain",
}
