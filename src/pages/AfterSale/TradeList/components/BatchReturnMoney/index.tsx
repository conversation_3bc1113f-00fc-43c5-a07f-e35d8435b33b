import React, { useEffect, useState } from 'react';
import { Button, Form, Input, Modal, Progress, Select, Table } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { useForm } from 'antd/es/form/Form';
import { BatchAgreeRefundFeeApi, BatchAgreeRefundGoodsApi, BatchConfirmReturnItemApi, BatchHandleReturnItemApi, CheckAfterAuthorityApi, CheckAfterAuthorityForBatchApi, QueryRefundAddressNewApi } from '@/apis/aftersale/trade';
import events from "@/utils/events";
import { authModal2, afterSaleChannelText } from '../../utils';
import { BatchAgreeRefundFeeResponse, BatchAgreeRefundGoodsResponse } from '@/types/schemas/aftersale/trade';
import message from '@/components/message';
import { afterSaleOperateType, BtnEnum, operateTypeToBtnEnum } from '../../constants';
import SelectRefundAddress from '../SelectRefundAddress';
import { PLAT_MAP, PLAT_JD, PLAT_TB, PLAT_ALI, PLAT_C2M, PLAT_HAND, PLAT_OTHER } from '@/constants';
import s from './index.module.scss';
import { batchHandleRes } from '@/utils/batchApiRequest';
import { isSourceHand } from '@/components-biz/ShopListSelect/shopListUtils';
import { copyToPaste } from '@/utils';

const confirmModalMap = {
	title: {
		[afterSaleOperateType.同意仅退款]: '确定同意仅退款吗？',
		[afterSaleOperateType.同意退货退款]: '确定同意退货退款吗？'
	}
};
const resultModalMap = {
	title: {
		[afterSaleOperateType.同意仅退款]: '同意仅退款',
		[afterSaleOperateType.同意退货]: '同意退货',
		[afterSaleOperateType.同意退货退款]: '批量同意退货退款',
		[afterSaleOperateType.确认收货]: '确认收货',
		[afterSaleOperateType.退货入库]: '退货入库',
	},
	info: {
		[afterSaleOperateType.同意仅退款]: '仅退款',
		[afterSaleOperateType.同意退货]: '退货',
		[afterSaleOperateType.同意退货退款]: '退货退款'
	}
};

const handleGoodsList = [
	{
		value: 'RU_KU',
		label: '退货入库'
	}, {
		value: 'XIAO_HUI',
		label: '销毁'
	}, {
		value: 'TUI_HUI',
		label: '退回'
	}
];

type IBatchAgreeRefundFeeProps = BatchAgreeRefundFeeResponse['data'];
type IBatchAgreeRefundGoodsProps = BatchAgreeRefundGoodsResponse['data'];
interface IInitPropsProps {
	operateType?: any,
	afterOk?: () => void,
	list?: any[]
}

let originList = [];
export default function BatchReturnMoney() {
	const [initProps, setInitProps] = useState<IInitPropsProps>({});
	const [loading, setLoading] = useState(false);
	const [captchaIsVerifying, setCaptchaIsVerifying] = useState(false);
	const [curType, setCurType] = useState('');
	const [confirmVisible, setConfirmVisible] = useState(false);
	const [receiveVisible, setReceiveVisible] = useState(false);
	const [handleVisible, setHandleVisible] = useState(false);
	const [resultVisible, setResultVisible] = useState(false);
	const [batchRefundAddressVisible, setBatchRefundAddressVisible] = useState(false); // 批量同意退货有多个退货地址二次确认弹框
	const [refundResult, setRefundResult] = useState<IBatchAgreeRefundFeeProps>({});
	const [isAllTb, setIsAllTb] = useState(false);
	const [isAllJd, setIsAllJd] = useState(false);
	const [codeVisible, setCodeVisible] = useState(false);
	const [msgCode, setMsgCode] = useState('');
	const [addressList, setAddressList] = useState([]);

	const [addressVisible, setAddressVisible] = useState(false);
	const [unsupportedTypesVisible, setUnsupportedTypesVisible] = useState(false);
	const [normalListCount, setNormalListCount] = useState(0);
	const [filteredNormalList, setFilteredNormalList] = useState([]);
	const [agreeRefundType, setAgreeRefundType] = useState<number>(); // 退款方式 1-TB退款验证 2-分批处理
	const [progressVisible, setProgressVisible] = useState(false); // 进度弹框
	const [progressInfo, setProgressInfo] = useState({ // 进度信息
		current: 0,
		total: 0,
		currentBatch: 0,
		totalBatch: 0,
		successCount: 0,
		failCount: 0,
		processingText: '正在处理...'
	});

	const [form] = useForm();


	useEffect(() => {
		events.on('batchReturnMoney', initHandle);
		return () => {
			events.off('batchReturnMoney', initHandle);
		};
	}, []);

	const initHandle = (data) => {
		setInitProps(data);
		originList = data.list;
		setCurType(data.operateType);
		// eslint-disable-next-line default-case
		switch (data.operateType) {
			case afterSaleOperateType.同意仅退款:
				if (checkInvalid()) return;
				if (checkUnsupportedTypes()) return;
				checkAuth();
				break;
			case afterSaleOperateType.同意退货:
				if (checkInvalid()) return;
				if (checkUnsupportedTypes()) return;
				getAddress();
				break;
			case afterSaleOperateType.同意退货退款:
				if (checkInvalid()) return;
				if (checkUnsupportedTypes()) return;
				checkAuth();
				break;
			case afterSaleOperateType.确认收货:
				setReceiveVisible(true);
				break;
			case afterSaleOperateType.退货入库:
				form.setFieldsValue({ handleType: 'RU_KU' });
				setHandleVisible(true);
				break;
		}
	};

	// 批量处理退货
	const getAddress = async() => {
		console.log('%c [ originList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', originList);
		const sellerIdList = Array.from(new Set(originList?.map(i => i.sellerId)));
		const refundInfoIds = Array.from(new Set(originList?.map(i => i.id)));
		// 查询售后默认地址及分销供应商售后地址
		const { refundAddressDTOList, refundRelateAddrList } = await QueryRefundAddressNewApi({ refundInfoIds });
		console.time('处理售后地址');
		const list = sellerIdList.map(sellerId => {
			// 找到同一个店铺的售后地址集合
			let sameSellerIdList = refundAddressDTOList?.filter(v => v.sellerId == sellerId) || []; 
			// 找到同样店铺的所有的售后地址id
			let sameSellerIdAddressId = sameSellerIdList?.map(item => item.id) || [];
			// 找到售后地址对应的售后单id
			let sameSellerIdAddressList = refundRelateAddrList?.filter(item => sameSellerIdAddressId?.includes(item.addressId)) || [];
			if (sameSellerIdList?.length) {
				let texts = sameSellerIdList.map(i => {
					return `${i?.refundName} ${i?.refundPhone} ${i?.refundProvince}${i?.refundCity}${i?.refundDistrict}${i?.refundDetail}`;
				})?.join(';\r\n');

				return {
					...sameSellerIdList[0],
					text: texts,
					sameSellerIdAddressId,
					sameSellerIdAddressList,
				};
			} else {
				let originSeller = originList?.find(v => v.sellerId == sellerId);
				return {
					sellerId: originSeller.sellerId,
					platform: originSeller.platform,
					sellerNick: originSeller.sellerNick,
				};
			}
		});
		console.timeEnd('处理售后地址');

		form.resetFields();
		setAddressList(list);
		setAddressVisible(true);
		form.setFieldsValue({
			reviewReasonCode: 1
		});
		
	};

	const checkInvalid = () => {
		// 移除手工单的过滤，因为现在统一在 checkUnsupportedTypes 中处理
		if (!originList?.length) {
			return true;
		}
		return false;
	};

	// 校验是否包含不支持的售后单类型
	const checkUnsupportedTypes = () => {
		// 检查1688平台
		const hasAli = originList.some(i => i.platform === PLAT_ALI);
		// 检查淘工厂平台
		const hasC2M = originList.some(i => i.platform === PLAT_C2M);
		// 检查手工售后单
		const hasHand = originList.some(i => i.platform === PLAT_HAND || isSourceHand(i));
		// 检查无主售后单
		const hasNoTradeMess = originList.some(i => i.isNoTradeMess);

		const afterSaleChannelError = originList.some(i => [afterSaleChannelText.手工录入, afterSaleChannelText.订单号搜索].includes(i.afterSaleChannel));

		const hasOther = originList.some(i => i.platform === PLAT_OTHER);
		
		// 检查京喜订单
		const hasJingxi = originList.some(i => i.refundTagList?.includes('jxTrade'));

		if (hasAli || hasC2M || hasHand || hasNoTradeMess || hasOther || afterSaleChannelError || hasJingxi) {
			// 过滤掉异常数据，计算正常数据数量
			const normalList = originList.filter(i => i.platform !== PLAT_ALI 
				&& i.platform !== PLAT_C2M 
				&& i.platform !== PLAT_HAND 
				&& !i.isNoTradeMess
				&& i.platform !== PLAT_OTHER
				&& !isSourceHand(i)
				&& i.afterSaleChannel !== afterSaleChannelText.手工录入
				&& i.afterSaleChannel !== afterSaleChannelText.订单号搜索
				&& !i.refundTagList?.includes('jxTrade'));
			
			// 显示自定义确认弹窗
			showUnsupportedTypesModal(normalList);
			return true;
		}
		return false;
	};

	// 显示不支持类型的弹窗
	const showUnsupportedTypesModal = (normalList) => {
		setNormalListCount(normalList.length);
		setFilteredNormalList(normalList);
		setUnsupportedTypesVisible(true);
	};

	// 处理过滤异常数据并继续操作
	const handleFilterAndContinue = (normalList) => {
		if (normalList.length === 0) {
			message.error('请选择售后单后操作');
			return;
		}
		
		// 更新originList为过滤后的正常数据
		originList = normalList;
		
		// 继续执行对应的操作
		switch (curType) {
			case afterSaleOperateType.同意仅退款:
			case afterSaleOperateType.同意退货退款:
				checkAuth();
				break;
			case afterSaleOperateType.同意退货:
				getAddress();
				break;
			default:
				break;
		}
	};

	// 校验售后权限，使用新接口
	const checkAuth = () => {
		setIsAllTb(originList.every(i => i.platform === PLAT_TB));
		setIsAllJd(originList.every(i => i.platform === PLAT_JD));
		const sellerIdList = Array.from(new Set(originList?.map(i => i.sellerId)));
		
		// 按店铺分组统计售后单数量
		const sellerRefundCountMap = {};
		originList.forEach(i => {
			if (sellerRefundCountMap[i.sellerId]) {
				sellerRefundCountMap[i.sellerId] += 1;
			} else {
				sellerRefundCountMap[i.sellerId] = 1;
			}
		});
		
		const sellerRefundNumDtoList = sellerIdList.map(sellerId => ({
			sellerId,
			refundNum: sellerRefundCountMap[sellerId]
		}));
		
		CheckAfterAuthorityForBatchApi({ 
			// sellerIdList, // 老的参数不要
			sellerRefundNumDtoList,
		}).then(res => {
			// 退款方式 1-TB退款验证 2-分批处理
			setAgreeRefundType(res?.agreeRefundType);
			setConfirmVisible(true);
		}).catch(err => {
			// 判断是否有退款权限
			authModal2(err);
		});
	};

	// 同意退款
	const onAgreeRefund = async() => {
		setConfirmVisible(false);
		
		// 根据agreeRefundType选择处理逻辑
		console.log('%c [ agreeRefundType ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', String(agreeRefundType));
		if (String(agreeRefundType) === '2') {
			// 分批处理逻辑
			await onBatchAgreeRefund();
		} else {
			// 原有的TB退款验证逻辑
			await onOriginalAgreeRefund();
		}
	};

	// 原有的同意退款逻辑
	const onOriginalAgreeRefund = async() => {
		setLoading(true);
		try {
			const res: IBatchAgreeRefundFeeProps = await BatchAgreeRefundFeeApi({
				ids: originList.map(i => i.id),
				isSecond: false,
				operateType: curType,
				requestVersion: 'V2',
			});
			if (res.code || res.batchResults.some(i => i.errorMessage == '验证码已发送')) {
				setCodeVisible(true);
				setMsgCode('');
			} else {
				setRefundResult(res);
				setResultVisible(true);
			}
		} catch (error) { console.log(error); }
		setLoading(false);
	};

	// 优化后的并发处理逻辑 - 添加详细打印信息
	const onBatchAgreeRefund = async() => {
		// 数据准备逻辑保持不变...
		const platformGroups = {};
		originList.forEach(item => {
			const platform = item.platform;
			if (!platformGroups[platform]) {
				platformGroups[platform] = [];
			}
			platformGroups[platform].push(item);
		});

		// 创建任务队列
		const batchSize = 5;
		const threadNum = 5; // 并发线程数
		const taskQueue = [];
		
		// 构建任务队列（保持原有的平台轮询逻辑）
		const platformKeys = Object.keys(platformGroups);
		const remainingData = { ...platformGroups };
		
		while (true) {
			const batchData = [];
			for (let j = 0; j < batchSize; j++) {
				let found = false;
				for (const platform of platformKeys) {
					if (remainingData[platform] && remainingData[platform].length > 0) {
						batchData.push(remainingData[platform].shift());
						found = true;
						break;
					}
				}
				if (!found) break;
			}
			if (batchData.length > 0) {
				taskQueue.push(batchData);
			} else {
				break;
			}
		}

		// 并发控制变量
		let timer = 0;
		let completedCount = 0;
		const totalTasks = taskQueue.length;
		const totalItems = originList.length;
		const allResults = [];
		let successCount = 0;
		let failCount = 0;
		let batchIndex = 0; // 批次索引
		let currentCount = 0; // 新增

		console.log(`%c[批量${curType}] 开始处理，共${totalItems}笔数据，分${totalTasks}批处理，每批最多${batchSize}笔，并发数${threadNum}`, 'color: #1890ff; font-weight: bold;');

		// 显示进度
		setProgressInfo({
			current: 0,
			total: totalItems,
			currentBatch: 0,
			totalBatch: Math.ceil(totalTasks / threadNum),
			successCount: 0,
			failCount: 0,
			processingText: `执行批量${operateTypeToBtnEnum[curType] || curType || ''}中...`
		});
		setProgressVisible(true);

		// API调用函数
		const callApi = async(batchData, callback, currentBatchIndex) => {
			const startIndex = currentBatchIndex * batchSize + 1;
			const endIndex = Math.min(startIndex + batchData.length - 1, totalItems);
			
			console.log(`%c[批量${curType}] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）开始请求...`, 'color: #52c41a;');
			
			try {
				const res = await BatchAgreeRefundFeeApi({
					ids: batchData.map(i => i.id),
					isSecond: false,
					operateType: curType,
					requestVersion: 'V2',
					hideErrorMessage: true,
				});
				
				const batchSuccess = res.successNum || 0;
				const batchFail = res.failedNum || 0;
				const batchTotal = batchData.length;
				
				console.log(`%c[批量${curType}] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）处理完成，共提交${batchTotal}笔，${curType}成功${batchSuccess}笔${batchFail > 0 ? `，失败${batchFail}笔` : ''}`, 
					batchFail > 0 ? 'color: #ff4d4f; font-weight: bold;' : 'color: #52c41a; font-weight: bold;');
				
				// 如果有失败的，打印失败信息
				if (res.batchResults && res.batchResults.some(item => item.errorMessage !== '成功')) {
					const failedItems = res.batchResults.filter(item => item.errorMessage !== '成功');
					console.log(`%c[批量${curType}] 第${currentBatchIndex + 1}批失败详情:`, 'color: #ff4d4f;', failedItems.map(item => `${item.operationId}: ${item.errorMessage}`).join('; '));
				}
				
				callback(res, batchData, currentBatchIndex);
			} catch (error) {
				console.log(`%c[批量${curType}] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）请求异常:`, 'color: #ff4d4f; font-weight: bold;', error);
				
				const errorResult = {
					code: 1,
					message: error.message || error.errorMessage || '请求失败',
					batchResults: batchData.map(item => ({
						operationId: item.id,
						errorMessage: error.message || error.errorMessage || '请求失败',
						success: false
					})),
					successNum: 0,
					failedNum: batchData.length
				};
				callback(errorResult, batchData, currentBatchIndex);
			}
		};

		// 任务处理器 - 资源竞争型
		const handler = () => {
			if (!taskQueue.length) {
				timer++;
				if (timer !== threadNum) return; // 等待所有线程完成
				
				// 所有任务完成
				console.log(`%c[批量${curType}] 所有批次处理完成！总计${totalItems}笔，成功${successCount}笔，失败${failCount}笔`, 
					failCount > 0 ? 'color: #ff4d4f; font-weight: bold; font-size: 14px;' : 'color: #52c41a; font-weight: bold; font-size: 14px;');
				
				const finalResult = {
					successNum: successCount,
					failedNum: failCount,
					batchResults: []
				};
				
				allResults.forEach(result => {
					if (result && result.batchResults) {
						finalResult.batchResults.push(...result.batchResults);
					}
				});

				setTimeout(() => {
					setProgressVisible(false);
					setRefundResult(finalResult);
					setResultVisible(true);
				}, 200);
				return;
			}

			// 处理下一个任务
			const currentBatch = taskQueue.shift();
			const currentBatchIndex = batchIndex++;
			
			callApi(currentBatch, (result, batchData, batchIdx) => {
				completedCount++;
				allResults.push(result);
				
				const batchSuccess = result.successNum || 0;
				const batchFail = result.failedNum || 0;
				successCount += batchSuccess;
				failCount += batchFail;

				currentCount += batchData.length; // 新增

				setProgressInfo(prev => ({
					...prev,
					current: Math.min(currentCount, totalItems), // 修正
					currentBatch: Math.ceil(completedCount / threadNum),
					successCount,
					failCount
				}));
				
				// 打印进度信息
				console.log(`%c[批量${curType}] 进度更新: 已完成${completedCount}/${totalTasks}批，累计成功${successCount}笔，累计失败${failCount}笔`, 'color: #1890ff;');

				// 继续处理下一个任务
				handler();
			}, currentBatchIndex);
		};

		// 启动并发线程
		for (let i = 0; i < threadNum; i++) {
			handler();
		}
	};

	// 淘宝同意退款
	const onAgreeTbRefund = async() => {
		setCaptchaIsVerifying(true);
		const res: IBatchAgreeRefundFeeProps = await BatchAgreeRefundFeeApi({
			ids: originList.map(i => i.id),
			operateType: curType,
			isSecond: true,
			code: msgCode,
			requestVersion: 'V2'
		});
		setCaptchaIsVerifying(false);
		setCodeVisible(false);
		setRefundResult(res);
		setResultVisible(true);
	};

	const onCodeSubmit = () => {
		if (!msgCode) {
			message.error('请填写短信验证码');
		} else {
			onAgreeTbRefund();
		}
	};

	// 选择退货地址回调
	const onChangeAddress = (address:any, index) => {
		console.log('%c [ onChangeAddress ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', address, index);
		if (address) {
			setAddressList(pre => {
				let sameSellerIdAddressId = [address.id];
				let sameSellerIdAddressList = pre[index]?.sameSellerIdAddressList?.map(item => {
					return {
						...item,
						addressId: address?.id,
					};
				});
				pre[index] = {
					...pre[index], // 防止把自定义的数据替换掉这里不能直接使用address
					...address,
					sameSellerIdAddressId,
					sameSellerIdAddressList
				};
				return [...pre];
			});
		}
	};

	const addressColumns = [
		{
			dataIndex: 'platform',
			title: '平台',
			width: 100,
			render: (val) => {
				return PLAT_MAP[val];
			}
		}, {
			dataIndex: 'sellerNick',
			title: '店铺',
			width: 150,
		}, {
			dataIndex: 'text',
			title: '退货地址',
			width: 300,
			render(text, record) {
				let list = text?.split('\r\n');
				if (list?.length > 1) {
					return (
						<div>
							{list?.map(item => (
								<div key={ item } style={ { wordBreak: "break-all" } }>{item}</div>
							))}
						</div>
					);
				}
				return (
					<div>
						<div style={ { wordBreak: "break-all" } }>{text}</div>
					</div>
				);
			}
		}, {
			dataIndex: 'operate',
			title: '操作',
			width: 100,
			render: (_, record, index) => {
				return <SelectRefundAddress filterData={ record } onOkAfter={ (address) => onChangeAddress(address, index) } />;
			}
		}
	];

	// 优化后的同意退货逻辑 - 添加详细打印信息
	const onAgreeRefundGoods = async() => {
		const { remark, reviewReasonCode } = form.getFieldsValue();
		if (addressList.some(i => !i?.sameSellerIdAddressId?.length)) {
			message.error('请选择退货地址');
			return;
		}
		if (!remark) {
			message.error('请输入退货说明');
			return;
		}
		
		// 创建完整的地址映射关系
		const addressMap = {};
		addressList.forEach(seller => {
			seller.sameSellerIdAddressList?.forEach(item => {
				addressMap[item.refundInfoId] = {
					addressId: item.addressId,
					refundInfoId: item.refundInfoId
				};
			});
		});
		
		// 按平台分组数据
		const platformGroups = {};
		originList.forEach(item => {
			const platform = item.platform;
			if (!platformGroups[platform]) {
				platformGroups[platform] = [];
			}
			platformGroups[platform].push(item);
		});

		// 创建任务队列
		const batchSize = 5;
		const threadNum = 5; // 并发线程数
		const taskQueue = [];
		
		// 构建任务队列（保持原有的平台轮询逻辑）
		const platformKeys = Object.keys(platformGroups);
		const remainingData = { ...platformGroups };
		
		while (true) {
			const batchData = [];
			for (let j = 0; j < batchSize; j++) {
				let found = false;
				for (const platform of platformKeys) {
					if (remainingData[platform] && remainingData[platform].length > 0) {
						batchData.push(remainingData[platform].shift());
						found = true;
						break;
					}
				}
				if (!found) break;
			}
			if (batchData.length > 0) {
				taskQueue.push(batchData);
			} else {
				break;
			}
		}

		// 并发控制变量
		let timer = 0;
		let completedCount = 0;
		const totalTasks = taskQueue.length;
		const totalItems = originList.length;
		const allResults = [];
		let successCount = 0;
		let failCount = 0;
		let batchIndex = 0; // 批次索引
		let currentCount = 0; // 新增

		console.log(`%c[批量同意退货] 开始处理，共${totalItems}笔数据，分${totalTasks}批处理，每批最多${batchSize}笔，并发数${threadNum}`, 'color: #1890ff; font-weight: bold;');

		// 显示进度
		setProgressInfo({
			current: 0,
			total: totalItems,
			currentBatch: 0,
			totalBatch: Math.ceil(totalTasks / threadNum),
			successCount: 0,
			failCount: 0,
			processingText: `执行批量同意退货中...`
		});
		setProgressVisible(true);

		// API调用函数
		const callApi = async(batchData, callback, currentBatchIndex) => {
			const startIndex = currentBatchIndex * batchSize + 1;
			const endIndex = Math.min(startIndex + batchData.length - 1, totalItems);
			
			console.log(`%c[批量同意退货] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）开始请求...`, 'color: #52c41a;');
			
			try {
				// 构建对应的addressList
				const batchAddressList = batchData.map(item => addressMap[item.id]).filter(Boolean);
				
				const res = await BatchAgreeRefundGoodsApi({
					ids: batchData.map(i => i.id),
					operateType: afterSaleOperateType.同意退货,
					remark,
					addressList: batchAddressList,
					reviewReasonCode,
				});
				
				const batchSuccess = res.successNum || 0;
				const batchFail = res.failedNum || 0;
				const batchTotal = batchData.length;
				
				console.log(`%c[批量同意退货] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）处理完成，共提交${batchTotal}笔，同意退货成功${batchSuccess}笔${batchFail > 0 ? `，失败${batchFail}笔` : ''}`, 
					batchFail > 0 ? 'color: #ff4d4f; font-weight: bold;' : 'color: #52c41a; font-weight: bold;');
				
				// 如果有失败的，打印失败信息
				if (res.batchResults && res.batchResults.some(item => item.errorMessage !== '成功')) {
					const failedItems = res.batchResults.filter(item => item.errorMessage !== '成功');
					console.log(`%c[批量同意退货] 第${currentBatchIndex + 1}批失败详情:`, 'color: #ff4d4f;', failedItems.map(item => `${item.operationId}: ${item.errorMessage}`).join('; '));
				}
				
				callback(res, batchData, currentBatchIndex);
			} catch (error) {
				console.log(`%c[批量同意退货] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）请求异常:`, 'color: #ff4d4f; font-weight: bold;', error);
				
				const errorResult = {
					code: 1,
					message: error.message || error.errorMessage || '请求失败',
					batchResults: batchData.map(item => ({
						operationId: item.id,
						errorMessage: error.message || error.errorMessage || '请求失败',
						success: false
					})),
					successNum: 0,
					failedNum: batchData.length
				};
				callback(errorResult, batchData, currentBatchIndex);
			}
		};

		// 任务处理器 - 资源竞争型
		const handler = () => {
			if (!taskQueue.length) {
				timer++;
				if (timer !== threadNum) return; // 等待所有线程完成
				
				// 所有任务完成
				console.log(`%c[批量同意退货] 所有批次处理完成！总计${totalItems}笔，成功${successCount}笔，失败${failCount}笔`, 
					failCount > 0 ? 'color: #ff4d4f; font-weight: bold; font-size: 14px;' : 'color: #52c41a; font-weight: bold; font-size: 14px;');
				
				const finalResult = {
					successNum: successCount,
					failedNum: failCount,
					batchResults: []
				};
				
				allResults.forEach(result => {
					if (result && result.batchResults) {
						finalResult.batchResults.push(...result.batchResults);
					}
				});

				setTimeout(() => {
					setProgressVisible(false);
					setRefundResult(finalResult);
					setResultVisible(true);
					setAddressVisible(false);
					setBatchRefundAddressVisible(false);
				}, 200);
				return;
			}

			// 处理下一个任务
			const currentBatch = taskQueue.shift();
			const currentBatchIndex = batchIndex++;
			
			callApi(currentBatch, (result, batchData, batchIdx) => {
				completedCount++;
				allResults.push(result);
				
				const batchSuccess = result.successNum || 0;
				const batchFail = result.failedNum || 0;
				successCount += batchSuccess;
				failCount += batchFail;

				currentCount += batchData.length; // 新增

				setProgressInfo(prev => ({
					...prev,
					current: Math.min(currentCount, totalItems), // 修正
					currentBatch: Math.ceil(completedCount / threadNum),
					successCount,
					failCount
				}));
				
				// 打印进度信息
				console.log(`%c[批量同意退货] 进度更新: 已完成${completedCount}/${totalTasks}批，累计成功${successCount}笔，累计失败${failCount}笔`, 'color: #1890ff;');

				// 继续处理下一个任务
				handler();
			}, currentBatchIndex);
		};

		// 启动并发线程
		for (let i = 0; i < threadNum; i++) {
			handler();
		}
	};

	// 批量收货
	const onReceiveGoods = async() => {
		const ids = originList?.map(i => i.id);
		const res = await BatchConfirmReturnItemApi({ ids });
		const result = batchHandleRes(res);
		setReceiveVisible(false);
		setRefundResult(result);
		setResultVisible(true);
	};

	// 批量退货入库 - 改造为并发处理
	const onHandleGoods = async() => {
		const { handleType } = form.getFieldsValue();
		
		// 按平台分组数据
		const platformGroups = {};
		originList.forEach(item => {
			const platform = item.platform;
			if (!platformGroups[platform]) {
				platformGroups[platform] = [];
			}
			platformGroups[platform].push(item);
		});

		// 创建任务队列
		const batchSize = 5;
		const threadNum = 2; // 并发线程数改为2
		const taskQueue = [];
		
		// 构建任务队列（保持原有的平台轮询逻辑）
		const platformKeys = Object.keys(platformGroups);
		const remainingData = { ...platformGroups };
		
		while (true) {
			const batchData = [];
			for (let j = 0; j < batchSize; j++) {
				let found = false;
				for (const platform of platformKeys) {
					if (remainingData[platform] && remainingData[platform].length > 0) {
						batchData.push(remainingData[platform].shift());
						found = true;
						break;
					}
				}
				if (!found) break;
			}
			if (batchData.length > 0) {
				taskQueue.push(batchData);
			} else {
				break;
			}
		}

		// 并发控制变量
		let timer = 0;
		let completedCount = 0;
		const totalTasks = taskQueue.length;
		const totalItems = originList.length;
		const allResults = [];
		let successCount = 0;
		let failCount = 0;
		let batchIndex = 0; // 批次索引
		let currentCount = 0; // 新增

		console.log(`%c[批量退货入库] 开始处理，共${totalItems}笔数据，分${totalTasks}批处理，每批最多${batchSize}笔，并发数${threadNum}`, 'color: #1890ff; font-weight: bold;');

		// 显示进度
		setProgressInfo({
			current: 0,
			total: totalItems,
			currentBatch: 0,
			totalBatch: Math.ceil(totalTasks / threadNum),
			successCount: 0,
			failCount: 0,
			processingText: `执行批量退货入库中...`
		});
		setProgressVisible(true);

		// API调用函数
		const callApi = async(batchData, callback, currentBatchIndex) => {
			const startIndex = currentBatchIndex * batchSize + 1;
			const endIndex = Math.min(startIndex + batchData.length - 1, totalItems);
			
			console.log(`%c[批量退货入库] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）开始请求...`, 'color: #52c41a;');
			
			try {
				const res = await BatchHandleReturnItemApi({ 
					ids: batchData.map(i => i.id), 
					handleType 
				});
				// console.log('%c [ res ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
				
				const batchSuccess = res.successNum || 0;
				const batchFail = res.failedNum || 0;
				const batchTotal = batchData.length;
				
				console.log(`%c[批量退货入库] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）处理完成，共提交${batchTotal}笔，退货入库成功${batchSuccess}笔${batchFail > 0 ? `，失败${batchFail}笔` : ''}`, 
					batchFail > 0 ? 'color: #ff4d4f; font-weight: bold;' : 'color: #52c41a; font-weight: bold;');
				
				// 如果有失败的，打印失败信息
				if (res.batchResults && res.batchResults.some(item => item.errorMessage !== '成功')) {
					const failedItems = res.batchResults.filter(item => item.errorMessage !== '成功');
					console.log(`%c[批量退货入库] 第${currentBatchIndex + 1}批失败详情:`, 'color: #ff4d4f;', failedItems.map(item => `${item.operationId}: ${item.errorMessage}`).join('; '));
				}
				
				callback(res, batchData, currentBatchIndex);
			} catch (error) {
				console.log(`%c[批量退货入库] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）请求异常:`, 'color: #ff4d4f; font-weight: bold;', error);
				
				const errorResult = {
					code: 1,
					message: error.message || error.errorMessage || '请求失败',
					batchResults: batchData.map(item => ({
						operationId: item.id,
						errorMessage: error.message || error.errorMessage || '请求失败',
						success: false
					})),
					successNum: 0,
					failedNum: batchData.length
				};
				callback(errorResult, batchData, currentBatchIndex);
			}
		};

		// 任务处理器 - 资源竞争型
		const handler = () => {
			if (!taskQueue.length) {
				timer++;
				if (timer !== threadNum) return; // 等待所有线程完成
				
				// 所有任务完成
				console.log(`%c[批量退货入库] 所有批次处理完成！总计${totalItems}笔，成功${successCount}笔，失败${failCount}笔`, 
					failCount > 0 ? 'color: #ff4d4f; font-weight: bold; font-size: 14px;' : 'color: #52c41a; font-weight: bold; font-size: 14px;');
				
				const finalResult = {
					successNum: successCount,
					failedNum: failCount,
					batchResults: []
				};
				
				allResults.forEach(result => {
					if (result && result.batchResults) {
						finalResult.batchResults.push(...result.batchResults);
					}
				});

				setTimeout(() => {
					setProgressVisible(false);
					setRefundResult(finalResult);
					setResultVisible(true);
					setHandleVisible(false);
				}, 200);
				return;
			}

			// 处理下一个任务
			const currentBatch = taskQueue.shift();
			const currentBatchIndex = batchIndex++;
			
			callApi(currentBatch, (result, batchData, batchIdx) => {
				completedCount++;
				allResults.push(result);
				
				const batchSuccess = result.successNum || 0;
				const batchFail = result.failedNum || 0;
				successCount += batchSuccess;
				failCount += batchFail;

				currentCount += batchData.length; // 新增

				setProgressInfo(prev => ({
					...prev,
					current: Math.min(currentCount, totalItems), // 修正
					currentBatch: Math.ceil(completedCount / threadNum),
					successCount,
					failCount
				}));
				
				// 打印进度信息
				console.log(`%c[批量退货入库] 进度更新: 已完成${completedCount}/${totalTasks}批，累计成功${successCount}笔，累计失败${failCount}笔`, 'color: #1890ff;');

				// 继续处理下一个任务
				handler();
			}, currentBatchIndex);
		};

		// 启动并发线程
		for (let i = 0; i < threadNum; i++) {
			handler();
		}
	};

	const cancelResultModal = () => {
		setResultVisible(false);
		if (refundResult?.successNum > 0) {
			initProps?.afterOk();
		}
	};

	const loadingAction = async(action) => {
		setLoading(true);
		try {
			await action();
		} catch (error) {
			console.log(error);
		}
		setLoading(false);
	};

	const handleAgreeRefundGoods = () => {
		

		// 如果批量同意退货里面有多个退货地址的，需要二次确认弹框
		if (addressList.filter(address => address?.sameSellerIdAddressId?.length > 1)?.length > 0) {
			setBatchRefundAddressVisible(true);
		} else {
			loadingAction(onAgreeRefundGoods);
		}
		
	};

	const copyFailedOrderNumbers = () => {
		// 筛选出失败的订单号
		const failedOrderNumbers = refundResult?.batchResults
			?.filter(item => item.errorMessage && item.errorMessage !== '成功')
			?.map(item => item.operationId)
			?.filter(Boolean) || [];
		
		if (failedOrderNumbers.length === 0) {
			message.info('没有失败的订单号需要复制');
			return;
		}
		
		// 将单号用换行符连接
		const copyText = failedOrderNumbers.join(',');
		
		copyToPaste(copyText);
	};

	// 判断是否需要显示复制按钮
	const shouldShowCopyButton = () => {
		return [
			afterSaleOperateType.同意仅退款,
			afterSaleOperateType.同意退货,
			afterSaleOperateType.同意退货退款,
			afterSaleOperateType.退货入库
		].includes(curType as any);
	};

	return (
		<>
			<Modal
				title={ curType == afterSaleOperateType.同意退货退款 ? "同意退货退款" : "同意仅退款" }
				visible={ confirmVisible }
				centered
				closable
				confirmLoading={ loading }
				width={ 500 }
				onOk={ onAgreeRefund }
				onCancel={ () => setConfirmVisible(false) }
			>
				<p className="r-fs-18 r-fw-500 r-ta-c">{confirmModalMap.title[curType]}</p>
				{
					isAllJd && (
						<div className="r-mt-10">
							<p className="r-mt-10">退款总金额：平台将根据原订单实付金额退款</p>
							<p className="r-mt-10">是否退运费：不退运费，可通过平台小额打款功能补偿运费</p>
						</div>
					)
				}
				<p className="r-mt-10">说明</p>
				<p className="r-mt-10">1、退款成功将会同步到平台，直接将资金退给买家</p>
				<p className="r-mt-10">2、已发货的售后单，需要线下联系快递追回</p>
				{isAllTb && (
					<p className="r-mt-10">3、淘系确定退款将平台子账号绑定手机号发送验证码</p>
				)}
				
			</Modal>
			<Modal
				title="系统提示"
				visible={ codeVisible }
				centered
				closable
				width={ 500 }
				maskClosable={ false }
				onOk={ onCodeSubmit }
				confirmLoading={ captchaIsVerifying }
				onCancel={ () => setCodeVisible(false) }
			>
				<div>
					<p>已发送验证码给子账号绑定手机，请填写短信验证码</p>
					<p className="r-flex r-ai-c r-mt-12">短信验证码：<Input style={ { width: 132 } } value={ msgCode } onChange={ (e) => setMsgCode(e.target.value) } /></p>
				</div>
			</Modal>
			<Modal
				title="系统提示"
				visible={ resultVisible }
				centered
				closable
				width={ 600 }
				onCancel={ cancelResultModal }
				footer={ (
					<div className="r-flex r-jc-sb r-ai-c">
						<div>
							{shouldShowCopyButton() && refundResult?.failedNum > 0 && (
								<Button onClick={ copyFailedOrderNumbers }>
									批量复制所有失败单号
								</Button>
							)}
						</div>
						<Button type="primary" onClick={ () => setResultVisible(false) }>
							确定
						</Button>
					</div>
				) }
				afterClose={ cancelResultModal }
			>
				<p className="r-fs-18 r-fw-500 r-ta-c">{resultModalMap.title[curType]}</p>
				{
					resultModalMap.info[curType] ? <p className="r-ta-c r-mt-5">提交同意{resultModalMap.info[curType]}后，平台会有延迟，请不要短时间内多次提交</p> : null
				}
				<div className="r-flex r-jc-c r-mb-10">
					<div>成功：<span className="r-fs-16 r-c-success">{refundResult?.successNum}</span> <span className="r-fs-16">单</span></div>
					<div className="r-ml-20">失败：<span className="r-fs-16 r-c-error">{refundResult?.failedNum}</span> <span className="r-fs-16">单</span></div>
				</div >
				<div style={ { maxHeight: 300, overflow: 'auto' } } >
					{
						refundResult?.batchResults?.map(i => <p key={ i?.operationId }>售后单号：{i?.operationId} {i?.errorMessage?.replace(/售后订单/g, '售后单')}</p>)
					}
				</div>
			</Modal>
			<Modal
				visible={ addressVisible }
				title="同意退货"
				centered
				closable
				width={ 700 }
				confirmLoading={ loading }
				maskClosable={ false }
				onOk={ () => handleAgreeRefundGoods() }
				onCancel={ () => setAddressVisible(false) }
			>
				<Form
					form={ form }
				>
					<Form.Item
						label="退货方式"
						rules={ [{ required: true, message: '' }] }
					>
						<Select style={ { width: "300px" } } defaultValue="1" disabled>
							<Select.Option value="1">客户发货</Select.Option>
							{/* <Select.Option value="2">上门取件</Select.Option> */}
						</Select>
					</Form.Item>
					<Form.Item
						label="退货地址"
						name="addressId"
						rules={ [{ required: true, message: '' }] }
						style={ { marginBottom: 0 } }
					/>
					<div className="r-flex r-ai-c r-mb-20">
						<Table dataSource={ addressList } columns={ addressColumns } pagination={ false } />
					</div>
					{
						originList.some(item => item.platform === PLAT_JD) && (
							<Form.Item
								label="审核原因"
								name="reviewReasonCode"
								rules={ [{ required: true, message: '' }] }
							>
								<Select style={ { width: "300px" } } disabled>
									<Select.Option value={ 1 }>7天无理由</Select.Option>
									{/* <Select.Option value="2">上门取件</Select.Option> */}
								</Select>
							</Form.Item>
						)
					}
					<Form.Item
						label="退货说明"
						name="remark"
						className={ s.batchModalFormItem }
						rules={ [{ required: true, message: '' }] }
					>
						<Input.TextArea />
					</Form.Item>
				</Form>
			</Modal>
			<Modal
				title={ <div className="r-bold r-c-333 r-fs-16">温馨提示</div> }
				visible={ batchRefundAddressVisible }
				centered
				closable
				maskClosable={ false }
				width={ 500 }
				confirmLoading={ loading }
				okText="继续，同意退货"
				onOk={ () => loadingAction(onAgreeRefundGoods) }
				onCancel={ () => setBatchRefundAddressVisible(false) }
			>
				<p className="r-ta-l">存在店铺有多个不同退货地址的情况，请确认售后退货地址是否无误，避免退货地址错误产生资损。</p>
				<p className="r-c-error r-mt-10">建议前往单笔售后单退货处理</p>
			</Modal>
			<Modal
				title="确认收货"
				visible={ receiveVisible }
				centered
				closable
				width={ 500 }
				confirmLoading={ loading }
				onOk={ () => loadingAction(onReceiveGoods) }
				onCancel={ () => setReceiveVisible(false) }
			>
				<p className="r-fs-18 r-fw-500 r-ta-c">确定批量收货吗？</p>
				<p className="r-mt-20">确认收货后，系统将自动根据剩余可收数量进行收货</p>
			</Modal>
			<Modal
				title="退货入库"
				visible={ handleVisible }
				centered
				closable
				width={ 500 }
				confirmLoading={ loading }
				onOk={ () => loadingAction(onHandleGoods) }
				onCancel={ () => setHandleVisible(false) }
			>
				<p className="r-fs-18 r-fw-500 r-ta-c">确定批量退货入库吗？</p>
				<p className="r-mt-20 r-mb-20">退货入库后，系统将根据对可入库数量，自动根据选择方式完成入库</p>
				<Form
					form={ form }
				>
					<Form.Item
						label="入库方式"
						name="handleType"
					>
						<Select getPopupContainer={ e => e.parentElement } options={ handleGoodsList } />
					</Form.Item>
				</Form>
			</Modal>
			<Modal
				title={ (
					<div className="r-flex r-ai-c">
						<ExclamationCircleFilled style={ { color: '#faad14', fontSize: '20px', marginRight: '8px' } } />
						<span>提示</span>
					</div>
				) }
				visible={ progressVisible }
				centered
				closable={ false }
				width={ 500 }
				maskClosable={ false }
				keyboard={ false }
				footer={ null }
			>
				<div>
					<p className="r-fs-14 r-mb-16">
						执行批量{operateTypeToBtnEnum[curType] || curType || ''}中... {progressInfo.current}/{progressInfo.total}
					</p>
					{/* <div className="r-mb-16">
						<Progress 
							percent={ progressInfo.total > 0 ? Math.round((progressInfo.current / progressInfo.total) * 100) : 0 }
							strokeColor="#1890ff"
							showInfo
						/>
					</div> */}
					<div className="r-fs-12 r-c-666">
						<p>注意：关闭页面会导致任务中断；如过程中不小心关闭页面，</p>
						<p>可至售后单日志查看操作结果</p>
					</div>
				</div>
			</Modal>
			<Modal
				title="系统提示"
				visible={ unsupportedTypesVisible }
				centered
				closable={ false }
				keyboard={ false }
				width={ 500 }
				maskClosable={ false }
				onCancel={ () => setUnsupportedTypesVisible(false) }
				footer={ (
					<div className="r-flex r-ai-c">
						<div key="info" style={ { flex: 1, textAlign: 'left', color: '#666', fontSize: '12px' } }>
							过滤异常后勾选（{normalListCount}单）
						</div>
						{
							normalListCount > 0 ? (
								<div className="r-flex r-ai-c r-jc-e">
									<Button 
										key="confirm" 
										type="primary" 
										onClick={ () => {
											// 计算异常数据
											const normalIds = new Set(filteredNormalList.map(i => i.id));
											const abnormalList = originList.filter(i => !normalIds.has(i.id));
											console.log('【批量操作-正常数据】', filteredNormalList);
											console.log('【批量操作-异常数据】', abnormalList);

											setUnsupportedTypesVisible(false);
											handleFilterAndContinue(filteredNormalList);
										} }
									>
										跳过异常 操作正常数据
									</Button>
									<Button key="cancel" onClick={ () => setUnsupportedTypesVisible(false) }>
										知道了
									</Button>
								</div>
							) : (
								<div className="r-flex r-ai-c r-jc-e">
									<Button key="cancel" type="primary" onClick={ () => setUnsupportedTypesVisible(false) }>
										知道了
									</Button>
								</div>
							)
						}
						
					</div>
				) }
			>
				<p>1688、淘工厂、京喜售后单、手工售后单、无主售后单暂不支持批量同意退款/退货/退货退款操作，请不要勾选相应数据</p>
			</Modal>
		</>
	);
}
