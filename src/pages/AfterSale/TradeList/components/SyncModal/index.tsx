import React, { useState, useEffect } from 'react';
import { Modal, Table, Form, Checkbox, Row, Spin, Progress, Button, Typography, Select, Col, Radio, Input, Alert } from 'antd';
import { useForm } from 'antd/es/form/Form';
import cs from 'classnames';
import { useRequest } from 'ahooks';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import dayjs from 'dayjs';
import { GetSyncRefundResultApi, SyncRefundTradeApi } from '@/apis/aftersale/trade';
import { GetSyncRefundResultRequest, GetSyncRefundResultResponse, SyncRefundTradeRequest } from '@/types/schemas/aftersale/trade';
import { getAllPlats, getShopsByPlat } from '@/components-biz/ShopListSelect/shopListUtils';
import ShopSingleSelect from "@/components-biz/ShopListSelect/shopSingleSelect";
import { platsName } from '../../utils';
import { getShopStatus, PLAT_JD, PLAT_PDD, PLAT_KTT, PLAT_DW, PLAT_YZ } from '@/constants';
import message from "@/components/message";
import { SyncWay } from '@/utils/enum/afterSale';
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import { HighSyncShopItem } from '@/types/trade/search/search';
import UseSyncStatusModal from '../SyncStatusModal/useSyncStatusModal';


interface Props {
	visible:boolean,
	dataSource?:any,
	onCancel: () => void,
	afterOK?: () => void,
}

const { Text } = Typography;

const SyncModal: React.FC<Props> = (props) => {
	const { visible, onCancel, afterOK, dataSource, } = props;
	const [form] = useForm();
	// 所有平台
	const [platformList, setPlatformList] = useState([]);
	// 店铺是否可操作
	const [itemShopDisable, setItemShopDisable] = useState(true);
	// 是否出现销售订单
	const [hasInputTid, setHasInputTid] = useState(false);
	// 已选择平台对应下的店铺
	const [itemShopList, setItemShopList] = useState([]);

	const [platform, setPlatform] = useState('');
	const [syncWay, setSyncWay] = useState(SyncWay.按指定编码同步);
	const [syncShopList, setSyncShopList] = useState<GetSyncRefundResultResponse['data']>([]);

	useEffect(() => {
	// 获取所有平台
		getPlatsList();
	}, []);

	// 手动同步退款单
	const { run: onConfirmSync, loading: syncLoading } = useRequest(SyncRefundTradeApi, {
		manual: true,
		onSuccess: (res) => {
			if (syncWay === SyncWay.按更新时间同步) {
				GetSyncRefundResultApi({
					shopList: res,
					syncWay: SyncWay.按更新时间同步,
				}).then(res1 => {
					let _syncShopList = res1.filter(item => !item.errorMsg);
					let syncErrorShopList = res1.filter(item => item.errorMsg);
					if (syncErrorShopList.length) {
						message.error({
							content: (
								<div>
									{syncErrorShopList.map(item => (
										<div key={ item.sellerId }>{item.sellerNick}：{item.errorMsg}</div>
									))}
								</div>
							)
						});
					}
					if (_syncShopList.length) {
						setSyncShopList(_syncShopList);
						modalClose();
					}
				});
			} else {
				message.success('同步成功！');
				modalClose();
				afterOK();
			}
		},
		
	});

	const modalClose = () => {
		onCancel();
		onResetModal();
	};

	// 重置modal
	const onResetModal = () => {
		form.resetFields();
	};

	// 获取平台
	const getPlatsList = async() => {
		const plats = await getAllPlats();
		setPlatformList(plats);
	};

	// 点击开始同步
	const modalOk = async() => {
		console.log("modalOk -> 点击开始同步");
		await form.validateFields();
		let params:SyncRefundTradeRequest = {};
		const { source = {}, refundId, syncWay, tid, time } = form.getFieldsValue();
		const { plat, shopId, allShopList = [] } = source;
		let shopList: HighSyncShopItem[] = [];
		if (shopId === '') {
			shopList = allShopList;
		} else {
			shopList = allShopList?.filter(item => item.sellerId === shopId) || [];
		}
		params = {
			platform: plat,
			sellerId: shopId,
			refundId,
			syncWay,
			tid,
		};
		
		if (syncWay === SyncWay.按更新时间同步) {
			if (!time[0] || !time[1]) {
				message.error('请先选择要同步的时间段');
				return;
			}
			if (dayjs(time[1]).diff(dayjs(time[0]), 'second') > 30 * 24 * 3600) {
				message.error('时间间隔不能大于30天');
				return;
			}

			if (!shopList.filter(item => item.status === 1).length) {
				message.error('不存在能同步的有效店铺');
				return;
			}

			params.startTime = dayjs(time[0]).format('YYYY-MM-DD HH:mm:ss');
			params.endTime = dayjs(time[1]).format('YYYY-MM-DD HH:mm:ss');
			params.shopList = shopList.map(shop => ({
				sellerId: shop.sellerId,
				sellerNick: shop.sellerNick,
				platform: shop.platform,
			}));
			delete params.sellerId;
			delete params.platform;
		}
		
		console.log(params, 'params');
		onConfirmSync(params);
	};
	// 订单来源校验器
	const sourceValidator = (_, value) => {
		
		let errorMsg = '';
		if (!value) {
			errorMsg = "请选择平台和店铺";
		} else if (syncWay === SyncWay.按指定编码同步) {
			const { plat, shopId } = value;
			if (!plat) {
				errorMsg = "请选择平台";
			} else if (!shopId) {
				errorMsg = "请选择店铺";
			}
		}
		if (errorMsg) {
			return Promise.reject(
				new Error(errorMsg)
			); 
		}
		return Promise.resolve();
	};

	const onValuesChange = (changedValue) => {
		console.log(changedValue, 'onValuesChange');
		
		if ('source' in changedValue) {
			const platform = changedValue.source?.plat;
			setPlatform(platform);
			setHasInputTid(platform === PLAT_PDD || platform === PLAT_JD);
		}

		if ('syncWay' in changedValue) {
			setSyncWay(changedValue.syncWay);
		}
	};

	const SyncStatusModal = UseSyncStatusModal({
		onClose: () => {
			setSyncShopList([]);
			afterOK();
		},
		visible: syncShopList.length > 0,
		syncShopList,
		needSearchTrade: true,
	});

	// const platformText = (platform, field) => {
	// 	if (platform === 'c2m') {
	// 		if (field === 'label') {
	// 			return '订单编号';
	// 		} else if (field === 'name') {
	// 			return 'tid';
	// 		} else if (field === 'message') {
	// 			return '订单编号不能为空!';
	// 		}
	// 	} else if (platform === 'jd') {
	// 		if (field === 'label') {
	// 			return '取消单/服务单';
	// 		} else if (field === 'name') {
	// 			return 'refundId';
	// 		} else if (field === 'message') {
	// 			return '取消单/服务单不能为空!';
	// 		}
	// 	} 
	// 	if (field === 'label') {
	// 		return '退款/售后编码';
	// 	} else if (field === 'name') {
	// 		return 'refundId';
	// 	} else if (field === 'message') {
	// 		return '退款/售后编码不能为空!';
	// 	}
	// };
	
	return (
		<>
		
			<Modal
				centered
				width={ 600 }
				title="指定同步"
				visible={ visible }
				onCancel={ modalClose }
				okText="开始同步"
				okButtonProps={ {
					loading: syncLoading
				} }
				maskClosable={ false }
				onOk={ modalOk }
			>
				<div>
					<Form
						form={ form }
						onValuesChange={ onValuesChange }
					>
						<Row>						
							<Col span={ 24 }>
								<Form.Item
									label="订单来源"
									labelCol={ { span: 5 } }
									wrapperCol={ { span: 19 } }
									name="source"
									validateTrigger={ false }
									rules={ [{ required: true, validator: sourceValidator }] }
								>
									<ShopSingleSelect style={ { width: 174 } } isDisableExpire isDisableStop hidePlatforms={ [PLAT_KTT, PLAT_DW, PLAT_YZ] } />
								</Form.Item>
							</Col>
						</Row>
						<Row>
							<Col span={ 24 }>
								<Form.Item
									label="同步方式"
									labelCol={ { span: 5 } }
									name="syncWay"
									rules={ [{ required: true }] }
									className={ cs('r-ai-c') }
									initialValue={ syncWay }
								>
									<Radio.Group>
										<Radio value={ SyncWay.按指定编码同步 }>按指定退款/售后编码同步</Radio>
										<Radio value={ SyncWay.按更新时间同步 }>按更新时间进行同步</Radio>
									</Radio.Group>
								</Form.Item>
							</Col>

						</Row>
						{syncWay === SyncWay.按指定编码同步 ? (
							<Row>
								{hasInputTid && (
									<Col span={ 24 }>
										<Form.Item
											labelCol={ { span: 5 } }
											label="销售订单编码"
											name="tid"
											rules={ [{ required: true, message: '销售订单编码不能为空!' }] }
										>
											<Input />
										</Form.Item>
									</Col>
								)}
								<Col span={ 24 }>
									<Form.Item
										label={ platform == 'c2m' ? '订单编号' : platform == 'jd' ? "取消单/服务单" : "退款/售后编码" }
										labelCol={ { span: 5 } }
										name={ platform == 'c2m' ? "tid" : "refundId" }
										rules={ [{ required: true, message: platform == 'c2m' ? '订单编号不能为空!' : platform == 'jd' ? "取消单/服务单不能为空!" : '退款/售后编码不能为空!' }] }
									>
										<Input />
									</Form.Item>
								</Col>

								<Text>
									淘宝平台叫<Text type="danger">退款编码</Text>,
									拼多多平台叫<Text type="danger">售后编码</Text>,
									快手平台叫<Text type="danger">退款单号</Text>,
									抖音平台叫<Text type="danger">售后编号</Text>,
									1688平台叫<Text type="danger">退款编号</Text>
								</Text>

							</Row>
						) : (
							<Row>
								<Col span={ 24 }>
									<Form.Item
										label="选择日期"
										labelCol={ { span: 5 } }
										name="time"
										style={ { alignItems: 'center' } }
										rules={ [{ required: true, message: '日期不能为空!' }] }
									>
										<KdzsDateRangePicker1
											format="YYYY-MM-DD HH:mm:ss"
											hideQuickChooseType={ ['近一个月'] }
											disabledDateDays={ 30 }
										/>
									</Form.Item>
				
								</Col>
								<Col span={ 24 }>
									<Alert
										message="注意：开始时间和结束时间之间的范围在30天以内"
										type="warning"
									/>
								</Col>
							</Row>
						)}
					</Form>

				
				</div>
			</Modal>

			{syncShopList.length ? SyncStatusModal : ''}
		</>
	);
};

export default SyncModal;
