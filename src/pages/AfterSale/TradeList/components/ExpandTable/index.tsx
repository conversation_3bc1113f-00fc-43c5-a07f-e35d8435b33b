import React, { useState, useEffect } from 'react';
import { Table, Form, Checkbox, Row, Spin, Progress, Button, Typography, Select, Col, Radio, Input, Space, Tooltip } from 'antd';
import cs from 'classnames';
import { useRequest } from 'ahooks';
import { observer } from 'mobx-react';
import { SyncRefundTradeApi, CreateExchangeTradeApi } from '@/apis/aftersale/trade';
import { CreateExchangeTradeRequest, SelectRefundListWithPageResponse, SyncRefundTradeRequest } from '@/types/schemas/aftersale/trade';
import BaseTable, { BaseColumnsTypes } from '@/components/SearchTable/BaseTable';
import { afterSaleChannelText, afterSaleTypeText, AGREE_PLAT, checkRefundAction, exchangeStatusEnum, getPlatformTradeLink, getRefundManageStatus, RefundItemTypeEnum, refundStageText, RefundSystemTypeEnum } from '../../utils';
import WaresInfo from '@/components-biz/WaresInfo';
import ReturnItemModal from "../ReturnItemModal";
import Pointer from '@/utils/pointTrack/constants';
import userStore from '@/stores/user';
import { getVersionConstants } from '@/constants/versionUtils';
import { PLAT_FXG, PLAT_PDD, PLAT_TB } from '@/constants';
import { decryptFn } from '@/pages/Trade/components/ListItem/components/Decrypt';
import CreateExchangeTradeModal from '../CreateExchangeTradeModal';
import sendPoint from '@/utils/pointTrack/sendPoint';
import { REFUND_SYSTEM_STATUS } from '../../constants';

export type RefundTradeItemType = SelectRefundListWithPageResponse["data"]["list"][number];
interface Props {
	dataSource: RefundTradeItemType["refundItemRecordInfos"],
	afterOK?: () => void,
	warehouseStatus?:boolean
	trade: RefundTradeItemType;
}

const { Text } = Typography;

const ExpandTable: React.FC<Props> = (props) => {
	const { afterOK, dataSource = [], trade, warehouseStatus } = props;
	const { afterSaleType, reviewStatus, exceptionType, refundStatus, platform, orderStatus, goodsStatus, refundSystemStatus } = trade;
	const isFinished = trade.refundSystemStatus == REFUND_SYSTEM_STATUS.已完成;
	const { isShowZeroStockVersion } = userStore;
	const [creatingOrder, setCreatingOrder] = useState(false);
	// 弹窗展示
	const [modalVisible, setModalVisible] = useState(false);
	// 弹窗展示类型
	const [modalType, setModalType] = useState('');
	// 弹窗展示的dataSource
	const [modalDataSource, setModalDataSource] = useState([]);

	const tableClose = () => {
		onResetTable();
	};

	// 重置table
	const onResetTable = () => {
	};

	// 点击确认收货
	const onClickConfirm = () => {
		// 确认收货的时候要把换货商品过滤掉
		const validDataSource = dataSource.filter(item => ![RefundItemTypeEnum.换货].includes(item.refundItemType));
		setModalDataSource(validDataSource);
		setModalVisible(true);
		setModalType('confirm');
	};

	// 点击退货处理
	const onClickSalesReturn = () => {
		// 退货处理的时候要把换货商品过滤掉
		const validDataSource = dataSource.filter(item => ![RefundItemTypeEnum.换货].includes(item.refundItemType));
		setModalDataSource(validDataSource);
		setModalVisible(true);
		setModalType('manage');
	};
	// 解密订单收件人信息
	const decryptReceiverInfo = async() => {
		const { successObj } = await decryptFn({
			type: "",
			togetherId: trade.tid,
			platform: trade.platform,
			mallId: trade.sellerId,
			encryptObj: {
				name: trade.exchangeReceiverName,
				phone: trade.exchangeReceiverMobile,
				address: trade.exchangeReceiverAddress,
				buyerNick: trade.buyerNickOrigin || trade.buyerNick, // 售后暂时没有快团团
			},
			decryptArr: [{
				sellerId: trade.sellerId,
				platform: trade.platform,
				caid: trade.oaid,
				tid: trade.platform === PLAT_FXG ? trade.refundId : trade.tid, // 如果是抖店，传售后单号才能解密
				sceneCode: 100,
				encodeMobile: trade.exchangeReceiverMobile,
				encodeReceiverPhone: trade.exchangeReceiverMobile,
				encodeReceiverName: trade.exchangeReceiverName,
				encodeReceiverAddress: trade.exchangeReceiverAddress,
			}],
			sellerNick: trade.sellerNick,
			sellerId: trade.sellerId,
			pack: {
				source: trade.source,
				platform: trade.platform,
			},
		}).catch(() => setCreatingOrder(false));
		return successObj;
	};
	// 点击生成补发换货订单
	const createdOrder = async() => {
		sendPoint(Pointer.售后_生成换货补发手工单);
		setCreatingOrder(true);
	};


	// 操作按钮内容
	const getActions = (value, row, index) => {
		// 针对淘宝在途退款 仅退款 已发货 且未收到货的情况
		if (afterSaleType === afterSaleTypeText.仅退款 && platform === PLAT_TB && trade.refundItemType !== RefundItemTypeEnum.退货) {
			return index === 0 ? tbOnWayInterBreak() : null;
		}

		// 售后单处于未完成状态
		// if (refundStage !== refundStageText.未完成) {
		// 	return null;
		// }



		const createExchangeOrder = (
			<Button
				size="small"
				disabled={ trade.exchangeTid && trade.exchangeTradeStatus !== exchangeStatusEnum.已删除 }
				loading={ creatingOrder }
				className={ cs('r-btn-1890FF') }
				onClick={ createdOrder }
			>生成换货订单
			</Button>
		);

		const createReissue = (
			trade.platform == PLAT_PDD
				? (
					<Tooltip title="因平台限制，拼多多补寄售后单暂不支持自动上传快递单号；如【售后高级设置】中已开启发货后自动上传配置，拼多多补发订单发货后，请手动复制快递单号至店铺后台填写">
						<Button
							size="small"
							loading={ creatingOrder }
							disabled={ trade.exchangeTid && trade.exchangeTradeStatus !== exchangeStatusEnum.已删除 }
							className={ cs('r-btn-1890FF') }
							onClick={ createdOrder }
						>生成补发订单
						</Button>
					</Tooltip>
				)
				: (
					<Button
						size="small"
						loading={ creatingOrder }
						disabled={ trade.exchangeTid && trade.exchangeTradeStatus !== exchangeStatusEnum.已删除 }
						className={ cs('r-btn-1890FF') }
						onClick={ createdOrder }
					>生成补发订单
					</Button>
				)
		);


		// afterSaleType = 2 退货退款类型
		// reviewStatus 审核状态 true已审核 false未审核
		// exceptionType  异常类型,0无异常,1异常,2忽略异常 忽略异常  和【生成换货单】【生成补发订单】没关系

		if (!reviewStatus || exceptionType === 2) {
			return index === 0 && (
				<Space direction="vertical">
					{/* 生成补发订单 */}
					{checkRefundAction(trade).createReissueOrder && createReissue}
					{/* 生成换货订单 */}
					{checkRefundAction(trade).createExchangeOrder && createExchangeOrder}
				</Space>
			);
		}

		const confirmReturnItemBtn = (
			<Button
				className={ cs('r-btn-1890FF') }
				data-point={ Pointer['售后_售后订单_售后操作_售后操作_确认收货'] }
				size="small"
				onClick={ () => onClickConfirm() }
			>确认收货
			</Button>
		);
		const manageReturnItemBtn = (
			<Button
				className={ cs('r-btn-1890FF') }
				data-point={ Pointer['售后_售后订单_售后操作_售后操作_退货处理'] }
				size="small"
				onClick={ () => onClickSalesReturn() }
			>退货处理
			</Button>
		);


		let hasManageReturnItemBtn = false;

		// 已处理数量小于售后数量,并且实收数量大于0时，才展示退货处理按钮
		dataSource.forEach((item:any) => {
			const { hasRefundNum = 0, hasDisposeNum = 0 } = item;
			if (hasRefundNum > 0 && hasDisposeNum < hasRefundNum) {
				hasManageReturnItemBtn = true;
			}
		});
		const isHideOptBtn = row.refundItemType === RefundItemTypeEnum.退款 && afterSaleType === afterSaleTypeText.仅退款;
		// 确认收货的展示逻辑
		const showConfirmReceiveBtn = () => {
			const isReissue = trade.refundSystemType == RefundSystemTypeEnum.补发;
			return !isFinished && !hasAllReturnItem() && !isHideOptBtn && !isReissue;
		};
		return index === 0 && (
			<Space direction="vertical">
				{/* 确认收货 */}
				{showConfirmReceiveBtn() && confirmReturnItemBtn}
				{/* 生成补发订单 */}
				{checkRefundAction(trade).createReissueOrder && createReissue}
				{/* 生成换货订单 */}
				{checkRefundAction(trade).createExchangeOrder && createExchangeOrder}
				{/* 退货处理 */}
				{/* <span>{hasManageReturnItemBtn + ''+warehouseStatus + ''+ !isHideOptBtn + ''}</span> */}
				{(hasManageReturnItemBtn && warehouseStatus && !isHideOptBtn) ? manageReturnItemBtn : null}
				{/* {(hasManageReturnItemBtn) ? manageReturnItemBtn : null} */}
				{trade.exchangeInvoices?.length ? (
					<div className="r-c-error">({trade.exchangeInvoices[0]?.exchangeShipName}:{trade.exchangeInvoices[0]?.exchangeInvoiceNo})</div>
				) : ''}
			</Space>
		);
	};

	const tbOnWayInterBreak = () => {
		const interceptTradeBtn = (
			<Button
				className={ cs('r-btn-1890FF') }
				size="small"
				style={ { width: 110 } }
				onClick={ () => {
					window.open(getPlatformTradeLink(trade['platform'], trade).refund, '_blank');
				} }
			>拦截快递
			</Button>
		);
		const refundGoodsAndMoney = (
			<Button
				className={ cs('r-btn-1890FF') }
				size="small"
				style={ { width: 110 } }
				onClick={ () => {
					window.open(getPlatformTradeLink(trade['platform'], trade).refund, '_blank');
				} }
			>协商退货退款
			</Button>
		);

		const interceptBtns = (
			<Space direction="vertical">
				{((orderStatus === "WAIT_BUYER_CONFIRM_GOODS" || orderStatus === "SELLER_CONSIGNED_PART") && goodsStatus == 1 && !isFinished) ? interceptTradeBtn : null}
				{((orderStatus === "WAIT_BUYER_CONFIRM_GOODS" || orderStatus === "SELLER_CONSIGNED_PART") && goodsStatus == 1) ? refundGoodsAndMoney : null}
			</Space>
		);
		return interceptBtns;
	};

	// 判断是否全部收货完成
	const hasAllReturnItem = () => {
		let hasAllReturnItem = true;
		dataSource.forEach((item:any) => {
			const { hasRefundNum = 0, applyRefundNum = 0 } = item;
			// 货品类型是换货或者是补寄，不需要判断是否需要收货
			const isReturnOrReissue = [RefundItemTypeEnum.换货, RefundItemTypeEnum.补发].includes(item.refundItemType);
			if (!isReturnOrReissue && hasRefundNum < applyRefundNum && applyRefundNum > 0) {
				hasAllReturnItem = false;
			}
		});
		return hasAllReturnItem;
	};
	const columns = [
		{
			title: "工单类型",
			key: 'wordOrderType',
			className: cs('table-right-border'),
			render: (value, row, index) => {
				return index === 0 && (
					<div className={ cs('r-bold') }>
						{RefundSystemTypeEnum[trade.refundSystemType]}
					</div>
				);
			},
			onCell: (row:any, index:number) => {
				return {
					style: index === 0 ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '创建时间',
			key: 'createTime',
			dataIndex: 'createTime',
			render: (text, record, index) => {
				return index === 0 && (
					<div className={ cs('r-flex') }>
						<div className={ cs('r-fc-black-65', 'r-mr-32') }>{index + 1}</div>
						<div className={ cs('r-fc-black-65') }>{text}</div>
					</div>
				);
			},
			onCell: (row:any, index:number) => {
				return {
					style: index === 0 ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: getVersionConstants('商品'),
			key: 'refundItemRecordInfos',
			dataIndex: 'refundItemRecordInfos',
			className: cs('table-right-border'),
			width: 380,
			render: (text, record, index) => {
				return (
					<WaresInfo
						isCombination={ record['isCombination'] }
						key={ record['id'] }
						imgSize={ 48 }
						imgUrl={ record['picUrl'] }
						wareName={ record['itemAlias'] }
						skuName={ record['skuName'] }
						outerId={ record['outerId'] }
					/>
				);
			}
		},
		{
			title: `${getVersionConstants('商品')}类型`,
			key: 'refundItemType',
			className: cs('table-right-border'),
			render: (value, row, index) => {
				return (
					<div className={ cs('r-bold') }>
						{RefundItemTypeEnum[row.refundItemType]}{getVersionConstants('商品')}
					</div>
				);
			},
		},
		{
			title: '售后数量',
			key: 'applyRefundNum',
			dataIndex: 'applyRefundNum',
		},
		{
			title: '实收数量',
			key: 'hasRefundNum',
			dataIndex: 'hasRefundNum',
			render: (text:any, record) => {
				if ([afterSaleTypeText.仅退款, afterSaleTypeText.退货退款].includes(afterSaleType)) {
					return (text || 0);
				}
				if ([RefundItemTypeEnum.换货, RefundItemTypeEnum.补发].includes(record.refundItemType)) {
					return '-';
				}
				return text || 0;
			}
		},
		{
			title: '已处理数量',
			key: 'hasDisposeNum',
			dataIndex: 'hasDisposeNum',
			render: (text:any, record) => {
				if ([afterSaleTypeText.仅退款, afterSaleTypeText.退货退款].includes(afterSaleType)) {
					return (text || 0);
				}
				if ([RefundItemTypeEnum.退货].includes(record.refundItemType)) {
					return (text || 0);
				}
				return text;
			}
		},
		{
			title: '处理状态',
			key: 'manageStatus',
			dataIndex: 'manageStatus',
			className: cs('table-right-border'),
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-65') }>{getRefundManageStatus(record, trade)}</div>
				);
			}
		},
		{
			title: '操作',
			width: 180,
			key: 'action',
			align: "center" as const,
			dataIndex: 'action',
			// onCell: (record, index) => {
			// 	if (index === 0) {
			// 	  return { rowSpan: dataSource.length + 1 };
			// 	} else {
			// 		return { rowSpan: 0 };
			// 	}
			// },
			render: getActions,
			onCell: (row:any, index:number) => {
				return {
					style: index === 0 ? {} : { borderTop: 0 }
				};
			}
		}
	];
	return (
		<div className={ cs('r-pb-16', 'aftersale-expand-table') }>
			<Table
				rowKey="id"
				size="small"
				columns={ columns }
				dataSource={ dataSource }
				bordered
				style={ { position: 'sticky', zIndex: 21, left: '16px', right: '16px', width: `calc(100vw - 84px)` } }
				pagination={ false }
			/>

			<ReturnItemModal
				visible={ modalVisible }
				type={ modalType }
				dataSource={ modalDataSource }
				trade={ trade }
				afterOK={ afterOK }
				onCancel={ () => setModalVisible(false) }
			/>

			{creatingOrder && (
				<CreateExchangeTradeModal
					refundTradeItem={ trade }
					onCancel={ () => setCreatingOrder(false) }
					afterOK={ afterOK }
				/>
			)}
		</div>
	);
};

export default observer(ExpandTable);
