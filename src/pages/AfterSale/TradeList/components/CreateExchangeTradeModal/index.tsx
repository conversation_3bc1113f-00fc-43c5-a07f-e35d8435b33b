import React, { useState, useEffect } from 'react';
import { Modal, Form, Row, Button, Input, Col, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import cs from 'classnames';
import { ColumnsType } from 'antd/lib/table';
import { observer } from 'mobx-react';
import { useRequest } from 'ahooks';
import WaresInfo from '@/components-biz/WaresInfo';
import { RefundItemTypeEnum, RefundSystemTypeEnum } from '../../utils';
import s from "../../index.module.scss";
import Table from "@/components/Table";
import userStore from '@/stores/user';
import { getVersionConstants } from '@/constants/versionUtils';
import { RefundTradeItemType } from '../ExpandTable';
import Icon from '@/components/Icon';
import { DecryptFiledEnum, decryptFn } from '@/pages/Trade/components/ListItem/components/Decrypt';
import { PLAT_FXG, PLAT_TB, PLAT_TM, PLAT_XHS } from '@/constants';
import AddrSelect from '@/components-biz/AddrSelect';
import { getTradePlatformLabel } from '@/pages/Trade/utils';
import { CreateExchangeTradeApi, FindSellerRefundForEditApi } from '@/apis/aftersale/trade';
import { CreateExchangeTradeRequest } from '@/types/schemas/aftersale/trade';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';


interface ICreateExchangeTradeModalProps {
	dataSource?:any,
	onCancel?: () => void,
	afterOK?: () => void,
    refundTradeItem?: RefundTradeItemType,
}

const CreateExchangeTradeModal: React.FC<ICreateExchangeTradeModalProps> = (props) => {
	const { onCancel, afterOK, dataSource, refundTradeItem = {} } = props;
	const [form] = useForm();

	// 当前售后单详细信息
	const [trade, setTrade] = useState<RefundTradeItemType>({});
	const [tradeTypeText, setTradeTypeText] = useState('换货');
	const [hideReceiverInfo, setHideReceiverInfo] = useState(true);
	const { runAsync, loading } = useRequest(CreateExchangeTradeApi, {
		manual: true,
	});

	// 获取商品详情
	const { runAsync: queryRefundDetail } = useRequest(FindSellerRefundForEditApi, {
		manual: true,
	});

	useEffect(() => {
		if (refundTradeItem?.['id']) {
			queryRefundDetail({ id: +refundTradeItem['id'] }).then(res => {
				setTrade({
					...res,
				});
				setTradeTypeText(RefundSystemTypeEnum[refundTradeItem.refundSystemType] || '换货');
				if (res.exchangeReceiverNameMask) {
					setHideReceiverInfo(false);
					form.setFieldsValue({
						...refundTradeItem,
						buyerNick: res.exchangeReceiverNameMask,
						receiverMobile: res.exchangeReceiverMobileMask,
						receiverAddress: res.exchangeReceiverAddressMask,
						receiverDistrict: res.exchangeReceiverTown,
						receiverCity: res.exchangeReceiverCity,
						receiverState: res.exchangeReceiverProvince,
					});
				}
			});
		}
	}, [refundTradeItem?.id]);

	const getHeaderCell = () => {
		return { style: { background: 'rgba(0,0,0,0.08)' } };
	};

	const getColumns = (tableType:string) => {
		let columns = [];
		if (tableType === 'platform') {
			columns = [
				{
					title: '序号',
					width: 65,
					key: 'index',
					align: 'center',
					onHeaderCell: getHeaderCell,
					dataIndex: 'index',
					render: (text, record, index) => {
						return index + 1;
					},
				},
				{
					title: '商品信息',
					key: 'itemInfos',
					width: 350,
					onHeaderCell: getHeaderCell,
					dataIndex: 'itemInfos',
					render: (text, record:any, index) => {
						return (
							<WaresInfo
								isCombination={ record.isCombination }
								outerId={ record.outerId }
								imgSize={ 48 }
								imgUrl={ record['picUrl'] }
								wareName={ record['itemAlias'] }
								skuName={ record['skuName'] }
							/>
						);
					}
				},
				{
					title: '商家编码',
					width: 200,
					onHeaderCell: getHeaderCell,
					dataIndex: 'outerSkuId',
				},
				{
					title: '货品类型',
					width: 200,
					onHeaderCell: getHeaderCell,
					key: 'refundItemType',
					dataIndex: 'refundItemType',
					render: (text, record, index) => {
						return RefundItemTypeEnum[text];
					}
				},
				{
					title: '售后单申请数量',
					key: 'applyRefundNum',
					onHeaderCell: getHeaderCell,
					dataIndex: 'applyRefundNum',
				}
			];
		} else if (tableType === 'item') {
			columns = [
				{
					title: '序号',
					width: 65,
					key: 'index',
					align: 'center',
					onHeaderCell: getHeaderCell,
					dataIndex: 'index',
					render: (text, record, index) => {
						return index + 1;
					},
				},
				{
					title: '货品信息',
					key: 'itemInfos',
					width: 350,
					onHeaderCell: getHeaderCell,
					dataIndex: 'itemInfos',
					render: (text, record:any, index) => {
						return (
							<WaresInfo
								isCombination={ record.isCombination }
								outerId={ record.outerId }
								imgSize={ 48 }
								imgUrl={ record['picUrl'] }
								wareName={ record['itemAlias'] }
								skuName={ record['skuName'] }
							/>
						);
					}
				},
				{
					title: '货品规格编码',
					width: 200,
					onHeaderCell: getHeaderCell,
					dataIndex: 'outerSkuId',
				},
				{
					title: '货品类型',
					width: 200,
					onHeaderCell: getHeaderCell,
					key: 'refundItemType',
					dataIndex: 'refundItemType',
					render: (text, record, index) => {
						return RefundItemTypeEnum[text];
					}
				},
				{
					title: '操作数量',
					key: 'applyRefundNum',
					onHeaderCell: getHeaderCell,
					dataIndex: 'applyRefundNum',
				}
			];
		}

		return columns;
	};


	// 商品信息表格
	const renderInfosTable = (type:string = 'platform') => {
		let dataSource = trade['refundPlatformItemRecordInfos'] || [];
		if (refundTradeItem.refundSystemType === RefundSystemTypeEnum['换货']) {
			dataSource = dataSource.filter(i => i.refundItemType == RefundItemTypeEnum['换货']);
		} else if (refundTradeItem.refundSystemType === RefundSystemTypeEnum['补发']) {
			dataSource = dataSource.filter(i => i.refundItemType == RefundItemTypeEnum['补发']);
		}
		if (type === 'item') {
			// 本地货品
			dataSource = trade['refundItemRecordInfos'] || [];
			if (refundTradeItem.refundSystemType === RefundSystemTypeEnum['换货']) {
				dataSource = dataSource.filter(i => i.refundItemType == RefundItemTypeEnum['换货']);
			} else if (refundTradeItem.refundSystemType === RefundSystemTypeEnum['补发']) {
				dataSource = dataSource.filter(i => i.refundItemType == RefundItemTypeEnum['补发']);
			}
		}
		return (
			<Table
				className="c-aftersale-table"
				size="small"
				bordered
				rowKey="sysSkuId"
				scroll={ { y: 200 } }
				columns={ getColumns(type) }
				pagination={ false }
				dataSource={ dataSource }
				locale={ {
					emptyText: "暂无数据"
				} }
			/>
		);
	};

	// 解密订单收件人信息
	const decryptReceiverInfo = async(type: DecryptFiledEnum[number]) => {
		sendPoint(Pointer.售后_生成手工单弹窗_解密);
		// console.log('%c [ trade ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', trade);
		let togetherId = trade.tid;
		if (trade.platform === PLAT_TB) {
			togetherId = trade.refundId;
			type = '';
		}
		const { successObj } = await decryptFn({
			type,
			togetherId,
			platform: trade.platform,
			mallId: trade.sellerId,
			encryptObj: {
				name: trade.exchangeReceiverName,
				phone: trade.exchangeReceiverMobile,
				address: trade.exchangeReceiverAddress,
				buyerNick: trade.buyerNickOrigin || trade.buyerNick, // 售后暂时没有快团团
			},
			decryptArr: [{
				sellerId: trade.sellerId,
				platform: trade.platform,
				caid: trade.oaid,
				tid: trade.platform === PLAT_FXG ? trade.refundId : trade.ptTid, // 如果是抖店，传售后单号才能解密
				ptTid: trade.platform === PLAT_FXG ? trade.refundId : trade.ptTid,
				sceneCode: 100,
				encodeMobile: trade.exchangeReceiverMobile,
				encodeReceiverPhone: trade.exchangeReceiverMobile,
				encodeReceiverName: trade.exchangeReceiverName,
				encodeReceiverAddress: trade.exchangeReceiverAddress,
			}],
			sellerNick: trade.sellerNick,
			sellerId: trade.sellerId,
			pack: {
				source: trade.source,
				platform: trade.platform,
				tid: trade.tid
			},
			decryptLocation: 'createExchangeTradeModal', // 添加一个传参标识来源
		}).catch(() => {});
		if ([PLAT_FXG].includes(trade.platform)) {
			let res = successObj[togetherId] || {};
			let addrKey = `${DecryptFiledEnum['地址']}IsDecrypt`;
			let mobileKey = `${DecryptFiledEnum['手机号']}IsDecrypt`;
			let nameKey = `${DecryptFiledEnum['收件人']}IsDecrypt`;
			res[addrKey] = res[addrKey] || trade[addrKey];
			res[mobileKey] = res[mobileKey] || trade[mobileKey];
			res[nameKey] = res[nameKey] || trade[nameKey];
			res[nameKey] && form.setFieldsValue({ buyerNick: res[nameKey] });
			res[mobileKey] && form.setFieldsValue({ receiverMobile: res[mobileKey] });
			res[addrKey] && form.setFieldsValue({ receiverAddress: res[addrKey] });
			let isDecrypted = !!(res[addrKey] && res[mobileKey] && res[nameKey]);
			setTrade(trade => ({
				...trade,
				...res,
				decryptObj: {
					...res,
				},
				isDecrypted: !!(res[addrKey] && res[mobileKey] && res[nameKey])
			}));

			if (isDecrypted) {
				setTimeout(() => {
					res[addrKey] && form.setFieldsValue({ receiverAddress: res[addrKey] });
				});
			}
		} else {
			let res = successObj[togetherId] || {};
			if (!res.receiverName && !res.receiverPhone && !res.receiverAddress) {
				message.error("解密失败");
				return;
			}
			form.setFieldsValue({ buyerNick: res.receiverName });
			form.setFieldsValue({ receiverMobile: res.receiverPhone });
			form.setFieldsValue({ receiverAddress: res.receiverAddress });
			setTrade(trade => ({
				...trade,
				...res,
				decryptObj: {
					...res,
				},
				isDecrypted: true,
			}));

			setTimeout(() => {
				form.setFieldsValue({ receiverAddress: res.receiverAddress });
			});
			console.log({
				...trade,
				...res,
				isDecrypted: true,
			});
		}
	};

	const renderDecryptIcon = (type: DecryptFiledEnum[number] = DecryptFiledEnum['全部']) => {
		return (
			<span className="r-pointer" onClick={ () => { decryptReceiverInfo(type); } }><Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5 } } /></span>
		);
	};

	const handleAddressValues = (add) => {
		setTrade(trade => ({
			...trade,
			receiverState: add?.provinceName || '',
			receiverCity: add?.cityName || '',
			receiverDistrict: add?.countyName || '',
		}));
	};

	const isDecrypt = (type: DecryptFiledEnum[number] = DecryptFiledEnum['全部']) => {
		if (![PLAT_FXG, PLAT_TB, PLAT_TM, PLAT_XHS].includes(trade.platform)) {
			return trade.isDecrypted;
		} else if (type) {
			return trade[`${type}IsDecrypt`];
		} else {
			return trade.isDecrypted;
		}
	};

	const onFinish = async(e) => {
		sendPoint(Pointer.售后_生成手工单弹窗_保存);
		const res = form.getFieldsValue();
		let isUpdate = false;
		let params: CreateExchangeTradeRequest = {
			refundInfoId: +trade.id,
			isUpdate,
		};
		if (trade.isDecrypted) {
			if (![PLAT_FXG, PLAT_TB, PLAT_TM, PLAT_XHS].includes(trade.platform)) {
				const { receiverAddress, receiverName, receiverPhone } = trade.decryptObj;
				if (receiverName == res.buyerNick
					&& receiverPhone == res.receiverMobile
					&& refundTradeItem.receiverState == res.receiverAddrInfo?.provinceName
					&& refundTradeItem.receiverCity == res.receiverAddrInfo?.cityName
					&& refundTradeItem.receiverDistrict == res.receiverAddrInfo?.countyName
					&& receiverAddress == res.receiverAddress) {
					isUpdate = false;
				} else {
					isUpdate = true;
					params = {
						refundInfoId: +trade.id,
						isUpdate,
						exchangeReceiverName: res.buyerNick,
						exchangeReceiverMobile: res.receiverMobile,
						exchangeReceiverProvince: res.receiverAddrInfo?.provinceName,
						exchangeReceiverCity: res.receiverAddrInfo?.cityName,
						exchangeReceiverTown: res.receiverAddrInfo?.countyName,
						exchangeReceiverAddress: res.receiverAddress,
					};
				}
			} else {
				let addrKey = `${DecryptFiledEnum['地址']}IsDecrypt`;
				let mobileKey = `${DecryptFiledEnum['手机号']}IsDecrypt`;
				let nameKey = `${DecryptFiledEnum['收件人']}IsDecrypt`;
				if (trade.decryptObj[nameKey] == res.buyerNick
					&& trade.decryptObj[mobileKey] == res.receiverMobile
					&& refundTradeItem.receiverState == res.receiverAddrInfo?.provinceName
					&& refundTradeItem.receiverCity == res.receiverAddrInfo?.cityName
					&& refundTradeItem.receiverDistrict == res.receiverAddrInfo?.countyName
					&& trade.decryptObj[addrKey] == res.receiverAddress) {
					isUpdate = false;
				} else {
					isUpdate = true;
					params = {
						refundInfoId: +trade.id,
						isUpdate,
						exchangeReceiverName: res.buyerNick,
						exchangeReceiverMobile: res.receiverMobile,
						exchangeReceiverProvince: res.receiverAddrInfo?.provinceName,
						exchangeReceiverCity: res.receiverAddrInfo?.cityName,
						exchangeReceiverTown: res.receiverAddrInfo?.countyName,
						exchangeReceiverAddress: res.receiverAddress,
					};
				}
			}
		}

		await runAsync(params);
		onCancel?.();
		afterOK?.();
	};

	return (
		<Modal
			centered
			width={ 1200 }
			title={ `生成${tradeTypeText}订单` }
			getContainer={ document.body }
			visible
			onCancel={ onCancel }
			destroyOnClose
			maskClosable={ false }
			footer={ [
				<Button key="back" onClick={ onCancel }>取消</Button>,
				<Button key="ok" loading={ loading } onClick={ onFinish } type="primary" >生成{tradeTypeText}订单</Button>
			] }
		>
			<div className={ cs(s.after_sale_modal) }>
				{/* 顶部编辑表格 */}
				{!hideReceiverInfo ? (
					<Form
						form={ form }
						preserve={ false }
						// initialValues={ getInitialFormValues() }
					>
						<div className="r-mb-12">
							店铺：{getTradePlatformLabel(trade.platform)}{trade.sellerNick}
						</div>
						<Row>
							<Col>
								<Form.Item
									label="收件人"
									name="buyerNick"
								>
									<Input
										disabled={ !trade.isDecrypted }
										style={ { width: 343 } }
										addonAfter={ isDecrypt(DecryptFiledEnum['收件人']) ? '' : renderDecryptIcon(DecryptFiledEnum['收件人']) }
									/>
								</Form.Item>
							</Col>
							<Col className="r-ml-12">
								<Form.Item
									label="联系方式"
									name="receiverMobile"
								>
									<Input
										disabled={ !trade.isDecrypted }
										style={ { width: 352 } }
										addonAfter={ isDecrypt(DecryptFiledEnum['手机号']) ? '' : renderDecryptIcon(DecryptFiledEnum['手机号']) }
									/>
								</Form.Item>
							</Col>
						</Row>

						{trade.isDecrypted ? (
							<Row>
								<Form.Item
									label={ `${tradeTypeText}地址` }
									name="receiverAddrInfo"
								>
									<AddrSelect
										province={ trade.receiverState }
										city={ trade.receiverCity }
										country={ trade.receiverDistrict }
										formItem
										triggerChange
										onChange={ handleAddressValues }
									/>
								</Form.Item>
								<Form.Item name="receiverAddress">
									<Input
										type="text"
										style={ { width: 280, marginLeft: 12 } }
									/>
								</Form.Item>
							</Row>
						) : (
							<Row>
								<Form.Item
									label={ `${tradeTypeText}地址` }
									name="receiverState"
								>
									<Input
										key="state"
										type="text"
										disabled
										className="r-mr-8"
										style={ { width: 160 } }
									/>
								</Form.Item>
								<Form.Item name="receiverCity">
									<Input
										type="text"
										disabled
										className="r-mr-8"
										style={ { width: 160 } }
									/>
								</Form.Item>
								<Form.Item name="receiverDistrict">
									<Input
										type="text"
										disabled
										className="r-mr-8"
										style={ { width: 160 } }
									/>
								</Form.Item>
								<Form.Item name="receiverAddress">
									<Input
										type="text"
										disabled
										className="r-mr-8"
										style={ { width: 260 } }
										addonAfter={ isDecrypt(DecryptFiledEnum['地址']) ? '' : renderDecryptIcon(DecryptFiledEnum['地址']) }
									/>
								</Form.Item>
							</Row>
						)}
					</Form>
				) : ''}

				{/* 商品 */}
				<div className={ cs(s.trade_title, s.trade_table_title) }><div className={ cs() } >{tradeTypeText}商品</div></div>
				{renderInfosTable()}

				{/* 货品 */}
				<div className={ cs(s.trade_title, s.trade_table_title, 'r-mt-8') }><div className={ cs() } >{tradeTypeText}货品</div></div>
				{renderInfosTable('item')}
			</div>

		</Modal>
	);
};

export default observer(CreateExchangeTradeModal);
