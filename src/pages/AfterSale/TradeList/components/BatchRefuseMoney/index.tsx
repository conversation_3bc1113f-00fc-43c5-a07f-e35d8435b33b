import React, { useEffect, useState, useMemo } from 'react';
import { Button, Form, Input, Modal, Progress, Select, Table, Upload, Image, Tooltip } from 'antd';
import { ExclamationCircleFilled, PlusCircleOutlined, FormOutlined } from '@ant-design/icons';
import { useForm } from 'antd/es/form/Form';
import cs from 'classnames';
import { GetRefuseReasonListApi, RefuseRefundFeeApi, RefuseRefundGoodsApi, RefuseRefundSupplyApi, BatchRefuseRefundApi } from '@/apis/aftersale/trade';
import events from "@/utils/events";
import { afterSaleChannelText } from '../../utils';
import { BatchAgreeRefundFeeResponse } from '@/types/schemas/aftersale/trade';
import message from '@/components/message';
import { afterSaleOperateType, BtnEnum, operateTypeToBtnEnum } from '../../constants';
import { PLAT_MAP, PLAT_JD, PLAT_TB, PLAT_ALI, PLAT_C2M, PLAT_HAND, PLAT_OTHER, PLAT_PDD, PLAT_SPH, PLAT_FXG, PLAT_KS, PLAT_TM, isViteDevMode } from '@/constants';
import { isSourceHand } from '@/components-biz/ShopListSelect/shopListUtils';
import { copyToPaste } from '@/utils';
import useGetState from '@/utils/hooks/useGetState';
import { getToken } from '@/utils/token';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import { getPlatformTradeLink } from '@/pages/AfterSale/TradeList/utils';
import BatchEditRefuseMessageModal from './components/BatchEditRefuseMessageModal';
import BatchEditProofModal from './components/BatchEditProofModal';
import s from './index.module.scss';

const modalMap = {
	[afterSaleOperateType.拒绝仅退款]: '拒绝仅退款',
	[afterSaleOperateType.拒绝退货]: '拒绝退货',
	[afterSaleOperateType.拒绝退货退款]: '拒绝退货退款',
};

type IBatchAgreeRefundFeeProps = BatchAgreeRefundFeeResponse['data'];

interface IInitPropsProps {
	isDistributor?: any,
	operateType?: any,
	afterOk?: () => void,
	list?: any[],
	singleRefuse?: (item: any, operateType: any) => void,
}

interface IProps {
    isDistributor?: any,
	onShowEditModal?: (id: string) => void,
}

let originList = [];

// 批量拒绝退款\退货\退货退款 批量拒绝
const BatchRefuseMoney = (props: IProps) => {
	const { isDistributor, onShowEditModal } = props;
	const [form] = useForm();
	const [batchRefuseForm] = useForm(); // 批量拒绝表单
	const [initProps, setInitProps, initPropsRef] = useGetState<IInitPropsProps>({});
	const [curType, setCurType, curTypeRef] = useGetState('');
	const [loading, setLoading] = useState(false);
	const [resultVisible, setResultVisible] = useState(false);
	const [refundResult, setRefundResult] = useState<IBatchAgreeRefundFeeProps>({});
	const [unsupportedTypesVisible, setUnsupportedTypesVisible] = useState(false);
	const [filteredNormalList, setFilteredNormalList] = useState([]);
	const [progressVisible, setProgressVisible] = useState(false); // 进度弹框
	const [progressInfo, setProgressInfo] = useState({ // 进度信息
		current: 0,
		total: 0,
		currentBatch: 0,
		totalBatch: 0,
		successCount: 0,
		failCount: 0,
		processingText: '正在处理...'
	});

	// 批量拒绝相关状态
	const [batchRefuseVisible, setBatchRefuseVisible] = useState(false); // 批量拒绝弹框
	const [batchRefuseData, setBatchRefuseData] = useState([]); // 批量拒绝数据
	// 移除全局的拒绝理由列表状态
	// const [refuseReasonList, setRefuseReasonList] = useState<GetRefuseReasonListResponse["data"]>([]); // 拒绝理由列表
	const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]); // 选中的行keys
	const [selectedRows, setSelectedRows] = useState<any[]>([]); // 选中的行数据
	const [validationErrors, setValidationErrors] = useState<{[key: string]: {[field: string]: string}}>({}); // 校验错误状态

	const [batchEditModalInfo, setBatchEditModalInfo] = useState<any>({ visible: false, type: "" }); // 批量编辑弹框状态

	// 显示不支持类型的弹窗
	const showUnsupportedTypesModal = (normalList) => {
		setFilteredNormalList(normalList);
		setUnsupportedTypesVisible(true);
	};

	/**
	 * 校验是否包含不支持的售后单类型
	 * 
	 * 检查的不支持类型包括：
	 * 
	 * 基础不支持类型（所有操作都不支持）：
	 * - 1688平台 (PLAT_ALI)
	 * - 拼多多平台 (PLAT_PDD)
	 * - 淘工厂平台 (PLAT_C2M)
	 * - 手工售后单平台 (PLAT_HAND)
	 * - 其他平台 (PLAT_OTHER)
	 * - 手工录入的售后单 (isSourceHand)
	 * - 无主售后单 (isNoTradeMess)
	 * - 手工录入渠道 (afterSaleChannelText.手工录入)
	 * - 订单号搜索渠道 (afterSaleChannelText.订单号搜索)
	 * - 未审核通过的售后单 (exceptionType !== 1 && !reviewStatus) 
	 * - 异常处理的售后单 (exceptionType === 1)
	 * - 京喜订单 (serviceTagList包含'jingxi')
	 * 
	 * 根据操作类型的额外限制：
	 * - 拒绝仅退款：额外不支持京东平台 (PLAT_JD)
	 * - 拒绝退货/拒绝退货退款：支持京东平台，但不支持京喜类型的订单
	 * 
	 * @param originList 原始售后单列表
	 * @param curType 当前操作类型
	 * @returns 如果包含不支持的类型返回true，否则返回false
	 */
	const checkUnsupportedTypes = (originList, curType) => {
		// 根据操作类型定义过滤条件
		const normalList = originList.filter(i => {
			// 基础过滤条件：所有操作类型都不支持的
			const baseFilter = i.platform !== PLAT_ALI 
				&& i.platform !== PLAT_PDD 
				&& i.platform !== PLAT_C2M 
				&& i.platform !== PLAT_HAND 
				&& !i.isNoTradeMess
				&& i.platform !== PLAT_OTHER
				&& !isSourceHand(i)
				&& i.afterSaleChannel !== afterSaleChannelText.手工录入
				&& i.afterSaleChannel !== afterSaleChannelText.订单号搜索
				&& !(i['exceptionType'] !== 1 && !i['reviewStatus'])
				&& i.exceptionType !== 1
				&& !i?.serviceTagList?.includes('jingxi');

			// 根据操作类型添加额外过滤条件
			if (curType === afterSaleOperateType.拒绝仅退款) {
				// 拒绝仅退款：京东订单不支持
				return baseFilter && i.platform !== PLAT_JD;
			} else {
				// 拒绝退货、拒绝退货退款：支持京东但京喜类型不支持（京喜已在基础过滤中排除）
				return baseFilter;
			}
		});

		// 如果有异常数据，显示弹窗
		if (normalList.length < originList.length) {
			showUnsupportedTypesModal(normalList);
			return true;
		}
		return false;
	};

	// 获取拒绝理由列表 - 修改为针对特定售后单
	const getRefuseReasonList = async(id, index) => {
		try {
			// 设置加载状态
			const newData = [...batchRefuseData];
			newData[index].refuseReasonLoading = true;
			setBatchRefuseData(newData);
			
			const res = await GetRefuseReasonListApi({ id });
			
			// 更新特定售后单的拒绝理由列表
			const updatedData = [...batchRefuseData];
			updatedData[index].refuseReasonList = res;
			updatedData[index].refuseReasonLoading = false;
			setBatchRefuseData(updatedData);
		} catch (error) {
			console.error('获取拒绝理由列表失败:', error);
			// 设置加载失败状态
			const newData = [...batchRefuseData];
			newData[index].refuseReasonLoading = false;
			setBatchRefuseData(newData);
		}
	};

	// 处理过滤异常数据并继续操作
	const handleFilterAndContinue = (normalList) => {
		if (normalList.length === 0) {
			message.error('请选择售后单后操作');
			return;
		}
		
		// 更新originList为过滤后的正常数据
		originList = normalList;
		
		// 继续执行对应的操作
		// 通过校验，如勾选数据＜2条，则直接执行单笔操作
		if (normalList.length == 1) {
			initPropsRef()?.singleRefuse?.(normalList[0], curTypeRef());
			return;
		}

		// 通过校验，如勾选数据＞2条，则执行批量操作
		showBatchRefuseModal(normalList);
	};

	// 显示批量拒绝弹框
	const showBatchRefuseModal = async(list) => {
		// 初始化批量拒绝数据，为每个售后单创建表单项
		const batchData = list.map((item, index) => ({
			...item,
			index: index + 1,
			refuseReasonId: undefined,
			refuseMessage: undefined,
			proofFileList: [],
			imgLoading: false,
			refuseReasonList: [], // 每个售后单单独维护拒绝理由列表
			refuseReasonLoading: false, // 拒绝理由加载状态
		}));
		
		setBatchRefuseData(batchData);
		setBatchRefuseVisible(true);

		// 批量设置拒绝理由
	};

	// 参考RefuseRefundModal的shouldShowProofUpload逻辑
	const shouldShowProofUpload = (platform) => {
		return [PLAT_FXG, PLAT_KS, PLAT_TB, PLAT_TM].includes(platform);
	};

	// 参考RefuseRefundModal的getMaxCount逻辑
	const getMaxCount = (platform) => {
		if ([PLAT_FXG, PLAT_KS].includes(platform)) {
			return 5; // 抖店、快手最多5张
		}
		if ([PLAT_TB, PLAT_TM].includes(platform)) {
			return 1; // 淘宝/天猫仅上传1张
		}
		return 1; // 默认1张
	};

	const EditIcon = ({ type }) => (
		<FormOutlined
			onClick={ () => {
				if (selectedRows.length === 0) {
					return message.warn('请先勾选需要操作的数据', 2);
				}
				setBatchEditModalInfo({ visible: true, type });
			} }
			className="r-c-blue r-pointer r-ml-5"
			style={ { fontSize: '14px' } }
		/>
	);

	// 批量拒绝弹框的表格列定义
	const batchRefuseColumns = [
		{
			title: '序号',
			dataIndex: 'index',
			width: 40,
			align: 'center' as const,
		},
		{
			title: '店铺',
			dataIndex: 'sellerNick',
			width: 150,
			render: (text, record) => (
				<div className={ cs('r-flex', 'r-ai-c') }>
					<PlatformIcon platform={ record['platform'] } />
					<span>{record['sellerNick']}</span>
				</div>
			),
		},
		{
			title: '售后单号',
			dataIndex: 'id',
			width: 150,
			render: (text, record) => (
				<div style={ { color: '#1890ff', fontSize: '12px' } }>
					{/* <a
						className={ cs('r-fc-1890FF') }
						href={ getPlatformTradeLink(record['platform'], record).refund }
						target="_blank"
						rel="noreferrer"
					>{record['refundId']}
					</a> */}
					<span 
						className={ cs('r-fc-1890FF', 'r-pointer') }
						onClick={ () => onShowEditModal(record.id) }
					>
						{record['refundId']}
					</span>

				</div>
			),
		},
		{
			title: (
				<div className={ cs('r-flex', 'r-ai-c') }>
					<div style={ { color: '#f5222d' } }>*</div>
					<div>拒绝理由</div>
				</div>
			),
			dataIndex: 'refuseReasonId',
			width: 150,
			render: (_, record, index) => {
				// 视频号小店也要显示拒绝理由选择
				// if (record.platform === PLAT_SPH) {
				// 	return null;
				// }
				
				const hasError = validationErrors[record.id]?.refuseReasonId;
				
				return (
					<Select
						key={ record.id }
						placeholder="请选择"
						style={ { width: '100%' } } 
						className={ hasError ? 'select-error' : '' }
						value={ record.refuseReasonId }
						loading={ record.refuseReasonLoading }
						onFocus={ () => {
							// 如果还没有获取过拒绝理由列表，则获取
							if (!record.refuseReasonList || record.refuseReasonList.length === 0) {
								getRefuseReasonList(record.id, index);
							}
						} }
						onChange={ (value) => {
							const newData = [...batchRefuseData];
							newData[index].refuseReasonId = value;
							setBatchRefuseData(newData);
							// 清除该字段的校验错误
							clearValidationError(record.id, 'refuseReasonId');
						} }
						getPopupContainer={ triggerNode => triggerNode.parentElement }
						placement="bottomLeft"
						listHeight={ 160 }
						dropdownMatchSelectWidth={ false }
					>
						{(record.refuseReasonList || []).map(item => (
							<Select.Option key={ item.reasonId } value={ item.reasonId }>
								{item.reasonText}
							</Select.Option>
						))}
					</Select>
				);
			},
		},
		{
			title: (
				<div className={ cs('r-flex', 'r-ai-c') }>
					<div style={ { color: '#f5222d' } }>*</div>
					<div>拒绝说明</div>
					<EditIcon type="refuseMessage" />
				</div>
			),
			dataIndex: 'refuseMessage',
			width: 150,
			render: (_, record, index) => {
				const hasError = validationErrors[record.id]?.refuseMessage;
				
				return (
					<Input
						placeholder="请输入拒绝说明"
						style={ hasError ? { borderColor: '#ff4d4f' } : {} }
						value={ record.refuseMessage }
						onChange={ (e) => {
							const newData = [...batchRefuseData];
							newData[index].refuseMessage = e.target.value;
							setBatchRefuseData(newData);
							// 清除该字段的校验错误
							clearValidationError(record.id, 'refuseMessage');
						} }
					/>
				);
			},
		},
		{
			title: (
				<div className={ cs('r-flex', 'r-ai-c') }>
					<Tooltip title="仅支持JPG、JPEG、PNG格式，单张大小不超过5M；抖店、快手可上传5张，淘宝/天猫仅可上传1张；其余平台暂不支持上传拒绝凭证">
						<div>拒绝凭证</div>
					</Tooltip>
					<EditIcon type="proofFileList" />
				</div>
			),
			dataIndex: 'proofFileList',
			width: 180,
			render: (_, record, index) => {
				// 参考RefuseRefundModal：只有特定平台才显示上传
				if (!shouldShowProofUpload(record.platform)) {
					return null;
				}

				const maxCount = getMaxCount(record.platform);
				const hasError = validationErrors[record.id]?.proofFileList;
				
				// 获取已上传成功的图片列表，用于预览
				const successImages = (record.proofFileList || [])
					.filter(file => file.status === 'done' && file.url);
				
				// 完全参考RefuseRefundModal的uploadProps配置
				const uploadProps = {
					action: location.origin + (isViteDevMode() ? '/api' : '') + '/index/common/uploadFile',
					withCredentials: true,
					maxCount,
					multiple: maxCount > 1,
					listType: "picture-card" as const,
					accept: ".jpg,.jpeg,.png",
					headers: {
						'qnquerystring': getToken() || '',
					},
					data: {
						uploadScene: "UPLOAD_REFUSE_PROOF_PIC",
						module: "AFTER_SALE",
					},
					beforeUpload: (file) => {
						const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
						if (!isJpgOrPng) {
							message.error('文件格式只支持JPG、JPEG、PNG!');
							return Upload.LIST_IGNORE;
						}
						// 检查文件大小
						const isLt5M = file.size / 1024 / 1024 < 5;
						if (!isLt5M) {
							message.error('文件大小超过 5MB!');
							return Upload.LIST_IGNORE;
						}
						return isJpgOrPng && isLt5M;
					},
					onChange: (info) => {
						console.log('%c [ info ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', info);
						const { fileList: newFileList } = info;
						try {
							const newData = [...batchRefuseData];
							
							// 检查是否有上传中的文件
							const hasUploading = newFileList.some(item => item.status === 'uploading');
							newData[index].imgLoading = hasUploading;
							
							// 处理文件列表：保留所有状态的文件，只对成功的文件设置url和name
							const _newFileList = newFileList?.map((item) => {
								// 只对上传成功的文件设置url和name
								if (item.status === 'done' && item?.response?.data?.fileUrl) {
									return {
										...item,
										url: item.response.data.fileUrl,
										name: item.response.data.fileName,
									};
								}
								return item;
							});
							
							newData[index].proofFileList = _newFileList || [];
							setBatchRefuseData(newData);
							
							// 如果有文件上传成功，清除校验错误
							const hasSuccessFile = _newFileList?.some(item => item.status === 'done' && item.url);
							if (hasSuccessFile) {
								clearValidationError(record.id, 'proofFileList');
							}
						} catch (error) {
							console.log("error: ", error);
							const newData = [...batchRefuseData];
							newData[index].imgLoading = false;
							setBatchRefuseData(newData);
						}
					},
					onPreview: async(file) => {
						console.log('%c [ file ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', file);
						// 只有上传成功的文件才能预览
						if (file?.url && file.status === 'done' && successImages.length > 0) {
							// 找到点击图片在成功图片列表中的索引
							const clickedIndex = successImages.findIndex(img => img.uid === file.uid);
							
							// 通过DOM操作触发对应的预览图片
							setTimeout(() => {
								const previewContainer = document.querySelector(`[data-preview-id="${record.id}"]`);
								if (previewContainer) {
									const targetImage = previewContainer.querySelector(`[data-index="${clickedIndex >= 0 ? clickedIndex : 0}"]`) as HTMLElement;
									if (targetImage) {
										targetImage.click();
									}
								}
							}, 50);
						}
					},
					onRemove: (file) => {
						// 手动移除文件时的处理
						const newData = [...batchRefuseData];
						newData[index].proofFileList = newData[index].proofFileList.filter(item => item.uid !== file.uid);
						setBatchRefuseData(newData);
						
						// 如果移除后没有文件了，可能需要重新显示错误
						if (newData[index].proofFileList.length === 0) {
							// 重新检查是否需要上传凭证
							const currentReason = (record.refuseReasonList || []).find(r => r.reasonId === record.refuseReasonId);
							if (record.tbSellerType || currentReason?.evidenceNeed === 'Y') {
								setValidationErrors(prev => ({
									...prev,
									[record.id]: {
										...prev[record.id],
										proofFileList: '请上传凭证'
									}
								}));
							}
						}
					},
				};

				return (
					<div style={ { position: 'relative' } }>
						<Upload { ...uploadProps } fileList={ record.proofFileList || [] } key={ `upload-${record.id}-${index}` } disabled={ record.imgLoading }>
							{(record.proofFileList || []).length < maxCount ? (
								<div style={ { 
									display: 'flex', 
									flexDirection: 'column', 
									alignItems: 'center', 
									justifyContent: 'center',
									cursor: record.imgLoading ? 'not-allowed' : 'pointer',
									// border: hasError ? '1px solid #ff4d4f' : undefined,
									// borderRadius: '6px',
								} }
								>
									<PlusCircleOutlined style={ { fontSize: '22px', color: record.imgLoading ? '#999' : (hasError ? '#ff4d4f' : '#1890ff') } } />
								</div>
							) : null}
						</Upload>
						
						{/* 错误提示 */}
						{hasError && (
							<span style={ { color: '#ff4d4f', fontSize: '12px', whiteSpace: 'nowrap', position: 'absolute', top: '6px', right: '10px' } }>
								{validationErrors[record.id]?.proofFileList}
							</span>
						)}
						
						{/* 隐藏的多图预览组件 */}
						{successImages.length > 0 && (
							<div 
								data-preview-id={ record.id }
								style={ { 
									position: 'absolute',
									left: '-9999px',
									top: '-9999px',
									visibility: 'hidden',
									pointerEvents: 'none'
								} }
							>
								<Image.PreviewGroup key={ successImages.map(item => item.uid).join(',') }>
									{successImages.map((file, imgIndex) => (
										<Image
											key={ `preview-${record.id}-${file.uid}` }
											src={ file.url }
											alt={ file.name || '拒绝凭证' }
											data-index={ imgIndex }
											style={ { width: 1, height: 1 } }
										/>
									))}
								</Image.PreviewGroup>
							</div>
						)}
					</div>
				);
			},
		},
		{
			title: '操作',
			width: 50,
			render: (_, record, index) => (
				<Button
					type="link"
					danger
					size="small"
					onClick={ () => {
						const newData = batchRefuseData.filter((_, i) => i !== index);
						// 重新设置序号
						const reIndexData = newData.map((item, i) => ({ ...item, index: i + 1 }));
						setBatchRefuseData(reIndexData);
						// 清除该行的校验错误
						setValidationErrors(prev => {
							const newErrors = { ...prev };
							delete newErrors[record.id];
							return newErrors;
						});
					} }
					className={ s.deleteBtn }
				>
					删除
				</Button>
			),
		}
	];

	// 表格选择配置
	const rowSelection = {
		columnWidth: 40,
		selectedRowKeys,
		onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
			setSelectedRowKeys(selectedRowKeys);
			setSelectedRows(selectedRows);
		},
		onSelectAll: (selected: boolean, selectedRows: any[], changeRows: any[]) => {
			if (selected) {
				// 全选
				const allKeys = batchRefuseData.map(item => item.id);
				setSelectedRowKeys(allKeys);
				setSelectedRows(batchRefuseData);
			} else {
				// 取消全选
				setSelectedRowKeys([]);
				setSelectedRows([]);
			}
		},
		onSelect: (record: any, selected: boolean, selectedRows: any[]) => {
			if (selected) {
				// 选中
				setSelectedRowKeys([...selectedRowKeys, record.id]);
				setSelectedRows([...selectedRows]);
			} else {
				// 取消选中
				const newKeys = selectedRowKeys.filter(key => key !== record.id);
				const newRows = selectedRows.filter(row => row.id !== record.id);
				setSelectedRowKeys(newKeys);
				setSelectedRows(newRows);
			}
		},
		getCheckboxProps: (record: any) => ({
			disabled: false, // 可以根据需要设置禁用条件
			name: record.id,
		}),
	};

	// 批量拒绝表单验证 - 修改验证逻辑，返回错误信息并设置错误状态
	const validateBatchRefuseForm = () => {
		const errors = [];
		const newValidationErrors = {};
		
		batchRefuseData.forEach((item, index) => {
			const rowNum = index + 1;
			const itemErrors = {};
			
			// 验证拒绝理由（所有平台都需要选择拒绝理由）
			if (!item.refuseReasonId) {
				errors.push(`第${rowNum}行：请选择拒绝理由`);
				itemErrors.refuseReasonId = '请选择拒绝理由';
			}
			
			// 验证拒绝说明
			if (!item.refuseMessage) {
				errors.push(`第${rowNum}行：请输入拒绝说明`);
				itemErrors.refuseMessage = '请输入拒绝说明';
			}
			
			// 验证拒绝凭证（使用当前售后单的拒绝理由列表）
			if (shouldShowProofUpload(item.platform)) {
				const currentReason = (item.refuseReasonList || []).find(r => r.reasonId === item.refuseReasonId);
				// 参考RefuseRefundModal：item.tbSellerType || curReason?.evidenceNeed === 'Y'
				if ((item.tbSellerType || currentReason?.evidenceNeed === 'Y') && (!item.proofFileList || item.proofFileList.length === 0)) {
					errors.push(`第${rowNum}行：请上传拒绝凭证`);
					itemErrors.proofFileList = '请上传凭证';
				}
			}
			
			// 如果有错误，记录到错误状态中
			if (Object.keys(itemErrors).length > 0) {
				newValidationErrors[item.id] = itemErrors;
			}
		});
		
		// 设置错误状态
		setValidationErrors(newValidationErrors);
		
		return errors;
	};

	// 清除校验错误
	const clearValidationError = (itemId: string, field: string) => {
		setValidationErrors(prev => {
			const newErrors = { ...prev };
			if (newErrors[itemId]) {
				delete newErrors[itemId][field];
				if (Object.keys(newErrors[itemId]).length === 0) {
					delete newErrors[itemId];
				}
			}
			return newErrors;
		});
	};

	// 执行批量拒绝操作 - 修改为使用BatchRefuseRefundApi接口并采用资源竞争型并发处理
	const executeBatchRefuse = async() => {
		// 按平台分组数据
		const platformGroups = {};
		batchRefuseData.forEach(item => {
			const platform = item.platform;
			if (!platformGroups[platform]) {
				platformGroups[platform] = [];
			}
			platformGroups[platform].push(item);
		});

		// 创建任务队列
		const batchSize = 5;
		const threadNum = 2; // 并发线程数默认为2
		const taskQueue = [];
		
		// 构建任务队列（保持原有的平台轮询逻辑）
		const platformKeys = Object.keys(platformGroups);
		const remainingData = { ...platformGroups };
		
		while (true) {
			const batchData = [];
			for (let j = 0; j < batchSize; j++) {
				let found = false;
				for (const platform of platformKeys) {
					if (remainingData[platform] && remainingData[platform].length > 0) {
						batchData.push(remainingData[platform].shift());
						found = true;
						break;
					}
				}
				if (!found) break;
			}
			if (batchData.length > 0) {
				taskQueue.push(batchData);
			} else {
				break;
			}
		}

		// 并发控制变量
		let timer = 0;
		let completedCount = 0;
		const totalTasks = taskQueue.length;
		const totalItems = batchRefuseData.length;
		const allResults = [];
		let successCount = 0;
		let failCount = 0;
		let batchIndex = 0; // 批次索引
		let currentCount = 0; // 当前处理数量

		console.log(`%c[批量${modalMap[curType]}] 开始处理，共${totalItems}笔数据，分${totalTasks}批处理，每批最多${batchSize}笔，并发数${threadNum}`, 'color: #1890ff; font-weight: bold;');

		// 显示进度
		setProgressInfo({
			current: 0,
			total: totalItems,
			currentBatch: 0,
			totalBatch: Math.ceil(totalTasks / threadNum),
			successCount: 0,
			failCount: 0,
			processingText: `执行批量${modalMap[curType]}中...`
		});
		setProgressVisible(true);

		// API调用函数
		const callApi = async(batchData, callback, currentBatchIndex) => {
			const startIndex = currentBatchIndex * batchSize + 1;
			const endIndex = Math.min(startIndex + batchData.length - 1, totalItems);
			
			console.log(`%c[批量${modalMap[curType]}] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）开始请求...`, 'color: #52c41a;');
			
			try {
				// 构建refuseRefundInfoList参数
				const refuseRefundInfoList = batchData.map(item => {
					// 获取拒绝理由文本 - 使用当前售后单的拒绝理由列表
					const currentReason = (item.refuseReasonList || []).find(r => r.reasonId === item.refuseReasonId);
					
					// 获取上传的图片URL列表
					const proofFileList = item.proofFileList?.map((file) => file.url).filter(url => url) || [];
					
					return {
						id: item.id,
						proofFileList,
						refuseMessage: item.refuseMessage,
						reasonId: item.refuseReasonId,
						reasonText: currentReason?.reasonText || '',
						evidenceNeed: currentReason?.evidenceNeed || 'N',
						mediaId: '', // 根据需要设置
					};
				});
				
				const res = await BatchRefuseRefundApi({
					operateType: curType,
					scmSelectRefundSource: isDistributor ? 0 : 1, // 默认为0-正常售后单列表
					refuseRefundInfoList,
				});
				
				const batchSuccess = res.successNum || 0;
				const batchFail = res.failedNum || 0;
				const batchTotal = batchData.length;
				
				console.log(`%c[批量${modalMap[curType]}] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）处理完成，共提交${batchTotal}笔，${modalMap[curType]}成功${batchSuccess}笔${batchFail > 0 ? `，失败${batchFail}笔` : ''}`, 
					batchFail > 0 ? 'color: #ff4d4f; font-weight: bold;' : 'color: #52c41a; font-weight: bold;');
				
				// 如果有失败的，打印失败信息
				if (res.batchResults && res.batchResults.some(item => item.errorMessage !== '成功')) {
					const failedItems = res.batchResults.filter(item => item.errorMessage !== '成功');
					console.log(`%c[批量${modalMap[curType]}] 第${currentBatchIndex + 1}批失败详情:`, 'color: #ff4d4f;', failedItems.map(item => `${item.operationId}: ${item.errorMessage}`).join('; '));
				}
				
				callback(res, batchData, currentBatchIndex);
			} catch (error) {
				console.log(`%c[批量${modalMap[curType]}] 第${currentBatchIndex + 1}批（第${startIndex}-${endIndex}笔）请求异常:`, 'color: #ff4d4f; font-weight: bold;', error);
				
				const errorResult = {
					code: 1,
					message: error.message || error.errorMessage || '请求失败',
					batchResults: batchData.map(item => ({
						operationId: item.id,
						errorMessage: error.message || error.errorMessage || '请求失败',
						success: false
					})),
					successNum: 0,
					failedNum: batchData.length
				};
				callback(errorResult, batchData, currentBatchIndex);
			}
		};

		// 任务处理器 - 资源竞争型
		const handler = () => {
			if (!taskQueue.length) {
				timer++;
				if (timer !== threadNum) return; // 等待所有线程完成
				
				// 所有任务完成
				console.log(`%c[批量${modalMap[curType]}] 所有批次处理完成！总计${totalItems}笔，成功${successCount}笔，失败${failCount}笔`, 
					failCount > 0 ? 'color: #ff4d4f; font-weight: bold; font-size: 14px;' : 'color: #52c41a; font-weight: bold; font-size: 14px;');
				
				const finalResult = {
					successNum: successCount,
					failedNum: failCount,
					batchResults: []
				};
				
				allResults.forEach(result => {
					if (result && result.batchResults) {
						finalResult.batchResults.push(...result.batchResults);
					}
				});

				setTimeout(() => {
					setProgressVisible(false);
					setRefundResult(finalResult);
					setResultVisible(true);
				}, 200);
				return;
			}

			// 处理下一个任务
			const currentBatch = taskQueue.shift();
			const currentBatchIndex = batchIndex++;
			
			callApi(currentBatch, (result, batchData, batchIdx) => {
				completedCount++;
				allResults.push(result);
				
				const batchSuccess = result.successNum || 0;
				const batchFail = result.failedNum || 0;
				successCount += batchSuccess;
				failCount += batchFail;

				currentCount += batchData.length;

				setProgressInfo(prev => ({
					...prev,
					current: Math.min(currentCount, totalItems),
					currentBatch: Math.ceil(completedCount / threadNum),
					successCount,
					failCount
				}));
				
				// 打印进度信息
				console.log(`%c[批量${modalMap[curType]}] 进度更新: 已完成${completedCount}/${totalTasks}批，累计成功${successCount}笔，累计失败${failCount}笔`, 'color: #1890ff;');

				// 继续处理下一个任务
				handler();
			}, currentBatchIndex);
		};

		// 启动并发线程
		for (let i = 0; i < threadNum; i++) {
			handler();
		}
	};

	// 确认批量拒绝
	const handleBatchRefuseConfirm = async() => {
		if (batchRefuseData.length === 0) {
			message.error('没有可操作的数据');
			return;
		}
		
		// 表单验证
		const errors = validateBatchRefuseForm();
		if (errors.length > 0) {
			message.error(errors[0]);
			return;
		}
		
		// 检查是否有上传中的文件
		const hasUploading = batchRefuseData.some(item => item.imgLoading);
		if (hasUploading) {
			message.error('请等待文件上传完成');
			return;
		}
		
		setBatchRefuseVisible(false);
		
		// 清空勾选项和编辑状态
		setSelectedRowKeys([]);
		setSelectedRows([]);
		setValidationErrors({});
		setBatchEditModalInfo({ visible: false, type: "" });
		
		// 开始执行批量拒绝操作
		await executeBatchRefuse();
	};

	// 取消批量拒绝弹框
	const handleBatchRefuseCancel = () => {
		setBatchRefuseVisible(false);
		setBatchRefuseData([]);
		// 移除全局拒绝理由列表的清理
		// setRefuseReasonList([]);
		setSelectedRowKeys([]);
		setSelectedRows([]);
		setValidationErrors({}); // 清除校验错误
		setBatchEditModalInfo({ visible: false, type: "" });
	};

	// 添加批量拒绝弹框关闭后的处理函数
	const handleBatchRefuseAfterClose = () => {
		// 确保弹框完全关闭后清空所有相关状态
		setBatchRefuseData([]);
		setSelectedRowKeys([]);
		setSelectedRows([]);
		setValidationErrors({});
		setBatchEditModalInfo({ visible: false, type: "" });
	};

	const cancelResultModal = () => {
		setResultVisible(false);
		if (refundResult?.successNum > 0) {
			initProps?.afterOk();
		}
	};

	const copyFailedOrderNumbers = () => {
		// 筛选出失败的订单号
		const failedOrderNumbers = refundResult?.batchResults
			?.filter(item => item.errorMessage && item.errorMessage !== '成功')
			?.map(item => item.operationId)
			?.filter(Boolean) || [];
		
		if (failedOrderNumbers.length === 0) {
			message.info('没有失败的订单号需要复制');
			return;
		}
		
		// 将单号用换行符连接
		const copyText = failedOrderNumbers.join(',');
		
		copyToPaste(copyText);
	};

	const initHandle = (data) => {
		originList = data.list;
		setInitProps(data);
		setCurType(data.operateType);

		// 判断是否支持该操作，不支持则不进行后续操作
		if (checkUnsupportedTypes(data.list, data.operateType)) {
			return;
		}

		// 没有异常开始处理
		handleFilterAndContinue(data.list);
	};

	useEffect(() => {
		events.on('batchRefuseMoney', initHandle);
		return () => {
			events.off('batchRefuseMoney', initHandle);

			originList = [];
		};
	}, []);

	// 添加批量编辑Modal关闭处理
	const handleBatchEditModalCancel = () => {
		setBatchEditModalInfo({ visible: false, type: '' });
	};

	const idToIndexMap = useMemo(() => {
		const map = new Map();
		batchRefuseData.forEach((item, index) => {
			map.set(item.id, index);
		});
		return map;
	}, [batchRefuseData]);

	// 添加批量编辑拒绝说明的处理函数
	const handleBatchEditRefuseMessage = (values: { addType: string; refuseMessage: string }) => {
		const { addType, refuseMessage } = values;
		
		console.time('handleBatchEditRefuseMessage');
		// 更新选中行的拒绝说明
		const newBatchRefuseData = [...batchRefuseData];
		
		// 收集需要清除校验错误的项，批量处理
		const itemsToClearValidation = [];
		
		selectedRows.forEach(selectedRow => {
			const index = newBatchRefuseData.findIndex(item => item.id === selectedRow.id);
			if (index !== -1) {
				let newRefuseMessage = '';
				const currentMessage = newBatchRefuseData[index].refuseMessage || '';
				
				switch (addType) {
					case 'replace':
						// 覆盖
						newRefuseMessage = refuseMessage;
						break;
					case 'prepend':
						// 添加至说明之前
						newRefuseMessage = currentMessage ? `${refuseMessage}${currentMessage}` : refuseMessage;
						break;
					case 'append':
						// 添加至说明之后
						newRefuseMessage = currentMessage ? `${currentMessage}${refuseMessage}` : refuseMessage;
						break;
					default:
						newRefuseMessage = refuseMessage;
				}
				
				newBatchRefuseData[index].refuseMessage = newRefuseMessage;
				
				// 收集需要清除校验错误的项，而不是立即清除
				itemsToClearValidation.push(selectedRow.id);
			}
		});
		
		// 批量清除校验错误，只触发一次状态更新
		if (itemsToClearValidation.length > 0) {
			setValidationErrors(prev => {
				const newErrors = { ...prev };
				itemsToClearValidation.forEach(itemId => {
					if (newErrors[itemId]) {
						delete newErrors[itemId].refuseMessage;
						if (Object.keys(newErrors[itemId]).length === 0) {
							delete newErrors[itemId];
						}
					}
				});
				return newErrors;
			});
		}
		
		console.timeEnd('handleBatchEditRefuseMessage');
		setBatchRefuseData(newBatchRefuseData);
		setBatchEditModalInfo({ visible: false, type: '' });
		// message.success('批量设置拒绝说明成功');
	};

	// 添加批量编辑拒绝凭证的处理函数
	const handleBatchEditProofFileList = (fileList: any[]) => {
		// 根据平台类型处理图片
		const newBatchRefuseData = [...batchRefuseData];
		
		// 收集需要清除校验错误的项，批量处理
		const itemsToClearValidation = [];
		
		selectedRows.forEach(selectedRow => {
			const index = newBatchRefuseData.findIndex(item => item.id === selectedRow.id);
			if (index !== -1) {
				const platform = selectedRow.platform;
				let processedFileList = [];
				
				// 根据平台类型处理图片数量
				if ([PLAT_FXG, PLAT_KS].includes(platform)) {
					// 抖店/快手正常回显（最多5张）
					processedFileList = fileList.slice(0, 5);
				} else if ([PLAT_TB, PLAT_TM].includes(platform)) {
					// 淘宝/天猫取第1张
					processedFileList = fileList.slice(0, 1);
				} else {
					// 其余平台跳过处理
					processedFileList = [];
				}
				
				// 只处理支持上传凭证的平台
				if (shouldShowProofUpload(platform)) {
					newBatchRefuseData[index].proofFileList = processedFileList;
					
					// 收集需要清除校验错误的项，而不是立即清除
					itemsToClearValidation.push(selectedRow.id);
				}
			}
		});
		
		// 批量清除校验错误，只触发一次状态更新
		if (itemsToClearValidation.length > 0) {
			setValidationErrors(prev => {
				const newErrors = { ...prev };
				itemsToClearValidation.forEach(itemId => {
					if (newErrors[itemId]) {
						delete newErrors[itemId].proofFileList;
						if (Object.keys(newErrors[itemId]).length === 0) {
							delete newErrors[itemId];
						}
					}
				});
				return newErrors;
			});
		}
		
		setBatchRefuseData(newBatchRefuseData);
		setBatchEditModalInfo({ visible: false, type: '' });
		// message.success('批量设置拒绝凭证成功');
	};

	return (
		<>
			{/* 结果弹框 */}
			{ resultVisible && (
				<Modal
					title="系统提示"
					visible
					centered
					closable
					width={ 600 }
					onCancel={ cancelResultModal }
					footer={ (
						<div className="r-flex r-jc-sb r-ai-c">
							<div>
								{refundResult?.failedNum > 0 && (
									<Button onClick={ copyFailedOrderNumbers }>
										批量复制所有失败单号
									</Button>
								)}
							</div>
							<Button type="primary" onClick={ () => setResultVisible(false) }>
								确定
							</Button>
						</div>
					) }
					afterClose={ cancelResultModal }
					destroyOnClose
				>
					<p className="r-fs-18 r-fw-500 r-ta-c">{modalMap[curType]}</p>
					<p className="r-ta-c r-mt-5">提交{modalMap[curType]}后，平台会有延迟，请不要短时间内多次提交</p>
					<div className="r-flex r-jc-c r-mb-10">
						<div>成功：<span className="r-fs-16 r-c-success">{refundResult?.successNum}</span> <span className="r-fs-16">单</span></div>
						<div className="r-ml-20">失败：<span className="r-fs-16 r-c-error">{refundResult?.failedNum}</span> <span className="r-fs-16">单</span></div>
					</div >
					<div style={ { maxHeight: 300, overflow: 'auto' } } >
						{
							refundResult?.batchResults?.map(i => <p key={ i?.operationId }>售后单号：{i?.operationId} {i?.errorMessage?.replace(/售后订单/g, '售后单')}</p>)
						}
					</div>
				</Modal>
			) }
			
			{/* 进度弹框 */}
			<Modal
				title={ (
					<div className="r-flex r-ai-c">
						<ExclamationCircleFilled style={ { color: '#faad14', fontSize: '20px', marginRight: '8px' } } />
						<span>提示</span>
					</div>
				) }
				visible={ progressVisible }
				centered
				closable={ false }
				width={ 400 }
				maskClosable={ false }
				keyboard={ false }
				footer={ null }
			>
				<div>
					<p className="r-fs-14 r-mb-16">
						执行批量{operateTypeToBtnEnum[curType] || curType || ''}中... {progressInfo.current}/{progressInfo.total}
					</p>
					
					<div className="r-fs-12 r-c-666">
						注意：关闭页面会导致任务中断；如过程中不小心关闭页面，可至售后单日志查看操作结果
					</div>
				</div>
			</Modal>

			{/* 异常弹框 */}
			{ unsupportedTypesVisible && (
				<Modal
					title="系统提示"
					visible
					centered
					closable={ false }
					keyboard={ false }
					width={ 480 }
					maskClosable={ false }
					onCancel={ () => setUnsupportedTypesVisible(false) }
					footer={ (
						<div className="r-flex r-ai-c">
							<div key="info" style={ { flex: 1, textAlign: 'left', color: '#666', fontSize: '12px' } }>
								过滤异常后勾选（{filteredNormalList.length}单）
							</div>
							{
								filteredNormalList?.length > 0 ? (
									<div className="r-flex r-ai-c r-jc-e">
										<Button 
											key="confirm" 
											type="primary" 
											onClick={ () => {
											// 计算异常数据
												const normalIds = new Set(filteredNormalList.map(i => i.id));
												const abnormalList = originList.filter(i => !normalIds.has(i.id));
												console.log('【批量操作-正常数据】', filteredNormalList);
												console.log('【批量操作-异常数据】', abnormalList);

												setUnsupportedTypesVisible(false);
												handleFilterAndContinue(filteredNormalList);
											} }
										>
											跳过异常 操作正常数据
										</Button>
										<Button key="cancel" onClick={ () => setUnsupportedTypesVisible(false) }>
											知道了
										</Button>
									</div>
								) : (
									<div className="r-flex r-ai-c r-jc-e">
										<Button key="cancel" type="primary" onClick={ () => setUnsupportedTypesVisible(false) }>
											知道了
										</Button>
									</div>
								)
							}
						
						</div>
					) }
					destroyOnClose
				>
					<p>所选售后单中部分数据暂不支持批量{modalMap[curType]}操作，请不要勾选以下售后单：</p>
					<p className="r-mt-8">
						1、{
							curType === afterSaleOperateType.拒绝仅退款 
								? '拼多多、1688、淘工厂、京东、手工售后单、无主售后单'
								: '拼多多、1688、淘工厂、京喜、手工售后单、无主售后单'
						}
					</p>
					<p>2、未审核通过的售后单（含需进行异常处理的售后单）</p>
				</Modal>
			) }

			{/* 批量拒绝弹框 */}
			<Modal
				title={ modalMap[curType] }
				visible={ batchRefuseVisible }
				centered
				width={ 1200 }
				maskClosable={ false }
				keyboard={ false }
				onCancel={ handleBatchRefuseCancel }
				onOk={ handleBatchRefuseConfirm }
				afterClose={ handleBatchRefuseAfterClose }
				className={ s.batchRefuseModal }
			>
				<div className="r-mb-16">
					<Table
						dataSource={ batchRefuseData }
						columns={ batchRefuseColumns }
						rowSelection={ rowSelection }
						pagination={ false }
						size="small"
						scroll={ { y: "calc(100vh - 380px)" } }
						rowKey={ (record) => record.id }
						bordered
					/>
				</div>
			</Modal>

			{/* 批量编辑拒绝说明弹框 */}
			{
				batchEditModalInfo.visible && batchEditModalInfo.type === 'refuseMessage' && (
					<BatchEditRefuseMessageModal
						visible
						onCancel={ handleBatchEditModalCancel }
						onOk={ handleBatchEditRefuseMessage }
						selectedRows={ selectedRows }
					/>
				)
			}

			{/* 批量编辑拒绝凭证弹框 */}
			{
				batchEditModalInfo.visible && batchEditModalInfo.type === 'proofFileList' && (
					<BatchEditProofModal
						visible
						onCancel={ handleBatchEditModalCancel }
						onOk={ handleBatchEditProofFileList }
						selectedRows={ selectedRows }
					/>
				)
			}
		</>
	);
};

export default BatchRefuseMoney;