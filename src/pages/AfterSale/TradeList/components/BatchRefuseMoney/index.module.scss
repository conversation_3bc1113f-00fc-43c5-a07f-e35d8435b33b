.batchRefuseModal {
	.deleteBtn{
		padding: 0!important;
	}

	:global{

		.ant-table-body{
			overflow-y: auto!important;
			min-height: 400px;
		}

		.ant-upload-list-picture-card-container{
			width: 68px;
			height: 68px;
		}
		.ant-upload.ant-upload-select-picture-card{
			width: 30px;
			height: 30px;
			border: 1px solid transparent;
			background-color: transparent;
			transition: border-color 0.3s ease;

			&:hover{
				// border-color: #1890ff;
			}
		}

		.ant-upload-list-picture .ant-upload-list-item-thumbnail, .ant-upload-list-picture-card .ant-upload-list-item-thumbnail{
			line-height: 18px!important;
		}
		.ant-upload-list-picture-card .ant-upload-list-item-progress{
			bottom: 0!important;
		}


		// Select 错误状态样式
		.select-error.ant-select:not(.ant-select-customize-input) .ant-select-selector {
			border-color: #ff4d4f !important;
			box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1) !important;
		}

		// Select 错误状态聚焦时的样式
		.select-error.ant-select:not(.ant-select-customize-input).ant-select-focused .ant-select-selector {
			border-color: #ff4d4f !important;
			box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
		}
	}
}