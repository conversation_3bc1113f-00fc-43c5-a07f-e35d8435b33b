import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Radio, Space } from 'antd';
import { useForm } from 'antd/es/form/Form';
import s from './index.module.scss';

const { TextArea } = Input;

interface BatchEditRefuseMessageModalProps {
	visible: boolean;
	onCancel: () => void;
	onOk: (values: { addType: string; refuseMessage: string }) => void;
	selectedRows: any[];
}

const BatchEditRefuseMessageModal: React.FC<BatchEditRefuseMessageModalProps> = ({
	visible,
	onCancel,
	onOk,
	selectedRows = []
}) => {
	const [form] = useForm();
	const [loading, setLoading] = useState(false);

	// 处理确定按钮点击
	const handleOk = async() => {
		try {
			setLoading(true);
			const values = await form.validateFields();
			onOk(values);
		} catch (error) {
			console.error('表单验证失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 处理取消按钮点击
	const handleCancel = () => {
		form.resetFields();
		onCancel();
	};

	// 弹框关闭后重置表单
	const handleAfterClose = () => {
		form.resetFields();
	};

	return (
		<Modal
			title="批量设置拒绝说明"
			visible={ visible }
			onOk={ handleOk }
			onCancel={ handleCancel }
			afterClose={ handleAfterClose }
			okText="确定"
			cancelText="取消"
			confirmLoading={ loading }
			width={ 500 }
			centered
			className={ s.batchEditRefuseMessageModal }
			destroyOnClose
		>
			<Form
				form={ form }
				initialValues={ {
					addType: 'replace', // 默认选择覆盖
					refuseMessage: ''
				} }
			>
				<Form.Item
					label="添加方式："
					name="addType"
					// rules={ [{ required: true, message: '请选择添加方式' }] }
				>
					<Radio.Group className="r-mt-4">
						<Space direction="horizontal">
							<Radio value="replace">覆盖</Radio>
							<Radio value="prepend">添加至说明之前</Radio>
							<Radio value="append">添加至说明之后</Radio>
						</Space>
					</Radio.Group>
				</Form.Item>

				<Form.Item
					label="拒绝说明："
					name="refuseMessage"
					rules={ [
						// { required: true, message: '请输入拒绝说明' },
						// { max: 500, message: '拒绝说明不能超过500个字符' }
					] }
				>
					<TextArea
						placeholder="请输入拒绝说明"
						// maxLength={ 500 }
						rows={ 4 }
						// showCount
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
};

export default BatchEditRefuseMessageModal; 