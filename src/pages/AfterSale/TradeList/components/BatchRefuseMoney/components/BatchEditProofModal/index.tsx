import React, { useState, useMemo } from 'react';
import { Modal, Form, Upload, Radio, Space, Image, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useForm } from 'antd/es/form/Form';
import { PLAT_FXG, PLAT_KS, PLAT_TB, PLAT_TM, isViteDevMode } from '@/constants';
import { getToken } from '@/utils/token';
import s from './index.module.scss';

interface BatchEditProofModalProps {
	visible: boolean;
	onCancel: () => void;
	onOk: (fileList: any[]) => void;
	selectedRows: any[];
}

const BatchEditProofModal: React.FC<BatchEditProofModalProps> = ({
	visible,
	onCancel,
	onOk,
	selectedRows = []
}) => {
	const [form] = useForm();
	const [loading, setLoading] = useState(false);
	const [fileList, setFileList] = useState<any[]>([]);
	const [successImages, setSuccessImages] = useState<any[]>([]);
	const [uploading, setUploading] = useState(false);

	// 上传配置
	const uploadProps = {
		action: location.origin + (isViteDevMode() ? '/api' : '') + '/index/common/uploadFile',
		withCredentials: true,
		maxCount: 5, // 批量设置最多支持5张
		multiple: true,
		listType: "picture-card" as const,
		accept: ".jpg,.jpeg,.png",
		headers: {
			'qnquerystring': getToken() || '',
		},
		data: {
			uploadScene: "UPLOAD_REFUSE_PROOF_PIC",
			module: "AFTER_SALE",
		},
		beforeUpload: (file: File) => {
			const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
			if (!isJpgOrPng) {
				message.error('文件格式只支持JPG、JPEG、PNG!');
				return Upload.LIST_IGNORE;
			}
			// 检查文件大小
			const isLt5M = file.size / 1024 / 1024 < 5;
			if (!isLt5M) {
				message.error('文件大小超过 5MB!');
				return Upload.LIST_IGNORE;
			}
			return isJpgOrPng && isLt5M;
		},
		onChange: (info: any) => {
			const { fileList: newFileList } = info;
			try {
				// 检查是否有上传中的文件
				const hasUploading = newFileList.some((item: any) => item.status === 'uploading');
				setUploading(hasUploading);
				
				// 处理文件列表：保留所有状态的文件，只对成功的文件设置url和name
				const _newFileList = newFileList?.map((item: any) => {
					// 只对上传成功的文件设置url和name
					if (item.status === 'done' && item?.response?.data?.fileUrl) {
						return {
							...item,
							url: item.response.data.fileUrl,
							name: item.response.data.fileName,
						};
					}
					return item;
				});
				console.log('%c [ _newFileList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', _newFileList);
				setFileList(_newFileList || []);

				setSuccessImages((_newFileList || []).filter(file => file.status === 'done' && file.url));
				
			} catch (error) {
				console.log("上传出错: ", error);
				setUploading(false);
			}
		},
		onPreview: async(file: any) => {
			console.log('%c [ file ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', file);
			// 只有上传成功的文件才能预览
			if (file?.url && file.status === 'done') {
				// 直接使用successImages，避免重新计算导致的状态不同步问题
				if (successImages.length > 0) {
					console.log('%c [ successImages ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', successImages);
					// 找到点击图片在成功图片列表中的索引
					const clickedIndex = successImages.findIndex(img => img.uid === file.uid);
					
					// 通过DOM操作触发对应的预览图片
					setTimeout(() => {
						const previewContainer = document.querySelector(`[data-batch-preview]`);
						if (previewContainer) {
							const targetImage = previewContainer.querySelector(`[data-index="${clickedIndex >= 0 ? clickedIndex : 0}"]`) as HTMLElement;
							if (targetImage) {
								targetImage.click();
							}
						}
					}, 50);
				}
			}
		},
		onRemove: (file: any) => {
			// 手动移除文件时的处理
			const newFileList = fileList.filter(item => item.uid !== file.uid);
			setFileList(newFileList);
		},
	};

	// 处理确定按钮点击
	const handleOk = async() => {
		try {
			setLoading(true);
			
			// 检查是否有上传中的文件
			if (uploading) {
				message.error('请等待文件上传完成');
				return;
			}

			// 获取成功上传的文件列表
			const successFiles = fileList.filter(file => file.status === 'done' && file.url);
			
			if (successFiles.length === 0) {
				message.error('请先上传拒绝凭证');
				return;
			}

			onOk(successFiles);
		} catch (error) {
			console.error('处理失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 处理取消按钮点击
	const handleCancel = () => {
		form.resetFields();
		setFileList([]);
		onCancel();
	};

	// 弹框关闭后重置表单
	const handleAfterClose = () => {
		form.resetFields();
		setFileList([]);
		setUploading(false);
	};

	return (
		<Modal
			title="批量设置拒绝凭证"
			visible={ visible }
			onOk={ handleOk }
			onCancel={ handleCancel }
			afterClose={ handleAfterClose }
			okText="确定"
			cancelText="取消"
			confirmLoading={ loading }
			width={ 540 }
			centered
			className={ s.batchEditProofModal }
			destroyOnClose
		>
			<Form
				form={ form }
				initialValues={ {
					addType: 'replace', // 默认选择覆盖
				} }
			>
				<Form.Item
					label="添加方式："
					name="addType"
				>
					<Radio.Group className="r-mt-4">
						<Space direction="horizontal">
							<Radio value="replace">覆盖</Radio>
						</Space>
					</Radio.Group>
				</Form.Item>

				<Form.Item
					label="拒绝凭证："
					name="proofFileList"
					extra={ (
						<p className="r-mt-4">
							仅支持JPG、JPEG、PNG格式，单张大小不超过5M；抖店、快手最多可上传5张，淘宝/天猫仅上传1张（如有多张，取第1张）；其余暂不支持将拒绝凭证上传至平台
						</p>
					) }
				>
					<Upload { ...uploadProps } fileList={ fileList }>
						{/* 当文件数量达到最大值时隐藏上传按钮 */}
						{fileList.length < 5 ? (
							<div className="r-flex r-ai-c r-jc-c">
								<UploadOutlined /> 
								<span className="r-ml-2">上传文件</span>
							</div>
						) : null}
					</Upload>
				</Form.Item>

				{/* 隐藏的多图预览组件 - 完全参考BatchRefuseMoney的实现 */}
				{successImages.length > 0 && (
					<div 
						data-batch-preview
						style={ { 
							position: 'absolute',
							left: '-9999px',
							top: '-9999px',
							visibility: 'hidden',
							pointerEvents: 'none'
						} }
					>
						<Image.PreviewGroup key={ successImages.map(item => item.uid).join(',') }>
							{successImages.map((file, imgIndex) => (
								<Image
									key={ `batch-preview-${file.uid}` }
									src={ file.url }
									alt={ file.name || '拒绝凭证' }
									data-index={ imgIndex }
									style={ { width: 1, height: 1 } }
								/>
							))}
						</Image.PreviewGroup>
					</div>
				)}
			</Form>
		</Modal>
	);
};

export default BatchEditProofModal; 