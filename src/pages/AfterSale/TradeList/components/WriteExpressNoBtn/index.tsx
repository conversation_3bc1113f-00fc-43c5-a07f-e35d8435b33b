import React, { useEffect, useState } from "react";
import { Modal, Form, Select, Input, Button, Tooltip } from "antd";
import memoFn from '@/libs/memorizeFn';
import { UploadExchangeInvoiceNoApi } from "@/apis/aftersale/trade";
import { PLAT_PDD } from "@/constants";


interface BtnProps {
	optName: string,
	data: any
	onFinished?:(params:{success: boolean})=>void
}
const WriteExpressNoBtn = (props:BtnProps) => {
	const { optName = '', data, onFinished } = props;
	const [form] = Form.useForm();
	const [expressData, setExpressData] = useState([]);
	const [visible, setVisible] = useState(false);
	const [confirmLoading, setConfirmLoading] = useState(false);
	console.log('data', data);
	const onOk = () => {
		form.validateFields().then((res) => {
			const { expressNum, expressCode } = res;
			const curExpress = expressData.find(express => express.exCode === expressCode);
			setConfirmLoading(true);
			UploadExchangeInvoiceNoApi({
				id: data?.id,
				exchangeInvoiceNo: expressNum,
				exchangeShipName: curExpress?.exName,
				exchangeShipCode: curExpress?.exCode
			}).then(() => {
				setVisible(false);
				onFinished({ success: true });
			}).finally(() => setConfirmLoading(false));
		});
	};
	useEffect(() => {
		if (visible) {
			memoFn.getExpressList().then(res => {
				setExpressData(res || []);
				// 如果已经有换货补发快递单信息，就取第一个。
				const expressInfo = data?.exchangeInvoices?.[0];
				if (expressInfo) {
					setTimeout(() => {
						form.setFieldsValue({
							expressCode: expressInfo.exchangeShipCode,
							expressNum: expressInfo.exchangeInvoiceNo
						});
					});
				}
			});
		}
	}, [visible]);

	const onClickWrite = () => {
		setVisible(true);
	};


	return (
		<div>
			{
				optName == '补发' && data?.platform == PLAT_PDD
					? (
						<Tooltip title="因平台限制，拼多多补寄售后单暂不支持自动上传快递单号；如【售后高级设置】中已开启发货后自动上传配置，拼多多补发订单发货后，请手动复制快递单号至店铺后台填写">
							<Button size="small" type="primary" onClick={ (e) => { e.stopPropagation(); onClickWrite(); } }>填写{optName}单号</Button>
						</Tooltip>
					)
					: (
						<Button size="small" type="primary" onClick={ (e) => { e.stopPropagation(); onClickWrite(); } }>填写{optName}单号</Button>
					)
			}
			{visible ? (
				<Modal
					visible={ visible }
					title={ `${optName}运单填写` }
					maskClosable={ false }
					onOk={ onOk }
					destroyOnClose
					onCancel={ () => setVisible(false) }
					getContainer={ document.body }
					confirmLoading={ confirmLoading }
				>
					<div>
						<Form form={ form } preserve={ false }>
							<Form.Item
								label="快递公司"
								required
								rules={ [{ required: true, message: '请选择快递公司' }] }
								name="expressCode"
								wrapperCol={ { span: 12 } }
							>
								<Select placeholder="请选择快递公司">
									{
										expressData.map(express => <Select.Option key={ express.exCode }>{express.exName}</Select.Option>)
									}
								</Select>
							</Form.Item>

							<Form.Item
								label="快递单号"
								required
								name="expressNum"
								rules={ [{ required: true, message: '请输入快递单号' }] }
								wrapperCol={ { span: 12 } }
							>
								<Input placeholder="请输入快递单号" />
							</Form.Item>
						</Form>

					</div>
				</Modal>
			) : ''}
		</div>

	);

};

export default WriteExpressNoBtn;
