import React, { useEffect, useState } from 'react';
import { Button, Form, Input, Modal, Select, Upload, Image } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { UploadOutlined, LoadingOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { RcFile, UploadChangeParam, UploadFile, UploadProps } from 'antd/es/upload/interface';
import message from '@/components/message';
import { GetRefuseReasonListApi, RefuseRefundFeeApi, RefuseRefundGoodsApi, RefuseRefundSupplyApi } from '@/apis/aftersale/trade';
import { getBase64 } from '../../utils';
import { GetRefuseReasonListRequest, GetRefuseReasonListResponse } from '@/types/schemas/aftersale/trade';
import { afterSaleOperateType } from '../../constants';
import { PLAT_SPH, PLAT_FXG, PLAT_KS, PLAT_TB, PLAT_TM, isViteDevMode } from '@/constants';
import { getToken } from '@/utils/token';


const modalMap = {
	[afterSaleOperateType.拒绝仅退款]: '拒绝退款',
	[afterSaleOperateType.拒绝退货]: '拒绝退货',
	[afterSaleOperateType.拒绝退货退款]: '拒绝退货退款',
};

interface RefuseRefundModalProps {
	visible:boolean,
	onClose:() => void,
	afterOK:() => void,
	dataSource:any

}
const RefuseRefundModal = (props:RefuseRefundModalProps) => {
	const [form] = useForm();
	const { visible, onClose, dataSource, afterOK } = props;
	const [reason, setReason] = useState<GetRefuseReasonListResponse["data"]>([]);
	const [reasonId, setReasonId] = useState('');
	const [curReason, setCurReason] = useState<GetRefuseReasonListResponse["data"][number]>(null);
	const [imgList, setImgList] = useState([]);
	const [imgLoading, setImgLoading] = useState(false);
	const [previewImage, setPreviewImage] = useState('');
	const [previewOpen, setPreviewOpen] = useState(false);

	useEffect(() => {
		if (reasonId && reason.length) {
			setCurReason(reason.find(r => +r.reasonId === +reasonId) || {});
		}
	}, [reason, reasonId]);


	useEffect(() => {
		if (visible) {
			const { id, platform } = dataSource;
			if (id) {
				message.loading({
					content: '加载中...',
					key: 'getRefuseReasonLoading',
					duration: 0,
				});
				getReasonList({ id });
			}
			
		} else {
			setReasonId('');
			setCurReason(null);
			setImgList([]);
			setPreviewOpen(false);
			setPreviewImage('');
			setImgLoading(false);
		}
	}, [visible]);


	const { loading: getReasonLoading, run: getReasonList } = useRequest(GetRefuseReasonListApi, {
		manual: true,
		onSuccess: (res) => {
			setReason(res);
			message.destroy('getRefuseReasonLoading');
		},
		onError: (err) => {
			onCancel();
			message.destroy('getRefuseReasonLoading');
		}
	});

	const { run: onRefuseRefundFeeApi } = useRequest(RefuseRefundFeeApi, {
		manual: true,
		onSuccess: (res) => {
			afterOK && afterOK();
			onCancel();
		}
	});
	const { run: onRefuseRefundGoodsApi } = useRequest(RefuseRefundGoodsApi, {
		manual: true,
		onSuccess: (res) => {
			afterOK && afterOK();
			onCancel();
		}
	});

	const { run: onRefuseRefundSupplyApi } = useRequest(RefuseRefundSupplyApi, {
		manual: true,
		onSuccess: (res) => {
			afterOK && afterOK();
			onCancel();
		}
	});

	const normFile = (e: any) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e && e.fileList;
	};

	// 根据平台判断是否显示拒绝凭证上传
	const shouldShowProofUpload = () => {
		const { platform } = dataSource;
		return [PLAT_FXG, PLAT_KS, PLAT_TB, PLAT_TM].includes(platform);
	};

	// 根据平台获取上传数量限制
	const getMaxCount = () => {
		const { platform } = dataSource;
		if ([PLAT_FXG, PLAT_KS].includes(platform)) {
			return 5; // 抖店、快手最多5张
		}
		if ([PLAT_TB, PLAT_TM].includes(platform)) {
			return 1; // 淘宝/天猫仅上传1张
		}
		return 1; // 默认1张
	};

	// 点击确定
	const onOk = () => {
		form.validateFields().then(() => {
			onActionRefund();
		});
	};

	const onActionRefund = () => {
		const { id, isRefundGoods, text } = dataSource;
		const { reasonId, refuseMessage } = form.getFieldsValue();
		
		// 获取上传的图片URL列表
		const proofFileList = imgList?.map((item) => item.url).filter(url => url);
		
		let params = {
			id,
			reasonId,
			refuseMessage,
			// proofFile: proofFileList.length > 0 ? proofFileList[0] : '', // 兼容原有单张图片逻辑
			proofFileList, // 新增图片列表参数
			reasonText: curReason?.reasonText,
			evidenceNeed: curReason?.evidenceNeed || 'N',
			operateType: dataSource?.operateType
		};
		if (text === "拒绝补寄") {
			onRefuseRefundSupplyApi(params);
		} else if (isRefundGoods) {
			onRefuseRefundGoodsApi(params);
		} else {
			onRefuseRefundFeeApi(params);
		}
	};

	const onCancel = () => {
		form.resetFields();
		onClose();
		setReason([]);
		setImgList([]);
	};

	const handleReasonChange = (e: string) => {
		setReasonId(e);
	};

	// 上传配置，参考BatchModifyLocalMemoPicModal
	const uploadProps = {
		action: location.origin + (isViteDevMode() ? '/api' : '') + '/index/common/uploadFile',
		withCredentials: true,
		maxCount: getMaxCount(),
		multiple: getMaxCount() > 1,
		listType: "picture-card" as "picture-card",
		accept: ".jpg,.jpeg,.png",
		headers: {
			'qnquerystring': getToken() || '',
		},
		data: {
			uploadScene: "UPLOAD_REFUSE_PROOF_PIC",
			module: "AFTER_SALE",
		},
		beforeUpload: (file: RcFile) => {
			const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
			if (!isJpgOrPng) {
				message.error('文件格式只支持JPG、JPEG、PNG!');
				return Upload.LIST_IGNORE;
			}
			// 检查文件大小
			const isLt5M = file.size / 1024 / 1024 < 5;
			if (!isLt5M) {
				message.error('文件大小超过 5MB!');
				return Upload.LIST_IGNORE;
			}
			return isJpgOrPng && isLt5M;
		},
		onChange: (info) => {
			console.log('%c [ info ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', info);
			const { fileList: newFileList } = info;
			try {
				// 检查是否有上传中的文件
				const hasUploading = newFileList.some(item => item.status === 'uploading');
				setImgLoading(hasUploading);
				
				// 处理文件列表：过滤掉失败的文件，保留成功和上传中的文件
				const _newFileList = newFileList?.filter(item => item.status === 'done').map((item) => {
					// 只处理上传成功的文件
					if (item?.response?.data?.fileUrl) {
						item.url = item.response.data.fileUrl;
						item.name = item.response.data.fileName;
					}
					return item;
				});
				
				setImgList(_newFileList);
			} catch (error) {
				console.log("error: ", error);
				setImgLoading(false);
			}
		},
		onPreview: async(file) => {
			console.log('%c [ file ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', file);
			// 只有上传成功的文件才能预览
			if (file?.url && file.status === 'done') {
				setPreviewImage(file.url || (file.preview as string));
				setPreviewOpen(true);
			}
		},
		onRemove: (file) => {
			// 手动移除文件时的处理
			const newFileList = imgList.filter(item => item.uid !== file.uid);
			setImgList(newFileList);
		},
	};

	const titleText = dataSource?.operateType ? modalMap[dataSource?.operateType] : dataSource?.isRefundGoods ? dataSource?.text || "拒绝退货" : dataSource?.text || '拒绝退款';

	return (
		<Modal
			title={ titleText }
			// visible={ visible && !!reason.length }
			visible={ visible }
			centered
			destroyOnClose
			onOk={ onOk }
			width={ 620 }
			maskClosable={ false }
			onCancel={ onCancel }
			confirmLoading={ imgLoading }
		>
			<Form
				form={ form }
				labelCol={ { span: 4 } }
				wrapperCol={ { span: 18 } }
			>
				<Form.Item
					label="拒绝理由"
					name="reasonId"
					rules={ [{ required: true, message: `请选择${titleText}理由` }] }
				>
					<Select getPopupContainer={ e => e.parentElement } placeholder={ `请选择${titleText}理由` } onChange={ handleReasonChange }>
						{
							reason.map(item => {
								return (
									<Select.Option key={ item.reasonId } value={ item.reasonId }>{item.reasonText}</Select.Option>
								);
							})
						}
					</Select>
				</Form.Item>
				<Form.Item
					label="拒绝说明"
					name="refuseMessage"
					rules={ [{ required: true, message: '请输入拒绝说明' }] }
				>
					<Input.TextArea />
				</Form.Item>

				{
					shouldShowProofUpload() && (
						<Form.Item
							label="拒绝凭证"
							name="proofFile"
							valuePropName="fileList"
							getValueFromEvent={ normFile }
							rules={ [{ required: dataSource?.tbSellerType || curReason?.evidenceNeed === 'Y', message: '请上传拒绝凭证' }] }
							extra={ (
								<p className="r-mt-4">
									仅支持JPG、JPEG、PNG格式，单张大小不超过5M；{getMaxCount() > 1 ? `最多可上传${getMaxCount()}张` : '仅可上传1张'}
								</p>
							) }
						>
							<Upload
								{ ...uploadProps }
								fileList={ imgList }
							>
								{imgList.length < getMaxCount() ? (
									<div
										style={ {
											display: 'flex',
											flexDirection: 'column',
											alignItems: 'center',
											justifyContent: 'center',
											cursor: imgLoading ? 'not-allowed' : 'pointer',
											transition: 'border-color 0.3s ease',
										} }
									>
										{imgLoading ? <LoadingOutlined style={ { fontSize: '16px', color: '#1890ff' } } /> : <UploadOutlined style={ { fontSize: '16px', color: '#1890ff' } } />}
										<div style={ { 
											marginTop: '8px', 
											color: imgLoading ? '#999' : '#666',
											fontSize: '14px'
										} }
										>
											{imgLoading ? '上传中...' : '上传文件'}
										</div>
									</div>
								) : null}
							</Upload>
						</Form.Item>
					)
				}
			</Form>
			
			{previewImage && (
				<Image
					wrapperStyle={ { display: 'none' } }
					preview={ {
						visible: previewOpen,
						onVisibleChange: (visible) => setPreviewOpen(visible),
						afterOpenChange: (visible) => !visible && setPreviewImage(''),
					} }
					src={ previewImage }
				/>
			)}

		</Modal>
	);
};

export default RefuseRefundModal;
