import Pointer from "@/utils/pointTrack/constants";

export const SCAN_REGISTER_FORM_SEARCH_SEARCH_TYPE = {
	default: 'sid',
	options: [
		{ label: '快递单号', value: 'sid', point: Pointer['售后扫描登记_快递单号'] },
		{ label: '手机号', value: 'receiverMobile', point: Pointer['售后扫描登记_手机号'] },
		{ label: '买家昵称', value: 'buyerNick', point: Pointer['售后扫描登记_买家昵称'] },
		{ label: '线上订单号', value: 'order', point: Pointer['售后扫描登记_线上订单编号'] },
		{ label: '收件人', value: 'receiverName', point: Pointer['售后扫描登记_收件人'] },
		{ label: '拿货小标签', value: 'labelId' }
	]
};

export const SearchTypeObj = {
	sid: '快递单号',
	receiverMobile: '手机号',
	buyerNick: '买家昵称',
	order: '线上订单号',
	receiverName: '收件人',
	labelId: '拿货小标签',
}

export const EVENT_BUS = {
	// 售后_扫描登记_切换Tab时触发
	"TABS_ONCHANGE": "aftersale_scan_register_tabs_change",
	// 售后_扫描登记_售后扫描Tab_搜索表单_触发搜索
	"SEARCH_FORM_ONSEARCH": "aftersale_scan_register_aftersale_scan_search_form",
	
	// 售后_扫描登记_售后扫描Tab_搜索结果
	"SCAN_REQUEST_RESULT": "aftersale_scan_register_request_result",

	// 售后_扫描登记_确认收货
	"CONFIRM_RECEIVE": "aftersale_scan_confirm_receive",
	
	// 售后_扫描登记_确认收货成功
	"RECEIVE_SUCCESS": "aftersale_scan_receive_success",

	// 售后_扫描登记_确认收货_表单校验成功
	"CONFIRM_RECEIVE_VALIDATE_SUCCESS": "aftersale_scan_confirm_receive_validate_success",

	// 售后_扫描登记_售后设置_弹窗关闭
	"SCAN_SET_MODAL_CLOSED": "aftersale_scan_scan_set_modal_closed",

	// 售后_扫描登记_售后设置_确认收货后 收货流程全部结束且表格中展示数据也处理完毕
	"CONFIRM_RECEIVE_SUCCESS_FINALLY": "aftersale_scan_confirm_receive_finally_success",

	"UPDATE_ORDER_FORM_DATA": "update_order_form_data",

	// 售后_顶部快捷查询tab数量统计
	"UPDATE_QUANTITY_STATISTICS": "aftersale_quick_query_tab_total",

	// 售后_同步售后单
	"SYNC_AFTERSALE": "aftersale_sync_aftersale",

	// 售后单_授权子账号弹框
	"AUTHORIZE_SUB_ACCOUNTS": "aftersale_authorize_sub_accounts",

};

export const enum ExpressStatusEnum {
	"无物流状态" = 'NO_STATUS',
	"无快递单号" = 'NO_LOGISTICS_NO',
	"无物流状态(有单号)" = 'NO_LOGISTICS_STATUS',
	"待揽收" = 'WAIT_ACCEPT',
	"已揽收" = 'ACCEPT',
	"运输中" = 'TRANSPORT',
	"派送中" = 'DELIVERING',
	"包裹异常" = 'FAILED',
	"拒签" = 'REJECT',
	"待提货" = 'AGENT_SIGN',
	"已签收" = 'SIGN',
	"已代签收" = 'AGENT_SIGN',
}

export const EXPRESS_STATUS_OPTIONS = {
	default: [],
	options: [
		// { label: '无物流状态', value: ExpressStatusEnum.无物流状态 },
		{ label: '无快递单号', value: ExpressStatusEnum.无快递单号 },
		{ label: '无物流状态(有单号)', value: ExpressStatusEnum["无物流状态(有单号)"] },
		{ label: '待揽收', value: ExpressStatusEnum.待揽收 },
		{ label: '已揽收', value: ExpressStatusEnum.已揽收, },
		{ label: '运输中', value: ExpressStatusEnum.运输中, },
		{ label: '派件中', value: ExpressStatusEnum.派送中, },
		{ label: '已代签收', value: ExpressStatusEnum.已代签收, },
		{ label: '已签收', value: ExpressStatusEnum.已签收, },
		{ label: '包裹异常', value: ExpressStatusEnum.包裹异常 }
	]
}; 

export const PACK_INTERCEPT_OPTIONS = {
	default: '',
	options: [
		{ label: '全部', value: '' },
		{ label: '已拦截', value: true },
		{ label: '未拦截', value: false }
	]
};

export const TRADE_LIST_SORT_OPTIONS = {
	default: 1,
	options: [
		{ label: '按申请时间，后申请的在前', value: 1 },
		{ label: '按更新时间，最近更新的在前', value: 2 },
		{ label: '按超时时间，快超时的在前', value: 3 }
	]
};

export const TRADE_SORT_CONFIG_KEY = "REFUND_QUERY_SORT_SET";

export const enum AFTERSALE_REQUEST_SOURCE {
	售后列表 = 1,
	批量登记 = 2,
	单个扫描 = 3,
	扫描记录 = 4,
	快递拦截 = 5,
	小程序扫描查询 = 6,
	小程序扫码登记 = 7,
	小程序批量登记 = 8,
}