import { Dayjs } from "dayjs";
import { PLAT_ICON_MAP, PLAT_TB, PLAT_TM, PLAT_PDD, PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_XHS, PLAT_ALI } from "@/constants";

export enum PolicyTypeEnum {
	"自动同意【未发货仅退款】" = 'ONLY_REFUND',
	"自动同意【退货】" = 'RETURN_GOODS',
	"自动同意【退货退款】" = 'RETURN_AND_REFUND',
	"平台自动策略" = 'PLATFORM_REFUND',
}

export const filterPlatformOptions = [PLAT_TB, PLAT_TM, PLAT_PDD, PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_XHS,PLAT_ALI];

export function getPolicyDescription(type: string): string | undefined {
    for (const [description, value] of Object.entries(PolicyTypeEnum)) {
        if (value == type) {
            return description;
        }
    }
    return undefined; // 如果没有找到对应的描述
}

export enum PeriodOfValidityEnum {
	"未开始" = '0',
	"进行中" = '1',
	"已过期" = '2',
	"长期有效" = '3',
}

export function getPeriodOfValidityDescription(type: string): string | undefined {
    for (const [description, value] of Object.entries(PeriodOfValidityEnum)) {
        if (value == type) {
            return description;
        }
    }
    return undefined; // 如果没有找到对应的描述
}

export interface SearchTableProps {
	pageNo?: number
	pageSize?: number
	timeType?: string
	rangeTime?: Dayjs
	platform?: string
	sellerNick?: string
    [key: string]: any
}

export const openOptions = [
    {
        label: '开启',
        value: '1'
    },
    {
        label: '关闭',
        value: '0'
    },
]

export const strategyTemplates = [
    {
        title: '自动同意【未发货仅退款】',
        info: '商品未发货，买家发起仅退款申请，满足设置的条件，则系统自动同意退款申请',
        tip: '淘宝/天猫、拼多多、抖音、快手、视频号、小红书',
        isHot: true,
        type: PolicyTypeEnum["自动同意【未发货仅退款】"],
        platform: [PLAT_TB,PLAT_TM, PLAT_PDD, PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_XHS],
    },
    {
        title: '自动同意【退货】',
        info: '商家已发货，买家收到货后申请退货退款，满足设置的条件，则系统自动同意退货申请',
        tip: '淘宝/天猫、拼多多、抖音、快手、视频号、小红书',
        isHot: false,
        type: PolicyTypeEnum["自动同意【退货】"],
        platform: [PLAT_TB,PLAT_TM, PLAT_PDD, PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_XHS],
    },
    {
        title: '自动同意【退货退款】',
        info: '退货退款的售后单，当买家已退货/退货入库后，系统自动同意退款申请',
        tip: '淘宝/天猫、拼多多、抖音、快手、视频号、小红书',
        isHot: true,
        type: PolicyTypeEnum["自动同意【退货退款】"],
        platform: [PLAT_TB,PLAT_TM, PLAT_PDD, PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_XHS],
    },
    {
        title: '平台自动策略',
        info: '设置平台自动自动售后策略',
        tip: '淘宝/天猫、拼多多、抖音',
        isHot: false,
        type: PolicyTypeEnum.平台自动策略,
        platform: [PLAT_TB,PLAT_TM, PLAT_PDD, PLAT_FXG, PLAT_ALI],
    },
]

// 支持的平台
export const PLAT_NAME = {
    [PLAT_TB]: '淘宝',
    [PLAT_TM]: '天猫',
    [PLAT_PDD]: '拼多多',
    [PLAT_FXG]: '抖音',
    [PLAT_KS]: '快手',
    [PLAT_SPH]: '视频号',
    [PLAT_XHS]: '小红书',
    [PLAT_ALI]:'1688'
};

export enum MoreFormItemType {
	"限定退款原因" = 'refundReason',
	"订单旗帜" = 'sellerFlag',
	"订单卖家备注" = 'sellerMemo',
	"策略执行时间" = 'policyRangeTime',
	"申请退货时间" = 'createRefundTime',
}

export const PLATFORM_INFO_CONSTANTS = [
    {
        title: '淘系自动退款（AG）',
        platformType: 'TB',
        instructionsUrl: 'https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/kgv9d7'
    },
    {
        title: '拼多多自动退款（PG）',
        platformType: 'PDD',
        instructionsUrl: 'https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/dz8901'
    },
    {
        title: '抖店售后小助手',
        platformType: 'FXG',
        instructionsUrl: 'https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/ytfgg1'
    },
    {
        title: '1688自动退款',
        platformType: 'ALI',
        instructionsUrl: 'https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/pvq0whedzo3lkqsr?singleDoc#'
    }
];