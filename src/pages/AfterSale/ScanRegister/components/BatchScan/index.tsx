import React, { useEffect, useState } from "react";
import { observer } from "mobx-react";
import { Spin } from "antd";
import s from "./index.module.scss";
import ScanForm from "./Components/ScanForm";
import SearchForm from "./Components/SearchForm";
import BatchScanTable from "./Components/Table";
import { 
	SelectBatchScanApi, 
	RefundScanRegistrationApi, 
	RefundScanRegistrationBatchApi,
	DelBatchScanApi, 
	SyncBatchScanApi, 
	BatchScanConfirmForSyncApi,
	BatchScanConfirmApi 
} from "@/apis/aftersale/scanRegister";
import { afterSaleTypeText } from "@/pages/AfterSale/TradeList/utils";
import { getMultiShops } from "@/components-biz/ShopListSelect/shopListUtils";
import useGetState from "@/utils/hooks/useGetState";
import ProgressModal from "./Components/ProgressModal";
import { avoidRepeatReq } from "@/utils/util";
import message from '@/components/message';
import ReceivedResModal from "./Components/ReceivedResModal";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { playAudio } from "@/pages/Trade/ScanPrint/utils";
import voice2 from "@/assets/mp3/匹配2件.mp3";
import voice3 from "@/assets/mp3/匹配3件.mp3";
import voice4 from "@/assets/mp3/匹配4件.mp3";
import voice5 from "@/assets/mp3/匹配5件.mp3";
import voiceMoreThan5 from "@/assets/mp3/超过5件.mp3";
import 售后换货 from '@/assets/mp3/售后换货.mp3';
// import 匹配多条数据 from '@/assets/mp3/匹配多条数据.mp3';
import voice成功 from '@/assets/mp3/成功.mp3';
import voice失败 from '@/assets/mp3/失败.mp3';
import voiceReceived from '@/assets/mp3/包裹已收货.mp3';
import voice未匹配 from '@/assets/mp3/未匹配.mp3';
import voice重复扫描 from '@/assets/mp3/重复扫描.mp3';
import 买家留言 from '@/assets/mp3/买家留言.mp3';
import 卖家备注 from '@/assets/mp3/卖家备注.mp3';
import { BatchScanStatus } from "../../constants";
import userStore from "@/stores/user";
import { TradeDictQueryDictApi } from "@/apis/trade/search";
import { flagAudio } from "@/constants";
// 导入新的批量扫描组件
import BatchScanProgressModal from "./Components/BatchScanProgressModal";
import BatchScanResultModal from "./Components/BatchScanResultModal";


const BatchScan = () => {
	const [loading, setLoading] = useState(false);
	const [tip, setTip] = useState("");
	const [dataSource, setDataSource] = useState([]);
	const [pagination, setPagination] = useState<{[K:string]:any}>({});
	const [lastSearchParams, setLastSearchParams, getLastSearchParams] = useGetState<{[K:string]:any}>({});
	const [progressModalVisible, setProgressModalVisible] = useState(false);
	const [receivedResModaVisible, setReceivedResModaVisible] = useState(false);
	const [receiveSuccessNum, setReceiveSuccessNum] = useState(0);
	const [receiveFailList, setReceiveFailList] = useState([]);
	const [errorExchangeMessages, setErrorExchangeMessages] = useState([]);
	const [percent, setPercent] = useState(0);
	const [shopList, setShopList] = useState([]);
	const [memoFlagConfig, setMemoFlagConfig, getMemoFlagConfig] = useGetState("");
	const [hasPlayedVoice, setHasPlayedVoice, getHasPlayedVoice] = useGetState(false);
	const [missedList, setMissedList] = useState([]); // 未查询到的数据
	const [missedType, setMissedType] = useState(''); // 未查询到的数据类型
	const [statistics, setStatistics] = useState({ refundCount: '', sidDistinctCount: '', tidDistinctCount: '', }); // 未查询到的数据类型

	// 批量扫描专用状态
	const [batchScanProgressVisible, setBatchScanProgressVisible] = useState(false);
	const [batchScanResultVisible, setBatchScanResultVisible] = useState(false);
	const [batchScanSuccessNum, setBatchScanSuccessNum] = useState(0);
	const [batchScanFailList, setBatchScanFailList] = useState([]);

	const handleResult = res => {
		const { list, pageSize, pageNo, total } = res;
		setPagination({ pageSize, current: pageNo, total });
		setDataSource(list);

		if (pageNo == 1) {
			setMissedList(res?.ydNoNotMatchList || []); // 未查询到结果的数据
			setMissedType(res?.tipQueryBackType || ''); // 未查询到结果提示类型 (暂时没有这个字段，保留先)
			setStatistics({
				refundCount: res?.refundCount || '', // 售后总数
				sidDistinctCount: res?.sidDistinctCount || '', // 去重后运单数
				tidDistinctCount: res?.tidDistinctCount || '' // 去重后订单数字
			});
		}
	};

	const queryBatchScanData = (params) => {
		// console.log('%c [ 查询 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
		if (loading) {
			return;
		}
		setTip("正在查询，请稍后...");
		setLoading(true);
		SelectBatchScanApi(params).then(res => {
			handleResult(res);
		}).finally(() => { setLoading(false); }).catch(() => setLoading(false));
	};

	/** ------------------ 收货进度和结果展示相关 ------------------*/

	// 展示收货结果
	const showReceiveRes = ({ successNum, errorList, errorExchangeMessages }, filterNoTrades) => {
		setReceivedResModaVisible(true);
		setReceiveSuccessNum(successNum);
		let noTrades = filterNoTrades?.map(item => {
			return {
				errorInfo: "无主件单没添加商品",
				sid: item.sid,
			};
		}) || [];
		setReceiveFailList([
			...errorList,
			...noTrades
		]);
		setErrorExchangeMessages(errorExchangeMessages); // 换货的错误信息
	};

	// 展示批量扫描结果
	const showBatchScanResult = (successNum, failList) => {
		setBatchScanResultVisible(true);
		setBatchScanSuccessNum(successNum);
		setBatchScanFailList(failList);
	};

	/** --------------------------------------------------------*/


	/** --------------  扫描表单相关  --------------- */

	const handleAudio = (itemNum) => {
		if (itemNum > 5) {
			playAudio(voiceMoreThan5);
			setHasPlayedVoice(true);
		} else {
			switch (itemNum) {
				case 2:
					playAudio(voice2);
					setHasPlayedVoice(true);
					break;
				case 3:
					playAudio(voice3);
					setHasPlayedVoice(true);
					break;
				case 4:
					playAudio(voice4);
					setHasPlayedVoice(true);
					break;
				case 5:
					playAudio(voice5);
					setHasPlayedVoice(true);
					break;
				default:
					playAudio(voice成功);
					setHasPlayedVoice(true);
					break;
			}
		}
	};

	const scanFormOnSearch = (loading) => {
		setTip(loading ? "正在查询，请稍后..." : "");
		setLoading(loading);
		const tableBody = document.querySelector('.scan-table .ant-table-body');
		if (tableBody) {
			tableBody.scrollTop = 0;
		}
	};

	// 语音提示顺序：匹配多条数据>售后换货>有备注＞有旗帜＞有留言>退货商品数量
	const playVoiceByScene = (res) => {
		if (!getHasPlayedVoice()) {
			let playMemoVoice = false;
			let playFlagVoice = false;
			let playMessageVoice = false;
			let playRefundItemType = false; // 售后换货
			let sellerFlag = "";
			res.tradeInfoList.forEach(trade => {
				if (trade.sellerMemo && getMemoFlagConfig().includes("memo")) {
					playMemoVoice = true;
				}
				if (flagAudio[trade.sellerFlag] && getMemoFlagConfig().includes("flag")) {
					playFlagVoice = true;
					sellerFlag = trade.sellerFlag;
				}
				if (trade.buyerMessage && getMemoFlagConfig().includes("message")) {
					playMessageVoice = true;
				}
				if (trade?.afterSaleType == afterSaleTypeText.换货) {
					playRefundItemType = true;
				}
			});
			// 暂时去掉，后面会按配置播报
			// if (res.matchNum && res.matchNum > 1) {
			// 	playAudio(匹配多条数据);
			// 	setHasPlayedVoice(true);
			// 	return;
			// }
			if (playRefundItemType) {
				playAudio(售后换货);
				setHasPlayedVoice(true);
				return;
			}
			if (playMemoVoice) {
				playAudio(卖家备注);
				setHasPlayedVoice(true);
				return;
			}
			if (playFlagVoice) {
				playAudio(flagAudio[sellerFlag]);
				setHasPlayedVoice(true);
				return;
			}
			if (playMessageVoice) {
				playAudio(买家留言);
				setHasPlayedVoice(true);
				return;
			}
			if (res.refundScanMatch == 1) { // 已匹配
				handleAudio(res.matchItemNum);
				return;
			}
		}
	};

	const scanFormOnFinish = (scanFormData) => {
		// 拿到快递公司和快递单号以后，触发扫描登记
		const { expressNumber, expressCompanyInfo } = scanFormData;
		setHasPlayedVoice(false);
		RefundScanRegistrationApi({
			sid: expressNumber,
			companyName: expressCompanyInfo?.exName,
			requestVersion: 'v2',
			refundBatchScanOrigin: "pc",
		}).then(async res => {
			if (res.hasShopAuthority === false) {
				message.error("退货包裹与店铺不匹配");
				setLoading(false);
				return;
			}
			console.log(209, res);
			if (res.refundScanMatch == 2) { // 未匹配
				playAudio(voice未匹配);
			} else {
				playVoiceByScene(res);
			}



			// 如果扫描登记成功，立马查询表格数据，这个时候的采用默认参数查询，而不是表格中的参数，避免查不出刚才扫描的数据
			if (res) {
				const multiShops = await getMultiShops({});
				queryBatchScanData({
					multiShops,
					pageSize: getPageSize(),
					// !! 保留操作人查询 https://tb.raycloud.com/task/664eb3d08312ee0022231ecd
					operatorNick: (document?.getElementById?.('operatorNick') as any)?.value || undefined,
					pageNo: 1,
					shopIsActualSelect: 0, // 页面是否实际勾选店铺查询 0否 1是
					count: false,
				});
			}
		}, (res) => {
			if (res.errorCode === BatchScanStatus.已登记) {
				playAudio(voice重复扫描);
			} else if (res.errorCode == BatchScanStatus.包裹已收货) {
				playAudio(voiceReceived);
			} else {
				playAudio(voice失败);
			}
			setLoading(false);

		}).catch(() => setLoading(false));

	};

	// 重新实现的批量扫描功能
	const onBatchScan = (sidList) => {
		// 如果没有数据，直接返回
		if (!sidList?.length) {
			return;
		}

		// 显示批量扫描进度弹框
		setBatchScanProgressVisible(true);
		setPercent(0);

		const chunkSize = 200; // 每批最多200个
		const maxConcurrentRequests = 4; // 最大并发请求数 4

		// 将 sidList 按 chunkSize 切割
		const chunks = [];
		for (let i = 0; i < sidList.length; i += chunkSize) {
			chunks.push(sidList.slice(i, i + chunkSize));
		}

		let totalSuccessNum = 0; // 总成功数量
		let totalErrorList = []; // 总错误列表

		// 创建所有请求的Promise数组
		const createRequest = async(chunk) => {
			const requestParams = {
				sidList: chunk,
				requestVersion: 'v2',
				refundBatchScanOrigin: "pc",
			};
			
			try {
				const res = await RefundScanRegistrationBatchApi(requestParams);
				
				// 处理返回结果 - 使用类型断言避免类型错误
				const response = res as any;
				if (response) {
					// 成功情况
					// 累加成功数量
					totalSuccessNum += response.successNum || 0;
					// 累加错误列表
					if (response.errorList && response.errorList.length > 0) {
						totalErrorList.push(...response.errorList);
					}
				} else {
					// API调用失败，将整个批次标记为失败
					const errorMessage = response?.errorMessage || response?.errorCode || "批量扫描失败";
					chunk.forEach(sid => {
						totalErrorList.push({
							sid,
							errorInfo: errorMessage
						});
					});
				}
				
				return { success: true, chunk };
			} catch (error) {
				console.error("批量扫描请求失败", error);
				// 请求失败时，构造默认的错误结果
				chunk.forEach(sid => {
					totalErrorList.push({
						sid,
						errorInfo: error?.errorMessage || error?.message || "请求失败"
					});
				});
				return { success: false, chunk, error };
			}
		};

		// 并发执行请求
		const executeConcurrentRequests = async() => {
			const promises = [];
			
			// 创建所有请求的Promise
			chunks.forEach((chunk, index) => {
				const promise = createRequest(chunk).then(result => {
					// 更新进度
					const progress = Math.floor(((index + 1) / chunks.length) * 100);
					setPercent(progress);
					return result;
				});
				promises.push(promise);
			});

			// 等待所有请求完成
			try {
				await Promise.all(promises);
				
				// 所有请求完成后的回调
				setBatchScanProgressVisible(false); // 关闭进度弹框
				
				// 显示批量扫描结果弹框
				if (totalErrorList?.length === 0) {
					message.success("批量登记快递成功");
				} else {
					showBatchScanResult(totalSuccessNum, totalErrorList);
				}

				setTimeout(() => {
					setPercent(0); // 清空进度
				}, 100);

				// 刷新表格数据
				queryBatchScanData({ ...lastSearchParams, count: true });
				
				// 成功回调
				// if (totalSuccessNum > 0) {
				// 	message.success(`批量扫描完成，成功${totalSuccessNum}单`);
				// }
				
				// 失败回调
				// if (totalErrorList.length > 0) {
				// 	message.warning(`批量扫描完成，失败${totalErrorList.length}单`);
				// }
				
			} catch (error) {
				console.error("批量扫描执行失败", error);
				setBatchScanProgressVisible(false);
				message.error("批量扫描执行失败");
				setPercent(0);
			}
		};

		// 开始执行并发请求
		executeConcurrentRequests();
	};

	/** ------------------------------------------- */


	/** --------------  表格相关  --------------- */

	// 批量异步确认收货
	const batchScanConfirmForSync = (params, filterNoTrades, cb, failCb) => {
		setProgressModalVisible(true); // 显示进度弹框
		const chunkSize = 5; // 每个请求切割 5 个数据
		const chunks = []; // 用于存储切割后的数据
		const results = []; // 用于存储所有请求的结果
		const maxConcurrentRequests = 4; // 最大并发请求数 4

		// 将 params.idList 按 chunkSize 切割
		for (let i = 0; i < params.idList.length; i += chunkSize) {
			chunks.push(params.idList.slice(i, i + chunkSize));
		}

		let completedRequests = 0; // 已完成的请求计数
		let activeRequests = 0; // 当前正在执行的请求计数
	
		const processChunk = async(chunk) => {
			const requestParams = { ...params, idList: chunk };
			try {
				const res = await BatchScanConfirmForSyncApi(requestParams); // 调用 API
				results.push(res); // 收集结果
				completedRequests += 1;
				// 修复进度计算，确保为整数
				setPercent(Math.floor((completedRequests / chunks.length) * 100));
			} catch (error) {
				console.error("请求失败", error);
				// 请求失败时，构造默认的错误结果并收集
				const errorResult = {
					successNum: 0,
					errorList: chunk.map((id) => ({
						sid: id,
						errorInfo: error?.errorMessage || "请求失败",
					})),
					errorExchangeMessages: [],
				};
				results.push(errorResult); // 收集失败的结果
				completedRequests += 1;
				setPercent(Math.floor((completedRequests / chunks.length) * 100)); // 更新进度
			} finally {
				activeRequests -= 1; // 请求完成，减少活动请求计数
				if (chunks.length > 0) {
					// 如果还有未处理的 chunk，则启动下一个请求
					const nextChunk = chunks.shift();
					activeRequests += 1; // 启动新请求时增加计数
					processChunk(nextChunk);
				} else if (activeRequests === 0) {
					// 如果所有请求都完成
					setProgressModalVisible(false); // 关闭进度弹框
					
					// 合并所有请求的结果
					const mergedResult = results.reduce(
						(acc, res) => {
							acc.successNum += res.successNum || 0;
							acc.errorList = acc.errorList.concat(res.errorList || []);
							acc.errorExchangeMessages = acc.errorExchangeMessages.concat(res.errorExchangeMessages || []);
							return acc;
						},
						{ successNum: 0, errorList: [], errorExchangeMessages: [] }
					);
	
					showReceiveRes(mergedResult, filterNoTrades); // 显示结果弹框

					setTimeout(() => {
						setPercent(0); // 清空进度
					}, 100);

					if (mergedResult.successNum === 0 && results.every(res => res.successNum === 0)) {
						// 如果所有请求都失败（successNum 为 0 且所有结果的 successNum 都为 0），调用失败回调
						failCb?.();
					} else {
						// 如果有至少一个请求成功，调用成功回调
						cb?.();
					}
				}
			}
		};
	
		// 启动初始的 4 个并发请求
		while (activeRequests < maxConcurrentRequests && chunks.length > 0) {
			const chunk = chunks.shift();
			activeRequests += 1;
			processChunk(chunk);
		}
	
	};

	// 批量收货时
	const onReceive = ({ upStockConfig, idList, batchRegisterAutoCreateExchangeOrder, filterNoTrades, ignoreException }, cb, failCb) => {
		// 单个勾选，或者全部是无主件没有添加商品的直接提示
		if (!idList?.length && filterNoTrades?.length) {
			showReceiveRes({ successNum: 0, errorList: [], errorExchangeMessages: [] }, filterNoTrades);
			failCb?.();
			return;
		}

		const params = {
			autoUpStockAfterConfirmReceiverGoods: upStockConfig,
			batchRegisterAutoCreateExchangeOrder,
			idList,
			ignoreException, // 是否忽略异常0-否1-是
			requestVersion: 'v2',
			hideErrorMessage: true,
		};
		batchScanConfirmForSync(params, filterNoTrades, cb, failCb);
	};

	// 批量删除时
	const onDelete = (selectedRowKeys, cb) => {
		setTip("正在删除所选订单，请稍后...");
		setLoading(true);
		DelBatchScanApi({
			idList: selectedRowKeys
		}).then(res => {
			queryBatchScanData({ ...lastSearchParams, count: true });
			cb && cb();
		}, () => {
			setTip("");
			setLoading(false);
		}).catch(() => {
			setTip("");
			setLoading(false);
		});
	};

	// 批量刷新时
	const onRefresh = (selectedRowKeys, cb) => {
		setTip("正在刷新所选退货包裹，请稍后...");
		setLoading(true);
		SyncBatchScanApi({
			idList: selectedRowKeys
		}).then(res => {
			queryBatchScanData({ ...lastSearchParams, count: true });
			cb && cb();
		}, () => {
			setTip("");
			setLoading(false);
		}).catch(() => {
			setTip("");
			setLoading(false);
		});
	};

	// 单个删除时
	const onDeleteSingle = (selectedRowKeys, cb) => {
		DelBatchScanApi({
			idList: selectedRowKeys
		}).then(res => {
			const { pageSize, current } = pagination;
			queryBatchScanData({
				...lastSearchParams,
				pageSize,
				pageNo: current,
				count: true
			});
			cb && cb();
		}).finally(() => {
			cb && cb();
		}).catch(() => {
			cb && cb();
		});
	};

	// 编辑完毕时

	const onEdited = () => {
		const { pageSize, current } = pagination;
		queryBatchScanData({
			...lastSearchParams,
			pageSize,
			pageNo: current,
			count: false,
		});
	};

	const onTableChange = (pagination, filters, sorter, { action }) => {
		const { pageSize, current } = pagination;
		console.log(262, pagination, action);
		if (action == 'paginate' && Object.keys(getLastSearchParams())?.length) {
			const params = {
				...lastSearchParams,
				pageSize,
				pageNo: current,
				current,
				count: current === 1,
			};
			queryBatchScanData(params);
			setLastSearchParams(params);
		}
	};

	/** ------------------------------------------- */

	const getPageSize = () => {
		let defaultPageSize = Number(localStorage.getItem('AfterSale_BatchScanTable')) || 10;
		return pagination?.pageSize || defaultPageSize; // 这里应该取上一次请求的分页数据
	};

	const onSearchFormSearch = (params) => {
		console.log('%c [ onSearchFormSearch ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
		
		const { current = 1 } = lastSearchParams;
		const newPageSize = getPageSize();
		const _params = {
			...params,
			current,
			pageSize: newPageSize,
			count: true,
		};
		queryBatchScanData(_params);
		setLastSearchParams(_params);
	};

	const getConfigInfo = () => {
		avoidRepeatReq(TradeDictQueryDictApi, {
			userDictEnum: 'AFTERSALE_SCAN_SET'
		}).then(res => {
			if (res.value) {
				try {
					const value = JSON.parse(res.value);
					setMemoFlagConfig(value?.memoFlagConfig?.split(","));
				} catch (e) {
					console.log(e);
				}
			}
		});
	};

	const initData = async() => {
		const shopList = await userStore.getShopList();
		setShopList(shopList);
	};

	const handleClearMissedList = () => {
		setMissedList([]);
		setMissedType('');
	};

	useEffect(() => {
		userStore.getSystemSetting();
		initData();
		getConfigInfo();
		sendPoint(Pointer["售后_批量扫描登记_展现"]);
	}, []);


	return (
		<Spin spinning={ loading } tip={ tip }>
			<div className={ s["batch-scan-body"] }>
				<div className={ s['scan-form-body'] }>
					<ScanForm onSearch={ scanFormOnSearch } onFinish={ scanFormOnFinish } onBatchScan={ onBatchScan } />
				</div>
				<div>
					<SearchForm onSearch={ onSearchFormSearch } />
				</div>
				<div>
					<BatchScanTable
						dataSource={ dataSource }
						missedList={ missedList }
						missedType={ missedType }
						statistics={ statistics }
						pagination={ pagination }
						onDelete={ onDelete }
						onRefresh={ onRefresh }
						onReceive={ onReceive }
						onChange={ onTableChange }
						onDeleteSingle={ onDeleteSingle }
						onEdited={ onEdited }
						lastSearchParams={ lastSearchParams }
						className="scan-table"
						onClearMissedList={ handleClearMissedList }
					/>
				</div>
			</div>

			{/* 展示收货进度 */}
			{
				progressModalVisible && <ProgressModal percent={ percent } />
			}

			{/* 展示收货结果 */}
			{
				receivedResModaVisible && (
					<ReceivedResModal
						successNum={ receiveSuccessNum }
						failList={ receiveFailList }
						errorExchangeMessages={ errorExchangeMessages }
						onOk={ () => { setReceivedResModaVisible(false); queryBatchScanData({ ...lastSearchParams, count: true }); } }
						onCancel={ () => { setReceivedResModaVisible(false); queryBatchScanData({ ...lastSearchParams, count: true }); } }
					/>
				)
			}

			{/* 展示批量登记进度 */}
			{
				batchScanProgressVisible && <BatchScanProgressModal percent={ percent } visible={ batchScanProgressVisible } />
			}

			{/* 展示批量登记结果 */}
			{
				batchScanResultVisible && (
					<BatchScanResultModal
						visible={ batchScanResultVisible }
						successNum={ batchScanSuccessNum }
						failList={ batchScanFailList }
						onOk={ () => { setBatchScanResultVisible(false); } }
						onCancel={ () => { setBatchScanResultVisible(false); } }
					/>
				)
			}


		</Spin>

	);
};

export default observer(BatchScan);
