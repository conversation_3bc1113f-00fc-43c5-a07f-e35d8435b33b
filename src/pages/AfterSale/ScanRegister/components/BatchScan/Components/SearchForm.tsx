import React, { useMemo, useState } from "react";
import { Button, Form, Input, Select } from "antd";
import dayjs from 'dayjs';
import { getMultiShops, getMultiShopsWithFilter } from "@/components-biz/ShopListSelect/shopListUtils";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import ShopMultiSelect from "@/components-biz/ShopListSelect/shopMultiSelect";
import InputMulti from "@/components/Input/InputMulti";
import FlagAndMemoSelect from "@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagAndMemoSelect";
import { OfflineMemoEnum } from "@/pages/AfterSale/TradeList/constants";
import FlagSelect from "@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagSelect";
import { flagGroup, PLAT_KTT, PLAT_DW, PLAT_YZ } from '@/constants';
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import { DatePickerKey, getCacheDateRange } from "@/components/DateRangeComp/kdzsRangePickerUtil";
import s from '../index.module.scss';

const isMatchOptions = [{
	value: 1,
	label: "已匹配"
}, {
	value: 2,
	label: "未匹配"
}];

const isPendingOptions = [{
	value: 1,
	label: "挂起"
}, {
	value: 0,
	label: "不挂起"
}];

const afterSaleTypeNames = [
	{
		value: 1,
		label: "仅退款"
	},
	{
		value: 2,
		label: "退货退款"
	},
	{
		value: 3,
		label: "换货"
	},
	{
		value: 5,
		label: "补发"
	}
];

const initialRangeTime = getCacheDateRange(DatePickerKey.aftersale_scanRegister) || [dayjs().subtract(30, 'days').startOf('day'), dayjs().endOf('day')];
// 搜索条件初始化
const initialValues = {
	date: initialRangeTime,
	shopInfo: {},
	isMatch: undefined,
	afterSaleType: undefined,
	isPending: undefined,
	flagValue: undefined,
	sellerMemo: undefined,
	sellerFlag: undefined,
	[OfflineMemoEnum.是否有线下备注]: undefined,
	localContent: undefined,
	expressCompany: undefined,
	expressNumber: undefined,
	operatorNick: undefined,
};

const SearchForm = (props) => {
	const [form] = Form.useForm();
	const { onSearch } = props;
	const [searchData, setSearchData] = useState({});


	const _onSearch = () => {
		sendPoint(Pointer["售后_批量扫描登记_查询"]);

		form.validateFields().then(async values => {
			const { plat_sellerIds = [], plats = [] } = values.shopInfo || {};
			let multiShopS = await getMultiShopsWithFilter({ plats, plat_sellerIds, hidePlatforms: [PLAT_KTT, PLAT_DW, PLAT_YZ] });

			let {
				expressNumber,
				expressCompany,
				isMatch,
				operatorNick,
				isPending,
				localContent,
				needFindLocalContent,
				afterSaleType, // 售后类型
			} = values;

			if (!values.date?.length) {
				values.date = initialRangeTime;
			}
			let params = {
				sid: expressNumber || null,
				companyName: expressCompany || null,
				isMatch: isMatch || null,
				afterSaleTypeList: afterSaleType ? [afterSaleType] : [], // 转数组
				isPending,
				multiShopS,
				operatorNick,
				// flagValue,
				localContent,
				needFindLocalContent,
				scanTimeStart: values?.date[0]?.format("YYYY-MM-DD HH:mm:ss"),
				scanTimeEnd: values?.date[1]?.format("YYYY-MM-DD HH:mm:ss"),
			};
			// 留言备注
			let flagValue;
			let flagSelValue;
			let sellerFlag;
			if (values.flagValue?.includes('_')) {
				flagSelValue = values.flagValue;
			} else {
				flagValue = values.flagValue;
			}
			if (values.sellerFlag?.length > 1) {
				let curFlag:any[] = [];
				flagGroup.forEach(item => {
					if (values.sellerFlag.includes(item.toSellerFlag)) {
						curFlag.push(item.value);
					}
				});
				flagSelValue = curFlag.toString() + '_1';
			} else {
				sellerFlag = values?.sellerFlag?.toString();
			}

			flagValue = flagValue === '-1' ? '10' : flagValue;
			params['flagValue'] = flagValue;
			params['flagSelValue'] = flagSelValue;
			params['sellerMemo'] = values.sellerMemo;
			params['sellerFlag'] = sellerFlag;
			if (!plats?.length && !plat_sellerIds?.length) {
				params['shopIsActualSelect'] = 0; // 页面是否实际勾选店铺查询 0否 1是
			} else {
				params['shopIsActualSelect'] = 1;
			}
			onSearch && onSearch(params);
		}).catch(e => {
			console.log(444, e);
		});
	};

	const _onReset = () => {
		form.resetFields();
		setTimeout(() => {
			_onSearch();
		});
	};

	useMemo(async() => {
		_onSearch();
	}, []);

	const handleChange = (e) => {
		setSearchData((prev) => ({ ...prev, ...e }));
	};

	return (
		<div className="print-batch-search-con">
			<Form
				size="small"
				className={ s['search-form-container'] }
				style={ { display: 'flex', flexWrap: 'wrap' } }
				form={ form }
				onFinish={ _onSearch }
				onValuesChange={ handleChange }
				initialValues={ initialValues }
			>
				<Form.Item name="date" style={ { marginRight: '10px' } }>
					<KdzsDateRangePicker1
						datePickerKey={ DatePickerKey.aftersale_scanRegister }
						style={ { width: 160 } }
						cacheQuickChoose
						useServeTime
					/>
				</Form.Item>

				<Form.Item name="shopInfo" style={ { maxWidth: 410 } }>
					<ShopMultiSelect
						isHasHandPlat
						hidePlatforms={ [PLAT_KTT, PLAT_DW, PLAT_YZ] }
						style={ { width: 160 } }
						size="small"
						// style={ { width: 200 } }
					/>
				</Form.Item>

				<Form.Item name="isMatch">
					<Select style={ { width: 160 } } placeholder="扫描结果" allowClear>
						{
							isMatchOptions.map(option => <Select.Option key={ option.value } value={ option.value } label={ option.label }>{option.label}</Select.Option>)
						}
					</Select>
				</Form.Item>

				<Form.Item name="afterSaleType">
					<Select style={ { width: 160 } } placeholder="售后类型" allowClear>
						{
							afterSaleTypeNames.map(option => <Select.Option key={ option.value } value={ option.value } label={ option.label }>{option.label}</Select.Option>)
						}
					</Select>
				</Form.Item>

				<Form.Item name="isPending">
					<Select style={ { width: 160 } } placeholder="是否挂起" allowClear>
						{
							isPendingOptions.map(option => <Select.Option key={ option.value } value={ option.value } label={ option.label }>{option.label}</Select.Option>)
						}
					</Select>
				</Form.Item>
				{
					['-1'].includes(searchData?.flagValue) ? (
						<>
							<Form.Item name="flagValue" >
								<FlagAndMemoSelect className={ s['list-flag-item'] } nodeMessage size="small" />
							</Form.Item>

							{/* 有备注旗帜（可搜） */}
							<Form.Item name="sellerMemo" >
								<InputMulti
									placeholder="备注内容"
									maxInputNum={ 50 }
									maxInputLength={ 1000 }
									lengthErrorMsg="备注最多输入1000个字数，请重新输入"
									numErrorMsg="单次查询最多筛选50个备注，请重新输入"
									style={ { width: '160px' } }
									size="small"
								/>
							</Form.Item>
							<Form.Item name="sellerFlag" style={ { width: '160px' } } >
								<FlagSelect
									placeholder="旗帜"
									style={ { width: '160px' } }
									size="small"
									allowClear
									showArrow
									showSearch={ false }
								/>
							</Form.Item>
						</>
					) : (
						<Form.Item name="flagValue" >
							<FlagAndMemoSelect className={ s['list-flag-item'] } nodeMessage size="small" />
						</Form.Item>
					)
				}
				<Form.Item name={ OfflineMemoEnum.是否有线下备注 } style={ { width: '160px' } }>
					<Select allowClear placeholder="线下备注" style={ { width: '160px' } }>
						<Select.Option value="0">无线下备注</Select.Option>
						<Select.Option value="1">有线下备注</Select.Option>
					</Select>
				</Form.Item>
				{
					searchData[OfflineMemoEnum.是否有线下备注] == 1 ? (
						<Form.Item name="localContent" >
							<Input placeholder="请输入线下备注" style={ { width: 160 } } />
						</Form.Item>
					) : null
				}
				<Form.Item name="expressCompany">
					<Input placeholder="快递公司，模糊查询" style={ { width: 160 } } allowClear />
					{/* <Select placeholder="请选择快递公司">
						<Select.Option value="" label="">快递公司</Select.Option>

						{expressCompanyList.map(company => {
							return <Select.Option key={ company.exName } value={ company.exName }>{company.exName}</Select.Option>;
						})}
					</Select> */}
				</Form.Item>

				<Form.Item name="expressNumber">
					<InputMulti size="small" style={ { width: 160 } } placeholder="快递单号" maxInputNum={ 1000 } className="high-placeholder" />
				</Form.Item>

				<Form.Item name="operatorNick">
					<Input
						style={ { width: 160 } }
						placeholder="操作人"
						allowClear
					/>
				</Form.Item>

				<div>
					<Button type="primary" htmlType="submit" style={ { marginBottom: '16px' } }>查询</Button>
					<Button className="r-ml-10" onClick={ _onReset } style={ { marginBottom: '16px' } }>重置</Button>
				</div>
			</Form>
		</div>
	);

};

export default SearchForm;
