import React, { useCallback, useEffect, useRef, useState } from "react";
import { Button, Form, Input, Select, Badge, Modal, Row } from "antd";
import { cloneDeep, debounce } from "lodash";
import { ExclamationCircleOutlined, FormOutlined } from "@ant-design/icons";
import s from "../index.module.scss";
import { GetLogisticsCompanyInfoByYdNoApi } from "@/apis/aftersale/scanRegister";
import useGetState from "@/utils/hooks/useGetState";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import memoFn from "@/libs/memorizeFn";
import Icon from "@/components/Icon";
import Code from "@/assets/image/wxApp/code.png";
import message from '@/components/message';

let timeoutId = null;
const ScanForm = (props) => {
	const { onSearch, onFinish, onBatchScan } = props;
	const [scanForm] = Form.useForm();
	const [querying, setQuerying] = useState(false);
	const [visible, setVisible] = useState(false);
	const [addValue, setAddValue] = useState('');
	const searchInput = useRef(null);
	const [expressCompanyList, setExpressCompanyList, _getExpressCompanyList] = useGetState([]);
	
	// 通过快递单号获取快递公司信息
	const getCompanyInfoByExpressNumber = (exNo) => {
		// 根据输入的快递单号查询快递公司信息
		GetLogisticsCompanyInfoByYdNoApi({ ydNoList: [exNo.trim()] }).then(res => {
			const companyInfo = res?.data?.logisticsCompanyInfos?.[0];
			// 判断获取到的快递公司是否有效
			const isValid = companyInfo && companyInfo.cpCode && !["unknown"].includes(companyInfo.cpCode);
			if (isValid) {
				// 判断快递公司是否存在已有的列表中，假如不存在，就添加到列表中去，避免下拉框出现只展示code的情况
				const hasExist = _getExpressCompanyList().find(express => express.exCode === companyInfo.cpCode);
				if (!hasExist) {
					const _expressCompanyList = cloneDeep(_getExpressCompanyList());
					_expressCompanyList.unshift({
						exName: companyInfo.companyName,
						exCode: companyInfo.cpCode,
					});
					setExpressCompanyList(_expressCompanyList);
				}
				scanForm.setFieldsValue({
					expressCompany: companyInfo.cpCode
				});
			}
		}).finally(() => { setQuerying(false); _onFinish(); }).catch(() => setQuerying(false));
	};

	// eslint-disable-next-line react-hooks/exhaustive-deps
	const onScanExpress = useCallback(debounce(() => {
		sendPoint(Pointer["售后_批量扫描登记_扫描"]);
		scanForm.validateFields().then(values => {
			setQuerying(true);
			getCompanyInfoByExpressNumber(values.expressNumber);
		});
	}, 500, { leading: true, trailing: false }), []);

	// 获取快递公司列表
	const getExpressCompanyList = () => {
		memoFn.getExpressList().then(res => {
			if (res && res.length > 0) {
				setExpressCompanyList(res);
			}
		});
	};

	const getExCompanyInfoByExCode = (exCode) => {
		const info = _getExpressCompanyList().find(express => express.exCode == exCode);
		return info;
	};

	// 快递公司匹配结束之后

	const _onFinish = () => {
		setTimeout(() => {
			const { expressCompany, expressNumber } = scanForm.getFieldsValue();
			const exCompanyInfo = getExCompanyInfoByExCode(expressCompany) || {};
			onFinish && onFinish({
				expressNumber,
				expressCompanyInfo: exCompanyInfo
			});
			scanForm.resetFields();
		}, 10);
		setTimeout(() => {
			searchInput?.current?.focus();
		}, 200);
	};

	useEffect(() => {
		if (querying) { // false的时候并不需要关闭loading，其他接口会关闭
			onSearch && onSearch(querying);
		}
	}, [querying]);

	useEffect(() => {
		getExpressCompanyList();
		setTimeout(() => {
			searchInput?.current?.focus();
		}, 200);
	}, []);

	const handleMouseEnter = () => {
		// 设置定时器
		handleMouseLeave();
		timeoutId = setTimeout(() => {
			sendPoint(Pointer.售后_批量扫描登记_手机批量登记);
		}, 500); // 500 毫秒后执行打点
	};
	
	const handleMouseLeave = () => {
		// 清除定时器
		if (timeoutId) {
			clearTimeout(timeoutId);
			timeoutId = null;
		}
	};

	const handleOk = (e) => {
		const validValue = addValue.trim();
		const arr = validValue.split('\n')?.map(i => i.trim());
		if (arr.length === 0) {
			message.error('请输入快递单号');
			return;
		} else if (arr.length > 1000) {
			message.error(`当前输入${arr.length}条，最大添加数量1000条`);
			return;
		} else if (arr.length === 1) {
			scanForm.setFieldsValue({
				expressNumber: arr[0]
			});
			onScanExpress();
		} else {
			onBatchScan && onBatchScan([...new Set(arr)]);
		}
		setAddValue('');
		setVisible(false);
	};

	return (
		<div>
			<div className="r-fs-16 r-flex r-jc-sb r-ai-c r-pb-16 r-mb-16" style={ { borderBottom: "1px solid #eee" } }>
				<div className="r-pl-8 r-bold" style={ { borderLeft: "2px solid #FD8204", lineHeight: "16px" } }>请在此<span className="r-bold" style={ { color: "#f00" } }>扫描退货快递</span>进行登记</div>
				<div>
					<Badge size="small" count="new" offset={ [-10, -2] } style={ { boxShadow: "none", height: 16, padding: "1px 5px", borderRadius: 8, zIndex: 99 } }>
						<div 
							className={ s.wxAppContent }
							onMouseEnter={ handleMouseEnter } // 鼠标进入时设置定时器
							onMouseLeave={ handleMouseLeave }
						>
							<div className={ s.wxAppMain }>
								<Icon
									style={ { fontSize: 18, color: "#07C160" } }
									type="weixinxiaochengxu"
								/>
								手机批量登记
							</div>

							<div className={ s.wxAppCode }>
								<div className={ s.codeMain }>
									<div className={ s.codeTitle }>快递助手ERP小程序</div>
									<img src={ Code } alt="" className={ s.code } />
									<div className={ s.codeBottom }>
										手机批量登记，方便快捷
									</div>
									<div className={ s.codeTip }>
										（子账号需要开通权限）
									</div>
								</div>
							</div>
						</div>
					</Badge>
				</div>
			</div>
			<div className="r-flex r-ai-c r-mt-8">
				<Form
					form={ scanForm }
					className={ s['scan-form-container'] }
				>
					<Form.Item
						label="快递公司"
						name="expressCompany"
					>
						<Select placeholder="请选择快递公司" style={ { width: 150 } }>
							{expressCompanyList.map(company => {
								return <Select.Option key={ company.exCode } value={ company.exCode }>{company.exName}</Select.Option>;
							})}
						</Select>
					</Form.Item>
					<div className="r-relative r-mr-16">
						<Form.Item
							label="快递单号"
							name="expressNumber"
							rules={ [{
								required: true,
								message: '请输入快递单号'
							}] }
						>
							<Input
								ref={ searchInput } 
								style={ { width: 300 } }
								placeholder="使用扫描枪扫描前请切换为英文输入法"
								onPressEnter={ querying ? () => null : onScanExpress }
								// allowClear
							/>
						</Form.Item>
						<FormOutlined className="r-absolute r-c-666" style={ { right: 10, top: 10, cursor: 'pointer', zIndex: 99 } } onClick={ () => setVisible(true) } />
					</div>
					<Button type="primary" onClick={ querying ? () => null : onScanExpress }>登记快递</Button>
				</Form>
				
				{/* <div className="r-mb-16 r-c-666 r-ml-16">
					<ExclamationCircleOutlined />
					<span className="r-ml-6">支持使用扫描枪扫码，扫描前请将输入法换成英文模式。</span>
				</div> */}
			
			</div>
			{
				visible && (
					<Modal
						centered
						visible={ visible }
						width={ 600 }
						maskClosable={ false }
						bodyStyle={ { padding: "10px 24px", height: 314 } }
						onOk={ handleOk }
						onCancel={ () => setVisible(false) }
						okButtonProps={ { size: 'middle' } }
						cancelButtonProps={ { size: 'middle' } }
						title={ (<>批量添加<span className="k-c-text r-fs-14 r-ml-8">批量添加（换行输入）或从Excel中粘贴复制</span></>) }
					>
						<Row className="r-c-333 r-mb-16 r-fs-16">
							当前最大添加数量1000条
						</Row>
						<Row>
							<Input.TextArea style={ { height: 194 } } value={ addValue } onChange={ (e) => setAddValue(e.target.value) } />
						</Row>
					</Modal>
				)
			}
		</div>
		
	);
};

export default ScanForm;