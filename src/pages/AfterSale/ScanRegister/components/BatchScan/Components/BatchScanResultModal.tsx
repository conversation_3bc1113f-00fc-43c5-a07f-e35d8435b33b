import React from "react";
import { But<PERSON>, Table } from "antd";
import Modal from "antd/lib/modal/Modal";
import { CheckCircleTwoTone, CloseCircleTwoTone, CopyOutlined } from "@ant-design/icons";
import { copyToPaste, splitFxgTid } from '@/utils';

// 批量扫描结果项接口
interface BatchScanResultItem {
	sid: string;
	errorInfo: string;
}

// 批量扫描结果组件属性接口
interface BatchScanResultModalProps {
	visible: boolean;
	successNum: number;
	failList: BatchScanResultItem[];
	onOk?: () => void;
	onCancel?: () => void;
}

const BatchScanResultModal: React.FC<BatchScanResultModalProps> = ({
	visible,
	successNum,
	failList,
	onOk,
	onCancel
}) => {
	const columns = [
		{
			title: "序号",
			dataIndex: "index",
			width: 50,
			render(_, record, index) { 
				return <span>{index + 1}</span>; 
			}
		},
		{
			title: "快递单号",
			dataIndex: "sid",
			render(_, record) {
				return (
					<div>
						<span>{record.sid}</span>
						<CopyOutlined
							onClick={ () => { copyToPaste(record.sid); } }
							className="r-fc-black-65 r-pointer r-ml-6"
						/>
					</div>
				);
			}
		},
		{
			title: "失败原因",
			dataIndex: "errorInfo"
		}
	];

	const copyAllSids = () => {
		const allSids = failList.map(i => i.sid);
		copyToPaste(splitFxgTid([...new Set(allSids)].join('\n')), "复制成功", true);
	};

	const handleOk = () => {
		onOk && onOk();
	};

	const handleCancel = () => {
		onCancel && onCancel();
	};

	const footer = (
		<div className="r-ta-c">
			<Button type="primary" onClick={ handleOk }>
				我知道了
			</Button>
		</div>
	);

	return (
		<Modal
			title="批量扫描结果"
			visible={ visible }
			centered
			onCancel={ handleCancel }
			onOk={ handleOk }
			footer={ footer }
		>
			<div>
				<div className="r-flex r-ai-c r-jc-c r-fs-20">
					<div>
						<CheckCircleTwoTone twoToneColor="#52c41a" />
						<span className="r-ml-4 r-bold" style={ { color: "#52c41a" } }>
							成功：
						</span>
						<span className="r-bold" style={ { color: "#000" } }>
							{successNum}单
						</span>
					</div>
					<div className="r-ml-30">
						<CloseCircleTwoTone twoToneColor="#f00" />
						<span className="r-ml-4 r-bold" style={ { color: "#f00" } }>
							失败：
						</span>
						<span className="r-bold" style={ { color: "#000" } }>
							{failList.length}单
						</span>
					</div>
				</div>

				{failList.length > 0 && (
					<div className="r-mt-14">
						<div>扫描登记失败列表：</div>
						<Table
							className="r-mt-8"
							size="small"
							scroll={ { y: 300 } }
							pagination={ false }
							bordered
							dataSource={ failList }
							columns={ columns }
							rowKey="sid"
						/>
						<div className="kdzs-link-text r-mt-10" onClick={ copyAllSids }>
							复制失败的快递单号
						</div>
					</div>
				)}
			</div>
		</Modal>
	);
};

export default BatchScanResultModal; 