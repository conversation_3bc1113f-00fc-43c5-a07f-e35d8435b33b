import React from "react";
import { Progress } from "antd";
import Modal from "antd/lib/modal/Modal";

interface BatchScanProgressModalProps {
	percent: number;
	visible: boolean;
}

const BatchScanProgressModal: React.FC<BatchScanProgressModalProps> = ({ percent, visible }) => {
	return (
		<Modal
			centered
			visible={ visible }
			closable={ false }
			title="批量扫描进度"
			footer={ null }
		>
			<div className="r-flex r-ai-c r-fd-c">
				<Progress
					type="circle"
					percent={ percent }
					strokeColor={ { '0%': '#108ee9', '100%': '#87d068' } }
				/>
				<div className="text-loading r-bold r-mt-10" style={ { color: "rgba(0,0,0,0.5)" } }>
					正在批量扫描登记
				</div>
				<div className="r-bold r-mt-10" style={ { color: "rgba(0,0,0,0.5)" } }>
					（关闭页面会导致任务中断！）
				</div>
			</div>
		</Modal>
	);
};

export default BatchScanProgressModal; 