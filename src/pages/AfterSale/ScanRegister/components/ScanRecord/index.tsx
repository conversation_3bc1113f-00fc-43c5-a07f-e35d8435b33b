/* 库存看板
 * @Author: sww
 * @Date: 2021-12-13 14:20:35
 * @Last Modified by: sww
 * @Last Modified time: 2022-01-19 15:42:54
 */
import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import cs from 'classnames';
import { Form, Button, Input, Popover, message, Radio, Select, Checkbox, Dropdown, Menu, Image } from 'antd';
import { SettingOutlined, SwapOutlined, CopyOutlined } from '@ant-design/icons';
import { observer } from "mobx-react";
import dayjs from 'dayjs';
import _ from 'lodash';
// import SearchTable from '@/components/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { clearParams } from '@/utils/stringHelper';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import { DatePickerKey } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import InputMulti from '@/components/Input/InputMulti';
import EnumSelect, { EnumStringSelect } from '@/components/Select/EnumSelect';
import { AFTER_SALE_STATUS_OPTIONS, AFTER_SALE_STATUS_TYPE, DEFAULT_PRODUCT_CONTENT_SETTING, GOOD_STATUS_OPTIONS, GOOD_STATUS_TYPE, PRODUCT_CONTENT_ENUM, PRODUCT_SETTING_OBJECT, TIME_TYPE_OPTIONS,
	supplierOptionsEnum, OperatorFromObj, RefundScanQueryTypeEnum, refundScanQueryTypeObj
} from './constants';
import { GetRefundExportConfigApi, QueryRefundScanRecordListApi } from '@/apis/aftersale/scanRegister';
import { getMultiShops, getMultiShopsWithFilter, isSourceScm, isAfterSaleSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import { QueryRefundScanRecordListRequest } from '@/types/schemas/aftersale/scanRegister';
import { downloadCenter } from '@/pages/Index/DownloadCenter/utils';
import { ModulesFunctionEnum } from '@/types/schemas/setting/download';
import userStore from '@/stores/user';
import { GetRefundGlobalConfigListApi, ResetRefundExportConfigApi, UpdateRefundExportConfigApi, UpdateRefundGlobalConfigApi, BatchUpdateLocalNoteApi } from '@/apis/aftersale/trade';
import { AftersaleGlobalConfig, OfflineMemoEnum, UpdateTypeEnum, listItemName } from '@/pages/AfterSale/TradeList/constants';
import BatchExportModal, { BatchExportMethod, IBatchExportModal } from '@/components-biz/BatchExportModal';
import GoodsContentSetting from '../ReturnLableRecord/components/Table/GoodsContentSetting';
import { renderGoodsInfo } from '../ReturnLableRecord/components/Table';
import InputArrayMulti from '@/components/Input/InputArrayMulti';
import { getGoodsInfoSelectOptions, getPlaceholderTips, handleGoodsInfoSearchParams, itemInfoEnumValue } from '@/pages/AfterSale/TradeList/itemInfoUtils';
import { SearchTableProps } from '@/components/SearchTable/SearchTable';
import SearchTable from '@/components/SearchTableVirtual';
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import { sorterFun } from '@/utils/util';
import { copyToPaste } from "@/utils";
import { getTradeFlag, getTradeFlagTag } from '@/pages/Trade/utils';
import Icon from "@/components/Icon";
import BatchModifyMemoModal from "@/pages/Trade/components/BatchModifyMemoModal";
import OfflineMemoModal, {
	EnumFromPage,
	IBatchOfflineMemoModalProps
} from "@/components-biz/OfflineMemoModal";
import { tradeStore } from "@/stores";
import { local } from "@/libs/db";
import { flagGroup, PLAT_KTT, PLAT_DW, PLAT_YZ } from "@/constants";
import { getPlatformTradeLink } from "@/pages/AfterSale/TradeList/utils";
import FlagAndMemoSelect from "@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagAndMemoSelect";
import FlagSelect from '@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagSelect';
import BatchModifyLocalMemoPicModal from '@/pages/AfterSale/TradeList/components/BatchModifyLocalMemoPicModal';
import s from './index.module.scss';
import { AFTERSALE_REQUEST_SOURCE } from '@/pages/AfterSale/constants';
import { getImageThumbnail } from '@/utils/img.scale';


const defaultParams = {
	timeType: 1,
	goodsIncludeStatus: '1',
	supplierIsIncluding: 'supplierContain', // 供应商包含
};

const { Option } = Select;

enum BatchExportType {
	快递单维度 = 2,
	商品维度 = 3
}

const searchInitValue: Partial<any> = {
	goodsIncludeStatus: '1',
};
const ScanRecord: React.FC<{}> = props => {
	const [form] = Form.useForm();
	const tableRef = useRef<any>();
	const batchModifyLocalMemoPicModalRef = useRef(null);
	const [loading, setLoading] = useState(false);
	const [isShowShopName, setIsShowShopName] = useState(false);
	const [queryParams, setQueryParams] = useState({});
	const [response, setResponse] = useState({});
	const [initList, setInitList] = useState([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [exportType, setExportType] = useState(BatchExportType.快递单维度);
	const [batchExportModalVisible, setBatchExportModalVisible] = useState(false);
	const [batchExportParams, setBatchExportParams] = useState<IBatchExportModal["exportParams"]>({});
	const [productContentList, setProductContentList] = useState(DEFAULT_PRODUCT_CONTENT_SETTING);
	const [sortOrder, setSortOrder] = useState(''); // 默认排序方式 desc 降序  asc 升序
	const [sortKey, setSortKey] = useState(''); // 默认排序方式 desc 降序  asc 升序
	const [dataSource, setDataSource] = useState([]);
	const [dataOrigin, setDataOrigin] = useState([]);
	const [selectedRows, setSelectedRows] = useState([]);

	const {
		setModifyMemoPackage,
		setIsShowBatchModifyMemoModal,
		isShowBatchModifyMemoModal,
		modifyMemoPackage,
	} = tradeStore;
	const [offlineMemoData, setOfflineMemoData] = useState<
		IBatchOfflineMemoModalProps["data"]
	>({ visible: false });

	const { isShowZeroStockVersion } = userStore;
	const [formData, setFormData] = useState<any>({ ...searchInitValue });

	const [operateBtnsDefault, setOperateBtnsDefault] = useState({
		batchMemoBtn: "batchMemo",
	});

	useEffect(() => {
		getColumnList();
		const defaultOperateBtns = local.get("scanRecordOperateBtnsDefault") || {};
		setOperateBtnsDefault((prev => {
			return {
				...prev,
				...defaultOperateBtns,
			};
		}));
	}, []);

	const verifySearchParams = (params: Partial<SearchTableProps>): boolean => {
		let goodsInfoIncludeNum = params.goodsIncludeStatus == itemInfoEnumValue.商品包含 ? [200, 500] : [50, 50];
		let goodsInfoNotIncludeNum = params.goodsNotIncludeStatus == itemInfoEnumValue.商品不包含 ? [200, 500] : [50, 50];
		const placeholderTips = getPlaceholderTips();
		if (params.shortNameIncludingList?.length > goodsInfoIncludeNum?.[0]) {
			message.error(`同时查询 ${placeholderTips[params.goodsIncludeStatus]?.[0]} 的数量不能超过${goodsInfoIncludeNum?.[0]}个`);
			return false;
		}
		if (params.skuIncludingList?.length > goodsInfoIncludeNum?.[1]) {
			message.error(`同时查询 ${placeholderTips[params.goodsIncludeStatus]?.[1]} 的数量不能超过${goodsInfoIncludeNum?.[1]}个`);
			return false;
		}
		if (params.shortNameNotIncludingList?.length > goodsInfoNotIncludeNum?.[0]) {
			message.error(`同时查询 ${placeholderTips[params.goodsNotIncludeStatus]?.[0]} 的数量不能超过${goodsInfoNotIncludeNum?.[0]}个`);
			return false;
		}
		if (params.skuNotIncludingList?.length > goodsInfoNotIncludeNum?.[1]) {
			message.error(`同时查询 ${placeholderTips[params.goodsNotIncludeStatus]?.[1]} 的数量不能超过${goodsInfoNotIncludeNum?.[1]}个`);
			return false;
		}
		return true;
	};

	const getColumnList = async() => {
		try {
			let res = await GetRefundGlobalConfigListApi({ bizEnumList:
				[
					AftersaleGlobalConfig.售后扫描记录列配置,
					AftersaleGlobalConfig.售后扫描记录展示字段自定义设置
				]
			});
			console.log(11, res);
			res.forEach(config => {
				 if (config.biz === AftersaleGlobalConfig.售后扫描记录列配置) {
					if (config.value) {
						try {
							let cValue = JSON.parse(config.value);
							console.log('%c [ 售后扫描记录列配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', cValue);
							if (Array.isArray(cValue)) {
								setInitList(cValue);
							}
						} catch (error) {
							console.log(error);
						}
					}
				} else if (config.biz === AftersaleGlobalConfig.售后扫描记录展示字段自定义设置) {
					if (config.value) {
						console.log("售后扫描记录展示字段自定义设置::", config.value);
						try {
							let cValue = JSON.parse(config.value);
							if (Array.isArray(cValue)) {
								setProductContentList(cValue);
							}
						} catch (error) {
							console.log(error);
						}
					}
				}
			});
		} catch (error) {
			console.log(error);
		}
	};

	const onFieldsChange = (changedValues, allValues) => {
		// 如果隐藏未收货记录勾选了，需要重新查询
		if ('isFilterNoConfirmRecord' in changedValues) {
			tableRef?.current?.refresh();
		}

		setFormData(pre => ({ ...allValues, goodsIncludeStatus: allValues.goodsIncludeStatus == undefined ? pre.goodsIncludeStatus : allValues.goodsIncludeStatus }));
	};
	const itemInfoQueryIncludeList = useMemo(() => {
		return getGoodsInfoSelectOptions(userStore.userInfo?.version, true);
	}, [userStore.userInfo?.version]);



	const itemInfoQueryIncludePlaceholder = useMemo(() => {
		return itemInfoQueryIncludeList.filter(i => i.value == formData.goodsIncludeStatus)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名'];
	}, [itemInfoQueryIncludeList, formData.goodsIncludeStatus]);

	const itemInfoQueryIncludeMaxNum = useMemo(() => {
		if (!('goodsIncludeStatus' in formData) || formData.goodsIncludeStatus == itemInfoEnumValue.商品包含) {
			return [200, 500];
		}
		return [50, 50];
	}, [formData.goodsIncludeStatus]);



	const FormFieldListV1: FormItemConfig[] = [
		{
			name: 'timeType',
			label: "",
			children: <EnumSelect enum={ TIME_TYPE_OPTIONS } style={ { width: 170 } } size="small" allowClear={ false } />
		},
		{
			name: 'date',
			initialValue: [dayjs().subtract(1, 'days').startOf('day'), dayjs().endOf('day')],
			label: '',
			children: <KdzsDateRangePicker1 datePickerKey={ DatePickerKey.afterSale_scan_record } cacheQuickChoose style={ { width: '169px' } } size="small" />
		},
		{
			name: 'multiShops',
			label: '',
			children: <ShopMultiSelect style={ { width: '169px' } } size="small" isHasHandPlat hidePlatforms={ [PLAT_KTT, PLAT_DW, PLAT_YZ] } />
		},
		{
			name: 'sid',
			label: '',
			children: <InputMulti size="small" placeholder="快递单号" maxInputNum={ 1000 } style={ { width: 170 } } className="high-placeholder" />
		},
		{
			name: 'companyName',
			label: '',
			children: <Input size="small" placeholder="快递公司" style={ { width: 170 } } />
		},
		{
			name: 'refundIds',
			label: '',
			children: <InputMulti size="small" placeholder="售后单号" maxInputNum={ 1000 } style={ { width: 170 } } className="high-placeholder" />
		},
		{
			name: 'afterSaleType',
			label: '',
			children: <EnumSelect placeholder="售后类型" enum={ AFTER_SALE_STATUS_OPTIONS } style={ { width: 170 } } size="small" />
		},
		{
			name: 'ptTids',
			label: '',
			children: <InputMulti size="small" placeholder="订单编号" maxInputNum={ 1000 } style={ { width: 170 } } />
		},
		{
			name: 'isNoTradeMess',
			label: '',
			children: (
				<Select size="small" style={ { width: 170 } } allowClear placeholder="是否无主件">
					<Option value={ 1 }>是</Option>
					<Option value={ 2 }>否</Option>
				</Select>
			)
		},
		{
			name: 'sysItemInclude',
			label: "",
			children: (
				<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
					<Form.Item noStyle name="goodsIncludeStatus" >
						<Select
							size="small"
							style={ { flex: 0.6, width: '124px' } }
							onChange={ () => { } }
							options={ itemInfoQueryIncludeList }
							dropdownMatchSelectWidth={ false }
						/>
					</Form.Item>
					{
						itemInfoQueryIncludePlaceholder?.[0] ? (
							<Form.Item noStyle name="shortNameIncludingList">
								<InputArrayMulti
									size="small"
									placeholder={ itemInfoQueryIncludePlaceholder[0] }
									maxInputNum={ itemInfoQueryIncludeMaxNum?.[0] }
									maxTagCount={ 1 }
									maxTagTextLength={ 8 }
									open={ false }
									tokenSeparators={ null }
									style={ { flex: 1, width: 212 } }
								/>
							</Form.Item>
						) : ''
					}
					{
						itemInfoQueryIncludePlaceholder?.[1] ? (
							<Form.Item noStyle name="skuIncludingList" >
								<InputArrayMulti
									size="small"
									placeholder={ itemInfoQueryIncludePlaceholder[1] }
									maxInputNum={ itemInfoQueryIncludeMaxNum?.[1] }
									maxTagCount={ 1 }
									maxTagTextLength={ 8 }
									open={ false }
									tokenSeparators={ null }
									style={ { flex: 1, width: 192 } }
								/>
							</Form.Item>
						) : ''
					}
				</Input.Group>
			),
		},
		{
			name: 'supplierContain',
			label: '',
			children: (
				<Input.Group compact style={ { display: "flex", alignItems: "center", width: 526 } }>
					<Form.Item noStyle name="supplierIsIncluding">
						<EnumStringSelect
							enum={ supplierOptionsEnum }
							style={ { width: '124px', flexShrink: 0 } }
							size="small"
							allowClear={ false }
						/>
					</Form.Item>
					<Form.Item noStyle name="supplierIncludingList">
						<InputArrayMulti
							size="small"
							placeholder="供应商"
							maxInputNum={ 500 }
							open={ false }
							enterSearch={ false }
							tokenSeparators={ null }
							style={ { flex: 1 } }
							maxTagCount="responsive"
						/>
					</Form.Item>
					<Form.Item noStyle name="marketIncludingList">
						<InputArrayMulti
							size="small"
							placeholder="市场"
							maxInputNum={ 500 }
							open={ false }
							enterSearch={ false }
							tokenSeparators={ null }
							style={ { flex: 1 } }
							maxTagCount="responsive"
						/>
					</Form.Item>
					<Form.Item noStyle name="stallIncludingList">
						<InputArrayMulti
							size="small"
							placeholder="档口"
							maxInputNum={ 500 }
							open={ false }
							enterSearch={ false }
							tokenSeparators={ null }
							style={ { flex: 1 } }
							maxTagCount="responsive"
						/>
					</Form.Item>
				</Input.Group>
			)
		},
		{
			name: 'confirmStatus',
			label: '',
			children: <EnumSelect placeholder="收货状态" enum={ GOOD_STATUS_OPTIONS } style={ { width: 170 } } size="small" />
		},
		{
			name: 'buyerNick',
			label: '',
			children: <InputMulti size="small" placeholder="买家昵称" maxInputNum={ 50 } style={ { width: 170 } } />
		},
		{
			name: 'receiverName',
			label: '',
			children: <InputMulti size="small" placeholder="收件人" maxInputNum={ 50 } style={ { width: 170 } } />
		},
		{
			name: 'receiverMobile',
			label: '',
			children: <InputMulti size="small" placeholder="手机号" maxInputNum={ 50 } style={ { width: 170 } } />
		},
		{
			name: "",
			label: "",
			children: (
				<>
					<Form.Item noStyle name="flagValue">
						<FlagAndMemoSelect size="small" style={ { width: 170 } } nodeMessage />
					</Form.Item>
					{
						formData.flagValue == "-1" && (
							<>
								<Form.Item noStyle name="sellerMemo">
									<InputMulti
										placeholder="备注内容"
										maxInputNum={ 50 }
										maxInputLength={ 1000 }
										lengthErrorMsg="备注最多输入1000个字数，请重新输入"
										numErrorMsg="单次查询最多筛选50个备注，请重新输入"
										className="r-ml-8"
										style={ { width: '170px' } }
										size="small"
									/>
								</Form.Item>
								<Form.Item noStyle name="sellerFlag">
									<FlagSelect
										placeholder="旗帜"
										className="r-ml-8"
										style={ { width: '170px' } }
										size="small"
									/>
								</Form.Item>
							</>
						)
					}
				</>
			)
		},
		{
			name: "",
			label: "",
			children: (
				<>
					<Form.Item
						noStyle
						name={ OfflineMemoEnum.是否有线下备注 }

					>
						<Select size="small" style={ { width: 170 } } allowClear placeholder="线下备注">
							<Option value="0">无线下备注</Option>
							<Option value="1">有线下备注</Option>
						</Select>
					</Form.Item>
					{
						formData[OfflineMemoEnum.是否有线下备注] == "1" && (
							<Form.Item
								noStyle
								name={ OfflineMemoEnum.线下备注内容 }
							>
								<Input size="small" className="r-ml-8" style={ { width: 170, verticalAlign: 'top' } } placeholder="请输入线下备注" />
							</Form.Item>
						)
					}
				</>
			),
		},
		{
			name: 'operatorNick',
			label: '',
			children: <Input size="small" placeholder="操作人" style={ { width: 170 } } />
		}
	];

	const shopInfoColHeader = (
		<div onClick={ () => setIsShowShopName(!isShowShopName) }>
			<span className="r-fw-500">
				{isShowShopName ? "店铺名称" : "店铺简称"}
				<Popover content={ `点击切换${isShowShopName ? "店铺简称" : "店铺名称"}` }>
					<SwapOutlined className="r-pointer r-ml-4" />
				</Popover>
			</span>

		</div>
	);

	const GoodsContentSettingOnOk = (val) => {
		UpdateRefundGlobalConfigApi([{
			biz: AftersaleGlobalConfig.售后扫描记录展示字段自定义设置,
			value: JSON.stringify(val)
		}]);
		setProductContentList(val);
	};

	const renderGoodsContentColHeader = (
		<div className="r-flex r-ai-c">
			<span className="r-bold">处理商品信息</span>
			<span className="r-pointer r-ml-4">
				<GoodsContentSetting
					onOk={ GoodsContentSettingOnOk }
					storageKey="afterSaleScanRegisterProductSetting"
					productSetting={ PRODUCT_SETTING_OBJECT }
					productContentEnum={ PRODUCT_CONTENT_ENUM }
					productContentSetting={ productContentList }
					title="商品信息设置"
				>
					<SettingOutlined />
				</GoodsContentSetting>
			</span>
		</div>
	);

	// 点击排序  默认按扫描时间倒序排列
	const handleSorter = (key:string) => {
		let newSortOrder = '';
		let sortedData = [];

		if (sortOrder == 'asc') {
			newSortOrder = 'desc';
		} else if (sortOrder == 'desc') {
			newSortOrder = '';
		} else {
			newSortOrder = 'asc';
		}

		let sortFun = (a, b) => {
			if (newSortOrder === 'asc') {
				return a[key] > b[key] ? 1 : -1;
			} else if (newSortOrder === 'desc') {
				return a[key] < b[key] ? 1 : -1;
			}
		};

		setSortOrder(newSortOrder);
		setSortKey(key);

		if (newSortOrder === 'asc') {
			sortFun = sorterFun(key, false, true);
			sortedData = [...dataSource].sort(sortFun);
		} else if (newSortOrder === 'desc') {
			sortFun = sorterFun(key, false, false);
			sortedData = [...dataSource].sort(sortFun);
		} else {
			sortedData = [...dataOrigin];
		}

		setDataSource(sortedData);
		setSortOrder(newSortOrder);
		setSortKey(key);
	};

	// 初始化排序
	const handleSortData = (data) => {
		if (!sortKey || !sortOrder) {
			return data;
		}
		let sortFun = null;
		let sortedData = [...data];
		if (sortOrder === 'asc') {
			sortFun = sorterFun(sortKey, false, true);
			sortedData = data.sort(sortFun);
		} else if (sortOrder === 'desc') {
			sortFun = sorterFun(sortKey, false, false);
			sortedData = data.sort(sortFun);
		}

		return sortedData;
	};

	// 排序表头
	const getSorterTitle = (title, key) => {
		return (
			<div onClick={ () => handleSorter(key) }>
				<span className="r-bold">
					{title}
					<Popover content="支持当前页排序">
						<SwapOutlined className="r-pointer r-ml-4" style={ { transform: 'rotate(90deg)', color: sortOrder && sortKey == key ? '#FD8204' : '' } } />
					</Popover>
				</span>

			</div>
		);
	};

	// 表格列定义
	const columns = [
		{
			width: 40,
			title: '序号',
			dataIndex: 'index',
			key: 'index',
			align: 'center',
			render: (value, row, index) => {
				return (<span>{index + 1}</span>);
			}
		},
		{
			title: getSorterTitle('扫描时间', 'refundScanTime'),
			sortSet: { name: '扫描时间' },
			width: 140,
			dataIndex: 'refundScanTime',
			render: (value) => {
				if (value) {
					return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
				}
				return "";
			}
		},
		{
			title: getSorterTitle('收货时间', 'refundConfirmTime'),
			sortSet: { name: '收货时间' },
			width: 140,
			dataIndex: 'refundConfirmTime',
			render: (value) => {
				if (value) {
					return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
				}
				return "";
			}
		},
		{
			dataIndex: "sid",
			title: "快递信息",
			width: 120,
			minWidth: 120,
			render: (value, record, index) => {
				const { companyName, sid } = record;
				return (
					<div>
						<div className="r-fs-12 r-ta-l">{companyName}</div>
						<div className="r-flex r-word-break">
							<span className="kdzs-link-text">{sid}
								<span hidden={ !sid }>
									<CopyOutlined
										onClick={ (e) => {
											e.stopPropagation();
											copyToPaste(record.sid);
										} }
										className="r-fc-black-65 r-pointer r-ml-4 r-mr-5"
									/>
								</span>
							</span>
						</div>
					</div>
				);
			}
		},
		{
			title: '售后类型',
			width: 80,
			dataIndex: 'afterSaleType',
			key: 'afterSaleType',
			render: (value, row, index) => {
				return (<span>{AFTER_SALE_STATUS_TYPE[value]}</span>);
			}
		},
		{
			title: renderGoodsContentColHeader,
			sortSet: { name: "处理商品信息" },
			width: 220,
			minWidth: 220,
			dataIndex: 'goodsInfo',
			key: 'goodsInfo',
			render(_, record) {
				return renderGoodsInfo(_, record?.itemDTOList || [], { productContentList, isShowZeroStockVersion, productSetting: PRODUCT_SETTING_OBJECT, productContentEnum: PRODUCT_CONTENT_ENUM });
			}
		},
		{
			title: '收货状态',
			width: 80,
			dataIndex: 'confirmStatus',
			key: 'confirmStatus',
			render: (value, row, index) => {
				return (<span>{GOOD_STATUS_TYPE[value]}</span>);
			}
		},
		{
			title: '收货总数',
			width: 80,
			dataIndex: 'confirmNumCount',
			key: 'confirmNumCount',
			render: (value, row, index) => {
				return value;
			}
		},
		{
			title: '订单/售后单号',
			width: 220,
			minWidth: 220,
			dataIndex: 'ptTid',
			key: 'ptTid',
			render: (value, record, index) => {
				const refundIds = record?.refundId?.split(',') || []; // 可能有多个售后单
				return (
					<div className={ s['trade-info-container'] }>
						{
							record?.buildType === 3 && <div style={ { color: "#f00" } }>无主件</div>
						}

						{
							record.ptTid && (
								<div className={ cs('r-flex') }>
									<div style={ { flexShrink: 0 } }>订单：</div>
									<div className="r-flex-1">
										<a
											className="r-fc-1890FF"
											href={ getPlatformTradeLink(record['platform'], record).trade }
											target="_blank"
											rel="noreferrer"
										>
											{record.ptTid}
										</a>

										<CopyOutlined onClick={ () => { copyToPaste(record.ptTid); } } className="r-fc-black-65 r-pointer r-ml-4 r-mr-5" />
									</div>
								</div>
							)
						}


						{
							refundIds?.length > 0 && (
								<div className={ cs('r-flex', 'r-mt-6') }>
									<div style={ { flexShrink: 0 } }>售后：</div>
									<div className="r-flex-1">
										{
											refundIds?.map((item, index) => {
												let isShowCopy = (refundIds?.length - 1) == index;
												let itemLink = getPlatformTradeLink(record['platform'], { ...record, refundId: item });
												return (
													<div key={ item }>
														<a
															className="r-fc-1890FF"
															href={ itemLink?.refund }
															target="_blank"
															rel="noreferrer"
														>
															{item}
														</a>
														{
															isShowCopy && (
																<CopyOutlined onClick={ () => { copyToPaste(record.refundId); } } className="r-fc-black-65 r-pointer r-ml-4 r-mr-5" />
															)
														}
													</div>
												);
											})
										}
									</div>
								</div>
							)
						}

					</div>
				);
			}
		},
		{
			title: '旗帜/卖家备注',
			key: 'sellerMemo',
			dataIndex: 'sellerMemo',
			width: 140,
			minWidth: 140,
			render: (text, record, index) => {
				if (record?.buildType === 3 || (record?.isNoTradeMess === true && record?.buildType === 1)) {
					return null;
				}
				// 添加判断是否为京喜订单的逻辑
				const hasJingxi = record.refundTagList?.includes('jxTrade');

				return (
					<>
						<div className="r-flex r-fw-w">
							<p className={ `${cs('r-fc-black-65')} r-mt-4` }>卖家备注：</p>
							<p>
								{getTradeFlag(0, null, record.sellerFlag)}
								{getTradeFlagTag(record.sellerFlag, record?.sellerFlagTag)}
								<span className={ cs('r-fc-black-65') }>{record['sellerMemo']}</span>
								{!hasJingxi && (
									<span className="r-as-c r-ml-2 r-fc-1890FF">
										{record['sellerMemo']
											? <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyMemo([record], index); } }><Icon type="bianji" /></span>
											: <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyMemo([record], index); } }><Icon type="tianjiabeizhu" /></span>}
									</span>
								)}
							</p>
						</div>
					</>
				);
			}
		},
		{
			title: '线下备注',
			key: 'localContent',
			dataIndex: 'localContent',
			width: 140,
			minWidth: 140,
			render: (text, record, index) => {
				return (
					<>
						<div className="r-flex r-fw-w r-mt-4">
							<p className={ `${cs('r-fc-black-65')}` }>线下备注：</p>
							<p>
								<span className={ cs('r-fc-black-65') }>{record[listItemName.线下备注]}</span>
								<span className="r-as-c r-ml-2 r-fc-1890FF">
									{record[listItemName.线下备注]
										? <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemo([record], index); } }><Icon type="bianji" /></span>
										: <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemo([record], index); } }><Icon type="tianjiabeizhu" /></span>}
								</span>
							</p>

						</div>
					</>
				);
			}
		},
		{
			title: '线下备注图片',
			key: 'localMemoPic',
			dataIndex: 'localMemoPic',
			width: 140,
			minWidth: 140,
			render: (text, record, index) => {
				const hasImgList = record.localContentPicList && record.localContentPicList.length > 0;
				return (
					<>
						<div className="r-flex r-fw-w r-mt-4">
							<p>
								{hasImgList ? (
									<div className="r-flex r-ai-c">
										{/* 修改为循环展示所有图片，从左到右排列，超出边界才换行 */}
										<div className="r-flex r-ai-c r-fw-w" style={ { maxWidth: '300px', display: 'flex', flexDirection: 'row' } }>
											{record.localContentPicList.map((imgUrl, imgIndex) => (
												<Popover
													key={ imgIndex }
													placement="right"
													content={ <Image src={ imgUrl } style={ { width: '300px' } } /> }
												>
													<div style={ { display: 'inline-block', marginRight: '4px', marginBottom: '4px' } }>
														<Image
															src={ getImageThumbnail({ noScale: false, url: imgUrl, width: 40, height: 40, quality: 100 }) }
															width={ 40 }
															height={ 40 }
															style={ { objectFit: 'cover' } }
														/>
													</div>
												</Popover>
											))}
										</div>
										<span className="r-as-c r-ml-2 r-fc-1890FF">
											<span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemoPic(record); } }><Icon type="bianji" /></span>
										</span>
									</div>
								) : (
									<span className="r-as-c r-fc-1890FF">
										<span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemoPic(record); } }><Icon type="tianjiabeizhu" /></span>
									</span>
								)}
							</p>
						</div>
					</>
				);
			}
		},
		{
			title: shopInfoColHeader,
			sortSet: { name: isShowShopName ? "店铺名称" : "店铺简称" },
			width: 120,
			minWidth: 120,
			dataIndex: 'shopInfo',
			key: 'shopInfo',
			render: (_, record) => {
				return isShowShopName ? !userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isAfterSaleSourceScm(record) ? '****' : record.sellerNick : record.sellerAbbreviation;
			}
		},
		{
			title: '买家昵称',
			width: 80,
			dataIndex: 'buyerNick',
			key: 'buyerNick',
			render: (_) => _,
		},
		{
			title: '收件信息',
			width: 100,
			dataIndex: 'receiverName',
			key: 'receiverName',
			render: (value, record, index) => {
				const { receiverName, receiverMobile } = record;
				return (
					<div>
						<div>{receiverName}</div>
						<div>{receiverMobile}</div>
					</div>
				);
			},
		},
		{
			title: '操作入口',
			width: 120,
			dataIndex: 'scanRecordSource',
			key: 'scanRecordSource',
			sortSet: { ischecked: false },
			render: (value, record, index) => {
				return <span>{OperatorFromObj?.[record?.scanRecordSource]}</span>;
			},
		},
		{
			title: '匹配维度',
			width: 120,
			dataIndex: 'refundScanContent',
			key: 'refundScanContent',
			sortSet: { ischecked: false },
			render: (value, record, index) => {
				const { refundScanContent = '', refundScanQueryType = '' } = record;
				return (
					<div>
						{
							refundScanQueryTypeObj?.[refundScanQueryType] && (
								<div>{refundScanQueryTypeObj?.[refundScanQueryType]} : </div>
							)
						}
						<div>{refundScanContent}</div>
					</div>
				);
			},
		},
		{
			title: '扫描操作人',
			width: 100,
			dataIndex: 'scanOperatorNick',
			key: 'scanOperatorNick',
			sortSet: { ischecked: false },
			render: (_) => _,
		},
		{
			title: '收货操作人',
			width: 100,
			dataIndex: 'operatorNick',
			key: 'operatorNick',
			sortSet: { ischecked: false },
			render: (_) => _,
		}
	];

	// 数据请求
	const getProductList = async(values) => {
		// 校验数据
		console.log("getProductListApi -> 查询数据", values);
		if (!verifySearchParams(values)) {
			return;
		}
		const params = {};
		handleGoodsInfoSearchParams(values, params, true);

		let info:QueryRefundScanRecordListRequest = clearParams(values);
		const _params:QueryRefundScanRecordListRequest = {
			// ...info,
			timeType: info.timeType,
			startTime: info.date[0]?.format("YYYY-MM-DD HH:mm:ss"),
			endTime: info.date[1]?.format("YYYY-MM-DD HH:mm:ss"),
			confirmStatus: info.confirmStatus || '',
			refundIds: info.refundIds,
			// tids: info.tids, // 系统单号移除
			ptTids: info?.ptTids || '',
			afterSaleType: info.afterSaleType || "",
			companyName: info.companyName,
			sid: info.sid,
			buyerNick: info.buyerNick,
			receiverName: info.receiverName,
			receiverMobile: info.receiverMobile,
			operatorNick: info.operatorNick,
			pageNo: info.pageNo,
			pageSize: info.pageSize,
			isFilterNoConfirmRecord: info?.isFilterNoConfirmRecord?.includes('isFilterNoConfirmRecord') ? 1 : 0, // 隐藏未收货记录
			marketIncludingList: info?.marketIncludingList?.map(i => i.trim()), // 市场
			stallIncludingList: info?.stallIncludingList?.map(i => i.trim()), // 档口
			supplierIncludingList: info?.supplierIncludingList?.map(i => i.trim()), // 供应商
			isNoTradeMess: info?.isNoTradeMess, // 是否无主件
			...params
		};
		// 留言备注
		let flagValue;
		let flagSelValue;
		let sellerFlag;
		if (values.flagValue?.includes('_')) {
			flagSelValue = values.flagValue;
		} else {
			flagValue = values.flagValue;
		}
		if (values.sellerFlag?.length > 1) {
			let curFlag: any[] = [];
			flagGroup.forEach(item => {
				if (values.sellerFlag.includes(item.toSellerFlag)) {
					curFlag.push(item.value);
				}
			});
			flagSelValue = curFlag.toString() + '_1';
		} else {
			sellerFlag = values?.sellerFlag?.toString();
		}

		flagValue = flagValue === '-1' ? '10' : flagValue;
		_params['flagValue'] = flagValue;
		_params['flagSelValue'] = flagSelValue;

		_params['sellerMemo'] = values.sellerMemo;
		_params['sellerFlag'] = sellerFlag;

		if (values[OfflineMemoEnum.是否有线下备注] !== undefined) {
			_params[OfflineMemoEnum.是否有线下备注] = values[OfflineMemoEnum.是否有线下备注] == 1;
		}

		if (values[OfflineMemoEnum.线下备注内容]) {
			_params[OfflineMemoEnum.线下备注内容] = values[OfflineMemoEnum.线下备注内容];
		}

		// 如果店铺平台都没有选择，不需要传递multiShops给后端，不然查不出来无主售后单的扫描记录
		const multiShopsData = info.multiShops || {};
		const validatePlatform = multiShopsData.plats?.length > 0;
		const validateShop = multiShopsData.plat_sellerIds?.length > 0;
		if (validatePlatform || validateShop) {
			const { plats, plat_sellerIds } = multiShopsData || {};
			let multiShops = await getMultiShopsWithFilter({ plats, plat_sellerIds, hidePlatforms: [PLAT_KTT, PLAT_DW, PLAT_YZ] });
			multiShops && (_params['multiShopS'] = multiShops);
		}
		setQueryParams(_params);

		return QueryRefundScanRecordListApi(_params);
	};

	const downloadData = async(params) => {
		await downloadCenter({ requestParams: params, fileName: '售后扫描记录', module: ModulesFunctionEnum.售后扫描登记 });
	};

	const onBatchExportCancel = () => {
		setBatchExportModalVisible(false);
	};
	const onBatchExportOk = async(exportParams) => {
		const { method: exportMethod } = exportParams;
		const params = {
			...queryParams,
			latitude: exportType,
			idList: null
		};
		// 如果导出方式是勾选订单，需要校验是否勾选
		if (exportMethod === BatchExportMethod.导出勾选订单) {
			if (selectedRowKeys?.length < 1) {
				message.warn('请选择需要导出的快递单');
				return;
			} else {
				params.idList = selectedRowKeys;
			}

		}
		downloadData(params);
		setBatchExportModalVisible(false);
	};

	const exportDetail = async(latitude = exportType) => {
		try {
			console.log("latitude::", latitude);
			setBatchExportModalVisible(true);
			setBatchExportParams({
				getListLoading: true,
				fieldTitleList: ["快递信息", "商品信息"],
				selectList: [],
				localName: "AfterSale.scanRegister.BatchExportMethod",
			});
			const res = await GetRefundExportConfigApi({ latitude });
			handleBatchExportParams(res);
		} catch (error) {
			console.log("exportDetail error:", error);
		}
	};

	const onBatchExportReset = async() => {
		const res = await ResetRefundExportConfigApi({ latitude: exportType });
		handleBatchExportParams(res);
	};

	const onBatchExportChange = async(params) => {
		const res = await UpdateRefundExportConfigApi({ ...params, latitude: exportType });
		handleBatchExportParams({ exportFields: params?.fields });
	};

	const handleBatchExportParams = (res) => {
		const tradeList = res?.exportFields?.filter((item) => (item.type == 0));
		const goodsList = res?.exportFields?.filter((item) => (item.type == 1));
		setBatchExportParams((prev) => {
			return {
				...prev,
				getListLoading: false,
				fieldList: [tradeList, goodsList],
				fieldTitleList: ["快递信息", "商品信息"],
			};
		});
	};

	const onResizeChange = (v) => {
		// setInitMap(v);
		// UpdateRefundGlobalConfigApi([{
		// 	biz: AftersaleGlobalConfig.售后订单列宽设置,
		// 	value: JSON.stringify(v)
		// }]);
	};

	const onSortChange = (v) => {
		setInitList(v);
		console.log("v:", v);
		UpdateRefundGlobalConfigApi([{
			biz: AftersaleGlobalConfig.售后扫描记录列配置,
			value: JSON.stringify(v)
		}]);
	};

	const onSortReset = (width, list) => {
		// onResizeChange(width);
		onSortChange(list);
	};

	const rowSelection = {
		onChange: (selectedRowKeys: React.Key[], selectedRows) => {
			setSelectedRowKeys(selectedRowKeys);
			setSelectedRows(selectedRows);
		},
	};
	const extraExportTypeContent = () => {
		const exportTypeChange = (e) => {
			const { value } = e.target;
			setExportType(value);
			setBatchExportParams((prev) => {
				return {
					...prev,
					exportExtraParams: {
						latitude: value
					}
				};
			});
			exportDetail(value);
		};
		return (
			<div>
				<span>导出类型：</span>
				<Radio.Group onChange={ exportTypeChange } value={ exportType }>
					<Radio type="radio" value={ BatchExportType.快递单维度 } className="r-mb-10" >按快递单维度</Radio>
					<Radio type="radio" value={ BatchExportType.商品维度 } className="r-mb-10" >按商品维度</Radio>
				</Radio.Group>
			</div>
		);
	};

	// 查询按钮后面的勾选项
	const getExpandedNode = (
		<Form.Item name="isFilterNoConfirmRecord" style={ { marginBottom: 0, flexShrink: 0 } }>
			<Checkbox.Group>
				<Checkbox value="isFilterNoConfirmRecord">隐藏未收货记录</Checkbox>
			</Checkbox.Group>
		</Form.Item>
	);

	const responseAdapter = (data, params) => {
		setSelectedRowKeys([]);

		const dataSource = data?.list?.map((i, index) => ({
			rowId: i.id,
			...i,
		})) || [];

		let sortData = handleSortData(dataSource);
		setDataSource(sortData);
		setDataOrigin(dataSource);

		return {
			list: sortData,
			total: data.total || 0
		};
	};

	const handleSetBtnsDefault = (fieldName, value) => {
		setOperateBtnsDefault(prev => {
			local.set("scanRecordOperateBtnsDefault", {
				...prev,
				[fieldName]: value
			});
			return {
				...prev,
				[fieldName]: value
			};
		});
	};

	// 添加修改线下备注图片的函数
	const modifyOfflineMemoPic = (item) => {
		// 添加 ref 检查
		if (batchModifyLocalMemoPicModalRef.current) {
			batchModifyLocalMemoPicModalRef.current.open({
			  refundId: item.refundId,
			  refundScanRecordId: item.id,
			  sid: item.sid,
			  localMemoPic: Array.isArray(item.localContentPicList)
			   ? item.localContentPicList.join(',')
			    : item.localContentPicList || '',
			  requestSource: AFTERSALE_REQUEST_SOURCE.扫描记录,
			}, refreshPage);
		  } else {
			console.error('BatchModifyLocalMemoPicModal ref is not initialized');
		  }
	};

	// 打开批量修改备注弹框
	const openModifyMemo = (list: any[]) => {
		const packList = list.map((item) => ({
			trades: [
				{
					sellerMemo: item.sellerMemo,
					sellerMemoFlag: item.sellerFlag,
					tid: item.tid,
					ptTid: item?.ptTid || "",
				}
			],
			platform: item.platform,
			source: item.source,
			togetherId: item.tid,
			sellerId: item.sellerId,
			refundStatus: item.refundStatus || "",
			afterSalesFlag: true,
		}));
		setIsShowBatchModifyMemoModal(true);
		setModifyMemoPackage(packList);
	};

	// 编辑备注
	const modifyMemo = (items, index: number) => {
		console.log(items, "modifyMemo");
		if (items?.length < 1) {
			message.info("请先选择");
			return;
		}
		if (items.some(item => item?.buildType === 3 || (item?.isNoTradeMess === true && item?.buildType === 1))) {
			message.info("无主件不可进行备注");
			return;
		}
		openModifyMemo(items);
	};

	// 编辑线下备注
	const modifyOfflineMemo = (items, index) => {
		if (items?.length < 1) {
			message.info("请先选择");
			return;
		}
		setOfflineMemoData({ visible: true, list: items });
	};

	// 编辑线下备注
	const handleOfflineMemoOk = ({ list }) => {
		console.log("handleOfflineMemoOk:", list);
		setOfflineMemoData((prev) => {
			return {
				...prev,
				loading: true,
			};
		});
		const newList = list.map((item) => ({
			...item,
			updateType: UpdateTypeEnum.线下备注,
			requestSource: AFTERSALE_REQUEST_SOURCE.扫描记录,
		}));
		BatchUpdateLocalNoteApi(newList)
			.then((res) => {
				console.log(res);
				setOfflineMemoData({ visible: false, loading: false });
				message.success("编辑成功");
				refreshPage();
			})
			.catch(() => {
				setOfflineMemoData((prev) => ({ ...prev, loading: false }));
			});
	};

	const handleOfflineMemoCancel = () => {
		setOfflineMemoData({ visible: false });
	};

	const BatchMemoBtns = useCallback(() => {
		return (
			<Dropdown.Button
				onClick={ _.debounce(() => {
					if (operateBtnsDefault.batchMemoBtn === "batchMemoOutline") {
						modifyOfflineMemo(selectedRows, 0);
					} else {
						modifyMemo(selectedRows, 0);
					}
				}, 500, {
					leading: true,
					trailing: false
				}) }
				className="r-mr-8"
				overlay={ (
					<Menu>
						<Menu.Item
							key="batchMemo"
							onClick={ _.debounce(() => modifyMemo(selectedRows, 0), 500, {
								leading: true,
								trailing: false
							}) }
						>
							<div className="r-flex r-jc-sb r-ai-c">
								<span className="r-mr-8">批量备注</span>
								{
									operateBtnsDefault?.batchMemoBtn === "batchMemo"
										? (
											<Icon
												className="r-c-warning"
												type="guding"
												onClick={ (e) => {
													e.stopPropagation();
												} }
											/>
										)
										: (
											<Icon
												className="r-c-999"
												type="weiguding"
												onClick={ (e) => {
													e.stopPropagation();
													handleSetBtnsDefault("batchMemoBtn", "batchMemo");
												} }
											/>
										)
								}
							</div>
						</Menu.Item>
						<Menu.Item key="batchMemoOutline" onClick={ ({ key }) => modifyOfflineMemo(selectedRows, 0) }>
							<div className="r-flex r-jc-sb r-ai-c">
								<span className="r-mr-8">批量线下备注</span>
								{
									operateBtnsDefault?.batchMemoBtn === "batchMemoOutline"
										? (
											<Icon
												className="r-c-warning"
												type="guding"
												onClick={ (e) => {
													e.stopPropagation();
												} }
											/>
										)
										: (
											<Icon
												className="r-c-999"
												type="weiguding"
												onClick={ (e) => {
													e.stopPropagation();
													handleSetBtnsDefault("batchMemoBtn", "batchMemoOutline");
												} }
											/>
										)
								}
							</div>
						</Menu.Item>
					</Menu>
				) }
				key="batchMemoBtn"
			>
				{ operateBtnsDefault?.batchMemoBtn === "batchMemoOutline" ? "批量线下备注" : "批量备注" }
			</Dropdown.Button>
		);
	}, [operateBtnsDefault, selectedRows]);

	const searchTableExtra = (
		<>
			<Button
				className="r-mr-8"
				type="primary"
				onClick={ () => { exportDetail(); } }
				disabled={ !response.dataSource || response?.dataSource?.length < 1 }
			>
				导出扫描记录
			</Button>
			{
				BatchMemoBtns()
			}
		</>
	);

	// 操作完成 刷新页面
	const refreshPage = () => {
		tableRef?.current?.refresh(() => {
			console.log("操作完成 刷新页面");
		});
	};

	return (
		<NormalLayout className={ cs('r-w-full', 'print-batch-search-con') }>
			<SearchTable
				pageSizeId="scanRecordTable"
				form={ form }
				ref={ tableRef }
				fetchData={ getProductList }
				responseAdapter={ responseAdapter }
				searchBtnText="查询"
				searchBtnProps={ { style: { marginRight: 10 } } }
				resetBtnText="重置"
				onReset={ () => {
					setTimeout(() => {
						form.setFieldsValue({
							...defaultParams
						});
						setFormData(form.getFieldsValue());
					});
				} }
				showSearch // 是否显示查询
				rowFormConfig={ { // 表单配置
					formList: FormFieldListV1,
					style: { paddingTop: 8 },
					defaultParams, // 默认表单查询项
					rowProps: {}, // 表单行配置
					colProps: { },
				} }
				additionalFormNode={ getExpandedNode }
				baseTableConfig={ { // 表格基础设置
					noGap: true,
					innerTableStyle: { paddingTop: 0 },
					rowKey: 'rowId',
					columns, // 列配置=
					onFieldsChange,
					cachePgination: true,
					pagination: false,
					loading,
					expandContext: searchTableExtra,
					expandContextStyle: { display: 'flex', alignItems: 'center', height: "60px", overflowX: 'unset' },
					headerColSet: {
						sortId: `AfterSale_scanRegister_sort_${userStore?.userInfo?.userId}_${userStore?.userInfo?.subUserId}`,
						// onResizeChange,
						onSortChange,
						onReset: onSortReset,
						initList,
						useDrawer: true
					},
					rowSelection: {
						type: 'checkbox',
						...rowSelection,
						selectedRowKeys,
					},
					scroll: {
						x: '100%',
					},
					isStickyHeader: true,
					stickyTop: 150,
					dataSource, // data
				} }
				onChange={ response => {
					setResponse(response);
				} }
			/>
			{
				batchExportModalVisible && <BatchExportModal exportParams={ batchExportParams } onReset={ onBatchExportReset } onChange={ onBatchExportChange } onCancel={ onBatchExportCancel } onOk={ onBatchExportOk } extraExportTypeContent={ extraExportTypeContent() } />
			}
			{/* 订单备注 */}
			{isShowBatchModifyMemoModal ? (
				<BatchModifyMemoModal
					onOk={ () => {
						setIsShowBatchModifyMemoModal(false);
						refreshPage();
					} }
				/>
			) : (
				""
			)}

			{/* 线下备注 */}
			<OfflineMemoModal
				onOk={ handleOfflineMemoOk }
				onCancel={ handleOfflineMemoCancel }
				data={ offlineMemoData }
				fromPage={ EnumFromPage.扫描记录 }
			/>

			<BatchModifyLocalMemoPicModal ref={ batchModifyLocalMemoPicModalRef } />
		</NormalLayout>
	);
};
export default observer(ScanRecord);
