import React, { useEffect, useState, useRef, useMemo } from 'react';
import { Form, Input, Select } from 'antd';
import cs from 'classnames';
import { CopyOutlined } from '@ant-design/icons';
import SearchTable from '@/components/SearchTable';
import BaseModal from '@/components/Modal';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import FlagAndMemoSelect from '@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagAndMemoSelect';
import { getTradeFlag, getTradeFlagTag } from '@/pages/Trade/utils';
import { getMultiShops, getMultiShopsWithFilter } from '@/components-biz/ShopListSelect/shopListUtils';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import { copyToPaste } from '@/utils';
import { clearParams } from '@/utils/stringHelper';
import { getModalTableScrollHeight } from '@/utils/util';
import WaresInfo from '@/components-biz/WaresInfo';
import { observer } from 'mobx-react';
import userStore from "@/stores/user";
import tradeStore from "@/stores/trade";
import FlagSelect from '@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagSelect';
import InputMulti from '@/components/Input/InputMulti';
import { TradeQueryTradeApi } from "@/apis/trade/search";
import { handlePackageList } from "@/pages/Trade/utils";
import {MessageFragment, ProductOrderFragment} from "@/pages/Trade/components/ListItem/components/simpleCom";
import ProductSettingPlus from "@/components-biz/ProductSetting";
import dayjs from 'dayjs';
import { flagGroup, PLAT_KTT, PLAT_DW, PLAT_YZ } from "@/constants";
import s from './index.module.scss';
import '@/pages/Trade/components/ListItem/index.scss';


const { Option } = Select;

interface IProps {
	skuId?: string;
	sysSkuId?: string;
	sysItemId?: any;
	skuOuterId?: string;
	handleCancel?: () => void;
}

const enum RefundStatusEnum {
	REFUND_SUCCESSED = '退款成功',
	REFUND_ING = '退款中',
	NOT_REFUND = '无售后',
}

const enum OutOfStockStatusEnum {
	HAS_STOCK = '有货',
	NO_STOCK = '无货',
	PART_STOCK = '部分有货',
}

enum TradeStatusEnum {
	'WAIT_BUYER_PAY' = '待付款',
	'WAIT_SELLER_SEND_GOODS' = '待发货',
	'SELLER_CONSIGNED_PART' = '部分发货',
	'WAIT_BUYER_CONFIRM_GOODS' = '已发货',
	'TRADE_BUYER_SIGNED' = '已签收',
	'TRADE_FINISHED' = '交易成功',
	'TRADE_CLOSED' = '交易自动关闭',
	'TRADE_CLOSED_BY_TAOBAO' = '交易主动关闭',
}


const TradeInfoModal = (props: IProps) => {
	const {handleCancel, sysSkuId, skuId } = props;
	const [form] = Form.useForm();
	const ref = useRef<SearchTableRefProps>(); // 获取表单对象
	
	const [loading, setLoading] = useState(false);
	const [total, setTotal] = useState(0);
	const [formData, setFormData] = useState<any>({});

	const {
		userInfo,
		isStockAllocationVersion
	} = userStore;

	const {productSetting} = tradeStore;

	// 表格列定义
	const columns = useMemo(()=>{
		let list = [
			{
				title: '平台店铺',
				width: 40,
				dataIndex: 'platform',
				key: 'platform',
				render: (value, row, index) => {
					return (
						<div className="r-flex">
							<PlatformIcon platform={ row?.platform } />{ row?.platform == 'HAND' ? '手工单' : row?.sellerNick }
						</div>
					);
				},
			},
			{
				title: '系统单号',
				width: 40,
				dataIndex: 'tid',
				key: 'tid',
				render: (value, row, index) => {
					const {tids=[], platform} = row;
					const list = tids?.[0]?.split('|') || [];
					return (
						<>
							{list.map((tid: string, idx: number, arr: string[]) => (
								<div key={ `${platform}-${tid}` }>
									{tid}
									{idx === arr.length - 1 && (
										<CopyOutlined
											className="r-pointer r-ml-5"
											onClick={() => {
												copyToPaste(list.join(','));
											}}
										/>
									)}
								</div>
							))}
						</>
					)
				},
			},
			{
				title: '订单编号',
				width: 40,
				dataIndex: 'ptTid',
				key: 'ptTid',
				render: (value, row, index) => {
					const {ptTids=[], platform} = row;
					const list = ptTids?.[0]?.split('|') || [];
					return (
						<>
							{list.map((ptTid: string, idx: number, arr: string[]) => (
								<div key={ `${platform}-${ptTid}` }>
									{ptTid}
									{idx === arr.length - 1 && (
										<CopyOutlined
											className="r-pointer r-ml-5"
											onClick={() => {
												copyToPaste(list.join(','));
											}}
										/>
									)}
								</div>
							))}
						</>
					)
				},
			},
			// {
			// 	title: '订单状态',
			// 	width: 30,
			// 	dataIndex: 'status',
			// 	key: 'status',
			// 	render: (value, row, index) => {
			// 		return (
			// 			<>
			// 				{TradeStatusEnum[value] || ''}
			// 			</>
			// 		);
			// 	},
			// },
			// {
			// 	title: '售后状态',
			// 	width: 30,
			// 	dataIndex: 'refundStatus',
			// 	key: 'refundStatus',
			// 	render: (value) => {
			// 		return (
			// 			<div>{RefundStatusEnum[value] || ''}</div>
			// 		);
			// 	}
			// },
			{
				title:(
					<div>
						商品信息
						{/* <ProductSettingPlus title='商品信息'></ProductSettingPlus> */}
					</div>
				),
				key: 'itemInfos',
				width: 260,
				dataIndex: 'itemInfos',
				render: (text, row:any, index) => {
					const { productOrders } = row;
					return (
						<div className={cs("r-flex r-fw-w product-container", s.productContainer)}>
							<ProductOrderFragment showSelect={ false } productOrders={ productOrders } pack={row} />
						</div>
					);
				}
			},
			{
				title: '留言备注',
				width: 40,
				dataIndex: 'sellerMemo',
				key: 'sellerMemo',
				render: (value, row, index) => {
					const {trades=[]} = row;
					return (
						<div style={ { wordBreak: 'break-word', whiteSpace: 'pre-wrap' } }>
							{trades.map(trade => (
								<MessageFragment key={ trade.tid } trade={ trade } />
							))}
						</div>
					)
				}
			},
			{
				title: '下单时间',
				width: 30,
				dataIndex: 'createTime',
				key: 'createTime',
			},
			{
				title: '付款时间',
				width: 30,
				dataIndex: 'payTime',
				key: 'payTime',
			}
		];
		return list.filter(d=>d.title);
	},[productSetting?.showColumn]);


	// 表单元素定义
	let FormFieldListV1: FormItemConfig[] = useMemo(()=>{

		let isShowMore = ['-1'].includes(formData.flagValue);

		let list = [
			{
				name: "platformInfo",
				children: (
					<ShopMultiSelect
						isSendPoint
						isHasHandPlat
						style={ { width: 150 } }
						size='small'
						hidePlatforms={ [PLAT_KTT, PLAT_DW, PLAT_YZ] }
					/>
				),
			}, 
			{
				name: "tid",
				children: <Input placeholder="输入系统单号" maxLength={ 150 } />,
			}, 
			{
				name: "ptTid",
				children: <Input placeholder="输入订单编号" maxLength={ 150 } />,
			},
			{
				name: 'refundStatus',
				children: (
					<Select className={ cs('r-w-full') } placeholder="退款状态" size="small" style={ { width: 150 } } allowClear>
						<Option value="" key={ 2 }>全部</Option>
						<Option value="HAS_REFUND" key={ 0 }>有退款</Option>
						<Option value="NOT_REFUND" key={ 1 }>无退款</Option>
					</Select>)
			}, 
			isStockAllocationVersion ? {
				name: 'goodStockStatus',
				children: (
					<Select className={ cs('r-w-full') } placeholder="订单缺货状态" size="small" style={ { width: 150 } } allowClear>
						<Option value="ALL" key="ALL">订单缺货状态</Option>
						<Option value="HAS_STOCK" key="HAS_STOCK">有货</Option>
						<Option value="PART_STOCK" key="PART_STOCK">部分有货</Option>
						<Option value="NO_STOCK" key="NO_STOCK">缺货</Option>
					</Select>)
			} : {}, 
			{
				name: "flagValue",
				children: (
					<FlagAndMemoSelect style={ { width: 150 } } />
				),
			},
			isShowMore ? {
				name: "buyerMessageOrSellerMemo",
				children: (
					<InputMulti
						placeholder="留言或备注内容"
						maxInputNum={ 50 }
						className={ `${formData.buyerMessageOrSellerMemo ? 'high-light-bg' : ''}` }
						maxInputLength={ 1000 }
						lengthErrorMsg="最多输入1000个字数，请重新输入"
						numErrorMsg="单次查询最多筛选50个请重新输入,"
						style={ { width: 150 } }
						size="small"
					/>
				),
			} : {},
			isShowMore ? {
				name: "sellerFlag",
				children: (
					<FlagSelect
						className={ `${formData.sellerFlag?.length ? 'high-light-bg' : ''}` }
						placeholder="旗帜"
						style={ { width: 150 } }
						size="small"
					/>
				),
			} : {},
		];
		return list.filter(d=>d.name);
	},[formData, isStockAllocationVersion])





	// 数据请求
	const getProductList = async(searchParams) => {
		if (!skuId && !sysSkuId) return;
		const { platformInfo, ...rest } = searchParams;
		const { plats = [], plat_sellerIds = [] } = platformInfo || {};
		let multiShopS = await getMultiShopsWithFilter({plats, plat_sellerIds, hidePlatforms: [PLAT_KTT, PLAT_DW, PLAT_YZ]});


		let flagValue;
		let flagSelValue;
		let sellerFlag;
		if (searchParams?.flagValue?.includes('_')) {
			flagSelValue = searchParams.flagValue;
		} else {
			flagValue = searchParams.flagValue;
		}

		flagValue = flagValue === '-1' ? '10' : flagValue;

		if (searchParams?.sellerFlag?.length > 1) {
			let curFlag:any[] = [];
			flagGroup.forEach(item => {
				if (searchParams?.sellerFlag?.includes(item.toSellerFlag)) {
					curFlag.push(item.value);
				}
			});
			flagSelValue = curFlag.toString() + '_1';
		} else {
			sellerFlag = searchParams?.sellerFlag?.toString();
		}

		const params:any = clearParams({
			...rest,
			skuId,
			sysSkuId,
			multiShopS, // 店铺
			isPlatformEmptyQuery: !plat_sellerIds?.length && !plats?.length,
			startTime: dayjs().subtract(1, "M").startOf('day').format('YYYY-MM-DD HH:mm:ss'),
			endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
			status: 'WAIT_SELLER_SEND_GOODS', // 待发货
			orderStatus: 'WAIT_SELLER_SEND_GOODS', // 子订单状态待发货
			timeType: '1', // 1 下单时间 2 付款时间
			sellerFlag,
			flagValue,
			flagSelValue,
		}, true);
		try {
			setLoading(true);
			const res:any = await TradeQueryTradeApi(params);
			const {data={}} = res;
			let _list = await handlePackageList(res.data.list, true, params);

			console.log('%c [ res ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', _list, res)

			setLoading(false);
			const infoTotal = params.pageNo == 1 ? data?.total ?? 0 : total;
			if (params.pageNo == 1) {
				setTotal(infoTotal);
			}
			return {
				list: _list || [],
				total: infoTotal
			};
		} catch (error) {
			console.log(error);
			setLoading(false);
		}
	};

	const afterCancel = () => {
		handleCancel && handleCancel();
		form.resetFields();
	};

	const onFieldsChange = (changedValues, allValues) => {
		console.log('changedValues', changedValues);
		console.log('allValues', allValues);

		setFormData(allValues);
	};

	useEffect(() => {
		tradeStore?.getProductSetting();
	}, []);

	return (
		<BaseModal
			triggerNode={ false }
			afterCancel={ afterCancel }
			visible
			width={ 1390 }
			footer={ null }
			title="待发货订单详情"
			centered
			forceRender
			className={s.stockTradeInfoModal}
			destroyOnClose
		>
			<div>
				<SearchTable
					pageSizeId="aftersaleScan_TradeInfoModalTable"
					ref={ ref } // 引用
					form={ form } //
					fetchData={ getProductList } // 接口请求
					searchBtnText="查询"
					resetBtnText="重置"
					showSearch // 是否显示查询
					autoSearch={ true } // 开启后才自动搜索
					rowFormConfig={ { // 表单配置
						formList: FormFieldListV1,
						defaultParams: { // 查询表单设置初始
						},
						rowProps: {}, // 表单行配置
						colProps: { },
						style: {
							padding: '0 0 16px 0'
						},
						size: "small",
					} }
					baseTableConfig={ { // 表格基础设置
						rowKey: 'id',
						columns, // 列配置
						pagination: { 
							showTotal: total => (
								<>
									
								</>
							),
							showSizeChanger: false,
							showQuickJumper: false
						},
						scroll: {
							scrollToFirstRowOnChange: true,
							y: getModalTableScrollHeight(),
						},
						loading,
						innerTableStyle: {
							padding: 0,
						},
						onFieldsChange
					} }
				/>
			</div>
		</BaseModal>
	);
};

export default observer(TradeInfoModal);
