.table {
	:global {
		.flex-grow {
			flex-grow: 1;
			min-width: 150px; // 设置最小宽度
		}

		// 默认状态下的row样式
		.ant-table-row.order {
			td {
				background-color: #ffeddd;
			}

			td.ant-table-cell-row-hover {
				background-color: #ffeddd;
			}
		}

		.ant-table-row.good {
			td {
				padding-bottom: 26px;
			}
		}

		.ant-table-row.row-start.row-end {
			.ant-table-selection-column::before {
				border-radius: 6px 0 0 6px;
			}
		}

		.ant-table-row.row-start {
			.ant-table-selection-column::before {
				border-radius: 6px 0 0 0;
			}
		}

		.ant-table-row {
			position: relative;

			td {
				background-color: #fff;
				vertical-align  : middle;
			}

			td.td-refund-mount input {
				color      : #f00;
				font-weight: bold;
			}

			td.td-refund-mount,
			td.td-receive-num {
				.ant-form-item {
					margin-bottom: 0;
				}
			}

			td.td-apply-refund-num {
				background-color: #ffccc7 !important;
				text-align      : center;
				font-size       : 16px;
				color           : rgba(0, 0, 0, 0.65);
			}

			td.ant-table-cell-row-hover.td-apply-refund-num {
				background-color: #ffccc7;
			}

			td.td-has-refund-num {
				background-color: #bae7ff !important;
				text-align      : center;
				font-size       : 16px;
				color           : rgba(0, 0, 0, 0.65);
			}

			td.ant-table-cell-row-hover.td-has-refund-num {
				background-color: #bae7ff;
			}

			.ant-table-selection-column::before {
				content   : " ";
				background: rgba(253, 36, 4, 0.3);
				width     : 10px;
				height    : calc(100% + 1px);
				display   : block;
				position  : absolute;
				top       : 0px;
				left      : -10px;
			}
		}

		.ant-table-row.row-end {
			.ant-table-selection-column::before {
				content      : "";
				height       : calc(100% - 9px);
				border-radius: 0 0 0 6px;
			}
		}

		.ant-table-row.row-end::after {
			content   : " ";
			display   : block;
			width     : 100%;
			height    : 10px;
			background: #fff;
			position  : absolute;
			left      : 0;
			bottom    : 0;
			border-top: 1px solid #f3f3f3;
		}

		// 勾选以后的row样式
		.ant-table-row.ant-table-row-selected {
			.ant-table-selection-column::before {
				background: rgba(253, 130, 4, 0.3);
			}
		}
	}
}

.order-info-container {
	>div:first-child {
		width     : 50px;
		text-align: left;
	}

	:global {
		.item {
			display      : flex;
			margin-right : 14px;
			margin-bottom: 4px;

			.label {
				white-space: nowrap;
				font-weight: bold;
				// width: 60px;
				text-align : left;
				color      : rgba(0, 0, 0, 0.65);
			}

			.value {
				flex      : 1;
				word-break: break-all;
			}
		}

		.shop {

			// min-width: 200px;
			// width: 30%;
			.value {
				display    : flex;
				align-items: flex-start;
				white-space: nowrap;
			}
		}

		.order-id {
			// min-width: 200px;
			// width: 30%;

			.value {
				white-space: nowrap;
			}
		}

		.refund-id {
			// min-width: 200px;
			// width: 30%;

			.value {
				white-space: nowrap;
			}
		}

		.refund-reason {
			// min-width: 200px;
			// width: 30%;
		}

		.buyer-nick {
			// min-width: 200px;
			// width: 30%;
		}

		.receiver {
			// min-width: 200px;
			// width: 20%;
		}

		.buyer-address {
			// min-width: 200px;
			// width: 20%;
		}

		.order-memo {
			// min-width: 200px;
			// width: 30%;
		}
	}
}

.goods-info-container {
	display    : flex;
	align-items: center;

	:global {
		.img-container {
			border         : 1px solid #e5e5e5;
			width          : 80px;
			height         : 80px;
			min-width      : 80px;
			min-height     : 80px;
			padding        : 1px;
			display        : flex;
			align-items    : flex-start;
			justify-content: center;
		}

		.goods-content {
			flex       : 1;
			margin-left: 16px;

			.goods-name {
				color: rgba(0, 0, 0, 0.65);
			}

			.goods-specification {
				margin-top: 4px;
				color     : rgba(0, 0, 0, 0.45);
			}
		}
	}
}

.product-container {
	display       : flex;
	// align-items: center;
	flex-direction: row;

	:global {
		.product-name {
			color: rgba(0, 0, 0, 0.65);
		}

		.product-specification {
			margin-top: 4px;
			color     : rgba(0, 0, 0, 0.45);
		}
	}
}

.receive-num-container {
	display    : flex;
	align-items: baseline;

	:global {
		.plus {}

		.minus {}
	}
}

.footer-container {
	// position: fixed;
	bottom    : 0;
	// width: calc(100% - 300px);
	width     : 100%;
	background: #fff;

	:global {
		.count-container {
			display   : flex;
			padding   : 10px 18px;
			background: rgba(0, 0, 0, 0.08);
			box-shadow: inset 0px -1px 0px 0px rgba(240, 240, 240, 1);

			.count-item {
				margin-right: 32px;

				span:first-child {
					font-size  : 14px;
					color      : rgba(0, 0, 0, 0.65);
					font-weight: 400;
				}

				span:nth-child(n + 2) {
					font-size  : 14px;
					color      : #ff4d4f;
					font-weight: 600;
					margin-left: 8px;
				}
			}
		}

		.operate-container {
			padding        : 24px 0 12px 0;
			display        : flex;
			align-items    : center;
			justify-content: center;
		}
	}
}

.popover {
	:global {
		.ant-popover-inner-content {

			// width: 200px;
			// height: 200px;
			.ant-image-img {
				border-radius: 6px;
			}
		}
	}
}

// 添加换货商品
.disAddExchangeGoods {
	user-select  : none;
	padding      : 4px 15px;
	font-size    : 14px;
	border-radius: 2px;
	color        : #00000040;
	border       : 1px solid transparent;
	border-color : #d9d9d9;
	background   : #f5f5f5;
	cursor       : not-allowed;
}