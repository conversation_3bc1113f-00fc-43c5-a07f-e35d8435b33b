import React, { useEffect, useState, useRef } from "react";
import cs from 'classnames';
import { Spin, Button } from "antd";
import { observer } from 'mobx-react';
import event from '@/libs/event';
import SearchComp from "./SearchComp";
import TableComp from "./TableComp";
import { EVENT_BUS } from '@/pages/AfterSale/constants';
import styles from './index.module.scss';
import { avoidRepeatReq } from "@/utils/util";
import { TradeDictQueryDictApi } from '@/apis/trade/search';
import { lowVersionLock } from "@/components-biz/LowVersionControlModal";
import { PageNameControlEnum } from "@/components-biz/LowVersionControlModal/constant";
import { GetRefundGlobalConfigListApi, UpdateRefundGlobalConfigApi } from "@/apis/aftersale/trade";
import { AftersaleGlobalConfig } from "@/pages/AfterSale/TradeList/constants";
import ColSortSettingDrawer from "@/components/SearchTable/ColSortSettingDrawer";
import { RRFUND_SCAN_SINGLE_COLUMN, defaultSortList } from "./constants";
import userStore from '@/stores/user';
import videoMonitorStore from "@/stores/trade/videoMonitor";
import SelectShootingDeviceModal from "@/components-biz/SelectShootingDeviceModal";
import { local } from '@/libs/db';

// 缓存key
const SELECTED_SHOOTING_DEVICES_CACHE_KEY = 'SELECTED_SHOOTING_DEVICES';

const AftersaleScan = () => {
	const [configInfo, setConfigInfo] = useState({});
	const [sortColumnList, setSortColumnList] = useState([]);
	const [refundScanAutoCreateExchangeOrder, setRefundScanAutoCreateExchangeOrder] = useState(false);
	const [upStockConfig, setUpStockConfig] = useState(true);
	const searchComp = useRef();
	const tableComp = useRef();
	const [loading, setLoading] = useState(false);
	const [isShowChooseModel, setIsShowChooseModel] = useState(false); // 是否显示选择拍摄设备
	const [selectedShootingDevices, setSelectedShootingDevices] = useState<any[]>([]); // 选中的拍摄设备
	const [nvrDevices, setNvrDevices] = useState<any[]>([]); // nvr设备列表
	const [loadingTip, setLoadingTip] = useState("");
	const { isShowZeroStockVersion, userInfo } = userStore;
	
	const { whiteListSetting } = userInfo || {};
	const whiteListSettingObj = JSON.parse(whiteListSetting || '{}');
	const { videoMonitor } = whiteListSettingObj || {}; // 视频监控开关
	const { 
		checkKdzsPrintComponent, 
		checkKdzsPrintComponentStatus,
		isKdzsPrintComponent,
		sendMessage,
		disconnectWs,
		isSocketConnected,
		connectWs,
	} = videoMonitorStore;


	const getUpConfig = () => {
		avoidRepeatReq(GetRefundGlobalConfigListApi, {
			bizEnumList: [
				AftersaleGlobalConfig.售后扫描自动入库存配置,
				AftersaleGlobalConfig.售后扫描确认收货自动创建换货手工单配置,
				RRFUND_SCAN_SINGLE_COLUMN
			]
		}).then(res => {
			res.forEach(item => {
				if (item.biz == AftersaleGlobalConfig.售后扫描自动入库存配置) {
					setUpStockConfig(item?.value ? item?.value == '1' : true);
				}
				if (item.biz == AftersaleGlobalConfig.售后扫描确认收货自动创建换货手工单配置) {
					setRefundScanAutoCreateExchangeOrder(item?.value ? item?.value == '1' : false);
				}
			});
			const filterArr = ["productInfo", "salableItemDistributableStock"];
			let sortList = [...defaultSortList];
			const columnValue = res.find(item => item.biz == RRFUND_SCAN_SINGLE_COLUMN)?.value;
			if (columnValue) {
				try {
					const value = JSON.parse(columnValue);
					sortList = value;
				} catch (e) { console.log(e); }
			}
			if (isShowZeroStockVersion) {
				sortList = sortList.filter(item => !filterArr.includes(item.key));
			}
			setSortColumnList([
				...sortList
			]);
		});
	};
	// 获取用户开关配置
	const handleConfigInfo = () => {
		avoidRepeatReq(TradeDictQueryDictApi, {
			userDictEnum: 'AFTERSALE_SCAN_SET'
		}).then(res => {
			if (res.value) {
				try {
					const value = JSON.parse(res.value);
					setConfigInfo(value);
				} catch (e) { console.log(e); }
			}
		});
	};
	const onScanSetModalClose = (data) => {
		handleConfigInfo();
	};

	const onSearch = (v) => {
		setLoading(v);
		setLoadingTip(v ? "正在查询，请稍后..." : "");
	};

	const onReceive = (v) => {
		setLoading(v);
		setLoadingTip(v ? "正在处理，请稍后..." : "");
	};

	const onChangeUpStockConfig = async(e) => {
		let checked = e?.target?.checked;
		setUpStockConfig(checked);
		await UpdateRefundGlobalConfigApi([{
			biz: AftersaleGlobalConfig.售后扫描自动入库存配置,
			value: checked ? '1' : '0'
		}]);

	};
	// 确认收货后自动创建换货手工单
	const onExchangeConfigChange = async(e) => {
		let checked = e?.target?.checked;
		setRefundScanAutoCreateExchangeOrder(checked);
		await UpdateRefundGlobalConfigApi([{
			biz: AftersaleGlobalConfig.售后扫描确认收货自动创建换货手工单配置,
			value: checked ? '1' : '0'
		}]);

	};

	const onSaveSort = (type, newList) => {
		let newSortList = type == "reset" ? defaultSortList : newList;
		if (isShowZeroStockVersion) {
			const filterArr = ["productInfo", "salableItemDistributableStock"];
			newSortList = newSortList.filter(item => !filterArr.includes(item.key));
		}
		setSortColumnList([...newSortList]);
		UpdateRefundGlobalConfigApi([{
			biz: RRFUND_SCAN_SINGLE_COLUMN,
			value: JSON.stringify(newSortList)
		}]);
	};

	// 校验并更新缓存的设备信息
	const validateAndUpdateCachedDevices = async() => {
		try {
			// 获取缓存的设备信息
			const cachedDevices = local.getByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY) || [];
			
			if (cachedDevices.length === 0) {
				return;
			}

			// 获取最新的设备列表
			const currentDeviceList = await videoMonitorStore.getDeviceCameraList();
			// 过滤掉禁用的设备
			const enabledDevices = currentDeviceList.filter(item => item.status === 1);

			// 校验缓存中的设备是否还存在
			const validDevices = cachedDevices.filter((cachedDevice: any) => enabledDevices.find((device: any) => device.id === cachedDevice.id));

			// 校验是否属于同一个NVR
			if (validDevices.length > 0) {
				const nvrIds = validDevices.map((device: any) => device.deviceId);
				const uniqueNvrIds = Array.from(new Set(nvrIds));
				if (uniqueNvrIds.length > 1) {
					// 只保留第一个NVR下的设备
					const firstNvrId = nvrIds[0];
					const sameNvrDevices = validDevices.filter((device: any) => device.deviceId === firstNvrId);
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, sameNvrDevices);
					setSelectedShootingDevices(sameNvrDevices);
				} else {
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, validDevices);
					setSelectedShootingDevices(validDevices);
				}
			} else {
				local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, []);
				setSelectedShootingDevices([]);
			}

			// 获取nvr设备列表
			const nvrDevices = await videoMonitorStore.getDeviceList();
			setNvrDevices(nvrDevices);
		} catch (error) {
			console.error('校验缓存设备失败:', error);
			// 清除无效缓存
			local.removeByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY);
			setSelectedShootingDevices([]);
			setNvrDevices([]);
		}
	};

	// 处理选择拍摄设备确认
	const handleSelectShootingDeviceConfirm = (selectedDevices: any[]) => {
		// 缓存选中的设备
		local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, selectedDevices);
		setSelectedShootingDevices(selectedDevices);
		setIsShowChooseModel(false);
	};

	// 处理选择拍摄设备取消
	const handleSelectShootingDeviceCancel = () => {
		setIsShowChooseModel(false);
	};

	// 获取选中的设备名称显示文本
	const getSelectedDevicesText = () => {
		if (selectedShootingDevices.length === 0) {
			return "请选择拍摄设备";
		} else {
			return selectedShootingDevices.map(item => item.cameraName).join(",");
		}
	};

	const init = async() => {
		await checkKdzsPrintComponent(); // 检测快递助手ERP聚合控件
		connectWs(); // 连接ERP聚合控件
	};

	useEffect(() => {
		if (videoMonitor == 1 && configInfo?.videoMonitor == 1) {
			init(); // 初始化视频监控相关
		} else {
			disconnectWs(); // 断开ERP聚合控件连接
		}
	}, [videoMonitor, configInfo?.videoMonitor]);

	useEffect(() => {
		const fn = async() => {
			if (await lowVersionLock(PageNameControlEnum.售后扫描)) return;
			handleConfigInfo();
			event.on(EVENT_BUS.SCAN_SET_MODAL_CLOSED, onScanSetModalClose);
			
			// 校验并更新缓存的设备信息
			await validateAndUpdateCachedDevices();
		};
		fn();
		getUpConfig();
		
		return () => {
			event.off(EVENT_BUS.SCAN_SET_MODAL_CLOSED, onScanSetModalClose);
			disconnectWs();
		};
	}, []);

	return (
		<Spin spinning={ loading } tip={ loadingTip }>
			<div className={ cs(styles['afterSale-container'], 'r-flex') }>
				<div className={ styles['search-container'] }>
					<SearchComp
						sortColumnList={ sortColumnList }
						configInfo={ configInfo }
						upStockConfig={ upStockConfig }
						onChangeUpStockConfig={ onChangeUpStockConfig }
						refundScanAutoCreateExchangeOrder={ refundScanAutoCreateExchangeOrder }
						onExchangeConfigChange={ onExchangeConfigChange }
						cRef={ searchComp }
						tableInstance={ tableComp }
						onSearch={ onSearch }
						onReceive={ onReceive }
						isVideoMonitor={ videoMonitor == 1 && configInfo?.videoMonitor == 1 }
						isIpcModel={ selectedShootingDevices?.length > 0 }
						nvrDevices={ nvrDevices }
						selectedShootingDevices={ selectedShootingDevices }
					/>
				</div>
				<div className={ styles['table-container'] }>
					<div className="r-ta-r r-mb-16">
						{
							videoMonitor == 1 && configInfo?.videoMonitor == 1 ? (
								<Button 
									type="primary" 
									className="r-mr-16" 
									style={ { backgroundColor: "#1890ff", borderColor: "#1890ff" } }
									onClick={ () => {
										setIsShowChooseModel(true);
									} }
								>
									{getSelectedDevicesText()}
								</Button>
							) : null
						}
						
						<ColSortSettingDrawer onSaveSort={ onSaveSort } colSortList={ sortColumnList } />
					</div>
					<TableComp sortColumnList={ sortColumnList } configInfo={ configInfo } cRef={ tableComp } searchInstance={ searchComp } />
				</div>
			</div>
			
			{/* 选择拍摄设备弹框 */}
			<SelectShootingDeviceModal
				visible={ isShowChooseModel }
				onCancel={ handleSelectShootingDeviceCancel }
				onConfirm={ handleSelectShootingDeviceConfirm }
				defaultSelectedDevices={ selectedShootingDevices }
			/>
		</Spin>

	);
};

export default observer(AftersaleScan);
