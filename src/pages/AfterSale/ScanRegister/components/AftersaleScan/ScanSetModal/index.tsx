import React, { useEffect, useState } from 'react';
import { Checkbox, Modal, Spin, Form, Select, Radio, Button, Popover } from 'antd';
import { observer } from 'mobx-react';
import styles from './index.module.scss';
import event from '@/libs/event';
import { TradeDictInsertDictApi, TradeDictQueryDictApi } from '@/apis/trade/search';
import { EVENT_BUS } from '@/pages/AfterSale/constants';
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import videoMonitorStore from "@/stores/trade/videoMonitor";
import scanPrintStore from "@/stores/trade/scanPrint";
import useGetState from '@/utils/hooks/useGetState';
import { avoidRepeatReq } from "@/utils/util";
import { local } from '@/libs/db';
import { LOCAL_SETTING } from '../../../constants';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import Icon from '@/components/Icon';
import message from "@/components/message";
import ControlsDownModal from '@/pages/Trade/components/ControlsDownModal';
import PrintCenter from '@/print/index';

export enum MailRepeatedInterceptionEnum {
	开启 = 1,
	关闭 = 2
}

export enum ConfirmDelRecordEnum{
	开启 = 1,
	关闭 = 2
}

const ScanSetModal = (props) => {
	const { visible } = props;
	const { userInfo } = userStore;
	const { whiteListSetting } = userInfo || {};
	const whiteListSettingObj = JSON.parse(whiteListSetting || '{}');
	const { videoMonitor } = whiteListSettingObj || {}; // 视频监控开关
	const {
		isSocketConnected,
		checkKdzsPrintComponent,
		connectWs,
		disconnectWs,
		clearMessageCallbacks,
	} = videoMonitorStore;

	const thePrinterList = scanPrintStore.printersList;
	const theThdXbqTempList = tradeStore.thdXbqTempList;
	const [form] = Form.useForm();
	const [originConfig, setOriginConfig] = useState<any>({});
	// const [autoFinish, setAutoFinish] = useState(false);
	const [autoPrintLabel, setAutoPrintLabel] = useState(false);
	const [spaceKeyReceive, setSpaceKeyReceive] = useState(false);
	const [queryLoading, setQueryLoading] = useState(false);
	const [insertLoading, setInsertLoading] = useState(false);
	const [receiveNumFill, setReceiveNumFill] = useState(false);
	const [mailRepeatedInterception, setMailRepeatedInterception] = useState(false);
	const [videoMonitorState, setVideoMonitorState] = useState(false); // 售后扫描视频监控状态
	const [confirmDelRecord, setConfirmDelRecord] = useState(false);
	const [noAutoSwitchCondition, setNoAutoSwitchCondition] = useState(local.get(LOCAL_SETTING)?.noAutoSwitchCondition);
	const [modalVisible, setModalVisible] = useState<boolean>(visible);
	const [printerList, setPrinterList, getPrinterList] = useGetState(thePrinterList);
	const [thdXbqTempList, setThdXbqTempList, getThdXbqTempList] = useGetState(theThdXbqTempList);
	const [memoFlagConfig, setMemoFlagConfig] = useState([]);
	const [isShowControlsDownModal, setIsShowControlsDownModal] = useState(false); // 是否显示下载控件


	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `售后扫描登记-售后扫描设置: 【 ${dataType} 】`,
			data: {
				...data
			}
		});
	};

	const handleConfigInfo = (value) => {
		const {
			autoPrintLabels,
			spacebarSettingsReceipt,
			confirmNumFillUpDefault,
			mailRepeatedInterception,
			// autoFinish,
			memoFlagConfig = "",
			defaultReturnLabelTemplate = undefined,
			defaultPrinter = undefined } = value;
		setOriginConfig(value);
		setAutoPrintLabel(autoPrintLabels === 1);
		setSpaceKeyReceive(spacebarSettingsReceipt === 1);
		setReceiveNumFill(confirmNumFillUpDefault === 1);
		setMailRepeatedInterception(mailRepeatedInterception === MailRepeatedInterceptionEnum.开启);
		setVideoMonitorState(value.videoMonitor === 1 && videoMonitor == 1); // 售后扫描视频监控状态 = 白名单开关+售后扫描视频监控开关
		setMemoFlagConfig(memoFlagConfig.split(","));
		setTimeout(() => {
			const printerList = getPrinterList();
			const thdXbqTempList = getThdXbqTempList().map(i => i.Mode_ListShowId + '');
			form.setFieldsValue({
				printer: printerList.includes(defaultPrinter || defaultPrinter + '') ? defaultPrinter : undefined,
				template: thdXbqTempList.includes(defaultReturnLabelTemplate) ? defaultReturnLabelTemplate : undefined
			});
		});
	};


	const getConfigInfo = async() => {
		setQueryLoading(true);
		let p1 = TradeDictQueryDictApi({
			userDictEnum: 'AFTERSALE_SCAN_SET'
		});
		let p2 = TradeDictQueryDictApi({
			userDictEnum: 'AFTERSALE_SCAN_SET_MAIN'
		});
		try {
			const allRes = await Promise.allSettled([p1, p2]);
			const [res1, res2] = allRes.map(i => i?.value?.value);
			if (res1) {
				const value = JSON.parse(res1);
				handleConfigInfo(value);
			}
			if (res2) {
				const value = JSON.parse(res2);
				setConfirmDelRecord(value?.confirmDelRecord == ConfirmDelRecordEnum.开启);
			}
		} catch (error) {
			console.log(error);
		}
		setQueryLoading(false);
	};
	const onOk = () => {
		form.validateFields().then(async(formValue) => {
			local.set(LOCAL_SETTING, {
				...local.get(LOCAL_SETTING),
				noAutoSwitchCondition,
			});
			const value = {
				...originConfig,
				memoFlagConfig: memoFlagConfig.join(','),
				confirmNumFillUpDefault: receiveNumFill ? 1 : 2,
				autoPrintLabels: autoPrintLabel ? 1 : 2,
				spacebarSettingsReceipt: spaceKeyReceive ? 1 : 2,
				// autoFinish: autoFinish ? 1 : 2,
				noAutoSwitchCondition: noAutoSwitchCondition ? 1 : 0,
				mailRepeatedInterception: mailRepeatedInterception ? MailRepeatedInterceptionEnum.开启 : MailRepeatedInterceptionEnum.关闭,
				videoMonitor: videoMonitorState ? 1 : 0,
				defaultPrinter: formValue.printer,
				defaultReturnLabelTemplate: formValue.template
			};
			const params = {
				userDictEnum: 'AFTERSALE_SCAN_SET',
				value: JSON.stringify(value)
			};
			const params2 = {
				userDictEnum: 'AFTERSALE_SCAN_SET_MAIN',
				value: JSON.stringify({ confirmDelRecord: confirmDelRecord ? ConfirmDelRecordEnum.开启 : ConfirmDelRecordEnum.关闭 })
			};
			setInsertLoading(true);
			try {
				await TradeDictInsertDictApi(params);
				mailRepeatedInterception && sendPoint(Pointer.快递重复收货拦截_勾选后保存);

				await TradeDictInsertDictApi(params2);
				confirmDelRecord && sendPoint(Pointer.确认收货后清除批量登记记录_勾选后保存);
			} catch (error) {
				console.log(error);
			}
			setInsertLoading(false);
			setModalVisible(false);

			// 记录视频监控开关
			if (
				(!originConfig.videoMonitor && originConfig.videoMonitor !== 0)
				|| originConfig.videoMonitor !== (videoMonitorState ? 1 : 0)
			) {
				customLogPost(`视频监控开关${videoMonitorState ? '开启' : '关闭'}`, { subUserId: userInfo?.subUserId, userId: userInfo?.userId });
			}
		});

	};
	const options = [
		{ label: '有留言提醒', value: 'message' },
		{ label: '有备注提醒', value: 'memo' },
		{ label: '有旗帜提醒', value: 'flag' }
	];

	const onCancel = () => {
		setModalVisible(false);
		setNoAutoSwitchCondition(local.get(LOCAL_SETTING)?.noAutoSwitchCondition);
	};

	const afterClose = () => {
		event.emit(EVENT_BUS.SCAN_SET_MODAL_CLOSED);
	};

	const autoSwitchOnChange = (e) => {
		setNoAutoSwitchCondition(e.target.checked);
	};

	const memoFlagOnChange = (v) => {
		setMemoFlagConfig(v);
	};

	const init = async() => {
		await checkKdzsPrintComponent(); // 检测快递助手ERP聚合控件
		connectWs(); // 连接ERP聚合控件
	};

	const handleReconnect = async() => {
		console.log('重新连接');
		init();
	};
	const handleDownload = () => {
		console.log('下载控件');
		// setIsShowControlsDownModal(true);
		const downloadUrl = (tradeStore.controlDonloadData || []).find(it => it.name === "快递助手ERP聚合打印控件")?.controlsArr[0]?.downLink;
		console.log('%c [ downloadUrl ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', tradeStore.controlDonloadData, downloadUrl);
		if (downloadUrl) {
			window.location.href = downloadUrl;
		} else {
			message.warning('请联系客服获取下载地址');
		}
	};

	useEffect(() => {
		setPrinterList(thePrinterList);
	}, [thePrinterList]);

	useEffect(() => {
		setThdXbqTempList(theThdXbqTempList);
	}, [theThdXbqTempList]);

	useEffect(() => {
		setModalVisible(visible);
	}, [visible]);

	useEffect(() => {
		if (modalVisible) {
			getConfigInfo();
		}
	}, [modalVisible]);
	return (
		<>
			<Modal
				title="售后扫描设置"
				afterClose={ afterClose }
				visible={ modalVisible }
				onCancel={ onCancel }
				onOk={ onOk }
				maskClosable={ false }
				className={ styles.modal }
				confirmLoading={ insertLoading }
			>
				<Spin spinning={ queryLoading }>
					<div className={ styles.container }>
						<div className={ styles['config-container'] }>
							<div className="config-title">确认收货设置</div>
							<div className="config-item">
								<div className="config-label">
									<Checkbox
										checked={ spaceKeyReceive }
										onChange={ () => setSpaceKeyReceive(!spaceKeyReceive) }
									>
										空格键快速收货
									</Checkbox>
								</div>
							</div>
							{/* <div className="config-item">
							<div className="config-label">
								<Checkbox
									checked={ autoFinish }
									onChange={ () => setAutoFinish(!autoFinish) }
								>
									确认收货后自动完成
								</Checkbox>
							</div>
						</div> */}
						</div>
						<div className={ styles['config-container'] }>
							<div className="config-title">打印设置</div>
							<div className="config-item">
								<div className="config-label">
									<Checkbox
										checked={ autoPrintLabel }
										onChange={ () => setAutoPrintLabel(!autoPrintLabel) }
									>
										确认收货会后自动打印退货标签
									</Checkbox>
								</div>
							</div>
							{
								autoPrintLabel ? (
									<div className={ styles['print-form'] }>
										<Form
											labelAlign="left"
											form={ form }
										>
											<Form.Item
												label="打印机"
												name="printer"
												rules={ [{ required: true, message: '请选择打印机' }] }
											>
												<Select getPopupContainer={ (e) => e.parentElement } size="small" placeholder="请选择打印机">
													{printerList.map(printer => (
														<Select.Option key={ printer } value={ `${printer}` }>{printer}</Select.Option>
													))}
												</Select>
											</Form.Item>
											<Form.Item
												label="标签模板"
												name="template"
												rules={ [{ required: true, message: '请选择模板' }] }

											>
												<Select getPopupContainer={ (e) => e.parentElement } size="small" placeholder="请选择退货标签模板">
													{thdXbqTempList.map(item => (
														<Select.Option value={ `${item.Mode_ListShowId}` } key={ item.Mode_ListShowId } >{item.ExcodeName}</Select.Option>
													))}
												</Select>
											</Form.Item>
										</Form>
									</div>
								) : ''
							}

						</div>
						<div className={ styles['config-container'] }>
							<div className="config-title">个性化设置</div>
							<div className="config-item">
								<div className="config-label">
									<Checkbox
										checked={ receiveNumFill }
										onChange={ () => setReceiveNumFill(!receiveNumFill) }
									>
										收货数量默认填充（填充值 = 申请数量 - 已退数量）
									</Checkbox>
								</div>
							</div>
							<div className="config-item">
								<div className="config-label">
									<Checkbox
										checked={ noAutoSwitchCondition }
										onChange={ autoSwitchOnChange }
									>
										售后扫描查询匹配条件不自动切换
									</Checkbox>
								</div>
							</div>
							<div className="memo-flag-item">
								<div>匹配订单留言备注提醒设置</div>
								<div className="config-item">
									<div className="config-label">
										<Checkbox.Group options={ options } value={ memoFlagConfig } onChange={ memoFlagOnChange } />
									</div>
								</div>
							</div>
							<div className="config-item">
								<div className="config-label">
									<Checkbox
										checked={ mailRepeatedInterception }
										onChange={ (e) => {
											setMailRepeatedInterception(e.target.checked);
										} }
									>
										快递重复收货拦截
									</Checkbox>
								</div>
							</div>
							<div className="config-item">
								<div className="config-label">
									<Checkbox
										checked={ confirmDelRecord }
										onChange={ (e) => {
											setConfirmDelRecord(e.target.checked);
										} }
									>
										确认收货后清除批量登记记录
									</Checkbox>
								</div>
							</div>

							<div className="config-item">
								<div className="config-label">
									视频监控：
									<Radio.Group
										value={ videoMonitorState ? 1 : 0 }
										style={ { marginLeft: 8 } }
										onChange={ e => {
											if (e.target.value === 1 && videoMonitor !== 1) {
											// 打开的时候如果白名单未开通，弹出提示，阻止切换
												message.warning("视频监控需增购后使用，具体增购费用请联系销售增购后使用");
												return;
											}
											setVideoMonitorState(e.target.value === 1);
										} }
									>
										<Radio value={ 0 }>关闭</Radio>
										<Radio value={ 1 } style={ { marginRight: 0 } }>开启</Radio>
									</Radio.Group>
									<Popover
										content={ (
											<div>
												目前支持USB监控摄像头（多个品牌）以及IPC监控设备（海康威视等品牌），具体详见<a href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/glroqagd4kb2gwx8?singleDoc#" target="_blank" rel="noreferrer" style={ { color: "#1890ff" } }>使用手册</a>
											</div>
										) }
										overlayStyle={ { maxWidth: 350 } }
									>
										<span>
											<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
										</span>
									</Popover>
								</div>
							</div>

							{
								videoMonitorState ? (
									<div className="config-item">
										<div className="config-label r-flex r-ai-c">
											控件连接状态：
											{
												isSocketConnected ? (
													<span className="r-c-success">已连接</span>
												) : (
													<>
														<span style={ { color: "rgba(0, 0, 0, 0.45)" } }>未连接</span>
														<Button
															type="link"
															style={ { padding: 0, marginLeft: 8, color: "#1890ff" } }
															onClick={ () => {
																handleReconnect();
															} }
														>
															重新连接
														</Button>
														<Button
															type="link"
															style={ { padding: 0, marginLeft: 8, color: "#1890ff" } }
															onClick={ () => {
																handleDownload();
															} }
														>
															下载控件
														</Button>
													</>
												)
											}
										</div>
									</div>
								) : ''
							}

						</div>
					</div>
				</Spin>

			</Modal>
			{/* <ControlsDownModal showControlsDownModal={ isShowControlsDownModal } setShowControlsDownModal={ setIsShowControlsDownModal } /> */}
		</>
	);

};
export default observer(ScanSetModal);
