import { Button, Checkbox, Form, Modal, Popover, Select } from "antd";
import { QuestionCircleOutlined, CopyOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import React, { useState, useMemo, useRef, useEffect, useImperativeHandle } from "react";
import dayjs from 'dayjs';
import _, { debounce } from 'lodash';
import cs from 'classnames';
import { Link, useHistory } from "react-router-dom";
import { platform } from "os";
import message from "@/components/message";
import Input from '@/components/Input/InputSearch';
import styles from './index.module.scss';
import { getExCompanyAll } from "@/apis/user";
import useGetState from '@/utils/hooks/useGetState';
import { refundReasonNames, RefundSystemTypeEnum, afterSaleTypeText } from '@/pages/AfterSale/TradeList/utils';
import { TradeDictInsertDictApi, TradeDictQueryDictApi } from '@/apis/trade/search';
import {
	SelectReqSelectRefundScanV2Api,
	RefundScanConfirmApi,
	RefundScanConfirmAgreeFeeApi,
	GetLogisticsCompanyInfoByYdNoApi
} from "@/apis/aftersale/scanRegister";
import { RefundAutoStrategyJudgeOpenAutoStrategyApi } from '@/apis/aftersale/automation';
import { CheckAfterAuthorityApi } from '@/apis/aftersale/trade';
import { SCAN_REGISTER_FORM_SEARCH_SEARCH_TYPE, EVENT_BUS, SearchTypeObj } from '@/pages/AfterSale/constants';
import ScanSetModal from "../ScanSetModal";
import event from '@/libs/event';
import { getAllPlats, getShopsByPlat } from "@/components-biz/ShopListSelect/shopListUtils";
import { PLAT_MAP, PLAT_HAND, PLAT_OTHER, PLAT_PDD, PLAT_TB, PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_XHS, PLAT_C2M, PLAT_JD } from "@/constants";
import { playAudio } from "@/pages/Trade/ScanPrint/utils";
import voiceReceiveSuccess from '@/assets/mp3/收货成功.mp3';
import voiceReceived from '@/assets/mp3/包裹已收货.mp3';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { copyToPaste, trimObject } from '@/utils';
import { PolicyTypeEnum } from '@/pages/AfterSale/Automation/constants';
import userStore from "@/stores/user";
import { BatchScanStatus, LOCAL_SETTING } from "../../../constants";
import { local } from "@/libs/db";
import UnpackRegisterModal from "../UnpackRegisterModal";
import Icon from "@/components/Icon";
import videoMonitorStore from "@/stores/trade/videoMonitor";
import { getToken } from "@/utils/token";
import { tokens } from '@/utils/request';

interface Props {
	configInfo:{[K:string]:any};
	upStockConfig?:any;
	onChangeUpStockConfig?:(v:any)=>void;
	refundScanAutoCreateExchangeOrder?:any;
	onExchangeConfigChange?:(v:any)=>void;
	cRef:React.MutableRefObject<any>;
	onSearch?: (v:boolean)=>void;
	onReceive?: (v:boolean)=>void;
	tableInstance?: React.MutableRefObject<any>;
	sortColumnList?: any[];
	isVideoMonitor?: boolean; // 视频监控开关
	isIpcModel?: boolean; // 是否是IPC模式
	nvrDevices?: any[]; // nvr设备列表
	selectedShootingDevices?: any[]; // 选中的拍摄设备
}

const buildTypeEnumObj = {
	'1': 'MATCH_REFUND',
	'2': 'MATCH_ORDER',
	'3': 'HAND_CREATE'
};
// 目前支持的平台
const VALID_PLATFORM = [PLAT_PDD, PLAT_TB, PLAT_FXG, PLAT_OTHER, PLAT_KS, PLAT_HAND, PLAT_SPH, PLAT_XHS, PLAT_C2M, PLAT_JD];
let unpackRegisterParams = {};
let curUpStockConfig;
let curRefundScanAutoCreateExchangeOrder;

const SearchComp = (props:Props) => {
	const { isShowZeroStockVersion } = userStore;
	const { configInfo, cRef, onSearch, onReceive, tableInstance, sortColumnList, isVideoMonitor, isIpcModel, nvrDevices, selectedShootingDevices } = props;
	const history = useHistory();
	const [platformList, setPlatformList] = useState([]);
	const [needBindOrder, setNeedBindOrder, getNeedBindOrder] = useGetState(false);
	const [selectedRows, setSelectedRows, getSelectedRows] = useGetState([]);
	const [formData, setFormData, getFormData] = useGetState({});
	const [ignoreException, setIgnoreException, getIgnoreException] = useGetState(false);
	const [relateTid, setRelateTid, getRelateTid] = useGetState('');
	const [allowSpaceKeyReceive, setAllowSpaceKeyReceive, getAllowSpaceKeyReceive] = useGetState(false);
	const [originConfig, setOriginConfig] = useState({});
	const [shopList, setShopList, getShopList] = useGetState([]);
	const [searchForm] = Form.useForm();
	const [orderForm] = Form.useForm();
	const [buildType, setBuildType, getBuildType] = useGetState(3);
	const [scanTime, setScanTime, getScanTime] = useGetState('');
	const [selectedExtraInfo, setSelectedExtraInfo, getSelectedExtraInfo] = useGetState({});
	const [scanSetModalVisible, setScanSetModalVisible] = useState(false);
	const [reqScanResLoading, setReqScanResLoading, getReqScanResLoading] = useGetState(false);
	// confirmLoading现在不作为确认收货按钮的loading，但是依然不能删除，因为空格收货时依然要依赖confirmLoading做判断
	const [confirmLoading, setConfirmLoading, getConfirmLoading] = useGetState(false);
	const [agreeFeeLoading, setAgreeFeeLoading, getAgreeFeeLoading] = useGetState(false); // 售后权限校验和退款流程
	const [searchTypeOptions, setSearchTypeOptions] = useState([]);
	const [textMessageCode, setTextMessageCode, getTextMessageCode] = useGetState('');
	const searchInput = useRef(null);
	const [autoRefund, setAutoRefund, getAutoRefund] = useGetState(false);
	const [autoFinish, setAutoFinish, getAutoFinish] = useGetState(1);
	const [expressCompanyList, setExpressCompanyList, getExpressCompanyList] = useGetState([]);
	const [refundScanConfirmRes, setRefundScanConfirmRes, getRefundScanConfirmRes] = useGetState({});
	const [companyInfo, setCompanyInfo, getCompanyInfo] = useGetState({});
	const [unpackRegisterModalVisible, setUnpackRegisterModalVisible] = useState(false);
	const [currentOrder, setCurrentOrder] = useState([]);
	const [scanId, setScanRecordId, getScanRecordId] = useGetState<any>(''); // 没匹配数据时候的扫描记录id
	const [aftersaleScanQueryType, setAftersaleScanQueryType] = useState(local.get("aftersaleScanQueryTypeDefault") || {
		aftersaleScanQueryType: "sid",
	});

	const latestPropsRef = useRef({ isVideoMonitor, isIpcModel });
	useEffect(() => {
		latestPropsRef.current = { isVideoMonitor, isIpcModel };
	}, [isVideoMonitor, isIpcModel]);

	const handleSetDefault = (fieldName, value) => {
		setAftersaleScanQueryType(prev => {
			local.set('aftersaleScanQueryTypeDefault', {
				...prev,
				[fieldName]: value
			});
			return {
				...prev,
				[fieldName]: value
			};
		});
	};


	const upStockConfig = props.upStockConfig;
	curUpStockConfig = upStockConfig;
	curRefundScanAutoCreateExchangeOrder = props.refundScanAutoCreateExchangeOrder;

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `售后扫描登记-售后扫描: 【 ${dataType} 】`,
			data: {
				configInfo, // noInfoItemBindOrder, autoRefund, spacebarSettingsReceipt, autoFinish
				curUpStockConfig,
				curRefundScanAutoCreateExchangeOrder,
				...data
			}
		});
	};

	const getSearchTypeOptions = useMemo(() => {
		return SCAN_REGISTER_FORM_SEARCH_SEARCH_TYPE.options.map(item => {
			return (
				<Select.Option key={ item.value } label={ item.label } value={ item.value }>
					<div className="r-flex r-ai-c r-jc-sb">
						<div>{item.label}</div>
						<div>
							{
								aftersaleScanQueryType?.aftersaleScanQueryType === item.value
									? (
										<Icon
											className="r-c-warning"
											type="guding"
										/>
									)
									: (
										<Icon
											className="r-c-999"
											type="weiguding"
											onClick={ (e) => {
												e.stopPropagation();
												handleSetDefault("aftersaleScanQueryType", item.value);
											} }
										/>
									)
							}

						</div>
					</div>
				</Select.Option>
			);
		});
	}, [aftersaleScanQueryType]);
	
	useEffect(() => {
		event.emit(EVENT_BUS.SEARCH_FORM_ONSEARCH, reqScanResLoading);
	}, [reqScanResLoading]);

	const updateOrderFormData = () => {
		setTimeout(() => {
			const orderFormData = orderForm.getFieldsValue();
			event.emit(EVENT_BUS.UPDATE_ORDER_FORM_DATA, orderFormData);
		});
	};

	const getConfirmReceiveParams = () => {
		const { sid, companyCode } = orderForm.getFieldsValue();
		const companyObj = getExpressCompanyList().find(i => i.exCode === companyCode);
		/**
		 * 确认收货的数据入参处理比较复杂，潜规则很多
		 */
		const formData = getFormData();
		const buildType = getBuildType();
		const selectedRows = getSelectedRows();
		const ignoreException = getIgnoreException();
		const orderInfo = {
			buildType: buildTypeEnumObj[buildType],
			sid,
			companyName: companyObj?.exName,
			companyCode,
			platform: '',
			sellerId: '',
			sellerNick: '',
			buyerNick: '',
			receiverName: '',
			receiverMobile: '',
			refundReason: '',
			autoUpStockAfterConfirmReceiverGoods: curUpStockConfig,
			refundScanAutoCreateExchangeOrder: curRefundScanAutoCreateExchangeOrder,
			isAutoAgreeRefundFee: getAutoRefund(),
			autoFinish: getAutoFinish(),
			relateTid: '',
			ignoreException, // 是否忽略异常0-否1-是
			refundScanTime: getScanTime() || dayjs().format("YYYY-MM-DD HH:mm:ss"),
		};
		// 如果当前匹配到的是销售单，并且用户填写了
		if (buildType === 2) {
			orderInfo.sid = sid;
			orderInfo.companyCode = companyCode;
			orderInfo.companyName = companyObj?.exName;
		}
		if (buildType !== 3) {
			const orderConfirmReqDTOList = [];
			const selectedOrderRows = selectedRows.filter(record => record.rowType === 'order');
			selectedOrderRows.forEach(orderRow => {
				const { originData } = orderRow;
				const orderConfirmReqDTOItem = {
					platform: originData.platform,
					buyerNick: originData.buyerNick,
					sellerId: originData.sellerId,
					sellerNick: originData.sellerNick,
					tid: originData.tid,
					refundId: originData.refundId,
					refundInfoId: originData.id,
					afterSaleType: originData.afterSaleType,
					erpMemo: originData.erpMemo,
					orderItemConfirmReqs: [],
					refundSystemType: originData.refundSystemType,
					refundItemType: originData.refundItemType,
					sysRefundItemType: originData.sysRefundItemType,
					scanRecordId: originData.scanRecordId,
				};
				selectedRows.forEach(selectedRow => {
					if (selectedRow.belongOrderRowKey === orderRow.key) {
						const rowOriginData = selectedRow.originData;
						const orderItemConfirmReqItem = {
							oid: selectedRow.oid,
							refundFee: formData[`refundFee_${selectedRow.key}`],
							confirmNum: formData[`receiveNum_${selectedRow.key}`],
							applyRefundNum: selectedRow.applyRefundNum,
							refundItemId: rowOriginData.itemRefundItemRecordId,
							numIid: rowOriginData.numIid,
							skuId: rowOriginData.skuId,
							picPath: rowOriginData.picUrl,
							title: rowOriginData.title,
							skuProperties: rowOriginData.skuName,
							itemAlias: rowOriginData.itemAlias,
							itemOuterId: rowOriginData.outerId,
							itemOuterSkuId: rowOriginData.outerSkuId,
							sysRefundItemId: rowOriginData.sysRefundItemRecordId,
							sysItemId: rowOriginData.sysItemId,
							sysSkuId: rowOriginData.sysSkuId,
							picUrl: rowOriginData.sysPicUrl,
							itemTitle: rowOriginData.sysItemName,
							skuInfo: rowOriginData.sysSkuName,
							sysSkuAlias: rowOriginData.sysSkuAlias,
							sysItemAlias: rowOriginData.sysItemAlias,
							outerId: rowOriginData.sysOuterId,
							outerSkuId: rowOriginData.sysOuterSkuId,
							itemNo: rowOriginData.sysItemNo,
							isCombination: rowOriginData.isCombination,
							refundItemType: rowOriginData.refundItemType ?? rowOriginData.sysRefundItemType,
							sysRefundItemType: rowOriginData.sysRefundItemType ?? rowOriginData.refundItemType,
						};
						orderConfirmReqDTOItem.orderItemConfirmReqs.push(orderItemConfirmReqItem);
						/**
						 * 如果是仅退款的售后单，需要copy一份"平台商品对应的货品信息"，然后组装给后端，仅限库存版
						 * 但是有一种情况不需要映射：就是售后单是异常订单并且订单中的商品没有映射的本地货品的时候。
						 * 原因是异常的售后单正常收货的时候，我们一定会提示“需要添加本地货品才能继续收货”，所以用户添加过了本地货品以后
						 * 我们就不需要再映射一份平台商品了
						 */

						// 校验售后单类型和工单类型
						const validType = originData.afterSaleType == afterSaleTypeText.仅退款 && originData.refundSystemType == RefundSystemTypeEnum.仅退款;
						// 校验是不是异常售后单
						const isExceptionRefund = originData.exceptionType === 1;
						// 当前商品是不是没有本地货品快照
						const noSystemItemInfo = isShowZeroStockVersion ? false : !rowOriginData.sysRefundItemRecordId;

						const allowCopy = validType && !ignoreException && !(isExceptionRefund && noSystemItemInfo);
						// console.log('allowCopy', rowOriginData, orderItemConfirmReqItem);
						// console.log('allowCopy', originData, validType, 1, !ignoreException, 2, !(isExceptionRefund && noSystemItemInfo));

						if (allowCopy) { // 如果忽略了异常，不需要执行此操作
							if (rowOriginData.refundItemSource == 0) {
								if (isShowZeroStockVersion) {
									const itemToSystemItem = {
										oid: rowOriginData.oid,
										refundFee: formData[`refundFee_${selectedRow.key}`],
										confirmNum: formData[`receiveNum_${selectedRow.key}`],
										applyRefundNum: selectedRow.applyRefundNum,
										...orderItemConfirmReqItem,
										refundItemId: null,
										sysItemAlias: null,
										sysItemId: null,
										sysSkuId: null,
										sysSkuAlias: null,
										sysRefundItemId: null,
										originalRefundItemId: rowOriginData.itemRefundItemRecordId,
									};
									orderConfirmReqDTOItem.orderItemConfirmReqs.push(itemToSystemItem);
								} else {
									const itemToSystemItem = {
										oid: rowOriginData.oid,
										refundFee: formData[`refundFee_${selectedRow.key}`],
										confirmNum: formData[`receiveNum_${selectedRow.key}`],
										applyRefundNum: selectedRow.applyRefundNum,
										refundItemId: null,
										originalSysRefundItemId: rowOriginData.sysRefundItemRecordId,
										numIid: null,
										skuId: null,
										picPath: null,
										title: null,
										skuProperties: rowOriginData.skuName,
										itemAlias: null,
										itemOuterId: null,
										itemOuterSkuId: null,
										sysRefundItemId: null,
										sysItemId: rowOriginData.sysItemId,
										sysSkuId: rowOriginData.sysSkuId,
										picUrl: rowOriginData.sysPicUrl,
										itemTitle: rowOriginData.sysTitle,
										skuInfo: rowOriginData.sysSkuName,
										sysSkuAlias: null,
										sysItemAlias: rowOriginData.sysItemAlias,
										outerId: rowOriginData.sysOuterId,
										outerSkuId: rowOriginData.sysOuterSkuId,
										itemNo: rowOriginData.sysItemNo,
										isCombination: rowOriginData.isCombination,
										refundItemType: rowOriginData.refundItemType ?? rowOriginData.sysRefundItemType,
										sysRefundItemType: rowOriginData.sysRefundItemType ?? rowOriginData.refundItemType,
									};
									orderConfirmReqDTOItem.orderItemConfirmReqs.push(itemToSystemItem);
								}
							}
						}
						console.log('allowCopy', allowCopy, orderConfirmReqDTOItem);
					}

				});
				orderConfirmReqDTOList.push(orderConfirmReqDTOItem);
			});
			const params = {
				...orderInfo,
				orderConfirmReqDTOList,
			};
			return params;
		} else {
			const { receiverMobile, receiverName, companyCode, buyerNick, platform, sellerId, sid, refundReason } = orderForm.getFieldsValue();
			const companyObj = getExpressCompanyList().find(i => i.exCode === companyCode);
			const sellerObj = getShopList().find(i => i.sellerId === sellerId);
			const handItemConfirmReqDTOList = [];
			const theOrderInfo = {
				...orderInfo,
				receiverMobile,
				companyCode,
				companyName: companyObj?.exName,
				buyerNick,
				platform,
				sellerId,
				sellerNick: sellerObj?.sellerNick,
				sid,
				receiverName,
				refundReason,
				relateTid: getRelateTid()
			};
			selectedRows.forEach(selectedRow => {
				console.log('selectedRowselectedRow', selectedRow);
				if (selectedRow && selectedRow.rowType === 'good') {
					const rowOriginData = selectedRow.originData;
					const handItemConfirmReqDTOItem = {
						oid: '',
						applyRefundNum: selectedRow.applyRefundNum,
						refundFee: formData[`refundFee_${selectedRow.key}`],
						confirmNum: formData[`receiveNum_${selectedRow.key}`],
						numIid: rowOriginData.numIid,
						skuId: rowOriginData.skuId,
						picPath: rowOriginData.picUrl,
						title: rowOriginData.title,
						skuProperties: rowOriginData.skuName,
						itemAlias: rowOriginData.itemAlias,
						itemOuterId: rowOriginData.outerId,
						itemOuterSkuId: rowOriginData.outerSkuId,
						sysRefundItemId: rowOriginData.sysRefundItemRecordId,
						sysItemId: rowOriginData.sysItemId,
						sysSkuId: rowOriginData.sysSkuId,
						picUrl: rowOriginData.sysPicUrl,
						itemTitle: rowOriginData.sysItemName,
						skuInfo: rowOriginData.sysSkuName,
						sysItemAlias: rowOriginData.sysItemAlias,
						outerId: rowOriginData.sysOuterId,
						outerSkuId: rowOriginData.sysOuterSkuId,
						itemNo: rowOriginData.sysItemNo,
						isCombination: rowOriginData.isCombination,
						refundItemType: rowOriginData.refundItemType ?? rowOriginData.sysRefundItemType,
						sysRefundItemType: rowOriginData.sysRefundItemType ?? rowOriginData.refundItemType,
					};

					handItemConfirmReqDTOList.push(handItemConfirmReqDTOItem);
				}
			});
			const params = {
				...theOrderInfo,
				orderConfirmReqDTOList: [{
					handItemConfirmReqDTOList,
					scanRecordId: getScanRecordId(), // 手工创建传入查询时候的查询记录id
				}],
			};
			return params;
		}
	};

	// 收货成功
	const onReceiveSuccess = () => {
		message.success('收货成功');
		searchForm.resetFields();
		setRelateTid('');
		playAudio(voiceReceiveSuccess);
		searchInput?.current?.focus();

		console.time('EVENT_BUS.RECEIVE_SUCCESS');
		event.emit(EVENT_BUS.RECEIVE_SUCCESS, getRefundScanConfirmRes());
	};

	// 收货流程全部结束且表格中展示数据也处理完毕
	const onConfirmReceiveSuccessFinally = () => {
		console.timeEnd('EVENT_BUS.CONFIRM_RECEIVE_SUCCESS_FINALLY');
		setConfirmLoading(false);
		setAgreeFeeLoading(false);
		onReceive && onReceive(false);

		// 如果是全部收货，才清除orderForm的值，不然再次收货的时候，物流信息就丢了
		const theTableInstance = tableInstance?.current;
		const tableDataSource = theTableInstance?._getDataSource();
		if (theTableInstance && tableDataSource?.length === 0) {
			orderForm.resetFields();
		}

		// 新增：收货流程结束，发送停止视频监控消息
		const { isVideoMonitor, isIpcModel } = latestPropsRef.current;
		console.log('%c [ isVideoMonitor ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', isVideoMonitor, isIpcModel);
		if (isVideoMonitor) {
			if (isIpcModel) {
			// IPC模式，发送stop_nvr_record消息
				videoMonitorStore.sendMessage("stop_nvr_record", {
					cancel: 0, // 0 ，正常保存。  cancel 1，不保存丢失视频记录
				}, (res) => {
					if (res?.msg) {
						message.error(res.msg);
					}
				});
			} else {
			// 普通模式，发送stop_record消息
				videoMonitorStore.sendMessage("stop_record", {}, (res) => {
					if (res?.msg) {
						message.error(res.msg);
					}
				});
			}
		}
		console.timeEnd('收货流程全部结束');
		customLogPost('收货流程全部结束', {});
	};

	// 获取验证码
	const textMessageValidate = () => {
		Modal.confirm({
			title: '系统提醒',
			width: 450,
			centered: true,
			content: (
				<>
					<div className="r-mt-16">已发送验证码给子账号绑定手机，请填写短信验证码</div>
					<div className="r-mt-18">
						<span>短信验证码：</span>
						<span>
							<Input onChange={ (e) => setTextMessageCode(e.target.value) } />
						</span>
					</div>
				</>
			),
			onOk() {
				return new Promise((resolve, reject) => {
					let errorMsg = '';
					const textMessageCode = getTextMessageCode();
					if (!textMessageCode) {
						errorMsg = '请填写短信验证码';
					} else if (textMessageCode && textMessageCode.length !== 6) {
						errorMsg = '请输入6位有效的验证码';
					}
					if (errorMsg) {
						message.error(errorMsg);
						reject();
					} else {
						resolve(true);
						confirmAgreeFee(true);
					}
				});
			}
		});
	};

	// 提醒需要验证码
	const alertNeedTextMessage = () => {
		const selectedRows = getSelectedRows();
		const orderRows = [];
		let refundAmountCount = 0;
		selectedRows.forEach(i => {
			if (i.rowType === 'order') {
				orderRows.push(i);
			} else {
				refundAmountCount += Number(i.refundAmount) * 100; // 转换为分;
			}
		});
		const refundAmountInYuan = refundAmountCount / 100; // 需要显示时再转换回元
		Modal.confirm({
			title: '系统提醒',
			centered: true,
			content: (
				<>
					<div>已选择退款订单{orderRows.length}单，退款金额：
						<span style={ { color: "#f00", whiteSpace: 'nowrap' } }>¥{refundAmountInYuan.toFixed(2)}</span>
						元
					</div>
					<div>确定退款将平台子账号绑定手机号发送验证码</div>
					<div>开启自动退款可通过自动确认完成退款，
						<span className="kdzs-link-text" onClick={ () => { history.push("/aftersale/trade"); } } >前往开启</span>
					</div>
				</>
			),
			onOk() {
				textMessageValidate();
			}
		});
		customLogPost('触发提醒需要验证码弹框', { orderRows, refundAmountCount: refundAmountInYuan });
	};

	const refundFailModal = (orderIds, errorMessage) => {
		const content = [];
		orderIds.forEach((i, index) => {
			content.push(
				<>
					<span>
						{i}
						<CopyOutlined onClick={ () => { copyToPaste(i); } } className={ cs('r-fc-black-65', 'r-pointer', 'r-ml-4') } />
					</span>
					<span>{index === orderIds.length - 1 ? '' : '，'}</span>
				</>
			);
		});

		Modal.info({
			title: '退款失败提醒',
			centered: true,
			content: (
				<>
					<div style={ { color: '#555' } }>售后订单【{content}】退款失败，请前往售后订单页面重新退款</div>
				</>
			),
		});
	};

	// 发起自动退款
	function confirmAgreeFee(isSecond) {
		const { refundInfoIdList, platform } = getSelectedExtraInfo();
		if (!refundInfoIdList?.length) {
			return;
		}
		const _buildType = getBuildType();
		let params = {
			buildType: buildTypeEnumObj[_buildType],
			refundInfoIds: refundInfoIdList,
			isSecond,
			sid: '',
			code: getTextMessageCode(),
			logisticsCompanyCode: '',
		};
		if (platform === PLAT_JD) {
			params = {
				...params,
				...unpackRegisterParams,
			};
		}
		customLogPost(`发起自动退款接口请求, ${isSecond}`);
		console.time('RefundScanConfirmAgreeFeeApi');
		RefundScanConfirmAgreeFeeApi(params).then((res) => {
			console.timeEnd('RefundScanConfirmAgreeFeeApi');
			
			const refundScanConfirmAgreeRes = res.batchResults;
			if (!isSecond) { // 如果是第一次调用接口
				// 判断是否需要提醒用户需要验证码
				if (res.code == '5810') {
					alertNeedTextMessage();
					return;
				}
				// 判断是否需要弹窗输入验证码
				if (refundScanConfirmAgreeRes && refundScanConfirmAgreeRes.length > 0) {
					const eitherBatchResults = refundScanConfirmAgreeRes[0];
					if (eitherBatchResults.errorMessage === '验证码已发送') { // 目前接口只支持这样判断
						message.success(eitherBatchResults.errorMessage);
						customLogPost('验证码已发送，需要弹窗输入验证码', { eitherBatchResults, isSecond });
						textMessageValidate();
						return;
					} else if (eitherBatchResults.errorMessage) {
						message.error(eitherBatchResults.errorMessage);
						const orderIds = refundScanConfirmAgreeRes.map(i => i.operationId);
						refundFailModal(orderIds, eitherBatchResults.errorMessage);
						customLogPost('退款失败提醒', { orderIds, eitherBatchResults, isSecond });
					}
				}
				onReceiveSuccess();
			} else if (refundScanConfirmAgreeRes && refundScanConfirmAgreeRes.length > 0) {
				// 如果触发了短信校验并且batchResults有值，说明有售后单退款失败，需要弹窗提醒
				const eitherBatchResults = refundScanConfirmAgreeRes[0];
				message.error(eitherBatchResults.errorMessage);
				const orderIds = refundScanConfirmAgreeRes.map(i => i.operationId);
				refundFailModal(orderIds, eitherBatchResults.errorMessage);
				customLogPost('退款失败提醒', { orderIds, eitherBatchResults, isSecond });
				onReceiveSuccess();
			} else {
				customLogPost('发起退款成功');
				onReceiveSuccess();
			}
		}).catch((error) => {
			customLogPost('发起自动退款接口请求失败', { error, refundInfoIdList, params });
			onReceiveSuccess(); // 退款失败，还要执行打印退货标签
		}).finally(() => {
			setAgreeFeeLoading(false);
			onReceive && onReceive(false);
		});
	}

	// 调用接口校验售后权限
	const checkAfterAuthority = () => {
		const { sellerId } = getSelectedExtraInfo();
		if (!sellerId) {
			return;
		}
		setAgreeFeeLoading(true);
		console.time('CheckAfterAuthorityApi');
		CheckAfterAuthorityApi({ sellerId }).then(res => {
			console.timeEnd('CheckAfterAuthorityApi');
			// 如果权限校验通过，那就发起自动退款
			confirmAgreeFee(false);
		}).catch(error => {
			setAgreeFeeLoading(false);
			onReceive && onReceive(false);
			console.log('CheckAfterAuthorityApi error', error);
			Modal.error({
				title: '权限提醒',
				content: (
					<div>
						{error?.message || error?.errorMessage || '权限不足，请联系ERP系统主账号配置售后处理权限'}
						<br />
						<a href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/mqo43kx9yvm0kpqq?singleDoc#" target="_blank" rel="noreferrer">点击查看设置教程</a>
					</div>
				)
			});
			customLogPost('权限不足提醒', { error: error?.message || error?.errorMessage });
		});
	};

	/**
	 * 如果当前匹配到的是售后单，并且开启了自动同意退款，并且是TB、PDD、FXG平台的时候需要校验权限
	 */
	const needCheckAuth = () => { // 判断是否需要校验权限
		const needCheckAuthPlatform = [PLAT_TB, PLAT_PDD, PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_XHS, PLAT_JD];
		const _buildType = getBuildType();
		const { platform } = getSelectedExtraInfo();
		// 是否是需要校验权限的平台
		const isNeedCheckAuthPlatform = needCheckAuthPlatform.includes(platform?.toLocaleLowerCase());
		// 匹配到的是否是售后订单
		const isAftersaleOrder = _buildType === 1;
		// 是否开启了自动退款，autoRefund就行
		const _autoRefund = getAutoRefund();
		console.log('%c [ 判断是否需要校验权限 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', { platform, _buildType, isNeedCheckAuthPlatform, isAftersaleOrder, _autoRefund });
		return isNeedCheckAuthPlatform && isAftersaleOrder && _autoRefund;
	};

	// 判断是否开启了自动策略
	const checkIsOpen = async() => {
		try {
			let res = await RefundAutoStrategyJudgeOpenAutoStrategyApi({ strategyType: PolicyTypeEnum["自动同意【退货退款】"] });
			return res;
		} catch (error) {
			customLogPost('自动策略判断失败了，继续原退款逻辑');
			return false;
		}
	};

	// 触发收货
	const confirmReceive = async() => {
		let confirmReceiveParams = getConfirmReceiveParams();
		const getNeedCheckAuth = needCheckAuth();

		// 现在不走自动策略就不需要请求自动策略校验接口
		// let excuteAutoStrategy = false;
		// if (getNeedCheckAuth) {
		// 	customLogPost('判断是否开启了自动策略');
		// 	excuteAutoStrategy = await checkIsOpen();
		// }

		confirmReceiveParams['excuteAutoStrategy'] = false; // 售后扫描不执行自动策略
		confirmReceiveParams['requestVersion'] = 'v2'; // 请求版本
		confirmReceiveParams['requestOrigin'] = 1; // 1：pc-单个扫描 2：pc-批量扫描 3：小程序


		// 如果走自动策略京东需要传拆包登记信息，现在不走自动策略就不需要传
		// if (excuteAutoStrategy && confirmReceiveParams.platform === PLAT_JD) {
		// 	confirmReceiveParams = {
		// 		...confirmReceiveParams,
		// 		...unpackRegisterParams
		// 	};
		// }

		console.log('%c [ 售后扫描-确认收货 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', confirmReceiveParams);

		setTextMessageCode('');
		setConfirmLoading(true);
		onReceive && onReceive(true);

		const { searchType, searchValue } = searchForm.getFieldsValue();

		customLogPost('确认收货接口请求', { searchType, searchValue });
		console.time('RefundScanConfirmApi');
		RefundScanConfirmApi(trimObject(confirmReceiveParams)).then((res) => {
			console.timeEnd('RefundScanConfirmApi');
			
			if (res && res.batchResults) {
				setRefundScanConfirmRes(res);
				const receiveRes = res.batchResults[0] || {};
				/**
				 * 这个接口最外层的success为true的情况下
				 * 还需要判断这个success，表示是否收货成功
				 */
				const receiveSuccess = receiveRes.success;
				if (receiveSuccess) {
					if (getNeedCheckAuth) {
						// 不走自动策略
						customLogPost('收货成功，调用接口校验售后权限', { searchType, searchValue });
						checkAfterAuthority();
					} else {
						onReceive && onReceive(false);
						customLogPost('收货成功，无需处理售后退款', { searchType, searchValue });
						onReceiveSuccess();
					}
				} else {
					onReceive && onReceive(false);
					customLogPost('确认收货失败', { receiveRes, searchType, searchValue });
					if (res.buildType === 'MATCH_ORDER' && receiveRes.errorMessage) {
						message.error(`收货失败，${receiveRes.errorMessage}，订单编号【${receiveRes.operationId}】`);
						return;
					}
					if (res.buildType === 'MATCH_REFUND' && receiveRes.errorMessage) {
						message.error(`收货失败，${receiveRes.errorMessage}，售后单号【${receiveRes.operationId}】`);
						return;
					}
					if (receiveRes.errorMessage) {
						message.error(receiveRes.errorMessage);
					}
				}

				// 处理售后换货
				let errorExchangeMessage = res?.errorExchangeMessages?.[0];
				if (errorExchangeMessage) {
					message.error(`${errorExchangeMessage.errorInfo}，售后单号【${errorExchangeMessage.refundId}】`);
					customLogPost('处理售后换货失败', { errorExchangeMessage });
				}
			} else {
				onReceive && onReceive(false);
			}
		}).catch((error) => {
			onReceive && onReceive(false);
			customLogPost('确认收货接口请求失败', { confirmReceiveParams, error });
		}).finally(() => {
			setConfirmLoading(false);
			onReceive && onReceive(false); // 暂时收货完就不显示加载状态保持原样
		});
	};

	const handleSelectedExtraInfo = () => {
		// 从勾选的数据中提取出店铺，平台等信息，并存下来，别的地方需要用到
		let _platform = '';
		let _sellerId = '';
		const selectedRows = getSelectedRows();
		const refundInfoIdList = [];
		selectedRows.forEach(row => {
			if (row.rowType === 'order') {
				const { platform, sellerId, id, isDistributorUserPushRefund } = row.originData || {};
				// 这里过滤分销商的售后单
				if (!isDistributorUserPushRefund) {
					refundInfoIdList.push(id);
					_platform = platform;
					_sellerId = sellerId;
				}
			}
		});
		setSelectedExtraInfo({
			platform: _platform,
			sellerId: _sellerId,
			refundInfoIdList,
		});
	};

	const bindOrderModal = () => {
		Modal.confirm({
			centered: true,
			width: 450,
			title: '请填写关联的销售订单号',
			content: (
				<div className="r-flex r-mt-20">
					<span>订单编号：</span>
					<div className="r-flex-1">
						<Input style={ { width: '100%' } } onChange={ (e) => setRelateTid(e.target.value) } />
					</div>
				</div>
			),
			onOk() {
				customLogPost('请填写关联的销售订单号,确定', { relateTid });
				confirmReceive();
			}
		});
	};

	const onConfirmReceiveValidateSuccess = (formData, selectedRows, ignoreException) => {
		console.timeEnd('EVENT_BUS.CONFIRM_RECEIVE_VALIDATE_SUCCESS');
		const { searchType, searchValue } = searchForm.getFieldsValue();
		// customLogPost('表格校验完成继续收货', { 
		// 	selectedRows: selectedRows.map(i => {
		// 		return {
		// 			refundId: i.originData?.refundId,
		// 			skuId: i.originData?.skuId,
		// 			numIid: i.originData?.numIid,
		// 			sysItemId: i.originData?.sysItemId,
		// 			sysSkuId: i.originData?.sysSkuId,
		// 		};
		// 	}),
		// 	ignoreException,
		// 	searchType, 
		// 	searchValue
		// });
		setSelectedRows(selectedRows);
		setFormData(formData);
		setIgnoreException(ignoreException);
		setTimeout(() => {
			handleSelectedExtraInfo();
		});
		setTimeout(() => {
			orderForm.validateFields().then(() => {
				const needBindOrder = getNeedBindOrder() && getBuildType() === 3;
				// 如果当前未匹配到任何订单，并且用户勾选了无信息件需绑定订单，则弹窗绑定订单
				if (needBindOrder) {
					bindOrderModal();
					customLogPost('无信息件需绑定订单', { searchType, searchValue });
					return;
				}
				confirmReceive();
			}).catch((error) => {
				customLogPost('售后订单信息校验失败：orderForm', { searchType, searchValue, error });
			});
		});

	};

	function onMatchFail(buildType, scanRecordId) {
		/**
		 * 如果查询售后订单或者销售单结果是空的，需要做以下操作
		 * 1.清空当前查询值；
		 * 2.将当前查询值复写到售后订单信息form中；
		 * 3.查询条件自动切换到下一个；
		 */
		setBuildType(buildType);
		const { searchType, searchValue } = searchForm.getFieldsValue();
		const searchTypeList = SCAN_REGISTER_FORM_SEARCH_SEARCH_TYPE.options;
		const curSearchTypeIndex = searchTypeList.findIndex(i => i.value === searchType);
		const nextSearchType = searchTypeList[curSearchTypeIndex + 1] || searchTypeList[0];
		event.emit(EVENT_BUS.SCAN_REQUEST_RESULT, [], { buildType, scanRecordId });
		nextSearchType.point && sendPoint(nextSearchType.point);
		searchForm.setFieldsValue(
			{
				searchValue: '',
			}
		);
		// 如果用户没开启开关，就不自动切换搜索类型
		if (!local.get(LOCAL_SETTING)?.noAutoSwitchCondition) {
			searchForm.setFieldsValue(
				{
					searchType: nextSearchType.value
				}
			);
		}
		orderForm.setFieldsValue({
			[searchType]: searchValue
		});
		updateOrderFormData();
	}


	const onMatchSuccess = (orderFormData, res) => {
		/**
		 * 如果能查到值，需要做以下操作
		 * 1.清空查询条件值
		 * 2.输入框重新聚焦
		 * 3.如果扫描的是快递单号，回显快递单号
		 */
		const { searchType, searchValue } = orderFormData;
		setBuildType(res[0].buildType);
		searchInput?.current?.blur();
		orderForm.resetFields();
		if (searchType === 'sid') {
			orderForm.setFieldsValue({
				sid: searchValue?.trim(),
				companyCode: getCompanyInfo()?.[searchValue]?.cpCode
			});
			updateOrderFormData();
		}
		event.emit(EVENT_BUS.SCAN_REQUEST_RESULT, res);

		// 判断是否需要发送视频监控消息
		// 视频监控开关开启，未选ipc就走usb，选了ipc就走ipc
		const { isVideoMonitor, isIpcModel } = latestPropsRef.current;
		if (isVideoMonitor) {
			const outSid = orderFormData.searchValue?.trim();
			const companyId = userStore.userInfo?.userId; // 用户id
			const operateUser = userStore.userInfo?.subUserName || userStore.userInfo?.userName; // 操作人
			const operateUserId = userStore.userInfo?.subUserId || userStore.userInfo?.userId; // 操作人id

			customLogPost('视频监控录制', { isIpcModel, searchValue, searchType, refundId: res?.[0]?.refundId, operateUserId, operateUser });

			if (isIpcModel) {
				const device = nvrDevices?.find(d => d.id == selectedShootingDevices[0]?.deviceId);
				if (!device) {
					return;
				}

				videoMonitorStore.sendMessage("start_nvr_record", {
					outSid, // 扫描查询的项，不固定，可能为售后单号，也可能为快递单号等等
					cancel: -1, // 有正在录制中的视频 0 返回错误 1丢弃视频 -1保存视频
					// fix_filename: {
					// 	pre_sid: '',
					// 	fix_sid: ''
					// },
					fake_sid: 0,
					host: window.location.hostname,
					token: getToken(),
					nvr_device: {
						device_ip: device?.deviceIp,
						device_username: device?.deviceAccountName,
						device_password: device?.deviceAccountPassword,
						device_channel: Number(selectedShootingDevices[0]?.cameraChannel),
						device_channel_list: selectedShootingDevices?.map(item => Number(item.cameraChannel)), // 同一个录像设备
					},
					extra_params: {
						erpVideoIds: selectedShootingDevices?.map(item => Number(item.deviceId)) || [],
						erpCameraIds: selectedShootingDevices?.map(item => Number(item.id)) || [],
						autoToOSS: false, // 是否自动上传到oss
						operateType: 1, // 1 售后扫描快递单
						monitorType: 1, // 1 ipc 2 usb
						outSid, // 扫描查询的值
						exNumber: searchType === 'sid' ? outSid : '', // 快递单号
						companyId, // 用户id
						operateUser, // 操作人
						operateUserId, // 操作人id
						channel: Number(selectedShootingDevices[0]?.cameraChannel),
						channelList: selectedShootingDevices?.map(item => Number(item.cameraChannel)), // 同一个录像设备
						afterSaleOrderNumber: res?.[0]?.refundId || '', // 售后单号
						platformOrderNumber: res?.[0]?.tid || '', // 平台订单号
						buildType: res?.[0]?.buildType || 3, // 扫描类型
						afterSaleType: res?.[0]?.afterSaleType, // 售后类型
						refundSystemType: res?.[0]?.refundSystemType, // 退款系统类型
						refundStatus: res?.[0]?.refundStatus, // 退款状态
						scanRecordId: res?.[0]?.scanRecordId || '', // 扫描记录id
						platform: res?.[0]?.platform || '', // 平台
						searchType, // 查询类型
					}
				}, (res) => {
					if (res?.msg) {
						message.error('视频录制失败：' + res.msg);
						customLogPost('视频录制失败ipc', { searchValue, msg: res.msg });
					}
				});

			} else {
				// 普通模式，发送start_record消息
				videoMonitorStore.sendMessage("start_record", {
					outSid: orderFormData.searchValue?.trim(), // 扫描查询的项，不固定，可能为售后单号，也可能为快递单号等等
					cancel: -1, // 有正在录制中的视频
					water_mark_texts: [`${SearchTypeObj[searchType]}：${outSid}`], // 自定义水印文字
					water_maker_type: 1, // 水印类型 0 默认 1 自定义
					hide_water_mark: 1, // 是否要隐藏客户端默认添加地运单号水印 0 不隐藏 1 隐藏运单号水印
					fake_sid: 0, // 是否是假单号 0 否 1 是
					host: window.location.hostname,
					token: getToken(),
					extra_params: {
						erpVideoIds: [],
						erpCameraIds: [],
						autoToOSS: false, // 是否自动上传到oss
						operateType: 1, // 1 售后扫描快递单
						monitorType: 2, // 1 ipc 2 usb
						outSid, // 扫描查询的值
						exNumber: searchType === 'sid' ? outSid : '', // 快递单号
						companyId, // 用户id
						operateUser, // 操作人
						operateUserId, // 操作人id
						channel: '',
						channelList: [], // 同一个录像设备
						afterSaleOrderNumber: res?.[0]?.refundId || '', // 售后单号
						platformOrderNumber: res?.[0]?.tid || '', // 平台订单号
						buildType: res?.[0]?.buildType || 3, // 扫描类型
						afterSaleType: res?.[0]?.afterSaleType, // 售后类型
						refundSystemType: res?.[0]?.refundSystemType, // 退款系统类型
						refundStatus: res?.[0]?.refundStatus, // 退款状态
						scanRecordId: res?.[0]?.scanRecordId || '', // 扫描记录id
						platform: res?.[0]?.platform || '', // 平台
						searchType, // 查询类型
					}
				}, (res) => {
					if (res?.msg) {
						message.error('视频录制失败：' + res.msg);
						customLogPost('视频录制失败ipc', { searchValue, msg: res.msg });
					}
				});
			}
		}
	};

	const onReqSelectRefundScanFinally = () => {
		setReqScanResLoading(false);
		setScanTime(dayjs().format('YYYY-MM-DD HH:mm:ss'));
	};


	const requestScanResult = (orderFormData) => {
		/**
		 * 查询的时候判断当前是否处于查询中，禁止查询中再次查询
		 */
		const data = {
			[orderFormData.searchType]: orderFormData.searchValue?.trim(),
			requestVersion: 'v2',
			requestOrigin: 1, // 1：pc-单个扫描 2：pc-批量扫描 3：小程序
			findExistWaitSendTrade: sortColumnList?.find(i => i.key === 'existWaitSendTrade')?.ischecked,
		};

		setReqScanResLoading(true);
		return SelectReqSelectRefundScanV2Api(data).then(res => {
			const {
				list = [], // 匹配到售后单销售单时返回
				buildType, // 扫描类型1匹配到售后单2-匹配到销售单3-手动创建
				scanRecordId, // 扫描记录id，未匹配到时返回
			} = res || {};

			// V2数据结构变更
			if (list?.length > 0) {
				onMatchSuccess(orderFormData, list);
				setCurrentOrder(list);
				setScanRecordId('');
			} else {
				// 未匹配到时记下扫描记录id，自定义添加数据时候使用
				if (buildType == 3 && scanRecordId) {
					setScanRecordId(scanRecordId);
				} else {
					setScanRecordId('');
				}
				onMatchFail(buildType, scanRecordId);
			}
		}).catch((e) => {
			setScanRecordId('');
			if (e?.errorCode == BatchScanStatus.包裹已收货) {
				playAudio(voiceReceived);
			}
		}).finally(onReqSelectRefundScanFinally);
	};

	const getCompanyInfoByExpressNo = (sid) => {
		// 根据输入的快递单号查询快递公司信息
		return GetLogisticsCompanyInfoByYdNoApi({ ydNoList: [sid.trim()] }).then(res => {
			const companyInfo = res?.data?.logisticsCompanyInfos?.[0];
			if (companyInfo && companyInfo.cpCode && !["unknown"].includes(companyInfo.cpCode)) {
				setCompanyInfo({
					...getCompanyInfo(),
					[sid]: companyInfo
				});
				orderForm.setFieldsValue({
					companyCode: companyInfo.cpCode
				});
			}
			updateOrderFormData();
		});
	};


	const onSearchOrder = () => {
		// 正在查询中，禁止再次查询
		if (getReqScanResLoading()) {
			return;
		}
		if (tokens.has('getTradeOrderData1')) {
			tokens.get('getTradeOrderData1').cancel('取消请求：查询取消待发货订单数据请求');
			tokens.delete('getTradeOrderData1');
			console.log('%c [ 查询时候，取消之前的待发货订单数据请求 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', 'getTradeOrderData1');
		}
		if (tokens.has('getTradeOrderData2')) {
			tokens.get('getTradeOrderData2').cancel('取消请求：查询取消待发货订单数据请求');
			tokens.delete('getTradeOrderData2');
			console.log('%c [ 查询时候，取消之前的待发货订单数据请求 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', 'getTradeOrderData2');
		}
		const requestArr = [];
		searchForm.validateFields().then(res => {
			onSearch && onSearch(true);
			// 查询之前先清空售后订单信息表单的值
			orderForm.resetFields();
			sendPoint(Pointer.售后_售后扫描登记_查询);
			requestArr.push(requestScanResult(res));
			if (res.searchType === "sid") {
				orderForm.setFieldsValue({
					sid: res.searchValue?.trim()
				});
				requestArr.push(getCompanyInfoByExpressNo(res.searchValue));
			}
			Promise.all(requestArr).finally(() => {
				onSearch && onSearch(false);
			});
		}).catch(() => {
			onSearch && onSearch(false);
		});


	};

	const validatorSearchValue = (_, value) => {
		if (!value) {
			const searchType = searchForm.getFieldValue('searchType');
			const searchTypeInfo = SCAN_REGISTER_FORM_SEARCH_SEARCH_TYPE.options.find(i => i.value === searchType);
			return Promise.reject(new Error(`请输入${searchTypeInfo?.label}`));
		}
		return Promise.resolve();
	};
	const searchFormInitialValues = {
		searchType: SCAN_REGISTER_FORM_SEARCH_SEARCH_TYPE.default
	};

	const onClickSetConfig = () => {
		sendPoint(Pointer.售后_售后扫描登记_售后设置);
		setScanSetModalVisible(true);
	};
	const onScanSetModalClose = (data) => {
		setScanSetModalVisible(false);
	};

	// 开始确认收货
	const onConfirmReceive = () => {
		if (getConfirmLoading()) {
			message.error('正在确认收货中');
			return;
		}
		if (getAgreeFeeLoading()) {
			message.error('正在处理退款中');
			return;
		}
		sendPoint(Pointer.售后_售后扫描登记_确认收货);

		console.time('EVENT_BUS.CONFIRM_RECEIVE');
		event.emit(EVENT_BUS.CONFIRM_RECEIVE);
	};

	// 开始确认收货前判断是否需要拆包登记
	const onBeforeConfirmReceive = () => {
		console.time('收货流程全部结束');
		const { searchType, searchValue } = searchForm.getFieldsValue();
		customLogPost('开始确认收货', { searchType, searchValue });

		// 清理下正在请求的待发货订单请求
		if (tokens.has('getTradeOrderData1')) {
			tokens.get('getTradeOrderData1').cancel('取消请求：确认收货取消待发货订单数据请求');
			tokens.delete('getTradeOrderData1');
			console.log('%c [ 确认收货时候，取消之前的待发货订单数据请求 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', 'getTradeOrderData1');
		}
		if (tokens.has('getTradeOrderData2')) {
			tokens.get('getTradeOrderData2').cancel('取消请求：确认收货取消待发货订单数据请求');
			tokens.delete('getTradeOrderData2');
			console.log('%c [ 确认收货时候，取消之前的待发货订单数据请求 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', 'getTradeOrderData2');
		}

		const afterSaleOrder = currentOrder[0] || {};
		if (afterSaleOrder?.platform === PLAT_JD && getAutoRefund() && [afterSaleTypeText.退货退款].includes(afterSaleOrder.afterSaleType) && [2, 3].includes(afterSaleOrder.refundStatus) && afterSaleOrder.refundStatusDesc !== "待处理") {
			unpackRegisterParams = {};
			setUnpackRegisterModalVisible(true);
			customLogPost('开始拆包登记');
		} else {
			onConfirmReceive();
		}
	};

	// 拆包登记确认回调
	const afterUnpackRegister = (params) => {
		unpackRegisterParams = params;
		onConfirmReceive();
		setUnpackRegisterModalVisible(false);
	};
	const getPlatformList = async() => {
		const platformList = await getAllPlats(true);
		const validPlatform = platformList.filter(i => VALID_PLATFORM.includes(i));
		setPlatformList(validPlatform);
	};

	// 当平台变更时，清空店铺选项并重新渲染店铺列表
	const onOrderFormChange = async(changedValues) => {
		if ('platform' in changedValues) {
			orderForm.setFieldsValue({ shop: '' });
			const shopList = await getShopsByPlat({ plats: [changedValues.platform], hasHand: true });
			orderForm.setFieldsValue({
				sellerId: '',
			});
			setShopList(shopList);
		}
		updateOrderFormData();
	};

	const onResetForm = () => {
		searchForm.resetFields();
		setTimeout(() => {
			searchInput?.current?.focus();
		}, 200);
	};

	// 处理用户配置开关
	const handleConfigInfo = (value) => {
		const { noInfoItemBindOrder, autoRefund, spacebarSettingsReceipt, autoFinish } = value;
		setAutoFinish(autoFinish);
		setOriginConfig(value);
		setAutoRefund(autoRefund === 1);
		setAllowSpaceKeyReceive(spacebarSettingsReceipt === 1);
		setNeedBindOrder(noInfoItemBindOrder === 1);
	};


	// 保存用户开关配置
	const onReceiveConfigChange = (e, type) => {
		const checked = e?.target?.checked;
		if (type === 'autoRefund') {
			setAutoRefund(checked);
		} else if (type === 'needBindOrder') {
			setNeedBindOrder(checked);
		}
		setTimeout(() => {
			const value = {
				...originConfig,
				autoRefund: getAutoRefund() ? 1 : 2,
				noInfoItemBindOrder: getNeedBindOrder() ? 1 : 2
			};
			const params = {
				userDictEnum: 'AFTERSALE_SCAN_SET',
				value: JSON.stringify(value)
			};
			TradeDictInsertDictApi(params);
		});
	};

	// 获取所有的快递公司
	const getExpressCompany = () => {
		if (getExpressCompanyList()?.length > 0) {
			return;
		}
		getExCompanyAll({}).then(res => {
			if (res && res.length > 0) {
				setExpressCompanyList(res);
			}
		});
	};


	const onKeyDown = async(event) => {
		const { pathname } = history.location;
		// 判断用户是否在扫描登记页面
		const isCurPage = pathname === "/aftersale/scanRegister";
		// 判断用户是否开启了空格键收货
		const allowSpaceKeyReceive = getAllowSpaceKeyReceive();
		// 判断用户是否处于输入状态
		const isInputStatus = ['input', 'textarea'].indexOf(event.target.tagName.toLowerCase()) > -1;
		// 判断当前是否有弹窗出现
		const allModal = document.querySelectorAll('.pup_express_box,.ant-modal-wrap');
		let hasModalShow = false;
		for (let key = 0; key < allModal.length; key++) {
			if (allModal[key]?.style?.display !== 'none') {
				hasModalShow = true;
			}
		}
		if (!isInputStatus && !hasModalShow && isCurPage) {
			if (event.keyCode === 32 || event.code === 'Space') {
				const e = window.event || event;
				if (e.preventDefault) {
					e.preventDefault();
				} else {
					event.returnValue = false;
				}
				if (allowSpaceKeyReceive) {
					console.log('%c [ 触发了空格确认收货 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '');
					const { searchType, searchValue } = searchForm.getFieldsValue();
					customLogPost('触发了空格确认收货', { searchType, searchValue });
					onBeforeConfirmReceive();
				}
			}
		}
	};

	// 限制空格确认收货
	const debounceOnKeyDown = debounce((event) => onKeyDown(event), 300);

	const tabsOnChange = (activeKey) => {
		if (activeKey === "1") {
			document.removeEventListener("keydown", debounceOnKeyDown, false);
			document.addEventListener("keydown", debounceOnKeyDown, false);
		} else {
			document.removeEventListener("keydown", debounceOnKeyDown, false);
		}
	};

	const getPopoverDom = (
		<div>
			<div>1、开启后，同意退款时优先按平台自动策略退款，商家后台未配置时将进行手动退款</div>
			<div>2、未开启时，不进行退款操作</div>
			<div>3、支持淘宝/天猫、拼多多、抖音、快手、视频号、小红书、京东平台退款操作</div>
		</div>
	);

	useEffect(() => {
		handleConfigInfo(configInfo);
	}, [configInfo]);

	useEffect(() => {
		searchForm.setFieldsValue({
			searchType: aftersaleScanQueryType.aftersaleScanQueryType
		});
		getPlatformList();
		// getSearchTypeOptions();
		getExpressCompany();
		setTimeout(() => {
			searchInput?.current?.focus();
		}, 200);
		document.addEventListener("keydown", debounceOnKeyDown, false);
		event.on(EVENT_BUS.TABS_ONCHANGE, tabsOnChange);
		event.on(EVENT_BUS.SCAN_SET_MODAL_CLOSED, onScanSetModalClose);
		event.on(EVENT_BUS.CONFIRM_RECEIVE_VALIDATE_SUCCESS, onConfirmReceiveValidateSuccess);
		event.on(EVENT_BUS.CONFIRM_RECEIVE_SUCCESS_FINALLY, onConfirmReceiveSuccessFinally);
		return () => {
			document.removeEventListener("keydown", debounceOnKeyDown, false);
			event.off(EVENT_BUS.TABS_ONCHANGE, tabsOnChange);
			event.off(EVENT_BUS.CONFIRM_RECEIVE_VALIDATE_SUCCESS, onConfirmReceiveValidateSuccess);
			event.off(EVENT_BUS.SCAN_SET_MODAL_CLOSED, onScanSetModalClose);
			event.off(EVENT_BUS.CONFIRM_RECEIVE_SUCCESS_FINALLY, onConfirmReceiveSuccessFinally);
		};
	}, []);

	useImperativeHandle(cRef, () => {
		return {
			searchForm,
			orderForm,
			getExpressCompanyList,
			getShopList,
		};
	}, []);
	return (
		<div>
			{/* 查询版块 */}
			<div>
				<div>
					<Form
						form={ searchForm }
						className={ styles['search-form'] }
						initialValues={ searchFormInitialValues }
					>
						<div className={ styles['search-type'] }>
							<Form.Item name="searchType">
								<Select
									optionLabelProp="label"
									onChange={ (e) => {
										let point = SCAN_REGISTER_FORM_SEARCH_SEARCH_TYPE.options.find(item => item.value === e).point;
										point && sendPoint(point);
									} }
								>
									{getSearchTypeOptions}
								</Select>
							</Form.Item>

						</div>

						<div className={ styles['search-value'] }>
							<Form.Item
								name="searchValue"
								rules={ [{
									validator: validatorSearchValue
								}
								] }
							>
								<Input onPressEnter={ onSearchOrder } ref={ searchInput } allowClear />
							</Form.Item>
						</div>

					</Form>


				</div>
				<div>
					<Button type="primary" onClick={ onSearchOrder } loading={ reqScanResLoading }>查询</Button>
					<Button className="r-ml-12" onClick={ onResetForm }>重置</Button>
				</div>
			</div>

			{/* 售后订单信息版块 */}

			<div className="r-mt-16">
				<div style={ { color: 'rgba(0,0,0,0.65)' } }>售后订单信息，新建时请手动完善以下信息</div>
				<Form
					className={ cs(styles['order-form'], 'r-mt-12') }
					requiredMark={ false }
					form={ orderForm }
					onValuesChange={ onOrderFormChange }
				>
					<Form.Item
						label="快递公司"
						name="companyCode"
						className={ styles.select }
						rules={ [{
							// required: [2, 3].includes(getBuildType()),
							message: '请选择快递公司'
						}] }
					>
						<Select placeholder="快递公司">
							{expressCompanyList.map(company => {
								return <Select.Option key={ company.exCode } value={ company.exCode }>{company.exName}</Select.Option>;
							})}
						</Select>
					</Form.Item>
					<Form.Item
						label="快递单号"
						name="sid"
						rules={ [{
							// required: [2, 3].includes(getBuildType()),
							message: '请输入快递单号'
						}] }
					>
						<Input placeholder="快递单号" />
					</Form.Item>
					<Form.Item
						label="手机号"
						name="receiverMobile"
						rules={ [{
							// required: getBuildType() === 3,
							message: '请输入手机号'
						}] }
					>
						<Input placeholder="手机号" />
					</Form.Item>
					<Form.Item
						label="收件人"
						name="receiverName"
						rules={ [{
							// required: getBuildType() === 3,
							message: '请输入收件人'
						}] }
					>
						<Input placeholder="收件人" />
					</Form.Item>
					<Form.Item
						label="买家昵称"
						name="buyerNick"
						rules={ [{
							// required: getBuildType() === 3,
							message: '请输入买家昵称'
						}] }
					>
						<Input placeholder="买家昵称" />
					</Form.Item>
					<Form.Item
						label="平台"
						name="platform"
						className={ styles.select }
						rules={ [{
							// required: getBuildType() === 3,
							message: '选择平台'
						}] }
					>
						<Select placeholder="平台">
							{platformList.map(plat => {
								return <Select.Option key={ plat } value={ plat }>{PLAT_MAP[plat]}</Select.Option>;
							})}
						</Select>
					</Form.Item>
					<Form.Item
						label="店铺"
						name="sellerId"
						className={ styles.select }
						rules={ [{
							// required: getBuildType() === 3 && orderForm.getFieldValue('platform') !== PLAT_HAND,
							message: '请选择店铺'
						}] }
					>
						<Select placeholder="店铺">
							{shopList.map(shop => {
								return <Select.Option key={ shop.sellerId } value={ shop.sellerId }>{shop.sellerNick}</Select.Option>;
							})}
						</Select>

					</Form.Item>
					<Form.Item
						label="售后原因"
						className={ styles.select }
						name="refundReason"
						rules={ [{
							// required: getBuildType() === 3,
							message: '请选择售后原因'
						}] }
					>
						<Select placeholder="售后原因">
							{
								refundReasonNames.map(type => {
									return <Select.Option key={ type.value } value={ type.value }>{type.name}</Select.Option>;
								})
							}
						</Select>
					</Form.Item>
				</Form>
			</div>

			<div>
				<div className={ styles['receive-config-container'] }>
					<div>
						<Checkbox
							checked={ autoRefund }
							onChange={ (e) => onReceiveConfigChange(e, 'autoRefund') }
						>
							确认收货后自动同意退款
							<Popover content={ getPopoverDom } arrowPointAtCenter placement="topLeft">
								<QuestionCircleOutlined className="r-ml-4" />
							</Popover>
						</Checkbox>
					</div>
					{
						userStore.isShowZeroStockVersion ? null : (
							<div className="r-mt-8">
								<Checkbox
									checked={ upStockConfig }
									onChange={ (e) => props?.onChangeUpStockConfig?.(e) }
								>
									确认收货自动退货入库
								</Checkbox>
							</div>
						)
					}
					<div className="r-mt-8">
						<Checkbox
							checked={ curRefundScanAutoCreateExchangeOrder }
							onChange={ (e) => props?.onExchangeConfigChange(e) }
						>
							确认收货后自动创建换货手工单
						</Checkbox>
					</div>
					<div className="r-mt-8">
						<Checkbox
							checked={ needBindOrder }
							onChange={ (e) => onReceiveConfigChange(e, 'needBindOrder') }
						>
							无信息件需绑定订单
						</Checkbox>
					</div>
				</div>

				<div className="r-mt-24">
					<span className="r-pointer" style={ { color: '#1890ff' } } onClick={ onClickSetConfig }>售后设置</span>
					<Button
						// loading={ confirmLoading }
						className="r-w-full r-mt-8"
						type="primary"
						onClick={ onBeforeConfirmReceive }
					>确认收货
					</Button>
				</div>
			</div>
			<ScanSetModal visible={ scanSetModalVisible } />
			<UnpackRegisterModal currentOrder={ currentOrder } onOk={ afterUnpackRegister } visible={ unpackRegisterModalVisible } onCancel={ () => { setUnpackRegisterModalVisible(false); } } />
		</div>
	);
};

export default observer(SearchComp);
