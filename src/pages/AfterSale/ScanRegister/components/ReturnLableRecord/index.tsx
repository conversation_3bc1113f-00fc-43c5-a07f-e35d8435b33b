/*
 * @Author: try <EMAIL>
 * @Date: 2024-10-15 13:42:27
 * @Description:
 */
import React, { useState } from "react";
import dayjs from "dayjs";
import message from '@/components/message';
import { downloadExcelByBlob } from "@/utils";
import Search from "./components/Search";
import { ExportRefundLabelApi } from "@/apis/aftersale/scanRegister";
import { TakeGoodsLabelSelectWithPageApi } from "@/apis/trade/takeGoodsLabel";
import Table from "./components/Table";
import s from "./index.module.scss";
import useGetState from "@/utils/hooks/useGetState";
import { getMultiShops, getMultiShopsWithFilter } from "@/components-biz/ShopListSelect/shopListUtils";
import { PLAT_KTT, PLAT_DW, PLAT_YZ } from "@/constants";

const defaultPagination = { pageSize: 10, pageNo: 1 };
const ReturnLableRecord = () => {
	const [formData, setFormData, getFormData] = useGetState({});
	const [loading, setLoading] = useState(false);
	const [pagination, setPagination, getPagination] = useGetState(defaultPagination);
	const [returnLabelRecordList, setReturnLabelRecordList] = useState([]);
	const [params, setParams] = useState({});
	const onSearch = async(formData) => {
		const multiShopsData = formData.multiShops || {};
		const { plats = [], plat_sellerIds = [] } = multiShopsData || {};
		let multiShops = await getMultiShopsWithFilter({ plats, plat_sellerIds, hidePlatforms: [PLAT_KTT, PLAT_DW, PLAT_YZ] });
		multiShops && (formData['multiShops'] = multiShops);

		if (!plats?.length && !plat_sellerIds?.length) {
			formData['shopIsActualSelect'] = 0; // 页面是否实际勾选店铺查询 0否 1是
		} else {
			formData['shopIsActualSelect'] = 1;
		}
		setFormData(formData);
		setPagination(defaultPagination);
		setTimeout(() => queryReturnLabelRecordList());
	};

	const handleData = (res) => {
		const { list, ...pageInfo } = res || {};
		setReturnLabelRecordList(list || []);
		setPagination(pageInfo);
	};
	const queryReturnLabelRecordList = () => {
		setLoading(true);
		const params = {
			takeGoodsLabelBizTypeList: ["REFUND_LABEL"],
			...getFormData(),
			...getPagination(),
			sortType: 21,
			time: undefined,
		};
		setParams(params);
		TakeGoodsLabelSelectWithPageApi({ ...params }).then(res => handleData(res)).finally(() => setLoading(false));
	};

	const pageOnChange = (pageNo, pageSize) => {
		setPagination({
			...getPagination(),
			pageNo,
			pageSize
		});
		setTimeout(() => queryReturnLabelRecordList());
	};

	const downloadData = () => {
		// ExportRefundLabelApi(params).then(res => {
		// 	message.success("导出成功！");
		// 	const now = dayjs().format("YYYYMMDDHHmmss");
		// 	const fileName = `退货标签记录${now}.xlsx`;
		// 	downloadExcelByBlob(res, fileName);
		// });
		return params; // 只返回参数，不直接调用API
	};
	return (
		<div className={ s.container } >
			<div className={ s["search-container"] }>
				<Search onSearch={ onSearch } />
			</div>
			<div className={ s["table-container"] }>
				<Table
					loading={ loading }
					dataSource={ returnLabelRecordList }
					onDownload={ downloadData }
					pagination={ {
						...pagination,
						current: pagination.pageNo,
						showSizeChanger: true,
						pageSizeOptions: [10, 30, 50, 100, 200],
						onChange: pageOnChange,
						align: 'right',
						bottom: '52px'
					} }
				/>
			</div>

		</div>
	);
};
export default ReturnLableRecord;
