import React, { useState } from "react";
import { Button, Form, Input, Select } from "antd";
import dayjs from "dayjs";
import KdzsDateRangePicker from '@/components/DateRangeComp/kdzsRangePicker1';
import { DatePickerKey } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import ShopMultiSelect from "@/components-biz/ShopListSelect/shopMultiSelect";
import InputMulti from "@/components/Input/InputMulti";
import s from "./index.module.scss";
import { TimeType } from "@/pages/AfterSale/ScanRegister/constants";
import { GoodsContainOnlyContainType } from "@/pages/Trade/GoodsTag/components/SearchContainer/SearchCondition";
import InputArrayMulti from "@/components/Input/InputArrayMulti";
import { PLAT_KTT, PLAT_DW, PLAT_YZ } from "@/constants";

type Props = {
	onSearch:(formData:{})=>void,
}

const Search = (props:Props) => {
	const { onSearch } = props;
	const [form] = Form.useForm();
	const [itemSkuType, setItemSkuType] = useState(1);

	const _onSearch = () => {
		form.validateFields().then(res => {
			const newRes = { ...res };
			if (newRes.item) {
				newRes.item = newRes.item.join(",");
			}
			if (newRes.sku) {
				newRes.sku = newRes.sku.join(",");
			}
			if (!newRes.itemSkuType) {
				newRes.itemSkuType = '1';
			}
			
			let [startTime, endTime] = newRes?.time || [];
			if (startTime) {
				startTime = dayjs(startTime).format("YYYY-MM-DD HH:mm:ss");
			}
			if (endTime) {
				endTime = dayjs(endTime).format("YYYY-MM-DD HH:mm:ss");
			}
			const params = {
				...newRes,
				startTime,
				endTime,
				refundWayBillNos: res?.refundWayBillNos ? res?.refundWayBillNos?.split(",") : [],
				refundIds: res?.refundIds ? res?.refundIds?.split(",") : [],
			};
			onSearch && onSearch(params);
		});
	};
	const _onReset = () => {
		form.resetFields();
		setItemSkuType(1);
	};
	const itemSkuTypeChange = (e:any) => {
		setItemSkuType(Number(e));
	};

	return (
		<div style={ { paddingTop: '8px' } }>
			<Form form={ form } layout="inline" className={ s.form }>
				<Form.Item name="timeType" initialValue={ TimeType.生成时间 }>
					<Select size="small" style={ { width: '170px' } }>
						<Select.Option value={ TimeType.生成时间 }>生成时间</Select.Option>
					</Select>
				</Form.Item>
				<Form.Item name="time">
					<KdzsDateRangePicker datePickerKey={ DatePickerKey.afterSale_scan_record } cacheQuickChoose style={ { width: '169px' } } size="small" />
				</Form.Item>
				<Form.Item name="multiShops">
					<ShopMultiSelect style={ { width: '169px' } } size="small" isHasHandPlat hidePlatforms={ [PLAT_KTT, PLAT_DW, PLAT_YZ] } />
				</Form.Item>
				<Form.Item name="refundIds">
					<InputMulti size="small" placeholder="售后订单编号" maxInputNum={ 1000 } style={ { width: 170 } } />
				</Form.Item>
				<Form.Item name="tid">
					<InputMulti size="small" placeholder="系统单号" maxInputNum={ 1000 } style={ { width: 170 } } />
				</Form.Item>
				<Form.Item name="ptTid">
					<InputMulti size="small" placeholder="订单编号" maxInputNum={ 1000 } style={ { width: 170 } } />
				</Form.Item>
				<Form.Item name="refundWayBillNos">
					<InputMulti size="small" placeholder="快递单号" maxInputNum={ 1000 } style={ { width: 170 } } />
				</Form.Item>
				<Form.Item name="labelId">
					<InputMulti size="small" placeholder="退款唯一码" maxInputNum={ 1000 } style={ { width: 170 } } />
				</Form.Item>
				<Input.Group compact style={ { width: "auto", display: "flex" } }>
					{/* 货品包含 */}
					<Form.Item className={ s.condition1 } name="itemSkuType">
						<GoodsContainOnlyContainType
							size="small"
							onChange={ itemSkuTypeChange }
						/>
					</Form.Item>
					{/* 商品简称 */}
					<Form.Item name="item">
						<InputArrayMulti
							size="small"
							placeholder={ (itemSkuType === 1 || itemSkuType === 2) ? "商品名称/简称/编码/ID" : "简称/货品编码" } 
							maxInputNum={ 200 }
							maxTagCount={ 1 }
							maxTagTextLength={ 8 }
							open={ false }
							tokenSeparators={ null }
							style={ { flex: 1, width: 212 } }
						/>
						{/* <InputMulti size="small" placeholder={ (itemSkuType === 1 || itemSkuType === 2) ? "商品名称/简称/编码/ID" : "货品名称/简称" } maxInputNum={ 1000 } style={ { width: '180px' } } /> */}
					</Form.Item>
					{/* 规格编码 */}
					<Form.Item name="sku" >
						<InputArrayMulti
							size="small"
							placeholder={ (itemSkuType === 1 || itemSkuType === 2) ? "规格名称/编码/别名" : "规格名称/编码/别名" }
							maxInputNum={ 200 }
							maxTagCount={ 1 }
							maxTagTextLength={ 8 }
							open={ false }
							tokenSeparators={ null }
							style={ { flex: 1, width: 212 } }
						/>
						{/* <InputMulti size="small" placeholder={ (itemSkuType === 1 || itemSkuType === 2) ? "规格名称/编码/别名" : "货品规格编码" } maxInputNum={ 1000 } style={ { width: '160px' } } /> */}
					</Form.Item>
				</Input.Group>
				<Form.Item name="market">
					<InputMulti size="small" placeholder="市场" maxInputNum={ 50 } style={ { width: 170 } } />
				</Form.Item>
				<Form.Item name="stall">
					<InputMulti size="small" placeholder="档口" maxInputNum={ 50 } style={ { width: 170 } } />
				</Form.Item>
				<Form.Item name="supplierName">
					<InputMulti size="small" placeholder="供应商" maxInputNum={ 50 } style={ { width: 170 } } />
				</Form.Item>
				<Form.Item>
					<Button size="small" type="primary" onClick={ _onSearch }>查询</Button>
					<Button size="small" className="r-ml-10" onClick={ _onReset }>重置</Button>
				</Form.Item>
				
			</Form>
		</div>
	);

};

export default Search;
