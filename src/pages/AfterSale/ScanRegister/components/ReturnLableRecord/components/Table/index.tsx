import React, { useEffect, useState } from "react";
import { Table, Popover, Button, Tooltip } from "antd";
import { TablePaginationConfig } from "antd/es/table";
import { SettingOutlined, SwapOutlined, DownloadOutlined } from "@ant-design/icons";
import _ from 'lodash';
import Image from "@/components/Image";
import message from '@/components/message';
import PrintCenter from '@/print/index';
import GoodsContentSetting from "./GoodsContentSetting";
import { DEFAULT_PRODUCT_CONTENT_SETTING, LOCAL_PRODUCT_SETTING, PRODUCT_CONTENT_ENUM, PRODUCT_SETTING_OBJECT } from "@/pages/AfterSale/ScanRegister/constants";
import { local } from "@/libs/db";
import s from './index.module.scss';
import { getPlatformDetailLink } from "@/pages/AfterSale/TradeList/utils";
import { getTradePlatformLabel } from "@/pages/Trade/utils";
import userStore from "@/stores/user";
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { ImgPlus } from "@/pages/Report/Bhd/BhdList/components/BhdOrderList/BhdTd";
import { DEFAULT_IMG } from "@/constants";
import { BQ_PRINT_TYPE } from "@/pages/Trade/constants";
import { BasicTable } from '@/components/SearchTableVirtual';
import { LabelPrintSourceEnum } from '@/constants/labelPush';
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import RefundItemType from '@/pages/AfterSale/components/RefundItemType';
import { isSourceScm, isAfterSaleSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import { getImageThumbnail } from "@/utils/img.scale";


type Props = {
	loading?:boolean
	onDownload?:()=>void
	dataSource:any[]
	pagination:TablePaginationConfig
}

export const renderGoodsInfo = (_, record, { productContentList, isShowZeroStockVersion, productSetting = PRODUCT_SETTING_OBJECT, productContentEnum = PRODUCT_CONTENT_ENUM, showLabel = false }) => {
	let _record = [];
	if (!Array.isArray(record)) {
		_record.push(record);
	} else {
		_record = [...record];
	}

	return _record?.map((recordItem) => {
		const showImg = productContentList.includes(productContentEnum.货品图片) || productContentList.includes(productContentEnum.商品图片);
		let imgSrc = recordItem.picUrl;
		if (productContentList.includes(productContentEnum.货品图片)) {
			imgSrc = recordItem.sysSkuPicUrl || recordItem.sysPicUrl;
		}
		const getLinkHref = getPlatformDetailLink(recordItem.platform, recordItem.numIid);
		const goodsInfoItems = [];
		const skuInfoItems = [];
		productSetting.forEach(i => {
			i.list.forEach(item => {
				const allowShow = productContentList.includes(item.value) && ![productContentEnum.货品图片, productContentEnum.商品图片].includes(item.value);
				// 由于商品简称和货品简称公用一个字段，所以需要特殊过滤
				if (item.value === PRODUCT_CONTENT_ENUM.简称) {
					if (isShowZeroStockVersion && item.showInStockVersion) {
						return;
					}
					if (!isShowZeroStockVersion && item.showInZeroStockVersion) {
						return;
					}
				}
				if (allowShow) {

					const itemContent = (
						<Tooltip title={ item.label }>
							<p style={ { textAlign: "left" } }>{showLabel ? (<span className="r-mr-4" style={ { color: "rgba(0,0,0,0.45)" } }>{`${item.label}:`}</span>) : ""}{recordItem[item.value]}</p>
						</Tooltip>
					);
					if (i.key == "good") {
						goodsInfoItems.push(itemContent);
					} else if (i.key == "sku") {
						skuInfoItems.push(itemContent);
					}
				}
			});
		});

		// 本次处理数量
		if (recordItem?.nowConfirmNum || recordItem?.nowConfirmNum === 0) {
			skuInfoItems.push(
				<span className="r-flex r-ai-c">
					<RefundItemType data={ recordItem } />
					{
						recordItem.nowConfirmNum > 1 ? (
							<div className="r-c-error r-ml-4" style={ { background: '#FFD591', padding: '0 4px', lineHeight: '20px' } }>x {recordItem.nowConfirmNum}</div>
						) : (
							<div className="r-c-error r-ml-4">x {recordItem.nowConfirmNum}</div>
						)
					}
				</span>
			);
		}


		const imgSize = productContentList.includes(PRODUCT_CONTENT_ENUM.小图) ? '300px'
			: productContentList.includes(PRODUCT_CONTENT_ENUM.中图) ? '500px'
				: productContentList.includes(PRODUCT_CONTENT_ENUM.大图) ? '800px' : '500px';

		return (
			<div className={ s['goods-container'] }>
				<div hidden={ !showImg } className={ !showImg ? 'r-hidden' : '' }>
					<Popover
						// overlayClassName={ s['popover'] }
						placement="right"
						content={ <Image src={ imgSrc } style={ { width: imgSize, height: imgSize } } /> }
					>
						<a
						// eslint-disable-next-line no-script-url
							href={ !userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(recordItem) ? undefined : getLinkHref || 'javascript:void(0)' }
							target={ getLinkHref ? '_blank' : '_self' }
							rel="noopener noreferrer"
							className="r-flex r-ai-c r-print-img"
							style={ { width: 60 } }
						>
							{showLabel ? (
								<ImgPlus
									src={ imgSrc || DEFAULT_IMG }
									width={ 60 }
									fallback={ DEFAULT_IMG }
								/>
							) : (
								<Image
									src={ getImageThumbnail({ noScale: false, url: imgSrc, width: 60, height: 60, quality: 100 }) }
								/>
							)}
							{showLabel}
						</a>
					</Popover>

				</div>
				<div>
					<div className="goods-info">
						{goodsInfoItems}
					</div>
					<div className="sku-info">
						{skuInfoItems}
					</div>

				</div>

			</div>
		);
	});

};

const CustomTable : React.FC<Props> = (props:Props) => {
	const { loading, onDownload, dataSource, pagination } = props;

	const [tableLoading, setTableLoading] = useState(loading);
	const [selectedRows, setSelectedRows] = useState([]);
	const [isShowShopName, setIsShowShopName] = useState(loading);
	const [tableData, setTableData] = useState([]);
	const [productContentList, setProductContentList] = useState(DEFAULT_PRODUCT_CONTENT_SETTING);
	const { isShowZeroStockVersion } = userStore;

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `售后扫描登记-退货标签记录: 【 ${dataType} 】`,
			data: {
				...data
			}
		});
	};

	const GoodsContentSettingOnOk = (list) => {
		console.log(15, list);
		setProductContentList(list);
	};
	const renderGoodsContentColHeader = (
		<div className="r-flex r-ai-c">
			<span>产品内容</span>
			<span className="r-pointer r-ml-4">
				<GoodsContentSetting onOk={ GoodsContentSettingOnOk } storageKey={ LOCAL_PRODUCT_SETTING }>
					<SettingOutlined />
				</GoodsContentSetting>
			</span>
		</div>
	);

	const renderShopInfoColHeader = (
		<div onClick={ () => setIsShowShopName(!isShowShopName) }>
			<span className="r-fw-500 r-flex r-ai-c">
				{isShowShopName ? "店铺名称" : "店铺简称"}
				<Popover content={ `点击切换${isShowShopName ? "店铺简称" : "店铺名称"}` }>
					<SwapOutlined className="r-pointer r-ml-4" style={ { transform: 'rotate(90deg)' } } />
				</Popover>
			</span>
		</div>
	);
	const handleProductContentList = () => {
		let productSetting = local.get(LOCAL_PRODUCT_SETTING);
		if (productSetting) {
			setProductContentList(productSetting);
		}
	};

	const columns = [
		{
			title: "序号2",
			dataIndex: "index",
			width: 60,
			render(_, record, index) {
				return index + 1;
			}
		},
		{
			title: renderGoodsContentColHeader,
			dataIndex: "goodsInfo",
			render(_, record) {
				return renderGoodsInfo(_, record, { productContentList, isShowZeroStockVersion });
			}
		},
		{
			title: "市场/档口",
			dataIndex: "market",
			render(_, record) {
				return `${record.market || "-"}/${record.stall || "-"}`;
			}
		},
		{
			title: "供应商",
			dataIndex: "supplierName",
		},
		{
			title: "退货唯一码",
			dataIndex: "labelId",
		},
		{
			title: "快递公司",
			dataIndex: "refundWaybillName",
		},
		{
			title: "快递单号",
			dataIndex: "refundWaybillNo",
		},
		{
			title: "售后订单号",
			dataIndex: "refundId",
		},
		{
			title: "系统单号",
			dataIndex: "tid",
		},
		{
			title: "销售订单号",
			dataIndex: "ptTid",
		},
		{
			title: renderShopInfoColHeader,
			dataIndex: "shop",
			render(_, record) {
				return (
					<div>
						<span>{getTradePlatformLabel(record.platform)}</span>
						<span>{isShowShopName ? !userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(record) ? '****' : record.sellerNick : record.sellerAbbreviation}</span>
					</div>
				);
			}
		},
		{
			title: "操作人",
			dataIndex: "opUserStr",
		},
		{
			title: "生成时间/打印时间",
			dataIndex: "gmtCreated",
			render(_, record) {
				return `${record.gmtCreated || "-"}/${record.printTime || "-"}`;
			}
		}
	];

	const downloadData = () => {
		if (dataSource.length < 1) {
			message.error("暂无可导出的数据！");
			return;
		}
		downloadCenter({
			requestParams: onDownload && onDownload(),
			fileName: `退货标签记录`,
			module: ModulesFunctionEnum.退货标签记录
		  });
		// onDownload && onDownload();
	};

	const rowSelection = {
		selectedRowKeys: selectedRows?.map(i => i.labelId),
		onChange: (selectedRowKeys, selectedRows) => {
			setSelectedRows(selectedRows);
		},
	};

	const onPrint = () => {
		if (selectedRows.length < 1) {
			message.error("请勾选需要打印的标签");
			return;
		}
		PrintCenter.printThdXbq({
			orderList: selectedRows.map(i => ({
				...i,
				refundLabelId: i.labelId,
				takeGoodsLabelPrintSource: LabelPrintSourceEnum.售后退货标签记录退货标签,
			}))
		});
		customLogPost('打印退货标签', { orderList: selectedRows?.map(i => {
			return {
				tid: i?.tid,
				skuid: i?.skuid,
				refundId: i?.refundId,
				refundLabelId: i?.refundLabelId,
				labelId: i?.labelId,
				refundItemType: i?.refundItemType,
			};
		}) });

		console.log(196, selectedRows);
	};

	// 退货清单模板
	const onClickReturnListTemp = () => {
		PrintCenter.showTemplateMain({ printType: 'thqd' });
	};

	const getPrintData = () => {
		let data = _.cloneDeep(selectedRows);
		// console.log('%c [ data ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', data);

		// 打印数据记录日志
		let logData = {
			list: [],
			printList: []
		};

		// 整理下数据，相同numiid+skuid的合并
		const mergedData = {};
		// 遍历原始数据数组
		data.forEach(item => {
			// 生成 numIid 和 skuId 的组合键
			let key = '';
			if (item.numIid || item.skuId) {
				key = `${item.numIid}_${item.skuId}`;
			} else {
				key = `${item.sysItemId}_${item.sysSkuId}`;
			}
			console.log('key', key);
			// 如果这个键在 mergedData 中不存在，则初始化它
			if (!mergedData[key]) {
				mergedData[key] = {
					num: 1, // 初始数量设为1
					...item,
					labelIdList: [item.labelId]
				};
			} else {
				// 如果键已存在，则增加数量
				mergedData[key].num++;
				mergedData[key].labelIdList.push(item.labelId);
			}

			logData.list.push({
				labelId: item.labelId,
				outerId: item.outerId,
				skuOuterId: item.skuOuterId,
				sysOuterId: item.sysOuterId,
				sysSkuOuterId: item.sysSkuOuterId,
				market: item.market,
				stall: item.stall,
				supplierName: item.supplierName,
			});
		});
		data = Object.values(mergedData);

		// 提取相同档口
		let stallGroups = {};
		data.forEach(item => {
			const { num = 1, supplierName = '', stall = '', market = '', sysSkuCostPrice = 0 } = item;

			if (!stallGroups[stall]) {
				stallGroups[stall] = {
					market,
					stall,
					supplierName, // 供应商 一个档口对应一个供应商
					goodsNum: 0, // 总数量
					goodsPrice: 0, // 总金额
					goodsInfo: []
				};
			}
			// 增加goodsNum计数
			stallGroups[stall].goodsNum += num; // 每条数据默认数量1
			stallGroups[stall].goodsPrice += (Number(sysSkuCostPrice) * num); // 总金额
			stallGroups[stall].goodsInfo.push(item);
		});

		// 提取stall为空的条目  放前面
		const emptyStallItems = stallGroups[''] ? [stallGroups['']] : [];
		delete stallGroups[''];
		const result = emptyStallItems.concat(Object.values(stallGroups));

		logData.printList = result?.map(item => {
			return {
				...item,
				goodsInfo: item?.goodsInfo?.map(i => {
					return {
						labelIdList: i?.labelIdList,
						numIid: i?.numIid,
						skuId: i?.skuId,
					};
				})
			};
		});

		customLogPost('打印退货清单', logData);
		return result;
	};

	// 打印退货清单
	const onPrintReturnList = () => {
		if (selectedRows.length < 1) {
			message.error("请勾选需要打印的商品");
			return;
		}
		message.loading({
			content: '正在加载中',
			key: 'batchPrintThqdList',
			duration: 0,
		});
		try {
			let printData = getPrintData();
			// console.log('%c [ printData ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', printData, selectedRows);
			message.destroy('batchPrintThqdList');
			PrintCenter.batchPrintThqdList({
				orderList: printData,
				printType: 'thqd'
			});
		} catch (error) {
			message.destroy('batchPrintThqdList');
		}
	};

	useEffect(() => {
		handleProductContentList();
	}, []);

	useEffect(() => {
		setTableData(dataSource);
	}, [dataSource]);

	useEffect(() => {
		setTableLoading(loading);
	}, [loading]);

	useEffect(() => {
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.退货清单);
	}, []);

	return (
		<div>
			<div className="r-mb-16">
				<Button
					type="primary"
					onClick={ downloadData }
					// disabled={ !response.dataSource || response?.dataSource?.length < 1 }
				>
					<DownloadOutlined />
					导出数据
				</Button>
			</div>
			<div className="r-mb-40">
				<BasicTable
					rowSelection={ rowSelection }
					rowKey="labelId"
					loading={ tableLoading }
					dataSource={ dataSource }
					columns={ columns }
					pagination={ pagination }
					paddingBottom={ 60 }
					sticky={ { stickyTop: 92 } }
				/>
			</div>

			<div className={ s["print-btn-container"] }>
				<Button type="primary" onClick={ onPrint }>打印退货标签</Button>
				<Button onClick={ onPrintReturnList } className="r-ml-20" data-point={ Pointer.售后_售后扫描登记_退货标签记录_打印退货清单按钮 }>打印退货清单</Button>
				<span className="r-relative">
					<Button type="link" onClick={ onClickReturnListTemp } className="r-c-primary r-absolute" data-point={ Pointer.售后_售后扫描登记_退货标签记录_退货清单模板按钮 }>退货清单模版</Button>
				</span>
			</div>
		</div>
	);

};

export default CustomTable;
