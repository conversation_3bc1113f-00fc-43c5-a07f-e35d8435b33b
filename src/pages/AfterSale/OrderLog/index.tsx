
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Button, Dropdown, Form, Input, Modal, Select, Tooltip, Tag, Popover } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useActivate } from "react-activation";
import cs from 'classnames';
import { debounce } from 'lodash';
import { CopyOutlined, SettingOutlined, FileExcelOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import { log } from 'console';
import SearchTable from "@/components/SearchTableVirtual";
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { SearchTableRefProps } from "@/components/SearchTable/SearchTable";
import { DatePickerKey, getCacheDateRange } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import InputMulti from "@/components/Input/InputMulti";
import { SelectOperateLogPageApi, SelectOperateLogSummaryApi } from '@/apis/aftersale/trade';
import { getTradePlatformLabel } from '@/pages/Trade/utils';
import { copyToPaste, splitFxgTid, useQuery } from '@/utils';
import Icon from '@/components/Icon';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import { ExceptionWarnTopTabs } from './components/TopTab';
import { getMultiShops, getMultiShopsWithFilter, getShopName, isSourceScm, isAfterSaleSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import userStore from '@/stores/user';
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import { PLAT_KTT, PLAT_DW, PLAT_YZ } from '@/constants';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import { downloadCenter } from '@/pages/Index/DownloadCenter/utils';
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import { getPlatformTradeLink, afterSaleTypeText } from '../TradeList/utils';
import { QuickSearchStatusEnum, OperationTypeEnum, OperationResultEnum } from './constants';
import message from "@/components/message";
import s from './index.module.scss';


// 默认最近1周 最长不超过3个月由后端限制
const initialRangeTime = getCacheDateRange(DatePickerKey.aftersale_orderLog) || [dayjs().subtract(6, 'days').startOf('day'), dayjs().endOf('day')];

const defaultParams = {
	quickSearchStatus: undefined,
	rangeTime: initialRangeTime,
	platformInfo: undefined,
	opTypeCodeList: [],
	opResult: undefined,
	refundId: undefined,
	sid: undefined,
	ptTid: undefined,
	operateDetail: undefined,
};

const OrderLog: React.FC = (props) => {
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [numInfo, setNumInfo] = useState<any>({}); // 各快捷查询tab 数量
	const [searchParams, setSearchParams] = useState({});
	const [loading, setLoading] = useState(false);

	const [form] = useForm();
	const ref = useRef<SearchTableRefProps>();
	
	// 获取快捷查询总数
	const getQuickSearchNum = async(search) => {
		try {
			let totalData = await SelectOperateLogSummaryApi(search);
			setNumInfo(totalData);
		} catch (error) {
			console.log(error);
		}
		
	};

	const fetchSystemList = async(info) => {
		const {
			quickSearchStatus, // 快捷查询类型
			searchTime,
			tids,
			ptTids,
			platformInfo = {},
			pageSize = 10, 
			pageNo = 1,
			timeType = "create",
			rangeTime = initialRangeTime,
			opTypeCodeList = [], // 操作类型
			opResult, // 操作结果
			operateDetail, // 操作内容
			refundId,
			sid,
			ptTid,
			...restInfo
		} = info;

		
		let { plats, plat_sellerIds } = platformInfo;
		let multiShopS = await getMultiShopsWithFilter({ plats, plat_sellerIds, hidePlatforms: [PLAT_KTT, PLAT_DW, PLAT_YZ] });

		const params = {
			pageSize,
			pageNo,
			multiShopS,
			isPlatformEmptyQuery: !plat_sellerIds?.length && !plats?.length,
			createTimeStart: searchTime?.[0]?.format("YYYY-MM-DD HH:mm:ss") ?? '',
			createTimeEnd: searchTime?.[1]?.format("YYYY-MM-DD HH:mm:ss") ?? '',
			opTypeCodeList,
			opResult,
			operateDetail,
			refundIdList: refundId ? refundId?.trim()?.split(',') : [],
			sidList: sid ? sid?.trim()?.split(',') : [],
			ptTidList: ptTid ? ptTid?.trim()?.split(',') : [],
			...restInfo,
		};

		if (rangeTime) {
			const [startTime, endTime] = rangeTime;
			if (!startTime && !endTime) {
				message.error("查询时间不能为空");
				return;
			}
			if (!startTime) {
				message.error("开始时间不能为空");
				return;
			}
			if (!endTime) {
				message.error("结束时间不能为空");
				return;
			}
			params[`${timeType}TimeStart`] = rangeTime[0].format('YYYY-MM-DD HH:mm:ss');
			params[`${timeType}TimeEnd`] = rangeTime[1].format('YYYY-MM-DD HH:mm:ss');
		}

		// 获取快捷筛选数量
		if (params.pageNo == 1) {
			getQuickSearchNum(params);
		}
        
		setSearchParams(params);
		setLoading(true);
		console.log('%c [ 请求参数 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
        
		return SelectOperateLogPageApi(params);
	};

	const operationTypeOptions = useMemo(() => {
		const quickSearchStatusArray = Object.entries(OperationTypeEnum)
			// .filter(([key, value]) => isNaN(Number(key))) // 过滤掉反向映射的数字键
			.map(([label, value]) => ({ label, value }));
		return quickSearchStatusArray;
	}, []);

	// const operationTypeObj = useMemo(() => {
	// 	let obj = {};
	// 	for (const [key, value] of Object.entries(OperationTypeEnum)) {
	// 		obj[value] = key;
	// 	}
	// 	return obj;
	// }, []);

	// 快捷查询选项
	const tabList = useMemo(() => {
		return [
			{
				key: QuickSearchStatusEnum.确认收货未触发退款,
				text: '确认收货未触发退款',
				num: numInfo[QuickSearchStatusEnum.确认收货未触发退款] ?? 0,
				point: Pointer['售后_售后管理_售后单日志_点击确认收货未触发退款'],
				tip: true,
			},
			{
				key: QuickSearchStatusEnum.退款失败,
				text: '退款失败',
				num: numInfo[QuickSearchStatusEnum.退款失败] ?? 0,
				point: Pointer['售后_售后管理_售后单日志_点击退款失败'],
			},
			{
				key: QuickSearchStatusEnum['生成换货/补发手工单失败'],
				text: '生成换货/补发手工单失败',
				num: numInfo[QuickSearchStatusEnum['生成换货/补发手工单失败']] ?? 0,
				point: Pointer['售后_售后管理_售后单日志_点击生成换货补发手工单失败'],
			},
			{
				key: QuickSearchStatusEnum.退货入库失败,
				text: '退货入库失败',
				num: numInfo[QuickSearchStatusEnum.退货入库失败] ?? 0,
				point: Pointer['售后_售后管理_售后单日志_点击退货入库失败'],
			},
			{
				key: QuickSearchStatusEnum['回填换货/补发单号失败'],
				text: '回填换货/补发单号失败',
				num: numInfo[QuickSearchStatusEnum['回填换货/补发单号失败']] ?? 0,
				point: Pointer['售后_售后管理_售后单日志_点击回填换货补发单号失败'],
			},
			{
				key: QuickSearchStatusEnum['退款成功'],
				text: '退款成功',
				num: numInfo[QuickSearchStatusEnum['退款成功']] ?? 0,
				point: Pointer['售后_售后管理_售后单日志_点击退款成功'],
			},
			{
				key: QuickSearchStatusEnum['确认收货成功'],
				text: '确认收货成功',
				num: numInfo[QuickSearchStatusEnum['确认收货成功']] ?? 0,
				point: Pointer['售后_售后管理_售后单日志_点击确认收货成功'],
			}
		];
	}, [numInfo]);

	// 点击tag删除选中项
	const handleRemoveTag = (value: any) => {
		console.log('%c [ value ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
		const oldValue = form.getFieldValue('opTypeCodeList') || [];
		const newValues = oldValue.filter((item) => item !== value);
		
		form.setFieldsValue({
			opTypeCodeList: newValues, // 操作类型
			quickSearchStatus: undefined,
		});
	};

	const FormFieldList: FormItemConfig[] = [
		{
			label: "",
			name: 'quickSearchStatus',
			colProps: {
				span: 24
			},
			children: <ExceptionWarnTopTabs tabList={ tabList } loading={ loading } />
		},
		{
			name: 'rangeTime',
			label: "",
			children: (
				<KdzsDateRangePicker1 style={ { width: 159 } } cacheQuickChoose datePickerKey={ DatePickerKey.aftersale_orderLog } useServeTime />
			),
		},
		{
			name: 'platformInfo',
			label: '',
			children: <ShopMultiSelect style={ { width: 160 } } size="small" hidePlatforms={ [PLAT_KTT, PLAT_DW, PLAT_YZ] } />
		},
		{
			name: 'opTypeCodeList',
			label: '',
			children: (
				<Select 
					maxTagCount="responsive" 
					size="small" 
					style={ { width: 160 } } 
					placeholder="操作类型" 
					mode="multiple" 
					allowClear
					showArrow
					showSearch
					options={ operationTypeOptions }
					optionFilterProp="children"
					filterOption={ (input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase()) }
					maxTagPlaceholder={ (omittedValues) => {
						if (omittedValues?.length) {
							return (
								<Popover
									placement="bottom"
									title=""
									content={ (
										<div 
											className={ s.responsiveSelect } 
											onMouseDown={ (e) => e.stopPropagation() }
										>
											{
												omittedValues.map((v) => (
													<Tag
														key={ v.value }
														closable
														onClose={ (e) => {
															e.preventDefault(); // 防止触发 Tooltip 关闭
															handleRemoveTag(v.value);
														} }
													>
														{v.label}
													</Tag>
												))
											}
										</div>
									) }
								>
									+{omittedValues.length}
								</Popover>
							);
						} else {
							return null;
						}
					} }
				/>
			)
		},
		{
			name: 'opResult',
			label: '',
			children: <Select 
				style={ { width: 160 } } 
				placeholder="操作结果" 
				allowClear
				showArrow
				options={ [
					{ label: '成功', value: 1 },
					{ label: '失败', value: 0 }
				] }
			/>,
		},
		{
			name: 'refundId',
			label: "",
			children: <InputMulti maxInputNum={ 1000 } placeholder="售后单号" size="small" style={ { width: 160 } } />,
		},
		{
			name: 'sid',
			label: "",
			children: <InputMulti maxInputNum={ 1000 } placeholder="退货快递单号" size="small" style={ { width: 160 } } />,
		},
		{
			name: 'ptTid',
			label: "",
			children: <InputMulti maxInputNum={ 1000 } placeholder="订单编号" size="small" style={ { width: 160 } } />,
		},
		{
			name: 'operateDetail',
			label: "",
			children: <Input size="small" placeholder="操作内容" style={ { width: 160 } } />,
		}

	];

	// 表格列定义，与AntdTable使用一致
	const columns: ColumnsType<any> = [
		{
			title: '序号',
			key: 'index',
			width: 50,
			render: (text, record, index) => {
				return index + 1;
			}
		},
		{
			title: '操作时间',
			width: 120,
			minWidth: 120,
			dataIndex: 'gmtCreated',
			render(value) {
				return value ? (
					// <div>{dayjs(value).format('YYYY-MM-DD HH:mm:ss')}</div>
					<div>{value}</div>
				) : '--';
			}
		},
		{
			title: '店铺/订单/售后单号',
			key: 'sellerNick',
			width: 220,
			minWidth: 180,
			dataIndex: 'sellerNick',
			className: 'table-right-border',
			render: (text, record, index) => {
				return (
					<div>
						<div hidden={ record.isNoTradeMess } className={ cs('r-fc-black-65', 'r-wb-bw', 'r-flex', 'r-ai-c') }>
							<PlatformIcon platform={ record['platform'] } />
							<span>{!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isAfterSaleSourceScm(record) ? '****' : getShopName({ plat: record?.platform, sellerNick: record?.sellerNick || "" }) }</span>
						</div>


						{
							record.isNoTradeMess ? (
								<div className="r-c-error r-mt-4">无主件</div>
							) : (
								<div className="r-flex r-mt-4 r-c-black85">
									<span style={ { flexShrink: 0 } }>订单：</span>
									<div>
										<a
											className={ cs('r-fc-1890FF') }
											href={ getPlatformTradeLink(record['platform'], record).trade }
											target="_blank"
											rel="noreferrer"
										>{record['ptTid'] || '--'}
										</a>
										<CopyOutlined 
											hidden={ !record['ptTid'] } 
											onClick={ (event) => { 
												event.stopPropagation();
												copyToPaste(splitFxgTid(record['ptTid'])); 
											} } 
											className={ cs('r-fc-black-65 r-ml-2', 'r-pointer') } 
										/>
									</div>
								</div>
							)
						}
						

						<div className="r-flex r-mt-4 r-c-black85">
							<span style={ { flexShrink: 0 } }>售后：</span>

							<div>
								{
									record.isNoTradeMess ? (
										<span>{record['refundId']}</span>
									) : (
										<a
											className={ cs('r-fc-1890FF') }
											href={ getPlatformTradeLink(record['platform'], record).refund }
											target="_blank"
											rel="noreferrer"
										>{record['refundId']}
										</a>
									)
								}
							
									
								<CopyOutlined 
									hidden={ !record['refundId'] } 
									onClick={ (event) => { 
										event.stopPropagation();
										copyToPaste(record['refundId']); 
									} } 
									className={ cs('r-fc-black-65 r-ml-2', 'r-pointer') }
								/>
							</div>
						</div>
					</div>
				);
			}
		},
		{
			title: '售后类型',
			key: 'afterSaleType',
			width: 120,
			dataIndex: 'afterSaleType',
			render: (text, record, index) => {
				// 售后类型,空全部,1仅退款,2退货退款,3换货,4补差价,5补发货品
				return (
					<div>{afterSaleTypeText?.[text]}</div>
				);
			}
		},
		{
			title: '退款金额',
			dataIndex: 'refundAmount',
			width: 120,
			render: (text, record, index) => {
				return (
					<div>
						<div className={ cs('r-fc-black-65') }>退款：{text}</div>
					</div>
				);
			}
		},
		{
			title: '退货快递单号',
			dataIndex: 'sid',
			width: 140,
			render: (text, record, index) => {
				return (
					<div>{record.sid || '--'}</div>
				);
			}
		},
		{
			title: '操作类型',
			dataIndex: 'opName',
			width: 120,
			render: (text, record, index) => {
				return (
					<div>
						{/* <div>{operationTypeObj?.[record.opType]}</div> */}
						{text}
					</div>
				);
			}
		},
		{
			title: '操作内容',
			dataIndex: 'detail',
			width: 360,
			minWidth: 180,
			render: (text, record, index) => {
				return (
					<div>{record.detail}</div>
				);
			}
		},
		{
			title: '操作结果',
			width: 120,
			dataIndex: 'opResult',
			render: (value, row, index) => {
				const isTrue = value === OperationResultEnum.成功;
				return (
					<div>
						<div>{ isTrue ? '成功' : '失败'}</div>
						{ !isTrue && row?.errMsg && (
							<div className="r-c-error">（{row?.errMsg}）</div>
						)}
					</div>
				);
			},
		},
		{
			title: '操作人',
			width: 120,
			dataIndex: 'opNick',
			render: (value, row, index) => {
				return <div>{row.opNick}</div>;
			}
		}
	];

	const onQuickSearchClick = (quickSearchStatus: QuickSearchStatusEnum) => {
		switch (quickSearchStatus) {
			case QuickSearchStatusEnum.确认收货未触发退款:
				form.setFieldsValue({
					...defaultParams,
					quickSearchStatus: QuickSearchStatusEnum.确认收货未触发退款,
					rangeTime: [dayjs().startOf('day'), dayjs().endOf('day')], // 时间今天
					opTypeCodeList: [OperationTypeEnum.自动策略], // 操作类型
			        operateDetail: '执行但未匹配售后自动策略', // 操作内容
				});
				break;
			case QuickSearchStatusEnum.退款失败:
				form.setFieldsValue({
					...defaultParams,
					quickSearchStatus: QuickSearchStatusEnum.退款失败,
					rangeTime: [dayjs().startOf('day'), dayjs().endOf('day')], // 时间今天
					opTypeCodeList: [OperationTypeEnum.同意退款, OperationTypeEnum.同意退货退款, OperationTypeEnum.AG退款], // 操作类型
					opResult: OperationResultEnum.失败, // 操作结果
				});
				break;
			case QuickSearchStatusEnum['生成换货/补发手工单失败']:
				form.setFieldsValue({
					...defaultParams,
					quickSearchStatus: QuickSearchStatusEnum['生成换货/补发手工单失败'],
					rangeTime: [dayjs().startOf('day'), dayjs().endOf('day')], // 时间今天
					opTypeCodeList: [OperationTypeEnum['生成换货/补发手工单']], // 操作类型
					opResult: OperationResultEnum.失败, // 操作结果
				});
				break;
			case QuickSearchStatusEnum.退货入库失败:
				form.setFieldsValue({
					...defaultParams,
					quickSearchStatus: QuickSearchStatusEnum.退货入库失败,
					rangeTime: [dayjs().startOf('day'), dayjs().endOf('day')], // 时间今天
					opTypeCodeList: [OperationTypeEnum.退货入库], // 操作类型
					opResult: OperationResultEnum.失败, // 操作结果
				});
				break;
			case QuickSearchStatusEnum['回填换货/补发单号失败']:
				form.setFieldsValue({
					...defaultParams,
					quickSearchStatus: QuickSearchStatusEnum['回填换货/补发单号失败'],
					rangeTime: [dayjs().startOf('day'), dayjs().endOf('day')], // 时间今天
					opTypeCodeList: [OperationTypeEnum['回填换货/补发单号']], // 操作类型
					opResult: OperationResultEnum.失败, // 操作结果
				});
				break;
			case QuickSearchStatusEnum['退款成功']:
				form.setFieldsValue({
					...defaultParams,
					quickSearchStatus: QuickSearchStatusEnum['退款成功'],
					rangeTime: [dayjs().startOf('day'), dayjs().endOf('day')], // 时间今天
					opTypeCodeList: [OperationTypeEnum.同意退款, OperationTypeEnum.同意退货退款, OperationTypeEnum.AG退款], // 操作类型
					opResult: OperationResultEnum.成功, // 操作结果
				});
				break;
			case QuickSearchStatusEnum['确认收货成功']:
				form.setFieldsValue({
					...defaultParams,
					quickSearchStatus: QuickSearchStatusEnum['确认收货成功'],
					rangeTime: [dayjs().startOf('day'), dayjs().endOf('day')], // 时间今天
					opTypeCodeList: [OperationTypeEnum.确认收货], // 操作类型
					opResult: OperationResultEnum.成功, // 操作结果
				});
				break;
			default:
				form.setFieldsValue({
					...defaultParams,
				});
				break;
		}

		setTimeout(() => {
			ref?.current?.submit();
		}, 200);
	};

	const onFieldsChange = (changedValues, allValues) => {
		// console.log('%c [ changedValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues, allValues);
		
		// 快捷查询，需要把对应的查询条件解析出来回显
		if ('quickSearchStatus' in changedValues) {
			onQuickSearchClick(changedValues?.quickSearchStatus);
		} else if (allValues.quickSearchStatus) {
			// 如果有快捷查询，且其他搜索项变更，需要取消快捷查询项选中状态
			form.setFieldsValue({
				quickSearchStatus: undefined,
			});
		}
	};

	const handleDown = debounce(async() => {
		console.log('%c [ searchParams ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', searchParams);

		sendPoint(Pointer['售后_售后管理_售后单日志_点击导出']);

		await downloadCenter({
			requestParams: {
				...searchParams,
				// pageSize: 999999,
			},
			fileName: '售后单日志',
			module: ModulesFunctionEnum.售后管理_售后单日志
		});
	}, 1000, { leading: true, trailing: false });


	const responseAdapter = (data, params) => {
		const dataSource = data?.list?.map((i, index) => ({
			rowId: `${i.id}`,
			...i,
		}));

		// setDataSource(dataSource);
		setLoading(false);
		return {
			list: dataSource,
			total: data.total
		};
	};

	const tableExtraFn = (sortNode) => {
		return (
			<>
				<div className="r-flex r-ai-c r-jc-fe r-mb-16">
					<Button icon={ <FileExcelOutlined /> } size="middle" className="r-mr-0" onClick={ () => { handleDown(); } }>导出</Button>
					
					<div className="r-ml-10">{sortNode}</div>
				</div>
			</>
		);
	};

	const _onChange = (pagination, filters, sorter, { action }) => {
		// 切换分页需要重置勾选项
		if (action == 'paginate') {
			
		}
	};

	useEffect(() => {
		return () => {
			setLoading(false);
			setDataSource([]);
		};
	}, []);

	useActivate(() => {
		console.log('组件被激活');
		getQuickSearchNum(searchParams);
	});

	return (
		<div className={ cs("r-mt-8", s.orderLog) }>
			<SearchTable
				ref={ ref }
				pageSizeId="AfterSaleOrderLog"
				form={ form }
				fetchData={ fetchSystemList }
				responseAdapter={ responseAdapter }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				rowFormConfig={ {
					defaultParams,
					formList: FormFieldList,
					size: 'small',
					colProps: {
					}
				} }
				baseTableConfig={ {
					tableExtraFn,
					// dataSource,
					onFieldsChange,
					rowKey: 'rowId',
					columns,
					pagination: {
						pageSizeOptions: [10, 20, 50, 100, 200, 500, 1000],
					},
					cachePgination: true,
					isStickyHeader: true,
					stickyTop: 0,
					headerColSet: {
						resizeId: `AfterSaleOrderLog_width_${userStore?.userInfo?.userId}`,
					},
				} }
				onChange={ ({ pageNo, pageSize }) => {
					_onChange({ current: pageNo, pageSize }, undefined, undefined, { action: 'paginate' });
				} }
			/>
		</div>
	);
};

export default observer(OrderLog);
