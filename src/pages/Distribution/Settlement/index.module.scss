.searchContainer {
	:global {
		.ant-form-item {
			margin: 0px;
		}
	}
}

.condition1 {
	width: 174px;
}

.condition2 {
	width: 348px;
}

.condition3 {
	width: 526px;
}

.tabs {
	:global {
		.ant-tabs-nav {
			margin: 0;
			padding-left: 16px;
			.ant-tabs-tab {
				padding: 12px 16px;
			}
		}
		.ant-tabs-content-holder {
			// padding: 16px 0 16px 12px;
			border-top: 8px solid #f0f2f5;
		}
	}
}

.statusDot {
	display: inline-block;
	margin-right: 8px;
	width: 6px;
	height: 6px;
	border-radius: 100%;
}

.distribution-order-detail {
	:global {
		.ant-form label {
			font-size: 12px;
		}
		td {
			vertical-align: middle;
		}
	}
}

.table-title-header-con {
	display: inline-flex;
	align-items: center;
	min-width: 100%;
	height: 54px;
	background: rgba(0, 0, 0, 0.08);
	user-select: none;
	justify-content: space-between;

	.table-header-item {
		position: relative;
		display: flex;
		align-items: center;
		text-align: left;
		font-size: 13px;
		color: rgba(0, 0, 0, 0.85);
		font-weight: 500;
	}
}

.productContentCol {
	flex: 1;
}
