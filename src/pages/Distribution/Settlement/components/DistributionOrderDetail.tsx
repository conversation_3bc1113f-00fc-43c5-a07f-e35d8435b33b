import cs from 'classnames';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from "react-router-dom";
import { Button, Dropdown, Tooltip, Space, Checkbox } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { observer } from 'mobx-react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import SearchTable from '@/components/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { SearchTableRefProps } from "@/components/SearchTable/SearchTable";
import s from '../index.module.scss';
import InputMulti from '@/components/Input/InputMulti';
import { DatePickerKey } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import { SearchTimeType } from '@/pages/Trade/components/SearchContainer/SearchCondition';
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import { DistributorSelect } from '@/components-biz/Distribution';
import userStore from '@/stores/user';
import { getGoodsInfoSelectOptions, getItemInfoQuerySelectOptions, handleGoodsInfoSearchParams } from '@/pages/Trade/components/SearchContainer/itemInfoUtils';
import { TradeQueryBaseTradeApi } from '@/apis/trade/search';
import { TradeQueryTradeRequest, TradeQueryTradeResponse } from '@/types/trade/search/search';
import { copyMenu, getTradePlatformLabel } from '@/pages/Trade/utils';
import { getMultiShops, getShopName, isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import { downloadCenter } from '@/pages/Index/DownloadCenter/utils';
import { ModulesFunctionEnum } from '@/types/schemas/setting/download';
import { TradeExportGetConfigApi, TradeExportSaveConfigApi } from '@/apis/trade';
import ProductSetting from '@/pages/Trade/components/ListHeader/components/productSetting';
import Icon from '@/components/Icon';
import { calcDomPostion } from '@/utils/util';
import { ProductOrderFragment } from '@/pages/Trade/components/ListItem/components/simpleCom';
import { copyToPaste } from '@/utils';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { GOODS_INCLUDES_OR_EXCLUDS_TYPE } from './constants';
import GoodsSearch from '@/components-biz/GoodsSearch';
import ProductContentCom from "@/pages/Trade/PushedOrder/components/List/ListItem/ProductContentCom";
import PackageOrderHeader from "@/pages/Trade/PushedOrder/components/List/ListHeader/PackageOrderHeader";

import { queryScmTradeApi } from '@/apis/distribution';
import distributionStore from '@/stores/distribution';
import { RefundStatusText, TradeStatusLabel } from '@/utils/enum/trade';
import message from '@/components/message';
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import useGetState from '@/utils/hooks/useGetState';

const DISTRIBUTION_LATITUDE = 2;
const supplierRoutes = ['/distribution/settlement']; // 供应商的路由
const DistributorRoutes = ['/distribution/settlementSale']; // 分销商的路由

const DistributionOrderDetail = () => {
	const { pathname } = useLocation();
	const [form] = useForm();
	const { initProductSet } = distributionStore;
	const { isDistributorAccount, isShowZeroStockVersion, userInfo, isFreeSupplierAccount } = userStore;
	const [dataSource, setDataSource] = useState<TradeQueryTradeResponse["data"]["list"]>([]);
	const [formData, setFormData] = useState({});
	const [itemInfoQueryPlaceholder, setItemInfoQueryPlaceholder] = useState(['商品名称/简称/编码/ID', '规格名称/编码/别名']);
	const ref = useRef<SearchTableRefProps>();
	const [searchParams, setSearchParams] = useState({});
	const [isShowPackageOrderConfig, setIsShowPackageOrderConfig] = useState(false);
	const [scrollTop, setScrollTop] = useState(0);
	const [showProductPagePosition, setShowProductPagePosition] = useState({ x: '', y: '' });
	const [pageExportLoading, setPageExportLoading] = useState(false);
	const [allExportLoading, setAllExportLoading] = useState(false);
	const [goodsIncludesType, setGoodsIncludesType] = useState<GOODS_INCLUDES_OR_EXCLUDS_TYPE>();
	const [goodsExcludesType, setGoodsExcludesType] = useState<GOODS_INCLUDES_OR_EXCLUDS_TYPE>();
	const includesPrefixFormItemName = "goodsIncludeStatus";
	const excludesPrefixFormItemName = "goodsNotIncludeStatus";
	const includesGoodFormItemName = "shortNameIncludingList";
	const excludesGoodFormItemName = "shortNameNotIncludingList";
	const includesSkuFormItemName = "skuIncludingList";
	const excludesSkuFormItemName = "skuNotIncludingList";
	const { distributorList = [], supplierList = [] } = distributionStore;
	const isSupplier = supplierRoutes.includes(pathname) || isFreeSupplierAccount; // 能明确的供应商身份的是免费供应商版
	const isDistributor = DistributorRoutes.includes(pathname) || isDistributorAccount; // 能明确的分销商身份的是免费分销商版
	const [isSameSidShow, setIsSameSidShow, getIsSameSidShow] = useGetState(false);

	useEffect(() => {
		// 我是供应商
		if (isSupplier && distributorList?.length) {
			let val = distributorList?.[0]?.saleUserId;
			form.setFieldsValue({ distributorUserIds: [val] });
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [distributorList, isSupplier]);
	useEffect(() => {
		if (isDistributor && supplierList?.length) {
			let val = supplierList?.[0]?.supplierUserId;
			form.setFieldsValue({ supplierUserIdList: [val] });
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [isDistributor, supplierList]);
	// 商品包含 相关
	const itemInfoQueryIncludesList = useMemo(() => {
		let version = userInfo?.version;
		if (isDistributor) {
			version = 2;
		}
		return getGoodsInfoSelectOptions(version, true);
	}, [userInfo?.version, isDistributor]);

	const prefixOptionsIncludesOnChange = v => {
		setGoodsIncludesType(v);
	};
	const itemInfoQueryIncludesPlaceholder = () => {
		const type = form.getFieldValue(includesPrefixFormItemName);
		return itemInfoQueryIncludesList.filter(i => i.value == type)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名'];
	};

	const getIncludesGoodsSearchComponentProps = useCallback(() => {
		return ({
			prefixOptions: itemInfoQueryIncludesList,
			goodMaxInputNum: 50,
			skuMaxInputNum: 50,
			prefixFormItemName: includesPrefixFormItemName,
			goodFormItemName: includesGoodFormItemName,
			skuFormItemName: includesSkuFormItemName,
			goodFormItemPlaceholder: itemInfoQueryIncludesPlaceholder()[0],
			skuFormItemPlaceholder: itemInfoQueryIncludesPlaceholder()[1],
			skuFormVisible: itemInfoQueryIncludesPlaceholder()[1]
		});
	}, [isShowZeroStockVersion, goodsIncludesType, isSupplier, formData]);




	// 商品不包含 相关
	const itemInfoQueryExcludesList = useMemo(() => {
		let version = userInfo?.version;
		if (isDistributor) {
			version = 2;
		}
		return getGoodsInfoSelectOptions(version, false);
	}, [userInfo?.version, isDistributor]);


	const prefixOptionsExcludesOnChange = v => {
		setGoodsExcludesType(v);
	};

	const itemInfoQueryExcludesPlaceholder = () => {
		const type = form.getFieldValue(excludesPrefixFormItemName);
		return itemInfoQueryExcludesList.filter(i => i.value == type)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名'];
	};

	const getExcludesGoodsSearchComponentProps = useCallback(() => {
		return ({
			prefixOptions: itemInfoQueryExcludesList,
			goodMaxInputNum: 50,
			skuMaxInputNum: 50,
			prefixFormItemName: excludesPrefixFormItemName,
			goodFormItemName: excludesGoodFormItemName,
			skuFormItemName: excludesSkuFormItemName,
			goodFormItemPlaceholder: itemInfoQueryExcludesPlaceholder()[0],
			skuFormItemPlaceholder: itemInfoQueryExcludesPlaceholder()[1],
			skuFormVisible: itemInfoQueryExcludesPlaceholder()[1]
		});
	}, [isShowZeroStockVersion, goodsExcludesType, isSupplier, formData]);

	const itemInfoQueryList = useMemo(() => {
		return getItemInfoQuerySelectOptions(userStore.userInfo?.version);
	}, [userStore.userInfo?.version]);

	const fetchData = async(info) => {
		if (isDistributor) {
			if (!info.supplierUserIdList) {
				info.supplierUserIdList = [supplierList?.[0]?.saleUserId];
				form.setFieldsValue({ supplierUserIdList: supplierList?.[0]?.saleUserId });
			}
			let search = {
				sid: info.sid,
				pageNo: info.pageNo,
				pageSize: info.pageSize,
				timeType: info.timeType || '1',
				status: "ALL_STATUS",
				tid: info.tid,
				scmTid: info.distributorTid,
				viewType: 1,
				supplierUserIdList: info.supplierUserIdList,
				startTime: info.searchTime[0]?.format("YYYY-MM-DD HH:mm:ss"),
				endTime: info.searchTime[1]?.format("YYYY-MM-DD HH:mm:ss"),
				multiShops: await getMultiShops({}),
				sameWaybillNoCombination: getIsSameSidShow(),
			};
			if (!search.supplierUserIdList?.length) {
				message.warn('请选择供应商');
				return;
			}
			handleGoodsInfoSearchParams(info, search);
			setSearchParams(search);
			return queryScmTradeApi(search);
		} else {
			if (!info.distributorUserIds) {
				info.distributorUserIds = [distributorList?.[0]?.saleUserId];
				form.setFieldsValue({ distributorUserIds: distributorList?.[0]?.saleUserId });
			}
			let search:TradeQueryTradeRequest = {
				distributorTid: info.tid,
				sid: info.sid,
				viewType: 0,
				equalFlag: info.goodsIncludeStatus == '1' ? 1 : 0,
				goodsContain: info.goodsIncludeStatus == '2',
				tid: info.distributorTid,
				timeType: info.timeType || '1',
				status: "ALL_STATUS",
				orderSource: 'scmOrder',
				isPreciseByTrade: false,
				isPrecise: false,
				distributorUserIds: info.distributorUserIds,
				startTime: info.searchTime[0]?.format("YYYY-MM-DD HH:mm:ss"),
				endTime: info.searchTime[1]?.format("YYYY-MM-DD HH:mm:ss"),
				pageNo: info.pageNo,
				pageSize: info.pageSize,
				sameWaybillNoCombination: getIsSameSidShow(),
			};
			if (!search.distributorUserIds?.length) {
				message.warn('请选择分销商');
				return;
			}
			handleGoodsInfoSearchParams(info, search);
			setSearchParams(search);
			return TradeQueryBaseTradeApi(search);
		}

	};
	const responseAdapter = (data: TradeQueryTradeResponse["data"]) => {
		let total = 0;
		let list = [];

		if (isDistributor) {
			total = data?.total || 0;
			list = data?.scmTradeVos || [];
			list.forEach((item, index) => {
				item.rowId = `${item.togetherId}_${item?.oid || ''}_${index}`;
			});
		} else {
			total = data.data?.total || 0;
			list = data.data?.list || [];
			list.forEach((item, index) => {
				item.rowId = `${item.togetherId}_${item?.ptTids?.join(',') || ''}_${index}`;
			});
		}

		setDataSource(list || []);
		return {
			total,
			list
		};
	};

	const FormFieldList: FormItemConfig[] = [
		{
			name: isSupplier ? 'distributorUserIds' : 'supplierUserIdList',
			label: '',
			children: <DistributorSelect size="small" isSingle style={ { width: '302px' } } roleType={ isSupplier ? 'supplier' : 'sale' } />
		},
		{
			name: 'timeType',
			label: '',
			className: s.condition1,
			children: <SearchTimeType noPrintTimeType />,
		},
		{
			name: 'searchTime',
			label: '',
			className: s.condition2,
			initialValue: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
			children: <KdzsDateRangePicker1 datePickerKey={ DatePickerKey.distribution_order_detail } cacheQuickChoose useServeTime />,
		},
		{
			name: 'tid',
			label: '',
			children: <InputMulti maxInputNum={ 500 } placeholder="平台订单编号	" style={ { width: '100%}' } } size="small" />,
		},
		{
			name: 'distributorTid',
			label: '',
			children: <InputMulti maxInputNum={ 500 } placeholder="供分销订单编号" style={ { width: '100%}' } } size="small" />,
		},
		{
			name: 'sid',
			label: '',
			children: <InputMulti maxInputNum={ 500 } placeholder="快递单号" style={ { width: '100%}' } } size="small" />,
		},
		{
			name: "goodsIncludes",
			label: "",
			children: <GoodsSearch { ...getIncludesGoodsSearchComponentProps() } prefixOptionsOnChange={ prefixOptionsIncludesOnChange } />
			,
		},
		{
			name: "goodsExcludes",
			label: "",
			children: <GoodsSearch { ...getExcludesGoodsSearchComponentProps() } prefixOptionsOnChange={ prefixOptionsExcludesOnChange } />
		}
	];

	const getProductInfoColumn = () => {
		if (isSupplier) {
			return ({
				title: () => {
					const stopBodyScroll = (isFixed: boolean) => {
						if (isFixed) {
							setScrollTop(window.scrollY);
							document.body.style.top = -window.scrollY + 'px';
							document.body.style.position = 'fixed';
						} else {
							document.body.style.position = '';
							document.body.style.top = '';
							window.scrollTo(0, scrollTop); // 回到原先的top
						}
					};
					return (
						<>
							产品内容
							<span
								className="r-ml-5 r-pointer"
								onClick={ (e) => {
									setIsShowPackageOrderConfig(true);
									setShowProductPagePosition(calcDomPostion(e));
									stopBodyScroll(true);
								} }
							>
								<Icon type="shezhi" style={ { color: '#999' } } pointer />
							</span>
							<ProductSetting
								visible={ isShowPackageOrderConfig }
								showProductPagePosition={ showProductPagePosition }
								hideItem={ { stockWarn: true, hideGoodsSelect: true, hidePartConsignItems: true } }
								onClose={ () => {
									setIsShowPackageOrderConfig(false);
									stopBodyScroll(false);
								} }
							/>
						</>
					);
				},
				width: 180,
				dataIndex: 'content',
				render(value, record) {
					return <ProductOrderFragment productOrders={ record?.trades?.[0].orders } pack={ record } pageFrom="settlement" />;
				},
			});
		}
		const width = 200;
		return ({
			width,
			title: () => {
				return (

					<div className={ `${s['table-header-item']} ${s['productContentCol']}` } style={ { width } }>
						产品内容
						<PackageOrderHeader />
					</div>
				);
			},
			render(_, record) {
				if (isDistributor && record.combinationWaybillNoInfos?.length > 0) {
					const pack = { ...record, scmOrderVos: [] };
					record.combinationWaybillNoInfos.forEach(item => pack.scmOrderVos.push(...item.scmOrderVos));
					const checkKey = `${pack.scmOrderVos[0]?.numIid}_${pack.scmOrderVos[0]?.outerSkuId}_${pack.scmOrderVos[0]?.title}`;
					if (pack.scmOrderVos.every((item, index) => index === 0 || `${item.numIid}_${item.outerSkuId}_${item.title}` === checkKey)) {
						pack.scmOrderVos = pack.scmOrderVos.slice(0, 1);
					}
					return <ProductContentCom pack={ pack } colWidth={ width } />;
				}
				return <ProductContentCom pack={ record } colWidth={ width } />;
			}
		});
	};


	// 表格列定义，与AntdTable使用一致
	const columns: ColumnsType<TradeQueryTradeResponse["data"]["list"][number]> = [
		{
			title: '序号',
			dataIndex: '',
			width: 50,
			render(value, record, index) {
				return index + 1;
			},
		}, 
		{
			title: '平台订单编号',
			dataIndex: 'togetherId',
			width: 180,
			render(_, record, index) {
				let value = isSupplier ? record?.trades?.[0]?.distributorTid : record.tid;
				if (isDistributor && record.combinationWaybillNoInfos?.length > 0) {
					value = record.combinationWaybillNoInfos.map(item => item.tid);
					if ([...new Set(value)].length === 1) {
						value = [...new Set(value)];
					}
				}
				return (
					<Dropdown
						placement="topCenter" 
						overlay={ 
							copyMenu(() => { copyToPaste(Array.isArray(value) ? value.join(',') : value); }) 
						}
					>
						<div>
							{
								Array.isArray(value) ? value.map(item => <div key={ item }>{item}</div>) : value
							}
						</div>
					</Dropdown>
				);
			},
		}, 
		{
			title: '供分销订单编号',
			dataIndex: 'distributorTid',
			width: 210,
			render(_, record, index) {
				let value = isSupplier ? record.togetherId : record.scmTid;
				if (isDistributor && record.combinationWaybillNoInfos?.length > 0) {
					value = record.combinationWaybillNoInfos.map(item => item.scmTid);
					if ([...new Set(value)].length === 1) {
						value = [...new Set(value)];
					}
				}
				return (
					<Dropdown
						placement="topCenter" 
						overlay={ 
							copyMenu(() => { copyToPaste(Array.isArray(value) ? value.join(',') : value); }) 
						}
					>
						<div>
							{
								Array.isArray(value) ? value.map(item => <div key={ item }>{item}</div>) : value
							}
						</div>
					</Dropdown>
				);
			},
		}, 
		{
			title: '省市区',
			dataIndex: 'receiverState',
			width: 70,
			render(_, record, index) {
				let value = `${record?.receiverState || record?.receiverProvince},${record?.receiverCity},${record?.receiverDistrict || record?.receiverCounty}`;
				if (isDistributor && record.combinationWaybillNoInfos?.length > 0) {
					value = record.combinationWaybillNoInfos.map(item => `${item?.receiverState || item?.receiverProvince},${item?.receiverCity},${item?.receiverDistrict || item?.receiverCounty}`);
					if ([...new Set(value)].length === 1) {
						value = [...new Set(value)];
					}
				}
				return (
					<div>
						{
							Array.isArray(value) ? value.map(item => <div key={ item }>{item}</div>) : value
						}
					</div>
				);
			},
		}, 
		{
			title: '快递公司',
			dataIndex: 'logisticsCompanySet',
			width: 70,
			render(_, record, index) {
				return record.logisticsCompanySet?.map(item => (
					<div key={ item } className="r-c-error">{item?.split(',')[0]}</div>
				));
			},
		}, 
		{
			title: '快递单号',
			dataIndex: 'waybillNoSet',
			width: 100,
			render(value, record) {
				return record.waybillNoSet?.filter(item => !!item)?.map(item => (
					<div key={ item } className="r-c-error">{item}</div>
				));
			}
		},
		getProductInfoColumn(), 
		{
			title: '数量',
			dataIndex: 'nums',
			width: 60,
			render(_, record, index) {
				let value = record.trades?.reduce((prev, cur) => prev + cur.totalNums, 0);
				if (isDistributor) {
					value = record.goodsNum;
				}
				return value;
			},
		}, 
		{
			title: (
				<div className="r-flex r-ai-c">
					商品结算金额
					<Tooltip title={ isSupplier ? "每一笔分销订单对应的商品的分销结算金额（结算价按供应商设置的计算）" : "每一笔分销订单对应的商品的分销结算金额（结算价按分销商设置的计算）" }>
						<QuestionCircleOutlined className="r-ml-2" />
					</Tooltip>
				</div>
			),
			dataIndex: 'itemSettleAmount',
			width: 120,
			render(value) {
				return value;
			}
		}, 
		{
			title: (
				<div className="r-flex r-ai-c">
					运费
					<Tooltip title="根据供应商的运费模板设置计算的理论运费，仅计算通过助手ERP发货订单。">
						<QuestionCircleOutlined className="r-ml-2" />
					</Tooltip>
				</div>
			),
			dataIndex: 'freightAmount',
			width: 80,
			render(value) {
				return value;
			}
		}, 
		{
			title: (
				<div className="r-flex r-ai-c">
					总金额
					<Tooltip title="总金额=商品结算金额+运费">
						<QuestionCircleOutlined className="r-ml-2" />
					</Tooltip>
				</div>
			),
			dataIndex: 'orderSettleAmountSum',
			width: 80,
			render(value) {
				return value;
			}
		}, 
		{
			title: '店铺名称',
			dataIndex: 'status',
			width: 140,
			render(value, record, index) {
				const getShopNameText = () => {
					let _sellerNick = getShopName({ plat: record.platform, sellerNick: record.sellerNick });
					return _sellerNick;
				};
				if (isDistributor) {
					if (record.combinationWaybillNoInfos?.length > 0) {
						const sellerNick = record.combinationWaybillNoInfos.map(item => {
							return { platform: item.platform, sellerNick: item.sellerNick };
						});
						const checkKey = `${sellerNick[0].platform}_${sellerNick[0].sellerNick}`;
						if (sellerNick.every((item, index) => index === 0 || `${item.platform}_${item.sellerNick}` === checkKey)) {
							return (
								<div
									className="r-flex r-ai-c"
								>
									{getTradePlatformLabel(sellerNick[0].platform)}
									<div>{getShopName({ plat: sellerNick[0].platform, sellerNick: sellerNick[0].sellerNick })}</div>
								</div>
							);
						}
						return sellerNick?.map(item => (
							<div
								className="r-flex r-ai-c"
							>
								{getTradePlatformLabel(item.platform)}
								<div>{getShopName({ plat: item.platform, sellerNick: item.sellerNick })}</div>
							</div>
						));
					}
				}
				return (
					<div
						className="r-flex r-ai-c"
					>
						{getTradePlatformLabel(record.platform)}
						<div>
							{isSupplier
								? !(userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(record))
									? '****'
									: getShopNameText()
								: getShopNameText()}
						</div>
					</div>
				);
			},
		}, 
		{
			title: isSupplier ? '分销商' : '供应商',
			dataIndex: 'distributorAccount',
			width: 140,
			render(_, record, index) {
				let value = <div>{record.distributorName} {record.distributorAccount ? `(${record.distributorAccount})` : ''}</div>;
				if (isDistributor) {
					if (record.combinationWaybillNoInfos?.length > 0) {
						const supplierMobile = record.combinationWaybillNoInfos.map(item => `${item.supplierName ?? ""} ${item.supplierMobile ? `(${item.supplierMobile})` : ''}`);
						if ([...new Set(supplierMobile)].length === 1) {
							return <div>{supplierMobile[0]}</div>;
						}
						return supplierMobile?.map(item => <div>{item}</div>);
					}
					return <div>{record.supplierName ?? ""} {record.supplierMobile ? `(${record.supplierMobile})` : ''}</div>;
				}
				return value;
			},
		}, 
		{
			title: '订单状态',
			dataIndex: 'status',
			width: 140,
			render(value, record) {
				if (isDistributor) {
					if (record.combinationWaybillNoInfos?.length > 0) {
						const status = record.combinationWaybillNoInfos.map(item => item.status);
						if ([...new Set(status)].length === 1) {
							return TradeStatusLabel[status[0]];
						}
						return status?.map(item => <div>{TradeStatusLabel[item]}</div>);
					}
				}
				return TradeStatusLabel[value];
			}
		}, 
		{
			title: '退款状态',
			dataIndex: 'refundStatus',
			width: 140,
			render(value, record) {
				if (isDistributor) {
					if (record.combinationWaybillNoInfos?.length > 0) {
						const refundStatus = record.combinationWaybillNoInfos.map(item => item.refundStatus);
						if ([...new Set(refundStatus)].length === 1) {
							return RefundStatusText[refundStatus[0]];
						}
						return refundStatus?.map(item => <div>{RefundStatusText[item]}</div>);
					}
				}
				return RefundStatusText[value];
			}
		}, 
		{
			title: '下单时间',
			dataIndex: 'createTime',
			width: 140,
			render(value, record) {
				if (isDistributor) {
					if (record.combinationWaybillNoInfos?.length > 0) {
						const createTime = record.combinationWaybillNoInfos.map(item => item.createTime);
						if ([...new Set(createTime)].length === 1) {
							return createTime[0];
						}
						return createTime?.map(item => <div>{dayjs(item).format('YYYY-MM-DD HH:mm:ss')}</div>);
					}
				}
				return value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '--';
			}
		}, 
		{
			title: '付款时间',
			dataIndex: 'payTime',
			width: 140,
			render(value, record) {
				if (isDistributor) {
					if (record.combinationWaybillNoInfos?.length > 0) {
						const payTime = record.combinationWaybillNoInfos.map(item => item.payTime);
						if ([...new Set(payTime)].length === 1) {
							return payTime[0];
						}
						return payTime?.map(item => <div>{dayjs(item).format('YYYY-MM-DD HH:mm:ss')}</div>);
					}
				}
				return value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '--';
			}
		}, 
		{
			title: '发货时间',
			dataIndex: 'shipTime',
			width: 140,
			render(value, record) {
				if (isDistributor) {
					if (record.combinationWaybillNoInfos?.length > 0) {
						const shipTime = record.combinationWaybillNoInfos.map(item => item.shipTime);
						if ([...new Set(shipTime)].length === 1) {
							return shipTime[0];
						}
						return shipTime?.map(item => <div>{dayjs(item).format('YYYY-MM-DD HH:mm:ss')}</div>);
					}
					return record.shipTime;
				}
				let val = record.trades?.[0]?.orders?.[0]?.sysShipTime;
				return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '--';
			}
		}
	];

	const exportResult = async(tid: string = '') => {
		const toggleLoading = (toggle: boolean) => {
			if (tid) {
				setPageExportLoading(toggle);
			} else {
				setAllExportLoading(toggle);
			}
		};
		try {
			if (isDistributor) {
				tid ? sendPoint(Pointer.分销商角色_分销结算_代发订单明细_导出当前页_点击次数) : sendPoint(Pointer.分销商角色_分销结算_代发订单明细_导出全部_点击次数);
			} else {
				tid ? sendPoint(Pointer["供应商_分销结算_分销订单明细>>导出当前页_点击"]) : sendPoint(Pointer["供应商_分销结算_分销订单明细>>导出全部_点击"]);
			}
			toggleLoading(true);
			const { exportFields, selectableExportFields } = await TradeExportGetConfigApi({
				latitude: DISTRIBUTION_LATITUDE
			});
			const params = {
				fields: selectableExportFields,
				latitude: DISTRIBUTION_LATITUDE,
			};
			await TradeExportSaveConfigApi(params);
			const requestParams = {
				...searchParams,
				// tid, // 原来的导出可以指定当前页tid,现在不需要
				latitude: DISTRIBUTION_LATITUDE,
			};
			console.log(449, requestParams);
			await downloadCenter({
				requestParams,
				fileName: isDistributor ? `代发订单明细` : `分销订单明细`,
				module: ModulesFunctionEnum.订单打印
			});

			toggleLoading(false);
		} catch (error) {
			toggleLoading(false);
		}
	};

	const onCheckSameSidShow = (e) => {
		const { checked } = e.target;
		setIsSameSidShow((prev) => checked);
		ref.current.submit();
	};

	const getExpandContext = () => {
		return (
			<>
				{/* <Button loading={ pageExportLoading } size="middle" disabled={ !dataSource.length } onClick={ () => exportResult(dataSource?.map(i => i.togetherId).join(',')) } className="r-mr-10">导出当前页</Button> */}
				<Button className="r-ml-10" loading={ allExportLoading } size="small" disabled={ !dataSource.length } onClick={ () => exportResult() }>导出Excel</Button>
				{
					isDistributor && (
						<Space>
							<Checkbox className="r-ml-14" checked={ getIsSameSidShow() } onChange={ (e) => onCheckSameSidShow(e) }>
								<span style={ { whiteSpace: 'nowrap' } }>相同快递单号合并显示</span>
							</Checkbox>
						</Space>
					)
				}
			</>
		);
	};

	const onFieldsChange = (changedValues, allValues) => {
		setFormData(allValues);
		console.log(changedValues, 'changedValues');

		if ('status' in changedValues) {
			ref?.current?.submit();
		}

		if ('goodsIncludeStatus' in changedValues) {
			setItemInfoQueryPlaceholder(itemInfoQueryList.filter(i => i.value == changedValues.goodsIncludeStatus)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名']);
		}
	};

	const handleInitialValues = () => {
		const initialValues:any = {
			[includesPrefixFormItemName]: "1",
			[excludesPrefixFormItemName]: "0",
		};
		if (isDistributor && supplierList?.length) {
			let val = supplierList?.[0]?.supplierUserId;
			initialValues.supplierUserIdList = [val];
		}
		if (isSupplier && distributorList?.length) {
			let val = distributorList?.[0]?.saleUserId;
			initialValues.distributorUserIds = [val];
		}
		setTimeout(() => {
			form.setFieldsValue(initialValues);
			setFormData((pre) => ({
				...pre,
				...initialValues
			}));
		});

	};

	useEffect(() => {
		initProductSet();
		handleInitialValues();
	}, []);

	const getEnhanceColumns = () => {
		if (!isSupplier) {
			const col = {
				title: (
					<div className="r-flex r-ai-c">
						实付金额
					</div>
				),
				dataIndex: 'orderActualPayment',
				width: 80,
				render(value) {
					return value;
				}
			};
			if (!columns.find(i => i.dataIndex === 'orderActualPayment')) {
				const index = columns.findIndex(i => i?.dataIndex === 'orderSettleAmountSum');
				columns.splice(index + 1, 0, col);
			}
		}
		return columns;
	};

	return (
		<NormalLayout className={ cs(['r-w-full', s['distribution-order-detail']]) } >
			<SearchTable
				ref={ ref }
				pageSizeId="distributionOrderDetailTable"
				form={ form }
				fetchData={ fetchData }
				responseAdapter={ responseAdapter }
				additionalFormNode={ getExpandContext() }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				rowFormConfig={ {
					formList: isSupplier ? FormFieldList : FormFieldList.filter(i => !["goodsIncludes", "goodsExcludes"].includes(i.name)),
					size: 'small',
					defaultParams: {
						goodsIncludeStatus: '1',
						timeType: '1',
					},
					colProps: {
					},
					style: {
						padding: '0 16px 8px 16px'
					}
				} }
				onReset={ handleInitialValues }
				baseTableConfig={ {
					onFieldsChange,
					dataSource,
					groupId: 'groupId_distributionOrderDetail',
					id: "distributionOrderDetail",
					rowKey: 'rowId',
					// rowSelection: {
					// 	type: 'checkbox',
					// 	...rowSelection,
					// },
					columns: getEnhanceColumns(),
					pagination: false,
					cachePgination: true,
					// expandContext: getExpandContext(),
				} }
			/>
		</NormalLayout>
	);
};

export default observer(DistributionOrderDetail);
