import React, { useEffect, useMemo, useState, useRef } from "react";
import { observer } from "mobx-react";
import {
	Empty,
	Layout,
	Pagination,
	Radio,
	Spin,
	Card,
	Button,
	Checkbox,
	Select,
	Form,
	Modal,
	Popconfirm,
	InputNumber,
	Popover,
	Tabs as AntTabs
} from "antd";
import { cloneDeep } from "lodash";
import { useLocation, useHistory } from "react-router-dom";
import { ExclamationCircleFilled, ExclamationCircleOutlined, QuestionCircleFilled, SettingOutlined } from '@ant-design/icons';
import Tabs from "@/components/Tabs";
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import SearchContainer from "./components/SearchContainer";
import List from "./components/List";
import BottomCom from "./components/BottomCom";
import ColumnSettingDrawer from '@/pages/Warehouse/System/PlatformGoods/components/ColumnSettingDrawer';
import { TradeDictQueryDictApi, TradeDictInsertDictApi } from '@/apis/trade/search';
import message from "@/components/message";
import { local } from "@/libs/db";
import userStore from "@/stores/user";
import { tradeStore } from "@/stores";
import { filterPrintContent } from "@/utils/trade/printContent";
import { ORDER_FILTER_OPTIONS, RECORD_COUNT_ENUM, EnumLabelTabs, EnumLabelTypeTabs } from "./constants";
import { ItemTakeGoodsLabelSelectWithPageRequest } from "@/types/trade/takeGoodsLabel";
import { ListHeaderText } from "./components/List/constants";
import {
	TradeLiveTagCreateTaskApi,
	TradeLiveTagCloseTaskApi,
	TradeLiveTagTradeRecordApi,
	TradeLiveTagLastTaskApi,
	TradeLiveTagTradeRecordCountApi,
	TradeLiveTagQueryTaskListApi,
	TradeLiveTagTradeStatusCountApi
} from "@/apis/trade";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import s from './index.module.scss';

interface IPageInfo {
    total: number;
    pageSize: number;
    pageNo: number;
}

const LiveLog = () => {
	const searchRef = useRef(null);
	const location = useLocation();
	const history = useHistory();
    
	const {
		// setSetting,
		tradeAdvanceSetObj,
		setTradeAdvanceSetObj,
		zbdXbqTempList,
		setZbdXbqTempList,
		selectedZbdXbqTemp,
		setSelectedZbdXbqTemp,
		liveTagListStore: { list, setList, isPrinting, setIsPrinting },
		scanPrintStore: {
			getLabelPushInfoList,
			getScanPrintSetting,
			scanPrintSetting,
			printersList,
			defaultPrinter,
			setDefaultPrinter,
		},
	} = tradeStore;

	const [isSearching, setIsSearching] = useState(false);
	const [searchParams, setSearchParams] = useState<ItemTakeGoodsLabelSelectWithPageRequest>({});
	const [liveRecord, setLiveRecord] = useState([]);
	const [emptyText, setEmptyText] = useState("暂无数据");
	const [columnSettingVisible, setColumnSettingVisible] = useState(false); // 是否显示列配置弹框
	const [pageColumnConfig, setPageColumnConfig] = useState({ userConfig: [], defaultConfig: [] }); // 列配置
	const [pageInfo, setPageInfo] = useState<IPageInfo>({
		total: 0,
		pageNo: 1,
		pageSize: 30,
	});
	const [labelTypeTabKey, setLabelTypeTabKey] = useState(
		EnumLabelTypeTabs.全部
	);
	const [initParams, setInitParams] = useState({});
	const [liveRecordCount, setLiveRecordCount] = useState([]);

	const renderNoList = () => {
		if (isSearching) {
			return (
				<Spin
					style={ {
						width: "100%",
						minHeight: "200px",
						padding: "100px",
					} }
					tip="查询中"
				/>
			);
		} else {
			return (
				<Empty
					style={ { minHeight: "200px", padding: "100px" } }
					description={ emptyText }
				/>
			);
		}
	};

	// 保存列配置
	const handleColumnSettingSave = async(type: string, config: any[]) => {
		console.log(type, config);

		try {
			if (type === 'reset') {
				await TradeDictInsertDictApi({ userDictEnum: 'LIVE_TRADE_RECORD_COLUMN_CONFIG', value: JSON.stringify(pageColumnConfig.defaultConfig) });
				setPageColumnConfig(prev => ({ ...prev, userConfig: pageColumnConfig.defaultConfig }));
			} else {
				await TradeDictInsertDictApi({ userDictEnum: 'LIVE_TRADE_RECORD_COLUMN_CONFIG', value: JSON.stringify(config) });
				setPageColumnConfig(prev => ({ ...prev, userConfig: config }));
			}
			message.success('列配置已保存');
		} catch (error) {
			console.log('保存列配置失败:', error);
		}
	};

	// 获取默认列配置
	const getDefaultConfig = () => {
		return cloneDeep(ListHeaderText)?.map((item, index) => ({
			...item,
			index,
			name: item.text,
		}));
	};

	// 添加配置合并函数
	const mergeColumnConfig = (userConfig: any[], defaultConfig: any[]) => {
		if (!userConfig || userConfig.length === 0) {
			return defaultConfig;
		}

		// 创建用户配置的key映射
		const userConfigMap = new Map();
		userConfig.forEach(item => {
			userConfigMap.set(item.key, item);
		});

		// 创建默认配置的key映射
		const defaultConfigMap = new Map();
		defaultConfig.forEach(item => {
			defaultConfigMap.set(item.key, item);
		});

		const mergedConfig = [];

		// 1. 处理默认配置中的字段
		defaultConfig.forEach(defaultItem => {
			const userItem = userConfigMap.get(defaultItem.key);
			if (userItem) {
				// 如果用户配置中存在该字段，使用用户配置但保持默认配置的基本信息
				mergedConfig.push({
					...defaultItem, // 保持默认配置的text、width等基本信息
					...userItem, // 应用用户的配置（如ischecked、index等）
					text: defaultItem.text, // 确保使用最新的显示文本
					name: defaultItem.text, // 确保name和text一致
				});
			} else {
				// 如果用户配置中不存在该字段（新增字段），使用默认配置
				mergedConfig.push({
					...defaultItem,
					name: defaultItem.text,
					ischecked: true, // 新增字段默认显示
				});
			}
		});

		// 2. 处理用户配置中存在但默认配置中不存在的字段（已删除的字段）
		// 这些字段不添加到合并结果中，实现删除同步

		// 3. 重新排序，保持用户配置的顺序
		mergedConfig.sort((a, b) => {
			const aIndex = userConfigMap.get(a.key)?.index ?? 999;
			const bIndex = userConfigMap.get(b.key)?.index ?? 999;
			return aIndex - bIndex;
		});

		mergedConfig.forEach((item, index) => {
			item.index = index;
		});

		console.log('%c [ 直播场次列配置项 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', mergedConfig);
		return mergedConfig;
	};

	// 获取列配置
	const getColumnSetting = async() => {
		try {
			const res = await TradeDictQueryDictApi({ userDictEnum: 'LIVE_TRADE_RECORD_COLUMN_CONFIG' });

			let userConfig = [];
			const defaultConfig = getDefaultConfig();
			try {
				userConfig = JSON.parse(res?.value || '[]');
			} catch (e) {
				console.log('解析用户配置失败:', e);
			}

			const mergedUserConfig = mergeColumnConfig(userConfig, defaultConfig);

			setPageColumnConfig({
				userConfig: mergedUserConfig,
				defaultConfig
			});
		} catch (error) {
			console.log('获取列配置失败:', error);
			const defaultConfig = getDefaultConfig();
			setPageColumnConfig({
				userConfig: defaultConfig,
				defaultConfig
			});
		}
	};

	const handleSearch = (value?: any) => {
		setSearchParams((prev) => ({
			pageSize: pageInfo.pageSize,
			...value,
			pageNo: 1,
		}));
	};

	const onPageChange = (pageNo: number, pageSize: number) => {
		setPageInfo((prev) => ({
			...prev,
			pageNo,
			pageSize,
		}));
		
		setSearchParams((prev) => ({
			...prev,
			pageNo: pageSize === prev.pageSize ? pageNo : 1,
			pageSize,
		}));
	};

	const resList = useMemo(() => {
		list.forEach((item) => {
			item._skuName = filterPrintContent(item.skuName);
		});
		return list;
	}, [list]);

	const listItem = useMemo(() => {
		return pageColumnConfig?.userConfig?.filter(item => item.ischecked) || [];
	}, [labelTypeTabKey, pageColumnConfig.userConfig]);

	const onLabelTabChange = async(e) => {
		setList([]);
		setLabelTypeTabKey(e);
		setSearchParams((prev) => ({
			pageSize: pageInfo.pageSize,
			...prev,
			pageNo: 1,
			queryGroupBy: e ? Number(e) : e,
		}));
	};

	useEffect(() => {
		if (searchParams && searchParams.pageNo) {
			setIsSearching(true);
			setList([]);
			TradeLiveTagTradeRecordCountApi(searchParams).then((res) => {
				setLiveRecordCount(res);
			});
			TradeLiveTagTradeRecordApi(searchParams)
				.then((res) => {
					setIsSearching(false);
					if (res && res.list) {
						setPageInfo((prev) => ({
							...prev,
							total: res.total !== -1 ? res.total : prev.total,
						}));
						setList(res.list);
					} else {
						setEmptyText("暂无数据");
					}
				})
				.catch(() => {
					setIsSearching(false);
				});
		}
	}, [searchParams]);

	const init = async() => {
		sendPoint(Pointer["直播订单明细-页面展现"]);
		const liveRecord = await TradeLiveTagQueryTaskListApi({});
		setLiveRecord(liveRecord || []);
	};

	useEffect(() => {
		getColumnSetting();

		init();

		return () => {
			setLiveRecord([]);
			setList([]);
		};
	}, []);

	useEffect(() => {
		const stateParams = location.state as {
            liveTradePrintTaskIdList?: string;
            labelType?: string;
        };

		console.log('%c [ state ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', stateParams);
        
		const liveTradePrintTaskIdList: string | null = stateParams?.liveTradePrintTaskIdList;
		const labelType: string | null = stateParams?.labelType;
        
		if (liveTradePrintTaskIdList) {
			setInitParams({
				liveTradePrintTaskIdList
			});
		}
        
		// 设置tab
		if (labelType) {
			setLabelTypeTabKey(labelType);
		}
	}, [location.state]);

	return (
		<NormalLayout>
			<Layout className="kdzs-section">
				<SearchContainer
					ref={ searchRef }
					liveRecord={ liveRecord }
					queryGroupBy={ labelTypeTabKey }
					list={ resList }
					handleSearch={ handleSearch }
					isSearching={ isSearching }
					initParams={ initParams }
					setInitParams={ setInitParams }
				/>
			</Layout>
			<Layout
				className="kdzs-section"
				style={ { marginBottom: "80px", marginTop: "0px", paddingTop: "0px" } }
			>
				<div
					className="r-flex r-jc-sb r-ai-fe"
					style={ {
						padding: "8px 0 12px",
						background: "#fff",
						marginTop: 0,
						position: "relative",
					} }
				>
					<Tabs
						style={ { width: "100%" } }
						defaultActiveKey={ EnumLabelTypeTabs.全部 }
						activeKey={ labelTypeTabKey }
						onChange={ onLabelTabChange }
					>
						{Object.keys(EnumLabelTypeTabs).map(
							(key) => (
								<Tabs.TabPane
									tab={ (
										<span>
											{key}
											<span className="r-c-error r-ml-8">
												{
													liveRecordCount[
														RECORD_COUNT_ENUM[
															key
														]
													]
												}
											</span>
										</span>
									) }
									key={ EnumLabelTypeTabs[key] }
								/>
							)
						)}
					</Tabs>

					<div style={ { paddingBottom: "8px", borderBottom: "1px solid #eee" } }>
						<Button
							size="middle"
							icon={ <SettingOutlined /> }
							onClick={ () => setColumnSettingVisible(true) }
						>
							列配置
						</Button>
					</div>
				</div>
				{resList.length > 0 ? (
					<>
						<List
							list={ resList }
							searchParams={ searchParams }
							listItem={ listItem }
							zbdTempList={ zbdXbqTempList }
						/>
						{searchParams?.pageSize && searchParams?.pageNo && (
							<Pagination
								size="small"
								style={ {
									marginTop: 16,
									marginBottom: 16,
									textAlign: "right",
								} }
								showQuickJumper
								showSizeChanger
								pageSize={
									searchParams?.pageSize
								}
								current={ searchParams?.pageNo }
								total={ pageInfo.total }
								showTotal={ (total) => `共${total}条` }
								onChange={ onPageChange }
								pageSizeOptions={ [
									"10",
									"30",
									"50",
									"100",
									"200"
								] }
							/>
						)}
					</>
				) : (
					renderNoList()
				)}
			</Layout>

			<BottomCom
				handleSearch={ handleSearch }
				searchParams={ searchParams }
				zbdTempList={ zbdXbqTempList }
			/>

			{/* 列配置 */}
			<ColumnSettingDrawer
				visible={ columnSettingVisible }
				onClose={ () => setColumnSettingVisible(false) }
				hideSkuSwitch
				colSortList={ pageColumnConfig.userConfig }
				onSaveSort={ handleColumnSettingSave }
			/>
		</NormalLayout>
	);
};

export default observer(LiveLog);