export enum EnumLabelTabs {
	直播打印 = "livePrint",
	直播订单明细 = "liveOrders",
	直播标签扫码打印 = "liveTagScanPrint",
}

export enum EnumLabelTypeTabs {
	"全部" = "",
	"已打印" = "1",
	"未打印-不符合" = "2",
	"未打印-异常" = "3",
}

export enum ORDER_FILTER_ENUM {
    不过滤退款订单 = 0,
    过滤退款完成订单 = 1,
    过滤退款中或退款完成订单 = 2,
}

export enum RECORD_COUNT_ENUM {
    "全部" = 'allCount',
    "已打印" = 'alreadyPrintCount',
    "未打印-不符合" = 'noPrintNoFitCount',
    "未打印-异常" = 'noPrintExceptionCount',
}

export const ORDER_FILTER_OPTIONS = [
	{
		label: '不过滤退款订单',
		value: 0
	},
	{
		label: '过滤退款完成订单',
		value: 1
	},
	{
		label: '过滤退款中或退款完成订单',
		value: 2
	}
];
