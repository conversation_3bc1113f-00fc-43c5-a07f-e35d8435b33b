import React, { Fragment } from 'react';
import { observer } from 'mobx-react';
import style from '../index.module.scss';
import { tradeStore } from '@/stores';
import scanPrintStore from '@/stores/trade/scanPrint';

const renderItem = (item:{text:string, key: string, width: number }) => {
	return <div className={ `${style['table-header-item']} ${item.key === "index" ? "batch_tbtlt_index" : ""}` } style={ { minWidth: `${item.width || 80}px` } }>{item.text}</div>;
};

const ListHeader = (props:any) => {
	const { listItem } = props;
	const {
		liveTagListStore: {
			listCheckStatus,
			setListCheckStatus,
			handleChoiceChange
		}
	} = tradeStore;
	const {
		hasPlatformAuthInfo
	} = scanPrintStore;

	const selectAllPacks = () => {
		if (listCheckStatus === 0) {
			handleChoiceChange({
				type: 0.5,
			});
		} else if (listCheckStatus === 1) {
			handleChoiceChange({
				type: 4,
			});
		}
	};

	const handleHalfClick = (e: any) => {
		e.preventDefault();
		handleChoiceChange({
			type: 2,
		});
	};

	const renderInput = () => (
		<label className={ `${style['table-header-item']} batch_tbtlt_check` }>
			{listCheckStatus === 0.5 ? <span className="half-checked" onClick={ handleHalfClick } /> : (
				<input
					type="checkbox"
					className="input_check packageCheckAll"
					checked={ listCheckStatus === 1 }
					onChange={ selectAllPacks }
				/>
			)}
		</label>
	);
	return (
		<div className={ style['table-title-header-con'] }>
			{renderInput()}
			{
				(listItem.filter(i => (hasPlatformAuthInfo() ? true : !["nhbNick"].includes(i.key)))).map((item) => (
					<Fragment key={ `${item.key}` }>
						{renderItem(item)}
					</Fragment>
				))
			}
		</div>
	);
};

export default observer(ListHeader);
