import React, { useEffect, useState, useRef } from 'react';
import { Form, Select, Modal } from "antd";
import _ from "lodash";
import { observer } from 'mobx-react';
import { tradeStore } from '@/stores';
import PrintCenterAPI from "@/print/index";
import s from './index.module.scss';
import ListHeader from './ListHeader';
import ListItem from './ListItem';
import { getFxgBuyerNick } from '@/components-biz/BuyerNickComp/fxg';
import { PLAT_FXG } from '@/constants';
import userStore from '@/stores/user';
import { scrollXLine } from '@/pages/Trade/components/TradeList/utils';

const TradeList = (props:any) => {
	const { list: recordList, listItem } = props;
	const {
		tradeAdvanceSetObj,
		liveTagListStore: {
			singleItem,
			setSingleItem,
		},
		scanPrintStore: { printersList, defaultPrinter },
		selectedZbdXbqTemp,
		zbdXbqTempList
	} = tradeStore;
	const [form] = Form.useForm();
	const [showPrintOptionModal, setShowPrintOptionModal] = useState(false);
	const { shopTokenCache } = userStore;

	const cleanupRef = useRef(null);

	useEffect(() => {
		form.setFieldsValue({
			tempId: selectedZbdXbqTemp.Mode_ListShowId || zbdXbqTempList[0]?.Mode_ListShowId,
			selectedPrinter: defaultPrinter || printersList[0],
		});
	}, [zbdXbqTempList, selectedZbdXbqTemp, printersList, defaultPrinter]);

	useEffect(() => {
		setSingleItem([]);
	}, []);

	const handleOk = _.debounce(() => handlePrintAct(), 500, {
		leading: true,
		trailing: false,
	}); 

	// 打印小标签
	const handlePrintAct = async() => {
		await form.validateFields();
		const crt = singleItem[0];
		if (crt?.platform?.toLocaleLowerCase() === PLAT_FXG && tradeAdvanceSetObj?.printSetExpandDTO?.showDyNickName) {
			try {
				const { ptTid, sellerId, sellerNick } = crt as any;
				const token = shopTokenCache[sellerId];
				const orderId = ptTid?.replace(/A/g, '');
				const nickData = await getFxgBuyerNick({
					sellerId,
					token,
					sellerName: sellerNick,
					orderIds: [orderId],
				});
				crt.base64Url = nickData?.[orderId] || '';
			} catch (error) {
				crt.base64Url = '';
			}
		}
		console.log("打印标签的顺序:", [crt]);
		PrintCenterAPI.batchPrintZbd({
			tempId: form.getFieldValue('tempId'),
			orderList: [crt],
			printType: "zbd",
			printer: form.getFieldValue('selectedPrinter'),
		});
		setShowPrintOptionModal(false);
		setSingleItem([]);
	};
		
	const handleFinish = (v) => {
		console.log(v);
	};

	useEffect(() => {
		if (singleItem.length > 0) {
			setShowPrintOptionModal(true);
		}
	}, [singleItem]);

	useEffect(() => {
		cleanupRef.current?.();
		cleanupRef.current = null;

		if (listItem?.length) {
			const dispose = scrollXLine();
			cleanupRef.current = dispose;
		}

		return () => {
			cleanupRef.current?.();
			cleanupRef.current = null;
		};
	}, [listItem]);

	return (
		<div>
			<div className={ `${s.tableListCon} tableListCon` }> 
				<div className="r-relative r-flex">
					<div style={ { overflowX: 'auto', width: "100%", justifyContent: "space-between", minHeight: "400px" } } id="scrollWrap">
						<div className={ s.scrollLine } id="scrollLine" style={ { width: '100%' } }>
							<div style={ { width: 0, height: 1 } } />
						</div>
						<ListHeader listItem={ listItem } />
						{recordList.map((item:any, index: number) => (
							<ListItem
								key={ `${item.platform}-${item.togetherId}-${index}` }
								pack={ item }
								index={ index }
								listItem={ listItem }
							/>
						))}
					</div>
				</div>
			</div>
			<Modal
				centered
				title="选择打印机和标签模板"
				visible={ showPrintOptionModal }
				width="480px"
				closable={ false }
				destroyOnClose
				maskClosable={ false }
				onOk={ handleOk }
				onCancel={ () => {
					setShowPrintOptionModal(false);
					setSingleItem([]);
				} }
			>
				<Form name="form" layout="inline" form={ form } onFinish={ handleFinish }>
					<Form.Item
						required
						name="selectedPrinter"
						rules={ [{ required: true, message: '请选择打印机' }] }
					>
						<Select
							size="small"
							placeholder="请选择打印机"
							style={ { width: "200px" } }
							allowClear
						>
							{printersList.map((printer) => (
								<Select.Option
									key={ printer }
									value={ printer }
								>
									{printer}
								</Select.Option>
							))}
						</Select>
					</Form.Item>
					<Form.Item
						required
						name="tempId"
						rules={ [{ required: true, message: '请选择标签模板' }] }
					>
						<Select
							size="small"
							placeholder="请选择标签模板"
							style={ { width: "200px" } }
							allowClear
						>
							{zbdXbqTempList.map((item) => (
								<Select.Option
									key={ item.Mode_ListShowId }
									value={ item.Mode_ListShowId }
								>
									{item.ExcodeName}
								</Select.Option>
							))}
						</Select>
					</Form.Item>
				</Form>
			</Modal>
		</div>
		
	);
};

export default observer(TradeList);
