import React, { Fragment } from 'react';
import { observer } from 'mobx-react';
import { TakeGoodsLabelStatusEnum } from "@/pages/Trade/constants";
import style from '../index.module.scss';
import { tradeStore } from '@/stores';
import { IPackage } from '@/pages/Trade/interface';
import ProductContent from './ProductContent';
import scanPrintStore from '@/stores/trade/scanPrint';
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import FxgLiveTag from '@/components-biz/BuyerNickComp/fxgLiveTag';
import { splitFxgTid } from "@/utils";
import { livePrintStatusFormat } from '../constants';

const CheckboxCom = observer((props: {
	pack: any,
	index: number
}) => {
	const {
		liveTagListStore: {
			dealShift,
		}
	} = tradeStore;
	const { pack, pack: { isChecked, noGoodsLink }, index } = props;
	return (
		<div
			className="batch_tbtlt_check"
			onClick={ () => {
				dealShift({ pack, index });
			} }
		>
			<input type="checkbox" disabled={ noGoodsLink } style={ { pointerEvents: noGoodsLink ? 'none' : 'auto' } } value={ isChecked || '' } checked={ isChecked } />
		</div>

	);
});

const ListItem = (props:any) => {
	const { pack, index, listItem } = props;
	const { hasPlatformAuthInfo } = scanPrintStore;
	const {
		liveTagListStore: {
			setSingleItem,
		}
	} = tradeStore;
	const renderItem = (item:any, pack:IPackage, index:number) => {
		if (item.key === "index") {
			return <div className="batch_tbtlt_index" style={ { width: `${item.width || 80}px` } }>{ index + 1}</div>;
		}
		// 标签状态
		if (item.key === "labelStatus") {
			return <div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>{TakeGoodsLabelStatusEnum[pack[item.key]]}</div>;
		}
		// 产品内容
		if (item.key === "info") {
			return (
				<div className={ ` ${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>
					{
						Array.isArray(pack.tradeItemInfoList) && pack.tradeItemInfoList.map(item1 => {
							return <ProductContent pack={ item1 } width={ item.width } />;
						})
					}
				</div>
			);
		}
		// 生成时间/打印时间
		if (item.key === 'gmtCreated') {
			return (
				<div className={ ` ${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>
					<div>{pack[item.key]}</div>
					<div>{pack.printTime}</div>
				</div>
			);
		}
		// 收件人信息
		if (item.key === 'receiverInfo') {
			return (
				<div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>
					<div style={ { opacity: 0, height: "0", width: "0" } }>
						<FxgLiveTag platform={ pack.platform } orderId={ splitFxgTid(pack.ptTid) } ptTid={ pack.ptTid } buyerNick={ pack.buyerNick } sellerId={ pack.sellerId } />
					</div>
					<div>{pack.receiverName} {pack.receiverMobile}</div>
					<div>{pack.receiverState} {pack.receiverCity} {pack.receiverreceiverDistrictState} {pack.receiverreceiverDistrictState} {pack.receiverAddress}</div>
				</div>
			);
		}
		// 实付金额
		if (item.key === "payInfo") {
			return (
				<div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>
					<div>{pack.payment}</div>
					<div className="r-fs-12">含运费{pack.postFee}元</div>
				</div>
			);
		}
		// 付款时间
		if (item.key === "payTime") {
			return (
				<div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>
					<div>{pack[item.key]}</div>
				</div>
			);
		}
		// 直播打印状态
		if (item.key === "livePrintStatus") {
			return (
				<div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>
					<div>{livePrintStatusFormat[pack[item.key]]}</div>
					{
						pack[item.key] === 2 && <div className="r-c-error">(不符合订单筛选规则)</div>
					}
					{
						pack[item.key] === 3 && pack.errorMsg && <div className="r-c-error">({pack.errorMsg})</div>
					}
				</div>
			);
		}
		// 操作
		if (item.key === "operate") {
			return (
				<div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }><div className="r-pointer r-fc-1890FF" onClick={ () => { sendPoint(Pointer.点击重打标签); setSingleItem([pack]); } }>重打标签</div></div>
			);
		}

		// 直播场次
		if (item.key === "liveNo") {
			return <div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>{ pack.liveNo}</div>;
		}

		// 达人信息
		if (item.key === "authorName") {
			return (
				<div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>
					<div className="r-flex r-fd-c">
						{
							pack?.authorIdList?.map((item:any, index:number) => {
								const authorName = pack?.authorNameList?.[index] || '-';
								const authorId = item;
								return (
									<div key={ authorId } className="r-wb-bw">
										<span>{authorName || '-'}</span>
										{
											authorId && (
												<span className="r-c-black65 r-ml-4">({authorId})</span>
											)
										}
									</div>
								);
							})
						}
					</div>
				</div>
			);
		}

		// 昵称
		if (item.key === "buyerNick") {
			return <div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>{ pack.buyerNick}</div>;
		}
		return <div className={ `${style['table-item']}` } style={ { width: `${item.width || 80}px` } }>{ pack[item.key]}</div>;
	};
	return (
		<div style={ { display: "inline-flex", justifyContent: "space-between", minWidth: "100%" } } className={ `${style['table-col']}` }>
			<CheckboxCom pack={ pack } index={ index } />
			{
				(listItem.filter(i => (hasPlatformAuthInfo() ? true : !["nhbNick"].includes(i.key)))).map((item) => (
					<div key={ item.key }>
						<Fragment key={ `${item.key}` }>
							{renderItem(item, pack, index)}
						</Fragment>
					</div>

				))
			}
		</div>
	);
};

export default observer(ListItem);
