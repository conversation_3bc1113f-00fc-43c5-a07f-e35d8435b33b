import React, { memo } from 'react';
import { observer } from 'mobx-react';
import style from '../index.module.scss';
import CombineTag from "@/components-biz/Trade/CombineTag";
import WaresInfo from "@/components-biz/WaresInfo";
import { IPackage } from '@/pages/Trade/interface';

const ProductContent = memo(observer(
	(props:{pack:IPackage, width?: number}) => {
		const { pack, width } = props;

		return (
			<div style={ { width: `${width || 160}px` } } className={ `${style['width160']} ${style['table-item']} ${style['product-content']}` }>
				<div className="r-relative">
					<WaresInfo
						isShowHoverName
						hoverImgWidth={ 300 }
						hoverImgHeight={ 300 }
						wareName={ pack.title }
						skuName={ pack.skuPropertiesName }
						onlyImg
						imgUrl={ pack.picUrl }
					/>
				</div>
				<div style={ { marginLeft: "6px" } }>
					<div>
						{/* 商品名称 */}
						<p className="r-mt-4 r-lh-16">
							{pack.itemTitle}
						</p>
					</div>
					<div>
						{/* 规格名称 */}
						<p className="r-mt-4 r-lh-16">{pack.skuName} </p>
					</div>
				</div>
			</div>
		);
	}
));

export default ProductContent;