.table-title-header-con {
	display: inline-flex;
	align-items: center;
	min-width: 100%;
	height: 54px;
	background: rgba(0, 0, 0, 0.08);	
	user-select: none;
	justify-content: space-between;

	.table-header-item {
		position: relative;
		text-align: center;
		font-size: 13px;
		color: rgba(0, 0, 0, 0.85);
		font-weight: 500;
	}
}
.table-col {
	display: flex;
	align-items: center;
	text-align: left;
	min-height: 54px;
	position: relative;
	padding: 6px 0;
	&:nth-of-type(2n - 1) {
		background: #f5f5f5;
	}
}
.printed-icon {
	position: absolute;
	left: -30px;
	color: #6632B8 !important;
}

.width30 {
	width: 30px;
}
.width200 {
	width: 200px;
}
.width50 {
	width: 50px;
}
.width80 {
	width: 80px;
}
.width120 {
	width: 120px;
}
.width140 {
	width: 140px;
}
.width160 {
	width: 160px;
}
.product-content {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	font-size: 12px;
}
// .packageId {
// 	word-break: break-all;
// }
.table-item {
	padding: 0 4px;
	word-break: break-word;
	text-align: center;
}

.scrollLine {
	position: fixed;
	bottom  : 70px;
	height  : 15px;
	left    : 0;
	right   : 0;
	width   : 100%;
	overflow: auto;
	z-index : 999;
}