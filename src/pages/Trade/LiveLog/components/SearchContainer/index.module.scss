.searchContainer {
    padding: 8px 0;

    :global {
        .ant-form-inline .ant-form-item {
            margin: 0px;
            margin-right: 8px;
            margin-bottom: 8px
        }
    }

    .range-condition {
        flex: 0.5;
        margin-right: 8px;
    }

    .trade-search-form {
        width: 100%;
        display: flex;

        .trade-search-left {
            max-width: 88%;
            display: flex;
            flex-wrap: wrap;
        }

        .trade-search-right {
            display: flex;
        }
    }
}

.condition1 {
    width: 160px;
}

.condition2 {
    width: 328px;
}

.condition3 {
    width: 496px;
}

.tip {
    margin-left: -7px;
}

.advance-search-item {
    // width: 115px;
}

.advance-search-item:hover {

    .advance-search-edit {
        visibility: visible;
    }
}

.advance-search-edit {
    visibility: hidden;
}

.advance-search-list {
    overflow: hidden;
    flex-wrap: wrap;

}

.live-option {
    padding: 12px;
    color: #333;
    border-bottom: 1px solid #ddd;
    cursor: pointer;

    &:hover {
        background-color: #f8f8f8;
    }
}