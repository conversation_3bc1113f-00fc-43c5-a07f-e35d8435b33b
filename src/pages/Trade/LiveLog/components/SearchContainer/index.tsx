import React, {
	useState,
	useCallback,
	useEffect,
	useImperativeHandle,
	forwardRef
} from "react";
import cs from "classnames";
import { Form, Select, Button, Input } from "antd";
import s from "./index.module.scss";
import InputMulti from "@/components/Input/InputMulti";
import { ItemTakeGoodsLabelSelectWithPageRequest } from "@/types/trade/takeGoodsLabel";

const searchInitValue = {};

const SearchContainer = (props, ref?: React.Ref<any>) => {
	const [form] = Form.useForm();
	const { handleSearch, isSearching, queryGroupBy, liveRecord, initParams, setInitParams } =	props;
	const [formData, setFormData] = useState({});
	const [selectedLive, setSelectedLive] = useState({});
	const [open, setOpen] = useState(false);

	useEffect(() => {
		if (isSearching) return;
		if (initParams && initParams.liveTradePrintTaskIdList) {
			reset(true, initParams);
			setSelectedLive(liveRecord[0] || {});
		} else {
			setSelectedLive({});
			reset(true);
		}
	}, [initParams]);

	useImperativeHandle(ref, () => ({
		labelSearchChange: (name, value) => {
			form.setFieldsValue({
				[name]: value,
			});
		},
	}));

	const handleFinish = useCallback(
		async(v) => {
			console.log("params:", v);
			let params: ItemTakeGoodsLabelSelectWithPageRequest = {
				queryGroupBy,
				authorName: v.authorName,
				authorId: v.authorId,
			};
			if (v.liveTradePrintTaskIdList) {
				params.liveTradePrintTaskIdList = [v.liveTradePrintTaskIdList];
			}
			if (v.ptTid) {
				if (v.ptTid.includes(',')) {
					params.ptTidList = v.ptTid.split(',');
				} else {
					params.ptTidList = [v.ptTid];
				}
			}

			handleSearch && handleSearch(params);
		},
		[handleSearch]
	);

	const reset = (reSearch, initParams = {}) => {
		setSelectedLive({});
		form.resetFields();
		form.setFieldsValue({
			...searchInitValue,
			...initParams,
		});
		if (reSearch) {
			form.submit();
		}
	};

	const onFormValueChange = (changedValues: {}, allValues) => {
		setFormData(allValues);
	};

	const handleChange = useCallback(
		(item) => {
			console.log("handleChange", item);
			setOpen(false);
			setSelectedLive(item);
			form.setFieldsValue({
				liveTradePrintTaskIdList: item.id
			});
		},
		[liveRecord]
	);

	const dropdownRender = () => {
		return (
			<div style={ { maxHeight: "400px", overflow: "auto" } }>
				{liveRecord.map((item) => {
					return (
						<div
							key={ item.id }
							className={ s["live-option"] }
							onClick={ () => {
								handleChange(item);
							} }
						>
							<div>直播场次：{item.liveNo}</div>
							<div>
								场次时间：{item.livePrintTimeStart}-
								{item.livePrintTimeActualEnd || item.livePrintTimeEnd}
							</div>
							<div>共计时长：{item.liveTimeStr}</div>
						</div>
					);
				})}
				<div className="r-ta-c r-pd-5">仅保留最近7天的直播打印场次</div>
			</div>
		);
	};

	return (
		<div className={ cs(s.searchContainer) }>
			<Form
				name="trade-search"
				className={ `${s["trade-search-form"]} print-batch-search-con` }
				size="small"
				layout="inline"
				form={ form }
				initialValues={ searchInitValue }
				onFinish={ handleFinish }
				onValuesChange={ onFormValueChange }
			>
				{/* 选择直播自动打印场次 */}
				<Form.Item className={ cs(s.condition2) } name="liveTradePrintTaskIdList" style={ { marginBottom: 0 } }>
					<div style={ { display: "none" } }>{ `${selectedLive?.livePrintTimeStart}-${selectedLive?.livePrintTimeEnd}` }</div>
					<Select
						value={ selectedLive?.livePrintTimeStart ? `${selectedLive?.livePrintTimeStart}-${selectedLive?.livePrintTimeEnd}` : null }
						open={ open }
						onDropdownVisibleChange={ (visible) => setOpen(visible) }
						placeholder="选择直播自动打印场次"
						dropdownRender={ dropdownRender }
					/>
				</Form.Item>
				{/* 订单编号 */}
				<Form.Item name="ptTid" style={ { marginBottom: 0 } }>
					<InputMulti
						size="small"
						placeholder="订单编号"
						style={ { width: "160px" } }
					/>
				</Form.Item>
				<Form.Item name="authorName" style={ { marginBottom: 0 } }>
					<Input
						size="small"
						placeholder="达人名称"
						style={ { width: "160px" } }
					/>
				</Form.Item>
				<Form.Item name="authorId" style={ { marginBottom: 0 } }>
					<Input
						size="small"
						placeholder="达人ID"
						style={ { width: "160px" } }
					/>
				</Form.Item>
				<Form.Item noStyle>
					<Button
						type="primary"
						className="r-ml-8 kdzs-search-btn"
						htmlType="submit"
						loading={ isSearching }
					>
						查询
					</Button>
					<Button className="r-ml-10 kdzs-search-btn" onClick={ () => { setInitParams({}); } }>
						重置
					</Button>
				</Form.Item>
			</Form>
		</div>
	);
};

export default forwardRef(SearchContainer);
