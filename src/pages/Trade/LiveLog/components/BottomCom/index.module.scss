.bottom-container {
	position: fixed;
	bottom: 0;
	background-color: #FFF;
	height: 70px;
	display: flex;
	width: 100%;
	padding: 0 40px;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0px -2px 16px 0px rgb(0 0 0 / 10%);
    z-index: 1000;
}
.bottom-left-container {
	display: flex;
	align-items: center;
}
.quick-choice-container,
.operate-container {
	border: 1px solid #e4e7ed;
	box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
	background: #ffffff;

	.quick-choice-item,
	.operate-item {
		display: flex;
		align-items: center;
		padding: 0 24px;
		height: 32px;
	}
}
.labelPushTip {
	border: 1px solid #999;
	border-radius: 4px;
	padding: 4px 6px;
}