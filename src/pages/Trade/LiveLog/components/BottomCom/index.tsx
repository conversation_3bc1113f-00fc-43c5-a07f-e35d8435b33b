import React, { useCallback, useEffect, useMemo, useState } from "react";
import { DownOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { observer } from "mobx-react";
import { Button, Form, Select, Modal } from "antd";
import _ from "lodash";
import message from "@/components/message";
import s from "./index.module.scss";
import { tradeStore } from "@/stores";
import PrintCenterAPI from "@/print/index";
import { ItemTakeGoodsLabelSelectWithPageResponse } from "@/types/trade/takeGoodsLabel";
import event from "@/libs/event";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { diffTodayDays, labelListSort } from "@/pages/Trade/utils";
import scanPrintStore from "@/stores/trade/scanPrint";

const initData = {
	selectedPrinter: '',
	tempId: '',
};

const BottomCom = (props: any) => {
	const { handleSearch, searchParams } = props;
	const {
		liveTagListStore: {
			list,
			checkedCount,
			listCheckStatus,
			printStatus,
			setPrintStatus,
			handleChoiceChange,
		},
		scanPrintStore: { printersList, defaultPrinter },
		selectedZbdXbqTemp,
		zbdXbqTempList,
	} = tradeStore;
	const { hasPlatformAuthInfo } = scanPrintStore;
	const [form] = Form.useForm();
	const [showPrintOptionModal, setShowPrintOptionModal] = useState(false);

	useEffect(() => {
		if (printStatus && printStatus.isSuccess) {
			Modal.confirm({
				title: "系统提示",
				centered: true,
				icon: <ExclamationCircleOutlined />,
				content: (
					<div>
						成功：{printStatus.successCount}, 失败：
						{printStatus.failCount}
					</div>
				),
				okText: "确定",
				cancelText: "取消",
				closable: true,
				zIndex: 9999,
				onOk: () => {
					setPrintStatus({
						isSuccess: false,
						successCount: 0,
						failCount: 0,
					});
				},
				onCancel: () => {
					setPrintStatus({
						isSuccess: false,
						successCount: 0,
						failCount: 0,
					});
				},
			});
		}
	}, [printStatus]);

	useEffect(() => {
		form.setFieldsValue({
			tempId: selectedZbdXbqTemp.Mode_ListShowId || zbdXbqTempList[0]?.Mode_ListShowId,
			selectedPrinter: defaultPrinter || printersList[0],
		});
	}, [zbdXbqTempList, printersList, selectedZbdXbqTemp, defaultPrinter]);

	const handleOk = _.debounce(() => handlePrintAct(), 500, {
		leading: true,
		trailing: false,
	}); 

	// 打印小标签
	const handlePrintAct = async(temp?: any) => {
		let selectList: ItemTakeGoodsLabelSelectWithPageResponse["data"]["list"] =			[];
		list.forEach((item) => {
			if (item.isChecked) {
				selectList.push(item);
			}
		});
		if (!selectList.length) {
			message.info("请选择要打印的订单");
			return;
		}
		await form.validateFields();
		console.log("打印标签的顺序:", selectList);
		batchPrintBhdXbq(selectList);
	
	};

	const batchPrintBhdXbq = (
		selectList: ItemTakeGoodsLabelSelectWithPageResponse["data"]["list"],
		temp?: any
	) => {
		PrintCenterAPI.batchPrintZbd({
			tempId: form.getFieldValue('tempId'),
			orderList: selectList,
			printType: "zbd",
			printer: form.getFieldValue('selectedPrinter'),
		});
		setShowPrintOptionModal(false);
	};
	const selectAllPacks = () => {
		if (listCheckStatus === 0) {
			handleChoiceChange({
				type: 0.5,
			});
		} else if (listCheckStatus === 1) {
			handleChoiceChange({
				type: 4,
			});
		}
	};
	const handleHalfClick = (e: any) => {
		e.preventDefault();
		handleChoiceChange({
			type: 2,
		});
	};

	const handleFinish = (v) => {
		console.log(v);
	};

	const handleSelectOption = useCallback(() => {
		sendPoint(Pointer.点击批量重打标签);
		if (!list.some(item => item.isChecked)) {
			message.info("请选择要打印的订单");
			return;
		}
		setShowPrintOptionModal(true);
	}, [list]);

	return (
		<>
			<div className={ s["bottom-container"] }>
				<div className={ s["bottom-left-container"] }>
					<label className="batch_tbtlt_check">
						{listCheckStatus === 0.5 ? (
							<span
								className="half-checked"
								onClick={ handleHalfClick }
							/>
						) : (
							<input
								type="checkbox"
								className="input_check packageCheckAll"
								checked={ listCheckStatus === 1 }
								value={ listCheckStatus }
								onChange={ selectAllPacks }
							/>
						)}
					</label>
					<div className="r-mr-16">{checkedCount}单</div>
				</div>
				<div>
					<Button
						className="r-mr-16"
						type="primary"
						size="large"
						style={ { width: 124 } }
						onClick={ handleSelectOption }
					>
						批量重打标签
					</Button>
				</div>
				<Modal
					centered
					title="选择打印机和标签模板"
					visible={ showPrintOptionModal }
					width="480px"
					closable={ false }
					destroyOnClose
					maskClosable={ false }
					onOk={ handleOk }
					onCancel={ () => {
						setShowPrintOptionModal(false);
						
					} }
				>
					<Form name="form" layout="inline" form={ form } onFinish={ handleFinish }>
						<Form.Item
							required
							name="selectedPrinter"
							rules={ [{ required: true, message: '请选择打印机' }] }
						>
							<Select
								size="small"
								placeholder="请选择打印机"
								style={ { width: "200px" } }
								allowClear
							>
								{printersList.map((printer) => (
									<Select.Option
										key={ printer }
										value={ printer }
									>
										{printer}
									</Select.Option>
								))}
							</Select>
						</Form.Item>
						<Form.Item
							required
							name="tempId"
							rules={ [{ required: true, message: '请选择标签模板' }] }
						>
							<Select
								size="small"
								placeholder="请选择标签模板"
								style={ { width: "200px" } }
								allowClear
							>
								{zbdXbqTempList.map((item) => (
									<Select.Option
										key={ item.Mode_ListShowId }
										value={ item.Mode_ListShowId }
									>
										{item.ExcodeName}
									</Select.Option>
								))}
							</Select>
						</Form.Item>
					</Form>
				</Modal>
				<div style={ { width: "100px" } } />
			</div>
		</>
	);
};

export default observer(BottomCom);
