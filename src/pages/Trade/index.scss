.print-batch-search-con {
	.high-light {
		color: #e63e35;
	}

	.high-light-bg {
		.ant-select-selector {
			background-color: #FFF7E6 !important;
			border          : 1px solid #FFA940 !important;
		}
	}

	.high-light-bg.ant-input,
	.high-light-bg.ant-input-affix-wrapper {
		background-color: #FFF7E6 !important;
		border          : 1px solid #FFA940 !important;
	}

	.high-light-bg.ant-input-affix-wrapper input {
		background-color: #FFF7E6 !important;
	}

	.high-light-bg.ant-picker {
		background-color: #FFF7E6 !important;
		border          : 1px solid #FFA940 !important;
	}

	.high-placeholder {
		input::placeholder {
			color: #FFC069;
		}
	}
}

.printBatchContainer {
	transform: translateZ(0);

	.batchTop {
		background-color: white;
		margin-top      : 5px;
		padding         : 0 12px 0 37px;

		.batchSearchBox {
			width          : 100%;
			box-sizing     : content-box;
			display        : flex;
			justify-content: space-between;
			padding        : 8px 0 6px;

			.batchSearch {
				height: 50px;
			}

			.helpOnline {
				height: 22px;
			}
		}

		.superSearchBox {
			width          : 100%;
			box-sizing     : content-box;
			display        : flex;
			justify-content: space-between;
		}
	}

	.batchContent {
		margin       : 10px 12px;
		padding      : 20px 25px;
		background   : white;
		border-radius: 5px;
	}
}

.batch_tb_shop {
	text-indent: 0.6em;
	background : #6ca6ab;
	line-height: 34px;
	font-weight: bold;
	border-top : 1px solid #ebf6f6;
	color      : #ffd200;
	font-size  : 13px;
	height     : 34px;
}

.package-item {
	display         : flex;
	width           : 100%;
	min-height      : 50px;
	align-items     : center;
	text-align      : left;
	padding         : 4px 0;
	box-shadow      : outset 0px -1px 0px 0px rgba(240, 240, 240, 1);
	// border-bottom: 1px solid #c6d1da;
	// border-top: 1px solid white;
	// font-family: "\5B8B\4F53" !important;
	position        : relative;
	color           : rgba(0, 0, 0, 0.85);
	word-break      : break-all;
	line-height     : 16px;
	font-size       : 12px;
}

.blue-color {
	background: #f5fafa;
}

.related-id-tip {
	background: url("https://img.alicdn.com/imgextra/i4/69942425/O1CN01iiwONr1Tmh4wxoVm5_!!69942425.png") no-repeat;
	position  : absolute;
	width     : 126px;
	height    : 23px;
	top       : -25px;

	.related-id-text {
		padding-left: 6px;
		color       : #264156;
		font-size   : 12px;
		line-height : 22px;
	}

	.mergeTrade {
		text-decoration: none;
	}

	.close-tip {
		font-size  : 12px;
		cursor     : pointer;
		color      : #ccc;
		margin-left: 6px;
		position   : absolute;
		top        : -1px;
	}
}

.decryptImg {
	color        : #fe4847;
	font-size    : 12px;
	margin-bottom: 3px;
}

.b_tidmarkImg {
	font-size  : 12px;
	color      : #fa8500;
	display    : none;
	font-weight: 600;
}

.h_tidmarkImg {
	font-size  : 12px;
	color      : #5d5d5d;
	display    : none;
	font-weight: 600;
}

.receiverMobile {
	display   : inline-block;
	margin-top: 7px;
}

.merge-text {
	display         : inline-block;
	color           : white;
	background-color: #f5821f;
	padding         : 0 2px;
	border-radius   : 2px;
}

.align_c {
	align-items: center;
}

.f_redblock {
	background: #f00;
	color     : white;
	padding   : 0 3px;
}

.villages-towns-label {
	color           : #fff;
	background-color: #0874e3;
}

.daySend,
.traceSend,
.freeSF,
.costSF,
.needSF,
.muchSale {
	display         : inline-block;
	color           : white;
	background-color: #0874e3;
	padding         : 0 2px;
	border-radius   : 2px;
}

.has-refund-label {
	display         : inline-block;
	color           : white;
	background-color: #f00;
	padding         : 0 2px;
	border-radius   : 2px;
}

.part-shiped-label {
	color           : #fff;
	background-color: red;
}

.trade-stockout-label {
	color           : #fff;
	background-color: red;
	margin-left     : 3px;
}

.gift-label,
.extra-delivery-label {
	display         : inline-block;
	color           : white;
	background-color: rgb(236, 138, 51);
	padding         : 0 2px;
	border-radius   : 2px;
}

.split-ship-label,
.preship-label,
.mendian-label {
	color           : #fff;
	background-color: #f60;
}

.cancel-preship {
	color          : #00b0ae;
	cursor         : pointer;
	text-decoration: underline;
}

.abnormal-address-label {
	display         : inline-block;
	color           : white;
	background-color: #f00;
	padding         : 0 2px;
	border-radius   : 2px;
}

.urge-ship-label {
	display      : inline-block;
	color        : #fc0314;
	border       : 1px solid #fc0314;
	padding      : 0 2px;
	border-radius: 3px;
}

.batch-send-clock {
	font-size  : 16px;
	color      : #ec3323;
	font-weight: 1000;

	.batch-clocktime-suspend {
		display  : none;
		font-size: 12px;
	}

	.clock-timeout {
		font-size: 12px;
	}
}

.smart-express-name-con {
	margin-top: 5px;
	text-align: center;

	.text {
		background-color: #3089dc;
		color           : #fff;
		border-radius   : 4px;
		padding         : 1px 2px;
	}
}

.invisible {
	visibility: hidden;
}

.highFontColor {
	font-size  : 15px;
	color      : red;
	font-weight: bold;
}

.add-beizhu-icon {
	position: relative;
	top     : 5px;
}

.product-order-num {
	word-break : normal;
	display    : inline-block;
	white-space: nowrap;
	font-size  : 14px;

	&.red-bold {
		color      : red;
		font-weight: bold;
		font-size  : 15px;
	}
}

.batch_tbtlt_username {
	position   : relative;
	display    : flex;
	align-items: center;
	// width: 120px;
}

.batch_tbtlt_index {
	margin: 0 6px 0 12px;
	width : 28px;
}

.batch_tbtlt_username,
.batch_tbtlt_message {
	text-align: left;

	.choice-check-con {
		display : inline-block;
		position: relative;
		margin  : 0 5px;

		img {
			width         : 12px;
			height        : 12px;
			vertical-align: -1px;
			margin-left   : -3px;
			cursor        : pointer;

			&.fan {
				transform        : rotate(180deg);
				-ms-transform    : rotate(180deg);
				-moz-transform   : rotate(180deg);
				-webkit-transform: rotate(180deg);
				-o-transform     : rotate(180deg);
			}
		}

		ul {
			position     : absolute;
			background   : #fff;
			box-shadow   : 0 4px 12px 0 rgba(0, 0, 0, 0.15);
			border-radius: 4px;
			width        : 132px;
			z-index      : 2;
			font-style   : normal;
			padding      : 6px 0;
			left         : -10px;
			top          : 25px;

			li {
				height      : 24px;
				line-height : 24px;
				padding-left: 12px;
				font-size   : 12px;
				color       : #365064;
				box-sizing  : border-box;
				cursor      : pointer;

				&:hover {
					background: #ffe0c4;
					color     : #fb8821;
				}

				&.bc {
					color: #bcbcbc;
				}
			}
		}
	}
}

.batch_tbtlt_num {
	// width: 45px;
	// text-align: center !important;
	// justify-content: center !important;
}

.batch_tbtlt_address {
	position     : relative;
	// width: 245px;
	min-width    : 200px;
	flex         : 1;
	padding-right: 15px;
}

.batch_tbtlt_message {
	position    : relative;
	// width: 17%;
	// max-width: 550px;
	// min-width: 160px;
	// width: 160px;
	padding     : 0 10px;
}

.batch_tbtlt_detail {
	width        : 130px;
	padding-right: 10px;
}

.batch_tbtlt_detail .isAllDown {
	width      : 15px;
	height     : 15px;
	font-size  : 0;
	line-height: 0;
	cursor     : pointer;
}

.batch_tbtlt_detail .down {
	background: url(//pddg.kuaidizs.cn//newIndex/resources/img/print/more.gif) 0 0;
}

.batch_tbtlt_detail .upward {
	background: url(//pddg.kuaidizs.cn/newIndex/resources/img/print/more.gif) 0 0;
	transform : rotateX(180deg);
}

.batch_tbtlt_zxwl {
	width: 70px;
}

.batch_tbtlt_product {
	position    : relative;
	padding     : 0 10px;
	// width: 300px;
	// min-width: 300px;
	// width: 500px;
	// flex: 1;
	display     : inline-flex;
	align-items : center;
}

.batch_tbtlt_totalPayment {
	position: relative;
	// width: 120px;
}

.batch_tbtlt_totalWeight {
	// width: 100px;
}

.batch_tbtlt_tradeId {
	// width: 200px;
	// padding: 0 10px;
}

.batch_tbtlt_tradeFrom {
	display    : flex;
	align-items: center;
	// width: 200px;
	// padding: 0 10px;
}

.batch_tbtlt_tradeLabel {

	// width: 100px;
	span {
		margin-bottom: 5px;
	}
}

.express_input_wrap {
	display       : flex;
	flex-direction: column;
	position      : relative;
	width         : 174px;

	.ant-input.ant-input-sm.ant-input-disabled.express_input {
		color : rgba(0, 0, 0, 0.65);
		width : 130px;
		cursor: text;
	}
}

.batch_tbtlt_express {
	position      : relative;
	flex-direction: column;
	// width: 190px;

	.ant-input {
		border-radius: 2px;
	}

	.express_show_more_wrap {
		margin-top   : 3px;
		margin-left  : -14px;
		padding      : 4px 6px;
		width        : 130px;
		border       : 1px solid rgba(0, 0, 0, 0.15);
		border-radius: 2px;
		background   : #ffffff;

		.express_show_more_scroll {
			overflow-x        : scroll;
			scrollbar-width   : none;
			/* Firefox */
			-ms-overflow-style: none;

			/* IE 10+ */
			&::-webkit-scrollbar {
				display: none;
				/* Chrome Safari */
			}

			>span {
				display    : inline-flex;
				align-items: center;
				height     : 20px;
				color      : rgba(0, 0, 0, 0.85);
				white-space: nowrap;
			}
		}
	}

	.recycle_elec_no {
		cursor: pointer;
	}

	.recycleElecNo_placeholder {
		width : 13px;
		height: 13px;
	}
}

.batch_tbtlt_regimentTime {
	// width: 150px;
}

.batch_tbtlt_deliveryTime {
	// width: 150px;
}

.batch_unfold_detail_con {
	border          : 12px solid #ffe7ba;
	padding         : 15px;
	border-top-width: 3px;
	font-size       : 12px;

	input {
		font-size: 12px;
	}

	.ant-btn,
	.ant-select {
		font-size : 12px !important;
		height    : 28px !important;
		align-self: baseline;
	}
}

.batch-fh-text {
	color    : #365064;
	font-size: 24px;

	// text-align: center;
	span {
		em {
			display     : inline-block;
			font-style  : normal;
			// min-width: 30px;
			color       : #00bb9c;
		}
	}
}

.print-trail-tip {
	position: relative;

	.help_xsbz_bianma {
		padding-left   : 12px;
		width          : 262px;
		background-size: 100% 100%;
		transform      : scaleX(-1);
		position       : absolute;
		right          : -37px;
	}

	.open {
		position       : absolute;
		top            : 15px;
		right          : 82px;
		text-decoration: none;
		color          : #122e67;
	}

	.close {
		top     : 15px;
		position: absolute;
		right   : 67px;
		color   : gray;
		cursor  : pointer;
	}
}

.warning-container {
	.warning-table-header-container {
		padding-right: 15px;
	}

	.warning-table-container {
		height       : 320px;
		padding-right: 15px;
	}

	.warning-table {
		border-collapse: collapse;

		th {
			background   : #f5f5f5;
			text-align   : center;
			padding      : 10px 0 10px 5px;
			border       : 1px solid #bbb;
			border-bottom: none;
		}

		td {
			text-align: center;
			padding   : 3px 0 3px 5px;
			border    : 1px solid #bbb;
			word-break: break-all;
		}
	}

	#showQrCode {
		color: #02a7f0;

		&:hover+#qrCode {
			display: block;
		}
	}

	#qrCode {
		display : none;
		position: absolute;
		right   : 0;
		top     : 20px;
	}
}

.row-dragging {
	background     : #fafafa;
	border         : 1px solid #ccc;
	display        : flex !important;
	align-items    : center !important;
	justify-content: space-around !important;
	padding        : 0 8px !important;
	height         : 32px;
	width          : 190px;

	.colset-label {
		margin      : 0 8px !important;
		margin-right: auto !important;
		width       : 108px !important;
	}
}

.row-dragging .drag-visible {
	visibility: visible;
}

.colset-container {
	position   : relative;
	width      : min-content;
	z-index    : 1001;
	user-select: none;
	max-height : calc(100vh - 300px);
	overflow-y : auto;

	.ant-table-thead {
		display: none;
	}

	.ant-table-cell {
		padding: 0 !important;
		border : none;
	}

	// drag会覆盖colset-item样式 加important
	.colset-item {
		display        : flex;
		align-items    : center;
		justify-content: space-around;
		padding        : 0 8px;
		height         : 32px;
		width          : 230px;
		font-size      : 14px !important;

		.colset-label {
			margin      : 0 8px;
			margin-right: auto;
			width       : 128px;
		}
	}
}

.table-title-header-con.batch_tbtlt_username {
	border-left: #b9c7d3 1px solid;
	min-width  : 180px;
}

.batch_tbtlt_check {
	padding: 0 8px;
}

.addr-abnormal-dialog {
	position  : fixed;
	top       : 0;
	left      : 0;
	width     : 100vw;
	height    : 100vh;
	background: rgba(0, 0, 0, 0.6);
	z-index   : 10000;

	.addr-abnormal-con {
		position   : fixed;
		top        : 50%;
		left       : 50%;
		margin-left: -273px;
		margin-top : -231px;

		.total-num {
			position  : absolute;
			color     : #fffc00;
			font-size : 26px;
			top       : 82px;
			left      : 163px;
			width     : 54px;
			text-align: center;
		}

		.now-num {
			position  : absolute;
			color     : #fffc00;
			font-size : 28px;
			top       : 83px;
			right     : 63px;
			width     : 54px;
			text-align: center;
		}
	}
}

//半选标签
.half-checked {
	cursor        : pointer;
	display       : inline-block;
	width         : 13px;
	height        : 13px;
	// margin-top: -5px;
	vertical-align: middle;
	background    : url(//pddg.kuaidizs.cn//newIndex/resources/img/print/half-check.png);
}

.ant-dropdown-menu {
	padding: 0 !important;

	.quick-choice-item,
	.operate-item {
		color: #666;

		&:hover {
			background: #f5f5f5;
			color     : #fd8204;
		}
	}
}

.th-filter-container {
	position        : absolute;
	top             : 0;
	left            : 10px;
	width           : 240px;
	border-radius   : 4px;
	z-index         : 1001;
	color           : #666666;
	border          : 1px solid #e4e7ed;
	box-shadow      : 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
	background-color: #fff;

	.th-filter-title {
		padding         : 5px 10px 0;
		// border-bottom: 1px solid #eee;
		text-align      : center;

		.th-filter-main-title {
			color      : rgba(0, 0, 0, 0.85);
			font-weight: bold;
			font-size  : 14px;
		}

		.th-filter-sub-title {
			font-size: 12px;
		}
	}

	.th-filter-body {
		margin: 12px 0 16px;

		.th-filter-item {
			display    : flex;
			align-items: center;
			font-size  : 14px;
			padding    : 0 8px;
			height     : 32px;

			&:hover {
				background: #f5f5f5;
				color     : #fd8204;
			}

			.th-filter-label {
				margin-left: 8px;
			}

			.th-filter-checkbox {
				width: 16px;
			}
		}

		.th-filter-input-con {
			display   : flex;
			flex-wrap : wrap;
			// gap: 8px 16px;
			width     : 180px;
			text-align: left;
		}
	}

	.th-filter-footer {
		margin         : 0 10px 12px;
		display        : flex;
		justify-content: center;
	}

	&.payment {
		position: relative;
		top     : 0;
		left    : 0;

		.th-filter-title {
			text-align: left;
		}

		.th-filter-label {
			margin: 0 !important;
		}

		.th-filter-body {
			.th-filter-item {
				justify-content: space-between;
			}
		}
	}

	&.product-setting {
		width: 340px !important;
		top  : 50px;
		left : auto;

		.th-filter-body {
			.th-filter-item {
				margin-bottom: 8px;
				height       : auto;

				.th-filter-label {
					align-self: flex-start;
				}
			}

			.th-filter-input-con {
				width: 220px;
			}
		}
	}
}

.total-payment-popover-container {
	.ant-popover-inner-content {
		padding: 0 !important;
	}
}

.batch_setfhd,
.batch_setgj,
.batch_setld,
.batch_setkdd,
.batch_setjhd,
.batch_express_remind {
	width     : 60px;
	height    : 67px;
	background: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/icon_circle.gif) no-repeat;
	display   : block;
}

.batch_setkdd {
	background-position: -67px 0;
	width              : 62px;
}

.batch_setjhd {
	background-position: -68px -156px;
	width              : 62px;
}

.batch_setgj {
	background-position: left -178px;
}

.hand-order-modal {
	.ant-modal-body {
		max-height: 750px !important;
		overflow-y: auto !important;
	}

	.ant-input-disabled {
		color: #666 !important;
	}
}

.template-con {
	width: 100%;
}

.template-con-fixed {
	position        : fixed;
	top             : 48px + 44px;
	left            : 0;
	margin          : 0 !important;
	z-index         : 100;
	background-color: #fff;
	box-shadow      : 0px -2px 16px 0px rgba(0, 0, 0, 0.1);
}

.fast-com {
	position: fixed;
	bottom  : 120px;
	right   : 4px;
	z-index : 10;

	div {
		opacity: 0.1;
		height : 30px;

		&:hover {
			opacity: 0.2;
		}
	}
}

.col-resize-con {
	position : absolute;
	top      : 50%;
	right    : 0;
	transform: translateY(-50%);
	cursor   : col-resize;
	display  : none;
}

.batch_tbtlt_address,
.batch_tbtlt_num,
.batch_tbtlt_authorInfo,
.batch_tbtlt_tradeLabel,
.batch_tbtlt_express,
.batch_tbtlt_message,
.batch_tbtlt_product,
.batch_tbtlt_product_2,
.batch_tbtlt_totalPayment,
.batch_tbtlt_regimentTime,
.batch_tbtlt_deliveryTime,
.batch_tbtlt_tradeId,
.batch_tbtlt_tradeType,
.batch_tbtlt_tradeFrom,
.batch_tbtlt_totalWeight,
.batch_tbtlt_username,
.batch_tbtlt_service_tag {
	position: relative;

	&:hover {
		.col-resize-con {
			display: inline-block;
		}
	}
}

.seller_flag_tag {
	padding            : 0px 8px;
	line-height        : 16px;
	font-size          : 14px;
	border-radius      : 30px;
	border             : 1px solid #ccc;
	// background-color: #fff;
	color              : #333;
	text-align         : center;
	word-break         : break-all;
}
