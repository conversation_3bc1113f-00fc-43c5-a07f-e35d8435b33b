import React, { useEffect, useMemo, useState } from 'react';
import ReactDOM from 'react-dom';
import message from '@/components/message';
import { filterAddrDetail, getDecryptInfoForHand } from '../../utils';
import { tradeStore } from '@/stores';
import userStore from '@/stores/user';
import { filterPrintContent } from '@/utils/trade/printContent';
import tradeSetStore from '@/stores/trade/tradeSet';
import { getStockDetailContent } from '../ListItem/components/simpleCom';
import { copyEleContents } from '@/utils';
import { IPackage } from '../../interface';

export enum CheckEnum {
	订单编号,
	系统单号,
	收件人信息,
	收件人,
	联系方式,
	收件地址,
	快递名称,
	快递单号,
	产品内容,
	产品图片,
	留言,
	旗帜备注
}

const flagText = {
	'0': "灰旗",
	'1': "红旗",
	'2': "黄旗",
	'3': "绿旗",
	'4': "蓝旗",
	'5': "紫旗"
};



const borderLine = '1px solid #999';

const tidStyle: React.CSSProperties = {
	border: borderLine,
};

const Td = (props) => {
	const { children, ...rest } = props;
	return <td style={ tidStyle } { ...rest }>{children}</td>;
};

const TABLE_ID = 'copyTradeInfoTable';

const genTableNode = (packs: IPackage[], checkVal:CheckEnum[]) => {
	const {
		productSetting: {
			showPicture,
			showItemTitle,
			showOuterId,
			showShortTitle,
			showSkuAlias,
			showSkuOuterId,
			showSkuTitle,
			showSysOuterId,
			showSysOuterSkuId,
		},
	} = tradeStore;
	const { isShowZeroStockVersion } = userStore;

	const getOrderContent = (order) => {
		if (order.status == 'TRADE_CLOSED') {
			return '已取消';
		}

		const comTag = <span style={ { color: '#e02020' } }>组</span>;
		const giftTag = <span style={ { color: '#02a7f0' } }>(赠)</span>;
		return (
			<>
				{order.isGift ? <>{giftTag}&nbsp;</> : ''}
				{/* 商品标题 */}
				{showItemTitle && order.title
					? (
						<>
							{
								(!showShortTitle || (showShortTitle && showItemTitle)) && order?.isCombination ? comTag : ''
							}
							{order.title}&nbsp;
						</>
					) : ''}
				{/* 货品简称 */}
				{showShortTitle && order.titleShort && !order?.ignore
					? (
						<>
							{!showItemTitle && order?.isCombination === 1 ? comTag : ''}
							{order.titleShort}&nbsp;
						</>
					) : ''}
				{/* 商品编码 */}
				{showOuterId && order.outerId ? <>{order.outerId}&nbsp;</> : ''}
				{/* 货品编码 */}
				{!isShowZeroStockVersion && showSysOuterId && order.sysOuterId ? <>{order.sysOuterId}&nbsp;</> : ''}

				{/* 规格名称 */}
				{showSkuTitle && order.skuPropertiesName ? <>{filterPrintContent(order.skuPropertiesName)}&nbsp; </> : ''}
				{/* 规格别名 */}
				{showSkuAlias && order.skuAlias ? <>{order.skuAlias}&nbsp; </> : ''}
				{/* 规格编码 */}
				{showSkuOuterId && order.outerSkuId ? <>{order.outerSkuId}&nbsp; </> : ''}
				{/* 货品规格编码 */}
				{!isShowZeroStockVersion && showSysOuterSkuId && order.sysOuterSkuId ? <>{order.sysOuterSkuId}&nbsp; </> : ''}
				{/* 数量 */}
				<span className={ order.num > 1 ? 'r-c-error' : '' }>{ order.num}</span>&nbsp;
				{/* 缺货屯货情况 */}
				{getStockDetailContent(order)}
			</>
		);
	};

	const getOrderImg = (order) => {
		const picSrc = showPicture ? order.picPath : order.sysPicPath;
		return picSrc ? <>&nbsp;<img src={ picSrc } width="40" height="40" alt="产品图片" /></> : '无图片';
	};

	const getTids = (trades) => {
		const tids = trades.map(trade => trade.tid.replace("A", ""));
		return tids.join(',');
	};

	const getPtTids = (trades) => {
		const ptTids = trades.map(trade => trade.ptTid.replace("A", ""));
		return ptTids.join(',');
	};
	const getSellerMemo = (trades) => {
		let sellerMemo = trades.map(trade => (`${Number(trade.sellerMemoFlag) ? flagText[trade.sellerMemoFlag] : ""}${trade.sellerMemo || ""}`));
		sellerMemo = sellerMemo.filter((item) => item);
		return sellerMemo.join(',');
	};
	const getBuyerMessage = (trades) => {
		let buyerMessage = trades.map(trade => trade.buyerMessage);
		buyerMessage = buyerMessage.filter((item) => item);
		return buyerMessage.join(',');
	};

	const getYdNoInfo = (pack, type) => {
		const { selectedTemp, selectedTempGroup, setting } = tradeStore;
		if (pack.ydNoSet?.length) {
			let ydName = [];
			let ydNo = [];
			pack.ydNoSet.forEach(item => {
				const data = item.split(":");
				ydName.push(data[0]);
				ydNo.push(data[1]);
			});
			if (type === CheckEnum.快递名称) {
				return ydName.join(",");
			} else if (type === CheckEnum.快递单号) {
				return ydNo.join(",");
			}
			return "";
		} else if (pack.sids?.length && !pack.sids[0].includes("打印后生成") && selectedTemp?.ExcodeName && setting?.groupPrintSetJsonString?.openMergePrint == 1) {
			if (pack.sids[pack.sids.length - 1].includes("打印后生成")) {
				pack.sids.pop();
			}
			if (type === CheckEnum.快递名称) {
				return selectedTemp?.ExcodeName;
			} else if (type === CheckEnum.快递单号) {
				return pack.sids.join();
			}
			return "";
		} else if (pack.sids?.length && !pack.sids[0].includes("打印后生成") && selectedTempGroup?.groupName && setting?.groupPrintSetJsonString?.openMergePrint == 2) {
			if (pack.sids[pack.sids.length - 1].includes("打印后生成")) {
				pack.sids.pop();
			}
			if (type === CheckEnum.快递名称) {
				return selectedTempGroup?.groupName;
			} else if (type === CheckEnum.快递单号) {
				return pack.sids.join();
			}
			return "";
		} else {
			return "";
		}
	};
	return (
		<div
			style={ {
				height: 0,
				opacity: 0,
				position: 'fixed'
			} }
		>
			<table
				id={ TABLE_ID }
				style={ {
					fontSize: '12px',
					textAlign: 'center',
					lineHeight: '30px',
					border: borderLine,
					outline: 'none',
				} }
			>
				<tbody>
					{
						packs.map(pack => {
							const { name, phone, address } = getDecryptInfoForHand(pack);
							console.log('name, phone, address', name, phone, address);
							return (
								<tr key={ pack.togetherId }>
									{ checkVal.includes(CheckEnum.订单编号) ? (
										<>
											<Td>
												{
													getPtTids(pack.trades)
												}
											</Td>
										</>
									) : null}
									{ checkVal.includes(CheckEnum.系统单号) ? (
										<>
											<Td>
												{
													getTids(pack.trades)
												}
											</Td>
										</>
									) : null}
									{ checkVal.includes(CheckEnum.收件人信息) ? (
										<>
											<Td style={ tidStyle }>{name}</Td>
											<Td style={ tidStyle }>{phone}</Td>
											<Td style={ tidStyle }>
												{pack.receiverState}
												{pack.receiverCity}
												{pack.receiverDistrict}
												{address}
											</Td>
										</>
									) : null}
									{ checkVal.includes(CheckEnum.收件人) ? (
										<Td>{name}</Td>
									) : null}
									{ checkVal.includes(CheckEnum.联系方式) ? (
										<Td>{phone}</Td>
									) : null}{ checkVal.includes(CheckEnum.收件地址) ? (
										<Td>
											{pack.receiverState}
											{pack.receiverCity}
											{pack.receiverDistrict}
											{address}
										</Td>
									) : null}
									{ checkVal.includes(CheckEnum.快递名称) ? (
										<>
											<Td style={ tidStyle }>{
												getYdNoInfo(pack, CheckEnum.快递名称)
											}
											</Td>
										</>
									) : null}
									{ checkVal.includes(CheckEnum.快递单号) ? (
										<>
											<Td style={ tidStyle }>{
												getYdNoInfo(pack, CheckEnum.快递单号)
											}
											</Td>
										</>
									) : null}
									{
										pack.productOrders.map(order => {
											return (
												<React.Fragment key={ order.oid } >
													{
														checkVal.includes(CheckEnum.产品内容) ? (
															<Td style={ tidStyle }>
																{getOrderContent(order)}
															</Td>
														) : null
													}
													{
														checkVal.includes(CheckEnum.产品图片) ? (
															<Td style={ tidStyle }>
																{getOrderImg(order)}
															</Td>
														) : null
													}
												</React.Fragment>
											);
										})
									}
									{ checkVal.includes(CheckEnum.留言) ? (
										<Td>
											{
												getBuyerMessage(pack.trades)
											}
										</Td>
									) : null}
									{ checkVal.includes(CheckEnum.旗帜备注) ? (
										<Td>
											{
												getSellerMemo(pack.trades)
											}
										</Td>
									) : null}

								</tr>
							);
						})
					}
				</tbody>
			</table>
		</div>
	);
};


export const copyPackInfos = (packs: IPackage[], checkVal:CheckEnum[]) => {
	let reactNode = genTableNode(packs, checkVal);
	let insertDiv = document.createElement('div');
	insertDiv.id = 'copyDomId';
	document.body.appendChild(insertDiv);

	ReactDOM.render(
		reactNode,
		document.getElementById("copyDomId")
	);

	let dom = document.getElementById(TABLE_ID);

	if (dom) {
		copyEleContents(dom as Node);
	}

	document.body.removeChild(insertDiv);
};
