import React, {
	memo,
	useCallback,
	useEffect,
	useLayoutEffect,
	useMemo,
	useRef,
	useState
} from "react";
import { LockOutlined } from "@ant-design/icons";
import _, { cloneDeep } from "lodash";
import { Dropdown, Tooltip, Menu } from "antd";
import { observer } from "mobx-react";
import { runInAction } from "mobx";
import cs from 'classnames';
import event from "@/libs/event";
import PackageDetail from "./components/packageDetail";
import ShopTr from "./components/shopTr";
import ReceiverAddressCom from "./components/receiverAddressCom";
import TradeIndexPrintContentCom from "./components/TradeIndexPrintContentCom";
import BuyerNickCom from "./components/buyerNickCom";
import ExpressnumCom from "./components/expressnumCom";
import {
	BuyerMessageCom,
	CheckboxCom,
	CreatedTimeCom,
	IndexCom,
	OrderNumberCom,
	PackageOrdersCom,
	PayTimeCom,
	TidCom,
	PtTidCom,
	TotalPaymentCom,
	TotalReceivedPaymentCom,
	TotalWeightCom,
	TradeFromCom,
	PackPrintStatus,
	TradeType,
	SourceCom,
	DistributorCom,
	SysMemoCom,
	AuthorInfoCom,
	SysMemoComPic,
	WaveNoCom
} from "./components/simpleCom";
import "./index.scss";
import { IPackage } from "../../interface";
import Icon from "@/components/Icon";
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import TradeExceptionCom from "./components/TradeExceptionCom";
import TradeLabelCom from "./components/TradeLabelCom";
import { copyToPaste, splitFxgTid } from "@/utils";
import { PLAT_MAP } from "@/constants";
import { detectMousePosition } from "@/utils/util";
import OperateCom from "./components/operateCom";

export interface IListItemProps {
	pack: IPackage;
	orderSearchStatus?: string;
	from?: string;
	fromTrade?: boolean;
	showShopTradeCount?: boolean;
}

const ListItem = observer((props: IListItemProps) => {
	const { pack, from, fromTrade, showShopTradeCount } = props;
	const { columnConfig, getColWidth, hotGoodsColumnConfig, shopTradeCount, shopTradeCountMap, tradeOrderSortType, tradeOrderSortTypeList, setting, tradeListStore: { togglePackageSelectStatus, toggleTogetherDetail } } = tradeStore;
	const { shopList } = userStore;
	const [shopObj, setShopObj] = useState({});
	const [placement, setPlacement] = useState<any>("bottomCenter");
	const [optMenu, setOptMenu] = useState<JSX.Element>(<div />);
	const optMenuComp = useRef("");
	const copyTids = () => {
		copyToPaste(splitFxgTid(pack?.ptTids?.[0].split('|')?.join(',')));
	};
	const optMenuContainer = (
		<div className="opt-menu-container" onClick={ (e) => { e.stopPropagation(); } }>
			<div className="opt-menu-item opt-tid-container" onClick={ copyTids }>
				<div style={ { whiteSpace: "nowrap" } }>复制订单编号</div>
			</div>
			<div className="opt-menu-item" onClick={ () => { toggleTogetherDetail(pack); } }>
				{pack?.isExpand ? "收起订单" : "展开订单"}
			</div>
			<div>
				{optMenu}
			</div>
		</div>
	);
	useEffect(() => {
		const shopObj = shopList?.reduce((init, item) => {
			init[item.sellerId] = item;
			return init;
		}, {});
		setShopObj(shopObj || {});
	}, [shopList]);
	useEffect(() => {
		let resizeObserver: any;
		let ele: any;
		if (from === 'virtual') {
			resizeObserver = new ResizeObserver((entries) => {
				for (let entry of entries) {
					let height = entry?.target?.clientHeight;

					if (height !== 0) {
						runInAction(() => {
							pack.height = height;
						});
					}
				}
			});
			ele = document.querySelector(`#listItem${pack.togetherId.replace(/\|/g, '_')}`);
			if (ele && resizeObserver) {
				resizeObserver.observe(ele);
			}
		}
		return () => {
			if (resizeObserver && ele) {
				resizeObserver.disconnect();
			}
		};
	}, []);



	const shopCountRender = (sellerId) => {
		const item = shopTradeCountMap[sellerId] || {};
		return <div className="r-pd-5" style={ { background: "#FFF7E6", fontWeight: "bold" } }>{PLAT_MAP[item.platform?.toLocaleLowerCase()]}-{item.sellerNick}（{item.tradeNum}订单）</div>;
	};


	const showShopTradeCountMemo = useMemo(() => {
		let flag = false;
		if (tradeOrderSortType === "ORDER_PLATFORM_ITEM_ID_SKU_ID") {
			flag = true;
		} else {
			const cur = tradeOrderSortTypeList.find(i => i.key === tradeOrderSortType);
			if ((cur?.customOrderRule1 === "TRADE_PLATFORM" && cur?.customOrderRule2 === "TRADE_SELLER_ID") || cur?.customOrderRule1 === "TRADE_SELLER_ID") {
				flag = true;
			}
		}
		return setting?.printSetExpandDTO?.showShopTradeCount && flag && showShopTradeCount && shopTradeCount?.length > 0;
	}, [setting, tradeOrderSortType, showShopTradeCount, shopTradeCount]);

	return (
		<div
			className="position-wrap"
			id={ `listItem${pack.togetherId.replace(/\|/g, '_')}` }
			key={ `${pack.platform}-${pack.togetherId}` }
			onClick={ () => { togglePackageSelectStatus(props.pack); } }
		>
			{
				showShopTradeCountMemo ? shopCountRender(pack.sellerId) : null
			}
			<Dropdown
				overlay={ optMenuContainer || <div /> }
				trigger={ ['contextMenu'] }
				placement={ placement }
				destroyPopupOnHide
			>

				<div
					className={
						cs(
							"packageItem",
							"package-item",
							pack.index % 2 !== 0 ? 'pack-zebra' : '',
							pack.isChecked ? "pack-selected" : "",
							pack.isPending ? "fc-pending" : "",
							pack.isExpand ? "batch_unfold" : "",
							pack.shipStatus === 'alreadyShipped' ? 'fc-already-shipped' : ''
						)
					}
					onContextMenu={ (e) => {
						const position = detectMousePosition(e, 150, 400);
						setPlacement(position);
						setOptMenu(optMenuComp.current?.getMenu());
					} }

				>
					{pack.isPending && (
						<Tooltip placement="right" title="订单挂起">
							<div className="r-trade-pending-icon" >
								<Icon type="suo_mian" />
							</div>
						</Tooltip>
					)}
					{/* 打印标记 */}
					<PackPrintStatus shipListPrintStatus={ pack.shipListPrintStatus } waybillPrintStatus={ pack.waybillPrintStatus } labelPrintStatus={ pack.labelPrintStatus } />
					<CheckboxCom pack={ pack } index={ pack.index } />
					{pack?.trades && (fromTrade ? columnConfig.userConfig : hotGoodsColumnConfig).filter(item => item.ischecked).map((col: any) => (
						<React.Fragment key={ col.key }>
							{col.key === "index" && (
								<IndexCom index={ pack.index } />
							)}
							{col.key === "source" && (
								<SourceCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "distributor" && (
								<DistributorCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "buyerNick" && (
								<BuyerNickCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "orderNumber" && (
								<OrderNumberCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "receiverAddress" && <ReceiverAddressCom pack={ pack } minWidth={ col.width } />}

							{col.key === "tradeIndexPrintContent" && (
								<TradeIndexPrintContentCom pack={ pack } minWidth={ col.width } />
							)}

							{col.key === "expressnum" && (
								<ExpressnumCom colWidth={ getColWidth(col.key) } pack={ pack } />
							)}
							{col.key === "serviceLabel" && (
								<TradeExceptionCom colWidth={ getColWidth(col.key) } pack={ pack } />
							)}
							{col.key === "payTime" && (
								<PayTimeCom colWidth={ getColWidth(col.key) } payTime={ pack.payTime } />
							)}
							{col.key === "created" && (
								<CreatedTimeCom colWidth={ getColWidth(col.key) } createdTime={ pack.createTime } />
							)}
							{col.key === "totalReceivedPayment" && (
								<TotalReceivedPaymentCom
									totalReceivedPayment={ pack.totalReceivedPayment }
									colWidth={ getColWidth(col.key) }
								/>
							)}
							{col.key === "totalPayment" && (
								<TotalPaymentCom
									trades={ pack.trades }
									platform={ pack.platform }
									serviceTagList={ pack.serviceTagList }
									totalPrePostFee={ pack.totalPrePostFee }
									totalRefundPostFee={ pack.totalRefundPostFee }
									totalPayment={ pack.totalPayment }
									totalPostFee={ pack.totalPostFee }
									sfExpressFee={ pack.sfExpressFee }
									hasSfExpressService={ pack.hasSfExpressService }
									paymentSensitive={ pack.trades?.findIndex((item) => item.paymentSensitive) > -1 }
									colWidth={ getColWidth(col.key) }
								/>
							)}
							{col.key === "tradeLabel" && (
								<TradeLabelCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "totalWeight" && (
								<TotalWeightCom colWidth={ getColWidth(col.key) } totalWeight={ pack.totalWeight } />
							)}
							{col.key === "tid" && (
								<TidCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "ptTid" && (
								<PtTidCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "tradeFrom" && (
								<TradeFromCom shopObj={ shopObj } pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "operate" && (
								<OperateCom pack={ pack } cRef={ optMenuComp } />
							)}
							{col.key === "packageOrders" && (
								<PackageOrdersCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "storageName" && (
								<>
									<div style={ { width: getColWidth(col.key), paddingRight: '3px' } } className={ `batch_tbtlt_deliveryTime ` }>
										{pack.storageName}（{pack.storageTypeDesc || "默认仓"}）
									</div>
								</>
							)}
							{col.key === "buyerMessage" && (
								<BuyerMessageCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "sysMemo" && (
								<SysMemoCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "sysMemoPic" && (
								<SysMemoComPic pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "tradeType" && (
								<TradeType packTotalType={ pack.trades[0].tradeTypeTagStr } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "authorInfo" && (
								<AuthorInfoCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
							{col.key === "waveNoList" && (
								<WaveNoCom pack={ pack } colWidth={ getColWidth(col.key) } />
							)}
						</React.Fragment>
					))}

				</div>
			</Dropdown>

			{pack.isExpand && (
				<PackageDetail packInfo={ pack } />
			)}
		</div>
	);
});

export default ListItem;
