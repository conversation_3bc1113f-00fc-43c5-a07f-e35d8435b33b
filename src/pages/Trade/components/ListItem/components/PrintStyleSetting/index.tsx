import { useRequest, useSetState } from "ahooks";
import { Form, Modal, Space, Checkbox, Radio, Select, Input, Alert, RadioChangeEvent, Tooltip } from "antd";
import { useForm } from "antd/es/form/Form";
import { observer } from "mobx-react";
import React, { useCallback, useEffect, useState } from "react";
import { InfoCircleOutlined } from "@ant-design/icons";
import { cloneDeep, isUndefined } from "lodash";
import { TradePrintContentSetRule } from "@/types/trade/tradeSet";
import tradeSetStore from "@/stores/trade/tradeSet";
import { useStores } from "@/stores/tool";
import {
	getGoodsNameSetMap,
	goodsNumDisplayMap,
	isTogetherMap,
	separatorSign,
	unitStyle,
	printGiftOrder,
	getSkuSetMap,
	PrintSetBreakPage,
	PrintSetBreakLine,
	PrintSetXBreakLine,
	getChildSetMap,
	getMarketStallSetMap,
	getOtherSetMap,
	ItemSortConfig,
	ItemSortRule,
	ItemSortConfigEnum,
	SkuSetFrontBack,
	CombinationShowEnum
} from "./constant";
import s from './index.module.scss';
import { ResponseTypeOfKDZS } from "@/types/schemas/common";
import { TradePrintSavePrintStyleApi } from "@/apis/trade/tradeSet";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import message from "@/components/message";
import userStore from "@/stores/user";
import { stockVersion } from "@/constants";
import { renderStockVersionTitleSetComp, renderNoStockVersionTitleSetComp, renderNoStockVersionSkuSetComp, renderStockVersionSkuSetComp, getShowCombinationVal } from "./utils";

interface PrintStyleSettingProps{
    visible: boolean;
    onCloseModal?: () => void;
}

const translateObjToArray = (obj: TradePrintContentSetRule, keys:string[]) => {
	const val: string[] = [];
	keys?.forEach(key => {
		if (['useSysOuterId', 'useSysOuterSkuId'].includes(key) && +obj?.[key] !== 0) {
			val.push(key);
		} else if (key == 'useItemNo' && obj?.showSequenceSku.includes('itemNo')) {
			val.push(key);
		} else if (key == 'useWarehouseSlotName' && obj?.showSequenceSku.includes('warehouseSlotName')) {
			val.push(key);
		} else if (obj?.[key] === true || obj?.[key] === '1') {
			val.push(key);
		}
	});
	return val;
};

const swapArr = (arr:any[], index1:number, index2:number) => {
	const item = arr[index1];
	arr[index1] = arr[index2];
	arr[index2] = item;
	return arr;
};


const translateSetToString = (dataMap : {
	name:string,
}[], value:string[]) => {
	let str = dataMap.map(item => {
		return value.includes(item.name) ? '1' : '0';
	});
	return str.join('');
};

const { Option } = Select;

// !!! 打印内容设置添加设置时，需要同步修改手工单生成打印内容
const PrintStyleSetting:React.FC<PrintStyleSettingProps> = observer((props) => {
	const store : typeof tradeSetStore = useStores('tradeSetStore');
	const [form] = useForm();
	const [titleSet, setTitleSet] = useState<string>('000');
	const [skuSet, setSkuSet] = useState<string>('000');
	const [marketStallSet, setMarketStallSet] = useState([]);
	const [skuSetFrontBack, setSkuSetFrontBack] = useState(SkuSetFrontBack.显示在规格前);
	const [isShowAutoLine, setIsShowAutoLine] = useState(false);
	const [isShowSkuFrontBack, setIsShowSkuFrontBack] = useState(false);
	const [showSeparatorSign, setShowSeparatorSign] = useState(false);
	const [childGoodsShowList, setChildGoodsShowList] = useState([]);
	const [showCustomUnit, setShowCustomUnit] = useState(false);

	const { isShowZeroStockVersion } = userStore;

	// * 宝贝名称显示排序
	const [titleSort, setTitleSort] = useState<string[]>();
	// * 规格颜色显示排序
	const [skuSort, setSkuSort] = useState<string[]>();
	// * 自定义显示排序
	const [customSort, setCustomSort] = useState<string[]>();
	const [showCustomSort, setShowCustomSort] = useState(false);
	// * 组合货品显示
	// const [combinationSet, setCombinationSet] = useState(false);
	const [showCombinationInfoType, setShowCombinationInfoType] = useState(1);

	const [partPrintSet, setPartPrintSet] = useSetState<{
		isTogether?: number[];
		breakpage?:number;
		breakPageInfinite?:number;
		useShortNameEmpty? :number;
		useOuterIidEmpty?: number;
		useOthersWithStall?: number;
		showSkuAliasEmpty?: number;
		useRuleOutIidEmpty?: number
	}>({});

	const {
		visible,
		onCloseModal,
	} = props;

	const [itemSortConfig, setItemSortConfig] = useState(0);

	useEffect(() => {
		const rule = store.printContentSet;
		setTitleSort(rule?.showSequence?.split(',')?.filter(Boolean));
		let showSequenceSku = rule?.showSequenceSku;
		if (rule?.showOrderPrice && !rule?.showSequenceSku.includes("orderPrice")) {
			showSequenceSku += ",orderPrice";
		} else if (!rule?.showOrderPrice && rule?.showSequenceSku.includes("orderPrice")) {
			showSequenceSku = showSequenceSku.replace("orderPrice", "");
		}
		setSkuSort(showSequenceSku?.split(',')?.filter(Boolean));

		const { userInfo } = userStore;

		let titleSetKeys = ['useOuterIid', 'useShortName', 'useTitle'];
		let skuSetKeys = ['useRuleOutIid', 'useSkuName', 'showSkuAlias'];
		let marketStallSetKeys = [];
		if (userInfo?.version === stockVersion.库存版) {
			// * 库存版额外添加了货品编码和货品规格编码维度
			titleSetKeys.splice(1, 0, 'useSysOuterId');
			skuSetKeys.splice(1, 0, 'useSysOuterSkuId');
			skuSetKeys.push('useItemNo');
			skuSetKeys.push('useWarehouseSlotName');
		}
		skuSetKeys.push('showOrderPrice');

		const titleSetValue = translateObjToArray(rule, titleSetKeys);
		const titleSet = translateSetToString(getGoodsNameSetMap(), titleSetValue);
		setTitleSet(titleSet);

		const skuSetValue = translateObjToArray(rule, skuSetKeys);
		console.log(skuSetKeys, skuSetValue);
		const skuSet = translateSetToString(getSkuSetMap(), skuSetValue);
		console.log(skuSet);
		setSkuSet(skuSet);

		// useOuterIidEmpty 接口参数设计的有点坑
		// useNumIid : useOuterIidEmpty == 2 , useOuterIidEmpty :useOuterIidEmpty == 1
		let useOuterIidEmpty;
		if (rule?.useNumIid) {
			useOuterIidEmpty = 2;
		} else {
			useOuterIidEmpty = rule?.useOuterIidEmpty ? 1 : 0;
		}
		const fieldValue = {
			titleSet: titleSetValue,
			useShortNameEmpty: rule?.useShortNameEmpty,
			useOuterIidEmpty,
			useOthersWithStall: rule?.useOthersWithStall,
			useSysOuterId: rule?.useSysOuterId, // 货品编码

			skuSet: skuSetValue,
			showSkuAliasEmpty: rule?.showSkuAliasEmpty ? 1 : 0,
			useRuleOutIidEmpty: rule?.useRuleOutIidEmpty ? 1 : 0,
			useSysOuterSkuId: rule?.useSysOuterSkuId, // 货品规格编码

			showStyle: rule?.showStyle,
			separatorSign: rule?.separatorSign?.includes("userCustomize") ? "userCustomize" : rule?.separatorSign,
			separatorSignCustomize: rule?.separatorSign?.includes("userCustomize") ? rule?.separatorSign.replace("userCustomize", "") : "",
			isTogether: rule?.isTogether == 0 ? [] : [rule?.isTogether],
			displayPrintOrderNum: translateObjToArray(rule, ['displayPrintOrderNum']),
			showOrderPrice: rule?.showOrderPrice ? 1 : 0,
			breakPageInfinite: rule?.breakPageInfinite || 0,
			breakpage: rule?.breakpage,
			breakpageNum: rule?.breakpageNum,
			breakline: rule?.breakline,
			breaklineNum: rule?.breaklineNum,
			goodsNumDisplay: rule?.goodsNumDisplay == 1 ? 3 : rule?.goodsNumDisplay,
			goodsNumDisplayType: rule?.goodsNumDisplay == 1 ? 1 : 9999,
			unitStyle: rule?.unitStyle == 0 ? 1 : rule?.unitStyle,
			unitStyleType: rule?.unitStyle == 0 ? 0 : 9999,
			unitStyleCustomize: rule?.unitStyle == 9999 ? String(rule?.extJson?.customUnitType) : "",
			printGiftOrder: rule?.printGiftOrder ?? true,
			showMarketStall: rule?.extJson?.showMarketStall?.split(',')?.filter(Boolean) ?? [],
			skuSetFrontBack: rule?.extJson?.skuSetFrontBack ?? SkuSetFrontBack.显示在规格前,
			showCombination: getShowCombinationVal(rule),
			childGoodsShowList: rule?.extJson?.childGoodsShowList?.split(',') ?? [],
			changeLineGoodsNum: rule?.extJson?.changeLineGoodsNum,
			changeLineConfig: rule?.extJson?.changeLineConfig,
			itemSortConfig: rule?.extJson?.itemSortConfig,
			itemSortRule: rule?.extJson?.itemSortRule || '1',
			showNumFlag: isUndefined(rule?.extJson?.showNumFlag) ? true : rule?.extJson?.showNumFlag,
			showType: isUndefined(rule?.extJson?.showType) ? 1 : +rule?.extJson?.showType,
			showContent: isUndefined(rule?.extJson?.showContent) ? '' : rule?.extJson?.showContent,
		};
		
		console.log(fieldValue);
		form.setFieldsValue(fieldValue);
		window.errorCollection?.customMessageUpload({
			type: `打印内容表单渲染`, 
			data: fieldValue
		});
		if (isUndefined(rule?.extJson?.showContent)) {
			let customSort = rule?.showSequence;
			if (rule?.extJson?.skuSetFrontBack === SkuSetFrontBack.显示在规格前) {
				customSort += `,${rule?.extJson?.showMarketStall?.split(',').sort((a, b) => a - b).join(',')}`;
			}
			if (rule?.showSequenceSku) {
				customSort += `,${rule?.showSequenceSku}`;
			}
			if (rule?.showOrderPrice) {
				customSort += `,orderPrice`;
			}
			if (rule?.extJson?.skuSetFrontBack === SkuSetFrontBack.显示在规格后) {
				customSort += `,${rule?.extJson?.showMarketStall?.split(',').sort((a, b) => a - b).join(',')}`;
			}
			customSort += `,itemNum`;
			setCustomSort(customSort.split(',')?.filter(Boolean));
		} else {
			let showContent = rule?.extJson?.showContent;
			if (!showContent.includes('itemNum')) showContent += ",itemNum";
			setCustomSort(showContent?.split(',')?.filter(Boolean));
		}
		setShowCustomSort(rule?.extJson?.showType == 2);
		setShowSeparatorSign(rule?.separatorSign?.includes("userCustomize"));
		setShowCustomUnit(rule?.unitStyle == 9999);
		// setCombinationSet(fieldValue.showCombinationIcon);
		setPartPrintSet({
			isTogether: fieldValue.isTogether,
			breakpage: fieldValue.breakpage,
			breakPageInfinite: fieldValue.breakPageInfinite || 0
		});
		setChildGoodsShowList(fieldValue.childGoodsShowList);
		setSkuSetFrontBack(fieldValue.skuSetFrontBack);
		setMarketStallSet(rule?.extJson?.showMarketStall?.split(',') ?? []);
		setIsShowSkuFrontBack(fieldValue.showMarketStall?.length > 0);
		setIsShowAutoLine(rule?.breakline != PrintSetBreakLine.不换行);
		setItemSortConfig(fieldValue.itemSortConfig);
		setShowCombinationInfoType(rule?.extJson?.showCombinationInfoType);
	}, [form, setPartPrintSet, store.printContentSet]);

	useEffect(() => {
		store.getPrintContentSet();
	}, [store]);

	const { run: savePrintStyleSet, loading } = useRequest(TradePrintSavePrintStyleApi, {
		manual: true,
		onSuccess: (res :ResponseTypeOfKDZS<boolean>, params:any) => {
			if (res.success) {
				store.setPrintContentSet(params[0]);
				message.success('保存成功');
				onCloseModal();
			} else {
				message.error('保存发货内容设置失败');
			}
		}
	});

	const handleSavePrintStyle = () => {
		const v = form.getFieldsValue();
		const rule = store.printContentSet;
		if (v.breaklineNum < 5) {
			sendPoint(Pointer.订单_订单打印_打印内容_换行显示_宝贝少于5);
		}
		if (v.breakpageNum > 5) {
			sendPoint(Pointer.订单_订单打印_打印内容_分页显示_宝贝数量大于5);
		}
		savePrintStyleSet({
			useOuterIid: v.titleSet.includes('useOuterIid'),
			useShortName: v.titleSet.includes('useShortName'),
			useTitle: v.titleSet.includes('useTitle'),
			useShortNameEmpty: v.useShortNameEmpty,
			useOuterIidEmpty: v.useOuterIidEmpty == 1,
			useNumIid: v.useOuterIidEmpty == 2,
			useOthersWithStall: v?.useOthersWithStall,
			useSysOuterId: v.useSysOuterId || (v.titleSet.includes('useSysOuterId') ? 1 : 0),
			showSequence: titleSort.join(','),

			useRuleOutIid: v.skuSet.includes('useRuleOutIid'),
			useSkuName: v.skuSet.includes('useSkuName'),
			showSkuAlias: v.skuSet.includes('showSkuAlias'),
			showSkuAliasEmpty: v.showSkuAliasEmpty == 1,
			useRuleOutIidEmpty: v.useRuleOutIidEmpty == 1,
			useSysOuterSkuId: v?.useSysOuterSkuId || (v.skuSet.includes('useSysOuterSkuId') ? 1 : 0),
			showSequenceSku: skuSort.join(','),
			showStyle: v.showStyle,
			separatorSign: v.separatorSign === "userCustomize" ? `${v.separatorSign}${v.separatorSignCustomize}` : v.separatorSign,
			isTogether: v.isTogether?.[0] || 0,
			displayPrintOrderNum: v.displayPrintOrderNum.includes('displayPrintOrderNum'),
			showOrderPrice: v.skuSet.includes("showOrderPrice"),
			breakPageInfinite: v.breakPageInfinite || 0,
			breakpage: v.breakpage,
			breakpageNum: v.breakpageNum,
			breakline: v.breakline,
			breaklineNum: v.breaklineNum,
			extJson: {
				...rule?.extJson,
				showMarketStall: v?.showMarketStall?.join(','),
				skuSetFrontBack,
				showCombinationIcon: v?.showCombination == CombinationShowEnum["显示(组)+组合货品信息"],
				hideCombination: v?.showCombination == CombinationShowEnum.不显示,
				childGoodsShowList: childGoodsShowList?.join(','),
				changeLineGoodsNum: v?.changeLineGoodsNum,
				changeLineConfig: v?.changeLineConfig,
				itemSortConfig: v?.itemSortConfig,
				itemSortRule: v?.itemSortRule,
				showCombinationInfoType,
				showContent: customSort?.join(','),
				showType: v?.showType,
				showNumFlag: v?.showNumFlag,
				customUnitType: v.unitStyle === 9999 ? v.unitStyleCustomize : "",
			},
			goodsNumDisplay: v.goodsNumDisplayType === 9999 ? v.goodsNumDisplay : v.goodsNumDisplayType,
			unitStyle: v.unitStyleType === 9999 ? v.unitStyle : v.unitStyleType,
			printGiftOrder: v.printGiftOrder,
		});
	};

	const handleItemSortConfigChange = (e) => {
		setItemSortConfig(e.target.value);
	};

	const handleChangeNameSet = (value:string[]) => {
		form.setFieldsValue({
			useSysOuterId: value.includes('useSysOuterId') ? 1 : 0,
		});
		setTitleSet(
			translateSetToString(getGoodsNameSetMap(), value)
		);
		handleChangeCustomSort(cloneDeep(titleSort), value.map(i => getGoodsNameSetMap().find(j => j.name === i)?.sortName));
		setTitleSort(getGoodsNameSetMap().filter(i => value.includes(i.name))?.map(i => i.sortName));
	};

	const handleChangeSkuSet = (value:string[]) => {
		form.setFieldsValue({
			useSysOuterSkuId: value.includes('useSysOuterSkuId') ? 1 : 0,
		});
		setSkuSet(
			translateSetToString(getSkuSetMap(), value)
		);
		handleChangeCustomSort(cloneDeep(skuSort), value.map(i => getSkuSetMap().find(j => j.name === i)?.sortName));
		setSkuSort(getSkuSetMap().filter(i => value.includes(i.name))?.map(i => i.sortName));
	};

	const handleChangeMarketStallSet = (value:string[]) => {
		handleChangeCustomSort(cloneDeep(marketStallSet), value.map(i => getMarketStallSetMap().find(j => j.key === i)?.sortName));
		setMarketStallSet(value);
	};

	const handleChangeCustomSort = (oldValue, newValue) => {
		console.log(oldValue, newValue);
		const sortMap = [...getGoodsNameSetMap(), ...getSkuSetMap(), ...getMarketStallSetMap(), ...getOtherSetMap()];
		if (oldValue?.length > newValue?.length) {
			const deleteSort = sortMap.find(i => i.sortName === oldValue.find(i => !newValue.includes(i)));
			setCustomSort(customSort.filter(item => item != deleteSort.sortName));
		} else {
			const addSort = sortMap.find(i => i.sortName === newValue.find(i => !oldValue.includes(i)));
			setCustomSort([...customSort, addSort.sortName]);
		}
	};

	const handleSortGoodName = (key:string, sortType:string, sortName:string) => {
		let newSort = sortName === 'titleSort' ? titleSort.concat() : skuSort.concat();

		const index = newSort.findIndex(i => i === key);
		if (sortType === 'forWard') {
			if (index === 0) {
				return;
			}
			swapArr(newSort, index, index - 1);
		} else {
			if (index === newSort.length) {
				return;
			}
			swapArr(newSort, index, index + 1);
		}
		sortName === 'titleSort' ? setTitleSort(newSort) : setSkuSort(newSort);
	};

	const handleSortCustomSort = (key:string, sortType:string) => {
		let newSort = customSort.concat();

		const index = newSort.findIndex(i => i === key);
		if (sortType === 'forWard') {
			if (index === 0) {
				return;
			}
			swapArr(newSort, index, index - 1);
		} else {
			if (index === newSort.length) {
				return;
			}
			swapArr(newSort, index, index + 1);
		}
		setCustomSort(newSort);
	};

	const handleChangeBreakPage = useCallback((e:RadioChangeEvent) => {
		if (e.target.value == PrintSetBreakPage.分页) {
			form.setFieldsValue({
				'changeLineConfig': PrintSetXBreakLine.自动换行,
				'breakline': PrintSetBreakLine.换行
			});
			setIsShowAutoLine(true);
		}
		setPartPrintSet({
			breakpage: e.target.value
		});
	}, [form, setPartPrintSet]);
	const openBreakPageInfinite = useCallback((e) => {
		form.setFieldsValue({
			'breakPageInfinite': e.target.checked ? 1 : 0
		});
		setPartPrintSet({
			breakPageInfinite: e.target.checked ? 1 : 0
		});
	}, [form, setPartPrintSet]);
	const onValuesChange = (changedValues) => {
		if ('breakline' in changedValues) {
			setIsShowAutoLine(changedValues.breakline != PrintSetBreakLine.不换行);
		}
		if ('showMarketStall' in changedValues) {
			setIsShowSkuFrontBack(changedValues.showMarketStall?.length);
		}

		// if ('showCombinationIcon' in changedValues) {
		// 	setCombinationSet(changedValues.showCombinationIcon);
		// }
	};

	return (
		<Modal
			centered
			width={ 800 }
			visible={ visible }
			title="发货内容打印样式设置"
			confirmLoading={ loading }
			onOk={ handleSavePrintStyle }
			onCancel={ onCloseModal }
			zIndex={ 1010 }
			maskClosable={ false }
			bodyStyle={ {
				padding: '10px 30px 30px 10px',
				fontSize: '12px',
				minHeight: '600px',
				overflow: 'auto',
				maxHeight: 'calc(100vh - 180px)'
			} }
		>
			<div>
				<Alert className="r-mb-16" message="发货内容一般由【商品、规格、数量】组成，可根据设置进行自定义设置" type="warning" />
				<Form
					form={ form }
					className={ s.printStyleSet }
					size="small"
					onValuesChange={ onValuesChange }
					labelCol={ {
						style: { width: 100 }
					} }
				>
					<Form.Item label="商品排序" name="itemSortConfig">
						<Radio.Group onChange={ handleItemSortConfigChange }>
							{ItemSortConfig.map(item => (
								<Radio key={ item.value } value={ item.value }>{item.label}</Radio>
							))}
							<Form.Item noStyle name="itemSortRule" >
								<Select
									getPopupContainer={ (e) => e.parentElement }
									disabled={ itemSortConfig !== ItemSortConfigEnum.自定义排序规则 }
									style={ { width: '200px' } }
									size="small"
								>
									{(isShowZeroStockVersion ? ItemSortRule.filter(i => i.isZeroStockVersion) : ItemSortRule.filter(i => i.isStockVersion)).map(item => (
										<Option value={ item.value } key={ item.value }>
											{item.label}
										</Option>
									))}
								</Select>
							</Form.Item>
						</Radio.Group>
					</Form.Item>
					<Form.Item label="商品" name="titleSet">
						<Checkbox.Group onChange={ handleChangeNameSet }>
							<Space size={ 90 }>
								{
									getGoodsNameSetMap().map(item => (
										<Checkbox value={ item.name } key={ item.name }>{item.label}</Checkbox>
									))
								}
							</Space>
						</Checkbox.Group>
					</Form.Item>
					<Form.Item
						wrapperCol={ {
							style: { marginLeft: 100 }
						} }
					>
						<div className={ s.setDescription }>
							{userStore.userInfo?.version === stockVersion.库存版 ? renderStockVersionTitleSetComp(titleSet) : renderNoStockVersionTitleSetComp(titleSet)}
						</div>

						{!showCustomSort && titleSort?.length > 1 ? (
							<div className={ s.sortWrapper }>
								显示顺序：
								<div className={ s.seqGroup }>
									{ titleSort?.map(item => (
										<span className={ s.sequenceBlock } key={ item } >
											<span className="r-pointer" onClick={ () => { handleSortGoodName(item, 'forWard', 'titleSort'); } }>&lt;</span>
											<span>
												&nbsp;&nbsp;
												{ getGoodsNameSetMap().find(i => i.sortName === item)?.sortLabel }
												&nbsp;&nbsp;
											</span>
											<span className="r-pointer" onClick={ () => { handleSortGoodName(item, 'backWard', 'titleSort'); } }>&gt;</span>
										</span>
									)) }
								</div>
							</div>
						) : ''}
					</Form.Item>

					<Form.Item label="规格" name="skuSet">
						<Checkbox.Group onChange={ handleChangeSkuSet }>
							<Space size={ [50, 8] } wrap>
								{
									getSkuSetMap().map(item => (
										<Checkbox value={ item.name } key={ item.name }>{item.label}</Checkbox>
									))
								}
							</Space>
						</Checkbox.Group>
					</Form.Item>
					<Form.Item wrapperCol={ {
						style: { marginLeft: 100 }
					} }
					>
						<div className={ s.setDescription }>
							{userStore.userInfo?.version === stockVersion.库存版 ? renderStockVersionSkuSetComp(skuSet) : renderNoStockVersionSkuSetComp(skuSet)}
						</div>

						{!showCustomSort && skuSort?.length > 1 ? (
							<div className={ s.sortWrapper }>
								<div style={ { minWidth: "5em" } }>显示顺序：</div>
								<div className={ s.seqGroup }>
									{ skuSort?.map(item => {
										if (!getSkuSetMap().find(i => i.sortName === item)?.sortLabel) return null;
										return (
											<span className={ s.sequenceBlock } key={ item }>
												<span className="r-pointer" onClick={ () => { handleSortGoodName(item, 'forWard', 'skuSort'); } }>&lt;</span>
												<span>
													&nbsp;&nbsp;
													{getSkuSetMap().find(i => i.sortName === item)?.sortLabel }
													&nbsp;&nbsp;
												</span>
												<span className="r-pointer" onClick={ () => { handleSortGoodName(item, 'backWard', 'skuSort'); } }>&gt;</span>
											</span>
										);
									}) }
								</div>
							</div>
						) : ''}
					</Form.Item>
					<Form.Item
						label="市场档口"
						name="showMarketStall"
					>
						<Checkbox.Group onChange={ handleChangeMarketStallSet }>
							<Space size={ 90 }>
								{
									getMarketStallSetMap().map(item => (
										<Checkbox value={ item.key } key={ item.key }>{item.label}</Checkbox>
									))
								}
							</Space>
						</Checkbox.Group>
					</Form.Item>
					{
						!showCustomSort && isShowSkuFrontBack ? (
							<Form.Item
								wrapperCol={ { style: { marginLeft: 100 } } }
							>
								<div className={ s.setDescription }>
									<Form.Item noStyle name="skuSetFrontBack">
										<Radio.Group defaultValue={ SkuSetFrontBack.显示在规格前 } onChange={ (e) => { setSkuSetFrontBack(e.target.value); } } >
											<Radio value={ SkuSetFrontBack.显示在规格前 }>
												显示在规格前
											</Radio>
											<Radio value={ SkuSetFrontBack.显示在规格后 }>
												显示在规格后
											</Radio>
										</Radio.Group>
									</Form.Item>
								</div>
							</Form.Item>
						) : null
					}
					<Form.Item
						label="数量"
						name="showNumFlag"
					>
						<Checkbox defaultChecked value disabled>数量</Checkbox>
					</Form.Item>
					<Form.Item label="显示样式" name="showStyle">
						<Radio.Group>
							<Space size={ 20 }>
								<div>
									<Radio value={ 1 } style={ { marginRight: '-3px' } }>
										商品、规格各数据项使用
									</Radio>
									<Form.Item noStyle name="separatorSign" >
										<Select getPopupContainer={ (e) => e.parentElement } style={ { width: '80px' } } onChange={ (e) => { sendPoint(Pointer.订单_订单打印_打印内容_展示样式_分隔); setShowSeparatorSign(e === "userCustomize"); } } size="small">
											{separatorSign.map(item => (
												<Option value={ item.value } key={ item.value }>
													{item.name}
												</Option>
											))}
										</Select>
									</Form.Item>
									{
										showSeparatorSign && (
											<Form.Item noStyle name="separatorSignCustomize">
												<Input maxLength={ 10 } style={ { width: "100px", verticalAlign: "top", marginLeft: "5px" } } />
											</Form.Item>
										)
									}
									<span style={ { fontSize: '14px', marginLeft: '3px' } }>隔开</span>
								</div>
								<Radio data-point={ Pointer.订单_订单打印_打印内容_展示样式_不分隔 } value={ 0 }>
									不分隔
								</Radio>
							</Space>
						</Radio.Group>
					</Form.Item>

					<Form.Item label="显示方式">
						<Form.Item name="showType">
							<Radio.Group onChange={ (e) => { setShowCustomSort(e.target.value === 2); } }>
								<Space size={ 20 }>
									<Radio value={ 1 } style={ { marginRight: '-3px' } }>
										按默认顺序展示(商品-规格-市场-数量）
									</Radio>
									<Radio value={ 2 }>
										按自定义顺序展示
									</Radio>
								</Space>
							</Radio.Group>
						</Form.Item>
						{
							showCustomSort && customSort?.length > 0 && (
								<Form.Item>
									<div className={ s.sortWrapper } style={ { paddingLeft: 8 } }>
										<div className={ s.seqGroup }>
											{ customSort?.map(item => (
												<span className={ s.sequenceBlock } key={ item } >
													<span className="r-pointer" onClick={ () => { handleSortCustomSort(item, 'forWard'); } }>&lt;</span>
													<span>
														&nbsp;&nbsp;
														{ [...getGoodsNameSetMap(), ...getSkuSetMap(), ...getMarketStallSetMap(), ...getOtherSetMap()].find(i => i.sortName === item)?.sortLabel }
														&nbsp;&nbsp;
													</span>
													<span className="r-pointer" onClick={ () => { handleSortCustomSort(item, 'backWard'); } }>&gt;</span>
												</span>
											)) }
										</div>
									</div>
								</Form.Item>
							)
						}
					</Form.Item>

					<Form.Item label="显示序号" name="displayPrintOrderNum">
						<Checkbox.Group>
							<Checkbox value="displayPrintOrderNum" >
								每条发货内容前展示序号
							</Checkbox>
						</Checkbox.Group>
					</Form.Item>

					<Form.Item label="数量样式" name="goodsNumDisplayType">
						<Radio.Group>
							<Radio value={ 9999 }>
								<span>使用符号</span>
								<Form.Item noStyle name="goodsNumDisplay">
									<Select
										className="r-ml-4 r-mr-4"
										getPopupContainer={ (e) => e.parentElement } 
										style={ { width: '80px' } }
										onClick={ e => e.preventDefault() }
									>
										{
											goodsNumDisplayMap.map(item => (
												<Option value={ item.value } key={ item.value }>{item.label}</Option>
											))
										}
									</Select>
								</Form.Item>
								<span>分隔显示</span>
							</Radio>
							<Radio value={ 1 }>直接显示</Radio>
						</Radio.Group>
					</Form.Item>

					<Form.Item label="数量单位" name="unitStyleType">
						<Radio.Group>
							<Radio value={ 9999 } >
								<span>使用单位</span>
								<Form.Item noStyle name="unitStyle">
									<Select 
										className="r-ml-4"
										getPopupContainer={ (e) => e.parentElement } 
										style={ { width: '80px' } } 
										onChange={ (e) => { setShowCustomUnit(e === 9999); } } 
										onClick={ e => e.preventDefault() }
										size="small"
									>
										{unitStyle.map(item => (
											<Option value={ item.value } key={ item.value }>
												{item.label}
											</Option>
										))}
									</Select>
								</Form.Item>
								{showCustomUnit && (
									<Form.Item noStyle name="unitStyleCustomize">
										<Input 
											maxLength={ 10 } 
											style={ { width: "100px", verticalAlign: "top", marginLeft: "5px" } } 
										/>
									</Form.Item>
								)}
							</Radio>
							<Radio value={ 0 }>无单位</Radio>
						</Radio.Group>
					</Form.Item>
					{
						userStore.isStockVersion ? (
							<>
								<Form.Item label="组合货品打印" name="showCombination">
									<Radio.Group>
										<Radio value={ CombinationShowEnum["显示(组)+组合货品信息"] }><span className={ s.label }>显示(组)+组合货品信息</span></Radio>
										<Radio value={ CombinationShowEnum.仅显示组合货品信息 }><span className={ s.label }>仅显示组合货品信息</span></Radio>
										<Radio value={ CombinationShowEnum.不显示 }><span className={ s.label }>不显示</span></Radio>
									</Radio.Group>
								</Form.Item>
								{/* {combinationSet ? (
									<Form.Item
										wrapperCol={ {
											style: { marginLeft: 100 }
										} }
									>
										<div className={ s['setDescription'] }>打印时展示【组】+ 组合货品信息</div>
									</Form.Item>
								) : ''} */}
								<Form.Item
									label={ (
										<>
											<Tooltip title="如该商品为组合货品，您可在高级设置 - 开启「打印组合货品显示子货品」，开启后将会显示子货品">
												<InfoCircleOutlined className="r-mr-2" />
											</Tooltip>
											子货品打印
										</>
									) }
								>
									<>
										<Form.Item>
											<Radio.Group value={ showCombinationInfoType } onChange={ (e) => { setShowCombinationInfoType(e.target.value); } }>
												<Radio value={ 1 }>自定义显示<Tooltip title="按自定义信息显示时，子商品不参与合并"><InfoCircleOutlined className="r-ml-4" /></Tooltip></Radio>
												<Radio value={ 2 }>按商品规格设置字段显示<Tooltip title="此设置下不显示组合货品信息，不显示【组】【子】标识，子货品支持与其他相同货品合并"><InfoCircleOutlined className="r-ml-4" /></Tooltip></Radio>
											</Radio.Group>
										</Form.Item>
										{
											showCombinationInfoType === 1 ? (
												<Form.Item label="子货品显示" style={ { marginBottom: 0, padding: "10px", background: "#f5f5f5" } }>
													<Checkbox.Group value={ childGoodsShowList } onChange={ (e) => { setChildGoodsShowList(e); } }>
														<Space size={ 60 }>
															{
																getChildSetMap().map(item => (
																	<Checkbox value={ item.key } key={ item.key }>{item.label}</Checkbox>
																))
															}
														</Space>
													</Checkbox.Group>
												</Form.Item>
											) : (
												<Form.Item label="子货品显示" style={ { marginBottom: 0, padding: "10px", background: "#f5f5f5" } }>
													<div className="r-flex" style={ { marginTop: "-4px", flexWrap: "wrap", gap: "4px" } }>
														{ titleSort?.map(item => (
															<span className="r-mr-5" key={ item } style={ { padding: "4px", background: "#e5e5e5" } }>
																{ getGoodsNameSetMap().find(i => i.sortName === item)?.sortLabel }
															</span>
														)) }
														{
															isShowSkuFrontBack && skuSetFrontBack === SkuSetFrontBack.显示在规格前 ? (
																marketStallSet?.map(item => (
																	<span className="r-mr-5" key={ item } style={ { padding: "4px", background: "#e5e5e5" } }>
																		{getMarketStallSetMap().find(i => i.key === item)?.label }
																	</span>
																))
															) : null
														}
														{ skuSort?.map(item => (
															<span className="r-mr-5" key={ item } style={ { padding: "4px", background: "#e5e5e5" } }>
																{getSkuSetMap().find(i => i.sortName === item)?.sortLabel }
															</span>
														)) }
														{
															isShowSkuFrontBack && skuSetFrontBack === SkuSetFrontBack.显示在规格后 ? (
																marketStallSet?.map(item => (
																	<span className="r-ml-5 r-mr" key={ item } style={ { padding: "4px", background: "#e5e5e5" } }>
																		{getMarketStallSetMap().find(i => i.key === item)?.label }
																	</span>
																))
															) : null
														}
													</div>
												</Form.Item>
											)
										}
									</>
								</Form.Item>
							</>

						) : ''
					}

					<Form.Item label="合并商品" name="isTogether">
						<Checkbox.Group onChange={ (val: number[]) => { setPartPrintSet({ isTogether: val }); } }>
							<Space size={ 20 }>
								{
									isTogetherMap.map(item => (
										<Checkbox
											data-point={ item.point }
											value={ item.value }
											key={ item.value }
											disabled={ partPrintSet.isTogether?.length && !partPrintSet.isTogether.includes(item.value) }
										>{item.name}
										</Checkbox>
									))
								}
							</Space>
						</Checkbox.Group>
					</Form.Item>

					{/* <Form.Item label="宝贝单价" name="showOrderPrice">
						<Radio.Group>
							<Radio value={ 1 }><span className={ s.label }>显示</span></Radio>
							<Radio value={ 0 }><span className={ s.label }>不显示</span></Radio>
						</Radio.Group>
					</Form.Item> */}

					<Form.Item label="分页显示" name="breakpage">
						<Radio.Group onChange={ handleChangeBreakPage }>
							<Radio data-point={ Pointer.订单_订单打印_打印内容_分页显示_不分页 } value={ PrintSetBreakPage.不分页 }><span className={ s.label }>不分页</span></Radio>
							<Radio value={ PrintSetBreakPage.分页 }>
								宝贝大于
								<Form.Item noStyle name="breakpageNum">
									<Input type="number" style={ { width: '50px' } } />
								</Form.Item>
								个时,宝贝信息打印在第二张面单上(默认换行)
							</Radio>
						</Radio.Group>
					</Form.Item>
					{partPrintSet.breakpage == PrintSetBreakPage.分页 && (
						<Form.Item
							name="breakPageInfinite"
							wrapperCol={ {
								style: { marginLeft: 190 }
							} }
						>
							<Checkbox onChange={ openBreakPageInfinite } defaultChecked={ partPrintSet?.breakPageInfinite === 1 } >
								内容超过第二页时自动分页，直至打印完全（仅支持菜鸟、抖音控件）
							</Checkbox>
						</Form.Item>
					)}
					<Form.Item label="换行显示" name="breakline">
						<Radio.Group>
							<Radio data-point={ Pointer.订单_订单打印_打印内容_换行显示_换行 } value={ PrintSetBreakLine.换行 }>
								<span className={ s.label }>
									换行
								</span>
							</Radio>
							<Radio data-point={ Pointer.订单_订单打印_打印内容_换行显示_不换行 } value={ PrintSetBreakLine.不换行 }>
								<span className={ s.label }>
									不换行
								</span>
							</Radio>
							<Radio value={ PrintSetBreakLine.条件换行 }>
								宝贝少于
								<Form.Item noStyle name="breaklineNum">
									<Input type="number" style={ { width: '50px' } } />
								</Form.Item>
								个时换行,否则不换行
							</Radio>
						</Radio.Group>
					</Form.Item>

					{
						!isShowAutoLine ? '' : (
							<Form.Item
								wrapperCol={ { style: { marginLeft: 100 } } }
							>
								<div className={ s.setDescription }>
									<Form.Item noStyle name="changeLineConfig">
										<Radio.Group defaultValue={ PrintSetXBreakLine.自动换行 } >
											<Radio value={ PrintSetXBreakLine.自动换行 }>
												<span >
													自动换行
												</span>
											</Radio>
											<Radio value={ PrintSetXBreakLine.条件换行 }>
												每
												<Form.Item noStyle name="changeLineGoodsNum">
													<Input type="number" style={ { width: '50px' } } />
												</Form.Item>
												个商品自动换行
											</Radio>
										</Radio.Group>
									</Form.Item>
								</div>
							</Form.Item>
						)
					}

					<div className="r-flex">
						<Form.Item label="赠品打印" name="printGiftOrder">
							<Radio.Group>
								{
									printGiftOrder.map((item) => (
										<Radio value={ item.value } key={ item.label + item.value }><span className={ s.label }>{item.label}</span></Radio>
									))
								}
							</Radio.Group>
						</Form.Item>
						<div className="r-c-error" style={ { lineHeight: "24px" } }>注：仅含赠品的拆分订单发货内容默认打印赠品</div>
					</div>
				</Form>
			</div>
		</Modal>
	);
});

export default PrintStyleSetting;
