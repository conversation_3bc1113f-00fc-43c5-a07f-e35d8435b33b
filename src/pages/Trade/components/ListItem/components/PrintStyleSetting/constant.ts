import { getVersionConstants } from "@/constants/versionUtils";
import Pointer from "@/utils/pointTrack/constants";
import { stockVersion } from "@/constants";
import userStore from "@/stores/user";

export const getChildSetMap = () => {
	const map = [{
		key: '1',
		name: "skuOuterId",
		label: "货品规格编码",
	}, {
		key: '2',
		name: "sysSkuName",
		label: "货品规格名称",
	}, {
		key: '3',
		name: "sysSkuAlias",
		label: "货品规格别名",
	}];
	return map;
};

export const enum MarketStallSupplierEnum {
	市场 = '1',
	档口 = '2',
	供应商 = '3'
}

export const enum CombinationShowEnum {
	'显示(组)+组合货品信息',
	'仅显示组合货品信息',
	'不显示'
}

export const getMarketStallSetMap = () => {
	const map = [{
		key: MarketStallSupplierEnum.市场,
		name: "market",
		sortName: MarketStallSupplierEnum.市场,
		sortLabel: '市场',
		label: "市场",
	}, {
		key: MarketStallSupplierEnum.档口,
		name: "stall",
		sortName: MarketStallSupplierEnum.档口,
		sortLabel: '档口',
		label: "档口",
	}, {
		key: MarketStallSupplierEnum.供应商,
		name: "supplier",
		sortName: MarketStallSupplierEnum.供应商,
		sortLabel: '供应商',
		label: "供应商",
	}];
	return map;
};

export const getOtherSetMap = () => {
	const map = [{
		name: "useItemNum",
		sortName: "itemNum",
		sortLabel: '数量',
		label: "数量",
	}];
	return map;
};

export const getGoodsNameSetMap = () => {
	let titleShortName = getVersionConstants('简称');
	let map = [
		{
			name: "useOuterIid",
			sortName: 'outerIid',
			sortLabel: '商家编码',
			label: "商家编码",
		},
		{
			name: "useShortName",
			sortName: 'titleShort',
			sortLabel: titleShortName,
			label: titleShortName,
		},
		{
			name: "useTitle",
			sortName: 'title',
			sortLabel: '商品名称',
			label: "商品名称",
		}
	];

	if (userStore?.userInfo?.version === stockVersion.库存版) {
		map.splice(1, 0, {
			name: "useSysOuterId",
			sortName: 'sysOuterId',
			sortLabel: '货品编码',
			label: "货品编码",
		});
	}
	return map;
};

export const getSkuSetMap = () => {
	let map = [{
		name: "useRuleOutIid",
		sortName: 'outerSkuId',
		sortLabel: '规格编码',
		label: "规格对应的商家编码",
	}, {
		name: "useSkuName",
		sortName: 'skuPropertiesName',
		sortLabel: '规格名称',
		label: "规格名称",
	}, {
		name: "showSkuAlias",
		sortName: 'skuAlias',
		sortLabel: '规格别名',
		label: "规格别名",
	}];

	if (userStore?.userInfo?.version === stockVersion.库存版) {
		map.splice(1, 0, {
			name: "useSysOuterSkuId",
			sortName: 'sysOuterSkuId',
			sortLabel: '货品规格编码',
			label: "货品规格编码",
		});
		map[0].label = '规格编码';
		map[0].sortLabel = '规格编码';

		map.push({
			name: "useItemNo",
			sortName: 'itemNo',
			sortLabel: '货号',
			label: "货号",
		});
		map.push({
			name: "useWarehouseSlotName",
			sortName: 'warehouseSlotName',
			sortLabel: '货位',
			label: "货位",
		});
	}

	map.push({
		name: "showOrderPrice",
		sortName: 'orderPrice',
		sortLabel: '单价',
		label: "单价",
	});

	return map;
};

export const separatorSign = [
	{
		value: ',',
		name: ','
	}, {
		value: '/',
		name: '/'
	}, {
		value: '|',
		name: '|'
	}, {
		value: '*',
		name: '*'
	}, {
		value: '_',
		name: '_'
	}, {
		value: ' ',
		name: '空格'
	}, {
		value: 'userCustomize',
		name: '自定义'
	}
];

export const isTogetherMap = [
	{
		value: 3,
		name: '同款商品同规格合并打印',
		// point: Pointer.订单_订单打印_打印内容_合并宝贝_同款宝贝合并打印
	},
	{
		value: 1,
		name: '同款商品不同规格合并打印',
		point: Pointer.订单_订单打印_打印内容_合并宝贝_同款宝贝合并打印
	},
	{
		value: 2,
		name: '同款商品不合并规格',
		point: Pointer.订单_订单打印_打印内容_合并宝贝_同规格商品不合并打印
	}
];


export const goodsNumDisplayMap = [
	{
		value: 3,
		label: '[]',
	},
	{
		value: 6,
		label: '【】',
	},
	{
		value: 2,
		label: '-',
	},
	{
		value: 4,
		label: '()',
	},
	{
		value: 5,
		label: '/',
	},
	{
		value: 8,
		label: '*',
	},
	{
		value: 7,
		label: '空格',
	},
	// {
	// 	value: 1,
	// 	label: '直接显示',
		// }
];

export const printGiftOrder = [
	{
		value: true,
		label: '默认打印赠品'
	}, {
		value: false,
		label: '默认不打印赠品'
	}
];

export const unitStyle = [
	{
		value: 1,
		label: '件',
	},
	{
		value: 2,
		label: '个',
	},
	{
		value: 3,
		label: '包',
	},
	{
		value: 4,
		label: '台',
	},
	{
		value: 5,
		label: '份',
	},
	{
		value: 6,
		label: '只',
	},
	{
		value: 7,
		label: '条',
	},
	{
		value: 8,
		label: '盒',
	},
	{
		value: 9,
		label: '双',
	},
	{
		value: 9999,
		label: '自定义',
	},
	// {
	// 	value: 0,
	// 	label: '无单位',
	// }
];

export const getUseOuterIidEmptyMap = () => {
	let titleShortName = getVersionConstants('简称');
	return [
		{
			value: 0,
			label: `没有时显示${titleShortName}或标题`,
		},
		{
			value: 2,
			label: '没有时显示宝贝ID',
		},
		{
			value: 1,
			label: '没有时不显示内容',
		}
	];
};

export const getUseTitleEmpty = () => {
	let titleShortName = getVersionConstants('简称');
	return [
		{
			value: 0,
			label: `同时打印商家编码和${titleShortName}，没有则打印标题`
		},
		{
			value: 3,
			label: '都没有时不显示内容'
		}
	];
};

export const getUseShortNameEmptyMap = () => {
	let titleShortName = getVersionConstants('简称');
	return [
		{
			value: 0,
			label: `无${titleShortName}时显示标题`,
		},
		{
			value: 2,
			label: `无${titleShortName}时显示商家编码`,
		},
		{
			value: 1,
			label: `无${titleShortName}时不显示内容`,
		}
	];
};

export const useRuleOutIidEmptyMap = [
	{
		value: 0,
		label: '无规格商家编码显示规格名称',
	},
	{
		value: 1,
		label: '无规格商家编码不显示内容',
	}
];

export const showSkuAliasEmptyMap = [
	{
		value: 0,
		label: '无规格别名显示规格名称',
	},
	{
		value: 1,
		label: '无规格别名不显示内容',
	}
];


export enum PrintSetBreakPage {
	'不分页' = -1,
	'分页' = 2,
}

export enum PrintSetBreakLine {
	'换行'= 1,
	'不换行'= -1,
	'条件换行'=2,
}

export enum PrintSetXBreakLine{
	自动换行,
	条件换行
}

export enum SkuSetFrontBack{
	显示在规格前,
	显示在规格后
}

export const useSysOuterIdEmptyMap = [
	{
		value: 1,
		label: '没有时显示商家编码',
	},
	{
		value: 2,
		label: '没有时显示简称或标题',
	},
	{
		value: 3,
		label: '没有时不显示内容',
	}
];

export const useSysOuterSkuIdEmptyMap = [
	{
		value: 1,
		label: '无货品规格编码显示规格名称',
	},
	{
		value: 2,
		label: '无货品规格编码不显示内容',
	}
];

export const ItemSortConfig = [{
	label: '订单商品默认顺序排序',
	value: 0,
}, {
	label: '自定义排序规则',
	value: 1,
}];

export const enum ItemSortConfigEnum {
	订单商品默认顺序排序 = 0,
	自定义排序规则 = 1
}

export const enum ItemSortRuleEnum {
	"按商家编码+规格编码升序" = '1',
	"按规格编码升序" = '2',
	"按规格名称升序" = '3',
	"按货品规格编码升序" = '4',
	"按货品简称升序" = '5',
	"按货品规格名称升序" = '6',
	"按货品规格别名升序" = '7',
	"按规格别名升序" = '8',
	"按简称+规格别名升序" = '9',
}

export const ItemSortRule = [{
	label: '按商家编码+规格编码升序',
	value: ItemSortRuleEnum['按商家编码+规格编码升序'],
	isStockVersion: true,
	isZeroStockVersion: true,
}, {
	label: '按规格编码升序',
	value: ItemSortRuleEnum['按规格编码升序'],
	isStockVersion: true,
	isZeroStockVersion: true,
}, {
	label: '按规格名称升序',
	value: ItemSortRuleEnum['按规格名称升序'],
	isStockVersion: true,
	isZeroStockVersion: true,
}, {
	label: '按规格别名升序',
	value: ItemSortRuleEnum['按规格别名升序'],
	isStockVersion: false,
	isZeroStockVersion: true,
}, {
	label: '按货品规格编码升序',
	value: ItemSortRuleEnum['按货品规格编码升序'],
	isStockVersion: true,
	isZeroStockVersion: false,
}, {
	label: '按货品简称升序',
	value: ItemSortRuleEnum['按货品简称升序'],
	isStockVersion: true,
	isZeroStockVersion: false,
}, {
	label: '按货品规格名称升序',
	value: ItemSortRuleEnum['按货品规格名称升序'],
	isStockVersion: true,
	isZeroStockVersion: false,
}, {
	label: '按货品规格别名升序',
	value: ItemSortRuleEnum['按货品规格别名升序'],
	isStockVersion: true,
	isZeroStockVersion: false,
}, {
	label: '按简称+规格别名升序',
	value: ItemSortRuleEnum['按简称+规格别名升序'],
	isStockVersion: true,
	isZeroStockVersion: false,
}];

export enum isTogetherEnum {
	同款商品同规格合并打印 = 3,
	同款商品不同规格合并打印 = 1,
	不合并打印 = 2,
}
