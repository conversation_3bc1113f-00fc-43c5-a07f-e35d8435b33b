/* eslint-disable react-hooks/rules-of-hooks */
import React, { Fragment, memo, useEffect, useState } from 'react';
import { Button, Dropdown } from 'antd';
import { observer } from 'mobx-react';
import NiceModal from '@ebay/nice-modal-react';
import { calculateCancelOrder, copyMenu, getMemoSysFlag, getNickPlatformIcon, getTradePlatformLabel, openWW } from '@/pages/Trade/utils';
import { copyToPaste } from '@/utils';
import { ManualMergeTradeHelper, manualMergeTradeHelper } from '../../ManualMergeTrade/helper';
import { IPackage } from '@/pages/Trade/interface';
import Pointer from '@/utils/pointTrack/constants';
import { tradeStore } from '@/stores';
import BuyerNickComp from '@/components-biz/BuyerNickComp';
import { CancelSplit } from '../../SplitOrder/modal';
import { PLAT_PDD } from '@/constants';
import Flex from '@/components/Flex';

export interface IBuyerNickProps {
	pack: IPackage;
	colWidth: string | number;
}

const BuyerNick = (props: IBuyerNickProps) => {
	const { pack, colWidth } = props;
	const { importExpress, storeSearchParams } = tradeStore;
	const renderRemindHourCom = () => {
		let result:any = '';
		let remindHour = +pack.sendRemindHour;
		const afterText = pack.platform == PLAT_PDD ? '将超时揽收' : '';
		if (remindHour >= 24) {
			const d = Math.floor(remindHour / 24);
			const h = (remindHour % 24).toFixed(1);
			result = <div style={ { color: '#00B77F' } }>剩余{d}天{h}小时 {afterText}</div>;
		} else if (remindHour > 0 && remindHour < 24) {
			result = <div style={ { color: '#FB9D2C ' } }>剩余{remindHour}小时 {afterText}</div>;
		} else if (remindHour < 0 && remindHour > -24) {
			result = <div style={ { color: 'rgba(255,0,0)' } }>已超{(remindHour + '').split('-')[1]}小时</div>;
		} else if (remindHour <= -24) {
			const d = Math.floor(Math.abs(remindHour) / 24);
			const h = (Math.abs(remindHour) % 24).toFixed(1);
			result = <div style={ { color: 'rgba(255,0,0)' } }>已超{d}天{h}小时</div>;
		} else if (remindHour === 0 && pack.sendRemindHour === '0.0') {
			result = <div style={ { color: '#FB9D2C ' } }>剩余{remindHour}小时 {afterText}</div>;
		} else if (remindHour === 0 && pack.sendRemindHour === '-0.0') {
			result = <div style={ { color: 'rgba(255,0,0)' } }>已超{remindHour}小时</div>;
		}
		return result;
	};
	const copyInfo = () => {
		copyToPaste(pack.buyerNick);
	};

	const showMergeModal = () => {
		// 重新手动更新合单信息
		const list = tradeStore.tradeListStore.list;

		const newMergeTrade = pack[ManualMergeTradeHelper.rowValue].map((t: IPackage) => {
			return list.filter(l => l.togetherId === t.togetherId)[0];
		});
		manualMergeTradeHelper.showMergeModal(newMergeTrade);
	};

	const modal = NiceModal.useModal(CancelSplit);
	useEffect(() => {
		if (pack && modal.visible) {
			modal.show({ pack });
		}
	}, [pack]);
	const isMergeMark = pack.isMerge && pack.mergeTradeCount && pack.allowSubOrderSplit !== false;
	return (
		<div>
			<div className="batch_tbtlt_username " style={ { width: colWidth } } onClick={ (e) => { e.stopPropagation(); } }>
				{/* {getTradePlatformLabel(pack.platform)} */}
				{getNickPlatformIcon({ platform: pack.platform, source: pack.source })}
				<div className="r-fd-c r-flex r-lh-18" >
					<div className="r-flex r-ai-c">
						<Dropdown placement="topLeft" overlay={ copyMenu(copyInfo) }>
							<div>
								<BuyerNickComp
									togetherId={ pack.togetherId }
									source={ pack.source }
									orderId={ pack?.trades?.[0]?.orders?.[0]?.ptOid }
									realTid={ pack?.trades?.[0]?.realTid || '' }
									ptTid={ pack?.trades?.[0]?.ptTid || '' }
									tid={ pack?.trades?.[0]?.tid || '' }
									encryptuid={ pack.buyerOpenUid }
									platform={ pack.platform }
									buyerNick={ pack.buyerNick }
									sellerId={ pack?.sellerId }
									sellerName={ pack?.sellerNick }
								/>
							</div>
						</Dropdown>
						<span className="userset_icons customSysMemoCon r-ml-5 r-lh-18">
							{Object.entries(pack?.sellerFlagSys || {}).map((flag: any) => (
								<Fragment key={ flag }>
									{flag[1] !== 0 ? <span className={ `${getMemoSysFlag(flag[0], flag[1])}` } style={ { cursor: 'text' } } /> : ''}
								</Fragment>
							))}
						</span>
					</div>
					{ !isMergeMark && !(pack.waveNoList && pack.waveNoList.length > 0) && (pack.isSplit) && calculateCancelOrder(tradeStore.tradeListStore.list, pack)
				&& (
					<div
						onClick={ () => NiceModal.show(CancelSplit, { pack }) }
						className="r-pointer r-lh-18"
						data-point={ Pointer.取消拆分_点击 }
						style={ { color: '#1890FF' } }
					>
						取消拆单
					</div>
				)}
					{ (pack[ManualMergeTradeHelper.rowValue]?.length > 1 && !importExpress?.tradeExpressImportLogSequence)
				&& (
					<div
						onClick={ showMergeModal }
						className="r-pointer r-lh-18"
						data-point={ Pointer.订单_订单打印_订单操作_合并订单_合单_展现 }
						style={ { color: '#1890FF' } }
					>
						有{pack[ManualMergeTradeHelper.rowValue].length}单可合并
					</div>
				)}
					{ isMergeMark && (
						<div
							onClick={ () => { manualMergeTradeHelper.splitTrade(pack); } }
							className="r-pointer r-lh-18"
							style={ { color: '#1890FF' } }
							data-point={ Pointer.订单_订单打印_订单操作_拆分子订单 }
						>
							按子订单拆分
						</div>
					) }
					{/* pdd和抖店有 淘宝先不支持 */}
					{!pack.orderTagList.find(item => item.name === 'region_black_delay_shipping')?.value ? renderRemindHourCom() : ''}
				</div>
			</div>
			{
				// 待发货+先发货的查询选项的时候是在status === 'WAIT_SELLER_SEND_GOODS'的基础上添加了字段sysStatus来处理的
				storeSearchParams?.status === 'WAIT_SELLER_SEND_GOODS' && !storeSearchParams?.sysStatus && pack.status === 'WAIT_BUYER_CONFIRM_GOODS' && (
					<div className="r-flex r-mt-5">
						<div style={ { backgroundColor: '#FD8204', color: "#fff", padding: '4px 6px', borderRadius: '4px' } }>
							(已发货)
						</div>
					</div>
				)
			}
		</div>
	);
};

export default observer(BuyerNick);
