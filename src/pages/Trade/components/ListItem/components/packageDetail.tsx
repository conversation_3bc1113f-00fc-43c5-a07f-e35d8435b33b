import React, { useMemo, useState } from 'react';
import { Button, Input, Popover, Tooltip, Modal, Row, Col, Divider } from 'antd';
import { CopyOutlined, InfoCircleFilled } from '@ant-design/icons';
import { observer } from 'mobx-react';
import { runInAction } from 'mobx';
import { debounce, groupBy } from 'lodash';
import message from '@/components/message';
import {
	filterUdbUdc,
	getMemoSysFlag,
	getOrderRefundStatus,
	getTradeFlag,
	getTradeFlagTag,
	getTradePlatformLabel,
	getTradeStatusLabel,
	getVirtualImgUrl,
	handleDecrypt, limitedPackTips,
	consolidateContent,
	getPackageDetailOrders,
	filterAddrDetail,
	isOrderDisabled,
	getDecryptInfoForHand,
	getCostPrice,
	JdConsolidateType,
	getPayDisplayTypeText
} from '@/pages/Trade/utils';
import { IPackage, ICopySettingItem, ISubTrade } from '@/pages/Trade/interface';
import { copyToPaste, splitFxgTid } from '@/utils';
import Icon from '@/components/Icon';
import WaresInfo from '@/components-biz/WaresInfo';
import { PLAT_FXG, PLAT_TB, PLAT_TM, PLAT_ALI, PLAT_YZ, GIFT_NORMAL_PLATFORM, PLAT_KS, PLAT_SPH, AFTERSALE_HANDORDER, IS_ADDR_TOWN, PLAT_PDD, GIFT_SPECIAL_PLATFORM, PLAT_JD, PLAT_SCMHAND, PLAT_DW, PLAT_KTT } from '@/constants';
import userStore from '@/stores/user';
import { TradeInfoBlurApi, TradeModifyAddressApi, TradeOperateLogAddApi } from '@/apis/trade';
import AddrSelect from '@/components-biz/AddrSelect';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { tradeStore, editGoodsStore } from '@/stores';
import CopySettingModal from './copySettingModal';
import tradeSetStore from '@/stores/trade/tradeSet';
import { operate } from '@/pages/Trade/PreShip/components/utils';
import { TradeOptEnum, TradeStatus } from '@/utils/enum/trade';
import { DecryptFiledEnum } from './Decrypt';
import BuyerNickComp from "@/components-biz/BuyerNickComp";
import { SecretPhoneTip } from './SecretPhoneTip';
import { isCheckStock } from '../../BottomCom/utils';
import OrderStockInfoItem from './OrderStockInfoItem';
import { ConsolidateType, PRINT_CONTENT_MAX_LENGTH, PRINT_CONTENT_MAX_ROW, PAY_DISPLAY_TYPE_COLOR_MAP, PAY_DISPLAY_TYPE_ICON_MAP } from '@/pages/Trade/constants';
import ShowLogistic from '@/pages/Report/KddLog/components/ShowLogistic';
import { isSourceHand, isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import PackageDetailGoodsInfo from './packageDetailGoodsInfo';
import { SerialNumberCom, SysMemoPicFragment } from './simpleCom';
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';

export interface IPackageDetailProps {
    packInfo: Partial<IPackage>,
}

export interface IFlagObj {
    flagArr: {
        color: string,
        value: string,
    }[],
    isEdit: string;
    editContent: string;
    sellerMemoFlag: string;
    sellerMemoFlagName: string
}

const PackageDetail = (props: IPackageDetailProps) => {
	const { packInfo } = props;
	const {
		setIsShowChangeGoodsModal,
		setChangeGoodsData,
		setIsShowBatchAddGoodsModal,
		setBatchAddGoodsPackage
	} = editGoodsStore;
	const {
		tradeListStore: {
			setIsShowKeywordSetting,
			setIsShowPrintStyleSetting,
		},
		tradeOptStore: {
			packageDetailUpdateSysMemo,
			packageDetailUpdateMemo,
			handleOrderCheck,
			modifySysMemo,
			setPackageVal,
			modifyAddr,
			setTradeVal,
			handleProductOrders
		},
		selectedTemp
	} = tradeStore;
	const [isModalVisible, setIsModalVisible] = useState(false);

	const [modalItem, setModalItem] = useState([]);
	const [copySettingVisible, setCopySettingVisible] = useState(false);
	const [modifyAddrLoading, setModifyAddrLoading] = useState(false);
	const { copySetting } = tradeSetStore;

	const isShipHold = useMemo(() => {
		return packInfo.trades.some(trade => trade.shipHold);
	}, [packInfo.trades]);

	const copyInfo = (type: 'name' | 'all' | 'addr') => {
		if (isShipHold) {
			limitedPackTips();
			return;
		}
		const {
			receiverState, receiverCity, receiverDistrict, base64Url
		} = packInfo;

		let { name, phone, address } = getDecryptInfoForHand(packInfo);
		name = base64Url || name;
		let copyStr = '';
		let copyArr: ICopySettingItem[] = [];
		let orderDom;
		switch (type) {
			case 'name':
				sendPoint(Pointer.订单_订单打印_订单操作_收货信息_复制昵称);
				copyStr = name;
				break;

			case 'all':
				sendPoint(Pointer.订单_订单打印_订单操作_收货信息_复制全部);
				copyArr = copySetting.filter(item => item.isChecked);
				copyArr.forEach(item => {
					if (item.key === 'expressInfo') {
						if (packInfo.sids && packInfo.sids?.[0] !== '打印后生成' && selectedTemp?.ExcodeName) {
							copyStr += `${selectedTemp?.ExcodeName}：${packInfo.sids?.[0]},`;
						}
					} else if (item.key === 'messageAndMemo') {
						packInfo.trades.forEach(trade => {
							let str = `${trade.buyerMessage || ''} ${trade.sellerMemo || ''}`;
							if (str.trim()) {
								copyStr += `${str},`;
							}
						});
					} else if (item.key === 'productInfo') {
						packInfo.trades.forEach(trade => {
							trade.orders.forEach(order => {
								orderDom = document.querySelector(`#productItem-${order.oid}`);
								if (orderDom && orderDom.textContent) {
									copyStr += `${orderDom.textContent},`;
								}
							});
						});
					} else if (item.key === 'receiverAddress') {
						copyStr += `${receiverState} ${receiverCity} ${receiverDistrict} ${address},`;
					} else if (item.key === 'receiverNameMask') {
						copyStr += `${name},`;
					} else if (item.key === 'receiverMobileMask') {
						copyStr += `${phone},`;
					} else {
						let str = `${packInfo[item.key] || ''}`;
						if (str.trim()) {
							copyStr += `${str},`;
						}
					}
				});
				if (copyStr[copyStr.length - 1] === ',') {
					copyStr = copyStr.slice(0, copyStr.length - 1);
				}
				break;

			case 'addr':
				sendPoint(Pointer.订单_订单打印_订单操作_收货信息_复制地址);
				copyStr = `${receiverState} ${receiverCity} ${receiverDistrict} ${address}`;
				break;
			default:
				break;
		}
		if (copyStr) {
			copyToPaste(copyStr);
		}
	};

	const handleAddressValues = (add) => {
		console.log('handleAddressValues', add);
		setPackageVal(packInfo, ['_receiverState', '_receiverCity', '_receiverDistrict', '_receiverTown'], [add?.provinceName || '', add?.cityName || '', add?.countyName || '', add?.townName || '']);
	};

	const handleEditAddressChange = (e: React.ChangeEvent<HTMLInputElement>, key: string) => {
		setPackageVal(packInfo, key, e.target.value || '');
	};

	const virtualModifyAddr = async() => {
		if (!packInfo.isDecrypted) {
			message.error('请全部解密后再确定');
			return;
		}
		if (packInfo.platform === PLAT_SPH && packInfo.status !== TradeStatus.等待卖家发货) {
			message.error('未发货的订单可以修改');
			return;
		}
		if (JdConsolidateType[packInfo.consolidateType]) {
			message.error('物流运转订单不支持操作，请前往京麦订单详情查看处理');
			return;
		}
		// * 抖店需要同步商家后台
		const operateLogFn = async() => {
			const { mobile, receiver, receiverAddress, newMobile, newReceiver, newReceiverAddress } = await TradeInfoBlurApi({
				mobile: packInfo.receiverMobileMask,
				receiver: packInfo.receiverNameMask,
				receiverAddress: packInfo.receiverAddressMask,
				newMobile: packInfo._receiverMobileMask,
				newReceiver: packInfo._receiverNameMask,
				newReceiverAddress: packInfo._receiverAddressMask,
			});
			await TradeOperateLogAddApi([{
				operateType: TradeOptEnum.收件人信息,
				// eslint-disable-next-line max-len
				operateContent: `旧信息: ${receiver} ${mobile} ${packInfo.receiverState} ${packInfo.receiverCity} ${packInfo.receiverDistrict}${IS_ADDR_TOWN(packInfo.platform) ? ' ' + packInfo.receiverTown : ''} ${receiverAddress} \n 新信息: ${newReceiver} ${newMobile} ${packInfo._receiverState} ${packInfo._receiverCity} ${packInfo._receiverDistrict}${IS_ADDR_TOWN(packInfo.platform) ? ' ' + packInfo._receiverTown : ''} ${newReceiverAddress}`,
				operateResult: 1,
				platform: packInfo.platform,
				tids: packInfo.togetherId.split('|').join(',')
			}]);
		};
		if ([PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_JD, PLAT_YZ].includes(packInfo.platform) && !isSourceHand(packInfo)) {
			setModifyAddrLoading(true);
			TradeModifyAddressApi({
				tids: packInfo.togetherId.split('|').join(','),
				ptTids: packInfo?.trades?.map(s => s.ptTid)?.join(','),
				sellerId: +packInfo.sellerId,
				platform: packInfo.platform,
				receiverProvince: packInfo._receiverState,
				receiverCity: packInfo._receiverCity,
				receiverCounty: packInfo._receiverDistrict,
				receiverTown: packInfo._receiverTown,
				receiverAddress: packInfo._receiverAddressMask,
				receiverName: packInfo._receiverNameMask,
				receiverMobile: packInfo._receiverMobileMask,
				receiverZip: packInfo._receiverZip,
				source: packInfo.source
			}).then(async() => {
				await operateLogFn();
				modifyAddr(packInfo);
			}).finally(() => {
				setModifyAddrLoading(false);
			});
		} else {
			await operateLogFn();
			modifyAddr(packInfo);
		}
	};

	const saveMome = (tradeIndex: number, flagIndex?: string) => {
		// 1688平台修改备注必须强制选择灰旗以外的旗帜
		if (packInfo.platform === PLAT_ALI && (flagIndex == '0' || flagIndex == '5' || !flagIndex)) {
			message.error('1688平台规定订单修改备注必须设置非灰、紫旗的旗帜，请设置旗帜后保存');
			return;
		}
		if (packInfo.platform === PLAT_ALI && !packInfo.trades[tradeIndex]._sellerMemo?.trim()) {
			message.error('1688平台规定订单备注必填');
			return;
		}
		packageDetailUpdateMemo(packInfo, tradeIndex, flagIndex);
	};

	const saveSysMemo = debounce((tradeIndex: number) => {
		packageDetailUpdateSysMemo(packInfo, tradeIndex);
	}, 500, {
		leading: true,
		trailing: false
	});

	const toggleSubOrderItem = async(e: any, tradeIndex: number, orderIndex: number) => {
		handleOrderCheck(packInfo, tradeIndex, orderIndex);
		handleProductOrders(packInfo);
	};


	const renderDecryptIcon = (type: DecryptFiledEnum[number] = DecryptFiledEnum['全部']) => {
		return (
			<span className="r-pointer" onClick={ () => { handleDecrypt(packInfo, type); } }><Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5 } } /></span>
		);
	};

	const handleTradeInfoChange = (tradeIndex: number, value: string, key: string) => {
		if (tradeIndex === -1) {
			setPackageVal(packInfo, key, value);
		} else {
			setTradeVal(packInfo.trades[tradeIndex], key, value);
		}
	};

	const popMultiPackModal = (orders: any) => {
		const ydNoSet = orders.map(order => order.ydNoSet).filter(item => item.length);
		setModalItem(ydNoSet);
		setIsModalVisible(true);
	};

	const handleModalOk = (arr: any) => {
		const str = arr.reduce((a:any, b:any) => a + b + '; ', '');
		copyToPaste(str);
		setIsModalVisible(false);
	};

	const MultiPackModal = () => {
		const renderItem: any[] = [];
		modalItem?.filter(Boolean).forEach((item: any) => {
			const ydNos = [];
			item.forEach(element => {
				const [ydName, ydNo] = element?.split(':') || [];
				if (ydNo) {
					ydNos.push({ ydNo, ydName });
				}
			});
			const ydNosGroup = groupBy(ydNos, 'ydName');
			renderItem.push(ydNosGroup);
		});

		console.log('modalItem', modalItem, renderItem);
		return (
			<Modal
				title="多包裹发货记录"
				visible={ isModalVisible }
				onOk={ () => handleModalOk(modalItem) }
				onCancel={ () => setIsModalVisible(false) }
				okText="复制运单号"
				cancelText="关闭"
				maskClosable={ false }
			>
				{
					renderItem.map((item: any, index) => {
						return 	(
							<div key={ index }>
								{index ? <Divider style={ { margin: '6px 0' } } /> : null}
								{Object.keys(item).map((key, j) => (
									<Row key={ key }>
										<Col span={ 8 } className="r-jc-fe r-flex">{key}:</Col>
										<Col>{item[key].map((v, k) => <div key={ k }>{v.ydNo}</div>)}</Col>
									</Row>
								))}
							</div>
						);
					})
				}
			</Modal>
		);
	};

	const cancelPreShip = (trade: ISubTrade) => {
		operate(2, [{
			opsId: '',
			tid: trade.tid,
			platform: packInfo.platform,
		}], () => {
			runInAction(() => {
				trade.isPreShip = false;
				if (packInfo.trades.every(trade => !trade.isPreShip)) {
					packInfo.isPreShip = false;
				}
			});
		});
	};

	const isDecrypt = (type: DecryptFiledEnum[number] = DecryptFiledEnum['全部']) => {
		if (![PLAT_FXG, PLAT_TB, PLAT_TM].includes(packInfo.platform)) {
			return packInfo.isDecrypted;
		} else if (type) {
			return packInfo[`${type}IsDecrypt`];
		} else {
			return packInfo.isDecrypted;
		}
	};

	const receiverNameValue = () => {
		return packInfo.isDecrypted ? packInfo._receiverNameMask : packInfo[`${DecryptFiledEnum['收件人']}IsDecrypt`] || packInfo._receiverNameMask;
	};

	const receiverMobileValue = () => {
		return packInfo.isDecrypted ? packInfo._receiverMobileMask : packInfo[`${DecryptFiledEnum['手机号']}IsDecrypt`] || packInfo._receiverMobileMask;
	};

	// 换商品
	const onClickChangeGoods = (trade) => {
		sendPoint(Pointer.订单_订单打印_订单详情_编辑商品);
		runInAction(() => {
			setIsShowChangeGoodsModal(true);
			setChangeGoodsData({
				packInfo,
				trades: [trade]
			});
		});
	};

	// 添加赠品
	const onClickAddGiftGoods = (trade) => {
		runInAction(() => {
			setIsShowBatchAddGoodsModal(true);
			setBatchAddGoodsPackage([packInfo], trade);
		});
	};

	// 换商品、添加赠品 可点击条件判断
	const getEditGoodsBtnStatus = (trade) => {
		if ([PLAT_DW].includes(trade.platform) || userStore.isDistributorAccount) { // 分销商
			return {
				disabled: true,
				changeGoodsReason: "暂不支持换商品",
				addGiftGoodsReason: "暂不支持添加赠品",
			};
		}
		if (isSourceScm(packInfo)) {
			return {
				disabled: true,
				changeGoodsReason: "分销推送订单不支持换商品",
				addGiftGoodsReason: "分销推送订单不支持添加赠品",
			};
		}
		// 拆单的订单不能编辑
		// if (packInfo?.isSplit) {
		// 	return {
		// 		disabled: true,
		// 		changeGoodsReason: "拆分订单不支持换商品",
		// 		addGiftGoodsReason: "拆分订单不支持添加赠品",
		// 	};
		// }
		if (trade.orders.some(i => i.dropShipping)) {
			return {
				disabled: true,
				changeGoodsReason: "存在分销代发商品，暂不支持换商品",
				addGiftGoodsReason: "存在分销代发商品，暂不支持添加赠品",
			};
		}
		if (trade.orders.some(i => i.needSerialNumber)) {
			return {
				disabled: true,
				changeGoodsReason: "该商品属于特殊类目商品，暂不支持换商品",
				addGiftGoodsReason: "该商品属于特殊类目商品，暂不支持添加赠品",
			};
		}
		if (packInfo.isPending) {
			return {
				disabled: true,
				changeGoodsReason: "订单已挂起，请解除挂起后再换商品",
				addGiftGoodsReason: "订单已挂起，请解除挂起后再添加赠品",
			};
		}
		// 官方仓发
		if (packInfo?.serviceTagList?.includes('gfcf')) {
			return {
				disabled: true,
				changeGoodsReason: "官方仓发不支持换商品",
				addGiftGoodsReason: "官方仓发不支持添加赠品",
			};
		}
		// 手工单，售后创建的手工单放开编辑
		// if (isSourceHand(packInfo) || packInfo.source === AFTERSALE_HANDORDER) {
		// 	return {
		// 		disabled: true,
		// 		changeGoodsReason: "售后创建的手工单不支持换商品",
		// 		addGiftGoodsReason: "售后创建的手工单不支持添加赠品",
		// 	};
		// }
		if (trade.isPreShip) {
			return {
				disabled: true,
				changeGoodsReason: "自动发货订单不支持换商品",
				addGiftGoodsReason: "自动发货订单不支持添加赠品",
			};
		}
		if (trade.isPrintFhd == 1 || packInfo.shipListPrintStatus === 'already') {
			return {
				disabled: true,
				changeGoodsReason: "已打印发货单不支持换商品",
				addGiftGoodsReason: "已打印发货单不支持添加赠品",
			};
		}
		if (trade.isPrintKdd == 1) {
			return {
				disabled: true,
				changeGoodsReason: "已打印快递单不支持换商品",
				addGiftGoodsReason: "已打印快递单不支持添加赠品",
			};
		}
		const hasLabel = trade.orders.every(order => order.labelstatus);
		if (hasLabel) {
			return {
				disabled: true,
				changeGoodsReason: "已生成商品标签不支持换商品",
				addGiftGoodsReason: "已生成商品标签不支持添加赠品",
			};

		}
		const hasPrint = trade.orders.every(order => order.labelPrintStatus);
		if (hasPrint) {
			return {
				disabled: true,
				changeGoodsReason: "已打印商品标签不支持换商品",
				addGiftGoodsReason: "已打印商品标签不支持添加赠品",
			};
		}

		const hasWaveNoList = trade.waveNoList && trade.waveNoList.length > 0;
		if (hasWaveNoList) {
			return {
				disabled: true,
				changeGoodsReason: "已生成拣货波次不支持换商品",
				addGiftGoodsReason: "已生成拣货波次不支持添加赠品",
			};
		}

		const hasWaitSend = trade.orders.some(order => TradeStatus.等待卖家发货 === order.status);
		if (!hasWaitSend) {
			return {
				disabled: true,
				changeGoodsReason: "只有待发货订单支持换商品",
				addGiftGoodsReason: "只有待发货订单支持添加赠品",
			};
		}
		return { disabled: false, changeGoodsReason: '', addGiftGoodsReason: '' };
	};

	const renderTextArea = () => {
		let isOverflow = packInfo.printContentOverflow || false;
		return (
			<textarea
				value={ packInfo.printContent }
				placeholder="请输入发货内容"
				className="print-content-textarea"
				style={ { borderColor: isOverflow ? 'red' : '#d9d9d9' } }
				maxLength={ PRINT_CONTENT_MAX_LENGTH }
				onInput={ (e) => {
					setPackageVal(packInfo, 'printContentOverflow', e.target.value.length >= PRINT_CONTENT_MAX_LENGTH);
					setPackageVal(packInfo, 'printContent', e.target.value);
				} }
			/>
		);
	};


	const modifySysPicMemo = (e: any, trade) => {
		const { setModifySysMemoPicPackage, setIsShowBatchSysMemoPicModal } = tradeStore;
		e.stopPropagation();
		runInAction(() => {
			setModifySysMemoPicPackage({ ...trade });
			setIsShowBatchSysMemoPicModal(true);
		});
	};

	const disabledEditAddress = packInfo.platform == PLAT_DW || (packInfo.platform == PLAT_SPH && packInfo?.trades?.[0]?.tradeTypeTagStr == 'exchange_goods');
	return (
		<div className={ `batch_unfold_detail_con ${packInfo.isPending ? 'fc-pending' : ''} ${packInfo.shipStatus === 'alreadyShipped' ? 'fc-already-shipped' : ''}` } onClick={ (e) => { e.stopPropagation(); } } >
			<div className="batch_detail_wrap">
				<span className="batch_detail_label">昵称：</span>
				<div className="r-mr-12 r-flex r-ai-c">
					<BuyerNickComp
						togetherId={ packInfo.togetherId }
						ptTid={ packInfo?.trades?.[0]?.ptTid || '' }
						tid={ packInfo?.trades?.[0]?.tid || '' }
						orderId={ packInfo?.trades?.[0]?.orders?.[0]?.ptOid }
						encryptuid={ packInfo.buyerOpenUid }
						platform={ packInfo.platform }
						buyerNick={ packInfo.buyerNick }
						sellerId={ packInfo?.sellerId }
						sellerName={ packInfo?.sellerNick }
					/>
					{!isDecrypt(DecryptFiledEnum['收件人']) ? (
						<span onClick={ () => { handleDecrypt(packInfo, DecryptFiledEnum['收件人']); } }>
							<Icon pointer type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5 } } />
						</span>
					) : ''}
					{getTradePlatformLabel(packInfo.platform)}
				</div>
				<Button className="r-mr-4" size="small" onClick={ () => { copyInfo('name'); } }>复制昵称</Button>
				<div className="r-flex r-ai-c r-relative">
					<Button size="small" onClick={ () => { copyInfo('all'); } }>复制全部</Button>
					{copySettingVisible ? <CopySettingModal onClose={ () => { setCopySettingVisible(false); } } /> : ''}
					<Button size="small" onClick={ () => { setCopySettingVisible(true); } }>
						<Icon type="shezhi" />
					</Button>
				</div>
				<Button
					style={ { marginLeft: 4 } }
					size="small"
					className="r-mr-12"
					onClick={ () => { copyInfo('addr'); } }
				>复制地址
				</Button>
				<div className="mark_dot">
					{Object.keys(packInfo?.sellerFlagSys || {}).map((flag, index) => (
						<span
							key={ flag }
							onClick={ () => { sendPoint(Pointer.订单_订单打印_订单操作_订单本地标记_单笔标记); modifySysMemo(packInfo, 'pack', flag); } }
							className={ `clickable r-pointer ${getMemoSysFlag(flag, packInfo.sellerFlagSys[flag])}` }
						/>
					))}
				</div>
				{/* <span className="memo-name-config" onClick={ () => { setIsShowMarkSetting(true); } }>标记设置</span>
				{isShowMarkSetting ? <TradeMarkSetting onClose={ () => { setIsShowMarkSetting(false); } } /> : ''} */}
			</div>
			<div className="batch_detail_wrap">
				<span className="batch_detail_label">收件人：</span>
				<Input
					disabled={ !packInfo.isDecrypted || disabledEditAddress }
					style={ { width: 160 } }
					value={ receiverNameValue() }
					addonAfter={ isDecrypt(DecryptFiledEnum['收件人']) ? '' : renderDecryptIcon(PLAT_FXG == packInfo.platform && packInfo.serviceTagList?.includes('ciphertextHandTrade') ? DecryptFiledEnum.全部 : DecryptFiledEnum.收件人) }
					onChange={ e => { handleEditAddressChange(e, '_receiverNameMask'); } }
				/>
				<span className="r-ml-12" >手机：</span>
				<Input
					disabled={ !packInfo.isDecrypted || disabledEditAddress }
					style={ { width: 160 } }
					value={ receiverMobileValue() }
					addonAfter={ isDecrypt(DecryptFiledEnum['手机号']) ? '' : renderDecryptIcon(PLAT_FXG == packInfo.platform && packInfo.serviceTagList?.includes('ciphertextHandTrade') ? DecryptFiledEnum.全部 : DecryptFiledEnum.手机号) }
					onChange={ e => { handleEditAddressChange(e, '_receiverMobileMask'); } }
				/>
				{packInfo.secret_no_expire_time ? (
					<div className="r-ml-4">
						<SecretPhoneTip pack={ packInfo } />
					</div>
				) : ''}
				{packInfo.isVirtualNum ? (
					<Popover
						placement="right"
						content={ (
							<img
								style={ { width: 500 } }
								src={ getVirtualImgUrl(packInfo.platform) }
								alt=""
							/>
						) }
					>
						<Button type="link">虚拟号可直接发货/联系消费者</Button>
					</Popover>
				) : ''}
				<span className="r-ml-12" >邮编：</span>
				<Input
					disabled={ !packInfo.isDecrypted || disabledEditAddress }
					style={ { width: 160 } }
					value={ filterUdbUdc(packInfo._receiverZip) }
					onChange={ e => { handleEditAddressChange(e, '_receiverZip'); } }
				/>
				{
					packInfo.platform === PLAT_TB
						&& packInfo.serviceTagList.includes("presentOrder") && packInfo?.trades?.[0]?.realReceiverOpenId && packInfo?.trades?.[0]?.realReceiverDisplayNick
						&& (
							<>
								<span className="r-ml-12" >收件人昵称：</span>
								<BuyerNickComp ptTid={ packInfo?.trades?.[0]?.ptTid || '' } tid={ packInfo?.trades?.[0]?.tid || '' } encryptuid={ packInfo?.trades?.[0]?.realReceiverOpenId || '' } platform={ packInfo.platform } buyerNick={ packInfo?.trades?.[0]?.realReceiverDisplayNick || '' } sellerId={ packInfo?.sellerId } />
								<Tooltip title="为保障用户信息及隐私，请勿对收货人（收货人nick）擅自透露订单的支付账号、优惠权益等支付信息"><InfoCircleFilled className="r-ml-10 r-c-warning" style={ { "fontSize": 14 } } /></Tooltip>
							</>
						)
				}
			</div>
			<div className="batch_detail_wrap">
				<span className="batch_detail_label">收件地址：</span>
				{
					packInfo.isDecrypted ? (
						<>
							<AddrSelect
								disabled={ disabledEditAddress }
								province={ packInfo._receiverState }
								city={ packInfo._receiverCity }
								country={ packInfo._receiverDistrict }
								town={ packInfo._receiverTown }
								hasTown={ IS_ADDR_TOWN(packInfo.platform) && !isSourceHand(packInfo) }
								formItem
								triggerChange
								onChange={ handleAddressValues }
								platform={ packInfo.platform }
							/>
							<Input
								type="text"
								disabled={ disabledEditAddress }
								style={ { width: 280, marginLeft: 12 } }
								className="input_text addr receiverAddress ml-10"
								value={ packInfo._receiverAddressMask }
								onChange={ (e) => handleEditAddressChange(e, '_receiverAddressMask') }
							/>
						</>
					) : (
						<>
							<Input
								key="state"
								type="text"
								disabled
								className="r-mr-8"
								style={ { width: 160 } }
								value={ packInfo._receiverState }
								onChange={ (e) => handleEditAddressChange(e, '_receiverState') }
							/>
							<Input
								key="city"
								type="text"
								disabled
								className="r-mr-8"
								value={ packInfo._receiverCity }
								style={ { width: 160 } }
								onChange={ (e) => handleEditAddressChange(e, '_receiverCity') }
							/>
							<Input
								key="district"
								type="text"
								disabled
								className="r-mr-8"
								value={ packInfo._receiverDistrict }
								style={ { width: 160 } }
								onChange={ (e) => handleEditAddressChange(e, '_receiverDistrict') }
							/>
							{
								IS_ADDR_TOWN(packInfo.platform) && !isSourceHand(packInfo) ? (
									<Input
										key="town"
										type="text"
										disabled
										className="r-mr-8"
										value={ packInfo._receiverTown }
										style={ { width: 160 } }
										onChange={ (e) => handleEditAddressChange(e, '_receiverTown') }
									/>
								) : null
							}

							<Input
								key="address"
								type="text"
								disabled
								className="r-mr-8"
								value={ filterAddrDetail(packInfo) || packInfo._receiverAddressMask }
								style={ { width: 260 } }
								onChange={ (e) => handleEditAddressChange(e, '_receiverAddressMask') }
								// 淘宝/天猫/淘特、抖音订单，订单详情地址解密改为全部解密的操作，点击对全部收件人信息进行解密
								addonAfter={ isDecrypt(DecryptFiledEnum['地址']) ? '' : renderDecryptIcon(DecryptFiledEnum['全部']) }
							/>
						</>
					)
				}
				{
					disabledEditAddress
						? null
						: (
							<>
								<Button
									className="r-ml-10 r-mr-10"
									onClick={ () => { virtualModifyAddr(); } }
									loading={ [PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_YZ].includes(packInfo.platform) ? modifyAddrLoading : false }
								>确定
								</Button>
								{
									[PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_YZ].includes(packInfo.platform) ? '' : (
										[PLAT_JD].includes(packInfo.platform) ? (
											<span className="r-c-666 r-fs-12">(该平台仅支持对三/四级地址、详细地址、姓名、电话修改)</span>
										) : (
											<span className="r-c-666 r-fs-12">(不会改变原订单地址)</span>
										)
									)
								}
							</>
						)
				}
			</div>
			{packInfo.trades.map((trade, tradeIndex) => {
				const { disabled: editGoodsBtnDisabled, changeGoodsReason, addGiftGoodsReason } = getEditGoodsBtnStatus(trade);
				const hasJingxi = trade.serviceTagList?.includes('jingxi');

				return (
					<div key={ trade.tid }>
						<div className="batchuf_ordernum">
							{
								trade.platform === PLAT_TB && trade.serviceTagList.includes("presentOrder") && <Tooltip title="订单的付款人和收货人不同，发货及售后问题请联系收货人；发票问题请联系付款人"><InfoCircleFilled className="r-mr-5 r-c-warning" style={ { "fontSize": 14 } } /></Tooltip>
							}
							<span className="r-mr-16">
								系统单号：{trade.tid}
								<CopyOutlined className="r-ml-18 r-mr-18 r-pointer" onClick={ () => { copyToPaste(splitFxgTid(trade.tid)); } } />
							</span>
							<span className="r-mr-16">
								订单编号：{trade.ptTid}
								<CopyOutlined className="r-ml-18 r-mr-18 r-pointer" onClick={ () => { copyToPaste(splitFxgTid(trade.ptTid)); } } />
							</span>

							{/* 订单状态 */}
							{Array.from(new Set(trade?.orders?.map(order => order?.ydNoSet?.[0]))).filter(Boolean)?.map(item => (
								<ShowLogistic ydNo={ item.split(',')[0]?.split(":")?.[1] }>
									<span key={ item } className="trade-status-error r-mr-10">(发货成功，{item.split(',')[0]})</span>
								</ShowLogistic>
							))}
							{trade.isPreShip && <span className="r-click r-mr-10" onClick={ () => cancelPreShip(trade) }>取消自动发货</span>}
							{trade.multiPackYdCount > 1 ? (
								<>
									<span className="r-mr-12 r-fc-1890FF r-pointer" onClick={ () => popMultiPackModal(trade.orders) }>多包裹单号</span>
								</>
							) : null}
							{getTradeStatusLabel(trade.status)}
							{
								trade.platform === PLAT_TB && trade.serviceTagList.includes("presentOrder") && <Tooltip title="为保障用户信息及隐私，请勿对付款人（付款人nick）擅自透露订单的收货地址、物流轨迹等信息"><InfoCircleFilled className="r-ml-5 r-c-warning" style={ { "fontSize": 14 } } /></Tooltip>
							}
							<span className="r-mr-40 r-ml-40">付款时间：{trade.payTime}</span>
							{
								trade.platform === PLAT_KS && trade.governmentDiscount && trade.governmentDiscount !== 0 ? (<b className="r-mr-40">享政府补贴{trade.governmentDiscount}元</b>) : null
							}
							{
								!trade.paymentSensitive
									? (
										<>
											{
												[PLAT_TB, PLAT_TM].includes(trade.platform) && packInfo?.serviceTagList?.includes('presentOrder')
													? <b>实付{trade.payment}元（含预付运费{trade.prePostFee}元，退运费差价{trade.refundPostFee}元，实际运费{trade.postFee}元）</b>
													: <b>实付{trade.payment}元（含运费{trade.postFee}）</b>
											}
											{
												[PLAT_TB, PLAT_TM].includes(trade.platform) && trade.payDisplayType ? (
													<Tooltip title={ getPayDisplayTypeText(trade.payDisplayType, trade.payDisplayTypeValue) }>
														<div className="r-mt-2">
															<Icon style={ { color: PAY_DISPLAY_TYPE_COLOR_MAP[trade.payDisplayType] } } type={ PAY_DISPLAY_TYPE_ICON_MAP[trade.payDisplayType] } className="r-mr-5" />
														</div>
													</Tooltip>
												) : null
											}
										</>
									)
									: null
							}
							{
								trade.isCod ? (<span className="r-ml-16">代收金额 {getCostPrice(trade.waitReceiveAmount || '0.00')} 元</span>) : null
							}
							{trade.duoduoWholesale ? <span className="r-mr-12 r-fw-500" style={ { color: '#20b7e6' } }>多多批发</span> : ''}
							{ConsolidateType[packInfo.consolidateType] ? consolidateContent(packInfo.consolidateType, 'detail', trade) : ''}
						</div>

						{
							trade.platform === PLAT_KTT && (
								<div className="batch_detail_wrap">
									<span className="batch_detail_label r-l-preWrap">活动标题：</span>{trade?.activityTitle || '-'}
									<span className="r-ml-40">跟团号：{trade?.participateNo || '-'}</span>
								</div>
							)
						}


						<div className="batch_detail_wrap">
							<span className="batch_detail_label">买家留言：</span>
							<Input
								onChange={ (e) => {
									handleTradeInfoChange(tradeIndex, e.target.value, '_buyerMessage');
								} }
								disabled={ isSourceScm(packInfo) }
								value={ packInfo.trades[tradeIndex]._buyerMessage }
								style={ { width: 900 } }
							/>
						</div>
						<div className="batch_detail_wrap">
							<span className="batch_detail_label">卖家备注：</span>
							<Tooltip
								title={ packInfo.platform === PLAT_KTT ? "快团团的备注信息仅支持系统内修改，不支持同步至线上" : "" }
								trigger={ packInfo.platform === PLAT_KTT ? "hover" : [] }
								placement="topLeft"
								overlayStyle={ { maxWidth: 600 } }
							>
								<Input
									onChange={ (e) => {
										handleTradeInfoChange(tradeIndex, e.target.value, '_sellerMemo');
									} }
									value={ packInfo.trades[tradeIndex]._sellerMemo }
									style={ { width: 900, marginRight: 10 } }
								/>
							</Tooltip>
							{getTradeFlag(tradeIndex, null, packInfo.trades[tradeIndex]._sellerMemoFlag, false)}
							{getTradeFlagTag(packInfo.trades[tradeIndex]._sellerMemoFlag, packInfo.trades[tradeIndex]?.sellerFlagTag)}
							{!hasJingxi && (
								<>
									{packInfo.platform !== PLAT_KTT && (
										<span
											className="r-ml-10 r-mr-10"
											onClick={ () => {
												setTradeVal(packInfo.trades[tradeIndex], 'isChangeFlag', true);
											} }
											style={ { color: '#999', textDecoration: 'underline', cursor: 'pointer' } }
										>(更改)
										</span>
									)}
									{packInfo.trades[tradeIndex].isChangeFlag ? (
										<span className="r-ml-10 r-mr-10">
											<span>选择旗帜：</span>
											{getTradeFlag(tradeIndex, saveMome, '', true, packInfo.platform)}
										</span>
									) : ''}
									<Button size="small" className="ml-10" onClick={ () => { saveMome(tradeIndex, packInfo.trades[tradeIndex]._sellerMemoFlag); } }>保存</Button>
								</>
							)}

						</div>
						<div className="batch_detail_wrap">
							<span className="batch_detail_label">线下标记：</span>
							<div className="userset_icons">
								{Object.keys(trade?.sellerFlagSys || {}).map((flag, index) => (
									<span
										key={ flag }
										onClick={ () => { modifySysMemo(packInfo, 'trade', flag, tradeIndex); } }
										className={ `clickable r-pointer ${getMemoSysFlag(flag, trade.sellerFlagSys[flag])}` }
									/>
								))}
								{trade.isPreShip && <span className="r-click r-mr-10" onClick={ () => cancelPreShip(trade) }>取消自动发货</span>}
							</div>
						</div>
						<div className="batch_detail_wrap">
							<span className="batch_detail_label">线下备注：</span>
							<Input
								onChange={ (e) => {
									handleTradeInfoChange(tradeIndex, e.target.value, '_sysMemo');
								} }
								disabled={ isSourceScm(packInfo) }
								value={ packInfo.trades[tradeIndex]._sysMemo }
								style={ { width: 900 } }
								maxLength={ 500 }
								showCount
							/>
							<Button size="small" disabled={ isSourceScm(packInfo) } className="ml-10" onClick={ () => { saveSysMemo(tradeIndex); } }>保存</Button>
						</div>
						<div style={ { marginLeft: 70, marginBottom: 16 } }>
							<div>
								<Button className="g-clickable" style={ { margin: 0, padding: 0 } } type="link" onClick={ (e) => { modifySysPicMemo(e, trade); } }>{packInfo.trades[tradeIndex]?.sysMemoPic ? "编辑图片" : "添加图片"}</Button>
							</div>
							<SysMemoPicFragment key={ trade.tid } trade={ trade } />
						</div>
						<div className="batch_detail_wrap" style={ { marginBottom: 0 } }>
							<span className="batch_detail_label r-as-fs" style={ { height: 28, lineHeight: '28px' } }>商品：</span>
							<div className="batch_detail_goods_con">
								<div className="r-flex r-ai-c r-mb-6">
									<Tooltip title={ editGoodsBtnDisabled && changeGoodsReason }>
										<Button size="small" disabled={ editGoodsBtnDisabled } onClick={ () => onClickChangeGoods(trade) }>换商品</Button>
									</Tooltip>
									<Tooltip title={ editGoodsBtnDisabled && addGiftGoodsReason }>
										<Button className="r-ml-10" size="small" disabled={ editGoodsBtnDisabled } onClick={ () => onClickAddGiftGoods(trade) }>添加商品</Button>
									</Tooltip>
								</div>
								{ getPackageDetailOrders(trade).map(($value, orderIndex) => (
									<div className="batch_detail_goods_item_wrap" key={ $value.oid }>
										<div className="batch_detail_goods_item">
											{ $value.dropShipping ? (
												<Tooltip title="分销代发商品不可勾选，如需操作，需前往待发订单撤销推单。" trigger="hover">
													<div style={ { display: 'flex' } } >
														<Input
															type="checkbox"
															name="checkGoods"
															className="r-mr-10"
															style={ { width: 16, marginTop: 2 } }
															checked={ $value.isChecked }
															disabled
														/>
													</div>
												</Tooltip>
											) : (
												<Input
													type="checkbox"
													name="checkGoods"
													className="r-mr-10"
													style={ { width: 16, marginTop: 2 } }
													checked={ $value.isChecked }
													disabled={ isOrderDisabled($value, trade.orders, trade?.serviceTagList) }
													onChange={ (e) => { toggleSubOrderItem(e, tradeIndex, $value.orderIndex); } }
												/>
											)}
											<div className="r-flex r-ai-c">
												<div className="r-relative" style={ { height: 60 } }>
													<WaresInfo
														orderInfo={ { ...$value, isCheckedStock: isCheckStock(trade) } }
														isShowHoverStock
														isShowHoverName
														wareName={ $value.title }
														skuName={ $value.skuPropertiesName }
														skuNum={ $value.num }
														onlyImg
														imgUrl={ $value.picPath || $value.sysPicPath }
														imgSize={ 60 }
														linkDetailId={ !$value?.itemLinkSensitive ? $value.numIid : null }
														linkDetailPlatForm={ packInfo.platform }
														banJump={ !userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(packInfo) }
														// 其实不用传 默认就是1
														previewPicSize={ 1 }
													/>
													{!$value?.ignore && userStore?.inventoryDeduct && isCheckStock(trade) ? (
														<>
															{ $value.noGoods ? <div className="order-no-goods-label">缺货</div> : ''}
															{ $value.goodsWarn ? <div className="order-warning-goods-label">警戒</div> : ''}
														</>
													) : ''}

													{
														$value.dropShipping ? (
															<div className="order-error-label">代发订单</div>
														) : ''
													}
												</div>
											</div>
											<div className="batch_detail_goods_info">
												{+$value.numIid === -1 ? (
													<>
														<div>(无商品)</div>
														<div />
														<div>
															{getTradeStatusLabel($value.status)}
															{getOrderRefundStatus($value.refundStatus)}
														</div>
													</>
												) : (
													<PackageDetailGoodsInfo trade={ trade } $value={ $value } packInfo={ packInfo } editGoodsBtnDisabled={ editGoodsBtnDisabled } />
												)}

											</div>
											{
												userStore.isStockAllocationVersion
													? <OrderStockInfoItem order={ $value } ignoreType={ trade.ignoreType } isCheckedStock={ isCheckStock(trade) } /> : ''
											}
										</div>
										{
											$value.needSerialNumber || $value.productIdCode ? (
												<SerialNumberCom order={ $value } />
											) : null
										}

									</div>
								))}
								{((trade.isHideFinishedGoods && trade.hideOrderByTradeFinished)
									|| (trade.hidePartShipOrder && trade.hideOrderByPartShipCount)
									|| (trade.isHideClosedGoods && trade.hideOrderByClosedGoods)
									|| (trade.isHideDropShippingGoods && trade.hideOrderByDropShippingGoods)
									|| (trade.hideOrderByRefundingGoods && trade.isHideRefundingGoods)) ? (
										<div
											onClick={ () => {
												runInAction(() => {
													trade.isHideFinishedGoods = false;
													trade.hidePartShipOrder = false;
													trade.isHideClosedGoods = false;
													trade.isHideRefundingGoods = false;
													trade.isHideDropShippingGoods = false;
												});
											} }
											className="r-click"
											style={ { color: '#FD8204' } }
										>显示{
												Number(trade.hideOrderByTradeFinished || 0)
												+ Number(trade.hideOrderByPartShipCount || 0)
												+ Number(trade.hideOrderByClosedGoods || 0)
												+ Number(trade.hideOrderByRefundingGoods || 0)
												+ Number(trade.hideOrderByDropShippingGoods || 0)
											}个隐藏的商品
										</div>
									) : ''}
							</div>
						</div>
					</div>
				);
			})}
			<div className="batch_detail_print_con">
				<div className="r-mb-16 r-mt-16">
					<span style={ { marginRight: 168 } }>要在快递单上打印的发货内容：</span>
					<span className="r-mr-24 g-clickable" onClick={ () => { setIsShowPrintStyleSetting(true); } }>设置打印内容</span>
					<span className="g-clickable" onClick={ () => { setIsShowKeywordSetting(true); } }>过滤词</span>
				</div>
				{/* packInfo.trades.length === 1 && packInfo.platform === "hand" 手工单发货信息进行编辑后 如果是单个订单，就用编辑后的信息 */}

				{renderTextArea()}
				{packInfo.printContentOverflow ? (
					<span className="r-c-error">发货内容字数已超过10000字上限</span>
				) : ''}
				{packInfo.printContentOverflowRow && !packInfo.printContentOverflow ? (
					<span className="r-c-error">发货内容行数超出{PRINT_CONTENT_MAX_ROW }行上限</span>
				) : ''}

			</div>
			{ MultiPackModal() }
		</div>
	);
};

export default observer(PackageDetail);
