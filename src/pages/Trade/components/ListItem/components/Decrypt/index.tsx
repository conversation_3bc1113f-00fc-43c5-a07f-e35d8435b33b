/* eslint-disable react/jsx-closing-tag-location */
/* eslint-disable camelcase */
import axios from 'axios';
import React from 'react';
import { Modal } from 'antd';
import { Obj } from '@/pages/Trade/interface';
import {cloneDeep} from 'lodash';
import './index.less';
import { TradeBatchDecryDataApi,
	TradeHufuGetSignApi,
	TradeQueryTradeVoApi
} from '@/apis/trade';
import { ITradePddOrderDecryptInfo, TradePddControlDecryptV1ReceiverInfoResponse } from '@/types/trade/index';
import { PLAT_ALI, PLAT_FXG, PLAT_PDD, PLAT_TB, PLAT_TM, PLAT_KS, PLAT_JD, PLAT_SPH, PLAT_XHS, PLAT_C2M, PLAT_YZ ,AFTERSALE_HANDORDER, PLAT_DW, PLAT_KTT} from '@/constants';
import userStore from '@/stores/user';
import { getTbSecurity } from '@/utils/TbSecurity';
import message from "@/components/message";
import { SystemOperateLogUploadApi } from '@/apis/user';
import { dealPlatAsHandPlat, isSourceHand } from '@/components-biz/ShopListSelect/shopListUtils';

const URLREG = /(http|https):[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/;
let decryptTrades:Obj = {};

async function _requestDecrypt(type: string, params:any, sellerNick: string) {
	let res: TradePddControlDecryptV1ReceiverInfoResponse;
	// if (type === 'receiverPhone') {
	// 	res = await TradePddControlDecryptV1ReceiverPhoneApi({
	// 		...params,
	// 	});
	// } else if (type === 'receiverName') {
	// 	res = await TradePddControlDecryptV1ReceiverNameApi({
	// 		...params,
	// 	});
	// } else if (type === 'receiverAddress') {
	// 	res = await TradePddControlDecryptV1ReceiverAddressApi({
	// 		...params,
	// 	});
	// }
	res = await axios({
		method: 'post',
		baseURL: `https://pdd-fangzhou.kuaidizs.cn`,
		url: `/pdd/control/decrypt/v1/${type}`,
		headers: {
			'X-PDD-VerifyAuthToken': window.localStorage.getItem(`verifyAuthToken_${params.mallId}`) || '',
			'Content-Type': 'application/json;charset=UTF-8',
		},
		data: JSON.stringify(params),
	});
	console.log(res, 'res');

	if (res.data.error_msg) {
		_go2WindControl(res.data, sellerNick);
		return new Promise((resolve, reject) => {
			reject();
		});
	}
	return res.data;
}

function _go2WindControl(err: Obj, sellerNick: string) {
	let {
		mall_id, client_id, risk_info, error_msg, error_code,
	} = err;

	// 54001代表风控了，需要新开页滑动解除风控
	if (error_code === 54001) {
		let {
			origin, pathname, search, hash,
		} = window.location;
		let redirectUrl = `${origin + pathname + search.split('&')[0]}&mallId=${err.mall_id}${hash}`;

		let href = `https://fuwu.pinduoduo.com/service-market/are-you-robot?mall_id=${mall_id}&client_id=${client_id}&verifyAuthToken=${risk_info.verify_auth_token}&redirect_url=${encodeURIComponent(redirectUrl)}`;

		window.open(href, '_blank');
	} else if (URLREG.exec(error_msg)) {
		// 错误里包含了链接，说明解密额度不足了，需要去商家后台申请额度
		Modal.confirm({
			centered: true,
			width: 500,
			title: '提示',
			content: (
				<div>
					订单解密：<span className="r-c-error">{sellerNick}</span>店铺,
					{error_msg || '数据解密失败'}
				</div>
			),
			okText: '前往申请',
			onOk: () => {
				let href = (URLREG.exec(error_msg) || [])[0];
				window.open(href, '_blank');
			},
		});
	} else {
		message.warning?.(`订单解密：${error_msg || '数据解密失败'}`);
	}
}

async function _singleReq(data:Obj) {
	console.log('_singleReq');
	const { userId } = await userStore.getUserInfo();
	let {
		togetherId, mallId, encryptObj, sellerNick, pack
	} = data;
	console.log(data, '123');

	let decryptTrade:Obj = {};

	let params:Obj = {
		tid: pack?.trades?.[0]?.realTid||pack.ptTid,
		mallId,
		userId,
	};
	let resPhone: ITradePddOrderDecryptInfo;
	let resName: ITradePddOrderDecryptInfo;
	let resAddress:ITradePddOrderDecryptInfo;
	let operateResult = false;
	try {
		resPhone = await _requestDecrypt('receiverPhone', {
			...params,
			encryptContent: encryptObj.phone,
		}, sellerNick);

		resName = await new Promise((resolve, reject) => {
			setTimeout(() => {
				resolve(_requestDecrypt('receiverName', {
					...params,
					encryptContent: encryptObj.name,
				}, sellerNick));
			}, 1000);
		});
		resAddress = await new Promise((resolve, reject) => {
			setTimeout(() => {
				resolve(_requestDecrypt('receiverAddress', {
					...params,
					encryptContent: encryptObj.address,
				}, sellerNick));
			}, 1000);
		});
		operateResult = true;
	// eslint-disable-next-line no-empty
	} catch (error) {
		console.error("解密失败 error:::", error);
	 }
	SystemOperateLogUploadApi({ operateLogType: 'TRADE_DECRY_DATA', operateResult, tid: params.tid });

	decryptTrade[togetherId] = {
		receiverPhone: resPhone?.order_info?.receiver_phone,
		isVirtualNum: +resPhone?.order_info?.virtual_number_type,
		identifyNumber: resPhone?.order_info?.virtual_identify_number,
		receiverAddress: resAddress?.order_info?.receiver_address,
		receiverName: resName?.order_info?.receiver_name,
	};

	return decryptTrade;
}

/**
 * params{
 *  source 模块 number
 *  tids   订单号 string
 *  userIds 店铺列表对象，获取mailId  Object
 *  mainTidList 合单的主订单 obj
 *  type 解密字段， number
 *       1 就姓名
 *       2 就地址
 *       无 姓名+手机+详细地址
 *  keep 是否需要保持缓存，有值就不缓存 string
 *       改了地址重打 addressPrint
 *       打印或没改重打    print
 *  decryTelType 报备类型 number
 *  isDecryTel 是否需要报备 boolean
 *  ydNo 运单号 string
 * }
 *
 * return 解密完成的对象， 失败的订单数组
 */
async function _getSingleDecrypt(params: Obj) {
	let {
		togetherId, mallId, encryptObj, sellerNick, pack
	} = params;
	let result:Obj = {};
	console.log('params__getSingleDecrypt', params);
	
	if (decryptTrades[togetherId]) {
		result[togetherId] = decryptTrades[togetherId];
	} else {
		let trade:Obj = await _singleReq({
			togetherId, mallId, encryptObj, sellerNick, pack
		});

		result = trade;
	}

	return {
		successObj: result,
		failArr: [] as any,
	};
}

/**
 * params{
 *  tids   订单号 array
 *  userIds 店铺列表对象，获取mailId  Object
 *  encryptObj { name, phone, address } 密文
 * }
 *
 * return 解密完成的对象， 失败的订单数组  await
 */
async function decryptFn(params: Obj) {
	let {
		type,
		togetherId,
		mallId,
		encryptObj,
		sellerNick,
		sellerId,
		platform,
		decryptArr,
		isDecrypted,
		pack: originPack, // 不要修改原始数据
		decryptLocation = '', // 点击解密的位置
	} = params;
	let res; 

	let pack = cloneDeep(originPack);

	console.log('pack',pack);

	try {
		res = await TradeQueryTradeVoApi({ tid: pack.encodeTid||pack.tid }) || {};
	} catch (error) {
		res = pack.encodeTid;
	}

	const realEncodeTid = res?.ptTid;
	if(realEncodeTid){
		pack.tid = realEncodeTid;
		pack.ptTid = realEncodeTid;

		// 售后这里不需要改
		if (decryptArr?.length && decryptLocation !== 'createExchangeTradeModal') {
			decryptArr[0].tid = realEncodeTid;
			decryptArr[0].ptTid = realEncodeTid;
		}
	}
	
	let obj;
	const isCiphertextHandTrade = pack.tradeEncodeType == 2 || pack.serviceTagList?.includes('ciphertextHandTrade')
	console.log('params_decryptFn', params, decryptArr, pack,isCiphertextHandTrade,isDecrypted,(isCiphertextHandTrade && (isDecrypted!=1||isDecrypted!=true)));

	if(isCiphertextHandTrade){
		console.log('密文手工单');
		decryptArr.forEach(element => {
			element.source = element.source|| 'HAND';
			element.ptTid = pack?.trades?.[0]?.realTid||pack.ptTid;
		});
	}
	// trade里面有hasDecrypt的售后手动单也可以直接返回信息
	// !! 底单情况特殊处理
	if (
		(isCiphertextHandTrade &&  isDecrypted)
		||!isCiphertextHandTrade && (dealPlatAsHandPlat(platform, [PLAT_XHS, PLAT_SPH, PLAT_DW], pack) || isDecrypted === 1 || isSourceHand(pack) || (pack?.source === AFTERSALE_HANDORDER && pack?.trades?.[0]?.hasDecrypt))) {
		// 手工单返回未加密信息
		let successObj = {};
		successObj[togetherId] = {
			receiverName: encryptObj.name,
			receiverPhone: encryptObj.phone,
			receiverAddress: encryptObj.address,
			buyerNick: encryptObj.buyerNick,
		};
		console.log('%c [ 手工单或者已解密或则视频号小红书得物快团团直接返回未加密信息 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', successObj)
		return { successObj };
	} else if (platform === PLAT_PDD) {
		obj = await _getSingleDecrypt({
			togetherId, mallId, encryptObj, sellerNick, pack
		});
		
		let { successObj, failArr } = obj;
		return { successObj, failArr };
	} else if ((isCiphertextHandTrade && platform!==PLAT_JD)||([PLAT_DW,PLAT_FXG, PLAT_ALI, PLAT_KS, PLAT_XHS, PLAT_C2M, PLAT_SPH,PLAT_YZ].includes(platform))) {
		let params = [...decryptArr];
		if (platform === PLAT_FXG) {
			// !如果先部分解密再全部解密则报错,所以这里全解依旧传入密文,会增加解密额度
			if (type) {
				for (const key in DecryptFiledEnum) {
					if (type !== DecryptFiledEnum[key]) {
						delete params[0][DecryptFiledEnum[key]];
					}
				}
			} else {
				params[0].encodeMobile = pack._receiverMobile || params[0].encodeMobile;
				params[0].encodeReceiverAddress = pack._receiverAddress || params[0].encodeReceiverAddress;
				params[0].encodeReceiverName = pack._receiverName || params[0].encodeReceiverName;
				// params[0].encodeReceiverPhone = pack._receiverMobile
			}
		}
		let res 
		try {
			res = await TradeBatchDecryDataApi(params);
		} catch (error) {
			console.log('222error', error);
			return { successObj: {[togetherId]:{}} };
			// res = await TradeBatchDecryDataApi(params);
		}
		console.log('resres',res);
		
		let successObj = {};
		let encodeTid = decryptArr[0].tid;
		let data = res[encodeTid] || {};
		if (data.errorCode && data.errorMessage && platform === PLAT_FXG) {
			let msg = {
				'3027': '订单状态处于无法解密的状态（如待支付、已关闭等），平台不允许解密',
				'3028': <div>您当天的查看额度已用完，您可以选择明日再尝试或者<a target="_blank" href="https://op.jinritemai.com/docs/question-docs/105/1163" rel="noreferrer">申请提高解密额度</a></div>,
				'3029': '订单状态处于无法解密的状态（如待支付、已关闭等），平台不允许解密'
			};
			Modal.info({
				content: msg[data.errorCode] || data.errorMessage || '',
			});
			return { successObj };
		} else if (data.errorMessage) {
			Modal.info({
				content: data.errorMessage || '',
			});
			return { successObj };
		}

		successObj[togetherId] = {
			receiverName: data.receiverName,
			receiverPhone: data.mobile || data.receiverPhone,
			receiverAddress: data.receiverAddress,
			// isVirtualNum: data?.mobile?.indexOf('-') > -1,
		};

		if (platform === PLAT_FXG) {
			if (type) {
				successObj[togetherId][`${type}IsDecrypt`] = data[DecryptFiledMapEnum[type]];
			} else {
				successObj[togetherId] = {
					receiverName: data.receiverName,
					receiverPhone: data.mobile,
					receiverAddress: data.receiverAddress,
					[`${DecryptFiledEnum['地址']}IsDecrypt`]: data.receiverAddress,
					[`${DecryptFiledEnum['手机号']}IsDecrypt`]: data.mobile,
					[`${DecryptFiledEnum['收件人']}IsDecrypt`]: data.receiverName,
				};
			}
		}
		return { successObj };
	} else if ([PLAT_TM, PLAT_TB].includes(platform)) {
		let tbSecurity = await getTbSecurity();
		let successObj = {};
		let tid = pack.tid;
		// let tid = togetherId?.split('|')[0];
		console.log('tbSecurity', tbSecurity, togetherId, decryptArr, pack);
		// 针对先拆单后合单的情况进行处理
		let res:any = await tbSecurity.decrypt({
			tid,
			oaid: decryptArr[0].caid,
			sellerId: decryptArr[0].sellerId,
			pack,
			type,
			decryptLocation
		});
		if (res?.receiver_list?.[0]) {
			const _res = res?.receiver_list?.[0];
			successObj[togetherId] = {
				receiverName: _res?.name,
				receiverPhone: _res?.mobile,
				receiverAddress: _res?.address_detail
			};
			if (type) {
				successObj[togetherId][`${type}IsDecrypt`] = _res[TbDecryptFiledMapEnum[type]];
				// 单个解密只有解密手机号会返回 隐私号过期时间
				if (type === "encodeMobile") {
					successObj[togetherId]["secret_no_expire_time"] = _res?.secret_no_expire_time;
				} else {
					successObj[togetherId]["secret_no_expire_time"] = pack?.secret_no_expire_time;
				}
			} else {
				successObj[togetherId] = {
					receiverName: _res?.name,
					receiverPhone: _res?.mobile,
					receiverAddress: _res?.address_detail,
					[`${DecryptFiledEnum['地址']}IsDecrypt`]: _res?.address_detail,
					[`${DecryptFiledEnum['手机号']}IsDecrypt`]: _res?.mobile,
					[`${DecryptFiledEnum['收件人']}IsDecrypt`]: _res?.name,
					secret_no_expire_time: _res?.secret_no_expire_time
				};
			}
		}
		// 解密失败
		if (res?.error_response) {
			Modal.error({
				centered: true,
				title: '系统提示',
				content: (<div dangerouslySetInnerHTML={ { __html: res.error_response.sub_msg || res.error_response.msg || '解密异常' } } />),
				width: 580,
				okText: '确定'
			});
		}
		return { successObj };
	} else if (platform === PLAT_JD) {
		console.log('jdSecurity', togetherId, decryptArr, pack);
		const encodeTid = pack.decodePtTid || decryptArr[0].ptTid || decryptArr[0].tid;
		const oaid = pack?.caid;
		const res:{receiverName?: string, receiverMobile?: string, receiverAddress?: string, pin?: string } = await jdDecrypt({ encodeTid, sellerId, oaid});
		let successObj = {};
		if (res) {
			successObj[togetherId] = {
				receiverName: res?.receiverName,
				receiverPhone: res?.receiverMobile,
				receiverAddress: res?.receiverAddress,
				buyerNick: res?.pin
			};
		}

		return { successObj };
	}
}

const jdDecrypt = async({ encodeTid, sellerId, oaid}:{encodeTid:string, sellerId: string, oaid:string}) => {
	let list = await userStore.getShopList();
	let token = "";
	for (let item of list) {
		if (item.sellerId === sellerId) token = item.token;
	}
	if (!token) {
		console.log('没有token');
		return;
	}

	let params:any = {
		apiName: "getSign",
		token,
		orderNos: encodeTid,
	};
	if(oaid){
		params.extendProps = {        
			scenesType: "1003" 
		}
	}
	const res = await TradeHufuGetSignApi(params);
	const { authorization, body, url, xJdcloudDate, xJdcloudNonc } = res;

	let decryptRes;
	let operateResult = false;
	// 京东虎符解密
	try {
		decryptRes = await axios({
			method: 'post',
			baseURL: url,
			headers: {
				'x-jdcloud-date': xJdcloudDate || '',
				'x-jdcloud-nonce': xJdcloudNonc || '',
				'Authorization': authorization || '',
				'Content-Type': 'application/json'
			},
			data: JSON.parse(body),
		});
	// eslint-disable-next-line no-empty
	} catch (error) { }

	let decryptObj = {};
	if (decryptRes?.data?.orderSensitiveInfo?.orderList?.[0]) {
		decryptObj = decryptRes.data.orderSensitiveInfo.orderList[0];
		operateResult = true;
	}
	SystemOperateLogUploadApi({ operateLogType: 'TRADE_DECRY_DATA', operateResult, tid: encodeTid });
	return decryptObj;
};

function clearDecrypt() {
	decryptTrades = {};
}

export {
	decryptFn,
	clearDecrypt,
	jdDecrypt
};

export enum DecryptFiledEnum {
	'收件人' = 'encodeReceiverName',
	'手机号' = 'encodeMobile',
	'地址' = 'encodeReceiverAddress',
	'全部' = '',
}

export enum DecryptFiledMapEnum {
	'encodeReceiverName' = 'receiverName',
	'encodeMobile' = 'mobile',
	'encodeReceiverAddress' = 'receiverAddress',
}

export enum TbDecryptFiledMapEnum {
	'encodeReceiverName' = 'name',
	'encodeMobile' = 'mobile',
	'encodeReceiverAddress' = 'address_detail',
}
