import { Input, Popover, Tooltip } from 'antd';
import React, { memo, useEffect, useState, useMemo } from 'react';
import { observer } from 'mobx-react';
import _, { cloneDeep } from "lodash";
import { EditFilled } from '@ant-design/icons';
import { tradeStore } from '@/stores';
import Icon from '@/components/Icon';
import { ExpressReachIconMap } from '@/pages/Trade/constants';
import s from '../../../index.module.scss';
import { IPackage } from '@/pages/Trade/interface';
import { PLAT_DW, PLAT_SPH, PLAT_XHS, PRINT_MAP } from '@/constants';
import message from "@/components/message";
import { TradeStatus } from '@/utils/enum/trade';
import { dealPlatAsHandPlat, transferOtherToHand } from '@/components-biz/ShopListSelect/shopListUtils';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';


const expressInfoNode = (isReach, expressCompanyMap) => {
	const expressInfoList = isReach?.logisticsRecommendedDeliveryVo?.expressInfoList?.filter(i => i.isDeliverable);

	const platItem = expressInfoList?.[0];
	if (!platItem) return;

	const detailNode = (platItem) => {
		const { avgCostHours, optimizedPercent, levelPercent } = platItem?.collectSignInfo ?? {};

		return (
			<>快递可达，
				{avgCostHours ? <>揽签时长<span style={ { color: 'orange' } }>{avgCostHours}</span>小时，</> : ''}
				{optimizedPercent ? `下降${optimizedPercent}%，` : '' }
				{levelPercent ? <>预估超过<span style={ { color: 'green' } }>{parseFloat(levelPercent).toFixed(2)}</span>%物流</> : '' }
			</>
		);
	};

	const detailDetailNode = (platItem) => {
		const { avgCostHours, optimizedPercent, levelPercent } = platItem?.collectSignInfo ?? {};

		return (
			<>快递可达，
				{avgCostHours ? <>揽件时长<span style={ { color: 'orange' } }>{avgCostHours}</span>小时，</> : ''}
				{optimizedPercent ? `下降${optimizedPercent}%，` : '' }
				{levelPercent ? <><br />预估超过<span style={ { color: 'green' } }>{parseFloat(levelPercent).toFixed(2)}</span>% 同路线其他物流</> : '' }
			</>
		);
	};

	const styleObj = {
		width: '84px', marginRight: '5px'
	};
	const pMoreContent = (
		<div>
			<div className="r-flex">
				<div style={ { ...styleObj, color: 'orange' } }>平台推荐</div>
				<div className="r-flex-1">平台将对快递质量进行升级管控，建议使用平台快递，提升店铺评分降低售后</div>
			</div>
			{
				expressInfoList.map(expressItem => {
					return (
						<div className="r-flex">
							<div style={ styleObj }>{expressCompanyMap[expressItem.express] ?? expressItem.express}</div>
							<div className="r-flex-1">{detailDetailNode(expressItem)}</div>
						</div>
					);
				})
			}

		</div>
	);


	return (
		<div style={ { width: 130, fontSize: '12px' } }>
			平台推荐：{expressCompanyMap[platItem.express] ?? platItem.express}
			{ expressInfoList.length > 1 ? (
				<>
					<Popover
						content={ pMoreContent }
						overlayInnerStyle={ { width: 390, height: 300, overflow: 'auto' } }
					>
						<span className="r-click r-ml-5" style={ { position: 'absolute' } }>更多</span>

					</Popover>
					<br />
				</>
			) : ''}
			{detailNode(platItem)}
		</div>
	);
};

export interface IExpressnumComProps {
    pack: IPackage,
	colWidth: number | string;
}

const ExpressnumCom = (props: IExpressnumComProps) => {
	const { pack, colWidth } = props;

	const {
		tradeListStore: { togglePackageSelectStatus },
		setting,
		selectedTemp,
		expressCompanyList,
		expressTemplateList,
		smartExpressSwitch,
		smartExpressList,
		kddTempList,
		selectedTempGroup,
		isMergePrint,
		tradePrintStore: { showRecycleElecNo },
		setEditSidModalProps,
		setIsShowEditSidModal
	} = tradeStore;

	const [smartExpressObj, setSmartExpressObj] = useState({});

	const inputValue = () => {
		if ((isMergePrint ? selectedTempGroup.id : selectedTemp.Mode_ListShowId) && pack.isChecked) {
			return pack.sids ? pack.sids[0] : '打印后生成';
		} else {
			return '';
		}
	};
	const isHandTempSid = useMemo(() => {
		const curExid = isMergePrint ? +selectedTempGroup.id : +selectedTemp.Exid;
		return curExid == -901;
	}, [isMergePrint, selectedTemp.Exid, selectedTempGroup.id]);

	const sidsExName = () => {
		if (isHandTempSid && pack.isChecked) {
			return pack.sidsExNames?.length ? pack.sidsExNames[0] : '';
		} else {
			return '';
		}
	};
	const onEditSid = () => {
		sendPoint(Pointer.订单_手写单号_图标);

		if ([PLAT_DW].includes(pack.platform)) {
			message.warn('得物订单不支持手写单号');
			return; 
		}

		if (pack.productOrders?.every(order => !order.isChecked)) {
			message.warn('请勾选订单下商品后进行手写单号');
			return;
		}
		setIsShowEditSidModal(true);
		setEditSidModalProps(pack);

	};
	const memoizedValue = useMemo(() => {
		if (isMergePrint) {
			let handPlatSetting = setting?.groupPrintSetJsonString?.orderMatchSetting || [];
			let orderTemp = selectedTempGroup?.userTemplateList?.find((o:any) => {
				if (dealPlatAsHandPlat(pack.platform, null, pack)) {
					let orderSettingType = handPlatSetting.find(o => o.platform === transferOtherToHand(pack.platform, pack));
					return o.expressType == orderSettingType.bindControlType;
				}
				if (PRINT_MAP[pack.platform] == 13) {
					return [13, 16].includes(o.expressType);
				} else {
					return o.expressType == PRINT_MAP[pack.platform];
				}
			});
			if (orderTemp && window.printAPI?.isRecyclable) {
				return window.printAPI.isRecyclable({
					kddType: orderTemp?.expressType,
					exCode: orderTemp?.exCode,
				});
			} else {
				return false;
			}
		} else {
			return showRecycleElecNo;
		}
	}, [selectedTempGroup, selectedTemp, isMergePrint]);
	// TODO 后续需优化传入的数据，将”打印后生成“等默认数据去掉
	const ydNo = pack.sids?.filter((i) => i !== '打印后生成') || [];
	const pYdNo = pack.pYdNo;

	const isReach = {
		...pack._reachItem,
		...ExpressReachIconMap[pack._canReach],
	};

	/**
	 * 能否回收
	 */
	const isZyExcode = (temp: any) => {
		let exCodeArr = ['3108002701_1011', 'BESTQJT', 'YMDD'];
		return temp.exType == 1 && !(exCodeArr.includes(temp.ExCode) && temp.KddType == 3);
	};
	const canRecycleElecNo = !isZyExcode(selectedTemp) && memoizedValue && ydNo.length > 0;

	/**
	 * 回收操作
	 */
	const recycleElecNo = async() => {
		try {
			let params = null;
			if (isMergePrint) {
				let handPlatSetting = setting?.groupPrintSetJsonString?.orderMatchSetting || [];
				let orderTemp = selectedTempGroup?.userTemplateList?.find((o:any) => {
					if (dealPlatAsHandPlat(pack.platform, null, pack)) {
						let orderSettingType = handPlatSetting.find(o => o.platform === transferOtherToHand(pack.platform, pack));
						return o.expressType == orderSettingType.bindControlType;
					}
					if (PRINT_MAP[pack.platform] == 13) {
						return [13, 16].includes(o.expressType);
					} else {
						return o.expressType == PRINT_MAP[pack.platform];
					}
				});
				params = {
					kdCode: orderTemp.exCode,
					kddType: orderTemp.expressType,
					exid: orderTemp.exId,
					kdId: orderTemp.exId,
				};
			} else {
				params =	{
					kdCode: selectedTemp.ExCode,
					kddType: selectedTemp.KddType,
					exid: selectedTemp.Exid,
					kdId: selectedTemp.Exid,
				};
			}
			console.log("recycleWaybillCode:", {
				pYdNo_sids: pack.pYdNo_sids,
				ydNo: ydNo.join(","),
				taobaoId: pack.sellerId,
				recycleAll: true,
				tids: pack.tids,
				pYdNo,
				...params
			});
			let res = await window.printAPI.recycleWaybillCode({
				ydNo: ydNo.join(","),
				taobaoId: pack.sellerId,
				recycleAll: true,
				tids: pack.tids,
				pYdNo,
				...params
			});
			if (res) {
				tradeStore.tradeOptStore.recycleElecNo(pack, res, pYdNo ? pack?.pYdNo_sids : []);
			}
		} catch (e) {
			message.error(`回收失败：${e?.data || e?.errorMessage || '未知错误'}`);
			console.log('window.printAPI.recycleWaybillCode', e);
		}
	};


	const expressCompanyMap = useMemo(() => {
		let nameMap = {};
		expressCompanyList.forEach((item) => {
			nameMap[item.exCode] = item.exName;
		});
		return nameMap;
	}, [expressCompanyList]);

	const getSmartContent = () => {

		const { smartExpressTemplate, packSmartExpressTemplate, isMerge } = pack;
		try {
			if ((smartExpressTemplate || packSmartExpressTemplate) && smartExpressSwitch) {
				const smartInfo = JSON.parse(isMerge ? packSmartExpressTemplate : smartExpressTemplate);
				const { printTemplateId, isManual, printTemplateName, hasCleared } = smartInfo;
				const smartExpressInfo = smartExpressObj[printTemplateId];
				// 如果智选快递模板已经删除了
				if (hasCleared === 'true') {
					return;
				}
				let showIcon = true;
				// 如果是手动指定的快递，不展示图标
				if (isManual == "true") {
					showIcon = false;
				}
				return (
					<div className={ `r-mb-5 r-flex r-ai-c ${isReach?.icon && !isHandTempSid ? 'r-ml-16' : ''}` }>
						{
							showIcon && (
								<Tooltip title="智选快递">
									<div className="r-mr-8 r-trade-smart-express">智</div>
								</Tooltip>
							)
						}
						{smartExpressInfo?.ExcodeName || printTemplateName}
					</div>
				);
			}
		} catch (e) {
			console.log(e);
		}
	};

	useEffect(() => {
		const smartExpressObj = {};
		expressTemplateList.forEach(i => {
			smartExpressObj[i.Mode_ListShowId] = i;
		});
		setSmartExpressObj(smartExpressObj);
	}, [expressTemplateList]);

	return (
		<div style={ { width: colWidth } } className="batch_tb_expressnum batch_tbtlt_express r-flex r-jc-c r-as-s r-ai-c" onClick={ (e) => { e.stopPropagation(); } }>
			<div className="express_input_wrap">
				{getSmartContent()}
				<div className="r-flex r-ai-c">
					<Popover
						content={ `${isReach?.title}${isReach?.undeliveredReason ? '，原因：' : ''}${isReach?.undeliveredReason || ''}` }
					>
						<span className={ s['express-reach-wrap'] }>
							{isReach?.icon && !isHandTempSid ? <Icon type={ isReach.icon } size={ 16 } style={ { color: isReach.color } } svg={ !!isReach.svg } /> : ''}
						</span>
					</Popover>

					<Input size="small" className="express_input" style={ { width: 130, fontSize: '12px' } } type="text" value={ (inputValue()) } disabled />
					{
						sidsExName() ? (
							<Tooltip title={ sidsExName() }>
								<span style={ { background: '#fd8204', color: '#fff', padding: '2px' } }>导</span>
							</Tooltip>
						) : null
					}
					{
						isHandTempSid
						&& !pack.pOrderUnfinished
						&& !pack.platformStockOut
						&& pack.trades.some(trade => [TradeStatus.卖家部分发货, TradeStatus.等待卖家发货, TradeStatus.等待买家确认收货, TradeStatus.交易成功].includes(trade.status as TradeStatus))
							? (<EditFilled onClick={ onEditSid } className="r-c-999 r-fs-14 r-ml-5" />)
							: ''
					}
					{(canRecycleElecNo && pack.isChecked) && <div className="batch_set_icon recycle_elec_no " onClick={ recycleElecNo } /> }
				</div>
			</div>
			<div className="express_yd_no" style={ { width: "180px", paddingLeft: isReach?.icon && !isHandTempSid ? 18 : 3 } }>
				{pack.isChecked && ydNo?.length > 1 && setting.showMultiYdNo === 2 && inputValue() != '' && (
					<div className="express_show_more_wrap" style={ { marginLeft: "0" } }>
						<div className="express_show_more_scroll">
							{ydNo.slice(1, ydNo.length).map((i) => <p key={ i }>{i}</p>)}
						</div>
					</div>
				)}
				<div style={ { textAlign: 'left', width: "140px" } }>{sidsExName()}</div>
			</div>
			<div style={ { width: "180px", paddingLeft: isReach?.icon && !isHandTempSid ? 18 : 3 } }>
				{ expressInfoNode(isReach, expressCompanyMap) }
			</div>

		</div>
	);
};

export default observer(ExpressnumCom);
