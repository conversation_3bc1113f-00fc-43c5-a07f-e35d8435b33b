import React, { Fragment, memo, useEffect, useImperativeHandle, useMemo, useState } from "react";
import { Button, Dropdown, Image, Menu, Modal, Popover, Tooltip, Spin, Alert, Input } from "antd";
import { observer } from "mobx-react";
import { CSSProperties } from "styled-components";
import { runInAction } from "mobx";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { debounce } from "lodash";
import cs from "classnames";
import { TRADE_TYPE_ENUM, PAY_DISPLAY_TYPE_ENUM, PAY_DISPLAY_TYPE_ICON_MAP, PAY_DISPLAY_TYPE_COLOR_MAP } from "@/pages/Trade/constants";
import s from '@/pages/Trade/index.module.scss';
import { IOrders, IPackage, ISubTrade } from "@/pages/Trade/interface";
import { copyMenu, getTradeFlag, getTradeFlagTag, getTradePlatformLabel, isOrderDisabled, isPackCheckDisable, scmPackTips, getPayDisplayTypeText } from "@/pages/Trade/utils";
import Icon from "@/components/Icon";
import WaresInfo from "@/components-biz/WaresInfo";
import { tradeStore, handStore, editGoodsStore } from "@/stores";
import userStore from "@/stores/user";
import { copyToPaste, splitFxgTid } from "@/utils";
import { filterPrintContent } from "@/utils/trade/printContent";
import tradeSetStore from "@/stores/trade/tradeSet";
import CombineTag from "@/components-biz/Trade/CombineTag";
import { PendingStatus, TradeStatus, IgnoreStatus } from '@/utils/enum/trade';
import { weightUnit } from "@/pages/Index/Settings/System/constants";
import { stockVersion, PLAT_HAND, GIFT_NORMAL_PLATFORM, PLAT_SCM, AFTERSALE_HANDORDER, DEFAULT_IMG, PLAT_SCMHAND, PLAT_TB, PLAT_TM } from "@/constants";
import { IndexPlatformShopGetPlatformShopsResponseObj } from "@/types/schemas/user";
import { getShopName, isSourceHand, isSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import { UpdateOrderSerialNumberApi } from "@/apis/trade";
import message from "@/components/message";
import BatchModifyLocalMemoPicModal from "../../BatchModifyLocalMemoPicModal";
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { SysExchangeTypeEnum } from '@/pages/Trade/components/BottomCom/constants';
import { getSysExchangeTypeEnumText } from '@/pages/Trade/components/BottomCom/utils';

export const OrderNumberCom = observer(
	(props: {
		pack: IPackage,
		colWidth: number | string;
	}) => {
		const { pack: { totalGoodsNum, totalOrderNum }, colWidth } = props;
		const { tradeListStore: { togglePackageSelectStatus } } = tradeStore;
		return (
			<div style={ { width: colWidth } } className="r-flex r-fd-c r-as-s r-jc-c">
				<div className={ `batch_tbtlt_num r-fs-14 ${totalOrderNum > 1 ? "highFontColor" : ""}` }>
					{totalOrderNum}
				</div>
				{totalGoodsNum > 1 ? <div className="r-fs-14">({totalGoodsNum}种)</div> : ""}
			</div>
		);
	}
);

export const CheckboxCom = observer((props: {
	pack: IPackage,
	index: number
}) => {
	const { pack: { isChecked }, index } = props;

	const isCheckDisable = isPackCheckDisable(props.pack);

	return (
		<div
			className="batch_tbtlt_check r-as-s"
			style={ { display: "inline-flex", alignItems: "center" } }
			onClick={ (e) => {
				tradeStore.tradeListStore.dealShift({ pack: props.pack, index });
				e.stopPropagation();
			} }
		>
			<input
				type="checkbox"
				disabled={ isCheckDisable }
				style={ { pointerEvents: isCheckDisable ? 'none' : 'auto' } }
				checked={ isChecked }
				readOnly
			/>
		</div>

	);
});

export const IndexCom = memo((props: {index: number}) => {
	const { index } = props;
	return (
		<div className="batch_tbtlt_index">{index + 1}</div>
	);
});

export const PayTimeCom = memo((props: { payTime: string, colWidth: number | string }) => {
	const { payTime, colWidth } = props;
	return <div style={ { width: colWidth, paddingRight: '3px' } } className="batch_tbtlt_regimentTime">{payTime}</div>;
});

export const CreatedTimeCom = memo((props: { createdTime: string, colWidth: number | string; }) => {
	const { createdTime, colWidth } = props;
	return (
		<div style={ { width: colWidth, paddingRight: '3px' } } className={ `batch_tbtlt_deliveryTime ` }>
			{createdTime}
		</div>
	);
});

export const TotalReceivedPaymentCom = memo(
	(props: { totalReceivedPayment: string; colWidth: number | string; }) => {
		const { totalReceivedPayment, colWidth } = props;
		return (
			<div style={ { width: colWidth } } className="align_c batch_tbtlt_totalPayment">
				<div className="total_payment">{totalReceivedPayment}元</div>
			</div>
		);
	}
);

export const TotalPaymentCom = memo(
	(props: {trades: any[], platform:string, serviceTagList:string[], totalPrePostFee:string, totalRefundPostFee:string, totalPayment: string; totalPostFee: string, sfExpressFee:string, hasSfExpressService:boolean, colWidth: number | string; paymentSensitive:boolean }) => {
		const { trades, totalPayment, totalPostFee, sfExpressFee, hasSfExpressService, colWidth, paymentSensitive = false, platform, serviceTagList = [], totalPrePostFee, totalRefundPostFee } = props;
		return (
			<div style={ { width: colWidth } } className="align_c batch_tbtlt_totalPayment">
				{
					!paymentSensitive ? (
						<>
							<div className="total_payment">{totalPayment}元</div>
							{
								[PLAT_TB, PLAT_TM].includes(platform) && serviceTagList?.includes('presentOrder')
									? <div className="post_fee"><div>(含预付运费{totalPrePostFee}元,</div><div>退运费差价{totalRefundPostFee}元,</div><div>实际运费{totalPostFee}元)</div></div>
									: <div className="post_fee">(含运费:{totalPostFee}元)</div>
							}
							{
								platform === PLAT_TM || platform === PLAT_TB ? (
									<div className="r-flex" style={ { flexWrap: 'wrap' } }>
										{
											trades?.map((trade, index) => {
												return (
													trade.payDisplayType ? (
														<Tooltip title={ getPayDisplayTypeText(trade.payDisplayType, trade.payDisplayTypeValue) }>
															<div key={ index } className="r-mr-5">
																<Icon style={ { color: PAY_DISPLAY_TYPE_COLOR_MAP[trade.payDisplayType] } } type={ PAY_DISPLAY_TYPE_ICON_MAP[trade.payDisplayType] } className="r-mr-5" />
															</div>
														</Tooltip>
													) : null
												);
											})
										}
									</div>
								) : null
							}
						</>
					) : null
				}
				{hasSfExpressService ? <div className="post_fee">顺丰加价服务费:{sfExpressFee}元</div> : null}
			</div>
		);
	}
);

export const TotalWeightCom = memo((props: { totalWeight: string, colWidth: number | string; }) => {
	const { totalWeight, colWidth } = props;
	return (
		<div style={ { width: colWidth } } className="batch_tbtlt_totalWeight align_c">
			{/* {totalWeight || "0"}g */}
			{
				userStore?.userSetting?.weightUnit === weightUnit.显示kg
					? Number(((+totalWeight || 0) / 1000).toFixed(6)) + 'kg'
					: Number(+totalWeight || 0).toFixed(3) + 'g'
			}
		</div>
	);
});

// 系统单号
export const TidCom = memo(
	(props: { pack: IPackage, colWidth: number | string }) => {
		const { tids: tradeId, platform } = props.pack;
		const { colWidth } = props;
		// const { tradeListStore: { togglePackageSelectStatus } } = tradeStore;
		const copyInfo = () => {
			copyToPaste(splitFxgTid(tradeId?.[0].split('|')?.join(',')));
		};
		return (
			<div
				style={ { width: colWidth } }
				className="batch_tbtlt_tradeId r-flex r-fd-c  r-jc-c"
				onClick={ (e) => { e.stopPropagation(); } }
			>
				<Dropdown placement="topCenter" overlay={ copyMenu(copyInfo) }>
					<div>
						{tradeId?.[0].split('|').map((tid: string) => (
							<div key={ `${platform}-${tid}` }>{tid}</div>
						))}
					</div>
				</Dropdown>
			</div>
		);
	}
);

// 订单编号
export const PtTidCom = memo(
	(props: { pack: IPackage, colWidth: number | string }) => {
		const { ptTids: tradeId, platform } = props.pack;
		const { colWidth } = props;
		// const { tradeListStore: { togglePackageSelectStatus } } = tradeStore;
		const copyInfo = (e) => {
			copyToPaste(splitFxgTid(tradeId?.[0].split('|')?.join(',')));
		};
		return (
			<div
				style={ { width: colWidth } }
				className="batch_tbtlt_tradeId r-flex r-fd-c r-jc-c"
				onClick={ (e) => { e.stopPropagation(); } }
			>
				<Dropdown placement="topCenter" overlay={ copyMenu(copyInfo) }>
					<div>
						{tradeId?.[0].split('|').map((ptTid: string) => (
							<div key={ `${platform}-${ptTid}` }>{ptTid}</div>
						))}
					</div>
				</Dropdown>
			</div>
		);
	}
);

export const SourceCom = memo(
	(props: { pack: IPackage, colWidth: number | string }) => {
		const { colWidth, pack } = props;
		const tradeSourceText = useMemo(() => {
			let text = '平台订单';
			if (isSourceScm(pack)) {
				text = '分销推送';
			} else if (isSourceHand(pack) || pack.source == AFTERSALE_HANDORDER) {
				text = '手工订单';
			}
			return text;
		}, [pack]);
		return (
			<div
				style={ { width: colWidth } }
				className="batch_tbtlt_tradeId r-flex r-fd-c r-as-s r-jc-c"
			>
				{tradeSourceText}
			</div>
		);
	}
);

// 达人信息
export const AuthorInfoCom = memo(
	(props: { pack: IPackage, colWidth: number | string }) => {
		const { colWidth, pack } = props;
		// 找出有达人信息的
		const filteredTrades = pack?.trades?.filter(trade => trade.isAuthorOrder) || [];

		const authorsMap = new Map();
		filteredTrades.forEach(trade => {
			trade.orders.forEach(order => {
				authorsMap.set(order.authorId, { authorId: order.authorId, authorName: order.authorName });
			});
		});

		const authors = Array.from(authorsMap.values());

		// authorsMap.clear();

		// console.log('%c [ 达人信息 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', authors);
		return (
			<div
				style={ { width: colWidth } }
				className="batch_tbtlt_authorInfo r-flex r-fd-c r-as-s r-jc-c r-l-preWrap"
			>
				{authors.length > 0 && (
					<div className="r-flex">
						{authors.length > 1 ? (
							<Popover
								trigger="hover"
								placement="topLeft"
								arrowPointAtCenter={ false }
								mouseEnterDelay={ 0.2 }
								// getPopupContainer={ triggerNode => triggerNode.parentElement }
								content={ (
									<div>
										{authors.map((author, index) => (
											<div key={ author.authorId } className="r-mb-4 r-flex r-ai-fs">
												<div className="r-mr-6 r-lh-16">{index + 1}. </div>
												<div className="r-flex r-fd-c r-lh-16">
													<span>名称：{author.authorName}</span>
													<span>ID：{author.authorId}</span>
												</div>
											</div>
										))}
									</div>
								) }
							>
								<div className="r-flex r-ai-c">共 <span className="r-c-warning">{authors.length}</span> 个达人</div>
							</Popover>
						) : (
							<div className="r-flex r-fd-c r-lh-16">
								<span>名称：{authors[0].authorName}</span>
								<span>ID：{authors[0].authorId}</span>
							</div>
						)}
					</div>
				)}
			</div>
		);
	}
);

export const DistributorCom = memo(
	(props: { pack: IPackage, colWidth: number | string }) => {
		const { colWidth, pack } = props;

		const onCopyMobile = () => {
			copyToPaste(`分销商名称：${pack.distributorName}； \n 备注：${pack.distributorRemark || ''}； \n 联系人：${pack.distributorLinkMan}； \n 电话：${pack.distributorMobile}`);
		};

		const content = (
			<div className="r-fs-14 r-lh-30" style={ { minWidth: 200, maxWidth: 300, wordBreak: 'break-all' } }>
				<div className="r-bold r-mb-10">商家信息</div>
				<div>分销商名称：{pack.distributorName}</div>
				<div>备注：{pack.distributorRemark || ''}</div>
				<div>联系人：{pack.distributorLinkMan || ''}</div>
				<div className="r-flex r-ai-c">
					电话：{pack.distributorMobile}
					<span className="r-click r-ml-10" onClick={ onCopyMobile }>复制</span>
				</div>
			</div>
		);

		return (
			<div
				style={ { width: colWidth } }
				className="batch_tbtlt_tradeId r-flex r-fd-c r-as-s r-jc-c"
			>
				{
					pack.distributorUserId ? (
						<div onClick={ (e) => { e.stopPropagation(); } }>
							<div>{pack.distributorMobile}</div>
							<Popover content={ content } title="" trigger="click">
								<div className="r-flex r-ai-c r-mt-5 r-click">
									<Icon type="lianxishangjia" className="r-mr-5" style={ { color: '#1890FF' } } />
									联系商家
								</div>
							</Popover>
						</div>
					) : ''
				}


			</div>
		);
	}
);


export const TradeFromCom = memo(observer(
	(props: { pack: IPackage, colWidth: number | string, shopObj:IndexPlatformShopGetPlatformShopsResponseObj}) => {
		const { sellerNick, platform, sellerId } = props.pack;
		const { colWidth, shopObj = {} } = props;
		const { tradeListStore: { togglePackageSelectStatus }, isShowShopName } = tradeStore;
		const getShopNameText = () => {
			let _sellerNick = getShopName({ plat: platform, sellerNick });
			// 手工单 展示无店铺 简称也是无店铺
			if (!isShowShopName && platform !== PLAT_HAND) {
				_sellerNick = shopObj[sellerId]?.sellerAbbreviation;
			}
			return _sellerNick;
		};
		return (
			<div
				style={ { width: colWidth } }
				className="align_c batch_tbtlt_tradeFrom"
				// onClick={ () => { togglePackageSelectStatus(props.pack); } }
			>
				{getTradePlatformLabel(platform)}
				<div className="r-ml-5">
					<div>{isShowShopName ? !userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(props.pack) ? '****' : getShopNameText() : getShopNameText()}</div>
				</div>
			</div>
		);
	}
));


export const TradeType = memo((props: {packTotalType: string, colWidth}) => {
	const { packTotalType, colWidth } = props;
	const validTradeTypeList = Object.values(TRADE_TYPE_ENUM);
	const curTradeTypeList = packTotalType?.split(",");
	const curTradeShowType = [];
	if (curTradeTypeList) {
		curTradeTypeList.forEach(type => {
			if (validTradeTypeList.includes(type)) {
				curTradeShowType.push(TRADE_TYPE_ENUM[type]);
			}
		});
	}
	return (
		<div style={ { width: colWidth, minWidth: 80, } } className="r-flex r-fd-c r-as-s r-jc-c">{curTradeShowType.join(',') || '普通订单'}</div>
	);
});


// 方法抽离，复制订单与订单显示
export const getStockDetailContent = (order) => {
	const stockType = {
		stockpileNum: "囤",
		stockOutNum: "缺"
	};
	const showContent = [];
	Object.keys(stockType).forEach(type => {
		if (order[type] > 0) {
			showContent.push(`${stockType[type]}:${order[type]}`);
		}
	});
	if (showContent.length > 0) {
		return <span style={ { color: '#f00' } }>({showContent.join('，')})</span>;
	}
	return null;
};

export const ProductOrderFragment = observer((
	(props: {
		productOrders: IOrders[];
		handleOrderChange?: any;
		pack: any;
		isCancelSplit?:boolean
		itemStyle?: CSSProperties;
		showSelect?: boolean;
		fullWidth?: boolean;
		pageFrom?: string;
	}) => {
		const {
			productOrders,
			pack,
			isCancelSplit,
			itemStyle = {},
			showSelect = false,
			pageFrom = null,
			fullWidth = false,
		} = props;

		const {
			productSetting: {
				showColumn,
				showPicture,
				showSysPicPath,
				showStockWarn,
				showStockAllocation,
				showPartConsignItems,
				showItemTitle,
				showOuterId,
				showShortTitle,
				showSkuAlias,
				showSkuOuterId,
				showSkuTitle,
				showSysOuterId,
				showSysOuterSkuId,
				showGoodsSelect,
				showSysGoodsStockBox
			},
			tradeOptStore: {
				handleOrderCheck,
				handleProductOrders
			},
			isAllExpanded,
		} = tradeStore;
		const { isShowZeroStockVersion } = userStore;
		const toggleSubOrderItem = async(e: any, tradeIndex: number, orderIndex: number, order: any) => {
			handleOrderCheck(pack, tradeIndex, orderIndex);
			handleProductOrders(pack);
		};
		// 控制当前包裹是否展开所有产品
		const [isExpanded, setIsExpanded] = useState(false);
		// 监听全局展开状态变化
		useEffect(() => {
			setIsExpanded(isAllExpanded);
		}, [isAllExpanded]);

		// 计算要显示的产品数量
		const displayOrders = useMemo(() => {
			if (!productOrders) return [];
			if (isExpanded) return productOrders;
			// 不展开时只显示第一排产品（根据showColumn决定显示几个）
			return productOrders.slice(0, showColumn || 1);
		}, [productOrders, isExpanded, showColumn]);

		// 是否有更多产品未显示
		const hasMoreOrders = useMemo(() => {
			return productOrders && productOrders.length > (showColumn || 1);
		}, [productOrders, showColumn]);

		return (
			<>
				{
					productOrders ? (
						<>
							{displayOrders.map((order, oIndex: number) => (
								+order.numIid === -1 ? (
									<div className={ `r-flex product-order-item ${!isCancelSplit && showColumn === 2 ? 'two-col' : 'one-col'}` } key={ order.oid }>
										<div className="r-relative" style={ { height: 48 } }>
											<Image
												width={ 48 }
												height={ 48 }
												src={ DEFAULT_IMG }
												preview={ false }
												fallback={ DEFAULT_IMG }
												className={ cs(s['image-container'], 'r-pointer') }
												loading="lazy"
											/>
											{
												order.dropShipping ? (
													<div className="order-error-label">代发</div>
												) : ''
											}
										</div>
										<div className="r-ml-5">(无商品)</div>
									</div>
								) : (order.isHideFinishedGoods || order.isHideClosedGoods || order.isHideRefundingGoods || order.isHideDropShippingGoods || order.isHideByPartShip)
									? ""
									: (
										<label
											className={ `r-flex product-order-item ${fullWidth ? 'one-col' : showColumn === 2 ? 'two-col' : 'one-col'}` }
											key={ order.oid }
											style={ itemStyle }
										>
											{
												showSelect && showGoodsSelect ? order.dropShipping ? (
													<Tooltip title="分销代发商品不可勾选，如需操作，需前往待发订单撤销推单。" trigger="hover">
														<div style={ { width: 20, height: 20 } }>
															<Input
																type="checkbox"
																className="r-mr-10"
																style={ { width: 16, marginTop: 2 } }
																checked={ order.isChecked }
																disabled
																onChangeCapture={ (e) => { toggleSubOrderItem(e, order.tradeIndex, order.orderIndex, order); } }
															/>
														</div>
													</Tooltip>
												) : (
													<div style={ { width: 20, height: 20 } }>
														<Input
															type="checkbox"
															className="r-mr-10"
															style={ { width: 16, marginTop: 2 } }
															checked={ order.isChecked }
															disabled={ isOrderDisabled(order, productOrders, pack?.serviceTagList) }
															onChangeCapture={ (e) => { toggleSubOrderItem(e, order.tradeIndex, order.orderIndex, order); } }
														/>
													</div>
												) : ""
											}

											{showPicture || (!isShowZeroStockVersion && showSysPicPath) ? (
												<div
													className="r-relative"
													onContextMenu={ (e) => {
														e.stopPropagation();
													} }
												>
													<WaresInfo
														orderInfo={ order }
														isShowHoverName
														isShowHoverStock
														wareName={ order.title }
														skuName={ order.skuPropertiesName }
														skuNum={ order.num }
														onlyImg
														imgUrl={ showPicture ? order.picPath : order.sysPicPath }
														linkDetailId={ !order?.itemLinkSensitive && showPicture ? order.numIid : null }
														linkDetailPlatForm={ showPicture ? pack?.platform : null }
														banJump={ !userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(pack) }
														previewPicSize={ tradeStore.productSetting?.customSetJson?.previewPicSize }
													/>
													{showStockWarn && userStore?.inventoryDeduct && order.isCheckedStock && !isCancelSplit ? (
														<>
															{order.noGoods && !order?.ignore ? <div className="order-no-goods-label">缺货</div> : ''}
															{order.goodsWarn && !order?.ignore ? <div className="order-warning-goods-label">警戒</div> : ''}
														</>
													) : ''}


													{showStockAllocation && userStore?.inventoryDeduct && order.isCheckedStock && !isCancelSplit ? (
														<>
															{order.noGoods && !order?.ignore ? <div className="order-no-goods-label">缺货</div> : ''}
														</>
													) : ''}

													{
														order.dropShipping && !isCancelSplit ? (
															<div className="order-error-label">代发</div>
														) : ''
													}
													{



														showPartConsignItems
														&& pack.isPartShipped
														&& (TradeStatus.交易成功 == order.status || TradeStatus.等待买家确认收货 == order.status || order.status == TradeStatus.卖家部分发货)
														&& !isCancelSplit
															? (<div className="order-part-send-label">已发货</div>)
															: ''
													}
												</div>
											) : ''}

											{!['TRADE_CLOSED', 'TRADE_CANCELLED', 'TRADE_CLOSED_BY_TAOBAO'].includes(order.status) || pageFrom === "settlement" ? (
												<div id={ `productItem-${order.oid}` } className="r-ml-8 r-flex r-fd-c">
													<div className="r-flex r-fw-w">
														{order.isGift ? (
															<Tooltip title="该商品是平台赠品">
																<span className="r-trade-is-gift">赠</span>
															</Tooltip>
														) : ''}
														{order.isSysGift ? (
															<Tooltip title="该商品是系统赠品">
																<span className="r-trade-is-sysGift">系统赠</span>
															</Tooltip>
														) : ''}
														{
															(!order.isSysGift && ([SysExchangeTypeEnum.置换商品, SysExchangeTypeEnum.置换货品].includes(order.sysExchangeType)
																|| [SysExchangeTypeEnum.置换数量].includes(order.sysExchangeType))
															) ? (
																<Tooltip title={ `此订单有${getSysExchangeTypeEnumText(order.sysExchangeType)}` }>
																		<span className="r-trade-exchange r-pointer">改</span>
																	</Tooltip>
																) : null
														}
														{!order.isSysGift && [SysExchangeTypeEnum.新增的商品, SysExchangeTypeEnum.新增的货品].includes(order.sysExchangeType) ? (
															<Tooltip title={ `此订单有${getSysExchangeTypeEnumText(order.sysExchangeType)}` }>
																<span className="r-trade-add r-pointer">新增</span>
															</Tooltip>
														) : ''}
														{[SysExchangeTypeEnum.线上改商品].includes(order.sysExchangeType) ? (
															<Tooltip title="此订单有线上更改商品">
																<span className="r-trade-modified-online r-pointer">线上改商品</span>
															</Tooltip>
														) : ''}
														{/* 商品标题 */}
														{showItemTitle && order.title ? (
															<span className="r-mr-5">
																{
																	!showShortTitle || (showShortTitle && showItemTitle)
																		? <CombineTag visible={ order?.isCombination === 1 } /> : null
																}
																{order.title}
															</span>
														) : ''}
														{/* 货品简称 */}
														{showShortTitle && order.titleShort && !order?.ignore
															? (
																<span className="r-mr-5">
																	<CombineTag visible={ !showItemTitle && order?.isCombination === 1 } />
																	{order.titleShort}
																</span>
															) : ''}
														{/* 商品编码 */}
														{showOuterId && order.outerId ? <span className="r-mr-5">{order.outerId} </span> : ''}
														{/* 货品编码 */}
														{!isShowZeroStockVersion && showSysOuterId && order.sysOuterId ? <span className="r-mr-5">{order.sysOuterId} </span> : ''}
													</div>
													<div className="r-flex r-fw-w">
														{/* 规格名称 */}
														{showSkuTitle && order.skuPropertiesName
															? <span className="r-mr-5">{filterPrintContent(order.skuPropertiesName)} </span> : ''}
														{/* 规格别名 */}
														{showSkuAlias && order.skuAlias ? <span className="r-mr-5">{order.skuAlias} </span> : ''}
														{/* 规格编码 */}
														{showSkuOuterId && order.outerSkuId ? <span className="r-mr-5">{order.outerSkuId} </span> : ''}
														{/* 货品规格编码 */}
														{!isShowZeroStockVersion && showSysOuterSkuId && order.sysOuterSkuId ? <span className="r-mr-5">{order.sysOuterSkuId} </span> : ''}
														{
															isCancelSplit ? null : (
																<span className={ `${order.num > 1 ? "r-c-error r-bold" : ""} r-fs-14` }>
																	{order.num} { order?.refundNumber ? `【已取消${order.refundNumber}件】` : ''}
																</span>
															)
														}
														

														{/* 缺货屯货情况 */}
														<div style={ { flex: "1 0 100%" } }>
															{getStockDetailContent(order)}
														</div>
														{order.sysStockCount && !order.noGoodsLink && showSysGoodsStockBox && !isCancelSplit ? <div>(可配/占用/实际：{order.sysStockCount - (order.stockPreemptedNum || 0)}/{order.stockPreemptedNum || 0}/{order.sysStockCount})</div> : null}
													</div>
												</div>
											) : <span className="r-c-error r-ml-8">已取消</span>}
										</label>
									)
							))}
							 {/* 添加展开/收起按钮，独立一行 */}
							 {hasMoreOrders && (
								<div className="r-flex r-jc-l r-mt-8 r-mb-8 r-w-100" style={ { clear: 'both', width: '100%' } }>
									<Button
										type="default"
										style={ {
											borderColor: '#F5821F',
											color: '#F5821F',
											backgroundColor: '#fff',
											fontSize: '12px',
											height: '20px',
											padding: '0 8px'
										} }
										onClick={ () => {
											setIsExpanded(!isExpanded);
										} }
									>
										{isExpanded ? (
											<>
												收起全部商品
											</>
										) : (
											<>
												查看全部商品
												{/* ({productOrders.length - (showColumn || 1)}) */}
											</>
										)}
									</Button>
								</div>
							)}
						</>
					) : <></>
				}
			</>
		);
	}
));

// 添加波次号组件
export const WaveNoCom = ({ pack, colWidth }) => {
	console.log(pack.waveNoList, 'pack.waveNoListpack.waveNoList');
	return (
		<div style={ { width: colWidth, paddingRight: '3px' } } className="batch_tbtlt_waveNo">
			{pack.waveNoList && pack.waveNoList.length > 0 ? (
				<div className="wave-no-vertical-list">
					{pack.waveNoList.map((waveNo, index) => (
						<div key={ index } className="wave-no-item">
							{waveNo}
						</div>
					))}
				</div>
			) : (
				''
			)}
		</div>
	);
};

export const PackageOrdersCom = observer((
	(props: {
		pack: IPackage,
		isCancelSplit?:boolean
		fullWidth?:boolean
		colWidth: string | number;
	}) => {
		const { tradeListStore: { togglePackageSelectStatus } } = tradeStore;
		const { pack, colWidth } = props;
		const { productOrders } = pack;
		const renderPackageOrders = () => {
			if (!productOrders) {
				return "";
			}

			return (
				<div className="r-flex r-fw-w product-container">
					<ProductOrderFragment showSelect={ !props.isCancelSplit } productOrders={ productOrders } { ...props } />
				</div>
			);
		};
		return (
			<div style={ { width: colWidth } } className="batch_tbtlt_product r-as-s" onClick={ (e) => { e.stopPropagation(); } }>
				{renderPackageOrders()}
			</div>
		);
	}
));

export const SysMemoFragment:React.FC<ISubTrade> = observer(({
	trade
}) => {
	return (
		<Fragment key={ trade.tid }>
			{trade.sysMemo ? (
				<Tooltip title={ trade.sysMemo }>
					<Icon type="beizhu" style={ { color: '#999' } } />
					<span>{trade.sysMemo.slice(0, 200)}{trade.sysMemo?.length > 200 ? "..." : ""}</span>
				</Tooltip>
			) : ''}
		</Fragment>
	);
});
export const SysMemoCom = observer(
	(props: {
		pack: IPackage,
		colWidth: string | number;
	}) => {
		const { pack: { trades }, colWidth } = props;
		const { setModifySysMemoPackage, setIsShowBatchSysMemoModal, tradeListStore: { togglePackageSelectStatus } } = tradeStore;
		let hasContent = trades.some(trade => trade.sysMemo);
		// let hasJingxi = false;
		const modifySysMemo = (e: any) => {
			e.stopPropagation();
			runInAction(() => {
				setModifySysMemoPackage([props.pack]);
				setIsShowBatchSysMemoModal(true);
			});
		};

		 // 检查是否为京喜订单
		//  if (trades.some(trade => trade.serviceTagList?.includes('jingxi'))) {
		// 	hasJingxi = true;
		//   }

		return (
			<div
				style={ { width: colWidth, wordBreak: 'break-word', whiteSpace: 'pre-wrap' } }
				className="batch_tbtlt_message r-flex r-as-s r-ai-c"
				// onClick={ () => { togglePackageSelectStatus(props.pack); } }
			>
				<div>
					{trades.map(trade => (
						<SysMemoFragment key={ trade.tid } trade={ trade } />
					))}
				</div>
				<div className="r-as-c r-ml-10">
					{hasContent
						? <span className="r-pointer" onClick={ modifySysMemo }><Icon type="bianji" /></span>
						: <span className="r-pointer" onClick={ modifySysMemo }><Icon type="tianjiabeizhu" /></span>}
				</div>
			</div>
		);
	}
);

export const MessageFragment:React.FC<ISubTrade> = observer(({
	trade
}) => {
	return (
		<Fragment key={ trade.tid }>
			{trade.buyerMessage ? (
				<div>
					<Icon type="beizhu" />
					<span>{trade.buyerMessage}</span>
				</div>
			) : ''}
			{(trade.sellerMemo || (!trade.sellerMemo && trade.sellerMemoFlag !== '0')) ? (
				<div>
					{getTradeFlag(0, null, trade.sellerMemoFlag)}
					{getTradeFlagTag(trade.sellerFlag, trade?.sellerFlagTag)}
					<span style={ { wordBreak: 'break-word' } }>{trade.sellerMemo}</span>
				</div>
			) : ''}
		</Fragment>
	);
});



export const BuyerMessageCom = observer(
	(props: {
		pack: IPackage,
		colWidth: string | number;
	}) => {
		const { pack: { trades, noGoodsLink }, colWidth } = props;
		const { setModifyMemoPackage, setIsShowBatchModifyMemoModal, tradeListStore: { togglePackageSelectStatus } } = tradeStore;
		let hasContent = false;
		let hasJingxi = false;
		if (trades.filter(trade => +trade.sellerMemoFlag || trade.sellerMemo).length) {
			hasContent = true;
		}
		const modifyMemo = (e: any) => {
			e.stopPropagation();
			// if (isSourceScm(props.pack)) {
			// 	scmPackTips();
			// 	return;
			// }
			runInAction(() => {
				setModifyMemoPackage([props.pack]);
				setIsShowBatchModifyMemoModal(true);
			});
		};

		 // 检查是否为京喜订单
		 if (trades.some(trade => trade.serviceTagList?.includes('jingxi'))) {
			hasJingxi = true;
		  }

		return (
			<div
				style={ { width: colWidth, wordBreak: 'break-word', whiteSpace: 'pre-wrap' } }
				className="batch_tbtlt_message r-flex r-as-s r-ai-c"
				// onClick={ () => { togglePackageSelectStatus(props.pack); } }
			>
				<div>
					{trades.map(trade => (
						<MessageFragment key={ trade.tid } trade={ trade } />
					))}
				</div>
				{
					!hasJingxi && (
						<div className="r-as-c r-ml-10">
							{hasContent
								? <span className="r-pointer" onClick={ modifyMemo }><Icon type="bianji" /></span>
								: <span className="r-pointer" onClick={ modifyMemo }><Icon type="tianjiabeizhu" /></span>}
						</div>
					)
				}

			</div>
		);
	}
);

// * 打印状态
export const PackPrintStatus = memo(
	(props: {
		waybillPrintStatus: string;
		shipListPrintStatus: string;
		labelPrintStatus: number
	}) => {

		const { waybillPrintStatus, shipListPrintStatus, labelPrintStatus } = props;

		return (

			<div className="pin-left-box">
				{shipListPrintStatus !== 'none' ? (
					<Tooltip placement="right" title={ shipListPrintStatus === 'already' ? '已打印发货单' : '部分打印发货单' }>
						<span className={ `mark mark-ship-${shipListPrintStatus}` } />
					</Tooltip>
				) : (
					<span className={ `mark mark-ship-${shipListPrintStatus}` } />
				)}
				{waybillPrintStatus !== 'none' ? (
					<Tooltip placement="right" title={ waybillPrintStatus === 'already' ? '已打印快递单' : '部分打印快递单' }>
						<span className={ `mark mark-waybill-${waybillPrintStatus}` } />
					</Tooltip>
				) : (
					<span className={ `mark mark-waybill-${waybillPrintStatus}` } />
				)}
				{
					labelPrintStatus === 1 && (
						<Tooltip placement="right" title="已打印商品标签">
							<Icon type="gou" size={ 12 } style={ { color: 'rgb(102, 50, 184)' } } />
						</Tooltip>
					)
				}
				{
					labelPrintStatus === 2 && (
						<Tooltip placement="right" title="部分已打印商品标签">
							<Icon type="dayin" size={ 12 } style={ { color: 'rgb(102, 50, 184)' } } />
						</Tooltip>
					)
				}
			</div>
		);
	}
);


export const SerialNumberCom = (props) => {
	const [inputValue, setInputValue] = useState('');
	const { order } = props;
	useEffect(() => {
	  setInputValue(order.productIdCode);
	}, [order.productIdCode]);

	const onInputChange = (e) => {
		setInputValue(e.target.value);
	};

	const onInputBlur = debounce((async() => {
		const productIdCode = inputValue;
		if ((/[^a-zA-Z0-9,]/g).test(productIdCode)) {
			message.warn('识别码输入有误，仅支持英文、英文逗号","、数字');
			return;
		}
		if (productIdCode.split(',')?.length != order.num) {
			message.warn('识别码个数与原商品数量不一致，请校验后再试');
			return;
		}
		const params = {
			tid: order.tid,
			oid: order.oid,
			sellerId: order.sellerId,
			platform: order.platform,
			productIdCode,
		};
		const res = await UpdateOrderSerialNumberApi(params);
		message.success('识别码设置成功');
		order.productIdCode = productIdCode;
	}), 500, { leading: true, trailing: false });

	const isDisabled = ![TradeStatus.等待卖家发货, TradeStatus.等待买家付款].includes(order.status);

	return (
		<div className="serial_number_item">
			<div className="serial_number_label">识别码
				<Tooltip className="r-ml-5" title="由于平台要求，该类目下发货必须填写识别码（SN码、IMEI号、ICCID码）" ><QuestionCircleOutlined /></Tooltip>

			</div>
			<Input
				value={ inputValue }
				className="serial_number_input"
				placeholder={ `必填，需填入${order.num}个识别码，请录入设备的SN码或ICCID码，多个请用英文","隔开` }
				onChange={ onInputChange }
				disabled={ isDisabled }
				onBlur={ onInputBlur }
			/>
			<Button disabled={ isDisabled } size="small" onClick={ onInputBlur }>保存</Button>
		</div>
	);
};


export const SysMemoPicFragment:React.FC<ISubTrade> = observer(({
	trade
}) => {
	const imgs = trade.sysMemoPic?.split(',') || [];

	return (
		<Fragment key={ trade.tid }>
			{
				imgs?.map((item) => (
					<div className="r-mr-8" style={ { display: "inline-block" } }>
						<Popover
							content={ (
								<Image
									width={ 200 }
									height={ 200 }
									src={ item || DEFAULT_IMG }
									preview={ false }
									fallback={ DEFAULT_IMG }
									className={ cs(s['image-container'], 'r-pointer') }
									loading="lazy"
								/>
							) }
						>
							<Image
								width={ 48 }
								height={ 48 }
								referrerPolicy="no-referrer"
								style={ { border: "1px solid #E5E5E5" } }
								src={ item || DEFAULT_IMG }
								fallback={ DEFAULT_IMG }
								loading="lazy"
								preview={ false }
							/>
						</Popover>
					</div>

				))
			}
		</Fragment>
	);
});
export const SysMemoComPic = observer(
	(props: {
		pack: IPackage,
		colWidth: string | number;
	}) => {
		const { pack: { trades }, colWidth } = props;
		const { setModifySysMemoPicPackage, setIsShowBatchSysMemoPicModal, tradeListStore: { togglePackageSelectStatus } } = tradeStore;

		const modifySysMemo = (e: any, trade) => {
			e.stopPropagation();
			runInAction(() => {
				setModifySysMemoPicPackage({ ...trade });
				setIsShowBatchSysMemoPicModal(true);
			});
		};

		return (
			<div
				style={ { width: colWidth, wordBreak: 'break-word', whiteSpace: 'pre-wrap' } }
				className="batch_tbtlt_message r-flex r-as-s r-ai-c"
			>
				<div>
					{trades.map(trade => (
						<div className="r-flex r-as-c r-ai-c r-fw-w">
							<SysMemoPicFragment
								key={ trade.tid }
								trade={ trade }
							/>
							<div className=" r-ml-10">
								{trade?.sysMemoPic
									? <span className="r-pointer" onClick={ (e) => { modifySysMemo(e, trade); } }><Icon type="bianji" /></span>
									: <span className="r-pointer" onClick={ (e) => { modifySysMemo(e, trade); } }><Icon type="tianjiabeizhu" /></span>}
							</div>
						</div>

					))}
				</div>

			</div>
		);
	}
);
