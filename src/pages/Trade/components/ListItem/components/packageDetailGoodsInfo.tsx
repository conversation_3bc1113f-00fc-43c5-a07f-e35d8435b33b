import React, { useEffect, useState } from "react";
import { Button, Image, Input, InputNumber, Modal, Popover, Select, Tooltip, message } from "antd";
import { observer } from "mobx-react";
import { IOrders, IPackage, ISubTrade } from "@/pages/Trade/interface";
import CombineTag from "@/components-biz/Trade/CombineTag";
import { weightUnit } from "@/pages/Index/Settings/System/constants";
import { getTradeStatusLabel, getOrderRefundStatus, handlePackageList, getCostPrice, getDeleteOrderTip } from "@/pages/Trade/utils";
import userStore from "@/stores/user";
import { getMirrorEnum } from "@/utils";
import { tradeStore,editGoodsStore } from "@/stores";
import { ItemItemEditSysItemInfoInTradeApi } from "@/apis/trade";
import { TradeOrderSplitDeleteSplitTradeApi } from "@/apis/trade/handOrder";
import { ItemItemEditSysItemInfoInTradeRequest } from "@/types/trade/index";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import s from './index.module.scss';
import { capitalizeFirstLetter } from "@/utils/util";
import SupplierSearchSelect from "@/pages/Warehouse/System/Archives/components/SupplierSearchSelect";
import { SupplierListResponse } from "@/types/schemas/warehouse/Supplier";
import { PLAT_PDD } from "@/constants";
import FieldsPermissionCheck, { FieldsPermissionCheckTypeEnum, FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { SysExchangeTypeEnum } from '@/pages/Trade/components/BottomCom/constants';
import { getSysExchangeTypeEnumText } from '@/pages/Trade/components/BottomCom/utils';
import { formatWeightDisplay } from '@/components/Input/InputNumber/WeightInput';

export interface IPackageDetailGoodsInfoProps {
	trade: ISubTrade;
	$value: IOrders;
	packInfo: IPackage;
	editGoodsBtnDisabled: boolean; // 是否支持删除赠品
}

enum OriginGoodsEditTypeEnum {
	'简称' = 'titleShort',
	'别名' = 'skuAlias',
	'供应商' = 'supplierId',
	'成本价' = 'costPrice',
	'市场' = 'market',
	'档口' = 'stall',
}

const GoodsEditTypeEnum = getMirrorEnum(OriginGoodsEditTypeEnum);

const PointMap = {
	[GoodsEditTypeEnum.简称]: Pointer.订单_详情_保存货品简称成功,
	[GoodsEditTypeEnum.别名]: Pointer.订单_详情_保存货品规别名成功,
	[GoodsEditTypeEnum.供应商]: Pointer['订单_详情_保存市场/档口/供应商'],
	[GoodsEditTypeEnum.成本价]: Pointer.订单_详情_保存成本价,
};

const EMPTY_SIGN = 'EMPTY';

const PackageDetailGoodsInfo = (props: IPackageDetailGoodsInfoProps) => {
	const { $value, packInfo, trade, editGoodsBtnDisabled } = props;
	const { hasCostPricePermission } = userStore;
	const {
		tradeListStore: {
			lastEditGoodsTime,
			setLastEditGoodsTime,
			getListByTogetherId,
			deleteTradeSplice,
		},
	} = tradeStore;
	const [supplierObj, setSupplierObj] = useState<Partial<SupplierListResponse["data"]["list"][number]>>({
		market: $value.market,
		stall: $value.stall,
	});

	const onInputEditGoodsInfo = (e, $value: IOrders, type: OriginGoodsEditTypeEnum = GoodsEditTypeEnum.简称) => {
		if (type === GoodsEditTypeEnum.供应商) {
			if (userStore.isShowZeroStockVersion) {
				$value[`temp${capitalizeFirstLetter(type)}`] = e.target.value;
			} else {
				e = e || {
					id: ''
				};
				setSupplierObj(e);
				$value[`temp${capitalizeFirstLetter(type)}`] = e.id;
			}
		} else {
			let value;
			if (type === GoodsEditTypeEnum.成本价) {
				value = parseFloat(e) ? parseFloat(e).toFixed(4) : '';
			} else {
				value = e.target.value;
			}
			$value[`temp${capitalizeFirstLetter(type)}`] = value;
		}
	};

	const renderSupplierEditInput = ($value: IOrders, type: OriginGoodsEditTypeEnum = GoodsEditTypeEnum.供应商) => (
		<div className={ s["eidtGoodsWrapper"] }>
			{!userStore.isShowZeroStockVersion ? (
				<div className="r-flex r-ai-c">
					<span className="r-fs-16 r-fc-F5821F">市场：</span>
					<Input size="small" value={ supplierObj.market } disabled style={ { width: 200 } } />
					<span className="r-fs-16 r-fc-F5821F r-ml-4">档口：</span>
					<Input size="small" disabled value={ supplierObj.stall } style={ { width: 200 } } />
					<span className="r-fs-16 r-fc-F5821F r-ml-4">供应商：</span>
					<SupplierSearchSelect
						className={ s["supplierSelect"] }
						size="small"
						onChange={ e => onInputEditGoodsInfo(e, $value, GoodsEditTypeEnum.供应商) }
						style={ { width: 200 } }
						defaultValue={ $value.supplierId }
						allowClear
					/>
				</div>
			) : (
				<>
					<span className="r-fs-16 r-fc-F5821F">市场：</span>
					<Input size="small" onInput={ e => onInputEditGoodsInfo(e, $value, GoodsEditTypeEnum.市场) } style={ { width: 200 } } defaultValue={ $value.market } />
					<span className="r-fs-16 r-fc-F5821F r-ml-4">档口：</span>
					<Input size="small" onInput={ e => onInputEditGoodsInfo(e, $value, GoodsEditTypeEnum.档口) } style={ { width: 200 } } defaultValue={ $value.stall } />
					<span className="r-fs-16 r-fc-F5821F r-ml-4">供应商：</span>
					<Input size="small" onInput={ e => onInputEditGoodsInfo(e, $value, GoodsEditTypeEnum.供应商) } style={ { width: 200 } } defaultValue={ $value.supplierName } />
				</>
			)}
			<Button
				loading={ $value.isEditSupplierIdLoading }
				className="r-ml-12 r-mr-12"
				onClick={ () => { onSaveEditGoodsInfo($value, type); } }
				size="small"
				type="primary"
			>保存
			</Button>
			<Button size="small" type="default" onClick={ () => { onCancelEditGoodsInfo($value, type); } } >取消</Button>
		</div>
	);

	const renderSingleEditInput = ($value: IOrders, type: OriginGoodsEditTypeEnum = GoodsEditTypeEnum.简称) => (
		<div className={ s["eidtGoodsWrapper"] }>
			<span className="r-fs-16 r-fc-F5821F">{GoodsEditTypeEnum[type]}：</span>
			{type === GoodsEditTypeEnum.成本价 ? (
				<InputNumber
					precision={ 4 }
					min={ 0 }
					max={ 999999.9999 }
					onChange={ e => onInputEditGoodsInfo(e, $value, type) }
					style={ { width: 200 } }
					defaultValue={ $value[type] }
					size="small"
				/>
			) : (
				<Input size="small" onInput={ e => onInputEditGoodsInfo(e, $value, type) } style={ { width: 400 } } defaultValue={ $value[type] } />
			)}

			<Button
				loading={ $value[`isEdit${capitalizeFirstLetter(type)}Loading`] }
				className="r-ml-12 r-mr-12"
				onClick={ () => { onSaveEditGoodsInfo($value, type); } }
				size="small"
				type="primary"
			>保存
			</Button>
			<Button size="small" type="default" onClick={ () => { onCancelEditGoodsInfo($value, type); } } >取消</Button>
		</div>
	);

	const renderEditGoodsInput = ($value: IOrders, type: OriginGoodsEditTypeEnum = GoodsEditTypeEnum.简称) => {
		return (type === GoodsEditTypeEnum.供应商 ? renderSupplierEditInput($value, type) : renderSingleEditInput($value, type));
	};

	const onCancelEditGoodsInfo = ($value: IOrders, type: OriginGoodsEditTypeEnum = GoodsEditTypeEnum.简称) => {
		$value[`isEdit${capitalizeFirstLetter(type)}`] = false;
		delete $value.tempTitleShort;
		delete $value.tempSkuAlias;
		delete $value.tempCostPrice;
		delete $value.tempSupplierId;
		delete $value.tempMarket;
		delete $value.tempStall;
	};

	const onSaveEditGoodsInfo = async($value: IOrders, type: OriginGoodsEditTypeEnum = GoodsEditTypeEnum.简称) => {
		PointMap[type] && sendPoint(PointMap[type]);
		console.log('xxxxxx',$value,type);

		if (type === GoodsEditTypeEnum.供应商 && userStore.isShowZeroStockVersion) {
			if (($value[`tempMarket`] === undefined || $value['tempMarket'] === $value['market'])
			&& ($value[`tempStall`] === undefined || $value['tempStall'] === $value['stall'])
			&& ($value[`tempSupplierId`] === undefined || $value['tempSupplierId'] === $value['supplierName'])
			) {
				onCancelEditGoodsInfo($value, type);
				return;
			}
		} else if ($value[`temp${capitalizeFirstLetter(type)}`] === undefined || $value[`temp${capitalizeFirstLetter(type)}`] === $value[type]) {
			// * 只点了保存或者修改前后相同
			onCancelEditGoodsInfo($value, type);
			return;
		}

		if ($value.tempTitleShort === '' && type === GoodsEditTypeEnum.简称) {
			message.error('简称不能为空');
			return;
		}

		// let now = new Date().getTime();
		// if (now - lastEditGoodsTime < 10000) {
		// 	message.error('10秒内仅可修改一次');
		// 	return;
		// }

		const { sellerId, togetherId, platform, source, distributorUserId } = packInfo;
		const { tid, oid, numIid, skuId, systemNumIid, skuAlias, systemSkuId, titleShort } = $value;
		let supplierId = $value.supplierId ?? -1;
		let supplierName = $value.supplierName ?? EMPTY_SIGN;
		let stall = $value.stall ?? EMPTY_SIGN;
		let market = $value.market ?? EMPTY_SIGN;

		if (type === GoodsEditTypeEnum.供应商) {
			supplierId = supplierObj.id || -1;
			supplierName = supplierObj.supplierName || EMPTY_SIGN;
			stall = supplierObj.stall || EMPTY_SIGN;
			market = supplierObj.market || EMPTY_SIGN;

			if (userStore.isShowZeroStockVersion) {
				stall = ($value.tempStall ?? $value.stall) || EMPTY_SIGN;
				market = ($value.tempMarket ?? $value.market) || EMPTY_SIGN;
				supplierId = undefined;
				supplierName = ($value.tempSupplierId ?? $value.supplierName) || EMPTY_SIGN;
			}
		}

		let params: ItemItemEditSysItemInfoInTradeRequest = {
			sellerId,
			platformType: platform,
			tid,
			oid,
			editType: 1,
			source,
			sysItemId: systemNumIid,
			sysSkuId: systemSkuId,
			// * 编辑某一项需要把其他项的原始值带上
			// * 空字符串 undefined null时都传EMPTY
			sysItemAlias: type === GoodsEditTypeEnum.简称 ? $value.tempTitleShort : titleShort || EMPTY_SIGN,
			sysSkuAlias: type === GoodsEditTypeEnum.别名 ? $value.tempSkuAlias || EMPTY_SIGN : skuAlias || EMPTY_SIGN,
			supplierId: userStore.isShowZeroStockVersion ? undefined : supplierId,
			supplier: supplierName,
			saleUserId: distributorUserId,
			stall,
			market,
			costPrice: type === GoodsEditTypeEnum.成本价 ? $value.tempCostPrice || EMPTY_SIGN : $value.costPrice,
		};
		if (userStore.isShowZeroStockVersion) {
			params.editType = type === GoodsEditTypeEnum.简称 ? 3 : 4;
			params.numIid = numIid;
			params.skuId = skuId;
		} else {
			params.editType = type === GoodsEditTypeEnum.简称 ? 1 : 2;
		}

		$value[`isEdit${capitalizeFirstLetter(type)}Loading`] = true;
		// console.log(params);
		if (!hasCostPricePermission) {
			delete params.costPrice;
		}
		ItemItemEditSysItemInfoInTradeApi(params).then(async(res) => {
			setLastEditGoodsTime(new Date().getTime());
			message.success('保存成功，商品变更资料会更新至商品，井刷新符合条件的订单');
			if (type === GoodsEditTypeEnum.供应商) {
				if (userStore.isShowZeroStockVersion) {
					$value.supplierName = ($value.tempSupplierId ?? $value.supplierName) || '';
					$value.stall = ($value.tempStall ?? $value.stall) || '';
					$value.market = ($value.tempMarket ?? $value.market) || '';
				} else {
					$value.supplierId = supplierObj.id || '';
					$value.supplierName = supplierObj.supplierName || '';
					$value.stall = supplierObj.stall || '';
					$value.market = supplierObj.market || '';
				}
			} else {
				$value[type] = $value[`temp${capitalizeFirstLetter(type)}`];
			}
			let prev = getListByTogetherId(togetherId)[0];
			prev = await handlePackageList([prev], false, tradeStore.storeSearchParams)[0];
			// * 先回显当前订单 再回显整个列表中相同货品的订单
			let { list } = tradeStore.tradeListStore;
			let updateList: IPackage[] = [];
			list.forEach((pack, index) => {
				let needUpdate = false;
				pack.trades.forEach(trade => {
					trade.orders.forEach(order => {
						if (type === GoodsEditTypeEnum.供应商) {
							if ((systemSkuId && order.systemSkuId === systemSkuId) || (!systemSkuId && order.skuId === skuId && order.numIid === numIid)) {
								needUpdate = true;
								if (userStore.isShowZeroStockVersion) {
									order.supplierName = ($value.tempSupplierId ?? $value.supplierName) || '';
									order.stall = ($value.tempStall ?? $value.stall) || '';
									order.market = ($value.tempMarket ?? $value.market) || '';
								} else {
									order.supplierId = supplierObj.id || '';
									order.supplierName = supplierObj.supplierName || '';
									order.stall = supplierObj.stall || '';
									order.market = supplierObj.market || '';
								}
							}
						} else if (type !== GoodsEditTypeEnum.简称) {
							if ((systemSkuId && order.systemSkuId === systemSkuId) || (!systemSkuId && order.skuId === skuId && order.numIid === numIid)) {
								needUpdate = true;
								order[type] = $value[`temp${capitalizeFirstLetter(type)}`];
							}
						} else if (type === GoodsEditTypeEnum.简称) {
							// 如果是编辑简称，则需要判断systemNumIid是否为空，为空则对比numIid
							if ((systemNumIid && order.systemNumIid === systemNumIid)||(!systemNumIid && order.numIid === numIid)) {
								needUpdate = true;
								order[type] = $value[`temp${capitalizeFirstLetter(type)}`];
							}
						}
					});
				});

				if (needUpdate) {
					updateList.push(pack);
				}
			});
			onCancelEditGoodsInfo($value, type);
			updateList = await handlePackageList(updateList, false, tradeStore.storeSearchParams);
		}).finally(() => {
			$value[`isEdit${capitalizeFirstLetter(type)}Loading`] = false;
		});
	};

	const handleServiceInfoModal = () => {
		let payInfo = [];
		let freeInfo = [];
		try {
			payInfo = JSON.parse($value.payEntryServiceInfo);
			console.log('payInfo: ', payInfo);
		} catch (error) {
			console.log('error: ', error);
		}
		try {
			let freeParse = JSON.parse($value.freeEntryServiceInfo);
			console.log('freeParse: ', freeParse);
			freeParse?.forEach(i => {
				i.c?.forEach(v => {
					let [type, serviceAttriribute, item] = v.split('^');
					console.log('type, name: ', type, serviceAttriribute, item);
					let getNode:any = item;
					if (type == 'images') {
						getNode = <Popover placement="right" content={ <img width="400px" alt={ serviceAttriribute } src={ item } /> }><img width="40px" alt={ serviceAttriribute } src={ item } /></Popover>;
					}
					freeInfo.push({
						serviceAttriribute,
						serviceName: getNode
					});
				});
			});
		} catch (error) {
			console.log('error: ', error);
		}
		Modal.info({
			icon: null,
			width: '500px',
			centered: true,
			bodyStyle: { paddingTop: '10px' },
			content: (
				<div>
					<div className="r-fs-20 r-mb-30">定制服务</div>
					<div>
						{
							payInfo?.map(i => {
								return (
									<div>
										<span className="r-mr-10" style={ { width: '100px', display: 'inline-block', textAlign: 'right' } }>{i.serviceAttriribute}：</span>
										<span style={ { minWidth: '150px', display: 'inline-block' } }>{i.serviceName}</span>
										<span className="r-ml-10">￥{i?.servicePrice}</span>
									</div>
								);
							})
						}
						{
							freeInfo?.map(i => {
								return (
									<div>
										<span className="r-mr-10" style={ { width: '100px', display: 'inline-block', textAlign: 'right' } }>{i.serviceAttriribute}：</span>
										<span style={ { minWidth: '150px', display: 'inline-block' } }>{i.serviceName}</span>
									</div>
								);
							})
						}
					</div>
				</div>
			)
		});
	};

	// 删除添加的赠品 $value, packInfo, trade
	const onClickRemoveGiftGoods = () => {
		console.log('%c [ 删除添加的赠品 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', )
		if (trade.orders.length === 1) {
			const tip = getDeleteOrderTip(packInfo);
			if (tip) {
				message.warn(tip);
				return;
			}
			Modal.confirm({
				centered: true,
				title: '确认',
				content: '删除此商品将同时删除订单，是否删除此商品？',
				width: '400px',
				onOk: () => {
					const { platform, sellerId } = packInfo;
					TradeOrderSplitDeleteSplitTradeApi({ tid: trade.tid, platform, sellerId }).then((res:any) => {
						if (res.success) {
							message.success('删除成功');
							if (packInfo.trades?.length > 1) {
								deleteTradeSplice(packInfo.togetherId, trade.tid);
							} else {
								deleteTradeSplice(packInfo.togetherId);
							}
						}
					}).catch((err) => {
						console.log("TradeOrderSplitDeleteSplitTradeApi err:", err);
					});
				}
			});
		} else {
			Modal.confirm({
				centered: true,
				title: '确认',
				content: '是否删除此商品？',
				width: '400px',
				onOk: () => {
					editGoodsStore.onRemoveGiftGoods(packInfo, trade, $value)
				}
			});
		}
	};

	return (
		<>
			<div>
				<CombineTag visible={ $value?.isCombination === 1 } />
				{$value?.labelstatus ? (
					<Tooltip title="已生成商品标签">
						<span className="r-trade-has-taged">标</span>
					</Tooltip>
				) : ''}
				{$value?.firstSend ? (
					<Tooltip title="先发货订单，已在系统中申请单号发货但未打印快递单">
						<span className="r-trade-first-send">发</span>
					</Tooltip>
				) : ''}
				{$value.isGift ? (
					<Tooltip title="该商品是平台赠品">
						<span className="r-trade-is-gift">赠</span>
					</Tooltip>
				) : ''}
				{$value.isSysGift ? (
					<Tooltip title="该商品是系统赠品">
						<span className="r-trade-is-sysGift">系统赠</span>
					</Tooltip>
				) : ''}
				{$value.waveNo ? (
					<Tooltip title="已生成拣货波次">
						<span className="r-trade-is-waveNo">波次</span>
					</Tooltip>
				) : ''}
				{
					(!$value.isSysGift && [SysExchangeTypeEnum.置换商品, SysExchangeTypeEnum.置换货品].includes($value.sysExchangeType) 
						|| [SysExchangeTypeEnum.置换数量].includes($value.sysExchangeType)
					) ? (
						<Tooltip title={`此订单有${getSysExchangeTypeEnumText($value.sysExchangeType)}`}>
							<span className="r-trade-exchange r-pointer">改</span>
						</Tooltip>
					) : ''
				}
				{!$value.isSysGift && [SysExchangeTypeEnum.新增的商品,SysExchangeTypeEnum.新增的货品].includes($value.sysExchangeType) ? (
					<Tooltip title={`此订单有${getSysExchangeTypeEnumText($value.sysExchangeType)}`}>
						<span className="r-trade-add r-pointer">新增</span>
					</Tooltip>
				) : ''}
				{[SysExchangeTypeEnum.线上改商品].includes($value.sysExchangeType) ? (
					<Tooltip title="此订单有线上更改商品">
						<span className="r-trade-modified-online r-pointer">线上改商品</span>
					</Tooltip>
				) : ''}
				平台名称：{$value.title}{$value.title ? `_${$value.numIid}` : ""}（商品编码：{($value.outerId && +$value.outerId !== -1) ? $value.outerId : $value.numIid}）；
				{$value.titleShort ? `简称：${$value.titleShort}` : ''}
				{userStore?.isShowZeroStockVersion ? '' : `${$value.sysOuterId ? `（货品编码：${$value.sysOuterId}）` : ''}`}
				{$value.isPreSale ? <span className="r-ml-12 r-trade-isPreSale">预售</span> : ''}
				{!$value.noGoodsLink && !$value.waveNo ? (
					<span
						className="g-clickable r-ml-24"
						onClick={ () => {
							// if (!$value.systemNumIid) {
							// 	message.warning("平台商品档案中无此商品，请先同步商品到系统后编辑");
							// 	return;
							// }
							$value.isEditTitleShort = !$value.isEditTitleShort;
						} }
					>编辑简称
					</span>
				) : ''}
				{$value.isEditTitleShort ? (
					renderEditGoodsInput($value, GoodsEditTypeEnum.简称)
				) : ''}
				{$value.payEntryServiceInfo || $value.freeEntryServiceInfo ? (
					<span
						className="g-clickable r-ml-24"
						onClick={ handleServiceInfoModal }
					>查看定制服务
					</span>
				) : null}
			</div>

			<div>
				规格名称：{$value.skuPropertiesName}{$value.outerSkuId && +$value.outerSkuId !== -1 ? `（规格编码：${$value.outerSkuId})` : ''}；
				{$value.skuAlias ? `别名：${$value.skuAlias}` : ''}
				{userStore?.isShowZeroStockVersion ? '' : `${$value.sysOuterSkuId ? `（货品规格编码：${$value.sysOuterSkuId})` : ''}`}
				{!$value.noGoodsLink && !$value.waveNo ? (
					<span
						className="g-clickable r-ml-24"
						onClick={ () => {
							$value.isEditSkuAlias = !$value.isEditSkuAlias;
						} }
					>编辑别名
					</span>
				) : ''}
			</div>
			{$value.isEditSkuAlias ? (
				renderEditGoodsInput($value, GoodsEditTypeEnum.别名)
			) : ''}

			{!$value.noGoodsLink && (
				<div>
					<span className="r-mr-10">市场：{$value.market || ''}；</span>
					<span className="r-mr-10">档口：{$value.stall || ''}；</span>
					<span className="r-mr-10">供应商：{$value.supplierName || ''}；</span>
					{ !$value.waveNo && <span
						className="g-clickable r-ml-24"
						onClick={ () => {
							$value.isEditSupplierId = !$value.isEditSupplierId;
							setSupplierObj({
								market: $value.market,
								stall: $value.stall,
							});
						} }
					>编辑市场/档口/供应商
					</span>}
				</div>
			)}
			{$value.isEditSupplierId ? (
				renderEditGoodsInput($value, GoodsEditTypeEnum.供应商)
			) : ''}

			<div>
				<span className="r-mr-10">数量：{$value.num}</span>
				<span className="r-mr-10">重量：{
					formatWeightDisplay(userStore?.userSetting?.weightUnit === weightUnit.显示kg, $value.weight) + (userStore?.userSetting?.weightUnit === weightUnit.显示kg?'kg':'g')
				}
				</span>
				{
					userStore?.userInfo?.version === 1 && !$value?.ignore && !userStore.isStockAllocationVersion
						? <span className="r-mr-10">库存：{$value.sysStockCount || 0}</span>
						: null
				}
			</div>
			<div className="r-felx r-ai-c r-lh-30">

				{!$value.noGoodsLink && (
					<FieldsPermissionCheck
						fieldsPermission={ FieldsPermissionEnum.成本价 }
						type={ FieldsPermissionCheckTypeEnum.仅展示 }
					>
						<span className="r-mr-10">成本价：{getCostPrice($value.costPrice || '0.00')}</span>
					</FieldsPermissionCheck>
				)}
				{
					!trade.paymentSensitive ? (
						<>
							<span className="r-mr-10">定价：{$value.price}</span>
							<span className="r-mr-10">实付：{$value.payment}{!!$value.subOrderTaxFee ? <>（含进口税{$value.subOrderTaxFee}）</> : null}</span>
						</>
					) : null
				}

				{trade.platform == PLAT_PDD ? <span className="r-mr-10">平台优惠金额：{trade.platformDiscount}</span> : null}
				{!$value.noGoodsLink && !$value.waveNo && (
					<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 } className="g-inline-flex">
						<span
							className="g-clickable r-ml-24 r-mr-24"
							onClick={ () => {
								$value.isEditCostPrice = !$value.isEditCostPrice;
							} }
						>编辑成本价
						</span>
					</FieldsPermissionCheck>
				)}
				<span className="r-ml-24">
					{getTradeStatusLabel($value.status)}
					{($value.status == 'WAIT_SELLER_SEND_GOODS' || $value.status == 'SELLER_CONSIGNED_PART') && $value.onlineShip ? <span className="r-ml-8 trade-status-error r-fw-700 r-fs-14">（线上已发货）</span> : null }
					{($value.status == 'WAIT_SELLER_SEND_GOODS' || $value.status == 'SELLER_CONSIGNED_PART') && $value.abnormalOnlineRefunded ? <span className="r-ml-8 trade-status-error r-fw-700 r-fs-14">（线上已退款）</span> : null }
				</span>
				{$value.refundStatus !== 'NOT_REFUND' ? getOrderRefundStatus($value.refundStatus, false, $value) : ''}

				{/* 这里系统增也支持删除 */}
				{
					(!editGoodsBtnDisabled 
						&& 
						([SysExchangeTypeEnum.新增的商品,SysExchangeTypeEnum.新增的货品].includes($value?.sysExchangeType) || $value?.isSysGift )) 
						&& 
						 // 添加条件：订单没有生成波次
   						!$value.waveNo&&(
						<Button className="r-ml-24 batch_detail_goods_remove" size="small" onClick={ () => onClickRemoveGiftGoods() }>删除</Button>
					)
				}
			</div>
			{$value.isEditCostPrice ? (
				renderEditGoodsInput($value, GoodsEditTypeEnum.成本价)
			) : ''}
		</>
	);
};

export default observer(PackageDetailGoodsInfo);
