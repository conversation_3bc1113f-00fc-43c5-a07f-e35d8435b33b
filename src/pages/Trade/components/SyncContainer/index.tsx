import { Button } from 'antd';
import cs from 'classnames';
import { observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import UpdateTradeStock from '@/components-biz/StockOccupation/UpdateTradeStock';
import IconHandleRender from "@/components/Icon/custom-icon";
import { stockVersion } from '@/constants';
import { local } from '@/libs/db';
import { InviteModal } from '@/pages/Distribution/MyDistributor/components/actionModal';
import PrintCenter from '@/print/index';
import { handStore, tradeStore } from '@/stores';
import userStore from '@/stores/user';
import events from '@/utils/events';
import FunctionPermissionCheck, { FunctionPermissionEnum } from '@/utils/permissionCheck/functionPermissionCheck';
import Pointer from '@/utils/pointTrack/constants';
import AdvancedSetting from '../AdvancedSetting';
import ControlsDownModal from '../ControlsDownModal';
import AdvancedSyncModal from './components/advancedSyncModal';
import UseSyncTimeCom from './components/useSyncTimeCom';
import { TradeUserFastShopSyncApi } from '@/apis/trade/search';
import { IFastShopSyncItem } from '@/types/trade/index';
import SyncCom from './components/syncCom';
import { getToken } from '@/utils/token';
import BatchImportModal from './components/batchImportModal';
import sendPoint from '@/utils/pointTrack/sendPoint';
import message from "@/components/message";
import UnlinkGoods from '../UnlinkGoods';
import ImportExpressModal from '../ImportExpressModal';
import s from './index.module.scss';

export interface ISyncContainerProps {
}

const SyncContainer = (props: ISyncContainerProps) => {
	const [inviteModalVisible, setInviteModalVisible] = useState(false);
	const [isShowAdvancedSyncModal, setIsShowAdvancedSyncModal] = useState(false);
	const [isShowAdvancedSetting, setIsShowAdvancedSetting] = useState(false);
	const [isShowControlsDownModal, setIsShowControlsDownModal] = useState(false);
	const [isShowBatchImportModal, setIsShowBatchImportModal] = useState(false); // 批量导入订单
	const [isShowImportExpressModal, setIsShowImportExpressModal] = useState(false); // 导入快递单号
	const { fjrInfoMap, setting, kddTempList } = tradeStore;
	const { isFreeSupplierAccount } = userStore;
	const SyncTimeCom = UseSyncTimeCom({});
	const { setIsShowHandOrderModal, setHandData } = handStore;

	useEffect(() => {
		events.on('openAdvancedSetting', openAdvancedSetting);
		return () => {
			events.off('openAdvancedSetting', openAdvancedSetting);
		};
	}, []);

	const openAdvancedSetting = () => {
		setIsShowAdvancedSetting(true);
	};

	/**
	 * 打开打印中心模板服务
	 * @param type
	 */
	const handleOpenPrintMainPanel = (type: string) => {
		// console.log(kddTempList, kddTempList.length);
		// if (type === 'kdd' && !kddTempList.length) {
		// 	message.warning('请等待模板加载完成');
		// }
		PrintCenter.showTemplateMain({ printType: type,
			fjrMap: {
				"isKddBindFjr": true,
				"senderInfo": fjrInfoMap.kdd,
				"isUseMore": true,
				"isUseCommon": false
			}
		});
	};

	const beforeBatchImport = () => {
		setIsShowBatchImportModal(true);
	};
	const history = useHistory();

	// 导入快递单号
	const handleImportExpress = () => {
		setIsShowImportExpressModal(true);
	};

	return (
		<div className={ cs(s.SyncContainer) }>
			<div className={ cs(s.SyncLeft) }>
				{/* {SyncTimeCom} */}
				<AdvancedSyncModal
					visible={ isShowAdvancedSyncModal }
					onClose={ () => {
						setIsShowAdvancedSyncModal(false);
					} }
				/>
				{!isFreeSupplierAccount && (
					<SyncCom needSearchTrade type="default" />
				)}
				{!isFreeSupplierAccount && (
					<Button
						data-point={
							Pointer.订单_订单打印_订单同步_高级同步订单_高级同步_展现
						}
						onClick={ () => {
							setIsShowAdvancedSyncModal(true);
						} }
						ghost
						type="primary"
						className={ cs("r-mr-12") }
					>
						高级同步订单
					</Button>
				)}
				<FunctionPermissionCheck
					functionPermission={ FunctionPermissionEnum.手工录入订单 }
				>
					<Button
						onClick={ () => {
							setIsShowHandOrderModal(true);
							setHandData({});
						} }
						className={ cs("r-mr-12") }
						type="primary"
						ghost
						disabled={
							!userStore.hasFunctionPermission(
								FunctionPermissionEnum.手工录入订单
							)
						}
					>
						手工录入订单
					</Button>
				</FunctionPermissionCheck>
				<FunctionPermissionCheck
					functionPermission={ FunctionPermissionEnum.批量导入订单 }
				>
					<Button
						data-point={ Pointer.订单_订单打印_手工单_批量导入 }
						onClick={ beforeBatchImport }
						className={ cs("r-mr-12") }
						disabled={
							!userStore.hasFunctionPermission(
								FunctionPermissionEnum.批量导入订单
							)
						}
					>
						批量导入订单
					</Button>
				</FunctionPermissionCheck>

				<Button
					data-point={ Pointer.订单_更多操作_导入快递单号 }
					onClick={ handleImportExpress }
					className={ cs('r-mr-12') }
				>导入快递单号
				</Button>

				{userStore.isStockAllocationVersion ? <UpdateTradeStock /> : ''}
				{userStore.userInfo.version === stockVersion.库存版 ? <UnlinkGoods /> : ''}
				<div style={ { position: 'relative', marginRight: '12px' } }>
					<Button
						data-point={ Pointer.订单_商品铺货快捷入口 }
						// style={ { background: 'rgba(22, 155, 213, 1)', border: 0 } }
						type="primary"
						onClick={ () => {
							history.push("/warehouse/platformGoods");
						} }
					>
						商品铺货
					</Button>
					<span className={ s.newIcon }>new</span>
				</div>
				{/* {userStore.isSupplierAccount && !isFreeSupplierAccount ? (
					<div style={ { position: 'relative' } }>
						<Button
							data-point={ Pointer.订单_邀请分销商按钮 }
							type="primary"
							onClick={ () => setInviteModalVisible(true) }
						>邀请分销商
							<Tooltip title="分销商可免费注册系统，订单代发更方便" placement="right">
								<QuestionCircleFilled />
							</Tooltip>
						</Button>
						<span className={ s.newIcon }>new</span>
					</div>
				) : null} */}
			</div>

			<div id="syncRight" className={ cs(s.SyncRight) }>
				<IconHandleRender
					type="icon-kuaididanshezhi"
					hoverType="icon-kuaididanshezhi_fill"
					text="快递单设置"
					onClick={ () => handleOpenPrintMainPanel("kdd") }
				/>
				<IconHandleRender
					type="icon-fahuodanshezhi"
					hoverType="icon-fahuodanshezhi_fill"
					text="发货单设置"
					onClick={ () => handleOpenPrintMainPanel("fhd") }
				/>
				<IconHandleRender
					text="高级设置"
					data-point={
						Pointer.订单_订单打印_订单同步_高级同步订单_高级同步_展现
					}
					onClick={ () => {
						setIsShowAdvancedSetting(true);
						if (
							!local.get("epidemicAreaTooltip")
							&& setting.showEpidemicArea !== 2
						) {
							local.set("epidemicAreaTooltip", true);
						}
					} }
					type="icon-gaojishezhi"
					hoverType="icon-gaojishezhi_fill"
				/>
			</div>
			<AdvancedSetting
				showAdvancedSet={ isShowAdvancedSetting }
				setShowAdvancedSet={ (show) => {
					setIsShowAdvancedSetting(show);
				} }
				setShowControlsDownModal={ (show) => {
					setIsShowControlsDownModal(show);
				} }
			/>
			<ControlsDownModal
				showControlsDownModal={ isShowControlsDownModal }
				setShowControlsDownModal={ (show) => {
					setIsShowControlsDownModal(show);
				} }
			/>
			{isShowBatchImportModal ? (
				<BatchImportModal
					onClose={ () => {
						setIsShowBatchImportModal(false);
					} }
				/>
			) : (
				""
			)}
			<InviteModal
				visible={ inviteModalVisible }
				onCancel={ () => setInviteModalVisible(false) }
			/>

			{/* 批量导入发货 */}
			{
				isShowImportExpressModal ? <ImportExpressModal onClose={ () => { setIsShowImportExpressModal(false); } } /> : null
			}
		</div>
	);
};

export default observer(SyncContainer);
