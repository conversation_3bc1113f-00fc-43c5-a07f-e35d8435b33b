import React, { useEffect, useMemo, useState } from "react";
import cs from 'classnames';
import { Image } from "antd";
import { observer } from "mobx-react";
import { runInAction } from "mobx";
import _ from "lodash";
import message from "@/components/message";
import s from './index.module.scss';
import Icon from "@/components/Icon";
import FilterOrderSettingModal from "../FilterOrderSettingModal";
import { calcDomPostion } from "@/utils/util";
import tradeSetStore from "@/stores/trade/tradeSet";
import { DEFAULT_IMG } from "@/constants";
import { IFilterOrderItem, IOrders } from "../../interface";
import { tradeStore } from "@/stores";
import { ItemFilterSettingSaveApi, TradePrintSetUpdatePrintSetApi } from "@/apis/trade";
import memoFn from "@/libs/memorizeFn";
import CombineTag from "@/components-biz/Trade/CombineTag";
import Pointer from "@/utils/pointTrack/constants";
import userStore from "@/stores/user";
import TextEllipsis from '@/components/TextEllipsis';
import { isPackCheckDisable, getSkuMergeKeyByRule } from "../../utils";
import WaresInfo from '@/components-biz/WaresInfo';


export interface IFilterOrderProps {}

const filterOrderMap = {
	1: { // 平台商品
		picPath: 'picPath', // 商品图片
		skuPropertiesName: 'skuPropertiesName', // 规格名称
		outerSkuId: 'outerSkuId', // 商家编码
		skuAlias: 'skuAlias', // 规格别名
		outerId: "outerId",
	},
	2: { // 本地货品
		picPath: 'sysPicPath', // 商品图片
		sysSkuName: 'sysSkuName', // 规格名称
		outerSkuId: 'sysOuterSkuId', // 商家编码
		skuAlias: 'skuAlias', // 规格别名
	}
};

const FilterOrder = observer((props: IFilterOrderProps) => {
	const [isShowFilterOrderSetting, setIsShowFilterOrderSetting] = useState(false);
	const [settingPosition, setSettingPosition] = useState({
		x: '',
		y: '',
	});
	const { filterOrderArr, filterOrderSetting, setFilterOrderSetting, filterOrderStickyTop } = tradeSetStore;
	const { setting, setSetting } = tradeStore;
	const [selectSaleAttrArr, setSelectSaleAttrArr] = useState([]);
	const { list, setList } = tradeStore.tradeListStore;

	// 如果勾选了仅显示选中的订单
	const checkTradeStatus = (filterOrderItem) => {
		setSelectSaleAttrArr([]);
		console.log('%c [ filterOrderItem ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', filterOrderItem, filterOrderArr);

		// 用合并规则生成key
		const mergeKey = getSkuMergeKeyByRule(filterOrderItem.order, filterOrderSetting.jsonConfig.skuMerge, filterOrderSetting.showColumn);

		const changeTradeCheck = (allowTabs, type?:string) => {
			console.log('%c [ allowTabs ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', allowTabs);
			let newList:any[] = [];

			if (allowTabs.length > 0) {
				// 允许的key集合
				const allowKeys = allowTabs.map(tab => getSkuMergeKeyByRule(tab.order, filterOrderSetting.jsonConfig.skuMerge, filterOrderSetting.showColumn));
				newList = list.map(item => {
					let isActive = !item.isPending && !isPackCheckDisable(item) && item.trades.some(trade => {
						return trade.orders.some(order => {
							const orderKey = getSkuMergeKeyByRule(order, filterOrderSetting.jsonConfig.skuMerge, filterOrderSetting.showColumn);
							return (
								allowKeys.includes(orderKey)
								&& !['TRADE_CLOSED', 'TRADE_CLOSED_BY_TAOBAO'].includes(order.status)
								&& !['REFUND_SUCCESSED', 'REFUND_ING'].includes(order.refundStatus)
							);
						});
					});

					if (type == 'cancelCheck') {
						isActive = isActive && !item.trades.some(trade => {
							return trade.orders.some(order => {
								const orderKey = getSkuMergeKeyByRule(order, filterOrderSetting.jsonConfig.skuMerge, filterOrderSetting.showColumn);
								return getSkuMergeKeyByRule(filterOrderItem.order, filterOrderSetting.jsonConfig.skuMerge, filterOrderSetting.showColumn) === orderKey;
							});
						});
					}

					if (isActive) {
						return {
							...item,
							isExpand: false,
							isChecked: true,
							isFilterByStatus: false,
							isFilter: false,
						};
					} else {
						return {
							...item,
							isExpand: false,
							isChecked: false,
							isFilterByStatus: true,
							isFilter: true,
						};
					}
				});

				if (!newList?.some(item => item?.isChecked)) {
					newList = list.map(item => {
						return {
							...item,
							isExpand: false,
							isChecked: false,
							isFilterByStatus: false,
							isFilter: false,
						};
					});
				}
			} else {
				newList = list.map(item => {
					return {
						...item,
						isExpand: false,
						isChecked: false,
						isFilterByStatus: false,
						isFilter: false,
					};
				});
			}
			setList(newList);
		};
		if (filterOrderItem.checkStatus === 'checkAll') {
			const hasCheckTabs = filterOrderArr?.filter(item => item.checkStatus == 'checkAll') || [];
			const allowTabs = hasCheckTabs?.filter(d => getSkuMergeKeyByRule(d.order, filterOrderSetting.jsonConfig.skuMerge, filterOrderSetting.showColumn) !== mergeKey) || [];
			changeTradeCheck(allowTabs, 'cancelCheck');
		} else if (['checkHalf', ''].includes(filterOrderItem.checkStatus)) {
			if (!filterOrderItem.normalOrderTotalCount) {
				message.info({
					content: (
						<div style={ { textAlign: 'left' } }>
							快递助手已为您主动过滤了以下几种类型订单，如有需要请手动勾选订单：
							<div>1、退款中或已退款订单；</div>
							<div>2、已取消订单；</div>
							<div>3、挂起订单；</div>
							<div>4、风控订单；</div>
							<div>5、暂停发货订单；</div>
							<div>6、拼团订单未完成；</div>
							<div>7、平台缺货订单；</div>
						</div>
					)
				});
			} else {
				const hasCheckTabs = filterOrderArr?.filter(item => item.checkStatus == 'checkAll') || [];
				const allowTabs = [
					...hasCheckTabs,
					filterOrderItem
				];
				changeTradeCheck(allowTabs);
			}
		}
	};

	// 点击宝贝筛选
	const handleFilterOrderClick = (filterOrderItem: IFilterOrderItem) => {
		if (filterOrderSetting.onlyShowSelected == 1) {
			checkTradeStatus(filterOrderItem);
			return;
		}

		const mergeKey = getSkuMergeKeyByRule(filterOrderItem.order, filterOrderSetting.jsonConfig.skuMerge, filterOrderSetting.showColumn);

		if (filterOrderItem.checkStatus === 'checkAll') {
			runInAction(() => {
				list.forEach(item => {
					if (item.trades.some(trade => trade.orders.some(order => {
						const orderKey = getSkuMergeKeyByRule(order, filterOrderSetting.jsonConfig.skuMerge, filterOrderSetting.showColumn);
						return orderKey === mergeKey && order.isChecked;
					}))) {
						item.isChecked = false;
					}
				});
			});
		} else if (['checkHalf', ''].includes(filterOrderItem.checkStatus)) {
			if (!filterOrderItem.normalOrderTotalCount) {
				message.info({
					content: (
						<div style={ { textAlign: 'left' } }>
							快递助手已为您主动过滤了以下几种类型订单，如有需要请手动勾选订单：
							<div>1、退款中或已退款订单；</div>
							<div>2、已取消订单；</div>
							<div>3、挂起订单；</div>
							<div>4、风控订单；</div>
							<div>5、暂停发货订单；</div>
							<div>6、拼团订单未完成；</div>
							<div>7、平台缺货订单；</div>
						</div>
					)
				});
			} else {
				runInAction(() => {
					list.filter(item => !item.isPending && !isPackCheckDisable(item) && item.riskControlStatus != '1').forEach(item => {
						if (item.trades.some(trade => trade.orders.some(order => {
							const orderKey = getSkuMergeKeyByRule(order, filterOrderSetting.jsonConfig.skuMerge, filterOrderSetting.showColumn);
							return orderKey === mergeKey && order.isChecked && !['TRADE_CLOSED', 'TRADE_CLOSED_BY_TAOBAO'].includes(order.status) && !['REFUND_SUCCESSED', 'REFUND_ING'].includes(order.refundStatus);
						}))) {
							item.isChecked = true;
						}
					});
				});
			}
		}
	};
	const onTabItemClick = (index) => {
		let _selectSaleAttrArr = [...selectSaleAttrArr];
		const _index = _selectSaleAttrArr.indexOf(index);
		if (_index > -1) _selectSaleAttrArr.splice(_index, 1);
		else _selectSaleAttrArr.push(index);
		setSelectSaleAttrArr(_selectSaleAttrArr);

		// 点击宝贝，选中对应有此宝贝的订单，其他订单不显示在列表
		if (filterOrderSetting.onlyShowSelected == 1) {
			let newList = list.map(item => {
				let isActive = (
					(_selectSaleAttrArr.includes(1) && item.totalOrderNum == 1 && item.totalGoodsNum == 1)
					|| (_selectSaleAttrArr.includes(2) && item.totalOrderNum > 1 && item.totalGoodsNum == 1)
					|| (_selectSaleAttrArr.includes(3) && item.totalOrderNum > 1 && item.totalGoodsNum > 1)
				);
				if (isActive) {
					return {
						...item,
						isExpand: false,
						isChecked: true,
						isFilterByStatus: false,
						isFilter: false,
					};
				} else {
					return {
						...item,
						isExpand: false,
						isChecked: false,
						isFilterByStatus: true,
						isFilter: true,
					};
				}
			});
			// 如果全部过滤了就都放出来
			if (newList.every(item => item.isFilter)) {
				newList = list.map(item => {
					return {
						...item,
						isExpand: false,
						isChecked: false,
						isFilterByStatus: false,
						isFilter: false,
					};
				});
			}
			setList(newList);
		} else {
			runInAction(() => {
				list.forEach(item => {
					item.isChecked = false;
					if	((_selectSaleAttrArr.includes(1) && item.totalOrderNum == 1 && item.totalGoodsNum == 1)
						|| (_selectSaleAttrArr.includes(2) && item.totalOrderNum > 1 && item.totalGoodsNum == 1)
						|| (_selectSaleAttrArr.includes(3) && item.totalOrderNum > 1 && item.totalGoodsNum > 1)) {
						item.isChecked = true;
					}
				});
			});
		}
	};

	return (
		<div
			className={
				cs(s['filter-order-con'], filterOrderSetting.isFixed && s['sticky'])
			}
			style={ {
				top: `${filterOrderSetting.isFixed ? filterOrderStickyTop + 'px' : 'unset'}`,
				maxHeight: `${filterOrderSetting.isFixed ? (document.body.clientHeight - filterOrderStickyTop - 70 - 30) + 'px' : 'unset'}`
			} }
		>
			<div className={ cs(s['filter-order-title']) }>
				<span className="r-mr-5">宝贝筛选</span>
				<Icon
					style={ { color: '#999' } }
					size={ 16 }
					pointer
					type="shezhi"
					onClick={ (e: any) => {
						setSettingPosition(calcDomPostion(e, 450, 500));
						setIsShowFilterOrderSetting(true);
						// 禁止底部页面滚动
						// document.documentElement.style.overflow = "hidden";
					} }
				/>
			</div>
			<div className={ cs(s['filter-order-body']) } data-point={ Pointer.订单_订单打印_点击宝贝 }>
				{
					filterOrderSetting.salesAttributesFilterSwitch == 1 ? (
						<div className="r-flex r-fd-c r-jc-c r-ai-c r-mt-6">
							<div
								onClick={ () => { onTabItemClick(1); } }
								className={ cs(s.afterSaleTopTabItem, selectSaleAttrArr.includes(1) ? s.checkedTabItem : '') }
							>单款单件({tradeStore.tradeListStore.diffStyleNumMap?.singleItemNum})
							</div>
							<div
								onClick={ () => { onTabItemClick(2); } }
								className={ cs(s.afterSaleTopTabItem, selectSaleAttrArr.includes(2) ? s.checkedTabItem : '') }
							>单款多件({tradeStore.tradeListStore.diffStyleNumMap?.singleStyleMultiNum})
							</div>
							<div
								onClick={ () => { onTabItemClick(3); } }
								className={ cs(s.afterSaleTopTabItem, selectSaleAttrArr.includes(3) ? s.checkedTabItem : '') }
							>多款多件({tradeStore.tradeListStore.diffStyleNumMap?.multiStyleMultiNum})
							</div>
						</div>
					) : null
				}
				{filterOrderArr?.map(item => (
					<div
						className={ cs(
							s['filter-order-item'],
							s[`${item.checkStatus}`],
						) }
						onClick={ () => { handleFilterOrderClick(item); } }
					>
						<div className="r-flex">
							<WaresInfo
								className={ cs(s['filter-order-img']) }
								imgUrl={ item.order[filterOrderMap[filterOrderSetting.showColumn]?.picPath] || DEFAULT_IMG }
								imgSize={ 32 }
								previewPicSize={ filterOrderSetting?.jsonConfig?.previewPicSize }
							/>
							<div>
								{
									filterOrderSetting.showSku?.split(",").map((skuName, index) => {
										return <p key={ skuName }>{index == 0 ? <CombineTag visible={ item.order?.isCombination === 1 } /> : null}<TextEllipsis className={ s['filter-order-sku'] } title={ item.order[filterOrderMap[filterOrderSetting.showColumn]?.[skuName]] || '默认规格' } /></p>;
									})
								}
							</div>
						</div>


						{
							filterOrderSetting?.showStockNumSwitch == 0 ? null
							// 库存版 展示选中和库存的数量
								: userStore.inventoryDeduct ? (
									<div className={ cs(s['filter-order-num-inventoryDeduct']) }>
										<span className={ s['mr-2'] }>数量/选中/库存</span>
										<span className={ s['filter-order-selected-num-content'] }>
											<TextEllipsis className={ s['filter-order-selected-num'] } title={ item.totalCount || '0' } width={ 40 } />
											<span>/</span>
											<TextEllipsis className={ s['filter-order-selected-num'] } title={ item.checkedCount || '0' } width={ 40 } />
											{
												!userStore.isShowZeroStockVersion ? (
													<>
														<span>/</span>
														<TextEllipsis className={ s['filter-order-selected-num'] } title={ item.stockNum || '0' } width={ 40 } />
													</>
												) : null
											}
										</span>

									</div>
								) : (
									<div className={ cs(s['filter-order-num']) }>{ item.totalCount || '0' }/{item.checkedCount || '0'}</div>
								)
						}

					</div>
				))}
			</div>
			<div
				className={ cs(s['filter-order-footer']) }
				data-point={ Pointer.订单_订单打印_宝贝筛选_不再展示 }
				onClick={ () => {
					let params = {
						..._.cloneDeep(setting),
						filterOrder: 1,
					};
					TradePrintSetUpdatePrintSetApi(params).then(res => {
						memoFn.updateAdvancedSet(params);
						setSetting(params);
					});

				} }
			>不再显示
			</div>

			{isShowFilterOrderSetting && (
				<FilterOrderSettingModal
					showPosition={ settingPosition }
					onClose={ () => {
						setIsShowFilterOrderSetting(false);
						// document.documentElement.style.overflow = "scroll";
					} }
				/>
			) }
		</div>
	);
});

export default FilterOrder;
