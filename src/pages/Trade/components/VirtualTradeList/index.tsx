import React, { Fragment, memo, useCallback, useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { IPackage } from '../../interface';
import ListItem from '../ListItem';
import s from './index.module.scss';
import event from '@/libs/event';

export interface IVirtualListTradeListProps {
    list: IPackage[],
	fromTrade: boolean,
}

const DEFAULT_ITEM_HEIGHT = 50;
const SHOW_LIST_SIZE = Math.ceil((window.innerHeight) / DEFAULT_ITEM_HEIGHT);

const VirtualTradeList = (props: IVirtualListTradeListProps) => {
	const { list, fromTrade } = props;
	const [showList, setShowList] = useState<IPackage[]>([]);

	const [style, setStyle] = useState({
		paddingTop: 0,
		paddingBottom: 0,
	});

	const calcShouldShowItem = (isChange?: boolean | Event) => {
		const top = window.scrollY;
		const maxTop = top + window.innerHeight;
		let preTop = document.querySelector('#listCon')?.offsetTop || 0;
		let paddingTop = 0;
		let paddingBottom = 0;
		let activeIndex = -1;

		list.forEach((node, index) => {
			const minY = preTop;
			const nodeHeight = node.height || DEFAULT_ITEM_HEIGHT;
			const maxY = minY + nodeHeight;
			const inView = (maxY >= top && maxY <= maxTop) || (minY >= top && minY <= maxTop) || (maxY >= maxTop && minY <= top);
			if (inView) {
				list[index].shouldShow = true;
				activeIndex = index;
			} else {
				list[index].shouldShow = false;
				if (activeIndex === -1) {
					paddingTop += nodeHeight;
				} else if (index > activeIndex) {
					paddingBottom += nodeHeight;
				}
			}
			preTop += nodeHeight;
		});

		// 展开详情 修正补足列表数量
		let _showList = list.filter((item) => item.shouldShow);
		let len = _showList.length;
		if (len < SHOW_LIST_SIZE) {
			let gap = SHOW_LIST_SIZE - len;
			for (let i = activeIndex + 1; i < activeIndex + gap; i++) {
				if (list[i]) {
					let index = _showList.push(list[i]);
					_showList[index - 1].shouldShow = true;
				}
			}
		}
		setStyle({
			paddingBottom,
			paddingTop,
		});
		setShowList(_showList);
	};

	useEffect(() => {
		setStyle((prev) => ({
			...prev,
			paddingBottom: DEFAULT_ITEM_HEIGHT * list.length,
		}));

		setShowList(list.slice(0, SHOW_LIST_SIZE).map(item => {
			item.shouldShow = true;
			return item;
		}));
	}, []);

	useEffect(() => {
		calcShouldShowItem(true);
		window.addEventListener('scroll', calcShouldShowItem);
		event.on('fastToBottom', fastToBottom);
		return () => {
			event.off('fastToBottom', fastToBottom);
			window.removeEventListener('scroll', calcShouldShowItem);
		};
	}, [list]);

	const fastToBottom = () => {
		let preTop = document.querySelector('#listCon')?.offsetTop || 0;
		let paddingTop = 0;
		let paddingBottom = 0;
		let activeIndex = -1;

		list.forEach((node, index) => {
			const nodeHeight = node.height || DEFAULT_ITEM_HEIGHT;
			if (index > list.length - SHOW_LIST_SIZE) {
				list[index].shouldShow = true;
				activeIndex = index;
			} else {
				list[index].shouldShow = false;
				if (activeIndex === -1) {
					paddingTop += nodeHeight;
				} else if (index > activeIndex) {
					paddingBottom += nodeHeight;
				}
			}
			preTop += nodeHeight;
		});

		// 展开详情 修正补足列表数量
		let _showList = list.filter((item) => item.shouldShow);
		setStyle({
			paddingBottom,
			paddingTop,
		});
		setShowList(_showList);

		window.scrollTo(0, 999999);
	};

	return (
		<div
			style={ {
				paddingTop: style.paddingTop,
				paddingBottom: style.paddingBottom,
			} }
			id="virtualList"
			className={ `${s.virtualListCon}` }
		>
			{showList.map((item: IPackage, index) => (
				<ListItem
					fromTrade={ fromTrade }
					key={ `${item.platform}-${item.togetherId}` }
					pack={ item }
					from="virtual"
					showShopTradeCount={ index === 0 || item.sellerId !== showList[index - 1]?.sellerId }
				/>
			))}
		</div>
	);
};

export default observer(VirtualTradeList);
