import { Button, Modal, Radio, Select, Transfer, Spin, Table, Tooltip, Checkbox, Typography } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import { SortableContainer, SortableContainerProps, SortableElement, SortableHandle, SortEnd } from 'react-sortable-hoc';
import cs from "classnames";
import { observer } from "mobx-react";
import _, { isNumber } from "lodash";
import { useBoolean } from "ahooks";
import { arrayMoveImmutable } from "array-move";
import { MenuOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { tradeStore } from "@/stores";
import { TradeExportApi, TradeExportGetConfigApi, TradeExportSaveConfigApi, TradeExportResetApi } from "@/apis/trade";
import message from "@/components/message";
import { local } from "@/libs/db";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import s from './index.module.scss';
import { IPackage } from "../../interface";

enum BatchExportType {
	订单维度 = 0,
	商品维度 = 1
}
enum BatchExportMethod {
	导出当前查询结果 = 0,
	导出勾选订单 = 1
}
const BatchExportText = {
	'0': "订单维度",
	'1': "商品维度"
};

const tooltipColumns = {
	"status": "商品状态下，订单状态为子订单状态",
	"sellerDiscount": "仅拼多多平台订单",
	"platformDiscount": "仅拼多多平台订单",
	"shipContent": '合单下的订单，发货内容导出后将显示整笔合单的发货内容',
};

const BatchExportModal = observer((props: { searchParams: any, list:IPackage[] }) => {
	const [loading, { setFalse, setTrue }] = useBoolean(false);
	const { searchParams, list } = props;
	const [fieldList, setFieldList] = useState([]);
	const { setIsShowBatchExportModal, modifyMemoPackage } = tradeStore;
	const [exportType, setExportType] = useState(BatchExportType.商品维度);
	const [exportMethod, setExportMethod] = useState(BatchExportMethod.导出当前查询结果);
	const [filterRefund, setFilterRefund] = useState(() => {
		return local.get('Trade.BatchExportFilterRefund') ?? false;
	});


	useEffect(() => {
		const _exportType = local.get('Trade.BatchExportType');
		const _exportMethod = local.get('Trade.BatchExportMethod');
		if (isNumber(_exportMethod)) {
			setExportMethod(_exportMethod);
		}
		if (parseInt(_exportType, 10) + 1) {
			setExportType(_exportType);
			getExportConfig({ latitude: _exportType });
		} else {
			getExportConfig({ latitude: exportType });
		}
	}, []);

	const onSaveField = (newList) => {
		const field = newList.flat().map((item, index) => ({
			...item,
			rank: index + 1
		}));
		TradeExportSaveConfigApi({
			latitude: exportType,
			fields: field,
			method: exportMethod
		});
	};

	const initField = (exportFields) => {
		const tradeList = exportFields.filter(i => i.type == BatchExportType.订单维度).sort((a, b) => (a.rank - b.rank));
		const goodsList = exportFields.filter(i => i.type == BatchExportType.商品维度).sort((a, b) => (a.rank - b.rank));
		setFieldList([tradeList, goodsList]);
	};

	const getExportConfig = (params) => {
		setTrue();
		TradeExportGetConfigApi(params).then(res => {
			setFalse();
			initField(res.exportFields);
		});
	};

	const handleOk = () => {
		const tidList = [];
		if (!fieldList.flat().filter(i => i.selected)?.length) {
			message.warn('请选择导出内容');
			return false;
		}
		// 如果导出方式是勾选订单，需要校验是否勾选
		if (exportMethod === BatchExportMethod.导出勾选订单) {
			const selectedPackList = list?.filter(item => item.isChecked) || [];
			if (!selectedPackList.length) {
				message.warn('请选择需要导出的订单');
				return;
			} else {
				selectedPackList.forEach(pack => {
					pack.trades.forEach(trade => {
						tidList.push(trade.tid);
					});
				});
			}
		}
		handleDownload(tidList);
	};

	const handleDownload = async(tidList) => {
		const requestParams = {
			...searchParams,
			latitude: exportType,
			method: exportMethod,
			filterOrderRefundFlag: filterRefund, // 过滤退款成功商品
		};
		if (exportMethod === BatchExportMethod.导出勾选订单) {
			requestParams.tid = tidList.join(',');
			requestParams.sid = tidList?.length ? "" : searchParams?.sid;
		}
		await downloadCenter({
			requestParams,
			fileName: `${BatchExportText[exportType]}_订单信息`,
			module: ModulesFunctionEnum.订单打印
		});
		setIsShowBatchExportModal(false);
	};

	const handleReset = async() => {
		let res = await TradeExportResetApi({ latitude: exportType });
		initField(res.exportFields);
	};

	const exportTypeChange = (e) => {
		const { value } = e.target;
		setExportType(value);
		getExportConfig({ latitude: value });
		local.set('Trade.BatchExportType', value);
	};

	const exportMethodOnChange = e => {
		const { value } = e.target;
		setExportMethod(value);
		local.set('Trade.BatchExportMethod', value);
	};

	const handleCheck = (e, collectionKey, itemIndex) => {
		const newList = [...fieldList];
		newList[collectionKey][itemIndex].selected = !newList[collectionKey][itemIndex].selected;
		setFieldList(newList);
		onSaveField(newList);
	};

	const DragHandle = SortableHandle(({ value, tooltipContent }) => (
		<div className={ s.dragMove }>
			<Tooltip
				title="拖拽排序"
				placement="topLeft"

			>
				{value.desc}
			</Tooltip>
			{tooltipContent && (
				<Tooltip title={ tooltipContent }>
					<QuestionCircleOutlined className="r-ml-4 r-pointer" />
				</Tooltip>
			)}
		</div>
	));

	const SortableItem = (SortableElement(({ value, itemIndex, collectionKey }) => {
		const tooltipContent = tooltipColumns[value.name];
		return (
			<div className={ cs("r-flex", "r-ai-c", s["drag-visible"]) } >
				<Checkbox onChange={ (e) => handleCheck(e, collectionKey, itemIndex) } checked={ !!value.selected } />
				<div className={ cs(s["dragItem"]) }>
					<DragHandle value={ value } tooltipContent={ tooltipContent } />
				</div>
			</div>
		);
	}));

	const SortableList = SortableContainer(({ collections }) => (
		<div className={ s.grapWrap }>
			{
				collections.map((items, collectionIndex) => (
					<>
						<strong>{['订单信息', '商品信息'][collectionIndex]}</strong>
						<div className={ s["dragList"] }>
							{
								items.map((value, index) => (
									<SortableItem
										key={ `item-${value.sort}` }
										index={ index }
										collection={ collectionIndex }
										itemIndex={ index }
										collectionKey={ collectionIndex }
										value={ value }
									/>
								))
							}
						</div>
					</>
				))
			}
		</div>
	));

	const onSortEnd = ({ oldIndex, newIndex, collection }) => {
		const newList = [...fieldList];
		newList[collection] = arrayMoveImmutable(newList[collection], oldIndex, newIndex).filter(el => !!el);
		setFieldList(newList);
		onSaveField(newList);
	};

	const handleSelectAll = () => {
		const newList = [...fieldList];
		newList.forEach((items) => {
			items.forEach((i) => {
				i.selected = !checkAllStatus;
			});
		});
		setFieldList(newList);
		onSaveField(newList);
	};

	const handleFilterRefund = (e: CheckboxChangeEvent) => {
		const checked = e.target.checked;
		console.log('%c [ checked ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', checked);
		
		setFilterRefund(checked);
		local.set('Trade.BatchExportFilterRefund', checked);
	};

	const checkAllStatus = useMemo(() => {
		return fieldList.flat().every(i => i.selected);
	}, [fieldList]);

	const indeterminate = useMemo(() => {
		const list = fieldList.flat();
		const checkList = list.filter(i => i.selected);
		return checkList.length > 0 && checkList.length < list.length;
	}, [fieldList]);
	return (
		<div>
			<Modal
				centered
				title={ (
					<div className="r-flex">
						批量导出
					</div>
				) }
				style={ {
					minHeight: '400'
				} }
				width={ 900 }
				okText="导出"
				cancelText="恢复默认"
				visible
				onCancel={ () => {
					setIsShowBatchExportModal(false);
				} }
				maskClosable={ false }
				destroyOnClose
				footer={ null }
			>
				<Spin spinning={ loading }>
					<div>
						<span>导出方式：</span>
						<Radio.Group onChange={ exportMethodOnChange } value={ exportMethod }>
							<Radio type="radio" value={ BatchExportMethod.导出勾选订单 } className="r-mb-10" >导出勾选订单</Radio>
							<Radio type="radio" value={ BatchExportMethod.导出当前查询结果 } className="r-mb-10" >导出当前查询结果</Radio>
						</Radio.Group>
					</div>
					<div>
						<span>导出类型：</span>
						<Radio.Group onChange={ exportTypeChange } value={ exportType }>
							<Radio type="radio" value={ BatchExportType.订单维度 } className="r-mb-10" >按订单维度</Radio>
							<Radio type="radio" value={ BatchExportType.商品维度 } className="r-mb-10 r-ml-14" >按商品维度</Radio>
						</Radio.Group>
					</div>
					<div className="r-mb-10">
						<span>过滤设置：</span>
						<Checkbox onChange={ handleFilterRefund } checked={ filterRefund }>过滤退款成功商品</Checkbox>
					</div>
					<div className="r-mb-20">
						<span>导出字段：</span>
						<Checkbox onChange={ handleSelectAll } checked={ checkAllStatus } indeterminate={ indeterminate }>全选</Checkbox>
					</div>
					<div
						style={ {
							maxHeight: 500,
							overflowY: 'auto'
						} }
					>
						<SortableList
							lockToContainerEdges
							axis="xy"
							useDragHandle
							collections={ fieldList }
							onSortEnd={ onSortEnd }
							helperClass={ s["row-dragging"] }
						/>
					</div>
					<div className="r-flex r-mt-20 r-jc-sb">
						<span className="r-lh-30">导出说明：单次导出上限2万条；超过2万条默认按下单时间排序导出，最多50万条</span>
						<div>
							<Button onClick={ () => setIsShowBatchExportModal(false) } className="r-mr-10">取消</Button>
							<Button onClick={ handleReset } className="r-mr-10">恢复默认</Button>
							<Button onClick={ handleOk } type="primary">导出 </Button>
						</div>
					</div>
				</Spin>
			</Modal>
		</div>
	);
});

export default BatchExportModal;
