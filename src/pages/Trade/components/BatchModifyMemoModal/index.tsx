import { Button, Input, Modal, Radio, Select, Alert, Tabs, Tooltip, Checkbox } from "antd";
import React, { useEffect, useState, useMemo } from "react";
import dayjs from 'dayjs';
import { observer } from "mobx-react";
import _ from "lodash";
import { useHistory } from "react-router-dom";
import cs from 'classnames';
import message from '@/components/message';
import { tradeStore } from "@/stores";
import Icon from "@/components/Icon";
import userStore from "@/stores/user";
import { IndexRemarkGetCommonRemarkApi, TradeSellerInfoGetSellerFlagListApi } from "@/apis/user";
import {
	TradeSellerInfoGetSellerFlagListRequest
} from "@/types/schemas/user";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import styles from './index.module.scss';
import { PLAT_ALI, PLAT_JD,flagArr, allFlagArr, PLAT_ALL_SCM, PLAT_KTT } from "@/constants";
import HistoryMemo from './HistoryMemo';
import FlagTagModal from './FlagTag';
import { local } from "@/libs/db";

export interface IBatchModifyMemoModalProps {
	fromPage?: string,
	onChange?: Function,
	onOk?: Function,
	onCancel?: Function,
}

export const enum ModifyMemoType {
	'添加至原备注之后' = 1,
	'覆盖原备注' = 2,
	'添加至原备注之前' = 3,
}
const localKey = 'LOCAL_MODIFY_MEMO_TYPE';

const settingOptions = [{
	label: '自动添加备注人',
	value: 'autoAddName'
}, {
	label: '自动添加备注时间',
	value: 'autoAddTime'
}]

const LOCAL_MODIFY_AUTO_SETTING = ['autoAddName', 'autoAddTime'];

const localSettingKey = 'LOCAL_MODIFY_AUTO_SETTING'

const allowList = ['tb', 'tm'];

const { Option } = Select;

const BatchModifyMemoModal = observer((props: IBatchModifyMemoModalProps) => {
	const { setIsShowBatchModifyMemoModal, modifyMemoPackage, tradeOptStore: { memoUpdating, setMemoUpdating } } = tradeStore;
	const { fromPage = "", onChange, onOk, onCancel } = props;
	const history = useHistory();
	const [params, setParams] = useState({
		type: ModifyMemoType.添加至原备注之后,
		flag: -1,
		memo: '',
		memoListIndex: -1,
		source: "",
		thisFlagTagList: [], // 当前旗帜的旗帜店铺备注列表
	});
	const [isBatch, setIsBatch] = useState(true);
	const [memoList, setMemoList] = useState([]);
	const [tagData, setTagData] = useState({});
	const [isShowHistory, setIsShowHistory] = useState(false); // 是否显示历史备注
	const [isShowFlagTag, setIsShowFlagTag] = useState(false); // 是否显示旗帜标签
	const [flagTagVisible, setFlagTagVisible] = useState(false); // 是否显示旗帜标签弹框
	const [flagTagIndex, setFlagTagIndex] = useState(''); // 是否显示旗帜标签弹框
	const [activeKey, setActiveKey] = useState('1'); // 是否显示历史备注
	const [autoSetting, setAutoSetting] = useState(LOCAL_MODIFY_AUTO_SETTING);
	const [userName, setUserName] = useState('');
	const [isShowMoreFlag, setIsShowMoreFlag] = useState(false);
	const [isShowMoreFlagBtn, setIsShowMoreFlagBtn] = useState(false);

	useEffect(() => {
		userStore.getUserInfo().then(async res => {
			console.log('user', res)
			const { userId, userName, subUserName } = res;
			setUserName(subUserName || userName);
			const { list } = await IndexRemarkGetCommonRemarkApi({ userId, pageNo: 1, pageSize: 100 }) || {};
			setMemoList(list.filter(item => item.enableStatus));
		});
		const {userId,subUserId} = userStore.userInfo;
		const localType = local.get(localKey) || local.get(localKey + `_${userId}_${subUserId}`);
		if (local.get(localSettingKey)) {
			setAutoSetting(local.get(localSettingKey));
		}
		handleChange(localType || ModifyMemoType.添加至原备注之后, 'type');
	}, []);

	console.log('%c [ modifyMemoPackage ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', modifyMemoPackage);

	useEffect(() => {
		const { userId, subUserId = '' } = userStore.userInfo;
		if (fromPage === 'scanPrint') {
			// 这里的数据结构不一样，modifyMemoPackage代表合单
			if (modifyMemoPackage?.length <= 1 && allowList?.includes(modifyMemoPackage?.[0]?.takeGoodsLabelInfos?.[0]?.platform)) {
				setIsShowHistory(true);
			}
			if (modifyMemoPackage?.some(i => allowList?.includes(i?.takeGoodsLabelInfos?.[0]?.platform))) {
				setIsShowFlagTag(true);
			}

			// 非合单，不显示修改备注形式
			if (modifyMemoPackage?.length <= 1) {
				let pack = modifyMemoPackage[0];
				setParams(prev => ({
					...prev,
					source: pack.source,
					memo: pack?.sellerMemo || '', // 这里可能没返回
					flag: +pack.sellerFlag || -1,
					thisFlagTagList: tagData?.[`${+pack.sellerFlag || -1}`] || [],
					type: ModifyMemoType.添加至原备注之后,
				}));
				setIsBatch(false);
			} else {
				setParams(prev => ({
					...prev,
					type: local.get(localKey) || local.get(localKey + `_${userId}_${subUserId}`) || ModifyMemoType.添加至原备注之后,
				}));
				setIsBatch(true);
			}
		} else {
			// modifyMemoPackage代表多笔勾选合单
			if (modifyMemoPackage?.length <= 1 && modifyMemoPackage[0]?.trades?.length <= 1 && allowList?.includes(modifyMemoPackage[0]?.platform)) {
				setIsShowHistory(true);
			}
			if (modifyMemoPackage?.some(i => allowList?.includes(i.platform))) {
				setIsShowFlagTag(true);
			}

			// 非合单，不显示修改备注形式
			if (modifyMemoPackage?.length <= 1 && modifyMemoPackage[0]?.trades?.length <= 1) {
				let trade = modifyMemoPackage[0].trades[0];
				setParams(prev => ({
					...prev,
					memo: trade.sellerMemo,
					source: trade.source,
					flag: +trade.sellerMemoFlag || -1,
					thisFlagTagList: tagData?.[`${+trade.sellerMemoFlag || -1}`] || [],
					type: ModifyMemoType.添加至原备注之后,
				}));
				setIsBatch(false);
				sendPoint(Pointer.订单_订单打印_订单操作_订单备注_单笔备注_展现);
			} else {
				setParams(prev => ({
					...prev,
					type: local.get(localKey) || local.get(localKey + `_${userId}_${subUserId}`) || ModifyMemoType.添加至原备注之后,
				}));
				sendPoint(Pointer.订单_订单打印_订单操作_订单备注_批量备注_展现);
			}
		}
		if (fromPage === 'scanPrint') {
			setIsShowMoreFlagBtn(modifyMemoPackage.every(item => allowList.includes(item?.takeGoodsLabelInfos?.[0]?.platform)));
		} else {
			setIsShowMoreFlagBtn(modifyMemoPackage.every(item => allowList.includes(item.platform)));
		}
	}, [modifyMemoPackage, tagData]);

	// 检查是否所有订单都是快团团平台
	const isAllKuaituantuanPlatform = useMemo(() => {
		if (!modifyMemoPackage || modifyMemoPackage.length === 0) {
			return false;
		}

		if (fromPage === 'scanPrint') {
			// 扫描打印页面：检查 takeGoodsLabelInfos[0].platform
			return modifyMemoPackage.every(item =>
				item?.takeGoodsLabelInfos?.[0]?.platform === PLAT_KTT
			);
		} else {
			// 普通页面：检查 platform
			return modifyMemoPackage.every(item => item.platform === PLAT_KTT);
		}
	},[modifyMemoPackage, fromPage]);

	const onSettingChange = (e) => {
		setAutoSetting(e);
		local.set(localSettingKey, e);
	}

	const onMemoUpdateWarnningModalOk = () => {
		const allTradWasAli = modifyMemoPackage?.every(i => i.platform === PLAT_ALI || i.platform === PLAT_JD);
		// 如果多选的全是1688订单，则直接关闭当前弹窗,不然直接触发接口更新备注
		if (!allTradWasAli) {
			tradeStore.tradeOptStore.updateMemo(paramsMemoFormat(), isBatch);
		}
	};

	const showMemoUpdateWarnningModal = (orderList:string[]) => {
		Modal.warning({
			centered: true,
			width: 420,
			title: '提示',
			content: <div>{getModalContent()}</div>,
			okText: '确定',
			className: styles['tid-list-modal'],
			onOk: onMemoUpdateWarnningModalOk
		});
		function getModalContent() {
			const content = orderList.map((tid, index) => {
				return (
					<div key={ tid }>
						<span className="r-fw-500">
							{tid}
							{index + 1 === orderList.length ? '' : '，'}
						</span>
						{index % 2 !== 0 ? <br /> : ''}
					</div>
				);
			});
			return (
				<div>
					<div>
						<Alert message="以下订单需设置旗帜后保存，1688平台规定订单修改备注必须设置非灰、紫旗的旗帜" type="warning" />
					</div>
					<div className={ cs(styles['tid-list-container'], 'r-mt-12') }>
						{content}
					</div>
				</div>
			);
		}
	};

	const handleChange = (val: any, type: string) => {
		setParams(prev => {
			prev[type] = val;
			if (type === 'memoListIndex') {
				if (isBatch) {
					prev.memo = prev.memoListIndex === -1 ? '' : memoList[prev.memoListIndex].content;
				} else if (fromPage == 'scanPrint') {
					prev.memo = prev.memoListIndex === -1 ? modifyMemoPackage?.[0]?.sellerMemo : memoList[prev.memoListIndex].content;
				} else {
					prev.memo = prev.memoListIndex === -1 ? modifyMemoPackage[0].trades[0].sellerMemo : memoList[prev.memoListIndex].content;
				}
			}
			if (type == 'flag') {
				prev['thisFlagTagList'] = tagData?.[`${val}`] || [];
			}
			return { ...prev };
		});
	};

	const paramsMemoFormat = () => {
		const sendParams = { ...params };
		if (params.memo?.trim()) {
			if (autoSetting.includes('autoAddName')) {
				sendParams.memo = `${sendParams.memo||''}【${userName}】`;
			}
			if (autoSetting.includes('autoAddTime')) {
				sendParams.memo = `${sendParams.memo||''}【${dayjs().format('MM-DD HH:mm:ss')}】`;
			}
		}
		sendParams.memo = sendParams.memo||''
		return sendParams;
	}

	const handleOk = () => {
		//缓存备注选项
		const { userId, subUserId ='' } = userStore.userInfo;
		// 1688平台修改备注必须强制选择灰旗以外的旗帜
		let orderList: string[] = [];
		const sendParams = paramsMemoFormat();
		if (fromPage === 'scanPrint') {
			modifyMemoPackage?.forEach(i => {
				if (i?.takeGoodsLabelInfos?.[0]?.platform === PLAT_ALI) {
					const tidList = i.togetherId?.split('|');
					orderList = orderList.concat(tidList);
				}
			});
			setMemoUpdating(true);
			console.log('params:::', sendParams, isBatch, modifyMemoPackage);
			if (orderList.length > 0) {
				if ([0, -1, 5].includes(sendParams.flag)) {
					setMemoUpdating(false);
					message.error('1688平台规定订单修改备注必须设置非灰、紫旗的旗帜，请设置旗帜后保存');
					return;
				} else if (!sendParams.memo?.trim()) {
					setMemoUpdating(false);
					message.error('1688平台规定订单备注必填');
					return;
				}
			}
			tradeStore.tradeOptStore.scanPrintUpdateMemo(sendParams, modifyMemoPackage).then((res) => {
				onChange && onChange(sendParams);
				setIsShowBatchModifyMemoModal(false);
				setMemoUpdating(false);
			}).catch((err) => {
				setIsShowBatchModifyMemoModal(false);
				setMemoUpdating(false);
			});
		} else {
			// 收集当前勾选订单所属平台
			let checkOrderPlatform = []
			modifyMemoPackage?.forEach(i => {
				if (i.platform === PLAT_ALI || i.platform === PLAT_JD) {
					const tidList = i.togetherId?.split('|');
					orderList = orderList.concat(tidList);
					if(!checkOrderPlatform.includes([i.platform]) )  checkOrderPlatform.push(i.platform)
				}
			});
			if (orderList.length > 0 && [0, -1, 5].includes(sendParams.flag) && checkOrderPlatform.includes(PLAT_ALI)) {
				// 如果只选择一个1688订单，则不需要弹窗提示，只需要message提示即可
				if (modifyMemoPackage.length === 1) {
					message.error('1688平台规定订单修改备注必须设置非灰、紫旗的旗帜，请设置旗帜后保存');
				} else {
					showMemoUpdateWarnningModal(orderList);
				}
				return
			}
			if (orderList.length > 0 && !sendParams.memo?.trim()) {
				let platformName = '1688'
				if(checkOrderPlatform.includes(PLAT_JD) && !checkOrderPlatform.includes(PLAT_ALI)) platformName = '京东'
				if(checkOrderPlatform.includes(PLAT_ALI) && !checkOrderPlatform.includes(PLAT_JD)) platformName = '1688'
				if(checkOrderPlatform.includes(PLAT_ALI) && checkOrderPlatform.includes(PLAT_JD)) platformName = '1688、京东'
				message.error(`${platformName}平台规定订单备注必填`);
				return
			}
			tradeStore.tradeOptStore.updateMemo(sendParams, isBatch, onOk);
		}
		local.set(localKey + `_${userId}_${subUserId}`, params.type);
		local.remove(localKey)
	};

	const goToSetting = () => {
		sendPoint(Pointer.订单_订单打印_订单操作_订单备注_设置常用备注);
		setIsShowBatchModifyMemoModal(false);
		history.push('/settings/memo');
	};

	const onTabChange = (key: string) => {
		console.log(key);
		setActiveKey(key);
	};

	// 点击旗帜标签icon
	const handleShowTagInfo = (event, flagIndex:string) => {
		event.stopPropagation(); // 阻止事件冒泡
		setFlagTagIndex(flagIndex);
		setFlagTagVisible(true);
	};

	const handleFlagtagClose = () => {
		setFlagTagIndex('');
		setFlagTagVisible(false);
	};

	const getetSellerFlagList = async() => {
		let list = [];

		if (fromPage == 'scanPrint') {
			modifyMemoPackage?.forEach(i => {
				if (allowList?.includes(i?.takeGoodsLabelInfos?.[0]?.platform)) {
					list.push({
						sellerId: i?.takeGoodsLabelInfos?.[0]?.sellerId,
						platform: i?.takeGoodsLabelInfos?.[0]?.platform,
					});
				}
			});
		} else {
			modifyMemoPackage?.forEach(i => {
				if (allowList?.includes(i?.platform)) {
					list.push({
						sellerId: i?.sellerId,
						platform: i?.platform,
					});
				}
			});
		}
		if (!list?.length) {
			return;
		}

		// 去重
		const uniqueSellers = list.filter((seller, index, self) => index === self.findIndex((s) => (
			s.sellerId === seller.sellerId && s.platform === seller.platform
		)));

		let params:TradeSellerInfoGetSellerFlagListRequest = {
			sellerFlagInfoList: uniqueSellers
		};
		let res = await TradeSellerInfoGetSellerFlagListApi(params);

		// 处理成每个旗帜的tag数据
		let obj = res?.reduce((acc, seller) => {
			// 如果acc中还没有这个sellerFlag的键，先创建一个空数组
			if (!acc[seller.sellerFlag]) {
				acc[seller.sellerFlag] = [];
			}
			// 将当前的seller对象添加到对应的数组中
			acc[seller.sellerFlag].push(seller);
			return acc;
		}, {});
		setTagData(obj || {});
	};

	const getMemoDom = () => {
		return (
			<div>
				{isBatch ? (
					<Radio.Group
						className="r-flex r-jc-sb r-mb-12"
						onChange={ (e) => {
							handleChange(e.target.value, 'type');
							// local.set(localKey, e.target.value);
						} }
						value={ params.type }
					>
						<Radio value={ ModifyMemoType.添加至原备注之后 }>添加至原备注之后</Radio>
						<Radio value={ ModifyMemoType.覆盖原备注 }>覆盖原备注</Radio>
						<Radio value={ ModifyMemoType.添加至原备注之前 }>添加至原备注之前</Radio>
					</Radio.Group>
				) : ''}
				<div className="r-flex r-mb-12">
					<Select
						size="small"
						getPopupContainer={ (e) => e.parentElement }
						style={ { width: 350 } }
						value={ params.memoListIndex }
						dropdownMatchSelectWidth={ false }
						onChange={ (e) => { sendPoint(Pointer.订单_订单打印_订单操作_订单备注_选择备注短语); handleChange(e, 'memoListIndex'); } }
					>
						<Option value={ -1 }>请选择备注短语</Option>
						{memoList.map((item, index) => (
							<Option key={ item.id } style={ { width: 350 } } value={ index }>{item.content}</Option>
						))}
					</Select>
					<Button size="small" type="primary" onClick={ goToSetting } className="r-ml-12">设置常用备注</Button>
				</div>

				<Input.TextArea
					style={ {
						height: 100
					} }
					value={ params.memo }
					placeholder="请输入备注内容（1688、京东平台需必填；快团团平台不支持同步至线上）"
					onInput={ (e) => { handleChange(e.target.value, 'memo'); } }
				/>
				<div style={{marginTop: '10px'}}>
					<Checkbox.Group options={ settingOptions } value={autoSetting} onChange={ onSettingChange } />
				</div>

				{
					!isAllKuaituantuanPlatform && (
						<div className="r-flex r-mt-12" style={{ flexWrap: 'wrap', justifyContent: 'space-between' }}>
							{(isShowMoreFlag ? allFlagArr : flagArr).map((item, index) => (
								<div
									key={ item.color }
									className={ `modify-memo-flag-item r-pointer ${params.flag === index ? 'active' : ''}` }
									onClick={ () => {
										handleChange(index === params.flag ? -1 : index, 'flag');
									} }
									style={{marginRight: '8px', marginBottom: '8px'}}
								>
									<Icon type="flag1" style={ { color: item.color, marginRight: 4 } } />
									{item.name}
									{
										isShowFlagTag && tagData?.[`${index}`]?.length > 0	&& (
											<Tooltip placement="topLeft" title="旗帜标签" arrowPointAtCenter>
												<span onClick={ (e) => handleShowTagInfo(e, `${index}`) }>
													<Icon type="biaoqianshezhi" style={ { color: '#FD8204' } } />
												</span>
											</Tooltip>
										)
									}

								</div>
							))}
							{
								isShowMoreFlagBtn && (
									<a className="r-pointer r-c-primary r-block" style={{ height: 32, lineHeight: '32px' }} onClick={() => {
										setIsShowMoreFlag(!isShowMoreFlag);
									}}>
										{isShowMoreFlag ? '收起' : '更多'}
									</a>
								)
							}
						</div>
					)
				}

				{
					isShowFlagTag && Object.keys(tagData)?.length > 0 && (
						<div className={ styles.tagTip }>
							注：旗帜标签仅淘宝/天猫订单生效，将根据店铺设置内容同步至平台
						</div>
					)
				}
			</div>
		);
	};


	useEffect(() => {
		console.log(modifyMemoPackage);
		if (isShowFlagTag && modifyMemoPackage?.length && modifyMemoPackage.every(item => !PLAT_ALL_SCM.includes(item.source))) {
			getetSellerFlagList();
		}
	}, [isShowFlagTag, modifyMemoPackage, fromPage]);

	return (
		<>
			<Modal
				width={ 570 }
				centered
				title={ (
					<div className="r-flex">
						批量修改备注
						{modifyMemoPackage?.length
							? (
								<div>
									(<span className="r-c-error">{modifyMemoPackage.length}</span>行)
								</div>
							)
							: ''}
					</div>
				) }
				okText="保存修改"
				visible
				onCancel={ () => {
					setIsShowBatchModifyMemoModal(false);
					onCancel?.();
				} }
				onOk={ handleOk }
				confirmLoading={ memoUpdating }
				destroyOnClose
				maskClosable={ false }
				className={ cs((isShowHistory && activeKey == '2') ? styles.historyView2 : isShowHistory ? styles.historyView : '') }
			>
				<div>
					{ isShowHistory ? (
						<Tabs activeKey={ activeKey } onChange={ onTabChange }>
							<Tabs.TabPane tab="订单备注" key="1">
								{getMemoDom()}
							</Tabs.TabPane>
							<Tabs.TabPane tab="历史备注" key="2">
								<HistoryMemo pack={ modifyMemoPackage[0] } fromPage={ fromPage } />
							</Tabs.TabPane>
						</Tabs>
					) : (
						<>
							{getMemoDom()}
						</>
					)}
				</div>
			</Modal>

			<FlagTagModal data={ tagData?.[`${flagTagIndex}`] } index={ flagTagIndex } close={ handleFlagtagClose } open={ flagTagVisible } />
		</>
	);
});

export default BatchModifyMemoModal;
