import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import s from "./index.module.scss";
import React from "react";
import cs from "classnames";
import Pointer from "@/utils/pointTrack/constants";
const arrowUpNode = (
	<div className={cs(s.arrow, s.isUp, s.demo)}>
		<CaretUpOutlined className={s.up} />
		<CaretDownOutlined className={s.down} />
	</div>
);

const arrowDownNode = (
	<div className={cs(s.arrow, s.isDesc, s.demo)}>
		<CaretUpOutlined className={s.up} />
		<CaretDownOutlined className={s.down} />
	</div>
);
export const sortInfo = {
	PAY_TIME: {
		title: "按订单付款时间排序",
		rule: (
			<>
				按照订单的付款时间排序，{arrowUpNode}代表时间从远到近，
				{arrowDownNode}代表从近到远。
			</>
		),
		example: (
			<>
				订单A支付时间为今天8点，订单B支付时间为今天9点。选择了{" "}
				{arrowDownNode} 则订单B排在前面
			</>
		),
		scene: "需要优先对最新支付的订单进行打单发货或者其他操作，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按订单付款时间,
	},
	ORDER_CREATED: {
		title: "按订单下单时间排序",
		rule: (
			<>
				按照订单的下单时间排序，{arrowUpNode}代表时间从远到近，
				{arrowDownNode} 代表从近到远。
			</>
		),
		example: (
			<>
				订单A下单时间为今天8点，订单B下单时间为今天9点。选择了{" "}
				{arrowDownNode} 则订单B排在前面
			</>
		),
		scene: "需要优先对最新下单的订单进行打单发货或者其他操作，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按订单下单时间,
	},
	LAST_SHIP_TIME: {
		title: "按剩余发货时间排序",
		rule: (
			<>
				按照订单的发货剩余时间多少排序，{arrowUpNode}代表时间从少到多，
				{arrowDownNode} 代表从多到少。
			</>
		),
		example: (
			<>
				订单A发货剩余时间为12小时，订单B发货剩余时间为11小时。选择了
				{arrowUpNode}则订单B排在前面
			</>
		),
		scene: "需要优先对剩余时间较少的订单进行打单发货或者其他操作，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按剩余发货时间,
	},
	PRINT_TIME: {
		title: "按打印时间排序",
		rule: (
			<>
				按照订单打印时间排序，{arrowUpNode}代表时间从远到近，
				{arrowDownNode} 代表从近到远
			</>
		),
		example: (
			<>
				订单A最后打印时间为今天8点，订单B最后打印时间为今天9点。选择了
				{arrowDownNode}则订单B排在前面
			</>
		),
		scene: "需要根据打印时间排序，按打印时间对照快递单进行配货的商家，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按打印时间排序,
	},
	PAYMENT: {
		title: "按订单实付金额排序",
		rule: (
			<>
				按照订单的实付金额排序，{arrowUpNode}代表金额从少到多，
				{arrowDownNode} 代表从多到少。
			</>
		),
		example: (
			<>
				订单A实收金额为30元，订单B实收金额为50元。选择了{arrowUpNode}
				则订单A排在前面。
			</>
		),
		scene: "希望按订单金额大小顺序处理订单，或者希望相同金额的订单统一处理的，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按订单实付金额排序,
	},
	ITEM_NUM: {
		title: "按订单宝贝数量排序",
		rule: (
			<>
				按照订单的商品数量排序，{arrowUpNode}代表数量从少到多，
				{arrowDownNode} 代表从多到少。
			</>
		),
		example: (
			<>
				订单A商品数量为 2 个，订单B商品数量为 5 个。选择了 {arrowUpNode}{" "}
				则订单A排在前面。
			</>
		),
		scene: "希望按订单数量大小顺序处理订单，或者希望相同数量的订单统一处理的，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按订单商品数量,
	},
	WEIGHT: {
		title: "按订单重量排序",
		rule: (
			<>
				按照订单重量排序，{arrowUpNode}代表重量从少到多，{arrowDownNode}{" "}
				代表从多到少。
			</>
		),
		example: (
			<>
				订单A商品重量为1kg，订单B商品重量为2kg，订单C商品重量为0。选择了
				{arrowUpNode} 则订单C排在最前，接下来是订单A，最后是订单B。
			</>
		),
		scene: "在系统内维护了商品重量，希望重量相同的订单一起处理的，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按订单重量_重的在前面,
	},
	ORDER_ITEM_ID: {
		title: "按商家编码",
		rule: (
			<>
				按照订单包含商品的商家编码排序，{arrowUpNode}
				代表商家编码从小到大，{arrowDownNode} 代表从大到小。
			</>
		),
		example: (
			<>
				订单A包含的商品编码是12345，订单B包含的商品编码是23456，订单C包含的商品没有编码。选择了
				{arrowUpNode}则订单C排在最前，接下来是订单A，最后是订单B
			</>
		),
		scene: "在商家后台发布商品时维护了商品编码，希望统一处理商品编码相同的订单，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按商家编码排序,
	},
	ORDER_OUTER_ID: {
		title: "按商品编码、SKU编码排序",
		rule: (
			<>
				按照订单包含商品的平台商家编码+规格编码排序，{arrowUpNode}
				代表商家编码从小到大，{arrowDownNode} 代表从大到小。
			</>
		),
		example: (
			<>
				订单A、B包含商品的商家编码为888,规格编码42，订单C的商家编码为888,规格编码56，订单D的商家编码为999,规格编码35,{" "}
				{arrowUpNode}则订单A、B、C排在一起
				。然后订单A和B的规格编码小于订单C，所以订单C排在订单A、B后面，D排在最后。
			</>
		),
		scene: "在商家后台发布商品时维护了商品编码和规格编码，希望统一处理商品编码+规格编码相同的订单，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按商品编码SKU编码排序,
	},
	TRADE_SKU_ID: {
		title: "按SKU编码排序",
		rule: (
			<>
				按照订单包含商品的规格编码排序，{arrowUpNode}
				代表规格编码从小到大，{arrowDownNode} 代表从大到小。
			</>
		),
		example: (
			<>
				订单A包含的商品规格编码是12345，订单B包含的商品规格编码是23456，订单C包含的商品没有编码。选择了
				{arrowUpNode}则订单C排在最前，接下来是订单A，最后是订单B
			</>
		),
		scene: "在商家后台发布商品时仅维护了规格编码，希望统一处理规格编码相同的订单，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按SKU编码排序,
	},
	RECEIVER_PROVINCE: {
		title: "按收件地址省份",
		rule: <>订单中的收件省份相同的排在一起。</>,
		example: "",
		scene: "希望相同省份的订单统一处理，推荐使用此排序。",
		point: Pointer.订单_订单打印_订单排序_按收件地址省份排序,
	},
	// "按宝贝简称+规格别名排序"
	ORDER_SYS_ITEM_ALIAS_SKU_ALIAS: {
		title: "按商品简称、规格别名排序",
		rule: (
			<>
				按照订单包含商品的简称+规格别名排序，{arrowUpNode}
				代表简称+规格别名从小到大，{arrowDownNode} 代表从大到小。
			</>
		),
		example: (
			<>
				订单A、B包含商品的简称为888,规格别名42，订单C的简称为888,规格别名56，订单D的简称为999,规格别名35,{" "}
				{arrowUpNode}则订单A、B、C排在一起
				。然后订单A和B的规格编码小于订单C，所以订单C排在订单A、B后面，D排在最后。
			</>
		),
		scene: "在系统内容维护了商品的简称和规格别名，希望统一处理简称+规格别名相同商品的订单，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按商品简称规格别名排序,
	},
	// "按商品简称+规格名称排序"
	ORDER_PLATFORM_ITEM_ID_SKU_NAME: {
		title: "按商品简称、规格名称排序",
		rule: (
			<>
				按照订单包含商品的简称+规格名称排序，{arrowUpNode}
				代表简称+规格名称从小到大，{arrowDownNode} 代表从大到小。
			</>
		),
		example: (
			<>
				订单A、B包含商品的简称为888,规格名称42，订单C的简称为888,规格名称56，订单D的简称为999,规格名称35,{" "}
				{arrowUpNode}则订单A、B、C排在一起
				。然后订单A和B的规格名称小于订单C，所以订单C排在订单A、B后面，D排在最后。
			</>
		),
		scene: "在系统内容维护了商品的简称，希望统一处理简称+规格名称相同商品的订单，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按商品简称规格名称排序,
	},
	//  "按商品+规格排序"
	TRADE_TITLE_SKU_PROPERTIES: {
		title: "按商品、规格排序",
		rule: (
			<>
				按照订单包含商品的商品标题+规格名称排序，{arrowUpNode}
				代表简称+规格别名从小到大，{arrowDownNode} 代表从大到小。
			</>
		),
		example: (
			<>
				订单A、B包含商品的商品标题为888,规格名称42，订单C的商品标题为888,规格名称56，订单D的商品标题为999,规格名称35,{" "}
				{arrowUpNode}则订单A、B、C排在一起
				。然后订单A和B的规格名称小于订单C，所以订单C排在订单A、B后面，D排在最后。
			</>
		),
		scene: "希望统一处理商品标题+规格名称相同商品的订单，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按商品规格排序,
	},
	// "按规格名称排序"
	TRADE_SKU_PROPERTIES: {
		title: "按规格名称排序",
		rule: (
			<>
				按照订单包含商品的规格名称排序，{arrowUpNode}
				代表规格名称从小到大，{arrowDownNode} 代表从大到小。
			</>
		),
		example: (
			<>
				订单A包含的规格名称是12345，订单B包含的规格名称是23456。选择了
				{arrowUpNode}则订单A排在最前，接下来是订单B
			</>
		),
		scene: "在商家后台发布商品时维护了规格名称，希望统一处理规格名称相同的订单，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按规格名称排序,
	},
	// "按货品规格编码排序"
	TRADE_SYS_SKU_PROPERTIES: {
		title: "按货品规格编码排序",
		rule: (
			<>
				按照订单包含商品关联的本地货品的货品规格编码排序，{arrowUpNode}
				代表商家编码从小到大，{arrowDownNode} 代表从大到小。
			</>
		),
		example: (
			<>
				订单A包含货品规格编码是12345，订单B包含的货品规格编码是23456，订单C没设置关联关系。选择了
				{arrowUpNode}则订单C排在最前，接下来是订单A，最后是订单B
			</>
		),
		scene: "在系统内维护了商品与货品的对应关系，希望统一处理商品货品规格编码相同的订单，推荐使用此排序",
		point: Pointer.订单_订单打印_订单排序_按货品规格编码排序,
	},
	// "按店铺商品ID排序"
	ORDER_PLATFORM_ITEM_ID_SKU_ID: {
		title: "按店铺商品ID排序",
		rule: (
			<>
				按照订单所在平台店铺进行排序，{arrowUpNode}代表店铺id从小到大，
				{arrowDownNode} 代表店铺id代表从大到小。
			</>
		),
		example:
			"有订单A、订单B、订单C、订单D , 订单A，订单D是M 店铺，订单B,订单C是 N店铺。A、D排一起,B、C排一起。",
		scene: "希望能按店铺排序，按店铺进行发货的商家",
		point: Pointer.订单_订单打印_订单排序_按店铺商品id排序,
	},
	// "按货品规格编码排序，数量由多到少"
	SAME_ITEM_SKU_OUTER_CATEGORY: {
		title: "按货品规格编码，数量由多到少排序",
		rule: (
			<>
				按照订单包含商品关联的本地货品的货品规格编码分许，再根据分组中的货品的数量又多到少排序。
			</>
		),
		example:
			"订单A含关联货品规格编码1344-32数量8，订单B含关联货品规格编码1344-32数量6 ， 订单C含关联货品规格编码2334-56数量10。使用此排序时，订单A、B中1344-32数量14 大于订单C2334-56的数量10，则订单A、B排在最前面，C排在后面",
		scene: "在系统内维护了商品与货品的对应关系，希望统一处理商品货品规格编码相同的订单，推荐使用此排序，然后先对商品数量多的订单进行先处理的商家",
		point: Pointer.订单_订单打印_订单排序_按货品规格编码数量由多到少排序,
	},
	// "相同商品挨在一起，数量由多到少"
	SAME_ITEM: {
		title: "相同商品挨在一起，数量由多到少",
		rule: (
			<>
				先将相同商品ID+规格ID对订单中的商品进行分组，再根据分组中总商品数量从多到少排序。
			</>
		),
		example: `订单A含商品规格1344-32数量8，订单B含商品ID规格ID1344-32数量6 ， 订单C含商品规格2334-56数量10。
		使用此排序时，订单A、B中1344-32数量14 大于订单C2334-56的数量10，则订单A、B排在最前面，C排在后面`,
		scene: "希望能将相同商品排一起，然后先对商品数量多的订单进行先处理的商家",
		point: Pointer.订单_订单打印_订单排序_相同商品挨在一起_数量由多到少,
	},
	// "相同商品挨在一起，区分颜色，数量由多到少"
	SAME_ITEM_COLOR: {
		title: "相同商品挨在一起，区分颜色，数量由多到少",
		rule: (
			<>
				先根据按平台商品商家编码组，按分组中商品数量多到少排序，同商品下按颜色分组，按分组中商品数量多到少排序
			</>
		),
		example: `订单A含商品编码LCNN-32，规格红色数量8，订单B含商品编码LCNN-32，规格红色数量6 ， 订单C含商品编码LCNN-32，规格蓝色数量10，订单D含商品编码MCNN-32，规格红色数量10。
		使用此排序时，订单A、B、C编码相同排一起，总数量24 排最前面，订单D排最后，订单A、B含LCNN-32，规格红色 总数量12 多于订单C LCNN-32，规格蓝色10 排在订单C前，最终排序为订单A、订单B、订单C、订单D`,
		scene: "希望能将相同商品排一起，区分颜色、尺码的商家然后先对商品数量多的订单进行先处理的商家，通常用户服饰类目商家",
		point: Pointer.订单_订单打印_订单排序_相同商品挨在一起_区分颜色_数量由多到少,
	},
	// "按宝贝简称、颜色、尺码从小到大排序"
	SAME_ITEM_ALIAS_CATEGORY_SIZE: {
		title: "按宝贝简称、颜色、尺码从小到大排序",
		rule: (
			<>
				先按商品简称分组，按分组中商品数量多到少排序，同商品简称下按颜色分组，按分组中商品数量多到少排序，同颜色下按尺码分组，按尺码（...,XXS,XS,S,M,L,XL,XXL,...）分组
			</>
		),
		example: `订单A含简称LCNN-32，红色L数量6，订单B含简称LCNN-32，红色M 数量8 ， 订单C含简称LCNN-32，蓝色L数量10，订单D含简称MCNN-32，红色XL，数量10。
		使用此排序时，订单A、B、C简称相同排一起，订单A、B 规格红色排一起，订单D排最后，A+B+C 简称LCNN-32数量24排D前，A+B规格颜色红色14大于C蓝色8排C前，B尺码M 在A尺码L前面，最终排序为订单B、订单A、订单C、订单D`,
		scene: "希望能将相同商品排一起，区分颜色、尺码的商家然后先对商品数量多的订单进行先处理的商家，通常用户服饰类目商家",
		point: Pointer[
			"订单_订单打印_订单排序_按宝贝简称、颜色，数量从多到少、尺码从小到大排序（S、M、L）"
		],
	},
	// "按商家编码、颜色、尺码从小到大排序"
	SAME_ITEM_OUTER_CATEGORY_SIZE: {
		title: "按商家编码、颜色、尺码从小到大排序",
		rule: (
			<>
				先按平台商家编码分组，按分组中商品数量多到少排序，同商品简称下按颜色分组，按分组中商品数量多到少排序，同颜色下按尺码分组，按尺码（...,XXS,XS,S,M,L,XL,XXL,...）分组
			</>
		),
		example: `订单A含商家编码LCNN-32，红色L数量6，订单B含商家编码LCNN-32，红色M 数量8 ， 订单C含商家编码LCNN-32，蓝色L数量10，订单D含商家编码MCNN-32，红色XL，数量10。
		使用此排序时，订单A、B、C商家编码相同排一起，订单A、B 规格红色排一起，订单D排最后，A+B+C 商家编码LCNN-32数量24排D前，A+B规格颜色红色14大于C蓝色8排C前，B尺码M 在A尺码L前面，最终排序为订单B、订单A、订单C、订单D`,
		scene: "希望能将相同商品排一起，区分颜色、尺码的商家然后先对商品数量多的订单进行先处理的商家，通常用户服饰类目商家",
		point: Pointer[
			"订单_订单打印_订单排序_按商家编码、颜色，数量从多到少、尺码从小到大排序（S、M、L）"
		],
	},
	// "按规格编码、颜色、尺码从小到大排序"
	SAME_ITEM_SKU_OUTER_CATEGORY_SIZE: {
		title: "按规格编码、颜色、尺码从小到大排序",
		rule: (
			<>
				先按规格商家编码分组，按分组中商品数量多到少排序，同商品简称下按颜色分组，按分组中商品数量多到少排序，同颜色下按尺码分组，按尺码（...,XXS,XS,S,M,L,XL,XXL,...）分组
			</>
		),
		example: `订单A含规格商家编码LCNN-32，红色L数量6，订单B含规格商家编码LCNN-32，红色M 数量8 ， 订单C含规格商家编码LCNN-32，蓝色L数量10，订单D含规格商家编码MCNN-32，红色XL，数量10。
		使用此排序时，订单A、B、C规格商家编码相同排一起，订单A、B 规格红色排一起，订单D排最后，A+B+C 规格商家编码LCNN-32数量24排D前，A+B规格颜色红色14大于C蓝色8排C前，B尺码M 在A尺码L前面，最终排序为订单B、订单A、订单C、订单D`,
		scene: "希望能将相同商品排一起，区分颜色、尺码的商家然后先对商品数量多的订单进行先处理的商家，通常用户服饰类目商家",
		point: Pointer[
			"订单_订单打印_订单排序_按规格编码、颜色，数量从多到少、尺码从小到大排序（S、M、L）"
		],
	},
};

export const hotKeyList = ["SAME_ITEM"];

export enum SortItemEnum {
	"TRADE_PLATFORM" = "平台",
	"TRADE_SELLER_ID" = "店铺ID",
	"TRADE_PAY_TIME" = "付款时间",
	"TRADE_CREATED_TIME" = "下单时间",
	"TRADE_PRINT_TIME" = "打印时间",
	"TRADE_LAST_SHIP_TIME" = "剩余发货时间",
	"TRADE_ITEM_NUM" = "商品数量",
	"TRADE_PAYMENT_AMOUNT" = "实付金额",
	"TRADE_WEIGHT" = "订单重量",
	"TRADE_PROVINCE" = "省份",
	"ORDER_ITEM_ID" = "商品ID",
	"ORDER_ITEM_NAME" = "商品名称",
	"ORDER_ITEM_ALIAS" = "商品简称",
	"ORDER_ITEM_OUTER_ID" = "商家编码",
	"ORDER_SKU_ID" = "规格ID",
	"ORDER_SKU_NAME" = "规格名称",
	"ORDER_SKU_OUTER_ID" = "规格编码",
	"ORDER_SKU_ALIAS" = "规格别名",
	"ORDER_SKU_COLOR_SIZE" = "颜色&尺码",
	"ORDER_SKU_COLOR" = "颜色",
	"ORDER_SKU_SIZE" = "尺码",
	"ORDER_CATEGORY_SIZE" = "尺码(S\\M\\L)",
	"ORDER_SYS_SKU_NAME" = "货品规格名称",
	"ORDER_SYS_SKU_OUTER_ID" = "货品规格编码",
	"TRADE_STORAGE" = "仓库名称",
	"ORDER_CLASSIFY_NAME" = "货品分类",
	"ORDER_WAREHOUSE_SLOT_NAME" = "货位",
	"ORDER_ITEM_NO" = "货号",
	"ORDER_BAR_CODE" = "条形码",
	// 新增市场、档口、供应商字段
	"ORDER_MARKET" = "市场",
	"ORDER_STALL" = "档口",
	"ORDER_SUPPLIER" = "供应商",
	// 库存版组合字段
	"ORDER_MARKET_STALL_SUPPLIER" = "供应商", //市场-档口-供应商排序  但是只展示供应商
}

// 定义库存版和零库存版特有的排序选项
export const stockVersionOnly = ["ORDER_MARKET_STALL_SUPPLIER"]; // 库存版特有
export const zeroStockVersionOnly = [
	"ORDER_MARKET",
	"ORDER_STALL",
	"ORDER_SUPPLIER",
]; // 零库存版特有

export const versionSortOnly = [
	"ORDER_SYS_SKU_NAME",
	"ORDER_SYS_SKU_OUTER_ID",
	"ORDER_WAREHOUSE_SLOT_NAME",
	"ORDER_ITEM_NO",
	"ORDER_BAR_CODE",
];

export const customSortOptions = Object.keys(SortItemEnum).map((item) => {
	return {
		value: item,
		label: SortItemEnum[item],
	};
});

export const CUSTOM_RULE_ARR = [1, 2, 3, 4, 5];
export const CUSTOM_RULE_NAME = (item) => `customOrderRule${item}`;
export enum TYPE_ENUM {
	系统 = 1,
	自定义 = 2,
}
