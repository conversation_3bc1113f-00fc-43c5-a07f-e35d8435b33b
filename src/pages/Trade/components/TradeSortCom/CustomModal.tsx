import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Table, Modal, Form, Input, Select, Button } from "antd";
import _ from "lodash";
import { useForm } from "antd/lib/form/Form";
import { observer } from "mobx-react";
import s from "./index.module.scss";
import message from "@/components/message";
import {
	delCustomQueryTradeOrderApi,
	saveCustomQueryTradeOrderApi,
} from "@/apis/trade";
import {
	CUSTOM_RULE_ARR,
	CUSTOM_RULE_NAME,
	customSortOptions,
	versionSortOnly,
	stockVersionOnly,
	zeroStockVersionOnly,
} from "./utils";
import userStore from "@/stores/user";
import { QueryTradeOrderDTO } from "@/types/trade/index";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";

export enum OperateEnum {
	"删除",
	"新增",
	"编辑",
}

interface SortModalProps {
	visible: boolean;
	editItem?: QueryTradeOrderDTO;
	onCancel?: () => void;
	onRefresh?: (k: OperateEnum) => void;
}

function SortModal(props: SortModalProps) {
	const { visible } = props;
	const [loading, setLoading] = useState(false);
	const [values, setValues] = useState([]);
	const [form] = useForm();

	const isEdit = useMemo(() => !!props.editItem?.key, [props.editItem]);
	useEffect(() => {
		if (!visible) return;
		form.resetFields();
		if (isEdit) {
			form.setFieldsValue({
				customOrderRule: "customOrderRule",
				...props.editItem,
			});
		} else {
			form.setFieldsValue({ customOrderRule: "customOrderRule" });
		}
	}, [form, isEdit, props.editItem, visible]);

	const onClose = () => {
		props?.onCancel?.();
	};

	const handleOk = async () => {
		if (!isEdit) {
			sendPoint(Pointer.排序设置_点击_确定新增);
		}
		const validRes = await form.validateFields();
		console.log(validRes, "validRes");
		const values = form.getFieldsValue();

		if (isEdit) {
			values.key = props.editItem.key;
		}
		delete values.customOrderRule;
		setLoading(true);
		try {
			await saveCustomQueryTradeOrderApi(values);
			props?.onRefresh?.(isEdit ? OperateEnum.编辑 : OperateEnum.新增);
			onClose();
		} catch (error) {
			console.log(error);
		}
		setLoading(false);
	};

	const deleteItem = async () => {
		sendPoint(Pointer.排序设置_点击_删除);
		await delCustomQueryTradeOrderApi(props.editItem);
		message.success("删除成功");
		props?.onRefresh?.(OperateEnum.删除);
		onClose();
	};

	const footerNode = (
		<>
			<div className="r-flex r-jc-sb r-ai-c">
				{isEdit ? (
					<div className="r-click" onClick={deleteItem}>
						删除自定义排序
					</div>
				) : (
					<div />
				)}
				<div>
					<Button onClick={onClose}>取消</Button>
					<Button type="primary" onClick={handleOk} loading={loading}>
						{isEdit ? "确认修改" : "确认新增"}
					</Button>
				</div>
			</div>
		</>
	);

	const customSortOptionsMemo = useMemo(() => {
		// 过滤掉不适用于当前版本的选项
		if (userStore.isShowZeroStockVersion) {
			// 零库存版：过滤掉库存版特有选项和versionSortOnly
			return customSortOptions
				.filter(
					(i) =>
						!versionSortOnly.includes(i.value) &&
						!stockVersionOnly.includes(i.value)
				)
				?.map((i) => {
					if (i.value == "ORDER_CLASSIFY_NAME") {
						return {
							...i,
							label: "商品分类",
						};
					}
					return i;
				});
		} else {
			// 库存版：过滤掉零库存版特有选项
			return customSortOptions.filter(
				(i) => !zeroStockVersionOnly.includes(i.value)
			);
		}
	}, [userStore.isShowZeroStockVersion]);

	const onValuesChange = (v, allValues) => {
		setValues(() => Object.values(allValues));
		form.validateFields([
			"customOrderRule1",
			"customOrderRule2",
			"customOrderRule3",
			"customOrderRule4",
			"customOrderRule5",
		]);
	};
	const alreadySet = useCallback(
		(key) => {
			return values.filter((s) => s && s == key)?.length > 1;
		},
		[values]
	);

	return (
		<Modal
			getContainer={document.body}
			centered
			visible={visible}
			width={558}
			onCancel={onClose}
			destroyOnClose
			title={<>{isEdit ? "编辑" : "新增"}自定义排序</>}
			footer={footerNode}
		>
			<Form form={form} onValuesChange={onValuesChange}>
				<Form.Item
					label="排序名称"
					name="name"
					rules={[{ required: true, message: "请输入排序规则名称" }]}
				>
					<Input placeholder="请输入排序规则名称" maxLength={20} />
				</Form.Item>
				<Form.Item
					label="排序规则"
					name="customOrderRule"
					rules={[{ required: true }]}
				>
					<div className={s.rule}>
						<div>1. 选择需要排序的字段，按优先级顺序进行设置</div>
						<div>
							2.
							多笔订单符合同一优先级，默认按下一优先级排序，以此类推
						</div>
					</div>
					{CUSTOM_RULE_ARR.map((item, index) => {
						return (
							<div className={s.ruleItem}>
								<div className={s.addIcon}>优先级-{item}</div>
								<Form.Item
									name={CUSTOM_RULE_NAME(item)}
									rules={[
										{
											validator(rule, value, callback) {
												const allValues =
													form.getFieldsValue();
												if (item == 1) {
													if (
														CUSTOM_RULE_ARR.every(
															(key) =>
																!allValues[
																	CUSTOM_RULE_NAME(
																		key
																	)
																]
														)
													) {
														callback(
															"未设置排序规则"
														);
													}
												}
												if (alreadySet(value)) {
													callback("规则重复");
												}
												callback();
											},
										},
									]}
								>
									<Select
										showSearch
										optionFilterProp="label"
										className={s.addSelect}
										options={customSortOptionsMemo}
										allowClear
									/>
								</Form.Item>
							</div>
						);
					})}
				</Form.Item>
			</Form>
		</Modal>
	);
}

export default observer(SortModal);
