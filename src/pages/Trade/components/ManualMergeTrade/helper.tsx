/* eslint-disable react/jsx-closing-tag-location */
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React from "react";
import { runInAction } from "mobx";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { groupBy } from "lodash";
import { TradeManualMergeTradeApi, TradeManualSplitMergeTradeApi } from "@/apis/trade";
import ManualMergeTrade from ".";
import { IPackage } from "../../interface";
import { handlePackageList, isPackCheckDisable } from "../../utils";
import { TradeStatus } from "@/utils/enum/trade";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import { PLAT_FXG, PLAT_KS, PLAT_TB, PLAT_TM } from "@/constants";
import { TradeManualSplitMergeTradeRequest } from '@/types/trade/index';
import styles from './index.module.less';

export enum MergeAction {
	合并 = 'mergeTrade'
}

export interface MergeTrade {
	trade: IPackage,
	action: MergeAction
}

export class ManualMergeTradeHelper {
	static rowKey = ['platform', 'mergeBuyerNick', 'sellerId'];

	static getRowKey = (trade: IPackage) => {
		if (trade.platform == PLAT_KS) {
			return [...ManualMergeTradeHelper.rowKey, 'consolidateType'].map(k => trade[k]).join('_');
		}
		return ManualMergeTradeHelper.rowKey.map(k => trade[k]).join('_');
	}

	static rowValue = 'mergeTrades';

	private tableRef = React.createRef();

	public getMergeTradeMap(packages: IPackage[] = [], cur?: IPackage) {
		const mergeTradeMap = { };
		packages?.forEach(p => {
			const key = ManualMergeTradeHelper.getRowKey(p);
			if (!mergeTradeMap[key]) mergeTradeMap[key] = [];
			if (p.trades.filter(t => (
				t.status === TradeStatus.等待卖家发货 || t.status === TradeStatus.卖家部分发货)
				&& !p.isPending
				&& !p.isPreShip).length === p.trades.length
				&& !isPackCheckDisable(p)
			) {
				mergeTradeMap[key].push(p);
			}
		});

		if (cur) {
			const arr = mergeTradeMap[ManualMergeTradeHelper.getRowKey(cur)];
			return arr.filter((a: IPackage) => a.togetherId === cur.togetherId).length ? arr : [];
		}
		return mergeTradeMap;
	}

	public tradeFactory = (packages: IPackage[]) => {
		// 获取所有合并订单集合
		const mergeTradeMap = this.getMergeTradeMap(packages);
		runInAction(() => {
			packages.forEach((p: IPackage, index: number) => {
				const arr = mergeTradeMap[ManualMergeTradeHelper.getRowKey(p)];
				const mergeArr = arr.filter((a: IPackage) => a.togetherId === p.togetherId).length ? arr : [];
				if (!p[ManualMergeTradeHelper.rowValue] || p[ManualMergeTradeHelper.rowValue].length !== mergeArr.length) {
					// packages[index] = _.cloneDeep(p);
					packages[index][ManualMergeTradeHelper.rowValue] = mergeArr;
				}
			});
		});
		return packages;
	}

	private async resetPrintList(mainTrade: IPackage, mergeTrade: IPackage[], newTrade: IPackage) {
		// 如果是临时合单，前端清空智选快递
		const isTemporaryMerge = ![PLAT_TB, PLAT_TM, PLAT_FXG, PLAT_KS].includes(newTrade.platform);
		if (isTemporaryMerge) {
			newTrade.packSmartExpressTemplate = "";
			newTrade.smartExpressTemplate = "";
		}
		// 手动将原有异常状态带入子单中
		newTrade.trades.forEach(t => {
			let temp = mergeTrade.filter(mt => mt.tids.includes(t.tid));
			t.abnormalAddress = temp?.[0]?.abnormalAddress || false;
			// t.epidemicMark = temp?.[0]?.epidemicMark;
		});
		// newTrade.epidemicMark = mainTrade.epidemicMark;
		const { tradeListStore: { list: allList, setList } } = tradeStore;
		const reList: IPackage[] = [];
		const mergeTids = mergeTrade.map((t: IPackage) => t.togetherId);
		for (let i = 0; i < allList.length; i++) {
			if (allList[i].togetherId === mainTrade.togetherId) {
				// eslint-disable-next-line no-await-in-loop
				const pack = await handlePackageList([newTrade], false, tradeStore.storeSearchParams);
				reList.push(pack[0]);
			} else if (!mergeTids.includes(allList[i].togetherId)) {
				reList.push(allList[i]);
			}
		}

		setList(reList);
	}

	public mergeTrade = (mainTrade: IPackage, mergeTrade: IPackage[]) => {
		const togetherIds = [mainTrade.togetherId].concat(mergeTrade.map(p => p.togetherId)).join("|").replace(/\|/ig, ',');
		const {
			encodeTid,
			sellerId,
			platform,
			receiverState,
			receiverCity,
			receiverDistrict,
			receiverAddress,
			receiverTown,
			receiverName,
			receiverZip,
			receiverMobile,
			receiverPhone,
			receiverNameMask,
			idxEncodeReceiverAddress,
			receiverAddressMask,
			idxEncodeReceiverMobile,
			idxEncodeReceiverName,
			receiverPhoneMask,
			receiverMobileMask,
			caid,
			isDecrypted,
			source,
			mergeBuyerNick
		} = mainTrade;
		return new Promise((resolve, reject) => {
			let params = {
				mainTid: encodeTid || mainTrade.togetherId,
				tids: togetherIds,
				isSplit: mainTrade.isSplit,
				sellerId,
				platform,
				receiverProvince: receiverState,
				receiverCity,
				receiverCounty: receiverDistrict,
				receiverAddress,
				receiverTown,
				receiverName,
				receiverZip,
				receiverMobile,
				receiverPhone,
				isDecrypted,
				receiverPhoneMask,
				receiverMobileMask,
				idxEncodeReceiverMobile,
				receiverAddressMask,
				idxEncodeReceiverAddress,
				receiverNameMask,
				idxEncodeReceiverName,
				caid,
				source,
				mergeMd5: mergeBuyerNick, // 合单md5值
			};
			TradeManualMergeTradeApi(params).then(newTrade => {
				this.resetPrintList(mainTrade, [mainTrade, ...mergeTrade], newTrade);
				resolve(true);
			}).catch(reject);
		});
	}

	private async splitTradeSingle(packages: IPackage) {
		const splitTrades: IPackage[] = [];
		packages.trades.forEach(pack => {
			const newPack = {
				...packages,
				...pack,
				...{
					// 手动拆单以后，不需要展示智选快递信息
					packSmartExpressTemplate: "",
					smartExpressTemplate: "",
					"isMerge": null,
					isChecked: false,
					isExpand: false,
					sids: null,
					"tids": [
						pack.tid
					],
					"ptTids": [
						pack.ptTid
					],
					"encodeTid": pack.tid,
					"togetherId": pack.tid,
					"totalPayment": pack.payment,
					"totalPlatformDiscount": pack.platformDiscount,
					"totalPostFee": pack.postFee,
					"totalSellerDiscount": pack.sellerDiscount,
					"totalWeight": pack.totalWeight,
					"trades": [pack],
					"waybillPrintStatus": pack?.isPrintKdd == 1 ? "already" : "none",
					"shipListPrintStatus": pack?.isPrintFhd == 1 ? "already" : "none",
					"isPartShipped": false,
					"hasRefund": pack.hasRefund,
					"consolidateType": pack.consolidateType,
					"appointmentArrival": pack.appointmentArrival,
					"needSerialNumber": pack.orders.some(order => order.needSerialNumber),
					"isDecrypted": false
				}

			};
			splitTrades.push(newPack);
		});
		return splitTrades;
	}

	// 手动拆分订单
	public splitTrade = async(packages: IPackage) => {
		const splitTrades: IPackage[] = await this.splitTradeSingle(packages);
		const newSplitTrades = await handlePackageList(splitTrades, false, tradeStore.storeSearchParams);
		const { tradeListStore: { manualMergeTradeSplice } } = tradeStore;
		manualMergeTradeSplice(newSplitTrades, packages);
		// splitTrades.forEach(p => {
		// 	p.epidemicMark = p.trades[0].epidemicMark;
		// });

		// 手动「取消合并」的订单，重新查询后允许保留拆分结果
		try {
			let newSystemSetting = await userStore.getSystemSetting(false);
			const {
				manualSplitMergeOrder
			} = newSystemSetting.mergeStrategyConfig || {};

			if (manualSplitMergeOrder) {
				const params:TradeManualSplitMergeTradeRequest = {
					tidList: splitTrades?.map(trade => trade.tid) || [],
					sellerId: packages.sellerId,
					platform: packages.platform,
					mergeMd5: packages.mergeBuyerNick,
				};
				await TradeManualSplitMergeTradeApi(params);
			}
			
		} catch (error) {
			console.error(error);
		}
		
	}

	// 工具函数：将数组按指定长度分割
	static splitArray<T>(arr: T[], size: number): T[][] {
		const result: T[][] = [];
		for (let i = 0; i < arr.length; i += size) {
			result.push(arr.slice(i, i + size));
		}
		return result;
	}

	// 批量手动拆分订单
	public batchSplitTrade = async(packages: IPackage[]) => {
		const packageList = packages.filter(p => p.trades.length > 1);
		const { tradeListStore: { manualMergeTradeSpliceBatch } } = tradeStore;
		const splitTrades: IPackage[] = [];
		const splitTradeObj = [];
		const splitResults = await Promise.all(
			packageList.map(async(p) => {
				const newSplitTrades1 = await this.splitTradeSingle(p);
				const newSplitTrades2 = await handlePackageList(newSplitTrades1, false, tradeStore.storeSearchParams);
				return {
					oldTrade: p,
					newTrade: newSplitTrades2,
					splitTrades: newSplitTrades1,
				};
			})
		);

		splitResults.forEach(res => {
			splitTradeObj.push({
				oldTrade: res.oldTrade,
				newTrade: res.newTrade,
			});
			splitTrades.push(...res.splitTrades);
		});
		manualMergeTradeSpliceBatch(splitTradeObj);

		// 手动「取消合并」的订单，重新查询后允许保留拆分结果
		try {
			let newSystemSetting = await userStore.getSystemSetting(false);
			const {
				manualMergeOrder,
				manualSplitMergeOrder
			} = newSystemSetting.mergeStrategyConfig || {};

			if (manualSplitMergeOrder) {
				const splitTradeObj = {};
				splitTrades.forEach(trade => {
					if (!splitTradeObj[`${trade.sellerId}_${trade.platform}`]) {
						splitTradeObj[`${trade.sellerId}_${trade.platform}`] = [];
					}
					splitTradeObj[`${trade.sellerId}_${trade.platform}`].push(trade.tid);
				});
				Object.keys(splitTradeObj).forEach(key => {
					const tidList: string[] = (splitTradeObj[key] as string[]); // 明确类型为string[]
					const sellerId = key.split('_')[0];
					const platform = key.split('_')[1];
					// 分批处理tidList，每批最多500个
					const tidChunks = ManualMergeTradeHelper.splitArray(tidList, 500);
					tidChunks.forEach(chunk => {
						const params:TradeManualSplitMergeTradeRequest = {
							tidList: chunk,
							sellerId,
							platform,
						};
						TradeManualSplitMergeTradeApi(params).then(res => {
							console.log(res, 'res');
						}).catch(err => {
							console.error(err);
						});
					});
				});
			}
			
		} catch (error) {
			console.error(error);
		}
	}

	public showMergeModal = (mergeTrades: IPackage[]) => {
		console.log('mergeTradesmergeTrades', mergeTrades);
		Modal.confirm({
			title: '手动合并订单',
			cancelText: '取消',
			okText: '合并订单',
			width: 1200,
			centered: true,
			closable: true,
			icon: null,
			bodyStyle: {
				padding: 0
			},
			wrapClassName: styles.manualMergeModal,
			content: <div>
				<span style={ { marginBottom: '16px', display: 'flex', alignItems: 'center' } }>请选择需要合并的订单
					<Tooltip title="合并订单时，请勾选订单后选择地址进行合并，部分发货订单仅支持与待发货订单合并">
						<QuestionCircleOutlined style={ { color: 'rgba(0, 0, 0, 0.45)', marginLeft: '4px' } } />
					</Tooltip>
				</span>
				<ManualMergeTrade mergeTrades={ mergeTrades } ref={ this.tableRef } />
			</div>,			
			onOk: () => {
				sendPoint(Pointer.订单_订单打印_订单操作_合并订单_保存);
				const {
					mainTrade,
					mergeTrade
				} = this.tableRef?.current['getMainTradeTids']?.();

				if (!mainTrade) return Promise.reject();
				return this.mergeTrade(mainTrade, mergeTrade);
			}
		});
	}
}

export const manualMergeTradeHelper = new ManualMergeTradeHelper();
