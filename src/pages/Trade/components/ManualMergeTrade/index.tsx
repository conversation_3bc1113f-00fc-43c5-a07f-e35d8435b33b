import { Alert, Select, Table } from "antd";
import React, { forwardRef, useImperativeHandle, useMemo, useState } from "react";
import cs from 'classnames';
import { IPackage, ISubTrade } from "../../interface";
import { MessageFragment, ProductOrderFragment } from "../ListItem/components/simpleCom";
import { decryptRewrite, filterAddrDetail, handleDecrypt } from "../../utils";
import WarpText from "@/components-biz/WarpText";
import { PrintStatus, TradeStatus } from "@/utils/enum/trade";
import Icon from "@/components/Icon";
import message from "@/components/message";
import { IS_ADDR_TOWN, PLAT_FXG, PLAT_KS, PLAT_TB, PLAT_TM, PLAT_JD, PLAT_SPH, PLAT_YZ, PLAT_DW, PLAT_KTT } from "@/constants";
import TradeLabelCom from "../ListItem/components/TradeLabelCom";
import styles from './index.module.less';

interface ManualMergeTradeProps {
	mergeTrades: IPackage[]
}

const ProductFragment:React.FC<{trades: IPackage['trades'], pack: IPackage}> = ({
	trades,
	pack,
}) => {
	return (
		<div className="r-flex r-fw-w product-container">
			{trades.map((t:ISubTrade) => {
				return <ProductOrderFragment pack={ pack } key={ t.tid } productOrders={ t.orders } itemStyle={ { width: '100%' } } />;
			})}
		</div>
	);
};

const AddressFragment: React.FC<{trade: IPackage, decrypt: () => void}> = ({
	trade,
	decrypt
}) => {

	return (
		<>
			<div>{trade.receiverNameMask}，{trade.receiverMobileMask || trade.receiverPhoneMask || ''}</div>
			<div>
				{trade.receiverState}
				{trade.receiverCity}
				{trade.receiverDistrict}
				{IS_ADDR_TOWN(trade.platform) ? trade.receiverTown : ''}
				{filterAddrDetail(trade)}
				{trade.isDecrypted}
				{!trade.isDecrypted ? <span onClick={ decrypt }><Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5 } } /></span> : ''}
				<p>	{(trade.trades.find((tradesItem) => tradesItem.status === TradeStatus.卖家部分发货) ?? false) ? <span style={ { 'color': '#ff9d2e', 'padding': '3px 5px', 'border': '1px solid #ff9d2e', 'marginTop': '10px', 'display': 'inline-block' } }>订单部分发货</span> : '' }</p>
			</div>
		</>
	);
};



const ManualMergeTrade = ({
	mergeTrades
}: ManualMergeTradeProps, ref: React.Ref<unknown>) => {
	const [selectedRows, setSelectedRows] = useState([]);
	const [mainTrade, setMainTrade] = useState<string>();
	const [trades, setTrades] = useState<IPackage[]>(mergeTrades || []);
	const selectedRowKeys = useMemo(() => {
		return Array.from(selectedRows, s => s.togetherId);
	}, [selectedRows]);
	const [isCheckedSELLER_CONSIGNED_PART, setIsCheckedSELLER_CONSIGNED_PART] = useState({ isChecked: false, tid: null });
	const addressOptions = useMemo(() => {
		return selectedRows.map(s => {
			const trade = trades.filter(t => t.togetherId === s.togetherId)[0];
			return {
				value: trade.togetherId,
				label: `
				${trade.receiverNameMask}
				${trade.receiverMobileMask || trade.receiverPhoneMask || ''},
				${trade.receiverState}
				${trade.receiverCity}
				${trade.receiverDistrict}
				${IS_ADDR_TOWN(trade.platform) ? trade.receiverTown : ''}
				${filterAddrDetail(trade)}
				`
			};
		});
	}, [selectedRows, trades]);

	const platform = useMemo(() => {
		return trades[0].platform;
	}, [trades]);


	const decrypt = async(trade: IPackage) => {
		const decryptInfo = await handleDecrypt(trade);
		trades.forEach((d: IPackage, index: number) => {
			if (d.togetherId === trade.togetherId) {
				trades[index] = {
					...trade,
					...decryptRewrite(trade, decryptInfo)
				};
			}

		});
		setTrades([...trades]);
	};

	const columns = [{
		title: '序号',
		width: 44,
		render: (t: string, row: IPackage, index: number) => {
			return (<>{index + 1}</>);
		}
	}, {
		title: '订单号',
		width: 150,
		dataIndex: 'togetherId',
		render: (t: string) => {
			return <WarpText>{t}</WarpText>;
		}
	}, {
		title: '商品信息',
		width: 220,
		render: (t: string, row: IPackage, index: number) => {
			return <ProductFragment trades={ row.trades } pack={ row } />;
		}
	}, {
		title: '昵称',
		width: 80,
		dataIndex: 'buyerNick',
		render: (t: string) => {
			return <WarpText>{t}</WarpText>;
		}
	}, {
		title: '收货信息',
		width: 220,
		render: (t: string, row: IPackage, index: number) => {
			return <WarpText><AddressFragment trade={ row } decrypt={ () => { decrypt(row); } } /></WarpText>;
		}
	}, {
		title: '打印状态',
		width: 80,
		dataIndex: 'waybillPrintStatus',
		render: (waybillPrintStatus: string) => {
			return (
				<>
					{PrintStatus.已打印 === waybillPrintStatus ? '已' : '未'}打印
				</>
			);
		}
	}, {
		title: '备注',
		width: 188,
		render: (t: string, row: IPackage, index: number) => {
			return (
				<>
					{row.trades.map(t => {
						return <WarpText key={ t.tid }><MessageFragment trade={ t } /></WarpText>;
					})}
				</>
			);
		}
	},
	{
		title: '订单标签',
		dataIndex: 'label',
		key: 'label',
		render: (_, record) => {
			return (
				<TradeLabelCom pack={ record as any } colWidth={ 120 } />
			);
		}
	}
	];

	useImperativeHandle(ref, () => {
		return {
			getMainTradeTids: () => {
				if (selectedRows.length < 2) {
					message.error('请选择需要合并的订单');
					return false;
				}
				if (!mainTrade) {
					message.error('请选择指定合单的收货信息');
					return false;
				}
				return {
					mainTrade: trades.filter(r => r.togetherId === mainTrade)[0],
					mergeTrade: selectedRows.filter(r => r.togetherId !== mainTrade)
				};
			}
		};
	}, [mainTrade, selectedRows, trades]);

	const rowSelection = {
		selectedRowKeys,
		onChange: (selectedRowKeys: any, selectedRows: any) => {
			console.log(trades, selectedRowKeys, selectedRows, isCheckedSELLER_CONSIGNED_PART, '-->selectedRowKeys');
			setIsCheckedSELLER_CONSIGNED_PART({ isChecked: false, tid: '' });

			if (mainTrade && !selectedRowKeys.includes(mainTrade)) {
				setMainTrade(null);
			}
			setSelectedRows(selectedRows);
			// 「部分发货」的订单选中了任意一个之后，其他「部分发货』的订单不能选中，需要去判断trandes里的status
			selectedRows.forEach((selectedRowsItem:any) => {
				selectedRowsItem.trades.forEach((tradesItem: {
					tid: any; status: TradeStatus;
				}) => {
					if (tradesItem.status === TradeStatus.卖家部分发货) {
						setIsCheckedSELLER_CONSIGNED_PART({ isChecked: true, tid: tradesItem.tid });
					}
				});
			});
		},
		getCheckboxProps: (record:{trades:Array<any>}) => ({
			disabled: isCheckedSELLER_CONSIGNED_PART.tid !== record.trades[0].tid && isCheckedSELLER_CONSIGNED_PART.isChecked && (record.trades.find((tradesItem: { status: TradeStatus; }) => tradesItem.status === TradeStatus.卖家部分发货) ?? false)
		})
	};

	const description = useMemo(() => {
		let info = '';
		if ([PLAT_TB, PLAT_TM, PLAT_FXG, PLAT_KS, PLAT_JD, PLAT_SPH].includes(platform)) {
			info = '请注意！建议解密后再合并，合并会将此买家的所有订单按照当前地址进行更改（淘宝、天猫、抖音、快手、京东、视频号会更新商家后台），请确认是否合并';
		} else if ([PLAT_YZ].includes(platform)) {
			info = '';
		} else if ([PLAT_DW].includes(platform)) {
			info = '得物平台合单有数量上限要求（企业卖家：美妆、配件、手表、运动户外、家用电器、家居、玩具乐器、3c数码是20单，其余都是8单；个人卖家：美妆、配件是20单，其余都是8单；）';
		} else {
			info = '请注意！仅临时合并，合并后将按此地址进行打印，再次点击“查询”按钮后将恢复';
		}
		return info;
	}, [platform]);

	return (
		<>
			<Table
				size="small"
				rowKey="togetherId"
				pagination={ false }
				columns={ columns }
				dataSource={ trades }
				rowSelection={ rowSelection }
				className={ styles.table }
				scroll={ { y: document.documentElement.clientHeight - 500 } }
			/>
			<div>
				<div className={ cs('r-mt-16') }>合并后信息确认</div>
				<div className={ cs('r-mt-8') }>
					{
						addressOptions.length > 1 && (
							<>
								<div className={ cs('r-flex', 'r-ai-c', 'r-mb-16') }>
									<span className={ cs('r-mr-4') }><span style={ { color: 'red', marginRight: 3 } }>*</span>收货信息：</span>
									<Select
										placeholder="请选择收货信息"
										className={ cs('r-flex-1') }
										value={ mainTrade }
										options={ addressOptions }
										onChange={ (value) => { setMainTrade(value); } }
									/>
								</div>
							</>
						)
					}

					{
						description && (
							<Alert
								style={ { padding: "8px 16px", background: '#FFFBE6', border: '1px solid #FFE58F' } }
								description={ (
									<div style={ { fontSize: '12px', color: 'rgba(0, 0, 0, 0.85)' } }>
										{description}
									</div>
								) }
							/>
						)
					}
					
					
				</div>
			</div>
		</>
	);
};

export default forwardRef(ManualMergeTrade);
