import React, { useState, useEffect } from 'react';
import { Input, Button, Popconfirm, message, Tooltip } from 'antd';
import { PlusOutlined, CloseCircleFilled } from '@ant-design/icons';
import styles from './index.module.scss';
import { GetUserQuickNotesListApi, AddUserQuickNotesApi, DeleteUserQuickNotesApi } from '@/apis/trade';

interface HandOrderQuickContentProps {
  onFill: (content: string) => void;
}

const HandOrderQuickContent: React.FC<HandOrderQuickContentProps> = ({ onFill }) => {
	const [quickList, setQuickList] = useState([]);
	const [quickNote, setQuickNote] = useState('');
	const [showInput, setShowInput] = useState(false);

	useEffect(() => {
		getQuickNotes();
	}, []);

	const getQuickNotes = () => {
		GetUserQuickNotesListApi({ bizType: 2 }).then(res => {
			setQuickList(res || []);
		});
	};

	const handleAdd = async() => {
		const val = quickNote.trim();
		if (!val) {
			message.warning('请输入内容');
			return;
		}
		if (quickList.some(item => item.content === val)) {
			message.warning('内容已存在');
			setQuickNote("");
			return;
		}
		await AddUserQuickNotesApi({
			bizType: 2,
			content: val,
		});
		getQuickNotes();
		setQuickNote('');
		setShowInput(false);
	};

	const handleDelete = async(item) => {
		await DeleteUserQuickNotesApi({
			notesId: item.id
		});
		getQuickNotes();
	};

	return (
		<div className={ styles.quickContentWrap }>
			<div className={ styles.quickBtnRow }>
				{quickList.length === 0 && <span className={ styles.emptyText }>暂无快捷内容</span>}
				{quickList.map((item) => (
					<div className={ styles.quickBtnWrap } key={ item.id }>
						<Button
							className={ styles.quickBtn }
							onClick={ () => onFill(item.content) }
						>
							{item.content}
						</Button>
						<Popconfirm title="确定删除该内容？" onConfirm={ () => handleDelete(item) } okText="删除" cancelText="取消">
							<CloseCircleFilled className={ styles.closeIcon } />
						</Popconfirm>
					</div>
				))}
				{
					showInput && <Input style={ { width: 200, marginRight: 8 } } placeholder="最多200字" maxLength={ 200 } value={ quickNote } onChange={ e => setQuickNote(e.target.value) } onPressEnter={ handleAdd } />
				}
				{
					quickList?.length < 100 && (
						<div className={ styles.quickBtnWrap }>
							{
								showInput && (
									<Button
										className={ styles.quickBtn }
										onClick={ () => {
											setShowInput(false);
											setQuickNote("");
										} }
										style={ { marginRight: 8 } }
									>
										取消
									</Button>
								)
							}
							<Button
								className={ styles.quickBtn }
								onClick={ () => (showInput ? handleAdd() : setShowInput(!showInput)) }
							>
								{
									showInput ? "保存" : "+新增快捷内容"
								}
							</Button>
							
						</div>
					)
				}
			</div>
		</div>
	);
};

export default HandOrderQuickContent; 