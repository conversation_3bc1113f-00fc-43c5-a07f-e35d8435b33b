.quickContentWrap {
  margin-top: 8px;
  background: none;
  padding: 0;
  width: 804px;
}
.inputRow {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.quickBtnRow {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 0 8px;
  min-height: 24px;
}
.quickBtnWrap {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.quickBtn {
  background: #fff;
  border: 1px solid #d9d9d9;
  color: #333;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 4px;
  box-shadow: none;
  transition: border 0.2s;
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  height: auto;
  text-align: left;
}
.closeIcon {
  position: absolute;
  top: -7px;
  right: -7px;
  border-radius: 50%;
  color: #999;
  background-color: #fff;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(0,0,0,0.08);
  transition: color 0.2s, opacity 0.2s;
  opacity: 0;
  pointer-events: none;
  z-index: 2;
}
.quickBtnWrap:hover .closeIcon {
  opacity: 1;
  pointer-events: auto;
}
.emptyText {
  color: #bbb;
  font-size: 12px;
  margin-left: 8px;
  line-height: 32px;
}
.quickItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.quickText {
  font-size: 13px;
  color: #333;
  word-break: break-all;
  flex: 1;
} 