// import event from '@libs/event';
// import memoFn, { updateMemoFn } from '@libs/memorizeFn';
// import { commonApis } from '@libs/server';
import { Button, Checkbox, Radio, RadioChangeEvent, Select, Space, Drawer, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import _, { forIn } from 'lodash';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { IProductSetting, ItemFilterSettingSaveRequest } from '@/types/trade/index';
import './index.scss';
import tradeSet from '@/stores/trade/tradeSet';
import { FilterOrderShowSku, FilterOrderSort, FilterOrderSkuMerge } from '@/utils/enum/trade';
import { ItemFilterSettingSaveApi } from '@/apis/trade';
import Pointer from '@/utils/pointTrack/constants';
import sendPoint from '@/utils/pointTrack/sendPoint';
import userStore from '@/stores/user';

const { Option } = Select;
export interface IFilterOrderSettingModalProps {
    onClose?: () => void;
	showPosition?: {
		x: string;
		y: string;
	}
}

// jsonConfig 后端新设的字段，后面加的都放这里，数据库可以不用加字段
const needChangeKeys = [
	'onlyShowSelected'
];

const FilterOrderSettingModal = observer((props: IFilterOrderSettingModalProps) => {
	const { onClose, showPosition } = props;
	const [params, setParams] = useState<ItemFilterSettingSaveRequest>({});
	const { filterOrderSetting, setFilterOrderSetting } = tradeSet;
	const { isShowZeroStockVersion } = userStore;
	useEffect(() => {
		if (isShowZeroStockVersion && filterOrderSetting?.showColumn == 2) {
			filterOrderSetting.showColumn = 1;
		}
		setParams(_.cloneDeep(filterOrderSetting));
	}, [filterOrderSetting]);

	// 把值放进jsonConfig里面
	const saveJsonConfig = (key, val) => {
		setParams((prev) => {
			if (needChangeKeys.includes(key)) {
				let oldJsonConfig = prev.jsonConfig || {};
				let jsonConfig = {
					...oldJsonConfig,
					[key]: val
				};
				return {
					...prev,
					[key]: val,
					jsonConfig,
				};
			} else {
				return {
					...prev,
					[key]: val,
				};
			}
		});
	};

	const renderCheckBox = (key: keyof IProductSetting, label: string) => (
		<label className="setting-input-item r-pointer" style={ { marginBottom: 0 } }>
			<Checkbox
				style={ { width: 16, marginRight: 8 } }
				onChange={ (e) => {
					if (key == 'isFixed' && e.target.checked) {
						sendPoint(Pointer.订单_订单打印_宝贝筛选_设置_窗口悬浮展示);
					}

					saveJsonConfig(key, +e.target.checked);
				} }
				checked={ !!params[key] }
			/>
			{label}
		</label>
	);

	const renderRadio = (value: string | number, label: string) => (
		<Radio value={ value }>
			{label}
		</Radio>
	);

	const handleOk = () => {
		// 使用类型断言来避免类型错误
		let newParams: Record<string, any> = {};
		for (const key in params) {
			if (Object.prototype.hasOwnProperty.call(params, key)) {
				const item = params[key];
				// 保存的时候已经存了最新的jsonConfig
				// 排除 previewPicSize 字段，因为它已经在 jsonConfig 中了
				if (!needChangeKeys.includes(key) && key !== 'previewPicSize') {
					newParams[key] = item;
				}
			}
		}

		// 确保 jsonConfig 存在并包含所有必要的属性
		if (params.jsonConfig) {
			newParams.jsonConfig = { ...params.jsonConfig };
		}

		console.log('保存参数:', newParams);

		ItemFilterSettingSaveApi(newParams).then(() => {
			setFilterOrderSetting(newParams);
			onClose();
		});
	};

	const handleRadioChange = (e:RadioChangeEvent, name: string) => {
		setParams(prev => ({
			...prev,
			jsonConfig: {
				...prev.jsonConfig,
				[name]: e.target.value
			},
		}));
		switch (name) {
			case 'showSku':
				sendPoint(Pointer.订单_订单打印_宝贝筛选_设置_规格显示内容);
				break;
			case 'sort':
				sendPoint(Pointer.订单_订单打印_宝贝筛选_设置_排序);
				break;
			default:
				break;
		}
	};

	const handleCheckboxChange = (e, name) => {
		setParams(prev => ({
			...prev,
			[name]: e.join(",")
		}));
	};

	return (
		<>
			<Drawer
				className="filter-order-drawer"
				visible
				maskClosable
				title="宝贝筛选设置"
				width={ 480 }
				onClose={ onClose }
			>
				{/* <div className="ant-modal-mask" onClick={ onClose } /> */}
				<div className="filter-order-setting-container">
					<div className="filter-order-body">
						<div className="filter-order-title">
							基础设置
						</div>
						<div className="filter-order-item">
							<span className="filter-order-label">窗口悬浮展示：</span>
							<div className="filter-order-input-con" >
								{renderCheckBox('isFixed', '开启')}
							</div>
						</div>
						<div className="filter-order-item">
							<span className="filter-order-label">销售属性筛选：</span>
							<div className="filter-order-input-con" >
								{renderCheckBox('salesAttributesFilterSwitch', '开启')}
							</div>
						</div>
						{!isShowZeroStockVersion ? (
							<div className="filter-order-item">
								<span className="filter-order-label">库存数量展示：</span>
								<div className="filter-order-input-con" >
									{renderCheckBox('showStockNumSwitch', '开启')}
								</div>
							</div>
						) : null}
						<div className="filter-order-item">
							<span>仅显示选中宝贝的订单：</span>
							<span>{renderCheckBox('onlyShowSelected', '开启')}</span>
						</div>
						<div className="filter-order-title">
							显示设置
						</div>
						<div className="filter-order-item">
							<span className="filter-order-label" style={ { alignSelf: 'center' } }>商品显示类型：</span>
							<div style={ { width: 140 } }>
								{
									isShowZeroStockVersion ? (
										<Select
											value={ params['showColumn'] }
											style={ { width: 140 } }
											size="small"
											onChange={ (e) => {
												setParams((prev) => ({
													...prev,
													showColumn: +e,
												}));
											} }
										>
											<Option value={ 1 }>平台商品</Option>
										</Select>
									) : (
										<Select
											value={ params['showColumn'] }
											style={ { width: 140 } }
											size="small"
											onChange={ (e) => {
												setParams((prev) => ({
													...prev,
													showColumn: +e,
												}));
											} }
										>
											<Option value={ 1 }>平台商品</Option>
											<Option value={ 2 }>本地货品</Option>
										</Select>
									)
								}
							</div>
						</div>
						<div className="filter-order-item">
							<span className="filter-order-label">预览图显示：</span>
							<div className="filter-order-input-con">
								{/* 添加调试信息 */}
								{console.log('Radio.Group value:', params?.jsonConfig?.previewPicSize, typeof params?.jsonConfig?.previewPicSize)}
								<Radio.Group
									value={ params?.jsonConfig?.previewPicSize === 0 ? "0" : (params?.jsonConfig?.previewPicSize === 2 ? "2" : "1") }
									onChange={ (e) => {
									// 打印调试信息
										console.log('选择预览图大小:', e.target.value, typeof e.target.value);

										setParams((prev) => {
											if (!prev.jsonConfig) {
												prev.jsonConfig = {};
											}
											// 将字符串值转换为数字
											const numValue = Number(e.target.value);
											console.log('转换后的数字值:', numValue);
											prev.jsonConfig.previewPicSize = numValue;
											return { ...prev };
										});
									} }
								>
									<Space direction="horizontal" size={ 8 }>
										<Radio value="0"><span style={ { whiteSpace: 'nowrap', display: 'inline-block', minWidth: '32px' } }>小图</span></Radio>
										<Radio value="1"><span style={ { whiteSpace: 'nowrap', display: 'inline-block', minWidth: '32px' } }>中图</span></Radio>
										<Radio value="2"><span style={ { whiteSpace: 'nowrap', display: 'inline-block', minWidth: '32px' } }>大图</span></Radio>
									</Space>
								</Radio.Group>
							</div>
						</div>
						<div className="filter-order-item">
							<span className="filter-order-label">规格展示内容：</span>
							<div className="filter-order-checkbox-con" >
								<Checkbox.Group onChange={ (e) => { handleCheckboxChange(e, 'showSku'); } } value={ params['showSku']?.split(',') }>
									{
										params?.showColumn == 1 ? (
											<>
												<Checkbox className="r-mb-6" value={ FilterOrderShowSku.规格名称 }>规格名称</Checkbox>
												<Checkbox value={ FilterOrderShowSku.规格别名 }>规格别名</Checkbox>
												<Checkbox style={ { marginLeft: 0 } } value={ FilterOrderShowSku.商家编码 }>商家编码</Checkbox>
												<Checkbox value={ FilterOrderShowSku.规格编码 }>规格编码</Checkbox>
											</>
										) : (
											<Space direction="vertical">
												<Checkbox value={ FilterOrderShowSku.货品规格名称 }>货品规格名称</Checkbox>
												<Checkbox value={ FilterOrderShowSku.规格别名 }>规格别名</Checkbox>
												<Checkbox value={ FilterOrderShowSku.规格编码 }>货品规格编码</Checkbox>
											</Space>
										)
									}
								</Checkbox.Group>
							</div>
						</div>
						<div className="filter-order-item">
							<span className="filter-order-label">排序：</span>
							<div className="filter-order-input-con" >
								<Radio.Group name="newSort" onChange={ (e) => { handleRadioChange(e, 'newSort'); } } value={ params?.jsonConfig?.['newSort'] }>
									<Space direction="vertical">
										{renderRadio(FilterOrderSort.按数量排序, '按数量排序')}
										{renderRadio(FilterOrderSort.相同简称排一起, '相同简称排一起')}
										{renderRadio(FilterOrderSort.相同规格名称排一起, '相同规格名称排一起')}
										{renderRadio(FilterOrderSort['相同简称+规格名称排一起'], '相同简称+规格名称排一起')}
										{renderRadio(FilterOrderSort['相同简称+规格别名排一起'], '相同简称+规格别名排一起')}
									</Space>
								</Radio.Group>
							</div>
						</div>
						{
							params.showColumn === 1 ? (
								<div className="filter-order-item">
									<span className="filter-order-label">
										规格合并：
									</span>
									<div className="filter-order-input-con">
										<Radio.Group
											name="skuMerge"
											value={ params?.jsonConfig?.skuMerge }
											onChange={ e => {
												handleRadioChange(e, 'skuMerge');
											} }
										>
											<Space direction="vertical">
												<Radio value={ FilterOrderSkuMerge.默认规则 }>
													默认规则
												</Radio>
												<Radio value={ FilterOrderSkuMerge['相同简称+规格名称合并'] }>
													相同简称+规格名称合并
													<Tooltip title="商品未设置简称时，取商品名称进行合并">
														<QuestionCircleOutlined className="r-ml-4 r-c-666" />
													</Tooltip>
												</Radio>
												<Radio value={ FilterOrderSkuMerge['相同简称+规格别名合并'] }>
													相同简称+规格别名合并
												</Radio>
											</Space>
										</Radio.Group>
									</div>
								</div>
							) : null
						}
					</div>
					<div className="filter-order-footer">
						<Button className="r-ml-8" onClick={ onClose }>取消</Button>
						<Button className="r-ml-8" type="primary" onClick={ handleOk }>确定</Button>
					</div>
				</div>
			</Drawer>
		</>
	);
});

export default FilterOrderSettingModal;
