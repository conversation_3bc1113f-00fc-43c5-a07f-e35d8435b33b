.filter-order-setting-container {
	border-radius: 4px;
	z-index: 1001;
	color: #333;

	// .filter-order-title {
	// 	padding: 5px 10px 0;
	// 	// border-bottom: 1px solid #eee;
	// 	text-align: center;
	// 	.filter-order-main-title {
	// 		color: rgba(0, 0, 0, 0.85);
	// 		font-weight: bold;
	// 		font-size: 14px;
	// 	}
	// 	.filter-order-sub-title {
	// 		font-size: 12px;
	// 	}
	// }

	.filter-order-body {
		.filter-order-item {
			display: flex;
			align-items: center;
			font-size: 14px;
			padding: 0 8px;
			margin-bottom: 8px;
			// height: 32px;

			&:hover {
				background: #f5f5f5;
				color: #fd8204;
			}

			.filter-order-label {
				width: 100px;
				align-self: flex-start;
                text-align: right;
			}

			.filter-order-checkbox {
				width: 16px;
			}
		}

		.filter-order-input-con {
			display: flex;
            flex-direction: column;
			// flex-wrap: wrap;
			// gap: 8px 16px;
			// width: 180px;
			text-align: left;
		}
		.filter-order-checkbox-con {
			display: flex;
			flex-direction: row;
			width: 220px;
			text-align: left;
		}
	}

	.filter-order-footer {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 10px;
		background: #fff;
		border-top: 1px solid #eee;
	}

	.setting-input-item {
		display: flex;
		align-items: center;
		margin-bottom: 8px;

		&:nth-of-type(2n - 1) {
			margin-right: 16px;
		}
	}
	.filter-order-title {
		color: #888;
		margin: 12px 8px;
		text-align: left;
	}
}

.filter-order-drawer {
	.ant-drawer-header-title {
		flex-direction: row-reverse;
	}
	.ant-drawer-close {
		margin-right: 0;
	}
	.ant-drawer-body {
		padding-bottom: 60px;
	}
}