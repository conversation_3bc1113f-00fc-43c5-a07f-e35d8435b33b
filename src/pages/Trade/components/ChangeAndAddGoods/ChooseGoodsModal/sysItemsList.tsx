import { Space, Input, Form, Typography, Modal, InputNumber, Tooltip, Radio, Select } from 'antd';
import React, { forwardRef, useCallback, useEffect, useMemo, useRef, useState, useImperativeHandle } from 'react';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/lib/table';
import dayjs, { Dayjs } from 'dayjs';
import cs from 'classnames';
import { useUpdateEffect, useUnmount } from 'ahooks';
import _, { isFinite, debounce, toNumber } from 'lodash';
import message from '@/components/message';
import SearchTable from '@/components/SearchTable';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { 
	SysItemGetSysItemListRequest, SysItemGetSysItemListResponse,
	ItemCommonItemSaveOrDelRequest, ItemCommonItemSaveOrDelResponse
} from '@/types/schemas/warehouse/system';
import { SysItemGetSysItemListApi, TradeGetSysItemListApi, StockGetSysItemListApi, ItemCommonItemSaveOrDelApi } from '@/apis/warehouse/system';
import WaresInfo from '@/components-biz/WaresInfo';
import WarpText from '@/components-biz/WarpText';
import { DEFAULT_IMG, CommonItemType } from '@/constants';
import warehouseStore from '@/stores/warehouse';
import { SupplierAllResponse } from '@/types/schemas/warehouse/Supplier';
import Icon from "@/components/Icon";
import EnumSelect from '@/components/Select/EnumSelect';
import events from "@/utils/events";
import SupplierSelect from '@/components-biz/SupplierSelect';
import { getModalTableScrollHeight } from '@/utils/util';
import s from './index.module.scss';
import { useStores } from '@/stores/tool';

const { Text } = Typography;
const { confirm } = Modal;

export const SingleOrCombine = ({ value = 0, onChange = (e) => {}, size = "middle", style = {}, disabledCombined = { isDisable: false, tips: "" } }) => {
	return (
		<Radio.Group defaultValue={ 0 } size={ size } style={ { ...style } } value={ value } onChange={ e => { onChange(e); } }>
			<Radio.Button value={ 0 }>单品</Radio.Button>
			<Tooltip title={ disabledCombined.tips }>
				<Radio.Button disabled={ disabledCombined.isDisable } value={ 1 }>组合</Radio.Button>
			</Tooltip>

		</Radio.Group>
	);
};

interface ProductListProps {
	type?: 'checkbox' | 'radio',
	code?:string,
	onChange?: Function,
	disabled?: {
		key: string,
		value: any[]
	}
	supplier?: {
		label: string,
		value: string
	}
	maxAddCount?: number // 可添加的最大数量
	addedCount?: number // 已添加的数量
	disabledCombined?: { isDisable: boolean, tips: string },
	from?: string,
	showColumnKeys?:string[]; // 控制某个地方单独显示的列配置
    hasBatchedNum?: boolean
	allSelectRows?: any[]
	scrollY?: string
	autoSearch?: boolean
}

const tableFormKey = (name:any, index:number) => {
	return `${name}.${index}`;
};

export enum FetchSysItemListEnum {
	订单 = "trade",
	库存 = "stock",
	其他 = "other",
}

const fetchListApiObj = {
	[FetchSysItemListEnum.订单]: TradeGetSysItemListApi,
	[FetchSysItemListEnum.库存]: StockGetSysItemListApi,
	[FetchSysItemListEnum.其他]: SysItemGetSysItemListApi
};

const ProductList = forwardRef((props:ProductListProps, ref?: React.Ref<HTMLDivElement>) => {
	const {
		type = 'checkbox',
		code = '',
		supplier,
		disabled,
		onChange,
		maxAddCount,
		addedCount,
		disabledCombined = { isDisable: false, tips: "" },
		from = FetchSysItemListEnum.其他,
		showColumnKeys = [],
		hasBatchedNum = false,
		allSelectRows,
		autoSearch = true,
	} = props;
	const [form] = useForm();
	const [editCountForm] = useForm();
	const groupStore = useStores('GroupStore');
	const { groupList } = groupStore;
	const tableRef = useRef<SearchTableRefProps>();
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [dataSource, setDataSource] = useState({});
	const [skuList, setSkuList] = useState<SysItemGetSysItemListResponse["data"]["list"]>([]);
	const sysSkuListRowIdRef = useRef([]);
	const isShowSetCommon = useMemo(() => showColumnKeys.includes('setCommon'), [showColumnKeys]); // 是否显示常用商品设置

	useUpdateEffect(() => {
		setSelectedRowKeys([]);
	}, [disabled]);

	useEffect(() => {
		if (supplier?.supplierName) {
			form.setFieldsValue({ supplierName: supplier?.supplierName });
		}
	}, [supplier]);

	useEffect(() => {
		groupStore.getGroupList();
	}, []);

	// 表格列定义，与AntdTable使用一致
	const columns: ColumnsType<unknown> = [
		{
			title: '货品简称',
			dataIndex: 'sysItemAlias',
			key: 'sysItemAlias',
			render: (t: string, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				return {
					children: row.colSpan && (
						<div className="r-flex r-fd-c">
							<WarpText>{t}</WarpText>
							<div className="r-fc-black-65">
								<Tooltip placement="right" title="货品编码">
									<span><WarpText>{row.outerId}</WarpText></span>
								</Tooltip>
							</div>
						</div>
					),
					props: {
					// rowSpan: row.colSpan || 0,
					}
				};
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.colSpan ? { borderRight: '1px solid #f0f0f0' } : { borderTop: 0, borderRight: '1px solid #f0f0f0' }
				};
			}
		},
		{
			title: '规格',
			width: 190,
			render: (text, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				return (
					<div>
						<WaresInfo 
							isCombination={ row.sysSkuList[0]?.isCombination } 
							imgUrl={ row.sysSkuList[0].picUrl } 
							wareName={ row.sysSkuList[0].sysSkuName } 
							align="fs"
						/>
					</div>
				);
			}
		},
		{
			title: '规格别名',
			dataIndex: 'sysSkuAlias',
			key: 'sysSkuAlias',
			render: (text, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				return <WarpText>{row.sysSkuList[0].sysSkuAlias}</WarpText>;
			}
		},
		{
			title: '货品规格编码',
			dataIndex: 'skuOuterId',
			key: 'skuOuterId',
			render: (text, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				return <WarpText>{row.sysSkuList[0].skuOuterId}</WarpText>;
			}
		},
		{
			title: '货号',
			width: 100,
			render: (text, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				return <WarpText>{row.sysSkuList[0].itemNo}</WarpText>;
			}
		},
		{
			title: '条形码',
			width: 100,
			render: (text, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				return <WarpText>{row.sysSkuList[0].barCode}</WarpText>;
			}
		},
		{
			title: '可配货库存',
			width: 100,
			render: (text, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				return <span>{row.sysSkuList[0]?.salableItemDistributableStock}</span>;
			}
		}
	];

	if (hasBatchedNum) {
		columns.splice(6, 0, {
			title: (
				<p className="r-flex">
					数量
					{/* <span className="r-ml-8 r-pointer" style={ { color: '#FD8204' } } onClick={ debounce(() => batchEditCount(), 500, { 'leading': true, 'trailing': false }) }>批量填充</span> */}
				</p>
			),
			width: 100,
			align: 'center',
			dataIndex: 'itemCount',
			key: 'itemCount',
			render: (text, record:any, index) => {
				return (
					<Space onClick={ e => { e.stopPropagation(); } }>
						<Form.Item
							className={ cs('r-mb-0') }
							name={ ["itemCount", record.rowId] }
							rules={ [{
								// required: true,
								validator: (_:any, value:any, callback:()=>void) => {
									if (value && !/^[1-9]{1}[\d]*$/.test(value)) {
										return Promise.reject(new Error('请正确输入数量'));
									}
									// 注意取值
									if (Number(value) < Number(record.instockCount)) {
										return Promise.reject(new Error('数量有误'));
									}
									// if (!value) {
									// 	return Promise.reject(new Error('采购数量不能为空'));
									// }
									return Promise.resolve();
								}
							}] }
						>
							<Input size="middle" />
						</Form.Item>
						{/* <Text>件</Text> */}
					</Space>

				);
			},
		});
	}

	const needFilterColumns = [
		{
			title: '市场',
			width: 80,
			key: 'market',
			render: (text, row) => {
				return <span>{row.sysSkuList[0]?.market}</span>;
			}
		},
		{
			title: '档口',
			width: 80,
			key: 'stall',
			render: (text, row) => {
				return <span>{row.sysSkuList[0]?.stall}</span>;
			}
		},
		{
			title: '供应商',
			width: 80,
			key: 'supplierName',
			render: (text, row) => {
				return <span>{row.sysSkuList[0]?.supplierName}</span>;
			}
		},
		{
			title: '常用',
			width: 80,
			key: 'setCommon', // showColumnKeys 配置
			render: (text, row) => {
				return (
					<div>
						{row?.sysSkuList[0]?.commonItem ? (
							<Tooltip title="取消常用">
								<span>
									<Icon svg size={ 18 } type="shoucang-mian" style={ { color: '#FEB432' } } onClick={ (e) => { e.stopPropagation(); handleSetCommon(row, false); } } />
								</span>
							</Tooltip>
						) : (
							<Tooltip title="设为常用">
								<span>
									<Icon svg size={ 18 } type="shoucang-xian" style={ { color: '#999' } } onClick={ (e) => { e.stopPropagation(); handleSetCommon(row, true); } } />
								</span>
							</Tooltip>
						)}
					</div>
				);
			}
		}
	];
	// showColumnKeys 在这里添加
	needFilterColumns.forEach(column => {
		if (showColumnKeys.includes(column.key)) {
			columns.push(column);
		}
	});

	const getFormBatchedNum = useCallback(() => {
		const formBatchedNum = form.getFieldValue('itemCount');
		return formBatchedNum;
	}, [form]);

	// 设置、取消设置常用商品
	const handleSetCommon = debounce((row:any, type:boolean) => {
		const search: ItemCommonItemSaveOrDelRequest = {
			type: type ? 1 : 2, // 1:新增,2:删除
			// skuId: '', 
			// numIid: '', // 商品id
			sysSkuId: row?.sysSkuList?.[0]?.sysSkuId, 
			sysItemId: row?.sysItemId,
		};

		ItemCommonItemSaveOrDelApi(search)
			.then((res) => {
				if (res) {
					updateList(search);
				}
			})
			.catch((res => {}));
	}, 300);

	const updateList = (search:ItemCommonItemSaveOrDelRequest) => {
		setSkuList(pre => {
			const newList = [...pre];
			newList.forEach(item => {
				if (search.sysItemId == item.sysItemId && item?.sysSkuList?.[0]?.sysSkuId == search.sysSkuId) {
					item['sysSkuList'][0].commonItem = search.type == 1;
				}
			});
			return newList;
		});
	};

	// 将子组件中需要调用的方法绑定到 ref
	useImperativeHandle(ref, (): any => ({
		_getDataSource,
		setSelectedRowKeys,
		form
	}));

	// 表单元素定义 下拉框？日期？ 多选框？ 多选按钮？
	const FormFieldList: FormItemConfig[] = [
		{
			name: 'isCombination',
			label: "",
			className: 'r-mb-8',
			children: <SingleOrCombine disabledCombined={ disabledCombined } onChange={ () => { tableRef?.current?.refresh(); } } size="small" />,
		},
		{
			name: 'supplierName',
			label: "",
			className: 'r-mb-8',
			children: (
				<SupplierSelect
					allowClear
					supplierName={ form.getFieldValue("supplierName") }
					callBackProp="supplierName"
					size="small"
					style={ { width: 130 } }
				/>
			),
		},
		{
			name: 'sysItemAlias',
			label: "",
			className: 'r-mb-8',
			children: <Input placeholder="货品简称" size="small" style={ { width: 120 } } />,
		},
		{
			name: 'outerId',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="货品规格编码" size="small" style={ { width: 120 } } />,
		},
		{
			name: 'sysSkuName',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="规格/规格别名" size="small" style={ { width: 120 } } />,
		},
		{
			name: 'barCode',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="条形码" size="small" style={ { width: 120 } } />,
		},
		{
			name: 'itemNo',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="货号" size="small" style={ { width: 120 } } />,
		}
	];

	if (isShowSetCommon) {
		let newObject = {
			name: 'findCommonItem',
			label: "",
			className: 'r-mb-8',
			children: <EnumSelect placeholder="是否常用商品" enum={ CommonItemType } size="small" style={ { width: 130 } } />,
		};
		FormFieldList.splice(1, 0, newObject); // 商品名称前面插入一个
	}

	let classifyIdListObj = {
		name: 'classifyIdList',
		label: '',
		children: (
			<Select
				mode="multiple"
				showSearch
				showArrow
				allowClear
				placeholder="货品分类"
				optionFilterProp="children"
				style={ { minWidth: 160, maxWidth: 300 } }
				size="small"
				maxTagCount={
					1
				}
				filterOption={ (input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase()) }
				options={
					groupList.map(item => ({ value: item.classifyId, label: item.classifyName }))
				}
			/>
		)
	};
	FormFieldList.splice(1, 0, classifyIdListObj); 
	// 接口查询、查询参数重装
	const fetchSystemList = (info: any) => {
		let [startCreated = '', endCreated = ''] = info.date || [];
		for (let key in info) {
			if (typeof info[key] === 'string') {
				info[key] = info[key].trim();
			}
		}
		const { findCommonItem, sysItemAlias, outerId, pageNo, pageSize, barCode, sysSkuName, itemNo, isCombination = 0, supplierName, classifyIdList } = info;
		const search: SysItemGetSysItemListRequest = {
			findCommonItem: findCommonItem ? findCommonItem == 1 : undefined, // 是否常用商品
			setCommonItem: true, // 是否设置常用商品标记 写死参数
			outerId,
			barCode,
			sysSkuName,
			itemNo,
			sysItemAlias,
			startCreated,
			isCombination,
			endCreated,
			supplierName,
			classifyIdList,
			pageNo,
			pageSize,
			permission_source: code
		};
		// 查询参数的时候重置数量form
		form.setFieldsValue({
			...form.getFieldsValue(),
			itemCount: undefined,
		});
		console.log('fetchSystemList', form.getFieldsValue());
		return fetchListApiObj[from]?.(search);
	};

	// 基于sku拆分维度
	const responseAdapter = (data: SysItemGetSysItemListResponse["data"]) => {
		const sysSkuList: SysItemGetSysItemListResponse["data"]["list"] = [];
		const dataS = {};
		let itemCountData = {};
		data.list.forEach((item, i) => {
			item.sysSkuList.forEach((sku, index) => {
				let uniqId = `${item.platform}_${item.sysItemId}_${sku.sysSkuId}`;
				let one = {
					rowId: `${item.sysItemId}_${index}_${item.sysSkuList.length}`,
					groupId: `groupId${i}`,
					...(index === 0 ? { colSpan: item.sysSkuList.length } : {}),
					...item,
					sysSkuList: [sku],
					uniqId
				};
				sysSkuList.push(one);
				dataS[uniqId] = one;

				let preSelected = allSelectRows.filter(i => i.rowId == one.rowId);
				itemCountData[one.rowId] = preSelected[0]?.num || 1; // 因为要记住翻页选中 所以初始化数量需要判断是否选中的时候改过 需要恢复回去
			});
		});
		// console.log('sysSkuListsysSkuList', sysSkuList);
		setSkuList(sysSkuList);
		setDataSource(dataS);
		// 备份rowId数据用来批量修改数据回填写
		sysSkuListRowIdRef.current = sysSkuList.map(item => item.rowId);

		form.setFieldsValue({ itemCount: itemCountData });

		// setSelectedRowKeys([]);
		
		return {
			list: sysSkuList,
			total: data.total
		};
	};

	const disabledRowKey = useMemo(() => {
		const { key, value } = disabled || {};
		console.log('%c [ disabled ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
		return value?.map(v => dataSource[v]?.rowId) || [];
	}, [dataSource, disabled]);

	const _getDataSource = () => {
		const newRows = skuList?.filter(item => selectedRowKeys?.includes(item?.rowId));
		const currentPageAllRowIds = skuList?.map(row => row.rowId) || [];
		let obj = getFormBatchedNum();
		newRows?.forEach(row => {
			row.num = isFinite(toNumber(obj?.[row?.rowId])) ? toNumber(obj?.[row?.rowId]) : 1; 
		});
		onChange?.(selectedRowKeys, newRows, currentPageAllRowIds);
	};

	useEffect(() => {
		_getDataSource();
	}, [selectedRowKeys, skuList, getFormBatchedNum, from]);

	const rowSelection = {
		// columnWidth: 50,
		onChange: (selectedRowKeysAct: string[], rows:any) => {
			let selectedRowKeys = selectedRowKeysAct.filter(s => !disabledRowKey.includes(s));
			// 获得不在当前页的已选中项
			const currentPageAllRowIds = skuList?.map(row => row.rowId) || [];
			const otherPageSelectedRows = allSelectRows.filter(item => !currentPageAllRowIds.includes(item.rowId));

			console.log('后-其他', otherPageSelectedRows.length);
			console.log('后-当前', rows.length);

			if (isFinite(maxAddCount) && isFinite(addedCount)) {
				const allowAddCount = maxAddCount - addedCount;
				if (otherPageSelectedRows.length + rows.length > allowAddCount) {
					message.error(`最多添加${maxAddCount}个商品，请重新选择！`);
					return;
				}
			}
			console.log('%c [ selectedRowKeysAct ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', selectedRowKeysAct);
			setSelectedRowKeys(selectedRowKeys);
		},
		renderCell: (checked: boolean, row: SysItemGetSysItemListResponse["data"]["list"][0], index: number, originNode: any) => {
			const { key, value } = disabled || {};
			const [rId, i, length] = row.rowId.split('_');
			const disabledMap = {};
			if (value && value.length && dataSource) {
				value.forEach(v => {
					if (dataSource[v]) {
						const [id] = dataSource[v]['rowId'].split('_');
						if (!disabledMap[id]) disabledMap[id] = [];
						disabledMap[id].push(v);
					}
				});
			}

			let cloneNode = originNode;
			// let groupIndex = +row.groupId.replace('groupId', '') + 1;
			if (disabledMap[rId] && Number(length) === disabledMap[rId].length) {
				cloneNode = React.cloneElement(originNode, {
					disabled: true
				});
			}
			return {
				children: row.colSpan && (
					<>
						{/* <span style={ { marginRight: 3 } }>{groupIndex}</span> */}
						{cloneNode}
					</>
				),
				props: {
					style: row.colSpan ? { textAlign: 'right', paddingRight: '24px' } : { borderTop: 0, textAlign: 'right', paddingRight: '24px' }
				}
			};
		},
		getCheckboxProps: (record:any) => {
			const { key, value } = disabled || {};
			return {
				disabled: key ? value.includes(record['sysSkuList'][0][key]) : false
			};
		}
	};

	// 批量修改数量
	const batchEditCount = useCallback(() => {
		const onOk = async() => {
			// 确认保存，form表单数据提取
			await editCountForm.validateFields();
			const itemCount = editCountForm.getFieldValue("itemCount");
			const sysSkuListRowId = sysSkuListRowIdRef.current;
			console.log('itemCount', itemCount, sysSkuListRowId);
			let itemCountData = {};
			sysSkuListRowId.forEach((item, index) => {
				itemCountData[item] = itemCount;
			});
			form.setFieldsValue({ itemCount: itemCountData });
			editCountForm.resetFields();
		};
		confirm({
			centered: true,
			title: '批量填充',
			icon: "",
			content: (
				<Form form={ editCountForm }>
					<Space>
						<Form.Item
							className={ cs('r-mb-0') }
							name="itemCount"
							label="数量"
							rules={ [{ required: true,
								validator: (_:any, value:any, callback:()=>void) => {
									if (value && !/^[1-9]{1}[\d]*$/.test(value)) {
										return Promise.reject(new Error('请输入大于0的整数'));
									}
									if (!value) {
										return Promise.reject(new Error('数量不能为空'));
									}
									return Promise.resolve();
								} }] }
						>
							<InputNumber addonAfter="件" controls={ false } className={ cs('r-w-full', 'r-flex-1') } placeholder="必须大于0" />
						</Form.Item>
					</Space>

				</Form>
			),
			okText: '确定',
			cancelText: '取消',
			onOk,
			onCancel() {
				editCountForm.resetFields();
			},
		});
	}, [editCountForm, form]);

	const handleFormChange = (val, all) => {
		// console.log('%c [ handleFormChange ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', val, all);
		if (val?.itemCount) {
			_getDataSource();
		}
		// 切换组合货品的时候，也需要清理下选项
		if (_.has(val, 'isCombination')) {
			setSelectedRowKeys([]);
		}
	};

	const checkMaxNumber = useCallback(() => {
		let rowKeys = selectedRowKeys?.filter(s => !disabledRowKey.includes(s));
		// 获得不在当前页的已选中项
		const currentPageAllRowIds = skuList?.map(row => row.rowId) || [];
		const otherPageSelectedRows = allSelectRows.filter(item => !currentPageAllRowIds.includes(item.rowId));

		console.log('前-其他', otherPageSelectedRows.length);
		console.log('前-总:', rowKeys);

		if (isFinite(maxAddCount) && isFinite(addedCount)) {
			const allowAddCount = maxAddCount - addedCount;
			if (rowKeys.length >= allowAddCount) {
				message.error(`最多添加${maxAddCount}个商品，请重新选择！`);
				return false;
			}
		}
		return true;
	}, [selectedRowKeys, disabledRowKey, maxAddCount, addedCount]);

	// 行点击，这里需要行内的子元素点击事件停止冒泡
	const handleRowClick = (e, record, index) => {
		if (disabledRowKey?.includes(record?.rowId)) {
			return;
		}

		if (!checkMaxNumber()) {
			return;
		}

		let _selectedRowKeys = [...selectedRowKeys];
		if (type == 'checkbox') {
			if (!_selectedRowKeys.includes(record.rowId)) {
				_selectedRowKeys.push(record.rowId);
			} else {
				_selectedRowKeys = _selectedRowKeys?.filter(key => key !== record.rowId);
			}
		} else {
			// 单选直接切换
			_selectedRowKeys = [record?.rowId];
		}
		
		setSelectedRowKeys(_selectedRowKeys);
	};

	return (
		<div className={ s.table }>
			<SearchTable<SysItemGetSysItemListResponse["data"]["list"][0]>
				pageSizeId="ChooseGoodsModal"
				ref={ tableRef }
				form={ form }
				fetchData={ fetchSystemList }
				responseAdapter={ responseAdapter }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				autoSearch={ autoSearch }
				searchBtnProps={ {
					size: 'small'
				} }
				rowFormConfig={ {
					formList: FormFieldList,
					colProps: {
						// span: 4 
					},
					rowProps: {
						style: {
							margin: 0
						}
					},
					style: {
						background: '#ffffff',
						paddingTop: '0'
					},
				} }
				baseTableConfig={ {
					rowKey: 'rowId',
					groupId: 'groupId',
					cachePgination: true,
					noPadding: true,
					columns,
					scroll: {
						y: props.scrollY || (getModalTableScrollHeight() - 40)
					},
					pagination: {
						size: 'small'
					},
					rowSelection: {
						type,
						...rowSelection,
						...{
							selectedRowKeys
						}
					},
					subRowSelection: {
						index: 1
					},
					dataSource: skuList,
					onFieldsChange: handleFormChange,
					onRow: (record, index) => ({
						onClick: (e) => handleRowClick(e, record, index)
					}) 	
				} }
			/>
		</div>
	);
});
export default ProductList;
