import { Button, Input, Checkbox, Tooltip, Space, Form } from 'antd';
import React, { cloneElement, useCallback, useEffect, useMemo, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/lib/table';
import dayjs, { Dayjs } from 'dayjs';
import { useUpdateEffect, useUnmount } from 'ahooks';
import { isFinite, debounce, toNumber } from 'lodash';
import cs from 'classnames';
import s from './index.module.scss';
import SearchTable from '@/components/SearchTable';
import message from '@/components/message';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { 
	ItemSysItemListOfItemRelationPlatformItemViewRequest, ItemSysItemListOfItemRelationPlatformItemViewResponse,
	ItemCommonItemSaveOrDelRequest, ItemCommonItemSaveOrDelResponse
} from '@/types/schemas/warehouse/system';
import { TradeListOfItemRelationPlatformItemViewApi, ItemCommonItemSaveOrDelApi } from '@/apis/warehouse/system';
import WaresInfo from '@/components-biz/WaresInfo';
import ShopSingleSelect, { ShopSingleSelectValue } from "@/components-biz/ShopListSelect/shopSingleSelect";
import { Platform } from "@/utils/enum/productManage";
import { platform } from '@/types/schemas/common';
import { PLAT_FXG, PLAT_HAND, CommonItemType } from '@/constants';
import useGetState from '@/utils/hooks/useGetState';
import userStore from "@/stores/user";
import { weightUnit } from '@/pages/Index/Settings/System/constants';
import { FetchSysItemListEnum } from '@/components-biz/Product/List';
import Icon from "@/components/Icon";
import EnumSelect from '@/components/Select/EnumSelect';
import { getModalTableScrollHeight } from '@/utils/util';
import { fetchWithPaginationOptimization } from '@/pages/Warehouse/System/Relation/components/PlatformTable/utils/paginationOptimization';

const initSearchParams: ItemSysItemListOfItemRelationPlatformItemViewRequest = {
	platform: '',
	sellerNick: '',
};
interface ZeroStockOnlineListProps {
	type?: 'checkbox' | 'radio',
	onChange?: Function,
	showColumnKeys?:string[];
	selectedDisabled?: boolean,
	tableLocale?: {},
	disabled?: {
		key: string,
		value: any[]
	},
	defaultParams?: {
		platform?: platform,
		shopId?: string,
		itemTitle?: string,
	};
	maxAddCount?: number // 可添加的最大数量
	addedCount?: number // 已添加的数量
	from?: string
	allSelectRows?: any[]
    hasBatchedNum?: boolean
	hasCollect?: boolean
	scrollY?: string
	autoSearch?: boolean
	returnSupplierInfo?: boolean
}

const ZeroStockOnlineList = (props:ZeroStockOnlineListProps, ref?: React.Ref<any>) => {
	const [form] = useForm();
	const {
		type = 'checkbox',
		showColumnKeys = [],
		defaultParams = {},
		disabled,
		selectedDisabled,
		onChange,
		maxAddCount,
		addedCount,
		from = FetchSysItemListEnum.其他,
		hasBatchedNum = false,
		hasCollect = false,
		allSelectRows,
		autoSearch = true,
		returnSupplierInfo = false, // 是否库存版本返回关联的系统货品市场档口供应商信息
	} = props;
	const tableRef = useRef<SearchTableRefProps>();
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [dataSource, setDataSource, getDataSource] = useGetState({});
	const [skuList, setSkuList] = useState<ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]['list']>([]);
	const [searchParams, setSearchParams] = useState<ItemSysItemListOfItemRelationPlatformItemViewRequest>({ ...initSearchParams });
	const { platform, shopId, itemTitle } = defaultParams;
	const [isKg, setIsKg] = useState(false);
	const isShowSetCommon = useMemo(() => showColumnKeys.includes('setCommon'), [showColumnKeys]); // 是否显示常用商品设置

	const { isShowZeroStockVersion } = userStore;

	const getFormBatchedNum = useCallback(() => {
		const formBatchedNum = form.getFieldValue('itemCount');
		return formBatchedNum;
	}, [form]);
    
	const _getDataSource = () => {
		const newRows = skuList?.filter(item => selectedRowKeys?.includes(item?.rowId));
		const currentPageAllRowIds = skuList?.map(row => row.rowId) || [];

		let obj = getFormBatchedNum();
		newRows?.forEach(row => {
			row.num = isFinite(toNumber(obj?.[row?.rowId])) ? toNumber(obj?.[row?.rowId]) : 1; 
		});

		onChange?.(selectedRowKeys, newRows, currentPageAllRowIds);
	};

	useImperativeHandle(ref, () => {
		return {
			setSelectedRowKeys,
			_getDataSource,
			form,
		};
	}, []);
    
	const shopChange = ({ plat, shopId }: ShopSingleSelectValue) => {
		console.log(plat, shopId, 'shopChange');
		setSearchParams({
			platform: plat,
			sellerId: shopId,
		});
	};
	// 表单元素定义 下拉框？日期？ 多选框？ 多选按钮？
	const FormFieldList: FormItemConfig[] = [
		{
			name: 'ShopData',
			label: "",
			className: 'r-mb-8',
			initialValue: { shopId, plat: platform },
			children: (
				<ShopSingleSelect
					disabledShop={ platform && platform !== PLAT_HAND }
					platDisabled={ platform && platform !== PLAT_HAND }
					onChange={ shopChange }
					style={ { width: 110 } }
					size="small"
				/>
			)
		},
		// {
		// 	name: 'itemContent',
		// 	label: "",
		// 	className: 'r-mb-8',
		// 	children: <Input placeholder="商品名称/简称/编码/ID" style={ { width: 180 } } size="small" />,
		// },
		{
			name: "title",
			className: 'r-mb-8',
			children: (
				<Input 
					placeholder="商品名称" 
					style={ { width: 110 } } 
					size="small"
				/>
			),
		},
		{
			name: "sysItemAlias",
			className: 'r-mb-8',
			children: (
				<Input 
					placeholder="简称" 
					style={ { width: 110 } } 
					size="small"
				/>
			),
		},
		{
			name: "outerId",
			className: 'r-mb-8',
			children: (
				<Input 
					placeholder="商家编码" 
					style={ { width: 110 } } 
					size="small"
				/>
			),
		},
		{
			name: "numIids",
			className: 'r-mb-8',
			children: (
				<Input 
					placeholder="商品ID" 
					style={ { width: 110 } } 
					size="small"
				/>
			),
		},
		{
			name: 'skuOuterId',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="规格编码" style={ { width: 110 } } size="small" />
		},
		{
			name: 'skuContent',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="规格/规格别名" style={ { width: 130 } } size="small" />
		},
		{
			name: 'itemNo',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="货号" style={ { width: 110 } } size="small" />
		},
		{
			name: 'barCode',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="条形码" style={ { width: 110 } } size="small" />
		}
	];

	if (isShowSetCommon) {
		let newObject = {
			name: 'findCommonItem',
			label: "",
			className: 'r-mb-8',
			children: <EnumSelect placeholder="是否常用商品" enum={ CommonItemType } size="small" style={ { width: 130 } } />,
		};
		FormFieldList.splice(1, 0, newObject); // 商品名称前面插入一个
	}

	const defaultColumns = [
		{
			title: '平台',
			dataIndex: 'platform',
			key: 'platform',
			width: 60,
			className: cs(s.noline),
			render: (text, row, index) => {
				return {
					children: row.colSpan && (<div className={ cs('r-fs-14', 'r-fc-black-65') }>{Platform[row.platform]}</div>),
					props: {
					// rowSpan: row.colSpan || 0,
					},
				};
			},
			onCell: (row: any, index: number) => {
				return {
					style: row.colSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '店铺',
			dataIndex: 'sellerNick',
			key: 'sellerNick',
			width: 80,
			className: cs(s.noline),
			render: (text, row, index) => {
				return {
					children: row.colSpan && (<div className={ cs('r-fs-14', 'r-fc-black-65') }>{row.sellerNick}</div>),
					props: {
					// rowSpan: row.colSpan || 0,
					},
				};
			},
			onCell: (row: any, index: number) => {
				return {
					style: row.colSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '商品名称',
			dataIndex: 'title',
			key: 'title',
			width: 180,
			className: cs('r-l-preWrap'),
			render: (text, row, index) => {
				return {
					children: row.colSpan && (<div className={ cs('r-fs-14', 'r-fc-black-65') }>{row.title}</div>),
					props: {
					// rowSpan: row.colSpan || 0,
					},
				};
			},
			onCell: (row: any, index: number) => {
				return {
					style: row.colSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '简称',
			dataIndex: 'sysItemAlias',
			key: 'sysItemAlias',
			width: 80,
			render: (text, row) => {
				const { skuOuterId = "", sysItemAlias = "", sysSkuAlias = "", } = row.platformItemSkuList[0]?.relationSystemItemList[0] || {};
				return <span>{sysItemAlias}</span>;
			},
			onCell: (row: any, index: number) => {
				return {
					style: row.colSpan ? { borderRight: '1px solid #f0f0f0' } : { borderTop: 0, borderRight: '1px solid #f0f0f0' }
				};
			}
		},
		{
			title: '规格',
			width: 160,
			key: 'skuInfo',
			render: (text, row) => {
				return (
					<WaresInfo 
						imgUrl={ row.platformItemSkuList[0].picUrl } 
						skuName={ row.platformItemSkuList[0].skuName } 
						align="fs"
					/>
				);
			}
		},
		{
			title: '规格别名',
			dataIndex: 'sysSkuAlias',
			key: 'sysSkuAlias',
			width: 80,
			render: (text, row) => {
				const { skuOuterId = "", sysItemAlias = "", sysSkuAlias = "", } = row.platformItemSkuList[0]?.relationSystemItemList[0] || {};
				return <span>{sysSkuAlias}</span>;
			}
		},
		{
			title: '规格编码',
			dataIndex: 'skuOuterId',
			key: 'skuOuterId',
			width: 80,
			render: (text, row) => {
				return <span>{row.platformItemSkuList[0].skuOuterId}</span>;
			}
		}
	];

	if (hasBatchedNum) {
		defaultColumns.splice(7, 0, {
			title: (
				<p className="r-flex">
					数量
					{/* <span className="r-ml-8 r-pointer" style={ { color: '#FD8204' } } onClick={ debounce(() => batchEditCount(), 500, { 'leading': true, 'trailing': false }) }>批量填充</span> */}
				</p>
			),
			width: 80,
			align: 'center',
			dataIndex: 'itemCount',
			key: 'itemCount',
			render: (text, record:any, index) => {
				return (
					<Space onClick={ e => { e.stopPropagation(); } }>
						<Form.Item
							className={ cs('r-mb-0') }
							name={ ["itemCount", record.rowId] }
							rules={ [{
								// required: true,
								validator: (_:any, value:any, callback:()=>void) => {
									if (value && !/^[1-9]{1}[\d]*$/.test(value)) {
										return Promise.reject(new Error('请输入大于0的整数'));
									}
									if (!value) {
										return Promise.reject(new Error('数量不能为空'));
									}
									// 注意取值
									// if (Number(value) < Number(record.instockCount)) {
									// 	return Promise.reject(new Error('数量有误'));
									// }
									return Promise.resolve();
								}
							}] }
						>
							<Input size="middle" />
						</Form.Item>
						{/* <span>件</span> */}
					</Space>
				);
			}
		});
	}


	const needFilterColumns = [{
		title: `重量 (${isKg ? 'kg' : 'g'})`,
		width: 80,
		key: 'weight',
		render: (text, row) => {
			const weight = row.platformItemSkuList[0]?.relationSystemItemList[0]?.weight;
			if (isKg && weight) {
				return <span> {Number(+weight / 1000).toFixed(3)}</span>;
			}
			return <span>{weight || ''}</span>;
		}
	},
	{
		title: '成本价 (元)',
		width: 80,
		key: 'costPrice',
		render: (text, row) => {
			const costPrice = row.platformItemSkuList[0]?.relationSystemItemList[0]?.costPrice;
			return <span>{costPrice}</span>;
		}
	},
	{
		title: '货号',
		width: 80,
		key: 'itemNo',
		render: (text, row) => {
			return <span>{row.platformItemSkuList[0].itemNo}</span>;
		}
	},
	{
		title: '条形码',
		width: 80,
		key: 'barCode',
		render: (text, row) => {
			return <span>{row.platformItemSkuList[0].barCode}</span>;
		}
	},
	{
		title: '市场',
		width: 80,
		key: 'market',
		render: (text, row) => {
			return <span>{row?.platformItemSkuList?.[0]?.relationSystemItemList?.[0]?.market}</span>;
		}
	},
	{
		title: '档口',
		width: 80,
		key: 'stall',
		render: (text, row) => {
			return <span>{row?.platformItemSkuList?.[0]?.relationSystemItemList?.[0]?.stall}</span>;
		}
	},
	{
		title: '供应商',
		width: 80,
		key: 'supplierName',
		render: (text, row) => {
			return <span>{row?.platformItemSkuList?.[0]?.relationSystemItemList?.[0]?.supplierName}</span>;
		}
	},
	{
		title: '常用',
		width: 80,
		key: 'setCommon', // showColumnKeys 配置
		render: (text, row) => {
			return (
				<div>
					{row.platformItemSkuList[0]?.commonItem ? (
						<Tooltip title="取消常用">
							<span>
								<Icon svg size={ 18 } type="shoucang-mian" style={ { color: '#FEB432' } } onClick={ (e) => { e.stopPropagation(); handleSetCommon(row, false); } } />
							</span>
						</Tooltip>
					) : (
						<Tooltip title="设为常用">
							<span>
								<Icon svg size={ 18 } type="shoucang-xian" style={ { color: '#999' } } onClick={ (e) => { e.stopPropagation(); handleSetCommon(row, true); } } />
							</span>
						</Tooltip>
					)}
				</div>
			);
		}
	}];
	needFilterColumns.forEach(column => {
		if (showColumnKeys.includes(column.key)) {
			defaultColumns.push(column);
		}
	});
	// 表格列定义，与AntdTable使用一致
	const columns: ColumnsType<ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]> = defaultColumns;

	useUpdateEffect(() => {
		if (!selectedDisabled) {
			setSelectedRowKeys([]);
		}
	}, [disabled]);

	// 接口查询、查询参数重装
	const fetchSystemList = (info: any, type: string) => {
		for (let key in info) {
			if (typeof info[key] === 'string') {
				info[key] = info[key].trim();
			}
		}
		const { findCommonItem, itemContent, title, sysItemAlias, outerId, numIids, skuOuterId, skuContent, pageNo, pageSize, barCode, skuName, itemNo, ShopData = {} } = info;
		if (type === 'reset') {
			searchParams.platform = "";
			searchParams.sellerId = "";
			setSearchParams({
				platform: '',
				sellerId: ''
			});
		}
		if (type === 'submit') {
			searchParams.platform = ShopData.plat;
			searchParams.sellerId = ShopData.shopId;
			setSearchParams({
				platform: ShopData.plat,
				sellerId: ShopData.shopId
			});
		}
		let search: ItemSysItemListOfItemRelationPlatformItemViewRequest = {
			...searchParams,
			findCommonItem: findCommonItem ? findCommonItem == 1 : undefined, // 是否常用商品
			setCommonItem: true, // 是否设置常用商品标记 写死参数
			skuOuterId,
			skuContent, // 规格名称/规格别名
			barCode,
			skuName,
			itemNo,
			// itemContent, // 商品名称/简称/编码/ID
			title,
			sysItemAlias,
			outerId,
			numIids: numIids ? [numIids] : undefined,
			pageNo,
			pageSize,
			returnSupplierInfo,
		};
		// if (search?.platform == PLAT_FXG) {
		// 	search.approveStatus = 'onsale'; // 抖音接口太慢了加上这个参数
		// }

		if (from == FetchSysItemListEnum.订单) {
			return TradeListOfItemRelationPlatformItemViewApi(search);
		} else {
			return fetchWithPaginationOptimization(search, pageSize);
		}
	};

	// 设置、取消设置常用商品
	const handleSetCommon = debounce((row:any, type:boolean) => {
		const search: ItemCommonItemSaveOrDelRequest = {
			type: type ? 1 : 2, // 1:新增,2:删除
			skuId: row?.platformItemSkuList?.[0]?.skuId, 
			numIid: row?.numIid, // 商品id
			// sysSkuId:'',
			// sysItemId:'',
		};

		ItemCommonItemSaveOrDelApi(search)
			.then((res) => {
				if (res) {
					updateList(search);
				}
			})
			.catch((res => {}));
	}, 300);

	const updateList = (search:ItemCommonItemSaveOrDelRequest) => {
		setSkuList(pre => {
			const newList = [...pre];
			newList.forEach(item => {
				if (search.numIid == item.numIid && item?.platformItemSkuList?.[0]?.skuId == search.skuId) {
					item['platformItemSkuList'][0].commonItem = search.type == 1;
				}
			});
			return newList;
		});
	};

	const handleSelectedRows = () => {
		const selectedRowKeys = [];
		Object.values(getDataSource()).forEach(dataSourceItem => {
			const { key, value } = disabled || {};
			if (key && value.includes(dataSourceItem[key])) {
				selectedRowKeys.push(dataSourceItem.rowId);
			}
		});
		setSelectedRowKeys(selectedRowKeys);
	};

	// 基于sku拆分维度
	const responseAdapter = (data: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]) => {
		const platformItemSkuList: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]['list'] = [];
		const dataS = {};
		let itemCountData = {};
		data.list.forEach((item, i) => {
			item.platformItemSkuList.forEach((sku, index: any) => {
				let uniqId = `${item.platform}_${item.numIid}_${sku.skuId}_${sku.skuName}`;
				let one = {
					rowId: `${item.numIid}_${index}_${item.platformItemSkuList.length}`,
					groupId: `groupId${i}`,
					...(index === 0 ? { colSpan: item.platformItemSkuList.length } : {}),
					...item,
					platformItemSkuList: [sku],
					title: item.title,
					uniqId
				};
				platformItemSkuList.push(one);
				dataS[uniqId] = one;

				let preSelected = allSelectRows.filter(i => i.rowId == one.rowId);
				itemCountData[one.rowId] = preSelected[0]?.num || 1; // 因为要记住翻页选中 所以初始化数量需要判断是否选中的时候改过 需要恢复回去
				// dataS[sku.relationSystemItemList[0]?.['sysSkuId']] = one;
			});
		});
		setDataSource(dataS);
		if (selectedDisabled) {
			handleSelectedRows();
		}

		form.setFieldsValue({ itemCount: itemCountData });

		// console.log('platformItemSkuList', platformItemSkuList);
		setSkuList(platformItemSkuList);
		
		return {
			list: platformItemSkuList,
			total: data.total
		};
	};

	const disabledRowKey = useMemo(() => {
		// console.log('disabled', disabled, dataSource);
		const { key, value } = disabled || {};
		console.log('%c [ disabled ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
		return value?.map(v => dataSource?.[v]?.rowId) || [];
	}, [dataSource, disabled]);

	const rowSelection = {
		onChange: (selectedRowKeysAct: string[], rows: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"]) => {
			let selectedRowKeys = selectedRowKeysAct.filter(s => !disabledRowKey.includes(s));
			// 获得不在当前页的已选中项
			const currentPageAllRowIds = skuList?.map(row => row.rowId) || [];
			const otherPageSelectedRows = allSelectRows.filter(item => !currentPageAllRowIds.includes(item.rowId));

			if (isFinite(maxAddCount) && isFinite(addedCount)) {
				const allowAddCount = maxAddCount - addedCount;
				if (otherPageSelectedRows.length + rows.length > allowAddCount) {
					message.error(`最多添加${maxAddCount}个商品，请重新选择！`);
					return;
				}
			}
			setSelectedRowKeys(selectedRowKeys);
			// onChange?.(selectedRowKeys, rows);
		},
		renderCell: (checked: boolean, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0], index: number, originNode: any) => {
			const { key, value } = disabled || {};
			const [rId, i, length] = row.rowId.split('_');
			const disabledMap = {};
			if (value && value.length) {
				value.forEach(v => {
					const arr = dataSource?.[v]?.['rowId'].split('_');
					let id = arr?.[0];
					if (!disabledMap[id]) disabledMap[id] = [];
					disabledMap[id].push(v);
				});
			}

			let cloneNode = originNode;
			if (disabledMap[rId] && Number(length) === disabledMap[rId].length) {
				cloneNode = React.cloneElement(originNode, {
					disabled: true
				});
			}
			return {
				children: row.colSpan && cloneNode,
				props: {
					// rowSpan: row.colSpan || 0,
					style: row.colSpan ? {} : { borderTop: 0 }
				},
			};
		},
		getCheckboxProps: (record: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
			const { key, value } = disabled || {};
			return {
				disabled: key ? value.includes(record?.[key]) : false
			};
		}
	};

	const getEmptyText = () => {
		// let href = '#/warehouse/relation';
		// if (isShowZeroStockVersion) {
		// 	href = '#/warehouse/platformGoods';
		// }
		// return (
		// 	<div style={ { minHeight: 200 } } className="r-flex r-jc-c r-ai-c r-fs-16">
		// 		请先前往商品页面下载同步平台商品后再进行平台商品添加/更换
		// 		<a target="_blank" className="kdzs-link-text" href={ href } rel="noreferrer">点击前往</a>
		// 	</div>
		// );

		return (
			<div style={ { minHeight: 200 } } className="r-fs-16">
				<img style={ { width: '147px' } } src="https://img.alicdn.com/imgextra/i3/69942425/O1CN01XalmOh1Tmh4RNp7Gp_!!69942425.png" alt="" />
				<p className="r-mb-40 r-mt-14 r-fs-14 r-pointer" onClick={ () => { tableRef?.current?.submit(); } }>请点击查询</p>
			</div>
		);
	};

	useEffect(() => {
		_getDataSource();
	}, [selectedRowKeys, skuList, getFormBatchedNum, from]);

	const handleFormChange = (val, all) => {
		// console.log('%c [ handleFormChange ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', val, all);
		if (val?.itemCount) {
			_getDataSource();
		}
	};

	useEffect(() => {
		setSearchParams({
			platform,
			sellerId: shopId,
		});
		userStore.getUserSetting().then(res => {
			setIsKg(res?.weightUnit == weightUnit.显示kg);
		});
	}, []);

	const checkMaxNumber = useCallback(() => {
		let rowKeys = selectedRowKeys?.filter(s => !disabledRowKey.includes(s));
		// 获得不在当前页的已选中项
		const currentPageAllRowIds = skuList?.map(row => row.rowId) || [];
		const otherPageSelectedRows = allSelectRows.filter(item => !currentPageAllRowIds.includes(item.rowId));

		if (isFinite(maxAddCount) && isFinite(addedCount)) {
			const allowAddCount = maxAddCount - addedCount;
			if (rowKeys.length >= allowAddCount) {
				message.error(`最多添加${maxAddCount}个商品，请重新选择！`);
				return false;
			}
		}
		return true;
	}, [selectedRowKeys, disabledRowKey, maxAddCount, addedCount]);

	// 行点击，这里需要行内的子元素点击事件停止冒泡
	const handleRowClick = (e, record, index) => {
		if (disabledRowKey?.includes(record?.rowId)) {
			return;
		}

		if (!checkMaxNumber()) {
			return;
		}

		let _selectedRowKeys = [...selectedRowKeys];
		if (type == 'checkbox') {
			if (!_selectedRowKeys.includes(record.rowId)) {
				_selectedRowKeys.push(record.rowId);
			} else {
				_selectedRowKeys = _selectedRowKeys?.filter(key => key !== record.rowId);
			}
		} else {
			// 单选直接切换
			_selectedRowKeys = [record?.rowId];
		}
		
		setSelectedRowKeys(_selectedRowKeys);
	};

	return (
		<div>
			<div className={ s.table }>
				<SearchTable<ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]>
					pageSizeId="ChooseGoodsModal"
					ref={ tableRef }
					form={ form }
					allowClear
					fetchData={ fetchSystemList }
					responseAdapter={ responseAdapter }
					searchBtnText="查询"
					resetBtnText="重置"
					showSearch
					autoSearch={ autoSearch }
					rowFormConfig={ {
						formList: FormFieldList,
						defaultParams: { title: itemTitle, ShopData: { shopId, plat: platform } },
						colProps: {
							// span: 3
						},
						rowProps: {
							style: {
								margin: 0
							}
						},
						style: {
							background: '#ffffff',
							paddingTop: 0
						},
					} }
					baseTableConfig={ {
						rowKey: 'rowId',
						groupId: 'groupId',
						cachePgination: true,
						noPadding: true,
						columns,
						scroll: {
							y: props.scrollY || (getModalTableScrollHeight() - 40)
						},
						locale: {
							emptyText: getEmptyText()
						},
						pagination: {
							size: 'small'
						},
						rowSelection: {
							type,
							...rowSelection,
							...{
								selectedRowKeys
							}
						},
						subRowSelection: {
							index: 4
						},
						dataSource: skuList,
						onFieldsChange: handleFormChange,
						onRow: (record, index) => ({
							onClick: (e) => handleRowClick(e, record, index)
						}) 	
					} }
				/>
			</div>
		</div>
	);
};
export default forwardRef(ZeroStockOnlineList);
