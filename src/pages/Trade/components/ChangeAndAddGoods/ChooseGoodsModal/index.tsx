import React, { useCallback, useEffect, useRef, useState, useMemo } from "react";
import { Modal, Tabs } from "antd";
import { observer } from "mobx-react";
import { runInAction } from 'mobx';
import { cloneDeep, debounce } from 'lodash';
import { editGoodsStore, tradeStore } from '@/stores';
import userStore from "@/stores/user";
import message from '@/components/message';
import { TradeTradeDetailGetApi } from "@/apis/trade";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { platform } from '@/types/schemas/common';
import { Base } from "@/types/schemas";
import { GoodsTypeEnum, allPlatform } from '../constants';
import GoodsList from './goodsList';
import SysItemsList from './sysItemsList';
import s from './index.module.scss';

interface Iprops extends Base {
	visible: boolean;
    onOk?: (selectRows:any[], type:any)=>void;
    onClose?: ()=>void;
	type?:"checkbox" | "radio";
	defaultParams?:{
		platform?:platform,
		shopId?:string,
		itemTitle?:string,
	};
	disabled?: {key:string, value:any[]};
	inputTitle?:string;
	localKey?:string;// 浏览器默认tab选项
	hasCollect?:boolean;// 是否有收藏按钮，创建手工单添加了收藏按钮
	beforeOk?: (selectRows:any[], type:any) => Promise<boolean>;
	isAllHand?:boolean; // 是否是手工单
	hasBatchedNum?: boolean, // 表格是否支持批量编辑数量
	chooseType?: string, // 'addGoods'
}

const ChooseGoodsModal = (props: Iprops) => {
	// console.log('%c [ defaultParams ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', props.defaultParams);
	const { 
		visible, 
		onOk, 
		onClose, 
		inputTitle,
		isAllHand = false,
		hasBatchedNum = false,
		hasCollect = false,
		chooseType = 'changeGoods',
		localKey
	} = props;
	const [selectRows, setSelectRows] = useState<any[]>([]);
	const [confirmLoading, setConfirmLoading] = useState(false);
	const [type, setType] = useState<number>(() => {
		if (localKey) {
			const savedTab = localStorage.getItem(localKey);
			return savedTab ? Number(savedTab) : GoodsTypeEnum.平台商品;
		}
		return GoodsTypeEnum.平台商品;
	}); // 0商品、1货品

	const searchRef = useRef(null);
	const searchRef2 = useRef(null);

	const {
		isShowBatchAddGoodsModal,
		isShowChangeGoodsModal,
		isShowBatchChangeGoodsModal,
	} = editGoodsStore;

	const { isShowZeroStockVersion } = userStore;

	useEffect(() => {
		// 手工单
		if (isAllHand) {
			setType(isShowZeroStockVersion ? GoodsTypeEnum.平台商品 : GoodsTypeEnum.系统货品);
		}
	}, [isAllHand, isShowZeroStockVersion]);

	const onChange = (keys: string[], rows: any[], currentPageAllRowIds: any[]) => {
		// 处理不在当前页的已选中项
		const otherPageSelectedRows = selectRows.filter(item => !currentPageAllRowIds.includes(item.rowId));

		// 处理当前页的选中项
		const currentPageSelectedRows = rows?.map(row => ({
			...row,
			num: row.num || 1,
			originNode: null
		})) || [];

		// 合并结果
		const finalRows = [...otherPageSelectedRows, ...currentPageSelectedRows];
		setSelectRows(finalRows);
	};

	const handleTabChange = (activeKey:string) => {
		const newType = Number(activeKey);
		setType(newType);
		if (localKey) {
			localStorage.setItem(localKey, String(newType));
		}

		// 切换tab,需要清空勾选项
		setSelectRows([]);
		searchRef?.current?.setSelectedRowKeys([]);
		searchRef2?.current?.setSelectedRowKeys([]);
	};

	const handleCancel = () => {
		onClose?.();
		setSelectRows([]);
		setConfirmLoading(false);
	};

	const handleOk = async() => {
		setConfirmLoading(true);

		try {
			if (props?.beforeOk) {
				const flag = await props.beforeOk(selectRows, type);
				setConfirmLoading(false);
				if (!flag) return;
			}
			setConfirmLoading(false);
			onOk?.(selectRows, type);
			handleCancel();
		} catch (e) {
			console.log(e);
			setConfirmLoading(false);
		}
	};

	const showTypeName = useMemo(() => {
		return type == GoodsTypeEnum.平台商品 ? '商品' : '货品';
	}, [type]);

	// 删除这条选项
	const handleRemoveItem = (item:any) => {
		let newSelectRows = selectRows?.filter(d => d.rowId != item.rowId);
		setSelectRows(newSelectRows);
		if (type == GoodsTypeEnum.平台商品) {
			searchRef?.current?.setSelectedRowKeys(newSelectRows.map(item => item.rowId));
		} else {
			searchRef2?.current?.setSelectedRowKeys(newSelectRows.map(item => item.rowId));
		}
	};

	return (
		<Modal
			className={ s.modal }
			visible={ visible }
			width={ 1200 }
			title={ chooseType == 'changeGoods' ? "选择商品" : "选择商品" }
			okText="保存并关闭"
			cancelText="取 消"
			onCancel={ handleCancel }
			onOk={ handleOk }
			okButtonProps={ { loading: confirmLoading } }
			cancelButtonProps={ { hidden: false } }
			maskClosable={ false }
			destroyOnClose
			centered
			bodyStyle={ { paddingBottom: '0px' } }
		>
			<div className={ s.checkList }>
				<div className={ s.listType }>{inputTitle || `要替换的${showTypeName}`}</div>
				<div className={ s.list }>
					<div className={ s.listNum }>已选择{selectRows?.length}个{showTypeName}</div>
					<div className={ s.listMain }>
						{selectRows.map((item, index) => (
							<div className={ s.listItem } key={ item?.rowId }>
								{
									type == GoodsTypeEnum.平台商品 ? (
										<span>{item?.numIid}-{item?.platformItemSkuList?.[0]?.skuId} *{item?.num || 1}</span>
									) : (
										<span>{item?.sysSkuList?.[0]?.skuOuterId} *{item?.num || 1}</span>
									)
								}
								<span className={ s.itemClose } onClick={ () => handleRemoveItem(item) }>×</span>
							</div>
						))}
					</div>
				</div>
			</div>

			<Tabs className={ s.tabs } onChange={ handleTabChange } activeKey={ `${type}` }>
				
				{/* 这次放开手工单的限制 */}
				<Tabs.TabPane tab="从平台商品中选择" key={ GoodsTypeEnum.平台商品 }>
					<GoodsList
						showColumnKeys={ ['weight', 'costPrice', hasCollect && 'setCommon'].filter(Boolean) }
						selectedDisabled={ props.selectedDisabled }
						defaultParams={ props.defaultParams }
						disabled={ props.disabled }
						type={ props.type || 'checkbox' }
						onChange={ onChange }
						maxAddCount={ props.maxAddCount }
						addedCount={ props.addedCount }
						from={ props.from }
						ref={ searchRef }
						hasBatchedNum={ hasBatchedNum }
						allSelectRows={ selectRows }
					/>
				</Tabs.TabPane>
			

				{
					!isShowZeroStockVersion && (
						<Tabs.TabPane tab="从ERP系统货品中选择" key={ GoodsTypeEnum.系统货品 }>
							<SysItemsList 
								code={ props.code }
								ref={ searchRef2 }
								from={ props.from }
								supplier={ props.supplier } 
								maxAddCount={ props.maxAddCount } 
								disabledCombined={ props.disabledCombined } 
								addedCount={ props.addedCount } 
								disabled={ props.disabled }  
								type={ props.type || 'checkbox' } 
								onChange={ onChange } 
								showColumnKeys={ [hasCollect && 'setCommon'].filter(Boolean) }
								hasBatchedNum={ hasBatchedNum }
								allSelectRows={ selectRows }
							/>
						</Tabs.TabPane>
					)
				}
			</Tabs>
				
			
		</Modal>
	);

};

export default observer(ChooseGoodsModal);
