.modal{
    .checkList{
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;

        .listType{
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 16px;
        }
        .list{
            flex: 1;
            display: flex;
            align-items: center;
            border: 1px solid #D9D9D9;
            padding: 4px;
            gap: 4px;
            background: #FFFFFF;
            box-sizing: border-box;
            
            .listNum{
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
                flex-shrink: 0;
            }

            .listMain{
                flex: 1;
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                max-height: 60px;
                overflow-y: auto;
            }

            .listItem{
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 0px 4px 0px 4px;
                gap: 4px;
                border-radius: 2px;
                background: #F5F5F5;
                box-sizing: border-box;
                border: 1px solid #F0F0F0;
                font-size: 12px;
                line-height: 20px;
                color: rgba(0, 0, 0, 0.85);

                .itemClose{
                    cursor: pointer;
                    font-size: 16px;
                    color:  rgba(0, 0, 0, 0.45);
                    padding: 0 4px;
                }
            }
        }
    }
}

.table{
    :global{
        .ant-table{
            color: rgba(0, 0, 0, 0.65);
        }

        .formRowContainer{
            &::after{
                display: none!important;
            }
        }
        .rowFormConfigStyle{
            padding-bottom: 8px !important;
        }

        .ant-form-item-explain-error{
            font-size: 12px;
        }
    }
}