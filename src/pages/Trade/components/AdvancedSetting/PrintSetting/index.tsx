import { Button, Checkbox, InputNumber, Radio, Select, Tooltip } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { ExclamationCircleFilled, QuestionCircleFilled } from '@ant-design/icons';
import { useHistory } from 'react-router-dom';
import Pointer from '@/utils/pointTrack/constants';
import { handOrderMatchPlatformMap } from '@/pages/Trade/constants';
import { PLAT_MAP, PLAT_KTT, PLAT_HAND } from '@/constants';
import userStore from '@/stores/user';
import { refluxOrderPrintSettingsEnum } from '@/types/trade/index';
import Icon from "@/components/Icon";
import { local } from '@/libs/db';

const Option = Select.Option;

const PrintSetting = (props: any) => {
	let { setting, handleInputChange, handleDragBtn, setShowControlsDownModal } = props;
	const history = useHistory();
	useEffect(() => {
		// console.log(setting?.groupPrintSetJsonString?.orderMatchSetting);
	}, [setting?.groupPrintSetJsonString?.orderMatchSetting]);
	useEffect(() => {
		if (local.get('splitOrderAutoRemark') && document.getElementById('addYdNoMemo')) {
			document.getElementById('addYdNoMemo').scrollIntoView({ behavior: 'smooth' });
		}
		return () => {
			local.remove('splitOrderAutoRemark');
		};
	}, []);
	const selectOptions = (o: any) => {
		let platformMap = handOrderMatchPlatformMap;
		if (o.platform === 'hand') {
			platformMap = handOrderMatchPlatformMap.filter(it => !['sph', 'dw'].includes(it.key));
		}
		// 快团团模板判断，不能选视屏号、有赞、得物模板
		if (o.platform === PLAT_KTT) {
			platformMap = platformMap.filter(it => !['sph', 'yz', 'dw'].includes(it.key));
		}
		let options = platformMap.map(i => (
			<Option key={ i.key } disabled={ i.disabled } value={ i.id }>{i.name}</Option>
		));
		return options;
	};
	return (
		<div className="itemBox">
			<div className="leftTittle" style={ { marginTop: '7px' } }>打印设置:</div>

			<div className="rightChoose">
				<div className="print_item">
					<div className="item_check">
						<div className="merge_ratio_box">
							<div className="merge_ratio_title" style={ { width: '160px' } }>1688回流明文订单设置：</div>
							<div className="merge_print_check">
								<Radio.Group
									className="r-flex merge_radio"
									value={ setting?.refluxOrderPrintSet }
									onChange={ (e) => handleInputChange(e, 'refluxOrderPrintSet') }
								>
									<div>
										<Radio value={ refluxOrderPrintSettingsEnum.使用菜鸟面单打印 } >
											<span style={ { color: 'rgba(0,0,0,0.65)' } }>使用菜鸟面单打印</span>
										</Radio>
									</div>
									<div style={ { marginTop: '8px' } } >
										<Radio value={ refluxOrderPrintSettingsEnum.使用对应平台面单打印 } >
											<span style={ { color: 'rgba(0,0,0,0.65)' } }>使用对应平台面单打印</span>
										</Radio>
										<Tooltip title="由于官方限制，视频号平台仅支持菜鸟面单打印">
											<QuestionCircleFilled style={ { fontSize: '14px', color: '#FD8204', marginLeft: "4px" } } />
										</Tooltip>

									</div>
								</Radio.Group>
							</div>
						</div>
					</div>
					<div className="item_check">
						<div>
							<span style={ { fontSize: '14px', color: '#000000d9' } }>聚合打单设置</span>
							<span style={ { color: 'gray', fontSize: '12px', margin: '0 9px' } }>智能匹配，一键打印全平台订单</span>
							<span className="downContorl" onClick={ () => setShowControlsDownModal(true) }>下载打印控件</span>
						</div>


						<div>
							<div className="merge_ratio_box">
								<div className="merge_ratio_title">设置订单打印顺序：</div>
								<div className="merge_print_check">
									<Radio.Group
										className="r-flex merge_radio"
										value={ setting?.groupPrintSetJsonString?.printOrderByOrder }
										onChange={ (e) => handleInputChange(e, 'groupPrintSetJsonString.printOrderByOrder') }
									>
										<div>
											<Radio value={ 1 } >
												<span style={ { color: 'rgba(0,0,0,0.65)' } }>按勾选订单顺序打印</span>
											</Radio>
										</div>
										<div style={ { marginTop: '8px' } } >
											<Radio value={ 2 } >
												<span style={ { color: 'rgba(0,0,0,0.65)' } }>按控件先后顺序打印</span>
											</Radio>
											<Icon
												type="shezhi"
												style={ { fontSize: `16px`, cursor: 'pointer', color: '#999' } }
												onClick={ () => handleDragBtn(true) }
											/>
										</div>
									</Radio.Group>


								</div>
							</div>
							<div className="merge_ratio_box">
								<div className="merge_ratio_title">订单匹配规则：</div>
								<div className="merge_print_check" style={ { fontSize: '14px' } }>
									<div style={ { marginTop: '8px' } } >
										<span>平台订单将智能匹配模板打印</span>
										<Tooltip title="淘宝、阿里、京东、抖音、快手平台订单将智能匹配合适的电子面单模板打印。">
											<QuestionCircleFilled style={ { fontSize: '14px', color: '#FD8204', marginLeft: "4px" } } />
										</Tooltip>
									</div>
									{
										setting?.groupPrintSetJsonString?.orderMatchSetting && setting?.groupPrintSetJsonString?.orderMatchSetting.map((o, i) => {
											return (
												<div style={ { marginTop: '8px' } }>
													<span style={ { color: 'rgba(0,0,0,0.65)' } }>{PLAT_MAP[o.platform]}{o.platform === 'dw' ? '现货' : ''}订单匹配平台</span>
													<Select
														getPopupContainer={ (e) => e.parentElement }
														className="r-ml-8"
														value={ o.bindControlType }
														onChange={ e => handleInputChange({ e, i }, 'groupPrintSetJsonString.orderMatchSetting') }
													>
														{
															selectOptions(o)
														}
													</Select>
													{PLAT_MAP[o.platform] === '手工单' && (
														<Tooltip title="注：抖音明文手工单因平台限制，默认按其他平台手工单设置平台进行模板分配">
															<QuestionCircleFilled style={ { fontSize: '14px', color: '#FD8204', marginLeft: "4px" } } />
														</Tooltip>
													)}
												</div>
											);
										})
									}
								</div>
							</div>
						</div>
					</div>
					<div className="item_check">
						<Checkbox
							data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示单号生成个数 }
							className="r-mr-5"
							checked={ setting.displayGenYdNum === 2 }
							onChange={ (e) => handleInputChange(e, 'displayGenYdNum') }
						>
							显示单号生成个数
							<Tooltip title="顺丰、京东、申通新版小红书模板不支持同一次申请多个单号">
								<QuestionCircleFilled style={ { fontSize: '14px', color: '#FD8204', marginLeft: "4px" } } />
							</Tooltip>
						</Checkbox>
					</div>
					<div className="item_check" style={ { display: 'flex' } }>
						<Checkbox
							// data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示单号生成个数 }
							className="r-mr-5"
							checked={ setting?.groupPrintSetJsonString?.openOddNum === 2 }
							onChange={ (e) => handleInputChange(e, 'groupPrintSetJsonString.openOddNum') }
						>
							剩余单量提醒
						</Checkbox>
						{setting?.groupPrintSetJsonString?.openOddNum === 2
								&& (
									<div className="numSetBox">
										<span>单量小于</span>
										<InputNumber
											min={ 1 }
											max={ 1000 }
											controls={ false }
											precision={ 0 }
											size="small"
											value={ setting?.groupPrintSetJsonString?.numTipVal }
											onChange={ (e) => handleInputChange(e, 'groupPrintSetJsonString.numTipVal') }
											style={ { width: '50px', height: '26px', margin: '0 10px' } }
										/>
										时红字展示
									</div>
								)}
					</div>
					<div className="item_check">
						<Checkbox
							data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示打印机打印份数 }
							className="r-mr-5"
							checked={ setting.displayPrintNum === 2 }
							onChange={ (e) => handleInputChange(e, 'displayPrintNum') }
						>
							显示打印机打印份数
						</Checkbox>
					</div>
					{
						userStore.isStockVersion ? (
							<div className="item_check">
								<Checkbox
									// data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示打印机打印份数 }
									className="r-mr-5"
									checked={ setting.showCombinationInfo === 2 }
									onChange={ (e) => handleInputChange(e, 'showCombinationInfo') }
								>
									打印组合货品显示子货品
								</Checkbox>
							</div>
						) : ''
					}
					{
						userStore.isShowAutoDecode ? (
							<div className="item_check">
								<Checkbox
									// data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示打印机打印份数 }
									className="r-mr-5"
									checked={ setting.printSetExpandDTO?.branchDzmdAutoDecode === 1 }
									onChange={ (e) => handleInputChange(e, 'branchDzmdAutoDecode') }
								>
									使用网点面单打印时自动解密订单
									<Tooltip title="说明：选择网点电子面单打印时，系统将对订单收件人信息进行解密，平台解密额度不足时订单解密失败，解密失败将终止打印流程。">
										<QuestionCircleFilled style={ { fontSize: '14px', color: '#FD8204', marginLeft: "4px" } } />
									</Tooltip>
								</Checkbox>
							</div>
						) : ''
					}
					<div className="item_check">
						<Checkbox
							// data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示打印机打印份数 }
							className="r-mr-5"
							checked={ setting.applyAndPrint === 1 }
							onChange={ (e) => handleInputChange(e, 'applyAndPrint') }
							disabled={ setting.showGoodsTag === 2 || setting.multiPrinterConfig === 1 }
						>
							申请单号自动打印模式
							<Tooltip title="申请单号后直接自动打印，快速打单发货">
								<QuestionCircleFilled style={ { fontSize: '14px', color: '#FD8204', marginLeft: "4px" } } />
							</Tooltip>
						</Checkbox>
					</div>
					<div className="item_check">
						<Checkbox
							data-point={ Pointer.订单_订单打印_高级设置_打印设置_打印快递单同时打印商品吊牌 }
							className="r-mr-5"
							checked={ setting.showGoodsTag === 2 }
							onChange={ (e) => handleInputChange(e, 'showGoodsTag') }
							disabled={ setting.applyAndPrint === 1 }
						>
							打印快递单同时打印商品吊牌
						</Checkbox>
						<Tooltip
							title={ (
								<div>
									<p>打印吊牌时需要安装Lodop控件，<a href="https://www.lodop.net/download/CLodop_Setup_for_Win64NT_6.609EN.zip" target="_blank" rel="noreferrer">点击下载</a>；</p>
									<p>组合货品默认打印子货品的吊牌；</p>
									<p>打印的吊牌的打印机需要单独设置；</p>
									<p>打印快递单同时打印吊牌会影响快递单打印速度；</p>
								</div>
							) }
						>
							<QuestionCircleFilled style={ { color: "#FD8204", fontSize: "14px" } } />
						</Tooltip>
						<Button type="link" onClick={ () => { history.push('/settings/system?introName=printTagSet'); } }>吊牌设置</Button>
					</div>
					<div className="item_check">
						<Checkbox
							// data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示打印机打印份数 }
							className="r-mr-5"
							checked={ setting.multiPrinterConfig === 1 }
							onChange={ (e) => handleInputChange(e, 'multiPrinterConfig') }
							disabled={ setting.applyAndPrint === 1 }
						>
							多台打印机同时打印
							<Tooltip title="暂时不支持与「申请单号自动打印模式」、「聚合打印模式」同时开启">
								<QuestionCircleFilled style={ { fontSize: '14px', color: '#FD8204', marginLeft: "4px" } } />
							</Tooltip>
						</Checkbox>
					</div>
					<div className="item_check">
						<Checkbox
							// data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示打印机打印份数 }
							className="r-mr-5"
							checked={ setting?.groupPrintSetJsonString?.useLocalCatchPrint === 2 }
							onChange={ (e) => handleInputChange(e, 'groupPrintSetJsonString.useLocalCatchPrint') }
						>
							使用上次选择打印机
							<Tooltip title="未勾选使用模板绑定打印机">
								<QuestionCircleFilled style={ { fontSize: '14px', color: '#FD8204', marginLeft: "4px" } } />
							</Tooltip>
						</Checkbox>
					</div>
				</div>

				<div className="item" id="addYdNoMemo" style={ local.get('splitOrderAutoRemark') ? { border: '1px dashed #FD8204', padding: '0 4px' } : {} }>
					<Checkbox
						className="r-mr-5"
						data-point={ Pointer.订单_订单打印_高级设置_打印设置_打印后自动追加快递单号到备注 }
						checked={ setting.addYdNoMemo === 2 }
						onChange={ (e) => handleInputChange(e, 'addYdNoMemo') }
					>
						申请单号/打单后自动追加快递单号到卖家备注
					</Checkbox>
					<Tooltip
						title={ (
							<div>
								<p>因1688平台限制修改备注需要和旗帜一起上传，开启此功能，无旗帜订单默认置红旗</p>
							</div>
						) }
					>
						<QuestionCircleFilled style={ { color: "#FD8204", fontSize: "14px" } } />
					</Tooltip>
				</div>
				<div className="item r-ml-10">
					<div className="r-flex">
						<Checkbox
							// data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示打印机打印份数 }
							className="r-mr-5"
							disabled={ setting.addYdNoMemo !== 2 }
							checked={ setting.addYdNoMemo === 2 && setting.addYdNoOverOne === 2 }
							onChange={ (e) => handleInputChange(e, 'addYdNoOverOne') }
						>
							申请/打印一个快递单号时不追加
						</Checkbox>
					</div>
				</div>
				<div className="item r-ml-10">
					<div className="r-flex">
						<Checkbox
							// data-point={ Pointer.订单_订单打印_高级设置_打印设置_显示打印机打印份数 }
							className="r-mr-5"
							disabled={ setting.addYdNoMemo !== 2 }
							checked={ setting.addYdNoMemo === 2 && setting?.printSetExpandDTO?.addYdNoShipped === 2 }
							onChange={ (e) => handleInputChange(e, 'addYdNoShipped') }
						>
							发货前申请/打印快递单号时不追加
						</Checkbox>
					</div>
				</div>
			</div>
		</div>
	);
};

export default PrintSetting;
