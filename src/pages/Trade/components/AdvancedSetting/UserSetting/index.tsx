import React, { useState } from 'react';
import { observer } from 'mobx-react';
import { Checkbox, Popover, Tooltip } from 'antd';
import { EyeOutlined, QuestionCircleFilled } from '@ant-design/icons';
import userStore from '@/stores/user';
import Pointer from '@/utils/pointTrack/constants';
import { tradeStore } from '@/stores';
import showTransparentBg from '@/assets/image/user/img_图示@2x.png';
import LowVersionControlModal from '@/components-biz/LowVersionControlModal';
import { PageNameControlEnum } from '@/components-biz/LowVersionControlModal/constant';

const UserSetting = (props: any) => {
	const [visible, setVisible] = useState(false);
	let { setting, handleInputChange } = props;
	const { setOptBtnCustomVisible } = tradeStore;
	const {
		userInfo: {
			level,
			whiteListSetting,
		},
	} = userStore;
	const dyNickName = JSON.parse(whiteListSetting || '{}')?.dyNickName;

	const getPopoverImage = () => {
		return (
			<div style={ { background: '#fff' } }>
				<img src={ showTransparentBg } alt="" style={ { width: 284 } } />
			</div>
		);
	};

	const getDyNickNamePopoverContent = () => {
		return (
			<div style={ { background: '#fff' } }>
				<p>1.订单列表点击<EyeOutlined />可查看买家真实昵称，查看昵称会消耗店铺解密额度。</p>
				<p>2.获取昵称后，可打印昵称在快递单上。</p>
				<p>3.开启后直播标签打印时将自动解密昵称信息。</p>
			</div>
		);
	};
	const getTitle = () => {
		return (
			<div>
				<span>抖音买家昵称</span>
				{level != 2 ? <span className="r-pointer" style={ { color: "#FD8204" } }>[高配版功能]</span> : null}
			</div>
		);
	};
	return (
		<div className="itemBox">
			{
				visible
					? (
						<LowVersionControlModal
							closable
							centered
							pageName={ PageNameControlEnum.抖音昵称打印 }
							onCancel={ () => {
								setVisible(false);
							} }
						/>
					)
					: null
			}
			<div className="leftTittle">个性设置:</div>
			<div className="rightChoose">
				<div className="item">
					<div className="r-mr-30 r-flex">
						<Checkbox
							className="r-mr-5"
							disabled={ dyNickName != 1 && level == 2 }
							checked={ dyNickName == 1 ? setting?.printSetExpandDTO?.showDyNickName : false }
							onChange={ (e) => handleInputChange(e, 'showDyNickName') }
							onClick={ () => {
								if (level != 2 && dyNickName != 1) {
									setVisible(true);
								}
							} }
						>
							开启抖音买家昵称
						</Checkbox>
						<Popover 
							title={ getTitle() }
							placement="top" 
							arrowPointAtCenter 
							content={ getDyNickNamePopoverContent() } 
							trigger="hover"
							// zIndex={ 1000 }
							autoAdjustOverflow
						>
							{level != 2 ? <span className="r-pointer" style={ { color: "#FD8204", marginRight: '4px' } }>[高配版功能]</span> : null}
							<QuestionCircleFilled style={ { fontSize: '14px', color: "#FD8204" } } />
						</Popover>
					</div>
				</div>
				<div className="item">
					<div className="r-mr-30 r-flex">
						<Checkbox
							data-point={ Pointer.订单_订单打印_高级设置_个性化设置_开启宝贝筛选 }
							className="r-mr-5"
							checked={ setting.filterOrder === 2 }
							onChange={ (e) => handleInputChange(e, 'filterOrder') }
						>
							开启宝贝筛选
						</Checkbox>
					</div>
				</div>
				<div className="item">
					<div className="r-mr-30 r-flex">
						<Checkbox
							data-point={ Pointer.订单_订单打印_高级设置_个性化设置_开启订单多包裹发货 }
							className="r-mr-5"
							checked={ setting?.printSetExpandDTO?.multiplePackageDelivery === true }
							onChange={ (e) => handleInputChange(e, 'multiplePackageDelivery') }
						>
							开启订单多包裹发货 <Tooltip title="开启后，发货时可支持上传多个单号到平台" ><QuestionCircleFilled className="r-ml-5 r-c-999" style={ { fontSize: '14px', color: "#FD8204" } } /></Tooltip>
						</Checkbox>
					</div>
				</div>
				<div className="item">
					<div className="r-mr-30 r-flex">
						<Checkbox
							// data-point={ Pointer.订单_订单打印_高级设置_个性化设置_开启宝贝筛选 }
							className=""
							checked={ setting.customPrintSetStatus === 2 }
							onChange={ (e) => handleInputChange(e, 'customPrintSetStatus') }
						>
							更多操作按钮自定规则
						</Checkbox>
						<span
							className="r-pointer"
							style={ { color: "#1890ff" } }
							onClick={ () => setOptBtnCustomVisible(true) }
						>设置
						</span>
						<span>（关闭后按默认排序显示）</span>
					</div>
				</div>
				<div className="item">
					<div className="r-mr-30 r-flex">
						<Checkbox
							className=""
							checked={ setting?.printSetExpandDTO?.showTransparent == 1 }
							onChange={ (e) => handleInputChange(e, 'showTransparent') }
						>
							底部操作区域透明展示
						</Checkbox>
						<Popover 
							placement="bottom" 
							arrowPointAtCenter 
							content={ getPopoverImage() } 
							trigger="hover"
							// zIndex={ 1000 }
							autoAdjustOverflow
							// overlayInnerStyle={ { width: 300 } }
						>
							<span className="r-pointer">开启后图示</span>
						</Popover>
					</div>
				</div>
				<div className="item">
					<div className="r-mr-30 r-flex">
						<Checkbox
							className=""
							checked={ setting?.printSetExpandDTO?.logisticsRecommended == 1 }
							onChange={ (e) => handleInputChange(e, 'logisticsRecommended') }
						>
							开启抖音物流探查
						</Checkbox>
						<span>注意：开启后抖音订单可达判断根据平台接口判断</span>
					</div>
				</div>
			</div>
		</div>

	);
};

export default observer(UserSetting);
