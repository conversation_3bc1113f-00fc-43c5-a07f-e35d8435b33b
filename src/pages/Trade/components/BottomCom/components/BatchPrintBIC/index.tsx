import React, { useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, Modal, Form, Select } from "antd";
import { InfoCircleOutlined } from '@ant-design/icons';
import { useRequest, useReactive } from "ahooks";
import cs from "classnames";
import _, { compact, uniq } from "lodash";
import userStore from '@/stores/user';
import { tradeStore } from '@/stores';
import scanPrintStore from "@/stores/trade/scanPrint";
import { local, session } from "@/libs/db";
import { Base } from "@/types/schemas";
import PrintCenter from '@/print/index';
import message from "@/components/message";
import { SendType, TradeStatus, TradeOptEnum } from "@/utils/enum/trade";
import { PLAT_FXG } from "@/constants";
import useGetState from "@/utils/hooks/useGetState";
import { TradeBatchGetBicOrderCodeApi, TradeOperateLogAddApi } from "@/apis/trade";
import { copyToPaste } from "@/utils";
import { filterPrintContent } from "@/utils/trade/printContent";
import s from './index.module.scss';

interface IProps extends Base{

}

const { Option } = Select;

// 打印BIC订单码
const BatchPrintBIC = ((props: IProps) => {
	const { isMergePrint, selectedTemp, selectedTempGroup, lodopInstallStatus } = tradeStore;
	const { list, handleUpdateList, setIsBatchPrintBIC, setBicOrderCodeToList } = tradeStore.tradeListStore;
	const {
		printersList,
		getScanPrintSetting
	} = scanPrintStore;
	const [form] = Form.useForm();
    
	const [orderRecord, setOrderRecord, getOrderRecord] = useGetState<IProps[]>([]);
	const [printerList, setPrinterList] = useState([]); // 打印机列表
	const [printLabelShow, setPrintLabelShow] = useState(false);
	const [isPrintEnd, setIsPrintEnd] = useState(false);

	// 下载进度
	const modalProgress = useReactive({
		total: 0, // 总的请求
		completed: 0, // 已完成请求
		progress: 0,
		progressVisibility: false,
		progressResultVisibility: false, // 失败列表
		data: {} as any,
	});

	useEffect(() => {
		if (printersList.length) {
			setPrinterList(printersList);
		}
		let BICPrinterAndTemp = local.get('BICPrinterAndTemp');
		if (printersList?.length && !form.getFieldValue('printer')) {
			let printer = printersList[0];
			let selectPrinter = BICPrinterAndTemp && BICPrinterAndTemp.printer;
			if (selectPrinter && printersList.includes(selectPrinter)) {
				printer = selectPrinter;
			}
			form.setFieldsValue({
				printer,
			});
		}
	}, [printersList]);

	const remindModal = () => {
		Modal.warning({
			centered: true,
			title: '提醒',
			width: 540,
			content: (
				<div className="r-flex r-fd-c">
					<div>所选订单中部分订单不支持打印订单码，请不要选择以下订单：</div>
					<div>1、非“待发货”订单</div>
					<div>2、未勾选所有待发货的商品的订单</div>
					<div>3、挂起订单</div>
					<div>4、非“BIC质检订单”</div>
					<div className="r-c-error">注：仅支持抖店的BIC订单处理（一单一包裹模式）；</div>
				</div>
			),
			okText: '确定',
			closable: true,
			maskClosable: false,
			cancelButtonProps: { hidden: true }
		});
	};

	// 记录订单操作日志
	const reportLog = async(selectList) => {
		let params = selectList.map(item => ({
			operateType: TradeOptEnum.打印订单码,
			operateContent: `订单码: ${item.orderCode}`,
			operateResult: 1,
			tids: item.togetherId || item?.tids?.join(','),
			platform: item.platform,
			operateClassify: "other"
		}));
		let res = await TradeOperateLogAddApi(params);
		console.log('记录打印订单码日志', res);
	};

	const checkFn = async() => {
		// 检测c-lodop test111
		let installResult = await PrintCenter.checkLodopProgress(false);
		console.log('%c [ clodop ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', installResult);

		if (!installResult?.isSuccess) {
			setIsBatchPrintBIC(false);
			return;
		}

		let packList: IProps[] = _.cloneDeep(list?.filter(item => item.isChecked) || []);
		if (!packList.length) {
			message.warning('请先选择订单');
			setIsBatchPrintBIC(false);
			return;
		}

		// 校验勾选数据是否符合BIC打印订单码要求
		// 若勾选订单存在以下情况，则不可操作，点击确定关闭弹窗
		let isBreak = false;
		let errorText = "";

		for (let i = 0; i < packList.length; i++) {
			const pack = packList[i];
			let _checkedOrder = [];

			// 挂起订单
			if (pack.isPending) {
				isBreak = true;
				errorText = '挂起订单';
				break;
			}

			// 非抖店订单
			if (![PLAT_FXG].includes(pack.platform)) {
				isBreak = true;
				errorText = '非抖店订单';
				break;
			}

			// 非“待发货”订单
			if (!pack.trades.some(trade => [TradeStatus.等待卖家发货].includes(trade.status as TradeStatus))) {
				isBreak = true;
				errorText = '非“待发货”订单';
				break;
			}
			// 非BIC质检订单 service_tag_bic_order
			if (!pack?.serviceTagList?.includes('bic_order')) {
				isBreak = true;
				errorText = '非“BIC质检订单”';
				break;
			}

			// 未勾选所有待发货的商品的订单
			for (let j = 0; j < pack.trades.length; j++) {
				const trade = pack.trades[j];
				let checkedOrders = trade.orders.filter(order => order.isChecked);
					
				// 必须是待发货商品
				isBreak = !checkedOrders.every(order => [TradeStatus.等待卖家发货].includes(order.status));
				if (isBreak) {
					errorText = '有非“待发货”商品被勾选';
					break;
				}

				// 必须勾选所有待发货商品
				isBreak = checkedOrders.length !== trade.orders.filter(order => [TradeStatus.等待卖家发货].includes(order.status))?.length;
				if (isBreak) {
					errorText = '有“待发货”商品未被勾选';
					break;
				}

				_checkedOrder.push(...checkedOrders);
			}

			if (!_checkedOrder.length) {
				// errorText = '有不满足条件订单';
				isBreak = true;
				break;
			}
			if (isBreak) break;
		}

		if (isBreak) {
			console.log('%c [ errorText ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', errorText);
			remindModal();
			setIsBatchPrintBIC(false);
		} else {
			// 所有勾选数据都符合条件，开始下载订单码
			let _orderList: IProps[] = [];
			packList.forEach(pack => {
				// 找出有子订单勾选的订单
				pack.trades = pack.trades.filter(trade => trade.orders.filter(order => order.isChecked).length);

				// 理论上这里没有合单
				if (pack.isMerge) {
					// * 把合单打平
					pack.trades.forEach((trade, tradeIndex) => {
						_orderList.push({
							...pack,
							...trade,
							"trades": [trade],
							sellerId: pack.sellerId,
							sellerNick: pack.sellerNick,
						});
					});
				} else {
					_orderList.push({
						...pack,
						...pack.trades[0],
						sellerId: pack.sellerId,
						sellerNick: pack.sellerNick,
					});
				}
			});

			console.log('%c [ 打印数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', _orderList);
			setOrderRecord(_orderList); // 保存一份备用
		
			downloadOrderCode(_orderList);
		}

	};

	// 下载bic订单码
	const downloadOrderCode = async(orderList) => {
		const batchSize = 5; // 每批请求的大小
		const total = orderList.length; // 总数据量
		let completed = orderList?.filter(trade => !!trade?.orderCode)?.length || 0; // 当前完成的数据量
		const results = []; // 用于收集所有请求结果
		const dataArray = orderList?.filter(trade => !trade?.orderCode)?.map(item => {
			return {
				tid: item.tid,
				sellerId: item.sellerId,
				platform: item.platform,
			};
		}) || []; // 只请求没有code的订单
	
		// 如果全部有订单码,直接显示打印确认弹框
		if (total == completed) {
			console.log('%c [ 全部有订单码 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '直接打印');
			prePrint(orderList);
			return;
		}

		modalProgress.progressVisibility = true; 
		modalProgress.total = total;
	
		const processBatch = async(batch) => {
			console.log('%c [ batch ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', batch);
			try {
				const res = await TradeBatchGetBicOrderCodeApi(batch);
				results.push({ 
					success: true, 
					data: {
						orderCodeVOList: res?.orderCodeVOList || [],
						failReasonVOList: res?.failReasonVOList || [],
					} 
				});
			} catch (error) {
				results.push({ 
					success: false, 
					error, 
					data: {
						failReasonVOList: batch?.map(item => {
							return {
								tid: item?.tid,
								errorMsg: '网络请求失败'
							}; 
						}) || []
					} 
				});
			} finally {
				completed += batch.length; 
				const percentage = Math.floor((completed / total) * 100);
				modalProgress.progress = percentage;
				modalProgress.completed = completed;
				console.log(`已完成 ${completed} / ${total} (${percentage}%)`); 
			}
		};

		const maxConcurrentRequests = 4; // 最大并发请求数
		const queue = []; // 请求队列

		// 将所有批次添加到队列中
		for (let i = 0; i < total; i += batchSize) {
			const batch = dataArray.slice(i, i + batchSize); // 获取当前批次数据
			if (batch.length > 0) {
				queue.push(batch);
			}
		}

		// 创建并发请求的处理函数
		const worker = async() => {
			while (queue.length > 0) {
				const batch = queue.shift(); // 从队列中取出一个批次
				// eslint-disable-next-line no-await-in-loop
				await processBatch(batch); // 处理该批次
			}
		};

		// 启动多个工作线程
		const workers = Array.from({ length: maxConcurrentRequests }, worker);
		await Promise.all(workers); // 等待所有工作线程完成
        
	
		completed = total;
		modalProgress.progress = 100;

		// 所有请求完成后的回调
		console.log('所有请求已完成', results);
		setTimeout(() => {
			// 隐藏进度，显示结果
			modalProgress.progressVisibility = false; 
			modalProgress.progressResultVisibility = true;
	
			handleResult(results, orderList);
		}, 200);
		
	};

	const handleResult = (results, orderList) => {
		let successList = [];
		let errorList = [];

		results?.map(item => item.data?.orderCodeVOList || [])?.flat(2)?.forEach(item => {
			successList.push(item);
		});

		results?.map(item => item.data?.failReasonVOList || [])?.flat(2)?.forEach(item => {
			errorList.push(item);
		});

		modalProgress.data = {
			successNum: successList?.length || 0,
			failedNum: errorList?.length || 0,
			errorResultCollectList: errorList,
			successResultCollectList: successList,
		};

		// 把成功的订单码更新到页面
		let updateList = successList.map(item => {
			let trade = orderList?.find(d => d.tid == item.tid);
			return {
				...item,
				togetherId: trade?.togetherId
			};
		});
		setBicOrderCodeToList(updateList);

		// 如果没有失败的请求
		if (!errorList?.length && successList?.length) {
			// 把所有成功的订单码加入到数据里面
			let successMap = new Map();
			successList.forEach(item => {
				if (item.orderCode) {
					successMap.set(item.tid, item.orderCode);
				}
			});
			orderList?.forEach(item => {
				if (!item?.orderCode && successMap.has(item.tid)) {
					item.orderCode = successMap.get(item.tid);

					item?.trades?.forEach(trade => {
						trade.orderCode = successMap.get(item.tid);
					});
				}
			});
			successMap.clear();
			prePrint(orderList);
		}
	};

	// 打印确认弹窗
	const prePrint = (orderList) => {
		setOrderRecord(orderList); // 更新一份
		setPrintLabelShow(true); 
	};

	const resetModalProgress = () => {
		modalProgress.total = 0;
		modalProgress.completed = 0;
		modalProgress.progressVisibility = false;
		modalProgress.progress = 0;
		modalProgress.progressResultVisibility = false;
		modalProgress.data = {};
	};

	useEffect(() => {
		checkFn();

		return () => {
			resetModalProgress();
			setOrderRecord([]);
		};
	}, []);

	const progressResultFooter = (
		<div>
			<Button
				type="default"
				onClick={ () => {
					copyToPaste(modalProgress.data?.errorResultCollectList?.map(item => item.tid)?.join(','));
					setIsBatchPrintBIC(false);
				} }
			>复制所有失败的系统单号
			</Button>
			<Button
				type="primary"
				onClick={ () => {
					setIsBatchPrintBIC(false);
				} }
			>我知道了
			</Button>
		</div>
	);

	const handleClosePrintLabelShow = () => {
		setPrintLabelShow(false); 
		setIsBatchPrintBIC(false);
	};

	// 预览
	const review = async() => {
		const res = await form.validateFields();
		const { printer } = res;
		handlePrintAct({ printer, review: true });
		// handleClosePrintLabelShow(); // 预览不关闭弹框
	};

	// 打印
	const handlePrintLabelFinish = (val) => {
		const { printer } = val;
		local.set('BICPrinterAndTemp', { printer });
		handlePrintAct({ printer, review: false });
		handleClosePrintLabelShow();
	};

	// 打印前检查
	const handlePrintAct = async({ printer, review }:{printer?: string, review?: boolean}) => {
		let selectList:any[] = [];
		orderRecord?.forEach(item => {
			const { supplierName, sysSkuId, skuId, tid, ptTid, skuName } = item;
		
			// 过滤词处理
			// item.skuName = filterPrintContent(skuName);

			// 再次确认
			if (item.isChecked && item.orderCode) {
				selectList.push({
					...item,
					orderCode: item.orderCode,
				});
			}

		});

		if (!printer) {
			message.error('请选择打印机');
			return;
		}
		if (!selectList?.length) {
			message.error('没有要打印的订单');
			return;
		}
		
		batchPrintBICOrderCode({ selectList, printer, review });
	};

	const batchPrintBICOrderCode = (
		{ 
			selectList, 
			printer, 
			review = false 
		}:{
        selectList:any[],
        printer?: string,
        review:boolean}
	) => {
		let params = {
			orderList: selectList,
			review,
			printer,
		};
		PrintCenter.batchPrintBicOrderCode(params).finally(() => {
			setIsPrintEnd(true);
			reportLog(selectList);
		});
	};

	// useEffect(() => {
	// 	PrintCenter.getPrinterList();
	// }, []);
    
	return (
		<>
			{
				modalProgress.progressVisibility && (
					<Modal
						title={ (
							<div className="r-flex r-ai-c">
								<InfoCircleOutlined style={ { fontSize: 16, color: '#f90' } } />提示
							</div>
						) }
						centered
						visible
						destroyOnClose
						closable={ false }
						maskClosable={ false }
						keyboard={ false }
						footer={ null }
						width={ 480 }
						zIndex={ 1051 }
					>
						<div className="r-flex r-jc-c">
							下载订单码中，请勿操作…{modalProgress.completed}/{modalProgress.total}
						</div>
					</Modal>
				)
			}

			{
				modalProgress.progressResultVisibility && modalProgress.data?.errorResultCollectList?.length > 0 && (
					<Modal
						title={ (
							<div className="r-flex r-ai-c">
								<InfoCircleOutlined style={ { fontSize: 22, color: '#f90' } } />
								<span style={ { fontSize: 16, color: '#f90', fontWeight: 'bold', marginLeft: 4 } }>以下订单下载订单码失败</span>
							</div>
						) }
						centered
						visible
						destroyOnClose
						closable={ false }
						maskClosable={ false }
						keyboard={ false }
						width={ 480 }
						footer={ progressResultFooter }
						zIndex={ 1051 }
						bodyStyle={ { overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' } }
					>
						<div className="r-flex r-fd-c ">
							{
								modalProgress.data?.errorResultCollectList?.map(item => (
									<div className={ s.errorItem } key={ item.tid }>
										<div className={ s.errorTitle }>
											【订单编号：{item.tid}】下载订单码 <span className="r-c-error">失败</span> ！
										</div>
										<div className={ s.errorMsg }>
											{item.errorMsg}
										</div>
									</div>
								))
							}
						</div>
					</Modal>
				)
			}

			{
				printLabelShow && (
					<Modal
						centered
						visible
						footer={ null }
						width={ 460 }
						onCancel={ () => handleClosePrintLabelShow() }
						closable={ false }
						maskClosable={ false }
						keyboard={ false }
						destroyOnClose
						zIndex={ 1051 }
					>
						<Form
							form={ form }
							onFinish={ handlePrintLabelFinish }
							{ ...{
								labelCol: { span: 6 },
								wrapperCol: { span: 18 },
							} }
						>
							<Form.Item label="打印行数" className="r-mb-12 r-flex r-ai-c">
								<span>共 {orderRecord?.length} 行</span>
							</Form.Item>

							<Form.Item label="订单码模版" className="r-mb-12 r-flex r-ai-c">
								<span>订单码模版80*40mm</span>
							</Form.Item>
							
							<Form.Item label="选择打印机" name="printer">
								<Select style={ { width: 180 } }>
									{printerList.map(printer => (
										<Option key={ printer } value={ printer }>{printer}</Option>
									))}
								</Select>
							</Form.Item>
							
							<div className="r-flex r-ai-c r-jc-c">
								<Button onClick={ () => handleClosePrintLabelShow() }>取 消</Button>
								<Button className="r-ml-10 r-mr-2" type="primary" htmlType="submit">打印订单码</Button>
								<Button type="link" onClick={ review }>预览</Button>
							</div>
						</Form>

					</Modal>
				)
			}
			
		</>
	);
});

export default observer(BatchPrintBIC);