import React, { <PERSON>actN<PERSON>, useEffect, useMemo, useState } from "react";
import cs from "classnames";
import { observer } from "mobx-react";
import { Button, Input, Modal, Table } from "antd";
import { ColumnsType } from "antd/lib/table";
import _, { uniq } from "lodash";
import message from "@/components/message";
import { tradeStore } from "@/stores";
import { checkOrdersWithTemp } from "../../opt";
import { IPackage } from "@/pages/Trade/interface";
import { PLAT_FXG, PLAT_PDD, PLAT_TB, PLAT_TM, PLAT_KS, PLAT_JD, PLAT_SPH, PLAT_XHS, PLAT_C2M, PLAT_YZ } from "@/constants";
import './index.scss';
import { SendType, TradeStatus } from "@/utils/enum/trade";
import { gatherShipInfo } from "../../utils";
import { ISendSubTrades, TradeBatchResendRequest, TradeBatchResendResponse } from "@/types/trade/index";
import { pageLoading } from "@/components/PageLoading";
import { TradeBatchResendApi } from "@/apis/trade";
import useGetState from "@/utils/hooks/useGetState";
import { copyToPaste } from "@/utils";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";

export interface IBatchResendProps {}

interface IResendItem extends IPackage {
	canResend?: boolean;
	resendTips?: string | ReactNode;
	showMerge?: boolean;
	mergeCount?: number;
}

const BatchResend = (props: IBatchResendProps) => {
	const { isMergePrint, selectedTemp, selectedTempGroup } = tradeStore;
	const { list, setIsBatchResend } = tradeStore.tradeListStore;
	const [orderRecord, setOrderRecord, getOrderRecord] = useGetState<IResendItem[]>([]);
	const templateName = useMemo(() => {
		return isMergePrint ? selectedTempGroup.groupName : selectedTemp.ExcodeName;
	}, [isMergePrint, selectedTemp.ExcodeName, selectedTempGroup.groupName]);
	const [errorTidsList, setErrorTidsList] = useState<String[]>([]);
	const [isStartResend, setIsStartResend] = useState(false);
	const [isResendOver, setIsResendOver] = useState(false);

	useEffect(() => {
		checkFn();
	}, []);

	const remindModal = () => {
		Modal.warning({
			centered: true,
			title: '提醒',
			width: 450,
			content: (
				<div className="r-flex r-fd-c">
					<div>所选订单中部分订单不支持修改单号重新发货，请不要选择以下订单：</div>
					<div>1、未发货订单</div>
					<div>2、挂起订单</div>
					<div>3、未勾选所有商品的订单</div>
					<div>4、多包裹发货的订单</div>
					<div>5、1688、快团团、得物平台的订单</div>
					<div className="r-c-error">注：仅支持淘宝、拼多多、抖店、快手、京东、视频号、小红书、淘工厂、有赞订单修改单号重新发货，一次最多勾选50条。</div>
				</div>
			),
			okText: '确定'
		});
	};

	const checkFn = async() => {
		let tempLate = isMergePrint ? selectedTempGroup : selectedTemp;
		let isChooseTemplate = isMergePrint ? !!tempLate.userTemplateList : (!!tempLate.KddType || tempLate.Exid == -901);
		
		// 判断是否为手动导入单号并重新发货
		// 聚合打印模式下判断
		const isMergeManualImport = isMergePrint && tempLate.id == -901;
		// 非聚合打印模式下判断
		const isNormalManualImport = !isMergePrint && tempLate.Exid == -901;
		// 综合判断
		let isManualImportResend = isMergeManualImport || isNormalManualImport;
		if (!isChooseTemplate) {
			message.error('请先选择快递模板');
			setIsBatchResend(false);
			return;
		}

		try {
			let orderList: IResendItem[] = _.cloneDeep(list?.filter(item => item.isChecked) || []);
			let filterOrdersWithTemp = await checkOrdersWithTemp(orderList, tempLate, isManualImportResend);

			if (!filterOrdersWithTemp) {
				setIsBatchResend(false);
				return;
			}

			orderList = filterOrdersWithTemp as IResendItem[];

			let isBreak = false;

			if (!orderList.length) {
				setIsBatchResend(false);
				return;
			}

			if (orderList.length > 50) {
				remindModal();
				setIsBatchResend(false);
				return;
			}

			for (let i = 0; i < orderList.length; i++) {
				const pack = orderList[i];
				let _checkedOrder = [];
				// * 挂起、平台属于包裹维度
				if (![PLAT_FXG, PLAT_TB, PLAT_PDD, PLAT_TM, PLAT_KS, PLAT_JD, PLAT_SPH, PLAT_XHS, PLAT_C2M, PLAT_YZ].includes(pack.platform) || pack.isPending) {
					isBreak = true;
					break;
				}

				// * 多包裹发货
				const multiPackYdCount = pack.trades.length === 1 ? pack.trades[0]?.multiPackYdCount : pack.multiPackYdCount;
				if (multiPackYdCount > 1) {
					isBreak = true;
					break;
				}

				// * 未发货订单需要针对子订单进行判断
				for (let j = 0; j < pack.trades.length; j++) {
					const trade = pack.trades[j];
					let checkedOrders = trade.orders.filter(order => order.isChecked);
					_checkedOrder.push(...checkedOrders);
					isBreak = !checkedOrders.every(order => order.status === TradeStatus.等待买家确认收货);
					if (isBreak) break;

					// * 必须勾选所有子订单
					if (checkedOrders.length !== trade.orders.length) {
						isBreak = true;
						break;
					}
				}

				if (!_checkedOrder.length) {
					isBreak = true;
					break;
				}
				if (isBreak) break;
			}

			if (isBreak) {
				remindModal();
				setIsBatchResend(false);
			} else {
				let _orderList: IResendItem[] = [];
				orderList.forEach(pack => {
					pack.trades = pack.trades.filter(trade => trade.orders.filter(order => order.isChecked).length);
					let oldYdNoList = pack.ydNoSet?.map(ydNo => ydNo.split(',')[0]?.split(':')?.[1]?.trim());
					let oldYdNo = oldYdNoList[oldYdNoList.length - 1] || '';
					if (pack.isMerge) {
						// * 把合单打平
						pack.trades.forEach((trade, tradeIndex) => {
							let sid = '打印后生成';
							if (pack.sids[0] && !oldYdNoList.includes(pack.sids[0])) {
								sid = pack.sids[0];
							} else if (pack.sids[0] && !oldYdNoList.includes(pack.sids[pack.sids.length - 1])) {
								sid = pack.sids[pack.sids.length - 1];
							}
							if (sid === '打印后生成' || oldYdNoList.includes(sid)) {
								pack.canResend = false;
								pack.resendTips = <p className="r-c-gray">不支持重新发货</p>;
							} else {
								pack.canResend = true;
								pack.resendTips = '';
							}
							_orderList.push({
								...pack,
								...trade,
								mergeCount: pack.trades.length,
								showMerge: tradeIndex === 0,
								"trades": [trade],
								oldYdNo,
							});
						});
					} else {
						let sid = '打印后生成';
						if (pack.sids[0] && !oldYdNoList.includes(pack.sids[0])) {
							sid = pack.sids[0];
						} else if (pack.sids[0] && !oldYdNoList.includes(pack.sids[pack.sids.length - 1])) {
							sid = pack.sids[pack.sids.length - 1];
						}
						if (sid === '打印后生成' || oldYdNoList.includes(sid)) {
							pack.canResend = false;
							pack.resendTips = <p className="r-c-gray">不支持重新发货</p>;
						} else {
							pack.canResend = true;
							pack.resendTips = '';
						}
						_orderList.push({
							...pack,
							showMerge: true,
							mergeCount: pack.trades.length,
							oldYdNo,
						});
					}
				});

				console.log(_orderList, '_orderList');
				sendPoint(Pointer.订单_修改单号重新发货_弹窗展示);
				setOrderRecord(_orderList);
			}
		} catch (error) {
			setIsBatchResend(false);
		}
	};

	const onCellFn = (record: IPackage) => {
		if (record.showMerge) {
			return {
				rowSpan: record.mergeCount,
				className: 'rowSpanTd'
			};
		}
		return {
			rowSpan: 0,
		};
	};

	const columns: ColumnsType<IPackage> = [
		{
			title: "订单编号",
			dataIndex: "tids",
			width: 200,
			className: "verticalMid",
			render(val, record, index) {
				// * 优先展示合单中的tid
				return record.tid || val?.[0];
			}
		}, {
			title: "原单号",
			dataIndex: "oldYdNo",
			className: "verticalMid",
			width: 200
		}, {
			title: "新单号",
			dataIndex: "sids",
			width: 200,
			render(val, row) {
				let newYdNo = '打印后生成';
				if (val?.[0] && val?.[0] !== row.oldYdNo) {
					newYdNo = val?.[0];
				} else if (val?.[0] && val?.[val?.length - 1] !== row.oldYdNo) {
					newYdNo = val?.[val?.length - 1];
				}
				return <Input style={ { fontSize: 12 } } size="small" value={ newYdNo } disabled />;
			},
			onCell: onCellFn,
		}, {
			title: '说明',
			dataIndex: 'resendTips',
			onCell: onCellFn,
		}
	];

	const preResend = async() => {
		let canResendRecord = orderRecord.filter(item => item.canResend);
		if (!canResendRecord.length) {
			message.error('没有能够重新发货的订单');
			return;
		}
		// * 把相同togetherId的记录合并
		let resendList: IResendItem[] = [];
		canResendRecord.forEach((item, index) => {
			if (item.togetherId === canResendRecord?.[index - 1]?.togetherId) {
				resendList[resendList.length - 1].trades.push(...item.trades);
			} else {
				resendList.push({
					...item,
					sids: item.oldYdNo === item.sids?.[0] ? item.sids?.reverse() : item.sids,
					sidsExCode: item.oldYdNo === item.sids?.[0] ? item.sidsExCode?.reverse() : item.sidsExCode,
					sidsExNames: item.oldYdNo === item.sids?.[0] ? item?.sidsExNames?.reverse() : item.sidsExNames,
					sendMobile: item.oldYdNo === item.sids?.[0] ? item?.sendMobile?.reverse() : item.sendMobile,
				});
			}
		});
		let { params }:any = await gatherShipInfo(resendList, {
			isMergePrint,
			template: selectedTemp,
			templateGroup: selectedTempGroup,
			type: 'batchResend'
		});
		resendFn(params);
	};

	const resendFn = (params: {
		subTrades?: ISendSubTrades[];
		stockStatus?: string;
	}[]) => {
		// 添加发货类型
		(params && params.length > 0) && params.forEach(pack => {
			pack.subTrades.forEach(trade => {
				trade.sendType = SendType.重新发货;
			});
		});

		let timer = 0;
		let threadNum = 4;
		let failedList = [];
		let totalRequest = Math.ceil(params.length / 5);
		let requestCount = 0;

		const callApi = async(
			ApiParams: TradeBatchResendRequest,
			callback: any
		) => {
			try {
				pageLoading.loading();
				const sd = await TradeBatchResendApi(ApiParams);
				callback?.(sd, ApiParams);
			} catch (err) {
				pageLoading.destroy();
				console.log('err: ', err);
				callback?.(false, ApiParams);
			}
		};

		const handler = () => {
			if (!params.length) {
				timer++; // 资源已经耗尽，所有的请求发送出，等待所有发出的请求结束
				if (timer !== threadNum) {
					// 所有发出的请求已经结束，可以执行请求结束处理程序，当前同时异步数是4
					return;
				}
				return;
			}
			// 发货进行中
			callApi(
				params.splice(0, 5),
				(
					sd: TradeBatchResendResponse["data"],
					pms: TradeBatchResendRequest
				) => {
					requestCount++;
					// 去更新对应的包裹
					let _orderRecord = getOrderRecord();
					let successList = [];
					for (let i = 0; i < sd.length; i++) {
						let { result, togetherId, subTradeInfos } = sd[i];
						const { message } = subTradeInfos[0];

						let index = _orderRecord.findIndex(pack => pack.togetherId === togetherId);
						// eslint-disable-next-line no-continue
						if (index === -1) continue;
						let pack = _orderRecord[index];
						// 100发货成功，101, 部分发货成功，102 拆单发货成功，103发货失败
						if (result !== 103) {
							successList.push(pack);
							console.log('sd', sd);
							let exNumber = pms[i].subTrades[0].exNumber;
							pack.ydNoSet.push(
								`${templateName}:${exNumber}`
							);
							pack.ydNoSet = uniq(pack.ydNoSet);
							pack.resendTips = <p className="r-c-success">重新发货成功</p>;
						} else {
							pack.resendTips = <p className="r-c-error">重新发货失败:{message}</p>;
							failedList.push(pack.togetherId);
						}
					}
					setOrderRecord([..._orderRecord]);
					setErrorTidsList(failedList);
					const { list } = tradeStore.tradeListStore;
					successList.forEach(item => {
						let index = list.findIndex(i => i.togetherId == item.togetherId);
						if (index !== -1) {
							list[index].sendType = SendType.重新发货;
							list[index].ydNoSet = item.ydNoSet;
							list[index].trades.forEach(trade => {
								if (trade.orders.every(order => order.isChecked)) {
									trade.ydNoSet.push(item.ydNoSet[item.ydNoSet.length - 1]);
									trade.ydNoSet = uniq(trade.ydNoSet);
								}
							});
						}
					});
					pageLoading.destroy();
					// 继续去调用api查询数据，直到结束
					handler();
					if (requestCount >= totalRequest) {
						setIsStartResend(false);
						setIsResendOver(true);
						message.success('重新发货操作完成，请在弹窗内确认发货结果');
					}
				}
			);
		};

		setIsStartResend(true);
		// 资源竞争型，until deplete all the params，如增加同时异步数需要修改计数器
		for (let index = 0; index < threadNum; index++) {
			handler();
		}
	};

	const renderFooter = () => {
		if (isResendOver && errorTidsList.length) {
			return (
				<Button
					type="default"
					onClick={ () => {
						copyToPaste(errorTidsList.join(','));
					} }
					data-point={ Pointer.订单_修改单号重新发货_弹窗_批量复制失败订单号 }
				>批量复制失败订单号
				</Button>
			);
		} else if (isResendOver) {
			return null;
		} else {
			return (
				<>
					<Button type="default" onClick={ () => { setIsBatchResend(false); } }>取消</Button>
					<Button
						type="primary"
						data-point={ Pointer.订单_修改单号重新发货_弹窗_重新发货 }
						onClick={ preResend }
						loading={ isStartResend }
					>重新发货
					</Button>
				</>
			);
		}
	};

	return (
		<>
			{orderRecord.length && (
				<Modal
					title="修改单号重新发货"
					visible
					centered
					width={ 900 }
					onCancel={ () => { setIsBatchResend(false); } }
					footer={ renderFooter() }
				>
					<p className="r-mb-12">快递模板：{templateName || ''}</p>
					<Table
						rowKey="tid"
						scroll={ { y: 450 } }
						dataSource={ orderRecord }
						pagination={ false }
						columns={ columns }
					/>
				</Modal>
			)}
		</>
	);
};

export default observer(BatchResend);
