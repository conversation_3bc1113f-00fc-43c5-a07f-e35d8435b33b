import React, { useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, Modal, Form, Select, Progress, Table, InputNumber } from "antd";
import { ExclamationCircleOutlined, CopyOutlined } from '@ant-design/icons';
import { useRequest, useReactive } from "ahooks";
import cs from "classnames";
import _, { compact, uniq } from "lodash";
import { toJS } from 'mobx';
import userStore from '@/stores/user';
import { tradeStore } from '@/stores';
import scanPrintStore from "@/stores/trade/scanPrint";
import { local, session } from "@/libs/db";
import { Base } from "@/types/schemas";
import PrintCenter from '@/print/index';
import message from "@/components/message";
import { SendType, TradeStatus, TradeOptEnum } from "@/utils/enum/trade";
import { PLAT_FXG, stockVersion, PLAT_HAND, PLAT_PDD, PLAT_TB, PLAT_SPH, PRINT_MAP, PLAT_ALI, PLAT_JD, PLAT_KS, HL_PLAT, PLAT_C2M, PLAT_ALL_SCM, PLAT_YZ } from "@/constants";
import useGetState from "@/utils/hooks/useGetState";
import { TradeBatchGetBicOrderCodeApi, TradeOperateLogAddApi, TradeTradeDetailGetApi } from "@/apis/trade";
import { copyToPaste, splitFxgTid } from "@/utils";
import { filterPrintContent } from "@/utils/trade/printContent";
import { orderPreOccupiedStock } from '@/pages/Index/Settings/System/constants';
import event from '@/libs/event';
import { getModalTableScrollHeight } from '@/utils/util';
import { isSourceScm, dealPlatAsHandPlat, transferOtherToHand } from '@/components-biz/ShopListSelect/shopListUtils';
import { AbnormalFromType } from "@/pages/Trade/components/BottomCom/checkAbnormalUtil";
import { TradeWaveCreateWaveApi } from "@/apis/warehouse/wave";
import { TradeWaveCreateWaveRequest } from "@/types/warehouse/wave";
import memoFn from "@/libs/memorizeFn";
import history from "@/utils/history";
import { ISendOrders, ISendSubTrades, TradeBatchSendData, TradeBatchSendRequest, TradeQueryStockInfoResponse, refluxOrderPrintSettingsEnum } from "@/types/trade/index";
import s from './index.module.scss';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';

interface IProps extends Base{

}
const searchInitValue: any = {
	maxNum: '',
};
// 批量生产拣货波次
const BatchCreateWave = (props: IProps) => {
	const {
		isMergePrint,
		setSelectedTemp,
		selectedTemp,
		setSelectedTempGroup,
		selectedTempGroup,
	} = tradeStore;
	const { 
		list, 
		handleUpdateList, 
		isBatchCreateWave, 
		setIsBatchCreateWave
	} = tradeStore.tradeListStore;

	const { systemSetting, userInfo, isStockAllocationVersion } = userStore;

	const [form] = Form.useForm();
    
	const [orderRecord, setOrderRecord, getOrderRecord] = useGetState<IProps[]>([]);
	const [printLabelShow, setPrintLabelShow] = useState(false);
	const [formData, setFormData] = useState<any>({ ...searchInitValue });

	// 下载进度
	const modalProgress = useReactive({
		total: 0, // 总的请求
		completed: 0, // 已完成请求
		progress: 0,
		progressVisibility: false,
		progressResultVisibility: false, // 失败列表
		data: {} as any,
	});

	const resetModalProgress = () => {
		modalProgress.total = 0;
		modalProgress.completed = 0;
		modalProgress.progressVisibility = false;
		modalProgress.progress = 0;
		modalProgress.progressResultVisibility = false;
		modalProgress.data = {};
	};


	// 异常检测结果回调
	const handleAbnormalResult = (data) => {
		const { isBreak, orderList, tempLate } = data;

		// 有异常且关闭了弹框
		if (isBreak) {
			// 中断流程
			setIsBatchCreateWave(false);
			return;
		} 

		if (orderList?.length) {
			const _orderList: IProps[] = _.cloneDeep(orderList);
			console.log('%c [ 生成波次的数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', _orderList);
			setOrderRecord(_orderList); // 保存一份备用

			startCreateWaveBefore(_orderList);
		} else {
			// 没有符合条件的订单
			message.warning('没有符合条件的订单可生成波次');
			setIsBatchCreateWave(false);
		}
	};

	// 波次生成前拦截
	const checkFn = async() => {
		let packList: IProps[] = _.cloneDeep(list?.filter(item => item.isChecked) || []);
		if (!packList.length) {
			message.warning('请先选择订单');
			setIsBatchCreateWave(false);
			return;
		}

		// 校验有没有选中模板
		if (!isMergePrint && _.isEmpty(selectedTemp)) {
			message.warning('请选择快递模板');
			setIsBatchCreateWave(false);
			return;
		}
		// 添加手动导入单号检查
		const isManualImport = (!isMergePrint && selectedTemp?.Exid === -901) || (isMergePrint && selectedTempGroup?.id === -901);
		if (isManualImport) {
			message.warning('手动导入单号不支持生成波次，请选择其他快递模板');
			setIsBatchCreateWave(false);
			return;
		}
		if (isMergePrint) {
			if (_.isEmpty(selectedTempGroup)) {
				message.warning('请选择快递模板组');
				setIsBatchCreateWave(false);
				return;
			}
	
			if (selectedTempGroup.userTemplateList && !selectedTempGroup.userTemplateList.length) {
				message.warning('当前模板组暂无模板，请添加模板或更换模板组');
				setIsBatchCreateWave(false);
				return;
			}
		}

		// 生成波次前，需先校验是否开启预占库存，未开启预占库存不支持生成波次操作
		if (userInfo?.version === stockVersion.库存版 && systemSetting.orderPreOccupiedStock == orderPreOccupiedStock.不占用库存) {
			Modal.confirm({
				centered: true,
				title: '系统提示',
				okText: '我知道了',
				cancelText: '取消',
				icon: <ExclamationCircleOutlined />,
				content: (
					<div className={ cs('r-c-error') }>请先开启订单预占库存设置后，再进行生成波次操作</div>
				),
				closable: true,
				keyboard: false,
				maskClosable: false,
				// cancelButtonProps: { hidden: true }
			});
			setIsBatchCreateWave(false);
			return;
		}

		// 校验勾选数据是否符合要求
		// 若勾选订单存在以下情况，则不可操作，点击确定关闭弹窗
		let isBreak = false;
		let errorText = "";

		for (let i = 0; i < packList.length; i++) {
			const pack = packList[i];
			let _checkedOrder = [];

			if (isSourceScm(pack)) {
				isBreak = true;
				errorText = '分销推送订单不支持生成波次';
				break;
			}

			let checkedOrders = [];
			if (pack.trades?.length) {
				checkedOrders = pack.trades.map(trade => trade.orders.filter(order => order.isChecked)).flat();
			}
			if (!checkedOrders.length) {
				isBreak = true;
				errorText = '没有勾选任何商品';
				break;
			}

			// 未勾选所有待发货的商品的订单
			for (let j = 0; j < pack.trades.length; j++) {
				const trade = pack.trades[j];
				let checkedOrders = trade.orders.filter(order => order.isChecked);

				// 必须勾选有符合条件的商品
				isBreak = !([TradeStatus.等待卖家发货, TradeStatus.卖家部分发货].includes(trade.status as TradeStatus) || checkedOrders.some(order => order.firstSend));
				if (isBreak) {
					errorText = '非待发货/部分发货、先发货订单';
					break;
				}
					
				// 必须是待发货/部分发货、先发货订单
				isBreak = !checkedOrders.every(order => [TradeStatus.等待卖家发货, TradeStatus.卖家部分发货].includes(order.status) || order.firstSend);
				if (isBreak) {
					errorText = '有不符合条件商品被勾选';
					break;
				}

				_checkedOrder.push(...checkedOrders);
			}

			if (!_checkedOrder.length) {
				isBreak = true;
				if (!errorText) {
					errorText = '没有勾选符合条件的商品';
				}
				break;
			}
			if (isBreak) break;
		}

		if (isBreak) {
			console.log('%c [ errorText ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', errorText);
			
			if (errorText == '没有勾选任何商品') {
				message.warning('有订单没有勾选任何商品');
				setIsBatchCreateWave(false);
				return;
			}

			remindModal();
			setIsBatchCreateWave(false);
		} else {
			// 开始生成波次前异常校验逻辑： 具体查询异常订单检测配置
			// 做发货异常校验

			// 注册一次性事件监听器来接收结果
			event.once('wave.abnormal.result', handleAbnormalResult);

			event.emit('tradeList.send.abnormal', {
				afterPrint: null,
				orderList: packList,
				type: AbnormalFromType.波次生成,
			});
		}

	};

	// 请求详情，更新详情数据, 这里可以接受对象、也可以数组
	const { run: onUpdate, loading: updateLoading } = useRequest(TradeTradeDetailGetApi, {
		manual: true,
		onSuccess(res, params) {
			const { list = [] } = res?.data || {};
			if (list.length) {
				handleUpdateList({
					type: "updateTradesByDealAbnormal",
					data: list,
					// needCheck: true, // 需要选中就带上
				});
				// handleCancel();
			}
		},
		onError(e, params) {
			handleCancel();
		},
	});

	const handleOk = () => {
		handleCancel();
	};

	const handleCancel = () => {
		resetModalProgress();
		setIsBatchCreateWave(false);
	};

	// 波次生成前弹框
	const startCreateWaveBefore = (orderList) => {
		setPrintLabelShow(true);

		// 波次最大单数
		let maxNum = local.get('waveMaxNum');
		if (!maxNum || maxNum == 'null') {
			maxNum = 30;
			local.set('waveMaxNum', maxNum);
		}
		setFormData({ maxNum });
		setTimeout(() => {
			form?.setFieldsValue({
				maxNum
			});
		}, 100);
	};

	// 不分批方案，由后端分批，不满的放最后，前端假进度条
	const startCreateWave = async(packageList) => {
		console.log('%c [ packageList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', packageList);
		try {
			let template = isMergePrint ? selectedTempGroup : selectedTemp;
			console.log('%c [ template ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', toJS(template));
			// 显示进度条
			modalProgress.progressVisibility = true;
			modalProgress.total = packageList.length || 0;
			modalProgress.progress = 0;
			
			// 模拟进度增长
			const progressInterval = setInterval(() => {
				if (modalProgress.progress < 99) {
					modalProgress.progress += Math.floor(Math.random() * 5) + 1;
					if (modalProgress.progress > 99) {
						modalProgress.progress = 99;
					}
				}
			}, 100);

			// 为每个包裹获取对应的模板信息的函数
			const getTemplateInfoForPackage = (pack) => {
				if (!isMergePrint && template) {
					return {
						exId: +template.Exid,
						exCode: template.ExCode,
						exName: template.ExcodeName,
						kddType: template.kddType,
						userTemplateId: template.Mode_ListShowId, // 单模板只有Mode_ListShowId
					};
				} else if (isMergePrint && template) {
					const { setting } = tradeStore;
					let orderPLatSetting = setting?.groupPrintSetJsonString?.orderMatchSetting || [];

					// 手动导入单号模板 （手动导入单号暂时不能生成波次）
					if (template.id == -901) {
						return {
							exId: -901,
							exCode: undefined,
							exName: selectedTempGroup.groupName,
							kddType: undefined,
							userTemplateId: -901,
						};
					} else {
						// 查找匹配的模板组
						const currentTemplateGroup = selectedTempGroup?.userTemplateList?.find(it => {
							if (dealPlatAsHandPlat(pack.platform, null, pack)) {
								let orderSettingType = orderPLatSetting.find(o => o.platform == transferOtherToHand(pack.platform, pack));
								return it.expressType == orderSettingType?.bindControlType;
							}

							// 处理1688回流订单
							if (pack.platform == PLAT_ALI && pack.hlPlatformType) {
								const advancedRes = memoFn.getAdvancedSet();
								let isUsePlatTemp = advancedRes?.refluxOrderPrintSet == refluxOrderPrintSettingsEnum.使用对应平台面单打印;

								if (pack.hlEncryptOrder || (isUsePlatTemp && !pack.hlEncryptOrder && pack.hlPlatformType !== 'hl-sph')) {
									const hlPlat = HL_PLAT[pack.hlPlatformType];
									const printType = PRINT_MAP[hlPlat] || PRINT_MAP[PLAT_ALI];
									if (printType == "13") {
										return [13, 16].includes(it.expressType);
									} else {
										return it.expressType == printType;
									}
								}
							}

							if (PRINT_MAP[pack.platform] == "13") {
								return [13, 16].includes(it.expressType);
							} else {
								return it.expressType == PRINT_MAP[pack.platform];
							}
						});

						if (currentTemplateGroup) {
							return {
								exId: +currentTemplateGroup.exId,
								exCode: currentTemplateGroup.exCode,
								exName: currentTemplateGroup.exName,
								kddType: currentTemplateGroup.expressType,
								userTemplateId: currentTemplateGroup.userTemplateId,
							};
						}
					}
				}
				return {};
			};
			
			// 组装请求参数
			const packageInfoList = packageList.map(pack => {
				// 为每个包裹获取对应的模板信息
				const templateInfo = getTemplateInfoForPackage(pack);
				const { exId, exCode, exName, kddType, userTemplateId } = templateInfo || {};

				// 构建 orderIds，参考 tidsWithOids 的逻辑
				let orderIds = [];
				pack.trades.forEach(trade => {
					let checkedOrders = trade.orders.filter(order => order.isChecked);
		
					// 选中订单才需要添加
					if (checkedOrders.length === 0) {
						return;
					}

					// 所有商品都被选中的话，直接使用 ptTid
					if (checkedOrders.length === trade.orders.length) {
						orderIds.push(trade.ptTid);
					} else {
						// 部分商品被选中，使用 tid:oid,oid 格式（注意这里用逗号分隔oid）
						orderIds.push(`${trade.ptTid}:${checkedOrders.map(o => o.oid).join(",")}`);
					}
				});
	
				// 获取订单中的子订单信息
				const tradeInfoList = pack.trades.map(trade => {
					// 获取子订单中的商品信息
					const orderInfoList = trade.orders.filter(o => o.isChecked).map(o => {
						return {
							oid: o.oid,
							ptOid: o.ptOid,
							itemId: o.numIid,
							skuId: o.skuId,
							sysItemId: o.systemNumIid,
							sysSkuId: o.systemSkuId,
							number: o.num
						};
					});
					
					return {
						ptTid: trade.ptTid,
						tid: trade.tid,
						orderInfoList
					};
				});

				let exNumberList = [];
				if ([PLAT_KS].includes(pack.platform)) {
					exNumberList = pack.sids?.filter((i) => i !== '打印后生成').slice(0, 20);
				} else {
					exNumberList = pack.sids?.filter((i) => i !== '打印后生成').slice(0, 30);
				}

				return {
					exId, // exId,
					exCode: exCode || pack.sidsExCode?.[0] || '', // 快递code,
					exName: pack.sidsExNames?.[0] || exName || '', // 快递名,
					userTemplateId: pack?.userTemplateId || userTemplateId || '', // pack里面没存userTemplateId
					exNumber: exNumberList.join(','), // 快递单号 pack.sids?.[0] || ''
					orderIds: orderIds.join('|'), // 订单编号，参考 tidsWithOids 逻辑  需要打印处理分割
					deliveryInfo: pack.printContent || '', // 发货信息
					tradeInfoList
				};
			});
			
			// 构建请求参数
			const params: TradeWaveCreateWaveRequest = {
				packageInfoList,
				maxTradeNum: formData.maxNum || 30,
			};

			console.log('%c [ params ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
			// return;
			
			// 调用API
			const res = await TradeWaveCreateWaveApi(params);
			
			// 清除进度条定时器
			clearInterval(progressInterval);
			
			// 设置进度为100%
			modalProgress.progress = 100;
			modalProgress.completed = packageList.length;
			
			// 处理响应结果
			setTimeout(() => {
				// 隐藏进度，显示结果
				modalProgress.progressVisibility = false;
				modalProgress.progressResultVisibility = true;
				
				// 处理成功和失败的结果
				const successList = res?.successResultList || [];
				const failList = res?.failReasonList || [];
				
				// 构建结果数据
				if (!failList.length && successList.length) {
					// 全部成功
					modalProgress.data = {
						successNum: res.successPackageNum || 0,
						successResultCollectList: successList.map(item => ({
							tid: item.tid,
							ptTid: item.ptTid
						})),
						waveNum: res?.waveNum || 0
					};
				} else {
					modalProgress.data = {
						successNum: res.successPackageNum || 0,
						failedNum: res.failPackageNum || 0,
						errorResultCollectList: failList.map(item => ({
							ptTid: item.ptTid,
							message: item.failReason || '生成波次失败'
						})),
						successResultCollectList: successList.map(item => ({
							tid: item.tid,
							ptTid: item.ptTid
						})),
						waveNum: res.waveNum || 0,
					};
				}
				
				// 更新订单列表
				if (successList.length) {
					// 统计出所有成功的订单，转成需要的格式
					const tradeInfos = [];
					let allTids = successList.map(item => item.tid) || [];
					let samePlatformAndSellerIdMap = new Map();
					
					// 找出这些订单编号对应的数据
					packageList?.forEach(item => {
						const { sellerId, platform, togetherId, tids } = item;
						if (allTids?.some(d => tids?.[0]?.includes(d))) {
							const key = `${platform}_${sellerId}_${togetherId}`;
							// 把相同平台和店铺、且同一个合单的放一个对象的tids里面
							if (!samePlatformAndSellerIdMap.has(key)) {
								samePlatformAndSellerIdMap.set(key, { platform, sellerId, tids: togetherId?.split('|') });
							}
						}
					});
					
					// 直接更新有符合条件的合单的详情
					samePlatformAndSellerIdMap.forEach((value, key) => {  
						tradeInfos.push(value);  
					});

					console.log('%c [ 要更新的订单 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', tradeInfos, samePlatformAndSellerIdMap, successList);
					
					if (tradeInfos?.length) {
						console.log('%c [ 开始更新详情 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', tradeInfos);
						onUpdate({ tradeInfos });
					}
				}
			}, 200);
		} catch (error) {
			// 处理错误
			console.error('生成波次失败:', error);
			
			// 显示错误结果
			modalProgress.progressVisibility = false;
			modalProgress.progressResultVisibility = true;
			modalProgress.data = {
				successNum: 0,
				failedNum: packageList.length,
				errorResultCollectList: packageList.map(item => ({
					ptTid: item.ptTids[0],
					message: error?.message || error?.errorMessage || '网络请求失败'
				})),
				successResultCollectList: [],
				waveNum: 0
			};
			// 这里不提示message错误消息，接口不提示报错
			// message.error('生成波次失败：' + (error?.message || '网络请求失败'));
		}
	};

	const remindModal = () => {
		Modal.warning({
			centered: true,
			title: '提醒',
			width: 540,
			content: (
				<div className="r-flex r-fd-c">
					<div>所选订单中部分订单不支持生成波次，请不要选择以下订单：</div>
					<div>1、非“待发货/部分发货、先发货订单”订单</div>
					<div>2、分销订单</div>
					{/* <div className="r-c-error">注：仅支持待发货/部分发货、先发货订单生成波次；</div> */}
				</div>
			),
			okText: '确定',
			closable: true,
			maskClosable: false,
			cancelButtonProps: { hidden: true }
		});
	};

	// 去查看
	const handleJumpToSearch = () => {
		const successList = modalProgress.data?.successResultCollectList || []; // 直接查询替换成功订单
		// const tids = successList?.map(item => item.tid)?.join(',');

		// 本页面查询
		// event.emit('tradeSetting.handleSearchParams', { tid: successList?.map(item => item.tid)?.join(',') });

		// 打开波次打印页面
		// history.push('/warehouse/wave');

		// 如要要带参数查询对应的波次
		history.push({ pathname: '/warehouse/wave', state: { ptTids: successList?.map(item => item.ptTid)?.join(',') } });

		setTimeout(() => {
			handleCancel();
		}, 100);
	};

	// 复制失败的订单编号
	const batchCopyProgressResult = () => {
		let list = modalProgress.data?.errorResultCollectList || [];
		if (!list.length) {
			message.error('没有可复制内容');
			return;
		}
		const copyIds = list.map(row => row.ptTid).join(",");
		copyToPaste(copyIds);
	};
    
	const footer = (
		<div className="r-flex r-jc-sb r-ai-c">
			<Button onClick={ batchCopyProgressResult } type="link">复制失败的订单编号</Button>
			
			<div className="r-ml-20 r-flex r-jc-fe r-ai-c">
				<Button onClick={ handleOk } loading={ updateLoading }>取消</Button>
				{
					modalProgress.data?.successNum ? (
						<Button type="primary" onClick={ handleJumpToSearch }>查看已生成波次</Button>
					) : (
						<Button type="primary" onClick={ handleOk } loading={ updateLoading }>确定</Button>
					)
				}
			</div>
		</div>
	);
	const footer2 = (
		<div className="r-flex r-jc-fe r-ai-c">
			<Button onClick={ handleOk } loading={ updateLoading }>取消</Button>
			
			<div className="r-ml-20 r-flex r-ai-c">
				<Button type="primary" onClick={ handleJumpToSearch }>查看已生成波次</Button>
			</div>
		</div>
	);

	const progressResultColumns = [
		{
			title: '序号',
			width: 50,
			render: (value: any, row: any, index: number) => {
				return (
					<div>{index + 1 }</div>
				);
			},
		},
		{
			title: '订单编号',
			width: 300,
			render: (value: any, row: any, index: number) => {
				return (
					<div>
						<span>{row.ptTid}</span>
						<CopyOutlined
							hidden={ !row['ptTid'] }
							onClick={ (event) => {
								event.stopPropagation();
								copyToPaste(splitFxgTid(row['ptTid']));
							} }
							className={ cs('r-fc-black-65', 'r-ml-2', 'r-pointer') }
						/>
					</div>
				);
			},
		},
		{
			title: '失败原因',
			width: 300,
			render: (value: any, row: any, index: number) => {
				return (
					<div>{row.message}</div>
				);
			},
		}
	];

	const onFieldsChange = (changedValues, allValues) => {
		console.log('%c [ 111 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', allValues);
		setFormData(allValues);

		// 波次最大单数缓存
		// 波次最大单数缓存
		if ('maxNum' in changedValues) {
			local.set('waveMaxNum', allValues.maxNum);
		}
	};

	// 关闭波次最大次数弹框
	const handleClosePrintLabelShow = () => {
		setPrintLabelShow(false); 
		setIsBatchCreateWave(false);
	};

	// 波次最大次数弹框确定
	const handleOkPrintLabelShow = () => {
		sendPoint(Pointer.订单_操作_生成波次_确定);
		// 验证波次最大单数范围
		const maxNum = formData.maxNum;
		if (!maxNum || maxNum < 1 || maxNum > 100) {
			message.warning('波次最大单数必须在1-100之间');
			return;
		}
		setPrintLabelShow(false); 
		// 使用新的波次生成逻辑
		startCreateWave(getOrderRecord());
	};

	useEffect(() => {
		checkFn();

		return () => {
			resetModalProgress();
			setOrderRecord([]);
			setPrintLabelShow(false);
			setFormData({});
		};
	}, []);
    
	return (
		<>
			{
				modalProgress.progressVisibility && (
					<Modal
						title="生成波次中"
						centered
						visible
						destroyOnClose
						closable={ false }
						maskClosable={ false }
						keyboard={ false }
						width={ 480 }
						okText="确定"
						cancelButtonProps={ { hidden: true } }
						onOk={ () => { modalProgress.progressVisibility = false; } }
					>
						<div className="r-flex r-jc-c">
							<Progress type="circle" percent={ modalProgress?.progress } />
						</div>
					</Modal>
				)
			}

			{
				modalProgress.progressResultVisibility && modalProgress.data?.failedNum && (
					<Modal
						centered
						title="生成拣货波次"
						visible
						onCancel={ handleOk }
						width={ 800 }
						destroyOnClose
						closable
						maskClosable={ false }
						footer={ footer }
					>
						<div>
							<div className="r-flex r-jc-c r-mb-20">
								<div>成功：<span className="r-fs-20 r-c-success">{modalProgress.data?.successNum || 0}</span> <span className="r-fs-20">单</span></div>
								<div className="r-ml-20">失败：<span className="r-fs-20 r-c-error">{modalProgress.data?.failedNum || 0}</span> <span className="r-fs-20">单</span></div>
								<div className="r-ml-20">生成：<span className="r-fs-20 r-c-error">{modalProgress.data?.waveNum || 0}</span> <span className="r-fs-20">个波次</span></div>
							</div >

							{
								modalProgress.data?.errorResultCollectList?.length > 0 && (
									<>
										<div className="r-mb-16">以下订单生成失败：</div>
										<Table
											rowKey="tid"
											columns={ progressResultColumns }
											dataSource={ modalProgress.data?.errorResultCollectList }
											pagination={ false }
											size="small"
											scroll={ { y: getModalTableScrollHeight() } }
										/>
									</>
								)
							}
						</div>
					</Modal>
				)
			}

			{
				modalProgress.progressResultVisibility && !modalProgress.data?.failedNum && (
					<Modal
						centered
						title="生成拣货波次"
						visible
						onCancel={ handleOk }
						width={ 800 }
						destroyOnClose
						closable
						maskClosable={ false }
						footer={ footer2 }
					>
						<div>
							<div className="r-flex r-jc-c r-mb-20">
								<div>本次成功：<span className="r-fs-20 r-c-success">{modalProgress.data?.successNum || 0}</span> <span className="r-fs-20">单</span></div>
								<div className="r-ml-20">生成：<span className="r-fs-20 r-c-primary">{modalProgress.data?.waveNum || 0}</span> <span className="r-fs-20">个波次</span></div>
							</div >
						</div>
					</Modal>
				)
			}

			{
				printLabelShow && (
					<Modal
						centered
						title="生成波次"
						visible
						width={ 460 }
						onCancel={ handleClosePrintLabelShow }
						onOk={ handleOkPrintLabelShow }
						closable
						maskClosable={ false }
						keyboard={ false }
						destroyOnClose
						zIndex={ 1051 }
						okText="确定"
						cancelText="取消"
					>
						<div className="r-flex r-ai-c r-mb-12">
							<div className="r-fs-14 r-fc-black-85">本次勾选：</div>
							<div className="r-flex r-ai-c r-fs-14">
								<span className="trade-status-error">{orderRecord?.length}</span>
								<span className="r-ml-6"> 单</span>
							</div>
						</div>
						<Form
							form={ form }
							{ ...{
								labelCol: { span: 6 },
								wrapperCol: { span: 18 },
							} }
							onValuesChange={ onFieldsChange }
						>        
							<div className="r-flex r-ai-c">
								<div>
									<Form.Item
										label="波次最大单数"
										name="maxNum"
										style={ { marginBottom: 0 } }
										labelCol={ { span: 15 } }
										wrapperCol={ { span: 9 } }
									>
										<InputNumber 
											className={ formData.maxNum ? 'high-light-bg' : '' } 
											size="middle"
											min={ 1 } 
											max={ 100 } 
											placeholder="" 
											style={ { width: 60, marginBottom: 0 } } 
										/>
									</Form.Item>
								</div>
								<span className="r-ml-6"> 单</span>
							</div>

			
						</Form>
            
					</Modal>
				)
			}
			
		</>
	);
};

export default observer(BatchCreateWave);
