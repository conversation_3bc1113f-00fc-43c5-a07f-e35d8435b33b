import React, { useState } from "react";
import { But<PERSON>, Checkbox, Modal, Popover, Select } from "antd";
import _, { uniq } from "lodash";
import { toJS } from "mobx";
import event from "@/libs/event";
import { IGroupRelationRecordList, ISubTrade, IOrders, IPackage } from "../../interface";
import { ISendOrders, ISendSubTrades, TradeBatchSendData, TradeBatchSendRequest, TradeQueryStockInfoResponse, refluxOrderPrintSettingsEnum } from "@/types/trade/index";
import s from './index.module.scss';
import { GetTradeEncryptOutOrderInfo, TradeAllotStockNumByTidListApi, TradeQueryStockInfoApi } from '@/apis/trade';
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import { IKddTemp, IKddTempGroup } from "@/types/schemas/report";
import { PLAT_HAND, PLAT_PDD, PLAT_TB, PLAT_SPH, PRINT_MAP, PLAT_FXG, PLAT_ALI, PLAT_JD, PLAT_KS, HL_PLAT, PLAT_C2M, PLAT_ALL_SCM, PLAT_YZ, PLAT_KTT } from "@/constants";
import scanPrintStore from "@/stores/trade/scanPrint";
import { SID_STATUS, getUnPromiseLogisticsOrder } from "../AbnormalManageModal/abnormalOrder";
import message from "@/components/message";
import { IgnoreStatus, SendType, TradeChoiceType, TradeOptEnum, TradeStatus } from "@/utils/enum/trade";
import Icon from "@/components/Icon";
import { ABNORMAL_MODAL_BUTTON_PROPS, WaybillNoSource } from "../../constants";
import { groupStockNumOutWay, openAbnormalCheck } from "@/pages/Index/Settings/System/constants";
import { dealPlatAsHandPlat, isSourceHand, isSourceScm, transferOtherToHand } from "@/components-biz/ShopListSelect/shopListUtils";
import { pageLoading } from "@/components/PageLoading";
import { copyToPaste, splitFxgTid } from "@/utils";
import timer from "@/libs/timer";
import { SysExchangeTypeEnum } from "./constants";
import { AbnormalFromType, checkDistributionToFilter } from "./checkAbnormalUtil";
import voiceHandleAbnormal from '@/assets/mp3/请处理异常.mp3';
import voice发货失败 from '@/assets/mp3/发货失败.mp3';
import { playAudio, playAudioAsync, textTransformAudioPlay } from "../../ScanPrint/utils";
import packWeightStore from "@/stores/trade/packWeight";
import { KsConsolidateType } from '@/pages/Trade/utils';
import memoFn from "@/libs/memorizeFn";

export interface ICheckStockItem {
	tid?: string;
	num?: number;
	sysItemId?: number | string;
	sysSkuId?: number | string;
	stockNum?: string | number;
	titleShort?: string;
	skuPropertiesName?: string;
	togetherIds?: any[];
	groupRelationRecordList?: IGroupRelationRecordList[];
	parentSkuId?: string | number; // 组合商品的skuId
	sids?: any;
}

export interface ISendDelivery {
	togetherId?: string,
	isSendAbnormal?: boolean,
	isNoLogistics?: boolean,
	noLogisticsType?: SendType,
	noLogisticsParams?: {exName?: string, exNumber?: string},
	isPreShip?: boolean,
	optionType?: TradeOptEnum,
	noTemplate?: boolean, // 是否不需要快递模板
	callFrom?: 'directInputYdNo', // directInputYdNo 来自直接手动导入单号弹窗发货
	type?: 'trade' | 'scanPrint' | 'scanPrintKdd' | 'refundLabel' | 'inspectSend' | 'weighSend' | 'postPrintSend' | 'batchResend',
	scanPrintList?: IPackage[],
	afterPrint?: boolean,
	hasRefundIds?: boolean,
	callback?: (value: { [key: string]: any }) => void
	inspectSendCallback?: (value: { [key: string]: any }) => void
	_scanLabelId?: string
	scanPrintTemplateInfo?: any // 传递扫描打印模板信息
}

const customLogPost = (type: ISendDelivery['type'] | '', dataType: string, data:any = {}) => {
	let title = '发货';
	switch (type) {
		case 'scanPrint':
			title = '扫描打印发货';
			break;
		case 'refundLabel':
			title = '退款标签重匹配自动发货';
			break;
		case 'postPrintSend':
			title = '后置打印自动发货';
			break;
		case 'weighSend':
			title = '称重发货';
			break;
		case 'inspectSend':
			title = '验货发货';
			break;

		default:
			break;
	}
	window.errorCollection?.customMessageUpload({
		type: `${title} sendDelivery【${dataType}】`,
		data: {
			...data,
			type
		}
	});
};

export const sendDelivery = async(sendDeliveryParams: ISendDelivery,) => {
	const {
		kddTempList,
		selectedTemp,
		selectedTempGroup,
	} = tradeStore;
	const {
		togetherId = '',
		isSendAbnormal = false,
		isNoLogistics = false,
		noLogisticsParams = {},
		noLogisticsType = '',
		isPreShip = false,
		type = 'trade',
		optionType = null,
		scanPrintList = [],
		afterPrint = false,
		noTemplate = false,
		hasRefundIds = false,
		callback,
		inspectSendCallback,
		callFrom,
		_scanLabelId,
		scanPrintTemplateInfo
	} = sendDeliveryParams;
	const { isMergePrint, setting } = tradeStore;
	console.log('setting', setting);
	const { template, templateGroup, allList } = getCurTempAndList(type, scanPrintList, scanPrintTemplateInfo);
	console.log('allList:', allList);
	let multiPackageDelivery = setting?.printSetExpandDTO?.multiplePackageDelivery && localStorage.getItem('multiPackageDelivery') == 'true';

	console.log(template, templateGroup, 'sendDeilry');

	if (!noTemplate && !isMergePrint && _.isEmpty(template) && !isNoLogistics) {
		message.warning('请选择快递模板');
		if (type === 'scanPrint') { playAudio(voice发货失败); }
		customLogPost(type, '发货失败,请选择快递模板', {
			isMergePrint,
			templateGroup,
			template,
			kddTempList: toJS(kddTempList),
			selectedTemp,
			selectedTempGroup,
			_scanLabelId,
			scanPrintList
		});

		if (type === 'scanPrint') { 
			setTimeout(() => {
				customLogPost(type, '发货失败,请选择快递模板2', {
					isMergePrint,
					kddTempList: toJS(tradeStore.kddTempList),
					templateInfo: scanPrintStore.scanPrintFormSetting?.templateInfo,
				});
			}, 300);
		}
		return;
	}

	if (!noTemplate && isMergePrint && !isNoLogistics) {
		if (_.isEmpty(templateGroup)) {
			message.warning('请选择快递模板组');
			if (type === 'scanPrint') { playAudio(voice发货失败); }
			customLogPost(type, '发货失败,请选择快递模板组', {
				templateGroup,
				template,
				kddTempList: toJS(kddTempList),
				selectedTemp,
				selectedTempGroup,
				_scanLabelId,
				scanPrintList
			});

			if (type === 'scanPrint') { 
				setTimeout(() => {
					customLogPost(type, '发货失败,请选择快递模板组2', {
						isMergePrint,
						kddTempList: toJS(tradeStore.kddTempList),
						templateInfo: scanPrintStore.scanPrintFormSetting?.templateInfo,
					});
				}, 300);
			}
			return;
		}

		if (templateGroup.userTemplateList && !templateGroup.userTemplateList.length) {
			message.warning('当前模板组暂无模板，请添加模板或更换模板组');
			if (type === 'scanPrint') { playAudio(voice发货失败); }
			customLogPost(type, '当前模板组暂无模板，请添加模板或更换模板组', {
				templateGroup,
				template,
				kddTempList: toJS(kddTempList),
				selectedTemp,
				selectedTempGroup,
				_scanLabelId,
				scanPrintList
			});

			if (type === 'scanPrint') { 
				setTimeout(() => {
					customLogPost(type, '当前模板组暂无模板，请添加模板或更换模板组2', {
						isMergePrint,
						kddTempList: toJS(tradeStore.kddTempList),
						templateInfo: scanPrintStore.scanPrintFormSetting?.templateInfo,
					});
				}, 300);
			}
			return;
		}
	}

	let list: IPackage[] = [];
	let isSourceScmTrade = false;
	let isSourceHandTrade = false;

	if (togetherId) {
		list = _.cloneDeep([allList.find(item => item.togetherId === togetherId)]);
		isSourceScmTrade = isSourceScm({ source: list[0].source }); // 是否分销订单
		isSourceHandTrade = isSourceHand({ source: list[0].source, platform: list[0].platform }); // 是否手工单
	} else {
		list = _.cloneDeep(allList.filter(item => item.isChecked));
	}

	await userStore.getSystemSetting();

	const { userSetting } = userStore;

	// openAbnormalCheck是一个老的字段默认是0开启，现在用户默认都是开启异常检测的
	if (userSetting?.openAbnormalCheck === openAbnormalCheck.开启 && !isSendAbnormal) {

		// 做发货异常校验
		event.emit('tradeList.send.abnormal', {
			afterPrint,
			orderList: list,
			type: 'SEND',
			isNoLogistics,
			noLogisticsType,
			noLogisticsParams,
			isPreShip,
			togetherId,
			callFrom,
		});
		// 扫描打印按逻辑不会走到这里
		customLogPost(type, '做发货异常校验', { sendDeliveryParams });
		return;
	}

	console.log('要发货的list:', list);

	if (type === 'scanPrint' || type === 'scanPrintKdd') {
		let canSend = false;
		list.forEach(pack => {
			pack.trades.forEach(trade => {
				trade.orders.forEach(order => {
					if (order.firstSend) order.isChecked = false;
					if (order.isChecked) canSend = true;
				});
			});
		});

		if (!canSend) {
			console.log('全部是先发货订单，不再发货');
			// playAudio(voice发货失败);
			pageLoading.destroy();
			// * 打印时已经updateInfoStatus过了 先发货这里不再updateInfoStatus了
			// const { id } = scanPrintStore.searchScanPrintFormSetting;
			// if (list[0].labelIds && list[0].labelIds.includes(id)) {
			// 	scanPrintStore.updateInfoStatus();
			// }
			customLogPost(type, '全部是先发货订单，不再发货', { sendDeliveryParams });
			return;
		}
	}

	let { params } = await gatherShipInfo(list, {
		isMergePrint,
		template,
		templateGroup,
		type,
		optionType,
		hasRefundIds
	});

	if (!params.length && type !== 'scanPrint') {

		message.warning('请选择需要发货的订单');
		// playAudio(voice发货失败);
		customLogPost(type, '请选择需要发货的订单 gatherShipInfo', {
			isMergePrint,
			template,
			templateGroup,
			type,
			optionType
		});
		return;
	}

	console.log(params, 'params');

	const res = handleAbrShipInfo(list, isNoLogistics, type);
	console.log(res, 'handleAbrShipInfo');

	if (!res) {
		return;
	}

	const {
		noSelecteOrder,
		hasRefund,
		hasUnpaid,
		unpaidWW,
		notPrint,
		noPreShip,
		splitTrade,
		noSendAddGoods,
	} = res;

	if (isPreShip && !preShipCheck(noPreShip, splitTrade, sendDeliveryParams)) {
		// playAudio(voice发货失败);
		customLogPost(type, '预发货异常检测 preShipCheck error', {
			isMergePrint,
			type,
		});
		return;
	}

	if (noSelecteOrder.length && type !== 'scanPrint' && type !== 'inspectSend') {
		message.warning(`[${noSelecteOrder.join('],[')}]未勾选要发货的宝贝，请勾选后发货`);
		// playAudio(voice发货失败);
		customLogPost(type, '未勾选要发货的宝贝，请勾选后发货', {
			noSelecteOrder,
			type,
		});
		return;
	}

	const onCheckedDefault = (checked:any) => {
		multiPackageDelivery = checked;
		localStorage.setItem('multiPackageDelivery', checked);
	};

	const shipFunc = async() => {

		if (!list.length) {
			Modal.confirm({
				centered: true,
				content: '当前没有可发货的订单，请重新勾选'
			});
			customLogPost(type, '当前没有可发货的订单，请重新勾选', {
				type,
			});
			return;
		}

		let templateName = isMergePrint ? templateGroup.groupName : template.ExcodeName;
		let totalTrade = 0;
		params.forEach(item => {
			totalTrade += item.subTrades.length;
		});

		console.log('%c [ shipFunc ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', totalTrade, params, templateName, type);

		if (['inspectSend', 'weighSend', 'scanPrint', 'scanPrintKdd', 'refundLabel', 'postPrintSend'].includes(type)) {
			tradeStore.tradeOptStore.shipFuncOnOk({
				params,
				totalTrade,
				templateName,
				type,
				inspectSendCallback
			});
		} else {
			let defaultSendType = SendType.自己联系;
			if (isNoLogistics) {
				defaultSendType = SendType.无需物流发货;
				if (noLogisticsType) {
					defaultSendType = noLogisticsType;
				}
			} else if (isPreShip) {
				defaultSendType = SendType.自动发货;
			}
			let sendType = defaultSendType;

			let isShowMultiPackageDelivery = true;
			if (isNoLogistics || isPreShip) {
				isShowMultiPackageDelivery = false;
			// eslint-disable-next-line
			} else if (list.every(pack => ![PLAT_TB, PLAT_PDD, PLAT_ALI, PLAT_FXG, PLAT_JD, PLAT_KS, PLAT_C2M,PLAT_YZ, PLAT_KTT].includes(pack.platform) && !["HAND", "SCMHAND"].includes(pack.source))) {
				isShowMultiPackageDelivery = false;
			}
			console.log('isShowMultiPackageDelivery', isShowMultiPackageDelivery);

			// 快手新疆中转订单不需要多包裹发货按钮
			let ksxjzz = list.every(pack => {
				const ksConsolidateType = pack.platform == PLAT_KS && KsConsolidateType[pack.consolidateType];
				return !!ksConsolidateType;
			});
			if (ksxjzz) {
				isShowMultiPackageDelivery = false;
			}


			const SendTypeMap = {};
			Object.keys(SendType).forEach(key => { SendTypeMap[SendType[key]] = key; });

			const handleOk = () => {
				// 多包裹 传参
				params.forEach(item => item.subTrades.forEach(t => {
					if ([SendType.无需物流发货, SendType.买家自提, SendType.卖家配送].includes(sendType)) {
						t.waybillNoSource = WaybillNoSource.无需物流;
						if (noLogisticsParams?.exNumber && noLogisticsParams?.exName) {
							t.exNumber = noLogisticsParams.exNumber;
							t.exName = noLogisticsParams.exName;
						}
					}
					if (isMergePrint ? +templateGroup?.id === -901 : +template?.Exid === -901) {
						t.waybillNoSource = WaybillNoSource.导入发货;
					}
					if (multiPackageDelivery && sendType == SendType.自己联系
						&& (
							[PLAT_TB, PLAT_PDD, PLAT_ALI, PLAT_FXG, PLAT_JD, PLAT_KS, PLAT_C2M, PLAT_YZ, PLAT_KTT].includes(t.platform)
							// 快手新疆中转订单不需要多包裹发货
							// eslint-disable-next-line
							&& !(t.platform == PLAT_KS && KsConsolidateType[t.consolidateType])
							// eslint-disable-next-line
							|| ["HAND", "SCMHAND"].includes(t.source)
						)
					) {
						t.multiPack = true;
					} else {
						delete t.multiPack;
						delete t.exNumberList;
					}
				}));
				console.log("shipFuncOnOk:::", params);
				tradeStore.tradeOptStore.shipFuncOnOk({
					params,
					totalTrade,
					templateName,
					sendType,
					shipFinishFn,
					afterPrint,
					inspectSendCallback
				});
			};

			let isMultiPack = params[0].subTrades[0].exNumberList?.length > 1; // 是否手动填了多包裹
			if (isSourceHandTrade && callFrom == 'directInputYdNo') {
				if (isMultiPack) {
					multiPackageDelivery = true;
				}

				handleOk();
				return;
			}

			if (!isSourceHandTrade && !isSourceScmTrade && callFrom == 'directInputYdNo') {
				if (!isMultiPack) {
					handleOk();
					return;
				}
			}

			Modal.confirm({
				centered: true,
				title: '请设置发货方式?',
				content: (
					<>
						<div className={ s["send-modal-item"] } style={ { marginTop: 20 } }>
							<div className={ s["send-modal-item-label"] }>订单统计：</div>
							<div className={ s["send-modal-item-text"] }>共 {list.length} 行，共 {totalTrade} 个订单</div>
						</div>
						<div className={ s["send-modal-item"] }>
							<div className={ s["send-modal-item-label"] }>快递模版：</div>
							<div className={ s["send-modal-item-text"] }>{templateName || '无'}</div>
						</div>
						<div className={ s["send-modal-item"] }>
							<div className={ s["send-modal-item-label"] }>发货方式：</div>
							<Select
								defaultValue={ defaultSendType }
								onChange={ (value) => {
									sendType = value;
								} }
							>
								{isNoLogistics && (
									<Select.Option
										value={ noLogisticsType || SendType.无需物流发货 }
									>
										{noLogisticsType ? SendTypeMap[noLogisticsType] : '无需物流发货'}
									</Select.Option>
								)}
								{isPreShip && <Select.Option value={ SendType.自动发货 }>自动发货</Select.Option>}
								{!isNoLogistics && !isPreShip && <Select.Option value={ SendType.自己联系 }>自己联系</Select.Option>}
							</Select>
						</div>
						{
							isShowMultiPackageDelivery ? (
								<div>
									<Checkbox
										onChange={ (e) => {
											onCheckedDefault(e.target.checked);
										} }
										defaultChecked={ multiPackageDelivery }
										style={ { marginLeft: '85px' } }
										disabled={ !setting?.printSetExpandDTO?.multiplePackageDelivery }
									>多包裹发货
									</Checkbox>
									<Popover
										content={ (
											<>
												<div>1、订单可获取多个单号进行多包裹发货，支持平台：淘宝、拼多多、抖店、快手、京东、淘工厂、1688、有赞、快团团、手工单平台。</div>
												<div>
													<div>2、部分平台多包裹发货限制说明：</div>
													<div>【抖店】：包裹数不可超过“订单商品总数+子订单数*(≤10)”</div>
													<div>【快手】：部分商品发货时，包裹数不可超过订单商品数量；全部商品发货时，订单总包裹数不可超过20</div>
													<div>【1688】：包裹数不可超过子订单数</div>
												</div>
												<div>3、使用多包裹发货，单号更改时必须用相同数量的单号。</div>
												<div style={ { color: "#f00" } }>请前往订单打印-高级设置-个性化设置中开启。</div>
											</>
										) }
										title={ null }
									>
										<span >
											<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
										</span>
									</Popover>
								</div>
							) : null
						}

						{isPreShip && (
							<div className="r-c-error">
								<div>注意：1、选择“自动发货”，订单加入自动发货列表</div>
								<div style={ { marginLeft: 42 } }> 2、自动发货不是真实发货，请前往自动发货页面进行真实发货</div>
							</div>
						)}
					</>
				),
				onOk: () => {
					handleOk();
				}
			});
		}
	};

	const unPromiseLogistics = () => {
		let unPromiseLogisticsInfo = getUnPromiseLogisticsOrder(list, template);
		const nextFn = async() => {
			// 目前仅供应商账户并没开异常校验开关需做接口异常校验
			if ((userStore.isSupplierAccount || userStore.isFreeSupplierAccount) && userSetting?.openAbnormalCheck === openAbnormalCheck.不开启) {
				try {
					console.log('typetypetype', type);
					list = await checkDistributionToFilter(list, type == 'scanPrint' ? AbnormalFromType.发货 : undefined);

					shipFunc();
				} catch (error) {
					console.log('error: ', error);
					customLogPost(type, '所选快递与指定快递不符，继续发货失败', { error });
				}
			} else {

				shipFunc();
			}
		};
		if (!isSendAbnormal && unPromiseLogisticsInfo.num > 0) {
			Modal.confirm({
				centered: true,
				width: 600,
				title: '提示',
				content: <>【{unPromiseLogisticsInfo.togetherIds.join(',')}】订单<span style={ { color: '#ff3333' } }>所选快递与指定快递不符</span>，请确认是否继续进行发货操作?</>,
				okText: '继续发货',
				cancelText: '取消',
				onOk: () => {
					nextFn();
					customLogPost(type, '所选快递与指定快递不符，继续发货', { unPromiseLogisticsInfo });
				},
				onCancel: () => {
					customLogPost(type, '所选快递与指定快递不符，取消继续发货', { unPromiseLogisticsInfo });
				}
			});
		} else {
			nextFn();
		}
	};

	const stockAllotFunc = async() => {
		try {
			pageLoading.loading();
			const info = await getOrderAllotStock(list, { type });
			pageLoading.destroy();
			console.log('getOrderAllotStock', info);
			if (info?.num) {
				if (["refundLabel", 'postPrintSend'].includes(type)) {
					pageLoading.destroy();
				}
				sendDeliveryOnAbnormal({ type });
				const renderModalContent = () => (
					<>
						<div>以下 {info.num} 笔 订单库存不足，请确认是否强制发货</div>
						{info.togetherIds.map(item => (
							<div key={ item }>{item}</div>
						))}
					</>
				);
				Modal.confirm({
					centered: true,
					width: 600,
					title: '库存不足提示',
					content: renderModalContent(),
					okText: (
						<Popover className={ s['force-ship-tips'] } placement="top" content="强制发货后您可能会超卖，请谨慎操作">
							<span>强制发货</span>
						</Popover>
					),
					onOk: () => {
						sendDeliveryOnAbnormalContinue({ type });
						unPromiseLogistics();
						customLogPost(type, '库存不足提示，强制发货', { info });
					},
					onCancel: () => {
						sendDeliveryOnAbnormalCancel({ type });
						callback && callback({
							success: false,
							type: 'stockCheckCancel',
							result: info.tids.map(i => ({ tid: i }))
						});
						customLogPost(type, '库存不足，取消强制发货', { info });
					}
				});
			} else {

				unPromiseLogistics();
			}
		} catch (error) {
			pageLoading.destroy();
			customLogPost(type, '发货前库存检测 stockAllotFunc error', { error });
		}

	};

	const stockNotAllotFunc = async() => {
		try {

			pageLoading.loading();
			const stockWarnArr = await getOrderStock(list, { type });
			pageLoading.destroy();
			console.log('stockWarnArr', stockWarnArr);
			if (stockWarnArr.length) {
				const renderModalContent = () => (
					<>
						<div>以下货品库存不足</div>
						{stockWarnArr.map(item => (
							<div key={ item.sysSkuId }>
								【{item.titleShort} {item.skuPropertiesName}】需要
								<span className="r-c-error">{item.num}</span>，目前库存
								<span className="r-c-error">{item.stockNum}</span>
							</div>
						))}
					</>
				);
				sendDeliveryOnAbnormal({ type });
				Modal.confirm({
					centered: true,
					width: 600,
					title: '系统提示',
					content: renderModalContent(),
					okText: (
						<Popover className={ s['force-ship-tips'] } placement="top" content="强制发货后您可能会超卖，请谨慎操作">
							<span>强制发货</span>
						</Popover>
					),
					onOk: () => {
						sendDeliveryOnAbnormalContinue({ type });
						unPromiseLogistics();
						customLogPost(type, '货品库存不足，强制发货', { stockWarnArr });
					},
					onCancel: () => {
						sendDeliveryOnAbnormalCancel({ type });
						callback && callback({
							success: false,
							type: 'stockCheckCancel',
							result: stockWarnArr
						});
						customLogPost(type, '货品库存不足，取消强制发货', { stockWarnArr });
					}
				});
			} else {
				unPromiseLogistics();
			}
		} catch (error) {
			pageLoading.destroy();
			customLogPost(type, '货品库存检测 stockNotAllotFunc error', { error });
		}

	};

	const stockFunc = () => {

		// 如果是验货发货，不管设置中有没有开启异常校验的开关，都要走库存不足的校验逻辑（除非是零库存或者未开启扣库存）
		if (["inspectSend", "weighSend"].includes(type)) {
			if (userStore?.isStockAllocationVersion) {

				stockAllotFunc();
			} else if (userStore?.userInfo?.version !== 1 || userStore?.inventoryDeduct === 0 || userSetting?.openAbnormalCheck === openAbnormalCheck.开启 || afterPrint) {
				unPromiseLogistics();
			} else {
				stockNotAllotFunc();
			}
			return;
		}
		if (userStore?.userInfo?.version !== 1 || userStore?.inventoryDeduct === 0 || userSetting?.openAbnormalCheck === openAbnormalCheck.开启 || afterPrint) {


			unPromiseLogistics(); // 所选快递与指定快递不符
			return;
		}
		// 库存校验不足，占库存，不占库存
		if (userStore?.isStockAllocationVersion) {

			stockAllotFunc();
		} else {

			stockNotAllotFunc();
		}
	};

	const unPrintFunc = () => {
		const notShowConfirmTypeList = ['refundLabel', 'scanPrint', 'postPrintSend'];

		// 如果有未打印的，提示下，无需物流发货不提示，上面已经提示过电子面单未打印   直接手写单号弹窗发货过来的手工单和平台单不需要提示
		if (notPrint.length && template.exCode !== 'NONE' && !isNoLogistics && !notShowConfirmTypeList.includes(type) && !(!isSourceScmTrade && callFrom == 'directInputYdNo')) {
			sendDeliveryOnAbnormal({ type });
			Modal.confirm({
				centered: true,
				title: '提示',
				content: (
					<span style={ { fontSize: 16, lineHeight: 1.5 } }>
						您选择的订单含有<span className="r-c-error">未打印</span>的宝贝，请您仔细核对是否继续发货
					</span>
				),
				okText: '继续发货',
				onOk: () => {

					sendDeliveryOnAbnormalContinue({ type });
					stockFunc();
					customLogPost(type, '订单含有未打印的宝贝，继续发货', { notPrint });
				},
				onCancel: () => {
					sendDeliveryOnAbnormalCancel({ type });
					customLogPost(type, '订单含有未打印的宝贝，取消发货', { notPrint });
				}
			});
		} else {

			stockFunc();
		}
	};

	const unPaidFunc = () => {
		if (hasUnpaid && type !== 'scanPrint') {
			sendDeliveryOnAbnormal({ type });
			Modal.confirm({
				centered: true,
				title: '提示',
				content: (
					<span style={ { fontSize: 14, lineHeight: 1.5 } }>
						【{unpaidWW.join(',')}】的订单存在
						<span className="r-c-error r-bold">尚未付款</span>
						的宝贝，请您仔细核对是否继续发货
					</span>
				),
				okText: '继续发货',
				onOk: () => {

					sendDeliveryOnAbnormalContinue({ type });
					unPrintFunc();
					customLogPost(type, '订单含有尚未付款的宝贝，继续发货', { hasUnpaid });
				},
				onCancel: () => {
					sendDeliveryOnAbnormalCancel({ type });
					customLogPost(type, '订单含有尚未付款的宝贝，取消发货', { hasUnpaid });
				}
			});
		} else {

			unPrintFunc();
		}
	};

	const hasNoSendAddGoods = () => {
		// 没开启异常检测时提示
		if (noSendAddGoods.length > 0 && userSetting?.openAbnormalCheck !== openAbnormalCheck.开启) {
			sendDeliveryOnAbnormal({ type });
			Modal.confirm({
				centered: true,
				title: '系统提醒',
				bodyStyle: { padding: "20px 24px" },
				content: (
					<div style={ { maxHeight: 600, overflowY: 'auto' } }>
						<p style={ { color: "#777" } }>所选订单存在【
							<span style={ { color: "#f00" } }>有待发货的系统商品</span>
							】的订单，是否继续发货？
						</p>
						{/* <div className="r-mt-10">{content}</div> */}
					</div>
				),
				okText: '继续发货',
				cancelText: '取消',
				onOk: () => {
					sendDeliveryOnAbnormalContinue({ type });
					unPaidFunc();
					customLogPost(type, '订单含有有待发货的系统商品，继续发货', { noSendAddGoods });
				},
				onCancel: () => {
					sendDeliveryOnAbnormalCancel({ type });
					customLogPost(type, '有待发货的系统商品，取消继续发货', { noSendAddGoods });
				}
			});
			return;
		} else {
			unPaidFunc();
		}
	};

	if (hasRefund && type !== 'scanPrint') {
		Modal.confirm({
			centered: true,
			title: '提示',
			content: '所选订单存在退款中的订单，是否继续发货？',
			okText: '继续发货',
			onOk: () => {
				hasNoSendAddGoods();
				customLogPost(type, '所选订单存在退款中的订单，继续发货', { hasRefund });
			},
			onCancel(...args) {
				customLogPost(type, '所选订单存在退款中的订单，取消发货', { hasRefund });
			},
		});
	} else {
		hasNoSendAddGoods();
	}
};
// 通过type获取对应的快递模板、快递模板组、数据列表
const getCurTempAndList = (type, scanPrintList, scanPrintTemplateInfo) => {
	const {
		isMergePrint,
		kddTempList,
		selectedTemp,
		selectedTempGroup,
		tradeListStore: {
			list
		}
	} = tradeStore;

	let template: IKddTemp = {};
	let templateGroup: IKddTempGroup = {};
	let allList: IPackage[];
	// 如果是扫描打印
	if (type === 'scanPrint') {
		const { scanPrintFormSetting } = scanPrintStore;
		let _template = kddTempList.find(item => (!isMergePrint ? item.Mode_ListShowId : item.id) == scanPrintFormSetting.templateInfo);
		if (isMergePrint) {
			templateGroup = _template;
		} else {
			template = _template;
		}
		allList = scanPrintList;

		if (!_template) {
			customLogPost(type, '**扫描打印发货模板没有获取到', {
				isMergePrint,
				templateInfo: scanPrintFormSetting.templateInfo,
				kddTempList: toJS(kddTempList)
			});

			if (scanPrintTemplateInfo) {
				// 使用传递的扫描打印模板信息
				if (scanPrintTemplateInfo.isMergePrint) {
					// 融合打印模式：尝试在kddTempList中找到对应的模板组
					templateGroup = kddTempList.find(item => item.id == scanPrintTemplateInfo.id);
				} else {
					// 普通模板模式：尝试在kddTempList中找到对应的模板
					template = kddTempList.find(item => item.Mode_ListShowId == scanPrintTemplateInfo.id);
				}
				customLogPost(type, '**扫描打印发货模板从传参获取', {
					template,
					templateGroup,
					scanPrintTemplateInfo,
				});
			}
		} 
		
		// 如果是退款标签重匹配、扫描发货
	} else if (["refundLabel", 'inspectSend', 'weighSend', 'postPrintSend'].includes(type)) {
		allList = scanPrintList;
	} else {
		template = selectedTemp;
		templateGroup = selectedTempGroup;
		allList = list;
	}
	return {
		template, templateGroup, allList
	};
};

const preShipCheck = (noPreShip: string[], splitTrade: string[], sendDeliveryParams: ISendDelivery) => {
	let { tradeListStore: { handleChoiceChange } } = tradeStore;
	const noPreShipModal = () => {
		handleChoiceChange({
			type: TradeChoiceType.取消勾选指定订单,
			cancelChoiceOrders: noPreShip,
		});
		Modal.confirm({
			centered: true,
			title: '提示',
			content: (
				<>
					<div>勾选订单中包含不是<span className="r-c-error">待发货或待付款</span>订单，不支持自动发货</div>
					<div>系统可跳过不符订单发货，是否允许?</div>
				</>
			),
			okText: '跳过异常继续发货',
			onOk: () => {
				sendDelivery(sendDeliveryParams);
				customLogPost('', '预发货异常检测: 勾选订单中包含不是待发货或待付款订单,跳过异常继续发货', {
					noPreShip
				});
			},
			onCancel(...args) {
				customLogPost('', '预发货异常检测: 勾选订单中包含不是待发货或待付款订单,取消继续发货', {
					noPreShip
				});
			},
		});
	};
	const splitTradeModal = () => {
		handleChoiceChange({
			type: TradeChoiceType.取消勾选指定订单,
			cancelChoiceOrders: splitTrade,
		});
		Modal.confirm({
			centered: true,
			title: '提示',
			content: (
				<>
					<div>亲,系统检测到您勾选的订单发生异常,将中断发货</div>
					<div className="r-c-error">{splitTrade.length}笔订单勾选部分商品不支持自动发货</div>
					<div>系统可跳过不符订单发货，是否允许?</div>
				</>
			),
			okText: '跳过异常继续发货',
			onOk: () => {
				sendDelivery(sendDeliveryParams);
				customLogPost('', '预发货异常检测: 勾选部分商品不支持自动发货,跳过异常继续发货', {
					splitTrade
				});
			},
			onCancel(...args) {
				customLogPost('', '预发货异常检测: 勾选部分商品不支持自动发货,取消继续发货', {
					splitTrade
				});
			},
		});
	};
	if (noPreShip.length) {
		noPreShipModal();
		return false;
	} else if (splitTrade.length) {
		splitTradeModal();
		return false;
	} else {
		return true;
	}
};


// 发货异常校验异常收集（用作单个异常提示）
const handleAbrShipInfo = (list: IPackage[], isNoLogistics = false, type: ISendDelivery['type']) => {
	let hasPendingTrade = list.filter(item => item.isPending);
	if (hasPendingTrade.length && type !== 'scanPrint') {
		Modal.confirm({
			centered: true,
			title: '',
			content: (
				<div style={ { maxHeight: 600, overflowY: 'auto' } }>
					<p className="r-c-warning" >以下订单不可打印，请取消挂起后再试</p>
					{hasPendingTrade.map(item => (
						<p key={ item.togetherId }>{item.togetherId}</p>
					))}
				</div>
			),
			...ABNORMAL_MODAL_BUTTON_PROPS,
		});
		customLogPost(type, '以下订单不可打印，请取消挂起后再试', {
			hasPendingTrade,
			type
		});
		return false;
	}

	// 检查顺丰加运费选择的模板不是顺丰
	// const hasSfExpressOrder = list.filter((item) => item.hasSfExpressService);
	// const { selectedTempGroup, selectedTemp, isMergePrint } = tradeStore;
	// const ExCode = isMergePrint ? selectedTempGroup.exCode : selectedTemp.ExCode;
	// if (hasSfExpressOrder?.length && ExCode !== 'SF' && type !== 'scanPrint') {
	// 	Modal.confirm({
	// 		centered: true,
	// 		title: '订单打印异常提醒',
	// 		content: (
	// 			<div style={ { maxHeight: 600, overflowY: 'auto' } }>
	// 				<p className="r-c-warning" >以下订单不可发货，该订单买家申请加运费发顺丰订单，请使用顺丰物流打印发货。</p>
	// 				{hasSfExpressOrder.map(item => (
	// 					<p key={ item.togetherId }>{item.togetherId}</p>
	// 				))}
	// 			</div>
	// 		),
	// 		...ABNORMAL_MODAL_BUTTON_PROPS,
	// 	});
	// 	customLogPost(type, '以下订单不可发货，该订单买家申请加运费发顺丰订单，请使用顺丰物流打印发货', {
	// 		hasSfExpressOrder,
	// 		isMergePrint,
	// 		ExCode,
	// 		type
	// 	});
	// 	return false;
	// }

	// * 整笔订单发货之后 shipStatus变为alreadyShipped 防止整笔重发
	let alreadyShippedList = list.filter(item => item.shipStatus === 'alreadyShipped');
	if (alreadyShippedList.length) {
		message.warning(`${alreadyShippedList.map(item => `[${item.togetherId}];`)}订单已发货，请确认后再操作。`);
		customLogPost(type, '整笔订单已发货，请确认后再操作', { list });
		return false;
	}

	if (list.filter(pack => ['打印后生成', '无需运单号', '获取失败', '', undefined].includes(pack.sids?.[0])).length && !isNoLogistics) {
		message.warning('所选择订单需要填写快递单号，请确认');
		customLogPost(type, '所选择订单需要填写快递单号，请确认', { list });
		return false;
	}

	let noSelecteOrder: string[] = [];
	let hasRefund = false;
	let hasUnpaid = false;
	let unpaidWW: string[] = [];
	let notPrint: string[] = [];
	let noPreShip: string[] = [];
	let splitTrade: string[] = [];
	let noSendAddGoods :string[] = [];

	list.forEach(pack => {
		let checkedOrder: IOrders[] = [];
		pack.trades.forEach(trade => {
			checkedOrder = checkedOrder.concat(trade.orders.filter(order => order.isChecked));
			if (checkedOrder.filter(order => order.refundStatus !== 'NOT_REFUND').length) {
				hasRefund = true;
			}
			if (trade.status === 'WAIT_BUYER_PAY') {
				hasUnpaid = true;
				unpaidWW.push(trade.buyerNick);
			}
			if (!(+trade.isPrintKdd)) {
				notPrint.push(trade.buyerNick);
			}
			if (trade.status !== TradeStatus.等待卖家发货 && trade.status !== TradeStatus.等待买家付款 && trade.refundStatus !== 'NOT_REFUND') {
				noPreShip.push(pack.togetherId);
			}

			let hasNoSendAddGoods = false; // 存在未勾选且未发货的系统商品
			let hasNoSendGoods = false; // 存在未勾选且未发货的平台商品
			trade?.orders?.forEach(order => {
				// 判断未勾选的商品数据中不存在待发货的平台商品但存在待发货的系统商品
				if (!order?.isChecked && order.status === "WAIT_SELLER_SEND_GOODS") {
					if ([SysExchangeTypeEnum.新增的商品, SysExchangeTypeEnum.新增的货品].includes(order.sysExchangeType)) {
						hasNoSendAddGoods = true;
					} else {
						hasNoSendGoods = true;
					}
				}

			});

			if (hasNoSendAddGoods && !hasNoSendGoods) {
				noSendAddGoods.push(pack.togetherId);
			}
		});

		if (pack.isSplitSend) {
			splitTrade.push(pack.togetherId);
		}

		if (!checkedOrder.length) {
			noSelecteOrder.push(pack.buyerNick);
		}
	});



	return {
		noSelecteOrder,
		hasRefund,
		hasUnpaid,
		unpaidWW: uniq(unpaidWW),
		notPrint: uniq(notPrint),
		noPreShip: uniq(noPreShip),
		splitTrade,
		noSendAddGoods: uniq(noSendAddGoods),
	};
};

interface IGatherTemplateInfo {
	exId?: number;
	exCode?: string;
	exName?: string;
	kddType?: number;
}

export interface IGatherShipInfo {
	isMergePrint: boolean;
	template: IKddTemp,
	templateGroup: IKddTempGroup,
	type?: ISendDelivery['type'],
	optionType?: TradeOptEnum,
	hasRefundIds?: boolean;
}

export const gatherShipInfo = async(list: IPackage[], info: IGatherShipInfo) => {
	const { setting } = tradeStore;

	const { isMergePrint, templateGroup = {}, template = {}, type, optionType, hasRefundIds } = info;
	console.log(list, info, 'gather');
	const advancedRes = await memoFn.getAdvancedSet();
	let isUsePlatTemp = advancedRes?.refluxOrderPrintSet == refluxOrderPrintSettingsEnum.使用对应平台面单打印;


	// 后期后端判断 在模板列表增加字段判断是否是菜鸟子母件
	// 暂未接入
	// let isCainiaoZmj = (template?.styleId == 22 || template?.styleId == 23);
	let subTrades: ISendSubTrades[] = [];
	let params: TradeBatchSendRequest = [];
	let gatherTemplateInfo: IGatherTemplateInfo = {};
	if (!isMergePrint) {
		gatherTemplateInfo = {
			exId: +template.Exid,
			exCode: template.ExCode,
			exName: template.ExcodeName,
			kddType: template.kddType,
		};
	}
	list.forEach(pack => {
		let exNumberList = [];
		if ([PLAT_KS].includes(pack.platform)) {
			exNumberList = pack.sids?.filter((i) => i !== '打印后生成').slice(0, 20);
		} else {
			exNumberList = pack.sids?.filter((i) => i !== '打印后生成').slice(0, 30);
		}
		subTrades = [];
		const { trades } = pack;
		if (!trades.length) return;
		if (isMergePrint && !['refundLabel', 'postPrintSend'].includes(type)) {
			let orderPLatSetting = setting?.groupPrintSetJsonString?.orderMatchSetting || [];
			let currentTemplateGroup: IKddTempGroup['userTemplateList'][number];
			// * 手动导入单号模板
			if (templateGroup.id == -901) {
				currentTemplateGroup = {
					exId: templateGroup.id + '',
					exCode: undefined,
					exName: templateGroup.groupName,
					expressType: undefined,
				};
			} else {
				currentTemplateGroup = templateGroup?.userTemplateList?.find(it => {
					if (dealPlatAsHandPlat(pack.platform, null, pack)) {
						let orderSettingType = orderPLatSetting.find(o => o.platform == transferOtherToHand(pack.platform, pack));
						if (orderSettingType?.bindControlType == "13") {
							return [13, 16].includes(it.expressType);
						} else {
							return it.expressType == orderSettingType?.bindControlType;
						}
					}
					// 1688回流订单，密文和选择为平台的明文
					if (pack.platform == PLAT_ALI && pack.hlPlatformType) {
						if (pack.hlEncryptOrder || (isUsePlatTemp && !pack.hlEncryptOrder && pack.hlPlatformType !== 'hl-sph')) {
							const hlPlat = HL_PLAT[pack.hlPlatformType];
							const printType = PRINT_MAP[hlPlat] || PRINT_MAP[PLAT_ALI];
							if (printType == "13") {
								return [13, 16].includes(it.expressType);
							} else {
								return it.expressType == printType;
							}
						}
					}
					if (PRINT_MAP[pack.platform] == "13") {
						return [13, 16].includes(it.expressType);
					} else {
						return it.expressType == PRINT_MAP[pack.platform];
					}
				});
				console.log('currentTemplateGroup::', currentTemplateGroup);
			}
			// }
			if (currentTemplateGroup) {
				gatherTemplateInfo = {
					exId: +currentTemplateGroup.exId,
					exCode: currentTemplateGroup.exCode,
					exName: currentTemplateGroup.exName,
					kddType: currentTemplateGroup.expressType
				};
			} else {
				console.error('模板组中没找到对应的模板', templateGroup, pack);
			}
		}
		if (["refundLabel", 'inspectSend', 'weighSend', 'postPrintSend'].includes(type)) { // 如果是扫描发货
			gatherTemplateInfo = {
				exId: pack.expressTemplate?.exId,
				exCode: pack.expressTemplate?.exCode,
				exName: pack.expressTemplate?.exName,
				kddType: pack.expressTemplate?.kddType
			};
		}
		const { exId, exCode, exName, kddType } = gatherTemplateInfo;
		console.log('exIdexId', exId);
		// https://tb.raycloud.com/task/677cd1e443c33040d12ada05 手写单号不计算重量 sb
		const isHandElo = exId == -901;
		trades.forEach(trade => {
			let weight = 0;
			let oids: string[] = [];
			let ptOids: string[] = [];
			let orders: ISendOrders[] = [];
			let isRefundReject: boolean = false;
			let oidLabelList: any[] = [];
			trade.orders.filter(order => order.isChecked).forEach(order => {
				if (order?.isForceScan) {
					const labelIdList = Array.from(order.scanPrintLabelId || []);
					oidLabelList.push({ oid: order.oid, labelIdList });
				}
				// 重量计算
				weight += parseFloat((+order.weight * (isHandElo ? 1 : order.num)) + '');
				oids.push(order.oid);
				ptOids.push(order.ptOid);
				orders.push({
					skuUuid: order.skuUuid,
					num: order.num,
					numIid: order.numIid,
					tid: order.tid,
					ptOid: order.ptOid,
					oid: order.oid,
					payment: order.payment,
					skuId: order.skuId,
					needSerialNumber: order.needSerialNumber,
					productIdCode: order.productIdCode,
					systemNumIid: order.systemNumIid,
					systemSkuId: order.systemSkuId,
					ignore: order.ignore || 0,
					gift: !!order.isGift,
					sysExchangeType: order.sysExchangeType
					// takeGoodsLabelIds: order.takeGoodsLabelIds,
				});
				if (order.refundStatus == "REFUND_ING" || hasRefundIds) {
					isRefundReject = true;
				}
				// if (order.refundStatus == "REFUND_ING") {
				// 	isRefundReject = true;
				// }
			});

			let split = oids.length !== trade.orders.length;
			// 如果是验货发货模式，则split会在trade中传入
			if (["inspectSend", 'weighSend'].includes(info.type)) {
				split = trade.splitSend;
			}
			if (orders.length) {
				subTrades.push({
					saleUserId: pack.distributorUserId,
					togetherId: pack.togetherId,
					// pYdNo: isCainiaoZmj ? pack.ydNo : '',
					pYoNo: '',
					split,
					cod: trade.isCod ? 1 : 0,
					sendType: '1',
					sellerId: trade.sellerId,
					platform: pack.platform,
					buyerNick: pack.buyerNick,
					buyerOpenUid: pack.buyerOpenUid,
					receiverProvince: pack.receiverState,
					receiverCity: pack.receiverCity,
					receiverCounty: pack.receiverDistrict,
					receiverAddress: pack.receiverAddress,
					receiverTown: pack.receiverTown,
					receiverName: pack.receiverName,
					mobile: pack.receiverMobile,
					tel: pack.receiverPhone,
					exId, // Exid,
					exCode: exCode || pack.sidsExCode?.[0], // ExCode,
					exName: pack.sidsExNames?.[0] || exName, // ExcodeName,
					sendTel: pack?.sendMobile?.[0], // 单号回显的发货人手机号尾号4位,
					kddType,
					exNumber: pack.sids?.[0],
					exNumberList,
					goodsNum: pack.totalOrderNum,
					oidLabelList: oidLabelList?.length ? oidLabelList : undefined, // 如果开启了强制缺货备齐
					weight: weight + '',
					oids: oids.join(','),
					tid: trade.tid,
					ptTid: trade.ptTid,
					ptOids: ptOids.join(','),
					buyerMsg: trade.buyerMessage,
					sellerMemo: trade.sellerMemo,
					sellerFlag: +trade.sellerMemoFlag,
					payment: trade.payment,
					info: pack.printContent,
					packageId: trade.packageId || '',
					optionType: 1,
					orders,
					orderCode: trade?.orderCode, // BIC订单码
					caid: pack.caid,
					overseaTracing: '',
					imei: '',
					deviceSn: '',
					// feature: '',
					idxEncodeReceiverMobile: pack.idxEncodeReceiverMobile,
					idxEncodeReceiverName: pack.idxEncodeReceiverName,
					idxEncodeReceiverAddress: pack.idxEncodeReceiverAddress,
					receiverNameMask: pack.receiverNameMask,
					receiverAddressMask: pack.receiverAddressMask,
					receiverPhoneMask: pack.receiverPhoneMask,
					receiverMobileMask: pack.receiverMobileMask,
					encodeTid: pack.encodeTid,
					isDecrypted: pack.isDecrypted,
					sellerFlagSys: trade.sellerFlagSys,
					waybillNoSource: WaybillNoSource.系统申请来源,
					labelIds: pack.labelIds,
					source: pack.source,
					ignoreType: trade.ignoreType,
					isRefundReject,
					splitTradeType: trade.splitTradeType,
				});
			}

		});
		// PD(0, "批量打印发货"),1
		// YFH_REAL_SEND(1, "预发货真正发货"),1
		// LABEL(2, "小标签扫描发货"),1
		// SCAN_TRADE_SEND(3, "后置打印发货"),1
		// BATCH_RESEND(4, "重新发货"),
		// NO_LOGISTICS_SEND(5, "无需物流发货"),1
		// TRADE_INSPECT_SEND(6, "验货发货"),1
		// AUTO_SEND(7, "自动发货"),
		// TRADE_REFUND_RESEND(8, "退款标签重发"),1
		// WEIGHT_SEND(9, "称重发货");1

		const typeMap = {
			'scanPrint': 'LABEL',
			'postPrintSend': 'SCAN_TRADE_SEND',
			'inspectSend': "TRADE_INSPECT_SEND",
			'yfhSend': 'YFH_REAL_SEND',
			'batchResend': 'BATCH_RESEND',
			'noLogisticsSend': 'NO_LOGISTICS_SEND',
			'autoSend': 'AUTO_SEND',
			'weighSend': 'WEIGHT_SEND',
			'refundLabel': 'TRADE_REFUND_RESEND',
		};

		params.push({
			subTrades,
			sendFrom: typeMap[type] ?? 'PD',
			// 扫描 验货 验单发货 需要前端传给后端让后端来区分来源便于加日志
			optionType: optionType || SendTypeOptEnum[type]
		});
	});

	return { params: [...params] };
};

enum SendTypeOptEnum {
	scanPrint = TradeOptEnum.扫描发货,
	inspectSend = TradeOptEnum.验货发货, // 验货发货
}

const gatherStockInfo = (list: IPackage[], info?: IGatherShipInfo) => {
	let checkStockObj: Record<string, ICheckStockItem> = {};
	list.forEach(pack => {
		pack?.trades?.forEach(trade => {
			let _isCheckStock = isCheckStock(trade); // 是否校验库存
			_isCheckStock && trade.orders.filter(order => order.isChecked).forEach(order => {
				if (!(info?.type === 'scanPrint' && order.labelPrintStatus) && !order.ignore) {
					let sid = null;
					if (!(SID_STATUS.includes(order.sids?.[0]))) {
						sid = order.sids?.[0];
					}
					let ssId = order.systemSkuId;
					if (!checkStockObj[ssId]) {
						checkStockObj[ssId] = {};
						checkStockObj[ssId].togetherIds = [];
						checkStockObj[ssId].sids = [];
						checkStockObj[ssId].tid = order.tid;
						checkStockObj[ssId].num = +order.num;
						checkStockObj[ssId].sysSkuId = order.systemSkuId;
						checkStockObj[ssId].sysItemId = order.systemNumIid;
						checkStockObj[ssId].titleShort = order.titleShort;
						checkStockObj[ssId].skuPropertiesName = order.skuPropertiesName;
						checkStockObj[ssId].groupRelationRecordList = order.groupRelationRecordList;
					} else {
						checkStockObj[ssId].num += +order.num;
					}
					checkStockObj[ssId].togetherIds.push(pack.togetherId);
					checkStockObj[ssId]?.sids.push(sid);
				}
			});
		});
	});
	return checkStockObj;
};

const gatherTradeStockInfo = (list: IPackage[], info?: IGatherShipInfo) => {
	if (info?.type === "inspectSend") { // 验货发货暂时不走库存校验
		return [];
	}
	return list.filter(pack => {
		return pack?.trades?.some(trade => {
			let _isCheckStock = isCheckStock(trade); // 是否校验库存
			let checkOrder = trade.orders.filter(order => order.isChecked).some(order => {
				return !(info?.type === 'scanPrint' && order.labelPrintStatus) && !order.ignore;
			});
			return _isCheckStock && checkOrder;
		});
	});
};

// 是否校验库存
export const isCheckStock = (trades:ISubTrade) => {
	let systemSetting = userStore.systemSetting || {};
	let flag = systemSetting?.flagTrade || '00000'; // 红黄绿蓝紫 1设置 0未设置
	let flagaArr = flag.split('');
	let mark = systemSetting?.markTrade || '00000'; // 红黄绿蓝紫
	let markMap = {
		red: mark.charAt(0),
		yellow: mark.charAt(1),
		green: mark.charAt(2),
		blue: mark.charAt(3),
		purple: mark.charAt(4)
	};
	let isCheckStock = true;
	if (systemSetting?.inventoryDeduct === 1) {
		// 963812674834006016
		// 旗帜
		if (flag !== '00000' && trades._sellerMemoFlag != '0') {
			if (flagaArr[+(trades._sellerMemoFlag) - 1] == '1') {
				isCheckStock = false;
				// console.log('旗帜', isCheckStock);
			}
		}
		// 标记
		if (mark !== '00000' && isCheckStock && trades.sellerFlagSys) {
			let sellerFlagSys = trades.sellerFlagSys;
			for (let k in sellerFlagSys) {
				if (markMap[k] == '1' && sellerFlagSys[k] > 0) {
					isCheckStock = false;
					// console.log('标记', isCheckStock);
				}
			}
		}
		// 空单
		if (isCheckStock && [IgnoreStatus.自动标记, IgnoreStatus.手动标记].includes(trades.ignoreType)) {
			isCheckStock = false;
		}

		// 包材且包材无需管理库存
		if (isCheckStock && trades.orders) {
			const hasPackageMaterial = trades.orders.every(order => order.isChecked && order.sysProperty === 2 
                && systemSetting?.sysItempropertyManageStockFlag === 1);
            
			if (hasPackageMaterial) {
				isCheckStock = false;
			}
		}
	}
	return isCheckStock;
};


let shipModal: any = null;
const shipFinishFn = (data: {
	showMessage: {
		tid: string,
		buyerNick: string,
		message: string,
	}[], totalSend: number, totalTrade: number,
}) => {
	const { showMessage, totalSend, totalTrade } = data;
	console.log('shipFinishFn', data);
	const copyNode = <p className="g-c-blue r-pointer" onClick={ () => copyToPaste(splitFxgTid(showMessage.map(i => i.tid).join(','))) }>复制订单编号</p>;
	let config = {
		centered: true,
		title: '发货进度',
		content: (
			<div className="batchShipProcess">
				{showMessage.length ? (
					<div className="batch-note-load">
						<div style={ { height: 120, overflowY: 'auto' } }>
							{showMessage.map(item => (
								<p key={ item.message } style={ { fontSize: '14px' } }>{item.buyerNick}({item.tid})： <span className="r-c-error">{item.message}</span></p>
							))}
						</div>
					</div>
				) : ''}

				{
					totalSend === totalTrade && showMessage.length > 0 && (
						<>
							{
								showMessage.length !== totalTrade && (
									<div className="batch-end-someerr batch-end" style={ { display: 'block', marginTop: '30px' } }>
										<p className="batch-fh-text">部分订单已发货</p>
										<p className="batch-part-order">
											<span className="r-c-error">
												{showMessage.length}
											</span>
											个订单未发货成功。
										</p>
										{ copyNode }
									</div>
								)
							}
							{
								showMessage.length === totalTrade && (
									<div className="batch-end-allerr batch-end" style={ { marginTop: '30px' } }>
										<p className="batch-fh-text">发货失败</p>
										<p className="batch-part-order">
											<span className="r-c-error">
												所有订单均未能发货，请核对后再试
											</span>
										</p>
										{ copyNode }
									</div>
								)
							}
						</>
					)
				}
				{
					showMessage.length === 0 && (
						<>
							<div className="batch-ing batch-end" style={ { marginTop: '30px' } }>
								<p className="batch-fh-text">
									<span>
										已发
										<em className="greenText">{totalSend}</em>
										单/共
										<em className="greenText">{totalTrade}</em>
										单
									</span>
								</p>
							</div>
						</>
					)
				}
			</div>
		),
		onOk: () => {
			shipModal = null;
		},
		onCancel: () => {
			shipModal = null;
		}
	};
	if (shipModal) {
		shipModal.update(config);
	} else {
		shipModal = Modal.confirm(config);
	}
};


export const stockCheckFn = (res: TradeQueryStockInfoResponse['data'], checkStockObj: Record<string, ICheckStockItem>) => {
	let stockWarnArr: ICheckStockItem[] = [];
	console.log('stockCheckFn:', res, checkStockObj);
	res.forEach(item => {
		let curStockObj = checkStockObj[item.sysSkuId];
		const num = Number(curStockObj.num);
		if (item.isCombination) {
			// 组合商品校验
			if (userStore.systemSetting.groupStockNumOutWay === 0 && num > item.salableItemStock) {
				item.groupRelationRecordList.forEach(e => {
					// if (stockWarnArr.findIndex(i => i.sysSkuId === e.sysSkuId && i.groupRelationRecordList?.length) > -1) {
					// 	return;
					// }
					let currentGroupGoodsNum = 0;
					// * 加上子商品数量
					currentGroupGoodsNum += checkStockObj[e.sysSkuId]?.num || 0;
					let togetherIds = [];
					// * 找出其他组合商品中相同的子商品
					for (const key in checkStockObj) {
						if (Object.prototype.hasOwnProperty.call(checkStockObj, key)) {
							const element = checkStockObj[key];

							// * 该商品的库存信息
							// let tempStockInfo = res.find(i => i.sysSkuId === element.sysSkuId);
							// * 该组合商品是否包含子商品
							let index = element.groupRelationRecordList?.findIndex(l => l.sysSkuId == e.sysSkuId);
							if (index > -1) {
								// let num = (+element.num - +tempStockInfo.salableItemStock) * +element.groupRelationRecordList.find(i => +i.sysSkuId === +e.sysSkuId).groupProportionNum;
								let num = (+element.num) * +element.groupRelationRecordList.find(i => i.sysSkuId == e.sysSkuId).groupProportionNum;
								currentGroupGoodsNum += num;
								if (element.togetherIds) {
									togetherIds.push(...element.togetherIds);
								}
							}
						}
					}
					if (e.salableItemStock < Number(currentGroupGoodsNum)) {
						stockWarnArr.push({
							sysItemId: e.sysItemId,
							sysSkuId: e.sysSkuId,
							stockNum: e.salableItemStock,
							num: currentGroupGoodsNum,
							togetherIds: [...(curStockObj.togetherIds || []), ...togetherIds],
							titleShort: e.sysItemAlias || e.sysItemName,
							skuPropertiesName: e.sysSkuName,
							groupRelationRecordList: item.groupRelationRecordList,
							parentSkuId: item.sysSkuId,
						});
					}
				});
			} else if (userStore.systemSetting.groupStockNumOutWay === 1 && num > item.salableItemStock) {
				stockWarnArr.push({
					tid: curStockObj.tid,
					sysItemId: item.sysItemId,
					sysSkuId: item.sysSkuId,
					stockNum: item.salableItemStock,
					num,
					togetherIds: curStockObj.togetherIds,
					titleShort: curStockObj.titleShort,
					skuPropertiesName: curStockObj.skuPropertiesName,
				});
			}
		} else if (num > item.salableItemStock) {
			// 非组合商品校验
			stockWarnArr.push({
				sysItemId: item.sysItemId,
				sysSkuId: item.sysSkuId,
				stockNum: item.salableItemStock,
				tid: curStockObj.tid,
				num,
				togetherIds: curStockObj.togetherIds,
				titleShort: curStockObj.titleShort,
				skuPropertiesName: curStockObj.skuPropertiesName,
			});
		}
	});

	console.log(stockWarnArr, 'stockWarnArr');
	stockWarnArr.forEach((stock, index) => {
		if (checkStockObj[stock.sysSkuId]) {
			stockWarnArr[index].togetherIds?.push(...(checkStockObj?.[stock.sysSkuId]?.togetherIds || []));
		}
	});

	let stockWarnObj = {};
	for (let i = 0; i < stockWarnArr.length; i++) {
		let key = stockWarnArr[i].sysSkuId;
		if (!stockWarnObj[key]) {
			stockWarnObj[key] = stockWarnArr[i];
		} else if (Number(stockWarnObj[key].num) < stockWarnArr[i].num) {
			stockWarnObj[key] = stockWarnArr[i];
		}
	}

	stockWarnArr = Object.values(stockWarnObj);
	console.log(stockWarnArr, 'stockWarnArr');
	return stockWarnArr || [];
};

const allotStockSkuInfo = (res, packList) => {
	const sysSkuIds = new Set();
	packList.forEach(pack => {
		pack.trades.forEach(trade => {
			const resItem = res.find(item => item.tid == trade.tid);
			if (resItem) {
				trade.orders.forEach(order => {
					const oidItem = resItem.oidList.find(oidItem => oidItem.oid == order.oid);
					if (oidItem && oidItem.num > oidItem.alreadyAllotStockNum) {
						if (order.isCombination) {
							// 组合商品校验
							if (userStore.systemSetting.groupStockNumOutWay === groupStockNumOutWay.以组合货品为准) {
								sysSkuIds.add(order.systemSkuId);
							}
							if (userStore.systemSetting.groupStockNumOutWay === groupStockNumOutWay.以子货品为准) {
								order.groupRelationRecordList.forEach(group => {
									sysSkuIds.add(group.sysSkuId);
								});
							}
						} else {
							sysSkuIds.add(order.systemSkuId);
						}
					}
				});
			}
		});
	});
	return [...sysSkuIds];
};

// 针对正常订单和组合商品订单都存在某种货品 可能会出现重复信息的情况
const checkStockInfo = (stockInfo:any[]) => {
	let obj = {};
	stockInfo.forEach((item:any, index) => {
		let id = item.sysItemId + item.sysSkuId;
		if (!obj[id] || stockInfo[obj[id] - 1]?.num < item.num) {
			obj[id] = index + 1;
		}
	});

	let curStockInfo = stockInfo.filter((item, index) => obj[item.sysItemId + item.sysSkuId] - 1 === index);
	return curStockInfo;
};


// 检查库存版 不占库存
export const getOrderStock = async(list: IPackage[], info?: any) => {
	const checkStockObj = gatherStockInfo(list, info);
	const params = Object.keys(checkStockObj).map((key) => ({
		sysItemId: checkStockObj[key].sysItemId,
		sysSkuId: checkStockObj[key].sysSkuId,
	}));
	if (params.length == 0) return [];
	const res = await TradeQueryStockInfoApi(params);
	return stockCheckFn(res, checkStockObj);
};

// 检查库存版 不占库存
export const getOrderNotAllotStock = async(list: IPackage[]) => {
	try {
		const stockInfoArr = await getOrderStock(list);
		const togetherIdsArr = stockInfoArr.reduce((a, b) => a.concat(b.togetherIds), []);
		const togetherIds = Array.from(new Set(togetherIdsArr));
		const sidsArr = stockInfoArr.reduce((a, b) => a.concat(b.sids), []);
		const sids = Array.from(new Set(sidsArr));
		const info = checkStockInfo(stockInfoArr);
		window.errorCollection?.customMessageUpload({
			type: `检查库存版不占库存【成功】`,
			data: {
				stockInfoArr,
				inventoryDeduct: userStore?.systemSetting?.inventoryDeduct,
			},
		});
		return { togetherIds, num: togetherIds.length, info, sysSkuIds: info?.map(i => i.sysSkuId), sids };
	} catch (error) {
		window.errorCollection?.customMessageUpload({
			type: `检查库存版不占库存【失败】`,
			data: {
				error,
				tidList: list.reduce((a, b) => a.concat(b.trades.map(i => i.tid)), []),
				inventoryDeduct: userStore?.systemSetting?.inventoryDeduct,
			},
		});
		return {};
	}
};

// 检查库存版 占库存
export const getOrderAllotStock = async(packList: IPackage[], info?: any) => {
	try {
		let orderList = gatherTradeStockInfo(packList, info);
		if (orderList.length == 0) {
			return {};
		}

		const tidList = orderList.reduce((a, b) => a.concat(b.trades.map(i => i.tid)), []);
		const res = await TradeAllotStockNumByTidListApi({ tidList });
		// 收集所有已勾选的 oid
		const checkedOidSet = new Set();
		orderList.forEach(pack => {
			pack?.trades?.forEach(trade => {
				trade?.orders?.filter(order => order.isChecked).forEach(order => {
					checkedOidSet.add(order.oid);
				});
			});
		});
		
		// 只检查已勾选的 order 是否库存不足
		let stockWarnTidSet = res.filter(i => i.oidList?.some(oidItem => checkedOidSet.has(oidItem.oid) && oidItem.alreadyAllotStockNum < oidItem.num)).map(i => i.tid);

		const togetherIds = orderList.filter(pack => pack.trades.some(i => stockWarnTidSet.includes(i.tid))).map(i => i.togetherId);
		const sids = orderList.filter(pack => pack.trades.some(i => stockWarnTidSet.includes(i.tid))).map(i => i.sids?.[0]);
		const sysSkuIds = allotStockSkuInfo(res, packList);
		window.errorCollection?.customMessageUpload({
			type: `检查库存版占库存【成功】`,
			data: {
				info,
				res,
				tidList,
			},
		});
		return { num: togetherIds.length, togetherIds: Array.from(new Set(togetherIds)), tids: tidList, info: [], sysSkuIds, sids };
	} catch (error) {
		window.errorCollection?.customMessageUpload({
			type: `检查库存版占库存【失败】`,
			data: {
				tidList: packList?.reduce((a, b) => a.concat(b?.trades?.map(i => i?.tid)), []),
				error,
				info,
			},
		});
		return {};
	}

};

// 发货触发了异常校验时
const sendDeliveryOnAbnormal = ({ type }) => {
	const { InspectSendStore: { setSendPending } } = tradeStore;
	// 如果是验货发货，需要提示语音
	if (["inspectSend", 'weighSend'].includes(type)) {
		playAudioAsync(voiceHandleAbnormal, 200);
	}
};

// 发货触发了异常校验，但是选择了取消
const sendDeliveryOnAbnormalCancel = ({ type }) => {
	const { InspectSendStore: { setSendPending } } = tradeStore;
	if (["inspectSend"].includes(type)) {
		setSendPending(false);
	}
	if (['weighSend'].includes(type)) {
		packWeightStore.setSendPending(false);
	}
};

// 发货时触发了异常校验，但是选择了“继续”
const sendDeliveryOnAbnormalContinue = ({ type }) => {
	// 如果是验货发货，需要继续loading
	const { InspectSendStore: { setSendPending } } = tradeStore;
	if (["inspectSend"].includes(type)) {
		setSendPending(true);
	}
	if (['weighSend'].includes(type)) {
		packWeightStore.setSendPending(true);
	}
};


export const handleHlOrder = async(packList) => {
	const hlSellerMap = {};
	packList.forEach(pack => {
		console.log('pack', pack);
		// !!非分销订单且密文回流且未返回解密信息订单需要走接口
		if (pack.hlPlatformType
			&& pack.hlEncryptOrder
			&& !PLAT_ALL_SCM.includes(pack.source)
			&& !pack.trades?.some(s => s.downstreamEncryptDetails)
		) {
			if (!hlSellerMap[pack.sellerId]) {
				hlSellerMap[pack.sellerId] = [];
			}
			hlSellerMap[pack.sellerId].push(...pack?.trades?.map(trade => trade.tid));
		}
	});
	const paramsList = Object.keys(hlSellerMap).map(hlSeller => {
		return {
			tid: hlSellerMap[hlSeller],
			sellerId: hlSeller,
			platform: 'ALI'
		};
	});

	const requestList = paramsList.map(p => GetTradeEncryptOutOrderInfo(p));


	try {
		let resList = await Promise.allSettled(requestList);
		let resObj = {};
		resList.filter(res => res.status == 'fulfilled').forEach((res:any) => {
			resObj = { ...resObj, ...res?.value };
		});
		console.log('resObj: ', resObj);
		// packList.forEach(pack => {
		// 	if (pack.hlPlatformType && pack.hlEncryptOrder) {
		// 		pack.tradeEncryptOutOrderInfoMap = {};
		// 	}
		// 	pack.trades.forEach(trade => {
		// 		if (resObj[trade.tid]) {
		// 			pack.tradeEncryptOutOrderInfoMap[trade.tid] = resObj[trade.tid];
		// 		}
		// 	});
		// });

	} catch (error) {
		console.log('error: ', error);
	}
};

const SysExchangeTypeEnumByValue = Object.keys(SysExchangeTypeEnum).reduce((obj, key) => {
	obj[SysExchangeTypeEnum[key]] = key;
	return obj;
}, {});

export const getSysExchangeTypeEnumText = (type: SysExchangeTypeEnum) => {
	return SysExchangeTypeEnumByValue[type];
};
