/* eslint-disable no-await-in-loop */
/*
 * @Author: try <EMAIL>
 * @Date: 2024-03-20 13:50:26
 * @Description: 拆单
 */
import { cloneDeep, groupBy, chunk } from 'lodash';
import NiceModal from '@ebay/nice-modal-react';
import { Modal } from 'antd';
import userStore from "@/stores/user";
import { weightUnit } from "@/pages/Index/Settings/System/constants";
import { BatchSplit, SplitMsg, SplitMsgProcess } from './modal';
import { ESplitType, ISplitOrderUtilsProps } from './interface';
import { IOrders, IPackage } from '../../interface';
import { TradeOrderSplitOrderSplitApi } from '@/apis/trade';
import { customLogPost, errorMapList, getAllSkus, getAllSkusByNum, getStockSplitOrder, getWeightSplitOrder } from './utils';
import { TradeOrderSplitOrderSplitRequest } from '@/types/trade/index';
import { PLAT_TB, PLAT_TM } from '@/constants';
import SingleSplit from './modal.single';

class SplitOrderUtils {

	isBatch:boolean

	// 原始勾选的订单列表
	checkedList: IPackage = [];

	// 不满足拆分条件的订单
	failedOrderList: IPackage = [];

	// 不处理的订单
	unDealOrderList: IPackage = [];

	// 满足拆分条件的订单
	passOrderList:IPackage = [];

	// 拆分过后的订单组装数据
	orderSplitInfos:TradeOrderSplitOrderSplitRequest = [];

	constructor(props?: ISplitOrderUtilsProps) {
		if (props) {
			this.checkedList = props.checkedList;
			this.isBatch = props.isBatch;
			const params = {
				checkedList: cloneDeep(this.checkedList),
				onOk: this.onBatchOk.bind(this)
			};
			if (props.isBatch) {
				NiceModal.show(BatchSplit, params as any);
			} else {
				const valid = this.verifiedOrder(this.checkedList, ESplitType.SINGLE);
				if (valid?.length) {
					this.showErrorMsg(ESplitType.SINGLE);
				} else {
					NiceModal.show(SingleSplit, params as any);
				}
			}
		}
	}

	// allowMerge : 是否允许合并订单继续拆分
	onBatchOk(type: ESplitType, params?: any, allowMerge?:boolean) {
		if (type !== ESplitType.SINGLE) {
			this.verifiedOrder(this.checkedList, type, params, allowMerge);
		}
		this.dividerOrder(type, params, allowMerge);
	}

	coverData(type, pack, trade, params?: any) {
		const errorOrder: any = this.checkMergePack(trade, pack);
		console.log('errorOrder', errorOrder);
		if (errorOrder) {
			return [];
		}
		switch (type) {
			case ESplitType.SKU:
				return this.splitBySku(trade, pack);
			case ESplitType.WEIGHT:
				return this.splitByWeight(trade, params);
			case ESplitType.STOCK:
				return	this.splitByStock(trade, pack);
			case ESplitType.PRE_SALE:
				return this.splitByPreSale(trade, pack);
			case ESplitType.NUMBER:
				return this.splitByNumber(trade, params);
			case ESplitType.ITEM:
				return	this.splitByItem(trade, params, pack);
			case ESplitType.SINGLE:
				return this.splitBySingle(trade, params);
			default:
				break;
		}
	}

	filterSendData(orderSplitInfos: any) {
		// !! 淘宝不支持拆分超过10个子单 https://tb.raycloud.com/task/6686591b1ed7294009936f8f
		const tmpOrder = [];
		orderSplitInfos.forEach(order => {
			if ([PLAT_TB, PLAT_TM].includes(order.platform) && order?.groupSplitInfos?.length > 10) {
				this.failedOrderList.push({ ptTid: order.ptTid, tid: order.tid, msg: `淘宝最大拆单数量需小于等于10单` });
			} else {
				tmpOrder.push(order);
			}
		});
		this.orderSplitInfos = tmpOrder;
	}

	// 组装数据
	dividerOrder(type: ESplitType, params: any, allowMerge:boolean) {
		this.passOrderList?.forEach(pack => {
			pack?.trades?.forEach(trade => {
				const groupSplitInfos = this.coverData(type, pack, trade, params);
				if (groupSplitInfos?.length) {
					this.orderSplitInfos.push({
						sellerId: trade.sellerId,
						platform: trade.platform,
						tid: trade.tid,
						ptTid: trade.ptTid,
						// _debugData: { trade, pack },
						groupSplitInfos
					});
				}
			});
		});
		console.log('按照规则组装的数据', params, this.failedOrderList, this.passOrderList, this.orderSplitInfos, this.unDealOrderList);
		// !! 添加一层判断逻辑，在发送前再进行一次过滤
		this.filterSendData(this.orderSplitInfos);
		if (this.orderSplitInfos.length || this.unDealOrderList.length) {
			this.sendData(type, this.orderSplitInfos, this.unDealOrderList, params, allowMerge);
		} else {
			this.showErrorMsg(type);
		}
	}

	// 如果是合单场景，则需要遍历所有trade规则
	checkMergePack(trade, pack) {
		if (pack?.isMerge) {
			const errorMap: any = errorMapList(ESplitType.SKU, true);
			const errorOrder = errorMap.filter(item => item.rule({ ...pack, ...trade, trades: [trade] }))?.[0];
			if (errorOrder) {
				// 新增limitType 方便后续提示处理：合单的子单校验信息不需要弹窗处理
				this.unDealOrderList.push({ togetherId: trade.togetherId, ptTid: trade.ptTid, tid: trade.tid, msg: errorOrder.msg, limitType: 'merge' });
				return errorOrder;
			}
			return false;
		}
		return false;
	}

	// 按SKU拆分：多款拆分为单款。如订单有商品A*3，B*2，C*2，则拆分出来3个订单商品分别为A*3、B*2、C*2
	splitBySku(trade, pack) {
		const orders = trade?.orders;
		if (orders.length == 1) {
			this.unDealOrderList.push({ togetherId: trade.togetherId, ptTid: trade.ptTid, tid: trade.tid, msg: '单个商品无需拆单', limitType: pack.isMerge ? 'merge' : 'normal' });
			return [];
		}
		const groupSplitInfos = [];
		orders.forEach((order) => {
			const oidSplitInfos = [{
				oid: order.oid, number: order.num, isGift: order.isGift
			}];
			groupSplitInfos.push({
				oidSplitInfos,
				splitTradeType: this.getSplitTradeType(oidSplitInfos)
			});
		});
		return groupSplitInfos;
	}

	// 按重量拆分：按重量拆分，例如：订单重量大于 3kg 后按 1kg 拆分，则拆分出来3笔总重将≤1kg的订单, 如商品无法拆分则拆分失败
	// 原则1：按照最使用最小包裹数拆分：如果sku*4 每个1kg，订单总重量大于3kg，则拆分sku*2+sku*2，两个而不是2+1+1 or 1+1+1+1
	// 原则2：如果存在单个sku的重量大于分隔重量，则拆单失败
	// 需要将所有sku打散重组
	splitByWeight(trade, params) {
		const { userSetting } = userStore;
		const isKg = userSetting?.weightUnit == weightUnit.显示kg;
		let { limitWeight, stepWeight } = params;
		if (isKg) {
			limitWeight *= 1000;
			stepWeight *= 1000;
		}
		const orders = trade.orders;
		// 拆分结果
		const groupSplitInfos = [];
		// 打散所有SKU，单个数量并排序
		const allSkus: IOrders[] = getAllSkus(trade);
		// 超重SKU单独算一单
		const overWeightSkus = allSkus.filter(({ weight }) => weight > limitWeight || weight > stepWeight);
		// 每个SKU重量均超出上限无需拆单
		console.log('overWeightSkus', overWeightSkus, allSkus, limitWeight, stepWeight);
		if (overWeightSkus.length == allSkus.length) {
			this.unDealOrderList.push({ togetherId: trade.togetherId, ptTid: trade.ptTid, tid: trade.tid, msg: '每个SKU重量均超出上限无需拆单' });
			return [];
		}

		// 超出限制重量的子单单独拆分
		const overWeighOrder = orders.filter(order => (Number(order.weight) / order.num) > stepWeight);
		const overWeighOrderOid = overWeighOrder.map(s => s.oid);
		console.log('overWeighOrder', overWeighOrder);
		if (overWeighOrder.length == orders.length) {
			this.unDealOrderList.push({ togetherId: trade.togetherId, ptTid: trade.ptTid, tid: trade.tid, msg: '每个子单重量均超出拆分重量不支持拆分' });
			return [];
		}

		if (overWeighOrder.length) {
			overWeighOrder.forEach(order => {
				const oidSplitInfos = [{ oid: order.oid, number: order.num, isGift: order.isGift }];
				groupSplitInfos.push({ oidSplitInfos, splitTradeType: this.getSplitTradeType(oidSplitInfos) });
			});
		}

		// 已发货的单独拆分
		const alreadySendOrderList = orders.filter(order => order.status == 'WAIT_BUYER_CONFIRM_GOODS')?.filter(s => !overWeighOrderOid.includes(s.oid));
		if (alreadySendOrderList.length) {
			const oidSplitInfos = alreadySendOrderList.map(order => {
				return { oid: order.oid, number: order.num, isGift: order.isGift };
			});
			groupSplitInfos.push({ oidSplitInfos, splitTradeType: this.getSplitTradeType(oidSplitInfos) });
		}

		// 超重但是未发货的单独拆分
		if (overWeightSkus.length) {
			const notAlreadySendOrderList = overWeightSkus.filter(order => order.status !== 'WAIT_BUYER_CONFIRM_GOODS')?.filter(s => !overWeighOrderOid.includes(s.oid));
			notAlreadySendOrderList.forEach(order => {
				const oidSplitInfos = [{ oid: order.oid, number: order.num, isGift: order.isGift }];
				groupSplitInfos.push({ oidSplitInfos, splitTradeType: this.getSplitTradeType(oidSplitInfos) });
			});
		}

		// !! 不超重且未发货的参与计算 过滤 超出限制重量的子单
		const needSplitSkus = allSkus.filter(({ weight, status }) => weight <= limitWeight && status !== 'WAIT_BUYER_CONFIRM_GOODS');
		const calculateSkus = needSplitSkus?.filter(s => !overWeighOrderOid.includes(s.oid));
		console.log('calculateSkus', calculateSkus);
		if (calculateSkus.length) {
			const result = getWeightSplitOrder(calculateSkus, stepWeight);
			console.log('result', result, calculateSkus, stepWeight, groupSplitInfos);
			result.forEach(group => {
				groupSplitInfos.push({ ...group, splitTradeType: this.getSplitTradeType(group.oidSplitInfos) });
			});
		}

		return groupSplitInfos;
	}

	// 按有货无货拆分：需开启预占库存，系统将拆出库存不足的商品为一个订单
	// 需要将sku打散，根据alreadyAllotStockNum字段拆分
	// 1.判断是否开启预占库存，
	// 2.已分配库存等于购买数量时才满足有货条件
	// 3.商品未绑定时按缺货处理
	splitByStock(trade, pack) {
		const orders = trade?.orders;
		const groupSplitInfos = [];
		const { sufficientStock, insufficientStock } = getStockSplitOrder(orders);
		console.log('sufficientStock, insufficientStock', sufficientStock, insufficientStock);
		if (sufficientStock.length && !insufficientStock.length) {
			this.unDealOrderList.push({ togetherId: trade.togetherId, ptTid: trade.ptTid, tid: trade.tid, msg: '均为有货无需拆单', limitType: pack.isMerge ? 'merge' : 'normal' });
			return [];
		}
		if (!sufficientStock.length && insufficientStock.length) {
			this.unDealOrderList.push({ togetherId: trade.togetherId, ptTid: trade.ptTid, tid: trade.tid, msg: '均为无货无需拆单', limitType: pack.isMerge ? 'merge' : 'normal' });
			return [];
		}

		[sufficientStock, insufficientStock].filter(e => e.length).forEach((oidSplitInfos) => {
			groupSplitInfos.push({ oidSplitInfos, splitTradeType: this.getSplitTradeType(oidSplitInfos) });
		});
		return groupSplitInfos;
	}

	// 按现货、预售进行拆分:订单中包含现货+预售商品时，将拆分为现货订单和预售订单
	splitByPreSale(trade, pack) {
		console.log('按现货、预售进行拆分', trade);
		const orders = trade?.orders;
		const groupSplitInfos = [];
		const group = groupBy(orders, (order) => order.isPreSale == 1);
		if (group.true && !group.false) {
			this.unDealOrderList.push({ togetherId: trade.tid, ptTid: trade.ptTid, tid: trade.tid, msg: '均为预售无需拆单', type: 'preSale', limitType: pack.isMerge ? 'merge' : 'normal' });
			return [];
		}
		if (!group.true && group.false) {
			this.unDealOrderList.push({ togetherId: trade.tid, ptTid: trade.ptTid, tid: trade.tid, msg: '均不是预售无需拆单', type: '!preSale', limitType: pack.isMerge ? 'merge' : 'normal' });
			return [];
		}
		[group.true, group.false].filter(Boolean).forEach((group) => {
			const oidSplitInfos = group.map((order) => {
				return {
					oid: order.oid, number: order.num, isGift: order.isGift
				};
			});
			groupSplitInfos.push({ oidSplitInfos, splitTradeType: this.getSplitTradeType(oidSplitInfos) });
		});
		return groupSplitInfos;
	}

	// 按数量拆分：按数量进行拆单，将按照指定的数量拆分成多个订单
	// 需要将所有sku打散重组
	splitByNumber(trade, params) {
		let { limitNumber, stepNumber } = params;
		const orders = trade.orders;
		// 拆分结果
		const groupSplitInfos = [];
		// !! 这里不直接取pack层级的totalNum，避免被其他业务影响
		const totalNum = trade.orders.reduce((pre, cur) => pre + Number(cur.num), 0);
		console.log('totalNum', totalNum, trade.orders);
		if (totalNum <= limitNumber) {
			this.unDealOrderList.push({ togetherId: trade.togetherId, ptTid: trade.ptTid, tid: trade.tid, msg: '商品数量不超过拆分数量无需拆单' });
			return groupSplitInfos;
		}

		// 已发货的单独拆分不参与拆单运算
		const alreadySendOrderList = orders.filter(order => order.status == 'WAIT_BUYER_CONFIRM_GOODS');
		if (alreadySendOrderList.length) {
			const oidSplitInfos = alreadySendOrderList.map(order => {
				return { oid: order.oid, number: order.num, isGift: order.isGift };
			});
			groupSplitInfos.push({ oidSplitInfos, splitTradeType: this.getSplitTradeType(oidSplitInfos) });
		}

		// 待发货子单单独拆分
		const needSplitOrder = orders.filter(order => order.status !== 'WAIT_BUYER_CONFIRM_GOODS');
		const allSkus: IOrders[] = getAllSkusByNum(needSplitOrder);
		console.log('trade22', trade, allSkus);
		const result = chunk(allSkus, stepNumber);
		result.forEach((group) => {
			const oidSplitInfosMap = new Map();
			group.forEach(g => {
				const crt = oidSplitInfosMap.get(g.oid);
				if (crt) {
					oidSplitInfosMap.set(g.oid, { oid: g.oid, number: Number(crt.number) + Number(g.num), isGift: g.isGift });
				} else {
					oidSplitInfosMap.set(g.oid, { oid: g.oid, number: g.num, isGift: g.isGift });
				}
			});
			console.log('oidSplitInfosMap', oidSplitInfosMap);
			groupSplitInfos.push({ oidSplitInfos: Array.from(oidSplitInfosMap.values()), splitTradeType: this.getSplitTradeType(Array.from(oidSplitInfosMap.values())) });
		});
		return groupSplitInfos;
	}

	// 当订单中包含以下商品时，系统自动将指定商品从原订单拆分出去
	splitByItem(trade, params, pack) {
		const { selectSkuId } = params;
		const orders = trade?.orders;
		const groupSplitInfos = [];
		const group = groupBy(orders, (order) => selectSkuId.includes(order.skuId));
		if (!group.true && group.false) {
			this.unDealOrderList.push({ togetherId: trade.togetherId, ptTid: trade.ptTid, tid: trade.tid, msg: '均不是指定商品无需拆单', limitType: pack.isMerge ? 'merge' : 'normal' });
			return [];
		}
		const splitOrder = group.true.map(s => [s]);
		[...splitOrder, group.false].filter(Boolean).forEach((group) => {
			const oidSplitInfos = group.map((order) => {
				return {
					oid: order.oid, number: order.num, isGift: order.isGift
				};
			});
			groupSplitInfos.push({ oidSplitInfos, splitTradeType: this.getSplitTradeType(oidSplitInfos) });
		});
		return groupSplitInfos;
	}

	
	splitBySingle(trade, params) {
		const { originData, splitData, splitTids } = params;
		const groupSplitInfos = []; // 原始订单1组（如果没有被拆分完）、拆分后的订单组
		
		// 按交易组ID分组处理
		const tradeId = trade.tid;
		// 如果当前交易ID不在splitTids列表中，则不进行拆分处理
		if (splitTids.includes(tradeId)) {
			console.log(`交易ID ${tradeId} 在noModifyTids列表中，跳过拆分处理`);
			return [];
		}
		// 获取当前交易相关的原始数据
		const tradeOriginData = originData.filter(item => item.tid === tradeId);
		// 过滤原始订单数据：有可拆分数量的保留
		const validOriginData = tradeOriginData.filter(s => s.type === 'order' && Number(s.num) > (s.alreadySplitNum || 0));
		// console.log('noModifyTidsnoModifyTids', originData, tradeOriginData, validOriginData, splitTids, trade, params);
		
		if (validOriginData?.length) {
			const oidSplitInfos = [];
			validOriginData.forEach(order => {
				const { num, alreadySplitNum, oid, isGift } = order;
				const splitNum = num - (alreadySplitNum || 0);
				oidSplitInfos.push({
					oid,
					number: splitNum,
					isGift,
				});
			});
			groupSplitInfos.push({ oidSplitInfos, splitTradeType: this.getSplitTradeType(oidSplitInfos) });
		}
		// 获取当前交易相关的拆分数据
		const tradeSplitData = splitData.filter(item => item.tid === tradeId);
		
		// 对已拆分的数据进行分组
		const groupSplit = {};
		tradeSplitData.forEach(item => {
			if (item.type !== 'group') {
				if (!groupSplit[item.groupId]) {
					groupSplit[item.groupId] = [];
				}
				groupSplit[item.groupId].push(item);
			}
		});
		
		Object.keys(groupSplit).forEach(groupId => {
			const group = groupSplit[groupId];
			const oidSplitInfos = [];
			group.forEach(order => {
				const { splitNum, oid, isGift } = order;
				oidSplitInfos.push({
					oid,
					number: splitNum,
					isGift,
				});
			});
			groupSplitInfos.push({ oidSplitInfos, splitTradeType: this.getSplitTradeType(oidSplitInfos) });
		});
		
		console.log('单个拆分groupSplitInfos', groupSplit, groupSplitInfos);

		return groupSplitInfos;
	}

	// 判断拆单类型：仅普通商品(REGULAR_ORDER)、普通商品+赠品订单(REGULAR_GIFT_ORDER)、仅赠品订单(GIFT)
	getSplitTradeType(orderList = []) {
		if (orderList.every(item => !item.isGift)) {
			return "REGULAR_ORDER";
		} else if (orderList.every(item => item.isGift)) {
			return "GIFT";
		} else {
			return "REGULAR_GIFT_ORDER";
		}
	}

	// 可拆单判断:以下情况的订单则不进行拆单处理，同时收集相关结果按优先级展示
	// 1. 已拆单、已合单、
	// 2. 手工订单、分销订单、
	// 3. 待付款、已发货、交易成功、已关闭
	// 4. 快递单已打印、发货单已打印、
	// 5. 风控订单、异常地址、拼团订单未完成、平台缺货、
	// 6. 挂起订单、先发货订单、已生成商品标签、京仓订单、云仓订单、京配订单、拼多多集运订单(新疆集运、香港转运、西藏转运)、物流转运、货到付款、含赠品
	/**
	 *
	 * @param orderList 订单列表
	 * @param type 拆分类型
	 * @returns
	 */
	verifiedOrder(orderList, type?: ESplitType, params?: any, allowMerge = false) {
		const errorMap: any = errorMapList(type, allowMerge);
		console.log('orderList', orderList);
		orderList.forEach(pack => {
			const errorOrder = errorMap.filter(item => item.rule(pack))?.[0];
			if (errorOrder) {
				this.failedOrderList.push({ ptTid: pack.ptTids.join(','), tid: pack.tids.join(','), msg: `${typeof errorOrder.msg == 'string' ? errorOrder.msg + '不支持拆分' : errorOrder.msg(pack)}` });
			} else {
				this.passOrderList.push(pack,);
			}
		});
		// 按重量拆分提前过滤待处理数据
		if (type == ESplitType.WEIGHT) {
			const { userSetting } = userStore;
			const isKg = userSetting?.weightUnit == weightUnit.显示kg;
			let { limitWeight } = params;
			if (isKg) {
				limitWeight *= 1000;
			}
			const overWeightPacks = this.passOrderList.filter(pack => Number(pack.totalWeight || 0) <= limitWeight);
			overWeightPacks?.forEach(pack => {
				this.unDealOrderList.push({ ptTid: pack.ptTids.join(','), togetherId: pack.togetherId, tid: pack.tids.join(','), msg: '订单总重量小于或等于拆分重量无需拆分' });
			});
			this.passOrderList = this.passOrderList.filter(pack => Number(pack.totalWeight || 0) > limitWeight);
		}
		return this.failedOrderList;
	}

	async sendData(type, orderSplitInfos, unDealOrderList, params:any, allowMerge) {
		if (type == ESplitType.SINGLE) {
			try {
				const res = await TradeOrderSplitOrderSplitApi({ orderSplitInfos });
				customLogPost(this.isBatch, '拆单', orderSplitInfos);
				if (res) {
					params?.finishSplit(res, orderSplitInfos);
				}
			} catch (error) {
				console.log('orderSplitInfos', error);
				customLogPost(this.isBatch, '拆单失败', { orderSplitInfos, error });
			}
		} else {
			NiceModal.show(SplitMsgProcess as any, { allowMerge, type, orderSplitInfos, params, failedOrderList: this.failedOrderList, unDealOrderList, passOrderList: this.passOrderList });
		}
	}

	showErrorMsg(type, params = {}) {
		if (type == ESplitType.SINGLE && this.failedOrderList?.length) {
			Modal.info({
				title: '提示',
				content: this.failedOrderList[0].msg,
				okText: '知道了',
			});
			return;
		}
		if (this.failedOrderList?.length) {
			NiceModal.show(SplitMsg, { params, failedOrderList: this.failedOrderList, passOrderList: this.passOrderList } as any);
		}
	}
}


export default (arg?:ISplitOrderUtilsProps) => new SplitOrderUtils(arg);
