/*
 * @Author: try <EMAIL>
 * @Date: 2024-03-26 10:01:08
 * @Description:
 */
import { groupBy } from "lodash";
import { TradeOrderSplitOrderSplitResponse } from "@/types/trade/index";
import { tradeStore } from "@/stores";
import { IOrders, IPackage, ISubTrade } from "../../interface";
import { handlePackageList, KsConsolidateType } from "../../utils";
import { PLAT_DW, PLAT_FXG, PLAT_PDD, PLAT_TB, PLAT_KTT } from "@/constants";
import { ConsolidateType, FxgConsolidateType } from '../../constants';
import { isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import { ESplitType } from "./interface";
// 获取所有SKU*1并排序：先排序重量，再排SKU,SKU重量相同的，数量少的考前
export const getAllSkus = (trade: ISubTrade, allSkus = []) => {
	if (trade?.orders?.length) {
		trade.orders.forEach((sku) => {
			let skuNum = Number(sku.num || 0);
			while (skuNum) {
				const weight = Number(sku.weight || 0) / sku.num;
				allSkus.push({ ...sku, weight, num: 1, oid: sku.oid });
				skuNum--;
			}
		});
	}
	return allSkus.sort((a, b) => {
		if (a.weight != b.weight) {
			return b.weight - a.weight;
		}
		return a.oid.localeCompare(b.oid);
	});
};

export const getAllSkusByNum = (orders: IOrders[], allSkus = []) => {
	if (orders?.length) {
		orders.forEach((sku) => {
			let skuNum = Number(sku.num || 0);
			while (skuNum) {
				allSkus.push({ ...sku, num: 1, oid: sku.oid });
				skuNum--;
			}
		});
	}
	return allSkus.sort((a, b) => {
		return (a.skuId || a.numIid)?.localeCompare?.(b.skuId || b.numIid);
	});
};

export const reSortSkusByNum = (skus:ISubTrade) => {
	let allSkus = [];
	const result = [];
	const groups = groupBy(skus, "oid");
	Object.keys(groups).forEach((key) => {
		console.log('key', key);
		const sku = groups[key][0];
		const num = groups[key].length;
		const weight = sku.weight;
		allSkus.push({ num, weight, oid: sku.oid });
	});
	allSkus = allSkus.sort((a, b) => {
		if (a.weight != b.weight) {
			return b.weight - a.weight;
		}
		return a.num - b.num;
	});
	allSkus.forEach(s => {
		result.push(...groups[s.oid]);
	});
	console.log('allSkus', groups, result, allSkus);
	return result;
};
// 按重量计算拆分订单
export const getWeightSplitOrder = (sortedSkus, maxWeight) => {
	let index = sortedSkus.length;
	// 按照重量从大到小排序订单,重量相同按照oid排序
	const groupSplitInfos = [];
	const getGroupSplitInfos = (calculateOrder) => {
		const firstOrder = calculateOrder[0];
		index--;
		firstOrder.num = 0;
		let currentPackage = [{ oid: firstOrder.oid, weight: firstOrder.weight, number: 1, isGift: firstOrder.isGift }];
		let currentWeight = calculateOrder[0].weight;
		calculateOrder = reSortSkusByNum(calculateOrder);
		for (let j = 0; j < calculateOrder.length; j++) {
			const nextOrder = calculateOrder[j];
			if (nextOrder.num) {
				if (currentWeight + nextOrder.weight <= maxWeight) {
					const existingSkuIndex = currentPackage.findIndex(item => item.oid === nextOrder.oid);
					if (existingSkuIndex !== -1) {
						currentPackage[existingSkuIndex].number++;
					} else {
						currentPackage.push({ oid: nextOrder.oid, weight: nextOrder.weight, number: 1, isGift: nextOrder.isGift });
					}
					currentWeight += nextOrder.weight;
					// 置为0 标识已经处理
					nextOrder.num = 0;
					index--;
				} else {
					continue;
				}
			}
		}
		return currentPackage;
	};
	while (index) {
		const calculateOrder = sortedSkus.filter(order => order.num);
		if (calculateOrder.length) {
			groupSplitInfos.push({ oidSplitInfos: getGroupSplitInfos(getAllSkus(undefined, calculateOrder)) });
		} else {
			index = 0;
		}
	}
	console.log('groupSplitInfos', groupSplitInfos);
	return groupSplitInfos;

};
// 按有货无货拆分订单
export const getStockSplitOrder = (orders) => {
	// 有货
	let sufficientStock = [];
	// 无货
	let insufficientStock = [];
	orders.forEach(order => {
		const lackNum = Number(order.num) - Number(order.alreadyAllotStockNum);
		// 有货
		if (lackNum <= 0) {
			sufficientStock.push({ oid: order.oid, number: order.num, isGift: order.isGift });
		} else {
			insufficientStock.push({ oid: order.oid, number: lackNum, isGift: order.isGift });
			// 部分有货
			if (Number(order.alreadyAllotStockNum)) {
				sufficientStock.push({ oid: order.oid, number: order.alreadyAllotStockNum, isGift: order.isGift });
			}
		}
	});
	console.log('sufficientStock', sufficientStock, insufficientStock);

	return {
		sufficientStock,
		insufficientStock
	};
};

/**
 * 拆单、取消拆单需要对原有数据进行增删
 */
export const insertOrder = async(res: TradeOrderSplitOrderSplitResponse['data'], pack?: IPackage, type = 'split') => {
	const { tradeListStore: { splitOrder, closeSplitOrder } } = tradeStore;
	const { cancelOrHideTidList = [], packageInfoVos = [] } = res;
	// 调用原始处理订单逻辑
	const list = await handlePackageList(packageInfoVos as any, true);
	console.log('xxxxx', res, pack);
	if (type == 'split') {
		// 如果拆合单后结果一致，则不进行操作
		// !! 因为splitOrder方法执行的是先插入后删除的逻辑，会导致数据消失（删除两次）
		if (cancelOrHideTidList?.length == packageInfoVos?.length && cancelOrHideTidList?.[0] == packageInfoVos?.[0]?.togetherId) {
			return;
		} else {
			splitOrder(list as any, cancelOrHideTidList, pack);
		}
	} else {
		closeSplitOrder(list as any, cancelOrHideTidList, pack);
	}
};

/**
 * 拆单日志
 */
export const customLogPost = (isBatch: boolean, opt:string, data = {}) => {
	window.errorCollection?.customMessageUpload({
		type: `${isBatch ? '批量' : '单个'}${opt}`,
		data
	});
};
/**
 * 合单日志
 */
export const customLogPostForMerge = (data = {}) => {
	window.errorCollection?.customMessageUpload({
		type: '拆单合单日志',
		data
	});
};

/**
 *
 */
export const errorMapList = (type, allowMerge) => {
	if (type == ESplitType.SINGLE) allowMerge = true;
	return [
		{ msg: '得物订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.platform === PLAT_DW) : pack.platform === PLAT_DW) },
		{ msg: '已合单', rule: pack => pack.isMerge && !allowMerge && type != ESplitType.SINGLE },
		{ msg: () => '合并订单不支持按重量拆分', rule: pack => pack.isMerge && type != ESplitType.SINGLE && type == ESplitType.WEIGHT },
		{ msg: () => '合并订单不支持按商品数量拆分', rule: pack => pack.isMerge && type != ESplitType.SINGLE && type == ESplitType.NUMBER },
		{ msg: '整单发货订单', rule: pack => (allowMerge ? pack.trades.every(trade => trade?.serviceTagList?.some(i => i == 'full_order_ship')) : pack.trades.some(trade => trade?.serviceTagList?.some(i => i == 'full_order_ship'))) },
		{ msg: '已拆单', rule: pack => pack?.trades?.every(s => s.isSplit) },
		{ msg: '回流订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.hlPlatformType) : pack.hlPlatformType) },
		{ msg: '手工订单', rule: pack => pack.source === 'HAND' },
		{ msg: '分销订单', rule: pack => (allowMerge ? pack?.trades?.every(t => isSourceScm(t)) : isSourceScm(pack)) },
		{ msg: '订单状态不是待发货状态', rule: pack => pack.trades.some(s => !['SELLER_CONSIGNED_PART', 'WAIT_SELLER_SEND_GOODS'].includes(s.status)) },
		{ msg: '快递单已打印', rule: pack => (allowMerge ? pack?.trades?.every(t => t.isPrintKdd == 1) : pack.waybillPrintStatus === 'already' || pack.isPrintKdd == '1') },
		{ msg: '发货单已打印', rule: pack => (allowMerge ? pack?.trades?.every(t => t.isPrintFhd == 1) : pack.shipListPrintStatus === 'already' || pack.isPrintFhd == '1') },
		{ msg: '风控订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.riskControlStatus == '1') : pack.riskControlStatus == '1') },
		{ msg: '拼团订单未完成', rule: pack => (allowMerge ? pack?.trades?.every(t => t.pOrderUnfinished) : pack.pOrderUnfinished) },
		{ msg: '平台缺货', rule: pack => (allowMerge ? pack?.trades?.every(t => t.platformStockOut) : pack.platformStockOut) },
		{ msg: '挂起订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.isPending) : pack.isPending) },
		{ msg: '先发货订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.firstSend) : pack.firstSend) },
		{ msg: '已生成商品标签', rule: pack => (allowMerge ? pack?.trades?.every(t => t.labelstatus) : pack.labelstatus) },
		{ msg: '京仓订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.jdjc) : pack.jdjc) },
		{ msg: '云仓订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.jdyc) : pack.jdyc) },
		{ msg: '京配订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.jdjp) : pack.jdjp) },
		// { msg: '拼多多集运订单', rule: pack => (allowMerge ? pack?.trades?.every(t => ConsolidateType[pack.consolidateType]) : ConsolidateType[pack.consolidateType]) },
		// {
		// 	msg: '物流转运',
		// 	rule: pack => {
		// 		return allowMerge
		// 			? pack?.trades?.every(t => KsConsolidateType[t.consolidateType] || FxgConsolidateType[t.consolidateType])
		// 			: KsConsolidateType[pack.consolidateType] || FxgConsolidateType[pack.consolidateType];
		// 	} },
		{ msg: '货到付款', rule: pack => (allowMerge ? pack?.trades?.every(t => t.isCod) : pack.isCod) },
		{ msg: '拼多多含赠品', rule: pack => (allowMerge ? pack?.trades?.every(t => (t?.orders?.some(order => order?.isGift) || t?.giftOrders?.length) && t.platform === PLAT_PDD) : pack.hasGift && pack.platform === PLAT_PDD) },
		{ msg: '快团团含赠品', rule: pack => (allowMerge ? pack?.trades?.every(t => (t?.orders?.some(order => order?.isGift) || t?.giftOrders?.length) && t.platform === PLAT_KTT) : pack.hasGift && pack.platform === PLAT_KTT) },
		// { msg: '系统改商品', rule: pack => (allowMerge ? pack?.trades?.every(t => t.isSysExchanged) : pack.isSysExchanged) },
		{ msg: 'BIC质检订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t?.serviceTagList?.includes('bic_order')) : pack?.serviceTagList?.includes('bic_order')) },
		{ msg: '淘宝礼物订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.platform === PLAT_TB && t?.serviceTagList?.includes('presentOrder')) : pack.platform === PLAT_TB && pack?.serviceTagList?.includes('presentOrder')) },
		{ msg: '抖音礼物订单', rule: pack => (allowMerge ? pack?.trades?.every(t => t.platform === PLAT_FXG && t?.serviceTagList?.includes('presentOrder')) : pack.platform === PLAT_FXG && pack?.serviceTagList?.includes('presentOrder')) },
		// { msg: '含系统赠品',
		// 	rule: pack => {
		// 		return allowMerge ? pack?.trades?.every(t => t.serviceTagList?.includes('sysGift')) : pack?.serviceTagList?.includes('sysGift');
		// 	}
		// },
		{
			msg: '单个商品',
			rule: pack => ((allowMerge && pack.isMerge) ? false : pack?.trades?.length == 1 && pack?.trades?.[0]?.orders?.length == 1 && pack?.trades?.[0]?.orders?.[0].num == 1)
		},
		{ msg: '已生成波次订单', rule: pack => pack.waveNoList && pack.waveNoList.length > 0 }
	];
};
