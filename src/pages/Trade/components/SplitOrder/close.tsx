/* eslint-disable react/jsx-closing-tag-location */
/* eslint-disable no-await-in-loop */
/*
 * @Author: try <EMAIL>
 * @Date: 2024-03-20 13:50:26
 * @Description: 拆单
 */
import { cloneDeep } from 'lodash';
import React from 'react';
import NiceModal from '@ebay/nice-modal-react';
import { Modal } from 'antd';
import { closeOrder } from './modal';
import { ISplitOrderUtilsProps } from './interface';
import { IPackage } from '../../interface';
import { TradeOrderSplitOrderSplitRequest } from '@/types/trade/index';
import { SysExchangeTypeEnum } from '@/pages/Trade/components/BottomCom/constants';

class SplitOrderClose {
	checkedList: IPackage = [];

	failedOrderList: IPackage = [];

	passOrderList:IPackage = [];

	closeSystemOrderInfoList:TradeOrderSplitOrderSplitRequest = [];

	constructor(props?: ISplitOrderUtilsProps) {
		this.checkedList = cloneDeep(props.checkedList); 
		const totalOrder = this.checkedList.length;
		let totalItem = 0;
		this.checkedList?.forEach(pack => {
			const { trades } = pack;
			trades?.forEach(trade => {
				const { orders } = trade;
				const orderList = orders?.filter(order => order.isChecked);
				orderList.forEach(order => {
					totalItem += Number(order.num || 0);
				});
			});
		});
		
		Modal.confirm({
			centered: true,
			title: '关闭订单',
			content: <div>
				<div>确定要对{totalOrder}个订单中的{totalItem}个商品进行关闭？</div>
				<div style={ { color: '#f5222d', fontSize: '12px', marginTop: '4px' } }>注意：请仔细核对订单线上订单状态，关闭可能导致线上订单商品漏发</div>
			</div>,
			okText: '关闭订单',
			cancelText: '取消关闭',
			onOk: () => {
				this.verifiedOrder(this.checkedList);
			},
		});
	}

	verifiedOrder(orderList) {
		const errorMap :any = [
			{ 
				msg: '不是拆分订单，不可关闭',
				rule: pack => {
					let flag = false;
					pack?.trades?.forEach(trade => {
						const orderList = trade?.orders?.filter(order => order.isChecked);
						orderList.forEach(order => {
							if (![SysExchangeTypeEnum.新增的商品, SysExchangeTypeEnum.新增的货品].includes(order.sysExchangeType)) {
								flag = true;
							}
						});
					});
					return !pack.isSplit && flag;
				} },
			{
				msg: '仅支持线上已发货、退款成功的待发货订单',
				rule: pack => {
					let flag = true;
					pack?.trades?.forEach(trade => {
						const orderList = trade?.orders?.filter(order => order.isChecked);
						orderList.forEach(order => {
						// 针待发货订单本次仅准许 待发货（线上已发货）、待发货（退款成功）的子订单进行关闭
							if (order.status == 'WAIT_SELLER_SEND_GOODS' && (order.onlineShip || order.refundStatus == 'REFUND_SUCCESSED' || [SysExchangeTypeEnum.新增的商品, SysExchangeTypeEnum.新增的货品].includes(order.sysExchangeType))) {
								flag = false;
							}
						});
					});
					return flag;
				} }
		];
		orderList.forEach(pack => {
			const errorOrder = errorMap.filter(item => item.rule(pack))?.[0];
			if (errorOrder) {
				this.failedOrderList.push({ ptTid: pack.ptTids.join(','), tid: pack.tids.join(','), msg: errorOrder.msg });
			} else {
				this.passOrderList.push(pack);
			}
		});
		this.closeSystemOrderInfoList = [];
		// !! 如果合单情况这里就有问题
		this.passOrderList.forEach(pack => {
			const { trades, togetherId } = pack;
			trades.forEach(trade => {
				const { sellerId, ptTid, platform, tid, } = trade;
				const oidList = trade.orders?.filter(order => order.isChecked).map(order => order.oid);
				this.closeSystemOrderInfoList.push({
					togetherId,
					sellerId,
					ptTid,
					platform,
					tid,
					oidList
				});
			});
			return this.closeSystemOrderInfoList;
		});

		this.sendData(this.closeSystemOrderInfoList, this.failedOrderList);
	}

	sendData(closeSystemOrderInfoList, failedOrderList) {
		NiceModal.show(closeOrder as any, { closeSystemOrderInfoList, failedOrderList, passOrderList: this.passOrderList });
	}
}


export default (arg?:ISplitOrderUtilsProps) => new SplitOrderClose(arg);