/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable no-await-in-loop */
/*
 * @Author: try <EMAIL>
 * @Date: 2024-03-20 14:26:10
 * @Description: 
 */
import { Button, Empty, Progress, InputNumber, Modal, Radio, Space, Table, message, Descriptions, Dropdown, Form, Input, Alert, Checkbox, Popover, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import dayjs from "dayjs";
import { cloneDeep } from "lodash";
import { InfoCircleFilled, QuestionCircleFilled, QuestionCircleOutlined } from "@ant-design/icons";
import style from './index.module.scss';
import { BatchSplitProps, CancelSplitProps, ESplitType, SplitMsgProps } from "./interface";
import userStore from "@/stores/user";
import { orderPreOccupiedStock, weightUnit } from "@/pages/Index/Settings/System/constants";
import { IPackage } from "@/pages/ForeignManage/interface";
import { stockVersion } from "@/constants";
import { copyToPaste } from "@/utils";
import { TradeQueryTradeApi } from "@/apis/trade/search";
import TradeLabelCom from "../ListItem/components/TradeLabelCom";
import { TradeOrderSplitCancelSplitApi, TradeOrderSplitCloseSystemOrderApi, TradeOrderSplitManualMergeTradeApi, TradeOrderSplitOrderSplitApi } from "@/apis/trade";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { customLogPost, customLogPostForMerge, errorMapList, insertOrder } from "./utils";
import { copyMenu, getTradeFlag, getTradeFlagTag, getTradeStatusLabel, handlePackageList } from "../../utils";
import ReceiverAddressCom from "../ListItem/components/receiverAddressCom";
import { PackPrintStatus, PackageOrdersCom } from "../ListItem/components/simpleCom";
import useGetState from "@/utils/hooks/useGetState";
import Flex from "@/components/Flex";
import history from "@/utils/history";
import { local } from "@/libs/db";
import events from "@/utils/events";
import pdd from '@/assets/image/afterSale/pdd.png';
import tb from '@/assets/image/afterSale/tb.png';
import dy from '@/assets/image/afterSale/dy.png';
import ks from '@/assets/image/afterSale/ks.png';
import xhs from '@/assets/image/afterSale/xhs.png';
import sph from '@/assets/image/afterSale/sph.png';
import jd from '@/assets/image/afterSale/jd.png';
import yz from '@/assets/image/afterSale/yz.png';
import ali from '@/assets/image/afterSale/ali.png';
import c2m from '@/assets/image/afterSale/c2m.png';

/**
 * 批量拆单弹窗
 */
const POINT = {
	[ESplitType.SKU]: Pointer.批量拆单_按SKU拆分保存,
	[ESplitType.WEIGHT]: Pointer.批量拆单_按重量拆分,
	[ESplitType.STOCK]: Pointer.批量拆单_拆分有货无货,
	[ESplitType.PRE_SALE]: Pointer.批量拆单_按现货预售拆分,
	[ESplitType.NUMBER]: Pointer.批量拆单_按数量拆分,
	[ESplitType.ITEM]: Pointer.批量拆单_按商品拆分,
};

const platformRules = [
	{
		key: 'toutiao',
		platform: {
			icon: dy,
			name: '头条放心购'
		},
		supportSplitGoods: true,
		supportSplitQuantity: true,
		supportSplitQuantityDesc: "最大可上传单号数量不可超过商品购买数",
	},
	{
		key: 'pdd',
		platform: {
			icon: pdd,
			name: '拼多多'
		},
		supportSplitGoods: true,
		supportSplitQuantity: false,
		supportSplitQuantityDesc: "拆分发货后，系统发货一个商品后，平台为整单发货。其余单号将通过额外单号方式上传，可上传数量不超过商品购买数",
	},
	{
		key: 'taobao',
		platform: {
			icon: tb,
			name: '淘宝天猫'
		},
		supportSplitGoods: true,
		supportSplitQuantity: true,
		supportSplitQuantityDesc: "最大可上传单号数量不超过10个",
	},
	{
		key: 'kuaishou',
		platform: {
			icon: ks,
			name: '快手电商'
		},
		supportSplitGoods: true,
		supportSplitQuantity: true
	},
	{
		key: 'wechat',
		platform: {
			icon: sph,
			name: '微信视频号'
		},
		supportSplitGoods: true,
		supportSplitQuantity: false
	},
	{
		key: 'xiaohongshu',
		platform: {
			icon: xhs,
			name: '小红书'
		},
		supportSplitGoods: true,
		supportSplitQuantity: false
	},
	{
		key: 'youzan',
		platform: {
			icon: yz,
			name: '有赞微商城'
		},
		supportSplitGoods: true,
		supportSplitQuantity: false
	},
	{
		key: 'jd',
		platform: {
			icon: jd,
			name: '京东'
		},
		supportSplitGoods: true,
		supportSplitQuantity: true
	},
	{
		key: 'ali',
		platform: {
			icon: ali,
			name: '1688'
		},
		supportSplitGoods: true,
		supportSplitQuantity: false
	},
	{
		key: 'c2m',
		platform: {
			icon: c2m,
			name: '淘工厂'
		},
		supportSplitGoods: true,
		supportSplitQuantity: false
	}
];

const splitRuleContent = (
	<div className={ style.splitRuleTable }>
		<div className={ style.tableTitle }>查看平台拆分规则和同步发货规则</div>
		<Table
			bordered
			size="small"
			pagination={ false }
			dataSource={ platformRules }
			columns={ [
				{
					title: '平台',
					dataIndex: 'platform',
					key: 'platform',
					render: (platform) => (
						<div className={ style.platform }>
							<img width={ 32 } height={ 32 } src={ platform.icon } alt={ platform.name } className="r-mr-8" />
							{platform.name}
						</div>
					)
				},
				{
					title: (
						<div>是否支持<span style={ { color: "#f00" } }>拆分商品</span>并上传多个快递单号 <Tooltip title="拆分商品：如订单中含A、B两个商品，按商品拆分后，会拆分成含A的订单、含B的订单"><QuestionCircleFilled /></Tooltip></div>
					),
					dataIndex: 'supportSplitGoods',
					key: 'supportSplitGoods',
					align: 'center',
					render: (support) => (
						<span className={ support ? style.checkIcon : style.dashIcon }>
							{support ? '✓' : '-'}
						</span>
					)
				},
				{
					title: (
						<div>是否支持<span style={ { color: "#f00" } }>拆分数量</span>并上传多个快递单号 <Tooltip title="拆分数量：订单中含A商品数量为2，按数量拆分会分成两个含A*1 的订单"><QuestionCircleFilled /></Tooltip></div>
					),
					dataIndex: 'supportSplitQuantity',
					key: 'supportSplitQuantity',
					align: 'center',
					render: (support, row) => (
						<>
							<span className={ support ? style.checkIcon : style.dashIcon }>
								{support ? '✓' : '-'}
							</span>
							{	['toutiao', 'pdd', 'taobao'].includes(row.key) ? <Tooltip title={ row.supportSplitQuantityDesc }><QuestionCircleFilled className="r-ml-4 r-c-999" /></Tooltip>
								: !support && <Tooltip title="拆分发货后，系统发货了一个商品，线上为整单发货"><QuestionCircleFilled className="r-ml-4 r-c-999" /></Tooltip>}
						</>
					)
				}
			] }
		/>
	</div>
);

const dealBatchData = (realSuccess, splitedData, type, params) => {
	const groupMergeList = [];
	// groupTidList: ["4268196938755440611", "4238160877458440611", "S1352602984764198912", "4238484987540440611"]
	// platform : "c2m"
	// sellerId : "1000000000046473"

	// 1、按SKU拆分：不可拆分订单 单独拆分为一个包裹
	// 2、按有货/无货拆分：不可拆分订单 单独拆分为一个包裹
	// 3、按现货/预售拆分：不可拆分订单 单独拆分为一个包裹
	// 4、指定商品拆分：不可拆分订单 单独拆分为一个包裹
	// !! 由于拆分后不知道每笔对应的拆分条件（比如：不知道哪笔是有货、哪笔是无货等）需要根据type进行二次确认
	// const isCombPack = [ESplitType.PRE_SALE, ESplitType.STOCK, ESplitType.SKU].includes(type);
	realSuccess?.forEach((pack) => {
		const { sellerId, platform, source } = pack;
		const togetherId = pack?.togetherId?.split('|') || [];
		const splitDataGroup = splitedData.filter((item) => togetherId.includes(item.ptTids?.[0])); // todo 有优化空间，但非必要
		const splitOrderTids = splitDataGroup.map((item) => item.ptTids?.[0]);
		// 单包裹单商品异常单，不参与拆分但参与合单
		const errorMap = errorMapList(ESplitType.SINGLE, true);
		pack?.trades?.forEach((trade) => {
			const errorInfo = errorMap.filter(item => item.rule({ ...pack, ...trade, trades: [trade] }))?.[0];
			console.log('errorInfo', errorInfo);
			if (!errorInfo?.msg) {
				splitOrderTids.push(trade.tid);
				splitDataGroup.push({ ...trade, trades: [trade] });
			}
		});
		console.log('splitedData', splitedData);
		console.log('splitDataGroup', splitDataGroup);
		console.log('splitOrderTids', splitOrderTids);
		console.log('splitOrderTidspack', pack);
		// 1、按SKU拆分：每个SKU拆分为一个包裹
		if (type == ESplitType.SKU) {
			// 按skuId对splitDataGroup进行分组
			const skuGroups = new Map();
			splitDataGroup?.forEach((trade) => {
				const skuId = trade?.trades?.[0]?.orders?.[0]?.skuId;
				if (!skuGroups.has(skuId)) {
					skuGroups.set(skuId, []);
				}
				skuGroups.get(skuId).push(trade);
			});
			console.log('skuGroups', skuGroups);
			// 根据分组生成groupMergeList
			skuGroups.forEach((trades) => {
				const firstTrade = trades[0];
				const { sellerId, platform, source } = firstTrade;
				const groupTidList = trades.map(trade => trade.tids?.[0] || trade.tid);
				groupMergeList.push({
					_debug: { type: 'SKU', trades },
					sellerId,
					platform,
					source,
					groupTidList
				});
			});
		}
		// 2、按有货/无货拆分：可拆分订单 按有货/无货拆分为多个包裹
		if (type == ESplitType.STOCK) {
			const hasStock = [];
			const noStock = [];
			splitDataGroup?.forEach((pack) => {
				const { orders } = pack?.trades?.[0];
				const order = orders?.[0];
				const lackNum = Number(order.num) - Number(order.alreadyAllotStockNum);
				lackNum <= 0 ? hasStock.push(order) : noStock.push(order);
			});
			[hasStock, noStock].forEach((list, index) => {
				if (list?.length) {
					groupMergeList.push({
						_debug: { type: '有无货', order: list, hasStock: !index },
						sellerId,
						platform,
						source,
						groupTidList: list.map(item => item.tid),
					});
				}
			});
		}
		// 3、按现货/预售拆分：不可拆分订单 单独拆分为一个包裹
		if (type == ESplitType.PRE_SALE) {
			const isPreSale = [];
			const notPreSale = [];
			splitDataGroup?.forEach((pack) => {
				const { orders } = pack?.trades?.[0];
				const order = orders?.[0];
				order?.isPreSale == 1 ? isPreSale.push(order) : notPreSale.push(order);
			});
			[isPreSale, notPreSale].forEach((list, index) => {
				if (list?.length) {
					groupMergeList.push({
						_debug: { type: '现货/预售', order: list, isPreSale: !index },
						sellerId,
						platform,
						source,
						groupTidList: list.map(item => item.tid),
					});
				}
			});
		}
		// 4、指定商品拆分：不可拆分订单 单独拆分为一个包裹
		if (type == ESplitType.ITEM) {
			const includeSku = [];
			const excludeSku = [];
			const { selectSkuId } = params;
			splitDataGroup?.forEach((pack) => {
				const { orders } = pack?.trades?.[0];
				const order = orders?.[0];
				selectSkuId.includes(order.skuId) ? includeSku.push(order) : excludeSku.push(order);
			});
			[includeSku, excludeSku].forEach((list, index) => {
				if (list?.length) {
					groupMergeList.push({
						_debug: { type: '指定商品拆分', order: list, includeSku: !index },
						sellerId,
						platform,
						source,
						groupTidList: list.map(item => item.tid),
					});
				}
			});
		}

		/** ***********************  不可拆分订单 单独拆分为一个合单包裹 start ******************************/
		const remainingOrdersTids = [];
		pack.trades?.forEach((trade) => {
			// 未参与合单的单独成一单
			if (!splitOrderTids.includes(trade.tid)) {
				remainingOrdersTids.push(trade.tid);
			}
		});
		if (remainingOrdersTids.length) {
			groupMergeList.push({
				_debug: { type: '剩余不可单独拆分' },
				sellerId,
				platform,
				source,
				groupTidList: remainingOrdersTids
			});
		}
		/** ***********************  不可拆分订单 单独拆分为一个合单包裹 end ******************************/
	});
	console.log('groupMergeListgroupMergeList', groupMergeList);
	return groupMergeList;
};
export const BatchSplit = NiceModal.create((props: BatchSplitProps) => {
	const { onOk, ...rest } = props;
	const { systemSetting, userInfo } = userStore;
	const [type, setType] = useState();
	const [allowMerge, setAllowMerge] = useState(false);
	const [selectSkuId, setSelectSkuId] = useState(() => []);
	const [selectSkus, setSelectSkus] = useState(() => []);
	const [params, setParams] = useState({ selectSkuId, limitWeight: 0, stepWeight: 0, stepNumber: 0, limitNumber: 0 });
	const modal = useModal();
	const { userSetting } = userStore;
	const isKg = userSetting?.weightUnit == weightUnit.显示kg;
	const selectItem = () => NiceModal.show(selectItemsModal, {
		checkedList: rest.checkedList,
		selectSkuId,
		onOk: (data) => { setSelectSkuId(data); },
	} as any); 

	useEffect(() => {
		const selectData = getAllSku(rest.checkedList as any, selectSkuId);
		setSelectSkus(selectData);
		setParams(pre => ({ ...pre, selectSkuId }));
	}, [JSON.stringify(selectSkuId)]);
	const getLimitText = () => {
		return <span style={ { color: '#FD8204' } }>（暂不支持合并订单拆分）</span>;
	};
	return (
		<Modal 
			centered
			title={ (
				<span>
					批量拆分订单
					<a
						onClick={ () => {
							sendPoint(Pointer.拆单_点击教程);
							window.open('https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/dngfwts6h1u2zp23?singleDoc#');
						} }
						style={ { fontSize: '12px', marginLeft: '16px' } }
					>使用教程
					</a>
				</span>
			) }
			okText="确定拆分"
			width={ 1000 }
			getContainer={ () => document.getElementsByClassName('ka-content')?.[1] as any }
			visible={ modal.visible }
			onCancel={ modal.hide }
			afterClose={ modal.remove }
			onOk={ () => {
				if (!type) {
					message.warn('请选择拆单策略后进行批量拆单');
					return; 
				}
				if (type == ESplitType.WEIGHT) {
					if (!params.limitWeight || !params.stepWeight) {
						message.error('请输入拆分重量');
						return; 
					}
					if (params.limitWeight < params.stepWeight) {
						message.error('拆分重量不能大于订单总重量');
						return; 
					}
				}
				if (type == ESplitType.NUMBER) {
					if (!params.limitNumber || !params.stepNumber) {
						message.error('请输入拆分数量');
						return; 
					}
					if (params.limitNumber < params.stepNumber) {
						message.error('拆分数量不能大于订单总数量');
						return; 
					}
				}
				if (type == ESplitType.ITEM) {
					if (!params?.selectSkuId?.length) {
						message.error('请选择指定商品');
						return; 
					}
				}
				sendPoint(POINT[type]);
				onOk(type, params, allowMerge); 
				modal.hide();
			} }
			{ ...rest }
		>
			<div style={ { maxHeight: '450px', overflow: 'auto' } }>
				<Alert
					style={ { padding: "8px 16px", background: '#FFFBE6', border: '1px solid #FFE58F', marginBottom: '12px' } }
					description={ (
						<Flex>
							<div>说明：</div>
							<div>
								<div>1、如需拆分合并订单请勾选【合并订单允许继续拆分】，合单中不可拆分的部分将合并为一个包裹。</div>
								<div>2、赠品商品拆分为单独拆单后将按普通商品处理。</div>
								<div>
									<span>3、拆分后使用多单号发货时，因各平台发货规则不同，可能出现部分单号未上传情况。</span>
									<span
										className="r-c-primary r-pointer r-mr-8"
										onClick={ () => {
											local.set('splitOrderAutoRemark', true);
											events.emit('openAdvancedSetting');
										} }
									>开启申请单号自动备注
									</span>
									<Popover placement="bottom" content={ splitRuleContent }>
										<span className="r-c-primary r-pointer" onClick={ () => {} }>各平台拆分与发货规则</span>
									</Popover>
								</div>
							</div>
						</Flex>
					) }
				/>
				<Flex style={ { marginBottom: '12px' } }>
					<div>拆分限制：</div>
					<div>
						<Checkbox
							// disabled={ [ESplitType.WEIGHT, ESplitType.NUMBER].includes(type) }
							onChange={ (e) => setAllowMerge(e.target.checked) }
						>合并订单允许继续拆分
						</Checkbox>
						<Popover
							title={ null }
							content={ (
								<div style={ { width: '288px' } }>拆分订单再次查询后会根据自动合单策略进行合单，可开启
									<span style={ { color: '#FF0000' } }>「拆分订单支持合单」</span>与
									<span style={ { color: '#FF0000' } }>「手动合单结果保存」</span>保存拆分结果
									<span style={ { color: '#1890FF', cursor: 'pointer' } } onClick={ () => history.push('/settings/system?introName=mergeOrder') }>前往配置</span>
								</div>
							) }
						><QuestionCircleOutlined />
						</Popover>
					</div>
				</Flex>
				<Flex>
					<div>拆分规则：</div>
					<div style={ { flex: 1 } }>
						<Radio.Group onChange={ (e) => setType(e.target.value) } value={ type }>
							<Space direction="vertical" style={ { gap: '12px' } }>
								<Radio value={ ESplitType.SKU }>
									<Flex>
										<div>按SKU拆分:</div>
										<div className={ style.label } style={ { marginTop: 0, flex: 1 } }>多款拆分为单款。如订单有商品A*3，B*2，C*2，则拆分出来3个订单商品分别为A*3、B*2、C*2，合并订单拆分后，同规格将自动合并</div>
									</Flex>
								</Radio>
								<Radio value={ ESplitType.WEIGHT } >
									<div>
										按重量拆分:
										<span className={ style.label }>如：订单重量大于 3kg 后按 1kg 拆分，则拆分出来3笔总重将≤1kg的订单, 如商品无法拆分则拆分失败</span>
									</div>
									<div className="r-mt-12">
										订单总重量大于
										<InputNumber className="r-ml-6 r-mr-6" size="small" min={ 0 } onChange={ (e) => setParams({ ...params, limitWeight: e }) } />
										{isKg ? 'kg' : 'g'}后,按
										<InputNumber
											className="r-ml-6 r-mr-6"
											size="small"
											min={ 0 }
											onChange={ (e) => {
												setParams({ ...params, stepWeight: e });
											} }
										/>{isKg ? 'kg' : 'g'}进行拆分
										{getLimitText()}
									</div>
								</Radio>
								{
									userInfo?.version === stockVersion.库存版
										? (
											<Radio
												disabled={ systemSetting.orderPreOccupiedStock == orderPreOccupiedStock.不占用库存 }
												value={ ESplitType.STOCK }
											>按有货无货拆分:
												<span className={ style.label }>
													需开启预占库存，系统将拆出库存不足的商品为一个订单,商品未绑定时按缺货处理
												</span>
											</Radio>
										) : null
								}
								<Radio value={ ESplitType.PRE_SALE }>按现货、预售进行拆分:
									<span className={ style.label }>订单中包含现货+预售商品时，将拆分为现货订单和预售订单</span>
								</Radio>
								<Radio value={ ESplitType.NUMBER }>
									<div>
										按商品数量拆分:
										<span className={ style.label }>按数量进行拆单，将按照指定的数量拆分成多个订单</span>
									</div>
									<div className="r-mt-12">
										订单商品数量大于
										<InputNumber className="r-ml-6 r-mr-6" size="small" min={ 0 } onChange={ (e) => setParams({ ...params, limitNumber: e }) } />
										时,将按每单
										<InputNumber
											className="r-ml-6 r-mr-6"
											size="small"
											min={ 0 }
											onChange={ (e) => {
												setParams({ ...params, stepNumber: e });
											} }
										/>个进行拆分
										{getLimitText()}
									</div>
								</Radio>
								<Radio value={ ESplitType.ITEM }>
									<div>
										指定商品拆分:
										<span className={ style.label }>
											当订单中包含以下商品时，系统自动将指定商品从原订单拆分出去
										</span>
									</div>
									<div className="r-mt-12">
										<div>
											添加拆分商品
											<Button type="link" onClick={ selectItem }>选择商品</Button>
										</div>
										{selectSkus?.length
											? (
												<Table
													size="middle"
													locale={ { emptyText: <Empty description="暂无数据" /> } }
													className={ style.splitTable }
													scroll={ { y: 260 } }
													rowKey={ (record) => record.skuId }
													columns={ [
														{
															title: '序号',
															dataIndex: 'index',
															key: 'index',
															width: 48,
															render: (_, __, index) => {
																return (
																	<span>{ index + 1 }</span>
																);
															}
														},
														{
															title: '产品内容',
															dataIndex: 'trades',
															key: 'trades',
															render: (_, order) => {
																return (
																	<PackageOrdersCom
																		pack={ {
																			productOrders: [order]
																		} as any }
																		isCancelSplit
																		colWidth={ 500 }
																	/>
																);
															}
														},
														{
															title: '操作',
															dataIndex: 'label',
															align: 'center',
															key: 'label',
															fixed: 'right',
															width: 100,
															render: (_, record) => {
																return (
																	<Button
																		danger
																		type="link"
																		onClick={ () => {
																			console.log('selectSkuId', selectSkuId, record.skuId);
																			setSelectSkuId(() => selectSkuId.filter(s => s != record.skuId));
																		} }
																	>删除
																	</Button>
																);
															}
														}
													] }
													dataSource={ selectSkus }
													pagination={ false }
												/>
											)
											: null}
										
									</div>
								</Radio>
							</Space>
						</Radio.Group>
					</div>
						
				</Flex>
			</div>
		</Modal>
	);
});

/**
 * 批量拆单进度
 */
// 一次发送5个
const LIMIT_NUM = 5;
export const SplitMsgProcess = NiceModal.create((props: SplitMsgProps) => {
	const { type, failedOrderList, unDealOrderList, passOrderList, params, allowMerge } = props;
	const modal = useModal();
	const [orderSplitInfos] = useState(cloneDeep(props.orderSplitInfos));
	const [num, setNum, getNum] = useGetState(0);
	const total = cloneDeep([...props.orderSplitInfos])?.length || 0;

	// 剔除无需拆单的列表
	const unDealOrderIds = unDealOrderList.map(order => order.tid);
	// 处理togetherId可能包含多个id的情况
	let realSuccess = passOrderList.filter(order => {
		const togetherIds = order.togetherId?.split('|') || [];
		// 只要有一个id不在unDealOrderIds中就保留
		return !togetherIds.every(id => unDealOrderIds.includes(id));
	});
	const disPlayUnDealOrderList = unDealOrderList.filter(order => order.limitType != 'merge');
	console.log('realSuccess', realSuccess, unDealOrderList, disPlayUnDealOrderList, unDealOrderIds, passOrderList, allowMerge);
	const sendData = async() => {
		const splitData = [];
		const totalOrder = [];
		while (orderSplitInfos.length > 0) {
			const sendOrderSplitInfos = orderSplitInfos.splice(0, LIMIT_NUM);
			if (sendOrderSplitInfos.length) {
				try {
					const res = await TradeOrderSplitOrderSplitApi({ orderSplitInfos: sendOrderSplitInfos });
					// 如果不允许合并，走旧逻辑
					if (!allowMerge) {
						for (const pack of sendOrderSplitInfos) {
							const result = {
								cancelOrHideTidList: [pack.tid],
								failedList: [],
								packageInfoVos: res?.packageInfoVos?.filter(s => s.ptTids?.includes(pack.tid))
							};
							await insertOrder(result as any, pack);
							console.log('result', getNum(), total, getNum() / total, orderSplitInfos.length);
						}
					}
					totalOrder.push(...sendOrderSplitInfos);
					splitData.push(...res?.packageInfoVos);
					customLogPost(true, '拆单', { sendOrderSplitInfos });
					if (res?.failedList?.length) {
						failedOrderList.push(...res.failedList);
					}
					setNum(pre => pre + sendOrderSplitInfos.length);
				} catch (error) {
					realSuccess = [];
					failedOrderList.push(...sendOrderSplitInfos);
					console.log('error', error);
					customLogPost(true, '拆单失败', { sendOrderSplitInfos, error });
				}
			}
		}
		if (allowMerge) {
			finishSplit(realSuccess, splitData, totalOrder);
		}
	};
	const finishSplit = async(realSuccess, splitData, orderSplitInfos) => {
		// 整体合单
		// await insertOrder(result as any, pack);
		const groupMergeList = dealBatchData(realSuccess, splitData, type, params);
		console.log('dealBachDatadealBachData', realSuccess, splitData, groupMergeList, orderSplitInfos);
		// const mergeRes = await splitMergeOrder(newData, newSplitData);
		if (groupMergeList.length) {
			try {
				const res = await TradeOrderSplitManualMergeTradeApi({ groupMergeList });
				console.log('合单返回结果', res);
				customLogPostForMerge({ groupMergeList, result: res });
				res.cancelOrHideTidList = [];
				// 创建一个Set来跟踪已使用过的packageInfoVos索引
				const usedPackageIndices = new Set();
				realSuccess.forEach(pack => {
					const ptTidsSource = pack.ptTids?.[0]?.split('|') || [];
					const insertPack = {
						packageInfoVos: res.packageInfoVos?.filter((r, index) => {
							const ptTidsResult = r.ptTids?.[0]?.split('|') || [];
							const matches = ptTidsResult.every(ptTid => ptTidsSource.includes(ptTid));
							// 如果匹配且之前未使用过，则标记为已使用并返回true
							if (matches && !usedPackageIndices.has(index)) {
								usedPackageIndices.add(index);
								return true;
							}
							return false;
						}),
						cancelOrHideTidList: [pack.togetherId]
					};
					console.log('insertPack', insertPack, pack);
					insertOrder(insertPack, pack);
				});
				if (res) {
					// message.success('合单成功');
				} else {
					message.warn(res.msg || '合单失败');
				}
			} catch (err) {
				customLogPostForMerge({ groupMergeList, result: err });
				message.warn('合单失败，请稍后重试');
			}
		} else {
			for (const pack of orderSplitInfos) {
				const result = {
					cancelOrHideTidList: [pack.tid],
					failedList: [],
					packageInfoVos: splitData.filter(s => s.ptTids?.includes(pack.tid))
				};
				await insertOrder(result, pack);
			}
		}
		// modal.hide();
	};
	const handleCopyOk = () => {
		const ptTids = [...failedOrderList, ...disPlayUnDealOrderList].map(i => i.ptTid).join(',');
		copyToPaste(ptTids, '复制成功', true);
	};
	useEffect(() => {
		if (orderSplitInfos?.length > 0) {
			sendData();
		}
		// 不存在可拆单订单，则直接走合并逻辑
		// 过滤掉orderSplitInfos中的订单，避免重复处理
		const orderSplitTids = props.orderSplitInfos?.map(item => item.tid) || [];
		const filteredPassOrderList = passOrderList.filter(order => {
			const togetherIds = order.togetherId?.split('|') || [];
			return !togetherIds.some(id => orderSplitTids.includes(id));
		});
		console.log('filteredPassOrderList', filteredPassOrderList, orderSplitTids, passOrderList, orderSplitInfos);
		if (filteredPassOrderList.length) {
			finishSplit(filteredPassOrderList, [], []);
		}
	}, []);
	console.log('realSuccess', getNum(), total, realSuccess);
	return (
		<Modal
			title="批量拆分订单"
			okText="我知道了"
			cancelButtonProps={ { hidden: true } }
			width={ 680 }
			visible={ modal.visible }
			onCancel={ modal.hide }
			afterClose={ modal.remove }
			onOk={ () => {
				modal.hide();
			} }
		>
			<div>
				{
					(getNum() / total) == 1 || !realSuccess.length
						? (
							<div className="r-flex">
								<div>
									<div className={ style.errorInfo }>
										<div className="r-mr-12">成功<span className={ style.success }>{passOrderList.length}</span>单</div>
										<div>失败<span className={ style.error }>{(disPlayUnDealOrderList.length || 0) + (failedOrderList.length || 0)}</span>单</div>
									</div>
									{
										(failedOrderList.length || disPlayUnDealOrderList.length)
											?												(
												<>
													<Table
														size="middle"
														className={ style.splitTable }
														scroll={ { y: 260 } }
														locale={ { emptyText: <Empty description="暂无失败数据" /> } }
														rowKey={ (record) => record.tid }
														columns={ [
															{
																title: '订单编号',
																dataIndex: 'ptTid',
																key: 'ptTid',
																render: (text) => {
																	return (
																		<div>
																			<span>{text}</span>
																		</div>
																	);
																}
															},
															{
																title: '失败原因',
																dataIndex: 'msg',
																key: 'msg',
																render: (text, record) => {
																	return (
																		<div className="r-ml-4">{text || record.message}</div>
																	);
																}
															}
														] }
														dataSource={ [...failedOrderList, ...disPlayUnDealOrderList] }
														pagination={ false }
													/>
													<Button type="link" onClick={ handleCopyOk } style={ { paddingLeft: 0 } }>复制失败的订单号</Button>
												</>
											)
											: null
									}
								</div>
							</div>
						) : (
							<div style={ { textAlign: 'center' } }>
								<Progress type="circle" percent={ Number(((getNum() / total) * 100).toFixed(0)) } />
							</div>
						)
				}
			</div>
		</Modal>
	);
});
/**
 * 拆单错误提示
 */
export const SplitMsg = NiceModal.create((props: SplitMsgProps) => {
	const { failedOrderList, passOrderList, ...rest } = props;
	const modal = useModal();
	const handleCopyOk = () => {
		const ptTids = failedOrderList.map(i => i.ptTid).join('\n');
		copyToPaste(ptTids, '复制成功', true);
	};
	console.log('failedOrderList', failedOrderList);
	return (
		<Modal
			title="批量拆分订单"
			okText="我知道了"
			cancelButtonProps={ { hidden: true } }
			width={ 680 }
			visible={ modal.visible }
			onCancel={ modal.hide }
			afterClose={ modal.remove }
			onOk={ () => {
				modal.hide();
			} }
			{ ...rest }
		>
			<div className="r-flex">
				<div>
					<div className={ style.errorInfo }>
						<div className="r-mr-12">成功<span className={ style.success }>{passOrderList.length}</span>单</div>
						<div>失败<span className={ style.error }>{failedOrderList.length}</span>单</div>
					</div>
					<Table
						size="middle"
						className={ style.splitTable }
						scroll={ { y: 260 } }
						rowKey={ (record) => record.tid }
						columns={ [
							{
								title: '订单编号',
								dataIndex: 'ptTid',
								key: 'ptTid',
								render: (text) => {
									return (
										<div>
											<span>{text}</span>
										</div>
									);
								}
							},
							{
								title: '失败原因',
								dataIndex: 'msg',
								key: 'msg',
								render: (text, record) => {
									return (
										<div className="r-ml-4">{text || record.message}</div>
									);
								}
							}
						] }
						dataSource={ failedOrderList }
						pagination={ false }
					/>
					<Button type="link" onClick={ handleCopyOk } style={ { paddingLeft: 0 } }>复制失败的订单号</Button>
				</div>
			</div>
		</Modal>
	);
});

/**
 * 取消拆单
 */
export const CancelSplit = NiceModal.create((props: CancelSplitProps) => {
	const { pack } = props;
	const [data, setData] = useState<IPackage[]>(() => []);
	const [loading, setLoading] = useState<boolean>(false);
	const modal = useModal();
	useEffect(() => {
		const ptTid = Array.from(new Set([...pack.trades.map(trade => trade.ptTid)])).join(',');
		if (ptTid) {
			TradeQueryTradeApi({
				pageNo: 1,
				pageSize: 1000,
				startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
				endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
				querySource: 1,
				ptTid
			}).then((res) => {
				handlePackageList(res?.data?.list as any).then(data => {
					setData(data as any);
				});
			});
		}
	}, [pack]);

	const cancelBtnSubmit = () => {
		// 取消拆单，需要先取消合并订单
		if (pack?.isSplit && pack?.isMerge) {
			message.error('请先取消合并订单！');
			return;
		}
		if (loading) return;
		setLoading(true);
		const { sellerId, platform } = pack;
		const ptTid = Array.from(new Set([...pack.trades.map(trade => trade.ptTid)])).join(',');
		const tidList = [];
		data.forEach(trade => { trade.tids.forEach(tid => tidList.push(tid)); });
		TradeOrderSplitCancelSplitApi({
			cancelSplitInfos: {	sellerId, platform, ptTid, tidList } as any
		}).then(res => {
			insertOrder(res, pack);
			modal.hide();
		}).finally(() => {
			setLoading(false);
		});
	};
	const trade = pack?.trades?.[0];
	const ptTid = Array.from(new Set([...pack.trades.map(trade => trade.ptTid)])).join(',');

	return (
		<Modal
			title="取消拆单"
			okText="取消拆单"
			width={ 1000 }
			visible={ modal.visible }
			okButtonProps={ { loading } }
			onCancel={ modal.hide }
			afterClose={ modal.remove }
			onOk={ cancelBtnSubmit }
		>
			<div>
				<div className="r-flex">
					<div>
						<div className={ style.errorInfo }>
							<Descriptions column={ 2 }>
								<Descriptions.Item label="平台编号">
									<Dropdown placement="topCenter" overlay={ copyMenu(() => copyToPaste(ptTid)) }>
										<span>{ptTid}</span>
									</Dropdown>
								</Descriptions.Item>
								<Descriptions.Item label="昵称">{ pack.buyerNick }</Descriptions.Item>
								<Descriptions.Item label="收件人信息" span={ 2 }>
									<ReceiverAddressCom pack={ { ...pack, ydNoSet: [] } } minWidth={ 700 } isOneLine />
								</Descriptions.Item>
								<Descriptions.Item label="留言" span={ 2 }>
									<span>{trade.buyerMessage}</span>
								</Descriptions.Item>
								<Descriptions.Item label="旗帜/备注" span={ 2 }>
									{(trade.sellerMemo || (!trade.sellerMemo && trade.sellerMemoFlag !== '0')) ? (
										<span>{getTradeFlag(0, null, trade.sellerMemoFlag)} {getTradeFlagTag(trade.sellerMemoFlag, trade?.sellerFlagTag)} {trade.sellerMemo}</span>
									) : ''}
								</Descriptions.Item>
							</Descriptions>
						</div>
						<Table
							size="middle"
							loading={ !data.length }
							locale={ { emptyText: <Empty description="暂无数据" /> } }
							className={ style.splitTable }
							scroll={ { y: 260 } }
							rowKey={ (record) => record.togetherId }
							columns={ [
								{
									title: '序号',
									dataIndex: 'index',
									key: 'index',
									width: 48,
									render: (_, __, index) => {
										return (
											<span>{ index + 1 }</span>
										);
									}
								},
								{
									title: '订单编号',
									dataIndex: 'ptTids',
									key: 'ptTids',
									render: (text) => <span>{ text?.join(',') }</span>
								},
								{
									title: '产品内容',
									dataIndex: 'trades',
									key: 'trades',
									width: 300,
									render: (trades, pack) => {
										return <PackageOrdersCom pack={ pack as any } isCancelSplit colWidth={ 300 } />;
									}
								},
								{
									title: '订单状态',
									dataIndex: 'msg',
									key: 'msg',
									render: (_, record) => {
										return (
											<div>
												{getTradeStatusLabel(record?.trades?.[0]?.status)}
											</div>
										);
									}
								}, {
									title: '打印状态',
									dataIndex: 'waybillPrintStatus',
									key: 'waybillPrintStatus',
									render: (_, pack) => {
										if (pack.waybillPrintStatus == 'none' && pack.shipListPrintStatus == 'none') {
											return '未打印';
										}
										const { labelPrintStatus, ...res } = pack;
										return (
											<PackPrintStatus { ...res as any } />
										);
									}
								}, {
									title: '订单标签',
									dataIndex: 'label',
									key: 'label',
									render: (_, record) => {
										return (
											<TradeLabelCom pack={ record as any } colWidth={ 120 } />
										);
									}
								}
							] }
							dataSource={ data }
							pagination={ false }
						/>
					</div>
				</div>
			</div>
		</Modal>
	);
});

/**
 * 关闭订单
 */
export const closeOrder = NiceModal.create((props: SplitMsgProps) => {
	const { failedOrderList, closeSystemOrderInfoList } = props;
	const modal = useModal();
	const [orderSplitInfos] = useState(cloneDeep(props.closeSystemOrderInfoList));
	const [num, setNum, getNum] = useGetState(0);
	const total = cloneDeep([...props.closeSystemOrderInfoList])?.length || 0;
	useEffect(() => { sendData(); }, []);
	let realSuccess = closeSystemOrderInfoList;
	console.log('realSuccess', realSuccess);
	const sendData = async() => {
		while (orderSplitInfos.length > 0) {
			const sendOrderSplitInfos = orderSplitInfos.splice(0, LIMIT_NUM);
			if (sendOrderSplitInfos.length) {
				try {
					const res = await TradeOrderSplitCloseSystemOrderApi({ closeSystemOrderInfoList: sendOrderSplitInfos } as any);
					// 回显数据
					customLogPost(true, '关闭订单', sendOrderSplitInfos);
					for (const pack of sendOrderSplitInfos) {
						const result = {
							cancelOrHideTidList: [pack.tid],
							failedList: [],
							packageInfoVos: res?.packageInfoVos?.filter(s => s.tids?.includes(pack.tid))
						};
						console.log('result', pack, result);
						await insertOrder(result as any, pack, 'close');
					}
					if (res?.failedList?.length) {
						failedOrderList.push(...res.failedList);
					}
					setNum(pre => pre + sendOrderSplitInfos.length);
					console.log('result', getNum(), total);
				} catch (error) {
					realSuccess = [];
					failedOrderList.push(...sendOrderSplitInfos);
					console.log('error', error);
					customLogPost(true, '关闭订单失败', { sendOrderSplitInfos, error });
				}
			}
		}
	};

	const handleCopyOk = () => {
		const ptTids = [...failedOrderList].map(i => i.ptTid).join(',');
		copyToPaste(ptTids, '复制成功', true);
	};
	return (
		<Modal
			title="关闭订单"
			okText="我知道了"
			cancelButtonProps={ { hidden: true } }
			width={ 680 }
			visible={ modal.visible }
			onCancel={ modal.hide }
			afterClose={ modal.remove }
			onOk={ modal.hide }
		>
			<div>
				{
					(getNum() / total) == 1 || !realSuccess.length
						? (
							<div className="r-flex">
								<div>
									<div className={ style.errorInfo }>
										<div className="r-mr-12">成功<span className={ style.success }>{realSuccess.length}</span>单</div>
										<div>失败<span className={ style.error }>{failedOrderList.length || 0}</span>单</div>
									</div>
									{
										(failedOrderList.length)
											? (
												<>
													<Table
														size="middle"
														className={ style.splitTable }
														scroll={ { y: 260 } }
														locale={ { emptyText: <Empty description="暂无失败数据" /> } }
														rowKey={ (record) => record.tid }
														columns={ [
															{
																title: '订单编号',
																dataIndex: 'ptTid',
																key: 'ptTid',
																render: (text) => {
																	return (
																		<div>
																			<span>{text}</span>
																		</div>
																	);
																}
															},
															{
																title: '失败原因',
																dataIndex: 'msg',
																key: 'msg',
																render: (text, record) => {
																	return (
																		<div className="r-ml-4">{text || record.message}</div>
																	);
																}
															}
														] }
														dataSource={ failedOrderList }
														pagination={ false }
													/>
													<Button type="link" onClick={ handleCopyOk } style={ { paddingLeft: 0 } }>复制失败的订单号</Button>
												</>
											)
											: null
									}
								</div>
							</div>
						) : (
							<div style={ { textAlign: 'center' } }>
								<Progress type="circle" percent={ Number(((getNum() / total) * 100).toFixed(0)) } />
							</div>
						)
				}
			</div>
		</Modal>
	);
});

/**
 * 选择指定商品
 */
const getAllSku = (packs: IPackage[], skuIds?: string[]) => {
	const skuMaps = new Map();
	packs.forEach(pack => {
		pack.trades?.forEach(trade => {
			trade.orders.forEach(order => {
				const key = order.skuId;
				if (!skuMaps.has(key)) {
					if (skuIds) {
						if (skuIds?.includes?.(key)) {
							skuMaps.set(key, order);
						}
					} else {
						skuMaps.set(key, order);
					}
				}
			});
		});
	});
	return Array.from(skuMaps.values());
};

export const selectItemsModal = NiceModal.create((props: any) => {
	const { checkedList, onOk } = props;
	const modal = useModal();
	const [data, setData] = useState(() => []);
	const [current, setCurrent] = useState(1);
	const [pageSize, setPageSize] = useState(20);
	const [selectSkuId, setSelectSkuId] = useState(() => props.selectSkuId);
	const [params, setParams] = useState({ itemKeyWords: '', skuKeyWords: '' });
	const search = () => {
		const itemKeyWords = params.itemKeyWords.trim();
		const skuKeyWords = params.skuKeyWords.trim();
		if (!itemKeyWords && !skuKeyWords) {
			setData(() => getAllSku(checkedList));
			return;
		}
		console.log('data', getAllSku(checkedList));
		// itemKeyWords: 商品名称/编码/简称: title/outerId/titleShort/sysItemAlias
		// skuKeyWords: 规格名称/编码/别名/货品编码：skuName/outerSkuId/sysSkuAlias/sysOuterSkuId
		if (params.itemKeyWords || params.skuKeyWords) {
			const filterData = getAllSku(checkedList).filter(sku => {
				const { title, outerId, titleShort, sysItemAlias, skuName, skuAlias, outerSkuId, sysSkuAlias, sysOuterSkuId } = sku;
				return (title?.includes?.(itemKeyWords)
					|| outerId?.includes?.(itemKeyWords)
					|| titleShort?.includes?.(itemKeyWords)
					|| sysItemAlias?.includes?.(itemKeyWords))
					&& (skuName?.includes?.(skuKeyWords)
					|| outerSkuId?.includes?.(skuKeyWords)
					|| skuAlias?.includes?.(skuKeyWords)
					|| sysSkuAlias?.includes?.(skuKeyWords)
					|| sysOuterSkuId?.includes?.(skuKeyWords));
			});
			setData(() => filterData);
		} 
	};

	const reset = () => {
		setParams({ itemKeyWords: '', skuKeyWords: '' });
		setData(() => getAllSku(checkedList));
	};

	const onSelectChange = (selectSkuId: React.Key[]) => {
		selectSkuId.forEach(skuId => {
			selectRow(skuId);
		});
	};
	const selectRow = (skuId) => {
		console.log('skuId', skuId, selectSkuId);
		const newIds = [...selectSkuId];
		if (selectSkuId.indexOf(skuId) >= 0) {
			newIds.splice(selectSkuId.indexOf(skuId), 1);
		} else {
			newIds.push(skuId);
		}
		setSelectSkuId(() => ([...newIds]));
	};
	
	useEffect(() => {
		setData(() => getAllSku(checkedList));
	}, [checkedList]);
	
	console.log('data', selectSkuId, params, data);
	
	return (
		<Modal
			title="拆分商品"
			okText="保存"
			cancelButtonProps={ { hidden: true } }
			width={ 680 }
			visible={ modal.visible }
			onCancel={ modal.hide }
			afterClose={ modal.remove }
			onOk={ () => {
				modal.hide();
				onOk(selectSkuId);
			} }
		>
			<div>
				<Form layout="inline" style={ { marginBottom: '12px' } }>
					<div style={ { flex: 1, display: 'flex', justifyContent: 'space-between' } }>
						<Form.Item>
							<Input value={ params.itemKeyWords } style={ { width: '222px' } } placeholder="商品名称/编码/简称" onChange={ e => setParams(pre => ({ ...pre, itemKeyWords: e.target.value })) } />
						</Form.Item>
						<Form.Item>
							<Input value={ params.skuKeyWords } style={ { width: '222px' } } placeholder="规格名称/编码/别名/货品编码" onChange={ e => setParams(pre => ({ ...pre, skuKeyWords: e.target.value })) } />
						</Form.Item>
						<Form.Item style={ { marginRight: '0' } }>
							<Button style={ { marginRight: '12px' } } type="primary" onClick={ search }>查询</Button>
							<Button onClick={ reset }>重置</Button>
						</Form.Item>
					</div>
				</Form>
				{/* <ConfigProvider locale={ zhCN }> */}
				<Table
					rowSelection={ {
						hideSelectAll: true,
						selectedRowKeys: selectSkuId,
						onChange: onSelectChange,
					} }
					onRow={ record => ({
						onClick: () => selectRow(record.skuId)
					}) }
					size="middle"
					locale={ { emptyText: <Empty description="暂无数据" /> } }
					className={ style.splitTable }
					scroll={ { y: 260 } }
					rowKey={ (record:any) => record.skuId }
					columns={ [
						{
							title: '序号',
							dataIndex: 'index',
							key: 'index',
							width: 48,
							render: (_, __, index) => {
								return (
									<span>{ index + 1 }</span>
								);
							}
						},
						{
							title: '产品内容',
							dataIndex: 'trades',
							key: 'trades',
							render: (_, order) => {
								return (
									<PackageOrdersCom
										pack={ {
											productOrders: [order]
										} as any }
										isCancelSplit
										colWidth={ 300 }
									/>
								);
							}
						}
					] }
					dataSource={ data }
					onChange={ (pagination) => {
						console.log('pagination', pagination);
						setCurrent(pagination.current);
						setPageSize(pagination.pageSize);
					} }
					pagination={ {
						pageSize,
						current,
						total: data.length,
						showQuickJumper: true,
						showSizeChanger: true
					} }
				/>
				{/* </ConfigProvider> */}
			</div>
		</Modal>
	);
});