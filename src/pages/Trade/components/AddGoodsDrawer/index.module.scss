.addGoodsDrawer {
    .deawerTitle {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.85);
    }

    .logClose {
        font-size: 14px;
        padding: 0 0 0 10px;
        cursor: pointer;

        .close {
            color: rgba(0, 0, 0, 0.45);

            &:hover {
                color: rgba(0, 0, 0, 0.85);
            }
        }
    }

    .checkList{
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-bottom: 20px;

        .listType{
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 16px;
        }
        .list{
            flex: 1;
            display: flex;
            align-items: center;
            border: 1px solid #D9D9D9;
            padding: 4px;
            gap: 4px;
            background: #FFFFFF;
            box-sizing: border-box;
            
            .listNum{
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
                flex-shrink: 0;
            }

            .listMain{
                flex: 1;
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                max-height: 60px;
                overflow-y: auto;
            }

            .listItem{
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 0px 4px 0px 4px;
                gap: 4px;
                border-radius: 2px;
                background: #F5F5F5;
                box-sizing: border-box;
                border: 1px solid #F0F0F0;
                font-size: 12px;
                line-height: 20px;
                color: rgba(0, 0, 0, 0.85);

                .itemClose{
                    cursor: pointer;
                    font-size: 16px;
                    color:  rgba(0, 0, 0, 0.45);
                    padding: 0 4px;
                }
            }
        }
    }

    :global {
        .ant-drawer-body {
			padding-bottom: 0px;
		}

        .ant-drawer-content-wrapper {
            min-width: 1200px !important;
            width: 83vw !important;
        }

        .search-btn-wrap{
            // 确认按钮的margin-left
            .ant-btn-primary{
                margin-left: 0px!important;
            }
        }

    }
}