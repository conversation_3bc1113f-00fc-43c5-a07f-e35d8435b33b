import React, { useEffect, useState, useMemo, useRef } from 'react';
import { Drawer, Button, Form, Input, Select, Modal } from 'antd';
import { CloseOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { observer } from 'mobx-react';
import { throttle } from 'lodash';
import message from "@/components/message";
import userStore from '@/stores/user';
import history from "@/utils/history";
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { platform } from '@/types/schemas/common';
import { Base } from "@/types/schemas";
import GoodsList from '@/pages/Trade/components/ChangeAndAddGoods/ChooseGoodsModal/goodsList';
import SysItemsList from '@/pages/Trade/components/ChangeAndAddGoods/ChooseGoodsModal/sysItemsList';
import { getTableScroll } from '@/utils/table';
import s from './index.module.scss';

export enum AddTypeEnum {
	平台商品 = 'goods',
	系统货品 = 'sysItems'
}

interface Iprops extends Base {
    visible: boolean;
    onOk?: (selectRows:any[], type:any)=>void;
    onClose?: ()=>void;
	type?:"checkbox" | "radio";
	defaultParams?:{
		platform?:platform,
		shopId?:string,
		itemTitle?:string,
	};
	disabled?: {key:string, value:any[]};
	hasCollect?:boolean;// 是否有收藏按钮，创建手工单添加了收藏按钮
	hasBatchedNum?: boolean, // 表格是否支持批量编辑数量
	addType?: AddTypeEnum, // 'goods' 商品，'sysItems' 货品
    maxAddCount?: number // 可添加的最大数量
	addedCount?: number // 已添加的数量
    from?: string // 来源
    selectedDisabled?: boolean, // 是否禁用选中
    supplier?: {
		label: string,
		value: string
	}, // 供应商
    disabledCombined?: { isDisable: boolean, tips: string }, // 是否禁用组合
    code?: string, // 货品编码
    autoSearch?: boolean, // 是否自动搜索
	drawerTitle?:any;
}

// 备货单添加商品抽屉
const AddGoodsDrawer = (props: Iprops) => {
	const { 
		visible, 
		addType, 
		onClose, 
		onOk, 
		hasBatchedNum = false, 
		hasCollect = false, 
		autoSearch, 
		drawerTitle 
	} = props;

	const searchRef = useRef(null);
	const searchRef2 = useRef(null);

	const showTypeName = useMemo(() => {
		return addType == AddTypeEnum.平台商品 ? '商品' : '货品';
	}, [addType]);

	const { userInfo } = userStore;
	const { userId } = userInfo || {};

	const [selectRows, setSelectRows] = useState<any[]>([]);
	const [scrollY, setScrollY] = useState<any>(600);
	

	const customLogPost = (dataType: string, data: any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `备货单-添加商品: 【 ${dataType} 】`,
			data: {
				...data
			}
		});
	};

	const handleCancel = () => {
		onClose?.();
		setSelectRows([]);
		searchRef?.current?.setSelectedRowKeys([]);
		searchRef2?.current?.setSelectedRowKeys([]);
	};

	const handleOk = () => {
		if (selectRows.length === 0) {
			message.warning('请先选择要添加的商品');
			return;
		}
		onOk?.(selectRows, addType);
		handleCancel();
	};

	// 删除这条选项
	const handleRemoveItem = (item:any) => {
		let newSelectRows = selectRows?.filter(d => d.rowId != item.rowId);
		setSelectRows(newSelectRows);
		if (addType == AddTypeEnum.平台商品) {
			searchRef?.current?.setSelectedRowKeys(newSelectRows.map(item => item.rowId));
		} else {
			searchRef2?.current?.setSelectedRowKeys(newSelectRows.map(item => item.rowId));
		}
	};

	const onChange = (keys: string[], rows: any[], currentPageAllRowIds: any[]) => {
		// 处理不在当前页的已选中项
		const otherPageSelectedRows = selectRows.filter(item => !currentPageAllRowIds.includes(item.rowId));

		// 处理当前页的选中项
		const currentPageSelectedRows = rows?.map(row => ({
			...row,
			num: row.num || 1,
			originNode: null
		})) || [];

		// 合并结果
		const finalRows = [...otherPageSelectedRows, ...currentPageSelectedRows];
		setSelectRows(finalRows);
	};

	// 计算虚拟表格高度
	const resizeFn = useMemo(() => throttle(() => {
		setScrollY(getTableScroll(112, 'addGoodsDrawer'));
	}, 100), 
	[]);

	// 监听路由变化，关闭抽屉
	useEffect(() => {
		if (!visible) return;

		const unlisten = history.listen(() => {
			onClose?.();
		});
		resizeFn();

		return unlisten;
	}, [visible]);

	useEffect(() => {
		window.addEventListener('resize', resizeFn);
		return () => {
			window.removeEventListener('resize', resizeFn);
		};
	}, []);

	useEffect(() => {
		resizeFn();
	}, [selectRows]);

	return (
		<Drawer
			id="addGoodsDrawer"
			title={ (
				<div className="r-flex r-ai-c r-jc-sb">
					<div className={ s.deawerTitle }>
						{
							drawerTitle ? (
								<span className="r-bold">{drawerTitle}</span>
							) : (
								<span className="r-bold">{ addType == AddTypeEnum.平台商品 ? '添加平台商品' : '添加货品' }</span>
							)
						}
					</div>
					<div onClick={ () => handleCancel() } className={ s.logClose }>
						<CloseOutlined size={ 14 } className={ s.close } />
					</div>
				</div>
			) }
			placement="right"
			size="large"
			closable={ false }
			keyboard={ false }
			maskClosable
			onClose={ handleCancel }
			visible={ visible }
			className={ s.addGoodsDrawer }
			destroyOnClose
			footer={ (
				<div className="r-flex r-gap-8 r-ai-c">
					<Button onClick={ handleCancel }>
						取消
					</Button>
					<Button onClick={ handleOk } type="primary" data-point={ Pointer.报表_备货单_添加商品_确定_点击 }>
						确定
					</Button>
				</div>
			) }
		>
			{/* 已选中的项 */}
			<div className={ s.checkList }>
				<div className={ s.listType }>要添加的{showTypeName}</div>
				<div className={ s.list }>
					<div className={ s.listNum }>已选择{selectRows?.length}个{showTypeName}：</div>
					<div className={ s.listMain }>
						{selectRows.map((item, index) => (
							<div className={ s.listItem } key={ item?.rowId }>
								{
									addType == AddTypeEnum.平台商品 ? (
										<span>{item?.numIid}-{item?.platformItemSkuList?.[0]?.skuId} *{item?.num || 1}</span>
									) : (
										<span>{item?.sysSkuList?.[0]?.skuOuterId} *{item?.num || 1}</span>
									)
								}
								<span className={ s.itemClose } onClick={ () => handleRemoveItem(item) }>×</span>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* 添加商品 */}
			{
				addType == AddTypeEnum.平台商品 ? (
					<GoodsList
						ref={ searchRef }
						showColumnKeys={ ['weight', 'costPrice', 'market', 'stall', 'supplierName', hasCollect && 'setCommon'].filter(Boolean) }
						selectedDisabled={ props.selectedDisabled }
						defaultParams={ props.defaultParams }
						disabled={ props.disabled }
						type={ props.type || 'checkbox' }
						onChange={ onChange }
						maxAddCount={ props.maxAddCount }
						addedCount={ props.addedCount }
						from={ props.from }
						hasBatchedNum={ hasBatchedNum }
						allSelectRows={ selectRows }
						scrollY={ scrollY }
						autoSearch={ autoSearch }
						returnSupplierInfo
					/>
				) : (
					<SysItemsList 
						ref={ searchRef2 }
						showColumnKeys={ ['market', 'stall', 'supplierName', hasCollect && 'setCommon'].filter(Boolean) }
						from={ props.from }
						supplier={ props.supplier } 
						maxAddCount={ props.maxAddCount } 
						disabledCombined={ props.disabledCombined } 
						addedCount={ props.addedCount } 
						disabled={ props.disabled }  
						type={ props.type || 'checkbox' } 
						onChange={ onChange } 
						hasBatchedNum={ hasBatchedNum }
						allSelectRows={ selectRows }
						code={ props.code }
						scrollY={ scrollY }
						autoSearch={ autoSearch }
					/>
				)
			}
		</Drawer>
	);
};

export default observer(AddGoodsDrawer);