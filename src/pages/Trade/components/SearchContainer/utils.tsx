import React from 'react';
import { <PERSON><PERSON>, List } from 'antd';
import Empty from 'antd/es/empty';
import { SysMemoModeEnum, TradeAdvancedSearchGetListResponse, TradeSearchConditionConfig } from "@/types/trade/search/search";
import s from './index.module.scss';
import userStore from '@/stores/user';
import { getTradePlatformLabel } from '../../utils';
import { getMirrorEnum } from '@/utils';
import { GOOD_STOCK_STATUS_OPTIONS, NEW_TIME_REMAINING_TYPE_OPTIONS, ORDER_PRINT_STATUS, ORDER_STATUS_OPTIONS, ORDER_TYPE_OPTIONS, REFUND_TYPE_OPTIONS, SELL_ATTR_OPTIONS, TRADE_EXCEPTION_INCLUDE_OPTIONS, TRADE_EXCEPTION_UNINCLUDE_OPTIONS, TRADE_LABEL_NORMAL_INCLUDE_OPTIONS, TRADE_LABEL_NORMAL_UNINCLUDE_OPTIONS, TRADE_LABEL_OTHER_INCLUDE_OPTIONS, TRADE_LABEL_OTHER_UNINCLUDE_OPTIONS } from './constants';
import { flagValueList } from './FlagAndMemoSelect/flagAndMemoSelect';
import { tradeStore } from '@/stores';
import { SystemMarkList } from './OrderMarkSelect';
import { itemInfoEnumValue, itemInfoQueryMap } from './itemInfoUtils';
import distributionStore from '@/stores/distribution';
import { getPrintNode, getValueByOldStatus } from './SearchCondition';
// import {defaultSmartExpressList} from './SmartExpressTemplateSelect'
import { getName } from './AddressSelect/addressUtils';

export enum OrderSourceEnum {
	平台订单 = 'platfromOrder',
	手工订单 = 'handOrder',
	分销推送 = 'scmOrder'
}

export enum includeIngColorOrSizeEnum {
	'颜色尺码包含(模糊)',
	'颜色尺码包含(精确)',
	'颜色尺码不包含(精确)',
	'颜色尺码不包含(模糊)',
}

export const getIncludeIngColorOrSize = (includeIngColorOrSizeTwo:includeIngColorOrSizeEnum) => {
	return [includeIngColorOrSizeEnum['颜色尺码包含(模糊)'], includeIngColorOrSizeEnum['颜色尺码包含(精确)']].includes(includeIngColorOrSizeTwo)
};

export const getIsColorAndSizePrecise = (includeIngColorOrSizeTwo:includeIngColorOrSizeEnum) => {
	return [includeIngColorOrSizeEnum['颜色尺码不包含(模糊)'], includeIngColorOrSizeEnum['颜色尺码包含(模糊)']].includes(includeIngColorOrSizeTwo)
};

export const renderRange = (min: string, max: string, unit?: string) => {
	if (min && max) {
		return <>介于{min}{unit}-{max}{unit}之间</>;
	} else if (!min && max) {
		return <>小于等于{max}{unit}</>;
	} else if (min && !max) {
		return <>大于等于{min}{unit}</>;
	}
	return <></>;
};

// 克转化为千克
export const gramsToKilograms = (gramsVal) => {
	const grams = parseFloat(gramsVal);
	if (typeof grams !== 'number' || isNaN(grams)) {
		return "";
	}

	let kilograms = grams / 1000;
	return kilograms.toFixed(3); // 四舍五入到小数点后三位
};

// 千克转化为克
export const kilogramsToGrams = (kiloVal) => {
	const kilo = parseFloat(kiloVal);
	if (typeof kilo !== 'number' || isNaN(kilo)) {
		return "";
	}
	return kilo * 1000;
};

export const renderKgRange = (min: string, max: string, isShowKg?: boolean) => {
	const minVal = isShowKg ? gramsToKilograms(min) : min;
	const maxVal = isShowKg ? gramsToKilograms(max) : max;
	const unit = isShowKg ? "kg" : "g";
	if (min && max) {
		return <>介于{minVal}{unit}-{maxVal}{unit}之间</>;
	} else if (!min && max) {
		return <>小于等于{maxVal}{unit}</>;
	} else if (min && !max) {
		return <>大于等于{minVal}{unit}</>;
	}
	return <></>;
};

export const TRADE_LABEL_INCLUDE_ARR = [...TRADE_LABEL_NORMAL_INCLUDE_OPTIONS, ...TRADE_LABEL_OTHER_INCLUDE_OPTIONS];
export const TRADE_LABEL_NOT_INCLUDE_ARR = [...TRADE_LABEL_NORMAL_UNINCLUDE_OPTIONS, ...TRADE_LABEL_OTHER_UNINCLUDE_OPTIONS];

export const goodsIncludeStatusLabelMap = {
	[itemInfoEnumValue.商品包含]: '商品',
	[itemInfoEnumValue.商品不包含]: '商品',
	[itemInfoEnumValue.货品包含]: '货品',
	[itemInfoEnumValue.货品不包含]: '货品',

};

export const getRuleTooltip = (rule: TradeAdvancedSearchGetListResponse["data"][number], conditionSet: TradeSearchConditionConfig[], cb: any, {isShowKg}:any) => {
	let contentList: any[] = [];
	try {
		const ruleContent = JSON.parse(rule.ruleContent);
		// if (ruleContent?.addressInfo?.id) {
		// 	const crtAddress = userArea?.find(s => s.id == ruleContent?.addressInfo?.id)||{}
		// 		ruleContent.addressInfo = {
		// 			...crtAddress,
		// 			name:crtAddress?.id ? getName(crtAddress) : ''
		// 		}
		// }
		conditionSet.forEach(conditionItem => {
			switch (conditionItem.condition) {
				case 'sysMemo':
					if (ruleContent?.sysMemoMode == SysMemoModeEnum.有线下备注) {
						let content = [<span className={ s["content-label"] }>有线下备注：</span>];
						content.push(
							<>{ruleContent?.sysMemoList?.join('、')}<br /></>
						);
						contentList.push(content);
					} else if (ruleContent?.sysMemoMode === SysMemoModeEnum.无线下备注) {
						let content = [<span className={ s["content-label"] }>无线下备注：</span>];
						contentList.push(content);
					}
					break;
				case 'timeType':
					if (ruleContent?.endTime) {
						let content = [<span className={ s["content-label"] }>截单时间：</span>];
						content.push(
							<>{`截止 ${ruleContent?.endTime} 的订单`}<br /></>
						);
						contentList.push(content);
					}
					if (ruleContent.payEndTime) {
						const timerArr = ruleContent.payEndTime.split(":");
						let content = [<span className={ s["content-label"] }>付款时间：</span>];
						content.push(
							<>{`超出付款时间${timerArr[0] == "undefined" ? "00" : timerArr[0]}时${timerArr[1] == "undefined" ? "00" : timerArr[1]}分${timerArr[2] == "undefined" ? "00" : timerArr[2]}秒的订单`}<br /></>
						);
						contentList.push(content);
					}
					break;
				
				case 'platform':
					if (ruleContent['platformInfo']?.plat_sellerIds?.length) {
						let content = [];
						const { shopList } = userStore;
						ruleContent['platformInfo'].plat_sellerIds.forEach((item, index) => {
							const [plat = '', sellerId = ''] = item.split('_');
							let shopItem = shopList.find(i => i.sellerId === sellerId);
							if (shopItem) {
								content.push(
									<>
										{getTradePlatformLabel(plat)}
										<span>{shopItem.sellerNick}{index !== ruleContent['platformInfo'].plat_sellerIds.length - 1 ? '、' : ''}</span>
									</>
								);
							}
						});
						if (content.length) {
							content.unshift([<span className={ s["content-label"] }>平台店铺：</span>]);
							content.push(<br />);
							contentList.push(content);
						}
					}
					break;

				case 'orderSource':
					if (ruleContent?.orderSource) {
						let content = [<span className={ s["content-label"] }>订单来源：</span>];
						content.push(
							<>{getMirrorEnum(OrderSourceEnum)[ruleContent?.orderSource]}<br /></>
						);
						contentList.push(content);

						if (ruleContent?.distributorUserIds?.length) {
							let _content = [];
							const { distributorList = [] } = distributionStore;
							let arr = [];
							ruleContent?.distributorUserIds.forEach(id => {
								let item = distributorList.find(item => item.saleUserId === id);
								if (item) {
									arr.push(item.saleName);
								}
							});
							_content.push([<span className={ s["content-label"] }>分销商：</span>]);
							_content.push(arr.join('、'));
							_content.push(<br />);
							contentList.push(_content);
						}
					}
					break;

				case 'tradeType':
					if (ruleContent?.tradeType && ruleContent?.tradeType !== ORDER_TYPE_OPTIONS.default) {
						let content = [<span className={ s["content-label"] }>订单类型：</span>];
						content.push(
							<>{ORDER_TYPE_OPTIONS.options.find(i => i.key === ruleContent?.tradeType)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'status':
					if (ruleContent?.status) {
						let content = [<span className={ s["content-label"] }>订单状态：</span>];
						content.push(
							<>{ORDER_STATUS_OPTIONS.options.find(i => i.key === ruleContent?.status)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'printStatus':
					if (ruleContent?.printStatus || ruleContent?.allPrintStatus) {
						const printObj = ruleContent?.allPrintStatus || getValueByOldStatus(ruleContent?.printStatus);
						const arr = [printObj?.printStatus, printObj?.fhdPrintStatus || '', printObj?.labelPrintStatus || ''].join(',');
						let content = [<span className={ s["content-label"] }>打印状态：</span>];
						console.log('printObj', printObj, arr);
						content.push(<>{ getPrintNode(arr, printObj)}</>);
						contentList.push(content);
					}
					break;

				case 'refundStatus':
					if (ruleContent?.refundStatus) {
						if (Array.isArray(ruleContent?.refundStatus)) {
							ruleContent.refundStatus = ruleContent?.refundStatus?.length == 1 ? ruleContent?.refundStatus[0] : '';
						}
						let content = [<span className={ s["content-label"] }>退款状态：</span>];
						content.push(
							<>{REFUND_TYPE_OPTIONS.options.find(i => i.key === ruleContent?.refundStatus)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'goodStockStatus':
					if (ruleContent?.goodStockStatus && ruleContent?.goodStockStatus !== GOOD_STOCK_STATUS_OPTIONS.default) {
						let content = [<span className={ s["content-label"] }>订单缺货状态：</span>];
						content.push(
							<>{GOOD_STOCK_STATUS_OPTIONS.options.find(i => i.key === ruleContent?.goodStockStatus)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'tid':
					if (ruleContent?.tid) {
						let content = [<span className={ s["content-label"] }>订单编号：</span>];
						content.push(
							<>{ruleContent?.tid.split(',').join('、')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'sid':
					if (ruleContent?.sid) {
						let content = [<span className={ s["content-label"] }>快递单号：</span>];
						content.push(
							<>{ruleContent?.sid.split(',').join('、')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'itemInclude': {
					const { goodsIncludeStatus } = ruleContent;
					const key = Object.keys(itemInfoQueryMap).find(key => key == goodsIncludeStatus);
					const label = itemInfoQueryMap[key]?.label;
					if (label) {
						const { shortNameIncludingList = [] } = ruleContent;
						if (shortNameIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(包含)：</span>];
							content.push(
								<>{shortNameIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					} else if (ruleContent?.goodsIncludeStatus == itemInfoEnumValue.商品包含 || ruleContent?.goodsIncludeStatus == itemInfoEnumValue.货品包含) {
						// * 不要改成includes 老版的goodsIncludeStatus是number类型
						const { goodsIncludeStatus } = ruleContent;
						let { shortNameIncludingList = [], skuIncludingList = [] } = ruleContent;
						let skuIncluding;
						let shortNameIncluding;
						const { itemSku } = ruleContent;
						if (itemSku?.length) {
							skuIncluding = itemSku[0].skuIncluding;
							shortNameIncluding = itemSku[0].shortNameIncluding;
						}
						if (skuIncluding && !skuIncludingList.length) skuIncludingList = [skuIncluding.trim()];
						if (shortNameIncluding && !shortNameIncludingList.length) shortNameIncludingList = [shortNameIncluding.trim()];
						const label = goodsIncludeStatusLabelMap[goodsIncludeStatus];
						if (shortNameIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(包含)：</span>];
							content.push(
								<>{shortNameIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}

						if (skuIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{goodsIncludeStatus == itemInfoEnumValue.商品包含 ? '' : '货品'}规格(包含)：</span>];
							content.push(
								<>{skuIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					}
					break;
				}

				case 'itemNotInclude': {
					const { goodsNotIncludeStatus } = ruleContent;
					const key = Object.keys(itemInfoQueryMap).find(key => key == goodsNotIncludeStatus);
					const label = itemInfoQueryMap[key]?.label;
					if (label) {
						const { shortNameNotIncludingList = [] } = ruleContent;
						if (shortNameNotIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(不包含)：</span>];
							content.push(
								<>{shortNameNotIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					} else if ([itemInfoEnumValue.商品不包含, itemInfoEnumValue.货品不包含].includes(goodsNotIncludeStatus)) {
						const { shortNameNotIncludingList = [], skuNotIncludingList = [] } = ruleContent;
						const label = goodsIncludeStatusLabelMap[goodsNotIncludeStatus];
						if (shortNameNotIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(不包含)：</span>];
							content.push(
								<>{shortNameNotIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}

						if (skuNotIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{goodsNotIncludeStatus === itemInfoEnumValue.商品不包含 ? '' : '货品'}规格(不包含)：</span>];
							content.push(
								<>{skuNotIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					}
					break;
				}


				case 'colorSizeIncludingList':
					if ('includeIngColorOrSizeTwo' in ruleContent) {
						ruleContent.includeIngColorOrSize = getIncludeIngColorOrSize(ruleContent?.includeIngColorOrSizeTwo)
						ruleContent.isColorAndSizePrecise = getIsColorAndSizePrecise(ruleContent?.includeIngColorOrSizeTwo)
					} else {
						ruleContent.isColorAndSizePrecise = !ruleContent?.precise?.includes("isPrecise")
					}
					if (ruleContent?.colorIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>颜色{ruleContent?.includeIngColorOrSize ? '包含' : '不包含'}({ruleContent?.isColorAndSizePrecise ? '模糊' : '精确'})：</span>];
						content.push(
							<>{ruleContent?.colorIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}

					if (ruleContent?.sizeIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>尺码{ruleContent?.includeIngColorOrSize ? '包含' : '不包含'}({ruleContent?.isColorAndSizePrecise ? '模糊' : '精确'})：</span>];
						content.push(
							<>{ruleContent?.sizeIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'flagValue':
					if (ruleContent?.flagValue) {
						if (ruleContent?.flagValue === '-1') {
							if (ruleContent?.buyerMessage) {
								let content = [<span className={ s["content-label"] }>留言：</span>];
								content.push(
									<>{ruleContent?.buyerMessage}<br /></>
								);
								contentList.push(content);
							}

							if (ruleContent?.sellerMemo || ruleContent?.sellerFlag?.length) {
								let content = [<span className={ s["content-label"] }>备注：</span>];
								let sellerFlagStr = ruleContent?.sellerFlag?.map(item => flagValueList.find(i => i.key === item)?.value)?.join(',');
								content.push(
									<>{ruleContent?.sellerMemo} {sellerFlagStr ? `[${sellerFlagStr}]` : ''}<br /></>
								);
								contentList.push(content);
							}
						} else {
							let content = [<span className={ s["content-label"] }>留言备注：</span>];
							let temp = flagValueList.find(i => i.key === ruleContent?.flagValue)?.value;
							if (!temp) {
								const flagList = tradeStore.flagList || [];
								temp = flagList?.find(i => i.selValue === ruleContent?.flagValue)?.comment || '自定义';
							}

							content.push(
								<>{temp}<br /></>
							);
							contentList.push(content);
						}
					} else {
						if (ruleContent?.sellerMemo || ruleContent?.sellerFlag?.length) {
							let content = [<span className={ s["content-label"] }>备注：</span>];
							let sellerFlagStr = ruleContent?.sellerFlag?.map(item => flagValueList.find(i => i.key === item)?.value)?.join(',');
							content.push(
								<>{ruleContent?.sellerMemo} {sellerFlagStr ? `[${sellerFlagStr}]` : ''}<br /></>
							);
							contentList.push(content);
						}
						if (ruleContent?.buyerMessage) {
							let content = [<span className={ s["content-label"] }>留言：</span>];
							content.push(
								<>{ruleContent?.buyerMessage}<br /></>
							);
							contentList.push(content);
						}
					}
					break;

				case 'bizMark':
					if (ruleContent?.bizMarkObj?.bizMark) {
						const { value } = ruleContent?.bizMarkObj;
						let content = [<span className={ s["content-label"] }>订单标记：</span>];
						let temp = SystemMarkList.find(i => i.value === value)?.name;
						if (!temp) {
							const markList = tradeStore.markList || [];
							temp = markList?.find(i => i.id === value)?.remark || '自定义';
						}

						content.push(
							<>{temp}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'buyerNick':
					if (ruleContent?.buyerNick) {
						let content = [<span className={ s["content-label"] }>买家昵称：</span>];
						content.push(
							<>{ruleContent?.buyerNick}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'areaContain':
					if (ruleContent?.addressInfo?.id) {
						let content = [<span className={ s["content-label"] }>省市区：</span>];
						content.push(
							<>{ruleContent?.addressInfo?.name}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'receiveName':
					if (ruleContent?.receiveName) {
						let content = [<span className={ s["content-label"] }>收件人：</span>];
						content.push(
							<>{ruleContent?.receiveName}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'mobile':
					if (ruleContent?.mobile) {
						let content = [<span className={ s["content-label"] }>手机号：</span>];
						content.push(
							<>{ruleContent?.mobile}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'residueSendTime':
					if (ruleContent?.customizeResidueSendTime) {
						let desc = NEW_TIME_REMAINING_TYPE_OPTIONS.options.find(item => item.key === ruleContent?.customizeResidueSendTime)?.value;

						if (!desc) {
							const [startTime, endTime] = ruleContent?.customizeResidueSendTime?.split('-');
							if (startTime === 'none') {
								desc = `剩余时间小于${endTime}小时`;
							} else if (endTime === 'none') {
								desc = `剩余时间大于${startTime}小时`;
							} else {
								desc = `剩余时间介于${startTime}-${endTime}小时之间`;
							}
						}

						let content = [<span className={ s["content-label"] }>剩余发货时间：</span>];
						content.push(
							<>{desc}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'exceptionFlag':
					if (ruleContent?.tradeExceptionList?.length) {
						let tempArr = [];
						let labelContent = '';
						ruleContent?.tradeExceptionList.forEach(key => {
							let item = TRADE_EXCEPTION_INCLUDE_OPTIONS.find(item => item.key === key);
							if (!item) {
								item = TRADE_EXCEPTION_UNINCLUDE_OPTIONS.find(item => item.key === key);
								labelContent = '订单异常(不包含)：';
							} else {
								labelContent = '订单异常(包含)：';
							}

							item.value && tempArr.push(item.value);
						});
						let content = [<span className={ s["content-label"] }>{labelContent}</span>];
						content.push(
							<>{tempArr.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'quickQuery':
					if (ruleContent?.tradeLabelList?.length) {
						let tempArr = [];
						let labelContent = '';
						ruleContent?.tradeLabelList.forEach(key => {
							let item = TRADE_LABEL_INCLUDE_ARR.find(item => item.key === key);
							if (!item) {
								item = TRADE_LABEL_NOT_INCLUDE_ARR.find(item => item.key === key);
								labelContent = '订单标签(不包含)：';
							} else {
								labelContent = '订单标签(包含)：';
							}

							item.value && tempArr.push(item.value);
						});
						let content = [<span className={ s["content-label"] }>{labelContent}</span>];
						content.push(
							<>{tempArr.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'goodsTotalNumRange':
					if (ruleContent?.goodsTotalNumRange) {
						const [min, max] = ruleContent?.goodsTotalNumRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>商品数量：</span>];
							content.push(renderRange(min, max));
							contentList.push(content);
						}
					}
					break;

				case 'goodsTypeNumRange':
					if (ruleContent?.goodsTypeNumRange) {
						const [min, max] = ruleContent?.goodsTypeNumRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>商品种类：</span>];
							content.push(renderRange(min, max));
							contentList.push(content);
						}
					}
					break;

				case 'paymentRange':
					if (ruleContent?.paymentRange) {
						const [min, max] = ruleContent?.paymentRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>订单金额：</span>];
							content.push(renderRange(min, max));
							contentList.push(content);
						}
					}
					break;

				case 'weightRange':
					if (ruleContent?.weightRange) {
						const [min, max] = ruleContent?.weightRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>订单重量：</span>];
							content.push(renderKgRange(min, max, isShowKg));
							contentList.push(content);
						}
					}
					break;

				case 'sellAttribute':
					if (ruleContent?.sellAttributeList?.length) {
						let sellerOptionMap = {};
						SELL_ATTR_OPTIONS.options.forEach(i => sellerOptionMap[i.key] = i.value);
						let content = [<span className={ s["content-label"] }>销售属性：</span>];
						content.push(
							<>{ruleContent?.sellAttributeList.map(i => sellerOptionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
						return;
					}
					if (ruleContent?.sellAttribute && ruleContent?.sellAttribute !== SELL_ATTR_OPTIONS.default) {
						let content = [<span className={ s["content-label"] }>销售属性：</span>];
						content.push(
							<>{SELL_ATTR_OPTIONS.options.find(i => i.key === ruleContent?.sellAttribute)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'supplierIncludingList':
					if (ruleContent?.supplierIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>供应商：</span>];
						content.push(
							<>{ruleContent?.supplierIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'marketIncludingList':
					if (ruleContent?.marketIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>市场：</span>];
						content.push(
							<>{ruleContent?.marketIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'dangKouIncludingList':
					if (ruleContent?.dangKouIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>档口：</span>];
						content.push(
							<>{ruleContent?.dangKouIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'authorIncludingList':
					if (ruleContent?.authorIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>达人名称/ID：</span>];
						content.push(
							<>{ruleContent?.authorIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;
				case 'labelIdList':
					if (ruleContent?.labelIdList?.length) {
						let content = [<span className={ s["content-label"] }>标签唯一码：</span>];
						content.push(
							<>{ruleContent?.labelIdList}<br /></>
						);
						contentList.push(content);
					}
					break;
				
				// case 'smartExpressTemplateId':
				// 	let smartExpressTemplateId = ruleContent?.smartExpressTemplateId;
				// 	if (smartExpressTemplateId || smartExpressTemplateId == "") {
				// 		let content = [<span className={ s["content-label"] }>智选快递：</span>];
				// 		let value = defaultSmartExpressList?.find(tag => tag.printTemplateId == smartExpressTemplateId)?.printTemplateName;
				// 		let value2 = tradeStore?.smartExpressList?.find(tag => tag.printTemplateId == smartExpressTemplateId)?.printTemplateName;
				// 		content.push(
				// 			<>{value || value2}<br /></>
				// 		);
				// 		contentList.push(content);
				// 	}
				// 	break;
				default:
					break;
			}
		});

		if (ruleContent?.precise?.includes?.('isPrecise') || ruleContent?.precise === true) {
			contentList.push([<span className={ s["content-label"] }>宝贝精确查询：</span>, <>是</>]);
		}
	} catch (error) {
		console.error(error, '解析预设content报错');
	}
	return (
		<div className={ s["rule-tooltip-content"] } style={ { padding: '4px 8px' } } onClick={ e => e.stopPropagation() }>
			{contentList.length === 0 && (
				<Empty description="暂未设置查询条件" />
			)}
			{contentList.length !== 0 && (
				<List
					size="small"
					dataSource={ contentList.slice(0, 10) }
					renderItem={ item => {
						const [label = '', ...content] = item;
						return (
							<List.Item className="r-mb-4" style={ { padding: 4 } } key={item.label}>
								{label}
								<div className="r-oneline">{content}</div>
							</List.Item>
						);
					} }
				/>
			)}
			{contentList.length > 10 && (
				<div className="r-w-full r-flex r-jc-c">
					<Button type="link" onClick={ cb }>查看完整条件</Button>
				</div>
			)}
		</div>
	);
}

export const adjustTimeRange = (currentTime: Date, startTime: Date, endTime: Date, timeDiff: string): { startTime: Date; endTime: Date }=> {
	function parseTimeDiff(timeDiffStr: string): number {
	  const parts = timeDiffStr.split(':');
	  return (parseInt(parts[0], 10) * 60 * 60 * 1000) + (parseInt(parts[1], 10) * 60 * 1000) + (parseInt(parts[2], 10) * 1000);
	}
  
	const diffMs = parseTimeDiff(timeDiff);
	const adjustedCurrentTime = new Date(currentTime.getTime() - diffMs);
	
	if (adjustedCurrentTime >= startTime && adjustedCurrentTime >= endTime) {
	  return { startTime, endTime };
	} else if (adjustedCurrentTime >= startTime && adjustedCurrentTime <= endTime) {
		return { startTime, endTime:adjustedCurrentTime };
	} else if (adjustedCurrentTime <= startTime && adjustedCurrentTime <= endTime) {
		return { startTime, endTime: adjustedCurrentTime };
	} else {
	  return null;
	}
  }