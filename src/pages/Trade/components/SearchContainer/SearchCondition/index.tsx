import { Select, SelectProps, Space, Checkbox, Input, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import { QuestionCircleOutlined } from "@ant-design/icons";
import HighLightSelect from "@/components-biz/HighLightSelect";
import {
	ABNORMAL_TYPE_OPTIONS,
	LOGISTICS_TYPE_OPTIONS,
	ORDER_PRINT_STATUS,
	ORDER_STATUS_OPTIONS,
	PEND_TYPE_OPTIONS,
	PLATFORM_OPTIONS,
	REFUND_TYPE_OPTIONS,
	SELL_ATTR_OPTIONS,
	GOOD_STOCK_STATUS_OPTIONS,
	TIME_TYPE_OPTIONS,
	TIME_REMAINING_TYPE_OPTIONS,
	SERVICE_TAG,
	EXCEPTION_FLAG,
	ORDER_SOURCE_TYPE_OPTIONS,
	ORDER_TYPE_OPTIONS,
	LABEL_GENERATOR_STATUS,
	ORDER_PRINT_STATUS_TREE_DATA,
	ORDER_SOURCE_TYPE_OPTIONS_PLAIN,
	ORDER_STATUS_OPTIONS_GROUPED
} from '../constants';

import styles from './index.module.scss';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import message from "@/components/message";
import userStore from "@/stores/user";
import InputMulti from "@/components/Input/InputMulti";
import { tradeStore } from "@/stores";

interface tradeSearchSelectProps extends SelectProps{
	originOptions?: any;
	value?: any;
	disable?: boolean;
	isSupplierAccount?: boolean;
	isHideHand?: boolean;
	bgHighLight?: boolean;
	hideLight?: boolean;
	onChange?: (value: string) => void;
	noPrintTimeType?: boolean;
}

const { Option, OptGroup } = Select;

/**
 * 订单搜索条件 平台类型
 */
const SearchPlatType = () => {
	return (
		<Select defaultValue={ PLATFORM_OPTIONS.default } >
			{PLATFORM_OPTIONS.options.map(opt => (
				<Option value={ opt.key } key={ opt.key }>{opt.value}</Option>
			))}
		</Select>
	);
};

/**
 * 订单搜索条件 时间类型
 */
const SearchTimeType: React.FC<tradeSearchSelectProps> = ({ value = TIME_TYPE_OPTIONS.default, onChange, noPrintTimeType = false, bgHighLight }) => {
	return (
		<Select className={ `${value && bgHighLight ? 'high-light-bg' : ''} ` } defaultValue={ TIME_TYPE_OPTIONS.default } value={ value } onChange={ e => { sendPoint(Pointer.订单_订单打印_订单查询_查询选项_时间选择); onChange(e); } }>
			{(!noPrintTimeType ? TIME_TYPE_OPTIONS.options : TIME_TYPE_OPTIONS.options.filter(i => i.key !== '3')).map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</Select>
	);
};

/**
 * 订单搜索条件 订单状态
 */
const SearchTradeStatus: React.FC<tradeSearchSelectProps> = ({ value = ORDER_STATUS_OPTIONS_GROUPED.default, onChange, bgHighLight }) => {
	// 从getIsFirstSend中获取先发货模式设置
	const [isFirstSendMode, setIsFirstSendMode] = useState(false);
	const {
		goodsTagListStore: {
			getIsFirstSend
		}
	} = tradeStore;
	useEffect(() => {
		getIsFirstSend().then((res) => {
			if (res) setIsFirstSendMode(res);
		});
	}, []);

	const handleChange = (e) => {
		// 处理特殊选项
		if (e === 'WAIT_SEND_GOODS') {
			// 这里可以添加特殊处理逻辑，例如设置额外的查询参数
			// 如果需要在其他地方处理，可以直接传递这个值
		}
		sendPoint(Pointer.订单_订单打印_订单查询_查询选项_订单状态);
		onChange(e);
	};

	// 过滤快捷筛选选项，只有开启先发货模式时才显示 ---后续快捷筛选添加新项了 逻辑需要修改
	const filteredOptions = ORDER_STATUS_OPTIONS_GROUPED.options.filter(group => {
		if (group.label === '快捷筛选') {
			// 如果是快捷筛选组，只有在开启先发货模式且有选项时才显示
			return isFirstSendMode && group.options && group.options.length > 0;
		}
		return true; // 其他组正常显示
	});

	return (
		<HighLightSelect 
			bgHighLight={ bgHighLight } 
			initialValue={ ORDER_STATUS_OPTIONS_GROUPED.default } 
			value={ value } 
			onChange={ handleChange }
			listHeight={ 300 }
		>
			{filteredOptions.map(group => (
				<OptGroup label={ group.label } key={ group.label }>
					{group.options.map(item => (
						<Option value={ item.key } key={ item.key }>{item.value}
							{item.tip && (
								<Tooltip title={ item.tip }>
									<QuestionCircleOutlined style={ { marginLeft: 4 } } />
								</Tooltip>
							)}
						</Option>
					))}
				</OptGroup>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 订单类型
 */
const SearchTradeType: React.FC<tradeSearchSelectProps> = ({ value = ORDER_TYPE_OPTIONS.default, onChange, bgHighLight, hideLight }) => {
	return (
		<HighLightSelect bgHighLight={ bgHighLight } hideLight={ hideLight } initialValue={ ORDER_TYPE_OPTIONS.default } value={ value } onChange={ e => { onChange(e); } }>
			{ORDER_TYPE_OPTIONS.options.map(item => (
				<Option value={ item.key } key={ item.key } >{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 打印状态
 */
const SearchPrintStatus: React.FC<tradeSearchSelectProps> = ({ value = ORDER_PRINT_STATUS.default, onChange, ...reset }) => {
	const handleChange = (value:string) => {
		sendPoint(Pointer.订单_订单打印_订单查询_查询选项_打印状态);
		onChange(value);
	};
	return (
		<HighLightSelect { ...reset } initialValue={ ORDER_PRINT_STATUS.default } value={ value } onChange={ handleChange }>
			{ORDER_PRINT_STATUS.options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

// 添加波次号搜索组件
/**
 * 订单搜索条件 波次号
 */
const SearchBatchNo: React.FC<tradeSearchSelectProps> = ({ value, onChange, bgHighLight }) => {
	return (
		<Input 
			className={ `${value ? 'high-light-bg' : ''}` }
			placeholder="波次号" 
			style={ { width: '100%' } } 
			size="small"
			value={ value }
			onChange={ e => {
		  		onChange(e.target.value);
			} }
		/>
	);
};

/**
 * 订单搜索条件 打印状态 新
	* printStatus 打印状态，100 未打印，101已打印
	* fhdPrintStatus 发货单打印状态，0 未打印，1已打印
	* labelPrintStatus 商品标签打印状态，0未打印，1已打印
 */
const PrintNode = ({ isChecked, value }) => (value ? <span className={ styles.printIcon }><span>快</span>{ isChecked ? '已' : '未'}打印</span> : null);
const FhdPrintNode = ({ isChecked, value }) => (value ? <span className={ styles.fhPrintIcon }><span>发</span>{ isChecked ? '已' : '未'}打印</span> : null);
const LabelPrintNode = ({ isChecked, value }) => (value ? <span className={ styles.labelPrintIcon }><span>标</span>{isChecked ? '已' : '未'}打印</span> : null);
export const getPrintNode = (arrValue, values) => {
	let node; 
	if (!arrValue || arrValue == ",,") {
		return '打印状态';
	} else if (arrValue == '101,0,0') {
		node = '仅快递单已打印';
	} else if (arrValue == '100,1,1') {
		node = '仅快递单未打印';
	} else if (arrValue == '100,1,0') {
		node = '仅发货单已打印';
	} else if (arrValue == '101,0,1') {
		node = '仅发货单未打印';
	} else if (arrValue == '100,0,0') {
		node = '均未打印';
	} else if (arrValue == '101,1,1') {
		node = '均已打印';
	} else {
		node = (
			<div className={ styles.printNodeWrap }>
				<PrintNode isChecked={ values.printStatus == '101' } value={ values.printStatus } />
				<FhdPrintNode isChecked={ values.fhdPrintStatus == '1' } value={ values.fhdPrintStatus } />
				<LabelPrintNode isChecked={ values.labelPrintStatus == '1' } value={ values.labelPrintStatus } />
			</div>
		);
	}
	return node;
};

export const getValueByOldStatus = (oldStatus: string|number) => {
	// * 打印标记
	// * 0 快递单与发货单未打印
	// * 1 快递单或发货单已打印
	// * 2 仅快递单已打印
	// * 3 仅发货单已打印
	// * 4 快递单与发货单已打印
	// * 5 快递单或发货单未打印
	// * 6 快递单未打印
	// * 7 发货单未打印
	// * 8 商品标签未打印
	// * 9 商品标签已打印
	// * 10 均未打印
	// * 11 均已打印
	if (oldStatus == 0) {
		return { printStatus: '100', fhdPrintStatus: '0', labelPrintStatus: '' };
	}
	if (oldStatus == 1) { // !! 目前不用忽略我
		return { printStatus: '101', fhdPrintStatus: '1', labelPrintStatus: '' };
	}
	if (oldStatus == 2) {
		return { printStatus: '101', fhdPrintStatus: '0', labelPrintStatus: '0' };
	}
	if (oldStatus == 3) {
		return { printStatus: '100', fhdPrintStatus: '1', labelPrintStatus: '0' };
	}
	if (oldStatus == 4) {
		return { printStatus: '101', fhdPrintStatus: '1', labelPrintStatus: '' };
	}
	if (oldStatus == 5) { // !! 目前不用忽略我
		return { printStatus: '100', fhdPrintStatus: '0', labelPrintStatus: '' };
	}
	if (oldStatus == 6) {
		return { printStatus: '100', fhdPrintStatus: '', labelPrintStatus: '' };
	}
	if (oldStatus == 7) {
		return { printStatus: '', fhdPrintStatus: '0', labelPrintStatus: '' };
	}
	if (oldStatus == 8) {
		return { printStatus: '', fhdPrintStatus: '', labelPrintStatus: '0' };
	}
	if (oldStatus == 9) {
		return { printStatus: '', fhdPrintStatus: '', labelPrintStatus: '1' };
	}
	if (oldStatus == 10) {
		return { printStatus: '100', fhdPrintStatus: '0', labelPrintStatus: '0' };
	}
	if (oldStatus == 11) {
		return { printStatus: '101', fhdPrintStatus: '1', labelPrintStatus: '1' };
	}

	return {
		printStatus: '',
		fhdPrintStatus: '',
		labelPrintStatus: '',
	};
};
const NewSearchPrintStatus: React.FC<tradeSearchSelectProps> = ({ value, onChange, ...reset }) => {
	const [values, setValues] = useState({
		printStatus: '',
		fhdPrintStatus: '',
		labelPrintStatus: '',
		...value || {},
	});
	const [arrValue, setArrValues] = useState('');
	const [node, setNode]: any = useState();
	
	const handleChange = (field: string, value, isChecked: boolean) => {
		sendPoint(Pointer.订单_订单打印_订单查询_查询选项_打印状态);
		const newValues: any = {
			...values,
			[field]: isChecked ? value : ''
		};
		setValues(() => (newValues));
		setArrValues([newValues.printStatus, newValues.fhdPrintStatus, newValues.labelPrintStatus].join(','));
		onChange(newValues);
	};

	useEffect(() => {
		if (value) {
			setValues(pre => ({ ...pre, ...value }));
			setArrValues([value.printStatus, value.fhdPrintStatus, value.labelPrintStatus].join(','));
		}
	}, [value]);
	useEffect(() => {
		setNode(getPrintNode(arrValue, values));
	}, [JSON.stringify(arrValue), values]);
	
	return (
		<HighLightSelect.Tree
			{ ...reset }
			bgHighLight
			treeCheckable
			dropdownMatchSelectWidth={ false }
			treeDefaultExpandAll
			showArrow={ false }
			showSearch={ false }
			treeCheckStrictly
			treeIcon
			initialValue={ arrValue }
			value={ values }
			switcherIcon={ null }
			popupClassName="high-light-tree"
			className="high-light-tree-wrap"
			onChange={ handleChange }
			tagRender={ () => {
				return <div>{node}</div>;
			} }
			dropdownRender={ () => {
				return (
					<div style={ { padding: '4px 8px' } }>
						{ORDER_PRINT_STATUS_TREE_DATA.map(s => (
							<div key={ s.title }>
								<div>{s.title}</div>
								<div>
									<Space direction="vertical">
										{s.children.map(child => (
											<Checkbox onChange={ e => handleChange(s.value, child.value, e.target.checked) } checked={ values[s.value] == child.value } value={ child.value }>{child.title}</Checkbox>
										))}
									</Space>
								</div>
							</div>
						))}
					</div>
				);
			} }
		/>
	);
};

/**
 * 订单来源
 */
const OrderSourceType: React.FC<tradeSearchSelectProps> = ({ value = ORDER_SOURCE_TYPE_OPTIONS.default, onChange, isSupplierAccount, isHideHand = false, ...reset }) => {
	const handleChange = (value:string) => {
		onChange(value);
	};

	let options = [...ORDER_SOURCE_TYPE_OPTIONS.options];
	if (isSupplierAccount) {
		options.push({
			key: 'scmOrder',
			value: '分销推送'
		});
	}
	if (isHideHand) {
		options = options.filter(i => i.key != 'handOrder');
	}
	return (
		<HighLightSelect { ...reset } initialValue={ ORDER_SOURCE_TYPE_OPTIONS.default } value={ value } onChange={ handleChange }>
			{options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单来源
 */
const OrderSourceTypePlain: React.FC<tradeSearchSelectProps> = ({ value = ORDER_SOURCE_TYPE_OPTIONS_PLAIN.default, onChange, isSupplierAccount, isHideHand = false, ...reset }) => {
	const handleChange = (value:string) => {
		onChange(value);
	};

	let options = [...ORDER_SOURCE_TYPE_OPTIONS_PLAIN.options];
	if (isSupplierAccount) {
		options.push({
			key: 'SCM',
			value: '分销推送'
		});
	}
	if (isHideHand) {
		options = options.filter(i => i.key != 'HAND');
	}
	return (
		<HighLightSelect { ...reset } initialValue={ ORDER_SOURCE_TYPE_OPTIONS_PLAIN.default } value={ value } onChange={ handleChange }>
			{options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};


/**
 * 订单搜索条件 销售属性
 */
const SearchSellAttr: React.FC<tradeSearchSelectProps> = ({ value = SELL_ATTR_OPTIONS.default, onChange, disabled, bgHighLight }) => {
	return (
		<HighLightSelect
			allowClear
			bgHighLight={ bgHighLight }
			mode="multiple"
			placeholder="销售属性"
			initialValue={ SELL_ATTR_OPTIONS.default }
			optionLabelProp="label"
			maxTagCount="responsive"
			value={ value }
			disabled={ disabled }
			onChange={ e => { sendPoint(Pointer.订单_订单打印_订单查询_查询选项_销售属性); onChange(e); } }
		>
			{SELL_ATTR_OPTIONS.options.map(item => (
				<Option
					value={ item.key }
					label={ value.length < 2 ? item.value.substring(0, 6) + '...' : item.value.substring(0, 2) + '...' }
					key={ item.key }
				>{item.value}
				</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 缺货状态
 */
const SearchGoodStockStatus: React.FC<tradeSearchSelectProps> = ({ value = GOOD_STOCK_STATUS_OPTIONS.default, disable, onChange, onClick, originOptions = GOOD_STOCK_STATUS_OPTIONS.options, bgHighLight }) => {
	return (
		<HighLightSelect
			bgHighLight={ bgHighLight }
			initialValue={ GOOD_STOCK_STATUS_OPTIONS.default }
			value={ value }
			onChange={ e => { sendPoint(Pointer.订单_订单打印_订单查询_查询选项_缺货状态); onChange(e); } }
			onClick={ (e) => { disable && message.warn('需开启下单占用库存或付款占用库存可查询'); onClick && onClick(e); } }
		>
			{originOptions.map(item => (
				<Option value={ item.key } key={ item.key } disabled={ disable }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 物流方式
 */
const SearchLogisticsType: React.FC<tradeSearchSelectProps> = ({ value = LOGISTICS_TYPE_OPTIONS.default, onChange, bgHighLight }) => {
	return (
		<HighLightSelect bgHighLight={ bgHighLight } initialValue={ LOGISTICS_TYPE_OPTIONS.default } value={ value } onChange={ onChange }>
			{LOGISTICS_TYPE_OPTIONS.options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 退款状态
 */
const SearchRefundStatus: React.FC<tradeSearchSelectProps> = ({ value = REFUND_TYPE_OPTIONS.default, onChange, bgHighLight }) => {
	return (
		<HighLightSelect bgHighLight={ bgHighLight } initialValue={ REFUND_TYPE_OPTIONS.default } value={ value } onChange={ e => { sendPoint(Pointer.订单_订单打印_订单查询_查询选项_退款状态); onChange(e); } }>
			{REFUND_TYPE_OPTIONS.options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 剩余时间
 */
const SearchTimeRemainingStatus: React.FC<tradeSearchSelectProps> = ({ value = TIME_REMAINING_TYPE_OPTIONS.default, onChange, bgHighLight }) => {
	return (
		<HighLightSelect bgHighLight={ bgHighLight } initialValue={ REFUND_TYPE_OPTIONS.default } value={ value } onChange={ e => { onChange(e); } }>
			{TIME_REMAINING_TYPE_OPTIONS.options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 是否挂起
 */
const SearchPendStatus: React.FC<tradeSearchSelectProps> = ({ value = PEND_TYPE_OPTIONS.default, onChange, bgHighLight }) => {
	return (
		<HighLightSelect bgHighLight={ bgHighLight } initialValue={ PEND_TYPE_OPTIONS.default } value={ value } onChange={ onChange }>
			{PEND_TYPE_OPTIONS.options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 订单异常
 */
const SearchAbnormalTrade: React.FC<tradeSearchSelectProps> = ({ value = ABNORMAL_TYPE_OPTIONS.default, onChange, bgHighLight }) => {
	return (
		<HighLightSelect bgHighLight={ bgHighLight } initialValue={ ABNORMAL_TYPE_OPTIONS.default } value={ value } onChange={ onChange }>
			{ABNORMAL_TYPE_OPTIONS.options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 服务标签
 */
const SearchServiceTag: React.FC<tradeSearchSelectProps> = ({ value = SERVICE_TAG.default, onChange, bgHighLight }) => {
	return (
		<HighLightSelect bgHighLight={ bgHighLight } initialValue={ SERVICE_TAG.default } value={ value } onChange={ e => { onChange(e); } }>
			{SERVICE_TAG.options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};

/**
 * 订单搜索条件 异常订单
 */
const SearchExceptionFlag: React.FC<tradeSearchSelectProps> = ({ value = EXCEPTION_FLAG.default, onChange, bgHighLight }) => {
	const { isShowZeroStockVersion } = userStore;
	let options = EXCEPTION_FLAG.options;
	if (isShowZeroStockVersion) {
		options = EXCEPTION_FLAG.options.filter(i => !['unBindingItem'].includes(i.key));
	}
	return (
		<HighLightSelect bgHighLight={ bgHighLight } initialValue={ EXCEPTION_FLAG.default } value={ value } onChange={ e => { sendPoint(Pointer.订单_订单打印_异常订单查询); onChange(e); } }>
			{options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};


/**
 * 备货单搜索条件 是否生成小标签
 */
const LabelGenerateStatus: React.FC<tradeSearchSelectProps> = ({ value = LABEL_GENERATOR_STATUS.default, onChange, bgHighLight }) => {
	return (
		<HighLightSelect bgHighLight={ bgHighLight } initialValue={ LABEL_GENERATOR_STATUS.default } value={ value } onChange={ e => { onChange(e); } }>
			{LABEL_GENERATOR_STATUS.options.map(item => (
				<Option value={ item.key } key={ item.key }>{item.value}</Option>
			))}
		</HighLightSelect>
	);
};



const checkValueIsEmpty = (value:string) => {
	return value == 'EMPTY' ? '' : value;
};

export {
	SearchPlatType,
	SearchTimeType,
	SearchTradeStatus,
	SearchPrintStatus,
	NewSearchPrintStatus,
	SearchSellAttr,
	SearchGoodStockStatus,
	SearchLogisticsType,
	SearchRefundStatus,
	SearchPendStatus,
	SearchAbnormalTrade,
	SearchTimeRemainingStatus,
	SearchServiceTag,
	SearchTradeType,
	SearchExceptionFlag,
	OrderSourceType,
	OrderSourceTypePlain,
	checkValueIsEmpty,
	LabelGenerateStatus,
	SearchBatchNo
};
