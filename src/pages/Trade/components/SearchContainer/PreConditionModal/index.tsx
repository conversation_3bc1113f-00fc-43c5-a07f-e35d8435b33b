import { Button, Checkbox, Col, Form, Input, Modal, Radio, Row, Space, Tabs, Typography, List } from 'antd';
import React, { useCallback, useEffect, useImperativeHandle, useState, forwardRef, useMemo } from 'react';
import { CloseOutlined, ExclamationCircleOutlined, MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import cs from 'classnames';
import { useRequest } from 'ahooks';
import { cloneDeep, isEqual, isEqualWith, isEmpty, isPlainObject } from 'lodash';
import { observer } from 'mobx-react';
import userStore from '@/stores/user';
import Icon from "@/components/Icon";
import s from "./index.module.scss";
import { FLAG_VALUE, ORDER_PRINT_STATUS, ORDER_STATUS_OPTIONS, GOOD_STOCK_STATUS_OPTIONS, REFUND_TYPE_OPTIONS, TRADE_EXCEPTION_OPTIONS, TRADE_LABEL_OPTIONS, SERVICE_TAG_TO_NEW } from '../constants';
import InputRange from "@/components/Input/InputNumber/RangeInput";
import AddressSelect from '../AddressSelect/addressSelect';
import { TradeAdvancedSearchDeleteByIdApi, TradeAdvancedSearchSaveOrUpdateApi, TradeAdvancedSearchSwapSortApi } from '@/apis/trade/search';
import { swapArray } from '@/utils/util';
import { isRange } from '..';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import message from "@/components/message";
import InputMulti from '@/components/Input/InputMulti';
import { isBeyondInput } from '@/pages/Trade/utils';
import RemainingDeliveryTime from "@/components-biz/RemainingDeliveryTime";
import { TradeQueryGatherParams } from '@/types/trade/search/search';
import { SearchExceptionFlag, SearchServiceTag } from '../SearchCondition';
import InputArrayMulti from '@/components/Input/InputArrayMulti';
import InputSelect from '@/components/Input/InputSelect';


const { TabPane } = Tabs;
interface PreConditionModalProps {
	visible: boolean;
	isCanGoodStock?: boolean;
	onClose:()=> void;
	onSaveAfter?:(data?:any, isSubmit?: boolean, type?:string, id?:string)=> void;
	updateDataSource?:(list:any[])=> void;
	dataSource?: any[];
	ref:React.Ref<any>,
}
const config = {
	minConfig: { 'placeholder': '最小值' },
	maxConfig: { 'placeholder': '最大值' }
};
const PreConditionModal = forwardRef((props:PreConditionModalProps, ref: React.Ref<any>) => {
	const [form] = Form.useForm();
	const { visible, onClose, dataSource = [], onSaveAfter, updateDataSource, isCanGoodStock } = props;
	const [curRuleName, setRuleName] = useState<string>('');
	const [isPreciseByTrade, setIsPreciseByTrade] = useState<boolean>(false);
	const [curRuleId, setCurRuleId] = useState<string>('');
	const [curRuleContent, setCurRuleContent] = useState<string>('');
	const [waitLoading, setWaitLoading] = useState<boolean>(true);
	const [buyerMessageDisabled, setBuyerMessageDisabled] = useState<boolean>(false);
	const [sellerMemoDisabled, setSellerMemoDisabled] = useState<boolean>(false);
	const [formData, setFormData] = useState<TradeQueryGatherParams>({});
	const { isShowZeroStockVersion } = userStore;

	
	useEffect(() => {
		!visible && initSearchFormValue();
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [visible]);

	// 表单初始值
	const initValues = {
		status: "WAIT_SELLER_SEND_GOODS", // 订单状态：待发货
		goodStockStatus: 'ALL', // 缺货状态： 全部
		printStatus: "EMPTY", // 打印状态： 全部
		commentsRemarks: "EMPTY", // 留言备注：全部
		goodsIncludeStatus: 1, // 商品搜索： 商品(包含)
		customizeResidueSendTime: 'none-none' // 剩余发货时间
	};
	// 初始化查询条件表单
	const initSearchFormValue = () => {
		form.setFieldsValue(initValues);
		setIsPreciseByTrade(false);
	};

	// 保存｜更新
	const { runAsync: runAdvancedSearchSaveOrUpdateApi, loading: saveLoading } = useRequest(TradeAdvancedSearchSaveOrUpdateApi, {
		manual: true,
		// onSuccess: (res) => {
		// 	onSaveAfter();
		// }
	});

	// 删除
	const { run: runAdvancedSearchDeleteApi } = useRequest(TradeAdvancedSearchDeleteByIdApi, {
		manual: true,
		onSuccess: (res) => {
			onReset();
			onSaveAfter();
		}
	});

	// 移动查询条件
	const { run: runAdvancedSearchSwapSortApi, loading: moveLoading } = useRequest(TradeAdvancedSearchSwapSortApi, {
		manual: true
	});

	// 对于string类型和number类型的数据，暂不做恒等判断
	const customizer = (objValue:any, othValue:any) => {
		/**
		 * 如果是数组比较，对于乱序的数组，认定是相等的，Ex:[2,1] = [1,2];
		 * 对于更复杂的包含对象的数组，本次暂不考虑，Ex:[{a:1,b:2}] = [{b:2,a:1}]
		 */
		if (Array.isArray(objValue) && Array.isArray(othValue)) {
			const objValueTemp = objValue.sort((next, pre) => next - pre);
			const othValueTemp = othValue.sort((next, pre) => next - pre);
			if (isEqual(objValueTemp, othValueTemp)) {
				return true;
			}
		}
		if (objValue == othValue) {
			return true;
		}
	};

	// 递归处理对象的无效属性值，'',null,undefined,[],{}都认定是无效值
	function handleObjectInvalidValue(obj:object) {
		const otherInvalidValue = [null, '', undefined];
		if (isPlainObject(obj)) {
			if (isEmpty(obj)) {
				return true;
			}
			for (let key in obj) {
				if (Object.prototype.hasOwnProperty.call(obj, key)) {
					if (Array.isArray(obj[key])) {
						const validArr = getValidValueArr(obj[key]);
						const isEmpty = isEmptyArray(validArr);
						if (isEmpty) {
							delete obj[key];
						} else {
							obj[key] = validArr;
						}
					}
					if (isPlainObject(obj[key])) {
						const isInvalideObj = handleObjectInvalidValue(obj[key]);
						if (isInvalideObj) {
							delete obj[key];
						}
					}
					if (otherInvalidValue.includes(obj[key])) {
						delete obj[key];
					}
				}
			}
			return isEmpty(obj);
		}
		return false;
	}

	// 获取去除无效值以后的新数组，'',null,undefined,[],{}都认定是无效值
	function getValidValueArr(arr:[]) {
		const otherInvalidValue = [null, '', undefined];
		if (Array.isArray(arr)) {
			const res = arr.filter((item, index) => {
				if (Array.isArray(item)) {
					return !isEmptyArray(item);
				}
				if (isPlainObject(item)) {
					return !handleObjectInvalidValue(item);
				}
				return !otherInvalidValue.includes(item);
			});
			return res;
		}
	}


	// 判断数组是否为空,只要数组项不是有效值，都判断为空。'',null,undefined,[],{}都认定是无效值
	function isEmptyArray(arr:[]):boolean {
		const otherInvalidValue = [null, '', undefined];
		if (Array.isArray(arr)) {
			const isEmptyArr: boolean = arr.every(item => {
				if (isPlainObject(item)) {
					handleObjectInvalidValue(item);
					return isEmpty(item);
				}
				if (Array.isArray(item)) {
					return isEmptyArray(item);
				}
				return otherInvalidValue.includes(item);
			});
			return isEmptyArr;
		}
		return false;
	}

	// 判断表单值是否被更改了
	const hasChanged = () => {
		const originData = (curRuleContent && JSON.parse(curRuleContent)) || initValues;
		const formData = form.getFieldsValue();

		formatOriginContent(originData);

		// 判断地区是否修改过，前端默认展示所有省份，但是后端默认是不返回addressInfo的
		let addressInfoChanged = false;
		const originAddressInfo = originData.addressInfo;
		const formAddressInfo = formData.addressInfo;
		if (originAddressInfo) {
			addressInfoChanged = !formAddressInfo || !isEqualWith(originAddressInfo, formAddressInfo, customizer);
		} else {
			// 如果原本接口没有地区信息，但是现在form选择了‘所有省份’以外的选项，则表单被修改了
			addressInfoChanged = formAddressInfo && formAddressInfo.id;
		}

		if (addressInfoChanged) {
			return true;
		} else {
			// 删除表单中的地址信息，避免下文将表单信息和源信息做对比时造成干扰
			delete formData.addressInfo;
		}
		// 如果没有原内容，说明是默认面板，也需要判断是否修改过
		if (!curRuleContent) {
			const initFormData = cloneDeep(originData);
			const curFormData = cloneDeep(formData);
			/**
			 * initValue中不包含isPreciseByTrade（精确到订单）字段和ruleName（查询条件名称），
			 * 这两个表单值被放在了Form组件之外，所以默认面板如果修改了这两项值，都判定表单被修改了
			 */
			if (isPreciseByTrade || curRuleName) {
				return true;
			}
			[initFormData, curFormData].forEach(i => handleObjectInvalidValue(i));
			return !isEqualWith(initFormData, curFormData, customizer);
		}

		// 判断查询条件名称是否修改过
		let oldRuleName = '';
		dataSource.forEach(item => {
			if (item.id === curRuleId && curRuleId) {
				oldRuleName = item.ruleName;
			}
		});
		if (oldRuleName && curRuleName !== oldRuleName) {
			return true;
		}

		// console.log(222,cloneDeep(formData),cloneDeep(originData))
		// console.log(223,formData,originData)
		// 判断表单内容是否修改过
		formData.isPreciseByTrade = isPreciseByTrade;
		handleObjectInvalidValue(formData);
		handleObjectInvalidValue(originData);
		return !isEqualWith(formData, originData, customizer);
	};

	// 关闭
	const onCancel = () => {
		// 弹窗关闭时，判断内容前后是否有变更，如果有变更，则弹窗提醒
		if (!hasChanged()) {
			onClose();
			onReset();
			return;
		}
		Modal.confirm({
			title: '系统提示',
			icon: <ExclamationCircleOutlined />,
			content: `关闭后当前内容将不会被保存，确定要关闭么？`,
			okText: '确认',
			onOk: () => {
				onClose();
				onReset();
			},
			cancelText: '取消',
		});
	};

	// 重置modal
	const onReset = () => {
		form.resetFields();
		setCurRuleId('');
		setRuleName('');
		setCurRuleContent('');
		setIsPreciseByTrade(false);
		initSearchFormValue();
		setWaitLoading(true);
		setBuyerMessageDisabled(false);
		setSellerMemoDisabled(false);
	};

	const onCreateRule = () => {
		checkHasSaveTips(undefined, undefined, undefined, onReset);
	};

	// 操作
	const handleAction = (type:string) => {
		const res = form.getFieldsValue();
		res['isPreciseByTrade'] = isPreciseByTrade;
		if (isBeyondInput(res?.buyerMessage, 50)) {
			message.error('单次查询最多筛选50个留言，请重新输入');
			return false;
		}
		if (res.buyerMessage && res.buyerMessage.length > 1000) {
			message.error('留言最多输入1000个字数，请重新输入');
			return false;
		}
		if (isBeyondInput(res?.sellerMemo, 50)) {
			message.error('单次查询最多筛选50个备注，请重新输入');
			return false;
		}
		if (res.sellerMemo && res.sellerMemo.length > 1000) {
			message.error('备注最多输入1000个字数，请重新输入');
			return false;
		}
		if (!isRange(res?.weightRange, [1, 99999])) {
			message.error('订单重量输入超过限制，请调整数值在1-99999之间');
			return false;
		}
		if (!isRange(res?.goodsTotalNumRange, [1, 99999])) {
			message.error('商品数量输入超过限制，请调整数值在1-99999之间');
			return false;
		}
		if (!isRange(res?.goodsTypeNumRange, [1, 99999])) {
			message.error('商品种类输入超过限制，请调整数值在1-99999之间');
			return false;
		}
		if (!isRange(res?.paymentRange, [0])) {
			message.error('订单金额输入不正确，订单金额只能输入大于0的数值');
			return false;
		}
		if (res?.itemSku?.[0]?.shortNameIncludingList?.length > 50) {
			message.error(`同时查询 商品名称/简称/商品编码/id 的数量不能超过50个`);
			return false;
		}
		if (res?.itemSku?.[0]?.skuIncludingList?.length > 50) {
			message.error(`同时查询 规格名称/编码/别名 的数量不能超过50个`);
			return false;
		}
		if (isBeyondInput(res?.itemSku?.[0]?.shortNameIncluding || '', 50)) {
			message.error(`同时查询 商品名称/简称/商品编码/id 的数量不能超过50个`);
			return false;
		}
		if (isBeyondInput(res?.itemSku?.[0]?.skuIncluding || '', 50)) {
			message.error(`同时查询 规格名称/编码/别名 的数量不能超过50个`);
			return false;
		}
		let params = {
			ruleName: curRuleName,
			isVisible: 1,
			ruleContent: JSON.stringify(res),
		};

		curRuleId && (params['id'] = curRuleId);
		switch (type) {
			// 保存
			case 'save':
				if (!curRuleName) {
					message.warning('请输入查询条件名称');
					return;
				}
				runAdvancedSearchSaveOrUpdateApi(params).then(res => {
					onSaveAfter();
				});
				break;
			// 保存并查询
			case 'save&search':
				if (!curRuleName) {
					message.warning('请输入查询条件名称');

					return;
				}
				runAdvancedSearchSaveOrUpdateApi(params).then(() => {
					onClose();
					onReset();
					onSaveAfter(res, true, type, curRuleId);
				});
				break;
			// 仅查询
			case 'search':
				onClose();
				onReset();
				onSaveAfter(res, true, type);
				break;
			default:
				break;
		}
	};

	const onChangeRuleName = (e:any) => {
		const { value } = e.target;
		setRuleName(value);
	};

	// 对于新旧数据不同，判断是否保存单签修改项，需要将旧的数据格式转化成新的。
	const formatOriginContent = (jsonContent) => {
		// 商品
		if (jsonContent?.itemSku?.[0].shortNameIncluding && !jsonContent.itemSku[0].shortNameIncludingList) {
			jsonContent.itemSku[0].shortNameIncludingList = jsonContent?.itemSku?.[0].shortNameIncluding?.split(/,/);
		}
		if (jsonContent?.itemSku?.[0].skuIncluding && !jsonContent.itemSku[0].skuIncludingList) {
			jsonContent.itemSku[0].skuIncludingList = jsonContent?.itemSku?.[0].skuIncluding?.split(/,/);
		}

		// 订单标签、订单异常
		if (jsonContent?.exceptionFlag == 'unBindingItem') {
			jsonContent.tradeExceptionList = [jsonContent.exceptionFlag];
		}
		if (jsonContent?.serviePromiseType) {
			jsonContent.tradeLabelList = [SERVICE_TAG_TO_NEW[jsonContent.serviePromiseType]];
		}
		delete jsonContent.serviePromiseType;
		delete jsonContent.exceptionFlag;
	};

	// 切换选中查询条件
	const onSelectRule = (id:any, ruleName:string, ruleContent:string) => {
		setCurRuleId(id);
		setRuleName(ruleName);
		setCurRuleContent(ruleContent);
		let jsonContent = JSON.parse(ruleContent);
		jsonContent.goodsIncludeStatus = Number(jsonContent.goodsIncludeStatus);

		formatOriginContent(jsonContent);

		form.resetFields();
		form.setFieldsValue(jsonContent);
		// 由于antd Form的setFieldsValue不会触发onValuesChanged事件，所以卖家备注和买家留言的禁用需要重新设置
		const commentsRemarks = jsonContent.commentsRemarks;
		setBuyerMessageDisabled(!['EMPTY', '1'].includes(commentsRemarks));
		setSellerMemoDisabled(!['EMPTY', '2'].includes(commentsRemarks));
		setIsPreciseByTrade(jsonContent.isPreciseByTrade);
	};

	// 判断是否有需要保存的内容
	const checkHasSaveTips = (id:any, ruleName:string, ruleContent:string, cb:Function) => {
		let formContent = form.getFieldsValue();
		formContent['isPreciseByTrade'] = isPreciseByTrade;
		formContent = JSON.stringify(formContent);
		// 判断名字是否改变
		let oldRuleName = '';
		dataSource.forEach(item => {
			if (item.id === curRuleId && curRuleId) {
				oldRuleName = item.ruleName;
			}
		});

		if (hasChanged()) {
			Modal.confirm({
				title: '系统提示',
				icon: <ExclamationCircleOutlined />,
				content: `是否保存当前修改项？`,
				okText: '保存',
				onOk: () => {
					handleAction('save');
					setTimeout(() => {
						cb(id, ruleName, ruleContent);
					}, 500);
				},
				cancelText: '不保存',
				onCancel: () => {
					cb(id, ruleName, ruleContent);
				}
			});
		} else {
			cb(id, ruleName, ruleContent);
		}
	};

	// 渲染标题
	const renderTitle = () => {
		return (
			<div className="r-flex r-ai-c" >
				<div className="r-fw-500 r-mr-4">高级查询 </div>
				<a href="https://www.yuque.com/docs/share/9bdfe742-e324-42de-beba-a1889037b385?#" target="_blank" rel="noreferrer" >
					<Icon type="wenhao-xian" size={ 16 } style={ { color: '#999' } } />
				</a>
				<p className="r-fs-14 r-c-F5222D r-fw-400 r-ml-4">点击问号查看使用教程</p>
			</div>
		);
	};

	//
	const handleChange = (key:string, v: number[]) => {
		// console.log("🚀 ~ file: index.tsx ~ line 96 ~ handleChange ~ key", key, v);

	};

	// 移动查询条件
	const onMoveRule = (action:string) => {
		if (!curRuleId) {
			message.error('请选择要移动的查询条件后再进行操作');
			return;
		}
		const dataSourceLen = dataSource?.length || 0;
		let curIndex = 0;
		let params = {};
		dataSource.forEach((item, index) => {
			if (item?.id === curRuleId) {
				curIndex = index;
			}
		});
		let cloneList = cloneDeep(dataSource);
		if (action === 'up') {
			if (curIndex === 0) {
				message.warning('第一个不可上移');
				return;
			}
			params['swapId1'] = dataSource[curIndex - 1]?.id;
			params['swapId2'] = curRuleId;
			cloneList = swapArray(cloneList, curIndex - 1, curIndex);

		} else if (action === 'down') {
			if (curIndex === dataSourceLen - 1) {
				message.warning('最后一个不可下移');
				return;
			}
			params['swapId1'] = curRuleId;
			params['swapId2'] = dataSource[curIndex + 1]?.id;
			cloneList = swapArray(cloneList, curIndex, curIndex + 1);

		}
		updateDataSource(cloneList);
		runAdvancedSearchSwapSortApi(params);
	};

	const onValuesChange = (changedValues:object, allValues:TradeQueryGatherParams) => {
		// 如果留言备注单选被更改了
		const commentsRemarks = changedValues.commentsRemarks;
		if ('commentsRemarks' in changedValues) {
			// 重置卖家备注和买家留言
			setBuyerMessageDisabled(!['EMPTY', '1'].includes(commentsRemarks));
			setSellerMemoDisabled(!['EMPTY', '2'].includes(commentsRemarks));
			// 如果选了全部，不清空内容
			if (commentsRemarks !== 'EMPTY') {
				form.setFieldsValue({
					buyerMessage: '',
					sellerMemo: ''
				});
			}
		}
		setFormData(allValues);
	};
	const TRADE_LABEL_OPTIONS_1 = useMemo(() => {
		const hasWaveManagePermission = userStore.hasWaveManagePermission;
		if (!hasWaveManagePermission) {
			TRADE_LABEL_OPTIONS.forEach(op => {
				op.options.forEach(item => {
					item.arr = item.arr.filter(i => i.value !== '已生成拣货波次');
				});
			});
		}
		return [...TRADE_LABEL_OPTIONS];
	}, [userStore.hasWaveManagePermission]);

	const TRADE_EXCEPTION_OPTIONS_1 = (useMemo(() => {
		const hasWaveManagePermission = userStore.hasWaveManagePermission;
		if (!hasWaveManagePermission) {
			TRADE_EXCEPTION_OPTIONS.forEach(op => {
				op.options.forEach(item => {
					item.arr = item.arr.filter(i => i.value !== '波次拣货异常');
				});
			});
		}
		return [...TRADE_EXCEPTION_OPTIONS];
	}, [userStore.hasWaveManagePermission]));
	// 渲染查询条件内容
	const renderContentForm = () => {
		return (
			<div className={ s['condition-form'] }>
				<Form
					form={ form }
					className="r-pl-16"
					name="condition_form_in_modal"
					onValuesChange={ onValuesChange }
				>
					<Form.Item
						name="platformInfo"
						label="平台/店铺"
					>
						<ShopMultiSelect
							isSendPoint
							style={ { width: '40%', } }
							isHasHandPlat
						/>
					</Form.Item>
					<Form.Item
						name="status"
						label="订单状态"
						className="r-flex r-ai-c"

					>
						<Radio.Group>
							{
								ORDER_STATUS_OPTIONS.options.map(item => {
									return (
										<Radio key={ item.key } value={ item.key }>{item.value}</Radio>
									);
								})
							}
						</Radio.Group>
					</Form.Item>
					<Form.Item
						name="goodStockStatus"
						label="缺货状态"
						className="r-flex r-ai-c"
					>
						<Radio.Group>
							<span onClick={ () => { !isCanGoodStock && message.warn('需开启下单占用库存或付款占用库存可查询'); } }>
								{
									GOOD_STOCK_STATUS_OPTIONS.options.map(item => {
										if (item.key == 'ALL') item = { ...item, value: '全部' };
										return (
											<Radio value={ item.key } key={ item.key } disabled={ !isCanGoodStock }>
												{item.value}
											</Radio>
										);
									})
								}
							</span>
						</Radio.Group>
					</Form.Item>
					<Form.Item
						name="printStatus"
						label="打印状态"
						className="r-flex r-ai-c"
					>
						<Radio.Group>
							{
								ORDER_PRINT_STATUS.options.map(item => {
									if (item.key === '' || item.key === 'EMPTY') {
										return null;
									}
									return (
										<Radio key={ item.key } value={ item.key }>{item.value}</Radio>
									);
								})
							}
							<Radio value="EMPTY">全部</Radio>
						</Radio.Group>
					</Form.Item>
					<Row align="middle">
						<Form.Item
							label="商品搜索"
							name="goodsIncludeStatus"
							className="r-flex r-ai-c"

						>
							<Radio.Group>
								<Radio value={ 1 }>商品(包含)</Radio>
								<Radio value={ 0 }>商品(不包含)</Radio>
								{
									!isShowZeroStockVersion
										? (
											<>
												<Radio value={ 2 }>货品(包含)</Radio>
												<Radio value={ 3 }>货品(不包含)</Radio>
											</>
										)
										: ""
								}

							</Radio.Group>
						</Form.Item>
						<Form.Item
							name="precise"
							valuePropName="checked"
						>
							<Checkbox>宝贝精确查询</Checkbox>
						</Form.Item>
					</Row>

					<div style={ { marginLeft: 100 } } >
						<Form.List name="itemSku" initialValue={ [{}] } >
							{(fields, { add, remove }) => (
								<>
									{fields.map(({ key, name, ...restField }, index) => {
										return (
											<Space key={ key } style={ { display: 'flex', marginBottom: 8 } } align="baseline">
												<Form.Item
													{ ...restField }
													name={ [name, 'shortNameIncludingList'] }
													style={ { width: 240 } }
												>
													<InputArrayMulti
														placeholder={ Number(formData.goodsIncludeStatus) > 1 ? "货品简称/编码" : "商品名称/简称/商品编码/id" }
														maxInputNum={ 50 }
														maxTagCount={ 1 }
														maxTagTextLength={ 4 }
														open={ false }
														tokenSeparators={ null }
														style={ { width: 220 } }
													/>
													{/* <InputMulti placeholder={ Number(formData.goodsIncludeStatus) > 1 ? "货品简称/编码" : "商品名称/简称/商品编码/id" } maxInputNum={ 50 } /> */}
												</Form.Item>
												<Form.Item
													{ ...restField }
													name={ [name, 'skuIncludingList'] }
													style={ { width: 240 } }
												>
													<InputArrayMulti
														placeholder="规格名称/编码/别名"
														maxInputNum={ 50 }
														maxTagCount={ 1 }
														maxTagTextLength={ 4 }
														open={ false }
														tokenSeparators={ null }
														style={ { width: 220 } }
													/>
													{/* <InputMulti placeholder="规格名称/编码/别名" maxInputNum={ 50 } /> */}
												</Form.Item>
												{/* <PlusCircleOutlined onClick={ () => add() } />
												{
													index !== 0 ? <MinusCircleOutlined onClick={ () => remove(name) } /> : null
												} */}

											</Space>
										);
									})}
								</>
							)}
						</Form.List>
					</div>

					<Row>
						<Col span={ 12 }>
							<Form.Item
								name="goodsTotalNumRange"
								// labelCol={ { span: 8 } }
								// wrapperCol={ { span: 16 } }
								className={ s['content-range'] }
								label="商品数量"
							>
								<InputRange
									config={ config }
									onChange={ v => handleChange('goodsTotalNumRange', v) }
								/>
							</Form.Item>
						</Col>
						<Col span={ 12 }>
							<Form.Item
								name="goodsTypeNumRange"
								// labelCol={ { span: 8 } }
								// wrapperCol={ { span: 16 } }
								className={ s['content-range'] }
								label="商品种类"
							>
								<InputRange
									config={ config }
									onChange={ v => handleChange('goodsTypeNumRange', v) }
								/>
							</Form.Item>
						</Col>
					</Row>
					<Row>
						<Col span={ 12 }>
							<Form.Item
								name="paymentRange"
								// labelCol={ { span: 8 } }
								className={ s['content-range'] }
								label="订单金额(元)"
							>
								<InputRange
									config={ config }
									onChange={ v => handleChange('paymentRange', v) }
								/>
							</Form.Item>
						</Col>
						<Col span={ 12 }>
							<Form.Item
								name="weightRange"
								// labelCol={ { span: 8 } }
								className={ s['content-range'] }
								label="订单重量(g)"
							>
								<InputRange
									config={ config }
									onChange={ v => handleChange('weightRange', v) }
								/>
							</Form.Item>
						</Col>

					</Row>

					<Form.Item
						name="customizeResidueSendTime"
						label="剩余发货时间"
						className="r-flex r-ai-c"
						wrapperCol={ { span: 10 } }
					>
						<RemainingDeliveryTime />
					</Form.Item>

					<Form.Item
						name="commentsRemarks"
						label="留言备注"
						className="r-flex r-ai-c"
					>
						<Radio.Group>
							<Radio value="EMPTY">全部</Radio>
							{
								FLAG_VALUE.options.map((item:any, index) => {
									if (index > 5) {
										return null;
									}
									return (
										<Radio key={ item.key } value={ item.key }>{item.value}</Radio>
									);
								})
							}
						</Radio.Group>
					</Form.Item>

					<Row>
						<Col span={ 12 }>
							<Form.Item
								name="buyerMessage"
								label="买家留言"
							>
								<InputMulti
									disabled={ buyerMessageDisabled }
									placeholder="买家留言"
									maxInputNum={ 50 }
									maxInputLength={ 1000 }
									lengthErrorMsg="留言最多输入1000个字数，请重新输入"
									numErrorMsg="单次查询最多筛选50个留言，请重新输入"
								/>
							</Form.Item>
						</Col>
						<Col span={ 12 }>
							<Form.Item
								name="sellerMemo"
								label="卖家备注"
							>
								<InputMulti
									disabled={ sellerMemoDisabled }
									placeholder="卖家备注"
									maxInputNum={ 50 }
									maxInputLength={ 1000 }
									lengthErrorMsg="备注最多输入1000个字数，请重新输入"
									numErrorMsg="单次查询最多筛选50个备注，请重新输入"
								/>
							</Form.Item>
						</Col>
					</Row>

					<Form.Item
						name="sellerFlag"
						label="旗帜"

					>
						<Checkbox.Group style={ { width: '100%' } } >
							{
								FLAG_VALUE.options.map((item:any, index) => {
									if (index > 5) {
										return (
											<Checkbox key={ item.key } value={ item.key }>
												<Icon type="flag1" style={ { color: item.value } } size={ 16 } />
											</Checkbox>
										);
									} else {
										return null;
									}
								})
							}
						</Checkbox.Group>
					</Form.Item>

					<Form.Item
						name="refundStatus"
						label="退款状态"

					>
						<Checkbox.Group style={ { width: '100%' } } >
							<Checkbox value="HAS_REFUND">有退款</Checkbox>
							<Checkbox value="NOT_REFUND">无退款</Checkbox>
						</Checkbox.Group>
					</Form.Item>

					<Form.Item
						label="地区"
						name="addressInfo"
						wrapperCol={ { span: 10 } }

					>
						<AddressSelect isInitValue />
					</Form.Item>

					<Form.Item label="订单标签" className="r-mt-5" wrapperCol={ { span: 10 } } name="tradeLabelList">
						<InputSelect styleType="radio" optionsList={ TRADE_LABEL_OPTIONS_1 } />
					</Form.Item>

					<Form.Item label="订单异常" className="r-mt-5" wrapperCol={ { span: 10 } } name="tradeExceptionList">
						<InputSelect styleType="radio" optionsList={ TRADE_EXCEPTION_OPTIONS_1 } />
					</Form.Item>

					<Form.Item
						label="供应商"
						name="supplierIncludingList"
						className="r-mt-14"
						wrapperCol={ { span: 10 } }

					>
						<InputArrayMulti
							placeholder="供应商"
							maxInputNum={ 500 }
							maxTagCount={ 1 }
							maxTagTextLength={ 6 }
							open={ false }
							enterSearch={ false }
							tokenSeparators={ null }
							style={ { flex: 1 } }
						/>
					</Form.Item>
					<Form.Item
						label="档口"
						name="dangKouIncludingList"
						className="r-mt-14"
						wrapperCol={ { span: 10 } }

					>
						<InputArrayMulti
							placeholder="档口"
							maxInputNum={ 500 }
							maxTagCount={ 1 }
							maxTagTextLength={ 6 }
							open={ false }
							enterSearch={ false }
							tokenSeparators={ null }
							style={ { flex: 1 } }
						/>
					</Form.Item>
					<Form.Item
						label="市场"
						name="marketIncludingList"
						className="r-mt-14"
						wrapperCol={ { span: 10 } }

					>
						<InputArrayMulti
							placeholder="市场"
							maxInputNum={ 500 }
							maxTagCount={ 1 }
							maxTagTextLength={ 6 }
							open={ false }
							enterSearch={ false }
							tokenSeparators={ null }
							style={ { flex: 1 } }
						/>
					</Form.Item>
					<Form.Item
						label="达人"
						name="authorIncludingList"
						className="r-mt-14"
						wrapperCol={ { span: 10 } }

					>
						<InputArrayMulti
							placeholder="达人名称/ID"
							maxInputNum={ 500 }
							maxTagCount={ 1 }
							maxTagTextLength={ 6 }
							open={ false }
							enterSearch={ false }
							tokenSeparators={ null }
							style={ { flex: 1 } }
						/>
					</Form.Item>

				</Form>

				<div className={ cs(s['footer-btn-box'], 'r-flex-1') }>
					<div className="r-flex r-ai-c r-mb-8">
						<p style={ { width: 100 } }>查询条件名称：</p>
						<Input value={ curRuleName } onChange={ onChangeRuleName } placeholder="最多输入20字，保存并查询时必填" showCount maxLength={ 20 } style={ { width: 340 } } />
						{/* <Checkbox
							onChange={ (e) => {
								setIsPreciseByTrade(e.target.checked);
							} }
							checked={ isPreciseByTrade }
							className="r-ml-12"
						>精确到订单
						</Checkbox> */}
					</div>
					<p className="r-fs-14 r-c-F5222D r-mb-16" style={ { marginLeft: 100 } }>注：“仅查询”时，不会保存编辑的内容</p>

					<Space size={ 16 } style={ { marginLeft: 100 } }>
						<Button className={ s['btn-width'] } type="primary" loading={ saveLoading } onClick={ () => { handleAction('save'); } }>保存</Button>
						<Button className={ s['btn-width'] } type="primary" loading={ saveLoading } onClick={ () => { handleAction('save&search'); } }>保存并查询</Button>
						<Button className={ s['btn-width'] } type="primary" onClick={ () => { handleAction('search'); } }>仅查询</Button>
						<Button className={ s['btn-width'] } onClick={ onCancel }>取消</Button>
					</Space>
				</div>


			</div>
		);
	};

	const onScroll = (e:any) => {
		if (e.target.scrollTop > 20 && waitLoading) {
			setWaitLoading(false);
		}
	  };

	const renderConditionList = (dataList:any[]) => {
		return (
			<div style={ { height: 519, overflow: 'auto', padding: '0 9px' } } onScroll={ onScroll }>
				{
					dataList.map((item:any, index) => {
						if (index > 20 && waitLoading) {
							return null;
						}
						return (
							<div key={ item?.id } className="r-flex r-ai-c r-jc-sb r-mb-8" >
								<div
									className="r-pointer r-ta-c"
									// style={ { width: 40 } }
									onClick={ () => {
										runAdvancedSearchSaveOrUpdateApi({ isVisible: Number(!item?.isVisible), id: item?.id }).then(res => {
											onSaveAfter();
										});
									} }
								>
									<Icon type={ item?.isVisible === 0 ? 'bukejian' : 'kejian' } className="r-fc-black-45" size={ 16 } />
								</div>
								<Typography.Text
									className={ cs('r-flex-1', 'r-pointer', s['condition-ruleName'], curRuleId === item.id ? 'r-fc-F5821F' : 'r-fc-black-65') }
									// style={ { width: 102 } }
									ellipsis={ { tooltip: item?.ruleName } }
									onClick={ () => { checkHasSaveTips(item?.id, item?.ruleName, item?.ruleContent, onSelectRule); } }
								>
									{item?.ruleName}
								</Typography.Text>
								<CloseOutlined
									className="r-fc-black-45"
									onClick={ () => {
										Modal.confirm({
											title: '系统提示',
											icon: <ExclamationCircleOutlined />,
											content: `确认删除查询条件：【${item?.ruleName}】吗？`,
											okText: '确认',
											onOk: () => {
												runAdvancedSearchDeleteApi({ searchId: item?.id });
											},
											cancelText: '取消',
										});
									} }
								/>
							</div>
						);
					})
				}
			</div>

		);
	};

	useImperativeHandle(ref, () => {
		return {
			onEditCondition: (obj: any) => {
				const { id, ruleName, ruleContent } = obj;
				onSelectRule(id, ruleName, ruleContent);
			}
		};
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	return (
		<Modal
			title={ renderTitle() }
			centered
			width={ 1000 }
			visible={ visible }
			onCancel={ onCancel }
			footer={ false }
			className={ s['preCondition-modal'] }
			bodyStyle={ { padding: '0 0 24px 0' } }
			destroyOnClose
			maskClosable={ false }
		>
			<div className={ cs(s['condition-container'], 'r-flex') } >
				<div className={ cs(s['condition-list']) }>
					<div className={ cs(s['add-btn-container'], 'r-mb-12') }>
						<Button type="primary" onClick={ onCreateRule }>添加查询条件</Button>
					</div>
					{
						// 渲染查询条件列表
						renderConditionList(dataSource)
					}
					<div className={ s['move-btn-container'] }>
						<Button type="link" style={ { width: '50%', fontSize: 12 } } onClick={ () => { onMoveRule('up'); } }>
							<Icon
								pointer
								type="up"
								className="r-mr-4"
								style={ { fontSize: 13, color: '#f5821f' } }
							/>
							上移
						</Button>
						<Button type="link" style={ { width: '50%', fontSize: 12 } } onClick={ () => { onMoveRule('down'); } }>
							<Icon
								pointer
								type="down"
								className="r-mr-4"
								style={ { fontSize: 13, color: '#f5821f' } }
							/>
							下移
						</Button>
					</div>
				</div>

				{
					// 渲染查询条件表单
					renderContentForm()
				}
			</div>

		</Modal>
	  );
});

export default observer(PreConditionModal);
