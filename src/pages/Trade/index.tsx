import React, { memo, useCallback, useEffect, useRef, useState, useMemo, useLayoutEffect } from "react";
import { Alert, Button, Empty, Layout, Modal, ModalFuncProps, Pagination, Radio, Spin, Tooltip, Popover, Input } from "antd";
import cs from 'classnames';
import { observer } from 'mobx-react';
import _, { cloneDeep, entries, isEqual } from "lodash";
import { RouteComponentProps, useParams } from "react-router-dom";
import { runInAction, trace } from "mobx";
import { CopyOutlined, LoadingOutlined, ExclamationCircleFilled, QuestionCircleOutlined } from '@ant-design/icons';
import { color } from "echarts";
import SearchContainer from "./components/SearchContainer";
import TradeList from "./components/TradeList";
import s from './index.module.scss';
import './index.scss';
import SyncContainer from "./components/SyncContainer";
import event from '@/libs/event';
import TradeSortCom from "./components/TradeSortCom/index";
import ColSetCom from "./components/ColSetCom";
import BottomCom from "./components/BottomCom";
import { TradeQueryTradeRequest } from "@/types/trade/search/search";
import { TradeQueryTradeApi } from "@/apis/trade/search";
import { ORDER_STATUS_OPTIONS } from "./components/SearchContainer/constants";
import { handlePackageList } from "./utils";
import { tradeStore, handStore, editGoodsStore } from '@/stores';
import HandOrderModal from "./components/HandOrderModal";
import BatchModifyMemoModal from "./components/BatchModifyMemoModal";
import {
	TradeQueryWarningOrderSummaryApi,
	TradeQueryWarningCountForShipFailApi,
	TradeUserGetUserColumnConfigApi,
	TradeUserGetUserCustomSetApi,
	TradePrintSetUpdatePrintSetApi,
	TradeQueryShopTradeCountApi
} from "@/apis/trade";
import BatchModifyFlagModal from "./components/BatchModifyFlagModal";
import memoFn from '@/libs/memorizeFn';
import { manualMergeTradeHelper, MergeAction } from "./components/ManualMergeTrade/helper";
import BatchExportModal from "./components/BatchExportModal";
import tradeSetStore from "@/stores/trade/tradeSet";
import { useStores } from "@/stores/tool";
import { genePrintContent } from "@/utils/trade/printContent";
import { getTradeExReachStatus } from "@/utils/trade/judgeExpressReach";
import userStore from "@/stores/user";
import TradeOptLogModal from "./components/TradeOptLogModal";
import { local } from "@/libs/db";
import { DEFAULT_COLUMN_CONFIG, EVENT_BUS, paginationPointMap } from "./constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Icon from "@/components/Icon";
import { getTbSecurity } from "@/utils/TbSecurity";
import { setAdParams } from "@/utils/util";
import history from "@/utils/history";
import { IndexSettingSseGetExpressListApi, ItemSmartExpressGetListApi } from "@/apis/setting/smartExpress";
import { IndexSettingSseGetExpressListResponse } from "@/types/schemas/setting/smartExpress";
import { IndexSettingEditInvoicesSenderSettingApi, getExCompanyAll } from "@/apis/user";
import message from "@/components/message";
import events from "@/utils/events";
import PrintStyleSetting from '../Trade/components/ListItem/components/PrintStyleSetting';
import ExReachProgressModal from "./components/ExReachProgressModal";
import EditSidModal from "./components/EditSidModal";
import DealAbnormal from "./components/DealAbnormal";
import { PLAT_HAND, PLAT_SPH, PLAT_XHS, TEMP_ICON, PLAT_ICON_MAP, PLAT_DW, PLAT_KTT } from "@/constants";
import timer, { logTypeEnum } from "@/libs/timer";
import PrintCenter from '@/print/index';
import useClick from '@/utils/hooks/useClick';
import Pointer from "@/utils/pointTrack/constants";
import { copyToPaste, getTimerTraceId } from "@/utils";
import BatchSupplierSendModal from "./components/BatchSupplierSendModal";
import BatchCopyTradeInfoModal from "./components/BatchCopyTradeInfoModal";
import useGetState from "@/utils/hooks/useGetState";
import { getMultiShops } from '@/components-biz/ShopListSelect/shopListUtils';
import SyncShopTempModal from "./components/SyncShopTempModal";
import BatchModifySysMemoModal from "./components/BatchModifyLocalMemoModal";
import SplitOrder from "./components/SplitOrder";
import BatchModifyLocalMemoPicModal from "./components/BatchModifyLocalMemoPicModal";
import { TradeChoiceType } from "@/utils/enum/trade";
import ChangeGoodsModal from "./components/ChangeAndAddGoods/ChangeGoodsModal";
import BatchChangeGoodsModal from "./components/ChangeAndAddGoods/BatchChangeGoodsModal";
import BatchAddGoodsModal from "./components/ChangeAndAddGoods/BatchAddGoodsModal";
import BatchResultModal from "./components/ChangeAndAddGoods/BatchResultModal";
import ShopOrderCount from "./components/ShopOrderCount";

// mobx 引用类型赋值新对象，导致数据有缓存
const filterKddTemp = [-900]; // 无需物流发货模版不展示

const defaultSmartExpressList = [{
	printTemplateName: "全部快递",
	printTemplateId: "",
	id: "all",
}, {
	printTemplateName: "未匹配到快递",
	printTemplateId: "ALL_NONE",
	id: "all_none",
}];


const TradePage = observer((props: RouteComponentProps) => {
	const [isSearching, setIsSearching] = useState(false);
	const {
		syncBeforeSearchObj,
		setSyncBeforeSearchObj,
		isMergePrint,
		selectedTemp,
		setSelectedTemp,
		selectedFhdTemp,
		setSelectedFhdTemp,
		selectedTempGroup,
		showAddGroup,
		setSelectedTempGroup,
		kddTempList,
		fhdTempList,
		setSetting,
		setting,
		setTradeAdvanceSetObj,
		isShowBatchModifyMemoModal,
		setColumnConfig,
		isShowBatchModifyFlagModal,
		setProductSetting,
		isShowBatchExportModal,
		isShowTradeOptLogModal,
		setFixedTemplateComHeight,
		setExpressCompanyList,
		setSmartExpressSwitch,
		smartExpressSwitch, // 智选快递开关
		smartExpressList, // 智选快递列表
		setSmartExpressList,
		setStoreSearchParams,
		setSearchParamsMd5,
		setWarningPrintCount,
		searchParamsMd5,
		oldSearchParamsMd5,
		setOldSearchParamsMd5,
		getNewParamsMd5,
		setWarningCountForShipFail,
		fjrInfoMap,
		tradeAdvanceSetObj,
		shopTradeCount,
		setShopTradeCount,
		tradeListStore: {
			isUseVirtualList,
			setIsUseVirtualList,
			hideByPendingCount,
			hideByRefundCount,
			hideByDropShipCount,
			hideByWaveCount,
			hideCount,
			list,
			setList,
			setListCheckStatus,
			batchOperateLoading,
			setBatchOperateLoading,
			addHandOrder,
			editHandTrade,
			setIsShowPrintStyleSetting,
			isShowPrintStyleSetting,
			exReachProgessObj,
		},
		tradePrintStore: {
			waybillEcho
		},
		importExpress,
	} = tradeStore;

	const storeTradeSet: typeof tradeSetStore = useStores('tradeSetStore');
	const { isShowHandOrderModal, handData } = handStore;
	const { isShowChangeGoodsModal, isShowBatchChangeGoodsModal, isShowBatchAddGoodsModal, isBatchChangeOrAddGoodsResultModal } = editGoodsStore;
	const { userSetting, userInfo: { whiteListSetting, }, } = userStore;
	const [emptyText, setEmptyText] = useState('请点击查询，查看订单数据');
	const [pageInfo, setPageInfo] = useState({
		total: 0,
		totalCount: 0,
		// buyerCount: 0,
	});
	const [searchParams, setSearchParams, getSearchParams] = useGetState<Partial<TradeQueryTradeRequest>>(null);
	const [isFixedTemplateCon, setIsFixedTemplateCon] = useState(false);
	const indexParam: { param?: string } = useParams();
	const [locationSearch, setLocationSearch] = useState<string>(null);
	const [tradeListMessage, setTradeListMessage] = useState<string>("");
	const [syncMessage, setSyncMessage] = useState('');
	const [missedTids, setMissedTids] = useState([]);
	const [missedLabelIdList, setMissedLabelIdList] = useState([]);
	const [missedBuyerNickList, setMissedBuyerNickList] = useState([]);
	const [smartExpressObj, setSmartExpressObj] = useState({});
	const [pageSizeOptions, setPageSizeOptions] = useState(['10', '30', '50', '100', '200', '500', '1000']);
	const [mergeTipsVisible, setMergeTipsVisible] = useState(false);
	const [syncShopTempModalVisible, setSyncShopTempModalVisible] = useState(false);

	const [templateListToggleStatus, setTemplateListToggleStatus] = useState(false);
	const [adBannerWrapperHeight, setAdBannerWrapperHeight] = useState(0);
	const [scurrentSmartExpress, setCurrentSmartExpress] = useState('');

	// 设置模板hover目标
	// const [currentTemp, setCurrentTemp] = useState(0);
	const [currentGroupUpdateTemp, setCurrentGroupUpdateTemp] = useState(null);
	const [showUpdateTips, setShowUpdateTips] = useState(false);
	const showTemplateListToggleBtn = useMemo(() => {
		return isFixedTemplateCon && !isMergePrint && kddTempList.length > 20;
	}, [isFixedTemplateCon, isMergePrint, kddTempList.length]);
	const { tradeListStore: {
		handleChoiceChange
	},
	} = tradeStore;
	// 修改 handleUserColumnConfig 函数，使其支持异步操作
	const handleUserColumnConfig = useCallback((userColumnConfig) => {
		let configObj = JSON.parse(userColumnConfig);
		if (!userStore.isSupplierAccount && !userStore.isFreeSupplierAccount) {
			configObj = configObj.filter(i => i.key != 'distributor'); // 免费版分销商列配置不需要分销商
		}

		// 添加波次管理权限控制
		if (!userStore.hasWaveManagePermission) {
			configObj = configObj.filter(i => i.key != "waveNoList"); // 没有波次管理权限不显示波次号
		}
		return configObj;
	}, [userStore.isSupplierAccount, userStore.isFreeSupplierAccount, userStore.hasWaveManagePermission]);
	useEffect(() => {
		// 如果已经有列配置，则根据权限重新过滤
		if (tradeStore.columnConfig?.id) {
			const updatedConfig = {
				...tradeStore.columnConfig,
				userConfig: handleUserColumnConfig(JSON.stringify(tradeStore.columnConfig.userConfig)),
				default: handleUserColumnConfig(JSON.stringify(tradeStore.columnConfig.default))
			};
			setColumnConfig(updatedConfig);
		}
	}, [userStore.hasWaveManagePermission]);
	// 智选快递列表去重
	const uniqueSmartExpressList = useMemo(() => {
		const uniqueMap = new Map();
		console.log(smartExpressList, 'smartExpressList', isMergePrint, kddTempList);
		smartExpressList?.forEach(item => {
			uniqueMap.set(item.printTemplateId, item);
		});
		// 如果是聚合打印，需要展示模板组，不存在模板组内的展示快递
		if (isMergePrint) {
			kddTempList.forEach(item => {
				// 检查 item.userTemplateList 是否存在且是数组
				if (item.userTemplateList && Array.isArray(item.userTemplateList)) {
					// 找出 userTemplateList 中与 uniqueMap 的 key 一致的项
					const matchedTemplates = [];
					item.userTemplateList.forEach(template => {
						if (uniqueMap.has(template.userTemplateId)) {
							matchedTemplates.push(template.userTemplateId);
							// 从 uniqueMap 中删除匹配的项
							uniqueMap.delete(template.userTemplateId);
						}
					});

					// 如果找到匹配项，可以进行后续处理
					if (matchedTemplates.length > 0) {
						// 例如：将匹配的模板添加到 groupMap
						uniqueMap.set(item.id, {
							printTemplateName: item.groupName,
							printTemplateId: "",
							smartExpressTemplateIdList: (matchedTemplates || []).join(','),
							id: item.id,
						});
					}
				}
			});
		}
		return Array.from(uniqueMap.values());
	}, [smartExpressList, isMergePrint, kddTempList]);

	useEffect(() => {
		const smartExpressObj = {};
		smartExpressList.forEach(i => {
			smartExpressObj[i.printTemplateId] = i;
		});
		setSmartExpressObj(smartExpressObj);
	}, [smartExpressList]);


	useEffect(() => {
		getWarningPrintCount();
	}, []);

	const getWarningPrintCount = async() => {
		const multiShopS = await getMultiShops({});
		TradeQueryWarningOrderSummaryApi({
			isPlatformEmptyQuery: true,
			multiShopS
		}).then(res => {
			setWarningPrintCount(res);
		});
	};

	useEffect(() => {
		TradeQueryWarningCountForShipFailApi().then(res => {
			setWarningCountForShipFail(res);
		});
	}, []);

	useEffect(() => {
		if (setting?.printSetExpandDTO?.showShopTradeCount && searchParams) {
			TradeQueryShopTradeCountApi(searchParams).then(res => {
				setShopTradeCount(res);
			});
		}
	}, [searchParams, setting]);

	useEffect(() => {
		console.log(setting);
		if (setting?.printSetExpandDTO?.showShopTradeCount && shopTradeCount && shopTradeCount.length > 0) {
			const content = document.getElementById("shop-order-count");
			const btn = document.getElementById("show-all-shop");
			if (content?.offsetHeight > 44) {
				btn.style.position = "absolute";
				btn.style.bottom = "0";
				btn.style.right = "0";
				btn.style.display = "block";
			} else {
				btn.style.display = "none";
			}
		}
	}, [shopTradeCount, setting]);

	// 监听广告位高度，然后计算下拉值，使得模板吸顶
	useEffect(() => {
		// 监听广告位高度
		const observer = new ResizeObserver(entries => {
			for (let entry of entries) {
				const rect = entry.contentRect;
				setAdBannerWrapperHeight(rect.height);
			}
		});
		observer.observe(document.querySelector("#ad-banner-wrapper"));
		return () => {
			observer.disconnect();
		};
	}, []);

	useEffect(() => {
		const eventObj: Record<string, string> = {
			'printBatch.reSearch': 'handleReSearch',
		};
		events.sub('openFhSet', openFhSet, false);
		const eventHandle = (type: 'on' | 'off') => {
			for (const key in eventObj) {
				if (Object.prototype.hasOwnProperty.call(eventObj, key)) {
					event[type](key, (eventMapHandle[eventObj[key]]));
				}
			}
		};

		eventHandle('on');

		// setAdParams().then((res) => {
		// 	window.Ads.prototype.getAdShow('trade');
		// });

		// 获取列配置
		if (!tradeStore.columnConfig?.id) {
			TradeUserGetUserColumnConfigApi({}).then(res => {
				setColumnConfig({
					userConfig: handleUserColumnConfig(res?.customColumnConfig || res?.defaultColumnConfig || DEFAULT_COLUMN_CONFIG),
					default: handleUserColumnConfig(res?.defaultColumnConfig || DEFAULT_COLUMN_CONFIG),
					id: res?.id || '',
				});
			}).catch((e) => {
				console.log('e: ', e);
				setColumnConfig({
					userConfig: JSON.parse(DEFAULT_COLUMN_CONFIG),
					default: JSON.parse(DEFAULT_COLUMN_CONFIG),
					id: '',
				});
			});
		}

		// 产品内容设置
		if (!tradeStore.productSetting || !tradeStore.productSetting?.userId) {
			TradeUserGetUserCustomSetApi({}).then(res => {
				setProductSetting(res);
			});
		}

		getExCompanyAll({}).then(res => {
			setExpressCompanyList(res);
		});

		// 获取智选快递列表
		ItemSmartExpressGetListApi({}).then(res => {
			// 智选快递的开关
			if (res.smartExpressConfigVO) {
				setSmartExpressSwitch(res.smartExpressConfigVO.openStatus);
			}
			if (res.smartExpressTemplateVOList) {
				setSmartExpressList(res.smartExpressTemplateVOList);
			}
		});

		let templateCon = document.querySelector('#templateCon');
		let observer = new IntersectionObserver((entries) => {
			let templateDom = entries[entries.length - 1];
			if (templateDom.isIntersecting) {
				setTemplateListToggleStatus(false);
			} else {
				setTemplateListToggleStatus(local.get('batchPrint.toggleTemplateList') || false);
			}
			setIsFixedTemplateCon(!templateDom.isIntersecting);
		});
		if (templateCon && observer) {
			observer.observe(templateCon);
		}
		const handleChangeFhdFjr = async(params) => {
			let res = await IndexSettingEditInvoicesSenderSettingApi(params);
			userStore.getInvoicesSenderSetting({
				pageNo: 1,
				pageSize: 200,
				userId: userStore.userInfo?.userId
			});
			return res;
		};
		events.sub('fhdFjrChange', handleChangeFhdFjr, false);

		return () => {
			window.scrollTo(0, 0);
			eventHandle('off');
			events.removeSub('openFhSet', openFhSet);
			if (templateCon && observer) {
				observer.unobserve(templateCon);
			}
			// * 切换页面时 清空mobx中的数据
			setList([]);
			events.removeSub('fhdFjrChange', handleChangeFhdFjr);
		};
	}, []);

	// 页面路由跳转 携带参数处理
	useEffect(() => {
		if (props.location.search && locationSearch !== props.location.search) {
			setLocationSearch(props.location.search);
		}
	}, [props.location.search, locationSearch]);

	// 初始化高级设置
	useEffect(() => {
		const getAdvancedSet = async() => {
			let res = await memoFn.getAdvancedSet();
			// debugger;
			if (res) {
				// 为新用户爆款打单配置默认值
				if (!res.printSetExpandDTO) {
					const { version } = await userStore.getUserInfo();
					let topItemShow = "";
					let topSkuShow = "";
					if (version == 2) {
						topItemShow = `{"itemPerspective":["21","24","22","23"]}`;
						topSkuShow = `{"itemPerspective":["21","22","23"]}`;
					} else {
						topItemShow = `{"itemPerspective":["1","2","3"],"sysPerspective":["11","12"]}`;
						topSkuShow = `{"itemPerspective":["2","1"],"sysPerspective":["11","12","13"]}`;
					}
					res.printSetExpandDTO = { topItemShow, topSkuShow };
					memoFn.updateAdvancedSet(res);
				}
				if (res?.groupPrintSetJsonString && typeof res.groupPrintSetJsonString === 'string') {
					try {
						res.groupPrintSetJsonString = JSON.parse(res.groupPrintSetJsonString);
						if (res?.groupPrintSetJsonString?.printOrderByHandOrder || res?.groupPrintSetJsonString?.handOrderMatchPlatform) {
							res.groupPrintSetJsonString.orderMatchSetting = [// 订单匹配规则
								{
									platform: PLAT_HAND,
									bindControlType: res.groupPrintSetJsonString.handOrderMatchPlatform
								},
								{
									platform: PLAT_DW,
									bindControlType: res.groupPrintSetJsonString.handOrderMatchPlatform
								},
								{
									platform: PLAT_KTT,
									bindControlType: res.groupPrintSetJsonString.handOrderMatchPlatform
								}
							];
							delete res.groupPrintSetJsonString.printOrderByHandOrder;
							delete res.groupPrintSetJsonString.handOrderMatchPlatform;
						}
						// 只默认去除小红书的
						res.groupPrintSetJsonString.orderMatchSetting = res?.groupPrintSetJsonString?.orderMatchSetting.filter(o => ![PLAT_XHS, PLAT_SPH].includes(o.platform));
						memoFn.updateAdvancedSet(res);
					} catch (error) {
						console.error(error);
					}
				} else if (res?.groupPrintSetJsonString?.orderMatchSetting.length) {
					let includesKtt = false;
					res.groupPrintSetJsonString.orderMatchSetting = res?.groupPrintSetJsonString?.orderMatchSetting.filter(o => {
						if (o.platform == PLAT_KTT) includesKtt = true;
						return ![PLAT_XHS, PLAT_SPH].includes(o.platform);
					});
					if (!includesKtt) {
						res?.groupPrintSetJsonString?.orderMatchSetting?.push(
							{
								platform: PLAT_KTT,
								bindControlType: '3'
							}
						);
					}
					memoFn.updateAdvancedSet(res);
				}
				if (!("multiplePackageDelivery" in res?.printSetExpandDTO)) {
					res.printSetExpandDTO = { ...res.printSetExpandDTO, multiplePackageDelivery: true };
				}
				setSetting(res);
				setTradeAdvanceSetObj(res);
			}
		};
		getAdvancedSet();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [setSetting]);

	// 初始化打印内容设置和过滤词
	useEffect(() => {
		tradeSetStore.getPrintContentSet();
		tradeSetStore.getFilterWord();
		userStore.getUserSetting();
		userStore.getSystemSetting();
	}, []);

	useEffect(() => {
		(async() => {
			const keyWords = storeTradeSet.filterWord?.map(i => i);
			setList(list.map(item => ({
				...item,
				printContent: genePrintContent(item, storeTradeSet.printContentSet, keyWords, tradeStore.tradeAdvanceSetObj),
			})));
		})();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [storeTradeSet.printContentSet, storeTradeSet.filterWord, storeTradeSet.filterPatternList, tradeStore.tradeAdvanceSetObj]);


	/** 获取订单可达 */
	useEffect(() => {
		// 当模板选择有更新时，所有被选择的订单都要重新回显单号
		let echoList = list.filter((i) => i.isChecked);
		if (echoList.length) {
			waybillEcho(echoList);
		}
		getTradeExReachStatus();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [selectedTemp, selectedTempGroup, isMergePrint]);

	useLayoutEffect(() => {
		batchOperateLoading && batchOperateLoading();
		setBatchOperateLoading(null);
	});

	// 批打查询
	useEffect(() => {
		if (searchParams && searchParams.status) {
			if (isSearching) return;
			let syncShopList = window.sessionStorage.getItem('syncShopList');
			if (syncShopList) {
				// 存在正在同步的任务 先不去查询
				message.warning('订单同步中');
			} else {
				if (syncBeforeSearchObj.switch && searchParams.pageNo === 1) {
					event.emit('printBatch.startSync');
					return;
				}
				getTimerTraceId().then(traceId => {
					window.tradeTraceId = traceId;
					timer.start(`批打查询`);
					setList([]);
					setIsSearching(true);
					setListCheckStatus(0);
					setStoreSearchParams({ ...searchParams });
					setMissedTids([]);
					setMissedLabelIdList([]);
					setMissedBuyerNickList([]);
					let params: any = {};
					const random = Math.random().toString(36).substring(2);
					const { sysSkuId, sysItemId, skuId, itemId, ...rest } = searchParams;
					setSearchParamsMd5({ ...rest, random });
					params = searchParams;
					const { allPrintStatus, ...other } = params;
					TradeQueryTradeApi({ ...other, ...allPrintStatus || {} }).then(async res => {
						timer.stop(`批打查询`, {
							type: logTypeEnum['订单模块'],
							desc: `${searchParams.pageSize}单`
						});
						console.log('TradeQueryTradeApi res:', res);
						// res.data.message = "大促消费提示";
						const { message, syncBeforeQuery = false, syncBeforeDesc = '' } = res.data;
						message && setTradeListMessage(message);
						setSyncBeforeSearchObj({
							switch: syncBeforeQuery,
							desc: syncBeforeDesc
						});
						setSyncMessage(syncBeforeQuery ? syncBeforeDesc : '');
						if (res?.data?.missedTids) {
							setMissedTids(res.data.missedTids);
						}
						if (res?.data?.missedLabelIdList) {
							setMissedLabelIdList(res.data.missedLabelIdList);
						}
						if (res?.data?.missedBuyerNickList) {
							setMissedBuyerNickList(res.data.missedBuyerNickList);
						}
						if (res?.data?.list) {
							let len = res.data.list.length;
							if (!len) setEmptyText('暂无数据');
							timer.start(`订单数据处理`);
							let _list = await handlePackageList(res.data.list, true, searchParams);
							timer.stop(`订单数据处理`, {
								type: logTypeEnum['订单模块'],
								desc: `${len}条`,
							});
							timer.start(`订单渲染`);
							setIsUseVirtualList(_list.length > 200);
							setList(_list);
							setPageInfo(prev => ({
								total: searchParams.pageNo === 1 ? res?.data?.total : prev.total,
								totalCount: searchParams.pageNo === 1 ? res.data.totalCount : prev.totalCount,
								// buyerCount: searchParams.pageNo === 1 ? res.data.buyerCount : prev.buyerCount,
							}));
							// 设置一下当前取号打印并发数传给打印
							window.erpData.batchGetYdnoConfigList = res?.data?.batchGetYdnoConfigList || {};
							window.erpData.waybillGetConcurrentCount = res?.data?.waybillGetConcurrentCount || 4;
							window.erpData.batchGetElecSwitch = !!res?.data?.batchGetElecSwitch;
							window.erpData.batchGetYdNoTradeCount = res?.data?.batchGetYdNoTradeCount;
							getTradeExReachStatus();
						}
						if (searchParams?.changeSelectAll) {
							handleChoiceChange({
								type: TradeChoiceType.全选,
							});
							setTimeout(() => {
								if (isMergePrint) {
									handleSelectGroupChange({ target: { value: "-901" } });
								} else {
									handleSelectTempChange({ target: { value: -901 } });
								}
							}, 500);
						}
						setIsSearching(false);
					}).catch(error => {
						setIsSearching(false);
						setEmptyText('暂无数据');
						setPageInfo({
							total: 0,
							totalCount: 0,
							// buyerCount: 0,
						});
					});
				}).catch(() => {
					console.error('traceId error');
				});
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [searchParams]);
	const handlePrintModeTips = (val: boolean, isBtn?: boolean) => {
		let localStatus = localStorage.getItem('printModeTips');
		if (localStatus) {
			setMergeTipsVisible(val);
		} else if (isBtn) {
			setMergeTipsVisible(val);
			localStorage.setItem('printModeTips', 'true');
		}
	};
	const MergePrintTips = () => {
		return (
			<div style={ { width: '246px', fontSize: '14px' } }>
				<p>多平台多店铺一起打印，智能匹配订单与对应平台模版</p>
				<p style={ { textAlign: 'right' } }>
					<Button onClick={ () => { handlePrintModeTips(false, true); } } type="link">我知道了</Button>
				</p>
			</div>
		);
	};
	const TempUpdateTips = (props) => {
		const { tempId } = props;
		return (
			<div style={ { width: '246px', fontSize: '12px' } }>
				<p>平台对该快递面单样式进行了更新，使用旧模板打印可能会导致<span style={ { color: 'red' } }>面单打印不全</span>。建议对该模板进行<span style={ { color: '#fd8204', textDecoration: 'underline', cursor: 'pointer' } } onClick={ (e) => { openTempEdit(tempId); } }>编辑修改</span></p>
				<p style={ { textAlign: 'right' } }>
					<Button type="link">我知道了</Button>
				</p>
			</div>

		);
	};

	// const requestAgain = () => {
	// 	const lastSearchParams = getSearchParams();
	// 	const newSearchParams = {
	// 		...getSearchParams(),
	// 		// timestamp: new Date().valueOf()
	// 	};
	// 	setSearchParams(newSearchParams);
	// };
	useEffect(() => {
		let localStatus = localStorage.getItem('printModeTips');
		setMergeTipsVisible(!localStatus);
		// event.on(EVENT_BUS.REQUEST_AGAIN, requestAgain);
		// return () => {
		// 	event.off(EVENT_BUS.REQUEST_AGAIN, requestAgain);
		// };
	}, []);
	const openFhSet = () => {
		!isShowPrintStyleSetting && setIsShowPrintStyleSetting(true);
	};

	const closePrintSetting = () => {
		setIsShowPrintStyleSetting(false);
	};

	const eventMapHandle: Record<string, any> = {
		handleReSearch: () => {
			// 初始进入后没有点击查询 而直接点击高级设置的合并订单时 手动设置一下查询参数
			setSearchParams(prev => {
				if (!prev) {
					event.emit('tradeSetting.handleSearchParams');
					return null;
				} else {
					return {
						...prev,
						pageNo: 1
					};
				}
			});

		},
	};

	// 修改查询参数
	const handleSearch = useCallback((searchParams: TradeQueryTradeRequest) => {
		setSearchParams(prev => ({
			// ...prev,
			...searchParams,
			"pageNo": 1,
			"pageSize": prev?.pageSize || local.get('printBatch.pageSize') || 30,
			"testStatus": 0,
			smartSelectExpress: '',
		}));
	}, []);

	const onChange = (pageNo: number, pageSize: number) => {
		if (+searchParams?.pageSize !== pageSize) {
			sendPoint(paginationPointMap[pageSize]);
		}
		local.set('printBatch.pageSize', pageSize);
		setSearchParams(prev => ({
			...prev,
			pageNo: pageSize === prev?.pageSize ? pageNo : 1,
			pageSize,
		}));
	};


	/**
	 * 处理快递模板选择
	 * @param e
	 */
	const handleSelectTempChange = async(e: any) => {
		console.log("kddTempList:::", kddTempList);
		let temp = kddTempList.find((i) => i?.Mode_ListShowId === e.target.value);
		console.log("kddTempList temp::::", temp);
		if (temp?.customerChange) {
			let tempDetail = await window.printAPI.getTempDetailInfo({
				templateId: e.target.value
			});
			// 获取模板详情判断是否超区
			setShowUpdateTips(!!tempDetail?.ModeListShow?.overFlow);

		} else {
			setShowUpdateTips(false);

		}
		temp && setSelectedTemp(temp);
	};

	/**
	 * 处理发货单模板选择
	 * @param e
	 */
	const handleSelectFhdTempChange = async(e: any) => {
		let temp = fhdTempList.find((i) => i.Mode_ListShowId === e.target.value);
		setSelectedFhdTemp(temp);
	};
	// 打印模板类型切换
	const handlePrintModeChange = (e: any) => {
		if (!kddTempList.length && !showAddGroup) {
			message.warning('请等待当前打印模式加载完成');
			return;
		}
		const _setting = cloneDeep(setting);
		const pointType = e.target.value == 1 ? '单快递模版打印' : '多平台聚合打印';
		_setting.groupPrintSetJsonString.openMergePrint = e.target.value;
		// 1 关闭 2 打开
		let params = { ..._setting };
		if (params?.groupPrintSetJsonString) {
			params.groupPrintSetJsonString = (JSON.stringify(params.groupPrintSetJsonString) as any);
		}
		TradePrintSetUpdatePrintSetApi({ ...params }).then(res => {
			sendPoint(Pointer[pointType]);
			setTradeAdvanceSetObj({ ..._setting });
			memoFn.updateAdvancedSet({ ..._setting });
			setSetting(_setting);
		});
	};
	const openTempEdit = ({ e, printType }) => {
		PrintCenter.showTemplateMain({
			printType,
			tempId: e?.target?.value || e,
			groupTempID: currentGroupUpdateTemp?.ModeListShow?.Mode_ListShowId,
			fjrMap: {
				"isKddBindFjr": true,
				"senderInfo": fjrInfoMap.kdd,
				"isUseMore": true,
				"isUseCommon": false
			}
		});
	};
	const handleClickTemp = useClick({
		doubleCallback: openTempEdit
	});

	const handleSelectGroupChange = async(e: any) => {
		let temp = kddTempList.find((i) => i.id === e.target.value);
		if (!temp.userTemplateList.length) {
			message.warning('当前模板组暂无模板，请添加模板或更换模板组');
		} else {
			let isChange = temp.userTemplateList.find((item) => {
				return item.customerChange;
			});
			if (isChange) {
				let tempDetail = await window.printAPI.getTempDetailInfo({
					templateId: isChange.userTemplateId
				});
				// 记录一下当前模板组存在变更的模板信息
				setCurrentGroupUpdateTemp(tempDetail);
				// 获取模板详情判断是否超区
				setShowUpdateTips(!!tempDetail?.ModeListShow?.overFlow);
			} else {
				setShowUpdateTips(false);
			}
		}
		setSelectedTempGroup(temp);

		// let params = {
		// 	groupId: temp.id
		// }
		// getGroupInfo(params).then(res=>{
		// 	if(res.success){
		// 		setSelectedTempGroup(res.data);
		// 	}else{
		// 		message.error('获取模板组详情失败')
		// 	}
		// }).catch(err=>{
		// 	message.error(err)
		// })
	};
	const renderNoList = () => {
		if (isSearching) {
			return <Spin style={ { width: '100%' } } tip="查询订单中" />;
		} else {
			return <Empty description={ emptyText } />;
		}
	};

	const handleTempAct = (type: string) => {
		switch (type) {
			case 'add':
				{ window.printAPI.addKddTemplate(); }
				break;
			case 'del':
				{ window.printAPI.delKddTemplate(); }
				break;
			case 'addFhd':
				{ window.printAPI.addFhdTemplate(); }
				break;
			case 'delFhd':
				{ window.printAPI.delFhdTemplate(); }
				break;
			default:
				break;
		}
	};

	const searchHideOrder = (hideType: string) => {
		if (hideType === 'pending') {
			runInAction(() => {
				message.success(`${hideByPendingCount}个挂起订单已展示在列表中`);
				setList(list.map(item => {
					item.isHideByPending = false;
					return item;
				}));
			});
		} else if (hideType === 'refund') {
			runInAction(() => {
				message.success(`${hideByRefundCount}个已退款订单已展示在列表中`);
				setList(list.map(item => {
					item.isHideByRefund = false;
					return item;
				}));
			});
		} else if (hideType == 'dropShip') {
			runInAction(() => {
				message.success(`${hideByDropShipCount}个代发订单已展示在列表中`);
				setList(list.map(item => {
					item.isHideByDropShip = false;
					return item;
				}));
			});
		} else if (hideType == 'wave') {
			runInAction(() => {
				message.success(`${hideByWaveCount}个已生成波次订单已展示在列表中`);
				setList(list.map(item => {
					item.isHideByWave = false;
					return item;
				}));
			});
		}
	};

	const reList = useMemo(() => {
		return manualMergeTradeHelper.tradeFactory(list).filter(item => !item.isFilter
			&& !item.isHideByPending
			&& !item.isHideByRefund
			&& !item.isHideByDropShip
			&& !item.isHideByWave);
	}, [list]);

	const renderTradeHideInfo = () => {
		return (
			<span className="r-ml-10">
				（当前页{hideCount}个订单已隐藏，其中
				{hideByPendingCount ? (
					<span>
						<span onClick={ () => { searchHideOrder('pending'); } } style={ { color: '#FD8204' } } className="r-click r-ml-2 r-mr-2">{hideByPendingCount}</span>
						个挂起订单{hideByRefundCount || hideByDropShipCount ? ',' : ''}
					</span>
				) : ''}
				{hideByRefundCount ? (
					<span>
						<span onClick={ () => { searchHideOrder('refund'); } } style={ { color: '#FD8204' } } className="r-click r-ml-2 r-mr-2">{hideByRefundCount}</span>
						个退款订单{hideByDropShipCount ? ',' : ''}
					</span>
				) : ''}
				{hideByDropShipCount ? (
					<span>
						<span onClick={ () => { searchHideOrder('dropShip'); } } style={ { color: '#FD8204' } } className="r-click r-ml-2 r-mr-2">{hideByDropShipCount}</span>
						个代发订单
					</span>
				) : ''}
				{hideByWaveCount ? (
					<span>
						{hideCount === hideByWaveCount ? '' : ','}
						<span
							onClick={ () => { searchHideOrder('wave'); } }
							style={ { color: '#FD8204' } }
							className="r-click r-ml-2 r-mr-2"
						>
							{hideByWaveCount}
						</span>
						个已生成波次订单
					</span>
				) : ''}
				）
			</span>
		);
	};
	const renderTooltip = (item: any) => {
		let warningNumVal = setting?.groupPrintSetJsonString?.numTipVal || 0;
		let isOpenOddNum = setting?.groupPrintSetJsonString?.openOddNum == 2;

		return (
			item.length && item.map((it: any) => {
				return <p style={ { color: '#000', fontSize: '12px' } }>{it.tempType + '-' + it.branchName + '，剩余单号：'}<span style={ isOpenOddNum && it?.quantity < warningNumVal ? { color: 'red' } : {} }>{it.quantity}</span></p>;
			})
		);
	};
	const renderLoading = () => {
		const loadingOutlined = <LoadingOutlined style={ { fontSize: 20 } } spin />;

		return <Spin indicator={ loadingOutlined } />;
	};

	const SyncTempBtnNode = (
		userStore.shopList?.filter(i => i.platform == PLAT_SPH)?.length ? (
			<Button size="small" style={ { transform: 'translateY(-5px)' } } type="primary" onClick={ () => setSyncShopTempModalVisible(true) }>
				同步视频号电子面单
			</Button>
		) : null
	);
	const renderFhdTempList = () => {
		return (
			<Radio.Group value={ selectedFhdTemp?.Mode_ListShowId } onChange={ handleSelectFhdTempChange } >
				{fhdTempList.map((i) => (
					<Radio key={ i.Mode_ListShowId } value={ i.Mode_ListShowId } className={ cs(s["temp_item"]) } onClick={ (e) => { handleClickTemp({ e, printType: 'fhd' }); } }>
						{i.ExcodeName}
					</Radio>
				))}
				<div className={ cs(s["temp-action"]) }>
					<span className={ cs(s["temp-add"]) } title="添加发货单模板" onClick={ () => handleTempAct('addFhd') } />
					<span className={ cs(s["temp-subtract"]) } title="删除发货单模板" onClick={ () => handleTempAct('delFhd') } />
					{/* {SyncTempBtnNode} */}
				</div>
			</Radio.Group>
		);
	};
	const renderKddTempList = () => {
		// 如果当前是智选快递查询，需要过滤出符合智选快递模板的快递模板
		console.log(12);

		let curSmartExpressTempId = searchParams?.smartExpressTemplateId;
		let newKddTempList = cloneDeep(kddTempList);
		let warningNumVal = setting?.groupPrintSetJsonString?.numTipVal || 0;
		let isOpenOddNum = setting?.groupPrintSetJsonString?.openOddNum == 2;
		if (curSmartExpressTempId) {
			newKddTempList = newKddTempList.filter(item => item.Mode_ListShowId == curSmartExpressTempId);
		} else {
			newKddTempList = newKddTempList.filter(item => !filterKddTemp.includes(+item.Exid));
		}
		// 假如最终没有匹配到任何符合要求的模板组，那需要展示所有的模板组
		if (!newKddTempList?.length) {
			newKddTempList = kddTempList;
		}
		return (
			<Radio.Group value={ selectedTemp?.Mode_ListShowId } onChange={ handleSelectTempChange } >
				{newKddTempList.map((i) => (
					<Radio key={ i.Mode_ListShowId } value={ i.Mode_ListShowId } className={ cs(s["temp_item"]) } onClick={ (e) => { handleClickTemp({ e, printType: 'kdd' }); } }>
						{
							(selectedTemp?.Mode_ListShowId === i.Mode_ListShowId && showUpdateTips) && (
								<span >
									<Popover
										title="快递模板有更新"
										content={ <TempUpdateTips tempId={ i.Mode_ListShowId } /> }
										placement="bottom"
									// visible={ currentTemp === i.Mode_ListShowId }
									>
										<ExclamationCircleFilled style={ { fontSize: '16px', color: 'red', marginRight: '5px' } } />
									</Popover>
								</span>
							)
						}
						<span style={ { color: showUpdateTips && i.Mode_ListShowId === selectedTemp?.Mode_ListShowId ? 'red' : '' } }>
							{
								TEMP_ICON[i.KddType] && (
									<Icon type={ TEMP_ICON[i.KddType] } style={ { fontSize: `16px`, padding: '0 5px', verticalAlign: 'text-bottom' } } />
								)
							}

							{i.ExcodeName}
						</span>
						{!!userSetting?.isShowBranches && (
							<em className={ cs(s["cnwd_show_wrap"]) }>
								{i.wd?.branchName && <span className={ cs(s["cnwd_show"]) }>&nbsp;-&nbsp;{i.wd?.branchName}</span>}
								{i.wd?.branchName && i.wd?.quantity && <span className={ cs(s["cnwd_num"]) } style={ isOpenOddNum && i.wd?.quantity < warningNumVal ? { color: 'red' } : {} }>{i.wd?.quantity}</span>}
							</em>
						)}
					</Radio>
				))}
				<div className={ cs(s["temp-action"]) }>
					<span className={ cs(s["temp-add"]) } title="添加快递单模板" onClick={ () => handleTempAct('add') } />
					<span className={ cs(s["temp-subtract"]) } title="删除快递单模板" onClick={ () => handleTempAct('del') } />
					{/* {SyncTempBtnNode} */}
				</div>
			</Radio.Group>
		);
	};
	const renderMergeKddTempList = () => {
		// 如果当前是智选快递查询，需要过滤出符合智选快递模板的快递模板组
		let newKddTempList = cloneDeep(kddTempList);
		let warningNumVal = setting?.groupPrintSetJsonString?.numTipVal || 0;
		let isOpenOddNum = setting?.groupPrintSetJsonString?.openOddNum == 2;
		let curSmartExpressTempIdList = searchParams?.smartExpressTemplateIdList?.split(',');
		if (curSmartExpressTempIdList?.length) {
			newKddTempList = newKddTempList
				.filter(item => {
					return item.userTemplateList?.some(temp => curSmartExpressTempIdList.includes(temp.userTemplateId));
				});
		} else {
			newKddTempList = newKddTempList.filter(item => !filterKddTemp.includes(+item.Exid));
		}
		// 假如最终没有匹配到任何符合要求的模板组，那需要展示所有的模板组
		if (!newKddTempList?.length) {
			newKddTempList = kddTempList;
		}
		return (
			!kddTempList.length ? (
				<div className={ cs(s["add_group"]) } onClick={ () => handleTempAct('del') }>
					新建模板组
				</div>
			) : (
				<Radio.Group value={ selectedTempGroup?.id } onChange={ handleSelectGroupChange } >
					{newKddTempList.map((i) => (
						<Radio key={ i.id } value={ i.id } className={ cs(s["temp_item"]) } onClick={ (e) => { handleClickTemp({ e, printType: 'kdd' }); } }>
							{
								(selectedTempGroup.id === i.id && showUpdateTips) && (
									<span >
										<Popover
											title="快递模板有更新"
											content={ <TempUpdateTips tempId={ i.id } /> }
											placement="bottom"
										// visible={ currentTemp === i.id }
										>
											<ExclamationCircleFilled style={ { fontSize: '16px', color: 'red', marginRight: '5px' } } />
										</Popover>
									</span>
								)
							}
							<span style={ { color: showUpdateTips && i.id === selectedTempGroup.id ? 'red' : '' } }>{i.groupName}</span>

							{!!userSetting?.isShowBranches && !!i.wd && !!i.wd.length && (
								<Tooltip placement="bottomLeft" title={ renderTooltip(i.wd) } color="#fff">
									<em className={ cs(s["cnwd_show_wrap"]) }>
										{i.wd[0]?.branchName && <span className={ cs(s["cnwd_show"]) }>&nbsp;-&nbsp;{i.wd[0]?.branchName}</span>}
										{i.wd[0]?.branchName && i.wd[0]?.quantity && <span className={ cs(s["cnwd_num"]) } style={ isOpenOddNum && i.wd[0]?.quantity < warningNumVal ? { color: 'red' } : {} }>{i.wd[0]?.quantity}</span>}
									</em>
								</Tooltip>

							)}
						</Radio>
					))}
					<div className={ cs(s["temp-action"]) }>
						<span className={ cs(s["temp-add"]) } title="添加快递单模板" onClick={ () => handleTempAct('del') } />
						<span className={ cs(s["temp-subtract"]) } title="删除快递单模板" onClick={ () => handleTempAct('del') } />
						{/* {SyncTempBtnNode} */}
					</div>
				</Radio.Group>
			)

		);
	};

	// 智选快递查询
	const handleSearchSmartExpress = (item) => {
		sendPoint(Pointer.订单_智选快递_查询);
		if (isSearching) return;
		setSelectedTemp({});
		setSelectedTempGroup({});
		setCurrentSmartExpress(item.id);
		setSearchParams(prev => {
			if (!prev) {
				prev = {
					smartExpressTemplateId: item?.printTemplateId || "",
					smartExpressTemplateIdList: item.smartExpressTemplateIdList,
				};
				event.emit('tradeSetting.handleSearchParams', prev);
			}
			prev.smartExpressTemplateId = item?.printTemplateId || "";
			prev.smartExpressTemplateIdList = item.smartExpressTemplateIdList,
			prev.pageNo = 1;
			return { ...prev };
		});
	};


	const renderNoMatchTradeList = () => {
		const messageContent = (
			<div className="r-fs-14 r-bold">
				以下<span className="r-bold r-c-error">&nbsp;{missedTids.length}&nbsp;</span>个订单编号未查询到，请确认信息是否填写正确
			</div>
		);
		const description = missedTids.map((i, index) => {
			return (
				<span className="r-c-error r-bold">
					{i}{index + 1 === missedTids.length ? "" : ","}&nbsp;
				</span>
			);
		});
		const labelIdmessageContent = (
			<div className="r-fs-14 r-bold">
				以下<span className="r-bold r-c-error">&nbsp;{missedLabelIdList.length}&nbsp;</span>个标签唯一码未查询到，请确认信息是否填写正确
			</div>
		);
		const labelIdDescription = missedLabelIdList.map((i, index) => {
			return (
				<span className="r-c-error r-bold">
					{i}{index + 1 === missedLabelIdList.length ? "" : ","}&nbsp;
				</span>
			);
		});
		// 买家昵称
		const buyerNickmessageContent = (
			<div className="r-fs-14 r-bold">
				以下<span className="r-bold r-c-error">&nbsp;{missedBuyerNickList.length}&nbsp;</span>个买家昵称在 淘宝/天猫 未查询到数据，请确认信息是否填写正确
				<Popover content="其余平台不论是否查询到数据，暂都不做查询结果提示" trigger="hover">
					<QuestionCircleOutlined className={ s.qusStyle } />
				</Popover>
			</div>
		);
		const buyerNickDescription = missedBuyerNickList.map((i, index) => {
			return (
				<span className="r-c-error r-bold">
					{i}{index + 1 === missedBuyerNickList.length ? "" : ","}&nbsp;
				</span>
			);
		});
		if (missedTids.length) {
			return (
				<div>
					<Alert
						className={ s["no-match-alert"] }
						message={ messageContent }
						description={ <div>{description}</div> }
						type="warning"
						showIcon
						closable
					/>
				</div>
			);
		} else if (missedBuyerNickList.length) {
			return (
				<div>
					<Alert
						className={ s["no-match-alert"] }
						message={ buyerNickmessageContent }
						description={ <div>{buyerNickDescription}</div> }
						type="warning"
						showIcon
						closable
					/>
				</div>
			);
		} else if (missedLabelIdList.length) {
			return (
				<div>
					<Alert
						className={ s["no-match-alert"] }
						message={ labelIdmessageContent }
						description={ <div>{labelIdDescription}</div> }
						type="warning"
						showIcon
						closable
					/>
				</div>
			);
		}

	};

	// 当前查询栏
	const renderTemplateCom = (isFixed: boolean) => {
		if (isFixed) {
			setTimeout(() => {
				setFixedTemplateComHeight(document.querySelector('.template-con-fixed')?.clientHeight || 0);
			}, 0);
		}
		const renderCountItem = (item) => {
			return (
				<span className="r-mr-8" style={ { display: "inline-block" } }>
					<Icon className="r-mr-5" svg size={ 16 } type={ PLAT_ICON_MAP[item.platform?.toLocaleLowerCase()] } style={ { verticalAlign: 'middle' } } />
					<span style={ { verticalAlign: 'middle', display: "inline-block" } }>{item.sellerNick}：{item.tradeNum}</span>
				</span>
			);
		};
		return (
			<Layout style={ { top: 48 + 44 + adBannerWrapperHeight } } className={ `${isFixedTemplateCon && isFixed ? 'template-con-fixed' : ''} kdzs-section template-con` } id="templateCon">
				<div className={ cs(s.searchInfoContainer, 'r-fw-500') } >
					{!isFixed && (
						<>
							<div>当前查询：
								<span className="r-c-666 ">{ORDER_STATUS_OPTIONS.options.find(item => item.key === searchParams?.status)?.searchName}</span>
								<span className="ant-btn-link r-ml-20">{tradeListMessage}</span>
								<span className="ant-btn-link r-ml-20">{syncMessage}</span>
							</div>
							<div className="r-mt-12 r-mb-8 r-flex">
								<div style={ { flexShrink: "0" } }>订单情况：</div>
								<div className="r-c-666" id="order-status-wrap" style={ { maxHeight: "44px", overflow: "hidden", position: "relative", paddingRight: "90px" } }>
									<span>
										{/* {pageInfo.buyerCount}买家/ */}
										{pageInfo.totalCount}订单 {searchParams?.pageNo || 1}/{Math.ceil((pageInfo?.total || 1) / (searchParams?.pageSize || 1))}页
									</span>
									{hideCount > 0 ? renderTradeHideInfo() : ''}
									{reList.length > 0 ? <span className="r-ml-8">当前页共 {reList.length} 个包裹</span> : ''}
									{setting?.printSetExpandDTO?.showShopTradeCount && shopTradeCount && shopTradeCount.length > 0 && (
										<span className="r-ml-12" id="shop-order-count" style={ { verticalAlign: "top" } }>
											{
												shopTradeCount.map(item => renderCountItem(item))
											}
											<Popover
												content={ <ShopOrderCount /> }
												title="店铺订单数量"
												trigger="click"
												placement="left"
												destroyTooltipOnHide
											>
												<span id="show-all-shop" className="r-hide r-ml-12 r-c-primary r-pointer" style={ { display: "inline-block" } }>查看全部店铺</span>
											</Popover>
										</span>
									)}
								</div>
							</div>
						</>
					)}

					{/* 批量导入订单不展示智选快递 */}
					{smartExpressSwitch && !importExpress?.tradeExpressImportLogSequence ? (
						<div className="r-mb-8 r-flex">
							<div style={ { flexShrink: 0 } }>智选快递：</div>
							<div className="r-ai-c r-c-666">
								{defaultSmartExpressList.concat(uniqueSmartExpressList).map(item => (
									<span
										onClick={ () => { handleSearchSmartExpress(item); } }
										key={ item.id }
										className={ `${s["smart-express-company"]} ${item.id === scurrentSmartExpress ? s["active"] : ''}` }
									>{item.printTemplateName}
									</span>
								))}
							</div>
						</div>
					) : ''}

					<div id="printMode" className={ cs((templateListToggleStatus && !isMergePrint) ? s['templateToggleStatus'] : '', s['templateListCon']) }>
						<span className="r-fw-500" style={ { flexShrink: 0 } }>打印模式：</span>
						<span className="r-c-666">
							<Radio.Group value={ setting?.groupPrintSetJsonString?.openMergePrint } onChange={ handlePrintModeChange } >
								<Radio key={ 1 } value={ 1 } className={ cs(s["temp_item"]) }>单快递模板打印
								</Radio>
								<Radio key={ 2 } value={ 2 } className={ cs(s["temp_item"]) }>多平台聚合打印
								</Radio>
							</Radio.Group>

							<span
								className={ s.tip }
								style={ {
									position: 'relative',
									verticalAlign: 'middle',
									left: '-10px'
								} }
								onMouseEnter={ () => { handlePrintModeTips(true); } }
								onMouseLeave={ () => { handlePrintModeTips(false); } }
							>
								<Popover
									content={ <MergePrintTips /> }
									getPopupContainer={ (triggerNode) => { return triggerNode?.parentNode; } }
									visible={ mergeTipsVisible }
									placement={ isFixed ? 'bottom' : 'right' }
									trigger="hover"
									zIndex={ 101 }
									autoAdjustOverflow
								>
									<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
								</Popover>
							</span>
						</span>
					</div>
					<div className={ cs((templateListToggleStatus && !isMergePrint) ? s['templateToggleStatus'] : '', s['templateListCon']) }>
						<span className="r-fw-500" style={ { flexShrink: 0 } }>快递模板：</span>
						{
							(!kddTempList.length && !showAddGroup) ? renderLoading() : (
								<span className="r-c-666">
									{isMergePrint ? renderMergeKddTempList() : renderKddTempList()}
								</span>
							)
						}
					</div>

					{(!isFixed && setting?.groupPrintSetJsonString?.showFhdTempList === 2) && (
						<div className={ cs((templateListToggleStatus && !isMergePrint) ? s['templateToggleStatus'] : '', s['templateListCon']) }>
							<span className="r-fw-500" style={ { flexShrink: 0 } }>发货单模板：</span>
							{
								(!fhdTempList.length) ? renderLoading() : (
									<span className="r-c-666">
										{renderFhdTempList()}
									</span>
								)
							}
						</div>
					)}
				</div>
				<div>
					{renderNoMatchTradeList()}
				</div>
				{showTemplateListToggleBtn && isFixed ? (
					<Button
						className={ s["templateToggleWarpper"] }
						type="default"
						onClick={ () => {
							setTemplateListToggleStatus(prev => {
								local.set('batchPrint.toggleTemplateList', !prev);
								return !prev;
							});
						} }
					>
						<Icon type={ templateListToggleStatus ? 'zhankai' : 'shouqi' } size={ 14 } />
					</Button>
				) : ''}
			</Layout>
		);
	};

	const renderFastCom = () => {
		let rootDom = document.querySelector('#root');
		const toBottom = () => {
			if (isUseVirtualList) {
				event.emit('fastToBottom');
			} else {
				window.scroll(0, 9999999);
			}
		};
		if (rootDom.clientHeight > window.innerHeight) {
			return (
				<div className="fast-com">
					<div className="r-mb-8 r-pointer" onClick={ () => { window.scroll(0, 0); } } >
						<Icon style={ { fontSize: 30 } } type="daodingbu" />
					</div>
					<div className="r-pointer" onClick={ () => { toBottom(); } } >
						<Icon style={ { fontSize: 30 } } type="daodibu" />
					</div>
				</div>
			);
		}
		return '';
	};

	const canReachModal = () => {
		userStore.getUserInfo().then((userInfo) => {
			let canGo = true;

			if (userInfo.subUserId) {
				const authorityDetail = JSON.parse(userInfo?.authorityDetail || '[]');
				if (!authorityDetail.includes('38')) {
					canGo = false;
				}
			}
			let modalProps: ModalFuncProps = {
				title: '提示',
				content: (
					<div>快递判断数据由快递公司提供，根据使用经验准确率在95%左右，其中乡镇地址和开发区/新区地址请在快递网站上核对。也可自定义配置地址提高准确度</div>
				),
				okText: canGo ? '前往设置' : '取消',
			};
			if (canGo) {
				modalProps.onOk = () => {
					history.push('/settings/expressCanUp');
				};
				Modal.confirm(modalProps);
			} else {
				Modal.info(modalProps);
			}

		});
	};


	return (
		<div>
			<Layout className="kdzs-section">
				<SearchContainer isSearching={ isSearching } handleSearch={ handleSearch } initParam={ JSON.parse(indexParam?.param || '{}') } locationSearch={ locationSearch } />
			</Layout>

			<Layout className="kdzs-section">
				<SyncContainer />
			</Layout>

			{renderTemplateCom(false)}

			{isFixedTemplateCon && renderTemplateCom(true)}

			<Layout id="listCon" className="kdzs-section" style={ { paddingLeft: 0 } }>
				<div
					className={ s.tradeInfoContainer }
					style={ !reList.length ? {
						paddingBottom: 0,
					} : {} }
				>
					<div className="r-flex r-ai-c r-jc-fe r-mt-8 r-mb-8">
						<TradeSortCom />
						<ColSetCom />
					</div>
					{reList.length ? (
						<TradeList list={ reList } />
					) : renderNoList()}
					<Pagination
						style={ { marginTop: 16, marginBottom: 30, textAlign: 'right' } }
						showQuickJumper
						showSizeChanger
						pageSize={ searchParams?.pageSize || local.get('printBatch.pageSize') || 30 }
						current={ searchParams?.pageNo || 1 }
						total={ pageInfo?.total }
						onChange={ onChange }
						pageSizeOptions={ pageSizeOptions }
					/>
					{reList.length ? renderFastCom() : ''}
					{reList.length ? (
						<div className="r-flex r-jc-c r-ai-c">
							图标说明：
							<span className="mark mark-waybill-already" />已打印快递单
							<span className="mark mark-ship-already" />已打印发货单
							<span className="mark mark-waybill-partial" />部分订单已打印快递单
							<span className="mark mark-ship-partial" />部分订单已打印发货单
							<Icon type="gou" size={ 12 } style={ { color: 'rgb(102, 50, 184)' } } />已打印商品标签
							<Icon type="dayin" size={ 12 } style={ { color: 'rgb(102, 50, 184)' } } />部分订单已打印商品标签
							<Icon type="keda" size={ 12 } style={ { color: '#31B62E' } } />当前选择的快递公司可以到（<span className="r-click" onClick={ canReachModal }>提示</span>）
						</div>
					) : ''}
				</div>
			</Layout>

			<div className={ cs(reList.length ? '' : 'r-hide', s.batchPrintBottom) }>
				<BottomCom list={ reList } />
				<Pagination
					simple
					size="small"
					style={ { textAlign: 'right', position: "fixed", zIndex: "999", bottom: 20, right: 10 } }
					showSizeChanger
					pageSize={ searchParams?.pageSize || local.get('printBatch.pageSize') || 30 }
					current={ searchParams?.pageNo || 1 }
					total={ pageInfo?.total }
					onChange={ onChange }
					pageSizeOptions={ pageSizeOptions }
					className={ cs({ [s.showTransparentPagination]: setting?.printSetExpandDTO?.showTransparent }) }
				/>
			</div>

			{isShowHandOrderModal ? <HandOrderModal onOk={ addHandOrder } onEditOk={ editHandTrade } handData={ handData } /> : ''}

			{isShowBatchModifyMemoModal ? <BatchModifyMemoModal /> : ''}

			{isShowBatchModifyFlagModal ? <BatchModifyFlagModal /> : ''}

			{isShowBatchExportModal ? <BatchExportModal searchParams={ searchParams } list={ reList } /> : null}

			{isShowTradeOptLogModal && tradeStore.tradeOptLogPack ? <TradeOptLogModal /> : null}

			{isShowPrintStyleSetting ? (<PrintStyleSetting visible onCloseModal={ closePrintSetting } />) : ''}

			{/* 换商品弹框 */}
			{isShowChangeGoodsModal ? <ChangeGoodsModal /> : ""}
			{/* 批量换商品弹框 */}
			{isShowBatchChangeGoodsModal ? <BatchChangeGoodsModal /> : ""}
			{/* 批量添加赠品弹框 */}
			{isShowBatchAddGoodsModal ? <BatchAddGoodsModal /> : ""}
			{/* 批量操作结果弹框 */}
			{isBatchChangeOrAddGoodsResultModal ? <BatchResultModal /> : ""}

			<BatchModifySysMemoModal />

			<BatchModifyLocalMemoPicModal />

			<DealAbnormal />

			<ExReachProgressModal />

			<EditSidModal />

			<BatchSupplierSendModal />
			<BatchCopyTradeInfoModal />

			<SyncShopTempModal visible={ syncShopTempModalVisible } onCancel={ () => setSyncShopTempModalVisible(false) } />

			{/* 广告弹窗 */}
			{/* <div className="sys_position_replace" data-type="middlePopup" data-page="trade" />
			<div className="sys_position_replace rightBottomPopup" data-type="rightBottomPopup" data-page="trade" /> */}
		</div>
	);
});
export default TradePage;
