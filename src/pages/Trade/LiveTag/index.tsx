import React, {
	useEffect,
	useMemo,
	useState
} from "react";
import { useHistory } from "react-router-dom";
import { observer } from "mobx-react";
import { toJS } from "mobx";
import {
	Layout,
	Radio,
	Card,
	Button,
	Checkbox,
	Select,
	Form,
	Modal,
	InputNumber,
	Input,
	Tooltip,
	Space
} from "antd";
import { cloneDeep } from "lodash";
import Icon from "@/components/Icon";
import ShopMultiSelect from "@/components-biz/ShopListSelect/shopMultiSelect";
import { getMultiShops } from "@/components-biz/ShopListSelect/shopListUtils";
import AuthorMultiSelect from '@/components-biz/ShopListSelect/AuthorMultiSelect';
import InputSelect from "@/components/Input/InputSelect";
import {
	TradeLiveTagCreateTaskApi,
	TradeLiveTagLastTaskApi,
	TradeLiveTagTradeStatusCountApi,
	TradePrintSetUpdatePrintSetApi 
} from "@/apis/trade";
import { 
	TradeLivePrintBatchSaveLiveCommentApi,
	TradeLivePrintUpdateLiveCommentRuleApi,
	TradeLivePrintQueryCustomerBlackListApi
} from "@/apis/trade/live";
import memoFn from '@/libs/memorizeFn';
import { tradeStore } from "@/stores";
import PrintCenter from "@/print/index";
import LiveTypeTabs from './components/LiveTypeTabs';
import message from "@/components/message";
import { BQ_PRINT_TYPE } from "../constants";
import { ORDER_FILTER_OPTIONS, LIVE_TYPE_ENUM, liveTypeObj, ExCodeEnum } from "./constants";
import { EnumLabelTypeTabs } from '../LiveLog/constants';
import {
	TRADE_LABEL_OPTIONS,
	TRADE_EXCEPTION_OPTIONS
} from "@/pages/Trade/components/SearchContainer/constants";
import userStore from "@/stores/user";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import tradeSetStore from "@/stores/trade/tradeSet";
import styles from "./index.module.scss";
import LivePrintModal from './components/LivePrintModal';
import DanmuLivePrintModal from './components/DanmuLivePrintModal';
import FakerTradeSettingModal from './components/FakerTradeSettingModal';
import LiveLimitModal from './components/LiveLimitModal';
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import DyNickName from "@/assets/image/live/标签@2x.png";
import LowVersionControlModal from '@/components-biz/LowVersionControlModal';
import { PageNameControlEnum } from '@/components-biz/LowVersionControlModal/constant';
import { checkDanmuLivePrint } from './utils';

const initFormData = {
	liveTimeLength: 6,
	refundStatus: 0,
	liveType: 'shop',
	authorList: undefined,
	shopList: undefined,
	isOpenFakerTrade: false,
	isOpenBlackList: false,
	isOpenNewCustomer: false,
	isOpenRepeatTrade: false,
};

const LiveTag = observer(() => {
	const [showLivePrintModal, setShowLivePrintModal] = useState(false); // 显示直播打印弹框
	const [showLivePrintResultModal, setShowLivePrintResultModal] = useState(false); // 显示直播打印结果弹框
	const [form] = Form.useForm();
	const [modal, contextHolder] = Modal.useModal();
	const [formData, setFormData] = useState(initFormData); // 表单数据
	const [currentLiveInfo, setCurrentLiveInfo] = useState({} as any); // 当前直播信息
	const [isAgree, setIsAgree] = useState(false);	// 是否同意免责声明
	const [statusCount, setStatusCount] = useState({} as any); // 直播统计数据
	const [lastLive, setLastLive] = useState({} as any); // 上次直播信息
	const [liveType, setLiveType] = useState<'shop' | 'talent' | 'danmu'>('shop'); // 新增直播类型状态 
	const [isShowFakerTradeSettingModal, setIsShowFakerTradeSettingModal] = useState(false); // 显示虚拟订单规则设置弹框
	const [showLowVersionModal, setShowLowVersionModal] = useState(false); // 版本弹框
	const [showLiveLimitModal, setShowLiveLimitModal] = useState(false); // 直播功能限制弹框

	const history = useHistory();
	const {
		tradeAdvanceSetObj,
		setTradeAdvanceSetObj,
		setSetting,
		zbdXbqTempList,
		setZbdXbqTempList,
		selectedZbdXbqTemp,
		setSelectedZbdXbqTemp,
		scanPrintStore: {
			printersList,
			defaultPrinter,
			setDefaultPrinter,
		},
	} = tradeStore;

	const { shopTokenCache, userInfo } = userStore;
	const {
		level,
		whiteListSetting,
	} = userInfo;
	const { 
		dyNickName, // 抖音昵称
		liveStream, // 直播功能
	} = JSON.parse(whiteListSetting || '{}');

	// 当前登录账号是否正在直播
	const isLiving = useMemo(() => {
		return lastLive?.hasLivePrintTask && lastLive?.taskStatus === 1; // 执行状态0等待执行，1执行中，2成功，3失败4关闭
	}, [lastLive]);

	const getTempList = async() => {
		const data = await PrintCenter.getZbdTempList(BQ_PRINT_TYPE.直播标签);
		console.log(data.ModeListShows);
		setZbdXbqTempList(data.ModeListShows);
		if (data.ModeListShows[0]) {
			setSelectedZbdXbqTemp(data.ModeListShows[0]);
		}
		return data.ModeListShows;
	};

	useEffect(() => {
		console.log(defaultPrinter);
		form.setFieldsValue({
			selectedPrinter: defaultPrinter || printersList[0],
		});
	}, [printersList, defaultPrinter]);

	// 初始化高级设置
	useEffect(() => {
		const getAdvancedSet = async() => {
			let res = await memoFn.getAdvancedSet();
			if (res) {
				setTradeAdvanceSetObj(res);
			}
		};
		getAdvancedSet();
		tradeSetStore.getPrintContentSet();	
	}, []);

	const pageInit = async() => {
		const tempList = await getTempList();
		sendPoint(Pointer["直播打印-页面展现"]);
		TradeLiveTagLastTaskApi({ requestVersion: "v2" }).then((res) => {
			setLastLive(res || {});
			// 如果有直播打印任务,且执行中,则打开弹框
			if (res?.hasLivePrintTask && res?.taskStatus === 1) {
				setShowLivePrintModal(true);
				const { requestJson } = res;
				let initData = JSON.parse(requestJson);
				
				// 处理店铺信息 (店铺信息无法回显因为在直播中)
				// const plats = [...new Set(res.shopList.map(item => item.platform))]; // 去重获取所有平台
				// const plat_sellerIds = res.shopList.map(item => `${item.platform}_${item.sellerId}`); // 获取所有店铺ID
				// console.log('%c [ plats ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '111', plats, plat_sellerIds);

				initData.shopList = res.shopList.map(item => item.sellerId);
				initData.authorList = { authorList: [] };
				initData.liveTimeLength = res.liveTimeLength;
				
				// 处理达人信息,根据liveType判断
				if (res?.liveType == LIVE_TYPE_ENUM.达人直播) {
					setLiveType('talent');
					// 设置达人选择的值，需要转换为AuthorMultiSelect组件期望的格式
					initData.authorList = { authorList: res.authorList };
				} else if (res?.liveType == LIVE_TYPE_ENUM.店铺直播) {
					setLiveType('shop');
				} else if (res?.liveType == LIVE_TYPE_ENUM.弹幕扣号直播) {
					initData.shopList = { plats: ['fxg'], plat_sellerIds: [] };
					setLiveType('danmu');
				}
				
				// 处理打印模板
				const selectedZbdTempList = tempList.filter(item => item.Mode_ListShowId === initData.selectedZbdTemp);
				if (selectedZbdTempList[0]) {
					setSelectedZbdXbqTemp(selectedZbdTempList[0]);
				}
				
				// 设置表单值和数据
				form.setFieldsValue(initData);
				setFormData(initData);
				
				// 同时设置currentLiveInfo，确保弹框能正确显示直播信息
				setCurrentLiveInfo({
					...res,
					shopList: res.shopList || [],
					authorList: res.authorList || [],
					liveType: res.liveType, // 1=店铺直播，2=达人直播,3=弹幕扣号直播
					liveNo: res.liveNo, // 如果没有场次号，生成一个
				});
			} else {
				form.setFieldsValue(initFormData);
				setFormData(initFormData);
				setShowLivePrintModal(false);
				setShowLivePrintResultModal(false);
				setLiveType('shop'); // 重置为店铺直播
			}
		});
	};

	// 关闭自动打印弹框后执行
	const closeLivePrintAfter = async(data?: any) => {
		const lastLiveData = await TradeLiveTagLastTaskApi({
			requestVersion: "v2",
		});
		const lastLiveCount = await TradeLiveTagTradeStatusCountApi({
			liveTradePrintTaskId: lastLiveData.id,
			requestVersion: "v2",
		});

		// 弹幕直播，过滤订单数量从data中获取
		if (data && lastLiveData.liveType === 3) {
			lastLiveData.filterTradeNum = data.filterTradeNum;
		}
		setStatusCount(lastLiveCount);
		setLastLive(lastLiveData);
		setShowLivePrintResultModal(true);
		setShowLivePrintModal(false);
	};

	// 开启直播自动打印
	const openLivePrint = async() => {
		// 添加直播功能限制判断
		if (liveStream !== 1) {
			setShowLiveLimitModal(true);
			return;
		}
		
		if (!isAgree) {
			message.warn("请先勾选接受免责声明");
			return;
		}

		let installResult = await PrintCenter.checkLodopProgress(false);
		if (!installResult?.isSuccess) {
			console.log('请先安装并开启C-LODOP控件');
			return;
		}

		const lastTask = await TradeLiveTagLastTaskApi({
			requestVersion: "v2",
		});
		// 当前账号判断是否正在直播（多人登录同一账号）
		if (lastTask?.hasLivePrintTask && lastTask?.taskStatus === 1) {
			message.warn("当前已有直播打印任务在进行，请勿重复开启");
			return;
		}
		await form.validateFields();
		if (!selectedZbdXbqTemp || !selectedZbdXbqTemp.Mode_ListShowId) {
			message.warn("请选择模板，若无模板，请点击编辑模板进行新增");
			return;
		}
		const formValues = form.getFieldsValue();
		console.log('表单数据', formValues, formData);

		const { plats, plat_sellerIds } = formValues.shopList || {};
		let multiShops = await getMultiShops({ plats, plat_sellerIds });
		
		// 处理达人信息 - 新增逻辑
		let authorList = [];
		if (liveType === 'talent' && formValues.authorList?.authorList?.length > 0) {
			authorList = formValues?.authorList?.authorList?.map(author => ({
				platform: author.platform,
				authorId: author.authorId || author.value,
				authorName: author.authorName || author.label,
			}));
		}

		if (liveType === 'talent') {
			if (!authorList?.length) {
				message.warn("请选择达人信息");
				return;
			}
		}
		if (!plats?.length || !plat_sellerIds?.length) {
			message.warn("请选择店铺信息");
			return;
		}

		// 判断选择的店铺是否正在直播
		if (liveType === 'danmu') {
			if (plats?.some(plat => userStore.liveShopList?.some(shop => shop.platform === plat))) {
				message.warn("当前已有直播打印任务在进行，请勿重复开启");
				return;
			}
		}

		// 弹幕开启自动打印前校验是否是选择了扣号模板
		// if (liveType === 'danmu' && selectedZbdXbqTemp?.ExCode !== ExCodeEnum.扣号标签) {
		// 	message.warn("请选择扣号标签模板");
		// 	return;
		// }
		
		sendPoint(Pointer.开启直播自动打印);

		let params = {
			...formValues,
			liveType: liveTypeObj[liveType],
			userId: userInfo.userId,
			shopList: multiShops,
			authorList, // 新增达人信息
			selectedZbdTemp: selectedZbdXbqTemp.Mode_ListShowId,
			selectedPrinter: formValues.selectedPrinter,
			requestVersion: "v2",
		};

		if (liveType === 'danmu') {
			// 检查扣号直播抢单规则
			if (!checkDanmuLivePrint(formValues)) {
				return;
			}
			params.liveCommentRule = {
				itemType: formValues.itemType || 2, // 抢单类型1:普通商品2:尾货商品3:孤品
				timeRand: formValues.tailGoodsSeconds || 120, // 多少秒内打印
				maxPrintUserCount: formValues.tailGoodsCount || 10, // 打印前多少位扣号玩家
				numType: formValues.barrageFilter || 1, // 弹幕类型1全部打印（数字、小数、符号）2纯数字3数字+关键词
				keyword: formValues.keyword || '', // 关键词内容
				numRangeStart: formValues.numberRangeMin || 1, // 数字范围起始值
				numRangeEnd: formValues.numberRangeMax || 10000, // 数字范围结束值
				status: 1, // 状态1.有效2.无效
			};
			delete params.itemType;
			delete params.tailGoodsSeconds;
			delete params.tailGoodsCount;
			delete params.barrageFilter;
			delete params.keyword;
			delete params.numberRangeMin;
			delete params.numberRangeMax;
		} else {
			params.fakerTradeConfig = userStore.fakerTradeConfig || {}; // 虚拟订单配置
		}

		const data = await TradeLiveTagCreateTaskApi(params);

		console.log('%c [ 开启直播打印传参 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', data);
		// 透传给打印，包含完整的直播信息
		setCurrentLiveInfo({
			...data,
			shopList: multiShops,
			authorList,
			liveType: liveTypeObj[liveType],
			liveNo: data.liveNo, // 生成场次号
		});
		setShowLivePrintModal(true);
	};

	/**
	 * 打开打印中心模板服务
	 * @param type
	 */
	const handleOpenPrintMainPanel = () => {
		PrintCenter.showTemplateMain({ printType: "zbd" });
	};
	/**
	 * 处理快递模板选择
	 * @param e
	 */
	const handleSelectTempChange = (e: any) => {
		let temp = zbdXbqTempList.find(
			(i) => i.Mode_ListShowId === e.target.value
		);
		setSelectedZbdXbqTemp(temp);
	};

	// 跳转到直播订单明细页面并打开对应tab
	const jumpToDetail = (tabKey: string) => {
		const state = {
			liveTradePrintTaskIdList: lastLive.id,
			labelType: tabKey === "1" ? EnumLabelTypeTabs["已打印"] : EnumLabelTypeTabs["未打印-不符合"]
		};
		
		setShowLivePrintResultModal(false);

		if (lastLive.liveType === 3) {
			history.push({ pathname: '/takeNumberLiveLog', state });
		} else {
			history.push({ pathname: '/liveLog', state });
		}
		
	};

	// 打印模版
	const renderPrintComp = useMemo(() => {
		let _zbdXbqTempList = zbdXbqTempList || [];
		console.log('%c [ 打印模版 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '111', toJS(zbdXbqTempList));
		return (
			<Layout>
				<div className="r-flex r-bg-white">
					<div>
						<Form.Item
							required
							name="selectedPrinter"
							label="打印机"
						>
							<Select
								size="small"
								placeholder="请选择打印机"
								style={ { width: "200px" } }
								onChange={ (e) => { setDefaultPrinter(e); } }
							>
								{printersList?.map((printer) => (
									<Select.Option
										key={ printer }
										value={ printer }
									>
										{printer}
									</Select.Option>
								))}
							</Select>
						</Form.Item>
					</div>
					<div className="r-ml-10 r-mr-40 r-flex">
						<div style={ { width: "80px", flexShrink: 0 } }>标签模板：</div>
						<div className="r-c-666">
							<Radio.Group
								value={ selectedZbdXbqTemp?.Mode_ListShowId }
								onChange={ handleSelectTempChange }
							>
								{_zbdXbqTempList.map((i) => (
									<Radio
										key={ i.Mode_ListShowId }
										value={ i.Mode_ListShowId }
									>
										{i.ExcodeName}
									</Radio>
								))}
							</Radio.Group>
						</div>
						<div
							className=""
							style={ {
								cursor: "pointer",
								color: "#1890FF",
								minWidth: "60px",
							} }
							onClick={ () => handleOpenPrintMainPanel() }
						>
							编辑模板
						</div>
					</div>
				</div>
			</Layout>
		);
	}, [zbdXbqTempList, printersList, selectedZbdXbqTemp]);

	const onFormValueChange = (changedValues: {}, allValues) => {
		console.log(allValues);
		setFormData(allValues);
	};

	// 直播异常选项
	const TRADE_EXCEPTION_OPTIONS_1 = (useMemo(() => {
		const hasWaveManagePermission = userStore.hasWaveManagePermission;
		if (!hasWaveManagePermission) {
			TRADE_EXCEPTION_OPTIONS.forEach(op => {
				op.options.forEach(item => {
					item.arr = item.arr.filter(i => i.value !== '波次拣货异常');
				});
			});
		}
		return [...TRADE_EXCEPTION_OPTIONS];
	}, [userStore.hasWaveManagePermission]));

	// 直播类型切换处理
	const handleLiveTypeChange = (activeKey: string) => {
		if (activeKey) {
			setLiveType(activeKey as 'shop' | 'talent' | 'danmu');
			
			// 先重置字段，然后设置空值
			form.resetFields(['authorList', 'shopList']);
		
			// 延迟设置空值，确保重置生效
			let resetFormValue = {
				authorList: { authorList: [] },
				shopList: { plats: [], plat_sellerIds: [] },
			};
			// 弹幕扣号直播默认平台为抖店
			if (activeKey == 'danmu') {
				resetFormValue.shopList = { plats: ['fxg'], plat_sellerIds: [] };
			}
			setTimeout(() => {
				form.setFieldsValue(resetFormValue);
				
				// 同时重置 formData 状态
				setFormData(prev => ({
					...prev,
					...resetFormValue,
				}));
			}, 0);

			if (activeKey == 'talent') {
				userStore.getAuthorList(false);
			} else if (activeKey == 'shop' || activeKey == 'danmu') {
				userStore.getLiveShopList(true); // 获取正在直播中的店铺
			}
		}
	};

	// 处理抖音昵称配置变更
	const handleDyNickNameChange = async(e) => {
		const checked = e.target.checked;
		
		try {
			// 更新 tradeAdvanceSetObj
			const updatedConfig = {
				...tradeAdvanceSetObj,
				printSetExpandDTO: {
					...tradeAdvanceSetObj?.printSetExpandDTO,
					showDyNickName: checked ? 1 : 0
				}
			};
			
			// 保存到服务器
			let params:any = cloneDeep(updatedConfig);
			console.log('%c [ 高级设置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
			// 保持与高级设置组件一致
			params.hideTrade && delete params.hideTrade;
			if (params?.groupPrintSetJsonString) {
				params.groupPrintSetJsonString = JSON.stringify(params.groupPrintSetJsonString);
			}
			
			await TradePrintSetUpdatePrintSetApi({ ...params });
			
			// 更新本地状态
			setTradeAdvanceSetObj(updatedConfig);
			memoFn.updateAdvancedSet(updatedConfig);
			setSetting(updatedConfig);
		} catch (error) {
			console.error('保存抖音昵称配置失败:', error);
		}
	};

	// 过滤正在直播的达人ID
	const disabledAuthorIds = useMemo(() => {
		if (liveType == 'talent') {
			return userStore.authorList?.filter(item => item.liveStatus === 1)?.map(item => `${item.platform}_${item.authorId}`) || [];
		} else {
			return [];
		}
	}, [userStore.authorList, liveType]);

	// 根据选择的达人过滤可选的平台
	const filteredPlatformsForAuthors = useMemo(() => {
		if (liveType == 'talent') {
			if (!formData?.authorList?.authorList?.length) return [];
		
			const authorPlatforms = formData.authorList.authorList.map(author => author.platform);
			console.log('%c [ 可选的平台 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', [...new Set(authorPlatforms)]);
			return [...new Set(authorPlatforms)]; // 去重
		} else {
			return [];
		}
	}, [formData?.authorList?.authorList, liveType]);

	// 根据选择的平台和店铺过滤可选的达人平台
	const filteredPlatformsForShops = useMemo(() => {
		if (liveType == 'talent') {
			if (!formData?.shopList?.plats?.length && !formData?.shopList?.plat_sellerIds?.length) return [];
		
			const selectedPlatforms = formData.shopList.plats || [];
			const selectedShopIds = formData.shopList.plat_sellerIds || [];
		
			// 从店铺ID中提取平台信息
			const platformsFromShopIds = selectedShopIds.map(shopId => {
				const [platform] = shopId.split('_');
				return platform;
			});
		
			// 合并平台列表并去重
			const allPlatforms = [...new Set([...selectedPlatforms, ...platformsFromShopIds])];
			return allPlatforms;
		} else {
			return [];
		}
	}, [formData?.shopList?.plats, formData?.shopList?.plat_sellerIds, liveType]);

	// 达人直播不限制店铺
	const disabledShopIds = useMemo(() => {
		if (liveType == 'shop') {
			return userStore.liveShopList?.map(item => `${item.platform}_${item.sellerId}`) || [];
		} else {
			return [];
		}
	}, [userStore.liveShopList, liveType]);

	useEffect(() => {
		pageInit();
		userStore.getAuthorList(false);
		userStore.getLiveShopList(true);
		userStore.getFakerTradeConfig();
	}, []);

	return (
		<NormalLayout className={ styles['live-tag-warp'] }>
			<LiveTypeTabs
				activeKey={ liveType }
				onChange={ handleLiveTypeChange }
			/>

			<Form
				name="live-tag-search"
				size="small"
				form={ form }
				layout="inline"
				onValuesChange={ onFormValueChange }
			>
				<Layout>
					{
						['shop', 'talent'].includes(liveType) ? (
							<Card className={ styles['live-card'] } title="直播设置">
								{/* 达人直播时显示达人选择 */}
								{liveType === 'talent' && (
									<div className="r-flex r-ai-fs r-mb-16">
										<Form.Item
											name="authorList"
											label="选择达人"
											rules={ [
												{
													required: true, message: "请选择达人"
												},
												{
													validator(rule, value, callback) {
														if (value && !value?.authorList?.length) {
															return Promise.reject(new Error('请选择达人'));
														}
														return Promise.resolve();
													},
												}] }
										>
											<AuthorMultiSelect
												bgHighLight
												placeholder="请选择达人"
												dropdownStyle={ { maxHeight: '300px' } }
												dropdownOpenChange={ (open) => {
													// console.log('%c [ 达人选择器触发 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', open);
													if (open) {
														userStore.getAuthorList(true);
													}
												} }
												disabledAuthorIds={ disabledAuthorIds }
												filterPlatforms={ filteredPlatformsForShops } // 添加过滤选项，只显示对应平台的达人
											/>
										</Form.Item>
									</div>
								)}

								<div className="r-flex r-ai-fs">
									<div className="r-flex r-ai-fs">
										<Form.Item
											name="shopList"
											label="选择店铺"
											rules={ [
												{ required: true, message: "请选择店铺" },
												{
													validator(rule, value, callback) {
														if (value && !value?.plats?.length && !value?.plat_sellerIds?.length) {
															return Promise.reject(new Error('请选择店铺'));
														}
														return Promise.resolve();
													},
												}
											] }
										>
											<ShopMultiSelect 
												disabledShopIds={ disabledShopIds }
												disabledText="直播中"
												filterOptions={ liveType === 'talent' ? cloneDeep(filteredPlatformsForAuthors) : undefined } // 添加过滤选项，只显示对应平台的店铺
											/>
										</Form.Item>
									</div>
									<div className="r-ml-20 r-flex r-ai-fs">
										<Form.Item
											required
											label="直播时长:"
											name="liveTimeLength"
											rules={ [{ required: true, message: "请选择直播时长" }] }
										>
											<InputNumber
												max={ 12 }
												min={ 1 }
												precision={ 0 }
												style={ { width: "100px" } }
											/>
										</Form.Item>
										<div style={ { lineHeight: "24px" } }>小时，到达时间后关闭直播打印</div>
									</div>
								</div>
							</Card>
						) : (
							<Card className={ styles['live-card'] } title="直播设置">
								<div className="r-flex r-ai-fs r-mb-12">
									<Form.Item
										name="shopList"
										label="直播店铺"
										rules={ [
											{ required: true, message: "请选择店铺" },
											{
												validator(rule, value, callback) {
													if (value && !value?.plats?.length && !value?.plat_sellerIds?.length) {
														return Promise.reject(new Error('请选择店铺'));
													}
													return Promise.resolve();
												},
											}
										] }
									>
										<ShopMultiSelect
											bgHighLight={ false }
											chooseOne
											filterOptions={ ['fxg'] } // 添加过滤选项，只显示对应平台的店铺
											disabledPlatSelect
										/>
									</Form.Item>

									<Tooltip title="请在浏览器开启对应店铺的直播中控台页面" overlayStyle={ { maxWidth: '200px' } }>
										<span className="r-mt-2">
											<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
										</span>
									</Tooltip>
								</div>

								<div className="r-flex r-ai-fs r-mb-12">
									<Form.Item
										name="itemType"
										label="抢单监控"
										initialValue={ 2 }
										rules={ [
											{ required: true, message: "请选择抢单监控模式" }
										] }
									>
										<Radio.Group >
											<Space direction="vertical" size={ 10 }>
												<Radio value={ 1 }>
													<div className="r-flex r-ai-c">
														<div>普通商品：</div>
														<div className="r-ml-8">打印全部扣号的买家标签</div>
													</div>
												</Radio>
												<Radio value={ 2 }>
													<div className="r-flex r-ai-c">
														<div>尾货商品：</div>
														<div className="r-ml-8">
															<>
																<span className="k-ml-8">在</span>
																<Form.Item name="tailGoodsSeconds" noStyle initialValue={ 120 }>
																	<InputNumber
																		min={ 1 }
																		precision={ 0 }
																		style={ { width: 60, margin: '0 8px' } }
																	/>
																</Form.Item>
																<span>秒内，打印前</span>
																<Form.Item name="tailGoodsCount" noStyle initialValue={ 10 }>
																	<InputNumber
																		min={ 1 }
																		precision={ 0 }
																		style={ { width: 60, margin: '0 8px' } }
																	/>
																</Form.Item>
																<span>位扣号的买家标签</span>
															</>
														</div>
													</div>
												</Radio>
												<Radio value={ 3 }>
													<div className="r-flex r-ai-c">
														<div>孤品商品：</div>
														<div className="r-ml-8">
															<>
																<span className="k-ml-8">在</span>
																<Form.Item name="trialGoodsSeconds" noStyle initialValue={ 120 }>
																	<InputNumber
																		min={ 1 }
																		precision={ 0 }
																		style={ { width: 60, margin: '0 8px' } }
																	/>
																</Form.Item>
																<span>秒内，仅打印第一位扣号的买家标签</span>
															</>
														</div>
													</div>
												</Radio>
											</Space>
										</Radio.Group>
									</Form.Item>
								</div>

								<div className="r-flex r-ai-fs r-mb-12">
									<Form.Item
										name="barrageFilterWarp"
										label="弹幕匹配"
										initialValue={ 1 }
										rules={ [
											{ required: true }
										] }
										
									>
										<Radio value={ 1 } style={ { display: 'none' } }>1</Radio>
										
										<div className="r-flex r-fd-c">
											<div>直播弹幕扣号符合以下格式时自动打印标签</div>
											<Form.Item
												name="barrageFilter"
												label=""
												initialValue={ 1 }
												rules={ [
													{ required: true, message: "请选择弹幕匹配方式" }
												] }
												className="r-mt-12"
											>
												<Radio.Group>
													<Radio value={ 1 }>仅纯数字</Radio>
													<Radio value={ 2 } className="r-ml-8">全部打印（数字、小数、符号）</Radio>
													<Radio value={ 3 } className="r-ml-8">
														<div>
															<span>数字+关键词</span>
															<Form.Item name="keyword" noStyle>
																<Input 
																	placeholder="请输入关键词" 
																	style={ { width: 160, marginLeft: 8 } } 
																/>
															</Form.Item>
														</div>
													</Radio>
												</Radio.Group>
											</Form.Item>
										</div>
									</Form.Item>
								</div>

								<div className="r-flex r-ai-fs">
									<Form.Item
										name="numberRangeWarp"
										label="扣号范围"
										initialValue={ 1 }
										rules={ [
											{ required: true }
										] }
									>
										<Radio value={ 1 } style={ { display: 'none' } }>1</Radio>
										<div className="r-flex r-ai-c">
											<span className="r-mr-8">数字范围</span>
											<Form.Item
												name="numberRangeMin"
												initialValue={ 1 }
												rules={ [
													{ required: true, message: "请输入最小值" }
												] }
												noStyle
											>
												<InputNumber
													min={ 1 }
													precision={ 0 }
													style={ { width: 60 } }
												/>
											</Form.Item>
											<span>-</span>
											<Form.Item
												name="numberRangeMax"
												initialValue={ 10000 }
												rules={ [
													{ required: true, message: "请输入最大值" }
												] }
												noStyle
											>
												<InputNumber
													min={ 1 }
													precision={ 0 }
													style={ { width: 60 } }
												/>
											</Form.Item>
										</div>
									</Form.Item>
								</div>
							</Card>
						)
					}

					{
						['shop', 'talent'].includes(liveType) && (
							<Card className={ styles['live-card'] } title="订单设置">
								<div className="r-flex r-ai-c">
									<div style={ { width: 100, textAlign: 'right' } }>订单拉取规则：</div>
									<div>全部付款订单</div>
								</div>
							
								<div className="r-flex r-mt-10">
									<div style={ { width: 100, textAlign: 'right' } }>过滤指定订单：</div>
									<div className="r-flex">
										<Form.Item
											required
											name="refundStatus"
											style={ { width: "228px" } }
										>
											<Select
												options={
													ORDER_FILTER_OPTIONS
												}
												size="small"
											/>
										</Form.Item>
										<Form.Item
											name="tradeLabelList"
											style={ { width: "328px" } }
										>
											<InputSelect
												bgHighLight
												optionsList={
													[TRADE_LABEL_OPTIONS[0]]
												}
												size="small"
											/>
										</Form.Item>
										<Form.Item
											name="tradeExceptionList"
											style={ { width: "328px" } }
										>
											<InputSelect
												bgHighLight
												optionsList={
													[TRADE_EXCEPTION_OPTIONS_1[0]]
												}
												size="small"
											/>
										</Form.Item>
									</div>
								</div>
							
								<div className="r-flex r-mt-10">
									<div style={ { width: 100, textAlign: 'right' } }>虚拟订单：</div>
									<div className="r-flex r-ai-c">
										<Form.Item
											name="isOpenFakerTrade"
											valuePropName="checked"
										>
											<Checkbox>
												直播过程中生成虚拟订单
											</Checkbox>
										</Form.Item>
										<div
											className="r-pointer"
											style={ {
												color: "#1890FF",
												minWidth: "60px",
											} }
											onClick={ () => { setIsShowFakerTradeSettingModal(true); } }
										>
											规则设置
										</div>
									</div>
								</div>

								<div className="r-flex r-mt-10">
									<div style={ { width: 100, textAlign: 'right' } }>买家标识：</div>
									<div className="r-flex r-ai-c">
										<Form.Item
											name="isOpenBlackList"
											valuePropName="checked"
										>
											<Checkbox>
												打印黑名单标识
											</Checkbox>
										</Form.Item>

										<div
											className="r-pointer"
											style={ {
												color: "#1890FF",
												minWidth: "60px",
											} }
											onClick={ () => { history.push('/settings/customer'); } }
										>
											黑名单设置
											<Tooltip title="说明：订单打印页面使用「标记黑名单」功能进行黑名单用户添加" >
												<span className="r-ml-4">
													<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
												</span>
											</Tooltip>
										</div>

										<Form.Item
											name="isOpenNewCustomer"
											className="r-ml-20"
											valuePropName="checked"
										>
											<Checkbox>
												打印新人首单标识
											</Checkbox>
										</Form.Item>

										<Form.Item
											name="isOpenRepeatTrade"
											valuePropName="checked"
											className="r-ml-4"
										>
											<Checkbox>
												买家重复下单标识
											</Checkbox>
										</Form.Item>
									</div>
								</div>
							</Card>
						)
					}

					<Card
						className={ styles['live-card'] }
						title="打印设置"
					>
						{
							liveType === 'danmu' ? (
								<div className="r-flex r-ai-c r-mb-10">
									<div>买家标识:</div>
									<div className="r-flex r-ai-c r-ml-12">
										<Form.Item
											name="isOpenBlackList"
											valuePropName="checked"
										>
											<Checkbox>
												打印黑名单标识
											</Checkbox>
										</Form.Item>

										<div
											className="r-pointer"
											style={ {
												color: "#1890FF",
												minWidth: "60px",
											} }
											onClick={ () => { history.push('/settings/customer'); } }
										>
											黑名单设置
										</div>
									</div>
								</div>
							) : (
								<div className="r-flex r-ai-c r-mb-10">
									<div>抖音昵称:</div>
									<Checkbox 
										className="r-ml-10"
										disabled={ dyNickName != 1 && level == 2 }
										checked={ dyNickName == 1 ? tradeAdvanceSetObj?.printSetExpandDTO?.showDyNickName : false }
										onChange={ handleDyNickNameChange }
										onClick={ () => {
											if (level != 2 && dyNickName != 1) {
												setShowLowVersionModal(true);
											}
										} }
									>
										开启抖音昵称打印
									</Checkbox>
									<img src={ DyNickName } alt="" className="r-ml-12" style={ { width: '92px', height: '24px' } } />
								</div>
							)
						}
							
						{/* 打印机和模板设置 */}
						{renderPrintComp}
					</Card>

					<Card className={ styles['live-card'] } title="免责声明">
						<div>
							<div
								className="r-pd-10 r-fs-12"
								style={ {
									border: "1px solid #ddd",
								} }
							>
								<p>
									开启直播自动打印功能表示您已同意并接受以下事实：
								</p>
								<p>
									1.您知晓“直播打印”功能为商家打印发货的一项辅助功能，会受到服务器稳定性、接口波动、电脑关机、浏览器关闭等因素影响，可能存在部分订单打印失败或发货失败的风险，无法完全替代手动打印及相关确认操作。
								</p>
								<p>
									2.您应当在“直播打印”进程结束后及时核对订单打印和发货的情况，【快递助手ERP】不承担因中断后您未及时确认订单打印和发货状态而导致的损失。
								</p>
							</div>
							<div className="r-mt-16">
								<Checkbox
									value={ isAgree }
									onChange={ (e) => setIsAgree(
										e.target.checked
									) }
								>

									<div style={ { userSelect: "none" } }>
										我已阅读并接受
										<span className="r-c-warning r-ml-8">开启直播自动打印后将按照上方设置规则自动打印符合条件的订单</span>
									</div>
								</Checkbox>
							</div>
							<div className="r-flex r-ai-c r-jc-c r-mt-10 r-pb-10">
								<Button
									type="primary"
									size="middle"
									onClick={ openLivePrint }
								>
									开启直播自动打印
								</Button>

							</div>
						</div>
					</Card>
				</Layout>
			</Form>

			{/* 店铺和达人自动打印弹框 */}
			{
				[1, 2].includes(currentLiveInfo.liveType) && showLivePrintModal && (
					<LivePrintModal
						currentLiveInfo={ currentLiveInfo }
						onClose={ closeLivePrintAfter }
						isLiving={ isLiving }
						formData={ formData }
						form={ form }
					/>
				)
			}

			{/* 弹幕扣号自动打印弹框 */}
			{
				currentLiveInfo.liveType === 3 && showLivePrintModal && (
					<DanmuLivePrintModal
						currentLiveInfo={ currentLiveInfo }
						onClose={ closeLivePrintAfter }
						isLiving={ isLiving }
						formData={ formData }
						form={ form }
					/>
				)
			}
			

			{/* 功能限制弹框 */}
			<LiveLimitModal
				visible={ showLiveLimitModal }
				onClose={ () => setShowLiveLimitModal(false) }
			/>

			{/* 自动打印结果弹框 */}
			<Modal
				centered
				title="自动打印"
				visible={ showLivePrintResultModal }
				width="800px"
				footer={ null }
				destroyOnClose
				maskClosable={ false }
				onCancel={ () => { setShowLivePrintResultModal(false); } }
			>
				<div className="r-ta-c r-fs-20 r-c-warning">
					本次自动打印结束
				</div>
				<div className="r-flex r-jc-sb r-mt-20">
					<div>
						自动打印开始时间：
						{lastLive.livePrintTimeStart}
					</div>
					<div>
						结束时间：
						{lastLive.livePrintTimeActualEnd}
					</div>
					<div>
						共计：{lastLive.liveTimeStr}
					</div>
				</div>
				<div
					className="r-flex r-jc-c r-mt-20 r-ta-c r-pt-20 r-pl-40 r-pr-40"
					style={ { borderTop: "1px solid #eee" } }
				>
					<div style={ { padding: "0 50px" } }>
						<div className="r-c-success r-fs-20 r-pb-10 r-pointer" onClick={ () => jumpToDetail("1") }>
							{statusCount.alreadyPrintCount}
						</div>
						<div>标签已打印数量</div>
					</div>
					<div style={ { padding: "0 50px" } }>
						<div className="r-c-error r-fs-20 r-pb-10 r-pointer" onClick={ () => jumpToDetail("2") }>
							{statusCount.filterTradeNum}
						</div>
						<div>过滤订单数量</div>
					</div>
				</div>
				<div className="r-ta-r r-mt-30 r-pt-20" style={ { borderTop: "1px solid #eee" } }>
					<Button onClick={ () => { setShowLivePrintResultModal(false); } }>
						关闭
					</Button>
				</div>
			</Modal>

			{/* 虚拟订单规则设置弹框 */}
			{
				isShowFakerTradeSettingModal && (
					<FakerTradeSettingModal 
						visible={ isShowFakerTradeSettingModal }
						onClose={ () => { setIsShowFakerTradeSettingModal(false); } }
					/>		
				)
			}			

			{/* 低版本控制弹窗 */}
			{showLowVersionModal && (
				<LowVersionControlModal
					closable
					centered
					pageName={ PageNameControlEnum.抖音昵称打印 }
					onCancel={ () => {
						setShowLowVersionModal(false);
					} }
				/>
			)}
			
			{contextHolder}
		</NormalLayout>
	);
});

export default LiveTag;
