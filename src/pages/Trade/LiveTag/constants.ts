
export enum ORDER_FILTER_ENUM {
    不过滤退款订单 = 0,
    过滤退款完成订单 = 1,
    过滤退款中或退款完成订单 = 2,
}

export const ORDER_FILTER_OPTIONS = [
	{
		label: '不过滤退款订单',
		value: 0
	},
	{
		label: '过滤退款完成订单',
		value: 1
	},
	{
		label: '过滤退款中或退款完成订单',
		value: 2
	}
];

export enum LIVE_TYPE_ENUM {
	店铺直播 = 1,
	达人直播 = 2,
	弹幕扣号直播 = 3,
}

export const liveTypeObj = {
	shop: 1,
	talent: 2,
	danmu: 3,
};


export enum ExCodeEnum {
	直播标签='ZBD',
	直播小票='ZBXP',
	扣号标签='KHBQ',
}
