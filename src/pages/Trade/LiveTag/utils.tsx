import message from "@/components/message";


// 检查扣号直播抢单规则
export const checkDanmuLivePrint = (formValues: any) => {
	if (!formValues.tailGoodsSeconds) {
		message.warn("请配置多少秒内打印");
		return false;
	}
  
	if (formValues.itemType == 2 && !formValues.tailGoodsCount) {
		message.warn("请配置打印前多少位扣号玩家");
		return false;
	}
	if (formValues.barrageFilter == 3 && !formValues.keyword) {
		message.warn("请配置关键词内容");
		return false;
	}

	if (!formValues.numberRangeMin) {
		message.warn("请配置数字范围起始值");
		return false;
	}
	if (!formValues.numberRangeMax) {
		message.warn("请配置数字范围结束值");
		return false;
	}
	if (formValues.numberRangeMin >= formValues.numberRangeMax) {
		message.warn("数字范围填写错误");
		return false;
	}
	return true;
};