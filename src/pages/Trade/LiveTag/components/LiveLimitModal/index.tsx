import React from 'react';
import { Modal } from 'antd';
import Icon from '@/components/Icon';
import styles from './index.module.scss';

interface LiveLimitModalProps {
  visible: boolean;
  onClose: () => void;
}

const LiveLimitModal: React.FC<LiveLimitModalProps> = ({
	visible,
	onClose,
}) => {
	return (
		<Modal
			centered
			title="功能限制"
			visible={ visible }
			onCancel={ onClose }
			footer={ null }
			width={ 520 }
			closable
			maskClosable={ false }
			className={ styles.liveLimitModal }
			destroyOnClose
		>
			<div className={ styles.content }>
				<div className={ styles.mainMessage }>
					直播功能需付费使用
				</div>
        
				<div className={ styles.contactSection }>
					<div className={ styles.contactIcon }>
						<Icon type="lianxikefu" size={ 16 } style={ { color: 'rgba(0, 0, 0, 0.65)' } } />
					</div>
					<div className={ styles.contactInfo }>
						<div className={ styles.contactTitle }>联系销售</div>
						<div className={ styles.contactDesc }>
							如果使用请联系销售开通
						</div>
					</div>
				</div>
			</div>
		</Modal>
	);
};

export default LiveLimitModal;
