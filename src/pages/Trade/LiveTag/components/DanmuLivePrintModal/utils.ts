import dayjs from "dayjs";
import { getToken } from "@/utils/token";

export const getMockMessageData = () => {
	const time = dayjs().format('YYYY-MM-DD HH:mm:ss');
    
	// 生成随机长度（1-3）
	const length = Math.floor(Math.random() * 3) + 1;
    
	// 生成随机评论内容（数字1-100）
	const generateRandomComment = () => {
		const randomNum = Math.floor(Math.random() * 3) + 1;
		// 随机添加一些前缀或后缀
		const prefixes = ['', '', '我要', '买'];
		const suffixes = ['', '', '号', '个'];
        
		const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
		const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
        
		// return `${prefix}${randomNum}${suffix}`;
		return `${randomNum}`; // 纯数字
	};
    
	// 生成随机UID
	const generateRandomUid = () => {
		return 'user' + Math.floor(Math.random() * 1000000);
	};
    
	const data = Array.from(
		{ length },
		(_, index) => { 
			return {
				buyerNick: `用户昵称${index + 1}`,
				comment: generateRandomComment(),
				commentTag: null,
				uid: generateRandomUid(),
				createTime: time,
			}; 
		}
	);
    
	(window as any).handleLiveDanmuMessage({
		type: 1, 
		shopName: "小欣欣服饰的小店", 
		tabId: 12345, 
		commentList: data,
		content: '',
	});
};

export const fetchKeepAlive = (id: string) => {
	const closeData = {
		liveTradePrintTaskId: id,
		requestVersion: "v2"
	};

	// 获取 token
	const token = getToken() || '';
					
	// 使用 fetch 发送请求，支持自定义 headers
	fetch('/api/trade/livePrint/closeTask', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'qnquerystring': token,
			'erp-version': '1',
			'startDate': new Date().valueOf().toString()
		},
		body: JSON.stringify(closeData),
		keepalive: true // 确保请求在页面卸载时也能发送
	}).catch(() => {
		// 忽略错误，因为页面可能正在卸载
	});
};