export enum MessageType {
	DANMU = 1,
	PLUGIN = 2,
	OTHER = 3,
	ERROR = -1,
}

export interface SendMessageData {
    type?: MessageType;
    shopName?: string;
    commentList?: CommentsInfo[];
    content?: string; // 当type不为1时才有内容
    tabId?: number; // 浏览器tabId
    activeTabs?: number; // 活跃tabId
    code?: number; // 是否有打开的直播后台
}

export interface CommentsInfo {
    buyerNick: string;
    comment: string;
    commentTag: any;
    uid: string;
    createTime: string;
}