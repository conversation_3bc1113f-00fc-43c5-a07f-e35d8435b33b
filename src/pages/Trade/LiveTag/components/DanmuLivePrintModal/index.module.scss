.live-print-modal-wrap {
    top: 100px !important;
    z-index: 998;

    :global {
        .ant-modal-body {
            padding: 16px 20px 16px 24px;
        }

        .ant-table-body {
            overflow-y: auto !important;
            // min-height: 540px;

            .ant-table-row {
                td {
                    border-right: 1px solid #f0f0f0;
                }

                td:last-child {
                    border-right: none;
                }
            }

            .ant-table-row:last-child {
                td {
                    border-bottom: 1px solid #f0f0f0;
                }
            }

        }

        .innerTableChange {
            padding: 0px;
        }
    }

    .tip {
        color: #FF4D4F;
        font-size: 14px;
        line-height: 22px;
    }

    .content {
        display: flex;
        justify-content: space-between;

        .left {
            width: 768px;

            .table {
                width: 100%;
                min-height: 580px;
                border: 1px solid #f0f0f0;
            }

            .info {
                height: 164px;
                width: 100%;
                border: 1px solid rgba(0, 0, 0, 0.06);
                border-radius: 2px;
                padding: 16px;
                padding-bottom: 12px;
                box-sizing: border-box;

                .infoScroll {
                    width: 100%;
                    height: 100%;
                    overflow-y: auto;
                }

                .infoItem {
                    word-break: break-all;
                    line-height: 22px;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.85);
                    margin-bottom: 4px;

                    .infoItemComment {
                        color: rgba(0, 0, 0, 0.85);
                    }
                }
            }
        }

        .right {
            width: 368px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            gap: 16px;
            max-height: 740px;
            overflow-y: auto;
            overflow-x: hidden;

            .shopInfo {
                width: 100%;
                height: 114px;
                border-radius: 2px;
                display: flex;
                flex-direction: column;
                padding: 16px;
                gap: 8px;
                background: #F5F5F5;

                .content-item {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                }

                .content-item-title {
                    font-size: 14px;
                    line-height: 22px;
                    color: rgba(0, 0, 0, 0.85);
                }

                .content-item-content {
                    font-size: 14px;
                    line-height: 22px;
                    color: rgba(0, 0, 0, 0.85);
                    display: flex;
                    align-items: center;
                }

                .openLiveCenter {
                    font-size: 14px;
                    line-height: 22px;
                    color: #1890FF;
                    cursor: pointer;
                    margin-left: 72px;
                }
            }

            .timeInfo {
                .titleIcon {
                    width: 2px;
                    height: 16px;
                    background: #FD8204;
                }

                .titleText {
                    font-size: 16px;
                    font-weight: 500;
                    line-height: 24px;
                    color: rgba(0, 0, 0, 0.85);
                    margin-left: 8px;
                }

                .settingsForm {
                    background: #fff;
                    border: 1px solid #E8E8E8;
                    border-radius: 4px;
                    padding: 16px;
                    padding-right: 10px;

                    :global {
                        .ant-form-item {
                            margin-bottom: 16px;
                        }

                        .ant-form-item-label {
                            padding-bottom: 4px;
                        }

                        .ant-form-item-label>label {
                            font-size: 14px;
                            font-weight: 500;
                            color: rgba(0, 0, 0, 0.85);
                        }
                    }
                }

                .countdownArea {
                    width: 316px;
                    height: 168px;
                    border-radius: 2px;
                    background: #475569;
                    box-sizing: border-box;
                    padding: 8px;

                    .countdownAreaScroll {
                        width: 100%;
                        height: 100%;
                        overflow-y: auto;
                        display: flex;
                        flex-direction: column;
                        gap: 4px;

                        &::-webkit-scrollbar {
                            width: 6px;
                        }

                        &::-webkit-scrollbar-track {
                            background: transparent;
                        }

                        &::-webkit-scrollbar-thumb {
                            background: rgba(255, 255, 255, 0.3);
                            border-radius: 3px;
                        }

                        &::-webkit-scrollbar-thumb:hover {
                            background: rgba(255, 255, 255, 0.5);
                        }
                    }

                    .countdownAreaItem {
                        background: #CBD5E1;
                        width: 100%;
                        height: 48px;
                        display: flex;
                    }

                    .countdownAreaItemNumber {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: flex-end;
                        padding: 3px;
                        background: #CBD5E1;
                        flex: 1;

                        .number {
                            font-size: 34px;
                            font-weight: 600;
                            line-height: 40px;
                            color: #62728A;
                            overflow-x: auto;
                            overflow-y: hidden;
                            white-space: nowrap;
                            letter-spacing: 0px;

                            // 自定义滚动条样式
                            &::-webkit-scrollbar {
                                height: 4px; // 滚动条高度
                            }

                            &::-webkit-scrollbar-track {
                                background: transparent; // 滚动条轨道背景
                            }

                            &::-webkit-scrollbar-thumb {
                                background: rgba(0, 0, 0, 0.2); // 滚动条滑块颜色
                                border-radius: 2px; // 滚动条滑块圆角
                            }

                            &::-webkit-scrollbar-thumb:hover {
                                background: rgba(0, 0, 0, 0.3); // 鼠标悬停时滑块颜色
                            }
                        }

                        .text {
                            font-size: 14px;
                            font-weight: 600;
                            line-height: 22px;
                            color: #62728A;
                            flex-shrink: 0;
                        }

                        &.numberActive {
                            background: #BBF7D0;

                            .number {
                                color: #FF0000;
                            }

                            .text {
                                color: #FF0000;
                            }
                        }

                        &.countActive {
                            background: #FEF08A;

                            .number {
                                color: rgba(0, 0, 0, 0.85);
                            }

                            .text {
                                color: rgba(0, 0, 0, 0.85);
                            }
                        }

                        &.timeActive {
                            background: #A5F3FC;

                            .number {
                                color: #A11CAF;
                            }

                            .text {
                                color: #A11CAF;
                            }
                        }
                    }
                }
            }

            .printInfo {
                width: 100%;
                min-height: 182px;
                border-radius: 2px;
                border: 1px solid rgba(0, 0, 0, 0.06);
                display: flex;
                flex-direction: column;
                padding: 16px;
                gap: 16px;
                box-sizing: border-box;
                margin-top: 16px;

                .printInfoCount {
                    height: 30px;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.85);


                    .labelNum {
                        font-size: 20px;
                        font-weight: 600;
                        color: #52C41A;
                    }

                    .waitPrintNum {
                        font-size: 20px;
                        font-weight: 600;
                        color: #FD8204;
                    }
                }

                .printInfoContent {
                    width: 100%;
                }

                .reset {
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 22px;
                    color: #1890FF;
                    cursor: pointer;
                }

            }
        }
    }

}

.live-print-modal-wrap-living {
    top: 100px !important;
    z-index: 998;

    .living-print-modal-content {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .living-print-modal-content-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .living-print-modal-content-item-title {
            font-size: 14px;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.85);
        }

        .living-print-modal-content-item-content {
            font-size: 14px;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.85);
            display: flex;
            align-items: center;
        }

        .autoTip {
            color: #52C41A;
            font-size: 14px;
            line-height: 22px;
            margin-left: 16px;
        }
    }

    :global {}
}