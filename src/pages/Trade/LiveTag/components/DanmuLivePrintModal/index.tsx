import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { Modal, Button, Empty, Form, Select, Radio, InputNumber, Input, Space } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import cs from 'classnames';
import dayjs from 'dayjs';
import { tradeStore } from "@/stores";
import BaseTable from '@/components/SearchTable/BaseTable';
import styles from './index.module.scss';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import userStore from "@/stores/user";
import { getAllShopMap } from '@/components-biz/ShopListSelect/shopListUtils';
import message from '@/components/message';
import PrintCenter from "@/print/index";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { BQ_PRINT_TYPE } from "../../../constants";
import {
	TradeLiveTagCloseTaskApi,
	TradeLiveTagTradeStatusCountApi 
} from "@/apis/trade";
import { TradePrintEmptyPrintNumApi } from "@/apis/report/kddLog";
import {
	TradeLivePrintBatchSaveLiveCommentApi,
	TradeLivePrintUpdateLiveCommentRuleApi,
	TradeLivePrintQueryCustomerBlackListApi
} from '@/apis/trade/live';
import { TradeLivePrintUpdateLiveCommentRuleRequest } from '@/types/trade/index/live';
import { checkDanmuLivePrint } from '../../utils'; // 导入校验函数
import useGetState from '@/utils/hooks/useGetState';
import { getMockMessageData } from './utils';

interface DanmuLivePrintModalProps {
  currentLiveInfo: any;
  onClose: (data:any) => void;
  isLiving?: boolean;
  formData?: any;
  form?: any;
}

enum MessageType {
	DANMU = 1,
	PLUGIN = 2,
	OTHER = 3,
	ERROR = -1,
}

interface SendMessageData {
	type?: MessageType;
	shopName?: string;
	commentList?: CommentsInfo[];
	content?: string; // 当type不为1时才有内容
	tabId?: number; // 浏览器tabId
	activeTabs?: number; // 活跃tabId
	code?: number; // 是否有打开的直播后台
}

interface CommentsInfo {
	buyerNick: string;
	comment: string;
	commentTag: any;
	uid: string;
	createTime: string;
}


let timer = null; // 获取数量定时器
let liveTimer = null; // 处理直播数据定时器
let baseTableRefTimer = null;
let isManuallyClosed = false;
const keepHistoryData = false; // 是否保存倒计时轮次数据
const defaultInfoList = [{ createTime: dayjs().format('HH:mm:ss'), comment: '#系统提示：欢迎使用弹幕扣号直播，请开启店铺直播中控台！', color: 'rgba(0, 0, 0, 0.85)' }];

// 扣号弹幕直播自动打印弹框
const DanmuLivePrintModal: React.FC<DanmuLivePrintModalProps> = ({
	currentLiveInfo,
	onClose,
	isLiving = false,
	formData,
	form,
}) => {
	const [shopMap, setShopMap] = useState<any>({}); // 店铺信息映射
	const [statusCount, setStatusCount] = useState({} as any);
	const [liveList, setLiveList, getLiveList] = useGetState([]); // 表格数据
	const [infoList, setInfoList, getInfoList] = useGetState([...defaultInfoList]); // 信息列表数据
	const [liveCountdownList, setLiveCountdownList, getLiveCountdownList] = useGetState([]); // 倒计时列表数据
	const [roundsList, setRoundsList, getRoundsList] = useGetState([]); // 轮次数据
	const [blackList, setBlackList, getBlackList] = useGetState([]); // 黑名单列表数据
	const [settingsForm] = Form.useForm(); // 新增设置表单
	const [formInfo, setFormInfo] = useState(formData); // 表单数据
	const [lastPrintNo, setLastPrintNo] = useState(0); // 打印序号最新
	const [filterTradeNum, setFilterTradeNum] = useState(0); // 过滤订单数量前端统计
	const [waitPrintCount, setWaitPrintCount] = useState(0); // 等待打印数量前端统计
	const [printTagCount, setPrintTagCount] = useState(0); // 标签打印数量前端统计
	const [allRule, setAllRule, getAllRule] = useGetState([]); // 所有规则

	const containerRef = useRef(null);
	const lastItemRef = useRef(null);
	const infoScrollRef = useRef<HTMLDivElement>(null);
	const baseTableRef = useRef<any>(null);
	const countdownScrollRef = useRef<HTMLDivElement>(null);
	const {
		tradeAdvanceSetObj,
		setTradeAdvanceSetObj,
		setSetting,
		zbdXbqTempList,
		setZbdXbqTempList,
		selectedZbdXbqTemp,
		setSelectedZbdXbqTemp,
		scanPrintStore: {
			printersList,
			defaultPrinter,
			setDefaultPrinter,
		},
	} = tradeStore;
	const { shopTokenCache, userInfo } = userStore;
	const {
		level,
		whiteListSetting,
	} = userInfo;
	const { 
		dyNickName, // 抖音昵称
		liveStream, // 直播功能
	} = JSON.parse(whiteListSetting || '{}');

	const { requestJson = '{}' } = currentLiveInfo;
	const params = JSON.parse(requestJson);

	// 获取表单初始值
	const getInitialValues = useMemo(() => {
		const params = JSON.parse(currentLiveInfo.requestJson || '{}');
		const liveCommentRule = params?.liveCommentRuleList?.[0] || {}; // 这里是个数组？
		if (liveCommentRule) {
			return {
				itemType: liveCommentRule.itemType || 2,
				timeRand: liveCommentRule.timeRand || 120,
				maxPrintUserCount: liveCommentRule.maxPrintUserCount || 10,
				numType: liveCommentRule.numType || 1,
				keyword: liveCommentRule.keyword || '',
				numRangeStart: liveCommentRule.numRangeStart || 1,
				numRangeEnd: liveCommentRule.numRangeEnd || 10000,
				ruleID: liveCommentRule.ruleID || '',
			};
		}
		return {
			itemType: 2,
			timeRand: 120,
			maxPrintUserCount: 10,
			numType: 1,
			keyword: '',
			numRangeStart: 1,
			numRangeEnd: 10000,
			ruleID: '',
		};
	}, [currentLiveInfo.requestJson]);

	const shopInfo = useMemo(() => {
		return currentLiveInfo.shopList?.map((item:any) => {
			return {
				...item,
				shopName: shopMap[`${item.platform.toLowerCase()}_${item.sellerId}`]?.sellerNick || item.sellerId,
			};
		})?.[0] || {}; // 目前只有一个店铺
	}, [currentLiveInfo.shopList, shopMap]);

	// console.log('%c [ 111 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', currentLiveInfo);

	// 获取店铺信息映射
	const fetchShopMap = async() => {
		try {
			const map = await getAllShopMap({ hasHand: false });
			setShopMap(map);
		} catch (error) {
			console.error('获取店铺信息失败:', error);
		}
	};

	// 定义订单列表的表格列
	const liveOrderColumns = [
		{
			title: '序号',
			dataIndex: 'index',
			width: 50,
			render: (text: string, record: any, index: number) => {
				return <>{index + 1}</>;
			}
		},
		{
			title: '扣号时间',
			dataIndex: 'createTime',
			width: 140,
			render: (text: string, record: any) => {
				return dayjs(record.createTime).format('HH:mm:ss');
			},
		},
		{
			title: '买家昵称',
			dataIndex: 'buyerNick',
			width: 140,
			render: (text: string) => text || '-',
		},
		{
			title: '扣号内容',
			dataIndex: 'comment',
			width: 180,
			render: (text: string) => text || '-',
		},
		{
			title: '匹配情况',
			dataIndex: 'commentStatus',
			width: 200,
			render: (text: string, record: any) => {
				let { commentNumber, position, remainingTime, isAllowPrint, isOutNumber, rule } = record;
				const originalTimeRand = rule?.timeRand || 120; // 使用创建时的配置

				if (isAllowPrint) {
					return (
						<div>
							扣号：【{commentNumber}】，为第【{position}】位，此数字剩余倒计时：【{originalTimeRand}】秒
						</div>
					);
				} else if (isOutNumber) {
					return (
						<div>
							扣号：【{commentNumber}】，为第【{position}】位，超数量不打印
						</div>
					);
				} else {
					return null;
				}
			},
		}
	];

	// 清楚获取数量定时器
	const clearCountTimer = () => {
		if (timer) {
			clearInterval(timer);
			timer = null;
		}
	};

	const closeLivePrintAfter = () => {
		onClose?.({
			filterTradeNum,
		});
	};

	const closeLive = async() => {
		await TradeLiveTagCloseTaskApi({
			liveTradePrintTaskId: currentLiveInfo.id,
			requestVersion: "v2",
		});
	};

	// 关闭直播打印任务
	const closeLivePrint = async() => {
		sendPoint(Pointer.关闭自动打印);
		isManuallyClosed = true; // 标记为手动关闭
		await closeLive();
		
		closeLivePrintAfter();
	};

	// 关闭前二次确认
	const closeConfirm = () => {
		Modal.confirm({
			icon: <ExclamationCircleOutlined />,
			content: <div style={ { overflow: "hidden" } }>当前店铺存在自动打印进程，关闭该页面将导致自动打印中断</div>,
			okText: '确定关闭',
			centered: true,
			onOk() {
				closeLivePrint();
			},
			onCancel() {
			},
		});
	};

	// 更新统计数据
	const updateStatusCount = () => {
		TradeLiveTagTradeStatusCountApi({
			liveTradePrintTaskId: currentLiveInfo.id,
			requestVersion: "v2",
		}).then((res) => {
			// 如果任务状态不是执行中，则关闭弹框
			if (res.taskStatus !== 1) {
				console.log('%c [ 直播任务关闭状态 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
				mess
				closeLivePrintAfter();
			}
			setStatusCount(res);
		});
	};

	// 打印小标签
	const handlePrintAct = async(orderList?: any) => {
		if (!orderList?.length) {
			return;
		}
		let printList = [];
		printList = orderList.map((item) => {
			return {
				liveTradePrintTaskId: currentLiveInfo.id,
				...item,
			};
		});

		// 打印前回调 - 立即标记为正在打印，避免重复打印
		setLiveList(prevList => {
			return prevList.map(item => {
				// 只标记当前要打印的数据
				const isCurrentPrinting = orderList.some(printItem => printItem.id === item.id);
				if (isCurrentPrinting) {
					return {
						...item,
						isPrinting: true, // 只标记正在打印的数据
					};
				}
				return item;
			});
		});
		
		PrintCenter.batchPrintKhbq({
			temp: selectedZbdXbqTemp,
			orderList: printList,
			printType: BQ_PRINT_TYPE.扣号标签,
			printer: settingsForm?.getFieldValue("selectedPrinter"),
			isLiveTag: true,
			printNoPrefix: settingsForm?.getFieldValue("printNoPrefix"), // 打印前缀
			beforePrintHook: (list) => {
				let lastNo = 0; // 最新打印序号
				list.forEach((item, index) => {
					lastNo = item?.printNo;

					const message = `#打印序号: ${item.printNo}  #正在打印，【${item.buyerNick}】 内容 【${item.comment}】`;
					addInfoMessage(message);
				});
				setLastPrintNo(lastNo); // 更新打印序号
			},
			afterSavePrintLogHook: (list) => {
				// 打印完成回调
				list.forEach((item, index) => {
					const message = `#打印序号: ${item.printNo}  #完成打印，【${item.buyerNick}】 内容 【${item.comment}】`;
					addInfoMessage(message, '#52C41A');
				});

				// 更新标签打印数量（前端直接统计，实时更新）
				setPrintTagCount(prevCount => prevCount + list.length);

				// 保存符合抢单规则的扣号弹幕
				const saveCommentData = async() => {
					try {
						const liveCommentDataList = list.map(item => {
							return {
								commentNo: item.id, // 弹幕序号
								commentTime: item.createTime, // 扣号时间
								commentContent: item.comment, // 扣号内容
								buyerNick: item.buyerNick, // 买家昵称
								buyerId: item.uid, // 买家id，平台用户id
								commentStatus: 2, // 弹幕状态：弹幕状态1未打印 2已打印 3打印失败
								commentMatchRuleId: item.rule?.ruleID || '', // 弹幕匹配扣号规则id
								printErrorMsg: '', // 打印失败原因
								printTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 打印时间
								printNo: item.printNo, // 打印序号
								labelNo: item.printNo, // 标签序号（使用打印序号）
								isBlackList: item.isBlackList, // 是否黑名单
							};
						});

						await TradeLivePrintBatchSaveLiveCommentApi({
							liveTradePrintTaskId: currentLiveInfo.id,
							liveCommentDataList,
						});

						console.log('直播弹幕数据保存成功', liveCommentDataList);
					} catch (error) {
						console.error('保存直播弹幕数据失败:', error);
						// 可以选择在这里添加错误提示消息
						addInfoMessage('#系统提示: 保存直播弹幕数据失败', '#f5222d');
					}
				};

				// 异步调用保存数据
				saveCommentData();

				// 更新打印状态
				setLiveList(prevList => {
					const updatedList = prevList.map(item => {
						const printedItem = list.find(printed => printed.id === item.id);
						if (printedItem) {
							return {
								...item,
								isPrinted: true,
								isPrinting: false, // 清除正在打印状态
							};
						}
						return item;
					});

					// 重新计算待打印数量
					const newWaitPrintCount = updatedList.filter(item => item.isAllowPrint && !item.isPrinted && !item.isPrinting).length;
					setWaitPrintCount(newWaitPrintCount);

					return updatedList;
				});
			},
		});
	};

	const clearOrderList = () => {
		setLiveList([]);
		setInfoList([...defaultInfoList]);
		setBlackList([]);
		setWaitPrintCount(0); // 重置待打印数量
		setFilterTradeNum(0); // 重置过滤数量
		setPrintTagCount(0); // 重置标签打印数量
	};

	// 移除原有的useEffect打印逻辑，现在在handleLiveInfo中直接处理

	// 处理直播数据 - 专门处理轮次倒计时
	const handleLiveData = () => {
		liveTimer = setInterval(() => {
			const currentTime = Date.now();

			// 更新轮次倒计时
			setRoundsList(prevRounds => {
				const updatedRounds = prevRounds.map(round => {
					// 如果轮次已结束或配置已变更，跳过处理
					if (!round.isActive || round.isConfigChanged) {
						return round;
					}

					// 计算轮次倒计时
					const elapsedTime = Math.floor((currentTime - round.startTime) / 1000);
					const timeRand = round.rule?.timeRand || 120;
					const remainingTime = Math.max(0, timeRand - elapsedTime);

					// 检查轮次是否结束
					if (remainingTime === 0 && round.remainingTime > 0) {
						// 轮次刚结束，发送结束消息
						const message = `※抢单※: 数字：【${round.number}】倒计时已结束，共计【${round.items.length}】单被抢`;
						addInfoMessage(message);

						// 轮次结束后都保留，只是标记为非活跃状态
						return {
							...round,
							remainingTime: 0,
							isActive: false,
						};
					}

					return {
						...round,
						remainingTime,
					};
				}).filter(Boolean); // 过滤掉null项（被删除的轮次）

				return updatedRounds;
			});

			// 同步更新liveList中的倒计时
			setLiveList(prevList => {
				const currentRounds = getRoundsList();
				const roundsMap = new Map(currentRounds.map(round => [round.roundId, round]));

				const updatedList = prevList.map(item => {
					if (item.roundId && roundsMap.has(item.roundId)) {
						const round = roundsMap.get(item.roundId);
						return {
							...item,
							remainingTime: round.remainingTime.toString(),
						};
					}
					return item;
				});

				// 统计待打印数量和过滤数量
				const waitPrintCount = updatedList.filter(item => item.isAllowPrint && !item.isPrinted && !item.isPrinting && parseInt(item.remainingTime, 10) > 0).length;
				const filterTradeNum = updatedList.filter(item => item.isAllowPrint === false).length;

				setWaitPrintCount(waitPrintCount);
				setFilterTradeNum(filterTradeNum);

				return updatedList;
			});

			// 更新倒计时列表显示
			updateCountdownList();

		}, 1000);
	};

	// 检查弹幕匹配条件
	const checkCommentMatch = (comment: string, numType: number, keyword: string, numRangeStart: number, numRangeEnd: number) => {
		// 提取数字
		const numbers = comment.match(/\d+/g);
		if (!numbers || numbers.length === 0) {
			return false;
		}

		const number = parseInt(numbers[0], 10);
    
		// 检查数字范围
		if (number < numRangeStart || number > numRangeEnd) {
			return false;
		}

		// 检查弹幕类型匹配
		if (numType === 1) {
			// 仅纯数字
			return /^\d+$/.test(comment.trim());
		} else if (numType === 2) {
			// 全部打印（数字、符号）
			return true;
		} else if (numType === 3) {
			// 数字+关键词 - 修复：检查关键词是否有效且弹幕包含关键词
			return keyword && keyword.trim() !== '' && comment.includes(keyword.trim());
		}

		return false;
	};

	// 添加信息消息
	const addInfoMessage = (message: string, color = 'rgba(0, 0, 0, 0.85)') => {
		setInfoList(prevList => [
			...prevList,
			{
				createTime: dayjs().format('HH:mm:ss'),
				comment: message,
				color,
			}
		]);
	};

	// 滚动到 infoScroll 底部
	const scrollToBottom = () => {
		if (infoScrollRef.current) {
			infoScrollRef.current.scrollTop = infoScrollRef.current.scrollHeight;
		}
	};

	// 滚动到 BaseTable 底部
	const scrollTableToBottom = () => {
		if (baseTableRef.current && baseTableRef.current.scrollToIndex && liveList.length > 0) {
			baseTableRef.current.scrollToIndex(liveList.length - 1);
		}
	};

	// 滚动到 countdownAreaScroll 底部
	const scrollCountdownToBottom = () => {
		if (countdownScrollRef.current) {
			countdownScrollRef.current.scrollTop = countdownScrollRef.current.scrollHeight;
		}
	};

	// 监听 infoList 变化，自动滚动到底部
	useEffect(() => {
		scrollToBottom();
	}, [infoList.length]);

	// 监听 liveList 变化，自动滚动到底部
	useEffect(() => {
		baseTableRefTimer && clearTimeout(baseTableRefTimer);
		baseTableRefTimer = setTimeout(() => {
			scrollTableToBottom();
		}, 1000);
	}, [liveList.length]);

	// 监听 liveCountdownList 变化，自动滚动到底部
	useEffect(() => {
		scrollCountdownToBottom();
	}, [liveCountdownList.length]);

	// 更新倒计时列表 - 基于轮次数据
	const updateCountdownList = () => {
		const currentRounds = getRoundsList();
		const formValue = getAllRule()?.[0] || params?.liveCommentRuleList?.[0] || {};
		const { itemType } = formValue;

		if (itemType === 1) {
			// 普通商品模式：显示所有活跃轮次，无倒计时
			const countdownList = currentRounds
				.filter(round => round.isActive && !round.isConfigChanged)
				.map(round => ({
					number: round.number,
					count: round.items.length,
					time: '-',
					isActive: true,
					roundId: round.roundId,
				}));

			setLiveCountdownList(countdownList);
			return;
		}

		// 尾货/孤品模式 - 直接基于轮次数据
		setLiveCountdownList(prevList => {
			// 更新现有轮次
			const updatedList = prevList.map(item => {
				const round = currentRounds.find(r => r.roundId === item.roundId);
				if (round) {
					return {
						...item,
						count: round.items.length,
						time: round.remainingTime,
						isActive: round.isActive,
						isConfigChanged: round.isConfigChanged,
					};
				} else {
					// 轮次已不存在（被删除了），直接从倒计时列表中移除
					return false;
				}
			}).filter(Boolean); // 过滤掉false项

			// 添加新轮次
			const existingRoundIds = new Set(updatedList.map(item => item.roundId));
			currentRounds.forEach(round => {
				if (!existingRoundIds.has(round.roundId)) {
					updatedList.push({
						number: round.number,
						count: round.items.length,
						time: round.remainingTime,
						isActive: round.isActive,
						roundId: round.roundId,
						isConfigChanged: round.isConfigChanged,
					});
				}
			});

			return updatedList;
		});
	};


	// 处理插件信息
	const handleLiveInfo = useCallback((data: SendMessageData) => {
		console.log('%c [ 接受插件数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', data, shopInfo, liveList);
		const { commentList = [], shopName, type, content } = data || {};
		// 普通弹幕数据处理
		if (type === MessageType.DANMU) {
			// 判断是否是当前直播店铺
			if (shopName !== shopInfo.shopName) {
				console.log('%c [ 不是当前直播店铺 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '不是当前直播店铺');
				return;
			}
			const currentBlackList = getBlackList(); // 获取当前黑名单数据
			const currentList = getLiveList();
			const currentRounds = getRoundsList();
			const formValue = getAllRule()?.[0] || params?.liveCommentRuleList?.[0] || {};
			const { timeRand, itemType, maxPrintUserCount, numType, keyword, numRangeStart, numRangeEnd } = formValue;
			const currentTime = Date.now();

			const newListItems = [];
			const updatedRounds = [...currentRounds];
			const printableItems = []; // 可以打印的弹幕

			commentList?.forEach((item, index) => {
				const { comment, createTime } = item;

				// 是否黑名单用户
				const isBlackList = currentBlackList.some(blackItem => blackItem.buyerOpenUid === item.uid);

				// 检查弹幕匹配条件
				const isMatchComment = checkCommentMatch(comment, numType, keyword, numRangeStart, numRangeEnd);
				if (!isMatchComment) {
					// 不匹配的弹幕直接加入列表
					newListItems.push({
						...item,
						id: `${shopInfo.uid}_${createTime}_${index}`,
						isAllowPrint: false,
						position: 0,
						isOutNumber: false,
						roundId: null,
						commentNumber: '',
						isPrinted: false,
						remainingTime: '0',
						rule: formValue,
						isBlackList,
					});
					return;
				}

				// 提取数字
				const numbers = comment.match(/\d+/g);
				const extractedNumber = numbers ? numbers[0] : '';
				const createTimeStamp = new Date(createTime).getTime();
				const elapsedTime = Math.floor((currentTime - createTimeStamp) / 1000);
				const remainingTime = Math.max(0, timeRand - elapsedTime);

				// 查找是否有该数字的活跃轮次
				let targetRound = updatedRounds.find(round => round.number === extractedNumber
					&& round.isActive
					&& !round.isConfigChanged);

				let roundId: string;
				let position: number;
				let isAllowPrint = false;
				let isOutNumber = false;

				if (targetRound) {
					// 加入现有轮次
					roundId = targetRound.roundId;
					position = targetRound.items.length + 1;
					targetRound.items.push(item);
				} else {
					// 创建新轮次
					roundId = `${extractedNumber}_${createTimeStamp}`;
					position = 1;

					// 如果keepHistoryData为false，删除相同数字的旧轮次
					if (!keepHistoryData) {
						// 找到相同数字的非活跃轮次并删除
						const indexesToRemove = [];
						updatedRounds.forEach((round, index) => {
							if (round.number === extractedNumber && !round.isActive) {
								indexesToRemove.push(index);
							}
						});
						// 从后往前删除，避免索引变化
						indexesToRemove.reverse().forEach(index => {
							updatedRounds.splice(index, 1);
						});
					}

					targetRound = {
						roundId,
						number: extractedNumber,
						items: [item],
						startTime: createTimeStamp,
						remainingTime,
						isActive: true,
						isConfigChanged: false,
						rule: formValue,
						isBlackList
					};
					updatedRounds.push(targetRound);
				}

				// 根据抢单监控模式计算打印状态
				if (itemType === 1) {
					// 普通商品：打印全部符合条件的弹幕
					isAllowPrint = true;
				} else if (itemType === 2) {
					// 尾货商品：打印前几位
					if (position <= maxPrintUserCount) {
						isAllowPrint = true;
					} else {
						isOutNumber = true;
					}
				} else if (itemType === 3) {
					// 孤品模式：只打印第一位
					if (position === 1) {
						isAllowPrint = true;
					}
				}

				const newItem = {
					...item,
					id: `${shopInfo.uid}_${createTime}_${index}`,
					isAllowPrint,
					position,
					isOutNumber,
					roundId,
					commentNumber: extractedNumber,
					isPrinted: false,
					remainingTime: remainingTime.toString(),
					rule: formValue,
					isBlackList
				};

				newListItems.push(newItem);

				// 如果可以打印，加入打印队列并发送抢单消息
				if (isAllowPrint && remainingTime > 0) {
					printableItems.push(newItem);

					// 发送抢单消息
					let message = '';
					if (itemType === 1) {
						message = `※抢单※: (${item.buyerNick})数字：【${extractedNumber}】，为第【${position}】位`;
					} else if (itemType === 2) {
						message = `※抢单※: (${item.buyerNick})数字：【${extractedNumber}】，为第【${position}】位，此数字剩余倒计时：【${remainingTime}】秒`;
					} else if (itemType === 3) {
						message = `※抢单※: (${item.buyerNick})数字：【${extractedNumber}】，为第【${position}】位，抢单结束`;
					}

					if (message) {
						addInfoMessage(message);
					}
				}
			});

			// 更新数据
			const showList = [...currentList, ...newListItems];
			setLiveList(showList);
			setRoundsList(updatedRounds);

			// 立即触发打印
			if (printableItems.length > 0) {
				handlePrintAct(printableItems);
			}

		} else {
			// 处理其他类型的消息
			let message = '';

			if (type === MessageType.PLUGIN) {
				message = `#系统消息: #插件：${content}`;
			} else if (type === MessageType.OTHER) {
				message = `#系统消息: #其他：${content}`;
			} else if (type === MessageType.ERROR) {
				message = `#系统消息: #错误：${content}`;
			} else {
				message = `#系统消息: #未知类型：${content || '无内容'}`;
			}

			if (message) {
				addInfoMessage(message, '#f5222d');
			}
		}
	}, [shopInfo, liveList]);

	const getBlackListAll = () => {
		TradeLivePrintQueryCustomerBlackListApi({
			liveTradePrintTaskId: currentLiveInfo.id,
			requestVersion: "v2",
		}).then((res) => {
			console.log('%c [ 打印黑名单标识 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
			setBlackList(res || []);
		});
	};

	// 打开直播中控台
	const handleOpenLiveCenter = () => {
		// 去掉无用参数，只保留核心的直播中控台链接
		const liveCenterUrl = 'https://fxg.jinritemai.com/ffa/content-tool/live/control';

		const checkLiveCenterIsOpen = (window as any)?.checkLiveCenterIsOpen;
		if ((window as any)?.checkLiveCenterIsOpen) {
			// 检查直播中控台是否打开
			const isOpen = checkLiveCenterIsOpen(shopInfo);
			if (isOpen) {
				// 直播中控台已打开，则聚焦到该窗口
				message.info('直播中控台已打开,请勿重复打开');
			} else {
				// 直播中控台未打开，则在新窗口打开直播中控台
				window.open(liveCenterUrl, '_blank', 'fullscreen=yes,noopener,noreferrer');
			}
		} else {
			// 在新窗口打开直播中控台
			window.open(liveCenterUrl, '_blank', 'fullscreen=yes,noopener,noreferrer');
		}
	};

	// 保存设置
	const handleSaveSettings = async() => {
		try {
			const values = settingsForm.getFieldsValue();
			
			// 将表单字段名映射为校验函数期望的字段名
			const checkValues = {
				itemType: values.itemType,
				tailGoodsSeconds: values.timeRand, // timeRand -> tailGoodsSeconds
				tailGoodsCount: values.maxPrintUserCount, // maxPrintUserCount -> tailGoodsCount
				barrageFilter: values.numType, // numType -> barrageFilter
				keyword: values.keyword,
				numberRangeMin: values.numRangeStart, // numRangeStart -> numberRangeMin
				numberRangeMax: values.numRangeEnd, // numRangeEnd -> numberRangeMax
			};
			
			// 调用校验函数
			if (!checkDanmuLivePrint(checkValues)) {
				return; // 校验失败，不继续保存
			}

			// 检查配置是否与allRule第一条数据完全一致
			const allRules = getAllRule();
			const firstRule = allRules?.[0];
			if (firstRule) {
				const isSameConfig = firstRule.itemType === values.itemType
					&& firstRule.timeRand === values.timeRand
					&& firstRule.maxPrintUserCount === values.maxPrintUserCount
					&& firstRule.numType === values.numType
					&& (firstRule.keyword || '') === (values.keyword || '')
					&& firstRule.numRangeStart === values.numRangeStart
					&& firstRule.numRangeEnd === values.numRangeEnd;
				
				if (isSameConfig) {
					message.info('配置没有变更');
					return;
				}
			}

			// 记录原始配置用于生成变更信息
			const originalRule = firstRule || {};

			let saveParams:TradeLivePrintUpdateLiveCommentRuleRequest = {};

			const oldList = allRules?.map((item, index) => {
				return {
					ruleID: item.ruleID,
					itemType: values.itemType,
					timeRand: values.timeRand,
					maxPrintUserCount: values.maxPrintUserCount,
					numType: values.numType,
					keyword: values.keyword || '',
					numRangeStart: values.numRangeStart,
					numRangeEnd: values.numRangeEnd,
					status: 2, // 状态1.有效2.无效
				};
			});
			const newList = [
				...oldList,
				{
					itemType: values.itemType,
					timeRand: values.timeRand,
					maxPrintUserCount: values.maxPrintUserCount,
					numType: values.numType,
					keyword: values.keyword || '',
					numRangeStart: values.numRangeStart,
					numRangeEnd: values.numRangeEnd,
					status: 1, // 状态1.有效2.无效
				}
			];
			saveParams = {
				liveTradePrintTaskId: currentLiveInfo.id,
				liveCommentRuleList: newList
			};
			
			const res = await TradeLivePrintUpdateLiveCommentRuleApi(saveParams);
			setAllRule(res || []);

			// 保存成功后，只修改轮次数据，停止所有活跃轮次
			setRoundsList(prevRounds => {
				return prevRounds.map(round => {
					if (round.isActive) {
						return {
							...round,
							remainingTime: 0,
							isActive: false,
							isConfigChanged: true,
						};
					}
					return round;
				});
			});

			// 同步更新liveList中的倒计时状态
			setLiveList(prevList => {
				return prevList.map(item => {
					if (item.roundId && item.isAllowPrint) {
						return {
							...item,
							remainingTime: '0',
							isConfigChanged: true,
						};
					}
					return item;
				});
			});

			// 将现有倒计时列表标记为结束状态
			setLiveCountdownList(prevList => {
				return prevList.map(item => ({
					...item,
					time: 0,
					isActive: false,
					isConfigChanged: true,
				}));
			});

			// 添加保存成功消息 - 显示具体变更项
			const changes = [];
			
			// 抢单监控模式变更
			if (originalRule.itemType !== values.itemType) {
				const originalMode = originalRule.itemType === 1 ? '普通商品' : originalRule.itemType === 2 ? '尾货商品' : '孤品商品';
				const newMode = values.itemType === 1 ? '普通商品' : values.itemType === 2 ? '尾货商品' : '孤品商品';
				changes.push(`抢单监控模式：${originalMode}→${newMode}`);
			}
			
			// 倒计时时间变更
			if (originalRule.timeRand !== values.timeRand) {
				changes.push(`倒计时时间：${originalRule.timeRand || '-'}→${values.timeRand}秒`);
			}
			
			// 打印数量变更
			if (originalRule.maxPrintUserCount !== values.maxPrintUserCount) {
				changes.push(`打印数量：${originalRule.maxPrintUserCount || '-'}→${values.maxPrintUserCount}位`);
			}
			
			// 弹幕匹配类型变更
			if (originalRule.numType !== values.numType) {
				const originalType = originalRule.numType === 1 ? '仅纯数字' : originalRule.numType === 2 ? '全部打印' : '数字+关键词';
				const newType = values.numType === 1 ? '仅纯数字' : values.numType === 2 ? '全部打印' : '数字+关键词';
				changes.push(`弹幕匹配：${originalType}→${newType}`);
			}
			
			// 关键词变更
			if ((originalRule.keyword || '') !== (values.keyword || '')) {
				changes.push(`关键词：${originalRule.keyword || '无'}→${values.keyword || '无'}`);
			}
			
			// 数字范围变更
			if (originalRule.numRangeStart !== values.numRangeStart || originalRule.numRangeEnd !== values.numRangeEnd) {
				changes.push(`数字范围：${originalRule.numRangeStart || '-'}-${originalRule.numRangeEnd || '-'}→${values.numRangeStart}-${values.numRangeEnd}`);
			}
			
			const changeText = changes.length > 0 ? changes.join('，') : '配置已更新';
			const info = `#系统提示: 直播设置保存成功，${changeText}`;
			addInfoMessage(info, '#f5222d');
		
			message.success('设置保存成功');
		} catch (error) {
			console.error('保存设置失败:', error);
			message.error('保存设置失败');
		}
	};

	const onFormValueChange = (changedValues: {}, allValues) => {
		console.log(allValues);
		setFormInfo(allValues);
		
		// 如果打印前缀发生变化，添加消息
		if (changedValues.printNoPrefix !== undefined) {
			const message = `#系统提示: 打印序号前缀变更为： 【${allValues.printNoPrefix}】`;
			addInfoMessage(message, '#f5222d');
		}
	};

	// 打印序号清零
	const clearPrintNum = () => {
		Modal.confirm({
			centered: true,
			title: '请注意',
			content: '打印序号清零后将从0开始计数，且不能恢复。是否确认继续操作？',
			width: '400px',
			onOk: async() => {
				await TradePrintEmptyPrintNumApi({ type: 'kdd' }); // 暂时用快递单
				setLastPrintNo(0); // 打印序号清零后，将打印序号设置为0
				
				// 添加清零成功消息
				addInfoMessage('#系统提示: 打印序号清零成功', '#f5222d');
			}
		});
	};

	const clearLiveTimer = () => {
		if (liveTimer) {
			clearInterval(liveTimer);
			liveTimer = null;
		}
	};

	const showLiveCountdownList = useMemo(() => {
		if (liveCountdownList?.length == 0) {
			return [{}, {}, {}];
		} else if (liveCountdownList?.length < 3 && liveCountdownList?.length > 0) {
			return [{}, {}, {}]?.map((item, index) => {
				if (liveCountdownList[index]) {
					return liveCountdownList[index];
				} else {
					return {};
				}
			});
		} else {
			return liveCountdownList;
		}
	}, [liveCountdownList]);

	// 初始化表单值
	useEffect(() => {
		const initialValues = getInitialValues;
		settingsForm.setFieldsValue(initialValues);
		setFormInfo(initialValues);
		setAllRule([initialValues]);
	}, [getInitialValues]);


	useEffect(() => {
		if (params?.isOpenBlackList) {
			getBlackListAll();		
		}
	}, [params.isOpenBlackList]);

	useEffect(() => {
		if (!isLiving) {
			// window.addEventListener('message', handleLiveInfo, false);

			// 将处理方法挂载到window对象上，供浏览器插件调用
			(window as any).handleLiveDanmuMessage = (data: any) => {
				handleLiveInfo(data);
			};

			return () => {
				// window.removeEventListener('message', handleLiveInfo, false);
				delete (window as any).handleLiveDanmuMessage;
			};
		} 
	}, [isLiving, handleLiveInfo]);

	useEffect(() => {
		// console.log('%c [ 默认打印机 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', defaultPrinter);
		settingsForm.setFieldsValue({
			selectedPrinter: defaultPrinter || printersList[0],
		});
	}, [printersList, defaultPrinter]);

	useEffect(() => {
		isManuallyClosed = false; // 重置为手动关闭
		fetchShopMap(); // 获取店铺信息映射

		handleLiveData(); // 定时处理直播数据

		// 有在直播中的任务,每10秒更新一次状态，防止直播被同账号登录给关掉了不知道
		timer = setInterval(() => {
			updateStatusCount();
		}, 10000);

		const clear = async() => {
			clearLiveTimer();
			clearCountTimer();
			clearOrderList(); // 弹框关闭时清空订单列表
		};
		
		return () => {
			clear();
		};
	}, []);

	useEffect(() => {
		if (!isLiving) {
			const handleBeforeUnload = () => {
				// 只有在直播中且未手动关闭时才发送关闭请求
				if (!isManuallyClosed) {
					closeLive();
				}
			};
			
			window.addEventListener('beforeunload', handleBeforeUnload);

			return () => {
				window.removeEventListener('beforeunload', handleBeforeUnload);
			};
		}
	}, [isLiving, isManuallyClosed, currentLiveInfo.id]);

	return (
		<>
			{
				isLiving && (
					<Modal
						wrapClassName={ styles["live-print-modal-wrap-living"] }
						maskStyle={ { top: "100px", zIndex: 998 } }
						centered
						title="自动打印"
						visible
						width={ 480 }
						closable
						destroyOnClose
						maskClosable={ false }
						keyboard={ false }
						okText="关闭自动打印"
						onOk={ closeConfirm }
						onCancel={ closeConfirm }
						cancelButtonProps={ {
							style: {
								display: 'none'
							}
						} }
					>
						<div className={ styles['living-print-modal-content'] }>
							<div className={ styles['living-print-modal-content-item'] }>
								<div className={ styles['living-print-modal-content-item-title'] }>
									直播场次：
								</div>
								<div className={ styles['living-print-modal-content-item-content'] }>
									{currentLiveInfo.liveNo || '-'}
								</div>
							</div>

							<div className={ styles['living-print-modal-content-item'] }>
								<div className={ styles['living-print-modal-content-item-title'] }>
									直播店铺：
								</div>
								<div className={ styles['living-print-modal-content-item-content'] }>
									<PlatformIcon platform={ shopInfo.platform } fontSize={ 14 } />
									<span className="r-c-black85">{shopInfo.shopName}</span>
									<span className={ styles.autoTip }>自动打印中...</span>
								</div>
							</div>
						</div>
					</Modal>
				)
			}

			{
				!isLiving && (
					<Modal
						wrapClassName={ styles["live-print-modal-wrap"] }
						maskStyle={ { top: "100px", zIndex: 998 } }
						centered
						title="自动打印"
						visible
						width={ 1200 }
						closable
						destroyOnClose
						maskClosable={ false }
						keyboard={ false }
						okText="关闭自动打印"
						onOk={ closeConfirm }
						onCancel={ closeConfirm }
						cancelButtonProps={ {
							style: {
								display: 'none'
							}
						} }
						footer={ (
							<div className="r-flex r-jc-sb r-ai-c">
								<div className={ styles.tip }>直播自动打印时，不可以直接关闭当前页，关闭即停止打印</div>
								<Button type="primary" onClick={ closeConfirm }>关闭自动打印</Button>
							</div>
						) }
						// bodyStyle={ { maxHeight: 'calc(100vh - 240px)' } }
					>
						<div className={ styles.content }>
							<div className={ styles.left }>
								<div className={ styles.table }>
									<BaseTable
										ref={ baseTableRef }
										size="small"
										bordered={ false }
										loading={ false }
										columns={ liveOrderColumns }
										dataSource={ liveList }
										rowKey="id"
										pagination={ false }
										scroll={ { y: 540 } } // 'calc(100vh - 680px)'
										locale={ {
											emptyText: (
												<div style={ { width: '100%', textAlign: 'center', paddingTop: '205px' } } className="r-flex r-fd-c r-jc-c r-ai-c">
													<Empty 
														image={ Empty.PRESENTED_IMAGE_DEFAULT }
														description={ <p>请开启店铺直播中控台</p> }
													/>
												</div>
											)
										} }
									/>
								</div>
								<div className={ styles.info }>
									<div className={ styles.infoScroll } ref={ infoScrollRef }>
										{infoList?.map((item, index) => {
											return (
												<div key={ index } className={ styles.infoItem }>
													<span>[{item.createTime}]</span>
													<span className={ cs(styles.infoItemComment) } style={ { color: item.color } }>{item.comment}</span>
												</div>
											);
										})}
									</div>
								</div>
							</div>
							<div className={ styles.right }>
								<div className={ styles.shopInfo }>
									<div className={ styles['content-item'] }>
										<div className={ styles['content-item-title'] }>
											直播场次：
										</div>
										<div className={ styles['content-item-content'] }>
											{currentLiveInfo.liveNo || '-'}
										</div>
									</div>

									<div className={ styles['content-item'] }>
										<div className={ styles['content-item-title'] }>
											直播店铺：
										</div>
										<div className={ styles['content-item-content'] }>
											<PlatformIcon platform={ shopInfo.platform } fontSize={ 14 } />
											<span className="r-c-black85">{shopInfo.shopName}</span>
										</div>
									</div>

									<div className="r-flex r-ai-c">
										<div className={ styles.openLiveCenter } onClick={ handleOpenLiveCenter }>打开直播中控台</div>

										{/* 判断是否为开发环境 */}
										{(window.location.href.includes('http://*************:8088/') || window.location.href.includes('localhost:8088')) && (
											<div onClick={ getMockMessageData } className="r-c-warning r-ml-20">生成测试数据</div>
										)}
									</div>
									
								</div>

								<Form
									form={ settingsForm }
									layout="horizontal"
									onValuesChange={ onFormValueChange }
									size="small"
								>
									<div className={ styles.timeInfo }>
									
										<div className={ styles.settingsForm }>
											<div className="r-flex r-ai-c">
												<span className={ styles.titleIcon } />
												<span className={ styles.titleText }>直播设置</span>
											</div>
									
										
											{/* 抢单监控 */}
											<div className="r-mb-8 r-mt-10">
												<span>抢单监控：</span>
												<Form.Item
													name="itemType"
													label=""
													noStyle
												>
													<Select placeholder="请选择抢单监控模式" style={ { width: '200px' } }>
														<Select.Option value={ 1 }>普通商品</Select.Option>
														<Select.Option value={ 2 }>尾货商品</Select.Option>
														<Select.Option value={ 3 }>孤品商品</Select.Option>
													</Select>
												</Form.Item>
											</div>
											

											{/* 时间设置 */}
											<div className="r-flex r-jc-sb r-ai-fe r-mb-16">
												<div className="r-flex r-ai-c">
													<span className="r-mr-4">在</span>
													<Form.Item
														name="timeRand"
														label=""
														noStyle
													>
														<InputNumber
															min={ 1 }
															precision={ 0 }
															style={ { width: '60px' } }
															placeholder="请输入秒数"
														/>
													</Form.Item>
													<span className="r-ml-4">秒内，</span>
													{
														formInfo.itemType === 1 && (
															<span>打印全部</span>
														)
													}
													{
														formInfo.itemType === 2 && (
															<>
																<span className="r-mr-4">打印前</span>
																<Form.Item
																	name="maxPrintUserCount"
																	label=""
																	noStyle
																>
																	<InputNumber
																		min={ 1 }
																		precision={ 0 }
																		style={ { width: '60px' } }
																		placeholder="请输入打印数量"
																	/>
																</Form.Item>
																<span className="r-ml-4">位</span>
															</>
														)
													}
													{
														formInfo.itemType === 3 && (
															<span>仅打印第一位</span>
														)
													}
												</div>
												
												{/* 确定按钮 */}
												<Form.Item noStyle>
													<Button 
														type="primary" 
														onClick={ handleSaveSettings }
														style={ { width: '60px', alignSelf: 'flex-end' } }
														size="middle"
													>
														确定
													</Button>
												</Form.Item>
											</div>

											{/* 倒计时区域 */}
											<div className={ cs(styles.countdownArea, 'r-mb-16') }>
												<div className={ styles.countdownAreaScroll } ref={ countdownScrollRef }>
													{
														showLiveCountdownList?.map((item, index) => {
															return (
																<div className={ styles.countdownAreaItem } key={ index }>
																	<div className={ cs(styles.countdownAreaItemNumber, { [styles.numberActive]: item.isActive }) }>
																		<div className={ styles.number }>{item.number || '-'}</div>
																		<span className={ styles.text }>数字</span>
																	</div>
																	<div className={ cs(styles.countdownAreaItemNumber, { [styles.countActive]: item.isActive }) }>
																		<div className={ styles.number }>{item.count || '-'}</div>
																		<span className={ styles.text }>数量</span>
																	</div>
																	<div className={ cs(styles.countdownAreaItemNumber, { [styles.timeActive]: item.isActive }) }>
																		<div className={ styles.number }>{item.time ?? '-'}</div>
																		<span className={ styles.text }>秒数</span>
																	</div>
																</div>
															);
														})
													}
												</div>
											</div>
											

											{/* 弹幕匹配 */}
											<div className="r-mb-12">
												<div>弹幕匹配：</div>	
												<Form.Item
													name="numType"
													label=""
													noStyle
												>
													<Radio.Group>
														<div className="r-flex r-ai-c r-fw-w r-mt-8">
															<Radio value={ 1 }>仅纯数字</Radio>
															<Radio value={ 2 }>全部打印（数字、符号）</Radio>
															<Radio value={ 3 }>
																<div className="r-flex r-ai-c r-mt-8">
																	<span>数字+关键词</span>
																	<Form.Item name="keyword" noStyle>
																		<Input 
																			placeholder="请输入关键词" 
																			style={ { width: 160, marginLeft: 8 } }
																		/>
																	</Form.Item>
																</div>
															</Radio>
														</div>
													</Radio.Group>
												</Form.Item>
											</div>
											

											{/* 扣号范围 */}
											<div>
												<div>扣号范围：</div>
												<Form.Item
													label=""
													noStyle
												>
													<div className="r-flex r-ai-c r-mt-8">
														<span className="r-mr-4">数字范围</span>
														<Form.Item
															name="numRangeStart"
															noStyle
															rules={ [{ required: true, message: "请输入最小值" }] }
														>
															<InputNumber
																min={ 1 }
																precision={ 0 }
																style={ { width: 80 } }
																placeholder="最小值"
															/>
														</Form.Item>
														<span style={ { margin: '0 2px' } }>-</span>
														<Form.Item
															name="numRangeEnd"
															noStyle
															rules={ [{ required: true, message: "请输入最大值" }] }
														>
															<InputNumber
																min={ 1 }
																precision={ 0 }
																style={ { width: 80 } }
																placeholder="最大值"
															/>
														</Form.Item>
													</div>
												</Form.Item>
											</div>
											

										
										</div>
									</div>
								
								
									<div className={ styles.printInfo }>
										<div className={ styles.printInfoCount }>
											<div className="r-flex r-ai-c r-gap-8">
												<span>标签打印数量</span>
												<span className={ styles.labelNum }>{printTagCount}</span>
											</div>
											<div className="r-flex r-ai-c r-ml-16 r-gap-8">
												<span>待打印数量</span>
												<span className={ styles.waitPrintNum }>{waitPrintCount || 0}</span>
											</div>
										</div>
										
										<div className={ styles.printInfoContent }>
											<div className="r-flex r-ai-c r-mb-8">
												<Form.Item
													required
													name="selectedPrinter"
													label="打印机"
													style={ { marginBottom: 0 } }
													noStyle
												>
													<Select
														size="middle"
														placeholder="打印机"
														style={ { width: "100%" } }
														onChange={ (e) => { setDefaultPrinter(e); } }
													>
														{printersList?.map((printer) => (
															<Select.Option
																key={ printer }
																value={ printer }
															>
																{printer}
															</Select.Option>
														))}
													</Select>
												</Form.Item>
											</div>
											
											<div className="r-flex r-ai-c">
												<Select
													size="middle"
													placeholder="模板"
													style={ { width: "100%" } }
													value={ selectedZbdXbqTemp?.Mode_ListShowId }
													onChange={ (e) => {
														let temp = zbdXbqTempList.find(
															(i) => i.Mode_ListShowId === e
														);
														setSelectedZbdXbqTemp(temp);
													} }
													getPopupContainer={ (e) => e.parentElement }
												>
													{zbdXbqTempList?.map((i) => (
														<Select.Option
															key={ i.Mode_ListShowId }
															value={ i.Mode_ListShowId }
														>
															{i.ExcodeName}
														</Select.Option>
													))}
												</Select>
											</div>
										</div>

										<div className="r-flex r-ai-c r-gap-8">
											<span>打印序号：</span>
											<span>序号前缀</span>
											<Form.Item
												name="printNoPrefix"
												noStyle
											>
												<Input
													size="small"
													style={ { width: 60 } }
													placeholder=""
												/>
											</Form.Item>
											<span>{formInfo.printNoPrefix || ''}{lastPrintNo || '0'}</span>
											<span className={ styles.reset } onClick={ clearPrintNum }>归零</span>
										</div>
										
									</div>

								</Form>
							</div>
						
						</div>
					</Modal>
				)
			}
		</>
		
	);
};

export default observer(DanmuLivePrintModal);
