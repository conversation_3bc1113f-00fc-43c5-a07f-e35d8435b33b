import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { Modal, Button, Form, Input, Select, InputNumber } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import userStore from "@/stores/user";
import AddGoodsDrawer, { AddTypeEnum } from '@/pages/Trade/components/AddGoodsDrawer';
import message from "@/components/message";
import s from './index.module.scss';

interface FakerTradeSettingModalProps {
  visible: boolean;
  onClose: () => void;
}

const INTERVAL_OPTIONS = [
	{ label: '30秒', value: 30 },
	{ label: '1分钟', value: 60 },
	{ label: '5分钟', value: 300 },
	{ label: '10分钟', value: 600 },
	{ label: '30分钟', value: 1800 },
	{ label: '1小时', value: 3600 },
	{ label: '自定义', value: 'custom' }
];

const PRESET_VALUES = INTERVAL_OPTIONS.filter(i => i.value !== 'custom').map(i => i.value as number);

const FakerTradeSettingModal: React.FC<FakerTradeSettingModalProps> = ({
	visible,
	onClose,
}) => {
	const [form] = Form.useForm();

	const [fakerTradeItemList, setFakerTradeItemList] = useState<any[]>([]);
	const [formData, setFormData] = useState<any>({
		gapType: 30,
		flag: undefined
	});
	const [addGoodsDrawer, setAddGoodsDrawer] = useState({
		visible: false,
	});

	const init = async() => {
		const res = await userStore.getFakerTradeConfig();
		console.log('%c [ 虚拟订单设置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
		const gap = Number(res?.intervalSec ?? 30); // 间隔时间（秒）
		const isPreset = PRESET_VALUES.includes(gap); // 是否预设
		const formValues = {
			gapType: isPreset ? gap : 'custom',
			customGap: isPreset ? undefined : gap || undefined,
			flag: res?.fakerTradeFlag ?? '',
		};
		
		form.setFieldsValue(formValues);
		setFormData(formValues); // 同时更新 formData 状态

		const newList = res?.fakerTradeItemList?.map(item => {
			return {
				...item,
				rowId: `${item.numIid || item.itemId}_${item?.skuId}`
			};
		}) || [];
		setFakerTradeItemList(newList);
	};   

	useEffect(() => {
		if (visible) {
			init();
		}
	}, [visible]);

	const onGapTypeChange = (val:any) => {
		if (val !== 'custom') {
			form.setFieldsValue({ customGap: undefined });
		}
	};

	// 保存虚拟订单配置
	const handleSave = async() => {
		const values = await form.validateFields();
		const intervalSec = values.gapType === 'custom' ? Number(values.customGap) : Number(values.gapType);
		if (fakerTradeItemList?.length > 100) {
			message.error('虚拟订单商品最多添加100个');
			return;
		}
		if (!intervalSec) {
			message.error('请选择间隔时间');
			return;
		}

		await userStore.setFakerTradeConfig({
			intervalSec,
			fakerTradeFlag: (values.flag || '').trim(), // 虚拟标签可以不填，由后端推送虚拟单的时候给默认的虚拟订单标记■
			fakerTradeItemList: fakerTradeItemList || []
		});
		onClose?.();
	};

	const onValuesChange = (changedValues, allValues) => {
		setFormData(allValues);
	};

	// 添加商品回调
	const handleAddGoodsOk = (selectRows: any[], type: AddTypeEnum) => {
		console.log('%c [ selectRows ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', selectRows);
		const nextList = [...(fakerTradeItemList || [])];

		selectRows?.forEach((row) => {
			const { num, numIid, platform, platformItemSkuList } = row;
			const skuInfo = platformItemSkuList?.[0];
			const idx = nextList.findIndex((it) => it.rowId === `${numIid}_${skuInfo?.skuId}`);
			const addNum = Number(row?.num || 1);

			if (idx > -1) {
				nextList[idx] = {
					...nextList[idx],
					num: Number(nextList[idx]?.num || 0) + addNum,
				};
			} else {
				nextList.push({ 
					numIid,
					itemId: numIid,
					platform,
					outerId: row.outerId,
					itemTitle: row.title,
					sysItemAlias: row.sysItemAlias,
					skuId: skuInfo?.skuId,
					skuOuterId: skuInfo?.skuOuterId,
					skuName: skuInfo?.skuName,
					skuPrice: skuInfo?.skuPrice,
					sysSkuAlias: skuInfo?.sysSkuAlias,
					sysOuterSkuId: skuInfo?.sysOuterSkuId || skuInfo?.sysSkuOuterId,
					num: addNum,
					rowId: `${numIid}_${skuInfo?.skuId}`
				});
			}
		});

		setFakerTradeItemList(nextList);
		setAddGoodsDrawer({ visible: false });
	};

	const num = useMemo(() => {
		const num = fakerTradeItemList?.length || 0;
		return num;
	}, [fakerTradeItemList]);

	const handleRemoveItem = (item:any) => {
		let newSelectRows = fakerTradeItemList?.filter(d => d.rowId != item.rowId);
		setFakerTradeItemList(newSelectRows);
	};

	return (
		<>
			<Modal
				wrapClassName={ s.modal }
				centered
				title="虚拟订单设置"
				visible
				width="680px"
				closable
				destroyOnClose
				maskClosable={ false }
				keyboard={ false }
				okText="确 定"
				cancelText="取 消"
				onOk={ handleSave }
				onCancel={ onClose }
				bodyStyle={ { maxHeight: 'calc(100vh - 200px)' } }
			>
				<Form form={ form } onValuesChange={ onValuesChange }>
					<div className={ s.row }>
						<div className={ s.label }>虚拟订单生成频率：</div>
						<div className={ s.inline }>
							<span>每隔</span>
							<Form.Item
								name="gapType"
								className={ s.selectItem }
								rules={ [{ required: true, message: '请选择频率' }] }
							>
								<Select 
									size="small" 
									options={ INTERVAL_OPTIONS }
									onChange={ onGapTypeChange } 
									getPopupContainer={ trigger => trigger?.parentElement }
								/>
							</Form.Item>

							{formData?.gapType === 'custom' && (
								<Form.Item
									name="customGap"
									className={ s.customInput }
									rules={ [{ required: true, message: '请输入间隔时间' }] }
								>
									<InputNumber 
										size="small" 
										min={ 1 } 
										max={ 36000 }
										step={ 1 } 
										precision={ 0 }
										placeholder=""
										addonAfter="秒"
									/>
								</Form.Item>
							)}
							<span>生成一笔虚拟订单</span>
						</div>
					</div>

					<div className={ s.row }>
						<div className={ s.label }>虚拟订单标记：</div>
						<Form.Item name="flag" className={ s.inputItem }>
							<Input placeholder="请填写虚拟标识" allowClear size="small" />
						</Form.Item>
					</div>

					<div className={ s.tips }>
						注：不填写时默认展示：<span className={ s.defaultFlag }>■</span>，标签模版添加虚拟标识后生效
					</div>

					<div className="r-flex r-mt-16">
						<div className={ s.label }>虚拟订单商品：</div>
						<div className="r-flex-1">
							<div className="r-flex r-jc-sb r-ai-c">
								<div
									className="r-pointer"
									style={ {
										color: "#1890FF",
									} }
									onClick={ () => {
										setAddGoodsDrawer({
											visible: true
										}); 
									} }
								>添加商品
								</div>
								<div className="r-c-666">{num}/100</div>
							</div>

							<div className={ s.listMain }>
								{fakerTradeItemList?.map((item, index) => (
									<div className={ s.listItem } key={ item?.rowId }>
										<span>{item?.numIid}-{item?.skuId} *{item?.num || 1}</span>
										<span className={ s.itemClose } onClick={ () => handleRemoveItem(item) }>×</span>
									</div>
								))}
							</div>
						</div>
					</div>
				</Form>
			</Modal>

			{
				addGoodsDrawer.visible && (
					<AddGoodsDrawer
						visible={ addGoodsDrawer.visible }
						onClose={ () => setAddGoodsDrawer({ visible: false }) }
						onOk={ handleAddGoodsOk }
						addType={ AddTypeEnum.平台商品 }
						hasCollect={ false }
						hasBatchedNum
						autoSearch={ false }
						drawerTitle="选择商品"
					/>
				)
			}
			
		</>
		
	);
};

export default observer(FakerTradeSettingModal);
