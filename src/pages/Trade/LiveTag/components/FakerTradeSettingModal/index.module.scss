.modal {}

.row {
    display: flex;
    margin-top: 16px;
}

.label {
    width: 140px;
    text-align: right;
    color: #333;
}

.inline {
    display: flex;
}

.selectItem {
    margin: 0 8px;
    width: 100px;
}

.customInput {
    margin: 0 8px 0 0;
    width: 120px;
}

.inputItem {
    margin: 0;
    width: 160px;
}

.tips {
    color: #999;
    font-size: 14px;
    margin-left: 140px;
    margin-top: 6px;
}

.defaultFlag {
    display: inline-block;
}

.listMain {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 8px;

    .listItem {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 4px 0px 4px;
        gap: 4px;
        border-radius: 2px;
        background: #F5F5F5;
        box-sizing: border-box;
        border: 1px solid #F0F0F0;
        font-size: 12px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.85);

        .itemClose {
            cursor: pointer;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.45);
            padding: 0 4px;
        }
    }
}