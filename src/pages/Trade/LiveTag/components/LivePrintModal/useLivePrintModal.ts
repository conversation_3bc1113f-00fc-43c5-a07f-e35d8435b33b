import { useState, useCallback, useRef } from 'react';

interface OrderData {
  id?: string;
  ptTid: string;
  tid?: string; // 添加 tid 字段
  buyerNick?: string;
  receiverName?: string;
  tradeItemInfoList?: any[];
  totalNum?: number;
  payment?: string;
  livePrintStatus?: number;
  errorMsg?: string;
  addTime?: string;
  [key: string]: any;
}

interface UseLivePrintModalReturn {
  liveOrderList: OrderData[];
  addOrderToList: (orderData: OrderData) => void;
  updateOrderPrintStatus: (tid: string, status: number, errorMsg?: string) => void;
  clearOrderList: () => void;
}

export const useLivePrintModal = (): UseLivePrintModalReturn => {
	const [liveOrderList, setLiveOrderList] = useState<OrderData[]>([]);
	const orderMapRef = useRef<Map<string, OrderData>>(new Map());

	// 添加订单到列表的方法
	const addOrderToList = useCallback((orderData: OrderData) => {
		// 使用 tid 作为唯一键
		const uniqueKey = orderData.tid || orderData.ptTid;
		
		const orderWithId = {
			...orderData,
			id: orderData.id || `${uniqueKey}_${Date.now()}`,
			livePrintStatus: orderData.livePrintStatus ?? 0,
			addTime: orderData.addTime || new Date().toLocaleTimeString(),
		};

		setLiveOrderList(prevList => {
			// 优先使用 tid 查找，如果没有 tid 则使用 ptTid
			const existingIndex = prevList.findIndex(item => (item.tid && item.tid === orderData.tid) 
				|| (!item.tid && item.ptTid === orderData.ptTid));
      
			if (existingIndex >= 0) {
				const newList = [...prevList];
				newList[existingIndex] = { ...newList[existingIndex], ...orderWithId };
				orderMapRef.current.set(uniqueKey, newList[existingIndex]);
				return newList;
			} else {
				const newList = [orderWithId, ...prevList];
				orderMapRef.current.set(uniqueKey, orderWithId);
				return newList;
			}
		});
	}, []);

	// 更新订单打印状态的方法
	const updateOrderPrintStatus = useCallback((tid: string, status: number, errorMsg = '') => {
		setLiveOrderList(prevList => prevList.map(order => {
			// 优先使用 tid 匹配，如果没有 tid 则使用 ptTid
			if ((order.tid && order.tid === tid) || (!order.tid && order.ptTid === tid)) {
				const updatedOrder = { ...order, livePrintStatus: status, errorMsg };
				const uniqueKey = order.tid || order.ptTid;
				orderMapRef.current.set(uniqueKey, updatedOrder);
				return updatedOrder;
			}
			return order;
		}));
	}, []);

	// 清空订单列表
	const clearOrderList = useCallback(() => {
		setLiveOrderList([]);
		orderMapRef.current.clear();
	}, []);

	return {
		liveOrderList,
		addOrderToList,
		updateOrderPrintStatus,
		clearOrderList,
	};
};