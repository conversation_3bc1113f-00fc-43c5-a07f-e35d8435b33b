.live-print-modal-wrap {
  top: 100px !important;
  z-index: 998;

  :global {
    .ant-modal-body {
      padding: 16px 24px;
    }

    .ant-table-body {
      overflow-y: auto !important;
      min-height: 137px;

      .ant-table-placeholder td {
        border-bottom: 0;
      }
    }

    .innerTableChange {
      padding: 0px;
    }
  }

  .live-info-wrap {
    background: #f5f5f5;
    padding: 16px;
    display: flex;
    flex-direction: column;
    padding: 16px;
    gap: 8px;
    align-self: stretch;
  }

  .live-print-title {
    font-size: 20px;
    font-weight: 500;
    line-height: 30px;
    color: #52C41A;
    text-align: center;
    border-width: 1px 0px 0px 0px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.06);
    padding-top: 16px;
  }

  .live-print-count-wrap {
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding: 16px 0px 16px 0px;
    gap: 100px;
    align-self: stretch;
    box-sizing: border-box;
    border-width: 0px 0px 1px 0px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.06);

    .live-print-count-num {
      font-size: 20px;
      font-weight: 600;
      line-height: 30px;
      color: #52C41A;
    }

    .live-print-count-num2 {
      font-size: 20px;
      font-weight: 600;
      line-height: 30px;
      color: #FF4D4F;
    }

    .live-print-count-num3 {
      font-size: 20px;
      font-weight: 600;
      line-height: 30px;
      color: #FD8204;
    }

    .live-print-count-text {
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.65);
      margin-top: 8px;
    }
  }

  .shop-list-container {
    transition: height 0.3s ease; // 添加过渡动画

    &.shop-list-collapsed {
      overflow: hidden;
      height: 24px; // 限制高度为一行
    }
  }

  .faker-trade-wrap {
    .faker-trade-btn {
      color: #1890FF;
      cursor: pointer;
    }
  }

  .tip {
    color: #FF4D4F;
    font-size: 14px;
    line-height: 22px;
  }
}