import React, { useEffect, useLayoutEffect, useMemo, useState } from "react";
import cs from "classnames";
import { observer } from "mobx-react";
import { useRequest } from "ahooks";
import _ from "lodash";
import s from './index.module.scss';
import TradeList from "../components/TradeList";
import { tradeStore } from "@/stores";
import { TradeTradeDetailGetApi, TradeUserGetUserCustomSetApi } from "@/apis/trade";
import { getTradeExReachStatus } from "@/utils/trade/judgeExpressReach";
import BottomCom from "../components/BottomCom";
import HotGoodsSearchCom from "./components/HotGoodsSearchCom";
import { hotGoodsDefaultColumnConfig } from "@/constants";
import { ItemTakeGoodsLabelConfigEditApi, ItemTakeGoodsLabelSelectTradeInfoByLabelIdApi } from "@/apis/trade/takeGoodsLabel";
import { handlePackageList } from "../utils";
import BatchModifyMemoModal from "../components/BatchModifyMemoModal";
import BatchModifyFlagModal from "../components/BatchModifyFlagModal";
import TradeOptLogModal from "../components/TradeOptLogModal";
import PrintStyleSetting from "../components/ListItem/components/PrintStyleSetting";
import MergePrintModal from "../ScanPrint/components/MergePrintDetectModal";
import message from "@/components/message";
import scanPrintStore from "@/stores/trade/scanPrint";
import { local } from "@/libs/db";
import userStore from "@/stores/user";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import TradeSortCom from "./components/TradeSortCom";
import useGetState from "@/utils/hooks/useGetState";
import { playAudio } from "../ScanPrint/utils";
import voice扫描成功 from '@/assets/mp3/扫描.mp3';
import voice标签错误 from '@/assets/mp3/标签错误.mp3';
import voice权限不足 from '@/assets/mp3/权限不足.mp3';
import voice重复扫描 from '@/assets/mp3/重复扫描.mp3';

export interface IHotGoodsScanPrintProps {}

const BatchGetDetailNum = 500;
let prevLabelId = '';
const HotGoodsScanPrint = (props: IHotGoodsScanPrintProps) => {
	const [loading, setLoading] = useState(false);
	const [tidOrderType, setTidOrderType, getTidOrderType] = useGetState(1);
	const {
		tradeListStore: {
			list,
			setList,
			isShowPrintStyleSetting,
			setIsShowPrintStyleSetting,
			batchOperateLoading,
			setBatchOperateLoading,
		},
		tradePrintStore: {
			waybillEcho
		},
		setHotGoodsColumnConfig,
		isShowBatchModifyMemoModal,
		isShowBatchModifyFlagModal,
		selectedTemp,
		selectedTempGroup,
		isShowTradeOptLogModal,
		setProductSetting,
	} = tradeStore;
	const {
		setScanPrintFormSetting,
		scanPrintFormSetting,
		getScanPrintSetting,
		scanPrintSetting,
	} = scanPrintStore;

	useEffect(() => {
		userStore.getUserSetting();
		setHotGoodsColumnConfig(hotGoodsDefaultColumnConfig);
		if (!tradeStore.productSetting || !tradeStore.productSetting?.userId) {
			TradeUserGetUserCustomSetApi({}).then(res => {
				setProductSetting(res);
			});
		}
		getScanPrintSetting().then(res => {
			if (res?.scanSet?.tidOrderType) setTidOrderType(res?.scanSet?.tidOrderType);
		});
		return () => {
			setList([]);
			setHotGoodsColumnConfig(null);
			prevLabelId = '';
		};
	}, []);

	useLayoutEffect(() => {
		batchOperateLoading && batchOperateLoading();
		setBatchOperateLoading(null);
	});

	const { runAsync } = useRequest(ItemTakeGoodsLabelSelectTradeInfoByLabelIdApi, {
		manual: true,
	});
	const changePrevLabelId = (val) => {
		prevLabelId = val;
	};

	const handleFinish = (val: any) => {
		val.id = val.id.trim();
		if (prevLabelId == val.id) {
			playAudio(voice重复扫描);
			return;
		}
		sendPoint(Pointer.档口_爆款标签_查询);
		prevLabelId = val.id;
		if (!val.id.match(new RegExp(`^[B]+[0-9]{14}$`))) {
			message.error("您扫描的不是爆款码，请扫描爆款码");
			playAudio(voice标签错误);
			return;
		}
		setLoading(true);
		runAsync({
			labelId: val.id
		}).then(res => {
			console.log('res', res);

			try {
				playAudio(voice扫描成功);
				let tidArr = res?.packageList.map(item => item.tid);
				let promiseArr = [];
				while (tidArr.length) {
					promiseArr.push(TradeTradeDetailGetApi({
						tradeInfos: [{
							tids: tidArr.splice(0, BatchGetDetailNum),
						}],
						tidOrderType: getTidOrderType()
					}));
				}
				Promise.all(promiseArr).then(async res => {
					let _list = await handlePackageList(res.reduce((prev, cur) => prev.concat(cur.data.list), []), false, null, true, { isHotGoodsScanPrint: true });
					setList(_list);
					setLoading(false);
				});
			} catch (error) {
				playAudio(voice标签错误);
				setLoading(false);
			}
		}).catch((error) => {
			if (error.errorCode === 2003) {
				playAudio(voice权限不足);
			} else {
				playAudio(voice标签错误);
			}
			setLoading(false);
		});
	};

	const reList = useMemo(() => {
		return (list)?.filter(item => !item.isFilter);
	}, [list]);


	/** 获取订单可达 */
	useEffect(() => {
		// 当模板选择有更新时，所有被选择的订单都要重新回显单号
		let echoList = list?.filter((i) => i.isChecked);
		if (echoList.length) {
			waybillEcho(echoList);
		}
		getTradeExReachStatus();
	}, [selectedTemp, selectedTempGroup]);

	const handleChange = (val) => {
		let _setting = {
			...scanPrintFormSetting,
			...val,
			id: val?.id?.trim(),
		};
		setScanPrintFormSetting(_setting);
		let _hotGoodsScanPrintSearchSetting = local.get('hotGoodsScanPrintSearchSetting');
		local.set('hotGoodsScanPrintSearchSetting', { ..._hotGoodsScanPrintSearchSetting, ...val });
	};

	const changeSort = (params) => {
		const tidOrderType = params.queryLabelSort;
		setTidOrderType(tidOrderType);
		handleFinish({ id: prevLabelId });
		ItemTakeGoodsLabelConfigEditApi({
			scanSet: {
				...scanPrintSetting.scanSet,
				tidOrderType,
			}
		});
	};

	return (
		<div className={ cs(s['hotGoodsScanPrintPage']) }>
			<HotGoodsSearchCom
				loading={ loading }
				onFinish={ _.debounce((e) => handleFinish(e), 500) }
				onChange={ handleChange }
				onChangePrevLabelId={ changePrevLabelId }
			/>
			<div className={ s['hotGoodsScanPrintInfo'] }>
				{list?.length ? (
					<>
						<div className="r-flex r-mb-12 r-jc-fe">
							展示顺序：
							<TradeSortCom changeSort={ changeSort } queryLabelSort={ tidOrderType } />
						</div>
						<TradeList from="hotGoodsScanPrint" list={ reList } />
					</>
				) : ''}
			</div>
			<div>
				<BottomCom list={ reList } from="hotGoodsScanPrintPage" />

				{isShowBatchModifyMemoModal ? <BatchModifyMemoModal /> : ''}

				{isShowBatchModifyFlagModal ? <BatchModifyFlagModal /> : ''}

				{isShowTradeOptLogModal && tradeStore.tradeOptLogPack ? <TradeOptLogModal /> : null}

				{isShowPrintStyleSetting ? (<PrintStyleSetting visible onCloseModal={ () => { setIsShowPrintStyleSetting(false); } } />) : ''}
			</div>

			<MergePrintModal />
		</div>
	);
};

export default observer(HotGoodsScanPrint);
