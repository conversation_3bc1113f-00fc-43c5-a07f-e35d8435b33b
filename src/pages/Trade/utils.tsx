import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'antd';
import { CaretRightOutlined, CheckOutlined } from '@ant-design/icons';
import _, { isEmpty } from 'lodash';
import { runInAction } from 'mobx';
import dayjs from 'dayjs';
import { PLAT_SPH, PLAT_FXG, PLAT_PDD, PLAT_TB, PLAT_TM, PLAT_HAND, PLAT_ALI, PLAT_KS, PLAT_JD, PLAT_HAND_OLD, PLAT_SCM, AFTERSALE_HANDORDER, GIFT_NORMAL_PLATFORM, GIFT_SPECIAL_PLATFORM, flagArr, allFlagArr, FULL_ORDER_PLATFORM, FULL_PRESENT_ORDER_PLATFORM ,PLAT_DW, dwflagArr, PLAT_KTT} from '@/constants';
import { IFilterOrderItem, IFilterOrderObj, IFlagMemos, IOrders, IPackage, ISubTrade, ITradeRefundStatus, ITradeStatus, Obj } from './interface';
import Icon from '@/components/Icon';
import { tradeStore } from '@/stores';
import { TradeBatchUpdatePendingStatusApi, TradeBatchUpdateTradeFlagApi } from '@/apis/trade';
import event from '@/libs/event';
import { IPrintSet, TradeBatchUpdateTradeFlagRequest } from '@/types/trade/index';
import tradeSetStore from '@/stores/trade/tradeSet';
import { genePrintContent } from '@/utils/trade/printContent';
import { TradePrintContentSetRule } from '@/types/trade/tradeSet';
import { DecryptFiledEnum, DecryptFiledMapEnum, decryptFn } from './components/ListItem/components/Decrypt';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { TradeQueryTradeRequest } from '@/types/trade/search/search';
import PDD_VIRTUAL_IMG from '@/assets/image/trade/pdd_virtual_num.png';
import { local } from '@/libs/db';
import events from '@/utils/events';
import { jdDecrypt } from "@/pages/Trade/components/ListItem/components/Decrypt/index";
import message from "@/components/message";
import { DEFAULT_SHOP_SELECT, FxgConsolidateType, LabelPlatformMap, OccupyTradeOptType, packInitialFieldsMap, PAY_DISPLAY_TYPE_ENUM } from './constants';
import { ItemTakeGoodsLabelSelectWithPageResponse } from '@/types/trade/takeGoodsLabel';
import scanPrintStore from '@/stores/trade/scanPrint';
import { JDOrderShipType, JDStoreOrderType, SendType, TradeStatus } from '@/utils/enum/trade';
import { customSortVal, defaultSortIndex, labelSortVal } from '../Index/Settings/GoodsLabel/constants';
import { isCheckStock } from './components/BottomCom/utils';
import SEND_GOODS_IMG from '@/assets/image/trade/发货标准点.png';
import WAIT_ORDER_IMG from '@/assets/image/trade/等待拼单标准点.png';
import CONFIRM_ORDER_IMG from '@/assets/image/trade/签收标准点.png';
import SUBMIT_ORDER_IMG from '@/assets/image/trade/提交订单标准点.png';
import { ConsolidateType } from '@/pages/Trade/constants';
import s from './index.module.scss';
import { isSourceHand, isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import { platform } from "@/types/schemas/common";
import { SysExchangeTypeEnum } from './components/BottomCom/constants';
import memoFn from '@/libs/memorizeFn';
import userStore from '@/stores/user';

/**
 * data:循环数据
 * handler:单个处理器
 * limit:限制请求数
 */
function threadPool(data: any, handler: any, limit: any) {
	const sequence = [].concat(data); // 对数组做一个拷贝
	let count = 0;
	const promises = [];

	function load() {
		if (sequence.length <= 0 || count > limit) {
			return;
		}
		count += 1;

		// eslint-disable-next-line consistent-return
		return handler(sequence.shift())
			.catch((err: any) => {
				console.error(err);
			})
			.then(() => {
				count -= 1;
			})
			.then(() => load());
	}

	for (let i = 0; i < limit && i < sequence.length; i++) {
		promises.push(load());
	}
	return Promise.all(promises);
}

/**
 *
 * @param status
 * @returns
 */

function getTradeStatusLabel(status: ITradeStatus, needStyle = true) {
	const name: Record<string, string> = {
		'WAIT_BUYER_PAY': '待付款',
		'SELLER_CONSIGNED_PART': '部分发货',
		'WAIT_SELLER_SEND_GOODS': '待发货',
		'WAIT_BUYER_CONFIRM_GOODS': '已发货',
		'TRADE_FINISHED': '交易成功',
		'TRADE_CLOSED': '已关闭',
		'TRADE_CLOSED_BY_TAOBAO': '已关闭',
		'TRADE_CANCELLED': '已取消',
	};

	return (
		needStyle ? (
			<span className={ `${status === 'WAIT_SELLER_SEND_GOODS' ? 'trade-status-success' : 'trade-status-error'} r-fw-700 r-fs-14` }>{name[status]}</span>
		) : (
			<span>{name[status]}</span>
		)

	);
}

const getOrderRefundStatus = (status: ITradeRefundStatus, simple?: boolean, order?: IOrders) => {
	const name: Record<string, string> = {
		'REFUND_SUCCESSED': '退款成功',
		'REFUND_ING': '售后处理中',
		'NOT_REFUND': '',
	};
	if (status && name[status] && simple) {
		return (
			<span className="trade-status-error r-fs-14">({name[status]})</span>
		);
	}

	if (status && name[status]) {
		return (
			<span className="r-ml-10 trade-status-error r-fw-700 r-fs-14">
				状态：{name[status]} {order?.refundNumber ? `已取消${order.refundNumber}件`:''} {order?.refundAmount ? `，已退${order.refundAmount}元`:''}（请注意是否要打印此宝贝的发货信息）
			</span>
		);
	} else {
		return '';
	}

};

/**
 *
 * @param name
 * @returns
 */
function filterUdbUdc(name: string): string {
	return name ? String(name).replace(/\\udb40|\\udc0b/g, '') : '';
}

/**
 *
 * @param k
 * @param v
 * @returns
 */
function getMemoSysFlag(k:string, v: number): string {
	if (k === 'red' && v === 0) {
		return 'mark_dotred';
	}

	if (k === 'red' && v === 1) {
		return 'mark_half_dotred';
	}
	if (k === 'red' && v === 2) {
		return 'mark_dotred_sel';
	}

	if (k === 'yellow' && v === 0) {
		return 'mark_dotyellow';
	}
	if (k === 'yellow' && v === 1) {
		return 'mark_half_dotyellow';
	}
	if (k === 'yellow' && v === 2) {
		return 'mark_dotyellow_sel';
	}

	if (k === 'green' && v === 0) {
		return 'mark_dotgreen';
	}
	if (k === 'green' && v === 1) {
		return 'mark_half_dotgreen';
	}
	if (k === 'green' && v === 2) {
		return 'mark_dotgreen_sel';
	}

	if (k === 'blue' && v === 0) {
		return 'mark_dotblue';
	}
	if (k === 'blue' && v === 1) {
		return 'mark_half_dotblue';
	}
	if (k === 'blue' && v === 2) {
		return 'mark_dotblue_sel';
	}

	if (k === 'purple' && v === 0) {
		return 'mark_dotpurple';
	}
	if (k === 'purple' && v === 1) {
		return 'mark_half_dotpurple';
	}
	if (k === 'purple' && v === 2) {
		return 'mark_dotpurple_sel';
	}
	return '';
}

/**
 * @description:
 * @param {string} flag
 * @param {boolean} isNeedGray
 * @return {*}
 */
function getMemoFlag(flag: string, isNeedGray?: boolean): string {
	let flags = [
		'mark_flaggray',
		'mark_flagred',
		'mark_flagyellow',
		'mark_flaggreen',
		'mark_flagblue',
		'mark_flagpurple'
	];

	if (isNeedGray) {
		flags[0] = 'mark_flaggray';
	}

	return flags[+flag];
}

/**
 * @description:
 * @param {string} str
 * @param {string} target
 * @return {*}
 */
function lineBreak(str: string, target = ' ') {
	return (str || '').replace(/\n/g, target);
}
/**
 * @description:
 * @param {string} a
 * @param {string} b
 * @return {*}
 */
function calcPlus(num1: string, num2: string): number {
	let a = parseFloat(num1);
	let b = parseFloat(num2);
	if (Number.isNaN(a) && Number.isNaN(b)) {
		return 0;
	} if (Number.isNaN(a) && Number.isNaN(b)) {
		return Number.isNaN(a) ? b : a;
	}
	return a + b;
}
/**
 * @description:
 * @param {string} str
 * @return {*}
 */
function whitespace(str: string): string {
	return (str || '').replace(/ /g, '&nbsp;');
}
/**
 * @description:
 * @param {string} status
 * @return {*}
 */
function getPddRefundStatus(status: string): string {
	switch (status) {
		case '0':
			return '异常订单';
		case '1':
			return '无售后或售后关闭';
		case '2':
			return '售后处理中';
		case '3':
			return '退款中';
		case '4':
			return '退款成功';
		default:
			return '其他状态';
	}
}

function filterAddrDetail(pack: Partial<IPackage>, key?:string) {
	let addr = key ? (pack?.[key] || pack?.receiverAddressMask || '') : (pack?.receiverAddressMask || '');

	addr = addr.replace(pack.receiverState + pack.receiverCity + pack.receiverDistrict, '');
	return addr;
}

/**
     * 根据子订单汇总出来此订单的物流公司和单号信息
     * @param orders
     * return 字符串，物流公司和单号 物流公司1：1232，12312；物流公司2：12323，12323
     */
function getTradeLogisticInfo(orders: IOrders[]) {
	let info = {} as any;
	let names = [] as string[];
	let results = [] as string[];

	orders.forEach((order) => {
		let kdName = order.kdName;

		if (!kdName) {
			return;
		}

		if (!info[kdName]) {
			info[kdName] = [];
			names.push(kdName);
		}

		info[kdName].push(order.ydNo);
	});

	names.forEach(function(v, k) {
		results.push(`${v}:${uniqueArray(info[v]).join(',')}`);
	});

	return results.join(';');
}

// 数组去重, 仅支持数字和字符串
function uniqueArray(arr: []) {
	let obj = {} as any;
	let newArr = [] as any;
	let temp;

	for (let i = 0, j = arr.length; i < j; i++) {
		temp = arr[i];
		if (!obj[temp]) {
			newArr.push(temp);
			obj[temp] = true;
		}
	}

	return newArr;
}

function imgUrl(str = '') {
	return str.replace(/http[s]{0,1}:\/\/[^\\.]*\.taobaocdn\.com/img, '//img.alicdn.com');
}

const packageEditableKeyArr = [
	'receiverName',
	'receiverNameMask',
	'receiverMobile',
	'receiverMobileMask',
	'receiverAddress',
	'receiverAddressMask',
	'receiverZip',
	'receiverState',
	'receiverCity',
	'receiverDistrict',
	'receiverTown'
];

const tradeEditableKeyArr = [
	'sysMemo',
	'buyerMessage',
	'sellerMemo',
	'sellerMemoFlag'
];

// pack是否不能勾选
// isLimited受限订单， pOrderUnfinished拼单未完成， platformStockOut平台缺货, 快团团无需发货
export const isPackCheckDisable = (pack: IPackage) => {
	return pack.isLimited || pack.pOrderUnfinished || pack.platformStockOut || pack?.serviceTagList?.includes('wxfh');
};
export enum TradeToPackEnum {
	'isFlashBuyingProducts' = '闪电购商品',
	'isSmallStoreSelfSelling' = '小店自卖',
	'isSelectedAlliance' = '精选联盟',
	'isCod' = '货到付款',
}
/**
 *	处理订单接口数据
 *	订单打印、爆款标签。。。
 * @param {IPackage[]} list
 * @param {boolean} [isCalcAbnormalAddress=false]
 * @param {TradeQueryTradeRequest} [searchParams=null]
 * @param {boolean} [generatorPrintContent=true]
 * @param {*} [otherParams] 其他的参数都放这里
 * @return {*}
 */
const handlePackageList = async(list: IPackage[], isCalcAbnormalAddress = false, searchParams: TradeQueryTradeRequest = null, generatorPrintContent: boolean = true, otherParams:any = {}) => {
	let abnormalAddressCount = 0;
	let printContent : TradePrintContentSetRule = null;
	let keyWords:string[] = null;
	let advancedSetRes;
	try {
		printContent = await tradeSetStore.getPrintContentSet();
		const filterWord = await tradeSetStore.getFilterWord();
		keyWords = filterWord;
		advancedSetRes = await memoFn.getAdvancedSet();
	} catch (error) {
		console.error('获取发货内容设置失败', error);
	}
	runInAction(() => {
		list = list.map((pack, index) => {
			// * 自定义字段初始化
			// * 当pack上的自定义字段是通过order/trade推断出来的时候 拆单时子单会继承主单的自定义字段
			packInitialFieldsMap.forEach(fields => {
				pack[fields.field] = fields.initialVal;
			});
			calcHidePack(pack, searchParams);
			pack.abnormalAddress && abnormalAddressCount++;
			pack.occupiedStockStatus = '';
			pack.isChecked = false;
			pack.isTotalPostFee = +pack.totalPostFee > 0;
			let productOrders: IOrders[] = [];
			let isEveryOrderHasSysSkuId = true;
			// pack.isVillagesTowns = false;
			packageEditableKeyArr.forEach(key => {
				pack[`_${key}`] = pack[key];
			});
			if (pack.riskControlStatus === '1') {
				pack.isRiskControl = true;
			}
			// pack.isSysExchanged = pack.trades.some(trade => trade.orders.some(order => order.sysExchangeType && order.sysExchangeType !== SysExchangeTypeEnum.线上改商品));
			// pack.isOnlineExchanged = pack.trades.some(trade => trade.orders.some(order => order.sysExchangeType && order.sysExchangeType == SysExchangeTypeEnum.线上改商品));
			pack.isSysExchanged = pack?.serviceTagList?.includes('sysChangeItem') || false;
			pack.isOnlineExchanged = pack?.serviceTagList?.includes('ptChangeItem') || false;
			pack.hasGift = pack.trades.some(trade => trade.orders.some(order => order.isGift) || trade.giftOrders?.length);
			pack.isAllDropShipping = pack.trades.every(trade => trade.orders.every(order => order.dropShipping));
			pack.needSerialNumber = pack.trades.some(trade => trade.orders.some(order => order.needSerialNumber));
			pack.hasSfExpressService = pack.trades.some(trade => trade.hasSfExpressService);
			pack.hasDeliveryOnDoor = pack.trades.some(trade => trade.hasDeliveryOnDoor);
			// 顺丰加价运费求和
			pack.sfExpressFee = pack.trades.reduce((a, c) => a + Number(c.sfExpressFee || 0), 0);
			pack._giftsNum = 0;
			// if (pack.platform === 'ali' && !pack.hlPlatformType) {
			// 	pack.hlPlatformType = 'hl-fxg';
			// }
			try {
				pack.trades.forEach((trade, tradeIndex) => {
					// if (trade.epidemicMark === undefined) {
					// 	trade.epidemicMark = pack.epidemicMark;
					// }
					Object.keys(TradeToPackEnum).forEach(key => {
						if (trade[key]) {
							pack[key] = true;
						}
					});
					if (trade.appointmentArrival) {
						pack.appointmentArrival = true;
					}
					if (trade.promiseDeliveryTime) {
						pack.promiseDeliveryTime = true;
					}
					if (trade.wrapperDescription) {
						pack.wrapperDescription = true;
					}
					if (trade.duoduoWholesale) {
						pack.duoduoWholesale = true;
					}
					if (trade.shipHold) {
						pack.shipHold = true;
					}
					if (trade.freeSF) {
						pack.freeSF = true;
					}
					if (trade.isMendian) {
						pack.isMendian = true;
					}
					if (trade.shipHold) {
						pack.isLimited = true;
					}
					if (trade.changeAdderFlag) {
						pack.changeAdderFlag = true;
					}

					if (trade.platform === PLAT_JD) {
						pack.jdOrderShipType = Number(trade.jdOrderShipType);
						pack.jdStoreOrderType = Number(trade.jdStoreOrderType);
						pack.jdjp = trade.jdOrderShipType === JDOrderShipType.京配;
						pack.jdjc = trade.jdStoreOrderType === JDStoreOrderType.京仓订单;
						pack.jdyc = trade.jdStoreOrderType === JDStoreOrderType.云仓订单;
					}

					let hasOrderChecked = false;
					let hasGiftOrder = false;
					let noGoods = false;
					let isCheckedStock = isCheckStock({ ...trade, _sellerMemoFlag: trade.sellerMemoFlag });
					trade.orders.forEach((order, orderIndex) => {
						order.orderIndex = orderIndex;
						order.tradeIndex = tradeIndex;
						order.isCheckedStock = isCheckedStock;
						if (order.occupiedStockStatus === OccupyTradeOptType.释放 || !order.occupiedStockStatus) {
							pack.occupiedStockStatus = OccupyTradeOptType.释放;
						} else if (!pack.occupiedStockStatus) {
							pack.occupiedStockStatus = OccupyTradeOptType.占用;
						}
						!order.isHideByPartShip && !order.isHideFinishedGoods && productOrders.push(order);
						if (order.isGift) {
							hasGiftOrder = true;
							pack._giftsNum += +order.num;
						}

						// 商品是否勾选：交易关闭、退款中、退款完成的不勾选
						if (['TRADE_CLOSED', 'TRADE_CLOSED_BY_TAOBAO'].includes(order.status) || ['REFUND_SUCCESSED', 'REFUND_ING'].includes(order.refundStatus)) {
							order.isChecked = false;
						} else if (['WAIT_SELLER_SEND_GOODS'].includes(searchParams?.status) && (['WAIT_BUYER_CONFIRM_GOODS', 'TRADE_FINISHED'].includes(pack.status)) && (order.status === 'WAIT_BUYER_CONFIRM_GOODS' || order.status === 'TRADE_FINISHED')) {
							hasOrderChecked = true; // 待发货查询出已发货默认勾选已发货和交易成功商品
							order.isChecked = true;
						} else if (['WAIT_SELLER_SEND_GOODS', 'ALL_STATUS'].includes(searchParams?.status) && ((order.status === 'WAIT_BUYER_CONFIRM_GOODS' && !order.firstSend) || order.status === 'TRADE_FINISHED')) {
							order.isChecked = false; // 待发货、已完成
						} else if (order.dropShipping) {
							order.isChecked = false;
						} else {
							hasOrderChecked = true;
							order.isChecked = true;
						}

						// 爆款标签：爆款打印已发货、交易成功、交易关闭（已关闭、已取消）、退款中、退款完成的取消勾选
						if (otherParams?.isHotGoodsScanPrint && order?.isChecked) {
							if ((order.status === 'WAIT_BUYER_CONFIRM_GOODS' && !order.firstSend) || order.status === 'TRADE_FINISHED' || ['TRADE_CLOSED', 'TRADE_CLOSED_BY_TAOBAO'].includes(order.status) || ['REFUND_SUCCESSED', 'REFUND_ING'].includes(order.refundStatus)) {
								order.isChecked = false;
							}
						}

						if (order.noGoods) noGoods = true;

						if (!order.systemSkuId) {
							isEveryOrderHasSysSkuId = false;
						}

						if (order.isPreSale) {
							pack.isPreSale = true;
						}
						if (order.isStockOut == 1) {
							pack.platformStockOut = true;
						}

						if (order.sendType === SendType.重新发货) {
							pack.sendType = SendType.重新发货;
						}

					});
					if (trade.status === 'SELLER_CONSIGNED_PART') {
						pack.isPartShipped = true;
					}
					trade.noGoods = noGoods;
					tradeEditableKeyArr.forEach(key => {
						trade[`_${key}`] = trade[key];
					});

					// * 赠品处理 orders中有赠品了不再处理
					if (!hasGiftOrder) {
						trade?.giftOrders?.forEach((giftOrder, giftOrderIndex) => {
							giftOrder.orderIndex = trade.orders.length + giftOrderIndex;
							giftOrder.isChecked = hasOrderChecked;
							giftOrder.isGift = true;
							pack._giftsNum += +giftOrder.num
						});
						trade.orders = trade.orders.concat(trade.giftOrders);
						productOrders = productOrders.concat(trade.giftOrders);
					}
					if (!pack._sellerMemo) pack._sellerMemo = "";
					pack._sellerMemo += trade.sellerMemo || "";
					if (!pack._sysMemo) pack._sysMemo = "";
					pack._sysMemo += trade.sysMemo || "";
				});
			} catch (error) {
				console.error(error, ' handle package list');
			}

			if (pack.isMerge) {
				pack.mergeTradeCount = pack.trades.length;
			}
			calcPackTotalNum(pack);

			pack.productOrders = productOrders;
			try {
				if (!generatorPrintContent && pack.trades[0].shipInfo) {
					pack.printContent = pack.trades[0].shipInfo;
				} else {
					pack.printContent = genePrintContent(pack, printContent, keyWords, advancedSetRes);
				}

			} catch (error) {
				console.error(error);
			}
			pack.isEveryOrderHasSysSkuId = isEveryOrderHasSysSkuId;

			if (pack.riskControlStatus === '1') {
				pack.isLimited = true;
			}
			return pack;
		});
	});

	if (isCalcAbnormalAddress) {
		const { setAbnormalAddressCount } = tradeStore;
		setAbnormalAddressCount(abnormalAddressCount);
	}
	console.log(list, 'liost');
	// tradeStore.tradeListStore.setDiffStyleNumMap({ singleItemNum, singleStyleMultiNum, multiStyleMultiNum });
	return list;
};

const calcHidePack = (pack:IPackage, searchParams: TradeQueryTradeRequest) => {
	const { setting } = tradeStore;
	// 精确查询不进入统计
	// console.log('searchParams', searchParams);
	if (!searchParams?.ptTid && !searchParams?.tid) {
		// * 订单同时挂起和已退款 优先统计到挂起 后端统计
		if (setting.isHidePendingTrade === 2) {
			pack.isHideByPending = pack.isPending;
		}

		// 隐藏生成拣货波次订单
        if (setting?.printSetExpandDTO?.hideWaveTrade === 2) {
			// 然后检查每个trade是否有波次号
			const hasTradeWave = pack.trades.every(trade =>
				Array.isArray(trade.waveNoList) && trade.waveNoList.length > 0
			);
            pack.isHideByWave = hasTradeWave
        }

		if (setting.isHideRefundTrade === 2 && !pack.isHideByPending) {
			if (pack.trades.every(trade => trade.orders.every(order => ['REFUND_SUCCESSED', 'REFUND_ING'].includes(order.refundStatus)))) {
				pack.isHideByRefund = true;
			}
		}

		if (setting.groupPrintSetJsonString?.isHideAllDropShipTrade === 2 && !pack.isHideByPending) {
			if (pack.trades.every(trade => trade.orders.every(order => order.dropShipping))) {
				pack.isHideByDropShip = true;
			}
		}

		if (!(setting.isHidePartShipTrade === 2
			|| setting.isHideTradeCompletionGoods === 2
			|| setting.groupPrintSetJsonString.isHideClosedGoods === 2
			|| setting.groupPrintSetJsonString.isHideDropShippingGoods === 2
			|| setting.groupPrintSetJsonString.isHideRefundingGoods === 2)
		) {
			// 如果高级设置中没有开启商品隐藏设置 不进入下面的统计
			return;
		}
		// ['WAIT_SELLER_SEND_GOODS', 'ALL_STATUS'] 去掉对于查询条件为全部状态的判断
		if (['WAIT_SELLER_SEND_GOODS'].includes(searchParams?.status)) {
			// * 隐藏订单下已发货的子订单
			pack.trades.forEach(trade => {
				trade.hideOrderByPartShipCount = 0;
				trade.hideOrderByTradeFinished = 0;
				trade.hideOrderByClosedGoods = 0;
				trade.hideOrderByRefundingGoods = 0;
				trade.hideOrderByDropShippingGoods = 0;

				trade.orders.forEach(order => {
					// 隐藏已发货的商品
					if (setting.isHidePartShipTrade === 2 && order.status === 'WAIT_BUYER_CONFIRM_GOODS') {
						order.isHideByPartShip = true;
						trade.hideOrderByPartShipCount++;
						trade.hidePartShipOrder = true;
					} else if (setting.isHideTradeCompletionGoods === 2 && order.status === 'TRADE_FINISHED') {
						// 隐藏交易完成的商品
						order.isHideFinishedGoods = true;
						trade.hideOrderByTradeFinished++;
						trade.isHideFinishedGoods = true;
					} else if (setting.groupPrintSetJsonString.isHideClosedGoods === 2 && (order.status === 'TRADE_CLOSED' || order.status === 'TRADE_CLOSED_BY_TAOBAO')) {
						// 隐藏已关闭的商品
						order.isHideClosedGoods = true;
						trade.hideOrderByClosedGoods++;
						trade.isHideClosedGoods = true;
					} else if (setting.groupPrintSetJsonString.isHideRefundingGoods === 2 && order.refundStatus === 'REFUND_ING') {
						// 隐藏退款中的商品
						order.isHideRefundingGoods = true;
						trade.hideOrderByRefundingGoods++;
						trade.isHideRefundingGoods = true;
					} else if (setting.groupPrintSetJsonString.isHideDropShippingGoods === 2 && order.dropShipping) {
						// 隐藏代发的商品
						order.isHideDropShippingGoods = true;
						trade.hideOrderByDropShippingGoods++;
						trade.isHideDropShippingGoods = true;
					}
				});
			});
		}
	}
};

// 新增合并key生成函数
function getSkuMergeKeyByRule(order, skuMerge, showColumn) {
	let skuId = order.skuId;
	if (showColumn == 2 && order.systemSkuId) { // 本地商品
		skuId = order.systemSkuId;
	}
	let shortName = order.titleShort || order.title || '';
	if (showColumn === 1 && skuMerge === 2) {
		return `${shortName}__${order.skuPropertiesName || ''}`;
	}
	if (showColumn === 1 && skuMerge === 3) {
		return `${shortName}__${order.skuAlias || ''}`;
	}
	// 默认
	return skuId;
}

const calcFilterOrder = (order: IOrders, filterOrderObj: IFilterOrderObj, pack: IPackage) => {
	const { itemStockObj } = tradeStore.tradeListStore;
	const { filterOrderSetting } = tradeSetStore;
	let normalOrderCount = 0;
	let checkedCount = 0;
	let normalOrderCheckCount = 0;
	// * 此处不包含退款商品、已取消商品、挂起商品、隐藏商品、受限、风控订单
	if (
		!pack.isPending && order.isChecked
		&& !isPackCheckDisable(pack)
		&& !['TRADE_CLOSED', 'TRADE_CLOSED_BY_TAOBAO'].includes(order.status)
		&& !['REFUND_SUCCESSED', 'REFUND_ING'].includes(order.refundStatus)
		&& pack.riskControlStatus != '1'
	) {
		normalOrderCount += +order.num;
		if (pack.isChecked && order.isChecked) {
			normalOrderCheckCount += +order.num;
		}
	}

	if (pack.isChecked && order.isChecked) {
		checkedCount += +order.num;
	}

	// 用合并key
	const skuMerge = filterOrderSetting?.jsonConfig?.skuMerge || 1;
	const showColumn = filterOrderSetting?.showColumn;
	const orderKey = getSkuMergeKeyByRule(order, skuMerge, showColumn);

	if (filterOrderObj[orderKey]) {
		filterOrderObj[orderKey].totalCount += +order.num;
		filterOrderObj[orderKey].normalOrderTotalCount += normalOrderCount;
		filterOrderObj[orderKey].checkedCount += checkedCount;
		filterOrderObj[orderKey].normalOrderCheckCount += normalOrderCheckCount;
		filterOrderObj[orderKey].stockNum = itemStockObj[`${order.systemNumIid}_${order.systemSkuId}`] || 0;
		if (filterOrderObj[orderKey].order.num < order.num) {
			filterOrderObj[orderKey].order = {
				...filterOrderObj[orderKey].order || {},
				...order
			};
		}
	} else {
		filterOrderObj[orderKey] = {
			order: { ...order },
			checkedCount,
			normalOrderCheckCount,
			totalCount: +order.num,
			normalOrderTotalCount: normalOrderCount,
			checkStatus: '',
			stockNum: itemStockObj[`${order.systemNumIid}_${order.systemSkuId}`] || 0
		};
	}
};

export const calcPackTotalNum = (pack: IPackage) => {
	pack.hasGift = pack?.hasGift ?? pack?.trades?.some(trade => trade?.orders?.some(order => order?.isGift) || trade?.giftOrders?.length);
	pack.goodsIdObj = {};
	pack.totalOrderNum = 0;
	let giftsNum = 0;
	pack.trades.forEach(trade => {
		trade.orders.forEach(order => {
			if (order?.isChecked) {
				pack.totalOrderNum += (+order.num || 1);
				pack.goodsIdObj[order.numIid] = true;

				if(order?.isGift){
					giftsNum += (+order.num || 1);
				}
			}
		});
	});
	pack._noContainGiftsNum = pack.totalOrderNum;
	if (pack?.hasGift) pack._noContainGiftsNum = pack.totalOrderNum - giftsNum; // 这里只能减去勾选的赠品数量
	// console.log(pack.totalOrderNum)
	// console.log(pack._giftsNum)
	// console.log(pack._noContainGiftsNum)
	pack.totalGoodsNum = Object.keys(pack.goodsIdObj).length;
};



const getTradePlatformLabel = (platform: string = 'hand', autoSizeNum?: number) => {
	let newPlatform = platform?.toLocaleLowerCase() || '';
	if (newPlatform === PLAT_ALI) {
		return <Icon style={ { flexShrink: 0 } } svg size={ autoSizeNum || 22 } className={ `r-mr-6 ${autoSizeNum ? 'r-pt-4' : ''}` } type="a-1688biaoshi" />;
	} else {
		return <span className={ `${LabelPlatformMap[newPlatform]?.className} ${autoSizeNum ? 'autoSize' : ''}` }>{LabelPlatformMap[newPlatform]?.text}</span>;
	}
};

// 昵称中 手工单显示 手 其他店铺显示 其他
const getNickPlatformIcon = ({ platform = 'hand', autoSizeNum, source }:{platform: platform, autoSizeNum?: number, source?: string}) => {
	if (isSourceScm({ source })) {
		return <span className={ `${LabelPlatformMap[PLAT_SCM]?.className} ${autoSizeNum ? 'autoSize' : ''}` }>{LabelPlatformMap[PLAT_SCM]?.text}</span>;
	}
	if (isSourceHand({ source, platform }) || source === AFTERSALE_HANDORDER) {
		return <span className={ `${LabelPlatformMap[PLAT_HAND]?.className} ${autoSizeNum ? 'autoSize' : ''}` }>{LabelPlatformMap[PLAT_HAND_OLD]?.text}</span>;
	}
	if (platform === PLAT_ALI) {
		return <Icon style={ { flexShrink: 0 } } svg size={ autoSizeNum || 22 } className={ `r-mr-6 ${autoSizeNum ? 'r-pt-4' : ''}` } type="a-1688biaoshi" />;
	} else {
		return <span className={ `${LabelPlatformMap[platform]?.className} ${autoSizeNum ? 'autoSize' : ''}` }>{LabelPlatformMap[platform]?.text}</span>;
	}
};

const getVirtualImgUrl = (platform: string) => {
	if (platform === PLAT_PDD) {
		return PDD_VIRTUAL_IMG;
	} else if (platform === PLAT_TB) {
		return 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/kdzstb/img/virtual-mobile.png';
	}
	return '';
};

const copyMenu = (copyFn: any) => (
	<Menu style={ { width: 'fit-content' } }>
		<Menu.Item onClick={ copyFn }>复制</Menu.Item>
	</Menu>
);

// 获取旗帜icon
const getTradeFlag = (tradeIndex: number, flagClickFn: any, flagIndex = "", getAllFlag?: boolean,platform?: string) => {
	if (`${flagIndex}`) {
		return (
			<span className="r-pointer" onClick={ () => flagClickFn && flagClickFn(tradeIndex) }>
				<Icon type="flag1" style={ { color: allFlagArr?.[+flagIndex]?.color } } />
			</span>
		);
	} else if (getAllFlag) {
		// 这里是订单打印详情里面的编辑旗帜，暂时不开放所有旗帜
		return (
			<>
				{(platform==PLAT_DW? dwflagArr : [PLAT_TB, PLAT_TM].includes(platform) ? allFlagArr : flagArr).map((item, index) => (
					<Tooltip title={ item.name } key={ item.color }>
							<span key={ item.color } className="r-pointer r-mr-5" onClick={ () => { flagClickFn && flagClickFn(tradeIndex, index); } }>
								<Icon type="flag1" style={ { color: item.color } } />
							</span>
						</Tooltip>
				))}
			</>
		);
	}

};

// 获取旗帜标签
const getTradeFlagTag = (sellerFlag: string, sellerFlagTag: string, classObj = {}) => {
	if (`${sellerFlag}` && sellerFlagTag) {
		return (
			<span className="r-pointer seller_flag_tag" style={ { ...classObj } }>{sellerFlagTag}</span>
		);
	}
	return '';
};


const noGoodsLinkWarning = () => {
	Modal.warning({
		centered: true,
		title: '系统提示',
		content: (
			<div className="r-c-warning">该订单已被锁定，不能进行该操作，请先处理异常</div>
		),
		okText: '我知道了'
	});
};

const confirmWarning = (text:string) => {
	Modal.warning({
		centered: true,
		title: '系统提示',
		content: (
			<div className="r-c-warning">{text}</div>
		),
		okText: '我知道了'
	});
};

const calcBizMark = (sellerFlagSys: any) => {
	// 红黄绿蓝紫
	return `${sellerFlagSys.red ? 1 : 0}${sellerFlagSys.yellow ? 1 : 0}${sellerFlagSys.green ? 1 : 0}${sellerFlagSys.blue ? 1 : 0}${sellerFlagSys.purple ? 1 : 0}`;
};

// reg: 限制两位小数或者正整数
const calcInputVal = (val: string, reg: 'decimal' | 'integer', decimalNum = 2):string => {
	let regArr = {
		decimal: new RegExp(`^\\d+$|^\\d+\\.\\d{0,${decimalNum}}$`), // /^\d+$|^\d+\.\d{0,2}$/,
		integer: /^\d+$/
	};
	let res = '';

	const func = (value: string) => {
		if (!regArr[reg].test(value)) {
			res = value.trim().substring(0, value.length - 1);
			if (res.length) {
				func(res);
			} else {
				return res;
			}
		} else {
			res = value;
		}

		return res;
	};
	return func(val + '');
};

const decryptRewrite = (pack: IPackage, decryptInfo: any) => {
	if (!decryptInfo || isEmpty(decryptInfo)) return;
	let address = decryptInfo.receiverAddress;
	if (pack.platform === PLAT_JD && !isSourceHand(pack)) {
		let _spliceAdd = pack.receiverState + pack.receiverCity + pack.receiverDistrict;
		let _index = address.indexOf(_spliceAdd);
		if (_index > -1) {
			address = address.replace(`/${_spliceAdd}/`, "");
		}
	}
	let res:Obj = {};

	if ([PLAT_FXG, PLAT_TB, PLAT_TM].includes(pack.platform) && !isSourceHand(pack) && !(pack?.source === AFTERSALE_HANDORDER && pack?.trades?.[0]?.hasDecrypt)) {
		let addrKey = `${DecryptFiledEnum['地址']}IsDecrypt`;
		let mobileKey = `${DecryptFiledEnum['手机号']}IsDecrypt`;
		let nameKey = `${DecryptFiledEnum['收件人']}IsDecrypt`;
		res[addrKey] = decryptInfo[addrKey] || pack[addrKey];
		res[mobileKey] = decryptInfo[mobileKey] || pack[mobileKey];
		res[nameKey] = decryptInfo[nameKey] || pack[nameKey];
		res["secret_no_expire_time"] = decryptInfo["secret_no_expire_time"] || "";

		decryptInfo = {
			receiverName: res[nameKey] || "",
			receiverPhone: res[mobileKey] || "",
			receiverAddress: res[addrKey] || ""
		};
		let isDecrypted = !!(res[addrKey] && res[mobileKey] && res[nameKey]);
		address = decryptInfo.receiverAddress ? `${pack.receiverTown || ''}${decryptInfo.receiverAddress}` : "";
		res = {
			...res,
			isDecrypted,
			isVirtualNum: decryptInfo.isVirtualNum || 0,
			receiverPhoneMask: '',
			receiverPhone: '',
			receiverNameMask: decryptInfo.receiverName || pack.receiverNameMask,
			receiverName: decryptInfo.receiverName || pack.receiverName,
			receiverMobile: decryptInfo.receiverPhone || pack.receiverMobile,
			receiverMobileMask: decryptInfo.receiverPhone || pack.receiverMobileMask,
			receiverAddressMask: address || pack.receiverAddressMask,
			receiverAddress: address || pack.receiverAddress,
			_receiverNameMask: decryptInfo.receiverName || pack.receiverNameMask,
			// _receiverName: decryptInfo.receiverName,
			// _receiverMobile: decryptInfo.receiverPhone,
			_receiverMobileMask: decryptInfo.receiverPhone || pack.receiverMobileMask,
			_receiverAddressMask: address || pack.receiverAddressMask,
			// _receiverAddress: address,
			idxEncodeReceiverName: decryptInfo.receiverName || pack.idxEncodeReceiverName,
			idxEncodeReceiverMobile: decryptInfo.receiverPhone || pack.idxEncodeReceiverMobile,
			idxEncodeReceiverAddress: address || pack.idxEncodeReceiverAddress,
			buyerNick: pack.platform === PLAT_FXG ? (decryptInfo.receiverName || pack.buyerNick) : pack.buyerNick,
			caid: pack.caid
		};
	} else {
		let buyerNick = pack.buyerNick;
		if(pack.platform === PLAT_JD && !isSourceHand(pack)){
			buyerNick = decryptInfo.buyerNick;
		} else if(pack.platform === PLAT_KTT){
			buyerNick = decryptInfo.buyerNick;
		} else if(![PLAT_TB, PLAT_ALI].includes(pack.platform)){
			buyerNick = decryptInfo.receiverName || pack.buyerNick;
		} else {
			buyerNick = pack.buyerNick;
		}
		res = {
			isDecrypted: true,
			isVirtualNum: decryptInfo.isVirtualNum || 0,
			receiverPhoneMask: '',
			receiverPhone: '',
			receiverNameMask: decryptInfo.receiverName || pack.receiverNameMask,
			receiverName: decryptInfo.receiverName || pack.receiverName,
			receiverMobile: decryptInfo.receiverPhone || pack.receiverMobile,
			receiverMobileMask: decryptInfo.receiverPhone || pack.receiverMobileMask,
			receiverAddressMask: address || pack.receiverAddressMask,
			receiverAddress: address || pack.receiverAddress,
			_receiverNameMask: decryptInfo.receiverName || pack.receiverNameMask,
			// _receiverName: decryptInfo.receiverName,
			// _receiverMobile: decryptInfo.receiverPhone,
			_receiverMobileMask: decryptInfo.receiverPhone || pack.receiverMobileMask,
			_receiverAddressMask: address || pack.receiverAddressMask,
			// _receiverAddress: address,
			idxEncodeReceiverName: decryptInfo.receiverName || pack.idxEncodeReceiverName,
			idxEncodeReceiverMobile: decryptInfo.receiverPhone || pack.idxEncodeReceiverMobile,
			idxEncodeReceiverAddress: address || pack.idxEncodeReceiverAddress,
			buyerNick: buyerNick,
			caid: pack.platform === PLAT_TB && !isSourceHand(pack) ? '' : (pack.caid || ''),
		};
	}
	return res;
};

// 获取手工单的未加密信息（换货/补发订单放开限制）
const getDecryptInfoForHand = (pack: Partial<IPackage>) => {
	console.log('pack', pack);
	// 密文手工单不走手工单逻辑
	if (isSourceHand(pack) && !pack.serviceTagList.includes('ciphertextHandTrade')) {
		return {
			name: pack.receiverName,
			phone: pack.receiverMobile,
			address: filterAddrDetail(pack, 'receiverAddress'),
		};
	}
	return {
		name: pack.receiverNameMask,
		phone: pack.receiverMobileMask,
		address: pack.receiverAddressMask,
	};
};

const handleDecrypt = async(pack: Partial<IPackage>, type: DecryptFiledEnum[number] = DecryptFiledEnum['全部'], updatePackInfo = true) => {
	// if (pack.riskControlStatus === '1') return;
	if (FxgConsolidateType[pack.consolidateType]) {
		Modal.warning({
			centered: true,
			title: '系统提示',
			content: (
				<div className="r-c-warning">物流转运订单不支持解密，请到抖店商家后台查看处理</div>
			),
			okText: '我知道了'
		});
		return;
	}

	// 售后创建的手工单放开解密限制
	// if (pack.source === AFTERSALE_HANDORDER) {
	// 	message.warning('换货/补发订单暂不支持解密');
	// 	return;
	// }

	// 受限订单 不解密
	if (isSourceScm(pack)) {
		scmPackTips();
		return;
	}
	if (pack.isLimited) {
		limitedPackTips();
		return;
	}
	console.log('pack', pack);

	const { successObj } = await decryptFn({
		type,
		togetherId: pack.togetherId,
		platform: pack.platform,
		mallId: pack.sellerId,
		encryptObj: {
			name: pack.receiverName,
			phone: pack.receiverMobile,
			address: pack.receiverAddress, // receiverAddressMask
			buyerNick: pack.buyerNickOrigin || pack.buyerNick,
		},
		decryptArr: [{
			source: pack.source,
			userId: pack.distributorUserId,
			sellerId: pack.sellerId,
			platform: pack.platform,
			caid: pack.caid,
			tid: pack.encodeTid,
			ptTid: pack.encodePtTid || pack.encodeTid,
			sceneCode: 100,
			encodeReceiverPhone: pack.receiverPhone,
			encodeMobile: pack.receiverMobile,
			encodeReceiverName: pack.receiverName,
			encodeReceiverAddress: pack.receiverAddress,
		}],
		sellerNick: pack.sellerNick,
		isDecrypted: pack.isDecrypted,
		sellerId: pack.sellerId,
		pack,
	});
	let decryptInfo = successObj[pack.togetherId];
	console.log(decryptInfo, 'decryInfo');
	if (updatePackInfo) {
		const { tradeListStore: { getListByTogetherId, handleUpdateList } } = tradeStore;
		let prev = getListByTogetherId(pack.togetherId)[0];
		handleUpdateList({
			type: 'updatePackInfo',
			data: {
				togetherId: pack.togetherId,
				packInfo: {
					...prev,
					...decryptRewrite(prev, decryptInfo)
				},
			},
		});
	}
	return decryptInfo;
};

const handleReportDecrypt = async(pack: Partial<IPackage>, type: DecryptFiledEnum[number] = DecryptFiledEnum['全部']) => {
	const { successObj } = await decryptFn({
		type,
		togetherId: pack.togetherId,
		platform: pack.platform,
		mallId: pack.sellerId,
		encryptObj: {
			name: pack.receiverName,
			phone: pack.mobile,
			address: pack.receiverAddress,
			buyerNick: pack.buyerNickOrigin || pack.buyerNick,
		},
		decryptArr: [{
			sellerId: pack.sellerId,
			platform: pack.platform,
			caid: pack.caid,
			tid: pack.encodeTid,
			ptTid: pack.encodePtTid || pack.encodeTid,
			sceneCode: 100,
			encodeReceiverPhone: pack.receiverPhone,
			encodeMobile: pack.mobile,
			encodeReceiverName: pack.receiverName,
			encodeReceiverAddress: pack.receiverAddress,
		}],
		sellerNick: pack.sellerNick,
		sellerId: pack.sellerId,
		isDecrypted: pack.isDecrypted,
		pack
	});
	let decryptInfo = successObj[pack.togetherId];
	return decryptInfo;
};

const findTradeFromList = (pack1: IPackage, pack2: IPackage) => {
	return pack1.togetherId === pack2.togetherId && pack1.platform === pack2.platform;
};

const openWW = (nick: string) => {
	window.open(`//amos.alicdn.com/getcid.aw?site=cntaobao&uid=${nick}&charset=utf-8&status=1`);
};

// const epidemicAreaGuideModal = () => {
// 	Modal.info({
// 		title: '提示',
// 		content: '暂未开启疫情地区标识，请前往高级设置中开启此选项',
// 		okText: '前往高级设置',
// 		onOk: () => {
// 			local.set('epidemicAreaTooltip', true);
// 			events.emit('openAdvancedSetting');
// 		}
// 	});
// };


/**
 * 解析url参数
 * @param url url地址
 * @param selectKey 获取当前网址指定参数
 * @param filterKeys 过滤指定参数
 * @returns
 */
const getUrlData = (url?:string, selectKey?:string, filterKeys?:string) => {
	let obj = {};
	if (!url) {
		url = window.location.hash;
	}

	let url_l = url.split('?')[1];
	let url_ll = url_l?.split('&');

	url_ll?.forEach((item, idx) => {
		let key = item.split('=')[0];
		let val = item.split('=')[1];
		if (!filterKeys || filterKeys.indexOf(key) === -1) {
			obj[key] = val;
		}
	});
	if (selectKey && url_l?.indexOf(selectKey)) return obj[selectKey];
	return obj;
};

/**
 * 根据运单计算快递单号数量
 * @param ydNoSet
 * @returns
 */
const getMultiPackNum = (ydNoSet:Array<string>) => {
	let num = 0;
	ydNoSet.forEach((i:string) => {
		let arr = i.split(',');
		num += arr.length;
	});
	return num;
};
const isBeyondInput = (value:string = '', maxNum:number) => {
	value = value.replace(/，/g, ',');
	const len = value.split(',').length;
	return len > maxNum;
};

// 将arr按照num分隔成一个二维数组
const spArr = (arr:{}[], num:number = 1) => {
	if (num === 0) return arr;
	let newArr:Array<any> = [];
	for (let i = 0; i < arr.length;) {
		newArr.push(arr.slice(i, i += num));
	}
	return newArr;
};

// 返回距离今天天数 只计算到 年月日中日的维度 时间不计入
const diffTodayDays = (dateA:string) => {
	let _dateA = dayjs(dateA);
	let today = dayjs(new Date());
	let dateStr = dayjs(`${_dateA.year()}-${_dateA.month() + 1}-${_dateA.date()}`);
	let todayStr = dayjs(`${today.year()}-${today.month() + 1}-${today.date()}`);
	return todayStr.diff(dateStr, 'day');
};
const labelStatusSort = {
	STOCKOUT: 1,
	WAIT_SORT: 2,
	STOCKPILE: 3,
	ALREADY_SORT: 4,
	ARRIVAL_OF_GOODS: 5,
	REFUND: 6
};

interface UseLabelObj{selectList:ItemTakeGoodsLabelSelectWithPageResponse["data"]["list"], sameSupplierGoodsNumObj:any, sortSupplierKeyObj?: any}

const labelListSort = async({ selectList, sameSupplierGoodsNumObj, sortSupplierKeyObj = {} }:UseLabelObj) => {
	const { getScanPrintSetting } = scanPrintStore;
	const printSetting = await getScanPrintSetting();

	let isSortByStallRoute = printSetting?.useLabelStallRouteSort == 1; // 使用拿货路线

	let stallRoute = {} as any;
	if(isSortByStallRoute){
		const {
			stallStore: { getStallRouteListData,getLabelRouteObj },
		} = tradeSetStore;
		const res = await getStallRouteListData(true); // 重新获取拿货路线排序
		stallRoute = getLabelRouteObj(selectList);
	}
	console.log('%c [ 是否使用拿货路线 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', isSortByStallRoute)
	selectList.sort(function(a, b) {
		let supplierA = `${a.market || ""}-${a.stall || ""}-${a.supplierName || ""}`;
		let supplierB = `${b.market || ""}-${b.stall || ""}-${b.supplierName || ""}`;
		// 打印时根据备货单市场档口供应商进行分组
		if (Object.keys(sortSupplierKeyObj).length > 0) {
			supplierA = padStartTenNum(sortSupplierKeyObj[supplierA] || 0);
			supplierB = padStartTenNum(sortSupplierKeyObj[supplierB] || 0);
		}else if(isSortByStallRoute){
			// 无市场档口供应商, 拿货路线已设置顺序, 拿货路线未设置顺序
			supplierA = padStartTenNum(stallRoute[supplierA] || 0);
			supplierB = padStartTenNum(stallRoute[supplierB] || 0);
		}

		// 标签类型优先
		const preSortA = `${a.labelType}-${supplierA}`;
		const preSortB = `${b.labelType}-${supplierB}`;
		switch (printSetting.printLabelSort) {
			// 6 相同商家编码+规格编码排在一起
			case labelSortVal.商家编码和规格编码:
				a.sortName = `${preSortA}-${a.outerId || ""}-${a.skuOuterId || ""}`;
				b.sortName = `${preSortB}-${b.outerId || ""}-${b.skuOuterId || ""}`;
				break;
			// 2 相同简称+规格名称排在一起
			case labelSortVal.简称和规格名称:
				a.sortName = `${preSortA}-${a.sysItemAlias || ""}-${a.skuName}`;
				b.sortName = `${preSortB}-${b.sysItemAlias || ""}-${b.skuName}`;
				break;
			// 7 相同简称+货品规格编码排在一起
			case labelSortVal.简称和货品规格编码:
				a.sortName = `${preSortA}-${a.sysItemAlias || ""}-${a.sysSkuOuterId}`;
				b.sortName = `${preSortB}-${b.sysItemAlias || ""}-${b.sysSkuOuterId}`;
				break;
			// 3 先排所有多件标签再排单件标签
			case labelSortVal.先多件后单件:
				a.sortName = `${preSortA}-${padStartTenNum(100000 - (a.packageGoodsNum || 0))}-${a.skuOuterId || a.outerId}`;
				b.sortName = `${preSortB}-${padStartTenNum(100000 - (b.packageGoodsNum || 0))}-${b.skuOuterId || b.outerId}`;
				break;
			// 4 先排缺货再排正常
			case labelSortVal.先缺货再正常:
				a.sortName = `${preSortA}-${labelStatusSort[a.labelStatus] || 7}${a.labelStatus || ""}`;
				b.sortName = `${preSortB}-${labelStatusSort[b.labelStatus] || 7}${b.labelStatus || ""}`;
				break;
			// 5 同订单排在一起
			case labelSortVal.同订单商品排一起:
				a.sortName = `${preSortA}-${a.tids || ""}`;
				b.sortName = `${preSortB}-${b.tids || ""}`;
				break;
			// 8 自定义排序
			case labelSortVal.自定义:
				{
					const { sortNameA, sortNameB } = getCustomLabelSortObj({ sortObj: printSetting.customizeSort, a, b, sameSupplierGoodsNumObj });
					a.sortName = `${preSortA}-${sortNameA}`;
					b.sortName = `${preSortB}-${sortNameB}`;
				}
				break;
			default:
				// 1 相同平台商家编码+规格名称排在一起
				a.sortName = `${preSortA}-${a.outerId || ""}-${a.skuName || ""}`;
				b.sortName = `${preSortB}-${b.outerId || ""}-${b.skuName || ""}`;
				break;
		}
		let nameA = a.sortName;
		let nameB = b.sortName;

		if (nameA < nameB) {
			return -1;
		}
		if (nameA > nameB) {
			return 1;
		}
		return 0;
	});
	return selectList;
};
// 订单情况排序
const orderStatusSortFn = (orderStatusSort, a) => {
	switch (orderStatusSort) {
		// 先单件再多件
		case customSortVal.先单件再多件:
			return padStartTenNum(a.packageGoodsNum || 0);
		// 先多件再单件
		case customSortVal.先多件再单件:
			return padStartTenNum(1000000 - (a.packageGoodsNum || 0));
		// 先缺货再有货
		case customSortVal.先缺货再有货:
			return labelStatusSort[a.labelStatus] || 7;
		// 先正常再缺货
		case customSortVal.先正常再缺货:
			return String(10 - (labelStatusSort[a.labelStatus] || 7));

		// 已付款天数多到少
		case customSortVal.已付款天数多到少:
			return padStartTenNum(1000000 - (a.payDate || 0));

		// 已付款天数少到多
		case customSortVal.已付款天数少到多:
			return padStartTenNum(a.payDate || 0);
		default:
			return '';
	}
};
// 商品排序 规格排序
const goodsSortFn = (goodsSort, a) => {
	if (goodsSort) {
		return `${a[goodsSort]}`;
	}
	return '';
};
// 规格排序
const skuSortFn = (skuSort, a) => {
	if (skuSort) {
		if(skuSort == customSortVal.颜色尺码){
			// console.log('%c [ 颜色尺码 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', `${a['color'] || ""}-${a['size'] || ""}`)
			return `${a['color'] || ""}-${a?.['size']?.toUpperCase?.() || ""}`
		}
		return `${a[skuSort]}`;
	}
	return '';
};
// 数量由多到少排序
const goodsNumSortFn = (goodsNumSort, a, sameSupplierGoodsNumObj) => {
	switch (goodsNumSort) {
		case customSortVal.数量由多到少:
			return padStartTenNum(100000 - (sameSupplierGoodsNumObj[`${a.supplierName}_${a.sysSkuId || a.skuId}`] || 0));
		case customSortVal.数量由少到多:
			return padStartTenNum(sameSupplierGoodsNumObj[`${a.supplierName}_${a.sysSkuId || a.skuId}`] || 0);
		default:
			return '';
	}
};

const getSortName = ({ arrKeyRank, item, sortObj, sameSupplierGoodsNumObj }) => {
	const { orderStatusSort, goodsSort, skuSort, goodsNumSort } = sortObj;
	const sortNameMap: {[k:string]: any} = {};
	// 订单情况排序
	sortNameMap.orderStatusSort = orderStatusSortFn(orderStatusSort, item);
	// 商品排序 规格排序
	sortNameMap.goodsSort = goodsSortFn(goodsSort, item);
	// 规格排序
	sortNameMap.skuSort = skuSortFn(skuSort, item);
	// 数量由多到少排序
	sortNameMap.goodsNumSort = goodsNumSortFn(goodsNumSort, item, sameSupplierGoodsNumObj);
	return arrKeyRank.map(key => {
		return sortNameMap[key];
	}).filter(i => i).join('-');
};



const getCustomLabelSortObj = ({ sortObj, a, b, sameSupplierGoodsNumObj }) => {
	// console.log('%c [ sortObj ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', sortObj, a, b, sameSupplierGoodsNumObj)
	const sortIndexMap = sortObj?.sortIndexMap || defaultSortIndex;
	// 按顺序拼
	const arrKeyRank = Object.keys(sortIndexMap)?.sort((key1, key2) => {
		return sortIndexMap[key1] - sortIndexMap[key2];
	});
	// console.log('%c [ arrKeyRank ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', arrKeyRank)
	let _sortNameA = getSortName({ arrKeyRank, item: a, sortObj, sameSupplierGoodsNumObj });
	let _sortNameB = getSortName({ arrKeyRank, item: b, sortObj, sameSupplierGoodsNumObj });

	// console.log('%c [ _sortNameA ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', _sortNameA, _sortNameB)

	return { sortNameA: _sortNameA, sortNameB: _sortNameB };
};

// 将数字填充成10位字符串
const padStartTenNum = (num) => {
	return String(num).padStart(10, '0');
};

const limitedPackTips = () => {
	message.warning('受限订单暂不支持该操作');
};

export const scmPackTips = () => {
	message.warning('分销商订单不支持处理');
};

// 右键、更多用到的
export const getEditGoodsTip = (pack) => {
	let tip = '';
	if (userStore.isDistributorAccount) {
		tip = "暂不支持";
	} else if (isSourceScm(pack)) {
		tip = "分销推送订单不支持";
	} else if (pack.trades.some(trade => trade.orders.some(i => i.dropShipping))) {
		tip = "存在分销代发商品，暂不支持";
	} else if (pack.trades.some(trade => trade.orders.some(i => i.needSerialNumber))) {
		tip = "该商品属于特殊类目商品，暂不支持";
	} else if (pack.platform == PLAT_DW) {
		tip = "得物平台订单，暂不支持";
	}
	return tip;
};

export const getHandEditTip = (pack) => {
	let tip = '';
	if (isSourceScm(pack)) {
		tip = "分销推送订单不支持编辑";
	} else if (pack.trades.some(trade => trade.orders.some(i => i.dropShipping))) {
		tip = "代发订单不允许编辑";
	}
	return tip;
};

export const getDeleteOrderTip = (pack) => {
	let tip = '';
	if (isSourceScm(pack)) {
		tip = "分销推送订单不支持删除";
	} else if (pack.trades.some(trade => trade.orders.some(i => i.dropShipping))) {
		tip = "代发订单不允许删除，请撤回推单后再操作";
	}
	return tip;
};

const consolidateContent = (consolidateType, showType:'list'|'detail', trade?:object) => {
	const steps = [
		{
			name: '消费者下单',
			icon: SUBMIT_ORDER_IMG,
		},
		{
			name: '商家发货',
			icon: SEND_GOODS_IMG,
		},
		{
			name: '中转仓签收',
			icon: CONFIRM_ORDER_IMG,
		},
		{
			name: '中转仓发货',
			icon: SEND_GOODS_IMG,
		},
		{
			name: '消费者收货',
			icon: WAIT_ORDER_IMG,
		}
	];
	if (ConsolidateType[consolidateType]?.noTip) {
		return <span className="trade-gray-filled">{ConsolidateType[consolidateType].name}</span>;
	}
	return (
		<div style={ { display: 'inline-block' } }>
			<Popover
				overlayClassName={ s['consolidate-popover'] }
				// visible
				zIndex={ 9999 }
				title={ (
					<div
						className={ s['consolidate-title'] }
						onClick={ e => {
							e.stopPropagation();
							e?.domEvent?.stopPropagation();
						} }
					>
						<span className="r-fs-18 r-bold" hidden={ consolidateType != 3 }>
							西藏中转订单请
							<span className="r-bold" style={ { color: "#fd8105" } }>直接发货至中转仓库</span>
						</span>
						<span className="r-fs-18 r-bold" hidden={ consolidateType != 1 }>
							新疆中转订单请
							<span className="r-bold" style={ { color: "#fd8105" } }>直接发货至中转仓库</span>
						</span>
						<span className="r-fs-18 r-bold" hidden={ consolidateType != 0 }>集运订单请直接发货至集运仓库</span>
						<span>
							（详细信息请<a target="_blank" href={ ConsolidateType[consolidateType].course } className="kdzs-link-text" rel="noreferrer">查看课程</a>）
						</span>
					</div>
				) }
				content={ (
					<div
						className={ s['consolidate-container'] }
						onClick={ e => {
							e.stopPropagation();
							e?.domEvent?.stopPropagation();
						} }
					>
						{/* 香港集运说明 */}
						<div hidden={ consolidateType != 0 }>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">商家直接发货至集运仓库</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										订单收件人信息已替换为集运仓地址，打单发货时请
										<span style={ { color: "#fd8105" } }>复制完整的收货信息(包括地址后识别码)</span>
									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">集运仓库二次发货至消费者</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										集运仓入库后将重新打包，并将包裹转运至消费者实际地址
									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">消费者信息 / 中转状态 / 二段轨迹信息查看请移步至商家后台</div>
								</div>
								<div className="r-flex" hidden={ showType !== 'detail' }>
									<div className="description-icon" />
									<div className="description-content">
										若您需要查看消费者信息/中转状态 / 最新二段物流轨迹，请前往拼多多商家后台查看订单详情
										<a
											target="_blank"
											href={ `https://mms.pinduoduo.com/orders/detail?type=4399&sn=${trade?.tid}` }
											className="kdzs-link-text"
											rel="noreferrer"
										>查看订单详情
										</a>
									</div>
								</div>
							</div>
						</div>
						{/* 新疆集运说明 */}
						<div hidden={ consolidateType != 1 }>
							<div className="r-mb-8">
								<Alert
									message={ (
										<div>
											如果实际打印快递面单收件信息为
											<span style={ { color: "#fd8105" } }>新疆消费者地址</span>，
											请放心发往新疆，<span style={ { color: "#fd8105" } }>平台已补贴运费</span>。
											您与快递网点按照发西安价格结算，若网点按发新疆价格结算，请反馈至商家客服。
											<a
												target="_blank"
												href="https://mms.pinduoduo.com/other/questionnaire?surveyId=95526459282"
												className="kdzs-link-text"
												rel="noreferrer"
											>反馈入口
											</a>
										</div>
									) }
									type="warning"
									showIcon
								/>
							</div>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">中转流程</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										<div className={ s.step }>
											{
												steps.map((step, index) => (
													<div className="step-item">
														<div className="step-info">
															<div>
																<img src={ step.icon } alt="" />
															</div>
															<div>{step.name}</div>
														</div>
														<div className="step-icon" hidden={ index === steps.length - 1 }>
															<CaretRightOutlined />
														</div>
													</div>
												))
											}
										</div>

									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">商家只需发货至中转仓（仅需承担该段物流费用）</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										订单收货地址信息已替换为中转仓地址，打单发货时请复制完整的收货信息
										<span style={ { color: "#fd8105" } }>(包括地址后识别码)</span>
									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">中转仓收货后二次发货至消费者</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										中转仓收到商家包裹后将重新打包，使用新运单号，作为第二段物流直接发货至消费者实际地址
										<span style={ { color: "#fd8105" } }>(第二段物流运费由平台补贴承担)</span>
									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item" hidden={ showType !== 'detail' }>
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">消费者信息 / 中转状态 / 分段轨迹信息请至订单详情页面查看</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										若您需要查看消费者信息/中转状态 / 最新二段物流轨迹，请前往拼多多商家后台
										<a
											target="_blank"
											href={ `https://mms.pinduoduo.com/orders/detail?type=4399&sn=${trade?.tid}` }
											className="kdzs-link-text"
											rel="noreferrer"
										>查看订单详情
										</a>
									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">消费者咨询</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										消费者咨询时请优先确认包裹的物流状态，并告知消费者准确信息
									</div>
								</div>
							</div>

							<div className={ s["foot-container"] }>
								如遇第二段物流长时间未更新或其他问题，您可拨打（021）60683100进线咨询
							</div>
						</div>
						{/* 西藏中转说明 */}
						<div hidden={ consolidateType != 3 }>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">中转流程</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										<div className={ s.step }>
											{
												steps.map((step, index) => (
													<div className="step-item">
														<div className="step-info">
															<div>
																<img src={ step.icon } alt="" />
															</div>
															<div>{step.name}</div>
														</div>
														<div className="step-icon" hidden={ index === steps.length - 1 }>
															<CaretRightOutlined />
														</div>
													</div>
												))
											}
										</div>

									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">商家只需发货至中转仓（仅需承担该段物流费用）</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										订单收货地址信息已替换为中转仓地址，打单发货时请复制完整的收货信息
										<span style={ { color: "#fd8105" } }>(包括地址后识别码)</span>
									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">中转仓收货后二次发货至消费者</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										中转仓收到商家包裹后将重新打包，使用新运单号，作为第二段物流直接发货至消费者实际地址
										<span style={ { color: "#fd8105" } }>(第二段物流运费由平台补贴承担)</span>
									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item" hidden={ showType !== 'detail' }>
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">消费者信息 / 中转状态 / 分段轨迹信息请至订单详情页面查看</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										若您需要查看消费者信息/中转状态 / 最新二段物流轨迹，请前往拼多多商家后台
										<a
											target="_blank"
											href={ `https://mms.pinduoduo.com/orders/detail?type=4399&sn=${trade?.tid}` }
											className="kdzs-link-text"
											rel="noreferrer"
										>查看订单详情
										</a>
									</div>
								</div>
							</div>
							<div className="r-flex r-fd-c content-item">
								<div className="r-flex">
									<div className="title-icon">
										<CheckOutlined />
									</div>
									<div className="title-content">消费者咨询</div>
								</div>
								<div className="r-flex">
									<div className="description-icon" />
									<div className="description-content">
										消费者咨询时请优先确认包裹的物流状态，并告知消费者准确信息
									</div>
								</div>
							</div>

							<div className={ s["foot-container"] }>
								如遇第二段物流长时间未更新或其他问题，您可拨打（021）60683100进线咨询
							</div>
						</div>
					</div>

				) }
			>
				<span className="trade-gray-filled">{ConsolidateType[consolidateType].name}</span>
			</Popover>
		</div>
	);
};


export const getPackageDetailOrders = (trade: ISubTrade) => ((trade.hidePartShipOrder || trade.isHideFinishedGoods || trade.isHideClosedGoods || trade.isHideRefundingGoods || trade.isHideDropShippingGoods)
	? trade.orders.filter(order => !(order.isHideByPartShip || order.isHideFinishedGoods || order.isHideClosedGoods || order.isHideRefundingGoods || order.isHideDropShippingGoods))
	: trade.orders);

// 批打底部按钮处理
// 高级设置和订单底部操作中，对按钮进行新增按钮的添加，对不同类型用户做按钮删除
export const newBtnAddToPrintSet = (initBtn, optBtn, userStore) => {
	let { mainButton, moreButton, hiddenButton } = initBtn;
	let curButtonIdList = [...mainButton, ...moreButton, ...hiddenButton].map(btn => btn.id);
	// console.log('%c [ 原始数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', _.cloneDeep(mainButton), _.cloneDeep(moreButton))
	// 获取当前版本所有的本地配置, 排除分组的子级
	const allOptButtonIdMap = new Map<string, any>();
	const allOptButtonIds = Object?.values(optBtn)?.flat(2) || [];
	// console.log('%c [ 本地配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', allOptButtonIds)
	allOptButtonIds?.forEach((item:any)=>{
		allOptButtonIdMap.set(item.id, item)
	})

	// 如果新增的更多按钮都不在主按钮、隐藏按钮、更多按钮中，说明当前用户还没有更新过新按钮的配置，需要手动放到更多按钮中去
	const newBtnNotInIdList = (newBtnId) => {
		return !curButtonIdList.includes(newBtnId)
	};

	// 改变分组位置
	const changeIndexForGroup = (key:string, group:any)=>{
		let nIndex = moreButton.findIndex(i => i.id == key);
		if(~nIndex){
			moreButton.splice(nIndex, 1, group);
		}else{
			// console.log(`%c [ moreButton ${key} 没找到 ]: `, 'color: #bf2c9f; background: pink; font-size: 13px;', _.cloneDeep(moreButton))
			let mIndex = mainButton.findIndex(i => i.id == key);
			if(!~mIndex){ // 没有都放到更多里面
				moreButton.push(group);
			}else{
				mainButton.splice(mIndex, 1, group);
			}
		}
	}

	// 改变分组位置
	const changeIndexForGroupToMainButton = (key:string, group:any)=>{
		let nIndex = mainButton.findIndex(i => i.id == key);
		if(~nIndex){
			mainButton.splice(nIndex, 1, group);
		}else{
			// console.log(`%c [ mainButton ${key} 没找到 ]: `, 'color: #bf2c9f; background: pink; font-size: 13px;',_.cloneDeep(mainButton))
			let mIndex = moreButton.findIndex(i => i.id == key);
			if(!~mIndex){
				mainButton.push(group);
			}else{
				moreButton.splice(mIndex, 1, group);
			}
		}
	}

	optBtn.moreButton.forEach(newBtn => {
		// 这里改为只处理用户保存的自定义数据，新加的参数可以插入到具体位置
		if (newBtnNotInIdList(newBtn.id)) {
			// 原来逻辑有问题，如果换过分组不一定能找到插入的位置,所以平铺和更多都要处理
			// 找到分组的第一个数据的排序位置，把分组插入过去，分组的成员会在后面删除从而作为子菜单成员
			// 调整成员位置只能放在这里，只有新功能能插入位置，不要改变用户的排序数据

			if (newBtn.id == 'batchSplitTypes') { // 批量拆单插入第一个位置
				moreButton.unshift(newBtn);
			} else if(newBtn.id =='generatorTagTypes'){
				changeIndexForGroup('generatorTag', newBtn); // 生成标签/备货单分组，放到原生成标签位置
			} else if(newBtn.id =='noLogisticsSendTypes'){
				changeIndexForGroup('noLogisticsSend', newBtn); // 无需物流发货分组，放到原无需物流发货位置
			} else if(newBtn.id =='assignExpressTypes'){
				changeIndexForGroup('assignExpress', newBtn); // 设置快递分组，放到原指定快递模板位置
			} else if(newBtn.id =='batchModifyMemoTypes'){
				changeIndexForGroup('batchModifyMemo', newBtn); // 备注/旗帜分组，放到原批量备注位置
			} else if(newBtn.id =='batchPendingTypes'){
				changeIndexForGroup('batchPending', newBtn); // 挂起/标记分组，放到原批量挂起订单位置
			} else if(newBtn.id =='batchUpdateTradeTypes'){
				changeIndexForGroup('batchUpdateTrade', newBtn); // 更新/关闭订单分组，放到原批量更新订单位置
			} else if(newBtn.id =='batchResendTypes'){
				changeIndexForGroup('batchResend', newBtn); // 追加包裹/重新发货分组，放到原重新发货位置
			} else if(newBtn.id =='batchIgnoreAbnormalTypes'){
				changeIndexForGroup('batchIgnoreAbnormal', newBtn); // 批量忽略异常分组，放到原批量忽略异常位置
			} else {
				moreButton.push(newBtn); // 没有特殊要求的都放后面
			}
		}
	});

	curButtonIdList = [...mainButton, ...moreButton, ...hiddenButton].map(btn => btn.id);

	optBtn.mainButton.forEach(newBtn => {
		if (newBtnNotInIdList(newBtn.id)) {
			if(newBtn.id =='generatorTagTypes'){
				changeIndexForGroupToMainButton('generatorTag', newBtn); // 生成标签/备货单分组，放到原生成标签位置
			} else if(newBtn.id =='noLogisticsSendTypes'){
				changeIndexForGroupToMainButton('noLogisticsSend', newBtn); // 无需物流发货分组，放到原无需物流发货位置
			} else if(newBtn.id =='assignExpressTypes'){
				changeIndexForGroupToMainButton('assignExpress', newBtn); // 设置快递分组，放到原指定快递模板位置
			} else if(newBtn.id =='batchModifyMemoTypes'){
				changeIndexForGroupToMainButton('batchModifyMemo', newBtn); // 备注/旗帜分组，放到原批量备注位置
			} else if(newBtn.id =='batchPendingTypes'){
				changeIndexForGroupToMainButton('batchPending', newBtn); // 挂起/标记分组，放到原批量挂起订单位置
			} else if(newBtn.id =='batchUpdateTradeTypes'){
				changeIndexForGroupToMainButton('batchUpdateTrade', newBtn); // 更新/关闭订单分组，放到原批量更新订单位置
			} else if(newBtn.id =='batchResendTypes'){
				changeIndexForGroupToMainButton('batchResend', newBtn); // 追加包裹/重新发货分组，放到原重新发货位置
			} else if(newBtn.id =='batchIgnoreAbnormalTypes'){
				changeIndexForGroupToMainButton('batchIgnoreAbnormal', newBtn); // 批量忽略异常分组，放到原批量忽略异常位置
			} else {
				mainButton.push(newBtn);
			}
		}
	});

	// 过滤数据：这里可以过滤不是这个版本的、下线不需要的
	const filterBtn = (btnGroupName) => {
		initBtn[btnGroupName] = initBtn[btnGroupName].filter(i => {
			// 不想显示的，放在这里
			let allFilterId = [
				'batchFreedTradeStock',  // 批量释放库存
				'batchOccupyTradeStock', // 批量占用库存
				'scanSend', // 验单发货挪走了
				'importExpress', // 导入快递单号挪到中部菜单
				'batchImport', // 批量导入订单挪到中部菜单
			];
			// 分销商，不支持，生成标签和自动发货,
			if (userStore.isDistributorAccount) {
				allFilterId = [...allFilterId, 'preShip', 'generatorTag'];
			}
			// 免费版供应商用户不支持指定供应商发货
			if (userStore.isFreeSupplierAccount) {
				allFilterId.push('supplierSend');
			}
			// 没有批量拆单（关闭订单在分组里面）
			if (!userStore.isShowSplitMenu) {
				allFilterId.push('batchSplitTypes');
			}
			// 本地默认配置没有的功能都去掉，包含其他分支的配置在这个分支没有的功能
			if(!allOptButtonIdMap.has(i.id) && !allFilterId?.includes(i.id)){
				allFilterId.push(i.id);
			}


			return !allFilterId.includes(i.id);
		});
	};

	// 修改数据: 这里可以处理改名
	const changeBtn = (btnGroupName)=>{
		initBtn[btnGroupName] = initBtn?.[btnGroupName]?.map((item,index)=>{
			// if (item.name === "生成备货单") {
			// 	item.name = "批量生成备货单";
			// }
			if(allOptButtonIdMap.has(item.id)){
				let optItem = allOptButtonIdMap.get(item.id);

				// 带上本地的数据
				return {
					...optItem,
					...item,
					name: optItem.name, // 如果本地改名了，以本地的为主
					sort: index + 1
				}
			}
			return {
				...item,
				sort: index +1
			};
		})
	}

	// 处理平铺、收起、隐藏按钮（自定义按钮显示用到了）
	['mainButton','moreButton','hiddenButton'].forEach((btnGroupName)=>{
		filterBtn(btnGroupName);
		changeBtn(btnGroupName);
	})

	allOptButtonIdMap.clear();

	// console.log('%c [ initBtn ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', _.cloneDeep(initBtn))
};

export const getAdvancedHandShopSelect = (setting: IPrintSet) => {
	let obj = { handShopSelect: DEFAULT_SHOP_SELECT.默认不选择平台店铺 };
	if (setting?.printSetExpandDTO?.defaultHandConfig) {
		try {
			obj = JSON.parse(setting.printSetExpandDTO.defaultHandConfig);
		} catch (error) {
			console.log('error: ', error);
		}
	}
	return obj;
};

const tradeStatus = (data:any) => {
	let statusObj = {
		waitPrint: false,
		hadPrinted: false,
		hadSend: false
	};
	for (let item of data.trades) {
		// 待发货待打印
		if (item.status === "WAIT_SELLER_SEND_GOODS" && item.isPrintFhd === '0' && item.isPrintKdd === '0') {
			statusObj.waitPrint = true;
		}
		// 待发货已打印
		if (item.status === "WAIT_SELLER_SEND_GOODS" && (item.isPrintFhd === '1' || item.isPrintKdd === '1')) {
			statusObj.hadPrinted = true;
		}
		// 部分发货
		if (item.status === "SELLER_CONSIGNED_PART" && (item.isPrintFhd === '1' || item.isPrintKdd === '1')) {
			statusObj.hadSend = true;
		}
		// 已发货
		if (item.status === "WAIT_BUYER_CONFIRM_GOODS" || item.status === "TRADE_FINISHED") {
			statusObj.hadSend = true;
		}
	}
	return statusObj;
};

// 成本价 目前保留4位小数 如果4位小数都是0 就保留两位小数
export const getCostPrice = (num, toFixedNum = 2) => {
	num = Number(num);
	if (toFixedNum > 0 && Number.isInteger(num)) {
		num = num.toFixed(toFixedNum);
	}
	return num;
};

// 成本总价
export const getTotalCost = (num, toFixedNum = 4) => {
	if (toFixedNum >= 0 && !Number.isNaN(num)) {
		return Number(Number(num).toFixed(toFixedNum));
	}
	return num;
};

export const isOrderDisabled = (order, orders, serviceTagList) => {
	if (order.isGift && GIFT_SPECIAL_PLATFORM.includes(order.platform)) {
		if (orders?.[0]?.isChecked || orders?.[0]?.status == TradeStatus.等待买家确认收货) {
			return false;
		}
	}
	if (FULL_PRESENT_ORDER_PLATFORM.includes(order.platform) && serviceTagList?.includes('presentOrder')) {
		return true;
	}
	if (FULL_ORDER_PLATFORM.includes(order.platform) && serviceTagList?.includes('full_order_ship')) {
		return true;
	}
	return order.isGift && !GIFT_NORMAL_PLATFORM.includes(order.platform) && order.status !== TradeStatus.等待买家确认收货;
};

export const KsConsolidateType = {
	'ksxd_1': <><span className="r-c-error">新疆中转</span>，若需要查看消费者收货地址，请前往快手小店商家后台查看</>,
	'ksxd_15': <><span className="r-c-error">内蒙古中转</span>，若需要查看消费者收货地址，请前往快手小店商家后台查看</>,
};

export const JdConsolidateType = {
	'jd_1086': <><span className="r-c-error">新疆中转</span>，如需查看消费者真实收货信息，请前往京麦订单详情查看</>,
	// 'jd_15': <><span className="r-c-error">内蒙古中转</span>，如需查看消费者真实收货信息，请前往京麦订单详情查看</>,
};

/**
 * 计算是否存在取消拆单按钮
 */
export const calculateCancelOrder = (list, pack) => {
	let canCancel = true
	list.forEach(p => {
		if (p.trades.length > 1 && p.ptTids?.[0].includes(pack.ptTids[0])) {
			canCancel =  false
		}
	});
	pack?.trades.forEach(trade => {
		if (trade.cancelSplitTrade === false) {
			canCancel =  false
		}
	})
	return canCancel;
}

const getPayDisplayTypeText = (payDisplayType: string, payDisplayTypeValue: string) => {
	switch (payDisplayType) {
		case PAY_DISPLAY_TYPE_ENUM.花呗分期:
			return `花呗分期付款 ${payDisplayTypeValue} 期`;
		case PAY_DISPLAY_TYPE_ENUM.红包:
			return `红包支付金额 ${payDisplayTypeValue} 元`;
		case PAY_DISPLAY_TYPE_ENUM.信用卡:
			return `信用卡支付金额 ${payDisplayTypeValue} 元`;
		default:
			return payDisplayTypeValue;
	}
};
export {
	threadPool,
	getTradeStatusLabel,
	filterUdbUdc,
	getMemoSysFlag,
	getMemoFlag,
	lineBreak,
	calcPlus,
	whitespace,
	getPddRefundStatus,
	filterAddrDetail,
	getTradeLogisticInfo,
	imgUrl,
	handlePackageList,
	getTradePlatformLabel,
	getNickPlatformIcon,
	copyMenu,
	getTradeFlag,
	getTradeFlagTag,
	getOrderRefundStatus,
	getVirtualImgUrl,
	noGoodsLinkWarning,
	confirmWarning,
	calcBizMark,
	calcInputVal,
	handleDecrypt,
	getDecryptInfoForHand,
	decryptRewrite,
	handleReportDecrypt,
	findTradeFromList,
	tradeEditableKeyArr,
	packageEditableKeyArr,
	getMultiPackNum,
	openWW,
	calcFilterOrder,
	getUrlData,
	isBeyondInput,
	spArr,
	diffTodayDays,
	labelListSort,
	consolidateContent,
	limitedPackTips,
	tradeStatus,
	getSkuMergeKeyByRule,
	getPayDisplayTypeText
};
