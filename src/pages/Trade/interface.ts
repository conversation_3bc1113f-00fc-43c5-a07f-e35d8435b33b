import { platform } from "@/types/schemas/common";

import { IKddTemp, IKddTempGroup } from "@/types/schemas/report";
import { IndexSettingGetSenderSettingResponseObj } from "@/types/schemas/user";
import { ISendSubTrades } from "@/types/trade/index";
import { JDOrderShipType, JDStoreOrderType, SendType, TradeChoiceType } from "@/utils/enum/trade";
import { AbnormalFromType } from "./components/BottomCom/checkAbnormalUtil";

export enum IListCheckStatus {
	不选 = 0,
	半选 = 0.5,
	全选 = 1,
}

// export type IListCheckStatus = 0 | 0.5 | 1;

export interface Obj {
	[k: string]: any;
}

/**
ALL_STATUS("全部"),
WAIT_BUYER_PAY("等待买家付款"),
SELLER_CONSIGNED_PART("卖家部分发货"),
WAIT_SELLER_SEND_GOODS("等待卖家发货"),
WAIT_BUYER_CONFIRM_GOODS("等待买家确认收货"),
TRADE_BUYER_SIGNED("买家已签收"),
TRADE_FINISHED("交易成功"),
TRADE_CLOSED("交易自动关闭"),
TRADE_CLOSED_BY_TAOBAO("交易关闭");
 */

export type ITradeStatus =
	"ALL_STATUS"
	| "WAIT_BUYER_PAY"
	| "SELLER_CONSIGNED_PART"
	| "WAIT_SELLER_SEND_GOODS"
	| "WAIT_BUYER_CONFIRM_GOODS"
	| "TRADE_BUYER_SIGNED"
	| "TRADE_FINISHED"
	| "TRADE_CLOSED"
	| "TRADE_CLOSED_BY_TAOBAO"
	| "TRADE_CANCELLED";

/**
 * REFUND_SUCCESSED("退款成功"),
 * REFUND_ING("退款中"),
 * NOT_REFUND("无售后或售后关闭");
 */

export type ITradeRefundStatus =
	| "REFUND_SUCCESSED"
	| "REFUND_ING"
	| "NOT_REFUND";
interface ISellerFlagSys extends Obj {
	/**
	 * String
	 */
	mapKey?: {
		[k: string]: any;
	};
	/**
	 * Integer
	 */
	mapValue?: {
		[k: string]: any;
	};
	[k: string]: any;
}
export interface IOrders extends Obj {
	hasConnected?: boolean;
	num?: number;
	numIid?: string;
	oid?: string;
	cid?: string;
	outerId?: string;
	outerSkuId?: string;
	payment?: string;
	picPath?: string;
	price?: string;
	refundStatus?: ITradeRefundStatus;
	skuId?: string;
	status?: ITradeStatus;
	actualStockNum?: string;
	canSellStockNum?: string;
	tid?: string;
	title?: string;
	titleShort?: string;
	adjustAmount?: string;
	discount?: string;
	skuPropertiesName?: string;
	skuAlias?: string;
	divideOrderFee?: string;
	weight?: string;
	pid?: string;
	ydNo?: string;
	kdName?: string;
	togetherId?: string;
	isShShip?: boolean;
	totalFee?: string;
	/**
	 * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
	 */
	stockOutHandleStatus?: number;
	/**
	 * 是否缺货0-无缺货处理1：有缺货处理
	 */
	isStockOut?: number;
	printedNum?: number;
	timingPromise?: string;
	cutoffMinutes?: string;
	esTime?: string;
	deliveryTime?: string;
	collectTime?: string;
	dispatchTime?: string;
	signTime?: string;
	storeCode?: string;
	/**
	 * 预计配送时间段
	 */
	esRange?: string;
	/**
	 * 预计送达时间
	 */
	esDate?: string;
	/**
	 * 预计送达时间
	 */
	osDate?: string;
	/**
	 * 预计送达时间
	 */
	osRange?: string;
	/**
	 * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
	 */
	promiseService?: string;
	noGoods?: boolean;
	noGoodsLink?: boolean;
	goodsWarn?: boolean;
	isHideByPartShip?: boolean;
	groupRelationRecordList?: IGroupRelationRecordList[];
	isGift?: boolean;
	occupiedStockStatus?: number; // 库存占用状态 1 预占 2 回占
	isPreSale?: number; // 预售
	isEditTitleShort?: boolean;
	isEditTitleShortLoading?: boolean;
	isEditSkuAlias?: boolean;
	isEditSkuAliasLoading?: boolean;
	tempTitleShort?: string;
	tempSkuAlias?: string;
	sendType?: SendType;
	supplierId?: any;
	supplierName?: string;
	stall?: string;
	market?: string;
	[k: string]: any;
}

export interface IGroupRelationRecordList {
	groupProportionNum?: number; // 比例
	salableItemStock?: number; // 库存数
	sysItemId?: string;
	sysItemAlias?: string;
	sysItemName?: string;
	sysSkuName?: string;
	sysSkuId?: string | number;
	id?: string;
	[k: string]: any;
}
export interface ISubTrade extends Obj {
	buyerMessage?: string;
	buyerNick?: string;
	createTime?: string;
	invoiceName?: string;
	invoiceTitle?: string;
	invoiceKind?: string;
	invoiceType?: string;
	isChange?: boolean;
	isMendian?: boolean;
	isCod?: boolean;
	isPrintFhd?: number;
	isPrintKdd?: number;
	ignoreType?: number;
	payTime?: string;
	/**
	 * OrderVo
	 */
	orders?: IOrders[];
	/**
	 * OrderVo
	 */
	giftOrders?: IOrders[];
	gifts?: string;
	payment?: string;
	discountAmount?: string;
	postFee?: string;
	receiverMobile?: string;
	receiverCity?: string;
	receiverDistrict?: string;
	receiverPhone?: string;
	receiverAddress?: string;
	receiverName?: string;
	receiverState?: string;
	receiverTown?: string;
	receiverZip?: string;
	sellerFlag?: string;
	userId?: string;
	sellerNick?: string;
	platformId?: string;
	sellerMemo?: string;
	sellerMemoFlag?: string;
	sellerMemoFlagName?: string;
	tid?: string;
	totalFee?: string;
	status?: ITradeStatus;
	gift?: string;
	type?: string;
	sendRemindFlag?: number;
	sendRemindHour?: string;
	isAddCostSF?: boolean;
	/**
	 * OrderPromiseDetailVo
	 */
	orderPromiseDetailVo?: {
		orderPromiseKdCode?: string;
		orderPromiseKdName?: string;
		/**
		 * 指定包材
		 */
		orderPromiseBc?: string;
		orderPromiseDeliveryTime?: number;
		[k: string]: any;
	};
	freeSF?: number;
	/**
	 * 商家优惠金额
	 */
	sellerDiscount?: string;
	/**
	 * 平台折扣金额
	 */
	platformDiscount?: string;
	/**
	 * 是否是自动发货
	 */
	isPreShip?: boolean;
	isPending?: boolean;
	isCancelSend?: boolean;
	isMatchCancelSend?: boolean;
	imel?: string;
	deviceSn?: string;
	overseaTracing?: string;
	expressName?: string;
	/**
	 * 催发货时间
	 */
	urgeShippingTime?: string;
	/**
	 * 自动发货任务id
	 */
	preShipId?: string;
	/**
	 * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单(该参数为map)
	 */
	sellerFlagSys?: ISellerFlagSys;
	/**
	 * 标识当前订单是否存在退款，不做存储
	 */
	hasRefund?: boolean;
	refundStatus?: ITradeRefundStatus;
	totalWeight?: string;
	/**
	 * 快递单号
	 */
	ydNo?: string;
	ydNoSet?: string[];
	/**
	 * 快递公司编号
	 */
	logisticsId?: string;
	/**
	 * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
	 */
	stockOutHandleStatus?: number;
	/**
	 * 是否缺货0-无缺货处理1：有缺货处理
	 */
	isStockOut?: number;
	/**
	 * 预售时间
	 */
	preSaleTime?: string;
	/**
	 * 是否为预售商品1表示是0表示否
	 */
	isPreSale?: number;
	/**
	 * 风控状态（值为1则为风控订单，值为0则为正常订单）
	 */
	riskControlStatus?: string;
	receiverNameMask?: string;
	receiverPhoneMask?: string;
	receiverAddressMask?: string;
	/**
	 * 订单调fullInfo接口的时间
	 */
	tidFullInfoTime?: string;
	/**
	 * 天猫直送，值true或者false
	 */
	tmallDelivery?: boolean;
	/**
	 * 3PL有时效订单标，值true或者false
	 */
	threePlTiming?: boolean;
	/**
	 * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
	 */
	deliveryCps?: string;
	/**
	 * 取
	 */
	cpCode?: string;
	/**
	 * cpCode转cpName
	 */
	cpName?: string;
	/**
	 * 是否催发货1：催发货0：非催发货
	 */
	isUrgeDelivery?: number;
	/**
	 * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
	 */
	caid?: string;
	/**
	 * trade_fromWAP,JHS交易内部来源
	 */
	tradeFrom?: string;
	/**
	 * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
	 */
	fenxiaos?: number;
	payHours?: number;
	receiverSecret?: string;
	mobileSecret?: string;
	timingPromise?: string;
	promiseService?: string;
	esDate?: string;
	esRange?: string;
	osDate?: string;
	osRange?: string;
	cutoffMinutes?: string;
	esTime?: string;
	deliveryTime?: string;
	collectTime?: string;
	sendTime?: string;
	signTime?: string;
	dispatchTime?: string;
	/**
	 * value=logistics_upgrade为天猫物流升级订单
	 */
	asdpBizType?: string;
	/**
	 * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
	 */
	asdpAds?: string;
	/**
	 * 天猫送货上门快递名称
	 */
	sendExName?: string;
	/**
	 * 天猫送货上门快递编码
	 */
	sendExCode?: string;
	/**
	 * 全渠道商品通相关字段
	 */
	omnichannelParam?: string;
	/**
	 * 是否是海外购
	 */
	isThreePl?: boolean;
	/**
	 * 是否风险留言订单
	 */
	pbly?: boolean;
	/**
	 * 家装订单
	 */
	tmserSpu?: boolean;
	/**
	 * 淘宝后台getShippingType
	 */
	shippingType?: string;
	/**
	 * 淘宝后台getShippingType对应名称，后期
	 */
	shippingName?: string;
	deliveryTypeDesc?: string;
	/**
	 * 订单类型交易方式,一口价,送礼,赠品领取,货到付款,定金预售
	 */
	tradeType?: string;
	/**
	 * 支付类型
	 */
	payType?: string;
	biz?: string;
	// 手工单-无商品 发货内容
	shipInfo?: string;

	/**
	 * 额外运单 ,ExtraDeliveryVO
	 */
	extraDeliveryList?: {
		/**
		 * 快递运单号
		 */
		trackingNumber?: string;
		/**
		 * 快递公司id
		 */
		logisticsId?: number;
		/**
		 * 快递公司名称
		 */
		logisticsName?: string;
		hideOrderByPartShipCount?: number;
		hidePartShipOrder?: boolean;
		[k: string]: any;
	}[];

	newYdNo?: string[];
	epidemicMark?: 0 | 1 | 2;
	duoduoWholesale?: boolean;
	platform?: platform;
	jdStoreOrderType?: JDStoreOrderType;
	jdOrderShipType?: JDOrderShipType;
	[k: string]: any;
}
export interface IPackage extends Obj {
	waveNoList?:string[];
	// 手工单的时候是否有不同店铺的商品
	hasDiffSellerId?: boolean;
	source?: string;
	buyerNick?: string;
	isCod?: boolean;
	printInfo?: string;
	platform?: platform;
	receiverAddress?: string;
	receiverCity?: string;
	receiverDistrict?: string;
	receiverMobile?: string;
	receiverName?: string;
	receiverPhone?: string;
	receiverState?: string;
	receiverTown?: string;
	userId?: string;
	sellerNick?: string;
	platformId?: string;
	togetherId?: string;
	shipListPrintStatus?: string;
	waybillPrintStatus?: string;
	mainTid?: string;
	orderCode?: string;
	isSuccess?: boolean;
	errorMsg?: string;
	/**
	 * TradeVo
	 */
	trades?: ISubTrade[];
	sendRemindFlag?: number;
	sendRemindHour?: string;
	payTime?: string;
	isPending?: boolean;
	isAddCostSF?: boolean;
	freeSF?: number | boolean;
	expressName?: string;
	receiverNameMask?: string;
	receiverPhoneMask?: string;
	receiverAddressMask?: string;
	/**
	 * 是否包含自动发货订单
	 */
	isPreShip?: boolean;
	/**
	 * String
	 */
	preShipIds?: string[];
	hasRefund?: boolean;
	receiverZip?: string;
	/**
	 * (该参数为map)
	 */
	sellerFlagSys?: ISellerFlagSys;
	/**
	 * :
	 */
	tids?: string[];
	/**
	 * CaiNiaoIntelliExpress
	 */
	smartExpress?: {
		exCode?: string;
		exName?: string;
		[k: string]: any;
	};
	type?: string;
	/**
	 * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
	 */
	fenxiaos?: number;
	/**
	 * 是否合单
	 */
	isMerge?: string;
	/**
	 * 是否合单
	 */
	caid?: string;
	/**
	 * 总支付金额
	 */
	totalPayment?: string;
	/**
	 * 总商家优惠金额
	 */
	totalSellerDiscount?: string;
	/**
	 * 总平台折扣金额
	 */
	totalPlatformDiscount?: string;
	/**
	 * 总邮费
	 */
	totalPostFee?: string;
	/**
	 * 总重量
	 */
	totalWeight?: string;
	/**
	 * 已发货的快递单号
	 */
	ydNo?: string;
	ydNoSet?: string[];
	/**
	 * 订单标记（用于黑白订单标记）,0：正常订单（默认）,1：白订单（表明这笔订单是商家请人刷的订单）,2：黑名单（表明这笔订单是别人恶意刷的订单）
	 */
	tidMark?: number;
	/**
	 * OrderPromiseVo
	 */
	orderPromiseVo?: {
		orderPromiseDeliveryTime?: number;
		isManyKd?: boolean;
		[k: string]: any;
	};
	/**
	 * Object
	 */
	orderTagList?: {
		[k: string]: any;
	}[];
	payHours?: number;
	goodsNum?: string;
	/**
	 * 家装订单
	 */
	tmserSpu?: boolean;
	/**
	 * 异常地址
	 */
	abnormalAddress?: boolean;
	/**
	 * 智选快递的快递名称
	 */
	ExpressName?: string;
	/**
	 * 打印菜鸟面单时是否需要隐私服务
	 */
	cnPrivacy?: boolean;
	receiverSecret?: string;
	mobileSecret?: string;
	/**
	 * OpenAddresseeID（收件人ID）,基于收件人的信息(姓名、地址、手机号、电话）、订单创建时间、店铺、appkey加密生成的ID。,相同的收件人，在同一家店铺，在固定时间周期内（通常1个自然周）内创建的订单，OAID相同。
	 */
	oaid?: string;
	/**
	 * 天猫送货上门快递名称
	 */
	sendExName?: string;
	/**
	 * 天猫送货上门快递编码
	 */
	sendExCode?: string;
	/**
	 * value=logistics_upgrade为天猫物流升级订单
	 */
	asdpBizType?: string;
	/**
	 * value=201里面多个值时用英文逗号隔开,,201为送货上门服务
	 */
	asdpAds?: string;
	signTime?: string;
	deliveryTime?: string;
	/**
	 * 是否催发货1：催发货0：非催发货
	 */
	isUrgeDelivery?: number;
	/**
	 * 催发货时间，如果合单，则显示最早催发货时间
	 */
	urgeDeliveryTime?: string;
	noGoods?: boolean;
	noGoodsLink?: boolean;
	sellerId?: string;
	// 前端定义参数
	isFilter?: boolean;
	isFilterByPlatform?: boolean;
	isFilterByStatus?: boolean;
	isChecked?: boolean;
	isMendian?: boolean;
	sids?: string[];
	sendMobile?: string[]; // 单号回显返回的发货人手机号
	isDecrypted?: boolean;
	totalOrderNum?: number;
	totalGoodsNum?: number;
	isVirtualNum?: number;
	isEveryOrderHasSysSkuId?: boolean;
	// 可达标识
	_canReach?: number;
	isHideByPending?: boolean; // 隐藏挂起订单
	isHideByRefund?: boolean; // 隐藏退款订单
	isHideByDropShip?: boolean; // 隐藏代发订单
	isHideByWave?: boolean; // 隐藏已生成波次订单
	epidemicMark?: 0 | 1 | 2; // 0：非疫情地区,1：疫情中风险地区,2：疫情高风险地区
	isLimited?: boolean; // 受限订单 不能勾选、打印、发货、解密、复制等一些操作
	jdStoreOrderType?: JDStoreOrderType;
	jdOrderShipType?: JDOrderShipType;
	promiseDeliveryTime?: boolean;
	wrapperDescription?: boolean;
	duoduoWholesale?: boolean;
	shipHold?: boolean;
	isPreSale?: boolean | number; // 预售订单
	printContentOverflow?: boolean; // 打印内容是否溢出

	// 分销
	distributorLinkMan?: string;
	distributorAccount?: string;
	distributorUserId?: string;
	distributorStatus?: string;
	distributorRemark?: string;
	distributorMobile?: string;
	mergeTradeCount?: number; // 合单数量
	sendType?: SendType;
	[k: string]: any;
}

export interface IFlagMemosObj {
	flagMemos: IFlagMemos[];
	defaultFlag: number;
	needIcon: boolean;
	hasContent: boolean;
	advancedSet: AdvancedSet;
}

export interface IFlagMemos {
	sellerFlag: string;
	sellerFlagName: string;
	sellerMemo: string;
	buyerMessage: string;
	tid: string;
}

export interface AdvancedSet {
	autoTogether: boolean;
	bgTradeSellMemo: number;
	btnViewConfig: string;
	buttionFooter: boolean;
	defaultSellerMemoFlag: number;
	displayGenYdNum: boolean;
	displayGoods: boolean;
	displayMemo: boolean;
	expressOnOffTime: string;
	expressSwitch: number;
	filterOrder: boolean;
	filterOrderSpecNick: number;
	filterSelected: number;
	genSidType: string;
	hideAfterShip: boolean;
	isHidePendingTrade: number;
	isAbnormalAddress: number;
	isAutoAddPrintNum: number;
	isAutoDelete: number;
	isButtionFooter: boolean;
	isExpressMemoFilter: number;
	isFhdMorefjr: number;
	isFhdUnityfjr: number;
	isHotStyle: number;
	isKddUuityfjr: number;
	isMergeFirst: number;
	isMergeHint: number;
	isMergePriority: number;
	isNewYdNo: boolean;
	isNotAddPrintNumOnlyOne: number;
	isNumIid: number;
	isOnlyShowOnSale: number;
	isPrintReverse: number;
	isRelationJumpVisibility: number;
	isSendImel: number;
	isSendRemind: boolean;
	isTemplateSuspend: number;
	isYdNumWarn: boolean;
	isYdPlain: boolean;
	ismKddMorefjr: number;
	logisticsWarn: number;
	numIid: number;
	openPrintNotify: number;
	outerId: number;
	pageSizeSet: number;
	printByGoodSequence: number;
	printNumReset: number;
	printNumStyle: number;
	remarkHighLight: number;
	remindHour: string;
	remindModifySet: number;
	searchVersion: number;
	sendAfterPrint: number;
	sendRemind: boolean;
	shortName: number;
	showFlagName: number;
	showNum: number;
	skipAbnormal: number;
	skuAlias: number;
	skuId: number;
	skuName: number;
	sortRule: number;
	templateMode: number;
	title: number;
	weChatRemind: number;
	weChatRemindHour: string;
	ydNum: number;
	[k: string]: any;
}

export interface ReceiverInfo {
	receiverNameMask: string;
	receiverMobileMask: string;
	receiverPhoneMask: string;
	receiverAddressMask: string;
	receiverCity: string;
	receiverDistrict: string;
	receiverState: string;
	receiverZip: string;
	trades: ISubTrade[];
	[k: string]: any;
}

export interface IChoiceCheckItem {
	label?: string;
	point?: string;
	fm?: string;
	type: TradeChoiceType;
	disabled?: boolean;
	tip?: string;
	dependTemplate?: boolean;
}

export interface ICopySettingItem {
	name?: string;
	isChecked?: boolean;
	key?: string;
}

export interface IFilterOrderItem {
	[x: string]: any;
	order: IOrders; // 筛选的商品信息
	checkedCount: number; // 当前宝贝的选中数量
	normalOrderCheckCount: number; // 正常宝贝的选中数量
	totalCount: number; // 当前宝贝的总数量
	normalOrderTotalCount: number; // 当前宝贝的总数量 不包含退款、已取消、锁定、挂起以及隐藏的商品
	checkStatus: "" | "checkHalf" | "checkAll";
	stockNum?: number | string;
}

export interface IFilterOrderObj {
	[k: string]: IFilterOrderItem;
}

export interface IScanPrintFormSetting {
	scanNumSetting?: number[];
	sender?: number;
	senderInfo?: IndexSettingGetSenderSettingResponseObj;
	printer?: string;
	tagPrinter?: string;
	autoSend?: boolean; // 23.07.25 改存后端
	templateInfo?: number;
	template?: IKddTemp | IKddTempGroup;
	id?: string;
	platValue: platform[];
	shopValue: string[];
	[k: string]: any;
}

export interface IScanningLabelInfo {
	currentScan?: boolean;
	/**
	 * 主订单id
	 */
	tid?: string;
	/**
	 * 主要订单状态
	 */
	/**
	 * 订单付款时间
	 */
	payTime?: string;
	/**
	 * 订单商品总数量序号,表字段:sort_num_code
	 */
	sortNumCode?: string;
	/**
	 * 订单序号,表字段:sort_trade_sn
	 */
	sortTradeSn?: number;
	/**
	 * 买家昵称
	 */
	buyerNick?: string;
	/**
	 * 小标签信息集合 ,TakeGoodsLabelInfo
	 */
	/**
	 * 子订单id
	 */
	oid?: string;
	/**
	 * 卖家昵称
	 */
	sellerNick?: string;
	/**
	 * 平台类型
	 */
	platform?: string;
	/**
	 * 标签id
	 */
	labelId?: string;
	/**
	 * 货品规格别名
	 */
	sysSkuAlias?: string;
	/**
	 * 货品规格名称
	 */
	sysSkuName?: string;
	/**
	 * 货品规格商家编码
	 */
	sysSkuOuterId?: string;
	/**
	 * 商家编码
	 */
	outerId?: string;
	/**
	 * 子订单状态
	 */
	orderStatus?: ITradeStatus;
	/**
	 * 订单打印状态
	 * UN_PRINT(0, "未打印"),
	 * ALREADY_PRINT(1, "已打印"),
	 */
	printStatus?: number;
	/**
	 * 货品规格图片
	 */
	sysSkuPicUrl?: string;
	/**
	 * 主订单状态
	 */
	tradeStatus?: ITradeStatus;
	/**
	 * 商品标题
	 */
	title?: string;
	/**
	 * 标签状态WAIT_SORT("待配货"),,ALREADY_SORT("已配货"),,STOCKOUT("缺货"),,STOCKPILE("已屯货"),,REFUND("已退货"),,SEND("已发货"),,EXPIRE(7"已作废")
	 */
	labelStatus?: ILabelStatus;
	/**
	 * 宝贝数量
	 */
	itemNum?: number;
	goodsNum?: number;
	preparedNum?: number;
	preparedObj?: Obj;
	manualPrepare?: boolean;
	isChecked?: boolean;
	[k: string]: any;
}

export type ILabelStatus = 'WAIT_SORT' | 'ALREADY_SORT' | 'STOCKOUT' | 'STOCKPILE' | 'REFUND' | 'SEND' | 'EXPIRE';

export interface IShipFuncOnOk {
	params?: {
		subTrades?: ISendSubTrades[];
		stockStatus?: string;
	}[],
	totalTrade?: number,
	templateName?: string,
	sendType?: string,
	shipFinishFn?: Function,
	afterPrint?: boolean
	type?: 'trade' | 'scanPrint' | 'refundLabel'|'inspectSend' | 'weighSend' | 'postPrintSend',
}

export interface IAbnormalOrderManage {
	orderList?: IPackage[],
	type?: AbnormalFromType,
	noTemplate?: boolean,
	isNoLogistics?: boolean,
	isPreShip?: boolean,
	isInspectAndSend?: boolean,
	afterPrint?: boolean,
	togetherId?: string,
	templateInfo?: IKddTemp | IKddTempGroup,
	noLogisticsType?: SendType,
	noLogisticsParams?: {exName?: string, exNumber?: string},
	callFrom?: 'directInputYdNo',
}

export interface IChoiceItem {
	type: TradeChoiceType;
	[key: string]: any;
}
