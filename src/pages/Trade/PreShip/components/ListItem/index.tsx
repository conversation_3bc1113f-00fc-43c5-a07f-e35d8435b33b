import React, {
	memo,
	useCallback,
	useEffect,
	useLayoutEffect,
	useMemo,
	useRef,
	useState
} from "react";
import { LockOutlined } from "@ant-design/icons";
import _ from "lodash";
import { Modal, Tooltip } from "antd";
import { observer } from "mobx-react";
import event from "@/libs/event";
import { decryptFn } from '../../../components/ListItem/components/Decrypt';
import PackageDetail from "./components/packageDetail";
import { PLAT_TB, PLAT_ALI, PLAT_JD, PLAT_SCMHAND, PLAT_PDD, PLAT_KTT } from '@/constants';
import {
	CheckboxCom, ProductOrderFragment
} from "./components/simpleCom";
import Pointer from "@/utils/pointTrack/constants";
// import { IPackage } from "../../interface";
import Icon from "@/components/Icon";
import { getNickPlatformIcon, getTradePlatformLabel } from '@/pages/Trade/utils';
import { PreShipListData } from '@/types/trade/preShip';
import preShipStore from "@/stores/trade/preShip";
import { checkOperateTip, operate, sendPreShip } from '../utils';
import shipImg from '@/assets/image/trade/sanjiaofuhao_you.png';
import cancelImg from '@/assets/image/trade/cancel.png';
import '@/pages/Trade/components/ListItem/index.scss';
import "./index.scss";
import ShowLogistic from "@/pages/Report/KddLog/components/ShowLogistic";
import { getShopName, isSourceScm, isSourceHand } from "@/components-biz/ShopListSelect/shopListUtils";
import userStore from "@/stores/user";
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";


export interface IListItemProps {
	pack: PreShipListData;
	orderSearchStatus?: string;
	from?: string;
	index?: number;
	setIsShowPrintStyleSetting?: Function
	setIsShowKeywordSetting?: Function
}

const logisticsStatusMap = {
	1: '待揽收',
	2: '已揽收'
};

const ListItem = observer((props: IListItemProps) => {
	const { pack, from, index, setIsShowPrintStyleSetting, setIsShowKeywordSetting } = props;
	const { columnConfig, getColWidth, updateList } = preShipStore;

	const handleDecrypt = async(pack:PreShipListData) => {
		const { successObj } = await decryptFn({
			togetherId: pack.tid,
			platform: pack.platform,
			mallId: pack.sellerId,
			encryptObj: {
				name: pack.receiverName,
				phone: pack.receiverMobile,
				address: pack.receiverAddress,
				buyerNick: pack.buyerNickOrigin || pack.buyerNick,
			},
			decryptArr: [{
				sellerId: pack.sellerId,
				platform: pack.platform,
				caid: pack.caid,
				tid: pack.encodeTid || pack.tid,
				ptTid: pack?.trades?.map(s => s.ptTid)?.join(','),
				// tid: pack.encodeTid,
				sceneCode: 100,
				source: pack.tradeEncodeType == 2 ? 'HAND' : '',
				encodeReceiverPhone: pack.receiverPhone,
				encodeMobile: pack.receiverMobile,
				encodeReceiverName: pack.receiverName,
				encodeReceiverAddress: pack.receiverAddress,
			}],
			sellerNick: pack.sellerNick,
			isDecrypted: pack.isDecrypted,
			sellerId: pack.sellerId,
			pack
		});
		let decryptInfo = successObj[pack.tid];
		if (decryptInfo) {
			let buyerNick = pack.buyerNick;
			if (pack.platform === PLAT_JD && !isSourceHand(pack)) {
				buyerNick = decryptInfo.buyerNick;
			} else if (pack.platform === PLAT_KTT) {
				buyerNick = decryptInfo.buyerNick;
			} else if (![PLAT_TB, PLAT_ALI].includes(pack.platform)) {
				buyerNick = decryptInfo.receiverName || pack.buyerNick;
			} else {
				buyerNick = pack.buyerNick;
			}
			updateList(pack.tid, {
				isDecrypted: true,
				buyerNick,
				receiverNameMask: decryptInfo.receiverName || '',
				receiverTel: decryptInfo.mobile || decryptInfo.receiverPhone || '',
				receiverAddressMask: decryptInfo.receiverAddress || ''
			});
		}
	};
	// 暂停
	const pause = (pack:PreShipListData) => {
		let data = [{
			opsId: pack.id,
			platform: pack.platform
		}];
		operate(3, data);
	};
	// 取消
	const cancel = (pack:PreShipListData) => {
		let data = [{
			tid: pack.tid,
			platform: pack.platform
		}];
		operate(2, data);
	};
	// 发货
	const shipSend = (pack:PreShipListData) => {
		let data = [pack];
		sendPreShip(data);
	};
	// 恢复
	const recovery = (pack:PreShipListData) => {
		let data = [{
			opsId: pack.id,
			platform: pack.platform
		}];
		operate(4, data);
	};
	const renderRemindHourCom = () => {
		let remindHour = +pack.sendRemindHour;
		const afterText = pack.platform == PLAT_PDD ? '将超时揽收' : '';

		if (remindHour >= 24) {
			return <div style={ { color: '#00B77F' } }>剩余{remindHour}小时 { afterText}</div>;
		} else if (remindHour > 0 && remindHour < 24) {
			return <div style={ { color: '#FB9D2C ' } }>剩余{remindHour}小时 { afterText}</div>;
		} else if (remindHour < 0) {
			return <div style={ { color: 'rgba(255,0,0)' } }>已超{(remindHour + '').split('-')[1]}小时</div>;
		}
		return '';
	};
	return (
		<div className="position-wrap" key={ `${pack.platform}-${pack.tid}` }>
			<div
				className={
					`preShipPackageList
					${index % 2 !== 0 ? 'pack-zebra' : ''}
					${pack.isChecked ? "pack-selected" : ""}
					${pack.state == 1 ? 'preShip-pack-pause' : ''}`
				}
			>
				<CheckboxCom pack={ pack } index={ index } />
				{pack && columnConfig.userConfig.filter(item => item.ischecked).map((col: any) => (
					<React.Fragment key={ col.key }>
						{col.key === "index" && (
							// <IndexCom index={ index } />
							<div className="table_tbtlt_index preShipPackageItem" style={ { width: getColWidth(col.key) } }>{ index + 1 }</div>
						)}
						{col.key === "buyerNick" && (
							// <BuyerNickCom pack={ pack } colWidth={ getColWidth(col.key) } />
							<div className="table_tbtlt_username preShipPackageItem r-flex r-ai-c" style={ { width: getColWidth(col.key) } } >
								{getNickPlatformIcon({ platform: pack.platform, source: pack.source })}
								<div>
									<div>{ pack.buyerNick }
										{
											pack?.combinationOrderInfos?.length
												? (<span className="preShipIcon">合:{pack.combinationOrderInfos?.length}</span>)
												: ''
										}

									</div>
									<p>{renderRemindHourCom()}</p>
								</div>
								{/* lastShipTime */}
							</div>
						)}
						{col.key === "receiverAddress" && (
							// <ReceiverAddressCom pack={ pack } minWidth={ col.width } />
							<div className="table_tbtlt_address preShipPackageItem" style={ { width: getColWidth(col.key) } }>
								<div>
									<div>
										{pack.receiverNameMask}，{pack.receiverTel}
										{
											pack.state == 1 ? <span style={ { color: '#ff3333', marginLeft: '10px' } }>(暂停中)</span> : null
										}
									</div>
									<div className="r-mb-5">
										{pack.receiverProvince}{pack.receiverCity}{pack.receiverCounty}{pack.receiverAddressMask}
										{!pack.isDecrypted
											? (
												<span onClick={ () => { handleDecrypt(pack); } }>
													<Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5 } } />
												</span>
											) : ''}
									</div>
									{pack.refundStatus == 1 ? <span style={ { color: '#ff3333', padding: '4px 8px', border: '1px solid #ff3333' } }>订单中有退款</span> : null}
									{pack.changeAdderFlag ? <span className="r-mr-5 trade-has-change-address">改地址</span> : ''}
								</div>
							</div>
						)}
						{col.key === "tid" && (
							// <TidCom pack={ pack } colWidth={ getColWidth(col.key) } />
							<div className="table_tbtlt_tradeId" style={ { width: getColWidth(col.key) } }>
								 { pack.combinationOrderInfos?.length ? (
									<>
										{pack.combinationOrderInfos.map(i => <div>{i.tid}</div>)}
									</>
								) : pack.tid }
							</div>
						)}
						{col.key === "ptTid" && (
							<div className="table_tbtlt_tradeId" style={ { width: getColWidth(col.key) } }>
								{ pack.combinationOrderInfos?.length ? (
									<>
										{pack.combinationOrderInfos.map(i => <div>{i.ptTid}</div>)}
									</>
						  ) : pack.ptTid }
							</div>
						)}

						{col.key === "logisticsStatus" && (
							// <TidCom pack={ pack } colWidth={ getColWidth(col.key) } />
							<div className="table_tbtlt_logisticsStatus preShipPackageItem" style={ { width: getColWidth(col.key) } }>
								{ logisticsStatusMap[pack.wlStatus] || '-' }
							</div>
						)}
						{col.key === "newLogistics" && (
							// <TidCom pack={ pack } colWidth={ getColWidth(col.key) } />
							<div className="table_tbtlt_newLogistics preShipPackageItem" style={ { width: getColWidth(col.key) } }>{ pack.wlDesc || '-' }</div>
						)}
						{col.key === "exName" && (
							// <TidCom pack={ pack } colWidth={ getColWidth(col.key) } />
							<div className="table_tbtlt_exName preShipPackageItem" style={ { width: getColWidth(col.key) } }>{ pack.exName }</div>
						)}
						{col.key === "sid" && (
							// <TidCom pack={ pack } colWidth={ getColWidth(col.key) } />
							<ShowLogistic ydNo={ pack.exNumber }>
								<div className="table_tbtlt_sid preShipPackageItem" style={ { width: getColWidth(col.key) } }>{ pack.exNumber }</div>
							</ShowLogistic>
						)}
						{col.key === "tradeFrom" && (
							// <TradeFromCom pack={ pack } colWidth={ getColWidth(col.key) } />
							<div className="table_tbtlt_tradeFrom preShipPackageItem" style={ { width: getColWidth(col.key) } }>
								{getTradePlatformLabel(pack.platform)}
								{!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(pack) ? '****' : getShopName({ plat: pack?.platform, sellerNick: pack?.sellerNick || "" })}
							</div>
						)}
						{col.key === "productContent" && (
							<div className="table_tbtlt_product preShipPackageItem product-container" style={ { width: (preShipStore?.productSetting?.showColumn === 2 ? 2 : 1) * getColWidth(col.key) } }>
								<ProductOrderFragment productOrders={ pack.productContentList } pack={ pack } />
							</div>
						)}
						{col.key === "addTime" && (
							// <TradeFromCom pack={ pack } colWidth={ getColWidth(col.key) } />
							<div className="table_tbtlt_addTime preShipPackageItem" style={ { width: getColWidth(col.key) } }>{ pack.created }</div>
						)}
						{col.key === "operate" && (
							// <OperateCom pack={ pack } />
							<div style={ { width: getColWidth(col.key) } } className="table_tbtlt_operate table_item_operate preShipPackageItem r-flex r-ai-c">
								<Tooltip placement="top" title="发货">
									<img src={ shipImg } style={ { width: '24px', cursor: 'pointer' } } onClick={ async() => { await checkOperateTip(); shipSend(pack); } } alt="" data-point={ Pointer.自动发货_发货 } />
								</Tooltip>
								<Tooltip placement="top" title="取消自动发货">
									<img
										src={ cancelImg }
										style={ { width: '14px', cursor: 'pointer' } }
										onClick={ async() => { await checkOperateTip(); cancel(pack); } }
										className="r-mr-5"
										alt=""
										data-point={ Pointer.自动发货_取消 }
									/>
								</Tooltip>
								{
									pack.state === 1 ? (
										<Tooltip placement="top" title="恢复自动发货">
											<span className="table_item_operate_pause" onClick={ async() => { await checkOperateTip(); recovery(pack); } } data-point={ Pointer.自动发货_恢复 }>恢复</span>
										</Tooltip>
									) : (
										<Tooltip placement="top" title="暂停自动发货">
											<span className="table_item_operate_pause" onClick={ async() => { await checkOperateTip(); pause(pack); } } data-point={ Pointer.自动发货_暂停 }>暂停</span>
										</Tooltip>
									)
								}
							</div>
						)}
					</React.Fragment>
				))}

			</div>
			{pack.isExpand && (
				<PackageDetail packInfo={ pack } />
			)}
		</div>
	);
});

export default ListItem;
