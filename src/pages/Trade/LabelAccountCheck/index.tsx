import React, { useEffect, useMemo, useState } from "react";
import { observer } from "mobx-react";
import { Button, Empty, Layout, Pagination, Spin, Tabs, Modal, Select } from "antd";
import _ from "lodash";
import message from '@/components/message';
import SearchContainer from "./components/SearchContainer";
import List from "./components/List";
import { ItemTakeGoodsLabelSelectLabelCheckPageRequest } from "@/types/trade/takeGoodsLabel";
import { ItemTakeGoodsLabelConfigSelectApi, ItemTakeGoodsLabelSelectLabelCheckGroupByItemApi, ItemTakeGoodsLabelSelectLabelCheckPageApi } from "@/apis/trade/takeGoodsLabel";
import TradeSortCom from "./components/SearchContainer/TradeSortCom";
import s from './index.module.scss';
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import { ColumnsEnum, DEFAULT_PRODUCT_CONTENT_SETTING, LOCAL_PRODUCT_SETTING, PRODUCT_SETTING_CHANGE, SortType, TabPaneEnum, TabPaneT2TableColumns, getListHeaderText } from "./components/List/constants";
import { getMirrorEnum } from "@/utils";
import event from "@/libs/event";
import { local } from "@/libs/db";
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { downExcel } from "@/pages/Report/Bhd/BhdList/utils";
import PlatGoodsList from "./components/PlatGoodsList";
import { dealPlatListItem } from "./components/PlatGoodsList/utils";
import { STALL_SORT_KEY, SUPPLIER_SORT_KEY } from "./components/List/ListHeader";
import { ItemItemUserConfigQueryConfigApi, ItemItemUserConfigUpdateConfigApi } from "@/apis/trade/search";

interface IPageInfo {
	total: number,
	pageSize: number,
	pageNo: number
}

const LabelAccountCheck = observer((props:any) => {
	const [searchParams, setSearchParams] = useState<ItemTakeGoodsLabelSelectLabelCheckPageRequest>({});
	const [queryLabelCheckSort, setQueryLabelSort] = useState(1);
	const [list, setList] = useState([]);
	const [dataTotal, setDataTotal] = useState();
	const [pageInfo, setpageInfo] = useState<IPageInfo>({ total: 0, pageNo: 1, pageSize: 30 });
	const [isSearching, setIsSearching] = useState(false);
	const [exportLoading, setExportLoading] = useState(false);
	const [exportDetailLoading, setExportDetailLoading] = useState(false); // 新增导出明细loading状态
	const [emptyText, setEmptyText] = useState('请点击查询，查看订单数据');
	const [tabsActiveKey, setTabsActiveKey] = useState<string>('5'); // 当前弹窗tabs
	const [productContentList, setProductContentList] = useState([]);
	const [tableSettingVisible, setTableSettingVisible] = useState(false);
	const [mergeSettings, setMergeSettings] = useState({
		outerIdSameMerge: 0, // 0: 不合并, 1: 合并
	});

	// 获取表格设置配置
	const getTableSettingConfig = async() => {
		try {
			const res = await ItemItemUserConfigQueryConfigApi({ 
				itemUserConfigBizEnum: "LABEL_CHECK_ITEM_GROUP_SET_CONFIG" 
			});
			const rule = res?.find((item) => (item?.biz === "label_check_item_group_set_config"));
			if (rule?.value) {
				const config = JSON.parse(rule.value);
				setMergeSettings(config);
				console.log(config, 'configconfig');
			}
		} catch (error) {
			console.log("获取表格设置配置失败:", error);
		}
	};
	useEffect(() => {
		if (tableSettingVisible) {
			// 获取表格设置配置
			getTableSettingConfig();
		}
	}, [tableSettingVisible]);

	// 保存表格设置配置
	const saveTableSettingConfig = async(config) => {
		try {
			await ItemItemUserConfigUpdateConfigApi({
				value: JSON.stringify(config),
				itemUserConfigBizEnum: "LABEL_CHECK_ITEM_GROUP_SET_CONFIG"
			});
		} catch (error) {
			console.log("保存表格设置配置失败:", error);
		}
	};

	// 表格设置确认
	const handleTableSettingOk = async() => {
		await saveTableSettingConfig(mergeSettings);
		console.log('保存表格设置:', mergeSettings);
		setTableSettingVisible(false);
	};

	// 表格设置取消
	const handleTableSettingCancel = () => {
		setTableSettingVisible(false);
	};

	useEffect(() => {
		if (searchParams && searchParams.pageNo) {
			setIsSearching(true);
			setList([]);
			if (searchParams.groupType == TabPaneEnum.按平台商品) {
				ItemTakeGoodsLabelSelectLabelCheckGroupByItemApi({
					...searchParams,
				}).then((res) => {
					setIsSearching(false);
					if (res && res.list) {
						setDataTotal(res.dataTotal);
						const list = dealPlatListItem(res?.list, res.dataTotal);
						setList(list);
						searchParams.pageNo == 1 && setpageInfo((prev) => ({
							...prev,
							total: res.total || 0
						}));
					} else {
						setEmptyText('暂无数据');
					}
				}).catch(() => {
					setIsSearching(false);
				});
			} else {
				ItemTakeGoodsLabelSelectLabelCheckPageApi({
					...searchParams,
				}).then((res) => {
					setIsSearching(false);
					if (res && res.list) {
						setDataTotal(res.dataTotal);
						setList(res.list);
						searchParams.pageNo == 1 && setpageInfo((prev) => ({
							...prev,
							total: res.total || 0
						}));
					} else {
						setEmptyText('暂无数据');
					}
				}).catch(() => {
					setIsSearching(false);
				});
			}
			
		}
	}, [searchParams]);

	useEffect(() => {
		ItemTakeGoodsLabelConfigSelectApi({}).then((res) => {
			if (res.queryLabelCheckSort) setQueryLabelSort(res.queryLabelCheckSort);
		});
	}, []);

	const handleSort = (field: string, sortType: SortType) => {
		if (sortType === SortType.NONE) {
			setSearchParams(prev => ({
				...prev,
				sortAscOrDesc: null,
				sortType: null,
			  }));
		} else {
			setSearchParams(prev => ({
				...prev,
				sortAscOrDesc: sortType === SortType.ASC ? 1 : 2, // 1升序，2降序
				sortType: 1, // 写死
			  }));
		}
	  };
	  
	const changeSort = (value:number) => {
		setSearchParams(prev => ({
			...prev,
			sortType: value
		}));
		setQueryLabelSort(value);
	};
	const renderNoList = () => {
		if (isSearching) {
			return <Spin style={ { width: '100%', minHeight: "200px", padding: "100px" } } tip="查询订单中" />;
		} else {
			return <Empty style={ { minHeight: "200px", padding: "100px" } } description={ emptyText } />;
		}
	};
	const handleSearch = (value?:any) => {
		// 档口和供应商有排序就传sortType 1，和sortAscOrDesc；后端通过groupType来判断排序
		// 按平台sku也是传sortType但是不传sortAscOrDesc 。
		// 根据当前选中的 tab 添加对应的排序
		let sortParams = {};
		if (tabsActiveKey === TabPaneEnum.档口 && local.get(STALL_SORT_KEY) && local.get(STALL_SORT_KEY) !== SortType.NONE) {
			sortParams = {
				sortType: 1, 
				sortAscOrDesc: local.get(STALL_SORT_KEY) === SortType.ASC ? 1 : 2
			};
		} else if (tabsActiveKey === TabPaneEnum.供应商 && local.get(SUPPLIER_SORT_KEY) && local.get(SUPPLIER_SORT_KEY) !== SortType.NONE) {
			sortParams = {
				sortType: 1, 
				sortAscOrDesc: local.get(SUPPLIER_SORT_KEY) === SortType.ASC ? 1 : 2
			};
		} else if (tabsActiveKey === TabPaneEnum.按平台sku) {
			sortParams = {
				sortType: queryLabelCheckSort 
			};
		}
		setSearchParams({
			"pageSize": pageInfo.pageSize,
			...value,
			"pageNo": 1,
			groupType: tabsActiveKey,
			// sortType: queryLabelCheckSort,
			...sortParams
		});
	};
	const onChange = (pageNo: number, pageSize: number) => {
		setpageInfo(prev => (
			{
				...prev,
				pageNo,
				pageSize
			}
		));
		setSearchParams(prev => ({
			...prev,
			pageNo: pageSize === prev.pageSize ? pageNo : 1,
			pageSize,
			sortType: queryLabelCheckSort
		}));
	};
	// 导出对账单
	const exportList = async() => {
		if (!(Object.keys(searchParams).length)) {
			message.error("请先查询后再导出");
			return;
		}
		setExportLoading(true);
		if (searchParams.groupType == TabPaneEnum.按平台商品) {
			const {
				printHtml,
			} = getPrintDom();
			downExcel({
				fileName: '标签对账',
				downHtml: printHtml,
			});
		} else {
			await downloadCenter({
				requestParams: searchParams,
				fileName: '标签对账',
				module: ModulesFunctionEnum.标签对账报表
			});
		}
		setExportLoading(false);
	};

	// 导出对账明细
	const exportDetailList = async() => {
		if (!(Object.keys(searchParams).length)) {
			message.error("请先查询后再导出");
			return;
		}
		setExportDetailLoading(true);
		try {
			await downloadCenter({
				requestParams: searchParams,
				fileName: '标签对账明细',
				module: ModulesFunctionEnum.标签对账明细 // 使用新的模块枚举
			});
		} finally {
			setExportDetailLoading(false);
		}
	};


	const getPrintDom = () => {
		const $oldPrintDom = document.getElementById('LabelAccountCheckContentId');
		const $printDom = document.getElementById('LabelAccountCheckContentPrintHtml');
		$printDom.innerHTML = `
		<div class="bhd-printing-html">
			${$oldPrintDom.innerHTML}
		</div>`;
		$printDom.querySelectorAll('td').forEach(item => item.style.border = '1px solid #ccc');
		$printDom.querySelectorAll('th').forEach(item => item.style.border = '1px solid #ccc');
		
		$printDom.querySelectorAll('.r-hidden').forEach(item => item.remove());
		let printHtml = `
			<!DOCTYPE html>
			<div>
				${$printDom.innerHTML}
			</div>
		`;
		return {
			printHtml
		};
	};
	const getColumnsByTabPane = (tabPane) => {
		const 到货列 = [ColumnsEnum.到货数量, ColumnsEnum.到货金额, ColumnsEnum.结算数量, ColumnsEnum.结算金额];
		const 退货列 = [ColumnsEnum.退回数量, ColumnsEnum.退货金额, ColumnsEnum.结算数量, ColumnsEnum.结算金额];
		getMirrorEnum(ColumnsEnum);
		const columns = [];
		TabPaneT2TableColumns[tabPane]?.forEach(i => {
			if (searchParams?.onlyViewGoodsArrival) {
				if (!退货列.includes(i)) {
					columns.push({
						text: ColumnsEnum[i],
						key: i,
					});
				}
			} else if (searchParams?.onlyViewGoodsReturn) {
				if (!到货列.includes(i)) {
					columns.push({
						text: ColumnsEnum[i],
						key: i,
					});
				}
			} else {
				columns.push({
					text: ColumnsEnum[i],
					key: i,
				});
			}
			
		});
		return columns;
		
	};
	const TabPaneList = useMemo(() => {
		return [
			{
				tabName: '市场',
				key: TabPaneEnum.市场,
				columns: getColumnsByTabPane(TabPaneEnum.市场)
			}, {
				tabName: '档口',
				key: TabPaneEnum.档口,
				columns: getColumnsByTabPane(TabPaneEnum.档口)
			}, {
				tabName: '供应商',
				key: TabPaneEnum.供应商,
				columns: getColumnsByTabPane(TabPaneEnum.供应商)
			}, {
				tabName: '店铺',
				key: TabPaneEnum.店铺,
				columns: getColumnsByTabPane(TabPaneEnum.店铺)
			}, {
				tabName: '按平台sku',
				key: TabPaneEnum.按平台sku,
				columns: getColumnsByTabPane(TabPaneEnum.按平台sku)
			}, {
				tabName: '按平台商品',
				key: TabPaneEnum.按平台商品,
				columns: getColumnsByTabPane(TabPaneEnum.按平台商品)
			}
		];
	}, [list]);
	const onChangeTab = async(value:string) => {
		console.log('%c [ value ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
		setTabsActiveKey(value);

		// 根据当前选中的 tab 添加对应的排序
		let sortParams = { ...searchParams };
		if (value === TabPaneEnum.档口 && local.get(STALL_SORT_KEY) && local.get(STALL_SORT_KEY) !== SortType.NONE) {
			sortParams.sortType = 1;
			sortParams.sortAscOrDesc = local.get(STALL_SORT_KEY) === SortType.ASC ? 1 : 2;
		} else if (value === TabPaneEnum.供应商 && local.get(SUPPLIER_SORT_KEY) && local.get(SUPPLIER_SORT_KEY) !== SortType.NONE) {
			sortParams.sortType = 1;
			sortParams.sortAscOrDesc = local.get(SUPPLIER_SORT_KEY) === SortType.ASC ? 1 : 2;
		} else if (value === TabPaneEnum.按平台sku) {
			sortParams.sortType = queryLabelCheckSort;
			sortParams.sortAscOrDesc = null;
		} else {
			sortParams.sortType = null;
			sortParams.sortAscOrDesc = null;	
		}

		setSearchParams({
			...sortParams,
			groupType: value,
		});

		const points = {
			[TabPaneEnum.市场]: Pointer.标签对账_列表TAB_市场,
			[TabPaneEnum.档口]: Pointer.标签对账_列表TAB_档口,
			[TabPaneEnum.供应商]: Pointer.标签对账_列表TAB_供应商,
			[TabPaneEnum.店铺]: Pointer.标签对账_列表TAB_店铺,
			[TabPaneEnum.按平台sku]: Pointer.标签对账_列表TAB_平台SKU,
		};
		sendPoint(points[value]);
	};
	const productSettingOnChange = (v) => {
		setProductContentList(v);

	};
	useEffect(() => {
		const productSetting = local.get(LOCAL_PRODUCT_SETTING) || DEFAULT_PRODUCT_CONTENT_SETTING;
		setProductContentList(productSetting);
		event.on(PRODUCT_SETTING_CHANGE, productSettingOnChange);
		return () => {
			event.off(PRODUCT_SETTING_CHANGE, productSettingOnChange);
		};
	}, []);

	return (
		<div>
			<Layout className="kdzs-section">
				<SearchContainer handleSearch={ handleSearch } isSearching={ isSearching } />
			</Layout>
			<Layout className={ `${s["list-container"]} kdzs-section` }>
				<div>
					<Tabs
						// type="card"
						// tabBarGutter={ 6 }
						destroyInactiveTabPane={ false }
						animated={ false }
						activeKey={ tabsActiveKey }
						onChange={ onChangeTab }
						tabBarExtraContent={ (
							<div className="r-flex r-ai-c r-jc-fe r-mb-8">
								{tabsActiveKey == TabPaneEnum.按平台sku && <TradeSortCom size="default" changeSort={ changeSort } queryLabelCheckSort={ queryLabelCheckSort } />}
								<Button
									className="r-mr-16"
									// style={ { width: 110 } }
									loading={ exportLoading }
									onClick={ _.debounce(() => exportList(), 500, { 'leading': true, 'trailing': false }) }
									data-point={ Pointer.标签对账_导出 }
								>导出对账单
								</Button>
								<Button
									className="r-mr-16"
									// style={ { width: 110 } }
									loading={ exportDetailLoading }
									onClick={ _.debounce(() => exportDetailList(), 500, { 'leading': true, 'trailing': false }) }
									data-point={ Pointer.标签对账_导出 }
								>导出对账明细
								</Button>
								{tabsActiveKey == TabPaneEnum.按平台商品 && (
									<Button
										className="r-mr-16"
										onClick={ () => {
											setTableSettingVisible(true);
										} }
									>表格设置
									</Button>
								)}
							</div>
						) }
					>
						{
							TabPaneList.map(i => {
								return (
									<Tabs.TabPane tab={ i.tabName } key={ i.key }>
										
										{
											searchParams.groupType == TabPaneEnum.按平台商品 && list?.length ? (
												<PlatGoodsList dataSource={ list } searchParams={ searchParams } />
											) : (
												list.length > 0 ? (
													<>
														<List
															tabsActiveKey={ tabsActiveKey } // 传入当前激活的 tab
															onSort={ handleSort }
															list={ list }
															dataTotal={ dataTotal }
															columns={ i.columns }
															productContentList={ productContentList }
															searchParams={ searchParams }
														/>
														{
															searchParams?.pageSize && searchParams?.pageNo && (
																<Pagination
																	style={ { paddingTop: 26, marginBottom: 20, marginRight: 40, textAlign: 'right' } }
																	showQuickJumper
																	showSizeChanger
																	pageSize={ searchParams?.pageSize }
																	current={ searchParams?.pageNo }
																	total={ pageInfo.total }
																	onChange={ onChange }
																	pageSizeOptions={ ['30', '50', '100', '200'] }
																/>
															)
														}
													</>
												) : renderNoList()
											)
										}
									</Tabs.TabPane>
								);
							})
						}
					</Tabs>
				</div>
				<div id="LabelAccountCheckContentPrintHtml" style={ { display: 'none' } } />	
			</Layout>

			{/* 表格设置弹窗 */}
			<Modal
				title="表格设置"
				visible={ tableSettingVisible }
				onOk={ handleTableSettingOk }
				onCancel={ handleTableSettingCancel }
				okText="确定"
				cancelText="取消"
				width={ 500 }
			>
				<div style={ { padding: '10px 0' } }>
					<div className="r-flex r-ai-c ">
						<span>合并设置：</span>
						<span style={ { marginRight: 5 } }>商家编码相同时：</span>
						<Select
							value={ mergeSettings.outerIdSameMerge }
							style={ { width: 180 } }
							onChange={ (value) => setMergeSettings(prev => ({ ...prev, outerIdSameMerge: value })) }
						>
							<Select.Option value={ 1 }>合并</Select.Option>
							<Select.Option value={ 0 }>不合并</Select.Option>
						</Select>
					</div>
				</div>
			</Modal>
		</div>
	);
});
export default LabelAccountCheck;
