import { ExclamationCircleFilled } from '@ant-design/icons';
import { Alert, Empty, Layout, Spin, Pagination } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import BottomCom from './components/BottomCom';
import SearchContainer from './components/SearchContainer';
import s from './index.module.scss';
import List from "./components/List";
import { TradeScmQueryScmTradeRequest, TradeScmQueryScmTradeResponse } from '@/types/schemas/distribution';
import { queryScmTradeApi } from '@/apis/distribution';
import pushedTradeStore from '@/stores/distribution/pushedTrade';
import { local } from '@/libs/db';
import BatchDistributorRegretModal from '../components/BatchDistributorRegretModal';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { tradeStore } from '@/stores';
import BatchModifyMemoModal from "../components/BatchModifyMemoModal";

interface IPageInfo {
	total: number,
	pageSize: number,
	pageNo: number
}

const PushedOrder = () => {
	const [emptyText, setEmptyText] = useState('请点击查询，查看订单数据');
	const [searchParams, setSearchParams] = useState<Partial<TradeScmQueryScmTradeRequest>>({});
	const [isSearching, setIsSearching] = useState(false);
	const [pageInfo, setPageInfo] = useState<IPageInfo>({ total: 0, pageNo: 1, pageSize: 30 });
	const { scmList, setList } = pushedTradeStore;
	const { isShowBatchModifyMemoModal, setIsShowBatchModifyMemoModal } = tradeStore;
	const resList = scmList;

	useEffect(() => {
		sendPoint(Pointer.代发订单_页面展现_展现次数);
	}, []);


	useEffect(() => {
		getTradeList();
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [searchParams]);


	const getTradeList = async() => {
		if (!searchParams.pageNo) { return; }
		setIsSearching(true);
		let aimRes: TradeScmQueryScmTradeResponse['data'];
		try {
			aimRes = await queryScmTradeApi(searchParams);
			if (aimRes?.scmTradeVos?.length) {
				setList(aimRes.scmTradeVos);
				setPageInfo(pre => ({ ...pre, total: aimRes.total }));
			} else {
				setList([]);
				setEmptyText('暂无数据');
			}
		} catch (e) {
			console.log('e: ', e);
		}
		setIsSearching(false);

	};
	useEffect(() => {
		const pageSize = local.get('pushedOrder.pageSize');
		if (pageSize) {
			setPageInfo((prev) => ({
				...prev,
				pageSize
			}));
		}
	}, []);

	const handleSearch = (value?:any) => {
		setSearchParams({
			"pageSize": pageInfo.pageSize,
			...value,
			"pageNo": 1,
		});
	};

	const renderNoList = () => {
		if (isSearching) {
			return <Spin style={ { width: '100%', minHeight: "200px", padding: "100px" } } tip="查询订单中" />;
		} else {
			return <Empty style={ { minHeight: "200px", padding: "100px" } } description={ emptyText } />;
		}
	};

	const onChange = (pageNo: number, pageSize: number) => {
		setPageInfo(prev => (
			{
				...prev,
				pageNo,
				pageSize
			}
		));
		local.set('pushedOrder.pageSize', pageSize);
		setSearchParams(prev => ({
			...prev,
			pageNo: pageSize === prev.pageSize ? pageNo : 1,
			pageSize,
		}));
	};

	const onModalClose = () => {
		getTradeList();
	};

	const modifyMemoOk = () => {
		getTradeList();
		setIsShowBatchModifyMemoModal(false);
	};

	return (
		<>
			<Layout className="kdzs-section" style={ { marginTop: 0, paddingTop: 0 } }>
				<div className={ s.topTip }><ExclamationCircleFilled style={ { color: "#FAAD14", marginRight: 8 } } />代发订单页面，仅用于处理需供应商代发的订单。</div>
				<SearchContainer handleSearch={ handleSearch } isSearching={ isSearching } />
			</Layout>
			<Layout className="kdzs-section" style={ { marginBottom: "80px" } }>
				{
					resList.length > 0 ? (
						<>
							<List list={ resList } searchParams={ searchParams } />
							{
								searchParams?.pageSize && searchParams?.pageNo && (
									<Pagination
										style={ { marginTop: 16, marginBottom: 30, marginRight: 40, textAlign: 'right' } }
										showQuickJumper
										showSizeChanger
										pageSize={ searchParams?.pageSize }
										current={ searchParams?.pageNo }
										total={ pageInfo.total }
										showTotal={ total => `共${total}条` }
										onChange={ onChange }
										pageSizeOptions={ ['10', '30', '50', '100', '200', '500', '1000'] }
									/>
								)
							}
						</>
					) : renderNoList()
				}
			</Layout>
			{
			    resList.length > 0
					? <BottomCom handleSearch={ handleSearch } searchParams={ searchParams } />
					: ''
			}
			<BatchDistributorRegretModal onClose={ onModalClose } />
			{isShowBatchModifyMemoModal ? <BatchModifyMemoModal onOk={ () => { modifyMemoOk(); } } /> : ''}
		</>
	);
};

export default observer(PushedOrder);
