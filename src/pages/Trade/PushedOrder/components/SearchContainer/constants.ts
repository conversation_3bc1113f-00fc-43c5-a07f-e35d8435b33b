export interface ISearchItem {
    key: string | number;
    value: string;
	searchName?: string;
	point?: string;
}

interface ISelectOption {
	default: string | string[];
	mode?: 'multiple' | 'tags';
	placeholder?: string;
	options: ISearchItem[];
}

export const TIME_TYPE_OPTIONS: ISelectOption = {
	default: '1',
	options: [{
		key: '1',
		value: '下单时间',
	}, {
		key: '2',
		value: '付款时间'
	}, {
		key: '5',
		value: '推单时间'
	}]
};

export const ORDER_STATUS_OPTIONS: ISelectOption = {
	default: 'WAIT_SELLER_SEND_GOODS',
	options: [{
		key: 'WAIT_SELLER_SEND_GOODS',
		value: '待发货',
	}, {
		key: 'WAIT_BUYER_CONFIRM_GOODS',
		value: '已发货',
	}, {
		key: 'TRADE_FINISHED',
		value: '交易成功',
	}, {
		key: 'ALL_STATUS',
		value: '全部状态',
	}]
};
