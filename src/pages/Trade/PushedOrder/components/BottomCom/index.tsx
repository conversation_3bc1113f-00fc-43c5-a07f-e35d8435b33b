import React, { Fragment, memo, useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, Dropdown, Menu, Modal, Radio } from 'antd';
import _ from "lodash";
import dayjs from "dayjs";
import message from '@/components/message';
import s from './index.module.scss';
import pushedTradeStore from '@/stores/distribution/pushedTrade';
import { CHOICE_TYPE } from '../../constants';
import Pointer from '@/utils/pointTrack/constants';
import { tradeStore } from '@/stores';
import userStore from '@/stores/user';


const BottomCom = (props:any) => {
	const { setModifyMemoPackage, setIsShowBatchModifyMemoModal } = tradeStore;
	const { setIsBatchDistributorRegretModal, setBatchDistributorRegretPackage } = pushedTradeStore;
	const { userId } = userStore.userInfo || {};
	const {
		handleChoiceChange,
		listCheckStatus,
		scmList,
		checkedCount
	} = pushedTradeStore;

	const selectAllPacks = () => {
		if (listCheckStatus === 0) {
			handleChoiceChange({
				type: CHOICE_TYPE.全选,
			});
		} else if (listCheckStatus === 1) {
			handleChoiceChange({
				type: CHOICE_TYPE.反选,
			});
		}
	};
	const handleHalfClick = (e: any) => {
		e.preventDefault();
		handleChoiceChange({
			type: CHOICE_TYPE.全选,
		});
	};

	const handleAction = async() => {
		const checkedList = scmList.filter(item => item.isChecked);
		if (!checkedList?.length) {
			message.info('请先选择');
			return;
		}
		setIsBatchDistributorRegretModal(true);
		setBatchDistributorRegretPackage(checkedList);
	};

	const batchSellerMemo = async() => {
		const checkedList = scmList.filter(item => item.isChecked);
		if (!checkedList?.length) {
			message.info('请先选择');
			return;
		}
		const memoPack = checkedList.map(item => {
			return {
				...item,
				platform: item.platform?.toLocaleLowerCase(),
				ptTid: item.tid,
				tid: item.scmTid,
				distributorUserId: userId,
				supplierUserId: item.supplierUserId,
				source: "SCM",
				trades: [{
					...item,
					platform: item.platform?.toLocaleLowerCase(),
					ptTid: item.tid,
					tid: item.scmTid,
					sellerMemoFlag: item.sellerFlag,
				}]
			};
		});
		setModifyMemoPackage(memoPack);
		setIsShowBatchModifyMemoModal(true);
	};

	return (
		<>
			<div className={ s['bottom-container'] } style={ { padding: '0 16px' } }>
				<div className={ s['bottom-left-container'] }>
					<label className="batch_tbtlt_check">
						{listCheckStatus === 0.5 ? <span className="half-checked" onClick={ handleHalfClick } /> : (
							<input
								type="checkbox"
								className="input_check packageCheckAll"
								checked={ listCheckStatus === 1 }
								value={ listCheckStatus }
								onChange={ selectAllPacks }
							/>
						)}
					</label>
					<div className="r-mr-16">{checkedCount}单</div>
				</div>
				<div>
					<Button
						className="r-mr-16"
						size="large"
						data-point={ Pointer.代发订单_点击_撤销代发_批量__点击次数 }
						style={ { width: 124 } }
						type="primary"
						onClick={ _.debounce(() => handleAction(), 500, { 'leading': true, 'trailing': false }) }
					>撤销代发
					</Button>
					<Button size="large" onClick={ () => batchSellerMemo() }>
						批量卖家备注
					</Button>
				</div>
				<div style={ { width: 100 } } />
			</div>


		</>
	);
};

export default observer(BottomCom);
