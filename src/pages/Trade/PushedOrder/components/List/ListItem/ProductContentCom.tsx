import React, { Fragment, memo, useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import style from '../index.module.scss';
import CombineTag from "@/components-biz/Trade/CombineTag";
import WaresInfo from "@/components-biz/WaresInfo";
import distributionStore from '@/stores/distribution';
import { ScmTradeVo } from '@/types/schemas/distribution';

const ProductContent = observer(
	(props:{pack:ScmTradeVo, colWidth: any}) => {
		const { pack, colWidth } = props;
		const { productSetting } = distributionStore;
		const {
			showPicture,
			showItemTitle,
			showOuterId,
			showSkuOuterId,
			showSkuTitle,
			showSkuAlias,
			showShortTitle,
		} = productSetting || {};

		return (
			<div className={ ` ${style['table-item']} ${style['productContentCol']}` } style={ { width: colWidth } }>
				{
					pack.scmOrderVos.map(order => {
						return (
							<div className={ style['product-content'] }>
								{(showPicture) ? (
									<div className="r-relative">
										<WaresInfo
											isShowHoverName
											hoverImgWidth={ 300 }
											hoverImgHeight={ 300 }
											wareName={ order.title }
											skuName={ order.skuPropertiesName }
											onlyImg
											imgUrl={ order.picPath }
										/>
									</div>
								) : ''}
								<div style={ { marginLeft: "6px" } }>
									<div>
										{/* 商品标题 */}
										{showItemTitle && order.title ? (
											<p className="r-mt-4 r-lh-16">
												<CombineTag visible={ pack?.groupItem } />
												{order.title}
											</p>
										) : ''}
										{/* 简称 */}
										{showShortTitle && order.itemAlias ? <p className="r-mt-4 r-lh-16">{order.itemAlias} </p> : ''}
										{/* 商品编码 */}
										{showOuterId && order.outerId ? <p className="r-mt-4 r-lh-16">{order.outerId} </p> : ''}
									</div>
									<div>
										{/* 规格名称 */}
										{showSkuTitle && order.skuPropertiesName ? <p className="r-mt-4 r-lh-16">{order.skuPropertiesName} </p> : ''}
										{/* 规格别名 */}
										{showSkuAlias && order.skuAlias ? <p className="r-mt-4 r-lh-16">{order.skuAlias} </p> : ''}
										{/* 规格编码 */}
										{showSkuOuterId && order.outerSkuId ? <p className="r-mt-4 r-lh-16">{order.outerSkuId} </p> : ''}
									</div>
								</div>
							</div>
						);
					})
				}

			</div>
		);
	}
);

export default ProductContent;
