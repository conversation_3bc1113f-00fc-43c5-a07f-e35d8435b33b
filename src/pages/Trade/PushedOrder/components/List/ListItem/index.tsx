import React, { Fragment, memo, useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { useHistory } from 'react-router-dom';
import { Dropdown } from 'antd';
import style from '../index.module.scss';
import { LIST_HEADER } from "../constants.ts";
import { ScmTradeVo } from '@/types/schemas/distribution';
import BuyerNickCom from './BuyerNickCom';
import ReceiverAddressCom from './ReceiverAddressCom';
import ProductContentCom from './ProductContentCom';
import pushedTradeStore from '@/stores/distribution/pushedTrade';
import userStore from '@/stores/user';
import event from '@/libs/event';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { copyToPaste, splitFxgTid } from '@/utils';
import { copyMenu } from '@/pages/Trade/utils';
import { BuyerMessageCom } from '@/pages/Trade/components/ListItem/components/simpleCom';

const enum ACTION_KEY {
	撤销待发,
	订单编号
}

const CheckboxCom = observer((props: {
	pack: any,
	index: number
}) => {
	const { togglePackageSelectStatus } = pushedTradeStore;
	const { pack, pack: { isChecked } } = props;
	return (
		<div
			className="batch_tbtlt_check"
			onClick={ () => {
				togglePackageSelectStatus(pack);
			} }
		>
			<input type="checkbox" value={ isChecked || '' } checked={ isChecked } />
		</div>

	);
});




const renderItem = (item:any, pack:ScmTradeVo, index:number, handleAction: any, userId: any) => {

	if (item.key === "index") {
		return <div className="batch_tbtlt_index" style={ { width: item.width } }>{ index + 1}</div>;
	}
	if (item.key === "nickName") {
		return <BuyerNickCom pack={ pack } colWidth={ item.width } />;
	}
	// 产品内容
	if (item.key === "info") {
		return <ProductContentCom pack={ pack } colWidth={ item.width } />;
	}
	if (item.key == 'address') {
		return <ReceiverAddressCom pack={ pack } colWidth={ item.width } />;
	}
	// 订单编号
	if (item.key === "tid") {
		const copyInfo = () => copyToPaste(splitFxgTid(pack[item.key]));
		return (
			<div className={ `${style['table-item']} r-click` } style={ { width: item.width } } >
				<Dropdown placement="topCenter" overlay={ copyMenu(copyInfo) }>
					<div onClick={ () => handleAction(ACTION_KEY.订单编号) }>{pack[item.key]}</div>
				</Dropdown>
			</div>
		);
	}
	// 供应商订单编号
	if (item.key === "scmTid") {
		const copyInfo = () => copyToPaste(splitFxgTid(pack[item.key]));
		return (
			<div className={ `${style['table-item']}` } style={ { width: item.width } }>
				<Dropdown placement="topCenter" overlay={ copyMenu(copyInfo) }>
					<div>{pack[item.key]}</div>
				</Dropdown>
			</div>
		);
	}
	// 供应商订单编号
	if (item.key === "sellerMemo") {
		return (
			<div style={ { width: item.width } }>
				<BuyerMessageCom 
					pack={ { 
						...pack,
						ptTid: pack.tid,
						tid: pack.scmTid,
						platform: pack.platform?.toLocaleLowerCase(),
						distributorUserId: userId,
						supplierUserId: pack.supplierUserId,
						source: "SCM",
						trades: [{ 
							...pack, 
							ptTid: pack.tid,
							tid: pack.scmTid,
							platform: pack.platform?.toLocaleLowerCase(),
							sellerMemoFlag: pack.sellerFlag,
						}] } }
					colWidth={ 150 }
				/>
			</div>
		);
	}
	//
	if (item.key === "operate") {
		return <div className={ `${style['table-item']} r-click` } style={ { width: item.width } } onClick={ () => handleAction(ACTION_KEY.撤销待发) }>撤销代发</div>;
	}
	return <div className={ `${style['table-item']}` } style={ { width: item.width } }>{ pack[item.key]}</div>;
};



const ListItem = (props:any) => {
	const history = useHistory();
	const { pack, index } = props;
	const { setIsBatchDistributorRegretModal, setBatchDistributorRegretPackage } = pushedTradeStore;
	const { userId } = userStore.userInfo || {};
	const handleAction = (key: ACTION_KEY) => {
		switch (key) {
			case ACTION_KEY.撤销待发:
				sendPoint(Pointer.代发订单_点击_撤销代发_单个_点击次数);
				setIsBatchDistributorRegretModal(true);
				setBatchDistributorRegretPackage([pack]);
				break;
			case ACTION_KEY.订单编号:
				history.push('/trade/index');
				setTimeout(() => {
					event.emit('tradeSetting.handleSearchParams', { tid: pack.tid });
				}, 0);
				break;
			default:
				break;
		}

	};
	return (
		<div style={ { display: "inline-flex", justifyContent: "space-between", minWidth: "100%" } } className={ `${style['table-col']} batch_tbtlt_check` }>
			<CheckboxCom pack={ pack } index={ index } />
			{
				LIST_HEADER.map((item) => (
					<Fragment key={ `${item.key}` }>
						{renderItem(item, pack, index, handleAction, userId)}
					</Fragment>

				))
			}
		</div>
	);
};

export default observer(ListItem);
