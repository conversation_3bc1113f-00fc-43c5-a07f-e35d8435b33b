import React, { Fragment, memo, useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import style from '../index.module.scss';
import PackageOrderHeader from './PackageOrderHeader';
import { LIST_HEADER } from '../constants';
import pushedTradeStore from '@/stores/distribution/pushedTrade';
import { CHOICE_TYPE } from '../../../constants';

const renderItem = (item:{text:string, key: string, width: number }) => {
	if (item.key === "index") {
		return <div className={ `batch_tbtlt_index ${style['table-header-item']}` } style={ { width: item.width } }>{item.text}</div>;
	}
	if (item.key === "info") {
		return (
			<div className={ `${style['table-header-item']} ${style['productContentCol']}` } style={ { width: item.width } }>
				产品内容
				<PackageOrderHeader />
			</div>
		);
	}
	if (item.key === 'address') {
		return <div className={ `${style['table-header-item']} ${style['addressCol']}` } style={ { width: item.width } }>{item.text}</div>;
	}
	if (item.key === 'sellerMemo') {
		return <div className={ `${style['table-header-item']}` } style={ { width: item.width, paddingLeft: 10, paddingRight: 10 } }>{item.text}</div>;
	}

	return <div className={ `${style['table-header-item']}` } style={ { width: item.width } }>{item.text}</div>;
};

const ListHeader = (props:any) => {

	const { listCheckStatus, handleChoiceChange } = pushedTradeStore;

	const selectAllPacks = () => {
		if (listCheckStatus === 0) {
			handleChoiceChange({
				type: CHOICE_TYPE.全选,
			});
		} else if (listCheckStatus === 1) {
			handleChoiceChange({
				type: CHOICE_TYPE.反选,
			});
		}
	};

	const handleHalfClick = (e: any) => {
		e.preventDefault();
		handleChoiceChange({
			type: 2,
		});
	};

	const renderInput = () => (
		<label className={ `${style['table-header-item']} batch_tbtlt_check` }>
			{listCheckStatus === 0.5 ? <span className="half-checked" onClick={ handleHalfClick } /> : (
				<input
					type="checkbox"
					className="input_check packageCheckAll"
					checked={ listCheckStatus === 1 }
					onChange={ selectAllPacks }
				/>
			)}
		</label>
	);
	return (
		<div className={ style['table-title-header-con'] }>
			{renderInput()}
			{
				LIST_HEADER.map((item) => (
					<Fragment key={ `${item.key}` }>
						{renderItem(item)}
					</Fragment>
				))
			}
		</div>
	);
};

export default observer(ListHeader);
