import React, { useEffect, useState } from "react";
import { useHistory } from 'react-router-dom';
import { Button, Checkbox, Form, Modal, Popover, Select, Spin } from "antd";
import { observer } from "mobx-react";
import { QuestionCircleFilled } from "@ant-design/icons";
import { INSPECT_METHOD_STOCK, INSPECT_METHOD_ZERO_STOCK } from "../../constants";
import styles from "./index.module.scss";
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import Flex from "@/components/Flex";
import { flagGroup } from "@/constants";
import Icon from "@/components/Icon";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";

const AdvancedSettingModal = () => {
	const {
		InspectSendStore: {
			setAdvancedSettingModalVisible,
			getAdvancedSettingConfig,
			updateAdvancedSettingConfig,
		},
	} = tradeStore;
	const { isShowZeroStockVersion } = userStore;
	const [form] = Form.useForm<any>();
	const [loading, setLoading] = useState(false);
	const [isShowMore, setIsShowMore] = useState(true);
	const [options, setOptions] = useState([]);
	const [config, setConfig] = useState<{[k:string]:any}>();
	const [isPrintItemTag, setIsPrintItemTag] = useState(false);

	const history = useHistory();
	useEffect(() => {
		setOptions(isShowZeroStockVersion ? INSPECT_METHOD_ZERO_STOCK.options : INSPECT_METHOD_STOCK.options);
	}, [isShowZeroStockVersion]);

	const onCancel = () => {
		setAdvancedSettingModalVisible(false);
	};

	const onOk = async() => {
		sendPoint(Pointer.验货发货_设置_保存);
		setLoading(true);
		await updateAdvancedSettingConfig({
			...config,
			isPrintItemTag,
		});
		setLoading(false);
		setAdvancedSettingModalVisible(false);
	};

	const handleConfigInfo = async() => {
		setLoading(true);
		const configInfo = await getAdvancedSettingConfig().catch();
		setLoading(false);
		console.log('xconfigInfo', configInfo);
		
		setConfig(() => configInfo);
		form.setFieldsValue({ ...configInfo });
		setIsPrintItemTag(configInfo?.isPrintItemTag || false);
	};

	useEffect(() => {
		handleConfigInfo();
	}, []);

	return (
		<Modal 
			visible
			width={ 622 }
			title="验货设置"
			onOk={ onOk }
			className={ styles.modal }
			onCancel={ onCancel }
		>
			<Form
				form={ form }
				onValuesChange={ (e) => {
					setConfig(pre => ({ ...pre, ...e }));
				} }
			>
				<div style={ { overflow: 'auto', maxHeight: '500px' } }>
					<Spin spinning={ loading }>
						<div>
							<Flex.Column className={ styles["condition-item"] }>
								<div className={ styles.title }>验货设置</div>
								<div style={ { paddingLeft: '24px' } }>
									<Form.Item label="验货条码" name="inspectBarCode">
										<Select getPopupContainer={ (e) => e.parentElement } style={ { width: '220px' } } options={ options } />
									</Form.Item>
									<div style={ { display: 'flex' } }>
										<Form.Item label="验货操作" name="hasScanProductIdCode" valuePropName="checked" >
											<Checkbox>开始扫描设备识别码</Checkbox>
										</Form.Item>
										{/* <Popover placement="right" content="开启后，扫描后自动发货功能不可用">
											<QuestionCircleFilled style={ { color: "#E02020", position: 'relative', top: '9px' } } />
										</Popover> */}
									</div>
								</div>
							</Flex.Column>
							<Flex.Column className={ styles["condition-item"] }>
								<div className={ styles.title }>打印设置</div>
								<div style={ { paddingLeft: '24px' } }>
									<Form.Item label="吊牌打印">
										<div className="r-flex r-ai-c">
											<Checkbox checked={ isPrintItemTag } onChange={ (e) => setIsPrintItemTag(e.target.checked) }>
												打印快递单同时打印商品吊牌
											</Checkbox>
											<Popover
												content={ (
													<div>
														<p>打印吊牌时需要安装Lodop控件，<a href="https://www.lodop.net/download/CLodop_Setup_for_Win64NT_6.609EN.zip" target="_blank" rel="noreferrer">点击下载</a>；</p>
														<p>组合货品默认打印子货品的吊牌；</p>
														<p>打印的吊牌的打印机需要单独设置；</p>
														<p>打印快递单同时打印吊牌会影响快递单打印速度；</p>
													</div>
												) }
												style={ { width: 360 } }
											>
												<QuestionCircleFilled style={ { color: "#FD8204", fontSize: "14px" } } />
											</Popover>
											<Button type="link" onClick={ () => { history.push('/settings/system?introName=printTagSet'); } }>吊牌设置</Button>
										</div>
									</Form.Item>
								</div>
							</Flex.Column>
							<Flex.Column className={ styles["condition-item"] }>
								<div className={ styles.title }>发货设置</div>
								{/* <div style={ { paddingLeft: '24px' } }>
									<Flex>
										<div className={ styles.subTitle }>发货设置：</div>
										<Flex.Column>
											<Form.Item label="" name="hasPartSend" valuePropName="checked" >
												<Checkbox>允许订单部分发货</Checkbox>
											</Form.Item>
											<Form.Item label="" valuePropName="checked" name="hasForceSendByItemStatus">
												<Checkbox>订单存在以下商品状态允许强制发货</Checkbox>
											</Form.Item>
											{
												config?.hasForceSendByItemStatus
													?													(
														<div style={ { paddingLeft: '16px' } }>
															<Form.Item label="" name="forceSendItemStatusList">
																<Checkbox.Group
																	options={ [
																		{ label: '退款中', value: 'REFUND_ING' }
																	] }
																/>
															</Form.Item>
														</div>
													)
													: null
											}
											
										</Flex.Column>
									</Flex>
								</div> */}
							</Flex.Column>
							<Flex className={ styles["condition-item"] }>
								<div className={ styles.subTitle }>
									自动发货设置：
								</div>
								<Flex.Column>
									<Form.Item label="" name="hasNoAutoSendByItemStatus" valuePropName="checked">
										<Checkbox>订单中存在以下商品状态时不自动发货</Checkbox>
									</Form.Item>
									{
										config?.hasNoAutoSendByItemStatus
											?											(
												<Form.Item label="" name="noAutoSendItemStatusList" style={ { paddingLeft: '12px' } } >
													<Checkbox.Group
														options={ [
															{ label: '退款中', value: 'REFUND_ING' },
															{ label: '已发货', value: 'WAIT_BUYER_CONFIRM_GOODS' },
															{ label: '交易成功', value: 'TRADE_FINISHED' },
															{ label: '已关闭', value: 'TRADE_CLOSED' }
														] }
													/>
												</Form.Item>
											)
											: null
									}
									<Form.Item
										label=""
										name="hasNoAutoSendByMemoAndRemark"
										valuePropName="checked" 
									>
										<Checkbox>
											订单有留言备注不自动发货
										</Checkbox>
									</Form.Item>
									{
										config?.hasNoAutoSendByMemoAndRemark
											? (
												<Form.Item label="" name="memoAndRemarkList" style={ { paddingLeft: '12px' } } >
													<Checkbox.Group
														options={ [
															{ label: '有留言', value: 'HAS_MEMO' },
															{ label: '有备注', value: 'HAS_REMARK' }
														] }
													/>
												</Form.Item>
											)
											: null
									}
									<Form.Item
										label=""
										name="hasNoAutoSendBySellerFlag"
										valuePropName="checked" 
									>
										<Checkbox>订单有旗帜不自动发货</Checkbox>
									</Form.Item>
									{
										config?.hasNoAutoSendBySellerFlag
											? (
												<Form.Item name="sellerFlagList" label="" style={ { paddingLeft: '12px' } }>
													<Checkbox.Group style={ { width: '410px' } }>
														{flagGroup.slice(1).map((i, index) => {
															return (
																<span key={ i.value } style={ { marginBottom: 8 } } hidden={ index > 4 && isShowMore }>
																	<Checkbox
																		key={ i.value }
																		value={ i.value }
																	><Icon type="flag1" style={ { color: i.color } } />{ i.name }旗
																	</Checkbox>
																</span>
															);
														})}
														<span className="r-click" onClick={ () => setIsShowMore(pre => !pre) }>{isShowMore ? '更多' : '收起'}</span>
													</Checkbox.Group>
												</Form.Item>
											)
											: null
									}
								</Flex.Column>
							</Flex>
						</div>
					</Spin>
				</div>
			</Form>
		</Modal>
	);

};
export default observer(AdvancedSettingModal);