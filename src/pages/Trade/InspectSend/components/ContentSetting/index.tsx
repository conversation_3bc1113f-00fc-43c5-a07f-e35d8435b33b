import React, { useEffect, useState } from "react";
import { Checkbox, Form, Modal, Radio, Spin } from "antd";
import { observer } from "mobx-react";
import styles from "../AdvancedSetting/index.module.scss";
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import Flex from "@/components/Flex";

const ContentSettingModal = () => {
	const {
		InspectSendStore: {
			setContentSettingModalVisible,
			getContentSettingConfig,
			updateContentSettingConfig,
		},
	} = tradeStore;
	const { isShowZeroStockVersion } = userStore;
	const [form] = Form.useForm<any>();
	const [loading, setLoading] = useState(false);
	const [config, setConfig] = useState<{[k:string]:any}>();

	const onCancel = () => {
		setContentSettingModalVisible(false);
	};

	const onOk = async() => {
		setLoading(true);
		await updateContentSettingConfig(config);
		setLoading(false);
		setContentSettingModalVisible(false);
	};

	const handleConfigInfo = async() => {
		setLoading(true);
		const configInfo = await getContentSettingConfig().catch();
		console.log('configInfo222', configInfo);
		setLoading(false);

		// 确保 customSetJson 存在
		if (!configInfo.customSetJson) {
			configInfo.customSetJson = {};
		}

		// 如果 previewPicSize 不存在，设置默认值为 1
		if (configInfo.customSetJson.previewPicSize === undefined) {
			configInfo.customSetJson.previewPicSize = 1;
		}

		// 将 customSetJson 中的 previewPicSize 值设置到表单中
		configInfo.previewPicSize = configInfo.customSetJson.previewPicSize;

		// 处理图片展示的初始值
		if (configInfo.showSysPicPath === 1) {
			configInfo.showPicture = 2; // 货品图片
		} else if (configInfo.showPicture === 1) {
			configInfo.showPicture = 1; // 商品图片
		} else if (!configInfo.showSysPicPath && !configInfo.showPicture) {
			configInfo.showPicture = 1; // 商品图片
		}

		setConfig(() => configInfo);
		form.setFieldsValue({ ...configInfo });
	};

	useEffect(() => {
		handleConfigInfo();
	}, []);

	const itemOpts = [
		{ label: '商品标题', value: 'showItemTitle' },
		{ label: '货品简称', value: 'showShortTitle' },
		{ label: '商家编码', value: 'showOuterId' },
		isShowZeroStockVersion ? false : { label: '货品编码', value: 'showSysOuterId' }
	].filter(Boolean);
	const attrOpts = [
		{ label: '规格名称', value: 'showSkuTitle' },
		{ label: '规格别名', value: 'showSkuAlias' },
		{ label: '规格编码', value: 'showSkuOuterId' },
		isShowZeroStockVersion ? false : { label: '货品规格编码', value: 'showSysOuterSkuId' }
	].filter(Boolean);

	return (
		<Modal
			visible
			width={ 500 }
			title="产品内容设置"
			onOk={ onOk }
			className={ styles.modal }
			onCancel={ onCancel }
		>
			<Form
				form={ form }
				onValuesChange={ (changedValues, allValues) => {
					console.log('Changed values:', changedValues);

					// 处理图片展示互斥逻辑
					if ('showPicture' in changedValues) {
						const newConfig = {
							...changedValues,
							showSysPicPath: changedValues.showPicture === 2 ? 1 : 0,
							showPicture: changedValues.showPicture === 1 ? 1 : 0
						};
						
						if ('previewPicSize' in newConfig) {
							const customSetJson = config?.customSetJson || {};
							setConfig(pre => ({
								...pre,
								...newConfig,
								customSetJson: {
									...customSetJson,
									previewPicSize: newConfig.previewPicSize
								}
							}));
						} else {
							setConfig(pre => ({ ...pre, ...newConfig }));
						}
						return;
					}

					// 原有的 previewPicSize 处理逻辑
					if ('previewPicSize' in changedValues) {
						const customSetJson = config?.customSetJson || {};
						setConfig(pre => ({
							...pre,
							...changedValues,
							customSetJson: {
								...customSetJson,
								previewPicSize: changedValues.previewPicSize
							}
						}));
					} else {
						setConfig(pre => ({ ...pre, ...changedValues }));
					}
				} }
			>
				<div style={ { overflow: 'auto', maxHeight: '500px' } }>
					<Spin spinning={ loading }>
						<div>
							{!isShowZeroStockVersion && (
								<Flex className={ styles["condition-item"] }>
									<div className={ styles.subTitle }>图片展示：</div>
									<Form.Item label="" name="showPicture">
										<Radio.Group
											options={ [
												{ label: '商品图片', value: 1 },
												{ label: '货品图片', value: 2 }
											] }
										/>
									</Form.Item>
								</Flex>
							)}
							<Flex className={ styles["condition-item"] }>
								<div className={ styles.subTitle }>缩略图：</div>
								<Form.Item label="" name="showPicSize">
									<Radio.Group
										options={ [
											{ label: '小图', value: 0 },
											{ label: '中图', value: 1 },
											{ label: '大图', value: 2 }
										] }
									/>
								</Form.Item>
							</Flex>
							<Flex className={ styles["condition-item"] }>
								<div className={ styles.subTitle }>预览图展示：</div>
								<Form.Item label="" name="previewPicSize">
									<Radio.Group
										options={ [
											{ label: '小图', value: 0 },
											{ label: '中图', value: 1 },
											{ label: '大图', value: 2 }
										] }
									/>
								</Form.Item>
							</Flex>
							<Flex className={ styles["condition-item"] }>
								<div className={ styles.subTitle }>
									商品维度：
								</div>
								<Flex wrap="wrap">
									{itemOpts.map(s => (
										<Form.Item key={ s.value } label="" name={ s.value } valuePropName="checked">
											<Checkbox>{ s.label}</Checkbox>
										</Form.Item>
									))}
								</Flex>
							</Flex>
							<Flex className={ styles["condition-item"] }>
								<div className={ styles.subTitle }>
									规格维度：
								</div>
								<Flex wrap="wrap">
									{attrOpts.map(s => (
										<Form.Item key={ s.value } label="" name={ s.value } valuePropName="checked">
											<Checkbox>{ s.label}</Checkbox>
										</Form.Item>
									))}
								</Flex>
							</Flex>
						</div>
					</Spin>
				</div>
			</Form>
		</Modal>
	);

};
export default observer(ContentSettingModal);
