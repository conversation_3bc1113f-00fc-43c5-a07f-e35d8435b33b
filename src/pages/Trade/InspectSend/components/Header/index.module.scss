.scanContent {
	width: 290px;
	min-width: 290px;
	padding-right: 24px;
	.input {
		height: 48px;
		font-size: 18px;
		font-weight: 500;
		margin: 8px 0;
		:global {
			.ant-input {
				&::placeholder {
					font-size: 18px;
					font-weight: 500;
				}
			}
		}
	}
	.totalNum,
	.alreadyNum {
		font-family: Helvetica Neue;
		flex: 1;
		font-size: 48px;
		font-weight: 500;
		text-align: center;
		color: rgba(0, 0, 0, 0.85);
		span {
			font-size: 14px;
			color: rgba(0, 0, 0, 0.45);
		}
	}
	.alreadyNum {
		color: #ff4d4f;
	}
	.sendBtn {
		height: 40px;
	}
}

.errorInfo {
	font-size: 16px;
	font-weight: 500;
	line-height: 24px;
	letter-spacing: 0px;
	color: #ff4d4f;
	margin-top: 16px;
	margin-bottom: 16px;
}

.header-border {
	position: relative;
	padding-left: 10px;
	&::before {
		content: "";
		position: absolute;
		width: 2px;
		height: 16px;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		background-color: #fd8204;
	}
}
