/**
 * !! 验货发货2.0垃圾代码，需要重构
 */
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Button, Checkbox, Input, Modal, Radio, Form } from "antd";
import { observer } from "mobx-react";
import dayjs from "dayjs";
import { useHistory } from "react-router-dom";
import { useUpdateEffect } from "ahooks";
import { BulbOutlined, SettingOutlined } from "@ant-design/icons";
import { cloneDeep, isEmpty } from "lodash";
import styles from "./index.module.scss";
import { tradeStore } from "@/stores";
import scanPrintStore from "@/stores/trade/scanPrint";
import userStore from "@/stores/user";
import message from '@/components/message';
import { getUniqueKeyByOrderInfo, orderAllowInspect } from "../../utils";
import voiceSendSuccess from '@/assets/mp3/发货成功.mp3';
import voiceHandleFail from '@/assets/mp3/处理失败.mp3';
import { playAudio, textTransformAudioPlay } from "@/pages/Trade/ScanPrint/utils";
import { sendDelivery } from "@/pages/Trade/components/BottomCom/utils";
import { INSPECT_METHOD_ENUM, InspectSendEventBus, KM, TRADE_SEND_STATUS } from "../../constants";
import SelectNeedInspectModal from "../SelectNeedInspectModal";
import event from "@/libs/event";
import { getMirrorEnum } from "@/utils";
import useGetState from "@/utils/hooks/useGetState";
import voice_已发货 from "@/assets/mp3/订单存在已发货.mp3";
import voice_有退款 from "@/assets/mp3/订单存在有退款.mp3";
import voice_扫描 from "@/assets/mp3/扫描.mp3";
import Flex from "@/components/Flex";
import { TradeQueryTradeListByExNumberApi, TradeUpdateTradeScanRecordApi } from "@/apis/trade/inspectSend";
import InspectedProgress from "../InspectedProgress";
import AdvancedSetting from "../AdvancedSetting";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { TradeStatus } from "@/utils/enum/trade";
import { flagGroup } from "@/constants";
import { getSendContent } from "../TradeDetailCard/itemInfo";
import { UpdateOrderSerialNumberApi } from "@/apis/trade";
import SelectPrinter from "@/pages/Trade/PostPrint/components/Search/components/SelectPrinter";
import SelectTagTemplate from "@/pages/Trade/PostPrint/components/Search/components/SelectTagTemplate";
import PrintCenter from '@/print/index';
import { PrintTag } from "@/print/tagPrint";
import { BQ_PRINT_TYPE } from "@/pages/Trade/constants";

let checkTimer:any = null;

const Header = (props:any) => {
	const [form] = Form.useForm();
	const {
		expressTemplateList,
		InspectSendStore: {
			setTotalNum,
			contentSetting,
			advancedSettingModalVisible,
			setAdvancedSettingModalVisible,
			inspectedProgress, getInspectedProgress, setInspectedProgress,
			hasBeenAfterSale, setHasBeenAfterSale,
			hasNoAwatingSend, setHasNoAwatingSend,
			interceptInfo, setInterceptInfo, clearInterceptInfo,
			tradeList, setTradeList,
			getAdvancedSettingConfig,
			updateAdvancedSettingConfig,
			advancedSettingConfig,
			setCurScanGoodInfo,
			curScanGoodInfo,
			setShowLoading,
			sendPending, setSendPending,
			ignoreInspectOrder,
			setIgnoreInspectOrder,
			initStore,
			clearTotalNum,
			selectedRowKeys,
			setSelectedRowKeys,
			inspectedCount,
			getInspectedCount,
		},
	} = tradeStore;
	const { printersList } = scanPrintStore;
	const { systemSetting } = userStore;
	const history = useHistory();
	getMirrorEnum(TRADE_SEND_STATUS);
	const uniqueCodeInputRef:any = useRef();
	const expressInputRef:any = useRef();
	const productIdCodeInputRef:any = useRef();
	const [expressNo, setExpressNo] = useState('');
	const [goodsUniqueCode, setGoodsUniqueCode] = useState('');
	const [productIdCode, setProductIdCode] = useState('');
	const [searchLoading, setSearchLoading] = useState(false);
	const [templateObj, setTemplateObj] = useState({});
	const [isForceSend, setIsForceSend] = useState(false);
	const [allGoodOrderList, setAllGoodOrderList] = useState([]);
	const [selectInspectModalVisible, setSelectInspectModalVisible] = useState(false);
	const [needSelectOrders, setNeedSelectOrders] = useState([]);
	const [errorMsg, setErrorMsg] = useState('');
	const [_, setAdvancedSettingConfigTemp, getAdvancedSettingConfigTemp] :any = useGetState({});
	const [printTagSet, setPrintTagSet] = useState({});
	const [lodopWarn, setLodopWarn] = useState(false);

	useEffect(() => {
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.吊牌);
		userStore.getSystemSetting();
	}, []);

	useEffect(() => {
		try {
			const printTagSet = JSON.parse(systemSetting?.printTagSet);
			setPrintTagSet(printTagSet);
			if (printTagSet?.tagTemplateSwitch === "1") {
				form.setFieldsValue({ tagTempId: printTagSet?.tagTemplate?.Mode_ListShowId });
				if (printTagSet?.tagTemplate?.defaultPrinter) {
					if (printersList.find(printer => printer == printTagSet?.tagTemplate?.defaultPrinter)) {
						form.setFieldsValue({ tagPrinter: printTagSet?.tagTemplate?.defaultPrinter });
					} else {
						form.setFieldsValue({ tagPrinter: printersList?.[0] });
					}
				} else {
					form.setFieldsValue({ tagPrinter: printersList?.[0] });
				}
			} else {
				form.setFieldsValue({ tagPrinter: printersList?.[0] });
			}
		} catch (error) {
			userStore.getSystemSetting(true);
			console.log(error);
		}
	}, [systemSetting]);

	// useEffect(() => {
	// 	if (advancedSettingConfig?.isPrintItemTag && !lodopWarn) {
	// 		checkLodop();
	// 	}
	// }, [advancedSettingConfig]);

	// const checkLodop = () => {
	// 	if (checkTimer) {
	// 		clearTimeout(checkTimer);
	// 		checkTimer = null;
	// 	}
	// 	checkTimer = setTimeout(() => {
	// 		if (window.printAPI?.checkLodopProgress) {
	// 			PrintCenter.checkLodopProgress(false);
	// 			setLodopWarn(true);
	// 		} else {
	// 			checkLodop();
	// 		}
	// 	}, 1000);
	// };

	const expressInputFocus = () => {
		expressInputRef?.current?.focus();
		expressInputRef?.current?.select();
	};

	const uniqueCodeInputFocus = () => {
		uniqueCodeInputRef?.current?.focus();
		uniqueCodeInputRef?.current?.select();
	};

	const productIdCodeInputFocus = () => {
		productIdCodeInputRef?.current?.focus();
		productIdCodeInputRef?.current?.select();
	};
	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `验货发货【${dataType}】`,
			data: {
				sendPending,
				advancedSettingConfig,
				tradeList,
				...data
			}
		});
	};

	// 计算强制验货总验货数量
	const calcForceInspectCount = (packList) => {
		const inspectedProcess = {};
		const newKeys = new Set();
		// 先考虑现有已验货数量
		const { total, current } = getInspectedCount();
		packList.forEach(packInfo => {
			packInfo.trades.forEach(tradeInfo => {
				tradeInfo.orders.forEach(orderInfo => {
					const key = getUniqueKeyByOrderInfo(orderInfo);
					orderInfo.rowKey = key;
					if (orderInfo.isCombination) {
						let isChecked = true;
						orderInfo.groupRelationRecordList.forEach(groupItem => {
							const key = getUniqueKeyByOrderInfo(groupItem);
							groupItem.rowKey = key;
							inspectedProcess[key] = {
								total: Number(groupItem.num),
								current: groupItem.status == 'WAIT_SELLER_SEND_GOODS' ? Number(groupItem.num) : 0,
								rowData: {
									...groupItem,
									rowKey: key
								}
							};
							if (inspectedProcess[key].current == inspectedProcess[key].total) {
								newKeys.add(key);
							} else {
								isChecked = false;
								newKeys.delete(key);
							}
						});
						if (isChecked) {
							newKeys.add(key);
						} else {
							newKeys.delete(key);
						}
						return;
					}
					inspectedProcess[key] = {
						total: Number(orderInfo.num),
						current: orderInfo.status == 'WAIT_SELLER_SEND_GOODS' ? Number(orderInfo.num) : 0,
						rowData: {
							...orderInfo,
							rowKey: key
						}
					};
					if (inspectedProcess[key].current == inspectedProcess[key].total) {
						newKeys.add(key);
					} else {
						newKeys.delete(key);
					}
				});
			});
		});
		setTotalNum('verifyGoodsNum', total - current);
		setTradeList(packList);
		setSelectedRowKeys(Array.from(newKeys));

		const newIgnoreInspectOrder = { ...ignoreInspectOrder };
		for (let key in newIgnoreInspectOrder) {
			newIgnoreInspectOrder[key] = true;
		}
		setIgnoreInspectOrder(newIgnoreInspectOrder);
		console.log('inspectedProcess', inspectedProcess);

		setInspectedProgress(inspectedProcess, true);
		return Array.from(newKeys);
	};


	// 计算总验货数量
	const calcInspectCount = (packList) => {
		const inspectedProcess = {};
		const newKeys = new Set();
		packList.forEach(packInfo => {
			packInfo.trades.forEach(tradeInfo => {
				tradeInfo.orders.forEach(orderInfo => {
					const key = getUniqueKeyByOrderInfo(orderInfo);
					orderInfo.rowKey = key;
					if (orderInfo.isCombination) {
						let isChecked = true;
						orderInfo.groupRelationRecordList.forEach(groupItem => {
							const key = getUniqueKeyByOrderInfo(groupItem);
							groupItem.rowKey = key;
							inspectedProcess[key] = {
								total: Number(groupItem.num),
								current: (!advancedSettingConfig.hasCheckItem && groupItem.status == 'WAIT_SELLER_SEND_GOODS') ? Number(groupItem.num) : 0,
								rowData: {
									...groupItem,
									rowKey: key
								}
							};
							if (inspectedProcess[key].current == inspectedProcess[key].total) {
								newKeys.add(key);
							} else {
								isChecked = false;
								newKeys.delete(key);
							}
						});

						if (isChecked) {
							newKeys.add(key);
						} else {
							newKeys.delete(key);
						}
						console.log('notChecked', isChecked, newKeys);
						return;
					}
					inspectedProcess[key] = {
						total: Number(orderInfo.num),
						current: (!advancedSettingConfig.hasCheckItem && orderInfo.status == 'WAIT_SELLER_SEND_GOODS') ? Number(orderInfo.num) : 0,
						rowData: {
							...orderInfo,
							rowKey: key
						}
					};
					if (inspectedProcess[key].current == inspectedProcess[key].total) {
						newKeys.add(key);
					} else {
						newKeys.delete(key);
					}
				});
			});
		});
		setTradeList(packList);
		setSelectedRowKeys(Array.from(newKeys));
		console.log('inspectedProcessinspectedProcess', inspectedProcess);

		setInspectedProgress(inspectedProcess);
	};

	const handleRes = (packList) => {
		/**
		 * 原订单数据处理注意事项：
		 * 1.把不属于当前快递单的order全部过滤掉，只保留快递单当时申请时对应的order
		 * 2.对过滤后的order需要做发货状态校验和售后状态校验
		 * 3.过滤后的order只要有一个是已发货或者有售后状态，就要提示
		 * 4.pack和order需要加上isChecked = true
		 * 5.pack需要加上expressTemplate和sids，发货需要
		 * 6.把所有的货品找出来，如果用户扫描条码或者货号的时候，出现了多个不同的货品，需要弹窗让用户选择
		 * 7.根据order的发货状态，显示主订单的发货状态
		 * 8.如果只扫描到一笔订单，默认展开卡片，如果扫描到多笔订单，已发货的订单不默认展开
		 */
		const orderList = [];
		const ignoreInspectOrder = { };
		let splitSend = false; // 是否是拆单发货
		let hasBeenSend = false; // 匹配到的订单已经存在已发货
		let hasBeenAfterSale = false;// 匹配到的订单是否已经产生售后
		let hasBeenSendList = [];
		let hasBeenAfterSaleList = [];
		clearInterceptInfo();
		packList.forEach(pack => {
			pack["isActive"] = true;
			// 勾选上，发货需要
			pack["isChecked"] = true;
			// 给pack加上expressTemplate和sids属性，发货需要
			const expressTemplateInfo = {};
			pack.trades.forEach(trade => {
				const newOrders = [];
				// 已发货订单
				const allSendOrders = [];
				// 待发货订单
				const needSendOrders = [];
				// 对order进行排序，对于组合货品而言，订单内同时存在组合货品以及子货品，默认先验单独的子货品数量，再验组合货品下的子货品
				trade.orders = trade.orders.sort((pre, next) => {
					if (isEmpty(pre.groupRelationRecordList)) {
						return -1;
					}
					return 1;
				});
				trade.orders.forEach(order => {
					console.log('ordeorder', order);

					const rowKey = getUniqueKeyByOrderInfo(order);
					// 对应注意事项1
					if (order.inspectionShippingOrder) {
						/** ***** 新增自动发货拦截 start ******/
						// 退款中
						if (order.refundStatus == "REFUND_ING") {
							setInterceptInfo("refoundList", order);
						}
						// 交易成功
						if (order.status == TradeStatus.交易成功) {
							setInterceptInfo("finishList", order);
						}
						// 已关闭
						const isClosed = order.status === TradeStatus.交易关闭 || order.status === TradeStatus.交易自动关闭;
						if (isClosed) {
							setInterceptInfo("closeList", order);
						}

						/** ***** 新增自动发货拦截 end ******/
						// 有售后
						// !! 如果开启了退款中允许强制发货则这里需要排除一下
						if (
							(advancedSettingConfig?.hasForceSendByItemStatus && advancedSettingConfig?.forceSendItemStatusList?.includes('REFUND_ING') && !["NOT_REFUND"].includes(order.refundStatus) && order.refundStatus != 'REFUND_ING')
							|| (advancedSettingConfig?.hasForceSendByItemStatus && !advancedSettingConfig?.forceSendItemStatusList?.includes('REFUND_ING') && !["NOT_REFUND"].includes(order.refundStatus))
							|| (!advancedSettingConfig?.hasForceSendByItemStatus && !["NOT_REFUND"].includes(order.refundStatus))
						) {
							hasBeenAfterSale = true;
							pack.hasBeenAfterSale = true;
							setHasBeenAfterSale(true);
							hasBeenAfterSaleList.push(order);
						}
						// 待发货
						if (["WAIT_SELLER_SEND_GOODS"].includes(order.status)) {
							needSendOrders.push(order);
						}
						// 已发货
						if (["WAIT_BUYER_CONFIRM_GOODS", "SELLER_CONSIGNED_PART"].includes(order.status)) {
							console.log('xxxxxxx', order.status);

							setInterceptInfo("sendList", order);
							if (packList.length > 1) {
								pack["isActive"] = false;
							}
							hasBeenSend = true;
							pack.hasBeenSend = true;
							setHasNoAwatingSend(true);
							allSendOrders.push(order);
							hasBeenSendList.push(order);
						}
						// 部分发货
						if (["SELLER_CONSIGNED_PART"].includes(order.status)) {
							if (packList.length > 1) {
								pack["isActive"] = false;
							}
							hasBeenSend = true;
							pack.hasBeenSend = true;
							setHasNoAwatingSend(true);
						}
						if (order.noGoodsLink) {
							ignoreInspectOrder[rowKey] = false;
						}
						const templateInfo = templateObj[order.templateId] || {};
						expressTemplateInfo["exId"] = order.templateId;
						expressTemplateInfo["exCode"] = order.inspectionExCode;
						expressTemplateInfo["kddType"] = order.inspectionKddType;
						expressTemplateInfo["exName"] = templateInfo.ExcodeName;
						pack["expressTemplate"] = expressTemplateInfo;
						pack["sids"] = [order.inspectionExNumber];
						newOrders.push({
							...order,
							templateInfo,
							isChecked: false, // 这里必须需要给order设置isChecked。因为其他很多公共方法都有判断
						});
						orderList.push(order);
						if (order.isCombination) {
							const { groupRelationRecordList, ...rest } = order;
							order.groupRelationRecordList.forEach(groupItem => {
								// groupItem["price"] = groupItem.costPrice;
								groupItem["status"] = order.status;
								groupItem["num"] = Number(groupItem.groupProportionNum * order.num).toFixed();
								groupItem["titleShort"] = groupItem.sysItemAlias;
								groupItem["orderInfo"] = cloneDeep(rest);
								groupItem["sysOuterSkuId"] = groupItem.skuOuterId;
								// 给这个加上skuId，是因为验货时，用skuId来区分是不是不同的货品（因为同一条码或者货号可能对应不同的货品）
								groupItem["systemNumIid"] = groupItem.sysItemId;
								groupItem["systemSkuId"] = groupItem.sysSkuId;
								groupItem["isGroup"] = true;
								orderList.push(groupItem);
							});
						}

					} else { // 只要有一个order不属于当前快递单，就说明是要拆单发货
						splitSend = true;
					}
				});
				// 有留言
				if (trade.buyerMessage) {
					setInterceptInfo("buyerMessageList", trade.buyerMessage);
				}
				// 有备注
				if (trade.sellerMemo) {
					setInterceptInfo("sellerMemoList", trade.sellerMemo);
				}
				// 有旗帜
				if (trade.sellerFlag) {
					setInterceptInfo("sellerFlagList", trade.sellerFlag);
				}
				// 赋值pack，好显示订单是不是已发货
				if (trade.status === 'SELLER_CONSIGNED_PART') {
					pack["sendStatus"] = TRADE_SEND_STATUS["部分发货"];
				} else if (allSendOrders.length && allSendOrders.length === newOrders.length) {
					pack["sendStatus"] = TRADE_SEND_STATUS["已发货"];
				} else if (needSendOrders.length && needSendOrders.length === newOrders.length) {
					pack["sendStatus"] = TRADE_SEND_STATUS["待发货"];
				}
				trade.splitSend = splitSend;
				trade.orders = newOrders;
			});
		});
		if (hasBeenAfterSale) {
			expressInputFocus();
			setTimeout(() => { playAudio(voice_有退款); }, 1000);
			setErrorMsg('该快递单对应的订单已处理售后状态，请前往订单打印页面发货');
			customLogPost('该快递单对应的订单已处理售后状态，请前往订单打印页面发货', { hasBeenAfterSale, hasBeenAfterSaleList });
		// } else if (hasBeenSend && !advancedSettingConfig.hasPartSend) {
		} else if (hasBeenSend) {
			expressInputFocus();
			setTimeout(() => { playAudio(voice_已发货); }, 1000);
			setErrorMsg("该快递单号下存在已发货商品，请前往订单打印处理发货");
			customLogPost('该快递单号下存在已发货商品，请前往订单打印处理发货', { hasBeenSend, hasBeenSendList });
		}
		setSelectedRowKeys([]);
		if (advancedSettingConfig.hasCheckItem) {
			setTimeout(() => uniqueCodeInputRef?.current?.focus());
		} else if (advancedSettingConfig.hasScanProductIdCode) {
			setTimeout(() => productIdCodeInputRef?.current?.focus());
		}
		setAllGoodOrderList(orderList);
		setTradeList(packList);
		calcInspectCount(packList);
		console.log('6666_111');
		setIgnoreInspectOrder(ignoreInspectOrder);
		// 判断是否存在已发货的订单
	};
	// 处理当前扫描的货品信息
	const handleGoodInfo = (row) => {
		const goodInfo = { ...row };
		setCurScanGoodInfo(goodInfo);
	};
	const expressNoOnChange = (e) => {
		setExpressNo(e.target.value);
	};

	const goodsUniqueCodeOnChange = (e) => {
		setGoodsUniqueCode(e.target.value);
	};
	const productIdCodeOnChange = (e) => {
		setProductIdCode(e.target.value);
	};

	const onSearchTradeByExpressNo = () => {
		if (sendPending) {
			return;
		}
		if (expressNo) {
			setErrorMsg('');
			sendPoint(Pointer.验货发货_查询);
			initStore();
			setGoodsUniqueCode("");
			setProductIdCode("");
			setSearchLoading(true);
			setTotalNum('inspectionPackageNum', 1);
			const params = {
				sid: expressNo,
				startTime: dayjs().subtract(1, 'M').startOf('day').format("YYYY-MM-DD HH:mm:ss"),
				endTime: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
				pageNo: 1,
				pageSize: 200,
				uniqueCode: props.uniqueCode
			};
			setShowLoading(true);
			TradeQueryTradeListByExNumberApi(params).then(res => {
				if (res?.list?.length > 0) {
					handleRes(res.list);
					playAudio(voice_扫描);
				} else {
					expressInputFocus();
					message.error("未查询到订单");
					setErrorMsg("未查询到订单");
					setSearchLoading(false);
				}
			}, () => {
				playAudio(voiceHandleFail);
				setSearchLoading(false);
			}).finally(() => setShowLoading(false))
				.catch(() => setSearchLoading(false));
		}

	};

	// 对比sku,兼容没有skuid的情况
	const isContrastSku = (rowData, orderData) => {
		let rowKey = `${rowData.systemNumIid}_${rowData.systemSkuId}`;
		let orderKey = `${orderData.systemNumIid}_${orderData.systemSkuId}`;
		return rowKey === orderKey;
	};

	const startInspect = (orderInfo) => {
		const inspectedProgressTemp = { ...inspectedProgress };
		let curGoodHasBeenInspected = true; // 当前扫描到的货品是否已全部验货
		let curGoodIsMatch = false; // 当前扫描到的货品是否匹配到了
		for (let key in inspectedProgressTemp) {
			const { rowData, current, total } = inspectedProgressTemp[key];
			// 找到能匹配到的货品，可能会存在相同的货品处于不同的订单之中
			// const isMatch = rowData[curinspectBarCode] === goodsUniqueCode;
			const isMatch = isContrastSku(rowData, orderInfo);
			// 如果当前行的已验货数量小于需要验货的数量，说明当前行需要验货
			const noInspctedSuccess = current < total;
			// 当前行只有待发货和没有售后状态，才允许验货
			const allowInspect = orderAllowInspect(orderInfo);
			// 如果能找到当前扫描的货品，就把货品信息设置到store中，方便右面的验货进度版块展示货品信息
			if (isMatch) {
				curGoodIsMatch = true;
				handleGoodInfo(rowData);
			}
			if (isMatch && !allowInspect) {
				message.warning("该商品已发货或已产生售后，无需再次验货!");
				setErrorMsg("该商品已发货或已产生售后，无需再次验货!");
				curGoodHasBeenInspected = false;
				customLogPost('该商品已发货或已产生售后，无需再次验货', { rowData, orderInfo });
				break;
			}
			// 可能会存在相同的货品处于不同的订单之中，所以剔除掉已经验货成功的row
			if (isMatch && noInspctedSuccess && allowInspect) {
				curGoodHasBeenInspected = false;
				const tableTrDom = document.querySelector(`tr[data-row-key="${rowData.rowKey}"]`);
				if (tableTrDom) {
					tableTrDom?.scrollIntoView({ behavior: 'auto' });
					tableTrDom?.classList.add("kdzs-table-tr-active");
					setTimeout(() => {
						tableTrDom?.classList.remove("kdzs-table-tr-active");
					}, 500);
				}
				inspectedProgressTemp[key] = {
					...inspectedProgressTemp[key],
					current: current + 1
				};
				setTotalNum('verifyGoodsNum', 1);
				if (inspectedProgressTemp[key].current && inspectedProgressTemp[key].current == inspectedProgressTemp[key].total) {
					setSelectedRowKeys([...selectedRowKeys, key]);
					// !! 组合货品，如果子单扫描完，主货品需要选中
					if ((inspectedProgressTemp[key] as any)?.rowData?.isGroup) {
						const fatherOrder = inspectedProgressTemp[key]?.rowData?.orderInfo;
						if (fatherOrder) {
							const allGroupRowKeys = Object.keys(inspectedProgressTemp).filter(key => inspectedProgressTemp[key]?.rowData?.orderInfo?.oid == fatherOrder.oid);
							let verifyGroupFlag = true; // 所有子货品是否验货完毕
							allGroupRowKeys.forEach(element => {
								if (inspectedProgressTemp[element].current != inspectedProgressTemp[element].total) {
									verifyGroupFlag = false;
								}
							});
							if (verifyGroupFlag) {
								const fatherRowKey = getUniqueKeyByOrderInfo(fatherOrder);
								setSelectedRowKeys([...selectedRowKeys, fatherRowKey]);
							}
						}
					}
				}

				break;
			}
		}
		if (curGoodIsMatch && curGoodHasBeenInspected) {
			setErrorMsg("该商品已全部验货，无需再次验货!");
			customLogPost('该商品已全部验货，无需再次验货', { orderInfo });
			return;
		}
		// 如果开启了识别码
		if (advancedSettingConfig?.hasScanProductIdCode) {
			productIdCodeInputFocus();
		}
		console.log('inspectedProgressTemp', inspectedProgressTemp);

		setInspectedProgress(inspectedProgressTemp);
	};

	const goodsUniqueCodeOnSubmit = () => {
		/**
		 * 注意事项：
		 * 1.如果当前扫描到的货品已全部验货，需要进行提示。(这个处理需要特别注意多笔订单
		 * 中存在同一个货品的情况，只有所有订单都验货完毕才算是已验货);
		 * 2.如果用户扫条码或者货号的时候，出现了多个不一样的货品，需要弹窗展示这些货品，让用户自己选
		 * 择出需要验货的货品
		 */
		setErrorMsg('');
		sendPoint(Pointer.验货发货_商品码_查询);
		uniqueCodeInputFocus();
		const curinspectBarCode = KM[advancedSettingConfig.inspectBarCode];
		let needInspectOrder = {};
		let errorMsg = "";
		const curInspectGoodOrders = [];
		allGoodOrderList.forEach(order => {
			const isMatch = order[curinspectBarCode] == goodsUniqueCode?.trim();
			if (isMatch) {
				if (order.isCombination) {
					errorMsg = "组合货品默认请扫描子货品进行验货";
					return;
				}
				curInspectGoodOrders.push(order);
			}
		});
		if (errorMsg) {
			message.error(errorMsg);
			setErrorMsg(errorMsg);
			return;
		}
		if (!curInspectGoodOrders.length) {
			message.error("未匹配到货品");
			setErrorMsg('未匹配到货品');
			textTransformAudioPlay('商品错误');
			return;
		}

		// 判断匹配到的货品是不是大于1种（根据skuId）；
		const curInspectGoodSkuIds = [...new Set(curInspectGoodOrders.map(order => `${order.systemNumIid}_${order.systemSkuId}`))];
		if (curInspectGoodSkuIds.length > 1) {
			setSelectInspectModalVisible(true);
			setNeedSelectOrders(curInspectGoodOrders);
			customLogPost('判断匹配到的货品是不是大于1种,弹出选择需要验货的弹框', { curInspectGoodSkuIds, curInspectGoodOrders });
			return;
		} else {
			needInspectOrder = curInspectGoodOrders[0];
		}
		setGoodsUniqueCode("");
		startInspect(needInspectOrder);
	};

	const goodsProductIdCodesOnSubmit = () => {
		sendPoint(Pointer.验货发货_识别码_查询);
		curScanGoodInfo.productIdCode = productIdCode?.trim();
		// const modifyIndex = tradeList.findIndex(s => s.oid == curScanGoodInfo.oid);
		let curProductIdCode = productIdCode?.trim();
		tradeList.forEach(pack => {
			pack.trades?.forEach(trade => {
				trade?.orders?.forEach(order => {
					if (advancedSettingConfig.hasCheckItem) {
						if (order.oid == curScanGoodInfo.oid || order.oid == curScanGoodInfo?.orderInfo?.oid) {
							let codes;
							if (order.productIdCode) {
								codes = [...order?.productIdCode?.split(','), curScanGoodInfo.productIdCode].filter(Boolean).join(',');
							} else {
								codes = curScanGoodInfo.productIdCode;
							}
							if (codes.split(',')?.length > order.num) {
								message.error('识别码不得超过商品数量');
							} else {
								order.productIdCode = codes;
								if (codes.split(',')?.length === +order.num) {
									modifyProductCodes({ ...order, ...order?.orderInfo || {} }, order.productIdCode);
								}
								if (order.productIdCode?.split(',').length >= order.num) {
									uniqueCodeInputFocus();
								}
							}
						}
					} else if (curProductIdCode) {
						let codes;
						if (order.productIdCode) {
							codes = [...order?.productIdCode?.split(','), curProductIdCode].filter(Boolean).join(',');
						} else {
							codes = curProductIdCode;
						}
						if (codes.split(',')?.length <= order.num) {
							order.productIdCode = codes;
							curProductIdCode = "";
							setCurScanGoodInfo({ ...order });
							if (codes.split(',')?.length === +order.num) {
								modifyProductCodes({ ...order, ...order?.orderInfo || {} }, order.productIdCode);
							}
						}
					}
				});
			});
		});
		setProductIdCode('');
		console.log('productIdCode_setTradeList', curScanGoodInfo, tradeList);
		// tradeList[modifyIndex].productIdCode = curScanGoodInfo.productIdCode;
		setTradeList([...tradeList]);
		setCurScanGoodInfo({ ...curScanGoodInfo });
		const needSerialNumberList = tradeList[0]?.trades.filter(trade => trade.needSerialNumber);
		if (inspectedCount?.total > 0 && inspectedCount?.total == inspectedCount.current) {
			if (needSerialNumberList.every(trade => trade.orders.every(order => {
				return order.needSerialNumber && order.productIdCode && order.productIdCode.split(',').length === +order.num;
			}))) {
				willSend(true, false);
			}
		} else {
			uniqueCodeInputFocus();
		}
	};

	const modifyProductCodes = (order, productIdCode) => {
		const params = {
			tid: order.tid,
			oid: order.oid,
			sellerId: order.sellerId,
			platform: order.platform,
			productIdCode,
		};
		UpdateOrderSerialNumberApi(params);
	};
	// 检测订单是否部分发货
	const checkSomeSend = (newKeys = selectedRowKeys) => {
		const unSelectedOrder = [];
		const selectedOrder = [];
		const allOrder = [];
		tradeList.forEach(pack => {
			pack.trades?.forEach(trade => {
				trade?.orders?.forEach(order => {
					allOrder.push(order.oid);
					order.isChecked = newKeys.includes(order.rowKey);
					if (order.isChecked) {
						selectedOrder.push(order);
					} else {
						unSelectedOrder.push(order.oid);
					}
				});
			});
		});
		setTradeList(tradeList);
		return { unSelectedOrder, selectedOrder, allOrder };
	};
	const shippingInitiated = async(newKeys = selectedRowKeys, isForceSend = false) => {
		// !! 如果F1强制打印，商品选择逻辑走取消核验商品逻辑
		if (isForceSend) {
			const keys = calcForceInspectCount(tradeList);
			newKeys = keys;
		}
		const { unSelectedOrder, selectedOrder, allOrder } = checkSomeSend(newKeys);
		const sendList = [];
		const tid = [];
		/** 过滤空值 */
		tradeList.forEach(pack => {
			let isChecked = false;
			pack.trades?.forEach(trade => {
				let flag = false;
				trade?.orders?.forEach(order => {
					if (order.isChecked) {
						isChecked = true;
						flag = true;
					}
				});
				if (flag) {
					tid.push(trade.tid);
				}
			});
			if (isChecked) {
				sendList.push(pack);
			}
		});
		if (allOrder.length == unSelectedOrder.length) {
			message.error("请先选择需要发货的订单");
			return;
		}
		// if (unSelectedOrder.length && !advancedSettingConfig.hasPartSend) {
		if (unSelectedOrder.length) {
			message.error("存在未选择订单");
			setErrorMsg('存在未选择订单');
			textTransformAudioPlay("存在未选择订单");
			return;
		}
		const inspectNum = selectedOrder.reduce((a, b) => Number(b.num) + a, 0);
		const configInfo = await getAdvancedSettingConfig().catch();
		console.log('configInfoconfigInfo', configInfo);
		if (configInfo.isPrintItemTag) {
			PrintTag({
				orderList: sendList,
				templateInfo: {},
				tagTempId: form.getFieldValue('tagTempId'),
				tagPrinter: form.getFieldValue('tagPrinter'),
				senderInfo: {},
			});
		}
		setSendPending(true);
		sendDelivery({
			isSendAbnormal: true,
			type: 'inspectSend',
			scanPrintList: sendList,
			noTemplate: true,
			inspectSendCallback() {
				TradeUpdateTradeScanRecordApi({
					tid: tid.join(','),
					inspectNum,
					inspectProductInfo: getSendContent(contentSetting, selectedOrder),
					exNumber: expressNo,
					productIdCodes: selectedOrder.map(s => s.productIdCode).filter(Boolean).join(','),
					uniqueCode: props.uniqueCode
				});
			}
		});
	};
	// 全部忽略并发货
	const ignoreAllOrder = () => {
		const newIgnoreInspectOrder = { ...ignoreInspectOrder };
		const inspectedProgressTemp = { ...getInspectedProgress() };
		const newKeys = [...selectedRowKeys];
		for (let key in newIgnoreInspectOrder) {
			newIgnoreInspectOrder[key] = true;
			const curRowInspectedProgress = inspectedProgressTemp[key];
			if (curRowInspectedProgress) {
				curRowInspectedProgress.current = curRowInspectedProgress?.total;
			}
			newKeys.push(key);
		}
		setSelectedRowKeys([...newKeys]);
		setInspectedProgress(inspectedProgressTemp);
		setIgnoreInspectOrder(newIgnoreInspectOrder);
		shippingInitiated(newKeys);
		customLogPost('订单存在未绑定货品的商品未验货,批量忽略验货直接发货', { newIgnoreInspectOrder });
	};

	const willSend = (isAutoSend = false, isForceSend = false) => {
		expressInputFocus();
		const tradeInfo = tradeList[0]?.trades?.[0] || {};
		const isDemo = tradeInfo.demo;
		if (sendPending) {
			customLogPost('触发立即发货频率限制', { tradeInfo, isDemo });
			return;
		}
		if (tradeList[0]?.trades.some(trade => trade.needSerialNumber)) {
			const needSerialNumberList = tradeList[0]?.trades.filter(trade => trade.needSerialNumber);
			if (needSerialNumberList.some(trade => trade.orders.some(order => {
				return order.needSerialNumber && (!order.productIdCode || order.productIdCode.split(',').length < order.num);
			}))) {
				message.warning("订单存在缺少商品识别码的商品");
				return;
			}
		}
		// 判断是否存在还未忽略的商品
		if (!isForceSend && !isEmpty(ignoreInspectOrder)) {
			const hasNoIgnore = Object.values(ignoreInspectOrder).some(i => !i);
			if (hasNoIgnore) {
				Modal.confirm({
					title: "订单存在未绑定货品的商品未验货",
					content: (
						<div style={ { color: "#f00" } }>是否批量忽略验货直接发货</div>
					),
					onOk: ignoreAllOrder,
					cancelText: "关闭",
					okText: "忽略验货"
				});
				customLogPost('订单存在未绑定货品的商品未验货', { ignoreInspectOrder });
				return;
			}
		}
		if (isDemo) {
			message.warning("请先扫描快递单后再发货");
			customLogPost('请先扫描快递单后再发货', { tradeInfo, isDemo });
			return;
		}
		if (hasBeenAfterSale && advancedSettingConfig.hasForceSendByItemStatus && advancedSettingConfig.forceSendItemStatusList.includes('REFUND_ING')) {
			setTimeout(() => { playAudio(voice_有退款); }, 1000);
			setErrorMsg('该快递单对应的订单已处理售后状态，请前往订单打印页面发货');
			customLogPost('该快递单对应的订单已处理售后状态，请前往订单打印页面发货', { hasBeenAfterSale });
			return;
		}
		// if (hasNoAwatingSend && !advancedSettingConfig.hasPartSend) {
		if (hasNoAwatingSend) {
			setTimeout(() => { playAudio(voice_已发货); }, 1000);
			setErrorMsg('该快递单号下存在已发货商品，请前往订单打印处理发货');
			customLogPost('该快递单号下存在已发货商品，请前往订单打印处理发货', { hasNoAwatingSend });
			return;
		}
		/** 新增自动发货校验拦截 start*/
		// !! F1强制发货忽略自动发货校验
		if (isAutoSend && !isForceSend) {
			if (advancedSettingConfig.hasNoAutoSendByItemStatus && advancedSettingConfig?.noAutoSendItemStatusList?.includes?.('REFUND_ING') && interceptInfo.refoundList.length) {
				setErrorMsg('对应订单含退款中商品，自动发货失败，请选择手动发货');
				textTransformAudioPlay(`含退款中商品，自动发货失败，请选择手动发货`);
				return;
			}
			console.log('interceptInfo.sendList', interceptInfo.sendList);

			if (advancedSettingConfig.hasNoAutoSendByItemStatus && advancedSettingConfig.noAutoSendItemStatusList?.includes('WAIT_BUYER_CONFIRM_GOODS') && interceptInfo.sendList.length) {
				setErrorMsg('含已发货商品，自动发货失败，请选择手动发货');
				textTransformAudioPlay(`含已发货商品`);
				return;
			}
			if (advancedSettingConfig.hasNoAutoSendByItemStatus && advancedSettingConfig.noAutoSendItemStatusList?.includes('TRADE_FINISHED') && interceptInfo.finishList.length) {
				setErrorMsg('含交易成功商品，自动发货失败，请选择手动发货');
				textTransformAudioPlay(`含交易成功商品`);
				return;
			}
			if (advancedSettingConfig.hasNoAutoSendByItemStatus && advancedSettingConfig.noAutoSendItemStatusList?.includes('TRADE_CLOSED') && interceptInfo.closeList.length) {
				setErrorMsg('含已关闭商品，自动发货失败，请选择手动发货');
				textTransformAudioPlay(`含已关闭商品`);
				return;
			}
			if (advancedSettingConfig.hasNoAutoSendByMemoAndRemark && advancedSettingConfig.memoAndRemarkList.includes('HAS_MEMO') && interceptInfo.buyerMessageList.length) {
				setErrorMsg('有留言，自动发货失败，请选择手动发货');
				textTransformAudioPlay(`有留言`);
				return;
			}

			if (advancedSettingConfig.hasNoAutoSendByMemoAndRemark && advancedSettingConfig.memoAndRemarkList.includes('HAS_REMARK') && interceptInfo.sellerMemoList.length) {
				setErrorMsg('有备注，自动发货失败，请选择手动发货');
				textTransformAudioPlay(`有备注`);
				return;
			}
			if (advancedSettingConfig.hasNoAutoSendBySellerFlag && advancedSettingConfig?.sellerFlagList?.length && interceptInfo.sellerFlagList.length) {
				const limitFlags = advancedSettingConfig?.sellerFlagList || [];
				const hasFlags = interceptInfo?.sellerFlagList || [];
				const flag = flagGroup.filter(s => limitFlags.includes(s.value) && hasFlags.includes(s.value))?.[0];
				console.log('flag', flag, hasFlags, flagGroup);
				if (flag) {
					setErrorMsg(`有${flag.name}旗，自动发货失败，请选择手动发货`);
					textTransformAudioPlay(`有${flag.name}旗`);
					return;
				}
			}
		}

		/** 新增自动发货校验拦截 end*/
		shippingInitiated(undefined, isForceSend);
	};
	const getPlaceholder = () => {
		return `扫描${INSPECT_METHOD_ENUM[advancedSettingConfig.inspectBarCode]}`;
	};
	const getUniqueCodeInputDisabled = () => {
		if (hasBeenAfterSale || hasNoAwatingSend) {
			return true;
		}
		// 只有扫描到了可以验货发货的订单才允许扫描货品信息
		const disabled = tradeList.some(pack => pack?.trades?.some(trade => trade.demo));
		return disabled;
	};
	const handleSendRes = (res) => {
		console.log('finishRes', res);
		/**
		 * 单笔发货的时候触发提示音效
		 * 多笔发货暂时不考虑
		 */
		// pageLoading.destroy();
		setSendPending(false);
		const togetherId2SendRes = {}; // 把togetherId对应的发货结果保存起来
		expressInputFocus();
		if (res?.length > 0) {
			const resultFail = res.some(item => item.result === 103);
			// const sendInfoRes = res[0];
			if (resultFail) {
				playAudio(voiceHandleFail);
				// setTotalNum('sendFailNum', 1);
			} else {
				setHasNoAwatingSend(true);
				playAudio(voiceSendSuccess);
				// setTotalNum('sendSuccessNum', 1);
			}
		}

		res.forEach(item => {
			togetherId2SendRes[item.togetherId] = item;
		});

		if (res?.length > 0) {
			const tradeListTemp = [...tradeList];
			let sendSuccess = true;
			tradeListTemp.forEach(pack => {
				const curTradeSendRes = togetherId2SendRes[pack.togetherId];
				// 100发货成功，101, 部分发货成功，102 拆单发货成功，103发货失败
				if (curTradeSendRes?.result != 103) {
					setHasNoAwatingSend(true);
					pack['sendStatus'] = TRADE_SEND_STATUS["发货成功"];
					curScanGoodInfo.sendStatus = TRADE_SEND_STATUS["发货成功"];
				} else {
					const errorMsg = curTradeSendRes?.subTradeInfos[0]?.message;
					pack["sendFailMsg"] = errorMsg;
					pack['sendStatus'] = TRADE_SEND_STATUS["发货失败"];
					curScanGoodInfo.sendStatus = TRADE_SEND_STATUS["发货失败"];
					sendSuccess = false;
				}
			});
			setTotalNum(sendSuccess ? 'sendSuccessNum' : 'sendFailNum', 1);
			setCurScanGoodInfo(curScanGoodInfo);
			setTradeList(tradeListTemp);
		}
	};
	const selectInspectOnOk = (selectedRows) => {
		if (selectedRows.length) {
			const orderInfo = selectedRows[0];
			startInspect(orderInfo);
			setSelectInspectModalVisible(false);
			uniqueCodeInputFocus();
		} else {
			message.warning("请选择需要验货的货品");
			customLogPost('请选择需要验货的货品');
		}

	};
	const selectInspectOnCancel = () => {
		setSelectInspectModalVisible(false);
	};
	const onKeyDown = async(event) => {
		const { pathname } = history.location;
		const isCurPage = pathname === "/inspectSend";
		if (!sendPending && isCurPage) {
			if (event.keyCode === 112 || event.code === 'F1') {
				setIsForceSend(true);
				willSend(false, true);
				customLogPost('F1键直接发货', { isCurPage });
			}
		}
	};

	useEffect(() => {
		const templateObj = {};
		expressTemplateList.forEach(i => {
			templateObj[i.Mode_ListShowId] = i;
		});
		setTemplateObj(templateObj);
	}, [expressTemplateList]);

	// 主要处理快捷键发货

	useEffect(() => {
		document.addEventListener("keydown", onKeyDown, false);
		return () => {
			document.removeEventListener("keydown", onKeyDown, false);
		};
	}, [sendPending, hasBeenAfterSale, hasNoAwatingSend, tradeList]);

	// 主要处理发货结果

	useEffect(() => {
		event.on(InspectSendEventBus.HANDLE_SEND_RES, handleSendRes);
		return () => {
			event.off(InspectSendEventBus.HANDLE_SEND_RES, handleSendRes);
		};
	}, [tradeList]);

	// 监听是否已经全部验货完毕

	useUpdateEffect(() => {
		// 是否验货完毕
		const inspectFinished = inspectedCount?.total > 0 && inspectedCount?.total == inspectedCount.current;
		const allowAutoSend = getAdvancedSettingConfigTemp()?.hasAutoSend;
		// 是否开启了自动发货
		if (allowAutoSend && !inspectedCount.isForce && inspectFinished && !advancedSettingConfig.hasScanProductIdCode) {
			willSend(true);
			customLogPost('验货完成后自动发货', { allowAutoSend, inspectFinished, inspectedCount });
		}
	}, [inspectedCount]);

	useEffect(() => {
		setAdvancedSettingConfigTemp(advancedSettingConfig);
	}, [advancedSettingConfig]);

	useEffect(() => {
		initStore();
		clearTotalNum();
		getAdvancedSettingConfig();
	}, []);

	const updateConfig = (v) => {
		updateAdvancedSettingConfig({
			...advancedSettingConfig,
			...v
		});
	};
	console.log('goodsUniqueCode_curScanGoodInfo', curScanGoodInfo);

	return (
		<>
			{
				advancedSettingModalVisible && <AdvancedSetting />
			}
			{
				selectInspectModalVisible
				&& (
					<SelectNeedInspectModal
						orders={ needSelectOrders }
						onOk={ selectInspectOnOk }
						onCancel={ selectInspectOnCancel }
					/>
				)
			}
			<Flex.Column justifyContent="space-between" className={ styles.scanContent }>
				<div>
					<div>
						验单选项：
						<span>
							<Radio checked>快递单</Radio>
							<Button type="text" onClick={ () => { history.push("/trade/postPrint"); } }>发货单</Button>
						</span>
					</div>
					<Input
						className={ styles.input }
						ref={ expressInputRef }
						value={ expressNo }
						placeholder="请扫快递单号"
						allowClear
						onChange={ expressNoOnChange }
						onPressEnter={ onSearchTradeByExpressNo }
					/>
					<Flex justifyContent="space-between" alignItems="center">
						<Checkbox
							onChange={ (e) => updateConfig({ hasCheckItem: e.target.checked }) }
							checked={ advancedSettingConfig.hasCheckItem }
						>核验商品
						</Checkbox>
						<Button
							className="r-pr-0"
							type="link"
							onClick={ () => {
								setAdvancedSettingModalVisible(true);
							} }
						>验货设置
						</Button>
					</Flex>
					{
						advancedSettingConfig.hasCheckItem
							?							(
								<Input
									ref={ uniqueCodeInputRef }
									value={ goodsUniqueCode }
									className={ styles.input }
									placeholder={ getPlaceholder() }
									allowClear
									disabled={ getUniqueCodeInputDisabled() }
									onChange={ goodsUniqueCodeOnChange }
									onPressEnter={ goodsUniqueCodeOnSubmit }
								/>
							)
							: null
					}
					{
						advancedSettingConfig?.hasScanProductIdCode
							?	(
								<Input
									ref={ productIdCodeInputRef }
									value={ productIdCode }
									className={ styles.input }
									placeholder="扫描商品识别码"
									allowClear
									disabled={ getUniqueCodeInputDisabled() }
									onChange={ productIdCodeOnChange }
									onPressEnter={ goodsProductIdCodesOnSubmit }
								/>
							) : null
					}

					<Checkbox
						// disabled={ advancedSettingConfig.hasScanProductIdCode }
						checked={ advancedSettingConfig.hasAutoSend }
						onChange={ (e) => updateConfig({ hasAutoSend: e.target.checked }) }
					>扫描后自动发货
					</Checkbox>
					{
						advancedSettingConfig.isPrintItemTag && (
							<div className="r-mt-16">
								<div className={ styles["header-border"] }>设置吊牌模板</div>
								<Form
									form={ form }
									className={ styles.form }
								>
									<Form.Item
										name="tagPrinter"
										rules={ [{
											required: true,
											message: "请选择吊牌打印机"
										}] }
										className="r-mt-12"
									>
										<SelectPrinter />

									</Form.Item>
									<div className="r-flex r-ai-c">
										<Form.Item
											name="tagTempId"
											label="吊牌模板"
											required={ false }
											// validateTrigger={ validateTrigger }
											rules={ [{
												required: true,
												message: "请选择吊牌模板"
											}] }
											style={ { marginBottom: 0, flex: 1, marginRight: 8 } }
										>
											<SelectTagTemplate
												disabled={ printTagSet?.tagTemplateSwitch == "1" }
												onChange={ (v, info) => {
													if (printersList.find(printer => printer == info?.defaultPrinter)) {
														form.setFieldsValue({ tagPrinter: info?.defaultPrinter });
													}
											 } }
											/>
										</Form.Item>
										<SettingOutlined onClick={ () => { history.push('/settings/system?introName=printTagSet'); } } />
									</div>
									<Form.Item
										style={ { marginBottom: 0 } }
										label="打印份数"
									>
										<div style={ { height: 32, lineHeight: '32px' } }>
											{
												printTagSet?.printTagNumSwitch == "1" ? (
													<div>每个商品打印 <span style={ { color: "#FD8204" } }>{printTagSet?.printTagNum}</span> 份</div>
												) : '与商品数量一致'
											}
										</div>
									</Form.Item>
								</Form>
							</div>
						)
					}
					<div className={ styles.errorInfo }>{ errorMsg}</div>
					<InspectedProgress />
				</div>
				<Flex.Column>
					<Button
						className={ styles.sendBtn }
						type="primary"
						onClick={ () => {
							willSend();
							customLogPost('手动点击立即发货', { });
						} }
						loading={ sendPending }
					>立即发货
					</Button>
					<span style={ { marginTop: '8px' } }>
						<BulbOutlined className={ styles["icon-tooltip"] } />
						按F1可强制验货后发货
					</span>
				</Flex.Column>
			</Flex.Column>
		</>
	);

};
export default observer(Header);
