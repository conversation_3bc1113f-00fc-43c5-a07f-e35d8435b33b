/*
 * @Author: try <EMAIL>
 * @Date: 2024-08-06 13:57:14
 * @Description:
 */
import React from "react";
import { observer } from "mobx-react";
import { Popover } from "antd";
import userStore from "@/stores/user";
import { filterPrintContent } from "@/utils/trade/printContent";
import CombineTag from "@/components-biz/Trade/CombineTag";
import ImagePlus from "@/components/Image";
import styles from './index.module.scss';
import { RefundStatus, RefundStatusText, TradeStatus, TradeStatusLabel } from "@/utils/enum/trade";

export const getSendContent = (setting: any, orders: any) => {
	const {
		isShowZeroStockVersion,
		showItemTitle, showOuterId,
		showShortTitle, showSkuAlias,
		showSkuOuterId, showSkuTitle,
		showSysOuterId, showSysOuterSkuId,
	} = setting;
	const inspectProductInfo = [];
	orders.forEach(order => {
		const content = [];
		if (showItemTitle && order.title) {
			content.push(order.title);
		}
		// 货品简称
		if (showShortTitle && order.titleShort && !order?.ignore) {
			content.push(order.titleShort);
		}
		// 商品编码
		if (showOuterId && order.outerId) {
			content.push(order.outerId);
		}
		// 货品编码
		if (!isShowZeroStockVersion && showSysOuterId && order.sysOuterId) {
			content.push(order.sysOuterId);
		}
		// 规格名称
		if (showSkuTitle && order.skuPropertiesName) {
			content.push(order.skuPropertiesName);
		}
		// 规格名称
		if (showSkuTitle && order.skuPropertiesName) {
			content.push(order.skuPropertiesName);
		}
		// 规格别名
		if (showSkuAlias && order.skuAlias) {
			content.push(order.skuAlias);
		}
		// 规格编码
		if (showSkuOuterId && order.outerSkuId) {
			content.push(order.outerSkuId);
		}
		// 货品规格编码
		if (!isShowZeroStockVersion && showSysOuterSkuId && order.sysOuterSkuId) {
			content.push(order.sysOuterSkuId);
		}
		content.push(`【${order.num}】件`);
		inspectProductInfo.push(content.join(','));
	});
	return inspectProductInfo.filter(Boolean).join('\r\n');
};

const IMGS_SIZE = [60, 80, 120];
const ProductOrderFragment = observer(((props: { setting: any; order: any }) => {
	const {
		order,
		setting: {
			showPicSize,
			showPicture,
			showSysPicPath,
			showItemTitle,
			showOuterId,
			showShortTitle,
			showSkuAlias,
			showSkuOuterId,
			showSkuTitle,
			showSysOuterId,
			showSysOuterSkuId,
			customSetJson,
		},
	} = props;

	// 根据 previewPicSize 获取预览图大小
	const getPreviewSize = () => {
		// 从 customSetJson 中获取 previewPicSize
		const previewPicSize = customSetJson?.previewPicSize;

		// 根据 previewPicSize 返回相应的像素值
		switch (Number(previewPicSize)) {
			case 0: return 300;
			case 2: return 800;
			case 1:
			default: return 500;
		}
	};
	const { isShowZeroStockVersion } = userStore;
	let orderStatusTxt = '';
	if ([RefundStatus.退款中].includes(order.refundStatus)) {
		orderStatusTxt = RefundStatusText[order.refundStatus];
	} else if ([TradeStatus.等待买家确认收货, TradeStatus.交易关闭, TradeStatus.交易自动关闭].includes(order.status)) {
		orderStatusTxt = TradeStatusLabel[order.status];
	}
	console.log('orderorder', order);

	// 根据配置获取图片源
	const getImageSrc = () => {
		if (order.isGroup) {
			return order.picUrl;
		}
		
		// 库存版根据配置显示图片
		if (!isShowZeroStockVersion) {
			if (showPicture === 1) {
				// 商品图片：优先取平台规格图片，无规格图片取商品图片
				return order.skuPicUrl || order.picPath;
			} else if (showSysPicPath === 1) {
				// 货品图片：取货品规格图片
				return order.sysPicPath;
			}
		}
		
		// 零库存版或其他情况的默认逻辑
		return showPicture ? order.picPath : order.sysPicPath;
	};

	return (
		<>
			<label className={ `r-flex product-order-item ` } key={ order.oid } style={ { paddingLeft: order.isGroup ? `${IMGS_SIZE[showPicSize]}px` : 0 } }>
				{(showPicture || showSysPicPath) ? (
					<div className="r-relative" >
						{orderStatusTxt ? <div className={ styles["item-send-status"] }>{orderStatusTxt}</div> : null}
						<Popover
							placement="right"
							content={ <ImagePlus src={ getImageSrc() } height={ getPreviewSize() } width={ getPreviewSize() } /> }
						>
							<ImagePlus src={ getImageSrc() } height={ IMGS_SIZE[showPicSize] } width={ IMGS_SIZE[showPicSize] } />
						</Popover>
					</div>
				) : ''}
				<div id={ `productItem-${order.oid}` } className="r-ml-8 r-flex r-fd-c">
					<div className="r-flex r-fw-w">
						{/* 商品标题 */}
						<span className="r-mr-5">
							<CombineTag visible={ order?.isCombination === 1 } />
							{showItemTitle && order.title ? order.title : null}
						</span>
						{/* 货品简称 */}
						{showShortTitle && order.titleShort && !order?.ignore
							? (
								<span className="r-mr-5">
									{order.titleShort}
								</span>
							) : ''}
						{/* 商品编码 */}
						{showOuterId && (order.outerId || order.numIid) ? <span className="r-mr-5">{(order.outerId && +order.outerId !== -1) ? order.outerId : order.numIid}</span> : ''}
						{/* 货品编码 */}
						{!isShowZeroStockVersion && showSysOuterId && order.sysOuterId ? <span className="r-mr-5">{order.sysOuterId} </span> : ''}
					</div>
					<div className="r-flex r-fw-w">
						{/* 规格名称 */}
						{showSkuTitle && order.skuPropertiesName
							? <span className="r-mr-5">{filterPrintContent(order.skuPropertiesName)} </span> : ''}
						{/* 规格别名 */}
						{showSkuAlias && order.skuAlias ? <span className="r-mr-5">{order.skuAlias} </span> : ''}
						{/* 规格编码 */}
						{showSkuOuterId && order.outerSkuId ? <span className="r-mr-5">{order.outerSkuId} </span> : ''}
						{/* 货品规格编码 */}
						{!isShowZeroStockVersion && showSysOuterSkuId && order.sysOuterSkuId ? <span className="r-mr-5">{order.sysOuterSkuId} </span> : ''}
					</div>
				</div>
			</label>
		</>
	);
}
));

export default ProductOrderFragment;
