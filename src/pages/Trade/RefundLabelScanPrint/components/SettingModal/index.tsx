import React, { useEffect, useMemo, useState } from "react";
import { Modal, Radio, Select, Space, Tooltip, message, Form, Checkbox } from "antd";
import { observer } from "mobx-react";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { ItemMatchType, MatchDimensions, MatchModel, MatchType, SysItemMatchType } from "../../constants";
import Icon from "@/components/Icon";
import userStore from "@/stores/user";
import { tradeStore } from "@/stores";
import { ItemTakeGoodsLabelConfigEditApi } from "@/apis/trade/takeGoodsLabel";
import InputSelect from "@/components/Input/InputSelect";
import {
	TRADE_LABEL_OPTIONS,
	TRADE_EXCEPTION_OPTIONS
} from "@/pages/Trade/components/SearchContainer/constants";
import styles from "./index.module.scss";

interface Props {
	onOk:()=>void,
	onCancel:()=>void,
}

const PrintItemName = {
	printStatus: "快递单",
	fhdPrintStatus: "发货单",
	labelPrintStatus: "商品标签"
};

const SettingModal = (props:Props) => {
	const [form] = Form.useForm();
	const { onOk, onCancel } = props;
	const [loading, setLoading] = useState(false);
	const [matchModel, setMatchModel] = useState<MatchModel | "">(MatchModel.所有订单);
	const [dimension, setDimension] = useState<MatchDimensions | "">(MatchDimensions.商品);
	const [matchType, setMatchType] = useState<ItemMatchType | SysItemMatchType | "">("");
	const [printStatusObj, setPrintStatusObj] = useState({});
	const [sortType, setSortType] = useState<number>(1);
	const [kddPrintedNotAutoPrint, setKddPrintedNotAutoPrint] = useState(false);
	const [sellerCommentNotAutoPrint, setSellerCommentNotAutoPrint] = useState(false);
	const [buyerMessageNotAutoPrint, setBuyerMessageNotAutoPrint] = useState(false);
	const { refundLabelScanPrintStore: { settingConfig, getSettingConfig, updateSettingConfig } } = tradeStore;
	const { isShowZeroStockVersion } = userStore;

	const initData = () => {
		handleSettingConfig();
	};

	const handleSettingConfig = async() => {
		const settingConfig:{[k:string]:any} = await getSettingConfig();
		const { refundLabelMatch: { matchBy, matchWay, matchModel, printStatus, labelPrintStatus, fhdPrintStatus, excludeServiceTagList, excludeTradeExceptionList, sortType } } = settingConfig;
		form.setFieldsValue({
			excludeServiceTagList,
			excludeTradeExceptionList
		});
		setPrintStatusObj({
			printStatus,
			fhdPrintStatus,
			labelPrintStatus
		});
		setKddPrintedNotAutoPrint(settingConfig?.refundLabelMatch?.kddPrintedNotAutoPrint ?? false);
		setBuyerMessageNotAutoPrint(settingConfig?.refundLabelMatch?.buyerMessageNotAutoPrint ?? false);
		setSellerCommentNotAutoPrint(settingConfig?.refundLabelMatch?.sellerCommentNotAutoPrint ?? false);
		setSortType(sortType);
		setDimension(matchWay);
		setMatchType(matchBy);
		setMatchModel(matchModel);
	};

	const onMatchModelChange = (e) => {
		const value = e.target.value;
		setMatchModel(value);
	};

	const onDimensionsChange = (e) => {
		const value = e.target.value;
		setDimension(value);
		if (value == MatchDimensions.商品) {
			setMatchType(ItemMatchType["商品ID+规格ID"]);
		} else if (value == MatchDimensions.货品) {
			setMatchType(SysItemMatchType["货品ID+货品规格ID"]);
		}
	};

	const onMatchTypeChange = (v) => {
		setMatchType(v);
	};

	const onCheckChange = (e, key) => {
		if (e?.length == 0) {
			message.warning(`${PrintItemName[key]}已打印、未打印不可同时取消勾选`);
			return;
		}
		setPrintStatusObj(prev => {
			return {
				...prev,
				[key]: e
			};
		});
	};

	const _onOk = async() => {
		const formData = await form.validateFields();
		const refundLabelMatch = {
			matchBy: matchType,
			matchWay: dimension,
			matchModel,
			excludeServiceTagList: formData.excludeServiceTagList,
			excludeTradeExceptionList: formData.excludeTradeExceptionList,
			sortType,
			kddPrintedNotAutoPrint,
			buyerMessageNotAutoPrint,
			sellerCommentNotAutoPrint,
			...printStatusObj
		};
		setLoading(true);
		const params = {
			...settingConfig,
			refundLabelMatch
		};
		ItemTakeGoodsLabelConfigEditApi({
			scanSet: params
		}).then(() => {
			message.success("设置已保存");
			onOk && onOk();
			updateSettingConfig(params);
		}).finally(() => setLoading(false));
		
	};

	const options = useMemo(() => {
		if (!isShowZeroStockVersion && dimension === MatchDimensions.商品) {
			const filterOptions = [ItemMatchType["简称+规格别名"], ItemMatchType["简称+规格名称"], ItemMatchType["简称"], ItemMatchType["规格别名"]];
			const validOptions = MatchType[dimension].filter(i => !filterOptions.includes(i.value));
			return validOptions;
		}
		return MatchType[dimension];

	}, [dimension, isShowZeroStockVersion]);

	const _onCancel = () => {
		if (loading) {
			return;
		}
		onCancel && onCancel();
	};

	useEffect(() => {
		initData();
	}, []);	
	const TRADE_EXCEPTION_OPTIONS_1 = (useMemo(() => {
		const hasWaveManagePermission = userStore.hasWaveManagePermission;
		if (!hasWaveManagePermission) {
			TRADE_EXCEPTION_OPTIONS.forEach(op => {
				op.options.forEach(item => {
					item.arr = item.arr.filter(i => i.value !== '波次拣货异常');
				});
			});
		}
		return [...TRADE_EXCEPTION_OPTIONS];
	}, [userStore.hasWaveManagePermission]));
	return (
		<Modal
			visible
			title="扫描设置"
			bodyStyle={ { minHeight: 200 } }
			okText="保存"
			okButtonProps={ { loading } }
			onOk={ _onOk }
			onCancel={ _onCancel }
		>
			<div className={ styles["form-item"] }>
				<div className={ styles["form-item-lable"] }>
					匹配方式：
				</div>
				<div className={ styles["form-item-wrap"] }>
					<div hidden={ isShowZeroStockVersion }>
						<Radio.Group onChange={ onDimensionsChange } value={ dimension }>
							<Radio value={ MatchDimensions.货品 }>按本地货品</Radio>
							<Radio value={ MatchDimensions.商品 }>按平台商品</Radio>
						</Radio.Group>
					</div>
					<div className="r-flex r-mt-12">
						<div>匹配</div>
						<div className="r-m-lr-8">
							<Select style={ { width: 200 } } getPopupContainer={ (e) => e.parentElement } size="small" options={ options } value={ matchType } onChange={ onMatchTypeChange } />
						</div>
						<div>一致的订单</div>
					</div>
				</div>
			</div>

			<div className={ `${styles["form-item"]} r-mt-16` }>
				<div className={ styles["form-item-lable"] }>
					匹配订单
					<Tooltip title={ (
						<>
							<div>系统默认的匹配订单规则为：</div>
							<div>· 付款时间为近30天（包含当日）</div>
							<div>· 包含待发货+先发货订单</div>
							<div>· 无退款订单</div>
							<div>· 单商品单规格单件订单</div>
						</>
					) }
					>
						<QuestionCircleOutlined className="r-c-999 r-ml-5 r-mr-5" />
					</Tooltip>：
				</div>
				<div className={ styles["form-item-wrap"] }>
					<div>
						<Form form={ form } size="small">
							<Form.Item
								label="订单标签"
								name="excludeServiceTagList"
								style={ { width: "360px" } }
							>
								<InputSelect
									bgHighLight
									optionsList={
										[{ ...TRADE_LABEL_OPTIONS[1], label: '不包含' }]
									}
									size="small"
								/>
							</Form.Item>
							<Form.Item
								label="订单异常"
								name="excludeTradeExceptionList"
								style={ { width: "360px" } }
							>
								<InputSelect
									bgHighLight
									optionsList={
										[{ ...TRADE_EXCEPTION_OPTIONS_1[1], label: '不包含' }]
									}
									size="small"
								/>
							</Form.Item>
							<Form.Item
								label="打印状态"
								className="r-mb-0"
							>
								<Form.Item
									label="快递单"
									className="r-mb-8"
									labelCol={ { span: 7 } }
									labelAlign="left"
								>
									<Checkbox.Group value={ printStatusObj.printStatus } onChange={ e => onCheckChange(e, "printStatus") }>
										<Checkbox value={ 1 }>已打印</Checkbox>
										<Checkbox value={ 2 }>未打印</Checkbox>
									</Checkbox.Group>
								</Form.Item>
								<Form.Item
									label="发货单"
									className="r-mb-8"
									labelCol={ { span: 7 } }
									labelAlign="left"
								>
									<Checkbox.Group value={ printStatusObj.fhdPrintStatus } onChange={ e => onCheckChange(e, "fhdPrintStatus") }>
										<Checkbox value={ 1 }>已打印</Checkbox>
										<Checkbox value={ 2 }>未打印</Checkbox>
									</Checkbox.Group>
								</Form.Item>
								<Form.Item
									label="商品标签"
									className="r-mb-0"
									labelCol={ { span: 7 } }
									labelAlign="left"
								>
									<Checkbox.Group value={ printStatusObj.labelPrintStatus } onChange={ e => onCheckChange(e, "labelPrintStatus") }>
										<Checkbox value={ 1 }>已打印</Checkbox>
										<Checkbox value={ 2 }>未打印</Checkbox>
									</Checkbox.Group>
								</Form.Item>
							</Form.Item>
						</Form>
					</div>
				</div>
			</div>

			<div className={ `${styles["form-item"]} r-mt-16` }>
				<div className={ styles["form-item-lable"] }>
					匹配模式：
				</div>
				<div className={ styles["form-item-wrap"] }>
					<Radio.Group onChange={ onMatchModelChange } value={ matchModel }>
						<Space direction="vertical">
							<Radio value={ MatchModel.所有订单 }>匹配成功时返回所有订单</Radio>
							<Radio value={ MatchModel.一笔订单 }>匹配成功时仅返回一笔订单，并且自动打印</Radio>
						</Space>
					</Radio.Group>
					{
						matchModel === MatchModel.一笔订单 && (
							<div className="r-mt-8 r-ml-24">
								<Space direction="vertical">
									<Checkbox checked={ kddPrintedNotAutoPrint } onChange={ e => setKddPrintedNotAutoPrint(e.target.checked) }>已打印快递单的不自动打印</Checkbox>
									<Checkbox checked={ buyerMessageNotAutoPrint } onChange={ e => setBuyerMessageNotAutoPrint(e.target.checked) }>有买家留言不自动打印</Checkbox>
									<Checkbox checked={ sellerCommentNotAutoPrint } onChange={ e => setSellerCommentNotAutoPrint(e.target.checked) }>有卖家备注不自动打印</Checkbox>
								</Space>
							</div>
						)
					}
				</div>
			</div>

			<div className={ `${styles["form-item"]} r-mt-16` }>
				<div className={ styles["form-item-lable"] }>
					订单排序
					<Tooltip title="如匹配模式为返回所有订单，影响列表数据展示顺序；如匹配模式为仅返回一笔订单，则按照对应排序规则取第一笔订单">
						<span style={ { color: "#666" } } className="r-ml-4 r-mr-5">
							<Icon type="wenhao-xian" size={ 15 } />
						</span>
					</Tooltip>：
				</div>
				<div className={ styles["form-item-wrap"] }>
					<Select
						value={ sortType }
						options={ [
							{
								label: "按付款时间排序",
								value: 1
							},
							// {
							// 	label: "按付款时间降序",
							// 	value: 2
							// },
							{
								label: "按剩余发货时间排序",
								value: 3
							}
							// {
							// 	label: "按剩余发货时间降序",
							// 	value: 4
							// }
						] }
						style={ { width: 200 } }
						onChange={ e => setSortType(e) }
					/>
				</div>
			</div>
			<div />
		</Modal>
	);
};

export default observer(SettingModal);