import React, { useEffect, useState, useRef, FC, useCallback } from "react";
import { Input, Form, Select, Button, Checkbox } from "antd";
import { observer } from "mobx-react";
import { debounce, isArray, DebouncedFunc } from "lodash";
import message from '@/components/message';
import PrintCenter from '@/print/index';
import SelectSender from "./components/SelectSender";
import SelectTemplate from "./components/SelectTemplate";
import { tradeStore } from "@/stores";
import scanPrintStore from "@/stores/trade/scanPrint";
import styles from "./index.module.scss";
import { ItemTakeGoodsLabelCancelRefundAndTradeLabelApi } from "@/apis/trade/refundLabelScanPrint";
import { TradeTradeDetailGetApi, getBatchSids } from "@/apis/trade";
import { TakeGoodsLabelGenerateApi } from "@/apis/trade/takeGoodsLabel";
import { PrintRefundLabelExpressBill } from "../../utils";
import { PRINT_MAP, PLAT_DW } from "@/constants";
import { genePrintContent } from "@/utils/trade/printContent";
import { useStores } from "@/stores/tool";
import { PrintLabel } from "@/print/refundLabelScanPrint";
import PrintResult from "./components/PrintResult";
import { local } from "@/libs/db";
import { CLEAR_PAGE_DATA, PRINT_SETTING } from "../../constants";
import userStore from "@/stores/user";
import SelectPrinter from "./components/SelectPrinter";
import tradeSetStore from "@/stores/trade/tradeSet";
import { isOrderMatchingTemplate } from "@/pages/Trade/components/BottomCom/checkAbnormalUtil";
import event from "@/libs/event";
import { pageLoading } from "@/components/PageLoading";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import SettingModal from "../SettingModal";
import { playAudio, textTransformAudioPlay } from "@/pages/Trade/ScanPrint/utils";
import voice打印失败 from '@/assets/mp3/打印失败.mp3';
import { TradeDictInsertDictApi, TradeDictQueryDictApi, TradePrintCheckApi } from "@/apis/trade/search";
import { AbnormalType } from '@/pages/Trade/components/BottomCom/constants';
import { preCheckPrintLimit } from '@/utils/print/shopTemp';

interface ISearchProps{
	tiggerFunction:()=>void
}
	
// 在组件顶部定义 debounced 函数
const debouncedSaveAutoSend = debounce(async(isChecked: boolean, setAutoSend: (checked: boolean) => void) => {
	try {
		await TradeDictInsertDictApi({
			userDictEnum: "REFUND_LABEL_MATCH_SET_FOR_MAIN",
			value: JSON.stringify({ isAutoSend: isChecked ? 1 : 0 })
		});
	} catch (error) {
		console.log('保存自动发货设置失败:', error);
		message.error('保存设置失败');
		// 如果接口调用失败，恢复之前的状态
		setAutoSend(!isChecked);
	}
}, 200);

const Search: FC<ISearchProps> = ({ tiggerFunction }) => {
	const [form] = Form.useForm();
	const [settingModalVisible, setSettingModalVisible] = useState(false);
	const {
		isMergePrint,
		kddTempList,
		bhdXbqTempList,
		refundLabelScanPrintStore: {
			setPrintRes,
			getUserTakeLabelTemplateConfig,
			autoSend,
			getTakeLabelMathTradeList,
			takeLabelPrinter,
			takeLabelTemplate,
			selectedRows,
			setAutoSend,
			setLoadingTrue,
			setLoadingFalse,
			isMatching,
			settingConfig,
			isAutoPrintEnabled, setIsAutoPrintEnabled,
			setTakeLabelTemplateModalVisible
		}
	} = tradeStore;
	const { printersList } = scanPrintStore;
	const searchInput = useRef(null);
	const debouncedPrintRef = useRef<DebouncedFunc<() => void> | null>(null);

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `退货标签重匹配: 【 ${dataType} 】`,
			data: {
				autoSend, // 打印后自动发货
				isAutoPrintEnabled, // 是否可以自动打印
				settingConfig,
				selectedRows,
				isMergePrint,
				...data
			}
		});
	};

	// 通过模板id获取模板信息
	const getTempInfoByTempId = (tempId) => {
		let templateInfo;
		if (isMergePrint) {
			templateInfo = kddTempList.find((i) => i.id == tempId);
		} else {
			templateInfo = kddTempList.find((i) => i.Mode_ListShowId == tempId);
		}
		return templateInfo;
	};
	/** --------------- 回显用户上次选择的打印选项 ------------------ */

	// 回显是否自动发货

	const echoAutoSend = async() => {
		try {
			const res = await TradeDictQueryDictApi({ 
				userDictEnum: 'REFUND_LABEL_MATCH_SET_FOR_MAIN' 
			});
			const printSetting = local.get(PRINT_SETTING) || {};
			let autoSend;
			
			if (res?.value) {
				const config = JSON.parse(res.value);
				autoSend = config.isAutoSend === 1;
			} else {
				await TradeDictInsertDictApi({
					userDictEnum: "REFUND_LABEL_MATCH_SET_FOR_MAIN",
					value: JSON.stringify({ isAutoSend: printSetting.autoSend ? 1 : 0 })
				});
				autoSend = printSetting.autoSend;
			}
			setAutoSend(autoSend);
		} catch (error) {
			console.log('获取自动发货设置失败:', error);
		}
	};

	// 回显用户上次选择的模板

	const echoLastTemplate = () => {
		const curTemplate = form.getFieldValue("template");
		/**
		 * 如果表单已经有值，就不做回显，因为echoLastTemplate会基于store中的kddTempList执行（因为
		 * 模板列表是打印中心给的，我们不会调用接口来获取）
		 * 用户可能手动选择过模板，所以不做回显
		 */
		if (!curTemplate) {
			const printSetting = local.get(PRINT_SETTING) || {};
			const lastSelectedTemplate = printSetting.template;
			form.setFieldsValue({ template: lastSelectedTemplate });
		}
	};

	// 回显用户上次选择的发件人

	const echoSender = async() => {
		const curSender = form.getFieldValue("sender");
		if (!curSender) {
			const printSetting = local.get(PRINT_SETTING) || {};
			const lastSelectedSender = printSetting.sender;
			form.setFieldsValue({ sender: lastSelectedSender });
		}
	};

	// 回显用户上次选择的发件人

	const echoPrinter = async() => {
		const curPrinter = form.getFieldValue("printer");
		if (!curPrinter) {
			const printSetting = local.get(PRINT_SETTING) || {};
			const lastSelectedPrinter = printSetting.printer;
			form.setFieldsValue({ printer: lastSelectedPrinter });
		}
	};


	/** ---------------------------------------------------------- */


	const onTemplateChange = (value, info) => {
		form.setFieldsValue({ 'template': value });
		// 模板与发件人联动
		if (info.bindFjrId && info.bindFjrId != -1) {
			form.setFieldsValue({ 'sender': info.bindFjrId });
		}
		// 把用户选择的模板保存到local中
		const printSetting = local.get(PRINT_SETTING) || {};
		local.set(PRINT_SETTING, {
			...printSetting,
			template: value
		});
	};

	const onSenderChange = (value) => {
		// 把用户选择的发件人保存到local中
		const printSetting = local.get(PRINT_SETTING) || {};
		local.set(PRINT_SETTING, {
			...printSetting,
			sender: value
		});
	};

	const onPrinterChange = (value) => {
		// 把用户选择的打印机保存到local中
		const printSetting = local.get(PRINT_SETTING) || {};
		local.set(PRINT_SETTING, {
			...printSetting,
			printer: value
		});
	};

	/** --------------- 标签重打相关逻辑 ------------------ */

	// 标签重打前置校验
	const reprintLabelPreCheck = async() => {
		if (selectedRows.length < 1) {
			message.error("请先选择需要重打的订单");
			return;
		}
		if (!takeLabelTemplate) {
			message.error("请先设置拿货标签模板再重新打印");
			return;
		}
		if (!takeLabelPrinter) {
			message.error("请先设置拿货标签打印机再重新打印");
			return;
		}
		if (!takeLabelPrinter && !takeLabelTemplate) {
			message.error("请先设置拿货标签模板和打印机再重新打印");
			return;
		}
		return true;
	};

	// 标签重打前置接口（后端需要处理作废等逻辑）
	const voidLabel = async() => {
		const { currentScanLabelId, tid, labelIdList, labelId } = selectedRows[0] || {};
		const params = {
			currentScanLabelId,
			tid,
			labelId,
			labelIdList,
		};
		const voidRes = await ItemTakeGoodsLabelCancelRefundAndTradeLabelApi(params).then(() => true, () => false).catch(() => false);
		return voidRes;
	};

	// 获取订单详情
	const getTradeDetail = async() => {
		const { tid, sellerId, platform } = selectedRows[0] || {};
		const tradeInfos = [];
		let tradeList = [];
		tradeInfos.push({
			tids: [tid],
			sellerId,
			platform,
		});
		await TradeTradeDetailGetApi({ tradeInfos }).then(res => {
			if (res?.data?.list?.length > 0) {
				tradeList = res?.data?.list;
			}
		}).catch(console.log);
		return tradeList;
	};

	// 重新生成拿货小标签
	const regeneratorTag = async(tradeList) => {
		const getGeneratorTagParams = (list) => {
			let goodsLabelTradeInfoList = [];
			let tradeInfos = { tid: "", orderInfos: [], split: false };
			const tradeInfoList = [];
			for (let tradeListItem of list) {
				for (let trade of tradeListItem.trades) {
					tradeInfos.tid = trade.tid;
					for (let order of trade.orders) {
						if (!order.isGift) {
							tradeInfos.orderInfos.push({
								oid: order.oid,
								num: order.num,
								sysSkuId: order.systemSkuId,
								ignoreExcFlag: order.ignoreExcFlag ? "IGNORE_EXC" : "NOT_IGNORE_EXCE",
								alreadyAllotStockNum: order.alreadyAllotStockNum,
								combinationAllotStockNum: order.combinationAllotStockNum,
							});
						}
					}
					tradeInfoList.push(tradeInfos);
				}
				goodsLabelTradeInfoList.push({ tradeInfos: tradeInfoList });
			}
			return ({
				goodsLabelTradeInfoList,
				generateMarketLabelWhenUnderstock: true,
				skipAlreadyGenerateLabelTrade: true,
			});
		};
		const params = getGeneratorTagParams(tradeList);
		let takeGoodsLabels = [];
		await TakeGoodsLabelGenerateApi(params).then(res => {
			if (res.takeGoodsLabels.length > 0) {
				takeGoodsLabels = res.takeGoodsLabels;
			}
		}).catch(console.log);
		return takeGoodsLabels;
	};

	// 开始打印标签
	const printTakeLabels = (orderList) => {
		PrintLabel({
			printer: takeLabelPrinter,
			templateId: takeLabelTemplate,
			orderList,
		});
	};

	// 点击重打标签
	const onReprintLabel = async() => {
		sendPoint(Pointer.退款标签重匹配_重打标签);
		setPrintRes({ showRes: false });
		// 1.先做前置校验
		const isPreCheckSuccess = await reprintLabelPreCheck();
		if (isPreCheckSuccess) {
			setLoadingTrue("正在重新打印标签...");
			// 2.作废原有标签
			const isVoidLabelSuccess = await voidLabel();
			if (isVoidLabelSuccess) {
				// 3.作废成功以后，查询订单详情
				const tradeDetailList = await getTradeDetail();
				// 4.拿到订单详情以后，组装生成小标签的参数
				if (tradeDetailList.length > 0) {
					const newTakeGoodsLabels = await regeneratorTag(tradeDetailList);
					if (newTakeGoodsLabels.length > 0) {
						// 5.组装传递给打印中心的数据，并且触发标签重打
						const orderList = [];
						newTakeGoodsLabels.forEach(i => orderList.push({
							...i,
							id: i.labelId, // 重新映射一个id字段，打印那边获取商品标签唯一码用的是id
							sortCode: `${i.sortNumCode || ""}${i.sortTradeSn || ""}-${i.sortOrderSn || ""}`,
							isChecked: true, // 需要设置isChecked为true，不然公共方法handlePrintAct中会过滤掉
						}));
						printTakeLabels(orderList);
					} else {
						setLoadingFalse();
					}
				} else {
					setLoadingFalse();
				}
			} else {
				setLoadingFalse();
			}
		} else {
			playAudio(voice打印失败);
		}
	};
		/** ------------------------------------------------ */

	/** ---------------------- 打印快递单相关逻辑 -------------------------- */
	// 组装查询历史单号的参数
	const getHistorySidsParams = (list) => {
		try {
			const templateInfo = getTempInfoByTempId(form.getFieldValue("template"));
			let params = {};
			const curTradeInfo = list[0];
			if (isMergePrint) {
				const curExpressType = PRINT_MAP[curTradeInfo.platform];
				const curTemplate = templateInfo?.userTemplateList?.find(template => template.expressType == curExpressType);
				params = {
					kdCode: curTemplate.exCode,
					kdType: curTemplate.expressType,
					exid: curTemplate.exId,
					userTemplateId: curTemplate.userTemplateId,
					trades: JSON.stringify([{
						tids: curTradeInfo.tids[0],
						togetherId: curTradeInfo.togetherId,
						sellerId: curTradeInfo.sellerId,
						platform: curTradeInfo.platform
					}])
				};
			} else {
				params = {
					kdCode: templateInfo?.ExCode,
					kdType: templateInfo?.KddType,
					exid: templateInfo?.Exid,
					userTemplateId: templateInfo?.Mode_ListShowId,
					trades: JSON.stringify([{
						tids: curTradeInfo.tids[0],
						togetherId: curTradeInfo.togetherId,
						sellerId: curTradeInfo.sellerId,
						platform: curTradeInfo.platform
					}])
				};
			}
			return params;
		} catch (e) {
			console.log(e);
		}
	};
		// 判断是否已经申请过单号
	const isAlreadyApplySid = async(list) => {
		let alreadyApplySids = [];
		const params = getHistorySidsParams(list);
		await getBatchSids(params).then(res => {
			if (res.data?.sids) {
				res.data.sids.forEach(item => {
					item.sids.forEach(sidItem => alreadyApplySids.push(sidItem.sid));
				});
			}
		}).catch(error => {
			customLogPost('isAlreadyApplySid: 判断是否已经申请过单号失败', { error });
		});
		return alreadyApplySids;

	};

	// 规则校验，防止异常订单打印
	const checkPrintSettingRule = (tradeDetailList) => {
		const errorInfo = [
			{
				title: 'BIC质检订单不允许操作',
				rule: pack => pack?.serviceTagList?.includes('bic_order'),
				play: () => textTransformAudioPlay('B I C 质检订单不允许操作'),
			},
			{
				title: '系统赠品异常，不允许操作',
				rule: pack => pack?.serviceTagList?.includes('sysGiftAbnormal'),
				play: () => textTransformAudioPlay('系统赠品异常'),
			}
		];
		let errArr = [];
		tradeDetailList.forEach(pack => {
			errorInfo.forEach((errorItem:any) => {
				if (errorItem.rule(pack)) {
					errArr.push({
						text: errorItem?.errorTextFn ? errorItem.errorTextFn(pack) : errorItem.title,
						play: () => errorItem?.play?.(pack)
					});
				}
			});
		});
		return errArr;
	};

	const onPrintExpressBill = useCallback(async() => {
		sendPoint(Pointer.退款标签重匹配_打印快递单);
		setPrintRes({ showRes: false });
		await preCheckPrintLimit();
		form.validateFields(["printer", "template", "sender"]).then(async values => {
			const templateInfo:{[k:string]:any} = getTempInfoByTempId(values.template);
			if (selectedRows.length < 1) {
				// playAudio(voice打印失败);
				message.error("请先选择需要打印的订单");
				return;
			}
			const printContentSet = await tradeSetStore.getPrintContentSet();
			const filterWord = await tradeSetStore.getFilterWord();
			// 1.获取订单详情
			const tradeDetailList = await getTradeDetail();

			// 拦截异常
			const errList = checkPrintSettingRule(tradeDetailList);
			if (errList?.length) {
				let errItem = errList?.[0];
				errItem?.play();
				message.error(errItem?.text);
				customLogPost(`打印快递单: 异常校验拦截`, { errList });
				return;
			}

			const trades = tradeDetailList?.flatMap(pack => pack.trades);
			const checkRes = await TradePrintCheckApi({
				tidList: trades?.map(trade => trade.tid) || [],
				tradeCheckInfos: trades?.map(trade => ({
					sellerId: trade.sellerId,
					platform: trade.platform,
					source: trade.source,
					distributorUserId: trade.distributorUserId,
					tid: trade.tid,
					storageTime: trade.storageTime,
					ptTid: trade.ptTid,
					modified: trade.modified,
					oidList: trade?.orders?.map(o => o.oid) || [], // 系统赠品判断
				})),
			});

			// 接口异常拦截
			let hasAbnormal_14 = false;
			let hasAbnormal_15 = false;
			checkRes?.data?.abnormalTradeInfoList?.forEach((item: any) => {
				if (item.type == AbnormalType.订单正在执行自动策略) {
					hasAbnormal_14 = true;
				} else if (item.type == AbnormalType.列表加载商品数据与数据库商品数据不一致) {
					hasAbnormal_15 = true;
				}
			});
			if (hasAbnormal_14) {
				message.error('订单正在执行自动策略');
				playAudio(voice打印失败);
				customLogPost('打印快递单: 订单正在执行自动策略', { tradePrintCheck: checkRes });
				return;
			}
			if (hasAbnormal_15) {
				message.error('列表加载商品数据与数据库商品数据不一致');
				playAudio(voice打印失败);
				customLogPost('打印快递单: 列表加载商品数据与数据库商品数据不一致', { tradePrintCheck: checkRes });
				return;
			}

			const name = isMergePrint ? templateInfo?.exCode : templateInfo.ExCode;
			// 得物品牌直发—普通履约、多仓发货标签的订单需匹配得物模版
			const notSupportedDeWu = tradeDetailList.filter(order => {
				return order.platform == PLAT_DW && (order?.serviceTagList?.includes('ordinaryOrder') || order?.serviceTagList?.includes('warehouseOrder'));
			});
			
			if (notSupportedDeWu?.length && !["DWTYMB"].includes(name)) {
				playAudio(voice打印失败);
				message.error("不支持得物品牌直发订单处理，请选择【得物通用模板】使用官方电子面单打印");
				customLogPost('打印快递单: 不支持得物品牌直发订单处理，请选择【得物通用模板】使用官方电子面单打印', { notSupportedDeWu: notSupportedDeWu?.map(d => d.togetherId) });
				return;
			}

			// 校验订单和平台是否匹配
			const notMatchingTemplateOrderList = isOrderMatchingTemplate(tradeDetailList, templateInfo);
			if (notMatchingTemplateOrderList.length) {
				playAudio(voice打印失败);
				message.error("当前模板和所选订单所属平台不一致，请重新选择模板");
				customLogPost('打印快递单: 当前模板和所选订单所属平台不一致，请重新选择模板', { notMatchingTemplateOrderList: notMatchingTemplateOrderList?.map(d => d.togetherId) });
				return;
			}
			pageLoading.loading(true);
			if (tradeDetailList.length > 0) {
				// 2.组装打印数据
				 // 2.1 判断是否申请过历史单号
				const historySids = await isAlreadyApplySid(tradeDetailList);
				// theTradeDetail.isChecked = true; // 发货时需要
				// 3. 组装打印内容
				const keyWords = filterWord;
				let expressTemplate = {};
				if (isMergePrint) {
					expressTemplate = {
						exId: templateInfo.id,
						exCode: templateInfo.exCode,
						exName: templateInfo.groupName,
						kddType: templateInfo.expressType
					};
				} else {
					expressTemplate = {
						exId: templateInfo.Exid,
						exCode: templateInfo.ExCode,
						exName: templateInfo.ExcodeName,
						kddType: templateInfo.KddType
					};
				}
				tradeDetailList.forEach(pack => {
					pack.isChecked = true; // 发货时需要
					pack.expressTemplate = expressTemplate; // 加上expressTemplate字段，因为发货的时候需要用到
					pack.sids = historySids;
					pack.trades.forEach(trade => {
						// trade.sids = alreadyApplySids;
						trade.orders.forEach(order => order.isChecked = true); // 获取打印内容需要isChecked
					});
					const printContent = genePrintContent(pack, printContentSet, keyWords);
					pack.printContent = printContent;
				});
				// 作废小标签
				voidLabel();
				PrintRefundLabelExpressBill({
					isMergePrint,
					packList: tradeDetailList,
					senderId: values.sender,
					printer: values.printer,
					templateInfo: getTempInfoByTempId(values.template),
				});
			} else {
				customLogPost('打印快递单: 获取订单详情为空', { tradeDetailList });
			}
		}).catch((error) => {
			customLogPost('onPrintExpressBill: error', { error });
		});

	}, [selectedRows, isMergePrint, kddTempList]);

	useEffect(() => {
		// 创建新的 debounce 包装函数
		debouncedPrintRef.current = debounce(() => {
			onPrintExpressBill();
		}, 100);

		// 组件卸载或 onPrintExpressBill 变更时取消旧的 debounce
		return () => {
			debouncedPrintRef.current?.cancel?.();
		};
	}, [onPrintExpressBill]);

	// 自动打印时调用防抖函数
	useEffect(() => {
		if (isAutoPrintEnabled) {
			debouncedPrintRef.current?.();
		}
	}, [isAutoPrintEnabled]);

	// 手动点击打印按钮的处理函数
	const handlePrintClick = () => {
		debouncedPrintRef.current?.();
	};

	/** ----------------------------------------------------------------- */
	// let preLabelId = '';
	const onSubmit = () => {
		setIsAutoPrintEnabled(false);
		sendPoint(Pointer.退款标签重匹配_查询);
		setPrintRes({ showRes: false });
		form.validateFields(["labelId"]).then(values => {
			// if (values.labelId == preLabelId) {
			// 	playAudio(voice重复扫描);
			// 	return;
			// }
			// preLabelId = values.labelId;
			const params = {
				matchMode: settingConfig?.refundLabelMatch?.matchModel,
				labelId: values.labelId,
				pageNo: 1,
				pageSize: 10
			};

			getTakeLabelMathTradeList(params, tiggerFunction);
			form.resetFields(['labelId']);
			searchInput?.current?.focus();
		});
	};

	const scanSetting = () => {
		setSettingModalVisible(true);
	};

	const validatorTemplate = (_, value) => {
		if (!value) {
			return Promise.reject(new Error("请选择快递模板"));
		} else if (isMergePrint) {
			const templateInfo = kddTempList.find((i) => i.id == value);
			if (!templateInfo) {
				return Promise.reject(new Error("请选择快递模板组"));
			} else if (templateInfo.userTemplateList.length < 1) {
				return Promise.reject(new Error("当前模板组暂无模板，请添加模板或更换模板组"));
			}
		}
		return Promise.resolve();
	};

	const onClickSetTakeLabelTemplate = () => {
		setTakeLabelTemplateModalVisible(true);
	};

	// 修改 onChangeAutoSend 函数
	const onChangeAutoSend = (e) => {
		const isChecked = e.target.checked;
		setAutoSend(isChecked);
		debouncedSaveAutoSend(isChecked, setAutoSend);
	};

	const clearPageData = () => {
		form.setFieldsValue({
			labelId: undefined
		});
	};

	const settingModalOnCancel = () => {
		setSettingModalVisible(false);
	};

	const settingModalOnOk = () => {
		setSettingModalVisible(false);
	};

	useEffect(() => {
		getUserTakeLabelTemplateConfig();
	}, [bhdXbqTempList, printersList, getUserTakeLabelTemplateConfig]);

	useEffect(() => {
		echoSender();
		echoLastTemplate();
		echoPrinter();
		echoAutoSend();
		event.on(CLEAR_PAGE_DATA, clearPageData);
		return () => {
			event.off(CLEAR_PAGE_DATA, clearPageData);
		};
	}, []);

	return (
		<div>
			<Form
				form={ form }
				className={ styles.form }
			>
				<div>
					<Form.Item
						name="labelId"
						className="unique-code"
						rules={ [{
							required: true,
							message: "请输入扫描唯一码/退货唯一码"
						}] }
					>
						<Input placeholder="扫描唯一码/退货唯一码" onPressEnter={ onSubmit } ref={ searchInput } />
					</Form.Item>
					<div>
						<Button type="primary" onClick={ onSubmit }>查询</Button>
						<Button type="link" onClick={ scanSetting }>扫描设置</Button>
					</div>
					<div className="r-fs-12 r-c-666 r-mt-6">支持使用扫描枪扫码，扫描前请将输入法换成英文模式</div>
				</div>

				{/* 快递单设置 */}
				<div className="r-mt-10">
					<div className="r-mb-6">快递单设置</div>
					<Form.Item
						name="printer"
						rules={ [{
							required: true,
							message: "请选择打印机"
						}] }
					>
						<SelectPrinter onChange={ onPrinterChange } />

					</Form.Item>
					<Form.Item
						required
						name="template"
						// validateTrigger={ validateTrigger }
						rules={ [{
							validator: validatorTemplate
						}] }
					>
						<SelectTemplate onChange={ onTemplateChange } />

					</Form.Item>
					<Form.Item
						name="sender"
						rules={ [{
							required: true,
							message: "请选择发件人"
						}] }
					>
						<SelectSender onChange={ onSenderChange } />

					</Form.Item>

				</div>

				<div className="r-mt-10">
					<div className="kdzs-link-text" onClick={ onClickSetTakeLabelTemplate }>拿货标签打印模板设置</div>
				</div>

				<div className="r-flex r-mt-10">
					<div>
						<Button type="primary" onClick={ handlePrintClick }>打印快递单</Button>
					</div>
					<div className="r-ml-12">
						<Button onClick={ onReprintLabel }>重打标签</Button>
					</div>
				</div>

				<div className="r-mt-10">
					<Checkbox checked={ autoSend } onChange={ onChangeAutoSend }>打印后自动发货</Checkbox>
				</div>
			</Form>
			{
				!isMatching && <div className={ styles["error-text"] }>未匹配到订单</div>
			}
			<div className="r-mt-20">
				<PrintResult />
			</div>
			{
				settingModalVisible && <SettingModal onCancel={ settingModalOnCancel } onOk={ settingModalOnOk } />
			}
		</div>
	);
};
export default observer(Search);
