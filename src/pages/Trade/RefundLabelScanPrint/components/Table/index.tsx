
import React, { useEffect, useMemo, useState } from "react";
import { Table, Popover, Tooltip, Tag } from "antd";
import {
	CloseCircleFilled,
	CheckCircleFilled,
	SyncOutlined,
	ExclamationCircleFilled,
	SettingOutlined,
	CopyOutlined,
	PhoneOutlined,
	UserOutlined,
	EnvironmentOutlined,
	PhoneTwoTone,
	EnvironmentTwoTone
} from '@ant-design/icons';
import { cloneDeep } from "lodash";
import { observer } from "mobx-react";
import cs from 'classnames';
import styles from './index.module.scss';
import userStore from "@/stores/user";
import Image from "@/components/Image";
import { DEFAULT_IMG, PLAT_HAND, PLAT_SCMHAND } from "@/constants";
import useGetState from "@/utils/hooks/useGetState";
import { getShopName, isSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import { getTradeFlag, getTradeFlagTag, getTradePlatformLabel, handleDecrypt } from "@/pages/Trade/utils";
import { tradeStore } from "@/stores";
import GoodsContentSetting from "@/pages/AfterSale/ScanRegister/components/ReturnLableRecord/components/Table/GoodsContentSetting";
import { local } from "@/libs/db";
import { LOCAL_PRODUCT_SETTING, LabelStatus } from "../../constants";
import { DEFAULT_PRODUCT_CONTENT_SETTING, PRODUCT_CONTENT_ENUM, PRODUCT_SETTING_OBJECT } from "@/pages/AfterSale/ScanRegister/constants";
import { getPlatformDetailLink, getPlatformTradeLink } from "@/pages/AfterSale/TradeList/utils";
import PlatformIcon from "@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon";
import BuyerNickComp from "@/components-biz/BuyerNickComp";
import { copyToPaste } from "@/utils";
import Icon from "@/components/Icon";
import { TradeTradeDetailGetApi } from "@/apis/trade";
import { inventoryDeduct } from "@/pages/Index/Settings/System/constants";
import BatchModifyMemoModal from "@/pages/Trade/components/BatchModifyMemoModal";
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";


const CustomTable = (props) => {
	const {
		loading,
		showSysItem,
		dataSource: _dataSource,
		onClickSend,
	} = props;
	const {
		isMergePrint,
		kddTempList,
		setModifyMemoPackage,
		refundLabelScanPrintStore: {
			pagination,
			refundLabelMatchList,
			setRefundLabelMatchList,
			getTakeLabelMathTradeList,
			setSelectedRows,
			selectedRows
		}
	} = tradeStore;
	const [productContentList, setProductContentList] = useState(DEFAULT_PRODUCT_CONTENT_SETTING);
	const { isShowZeroStockVersion, systemSetting } = userStore;
	const [editMemoModalVisible, setEditMemoModalVisible] = useState(false);
	const [modifyMemoIndex, setModifyMemoIndex] = useState(-1);
	const [dataSource, setDataSource, getDataSource] = useGetState([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	// const [selectedRows, setSelectedRows] = useState([]);

	/** ----------------- 留言备注相关 ----------------------- */

	const modifyMemo = (item, index) => {
		setModifyMemoIndex(index);
		openModifyMemo([item]);
	};
	const openModifyMemo = (list) => {
		setSelectedRows(list);
		const packList = list.map((item) => ({
			trades: [{
				sellerMemo: item.sellerMemo,
				sellerMemoFlag: item.sellerFlag,
				tid: item.tid,
				ptTid: item.ptTid,
			}],
			platform: item.platform?.toLocaleLowerCase(),
			source: item.source,
			togetherId: item.tid,
			sellerId: item.sellerId,
			refundStatus: item.refundStatus || "",
		}));
		setModifyMemoPackage(packList);
		setEditMemoModalVisible(true);
	};

	// 旗帜标签的回显
	const onModifiedMemoOk = (data) => {
		setEditMemoModalVisible(false);
		const refundLabelMatchListTemp = cloneDeep(refundLabelMatchList);
		refundLabelMatchListTemp.forEach((item, index) => {
			if (index == modifyMemoIndex) {
				item.sellerMemo = data[0]?.memo ?? item.sellerMemo;
				item.sellerFlag = data[0]?.sellerFlag ?? item.sellerFlag;
				item.sellerFlagTag = data[0]?.sellerFlagTag || '';
			}
		});
		setRefundLabelMatchList(refundLabelMatchListTemp);

		setModifyMemoIndex(-1);
	};

	/** ---------------------------------------------------- */


	/** ----------------- 解密收件人 ----------------------- */
	// 获取订单详情
	const getTradeDetail = async(packInfo) => {
		const { tid, sellerId, platform } = packInfo;
		const tradeInfos = [];
		let tradeList = [];
		tradeInfos.push({
			tids: [tid],
			sellerId,
			platform,
		});
		await TradeTradeDetailGetApi({ tradeInfos }).then(res => {
			if (res?.data?.list?.length > 0) {
				tradeList = res?.data?.list;
			}
		}).catch(console.log);
		return tradeList;
	};


	const decryptTrade = async(row) => {
		if (row.isDecrypted) {
			return;
		}
		const tradeDetailList = await getTradeDetail(row);
		if (tradeDetailList.length > 0) {
			try {
				// const decryInfo = await handleDecrypt({
				// 	...tradeDetailList[0].trades[0],
				// 	togetherId: tradeDetailList[0].togetherId
				// }, undefined, false);
				const decryInfo = await handleDecrypt(tradeDetailList[0], undefined, false);
				const { receiverName, receiverPhone, receiverAddress, } = decryInfo || {};
				const refundLabelMatchListTemp = cloneDeep(refundLabelMatchList);
				refundLabelMatchListTemp.forEach(trade => {
					if (trade.tid === row.tid) {
						trade.isDecrypted = true;
						trade.receiverNameMask = receiverName;
						trade.receiverMobileMask = receiverPhone;
						trade.receiverAddressMask = receiverAddress;
					}
				});
				setRefundLabelMatchList(refundLabelMatchListTemp);
			} catch (e) {
				console.log(e);
			}




		}

	};
	/** -------------------------------------------------- */


	/** ----------------- 列表渲染相关 ----------------------- */


	const GoodsContentSettingOnOk = (list) => {
		local.set(LOCAL_PRODUCT_SETTING, list);
		setProductContentList(list);
	};

	const renderGoodsContentColHeader = (
		<div className="r-flex r-ai-c">
			<span>产品内容</span>
			<span className="r-pointer r-ml-4">
				<GoodsContentSetting onOk={ GoodsContentSettingOnOk } storageKey={ LOCAL_PRODUCT_SETTING }>
					<SettingOutlined />
				</GoodsContentSetting>
			</span>
		</div>
	);
	/** ----------------- 列表渲染相关 ----------------------- */

	const renderSerialNumber = (_, __, index) => {
		const { pageSize = 10, current = 1 } = pagination || {};
		const baseNumber = pageSize * (current - 1);
		const curSerialNumber = parseInt(baseNumber + index + 1, 10);
		return <span>{curSerialNumber}</span>;
	};

	const renderGoodsInfo = (_, record) => {

		const showImg = productContentList.includes(PRODUCT_CONTENT_ENUM.货品图片) || productContentList.includes(PRODUCT_CONTENT_ENUM.商品图片);
		let imgSrc = record.picUrl;
		if (productContentList.includes(PRODUCT_CONTENT_ENUM.货品图片)) {
			imgSrc = record.sysPicUrl;
		}
		const getLinkHref = getPlatformDetailLink(record.platform, record.numIid);
		const goodsInfoItems = [];
		const skuInfoItems = [];
		PRODUCT_SETTING_OBJECT.forEach(i => {
			i.list.forEach(item => {
				const allowShow = productContentList.includes(item.value) && ![PRODUCT_CONTENT_ENUM.货品图片, PRODUCT_CONTENT_ENUM.商品图片].includes(item.value);
				// 由于商品简称和货品简称公用一个字段，所以需要特殊处理，不然goodsInfoItems会push两次sysItemAlias
				if (item.value === PRODUCT_CONTENT_ENUM.简称) {
					if (isShowZeroStockVersion && item.showInStockVersion) {
						return;
					}
					if (!isShowZeroStockVersion && item.showInZeroStockVersion) {
						return;
					}
				}
				if (allowShow) {
					const itemContent = (
						<Tooltip title={ item.label }>
							<span>{record[item.value]}</span>
						</Tooltip>
					);
					if (i.key == "good") {
						goodsInfoItems.push(itemContent);
					} else if (i.key == "sku") {
						skuInfoItems.push(itemContent);
					}
				}
			});
		});
		return (
			<div className={ styles['goods-container'] }>
				<div hidden={ !showImg }>
					<Popover overlayClassName={ styles['popover'] } placement="right" content={ <Image src={ imgSrc } /> }>
						<a
							// eslint-disable-next-line no-script-url
							href={ getLinkHref || 'javascript:void(0)' }
							target={ getLinkHref ? '_blank' : '_self' }
							rel="noopener noreferrer"
							className="r-flex r-ai-c"
						>
							<Image src={ imgSrc } />
						</a>
					</Popover>

				</div>
				<div>
					<div className="goods-info">
						{goodsInfoItems}
					</div>
					<div className="sku-info">
						{skuInfoItems}
					</div>
				</div>

			</div>
		);
	};

	const renderTradeInfo = (_, record) => {
		const { source, platform, sellerNick } = record;
		return (
			<div>
				<div className="r-flex r-ai-c">
					<PlatformIcon platform={ platform } />
					
					<span className={ cs("r-bold r-mr-4 r-word-break") }>{!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(record) ? '****' : source?.toLowerCase() === PLAT_HAND ? "无店铺" : sellerNick}</span>
				</div>
				<div className="r-mt-6">
					<div className="r-fc-black-65 r-wb-bw">
						<BuyerNickComp ptTid={ record?.ptTid || '' } tid={ record.tid } encryptuid={ record.buyerOpenUid } platform={ record.platform } buyerNick={ record.buyerNick } sellerId={ record?.sellerId } />
					</div>
					{/* {buyerNick} */}
				</div>
				<div>
					<a
						className="r-fc-1890FF"
						href={ getPlatformTradeLink(record['platform'], record).trade }
						target="_blank"
						rel="noreferrer"
					>{record.ptTid}
					</a>
					<CopyOutlined onClick={ () => { copyToPaste(record.ptTid); } } className="r-fc-black-65 r-pointer r-ml-4 r-mr-5" />
				</div>
			</div>
		);
	};

	const renderReceiverInfo = (_, record) => {
		const {
			isDecrypted,
			receiverState = "",
			receiverCity = "",
			receiverDistrict = "",
			receiverTown = "",
			receiverAddressMask = "-",
			receiverNameMask = "-",
			receiverMobileMask = "-" } = record;
		return (
			<div>
				<div className="r-word-break">
					{/* <span className="r-mr-4"><UserOutlined style={ { color: "#1890ff" } } /></span> */}
					<span>{receiverNameMask}</span>
					<span className="r-ml-10"><PhoneTwoTone /></span>
					<span className="r-ml-4">{receiverMobileMask}</span>
				</div>
				<div>
					<span><EnvironmentTwoTone /> </span>
					<span>{`${receiverState}${receiverCity}${receiverDistrict}${receiverTown}${receiverAddressMask}`}</span>
					<span
						className="r-pointer"
						hidden={ isDecrypted }
						onClick={ () => { decryptTrade(record); } }
					>
						<Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5 } } />
					</span>
				</div>
			</div>
		);
	};

	const renderMemo = (_, record, index) => {
		return (
			<div>
				{record.buyerMessage ? (
					<div>
						<Icon type="beizhu" />
						<span>{record.buyerMessage}</span>
					</div>
				) : ''}
				<span>{getTradeFlag(0, null, record.sellerFlag)} {getTradeFlagTag(record?.sellerFlag, record?.sellerFlagTag, { lineHeight: '22px' })}</span>
				<span className="r-fc-black-65">{record['sellerMemo']}</span>
				<span className="r-as-c r-ml-2 r-fc-1890FF">
					{record['sellerMemo']
						? <span className="r-pointer" onClick={ () => { modifyMemo(record, index); } }><Icon type="bianji" /></span>
						: <span className="r-pointer" onClick={ () => { modifyMemo(record, index); } }><Icon type="tianjiabeizhu" /></span>}
				</span>
			</div>
		);
	};

	const renderSendMethod = (_, record) => {
		return record.isFirstSend ? "先发货" : "普通发货";
	};

	const renderLabelStatus = (_, record) => {
		return LabelStatus[record.labelStatus];
	};

	const renderLabelPrintType = (_, record) => {
		return record.labelIsPrint ? "今日已打印" : "今日未打印";
	};
	/** --------------- A splendid dividing line ----------- */


	const columns:any[] = [{
		title: "序号",
		dataIndex: "serialNumber",
		width: 60,
		fixed: "left",
		render: renderSerialNumber

	}, {
		title: renderGoodsContentColHeader,
		dataIndex: "goodsInfo",
		width: 300,
		render: renderGoodsInfo
	}, {
		title: "付款时间",
		dataIndex: "payTime",
		width: 140,
	}, {
		title: "系统单号",
		width: 140,
		render: (_, record, index) => {
			return <div>{record?.tid || ''}</div>;
		}
	}, {
		title: "订单信息",
		dataIndex: "tradeInfo",
		width: 200,
		render: renderTradeInfo
	}, {
		title: "收件信息",
		dataIndex: "receiverInfo",
		width: 200,
		render: renderReceiverInfo
	}, {
		dataIndex: "sellerMemo",
		title: "留言备注",
		render: renderMemo,
		width: 120,
	}, {
		title: "发货方式",
		dataIndex: "sendMethod",
		width: 80,
		render: renderSendMethod
	}, {
		title: "唯一码",
		dataIndex: "labelId",
		width: 100,
	}, {
		title: "标签状态",
		dataIndex: "labelStatus",
		width: 80,
		render: renderLabelStatus
	}, {
		title: "标签打印",
		dataIndex: "labelPrintType",
		width: 100,
		render: renderLabelPrintType
	}, {
		title: "累计打印次数",
		width: 120,
		dataIndex: "labelPrintNum"
	}];


	const pageOnChange = (page, pageSize) => {
		const params = {
			labelId: refundLabelMatchList?.[0].currentScanLabelId,
			pageNo: page,
			pageSize,
		};
		if (pageSize !== pagination.pageSize) {
			params.pageNo = 1;
		}
		setSelectedRows([]);
		getTakeLabelMathTradeList(params);
	};
	const handleProductContentList = () => {
		let productSetting = local.get(LOCAL_PRODUCT_SETTING);
		if (productSetting) {
			setProductContentList(productSetting);
		}
	};

	const rowSelection:any = {
		selectedRowKeys,
		fixed: "left",
		type: "radio",
		// getCheckboxProps: record => ({
		// 	disabled: record.isCurrentLabelTrade
		// }),
		onChange: (selectedRowKeys: React.Key[], selectedRows) => {
			setSelectedRows(selectedRows);

		},
	};

	useEffect(() => {
		const selectedRowKeys = selectedRows.map(record => record.tid);
		setSelectedRowKeys(selectedRowKeys);
	}, [selectedRows]);

	useEffect(() => {
		handleProductContentList();
	}, []);
	return (
		<div>
			<Table
				rowKey="tid"
				loading={ loading }
				className={ styles.table }
				rowSelection={ rowSelection }
				dataSource={ refundLabelMatchList }
				columns={ columns }
				scroll={ { x: 100 } }
				pagination={ false }
			/>
			{editMemoModalVisible ? (
				<BatchModifyMemoModal
					onOk={ onModifiedMemoOk }
					onCancel={ () => setEditMemoModalVisible(false) }
				/>
			) : ''}
		</div>

	);
};

export default observer(CustomTable);
