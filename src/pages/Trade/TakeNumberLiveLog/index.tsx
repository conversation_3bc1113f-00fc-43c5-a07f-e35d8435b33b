import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { Button, Image, Select, Form, Tooltip, Input, Modal } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { CopyOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import cs from 'classnames';
import dayjs from 'dayjs';
import SearchTable from "@/components/SearchTableVirtual";
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { PLAT_MAP } from '@/constants';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import { clearParams } from '@/utils/stringHelper';
import message from "@/components/message";
import { 
	getPlatAndShops,
	getMultiShops
} from '@/components-biz/ShopListSelect/shopListUtils';
import { DatePickerKey, getCacheDateRange } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import { TradeLivePrintSelectLiveCommentRecordWithPageApi } from '@/apis/trade/live';
import { TradeLivePrintSelectLiveCommentRecordWithPageRequest, TradeLivePrintSelectLiveCommentRecordWithPageResponse } from '@/types/trade/index/live';
import userStore from '@/stores/user';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import PrintNumSet from '@/pages/Report/PrintLog/components/PrintNumSet';
import { OperationTypeList } from './constants';
import s from './index.module.scss';

const initialRangeTime = [dayjs().subtract(6, 'd').startOf('day'), dayjs().endOf('day')];
const defaultParams = {
	operateType: 1,
	operateTime: initialRangeTime,
	platformInfo: undefined,
	liveNo: undefined,
	buyerNick: undefined,
	comment: undefined,
};
interface LogItem {
	liveNo: string;
	buyerNick: string;
	comment: string;
	
	[key: string]: any;
}

const { Option } = Select;

// 达人管理
const TakeNumberLiveLog: React.FC = () => {
	const [form] = Form.useForm();
	const tableRef = useRef<SearchTableRefProps>();
	const [formData, setFormData] = useState<any>({ ...defaultParams });


	// 表单配置
	const FormFieldList: FormItemConfig[] = [
		{
			name: "operateType",
			label: "",
			children: (
				<Select
					className={ cs("r-w-full") }
					placeholder="操作类型"
					size="small"
					style={ { width: 150 } }
					allowClear={ false }
				>
					{
						OperationTypeList.map(v => (
							<Option value={ v.value } key={ v.value }>
								{v.name}
							</Option>
						))
				 }
				</Select>
			),
		},
		{
			name: "operateTime",
			children: (
				<KdzsDateRangePicker1 
					style={ { width: 159 } } 
					cacheQuickChoose={ false }
					useServeTime 
					placeholder={ ['开始时间', '结束时间'] } 
				/>
			),
		},
		{
			name: "platformInfo",
			children: (
				<ShopMultiSelect
					placeholderArray={ ["选择平台", "选择店铺"] }
					isSendPoint
					style={ { width: 159 } }
					filterOptions={ [] }
					bgHighLight
					size="small"
				/>
			),
		},
		{
			name: "liveNo",
			children: <Input placeholder="直播场次" style={ { width: 160 } } className={ formData.liveNo ? 'high-light-bg' : '' } allowClear />,
		},
		{
			name: "buyerNick",
			children: <Input placeholder="买家昵称" style={ { width: 160 } } className={ formData.buyerNick ? 'high-light-bg' : '' } allowClear />,
		},
		{
			name: "comment",
			children: <Input placeholder="扣号内容" style={ { width: 160 } } className={ formData.comment ? 'high-light-bg' : '' } allowClear />,
		}
	];

	// 表格列配置
	const getColumns = useMemo(() => {
		const columns: ColumnsType<LogItem> = [
			{
				title: '序号',
				align: 'center',
				width: 40,
				render: (text, row, index) => {
					return <>{index + 1}</>;
				}
			},
			{
				title: '直播场次',
				width: 160,
				minWidth: 100,
				dataIndex: 'liveNo',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '平台店铺',
				width: 160,
				dataIndex: 'platform',
				render: (_, record) => {
					return (
						<div>
							<div className={ s.platformInfo }>
								<PlatformIcon platform={ record['platform'] } fontSize={ 16 } />
								<span className={ s.platformName }>{record.sellerNick}</span>
							</div>
						</div>
					);
				}
			},
			{
				title: '扣号时间',
				width: 160,
				dataIndex: 'commentTime',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '买家昵称',
				width: 160,
				dataIndex: 'buyerNick',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '扣号内容',
				width: 160,
				dataIndex: 'commentContent',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '打印时间',
				width: 160,
				dataIndex: 'printTime',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '标签序号',
				width: 120,
				dataIndex: 'labelNo',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '打印序号',
				width: 120,
				dataIndex: 'printNo',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '操作',
				width: 160,
				render: (_, record) => {
					return (
						<Button type="link" size="small" onClick={ () => handlePrint(record) } style={ { color: '#1890FF', padding: '0px' } }>
							重新打印
						</Button>
					);
				}
			}
		];

		return columns;
	}, []);

	// 重新打印
	const handlePrint = (record) => {
		const { authorName, authorId, id, platform } = record;
	
		
	};

	// 接口查询函数
	const fetchList = async(params: any) => {
		// 处理搜索参数
		for (let key in params) {
			if (typeof params[key] === 'string') {
				params[key] = params[key].trim();
			}
		}

		const { platformInfo, operateTime, ...restParams } = params;
		const { plats, plat_sellerIds } = platformInfo || {};
		// const { shopId, platform } = await getPlatAndShops(platformInfo, false);
		const multiShopList:any = await getMultiShops({ plats, plat_sellerIds });

		// 构建接口请求参数
		const requestParams: TradeLivePrintSelectLiveCommentRecordWithPageRequest = {
			liveNo: params.liveNo,
			buyerNick: params.buyerNick,
			liveCommentContent: params.comment, // 扣号内容
			userId: userStore.userInfo?.userId, // 用户ID
			pageNo: params.pageNo,
			pageSize: params.pageSize,
			multiShopS: multiShopList,
			shopIsActualSelect: !plats?.length && !plat_sellerIds?.length ? 0 : 1,
		};

		// 处理时间范围参数
		if (operateTime) {
			const timeType = params.operateType === 1 ? 'print' : 'liveComment';
			const [startTime, endTime] = operateTime;
			if (!startTime && !endTime) {
				message.error("查询时间不能为空");
				return;
			}
			if (!startTime) {
				message.error("开始时间不能为空");
				return;
			}
			if (!endTime) {
				message.error("结束时间不能为空");
				return;
			}
			requestParams[`${timeType}TimeStart`] = operateTime[0].format('YYYY-MM-DD HH:mm:ss');
			requestParams[`${timeType}TimeEnd`] = operateTime[1].format('YYYY-MM-DD HH:mm:ss');
		}


		// 清理空参数
		const cleanedParams = clearParams(requestParams, true);

		// 调用真实接口
		return TradeLivePrintSelectLiveCommentRecordWithPageApi(cleanedParams);
	};

	// 数据适配器 - 使用接口返回的原始状态值进行统计
	const responseAdapter = (data: any) => {
		const { list = [], total = 0 } = data || {};

		return {
			list,
			total
		};
	};

	const onFieldsChange = (changedValues, allValues) => {
		console.log('%c [ changedValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues, allValues);
		setFormData(allValues);
	};

	const _onChange = (pagination, filters, sorter, { action }) => {
		// 切换分页需要重置勾选项
		if (action == 'paginate') {
			// setSelectedRows([]);
			// setSelectedRowKeys([]);
		}
	};

	const expandContext = (
		<div className={ s.tabContainer }>
			<div className="r-flex r-ai-c r-jc-fe">
				<div className="r-flex r-ai-c">
					<PrintNumSet type="default" size="middle" />
				</div>
			</div>
		</div>
	);

	const onReset = () => {
		console.log('%c [ 重置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '重置');
		setFormData(defaultParams);
	};

	useEffect(() => {
		userStore.getSystemSetting().then(res => {
			console.log('%c [ 系统设置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
		});
	}, []);

	return (
		<NormalLayout className={ cs(s.takeNumberLiveLog, "r-bg-white") }>
			<SearchTable
				pageSizeId="takeNumberLiveLog"
				ref={ tableRef }
				form={ form }
				fetchData={ fetchList }
				responseAdapter={ responseAdapter }
				onReset={ onReset }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				rowFormConfig={ {
					defaultParams,
					formList: FormFieldList,
					colProps: {
						// span: 3
					},
					size: "small",
				} }
				baseTableConfig={ {
					rowKey: "id",
					columns: getColumns,
					cachePgination: true,
					pagination: {
						defaultPageSize: 200,
						pageSizeOptions: [10, 20, 50, 100, 200, 500],
					},
					expandContext,
					expandContextStyle: {
						marginBottom: '0px',
						padding: '16px 16px 12px'
					},
					noGap: true,
					innerTableStyle: {
						paddingTop: 0
					},
					isStickyHeader: true,
					stickyTop: 155,
					headerColSet: {
						resizeId: `TakeNumberLiveLog_width_${userStore?.userInfo?.userId}`,
					},
					onFieldsChange, // 查询项变更
				} }
				onChange={ ({ pageNo, pageSize }) => {
					_onChange({ current: pageNo, pageSize }, undefined, undefined, { action: 'paginate' });
				} }
			/>
		</NormalLayout>
	);
};

export default observer(TakeNumberLiveLog);