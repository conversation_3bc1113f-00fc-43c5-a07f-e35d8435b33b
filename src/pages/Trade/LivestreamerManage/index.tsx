import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { Button, Image, Select, Form, Tooltip, Input, Modal } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { CopyOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import cs from 'classnames';
import s from './index.module.scss';
import SearchTable from "@/components/SearchTableVirtual";
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { PLAT_MAP } from '@/constants';
import PlatformMultiSelect from '@/components-biz/ShopListSelect/PlatformMultiSelect';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import { clearParams } from '@/utils/stringHelper';
import message from "@/components/message";
import { 
	getPlatAndShops 
} from '@/components-biz/ShopListSelect/shopListUtils';
import { TradeLiveAuthorQueryPageListApi, TradeLiveAuthorSaveApi, TradeLiveAuthorDeleteApi } from '@/apis/trade/live';
import { TradeLiveAuthorQueryPageListRequest, TradeLiveAuthorQueryPageListResponse } from '@/types/trade/index/live';
import userStore from '@/stores/user';
import AddLivestreamerModal from './components/AddLivestreamerModal';
import AuthorManageSettingModal from './components/AuthorManageSettingModal';

const defaultParams = {
	operateType: 1,
	operateTime: undefined,
	platformInfo: undefined,
	liveName: undefined,
	liveId: undefined,
};

const OperationTypeList = [
	{
		name: '添加时间',
		value: 1
	}
];

const { Option } = Select;

interface LogItem {
	liveName: string;
	liveId: string;
	operateTime: string;
	sourcePlatformInfo: string;
	platformInfo: string;
	
	[key: string]: any;
}

// 达人管理
const LivestreamerManageLog: React.FC = () => {
	const [form] = Form.useForm();
	const tableRef = useRef<SearchTableRefProps>();
	const [formData, setFormData] = useState<any>({ ...defaultParams });
	const [addModalVisible, setAddModalVisible] = useState(false);
	const [settingModalVisible, setSettingModalVisible] = useState(false);


	// 表单配置
	const FormFieldList: FormItemConfig[] = [
		{
			name: "operateType",
			label: "",
			children: (
				<Select
					className={ cs("r-w-full") }
					placeholder="操作类型"
					size="small"
					style={ { width: 150 } }
					allowClear={ false }
				>
					{
						OperationTypeList.map(v => (
							<Option value={ v.value } key={ v.value }>
								{v.name}
							</Option>
						))
				 }
				</Select>
			),
		},
		{
			name: "operateTime",
			children: (
				<KdzsDateRangePicker1 
					style={ { width: 159 } } 
					cacheQuickChoose={ false }
					useServeTime 
					placeholder={ ['开始时间', '结束时间'] } 
				/>
			),
		},
		{
			name: "platformInfo",
			children: (
				<PlatformMultiSelect
					size="small"
					placeholder="选择平台"
					onChange={ (value) => console.log(value) }
					bgHighLight
					width={ 159 }
					filterOptions={ [] }
				/>
			),
		},
		{
			name: "liveName",
			children: <Input placeholder="达人名称" style={ { width: 160 } } className={ formData.liveName ? 'high-light-bg' : '' } allowClear />,
		},
		{
			name: "liveId",
			children: <Input placeholder="达人ID" style={ { width: 160 } } className={ formData.liveId ? 'high-light-bg' : '' } allowClear />,
		}
	];

	// 表格列配置
	const getColumns = useMemo((): ColumnsType<LogItem> => {
		const columns: ColumnsType<LogItem> = [
			{
				title: '序号',
				align: 'center',
				width: 40,
				render: (text, row, index) => {
					return <>{index + 1}</>;
				}
			},
			{
				title: '达人信息',
				width: 200,
				minWidth: 100,
				dataIndex: 'authorName',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '达人ID',
				width: 200,
				dataIndex: 'authorId',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '平台',
				width: 200,
				dataIndex: 'platform',
				render: (_, record) => {
					return (
						<div>
							{PLAT_MAP[_]}
						</div>
					);
				}
			},
			{
				title: '添加时间',
				width: 200,
				dataIndex: 'gmtCreated',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '操作人',
				width: 200,
				dataIndex: 'operator',
				render: (_, record) => {
					return _;
				}
			},
			{
				title: '操作',
				width: 100,
				render: (_, record) => {
					return (
						<Button type="link" size="small" onClick={ () => handleDelete(record) }>
							删除
						</Button>
					);
				}
			}
		];

		return columns;
	}, []);

	// 打开添加达人弹框
	const handleAddLivestreamer = () => {
		setAddModalVisible(true);
	};

	// 关闭添加达人弹框
	const handleAddModalCancel = () => {
		setAddModalVisible(false);
	};

	// 添加达人成功回调
	const handleAddModalSuccess = () => {
		setAddModalVisible(false);
		// 刷新列表
		tableRef.current?.refresh();

		// 重新请求下所有达人
		userStore.getAuthorList(true);
	};

	// 打开设置弹框
	const handleOpenSetting = () => {
		setSettingModalVisible(true);
	};

	// 关闭设置弹框
	const handleSettingModalCancel = () => {
		setSettingModalVisible(false);
	};

	// 删除
	const handleDelete = (record) => {
		const { authorName, authorId, id, platform } = record;
	
		Modal.confirm({
			title: '删除达人信息',
			centered: true,
			icon: <ExclamationCircleOutlined />,
			content: (
				<div>
					<p>确定删除达人信息吗？</p>
					<p><span className="r-c-black65">达人名称：{authorName}</span></p>
					<p><span className="r-c-black65">达人ID：{authorId}</span></p>
					<p><span className="r-c-black65">平台：{PLAT_MAP[platform]}</span></p>
				</div>
			),
			okText: '确定删除',
			cancelText: '取消',
			onOk: async() => {
				try {
					const res = await TradeLiveAuthorDeleteApi({ id });
					message.success('删除成功');
					// 刷新列表
					tableRef.current?.refresh();
				} catch (error) {
					console.error('删除失败:', error);
				}
			},
			onCancel: () => {
				console.log('取消删除');
			}
		});
	};

	// 接口查询函数
	const fetchList = async(params: any) => {
		// 处理搜索参数
		for (let key in params) {
			if (typeof params[key] === 'string') {
				params[key] = params[key].trim();
			}
		}

		const { platformInfo, sourcePlatformInfo, ...restParams } = params;
		const { shopId, platform } = await getPlatAndShops(platformInfo, false);

		// 处理时间范围参数
		let startTime;
		let endTime;
		if (params.operateTime && params.operateTime.length) {
			startTime = params.operateTime[0]?.format('YYYY-MM-DD HH:mm:ss');
			endTime = params.operateTime[1]?.format('YYYY-MM-DD HH:mm:ss');
		}

		// 构建接口请求参数
		const requestParams: TradeLiveAuthorQueryPageListRequest = {
			timeType: params.operateType, // 时间类型,1.创建时间
			pageNo: params.pageNo,
			pageSize: params.pageSize,
			startTime,
			endTime,
			platformList: platform, // 这里是数组
			authorName: params.liveName,
			authorId: params.liveId,
		};

		// 清理空参数
		const cleanedParams = clearParams(requestParams, true);

		// 调用真实接口
		return TradeLiveAuthorQueryPageListApi(cleanedParams);
	};

	// 数据适配器 - 使用接口返回的原始状态值进行统计
	const responseAdapter = (data: TradeLiveAuthorQueryPageListResponse['data']) => {
		const { list = [], total = 0 } = data || {};

		return {
			list,
			total
		};
	};

	const onFieldsChange = (changedValues, allValues) => {
		console.log('%c [ changedValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues, allValues);
		setFormData(allValues);
	};

	const _onChange = (pagination, filters, sorter, { action }) => {
		// 切换分页需要重置勾选项
		if (action == 'paginate') {
			// setSelectedRows([]);
			// setSelectedRowKeys([]);
		}
	};

	const expandContext = (
		<div className={ s.tabContainer }>
			<div className="r-flex r-ai-c r-jc-sb">
				<div className="r-flex r-ai-c">
					<Button type="primary" size="middle" onClick={ handleAddLivestreamer }>
						添加达人信息
					</Button>
				</div>
				<div className="r-flex r-ai-c">
					<Button type="default" size="middle" onClick={ handleOpenSetting }>
						达人管理设置
					</Button>
				</div>
			</div>
		</div>
	);

	const onReset = () => {
		console.log('%c [ 重置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '重置');
		// form.resetFields();

		setFormData(defaultParams);
	};

	useEffect(() => {
		userStore.getSystemSetting().then(res => {
			console.log('%c [ 系统设置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
		});
	}, []);

	return (
		<NormalLayout className={ cs(s.livestreamerManageLog, "r-bg-white") }>
			<SearchTable
				pageSizeId="livestreamerManageLog"
				ref={ tableRef }
				form={ form }
				fetchData={ fetchList }
				responseAdapter={ responseAdapter }
				onReset={ onReset }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				rowFormConfig={ {
					defaultParams,
					formList: FormFieldList,
					colProps: {
						// span: 3
					},
					size: "small",
				} }
				baseTableConfig={ {
					rowKey: "id",
					columns: getColumns,
					cachePgination: true,
					pagination: {
						defaultPageSize: 200,
						pageSizeOptions: [10, 20, 50, 100, 200, 500],
					},
					expandContext,
					expandContextStyle: {
						marginBottom: '0px',
						padding: '16px 16px 12px'
					},
					noGap: true,
					innerTableStyle: {
						paddingTop: 0
					},
					isStickyHeader: true,
					stickyTop: 155,
					headerColSet: {
						resizeId: `LivestreamerManageLog_width_${userStore?.userInfo?.userId}`,
					},
					onFieldsChange, // 查询项变更
				} }
				onChange={ ({ pageNo, pageSize }) => {
					_onChange({ current: pageNo, pageSize }, undefined, undefined, { action: 'paginate' });
				} }
			/>
			
			{/* 添加达人信息弹框 */}
			<AddLivestreamerModal
				visible={ addModalVisible }
				onCancel={ handleAddModalCancel }
				onSuccess={ handleAddModalSuccess }
			/>

			{/* 达人管理设置弹框 */}
			<AuthorManageSettingModal
				visible={ settingModalVisible }
				onCancel={ handleSettingModalCancel }
			/>
		</NormalLayout>
	);
};

export default observer(LivestreamerManageLog);