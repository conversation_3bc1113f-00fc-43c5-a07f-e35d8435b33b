import React, { useEffect, useState } from 'react';
import { Modal, Checkbox, Button } from 'antd';
import { observer } from 'mobx-react';
import s from './AuthorManageSettingModal.module.scss';
import { SettingSaveSystemSettingApi } from '@/apis/user';
import userStore from '@/stores/user';
import message from '@/components/message';

export interface AuthorManageSettingModalProps {
  visible: boolean;
  onCancel: () => void;
}

const AuthorManageSettingModal: React.FC<AuthorManageSettingModalProps> = observer(({
	visible,
	onCancel
}) => {
	const [loading, setLoading] = useState(false);
	const [autoUpdateAuthorInfo, setAutoUpdateAuthorInfo] = useState(false);

	// 获取配置
	const getConfig = async() => {
		try {
			const systemSetting = await userStore.getSystemSetting();
			const value = systemSetting?.extendInfo?.authorSettingTradeSyncAutoUpdateAuthorInfo;
			console.log('%c [ 自动将订单中的达人信息添加至列表 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
			setAutoUpdateAuthorInfo(value === 1 || value === true);
		} catch (error) {
			console.error('获取配置失败:', error);
		}
	};

	// 保存配置
	const handleSave = async() => {
		setLoading(true);
		try {
			const params = {
				extendInfo: {
					authorSettingTradeSyncAutoUpdateAuthorInfo: autoUpdateAuthorInfo ? 1 : 0
				}
			};
      
			const saveRes = await SettingSaveSystemSettingApi(params);
			if (saveRes) {
				message.success('保存成功');
				// 刷新系统设置缓存
				const setting = await userStore.getSystemSetting(true);
				userStore.setSystemSetting(setting);
				onCancel();
			} else {
				message.error('保存失败');
			}
		} catch (error) {
			console.error('保存失败:', error);
			message.error('保存失败');
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (visible) {
			getConfig();
		}
	}, [visible]);

	return (
		<Modal
			title="达人管理设置"
			visible={ visible }
			onCancel={ onCancel }
			maskClosable={ false }
			width={ 480 }
			className={ s.authorManageSettingModal }
			footer={ [
				<Button key="cancel" onClick={ onCancel }>
					取消
				</Button>,
				<Button key="save" type="primary" loading={ loading } onClick={ handleSave }>
					确定
				</Button>
			] }
		>
			<div className={ s.modalContent }>
				<Checkbox
					checked={ autoUpdateAuthorInfo }
					onChange={ (e) => setAutoUpdateAuthorInfo(e.target.checked) }
				>
					自动将订单中的达人信息添加至列表
				</Checkbox>
			</div>
		</Modal>
	);
});

export default AuthorManageSettingModal;
