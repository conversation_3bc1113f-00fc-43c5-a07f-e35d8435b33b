import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Radio, Button, message } from 'antd';
import { RadioChangeEvent } from 'antd/lib/radio';
import PlatformMultiSelect from '@/components-biz/ShopListSelect/PlatformMultiSelect';
import { 
	getPlatAndShops, 
	getPlatAndShopsWithFilter
} from '@/components-biz/ShopListSelect/shopListUtils';
import { TradeLiveAuthorSaveApi } from '@/apis/trade/live';
import { TradeLiveAuthorSaveRequest } from '@/types/trade/index/live';
import s from './AddLivestreamerModal.module.scss';

export interface AddLivestreamerModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

enum AddTypeEnum {
  NAME_ID = 'name_id',
  ORDER_NO = 'order_no'
}

const AddLivestreamerModal: React.FC<AddLivestreamerModalProps> = ({
	visible,
	onCancel,
	onSuccess
}) => {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const [addType, setAddType] = useState<AddTypeEnum>(AddTypeEnum.NAME_ID);

	// 重置表单
	const resetForm = () => {
		form.resetFields();
		setAddType(AddTypeEnum.NAME_ID);
	};

	// 关闭弹框
	const handleCancel = () => {
		resetForm();
		onCancel();
	};

	// 添加类型变更
	const handleAddTypeChange = (e: RadioChangeEvent) => {
		const newType = e.target.value;
		setAddType(newType);
		// 切换类型时清空输入值
		if (newType === AddTypeEnum.NAME_ID) {
			form.setFieldsValue({ orderNo: undefined });
		} else {
			form.setFieldsValue({ liveName: undefined, liveId: undefined, platformInfo: undefined });
		}
	};

	// 提交表单
	const handleSubmit = async() => {
		try {
			const values = await form.validateFields();
			setLoading(true);

			// 根据添加类型调用不同接口
			if (addType === AddTypeEnum.NAME_ID) {
				// 根据名称/ID添加达人
				await TradeLiveAuthorSaveApi({
					addType: 1,
					authorName: values.liveName,
					authorId: values.liveId,
					platform: values.platformInfo?.plats?.[0]
				});
			} else {
				// 根据订单编号添加达人
				await TradeLiveAuthorSaveApi({
					addType: 2,
					ptTid: values.orderNo
				});
			}

			message.success('添加达人信息成功');
			resetForm();
			onSuccess(); // 刷新列表
		} catch (error) {
			console.error('添加达人信息失败:', error);
			// message.error('添加达人信息失败');
		} finally {
			setLoading(false);
		}
	};

	// 达人名称验证规则
	const liveNameRules = [
		{ required: true, message: '请填写内容后保存' },
		{ max: 100, message: '达人名称不能超过100个字符' },
		{
			pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s\p{P}\p{S}\p{So}]+$/u,
			message: '达人名称支持中文、英文、数字、符号、表情'
		}
	];

	// 订单编号验证规则
	const orderNoRules = [
		{ required: true, message: '请填写内容后保存' },
		{ max: 50, message: '订单编号不能超过50个字符' }
	];

	// 平台验证规则
	const platformRules = [
		{
			required: true,
			message: '请选择平台后保存'
		},
		{
			validator: (_, value) => {
				if (value?.plats?.length === 0) {
					return Promise.reject(new Error('请选择平台后保存'));
				}
				return Promise.resolve();
			}
		}
	];

	// 达人ID验证规则
	const liveIdRules = [
		{
			required: true,
			message: '请填写内容后保存'
		}
	];

	return (
		<Modal
			title="添加达人信息"
			visible={ visible }
			width={ 480 }
			centered
			destroyOnClose
			maskClosable={ false }
			className={ s.addLivestreamerModal }
			okText="确定"
			cancelText="取消"
			onOk={ handleSubmit }
			onCancel={ handleCancel }
			okButtonProps={ {
				loading
			} }
		>
			<div className={ s.modalContent }>
				<Form
					form={ form }
					layout="horizontal"
					className={ s.form }
					labelCol={ { span: 5 } }
					wrapperCol={ { span: 19 } }
					initialValues={ { 
						addType: AddTypeEnum.NAME_ID 
					} }
				>
					{/* 添加类型选择 */}
					<Form.Item label="" name="addType">
						<Radio.Group
							value={ addType }
							onChange={ handleAddTypeChange }
							className={ s.radioGroup }
						>
							<Radio value={ AddTypeEnum.NAME_ID } className={ s.radioItem }>
								根据名称/ID添加
							</Radio>
							<Radio value={ AddTypeEnum.ORDER_NO } className={ s.radioItem }>
								根据订单编号添加
							</Radio>
						</Radio.Group>
					</Form.Item>

					{/* 根据名称/ID添加 */}
					{addType === AddTypeEnum.NAME_ID && (
						<>
							<Form.Item
								name="liveName"
								label="达人名称"
								rules={ liveNameRules }
								className={ s.formItem }
							>
								<Input
									placeholder="请填写达人名称"
									maxLength={ 100 }
									showCount
									className={ s.input }
									allowClear
								/>
							</Form.Item>

							<Form.Item
								name="liveId"
								label="达人ID"
								className={ s.formItem }
								rules={ liveIdRules }
							>
								<Input
									placeholder="请填写达人ID"
									className={ s.input }
									allowClear
								/>
							</Form.Item>

							<Form.Item 
								label="平台" 
								name="platformInfo"
								className={ s.formItem }
								rules={ platformRules }
							>
								<PlatformMultiSelect
									size="middle"
									placeholder="请选择平台"
									onChange={ (value) => console.log(value) }
									bgHighLight={ false }
									width="100%"
									filterOptions={ [] }
									// dropdownMatchSelectWidth={ false }
									dropdownStyle={ { maxHeight: 200 } }
									chooseOne
								/>
							</Form.Item>
						</>
					)}

					{/* 根据订单编号添加 */}
					{addType === AddTypeEnum.ORDER_NO && (
						<Form.Item
							name="orderNo"
							label="订单编号"
							rules={ orderNoRules }
							className={ s.formItem }
						>
							<Input
								placeholder="请填写达人所属的订单编号"
								className={ s.input }
							/>
						</Form.Item>
					)}
				</Form>
			</div>
		</Modal>
	);
};

export default AddLivestreamerModal;
