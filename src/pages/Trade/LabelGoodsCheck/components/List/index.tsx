import React, { Fragment, memo, useEffect, useState, forwardRef, useImperativeHandle, useCallback } from 'react';
import { observer } from 'mobx-react';
import { But<PERSON>, Modal, Popover, Radio } from 'antd';
import dayjs from 'dayjs';
import _, { debounce } from 'lodash';
import message from '@/components/message';
import Icon from '@/components/Icon';
import s from './index.module.scss';
import ListHeader from './ListHeader';
import ListItem from './ListItem';
import PrintCenter from '@/print/index';
import { ItemTakeGoodsLabelCheckGoodsResponse, ItemTakeGoodsLabelCheckGoodsResultExportRequest } from '@/types/trade/takeGoodsLabel';
import { IScanInfo } from '../..';
import { ItemTakeGoodsLabelCheckGoodsResultExportApi } from '@/apis/trade/takeGoodsLabel';
import { CheckGoodsType, ExportScanType } from '../constants';
import SelectTemplateModal from '../SelectTemplateModal';
import useGetState from '@/utils/hooks/useGetState';
import FieldsPermissionCheck, { FieldsPermissionCheckTypeEnum, FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { BQ_PRINT_TYPE } from '@/pages/Trade/constants';
import { LabelPrintSourceEnum } from '@/constants/labelPush';

interface ITradeList {
	scanInfo: IScanInfo,
	checkGoodsType: string,
	clearList: (data?:{})=> void;
	list: ItemTakeGoodsLabelCheckGoodsResponse["data"][];
	listTitle: {text: string, key:string}[];
	setListToProps: (data?:{})=> void;
}
const TradeList = forwardRef((props:ITradeList, ref) => {
	const { list, scanInfo, clearList, checkGoodsType, listTitle, setListToProps } = props;
	const [exportScanModalVisible, setExportScanModalVisible] = useState(false);
	const [exportType, setExportType] = useState("STATISTIC_RESULT");
	const [oldIndex, setOldIndex] = useState(-1);
	const [_list, setList, getList] = useGetState([]);
	const [isShiftDown, setIsShiftDown] = useState(false);
	const [selectTemplateModalVisible, setSelectTemplateModalVisible] = useState(false);

	useEffect(() => {
		setList(list);
	}, [list]);

	useEffect(() => {
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.退货清单);
	}, []);

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `标签点货: 【 ${dataType} 】`,
			data: {
				...data
			}
		});
	};

	const clear = () => {
		let listTemp = _list.filter(i => !i.isChecked);
		setListToProps(listTemp);
	};
	const exportRes = () => {
		let listTemp = _list.slice(0, _list.length);
		const params:ItemTakeGoodsLabelCheckGoodsResultExportRequest = {
			exportType,
			checkGoodsType,
			checkGoodsResultList: listTemp
		};
		let _date = dayjs(new Date()).format('YYYYMMDDHHmmss');
		const fileText = `${CheckGoodsType[checkGoodsType]}_${ExportScanType[exportType]}_${_date}`;
		ItemTakeGoodsLabelCheckGoodsResultExportApi(params).then((res:any) => {
			let blob = new Blob([res]);
			const aLink = document.createElement('a');
			aLink.download = `${fileText}.xlsx`;
			aLink.href = window.URL.createObjectURL(blob);
			aLink.click();
		}).catch((error) => {});
		setExportScanModalVisible(false);

		const points = {
			STATISTIC_RESULT: Pointer.标签点货_导出本次扫描结果_统计结果,
			SCAN_DETAIL: Pointer.标签点货_导出本地扫描结果_扫描明细,
		};
		sendPoint(points[exportType]);
	};
	const exportConfirm = () => {
		if (!_list.length) {
			message.error('请先查询后再导出');
			return;
		}
		const type = localStorage.getItem('Label_ExportScanType');
		type && setExportType(type);
		setExportScanModalVisible(true);
	};
	const onChangeExportType = (e:any) => {
		const { value } = e.target;
		setExportType(value);
		window.localStorage.setItem(`Label_ExportScanType`, value);
	};
	const dealShift = ({ pack, index }) => {
		const listTemp = [..._list];
		// 按下shift 时选择
		if (isShiftDown) {
			let start = -1;
			let end = -1;
			// 找到开始和结束位置
			if (index > oldIndex) {
				start = oldIndex;
				end = index;
			} else {
				start = index;
				end = oldIndex;
			}
			// 判断是选中还是取消选中
			const checked = _list[oldIndex].isChecked;
			// 将shift选中的部分批量选中或取消选中
			for (let i = start; i <= end; i++) {
				listTemp[i].isChecked = checked;
			}
		} else {
			setOldIndex(index);
			listTemp[index].isChecked = !listTemp[index].isChecked;
		}
		setList(listTemp);
	};
	const keyDown = () => {
		// 键盘按下事件
		document.onkeydown = (e:{keyCode:number}) => {
			let e1:any = e || window.event;
			// 键盘按键判断:左箭头-37;上箭头-38；右箭头-39;下箭头-40  回车：13   ctrl：17   shift：16
			if (e1.keyCode === 16) setIsShiftDown(true);
		};
		// 键盘抬起事件
		document.onkeyup = (e) => {
			let e1:any = e || window.event;
			if (e1.keyCode === 16) setIsShiftDown(false);
		};

		window.addEventListener('blur', () => {
			setIsShiftDown(false);
		});
	};
	const handleChoiceChange = (choiceItem:any) => {
		const noTradeTipFn = (checkCount: number) => {
			if (checkCount === 0) {
				message.info('当前没有符合勾选的订单');
			}
		};
		if (list.length) {
			let checkCount = 0;
			const listTemp = [...list];
			switch (choiceItem.type) {
				case 0.5:
					// 半选 过滤挂起订单
					listTemp.forEach((item:any) => {
						item.isChecked = true;
						checkCount++;
					});
					setList(listTemp);
					noTradeTipFn(checkCount);
					break;
				case 2:
					// 全选
					listTemp.forEach((item:any) => {
						item.isChecked = true;
						checkCount++;
					});
					setList(listTemp);
					noTradeTipFn(checkCount);
					break;
				case 4:
					// 反选
					listTemp.forEach((item:any) => {
						item.isChecked = !item.isChecked;
					});
					setList(listTemp);
					break;
				case 6:
					// 勾选已打印
					listTemp.forEach((item:any) => {
						if (item.printStatus === "ALREADY_PRINT") {
							checkCount++;
							item.isChecked = true;
						} else {
							item.isChecked = false;
						}
					});
					noTradeTipFn(checkCount);
					setList(listTemp);
					break;
				case "changePrintStatus":
					listTemp.forEach((item:any) => {
						const _labelItem = choiceItem?.changeLabelObj[item.labelId];
						if (_labelItem) {
							item.printStatus = "ALREADY_PRINT";
							item.printTime = _labelItem.printTime;
						}
					});
					setList(listTemp);
					break;
				case "cancelLabel":
					listTemp.forEach((item:any) => {
						if (choiceItem.cancelLabels.includes(item.labelId)) {
							item.isChecked = false;
						}
					});
					setList(listTemp);
					break;
				default:
					break;
			}

		}
	};
	const onClickPrintLabel = () => {
		if (checkGoodsType == 'ARRIVAL_OF_GOODS_AND_COUNT') {
			sendPoint(Pointer.标签点货_到货点货_重新打印标签);
		}
		const selectList = _list.filter(i => i.isChecked);
		if (!selectList.length) {
			message.warn("请选择要打印的标签");
			return;

		}
		setSelectTemplateModalVisible(true);
	};

	const selectTemplateModalOnOk = debounce((values) => {
		setSelectTemplateModalVisible(false);
		const selectList = [];
		getList().forEach(item => {
			if (item.isChecked) {
				const labelInfo = item.labelInfo;
				labelInfo.id = labelInfo.labelId;
				selectList.push({
					...labelInfo,
					isStockPile: labelInfo.packageExistsStockpileLabel || (labelInfo.labelStatus == "STOCKPILE")
				});
			}
		});
		PrintCenter.batchPrintBhdXbq({
			tempId: values.template,
			printer: values.printer,
			orderList: selectList,
			takeGoodsLabelPrintSource: LabelPrintSourceEnum.标签点货,
		});
		customLogPost('打印勾选标签', { selectList: selectList?.map(item => item?.labelId) });
	}, 500, { 'leading': true, 'trailing': false });

	// 退货清单模板
	const onClickReturnListTemp = () => {
		PrintCenter.showTemplateMain({ printType: 'thqd' });
	};

	const getPrintData = () => {
		// console.log('%c [ list ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', _list);
		let data = [];

		// 打印数据记录日志
		let logData = {
			list: [],
			printList: []
		};
		getList().forEach(item => {
			if (item.isChecked) {
				const labelInfo = item.labelInfo; // 这里拿出列表数据
				labelInfo.id = labelInfo.labelId;
				data.push(labelInfo);
				logData.list.push({
					labelId: item.labelId,
					outerId: item.outerId,
					skuOuterId: item.skuOuterId,
					sysOuterId: item.sysOuterId,
					sysSkuOuterId: item.sysSkuOuterId,
					market: item.market,
					stall: item.stall,
					supplierName: item.supplierName,
				});
			}
		});

		// 整理下数据，相同numiid+skuid的合并
		const mergedData = {};
		// 遍历原始数据数组
		data.forEach(item => {
			// 生成 numIid 和 skuId 的组合键
			let key;
			if (item.numIid || item.skuId) {
				key = `${item.numIid}_${item.skuId}`;
			} else {
				key = `${item.sysItemId}_${item.sysSkuId}`;
			}

			// 如果这个键在 mergedData 中不存在，则初始化它
			if (!mergedData[key]) {
				mergedData[key] = {
					num: 1, // 初始数量设为1
					...item,
					labelIdList: [item.labelId]
				};
			} else {
				// 如果键已存在，则增加数量
				mergedData[key].num++;
				mergedData[key].labelIdList.push(item.labelId);
			}
		});
		data = Object.values(mergedData);

		// 提取相同档口
		let stallGroups = {};
		data.forEach(item => {
			const { num = 1, supplierName = '', stall = '', market = '', sysSkuCostPrice = 0 } = item;

			if (!stallGroups[stall]) {
				stallGroups[stall] = {
					market,
					stall,
					supplierName, // 供应商 一个档口对应一个供应商
					goodsNum: 0, // 总数量
					goodsPrice: 0, // 总金额
					goodsInfo: []
				};
			}
			// 增加goodsNum计数
			stallGroups[stall].goodsNum += num; // 每条数据默认数量1
			stallGroups[stall].goodsPrice += (Number(sysSkuCostPrice) * num); // 总金额
			stallGroups[stall].goodsInfo.push(item);
		});

		// 提取stall为空的条目  放前面
		const emptyStallItems = stallGroups[''] ? [stallGroups['']] : [];
		delete stallGroups[''];
		const result = emptyStallItems.concat(Object.values(stallGroups));

		logData.printList = result?.map(item => {
			return {
				...item,
				goodsInfo: item?.goodsInfo?.map(i => {
					return {
						labelIdList: i?.labelIdList,
						numIid: i?.numIid,
						skuId: i?.skuId,
					};
				})
			};
		});

		customLogPost('打印退货清单', logData);
		return result;
	};

	// 打印退货清单
	const onPrintReturnList = () => {
		const selectList = _list.filter(i => i.isChecked);
		if (!selectList.length) {
			message.error("请勾选需要打印的商品");
			return;
		}
		message.loading({
			content: '正在加载中',
			key: 'batchPrintThqdList',
			duration: 0,
		});

		try {
			let printData = getPrintData();
			// console.log('%c [ printData ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', printData, selectList);

			message.destroy('batchPrintThqdList');
			PrintCenter.batchPrintThqdList({
				orderList: printData,
				printType: 'thqd'
			});
		} catch (error) {
			message.destroy('batchPrintThqdList');
		}
	};

	useEffect(() => {
		window.addEventListener("keydown", keyDown);
	 return () => {
		 setOldIndex(-1);
		 window.removeEventListener("keydown", keyDown);
	 };
	}, []);

	// 打印当前扫码的标签
	const printThisLabel = useCallback((res, takeLabelPrinter, takeLabelTemplate) => {
		if (!res?.labelInfo) {
			return;
		}
		const labelInfo = res.labelInfo;
		// 构造单个标签的打印数据
		const printItem = {
			...labelInfo,
			id: labelInfo.labelId,
			isStockPile: labelInfo.packageExistsStockpileLabel || (labelInfo.labelStatus == "STOCKPILE")
		};

		PrintCenter.batchPrintBhdXbq({
			tempId: takeLabelTemplate,
			orderList: [printItem],
			printer: takeLabelPrinter,
			takeGoodsLabelPrintSource: LabelPrintSourceEnum.标签点货,
		});

		customLogPost('自动打印单个标签', { labelId: labelInfo.labelId });

	}, []);

	useImperativeHandle(ref, () => {
		return {
			printThisLabel
		};
	}, [printThisLabel]);


	return (
		<div className={ s['list-container'] }>
			<div style={ { overflowX: 'auto', padding: '0 12px', width: "100%", justifyContent: "space-between", minHeight: "400px" } }>
				<ListHeader listTitle={ listTitle } list={ _list } handleChoiceChange={ handleChoiceChange } />
				{_list.map((item:any, index: number) => ((
					<>
						<ListItem
							key={ `${item.labelId}-${index}` }
							pack={ item }
							index={ index }
							listTitle={ listTitle }
							dealShift={ dealShift }
						/>
					</>
				)))}
			</div>

			<div className={ s['list-bottom'] }>
				{/* 统计区域 */}
				<div className={ s['list-bottom-result'] }>
					<Icon type="tishi-fill" className="r-c-999 r-pointer r-mr-8" />
					<span className="r-mr-4">本次共扫描：<span className="r-c-error">{scanInfo.scanCount}</span></span>
					<span className="r-mr-4">扫描成功：<span className="r-c-error">{_list.length}</span></span>
					<span className="r-mr-4">扫描失败：<span className="r-c-error">{scanInfo.scanFailCount}</span></span>
					{
						checkGoodsType === "TAKE_STOCK"
							? <div className="">入库总数量：<span className="r-c-error">{scanInfo.takeStockNum}</span></div>
							: (
								<FieldsPermissionCheck
									fieldsPermission={ FieldsPermissionEnum.成本价 }
									type={ FieldsPermissionCheckTypeEnum.仅展示 }
									noPermissionRender={ <div className="">总金额：<span className="r-c-error">***</span>元</div> }
								>
									<div className="">总金额：<span className="r-c-error">{scanInfo.sysSkuCostPriceAll}</span>元</div>
								</FieldsPermissionCheck>
							)
					}
				</div>

				<div className={ s['list-bottom-btn'] }>
					<Button
						className="r-mr-16"
						style={ { width: 124 } }
						type="primary"
						onClick={ onClickPrintLabel }
						hidden={ checkGoodsType !== "ARRIVAL_OF_GOODS_AND_COUNT" }
					>打印勾选标签
					</Button>
					<Button className="r-mr-12" type="primary" onClick={ clear } data-point={ Pointer.标签点货_删除 }>删除商品</Button>
					<Button onClick={ exportConfirm }>导出本次扫描结果</Button>
					{
						checkGoodsType === 'SEND_BACK_STALL' && (
							<>
								<Button onClick={ onPrintReturnList } className="r-ml-20" data-point={ Pointer.档口_标签点货_打印退货清单按钮 }>打印退货清单</Button>
								<Button type="link" onClick={ onClickReturnListTemp } className="r-c-primary" data-point={ Pointer.档口_标签点货_退货清单模板按钮 }>退货清单模版</Button>
							</>
						)
					}
				</div>
			</div>
			<Modal
				centered
				visible={ exportScanModalVisible }
				title="导出扫描结果"
				okText="导出"
				onOk={ exportRes }
				onCancel={ () => { setExportScanModalVisible(false); } }
				// confirmLoading={ exportLoading }
				destroyOnClose
				maskClosable={ false }
			>
				<div>
					导出类型：
					<Radio.Group value={ exportType } onChange={ onChangeExportType }>
						<Radio value="STATISTIC_RESULT">统计结果</Radio>
						<Radio value="SCAN_DETAIL">扫描明细</Radio>
					</Radio.Group>
				</div>
			</Modal>
			{
				selectTemplateModalVisible && <SelectTemplateModal onOk={ selectTemplateModalOnOk } onCancel={ () => setSelectTemplateModalVisible(false) } />
			}

		</div>
	);
});

export default observer(TradeList);
