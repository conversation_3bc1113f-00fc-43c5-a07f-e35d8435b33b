
import React, { useEffect, useState, forwardRef, useImperative<PERSON><PERSON><PERSON>, useCallback } from "react";
import { observer } from "mobx-react";
import { Button, Checkbox, Form, Tooltip, Radio, RadioChangeEvent, Input, Select } from "antd";
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import { QuestionCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import message from '@/components/message';
import useGetState from '@/utils/hooks/useGetState';
import s from "./index.module.scss";
import { ItemTakeGoodsLabelCheckGoodsRequest } from "@/types/trade/takeGoodsLabel";
import userStore from "@/stores/user";
import { tradeStore } from "@/stores";
import scanPrintStore from "@/stores/trade/scanPrint";
import { getWarehouseVersion } from '@/pages/AfterSale/TradeList/utils';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { TradeDictInsertDictApi, TradeDictQueryDictApi } from '@/apis/trade/search';
import DatePicker from "@/components/DatePicker";
import PrintCenter from '@/print/index';
import { local } from '@/libs/db';


const searchInitValue = {
	checkGoodsType: "ARRIVAL_OF_GOODS_AND_COUNT",
	sacnAtferStock: false,
	showScanTime: false,
	sendBackStallTime: null,
};
interface ISearchContainerProps {
	searchFinish: (data?:{})=> void;
	onChange: (data?:{})=>void;
	scanMsg: {
		message: string,
		type: string
	};
	printMsg: {
		message: string,
		type: string
	};
	loading: boolean;
}

export const focusLabelGoodsCheckInput = () => {
	let el = document.querySelector('#labelGoodsCheckInput') as HTMLInputElement;
	el?.focus();
	el?.select();
};

const SearchContainer = observer(forwardRef((props:ISearchContainerProps, ref) => {
	const { isStockVersion } = userStore;
	const {
		lodopInstallStatus, // lodop安装状态
	} = tradeStore;
	const theTemplateList = tradeStore.bhdXbqTempList || [];
	const thePrinterList = scanPrintStore.printersList || [];

	const { searchFinish, scanMsg, loading, onChange, printMsg } = props;
	const [form] = Form.useForm();
	const [inventoryDeduct, setInventoryDeduct] = useState(false);
	const [isShowAutoStock, setIsShowAutoStock] = useState(false); // 是否显示扫描后扣库存
	const [isShowAutoPrintLabel, setIsShowAutoPrintLabel] = useState(false); // 是否显示扫描后重打标签
	const [checkAutoPrintLabel, setCheckAutoPrintLabel] = useState(local.getBySubUserId('labelGoodsCheck_autoPrintLabel') || false); // 是否显示重打标签模板 (扫描后重打标签勾选状态)

	const [printerList, setPrinterList, getPrinterList] = useGetState([]); // 小标签模板
	const [templateList, setTemplateList, getTemplateList] = useGetState([]); // 打印机
	const [setting, setSetting]: any = useState({});
	const [scanTime, setScanTime]:any = useState('');

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `标签点货: 【 ${dataType} 】`,
			data: {
				...data
			}
		});
	};


	const initFormData = () => {
		const initCheckGoodsType = localStorage.getItem('Label_CheckedGoodsType');
		if (initCheckGoodsType) {
			form.setFieldsValue({
				checkGoodsType: initCheckGoodsType
			});

			setIsShowAutoStock(initCheckGoodsType === 'SEND_BACK_STALL');
			setIsShowAutoPrintLabel(initCheckGoodsType === 'ARRIVAL_OF_GOODS_AND_COUNT');
		}
	};

	useEffect(() => {
		getWarehouseVersion().then(res => {
			setInventoryDeduct(res);
		});
	}, []);

	useEffect(() => {
		setTimeout(() => {
			initFormData();
		}, 0);
	}, []);

	useEffect(() => {
		console.log('%c [ printerList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', thePrinterList);
		setPrinterList(thePrinterList);
	}, [thePrinterList]);

	useEffect(() => {
		const templateList = theTemplateList?.filter(i => i.ExCode === "BHD");
		console.log('%c [ templateList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', templateList);
		setTemplateList(templateList);
	}, [theTemplateList]);

	const getConfig = async() => {
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: 'LABEL_SET' });
			const data = JSON.parse(res?.value);
			setSetting(data);
			form.setFieldsValue({
				sacnAtferStock: data?.sacnAtferStock == 1, // 1打开 2关闭
				showScanTime: data?.showScanTime == 1, // 1打开 2关闭
				sendBackStallTime: data?.showScanTime == 1 ? dayjs() : '',
			});
		} catch (error) {
			console.log(error, 'error');
		}
	};

	useEffect(() => {
		if (isShowAutoStock)getConfig();
	}, [isShowAutoStock, isStockVersion]);


	// 表单变更
	const handleChange = async() => {
		try {
			const res = await form.validateFields();
			setIsShowAutoStock(res.checkGoodsType === 'SEND_BACK_STALL');
			setIsShowAutoPrintLabel(res.checkGoodsType === 'ARRIVAL_OF_GOODS_AND_COUNT');
			onChange?.({
				...res
			});
		} catch (error) {
			console.log(error);
		}
	};

	// 扫描后扣库存
	const onChangeCheckbox = (e: CheckboxChangeEvent, key) => {
		console.log('checked = ', e.target.checked);
		TradeDictInsertDictApi({
			userDictEnum: 'LABEL_SET',
			value: JSON.stringify({
				...setting,
				[key]: e.target.checked ? 1 : 2
			})
		}).then(() => {
			setSetting(pre => {
				return { ...pre, [key]: e.target.checked ? 1 : 2 };
			});
		});
	};

	const initAutoPrintSetting = () => {
		const autoPrintLabelVal = local.getBySubUserId('labelGoodsCheck_autoPrintLabel');
		const defaultPrinter = local.getBySubUserId('labelGoodsCheck_autoPrintLabel_printer');
		const defaultTemplate = local.getBySubUserId('labelGoodsCheck_autoPrintLabel_template');

		const printerList = getPrinterList();
		const templateList = getTemplateList().map(i => i.Mode_ListShowId);

		console.log('%c [ 默认打印机和模板 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', defaultPrinter, defaultTemplate);
		form.setFieldsValue({
			autoPrintLabel: autoPrintLabelVal,
			printer: defaultPrinter && printerList.includes(defaultPrinter) ? defaultPrinter : undefined,
			template: defaultTemplate && templateList.includes(defaultTemplate) ? defaultTemplate : undefined
		});
	};

	useEffect(() => {
		// 如果显示扫描后重打标签（到货点货）
		if (isShowAutoPrintLabel) {
			initAutoPrintSetting();
		}
	}, [isShowAutoPrintLabel, printerList, templateList]);

	// 扫描后重打标签
	const onChangePrintLabel = async(value, key) => {
		console.log('%c [ onChangePrintLabel ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value, key);

		if (key == 'autoPrintLabel') {
			// 勾选时检查lodop控件状态
			if (value) {
				let installResult = await PrintCenter.checkLodopProgress(false);
				console.log('%c [ clodop ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', installResult);
				if (!installResult?.isSuccess) {
					message.error('请先安装并开启C-LODOP控件');
					form.setFieldsValue({ autoPrintLabel: false });
					return;
				}
				// 勾选时打点
				sendPoint(Pointer.标签点货_勾选扫描后重打标签);
			}

			setCheckAutoPrintLabel(value);
			local.setBySubUserId('labelGoodsCheck_autoPrintLabel', value); // 缓存勾选状态
			customLogPost('勾选扫描后重打标签', { value });
		} else {
			local.setBySubUserId(`labelGoodsCheck_autoPrintLabel_${key}`, value); // 缓存打印机/模板
		}
	};

	const handleFinish = (v:ItemTakeGoodsLabelCheckGoodsRequest) => {
		console.log('handleFinish:', v);
		sendPoint(Pointer.标签点货_点击查询);
		if (!v.labelId) {
			message.error('请输入唯一码');
			return;
		}
		document.querySelector('#labelGoodsCheckInput')?.blur();
		searchFinish && searchFinish(v);
	};

	// 点货操作方式变更
	const changeForm = (e:RadioChangeEvent) => {
		const { value } = e.target;
		window.localStorage.setItem(`Label_CheckedGoodsType`, value);
		form.setFieldsValue({ labelId: "" });
		const points = {
			ARRIVAL_OF_GOODS_AND_COUNT: Pointer.标签点货_点击到货点货,
			TAKE_STOCK: Pointer.标签点货_点击盘点入库,
			SEND_BACK_STALL: Pointer.标签点货_点击退回档口,
		};
		sendPoint(points[value]);
	};

	// console.log('ssssss', setting);
	// console.log('scanTime', scanTime);

	// 自动重打检查打印机和模板
	const checkAllowAutoPrintLabel = useCallback(() => {
		let obj = {
			hasPrinter: false,
			hasTemplate: false,
		};
		// 已勾选重打
		if (isShowAutoPrintLabel && checkAutoPrintLabel) {
			const values = form.getFieldsValue();
			obj = {
				hasPrinter: values?.printer,
				hasTemplate: values?.template,
			};
		}
		return obj;
	}, [isShowAutoPrintLabel, checkAutoPrintLabel]);

	useImperativeHandle(ref, () => {
		return {
			checkAllowAutoPrintLabel,
			autoPrintLabel: checkAutoPrintLabel
		};
	}, [checkAllowAutoPrintLabel, checkAutoPrintLabel]);

	return (
		<div className={ s["search-container"] }>
			<Form
				onFinish={ handleFinish }
				onChange={ handleChange }
				initialValues={ searchInitValue }
				form={ form }
				size="small"
			>
				<Form.Item label="唯一码" name="labelId" className="r-mb-16 r-mt-10">
					<Input id="labelGoodsCheckInput" autoFocus placeholder="请输入唯一码" />
				</Form.Item>
				<Form.Item label="点货操作" name="checkGoodsType" style={ { marginBottom: 0 } }>
					<Radio.Group onChange={ changeForm }>
						<Radio value="ARRIVAL_OF_GOODS_AND_COUNT" className="r-mb-12">
							到货点货
							<Tooltip placement="right" className="r-ml-5" title="用于档口拿货或仓库拣货的后的商品标签点货，扫描后表示商品已经到货或完成仓库拣货"><QuestionCircleOutlined /></Tooltip>
						</Radio>
						{
							isShowAutoPrintLabel && (
								<Form.Item
									label=""
									colon={ false }
									name="autoPrintLabel"
									valuePropName="checked"
									style={ { marginLeft: 8, marginBottom: 12 } }
								>
									<Checkbox onChange={ e => {
										onChangePrintLabel(e.target.checked, 'autoPrintLabel');
									} }
									>
										扫描后重打标签
										<Tooltip
											placement="right"
											className="r-ml-5"
											title={ (
												<div>
													<p>1. 开启后，所有扫描成功均会自动打印标签</p>
													<p>2. 标签将按指定模板重新打印</p>
												</div>
											) }
											overlayStyle={ { maxWidth: 500 } }
										>
											<QuestionCircleOutlined />
										</Tooltip>
									</Checkbox>
								</Form.Item>
							)
						}
						{
							isShowAutoPrintLabel && checkAutoPrintLabel && (
								<>
									<Form.Item
										label=""
										colon={ false }
										name="printer"
										rules={ [{ required: false, message: '请选择打印机' }] }
										style={ { marginLeft: 8, marginBottom: 12 } }
									>
										<Select
											placeholder="请选择打印机"
											onChange={ e => {
												onChangePrintLabel(e, 'printer');
											} }
											allowClear
										>
											{printerList?.map(printer => (
												<Select.Option key={ printer } value={ `${printer}` }>{printer}</Select.Option>
											))}
										</Select>
									</Form.Item>
									<Form.Item
										label=""
										colon={ false }
										name="template"
										rules={ [{ required: false, message: '请选择模板' }] }
										style={ { marginLeft: 8, marginBottom: 12 } }
									>
										<Select
											placeholder="请选择退货标签模板"
											onChange={ e => {
												onChangePrintLabel(e, 'template');
											} }
											allowClear
										>
											{templateList?.map(item => (
												<Select.Option value={ item.Mode_ListShowId } key={ item.Mode_ListShowId } >{item.ExcodeName}</Select.Option>
											))}
										</Select>
									</Form.Item>
								</>
							)
						}
						{
							isStockVersion
								? (
									<Radio value="TAKE_STOCK" className="r-mb-12">
										盘点入库
										<Tooltip placement="right" className="r-ml-5" title="当商品因买家退货或屯货时，可扫描标签完成商品入库"><QuestionCircleOutlined /></Tooltip>
									</Radio>
								) : ""
						}
						<Radio value="SEND_BACK_STALL" className="r-mb-12" >
							退回档口
							<Tooltip
								placement="right"
								className="r-ml-5"
								title={ (
									<div>
										<p>1. 支持商品标签/退货标签扫描</p>
										<p>2. 当商品无法发货需要退回档口时，可扫描标签完成商品退回档口登记</p>

									</div>
								) }
							>
								<QuestionCircleOutlined />
							</Tooltip>
						</Radio>
					</Radio.Group>
				</Form.Item>
				{
					isShowAutoStock && (
						<>
							{isStockVersion ? (
								<Form.Item
									label=""
									colon={ false }
									name="sacnAtferStock"
									// wrapperCol={ { offset: 10 } }
									valuePropName="checked"
									style={ { marginLeft: 78, marginBottom: 0 } }
								>
									<Checkbox onChange={ e => onChangeCheckbox(e, 'sacnAtferStock') }>
										扫描后扣库存
										<Tooltip
											placement="right"
											className="r-ml-5"
											title={ (
												<div>
													<p>仅支持售后退货标签</p>
												</div>
											) }
										>
											<QuestionCircleOutlined />
										</Tooltip>
									</Checkbox>
								</Form.Item>
							) : null}
							<Form.Item
								label=""
								colon={ false }
								name="showScanTime"
								valuePropName="checked"
								style={ { marginLeft: 78, marginBottom: 0 } }
							>
								<Checkbox onChange={ e => {
									onChangeCheckbox(e, 'showScanTime');
									if (e.target.checked) {
										setScanTime(dayjs());
										form.setFieldsValue({ sendBackStallTime: dayjs() });
									}
								} }
								>
									退回档口时间
									<Tooltip
										placement="right"
										className="r-ml-5"
										title={ (
											<div>
												<p>1. 支持自定义退回档口时间，影响对账计数</p>
												<p>2. 如不设置，则默认为等于退回档口扫描时间</p>
											</div>
										) }
									>
										<QuestionCircleOutlined />
									</Tooltip>
								</Checkbox>
							</Form.Item>
							{
								setting?.showScanTime == 1
									?									(
										<div className="r-mt-8">
											<Form.Item
												label=""
												colon={ false }
												name="sendBackStallTime"
												style={ { marginLeft: 78 } }
											>
												<DatePicker
													allowClear={ false }
													placeholder=""
													style={ { width: 196 } }
													showTime={ { format: 'HH:mm:ss' } }
													format="YYYY-MM-DD HH:mm:ss"
													size="small"
													// !! 当前版本获取不到面板点击日期回调暂不支持
													// disabledHours={ () => {
													// 	const isToday = dayjs(form.getFieldValue('sendBackStallTime')).isSame(dayjs(), 'day');
													// 	console.log('xxxxxx', isToday);
													// 	return [12];
													// } }
													disabledDate={ (current) => {
														if (current > dayjs().startOf('day')) {
															return false;
														} else {
															return true;
														}
													} }
												/>
											</Form.Item>
										</div>
									)
									: null
							}
						</>
					)
				}
				<Form.Item label=" " colon={ false } style={ { textAlign: "center", marginTop: 20 } }>
					<Button type="primary" htmlType="submit" loading={ loading }>查询</Button>
				</Form.Item>
			</Form>

			<div className={ scanMsg?.type === "fail" ? "r-c-error" : "r-c-success" }>{scanMsg?.message || ""}</div>

			<div className={ printMsg?.type === "fail" ? "r-c-error" : "r-c-success" } style={ { marginTop: 10 } }>{printMsg?.message || ""}</div>
		</div>
	);
}));
export default SearchContainer;
