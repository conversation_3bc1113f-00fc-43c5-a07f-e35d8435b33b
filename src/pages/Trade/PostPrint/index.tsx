/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-08-11 16:29:32
 * @Description:
 */
import React, { useEffect } from "react";
import { observer } from "mobx-react";
import { Spin } from "antd";
import Search from "./components/Search";
import styles from "./index.module.scss";
import CardContent from "./components/CardContent";
import TradeDetailCard from "./components/TradeDetailCard";
import AlertMsg from "./components/AlertMsg";
import { tradeStore } from "@/stores";
import LineAudio from "./components/LineAudio";
import ContentSetting from "./components/ContentSetting";

const PostPrint = () => {
	const { postPrintStore: {
		isLoading,
		tip,
		resetStore,
		getSettingConfig,
		settingConfig,
		postPrintList,
		getContentSettingConfig,
		contentSettingModalVisible,
	} } = tradeStore;

	useEffect(() => {
		getSettingConfig();
		getContentSettingConfig();
		return () => {
			resetStore();
		};
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const getTradeDetailCard = () => {
		const tradeDetailList = [];
		postPrintList[0]?.trades.forEach(trade => {
			tradeDetailList.push(
				<div key={ trade.tid } style={ { marginBottom: '16px' } }>
					<TradeDetailCard pack={ postPrintList[0] } trade={ trade } />
				</div>
			);
		});
		return tradeDetailList;
	};

	return (
		<Spin spinning={ isLoading } tip={ tip }>
			{contentSettingModalVisible && <ContentSetting />}
			<div className={ styles['main-container'] }>
				<div>
					<Search />
				</div>
				{
					settingConfig.scanType !== "PT_TID" && <CardContent />
				}
				<div className="r-flex-1">
					<div className={ styles["alert-container"] }>
						<AlertMsg />
					</div>
					<div className={ styles["table-container"] }>
						{getTradeDetailCard()}
					</div>
					<div className={ styles["audio-container"] }>
						<LineAudio />
					</div>
				</div>
			</div>
		</Spin>

	);
};
export default observer(PostPrint);
