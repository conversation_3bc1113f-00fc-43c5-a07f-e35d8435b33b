import React, { useEffect, useRef, useState } from "react";
import { observer } from "mobx-react";
import { Select } from "antd";
import { template } from "lodash";
import { tradeStore } from "@/stores";

const SelectTagTemplate = (props) => {
	const {
		dpTempList,
	} = tradeStore;
	const { value, onChange, disabled } = props;
	const popupContainerRef = useRef();
	const [_value, _setValue] = useState<any>(value);
	const { Option } = Select;
	const getTemplateList = () => {
		const templateList = [];
		dpTempList?.forEach(i => {
			templateList.push(
				<Option key={ i.Mode_ListShowId } value={ i.Mode_ListShowId }>
					<span>{i.ExcodeName}</span>
				</Option>
			);
		});
		return templateList;
	};

	const _onChange = (v) => {
		const templateInfo = dpTempList.find(template => template.id == v || template.Mode_ListShowId == v);
		onChange && onChange(v, templateInfo);
		_setValue(v);
	};

	useEffect(() => {
		// 当外部的form设置sender的值时触发
		if (value) {
			const templateInfo = dpTempList.find(template => template.id == value || template.Mode_ListShowId == value);
			// 只有模板存在，才做回显
			if (templateInfo) {
				_setValue(value);
			}
		}
	}, [value, dpTempList]);

	return (
		<div ref={ popupContainerRef }>
			<Select
				placeholder="请选择快递模板"
				onChange={ _onChange }
				value={ _value }
				getPopupContainer={ () => popupContainerRef.current as HTMLDivElement }
				disabled={ disabled }
				style={ { width: '158px' } }
			>
				{getTemplateList()}
			</Select>
		</div>
		
	);
};

export default observer(SelectTagTemplate);
