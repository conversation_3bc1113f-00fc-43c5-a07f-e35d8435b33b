
import React from "react";
import userStore from "@/stores/user";
import { PrintExpressBill } from "@/print/postPrint";
import { TRADE_SEND_STATUS } from "./constants";


export const focusPostPrintLabelInput = () => {
	let el = document.querySelector('#labelIdPostPrintInput') as HTMLInputElement;
	if (el) {
		el.disabled = false;
	}
};

export const disabledPostPrintLabelInput = () => {
	let el = document.querySelector('#labelIdPostPrintInput') as HTMLInputElement;
	if (el) {
		el.disabled = true;
	}
};

export const PrintRefundLabelExpressBill = async(params) => {
	const { packList, senderId, printer, tagPrinter, templateInfo, tagTempId, isMergePrint, isForcePrint, historySids, firstSendMode, logReprint, isMustUseOldCode } = params;
	const senderInfo = (userStore?.senderSetting?.list || []).find(item => item.id == senderId);
	console.log(params);
	const sender = {
		[packList?.[0]?.sellerId]: {
			info: {
				id: senderInfo.id,
				city: senderInfo.senderCity,
				district: senderInfo.senderDistrict,
				address: senderInfo.senderAddressDetail,
				name: senderInfo.senderName,
				phone: senderInfo.senderMobile || senderInfo.senderPhone,
				postCode: senderInfo.senderPostCode,
				province: senderInfo.senderProvince,
				sign: senderInfo.senderSign
			},
			taobaoNick: packList?.[0]?.sellerNick
		}
	};
	PrintExpressBill({
		senderInfo: {
			isUseMore: false,
			isUseCommon: true,
			sender,
		},
		orderList: packList,
		templateInfo,
		tagTempId,
		printer,
		tagPrinter,
		isMergePrint,
		isForcePrint,
		historySids,
		firstSendMode,
		isMustUseOldCode,
		logReprint,
	});
};
// 键值不同，兼容一下
export const PRODUCT_MAP = {
	skuOuterId: 'outerSkuId',
	sysSkuOuterId: 'sysOuterSkuId',
};

export const getUniqueKeyByOrderInfo = (orderInfo) => {
	// if (orderInfo.isGroup) {
	// 	return `${orderInfo?.orderInfo?.oid}@${orderInfo.sysItemId}@${orderInfo.sysSkuId}`;
	// }
	return `${orderInfo.tid}@${orderInfo.numIid}@${orderInfo.oid}`;
};

export const getTradeStatusText = (status) => {
	let color = "";
	switch (TRADE_SEND_STATUS[status]) {
		case TRADE_SEND_STATUS.SELLER_CONSIGNED_PART:
			color = "#FD8204";
			break;
		case TRADE_SEND_STATUS.WAIT_BUYER_CONFIRM_GOODS:
			color = "#f00";
			break;
		case TRADE_SEND_STATUS.WAIT_SELLER_SEND_GOODS:
			color = "#4caf50";
			break;
		default: break;
	}
	return <span className="r-bold" style={ { color } }>{TRADE_SEND_STATUS[status]}</span>;
};

// 当前订单是否允许验货，只有待发货和没有售后状态，才允许验货

export const orderAllowInspect = (order) => {
	let orderInfo = order;
	if (order.isGroup) {
		orderInfo = order.orderInfo;
	}
	const noLink = orderInfo.noGoodsLink;
	const waitSendStatus = ['WAIT_SELLER_SEND_GOODS'].includes(orderInfo.status);
	const noAftersaleStatus = ['NOT_REFUND'].includes(orderInfo.refundStatus);
	const allow = !noLink && waitSendStatus && noAftersaleStatus;
	return allow;
};