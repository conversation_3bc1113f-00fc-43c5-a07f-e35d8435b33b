import React, { useEffect, useState } from "react";
import * as echarts from "echarts";
import { Statistic } from "antd";
import dayjs from "dayjs";
import { SyncOutlined } from "@ant-design/icons";
import { Loading, BorderBox8, Decoration1 } from "@jiaminghi/data-view-react";
import { IndexSettingGetSenderSettingApi, logisticsInfoGetApi } from "../utils/api";
import "../index.css";
import s from "./index.module.scss";
import Title from "../SaleData/components/Title";
import sendPoint from "../utils/pointTrack/sendPoint";
import Pointer from "../utils/pointTrack/constants";
import { MapOriginVal, chinaGeoCoordMap, removeSuffix, updateTimerPerSecond } from "../utils/util";
import china from "../SaleData/china.json";

const myChart2Basic = {
	title: {
		show: true,
		text: "当月快递效率",
		x: "center",
		textStyle: {
			// 主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
			fontSize: 14,
			fontStyle: "normal",
			fontWeight: "normal",
			color: "#01c4f7",
		},
	},
	tooltip: {
		trigger: "axis",
		axisPointer: {
			type: "shadow",
		},
	},
	legend: {
		data: ["待揽收包裹数", "在途包裹数", "派件包裹数"],
		textStyle: {
			fontSize: 12,
			color: "#ffffff",
		},
		top: 20,
		itemWidth: 20, // 设置宽度

		itemHeight: 12, // 设置高度

		itemGap: 10, // 设置间距
	},
	grid: {
		left: "3%",
		right: "4%",
		bottom: "3%",
		containLabel: true,
	},
	xAxis: {
		type: "category",
		data: [],
		splitLine: {
			show: true,
			lineStyle: {
				color: ["#07234d"],
			},
		},
		axisLabel: {
			show: true,
			textStyle: {
				color: "#c3dbff", // 更改坐标轴文字颜色
				fontSize: 12, // 更改坐标轴文字大小
			},
		},
	},
	yAxis: {
		type: "value",
		boundaryGap: [0, 0.01],
		splitLine: {
			show: true,
			lineStyle: {
				color: ["#07234d"],
			},
		},
		axisLabel: {
			show: true,
			textStyle: {
				color: "#c3dbff", // 更改坐标轴文字颜色
				fontSize: 12, // 更改坐标轴文字大小
			},
		},
	},
	series: [
		{
			name: "待揽收包裹数",
			type: "bar",
			data: [],
			itemStyle: {
				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					{ offset: 0, color: "#546BFF" },
					{ offset: 1, color: "rgba(0,240,255,0.00)" }
				]),
			},
		},
		{
			name: "在途包裹数",
			type: "bar",
			data: [],
			itemStyle: {
				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					{ offset: 0, color: "#54A0FF" },
					{ offset: 1, color: "rgba(64,179,255,0.00)" }
				]),
			},
		},
		{
			name: "派件包裹数",
			type: "bar",
			data: [],
			itemStyle: {
				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					{ offset: 0, color: "#34DCFE" },
					{ offset: 1, color: "rgba(64,179,255,0.00)" }
				]),
			},
		}
	],
};

const myChart1Basic = {
	tooltip: {
		trigger: "item",
	},

	visualMap: {
		min: 0,
		left: "20%",
		max: 500,
		text: ["高", "低"], // 文本，默认为数值文本
		splitNumber: 0,
		color: ["#0054bb", "#85ADDE"],
		textStyle: {
			color: "#c3dbff",
		},
	},
	geo: {
		map: 'china',
		zoom: 1.1,
		label: {
			show: true,
			textStyle: {
				color: '#FFFFFF',
				fontWeight: "bold",
			}
		},
		roam: false, // 是否允许缩放
		itemStyle: {
		  normal: {
				color: '#1a1e45',
				borderColor: 'rgba(0, 0, 0, 0.2)', // 省市边界线00fcff 516a89
				borderWidth: 0.2,
				textStyle: '#fff',
		  },
		  emphasis: {
				color: '#22ccfb', // 悬浮背景
		  },
		},
		data: MapOriginVal,
	},

	series: [
		{
			name: "今日发货包裹",
			type: "map",
			mapType: "china",
			geoIndex: 0,
			mapLocation: {
				x: "left",
			},
			// selectedMode: 'multiple',
			itemStyle: {
				normal: {
					label: { show: true, color: "#fff" },
					borderWidth: 0,
				},
			},
			data: MapOriginVal
		}
	],
};

let timer = null;
let currentTimerInterval = null;
let echart1 = null;
let echart2 = null;
let visibilityChangeHandler = null;

const SaleData = (props) => {
	const { userInfo, versionControl } = props;
	const [saleInfoData, setSaleInfoData] = useState(null);
	const [isGettingInfo, setIsGettingInfo] = useState(false);
	const [loading, setLoading] = useState(false);
	const [currentTimer, setCurrentTimer] = useState("");
	// 发件人省份
	const [senderProvince, setSenderProvince] = useState("");

	useEffect(() => {
		sendPoint(Pointer.物流实时大屏);
		echarts.registerMap("china", china as any);
		setLoading(true);
		initialECharts();
		initialEChartsMyChart2();
		saleInfoGet();
		refreshData();
		return () => {
			clearTimeout(timer);
			clearInterval(currentTimerInterval);
			if (visibilityChangeHandler) {
				document.removeEventListener('visibilitychange', visibilityChangeHandler);
				visibilityChangeHandler = null;
			}
		};
	}, []);

	useEffect(() => {
		if (userInfo?.userId) {
			getSenderSetting();
		}
	}, [userInfo]);

	useEffect(() => {
		if (versionControl) clearTimeout(timer);
	}, [versionControl]);

	useEffect(() => {
		if (!saleInfoData || !echart1 || !echart2) return;
		// 包裹地域排名
		if (saleInfoData?.todayBuyerAreaRankList.length > 0) {
			const mapArr = [];
			const hasValueCityObj = {};
			const flyData = [];
			saleInfoData?.todayBuyerAreaRankList?.forEach((item, index) => {
				// const _val = (item.rate * 100).toFixed();
				const _val = item.packageSize;
				const _name = removeSuffix(item.areaName);
				mapArr.push({
					name: _name,
					areaName: item.areaName,
					value: _val
				});
				if (index < 7) {
					flyData.push([{
						name: _name,
						value: _val
					}]);
				}
				hasValueCityObj[item.name] = item;
			});
			MapOriginVal.forEach(item => {
				if (!hasValueCityObj[item.name]) {
					mapArr.push(item);
				}
			});
			myChart1Basic.series = drawAirLine({ flyData, mapArr, senderProvince });

			myChart1Basic.geo.data = mapArr;
			echart1?.setOption(myChart1Basic);
		} else {
			echart1?.setOption(myChart1Basic);
		}

		let pendingCollectionNumList = [];
		let transitNumList = [];
		let dispatchNumList = [];
		let waybillNameList = [];

		saleInfoData.monthExpressDeliveryEfficiencyList.forEach((item) => {
			const {
				pendingCollectionNum,
				transitNum,
				dispatchNum,
				waybillName,
			} = item;
			pendingCollectionNumList.push(pendingCollectionNum);
			transitNumList.push(transitNum);
			dispatchNumList.push(dispatchNum);
			waybillNameList.push(waybillName);
		});
		myChart2Basic.xAxis.data = waybillNameList;
		myChart2Basic.series[0].data = pendingCollectionNumList;
		myChart2Basic.series[1].data = transitNumList;
		myChart2Basic.series[2].data = dispatchNumList;

		echart2?.setOption(myChart2Basic);
	}, [saleInfoData, echart1, echart2, senderProvince]);

	const getSenderSetting = async() => {
		const { userId } = userInfo;
		const res = await IndexSettingGetSenderSettingApi({ pageNo: 1, pageSize: 1, userId });
		if (res.list.length > 0) {
			const senderProvince = res.list[0].senderProvince;
			setSenderProvince(senderProvince);
		}
	};

	const drawAirLine = ({ flyData, mapArr, senderProvince }) => {
		const numSeries = {
			name: "今日发货包裹",
			type: "map",
			mapType: "china",
			geoIndex: 0,
			mapLocation: {
				x: "left",
			},
			itemStyle: {
				normal: {
					label: { show: true, color: "#fff" },
					borderWidth: 0,
				},
			},
			tooltip: {
				formatter: "包裹数量：{c} ",
			},
			data: mapArr,
		};
		if (!senderProvince) {
			return [numSeries];
		}

		let _senderProvince = removeSuffix(senderProvince);
		let formdata = _senderProvince; // 中心点

		let convertData = function(data) {
		  let res = [];
		  for (let i = 0; i < data.length; i++) {
				let dataItem = data[i];
				let fromCoord = chinaGeoCoordMap[dataItem[0].name];
				let toCoord = chinaGeoCoordMap[_senderProvince];
				if (fromCoord && toCoord) {
			  res.push([
						{
				  // 飞线从哪里出发
				  coord: toCoord,
						},
						{
				  // 飞线去往哪里
				  coord: fromCoord,
				  value: dataItem[0].value,
						}
			  ]);
				}
		  }
		  return res;
		};
		let series = [];
		[[formdata, flyData]].forEach(function(item, i) {
		  series.push(
				{
			  type: 'lines',
			  coordinateSystem: 'geo',
			  zlevel: 2,
			  effect: {
						show: true,
						period: 4, // 箭头指向速度，值越小速度越快
						trailLength: 0, // 特效尾迹长度[0,1]值越大，尾迹越长重
						symbol: 'arrow', // 箭头图标
						symbolSize: 5, // 图标大小
						color: '#fcdd6e', // 图标颜色
			  },
			  lineStyle: {
						normal: {
				  show: true,
				  width: 1, // 尾迹线条宽度
				  opacity: 1, // 尾迹线条透明度
				  curveness: 0.3, // 尾迹线条曲直度
				  color: '#fcdd6e', // 飞线颜色
						},
						color: '#fcdd6e',
			  },
			  data: convertData(item[1]),
				},
				{
			  type: 'effectScatter',
			  coordinateSystem: 'geo',
			  zlevel: 2,
			  rippleEffect: {
						// 涟漪特效
						period: 4, // 动画时间，值越小速度越快
						brushType: 'stroke', // 波纹绘制方式 stroke, fill
						scale: 3, // 波纹圆环最大限制，值越大波纹越大
						color: '#fcdd6e',
			  },
			  label: {
						normal: {
				  show: false,
				  position: 'right', // 显示位置
				  offset: [5, 0], // 偏移设置
				  formatter(params) {
								// 圆环显示文字
								return params.data.name;
				  },
				  fontSize: 13,
						},
						emphasis: {
				  show: false,
						},
			  },
			  symbol: 'circle',
			  symbolSize(val) {
						return 5; // 圆环大小
			  },
			  itemStyle: {
						normal: {
				  show: false,
				  color: '#fce182',
						},
			  },
			  data: item[1].map((dataItem) => {
						return {
				  name: dataItem[0].name,
				  value: chinaGeoCoordMap[dataItem[0].name].concat([dataItem[0].value]),
						};
			  }),
				},
				// 中心点
				{
			  type: 'effectScatter',
			  coordinateSystem: 'geo',
			  zlevel: 15,
			  rippleEffect: {
						period: 4,
						brushType: 'stroke',
						scale: 4,
						color: '#38ff85',
			  },
			  label: {
						normal: {
				  show: false,
				  position: 'right',
				  // offset:[5, 0],
				  color: '#38ff85',
				  formatter: '{b}',
				  textStyle: {
								color: '#38ff85',
				  },
						},
						emphasis: {
				  show: false,
				  color: '#38ff85',
						},
			  },
			  symbol: 'circle',
			  symbolSize: 5,
			  itemStyle: {
						color: '#38ff85',
			  },
			  data: [
						{
				  name: item[0],
				  value: chinaGeoCoordMap[item[0]].concat([10]),
						}
			  ],
				},
				numSeries
		  );
		});
		return series;
	};

	const initialEChartsMyChart2 = () => {
		echart2 = echarts.init(document.getElementById("statistic_mainMap3"));
		echart2?.setOption(myChart2Basic);
	};

	const initialECharts = () => {
		// echarts.registerMap("zhongguo", geoJson);
		echart1 = echarts.init(document.getElementById("mainMap"));
		echart1?.setOption(myChart1Basic);
	};

	useEffect(() => {
		window.onresize = () => {
			echart1.resize();
			echart2.resize();
		};
	}, []);

	const setTimer = (date = "") => {
		setCurrentTimer((prev) => {
			let _prev = updateTimerPerSecond(date || prev);
			return dayjs(_prev).format("YYYY-MM-DD HH:mm:ss");
		});
	};
	const dealCurrentTimer = (date) => {
		setTimer(date);
		clearInterval(currentTimerInterval);
		currentTimerInterval = setInterval(() => {
			setTimer();
		}, 1000);
	};

	const refreshData = () => {
		if (timer) clearTimeout(timer);

		if (document.visibilityState === 'visible') {
			timer = setTimeout(async() => {
				const { isLogTimeOut } = props;
				await saleInfoGet();
				if (!isLogTimeOut) refreshData();
			}, 15000);
		} else if (document.visibilityState === 'hidden') {
			// 页面不可见时，添加监听器等待页面变为可见
			if (!visibilityChangeHandler) {
				visibilityChangeHandler = () => {
					if (document.visibilityState === 'visible') {
						document.removeEventListener('visibilitychange', visibilityChangeHandler);
						visibilityChangeHandler = null;
						refreshData(); // 页面重新可见时重新启动定时器
					}
				};
				document.addEventListener('visibilitychange', visibilityChangeHandler);
			}
		}
	};

	const saleInfoGet = (forceUpdate = false) => {
		if (isGettingInfo) return;
		setIsGettingInfo(true);
		if (sessionStorage.getItem("logisticsForceUpdate") !== "true") {
			forceUpdate = true;
			sessionStorage.setItem("logisticsForceUpdate", "true");
		}
		if (forceUpdate) sendPoint(Pointer.手动更新数据);
		logisticsInfoGetApi({ forceUpdate }).then((res) => {
			if (res) {
				dealCurrentTimer(res.date || new Date());
				const { todayTradeSendPackageNum = 0, modified = "" } = res;
				res.modified = dayjs(modified).format("YYYY-MM-DD HH:mm");
				res.todaySaleArr = String(todayTradeSendPackageNum).padStart(7, "0").split("");
				if (res.monthSendInfo) {
					const { todaySendRate = 0, hour24SendRate = 0, hour48SendRate = 0 } = res.monthSendInfo;
					let monthSendInfo = {
						todaySendRate: (Number(todaySendRate) * 100).toFixed() + "%",
						hour24SendRate: (Number(hour24SendRate) * 100).toFixed() + "%",
						hour48SendRate: (Number(hour48SendRate) * 100).toFixed() + "%",
					};
					res.monthSendInfo = monthSendInfo;
				}
				setSaleInfoData(res);
			}
		}).finally(() => {
			setIsGettingInfo(false);
			setLoading(false);
			return Promise.resolve();
		});
	};

	return (
		<div className={ `wrapper ${s.largeDataContainer}` }>
			{loading ? (
				<Loading>
					<p style={ { color: "white", marginTop: "12px" } }>
						正在计算所有店铺最新数据，请耐心等待...
					</p>
				</Loading>
			) : (
				""
			)}
			<div className="container-h70">
				<div
					className="row fill-h"
					style={ { display: "flex", height: "100%" } }
				>
					<div className="col-lg-3 fill-h" style={ { width: "25%" } }>
						<div className={ s.dataUpdateTime }>
							数据更新于：{saleInfoData?.modified}
							<SyncOutlined spin={ isGettingInfo } style={ { marginLeft: 8 } } onClick={ () => { saleInfoGet(true); } } />
						</div>
						<Title
							title="当月发货数据"
							tip="当月发货数据均包含已售后的订单"
							largeStyle={ { background: "rgba(7,16,44, 1)" } }
						/>
						<div className="xpanel-wrapper">
							<div className={ s.orderStatus }>
								<Statistic
									className={ s.statistic }
									title="当日发货率"
									value={ saleInfoData?.monthSendInfo.todaySendRate }
								/>
								<Statistic
									className={ s.statistic }
									title="24小时发货率"
									value={ saleInfoData?.monthSendInfo.hour24SendRate }
								/>
								<Statistic
									className={ s.statistic }
									title="48小时发货率"
									value={ saleInfoData?.monthSendInfo.hour48SendRate }
								/>
							</div>
						</div>
					</div>

					<div
						className="col-lg-6 fill-h"
						style={ {
							width: "50%",
							display: "flex",
							justifyContent: "space-between",
							flexDirection: "column",
						} }
					>
						<div
							className="xpanel-wrapper"
							style={ { height: "86%", minHeight: "86%" } }
						>
							<div
								className="xpanel"
								style={ {
									position: "relative",
								} }
							>
								<div className="map_bg" />
								<div className="circle_allow" />
								<div className="circle_bg" />

								<p className={ s.saleDataTitle }>今日发货包裹</p>
								<div className={ s.dataContainer }>
									{saleInfoData?.todaySaleArr?.map((item) => {
										return (
											<div className="databg">
												{item}
											</div>
										);
									})}
								</div>
								<div
									style={ {
										height: 40,
										width: 160,
										position: "absolute",
										top: 20,
										right: 20,
									} }
								>
									<Decoration1
										style={ {
											width: "100%",
											height: "100%",
										} }
									/>
								</div>

								<div
									className="fill-h"
									id="mainMap"
									style={ { top: "17%", right: 0 } }
								/>
							</div>
						</div>
					</div>
					<div className="col-lg-3 fill-h" style={ { width: "25%" } }>
						<div className={ s.dataUpdateTime }>
							当前时间：{currentTimer}
						</div>
						<Title
							title="当月物流数据"
							tip="当月物流数据均取自物流公司的轨迹反馈，仅供参考"
							largeStyle={ { background: "rgba(7,16,44, 1)" } }
						/>

						<div className="xpanel-wrapper">
							<div className={ s.orderStatus }>
								<Statistic
									className={ s.statistic }
									title="待揽收包裹数"
									value={
										saleInfoData?.monthLogisticsInfo
											.pendingCollectionNum
									}
								/>
								<Statistic
									className={ s.statistic }
									title="在途包裹数"
									value={
										saleInfoData?.monthLogisticsInfo
											.transitNum
									}
								/>
								<Statistic
									className={ s.statistic }
									title="派件包裹数"
									value={
										saleInfoData?.monthLogisticsInfo
											.dispatchNum
									}
								/>
								<Statistic
									className={ s.statistic }
									title="已签收包裹数"
									value={
										saleInfoData?.monthLogisticsInfo.signNum
									}
								/>
								<Statistic
									className={ s.statistic }
									title="异常包裹数"
									value={
										saleInfoData?.monthLogisticsInfo
											.errorNum
									}
								/>
							</div>
						</div>
					</div>
				</div>
				<div
					className="xpanel-wrapper xpanel-wrapper-4"
					style={ { width: "98%", padding: "0 1%" } }
				>
					<BorderBox8>
						<div className="xpanel">
							<div
								className="fill-h"
								id="statistic_mainMap3"
							/>
						</div>
					</BorderBox8>
				</div>
			</div>
		</div>
	);
};

export default SaleData;
