import React, { useEffect, useState, useMemo } from "react";
import { Progress, Drawer, Checkbox, Button, Tooltip } from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import Title from "../Title";
import {
	IndexSettingGetSenderSettingApi,
	saleInfoGetApi,
	TradeUpdateDataBoardSaleSet,
	TradeGetDataBoardSaleSet
} from "../../../utils/api";
import styles from "./index.module.scss";

const optionList4 = [
	{
		label: "排除空包订单",
		value: "isExcludeEmptyTrade",
		tips: "即不统计空包订单",
	},
	{
		label: "排除赠品订单",
		value: "isExcludeGift",
		tips: "即不统计“商品有赠品标识”的子订单",
	},
	{
		label: "排除发货前取消订单",
		value: "isExcludeNoShipClosed",
		tips: "即统计时不包含发货前取消的订单（子订单维度）",
	},
	{
		label: "排除退款完成订单",
		value: "isExcludeRefundSuccess",
		tips: "即统计时不包含退款完成的订单（子订单维度）",
	}
];

const SettingDrawer = (props) => {
	const { showSettingDrawer, setShowSettingDrawer, userInfo, saleInfoGet } = props;

	// checkbox
	const [optionList1, setOptionList1] = useState([]);
	const [optionList2, setOptionList2] = useState([]);
	const [optionList3, setOptionList3] = useState([]);
	const [checkedList1, setCheckedList1] = useState([]);
	const [checkedList2, setCheckedList2] = useState([]);
	const [checkedList3, setCheckedList3] = useState([]);
	const [checkedList4, setCheckedList4] = useState([]);
	const [indeterminate1, setIndeterminate1] = useState(false);
	const [indeterminate2, setIndeterminate2] = useState(false);
	const [indeterminate3, setIndeterminate3] = useState(false);
	const [checkAll1, setCheckAll1] = useState(false);
	const [checkAll2, setCheckAll2] = useState(false);
	const [checkAll3, setCheckAll3] = useState(false);

	useEffect(() => {
		if (showSettingDrawer) {
			init();
		}
	}, [showSettingDrawer]);

	const init = async() => {
		const res = await TradeGetDataBoardSaleSet({});
		const checkedList4 = [];
		optionList4.forEach(i => {
			if (res[i.value]) {
				checkedList4.push(i.value);
			}
		});
		setCheckedList4(checkedList4);
		let optionList1 = [];
		let checkedList1 = [];
		let optionList2 = [];
		let checkedList2 = [];
		res?.notSellerIdList?.forEach((item) => {
			if (
				item.platform === "other"
				|| (item.platform === "hand" && item.sellerNick === "无店铺")
			) {
				if (item.selected) {
					checkedList2.push(item.sellerId);
				}
				optionList2.push({
					label: item.sellerNick,
					value: item.sellerId,
				});
			} else {
				if (item.selected) {
					checkedList1.push(item.sellerId);
				}
				optionList1.push({
					label: item.sellerNick,
					value: item.sellerId,
				});
			}
		});
		let optionList3 = [];
		let checkedList3 = [];
		optionList3 = res?.notSaleUserIdList?.map((item) => {
			if (item.selected) {
				checkedList3.push(item.saleUserId);
			}
			return {
				label: item.saleUserName,
				value: item.saleUserId,
			};
		});
		setOptionList1(optionList1);
		setOptionList2(optionList2);
		setOptionList3(optionList3);
		onChange1(checkedList1, optionList1);
		onChange2(checkedList2, optionList2);
		onChange3(checkedList3, optionList3);
	};

	const updateSetting = async() => {
		const optionList = [...optionList1, ...optionList2].map(
			(item) => item.value
		);
		const checkList = [...checkedList1, ...checkedList2];
		await TradeUpdateDataBoardSaleSet({
			isExcludeEmptyTrade: checkedList4.includes("isExcludeEmptyTrade"),
			isExcludeGift: checkedList4.includes("isExcludeGift"),
			isExcludeNoShipClosed: checkedList4.includes("isExcludeNoShipClosed"),
			isExcludeRefundSuccess: checkedList4.includes("isExcludeRefundSuccess"),
			notSaleUserIdList: optionList3
				.map((item) => item.value)
				.filter((item) => !checkedList3.includes(item)),
			notSellerIdList: optionList.filter(
				(item) => !checkList.includes(item)
			),
			userId: userInfo.userId,
		});
		setShowSettingDrawer(false);
		saleInfoGet(true);
	};

	const indeterminate0 = useMemo(() => {
		return (
			indeterminate1
			|| indeterminate2
			|| (checkAll1 && !checkAll2)
			|| (!checkAll1 && checkAll2)
		);
	}, [indeterminate1, indeterminate2, checkAll1, checkAll2]);

	const checkAll0 = useMemo(() => {
		return checkAll1 && checkAll2;
	}, [checkAll1, checkAll2]);

	const onCheckAllChange0 = (e) => {
		setCheckedList1(
			e.target.checked ? optionList1.map((item) => item.value) : []
		);
		setCheckedList2(
			e.target.checked ? optionList2.map((item) => item.value) : []
		);
		setIndeterminate1(false);
		setIndeterminate2(false);
		setCheckAll1(e.target.checked);
		setCheckAll2(e.target.checked);
	};

	const onChange1 = (list, optionList = null) => {
		const len = optionList?.length || optionList1.length;
		setCheckedList1(list);
		setIndeterminate1(!!list.length && list.length < len);
		setCheckAll1(list.length === len);
	};

	const onCheckAllChange1 = (e) => {
		setCheckedList1(
			e.target.checked ? optionList1.map((item) => item.value) : []
		);
		setIndeterminate1(false);
		setCheckAll1(e.target.checked);
	};

	const onChange2 = (list, optionList = null) => {
		const len = optionList?.length || optionList2.length;
		setCheckedList2(list);
		setIndeterminate2(!!list.length && list.length < len);
		setCheckAll2(list.length === len);
	};

	const onCheckAllChange2 = (e) => {
		setCheckedList2(
			e.target.checked ? optionList2.map((item) => item.value) : []
		);
		setIndeterminate2(false);
		setCheckAll2(e.target.checked);
	};

	const onChange3 = (list, optionList = null) => {
		const len = optionList?.length || optionList3.length;
		setCheckedList3(list);
		setIndeterminate3(!!list.length && list.length < len);
		setCheckAll3(list.length === len);
	};

	const onCheckAllChange3 = (e) => {
		setCheckedList3(
			e.target.checked ? optionList3.map((item) => item.value) : []
		);
		setIndeterminate3(false);
		setCheckAll3(e.target.checked);
	};

	const onChange4 = (list) => {
		setCheckedList4(list);
	};
	return (
		<Drawer
			title={ (
				<div>
					统计配置
					<span
						style={ {
							marginLeft: "10px",
							fontSize: "14px",
							color: "rgba(255, 255, 255, 0.65)",
							fontWeight: "400",
						} }
					>
						可自定义设置订单的统计范围
					</span>
				</div>
			) }
			width={ 800 }
			className="setting-drawer"
			placement="right"
			footer={ (
				<div>
					<Button className="common-btn" type="primary" onClick={ updateSetting }>
						确定
					</Button>
				</div>
			) }
			onClose={ () => {
				setShowSettingDrawer(false);
			} }
			visible={ showSettingDrawer }
		>
			<div className={ styles["checkbox-wrap"] }>
				<div style={ { fontSize: "20px" } }>
					自营店铺
				</div>
				<div style={ { margin: "10px 0" } }>
					<Checkbox
						indeterminate={ indeterminate0 }
						onChange={ onCheckAllChange0 }
						checked={ checkAll0 }
					>
						全选
					</Checkbox>
				</div>
				<div style={ { margin: "10px 0" } }>
					<Checkbox
						indeterminate={ indeterminate1 }
						onChange={ onCheckAllChange1 }
						checked={ checkAll1 }
					>
						平台店铺
					</Checkbox>
				</div>
				<div style={ { margin: "10px 0" } }>
					<Checkbox.Group
						options={ optionList1 }
						value={ checkedList1 }
						onChange={ onChange1 }
					/>
				</div>
				<div style={ { margin: "10px 0" } }>
					<Checkbox
						indeterminate={ indeterminate2 }
						onChange={ onCheckAllChange2 }
						checked={ checkAll2 }
					>
						虚拟店铺
					</Checkbox>
				</div>
				<div style={ { margin: "10px 0" } }>
					<Checkbox.Group
						options={ optionList2 }
						value={ checkedList2 }
						onChange={ onChange2 }
					/>
				</div>
				<div style={ { marginTop: "40px", fontSize: "20px" } }>
					分销渠道
				</div>
				<div style={ { margin: "10px 0" } }>
					<Checkbox
						indeterminate={ indeterminate3 }
						onChange={ onCheckAllChange3 }
						checked={ checkAll3 }
					>
						全选
					</Checkbox>
				</div>
				<div style={ { margin: "10px 0" } }>
					<Checkbox.Group
						options={ optionList3 }
						value={ checkedList3 }
						onChange={ onChange3 }
					/>
				</div>
				<div style={ { marginTop: "40px", fontSize: "20px" } }>
					其他配置
				</div>
				<div style={ { margin: "10px 0" } }>
					<Checkbox.Group value={ checkedList4 } onChange={ onChange4 }>
						{optionList4.map((item) => {
							return (
								<Checkbox value={ item.value }>
									{item.label}{" "}
									{item.tips && (
										<Tooltip title={ item.tips }>
											<QuestionCircleOutlined />
										</Tooltip>
									)}
								</Checkbox>
							);
						})}
					</Checkbox.Group>
				</div>
			</div>
		</Drawer>
	);
};

export default SettingDrawer;
