import React, { useEffect, useState, useMemo } from "react";
import { Tooltip } from "antd";
import { FreeMode, Autoplay, Mousewheel, Scrollbar, Virtual } from "swiper";
import { Swiper, SwiperSlide } from "swiper/react";
import Rank1 from "../../../assets/images/rank1.png";
import Rank2 from "../../../assets/images/rank2.png";
import Rank3 from "../../../assets/images/rank3.png";
import { getPlatformDetailLink } from "../../../utils/util";
import { PLAT_JD, PLAT_XHS, PLAT_YZ, DEFAULT_IMG } from "../../../utils/constant";
import Title from "../Title";


enum showItemOrSysWayEnum {
	商品 = "sysItem",
	sku = "sysSku",
	货品 = "item",
	货品sku = "sku",
}

const TodayHot = (props) => {
	const { saleInfoData, isZeroStockVersion } = props;
	const [showItemOrSysWay, setShowItemOrSysWay] = useState(
		showItemOrSysWayEnum.商品
	);
	const [hotItemData, setHotItemData] = useState([]);

	useEffect(() => {
		if (saleInfoData) {
			changeItemRank(showItemOrSysWay, false);
		}
	}, [saleInfoData]);

	const needLoop = useMemo(() => {
		return hotItemData && hotItemData.length >= 8;
	}, [hotItemData]);

	const changeItemRank = (val, reset = true) => {
		setHotItemData([]);
		setShowItemOrSysWay(val);
		handleHotItem(val, reset);
	};

	const handleHotItem = (val, reset) => {
		let rank = [];
		if (val === showItemOrSysWayEnum.商品) {
			rank = saleInfoData.todayHotItem.itemRank;
		} else if (val === showItemOrSysWayEnum.sku) {
			rank = saleInfoData.todayHotItem.skuRank;
		} else if (val === showItemOrSysWayEnum.货品) {
			rank = saleInfoData.todayHotSysItem.sysItemRank;
		} else if (val === showItemOrSysWayEnum.货品sku) {
			rank = saleInfoData.todayHotSysItem.sysSkuRank;
		}
		if (reset) {
			setTimeout(() => {
				setHotItemData(rank);
				console.log(document.querySelector('.swiper-slide'));
				document.querySelector('.swiper-slide');
			}, 100);
		} else {
			setHotItemData(rank);
		}
	};

	const getRankIcon = (index) => {
		if (index > 2) {
			return index + 1;
		} else {
			let imgSrc = Rank1;
			if (index === 1) {
				imgSrc = Rank2;
			} else if (index === 2) {
				imgSrc = Rank3;
			}
			return <img width={ 36 } height={ 36 } alt={ index + 1 } src={ imgSrc } referrerPolicy="no-referrer" />;
		}
	};

	const getPlatformGoodsLink = (item) => {
		const { numIid, skuId, platform = "" } = item;
		let id = numIid;
		const _platform = platform.toLowerCase();
		if ([PLAT_XHS, PLAT_JD, PLAT_YZ].includes(_platform)) {
			id = skuId;
		}
		return getPlatformDetailLink(_platform, id);
	};

	const renderItem = (item) => {
		if (showItemOrSysWay === showItemOrSysWayEnum.商品) {
			const linkHref = getPlatformGoodsLink(item);
			return (
				<a
					href={ linkHref || "javascript:void(0)" }
					target={ linkHref ? "_blank" : "_self" }
					rel="noreferrer"
				>
					<div style={ { display: "flex", alignItems: "center" } }>
						<img width={ 48 } height={ 48 } src={ item.picUrl || DEFAULT_IMG } alt="" referrerPolicy="no-referrer" />
						<div style={ { marginLeft: "8px" } }>
							<Tooltip title={ item.itemName }>
								<div className="hot-item-name">
									{item.itemName}
								</div>
							</Tooltip>
							<Tooltip title={ item.sellerNick }>
								<div className="hot-seller-name">
									所属店铺：{item.sellerNick}
								</div>
							</Tooltip>
						</div>
					</div>
				</a>
			);
		} else if (showItemOrSysWay === showItemOrSysWayEnum.sku) {
			const linkHref = getPlatformGoodsLink(item);
			return (
				<a
					href={ linkHref || "javascript:void(0)" }
					target={ linkHref ? "_blank" : "_self" }
					rel="noreferrer"
				>
					<div style={ { display: "flex", alignItems: "center" } }>
						<img width={ 48 } height={ 48 } src={ item.skuPicUrl || item.picUrl || DEFAULT_IMG } alt="" referrerPolicy="no-referrer" />
						<div style={ { marginLeft: "8px" } }>
							<Tooltip title={ item.itemName }>
								<div className="hot-item-name">{item.skuName}</div>
							</Tooltip>
							<Tooltip title={ item.itemName }>
								<div className="hot-seller-name">
									所属商品：{item.itemName}
								</div>
							</Tooltip>
						</div>
					</div>
				</a>
			);
		} else if (showItemOrSysWay === showItemOrSysWayEnum.货品) {
			return (
				<div style={ { display: "flex", alignItems: "center" } }>
					<img width={ 48 } height={ 48 } src={ item.picUrl || DEFAULT_IMG } alt="" referrerPolicy="no-referrer" />
					<div style={ { marginLeft: "8px" } }>
						<Tooltip title={ item.itemName }>
							<div className="hot-item-name">{item.itemName}</div>
						</Tooltip>
					</div>
				</div>
			);
		} else if (showItemOrSysWay === showItemOrSysWayEnum.货品sku) {
			return (
				<div style={ { display: "flex", alignItems: "center" } }>
					<img width={ 48 } height={ 48 } src={ item.skuPicUrl || item.picUrl || DEFAULT_IMG } alt="" referrerPolicy="no-referrer" />
					<div style={ { marginLeft: "8px" } }>
						<Tooltip title={ item.itemName }>
							<div className="hot-item-name">{item.skuName}</div>
						</Tooltip>
						<Tooltip title={ item.itemName }>
							<div className="hot-seller-name">
								{item.skuOuterId}
							</div>
						</Tooltip>
					</div>
				</div>
			);
		}
	};
	return (
		<>
			<Title
				title="今日爆款"
				largeStyle={ {
					background: "rgba(9,20,59, 1)",
				} }
			>
				<div className="switch-tab">
					<div
						className={
							showItemOrSysWay === showItemOrSysWayEnum.商品
								? "select"
								: ""
						}
						onClick={ () => {
							changeItemRank(showItemOrSysWayEnum.商品);
						} }
					>
						商品
					</div>
					<div
						className={
							showItemOrSysWay === showItemOrSysWayEnum.sku
								? "select"
								: ""
						}
						onClick={ () => {
							changeItemRank(showItemOrSysWayEnum.sku);
						} }
					>
						商品SKU
					</div>
					{!isZeroStockVersion && (
						<div
							className={
								showItemOrSysWay === showItemOrSysWayEnum.货品
									? "select"
									: ""
							}
							onClick={ () => {
								changeItemRank(showItemOrSysWayEnum.货品);
							} }
						>
							货品
						</div>
					)}
					{!isZeroStockVersion && (
						<div
							className={
								showItemOrSysWay
								=== showItemOrSysWayEnum.货品sku
									? "select"
									: ""
							}
							onClick={ () => {
								changeItemRank(showItemOrSysWayEnum.货品sku);
							} }
						>
							货品SKU
						</div>
					)}
				</div>
			</Title>
			<div
				className="xpanel-wrapper xpanel-wrapper-4"
				style={ {
					position: "relative",
					height: "48vh",
				} }
			>
				<div className="xpanel">
					<div className="fill-h" id="worldMap">
						<div
							className="table-header"
							style={ {
								display: "flex",
								color: "#fff",
								textAlign: "center",
								fontSize: "14px",
							} }
						>
							<div style={ { minWidth: "60px" } }>排名</div>
							<div
								style={ {
									width: "70%",
									textAlign: "left",
								} }
							>
								名称
							</div>
							<div style={ { width: "20%" } }>销售额</div>
							<div style={ { width: "10%" } }>件数</div>
						</div>
						<div
							style={ {
								height: "40vh",
								overflow: "hidden",
							} }
						>
							{
								hotItemData && hotItemData.length > 0 && (
									<Swiper
										direction="vertical"
										modules={ [
											FreeMode,
											Autoplay,
											Mousewheel,
											Scrollbar,
											Virtual
										] }
										spaceBetween={ 0 }
										slidesPerView={ 8 }
										style={ { color: "#fff" } }
										autoplay={ {
											delay: 1500,
											pauseOnMouseEnter: true,
										} }
										height={ 480 }
										loop={ needLoop }
										freeMode
										mousewheel
										scrollbar
										virtual
									>
										{hotItemData.map((item, index) => {
											return (
												<SwiperSlide>
													<div
														className=""
														style={ {
															display: "flex",
															flex: 1,
															alignItems: "center",
															textAlign: "center",
															fontSize: "14px",
															height: "100%",
														} }
													>
														<div
															style={ {
																minWidth: "60px",
																lineHeight: "36px",
															} }
														>
															{getRankIcon(index)}
														</div>
														<div
															style={ {
																width: "70%",
																maxWidth: "70%",
																textAlign: "left",
															} }
														>
															{renderItem(item)}
														</div>
														<div
															style={ {
																width: "20%",
															} }
														>
															{item.saleVolume}
														</div>
														<div
															style={ {
																width: "10%",
															} }
														>
															{item.saleQuantity}
														</div>
													</div>
												</SwiperSlide>
											);
										})}
									</Swiper>
								)
							}
						</div>
					</div>
				</div>
			</div>
		</>
	);
};

export default TodayHot;
