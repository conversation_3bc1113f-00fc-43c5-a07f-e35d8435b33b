import React, { useEffect, useState } from "react";
import * as echarts from "echarts";
import {
	SyncOutlined,
	FullscreenOutlined,
	SettingOutlined
} from "@ant-design/icons";
import { Button } from "antd";
import dayjs from "dayjs";
import { BorderBox8, Decoration1, Loading } from "@jiaminghi/data-view-react";
import "../index.css";
import s from "./index.module.scss";
import sendPoint from "../utils/pointTrack/sendPoint";
import Pointer from "../utils/pointTrack/constants";
import {
	IndexSettingGetSenderSettingApi,
	saleInfoGetApi,
	TradeGetDataBoardSaleSet
} from "../utils/api";
// import { _saleData } from "../../utils/mock";
import {
	MapOriginVal,
	chinaGeoCoordMap,
	removeSuffix,
	updateTimerPerSecond
} from "../utils/util";
import ShipProcess from "./components/ShipProcess";
import TodayHot from "./components/TodayHot";
import ShopRank from "./components/ShopRank";
import BuyerAreaRank from "./components/BuyerAreaRank";
import SettingDrawer from "./components/SettingDrawer";
import china from "./china.json";

const TimerArr = [
	0, 1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
	22, 23, 24
];

const buyerAreaMapOption = {
	tooltip: {
		trigger: "item",
	},
	visualMap: [
		{
			type: "continuous",
			left: "15%",
			min: 0,
			max: 100,
			text: ["高", "低"], // 文本，默认为数值文本
			itemHeight: 120,
			splitNumber: 0,
			calculable: true,
			color: ["#0054bb", "#85ADDE"],
			// inRange: {
			// 	// 选中范围中的视觉配置
			// 	color: ["#85ADDE", "#0054bb"], // 定义了图形颜色映射的颜色列表，
			// },
			textStyle: {
				color: "#c3dbff",
				fontWeight: "bold",
			},
		}
	],
	geo: {
		map: "china",
		zoom: 1.1,
		label: {
			show: true,
			textStyle: {
				color: "#FFFFFF",
				fontWeight: "bold",
			},
		},
		roam: false, // 是否允许缩放
		itemStyle: {
			normal: {
				color: "#1a1e45",
				borderColor: "rgba(0, 0, 0, 0.2)", // 省市边界线00fcff 516a89
				borderWidth: 0.2,
				textStyle: "#fff",
			},
			emphasis: {
				color: "#22ccfb", // 悬浮背景
			},
		},
		regions: MapOriginVal,
	},
	series: [
		{
			name: "销售额",
			type: "map",
			mapType: "china",
			geoIndex: 0,
			mapLocation: {
				x: "left",
			},
			itemStyle: {
				normal: {
					label: { show: true, color: "#fff" },
					borderWidth: 0,
				},
			},
			tooltip: {
				formatter: "{c}%",
			},
			data: MapOriginVal,
		}
	],
};

const saleVolumeTrendOption = {
	title: {
		show: true,
		text: "销售实时金额",
		x: "center",
		top: 10,
		textStyle: {
			// 主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
			fontSize: 14,
			fontStyle: "normal",
			fontWeight: "normal",
			color: "#01c4f7",
		},
	},
	tooltip: {
		trigger: "axis",
		axisPointer: {
			type: "shadow",
		},
	},
	legend: {
		data: ["昨天", "今天"],
		textStyle: {
			fontSize: 12,
			color: "#ffffff",
		},
		top: 20,
		right: 20,
		itemWidth: 20, // 设置宽度

		itemHeight: 12, // 设置高度

		itemGap: 10, // 设置间距
	},
	grid: {
		left: "3%",
		right: "4%",
		bottom: "3%",
		containLabel: true,
	},
	xAxis: {
		type: "category",
		data: TimerArr,
		name: "h",
		nameTextStyle: {
			color: "#c3dbff",
		},
		splitLine: {
			show: true,
			lineStyle: {
				color: ["#07234d"],
			},
		},
		axisLabel: {
			show: true,
			textStyle: {
				color: "#c3dbff", // 更改坐标轴文字颜色
				fontSize: 12, // 更改坐标轴文字大小
			},
		},
	},
	yAxis: {
		type: "value",
		boundaryGap: [0, 0.01],
		name: "元",
		nameTextStyle: {
			color: "#c3dbff",
		},
		splitLine: {
			show: true,
			lineStyle: {
				color: ["#07234d"],
			},
		},
		axisLabel: {
			show: true,
			textStyle: {
				color: "#c3dbff", // 更改坐标轴文字颜色
				fontSize: 12, // 更改坐标轴文字大小
			},
		},
	},
	series: [
		{
			name: "昨天",
			type: "bar",
			data: [],
			itemStyle: {
				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					{ offset: 0, color: "rgba(84, 107, 255, 1)" },
					{ offset: 1, color: "rgba(64, 179, 255, 0)" }
				]),
			},
		},
		{
			name: "今天",
			type: "bar",
			data: [],
			itemStyle: {
				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					{ offset: 0, color: "rgba(246, 189, 22, 1)" },
					{ offset: 1, color: "rgba(246, 189, 22, 0)" }
				]),
			},
		},
		{
			name: "昨天",
			type: "line",
			data: [],
			itemStyle: {
				color: "#34DCFE",
			},
			tooltip: {
				show: false,
			},
		},
		{
			name: "今天",
			type: "line",
			data: [],
			// yAxisIndex: 1,
			itemStyle: {
				color: "#546BFF",
			},
			tooltip: {
				show: false,
			},
		}
	],
};

let timer = null;
let currentTimerInterval = null;
let firstGet = true;
let echarts1 = null;
let echarts2 = null;
let visibilityChangeHandler = null;

const SaleData = (props) => {
	const { userInfo, versionControl } = props;
	const [loading, setLoading] = useState(true);
	const [saleInfoData, setSaleInfoData] = useState(null);
	// 是否正在调用接口
	const [isGettingInfo, setIsGettingInfo] = useState(false);
	const [currentTimer, setCurrentTimer] = useState("");
	const [isZeroStockVersion, setIsZeroStockVersion] = useState(false);
	const [showSettingDrawer, setShowSettingDrawer] = useState(false);
	// 发件人省份
	const [senderProvince, setSenderProvince] = useState("");

	useEffect(() => {
		sendPoint(Pointer.销售实时大屏);
		echarts.registerMap("china", china as any);

		// initialECharts1();
		// initialECharts2();
		echarts1 = echarts.init(document.getElementById("mainMap3"));
		echarts1.setOption(saleVolumeTrendOption);
		echarts2 = echarts.init(document.getElementById("mainMap"));
		echarts2.setOption(buyerAreaMapOption);
		setLoading(true);
		saleInfoGet();
		refreshData();
		// getDataBoardSet();
		const resize = () => {
			echarts1.resize();
			echarts2.resize();
		};
		window.addEventListener('resize', resize);
		return () => {
			clearTimeout(timer);
			clearInterval(currentTimerInterval);
			window.removeEventListener('resize', resize);
			if (visibilityChangeHandler) {
				document.removeEventListener('visibilitychange', visibilityChangeHandler);
				visibilityChangeHandler = null;
			}
		};
	}, []);

	useEffect(() => {
		if (+userInfo.version === 2 && +userInfo.versionType === 1) {
			setIsZeroStockVersion(true);
		}
		if (userInfo.userId) {
			getSenderSetting();
		}
	}, [userInfo]);

	useEffect(() => {
		if (versionControl) clearTimeout(timer);
	}, [versionControl]);

	const getSenderSetting = async() => {
		const { userId } = userInfo;
		const res = await IndexSettingGetSenderSettingApi({
			pageNo: 1,
			pageSize: 1,
			userId,
		});
		if (res.list.length > 0) {
			const senderProvince = res.list[0].senderProvince;
			setSenderProvince(senderProvince);
		}
	};

	const getDataBoardSet = () => {
		TradeGetDataBoardSaleSet({}).then((res) => {
			console.log(res);
		});
	};

	useEffect(() => {
		if (!saleInfoData || !echarts1 || !echarts2) return;
		// 买家地域排名;
		if (saleInfoData?.todayBuyerAreaRank?.provinceDataList.length > 0) {
			const mapArr = [];
			const leftLocalRank = [];
			const hasValueCityObj = {};
			const flyData = [];
			let otherAreaVal = 100;
			saleInfoData?.todayBuyerAreaRank?.provinceDataList?.forEach((item, index) => {
				const name = removeSuffix(item.areaName);
				const value = (item.rate * 100).toFixed();
				mapArr.push({
					name,
					areaName: item.areaName,
					value,
				});
				if (index < 7) {
					leftLocalRank.push({
						name: item.areaName,
						value: `${value}%`,
					});
					flyData.push([
						{
							name,
							value,
						}
					]);
					otherAreaVal -= Number(value);
				}
				hasValueCityObj[name] = item;
			});
			MapOriginVal.forEach((item) => {
				if (!hasValueCityObj[item.name]) {
					mapArr.push(item);
				}
			});
			leftLocalRank.push({
				name: "其它",
				value: `${otherAreaVal}%`,
			});

			buyerAreaMapOption.series = drawAirLine({
				flyData,
				mapArr,
				senderProvince,
			});
			buyerAreaMapOption.geo.regions = mapArr;
			echarts2.setOption(buyerAreaMapOption);
		} else {
			echarts2.setOption(buyerAreaMapOption);
		}

		// 当日店铺销售额排名

		const yesterdayMap = new Map();
		const todayMap = new Map();
		saleInfoData.saleVolumeTrend?.yesterdayInfoArr?.forEach((item) => {
			const { hour, saleVolume } = item;
			yesterdayMap.set(hour, saleVolume);
		});
		saleInfoData.saleVolumeTrend?.todayInfoArr?.forEach((item) => {
			const { hour, saleVolume } = item;
			todayMap.set(hour, saleVolume);
		});

		const yesterdayInfoArr = [];
		const todayInfoArr = [];
		// 后端只返回了有数据的几个时间 需要我们自己将没有数据的时间置为0
		TimerArr.forEach((item) => {
			if (yesterdayMap.get(item)) {
				yesterdayInfoArr.push(yesterdayMap.get(item));
			} else {
				yesterdayInfoArr.push(0);
			}

			if (todayMap.get(item)) {
				todayInfoArr.push(todayMap.get(item));
			} else {
				todayInfoArr.push(0);
			}
		});

		saleVolumeTrendOption.series[0].data = yesterdayInfoArr;
		saleVolumeTrendOption.series[1].data = todayInfoArr;
		saleVolumeTrendOption.series[2].data = yesterdayInfoArr;
		saleVolumeTrendOption.series[3].data = todayInfoArr;
		echarts1.setOption(saleVolumeTrendOption);
	}, [saleInfoData, echarts1, echarts2, senderProvince]);

	// 大于10w金额显示 万
	const getMoneyText = (num = "0") => {
		let _num: string | number = Number(num);
		if (_num > 99999) {
			_num = (_num / 10000).toFixed(2) + "万";
		} else {
			_num = _num.toFixed(2);
		}
		return _num;
	};

	const drawAirLine = ({ flyData, mapArr, senderProvince }) => {
		const numSeries = {
			name: "销售额",
			type: "map",
			mapType: "china",
			geoIndex: 0,
			mapLocation: {
				x: "left",
			},
			itemStyle: {
				normal: {
					label: { show: true, color: "#fff" },
					borderWidth: 0,
				},
			},
			tooltip: {
				formatter: "{c}%",
			},
			data: mapArr,
		};
		if (!senderProvince) {
			return [numSeries];
		}

		let _senderProvince = removeSuffix(senderProvince);
		console.log("_senderProvince:", _senderProvince);
		let formdata = _senderProvince; // 中心点

		let convertData = function(data) {
			let res = [];
			for (let i = 0; i < data.length; i++) {
				let dataItem = data[i];
				let fromCoord = chinaGeoCoordMap[dataItem[0].name];
				let toCoord = chinaGeoCoordMap[_senderProvince];
				if (fromCoord && toCoord) {
					res.push([
						{
							// 飞线从哪里出发
							coord: toCoord,
						},
						{
							// 飞线去往哪里
							coord: fromCoord,
							value: dataItem[0].value,
						}
					]);
				}
			}
			return res;
		};
		let series = [];
		[[formdata, flyData]].forEach(function(item, i) {
			series.push(
				{
					type: "lines",
					//   coordinateSystem: 'geo',
					zlevel: 2,
					effect: {
						show: true,
						period: 4, // 箭头指向速度，值越小速度越快
						trailLength: 0, // 特效尾迹长度[0,1]值越大，尾迹越长重
						symbol: "arrow", // 箭头图标
						symbolSize: 8, // 图标大小
						color: "#fcdd6e", // 轨迹颜色
					},
					lineStyle: {
						normal: {
							show: true,
							width: 2, // 尾迹线条宽度
							opacity: 1, // 尾迹线条透明度
							curveness: 0.3, // 尾迹线条曲直度
							color: "#fcdd6e", // 飞线颜色
						},
					},
					data: convertData(item[1]),
				},
				{
					type: "effectScatter",
					coordinateSystem: "geo",
					zlevel: 2,
					rippleEffect: {
						// 涟漪特效
						period: 4, // 动画时间，值越小速度越快
						brushType: "stroke", // 波纹绘制方式 stroke, fill
						scale: 3, // 波纹圆环最大限制，值越大波纹越大
						color: "#fcdd6e",
					},
					label: {
						normal: {
							show: false,
							position: "right", // 显示位置
							offset: [5, 0], // 偏移设置
							formatter(params) {
								// 圆环显示文字
								return params.data.name;
							},
							fontSize: 13,
						},
						emphasis: {
							show: false,
						},
					},
					symbol: "circle",
					symbolSize(val) {
						return 5; // 圆环大小
					},
					itemStyle: {
						normal: {
							show: false,
							color: "#fce182",
						},
					},
					data: item[1].map((dataItem) => {
						return {
							name: dataItem[0].name,
							value: chinaGeoCoordMap[dataItem[0].name].concat([
								dataItem[0].value
							]),
						};
					}),
				},
				// 中心点
				{
					type: "effectScatter",
					coordinateSystem: "geo",
					zlevel: 15,
					rippleEffect: {
						period: 4,
						brushType: "stroke",
						scale: 4,
						color: "#38ff85",
					},
					label: {
						normal: {
							show: false,
							position: "right",
							// offset:[5, 0],
							color: "#38ff85",
							formatter: "{b}",
							textStyle: {
								color: "#38ff85",
							},
						},
						emphasis: {
							show: false,
							color: "#38ff85",
						},
					},
					symbol: "circle",
					symbolSize: 5,
					itemStyle: {
						color: "#38ff85",
					},
					data: [
						{
							name: item[0],
							value: chinaGeoCoordMap[item[0]].concat([10]),
						}
					],
				},
				numSeries
			);
		});
		return series;
	};

	const refreshData = () => {
		if (timer) clearTimeout(timer);
		// 只有在页面可见时才启动定时器

		if (document.visibilityState === 'visible') {
			timer = setTimeout(async() => {
				const { isLogTimeOut } = props;
				await saleInfoGet();
				if (!isLogTimeOut) refreshData();
			}, 15000);
		} else if (!visibilityChangeHandler) {
			// 页面不可见时，设置监听器等待页面变为可见
			visibilityChangeHandler = () => {
				if (document.visibilityState === 'visible') {
					refreshData();
				}
			};
			document.addEventListener('visibilitychange', visibilityChangeHandler);
		}
	};

	const saleInfoGet = (forceUpdate = false) => {
		if (isGettingInfo) return;
		if (forceUpdate) sendPoint(Pointer.手动更新数据);
		setIsGettingInfo(true);
		saleInfoGetApi({ forceUpdate })
			.then((res) => {
				if (res) {
					const { todaySaleVolume = "", modified = "" } = res;
					res.modified = dayjs(modified).format("YYYY-MM-DD HH:mm");
					res.todaySaleArr = todaySaleVolume
						.split(".")[0]
						.padStart(7, "0")
						.split("");
					setSaleInfoData(res);
					// 处理当前时间
					// dealCurrentTimer(res.date || new Date());
					setIsGettingInfo(false);
					setLoading(false);
					return Promise.resolve();
				} else if (res === null && firstGet) {
					saleInfoGet(true);
				}
			})
			.catch(() => {
				setIsGettingInfo(false);
				setLoading(false);
				return Promise.reject();
			})
			.finally(() => {
				firstGet = false;
			});
	};

	const setTimer = (date = "") => {
		setCurrentTimer((prev) => {
			if (prev || date) {
				let _prev = updateTimerPerSecond(date || prev);
				return dayjs(_prev).format("YYYY-MM-DD HH:mm:ss");
			}
			return "";
		});
	};
	const dealCurrentTimer = (date) => {
		setTimer(date);
		clearInterval(currentTimerInterval);
		currentTimerInterval = setInterval(() => {
			setTimer();
		}, 1000);
	};

	const setFullScreen = () => {
		const docEle: any = document.querySelector("html");
		if (docEle.requestFullscreen) {
			docEle.requestFullscreen();
		} else if (docEle.mozRequestFullScreen) {
			docEle.mozRequestFullScreen(); // 火狐
		} else if (docEle.webkitRequestFullScreen) {
			docEle.webkitRequestFullScreen(); // 谷歌
		} else if (docEle.msRequestFullscreen) {
			docEle.msRequestFullscreen(); // IE
		}
	};

	return (
		<div className={ `wrapper ${s.saleDataContainer}` }>
			{loading ? (
				<Loading>
					<p style={ { color: "white", marginTop: "12px" } }>
						正在计算所有店铺最新数据，请耐心等待...
					</p>
				</Loading>
			) : (
				""
			)}
			<div className="container-fluid">
				<div className="row fill-h" style={ { display: "flex" } }>
					<div
						className="col-lg-3 fill-h"
						style={ { width: "30%", overflowY: "scroll" } }
					>
						<div className={ s.dataUpdateTime }>
							数据更新于：{saleInfoData?.modified}
							<SyncOutlined
								spin={ isGettingInfo }
								style={ { marginLeft: 8 } }
								onClick={ () => {
									saleInfoGet(true);
								} }
							/>
						</div>
						{/* 当日销售额排名 */}
						<ShopRank saleInfoData={ saleInfoData } />
						{/* 买家地域排名 */}
						<BuyerAreaRank
							saleInfoData={ saleInfoData }
						/>
					</div>
					{/* 中间部分 */}
					<div
						className="col-lg-6 fill-h"
						style={ {
							width: "40%",
							display: "flex",
							justifyContent: "space-between",
							flexDirection: "column",
						} }
					>
						<div className="xpanel-wrapper xpanel-wrapper-5">
							<div
								className="xpanel"
								style={ {
									position: "relative",
								} }
							>
								<div className="map_bg" />
								<div className="circle_allow" />
								<div className="circle_bg" />

								<p className={ s.saleDataTitle }>
									今日销售额（元）
								</p>
								<div className={ s.saleDataNum }>
									{saleInfoData?.todaySaleArr.map((item) => {
										return (
											<div className="databg">
												{item}
											</div>
										);
									})}
								</div>
								<div
									style={ {
										height: 40,
										width: 160,
										position: "absolute",
										top: 20,
										right: 20,
									} }
								>
									<Decoration1
										style={ {
											width: "100%",
											height: "100%",
										} }
									/>
								</div>
								<div className="sell-info">
									<div className="info-item">
										<span>销售订单数</span>{saleInfoData?.todayTradeSize}
									</div>
									<div className="info-item">
										<span>销售件数</span>{saleInfoData?.todaySaleQuantity}
									</div>
								</div>
								<div
									className="fill-h"
									id="mainMap"
									style={ {
										top: "24%",
										minHeight: "90%",
										height: "90%",
										right: 0,
									} }
								/>
							</div>
						</div>
						<div
							className="xpanel-wrapper xpanel-wrapper-6"
							style={ { display: "flex", marginBottom: 20 } }
						>
							<div style={ { width: "100%", paddingLeft: 8 } }>
								<BorderBox8>
									<div className="xpanel">
										<div className="fill-h" id="mainMap3" />
									</div>
								</BorderBox8>
							</div>
						</div>
					</div>
					{/* 右边部分 */}
					<div
						className="col-lg-3 fill-h"
						style={ {
							width: "30%",
							paddingTop: "2px",
							paddingRight: "1%",
						} }
					>
						{/* <div className={s.dataUpdateTime}>
							当前时间：{currentTimer}
						</div> */}
						<div style={ { textAlign: "right", marginBottom: "8px" } }>
							<Button
								className="common-btn"
								icon={ <SettingOutlined /> }
								type="primary"
								style={ { width: "auto", padding: "0 8px" } }
								onClick={ () => {
									setShowSettingDrawer(true);
								} }
							>
								统计配置
							</Button>
							<Button
								className="common-btn"
								icon={ <FullscreenOutlined /> }
								type="primary"
								style={ { width: "auto", padding: "0 8px" } }
								onClick={ setFullScreen }
							>
								全屏
							</Button>
						</div>
						<TodayHot
							saleInfoData={ saleInfoData }
							isZeroStockVersion={ isZeroStockVersion }
						/>
						<ShipProcess tradeRateInfo={ saleInfoData?.tradeRateInfo || {} } />
					</div>
				</div>
			</div>
			<SettingDrawer
				showSettingDrawer={ showSettingDrawer }
				setShowSettingDrawer={ setShowSettingDrawer }
				userInfo={ userInfo }
				saleInfoGet={ saleInfoGet }
			/>
		</div>
	);
};

export default SaleData;
