import md5 from 'md5';
import tb from '../assets/images/淘宝.png';
import hand from '../assets/images/手工订单.png';
import ali from '../assets/images/1688.png';
import jd from '../assets/images/京东.png';
import ksxd from '../assets/images/快手小店.png';
import c2m from '../assets/images/淘工厂.png';
import tm from '../assets/images/天猫.png';
import xhs from '../assets/images/小红书.png';
import fxg from '../assets/images/抖音.png';
import pdd from '../assets/images/拼多多.png';
import yz from '../assets/images/有赞.png';
import dw from '../assets/images/得物.png';
import ktt from '../assets/images/快团团.png';
import other from '../assets/images/其他.png';
import { getToken as utilsGetToken } from "./token";
import sph from '../assets/images/视频号.png';


const PLAT_MAP = {
	tb,
	hand,
	ali,
	jd,
	ksxd,
	c2m,
	tm,
	xhs,
	other,
	fxg,
	pdd,
	sph,
	yz,
	dw,
	ktt
};

const platformTradeLink = {
	pdd: {
		trade: 'https://mms.pinduoduo.com/orders/detail?type=0&sn=',
		refund: 'https://mms.pinduoduo.com/aftersales-ssr/detail?id=',
		item: 'https://mobile.yangkeduo.com/goods.html?goods_id='
	},
	tb: {
		trade: 'https://trade.taobao.com/trade/detail/trade_order_detail.htm?biz_order_id=',
		refund: 'https://refund2.taobao.com/dispute/detail.htm?disputeId=',
		item: 'https://item.taobao.com/item.htm?id='
	},
	tm: {
		trade: 'https://trade.taobao.com/trade/detail/trade_order_detail.htm?biz_order_id=',
		refund: 'https://refund2.taobao.com/dispute/detail.htm?disputeId=',
		item: 'https://detail.tmall.com/item.htm?id='
	},
	fxg: {
		trade: 'https://fxg.jinritemai.com/ffa/morder/order/detail?id=',
		refund: 'https://fxg.jinritemai.com/ffa/morder/aftersale/detail-v2?aftersale_id=',
		item: 'https://haohuo.jinritemai.com/views/product/item2?id='
	},
	ali: {
		trade: 'https://trade.1688.com/order/new_step_order_detail.htm?orderId=',
		refund: 'https://dispute.1688.com/refund/assureRefundDetail.htm?refundId=',
		item: 'https://detail.1688.com/offer/'
	},
	ksxd: {
		trade: 'https://s.kwaixiaodian.com/zone-origin/order/detail?id=',
		refund: 'https://s.kwaixiaodian.com/zone/refund/detail?refer=REFUND_LIST&refundId=',
		item: 'https://app.kwaixiaodian.com/merchant/shop/detail?id='
	},
	jd: {
		trade: 'https://neworder.shop.jd.com/order/orderDetail?orderId=',
		refund: 'https://afs.shop.jd.com/after/refundmentNew_list.action?refundmentApplyQuery.orderId=',
		item: 'https://item.jd.com/'
	},
	sph: {
		trade: 'https://store.weixin.qq.com/shop/order/detail?orderid=',
		refund: 'https://store.weixin.qq.com/shop/aftersale/detail?orderid=',
		item: 'https://store.weixin.qq.com/shop/order/detail?orderid='
	},
	xhs: {
		trade: 'https://ark.xiaohongshu.com/app-order/order/detail/',
		refund: '',
		item: 'https://www.xiaohongshu.com/goods-detail/'
	},
	c2m: {
		trade: 'https://trade.taobao.com/trade/detail/trade_order_detail.htm?biz_order_id=',
		refund: 'https://refund2.taobao.com/dispute/detail.htm?disputeId=',
		item: 'https://item.taobao.com/item.htm?id='
	},
	yz: {
		trade: 'https://www.youzan.com/v4/trade/order/detail?orderNo=',
		refund: 'https://www.youzan.com/v4/trade/refund/detail?orderNo=',
		item: 'https://detail.youzan.com/show/goods?alias='
	}
};

// 获取交易详情图片点击跳转各平台详情页的链接
export const getPlatformDetailLink = (platform:string, id:string) => {
	let curLink = platformTradeLink[platform];
	let curMain = curLink ? curLink.item : '';
	let curUrl = '';

	if (curLink && curMain && id) {
		if (platform === 'ali' || platform === 'jd') {
			curUrl = `${curMain}${id}.html`;
		} else {
			curUrl = `${curMain}${id}`;
		}
	}

	return curUrl;
};

export const getUserInfoFromHref = (searchParams: string) => {
	if (searchParams) {
		let searchArr = searchParams.split('&');
		searchArr.forEach(item => {
			const [key, value] = item.split('=');
			localStorage.setItem(key, value);
		});
	}
};

export const getToken = () => {
	return localStorage.getItem('kdzsMallToken') || '';
};

export const getUserId = () => {
	return localStorage.getItem('userId') || '';
};

export const getUserInfo = () => {
	return JSON.parse(sessionStorage.getItem('userInfo') || '{}');
};

export const unique = (arr) => {
	return [...(new Set(arr))];
};


export const getMd5Str = (str: string = '') => md5(str);

export function getCookie(name: string) {
	let arr; let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");

	arr = document.cookie.match(reg);
	return arr ? decodeURIComponent(arr[2]) : null;
}

export const getMaxVal = (val1: number | string, val2: number | string) => {
	val1 = +val1;
	val2 = +val2;
	return (val1 > val2 ? val1 : val2);
};
export const calcDomPostion = (parnetDom: any, domWidth: number = 300, domHeight: number = 300) => {
	let winHeight = 0;
	let winWidth = 0;
	if (window.innerWidth) {
		winWidth = window.innerWidth;
	} else if ((document.body) && (document.body.clientWidth)) {
		winWidth = document.body.clientWidth;
	}
	if (window.innerHeight) {
		winHeight = window.innerHeight;
	} else if ((document.body) && (document.body.clientHeight)) {
		winHeight = document.body.clientHeight;
	}
	// 边界判断
	const pageY = (winHeight - parnetDom.clientY - domHeight) < 0 ? parnetDom.clientY + ((winHeight - parnetDom.clientY - domHeight)) : parnetDom.clientY - 20;
	const pageX = (winWidth - parnetDom.clientX - domWidth) < 0 ? parnetDom.clientX + (winWidth - parnetDom.clientX - domWidth) : parnetDom.clientX - 80;
	return {
		x: pageX + 'px',
		y: pageY + 'px'
	};
};

// 调换数组内两位素位置
export const swapArray = (arr: any[], index1: number, index2: number) => {
	arr[index1] = arr.splice(index2, 1, arr[index1])[0];
	return arr;
};

export const sleep = (time: number) => {
	return new Promise((resolve) => {
		setTimeout(resolve, time);
	});
};

interface IRepeatReqItem {
	funcName: string;
	isRequset: boolean;
	result: any;
}

let repeatReqArr: IRepeatReqItem[] = [];

export const avoidRepeatReq = async(func: Function, params: any) => {
	let curReq = repeatReqArr.find(item => item.funcName === func.name);
	if (curReq && curReq.isRequset) {
		while (curReq.isRequset) {
			// eslint-disable-next-line no-await-in-loop
			await sleep(200);
		}
		return curReq.result;
	} else {
		let temp: IRepeatReqItem = {
			funcName: func.name,
			isRequset: true,
			result: null,
		};
		repeatReqArr.push(temp);
		try {
			const res = await func(params);
			temp.result = res;
			temp.isRequset = false;
			repeatReqArr = repeatReqArr.filter(item => item.funcName !== func.name);
			return res;
		} catch (error) {
			temp.result = error;
			temp.isRequset = false;
			repeatReqArr = repeatReqArr.filter(item => item.funcName !== func.name);
			return error;
		}
	}
};
// 点击Table行的时候判断是不是有效点击（点击时没有内容选区并且点击的元素是tr || td）
export const isValidTableRowClick = (e) => {
	const selectObj = window.getSelection();
	const clickElisTdOrTrNode = ['TD', 'TR'].includes(e?.target?.nodeName);
	const hasSelectedText = !!selectObj.toString();
	return clickElisTdOrTrNode && !hasSelectedText;
};

// 时间+1s
export const updateTimerPerSecond = (timer) => {
	const currentTime = new Date(timer);
	currentTime.setSeconds(currentTime.getSeconds() + 1);
	return currentTime;

};
export const removeSuffix = (location) => {
	const suffixes = ['特别行政区', '壮族自治区', '回族自治区', '维吾尔自治区', '藏族自治区', '自治区', '自治州', '地区', '县', '区', '省', '市'];

	for (let i = 0; i < suffixes.length; i++) {
		if (location.endsWith(suffixes[i])) {
			return location.substring(0, location.length - suffixes[i].length);
		}
	}

	return location; // 如果没有找到任何后缀，返回原始位置
};


/**
 * 获取订购链接
 * @param userId 用户Id
 * @returns
 */
export const getPayOrderURL = (userId:string|number) => {
	const kdzsErpToken = utilsGetToken();
	return `${window.location.origin}/orderRecord/forward?userId=${userId}&kdzserptoken=${kdzsErpToken}`;
	// return `${location.origin}/index.html?#/payGuide`;
};

export const getIconByPlat = (plat) => {
	plat = plat?.toLowerCase();
	return `<div style="width: 16px; height: 16px; background: white;margin-right: 6px; display: inline-block;border-radius: 4px;line-height: 16px;position: relative;">
		<img style="width: 18px; height: 18px;position: absolute;top: -1px; right: -1px;" alt="" src=${PLAT_MAP[plat] || PLAT_MAP["hand"]} />
	</div>`;
};

export const MapOriginVal = [
	{ name: '西藏', value: 0 },
	{ name: '青海', value: 0 },
	{ name: '宁夏', value: 0 },
	{ name: '海南', value: 0 },
	{ name: '甘肃', value: 0 },
	{ name: '贵州', value: 0 },
	{ name: '新疆', value: 0 },
	{ name: '云南', value: 0 },
	{ name: '重庆', value: 0 },
	{ name: '吉林', value: 0 },
	{ name: '山西', value: 0 },
	{ name: '天津', value: 0 },
	{ name: '江西', value: 0 },
	{ name: '广西', value: 0 },
	{ name: '陕西', value: 0 },
	{ name: '黑龙江', value: 0 },
	{ name: '内蒙古', value: 0 },
	{ name: '安徽', value: 0 },
	{ name: '北京', value: 0 },
	{ name: '福建', value: 0 },
	{ name: '上海', value: 0 },
	{ name: '湖北', value: 0 },
	{ name: '湖南', value: 0 },
	{ name: '四川', value: 0 },
	{ name: '辽宁', value: 0 },
	{ name: '河北', value: 0 },
	{ name: '河南', value: 0 },
	{ name: '浙江', value: 0 },
	{ name: '山东', value: 0 },
	{ name: '江苏', value: 0 },
	{ name: '广东', value: 0 },
	{ name: '台湾', value: 0 }
	// { name: '南海诸岛', value: 0 }
];

export const chinaGeoCoordMap = {
	黑龙江: [127.9688, 45.368],
	内蒙古: [110.3467, 41.4899],
	吉林: [125.8154, 44.2584],
	北京: [116.4551, 40.2539],
	辽宁: [123.1238, 42.1216],
	河北: [114.4995, 38.1006],
	天津: [117.4219, 39.4189],
	山西: [112.3352, 37.9413],
	陕西: [109.1162, 34.2004],
	甘肃: [103.5901, 36.3043],
	宁夏: [106.3586, 38.1775],
	青海: [101.4038, 36.8207],
	新疆: [87.9236, 43.5883],
	西藏: [91.11, 29.97],
	四川: [103.9526, 30.7617],
	重庆: [108.384366, 30.439702],
	山东: [117.1582, 36.8701],
	河南: [113.4668, 34.6234],
	江苏: [118.8062, 31.9208],
	安徽: [117.29, 32.0581],
	湖北: [114.3896, 30.6628],
	浙江: [119.5313, 29.8773],
	福建: [119.4543, 25.9222],
	江西: [116.0046, 28.6633],
	湖南: [113.0823, 28.2568],
	贵州: [106.6992, 26.7682],
	云南: [102.9199, 25.4663],
	广东: [113.12244, 23.009505],
	广西: [108.479, 23.1152],
	海南: [110.3893, 19.8516],
	上海: [121.4648, 31.2891],
	台湾: [121.5080, 25.0443],
	香港: [114.1733, 22.3200],
	澳门: [113.5489, 22.1988],
};

export const chinaDatas = [
	[
	  {
			name: '黑龙江',
			value: 100,
	  }
	],
	[
	  {
			name: '内蒙古',
			value: 0,
	  }
	],
	[
	  {
			name: '吉林',
			value: 0,
	  }
	],
	[
	  {
			name: '辽宁',
			value: 0,
	  }
	]
];
