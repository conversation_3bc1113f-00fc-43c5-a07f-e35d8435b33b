import { <PERSON><PERSON>, <PERSON>, Col, Row, Tooltip } from "antd";
import _ from "lodash";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { observer } from "mobx-react";
import { SyncOutlined } from "@ant-design/icons";
import message from '@/components/message';
import { TradeLogisticsQueryLogisticsWarnIndexCountApi } from "@/apis/user";
import { StockWarnRemindGetListApi } from "@/apis/warehouse/stock";
import userStore from "@/stores/user";
import s from '../index.module.scss';
import Icon from "@/components/Icon";
import Pointer from "@/utils/pointTrack/constants";
import { session } from "@/libs/db";
import { LOGISTICS_TYPE, logisticsWarningSettingEnum } from "@/pages/Report/LogisticsWarning/contants";
import { getMultiShops } from "@/components-biz/ShopListSelect/shopListUtils";
import { TradeLogisticsGetUserConfigApi, TradeLogisticsManualSyncLogisticsInfoApi, TradeLogisticsQueryLogisticsSyncTimeApi } from "@/apis/report/logisticsWarning";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { TradeLogisticsGetUserConfigResponse } from "@/types/schemas/report/logisticsWarning";
import useGetState from "@/utils/hooks/useGetState";
import { MenuIdMap } from "@/components-biz/layouts/HeaderMenu/menu";

export interface IMonitorCompProps {}

const UPDATE_DATE = "index_page_logistcs_remind_update_date";

const MonitorComp = (props: IMonitorCompProps) => {
	const { isShowZeroStockVersion } = userStore;
	const [switchStatus, setSwitchStatus, getSwitchStatus] = useGetState(0);
	const [updating, setUpdating] = useState(false);
	const [lastUpdateDate, setLastUpdateDate] = useState("--");

	useEffect(() => {
		// getUpdateDate();
		getLogisticsConfig();
	}, []);
	/**
	 * 异常监控
	 */
	const [monitorView, setMonitorView] = useState(() => {
		const monitorView = [
			{
				key: "stockRemind",
				totalCount: "-",
				title: "库存预警",
				desc: "本地货品库存，已达到库存预警值",
				version: [1],
				path: "/warehouse/stockWarnRemind"
			},
			{
				key: 2,
				totalCount: "-",
				title: "发货未更新",
				desc: "物流预警功能自动识别的发货未揽收数量",
				version: [1, 2],
				point: Pointer.首页_物流预警_发货未更新,
				path: `/report/logisticsWarning?type=${LOGISTICS_TYPE.发货未揽收}`
			},
			{
				key: 3,
				totalCount: "-",
				title: "揽收未更新",
				desc: "物流预警功能自动识别的揽件未更新数量",
				point: Pointer.首页_物流预警_揽收未更新,
				version: [1, 2],
				path: `/report/logisticsWarning?type=${LOGISTICS_TYPE.揽件未更新}`
			},
			{
				key: 4,
				totalCount: "-",
				title: "中转未更新",
				desc: "物流预警功能自动识别的中转未更新数量",
				point: Pointer.首页_物流预警_中转未更新,
				version: [1, 2],
				path: `/report/logisticsWarning?type=${LOGISTICS_TYPE.中转未更新}`
			},
			{
				key: 5,
				totalCount: "-",
				title: "派件预警",
				desc: "物流预警功能自动识别的派件预警数量",
				point: Pointer.首页_物流预警_派件预警,
				version: [1, 2],
				path: `/report/logisticsWarning?type=${LOGISTICS_TYPE.派件未更新}`

			},
			{
				key: 6,
				totalCount: "-",
				title: "超时未签收",
				desc: "物流预警功能自动识别的超时未签收数量",
				point: Pointer.首页_物流预警_超时未签收,
				version: [1, 2],
				path: `/report/logisticsWarning?type=${LOGISTICS_TYPE.超时未签收}`
			}
		];
		return monitorView;
	});

	/**
	 * 获取异常监控 和 权限限制
	 */
	const getMonitorCount = async(manulUpdate?:boolean) => {
		const userInfo = await userStore.getUserInfo();
		if (manulUpdate) {
			await TradeLogisticsManualSyncLogisticsInfoApi({ getAllIds: true }).finally(() => setUpdating(false));
		}
		const multiShopS = await getMultiShops({});
		const P2 = StockWarnRemindGetListApi({});
		const queryLogisticsWarnIndexCountList = TradeLogisticsQueryLogisticsWarnIndexCountApi({
			multiShopS
		});
		await Promise.all([P2, queryLogisticsWarnIndexCountList]).then(res => {
			if (manulUpdate) {
				message.success("数据已更新");
			}
			const logisticsWarnIndexCountObj = {};
			res[1]?.forEach(item => {
				 logisticsWarnIndexCountObj[item.logisticsType] = item.num;
			});
			const monitor = _.cloneDeep(monitorView);
			monitor.forEach(i => {
				if (i.key in logisticsWarnIndexCountObj && getSwitchStatus()) {
					i.totalCount = logisticsWarnIndexCountObj[i.key] || 0;
				}
			});
			// 库存预警取值 - 去掉权限限制
			monitor[0].totalCount = String(res[0]?.total || 0);
			setMonitorView(monitor);
		});
	};

	const updateData = async() => {
		session.set(UPDATE_DATE, dayjs().format("YYYY-MM-DD HH:mm:ss"));
		// await TradeLogisticsManualSyncLogisticsInfoApi();
		setUpdating(true);
		await getMonitorCount(true);
		setLastUpdateDate(dayjs().format("YYYY-MM-DD HH:mm:ss"));
		setUpdating(false);

	};

	const handleUpdate = () => {
		const moreThanSeconds = 30;
		const lastUpdateDate = session.get(UPDATE_DATE);
		if (lastUpdateDate) {
			const diffTime = dayjs().diff(lastUpdateDate, 's');
			if (diffTime < moreThanSeconds) {
				message.warning({
					content: (
						<span>
							30s 内只能更新一次，请在
							<span style={ { color: "#fc8305" } }>{30 - diffTime}</span>秒后重试
						</span>
					),
				});
				return;
			} else {
				updateData();
			}
		} else {
			updateData();
		}
	};

	const getUpdateDate = async() => {
		const { userId } = await userStore.getUserInfo();
		TradeLogisticsQueryLogisticsSyncTimeApi({
			cacheKey: `logistics_manual_sync_cache_key_${userId}`
		}).then(res => {
			if (res) {
				const dateFormat = dayjs(res).format("YYYY-MM-DD HH:mm:ss");
				setLastUpdateDate(dateFormat);
			}
		});
	};
	
	const onClickUpdate = () => {
		sendPoint(Pointer.首页_物流预警_更新);
		if (updating) {
			return;
		}
		handleUpdate();
	};

	const cardExtra = (
		<div>
			<span>
				更新时间：<span style={ { color: "#666" } }>{lastUpdateDate}</span>
			</span>
			
			<SyncOutlined
				hidden={ !switchStatus }
				className="r-pointer kdzs-link-text r-ml-6"
				spin={ updating }
				onClick={ onClickUpdate }
			/>
		</div>
	);

	const getLogisticsConfig = () => {
		TradeLogisticsGetUserConfigApi({}).then(res => {
			setSwitchStatus(res?.onOff);
			if (res?.onOff) {
				getUpdateDate();
			}
		}).finally(() => {
			getMonitorCount();
		});
	};

	return (
		<Card
			title="异常监控"
			className={ s.card }
			bordered={ false }
			style={ { height: '100%' } }
			extra={ cardExtra }
		>
			<Row className={ s.monitorContent }>
				{
					monitorView.map(item => (
						<div key={ item.key } className={ s["monitor-item"] } hidden={ isShowZeroStockVersion && !item.version.includes(2) }>
							<div>
								<span>{item.title}</span>
								<Tooltip title={ item.desc }>
									<span className="r-ml-5">
										<Icon type="wenhao-xian" size={ 15 } />
									</span>
								</Tooltip>
							</div>
							<div className="r-mt-4">
								<Link to={ item.path } onClick={ () => sendPoint(item.point) }>
									<span className={ s.link }>{item.totalCount}</span>
								</Link>
							</div>
						</div>
					))
				}
			</Row>
		</Card>
	);
};

export default observer(MonitorComp);