import React, { useEffect, useState, useRef } from 'react';
import { But<PERSON>, message } from 'antd';
import { useHistory, useLocation } from 'react-router-dom';
import axios from 'axios';
import successIcon from '@/assets/image/img_成功@2x.png';
import fallIcon from '@/assets/image/img_失败@2x.png';
import s from './index.module.scss';
import { checkWxBindApi } from '@/apis/user';

const CheckWxBind: React.FC = () => {
	const history = useHistory();
	const location = useLocation();
	const [wxMpUrl, setWxMpUrl] = useState<string>(null);
	const [loading, setLoading] = useState(true);
	const [isSuc, setIsSuc] = useState(false);
	const [errorInf, setErrorInf] = useState({});
	
	// 使用useRef来存储intervalId，这样它的值不会因为组件重新渲染而丢失
	const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
	
	// 清除轮询
	const clearPolling = () => {
		if (intervalIdRef.current) {
			clearInterval(intervalIdRef.current);
			intervalIdRef.current = null;
			console.log('轮询已清除');
		}
	};

	useEffect(() => {
		// 从URL中获取wxMpUrl参数
		const params = new URLSearchParams(location.search);
		const wxMpUrl = params.get('wxMpUrl');
		const interaction = params.get('interaction');
		const token = params.get('token');
		setWxMpUrl(wxMpUrl || '');
		setLoading(true);
		
		if (interaction) {
			// 设置轮询，每3秒检查一次
			intervalIdRef.current = setInterval(async() => {
				const data = {
					wxScanInteractionCode: interaction,
				};
				try {
					const res = await checkWxBindApi(data, token);
					console.log(res, 'resresres');
					// 如果绑定成功
					if (res.data) {
						setLoading(false);
						setIsSuc(true);
						clearPolling(); // 清除轮询
						message.success('微信绑定成功');
						// 5秒后自动关闭页面
						setTimeout(() => {
							window.close();
						}, 5000);
					} else {
						setLoading(false);
						setIsSuc(false);
						message.error('微信绑定失败');
					}
				} catch (error) {
					console.error('轮询绑定状态出错:', error);
					if (error.errorCode !== 1111) {
						console.error('轮询绑定状态出错:111', error);
						setLoading(false); // 确保设置为非加载状态
						setIsSuc(false); // 确保设置为失败状态
						setErrorInf(error);
						clearPolling(); // 清除轮询
						// message.error(error.errorMessage || '');
					}
					if (error.errorCode === 1110) {
						message.error(error.errorMessage || '');
					}
				}
			}, 3000);
			
			console.log('轮询已设置', intervalIdRef.current);
		}
		
		// 组件卸载时清除轮询
		return () => {
			clearPolling();
		};
	}, [location]);

	const handleReturn = () => {
		window.open('https://wpa1.qq.com/dSY8GJNm?_type=wpa&qidian=true', '_blank');
	};

	return (
		<div className={ s.container }>
			<div className={ s.card }>
				<div className={ s.imageContainer }>
					{
						loading ? <img src={ wxMpUrl } alt="" className={ s.image } /> 
							: isSuc ? <img src={ successIcon } alt="" className={ s.image } />
						 : <img src={ fallIcon } alt="" className={ s.image } /> 
					}
					
				</div>
				{
					loading && (
						<>
							<h2 className={ s.title }>使用微信扫一扫</h2>
						</>
					)
				}

				
				{
					!loading && isSuc && (
						<>
							<h2 className={ s.title }>关联微信成功</h2>
							<p className={ s.subtitle }>5秒后自动关闭该页面</p>
						</>
					)
				}

				{
					!loading && !isSuc && (
						<>
							<h2 className={ s.title }>关联微信失败，原因：{errorInf.errorCode === 1123 ? '当前微信已被其他用户授权关联' : (errorInf.errorMessage || '未知错误')}</h2>
							{
								errorInf.errorCode === 1123 && (
									<p className={ s.subtitle }>{errorInf.errorMessage}</p>
								)
							}
							<Button type="default" onClick={ handleReturn } className={ s.button }>
								联系客服
							</Button>
						</>
					)
				}
				
			</div>
		</div>
	);
};

export default CheckWxBind;
