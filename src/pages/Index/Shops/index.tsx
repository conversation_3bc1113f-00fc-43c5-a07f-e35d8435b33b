import React, { useEffect, useMemo, useState } from "react";
import { Input, Form, Button, Select, Modal, Empty, Spin, Tabs, Popover, Radio } from "antd";
import { QuestionCircleOutlined, MoneyCollectOutlined, ProfileOutlined, FormOutlined } from '@ant-design/icons';
import cs from 'classnames';
import { observer } from "mobx-react-lite";
import { useBoolean } from "ahooks";
import ShopCard from "../components/ShopCard";
import ImportItemModal from "./components";
import { AddNewShop, addNewShop, shopTypeEnum } from './components/addShop';
import { IndexPlatformShopGetPlatformShopsResponseObj } from '@/types/schemas/user';
import userStore from "@/stores/user";
import KddSenderTable from "../components/KddSenderTable";
import FhdSenderTable from "../components/FhdSenderTable";
import { ItemGetNotImportShopsApi } from "@/apis/user";
import { PLAT_MAP, PLAT_OTHER } from "@/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import SortShopListModal from './components/SortShopListModal';
// import { setAdParams } from "@/utils/util";
import s from './index.module.scss';
import { getParaName } from "@/utils";


const { TabPane } = Tabs;

const { Option } = Select;
enum ShopType {
    正常 = 1,
    已暂停 = 0,
    失效 = 2,
}

const ADD_EXPLAIN = [
	{ text: "平台授权费说明", link: 'https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/thyubqgxzxhl2zgq?singleDoc#', icon: <MoneyCollectOutlined className="r-mr-4" /> },
	{ text: "无店铺面单授权教程", link: 'https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/cggw909dqdavke17?singleDoc#', icon: <ProfileOutlined className="r-mr-4" /> }
];

const ShopsPage: React.FC = (props) => {
	const [form] = Form.useForm();
	const [platformList, setPlatformList] = useState(null);
	const [notImportShops, setNotImportShops] = useState(null);
	const [imporShopsModalVisible, setImportItemModalVisible] = useState<boolean>(false);
	const [loading, { setTrue, setFalse }] = useBoolean(false);
	const [platShopNums, setPlatShopNums] = useState<number>(0);
	const [isInit, setIsInit] = useState(true);
	const { userInfo, bindShopsList,
		 otherShopList, isFreeSupplierAccount, 
		 shopType, sortShopListVisible, 
		 setSortShopListVisible, validPlatformShopSize
	} = userStore;

	useEffect(() => {
		const nums = bindShopsList?.length - otherShopList?.length;
		setPlatShopNums(nums);
	}, [bindShopsList, otherShopList]);

	useEffect(() => {
		if (shopType < 0) return;
		form.setFieldsValue({ shopType });
		onSearch();
	}, [shopType]);
	

	useEffect(() => {
		setTrue();
		const hashParams:{shopType?: number} = getParaName(window.location.hash);
		setIsInit(false);
		if (hashParams?.shopType == 1) {
			userStore.setShopType(shopTypeEnum.面单店铺);
			return;
		}
		// userStore.setSearchParams({});
		const fn = async() => {
			await userStore.getUserInfo();
			getNotImportShopList();
		};
		fn();
		userStore.setShopType(shopTypeEnum.普通店铺);
		userStore.updateShopList().then((list: any) => {
			setFalse();
			let arr: string[] = [];
			list?.forEach((item: IndexPlatformShopGetPlatformShopsResponseObj) => {
				if (!arr.includes(item.platform)) {
					arr.push(item.platform);
				}
			});
			setPlatformList(arr);

			// 如果是分销商并且没有合作中的供应商，不打开添加店铺的弹窗
			// 首页跳转过来，打开添加店铺弹窗
			if (
				bindShopsList
				&& (!bindShopsList.length || props.location.state?.flag === 'openAddShopModal')
			) {
				addNewShop();
			}
		});
		return () => {
			userStore.updateShopList();
			userStore.setShopType(shopTypeEnum.普通店铺);
		};
	}, []);

	const getNotImportShopList = async() => {
		const { isShowZeroStockVersion } = userStore;
		console.log('isShowZeroStockVersion:::', isShowZeroStockVersion);
		if (isShowZeroStockVersion) return;
		if (shopType === shopTypeEnum.面单店铺) return;
		const ret = await ItemGetNotImportShopsApi();
		const notImportShops = ret?.filter(item => (!item.importItem && item.platform.toLowerCase() !== PLAT_OTHER));
		if (notImportShops.length) {
			setNotImportShops(notImportShops);
			setImportItemModalVisible(true);
			sendPoint(Pointer['店铺_待导商品的店铺弹窗_店铺弹窗_展现']);
		}
	};
	/**
     *
     * @param val 搜索
     */
	const onSearch = async() => {
		const val = form.getFieldsValue();
		try {
			setTrue();
			let res = [];
			if (val?.shopType == shopTypeEnum.普通店铺) {
				res = await userStore.updateShopList(val, true);
			} else {
				res = await userStore.updateEbillShopList({ params: val, notUpdateList: true, isUpdateTab: false });
			}
			let arr: string[] = [];
			res?.forEach((item: IndexPlatformShopGetPlatformShopsResponseObj) => {
				if (!arr.includes(item.platform)) {
					arr.push(item.platform);
				}
			});
			setPlatformList(arr);
			setFalse();
		} catch (error) {
			setFalse();
		}
	};

	const handleShopTypeChange = async(e) => {
		const { value } = e.target;
		userStore.setShopType(value);
		onSearch();
	};

	const PopoverContentCom = useMemo(() => {
		return (
			<div className={ s.popoverContent }>
				{
					ADD_EXPLAIN.map(x => (
						<a key={ x.text } className={ cs(s.block, 'r-c-black') } href={ x.link } target="_blank" rel="noreferrer">{x.icon}{x.text}</a>
					))
				}
			</div>
		);

	}, []);

	// 主账号修改店铺排序
	const handleSortShops = () => {
		setSortShopListVisible(true);
	};

	// 取消店铺排序
	const handleCancelSort = () => {
		setSortShopListVisible(false);
	};

	// 保存排序后更新列表
	const handleChangeSort = () => {
		// setTrue();
		// userStore.updateShopList().then((list: any) => {
		// 	setFalse();
		// 	let arr: string[] = [];
		// 	list?.forEach((item: IndexPlatformShopGetPlatformShopsResponseObj) => {
		// 		if (!arr.includes(item.platform)) {
		// 			arr.push(item.platform);
		// 		}
		// 	});
		// 	setPlatformList(arr);
		// });

		handleCancelSort();
	};

	return (
		<div className={ s.shopsContainerWarp }>
			<Spin spinning={ userStore.shopsLoading }>
				<div className={ s.shopsContainer }>
					<Form
						form={ form }
						layout="inline"
						onFinish={ onSearch }
						initialValues={ { shopType: userStore.shopType } }
						className={ cs(s.searchBar, 'r-bg-white', 'r-flex', 'r-ai-c', 'r-pd-16') }
					>

						<Form.Item name="shopType">
							<Radio.Group buttonStyle="solid" onChange={ handleShopTypeChange }>
								<Radio.Button value={ shopTypeEnum.普通店铺 }>普通店铺</Radio.Button>
								<Radio.Button value={ shopTypeEnum.面单店铺 }>面单店铺</Radio.Button>
							</Radio.Group>
						</Form.Item>	
						<Form.Item name="platformType">
							<Select
								allowClear
								placeholder="选择平台"
								style={ { width: 160 } }
								// size="small"
							>
								{platformList?.map((plat: string) => {
									return <Select.Option key={ plat } value={ plat }>{PLAT_MAP[plat]}</Select.Option>;
								})}
							</Select>
						</Form.Item>
						<Form.Item
							name="sellerNick"
						>
							<Input
								style={ { width: 160 } }
								// size="small"
								placeholder="店铺名称"
							/>
						</Form.Item>
						<Form.Item style={ {
							'flex': '1',
						} }
						>
							<div className="r-flex r-jc-sb r-w-full">
								<div className="r-flex r-ai-c">
									<Button loading={ loading } type="primary" htmlType="submit" data-point={ Pointer.店铺_查询 }>
										查询
									</Button>
									<Button className="r-ml-8" onClick={ () => form.resetFields() } >
										重置
									</Button>
								</div>
								<div className="r-flex r-ai-c">
									<AddNewShop disabled={ !bindShopsList } />
									
									<Popover arrowPointAtCenter placement="bottomRight" content={ PopoverContentCom }>
										<QuestionCircleOutlined className="r-ml-8" />
									</Popover>

								</div>
							</div>
						</Form.Item>
					</Form>
					<Tabs
						defaultActiveKey="0"
						className={ cs(s.shopListWrapper, 'r-mt-8 r-pb-16 r-bg-white r-pl-16') }
						tabBarExtraContent={ isFreeSupplierAccount ? '' : (
							<div
								className="r-c-warning"
								style={ { position: 'absolute', left: `${(userStore?.statusList?.length || 0) * 90}px`, bottom: 11 } }
							>
								{userStore?.userInfo?.level === 2 && userStore?.userInfo?.shopNum >= 20 ? `不限制店铺数` : `可添加店铺数：${validPlatformShopSize >= 0 ? validPlatformShopSize : 0}/${userInfo?.shopNum || 0}`}
							</div>
						) }
						moreIcon={ null }
					>
						{userStore?.statusList?.map((statusListItem) => {
							return (
								<TabPane tab={ `${statusListItem.title}(${statusListItem.tabDataList.length})` } key={ statusListItem.activeKey }>
									<Spin spinning={ loading }>
										{
											statusListItem.activeKey == 0 && statusListItem.tabDataList.length > 0 && !userInfo?.subUserId && (
												<div>
													<Button 
														type="default" 
														size="small" 
														icon={ <FormOutlined /> }
														onClick={ handleSortShops }
													>
														修改排序
													</Button>
												</div>
											)
										}
										<div style={ { display: "flex", flexWrap: "wrap" } }>
											{
												statusListItem.tabDataList.length ? statusListItem.tabDataList.map((item: IndexPlatformShopGetPlatformShopsResponseObj) => {
													return <ShopCard searchData={ form.getFieldsValue() } data={ item } key={ item.id } getNotImportShopList={ getNotImportShopList } />;
												}) : (
													<div style={ { width: "100%", minHeight: "300px", paddingTop: 50 } }>
														<Empty
															image={ Empty.PRESENTED_IMAGE_DEFAULT }
															description={ (
																<span>
																	{statusListItem.noDataTips}
																</span>
															) }
														>
															{
																statusListItem.isCanAddNewShop ?	(
																	<AddNewShop disabled={ !bindShopsList } />
																) : ''
															}

														</Empty>
													</div>
												)
											}
										</div>
									</Spin>
								</TabPane>
							);
						})}
					</Tabs>
				</div>
			</Spin>
			{/* <ImportItemModal shopList={ notImportShops } onOk={ () => addNewShop() } onClose={ () => setImportItemModalVisible(false) } visible={ imporShopsModalVisible } /> */}
			<KddSenderTable shopData={ userStore.curShopCardData } visible={ userStore.kddSenderTableVisible } onClose={ () => userStore.setKddSenderTableVisible(false) } />
			<FhdSenderTable shopData={ userStore.curShopCardData } visible={ userStore.fhdSenderTableVisible } onClose={ () => userStore.setFhdSenderTableVisible(false) } />
			

			{
				sortShopListVisible && (
					<SortShopListModal onCancel={ handleCancelSort } onChange={ handleChangeSort } /> 
				)
			}
		</div>
	);
};
export default observer(ShopsPage);
