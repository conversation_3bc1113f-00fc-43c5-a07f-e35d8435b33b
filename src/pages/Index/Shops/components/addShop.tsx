import React, { useEffect, useMemo, useState } from "react";
import { Input, Form, Button, Select, Modal, Empty, Spin, Tabs, Popover, Table, Dropdown, Menu } from "antd";
import _, { cloneDeep } from "lodash";
import cs from 'classnames';
import ReactDOM from 'react-dom';
import { DownOutlined, ExclamationCircleFilled, ExclamationCircleOutlined } from "@ant-design/icons";
import { PLAT_HAND, PLAT_MAP, PLAT_OTHER, UserLevelMap } from "@/constants";
import userStore from "@/stores/user";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { getPayOrderURL, setAdParams } from "@/utils/util";
import { Authorize, diffShops } from "@/utils";
import message from "@/components/message";
import { PlatInfo, MDPlatInfo } from "../constant";
import Icon from '@/components/Icon';
import { ItemGetNotImportShopsApi, LoginCustomShopCreateApi, OperateShopApi } from "@/apis/user";
import { TradeUserOperateShopApi } from "@/apis/trade";
import { updateShopTemp } from "@/utils/print/shopTemp";
import s from './index.module.scss';

const { confirm } = Modal;

export enum shopTypeEnum {
	普通店铺 = 0,
	面单店铺 = 1,
}

/**
 * 添加店铺-选择平台
 */
export const AddNewShop = (props) => {
	const { show, disabled } = props;
	const { userInfo, bindShopsList, otherShopList, isFreeSupplierAccount, eBillBindShopsList, shopType } = userStore;
	const platShopNums = bindShopsList?.length - otherShopList?.length;
	const [visible, setVisible] = useState(false);
	const [dataSource, setDataSource] = useState([]);
	const [selectPlat, setSelectPlat] = useState(null);
	const [platInfoList, setPlatInfoList] = useState(PlatInfo);

	const canAddShopNums = useMemo(() => {
		return userInfo?.shopNum - ((bindShopsList?.length || 0) - (otherShopList?.length || 0));
	}, [userInfo?.shopNum, bindShopsList, otherShopList]);

	const canAddEbillShopNums = useMemo(() => {
		return userInfo?.ebillShopNum - eBillBindShopsList?.length || 0;
	}, [userInfo?.ebillShopNum, eBillBindShopsList]);

	useEffect(() => {
		if (show) {
			setVisible(true);
		}
	}, [show]);

	useEffect(() => {
		if (shopType == shopTypeEnum.面单店铺) {
			setPlatInfoList(MDPlatInfo);
			setSelectPlat(MDPlatInfo?.[0]?.plat);
		} else {
			setPlatInfoList(PlatInfo);
			setSelectPlat(PlatInfo?.[0]?.plat);
		}
	}, [shopType, visible]);

	useEffect(() => {
		if (!selectPlat || !platInfoList?.length) return;
		setDataSource(() => platInfoList?.filter((item) => item.plat === selectPlat));
	}, [selectPlat, platInfoList]);


	const authorize = (plat: string, waitDevelop = false) => {
		if (plat === PLAT_HAND) {
			beforeAddOtherNewShop();
			return;
		}
		// 供应商免费版暂时不支持添加其他平台店铺
		if (isFreeSupplierAccount && shopType == shopTypeEnum.普通店铺) {
			message.info('当前版本不支持绑定平台店铺');
			return;
		}
		if (shopType == shopTypeEnum.面单店铺 && canAddEbillShopNums <= 0) {
			confirm({
				closable: true,
				maskClosable: true,
				title: '店铺绑定数量不足',
				content: (
					<div className="r-error">
						面单店铺最多绑定100个店铺，请您先删除部分店铺后绑定
					</div>
				),
				okText: "确定",
				cancelText: '联系客服',
				onOk: (closeFn) => {
					closeFn();
				},
				onCancel: (e) => {
					if (!e?.triggerCancel) {
						sendPoint(Pointer.绑定店铺_数量不足_联系客服);
						window.open('https://wpa1.qq.com/dSY8GJNm?_type=wpa&qidian=true', '_blank');
					}
				}
			});
			return;
		} else if (shopType == shopTypeEnum.普通店铺 && canAddShopNums <= 0) {

			confirm({
				closable: true,
				maskClosable: true,
				title: '店铺绑定数量不足',
				content: (
					<div className="r-error">
						您当前版本为{UserLevelMap[userInfo?.level]}本，最多可绑定{userInfo?.shopNum}个店铺，目前已绑定{platShopNums}个店铺{userInfo?.level === 2 ? '。' : '，您可以增加店铺或升级版本'}
					</div>
				),
				okText: userInfo?.level === 2 ? null : '升级版本',
				cancelText: '联系客服',
				onOk: (closeFn) => {
					if (userInfo?.level === 2) {
						closeFn();
					} else {
						sendPoint(Pointer.绑定店铺_数量不足_升级版本);
						window.open(getPayOrderURL(userInfo?.userId), '_blank');
					}
				},
				onCancel: (e) => {
					if (!e?.triggerCancel) {
						sendPoint(Pointer.绑定店铺_数量不足_联系客服);
						window.open('https://wpa1.qq.com/dSY8GJNm?_type=wpa&qidian=true', '_blank');
					}
				}
			});
			return;
		}

		if (!waitDevelop) {
			Authorize(plat, 2, null, shopType);
			document.removeEventListener('visibilitychange', updateStoreFunction);
			document.addEventListener("visibilitychange", updateStoreFunction);
		} else {
			message.info('该平台正在努力对接中');
		}
	};

	const renderTitle = (
		<>
			<span className="r-mr-6">{shopType == shopTypeEnum.普通店铺 ? "添加店铺-选择平台" : "添加面单店铺-选择平台"}</span>
			{
				shopType == shopTypeEnum.面单店铺 ? <span className="r-c-warning r-fs-14">（面单店铺将不影响您的店铺绑定数量）</span>
					: canAddShopNums > 0 ? (isFreeSupplierAccount ? '' : (
						<span className="r-c-warning r-fs-14">{userInfo?.level === 2 && userInfo?.shopNum >= 20 ? `（不限制店铺数）` : `当前可添加店铺数：${canAddShopNums}个`}</span>
					)) : (
						<span className="r-c-warning r-fs-14">
							可添加线上店铺数已达上线{canAddShopNums}
							{userInfo?.level === 2 ? '。' : (
								<>
									，可升级高配版添加更多店铺
									<a
										className="r-ml-10 r-click"
										href={ getPayOrderURL(userInfo?.userId) }
										target="_blank"
										rel="noreferrer"
									>
										前往升级
									</a>
								</>
							)}
						</span>
					)
			}
		</>
	);

	const columns = [
		{
			title: "平台",
			dataIndex: "name",
			width: 130,
			render(_, record,) {
				let name = record.name;
				if (record.name === '快团团') {
					name = '快团团（团长业务）';
				}
				return (
					<span className="r-c-666">{name}</span>
				);
			}
		},
		{
			title: "接口费用",
			dataIndex: "apiPayment",
			width: 150,
			render(_, record,) {
				return (
					<span className="r-c-666">{ typeof _ === 'string' ? _ : `¥ ${_}元/年`}</span>
				);
			}
		}, {
			title: "可退款金额",
			dataIndex: "refundMoney",
			render(_, record,) {
				return (
					<span className="r-c-666">
						{typeof _ === 'string' ? _ : `¥ ${_}`}
						<a className="r-ml-4 r-c-primary" href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/thyubqgxzxhl2zgq?singleDoc#" target="_blank" rel="noreferrer">退款教程</a>
					</span>
				);
			}
		}, {
			title: "平台手续费",
			dataIndex: "servicePayment",
			render(_, record,) {
				return (
					<span className="r-c-666">
						<span className="r-c-error">{typeof _ === 'string' ? _ : `¥ ${_} `}</span>
						<span>/店铺</span>
					</span>
				);
			}
		}, {
			title: "操作",
			dataIndex: "errorMessage",
			render(_, record,) {

				return (
					<Button type="primary" size="small" onClick={ () => { authorize(record.plat, record.waitDevelop); } }>添加新店铺</Button>
				);
			}
		}
	];
	const onClose = () => {
		setSelectPlat(null);
		setDataSource([]);
		setVisible(false);
	};

	const onOk = () => {
		setVisible(false);
	};
	const handleSelectPlatform = (platInfo) => {
		const { plat, waitDevelop } = platInfo;
		setSelectPlat(plat);

	};

	const handleShopType = () => {
		// 免费供应商 普通店铺只能添加手工店铺
		if (isFreeSupplierAccount && shopType == shopTypeEnum.普通店铺) {
			authorize(PLAT_HAND);
		} else {
			setVisible(true);
		}

		if (shopType === shopTypeEnum.普通店铺) {
			sendPoint(Pointer.店铺_添加新店铺);
		} else {
			sendPoint(Pointer.店铺_添加面单店铺);
		}
	};



	return (
		<>
			<Modal
				visible={ visible }
				className="shops-addShopModal"
				title={ renderTitle }
				onCancel={ onClose }
				onOk={ () => { sendPoint(Pointer['店铺_添加店铺弹窗_确定']); onOk(); } }
				centered
				footer={ null }
				width={ 948 }
				maskClosable={ false }
			>
				<div>
					<div className="r-flex r-jc-fs r-fw-w r-hairline--top r-mb-30" style={ { gap: 16 } }>
						{
							platInfoList.map((item) => (
								<div
								// authorize(item.plat, item.waitDevelop)
									onClick={ () => { handleSelectPlatform(item); } }
									key={ item.key }
									className={ cs('r-pointer r-ta-c r-selected addShop r-mb-20', selectPlat == item?.plat ? 'addShopSelected' : '') }
									style={ { width: 108, height: 130, position: 'relative', border: '1px solid rgba(0,0,0,0.1)' } }
								>
									<div style={ { position: 'relative' } }>
										<Icon svg size={ 60 } type={ item.platform } className="r-mt-20" />
										{item.waitDevelop ? <span className={ s['wait-develop'] }>即将上线</span> : ''}
									</div>
									<div className="r-mt-16 r-fs-13" style={ { color: 'rgba(0,0,0,0.5)' } }>{item.name}</div>
								</div>
							))
						}
					</div>
				</div>

				<div className={ s.BottomListContainer }>
					{/* <div className={ s.warningTip }> */}
					{shopType == shopTypeEnum.普通店铺
						? (
							<div className={ s.warningTip }>
								<p>1. 普通店铺可以下载店铺订单，进行打单发货，如无平台店铺，建议选择面单店铺。 </p>
								<p>2. 由于电商平台要求，绑定店铺时需要支付店铺接口费用，店铺绑定成功后可在「设置 - 平台授权费返还」申请退款。 </p>
								{/* <ExclamationCircleFilled style={ { color: "#FAAD14" } } /> 由于电商平台要求，绑定店铺时需要支付店铺接口费用，店铺绑定成功后可在「设置 - 平台授权费返还」申请退款。 */}
							</div>
						)
						: (
							<div className={ s.warningTip }>
								<p>1. 面单店铺支持打印快递单，不支持订单/商品下载、库存同步、订单发货、售后下载等，如需如上操作，建议选择普通店铺。</p>
								<p>2. 由于电商平台要求，绑定店铺时需要支付店铺接口费用，店铺绑定成功后可在「设置 - 平台授权费返还」申请退款。 </p>
								{/* <ExclamationCircleFilled style={ { color: "#FAAD14" } } /> 由于电商平台要求，绑定店铺时需要支付店铺接口费用，店铺绑定成功后可在「设置 - 平台授权费返还」申请退款。 */}
							</div>
						)}

					{/* </div> */}
					<Table
						size="small"
						pagination={ false }
						bordered
						dataSource={ dataSource }
						columns={ columns }
					/>
				</div>
			</Modal>
			<Button disabled={ disabled } type="primary" onClick={ handleShopType }>添加新店铺</Button>
		</>

	);


};

/**
 * 添加店铺-选择平台
 */
export const addNewShop = () => {
	const { userInfo, bindShopsList, otherShopList, isFreeSupplierAccount } = userStore;
	const platShopNums = bindShopsList?.length - otherShopList?.length;
	let canAddShopNums = userInfo?.shopNum - platShopNums;
	console.log('%c [ 可添加店铺数量 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', canAddShopNums);
	const authorize = (plat: string, waitDevelop = false) => {
		if (plat === PLAT_HAND) {
			beforeAddOtherNewShop();
			return;
		}
		// 供应商免费版暂时不支持添加其他平台店铺
		if (isFreeSupplierAccount) {
			message.info('当前版本不支持绑定平台店铺');
			return;
		}

		if (canAddShopNums <= 0) {
			confirm({
				closable: true,
				maskClosable: true,
				title: '店铺绑定数量不足',
				content: (
					<div className="r-error">
						您当前版本为{UserLevelMap[userInfo?.level]}本，最多可绑定{userInfo?.shopNum}个店铺，目前已绑定{platShopNums}个店铺{userInfo?.level === 2 ? '。' : '，您可以增加店铺或升级版本'}
					</div>
				),
				okText: userInfo?.level === 2 ? null : '升级版本',
				cancelText: '联系客服',
				onOk: (closeFn) => {
					if (userInfo?.level === 2) {
						closeFn();
					} else {
						sendPoint(Pointer.绑定店铺_数量不足_升级版本);
						window.open(getPayOrderURL(userInfo?.userId), '_blank');
					}
				},
				onCancel: (e) => {
					if (!e?.triggerCancel) {
						sendPoint(Pointer.绑定店铺_数量不足_联系客服);
						window.open('https://wpa1.qq.com/dSY8GJNm?_type=wpa&qidian=true', '_blank');
					}
				}
			});
			return;
		}

		if (!waitDevelop) {
			Authorize(plat, 2);
			document.removeEventListener('visibilitychange', updateStoreFunction);
			document.addEventListener("visibilitychange", updateStoreFunction);
		} else {
			message.info('该平台正在努力对接中');
		}
	};

	confirm({
		icon: '',
		className: 'shops-addShopModal',
		centered: true,
		width: 810,
		title: '添加店铺-选择平台',
		content: (
			<div>
				{
					canAddShopNums > 0 ? (isFreeSupplierAccount ? '' : (
						<div className="r-c-warning">{userInfo?.level === 2 && userInfo?.shopNum >= 20 ? `不限制店铺数` : `当前可添加店铺数：${canAddShopNums}个`}</div>
					)) : (
						<div className="r-c-warning">
							可添加线上店铺数已达上线
							{userInfo?.level === 2 ? '。' : (
								<>
									，可升级高配版添加更多店铺
									<a
										className="r-ml-10 r-click"
										href={ getPayOrderURL(userInfo?.userId) }
										target="_blank"
										rel="noreferrer"
									>
										前往升级
									</a>
								</>
							)}
						</div>
					)
				}
				<div className="r-flex r-jc-fs r-fw-w r-hairline--top r-mt-40 r-mb-30" style={ { gap: 20 } }>
					{
						PlatInfo.map((item) => (
							<div
								onClick={ () => authorize(item.plat, item.waitDevelop) }
								key={ item.key }
								className={ cs('r-pointer r-ta-c r-selected addShop r-mb-20') }
								style={ { width: 132, height: 130, position: 'relative', border: '1px solid rgba(0,0,0,0.1)' } }
							>
								<div style={ { position: 'relative' } }>
									<Icon svg size={ 60 } type={ item.platform } className="r-mt-20" />
									{item.waitDevelop ? <span className={ s['wait-develop'] }>即将上线</span> : ''}
								</div>
								<div className="r-mt-16 r-fs-15" style={ { color: 'rgba(0,0,0,0.5)' } }>{item.name}</div>
							</div>
						))
					}
				</div>
			</div>
		),
	});
};


const AddNewShopModal = (props:{open:boolean}) => {
	const [addNewForm] = Form.useForm();
	const [show, setShow] = useState(props.open || false);
	const addOtherNewShopFunc = async() => {
		sendPoint(Pointer.店铺_添加新店铺_其他_弹窗点击);
		let { sellerNick, sellerAbbreviation } = addNewForm.getFieldsValue();
		sellerNick = sellerNick?.trim();
		sellerAbbreviation = sellerAbbreviation?.trim();
		await addNewForm.validateFields();
		try {
			await LoginCustomShopCreateApi({ sellerNick, sellerAbbreviation });
			message.success("添加成功");
			const oldShopList = cloneDeep(userStore?.shopList);
			const list = await userStore.updateShopList();
			const newShopList = _.cloneDeep(list);
			operateShopsAdd(oldShopList, newShopList, PLAT_OTHER);
			addNewForm.resetFields();
		} catch (error) {
			return Promise.reject(new Error("添加店铺失败"));
		}
	};
	return (
		<Modal
			title="添加新店铺-其他"
			centered
			width={ 460 }
			onCancel={ () => {
				addNewForm.resetFields();
				setShow(false);
			} }
			onOk={ async() => {
				await addOtherNewShopFunc();
				setShow(false);
			} }
			okText="确定"
			cancelText="取消"
			visible={ show }
			destroyOnClose
			closable
			maskClosable={ false }
		>
			<Form form={ addNewForm }>
				<Form.Item
					name="sellerNick"
					label="店铺名称"
					rules={ [
						{
							required: true,
							message: '请输入店铺名称',
							type: 'string'
						},
						{
							whitespace: true,
							message: '不能为空字符',
						},
						{
							max: 16,
							message: '最多16个字符'
						}
					] }
				>
					<Input style={ { width: 260, height: 30 } } size="small" placeholder="请输入店铺名称，最多16个字符" />
				</Form.Item>
				<Form.Item
					name="sellerAbbreviation"
					label="店铺简称"
					rules={ [
						{
							required: true,
							message: '请输入店铺简称',
							type: 'string'
						},
						{
							whitespace: true,
							message: '不能为空字符',
						},
						{
							max: 20,
							message: '最多20个字符'
						}
					] }
				>
					<Input style={ { width: 260, height: 30 } } size="small" placeholder="请输入店铺简称，最多20个字符" />
				</Form.Item>
			</Form>
		</Modal>
	);
};
const beforeAddOtherNewShop = () => {
	console.log('%c [ 添加其他店铺 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '添加其他店铺');
	// if (userStore?.otherShopList?.length > 9) {
	// 	message.warning("最多支持添加10个其他虚拟店铺");
	// 	return;
	// }
	sendPoint(Pointer.店铺_添加新店铺_其他_弹窗展现);

	const element = document.getElementById('addOtherNewShop');
	if (element) {
		element.remove();
	}
	let insertDiv = document.createElement('div');
	insertDiv.id = 'addOtherNewShop';
	document.body.appendChild(insertDiv);

	ReactDOM.render(
		<AddNewShopModal open />,
		document.getElementById("addOtherNewShop")
	);
};

const updateStoreFunction = () => {
	if (document.visibilityState === 'visible') {
		updateShops();
	}
};

const updateShops = () => {
	document.removeEventListener('visibilitychange', updateStoreFunction);
	const onOk = async() => {
		const type = userStore.shopType;
		let list = [];
		let oldShopList = [];
		if (type == shopTypeEnum.面单店铺) {
			oldShopList = cloneDeep(userStore?.ebillShopList);
			list = await userStore.updateEbillShopList({});
		} else {
			oldShopList = cloneDeep(userStore?.shopList);
			list = await userStore.updateShopList();
		}

		const newShopList = _.cloneDeep(list);
		operateShopsAdd(oldShopList, newShopList);
		console.log("oldShopList, newShopList :", oldShopList, newShopList);
		if (oldShopList.length === newShopList?.length) {
			message.error('系统未检测到添加店铺，请您重新尝试');
		}
	};
	Modal.confirm({
		centered: true,
		title: '是否授权成功',
		content: '若您已授权登录成功，请点击按钮更新店铺',
		okText: '更新店铺',
		cancelText: '退出',
		onOk,
		onCancel: () => document.removeEventListener('visibilitychange', updateStoreFunction)
	});
};



/**
	 *
	 * @param type 操作类型,1添加,2停用删除,3重新启用
	 * @param newShopList 新店铺
	 */
const operateShopsAdd = (oldShopList: any, newShopList: any, from?: string) => {
	const shops = diffShops(oldShopList, newShopList);
	const { shopType } = userStore;
	console.log(shops, oldShopList, newShopList, '新添加的店铺', shopType);

	if (shopType === shopTypeEnum.普通店铺) {
		if (shops.length > 0) {
			const param = {
				opType: 1,
				sellerId: shops[0]?.sellerId,
				platform: shops[0]?.platform,
				sellerNick: shops[0]?.sellerNick,
			};
			if (from !== PLAT_OTHER) OperateShopApi(param);
			TradeUserOperateShopApi(param);
			userStore.setNewUserGuide({ shopAuth: true });
		}
		// updateShopTemp(shops);
	} else if (shops.length > 0) {
		userStore.setNewUserGuide({ shopAuth: true });
	}
};
