import React, { useEffect, useState } from "react";
import cs from 'classnames';
import { Form, Input, Button, Checkbox, Modal } from 'antd';
import _ from 'lodash';
import { Link, useHistory } from "react-router-dom";
import { useCountDown, useBoolean } from "ahooks";
import s from './index.module.scss';
import { IndexUserCheckInvitationCode<PERSON>pi, user<PERSON>ogin<PERSON>pi, userRegister<PERSON><PERSON> } from "@/apis/user";
import VerifyCode from "../../components/VerifyCode";
import FooterLayout from "@/components-biz/layouts/FooterLayout";
import HeaderLayout from "@/components-biz/layouts/HeaderLayout";
import userStore from "@/stores/user";
import { ProtocalTemp } from "./protocol";
import { PASSWORD_RULES } from "@/constants";
import { getMd5Str } from "@/utils/util";
import platformLogo from '../../../../assets/image/index/image_logo.png';
import erpText1 from '../../../../assets/image/index/text.png';
import Icon from "@/components/Icon";
import events from "@/utils/events";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { getStrParam } from "@/utils";
import { local } from "@/libs/db";
import message from "@/components/message";


const RegisterPage: React.FC = () => {
	const history = useHistory();
	const [form] = Form.useForm();
	const [verifyCode_, updateVerifyCode] = useState<string>(null);
	const [mobile_, updateMobile] = useState<string>(null);
	const [isShowPwd, setIsShowPwd] = useState<boolean>(true);
	const [loading, setLoading] = useState<boolean>(false);
	const [targetDate, setTargetDate] = useState<number>();
	const [countdown, formattedRes] = useCountDown({ 
		targetDate,
		onEnd: () => { !loading && go2Login(); } 
	});
	const { seconds } = formattedRes;
	const [showSuccesIcon, { setFalse, setTrue }] = useBoolean(false);
	const [loginInfoCache, setLoginInfoCache] = useState(null);
	const [info, setInfo] = useState<string>(null);
	/**
	 * 提交表单
	 * @param val 表单数据
	 */
	const onFinish = async(val: any) => {
		if (loading) return;
		setLoading(true);
		try {
			if (val?.invitationCode) {
				const data = await IndexUserCheckInvitationCodeApi({
					invitationCode: val?.invitationCode,
				});
	
				if (data) {
					form.setFields([{
						name: 'invitationCode',
						errors: ['邀请码错误，请联系销售人员'],
					}]);
					setLoading(false);
					return;
				}
			}
			const _source = localStorage.getItem("source");
			if (_source) {
				val.source = _source;
				local.remove('source');
			}
			let loginInfoCache = _.cloneDeep(val);
			delete loginInfoCache?.invitationCode;
			setLoginInfoCache(loginInfoCache);
			const { mobile } = await userRegisterApi(val) || {};
			setLoading(false);
			if (mobile) {
				setTrue();
				sendPoint(Pointer.登录页_注册窗口_注册成功, false);
				setTargetDate(Date.now() + 1000 * 3);
			}
		} catch (error) {
			setLoading(false);
		}
	};

	const go2Login = async() => {
		if (loading) return;
		setLoading(true);
		const params = {
			accoutName: form.getFieldValue('mobile'),
			...loginInfoCache,
			loginType: 3,
		};
		if (info) {
			params.info = info;
		}
		try {
			const res = await userLoginApi(params);
			if (res?.userId) {
				userStore.setUserInfo(res);
				setTimeout(() => {
					setLoading(false);
					location.href = `?userId=${getMd5Str(`${res.userId}_${res?.subUserId}`)}#/initialize`;
				}, 200);
			}
		} catch (error) {
			setLoading(false);
		}
	};

	const onFormValueChange = (e: any) => {
		const { verifyCode, mobile } = e;
		// eslint-disable-next-line no-prototype-builtins
		if (e.hasOwnProperty('verifyCode')) {
			updateVerifyCode(verifyCode);
		}
		// eslint-disable-next-line no-prototype-builtins
		if (e.hasOwnProperty('mobile')) {
			updateMobile(mobile);
		}
		// eslint-disable-next-line no-mixed-operators
		// if (mobile_?.length === 11 && verifyCode?.length === 6 || mobile?.length === 11 && verifyCode_?.length === 6) {
		// 	setIsShowPwd(true);
		// }
	};

	const onShowProtocol = () => {
		const onOk = () => {
			form.setFieldsValue({ remember: true });
		};
		Modal.confirm({
			icon: null,
			title: "快递助手ERP用户协议及隐私政策",
			centered: true,
			visible: true,
			width: 800,
			content: (
				<div style={ { height: 500, overflow: 'scroll' } }>
					{ProtocalTemp()}
				</div>
			),
			okText: '同意',
			onOk,
		});
	};
	useEffect(() => {
		sendPoint(Pointer.登录页_注册窗口_注册窗口_展现, false);
		const clearCaptcha = () => {
			form.setFieldsValue({ captcha: '' });
		};
		events.on('refreshCaptcha', clearCaptcha);
		const info = getStrParam('info');
		if (info) {
			setInfo(info);
		}
		return () => {
			events.off('refreshCaptcha', clearCaptcha);
		};
	}, []);
	return (
		<div>
			<HeaderLayout />
			<div className={ cs(s.registerContainer) }>
				<img className={ s.platformLogo } src={ platformLogo } alt="" srcSet="" />
				<img className={ s.erpText } src={ erpText1 } alt="" srcSet="" />
				<div className={ cs(s.formContainer, 'r-bg-white r-f-r r-mr-16') }>
					{
						!showSuccesIcon ? (
							<>
								<h3 className="r-fs-20 r-fw-600 r-ta-c r-mb-24">新用户注册</h3>
								<Form
									autoComplete="off"
									form={ form }
									name="normal_register"
									className="loginForm"
									initialValues={ { remember: false } }
									onFinish={ onFinish }
									onValuesChange={ onFormValueChange }
								>
									<VerifyCode type={ 1 } isCheckMobile showCaptcha />
									{
										isShowPwd
											? (
												<div>
													<Form.Item
														name="password"
														rules={ PASSWORD_RULES }
													>
														<Input
															style={ { height: 40 } }
															type="password"
															placeholder="请设置密码，8 - 16位密码"
														/>
													</Form.Item>
													<Form.Item
														name="passwordConfirm"
														dependencies={ ['password'] }
														rules={ [PASSWORD_RULES[0], ({ getFieldValue }) => ({
															validator(_, value) {
																if (!value || getFieldValue('password') === value) {
																	return Promise.resolve();
																}
																return Promise.reject(new Error('两次输入密码不一致'));
															},
														})] }
													>
														<Input
															style={ { height: 40 } }
															type="password"
															placeholder="请再次输入密码"
														/>
													</Form.Item>
													<Form.Item
														name="invitationCode"
													>
														<Input
															style={ { height: 40 } }
															placeholder="请填写邀请码，非必填"
														/>
													</Form.Item>
													<Form.Item >
														<Form.Item
															name="remember"
															rules={ [
																{
																	validator: (_, value) => (value ? Promise.resolve() : Promise.reject(new Error('请勾选协议'))),
																}
															] }
															valuePropName="checked"
															noStyle
														>
															<Checkbox className=""><span className="r-fs-12 r-c-333">阅读并同意</span></Checkbox>
														</Form.Item>
														<span className={ cs('r-fs-12 r-pointer') } onClick={ onShowProtocol } style={ { color: '#1890FF', marginLeft: -7 } }>
															软件服务协议
														</span>
													</Form.Item>
												</div>
											) : null
									}
									<Form.Item
										className="r-ta-c"
										noStyle
									>
										<Button loading={ loading } type="primary" htmlType="submit" className={ cs(s.registerButton, 'r-fs-16') }>
											注册
										</Button>
									</Form.Item>
									<Form.Item className="r-ta-r r-mt-15">
										<Link style={ { color: '#1890FF' } } to="/login">返回登录</Link>
									</Form.Item>
								</Form>
							</>
						) : (
							<div className={ cs(s.successWrapper) }>
								<div className={ s.successIcon }>
									<Icon size={ 42 } type="gou" style={ { color: 'white' } } />
								</div>
								<div className={ s.title }>账号注册成功</div>
								<Button type="primary" loading={ loading } onClick={ go2Login } className={ cs(s.loginButton) }>
									自动登录 ({seconds || 0}s)
								</Button>
							</div>
						)
					}
				</div>
			</div>
			<FooterLayout />
		</div>
	);
};
export default RegisterPage;
