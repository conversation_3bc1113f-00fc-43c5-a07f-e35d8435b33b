import React, { useEffect, useState } from 'react';
import cs from 'classnames';
import { Link, useHistory, useParams } from 'react-router-dom';
import { Button, Checkbox, Form, Input, Modal } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useCountDown } from "ahooks";
import { CheckCircleFilled, RightCircleOutlined } from '@ant-design/icons';
import Icon from "@/components/Icon";
import { ProtocalTemp } from '../Register/protocol';

import VerifyCode from '../../components/VerifyCode';
import s from './index.module.scss';
import { createScmRelationApi, getSecretKeyInfoApi, registerToFxdfApi, createScmRelationDistributorApi } from '@/apis/distribution';
import { IndexScmCreateScmRelationRequest, IndexScmGetSecretKeyInfoResponse } from '@/types/schemas/distribution';
import { checkMobileUserType<PERSON><PERSON>, user<PERSON><PERSON><PERSON><PERSON><PERSON>, user<PERSON>eg<PERSON><PERSON><PERSON> } from '@/apis/user';
import { local } from '@/libs/db';
import { USER_TYPE } from '@/constants';
import FooterLayout from '@/components-biz/layouts/FooterLayout';
import distributionImg from '@/assets/image/index/distribution.png';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import userStore from '@/stores/user';
import { getMd5Str } from '@/utils/util';
import { ProtocalTempB } from './protocol';
import message from '@/components/message';

const formItemLayout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 24 },
};
const formTailLayout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 24, offset: 6 },
};

enum typeProEnum {
	助手ERP版,
	快递助手版
}
const initBtnText = '申请分销合作';

const InviteDistribution = () => {
	const history = useHistory();
	const { code, inviteType = 'sale' }: {code: string, inviteType: 'sale' | 'supplier'} = useParams(); // code 秘钥 inviteType 邀请类型 供应商邀请分销商 sale 分销商邀请供应商 supplier
	const [loading, setLoading] = useState(false);
	const [extraNodeText, setExtraNodeText] = useState('');
	const [btnText, setBtnText] = useState(initBtnText);
	const [mobileUserType, setMobileUserType] = useState(-1);
	const [showSuccessRes, setShowSuccessRes] = useState(false);
	const [supplierInfo, setSupplierInfo] = useState<IndexScmGetSecretKeyInfoResponse['data']>({});
	const [loginInfoCache, setLoginInfoCache] = useState(null);
	const [targetDate, setTargetDate] = useState<number>();
	const [countdown, formattedRes] = useCountDown({
		targetDate,
		onEnd: () => { !loading && goLogin(); }
	});
	const [showErr, setShowErr] = useState(false);
	const [nextModalVisible, setNextModalVisible] = useState(false);
	const [checkB, setCheckB] = useState(true);
	const [checkA, setCheckA] = useState(true);
	const [type, setType] = useState(typeProEnum.助手ERP版); // 邀请类型 邀请免费供应商的需要额外判断
	const { seconds } = formattedRes;

	const [form] = useForm();
	const isFreeSupplierUser = inviteType == 'supplier';
	const isSaleUser = inviteType == 'sale';

	const getInit = async(secretKey) => {
		try {
			if (isSaleUser) {
				const res = await getSecretKeyInfoApi({ secretKey }); // 通过供应商密钥获取供应商信息
				setSupplierInfo({
					supplierUserId: res?.supplierUserId,
					supplierMobile: res?.supplierMobile,
				});
			} else if (isFreeSupplierUser) { // 分销商邀请免费供应商
				const res = await getSecretKeyInfoApi({ secretKey, type: 1, }); // 通过密钥获取分销商信息 分销商查询 1
				setSupplierInfo({
					saleUserId: res?.saleUserId,
					saleMobile: res?.saleMobile,
					saleName: res?.saleName,
					saleLinkMan: res?.saleLinkMan,
				});
			}
		} catch (error) {
			setShowErr(true);
		}
	};

	useEffect(() => {
	  if (code) getInit(code);
	}, [code, inviteType]);

	useEffect(() => {
		if (isFreeSupplierUser) {
			setType(typeProEnum.助手ERP版);
			setCheckA(false);
		}
	  }, [isFreeSupplierUser]);

	useEffect(() => {
		sendPoint(Pointer.申请分销合作_页面展现_展现次数, false);
	}, []);

	// 注册用户
	const registerUser = async(info) => {
		const val: any = {
			captcha: info.captcha,
			mobile: info.mobile,
			remember: info.remember,
			verifyCode: info.verifyCode,
			loginType: 2,
			userType: isSaleUser ? USER_TYPE.免费分销商 : USER_TYPE.免费版供应商用户,
			password: '123456Aa',

		};
		if (local.get('source')) {
			val.source = local.get('source');
			local.remove('source');
		}
		const res = await userRegisterApi(val); // 注册
		if (res.mobile) {
			setLoginInfoCache({
				...val,
				accoutName: res.mobile,
				loginType: 3,
			});
		}
		return res;
	};

	// 创建分销关系
	const onRelation = async(info) => {
		if (isSaleUser) {
			const params: IndexScmCreateScmRelationRequest = {
				supplierUserId: supplierInfo.supplierUserId,
				supplierMobile: supplierInfo.supplierMobile,
				verifyCode: info.verifyCode,
				saleName: info.saleName,
				saleLinkMan: info?.saleLinkMan || '',
				saleMobile: info.mobile,
			};
			await createScmRelationApi(params);
		} else if (isFreeSupplierUser) {
			const params: IndexScmCreateScmRelationRequest = {
				saleUserId: supplierInfo.saleUserId, // 分销商id
				saleMobile: supplierInfo.saleMobile, // 分销商手机
				saleName: supplierInfo.saleName, // 分销商名称
				saleLinkMan: supplierInfo?.saleLinkMan || '', // 分销商联系人
				verifyCode: info.verifyCode,
				// supplierUserId: '',
				supplierName: info.supplierName, // 供应商名字
				supplierMobile: info.mobile, // 供应商手机号
			};
			await createScmRelationDistributorApi(params);
		}

		setShowSuccessRes(true);
		setTargetDate(Date.now() + 1000 * 3);
	};

	const onFinish = async() => {
		setLoading(true);
		try {
			await handleTypeSure();
		} catch (error) {
			console.log('error: ', error);
		}
		setLoading(false);

	};

	const autoLogin = async() => {
		const res = await userLoginApi(loginInfoCache); // 登录
		if (res?.userId) {
			userStore.setUserInfo(res);
			setTimeout(() => {
				setLoading(false);
				location.href = `?userId=${getMd5Str(`${res.userId}_${res?.subUserId}`)}#/initialize`; // 初始化仓库、绑定店铺、生成货品档案
			}, 200);
		}
	};

	const goLogin = async() => {
		if (loginInfoCache) {
			try {
				await autoLogin();
				return;
			} catch (error) {
				console.log('error: ', error);
			}
		}
		history.push('/login?type=verifyCodeLogin');
	};

	const onFormValueChange = (changedValues: {}) => {
		if ('mobile' in changedValues) {
			setExtraNodeText('');
			setBtnText(initBtnText);
		}
	};

	// 用户协议及隐私政策
	const onShowProtocol = (e, type: typeProEnum) => {
		e.stopPropagation();
		const isB = typeProEnum.快递助手版 == type;
		const onOk = () => {
			isB ? setCheckB(true) : setCheckA(true);
		};
		Modal.confirm({
			icon: null,
			title: isB ? "快递助手-分销代发用户协议及隐私政策" : '快递助手ERP用户协议及隐私政策',
			centered: true,
			visible: true,
			width: 800,
			content: (
				<div style={ { height: 500, overflow: 'scroll' } }>
					{ isB ? ProtocalTempB() : ProtocalTemp()}
				</div>
			),
			okText: '同意',
			onOk,
		});
	};

	// 同意分销合作
	const handleTypeSure = async() => {
		const info = form.getFieldsValue();
		console.log('info: ', info);
		if (type == typeProEnum.快递助手版) {
			sendPoint(Pointer.申请分销合作_点击_选择的模式_快递助手版, false);
			if (!checkB) {
				message.warn('请先阅读并同意软件服务协议');
				return;
			}
			const res = await registerToFxdfApi({
				supplierUserId: supplierInfo.supplierUserId,
				supplierMobile: supplierInfo.supplierMobile,
				verifyCode: info.verifyCode,
				saleName: info.saleName,
				saleLinkMan: info?.saleLinkMan || '',
				saleMobile: info.mobile,
				secretKey: code,
			});
			window.open(res);
		} else if (type == typeProEnum.助手ERP版) {
			sendPoint(Pointer.申请分销合作_点击_选择的模式_助手ERP, false);
			if (!checkA) {
				message.warn('请先阅读并同意软件服务协议');
				return;
			}
			const mobileUserType = await checkMobileUserTypeApi({ mobile: info.mobile, captcha: info.captcha }); // 校验手机是否重复，并返回当前手机号的用户类型
			setMobileUserType(mobileUserType);

			// 填写的手机号是邀请人自己的，提示不能邀请自己
			if (isSaleUser && info?.mobile?.trim() == supplierInfo?.supplierMobile?.trim()) {
				message.warn('不能邀请自己');
				return;
			}
			// 填写的手机号是邀请人自己的，提示不能邀请自己
			if (isFreeSupplierUser && info?.mobile?.trim() == supplierInfo?.saleMobile?.trim()) {
				message.warn('不能邀请自己');
				return;
			}

			// 如果填写账号为常规版0
			if (mobileUserType == 0) {
				message.warn('该账户已是常规版用户，但未开通分销功能');
				return;
			}
			// 如果填写账号为常规版1
			if (mobileUserType == 1) {
				message.warn('该账户已是常规版用户，请走绑定流程');
				return;
			}
			// 如果填写账号为常规版3 且是常规版供应商邀请分销商
			if (mobileUserType == 3 && isSaleUser) {
				message.warn('该账户已是供应商版用户，供应商不可邀请供应商');
				return;
			}
			// 如果填写账号为常规版2 且是常规版分销商邀请供应商
			if (mobileUserType == 2 && isFreeSupplierUser) {
				message.warn('该账户已是分销商版用户，分销商不可邀请分销商');
				return;
			}

			// 已建立分销合作关系的后端会抛出
			// 若合作状态为“合作中”、“申请中”
			// 若合作状态为“已终止”、“已拒绝”

			if (mobileUserType == -1) {
				await registerUser(info);
			}
			await onRelation(info); // 无需注册的，直接绑定
			setNextModalVisible(false);
		}
	};

	const successNode = (
		<div className={ cs(s.successWrapper) }>
			<div className={ s.successIcon }>
				<Icon size={ 42 } type="gou" style={ { color: 'white' } } />
			</div>
			<div className={ s.title }>{isSaleUser ? '分销商账号' : '供应商账号'}{ mobileUserType == -1 ? '注册' : '绑定'}成功</div>
			<Button type="primary" onClick={ goLogin } className={ cs(s.loginButton) }>
				{ loginInfoCache ? '自动登录' : '自动跳转登录页'}  ({seconds || 0}s)
			</Button>
		</div>
	);

	// const onCheckedMobile = (ret) => {
	// setMobileUserType(ret);
	// if (ret == -1) {
	// 	setExtraNodeText('该手机号将自动注册分销版账号');
	// }
	// if ([USER_TYPE.默认, USER_TYPE.常规版].includes(ret)) {
	// 	setExtraNodeText('已是常规版注册账号');
	// 	setBtnText("已是常规版注册用户，不可申请");
	// }
	// if (ret == USER_TYPE.免费分销商) {
	// 	setExtraNodeText('已是分销商账号');
	// }
	// };

	const showNextModal = () => {
		sendPoint(Pointer.申请分销合作_点击_申请分销合作_点击次数, false);
		setNextModalVisible(true);
	};

	const registerForm = (
		<>
			{isSaleUser ? (
				<>
					<div className={ s.writeLabel }>—— 填写分销商申请资料 ——</div>
					<Form
						autoComplete="off"
						form={ form }
						name="invite_distribution"
						className={ s.inputForm }
						{ ...formItemLayout }
						initialValues={ { remember: false } }
						onFinish={ showNextModal }
						onValuesChange={ onFormValueChange }
					>
						<Form.Item
							name="saleName"
							label="分销商名称"
							rules={ [{ required: true, message: '请输入分销商名称' }] }
							required
						>
							<Input
								maxLength={ 32 }
								style={ { height: 40 } }
								placeholder="填写分销商名称"
							/>
						</Form.Item>
						<Form.Item
							label="联系人"
							required
							rules={ [{ required: true, message: '请输入联系人' }] }
							name="saleLinkMan"
						>
							<Input
								maxLength={ 24 }
								style={ { height: 40 } }
								placeholder="填写联系人"
							/>
						</Form.Item>
						<VerifyCode
							type={ 10 }
							// isCheckMobile
							// onCheckedMobile={ onCheckedMobile }
							pageForm="inviteDistribution"
							showCaptcha
							labelVisible
							pwdLabel=""
							extraNode={ extraNodeText ? <div className={ s.mobileTailText }><text>{extraNodeText}</text></div> : '' }
							formTailLayout={ formTailLayout }
							mobilePlaceholder="填写联系人手机号"
							mobilelabel="联系方式"
						/>
						<Form.Item
							className="r-ta-c"
						>
							<Button
								disabled={ btnText != initBtnText }
								type="primary"
								htmlType="submit"
								style={ { minWidth: 160 } }
							>
								{btnText}
							</Button>
						</Form.Item>
					</Form>
					<div className="r-ta-c">支持
						<a className="kdzs-link-text" href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/ga74ez8q2qo8kok9?singleDoc#" target="_blank" rel="noreferrer">ERP分销版</a
						>和<a className="kdzs-link-text" href="https://www.kdzs.com/df/index.html" target="_blank" rel="noreferrer">快递助手分销代发</a>
						两种模式，点击申请合作时可选
					</div>
				</>
			) : (
				<>
					<div className={ s.writeLabel }>—— 填写供应商申请资料 ——</div>
					<Form
						autoComplete="off"
						form={ form }
						name="invite_distribution"
						className={ s.inputForm }
						{ ...formItemLayout }
						initialValues={ { remember: false } }
						onFinish={ onFinish }
						onValuesChange={ onFormValueChange }
					>
						<Form.Item
							name="supplierName"
							label="供应商名称"
							rules={ [{ required: true, message: '请输入供应商名称' }] }
							required
						>
							<Input
								maxLength={ 32 }
								style={ { height: 40 } }
								placeholder="填写供应商名称"
							/>
						</Form.Item>
						<VerifyCode
							type={ 10 }
							// isCheckMobile
							// onCheckedMobile={ onCheckedMobile }
							pageForm="inviteDistribution"
							showCaptcha
							labelVisible
							pwdLabel=""
							extraNode={ extraNodeText ? <div className={ s.mobileTailText }><text>{extraNodeText}</text></div> : '' }
							formTailLayout={ formTailLayout }
							mobilePlaceholder="填写联系人手机号"
							mobilelabel="联系方式"
						/>
						<Form.Item
							wrapperCol={ { offset: 6, span: 18 } }
						>
							<div>
								<Checkbox checked={ checkA } onClick={ (e) => { e.stopPropagation(); setCheckA(pre => !pre); } } className=""><span className="r-fs-12 r-c-333">阅读并同意</span></Checkbox>
								<span className={ cs('r-fs-12 r-pointer') } onClick={ (e) => onShowProtocol(e, typeProEnum.助手ERP版) } style={ { color: '#1890FF', marginLeft: -7 } }>
									软件服务协议
								</span>
							</div>
						</Form.Item>

						<Form.Item
							wrapperCol={ { offset: 6, span: 18 } }
						>
							<Button
								type="primary"
								htmlType="submit"
								style={ { minWidth: 160 } }
							>
								同意分销合作
							</Button>
						</Form.Item>
					</Form>
				</>

			)}


		</>

	);

	const PNodeIcon = <CheckCircleFilled style={ { color: '#74c041', marginRight: '5px' } } />;
	return (
		<>
			<header className={ s.layoutHeader }>
				<div className={ s.layoutContent }>
					{
						isSaleUser ? (
							<Icon type="fenxiaoshanglogo" size={ 300 } style={ { color: '#FD8204' } } />
						) : (
							<Icon type="gongyingshanglogo" size={ 300 } style={ { color: '#FD8204' } } />
						)
					}
					<Link style={ { color: '#FD8204' } } to="/login">登录</Link>
				</div>
			</header>
			<div className={ cs(s.inviteContainer) }>
				<div className={ s.layoutContent }>
					<img className={ s.distributionImg } src={ distributionImg } alt="" srcSet="" />
					{
						supplierInfo.supplierMobile || supplierInfo.saleMobile ? (
							<div className={ cs(s.inviteWrap) }>
								{
									isSaleUser ? (
										<h3 className="r-fs-24 r-fw-600 r-ta-c">供应商（{supplierInfo.supplierMobile}）邀请您分销合作</h3>
									) : (
										<h3 className="r-fs-24 r-fw-600 r-ta-c">分销商（{supplierInfo.saleMobile}）邀请您分销合作</h3>
									)
								}

								{
									showSuccessRes ? successNode : registerForm
								}
							</div>
						) : showErr ? (
							<div className={ cs(s.inviteNoWrap) }>
								<div className={ s.imgContainer } />
								<h3 className="r-fs-20 r-fw-600 r-ta-c">该链接已失效，请联系{isSaleUser ? '供应商' : '分销商'}重新获取</h3>
							</div>
						) : ''
					}
				</div>
			</div>
			<Modal
				className={ s.nextModal }
				visible={ nextModalVisible }
				onCancel={ () => setNextModalVisible(false) }
				footer={ null }
				centered
				width="860px"
			>
				<div className={ s.backColor } />
				<div className="r-relative" >
					<div className={ s.topTitle }>选择您的<span style={ { color: '#F5821F' } }>代发系统</span></div>
					<div className={ cs("r-flex", s.tabWrap) }>
						<div className={ cs(s.tabItem, type == typeProEnum.助手ERP版 ? s.tabItemActive : '') } onClick={ () => setType(typeProEnum.助手ERP版) }>
							<div className={ s.tabTitle }>助手ERP版</div>
							<div className={ s.content }>
								<p>{PNodeIcon}支持商品管理及分销订单推送</p>
								<p>{PNodeIcon}系统免费，但需垫付平台接口费，推单后可退
									<a className={ s.detail } target="_blank" href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/dactw9x6m3nxol49?singleDoc# 《分销代发各平台接口费及返款情况》" rel="noreferrer" onClick={ (e) => { e.stopPropagation(); } }><RightCircleOutlined className="r-mr-4" />费用明细</a>
								</p>
							</div>
							<div>
								<Checkbox checked={ checkA } onClick={ (e) => { e.stopPropagation(); setCheckA(pre => !pre); } } className=""><span className="r-fs-12 r-c-333">阅读并同意</span></Checkbox>
								<span className={ cs('r-fs-12 r-pointer') } onClick={ (e) => onShowProtocol(e, typeProEnum.助手ERP版) } style={ { color: '#1890FF', marginLeft: -7 } }>
									软件服务协议
								</span>
							</div>
						</div>
						<div className={ cs(s.tabItem, type == typeProEnum.快递助手版 ? s.tabItemActive : '', 'r-ml-16') } onClick={ () => setType(typeProEnum.快递助手版) } >
							<div className={ s.tabTitle }>快递助手版</div>
							<div className={ s.content }>

								<p>{PNodeIcon}支持商品管理及分销订单推送</p>
								<p>{PNodeIcon}系统15-20元/月
									<a className={ s.detail } target="_blank" href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/wk6x0m/noa25c9dqc2vicbr#2fRF" rel="noreferrer" onClick={ (e) => { e.stopPropagation(); } } ><RightCircleOutlined className="r-mr-4" />费用明细</a>
								</p>
							</div>
							<div>
								<Checkbox checked={ checkB } onClick={ (e) => { e.stopPropagation(); setCheckB(pre => !pre); } } className=""><span className="r-fs-12 r-c-333">阅读并同意</span></Checkbox>
								<span className={ cs('r-fs-12 r-pointer') } onClick={ (e) => onShowProtocol(e, typeProEnum.快递助手版) } style={ { color: '#1890FF', marginLeft: -7 } }>
									软件服务协议
								</span>
							</div>
						</div>
					</div>
				</div>

				<div className="r-flex r-jc-c r-mt-40">
					<Button
						type="primary"
						loading={ loading }
						onClick={ onFinish }
						style={ { width: '120px' } }
					>确定
					</Button>
				</div>
			</Modal>
			<FooterLayout />
		</>
	);
};

export default InviteDistribution;
