/* eslint-disable react/jsx-no-bind */
import React, { useEffect, useState } from "react";
import cs from 'classnames';
import { Modal, Spin } from 'antd';
import { LoadingOutlined, RedoOutlined, ReloadOutlined } from "@ant-design/icons";
import s from './index.module.scss';
import { GetQrcode, saoUserLoginApi, userLoginApi } from "@/apis/user";
import { loginRedirect } from "@/utils/util";
import { dealAuthority } from "@/utils";
import message from "@/components/message";

const sceneType = {
	'小程序': '1',
	'公众号登录': '2',
	'公众号绑定': '3',
};

interface QRCodeLoginProps {
	type: string;
	changeTab?: Function;
  }
// 导出清除轮询的函数，可以从外部调用
export const clearPolling = (() => {
	let intervalId: NodeJS.Timeout | null = null;
	
	return {
		setIntervalId: (id: NodeJS.Timeout | null) => {
			intervalId = id;
		},
		clear: () => {
			if (intervalId) {
				clearInterval(intervalId);
				intervalId = null;
			}
		}
	};
})();

export const QRCodeLogin : React.FC<QRCodeLoginProps> = ({ type, changeTab }) => {
	const [imageUrl, setImageUrl] = useState<string>('');
	const [loading, setLoading] = useState<boolean>(true);
	const [isOutTime, setIsOutTime] = useState(false);
	
	// 轮询登录状态的函数
	const pollLoginStatus = async(interactionCode: string) => {
		try {
			const params = {
				loginType: 7,
				wxScanInteractionCode: interactionCode
			};
			const res = await saoUserLoginApi(params);
			if (res?.userId) {
				clearPolling.clear(); // 登录成功，停止轮询
				message.success('登录成功');
				
				// 处理权限
				let isContinue = true;
				isContinue = await dealAuthority();
				if (!isContinue) return;
				
				// 处理弹窗设置
				try {
					if (res?.popupSetting) {
						res.popupSetting = JSON.parse(res.popupSetting);
					}
				} catch (error) {
					console.log(error);
				}
				
				// 登录重定向
				loginRedirect(res);
			}
		} catch (error) {
			console.error('轮询绑定状态出错:', error);
			if (error.errorCode === 1112) {
				clearPolling.clear();
				 // 显示微信未绑定弹窗
				 Modal.confirm({
					title: '您的微信还未绑定"快递助手ERP"账号',
					content: '请您先通过账户密码或短信登录后，右上角头像处关联微信，登录后可通过微信公众号扫码验证',
					okText: '立即登录',
					cancelText: '取消',
					centered: true,
					className: 's-wx-bind-modal',
					width: 520,
					onOk: () => {
						// 跳转到登录页面或其他操作
						changeTab();
					}
				});
				return;
			}
			if (error.errorCode === 1110) {
				clearPolling.clear();
				setIsOutTime(true);
				return;
			}
		}
	};

	const fetchQRCode = async() => {
		try {
			setLoading(true);
			setIsOutTime(false);
			// 清除之前的轮询
			clearPolling.clear();
			
			const data = {
				type,
			};
			const response = await GetQrcode(data);
			console.log(response, 'response');
			if (response) {
				const xcxImgBase:string = `data:image/gif;base64,${response.miniAppImage}`;
				const gzhImgBase:string = response.wxMpUrl;
				console.log(xcxImgBase, gzhImgBase, 'responseresponse');
				setImageUrl(type == sceneType['小程序'] ? xcxImgBase : gzhImgBase);
				
				// 设置轮询
				if (response.interaction) {
					const id = setInterval(() => pollLoginStatus(response.interaction), 5000);
					clearPolling.setIntervalId(id);
				}
			} 
		} catch (error) {
			console.error('获取二维码出错:', error);
		} finally {
			setLoading(false);
		}
	};
	
	// 组件挂载时获取二维码
	useEffect(() => {
		fetchQRCode();
		// 组件卸载时清除轮询
		return () => clearPolling.clear();
	}, []);

	const onClickImg = () => {
		fetchQRCode();
	};

	return (
		<div style={ { width: '100%', overflow: 'auto', maxHeight: '80vh', position: 'relative' } }>
			<div style={ { cursor: 'pointer', position: 'relative' } } onClick={ onClickImg }>
				{
					imageUrl && loading ? (
						<Spin size="large" indicator={ <LoadingOutlined spin /> } >
							<img 
								src={ imageUrl } 
								alt="生成的图片" 
								style={ { 
									width: '160px',
									height: '160px', 
									display: 'block',
									margin: '64px auto 16px auto'
								} }
							/>
						</Spin>
					) : imageUrl ? (
						<div style={ { position: 'relative' } }>
							<img 
								src={ imageUrl } 
								alt="生成的图片" 
								style={ { 
									width: '160px',
									height: '160px', 
									display: 'block',
									margin: '64px auto 16px auto',
									opacity: isOutTime ? '0.1' : '1'
								} }
							/>
							{isOutTime && (
								<div 
									style={ { 
										position: 'absolute', 
										top: '0px', 
										left: '50%', 
										transform: 'translateX(-50%)',
										width: '160px', 
										height: '160px', 
										background: 'rgba(0, 0, 0, 0.1)', 
										display: 'flex',
										flexDirection: 'column',
										justifyContent: 'center',
										alignItems: 'center',
										color: '#fff',
										fontSize: '14px',
										textAlign: 'center'
									} }
								>
									<ReloadOutlined 
										style={ { 
											color: '#666666',
											fontSize: '24px'
										} }
									/>
								</div>
							)}
						</div>
					) : (
						<div
							style={ { 
								width: '160px',
								height: '160px', 
								display: 'block',
								margin: '64px auto 16px auto',
								textAlign: 'center'
							} }
						>
							<Spin style={ { lineHeight: '100px' } } size="large" indicator={ <LoadingOutlined spin /> } />
						</div>
					)
				}
			</div>
			<div className={ s.qrcodeText }>
				<p>使用微信扫一扫</p>
				<p>快递助手ERP
					{type == sceneType['小程序'] ? '小程序' : '公众号'}
					登录
				</p>
			</div>
		</div>

	);
};
