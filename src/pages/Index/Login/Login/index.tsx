/* eslint-disable react/jsx-no-bind */
import React, { useEffect, useState } from "react";
import cs from 'classnames';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lt<PERSON>, Spin } from 'antd';
import { Link } from "react-router-dom";
import { useBoolean } from "ahooks";
import { divide, size } from "lodash";
import QRCode from 'qrcode.react'; // 引入二维码组件，需要安装: npm install qrcode.react --save
import { LoadingOutlined } from "@ant-design/icons";
import VercodeLogin from "../components/VercodeLogin";
import PwdLogin from "../components/PwdLogin";
import s from './index.module.scss';
import FooterLayout from "@/components-biz/layouts/FooterLayout";
import HeaderLayout from "@/components-biz/layouts/HeaderLayout";
import Icon from '@/components/Icon';
import history from "@/utils/history";
import { getStrParam } from "@/utils";
import platformLogo from '../../../../assets/image/index/image_logo.png';
import erpText1 from '../../../../assets/image/index/text.png';
import Pointer from "@/utils/pointTrack/constants";
import { local } from "@/libs/db";
import { PLAT_ALI, PLAT_C2M, PLAT_DW, PLAT_FXG, PLAT_JD, PLAT_KS, PLAT_PDD, PLAT_SPH, PLAT_TB, PLAT_XHS, PLAT_YZ, PLAT_KTT } from "@/constants";
import { CacheKeys } from "@/libs/memorizeFn/cacheCommon";
import { switchProtocolToHTTPS } from "@/utils/util";
import Erweima from '@/assets/image/loginewm.png';
import Diannao from '@/assets/image/logindiannao.png';
import { GetQrcode } from "@/apis/user";
import { QRCodeLogin } from "./codeLogin";

const { TabPane } = Tabs;
const sceneType = {
	'小程序': '1',
	'公众号登录': '2',
	'公众号绑定': '3',
};
const sessionWhiteKeyList = [
	"erp_userInfo", "qnquerystring", "syncAllShopList",
	...Object.values(CacheKeys)
];
const BindErpAccountModal = () => {
	const [visible, { setFalse, setTrue }] = useBoolean(true);
	return (
		<>
			<Modal
				title="您的店铺还未绑定“快递助手ERP”账号"
				width={ 450 }
				footer={ null }
				visible={ visible }
				onCancel={ setFalse }
				maskClosable={ false }
				centered
			>
				<p>如果您已有ERP账号，登录可自动绑定该店铺；</p>
				<p>如果您还没有ERP账号，请先注册后进行登录。</p>
				<div className="r-mt-20 r-ta-r">
					<Button type="primary" className="r-mr-20" onClick={ () => history.push('/register') }>
						免费注册
					</Button>
					<Button onClick={ setFalse }>
						立即登录
					</Button>
				</div>
			</Modal>
		</>
	);
};

export const loginPlatformParams = [
	{
		plat: '淘宝',
		point: Pointer.登录页_授权登录_淘宝登录,
		href: 'https://erp.kuaidizs.cn/login/authLogin?ptType=tb',
		type: 'taobao',
		platform: PLAT_TB,
	}, {
		plat: '拼多多',
		point: Pointer.登录页_授权登录_拼多多登录,
		href: 'https://erp.kuaidizs.cn/login/authLogin?ptType=pdd',
		type: 'pinduoduo',
		platform: PLAT_PDD,
	}, {
		plat: '抖店',
		point: Pointer.登录页_授权登录_抖店登录,
		href: 'https://erp.kuaidizs.cn/login/authLogin?ptType=fxg',
		type: 'douyin',
		platform: PLAT_FXG,
	}, {
		plat: '1688',
		href: 'https://erp.kuaidizs.cn/login/authLogin?ptType=ali',
		type: 'a-1688',
		point: Pointer.登录_授权登录_1688登录入口,
		platform: PLAT_ALI,
	}, {
		plat: '快手',
		href: 'http://erp.kuaidizs.cn/login/authLogin?ptType=ksxd',
		type: 'kuaishouxiaodian',
		point: Pointer.登录_授权登录_快手登录入口,
		platform: PLAT_KS,
	}, {
		plat: '京东',
		href: 'http://erp.kuaidizs.cn/login/authLogin?ptType=jd',
		type: 'jingdong ',
		point: Pointer.登录_授权登录_京东登录入口,
		platform: PLAT_JD,
	}, {
		plat: '视频号',
		href: 'https://store.weixin.qq.com/shop',
		type: 'shipinhao',
		point: Pointer.登录_授权登录_视频号小店登录入口,
		platform: PLAT_SPH,
	}, {
		plat: '小红书',
		href: 'http://erp.kuaidizs.cn/login/authLogin?ptType=xhs',
		type: 'xiaohongshu',
		point: Pointer.登录_授权登录_视频号小店登录入口,
		platform: PLAT_XHS,
	}, {
		plat: '淘工厂',
		href: 'http://erp.kuaidizs.cn/login/authLogin?ptType=c2m',
		type: 'taogongchang',
		point: Pointer.登录_授权登录_视频号小店登录入口,
		platform: PLAT_C2M,
	}, {
		plat: '有赞',
		href: 'https://erp.kuaidizs.cn/login/authLogin?ptType=yz',
		type: 'youzan',
		point: Pointer.登录_授权登录_有赞登录入口,
		platform: PLAT_YZ,
	}, {
		plat: '得物',
		href: 'https://erp.kuaidizs.cn/login/authLogin?ptType=dw',
		type: 'dewu',
		point: Pointer.登录_授权登录_得物登录入口,
		platform: PLAT_DW,
	}, {
		plat: '快团团',
		href: 'https://erp.kuaidizs.cn/login/authLogin?ptType=ktt',
		type: 'kuaituantuan',
		point: Pointer.登录_授权登录_快团团登录入口,
		platform: PLAT_KTT,
	}
];

const LoginPage: React.FC = () => {
	const [loginType, setLoginType] = useState<string>('pwdLogin');
	const [isShowBindErp, { setFalse, setTrue }] = useBoolean(false);
	const [info, setInfo] = useState<string>(null);
	const [isQRCodeMode, setIsQRCodeMode] = useBoolean(false);
	const [showKey, setShowKey] = useState('1');
	
	function changeTab(key: string) {
		setLoginType(key);
	}
	
	const clearSession = async() => {
		import('@/utils/memo').then(res => {
			sessionWhiteKeyList.forEach(key => res.memoClean(key));
		});
	};
	
	useEffect(() => {
		clearSession();
		switchProtocolToHTTPS();
		const info = getStrParam('info');
		if (info) {
			setTrue();
			setInfo(info);
		}
		if (history.location.search.includes('type=verifyCodeLogin')) {
			setLoginType('verifyCodeLogin');
		}
		let arr = history.location.search.split('?source=');
		if (arr[1]) {
			local.set('source', arr[1]);
		}
	}, []);

	const ewmTb = () => {
		return (
			<div className={ s.qrcodeToggle } onClick={ () => { setIsQRCodeMode.toggle(); setShowKey('1'); } }>
				{/* 二维码图标按钮 */}
				<img 
					src={ isQRCodeMode ? Diannao : Erweima } 
					alt={ isQRCodeMode ? "电脑登录" : "扫码登录" }
					width={ 64 }
					height={ 64 }
				/>
			</div>
		);
	};
	return (
		<>
			<HeaderLayout />
			<div className={ cs(s.loginContainer, '') }>
				<img className={ s.platformLogo } src={ platformLogo } alt="" srcSet="" />
				<img className={ s.erpText } src={ erpText1 } alt="" srcSet="" />
				<Tooltip visible={ isShowBindErp } placement="topLeft" title="登录ERP账号，自动绑定您的店铺">
					<div className={ cs(s.loginPannel, 'r-bg-white') }>
						
						{
							isQRCodeMode ? (
								ewmTb()
							) : (
								<Tooltip
									placement="left"
									getPopupContainer={ trigger => trigger.parentElement }
									overlayClassName={ s['custom-tooltip-orange'] }
									overlayInnerStyle={ {
										backgroundColor: '#FFF2F0',
										color: '#FD8204'
									} }
									title="微信扫码登录"
								>
									{ewmTb()}
								</Tooltip>
							)
						}
					
						{isQRCodeMode ? (
							<>
								<Tabs defaultActiveKey="1" activeKey={ showKey } onChange={ (e) => setShowKey(e) }>
									<TabPane tab="小程序登录" key="1">
										{
											showKey == '1' ? (
												<QRCodeLogin type={ sceneType['小程序'] } changeTab={ setIsQRCodeMode.toggle } />
											) : null
										}
									</TabPane>
									<TabPane tab="公众号登录" key="2" >
										{
											showKey == '2' ? (
												<QRCodeLogin type={ sceneType['公众号登录'] } changeTab={ setIsQRCodeMode.toggle } />
											) : null
										}
									</TabPane>
								</Tabs>
							</>
						) : (
							<>
								<Tabs activeKey={ loginType } onChange={ changeTab }>
									<TabPane tab="密码登录" key="pwdLogin">
										<PwdLogin loginType={ loginType } info={ info } />
									</TabPane>
									<TabPane tab="验证码登录" key="verifyCodeLogin">
										<VercodeLogin loginType={ loginType } info={ info } />
									</TabPane>
								</Tabs>
								<div className={ cs(s.authorizeLogin, 'r-fs-14 r-pt-5', isShowBindErp ? 'r-hide' : '') }>
									{
										loginPlatformParams.map((item) => {
											return (
												<div style={ { width: '105px' } } key={ item.type }>
													<Icon svg size={ 15 } type={ item.type } />
													<a
														href={ item.href }
														className="r-ml-4 r-mr-5"
														rel="noreferrer"
														data-point={ item.point || '' }
														data-point-tag={ false }
													>{item.plat}登录
													</a>
												</div>
											);
										})
									}
								</div>
								<div className={ cs(s.go2other, 'r-ta-r r-fs-14 r-mt-20') }>
									<Link to="/register">免费注册</Link>
									<Link to="/forgetPwd" className="r-ml-15">忘记密码</Link>
								</div>
							</>
						)}
					</div>
				</Tooltip>
			</div>
			<FooterLayout />
			{
				isShowBindErp ? <BindErpAccountModal /> : null
			}
		</>

	);
};
export default LoginPage;
