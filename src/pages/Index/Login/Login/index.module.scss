.loginContainer {
	width: 100%;
	min-height: 600px;
	min-width: 1340px;
	height: calc(100vh - 140px);
	background-image: url("../../../../assets/image/index/bg.png");
	overflow: hidden;
	background-size: 100% 100%;
	justify-content: space-around;
	.platformLogo {
		float: left;
		margin-left: 16.19%;
		width: 16.2%;
		margin-top: 210px;
	}
	@media screen and (max-width: 1440px) {
		.platformLogo {
			margin-left: 8%;
			min-width: 280px;
		}
		.erpText {
			min-width: 400px;
			margin-left: 3%;
		}
	}
	@media screen and (max-width: 1540px) {
		.platformLogo {
			margin-left: 9%;
		}
	}

	.erpText {
		float: left;
		margin-left: 3%;
		width: 23%;
		margin-top: 238px;
	}
	.loginPannel {
		margin-left: 5%;
		float: left;
		margin-top: 100px;
		box-shadow: 0px 6px 16px 1px rgba(250, 173, 20, 0.16);
		border-radius: 8px;
		padding: 24px;
		width: 376px;
		min-height: 385px;
		position: relative;
		
		.verCodeInp {
			width: 200px;
		}
		
		:global {
			.ant-tabs-tab .ant-tabs-tab-btn {
				font-size: 15px;
			}
		}
	}
	.loginFormButton {
		width: 250px;
	}
	.authorizeLogin {
		margin-top: -10px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
		a {
			color: rgba(0, 0, 0, 0.65);
		}
	}
	.go2other {
		a {
			color: #1890ff;
			letter-spacing: 0;
			line-height: 22px;
			font-weight: 400;
		}
	}
	:global {
		.ant-form-item-control {
			min-height: 56.38px !important;
		}
		.ant-form-item {
			margin-bottom: 0;
		}
	}
}

// .authLink {
// 	display: inline-block;
// 	width: 80px;
// }

.qrcodeToggle {
  position: absolute;
  top: 0px;
  right: 0px;
  cursor: pointer;
  z-index: 10;
  overflow: hidden;
  width: 64px;
  height: 64px;
  transition: all 0.6s ease-in-out;
  
  img {
    display: block;
    width: 64px;
    height: 64px;
    position: absolute;
    top: 0;
    right: 0;
    clip-path: polygon(100% 0, 0 0, 100% 100%); /* 只显示右上角三角形部分 */
    transition: all 0.6s ease-in-out;
  }
  
  &:hover {
    /* 移除宽高变化，保持原始大小 */
    
    img {
      /* 保持clip-path，不显示完整图标 */
      opacity: 0.8; /* 仅添加轻微的悬停效果 */
    }
  }
}

.qrcodeLoginContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  min-height: 350px;
}

.qrcodeBox {
  padding: 15px;
  background: #fff;
  border: 1px dashed #ddd;
  border-radius: 4px;
  margin-bottom: 20px;
}

.qrcodeText {
  text-align: center;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 58px;
  p {
    margin: 5px 0;
    font-size: 14px;
	line-height: 22px;
  }
}

.custom-tooltip-orange{
	:global {
		.ant-tooltip-arrow-content {
			background-color: #FFF2F0 !important;
		}
	}
}