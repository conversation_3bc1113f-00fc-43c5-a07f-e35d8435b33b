import React, { useState } from "react";
import { Form, Button, Input, Modal } from 'antd';
import { useBoolean, useCountDown } from "ahooks";
import cs from 'classnames';
import _ from 'lodash';
import { IndexPlatformShopSendSmsCodeApi, IndexPlatformShopEditPlatformShopApi, OperateShopApi, IndexVirtualPlatformShopGetPlatformShopsApi, IndexVirtualPlatformShopEditPlatformShopApi } from "@/apis/user";
import s from './index.module.scss';
import userStore from "@/stores/user";
import { TradeUserOperateShopApi } from "@/apis/trade";
import { ItemDeleteShopItemsApi } from "@/apis/warehouse/system";
import message from "@/components/message";
import { PLAT_SPH } from "@/constants";
import { scmTradeRegretAllApi } from "@/apis/distribution";
import distributionStore from "@/stores/distribution";
import { distributionType } from "@/pages/Distribution/utils";
import { shopTypeEnum } from "../../Shops/components/addShop";


interface DelShopModalInf {
    visible: boolean;
    onClose: () => void;
}
const Reg = /(\d{3})\d*(\d{4})/;
const DelShopModal: React.FC<DelShopModalInf> = (props) => {
	const { visible, onClose } = props;
	const [form] = Form.useForm();
	const [yzmTxt, setYzmTxt] = useState('获取验证码');
	const [loading, { setFalse, setTrue }] = useBoolean(false);
	const [targetDate, setTargetDate] = useState<number>();

	const [countdown, formattedRes] = useCountDown({
		targetDate,
		onEnd: () => { setYzmTxt('重新获取'); }
	});
	const { seconds } = formattedRes;

	const pStyle = {
		color: '#f00'
	};

	/**
    * 获取短信验证码
    * @returns
    */
	const getSmsCode = async() => {
		const params = {
			mobile: userStore.userInfo.mobile,
			accountName: userStore.curShopCardData.operatorUserName,
			platformShopName: userStore.curShopCardData.sellerNick
		};
		const ret = IndexPlatformShopSendSmsCodeApi(params);
		if (ret) {
			message.success('发送成功');
			setTargetDate(Date.now() + 1000 * 60);
		}
	};

	const regretDistribution = async() => {
		// 常规版和分销商版撤回推送订单
		if (!userStore.isFreeSupplierAccount) {
			await distributionStore.initSupplierList();
			const supplier = distributionStore.supplierList?.filter(supplier => supplier.status == distributionType.合作中);
			if (supplier?.length) {
				await scmTradeRegretAllApi({
					supplierUserId: supplier?.[0]?.supplierUserId, // TODO 为什么是第一个合作中的供应商
					saleUserId: userStore?.userInfo.userId,
				});
			}
		}
	};

	const onOk = async() => {
		try {
			const { verifyCode } = await form.validateFields();
			const { id, sellerId, platform, sellerNick } = userStore.curShopCardData;
			const { shopType } = userStore;
			const param = {
				verifyCode,
				id,
				isDelete: true,
				sellerId,
			};
			setTrue();
			let ret = null;
			if (shopType === shopTypeEnum.普通店铺) {
				await ItemDeleteShopItemsApi({ platform, sellerId, verifyCode });
				ret = await IndexPlatformShopEditPlatformShopApi(param);
			} else {
				ret = await IndexVirtualPlatformShopEditPlatformShopApi(param);
			}
			setFalse();
			if (ret) {
				message.success('删除成功');
				// 操作店铺 暂停
				const param1 = {
					opType: 4,
					sellerId: String(sellerId),
					platform,
					sellerNick,
				};
				if (shopType === shopTypeEnum.普通店铺) {
					OperateShopApi(param1);
					userStore.updateShopList();
					TradeUserOperateShopApi(param1);
				} else {
					userStore.updateEbillShopList({});
				}
				if (platform == PLAT_SPH) {
					const res = await window.printAPI?.sphUserTemplateDelete(sellerId);
					console.log('res: ', res);
				}
				// regretDistribution();
			}
			onClose();
			form.resetFields();
		} catch (error) {
			console.log('Failed:', error);
		}
	};

	return (
		<div>
			<Modal
				centered
				className="deleteModal"
				title="删除店铺验证"
				visible={ visible }
				onOk={ onOk }
				okText="确认删除"
				onCancel={ () => { onClose(); form.resetFields(); } }
				loading={ loading }
				maskClosable={ false }
			>
				<p className={ cs(s.cred, 'r-mb-20') }>点击向主账号手机{userStore?.userInfo?.mobile?.replace(Reg, "$1***$2")}发送验证码</p>
				<Form
					form={ form }
				>
					<Form.Item
						label="验证码"
					>
						<Form.Item
							name="verifyCode"
							rules={ [{ required: true, message: '请输入验证码', len: 6, type: 'string' }] }
							noStyle
						>
							<Input
								maxLength={ 6 }
								style={ { width: 250 } }
								type="text"
								placeholder="请输入验证码"
							/>
						</Form.Item>
						<Button className="r-ml-15" disabled={ countdown > 0 } onClick={ _.throttle(() => getSmsCode(), 2000) }>{countdown > 0 ? `${seconds}后重新获取` : yzmTxt}</Button>
					</Form.Item>
				</Form>
			</Modal>
		</div>
	);
};

export default DelShopModal;
