import dayjs from "dayjs";

export enum apiName {
    系统设置 = "SettingSaveSystemSettingApi",
    用户设置 = "SettingSaveUserSettingApi",
    更新合单 = "updateMergeConfigApi",
    忽略配置 = "IndexSettingUpdateIgnoreConfigApi",
}

const settingMap = {
	[apiName.系统设置]: [
		'platformGift', 'sysItempropertyManageStockFlag', 'sysItempropertyManageStockFlagTime',
		"systemLastShipTime",
		"changeCommodityInfoSign", "expireRemindDays",
		"flagTrade", "forceInterceptWhenNoGoodsLink",
		"groupStockNumOutWay", "insertItemAutomaticRelation",
		"inventoryDeduct", "inventoryDeductTime",
		"markTrade", "orderPreOccupiedStock",
		"printTagSet", "stockSyncConfig",
		"syncModifyProductCode", "combinationFormula", "stockZero",
		"weightingAverageCost", "itemCustomAttribute",
		"flashItemOuterIdFlag", 'autoGenerateSysSkuBarCode', "autoSplitTradeByCombSku"
	],
	[apiName.用户设置]: [
		"abnormalAddressCheck", "abnormalExpressArriveCheck",
		"abnormalRefundCheck", "abnormalStockWarnCheck",
		"autoSearch", "isShowBranches",
		"openAbnormalCheck", "openExportSecurityVerification",
		"remindShip", "syncOrder", "weightUnit", "longStayWithoutCheck",
		"extraSetting"
	],
	[apiName.更新合单]: ["beforeMergeOrder", "mergeOrder"],
	[apiName.忽略配置]: ["ignoreFlag"]
};

const excludeArr = ["mergeOrderLastChangeTime", "flashItemOuterIdFlagTimer"];
// 需要对比的时间
const timeCompareArr = ["inventoryDeductTime"];


export const compareObj = (newObj = {}, oldObj = {}) => {
	const diffObj:any = {};
	Object.keys(newObj).forEach((item) => {
		let diffItem = null;
		// 时间比较 将两个时间转为时间戳比较
		if (timeCompareArr.includes(item)) {
			if (dayjs(newObj[item] || "").valueOf() !== dayjs(oldObj[item] || "").valueOf()) {
				diffItem = item;
			}
		} else if (!excludeArr.includes(item) && newObj[item] !== oldObj[item]) {
			diffItem = item;
		}
		if (!diffItem) return;
		for (let settingItem in settingMap) {
			if (settingMap[settingItem].includes(item)) {
				if (!diffObj[settingItem]) {
					diffObj[settingItem] = {};
				}
				diffObj[settingItem][item] = newObj[item];
			}
		}
	});
	return diffObj;
};


