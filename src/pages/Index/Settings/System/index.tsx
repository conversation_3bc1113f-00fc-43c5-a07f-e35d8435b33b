import React, { useEffect, useRef, useState } from 'react';
import cs from 'classnames';
import { Button, Form, Tooltip, Select, Modal, InputNumber } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { observer } from 'mobx-react-lite';
import { ExclamationCircleFilled, QuestionCircleOutlined } from '@ant-design/icons';
import s from './index.module.scss';
import './index.scss';
import EnumSelect from '@/components/Select/EnumSelect/EnumSelect';
import { syncModifyProductCode,
	inventoryDeduct,
	expireRemindDays,
	isShowBranches,
	weightUnit,
	exportSecurityVerification,
	autoSearch,
	groupStockNumOutWay,
	autoMergeConfig,
	orderPreOccupiedStock,
	tradeGoodsInfoUpdateConfig,
	combinationFormula,
	longStayWithoutChecEnum,
	openOrCloseConfig,
	weightingAverageCostConfig,
	verticalMenu, zeroVerticalMenu, factoryVerticalMenu, remindShip,
	packageMaterialStockConfig,
	autoGenerateSysSkuBarCodeConfig
} from './constants';
import { IndexSettingUpdateMergeMaxSizeApi, SettingSaveSystemSettingApi, SettingSaveUserSettingApi } from '@/apis/user';
import { setSystemSettingInf, SettingGetSystemSettingResponse, SettingGetUserSettingResponse, setUserSettingInf } from '@/types/schemas/user';
import NoInventoryDeductSetModal from '../components/NoInventoryDeductSetModal';
import userStore from '@/stores/user';
import Pointer from "@/utils/pointTrack/constants";
import TradeCheckConfigModal from "../components/TradeCheckConfigModal";
import MarkEmptyModal from '../components/MarkEmptyModal';
import SendTimeModal from '../components/SendTimeModal';
import ModifyAddressModal from '../components/ModifyAddressModal';
import message from "@/components/message";
import { IndexSettingSelectIgnoreConfigApi, IndexSettingUpdateIgnoreConfigApi } from "@/apis/setting/smartExpress";
import DatePicker from '@/components/DatePicker';
import { getUrlData } from '@/pages/Trade/utils';
import { changeOccupiedStockSettingApi, updateMergeConfigApi } from '@/apis/warehouse/syncstock';
import event from '@/libs/event';
import { TaskProgressEnum } from '@/components-biz/StockOccupation/StockProgressModal';
import TradeGoodsInfoUpdateModal from '../components/TradeGoodsInfoUpdateModal';
import { lowVersionLock } from '@/components-biz/LowVersionControlModal';
import { PageNameControlEnum } from '@/components-biz/LowVersionControlModal/constant';
import { apiName, compareObj } from './util';
import TagSettingModal, { ITagSettingModalProps } from '../components/TagSettingModal';
import LongStayWithoutCheckModal from '../components/LongStayWithoutCheck';
import PlatformGiftModal from '../components/PlatformGiftModal';
import NumOcrRuleModal from '../components/NumOcrRuleModal';
import WeightingAverageCostModal from './components/WeightingAverageCostModal';
import CustomerConfigSettingModal from '../components/CustomerConfigSettingModal';
import AutoMergeTradesRuleModal from './components/AutoMergeTradesRuleModal';
import Icon from "@/components/Icon";
import sendPoint from '@/utils/pointTrack/sendPoint';
import BarcodeRuleModal from './components/BarcodeRuleModal';
import SplitSettingModal from './components/SplitSettingModal';


const SystemPage: React.FC = () => {
	const [form] = Form.useForm();
	const [autoMergeOrderForm] = Form.useForm();
	const [markEmptyModal, setMarkEmptyModal] = useState(false);
	const [platformGiftModal, setPlatformGiftModal] = useState(false);
	const [numOcrRuleModal, setNumOcrRuleModal] = useState(false);
	const [sendTimeModal, setSendTimeModal] = useState(false);
	const [modifyAddressModal, setModifyAddressModal] = useState(false);
	const [isShowInventoryDeductTime, setIsShowInventoryDeductTime] = useState<number>(0);
	// const [isOpenAbnormalCheck, setIsOpenAbnormalCheck] = useState<number>(0);
	const [submitLoading, setSubmitLoading] = useState(false);
	const [settingConf, setSettingConf] = useState<SettingGetSystemSettingResponse['data'] & SettingGetUserSettingResponse['data']>(null);
	const [tradeCheckConfigModalVisible, setTradeCheckConfigModalVisible] = useState<boolean>(false);
	const [tradeGoodsInfoUpdateVisible, setTradeGoodsInfoUpdateVisible] = useState<boolean>(false);
	const [longStayWithoutCheckVisible, setLongStayWithoutCheckVisible] = useState<boolean>(false);
	const [tradeUpdateConfig, setTradeUpdateConfig] = useState({});
	const [showTagSettingModal, setShowTagSettingModal] = useState(false);
	const [printTagSet, setPrintTagSet] = useState<ITagSettingModalProps["printTagSet"]>({
		tagTemplateSwitch: '0',
		tagTemplate: null,
		printTagNumSwitch: '0',
		printTagNum: '1',
		printTagGift: "1",
	});
	const [occupiedLock, setOccupiedLock] = useState(false);
	const [oldSettingObj, setOldSettingObj] = useState({});
	const [autoMergeTradesRuleModalVisible, setAutoMergeTradesRuleModalVisible] = useState(false); // 自动喝的那规格设置

	const [settingPerm, setSettingPerm] = useState(() => (
		{
			userPerm: true,
			settingPerm: true
		}
	));
	const [noInventoryDeductSetModalVisible, setNoInventoryDeductSetModalVisible] = useState(false);
	const [stockSyncConfigValue, setStockSyncConfigValue] = useState(null);
	const [introItemName, setIntroItemName] = useState('');
	const { systemSetting, isShowZeroStockVersion, isFreeSupplierAccount, isDistributorAccount } = userStore;
	const [splitSettingModalVisible, setSplitSettingModalVisible] = useState(false);

	useEffect(() => {
		const fn = async() => {
			const res = await lowVersionLock(PageNameControlEnum.预占库存);
			setOccupiedLock(!!res);
		};
		fn();
	}, []);

	// 处理空包订单的配置
	const handleIgnoreConfig = () => {
		IndexSettingSelectIgnoreConfigApi({}).then(res => {
			const _obj = { ignoreFlag: res ? !!res.ignoreFlag : false };
			form.setFieldsValue({ ..._obj });
			setOldSettingObj((prev) => ({ ...prev, ..._obj }));
		});
	};
	useEffect(() => {
		window.scrollTo(0, 0);
		userStore.getUserInfo().then(ret => {
			const { userId, authorityDetail } = ret;
			const _settingPerm = cloneDeep(settingPerm);
			// 进行权限控制判断
			if (authorityDetail) {
				let authorityDetailArr = JSON.parse(authorityDetail || '[]');
				// 用户个性化设置
				_settingPerm.userPerm = authorityDetailArr.includes('36');
				// 系统设置
				_settingPerm.settingPerm = authorityDetailArr.includes('35');
				setSettingPerm(_settingPerm);
			}

			const P1 = _settingPerm.settingPerm ? userStore.getSystemSetting() : {};
			const P2 = _settingPerm.userPerm ? userStore.getUserSetting() : {};

			Promise.all([P1, P2]).then((res: [SettingGetSystemSettingResponse['data'], SettingGetUserSettingResponse['data']]) => {
				if (res[1]?.weightUnit == undefined) {
					res[1].weightUnit = weightUnit.显示g;
				}
				if (res[1]?.openAbnormalCheck !== 0) {
					res[1].openAbnormalCheck = 1;
				}
				setIsShowInventoryDeductTime(res[0]?.inventoryDeduct);
				console.log(res[0], res[1], 'res[0]');
				const _obj = {
					...res[0],
					...res[1],
					'tradeGoodsInfoUpdateConfig': tradeGoodsInfoUpdateConfig.开启,
					'weightingAverageCost': res[0]?.extendInfo?.weightingAverageCost, // getSystemSetting 的 extendInfo
					'autoGenerateSysSkuBarCode': res[0]?.extendInfo?.autoGenerateSysSkuBarCode || autoGenerateSysSkuBarCodeConfig.不开启,
					'autoSplitTradeByCombSku': res[0]?.extendInfo?.autoSplitTradeByCombSku, // getSystemSetting 的 extendInfo
				};
				form.setFieldsValue({
					..._obj
				});
				setOldSettingObj((prev) => ({
					...prev,
					..._obj
				}));

				// * 大促降级 readOnlyFields是一样的
				res[1].readOnlyFields?.forEach(key => {
					form.setFields([{
						name: key,
						errors: [res[1].readOnlyDesc || ''],
					}]);
				});
				setSettingConf({
					...res[0],
					...res[1],
				});
				setTradeUpdateConfig({
					changeCommodityInfoSign: res[0]?.changeCommodityInfoSign,
				});
				try {
					if (!res[0]?.printTagSet) {
						setPrintTagSet({
							tagTemplateSwitch: '0',
							tagTemplate: null,
							printTagNumSwitch: '0',
							printTagNum: '1',
							printTagGift: "1",
						});
					} else {
						setPrintTagSet({ printTagGift: "1", ...JSON.parse(res[0]?.printTagSet) });
					}
				} catch (error) {
					console.error(error);
				}

				let { introName } = getUrlData('', 'introName');
				console.log(introName, 'introNameintroName');
				if (introName) {
					setIntroItemName(introName);
					setTimeout(() => {
						if (['printTagSet', 'openAbnormalCheck', 'goodsCustomSet', 'tradeGoodsInfoUpdateConfig'].includes(introName)) {
							document.querySelector(`#${introName}`)?.scrollIntoView({ behavior: 'smooth', block: 'center' });
						} else {
							form.scrollToField(introName, {
								block: 'center'
							});
						}
					}, 350);
				}
			});
		});
		handleIgnoreConfig();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);
	const [verticalShowMenu, setVerticalMenu] = useState(verticalMenu);
	const getVerticalMenu = () => {
		   // 零库存版本
		if (isShowZeroStockVersion) {
			return zeroVerticalMenu;
		}
		// 免费供应商版本
		if (isFreeSupplierAccount) {
			return factoryVerticalMenu;
		}
		// 分销商版本
		if (isDistributorAccount) {
			return factoryVerticalMenu;
		}
		// 默认菜单项目
		return verticalMenu; // 或者返回默认的菜单项
	  };

	  useEffect(() => {
		const menu = getVerticalMenu();
		setVerticalMenu(menu);
	  }, []);

	useEffect(() => {
		// setAdParams().then((res) => {
		// 	window.Ads.prototype.getAdShow('system');
		// });
	}, []);

	useEffect(() => {
		if (systemSetting) {
			setStockSyncConfigValue(systemSetting.stockSyncConfig);
		}
	}, [systemSetting]);

	const confirmPromise = () => {
		return new Promise<void>((resolve, reject) => {
			Modal.confirm({
				title: '提示',
				icon: <ExclamationCircleFilled />,
				content: '您对库存占用方式进行了调整，系统将自动将您近30天的订单库存重新分配，这可能要需要一段时间处理订单，是否确认提交？',
				onOk() {
					console.log('OK');
					resolve();
				},
				onCancel() {
					console.log('Cancel');
					reject();
				},
			});
		});
	};

	/**
	 * 保存配置项
	 * @param val
	 */
	interface SaveConfig extends setSystemSettingInf, setUserSettingInf { }
	const onSave = async(val: SaveConfig) => {
		// debugger
		const apiObj = {
			[apiName.系统设置]: SettingSaveSystemSettingApi,
			[apiName.用户设置]: SettingSaveUserSettingApi,
			[apiName.更新合单]: updateMergeConfigApi,
			[apiName.忽略配置]: IndexSettingUpdateIgnoreConfigApi
		};
		// 订单同步自动标记为"空包订单" 埋点
		const ignoreFlag = form.getFieldValue('ignoreFlag');
		sendPoint(ignoreFlag ? Pointer.设置_系统设置_自动标记为空单_开启 : Pointer.设置_系统设置_自动标记为空单_关闭);
		const { inventoryDeductTime, inventoryDeduct } = val;
		if (inventoryDeduct === 1 && !inventoryDeductTime) {
			message.warn('请设置扣减库存时间');
			return false;
		}
		const isChangedStock = val.orderPreOccupiedStock && settingConf?.orderPreOccupiedStock != val.orderPreOccupiedStock;
		if (isChangedStock) {
			await confirmPromise();
		}
		const isChangedGroup = settingConf?.groupStockNumOutWay != val?.groupStockNumOutWay;
		val = { ...val };
		const diffObj = compareObj(val, oldSettingObj);

		console.log('val', val, oldSettingObj);

		const reqArr = [];
		if (!Object.keys(diffObj).length) {
			message.warning("本次操作无变更项");
			return;
		}
		console.log('diffObj:::', diffObj);
		if (diffObj?.[apiName.更新合单]?.mergeOrder) {
			diffObj[apiName.更新合单].beforeMergeOrder = settingConf.mergeOrder;
		}

		// 系统设置变更包含 weightingAverageCost 需要转化一下
		if (diffObj?.[apiName.系统设置] && Object.keys(diffObj?.[apiName.系统设置]).includes('weightingAverageCost')) {
			diffObj[apiName.系统设置]['extendInfo'] = {
				weightingAverageCost: diffObj?.[apiName.系统设置]['weightingAverageCost'],
			};
			delete diffObj[apiName.系统设置]['weightingAverageCost'];
		}

		if ([0, 1].includes(diffObj?.[apiName.系统设置]?.sysItempropertyManageStockFlag)) {
			diffObj[apiName.系统设置].extendInfo = {
				...diffObj[apiName.系统设置].extendInfo,
				sysItempropertyManageStockFlag: diffObj?.[apiName.系统设置]?.sysItempropertyManageStockFlag
			};
			delete diffObj[apiName.系统设置].sysItempropertyManageStockFlag;
		}
		// 系统设置变更包含 autoGenerateSysSkuBarCode 需要转化一下
		if (diffObj?.[apiName.系统设置] && Object.keys(diffObj?.[apiName.系统设置]).includes('autoGenerateSysSkuBarCode')) {
			diffObj[apiName.系统设置]['extendInfo'] = {
				...diffObj[apiName.系统设置]['extendInfo'],
				autoGenerateSysSkuBarCode: diffObj?.[apiName.系统设置]['autoGenerateSysSkuBarCode'],
			};
			delete diffObj[apiName.系统设置]['autoGenerateSysSkuBarCode'];
		}
		if (diffObj?.[apiName.系统设置] && Object.keys(diffObj?.[apiName.系统设置]).includes('autoSplitTradeByCombSku')) {
			diffObj[apiName.系统设置]['extendInfo'] = {
				autoSplitTradeByCombSku: diffObj?.[apiName.系统设置]['autoSplitTradeByCombSku'],
			};
			delete diffObj[apiName.系统设置]['autoSplitTradeByCombSku'];
		}

		if ([0, 1].includes(diffObj?.[apiName.系统设置]?.itemCustomAttribute)) {
			diffObj[apiName.系统设置].extendInfo = {
				...diffObj[apiName.系统设置].extendInfo,
				itemCustomAttribute: diffObj?.[apiName.系统设置]?.itemCustomAttribute
			};
			delete diffObj[apiName.系统设置].itemCustomAttribute;
		}
		if ([0, 1].includes(diffObj?.[apiName.系统设置]?.flashItemOuterIdFlag)) {
			diffObj[apiName.系统设置].extendInfo = {
				...diffObj[apiName.系统设置].extendInfo,
				flashItemOuterIdFlag: diffObj?.[apiName.系统设置]?.flashItemOuterIdFlag
			};
			delete diffObj[apiName.系统设置].flashItemOuterIdFlag;
		}
		for (let item in diffObj) {
			reqArr.push(apiObj[item](diffObj[item]));
		}
		setSubmitLoading(true);
		Promise.all(reqArr).then(async(res) => {
			// if (res[1] && res[0] && res[2]) {
			const allSettled = res.every((item) => (item === true || item === null));
			if (allSettled) {
				message.success('保存成功');
				setSubmitLoading(false);
				let currentSetting = { ...userStore.userSetting };
				for (let k in currentSetting) {
					if (val[k] !== undefined) currentSetting[k] = val[k];
				}
				userStore.setUserSetting(currentSetting);

				await userStore.getSystemSetting(true);
				currentSetting = { ...userStore.systemSetting }; // 最新的系统设置
				userStore.setSystemSetting(currentSetting);
				setSettingConf(() => currentSetting);
				setOldSettingObj((prev) => ({
					...prev,
					...val,
					...currentSetting
				}));
				userStore.getUserInfo(true);
				if (isChangedStock) {
					await changeOccupiedStockSettingApi({ oldOrderPreOccupiedStock: settingConf?.orderPreOccupiedStock, newOrderPreOccupiedStock: val.orderPreOccupiedStock });
					event.emit('stock_progress_modal', { type: TaskProgressEnum.系统保存 });
				} else if (isChangedGroup) {
					event.emit('stock_progress_modal', { type: TaskProgressEnum.组合货品计算 });
				}
			} else {
				setSubmitLoading(false);
				message.success('保存失败');
			}
		}).catch(() => {
			message.success('保存失败');
			setSubmitLoading(false);
		});
	};

	const onWeightUnithange = () => {
		sendPoint(Pointer.设置_系统设置_系统内商品重量单位);
	};

	const onInventoryDeductChange = (val: number) => {
		setIsShowInventoryDeductTime(val);
		if (val === 1) sendPoint(Pointer.设置_系统设置_订单库存扣减_开启);
	};

	const onAutoSearchChange = (val:number) => {
		if (val === 0)sendPoint(Pointer.设置_系统设置_开启自动查询_关闭);
		else if (val === 1)sendPoint(Pointer.设置_系统设置_开启自动查询_开启);
	};

	const onExportSecurityVerificationChange = (val:number) => {
		if (val === 0) sendPoint(Pointer.系统_系统设置_数据导出安全校验_子账户校验);
		if (val === 1) sendPoint(Pointer.系统_系统设置_数据导出安全校验_主账户校验);
	};

	const handleClickCombinationGoodsTypeOutofStockType = (val:number) => {
		if (val === 0)sendPoint(Pointer.设置_系统设置_组合货品出库方式_子商品);
		else if (val === 1)sendPoint(Pointer.设置_系统设置_组合货品出库方式_组合商品);
	};

	// const onOpenAbnormalCheck = (val: number) => {
	// 	setIsOpenAbnormalCheck(val);
	// };

	const stockSyncConfigChange = (val:number) => {
		sendPoint(Pointer.设置_自动同步库存);
		setStockSyncConfigValue(val);
	};

	const isDisabeldByReadOnlyFields = (fields) => {
		return settingConf?.readOnlyFields?.includes(fields);
	};

	const [activeName, setActiveName] = useState('item-1');
	// let clickStatus = useReactive({
	// 	status: false
	// })
	const contentRefs = useRef({}); // 存储内容区块的引用
	useEffect(() => {
		contentRefs.current = verticalShowMenu.reduce((acc, item) => {
			console.log(acc, item, 'acc');
			acc[item.value] = React.createRef();
			return acc;
		}, {});
	}, [verticalShowMenu]);

	//   useEffect(() => {
	// 	window.addEventListener('scroll', () => {
	// 		setTimeout(() => {
	// 			clickStatus.status = false
	// 		}, 1000); // 延迟100毫秒后认为滚动结束
	// 	});
	//   }, [])
	const handleVerticalClick = (item) => {
		setActiveName(item.value);
		console.log('item.value', item);

		// clickStatus.status = true
		// 找到内容区域中对应的部分
		const element = document.getElementById(item.value);
		if (element) {
			// 计算元素相对于视口的顶部位置，然后减去偏移量
			const offsetTop = element.getBoundingClientRect().top + window.scrollY - 92;
			// 使用window.scrollTo实现带偏移量的平滑滚动
			window.scrollTo({
				top: offsetTop,
				left: 0,
				behavior: 'smooth'
			});
		}
	};

	const findFirstVisibleElement = (refs:any) => {
		for (const ref of Object.values(refs.current)) {
		  if (ref?.current && isElementAtTop(ref?.current)) {
				return ref?.current;
		  }
		}
		return null;
	  };

	  const isElementAtTop = (element) => {
		return element.getBoundingClientRect().top >= 0;
	  };

	  useEffect(() => {
		let scrollTimeout;

		const onScroll = () => {
		  clearTimeout(scrollTimeout);
		  scrollTimeout = setTimeout(() => {
			// 滚动停止后，检查哪个区块在顶部并激活它
				const firstVisibleElement = findFirstVisibleElement(contentRefs);
				if (firstVisibleElement) {
			  setActiveName(firstVisibleElement.getAttribute('data-id'));
				}
		  }, 100);
		};

		window.addEventListener('scroll', onScroll);

		return () => {
		  window.removeEventListener('scroll', onScroll);
		  clearTimeout(scrollTimeout);
		};
	  }, [contentRefs]);

	const renderFastCom = () => {
		let rootDom = document.querySelector('#root');
		const toBottom = () => {
			window.scrollTo({
				top: document.body.scrollHeight,
				behavior: 'smooth',
			});
		};
		if (rootDom.clientHeight > window.innerHeight) {
			return (
				<div className="fast-com">
					<div
						className="r-mb-8 r-pointer"
						onClick={ () => {
							window.scrollTo({
								top: 0,
								behavior: 'smooth',
							});
						} }
					>
						<Icon style={ { fontSize: 30 } } type="daodingbu" />
					</div>
					<div className="r-pointer" onClick={ () => { toBottom(); } } >
						<Icon style={ { fontSize: 30 } } type="daodibu" />
					</div>
				</div>
			);
		}
		return '';
	};

	// 自动合单规则设置
	const handleAutoMergeTradesRuleModalCancel = () => {
		setAutoMergeTradesRuleModalVisible(false);
	};

	const handleAutoMergeOrder = () => {
		setAutoMergeTradesRuleModalVisible(true);
	};

	// 自动合单规则设置保存
	const handleAutoMergeTradesRuleModalOk = (data:any) => {
		console.log('%c [ 自动合单规则设置保存 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', data);
		setSettingConf(prev => ({
			...prev,
			...data,
		}));
		setAutoMergeTradesRuleModalVisible(false);
	};

	return (
		<div className={ cs(s.systemSettingContainer,) }>
			<div style={ { display: 'flex' } }>
				<div
					className={ s.verticalContainer }
				>
					{
						verticalShowMenu.map((item, index) => {
							return (
								<div
									onClick={ () => handleVerticalClick(item) }
									key={ item.value }
									className={ cs(s.normalActive, (activeName) == item.value ? s.activeVertical : '') }
								>
									{item.label}
								</div>
							);
						})
					}
				</div>
				<Form
					style={ { flex: 1 } }
					form={ form }
					name="systemAndUserSetting"
					onFinish={ onSave }
					className="systemSettingForm"
					onFieldsChange={ (_, allFields) => {
						onSave(form.getFieldsValue());
					} }
				>
					{
						settingPerm.userPerm ? (
							<div
								className={ cs(s.systemWrapper, 'r-bg-white') }
								style={ { marginTop: 8 } }
								id="item-1"
								ref={ contentRefs.current['item-1'] }
								data-id="item-1"
							>
								<h3 className={ cs(s.title, 'r-hairline--bottom r-fs-16') }>
									基础设置
								</h3>

								<div className={ s.content }>
									<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
										<div className="r-pt-12">
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>店铺到期提醒</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>店铺快到期提醒的提前天数，将会短信提醒主账号手机号</p>
										</div>
										<Form.Item
											className="r-mt-20"
											name="expireRemindDays"
											style={ { width: 200 } }
										>
											<EnumSelect disabled={ isDisabeldByReadOnlyFields("expireRemindDays") } enum={ expireRemindDays } allowClear={ false } />
										</Form.Item>
									</div>


									<div className={ cs("r-hairline--bottom", "r-flex", "r-jc-sb", "r-pb-12") }>
										<div className="r-pt-12">
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>长时间停留未操作提醒设置</h3>
											<div className={ cs(s.contentDesc, 'r-fs-14') }>
												商家停留页面长时间未操作时系统给予提醒，避免订单信息未及时更新造成资损
											</div>
										</div>
										<Form.Item
											className={ cs("r-mt-20") }
											name="longStayWithoutCheck"
											style={ { width: 200 } }
										>
											<EnumSelect
												enum={ longStayWithoutChecEnum }
												onChange={ (e:longStayWithoutChecEnum) => {
													if (e === longStayWithoutChecEnum.不开启) {
														sendPoint(Pointer.设置_长时间停留提醒设置_不开启);
													} else {
														sendPoint(Pointer.设置_长时间停留提醒设置_开启);
													}
												} }
												allowClear={ false }
											/>
										</Form.Item>
										<Button
											type="link"
											onClick={ () => { !isDisabeldByReadOnlyFields('longStayWithoutCheck') && setLongStayWithoutCheckVisible(true); } }
											style={ { bottom: '-3px', right: '151px' } }
											className="r-fs-12 r-absolute r-pd-0"
										>提醒设置
										</Button>
									</div>

								</div>
							</div>
						) : null
					}
					{
						settingPerm.userPerm ? (
							<div
								className={ cs(s.systemWrapper, 'r-bg-white') }
								style={ { marginTop: 8 } }
								id="item-2"
								ref={ contentRefs.current['item-2'] }
								data-id="item-2"
							>
								<h3 className={ cs(s.title, 'r-hairline--bottom r-fs-16') }>
									安全设置
								</h3>

								<div className={ s.content }>
									<div className="r-hairline--bottom r-flex r-jc-sb r-pb-12">
										<div className="r-pt-12">
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>数据导出安全校验</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>数据导出时需要手机号校验，可设为主账号手机或子账号手机</p>
										</div>
										<Form.Item
											className={ cs("r-mt-20", introItemName === 'openExportSecurityVerification' ? s.leftArrow : '') }
											name="openExportSecurityVerification"
											style={ { width: 200 } }
										>
											<EnumSelect
												disabled={ isDisabeldByReadOnlyFields("openExportSecurityVerification") || !!userStore?.userInfo?.subUserId }
												enum={ exportSecurityVerification }
												allowClear={ false }
												onChange={ onExportSecurityVerificationChange }
											/>
										</Form.Item>
									</div>

								</div>
							</div>
						) : null
					}
					{
						settingPerm.settingPerm ? (
							<div
								className={ cs(s.systemWrapper, 'r-bg-white') }
								id="item-3"
								ref={ contentRefs.current['item-3'] }
								data-id="item-3"
							>
								<h3 className={ cs(s.title, 'r-hairline--bottom r-fs-16') }>订单同步</h3>
								<div className={ s.content }>
									<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
										<div>
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>订单自动合单规则设置</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>开启后，平台订单同步到系统后，查询时将按合单结果展示；自动合单规则修改后对新订单自动生效（历史订单需重新同步）;（自动合单暂不支持得物平台订单）</p>
										</div>
										<div>
											<div className={ cs(s.stockSyncConfig) }>
												<Tooltip
													title={
														(
															<div>
																<p>说明：</p>
																<p>1、开启后合单后，开启时间之后的订单自动合单，之前待发货订单不自动合单。</p>
																<p>2、关闭后，关闭时间之后的订单不自动合单，之前已合并订单不自动拆单修改请点击更新待发货订单</p>
															</div>
														)
													}
													autoAdjustOverflow
												>
													<ExclamationCircleFilled style={ { fontSize: '20px', color: '#FD8204' } } />
												</Tooltip>
												<Form.Item
													className="r-ml-8"
													name="mergeOrder"
													style={ { width: 200, marginBottom: 0 } }
												>
													<EnumSelect disabled={ isDisabeldByReadOnlyFields("mergeOrder") } enum={ autoMergeConfig } allowClear={ false } />
												</Form.Item>
											</div>
											<div className={ cs(s.orderMergeConfigBottom) }>
												<div style={ { opacity: 0.46, fontSize: 14, textAlign: "right" } }>
													{settingConf && settingConf.mergeOrder === 2 ? "开启时间：" : "关闭时间："}
													{settingConf && settingConf.mergeOrderLastChangeTime ? settingConf.mergeOrderLastChangeTime : ''}
												</div>
												<Button
													type="link"
													id="mergeOrder"
													className={ cs("r-pd-0", introItemName === 'mergeOrder' ? s.leftArrow : '') }
													style={ { fontSize: 12, textAlign: "left", padding: 0, margin: 0, lineHeight: "16px", paddingLeft: "18px", height: "16px" } }
													onClick={ () => {
														handleAutoMergeOrder();
													} }
												>自动合单规则设置
												</Button>
											</div>

										</div>
									</div>

									<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
										<div>
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>订单同步平台赠品设置</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>开启后，将根据平台赠品设置规则进行逻辑处理，规则修改后对新订单自动生效。</p>
										</div>
										<div className={ cs(s.rightItemContainer) }>
											<Form.Item
												className="r-ml-8"
												name="platformGift"
												style={ { width: 200, marginBottom: 0 } }
											>
												<Select disabled={ isDisabeldByReadOnlyFields("platformGift") }>
													<Select.Option value={ 0 }>关闭</Select.Option>
													<Select.Option value={ 1 }>开启</Select.Option>
												</Select>
											</Form.Item>
											{
												settingConf?.platformGiftLastChangeTime ? (
													<div style={ { opacity: 0.46, fontSize: 14, textAlign: "right" } }>
														{settingConf?.platformGift == 1 ? "开启时间：" : "关闭时间："}
														{settingConf?.platformGiftLastChangeTime ? dayjs(settingConf?.platformGiftLastChangeTime).format("YYYY-MM-DD HH:mm:ss") : ''}
													</div>
												) : null
											}
											<div style={ { width: "100%", display: "flex", justifyContent: "flex-start" } }>
												<Button
													type="link"
													onClick={ () => {
														!isDisabeldByReadOnlyFields('platformGift') && setPlatformGiftModal(true);
													} }
													style={ { textAlign: "left", padding: 0, margin: 0, lineHeight: "16px", paddingLeft: "8px", height: "16px" } }
													className="r-fs-12"
												>平台赠品规则设置
												</Button>
											</div>
										</div>

									</div>

									<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
										<div>
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>订单同步自动标记为"空包订单"</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>开启后，平台订单同步到系统后将自动标记空单。标记后可前往订单打印页面手动取消或标记</p>
										</div>
										<div>
											<Form.Item
												className="r-ml-8"
												name="ignoreFlag"
												style={ { width: 200, marginBottom: 0 } }
											>
												<Select disabled={ isDisabeldByReadOnlyFields("ignoreFlag") }>
													<Select.Option value={ false }>关闭</Select.Option>
													<Select.Option value>开启</Select.Option>
												</Select>
											</Form.Item>
											<Button
												type="link"
												onClick={ () => {
													!isDisabeldByReadOnlyFields('ignoreFlag') && setMarkEmptyModal(true);
												} }
												style={ { bottom: '-3px' } }
												className="r-fs-12 r-absolute r-pd-0 r-ml-8"
											>标记规则设置
											</Button>
										</div>
									</div>

									<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
										<div>
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>订单首次同步时支持根据商家编码公式自动换商品（可拆分平台组合装商品）</h3>
											<p className={ cs(s.errorMsg) }>(规格)商家编码标准公式为：商家编码1*数量+商家编码2*数量。比如A1+A2*2+B1，订单首次同步后会自动拆分子商品：1个A1、2个A2、1个B1。</p>
										</div>
										<div className="r-flex r-ai-c">
											<Tooltip
												overlayInnerStyle={ { width: 400 } }
												placement="bottom"
												title={
													(
														<div className="r-ml-8">
															<p>说明：</p>
															<p>1）被拆分的商品、替换的商品均需提前在平台维护标准的商家编码（取规格编码，无规格商品取商家编码）</p>
															<p>2）拆分后的商品需为同店铺内的正常有效商品</p>
															<p>3）启用该功能后，不要采用线上换货，防止商品发错</p>
															<p>4）自动换商品后无法撤销，开启前注意确认</p>
															<p>5）自动换商品可能有几秒钟延迟，若发现未拆分，可尝试重新查询</p>
														</div>
													)
												}
											>
												<ExclamationCircleFilled style={ { fontSize: '20px', color: '#FD8204' } } />
											</Tooltip>
											<Form.Item
												className="r-ml-8"
												name="combinationFormula"
												style={ { width: 200, marginBottom: 0 } }
											>
												<EnumSelect
													onChange={ e => {
														sendPoint(e == 1 ? Pointer["根据商家编码公式自动换商品：不开启_->_开启"] : Pointer["根据商家编码公式自动换商品：开启_->_不开启"]);
													} }
													disabled={ isDisabeldByReadOnlyFields("combinationFormula") }
													enum={ combinationFormula }
													allowClear={ false }
												/>
											</Form.Item>
										</div>
									</div>
									{
										!isShowZeroStockVersion
											? (
												<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
													<div>
														<h3 className={ cs(s.contentTitle, 'r-fs-14') }>订单首次同步时组合货品自动拆分为子货品</h3>
														<p className={ cs(s.contentDesc, 'r-fs-14') }>拆分操作不可逆，请确认后开启。</p>
														<p className={ cs(s.errorMsg) }>商品关联货品为组合货品，订单首次同步时自动拆分为子货品。比如商品A 关联组合货品B(a*2+b*3), 同步后自动拆分为子商品：a*2、b*3</p>
													</div>
													<div className="r-flex">
														<div className="r-mt-8">
															<Tooltip
																overlayInnerStyle={ { width: 400 } }
																placement="bottom"
																title={
																	(
																		<div className="r-ml-8">
																			<p>说明：</p>
																			<p>1）仅待付款/待发货商品支持拆分，被拆分商品需关联本地组合货品</p>
																			<p>2）拆分后组合货品下子货品变更不会更新订单拆分结果</p>
																			<p>3）订单自动拆分处理将在「空单自动标记」、「系统赠品」处理之前请仔细核对</p>
																			<p>4）组合商品拆操作不可撤销，开启前请注意确认</p>
																			<p>5）自动拆分品可能有几秒钟延迟，若发现未拆分，可尝试重新查询</p>
																		</div>
																	)
																}
															>
																<ExclamationCircleFilled style={ { fontSize: '20px', color: '#FD8204' } } />
															</Tooltip>
														</div>
														<div>
															<Form.Item
																className="r-ml-8"
																name="autoSplitTradeByCombSku"
																style={ { width: 200, marginBottom: 0 } }
															>
																<Select
																	disabled={ isDisabeldByReadOnlyFields("autoSplitTradeByCombSku") }
																	onChange={ e => {
																		if (e === 1) {
																			sendPoint(Pointer.系统设置_组合拆分子货品_开启);
																		}
																	} }
																>
																	<Select.Option value={ 0 }>关闭</Select.Option>
																	<Select.Option value={ 1 }>开启</Select.Option>
																</Select>
															</Form.Item>
															<div>
																<div style={ { opacity: 0.46, fontSize: 14, textAlign: "right" } }>
																	{settingConf?.autoSplitTradeByCombSkuTime ? (settingConf?.autoSplitTradeByCombSku === combinationFormula.开启 ? "开启时间：" : "关闭时间：") : ""}
																	{settingConf?.autoSplitTradeByCombSkuTime ? dayjs(settingConf?.autoSplitTradeByCombSkuTime).format("YYYY-MM-DD HH:mm:ss") : ''}
																</div>
															</div>
															<Button
																type="link"
																style={ { bottom: '0px' } }
																onClick={ () => {
																	!isDisabeldByReadOnlyFields('autoSplitTradeByCombSku') && setSplitSettingModalVisible(true);
																} }
																className="r-fs-12 r-absolute r-pd-0 r-ml-8"
															>拆分设置
															</Button>
														</div>
													</div>
												</div>
											)
											: null
									}

									<>
										{
											isShowZeroStockVersion || isDistributorAccount ? null : (
												<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
													<div>
														<h3 className={ cs(s.contentTitle, 'r-fs-14') }>闪购商品订单同步时，自动绑定本地货品（适用于直播场景）</h3>
														<p className={ cs(s.contentDesc, 'r-fs-14') }>开启后，平台订单同步到系统后，针对未绑定货品订单支持按 @@ 格式提取商家编码，并根据编码自动绑定本地货品</p>
														<p className={ cs(s.errorMsg) }>案例：商品名称为"夏季新品白色L@@sku0008"，则编码识别为"sku0008"，并与相同货品规格编码的货品自动关联绑定</p>
													</div>
													<div>
														<div className={ cs(s.rightItemContainer) }>
															<Form.Item
																className="r-ml-20"
																name="flashItemOuterIdFlag"
																style={ { width: 200, marginBottom: 0 } }
															>
																<EnumSelect
																	enum={ combinationFormula }
																	allowClear={ false }
																/>
															</Form.Item>
															<div className={ cs(s.orderMergeConfigBottom) }>
																<div style={ { opacity: 0.46, fontSize: 14, textAlign: "right" } }>
																	{settingConf?.flashItemOuterIdFlagTime ? (settingConf?.flashItemOuterIdFlag === combinationFormula.开启 ? "开启时间：" : "关闭时间：") : ""}
																	{settingConf?.flashItemOuterIdFlagTime ? dayjs(settingConf?.flashItemOuterIdFlagTime).format("YYYY-MM-DD HH:mm:ss") : ''}
																</div>
															</div>

															<div style={ { width: "100%", display: "flex", justifyContent: "flex-start" } }>
																<Button
																	type="link"
																	onClick={ () => {
																		!isDisabeldByReadOnlyFields('flashItemOuterIdFlag') && setNumOcrRuleModal(true);
																	} }
																	style={ { textAlign: "left", padding: 0, margin: 0, lineHeight: "16px", paddingLeft: "18px", height: "16px" } }
																	className="r-fs-12"
																>编码识别规则设置
																</Button>
															</div>
														</div>
													</div>
												</div>
											)
										}
									</>
								</div>
							</div>
						) : null
					}
					{
						settingPerm.settingPerm ? (
							<div
								className={ cs(s.systemWrapper, 'r-bg-white') }
								ref={ contentRefs.current['item-4'] }
								data-id="item-4"
								id="item-4"
							>
								<h3 className={ cs(s.title, 'r-hairline--bottom r-fs-16') }>订单基础</h3>
								<div className={ s.content }>

									<div className="r-hairline--bottom r-flex r-jc-sb r-pb-12">
										<div className="r-pt-12">
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>开启自动查询</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>进入订单页面后系统将自动查询订单</p>
										</div>
										<Form.Item
											className="r-mt-20"
											name="autoSearch"
											style={ { width: 200 } }
										>
											<EnumSelect disabled={ isDisabeldByReadOnlyFields("autoSearch") } enum={ autoSearch } allowClear={ false } onChange={ onAutoSearchChange } />
										</Form.Item>
									</div>

									<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12" style={ { display: 'flex', alignItems: 'center' } }>
										<div>
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>
												平台买家自助修改地址设置
											</h3>
											<p className={ cs(s.contentDesc) }>平台开启买家自动修改地址的后，系统将根据此配置进行自动改地址审核。</p>
										</div>
										<div>
											<Button
												type="link"
												style={ { paddingLeft: 0 } }
												onClick={ () => {
													setModifyAddressModal(true);
												} }
											>订单改地址审核规则
											</Button>
										</div>
									</div>

									<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
										<div>
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>系统剩余发货时间</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>开启后，订单剩余发货时间将根据配置进行取值，未设置的平台店铺仍取平台剩余发货时间。</p>
										</div>
										<div>
											<Form.Item
												className="r-ml-8"
												name="systemLastShipTime"
												style={ { width: 200, marginBottom: 0 } }
											>
												<Select disabled={ isDisabeldByReadOnlyFields("systemLastShipTime") }>
													<Select.Option value={ 0 }>关闭</Select.Option>
													<Select.Option value={ 1 }>开启</Select.Option>
												</Select>
											</Form.Item>
											<Button
												type="link"
												onClick={ () => {
													!isDisabeldByReadOnlyFields('systemLastShipTime') && setSendTimeModal(true);
												} }
												style={ { bottom: '-3px' } }
												className="r-fs-12 r-absolute r-pd-0 r-ml-8"
											>规则设置
											</Button>
										</div>

									</div>

									<div className={ cs("r-hairline--bottom", "r-flex", "r-jc-sb", "r-pb-12") } id="tradeGoodsInfoUpdateConfig">
										<div className="r-pt-12">
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>订单商品信息更新</h3>
											<div className={ cs(s.contentDesc, 'r-fs-14') }>
												系统商品信息变更时，支持以下类型订单商品信息自动更新
											</div>
										</div>
										<Form.Item
											className={ cs("r-mt-20") }
											name="tradeGoodsInfoUpdateConfig"
											style={ { width: 200 } }
										>
											<EnumSelect disabled enum={ tradeGoodsInfoUpdateConfig } allowClear={ false } />
										</Form.Item>
										<Button
											type="link"
											onClick={ () => { !isDisabeldByReadOnlyFields('tradeGoodsInfoUpdateConfig') && setTradeGoodsInfoUpdateVisible(true); } }
											style={ { bottom: '-3px', right: '151px' } }
											className="r-fs-12 r-absolute r-pd-0"
										>前往配置
										</Button>
									</div>

								</div>
							</div>
						) : null
					}
					{
						settingPerm.settingPerm ? (
							<div
								className={ cs(s.systemWrapper, 'r-bg-white') }
								ref={ contentRefs.current['item-5'] }
								data-id="item-5"
								style={ { marginTop: 8 } }
								id="item-5"
							>
								<h3 className={ cs(s.title, 'r-hairline--bottom r-fs-16') }>打印发货</h3>

								<div className={ s.content }>
									<div className="r-hairline--bottom r-flex r-jc-sb r-pb-12">
										<div className="r-pt-12">
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>打印后是否提醒发货</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>打印后是否提醒发货，默认为打印后提醒</p>
										</div>
										<Form.Item
											className="r-mt-20"
											name="remindShip"
											style={ { width: 200 } }
										>
											<EnumSelect disabled={ isDisabeldByReadOnlyFields("remindShip") } enum={ remindShip } allowClear={ false } />
										</Form.Item>
									</div>

									<div className={ cs("r-hairline--bottom", "r-flex", "r-jc-sb", "r-pb-12", "r-pt-12", "r-ai-c") }>
										<div>
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>订单异常检测</h3>
											<div className={ cs(s.contentDesc, s.errorMsg, 'r-fs-14') }>
												打印前、发货前系统将根据配置进行订单校验，提示异常订单，减少错发损失
											</div>
										</div>
										<Button
											type="link"
											id="openAbnormalCheck"
											className={ cs("r-pd-0", introItemName === 'openAbnormalCheck' ? s.leftArrow : '') }
											onClick={ () => {
												setTradeCheckConfigModalVisible(true);
											} }
										>检测配置
										</Button>
									</div>

									<div className="r-hairline--bottom r-flex r-jc-sb r-pb-12">
										<div className="r-pt-12">
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>是否显示网点名称</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>快递单网点名称，默认会显示</p>
										</div>
										<Form.Item
											className="r-mt-20"
											name="isShowBranches"
											style={ { width: 200 } }
										>
											<EnumSelect disabled={ isDisabeldByReadOnlyFields("isShowBranches") } enum={ isShowBranches } allowClear={ false } />
										</Form.Item>
									</div>
									<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
										<div className="r-pt-12">
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>吊牌打印设置</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>在打印快递单时，您可以优先配置商品吊牌的配置</p>
										</div>
										<div style={ { width: 200 } } className="r-flex r-ai-c">
											<Button
												type="link"
												id="printTagSet"
												size="large"
												onClick={ () => { !isDisabeldByReadOnlyFields('printTagSet') && setShowTagSettingModal(true); } }
												className={ cs("r-fs-14", "r-as-c", "r-pl-0", introItemName === 'printTagSet' ? s.leftArrow : '') }
												disabled={ isDisabeldByReadOnlyFields('printTagSet') }
												data-point={ Pointer.系统设置_吊牌规则配置 }
											>配置吊牌规则
											</Button>
										</div>
									</div>
								</div>
							</div>
						) : null
					}
					{
					// settingPerm.userPerm ? (
						settingPerm.settingPerm && userStore?.userInfo?.version === 1 && !isShowZeroStockVersion ? (
							<div
								className={ cs(s.systemWrapper, 'r-bg-white') }
								ref={ contentRefs.current['item-6'] }
								data-id="item-6"
								style={ { marginTop: 8 } }
								id="item-6"
							>
								<h3 className={ cs(s.title, 'r-hairline--bottom r-fs-16') }>订单&库存</h3>
								<div className={ s.content }>
									{
										userStore?.userInfo?.version === 1 && !isShowZeroStockVersion ? (
											<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
												<div className="r-pt-12">
													<h3 className={ cs(s.contentTitle, 'r-fs-14') }>订单是否需要扣减库存</h3>
													<p className={ cs(s.contentDesc, 'r-fs-14') }>如果设置了扣减库存，必须设置开始扣减库存时间</p>
													<p className={ cs(s.errorMsg) }>请注意此功能开启后不能被关闭</p>
												</div>
												<div>
													<Form.Item
														className=""
														name="inventoryDeduct"
														style={ { width: 200 } }
													>
														<EnumSelect
															disabled={ isDisabeldByReadOnlyFields("inventoryDeduct") || settingConf?.inventoryDeduct === 1 }
															onChange={ onInventoryDeductChange }
															allowClear={ false }
															enum={ inventoryDeduct }
														/>
													</Form.Item>
													{
														isShowInventoryDeductTime ? (
															<Form.Item
																name="inventoryDeductTime"
																style={ { marginBottom: 0 } }
															>
																<DatePicker
																	disabled={ isDisabeldByReadOnlyFields("inventoryDeduct") || settingConf?.inventoryDeduct === 1 }
																	style={ { width: 200 } }
																	showTime={ { format: 'HH:mm' } }
																	format="YYYY-MM-DD HH:mm:ss"
																/>
															</Form.Item>
														) : null
													}
													{
														settingConf?.inventoryDeduct === 1 ? (
															<Button
																type="link"
																style={ { paddingLeft: 0 } }
																onClick={ () => {
																	!isDisabeldByReadOnlyFields('inventoryDeduct') && setNoInventoryDeductSetModalVisible(true);
																} }
															>不扣库存订单设置
															</Button>
														) : null
													}
												</div>
											</div>
										) : null
									}
									{
										userStore?.userInfo?.version === 1 && isShowInventoryDeductTime && !isShowZeroStockVersion && !isFreeSupplierAccount ? (
											<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
												<div className="r-pt-12">
													<h3 className={ cs(s.contentTitle, 'r-fs-14') }>
														订单库存占用方式
														<a href="#/levelCompare" target="_blank" className="r-ml-6" style={ { fontWeight: 400 } }>(高配版功能)</a>
													</h3>
													<p className={ cs(s.errorMsg) }>该设置决定您的订单在何时占用可配货库存</p>
												</div>
												<Form.Item
													className="r-mt-20"
													name="orderPreOccupiedStock"
													style={ { width: 200 } }
												>
													<EnumSelect
														disabled={ isDisabeldByReadOnlyFields("orderPreOccupiedStock") || occupiedLock }
														enum={ orderPreOccupiedStock }
														onChange={ () => { sendPoint(Pointer['设置_系统设置_系统设置_订单库存占用方式']); } }
														allowClear={ false }
													/>
												</Form.Item>
											</div>
										) : null
									}
									{
										userStore?.userInfo?.version === 1 && isShowInventoryDeductTime && !isShowZeroStockVersion ? (
											<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
												<div className="r-pt-20">
													<h3 className={ cs(s.contentTitle, 'r-fs-14') }>
														扣减库存最少为0，不可为负数（适用于仅管理售后库存等场景）
													</h3>
													<p className={ cs(s.contentDesc) }>开启后，库存扣减后最少为0，扣减后若为负数仍显示为0</p>
												</div>
												<div className={ cs(s.rightItemContainer) }>
													<Form.Item
														className="r-mt-20"
														name="stockZero"
														style={ { width: 200, marginBottom: 0 } }
													>
														<EnumSelect
															enum={ openOrCloseConfig }
															allowClear={ false }
														/>
													</Form.Item>
													<div style={ { opacity: 0.46, fontSize: 14, textAlign: "right" } }>
														{settingConf?.stockZeroTime ? (settingConf?.stockZero === openOrCloseConfig.开启 ? "开启时间：" : "关闭时间：") : ""}
														{settingConf?.stockZeroTime ? dayjs(settingConf?.stockZeroTime).format("YYYY-MM-DD HH:mm:ss") : ''}
													</div>
												</div>
											</div>
										) : null
									}
									{
										!isShowZeroStockVersion ? (
											<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
												<div className="r-pt-20">
													<h3 className={ cs(s.contentTitle, 'r-fs-14') }>
														包材是否管库存
													</h3>
													<p className={ cs(s.contentDesc) }>若设置无需管理库存，则包材货品不进行库存占用、分配、扣减等逻辑；若需要管理库存，则和成品货品处理逻辑一致</p>
												</div>
												<div className={ cs(s.rightItemContainer) } >
													<Form.Item
														className={ cs("r-mt-20", introItemName === 'sysItempropertyManageStockFlag' ? s.leftArrow : '') }
														name="sysItempropertyManageStockFlag"
														style={ { width: 200, marginBottom: 0 } }
													>
														<EnumSelect
															enum={ packageMaterialStockConfig }
															allowClear={ false }
														/>
													</Form.Item>
													<div
														style={ { opacity: 0.46, fontSize: 14, textAlign: "right" } }
													>
														{settingConf?.sysItempropertyManageStockFlagTime && '操作时间：'}
														{settingConf?.sysItempropertyManageStockFlagTime ? dayjs(settingConf?.sysItempropertyManageStockFlagTime).format("YYYY-MM-DD HH:mm:ss") : ''}
													</div>
												</div>
											</div>
										) : null
									}

								</div>
							</div>
						) : null
					}
					{
						settingPerm.userPerm ? (
							<div
								className={ cs(s.systemWrapper, 'r-bg-white') }
								ref={ contentRefs.current['item-7'] }
								data-id="item-7"
								style={ { marginTop: 8 } }
								id="item-7"
							>
								<h3 className={ cs(s.title, 'r-hairline--bottom r-fs-16') }>商品档案</h3>
								<div className={ s.content }>

									<div className="r-hairline--bottom r-flex r-jc-sb r-pb-12">
										<div className="r-pt-12">
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>系统内商品重量单位</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>商品重量展示单位显示，您可以选择克或千克作为单位</p>
										</div>
										<Form.Item
											className="r-mt-20"
											name="weightUnit"
											style={ { width: 200 } }
										>
											<EnumSelect disabled={ isDisabeldByReadOnlyFields("weightUnit") } enum={ weightUnit } allowClear={ false } onChange={ onWeightUnithange } />
										</Form.Item>
									</div>
									<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
										<div>
											<h3 className={ cs(s.contentTitle, 'r-fs-14') }>{isShowZeroStockVersion ? '商品' : '货品'}自定义属性</h3>
											<p className={ cs(s.contentDesc, 'r-fs-14') }>开启后，系统会根据[自定义属性]设置显示在{isShowZeroStockVersion ? "平台商品" : "货品/组合货品"}信息内</p>
										</div>

										<div id="goodsCustomSet" className={ cs(s.rightItemContainer, introItemName === 'goodsCustomSet' ? s.leftArrow : '') }>
											<Form.Item
												className="r-mt-12"
												name="itemCustomAttribute"
												style={ { width: 200 } }
											>
												<EnumSelect enum={ autoSearch } allowClear={ false } />
											</Form.Item>
											<div style={ { opacity: 0.46, fontSize: 14, textAlign: "right", position: "relative", bottom: 10, } }>
												{settingConf?.itemCustomAttributeTime ? (settingConf?.itemCustomAttribute === autoSearch.开启 ? "开启时间：" : "关闭时间：") : ""}
												{settingConf?.itemCustomAttributeTime ? dayjs(settingConf?.itemCustomAttributeTime).format("YYYY-MM-DD HH:mm:ss") : ''}
											</div>
											<CustomerConfigSettingModal />
										</div>

									</div>
									{
										!isShowZeroStockVersion && (
											<div className="r-hairline--bottom r-flex r-jc-sb r-pb-12">
												<div className="r-pt-12">
													<h3 className={ cs(s.contentTitle, 'r-fs-14') }>将加权平均成本价更新至"货品档案"的"成本价"</h3>
													<p className={ cs(s.contentDesc, 'r-fs-14') }>
														开启后，每次有入库行为时，将根据加权平均成本价计算逻辑自动更新货品档案中的成本价
														<WeightingAverageCostModal />
													</p>
												</div>
												<div>
													<Form.Item
														className="r-mt-20"
														name="weightingAverageCost"
														style={ { width: 200, marginBottom: 0 } }
													>
														<EnumSelect
															enum={ weightingAverageCostConfig }
															allowClear={ false }
														/>
													</Form.Item>
													{
														settingConf?.extendInfo?.weightingAverageCostTime && (
															<div style={ { opacity: 0.46, fontSize: 14, textAlign: "right" } }>
																{settingConf?.extendInfo?.weightingAverageCost === weightingAverageCostConfig.开启 ? "开启时间：" : "关闭时间："}
																{dayjs(settingConf?.extendInfo?.weightingAverageCostTime).format("YYYY-MM-DD HH:mm:ss")}
															</div>
														)
													}
												</div>
											</div>
										)
									}
									{
										!isShowZeroStockVersion && (
											<div className="r-hairline--bottom r-flex r-jc-sb r-pb-12">
												<div className="r-pt-12">
													<h3 className={ cs(s.contentTitle, 'r-fs-14') }>创建系统货品/规格时自动生成条形码</h3>
													<p className={ cs(s.contentDesc, 'r-fs-14') }>
														开启后，每次新增货品规格、组合货品规格时，将自动生成条形码
														{/* <BarcodeRuleModal /> */}
														<span style={ { color: "#ff4d4f" } }>（提示：条形码生成不能完全保证唯一，生成后请核查后使用）</span>
													</p>
												</div>
												<div>
													<Form.Item
														className="r-mt-20"
														name="autoGenerateSysSkuBarCode"
														style={ { width: 200, marginBottom: 0 } }
													>
														<EnumSelect
															enum={ autoGenerateSysSkuBarCodeConfig }
															allowClear={ false }
														/>
													</Form.Item>
													{
														settingConf?.extendInfo?.autoGenerateSysSkuBarCodeTime && (
															<div style={ { opacity: 0.46, fontSize: 14, textAlign: "right" } }>
																{settingConf?.extendInfo?.autoGenerateSysSkuBarCode === autoGenerateSysSkuBarCodeConfig.开启 ? "开启时间：" : "关闭时间："}
																{dayjs(settingConf?.extendInfo?.autoGenerateSysSkuBarCodeTime).format("YYYY-MM-DD HH:mm:ss")}
															</div>
														)
													}
												</div>
											</div>
										)
									}

									{
										userStore?.userInfo?.version === 1 && !isShowZeroStockVersion && !isFreeSupplierAccount ? (
											<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
												<div className="r-pt-12">
													<h3 className={ cs(s.contentTitle, 'r-fs-14') }>组合货品库存计算方式</h3>
													<p className={ cs(s.contentDesc, s.errorMsg, 'r-fs-14') }>商家可以选择组合货品维度或子货品维度来展示库存数</p>
												</div>
												<Form.Item
													className="r-mt-20"
													name="groupStockNumOutWay"
													style={ { width: 200 } }
												>
													<EnumSelect
														disabled={ isDisabeldByReadOnlyFields("groupStockNumOutWay") || form.getFieldValue('groupStockNumOutWay') === groupStockNumOutWay.以子货品为准 }
														enum={ groupStockNumOutWay }
														allowClear={ false }
														onChange={ handleClickCombinationGoodsTypeOutofStockType }
													/>
												</Form.Item>
											</div>
										) : ''
									}
								</div>
							</div>
						) : null
					}

					{
						settingPerm.userPerm && !(isShowZeroStockVersion || isFreeSupplierAccount) ? (
							<div
								className={ cs(s.systemWrapper, 'r-bg-white') }
								ref={ contentRefs.current['item-8'] }
								data-id="item-8"
								style={ { marginTop: 8 } }
								id="item-8"
							>
								<h3 className={ cs(s.title, 'r-hairline--bottom r-fs-16') }>商品绑定</h3>
								<div className={ s.content }>
									{
										isShowZeroStockVersion || isFreeSupplierAccount ? null : (
											<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
												<div>
													<h3 className={ cs(s.contentTitle, 'r-fs-14') }>新增商品及规格时自动绑定本地货品</h3>
													<p className={ cs(s.contentDesc, 'r-fs-14') }>开启后，新商品下载或新规格下载时，系统根据规格编码自动绑定本地货品 （新增规格匹配只支持抖音、淘宝、拼多多、视频号、快手、有赞、快团团平台）</p>
												</div>
												<Form.Item
													className="r-mt-14"
													name="insertItemAutomaticRelation"
													style={ { width: 200, marginBottom: 0 } }
												>
													<EnumSelect disabled={ isDisabeldByReadOnlyFields("insertItemAutomaticRelation") } enum={ autoSearch } allowClear={ false } />
												</Form.Item>
											</div>
										)
									}
								</div>
							</div>
						) : null
					}
					{
						settingPerm.userPerm && !(isShowZeroStockVersion || isFreeSupplierAccount) ? (
							<div
								className={ cs(s.systemWrapper, 'r-bg-white') }
								ref={ contentRefs.current['item-9'] }
								data-id="item-9"
								style={ { marginTop: 8 } }
								id="item-9"
							>
								<h3 className={ cs(s.title, 'r-hairline--bottom r-fs-16') }>商品更新</h3>
								<div className={ s.content }>

									{isShowZeroStockVersion || isFreeSupplierAccount ? null : (
										<div className="r-hairline--bottom r-flex r-jc-sb r-pd-tb-12">
											<div>
												<h3 className={ cs(s.contentTitle, 'r-fs-14') }>同步修改平台商家编码</h3>
												<p className={ cs(s.contentDesc, 'r-fs-14') }>修改货品规格编码时，是否同步修改线上商品的商家编码</p>
											</div>
											<Form.Item
												className="r-mt-14"
												name="syncModifyProductCode"
												style={ { width: 200, marginBottom: 0 } }
											>
												<EnumSelect disabled={ isDisabeldByReadOnlyFields("syncModifyProductCode") } enum={ syncModifyProductCode } allowClear={ false } />
											</Form.Item>
										</div>
									)}

								</div>
							</div>
						) : null
					}

					{/* <Form.Item className={ cs(s.butWrapper, 'r-ta-c r-bg-white') } style={ { marginBottom: 0 } }>
					<Button htmlType="submit" loading={ submitLoading } type="primary" style={ { width: 110, height: 40 } } data-point={ Pointer['设置_系统设置_保存'] }>保存</Button>
				</Form.Item> */}
				</Form>
			</div>

			{/* 不扣库存订单设置 */}
			<NoInventoryDeductSetModal
				visible={ noInventoryDeductSetModalVisible }
				setting={ settingConf }
				onClose={ () => { setNoInventoryDeductSetModalVisible(false); } }
			/>

			{/* 订单异常配置 */}
			<TradeCheckConfigModal
				visible={ tradeCheckConfigModalVisible }
				onCancel={ () => { setTradeCheckConfigModalVisible(false); } }
			/>
			<MarkEmptyModal visible={ markEmptyModal } onOk={ () => setMarkEmptyModal(false) } onCancel={ () => setMarkEmptyModal(false) } />
			{/* 平台赠品配置 */}
			<PlatformGiftModal
				visible={ platformGiftModal }
				onCancel={ () => setPlatformGiftModal(false) }
			/>
			{/* 编码识别配置 */}
			<NumOcrRuleModal
				visible={ numOcrRuleModal }
				onCancel={ () => setNumOcrRuleModal(false) }
			/>
			<SendTimeModal visible={ sendTimeModal } onOk={ () => setSendTimeModal(false) } onCancel={ () => setSendTimeModal(false) } />
			<ModifyAddressModal visible={ modifyAddressModal } onOk={ () => setModifyAddressModal(false) } onCancel={ () => setModifyAddressModal(false) } />

			{tradeGoodsInfoUpdateVisible && (
				<TradeGoodsInfoUpdateModal
					onCancel={ () => { setTradeGoodsInfoUpdateVisible(false); } }
					onHandleSave={ (params) => {
						console.log('val.params.changeCommodityInfoSign', params.changeCommodityInfoSign);
						sendPoint(Pointer.订单商品信息更新_设置);
						setTradeUpdateConfig({ changeCommodityInfoSign: params.changeCommodityInfoSign });
						// form.setFieldsValue({ changeCommodityInfoSign: params.changeCommodityInfoSign });
						onSave({ ...form.getFieldsValue(), changeCommodityInfoSign: params.changeCommodityInfoSign });
					} }
				/>
			)}

			{showTagSettingModal && (
				<TagSettingModal
					onOk={ (set) => {
						setPrintTagSet({ ...set });
					} }
					printTagSet={ printTagSet }
					onCancel={ () => { setShowTagSettingModal(false); } }
				/>
			)}

			{longStayWithoutCheckVisible && (
				<LongStayWithoutCheckModal
					longStayWithoutTime={ settingConf.longStayWithoutTime }
					onCancel={ () => { setLongStayWithoutCheckVisible(false); } }
					onSave={ (data) => {
						setSettingConf(prev => ({
							...prev,
							...data,
						}));
					} }
				/>
			)}

			{autoMergeTradesRuleModalVisible && (
				<AutoMergeTradesRuleModal
					visible={ autoMergeTradesRuleModalVisible }
					setting={ settingConf }
					onCancel={ handleAutoMergeTradesRuleModalCancel }
					onOk={ handleAutoMergeTradesRuleModalOk }
				/>
			)}

			{renderFastCom()}

			<SplitSettingModal visible={ splitSettingModalVisible } onCancel={ () => setSplitSettingModalVisible(false) } />

		</div>
	);
};

export default observer(SystemPage);
