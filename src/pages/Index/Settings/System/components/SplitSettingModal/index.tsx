import React, { useEffect, useState } from 'react';
import { Modal, Button, Checkbox, Input, Tag, Select, Form, message, Alert } from 'antd';
import cs from 'classnames';
import dayjs from 'dayjs';
import userStore from '@/stores/user';
import { SettingSaveSystemSettingApi } from '@/apis/user';
import GenericSelectWithModal from '@/pages/Warehouse/StockSync/AutoStockSync/components/SyncBanRuleForm/SelectShop';
import { getShopsByPlat } from '@/components-biz/ShopListSelect/shopListUtils';
import s from '../AutoMergeTradesRuleModal/index.module.scss';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';

const { TextArea } = Input;
const { Option } = Select;


const SplitSettingModal = ({ visible, onCancel }) => {
	const [form] = Form.useForm();
	const [autoSplitTradeByCombSkuConfig, setAutoSplitTradeByCombSkuConfig] = useState({});
	const [autoSplitTradeByCombSkuConfigTime, setAutoSplitTradeByCombSkuConfigTime] = useState("");
	const [shopList, setShopList] = useState([{}]);
	const [showShopList, setShowShopList] = useState(false);
	const [showSourceList, setShowSourceList] = useState(false);

	const saveSetting = async() => {
		sendPoint(Pointer.系统设置_组合拆分子货品_拆分设置_保存);
		const values = await form.validateFields();
		const { sysSkuOuterIdEnable, shopEnable, sourceEnable, sysSkuOuterIdList, shopIdList, sourceList } = values;
		const autoSplitTradeByCombSkuConfigNew = {
			sysSkuOuterIdEnable: sysSkuOuterIdEnable.includes(1) ? 1 : 0,
			shopEnable: shopEnable.includes(1) ? 1 : 0,
			// sourceEnable: sourceEnable.includes(1) ? 1 : 0,
			sysSkuOuterIdList,
			shopIdList: shopIdList?.map(i => i.sellerId)?.join(',') || "",
			// sourceList: sourceList?.join(',') || "",
		};
		const extendInfo = { ...autoSplitTradeByCombSkuConfig, ...autoSplitTradeByCombSkuConfigNew };
		const saveRes = await SettingSaveSystemSettingApi({ extendInfo: { autoSplitTradeByCombSkuConfig: extendInfo } });
		if (saveRes) {
			message.success('保存成功');
			const setting = await userStore.getSystemSetting(true);
			userStore.setSystemSetting(setting);
		} else {
			message.error('保存失败');
		}
		onCancel();
	};

	useEffect(() => {
		if (visible) {
			form.setFieldsValue({});
			userStore.getSystemSetting().then(async(res) => {
				const { autoSplitTradeByCombSkuConfig = {}, autoSplitTradeByCombSkuConfigTime = "" } = res?.extendInfo || {};
				setAutoSplitTradeByCombSkuConfig(autoSplitTradeByCombSkuConfig);
				setAutoSplitTradeByCombSkuConfigTime(autoSplitTradeByCombSkuConfigTime ? dayjs(autoSplitTradeByCombSkuConfigTime).format("YYYY-MM-DD HH:mm:ss") : "---");
				setShowShopList(autoSplitTradeByCombSkuConfig.shopEnable == 1);
				// setShowSourceList(autoSplitTradeByCombSkuConfig.sourceEnable == 1);
				const shopList = await getShopsByPlat({ hasHand: false });
				const shopListFilter = shopList.filter(i => autoSplitTradeByCombSkuConfig.shopIdList?.includes(i.sellerId)).map(i => {
					return { ...i, value: i.id };
				});
				setShopList(shopListFilter);
				const values = {
					sysSkuOuterIdEnable: [autoSplitTradeByCombSkuConfig.sysSkuOuterIdEnable],
					sysSkuOuterIdList: autoSplitTradeByCombSkuConfig.sysSkuOuterIdList,
					shopEnable: [autoSplitTradeByCombSkuConfig.shopEnable],
					shopIdList: shopListFilter,
					// sourceEnable: [autoSplitTradeByCombSkuConfig.sourceEnable],
					// sourceList: autoSplitTradeByCombSkuConfig.sourceList?.split(","),
				};
				console.log(values);
				form.setFieldsValue(values);
			});
		}
	}, [visible]);

	return (
		<Modal
			title="拆分设置"
			visible={ visible }
			onCancel={ onCancel }
			onOk={ () => saveSetting() }
			width={ 700 }
			footer={ (
				<div className="r-flex r-jc-sb">
					<div className={ cs(s.info, 'r-flex r-ai-c') }>
						最近调整时间：<span style={ { marginLeft: 8 } }>{autoSplitTradeByCombSkuConfigTime}</span>
					</div>
					<div>
						<Button onClick={ onCancel }>取消</Button>
						<Button type="primary" onClick={ () => saveSetting() }>确定</Button>
					</div>
				</div>
			) }
			centered
			className={ s.modal }
		>
			<Alert
				type="warning"
				message={
					(
						<div className="r-c-333 r-flex">
							<div>
								说明：
							</div>
							<div>
								<div>
									1、未设置时，默认组合货品全部拆分为子货品
								</div>
								<div>
									2、拆分操作不可逆，请确认后开启
								</div>
							</div>
						</div>
					)
				}
			/>
			<Form form={ form } layout="vertical" className="r-mt-12">
				<Form.Item name="sysSkuOuterIdEnable" noStyle>
					<Checkbox.Group>
						<Checkbox value={ 1 }>
							指定货品组合货品规格编码的货品拆分，未填写时默认全部
						</Checkbox>
					</Checkbox.Group>
				</Form.Item>
				<div style={ { color: '#ff4d4f', marginLeft: 24 } } className="r-mt-4 r-mb-4">请填写组合货品规格编码后保存</div>
				<div className="r-ml-24">
					<Form.Item name="sysSkuOuterIdList" noStyle>
						<TextArea
							placeholder="请填写需要拆分的组合货品的货品规格编码，多个用，隔开"
							autoSize={ { minRows: 2, maxRows: 4 } }
							style={ { marginBottom: 16, } }
						/>
					</Form.Item>
				</div>
				<Form.Item name="shopEnable" noStyle>
					<Checkbox.Group>
						<Checkbox value={ 1 } onChange={ e => setShowShopList(e.target.checked) }>
							指定店铺的商品拆分，未开启时全部商品进行拆分
						</Checkbox>
					</Checkbox.Group>
				</Form.Item>
				<div />
				{showShopList && (
					<div className="r-ml-24">
						<div>
							<div style={ { color: '#ff4d4f', marginBottom: 4 } }>请添加店铺后保存</div>
							<div>
								<GenericSelectWithModal
									style={ { paddingLeft: 0, marginBottom: 0 } }
									name="shopIdList"
									label=""
									placeholder="选择店铺，不选默认全部店铺生效"
									initValue={ shopList }
									selectWidth="628px"
									onUpdate={ (value) => {	
										console.log(value);	
										// setShopList(value);		
										form.setFieldsValue({ 'shopIdList': value }); 
									} }
								/>
							</div>
						</div>
					</div>
				)}
				<div />
				{/* <Form.Item name="sourceEnable" noStyle>
					<Checkbox.Group className="r-mt-8">
						<Checkbox value={ 1 } onChange={ e => setShowSourceList(e.target.checked) }>指定订单来源的订单进行拆分</Checkbox>
					</Checkbox.Group>
				</Form.Item>
				{showSourceList && (
					<div className="r-mt-8" style={ { marginLeft: 24 } }>
						<Form.Item name="sourceList" noStyle>
							<Checkbox.Group>
								<Checkbox value="PLATFORM">平台订单</Checkbox>
								<Checkbox value="HAND">手工订单</Checkbox>
								<Checkbox value="SCM">分销订单</Checkbox>
							</Checkbox.Group>
						</Form.Item>
					</div>
				)} */}
			</Form>
		</Modal>
	);
};

export default SplitSettingModal; 