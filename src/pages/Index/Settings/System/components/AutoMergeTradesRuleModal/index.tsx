import React, { useEffect, useState } from 'react';
import cs from 'classnames';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { observer } from "mobx-react";
import { Button, Modal, Form, InputNumber, Select, Radio, Checkbox, Table, Popover, message } from "antd";
import userStore from "@/stores/user";
import { Base } from "@/types/schemas";
import { IndexSettingUpdateMergeStrategyConfigApi, IndexSettingSelectUserOperateTimeApi } from '@/apis/user';
import { IndexSettingUpdateMergeStrategyConfigRequest } from '@/types/schemas/user';
import { PLAT_DW } from '@/constants';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import { getShopName, getShopsByPlat } from "@/components-biz/ShopListSelect/shopListUtils";
import Img1 from '@/assets/image/index/img_合单.png';
import Img2 from '@/assets/image/index/img_拆分.png';
import Img3 from '@/assets/image/index/img_待发货未打印快递单.png';
import s from './index.module.scss';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';

interface Iprops extends Base{
    visible: boolean;
    setting: any;
    onCancel?: ()=>void;
    onOk?: (data?:any)=>void;
}


const { Option } = Select;

const defaultMergeSize = 20;

// 订单自动合单规则设置
const AutoMergeTradesRuleModal = (props: Iprops) => {
	const {
		visible,
		setting,
		onCancel,
		onOk,
	} = props;

	const initialValues = {
		mergeMaxSize: defaultMergeSize,
		mergeMaxSizeType: 'GLOBAL',
		waitSendTradeUnprinted: false,
	};

	const [settingConf, setSettingConf] = useState<any>({}); // 设置副本
	const [loading, setLoading] = useState(false);
	const [mergeMaxSizeType, setMergeMaxSizeType] = useState('GLOBAL'); // GLOBAL：全局统一，SHOP店铺
	const [dataSource, setDataSource] = useState([]);
	const [lastOperateTime, setLastOperateTime] = useState(''); // 最近修改时间
	const [shopList, setShopList] = useState([]); // 最新的店铺
	// 添加一个状态来记录初始的waitSendTradeUnprinted值
	const [initialWaitSendTradeUnprinted, setInitialWaitSendTradeUnprinted] = useState(false);

	const { systemSetting, isStockAllocationVersion } = userStore;

	const [form] = Form.useForm();

	const getLastOperateTime = async() => {
		let res = await IndexSettingSelectUserOperateTimeApi({
			typeList: ['MERGE_STRATEGY_CONFIG']
		});
		// 这里和后端沟通了，只使用查找类型的第一条
		const info = res?.find(item => item.type == 'MERGE_STRATEGY_CONFIG');
		if (info) {
			setLastOperateTime(info?.time);
		}
	};

	const init = async() => {
		let shopList = await getShopsByPlat({ hasHand: true });
		// console.log('%c [ shopList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', shopList);
		setShopList(shopList);
	};

	useEffect(() => {
		init();
		getLastOperateTime();
	}, []);

	useEffect(() => {
		if (systemSetting) {
			const {
				mergeMaxSize,
				mergeStrategyConfig,
			} = systemSetting;
			const {
				sysStockOut,
				sysPartStockOut,
				preSale,
				splitOrder,
				manualMergeOrder,
				manualSplitMergeOrder,
				waitSendTradeUnprinted
			} = mergeStrategyConfig;

			const mergeConfigType = mergeStrategyConfig?.mergeConfigType || 'GLOBAL';
			setMergeMaxSizeType(mergeConfigType);
			setSettingConf(cloneDeep(mergeStrategyConfig));

			let obj:any = {
				mergeMaxSizeType: mergeConfigType,
				sysStockOut,
				sysPartStockOut,
				preSale,
				splitOrder,
				manualMergeOrder,
				manualSplitMergeOrder,
				waitSendTradeUnprinted,
			};

			if (mergeConfigType == 'GLOBAL') {
				obj = {
					...obj,
					mergeMaxSize,
				};
			} else {
				obj = {
					...obj,
					mergeMaxSize,
				};
			}
			// 这里只是初始回显最大合单数方式和其他勾选项
			form.setFieldsValue(obj);
		}
	}, [systemSetting, isStockAllocationVersion]);

	// 在useEffect中记录初始值
	useEffect(() => {
		if (systemSetting) {
			const { mergeStrategyConfig } = systemSetting;
			const { waitSendTradeUnprinted } = mergeStrategyConfig;
			
			// 记录初始值
			setInitialWaitSendTradeUnprinted(!!waitSendTradeUnprinted);
		}
	}, [systemSetting]);

	const handleCancel = () => {
		onCancel?.();
	};

	const handleOk = async() => {
		await form.validateFields();

		setLoading(true);
		try {
			const formValue = form.getFieldsValue();
			const {
				mergeMaxSize,
				mergeMaxSizeType,
				sysStockOut,
				sysPartStockOut,
				preSale,
				splitOrder,
				manualMergeOrder,
				manualSplitMergeOrder,
				waitSendTradeUnprinted
			} = formValue;

			// 检查waitSendTradeUnprinted是否从false变为true
			if (!initialWaitSendTradeUnprinted && waitSendTradeUnprinted) {
				sendPoint(Pointer.设置_系统设置_待发货未打印快递单与已打印订单不自动合并);
			}

			let mergeConfigList = [];
			dataSource?.forEach(item => {
				if (formValue[item.key]) {
					mergeConfigList.push({
						sellerId: item.sellerId,
						platform: item.platform,
						// sellerNick: item.sellerNick,
						mergeMaxSize: formValue[item.key],
					});
				}
			});

			let params:IndexSettingUpdateMergeStrategyConfigRequest = {
				mergeMaxSize: mergeMaxSize ?? systemSetting?.mergeMaxSize,
				mergeConfigType: mergeMaxSizeType,
				sysStockOut,
				sysPartStockOut,
				preSale,
				splitOrder,
				manualMergeOrder,
				manualSplitMergeOrder,
				waitSendTradeUnprinted,
				shopMergeConfigList: mergeConfigList?.length ? mergeConfigList : (settingConf?.shopMergeConfigList || []),
			};
			const res = await IndexSettingUpdateMergeStrategyConfigApi(params);
			if (res.success) {
				message.success('保存成功', 2);
			}

			// 这里保存的数据是给后端用的，保存后理论上不需要更新
			let newSystemSetting = await userStore.getSystemSetting(true);

			setLoading(false);
			onOk?.(newSystemSetting);
		} catch (error) {
			setLoading(false);
		}
	};

	const footer = (
		<div className="r-flex r-jc-sb r-ai-c">
			<div className={ cs(s.info, "r-flex r-ai-c") }>
				最近调整时间：
				<span>{dayjs(lastOperateTime)?.isValid() ? dayjs(lastOperateTime).format('YYYY-MM-DD HH:mm:ss') : ''}</span>
			</div>
			<div>
				<Button onClick={ () => handleCancel() }>取 消</Button>
				<Button type="primary" onClick={ () => handleOk() } loading={ loading }>确 定</Button>
			</div>
		</div>
	);

	const onValuesChange = (changedValues, allValues) => {
		// 选择长期有效，清空限时有效时间
		if ('mergeMaxSizeType' in changedValues) {
			setMergeMaxSizeType(changedValues.mergeMaxSizeType || 1);
		}
		
		// 添加waitSendTradeUnprinted变更时的打点
		// if ('waitSendTradeUnprinted' in changedValues) {
		// 	sendPoint(Pointer.设置_系统设置_待发货未打印快递单与已打印订单不自动合并);
		// }
		
		// console.log('%c [ allValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', allValues);
	};
	const columns = [
		{
			title: "平台店铺",
			dataIndex: "sellerId",
			width: 300,
			render(_, record, index) {
				const { sellerId, sellerNick } = record;
				return (
					<div>
						{sellerId == '-99' ? (
							<div className={ s.newShops }>&lt;新增店铺&gt;</div>
						) : (
							<div className={ cs('r-fc-black-65', 'r-wb-bw', 'r-flex', 'r-ai-c') }>
								<PlatformIcon platform={ record['platform'] } />
								<span className="r-fs-14">{getShopName({ plat: record?.platform, sellerNick: record?.sellerNick || "" })}</span>
							</div>
						)}
					</div>
				);

			}
		},
		{
			title: (
				<div>
					<span className="r-c-error"> * </span>
					最大合单数
				</div>
			),
			dataIndex: "mergeMaxSize",
			width: 300,
			render(_, record,) {
				const { key } = record;
				return (
					<div>
						<Form.Item
							label=""
							name={ key }
							rules={ record.platform == PLAT_DW ? [] : [
								{
									required: true,
									validator: (_:any, value:any) => {
										if (!value && value !== 0) {
											return Promise.reject(new Error('请输入最大合单数'));
										}
										if (Number(value) < 2) {
											return Promise.reject(new Error('数量最少为2'));
										}
										if (Number(value) > 100) {
											return Promise.reject(new Error('数量最大为100'));
										}
										return Promise.resolve();
									}
								}
							] }
							style={ { marginBottom: 0 } }
						>
							<InputNumber disabled={ record.platform == PLAT_DW } style={ { width: 80 } } size="small" min={ record.platform == PLAT_DW ? 0 : 2 } max={ 100 } />
						</Form.Item>
					</div>
				);

			}
		}
	];

	useEffect(() => {
		if (mergeMaxSizeType == 'GLOBAL') { // 切换到统一设置的时候，删除区分店铺的数据

			form.setFieldsValue({
				mergeMaxSize: systemSetting?.mergeMaxSize
			});
		} else {
			let list = settingConf?.shopMergeConfigList || [];
			let defaultConfig = list?.find(item => item.sellerId == '-99') || {
				"mergeMaxSize": 20,
				"platform": "OTHER",
				"sellerId": "-99",
				"sellerNick": "新增店铺"
			};
			let newList = [
				{ ...defaultConfig }
			];
			let defaultMergeMaxSize = list?.find(item => item.sellerId == '-99')?.mergeMaxSize || defaultMergeSize;
			list?.forEach(item => {
				// 已经被删除的店铺不再展示
				if (shopList?.find(shop => item.sellerId == shop.sellerId)) {
					newList.push(item);
				}
			});
			// 从接口获取拼接到数据里面
			shopList?.forEach(item => {
				let oldItem = newList.find(d => d.sellerId === item.sellerId);
				if (!oldItem) {
					newList.push({
						sellerId: item.sellerId,
						sellerNick: item.sellerNick,
						platform: item.platform,
						mergeMaxSize: item.platform == PLAT_DW ? 0 : defaultMergeMaxSize,
					});
				} else {
					oldItem['sellerNick'] = item.sellerNick; // 这里更新下昵称，后端决定不存昵称了
				}
			});

			newList.forEach((item) => {
				item['key'] = `shop_${item.sellerId}`;
			});
			setDataSource(newList);
			// 找出所有的店铺，赋值最大合单数
			let obj = {};
			newList.forEach(item => {
				obj[`shop_${item.sellerId}`] = item.mergeMaxSize;
			});
			form.setFieldsValue({
				...obj,
			});
		}
	}, [systemSetting, mergeMaxSizeType, settingConf, shopList]);

	return (
		<>
			<Modal
				title={ <div>自动合单规则设置<span style={ { fontWeight: 400 } }>（暂不支持得物平台订单）</span></div> }
				maskClosable={ false }
				keyboard={ false }
				closable
				onCancel={ handleCancel }
				onOk={ handleOk }
				visible={ visible }
				width={ 800 }
				destroyOnClose
				centered
				okText="确 定"
				cancelText="取 消"
				bodyStyle={ { overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' } }
				className={ s.modal }
				footer={ footer }
			>
				<div>
					<Form
						form={ form }
						name="form"
						initialValues={ initialValues }
						onValuesChange={ onValuesChange }
					>
						<Form.Item
							label="最大合单数"
							name="mergeMaxSizeType"
						>
							<Radio.Group size="middle" className="r-mt-4">
								<Radio value="GLOBAL">统一设置</Radio>
								<Radio value="SHOP">区分店铺</Radio>
							</Radio.Group>
						</Form.Item>

						{
							mergeMaxSizeType == 'GLOBAL' ? (
								<div className={ s.maxSize }>
									<div className="r-flex r-ai-c">
										<span className="r-c-error r-fs-20 r-mr-4"> * </span>
										<span>每个包裹最多可合并订单数</span>
										<span style={ { color: 'rgba(0, 0, 0, 0.45)' } }>（仅支持同店铺）</span>：

										<Form.Item
											name="mergeMaxSize"
											label=""
											noStyle={ false }
											style={ { marginBottom: 0 } }
											rules={
												[{
													required: true,
													validator: (_:any, value:any) => {
														// console.log('%c [ value ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);

														// if (value && !/^[1-9]{1}[\d]*$/.test(value)) {
														// 	return Promise.reject(new Error('请正确输入数量'));
														// }
														if (!value && value !== 0) {
															return Promise.reject(new Error('数量不能为空'));
														}
														if (Number(value) < 2) {
															return Promise.reject(new Error('数量最少为2'));
														}
														if (Number(value) > 100) {
															return Promise.reject(new Error('数量最大为100'));
														}
														return Promise.resolve();
													}
												}]
											}
										>
											<InputNumber addonAfter="单" controls={ false } placeholder="必须大于0" style={ { width: 130 } } />
										</Form.Item>
									</div>
								</div>
							) : (
								<div className={ s.maxSizeList }>
									<Table
										size="small"
										scroll={ { y: 200, x: 'auto' } }
										pagination={ false }
										bordered
										dataSource={ dataSource }
										columns={ columns }
										className={ s.table }
										rowKey="sellerId"
									/>
								</div>
							)
						}
						<Form.Item className="r-mt-16" name="waitSendTradeUnprinted" valuePropName="checked" label="">
							<Checkbox>
								「
								<Popover
									content={ <img src={ Img3 } alt="" className={ s.tipIconB } /> }
									title=""
									trigger="hover"
									getPopupContainer={ trigger => trigger?.parentElement }
									arrowPointAtCenter
									autoAdjustOverflow
									placement="bottom"
								>
									<span className={ s.tipText }>待发货未打印快递单</span>
								</Popover>
								」与已打印订单不自动合并
							</Checkbox>
						</Form.Item>
						{
							isStockAllocationVersion && (
								<>
									<Form.Item name="sysStockOut" valuePropName="checked" label="" className="r-mt-16">
										<Checkbox>允许合并「系统判断为“缺货”」的订单</Checkbox>
									</Form.Item>
									<Form.Item name="sysPartStockOut" valuePropName="checked" label="">
										<Checkbox>允许合并「系统判断为“部分缺货”」的订单</Checkbox>
									</Form.Item>
								</>
							)
						}

						<Form.Item name="preSale" valuePropName="checked" label="">
							<Checkbox>允许合并「订单标签为“预售订单”」的订单</Checkbox>
						</Form.Item>
						<Form.Item name="splitOrder" valuePropName="checked" label="">
							<Checkbox>允许合并「订单标签为“拆分订单”」的订单</Checkbox>
						</Form.Item>
						<Form.Item name="manualMergeOrder" valuePropName="checked" label="">
							<Checkbox>
								手动「
								<Popover
									content={ <img src={ Img1 } alt="" className={ s.tipIcon } /> }
									title=""
									trigger="hover"
									getPopupContainer={ trigger => trigger?.parentElement }
									arrowPointAtCenter
									autoAdjustOverflow
									placement="bottom"
								>
									<span className={ s.tipText }>合并</span>
								</Popover>
								」的订单，重新查询后允许保留合单结果
							</Checkbox>
						</Form.Item>
						<Form.Item name="manualSplitMergeOrder" valuePropName="checked" label="">
							<Checkbox>
								手动「
								<Popover
									content={ <img src={ Img2 } alt="" className={ s.tipIcon } /> }
									title=""
									trigger="hover"
									getPopupContainer={ trigger => trigger?.parentElement }
									arrowPointAtCenter
									autoAdjustOverflow
									placement="bottom"
								>
									<span className={ s.tipText }>取消合并</span>
								</Popover>
								」的订单，重新查询后允许保留拆分结果
							</Checkbox>
						</Form.Item>
					</Form>
				</div>
			</Modal>
		</>
	);
};

export default observer(AutoMergeTradesRuleModal);
