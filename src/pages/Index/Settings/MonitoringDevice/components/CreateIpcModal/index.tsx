import React, { useState, useEffect } from 'react';
import { Modal, Form, Select, Input, Radio, Button, message, Popover } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { debounce } from 'lodash';
import { observer } from 'mobx-react';
import { number } from 'echarts';
import { 
	IndexMonitoringQueryMonitoringDeviceApi, 
	IndexMonitoringSaveDeviceCameraApi,
	IndexMonitoringCheckRepeatCameraNameApi,
	IndexMonitoringEditDeviceCameraApi // 新增
} from '@/apis/setting/monitoringDevice';
import videoMonitorStore from '@/stores/trade/videoMonitor';
import s from './index.module.scss';

const { Option } = Select;

interface CreateIpcModalProps {
	visible: boolean;
	onCancel: () => void;
	onSuccess: () => void;
	editData?: any; // 新增
}
/**
 * 新建IPC摄像头模态框
 */
const CreateIpcModal: React.FC<CreateIpcModalProps> = ({
	visible,
	onCancel,
	onSuccess,
	editData
}) => {
	const { 
		checkKdzsPrintComponent, 
		checkKdzsPrintComponentStatus,
		isKdzsPrintComponent,
		sendMessage,
		disconnectWs,
		isSocketConnected,
		connectWs,
		clearMessageCallbacks, // 添加清理消息回调的方法
		getDeviceList,
	} = videoMonitorStore;
    
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const [nvrDevices, setNvrDevices] = useState<any[]>([]);
	const [channels, setChannels] = useState<number[]>([]);

	const debounceCheckDeviceName = debounce(
		(cameraName: string, resolve: (v?: any) => void, reject: (reason?: any) => void) => {
			IndexMonitoringCheckRepeatCameraNameApi({ cameraName })
				.then(() => resolve())
				.catch((error) => {
					if (error?.errorCode == 1200) {
						reject('摄像头名称已存在');
					} else {
						reject(error?.errorMessage || '校验摄像头名称失败');
					}
				});
		},
		500 // 500ms防抖
	);

	// 生成通道选项
	const generateChannels = (deviceId: string, nvrDevices: any[]) => {
		const device = nvrDevices.find(item => item.id == deviceId);
		if (!device || device.status == 0) {
			message.warning('NVR存储设备不存在或已关闭');
			setChannels([]);
			return;
		}
		const { deviceIp, deviceAccountName, deviceAccountPassword } = device;
		sendMessage('login_nvr_device', {
			device_ip: deviceIp,
			device_username: deviceAccountName,
			device_password: deviceAccountPassword
		}, (res) => {
			// 根据返回结果更新验证状态
			if (res.result === 0) {
				setChannels(res?.data?.channels || []);
			} else {
				setChannels([]);
				message.error(res?.msg || '控件连接失败');
			}
		});
	};

	// 获取NVR存储设备列表
	const fetchNvrDevices = async() => {
		const res = await getDeviceList();
		if (res?.length) {
			setNvrDevices(res);
		}

		setTimeout(() => {
			if (editData) {
				form.setFieldsValue({
					deviceId: Number(editData?.deviceId || ''),
					cameraName: editData?.cameraName,
					cameraChannel: editData?.cameraChannel,
					watermark: Boolean(editData?.watermark || 0),
				});
				generateChannels(editData?.deviceId, res);
			} else {
				form.resetFields();
			}
		}, 0);
        
		// try {
		// 	const response = await IndexMonitoringQueryMonitoringDeviceApi({});
		// 	if (response?.length) {
		// 		setNvrDevices(response);
		// 	}
		// } catch (error) {
		// 	console.error('获取NVR设备列表失败:', error);
		// }
	};

	// 预览功能
	const handlePreview = async() => {
		// 先判断有没有安装控件，没有安装则提示下载控件
		await checkKdzsPrintComponentStatus();
        
		const deviceId = form.getFieldValue('deviceId');
		const channel = form.getFieldValue('cameraChannel');
		
		if (!deviceId || !channel) {
			message.warning('请先选择NVR存储设备和摄像头通道');
			return;
		}
		
		// 实现预览功能
		const device = nvrDevices.find(item => item.id == deviceId);
		if (!device || device.status == 0) {
			message.warning('NVR存储设备不存在或已关闭');
			return;
		}
		const { deviceIp, deviceAccountName, deviceAccountPassword } = device;
        
		sendMessage('start_nvr_preview', {
			device_ip: deviceIp,
			device_username: deviceAccountName,
			device_password: deviceAccountPassword,
			device_channel: Number(channel)
		}, (res) => {
			// 根据返回结果更新验证状态
			if (res.msg) {
				message.error(res.msg);
			}
		});
	};

	// 提交表单
	const handleSubmit = async() => {
		const values = await form.validateFields();
		try {
			setLoading(true);

			const device = nvrDevices.find(item => item.id == values.deviceId);
			if (!device) {
				message.warning('NVR存储设备不存在');
				return;
			}
			
			const params = {
				...values,
				cameraName: values.cameraName, // 摄像头名称
				cameraChannel: values.cameraChannel, // 摄像头通道
				watermark: values?.watermark ? 1 : 0, // 水印是否开启，0：关闭，1开启
				status: editData ? editData.status : 1, // 启用状态，0：禁用，1：启用
				deviceId: values.deviceId, // 设备ID
				deviceName: device?.deviceName, // 设备名称
				hideErrorMessage: true, // 隐藏错误信息
			};
			
			let response;
			if (editData) {
				// 编辑时调用编辑接口
				response = await IndexMonitoringEditDeviceCameraApi({
					...editData,
					...params,
					id: editData.id, // 传递摄像头ID
				});
			} else {
				// 新增时调用保存接口
				response = await IndexMonitoringSaveDeviceCameraApi(params);
			}
			
			if (response) {
				message.success('保存成功');
				form.resetFields();
				onSuccess();
			}
		} catch (error) {
			console.error('创建/编辑IPC摄像头失败:', error);

			if (error?.errorCode === 1204) {
				Modal.error({
					title: editData ? '编辑IPC摄像头失败' : '新建IPC摄像头失败',
					content: (
						<div className="r-c-error">
							存储设备通道已被占用
						</div>
					),
					onOk: () => {
                        
					}
				});
			} else {
				message.error(error?.errorMessage || '保存失败');
			}
		} finally {
			setLoading(false);
		}
	};

	// 取消操作
	const handleCancel = () => {
		form.resetFields();
		onCancel();
	};

	// 处理摄像头通道点击事件
	const handleChannelClick = async() => {
		// 先判断有没有安装控件，没有安装则提示下载控件
		await checkKdzsPrintComponentStatus();

		// 先检查是否选择了NVR存储设备
		const deviceId = form.getFieldValue('deviceId');
		if (!deviceId) {
			message.warning('请先选择NVR存储设备');
			return;
		}
	};

	const handleValuesChange = (changedFields: any, allFields: any) => {
		if (changedFields?.deviceId) {
			form.setFieldsValue({
				cameraChannel: undefined
			});
			generateChannels(changedFields?.deviceId, nvrDevices);
		}
	};

	const cleanup = () => {
		// 清理消息回调，避免内存泄漏
		clearMessageCallbacks();
		// 重置验证状态
		setChannels([]);

		// 关闭预览
		sendMessage('close_nvr_preview', {}, (res) => {
			if (res.msg) {
				// message.error(res.msg);
			}
		});

	};

	// 模态框显示时初始化数据
	useEffect(() => {
		if (visible) {
			fetchNvrDevices(); // 获取NVR存储设备列表
		} else {
			cleanup();
		}
	}, [visible]);

	useEffect(() => {
		return () => {
			cleanup();
		};
	}, []);

	return (
		<Modal
			title={ editData ? "编辑IPC摄像头" : "新建IPC摄像头" }
			visible={ visible }
			width={ 480 }
			destroyOnClose
			centered
			maskClosable={ false }
			keyboard={ false }
			okText="提交"
			cancelText="取消"
			onOk={ handleSubmit }
			onCancel={ handleCancel }
			okButtonProps={ { loading } }
		>
			<Form
				form={ form }
				className={ s.createIpcForm }
				labelCol={ { span: 6 } }
				wrapperCol={ { span: 18 } }
				onValuesChange={ handleValuesChange }
			>
				{/* NVR存储设备 */}
				<div className={ s.section }>
					<Form.Item
						label="NVR存储设备"
						name="deviceId"
						rules={ [{ required: true, message: '请选择NVR存储设备' }] }
					>
						<Select
							placeholder="请选择NVR存储设备"
							showSearch={ false }
							optionFilterProp="children"
							style={ { width: 246 } }
							disabled={ !!editData } // 编辑模式下禁用选择
						>
							{nvrDevices.map(device => (
								<Option key={ device.id } value={ device.id }>
									{device.deviceName}
								</Option>
							))}
						</Select>
					</Form.Item>
				</div>

				{/* IPC摄像头配置 */}
				<div className={ s.section }>
					<div className={ s.sectionTitle }>IPC摄像头配置</div>

					<Form.Item
						label="摄像头名称"
						name="cameraName"
						rules={ [
							{ required: true, message: '请输入摄像头名称' },
							{
								validator: async(_, value) => {
									if (!value) return Promise.resolve();
									// 编辑时，只有名称变更才校验
									if (editData && value === editData.cameraName) {
										return Promise.resolve();
									}
									return new Promise((resolve, reject) => {
										debounceCheckDeviceName(value, resolve, reject);
									});
								}
							}
						] }
					>
						<Input placeholder="请输入摄像头名称" style={ { width: 246 } } />
					</Form.Item>

					<Form.Item
						label="摄像头通道"
						name="cameraChannel"
						rules={ [{ required: true, message: '请选择摄像头通道' }] }
					>
						<div className={ s.channelWrapper }>
							<Form.Item name="cameraChannel" noStyle>
								<Select placeholder="请选择通道" className={ s.channelSelect } style={ { width: 246 } } onClick={ handleChannelClick }>
									{channels.map(channel => (
										<Option key={ channel } value={ channel }>
											{channel}
										</Option>
									))}
								</Select>
							</Form.Item>
							
							<Button type="link" onClick={ handlePreview } className={ s.previewBtn }>
								预览
							</Button>
						</div>
					</Form.Item>

					{/* <Form.Item
						label="录制超时时间"
						name="recordingTimeout"
						rules={ [
							{ required: true, message: '请输入录制超时时间' },
							{ min: 1, max: 60, message: '录制超时时间必须在1-60秒之间' }
						] }
					>
						<Input
							type="number"
							placeholder="请输入时长(秒), 最多60秒"
							className={ s.timeoutInput }
							addonAfter="秒"
							style={ { width: 246 } }
						/>
					</Form.Item> */}

					{/* <Form.Item
						label="视频水印"
						name="watermark"
						initialValue={ false }
					>
						<Radio.Group className="r-mt-6">
							<Radio value={ false }>关闭</Radio>
							<Radio value>
								开启
								<Popover content="开启后，视频会添加水印">
									<QuestionCircleOutlined className={ s.helpIcon } />
								</Popover>
							</Radio>
						</Radio.Group>
					</Form.Item> */}
				</div>
			</Form>
		</Modal>
	);
};

export default observer(CreateIpcModal); 