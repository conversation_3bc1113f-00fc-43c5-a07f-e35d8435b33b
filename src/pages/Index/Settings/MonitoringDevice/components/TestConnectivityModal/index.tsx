import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Modal, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import s from './index.module.scss';
import videoMonitorStore from '@/stores/trade/videoMonitor';
import message from "@/components/message";
import { tradeStore } from "@/stores";

interface TestConnectivityModalProps {
  visible: boolean;
  record?: any; // 新增record参数
  onCancel: () => void;
}

const TestConnectivityModal: React.FC<TestConnectivityModalProps> = ({
	visible,
	record, // 新增record参数
	onCancel,
}) => {
	const { 
		checkKdzsPrintComponent, 
		checkKdzsPrintComponentStatus,
		isKdzsPrintComponent,
		sendMessage,
		disconnectWs,
		isSocketConnected,
		connectWs,
		clearMessageCallbacks, // 添加清理消息回调的方法
	} = videoMonitorStore;

	const nvrName = record?.deviceName || '';

	const [checkLoginNvrDeviceStatus, setCheckLoginNvrDeviceStatus] = useState(false);
	const [loading, setLoading] = useState(false);

	const handleConnectWs = async() => {
		await checkKdzsPrintComponentStatus(); // 这里没有安装会提示安装ERP聚合控件
		connectWs();
	};

	// 验证海康威视录像机登录
	const checkLoginNvrDevice = async() => {
		setLoading(true);
		const { deviceIp, deviceAccountName, deviceAccountPassword } = record;
		sendMessage('login_nvr_device', {
			device_ip: deviceIp,
			device_username: deviceAccountName,
			device_password: deviceAccountPassword
		}, (res) => {
			setLoading(false);
			
			// 根据返回结果更新验证状态
			if (res.result === 0) {
				setCheckLoginNvrDeviceStatus(true);
			} else {
				setCheckLoginNvrDeviceStatus(false);
				message.error(res.msg);
			}
		});
	};

	const init = async() => {
		checkLoginNvrDevice();
	};

	// 清理函数 - 断开连接并清理回调
	const cleanup = () => {
		// 清理消息回调，避免内存泄漏
		clearMessageCallbacks();
		// 重置验证状态
		setCheckLoginNvrDeviceStatus(false);
		setLoading(false);
	};

	// 处理模态框关闭
	const handleCancel = () => {
		cleanup();
		onCancel();
	};

	const handleDownload = () => {
		const downloadUrl = (tradeStore.controlDonloadData || []).find(it => it.name === "快递助手ERP聚合打印控件")?.controlsArr[0]?.downLink;
		console.log('%c [ downloadUrl ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', tradeStore.controlDonloadData, downloadUrl);
		if (downloadUrl) {
			window.location.href = downloadUrl;
		} else {
			message.warning('请联系客服获取下载地址');
		}
	};

	useEffect(() => {
		if (visible) {
			init();
		} else {
			// 当模态框隐藏时进行清理
			cleanup();
		}
	}, [visible]);

	// 组件卸载时清理
	useEffect(() => {
		return () => {
			cleanup();
		};
	}, []);

	return (
		<Modal
			title={ `测试连通：${nvrName}` }
			visible={ visible }
			onCancel={ handleCancel } // 使用新的handleCancel方法
			footer={ null }
			width={ 480 }
			centered
			destroyOnClose
			maskClosable={ false }
		>
			<div className={ s.statusRow }>
				<span>聚合控件连接状态：</span>
				{
					isKdzsPrintComponent && isSocketConnected ? (
						<span className={ s.success }>已连接</span>
					) : (
						<span className={ s.gray }>未连接</span>
					)
				}
				{
					(!isKdzsPrintComponent || !isSocketConnected) && (
						<>
							<span className={ s.action } onClick={ handleConnectWs }>重新连接</span>
							<span className={ s.action } onClick={ handleDownload }>下载控件</span>
						</>
					)
				}
			</div>
			<div className={ s.statusRow }>
				<span>账户密码验证结果：</span>
				{
					!isKdzsPrintComponent || !isSocketConnected || !checkLoginNvrDeviceStatus ? (
						<span className={ s.error }>验证失败</span>
					) : (
						<span className={ s.success }>验证成功</span>
					)
				}
	
				<Button 
					type="link" 
					onClick={ checkLoginNvrDevice } 
					loading={ loading }
					style={ { padding: 0, color: '#1890ff', marginLeft: 6 } }
				>
					重新验证
				</Button>

				<Tooltip
					title={ (
						<div>
							如果您尝试多次失败，可以排查以下原因：<br />
							1. 检查海康威视录像机存储服务器是否正常<br />
							2. 快递助手ERP聚合控件版本过低<br />
							3. 内网服务器IP地址错误或者端口错误<br />
							4. 访问账户密码不匹配填写错误<br />
						</div>
					) }
					overlayStyle={ { maxWidth: 500 } }
				>
					<QuestionCircleOutlined className={ s.helpIcon } />
				</Tooltip>
			</div>
		</Modal>
	);
};

export default observer(TestConnectivityModal);
