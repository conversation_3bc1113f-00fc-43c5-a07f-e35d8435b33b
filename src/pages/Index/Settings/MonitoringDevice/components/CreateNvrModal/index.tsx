import React, { useState, useEffect } from 'react';
import { Modal, Form, Select, Input, Button, message, Popover } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { debounce } from 'lodash';
import { observer } from 'mobx-react';
import { 
	IndexMonitoringCheckRepeatDeviceNameApi,
	IndexMonitoringSaveMonitoringDeviceApi,
	IndexMonitoringEditMonitoringDeviceApi // 新增编辑接口
} from '@/apis/setting/monitoringDevice';
import videoMonitorStore from '@/stores/trade/videoMonitor';
import NVR_IP from '@/assets/image/index/nvr_ip.png';
import NVR_PORT from '@/assets/image/index/nvr_port.png';
import NVR_ACCOUNT from '@/assets/image/index/nvr_user.png';
import NVR_PASSWORD from '@/assets/image/index/nvr_password.png';

import s from './index.module.scss';

const { Option } = Select;

interface CreateNvrModalProps {
	visible: boolean;
	onCancel: () => void;
	onSuccess: () => void;
	editData?: any; // 新增
}

/**
 * 新建NVR存储设备模态框
 */
const CreateNvrModal: React.FC<CreateNvrModalProps> = ({
	visible,
	onCancel,
	onSuccess,
	editData
}) => {
	const { getBrandList } = videoMonitorStore;
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const [brandList, setBrandList] = useState<any[]>([]);
	const [checkingName, setCheckingName] = useState(false);

	// 获取设备品牌列表
	const fetchBrandList = async() => {
		const res = await getBrandList();
		if (res?.length) {
			setBrandList(res);
		}
	};

	const debounceCheckDeviceName = debounce(
		(deviceName: string, resolve: (v?: any) => void, reject: (reason?: any) => void) => {
			IndexMonitoringCheckRepeatDeviceNameApi({ deviceName })
				.then(() => resolve())
				.catch((error) => {
					if (error?.errorCode == 1200) {
						reject('设备名称已存在');
					} else {
						reject(error?.errorMessage || '校验设备名称失败');
					}
				});
		},
		500 // 500ms防抖
	);

	// 提交表单
	const handleSubmit = async() => {
		try {
			const values = await form.validateFields();
			setLoading(true);

			let params = {
				...values,
			};

			if (editData) {
				// 编辑模式，status保持原状态
				params = { ...editData, ...params, status: editData.status, id: editData.id };
				const response = await IndexMonitoringEditMonitoringDeviceApi(params);
				if (response) {
					message.success('编辑成功');
					form.resetFields();
					onSuccess();
				}
			} else {
				// 新增模式，status为0
				params = { ...params, status: 0 };
				const response = await IndexMonitoringSaveMonitoringDeviceApi(params);
				if (response) {
					message.success('创建成功');
					form.resetFields();
					onSuccess();
				}
			}
		} catch (error) {
			console.error(editData ? '编辑NVR存储设备失败:' : '创建NVR存储设备失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 取消操作
	const handleCancel = () => {
		form.resetFields();
		onCancel();
	};

	// 模态框显示时初始化数据
	useEffect(() => {
		if (visible) {
			fetchBrandList(); // 获取设备品牌列表
			if (editData) {
				form.setFieldsValue(editData);
			} else {
				form.resetFields();
			}
		}
	}, [visible]);

	return (
		<Modal
			title={ editData ? "编辑NVR存储设备" : "新建NVR存储设备" }
			visible={ visible }
			width={ 480 }
			destroyOnClose
			centered
			maskClosable={ false }
			keyboard={ false }
			okText="提交"
			cancelText="取消"
			onOk={ handleSubmit }
			onCancel={ handleCancel }
			confirmLoading={ loading }
		>
			<Form
				form={ form }
				className={ s.createNvrForm }
				labelCol={ { span: 8 } }
				wrapperCol={ { span: 16 } }
				// autoComplete="off" // 防止表单自动填充
			>
				{/* 设备品牌 */}
				<Form.Item
					label="设备品牌"
					name="brandCode"
					rules={ [{ required: true, message: '请选择设备品牌' }] }
				>
					<Select
						placeholder="请选择品牌"
						showSearch={ false }
						optionFilterProp="children"
						style={ { width: 240 } }
					>
						{brandList.map(brand => (
							<Option key={ brand.brandCode } value={ brand.brandCode }>
								{brand.brandName}
							</Option>
						))}
					</Select>
				</Form.Item>

				{/* NVR存储设备名称 */}
				<Form.Item
					label="NVR存储设备名称"
					name="deviceName"
					rules={ [
						{ required: true, message: '请输入设备名称' },
						{
							validator: (_, value) => {
								// 编辑模式下，只有名称有更改才校验
								if (!value) return Promise.resolve();
								if (editData && value === editData.deviceName) {
									return Promise.resolve();
								}
								return new Promise((resolve, reject) => {
									debounceCheckDeviceName(value, resolve, reject);
								});
							}
						}
					] }
				>
					<Input 
						placeholder="请输入设备名称" 
						style={ { width: 240 } }
						disabled={ checkingName }
						// onBlur={ handleDeviceNameChange }
					/>
				</Form.Item>

				{/* 内网服务器IP地址 */}
				<Form.Item
					label="内网服务器IP地址"
					name="deviceIp"
					rules={ [
						{ required: true, message: '请输入IP地址' },
						{ 
							pattern: /^(\d{1,3}\.){3}\d{1,3}$/, 
							message: '请输入正确的IP地址格式' 
						}
					] }
				>
					<div className={ s.inputWrapper }>
						<Form.Item name="deviceIp" noStyle>
							<Input 
								placeholder="请输入IP地址" 
								style={ { width: 240 } }
							/>
						</Form.Item>
						<Popover 
							content={ (
								<div>
									<img width={ 600 } src={ NVR_IP } alt="" style={ { objectFit: 'contain' } } />
								</div>
							) }
						>
							<QuestionCircleOutlined className={ s.helpIcon } />
						</Popover>
					</div>
				</Form.Item>

				{/* 内网端口 */}
				<Form.Item
					label="内网端口"
					name="devicePort"
					rules={ [
						{ required: true, message: '请输入内网端口' }
						// { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间' }
					] }
				>
					<div className={ s.inputWrapper }>
						<Form.Item name="devicePort" noStyle>
							<Input
								type="number"
								placeholder="请输入内网端口" 
								style={ { width: 240 } }
							/>
						</Form.Item>
						<Popover 
							content={ (
								<div>
									<img width={ 600 } src={ NVR_PORT } alt="" style={ { objectFit: 'contain' } } />
								</div>
							) }
						>
							<QuestionCircleOutlined className={ s.helpIcon } />
						</Popover>
					</div>
				</Form.Item>

				{/* 账户名 */}
				<Form.Item
					label="账户名"
					name="deviceAccountName"
					rules={ [
						{ required: true, message: '请输入账户名' },
						{ min: 1, max: 50, message: '账户名长度必须在1-50个字符之间' }
					] }
				>
					<div className={ s.inputWrapper }>
						<Form.Item name="deviceAccountName" noStyle>
							<Input 
								placeholder="请输入账户名" 
								style={ { width: 240 } }
								autoComplete="new-username"
							/>
						</Form.Item>
						<Popover 
							content={ (
								<div>
									<img width={ 600 } src={ NVR_ACCOUNT } alt="" style={ { objectFit: 'contain' } } />
								</div>
							) }
						>
							<QuestionCircleOutlined className={ s.helpIcon } />
						</Popover>
					</div>
				</Form.Item>

				{/* 验证密码 */}
				<Form.Item
					label="验证密码"
					name="deviceAccountPassword"
					rules={ [
						{ required: true, message: '请输入密码' }
						// { min: 6, max: 50, message: '密码长度必须在6-50个字符之间' }
					] }
				>
					<div className={ s.inputWrapper }>
						<Form.Item name="deviceAccountPassword" noStyle>
							<Input.Password 
								placeholder="请输入密码" 
								style={ { width: 240 } }
								autoComplete="new-password"
							/>
						</Form.Item>
						<Popover 
							content={ (
								<div>
									<img width={ 600 } src={ NVR_PASSWORD } alt="" style={ { objectFit: 'contain' } } />
								</div>
							) }
						>
							<QuestionCircleOutlined className={ s.helpIcon } />
						</Popover>
					</div>
				</Form.Item>
			</Form>
		</Modal>
	);
};

export default observer(CreateNvrModal); 