.monitoringRecord{
    .operation{
        :global{
            .ant-btn-sm{
                padding: 0 2px !important;
            }
        }
    }
    :global {
        .innerTableChange{
            position: relative!important;
            top: 0!important;
        }
        
        .art-table-header-row {
    
            .art-table-header-cell:last-of-type,
            // .art-table-header-cell:nth-child(1),
            .art-table-header-cell:nth-child(2) {
                .resize-handle {
                    display: block !important;
                }
            }
        }

        .art-table-header-cell.first > *:first-child {
			margin-left: 0 !important;
		}

        .rangeTime .ant-picker-input > input{
            font-size: 13px !important;
        }
    }
}