import React, { useCallback, useEffect, useRef, useState } from "react";
import { observer } from 'mobx-react';
import { Button, Form, Input, Select, Modal, Space, Switch } from "antd";
import { ExclamationCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useActivate } from 'react-activation';
import message from "@/components/message";
import userStore from '@/stores/user';
import videoMonitorStore from '@/stores/trade/videoMonitor';
import SearchTable from "@/components/SearchTableVirtual";
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import Icon from "@/components/Icon";
import { 
	IndexMonitoringQueryMonitoringDeviceApi, 
	IndexMonitoringEditMonitoringDeviceApi 
} from '@/apis/setting/monitoringDevice';
import s from "./index.module.scss";
import CreateNvrModal from '../components/CreateNvrModal';
import TestConnectivityModal from '../components/TestConnectivityModal';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from '@/utils/pointTrack/constants'; 

const { Option } = Select;

// 默认查询参数
const defaultParams = {
	deviceName: undefined, // NVR存储设备名称
	status: undefined, // 启用状态
};

/**
 * NVR存储设备管理
 */
const NvrDevice = observer((props) => {
	const { userInfo } = userStore;
	const { userId, subUserId } = userInfo || {};

	const { 
		checkKdzsPrintComponent, 
		checkKdzsPrintComponentStatus,
		isKdzsPrintComponent,
		sendMessage,
		disconnectWs,
		isSocketConnected,
		connectWs,
	} = videoMonitorStore;

	const [form] = Form.useForm();
	const ref = useRef<any>();

	const [loading, setLoading] = useState(false);
	const [formData, setFormData] = useState<any>({ ...defaultParams });
	const [dataSource, setDataSource] = useState([]); // 当前显示的数据
	const [createModalVisible, setCreateModalVisible] = useState(false);
	const [testModalVisible, setTestModalVisible] = useState(false);
	const [currentNvr, setCurrentNvr] = useState(null);
	const [editData, setEditData] = useState<any>(null); // 新增

	// 切换启用状态
	const handleStatusChange = async(checked: boolean, record: any) => {
		if (!checked) {
			Modal.confirm({
				title: '您确定要进行停用操作吗？',
				icon: <ExclamationCircleOutlined />,
				content: (
					<div className="r-c-error">
						停用录像机后，录像机对应的摄像头将无法使用，需要重新配置录像机后才可启用
					</div>
				),
				okText: '确认',
				cancelText: '取消',
				onOk: async() => {
					try {
						const params = {
							...record,
							id: record.id,
							status: 0
						};
                        
						const response = await IndexMonitoringEditMonitoringDeviceApi(params);
                        
						if (response) {
							message.success('关闭成功！');
							// 刷新数据
							ref.current?.submit();
						}
					} catch (error) {
						console.error('切换状态失败:', error);
					}
				}
			});
			return;
		}
        
		try {
			const params = {
				...record,
				id: record.id,
				status: checked ? 1 : 0
			};
			
			const response = await IndexMonitoringEditMonitoringDeviceApi(params);
			
			if (response) {
				message.success(checked ? '开启成功！' : '关闭成功！');
				// 刷新数据
				ref.current?.submit();
			}
		} catch (error) {
			console.error('切换状态失败:', error);
		}
	};

	// 表格列定义
	const columns: ColumnsType<any> = [
		{
			title: '序号',
			width: 50,
			dataIndex: 'index',
			render: (text, record, index) => {
				return index + 1;
			}
		},
		{
			title: 'NVR存储设备名称',
			width: 200,
			dataIndex: 'deviceName',
			render: (text, record) => {
				return (
					<span>
						{text}
					</span>
				);
			}
		},
		{
			title: '服务器地址',
			width: 200,
			dataIndex: 'deviceIp',
			render: (text) => {
				return text;
			}
		},
		{
			title: '端口',
			width: 200,
			dataIndex: 'devicePort',
			render: (text) => {
				return text;
			}
		},
		{
			title: '绑定摄像头数量',
			width: 200,
			dataIndex: 'deviceCameraNum',
			render: (text) => {
				return text || 0;
			}
		},
		{
			title: '启用状态',
			width: 200,
			dataIndex: 'status',
			render: (text, record) => {
				return (
					<Switch
						checked={ text === 1 }
						checkedChildren="开启"
						unCheckedChildren="关闭"
						onChange={ (checked) => handleStatusChange(checked, record) }
						size="default"
					/>
				);
			}
		},
		{
			title: '创建时间',
			width: 200,
			dataIndex: 'gmtCreated',
			render: (text) => {
				return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
			}
		},
		{
			title: '操作',
			width: 200,
			dataIndex: 'operation',
			render: (text, record) => {
				return (
					<Space size="small" className={ s.operation }>
						<Button type="link" size="small" onClick={ () => handleEditDevice(record) } style={ { color: '#1890ff', fontSize: 12 } }>
							编辑
						</Button>
						<Button type="link" size="small" onClick={ () => handleTestConnectivity(record) } style={ { color: '#1890ff', fontSize: 12 } }>
							测试连通
						</Button>
						<Button type="link" size="small" onClick={ () => handleDeleteDevice(record) } style={ { color: '#1890ff', fontSize: 12 } }>
							删除
						</Button>
					</Space>
				);
			}
		}
	];

	// 查询表单配置
	const FormFieldList: FormItemConfig[] = [
		{
			label: '',
			name: 'deviceName',
			children: (
				<Input 
					placeholder="NVR存储设备名称" 
					style={ { width: 160 } } 
					className={ formData.deviceName ? 'high-light-bg' : '' }
					allowClear
				/>
			)
		},
		{
			label: '',
			name: 'status',
			children: (
				<Select placeholder="启用状态" allowClear style={ { width: 160 } } className={ formData.status !== undefined ? 'high-light-bg' : '' }>
					<Option value={ 1 }>启用</Option>
					<Option value={ 0 }>禁用</Option>
				</Select>
			)
		}
	];

	// 编辑设备
	const handleEditDevice = (record: any) => {
		setEditData(record); // 传递当前编辑数据
		setCreateModalVisible(true);
	};

	// 测试连通性
	const handleTestConnectivity = (record: any) => {
		setCurrentNvr(record);
		setTestModalVisible(true);
	};

	// 删除设备
	const handleDeleteDevice = (record: any) => {
		Modal.confirm({
			title: '您确定要进行删除操作吗？',
			icon: <ExclamationCircleOutlined />,
			// content: `确定要删除设备"${record.deviceName}"吗？`,
			content: (
				<div className="r-c-error">
					删除录像机后，录像机对应的摄像头将无法使用，需要重新配置录像机后才可启用
				</div>
			),
			okText: '确认',
			cancelText: '取消',
			onOk: async() => {
				try {
					// 调用删除API，传入enableStatus=0
					const params = {
						...record,
						id: record.id,
						enableStatus: 0
					};
					
					const response = await IndexMonitoringEditMonitoringDeviceApi(params);
					
					if (response) {
						message.success('删除成功');
						ref.current?.submit();
					}
				} catch (error) {
					message.error('删除失败');
				}
			}
		});
	};

	// 新增设备
	const handleAddDevice = () => {
		sendPoint(Pointer.设置_监控设备管理_新建NVR存储设备_点击);
		setEditData(null); // 新增时清空
		setCreateModalVisible(true);
	};

	// 新建成功回调
	const handleCreateSuccess = () => {
		setCreateModalVisible(false);
		setEditData(null); // 关闭时清空
		ref.current?.submit();
	};

	// 取消新建
	const handleCancelCreate = () => {
		setCreateModalVisible(false);
		setEditData(null); // 关闭时清空
	};

	// 获取数据
	const fetchSystemList = async(info: any) => {
		console.log('%c [ info ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', info);
		const { pageNo, pageSize, ...rest } = info;

		// 无需分页
		const params = {
			...rest,
		};

		return IndexMonitoringQueryMonitoringDeviceApi(params);
	};

	// 数据适配器
	const responseAdapter = (data: any, params: any) => {
		const list = data || [];
		setDataSource(list);
		videoMonitorStore.setNvrDeviceList(list);
		return { list };
	};

	// 表单字段变化处理
	const onFieldsChange = (changedValues: any, allValues: any) => {
		setFormData(allValues);
	};

	// 表格额外功能区域
	const tableExtraFn = (sortNode: React.ReactNode) => {
		return (
			<div className="r-flex r-ai-c r-jc-sb r-mb-16">
				<Button size="middle" type="primary" onClick={ handleAddDevice }>
					新建NVR存储设备
				</Button>
			</div>
		);
	};

	const onReset = () => {
		form.resetFields();
		setFormData(defaultParams);
	};

	const init = async() => {
		await checkKdzsPrintComponent(); // 检测快递助手ERP聚合控件
		connectWs(); // 连接ERP聚合控件
	};

	useEffect(() => {
		init();

		return () => {
			disconnectWs();
		};
	}, []);

	return (
		<NormalLayout className="print-batch-search-con" id="nvrDevice">
			<div className={ s.nvrDevice }>
				<SearchTable
					ref={ ref }
					pageSizeId="NvrDeviceListTable"
					form={ form }
					fetchData={ fetchSystemList }
					responseAdapter={ responseAdapter }
					onReset={ onReset }
					searchBtnText="查询"
					resetBtnText="重置"
					showSearch
					hidePagination
					rowFormConfig={ {
						defaultParams,
						formList: FormFieldList,
						size: 'small',
						colProps: {},
					} }
					baseTableConfig={ {
						// dataSource,
						onFieldsChange,
						rowKey: 'id',
						columns,
						tableExtraFn,
						pagination: false,
						cachePgination: true,
						isStickyHeader: true,
						stickyTop: 92,
						headerColSet: {
							resizeId: `NvrDeviceListTable_width_${userStore?.userInfo?.userId}`,
						},
						enableRowClick: false
					} }
				/>
				
				{/* 新建NVR存储设备模态框 */}
				<CreateNvrModal
					visible={ createModalVisible }
					onCancel={ handleCancelCreate }
					onSuccess={ handleCreateSuccess }
					editData={ editData } // 传递
				/>

				{/* 测试连通性模态框 */}
				<TestConnectivityModal
					visible={ testModalVisible }
					record={ currentNvr } // 传入对应的record
					onCancel={ () => setTestModalVisible(false) }
				/>
			</div>
		</NormalLayout>
	);
});

export default NvrDevice;

