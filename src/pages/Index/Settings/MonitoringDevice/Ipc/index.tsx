import React, { useCallback, useEffect, useRef, useState } from "react";
import { observer } from 'mobx-react';
import { Button, Form, Input, Select, Modal, Space, Switch } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useActivate } from 'react-activation';
import message from "@/components/message";
import userStore from '@/stores/user';
import videoMonitorStore from '@/stores/trade/videoMonitor';
import SearchTable from "@/components/SearchTableVirtual";
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import Icon from "@/components/Icon";
import { 
	IndexMonitoringQueryDeviceCameraApi, 
	IndexMonitoringEditDeviceCameraApi 
} from '@/apis/setting/monitoringDevice';
import CreateIpcModal from '../components/CreateIpcModal';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from '@/utils/pointTrack/constants'; 
import s from "./index.module.scss";

const { Option } = Select;

// 默认查询参数
const defaultParams = {
	cameraName: undefined, // 摄像头名称
	cameraChannel: undefined, // 视频通道
	deviceName: undefined, // NVR存储设备名称
	status: undefined, // 启用状态
};

/**
 * IPC摄像头设备管理
 */
const IpcDevice = observer((props) => {
	const { userInfo } = userStore;
	const { userId, subUserId } = userInfo || {};

	const { 
		checkKdzsPrintComponent, 
		checkKdzsPrintComponentStatus,
		isKdzsPrintComponent,
		sendMessage,
		disconnectWs,
		isSocketConnected,
		connectWs,
	} = videoMonitorStore;

	const [form] = Form.useForm();
	const ref = useRef<any>();

	const [loading, setLoading] = useState(false);
	const [formData, setFormData] = useState<any>({ ...defaultParams });
	const [dataSource, setDataSource] = useState([]); // 当前显示的数据
	const [createModalVisible, setCreateModalVisible] = useState(false);
	const [editData, setEditData] = useState<any>(null); // 新增

	// 切换启用状态
	const handleStatusChange = async(checked: boolean, record: any) => {
		try {
			const params = {
				...record,
				id: record.id,
				status: checked ? 1 : 0,
				hideErrorMessage: true
			};
			
			const response = await IndexMonitoringEditDeviceCameraApi(params);
			
			if (response) {
				message.success(checked ? '开启成功！' : '关闭成功！');
				// 刷新数据
				ref.current?.submit();
			}
		} catch (error) {
			console.error('切换状态失败:', error);
			if (error?.errorCode === 1203) {
				Modal.error({
					title: 'IPC摄像头启用失败',
					content: (
						<div className="r-c-error">
							摄像头对应的录像机目前已关闭或不存在，请重新编辑后再试
						</div>
					),
					onOk: () => {
                        
					}
				});
			} else {
				message.error(error?.errorMessage || '切换状态失败');
			}
		}
	};

	// 表格列定义
	const columns: ColumnsType<any> = [
		{
			title: '序号',
			width: 50,
			dataIndex: 'index',
			render: (text, record, index) => {
				return index + 1;
			}
		},
		{
			title: '摄像头名称',
			width: 200,
			dataIndex: 'cameraName',
			render: (text, record) => {
				return (
					<span>
						{text}
					</span>
				);
			}
		},
		{
			title: '设备品牌',
			width: 200,
			dataIndex: 'deviceBrandName',
			render: (text) => {
				return text;
			}
		},
		{
			title: '视频通道',
			width: 200,
			dataIndex: 'cameraChannel',
			render: (text) => {
				return text;
			}
		},
		{
			title: 'NVR存储设备名称',
			width: 200,
			dataIndex: 'deviceName',
			render: (text) => {
				return text;
			}
		},
		{
			title: '启用状态',
			width: 200,
			dataIndex: 'status',
			render: (text, record) => {
				return (
					<Switch
						checked={ text === 1 }
						checkedChildren="启用"
						unCheckedChildren="禁用"
						onChange={ (checked) => handleStatusChange(checked, record) }
						size="default"
					/>
				);
			}
		},
		{
			title: '创建时间',
			width: 200,
			dataIndex: 'gmtCreated',
			render: (text) => {
				return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
			}
		},
		{
			title: '操作',
			width: 200,
			dataIndex: 'operation',
			render: (text, record) => {
				return (
					<Space size="small" className={ s.operation }>
						<Button type="link" size="small" onClick={ () => handleEditDevice(record) } style={ { color: '#1890ff', fontSize: 12 } }>
							编辑
						</Button>
						<Button type="link" size="small" danger onClick={ () => handleDeleteDevice(record) } style={ { color: '#1890ff', fontSize: 12 } }>
							删除
						</Button>
					</Space>
				);
			}
		}
	];

	// 查询表单配置
	const FormFieldList: FormItemConfig[] = [
		{
			label: '',
			name: 'cameraName',
			children: (
				<Input 
					placeholder="摄像头名称" 
					style={ { width: 160 } } 
					className={ formData.cameraName ? 'high-light-bg' : '' }
					allowClear
				/>
			)
		},
		{
			label: '',
			name: 'cameraChannel',
			children: (
				<Input 
					placeholder="视频通道" 
					style={ { width: 160 } } 
					className={ formData.cameraChannel ? 'high-light-bg' : '' }
					allowClear
				/>
			)
		},
		{
			label: '',
			name: 'deviceName',
			children: (
				<Input 
					placeholder="NVR存储设备名称" 
					style={ { width: 160 } } 
					className={ formData.deviceName ? 'high-light-bg' : '' }
					allowClear
				/>
			)
		},
		{
			label: '',
			name: 'status',
			children: (
				<Select placeholder="启用状态" allowClear style={ { width: 160 } } className={ formData.status !== undefined ? 'high-light-bg' : '' }>
					<Option value={ 1 }>启用</Option>
					<Option value={ 0 }>禁用</Option>
				</Select>
			)
		}
	];

	// 编辑设备
	const handleEditDevice = (record: any) => {
		setEditData(record); // 传递当前编辑数据
		setCreateModalVisible(true);
	};

	// 删除设备
	const handleDeleteDevice = (record: any) => {
		Modal.confirm({
			title: '确认删除',
			icon: <ExclamationCircleOutlined />,
			content: `确定要删除摄像头"${record.cameraName}"吗？`,
			okText: '确认',
			cancelText: '取消',
			onOk: async() => {
				try {
					// 调用删除API，传入enableStatus=0
					const params = {
						...record,
						id: record.id,
						enableStatus: 0
					};
					
					const response = await IndexMonitoringEditDeviceCameraApi(params);
					
					if (response) {
						message.success('删除成功');
						ref.current?.submit();
					}
				} catch (error) {
					console.log('删除失败', error);
				}
			}
		});
	};

	// 新增设备
	const handleAddDevice = () => {
		sendPoint(Pointer.设置_监控设备管理_新建IPC摄像头_点击);
		setEditData(null); // 新增时清空
		setCreateModalVisible(true);
	};

	// 新建成功回调
	const handleCreateSuccess = () => {
		setCreateModalVisible(false);
		setEditData(null); // 关闭时清空
		ref.current?.submit();
	};

	// 取消新建
	const handleCancelCreate = () => {
		setCreateModalVisible(false);
		setEditData(null); // 关闭时清空
	};

	// 获取数据
	const fetchSystemList = async(info: any) => {
		console.log('%c [ info ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', info);
		const { pageNo, pageSize, ...rest } = info;

		// 无需分页
		const params = {
			...rest,
			cameraChannel: info?.cameraChannel,
			cameraName: info?.cameraName,
			deviceName: info?.deviceName,
			status: info?.status,
		};

		return IndexMonitoringQueryDeviceCameraApi(params);
	};

	// 数据适配器
	const responseAdapter = (data: any, params: any) => {
		const list = data || [];
		setDataSource(list);
		videoMonitorStore.setIpcDeviceList(list);
		return { list };
	};

	// 表单字段变化处理
	const onFieldsChange = (changedValues: any, allValues: any) => {
		setFormData(allValues);
	};

	// 表格额外功能区域
	const tableExtraFn = (sortNode: React.ReactNode) => {
		return (
			<div className="r-flex r-ai-c r-jc-sb r-mb-16">
				<Button size="middle" type="primary" onClick={ handleAddDevice }>
					新建IPC摄像头
				</Button>
			</div>
		);
	};

	const onReset = () => {
		form.resetFields();
		setFormData(defaultParams);
	};

	const init = async() => {
		await checkKdzsPrintComponent(); // 检测快递助手ERP聚合控件
		connectWs(); // 连接ERP聚合控件
	};

	useEffect(() => {
		init();

		return () => {
			disconnectWs();
		};
	}, []);

	return (
		<NormalLayout className="print-batch-search-con" id="ipcDevice">
			<div className={ s.ipcDevice }>
				<SearchTable
					ref={ ref }
					pageSizeId="IpcDeviceListTable"
					form={ form }
					fetchData={ fetchSystemList }
					responseAdapter={ responseAdapter }
					onReset={ onReset }
					searchBtnText="查询"
					resetBtnText="重置"
					showSearch
					hidePagination
					rowFormConfig={ {
						defaultParams,
						formList: FormFieldList,
						size: 'small',
						colProps: {},
					} }
					baseTableConfig={ {
						// dataSource,
						onFieldsChange,
						rowKey: 'id',
						columns,
						tableExtraFn,
						pagination: false,
						cachePgination: true,
						isStickyHeader: true,
						stickyTop: 92,
						headerColSet: {
							resizeId: `IpcDeviceListTable_width_${userStore?.userInfo?.userId}`,
						},
						enableRowClick: false
					} }
				/>
				
				{/* 新建IPC摄像头模态框 */}
				<CreateIpcModal
					visible={ createModalVisible }
					onCancel={ handleCancelCreate }
					onSuccess={ handleCreateSuccess }
					editData={ editData } // 传递
				/>
			</div>
		</NormalLayout>
	);
});

export default IpcDevice;
