import React, { ReactElement, useEffect } from "react";
import { Route, RouteProps, Switch, Redirect } from "react-router-dom";
import KeepAlive from "react-activation";
import { observer } from "mobx-react";
import SubMenuLayout from "@/components-biz/layouts/SubMenuLayout";
import settingsMonitoringDeviceSubRoutes from "@/routers/settingsMonitoringDeviceSubRoutes";

const Order = () => {
	return (
		<SubMenuLayout
			menus={ settingsMonitoringDeviceSubRoutes }
			bodyStyle={ { background: 'transparent' } }
			navStyle={ { 
				marginBottom: 0, 
			} }
		>
			<Switch>
				{settingsMonitoringDeviceSubRoutes.map(
					({ path, exact, component: Component }): ReactElement<RouteProps> => {
						return (
							<Route
								key={ path }
								exact={ exact }
								path={ path }
								render={ (props: any) => (
									<KeepAlive name={ path }>
										<Component { ...props } />
									</KeepAlive>
								) }
							/>
						);
					}
				)}
			</Switch>
		</SubMenuLayout>
	);
};

export default observer(Order);
