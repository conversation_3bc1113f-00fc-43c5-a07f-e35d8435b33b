import React, { useC<PERSON>back, useEffect, useImperative<PERSON><PERSON><PERSON>, useRef, useState } from "react";
import { Button, Checkbox, Input, InputNumber, Modal, Popover, Select, Spin, Switch, Table, Tag, Tooltip, message } from "antd";
import { observer } from "mobx-react";
import cs from "classnames";
import { CheckOutlined, CloseOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import { useHistory } from "react-router-dom";
import s from './index.module.scss';
import InputPrice from '@/components/Input/InputNumber/InputPrice';
import { PLAT_ALI, PLAT_C2M, PLAT_DW, PLAT_FXG, PLAT_JD, PLAT_KS, PLAT_MAP, PLAT_OTHER, PLAT_PDD, PLAT_SPH, PLAT_TB, PLAT_TM, PLAT_XHS, PLAT_YZ, PLAT_KTT } from "@/constants";
import userStore from "@/stores/user";
import AddProducts from "./AddProducts";
import SelectAreaModal from "./SelectAreaModal";
import SelectShopModal from "@/pages/Warehouse/StockSync/AutoStockSync/components/AddRuleModal/SelectShopModal";
import useGetState from "@/utils/hooks/useGetState";
import PlatformIcon from "@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon";
import memoFn from "@/libs/memorizeFn";
import { ItemSmartExpressInsertOrUpdateApi, ItemSmartExpressUpdateItemGoodsListApi } from "@/apis/setting/smartExpress";
import { ItemSmartExpressUpdateItemGoodsListRequest } from "@/types/schemas/setting/smartExpress";
import { ItemSysItemListOfItemRelationPlatformItemViewApi, SysItemGetSysItemListApi } from "@/apis/warehouse/system";
import { weightUnit } from "../../../System/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { tradeStore } from "@/stores";

enum AreaSelectedType {
	优先省份 = 1,
	指定省份 = 2,
}
interface Props {
	onOk?:()=>void,
	onCancel?:()=>void,
	// cRef:React.MutableRefObject<any>,
	data:{[k:string]:any}
}

interface ItemSaveZeroStockItem {
	id?: number;
    smartExpressTemplateConfigId?: number;
    sellerNick?: string;
    sellerId?: number;
    title?: string;
    platform?: string;
    numId?: number;
    skuId?: number;
    skuName?: string;
    outerId?: string;
    ruleType?: number;
    includeType?: number;
    [k: string]: any;
}
interface ItemSaveStockItem {
	id?: number;
    smartExpressTemplateConfigId?: number;
    sysItemId?: number;
    sysSkuId?: number;
    sysItemAlias?: string;
    sysSkuName?: string;
    sysSkuOuterId?: string;
    ruleType?: number;
    includeType?: number;
    [k: string]: any;
}

interface ItemSaveProps {
	needUpdateItemVoList?: ItemSaveZeroStockItem,
	needUpdateGoodsVoList?:ItemSaveStockItem,
	needDeleteItemVoList?:ItemSaveZeroStockItem,
	needDeleteGoodsVoList?:ItemSaveStockItem
}
interface ItemSaveKeys{
	matchRuleIncludeItem: ItemSaveProps
	matchRuleExcludeItem: ItemSaveProps
	priorityItem: ItemSaveProps
	[k: string]: any;
}


const RulesManager = (props:Props) => {
	const { onOk, onCancel, data } = props;
	const { isShowZeroStockVersion, userSetting } = userStore;
	const { expressTemplateList } = tradeStore;
	const isKg = userSetting?.weightUnit == weightUnit.显示kg;
	const history = useHistory();
	const addProductsComp = useRef(); // 添加商品
	const yesProductsComp = useRef(); // 指定商品
	const notProductsComp = useRef(); // 禁止商品
	const [supportPlatformOptions, setSupportPlatformOptions] = useState([]);
	const [supportPlatformValues, setSupportPlatformValues] = useState([]);
	const [priorityLogisticsFeeSwitch, setPriorityLogisticsFeeSwitch] = useState(data.priorityLogisticsFee);
	const [showSelectedAreaModal, setShowSelectedAreaModal] = useState(false);
	const [showCity, setShowCity] = useState(false); // 选择区域时，是否展示"市"
	const [showDistrict, setShowDistrict] = useState(false); // 选择区域时，是否展示"区"
	const [areaSelectedType, setAreaSelectedType] = useState<AreaSelectedType>();
	const [disabledArea, setDisabledAddrea] = useState([]);
	const [prioritizeProvinceList, setPrioritizeProvinceList] = useState([]); // 优先的省份列表
	const [designatedProvinceList, setDesignatedProvinceList] = useState([]); // 指定省份
	const [priorityValue, setPriorityValue] = useState(data.priority); // 优先级
	const [reachableExpressSwitch, setReachableExpressSwitch] = useState(data.hasOpenedReachableExpress); // 快递可达
	const [limitRuleSwitch, setLimitRuleSwitch] = useState(data.hasOpenedLimitRuleConfig); // 匹配规则
	const [selectedShopData, setSelectedShopData, getSelectedShopData] = useGetState({ visible: false, data: [] }); // 指定店铺
	const [expressCompanyList, setExpressCompanyList] = useState([]); // 快递公司列表
	const [minAmount, setMinAmount] = useState(''); // 订单金额
	const [maxAmount, setMaxAmount] = useState(''); // 订单金额
	const [minWeight, setMinWeight] = useState(''); // 订单重量
	const [maxWeight, setMaxWeight] = useState(''); // 订单重量
	const [memoExpressMatch, setMemoExpressMatch] = useState(data?.limitRuleConfig?.memoExpressMatch); // 留言备注
	const [bannedAreaValue, setBannedAreaValue] = useState(data?.limitRuleConfig?.bannedArea); // 禁止地区
	const [assignExpress, setAssignExpress] = useState(data?.limitRuleConfig?.assignExpress); // 指定快递 (隐藏)
	const [loading, setLoading] = useState(false);
	const [originItemList, setOriginItemList] = useState([]); // 优先商品-零库存版商品
	const [originSysItemList, setOriginSysItemList] = useState([]); // // 优先商品-库存版货品
	const [yesOriginItemList, setYesOriginItemList] = useState([]); // 指定商品-零库存版商品
	const [yesOriginSysItemList, setYesOriginSysItemList] = useState([]); // // 指定商品-库存版货品
	const [notOriginItemList, setNotOriginItemList] = useState([]); // 禁止商品-零库存版商品
	const [notOriginSysItemList, setNotOriginSysItemList] = useState([]); // // 禁止商品-库存版货品
	const [smartExpressObj, setSmartExpressObj, getSmartExpressObj] = useGetState({}); // 快递模板

	// 初始化数据
	const initData = () => {
		handlePrioritizeProvince(); // 优先省份和指定省份
		getExpressCompanyList(); // 获取快递公司列表
		handleAssignShopList(); // 获取指定店铺
		handleMatchGoodsInfo(); // 匹配规则和优先规则中的商品货品信息处理
		handleWeight(); // 订单重量
		handleAmount(); // 订单金额
	};

	/**
	 * 指定商品、禁止商品、优先商品
	 * 规则中的商品货品信息处理注意点：
	 * 1. 前端需要根据接口中的货品或者商品信息，重新去调用接口查询获取最新的商品货品信息
	 * 2. 查不到的商品货品表格需要展示“已失效”
	 * 3. 用户删除商品或者新增商品时，分别提取出来，放在不同的字段里面返回给后端
	 * 4. 原商品删除再新增，不算被操作
	 * 5. 需要区分库存版和零库存板
	 */
	const handleMatchGoodsInfo = async() => {
		const { matchRuleIncludeItem = {}, matchRuleExcludeItem = {}, priorityItem = {} } = data || {}; // matchRuleIncludeItem包含商品 matchRuleExcludeItem不包含商品 priorityItem 优先商品
		const { itemVoList: yesItemList = [], goodsVoList: yesGoodsList = [] } = matchRuleIncludeItem || {};
		const { itemVoList: notItemList = [], goodsVoList: notGoodsList = [] } = matchRuleExcludeItem || {};
		const { itemVoList: addItemList = [], goodsVoList: addGoodsList = [] } = priorityItem || {};

		if (isShowZeroStockVersion) {
			await handleOriginItemListInfo(yesItemList, 'yes');
			await handleOriginItemListInfo(notItemList, 'not');
			await handleOriginItemListInfo(addItemList, 'add');
		} else {
			await handleOriginSysItemListInfo(yesGoodsList, 'yes');
			await handleOriginSysItemListInfo(notGoodsList, 'not');
			await handleOriginSysItemListInfo(addGoodsList, 'add');
		}
	};

	// 处理零库存版的商品
	const handleOriginItemListInfo = async(list, type:string = 'add') => {
		if (list.length) {
			const numIids = [];
			const skuIds = [];
			list.forEach(itemInfo => {
				numIids.push(itemInfo.numId);
				skuIds.push(itemInfo.skuId);
			});
			let newList = list;
			await ItemSysItemListOfItemRelationPlatformItemViewApi({
				numIids,
				skuIds,
				pageSize: skuIds.length
			}).then(res => {
				if (res.list) {
					newList = handleItemList(res.list);
				}
			});
			if (type == 'yes') {
				setYesOriginItemList(newList);
			} else if (type == 'not') {
				setNotOriginItemList(newList);
			} else {
				setOriginItemList(newList);
			}
		}

		function handleItemList(itemList) {
			/**
			 * 需要将查询到的最新的商品信息更新到规则原本的商品信息中，
			 * 但是原本规则中的的商品可能会存在被删除的情况，这种情况下，已删除的商品依然展示，只不过没法更新信息
			 */
			
			const items = {}; // 用numIid+skuId作为key把每个sku的信息存下来，这样可以降低商品信息更新的时间复杂度
			itemList.forEach(item => {
				item.platformItemSkuList?.forEach(skuInfo => {
					const key = `${item.numIid}_${skuInfo.skuId}`;
					items[key] = {
						title: item.title,
						numIid: item.numIid,
						outerId: item.outerId,
						...skuInfo,
					};
				});
			});
			const newList = list.map(i => {
				const key = `${i.numId}_${i.skuId}`;
				const newItemInfo = items[key];
				// 如果查到了最新的货品信息
				if (newItemInfo) {
					return ({
						...i,
						sellerNick: newItemInfo.sellerNick,
						sellerId: newItemInfo.sellerId,
						title: newItemInfo.title,
						platform: newItemInfo.platform,
						numId: newItemInfo.numIid,
						skuId: newItemInfo.skuId,
						skuName: newItemInfo.skuName,
						outerId: newItemInfo.outerId,
					});
				}
				// 如果未查到最新的货品信息，说明已经被删除了
				return ({
					...i,
					isDeleted: true
				});
			});
			return newList;

		}
		
	};

	// 处理库存版的货品
	const handleOriginSysItemListInfo = async(list, type:string = 'add') => {
		if (list.length) {
			let newList = list;
			const sysSkuIdList = list.map(i => i.sysSkuId);
			await SysItemGetSysItemListApi({
				sysSkuIdList,
				pageSize: sysSkuIdList.length
			}).then(res => {
				if (res.list) {
					newList = handleSysItemList(res.list);
				}
			});
			
			if (type == 'yes') {
				setYesOriginSysItemList(newList);
			} else if (type == 'not') {
				setNotOriginSysItemList(newList);
			} else {
				setOriginSysItemList(newList);
			}
		}
		

		function handleSysItemList(sysItemList) {
			/**
			 * 需要将查询到的最新的货品信息更新到规则原本的货品信息中，
			 * 但是原本规则中的的货品可能会存在被删除的情况，这种情况下，已删除的货品依然展示，只不过没法更新信息
			 */
			
			const sysItems = {}; // 用sysSkuId作为key把每个sku的信息存下来，这样可以降低货品信息更新的时间复杂度
			sysItemList.forEach(item => {
				item.sysSkuList.forEach(sysSkuInfo => {
					sysItems[sysSkuInfo.sysSkuId] = sysSkuInfo;
				});
			});
			const newList = list.map(i => {
				const newSysItemInfo = sysItems[i.sysSkuId];
				// 如果查到了最新的货品信息
				if (newSysItemInfo) {
					return ({
						...i,
						sysItemId: newSysItemInfo.sysItemId,
						sysSkuId: newSysItemInfo.sysSkuId,
						sysItemAlias: newSysItemInfo.sysItemAlias,
						sysSkuName: newSysItemInfo.sysSkuName,
						sysSkuOuterId: newSysItemInfo.skuOuterId
					});
				}
				// 如果未查到最新的货品信息，说明已经被删除了
				return ({
					...i,
					isDeleted: true
				});
			});
			return newList;

		}
		
	};

	const handleWeight = () => {
		let showMinWeight = data?.limitRuleConfig?.minWeight;
		let showMaxWeight = data?.limitRuleConfig?.maxWeight;
		if (showMinWeight) {
			showMinWeight = isKg ? (showMinWeight / 1000).toFixed(3) : showMinWeight;
		}
		if (showMaxWeight) {
			showMaxWeight = isKg ? (showMaxWeight / 1000).toFixed(3) : showMaxWeight;
		}
		setMinWeight(showMinWeight);
		setMaxWeight(showMaxWeight);
	};

	const handleAmount = () => {
		let showMinAmount = data?.limitRuleConfig?.minAmount;
		let showMaxAmount = data?.limitRuleConfig?.maxAmount;
		if (showMinAmount) {
			showMinAmount = (showMinAmount / 100).toFixed(2);
		}
		if (showMaxAmount) {
			showMaxAmount = (showMaxAmount / 100).toFixed(2);
		}
		setMinAmount(showMinAmount);
		setMaxAmount(showMaxAmount);
	};

	/** ------------ 获取快递公司列表 -------------- */
	const getExpressCompanyList = () => {
		memoFn.getExpressList().then(res => {
			if (res && res.length > 0) {
				setExpressCompanyList(res);
			}
		});
	};
	/** ----------------------------------------- */
	
	/** ------------ 支持平台 -------------- */
	const handleSupportPlatform = () => {
		console.log('datadata', data);
		const curTemplateInfo = getSmartExpressObj()[data.printTemplateId];
		let defaultPlatformList = [PLAT_SPH, PLAT_XHS, PLAT_DW, PLAT_KTT, PLAT_OTHER];

		// 视频号、有赞不能打手工单，其他平台都可以
		if ([PLAT_YZ, PLAT_SPH].includes(curTemplateInfo.platform)) {
			defaultPlatformList = [];
		} 
		// 获取接口返回的当前模板支持的平台
		const originTemplatePlatform = data?.suitPlatform?.map(platform => platform.toLocaleLowerCase());
		// 处理支持的平台列表
	
		// const templateToPlatformList = [];
		// templatePlatform?.forEach(platform => {
		// 	if ([PLAT_TB, PLAT_ALI, PLAT_C2M].includes(platform)) {
		// 		templateToPlatformList.push(PLAT_TB, PLAT_ALI, PLAT_C2M);
		// 	} else if (!defaultPlatformList.includes(platform)) {
		// 		templateToPlatformList.push(platform);
		// 	}
		// });
		// let validTemplateToPlatformList = [...new Set(templateToPlatformList)];
		let newPlatList = [...originTemplatePlatform];
		console.log('curTemplateInfo', curTemplateInfo);
		
		if (curTemplateInfo?.platform) {
			newPlatList.unshift(curTemplateInfo.platform);
		}
		if (curTemplateInfo?.platform === PLAT_TB) {
			newPlatList.unshift(PLAT_ALI, PLAT_C2M);
		}
		// 网点电子面单创建的时候，平台不受限制，所以把全平台给后端
		if (curTemplateInfo?.KddType == 2) {
			newPlatList = [PLAT_JD, PLAT_KS, PLAT_FXG, PLAT_PDD, PLAT_TB, PLAT_ALI, PLAT_C2M].concat(defaultPlatformList);
		}

		// 把默认平台剔除掉，再把默认平台concat，这样可以保证默认平台始终处于队列末尾
		newPlatList = newPlatList.filter(i => !defaultPlatformList.includes(i)).concat(defaultPlatformList);
		let validTemplateToPlatformList = [...new Set(newPlatList)];
		const options = validTemplateToPlatformList
			.map(platform => (
				{
					label: PLAT_MAP[platform],
					value: platform,
					// disabled: validTemplateToPlatformList.includes(platform)
				}
			));
		setSupportPlatformValues(originTemplatePlatform);
		setSupportPlatformOptions(options);
	};
	const supportPlatformOnChange = (v) => {
		setSupportPlatformValues(v);
		
	};

	/** ----------------------------------- */

	/** ------------ 指定店铺 -------------- */
	const handleAssignShopList = () => {
		if (data?.limitRuleConfig?.assignShopList?.length) {
			setSelectedShopData({
				data: data.limitRuleConfig.assignShopList,
				visible: false
			}); 
		}	
	};
	/** ----------------------------------- */


	/** ------------ 优先省份、指定省份 -------------- */
	const handlePrioritizeProvince = () => {
		if (data.priorityProvince) {
			const oldProvinceList = data.priorityProvince.split(',');
			const prioritizeProvinceList = oldProvinceList.map(provinceName => ({ name: provinceName }));
			setPrioritizeProvinceList(prioritizeProvinceList);
		}
		// 指定省份
		if (data?.limitRuleConfig?.priorityProvince) {
			const oldProvinceList = data?.limitRuleConfig?.priorityProvince?.split(',') || [];
			const prioritizeProvinceList = oldProvinceList.map(provinceName => ({ name: provinceName }));
			setDesignatedProvinceList(prioritizeProvinceList);
		}
	};

	// 添加省份
	const onAddProvince = () => {
		sendPoint(Pointer.智选快递_自动匹配规则_添加省份);
		setAreaSelectedType(AreaSelectedType.优先省份);
		setShowSelectedAreaModal(true);
		setShowDistrict(false);
		setShowCity(false);
	};

	const provinceOnDelete = (provinceName, type) => {
		if (type == 'yes') {
			const newList = designatedProvinceList.filter(province => province.name !== provinceName);
			setDesignatedProvinceList(newList);
		} else {
			const newList = prioritizeProvinceList.filter(province => province.name !== provinceName);
			setPrioritizeProvinceList(newList);
		}
	};

	const getPrioritizeProvinceList = (type: string = 'add') => {
		let list = type == 'yes' ? designatedProvinceList : prioritizeProvinceList;
		const tagList = list.map(province => (
			<Tag
				className="r-mb-4"
				key={ province.name }
				closable
				onClose={ () => provinceOnDelete(province.name, type) }
			>{province.name}
			</Tag>
		));
		return tagList;
	};
	/** ----------------------------------- */

	/** ----------------- 指定省份  ------------------ */
	const onAddForbiddeArea = () => {
		setAreaSelectedType(AreaSelectedType.指定省份);
		setShowSelectedAreaModal(true);
		setShowDistrict(false);
		setShowCity(false);
	};

	// 省份拼接为字符串 （保留着）
	const getAreaStrByAreaObj = (areaList) => {
		let addressString = "";
		try {
			const provinceStrList = [];
			areaList.forEach(province => {
				let provinceStr = province.name;
				const citiesList = province.childList;
				// const citiesStrList = [];
				if (citiesList.length) {
					const citiesStr = citiesList.map(city => {
						let cityStr = city.name;
						const districtList = city.childList;
						if (districtList.length) {
							const districtStr = districtList.map(district => district.name).join("，");
							cityStr = `${city.name} ( ${districtStr} )`;
						}
						// citiesStrList.push(cityStr);
						return cityStr;
					}).join("，");
					provinceStr = `${province.name}（${citiesStr}）`;
				}
				provinceStrList.push(provinceStr);
				
			});
			addressString = provinceStrList.join("、");
		} catch (e) {
			addressString = "";
		}
		return addressString;
	};

	const getDesignatedProvinceList = () => {
		const value = getAreaStrByAreaObj(designatedProvinceList);

		if (!value) {
			return;
		}
		return <Input.TextArea disabled autoSize={ { minRows: 2, maxRows: 6 } } value={ value } />;
	};
	/** --------------------------------------------- */

	/** ------------ 区域选择弹窗相关逻辑 -------------- */
	const selectedPrioritizeProvinceOnOk = (selectedArea) => {
		setShowSelectedAreaModal(false);
		setPrioritizeProvinceList(selectedArea);
	};

	// 指定省份确定
	const selectedForbiddeAreaOnOk = (selectedArea) => {
		setShowSelectedAreaModal(false);
		setDesignatedProvinceList(selectedArea);
	};

	const selectAreaModalOnCancel = () => {
		setShowSelectedAreaModal(false);
	};
	
	/** -------------------------------------------- */


	/** -------------- 店铺选择 --------------- */

	const selectedShopOnClick = () => {
		sendPoint(Pointer.智选快递_自动匹配规则_添加店铺);
		setSelectedShopData({
			...selectedShopData,
			visible: true
		});
	};

	const selectShopModalOnOk = (shopInfoList) => {
		setSelectedShopData({
			data: shopInfoList,
			visible: false
		});
	};

	const selectShopModalOnCancel = () => {
		setTimeout(() => {
			setSelectedShopData({
				...getSelectedShopData(),
				visible: false
			});	
		});
			
	};

	const shopOnDelete = (shopInfo) => {
		const newShopInfoList = getSelectedShopData().data?.filter(shop => shop.sellerId !== shopInfo.sellerId);
		setSelectedShopData({
			...getSelectedShopData(),
			data: newShopInfoList
		});
	};

	const getSelectedShopList = () => {
		const tagList = selectedShopData.data?.map(shopInfo => (
			<div className={ s["shop-tag-container"] }>
				<Tag closable key={ shopInfo.sellerId } onClose={ () => shopOnDelete(shopInfo) } >
					<PlatformIcon platform={ shopInfo.platform } />
					<span style={ { marginLeft: 0 } }>
						{shopInfo.sellerNick}
					</span>
					<span className="r-c-error r-ml-4">{shopInfo.tokenStatus === 0 ? "( 授权过期 )" : ""}</span>
				</Tag>
			</div>
			
		));
		return tagList;
	};

	/** -------------------------------------- */
	/** ----------------- 金额重量  ------------------ */
	const onMinAmountChange = (v) => {
		setMinAmount(v);
	};
	const onMaxAmountChange = (v) => {
		setMaxAmount(v);
	};
	const minWeightOnChange = (v) => {
		setMinWeight(v);
	};
	const maxWeightOnChange = (v) => {
		setMaxWeight(v);
	};

	const onMemoExpressMatchChange = (e) => {
		sendPoint(Pointer.智选快递_自动匹配规则_勾选留言备注);
		const isChecked = e.target.checked;
		setMemoExpressMatch(isChecked);
	};

	// 禁止地区
	const onBannedAreaChange = (e) => {
		const value = e.target.value;
		setBannedAreaValue(value);
	};
	
	/** --------------------------------------------- */



	// 获取更新商品的数据
	const getGoodsInfoForMatch = (list:any[], type:string) => {
		const needUpdateItemVoList = [];
		const needUpdateGoodsVoList = [];
		const needDeleteItemVoList = [];
		const needDeleteGoodsVoList = [];

		let sysItemList = [];
		let itemList = [];
		switch (type) {
			case 'add':
				sysItemList = originSysItemList;
				itemList = originItemList;
				break;
			case 'yes':
				sysItemList = yesOriginSysItemList;
				itemList = yesOriginItemList;
				break;
			case 'not':
				sysItemList = notOriginSysItemList;
				itemList = notOriginItemList;
				break;
			default:
				break;
		}
		
		if (!isShowZeroStockVersion) {
			const originSysItemIdList = sysItemList.map(i => i.sysSkuId); 
			const newSysItemIdList = [];
			// 找出新增的货品
			list.forEach(sysItemInfo => {
				newSysItemIdList.push(sysItemInfo.sysSkuId);
				if (!originSysItemIdList.includes(sysItemInfo.sysSkuId)) {
					const item = {
						smartExpressTemplateConfigId: data.id,
						sysItemId: sysItemInfo.sysItemId,
						sysSkuId: sysItemInfo.sysSkuId,
						sysItemAlias: sysItemInfo.sysItemAlias,
						sysSkuName: sysItemInfo.sysSkuName,
						sysSkuOuterId: sysItemInfo.skuOuterId
					};
					needUpdateGoodsVoList.push(item);
				}
			});
			// 找出删除的货品
			sysItemList.forEach(sysItemInfo => {
				if (!newSysItemIdList.includes(sysItemInfo.sysSkuId)) {
					const item = {
						id: sysItemInfo.id,
						smartExpressTemplateConfigId: data.id,
						sysItemId: sysItemInfo.sysItemId,
						sysSkuId: sysItemInfo.sysSkuId,
						sysItemAlias: sysItemInfo.sysItemAlias,
						sysSkuName: sysItemInfo.sysSkuName,
						sysSkuOuterId: sysItemInfo.skuOuterId
					};
					needDeleteGoodsVoList.push(item);
				}
			});

		} else {
			const itemIdList = itemList.map(skuInfo => `${skuInfo.numId}_${skuInfo.skuId}`);
			const newItemIdList = [];
			// 找出新增的商品
			list.forEach(itemInfo => {
				newItemIdList.push(`${itemInfo.numId}_${itemInfo.skuId}`);
				if (!itemIdList.includes(`${itemInfo.numId}_${itemInfo.skuId}`)) {
					const item = {
						smartExpressTemplateConfigId: data.id,
						sellerNick: itemInfo.sellerNick,
						sellerId: itemInfo.sellerId,
						title: itemInfo.title,
						platform: itemInfo.platform,
						numId: itemInfo.numId,
						skuId: itemInfo.skuId,
						skuName: itemInfo.skuName,
						outerId: itemInfo.outerId,
					};
					needUpdateItemVoList.push(item);
				}
				
			});
			// 找出删除的商品
			itemList.forEach(itemInfo => {
				if (!newItemIdList.includes(`${itemInfo.numId}_${itemInfo.skuId}`)) {
					const item = {
						id: itemInfo.id,
						smartExpressTemplateConfigId: data.id,
						sellerNick: itemInfo.sellerNick,
						sellerId: itemInfo.sellerId,
						title: itemInfo.title,
						platform: itemInfo.platform,
						numId: itemInfo.numId,
						skuId: itemInfo.skuId,
						skuName: itemInfo.skuName,
						outerId: itemInfo.outerId,
					};
					needDeleteItemVoList.push(item);
				}
			});
		}

		return {
			needUpdateItemVoList,
			needUpdateGoodsVoList,
			needDeleteItemVoList,
			needDeleteGoodsVoList,
		};
	};
	const handleGoodsInfo = () => {	
		let params:ItemSaveKeys = {
			id: data?.id,
			priorityItem: {},
			matchRuleIncludeItem: {},
			matchRuleExcludeItem: {},
		}; 
		
		const addList = addProductsComp?.current?.getProductList || [];
		const yesList = yesProductsComp?.current?.getProductList || [];
		const notList = notProductsComp?.current?.getProductList || [];

		params = {
			...params,
			matchRuleIncludeItem: getGoodsInfoForMatch(yesList, 'yes'), // 指定商品
			matchRuleExcludeItem: getGoodsInfoForMatch(notList, 'not'), // 禁止商品
			priorityItem: getGoodsInfoForMatch(addList, 'add'), // 优先商品
		};
		
		// 删除更新商品货品
		ItemSmartExpressUpdateItemGoodsListApi(params as ItemSmartExpressUpdateItemGoodsListRequest);
	};

	// 校验规则是否存在错误
	const validateValues = async() => {
		// 判断平台是否为空
		if (!supportPlatformValues.length) {
			return ({ success: false, reason: "至少选择一个平台" });
		}
		// 判断重量或者金额是否只填写了单个
		const isSingleWeight = (minWeight && !maxWeight) || (!minWeight && maxWeight);
		const isSingleAmount = (minAmount && !maxAmount) || (!minAmount && maxAmount);
		if (isSingleAmount) {
			return ({ success: false, reason: "订单金额未填写完整" });
		}
		if (isSingleWeight) {
			return ({ success: false, reason: "订单重量未填写完整" });
		}
		// 判断优先级是否填写
		if (!priorityValue) {
			return ({ success: false, reason: "请填写优先级（1 - 99）" });
		}
		// 判断优先级数值是否符合
		if (priorityValue && (priorityValue < 1 || priorityValue > 99)) {
			return ({ success: false, reason: "优先级范围为（1 - 99），请重新填写" });
		}

		return ({ success: true });
	};

	const getSaveParams = () => {
		const paramItem = {
			id: data.id,
			suitPlatform: supportPlatformValues,
			priorityLogisticsFee: priorityLogisticsFeeSwitch,
			priorityProvince: prioritizeProvinceList.map(item => item.name).join(','), // 优先省份
			priority: priorityValue,
			hasOpenedReachableExpress: reachableExpressSwitch,
			hasOpenedLimitRuleConfig: limitRuleSwitch,
			limitRuleConfig: {
				assignShopList: selectedShopData?.data?.map(shop => ({
					sellerId: shop.sellerId,
					sellerNick: shop.sellerNick,
					platform: shop.platform,
				})),
				minAmount: minAmount && (minAmount * 100).toFixed(0),
				maxAmount: maxAmount && (maxAmount * 100).toFixed(0),
				minWeight: minWeight && (isKg ? (minWeight * 1000).toFixed(0) : minWeight),
				maxWeight: maxWeight && (isKg ? (maxWeight * 1000).toFixed(0) : maxWeight),
				memoExpressMatch,
				bannedArea: bannedAreaValue, // 禁止地区
				assignExpress,
				priorityProvince: designatedProvinceList.map(item => item.name).join(','), // 指定省份
			},
		};
		return paramItem;
	};


	/** ------------ 弹窗相关逻辑 -------------- */
	const _onOk = async() => {
		sendPoint(Pointer.智选快递_自动匹配规则_保存);
		// 处理货品商品更新逻辑
		handleGoodsInfo();
		const { success, reason } = await validateValues();
		if (!success) {
			message.error(reason);
			return;
		}
		setLoading(true);
		const saveParams = getSaveParams();
		ItemSmartExpressInsertOrUpdateApi({ smartExpressTemplateVOList: [saveParams] }).then(() => {
			// 判断操作是否成功，不需要判断res
			message.success("规则保存成功!");
			onOk && onOk();
		}).finally(() => setLoading(false)).catch(() =>	message.error("规则保存失败!"));
	};
	const _onCancel = () => {
		onCancel && onCancel();
	};
	/** -------------------------------------- */

	const onPriorityLogisticsFeeSwitchChange = (checked) => {
		sendPoint(Pointer.智选快递_自动匹配规则_开启运费);
		setPriorityLogisticsFeeSwitch(checked);
	};

	// 第四优先级 优先级
	const onPriorityChange = (v) => {
		setPriorityValue(v);
	};

	// 快递可达
	const onReachableExpressSwitchChange = (checked) => {
		sendPoint(Pointer.智选快递_自动匹配规则_开启可达);
		setReachableExpressSwitch(checked);
	};

	// 匹配规则开启
	const onLimitRuleSwitchChange = (checked) => {
		sendPoint(Pointer.智选快递_自动匹配规则_开启限制);
		setLimitRuleSwitch(checked);
	};

	// 指定快递
	const onAssignExpressChange = (v) => {
		setAssignExpress(v);

	};


	

	const title = (
		<div className={ s['modal-title'] }>
			<div>添加模板规则</div>
			<div>选择模版添加智选快递规则</div>
		</div>
	);

	useEffect(() => {
		const smartExpressObj = {};
		expressTemplateList.forEach(i => {
			smartExpressObj[i.Mode_ListShowId] = i;
		});
		setSmartExpressObj(smartExpressObj);
		setTimeout(() => {
			handleSupportPlatform(); // 处理平台
		});
	}, [expressTemplateList]);

	useEffect(() => {
		initData();
	}, []);

	// useImperativeHandle(cRef, () => {
	// 	return ({
	// 		getProvinceList: prioritizeProvinceList
	// 	});
	// }, [prioritizeProvinceList]);

	return (
		<div>
			<Modal
				visible
				closable
				width={ 1000 }
				centered
				title="自动匹配快递规则"
				footer={ 	(
					<div className="">
						<Button onClick={ _onCancel }>取消</Button>
						<Button type="primary" onClick={ _onOk }>保存</Button>
					</div>
				) }
				className={ s["modal"] }
				onCancel={ _onCancel }
				onOk={ _onOk }
				maskClosable={ false }
				bodyStyle={ { overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' } }
			>
				<Spin spinning={ loading }>
					<div>
						<div className={ s["condition-group"] }>
							<div className={ s["condition-group-label"] }>
								快递模板
							</div>
							<div className={ s["condition-group-control"] }>
								{data.printTemplateName}
							</div>
						</div>
						<div className={ s["condition-group"] }>
							<div className={ s["condition-group-label"] }>
								支持平台
							</div>
							<div className={ s["condition-group-control"] }>
								<Checkbox.Group
									value={ supportPlatformValues }
									options={ supportPlatformOptions }
									onChange={ supportPlatformOnChange }
								/>
							</div>
						</div>
						{/* 限定规则组 */}
						<div className={ s["condition-group"] }>
							<div className={ s["condition-group-label"] }>
								匹配规则
							</div>
							<div className={ s["condition-group-control"] }>

								{/* 限定规则总开关 */}
								<div className={ cs(s["condition-item"]) }>
									
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div style={ { marginLeft: 0 } }>
											
												<Switch
													size="small"
													onChange={ onLimitRuleSwitchChange }
													checkedChildren="开"
													unCheckedChildren="关"
													checked={ limitRuleSwitch }
												/>
											</div>
											<div className="r-c-error" style={ { marginLeft: 16 } }>
												即匹配当前快递模板的订单需符合以下规则
											</div>
										</div>
								
									</div>
									<div />
								</div>
						
								{/* 指定店铺 */}
								<div className={ cs(s["condition-item"], s["shop-rule"]) }>
									<div className={ s["condition-item-label"] }>
										<span className={ s['condition-item-text'] }>指定店铺：</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div>
												<Button type="primary" size="small" onClick={ selectedShopOnClick }>选择店铺</Button>
											</div>
											<div className={ s['extra-desc'] }>（不选默认无限制）</div>
										</div>
										<div>
											<div className="r-mt-6">
												{getSelectedShopList()}
											</div>
										</div>
								
									</div>
									<div />
								</div>

								{/* 订单金额*/}
								<div className={ cs(s["condition-item"], s["price-rule"]) }>
									<div className={ s["condition-item-label"] }>
										<span className={ s['condition-item-text'] }>订单金额：</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div>
												<InputPrice
													suffix="元"
													precision={ 2 }
													value={ minAmount }
													onChange={ onMinAmountChange }
													className={ s["range-input"] }
													placeholder="最小金额"
													maxLength={ 10 }
												/>
											</div>
											<div>
												~
											</div>
											<div>
												<InputPrice
													suffix="元"
													precision={ 2 }
													value={ maxAmount }
													onChange={ onMaxAmountChange }
													className={ s["range-input"] }
													placeholder="最大金额"
													maxLength={ 10 }
												/>
											</div>
										</div>
								
									</div>
									<div />
								</div>

								{/* 订单重量*/}
								<div className={ cs(s["condition-item"], s["weight-rule"]) }>
									<div className={ s["condition-item-label"] }>
										<span className={ s['condition-item-text'] }>订单重量：</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div>
												<InputPrice
													suffix={ isKg ? "kg" : "g" }
													precision={ isKg ? 3 : 0 }
													value={ minWeight }
													onChange={ minWeightOnChange }
													className={ s["range-input"] }
													placeholder="最小重量"
													maxLength={ 10 }
												/>
											</div>
											<div>
												~
											</div>
											<div>
												<InputPrice
													suffix={ isKg ? "kg" : "g" }
													precision={ isKg ? 3 : 0 }
													value={ maxWeight }
													onChange={ maxWeightOnChange }
													className={ s["range-input"] }
													placeholder="最大重量"
													maxLength={ 10 }
												/>
											</div>
										</div>
								
									</div>
									<div />
								</div>

								{/* 留言备注 */}
								<div className={ cs(s["condition-item"], s["memo-rule"]) }>
									<div className={ s["condition-item-label"] }>
										<span className={ s['condition-item-text'] }>留言备注：</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div >
												<Checkbox checked={ memoExpressMatch } onChange={ onMemoExpressMatchChange }>根据留言备注中的快递名称匹配</Checkbox>
											</div>
											<div className={ s['extra-desc'] }>
												如果带有否定词（例如：不，别）则不会按照此规则匹配优选快递
											</div>
										</div>
									</div>
									<div />
								</div>

								{/* 指定商品 */}
								<div className={ cs(s["condition-item"], s["memo-rule"]) }>
									<div className={ s["condition-item-label"] }>
										<span className={ s['condition-item-text'] }>指定商品：</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div >
												添加指定商品后，仅该商品订单匹配当前快递模板
											</div>
											<div className={ s['extra-desc'] }>
												（不添加默认无限制）
											</div>
										</div>
										<div>
											<AddProducts cRef={ yesProductsComp } dataSource={ isShowZeroStockVersion ? yesOriginItemList : yesOriginSysItemList } />
										</div>
									</div>
									<div />
								</div>

								{/* 禁止商品 */}
								<div className={ cs(s["condition-item"], s["memo-rule"]) }>
									<div className={ s["condition-item-label"] }>
										<span className={ s['condition-item-text'] }>禁止商品：</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div >
												添加禁止商品后，该商品订单不匹配当前快递模板
											</div>
										</div>
										<div>
											<AddProducts cRef={ notProductsComp } dataSource={ isShowZeroStockVersion ? notOriginItemList : notOriginSysItemList } />
										</div>
									</div>
									<div />
								</div>

								{/* 指定省份 */}
								<div className={ cs(s["condition-item"], s["memo-rule"]) }>
									<div className={ s["condition-item-label"] }>
										<span className={ s['condition-item-text'] }>指定省份：</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div className="kdzs-link-text-theme r-ml-6" onClick={ onAddForbiddeArea }>添加省份</div>
					
											<div className={ s['extra-desc'] }>添加指定省份后，仅该省份订单匹配当前快递模板（不添加默认无限制）</div>
										</div>
										<div>
											<div className={ s["prioritize-province-tag-container"] }>
												{getPrioritizeProvinceList('yes')}
												{/* <Table /> */}
											</div>
										</div>
										
									</div>
								</div>

								{/* 禁止地区 */}

								<div className={ cs(s["condition-item"], s["not-city-rule"]) }>
									<div className={ s["condition-item-label"] }>
										<span className={ s['condition-item-text'] }>禁止地区：</span>
									</div>
						
									<div className="r-flex-1">
										<Input.TextArea placeholder="请填写地区关键字，多个通过逗号“,”隔开" value={ bannedAreaValue } onChange={ onBannedAreaChange } />
									</div>
									<div />
								</div>

								{/* 指定快递 */}

								<div className={ cs(s["condition-item"], s["manul-selected-express-rule"]) } hidden>
									<div className={ s["condition-item-label"] }>
										<span className={ s['condition-item-text'] }>指定快递：</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div>
												<Select
													allowClear
													value={ assignExpress }
													size="small"
													style={ { minWidth: 150 } }
													onChange={ onAssignExpressChange }
												>
													{expressCompanyList.map(company => {
														return <Select.Option key={ company.exCode } value={ company.exCode }>{company.exName}</Select.Option>;
													})}
												</Select>
											</div>
											<Tooltip className={ s["help-icon"] } title="拼多多平台指定快递将可以分配此模版">
												<QuestionCircleOutlined />
											</Tooltip>
											<div className={ s['extra-desc'] }>
												仅支持（拼多多平台）
											</div>
										</div>
									</div>
									<div />
								</div>

								<div className={ cs(s["condition-item"], s["express-can-up-rule"]) }>
									
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div>快递可达判断：</div>
											<div>
												<Switch
													size="small"
													onChange={ onReachableExpressSwitchChange }
													checkedChildren="开"
													unCheckedChildren="关"
													checked={ reachableExpressSwitch }
												/>
												{/* <Switch size="small" onChange={}/> */}
												<Tooltip className={ s['help-icon'] } title="系统根据自动判断订单地址是否可达，设置自定义可达更准确">
													<QuestionCircleOutlined />
												</Tooltip>
											</div>
											<div>
												<span
													className="kdzs-link-text-theme"
													onClick={ () => history.push("/settings/expressCanUp") }
												>设置自定义可达规则
												</span>
											</div>
										</div>
									</div>
									<div />
								</div>

							</div>
						</div>

						{/* 分配规则组 */}
						<div className={ s["condition-group"] }>
							<div className={ s["condition-group-label"] }>
								分配规则
							</div>
							<div className={ s["condition-group-control"] }>
								<div className={ cs(s["condition-item"]) }>
									
									<div className={ cs(s["condition-item-control"]) }>
										<div className="r-c-error">
											若有多个快递模板匹配成功，则按以下分配优先级确定最终模板
										</div>
								
									</div>
									<div />
								</div>

								{/* 第一优先级 */}
								<div className={ cs(s["condition-item"], s["first-rule"]) }>
									<div className={ s["condition-item-label"] }>
										{/* <span className={ s['level-number'] } >1</span> */}
										<span className={ s['condition-item-text'] }>第
											<span className="r-bold" style={ { color: "#fd8204" } }>一</span>
											优先级：
										</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div>优先商品</div>
											<div className={ s['extra-desc'] }>请添加商品，如未添加，按商品计算快递时不生效</div>
										</div>
										<div>
											<AddProducts cRef={ addProductsComp } dataSource={ isShowZeroStockVersion ? originItemList : originSysItemList } />
											<div>
												{/* <Table /> */}
											</div>
										</div>
								
									</div>
									<div />
								</div>
								{/* 第二优先级 */}
								<div className={ cs(s["condition-item"], s["second-rule"]) }>
									<div className={ s["condition-item-label"] }>
										{/* <span className={ s['level-number'] } >2</span> */}
										<span className={ s['condition-item-text'] }>第
											<span className="r-bold" style={ { color: "#fd8204" } }>二</span>
											优先级：
										</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div>优先低运费</div>
											<Switch
												size="small"
												className="r-ml-6"
												onChange={ onPriorityLogisticsFeeSwitchChange }
												checkedChildren="开"
												unCheckedChildren="关"
												checked={ priorityLogisticsFeeSwitch }
											/>
											<div className={ s['extra-desc'] }>
												优先匹配运费低的快递，启用后请维护
												<span
													className="kdzs-link-text-theme"
													onClick={ () =>	history.push("/settings/expressTemplate") }
												>运费模版
												</span>
												及商品重量，否则无效
											</div>
										</div>
								
									</div>
									<div />
								</div>
								{/* 第三优先级 */}
								<div className={ cs(s["condition-item"], s["third-rule"]) }>
									<div className={ s["condition-item-label"] }>
										{/* <span className={ s['level-number'] } >3</span> */}
										<span className={ s['condition-item-text'] }>第
											<span className="r-bold" style={ { color: "#fd8204" } }>三</span>
											优先级：
										</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div>优先省份</div>
											<div className="kdzs-link-text-theme r-ml-6" onClick={ onAddProvince }>添加省份</div>
											<div className={ s['extra-desc'] }>请选择省份，如未添加，按省份分配快递时不生效</div>
										</div>
										<div>
											<div className={ s["prioritize-province-tag-container"] }>
												{getPrioritizeProvinceList('add')}
												{/* <Table /> */}
											</div>
										</div>
									</div>
									<div />
								</div>

								{/* 第四优先级 */}
								<div className={ cs(s["condition-item"], s["fourth-rule"]) }>
									<div className={ s["condition-item-label"] }>
										{/* <span className={ s['level-number'] } >4</span> */}
										<span className={ s['condition-item-text'] }>第
											<span className="r-bold" style={ { color: "#fd8204" } }>四</span>
											优先级：
										</span>
									</div>
									<div className={ cs(s["condition-item-control"]) }>
										<div>
											<div className="r-label-require">优先级：</div>
											<div>
												<InputPrice
													style={ { width: 60 } }
													size="small"
													precision={ 0 }
													value={ priorityValue }
													onChange={ onPriorityChange }
												/>
											</div>
											<div className={ s['extra-desc'] }>
												数字越小、优先级越高(1-99)
											</div>
										</div>
									</div>
									<div />
								</div>
							</div>
						</div>
					</div>
				</Spin>
				
			</Modal>

			{/* 优先省份、指定省份 区域选择弹窗 */}
			{
				showSelectedAreaModal
				&& (
					<SelectAreaModal
						showDistrict={ showDistrict }
						showCity={ showCity }
						onOk={ areaSelectedType === AreaSelectedType.优先省份 ? selectedPrioritizeProvinceOnOk : selectedForbiddeAreaOnOk }
						onCancel={ selectAreaModalOnCancel }
						selectedArea={ areaSelectedType === AreaSelectedType.优先省份 ? prioritizeProvinceList : designatedProvinceList }
						disabledArea={ disabledArea }
					/>
				)
			}
			 {/* 店铺选择 */}
			 <SelectShopModal
				selectShopData={ selectedShopData }
				onOk={ selectShopModalOnOk }
				onCancel={ selectShopModalOnCancel }
			 />

		</div>
		
	);
};

export default observer(RulesManager);