import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Tree, Row, Col, Checkbox, Tooltip, Button } from 'antd';
import _ from 'lodash';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import { AuthorityEditSubAccountUserApi, UpdateUserAddressWaybillApi } from '@/apis/user';
import userStore from '@/stores/user';
import { AuthorityGetSubAccountUserListObj, BranchAuthoritySubmitDTO, IndexAuthorityEditSubAccountUserRequest, IndexAuthorityGetAuthorityTemplateResponse, IndexAuthorityUpdateUserAddressWaybillRequest } from '@/types/schemas/user';
import { TreeLevelEnum, generateTree } from '@/utils';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import message from "@/components/message";
import { PLAT_MAP, PLAT_OTHER } from '@/constants';
import Tabs from '@/components/Tabs';
import { MenuIdMap } from '@/components-biz/layouts/HeaderMenu/menu';
import { FieldsAuthorityList, getCheckedKeys } from './constants';
import Temp from './Temp';
import groupStore from '@/stores/warehouse/Group';
import { lowVersionLock } from "@/components-biz/LowVersionControlModal";
import { PageNameControlEnum, VersionEnum } from "@/components-biz/LowVersionControlModal/constant";
import ExpressTemp from './ExpressTemp';
import s from './index.module.scss';
import StorageTemp from './StorageTemp';

interface PermSettingModalInf {
    visible: boolean;
    onClose: () => void;
    permData: AuthorityGetSubAccountUserListObj
    updateTable?: () => void;
}
const enum authType {
	"菜单权限" = '1',
	"店铺权限" = '2',
	"数据权限" = '3',
	"面单网点权限" = '4',
	"商品分类权限" = '5',
	"快递模版权限" = '6',
	"小程序权限" = "7",
	"仓库权限" = "8",
	"PDA权限" = "9",  // 添加PDA权限类型
}

export const enum selectFlagEnum {
	全选 = 0,
	反选 = 1,
	清空 = 2,
}

interface CheckParams {
	branchAuthoritySubmitDTO?: BranchAuthoritySubmitDTO;
	unCheckedUserAddress?: IndexAuthorityUpdateUserAddressWaybillRequest;
}
const PermSettingModal: React.FC<PermSettingModalInf> = (props) => {
	const { visible, onClose, permData, updateTable } = props;
	const [activeKey, setActiveKey] = useState(authType.菜单权限);
	const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(null);
	const [checkedKeys, setCheckedKeys] = useState<number[]>([2, 11]);
	const [tempCheckedKeys, setTempCheckedKeys] = useState<CheckParams>({});
	const [checkedShopsKeys, setCheckedShopsKeys] = useState<string[]>([]);
	const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
	const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
	const [permTreeData, setPermTreeData] = useState<IndexAuthorityGetAuthorityTemplateResponse['data']>(null);
	const [shopTreeData, setShopTreeData] = useState(null);
	const [allShopList, setAllShopList] = useState([]);
	const [showFunctionAuthorityModal, setShowFunctionAuthorityModal] = useState(false);
	const [functionAuthorityData, setFunctionAuthorityData] = useState<typeof permTreeData[number]>(null);
	const [functionAuthorityCheckedList, setFunctionAuthorityCheckedList] = useState<string[]>([]);
	const [fieldsAuthorityCheckedList, setFieldsAuthorityCheckedList] = useState<string[]>([]);
	const [itemSortAuthority, setItemSortAuthority] = useState<any>({
		notClassifIdList: [],
		notItemTradeFlag: true,
		unclassifiedFlag: true,
		unrelatedSysItemTradeFlag: true
	});
	const [exCheckedData, setExCheckedData] = useState({ templateIdList: [], groupIdList: [], authorityType: 1, templateNameList: [], groupNameList: [] });
	const [miniAppAuthority, setMiniAppAuthority] = useState({
		scanAndInspect: false, // 扫拿货码验货
		scanAndPacking: false, // 扫快递单发货
		sendRecord:false, // 小程序发货记录
		aftersaleScan:false , // 售后扫描查询
		aftersaleScanRegister:false , // 售后扫描登记
		refundBatchScanRegister:false , // 批量快递登记
		showBhd:false , // 备货单
		dataBoard:false,
		manualOrder: true, // 手工下单
		singleProductInventory: {queryList: false, modifyStock: false, modifyRemark: false}, // 单品库存
		combinationProductInventory: {queryList: false, modifyStock: false, modifyRemark: false}, // 组合库存
		scanAndPackingAuthority:{modifyRemark:false,modifySysMemo:false,send:false}, // 扫快递单发货功能权限
		reachRecordAuthority:{queryList: false, modifyStock: false, modifyRemark:false,modifySysMemo:false,send:false} // 入库单权限
	});

	// 添加PDA权限状态
	const [pdaAuthority, setPdaAuthority] = useState<any>({
		pickingHall: false,      // 拣货大厅
		myPicking: false,        // 我的拣货
		abnormalRefund: false,   // 异常退款
		warehouseSlotSort: false,     // 货位排序
		itemStock: false,        // 商品入库
	});

	const [showMiniAppFunctionAuthorityModal, setShowMiniAppFunctionAuthorityModal] = useState({show: false, isSingle: true});
	const [storageAuthority, setStorageAuthority] = useState({})
	const [scanAndPackingShow,setScanAndPackingShow] = useState(false)
	const [aftersaleScanLock, setAftersaleScanLock] = useState(false); // 小程序售后扫描高级版和白名单限制
	const [aftersaleExpressScanLock, setAftersaleExpressScanLock] = useState(false); // 小程序批量快递登记高级版和白名单限制

	const [form] = Form.useForm();
	const groupList = groupStore?.groupList?.filter(s => s.classifyId != '-1') || [];

	const {userInfo} = userStore;

	// * 获取商品分数数据
	useEffect(() => {
		groupStore.getGroupList();
	}, []);
	// * 菜单权限初始化
	useEffect(() => {
		const fn = async() => {
			const res = await userStore.getPermTemp();
			console.log('resres',res);

			if (res) {
				const generateTreeArr: typeof permTreeData = [];
				res.forEach((item: any, index: number) => {
					// 账号与权限暂时屏蔽
					if (item.id == MenuIdMap['设置_账号与权限']) {
						item.disabled = true;
					}
					item.title = item.authorityName;
					item.key = item.id;
					// 下线组合货品打包
					if (item.id != MenuIdMap['库存_组合货品打包']) {
						generateTreeArr.push(item);
					}
				});
				const treeData = generateTree(generateTreeArr);
				console.log('%c [ treeData ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', treeData)
				setPermTreeData(treeData);
			}
		};
		fn();
	}, []);

	useEffect(() => {
		if (permData) {
			const { expressTemplateAuthority } = permData;
			if (expressTemplateAuthority) {
				setExCheckedData({
					authorityType: expressTemplateAuthority.authorityType ?? 1,
					groupIdList: expressTemplateAuthority.groupIdList || [],
					templateIdList: expressTemplateAuthority.templateIdList || [],
					groupNameList: expressTemplateAuthority.groupNameList || [],
					templateNameList: expressTemplateAuthority.templateNameList || []
				});
			}
		}
	}, [permData]);
	// * 店铺权限初始化
	useEffect(() => {
		const fn = async() => {
			let _allShopList = [];
			const shopList = await userStore.getShopList();
			let shopObj = {};
			for (let item of shopList) {
				const _platform = item.platform;
				item.title = item.sellerNick;
				item.id = item.sellerId;
				item.key = item.sellerId;
				if (!shopObj[_platform]) {
					shopObj[_platform] = {
						id: _platform,
						title: PLAT_MAP[_platform],
						children: [item],
						key: _platform
					};
				} else {
					shopObj[_platform].children.push(item);
				}
				_allShopList.push(item.sellerId);
			}
			setAllShopList(_allShopList.sort());
			let shopTreeData = [];
			for (let key in shopObj) {
				if (key !== PLAT_OTHER) {
					shopTreeData.push(shopObj[key]);
				}
			}
			shopObj[PLAT_OTHER] && shopTreeData.push(shopObj[PLAT_OTHER]);
			setShopTreeData(shopTreeData);
		};
		fn();
	}, []);

	// * 数据权限初始化
	useEffect(() => {
		const allFieldsAuthorityArr = FieldsAuthorityList.map(i => i.value);
		if (permData.dataAuthority) {
			try {
				const dataAuthorityArr = JSON.parse(permData.dataAuthority) as string[];
				setFieldsAuthorityCheckedList(_.differenceBy(allFieldsAuthorityArr, dataAuthorityArr));
			} catch (error) {
				console.error('解析dataAuthority失败', error);
			}
		} else {
			setFieldsAuthorityCheckedList(allFieldsAuthorityArr);
		}
		setItemSortAuthority(permData?.itemSortAuthority || {});
		setPdaAuthority(permData?.pdaAuthority || {});
		if (permData?.miniAppAuthority) {
			setMiniAppAuthority(permData?.miniAppAuthority);
		}
	}, []);

	useEffect(() => {
		if (!permData) return;
		permData._platForm = permData.platform ? PLAT_MAP[permData.platform] : '自建';
		form.setFieldsValue(permData);
		if (permData.shopAuthority === 'all' || !permData.shopAuthority) {
			setCheckedShopsKeys(allShopList);
		} else {
			setCheckedShopsKeys(permData.shopAuthority.split(',') || []);
		}

	}, [permData, allShopList]);


	useEffect(() => {
		if (!permData || !permTreeData) return;
		let { authorityDetail } = permData || {};
		let arr = [];
		let _arr: any[] = [];
		let notAllSelect: any = new Set();
		if (authorityDetail) {
			arr = JSON.parse(authorityDetail);
		}
		for (let index in permTreeData) {
			if (permTreeData[index].children) {
				for (let child of permTreeData[index].children) {
					if (!child?.disabled && !arr.includes(child.id + '')) {
						notAllSelect.add(permTreeData[index].id + '');
					}
				}
			}
		}
		arr.forEach((item: number) => {
			if (!notAllSelect.has(item)) {
				_arr.push(item * 1);
			}
		});
		console.log(arr)
		console.log(_arr)
		setCheckedKeys(_arr);
	}, [permData, permTreeData]);

	useEffect(() => {
		if (visible) {
			sendPoint(Pointer['设置_账号与权限_设置菜单权限_展现']);
		}
	}, [visible]);

	const onExpand = (expandedKeysValue: React.Key[]) => {
		console.log('onExpand', expandedKeysValue);
		// if not set autoExpandParent to false, if children expanded, parent can not collapse.
		// or, you can remove all expanded children keys.
		setExpandedKeys(expandedKeysValue);
		setAutoExpandParent(false);
	};

	const onCheck = (checkedKeysValue) => {
		console.log('选中的菜单项:', checkedKeysValue);
		setCheckedKeys(checkedKeysValue);
	};

	const onSelect = (selectedKeysValue: React.Key[], info: any) => {
		console.log('onSelect', info);
		setSelectedKeys(selectedKeysValue);
	};

	const onOk = async(e, closeModal = true) => {
		let _checkedShopsKeys = [];
		let shopAuthority = "";
		checkedShopsKeys.forEach((item) => {
			if (!PLAT_MAP[item]) _checkedShopsKeys.push(item);
		});
		shopAuthority = _checkedShopsKeys.sort().join(',');
		// 选全部店铺 就会传all
		if (shopAuthority === allShopList.join(',')) {
			shopAuthority = "all";
		}
		let params:IndexAuthorityEditSubAccountUserRequest = {
			...permData,
			editType: 1,
			authorityDetail: checkedKeys.join(','),
			shopAuthority: shopAuthority || "-1",
			dataAuthority: permData.dataAuthority ? JSON.parse(permData.dataAuthority)?.join(',') : undefined,
			funcAuthority: permData.funcAuthority ? JSON.parse(permData.funcAuthority)?.join(',') : undefined,
			expressTemplateAuthority: {
				authorityType: exCheckedData?.authorityType,
				groupIdList: exCheckedData?.groupIdList,
				templateIdList: exCheckedData?.templateIdList,
				logTemplateNameList: [...exCheckedData?.groupNameList || [], ...exCheckedData?.templateNameList || []]
			},
			miniAppAuthority: {
				...miniAppAuthority
			},
			pdaAuthority: {
				...pdaAuthority
			},
			storageAuthority: Object.keys(storageAuthority)?.length > 0 ? {
				...storageAuthority
			} : null
		};
		if (!closeModal) {
			const oldPermFunc = new Set(JSON.parse(permData.funcAuthority || '[]'))
			const allFunctionCodeArr = functionAuthorityData?.functionChildren?.map(item => item.templateCode) || [];
			let notIncludeFuncAuthorityArr = _.differenceBy(allFunctionCodeArr, functionAuthorityCheckedList)
			// 先清楚再添加
			allFunctionCodeArr.forEach(e => {oldPermFunc.delete(e)})
			notIncludeFuncAuthorityArr.forEach(e => {oldPermFunc.add(e)})
			const permFuncArr = Array.from(oldPermFunc)
			console.log('xxxxx',permFuncArr,allFunctionCodeArr,notIncludeFuncAuthorityArr);
			params.funcAuthority = permFuncArr?.filter((s:string)=>s.length>1)?.join(',') || '-1';
			await AuthorityEditSubAccountUserApi(params);
			setShowFunctionAuthorityModal(false);
			updateTable && updateTable();
		} else {
			let notIncludeDataAuthorityArr = _.differenceBy(FieldsAuthorityList.map(i => i.value), fieldsAuthorityCheckedList);
			params.dataAuthority = notIncludeDataAuthorityArr.join(',') || '-1';
			params.branchAuthoritySubmitDTO = tempCheckedKeys?.branchAuthoritySubmitDTO;
			// params
			// itemSortAuthority
			params.itemSortAuthority = {
				...params.itemSortAuthority,
				...itemSortAuthority,
			};
			await AuthorityEditSubAccountUserApi(params);
			if (activeKey == authType.面单网点权限 && tempCheckedKeys?.unCheckedUserAddress) {
				await UpdateUserAddressWaybillApi(tempCheckedKeys?.unCheckedUserAddress);
			}
			message.success('设置成功');
			onClose && onClose();
			updateTable && updateTable();
		}
	};

	const handleFunctionAuthorityModal = (e, functionNode: typeof permTreeData[number]) => {
		e.stopPropagation();
		setShowFunctionAuthorityModal(true);
		setFunctionAuthorityData(functionNode);
		const allFunctionCodeArr = functionNode?.functionChildren?.map(i => i.templateCode) || [];
		if (permData.funcAuthority) {
			try {
				const functionAuthorityArr = JSON.parse(permData.funcAuthority) as string[];
				setFunctionAuthorityCheckedList(_.differenceBy(allFunctionCodeArr, functionAuthorityArr));
			} catch (error) {
				console.error('解析dataAuthority失败', error);
			}
		} else {
			setFunctionAuthorityCheckedList(allFunctionCodeArr);
		}
	};

	const functionRender = (nodeData: typeof permTreeData[number]) => {
		const { title, level, functionChildren=[] } = nodeData;
		if (level === TreeLevelEnum['菜单']) {
			return title;
		} else if (level === TreeLevelEnum['页面']) {
			if (functionChildren?.length) {
				return (
					<div className="r-flex">
						<div>{title}</div>
						<div className="r-c-warning r-ml-12" onClick={ e => handleFunctionAuthorityModal(e, nodeData) }>功能权限</div>
					</div>
				);
			} else {
				return title;
			}
		} else if (level === TreeLevelEnum['功能']) {
			return title;
		}
	};

	const handleFieldsAuthorityChangeItem = (checkedValue: string[]) => {
		setFieldsAuthorityCheckedList(checkedValue);
	};

	const handleFunctionAuthorityChangeItem = (checkedValue: string[]) => {
		console.log(checkedValue, 'checkedValue');
		setFunctionAuthorityCheckedList(checkedValue);
	};

	// 修改商品分类权限、货品分类权限
	const changeItemAuthority = (newValue:any) => {
		setItemSortAuthority(pre => ({ ...pre, ...newValue }));
	};

	const changeMiniAppAuthority = (value)=> {
		setMiniAppAuthority(pre => ({ ...pre, ...value }));
	}

	const handleChangeProductInventory = (isSingle)=> {
		setShowMiniAppFunctionAuthorityModal({show: true, isSingle})
	}

	const changeMiniAppAuthoritySelect = (flag) => {
		//优化一下这个方法
		const defaultPermissions = {
			scanAndInspect: false,
			scanAndPacking: false,
			sendRecord: false,
			aftersaleScan: false,
			aftersaleScanRegister: false,
			refundBatchScanRegister: false,
			dataBoard: false,
			showBhd: false,
			manualOrder: false,
			'singleProductInventory.queryList': false,
			'combinationProductInventory.queryList': false,
			'reachRecordAuthority.queryList': false
		};
	
		const _miniAppAuthority = {...miniAppAuthority};
		
		if (flag === selectFlagEnum.全选 || flag === selectFlagEnum.清空) {
			const value = flag === selectFlagEnum.全选;
			Object.entries(defaultPermissions).forEach(([key, _]) => {
				if (key.includes('.')) {
					const [parent, child] = key.split('.');
					_miniAppAuthority[parent] = {
						..._miniAppAuthority[parent],
						[child]: value
					};
				} else {
					_miniAppAuthority[key] = value;
				}
			});
		} else if (flag === selectFlagEnum.反选) {
			Object.entries(defaultPermissions).forEach(([key, _]) => {
				if (key.includes('.')) {
					const [parent, child] = key.split('.');
					_miniAppAuthority[parent] = {
						..._miniAppAuthority[parent],
						[child]: !_miniAppAuthority[parent]?.[child]
					};
				} else {
					_miniAppAuthority[key] = !_miniAppAuthority[key];
				}
			});
		}
	
		setMiniAppAuthority(_miniAppAuthority);
	};

	const changeSortSelect = (flag)=> {
		if (flag === selectFlagEnum.全选) {
			changeItemAuthority({ unclassifiedFlag: true, notClassifIdList: []})
		} else if (flag === selectFlagEnum.清空) {
			changeItemAuthority({ unclassifiedFlag: false, notClassifIdList: groupList.map(s => s.classifyId) });
		} else if (flag === selectFlagEnum.反选) {
			const _notClassifIdList = groupList.filter((item)=>(!itemSortAuthority.notClassifIdList.includes(item.classifyId))).map(s => s.classifyId);
			changeItemAuthority({ unclassifiedFlag: !itemSortAuthority.unclassifiedFlag, notClassifIdList: _notClassifIdList})
		}
	}

	const changeFieldsAuthoritySelect = (flag)=> {
		const allValue = FieldsAuthorityList.map((item)=>(item.value));
		if (flag === selectFlagEnum.全选) {
			setFieldsAuthorityCheckedList(allValue)
		} else if (flag === selectFlagEnum.清空) {
			setFieldsAuthorityCheckedList([])
		} else if (flag === selectFlagEnum.反选) {
			setFieldsAuthorityCheckedList(allValue.filter((item)=>(!fieldsAuthorityCheckedList.includes(item))))
		}
	}

	const handleChangeSelect = (flag)=> {
		let _checkedKeys = [];
		switch (activeKey) {
			case authType.菜单权限:
				_checkedKeys = getCheckedKeys(flag, permTreeData, checkedKeys);
				setCheckedKeys([..._checkedKeys])
				break;
			case authType.小程序权限:
				changeMiniAppAuthoritySelect(flag);
				break;
			case authType.店铺权限:
				_checkedKeys = getCheckedKeys(flag, shopTreeData, checkedShopsKeys);
				setCheckedShopsKeys([..._checkedKeys])
				break;
			case authType.商品分类权限:
				changeSortSelect(flag);
				break;
			case authType.数据权限:
				changeFieldsAuthoritySelect(flag);
				break;
			case authType.PDA权限:
				changePdaAuthoritySelect(flag);
				break;
			default:
				break;
		}
	}
	
	// 添加 PDA 权限的全选、反选、清空处理函数
	const changePdaAuthoritySelect = (flag: selectFlagEnum) => {
		const allFields = {
			pickingHall: false,
			myPicking: false,
			abnormalRefund: false,
			warehouseSlotSort: false,
			itemStock: false,
		};

		switch (flag) {
			case selectFlagEnum.全选:
				// 全部设为 true
				setPdaAuthority(Object.keys(allFields).reduce((acc, key) => ({
					...acc,
					[key]: true
				}), {}));
				break;
			case selectFlagEnum.清空:
				// 全部设为 false
				setPdaAuthority(allFields);
				break;
			case selectFlagEnum.反选:
				// 当前值取反
				setPdaAuthority(prev => 
					Object.keys(prev).reduce((acc, key) => ({
						...acc,
						[key]: !prev[key]
					}), {})
				);
				break;
		}
	};

	useEffect(() => {
		const fn = async() => {
			const res = await lowVersionLock(PageNameControlEnum.小程序售后扫描登记);
			const res1 = await lowVersionLock(PageNameControlEnum.小程序批量快递登记);
			console.log('%c [ 小程序售后扫描登记 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res)
			setAftersaleScanLock(!!res); // 既不是高级版又没开白名单权限
			setAftersaleExpressScanLock(!!res1); // 既不是高级版又没开白名单权限
		};
		fn();
	}, [userInfo]);

	return (
		<Modal
			centered
			visible={ visible }
			title={ `权限设置 【${permData?.subAccountName || ''}】` }
			onCancel={ onClose }
			onOk={ onOk }
			width={ 950 }
			maskClosable={ false }
			className={s.PermSettingModal}
		>
			<Form
				form={form}
				labelCol={{ span: 6 }}
				wrapperCol={{ span: 16 }}
				labelWrap={false}
			>
				<div className="r-flex">
					<Form.Item label="账号名" name="subAccountName">
						<Input disabled />
					</Form.Item>
					<Form.Item label="手机号" name="mobile">
						<Input disabled />
					</Form.Item>
					<Form.Item label="平台" name="_platForm">
						<Input disabled />
					</Form.Item>
				</div>
			</Form>
			<Tabs 
			    className={s.detailModal}
				style={ { maxHeight: 500, overflowY: "auto" } } 
				activeKey={ activeKey } onChange={ (v) => setActiveKey(v) }
			>
				{
					[authType.快递模版权限, authType.面单网点权限,authType.仓库权限].includes(activeKey) ? null : (
						<div style={{width: "100%"}} className='r-flex r-jc-fe r-pt-6'>
							<Button type="link" style={{padding: 0}} className='r-mr-8' onClick={()=>{handleChangeSelect(selectFlagEnum.全选)}}>全选</Button>
							<Button type="link" style={{padding: 0}} className='r-mr-8' onClick={()=>{handleChangeSelect(selectFlagEnum.反选)}}>反选</Button>
							<Button type="link" style={{padding: 0}} className='r-mr-8' onClick={()=>{handleChangeSelect(selectFlagEnum.清空)}}>清空</Button>
						</div>
					)
				}

				<Tabs.TabPane tab="菜单权限" key={ authType.菜单权限 }>
					<Tree
						checkable
						onExpand={onExpand}
						expandedKeys={expandedKeys}
						autoExpandParent={autoExpandParent}
						onCheck={onCheck}
						checkedKeys={checkedKeys}
						onSelect={onSelect}
						selectedKeys={selectedKeys}
						selectable={false}
						treeData={permTreeData}
						style={{ marginTop: 12 }}
						titleRender={functionRender}
					/>
				</Tabs.TabPane>
				<Tabs.TabPane tab="小程序权限" key={ authType.小程序权限 }>
					<div className="r-mt-12 r-ml-20">
						<div className='r-mb-6'>
							<Checkbox checked={ miniAppAuthority?.scanAndInspect } onChange={ e => changeMiniAppAuthority({ scanAndInspect: e.target.checked }) }>扫拿货码验货</Checkbox>
						</div>
						<div className='r-mb-6'>
							<Checkbox checked={ miniAppAuthority?.scanAndPacking } onChange={ e => changeMiniAppAuthority({ scanAndPacking: e.target.checked }) }>扫快递单发货</Checkbox>
							<span className="r-c-warning r-ml-6 r-pointer" onClick={()=>{setScanAndPackingShow(true)}}>功能权限</span>
						</div>
						<div className='r-mb-6'>
							<Checkbox checked={ miniAppAuthority?.sendRecord } onChange={ e => changeMiniAppAuthority({ sendRecord: e.target.checked }) }>小程序发货记录</Checkbox>
						</div>
						<div className='r-mb-6'>
							<Checkbox checked={ miniAppAuthority?.showBhd } onChange={ e => changeMiniAppAuthority({ showBhd: e.target.checked }) }>备货单</Checkbox>
						</div>
						<div className='r-mb-6'>
							<Checkbox checked={ miniAppAuthority?.manualOrder } onChange={ e => changeMiniAppAuthority({ manualOrder: e.target.checked }) }>手工下单</Checkbox>
						</div>
						<div className='r-mb-6'>
							<Checkbox checked={ miniAppAuthority?.aftersaleScan } onChange={ e => changeMiniAppAuthority({ aftersaleScan: e.target.checked }) }>售后扫描查询</Checkbox>
						</div>
						{
							!aftersaleScanLock && (
								<div className='r-mb-6'>
									<Checkbox checked={ miniAppAuthority?.aftersaleScanRegister } onChange={ e => changeMiniAppAuthority({ aftersaleScanRegister: e.target.checked }) }>售后扫描登记</Checkbox>
								</div>
							)
						}

						{
							!aftersaleExpressScanLock && (
								<div className='r-mb-6'>
									<Checkbox checked={ miniAppAuthority?.refundBatchScanRegister } onChange={ e => changeMiniAppAuthority({ refundBatchScanRegister: e.target.checked }) }>批量快递登记</Checkbox>
								</div>
							)
						}

						{
							!userStore.isShowZeroStockVersion ? (
								<>
									<div className='r-mb-6'>
										<Checkbox checked={ miniAppAuthority?.singleProductInventory?.queryList } onChange={ e => changeMiniAppAuthority({ singleProductInventory: { ...miniAppAuthority?.singleProductInventory, queryList: e.target.checked,} }) }>
											单品库存
										</Checkbox>
										<span className="r-c-warning r-ml-6 r-pointer" onClick={()=>{handleChangeProductInventory(true)}}>功能权限</span>
									</div>
									<div className='r-mb-6'>
										<Checkbox checked={ miniAppAuthority?.combinationProductInventory?.queryList } onChange={ e => changeMiniAppAuthority({ combinationProductInventory: {...miniAppAuthority?.combinationProductInventory, queryList: e.target.checked}}) }>
											组合库存
										</Checkbox>
										<span className="r-c-warning r-ml-6 r-pointer" onClick={()=>{handleChangeProductInventory(false)}}>功能权限</span>
									</div>
								</>
							) : null
						}
						<div className='r-mb-6'>
							<Checkbox checked={ miniAppAuthority?.reachRecordAuthority?.queryList } onChange={ e => changeMiniAppAuthority({ reachRecordAuthority: {...miniAppAuthority?.reachRecordAuthority, queryList: e.target.checked}}) }>入库单</Checkbox>
						</div>
						<div className='r-mb-6'>
    						<Checkbox checked={ miniAppAuthority?.dataBoard } onChange={ e => changeMiniAppAuthority({ dataBoard: e.target.checked }) }>基础数据看板</Checkbox>
						</div>
					</div>
				</Tabs.TabPane>
				<Tabs.TabPane tab="店铺权限" key={ authType.店铺权限 }>
					<Tree
						style={{ marginTop: 12 }}
						checkable
						onCheck={(val) => {
							setCheckedShopsKeys(val);
						}}
						checkedKeys={checkedShopsKeys}
						treeData={shopTreeData}
						selectable={false}
					/>
				</Tabs.TabPane>
				<Tabs.TabPane
					tab={
						userStore.isShowZeroStockVersion
							? "商品分类权限"
							: "货品分类权限"
					}
					key={authType.商品分类权限}
				>
					<div className="r-mt-12 r-ml-20">
						{/* <p className="r-c-error r-mb-12">
							{
								userStore.isShowZeroStockVersion
									? '注：子账号仅显示勾选分类商品的商品及订单数据权限'
									: '注：子账号仅显示勾选分类货品的商品及订单数据权限'
							}
						</p> */}
						{/* !!隐藏和订单有关的权限 */}
						{/* <div className="r-mb-10">
							{
								userStore.isShowZeroStockVersion
									? (
										<Checkbox checked={ itemSortAuthority.notItemTradeFlag } onChange={ e => changeItemAuthority({ notItemTradeFlag: e.target.checked }) }>无商品订单
											<Tooltip placement="right" title="勾选后系统默认子账号拥有无商品订单的数据权限">
												<QuestionCircleOutlined className="r-c-999 r-ml-4" />
											</Tooltip>
										</Checkbox>
									)
									: (
										<Checkbox checked={ itemSortAuthority.unrelatedSysItemTradeFlag } onChange={ e => changeItemAuthority({ unrelatedSysItemTradeFlag: e.target.checked }) }>未关联本地货品订单
											<Tooltip placement="right" title="勾选后系统默认子账号拥有未关联本地货品订单的数据权限">
												<QuestionCircleOutlined className="r-c-999 r-ml-4" />
											</Tooltip>
										</Checkbox>
									)
							}
						</div> */}
						<div className="r-mb-10">
							<Checkbox
								checked={itemSortAuthority.unclassifiedFlag}
								onChange={(e) =>
									changeItemAuthority({
										unclassifiedFlag: e.target.checked,
									})
								}
							>
								未设置分类
								<Tooltip
									placement="right"
									title="勾选后系统默认子账号拥有未设置分类商品的数据权限"
								>
									<QuestionCircleOutlined className="r-c-999 r-ml-4" />
								</Tooltip>
							</Checkbox>
						</div>
						<div className="r-mb-10">
							<Checkbox
								indeterminate={
									itemSortAuthority.notClassifIdList.length &&
									itemSortAuthority.notClassifIdList.length !=
										groupList.length
								}
								checked={
									!itemSortAuthority.notClassifIdList.length
								}
								onChange={(e) =>
									changeItemAuthority({
										notClassifIdList: e.target.checked
											? []
											: groupList.map(
													(s) => s.classifyId
											  ),
									})
								}
							>
								全部分类
							</Checkbox>
						</div>
						<div className="r-ml-24">
							<Checkbox.Group
								style={{ width: "100%" }}
								value={groupList
									.filter(
										(s) =>
											!itemSortAuthority?.notClassifIdList?.includes(
												s.classifyId
											)
									)
									.map((s) => s.classifyId)}
								onChange={(e) =>
									changeItemAuthority({
										notClassifIdList: groupList
											?.filter(
												(s) =>
													!e?.includes(s.classifyId)
											)
											.map((s) => s.classifyId),
									})
								}
							>
								{groupList?.map((item) => (
									<div
										key={item.classifyId}
										style={{
											width: "50%",
											display: "inline-block",
											marginBottom: "4px",
										}}
									>
										<Checkbox value={item.classifyId}>
											{item.classifyName}
										</Checkbox>
									</div>
								))}
							</Checkbox.Group>
						</div>
					</div>
				</Tabs.TabPane>
				<Tabs.TabPane tab="数据权限" key={authType.数据权限}>
					<div className="r-mt-12 r-ml-20">
						<p className="r-c-error r-mb-12">
							注：取消勾选后，对应子账号无此字段数据的查看权限
						</p>
						<Checkbox.Group
							value={fieldsAuthorityCheckedList}
							onChange={handleFieldsAuthorityChangeItem}
						>
							{FieldsAuthorityList.map((item) => (
								<Checkbox key={item.value} value={item.value}>
									{item.name}
									{item.warnText ? (
										<Tooltip title={item.warnText}>
											<QuestionCircleOutlined className="r-c-gray r-ml-2" />
										</Tooltip>
									) : null}
								</Checkbox>
							))}
						</Checkbox.Group>
					</div>
				</Tabs.TabPane>
				<Tabs.TabPane tab="面单网点权限" key={authType.面单网点权限}>
					<Temp
						permData={permData}
						onCheckedTemp={(val) => setTempCheckedKeys(val)}
					/>
				</Tabs.TabPane>
				<Tabs.TabPane tab="快递模版权限" key={authType.快递模版权限}>
					<ExpressTemp
						permData={permData}
						onCheckedTemp={(val) => {
							setExCheckedData((prev) => ({ ...prev, ...val }));
						}}
					/>
				</Tabs.TabPane>
				<Tabs.TabPane tab="仓库权限" key={ authType.仓库权限 }>
					<StorageTemp permData={ permData } onCheck={(val)=>{console.log("val:::", val);
					 setStorageAuthority((prev)=>{return {...prev, ...val}})}}/>
				</Tabs.TabPane>
				{userStore.hasWaveManagePermission&&<Tabs.TabPane tab="PDA权限" key={authType.PDA权限}>
					<div className="pda-authority-container r-ml-20">
						<div className="r-mb-6">
							<Checkbox 
								checked={pdaAuthority.pickingHall}
								onChange={e => setPdaAuthority(prev => ({
									...prev,
									pickingHall: e.target.checked
								}))}
							>
								拣货大厅
							</Checkbox>
						</div>
						<div className="r-mb-6">
							<Checkbox 
								checked={pdaAuthority.myPicking}
								onChange={e => setPdaAuthority(prev => ({
									...prev,
									myPicking: e.target.checked
								}))}
							>
								我的拣货
							</Checkbox>
						</div>
						<div className="r-mb-6">
							<Checkbox 
								checked={pdaAuthority.abnormalRefund}
								onChange={e => setPdaAuthority(prev => ({
									...prev,
									abnormalRefund: e.target.checked
								}))}
							>
								异常退款
							</Checkbox>
						</div>
						<div className="r-mb-6">
							<Checkbox 
								checked={pdaAuthority.warehouseSlotSort}
								onChange={e => setPdaAuthority(prev => ({
									...prev,
									warehouseSlotSort: e.target.checked
								}))}
							>
								货位排序
							</Checkbox>
						</div>
						<div className="r-mb-6">
							<Checkbox 
								checked={pdaAuthority.itemStock}
								onChange={e => setPdaAuthority(prev => ({
									...prev,
									itemStock: e.target.checked
								}))}
							>
								商品入库
							</Checkbox>
						</div>
					</div>
				</Tabs.TabPane>}
			</Tabs>

			{showFunctionAuthorityModal ? (
				<Modal
					onCancel={() => setShowFunctionAuthorityModal(false)}
					onOk={(e) => onOk(e, false)}
					visible
					title={<>功能权限【{functionAuthorityData?.title}】</>}
					centered
					maskClosable={false}
				>
					<Checkbox.Group
						style={{ width: "100%" }}
						value={functionAuthorityCheckedList}
						onChange={handleFunctionAuthorityChangeItem}
					>
						<Row gutter={[0, 8]}>
							{functionAuthorityData?.functionChildren?.map(
								(item) => (
									<Col span={8} key={item.templateCode}>
										<Checkbox value={item.templateCode}>
											{item.title}
										</Checkbox>
									</Col>
								)
							)}
						</Row>
					</Checkbox.Group>
				</Modal>
			) : ''}
            {scanAndPackingShow? (
				<Modal
					onCancel={ () => setScanAndPackingShow(false) }
					onOk={ () => setScanAndPackingShow(false) }
					visible
					title={ <>功能权限【扫快递单发货】</> }
					centered
					maskClosable={ false }
				>
					<Checkbox checked={ miniAppAuthority?.scanAndPackingAuthority?.send } onChange={ e => changeMiniAppAuthority({ scanAndPackingAuthority: {...miniAppAuthority?.scanAndPackingAuthority, send: e.target.checked}}) }>
						立即发货
					</Checkbox>
					<Checkbox checked={ miniAppAuthority?.scanAndPackingAuthority?.modifyRemark } onChange={ e => changeMiniAppAuthority({ scanAndPackingAuthority: {...miniAppAuthority?.scanAndPackingAuthority, modifyRemark: e.target.checked}}) }>
						修改卖家备注
					</Checkbox>
					<Checkbox checked={ miniAppAuthority?.scanAndPackingAuthority?.modifySysMemo } onChange={ e => changeMiniAppAuthority({ scanAndPackingAuthority: {...miniAppAuthority?.scanAndPackingAuthority, modifySysMemo: e.target.checked}}) }>
						修改线下备注
					</Checkbox>
				</Modal>
			) : ''}
			{showMiniAppFunctionAuthorityModal?.show ? (
				<Modal
					onCancel={ () => setShowMiniAppFunctionAuthorityModal({show: false, isSingle: true}) }
					onOk={ () => setShowMiniAppFunctionAuthorityModal({show: false, isSingle: true}) }
					visible
					title={ <>功能权限【{showMiniAppFunctionAuthorityModal.isSingle? "单品库存" : "组合库存"}】</> }
					centered
					maskClosable={false}
				>
					{
						showMiniAppFunctionAuthorityModal.isSingle ? (
							<>
								<Checkbox checked={ miniAppAuthority?.singleProductInventory?.modifyStock } onChange={ e => changeMiniAppAuthority({ singleProductInventory: {...miniAppAuthority?.singleProductInventory, modifyStock: e.target.checked}}) }>
									修改实际总库存
								</Checkbox>
								<Checkbox checked={ miniAppAuthority?.singleProductInventory?.modifyRemark } onChange={ e => changeMiniAppAuthority({ singleProductInventory: {...miniAppAuthority?.singleProductInventory, modifyRemark: e.target.checked}}) }>
									修改备注
								</Checkbox>
							</>
						) : (
							<Checkbox checked={ miniAppAuthority?.combinationProductInventory?.modifyRemark } onChange={ e => changeMiniAppAuthority({ combinationProductInventory: {...miniAppAuthority?.combinationProductInventory, modifyRemark: e.target.checked}}) }>
								修改备注
							</Checkbox>
						)
					}

					<Checkbox.Group
						style={{ width: "100%" }}
						value={functionAuthorityCheckedList}
						onChange={handleFunctionAuthorityChangeItem}
					>
						<Row gutter={[0, 8]}>
							{functionAuthorityData?.functionChildren?.map(
								(item) => (
									<Col span={8} key={item.templateCode}>
										<Checkbox value={item.templateCode}>
											{item.title}
										</Checkbox>
									</Col>
								)
							)}
						</Row>
					</Checkbox.Group>
				</Modal>
			) : (
				""
			)}
		</Modal>

	);
};

export default observer(PermSettingModal);
