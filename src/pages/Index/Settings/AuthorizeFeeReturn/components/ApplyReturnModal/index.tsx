import React, { useEffect, useState, useRef, useMemo } from 'react';
import { Form, Button, Input, Card, Statistic, Empty, Tooltip, Modal, Row, Col, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import _ from "lodash";
import cs from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { observer } from 'mobx-react';
import SearchTable from '@/components/SearchTable';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import ReturnListModal from '../ReturnListModal';
import { AuthorizeFeeReturnApplyReturnApi } from '@/apis/setting/smartExpress';
import { AuthorizeFeeReturnApplyReturnRequest } from '@/types/schemas/setting/smartExpress';
import ShopSingleSelect from '@/components-biz/ShopListSelect/shopSingleSelect';

interface ApplyModalProp {
	visible: boolean,
	onCancel: ()=>void,
	onOk: ()=>void
}

const ApplyReturnModal = (props:ApplyModalProp) => {
	const { visible, onOk, onCancel } = props;
	const [form] = useForm();
	const [loading, setLoading] = useState(false);
	const [selectItem, setSelectItem] = useState<AuthorizeFeeReturnApplyReturnRequest>(null);
	const [listModalShow, setListModalShow] = useState(false);
	const ref = useRef<SearchTableRefProps>(); // 获取表单对象
	const selectReturnItem = () => {
		setListModalShow(true);
	};
	const handleCancel = () => {
		form.resetFields();
		setSelectItem(null);
		onCancel && onCancel();
	};
	const handleOk = async() => {
		try {
			if (!selectItem) {
				message.error("请选择返款订单");
				return;
			}
			const res = await form.validateFields();
			if (!res) return;
			setLoading(true);
			const { returnAlipayAccount, returnAlipayName } = form.getFieldsValue();
			const { platform = "", sellerNick = "", sellerId = "", platformOrderId = "", innerOrderId = "", returnAmount = "", purchaseAmount = "" } = selectItem;
			const params = {
				platform,
				sellerNick,
				sellerId,
				platformOrderId,
				innerOrderId,
				returnAmount,
				purchaseAmount,
				returnAlipayAccount,
				returnAlipayName,
			};
			AuthorizeFeeReturnApplyReturnApi(params).then((res) => {
				message.success("申请成功");
				setLoading(false);
				setSelectItem(null);
				form.resetFields();
				onOk && onOk();
			}).catch((error) => {
				setLoading(false);
				message.error("申请失败");
			});
		} catch (error) {
			setLoading(false);
		}


	};
	const handleListModalCancel = () => {
		setListModalShow(false);
	};
	const handleListModalOk = (item) => {
		setListModalShow(false);
		const params = {
			returnAmount: item.returnAmount,
			platInfo: { shopId: item.sellerId, plat: item.platform }
		};
		setSelectItem(item);
		form.setFieldsValue({ ...params });
	};
	return (
		<>
			<Modal
				title="申请返款"
				visible={ visible }
				centered
				closable
				maskClosable={ false }
				width={ 600 }
				onCancel={ () => { handleCancel(); } }
				onOk={ () => { handleOk(); } }
				confirmLoading={ loading }
			>
				<Form
					form={ form }
					labelCol={ { style: { width: 120 } } }
					wrapperCol={ { span: 14 } }
				>
					<Form.Item
						name="amount"
						label="选择返款订单"
					>
						<Button type="primary" size="small" onClick={ () => { selectReturnItem(); } }>选择</Button>
					</Form.Item>
					<Form.Item
						name="platInfo"
						label="平台店铺"
					>
						<ShopSingleSelect
							style={ { width: '170px' } }
							size="small"
							isHasEbillPlat
							disabled
						/>
					</Form.Item>
					<div className="r-flex">
						<Form.Item
							name="returnAmount"
							label="返款金额"
						>
							<Input disabled suffix="元" size="small" style={ { width: 160 } } />
						</Form.Item>
						<Button
							type="link"
							style={ { paddingLeft: 4, paddingTop: 0 } }
							onClick={ (e) => {
								e.stopPropagation();
								window.open(" http://helperp.kuaidizs.cn/a401/c2f6/7102", '_blank');
							} }
						>平台授权费返还说明
						</Button>
					</div>
					<Form.Item
						name="returnAlipayName"
						label="返款支付宝姓名"
						rules={ [
							{ required: true, message: '请输入返款支付宝姓名' },
							{ pattern: /^[\u4e00-\u9fa5()（）]{2,20}$/, message: '姓名必须为2-20位中文，支持括号' }
						] }
					>
						<Input size="small" style={ { width: 320 } } placeholder="请输入2-20位中文姓名" />
					</Form.Item>
					<Form.Item
						name="returnAlipayAccount"
						label="返款支付宝账号"
						rules={ [
							{ required: true, message: '请输入返款支付宝账号' },
							{
								validator: (_, value) => {
									if (!value) return Promise.resolve();
									const phoneReg = /^1[3-9]\d{9}$/;
									const emailReg = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
									if (phoneReg.test(value) || emailReg.test(value)) {
										return Promise.resolve();
									}
									return Promise.reject(new Error('请输入正确的手机号或邮箱'));
								}
							}
						] }
					>
						<Input size="small" style={ { width: 320 } } placeholder="请输入手机号或邮箱" />
					</Form.Item>
				</Form>
			</Modal>
			<ReturnListModal visible={ listModalShow } onCancel={ () => { handleListModalCancel(); } } onOk={ (item) => { handleListModalOk(item); } } />
		</>
	);
};

export default observer(ApplyReturnModal);
