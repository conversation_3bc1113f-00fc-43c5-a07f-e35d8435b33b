import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Alert, Button, Checkbox, Dropdown, Form, Input, Menu, message, Modal, Progress, Select, Tag, Tooltip } from 'antd';
import qs from 'qs';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { cloneDeep, debounce } from 'lodash';
import { CopyOutlined, LoadingOutlined, SettingOutlined, FileExcelOutlined, QuestionCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useHistory, useLocation } from 'react-router-dom';
import { observer } from 'mobx-react';
import event from '@/libs/event';
import SearchTable from '@/components/SearchTableVirtual';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { SearchTableRefProps } from "@/components/SearchTableVirtual/SearchTable";
import s from './index.module.scss';
import { DatePickerKey, getCacheDateRange } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import { EnumStringSelect } from '@/components/Select/EnumSelect';
import InputMulti from "@/components/Input/InputMulti";
import { KddExNameSelect } from '@/pages/Report/components/kddTempSelect';
import { HandleStatusEnum, SendStatusEnum, WarnStatusEnum, INumList, warnKeyMap, tradeStatusTextEnum, handleWarnTradeDecrypt, getReceiverAddress, pasteAddressFn } from './utils';
import { BatchUpdateWarningApi, GetWarningDetailApi, QueryTradeWarningApi, QueryTradeWarningCountApi, QueryTradeWarningTaskConfig, UpdateTradeWarningTaskConfig, FastRefundSelectRefundByPtTidListApi, FastRefundFastRefundInStockApi } from '@/apis/trade';
import { ItemItemUserConfigQueryConfigApi, ItemItemUserConfigUpdateConfigApi } from '@/apis/trade/search';
import { MallTrade, TradeWarningInfo } from '@/types/trade/index';
import { copyMenu, getOrderRefundStatus, getTradeFlag, getTradeFlagTag, getTradePlatformLabel, getTradeStatusLabel } from '@/pages/Trade/utils';
import { copyToPaste, splitFxgTid } from '@/utils';
import Icon from '@/components/Icon';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import { ExceptionWarnTopTabs, PlatIconEnum } from './components/TopTab';
import { getMultiShops, getShopName, isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import PreWarnSetModal from './components/PreWarnSetModal';
import { downloadCenter } from '../Index/DownloadCenter/utils';
import { ModulesFunctionEnum } from '@/types/schemas/setting/download';
import ShowLogistic from '../Report/KddLog/components/ShowLogistic';
import WaresInfo from '@/components-biz/WaresInfo';
import userStore from '@/stores/user';
import { weightUnit } from '../Index/Settings/System/constants';
import { ITradeRefundStatus, ITradeStatus } from '../Trade/interface';
import BatchModifyMemoModal from '../Trade/components/BatchModifyMemoModal';
import BatchModifySysMemoModal from "../Trade/components/BatchModifyLocalMemoModal";
import { OrderSourceType } from '../Trade/components/SearchContainer/SearchCondition';
import { tradeStore } from '@/stores';
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import { lowVersionLock } from '@/components-biz/LowVersionControlModal';
import { PageNameControlEnum } from '@/components-biz/LowVersionControlModal/constant';
import BatchCancelYdNoModel from '@/components-biz/BatchCancelYdNoModel';
import ResultModal from './components/ResultModal';

const AddressInfoCom = ({ pack, afterDecrypt }) => {
	const emptyTmp = () => {
		return pack.isDecrypted ? '' : '***';
	};
	const updateWarnTradeDecrypt = async(pack) => {
		let decryptInfo = await handleWarnTradeDecrypt(pack);
		console.log('%c [ decryptInfo ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', decryptInfo);
		const newPack = { ...pack };
		// 如果名字和电话没解出来，就不能更新信息
		if (decryptInfo && (decryptInfo.receiverName || decryptInfo.receiverPhone)) {
			newPack.receiverNameMask = decryptInfo.receiverName ?? '';
			newPack.receiverMobileMask = decryptInfo.receiverPhone ?? '';
			newPack.receiverAddressMask = decryptInfo.receiverAddress ?? '';
			newPack.isDecrypted = true;
		}
		afterDecrypt(newPack);
		return newPack;
	};
	const copyInfo = async() => {
		if (pack.isDecrypted) {
			pasteAddressFn(pack);
		} else {
			let newPack = await updateWarnTradeDecrypt(pack);
			pasteAddressFn(newPack);
		}
	};
	return (
		<Dropdown placement="topCenter" overlay={ copyMenu(copyInfo) }>
			<div className="r-pointer r-lh-18">
				<div>{pack.receiverNameMask ?? emptyTmp()}，{pack.receiverMobileMask ?? emptyTmp()}</div>
				<div className="r-mb-2">
					{pack.receiverProvince}
					{pack.receiverCity}
					{pack.receiverCounty}
					{getReceiverAddress(pack) ?? emptyTmp()}
					{!pack.isDecrypted ? (
						<span onClick={ (e) => { updateWarnTradeDecrypt(pack); } }>
							<Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5 } } />
						</span>
					) : ''}
				</div>
			</div>
		</Dropdown>
	);
};


const defaultParams = {
	processingStatus: HandleStatusEnum.待处理,
	tradeStatus: ''
};
let GLOBAL_PARAMS = {};

const ExceptionWarn: React.FC = (props) => {
	const [dataSource, setDataSource] = useState<TradeWarningInfo[]>([]);
	const [expandedKeys, setExpandedKeys] = useState([]);
	const [warnDetail, setWarnDetail] = useState<{ [key: string]: MallTrade }>({});
	const [detailLoading, setDetailLoading] = useState<{ [key: string]: boolean }>({});
	const [form] = useForm();
	const [selectedRows, setSelectedRows] = useState<TradeWarningInfo[]>([]);
	const ref = useRef<SearchTableRefProps>();
	const [preWarnSetModalVisible, setPreWarnSetModalVisible] = useState(false);
	const [numInfo, setNumInfo] = useState<INumList>({});
	const [markOkLoading, setMarkOkLoading] = useState(false);
	const [warningConfig, setWarningConfig] = useState({});
	const history = useHistory();
	const { setModifyMemoPackage, setIsShowBatchModifyMemoModal, isShowBatchModifyMemoModal, setModifySysMemoPackage, setIsShowBatchSysMemoModal } = tradeStore;
	const { isSupplierAccount, isFreeSupplierAccount, isShowZeroStockVersion } = userStore;
	const { search: locationSearch } = useLocation();
	const [curSearch, setCurSearch] = useState({});
	const [total, setTotal] = useState(0);
	const [labelSettingLockLogistics, setLabelSettingLockLogistics] = useState(false);// 物流预警功能控制 如果tj关闭对应功能 返回true
	const [labelSettingLockIntercept, setLabelSettingLockIntercept] = useState(false);// 快递拦截功能控制 如果tj关闭对应功能 返回true
	// 回收单号弹窗
	const [cancelYdNovisible, setCancelYdNovisible] = useState(false);
	const [cancelYdNoData, setCancelYdNoData] = useState({});
	const [isRefundInStockModalVisible, setIsRefundInStockModalVisible] = useState(false);
	const [isRefundWarningModalVisible, setIsRefundWarningModalVisible] = useState(false);
	const [validRefundRows, setValidRefundRows] = useState([]);
	const [refundStockFinishCancelYdNo, setRefundStockFinishCancelYdNo] = useState(false);
	const [refundStockFinishUpdateStatus, setRefundStockFinishUpdateStatus] = useState(false);
	// 添加进度相关状态
	const [isProcessing, setIsProcessing] = useState(false);
	const [progress, setProgress] = useState(0);
	const [processResult, setProcessResult] = useState({
		successNum: 0,
		failNum: 0,
		stockErrorList: [],
		sidErrorList: []
	});
	const [showProcessResult, setShowProcessResult] = useState(false);

	useEffect(() => {
		getWaringConfig();
		const fn = async() => {
			const res = await lowVersionLock(PageNameControlEnum.物流预警);
			setLabelSettingLockLogistics(!!res);
			const res2 = await lowVersionLock(PageNameControlEnum.快递拦截);
			setLabelSettingLockIntercept(!!res2);
		};
		fn();

	}, []);

	useEffect(() => {
		form.resetFields();
		form.setFieldsValue(defaultParams);
	}, [form]);

	useEffect(() => {
		const searchParam = qs.parse(locationSearch.substring(1));
		form.setFieldsValue({
			warnStatus: searchParam.warnStatus ? Number(searchParam.warnStatus) : ''
		});
		if (searchParam?.warnStatus !== curSearch?.warnStatus) {
			setCurSearch(searchParam);
			ref.current.submit();
		}
		if (searchParam.openSetting) {
			setPreWarnSetModalVisible(true);
		}
	}, [locationSearch]);

	const getFastRefundConfig = async() => {
		ItemItemUserConfigQueryConfigApi({
			itemUserConfigBizEnum: "TRADE_WARNING_SET"
		}).then(res => {
			if (res?.[0]) {
				const config = JSON.parse(res?.[0].value || "{}");
				console.log(config);
				setRefundStockFinishCancelYdNo(config.refundStockFinishCancelYdNo == 1);
				setRefundStockFinishUpdateStatus(config.refundStockFinishUpdateStatus == 1);
			}
		});
	};

	const updateFastRefundConfig = async(params) => {
		ItemItemUserConfigUpdateConfigApi({
			itemUserConfigBizEnum: "TRADE_WARNING_SET",
			value: JSON.stringify(params)
		}).then(res => {
			message.success("设置成功");
			setRefundStockFinishCancelYdNo(params.refundStockFinishCancelYdNo == 1);
			setRefundStockFinishUpdateStatus(params.refundStockFinishUpdateStatus == 1);
		}).catch(err => {
			message.error(`设置失败：${err}`);
		});
	};

	const getWaringConfig = () => {
		QueryTradeWarningTaskConfig().then(res => {
			setWarningConfig(res);
		});
	};

	const updateWaringConfig = async(params) => {
		await UpdateTradeWarningTaskConfig(params);
		QueryTradeWarningTaskConfig().then(res => {
			setWarningConfig(res);
		});
	};

	const fetchSystemList = async(info) => {
		setSelectedRows([]);
		setExpandedKeys([]);
		setWarnDetail({});
		setDetailLoading({});
		const {
			warnStatus,
			warnStatusList = [],
			searchTime,
			tids,
			ptTids,
			exNumberList,
			platformInfo = {},
			...restInfo
		} = info;

		let { plats, plat_sellerIds } = platformInfo;
		let multiShops = await getMultiShops({ plats, plat_sellerIds });

		const search = {
			...restInfo,
			multiShops,
			startTime: searchTime?.[0]?.format("YYYY-MM-DD HH:mm:ss") ?? '',
			endTime: searchTime?.[1]?.format("YYYY-MM-DD HH:mm:ss") ?? '',
		};
		if (tids?.length) {
			search.tids = tids?.split(',').filter(i => i);
		}
		if (ptTids?.length) {
			search.ptTids = ptTids?.split(',').filter(i => i);
		}
		if (exNumberList?.length) {
			search.exNumberList = exNumberList?.split(',').filter(i => i);
		}
		console.log('%c [ plats, plat_sellerIds ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', plats, plat_sellerIds);
		search.isPlatformEmptyQuery = !plats?.length && !plat_sellerIds?.length;
		let totalData = numInfo;
		// if (info.pageNo == 1) {
		totalData = await QueryTradeWarningCountApi(search);
		setNumInfo(totalData);
		// }
		const params = { ...search };
		params.warnStatusList = [...new Set([...warnStatusList, warnStatus])].filter(Boolean);
		GLOBAL_PARAMS = params;
		return QueryTradeWarningApi(params).then(res => {
			// const infoKey = warnKeyMap[warnStatus] ?? 'totalNum';
			if (info.pageNo == 1) {
				setTotal(res.totalNum);
				res.total = res.totalNum;
			} else {
				res.total = total;
			}
			const list = (res.list || []).map(item => {
				return {
					...item,
					rowId: `${item.id}_${item.tid}`
				};
			});
			setDataSource(list);
			return res;
		});
	};

	const tabList = useMemo(() => {
		return [{
			key: WarnStatusEnum.申请单号未打印,
			text: '申请单号未打印',
			tip: '监控在助手erp中存在【申请单号未打印】的订单，可筛选后进行单号回收',
			// 地址预警：抖音、拼多多、淘宝、京东、快手、小红书
			// plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			num: numInfo.appliedAndUnprintedNum ?? 0,
			setting: true,
			point: Pointer.订单异常预警_快速筛选_申请单号未打印
		}, {
			key: WarnStatusEnum.单号打印多次,
			text: '单号打印多次',
			tip: '监控在助手erp中存在【单号打印多次】的订单，可筛选后进行单号回收',
			// 地址预警：抖音、拼多多、淘宝、京东、快手、小红书
			// plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			num: numInfo.repeatPrintNum ?? 0,
			setting: true,
			point: Pointer.订单异常预警_快速筛选_单号打印多次
		}, {
			key: WarnStatusEnum.已打印未发货,
			text: '已打印未发货',
			tip: '监控在助手erp中存在【已打印未发货】的订单，可筛选后进行单号回收',
			// 地址预警：抖音、拼多多、淘宝、京东、快手、小红书
			// plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			num: numInfo.printedAndUnshipNum ?? 0,
			setting: true,
			point: Pointer.订单异常预警_快速筛选_已打印未发货
		}, {
			key: WarnStatusEnum.发货失败,
			text: '发货失败',
			tip: '监控在助手erp中存在【发货失败】的订单，可筛选复制单号后前往订单打印页面重发',
			// 售后预警: 抖音、拼多多、淘宝、淘工厂、阿里、京东、快手、视频号，小红书
			// plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.淘工厂, PlatIconEnum.阿里, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.视频号, PlatIconEnum.小红书],
			num: numInfo.shipFailWarningNum ?? 0,
			point: Pointer.订单异常预警_快速筛选_发货失败
		}, {
			key: WarnStatusEnum.打印后改地址,
			text: '打印后改地址',
			tip: '当前支持以下平台的改地址变更提醒',
			// 地址预警：抖音、拼多多、淘宝、京东、快手、小红书
			plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			num: numInfo.sendWarningNum ?? 0,
			point: Pointer.订单异常预警_快速筛选_打印后改地址
		}, {
			key: WarnStatusEnum.打印后改备注,
			text: '打印后改备注',
			tip: '当前支持以下平台的改备注变更提醒',
			// 备注预警：抖音、拼多多、淘宝、阿里、京东、快手、小红书
			plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.阿里, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			num: numInfo.memoWarningNum ?? 0,
			point: Pointer.订单异常预警_快速筛选_打印后改备注
		}, {
			key: WarnStatusEnum.打印后未发货退款,
			text: '打印后未发货退款',
			tip: '监控系统中未操作发货产生售后情况，支持以下平台的产生售后提醒',
			// 售后预警: 抖音、拼多多、淘宝、淘工厂、阿里、京东、快手、视频号，小红书
			plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.淘工厂, PlatIconEnum.阿里, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.视频号, PlatIconEnum.小红书],
			num: numInfo.refundWarningNum ?? 0,
			point: Pointer.订单异常预警_快速筛选_打印后未发货退款
		},
		{
			key: WarnStatusEnum.打印后已发货退款,
			text: '打印后已发货退款',
			tip: '监控系统中发货后产生售后情况，支持以下平台的产生售后提醒',
			plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.淘工厂, PlatIconEnum.阿里, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.视频号, PlatIconEnum.小红书],
			num: numInfo.printedAndShippedRefundNum ?? 0,
			point: Pointer.订单异常预警_快速筛选_打印后已发货退款
		},
		{
			key: WarnStatusEnum.揽收前退款,
			text: <span>揽收前退款<span style={ { color: '#FFA500' } }>{labelSettingLockLogistics && '【高配版功能】'}</span></span>,
			tip: (
				<div>
					<div>1.监控在助手erp中存在【揽收前退款】的订单，可筛选后进行单号回收</div>
					{labelSettingLockLogistics && (
						<div>2.此功能需开通【物流预警】功能后生效
							<span
								onClick={ (e) => {
									e.stopPropagation();
									history.push('/report/logisticsWarning');
								} }
								style={ { color: '#409EFF', cursor: 'pointer', marginLeft: '5px' } }
							>前往开通
							</span>
						</div>
					)}
				</div>
			),
			// 地址预警：抖音、拼多多、淘宝、京东、快手、小红书
			// plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			num: numInfo.preAcceptRefundNum ?? 0,
			point: Pointer.订单异常预警_快速筛选_揽收前退款
		}, {
			key: WarnStatusEnum.即将超时揽收,
			text: '即将超时揽收',
			// tip: '暂不支持淘宝/天猫平台',
			setting: true,
			// 地址预警：抖音、拼多多、淘宝、京东、快手、小红书
			// plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			num: numInfo.nearTimeoutAcceptWarningNum ?? 0,
			point: Pointer.订单异常预警_快速筛选_即将超时揽收
		}, {
			key: WarnStatusEnum.超时未揽收,
			text: '超时未揽收',
			// tip: '暂不支持淘宝/天猫平台',
			// 地址预警：抖音、拼多多、淘宝、京东、快手、小红书
			// plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			num: numInfo.timeoutUnacceptableWarningNum ?? 0,
			point: Pointer.订单异常预警_快速筛选_超时未揽收
		}, {
			key: WarnStatusEnum.发货后未揽收,
			text: <span>发货后未揽收<span style={ { color: '#FFA500' } }>{labelSettingLockLogistics && '【高配版功能】'}</span></span>,
			// tip: '监控在助手erp中存在【揽收前退款】的订单，可筛选后进行单号回收',
			// 地址预警：抖音、拼多多、淘宝、京东、快手、小红书
			// plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			// num: numInfo.preAcceptRefundNum ?? 0
			point: Pointer.订单异常预警_快速筛选_发货后未揽收
		}, {
			key: WarnStatusEnum.快递拦截,
			text: <span>快递拦截<span style={ { color: '#FFA500' } }>{labelSettingLockIntercept && '【高配版功能】'}</span></span>,
			// tip: '监控在助手erp中存在【揽收前退款】的订单，可筛选后进行单号回收',
			// 地址预警：抖音、拼多多、淘宝、京东、快手、小红书
			// plats: [PlatIconEnum.抖音, PlatIconEnum.拼多多, PlatIconEnum.淘宝, PlatIconEnum.京东, PlatIconEnum.快手, PlatIconEnum.小红书],
			// num: numInfo.preAcceptRefundNum ?? 0
			point: Pointer.订单异常预警_快速筛选_快递拦截
		}];
	}, [numInfo]);

	const warnStatusSelect = () => {
		let renderOptions = null;
		let keys = [WarnStatusEnum.申请单号未打印, WarnStatusEnum.单号打印多次, WarnStatusEnum.已打印未发货, WarnStatusEnum.发货失败, WarnStatusEnum.打印后改地址, WarnStatusEnum.打印后改备注, WarnStatusEnum.打印后未发货退款, WarnStatusEnum.打印后已发货退款, WarnStatusEnum.揽收前退款, WarnStatusEnum.即将超时揽收, WarnStatusEnum.超时未揽收, WarnStatusEnum.发货后未揽收, WarnStatusEnum.快递拦截];
		renderOptions = keys.map(key => {
			return <Select.Option value={ Number(key) } key={ key }>{WarnStatusEnum[key]}</Select.Option>;
		});
		return (
			<Select mode="multiple" maxTagCount="responsive" placeholder="异常告警原因" showArrow allowClear style={ { width: 160 } }>
				{renderOptions}
			</Select>
		);
	};

	const FormFieldList: FormItemConfig[] = [
		{
			label: "",
			name: 'warnStatus',
			colProps: {
				span: 24
			},
			children: <ExceptionWarnTopTabs tabList={ tabList } warningConfig={ warningConfig } updateWaringConfig={ updateWaringConfig } />
		},
		{
			name: 'platformInfo',
			label: '',
			children: <ShopMultiSelect style={ { width: '170px' } } size="small" />
		},
		{
			name: 'orderSource',
			label: '',
			className: s.condition1,
			children: <OrderSourceType hideLight bgHighLight isSupplierAccount={ isSupplierAccount || isFreeSupplierAccount } />,
		},
		{
			name: 'searchTime',
			label: '警告时间',
			initialValue: getCacheDateRange(DatePickerKey.exception_warn_modal) || [dayjs().subtract(90, 'days').startOf('day'), dayjs().endOf('day')],
			children: <KdzsDateRangePicker1 datePickerKey={ DatePickerKey.exception_warn_modal } cacheQuickChoose useServeTime />,
		},
		{
			name: 'processingStatus',
			label: '处理状态',
			children: <EnumStringSelect style={ { width: '150px' } } enum={ HandleStatusEnum } placeholder="全部" />,
		},
		{
			name: 'tradeStatus',
			label: '发货状态',
			children: <EnumStringSelect style={ { width: '150px' } } enum={ SendStatusEnum } placeholder="全部" />,
		},
		{
			name: 'warnStatusList',
			label: '',
			children: warnStatusSelect(),
			// children: <InputMulti size="small" placeholder="异常告警原因" maxInputNum={ 50 } style={ { flex: 1 } } />,
		},
		{
			name: 'ptTids',
			label: '',
			children: <InputMulti size="small" placeholder="订单编号" maxInputNum={ 2000 } style={ { flex: 1 } } />,
		},
		{
			name: 'exCodeList',
			label: '',
			children: <KddExNameSelect valueKey="ExCode" style={ { width: '150px' } } />,
		},
		{
			name: 'exNumberList',
			label: '',
			children: <InputMulti size="small" placeholder="快递单号" maxInputNum={ 1000 } style={ { flex: 1 } } />,
		}
		// {
		// 	name: 'tids',
		// 	label: '',
		// 	children: <InputMulti size="small" placeholder="系统单号" maxInputNum={ 200 } style={ { flex: 1 } } />,
		// }
	];

	const onAfterDecrypt = (newPack) => {
		setDataSource((pre: TradeWarningInfo[]) => {
			let cur = [...pre].map(i => {
				if (i.id == newPack.id) {
					return newPack;
				}
				return i;
			});
			return cur;
		});
	};

	// 表格列定义，与AntdTable使用一致
	const columns: ColumnsType<TradeWarningInfo> = [
		{
			title: '序号',
			width: 40,
			render: (_, __, index) => {
				return index + 1;
			},
		},
		{
			title: '平台店铺',
			width: 100,
			render(value, record, index) {
				const platform = record.platform.toLowerCase();
				return (
					<div className="r-flex">
						{getTradePlatformLabel(platform)}
						<div className="r-ml-5">
							{!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(record) ? '****' : getShopName({ plat: platform, sellerNick: record.sellerNick })}
						</div>
					</div>
				);
			},
		}, {
			title: '订单编号',
			width: 80,
			dataIndex: 'ptTid',
			render(value) {
				const copyInfo = () => copyToPaste(splitFxgTid(value));
				const tidAction = () => {
					history.push('/trade/index');
					setTimeout(() => {
						event.emit('tradeSetting.handleSearchParams', { ptTid: value });
					}, 0);
				};
				return (
					<>
						<span className="r-click" onClick={ tidAction }>{value}</span>
						<CopyOutlined className="r-ml-5 r-pointer" onClick={ copyInfo } />
					</>
				);
			}
		},
		{
			title: '异常预警原因			',
			width: 118,
			dataIndex: 'warningStatus',
			render(value, record) {
				return (
					<>
						{
							value === WarnStatusEnum.打印后未发货退款 ? (
								<div>
									{record.oldRefundWarningDisplay === true || record.oldRefundWarningDisplay === null ? WarnStatusEnum[value] : "打印后发生售后"}
								</div>
							) : <div>{WarnStatusEnum[value]}</div>
						}

						{
							(value === WarnStatusEnum.发货失败) && <div className="r-c-error">(失败原因：{record.warningReason})</div>
						}
						{
							(value === WarnStatusEnum.即将超时揽收) && <div className="r-c-error">({record.warningReason})</div>
						}
					</>
				);
			}
		},
		{
			title: '发货状态',
			width: 64,
			dataIndex: 'tradeStatus',
			render(value) {
				return tradeStatusTextEnum[value];
			}
		},
		{
			title: '留言备注',
			width: 160,
			dataIndex: 'sellerMemo',
			render(value, record, index) {
				const openModifyMemo = (list) => {
					const packList = list.map((item) => ({
						trades: [{
							sellerMemo: item.sellerMemo,
							sellerMemoFlag: item.sellerFlag,
							tid: item.tid,
							ptTid: item.ptTid,
						}],
						platform: item.platform.toLowerCase(),
						source: item.source,
						togetherId: item.tid,
						sellerId: item.sellerId,
						refundStatus: item.refundStatus || "",
					}));
					setIsShowBatchModifyMemoModal(true);
					setModifyMemoPackage(packList);
				};

				const modifyMemo = (item, index: number) => {
					console.log(item, 'modifyMemo');
					if (item.isNoTradeMess) {
						message.info('无主件不可进行备注');
						return;
					}
					if (item.exceptionType !== 1) {
						openModifyMemo([item]);
					} else {
						Modal.warning({
							title: '系统提示',
							content: '该订单已被锁定，不能进行该操作，请先处理异常',
						});
					}
				};
				 // 添加判断是否为京喜订单的逻辑
				 const hasJingxi = record.serviceTagList?.includes('jingxi');
				return (
					<>
						<div className={ s.buyerMessageCon }>
							<span className={ s.detailLabel }>留言：</span>
							{record.buyerMessage}
						</div>
						<div className={ s.detailCon }>
							<span className={ s.detailLabel }>备注：</span>
							{getTradeFlag(0, null, record?.sellerFlag)}
							{getTradeFlagTag(record?.sellerFlag, record?.sellerFlagTag)}
							{record.sellerMemo}
							{
								!hasJingxi && (
									<span className="r-ml-6 r-fc-1890FF">
										{record['sellerMemo']
											? <span className="r-pointer r-flex r-ai-c" onClick={ () => { modifyMemo(record, index); } }><Icon type="bianji" /></span>
											: <span className="r-pointer r-flex r-ai-c" onClick={ () => { modifyMemo(record, index); } }><Icon type="tianjiabeizhu" /></span>}
									</span>
								)
							}
							
						</div>
					</>
				);
			}
		},
		{
			title: '线下备注',
			width: 160,
			dataIndex: 'sysMemo',
			render(value, record) {
				// return tradeStatusTextEnum[value];
				const packList = [{
					trades: [{
						sellerMemo: record.sellerMemo,
						sellerMemoFlag: record.sellerFlag,
						tid: record.tid,
						ptTid: record.ptTid,
					}],
					platform: record.platform.toLowerCase(),
					source: record.source,
					togetherId: record.tid,
					sellerId: record.sellerId,
					refundStatus: record.refundStatus || "",
				}];
				return (
					<div className="r-flex r-fw-w r-mt-4">
						<p className="r-fc-black-65">线下备注：</p>
						<p>
							<span className="r-fc-black-65">{value}</span>
							<span className="r-as-c r-ml-2 r-fc-1890FF">
								{value
									? <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); setModifySysMemoPackage(packList); setIsShowBatchSysMemoModal(true); } }><Icon type="bianji" /></span>
									: <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); setModifySysMemoPackage(packList); setIsShowBatchSysMemoModal(true); } }><Icon type="tianjiabeizhu" /></span>}
							</span>
						</p>

					</div>
				);
			}
		},
		{
			title: '收件信息',
			width: 240,
			render(value, record: TradeWarningInfo) {
				return <AddressInfoCom pack={ record } afterDecrypt={ onAfterDecrypt } />;
			}
		},
		{
			title: '打印时间',
			width: 70,
			dataIndex: 'printTime',
			render(value) {
				return value ? (
					<>
						<div>{dayjs(value).format('YYYY-MM-DD')}</div>
						<div>{dayjs(value).format('HH:mm:ss')}</div>
					</>
				) : '--';
			}
		},
		{
			title: '快递单号',
			width: 110,
			dataIndex: 'waybillNo',
			render(value, record: TradeWarningInfo) {
				const noList = value?.split(',');
				if (!noList?.length) {
					return;
				}
				const node = noList?.map((item, index) => {
					return (
						<ShowLogistic ydNo={ item }>
							{index == 0 ? '' : ','}
							<span className={ s.hoverItem }> {item}</span>
						</ShowLogistic>
					);
				});
				return (<>{node}<CopyOutlined className="r-ml-5 r-pointer" onClick={ () => { copyToPaste(value); } } /></>);
			}
		},
		{
			title: '发货时间',
			width: 70,
			dataIndex: 'sysShipTime',
			render(value) {
				return value ? (
					<>
						<div>{dayjs(value).format('YYYY-MM-DD')}</div>
						<div>{dayjs(value).format('HH:mm:ss')}</div>
					</>
				) : '--';
			}
		}, {
			title: (
				<div>
					承诺揽收时间
					{/* <Tooltip title="暂不支持淘宝/天猫平台">
						<QuestionCircleOutlined style={ { marginLeft: '4px' } } />
					</Tooltip> */}
				</div>
			),
			dataIndex: 'lastShipTime',
			render(value, row) {
				const isOver = !!(row.timeoutMinutes > 0);
				const formatTimeoutDisplay = (minutes) => {
					if (!minutes) return '0分钟';
					const hours = Math.floor(Math.abs(minutes) / 60);
					const remainingMinutes = Math.abs(minutes) % 60;
					if (hours > 0 && remainingMinutes > 0) {
						return `${hours}小时${remainingMinutes}分钟`;
					} else if (hours > 0) {
						return `${hours}小时`;
					} else {
						return `${remainingMinutes}分钟`;
					}
				};
				return (value) ? (
					<>
						<div>{dayjs(value).format('YYYY-MM-DD')}</div>
						<div>{dayjs(value).format('HH:mm:ss')}</div>
						{
							 row.operator !== 'system' && (
								<>
									{
										isOver ? (
											<div>
												<span style={ { color: '#FD8204' } }>
													已超时
												</span>
												<span style={ { color: '#FD8204' } }>{formatTimeoutDisplay(row.timeoutMinutes)}</span>
											</div>
										) : (
											<div>
												<span style={ { color: '#FD8204' } }>{formatTimeoutDisplay(row.timeoutMinutes)}</span>
												<span style={ { color: '#FD8204' } }>
													后将超时揽收
												</span>
											</div>
										)
									}
								</>
							)
						}
						
					</>
				) : '--';
			}
		}, {
			title: '警告时间',
			width: 70,
			dataIndex: 'modified',
			render(value) {
				return value ? (
					<>
						<div>{dayjs(value).format('YYYY-MM-DD')}</div>
						<div>{dayjs(value).format('HH:mm:ss')}</div>
					</>
				) : '--';
			}
		},
		{
			title: (
				<>
					处理状态
					<span>
						{
							expandedKeys.length == dataSource.length ? (
								<Button type="link" onClick={ e => setExpandedKeys([]) }><Icon type="shouqi" size={ 14 } /></Button>
							) : (
								<Button type="link" onClick={ e => setExpandedKeys(dataSource.map(i => i.rowId)) }><Icon type="zhankai" size={ 14 } /></Button>
							)
						}
					</span>
				</>
			),
			width: 110,
			// fixed: true,
			dataIndex: 'processingStatus',
			render(value, record, index) {
				value += '';
				return (
					<>
						<span
							style={ { color: value == HandleStatusEnum.已处理 ? '#aaa' : '#FD8202' } }
						>
							{value == HandleStatusEnum.已处理 ? '已处理' : '待处理'}
						</span>
						{/* {
							expandedKeys.includes(record.id) ? (
								<span className="r-click" style={ { color: '#FD8202', marginLeft: '20px', userSelect: 'none' } } onClick={ e => setExpandedKeys(pre => pre.filter(i => i != record.id)) }>收起</span>
							) : (
								<span
									className="r-click"
									style={ { color: '#FD8202', marginLeft: '20px', userSelect: 'none' } }
									onClick={ e => {
										sendPoint(Pointer.订单异常预警_点击更多);
										setExpandedKeys(pre => [...pre, record.id]);
									} }
								>展开
								</span>
							)
						} */}
					</>
				);
			},
		}

	];
	const onFieldsChange = (changedValues, allValues) => {
		if ('warnStatus' in changedValues) {
			ref?.current?.submit();
		}
	};
	const onSelectChange = (newSelectedRowKeys, newSelectedRows) => {
		setSelectedRows(newSelectedRows);
	};

	const selectedRowKeys = useMemo(() => {
		return selectedRows.map((i) => i.rowId);
	}, [selectedRows]);

	const rowSelection = {
		selectedRowKeys,
		onChange: onSelectChange,
	};

	const handleMarkOk = async() => {
		const ids = selectedRows
			.filter(i => i.processingStatus == HandleStatusEnum.待处理)
			.map(i => i.id);
		if (ids.length == 0) {
			message.info("当前选择不包含待处理的预警，请重新选择");
			return;
		}
		setMarkOkLoading(true);
		try {
			await BatchUpdateWarningApi({ ids });
			message.success('成功');
			ref?.current?.refresh();
		} catch (error) {
			console.log('error: ', error);
		}
		setMarkOkLoading(false);


	};

	const handleCopyOk = (key, msg = '复制成功') => {
		if (!selectedRows?.length) {
			message.warning('请先勾选');
			return;
		}
		const tids = selectedRows.map(i => i[key]).join('\n');
		copyToPaste(tids, msg, true);
	};

	// 跳转批打页面查询
	const handleSearchTrade = (e: any) => {
		e.preventDefault();
		if (!selectedRows.length) {
			message.warning('请先勾选');
			return;
		}
		window.open(history.createHref({ pathname: "/trade/index", search: `tid=${selectedRows?.map(item => item.tid).join(',')}` }));
	};
	// 极速退货入库
	const batchRefundInStock = async() => {
		sendPoint(Pointer.订单异常预警_点击_极速退货入库);
		if (!selectedRows.length) {
			message.warning('请先勾选订单');
			return;
		}

		const validRows = selectedRows.filter(row => row.warningStatus === WarnStatusEnum.揽收前退款);
		const invalidRows = selectedRows.filter(row => row.warningStatus !== WarnStatusEnum.揽收前退款);

		if (invalidRows.length > 0) {
			setValidRefundRows(validRows);
			setIsRefundWarningModalVisible(true);
			return;
		}
		getFastRefundConfig();
		setIsRefundInStockModalVisible(true);
	};

	const handleRefundInStockOk = async() => {
		setIsRefundInStockModalVisible(false);
		setIsProcessing(true);
		setProgress(0);
		setProcessResult(null);

		try {
			// 第一步：查询售后数据
			const res = await FastRefundSelectRefundByPtTidListApi(
				{ 
					tradeWarningRequestList: selectedRows.map(item => {
						return {
							ptTid: item.ptTid,
							waybillNo: item.waybillNo,
							exCode: item.waybillCode,
							tel: item.sendMobile,
							id: item.id
						};
					}) 
				}
			);

			// 使用类型断言，res.data是数组
			const refundData = cloneDeep(res);
			if (!refundData?.length) {
				message.warning('未查询到符合条件的售后数据');
				setIsProcessing(false);
				return;
			}

			setProgress(10);

			// 第二步：分批处理数据
			const batchSize = 10; // 每批处理10条
			const batches = [];
			for (let i = 0; i < refundData.length; i += batchSize) {
				batches.push(refundData.slice(i, i + batchSize));
			}

			const results = [];
			let successNum = 0;
			let failNum = 0;
			const stockErrorList = [];
			const sidErrorList = [];

			// 同时发起所有批次请求
			
			let completedBatches = 0;
			const batchPromises = batches.map((batch, index) => {
				return FastRefundFastRefundInStockApi({
					refundFastPtTidDTOList: batch
				}).then(batchResult => {
					// 更新进度
					completedBatches++;
					const currentProgress = 10 + Math.floor((completedBatches / batches.length) * 80);
					setProgress(currentProgress);
					
					return { batchResult, batch, index };
				}).catch(error => {
					// 更新进度
					completedBatches++;
					const currentProgress = 10 + Math.floor((completedBatches / batches.length) * 80);
					setProgress(currentProgress);
					
					return { error, batch, index };
				});
			});

			// 等待所有请求完成
			const batchResults = await Promise.all(batchPromises);
			console.log('batchResults: ', batchResults);
			// 处理所有结果
			batchResults.forEach((result: any) => {
				const { batchResult, error, batch } = result;
				if (error) {
					// 请求失败
					failNum += batch.length;
					stockErrorList.push({
						errorInfo: error?.message || '网络请求失败',
						ptTid: batch.map((item: any) => item.ptTid).join(',')
					});
				} else if (batchResult?.success) {
					// 请求成功
					results.push(batchResult);
					successNum += batchResult?.successNum || 0;
					failNum += batchResult?.failNum || 0;
					
					if (batchResult?.errorList?.length) {
						batchResult?.errorList?.forEach(item => {
							if (item.sid) {
								sidErrorList.push(item);
							} else {
								stockErrorList.push(item);
							}
						});
					}
				} else {
					// 接口返回失败
					failNum += batch.length;
					stockErrorList.push({
						errorInfo: batchResult?.errorMessage || '接口调用失败',
						ptTid: batch.map((item: any) => item.ptTid).join(',')
					});
				}
			});

			setProgress(100);

			// const mockResult = {
			// 	"errorList": [
			// 		{
			// 			"errorInfo": "售后单状态不是无异常，售后单号：TQ219116064573849687。",
			// 			"ptTid": "2624983251086848796",
			// 			"sid": "123123123"
			// 		},
			// 		{
			// 			"errorInfo": "售后单状态不是无异常，售后单号：TQ219116064573849688。",
			// 			"ptTid": "2624983251086848797",
			// 		}
			// 	],
			// 	"failNum": 1,
			// 	"finish": false,
			// 	"progress": 0,
			// 	"success": true,
			// 	"successNum": 0,
			// 	"total": 1,
			// 	"userId": "1234568"
			// };

			// 显示结果
			const resultData = {
				total: refundData.length,
				successNum,
				failNum,
				stockErrorList,
				sidErrorList,
				results
			};

			setProcessResult(resultData);
			setShowProcessResult(true);

			// 如果设置了标记为已处理，刷新列表
			ref?.current?.refresh();

		} catch (error: any) {
			console.error('极速退货入库处理失败:', error);
			message.error(`处理失败: ${error?.message || '未知错误'}`);
		} finally {
			setIsProcessing(false);
		}
	};

	// 批量回收单号
	const batchCancelYdNo = () => {
		console.log(selectedRows);
		let allYdnoNum = 0;
		let hasMoreYdnoNum = 0;
		let cancelParams = [];
		let noYdNoList = [];
		selectedRows.forEach(it => {
			if (!it.waybillNo) {
				noYdNoList.push(it);
			} else {
				let ydNos = it.waybillNo?.split(',');
				allYdnoNum += ydNos?.length || 0;
				if (ydNos?.length > 1) {
					hasMoreYdnoNum++;
				}
				cancelParams.push(
					{
						taobaoId: it.sellerId,
						tids: it.tid || '',
						ydNO: it.waybillNo,
					}
				);
			}
		});
		if (!cancelParams.length) {
			message.warning('当前勾选订单无可回收单号');
			return;
		}
		let cancelYdNoParamsData = {
			allYdnoNum,
			hasMoreYdnoNum,
			cancelParams,
			noYdNoList,
			checkedList: selectedRows
		};
		setCancelYdNoData(cancelYdNoParamsData);
		setCancelYdNovisible(true);
	};
	const batchModifySysMemoHandle = () => {
		if (!selectedRows.length) {
			message.warning('请先勾选');
			return;
		}
		const packList = selectedRows.map((item) => ({
			trades: [{
				sysMemo: item.sysMemo,
				tid: item.tid,
				ptTid: item.ptTid,
			}],
			platform: item.platform.toLowerCase(),
			source: item.source,
			togetherId: item.tid,
			sellerId: item.sellerId,
			refundStatus: item.refundStatus || "",
		}));
		setModifySysMemoPackage(packList);
		setIsShowBatchSysMemoModal(true);
	};

	const batchModifyMemoHandle = () => {
		if (!selectedRows.length) {
			message.warning('请先勾选');
			return;
		}
		let breakFlag = false;
		selectedRows.forEach((item) => {
			if (item.isNoTradeMess) {
				breakFlag = true;
				message.info('无主件不可进行备注');
				return;
			}
			if (item.exceptionType === 1) {
				breakFlag = true;
				Modal.warning({
					title: '系统提示',
					content: '有订单已被锁定，不能进行该操作，请先处理异常',
				});
			}
		});
		if (breakFlag) return;
		const packList = selectedRows.map((item) => ({
			trades: [{
				sellerMemo: item.sellerMemo,
				sellerMemoFlag: item.sellerFlag,
				tid: item.tid,
				ptTid: item.ptTid,
			}],
			platform: item.platform.toLowerCase(),
			source: item.source,
			togetherId: item.tid,
			sellerId: item.sellerId,
			refundStatus: item.refundStatus || "",
		}));
		setIsShowBatchModifyMemoModal(true);
		setModifyMemoPackage(packList);
	};

	const expandContext = (
		<div className="r-flex r-jc-sb r-flex-1">
			<div className={ s["button-wrap"] }>
				<Button loading={ markOkLoading } size="middle" type="primary" data-point={ Pointer.订单异常预警_标记为已处理_点击次数 } className="r-mr-8" onClick={ handleMarkOk }>
					标记为已处理
				</Button>
				<Button size="middle" className="r-mr-8" onClick={ (e) => handleSearchTrade(e) }>
					前往订单页面查询
				</Button>
				<Button size="middle" className="r-mr-8" onClick={ () => handleCopyOk('ptTid', '复制订单号成功') }>
					复制订单编号
				</Button>
				<Button size="middle" className="r-mr-8" onClick={ () => handleCopyOk('waybillNo', '复制快递单号单号成功') }>
					复制快递单号
				</Button>
				{
					!isShowZeroStockVersion && (
						<Button size="middle" className="r-mr-8" onClick={ batchRefundInStock }>
							<div className="r-flex r-ai-c">
								<span className="r-mr-4">极速退货入库</span>
								<Tooltip title="适用场景：订单已操作发货，但在快递员揽收前产生退款，实际商品未发出；可通过该功能进行极速退货入库，系统会自动进行售后单审核-确认收货-退货入库操作，完成库存增加">
									<QuestionCircleOutlined className="r-c-999" />
								</Tooltip>
							</div>
						</Button>
					)
				}
				<Button size="middle" className="r-mr-8" onClick={ batchCancelYdNo }>
					批量回收单号
				</Button>
				<Dropdown.Button
					onClick={ debounce(() => batchModifyMemoHandle(), 500, {
						leading: true,
						trailing: false
					}) }
					size="middle"
					className="r-mr-8"
					overlay={ (
						<Menu
							className=""
							onClick={ ({ key }) => batchModifySysMemoHandle() }
						>
							<Menu.Item key="cancelClose">
								批量线下备注
							</Menu.Item>
						</Menu>
					) }
				>批量备注
				</Dropdown.Button>
				<Button icon={ <FileExcelOutlined /> } size="middle" className="r-mr-8" onClick={ () => { handleDown(); } }>导出</Button>
			</div>
			<div>
				<Button icon={ <SettingOutlined /> } size="middle" data-point={ Pointer.订单异常预警_预警设置_点击次数 } className="r-ml-10 r-mr-10" onClick={ () => setPreWarnSetModalVisible(true) }>
					预警设置
				</Button>
			</div>
		</div>

	);
	const titleNode = (
		<div style={ { background: '#fff', padding: '10px 10px 0' } }>
			<Alert type="info" message={ <span >说明：对订单打印发货中的多种异常情况进行预警，请及时处理各种订单异常情况。配合<span className="r-c-primary">物流预警、快递拦截</span>功能，可联系快递员协助处理揽收后的异常情况</span> } />
		</div>
	);

	const handleDown = debounce(async() => {
		sendPoint(Pointer.订单异常预警_点击下载);
		await downloadCenter({
			requestParams: GLOBAL_PARAMS,
			fileName: '异常预警',
			module: ModulesFunctionEnum.异常预警
		});
	}, 1000, { leading: true, trailing: false });

	useEffect(() => {
		const tidArr = dataSource
			.filter(item => expandedKeys.includes(item.rowId) && !warnDetail[item.tid])
			.map(i => i.tid);
		if (!tidArr.length) {
			return;
		}
		const handleLoading = (flag) => {
			let map = {};
			tidArr.map(tid => map[tid] = flag);
			setDetailLoading(map);
		};
		handleLoading(true);
		GetWarningDetailApi({ tid: [...new Set(tidArr)].join(',') }).then(res => {
			handleLoading(false);
			const detailMap = {};
			res.trades.forEach(trade => {
				detailMap[trade.tid] = trade;
			});
			setWarnDetail(pre => {
				return {
					...pre,
					...detailMap
				};
			});
		});
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [expandedKeys]);

	const expandedRowRender = (record, index) => {
		const trade = warnDetail[record.tid];
		if (detailLoading[record.tid]) {
			return <LoadingOutlined style={ { marginLeft: '60px' } } />;
		}
		if (!trade) {
			return '暂无订单';
		}
		const orderNodes = trade.orders.map((order, index) => {
			return (

				<div className={ s.detailWarp }>
					<div className="r-mr-10 r-bold">{index + 1}</div>
					<WaresInfo
						hoverImgWidth={ 300 }
						hoverImgHeight={ 300 }
						imgSize={ 60 }
						onlyImg
						isCombination={ order.isCombination }
						imgUrl={ order.picPath }
					/>
					{
						order.itemId == '-1' ? (
							<>
								<div>(无商品)</div>
								<div />
								<div>
									{getTradeStatusLabel(order.status as ITradeStatus)}
									{getOrderRefundStatus(order.refundStatus as ITradeRefundStatus)}
								</div>
							</>
						) : (
							<div className={ s.detailGoodsInfo }>
								<div>
									{order.isGift ? (
										<Tooltip title="该商品是赠品">
											<span className="r-trade-is-gift">赠</span>
										</Tooltip>
									) : ''}
									平台名称：{order.title}（商品编码：{(order.outerId && +order.outerId !== -1) ? order.outerId : order.itemId}）；
									{order.titleShort ? `简称：${order.titleShort}` : ''}
									{userStore?.isShowZeroStockVersion ? '' : `${order.sysOuterId ? `（货品编码：${order.sysOuterId}）` : ''}`}
									{order.isPreSale ? <span className="r-ml-12 r-trade-isPreSale">预售</span> : ''}
								</div>
								<div>
									规格名称：{order.skuProperties}{order.outerSkuId && +order.outerSkuId !== -1 ? `（规格编码：${order.outerSkuId})` : ''}；
									{order.skuAlias ? `别名：${order.skuAlias}` : ''}
									{userStore?.isShowZeroStockVersion ? '' : `${order.sysOuterSkuId ? `（货品规格编码：${order.sysOuterSkuId})` : ''}`}

								</div>
								<div>
									<span className="r-mr-10">数量：{order.number}</span>
									<span className="r-mr-10">重量：{
										userStore?.userSetting?.weightUnit === weightUnit.显示kg
											? Number(((+order.weight || 0) / 1000).toFixed(3)) + 'kg'
											: (order.weight || 0) + 'g'
									}
									</span>
									{
										userStore?.userInfo?.version === 1 && !order?.ignore && !userStore.isStockAllocationVersion
											? <span className="r-mr-10">库存：{order.sysStockCount || 0}</span>
											: null
									}
									<span className="r-ml-24">
										{getTradeStatusLabel(order.status as ITradeStatus)}
									</span>
									{order.refundStatus !== 'NOT_REFUND' ? getOrderRefundStatus(order.refundStatus as ITradeRefundStatus) : ''}
								</div>

							</div>
						)
					}
				</div>
			);
		});

		return (
			<div className={ s.tradeInfo }>
				<div className={ s.tradeNo }>
					订单编号<span className="r-c-primary r-ml-8 r-mr-16">{trade.ptTid}</span>系统单号<span className="r-c-primary r-ml-8">{trade.tid}</span>
				</div>
				{orderNodes}
			</div>
		);
	};

	const batchModifySysMemoModalOk = (e) => {
		const resultMap = {};
		e?.data?.tradeSysMemoVOList?.forEach((item) => {
			const { sysMemo } = item;
			resultMap[item.tid] = { sysMemo };
		});
		const list = dataSource.map(item => {
			if (resultMap[item.tid]) {
				return {
					...item,
					...resultMap[item.tid]
				};
			}
			return item;
		});
		setDataSource(list);
		setIsShowBatchSysMemoModal(false);
	};

	// 旗帜标签的回显
	const batchModifyMemoModalOk = (e) => {
		const resultMap = {};
		e?.forEach((item) => {
			const { memo: sellerMemo, sellerFlag, sellerFlagTag } = item;
			resultMap[item.tid] = {
				sellerMemo,
				sellerFlag,
				sellerFlagTag
			};
		});
		const list = dataSource.map(item => {
			if (resultMap[item.tid]) {
				return {
					...item,
					...resultMap[item.tid]
				};
			}
			return item;
		});
		setDataSource(list);
		setIsShowBatchModifyMemoModal(false);
	};

	return (
		<>
			{titleNode}
			<SearchTable
				ref={ ref }
				pageSizeId="exceptionWarnPage"
				form={ form }
				fetchData={ fetchSystemList }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				rowFormConfig={ {
					defaultParams,
					formList: FormFieldList,
					size: 'small',
					colProps: {
					}
				} }
				// additionalFormNode={
				// 	<Button className="r-ml-8" onClick={ handleDown }>下载</Button>
				// }
				baseTableConfig={ {
					noGap: true,
					innerTableStyle: { paddingTop: 0 },
					dataSource,
					expandContext,
					onFieldsChange,
					// innerTableStyle: { paddingLeft: 0, paddingRight: 0 },
					rowSelection,
					rowKey: 'rowId',
					columns,
					pagination: {
						pageSizeOptions: [10, 20, 50, 100, 200, 500, 1000],
					},
					cachePgination: true,
					hasVt: false,
					headerColSet: {
						resizeId: "exceptionWarn",
					},
					expandable: {
						// fixed: true,
						expandedRowKeys: expandedKeys,
						expandedRowRender,
						// columnWidth: 1,
						expandIcon: ({ expanded, onExpand, record }) => {
							const expandBtnNode = expanded ? (
								<span
									className="r-click"
									style={ { color: '#FD8202', marginLeft: '8px', userSelect: 'none' } }
									onClick={ e => {
										onExpand(record, e);
										setExpandedKeys(pre => pre.filter(i => i != record.rowId));
									} }
								>收起
								</span>
							) : (
								<span
									className="r-click"
									style={ { color: '#FD8202', marginLeft: '8px', userSelect: 'none' } }
									onClick={ e => {
										sendPoint(Pointer.订单异常预警_点击更多);
										onExpand(record, e);
										setExpandedKeys(pre => [...pre, record.rowId]);
									} }
								>展开
								</span>
							);
							return expandBtnNode;
						},
						// columnWidth: 100,
						// columnTitle: <>处理状态</>,
						// fixed: true,
						// expandIconColumnIndex: columns.length + 1,
						// expandIcon: ({ expanded, onExpand, record }) => {
						// 	const iconNode = expanded ? (
						// 		<Button type="link" onClick={ e => onExpand(record, e) }><Icon type="shouqi" size={ 14 } /></Button>
						// 	) : (
						// 		<Button type="link" onClick={ e => onExpand(record, e) }><Icon type="zhankai" size={ 14 } /></Button>
						// 	);
						// 	return (
						// 		<>
						// 			{iconNode}
						// 		</>
						// 	);
						// },
						// expandedRowClassName: () => s['expandRowUnfold'],
						// onExpandedRowsChange: (expandedKeys:string[]) => { setExpandedKeys(expandedKeys); },
					}
				} }
			/>
			{/* 订单备注 */}
			{isShowBatchModifyMemoModal ? (
				<BatchModifyMemoModal
					onOk={ (e) => { batchModifyMemoModalOk(e); } }
				/>
			) : ''}
			<BatchCancelYdNoModel
				visible={ cancelYdNovisible }
				setVisible={ setCancelYdNovisible }
				cancelYdNoData={ cancelYdNoData }
				showHandleCheck
			/>
			<BatchModifySysMemoModal onOk={ (e) => { batchModifySysMemoModalOk(e); } } />
			<PreWarnSetModal visible={ preWarnSetModalVisible } onClose={ () => setPreWarnSetModalVisible(false) } />
			<Modal
				title={ (
					<div className="r-flex r-ai-c">
						<ExclamationCircleOutlined style={ { color: 'red', fontSize: '20px', marginRight: '8px' } } />
						<span>系统提示</span>
					</div>
				) }
				visible={ isRefundWarningModalVisible }
				onCancel={ () => setIsRefundWarningModalVisible(false) }
				footer={ null }
				width={ 460 }
			>
				<div>
					<p>极速退货入库目前仅针对&quot;揽收前退款&quot;数据，请不要勾选其他异常预警原因数据</p>
					<div className="r-flex r-jc-sb r-ai-c" style={ { marginTop: '20px' } }>
						<span>过滤异常后勾选（<span className="r-c-error">{validRefundRows.length}</span>单）</span>
						<div>
							
							<Button onClick={ () => setIsRefundWarningModalVisible(false) }>知道了</Button>
							{validRefundRows.length > 0 && (
								<Button
									type="primary"
									className="r-ml-8"
									onClick={ () => {
										setIsRefundWarningModalVisible(false);
										setSelectedRows(validRefundRows);
										getFastRefundConfig();
										setIsRefundInStockModalVisible(true);
									} }
								>
									跳过异常 操作正常数据
								</Button>
							)}
						</div>
					</div>
				</div>
			</Modal>
			<Modal
				title="极速退货入库"
				visible={ isRefundInStockModalVisible }
				onOk={ handleRefundInStockOk }
				onCancel={ () => setIsRefundInStockModalVisible(false) }
				width={ 700 }
				footer={ (
					<div className="r-flex r-jc-sb r-ai-c">
						<div>
							<Checkbox
								checked={ refundStockFinishCancelYdNo }
								onChange={ e => {
									updateFastRefundConfig({
										refundStockFinishCancelYdNo: e.target.checked ? 1 : 0,
										refundStockFinishUpdateStatus: refundStockFinishUpdateStatus ? 1 : 0
									});
								} }
								className="r-mr-16"
							>
								退货入库成功后同步回收单号
							</Checkbox>
							<Checkbox
								checked={ refundStockFinishUpdateStatus }
								onChange={ e => {
									updateFastRefundConfig({
										refundStockFinishCancelYdNo: refundStockFinishCancelYdNo ? 1 : 0,
										refundStockFinishUpdateStatus: e.target.checked ? 1 : 0
									});
								} }
							>
								退货入库成功后标记预警记录为已处理
							</Checkbox>
						</div>
						<div>
							<Button onClick={ () => setIsRefundInStockModalVisible(false) }>取消</Button>
							<Button type="primary" onClick={ handleRefundInStockOk }>确定</Button>
						</div>
					</div>
				) }
			>
				<div>
					<h3>本次勾选: <span className="r-bold" style={ { color: "#f00" } }>{selectedRows.length}</span> 笔订单, 是否确定继续操作?</h3>
					<div className="r-mt-16" style={ { border: '1px solid #FFE58F', background: "#FFFBE6", padding: '8px 16px' } }>
						<p>注意事项:</p>
						<p>1、极速退货入库指针对符合条件的售后单数据进行&apos;审核-确认收货-退回入库&apos;操作；<span style={ { color: "#f00" } }>为避免产生资损, 操作前请务必确认实际商品未发出</span></p>
						<div>
							<div>2、符合条件的数据是指: </div>
							<div>
								<p>正向订单非指定供应商发货订单</p>
								<p>正向订单已绑定货品</p>
								<p>售后的售后类型为&apos;仅退款&apos;且平台退款状态不为&apos;退款关闭&apos;</p>
								<p>售后单未有确认收货记录（避免重复收货入库）</p>
							</div>
						</div>
					</div>
				</div>
			</Modal>
			<BatchModifySysMemoModal onOk={ (e) => { batchModifySysMemoModalOk(e); } } />
			<PreWarnSetModal visible={ preWarnSetModalVisible } onClose={ () => setPreWarnSetModalVisible(false) } />
			
			{/* 处理进度弹窗 */}
			<Modal
				title="极速退货进度"
				visible={ isProcessing }
				footer={ null }
				closable={ false }
				maskClosable={ false }
				width={ 500 }
			>
				<div style={ { textAlign: 'center', padding: '20px 0' } }>
					<Progress 
						type="circle" 
						percent={ progress } 
						format={ () => `${progress}%` }
						strokeColor="#FD8204"
						status="active"
					/>
					<div style={ { marginTop: '20px', fontSize: '16px', color: '#666' } }>
						<div>正在退货入库…</div>
						<div>（关闭页面会导致任务中断！）</div>
					</div>
				</div>
			</Modal>

			{/* 处理结果弹窗 */}
			<ResultModal 
				title="极速退货入库结果"
				visible={ showProcessResult }
				successNum={ processResult?.successNum }
				failNum={ processResult?.failNum }
				stockErrorList={ processResult?.stockErrorList }
				sidErrorList={ processResult?.sidErrorList }
				onCancel={ () => setShowProcessResult(false) }
				onOk={ () => setShowProcessResult(false) }
			/>
		</>
	);
};

export default observer(ExceptionWarn);
