import { TradeWarningInfo } from "@/types/trade/index";
import { decryptFn } from '@/pages/Trade/components/ListItem/components/Decrypt/index';
import { copyToPaste } from "@/utils";
// 发货状态
export enum SendStatusEnum {
	全部 = '',
	待发货 = 'WAIT_SELLER_SEND_GOODS',
	已发货 = 'WAIT_BUYER_CONFIRM_GOODS'
}

// 处理状态
export enum HandleStatusEnum {
	全部 = '',
	已处理 = '1',
	待处理 = '0'
}

//  订单预警状态
export enum WarnStatusEnum {
	// 全部 = '',
	打印后改地址 = 1,
	打印后改备注 = 2,
	打印后未发货退款 = 3,
	发货失败 = 5,
	申请单号未打印 = 6,
	单号打印多次 = 7,
	已打印未发货 = 8,
	揽收前退款 = 9,
	打印后已发货退款 = 10,
	发货后未揽收 = 100, // 前端定义的id
	快递拦截 = 101, // 前端定义的id
	即将超时揽收 = 11,
	超时未揽收 = 12
}


export interface INumList {
	totalNum?: number,
	appliedAndUnprintedNum?: number,
	repeatPrintNum?: number,
	printedAndUnshipNum?: number,
	sendWarningNum?: number,
	memoWarningNum?: number,
	refundWarningNum?: number,
	shipFailWarningNum?: number,
	preAcceptRefundNum?: number,
	printedAndShippedRefundNum?: number,
	timeoutUnacceptableWarningNum?: number,
	nearTimeoutAcceptWarningNum?: number,
}

export enum warnKeyMap {
	totalNum = WarnStatusEnum.全部,
	appliedAndUnprintedNum = WarnStatusEnum.申请单号未打印,
	repeatPrintNum = WarnStatusEnum.单号打印多次,
	printedAndUnshipNum = WarnStatusEnum.已打印未发货,
	sendWarningNum = WarnStatusEnum.打印后改地址,
	memoWarningNum = WarnStatusEnum.打印后改备注,
	refundWarningNum = WarnStatusEnum.打印后未发货退款,
	shipFailWarningNum = WarnStatusEnum.发货失败,
	preAcceptRefundNum = WarnStatusEnum.揽收前退款,
	printedAndShippedRefundNum = WarnStatusEnum.打印后已发货退款,
}

export enum WarningTipsShowEnum {
	弹出提醒弹窗_并且不自动关闭弹窗 = 1,
	弹出提醒弹窗_隔10秒自动关闭弹窗 = 2,
	不弹出提醒弹窗 = 3,
}


export enum WarningRedPointShowEnum {
	悬浮菜单窗显示红点 = 1,
	悬浮菜单窗不显示红点 = 2,
}

export interface IExceptionWarnSet {
	enableAppliedAndUnprintedWarning?: boolean,
	enableRepeatPrintWarning?: boolean,
	enablePrintedAndUnshipWarning?: boolean,
	enablePrintedAndShippedRefundWarning?: boolean, // 打印后已发货退款
	enableSendWarning?: boolean,
	enableMemoWarning?: boolean,
	enableRefundWarning?: boolean,
	enableShipFailWarning?: boolean,
	enablePreAcceptRefundWarning?: boolean,
	enableWarnList?: WarnStatusEnum[],
	warningTipsShow?: WarningTipsShowEnum,
	warningRedPointShow?: WarningRedPointShowEnum,
	enableNearTimeoutAcceptWarning?: boolean,
	enableTimeoutUnacceptableWarning?: boolean,
}

export const getWarnSetDefaultVal = (exceptionWarnSet: IExceptionWarnSet) => {
	let {
		enableAppliedAndUnprintedWarning = true,
		enableRepeatPrintWarning = true,
		enablePrintedAndUnshipWarning = true,
		enableSendWarning = true,
		enableMemoWarning = true,
		enableRefundWarning = true,
		enableShipFailWarning = true,
		enablePreAcceptRefundWarning = true,
		enablePrintedAndShippedRefundWarning = true, // 打印后已发货退款
		enableNearTimeoutAcceptWarning = true, // 即将超时揽收
        enableTimeoutUnacceptableWarning = true, // 超时未揽收
		warningTipsShow = WarningTipsShowEnum.弹出提醒弹窗_并且不自动关闭弹窗,
		warningRedPointShow = WarningRedPointShowEnum.悬浮菜单窗显示红点
	} = exceptionWarnSet ?? {};

	warningTipsShow = Number(warningTipsShow);
	warningRedPointShow = Number(warningRedPointShow);

	return {
		// enableSendWarning,
		// enableMemoWarning,
		// enableRefundWarning,
		// enableShipFailWarning,
		enableWarnList: [
			enableAppliedAndUnprintedWarning ? WarnStatusEnum.申请单号未打印 : null,
			enableRepeatPrintWarning ? WarnStatusEnum.单号打印多次 : null,
			enablePrintedAndUnshipWarning ? WarnStatusEnum.已打印未发货 : null,
			enableSendWarning ? WarnStatusEnum.打印后改地址 : null,
			enableMemoWarning ? WarnStatusEnum.打印后改备注 : null,
			enableRefundWarning ? WarnStatusEnum.打印后未发货退款 : null,
			enableShipFailWarning ? WarnStatusEnum.发货失败 : null,
			enablePreAcceptRefundWarning ? WarnStatusEnum.揽收前退款 : null,
			enablePrintedAndShippedRefundWarning ? WarnStatusEnum.打印后已发货退款 : null,
			enableNearTimeoutAcceptWarning ? WarnStatusEnum.即将超时揽收 : null,
            enableTimeoutUnacceptableWarning ? WarnStatusEnum.超时未揽收 : null,
		].filter(i => i),
		warningTipsShow,
		warningRedPointShow
	};
};


export enum tradeStatusTextEnum {
	'WAIT_BUYER_PAY' = '待付款',
	'SELLER_CONSIGNED_PART' = '部分发货',
	'WAIT_SELLER_SEND_GOODS' = '待发货',
	'WAIT_BUYER_CONFIRM_GOODS' = '已发货',
	'TRADE_FINISHED' = '交易成功',
	'TRADE_CLOSED' = '已关闭',
	'TRADE_CLOSED_BY_TAOBAO' = '已关闭',
}


export const handleWarnTradeDecrypt = async(pack: TradeWarningInfo) => {
	pack.platform = pack.platform.toLowerCase();
	const { successObj } = await decryptFn({
		type: '',
		togetherId: pack.tid,
		platform: pack.platform,
		mallId: pack.sellerId,
		encryptObj: {
			name: pack.receiverName,
			phone: pack.receiverMobile,
			address: pack.receiverAddress,
			buyerNick: pack.buyerNickOrigin || pack.buyerNick,
		},
		decryptArr: [{
			sellerId: pack.sellerId,
			platform: pack.platform,
			caid: pack.oaid,
			tid: pack.encodeTid || pack.tid,
			ptTid: pack?.trades?.map(s => s.ptTid)?.join(','),
			sceneCode: 100,
			source: pack.tradeEncodeType == 2 ? 'HAND' : '',
			encodeReceiverPhone: pack.receiverPhone,
			encodeMobile: pack.receiverMobile,
			encodeReceiverName: pack.receiverName,
			encodeReceiverAddress: pack.receiverAddress,
		}],
		sellerNick: pack.sellerNick,
		isDecrypted: pack.isDecrypted,
		sellerId: pack.sellerId,
		pack,
	});
	console.log('%c [ successObj ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', successObj)
	let decryptInfo = successObj[pack.tid];
	return decryptInfo;
};

export const getReceiverAddress = (pack) => {
	return pack.receiverAddressMask?.replace(pack.receiverProvince + pack.receiverCity + pack.receiverCounty, '');
};

export const pasteAddressFn = (pack) => copyToPaste(`${pack.receiverNameMask}，${pack.receiverMobileMask} ${pack.receiverProvince} ${pack.receiverCity} ${pack.receiverCounty} ${getReceiverAddress(pack)}`);

