import React, { useEffect, useRef, useState, useMemo } from "react";
import { Popover, Modal, Input, Select, message } from 'antd';
import cs from 'classnames';
import dayjs from "dayjs";
import { QuestionCircleOutlined, SettingOutlined } from "@ant-design/icons";
import { fontWeight } from "html2canvas/dist/types/css/property-descriptors/font-weight";
import s from "./index.module.scss";
import tt from '@/assets/image/afterSale/tt.png';
import pdd from '@/assets/image/afterSale/pdd.png';
import tm from '@/assets/image/afterSale/tm.png';
import tb from '@/assets/image/afterSale/tb.png';
import dy from '@/assets/image/afterSale/dy.png';
import ks from '@/assets/image/afterSale/ks.png';
import xhs from '@/assets/image/afterSale/xhs.png';
import c2m from '@/assets/image/afterSale/c2m.png';
import sph from '@/assets/image/afterSale/sph.png';
import jd from '@/assets/image/afterSale/jd.png';
import ali from '@/assets/image/afterSale/ali.png';
import sendPoint from "@/utils/pointTrack/sendPoint";
import history from "@/utils/history";
import { WarnStatusEnum } from '../../utils';
import { updateNearTimeoutAcceptHour, queryNearTimeoutAcceptHour } from "@/apis/report/logisticsWarning";
import Pointer from "@/utils/pointTrack/constants";

export enum PlatIconEnum {
	视频号,
	淘工厂,
	淘特,
	抖音,
	拼多多,
	淘宝,
	阿里,
	京东,
	快手,
	天猫,
	小红书
}

const platIcon = {
	[PlatIconEnum.淘特]: tt,
	[PlatIconEnum.拼多多]: pdd,
	[PlatIconEnum.天猫]: tm,
	[PlatIconEnum.淘宝]: tb,
	[PlatIconEnum.抖音]: dy,
	[PlatIconEnum.快手]: ks,
	[PlatIconEnum.小红书]: xhs,
	[PlatIconEnum.淘工厂]: c2m,
	[PlatIconEnum.视频号]: sph,
	[PlatIconEnum.京东]: jd,
	[PlatIconEnum.阿里]: ali,
};

const typeEnum = {
	[WarnStatusEnum.申请单号未打印]: "APPLIED_AND_UNPRINTED",
	[WarnStatusEnum.单号打印多次]: "REPEAT_PRINT",
	[WarnStatusEnum.已打印未发货]: "PRINTED_AND_UNSHIP",
};

interface IExceptionWarnTopTabsProps {
	tabList: any[];
	value?: any;
	warningConfig?: any,
	updateWaringConfig?: (k: object) => {};
	onChange?: (k: string) => {};
}

export function ExceptionWarnTopTabs(props: IExceptionWarnTopTabsProps) {
	const { tabList, value = '', warningConfig, updateWaringConfig, onChange } = props;
	const [currentType, setCurrentType] = useState(WarnStatusEnum.申请单号未打印);
	const [isShowSettingModal, setIsShowSettingModal] = useState(false);
	const [currentConfig, setCurrentConfig] = useState({} as any);
	const [nearTimeoutHours, setNearTimeoutHours] = useState(3); // 默认值为3小时

	const onTabClick = (i) => {
		i.point && sendPoint(i.point);
		if (i.key === WarnStatusEnum.发货后未揽收) {
			history.push('/report/logisticsWarning');
		} else if (i.key === WarnStatusEnum.快递拦截) {
			history.push('/aftersale/expressIntercept');
		} else {
			onChange?.(i.key);
		}
	};
	const showSettingModal = async(e, key) => {
		e.stopPropagation();
		if (key === WarnStatusEnum.即将超时揽收) {
			try {
				const res = await queryNearTimeoutAcceptHour();
				if (res) {
					setNearTimeoutHours(res || 3);
					setCurrentConfig({ ...currentConfig, exceedSeconds: (res || 3) * 3600 });
				}
			} catch (error) {
				console.error('获取即将超时揽收时间失败', error);
			}
		} else {
			const config = warningConfig.configInfoList.find(item => item.configName === typeEnum[key]);
			setCurrentConfig(config);
		}
		setCurrentType(key);
		setIsShowSettingModal(true);
	};

	const updateSetting = async() => {
		if (currentType === WarnStatusEnum.即将超时揽收) {
			try {
				const nearTimeoutAcceptHour = currentConfig.exceedSeconds / 3600;
				 // 添加打点
				 sendPoint(Pointer.订单异常预警_快速筛选_即将超时揽收弹窗确定);
				await updateNearTimeoutAcceptHour(nearTimeoutAcceptHour);
				message.success('设置成功');
			} catch (error) {
				console.error('更新即将超时揽收时间失败', error);
				message.error('设置失败');
				return;
			}
		} else {
			await updateWaringConfig({
				id: warningConfig.id,
				...currentConfig
			});
		}
		setIsShowSettingModal(false);
	};

	const typeSetting = useMemo(() => {
		const setting = {
			[WarnStatusEnum.申请单号未打印]: {
				title: "申请单号未打印监控设置",
				tips: "说明：超过设置时间的订单会标记为异常，进行提醒",
				subTitle: "监控策略",
				content: <div>自定义监控：每日零点进行一次预警，监控申请单号后超过<Input value={ currentConfig?.exceedSeconds / 3600 } style={ { width: "40px" } } onChange={ (e) => { setCurrentConfig({ ...currentConfig, exceedSeconds: e.target.value * 3600 }); } } />小时未打印订单</div>
			},
			[WarnStatusEnum.单号打印多次]: {
				title: "单号打印多次监控设置",
				tips: "说明：快递单号重复打印多次的订单会标记为异常，进行提醒",
				subTitle: "监控策略",
				content: <div>单号打印次数大于<Input value={ currentConfig?.exceedPrintNum } style={ { width: "40px" } } onChange={ (e) => { setCurrentConfig({ ...currentConfig, exceedPrintNum: e.target.value }); } } />次，标记未异常进行提醒</div>
			},
			[WarnStatusEnum.已打印未发货]: {
				title: "已打印未发货监控设置",
				tips: "说明：对打印后未发货的订单会标记为异常，进行提醒",
				subTitle: "监控策略",
				content: <div>自定义监控：每日零点进行一次预警，订单打印后超过<Input value={ currentConfig?.exceedSeconds / 3600 } style={ { width: "40px" } } onChange={ (e) => { setCurrentConfig({ ...currentConfig, exceedSeconds: e.target.value * 3600 }); } } />小时未发货</div>
			},
			[WarnStatusEnum.即将超时揽收]: {
				title: "即将超时揽收监控设置",
				tips: (
					<div className="r-flex">
						<div style={ { minWidth: "3em" } }>说明：</div>
						<div>
							<div>已发货但未在时限内完成揽收的订单会标记为异常，进行提醒：</div>
							<div className="r-fs-12 r-c-999">（淘宝/天猫、拼多多、京东一是需在承诺发货时间内完成发货及揽收，否则可能会造成延迟发货；二是要在发货后24小时内完成揽收，否则可能会造成虚假发货；其余平台暂时只监控需在发货后24小时内完成揽收）</div>
						</div>
					</div>
				),
				subTitle: "监控策略",
				content: (
					<div>
						<div>距承诺揽收时间不足 
							<Select 
								value={ currentConfig?.exceedSeconds ? currentConfig.exceedSeconds / 3600 : nearTimeoutHours }
								style={ { width: 60, margin: "0px 5px" } }
								onChange={ (value) => { setCurrentConfig({ ...currentConfig, exceedSeconds: value * 3600 }); } }
							>
								{Array.from({ length: 24 }, (_, i) => i + 1).map(hour => (
									<Select.Option key={ hour } value={ hour }>{hour}</Select.Option>
								))}
							</Select> 小时，进行订单预警
						</div>
						<div className="r-mt-8 r-c-666">
							注：淘宝/天猫遵循店铺后台配置，不受该时间影响<a className="r-ml-4 r-c-primary" target="_blank" href="https://myseller.taobao.com/home.htm/package-center/packageMonitor" rel="noreferrer">去调整</a>
						</div>
					</div>
				)
			},
		};
		return setting[currentType];
	}, [currentType, currentConfig]);

	return (
		<div style={ { marginBottom: 8, paddingBottom: 8, borderBottom: "1px solid #eee" } } className="r-flex r-fw-w">
			{
				tabList.map((i) => {
					const isActive = value == i.key;
					const content = i.plats || i.tip ? (
						<Popover
							content={
								(
									<div style={ { maxWidth: 320 } }>
										<div className="">{i.tip}</div>
										{
											i.plats && (
												<div className="r-flex r-jc-s r-mt-10">
													{
														i.plats?.map(src => (<img key={ src } style={ { width: 32, height: 32 } } src={ platIcon[src] } alt="#" className="r-mr-5" />))
													}
												</div>
											)
										}
									</div>
								)
							}
							trigger="hover"
						>
							<QuestionCircleOutlined style={ { color: '#999' } } className="r-mr-10" />
						</Popover>

					) : null;
					return (
						<>
							<div
								key={ i.key }
								onClick={ () => onTabClick(i) }
								className={ cs(s.exceptionWarnTopTabItem, isActive ? s.checkedTabItem : '') }
							>
								{ content }
								{ i.text }
								<span className="r-ml-5 r-c-error">{i.num}</span>
								{
									i.setting && <SettingOutlined className="r-ml-10 r-c-999" onClick={ (e) => { showSettingModal(e, i.key); } } />
								}
							</div>
							{
								[WarnStatusEnum.单号打印多次, WarnStatusEnum.发货失败, WarnStatusEnum.打印后未发货退款].includes(i.key) && (
									<div className={ s["tab-divide"] } />
								)
							}
						</>
					);
				})
			}
			<Modal width={ 530 } title={ typeSetting.title } visible={ isShowSettingModal } onOk={ updateSetting } onCancel={ () => { setIsShowSettingModal(false); } } >
				<div className={ s["setting-tips"] }>{ typeSetting.tips }</div>
				<div className={ s["setting-subTitle"] }>{ typeSetting.subTitle }</div>
				<div className={ s["setting-content"] }>{ typeSetting.content }</div>
			</Modal>
		</div>
	);
}
