
import React, { useEffect, useState } from 'react';
import { Checkbox, Form, Modal, Radio, Space, Tooltip } from 'antd';
import { observer } from 'mobx-react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { WarnStatusEnum, WarningRedPointShowEnum, WarningTipsShowEnum, getWarnSetDefaultVal } from '../../utils';
import { TradePrintSetUpdateWarningConfigSetApi } from '@/apis/trade';
import { tradeStore } from '@/stores';
import memoFn from '@/libs/memorizeFn';
import userStore from '@/stores/user';
import styles from './index.module.scss';


function PreWarnSetModal(props) {
	const { visible, onClose } = props;
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const [disabled, setDisabled] = useState(false);
	const { userInfo } = userStore;
	console.log('userInfouserInfo', userInfo);
	
	useEffect(() => {
		if (visible) {
			memoFn.getAdvancedSet().then(res => {
				const data = getWarnSetDefaultVal(res?.printSetExpandDTO);
				form.setFieldsValue(data);
				setDisabled(!data?.enableWarnList?.length);
			});
		}
	}, [form, visible]);


	const handleOk = async() => {
		setLoading(true);
		const { enableWarnList, ...v } = form.getFieldsValue();
		const params = await memoFn.getAdvancedSet();
		params.printSetExpandDTO = {
			...params.printSetExpandDTO,
			...v,
			enableSendWarning: enableWarnList.includes(WarnStatusEnum.打印后改地址), // 打印后改地址异常预警
			enableMemoWarning: enableWarnList.includes(WarnStatusEnum.打印后改备注), // 打印后改备注异常预警
			enableRefundWarning: enableWarnList.includes(WarnStatusEnum.打印后未发货退款), // 打印后退款异常预警
			enablePrintedAndShippedRefundWarning: enableWarnList.includes(WarnStatusEnum.打印后已发货退款), // 打印后退款异常预警
			enableShipFailWarning: enableWarnList.includes(WarnStatusEnum.发货失败), // 发货失败异常预警
			enableAppliedAndUnprintedWarning: enableWarnList.includes(WarnStatusEnum.申请单号未打印), // 申请单号未打印异常预警
			enableRepeatPrintWarning: enableWarnList.includes(WarnStatusEnum.单号打印多次), // 单号打印多次异常预警
			enablePrintedAndUnshipWarning: enableWarnList.includes(WarnStatusEnum.已打印未发货), // 已打印未发货异常预警
			enablePreAcceptRefundWarning: enableWarnList.includes(WarnStatusEnum.揽收前退款), // 揽收前退款异常预警
			enableNearTimeoutAcceptWarning: enableWarnList.includes(WarnStatusEnum.即将超时揽收), // 即将超时揽收异常预警
			enableTimeoutUnacceptableWarning: enableWarnList.includes(WarnStatusEnum.超时未揽收), // 超时未揽收异常预警
		};
		try {
			await TradePrintSetUpdateWarningConfigSetApi({
				id: params.id,
				printSetExpandDTO: params.printSetExpandDTO
			});
			await memoFn.updateAdvancedSet({ ...params });
			tradeStore.setExceptionWarnSet(v);
		} catch (error) {
			console.log('error: ', error);
		}
		onClose(false);
		setLoading(false);
	};

	const onValuesChange = (changedValues) => {
		console.log(changedValues);
		if ('enableWarnList' in changedValues) {
			setDisabled(!changedValues.enableWarnList?.length);
		}
	};

	return (
		<Modal
			centered
			closable
			width="580px"
			confirmLoading={ loading }
			title={ <strong>预警提醒设置</strong> }
			maskClosable={ false }
			visible={ visible }
			okText="保存"
			onOk={ handleOk }
			onCancel={ () => { onClose(false); } }
		>
			<Form
				form={ form }
				onValuesChange={ onValuesChange }
				name="preWarnSetForm"
				layout="vertical"
			>
				<Form.Item
					name="enableWarnList"
					label={ (
						<>
							<strong>异常类型设置</strong>
							<span className="r-fs-12">（子账号不可设置，如需调整请联系主账号调整提醒类型）</span>
						</>
					) }
				>
					<Checkbox.Group className={ styles["checkbox-group"] } disabled={ !!userInfo?.subUserId }>
						<div className={ styles["checkbox-group-label"] }>打印阶段:</div>
						<Checkbox value={ WarnStatusEnum.申请单号未打印 }>申请单号未打印</Checkbox>
						<Checkbox value={ WarnStatusEnum.单号打印多次 }>单号打印多次</Checkbox>
						<Checkbox value={ WarnStatusEnum.已打印未发货 }>已打印未发货</Checkbox>
						<Checkbox value={ WarnStatusEnum.打印后改地址 }>打印后改地址</Checkbox>
						<Checkbox value={ WarnStatusEnum.打印后改备注 }>打印后改备注</Checkbox>
						<Checkbox value={ WarnStatusEnum.打印后未发货退款 }>打印后未发货退款</Checkbox>
						<div className={ styles["checkbox-group-label2"] }>发货阶段:</div>
						<Checkbox value={ WarnStatusEnum.打印后已发货退款 }>打印后已发货退款</Checkbox>
						<Checkbox className="r-mt-6" value={ WarnStatusEnum.发货失败 }>发货失败</Checkbox>
						<Checkbox className="r-mt-6" value={ WarnStatusEnum.揽收前退款 }>揽收前退款</Checkbox>
						<Checkbox value={ WarnStatusEnum.即将超时揽收 }>即将超时揽收
							{/* <Tooltip title="暂不支持淘宝/天猫平台">
								<QuestionCircleOutlined style={ { marginLeft: '4px' } } />
							</Tooltip> */}
						</Checkbox>
						<Checkbox value={ WarnStatusEnum.超时未揽收 }>超时未揽收
							{/* <Tooltip title="暂不支持淘宝/天猫平台">
								<QuestionCircleOutlined style={ { marginLeft: '4px' } } />
							</Tooltip> */}
						</Checkbox>
					</Checkbox.Group>
				</Form.Item>
				<Form.Item name="warningTipsShow" label={ <strong>弹窗提醒设置</strong> } >
					<Radio.Group disabled={ disabled }>
						<Space direction="vertical">
							<Radio value={ WarningTipsShowEnum.弹出提醒弹窗_并且不自动关闭弹窗 }>有异常订单时，弹出提醒弹窗，并且不自动关闭弹窗</Radio>
							<Radio value={ WarningTipsShowEnum.弹出提醒弹窗_隔10秒自动关闭弹窗 }>有异常订单时，弹出提醒弹窗，隔10秒自动关闭弹窗</Radio>
							<Radio value={ WarningTipsShowEnum.不弹出提醒弹窗 }>有异常订单时，不弹出提醒弹窗</Radio>
						</Space>
					</Radio.Group>
				</Form.Item>
				<Form.Item name="warningRedPointShow" label={ <strong>红点提醒设置</strong> }>
					<Radio.Group disabled={ disabled }>
						<Space direction="vertical">
							<Radio value={ WarningRedPointShowEnum.悬浮菜单窗显示红点 }>悬浮菜单窗显示红点（即待处理数量）</Radio>
							<Radio value={ WarningRedPointShowEnum.悬浮菜单窗不显示红点 }>悬浮菜单窗不显示红点</Radio>
						</Space>
					</Radio.Group>
				</Form.Item>
			</Form>
		</Modal>
	);
}

export default observer(PreWarnSetModal);
