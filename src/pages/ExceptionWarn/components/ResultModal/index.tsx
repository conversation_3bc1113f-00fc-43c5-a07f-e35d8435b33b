import React, { useEffect, useState } from "react";
import { Button, Progress, Table, Tag } from "antd";
import Modal from "antd/lib/modal/Modal";
import { CheckCircleTwoTone, CloseCircleTwoTone, CopyOutlined } from "@ant-design/icons";
import { calculateTimeDiff, copyToPaste, splitFxgTid } from '@/utils';

type DataSourceItem = {
	ptTid:string,
	sid:string,
	errorInfo:string,
	[k:string]:any
}

type Props = {
	successNum:number,
	failNum:number,
	visible:boolean,
	stockErrorList:DataSourceItem[],
	sidErrorList:DataSourceItem[],
	onCancel?:()=>void,
	onOk?:()=>void,
	title?:string
}
const ResultModal = (props: Props) => {
	const { successNum, failNum, stockErrorList = [], sidErrorList = [], onOk, onCancel, title, visible } = props;

	const stockColumns = [
		{
			title: "序号",
			dataIndex: "index",
			width: 50,
			render(_, record, index) { return <span>{index + 1}</span>; }
		},
		{
			title: "订单编号",
			dataIndex: "ptTid",
			width: 180,
			render(_, record) {
				return (
					<div>
						<span>{record.ptTid}</span>
						<CopyOutlined
							onClick={ () => { copyToPaste(splitFxgTid(record['ptTid'])); } }
							className="r-fc-black-65 r-pointer r-ml-6"
						/>
					</div>

				);
				
			}
		}, {
			title: "失败原因",
			dataIndex: "errorInfo"
		}
	];

	const sidColumns = [
		{
			title: "序号",
			dataIndex: "index",
			width: 50,
			render(_, record, index) { return <span>{index + 1}</span>; }
		},
		{
			title: "快递单号",
			dataIndex: "sid",
			width: 180,
			render(_, record) {
				return (
					<div>
						<span>{record.sid}</span>
						<CopyOutlined
							onClick={ () => { copyToPaste(record['sid']); } }
							className="r-fc-black-65 r-pointer r-ml-6"
						/>
					</div>

				);
				
			}
		}, {
			title: "失败原因",
			dataIndex: "errorInfo"
		}
	];

	const copyAllTids = () => {
		const allTids = stockErrorList.map(i => i.ptTid);
		copyToPaste(splitFxgTid([...new Set(allTids)].join(',')));
	};

	const copyAllSids = () => {
		const allSids = sidErrorList.map(i => i.sid);
		copyToPaste(splitFxgTid([...new Set(allSids)].join(',')));
	};

	const _onOk = () => {
		onOk && onOk();

	};
	const _onCancel = () => {
		onCancel && onCancel();
	};

	const footer = (
		<div className="r-ta-c">
			<Button type="primary" onClick={ _onOk }>我知道了</Button>
		</div>
	);

	return (
		<Modal
			width={ 800 }
			title={ title }
			visible={ visible }
			centered
			onCancel={ _onCancel }
			onOk={ _onOk }
			footer={ footer }
		>
			<div>
				{/* <div>批量添加商品结果</div> */}
				<div className="r-flex r-ai-c r-jc-c r-fs-20">
					<div>
						<CheckCircleTwoTone twoToneColor="#52c41a" />
						<span className="r-ml-4 r-bold" style={ { color: "#52c41a" } }>成功：</span>
						<span className="r-bold" style={ { color: "#000" } }>{successNum}单</span>
					</div>
					<div className="r-ml-30">
						<CloseCircleTwoTone twoToneColor="#f00" />
						<span className="r-ml-4 r-bold" style={ { color: "#f00" } }>失败：</span>
						<span className="r-bold" style={ { color: "#000" } }>{failNum}单</span>
					</div>
			
				</div>

				<div hidden={ stockErrorList.length < 1 }>
					<div className="r-mb-8">退货入库失败列表：</div>
					<Table
						size="small"
						scroll={ { y: 300 } }
						pagination={ false }
						bordered
						dataSource={ stockErrorList }
						columns={ stockColumns }
					/>
					<div className="kdzs-link-text r-mt-8" onClick={ copyAllTids }>复制失败的订单编号</div>
				</div>

				<div hidden={ sidErrorList.length < 1 }>
					<div className="r-mb-8">回收单号失败列表：</div>
					<Table
						size="small"
						scroll={ { y: 300 } }
						pagination={ false }
						bordered
						dataSource={ sidErrorList }
						columns={ sidColumns }
					/>
					<div className="kdzs-link-text r-mt-8" onClick={ copyAllSids }>复制失败的快递单号</div>
				</div>
		
			</div>
		</Modal>
	);

};


export default ResultModal;
