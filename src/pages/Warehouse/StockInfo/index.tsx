/* 库存看板
 * @Author: sww
 * @Date: 2021-12-13 14:20:35
 * @Last Modified by: sww
 * @Last Modified time: 2022-01-19 15:42:54
 */
import React, { useEffect, useState, useRef, useMemo, useLayoutEffect } from 'react';
import cs from 'classnames';
import { Form, Button, Input, Tooltip, Modal, Popover, Select, InputNumber, Radio, Checkbox, Dropdown, Menu } from 'antd';
import { observer } from "mobx-react";
import _, { isEqual } from "lodash";
import dayjs from 'dayjs';
import { QuestionCircleOutlined, SettingOutlined, FileExcelOutlined, DownOutlined } from '@ant-design/icons';
import SearchTable from '@/components/SearchTable';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import EnumSelect from '@/components/Select/EnumSelect';
import { StockInfoProps, StockInfoListProps, StockInfoItemProps } from './interfance';
import { QueryType, StockType, WarningType, TableQueryList, TableSortList, SkuPicConfig, STOCK_INFO_QUERY_BIZ, STOCK_INFO_COLUMN_BIZ } from './constant';
import { StockGetPageListApi, StockStockPageCheckStockApi } from "@/apis/warehouse/stock";
import {
	StockGetPageListRequest
} from '@/types/schemas/warehouse/stock';
import { useStores } from '@/stores/tool';
import StockInfoModal from './components/StockInfoModal';
import StockTradeInfoModal from './components/StockTradeInfoModal';
import WaresInfo from "@/components-biz/WaresInfo";
import { BrandSelect } from "@/components-biz/Product/Brand";
import { clearParams } from '@/utils/stringHelper';
import WarpText from '@/components-biz/WarpText';
import s from './index.module.scss';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import Pointer from "@/utils/pointTrack/constants";
import message from "@/components/message";
import Icon from '@/components/Icon';
import sendPoint from '@/utils/pointTrack/sendPoint';
import userStore from '@/stores/user';
import { PRINT_BATCH_ABNORMAL_TRADE_SYSSKUIDLIST, stockVersion } from '@/constants';
import { local } from '@/libs/db';
import { getStrParam } from '@/utils';
import GroupDetailModal from './components/GroupDetailModal';
import { groupStockNumOutWay } from '@/pages/Index/Settings/System/constants';
import InputArrayMulti from '@/components/Input/InputArrayMulti';
import BatchImportModal from '../StockCheck/components/BatchImportModal';
import { downloadCenter } from '@/pages/Index/DownloadCenter/utils';
import { ModulesFunctionEnum } from '@/types/schemas/setting/download';
import useGetState from '@/utils/hooks/useGetState';
import { TradeDictInsertDictApi, TradeDictQueryDictApi, ItemItemUserConfigQueryConfigApi, ItemItemUserConfigUpdateConfigApi } from '@/apis/trade/search';
import SupplierSelect from '@/components-biz/SupplierSelect';
import FunctionPermissionCheck, { FunctionPermissionEnum } from '@/utils/permissionCheck/functionPermissionCheck';
import FieldsPermissionCheck, { FieldsPermissionCheckTypeEnum, FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import EditText from '../System/Archives/components/EditText';
import { ItemSysSkuEditSysSkuApi } from '@/apis/warehouse/system';
import Code from '@/assets/image/wxApp/code.png';
import SearchSetting from './components/SearchSetting';
import UpdateArchives from "../System/Archives/components/UpdateArchives";
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";

const MAX_SHOW_COUNT:number = 3;

const renderContent = (value:any, row:any) => {
	const obj:any = {
		children: <WarpText>{value || ''}</WarpText>,
		props: {},
	};
	if (row?.sku_isCollapse) {
		// obj.props.colSpan = 0;
	}
	return obj;
};
enum TabShowsEnum {
	系统货品 = '0',
	系统规格 = '2',
	组合装 = '1'
}
export enum 排序 {
	不排序 = 0,
	升序 = 1,
	降序 = 2
}

export enum 查询排序 {
	不排序 = "",
	升序 = "asc",
	降序 = "desc"
}

export const SearchType = {
	"0": "sysItem",
	"1": "group",
	"2": "sysSku",
};

export enum InventoryInfo {
    "实际总库存" = "salableItemStockTotal",
    "可配货库存" = "salableItemDistributableStockTotal",
    "订单占有库存" = "salableItemPreemptedNumTotal",
    "货品简称" = "sysItemAlias",
    "货品编码" = "outerId",
    "品牌" = "brandId",
	"分类" = "classifyId",
	"货号" = "itemNo",
	"供应商" = "supplierId",
	"货品规格编码" = "skuOuterId",
	"规格别名" = "sysSkuAlias",
	"规格名称" = "sysSkuName",
	"条形码" = "barCode",
	"售价" = "price",
	"成本价" = "costPrice",
	"创建时间" = "created",
};

const STOCKINFO_ACTIVEKEY = "LOCAL_STOCKINFO_ACTIVEKEY";

let timeoutId = null;
const TooltipCom = (chirld, title?) => {
	return (
		<Tooltip    
			trigger={["focus", "click"]}
			placement="top" 
			title={title ? title : '默认模糊搜索，如需精确搜索请在字段开头加上@@'}
		>
			{chirld}
		</Tooltip>
	);
};
const StockInfo: React.FC<StockInfoProps> = props => {
	const warehouseStore = useStores('WarehouseStore'); // mobx仓库
	const { getStockStorageInfo } = warehouseStore;
	const [curSearchParams, setCurSearchParams] = useState({});
	const [form] = Form.useForm();
	const [editRealStockForm] = Form.useForm();
	const ref = useRef<SearchTableRefProps>(); // 获取表单对象
	const [dataSource, setDataSource, gatDataSource] = useGetState<StockInfoItemProps[]>([]);
	const [infoModalShow, setInfoModalShow] = useState(false);
	const [stockTradeModalShow, setStockTradeModalShow] = useState<{[k: string]: any, visible: boolean}>({ visible: false });
	const [groupDetailModalShow, setGroupDetailModalShow] = useState<{[k: string]: any, visible: boolean}>({ visible: false });
	const [skuInfo, setSkuInfo] = useState({});
	const [loading, setLoading] = useState(false);
	const [batchToggleStatus, setBatchToggleStatus] = useState(false);
	const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
	const [exportLoading, setExportLoading] = useState<boolean>(false);
	const [oldInfo, setOldInfo, getOldInfo] = useGetState(null);
	const [saleSortObj, setSaleSortObj, getSaleSortObj] = useGetState({
		thirtyDaySales: 排序.不排序,
		fifteenDaySales: 排序.不排序,
		sevenDaySales: 排序.不排序,
		threeDaySales: 排序.不排序,
		yesterDaySales: 排序.不排序,
		yesterDayActualNumber: 排序.不排序,
		threeDayActualNumber: 排序.不排序,
		sevenDayActualNumber: 排序.不排序,
		fifteenDayActualNumber: 排序.不排序,
		thirtyDayActualNumber: 排序.不排序,
		availableSalesDay: 排序.不排序,
	});
	const [sortQueryObj, setSortQueryObj, getSortQueryObj] = useGetState({
		sortValue: "",
		sortType: "",
	});
	const [columnList, setColumnList] = useState([]);
	const [queryList, setQueryList] = useState([]);
	const [activeKey, setActiveKey] = useState(String(local.get(STOCKINFO_ACTIVEKEY) ?? 0));
	const [skuPicConfig, setSkuPicConfig] = useState({});
	const groupStore = useStores('GroupStore');
	const { groupList } = groupStore;
	const [saleDaysType, setSaleDaysType] = useState("thirtyDayActualNumber");


	useEffect(() => {
		userStore.getUserInfo().then(userInfo => {
			if (userInfo.version === stockVersion.库存版) {
				userStore.getSystemSetting();
			}
		});
		getSkuPicConfig();
		ItemItemUserConfigQueryConfigApi({
			itemUserConfigBizEnum: "stock_page_list_set"
		}).then(res => {
			res?.forEach(item => {
				if (item.biz === "stock_page_list_set") {
					try {
						const result = JSON.parse(item.value);
						if (result) {
							setSaleDaysType(result?.availableSalesDayCalculateRule)
						}
					} catch (err) {
						console.log(err)
					}
				}
			})
		})
	}, []);

	useEffect(() => {
		const isCombination = getStrParam('isCombination', props?.location?.search);
		if (isCombination) {
			setActiveKey(isCombination);
		}
	}, [props?.location?.search]);

	// 组合货品开启，出库以子货品为准
	const isVirStock = useMemo(() => {
		return userStore?.systemSetting?.groupStockNumOutWay == groupStockNumOutWay.以子货品为准 && activeKey == TabShowsEnum.组合装;
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [activeKey, userStore?.systemSetting?.groupStockNumOutWay]);

	useEffect(() => {
		let combinedType = 'custom';
		if (activeKey === TabShowsEnum.组合装) combinedType = 'combined';
		groupStore.getGroupList(true, combinedType);
	}, [activeKey, groupStore]);

	// 展开更多货品
	const toggleList = (row: StockInfoItemProps) => {
		setDataSource(pre => {
			pre.forEach(i => {
				if (i.groupId === row.groupId) i.collapseShow = !i.collapseShow;
			});
			return [...pre];
		});
	};

	const batchToggleList = () => {
		sendPoint(Pointer.库存_库存看板_批量展开收起);
		setDataSource(pre => {
			pre.forEach(i => {
				i.collapseShow = !batchToggleStatus;
			});
			return [...pre];
		});
		setBatchToggleStatus(pre => !pre);
	};

	const openStockTradeInfoModal = (row) => {
		sendPoint(Pointer.库存_库存看板_订单占用库存_点击);
		setStockTradeModalShow({ visible: true, sysSkuId: row.sku_id, sysItemId: row.sys_sysItemId, skuOuterId: row.sku_skuOuterId });
	};
	// 手动修改实际总库存
	const editRealStockModal = (item) => {
		const onOk = async() => {
			const { id = undefined } = await getStockStorageInfo();
			await editRealStockForm.validateFields();
			const { sku_id, sys_sysItemId } = item;
			const { stock } = editRealStockForm.getFieldsValue();
			if (stock < 0) {
				message.warning("修改后库存不能小于0");
				return Promise.reject();
			}
			const params = {
				checkCount: stock,
				sysItemSkuId: sku_id,
				sysItemId: sys_sysItemId,
				storageId: Number(id)
			};
			const ret = await StockStockPageCheckStockApi(params);
			if (!ret) {
				message.error('修改失败');
				editRealStockForm.resetFields();
				return Promise.resolve();
			}
			message.success('修改成功');
			editRealStockForm.resetFields();
			item.sku_salableItemStock = stock;
			const _dataSource = changeItemData({ itemObj: item, data: gatDataSource() });
			setDataSource(_dataSource);
			return Promise.resolve();
		};
		const onCancel = () => {
			editRealStockForm.resetFields();
		};
		Modal.confirm({
			centered: true,
			title: `修改实际总库存`,
			content: (
				<div>
					<div className="r-c-error r-m-tb-12">提交后，勾选的规格的<span style={ { textDecoration: "underline" } }>实际总库存</span>会对应进行盘盈/盘亏库存</div>
					<Form
						form={ editRealStockForm }
						name="editRealStockForm"
					>
						<Form.Item
							name="stock"
							label="修改后库存"
							rules={ [{ required: true, message: '请输入修改后库存', type: "number" }] }
						>
							<InputNumber />
						</Form.Item>
					</Form>
				</div>
			),
			onOk,
			onCancel,
		});
	};

	const changeItemData = ({ itemObj, data }) => {
		const _data = _.cloneDeep(data);
		let _sku_summaryStockNum = 0;
		let _sum = 0;
		_data.forEach(item => {
			if (item.sys_sysItemId === itemObj.sys_sysItemId && item.sku_id === itemObj.sku_id) {
				item.sku_salableItemStock = itemObj.sku_salableItemStock;
			}
			if (item.sys_sysItemId === itemObj.sys_sysItemId && !item.sku_isSummary) {
				_sku_summaryStockNum += Number(item.sku_salableItemStock || 0);
			}
			if (item.sys_sysItemId === itemObj.sys_sysItemId && item.sku_isSummary) {
				item.sku_summaryStockNum = _sku_summaryStockNum;
			}
			if (!item.sku_isSummary && !item.sku_sum) {
				_sum += Number(item.sku_salableItemStock || 0);
			}
			if (item.sku_sum) {
				item.sku_summaryStockNum = _sum;
			}
		});
		return _data;
	};
	const shouldCellUpdate = (recode: any, prevRecord: any) => {
		return !isEqual(recode, prevRecord);
	};

	const handleMouseEnter = () => {
		// 设置定时器
		handleMouseLeave();
		timeoutId = setTimeout(() => {
			sendPoint(Pointer.库存_库存看板_手机看库存展示);
		}, 500); // 500 毫秒后执行打点
	};

	const handleMouseLeave = () => {
		// 清除定时器
		if (timeoutId) {
			clearTimeout(timeoutId);
			timeoutId = null;
		}
	};

	// 表单元素定义 下拉框？日期？ 多选框？ 多选按钮？
	const FormFieldListV1: FormItemConfig[] = [
		{
			label: "",
			shouldUpdate: false,
			colProps: {
				span: 24
			},
			children: (
				<div className="r-pb-16 r-mb-8 r-relative" style={ { borderBottom: "1px solid #eee" } }>
					<Radio.Group value={ activeKey } onChange={ (e) => changeActiveKey(e.target.value) }>
						<Radio.Button value="0">系统货品</Radio.Button>
						<Radio.Button value="2">系统规格</Radio.Button>
						<Radio.Button value="1">组合装</Radio.Button>
					</Radio.Group>
					<div
						className={ s.wxAppContent }
						onMouseEnter={ handleMouseEnter }
						onMouseLeave={ handleMouseLeave }
					>
						<div className={ s.wxAppMain }>
							<Icon style={ { fontSize: 18, color: '#07C160' } } type="weixinxiaochengxu" />
							手机看库存
						</div>
						<div className={ s.wxAppCode }>
							<div className={ s.codeMain }>
								<div className={ s.codeTitle }>快递助手ERP小程序</div>
								<img src={ Code } alt="" className={ s.code } />
								<div className={ s.codeBottom }>随时随地，掌握库存数量</div>
								<div className={ s.codeTip }>（子账号需要开通权限）</div>
							</div>
						</div>
					</div>
				</div>
			),
		},
		{
			name: 'sysItemAlias',
			label: "",
			children:  TooltipCom(
				<Form.Item name={'sysItemAlias'} className={ cs('r-mb-0', 'r-mt-0') }>
					<Input placeholder="货品简称" style={ { width: 160 } } size="small" />
				</Form.Item>
			)
		},
		{
			name: 'sysSkuInfo',
			label: '',
			children: 
			TooltipCom(
				<Form.Item name={'sysSkuInfo'} className={ cs('r-mb-0', 'r-mt-0') }>
					<Input placeholder="规格/规格别名" style={ { width: 160 } } size="small" />
				</Form.Item>
				
			)
		},
		{
			name: 'skuOuterIdList',
			label: '',
			children: 
				<InputArrayMulti
				size="small"
				placeholder="货品规格编码"
				maxInputNum={ 50 }
				maxTagCount={ 1 }
				maxTagTextLength={ 6 }
				open={ false }
			/>
			
		},
		{
			name: 'brandIds',
			label: '',
			children: <BrandSelect
				showArrow
				filterOption={ (input, option) => option.children["toLowerCase"]().indexOf(
					input.toLowerCase()
				) >= 0 }
				mode="multiple"
				maxTagCount={ 3 }
				maxTagTextLength={ 3 }
				allowClear
				size="small"
				style={ { width: 160 } }
			/>
		},
		{
			name: 'classifyIdList',
			label: '',
			children: (
				<Select
					mode="multiple"
					showSearch
					showArrow
					allowClear
					placeholder="分类"
					optionFilterProp="children"
					style={ { minWidth: 160, maxWidth: 300 } }
					size="small"
					filterOption={ (input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase()) }
					options={
						groupList.map(item => ({ value: item.classifyId, label: item.classifyName }))
					}
				/>
			)
		},
		{
			name: 'barCode',
			label: '',
			children:TooltipCom(
				<Form.Item name={'barCode'} className={ cs('r-mb-0', 'r-mt-0') }>
						<Input placeholder="条形码" style={ { width: 160 } } size="small" />
				</Form.Item>
			
			) 
		},
		{
			name: 'itemNo',
			label: '',
			children: TooltipCom(
				<Form.Item name={'itemNo'} className={ cs('r-mb-0', 'r-mt-0') }>
					<Input placeholder="货号" style={ { width: 160 } } size="small" />
				</Form.Item>
				
			) 
		},
		{
			name: 'stockStatus',
			label: '',
			children: (
				<Select allowClear placeholder="库存状态" style={ { width: 160 } } size="small">
					{StockType.map((item) => (
						<Select.Option key={ item.value } value={ item.key }>
							{item.value}
						</Select.Option>
					))}
				</Select>
			)
		},
		{
			name: 'warnStockStatus',
			label: '',
			children: (
				<Select allowClear placeholder="警戒状态" style={ { width: 160 } } size="small">
					{WarningType.map((item) => (
						<Select.Option key={ item.value } value={ item.key }>
							{item.value}
						</Select.Option>
					))}
				</Select>
			)
		},
		{
			name: 'supplierName',
			children: (
				<SupplierSelect
					allowClear
					style={ { width: 160 } }
					size="small"
					callBackProp="supplierName"
				/>
			),
		},
		{
			name: 'sysSkuMemo',
			label: '',
			children: <Input placeholder="备注" style={ { width: 160 } } size="small" />
		},
		{
			name: 'salableItemStock',
			label: '',
			children: (
				<>
					<Form.Item noStyle name="salableItemStockStart">
						<Input placeholder="实际总库存" style={ { width: 160 } } size="small" allowClear />
					</Form.Item>
					<span style={ { marginLeft: "1px", marginRight: "2px" } }>-</span>
					<Form.Item noStyle name="salableItemStockEnd">
						<Input placeholder="实际总库存" style={ { width: 160 } } size="small" allowClear />
					</Form.Item>
				</>
			)
		},
		{
			name: 'salableItemDistributableStock',
			label: '',
			children: (
				<>
					<Form.Item noStyle name="salableItemDistributableStockStart">
						<Input placeholder="可配货库存" style={ { width: 160 } } size="small" allowClear />
					</Form.Item>
					<span style={ { marginLeft: "1px", marginRight: "2px" } }>-</span>
					<Form.Item noStyle name="salableItemDistributableStockEnd">
						<Input placeholder="可配货库存" style={ { width: 160 } } size="small" allowClear />
					</Form.Item>
				</>
			)
		},
		{
			name: "created",
			label: "",
			className: "r-mb-8",
			children: (
				<KdzsDateRangePicker1
					style={{ width: "159px" }}
					format="YYYY-MM-DD HH:mm"
					placeholder={["创建时间起", "创建时间止"]}
					className={cs("r-w-full")}
				/>
			),
			colProps: {
				// span: 5
			},
		},
	];

	const FormField = useMemo(() => {
		const fieldObj = {};
		FormFieldListV1.forEach(item => {
			fieldObj[item.name] = item;
		});
		let _list = [FormFieldListV1[0]];
		const checkedQuery = queryList?.filter(i => i.ischecked);
		checkedQuery.forEach(item => {
			if (fieldObj[item.key]) {
				_list.push(fieldObj[item.key]);
			}
		});
		return _list;
	}, [activeKey, groupList, queryList]);

	const openGroupDetailModal = (row) => {
		setGroupDetailModalShow({ visible: true, info: row });
	};

	const onSkuPicConfigChange = (value) => {
		TradeDictInsertDictApi({
			userDictEnum: "STOCK_PAGE_COLUMN_PIC_CONFIG",
			value: JSON.stringify({ value })
		}).then(() => {
			setSkuPicConfig({value});
		});
	}


	const handleSaleDaysTypeChange = async(e) => {
		setSaleDaysType(e.target.value);
		ItemItemUserConfigUpdateConfigApi({
			itemUserConfigBizEnum: "stock_page_list_set",
			value: JSON.stringify({availableSalesDayCalculateRule: e.target.value})
		}).then(res => {
			ref?.current?.refresh();
		})
	};

	const saleDaysSettingContent = (
		<div style={{ width: "400px" }}>
			<div className='r-fs-16 r-mb-16' style={{ fontSize: "16px" }}>可售天数计算规则</div>
			<div className="r-mb-16">
				<div className='r-fs-16 r-ta-c' style={{ color: "#FD8204", background: "#F5F5F5", padding: "10px" }}>公式：可售天数=实际总库存/日均单量</div>
			</div>
			<div>
				<div>日均单量定义（可设置）：</div>
				<div className="r-mt-8 r-mt-8">
					<Radio.Group value={saleDaysType} onChange={handleSaleDaysTypeChange}>
						<div className="r-flex r-pl-24" style={{ flexWrap: "wrap" }}>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="thirtyDayActualNumber">30日实发量/30日</Radio></div>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="thirtyDaySales">30日销量/30日</Radio></div>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="fifteenDayActualNumber">15日实发量/15日</Radio></div>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="fifteenDaySales">15日销量/15日</Radio></div>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="sevenDayActualNumber">7日实发量/7日</Radio></div>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="sevenDaySales">7日销量/7日</Radio></div>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="threeDayActualNumber">3日实发量/3日</Radio></div>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="threeDaySales">3日销量/3日</Radio></div>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="yesterDayActualNumber">昨日实发量/1日</Radio></div>
							<div style={{ width: "50%", paddingBottom: "4px" }}><Radio value="yesterDaySales">昨日销量/1日</Radio></div>
						</div>
					</Radio.Group>
				</div>
			</div>
		</div>
	);

	// 表格列定义
	const columnstmp: StockInfoItemProps[] = [
		{
			width: 60,
			dataIndex: 'index',
			noDrag: true,
			key: 'index',
			align: 'center',
			sortSet: { name: "序号" },
			render: (value, row, index) => {
				if (row?.sku_isCollapse) {
					return (
						<>
							<div style={ { height: '20px' } } />
							<span
								onClick={ () => toggleList(row) }
								className={ s.collapseBtn }
								style={ { position: 'absolute', wordBreak: 'keep-all', left: window.innerWidth / 2.5, top: '9px', lineHeight: '20px', zIndex: 9 } }
								data-point={ row?.collapseShow ? Pointer['库存_库存看板_收起货品规格'] : Pointer['库存_库存看板_展开更多货品规格'] }
							>{row?.collapseShow ? '收起' : '展开更多'}货品规格
							</span>
						</>
					);
				} else if (row?.sku_sum) {
					return null;
				} else {
					return {
						children: row.rowSpan && <span>{row.index}</span>,
						props: {
						// rowSpan: row.rowSpan || 0,
						},
					};
				}

			},
			onCell: (row:any, index:number) => {
				return {
					style: row.rowSpan ? { overflow: 'unset' } : { borderTop: 0, overflow: 'unset' }
				};
			}
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					简称
					{
						activeKey !== TabShowsEnum.组合装 &&<Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.货品简称 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.货品简称); } }
					/>
					}
					{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSortQuery(InventoryInfo.货品简称); } } /> */}
				</div>
			),
			width: 200,
			key: 'sysItemAlias',
			dataIndex: 'sysItemAlias',
			className: cs('table-right-border'),
			render: (value, row, index) => {
				return {
					children: row.rowSpan && (
						<div className="r-flex r-fd-c">
							<WarpText><Tooltip placement="right" title="货品简称">{row.sys_sysItemAlias}</Tooltip></WarpText>
							{/* <WarpText className="r-fc-black-65"><Tooltip placement="right" title="货品编码">{row.sys_outerId}</Tooltip></WarpText> */}
						</div>
					),
					props: {
						// rowSpan: row.rowSpan || 0,
					},
				};
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.rowSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: () => {
				const settingArr = [{
					label: '小图',
					value: '0',
				}, {
					label: '中图',
					value: '1',
				}, {
					label: '大图',
					value: '2',
				}];
				const content = settingArr.map(item => {
					return (
						<div className="r-pd-4">
							<Checkbox checked={ skuPicConfig?.value === item.value } onChange={ (e) => onSkuPicConfigChange(item.value) } >{item.label}</Checkbox>
						</div>
					);
				});
				const title = (
					<div className='r-flex r-ai-c'>
						规格图
						<Popover placement="bottom" title={ null } content={ content } trigger="click">
							<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
						</Popover>
					</div>
				);
				return title;
			},
			width: 100, // 40
			dataIndex: 'picUrl',
			key: 'picUrl',
			render: (value, row, index) => {
				const obj = {
					children: value,
					props: {},
				};
				let renderComp:React.ReactNode = <></>;
				let props:any = {};
				if (row?.sku_sum || row?.sku_isCollapse) {
					return null;
				} else if (!row?.sku_isSummary && !row?.sku_isCollapse) {
					const sizeObj = {
						"0": 32,
						"1": 48,
						"2": 64,
					}
					renderComp = (
						<div className="r-flex r-ai-c">
							<WaresInfo isCombination={ row?.sku_isCombination } imgUrl={ row.sku_picUrl } imgSize={ sizeObj[skuPicConfig?.value] || 48 } />
						</div>
					);
				} else {
					renderComp = <span className="r-fw-500">合计：</span>;
					// props.colSpan = 4;
				}
				return {
					children: renderComp,
					props,
				};
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					规格名称
					{activeKey === TabShowsEnum.系统规格 && <Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.规格名称 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.规格名称); } }
					/>}
				</div>
			),
			width: 200,
			dataIndex: 'sku_sysSkuName',
			key: 'sku_sysSkuName',
			render: renderContent,
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					货品规格编码
					{activeKey === TabShowsEnum.系统规格 && <Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.货品规格编码 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.货品规格编码); } }
					/>}
				</div>
			),
			width: 180,
			dataIndex: 'sku_skuOuterId',
			key: 'sku_skuOuterId',
			render: renderContent,
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					规格别名
					{activeKey === TabShowsEnum.系统规格 && <Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.规格别名 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.规格别名); } }
					/>}
				</div>
			),
			width: 140,
			dataIndex: 'sku_sysSkuAlias',
			key: 'sku_sysSkuAlias',
			render: renderContent,
		},
		{
			title: (
				<div className="r-flex">
					<Tooltip title="由子货品仓内的实际总库存按组合比例计算">
						<div>虚拟总库存</div>
					</Tooltip>
				</div>
			),
			sortSet: { name: "虚拟总库存" },
			width: 100,
			hide: TabShowsEnum.系统规格,
			dataIndex: 'sku_fictitiousStock',
			key: 'sku_fictitiousStock',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_fictitiousStock}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: isVirStock
							? <span onClick={ () => openGroupDetailModal(row) } className="g-c-blue r-pointer">{value}</span>
							: <span>{value}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="实际仓库内的库存，由可配货库存 + 订单占用库存组成">
						<div className={s["tips-hover"]}>实际总库存</div>
					</Tooltip>
					{/* <Popover content="支持当前页排序，按30日销量字段">
						<SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("salableItemStock"); } } />
					</Popover> */}
					<Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.实际总库存 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.实际总库存); } }
					/>
					{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSortQuery(InventoryInfo.实际总库存); } } /> */}

				</div>
			),
			sortSet: { name: "实际总库存" },
			width: 120,
			dataIndex: 'sku_salableItemStock',
			key: 'sku_salableItemStock',
			align: "center",
			hide: TabShowsEnum.组合装,
			render: (value:any, row:any) => {
				if (row?.sku_isSummary || row?.sku_sum) {
					return {
						children: <span className="r-fw-500">{row.sku_summaryStockNum}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: activeKey !== TabShowsEnum.组合装
							? (
								<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.修改实际总库存 }>
									<span onClick={ () => editRealStockModal(row) } className="g-c-blue r-pointer">{value}</span>
								</FunctionPermissionCheck>
							)
							: <span>{value}</span>,

						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="仓内目前所剩库存或所需库存">
						<div className={s["tips-hover"]}>可配货库存</div>
					</Tooltip>
					<Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.可配货库存 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.可配货库存); } }
					/>
					{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSortQuery(InventoryInfo.可配货库存); } } /> */}
				</div>
			),
			sortSet: { name: "可配货库存" },
			width: 120,
			align: "center",
			dataIndex: 'sku_salableItemDistributableStock',
			key: 'sku_salableItemDistributableStock',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary || row?.sku_sum) {
					return {
						children: <span className="r-fw-500">{row.sku_salableItemDistributableStock}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: isVirStock
							? <span onClick={ () => openGroupDetailModal(row) } className="g-c-blue r-pointer">{value}</span>
							: <span>{value}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="由子货品仓内的可配货库存按组合比例计算">
						<div className={s["tips-hover"]}>虚拟可配货库存</div>
					</Tooltip>
				</div>
			),
			sortSet: { name: "(虚拟)可配货库存" },
			width: 120,
			align: "center",
			dataIndex: 'sku_fictitiousDistributableStock',
			key: 'sku_fictitiousDistributableStock',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary || row?.sku_sum) {
					return {
						children: <span className="r-fw-500">{row.sku_fictitiousDistributableStock}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span onClick={ () => openGroupDetailModal(row) } className="g-c-blue r-pointer">{row.sku_fictitiousDistributableStock}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="订单所需的库存数量">
						<div className={s["tips-hover"]}>订单占用库存</div>
					</Tooltip>
					<Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.订单占有库存 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.订单占有库存); } }
					/>
					{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSortQuery(InventoryInfo.订单占有库存); } } /> */}
				</div>
			),
			width: 120,
			align: "center",
			dataIndex: 'sku_salableItemPreemptedNum',
			key: 'sku_salableItemPreemptedNum',
			hide: TabShowsEnum.组合装,
			sortSet: { name: "订单占用库存" },
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary || row?.sku_sum) {
					return {
						children: <span className="r-fw-500">{row.sku_salableItemPreemptedNum}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span onClick={ () => openStockTradeInfoModal(row) } className="g-c-blue r-pointer">{value}</span>,
						// children: <span>{value}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="即采购单中未到货的货品数量">
						<div className={s["tips-hover"]}>采购在途</div>
					</Tooltip>
				</div>
			),
			sortSet: { name: "采购在途" },
			width: 100,
			align: "center",
			dataIndex: 'sku_transitItemStock',
			key: 'sku_transitItemStock',
			hide: TabShowsEnum.组合装,
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary || row?.sku_sum) {
					return {
						children: <span className="r-fw-500">{row.sku_transitItemStock}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="售后→售后管理→高级设置中支持销退在途的自定义设置（可设置“物流签收前算为销退在途”或“确认收货前算为销退在途”或“入库前算为销退在途”；如未设置，默认入库前算为销退在途）">
						<div className={s["tips-hover"]}>销退在途</div>
					</Tooltip>
				</div>
			),
			sortSet: { name: "销退在途" },
			width: 100,
			align: "center",
			dataIndex: 'sku_refundStockWaitHandNum',
			key: 'sku_refundStockWaitHandNum',
			hide: TabShowsEnum.组合装,
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary || row?.sku_sum) {
					return {
						children: <span className="r-fw-500">{row.sku_refundStockWaitHandNum}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="昨日销量该货品规格已付款维度的销售数量。数据来源于销售利润报表，当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>昨日销量</div>
					</Tooltip>
					<Popover content="支持当前页排序，按昨日销量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.yesterDaySales === 排序.不排序 ? "morenpaixu" : saleSortObj.yesterDaySales === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("yesterDaySales"); } }
						/>
					</Popover>
				</div>
			),
			sortSet: { name: "昨日销量" },
			hide: TabShowsEnum.组合装,
			width: 100,
			align: "center",
			dataIndex: 'sku_yesterDaySales',
			key: 'sku_yesterDaySales',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_yesterDaySales}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="近3天该货品规格已付款维度的销售数量（不含今日）。数据来源于销售利润报表，当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>3日销量</div>
					</Tooltip>
					<Popover content="支持当前页排序，按3日销量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.threeDaySales === 排序.不排序 ? "morenpaixu" : saleSortObj.threeDaySales === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("threeDaySales"); } }
						/>
						{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("threeDaySales"); } } /> */}
					</Popover>
				</div>
			),
			sortSet: { name: "3日销量" },
			hide: TabShowsEnum.组合装,
			width: 100,
			align: "center",
			dataIndex: 'sku_threeDaySales',
			key: 'sku_threeDaySales',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_threeDaySales}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="近7天该货品规格已付款维度的销售数量（不含今日）。数据来源于销售利润报表，当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>7日销量</div>
					</Tooltip>
					<Popover content="支持当前页排序，按7日销量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.sevenDaySales === 排序.不排序 ? "morenpaixu" : saleSortObj.sevenDaySales === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("sevenDaySales"); } }
						/>
						{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("sevenDaySales"); } } /> */}
					</Popover>
				</div>
			),
			sortSet: { name: "7日销量" },
			hide: TabShowsEnum.组合装,
			width: 100,
			align: "center",
			dataIndex: 'sku_sevenDaySales',
			key: 'sku_sevenDaySales',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_sevenDaySales}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="近15天该货品规格已付款维度的销售数量（不含今日）。数据来源于销售利润报表，当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>15日销量</div>
					</Tooltip>
					<Popover content="支持当前页排序，按15日销量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.fifteenDaySales === 排序.不排序 ? "morenpaixu" : saleSortObj.fifteenDaySales === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("fifteenDaySales"); } }
						/>
						{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("fifteenDaySales"); } } /> */}
					</Popover>

				</div>
			),
			sortSet: { name: "15日销量" },
			hide: TabShowsEnum.组合装,
			width: 100,
			align: "center",
			dataIndex: 'sku_fifteenDaySales',
			key: 'sku_fifteenDaySales',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_fifteenDaySales}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="近30天该货品规格已付款维度的销售数量（不含今日）。数据来源于销售利润报表，当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>30日销量</div>
					</Tooltip>
					<Popover content="支持当前页排序，按30日销量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.thirtyDaySales === 排序.不排序 ? "morenpaixu" : saleSortObj.thirtyDaySales === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("thirtyDaySales"); } }
						/>
						{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("thirtyDaySales"); } } /> */}
					</Popover>

				</div>
			),
			sortSet: { name: "30日销量" },
			hide: TabShowsEnum.组合装,
			width: 100,
			align: "center",
			dataIndex: 'sku_thirtyDaySales',
			key: 'sku_thirtyDaySales',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_thirtyDaySales}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="昨日货品规格已发货维度的销售数量（不含今日）。数据来源于销售利润报表，空包订单以及当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>昨日实发量</div>
					</Tooltip>
					<Popover content="支持当前页排序，昨日实发量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.yesterDayActualNumber === 排序.不排序 ? "morenpaixu" : saleSortObj.yesterDayActualNumber === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("yesterDayActualNumber"); } }
						/>
						{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("yesterDayActualNumber"); } } /> */}
					</Popover>
				</div>
			),
			sortSet: { name: "昨日实发量" },
			hide: TabShowsEnum.组合装,
			width: 120,
			align: "center",
			dataIndex: 'sku_yesterDayActualNumber',
			key: 'sku_yesterDayActualNumber',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_yesterDayActualNumber}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="近3天该货品规格已发货维度的销售数量（不含今日）。数据来源于销售利润报表，空包订单以及当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>3日实发量</div>
					</Tooltip>
					<Popover content="支持当前页排序，按3日实发量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.threeDayActualNumber === 排序.不排序 ? "morenpaixu" : saleSortObj.threeDayActualNumber === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("threeDayActualNumber"); } }
						/>
						{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("threeDayActualNumber"); } } /> */}
					</Popover>
				</div>
			),
			sortSet: { name: "3日实发量" },
			hide: TabShowsEnum.组合装,
			width: 120,
			align: "center",
			dataIndex: 'sku_threeDayActualNumber',
			key: 'sku_threeDayActualNumber',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_threeDayActualNumber}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="近7天该货品规格已发货维度的销售数量（不含今日）。数据来源于销售利润报表，空包订单以及当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>7日实发量</div>
					</Tooltip>
					<Popover content="支持当前页排序，按7日实发量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.sevenDayActualNumber === 排序.不排序 ? "morenpaixu" : saleSortObj.sevenDayActualNumber === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("sevenDayActualNumber"); } }
						/>
						{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("sevenDayActualNumber"); } } /> */}
					</Popover>
				</div>
			),
			sortSet: { name: "7日实发量" },
			hide: TabShowsEnum.组合装,
			width: 120,
			align: "center",
			dataIndex: 'sku_sevenDayActualNumber',
			key: 'sku_sevenDayActualNumber',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_sevenDayActualNumber}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="近15天该货品规格已发货维度的销售数量（不含今日）。数据来源于销售利润报表，空包订单以及当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>15日实发量</div>
					</Tooltip>
					<Popover content="支持当前页排序，按15日实发量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.fifteenDayActualNumber === 排序.不排序 ? "morenpaixu" : saleSortObj.fifteenDayActualNumber === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("fifteenDayActualNumber"); } }
						/>
						{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("fifteenDayActualNumber"); } } /> */}
					</Popover>
				</div>
			),
			sortSet: { name: "15日实发量" },
			hide: TabShowsEnum.组合装,
			width: 120,
			align: "center",
			dataIndex: 'sku_fifteenDayActualNumber',
			key: 'sku_fifteenDayActualNumber',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_fifteenDayActualNumber}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<Tooltip title="近30天该货品规格已发货维度的销售数量（不含今日）。数据来源于销售利润报表，空包订单以及当日建立关联商品的订单不纳入统计。">
						<div className={s["tips-hover"]}>30日实发量</div>
					</Tooltip>
					<Popover content="支持当前页排序，按30日实发量字段">
						<Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.thirtyDayActualNumber === 排序.不排序 ? "morenpaixu" : saleSortObj.thirtyDayActualNumber === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("thirtyDayActualNumber"); } }
						/>
						{/* <SwapOutlined className="r-pointer r-mr-4" style={ { transform: 'rotate(90deg)' } } onClick={ () => { handleSort("thirtyDayActualNumber"); } } /> */}
					</Popover>
				</div>
			),
			sortSet: { name: "30日实发量" },
			hide: TabShowsEnum.组合装,
			width: 120,
			align: "center",
			dataIndex: 'sku_thirtyDayActualNumber',
			key: 'sku_thirtyDayActualNumber',
			render: (value:any, row:any, index:number) => {
				if (row?.sku_isSummary) {
					return {
						children: <span className="r-fw-500">{row.sku_thirtyDayActualNumber}</span>,
						props: {},
					};
				} else if (row?.sku_isCollapse) {
					return null;
				} else {
					return {
						children: <span>{value || 0}</span>,
						props: {},
					};
				}
			},
		},
		{
			title: '备注',
			width: 140,
			dataIndex: 'sku_sysSkuMemo',
			key: 'sku_sysSkuMemo',
			render: (t: string, row: any, index: number) => {
				if (row?.sku_isSummary || row?.sku_isCollapse || row?.sku_sum) {
					return null;
				} else {
					return (
						<div className={ cs('colorblack') }>
							<EditText isSingle={ activeKey !== TabShowsEnum.组合装 } tipText="简称" text={ t } onChange={ (text: string) => { editSysSkuMemo(text, row); } } canEmpty maxLength={ 500 } />
						</div>
					);
				}
			}
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					条形码
					{activeKey === TabShowsEnum.系统规格 && <Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.条形码 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.条形码); } }
					/>}
				</div>
			),
			width: 80,
			dataIndex: 'sku_barCode',
			key: 'sku_barCode',
			render: renderContent,
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					货号
					{activeKey === TabShowsEnum.系统规格 && <Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.货号 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.货号); } }
					/>}
				</div>
			),
			width: 80,
			dataIndex: 'sku_itemNo',
			key: 'sku_itemNo',
			render: renderContent,
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					<div>可售天数</div>
					<Popover placement="right" content={saleDaysSettingContent}>
						<SettingOutlined className="r-ml-5 r-c-gray" size={16} style={{ paddingTop: "2px" }} />
					</Popover>
					{
						activeKey === TabShowsEnum.系统规格 && <Icon
							className="r-ml-5 r-c-gray"
							type={ saleSortObj.availableSalesDay === 排序.不排序 ? "morenpaixu" : saleSortObj.availableSalesDay === 排序.降序 ? "paixuxia" : "paixushang" }
							size={ 16 }
							onClick={ () => { handleSort("availableSalesDay"); } }
						/>
					}
				</div>
			),
			width: 120,
			dataIndex: 'sku_availableSalesDay',
			key: 'sku_availableSalesDay',
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					创建时间 
					{activeKey === TabShowsEnum.系统规格 && <Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.创建时间 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.创建时间); } }
					/>}
				</div>
			),
			width: 140,
			dataIndex: 'sku_created',
			key: 'sku_created',
			render: renderContent,
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					供应商
					{activeKey === TabShowsEnum.系统规格 && <Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.供应商 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.供应商); } }
					/>}
				</div>
			),
			width: 120,
			dataIndex: 'sku_supplierName',
			key: 'sku_supplierName',
			render: renderContent,
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					品牌
					<Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.品牌 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.品牌); } }
					/>
				</div>
			),
			width: 120,
			dataIndex: 'brandName',
			key: 'brandName',
			render: (text, row) => {
				return {
					children: row.rowSpan && <div>{ row.sys_brandName }</div>
				};
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.rowSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					成本价
					{activeKey === TabShowsEnum.系统规格 && <Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.成本价 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.成本价); } }
					/>}
				</div>
			),
			width: 120,
			dataIndex: 'sku_cost_price',
			key: 'sku_cost_price',
			render: (text, row) => {
				return (
					<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 } type={ FieldsPermissionCheckTypeEnum.仅展示 } noPermissionRender={ <span>***</span> }>
						<span>{row.sku_costPrice}</span>
					</FieldsPermissionCheck>
				);
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					售价
					{activeKey === TabShowsEnum.系统规格 && <Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.售价 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.售价); } }
					/>}
				</div>
			),
			width: 120,
			dataIndex: 'sku_price',
			key: 'sku_price',
			render: renderContent,
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					分类
					<Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.分类 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.分类); } }
					/>
				</div>
			),
			width: 120,
			dataIndex: 'classifyName',
			key: 'classifyName',
			render: (text, row) => {
				return {
					children: row.rowSpan && <div>{ row.sys_classifyName }</div>
				};
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.rowSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					货品编码
					<Icon
						className="r-ml-5 r-c-gray"
						type={ sortQueryObj.sortType === InventoryInfo.货品编码 ? sortQueryObj.sortValue === 查询排序.降序 ? "paixuxia" : "paixushang" : "morenpaixu"}
						size={ 16 }
						onClick={ () => { handleSortQuery(InventoryInfo.货品编码); } }
					/>
				</div>
			),
			width: 120,
			dataIndex: 'outerId',
			key: 'outerId',
			render: (text, row) => {
				return {
					children: row.rowSpan && <div>{ row.sys_outerId }</div>
				};
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.rowSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '关联平台商品',
			width: 120,
			dataIndex: 'sku_relate_platform_item',
			key: 'sku_relate_platform_item',
			shouldCellUpdate,
			render: (text, row) => {
				if (row.isCollapse) return null;
				return (
					<UpdateArchives
						onChange={ () => {
							ref?.current?.refresh();
						} }
						text={ <div className="r-fc-1890FF r-pointer">查看</div> }
						sysItemId={ row.sku_sysItemId }
						sysSkuId={ row.sku_sysSkuId as number }
					/>
				);
			},
		},
		{
			title: (
				<div className="r-flex">
					操作
					<Tooltip title="展开/收起所有货品规格" placement="topRight">
						<div>
							<Icon onClick={ batchToggleList } size={ 14 } style={ { color: '#FD8204' } } type={ batchToggleStatus ? 'shouqi' : 'zhankai' } />
						</div>
					</Tooltip>
				</div>
			),
			sortSet: { name: "操作" },
			colFixed: 'right',
			dataIndex: 'action',
			key: "action",
			width: 80,
			render: (value, row, index) => {
				if (row?.sku_isSummary) {
					return {
						children: value,
						props: {},
					};
				} else if (row?.sku_isCollapse || row?.sku_sum) {
					return null;
				} else {
					return {
						children: (
							<div>
								<Button size="small" type="primary" onClick={ _.debounce(() => handleShowItem(value, row, index), 200) } data-point={ Pointer['库存_库存看板_日志'] }>日志</Button>
							</div>
						),
						props: {},
					};
				}
			},
		}
	];

	const editSysSkuMemo = async(sysSkuMemo, row) => {
		try {
			const params = {
				sysSkuId: row?.sku_id,
				sysSkuMemo
			};
			await ItemSysSkuEditSysSkuApi(params);
			setDataSource((prev) => {
				prev.forEach(d => {
					if (d.sku_id === row?.sku_id) {
						d.sku_sysSkuMemo = sysSkuMemo;
					}
				});
				return [...prev];
			});
			message.success('编辑成功');
		} catch (error) {
			message.error('编辑失败');
		}

	};

	const columns = useMemo(() => {
		let _columnstmp = columnstmp.filter(i => !(Number(i.hide) > -1 && i.hide == activeKey));
		const columnskeys = columnList.map(i => i.key);
		if (isVirStock) {
			const hideItems = ['sku_salableItemPreemptedNum', 'sku_transitItemStock', 'sku_salableItemStock'];
			if (activeKey === TabShowsEnum.组合装) hideItems.push('realAllStock');
			return _columnstmp.filter(i => i.key === "index" || (!hideItems.includes(i.key) && columnskeys.includes(i.key)));
		}
		if (!(userStore?.userInfo?.version === stockVersion.库存版 && userStore?.inventoryDeduct)) {
			const hideItems = ['sku_salableItemPreemptedNum', 'sku_fictitiousStock'];
			return _columnstmp.filter(i => i.key === "index" || (!hideItems.includes(i.key) && columnskeys.includes(i.key)));
		}
		const hideItems = [''];
		_columnstmp = _columnstmp.filter(i => !hideItems.includes(i.key));
		return _columnstmp.filter((i) => i.key === "index" || columnskeys.includes(i.key));
	}, [isVirStock, batchToggleStatus, userStore?.inventoryDeduct, activeKey, columnList, skuPicConfig, sortQueryObj, saleSortObj, saleDaysType]);

	// 数据请求
	const getProductList = async(params: StockGetPageListRequest) => {
		let info:StockGetPageListRequest = {
			...clearParams(params),
			...getSortQueryObj()
		};
		let [createdStart = "", createdEnd = ""] = params.created || [];
		createdStart = createdStart
			? dayjs(createdStart).format("YYYY-MM-DD HH:mm:00")
			: createdStart;
		createdEnd = createdEnd
			? dayjs(createdEnd).format("YYYY-MM-DD HH:mm:59")
			: createdEnd;
		info.createdStart = createdStart;
		info.createdEnd = createdEnd;
		delete info.created;
		if (info.sort == 0) {
			info.sort = undefined;
		}
		if (info.stockStatus && info.stockStatus != 'ALL') {
			info.stockStatusList = [info.stockStatus];
		}
		info.isCombination = activeKey === TabShowsEnum.组合装 ? "1" : "0";
		info.searchType = SearchType[activeKey];
		delete info.stockStatus;
		if (info.isCombination == 'ALL') {
			delete info.isCombination;
		}
		let sysSkuIdList = local.get(PRINT_BATCH_ABNORMAL_TRADE_SYSSKUIDLIST);
		if (sysSkuIdList) {
			local.remove(PRINT_BATCH_ABNORMAL_TRADE_SYSSKUIDLIST);
			info.sysSkuIdList = sysSkuIdList;
		}
		if (info.skuOuterIdList?.length == 1) {
			info.skuOuterId = info.skuOuterIdList[0];
			delete info.skuOuterIdList;
		}
		if (info.warnStockStatus) {
			info.warnStockStatusList = [info.warnStockStatus];
		}
		delete info.warnStockStatus;

		console.log('查询条件：', info);
		setCurSearchParams(info);
		try {
			setLoading(true);
			return StockGetPageListApi(info);
		} catch (error) {
			console.log(error);
			setLoading(false);
			return [];
		}
	};


	const handleSort = (key) => {
		setSaleSortObj((prev) => {
			Object.keys(prev).forEach(okey => {
				if (key !== okey) {
					prev[okey] = 排序.不排序;
				}
			})
			if (prev[key] === 排序.降序) {
				prev[key] = 排序.升序;
			} else {
				prev[key] = 排序.降序;
			}
			return { ...prev };
		});
		handleList({ info: getOldInfo(), sortKey: key });
	};

	const handleSortQuery = (key) => {
		setSortQueryObj((prev) => {
			if (prev.sortType === key) {
				if (prev.sortValue === 查询排序.降序) {
					prev.sortValue = 查询排序.升序;
				} else if (prev.sortValue === 查询排序.升序) {
					prev.sortValue = "";
					prev.sortType = "";
				} else {
					prev.sortValue = 查询排序.降序
				}
				return { ...prev };
			} else {
				return {
					sortType: key,
					sortValue: 查询排序.降序
				};
			}

		});
		ref.current.submit();
	}

	// 返回值重构
	const responseAdapter = (info: any) => {
		if (info) {
			setOldInfo(info);
		}
		return handleList({info});
	};

	const sortList = ({ data, sortKey }) => {
		let _saleSortObj = getSaleSortObj();
		data.forEach(item => {
			item.sevenDaySales = 0;
			item.fifteenDaySales = 0;
			item.threeDaySales = 0;
			item.thirtyDaySales = 0;
			item.salableItemStock = 0;
			item.yesterDaySales = 0;
			item.yesterDayActualNumber = 0; // 昨日实发数量
			item.threeDayActualNumber = 0; // 3日实发数量
			item.sevenDayActualNumber = 0; // 七日实发数量
			item.fifteenDayActualNumber = 0;	// 十五日实发数量
			item.thirtyDayActualNumber = 0; 	// 三十日实发数量
			item.availableSalesDay = 0; 	// 可售天数
			item.sysSkuStockInfoList.forEach((sysItem) => {
				item.sevenDaySales += Number(sysItem?.sevenDaySales || 0);
				item.threeDaySales += Number(sysItem?.threeDaySales || 0);
				item.fifteenDaySales += Number(sysItem?.fifteenDaySales || 0);
				item.thirtyDaySales += Number(sysItem?.thirtyDaySales || 0);
				item.yesterDaySales += Number(sysItem?.yesterDaySales || 0);
				item.salableItemStock += Number(sysItem?.salableItemStock || 0);
				item.yesterDayActualNumber += Number(sysItem?.yesterDayActualNumber || 0);
				item.threeDayActualNumber += Number(sysItem?.threeDayActualNumber || 0);
				item.sevenDayActualNumber += Number(sysItem?.sevenDayActualNumber || 0);
				item.fifteenDayActualNumber += Number(sysItem?.fifteenDayActualNumber || 0);
				item.thirtyDayActualNumber += Number(sysItem?.thirtyDayActualNumber || 0);
				item.availableSalesDay += Number(sysItem?.availableSalesDay || 0);
			});
			item.sysSkuStockInfoList.sort((a, b) => {
				if (_saleSortObj[sortKey] === 排序.升序) {
					return (a[sortKey] - b[sortKey]);
				} else {
					return (b[sortKey] - a[sortKey]);
				}
			});
		});
		data.sort((a, b) => {
			if (_saleSortObj[sortKey] === 排序.升序) {
				return (a[sortKey] - b[sortKey]);
			} else {
				return (b[sortKey] - a[sortKey]);
			}
		});
		return data;
	};

	const handleList = ({ info, sortKey = "" }) => {
		let sum = {
			// 总库存
			summaryStockNum: 0,
			// 可配货库存
			salableItemDistributableStock: 0,
			// 订单占用库存
			salableItemPreemptedNum: 0,
			// 采购在途
			transitItemStock: 0,
			// 销退在途
			refundStockWaitHandNum: 0,
			fictitiousDistributableStock: 0,
			fictitiousStock: 0,
			yesterDaySales: 0,
			sevenDaySales: 0,
			threeDaySales: 0,
			fifteenDaySales: 0,
			thirtyDaySales: 0,
			yesterDayActualNumber: 0,
			threeDayActualNumber: 0,
			sevenDayActualNumber: 0, // 七日实发数量
			fifteenDayActualNumber: 0,	// 十五日实发数量
			thirtyDayActualNumber: 0, 	// 三十日实发数量
			sum: true,
		};
		setLoading(false);
		let _list = _.cloneDeep(info?.list || []);
		if (sortKey) {
			_list = sortList({ data: _list, sortKey });
		}
		// 总结栏
		let oldList:StockInfoListProps[] = _list;
		oldList.forEach((k:any, i:number) => {
			let summary = {
				isSummary: true,
				summaryStockNum: 0, // 实际总库存
				salableItemPreemptedNum: 0, // 总预占库存
				salableItemDistributableStock: 0, // 总可用库存
				transitItemStock: 0, // 总在途
				fictitiousStock: 0, // 虚拟总库存
				refundStockWaitHandNum: 0, // 销退在途
				fictitiousDistributableStock: 0, // 虚拟可配货库存
				yesterDaySales: 0,
				sevenDaySales: 0,
				threeDaySales: 0,
				fifteenDaySales: 0,
				thirtyDaySales: 0,
				yesterDayActualNumber: 0,
				threeDayActualNumber: 0,
				sevenDayActualNumber: 0, // 七日实发数量
				fifteenDayActualNumber: 0,	// 十五日实发数量
				thirtyDayActualNumber: 0, 	// 三十日实发数量
			};
			k.sysSkuStockInfoList.forEach((item:any) => {
				summary.salableItemDistributableStock += Number(item?.salableItemDistributableStock || 0);
				summary.summaryStockNum += Number(item?.salableItemStock || 0);
				summary.salableItemPreemptedNum += Number(item?.salableItemPreemptedNum || 0);
				summary.transitItemStock += Number(item?.transitItemStock || 0);
				summary.fictitiousStock += Number(item?.fictitiousStock || 0);
				summary.yesterDaySales += Number(item?.yesterDaySales || 0);
				summary.sevenDaySales += Number(item?.sevenDaySales || 0);
				summary.threeDaySales += Number(item?.threeDaySales || 0);
				summary.fifteenDaySales += Number(item?.fifteenDaySales || 0);
				summary.thirtyDaySales += Number(item?.thirtyDaySales || 0);
				summary.yesterDayActualNumber += Number(item?.yesterDayActualNumber || 0);
				summary.threeDayActualNumber += Number(item?.threeDayActualNumber || 0);
				summary.sevenDayActualNumber += Number(item?.sevenDayActualNumber || 0);
				summary.fifteenDayActualNumber += Number(item?.fifteenDayActualNumber || 0);
				summary.thirtyDayActualNumber += Number(item?.thirtyDayActualNumber || 0);
				summary.refundStockWaitHandNum += Number(item?.refundStockWaitHandNum || 0);
				summary.fictitiousDistributableStock += Number(item?.fictitiousDistributableStock || 0);
				sum.summaryStockNum += Number(item?.salableItemStock || 0);
				sum.refundStockWaitHandNum += Number(item?.refundStockWaitHandNum || 0);
				sum.salableItemPreemptedNum += Number(item?.salableItemPreemptedNum || 0);
				sum.transitItemStock += Number(item?.transitItemStock || 0);
				sum.salableItemDistributableStock += Number(item?.salableItemDistributableStock || 0);
				sum.fictitiousDistributableStock += Number(item?.fictitiousDistributableStock || 0);
				sum.fictitiousStock += Number(item?.fictitiousStock || 0);
				sum.yesterDaySales += Number(item?.yesterDaySales || 0);
				sum.sevenDaySales += Number(item?.sevenDaySales || 0);
				sum.threeDaySales += Number(item?.threeDaySales || 0);
				sum.fifteenDaySales += Number(item?.fifteenDaySales || 0);
				sum.thirtyDaySales += Number(item?.thirtyDaySales || 0);
				sum.yesterDayActualNumber += Number(item?.yesterDayActualNumber || 0);
				sum.threeDayActualNumber += Number(item?.threeDayActualNumber || 0);
				sum.sevenDayActualNumber += Number(item?.sevenDayActualNumber || 0);
				sum.fifteenDayActualNumber += Number(item?.fifteenDayActualNumber || 0);
				sum.thirtyDayActualNumber += Number(item?.thirtyDayActualNumber || 0);
				// sum.refundStockWaitHandNum += Number(item?.refundStockWaitHandNum || 0);
				// sum.fictitiousDistributableStock += Number(item?.fictitiousDistributableStock || 0);
			});
			// 展开规格的点击判断
			if (k?.sysSkuStockInfoList?.length > MAX_SHOW_COUNT) {
				k.sysSkuStockInfoList.push({ isCollapse: true });
			}
			if (k?.sysSkuStockInfoList?.length > 1) {
				k.sysSkuStockInfoList.push(summary);
			}
		});
		if (oldList.length) {
			oldList.push({ sysSkuStockInfoList: [{ ...sum }] });
		}

		// 把数组展平
		let showList: Array<StockInfoItemProps> = [];
		oldList.forEach((sysItem: StockInfoListProps, sysIndex: number) => {
			let sysData: StockInfoItemProps = {};
			for (const key in sysItem) {
				if (Object.prototype.hasOwnProperty.call(sysItem, key)) {
					if (key !== 'sysSkuStockInfoList') {
						sysData[`sys_${key}`] = sysItem[key];
					}
				}
			}

			// 可能先需要sort排序
			_.sortBy(sysItem.sysSkuStockInfoList, function(item) {
				return item.sort;
			});

			sysItem.sysSkuStockInfoList?.forEach((skuItem: any, skuIndex: number) => {
				let showItem: StockInfoItemProps = { ...sysData };

				if (skuIndex == 0) {
					showItem['rowSpan'] = sysItem.sysSkuStockInfoList.length;
					showItem['isSysIndex'] = true; // 后面需要显示sys序号
				}

				showItem['collapseIndex'] = skuIndex;

				for (const key in skuItem) {
					if (Object.prototype.hasOwnProperty.call(skuItem, key)) {
						showItem[`sku_${key}`] = skuItem[key];
					}
				}

				showItem['groupId'] = `groupId${showItem['sys_id']}`;
				showItem[`rowId`] = `${showItem['sys_id']}_${showItem['sku_id'] || 0}_${skuIndex}`;

				showList.push(showItem);
			});
		});
		// 每次的数据处理序号
		let i: number = 0;
		let list = showList.map((item: StockInfoItemProps) => {
			return {
				...item,
				index: item.isSysIndex ? ++i : i,
			};
		});
		setBatchToggleStatus(false);
		setDataSource(list);
		console.log('dataSource:::', list);
		return {
			total: info?.total || 0,
			list
		};
	};

	const tableRowDataSource = useMemo(() => {
		if (activeKey === TabShowsEnum.系统规格) {
			return dataSource.filter(i => !(i.sku_isSummary || i.sku_isCollapse || i.collapseShow)).map((item, index) => {
				return {
					...item,
					rowSpan: 1,
					index: index + 1
				};
			});
		}
		return dataSource.filter(i => i.sku_isSummary || i.sku_isCollapse || i.collapseIndex < MAX_SHOW_COUNT || i.collapseShow);
	}, [dataSource]);

	// 保存当前 SKU  当前行的值，当前行数据，行索引
	const handleShowItem = (v: any, r: any, i: number) => {
		// console.log(v, r, i);
		setInfoModalShow(true);
		setSkuInfo(r);
	};

	const batchModalHide = () => {
		setInfoModalShow(false);
	};

	// 导出excel文档
	const onDownloadExcel = async(downloadType) => {
		let params = { ...curSearchParams };
		let exportSysSkuIdList = [];
		if (downloadType !== "all") {
			exportSysSkuIdList = dataSource.map(item => item.sku_sysSkuId)?.filter(Boolean)
		}
		if (downloadType === "current") {
			params.exportSysSkuIdList = exportSysSkuIdList;
			params.needPic = false;
		} else if (downloadType === "currentWithImage") {
			params.exportSysSkuIdList = exportSysSkuIdList;
			params.needPic = true;
		}
		setExportLoading(true);
		await downloadCenter({
			requestParams: params,
			fileName: `快递助手ERP${activeKey !== TabShowsEnum.组合装 ? '单品' : '组合'}库存导出`,
			module: ModulesFunctionEnum.库存看板,
			permission_source: FunctionPermissionEnum.导出库存
		});
		setExportLoading(false);
	};
	// 刷新页面
	const refreshPage = () => {
		ref?.current?.refresh();
	};

	const changeActiveKey = (activeKey) => {
		setActiveKey(activeKey);
		local.set(STOCKINFO_ACTIVEKEY, activeKey);
		setTimeout(() => {
			ref.current.submit();
		}, 200);
	};

	const expandContext = (
		<div className="r-flex r-jc-sb">
			<div className="r-flex">
				<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.导入库存 }>
					<Button disabled={ !userStore.hasFunctionPermission(FunctionPermissionEnum.导入库存) } data-point={ Pointer.库存_按货品盘库_导入库存 } type="primary" onClick={ () => { setImportModalVisible(true); } } className="importBtn">导入库存</Button>
				</FunctionPermissionCheck>
				<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.导出库存 }>
					<Dropdown
						overlay={
							<Menu>
								<Menu.Item key="current" onClick={ () => onDownloadExcel('current') }>导出当前页</Menu.Item>
								<Menu.Item key="all" onClick={ () => onDownloadExcel('all') }>导出全部</Menu.Item>
								<Menu.Item key="currentWithImage" onClick={ () => onDownloadExcel('currentWithImage') }>导出当前页(带图片)</Menu.Item>
							</Menu>
						}
						disabled={ !userStore.hasFunctionPermission(FunctionPermissionEnum.导出库存) }
					>
						<Button 
							loading={ exportLoading } 
							className="exportBtn"
							data-point={ Pointer.库存_库存看板_导出 }
						>
							<FileExcelOutlined className="r-c-666" /> 导出 <DownOutlined className="r-c-999" />
						</Button>
					</Dropdown>
				</FunctionPermissionCheck>
			</div>
		</div>
	);

	const getSkuPicConfig = async() => {
		let config = _.cloneDeep(SkuPicConfig);
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: 'STOCK_PAGE_COLUMN_PIC_CONFIG' });
			config = JSON.parse(res?.value);
		} catch (error) {
			console.log(error, 'error');
		}
		setSkuPicConfig(config);
	};

	const getQueryConfig = async() => {
		let list = _.cloneDeep(TableQueryList[activeKey]);
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: STOCK_INFO_QUERY_BIZ[activeKey] });
			const resList = JSON.parse(res?.value);
			const newList = [];
			const noExistList = [];
			list.forEach((item, index) => {
				const existItem = resList.find(i => i.key === item.key);
				if (existItem) {
					newList.push(existItem);
				} else {
					noExistList.push(item);
				}
			})
			newList.sort((a, b) => {
				return a.index - b.index;
			})
			if (noExistList.length) {
				noExistList.forEach((item, index) => {
					item.index = newList.length + index + 1;
				})
			}
			list = [...newList, ...noExistList];
		} catch (error) {
			console.log(error, 'error');
		}
		setQueryList([...list]);
	};

	const handleQueryReset = () => {
		TradeDictInsertDictApi({
			userDictEnum: STOCK_INFO_QUERY_BIZ[activeKey],
			value: JSON.stringify(TableQueryList[activeKey]),
		}).then(() => {
			setQueryList([...TableQueryList[activeKey]]);
		});
	};

	const handleQuerySortChange = (list) => {
		TradeDictInsertDictApi({
			userDictEnum: STOCK_INFO_QUERY_BIZ[activeKey],
			value: JSON.stringify(list)
		}).then(() => {
			setQueryList([...list]);
		});
	};

	const getColumnConfig = async() => {
		let list = _.cloneDeep(TableSortList[activeKey]);
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: STOCK_INFO_COLUMN_BIZ[activeKey] });
			const resList = JSON.parse(res?.value);
			list.forEach((item, index) => {
				const existItem = resList.find(i => i.key === item.key);
				if (existItem) {
					list[index] = existItem;
				}
			})
		} catch (error) {
			console.log(error, 'error');
		}
		setColumnList([...list]);
	};

	const handleReset = (s) => {
		TradeDictInsertDictApi({
			userDictEnum: STOCK_INFO_COLUMN_BIZ[activeKey],
			value: JSON.stringify(TableSortList[activeKey]),
		}).then(() => {
			setColumnList([...TableSortList[activeKey]]);
		});
	};

	const handleSortChange = (list) => {
		TradeDictInsertDictApi({
			userDictEnum: STOCK_INFO_COLUMN_BIZ[activeKey],
			value: JSON.stringify(list)
		}).then(() => {
			setColumnList([...list]);
		});
	};

	useLayoutEffect(() => {
		getColumnConfig();
		getQueryConfig();
	}, [activeKey]);

	return (
		<NormalLayout className={ cs('r-w-full') }>
			<div className={ cs(s.main) }>
				{/* SearchTable */}
				<SearchTable<StockInfoItemProps>
					searchBtnPoint={ Pointer['库存_库存看板_查询'] }
					searchBtnProps={ { style: { marginLeft: 0, marginRight: '8px' } } }
					pageSizeId="stockInfoTable"
					ref={ ref } // 引用
					form={ form } //
					fetchData={ getProductList } // 接口请求
					responseAdapter={ responseAdapter } // 返回值适配
					searchBtnText="查询"
					resetBtnText="重置"
					additionalFormNode={ <SearchSetting colSortList={ queryList } handleQueryReset={ handleQueryReset } handleQuerySortChange={ handleQuerySortChange } /> }
					showSearch // 是否显示查询
					rowFormConfig={ { // 表单配置
						formList: FormField,
						defaultParams: { // 查询表单设置初始
							sort: 0,
							stockStatus: 'ALL',
						},
						rowProps: {}, // 表单行配置
						colProps: { // 表单列配置
							// span: 3, // 表单条件宽度
						},
						formItemProps: {},
					} }
					baseTableConfig={ { // 表格基础设置
						dataSource: tableRowDataSource,
						rowKey: 'rowId',
						columns, // 列配置
						// pagination: false, // 单独的分页
						pagination: {
							pageSizeOptions: activeKey === TabShowsEnum.系统规格 ? [50, 100, 200, 500, 1000] : [10, 20, 50, 100, 200],
						},
						cachePgination: true,
						scroll: {
							scrollToFirstRowOnChange: true,
							x: '100%'
						},
						expandContext, // 自定义区域
						hasVt: false,
						fullWindowScroll: true,
						cardHeight: 100,
						offsetScroll: 64,
						tableContainerStyle: { marginBottom: '64px' },
						expandContextStickStyle: { position: 'sticky', top: '90px', paddingBottom: '10px', zIndex: 99, background: '#fff' },
						paginationWrapStyle: { position: 'fixed', bottom: 0, zIndex: 99, width: '100%' },
						expandContextStyle: 'tabExpand',
						loading,
						headerColSet: {
							useDrawer: true,
							resizeId: "stockInfo",
							sortId: "stockInfo",
							initList: columnList,
							onReset: (w, s) => {
								handleReset(s);
							},
							onSortChange: (list) => {
								handleSortChange(list);
							},
						}
					} }
					// onChange={ onChange }
				/>

				{/* 批量设置库存 */}
				{/* 批量设置成本价 */}
			</div>
			<StockInfoModal triggerNode={ false } visible={ infoModalShow } handleCancel={ batchModalHide } info={ skuInfo } />
			<StockTradeInfoModal
				triggerNode={ false }
				visible={ stockTradeModalShow.visible }
				handleCancel={ () => setStockTradeModalShow({ visible: false }) }
				sysSkuId={ stockTradeModalShow.sysSkuId }
				sysItemId={ stockTradeModalShow.sysItemId }
				skuOuterId={ stockTradeModalShow.skuOuterId }
			/>
			<GroupDetailModal
				visible={ groupDetailModalShow.visible }
				onCancel={ () => setGroupDetailModalShow({ visible: false }) }
				info={ groupDetailModalShow.info }
			/>
			{/* 导入弹窗 */}
			<BatchImportModal
				visible={ importModalVisible }
				onOkAfter={ refreshPage }
				onClose={ () => { setImportModalVisible(false); } }
			/>
			{/* 广告弹窗 */}
			{/* <div className="sys_position_replace" data-type="middlePopup" data-page="StockInfo" />
			<div className="sys_position_replace rightBottomPopup" data-type="rightBottomPopup" data-page="StockInfo" /> */}
		</NormalLayout>
	);
};
export default observer(StockInfo);
