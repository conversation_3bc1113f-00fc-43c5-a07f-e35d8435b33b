import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { Form, Button, Input, Card, Select, Empty } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import _ from "lodash";
import cs from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import SearchTable from '@/components/SearchTable';
import BaseModal from '@/components/Modal';
import { ChangeDataProps, SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import EnumSelect from '@/components/Select/EnumSelect';
import { StockInOutRecordGetListApi } from "@/apis/warehouse/stock";
import {
	StockInOutRecordGetListRequest, StockInOutRecordGetListResponse
} from '@/types/schemas/warehouse/stock';
import { StockInOutRecordListProps } from './interfance';
// import DateRangeComp from '@/components/DateRangeComp';
import { StockInfoItemProps } from '../../interfance';
import DatePicker from '@/components/DatePicker'; // DatePicker.RangePicker
import WarpText from '@/components-biz/WarpText';
import s from './index.module.scss';
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import { StockInOutType, StockUpType, StockOutType, StockUpOutType, StockExcelType } from '@/pages/Warehouse/constants';
import message from "@/components/message";

interface StockInfoModalProps {
	triggerNode: React.ReactNode;
	visible?:boolean;
	info:StockInfoItemProps;
	handleCancel?: () => void;
}

const myformat = "YYYY-MM-DD HH:mm:ss";

const StockInfoModal: React.FC<StockInfoModalProps> = (props) => {
	const { triggerNode, visible, info, handleCancel } = props;
	const [form] = useForm();
	const ref = useRef<SearchTableRefProps>(); // 获取表单对象
	const startTime:Dayjs = useMemo(() => dayjs().startOf('day').subtract(6, 'day'), []);
	const endTime:Dayjs = useMemo(() => dayjs().endOf('day'), []);
	const [stockType, setStockType] = useState({});
	const [two, setTwo] = useState({});
	const [dates, setDates] = useState([]); // 待选日期发生变化的回调 默认展示近7天，最大支持1个自然月查询
	const [value, setValue] = useState<[Dayjs, Dayjs]>([startTime, endTime]); // 日期变化
	const [hackValue, setHackValue] = useState<[Dayjs, Dayjs]>();
	const [loading, setLoading] = useState(false);
	const [total, setTotal] = useState(0);


	// 表格列定义
	const columns: ColumnsType<StockInOutRecordListProps> = [
		{
			width: 20,
			align: 'center',
			render: (value, row, index) => {
				return {
					children: <span>{index + 1}</span>,
					props: {},
				};
			},
		},
		{
			title: '仓库/货位',
			width: 40,
			dataIndex: 'storageName',
			key: 'storageName',
			render: (value, row, index) => {
				return (
					<div className={ cs('r-l-preWrap') }>
						<div>{value}</div>
						<div style={ { fontSize: '12px' } } className={ cs('r-fc-black-45') }>{row.itemPlaceId}</div>
					</div>
				);
			},
		},
		{
			title: '类型',
			width: 40,
			dataIndex: 'stockUpType',
			key: 'stockUpType',
			render: (value, row, index) => {
				return (
					<div>
						{StockUpOutType[value]}
					</div>
				);
			},
		},
		{
			title: '操作前',
			width: 30,
			dataIndex: 'opBeforeNum',
			key: 'opBeforeNum',
		},
		{
			title: '操作数量',
			width: 30,
			dataIndex: 'opNum',
			key: 'opNum',
			render: (value, row, index) => {
				return (
					<div className={ value > 0 ? 'r-c-success' : 'r-c-warning' }>
						{value}
					</div>
				);
			}
		},
		{
			title: '操作后',
			width: 30,
			dataIndex: 'opAfterNum',
			key: 'opAfterNum',
		},
		{
			title: '成本价',
			width: 30,
			dataIndex: 'costPrice',
			key: 'costPrice',
		},
		{
			title: '售卖价',
			width: 30,
			dataIndex: 'retailPrice',
			key: 'retailPrice',
		},
		{
			title: '操作时间/系统单号/订单编号/售后单号',
			width: 80,
			dataIndex: 'created',
			key: 'created',
			render: (value, row, index) => {
				return (
					<div className="k-c-text">
						<p>{value}</p>
						{ row.tid && row.tid !== '-1' && <p className="r-fc-black-45">{row.tid}</p> }
						{ row.ptTid && row.ptTid !== '-1' && <p className="r-fc-black-45">{row.ptTid}</p> }
						{ row.refundId && (!row.tid || row.tid === '-1') && <p className="r-fc-black-45">{row.refundId}</p> }
					</div>
				);
			},
		},
		{
			title: '操作人',
			width: 40,
			dataIndex: 'opUserStr',
			key: 'opUserStr',
			render: (value, row, index) => {
				return (
					<WarpText>
						{value}
					</WarpText>
				);
			},
		}
		// {
		// 	title: '备注',
		// 	width: 30,
		// 	dataIndex: 'remark',
		// 	key: 'remark',
		// 	render: (value, row, index) => {
		// 		return (
		// 			<div>
		// 				{value}
		// 			</div>
		// 		);
		// 	},
		// }
	];

	// 日期范围获取
	const handleSizeChange = (value: [Dayjs, Dayjs]) => {
		console.log('时间:', dayjs(value[0]).format(myformat), dayjs(value[1]).format(myformat));

		setValue(value);
	};

	// 联动
	const handleOneChange = (data:any) => {
		if (Object.prototype.toString.call(data) == '[object Number]') {
			let twoType: typeof StockUpType | typeof StockOutType | typeof StockExcelType;
			switch (data) {
				case 0:
					twoType = {
						...StockOutType,
					};
					form.setFieldsValue({
						stockUpType: undefined
					});
					break;
				case 1:
					twoType = {
						...StockUpType,
					};
					form.setFieldsValue({
						stockUpType: undefined
					});
					break;
				case 2:
					twoType = {
						...StockExcelType,
					};
					form.setFieldsValue({
						stockUpType: undefined
					});
					break;
				default:
					break;
			}
			setTwo(twoType);

		} else {
			// 重置出入库类型
			setTwo({});
			form.setFieldsValue({
				stockUpType: undefined
			});
		}
	};

	const disabledDate = (current:any) => {
		if (current > dayjs().subtract(3, 'month').add(1, 'day').startOf('day') && current < dayjs().add(1, 'day').startOf('day')) {
			return false;
		} else {
			return true;
		}
	};

	const onOpenChange = (open:boolean) => {
		if (open) {
		  setHackValue(undefined);
		  setDates([]);
		} else {
		  setHackValue(undefined);
		}
	};

	// 表单元素定义 下拉框？日期？ 多选框？ 多选按钮？
	const FormFieldListV1: FormItemConfig[] = [
		{
			name: 'timeRange',
			label: "",
			children: (
				<KdzsDateRangePicker1 allowClear format="YYYY-MM-DD HH:mm" />
			),
			colProps: {
				// span: 8
			}
		},
		{
			name: 'stockInOutType',
			label: '',
			children: <EnumSelect
				placeholder="库存变更类型"
				enum={ StockInOutType }
				onChange={ handleOneChange }
				style={ { width: 160 } }
				size="small"
			/>
		},
		{
			name: 'stockUpType',
			label: '',
			children: <EnumSelect
				placeholder="出入库类型"
				enum={ two }
				notFoundContent={ (
					<Empty
						image={ Empty.PRESENTED_IMAGE_DEFAULT }
						imageStyle={ {
							width: 120,
							display: 'flex',
							justifyContent: 'center'
						} }
						description={ <span>暂无数据</span> }
					/>
				) }
				style={ { width: 160 } }
				size="small"
			/>
		},
		{
			name: 'tid',
			label: '',
			children: <Input placeholder="系统单号" size="small" />
		}
	];

	// 表格数据改变
	const onChange = (formData: ChangeDataProps<StockInOutRecordListProps>) => {
		console.log('表格数据改变:', formData);
	};



	// 数据请求
	const getProductList = async(params: StockInOutRecordGetListRequest) => {
		// let infoData = _.cloneDeep(list);
		// 这里请求数据
		// return Promise.resolve({ list: [...infoData].splice((info.pageNo - 1) * info.pageSize, info.pageSize), total: infoData.length });

		// 重置重新输入下日期
		let timeRange:[Dayjs, Dayjs] = params?.timeRange || [startTime, endTime];
		if (!params.timeRange) {
			await form.setFieldsValue({ timeRange: [dayjs().startOf('day').subtract(6, 'day'), dayjs().endOf('day')], });
			timeRange = form.getFieldValue('timeRange');
		}
		// 如果重置，把出入库类型数据清理掉
		if (params.stockInOutType === undefined && params.stockUpType === undefined && Object.keys(two).length) {
			setTwo({});
			form.setFieldsValue({
				stockUpType: undefined
			});
		}
		params = {
			...params,
			needOccupiedStock: false,
			startTime: dayjs(timeRange[0]).format(myformat),
			stopTime: dayjs(timeRange[1]).format(myformat),
			sysSkuId: info.sku_id,
		};

		delete params['timeRange'];

		console.log('查询条件：', params);

		try {
			setLoading(true);
			const infoData:StockInOutRecordGetListResponse['data'] = await StockInOutRecordGetListApi(params);
			const infoTotal = params.pageNo == 1 ? infoData?.total ?? 0 : total;
			setLoading(false);
			params.pageNo == 1 && setTotal(infoTotal);

			return {
				list: infoData?.list || [],
				total: infoTotal
			};
		} catch (error) {
			console.log(error);
			setLoading(false);
		}
	};

	// 返回值重构
	const responseAdapter = (info: any) => {
		console.log('responseAdapter', info);

		return {
			total: info?.total || 0,
			list: info?.list || []
		};
	};

	const resetDefaultValue = useCallback(
		() => {
			form?.setFieldsValue({ stockInOutType: undefined, stockUpType: undefined, timeRange: [startTime, endTime], });
		},
		[form, startTime, endTime],
	);

	const afterCancel = () => {
		// 清空搜索条件 更新时间选项
		resetDefaultValue();
		handleCancel && handleCancel();
	};

	useEffect(() => {
		resetDefaultValue();
		visible && ref?.current?.submit(); // 第一次会请求两次
	}, [resetDefaultValue, visible]);

	return (
		<BaseModal
			triggerNode={ triggerNode }
			// beforeOk={ }
			afterCancel={ afterCancel }
			visible={ visible }
			width={ 1190 }
			footer={ null }
			title="查看日志"
			centered
			forceRender
			className={ s.stockInfoModal }
		>
			<div style={ { maxHeight: '597px', minHeight: '384px', 'overflow': 'hidden' } }>
				<SearchTable<StockInOutRecordListProps>
					pageSizeId="stockInfoModalTable"
					ref={ ref } // 引用
					form={ form } //
					fetchData={ getProductList } // 接口请求
					responseAdapter={ responseAdapter } // 返回值适配
					searchBtnText="查询"
					resetBtnText="重置"
					showSearch // 是否显示查询
					autoSearch={ visible } // 开启后才自动搜索
					// showCard // 表单是否带边框
					rowFormConfig={ { // 表单配置
						formList: FormFieldListV1,
						defaultParams: { // 查询表单设置初始
							timeRange: [startTime, endTime],
						},
						rowProps: {}, // 表单行配置
						colProps: { // 表单列配置
							// span: 3, // 表单条件宽度
						},
						formItemProps: {

						},
						style: {
							padding: '0 0 8px 0'
						}
					} }
					baseTableConfig={ { // 表格基础设置
						rowKey: 'id',
						columns, // 列配置
						pagination: { // 分页设置:具体参数可参考Table-pagination
							// pageSizeOptions: ["10", "500"],
							// defaultCurrent: 1, // 当前页
							// defaultPageSize: 500
						},
						scroll: {
							scrollToFirstRowOnChange: true,
							// x: 'max-content',
							y: 400,
						},
						// expandContext // 自定义区域
						loading,
						innerTableStyle: {
							padding: 0,
						}
					} }
					// onChange={ onChange }
				/>
			</div>
		</BaseModal>
	);
};

export default StockInfoModal;
