
// 0: '未入库', 1: '部分入库', 2: '全部入库'
export enum 采购状态 {
	未入库,
	部分入库,
	全部入库
}

export enum 开启状态 {
	全部,
	开启,
	关闭
}
export enum 扫描设置 {
	入库数量 = "warehousingNum",
	出库数量 = "outNum",
	无采购单入库模式 = "noPurchaseNoModel",
	逐件扫描模式 = "pieceByPieceModel",
}

export const 采购单号id = "searchPurchaseNoInput";
export const 货品搜索id = "searchValInput";
export const 入库数量id = "inStockCountInput";
export const 出库数量id = "outStockCountInput";


export enum SEARCH_TYPE_OPTIONS {
	货品规格编码 = 'skuOuterId',
	条形码 = "barCode",
	货号 = "itemNo",
	货位 = 'warehouseSlotName'
}

export enum SEARCH_TYPE_OPTIONS_NOWAREHOUSE {
	货品规格编码 = 'skuOuterId',
	条形码 = "barCode",
	货号 = "itemNo",
}

export const 采购扫描设置字典值 = "PURCHASE_SCAN";


