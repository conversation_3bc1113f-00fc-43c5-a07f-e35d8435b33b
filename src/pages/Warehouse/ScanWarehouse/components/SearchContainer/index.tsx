
import React, { ChangeEvent, useEffect, useMemo, useRef, useState } from "react";
import { observer } from "mobx-react";
import { Button, Checkbox, Form, Tooltip, Input, Space, Select, Modal, Radio } from "antd";
import { CloseCircleFilled, CloseCircleOutlined, QuestionCircleFilled, QuestionCircleOutlined, SettingOutlined } from "@ant-design/icons";
import _ from "lodash";
import message from '@/components/message';
import Icon from '@/components/Icon';
import s from "./index.module.scss";
import scanWarehouseStore from "@/stores/warehouse/scanWarehouse";
import event from '@/libs/event';
import EnumSelect, { EnumStringSelect } from "@/components/Select/EnumSelect";
import { TradeDictInsertDictApi } from "@/apis/trade/search";
import { SysItemGetSysItemListApi } from "@/apis/warehouse/system";
import { SysItemGetSysItemListRequest } from "@/types/schemas/warehouse/system";
import useGetState from "@/utils/hooks/useGetState";
import { SEARCH_TYPE_OPTIONS, SEARCH_TYPE_OPTIONS_NOWAREHOUSE, 入库数量id, 开启状态, 扫描设置, 货品搜索id, 采购单号id, 采购扫描设置字典值, 采购状态 } from "../../constant";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { local } from "@/libs/db";
import 匹配采购单成功 from '@/assets/mp3/匹配采购单成功.mp3';
import 匹配采购单失败 from '@/assets/mp3/匹配采购单失败.mp3';
import { playAudio } from "@/pages/Trade/ScanPrint/utils";

const searchInitValue = {
	searchType: SEARCH_TYPE_OPTIONS.货品规格编码
	// checkGoodsType: "ARRIVAL_OF_GOODS_AND_COUNT"
};

const SearchTypeLocal = "scanWarehouse_searchType";

interface ISearchContainerProps {
	// loading: boolean;
}

export const focusInput = (e) => {
	let el = document.getElementById(e) as HTMLInputElement;
	el?.focus();
	el?.select();
};

const SearchContainer = observer((props:ISearchContainerProps) => {
	const [form] = Form.useForm();
	const [inStockForm] = Form.useForm();
	const { 
		getStockCurrentDetailList, 
		setPurchaseList,
		purchaseList,
		currentPurchaseInfo, 
		setCurrentDetailList, 
		currentDetailInfo,
		setCurrentDetailInfo,
		getScanSetting,
		scanSetting,
		setScanSetting,
		scanMsg,
		getNoStockCurrentDetailList,
		setScanMsg
	} = scanWarehouseStore;
	const [purchaseNoLoading, setPurchaseNoLoading] = useState(false);
	const [searchValLoading, setSearchValLoading] = useState(false);

	useEffect(() => {
		const searchType = local.get(SearchTypeLocal);
		if (searchType) form.setFieldsValue({ searchType });
		getScanSetting();
		event.on('ScanWarehouse.handleReset', handleReset);
		return () => {
			handleReset();
			event.off('ScanWarehouse.handleReset', handleReset);
		};
	}, []);

	useEffect(() => {
		if (scanSetting[扫描设置.无采购单入库模式] == 开启状态.开启) {
			form.setFieldsValue({ [扫描设置.无采购单入库模式]: 开启状态.开启 });
		} else {
			form.setFieldsValue({ [扫描设置.无采购单入库模式]: 开启状态.关闭 });
		}
	}, [scanSetting]);

	useEffect(() => {
		if (!currentDetailInfo.id) return;
		// 只要有默认值，就取默认值
		if (scanSetting[扫描设置.入库数量] > 0) {
			form.setFieldsValue({ willInStockCount: scanSetting[扫描设置.入库数量] });
			setTimeout(() => {
				handleAdd();
			}, 200);
			return;
		}
		if (scanSetting[扫描设置.无采购单入库模式] === 开启状态.关闭) {
			const { itemCount, instockCount, willInStockCount = 0 } = currentDetailInfo;
			form.setFieldsValue({ willInStockCount: (itemCount - instockCount - willInStockCount) || 0 });
		}
		focusInput(入库数量id);
	}, [currentDetailInfo]);

	const handleAdd = () => {
		sendPoint(Pointer.库存_采购入库_扫描入库_确认入库_添加);
		try {
			const val = form.getFieldsValue();
			const { currentDetailInfo, purchaseList } = scanWarehouseStore;
			const { sysItemCost = currentDetailInfo.costPrice || 0, willInStockCount = 1, searchType, purchaseNo, searchVal } = val;
			if (!currentDetailInfo.id || !searchVal || scanMsg?.message) {
				message.error("请先搜索到货品信息再进行添加");
				return;
			}
			if (willInStockCount < 0) {
				message.error("入库数量不能小于0");
				return;
			}
			if (val[扫描设置.无采购单入库模式] === 开启状态.开启) {
				const { sysSkuId, isCombination } = currentDetailInfo;
				console.log('currentDetailInfo:', currentDetailInfo);
				if (isCombination) {
					setScanMsg({ type: "fail", message: "不支持创建组合货品进行入库，请重新输入", source: 货品搜索id });
					focusInput(货品搜索id);
					return;
				}
				let index = purchaseList.findIndex((item) => (item.sysSkuId === sysSkuId));
				if (index > -1) {
					const _willInStockCount = +willInStockCount + +purchaseList[index].willInStockCount || 0;
					purchaseList[index] = {
						...purchaseList[index],
						sysItemCost,
						willInStockCount: _willInStockCount
					};
				} else {
					purchaseList.push({
						...currentDetailInfo,
						sysItemCost,
						willInStockCount
					});
				}
				setPurchaseList([...purchaseList]);
				formReset();
				setTimeout(() => {
					focusInput(货品搜索id);
				}, 100);
			} else {
				const { sysSkuId, itemCount, willInStockCount: itemWillInStockCount = 0, instockCount } = currentDetailInfo;
				const _willInStockCount = +willInStockCount + +itemWillInStockCount;
				if (Number(itemCount) < (+_willInStockCount + +instockCount)) {
					message.error("入库数量不能大于采购数量");
					return;
				}
				let index = purchaseList.findIndex((item) => (item.sysSkuId === sysSkuId));
				if (index > -1) {
					purchaseList[index] = {
						...purchaseList[index],
						willInStockCount: _willInStockCount
					};
				}
				message.success("添加成功");
				setPurchaseList([...purchaseList]);
				formReset({ purchaseNo, searchType });
				setTimeout(() => {
					focusInput(货品搜索id);
				}, 100);
				// focusInput(货品搜索id);
			}
		} catch (error) {
			console.log('error::', error);
		}
	};

	const handleReset = () => {
		setPurchaseList([]);
		setCurrentDetailInfo({});
		setCurrentDetailList([]);
		formReset();
		const noPurchaseNoModel = form.getFieldValue(扫描设置.无采购单入库模式);
		if (noPurchaseNoModel === 开启状态.开启) focusInput(货品搜索id);
		else focusInput(采购单号id);
	};
	
	// 根据采购单号获取采购单详情
	const onSearchPurchaseNo = async() => {
		let val = form.getFieldValue("purchaseNo");
		if (purchaseNoLoading) return;
		setPurchaseNoLoading(true);
		val = val.trim();
		if (!val) {
			setPurchaseNoLoading(false);
			message.warning("请输入采购单号");
			return;
		}
		formReset({ purchaseNo: val });
		const res = await scanWarehouseStore.getPurchaseList({ purchaseNo: val });
		setPurchaseNoLoading(false);
		if (res) {
			focusInput(货品搜索id);
			playAudio(匹配采购单成功);
		} else {
			focusInput(采购单号id);
			playAudio(匹配采购单失败);
		}
	};

	const onSearchVal = async(val) => {
		console.log('%c [ val ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', val);
		try {
			if (searchValLoading) return;
			const noPurchaseNoModel = form.getFieldValue(扫描设置.无采购单入库模式);
			setScanMsg({});
			let searchType = form.getFieldValue("searchType");
			val = val.trim();
			if (!val) {
				message.error("请输入");
				return;
			}
			// 无采购单入库模式
			if (noPurchaseNoModel === 开启状态.开启) {
				setSearchValLoading(true);
				await getNoStockCurrentDetailList({ val, searchType });
				setSearchValLoading(false);
			} else {
				getStockCurrentDetailList({ val, type: searchType });
			}
		} catch (error) {
			setSearchValLoading(false);
		}
		
		
	};
	// 切换有无采购单模式
	const changeScanSetting = (value, type) => {
		let changeParam = {};
		changeParam[type] = value;
		const _params = { ...scanSetting, ...changeParam };
		setScanSetting({ ..._params });
		if (type !== 扫描设置.入库数量) {
			handleReset();
		}
		return TradeDictInsertDictApi({ userDictEnum: 采购扫描设置字典值, value: JSON.stringify(_params) });
	};

	const searchValDisabled = useMemo(() => {
		const noPurchaseNoModel = scanSetting[扫描设置.无采购单入库模式];
		// 无采购单模式不需要禁止
		if (noPurchaseNoModel === 开启状态.开启) return false;
		// 采购单模式 全部入库 扫描采购单后报错  返回列表为空 需要禁止
		return (
			currentPurchaseInfo.purchaseStatus === 采购状态.全部入库 
			|| !purchaseList.length
			|| scanMsg?.source === 采购单号id
		);
	}, [currentPurchaseInfo, scanMsg, purchaseList, scanSetting]);


	const handleEditInStockCount = async() => {
		const inStockNum = scanSetting[扫描设置.入库数量];
		let inStockFormInitVal = {};
		if (inStockNum > 0) {
			inStockFormInitVal = {
				isHasDefaultVal: 1,
				inStockNum
			};
		} else {
			inStockFormInitVal = {
				isHasDefaultVal: 0
			};
		}
		inStockForm.setFieldsValue(inStockFormInitVal);
		Modal.confirm({
			title: '入库数量默认值',
			width: 450,
			centered: true,
			content: (
				<>
					<Form className="r-mt-18" form={ inStockForm }>
						<Form.Item label="" name="isHasDefaultVal" >
							<Radio.Group >
								<Radio type="radio" value={ 0 } >无默认值（可自行输入，适用批量入库场景）</Radio>
								<Radio type="radio" value={ 1 }>
									<div className="r-flex r-mt-16">
										<span className="r-mr-6">有默认值</span>
										<Form.Item name="inStockNum">
											<Input 
												style={ { width: "120px" } }
												type="number"
												size="small"
											/>
										</Form.Item>
									</div>
									
								</Radio>
							</Radio.Group>
						</Form.Item>
					</Form>
				</>
			),
			async onOk() {
				try {
					const { isHasDefaultVal, inStockNum = 0 } = inStockForm.getFieldsValue();
					if (isHasDefaultVal) {
						await changeScanSetting(inStockNum, 扫描设置.入库数量);
						form.setFieldsValue({ willInStockCount: inStockNum });
					} else {
						await changeScanSetting(0, 扫描设置.入库数量);
						form.setFieldsValue({ willInStockCount: "" });
					}
					inStockForm.resetFields();
					return Promise.resolve();
				} catch (error) {
					return Promise.reject();
				}
				
			},
			onCancel() {
				inStockForm.resetFields();
			}
		});
	};

	const formReset = (resetObj = {}) => {
		const searchType = local.get(SearchTypeLocal) || searchInitValue.searchType;
		const noPurchaseNoModel = form.getFieldValue(扫描设置.无采购单入库模式);
		form.resetFields();
		form.setFieldsValue({ searchType, ...resetObj, [扫描设置.无采购单入库模式]: noPurchaseNoModel });
	};

	const searchValAutoFocus = useMemo(() => {
		const noPurchaseNoModel = scanSetting[扫描设置.无采购单入库模式];
		// if (noPurchaseNoModel === 开启状态.开启) return true
		return !!(noPurchaseNoModel === 开启状态.关闭 && purchaseList.length);
	}, [purchaseList, scanSetting]);

	const handleSearchTypeChange = (e) => {
		local.set(SearchTypeLocal, e);
	};

	const handleFormChange = (changedValues) => {
		if (changedValues[扫描设置.无采购单入库模式]) {
			changeScanSetting(changedValues[扫描设置.无采购单入库模式], 扫描设置.无采购单入库模式);
		} 
		if (changedValues[扫描设置.无采购单入库模式] === 2 && SEARCH_TYPE_OPTIONS.货位 === local.get(SearchTypeLocal)) {
			handleSearchTypeChange(SEARCH_TYPE_OPTIONS_NOWAREHOUSE.货品规格编码);
			 // 更新表单值
			 form.setFieldsValue({ searchType: SEARCH_TYPE_OPTIONS_NOWAREHOUSE.货品规格编码 });
		}
	};

	const purchaseModeOpen = useMemo(() => (scanSetting[扫描设置.无采购单入库模式] == 开启状态.关闭), [scanSetting]);

	return (
		<div className={ s["search-container"] }>
			<Form
				initialValues={ searchInitValue }
				form={ form }
				onValuesChange={ handleFormChange }
				size="small"
			>
				<Form.Item label="采购单选项" name={ 扫描设置.无采购单入库模式 } >
					<Radio.Group>
						<Radio type="radio" value={ 开启状态.关闭 } >有采购单</Radio>
						<Radio type="radio" value={ 开启状态.开启 } >无采购单</Radio>
					</Radio.Group>
				</Form.Item>
				{
					purchaseModeOpen
						? (
							<>
								<Form.Item label="" name="purchaseNo">
									<Input suffix={ null } autoFocus key={ 采购单号id } id={ 采购单号id } size="middle" style={ { width: 280 } } onPressEnter={ onSearchPurchaseNo } placeholder="请输入采购单号" loading={ purchaseNoLoading } />
								</Form.Item>
								<Form.Item label="" colon={ false }>
									<Button type="primary" size="middle" onClick={ () => { onSearchPurchaseNo(); } } >查询</Button>
								</Form.Item>
							</>
						) : null
				}
				
				<Form.Item label="核验商品请扫描" />
				<div className="r-flex" style={ { position: "relative" } }>
					<Form.Item name="searchType">
						<EnumStringSelect 
							size="middle" 
							onChange={ handleSearchTypeChange } 
							enum={ purchaseModeOpen ? SEARCH_TYPE_OPTIONS_NOWAREHOUSE : SEARCH_TYPE_OPTIONS } 
							style={ { width: 114 } } 
							allowClear={ false } 
						/>
					</Form.Item>
					<Form.Item name="searchVal">
						<Input.Search
							autoFocus={ searchValAutoFocus }
							key={ 货品搜索id }
							id={ 货品搜索id } 
							disabled={ searchValDisabled } 
							loading={ searchValLoading }
							size="middle"
							placeholder="请输入" 
							style={ { width: 166 } }
							onSearch={ onSearchVal }
							onPressEnter={ (e) => { onSearchVal(e.currentTarget.value); } } // 监听回车，确保扫码枪触发
						/>
					</Form.Item>
				</div>
				<div className="r-flex r-mb-16 r-ai-c r-jc-sb">
					<Form.Item name="willInStockCount" style={ { marginBottom: 0 } }>
						<Input 
							placeholder="入库数量" 
							size="middle"
							id={ 入库数量id } 
							style={ { width: 280 } }
							disabled={ !currentDetailInfo?.id 
										|| !form.getFieldValue("searchVal")
										|| scanMsg?.message } 
							onPressEnter={ () => { handleAdd(); } }
						/>
					</Form.Item>
					<SettingOutlined onClick={ () => { handleEditInStockCount(); } } />
				</div>
				

				<Form.Item label=" " colon={ false } >
					<Button type="primary" size="middle" onClick={ () => { handleAdd(); } } >添加</Button>
					<Button className="r-ml-24" onClick={ () => { handleReset(); } } size="middle">重置</Button>
				</Form.Item>
			</Form>
			{
				scanMsg?.type === "fail" && scanMsg?.message ? (
					<div className={ `${s.errorMsgContainer} r-mt-24` }><CloseCircleFilled className="r-mr-4" style={ { color: "#F5222D" } } />{scanMsg?.message}</div>
				) : null
			}
			
		</div>
		
	);
});
export default SearchContainer;
