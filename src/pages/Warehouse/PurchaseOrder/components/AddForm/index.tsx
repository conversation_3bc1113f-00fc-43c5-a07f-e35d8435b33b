import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Input, Select, Form, Modal, Space, Button, Row, Col } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { observer } from 'mobx-react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import cs from 'classnames';
import { useRequest } from 'ahooks';
import Tabs from '@/components/Tabs';
import ProductListModal from "@/components-biz/Product/List/Model";
import SupplierDialog from '@/pages/Warehouse/Supplier/components/SupplierDialog';
import { SupplierAllApi } from '@/apis/warehouse/Supplier';
import { SysSkuGetSysSkuListApi } from '@/apis/warehouse/PurchaseOrder';
import { SupplierAllResponse } from '@/types/schemas/warehouse/Supplier';
import { SysSkuGetSysSkuListRequest, SysSkuGetSysSkuListResponse } from '@/types/schemas/warehouse/PurchaseOrder';
import InputPrice from '@/components/Input/InputNumber/InputPrice';
import warehouseStore from '@/stores/warehouse';
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import CombineTag from '@/components-biz/Trade/CombineTag';
import SupplierSelect from '@/components-biz/SupplierSelect';

const { TabPane } = Tabs;
const { confirm } = Modal;
const { Option } = Select;
interface AddFormProps {
	tableList: any[],
	addTableLine: (data: any) => void,
	status: string,
	purchaseData?: any,
	location:any
	sysSkuIds?: string[],
	actKey: string
}

export interface AddFormRefProps{
	setAddFormActiveKey:(arg0: string)=>void;
	refreshSkuList : () => void,
	getAddFormData : ()=>void,
	resetForm:()=>void,
}
const InternalForm = (props:AddFormProps, addFormRef: React.Ref<AddFormRefProps>, ref:any) => {
	const { tableList, addTableLine, status, purchaseData, sysSkuIds, actKey } = props;
	const [form] = useForm();
	const [skusList, setSkusList] = useState<SysSkuGetSysSkuListResponse['data']['list']>([]);
	const [currentSku, setCurrentSku] = useState<SysSkuGetSysSkuListResponse['data']['list'][0]>({});
	const [skuSearchValue, setSkuSearchValue] = useState('');
	const [disableList, setDisableList] = useState([]);
	const [activeKey, setActiveKey] = useState('1');
	const [supplierName, setSupplierName] = useState<any>({});
	const { refreshSupplierList, getSupplierList } = warehouseStore;

	const { run: getSysSkuList } = useRequest(() => {
		return SysSkuGetSysSkuListApi({ searchKey: skuSearchValue, pageNo: 1, pageSize: 200 });
	}, {
		manual: true,
		debounceWait: 500,
		onSuccess: (res: any, params: any[]) => {
			const { list = [] } = res;
			setSkusList(list);
		}
	});

	useImperativeHandle(addFormRef, () => ({
		refreshSkuList: () => {
			return getSysSkuList();
		},
		getAddFormData: () => {
			const { purchaseName } = form.getFieldsValue();
			const { market, stall, id, storageAddr, storageAddrCity, storageAddrDistrict, storageAddrProvince } = supplierName;
			return { supplierId: id, supplierName: supplierName.supplierName, purchaseName, market, stall, storageAddr, storageAddrCity, storageAddrDistrict, storageAddrProvince };
		},
		resetForm: () => {
			setActiveKey('1');
			setCurrentSku({});
			setSkuSearchValue('');
			form.resetFields();
		},
		setAddFormActiveKey: (key:string) => {
			setActiveKey(key);
		}
	}));

	const tabsCallback = (key: any) => {
		setActiveKey(key);
	};
	// 供应商下拉框变更
	const handleSupplierChange = (supplier) => {
		const { supplierName = "", id = "" } = supplier;
		// 修改采购商名称
		if (supplierName || id) {
			form.setFieldsValue({
				purchaseName: (supplierName || id) + '-' + dayjs().format('YYYY-MM-DD')
			});
		} else {
			form.setFieldsValue({
				purchaseName: ""
			});
		}

	};

	// 货品简称下拉框变更
	const skusListChange = (obj: { key: any, label: any, value: any }) => {
		// 存储sku信息
		setCurrentSku(skusList.filter(el => el.sysSkuId === obj.value)[0] || {});
	};

	//  重置
	const resetForm = () => {
		form.setFieldsValue({
			...form.getFieldsValue(),
			itemCount: undefined,
			sysItemCost: undefined,
			sysItemAlias: undefined,
			sysItemMemo: undefined,
		});
	};

	//  表单添加
	const addForm = async() => {
		await form.validateFields();
		const { sysItemAlias, itemCount, sysItemCost, sysItemMemo } = form.getFieldsValue();
		let existingData = tableList.filter((el: any) => el.sysSkuId === sysItemAlias.value);
		if (existingData.length) {
			let count = existingData[0].itemCount;
			confirm({
				centered: true,
				title: '系统提示',
				icon: <ExclamationCircleOutlined />,
				content: `您已经添加${count ? count + '件' : ''}相同单位的货品，请直接修改已添加的货品数量，或者先删除已添加的货品`,
			});
			return;
		}
		const { sysItemId, sysItemName, sysSkuId, sysSkuName, sysSkuAlias, outerId, picUrl, skuOuterId, costPrice, itemNo, isCombination } = currentSku;
		let newData = {
			sysItemId, // 货品id
			sysItemName, //	货品名称
			sysItemAlias: currentSku.sysItemAlias, //	货品别名
			sysSkuId, //	规格id
			sysSkuName, //	规格名称
			sysSkuAlias, //	规格别名
			picUrl, //	规格图片
			skuOuterId, //	规格商家编码
			itemNo, //	货号
			itemCount, //	采购数量
			costPrice, //	原始规格成本价
			sysItemMemo, // 规格备注(特殊说明)
			instockCount: 0, //	已入库数量
			sysItemCost, //	现有规格成本价
			isCombination,
			outerId,
		};
		// 更新表格数据
		addTableLine([newData]);
		// 重置form表单数据
		resetForm();
	};

	// 货品规格搜索
	const getSkuSearch = (value: string) => {
		setSkuSearchValue(value);
	};

	const checkSelectedRows = () => {
		let hasSkuIdList:any[] = [];
		tableList.forEach(item => {
			hasSkuIdList.push(item.sysSkuId);
		});
		return hasSkuIdList;
	};

	//	渲染表格
	const renderTableList = (selectRows:any) => {
		console.log('selectRows', selectRows);
		const selectedList = checkSelectedRows();
		// 有可能数据会重复
		const sysSkuList = selectRows.reduce(function(a:any, b:any) {
			// 判断是否已经选择过
			let hasSelect = selectedList.includes(b.sysSkuList[0].sysSkuId);
			if (!hasSelect) {
				return a.concat(b.sysSkuList);
			} else {
				console.log('已经选择过了:', b);
				return a;
			}

		}, []);
		console.log('sysSkuList', sysSkuList);
		const newtableLine = sysSkuList.map((item:any) => {
			const { sysItemAlias, sysItemId, sysItemName, sysSkuId, sysSkuName, sysSkuAlias, outerId, picUrl, skuOuterId, costPrice, itemNo, isCombination, itemCount = '' } = item;
			return {
				sysItemId, // 货品id
				sysItemName, //	货品名称
				sysItemAlias: currentSku.sysItemAlias || sysItemAlias, //	货品别名
				sysSkuId, //	规格id
				sysSkuName, //	规格名称
				sysSkuAlias, //	规格别名
				picUrl, //	规格图片
				skuOuterId, //	规格商家编码
				itemNo, //	货号
				itemCount, //	采购数量
				costPrice, //	原始规格成本价
				sysItemMemo: '', // 规格备注(特殊说明)
				instockCount: 0, //	已入库数量
				sysItemCost: costPrice, //	现有规格成本价
				isCombination,
				outerId,
			};
		});
		// 更新表格数据
		addTableLine(newtableLine);
		setDisableList(tableList.map(item => (item.sysSkuId)));
	};

	// 添加货品表单字段
	const FieldList = [
		{
			name: 'supplierName',
			label: '供应商名称',
			children: (
				<SupplierSelect
					valuePropName="supplierName"
					allowClear
					onChange={ handleSupplierChange }
				/>
			),
			// rules: [{ required: true, message: '供应商不能为空' }],
		},
		{
			name: '添加供应商',
			isForm: false,
			children: (
				<Button className={ cs('r-ml-8') } type="link">
					<SupplierDialog
						beforeOk={ () => {
							refreshSupplierList();
						} }
						triggerNode={ <div>添加供应商</div> }
					/>
				</Button>
			)
		},
		{
			name: 'purchaseName',
			label: '采购单名称',
			colSpan: 24,
			labelCol: { span: 4 },
			wrapperCol: { span: 20 },
			children: <Input placeholder="请填写采购单名称" maxLength={ 100 } />,
			rules: [{ required: true, message: '采购单名称不能为空' }],

		},
		{
			name: 'sysItemAlias',
			label: '货品简称',
			children: (
				<Select
					showSearch
					className={ cs('r-mr-8') }
					labelInValue
					onChange={ skusListChange }
					onBlur={ () => { getSkuSearch(''); } }
					optionFilterProp="children"
					dropdownMatchSelectWidth={ false }
					onSearch={ (value: string) => {
						getSkuSearch(value);
					} }
					placeholder="选择货品"
				>
					{skusList.map((item: any) => (
						<Option key={ item.sysSkuId } value={ item.sysSkuId }>
							<CombineTag visible={ item.isCombination === 1 } />{item.sysItemAlias}-{item.sysSkuName}-{item.skuOuterId}
						</Option>
					))}
				</Select>
			),
			rules: [{ required: true, message: '货品不能为空' }],
		},
		{
			label: '单位',
			children: (
				<Input value="件" disabled maxLength={ 100 } />
			),
		},
		{
			name: 'itemCount',
			label: '数量',
			children: <InputPrice precision={ 0 } />,
			rules: [{ required: true,
				validator: (_:any, value:any, callback:()=>void) => {
					console.log('value', value);
					if (value === '0' || value === 0) {
						return Promise.reject(new Error('数量不能为0'));
					}
					if (!value) {
						return Promise.reject(new Error('数量不能为空'));
					}
					return Promise.resolve();
				}
			}],
		},
		{
			name: 'sysItemCost',
			label: '成本价',
			children: <InputPrice />,
			rules: [{
				//  required: true,
				// message: '成本价不能为空'
			}],
		}
	];

	useEffect(() => {
		setActiveKey(actKey);
	}, [actKey]);
	// 单品添加
	const singleAdd = (
		<Form className={ cs('r-mt-16') } form={ form } labelCol={ { span: 8 } } wrapperCol={ { span: 16 } } size="middle">
			<Row>
				<Col span={ 18 }>
					<Row>
						{
							FieldList.map(({ children, isForm, ...reset }, i) => (
								<Col key={ String(reset.name) } span={ reset.colSpan || 12 }>
									<Form.Item
										labelCol={ reset.labelCol || { span: 8 } }
										wrapperCol={ reset.wrapperCol || { span: 16 } }
										{ ...reset }
									>
										{children}
									</Form.Item>
								</Col>
							))
						}
					</Row>
				</Col>
			</Row>
			<Row>
				<Col span={ 18 }>
					<Form.Item label="备注" labelCol={ { span: 4 } } wrapperCol={ { span: 20 } } name="sysItemMemo">
						<Input maxLength={ 110 } placeholder="" />
					</Form.Item>
				</Col>
				<Col offset={ 1 } span={ 4 }>
					<Space>
						<Button data-point={ Pointer["采购_采购单_新建采购单_ 新建采购单_添加"] } onClick={ addForm } type="primary">添加</Button>
						<Button onClick={ resetForm }>清空</Button>
					</Space>
				</Col>
			</Row>
		</Form>
	);

	const onFieldsFormChange = (e) => {
		const { supplierName = null } = e;
		if (supplierName) {
			setSupplierName(supplierName);
		}
	};
	// 批量添加
	const batchAdd = (
		<Form className={ cs('r-mt-16') } form={ form } onValuesChange={ (e) => { onFieldsFormChange(e); } } labelCol={ { span: 3 } } wrapperCol={ { span: 6 } } size="middle">
			<Row>
				<Col span={ 18 }>
					<Row>
						{FieldList.filter(el => ['supplierName', 'purchaseName', '添加供应商'].includes(el.name)).map(({ children, isForm, ...reset }, i) => (
							<Col key={ String(reset.name) } span={ 12 }>
								<Form.Item
									{ ...reset }
									labelCol={ { span: 8 } }
									wrapperCol={ { span: 16 } }
								>
									{children}
								</Form.Item>
							</Col>
						))}
						{/* 批量添加商品 */}
						<Col key="批量添加商品" span={ 12 }>
							<Button data-point={ Pointer["采购_采购单_新建采购单_新建采购单_批量添加"] } className={ cs('r-ml-8') } type="primary">
								<ProductListModal
									showSave
									disabled={ { key: 'sysSkuId', value: disableList } }
									onOk={ renderTableList }
									supplier={ supplierName }
									disabledCombined={ { isDisable: true, tips: "采购单不支持创建组合货品进行采购" } }
									hasBatchedNum
								>批量添加商品
								</ProductListModal>
							</Button>
						</Col>
					</Row>
				</Col>
			</Row>
		</Form>
	);


	useEffect(() => {
		if (sysSkuIds && sysSkuIds.length > 0) {
			setActiveKey('2');
		}
	}, [sysSkuIds]);
	useEffect(() => {
		getSupplierList();
	}, []);

	useEffect(() => {
		getSysSkuList();
	}, [getSysSkuList, skuSearchValue]);

	useEffect(() => {
		if (status === 'copy' && purchaseData) {
			form.setFieldsValue({
				...form.getFieldsValue(),
				purchaseName: purchaseData.purchaseName + '(复制)',
				supplierName: purchaseData?.supplierName || undefined
			});
			setSupplierName({
				id: purchaseData?.id, 
				supplierName: purchaseData?.supplierName,
			});
		}

	}, [form, status, purchaseData]);

	useEffect(() => {
		setDisableList(tableList.map(item => (item.sysSkuId)));
	}, [tableList]);
	return (
		<>
			{ batchAdd }
		</>
	);
};
const AddForm = React.forwardRef(InternalForm);
export default observer(AddForm);
