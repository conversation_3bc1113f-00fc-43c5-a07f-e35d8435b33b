import React, { useEffect, useState, useCallback } from 'react';
import { Drawer, Button, Form, Input, Select, Modal } from 'antd';
import { useForm } from "antd/lib/form/Form";
import { ColumnsType } from 'antd/es/table';
import { CloseOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import _ from 'lodash';
import { observer } from 'mobx-react';
import BaseTable from '@/components/SearchTable/BaseTable';
import { getModalTableScrollHeight } from '@/utils/util';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import { OrderSourceTypePlain } from "@/pages/Trade/components/SearchContainer/SearchCondition";
import { getAllPlats, getMultiShops, getShopName, isSourceScm, isAfterSaleSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
// import DropdownButton from '@/components/DropdownButton';
import message from "@/components/message";
import {
	BatchPrintTypeEnum,
	packageExceptionStatusColorMap,
	packageExceptionStatusMap,
	packagePickStatusColorMap,
	packagePickStatusMap,
	printStatusMap, printStatusColorMap,
	packageStatusColorMap, packageStatusMap,
	tickedOutOptions,
	wave_waveStatusListOptions
} from '../../constants';
import { checkPackageExceptions, checkTemplateAndWaybill } from '../../utils';
import userStore from '@/stores/user';
import { tradeStore } from "@/stores";
import waveStore from "@/stores/warehouse/Wave";
import event from '@/libs/event';
import { local } from "@/libs/db";
import { TradeWaveQueryPickPackageDetailsApi, TradePickExcludeWaveApi } from '@/apis/warehouse/wave';
import PrintCenterAPI from '@/print/index';
import { pageLoading } from '@/components/PageLoading';
import s from './index.module.scss';
import history from "@/utils/history";
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';

interface WaveDetailDrawerProps {
	visible: boolean;
	onClose: () => void;
	waveNo: string;
	kddFjr?: any;
	wave?: any;
}

const defaultParams = {
	platformInfo: undefined,
	ptTid: undefined,
	tradeSource: undefined,
};

const WaveDetailDrawer: React.FC<WaveDetailDrawerProps> = ({
	visible,
	onClose,
	waveNo,
	kddFjr,
	wave = {}
}) => {
	const [form] = useForm();

	const { userInfo, shopList } = userStore;
	const { userId, subUserId } = userInfo || {};
	const { kddTempList, expressTemplateList } = tradeStore;
	const { refreshMultipleWaveInfo } = waveStore;

	const [loading, setLoading] = useState(false);
	const [kickOutLoading, setKickOutLoading] = useState(false); // 踢出波次状态
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [selectedRows, setSelectedRows] = useState<any[]>([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
	const [formData, setFormData] = useState<any>({ ...defaultParams });
	const [currentWave, setCurrentWave] = useState<any>(wave); // 当前波次

	const [operateBtnsDefault, setOperateBtnsDefault] = useState({
		printBtn: BatchPrintTypeEnum.打印快递单
	});

	const customLogPost = (dataType: string, data: any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `波次管理-波次详情: 【 ${dataType} 】`,
			data: {
				...data
			}
		});
	};

	// 模拟获取波次详情数据
	const fetchWaveDetail = useCallback(async() => {
		setLoading(true);
		try {
			const formValues = form.getFieldsValue();

			let params = {
				userId,
				waveNo,
				...formValues,
				ptTid: formValues?.ptTid?.trim(),
			};

			const platformInfo = formValues.platformInfo || form.getFieldValue('platformInfo');

			// 处理多店铺选择
			const { plat_sellerIds = [], plats = [] } = platformInfo || {};
			let multiShops = await getMultiShops({ plats, plat_sellerIds });
			multiShops && (params['multiShopS'] = multiShops);
			delete params.platformInfo;

			// 没有平台店铺
			if (!plats?.length && !plat_sellerIds?.length) {
				params['shopIsActualSelect'] = 0; // 页面是否实际勾选店铺查询 0否 1是
			} else {
				params['shopIsActualSelect'] = 1;
			}

			console.log('%c [ params ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);

			// 这里更新下波次数据
			const waveData = await refreshMultipleWaveInfo([waveNo]);
			console.log('%c [ waveData ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', waveData);
			if (waveData?.length) {
				setCurrentWave(waveData[0]);
			}

			const res = await TradeWaveQueryPickPackageDetailsApi(params);
			console.log('%c [ 波次详情 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
			if (res?.length > 0) {
				// 处理返回的数据
				const formattedData = res.map((item, index) => {
					return {
						...item,
						rowId: item.id,
					};
				});
				setDataSource(formattedData);
			} else {
				setDataSource([]);
			}
		} catch (error) {
			console.error('获取波次详情失败', error);
			setDataSource([]);
		} finally {
			setLoading(false);
		}
	}, [waveNo, userId, form]);

	const handleOptionChange = (fieldName, value) => {
		setOperateBtnsDefault(prev => {
			const newState = {
				...prev,
				[fieldName]: value
			};
			// 不需要在这里设置缓存，因为组件内部已经设置了

			return newState;
		});
	};

	const handleCancel = () => {
		onClose?.();
	};

	// 获取波次详情数据
	useEffect(() => {
		if (visible && waveNo) {
			form.setFieldsValue(defaultParams);
			setFormData(defaultParams);

			setTimeout(() => {
				fetchWaveDetail();
			}, 100);
		} else if (!visible) {
			// 清理选中的值
			setSelectedRows([]);
			setSelectedRowKeys([]);
			setDataSource([]);

			// 重置表单数据，确保下次打开时不会保留上次的筛选条件
			handleResetForm();
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [visible, waveNo, fetchWaveDetail]);

	// 监听路由变化，关闭抽屉
	useEffect(() => {
		if (!visible) return;

		const unlisten = history.listen(() => {
			onClose?.();
		});

		return unlisten;
	}, [visible]);

	// 搜索
	const handleSearch = () => {
		fetchWaveDetail();
	};

	const handleResetForm = () => {
		form.resetFields();
		setFormData(defaultParams);
	};

	// 重置
	const handleReset = () => {
		console.log('%c [ 重置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '重置');
		handleResetForm();	

		setTimeout(() => {
			fetchWaveDetail();
		}, 100);
	};

	// 表格列配置
	const columns: ColumnsType<any> = [
		{
			title: '序号',
			dataIndex: 'id',
			width: 44,
			render: (text, record, index) => index + 1
		},
		{
			title: '包裹号',
			dataIndex: 'packageNo',
			width: 120,
			minWidth: 100,
		},
		{
			title: '包裹状态',
			dataIndex: 'packageStatus',
			width: 100,
			minWidth: 70,
			render: (text) => {
				const statusText = packageStatusMap[text] || text;
				return <span style={ packageStatusColorMap[text] || {} }>{statusText}</span>;
			}
		},
		{
			title: '打印状态',
			dataIndex: 'printStatus',
			width: 100,
			minWidth: 70,
			render: (text) => {
				const statusText = printStatusMap[text] || text;
				return <span style={ printStatusColorMap[text] || {} }>{statusText}</span>;
			}
		},
		{
			title: '快递',
			dataIndex: 'exName',
			width: 140,
			minWidth: 100,
		},
		{
			title: '快递单号',
			dataIndex: 'exNumber',
			width: 140,
			minWidth: 100,
		},
		{
			title: '拣货状态',
			dataIndex: 'pickStatus',
			width: 100,
			minWidth: 70,
			render: (text) => {
				const statusText = packagePickStatusMap[text] || text;
				return <span style={ packagePickStatusColorMap[text] || {} } className={ s.tag }>{statusText}</span>;
			}
		},
		{
			title: '包裹异常',
			dataIndex: 'packageExceptionStatusList',
			width: 140,
			minWidth: 70,
			render: (text) => {
				if (!text || text.length === 0) return null;

				return (
					<div className="r-flex r-gap-4 r-fd-r r-fw-w">
						{text.map((item) => {
							// 获取异常状态的中文名称
							const exceptionStatus = packageExceptionStatusMap[item] || item;
							// 获取对应的样式
							const style = packageExceptionStatusColorMap[item] || { background: '#ff4d4f', color: '#fff' };

							return (
								<span key={ item } style={ style } className={ s.tag }>
									{exceptionStatus}
								</span>
							);
						})}
					</div>
				);
			}
		},
		{
			title: '是否踢出',
			dataIndex: 'tickedOut',
			width: 100,
			minWidth: 70,
			render: (text, record, index) => {
				return tickedOutOptions?.find(item => item.value == text)?.label;
			}
		},
		{
			title: '货品数量',
			width: 100,
			minWidth: 70,
			dataIndex: 'packageItemCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '货品种类',
			width: 100,
			minWidth: 70,
			dataIndex: 'packageItemTypeCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '计划拣货数',
			width: 100,
			minWidth: 70,
			dataIndex: 'packagePlannedPickCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '已拣数量',
			width: 100,
			minWidth: 70,
			dataIndex: 'packagePickCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '未拣数量',
			width: 100,
			minWidth: 70,
			dataIndex: 'packageWaitPickCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '缺货数量',
			width: 100,
			minWidth: 70,
			dataIndex: 'packageOutOfStockCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '退款数量',
			width: 100,
			minWidth: 70,
			dataIndex: 'packageRefundCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '订单号',
			width: 155,
			minWidth: 100,
			dataIndex: 'orderIds',
			render: (text, record, index) => {
				return text;
			}
		}
	];

	// 踢出波次
	const handleKickOut = () => {
		sendPoint(Pointer.波次管理_波次详情_剔出波次);
		if (selectedRowKeys.length === 0) {
			message.warning('请选择需要踢出的包裹');
			return;
		}

		// 检查是否有已踢出的包裹
		const alreadyKickedOut = selectedRows.filter(row => row.tickedOut === 'TICKED_OUT');

		if (alreadyKickedOut.length > 0) {
			Modal.confirm({
				centered: true,
				title: '包裹状态异常，不可踢出',
				content: '选中包裹中包含已踢出的包裹，请取消选择后重试',
				icon: <ExclamationCircleOutlined style={ { color: '#faad14' } } />,
				okText: '确定',
				cancelButtonProps: { style: { display: 'none' } }
			});
			return;
		}

		// 检查波次状态 - 使用传入的currentWave
		if (currentWave && (currentWave.waveStatus === 'COMPLETED' || currentWave.waveStatus === 'CANCELED')) {
			Modal.confirm({
				centered: true,
				title: '波次状态异常，不可踢出波次',
				content: '波次状态为「已完成」、「已取消」时不可踢出波次',
				icon: <ExclamationCircleOutlined style={ { color: '#faad14' } } />,
				okText: '确定',
				cancelButtonProps: { style: { display: 'none' } }
			});
			return;
		}

		// 确认对话框
		Modal.confirm({
			title: '请确认踢出这些订单?',
			icon: <ExclamationCircleOutlined style={ { color: '#FAAD14' } } />,
			content: (
				<div style={ { marginLeft: '0', marginTop: '8px' } }>
					<p>1.踢出波次后已拣货的货品请归还至货架上</p>
					<p>2.订单将标记为「已踢出」，同时订单将与该波次解绑</p>
				</div>
			),
			okText: '确定',
			cancelText: '取消',
			okButtonProps: {
				style: { backgroundColor: '#FF8000', borderColor: '#FF8000' }
			},
			onOk: async() => {
				try {
					setKickOutLoading(true);
					// const packageNos = selectedRows.map(row => row.packageNo);
					const boxNos = selectedRows.map(row => row.boxNo);

					const params = {
						userId,
						waveNo,
						// packageNoList: packageNos,
						boxNoList: boxNos
					};

					const res = await TradePickExcludeWaveApi(params);

					if (res) {
						message.success(`成功踢出 ${selectedRowKeys.length} 个包裹`);
						// 刷新数据
						fetchWaveDetail();
						// 清空选择
						setSelectedRowKeys([]);
						setSelectedRows([]);

						// 波次列表也更新下
						// event.emit('waveList.updateWaveList');
					} else {
						// 处理业务错误
						message.error('踢出波次失败，请稍后重试');
					}
				} catch (error) {
					console.error('踢出波次失败', error);

				} finally {
					setKickOutLoading(false);
				}
			}
		});
	};

	// 打印快递单
	const handleBatchPrint = () => {

	};

	// 混合打印
	const handleMixedPrint = () => {
		console.log('%c [ 混合打印 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', selectedRowKeys);
		sendPoint(Pointer.波次管理_波次详情_打印快递单);
		if (selectedRowKeys.length === 0) {
			message.warning('请选择需要打印的包裹');
			return;
		}

		// 前置过滤已踢出的包裹
		const filteredRows = selectedRows.filter(row => row.tickedOut !== 'TICKED_OUT');
        
		if (filteredRows.length === 0) {
			message.warning('没有可打印的有效包裹，所有选中包裹已被踢出');
			return;
		}

		// 检查波次状态是否允许打印
		if (currentWave && currentWave.waveStatus !== 'PICK_COMPLETED') {
			Modal.confirm({
				centered: true,
				icon: <ExclamationCircleOutlined style={ { color: '#faad14' } } />,
				title: '波次状态异常，不可打印',
				content: (
					<div>
						<p>波次状态仅为【拣货完成】时支持快递单打印</p>
						<p>{`${waveNo} — ${wave_waveStatusListOptions.find(option => option.value === currentWave.waveStatus)?.label || "待处理"}`}</p>
					</div>
				),
				okText: '确定',
				cancelButtonProps: { style: { display: 'none' } }
			});
			return;
		}

		// 所有包裹都正常，直接打印
		console.log('直接打印', filteredRows);
		startMixedPrint(filteredRows);

	};

	// 开始打印
	const startMixedPrint = async(validPackages) => {
		try {
			// 这里是单个波次
			const validWaves = [{
				...currentWave,
				pickPackageList: validPackages // 只保留有效的包裹
			}];

			// 打印前判断模板和单号异常
			const checkedWaves = await checkTemplateAndWaybill(validWaves, 'print', setSelectedRowKeys, setSelectedRows, customLogPost, 'package', expressTemplateList);

			// 走包裹异常判断逻辑 （不走通用异常拦截逻辑）
			// 检查包裹异常
			const filteredWaves = await checkPackageExceptions(
				checkedWaves,
				'print',
				customLogPost,
				setSelectedRowKeys,
				setSelectedRows,
				'package' // 指定为包裹维度
			);

			let invalidTids = []; // 假设这里是被过滤掉的异常的订单号
			onHandlePrint(filteredWaves, invalidTids);
		} catch (error) {
			console.log('打印前检查失败或用户取消', error);
		}
	};

	// 过滤异常包裹数据（和波次的有点不一样）
	const formatTradeData = (validWaves, invalidTids) => {
		if (invalidTids?.length) {
			let orderList = [];
			let packagesToFilter = []; // 存储需要过滤掉的包裹

			// 从validWaves中提取订单数据，并过滤掉包含无效tid的包裹
			if (validWaves && validWaves.length > 0) {
				validWaves.forEach(wave => {
					if (wave.pickPackageList && wave.pickPackageList.length > 0) {
						// 过滤掉包含无效tid的包裹
						const validPackages = wave.pickPackageList.filter(pkg => {
							// 检查包裹中的所有订单项
							if (pkg.pickItemList && pkg.pickItemList.length > 0) {
								// 如果包裹中有任何一个订单在invalidTids中，则过滤掉整个包裹
								const shouldFilter = pkg.pickItemList.some(item => item.tid && invalidTids.includes(item.tid));

								// 如果需要过滤，将包裹添加到过滤列表
								if (shouldFilter) {
									packagesToFilter.push(pkg);
								}

								return !shouldFilter;
							}
							return true; // 如果没有pickItemList，保留该包裹
						});

						// 将有效包裹添加到orderList
						if (validPackages.length > 0) {
							// 添加整个波次数据，但只包含有效的包裹
							orderList.push({
								...wave,
								pickPackageList: validPackages // 只保留有效的包裹
							});
						}
					}
				});
			}

			// 如果有需要过滤的包裹，取消勾选
			if (packagesToFilter.length > 0) {
				const packagesToFilterIds = packagesToFilter.map(pkg => pkg.rowId);
				const remainingValidPackageIds = selectedRowKeys.filter(key => !packagesToFilterIds.includes(key));
				const remainingValidPackages = selectedRows.filter(pkg => !packagesToFilterIds.includes(pkg.rowId));

				// 更新选中状态
				setSelectedRowKeys(remainingValidPackageIds);
				setSelectedRows(remainingValidPackages);

				// 提示用户部分包裹被过滤
				if (packagesToFilter.length > 0) {
					console.log('%c [ 已过滤 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;',
						`已过滤 ${packagesToFilter.length} 个包含异常订单的包裹`);
				}
			}

			return orderList;
		} else {
			return validWaves;
		}
	};

	const onHandlePrint = (validWaves, invalidTids) => {
		// 这里需要把异常订单号所在包裹剔除掉

		const orderList = formatTradeData(validWaves, invalidTids);
		// 如果所有波次都无效，提示用户
		if (orderList.length === 0) {
			message.warning('没有可打印的有效包裹');
			return;
		}

		// 打印只需要包裹
		const printOrderList = orderList.flatMap(wave => {
			return wave.pickPackageList;
		});

		pageLoading.loading(false, 100);
		PrintCenterAPI.batchPrintWaveKdd({
			fjrInfoMap: {
				kddFjrMap: {
					isUseCommon: false,
					isUseMore: true,
					isKddBindFjr: true,
					senderInfo: kddFjr, // 发件人
				},
				// 模板绑定发件人
				userIdList: Array.from(new Set(
					printOrderList.map(pkg => pkg.sellerId).filter(Boolean)
				))
			},
			orderList: printOrderList,
			from: 'waveDetail'
		});
	};


	// 处理行选择变化
	const handleRowSelectionChange = (selectedRowKeysAct: number[], selectedRowsAct: any[]) => {
		setSelectedRowKeys(selectedRowKeysAct);
		setSelectedRows(selectedRowsAct);
	};

	// 行点击事件处理
	const handleRowClick = (e: React.MouseEvent, record: any) => {
		// 防止点击行内元素时触发行选择
		if ((e.target as HTMLElement).tagName.toLowerCase() !== 'td') {
			return;
		}

		let newSelectedRowKeys = [...selectedRowKeys];
		let newSelectedRows = [...selectedRows];

		if (newSelectedRowKeys.includes(record.rowId)) {
			// 取消选中
			newSelectedRowKeys = newSelectedRowKeys.filter(key => key !== record.rowId);
			newSelectedRows = newSelectedRows.filter(row => row.rowId !== record.rowId);
		} else {
			// 选中
			newSelectedRowKeys.push(record.rowId);
			newSelectedRows.push(record);
		}

		setSelectedRowKeys(newSelectedRowKeys);
		setSelectedRows(newSelectedRows);
	};

	// 表格行选择配置
	const rowSelection: any = {
		type: 'checkbox',
		columnWidth: 48,
		selectedRowKeys,
		onChange: handleRowSelectionChange,
		getCheckboxProps: (record: any) => ({
			// 可以在这里设置某些行禁用选择
			// disabled: record.someCondition
		})
	};

	const handleFormChange = (changedValues, allValues) => {
		console.log('%c [ 111 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues, allValues);

		setFormData(allValues);
	};

	

	// 监听打印完成事件
	useEffect(() => {
		// 监听波次详情打印完成事件
		const handleAfterPrint = (packageNoList) => {
			console.log('波次详情打印完成', packageNoList);

			// 刷新波次详情数据
			fetchWaveDetail();

			// 清空选择
			setSelectedRowKeys([]);
			setSelectedRows([]);
		};

		const handleAfterSend = ({ res, params, result, isPrintAutoSend }) => {
			console.log('波次详情发货完成', res, params, result, isPrintAutoSend);

			fetchWaveDetail();
		};
	
		// 添加事件监听
		if (visible) {
			event.off('waveDetail.afterPrint', handleAfterPrint);
			event.off('waveDetail.afterSend', handleAfterSend);
			event.on('waveDetail.afterPrint', handleAfterPrint);
			event.on('waveDetail.afterSend', handleAfterSend);
		}

		// 组件卸载或visible变为false时移除事件监听
		return () => {
			event.off('waveDetail.afterPrint', handleAfterPrint);
			event.off('waveDetail.afterSend', handleAfterSend);
		};
	}, [visible, fetchWaveDetail]);

	// 计算抽屉宽度：最小1200px，最大为屏幕宽度的83% -- 用css控制，不然还要监听屏幕变化
	// const getDrawerWidth = () => {
	// 	const screenWidth = window.innerWidth;
	// 	const calculatedWidth = Math.floor(screenWidth * 0.83);
	// 	return Math.max(1200, calculatedWidth);
	// };

	return (
		<Drawer
			title={ (
				<div className="r-flex r-ai-c r-jc-sb">
					<div className={ s.deawerTitle }>
						<span className="r-bold">波次详情</span>
						<span className={ s.waveNo }>【{waveNo}】</span>
					</div>
					<div onClick={ () => handleCancel() } className={ s.logClose }>
						<CloseOutlined size={ 14 } className={ s.close } />
					</div>
				</div>
			) }
			// width={ getDrawerWidth() }
			placement="right"
			size="large"
			closable={ false }
			keyboard={ false }
			maskClosable
			onClose={ onClose }
			visible={ visible }
			className={ s.waveDetailDrawer }
			destroyOnClose
			footer={ (
				<div className="r-flex r-gap-8 r-ai-c">
					<Button onClick={ onClose }>
						取消
					</Button>
					<Button onClick={ onClose } type="primary">
						确定
					</Button>
				</div>
			) }
			// getContainer={ () => document.getElementById('waveList') } // 如果不设置容器，跳转的时候不会关闭
		>
			<div className={ s.searchArea }>
				<Form
					form={ form }
					layout="inline"
					// initialValues={ { ...defaultParams } }
					onValuesChange={ handleFormChange }
				>
					<Form.Item name="platformInfo">
						<ShopMultiSelect
							bgHighLight
							style={ { width: 160 } }
							size="small"
							isHasHandPlat // 是否显示手工单平台
						/>
					</Form.Item>

					<Form.Item name="tradeSource">
						<OrderSourceTypePlain
							isSupplierAccount={ false }
							style={ { width: 160 } }
							size="small"
						/>
					</Form.Item>

					<Form.Item name="ptTid">
						<Input placeholder="订单编号" style={ { width: 160 } } size="small" className={ formData.ptTid ? 'high-light-bg' : '' } />
					</Form.Item>

					<Form.Item>
						<Button type="primary" onClick={ handleSearch } size="small">
							查询
						</Button>
					</Form.Item>
					<Form.Item>
						<Button onClick={ handleReset } size="small">
							重置
						</Button>
					</Form.Item>
				</Form>
			</div>

			<div className={ s.operationArea }>
				{/* <DropdownButton
					options={ [
						{
							key: BatchPrintTypeEnum.打印快递单,
							label: "打印快递单",
							onClick: () => handleBatchPrint()
						},
						{
							key: BatchPrintTypeEnum.混合打印,
							label: "混合打印",
							onClick: () => handleMixedPrint()
						}
					] }
					defaultKey={ operateBtnsDefault.printBtn }
					onOptionChange={ (key) => handleOptionChange('printBtn', key) }
					cacheKey="WarehouseWaveListDetailOperateBtnsDefault"
					userId={ userId }
					type="primary"
					size="middle"
					overlayClassName={ s.overlay }
					leftButtonStyle={ { width: 94 } }
				/> */}

				<Button
					className={ s.expandBtn }
					onClick={ _.debounce(() => handleMixedPrint(), 500, {
						leading: true,
						trailing: false
					}) }
					size="middle"
					type="primary"
				>
					打印快递单
				</Button>

				<Button
					className={ s.expandBtn }
					onClick={ _.debounce(() => handleKickOut(), 500, {
						leading: true,
						trailing: false
					}) }
					size="middle"
					// disabled={ selectedRowKeys.length === 0 }
					loading={ kickOutLoading }
				>
					踢出波次
				</Button>

			</div>

			<div className={ s.tableArea } >
				<BaseTable
					size="small"
					bordered
					loading={ loading }
					columns={ columns }
					dataSource={ dataSource }
					rowKey="rowId"
					pagination={ false }
					rowSelection={ rowSelection }
					innerTableStyle={ { padding: 0 } }
					onRow={ (record) => ({
						onClick: (e) => handleRowClick(e, record)
					}) }
					// scroll={ { y: scrollY } } // 不能填
					id="WaveDetailDrawer_Table"
					headerColSetId="WaveDetailDrawer_Table"
					scrollExtraHeight={ 0 }
				/>
			</div>
		</Drawer>
	);
};

export default observer(WaveDetailDrawer);