import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { observer } from 'mobx-react';
import { Button, Form, Input, Select, Tooltip, InputNumber, Dropdown, Menu, Modal } from "antd";
import { CloseOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useActivate } from 'react-activation';
import { useLocation } from 'react-router-dom';
import event from '@/libs/event';
import { local } from "@/libs/db";
import message from "@/components/message";
import { tradeStore } from "@/stores";
import userStore from '@/stores/user';
import waveStore from "@/stores/warehouse/Wave";
import SearchTable from "@/components/SearchTableVirtual";
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import { DatePickerKey, getCacheDateRange } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import InputMulti from "@/components/Input/InputMulti";
// import InputArrayMulti from '@/components/Input/InputArrayMulti';
import SelectMultiple from "@/pages/AfterSale/TradeList/components/SelectMultiple";
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import { KddExCodeSelect, KddExNameSelect, KddTempListSelect, KddTempTypeSelect } from '@/pages/Report/components/kddTempSelect';
import Icon from "@/components/Icon";
import memoFn from "@/libs/memorizeFn";
import useGetState from '@/utils/hooks/useGetState';
// import DropdownButton from '@/components/DropdownButton';
import {
	TradeWaveQueryWaveInfoPageListApi,
	TradeWaveCancelWaveApi,
	TradePickCancelWaveApi
} from '@/apis/warehouse/wave';
import {
	wave_timeTypeOptions,
	wave_waveStatusListOptions,
	wave_wavePrintStatusListOptions,
	BatchPrintTypeEnum,
	WaveSortInfo,
	SortType,
	waveStatusMap,
	waveStatusColorMap
} from './constants';
import { checkPackageExceptions, filterTickedOutPackage, checkWaveStatus, checkTemplateAndWaybill } from './utils';
import ThFilterWaveStatus from './components/ThFilterWaveStatus';
import ThFilterWavePrintStatus from './components/ThFilterWavePrintStatus';
import WavePickDetailModal, { WavePickDetailType } from './components/WavePickDetailModal';
import WaveDetailDrawer from './components/WaveDetailDrawer';
import PrintCenterAPI from '@/print/index';
import { pageLoading } from '@/components/PageLoading';
import { formatKddFjrUtil } from '@/utils/print/formatFjrData';
import s from "./index.module.scss";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";

const { Option } = Select;

// 默认最近1个月
const initialRangeTime = getCacheDateRange(DatePickerKey.warehouse_waveList) || [dayjs().subtract(1, 'M').startOf('day'), dayjs().endOf('day')];
const defaultParams = {
	timeType: 'gmtCreated',
	rangeTime: initialRangeTime,
	waveStatusList: [],
	wavePrintStatusList: [],
	exNumber: undefined,
	ptTid: undefined,
	pickUserName: undefined,
	waveNo: undefined,
	packageNo: undefined,
	exName: undefined,
	exCode: undefined,
};

/**
 * 波次打印
 * @returns 
 */
const WaveList = (props) => {
	const { userInfo, shopList, getShopList } = userStore;
	const { userId, subUserId } = userInfo || {};
	const { expressTemplateList, fjrInfoMap, kddTempList } = tradeStore;
	const {
		setWavePackageList,
		wavePackageList,
		batchWaveSendFromList,
		setWavePackageFilterType
	} = waveStore;

	const [form] = Form.useForm();
	const ref = useRef<any>();
	const location = useLocation();

	const [expressCompanyList, setExpressCompanyList, getExpressCompanyList] = useGetState([]);
	const [refuseRefundModalVisible, setRefuseRefundModalVisible] = useState(false);
	const [selectedRows, setSelectedRows] = useState<any[]>([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
	const [searchParams, setSearchParams] = useState({});
	const [loading, setLoading] = useState(false);
	const [cancelLoading, setCancelLoading] = useState(false); // 取消波次
	const [formData, setFormData] = useState<any>({ ...defaultParams });
	const [operateBtnsDefault, setOperateBtnsDefault] = useState({
		printBtn: BatchPrintTypeEnum.打印快递单
	});
	const [kddFjr, setKddFjr, getKddFjr] = useGetState<any[]>([]); // 发件人
	// 排序
	const [sortQueryObj, setSortQueryObj, getSortQueryObj] = useGetState({
		sortValue: "", // 排序字段
		sortType: "", // 排序方式
	});
	const [isShowFilterWaveStatus, setIsShowFilterWaveStatus] = useState(false);
	const [isShowFilterWavePrintStatus, setIsShowFilterWavePrintStatus] = useState(false);
	const [dataSource, setDataSource] = useState([]); // 当前显示的数据

	// 波次拣货详情
	const [pickDetailModalVisible, setPickDetailModalVisible] = useState(false);
	const [pickDetailType, setPickDetailType] = useState<WavePickDetailType>(WavePickDetailType.PLANNED);
	const [currentWaveNo, setCurrentWaveNo] = useState<string>('');
	const [pickDetailData, setPickDetailData] = useState<any[]>([]);

	// 波次详情弹框
	const [waveDetailVisible, setWaveDetailVisible] = useState(false);

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `波次管理-波次打印: 【 ${dataType} 】`,
			data: {
				...data
			}
		});
	};

	// 打开波次详情抽屉
	const handleOpenWaveDetail = (waveNo) => {
		sendPoint(Pointer.波次管理_查看波次详情);
		setCurrentWaveNo(waveNo);
		setWaveDetailVisible(true);
	};
	
	// 关闭波次详情抽屉
	const handleCloseWaveDetail = () => {
		setWaveDetailVisible(false);
	};

	// 打开详情弹窗的处理函数
	const handleOpenPickDetailModal = (record: any, type: WavePickDetailType) => {
	// 检查数量是否为0
		const countMap = {
			[WavePickDetailType.PLANNED]: record.plannedPickCount,
			[WavePickDetailType.PICKED]: record.pickCount,
			[WavePickDetailType.WAITING]: record.waitPickCount,
			[WavePickDetailType.OUT_OF_STOCK]: record.outOfStockCount,
			[WavePickDetailType.REFUND]: record.refundCount,
		};
		
		if (countMap[type] && countMap[type] > 0) {
			setPickDetailType(type);
			setCurrentWaveNo(record.waveNo);
			// 这里可以预设一些数据，或者在弹窗组件中再次请求
			setPickDetailData([]);
			setPickDetailModalVisible(true);
		}
	};

	const handleSortQuery = (key) => {
		setSortQueryObj((prev) => {
			if (prev.sortType === SortType.降序 || prev.sortType === SortType.升序) {
				if (prev.sortValue === key) {
					// Toggle sort direction for the same column
					return {
						sortValue: key,
						sortType: prev.sortType === SortType.降序 ? SortType.升序 : SortType.降序
					};
				} else {
					// New column, default to descending
					return {
						sortValue: key,
						sortType: SortType.降序
					};
				}
			} else {
				// Initial sort
				return {
					sortValue: key,
					sortType: SortType.降序
				};
			}
		});
		ref.current.submit();
	};

	const columns: ColumnsType<any> = [
		{
			title: '序号',
			width: 44,
			dataIndex: 'index',
			render: (text, record, index) => {
				return index + 1;
			}
		},
		{
			title: '波次号',
			width: 120,
			minWidth: 50,
			dataIndex: 'waveNo',
			render: (text, record, index) => {
				return (
					<span 
						className={ s.clickableCell }
						onClick={ (e) => {
							e.stopPropagation();
							
							handleOpenWaveDetail(text); 
						} }
					>
						{text}
					</span>
				);
			}
		},
		{
			title: (
				<div className="r-flex r-ai-c">
					波次状态
					<ThFilterWaveStatus
						visible={ isShowFilterWaveStatus }
						onVisibleChange={ setIsShowFilterWaveStatus }
						onClose={ () => setIsShowFilterWaveStatus(false) }
					>
						<Icon 
							type="shaixuan" 
							style={ { color: '#999', marginLeft: 5, cursor: 'pointer' } } 
							onClick={ (e) => {
								e.stopPropagation();
							} }
						/>
					</ThFilterWaveStatus>
				</div>
			),
			sortSet: { name: "波次状态" },
			width: 120,
			minWidth: 50,
			dataIndex: 'waveStatus',
			render: (text, record, index) => {
				const statusText = waveStatusMap[text] || text;
				return <span style={ waveStatusColorMap[text] || {} } className={ s.tag }>{statusText}</span>;
			}
		},
		{
			title: '快递',
			width: 120,
			minWidth: 50,
			dataIndex: 'exName',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: (
				<div className="r-flex r-ai-c">
					快递单打印状态
					<ThFilterWavePrintStatus
						visible={ isShowFilterWavePrintStatus }
						onVisibleChange={ setIsShowFilterWavePrintStatus }
						onClose={ () => setIsShowFilterWavePrintStatus(false) }
					>
						<Icon 
							type="shaixuan" 
							style={ { color: '#999', marginLeft: 5, cursor: 'pointer' } } 
							onClick={ (e) => {
								e.stopPropagation();
							} }
						/>
					</ThFilterWavePrintStatus>
				</div>
			),
			sortSet: { name: "快递单打印状态" },
			width: 120,
			minWidth: 50,
			dataIndex: 'wavePrintStatus',
			render: (text, record, index) => {
				const wavePrintStatus = wave_wavePrintStatusListOptions?.find(d => d.value == record.wavePrintStatus)?.label;
				return wavePrintStatus;
			}
		},
		{
			title: '包裹数量',
			width: 80,
			dataIndex: 'packageCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '订单数量',
			width: 80,
			dataIndex: 'tradeCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '货品数量',
			width: 80,
			dataIndex: 'itemCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '货品种类',
			width: 80,
			dataIndex: 'itemTypeCount',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: '计划拣货数',
			width: 80,
			dataIndex: 'plannedPickCount',
			render: (text, record, index) => {
				return (
					<span 
						className={ text > 0 ? s.clickableCell : s.noClickableCell }
						onClick={ (e) => {
							e.stopPropagation();
							text > 0 && handleOpenPickDetailModal(record, WavePickDetailType.PLANNED); 
						} }
					>
						{text}
					</span>
				);
			}
		},
		{
			title: '已拣数量',
			width: 80,
			dataIndex: 'pickCount',
			render: (text, record, index) => {
				return (
					<span 
						className={ text > 0 ? s.clickableCell : s.noClickableCell }
						onClick={ (e) => {
							e.stopPropagation();
							text > 0 && handleOpenPickDetailModal(record, WavePickDetailType.PICKED); 
						} }
					>
						{text}
					</span>
				);
			}
		},
		{
			title: '未拣数量',
			width: 80,
			dataIndex: 'waitPickCount',
			render: (text, record, index) => {
				return (
					<span 
						className={ text > 0 ? s.clickableCell : s.noClickableCell }
						onClick={ (e) => {
							e.stopPropagation();
							text > 0 && handleOpenPickDetailModal(record, WavePickDetailType.WAITING); 
						} }
					>
						{text}
					</span>
				);
			}
		},
		{
			title: '缺货数量',
			width: 80,
			dataIndex: 'outOfStockCount',
			render: (text, record, index) => {
				return (
					<span 
						className={ text > 0 ? s.clickableCell : s.noClickableCell }
						onClick={ (e) => {
							e.stopPropagation();
							text > 0 && handleOpenPickDetailModal(record, WavePickDetailType.OUT_OF_STOCK); 
						} }
					>
						{text}
					</span>
				);
			}
		},
		{
			title: '退款数量',
			width: 80,
			dataIndex: 'refundCount',
			render: (text, record, index) => {
				return (
					<span 
						className={ text > 0 ? s.clickableCell : s.noClickableCell }
						onClick={ (e) => {
							e.stopPropagation();
							text > 0 && handleOpenPickDetailModal(record, WavePickDetailType.REFUND); 
						} }
					>
						{text}
					</span>
				);
			}
		},
		{
			title: '拣货员',
			width: 120,
			minWidth: 50,
			dataIndex: 'pickUserName',
			render: (text, record, index) => {
				return text;
			}
		},
		{
			title: (
				<div className="r-flex r-ai-c">
					<div className={ s["tips-hover"] }>生成时间</div>
					<Tooltip title={ sortQueryObj.sortType === SortType.降序 ? '点击升序' : '点击降序' }>
						<span>
							<Icon
								className="r-ml-5 r-c-gray"
								type={ sortQueryObj.sortValue === WaveSortInfo.生成时间 ? sortQueryObj.sortType === SortType.降序 ? "paixuxia" : "paixushang" : "morenpaixu" }
								size={ 16 }
								onClick={ () => { handleSortQuery(WaveSortInfo.生成时间); } }
							/>
						</span>
					</Tooltip>
					
				</div>
			),
			width: 140,
			minWidth: 50,
			dataIndex: 'gmtCreated',
			render: (text, record, index) => {
				return text;
			}
		}
	];

	// 删除某个tag, 更新表单和数据
	const handleRemoveTag = (value, fieldName) => {
		console.log('%c [ value ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
		const oldValue = form.getFieldValue(fieldName) || [];
		const newValues = oldValue.filter((item) => item !== value);
		
		console.log('%c [ newValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', newValues);
		setFormData(prev => {
			return {
				...prev,
				[fieldName]: newValues
			};
		});
        
		form.setFieldsValue({
			[fieldName]: newValues
		});
	};

	// 获取快递公司
	const getExpressCompany = () => {
		memoFn.getExpressList().then((res) => {
			if (res && res.length > 0) {
				setExpressCompanyList(res);
			}
		});
	};


	const FormFieldList: FormItemConfig[] = [
		{
			name: 'timeType',
			label: "",
			children: (
				<Select size="small" placeholder="生成时间" optionLabelProp="label" style={ { width: 160 } }>
					{
						wave_timeTypeOptions.map(item => {
							return (
								<Option value={ item.value } label={ item.label } key={ item.value }>
									<div className="r-flex r-jc-sb r-ai-c">
										<span>{item.label}</span>
									</div>
								</Option>
							);
						})
					}
				</Select>
			),
		},
		{
			name: 'rangeTime',
			label: "",
			children: (
				<KdzsDateRangePicker1 style={ { width: 159 } } cacheQuickChoose datePickerKey={ DatePickerKey.warehouse_waveList } useServeTime />
			),
		},
		{
			name: 'waveStatusList',
			label: "",
			children: (
				<SelectMultiple
					className={ formData.waveStatusList?.length > 0 ? 'high-light-bg' : '' }
					placeholder="波次状态"
					options={ wave_waveStatusListOptions }
					fieldName="waveStatusList"
					handleRemoveTag={ handleRemoveTag }
				/>
			),
		},
		{
			name: 'wavePrintStatusList',
			label: "",
			children: (
				<SelectMultiple
					className={ formData.wavePrintStatusList?.length > 0 ? 'high-light-bg' : '' }
					placeholder="快递单打印状态"
					options={ wave_wavePrintStatusListOptions }
					fieldName="wavePrintStatusList"
					handleRemoveTag={ handleRemoveTag }
				/>
			),
		},
		{
			name: 'waveNo',
			label: "",
			children: <InputMulti maxInputNum={ 1000 } placeholder="波次号" size="small" style={ { width: 160 } } className={ formData.waveNo ? 'high-light-bg' : '' } />,
		},
		{
			name: 'packageNo',
			label: "",
			children: <Input placeholder="包裹号" size="small" style={ { width: 160 } } type="text" className={ formData.packageNo ? 'high-light-bg' : '' } />,
		},
		{
			name: 'ptTid',
			label: "",
			children: <InputMulti maxInputNum={ 1000 } placeholder="订单编号" size="small" style={ { width: 160 } } className={ formData.ptTid ? 'high-light-bg' : '' } />,
		},
		{
			name: 'exCode',
			label: "",
			children: (
				<KddExCodeSelect placeholder="快递公司" style={ { width: 160 } } mode="single" allowClear className={ formData.exCode ? 'high-light-bg' : '' } />
			)
		},
		{
			name: 'exName',
			label: "",
			children: (
				<KddExNameSelect placeholder="快递模板" style={ { width: 160 } } mode="single" allowClear className={ formData.exName ? 'high-light-bg' : '' } />
			),
		},
		{
			name: 'exNumber',
			label: "",
			children: <Input size="small" placeholder="快递单号" style={ { width: 160 } } type="text" className={ formData.exNumber ? 'high-light-bg' : '' } />,
		},
		{
			name: 'pickUserName',
			label: "",
			children: <Input size="small" placeholder="拣货员" style={ { width: 160 } } type="text" className={ formData.pickUserName ? 'high-light-bg' : '' } />,
		}

	];

	
	const fetchSystemList = async(info) => {
		const {
			pageSize = 30, 
			pageNo = 1,
			timeType = "gmtCreated",
			rangeTime = initialRangeTime,
			exNumber,
			ptTid,
			packageNo,
			waveNo,
			pickUserName,
			...restInfo
		} = info;

		sendPoint(Pointer.波次管理_查询);

		const params = {
			...restInfo,
			...getSortQueryObj(),
			querySource: 'PC', // 查询来源,PC,PDA
			pageSize,
			pageNo,
			packageNo: packageNo?.trim(), // 包裹号
			exNumber: exNumber?.trim(), // 快递单号
			pickUserName: pickUserName?.trim(), // 拣货员
			ptTidList: ptTid ? ptTid?.trim()?.split(/[,，]/) : [], // 平台订单编号集合，支持中英文逗号
			waveNoList: waveNo ? waveNo?.trim()?.split(/[,，]/) : [], // 波次号，支持中英文逗号
		};
    
		if (rangeTime) {
			const [startTime, endTime] = rangeTime;
			if (!startTime && !endTime) {
				message.error("查询时间不能为空");
				return;
			}
			if (!startTime) {
				message.error("开始时间不能为空");
				return;
			}
			if (!endTime) {
				message.error("结束时间不能为空");
				return;
			}
			params[`${timeType}Start`] = rangeTime[0].format('YYYY-MM-DD HH:mm:ss');
			params[`${timeType}End`] = rangeTime[1].format('YYYY-MM-DD HH:mm:ss');
		}

		// 如果要跳转过来带参查询
		if (location?.state?.ptTids) {
			console.log('%c [ ptTids ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', location?.state?.ptTids);
			// params['ptTidList'] = location?.state?.ptTids?.split(',') || [];

			// setFormData(prev => {
			// 	return {
			// 		...prev,
			// 		ptTid: location?.state?.ptTids
			// 	};
			// });
			// form.setFieldsValue({
			// 	ptTid: location?.state?.ptTids
			// });
		}
    
		setSearchParams(params);
		setLoading(true);
		console.log('%c [ 请求参数 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
            
		return TradeWaveQueryWaveInfoPageListApi(params);
	};

	const responseAdapter = (data, params) => {
		const dataSource = data?.list?.map((i, index) => ({
			rowId: `${i.id}`,
			...i,
		}));

		setWavePackageFilterType('');
		setWavePackageList(dataSource);

		setLoading(false);
		return {
			list: dataSource,
			total: data.total
		};
	};

	const _onChange = (pagination, filters, sorter, { action }) => {
		// 切换分页需要重置勾选项
		if (action == 'paginate') {
			setSelectedRows([]);
			setSelectedRowKeys([]);
		}
	};

	const onFieldsChange = (changedValues, allValues) => {
		console.log('%c [ changedValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues, allValues);
		setFormData(allValues);
	};

	// 点击发货 （自动发货逻辑在打印回调钩子实现）
	const handleSend = async() => {
		sendPoint(Pointer.波次管理_发货);
		if (!selectedRows.length) {
			message.warning('请先选择波次');
			return;
		}

		// 限制只能选择单个波次打印
		if (selectedRows.length > 1) {
			Modal.info({
				centered: true,
				icon: <ExclamationCircleOutlined style={ { color: '#FF9500' } } />,
				title: '仅支持单个波次发货',
				content: '',
				closable: true,
				okText: '我知道了',
				cancelButtonProps: { style: { display: 'none' } }
			});
			return;
		}

		// 前置过滤已踢出的包裹
		const filterTickedOutWaves = filterTickedOutPackage(selectedRows);

		if (filterTickedOutWaves.length === 0) {
			message.warning('没有可发货的有效包裹，所有包裹已被踢出');
			return;
		}

		// 波次状态异常拦截
		const { canContinue, filteredWaves } = await checkWaveStatus(filterTickedOutWaves, 'send', setSelectedRowKeys, setSelectedRows, customLogPost);
		if (!canContinue) {
			return;
		}

		// 检查是否有未打印或部分打印的波次
		const unprintedWaves = filteredWaves.filter(wave => wave.wavePrintStatus !== 'COMPLETED_PRINT');
    
		// 单个波次的异常提示， 直接拦截不打印
		if (filteredWaves.length === 1 && unprintedWaves.length > 0) {
			Modal.confirm({
				centered: true,
				icon: <ExclamationCircleOutlined style={ { color: '#faad14' } } />,
				title: '快递单打印状态异常，请重新选择波次',
				content: (
					<div>
						<p>快递单打印状态【已打印】时，可进行发货</p>
						<p>{`${filteredWaves[0].waveNo} — ${wave_wavePrintStatusListOptions.find(option => option.value === filteredWaves[0].wavePrintStatus)?.label || "待处理"}`}</p>
					</div>
				),
				okText: '确定',
				cancelButtonProps: { style: { display: 'none' } }
			});
			return;
		}

		// 多个的逻辑 （保留）
		if (filteredWaves.length > 1 && unprintedWaves.length > 0) {
			// 获取波次快递单打印状态的中文描述
			const getPrintStatusText = (wavePrintStatus) => {
				return wave_wavePrintStatusListOptions.find(option => option.value === wavePrintStatus)?.label || "待处理";
			};
	
			// 构建波次列表显示
			const waveListContent = unprintedWaves.map(wave => {
				return <p>{`${wave.waveNo} — ${getPrintStatusText(wave.wavePrintStatus)}`}</p>;
			});
	
			// 显示确认弹框
			Modal.confirm({
				centered: true,
				icon: <ExclamationCircleOutlined style={ { color: '#faad14' } } />,
				title: '波次未打印，是否继续发货',
				content: (
					<div>
						<p>快递单打印状态【已打印】时，可进行发货</p>
						<div style={ { maxHeight: '300px', overflowY: 'auto' } }>
							{waveListContent}
						</div>
					</div>
				),
				okText: '跳过异常继续发货',
				cancelText: '取消',
				okButtonProps: { 
					style: { backgroundColor: '#FF9500', borderColor: '#FF9500' } 
				},
				onOk: () => {
					// 继续发货逻辑
					// 过滤掉异常波次，只发货正常波次
					const validWaves = filteredWaves.filter(wave => wave.wavePrintStatus === 'COMPLETED_PRINT');
                    
					// 取消勾选异常波次
					const validWaveKeys = validWaves.map(wave => wave.rowId);
					setSelectedRowKeys(validWaveKeys);
					setSelectedRows(validWaves);
                    
					customLogPost('跳过异常继续发货', { unprintedWaves, validWaveKeys });

					if (validWaves.length === 0) {
						message.warning('没有可发货的波次数据');
						return;
					}

					// 继续发货
					startSend(validWaves);
				},
				onCancel: () => {
					console.log('取消发货');
					customLogPost('波次快递单打印状态异常, 取消发货', { unprintedWaves });
				}
			});
			return;
		}
	
		// 所有波次都已打印，直接发货
		console.log('直接发货', filteredWaves);

		// 发货逻辑
		startSend(filteredWaves);
	};

	// 开始发货
	const startSend = async(validWaves) => {
		try {
			// 发货前判断模板和单号异常
			const checkedWaves = await checkTemplateAndWaybill(validWaves, 'send', setSelectedRowKeys, setSelectedRows, customLogPost, 'wave', expressTemplateList);

			// 走包裹异常判断逻辑 （不走通用异常拦截逻辑）
			// 检查包裹异常
			const filteredWaves = await checkPackageExceptions(
				checkedWaves, 
				'send', 
				customLogPost,
				setSelectedRowKeys,
				setSelectedRows,
				'wave' // 指定为波次维度
			);

			let invalidTids = []; // 假设这里是被过滤掉的异常的订单号
			onHandleSend(filteredWaves, invalidTids);
		} catch (error) {
			console.log('发货前检查失败或用户取消', error);
		}
	};

	const onHandleSend = (validWaves, invalidTids) => {
		// 这里需要把异常订单号所在包裹剔除掉
		const orderList = formatTradeData(validWaves, invalidTids);
		// 如果所有波次都无效，提示用户
		if (orderList.length === 0) {
			message.warning('没有可发货的有效包裹');
			return;
		}

		batchWaveSendFromList(orderList, 'waveList'); // 开始发货
	};

	// 设置按钮默认缓存 - （保留着后面做单个模板打印的还要用到）
	const handleOptionChange = (fieldName, value) => {
		setOperateBtnsDefault(prev => {
			const newState = {
				...prev,
				[fieldName]: value
			};
			// 不需要在这里设置缓存，因为组件内部已经设置了
			
			return newState;
		});
	};
    
	// 取消波次
	const handleExitWave = () => {
		sendPoint(Pointer.波次管理_取消波次);
		if (!selectedRows.length) {
			message.warning('请先选择波次');
			return;
		}

		// 检查是否有已完成或已取消的波次
		const invalidWaves = selectedRows.filter(wave => wave.waveStatus === 'COMPLETED' || wave.waveStatus === 'CANCELED');

		if (invalidWaves.length > 0) {
			// 显示异常弹框
			Modal.confirm({
				centered: true,
				title: '波次状态异常，不可取消',
				content: '波次状态为「已完成」、「已取消」时不可取消波次',
				icon: <ExclamationCircleOutlined style={ { color: '#faad14' } } />,
				okText: '确定',
				cancelText: '取消',
				cancelButtonProps: { style: { display: 'none' } }
			});
			return;
		}

		// 二次确认弹框
		Modal.confirm({
			centered: true,
			title: '请确认取消这些波次?',
			icon: <ExclamationCircleOutlined style={ { color: '#FAAD14' } } />,
			content: (
				<div style={ { marginLeft: '0', marginTop: '8px' } }>
					<p>1.取消波次后已拣货的货品请归还至货架上；</p>
					<p>2.波次状态将变为为「已取消」，同时将解绑该波次下的订单。</p>
				</div>
			),
			okText: '确定',
			cancelText: '取消',
			onOk: async() => {
				try {
					setCancelLoading(true);
					const waveNos = selectedRows.map(row => row.waveNo);
                    
					const params = {
						userId,
						waveNoList: waveNos
					};
                    
					const res = await TradePickCancelWaveApi(params);
                    
					if (res) {
						message.success(`成功取消 ${selectedRows.length} 个波次`);
						// 刷新数据
						ref?.current?.refresh();
						// 清空选择
						setSelectedRowKeys([]);
						setSelectedRows([]);
					} else {
						// 处理业务错误
						message.error('取消波次失败，请稍后重试');
					}
				} catch (error) {
					console.error('取消波次失败', error);
				} finally {
					setCancelLoading(false);
				}
			}
		});
	};

	// 打印快递单 -- 单个模板的暂时不做
	const handleBatchPrint = () => {
		console.log('%c [ 打印快递单 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '打印快递单');
	};

	// 混合打印
	const handleMixedPrint = async() => {
		sendPoint(Pointer.波次管理_打印快递单);
		if (!selectedRows.length) {
			message.warning('请先选择波次');
			return;
		}

		// 限制只能选择单个波次打印
		if (selectedRows.length > 1) {
			Modal.info({
				centered: true,
				icon: <ExclamationCircleOutlined style={ { color: '#FF9500' } } />,
				title: '仅支持单个波次打印',
				content: '',
				closable: true,
				okText: '我知道了',
				cancelButtonProps: { style: { display: 'none' } }
			});
			return;
		}

		// 前置过滤已踢出的包裹
		const filterTickedOutWaves = filterTickedOutPackage(selectedRows);
        
		if (filterTickedOutWaves.length === 0) {
			message.warning('没有可打印的有效包裹，所有包裹已被踢出');
			return;
		}

		// 波次状态异常拦截
		const { canContinue, filteredWaves } = await checkWaveStatus(filterTickedOutWaves, 'print', setSelectedRowKeys, setSelectedRows, customLogPost);
		if (!canContinue) {
			return;
		}

		// 打印过滤异常后的波次
		startMixedPrint(filteredWaves);
	};

	// 开始打印
	const startMixedPrint = async(validWaves) => {
		try {
			// 打印前判断模板和单号异常
			const checkedWaves = await checkTemplateAndWaybill(validWaves, 'print', setSelectedRowKeys, setSelectedRows, customLogPost, 'wave', expressTemplateList);
            
			// 走包裹异常判断逻辑 （不走通用异常拦截逻辑）
			const filteredWaves = await checkPackageExceptions(
				checkedWaves, 
				'print', 
				customLogPost,
				setSelectedRowKeys,
				setSelectedRows,
				'wave' // 指定为波次维度
			);
            
			let invalidTids = []; // 假设这里是被过滤掉的异常的订单号
			onHandlePrint(filteredWaves, invalidTids);
		} catch (error) {
			console.log('打印前检查失败或用户取消', error);
		}
	};

	// 过滤异常包裹数据
	const formatTradeData = (validWaves, invalidTids) => {
		if (invalidTids?.length) {
			let orderList = [];
			let wavesWithNoValidPackages = []; // 存储没有有效包裹的波次
	
			// 从validWaves中提取订单数据，并过滤掉包含无效tid的包裹
			if (validWaves && validWaves.length > 0) {
				validWaves.forEach(wave => {
					if (wave.pickPackageList && wave.pickPackageList.length > 0) {
						// 过滤掉包含无效tid的包裹
						const validPackages = wave.pickPackageList.filter(pkg => {
							// 检查包裹中的所有订单项
							if (pkg.pickItemList && pkg.pickItemList.length > 0) {
								// 如果包裹中有任何一个订单在invalidTids中，则过滤掉整个包裹
								return !pkg.pickItemList.some(item => item.tid && invalidTids.includes(item.tid));
							}
							return true; // 如果没有pickItemList，保留该包裹
						});
						
						// 将有效包裹添加到orderList
						if (validPackages.length > 0) {
							// 添加整个波次数据，但只包含有效的包裹
							orderList.push({
								...wave,
								pickPackageList: validPackages // 只保留有效的包裹
							});
						} else {
							// 记录没有有效包裹的波次
							wavesWithNoValidPackages.push(wave);
						}
					} else {
						// 如果波次没有包裹列表，也记录为无效
						wavesWithNoValidPackages.push(wave);
					}
				});
			}
	
			// 如果有无效波次，取消勾选
			if (wavesWithNoValidPackages.length > 0) {
				const invalidWaveIds = wavesWithNoValidPackages.map(wave => wave.rowId);
				const remainingValidWaveIds = selectedRowKeys.filter(key => !invalidWaveIds.includes(key));
				const remainingValidWaves = selectedRows.filter(wave => !wavesWithNoValidPackages.includes(wave));
				
				// 更新选中状态
				setSelectedRowKeys(remainingValidWaveIds);
				setSelectedRows(remainingValidWaves);
				
				// 提示用户部分波次被过滤
				if (wavesWithNoValidPackages.length > 0) {
					// message.info(`已过滤 ${wavesWithNoValidPackages.length} 个没有有效包裹的波次`);
					console.log('%c [ 已过滤 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', `已过滤 ${wavesWithNoValidPackages.length} 个没有有效包裹的波次`);
				}
			}
	
			return orderList;
		} else {
			return validWaves;
		}
	};

	const onHandlePrint = (validWaves, invalidTids) => {
		// 这里需要把异常订单号所在包裹剔除掉

		const orderList = formatTradeData(validWaves, invalidTids);
		// 如果所有波次都无效，提示用户
		if (orderList.length === 0) {
			message.warning('没有可打印的有效包裹');
			return;
		}
		
		// 打印只需要包裹
		const printOrderList = orderList.flatMap(wave => {
			return wave.pickPackageList;
		});

		pageLoading.loading(false, 100);
		PrintCenterAPI.batchPrintWaveKdd({
			fjrInfoMap: {
				kddFjrMap: {
					isUseCommon: false,
					isUseMore: true,
					isKddBindFjr: true,
					senderInfo: getKddFjr(), // 发件人
				},
				// 模板绑定发件人
				userIdList: Array.from(new Set(
					printOrderList.map(pkg => pkg.sellerId).filter(Boolean)
				))
			},
			orderList: printOrderList,
			from: 'waveList'
		});
	};

	// 列表页顶部额外的操作内容
	const expandContext = (
		<div
			style={ { width: "100%", padding: "12px 0" } }
			className="r-flex r-ai-c"
		>
			<div className="r-flex r-ai-c r-gap-8">
				{/* <div>
					<DropdownButton
						options={ [
							{
								key: BatchPrintTypeEnum.打印快递单,
								label: "打印快递单",
								onClick: () => handleBatchPrint()
							},
							{
								key: BatchPrintTypeEnum.混合打印,
								label: "混合打印",
								onClick: () => handleMixedPrint()
							}
						] }
						defaultKey={ operateBtnsDefault.printBtn }
						onOptionChange={ (key) => handleOptionChange('printBtn', key) }
						cacheKey="WarehouseWaveListOperateBtnsDefault"
						userId={ userId }
						type="primary"
						size="middle"
						overlayClassName={ s.overlay }
						leftButtonStyle={ { width: 94 } }
					/>
				</div> */}

				<Button
					className={ s.expandBtn }
					onClick={ _.debounce(() => handleMixedPrint(), 500, {
						leading: true,
						trailing: false
					}) }
					size="middle"
					type="primary"
				>
					打印快递单
				</Button>

				<Button
					className={ s.expandBtn }
					onClick={ _.debounce(() => handleSend(), 500, {
						leading: true,
						trailing: false
					}) }
					size="middle"
				>
					发货
				</Button>

				<Button
					className={ s.expandBtn }
					onClick={ _.debounce(() => handleExitWave(), 500, {
						leading: true,
						trailing: false
					}) }
					size="middle"
					loading={ cancelLoading }
				>
					取消波次
				</Button>
			</div>
		</div>
	);

	const rowSelection = {
		columnWidth: 48,
		onChange: (selectedRowKeysAct: number[], selectedRows: []) => {
			console.log('%c [ selectedRowKeysAct ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', selectedRowKeysAct);
			setSelectedRows([...selectedRows]);
			setSelectedRowKeys([...selectedRowKeysAct]);
		},
		selectedRowKeys
	};

	const onReset = () => {
		form.resetFields();
		setFormData({ ...defaultParams });
		setSelectedRows([]);
		setSelectedRowKeys([]);
	};

	useEffect(() => {
		userStore.getUserSetting();
		userStore.getSystemSetting();
		sendPoint(Pointer.波次管理_页面展示);

		getExpressCompany();

		getShopList();
	}, []);

	// 监听 wavePackageList 变化，过滤掉 isFilterByStatus 为 true 的数据
	useEffect(() => {
		if (wavePackageList && wavePackageList.length > 0) {
			const filteredData = wavePackageList.filter(item => !item.isFilterByStatus);
			setDataSource(filteredData);
        
			// 更新勾选项，移除被过滤掉的数据
			if (selectedRowKeys.length > 0) {
				const filteredIds = filteredData.map(item => item.rowId);
				const newSelectedRowKeys = selectedRowKeys.filter(key => filteredIds.includes(key));
				const newSelectedRows = selectedRows.filter(row => filteredIds.includes(row.rowId));
            
				if (newSelectedRowKeys.length !== selectedRowKeys.length) {
					setSelectedRowKeys(newSelectedRowKeys);
					setSelectedRows(newSelectedRows);
				}
			}
		} else {
			setDataSource([]);
			// 清空勾选项
			if (selectedRowKeys.length > 0) {
				setSelectedRowKeys([]);
				setSelectedRows([]);
			}
		}
	}, [wavePackageList, selectedRowKeys, selectedRows]);


	/**
     * 格式化发货单发件人 用于打印中心
     */
	useEffect(() => {
		const fetchSenderSettings = async() => {
			const { list: kddFjrInfoMap } = await userStore.getSenderSetting();
			let { senderInfo, allList } = formatKddFjrUtil({ shopList, kddFjrInfoMap });
        
			// taobaoNick 暂时不需要传
			// 模板绑定发件人
			fjrInfoMap.kdd = [
				{
					list: allList,
					"taobaoNick": '',
					"userId": ''
				}
			];
			// 模板绑定发件人
			// @ts-ignore
			window.erpData.allKddFjr = fjrInfoMap.kdd;
			// 模板绑定发件人需传入
			const fjr = [...senderInfo, {
				list: allList,
				"taobaoNick": '',
				"userId": userId // 手工单使用
			}];
			setKddFjr(fjr);
			console.log('%c [ 发件人数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', fjr);
		};

		fetchSenderSettings();
	}, [fjrInfoMap, shopList, userId]);

	// 监听打印完成事件
	useEffect(() => {
		// 监听波次列表打印完成事件
		const handleAfterPrint = (packageNoList) => {
			console.log('波次列表打印完成', packageNoList);
            
			// 刷新列表数据
			ref?.current?.refresh();
                
			// 清空选择
			setSelectedRowKeys([]);
			setSelectedRows([]);
		};

		const handleAfterSend = ({ res, params }) => {
			console.log('波次列表发货完成', res, params);
			// 刷新列表数据
			ref?.current?.refresh();
                
			// 清空选择
			setSelectedRowKeys([]);
			setSelectedRows([]);
		};

		const handleUpdateWaveList = () => {
			ref?.current?.refresh();
		};
        
		// 添加事件监听
		event.on('waveList.afterPrint', handleAfterPrint);
		event.on('waveList.afterSend', handleAfterSend);
		event.on('waveList.updateWaveList', handleUpdateWaveList);
        
		// 组件卸载时移除事件监听
		return () => {
			event.off('waveList.afterPrint', handleAfterPrint);
			event.off('waveList.afterSend', handleAfterSend);
			event.off('waveList.updateWaveList', handleAfterSend);
		};
	}, []);

	useActivate(() => {
		console.log('%c [ 当前组件激活 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', location);
		// 如果要跳转过来带参查询
		if (location?.state?.ptTids) {
			ref?.current?.refresh();
		}
		
	});
    
	return (
		<NormalLayout className="print-batch-search-con" id="waveList">
			<div className={ s.waveList }>
				<SearchTable
					ref={ ref }
					pageSizeId="WarehouseWaveListTable"
					form={ form }
					fetchData={ fetchSystemList }
					responseAdapter={ responseAdapter }
					onReset={ onReset }
					searchBtnText="查询"
					resetBtnText="重置"
					showSearch
					rowFormConfig={ {
						defaultParams,
						formList: FormFieldList,
						size: 'small',
						colProps: {
						}
					} }
					baseTableConfig={ {
						dataSource,
						onFieldsChange,
						rowKey: 'rowId',
						columns,
						expandContext,
						expandContextStyle: {
							marginBottom: '0px',
							padding: '0 16px 8px'
						},
						pagination: {
							defaultPageSize: 30,
							pageSizeOptions: [30, 50, 100, 200],
						},
						cachePgination: true,
						isStickyHeader: true,
						stickyTop: 155,
						headerColSet: {
							resizeId: `WarehouseWaveListTable_width_${userStore?.userInfo?.userId}`,
						},
						enableRowClick: true, // 是否开启行点击选中，其他事件需要阻止冒泡
						rowSelection: {
							type: 'checkbox',
    
							...rowSelection,
							...{
								selectedRowKeys
							}
						},
						rowClassName: (record) => {
							if (selectedRowKeys.includes(record.rowId)) {
								return 'r-row-checked';
							}
							return 'r-row-default';
						}
					} }
					onChange={ ({ pageNo, pageSize }) => {
						_onChange({ current: pageNo, pageSize }, undefined, undefined, { action: 'paginate' });
					} }
				/>
			</div>

			<WavePickDetailModal
				visible={ pickDetailModalVisible }
				onCancel={ () => setPickDetailModalVisible(false) }
				type={ pickDetailType }
				waveNo={ currentWaveNo }
				data={ pickDetailData }
			/>

			<WaveDetailDrawer
				visible={ waveDetailVisible }
				onClose={ handleCloseWaveDetail }
				waveNo={ currentWaveNo }
				wave={ wavePackageList.find(wave => wave.waveNo === currentWaveNo) }
				kddFjr={ kddFjr }
			/>
		</NormalLayout>
	);
};

export default observer(WaveList);