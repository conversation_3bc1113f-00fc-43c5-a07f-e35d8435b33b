import React, { useState, useRef, useMemo } from 'react';
import { Button, Input, Empty } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import cs from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import SearchTable from '@/components/SearchTable';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import EnumSelect from '@/components/Select/EnumSelect';
import { StockInOutRecordGetPageListApi } from "@/apis/warehouse/stock";
import {
	StockInOutRecordGetPageListRequest, StockInOutRecordGetPageListResponse
} from '@/types/schemas/warehouse/stock';
import { StockInOutRecordListProps } from './interfance';
import WaresInfo from "@/components-biz/WaresInfo";
import { clearParams } from '@/utils/stringHelper';
import WarpText from '@/components-biz/WarpText';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import Pointer from "@/utils/pointTrack/constants";
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import { StockInOutType, StockOutType, StockUpOutType, StockUpType } from '../constants';
import { downloadCenter } from '@/pages/Index/DownloadCenter/utils';
import { ModulesFunctionEnum } from '@/types/schemas/setting/download';
import FieldsPermissionCheck, { FieldsPermissionCheckTypeEnum, FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';

interface StockLogProps {

}

const myformat = "YYYY-MM-DD HH:mm:ss";

const StockLog: React.FC<StockLogProps> = (props) => {
	const startTime:Dayjs = useMemo(() => dayjs().startOf('day').subtract(6, 'day'), []);
	const endTime:Dayjs = useMemo(() => dayjs().endOf('day'), []);
	const [form] = useForm();
	const ref = useRef<SearchTableRefProps>(); // 获取表单对象
	const [two, setTwo] = useState({});
	const [loading, setLoading] = useState(false);
	const [total, setTotal] = useState(0);
	const [exporting, setExporting] = useState(false);
	// 表格列定义
	const columns: ColumnsType<StockInOutRecordListProps> = [
		{
			width: 20,
			align: 'center',
			render: (value, row, index) => {
				return {
					children: <span>{index + 1}</span>,
					props: {},
				};
			},
		},
		{
			title: '货品简称/规格',
			width: 100,
			key: 'sysItemAlias',
			render: (value, row, index) => {
				return (
					<WaresInfo outerId={ row.outerId } imgUrl={ row.picUrl } skuName={ row.sysSkuName } wareName={ row.sysItemAlias } isCombination={ row.isCombination } />
				);
			},
		},
		{
			title: '货品规格编码/货号',
			width: 60,
			key: 'skuOuterId',
			render: (value, row, index) => {
				return (
					<div className={ cs('r-l-preWrap') }>
						<div>{row.skuOuterId}</div>
						<div style={ { fontSize: '12px' } } className={ cs('r-fc-black-45') }>{row.itemNo}</div>
					</div>
				);
			},
		},
		{
			title: '仓库',
			width: 30,
			dataIndex: 'storageName',
			key: 'storageName',
			render: (value, row, index) => {
				return (
					<WarpText>
						{value}
					</WarpText>
				);
			},
		},
		{
			title: '类型',
			width: 30,
			dataIndex: 'stockUpType',
			key: 'stockUpType',
			render: (value, row, index) => {
				return (
					<div>
						{StockUpOutType[value]}
					</div>
				);
			},
		},
		{
			title: '操作前',
			width: 30,
			dataIndex: 'opBeforeNum',
			key: 'opBeforeNum',
			render: (value, row, index) => {
				return (
					<div>
						{value}
					</div>
				);
			},
		},
		{
			title: '操作数量',
			width: 30,
			dataIndex: 'opNum',
			key: 'opNum',
			render: (value, row, index) => {
				return (
					<div className={ value > 0 ? 'r-c-success' : 'r-c-warning' }>
						{value}
					</div>
				);
			},
		},
		{
			title: '操作后',
			width: 30,
			dataIndex: 'opAfterNum',
			key: 'opAfterNum',
			render: (value, row, index) => {
				return (
					<div>
						{value}
					</div>
				);
			},
		},
		{
			title: '正品/次品',
			width: 30,
			dataIndex: 'grade',
			key: 'grade',
			render: (value, row, index) => {
				return (
					<div>
						{value == 1 ? '正品' : '次品'}
					</div>
				);
			},
		},
		{
			title: '成本价',
			width: 30,
			dataIndex: 'costPrice',
			key: 'costPrice',
			render(value, record, index) {
				return (
					<FieldsPermissionCheck
						fieldsPermission={ FieldsPermissionEnum.成本价 }
						type={ FieldsPermissionCheckTypeEnum.仅展示 }
					>
						<div>{value}</div>
					</FieldsPermissionCheck>
				);
			},
		},
		{
			title: '售卖价',
			width: 30,
			dataIndex: 'retailPrice',
			key: 'retailPrice',
		},
		{
			title: '操作时间/系统单号/订单编号/售后单号',
			width: 80,
			dataIndex: 'created',
			key: 'created',
			render: (value, row, index) => {
				return (
					<div>
						<p>{value}</p>
						{ row.tid && row.tid !== '-1' && <p className="r-fc-black-45">{row.tid}</p> }
						{ row.ptTid && row.ptTid !== '-1' && <p className="r-fc-black-45">{row.ptTid}</p> }
						{ row.refundId && (!row.tid || row.tid === '-1') && <p className="r-fc-black-45">{row.refundId}</p> }
					</div>
				);
			},
		},
		{
			title: '采购/入库单号',
			width: 60,
			dataIndex: 'purchaseNo',
			key: 'purchaseNo',
			render: (value, row, index) => {
				return (
					<div>
						<p>{value}</p>
						{ row.reachNo && <p className="r-fc-black-45">{row.reachNo}</p> }
					</div>
				);
			},
		},
		{
			title: '操作人',
			width: 40,
			dataIndex: 'opUserStr',
			key: 'opUserStr',
			render: (value, row, index) => {
				return (
					<WarpText>
						{value}
					</WarpText>
				);
			},
		}
	];

	// 联动
	const handleOneChange = (data:any) => {
		if (Object.prototype.toString.call(data) == '[object Number]') {
			let twoType = data == 1 ? StockUpType : StockOutType;
			setTwo(twoType);
			form.setFieldsValue({
				stockUpType: undefined
			});
		} else {
			// 重置出入库类型
			setTwo({});
			form.setFieldsValue({
				stockUpType: undefined
			});
		}
	};

	// 表单元素定义 下拉框？日期？ 多选框？ 多选按钮？
	const FormFieldListV1: FormItemConfig[] = [
		{
			name: 'timeRange',
			label: "",
			children: (
				<KdzsDateRangePicker1 style={ { width: '169px' } } />
			),
			colProps: {
				// span: 5
			},
		},
		{
			name: 'stockInOutType',
			label: '',
			children: <EnumSelect placeholder="库存变更类型" enum={ StockInOutType } onChange={ handleOneChange } style={ { width: 170 } } size="small" />
		},
		{
			name: 'stockUpType',
			label: '',
			children: <EnumSelect
				placeholder="出入库类型"
				enum={ two }
				notFoundContent={ (
					<Empty
						image={ Empty.PRESENTED_IMAGE_DEFAULT }
						imageStyle={ {
							width: 120,
							display: 'flex',
							justifyContent: 'center'
						} }
						description={ <span>暂无数据</span> }
					/>
				) }
				style={ { width: 170 } }
				size="small"
			/>
		},
		{
			name: 'sysItemInfo',
			label: "",
			children: <Input placeholder="货品简称" style={ { width: 170 } } size="small" />,
		},
		{
			name: 'skuOuterId',
			label: '',
			children: <Input placeholder="货品规格编码" style={ { width: 170 } } size="small" />
		},
		{
			name: 'ptTid',
			label: '',
			children: <Input placeholder="订单编号" style={ { width: 170 } } size="small" />
		},
		{
			name: 'tid',
			label: '',
			children: <Input placeholder="系统单号" style={ { width: 170 } } size="small" />
		},
		{
			name: 'purchaseNo',
			label: '',
			children: <Input placeholder="采购单号" style={ { width: 170 } } size="small" />
		},
		{
			name: 'reachNo',
			label: '',
			children: <Input placeholder="入库单号" style={ { width: 170 } } size="small" />
		}
	];

	const getParams = async(params) => {
		let timeRange:[Dayjs, Dayjs] = params?.timeRange || [startTime, endTime];
		if (!params.timeRange) {
			await form.setFieldsValue({ timeRange: [dayjs().startOf('day').subtract(6, 'day'), dayjs().endOf('day')], });
			timeRange = form.getFieldValue('timeRange');
		}
		if (params.stockInOutType === undefined && params.stockUpType === undefined && Object.keys(two).length) {
			setTwo({});
			form.setFieldsValue({
				stockUpType: undefined
			});
		}
		params = {
			...clearParams(params),
			needOccupiedStock: false,
			startTime: dayjs(timeRange[0]).format(myformat),
			stopTime: dayjs(timeRange[1]).format(myformat),
		};
		delete params['timeRange'];
		return params;
	};

	// 数据请求
	const getProductList = async(params: StockInOutRecordGetPageListRequest) => {
		params = await getParams(params);
		console.log('查询条件：', params);

		try {
			setLoading(true);
			const infoData:StockInOutRecordGetPageListResponse['data'] = await StockInOutRecordGetPageListApi(params);
			const infoTotal = params.pageNo == 1 ? infoData.total : total;
			setLoading(false);
			params.pageNo == 1 && setTotal(infoData.total);

			return {
				list: infoData?.list || [],
				total: infoTotal
			};
		} catch (error) {
			console.log(error);
			setLoading(false);
		}
	};

	// 返回值重构
	const responseAdapter = (info: StockInOutRecordGetPageListResponse["data"]) => {
		console.log('responseAdapter', info);

		return {
			total: info?.total || 0,
			list: info?.list || []
		};
	};

	const handleExport = async() => {
		setExporting(true);

		const searchParams = await getParams(form.getFieldsValue());
		await downloadCenter({
			requestParams: searchParams,
			fileName: `出入库日志`,
			module: ModulesFunctionEnum.出入库日志
		});
		setExporting(false);
	};

	const expandContext = (
		<div style={ { width: '100%', display: 'flex' } }>
			<Button
				loading={ exporting }
				type="primary"
				className="r-pointer r-mr-12"
				onClick={ () => { handleExport(); } }
			>导出
			</Button>
		</div>
	);

	return (
		<NormalLayout>
			<SearchTable<StockInOutRecordListProps>
				searchBtnPoint={ Pointer['库存_出入库日志_查询'] }
				searchBtnProps={ { style: { marginLeft: 0, marginRight: '8px' } } }
				pageSizeId="stockLogTable"
				ref={ ref } // 引用
				form={ form } //
				fetchData={ getProductList } // 接口请求
				responseAdapter={ responseAdapter } // 返回值适配
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch // 是否显示查询
				rowFormConfig={ { // 表单配置
					formList: FormFieldListV1,
					defaultParams: { // 查询表单设置初始
						timeRange: [startTime, endTime]
					},
					rowProps: {}, // 表单行配置
					colProps: { // 表单列配置
						// span: 2, // 表单条件宽度
					},
					formItemProps: {

					},
				} }
				baseTableConfig={ { // 表格基础设置
					noGap: true,
					innerTableStyle: { paddingTop: 0 },
					rowKey: 'id',
					columns, // 列配置
					pagination: { // 分页设置:具体参数可参考Table-pagination
						// pageSizeOptions: ["10", "500"],
						// defaultCurrent: 1, // 当前页
						// defaultPageSize: 500
					},
					expandContext,
					scroll: {
						scrollToFirstRowOnChange: true,
						x: '100%',
						// y: 400,
					},
					loading,
					tableLayout: 'fixed'
				} }
				// onChange={ onChange }
			/>
			{/* 广告弹窗 */}
			{/* <div className="sys_position_replace" data-type="middlePopup" data-page="stockLog" />
			<div className="sys_position_replace rightBottomPopup" data-type="rightBottomPopup" data-page="stockLog" /> */}
		</NormalLayout>
	);
};

export default StockLog;
