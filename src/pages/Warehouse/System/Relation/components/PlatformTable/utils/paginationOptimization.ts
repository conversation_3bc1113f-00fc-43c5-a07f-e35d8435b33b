import {
	ItemSysItemListOfItemRelationPlatformItemViewApi,
	listOfItemRelationPlatformItemViewForSplit,
	ItemSysItemListOfItemRelationSysTemItemViewApi,
	ItemSysItemListOfItemRelationSysTemItemViewSplitApi
} from '@/apis/warehouse/system';
import {
	ItemSysItemListOfItemRelationPlatformItemViewRequest,
	ItemSysItemListOfItemRelationPlatformItemViewResponse,
	ItemSysItemListOfItemRelationSysTemItemViewRequest,
	ItemSysItemListOfItemRelationSysTemItemViewResponse
} from '@/types/schemas/warehouse/system';

/**
 * 信号量控制并发数量
 */
class Semaphore {
	private current = 0;

	private waiting: (() => void)[] = [];

	private max: number;

	constructor(max: number) {
		if (max <= 0) {
			throw new Error('Semaphore max value must be positive');
		}
		this.max = max;
	}

	async acquire(): Promise<void> {
		if (this.current < this.max) {
			this.current++;
			return Promise.resolve();
		}

		return new Promise<void>((resolve) => {
			this.waiting.push(resolve);
		});
	}

	release(): void {
		if (this.current <= 0) {
			throw new Error('Cannot release semaphore: no permits acquired');
		}
		
		this.current--;
		if (this.waiting.length > 0) {
			this.current++;
			const resolve = this.waiting.shift()!;
			resolve();
		}
	}

	/**
	 * 获取当前可用的信号量数量
	 */
	get available(): number {
		return this.max - this.current;
	}

	/**
	 * 获取当前等待队列长度
	 */
	get waitingCount(): number {
		return this.waiting.length;
	}

	/**
	 * 获取当前已使用的信号量数量
	 */
	get currentCount(): number {
		return this.current;
	}

	/**
	 * 获取最大信号量数量
	 */
	get maxCount(): number {
		return this.max;
	}
}

/**
 * 分页优化处理工具函数
 * 当分页数量>=阈值时，使用分批请求优化性能
 * 
 * @param searchParams 搜索参数
 * @param pageSize 分页大小
 * @param options 配置选项
 * @param options.threshold 触发分批处理的阈值，默认200
 * @param options.batchSize 每批处理的数量，默认100
 * @param options.concurrentLimit 并发请求限制，默认2
 * @returns Promise<ItemSysItemListOfItemRelationPlatformItemViewResponse>
 */
export const fetchWithPaginationOptimization = async(
	searchParams: ItemSysItemListOfItemRelationPlatformItemViewRequest,
	pageSize: number,
	options: {
        threshold?: number;
        batchSize?: number;
        concurrentLimit?: number;
    } = {}
): Promise<ItemSysItemListOfItemRelationPlatformItemViewResponse> => {
	const {
		threshold = 200, // 多少条以上才分批
		batchSize = 100, // 每批条数
		concurrentLimit = 2, // 并发数量
	} = options;

	console.log('分页优化函数调用:', { pageSize, threshold, searchParams });

	// 如果分页数量小于阈值，使用原有逻辑
	if (pageSize < threshold) {
		console.log('使用原有逻辑，直接调用原接口');
		return ItemSysItemListOfItemRelationPlatformItemViewApi(searchParams);
	}

	try {
		// 第一步：获取numIid列表（当tradeFindType为'1'时，还会返回skuIdList）
		const numIidData = await ItemSysItemListOfItemRelationPlatformItemViewApi({
			...searchParams,
			returnSku: false, // 设置为false，表示只返回numIid列表
		});

		if (!numIidData?.list?.length) {
			console.log('第一步没有数据，直接返回');
			return numIidData;
		}

		// 检查是否为tradeFindType='1'的情况，需要处理skuIdList
		const isTradeFindTypeOne = searchParams.tradeFindType === '1' && (searchParams.tradeWaitSendFlag || searchParams.trade3DaySalesVolumeFlag);
		console.log('是否为tradeFindType=1:', isTradeFindTypeOne);

		// 构建numIid到skuIdList的映射关系（仅在tradeFindType='1'时需要）
		const numIidToSkuIdsMap = new Map<string, string[]>();
		if (isTradeFindTypeOne) {
			numIidData.list.forEach(item => {
				if (item.numIid && item.skuIdList && Array.isArray(item.skuIdList)) {
					numIidToSkuIdsMap.set(item.numIid, item.skuIdList);
				}
			});
			console.log('构建的numIid到skuIds映射:', numIidToSkuIdsMap);
		}

		// 提取所有numIid
		const allNumIids = numIidData.list.map(item => item.numIid);
		console.log('提取到的numIids:', allNumIids);

		// 第二步：分批处理，同时保持numIids和skuIds的对应关系
		const batches: Array<{
			numIids: string[];
			skuIds?: string[]; // 只在tradeFindType='1'时有值
		}> = [];
		
		for (let i = 0; i < allNumIids.length; i += batchSize) {
			const numIidBatch = allNumIids.slice(i, i + batchSize);
			
			// 如果是tradeFindType='1'，收集该批次对应的所有skuIds
			let skuIdBatch: string[] = [];
			if (isTradeFindTypeOne) {
				numIidBatch.forEach(numIid => {
					const skuIds = numIidToSkuIdsMap.get(numIid);
					if (skuIds && Array.isArray(skuIds)) {
						skuIdBatch = skuIdBatch.concat(skuIds);
					}
				});
			}
			
			batches.push({
				numIids: numIidBatch,
				...(isTradeFindTypeOne && skuIdBatch.length > 0 ? { skuIds: Array.from(new Set(skuIdBatch)) } : {})
			});
		}

		console.log('分批结果:', batches);

		// 第三步：使用信号量控制并发请求
		const semaphore = new Semaphore(concurrentLimit);

		const requestPromises = batches.map(async(batch, index) => {
			await semaphore.acquire(); // 获取信号量
			
			try {
				const params = {
					...searchParams,
					numIids: batch.numIids,
					// 如果是tradeFindType='1'且有对应的skuIds，则传入skuIds参数
					...(batch.skuIds ? { skuIds: batch.skuIds } : {}),
					pageNo: 1,
					pageSize: batch.numIids.length,
					notCount: true, // 不统计总数
					tradeWaitSendFlag: false, // 不查询待发货订单
					trade3DaySalesVolumeFlag: false, // 不查询近3天有销量
				};
				console.log(`调用listOfItemRelationPlatformItemViewForSplit，第${index + 1}批，参数:`, params);
				const result = await listOfItemRelationPlatformItemViewForSplit(params);
				console.log(`第${index + 1}批结果:`, result);
				return result;
			} finally {
				semaphore.release(); // 释放信号量
			}
		});

		// 等待所有请求完成
		const batchResults = await Promise.all(requestPromises);

		console.log('所有批次结果:', batchResults);

		// 第四步：按照numIid顺序合并结果
		const mergedList = [];
		const resultMap = new Map();

		// 将所有结果按numIid建立映射
		batchResults.forEach(result => {
			if (result?.list) {
				result.list.forEach(item => {
					resultMap.set(item.numIid, item);
				});
			}
		});

		// 按照原始numIid顺序重新排列
		allNumIids.forEach(numIid => {
			const item = resultMap.get(numIid);
			if (item) {
				mergedList.push(item);
			}
		});

		// 返回合并后的结果，保持原有的分页信息
		const finalResult = {
			...numIidData,
			list: mergedList
		};

		console.log('返回的最终结果:', finalResult);
		return finalResult;

	} catch (error) {
		console.error('分页优化请求失败:', error);
		throw error;
	}
};

/**
 * 货品视角分页优化处理工具函数
 * 当分页数量>=阈值时，使用分批请求优化性能
 * 
 * @param searchParams 搜索参数
 * @param pageSize 分页大小
 * @param options 配置选项
 * @param options.threshold 触发分批处理的阈值，默认100
 * @param options.batchSize 每批处理的数量，默认50
 * @param options.concurrentLimit 并发请求限制，默认2
 * @returns Promise<ItemSysItemListOfItemRelationSysTemItemViewResponse>
 */
export const fetchSysItemViewWithPaginationOptimization = async(
	searchParams: ItemSysItemListOfItemRelationSysTemItemViewRequest,
	pageSize: number,
	options: {
        threshold?: number;
        batchSize?: number;
        concurrentLimit?: number;
    } = {}
): Promise<ItemSysItemListOfItemRelationSysTemItemViewResponse> => {
	const {
		threshold = 100, // 多少条以上才分批
		batchSize = 50, // 每批条数
		concurrentLimit = 2, // 并发数量
	} = options;

	console.log('货品视角分页优化函数调用:', { pageSize, threshold, searchParams });

	// 如果分页数量小于阈值，使用原有逻辑
	if (pageSize < threshold) {
		console.log('使用原有逻辑，直接调用原接口');
		return ItemSysItemListOfItemRelationSysTemItemViewApi(searchParams);
	}

	try {
		// 第一步：获取sysItemId列表
		const sysItemData = await ItemSysItemListOfItemRelationSysTemItemViewApi({
			...searchParams,
			returnSku: false, // 设置为false，表示只返回ID列表
		});

		if (!sysItemData?.list?.length) {
			console.log('第一步没有数据，直接返回');
			return sysItemData;
		}

		// 提取所有sysItemId
		const allSysItemIds = sysItemData.list.map(item => item.sysItemId);
		console.log('提取到的sysItemIds:', allSysItemIds);

		// 第二步：分批处理
		const batches = [];
		for (let i = 0; i < allSysItemIds.length; i += batchSize) {
			batches.push(allSysItemIds.slice(i, i + batchSize));
		}

		// 第三步：使用信号量控制并发请求
		const semaphore = new Semaphore(concurrentLimit);

		const requestPromises = batches.map(async(batch, index) => {
			await semaphore.acquire(); // 获取信号量
			
			try {
				const params = {
					...searchParams,
					sysItemIds: batch,
					pageNo: 1,
					pageSize: batch.length,
					notCount: true, // 不统计总数
					tradeWaitSendFlag: false, // 不查询待发货订单
					trade3DaySalesVolumeFlag: false, // 不查询近3天有销量
				};
				console.log(`调用ItemSysItemListOfItemRelationSysTemItemViewSplitApi，第${index + 1}批，参数:`, params);
				const result = await ItemSysItemListOfItemRelationSysTemItemViewSplitApi(params);
				console.log(`第${index + 1}批结果:`, result);
				return result;
			} finally {
				semaphore.release(); // 释放信号量
			}
		});

		// 等待所有请求完成
		const batchResults = await Promise.all(requestPromises);
		console.log('所有批次结果:', batchResults);

		// 第四步：按照sysItemId顺序合并结果
		const mergedList = [];
		const resultMap = new Map();

		// 将所有结果按sysItemId建立映射
		batchResults.forEach(result => {
			if (result?.list) {
				result.list.forEach(item => {
					resultMap.set(item.sysItemId, item);
				});
			}
		});

		// 按照原始sysItemId顺序重新排列
		allSysItemIds.forEach(sysItemId => {
			const item = resultMap.get(sysItemId);
			if (item) {
				mergedList.push(item);
			}
		});

		// 返回合并后的结果，保持原有的分页信息
		const finalResult = {
			...sysItemData,
			list: mergedList
		};

		console.log('货品视角返回的最终结果:', finalResult);
		return finalResult;

	} catch (error) {
		console.error('货品视角分页优化请求失败:', error);
		throw error;
	}
}; 