import React, { useEffect, useRef, useState } from "react";
import { AutoComplete, Typography, Button, Input, Spin } from "antd";
import { useRequest } from "ahooks";
import { observer } from "mobx-react";
import { ItemSysSkuGetSysSkuPageListApi } from "@/apis/warehouse/system";
import WaresInfo from "@/components-biz/WaresInfo";
import s from './index.module.scss';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";


const SearchWithSkuOuterId = ({ onSkuOuterIdChange, onSkuOuterIdBind, skuOuterId }) => {
	const [searchValue, setSearchValue] = useState("");
	const [dataSource, setDataSource] = useState([]);
	const visibleRef = useRef(false);
	const isBindType = useRef(false);
	const { loading, runAsync } = useRequest(ItemSysSkuGetSysSkuPageListApi, { manual: true, debounceWait: 200 });
	useEffect(() => {
		setSearchValue(skuOuterId);
	}, [skuOuterId]);
	const handleSelectBindItem = (item) => {
		console.log('item::绑定', item);
		isBindType.current = true;
		console.log('item::onSkuOuterIdChange4444', isBindType.current);
		sendPoint(Pointer.商品_普通货品_货品规格_快速绑定货品规格);
		setSearchValue(item.skuOuterId);
		onSkuOuterIdBind(item);
	};

	const onSearch = (value) => {
		isBindType.current = false;
		setSearchValue(value);
		searchFunc(value);
	};

	const searchFunc = (value) => {
		runAsync({
			skuOuterId: value,
			blurSearch: true,
			pageNo: 1,
			pageSize: 80,
		}, 'getSearchResult').then((res) => {
			setDataSource(res?.list || []);
		}).catch((err) => {
			console.log("ItemSysSkuGetSysSkuPageListApi err:", err);
		});
	};
	const dropdownRender = () => {
		return (
			<Spin spinning={ loading }>
				<div style={ { position: 'relative', zIndex: 99 } }>
					<div className={ `${s.searchWithSkuOuterIdWrap}` } style={ { maxHeight: 300, overflowY: "auto", minWidth: '389px' } }>
						{
							dataSource?.length ? dataSource?.map((item) => {
								return (
									<div
										className="r-flex r-pd-8 r-jc-sb r-ai-c"
										style={ { borderBottom: "1px solid rgba(0, 0, 0, 0.06)" } }
										key={ item.skuOuterId }
										onClick={ () => { handleSelectBindItem(item); } }
									>
										<div className="r-flex r-jc-fs">
											<WaresInfo
												onlyImg
												outerId={ item.outerId }
												imgSize={ 48 }
												imgUrl={ item["picUrl"] }
												wareName={ item["itemAlias"] }
												skuName={ item["skuName"] }
											/>
											<div style={ { width: 270, marginLeft: 8 } } className={ s.ellipsisWrap }>
												<p className="r-fs-12 r-fc-black-65 r-fs-999" style={ { textAlign: 'left' } }>
													<Typography.Paragraph ellipsis={ { rows: 2, tooltip: `货品规格编码：${item.skuOuterId}` } }>
														货品规格编码：{item.skuOuterId}
													</Typography.Paragraph>
												</p>
												<p className={ `r-fs-12 r-fc-black-65 ${s.ellipsis} r-fs-666` }>
													<Typography.Paragraph ellipsis={ { rows: 1, tooltip: item.sysItemAlias } }>
														{item.sysItemAlias}
													</Typography.Paragraph>
												</p>
											</div>
										</div>
										<Button type="link" onClick={ (e) => { e.stopPropagation(); handleSelectBindItem(item); } } style={ { fontSize: 12, margin: 0, padding: 0, paddingLeft: 6, } }>快速绑定</Button>
									</div>
								);
							}) : <div style={ { textAlign: 'center' } }>未匹配到货品</div>
						}
					</div>
					<div className="r-c-999 r-fs-12 r-flex r-ai-c r-jc-c r-pt-4">以上为搜索推荐货品规格信息</div>
				</div>
			</Spin>
		);
	};
	return (
		<div className={ s.searchWithSkuOuterIdWrap }>
			<AutoComplete
				// open
				dropdownClassName={ s.searchWithSkuOuterId }
				onSearch={ (e) => { onSearch(e); } }
				notFoundContent={ (<div>未匹配到货品</div>) }
				getPopupContainer={ () => document.body }
				dropdownRender={ dropdownRender }
				size="small"
				value={ searchValue }
				onDropdownVisibleChange={ (visible) => {
					isBindType.current = false;
					setTimeout(() => {
						visibleRef.current = visible;
					}, 10);
					console.log('item::visible', visible);
					if (!visible) {
						// 延迟清空数据，避免视觉卡顿
						setTimeout(() => {
							setDataSource([]);
						}, 300);
					}
				} }
			>
				<Input
					size="small"
					onBlur={ (e) => {
						isBindType.current = false;
						console.log('item::onSkuOuterIdChange11111', isBindType.current);
						setTimeout(() => {
							console.log('item::onSkuOuterIdChange2222', isBindType.current);
							if (!isBindType.current) {
								console.log('item::onSkuOuterIdChange33333', isBindType.current);
								onSkuOuterIdChange(e, null);
							}
						}, 200);
					} }
					onFocus={ () => {
						searchValue && !visibleRef.current && searchFunc(searchValue);
					} }
					onPressEnter={ (e) => { e.stopPropagation(); e.preventDefault(); } }
					placeholder="规格编码"
				/>
			</AutoComplete>
		</div>
	);
};

export default observer(SearchWithSkuOuterId);
