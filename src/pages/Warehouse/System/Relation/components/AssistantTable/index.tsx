
import React, { useRef, useState, ReactNode, useEffect, useMemo } from 'react';
import { Button, Image, Popover, Select, Modal, Checkbox, Tooltip } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/lib/table';
import cs from 'classnames';
import { useSetState } from 'ahooks';
import { cloneDeep } from 'lodash';
import s from './index.module.scss';
import SyncWaresModal from "@/pages/Warehouse/StockSync/HandStockSync/components/SyncWaresModal";
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import ProductOnlineListModal from "@/components-biz/Product/OnlineList/Model";
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { GreenButton, RedButton } from '@/components/Botton';
import SearchTable from '@/components/SearchTable';
import { PLAT_MAP, DEFAULT_IMG, PLAT_XHS, PLAT_JD, PLAT_YZ } from '@/constants';
import {
	ItemSysItemDeleteItemRelationWithBatchApi,
	ItemSysItemSaveItemRelationApi
} from '@/apis/warehouse/system';
import {
	ItemSysItemListOfItemRelationSysTemItemViewRequest,
	ItemSysItemListOfItemRelationSysTemItemViewResponse,
	ItemSysItemDeleteItemRelationWithBatchRequest,
	ItemSysItemSaveItemRelationRequest
} from '@/types/schemas/warehouse/system';
import Input from '@/components/Input/InputSearch';
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import CombineTag from '@/components-biz/Trade/CombineTag';
import message from "@/components/message";
import Icon from '@/components/Icon';
import { getPlatformDetailLink } from '@/pages/AfterSale/TradeList/utils';
import { tradeStore } from '@/stores';
import { belongStartAndEnd, compareRowId } from '@/pages/Warehouse/utils';
import FunctionPermissionCheck, { FunctionPermissionEnum } from '@/utils/permissionCheck/functionPermissionCheck';
import UserStore from '@/stores/user';
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import { isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import useGetState from '@/utils/hooks/useGetState';
import { fetchSysItemViewWithPaginationOptimization } from '@/pages/Warehouse/System/Relation/components/PlatformTable/utils/paginationOptimization';
import { getImageThumbnail } from '@/utils/img.scale';


interface confirmInfoProps {
  visible: boolean;
  params: ItemSysItemSaveItemRelationRequest;
  [key: string]: any;
}
const MAX_SHOW_COUNT:number = 3;
const { Option } = Select;
const { confirm } = Modal;
/**
 * 助手视角
 * @param props
 * @returns
 */
const AssistantTable: React.FC<{searchParam: string, expandNode?: any, visible?: boolean}> = props => {
	const { searchParam } = props;
	const [form] = useForm();
	const tableRef = useRef<SearchTableRefProps>();
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [syncWaresModalVisible, setSyncWaresModalVisible] = useState(false);
	const [confirmInfo, setConfirmInfo] = useSetState<confirmInfoProps>({
		visible: false,
		params: [],
		sysSkuOuterId: ''
	});
	const [batchToggleStatus, setBatchToggleStatus] = useState(false);
	const [oldSelectObj, setOldSelectObj] = useState({ index: "", checked: false });
	// 添加状态
	const [barCodeRepeatFlag, setBarCodeRepeatFlag, getBarCodeRepeatFlag] = useGetState(false);
	// 表单元素
	const FormFieldList: FormItemConfig[] = [
		// {
		// 	name: "platformInfo",
		// 	className: cs(s.condition2),
		// 	children: (
		// 		<ShopMultiSelect
		// 			isSendPoint
		// 			style={ { width: '100%', flex: "1 1 0%" } }
		// 			isHasHandPlat
		// 		/>
		// 	),
		// },
		{
			name: "itemAlias",
			className: 'r-mb-8',
			children: <Input placeholder="货品简称" />,
		},
		{
			name: 'skuOuterId',
			className: 'r-mb-8',
			children: <Input placeholder="货品规格编码" />
		},
		{
			name: 'skuName',
			className: 'r-mb-8',
			children: <Input placeholder="规格/规格别名" />
		},
		{
			name: 'itemNo',
			className: 'r-mb-8',
			children: <Input placeholder="货号" />
		},
		{
			name: 'barCode',
			className: 'r-mb-8',
			children: <Input placeholder="条形码" />
		},
		{
			name: 'relationException',
			className: 'r-mb-8',
			children: (
				<Select className={ cs('r-w-full') } placeholder="是否异常关系" size="small" style={ { width: 160 } }>
					<Option value={ 0 } key={ 0 }>全部</Option>
					<Option value={ 1 } key={ 1 }>异常</Option>
				</Select>)
		}
	];

	useEffect(() => {
		tradeStore.tradeListStore.keyDown();
	 return () => {
		 tradeStore.tradeListStore.cancelKeyDown();
	 };
	}, []);

	// group选择
	const onCheckedGroup = (e:CheckboxChangeEvent, record: any) => {
		setDataSource(prev => {
			prev.forEach(item => {
				if (item.groupId == record.groupId && !item.checkDisabled) {
					item.isChecked = e.target.checked;
				}
			});
			return [...prev];
		});
	};
	const dealShift = (record) => {
		const { rowId } = record;
		if (oldSelectObj.index) {
			const { index, checked } = oldSelectObj;
			const [startRowId, endRowId] = compareRowId(rowId, index);

			setDataSource(prev => {
				prev.forEach((item) => {
					if (belongStartAndEnd({ startRowId, endRowId, compareRowId: item.rowId }) && item.isItemShow && !item.checkDisabled) {
						item.isChecked = checked;
					}
				});
				return [...prev];
			});
		}
	};
	// 单选
	const onCheckedItem = (e:CheckboxChangeEvent, record: any) => {
		const { isShiftDown } = tradeStore.tradeListStore;
		if (!isShiftDown) {
			let checked = false;
			setDataSource(prev => {
				let prevIndex = prev.findIndex(d => d.rowId === record.rowId);
				if (prevIndex > -1) {
					prev[prevIndex].isChecked = e.target.checked;
					checked = e.target.checked;
				}
				return [...prev];
			});
			setOldSelectObj({
				index: record.rowId,
				checked
			});
		} else {
			dealShift(record);
		}
	};
	// 全选
	const onCheckAllChange = (e:CheckboxChangeEvent) => {
		setDataSource(prev => {
			prev.forEach(item => {
				if (!item.checkDisabled) {
					item.isChecked = e.target.checked;
				}
			});

			return [...prev];
		});
	};

	const checkedGroup = useMemo(() => {
		let filterList = dataSource.filter(item => !item.isCollapse);
		const tmp = { num: 0, checked: false, disabled: false, disNum: 0, indeterminate: false };
		if (filterList.length == 0) return { group_all: tmp };
		const map = {
			'group_all': { ...tmp, total: filterList.length }
		};
		filterList.forEach(item => {
			if (!map[item.groupId]) map[item.groupId] = { ...tmp, total: item.colSpan };

			if (item.checkDisabled) {
				map[item.groupId].disNum += 1;
				map['group_all'].disNum += 1;
			}
			if (item.isChecked) {
				map[item.groupId].num++;
				map['group_all'].num++;
			}
		});
		for (let key in map) {
			const { disNum, total, num } = map[key];
			if (disNum == total) {
				map[key].disabled = true;
			}
			if (num == total || (num > 0 && num + disNum == total)) {
				map[key].checked = true;
			}
			if (num > 0 && num + disNum < total) {
				map[key].indeterminate = true;
			}
		}
		return map;
	}, [dataSource]);

	const selectedRows = useMemo(() => {
		return dataSource.filter(item => !item.isCollapse && item.isChecked);
	}, [dataSource]);


	// 展开更多货品
	const toggleList = (row: any) => {
		dataSource.forEach((i, index) => {
			if (i.groupId === row.groupId) {
				i.collapseShow = !i.collapseShow;
				if (i.collapseShow) {
					i.isItemShow = true;
				} else if (index < MAX_SHOW_COUNT) {
					i.isItemShow = true;
				} else {
					i.isItemShow = false;
				}
			}
		});
		setDataSource([...dataSource]);
	};

	const batchToggleList = () => {
		sendPoint(Pointer.商品_货品与商品关系_批量展开收起);
		dataSource.forEach(i => {
			i.collapseShow = !batchToggleStatus;
		});
		setDataSource([...dataSource]);
		setBatchToggleStatus(!batchToggleStatus);
	};

	const onClickImg = (platform, detailId) => {
		if (!UserStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息)) return;
		const goodsDetailUrl = getPlatformDetailLink(platform, detailId);
		if (goodsDetailUrl) {
			window.open(goodsDetailUrl, '_blank');
		}
	};

	// 表格列定义，与AntdTable使用一致
	const columns: ColumnsType<unknown> = [
		{
			title: (
				<Checkbox
					checked={ checkedGroup.group_all.checked }
					disabled={ checkedGroup.group_all.disabled }
					indeterminate={ checkedGroup.group_all.indeterminate }
					onChange={ onCheckAllChange }
				/>
			),
			align: 'center',
			width: 30,
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const { colSpan } = row;
				const checkNode = (
					<Checkbox
						disabled={ checkedGroup[row.groupId].disabled }
						onChange={ e => onCheckedGroup(e, row) }
						checked={ checkedGroup[row.groupId].checked }
						indeterminate={ checkedGroup[row.groupId].indeterminate }
					/>
				);
				return {
					children: colSpan && checkNode,
				};
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.colSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '货品简称',
			width: 180,
			dataIndex: 'sysItemAlias',
			key: 'sysItemAlias',
			className: cs(s.noline, 'r-l-preWrap'),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0], index) => {
				return {
					children: row.colSpan && (
						<>
							<div className={ cs('r-fs-12', 'r-fc-black') }>{row.sysItemAlias}</div>
							<div className={ cs('r-fs-12', 'r-fc-black-65') }>{row.outerId}</div>
						</>
					),
					props: {
						// rowSpan: row.colSpan || 0,
					},
				};
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.colSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '图片',
			width: 60,
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const popContent:ReactNode = (<Image width={ 200 } height={ 200 } src={ row.picUrl || DEFAULT_IMG } fallback={ DEFAULT_IMG } preview={ false } />);
				return {
					children: row.colSpan2 && (
						<div>
							<Popover placement="right" content={ popContent } >
								<Image
									width={ 48 }
									height={ 48 }
									src={ getImageThumbnail({
										noScale: false,
										url: row.picUrl || DEFAULT_IMG,
										width: 48,
										height: 48
									}) }
									fallback={ DEFAULT_IMG }
									preview={ false }
								/>
							</Popover>
						</div>
					),
					props: {
						// rowSpan: row.colSpan2 || 0,
					},
				};
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.colSpan2 || row.isCollapse ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '规格/货品规格编码/条码/货号',
			width: 200,
			className: cs('r-l-preWrap', 'table-right-border'),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				return {
					children: row.colSpan2 && (
						<div style={ { wordBreak: 'break-all' } } className={ cs('r-fs-12', 'r-fc-black-65') }>
							<div className="r-flex r-ai-c"><CombineTag visible={ row.isCombination === 1 } /> {row.sysSkuName}</div>
							<div>货品规格编码：{row.skuOuterId}</div>
							<div>条码：{row.barCode}</div>
							<div>货号：{row.itemNo}</div>
						</div>
					),
					props: {
						// rowSpan: row.colSpan2 || 0,
					},
				};
			},
			onCell: (row:any, index:number) => {
				return {
					className: row.isCollapse ? s['not-show-next-cell'] : '',
					style: row.isCollapse ? { borderRight: 0 } : row.colSpan2 ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '',
			align: 'center',
			width: 40,
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				return {
					children: (
						<Checkbox
							disabled={ row.checkDisabled }
							onChange={ e => onCheckedItem(e, row) }
							checked={ row.isChecked }
						/>
					),
				};
			}
		},
		{
			title: '图片',
			width: 60,
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const { hasplatformItem, platformItem = {} } = row;
				const { picUrl, platform, numIid, skuId, skuUuid } = platformItem;
				const popContent:ReactNode = (<Image width={ 200 } height={ 200 } src={ picUrl || DEFAULT_IMG } fallback={ DEFAULT_IMG } preview={ false } />);
				let id = numIid;
				if ([PLAT_XHS, PLAT_JD].includes(platform)) {
					id = skuId;
				}
				if ([PLAT_YZ].includes(platform)) {
					id = skuUuid;
				}
				return {
					children: (
						hasplatformItem
							? (
								<Popover placement="right" content={ popContent } >
									<Image
										className="r-pointer"
										width={ 48 }
										height={ 48 }
										src={ getImageThumbnail({
											noScale: false,
											url: picUrl || DEFAULT_IMG,
											width: 48,
											height: 48
										}) }
										fallback={ DEFAULT_IMG }
										preview={ false }
										onClick={ () => {
											if (!UserStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(row)) {
												return;
											} else {
												onClickImg(platform, id);
											}
										} }
									/>
								</Popover>
							) : ''
					)
				};
			}
		},
		{
			title: '商品名称',
			width: 200,
			className: cs(s.noline, 'r-l-preWrap'),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const { hasplatformItem, platformItem = {} } = row;
				const { platform, sellerNick, relationStatus, title, numIid } = platformItem;
				const NORMAL = (
					<div>
						<div className={ cs('r-fs-12', 'r-fc-black-65') }>{hasplatformItem ? title : ''}</div>
						<div className="r-mt-4  r-fc-black-65">商品ID: { numIid }</div>
					</div>
				);
				const EXPIRE = (
					<div className={ cs('r-c-error', 'r-fs-12', 'r-fw-500') }>
						<div>已失效商品</div>
						<div>该商品已在【{PLAT_MAP[platform] || platform}&gt;{sellerNick}】店铺中新增了规格!</div>
					</div>
				);
				const DELETE = (
					<div className={ cs('r-c-error', 'r-fs-12', 'r-fw-500') }>
						<div>已失效商品</div>
						<div>该商品已在【{PLAT_MAP[platform] || platform}&gt;{sellerNick}】店铺中删除!</div>
					</div>
				);
				const render1 = {
					children: (
						<span
							onClick={ () => toggleList(row) }
							className={ s.collapseBtn }
							data-point={ row?.collapseShow ? Pointer['商品_货品与商品关系_收起货品规格'] : Pointer['商品_货品与商品关系_展开更多货品规格'] }
						>
							{row?.collapseShow ? '收起' : '展开更多'}货品规格
						</span>),
					props: { colSpan: 1 }
				};
				const render2 = {
					children: { EXPIRE, DELETE, NORMAL }[relationStatus],
					props: {
						colSpan: { EXPIRE: true, DELETE: true }[relationStatus] ? 3 : 1
					},
				};
				return row.isCollapse ? render1 : render2;
			}
		},
		{
			title: '规格/条码',
			width: 160,
			className: cs(s.noline, 'r-l-preWrap'),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const { hasplatformItem, platformItem = {} } = row;
				const { relationStatus } = platformItem;
				return {
					children: (
						hasplatformItem ? (
							<div style={ { wordBreak: 'break-all' } } className={ cs('r-fs-12', 'r-fc-black-65') }>
								<div>{platformItem.skuName}</div>
								<div>{platformItem.barCode}</div>
							</div>
						) : ''
					),
					props: {
						colSpan: { EXPIRE: true, DELETE: true }[relationStatus] ? 0 : 1
					},
				};
			}
		},
		{
			title: '规格编码',
			align: "center",
			width: 110,
			className: cs(s.noline, 'r-ta-c'),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const { hasplatformItem, platformItem = {}, skuOuterId } = row;
				const { skuOuterId: platformSkuOuterId, relationStatus } = platformItem;
				const hasTips = platformSkuOuterId && (skuOuterId === platformSkuOuterId);
				const renderText = {
					true: (
						<>
							<div className={ cs('r-fs-12', hasTips ? 'r-fc-black-65' : s.emptyText) }>
								{platformSkuOuterId || '规格编码为空'}
							</div>
							{!hasTips && platformSkuOuterId && <div style={ { textAlign: 'left' } } className={ cs('r-fs-12', 'r-c-999') }>与关联货品的编码不同！</div>}
						</>
					),
					fale: (<></>),
				};
				return {
					children: (renderText[hasplatformItem]),
					props: {
						colSpan: { EXPIRE: true, DELETE: true }[relationStatus] ? 0 : 1
					},
				};
			}
		},
		{
			title: '平台',
			width: 60,
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const { hasplatformItem } = row;
				return <div className={ cs('r-fs-12', 'r-fc-black-65') }>{hasplatformItem ? PLAT_MAP[row.platformItem.platform] : ''}</div>;
			}
		},
		{
			title: '店铺',
			width: 100,
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const { hasplatformItem } = row;
				return <div className={ cs('r-fs-12', 'r-fc-black-65') }>{hasplatformItem ? row.platformItem.sellerNick : ''}</div>;
			}
		},
		{
			title: (
				<div className="r-flex">
					操作
					<Tooltip title="展开/收起所有货品规格">
						<div>
							<Icon onClick={ batchToggleList } size={ 14 } style={ { color: '#FD8204' } } type={ batchToggleStatus ? 'shouqi' : 'zhankai' } />
						</div>
					</Tooltip>
				</div>
			),
			width: 80,
			align: "center",
			className: cs('table-right-border'),
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const { hasplatformItem, sysItemId, sysSkuId, platformItem = {} } = row;
				const { skuId: platformSkuId, numIid: platformNumIid, platform, sellerId } = platformItem;
				const params:ItemSysItemDeleteItemRelationWithBatchRequest = {
					reqDeleteItemRelationDTOs: [{
						sysItemId,
						sysSkuId,
						platformSkuId,
						platformNumIid,
						platform,
						sellerId
					}]
				};
				return (
					hasplatformItem ? (
						<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.解除批量解除 }>
							<RedButton
								type="primary"
								size="small"
								disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.解除批量解除) }
								data-point={ Pointer['商品_货品与商品关系_快递助手货品视角_解除'] }
								onClick={ () => { removePlateFormItem(params, '确定解除该平台商品与快递助手货品的关联关系吗？'); } }
							>
								<Tooltip title="解除当前已绑定的本地货品">解除</Tooltip>
							</RedButton>
						</FunctionPermissionCheck>
					) : ''
				);
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.isCollapse ? { borderRight: 0 } : { }
				};
			}
		},
		{
			width: 80,
			align: "center",
			render: (text, row: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]) => {
				const { sysItemId, sysSkuId, relationPlatformItemList, platformItem = {} } = row;
				const removeParams:ItemSysItemDeleteItemRelationWithBatchRequest = {
					sysItemId,
					sysSkuId,
					deleteAll: true
				};
				return {
					children: row.colSpan2 && (
						<>
							<div>
								<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.商品添加 }>
									<Tooltip placement="topRight" title="指定本地货品绑定平台商品">
										<Button disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.商品添加) } size="small" type="primary">
											<ProductOnlineListModal
												type="checkbox"
												beforeOk={ (selectRows:any) => {
													return new Promise((resolve, reject) => {
														setConfirmInfo({ resolve, reject, selectRows, row, sysSkuOuterId: row.skuOuterId });
														try {
															addBeforeConfim(row, selectRows);
														} catch (e) {
															reject();
														}
													});
												} }
											>	添加
											</ProductOnlineListModal>
										</Button>
									</Tooltip>
								</FunctionPermissionCheck>
							</div>
							<div className={ cs('r-mt-8') }>
								{relationPlatformItemList.length > 1
									&& (
										<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.解除批量解除 }>
											<Button
												size="small"
												disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.解除批量解除) }
												data-point={ Pointer['商品_货品与商品关系_快递助手货品视角_全解除'] }
												onClick={ () => {	removePlateFormItem(removeParams, '确定解除该平台商品与快递助手货品的关联关系吗？'); } }
											>
												全解除
											</Button>
										</FunctionPermissionCheck>
									)}
							</div>
						</>
					),
					props: {
						// rowSpan: row.colSpan2 || 0,
					},
				};
			},
			onCell: (row:any) => {
				return {
					style: row.colSpan2 || row.isCollapse ? {} : { borderTop: 0 }
				};
			}
		}
	];

	// 接口查询、查询参数重装
	const fetchSystemList = (info: ItemSysItemListOfItemRelationSysTemItemViewRequest) => {
		for (let key in info) {
			if (typeof info[key] === 'string') {
				info[key] = info[key].trim();
			}
		}
		const { itemAlias, skuOuterId, pageNo, pageSize, barCode, skuName, itemNo, relationException, platformInfo = {} } = info;
		const search: ItemSysItemListOfItemRelationSysTemItemViewRequest = {
			platformList: platformInfo.plats,
			sellerIdList: platformInfo.plat_sellerIds?.map((i: string) => i.split('_')[1]),
			itemAlias,
			skuOuterId,
			skuName,
			itemNo,
			barCode,
			pageNo,
			pageSize,
		};
		if (relationException) {
			search.relationException = true;
		}

		search.barCodeRepeatFlag = getBarCodeRepeatFlag();

		return fetchSysItemViewWithPaginationOptimization(search, pageSize, { threshold: 100 });
	};

	// 基于平台商品拆分维度
	const responseAdapter = (data: ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]) => {
		let platformDataList:any = [];
		let dataSourceIndex = 0;
		data.list.forEach((item, i:any) => {
			let outerNum = 0;
			const addList:any = [];
			item.sysItemSkuList.forEach((sku:any, index:number) => {
				outerNum += sku.relationPlatformItemList.length || 1;
				if (sku?.relationPlatformItemList?.length) {
					sku.relationPlatformItemList.forEach((platformItem:any, idx:number) => {
						let one = {
							rowId: `${i}`,
							hasplatformItem: true,
							sysItemIndex: i,
							skuIndex: index,
							platformItemIndex: idx,
							groupId: `groupId_${item.sysItemId}__${i}`,
							checkDisabled: false,
							...(idx === 0 ? { colSpan2: sku.relationPlatformItemList.length } : {}),
							...item,
							...sku,
							platformItem,
							collapseShow: dataSource[dataSourceIndex]?.collapseShow,
							isItemShow: idx < MAX_SHOW_COUNT,
						};
						if (index === 0 && idx === 0 && item.sysItemSkuList.length) {
						// 第一条数据关联了商品
							let num = 0;
							item.sysItemSkuList.forEach((k, j) => {
								num += k.relationPlatformItemList.length || 1;
							});
							one.colSpan = num;
						}
						addList.push(one);
					});
				} else {
					let one = {
						rowId: `${i}`,
						groupId: `groupId_${item.sysItemId}__${i}`,
						hasplatformItem: false,
						colSpan2: 1,
						sysItemIndex: i,
						skuIndex: index,
						checkDisabled: true,
						...(index === 0 ? { colSpan: item.sysItemSkuList.length } : {}),
						...item,
						...sku,
						collapseShow: dataSource[dataSourceIndex]?.collapseShow,
						isItemShow: index < MAX_SHOW_COUNT,
					};
					if (index === 0) {
						// 第一条数据是空商品
						let num = 0;
						item.sysItemSkuList.forEach((k, j) => {
							num += k.relationPlatformItemList.length || 1;
						});
						one.colSpan = num;
					}
					addList.push(one);
				}
				dataSourceIndex++;
			});
			// 展开规格的点击判断
			if (addList.length > MAX_SHOW_COUNT) {
				addList.push({
					isCollapse: true,
					groupId: addList[0].groupId,
					rowId: i,
					collapseShow: addList[addList.length - 1].collapseShow,
				});
			}
			addList.forEach((item:any, index:number) => {
				item.rowId += `_${index}_${outerNum}`;
				item['collapseIndex'] = index;
			});
			platformDataList = [...platformDataList, ...addList];
		});
		console.log('platformDataList', platformDataList);
		setDataSource(platformDataList);
		return {
			list: platformDataList,
			total: data.total
		};
	};

	const tableRowDataSource = useMemo(() => {
		return dataSource.filter(i => i.isCollapse || i.collapseIndex < MAX_SHOW_COUNT || i.collapseShow);
	}, [dataSource]);

	const betchRemove = () => {
		const params:ItemSysItemDeleteItemRelationWithBatchRequest = {
			reqDeleteItemRelationDTOs: []
		};
		selectedRows.forEach((row, index:number) => {
			const { platformItem = {}, sysItemId, sysSkuId } = row;
			const { skuId: platformSkuId, numIid: platformNumIid, platform, sellerId } = platformItem;
			params.reqDeleteItemRelationDTOs.push({
				sysItemId,
				sysSkuId,
				platformSkuId,
				platformNumIid,
				platform,
				sellerId
			});
		});
		removePlateFormItem(params, `确定要将这${selectedRows.length}种平台商品与快递助手货品解除关联关系吗？`);
	};

	const removePlateFormItem = (params:ItemSysItemDeleteItemRelationWithBatchRequest = {}, content:string) => {
		// 单个、批量、全部
		confirm({
			centered: true,
			title: '解除提示',
			icon: <ExclamationCircleOutlined />,
			content,
			okText: '确认解除',
			cancelText: '取消',
			onOk: async() => {
				await fetchRemove(params);
			}
		});
	};

	//	添加按钮处理
	const addBeforeConfim = async(row:any, selectRows:any) => {
		const params:ItemSysItemSaveItemRelationRequest = [];
		const { sysItemId, sysSkuId, skuOuterId: sysSkuOuterId } = row;
		let shouldConfirm = false;
		selectRows.forEach((item:any) => {
			const { platform, sellerId, numIid: platformNumIid } = item;
			const { skuId: platformSkuId, skuOuterId } = item.platformItemSkuList[0] || {};
			params.push({
				sysItemId,
				sysSkuId,
				platformSkuId,
				platformNumIid,
				platform,
				sellerId,
			});
			// 判断确认弹窗是否展示
			if (sysSkuOuterId !== skuOuterId) {
				shouldConfirm = true;
			}
		});
		if (shouldConfirm) {
			setConfirmInfo({ visible: true, params });
		} else {
			setConfirmInfo({ visible: false });
			await ItemSysItemSaveItemRelationApi(params);
			message.success('添加商品关联关系成功');
			tableRef.current.refresh();
		}
	};
	const bindSysItem = async(type:number) => {
		if (!type) {
			setConfirmInfo({ visible: false });
			return confirmInfo.reject();
		}
		let params = cloneDeep(confirmInfo.params);
		let isUploadSkuOuterId = 0;
		if (type === 1) {
			isUploadSkuOuterId = 1;
			params.forEach((item:any) => {
				item.sysSkuOuterId = confirmInfo.sysSkuOuterId;
				item.isUploadSkuOuterId = isUploadSkuOuterId;
			});
		}
		setConfirmInfo({ visible: false });
		await ItemSysItemSaveItemRelationApi(params);
		message.success('添加商品关联关系成功');
		tableRef.current.refresh();
		confirmInfo.resolve(true);
	};

	const fetchRemove = async(params:ItemSysItemDeleteItemRelationWithBatchRequest) => {
		await ItemSysItemDeleteItemRelationWithBatchApi(params);
		tableRef.current.refresh();
	};
	// 添加处理函数
	const onChangeBarCodeRepeat = (e) => {
		const { checked } = e.target;
		setBarCodeRepeatFlag((prev) => checked);
		setTimeout(() => {
			tableRef.current.refresh();
		}, 0);
	};
	useEffect(() => {
		if (searchParam) {
			form.setFieldsValue({ relationException: searchParam === 'abnormal' ? 1 : 0 });
		}
	}, [form, searchParam]);

	const topAdditionalFormNode = (
		<>
			<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.下载平台商品 }>
				<GreenButton disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.下载平台商品) } data-point={ Pointer['商品_货品与商品关系_同步商品'] } className={ cs('r-ml-8') } type="primary" onClick={ () => { setSyncWaresModalVisible(true); } } size="small">同步商品</GreenButton>
			</FunctionPermissionCheck>
			<Checkbox className="r-ml-14" checked={ getBarCodeRepeatFlag() } onChange={ (e) => onChangeBarCodeRepeat(e) }><span style={ { whiteSpace: 'nowrap' } }>条形码重复</span></Checkbox>
		</>

	);

	const additionalFormNode = (
		<>
			<Button type="primary" size="middle" data-point={ Pointer['商品_货品与商品关系_快递助手货品视角_批量解除'] } disabled={ !selectedRows.length } onClick={ betchRemove }>批量解除</Button>
		</>
	);
	useEffect(() => {
		props.visible && sendPoint(Pointer['商品_货品与商品关系_快递助手货品视角_展现']);
	}, [props.visible]);

	if (!props.visible) return null;
	return (
		<div >
			<SearchTable<ItemSysItemListOfItemRelationSysTemItemViewResponse["data"]["list"][0]>
				ref={ tableRef }
				form={ form }
				fetchData={ fetchSystemList }
				responseAdapter={ responseAdapter }
				searchBtnPoint={ Pointer['商品_货品与商品关系_快递助手货品视角_查询'] }
				searchBtnText="查询"
				resetBtnText="重置"
				additionalFormNode={ topAdditionalFormNode }
				showSearch
				showSearchToggle
				onReset={ () => {
					setBarCodeRepeatFlag(false);
				} }
				// fullWindowFixed
				rowFormConfig={ {
					expandNode: props.expandNode,
					formList: FormFieldList,
					colProps: {
						// span: 3
					},
					size: 'small'
				} }
				baseTableConfig={ {
					noGap: true,
					innerTableStyle: {
						paddingTop: 0
					},
					rowKey: 'rowId',
					groupId: 'groupId',
					cachePgination: true,
					pagination: false,
					expandContext: additionalFormNode,
					// fullWindowScroll: true,
					columns,
					dataSource: tableRowDataSource,
					components: {
						header: {
							row: (row:any) => {
								return (
									<>
										<tr>
											<th style={ { color: '#fff' } } className={ cs('r-bg-F5821F') } colSpan={ 4 }>快递助手货品</th>
											<th style={ { color: '#fff' } } className={ cs('r-bg-52C41A') } colSpan={ 10 }>平台商品</th>
										</tr>
										<tr>
											{row.children.map((item:any) => {
												return item;
											})}
										</tr>
									</>
								);
							}
						}
					},
				} }
			/>
			<SyncWaresModal
				visible={ syncWaresModalVisible }
				onCancel={ () => { setSyncWaresModalVisible(false); } }
			/>
			{/* 确认弹窗 */}
			<Modal
				centered
				visible={ confirmInfo.visible }
				title="系统提示"
				maskClosable={ false }
				zIndex={ 1001 }
				onCancel={ () => {
					bindSysItem(0);
				} }
				footer={ [
					<Button type="primary" onClick={ () => { bindSysItem(1); } } key="tongbu">
						同步到线上
					</Button>,
					<Button key="butongbu" onClick={ () => { bindSysItem(2); } } type="primary">
						不同步
					</Button>,
					<Button onClick={ () => { bindSysItem(0); } } key="cancel">
						取消
					</Button>
				] }
			>
				确定将线上平台商品的规格编码更换为{confirmInfo.sysSkuOuterId}
			</Modal>
		</div>
	);
};
export default AssistantTable;
