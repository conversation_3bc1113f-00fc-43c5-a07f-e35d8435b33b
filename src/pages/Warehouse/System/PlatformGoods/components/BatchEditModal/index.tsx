import React, { useState, useEffect, memo, useMemo, useCallback } from 'react';
import { Input, Form, Modal, Checkbox, Radio, InputNumber, Tooltip, Select, Space } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { EventEmitter } from 'events';
import { debounce } from 'lodash';
import s from './index.module.scss';
import UserStore from "@/stores/user";
import { weightUnit } from '@/pages/Index/Settings/System/constants';
import Icon from '@/components/Icon';
import { accMul } from '@/utils/util';
import { AutoCalculate } from '@/utils/enum/productManage';
import groupStore from '@/stores/warehouse/Group';
import { IBatchEditModalInfo } from '../../../Archives';
import { batchEditFuncName } from '../../../Archives/constant';

// 创建全局的事件总线
export const eventBus = new EventEmitter();

interface BatchEditModalProps<T> {
	visible: boolean;
	type: string;
	from?: string;
	batchEditModalInfo?: IBatchEditModalInfo;
	onOk?: (data: object, type: string) => void;
	onCancel?: () => void;
}

enum AddType {
	覆盖 =1,
	添加 = 2,
	替换 = 3,
	删除 = 4
}

export enum 抓取规则 {
	面料 = "fabric",
	成分 = "component",
	面料或成分 = "fabric_component",
	颜色 = "color",
	尺码 = "size",
	颜色或尺码 = "color_size",
	产地 = "producingArea",
	fabric = "面料",
	component = "成分",
	fabric_component= "面料或成分",
	color = "颜色",
	size= "尺码 ",
	color_size = "颜色或尺码",
	producingArea = "产地",
}

export enum 货品颜色和尺码 {
	sysColor = "_sysColor",
	sysSize = "_sysSize",
}

const getStr = (i) => `【${i}】`;

export enum addMap {
	商家编码 = 'outerId',
	货号 = 'itemNo',
	规格编码 = 'skuOuterId',
	规格名称 = 'skuName',
	货品规格编码 = 'skuOuterId',
	货品规格名称 = 'sysSkuName',
	货品编码 = 'outerId'
}

export enum addPosMap {
	覆盖 = 1,
	现有前 = 2,
	现有后 = 3,
}

enum editCustomTypeMap {
	自定义 = "custom",
	平台抓取 = "platform",
}

export const getNewContent = (content: string = '', row: { [x: string]: any; }, type: string | number) => {
	if (addArrObj[type]) {
		Object.keys(addMap).forEach(i => {
			const iStr = getStr(i);
			const iKey = addMap[i];
			content = content.replace(new RegExp(iStr, 'g'), row[iKey] || '');
		});
	}
	return content;
};
export const getAddRuleContent = (type: string = '', row: { [x: string]: any; }) => {
	let content = "";
	const ruleArr = type.split("_");
	content = row?.[ruleArr?.[0]] || row?.[ruleArr?.[1]] || "";
	return content;
};

export enum nameMap {
	sysItemAlias = '简称',
	sysSkuAlias = '别名',
	weight = '重量',
	classifyId="商品分类",
	costPrice = '成本价',
	salePrice = '售价',
	market = '市场',
	stall = '档口',
	stock_sysItemAlias = '简称',
	stock_sysSkuAlias = '别名',
	supplierName = '供应商',
	tagPrice = "吊牌价",
	combinedCostPrice = "组合成本价",
	combinedWeight = "组合重量",
	sysColor = '颜色',
	SysSize = '尺码',
	skuOuterId='编码',
	warehouseSlotName='货位',
	简称 = 'sysItemAlias',
	别名 = 'sysSkuAlias',
	重量 = 'weight',
	成本价 = 'costPrice',
	售价 = 'salePrice',
	市场 = 'market',
	档口 = 'stall',
	供应商 = 'supplierName',
	分类 = 'classifyId',
	库存版简称 = 'stock_sysItemAlias',
	库存版别名 = 'stock_sysSkuAlias',
	吊牌价 = "tagPrice",
	组合成本价 = "combinedCostPrice",
	组合重量 = "combinedWeight",
	颜色= 'sysColor',
	尺码 = 'sysSize',
	货品规格编码 = 'skuOuterId',
	货位 = 'warehouseSlotName',
}

const addArrObj = {
	sysItemAlias: ['商家编码', '货号'],
	sysSkuAlias: ['规格编码', '规格名称'],
	stall: ['商家编码', '规格编码'],
	stock_sysItemAlias: ['货品编码'],
	stock_sysSkuAlias: ['货品规格编码', '货品规格名称'],
};

type addtypeenum = '商家编码' | '货号' | '规格编码' | '规格名称' | '商家编码'


function BatchEditModal<T extends object = any>(props: BatchEditModalProps<T>) {
	const [form] = useForm();
	const { type, visible, onOk, onCancel, from, batchEditModalInfo = null } = props;
	const [disabled, setDisabled] = useState(false);
	const [formData, setFormData] = useState<any>({});


	const initialValues = { addType: type === batchEditFuncName.批量设置货品规格编码 ? AddType.添加 : addPosMap.覆盖 };

	useEffect(() => {
		if (visible) {
			form.setFieldsValue({ addType: addPosMap.覆盖 });
		}
		if (batchEditModalInfo?.[AutoCalculate.自动计算成本]) {
			form.setFieldsValue({ [AutoCalculate.自动计算成本]: true });
			setDisabled(true);
		}
		if (batchEditModalInfo?.[AutoCalculate.自动计算重量]) {
			form.setFieldsValue({ [AutoCalculate.自动计算重量]: true });
			setDisabled(true);
		}
		if (type === batchEditFuncName.批量设置自定义属性) {
			form.setFieldsValue({ editType: editCustomTypeMap.自定义 });
		}
		if ([batchEditFuncName.批量设置颜色, batchEditFuncName.批量设置尺码].includes(type)) {
			form.setFieldsValue({ editType: editCustomTypeMap.自定义 });
		}
	}, [form, visible, batchEditModalInfo, type]);

	const isKg = UserStore.userSetting?.weightUnit == weightUnit.显示kg;

	const inputWeightProps = isKg
		? {
			// Allow up to 6 decimal places for kg
			formatter: (value: any) => (value === '' || isNaN(value)
				? ''
				: parseFloat((Number(value) / 1000).toFixed(6))), // Divide by 1000 and format up to 6 decimal places
			parser: (value: any) => (value === '' || isNaN(value)
				? ''
				: parseFloat((Number(value) * 1000).toFixed(6))), // Multiply by 1000 and retain up to 6 decimal places
			precision: 6, // Set precision to 6 for kg
		}
		: {
			// Allow up to 3 decimal places for g
			formatter: (value: any) => (value === '' || isNaN(value)
				? ''
				: parseFloat(Number(value).toFixed(3))), // Format with 3 decimal places if it's not an empty string
			parser: (value: any) => (value === '' || isNaN(value)
				? ''
				: parseFloat(value)), // Parse to a float for consistency
			precision: 3, // Set precision to 3 for g
		};

	const handleClose = () => {
		onCancel && onCancel();
		form.resetFields();
		setErrorArrs([]);
		setDisabled(false);
	};

	// 使用 useCallback 包裹防抖函数
	const debouncedHandleOk = useCallback(
	    debounce(async() => {
	      
	            await form.validateFields();
	            const val = form.getFieldsValue();
	            if (batchEditFuncName.批量设置自定义属性 === type) {
	                val.addType = batchEditModalInfo?.attrKey;
	                val.attrName = batchEditModalInfo?.attrName;
	                val.isCustomAttr = true;
	            } else if ([batchEditFuncName.批量设置颜色, batchEditFuncName.批量设置尺码].includes(type)) {
	                val.addType = batchEditModalInfo?.attrKey;
	                val.attrName = batchEditModalInfo?.attrName;
	                if (val.editType == editCustomTypeMap.平台抓取) val.addRule = 货品颜色和尺码[type];
	            }
	            onOk && (await onOk(val, type));
	            if (![batchEditFuncName.批量设置货品规格编码].includes(type)) {
	                handleClose();
	            }
	        
	    }, 300),
	    [form, type, onOk] // 依赖项数组
	);

	const handleOk = () => {
	    debouncedHandleOk();
	};

	const onFormChange = (changedValues, allValues) => {
		const { addOther, addContent = '' } = allValues;
		setFormData({ ...allValues });
		if ([nameMap.组合成本价, nameMap.组合重量].includes(type)) {
			if (allValues["autoWeight"] || allValues["autoCostPrice"]) {
				setDisabled(true);
				form.setFieldsValue({ addContent: "" });
			} else {
				setDisabled(false);
			}
		}
		let newAddContent = String(addContent);
		if (changedValues.addOther) {
			addArrObj[type].forEach((i: addtypeenum) => {
				const iStr = getStr(i);
				// 勾选了，但是内容没有，添加到末尾
				if (addOther.includes(addMap[i]) && !addContent?.includes(iStr)) {
					newAddContent += iStr;
				}
				// 没有勾选了，但是内容有，过滤内容
				if (!addOther.includes(addMap[i]) && addContent?.includes(iStr)) {
					newAddContent = newAddContent.replace(new RegExp(iStr, 'g'), '');
				}
			});
			form.setFieldsValue({ addContent: newAddContent });
		}
	};

	const AddPosRadio = (
		<Radio.Group>
			<Radio value={ addPosMap.覆盖 }>覆盖</Radio>
			<Radio value={ addPosMap.现有前 }>现有{nameMap[type]}前</Radio>
			<Radio value={ addPosMap.现有后 }>现有{nameMap[type]}后</Radio>
		</Radio.Group>
	);

	const AddOtherCheckBox = (
		<Checkbox.Group>
			{addArrObj[type]?.map(i => <Checkbox key={ i } value={ addMap[i] }>{i}</Checkbox>)}
			{type === nameMap.库存版简称 && (
				<Tooltip title="若编码为空，则保留原简称">
					<span style={ { lineHeight: '24px' } }>
						<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
					</span>
				</Tooltip>
			)}
		</Checkbox.Group>
	);

	useEffect(() => {
		if (type === batchEditFuncName.批量设置货品规格编码) {
			form.setFieldsValue({ addType: AddType.添加 });
		}
	}, [type]);

	const operations = [
		{
			value: AddType.覆盖,
			label: AddType[AddType.覆盖], // 使用枚举 key 作为 label
			render: () => (
				<>
					<div>
						<div style={ { display: 'inline-block', color: 'rgba(0, 0, 0, 0.45)', fontSize: '14px' } }>
							快捷添加：
						</div>
						<Form.Item name="addOther" noStyle>
							<Checkbox.Group>
								{addArrObj[type]?.map((item) => (
									<Checkbox key={ item } value={ addMap[item] }>
										{item}
									</Checkbox>
								))}
							</Checkbox.Group>
						</Form.Item>
						{type === nameMap.库存版简称 && (
							<Tooltip title="若编码为空，则保留原简称">
								<span style={ { lineHeight: '24px' } }>
									<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
								</span>
							</Tooltip>
						)}
					</div>
					<Form.Item name="addContent">
						<Input.TextArea rows={ 2 } placeholder={ `请输入${[nameMap.简称, nameMap.库存版简称, batchEditFuncName.批量设置简称].includes(type) ? '新简称' : [nameMap.库存版别名, nameMap.别名, batchEditFuncName.批量设置别名].includes(type) ? '新规格别名' : ''}` } style={ { width: '100%' } } />
					</Form.Item>
					{[batchEditFuncName.批量设置别名, nameMap.别名].includes(type) && (
						<Form.Item name="filter" noStyle>
							<Checkbox.Group>
								<Checkbox key="filter-1" value="1">过滤【】以及中间的内容</Checkbox>
								<Checkbox key="filter-3" value="3">过滤（）以及中间的内容</Checkbox>
								<Checkbox key="filter-2" value="2">过滤中英文逗号</Checkbox>
							</Checkbox.Group>
						</Form.Item>
					)}
				</>
			),
		},
		{
			value: AddType.添加,
			label: AddType[AddType.添加], // 动态取枚举的key
			render: () => (
				<Form.Item style={ { display: 'flex', alignItems: 'center', marginBottom: 'unset', flex: 1 } }>
					<Input.Group compact>
						<Form.Item name="prefix" noStyle>
							<Input placeholder="前缀" style={ { width: '220px' } } />
						</Form.Item>
						<span style={ { verticalAlign: 'sub', marginLeft: '6px', marginRight: '6px' } }> + 原{nameMap[type]} +</span>
						<Form.Item name="suffix" noStyle>
							<Input placeholder="后缀" style={ { width: '220px' } } />
						</Form.Item>
					</Input.Group>
				</Form.Item>
			),
		},
		{
			value: AddType.替换,
			label: AddType[AddType.替换], // 动态取枚举的key
			render: () => (
				<Form.Item style={ { display: 'flex', alignItems: 'center', marginBottom: 'unset', flex: 1 } }>
					<Input.Group compact>
						<Form.Item name="replaceKeyword" noStyle>
							<Input placeholder="请输入要替换的关键词" style={ { width: '220px' } } />
						</Form.Item>
						<span style={ { verticalAlign: 'sub', marginLeft: '20px', marginRight: '20px' } }>替换为</span>
						<Form.Item name="replaceWith" noStyle>
							<Input placeholder="替换为" style={ { width: '220px' } } />
						</Form.Item>
					</Input.Group>
				</Form.Item>
			),
		},
		{
			value: AddType.删除,
			label: AddType[AddType.删除],
			render: () => (
				<Form.Item name="deleteKeyword" style={ { display: 'flex', alignItems: 'center', marginBottom: 'unset', flex: 1 } }>
					<Input placeholder="请输入要删除的关键词" style={ { width: '100%' } } />
				</Form.Item>
			),
		}
	];
	const handleRadioChange = (e) => {
		const selectedType = e.target.value;

		// 定义每种操作需要清除的字段
		const fieldsToClear = {
			[AddType.覆盖]: ['prefix', 'suffix', 'replaceKeyword', 'deleteKeyword', 'replaceWith'],
			[AddType.添加]: ['addOther', 'addContent', 'replaceKeyword', 'deleteKeyword', 'replaceWith'],
			[AddType.替换]: ['addOther', 'addContent', 'prefix', 'suffix', 'deleteKeyword'],
			[AddType.删除]: ['addOther', 'addContent', 'prefix', 'replaceKeyword', 'suffix', 'replaceWith']
		};

		// 清除选中类型不需要的字段
		const clearValues = fieldsToClear[selectedType].reduce((acc, field) => {
			acc[field] = '';
			return acc;
		}, {});

		// 清除不相关的 Form.Item 的值
		form.setFieldsValue(clearValues);
	};

	const formItemMap = {
		sysItemAlias: [
			// {
			// 	name: "addOther",
			// 	label: "快捷添加",
			// 	valuePropName: "checked",
			// 	children: AddOtherCheckBox,
			// }, {
			// 	name: "addType",
			// 	label: "添加方式",
			// 	children: AddPosRadio,
			// }, {
			// 	name: "addContent",
			// 	label: "简称内容",
			// 	children: (
			// 		<Input.TextArea maxLength={ 60 } rows={ 2 } placeholder="新简称" />
			// 	),
			// }
			{
				name: "addType",
				children: (
					<Radio.Group onChange={ handleRadioChange }>
						{operations.map((op) => (
							<div style={ { display: 'flex', marginTop: '12px' } } key={ op.value }>
								<Radio value={ op.value } style={ { display: 'flex' } }>
									<strong>{op.label}</strong>
								</Radio>
								<div style={ { flex: 1 } }>
									{op.render()}
								</div>
							</div>
						))}
					</Radio.Group>),
			}
		],
		stock_sysItemAlias: [
			{
				name: "addType",
				children: (
					<Radio.Group onChange={ handleRadioChange }>
						{operations.map((op) => (
							<div style={ { display: 'flex', marginTop: '12px' } } key={ op.value }>
								<Radio value={ op.value } style={ { display: 'flex' } }>
									<strong>{op.label}</strong>
								</Radio>
								<div style={ { flex: 1 } }>
									{op.render()}
								</div>
							</div>
						))}
					</Radio.Group>),
			}
		],
		stock_sysSkuAlias: [
			{
				name: "addType",
				children: (
					<Radio.Group onChange={ handleRadioChange }>
						{operations.map((op) => (
							<div style={ { display: 'flex', marginTop: '12px' } } key={ op.value }>
								<Radio value={ op.value } style={ { display: 'flex' } }>
									<strong>{op.label}</strong>
								</Radio>
								<div style={ { flex: 1 } }>
									{op.render()}
								</div>
							</div>
						))}
					</Radio.Group>),
			}
		],
		sysSkuAlias: [
			// {
			// 	name: "addOther",
			// 	label: "快捷添加",
			// 	children: AddOtherCheckBox,
			// }, {
			// 	name: "addType",
			// 	label: "添加方式",
			// 	children: AddPosRadio,
			// }, {
			// 	name: "addContent",
			// 	label: "别名内容",
			// 	children: (
			// 		<Input.TextArea maxLength={ 60 } rows={ 2 } placeholder="新规格别名" />
			// 	),
			// }
			{
				name: "addType",
				children: (
					<Radio.Group onChange={ handleRadioChange }>
						{operations.map((op) => (
							<div style={ { display: 'flex', marginTop: '12px' } } key={ op.value }>
								<Radio value={ op.value } style={ { display: 'flex' } }>
									<strong>{op.label}</strong>
								</Radio>
								<div style={ { flex: 1 } }>
									{op.render()}
								</div>
							</div>
						))}
					</Radio.Group>),
			}
		],
		weight: [
			{
				name: "addContent",
				label: `重量(${isKg ? 'kg' : 'g'})`,
				children: (
					<InputNumber
						min={ 0 }
						max={ 99999999 }
						{ ...inputWeightProps }
						style={ { width: '70%' } }
						disabled={ disabled }
					/>
				),
			}
		],
		classifyId: [
			{
				name: "addContent",
				label: `统一修改为`,
				children: (
					<Select>
						{groupStore?.groupList?.map(s => <Select.Option value={ s.classifyId } key={ s.classifyId }>{ s.classifyName}</Select.Option>)}
					</Select>
				),
			}
		],
		combinedWeight: [
			{
				name: "addContent",
				label: `重量(${isKg ? 'kg' : 'g'})`,
				children: (
					<InputNumber
						min={ 0 }
						max={ 99999999 }
						{ ...inputWeightProps }
						style={ { width: '70%' } }
						disabled={ disabled }
					/>
				),
			},
			{
				name: AutoCalculate.自动计算重量,
				label: "",
				valuePropName: "checked",
				children: (
					<Checkbox style={ { marginLeft: 54 } }>
						<div className="r-fs-13 r-c-gray r-flex r-ai-c">
							<div>组合货品自动计算</div>
							<Tooltip title="系统根据单品比例自动计算">
								<QuestionCircleOutlined className="r-ml-2" />
							</Tooltip>
						</div>
					</Checkbox>
				),
			}
		],
		costPrice: [
			{
				name: "addContent",
				label: "成本价（元）",
				children: (
					<InputNumber
						min={ 0 }
						max={ 999999.9999 }
						precision={ 4 }
						style={ { width: '70%' } }
					/>
				),
			}
		],
		combinedCostPrice: [
			{
				name: "addContent",
				label: "成本价（元）",
				children: (
					<InputNumber
						min={ 0 }
						max={ 999999.99 }
						precision={ 2 }
						style={ { width: '70%' } }
						disabled={ disabled }
					/>
				),
			},
			{
				name: AutoCalculate.自动计算成本,
				label: "",
				valuePropName: "checked",
				children: (
					<Checkbox style={ { marginLeft: 98 } }>
						<div className="r-fs-13 r-c-gray r-flex r-ai-c">
							<div>组合货品自动计算</div>
							<Tooltip title="系统根据单品比例自动计算">
								<QuestionCircleOutlined className="r-ml-2" />
							</Tooltip>
						</div>
					</Checkbox>
				),
			}
		],
		salePrice: [{
			name: "addContent",
			label: "售价",
			children: (
				<InputNumber
					min={ 0 }
					max={ 999999.99 }
					precision={ 2 }
					style={ { width: '70%' } }
				/>
			),
		}],
		tagPrice: [{
			name: "addContent",
			label: "吊牌价",
			children: (
				<InputNumber
					min={ 0 }
					max={ 999999.99 }
					precision={ 2 }
					style={ { width: '70%' } }
				/>
			),
		}],
		market: [
			{
				name: "addType",
				label: "添加方式",
				children: AddPosRadio,
			}, {
				name: "addContent",
				label: "市场名称",
				children: (
					<Input.TextArea maxLength={ 64 } rows={ 2 } placeholder="市场名称" />
				),
			}
		],
		stall: [
			{
				name: "addOther",
				label: "快捷添加",
				children: AddOtherCheckBox,
			}, {
				name: "addType",
				label: "添加方式",
				children: AddPosRadio,
			}, {
				name: "addContent",
				label: "档口名称",
				children: (
					<Input.TextArea maxLength={ 64 } rows={ 2 } placeholder="档口名称" />
				),
			}
		],
		supplierName: [
			{
				name: "addType",
				label: "添加方式",
				children: AddPosRadio,
			}, {
				name: "addContent",
				label: "供应商名",
				children: (
					<Input.TextArea maxLength={ 64 } rows={ 2 } placeholder="供应商名称" />
				),
			}
		],
		[batchEditFuncName.批量设置自定义属性]: [{
			name: "editType",
			label: "修改方式",
			children: (
				<Radio.Group>
					<Radio value={ editCustomTypeMap.自定义 }>自定义</Radio>
					<Radio value={ editCustomTypeMap.平台抓取 }>平台抓取</Radio>
				</Radio.Group>
			),
		}],
		[batchEditFuncName.批量设置颜色]: [{
			name: "editType",
			label: "修改方式",
			children: (
				<Radio.Group>
					<Radio value={ editCustomTypeMap.自定义 }>自定义</Radio>
					<Radio value={ editCustomTypeMap.平台抓取 }>平台抓取</Radio>
				</Radio.Group>
			),
		}],
		[batchEditFuncName.批量设置尺码]: [{
			name: "editType",
			label: "修改方式",
			children: (
				<Radio.Group>
					<Radio value={ editCustomTypeMap.自定义 }>自定义</Radio>
					<Radio value={ editCustomTypeMap.平台抓取 }>平台抓取</Radio>
				</Radio.Group>
			),
		}],
		[batchEditFuncName.批量设置货品规格编码]: [
			{
				name: "addType",
				children: (
					<Radio.Group onChange={ handleRadioChange }>
						{operations.filter(i => i.value !== AddType.覆盖).map((op) => (
							<div style={ { display: 'flex', marginTop: '12px' } } key={ op.value }>
								<Radio value={ op.value } style={ { display: 'flex', } }>
									<strong>{op.label}</strong>
								</Radio>
								<div style={ { flex: 1 } }>
									{op.render()}
								</div>
							</div>
						))}
					</Radio.Group>),
			}
		],
		[batchEditFuncName.批量设置货位]: [
			{
				name: "addContent",
				label: "货位",
				children: (
					<Input
						allowClear
						style={ { width: '70%' } }
						placeholder=""
						maxLength={ 32 }
					/>
				),
			}
		],
	};
	const customAttributesContent = {
		[editCustomTypeMap.自定义]: {
			name: "addContent",
			label: batchEditModalInfo?.attrName,
			children: (
				<Input style={ { width: '70%' } } maxLength={ 120 } />
			),
		},
		[editCustomTypeMap.平台抓取]: {
			name: "addRule",
			label: "抓取规则",
			children: (
				<Select style={ { width: 260 } } allowClear placeholder="抓取规则">
					<Select.Option value={ 抓取规则.面料 }>面料</Select.Option>
					<Select.Option value={ 抓取规则.成分 }>成分</Select.Option>
					<Select.Option value={ 抓取规则.面料或成分 }>面料或成分</Select.Option>
					<Select.Option value={ 抓取规则.颜色 }>颜色</Select.Option>
					<Select.Option value={ 抓取规则.尺码 }>尺码</Select.Option>
					<Select.Option value={ 抓取规则.颜色或尺码 }>颜色或尺码</Select.Option>
					<Select.Option value={ 抓取规则.产地 }>产地</Select.Option>
				</Select>
			),
		},
	};

	const formItemType = useMemo(() => {
		const _type = formItemMap[type];
		if (type === batchEditFuncName.批量设置自定义属性) {
			const editType = form.getFieldValue("editType") || editCustomTypeMap.自定义;
			_type.push(customAttributesContent[editType]);
		} else if ([batchEditFuncName.批量设置颜色, batchEditFuncName.批量设置尺码].includes(type)) {
			const editType = form.getFieldValue("editType") || editCustomTypeMap.自定义;
			if (editType === editCustomTypeMap.自定义) _type.push(customAttributesContent[editType]);
		}
		return _type;
	}, [type, formData]);
	const [errorArrs, setErrorArrs] = useState([]);

	useEffect(() => {
		// 监听事件，并获取传递的值
		const eventHandler = (data) => {
			console.log('接收到组件A传递的值:', data);
			setErrorArrs(data);
			if (data.length < 1) {
				handleClose();
			}
		};
		eventBus.on('handleColseOrErrorMessage', eventHandler);
		// 清除监听器
		return () => {
			eventBus.off('handleColseOrErrorMessage', eventHandler);
		};
	}, []);


	return (
		<>
			<Modal
				title={ `批量设置${nameMap[type] || batchEditModalInfo?.attrName}` }
				onOk={ handleOk }
				onCancel={ handleClose }
				visible={ visible }
				maskClosable={ false }
				centered
				width={ 640 }
			>
				<div style={ { maxHeight: '302px', overflowY: 'auto' } }>
					<Form
						form={ form }
						className={ s.batchEditForm }
						onValuesChange={ onFormChange }
						initialValues={ initialValues }
					>
						{type === batchEditFuncName.批量设置货品规格编码 && (
							<div style={ { width: '592px', height: '40px', background: '#FFF1F0', lineHeight: '40px', padding: '0 16px' } }>
								风险提示：货品规格编码为系统重要字段，请谨慎修改
							</div>
						)}
						{
							formItemType?.map(i => {
								const { children, ...ret } = i;
								return (
									<Form.Item
										key={ ret.name }
										{ ...ret }
									>
										{children}
									</Form.Item>
								);
							})
						}

						{/* 操作类型选择 */}
						{/* <Form.Item name="addType">
							<Radio.Group>
								{operations.map((op) => (
									<div style={ { display: 'flex', marginTop: '12px' } } key={ op.value }>
										<Radio value={ op.value } style={ { display: 'flex', alignItems: op.value === 1 ? 'top' : 'center' } }>
											<strong>{op.label}</strong>
										</Radio>
										<div style={ { flex: 1 } }>
											{op.render()}
										</div>
									</div>
								))}
							</Radio.Group>
						</Form.Item> */}
					</Form>
					{
						errorArrs?.length > 0 && (
							<div className="r-c-error">
								<p>修改失败，以下货品规格编码修改后存在重复：</p>
								{
									errorArrs.map((i, idx) => (<span key={ i }>{i}{idx !== errorArrs.length - 1 && '、'}</span>))
								}
							</div>
						)
					}
					{
						from !== "archives" && (<div className="r-c-error" style={ { paddingLeft: '5em' } }>注：添加后请点击保存后生效</div>)
					}
				</div >
			</Modal>
		</>
	);
}
export default memo(BatchEditModal);
