import React, { useEffect, useState } from "react";
import { Modal, Tabs, Table, Pagination, Space, message, Button } from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { tradeLimitQueryDayDetailPageListApi, tradeLimitQueryDetailPageListApi, preCheckTradeLimitApi, queryDayDetailPageList, queryDetailPageList } from "@/apis/trade";
import DateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';

interface StatisticModalProps {
  visible: boolean;
  phItemConsumeNum: number|string;
  phItemConsumeMaxNum: number|string;
  onCancel: () => void;
}

interface ApiResponse<T> {
  list: T[];
  total: number;
}

interface DayData {
  key: string;
  groupKey: string;
  number: number;
}

interface DetailData {
  key: string;
  statisticalTime: string;
  sellerNick: string;
  bizCode: string;
  tradeNum: number;
}

const dayColumns: ColumnsType<any> = [
	{ title: '日期', dataIndex: 'groupKey', key: 'groupKey' },
	{ title: '当月使用', dataIndex: 'number', key: 'number' }
];
const detailColumns: ColumnsType<any> = [
	{ title: '操作时间', dataIndex: 'statisticalTime', key: 'statisticalTime' },
	{ title: '平台/店铺',
		dataIndex: 'sellerNick',
		key: 'sellerNick',
		render: (text, record) => {
			return (
				<div className="r-flex r-ai-c">
					<PlatformIcon platform={ record.platform } />
					{ text || "---" }
				</div>
			);
		} },
	{ title: '商品ID', dataIndex: 'bizCode', key: 'bizCode' }
];

const ItemMigrationModal: React.FC<StatisticModalProps> = ({ visible, phItemConsumeNum, phItemConsumeMaxNum, onCancel }) => {
	// 按yue统计
	const [dayData, setDayData] = useState<any[]>([]);
	const [dayTotal, setDayTotal] = useState(0);
	const [dayPage, setDayPage] = useState(1);
	const [dayPageSize, setDayPageSize] = useState(10);
	// 明细
	const [detailData, setDetailData] = useState<any[]>([]);
	const [detailTotal, setDetailTotal] = useState(0);
	const [detailPage, setDetailPage] = useState(1);
	const [detailPageSize, setDetailPageSize] = useState(10);
	const [range, setRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([dayjs().subtract(1, 'M').startOf('day'), dayjs().endOf('day')]);
	const [tab, setTab] = useState('1');
	const [loading, setLoading] = useState(false);
	const [totalConsumeNum, setTotalConsumeNum] = useState(0);
	
	const showNum = () => {
		return	tab == '1' ? dayTotal : detailTotal;
	};
	// 示例接口
	const fetchDayData = async() => {
		setLoading(true);
		try {
			const res = await queryDayDetailPageList({
				pageNo: dayPage,
				pageSize: 999,
				module: 'PT_ITEM_LIMIT'
			}) as ApiResponse<DayData>;
			console.log(res);
			setDayData(res?.list);
			setDayTotal(res?.total);
		} catch (e) {
			message.error('获取按月统计失败');
		} finally {
			setLoading(false);
		}
	};
	const fetchDetailData = async(isClick?: boolean) => {
		if (isClick && detailPage !== 1) {
			setDetailPage(1);
			return;
		}
		setLoading(true);
		try {
			const res = await queryDetailPageList({
				pageNo: isClick ? 1 : detailPage,
				pageSize: detailPageSize,
				startTime: dayjs(range[0]).format('YYYY-MM-DD HH:mm:ss'),
				endTime: dayjs(range[1]).format('YYYY-MM-DD HH:mm:ss'),
				module: 'PT_ITEM_LIMIT'
			}) as ApiResponse<DetailData>;
			setDetailData(res?.list);
			setDetailTotal(res?.total);
		} catch (e) {
			message.error('获取明细失败');
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (!visible) return;
		if (tab === '1') fetchDayData();
		else fetchDetailData();
		// preCheckTradeLimitApi({}).then(res => {
		// 	setTotalConsumeNum(res.totalConsumeNum);
		// });
		// eslint-disable-next-line
  }, [visible, tab, dayPage, dayPageSize, detailPage, detailPageSize]);

	return (
		<Modal
			visible={ visible }
			onCancel={ onCancel }
			footer={ null }
			width={ 720 }
			title="额度使用统计"
			destroyOnClose
			centered
			maskClosable={ false }
			bodyStyle={ { maxHeight: 'calc(100vh - 200px)', minHeight: '500px' } }
		>
			<Tabs activeKey={ tab } onChange={ setTab } style={ { marginTop: -16 } } tabBarExtraContent={ tab == '2' ? <div>总消耗量：<span style={ { color: '#f00' } }>{showNum()}</span></div> : "数据每天凌晨更新" }>
				<Tabs.TabPane tab="按月统计" key="1">
					<Table
						size="small"
						columns={ dayColumns }
						dataSource={ dayData }
						pagination={ false }
						rowKey="key"
						bordered
						loading={ loading }
						style={
							{ maxHeight: '400px', overflow: 'auto' }
						}
					/>
					<div style={ { display: 'flex', justifyContent: 'flex-end', alignItems: 'center', marginTop: 16 } }>
						{/* <span className="r-mr-16">总计 {dayTotal} 项</span>
						<Pagination
							total={ dayTotal }
							pageSize={ dayPageSize }
							current={ dayPage }
							showSizeChanger
							onChange={ setDayPage }
							onShowSizeChange={ (_, size) => setDayPageSize(size) }
							style={ { marginRight: 8 } }
						/> */}
					</div>
				</Tabs.TabPane>
				<Tabs.TabPane tab="使用明细" key="2">
					<Space style={ { marginBottom: 16 } }>
						<DateRangePicker1
							value={ range }
							allowClear={ false }
							size="middle"
							onChange={ v => v && setRange(v as [dayjs.Dayjs, dayjs.Dayjs]) }
						/>
						<Button type="primary" size="middle" onClick={ () => fetchDetailData(true) }>查询</Button>
					</Space>
					<Table
						size="small"
						columns={ detailColumns }
						dataSource={ detailData }
						pagination={ false }
						rowKey="key"
						bordered
						loading={ loading }
						style={
							{ maxHeight: '300px', overflow: 'auto' }
						}
					/>
					<div style={ { display: 'flex', justifyContent: 'flex-end', alignItems: 'center', marginTop: 16 } }>
						<span className="r-mr-16">总计 {detailTotal} 项</span>
						<Pagination
							total={ detailTotal }
							pageSize={ detailPageSize }
							current={ detailPage }
							showSizeChanger
							onChange={ setDetailPage }
							onShowSizeChange={ (_, size) => setDetailPageSize(size) }
							style={ { marginRight: 8 } }
						/>
					</div>
				</Tabs.TabPane>
			</Tabs>
		</Modal>
	);
};

export default ItemMigrationModal; 