.categoryConfirmModal {
  :global {
      .ant-modal-content {
          border-radius: 0;
      }
      
      .ant-modal-header {
          border-bottom: 1px solid #f0f0f0;
          padding: 16px 24px;
          
          .ant-modal-title {
              font-size: 16px;
              font-weight: 500;
          }
      }
      
      .ant-modal-body {
          padding: 24px;
      }
      
      .ant-modal-footer {
          border-top: 1px solid #f0f0f0;
          padding: 16px 24px;
          text-align: right;
      }
  }
}

.categoryConfirmContent {
  .description {
      color: #666;
      font-size: 14px;
      margin-bottom: 24px;
      line-height: 1.5;
  }
  
  .categoryTable {
      width: 100%;
      border-collapse: collapse;
      border: 1px solid #f0f0f0;
      
      th, td {
          border: 1px solid #f0f0f0;
          padding: 12px 16px;
          text-align: left;
      }
      
      th {
          background-color: #fafafa;
          font-weight: 500;
          color: #333;
      }
      
      .shopNameColumn {
          width: 200px;
      }
      
      .categoryColumn {
          .required {
              color: #ff4d4f;
              margin-right: 4px;
          }
      }
      
      .shopInfoCell {
          vertical-align: middle;
      }
      
      .categoryCell {
          vertical-align: middle;
          :global {
              .cascader-high-zindex {
                  top: 36px !important;
                  bottom: auto !important;
              }
            //   ant-cascader-menus
              .ant-cascader-dropdown {
                height: 260px !important;
                  
                  .ant-cascader-menu {
                    width: 100%;
                      height: 260px !important;
                  }
              }
          }
      }
  }
  
  .shopInfo {
      display: flex;
      align-items: center;
      
      .shopIcon {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 8px;
          font-size: 12px;
          font-weight: bold;
          color: white;
      }
      
      .taobaoIcon {
          background-color: #ff4400;
      }
      
      .tmallIcon {
          background-color: #ff0036;
      }
      
      .douyinIcon {
          background-color: #000;
      }
      
      .shopName {
          color: #333;
          font-size: 14px;
      }
  }
}
