import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Select, message, Divider, Cascader } from 'antd';
import { observer } from 'mobx-react';
import _ from 'lodash';
import s from './index.module.scss';
import { PLAT_ICON_MAP, PLAT_TB } from '@/constants';
import Icon from "@/components/Icon";
import { CategoryTreeItem, CategoryTreeRequest } from '@/types/schemas/user';
import { getCategoryTreeApi } from '@/apis/user';
import { CategorySelectList } from '@/pages/Warehouse/components/ConfigDrawer/interface';

interface CategoryConfirmModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (categoryData: any) => void;
  selectedShops: any[];
}

const CategoryConfirmModal: React.FC<CategoryConfirmModalProps> = ({
	visible,
	onCancel,
	onConfirm,
	selectedShops
}) => {
	const [categorySelections, setCategorySelections] = useState<Record<string, any[]>>({});
	const [categoryOptions, setCategoryOptions] = useState<Record<string, CategoryTreeItem[]>>({});
	const [loading, setLoading] = useState<Record<string, boolean>>({});
	const [searchOptions, setSearchOptions] = useState<Record<string, any[]>>({});
	const [isSearching, setIsSearching] = useState<Record<string, boolean>>({});

	useEffect(() => {
		if (visible && selectedShops.length > 0) {
			// 清空之前的数据
			setCategoryOptions({});
			setCategorySelections({});
			setLoading({});
			setSearchOptions({});
			setIsSearching({});
		}
	}, [visible, selectedShops]);

	const buildFullCategoryTree = (data: CategoryTreeItem[]): any[] => {
		return data.map(item => ({
			value: item.categoryId,
			label: item.name,
			isLeaf: item.isLeaf,
			// 确保 children 存在且不为空数组时才设置
			children: item.children && item.children.length > 0 ? buildFullCategoryTree(item.children) : undefined
		}));
	};

	const buildTbCategoryTree = (data: CategoryTreeItem[]): any[] => {
		return data.map(item => ({
			value: item.categoryId,
			label: item.name,
			isLeaf: item.isLeaf,
			children: item.isLeaf ? undefined : []
		}));
	};

	const buildSearchCategoryTree = (data: CategoryTreeItem[]): any[] => {
		const map = new Map<string, any>();
		const root: any[] = [];
		
		for (const item of data) {
			const newItem = {
				value: item.categoryId,
				label: item.name,
				isLeaf: item.isLeaf,
				children: item.isLeaf ? undefined : [],
				level: item.level,
				parentId: item.parentId
			};
			map.set(item.categoryId, newItem);
		}
		
		for (const item of data) {
			const currentNode = map.get(item.categoryId);
			if (item.level === 1 || item.parentId === '0') {
				root.push(currentNode);
			} else {
				const parent = map.get(item.parentId);
				if (parent && parent.children) {
					parent.children.push(currentNode);
				}
			}
		}
		
		return root;
	};

	const fetchCategoryTree = async(platform: string, sellerId: string) => {
		const shopKey = `${platform}_${sellerId}`;
		try {
			setLoading(prev => ({ ...prev, [shopKey]: true }));
			const param: CategoryTreeRequest = { 
				platform, 
				sellerId,
				...(CategorySelectList.includes(platform) ? { parentCategory: '0' } : {})
			};
			const response = await getCategoryTreeApi(param, true);
			const treeData = CategorySelectList.includes(platform) ? buildTbCategoryTree(response || []) : buildFullCategoryTree(response || []);
			setCategoryOptions(prev => ({
				...prev,
				[shopKey]: treeData
			}));
		} catch (error) {
			console.error('获取类目失败:', error);
		} finally {
			setLoading(prev => ({ ...prev, [shopKey]: false }));
		}
	};

	const searchTbCategory = async(inputValue: string, shop: any) => {
		const shopKey = `${shop.platform}_${shop.sellerId}`;
		
		if (!inputValue.trim()) {
			setSearchOptions(prev => ({ ...prev, [shopKey]: [] }));
			return;
		}
		setCategorySelections(prev => {
			const newSelections = { ...prev };
			delete newSelections[`${shop.sellerId}_${shop.platform}`];
			return newSelections;
		});
		try {
			setIsSearching(prev => ({ ...prev, [shopKey]: true }));
			const param: CategoryTreeRequest = { 
				platform: shop.platform, 
				sellerId: shop.sellerId,
				cName: inputValue
			};
			const response = await getCategoryTreeApi(param, true);
			
			// 将搜索结果与原有的类目数据合并
			const searchResults = buildSearchCategoryTree(response || []);
			const originalOptions = searchOptions[shopKey] || [];
			const mergedOptions = mergeSearchWithOriginal(originalOptions, searchResults);
			console.log(searchResults, mergedOptions, 'searchResultssearchResultssearchResultssearchResults');

			setSearchOptions(prev => ({ ...prev, [shopKey]: mergedOptions }));
		} catch (error) {
			console.error('搜索类目失败:', error);
			setSearchOptions(prev => ({ ...prev, [shopKey]: [] }));
		} finally {
			setIsSearching(prev => ({ ...prev, [shopKey]: false }));
		}
	};

	// 合并搜索结果与原有数据
	const mergeSearchWithOriginal = (originalOptions: any[], searchResults: any[]): any[] => {
		const merged = [...originalOptions];
		
		// 将搜索结果合并到对应的父节点中
		const mergeNode = (nodes: any[], searchNodes: any[]) => {
			searchNodes.forEach(searchNode => {
				const existingNode = nodes.find(node => node.value === searchNode.value);
				if (existingNode) {
					// 如果节点已存在，合并其子节点
					if (searchNode.children && searchNode.children.length > 0) {
						existingNode.children = existingNode.children || [];
						mergeNode(existingNode.children, searchNode.children);
					}
				} else {
					// 如果节点不存在，直接添加
					nodes.push(searchNode);
				}
			});
		};
		
		mergeNode(merged, searchResults);
		return merged;
	};

	const debouncedSearchTbCategory = _.debounce(searchTbCategory, 300);

	const handleSearch = (inputValue: string, shop: any) => {
		
		if (CategorySelectList.includes(shop.platform)) {
			debouncedSearchTbCategory(inputValue, shop);
		}
	};

	const loadTbData = async(selectedOptions: any[], shop: any) => {
		const targetOption = selectedOptions[selectedOptions.length - 1];
		targetOption.loading = true;
		const shopKey = `${shop.platform}_${shop.sellerId}`;
		
		// 清空搜索选项
		setSearchOptions(prev => ({ ...prev, [shopKey]: [] }));
		setCategorySelections(prev => {
			const newSelections = { ...prev };
			delete newSelections[`${shop.sellerId}_${shop.platform}`];
			return newSelections;
		});
		try {
			const param: CategoryTreeRequest = { 
				platform: shop.platform, 
				sellerId: shop.sellerId,
				parentCategory: targetOption.value
			};
			const response = await getCategoryTreeApi(param, true);
			const childrenData = buildTbCategoryTree(response || []);
			
			// 同时更新 targetOption 和 categoryOptions
			targetOption.children = childrenData;
			
			// 更新 categoryOptions 状态中对应的节点
			setCategoryOptions(prev => {
				const updatedOptions = { ...prev };
				const currentOptions = updatedOptions[shopKey] || [];
				
				const updateNodeChildren = (nodes: any[]): any[] => {
					return nodes.map(node => {
						if (node.value === targetOption.value) {
							return { ...node, children: childrenData };
						}
						if (node.children) {
							return { ...node, children: updateNodeChildren(node.children) };
						}
						return node;
					});
				};
				
				updatedOptions[shopKey] = updateNodeChildren(currentOptions);
				return updatedOptions;
			});
		} catch (error) {
			console.error('获取子类目失败:', error);
		} finally {
			targetOption.loading = false;
		}
	};


	const handleCascaderChange = (sellerId: string, platform: string, value: any[], selectedOptions: any[]) => {
		console.log(sellerId, value, selectedOptions, 'valuevaluevaluevaluevaluevalue');
		const categoryIds = selectedOptions?.map(v => {
			return v.value;
		})?.join(',');
		const categoryNames = selectedOptions?.map(v => {
			return v.label;
		})?.join(',');
		console.log(sellerId, categoryIds, categoryNames, 'categorySelectionscategorySelectionscategorySelections');

		setCategorySelections(prev => ({
			...prev,
			[`${sellerId}_${platform}`]: {
				sellerId,
				platform,
				categoryIds,
				categoryNames
			}
		}));

	};

	const handleCascaderClick = async(shop: any) => {
		const shopKey = `${shop.platform}_${shop.sellerId}`;
		
		// 更严格的判断
		const hasData = categoryOptions[shopKey] && Array.isArray(categoryOptions[shopKey]) && categoryOptions[shopKey].length > 0;
		const isLoading = loading[shopKey] === true;

		if (hasData || isLoading) {
			return;
		} else {
			await fetchCategoryTree(shop.platform, shop.sellerId);
		}
	};

	const handleConfirm = () => {
		// 检查是否所有店铺都已选择类目
		const missingShops = selectedShops.filter(shop => !categorySelections[`${shop.sellerId}_${shop.platform}`]);
		if (missingShops.length > 0) {
			message.warning('请为所有店铺选择商品类目');
			return;
		}
		
		// 构建类目数据
		const categoryData = categorySelections;
		
		console.log('构建的类目数据:', selectedShops, categoryData);
		onConfirm(categoryData);
	};

	return (
		<Modal
			title="确认商品类目"
			visible={ visible }
			onCancel={ onCancel }
			width={ 800 }
			zIndex={ 1001 }
			footer={ [
				<Button key="cancel" onClick={ onCancel } style={ { marginRight: 8 } }>
					取消
				</Button>,
				<Button key="confirm" type="primary" onClick={ handleConfirm } style={ { backgroundColor: '#FF9500', borderColor: '#FF9500' } }>
					开始复制
				</Button>
			] }
			centered
			maskClosable={ false }
			className={ s.categoryConfirmModal }
			bodyStyle={ { minHeight: '450px', maxHeight: "calc(100vh - 400px)", overflow: "auto" } }
		>
			<div className={ s.categoryConfirmContent }>
				<div className={ s.description }>
					仅铺货配置中要求在铺货时手动选择类目的店铺才需要在此处进行配置确认
				</div>
				
				<table className={ s.categoryTable }>
					<thead>
						<tr>
							<th className={ s.shopNameColumn }>店铺名称</th>
							<th className={ s.categoryColumn }>
								<span className={ s.required }>*</span>
								商品类目
							</th>
						</tr>
					</thead>
					<tbody>
						{selectedShops.map(shop => (
							<tr key={ shop.sellerId }>
								<td className={ s.shopInfoCell }>
									<div className={ s.shopInfo }>
										<div className={ s.shopIcon }>
											<Icon key={ shop.platform } type={ PLAT_ICON_MAP[shop.platform] } size={ 20 } style={ { marginLeft: '2px' } } />;
										</div>
										<span className={ s.shopName }>{shop.sellerNick}</span>
									</div>
								</td>
								<td className={ s.categoryCell }>
									<Cascader
										style={ { width: '536px' } }
										placeholder="请选择类目"
										options={ CategorySelectList.includes(shop.platform) && searchOptions[`${shop.platform}_${shop.sellerId}`]?.length > 0 
											? searchOptions[`${shop.platform}_${shop.sellerId}`] 
											: categoryOptions[`${shop.platform}_${shop.sellerId}`] || [] }
										loading={ loading[`${shop.platform}_${shop.sellerId}`] || isSearching[`${shop.platform}_${shop.sellerId}`] }
										loadData={ CategorySelectList.includes(shop.platform) ? (selectedOptions) => loadTbData(selectedOptions, shop) : undefined }
										displayRender={ (label) => label.join(' > ') }
										onChange={ (value, selectedOptions) => {
											handleCascaderChange(shop.sellerId, shop.platform, value, selectedOptions);
											// 如果值为空，说明是清空操作，需要重新加载初始数据
											if (!value || value.length === 0) {
												if (CategorySelectList.includes(shop.platform)) {
													const shopKey = `${shop.platform}_${shop.sellerId}`;
													setSearchOptions(prev => ({ ...prev, [shopKey]: [] }));
													fetchCategoryTree(shop.platform, shop.sellerId);
												}
											}
										} }
										onSearch={ (inputValue) => handleSearch(inputValue, shop) }
										popupClassName="cascader-high-zindex"
										getPopupContainer={ (triggerNode) => triggerNode.parentElement }
										onDropdownVisibleChange={ (open) => {
											if (open) {
												handleCascaderClick(shop);
											}
										} }
										value={ categorySelections[`${shop.sellerId}_${shop.platform}`]?.categoryIds?.split(',') || [] }
										placement="bottomLeft"
										showSearch={ CategorySelectList.includes(shop.platform) ? {
											filter: (inputValue, path) => {
												if (!inputValue) return true;
												return path.some(option => option.label 
													&& option.label.toString().toLowerCase().includes(inputValue.toLowerCase()));
											},
											matchInputWidth: false,
											limit: 200
										} : {
											filter: (inputValue, path) => {
												if (!inputValue) return true;
												return path.some(option => option.label 
													&& option.label.toString().toLowerCase().includes(inputValue.toLowerCase()));
											},
											matchInputWidth: false,
											limit: 200
										} }
									/>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</Modal>
	);
};

export default observer(CategoryConfirmModal);
