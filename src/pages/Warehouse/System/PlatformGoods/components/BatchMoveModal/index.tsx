import React, { useState, useEffect } from 'react';
import { Modal, Button, Checkbox, Row, Col, Space, message, Spin } from 'antd';
import { observer } from 'mobx-react';
import { platform } from 'os';
import s from './index.module.scss';
import Icon from '@/components/Icon';
import { GetNumLimit, getPlatformShopPuhuoSettingList, IndexPlatformShopGetPlatformShopsApi } from '@/apis/user';
import userStore from '@/stores/user';
import { PLAT_ALI, PLAT_FXG, PLAT_ICON_MAP, PLAT_PDD, PLAT_TB, PLAT_TM } from '@/constants';
import ConfigDrawer from '@/pages/Warehouse/components/ConfigDrawer';
import scanPrintStore from '@/stores/trade/scanPrint';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { ItemItemUserConfigQueryConfigApi } from '@/apis/trade/search';
import { itemMigrationplatform } from '@/pages/Warehouse/components/ConfigDrawer/interface';
import CategoryConfirmModal from './components/CategoryConfirmModal';
import ItemMigrationModal from './itemMigrationModal';

interface BatchMoveModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (selectedShops: string[]) => void;
  selectedCount: number;
}

const BatchMoveModal: React.FC<BatchMoveModalProps> = ({
	visible,
	onCancel,
	onOk,
	selectedCount = 0
}) => {
	const [selectedShops, setSelectedShops] = useState<string[]>([]);
	const [shopList, setShopList] = useState<any[]>([]);
	const [loading, setLoading] = useState(false);
	const [configVisible, setConfigVisible] = useState(false);
	const [currentShop, setCurrentShop] = useState<any>(null);
	const [subLimitConfig, setSubLimitConfig] = useState(null);
	const [categoryConfirmVisible, setCategoryConfirmVisible] = useState(false);
	// 筛选出铺货时手动选择的店铺
	const [categoryConfirmModalList, setCategoryConfirmModalList] = useState<any[]>([]);
	const [quotaModalVisible, setQuotaModalVisible] = useState(false);
	const [ItemMigrationModalVisible, setItemMigrationModalVisible] = useState(false);
	const [phItemConsumeNum, setPhItemConsumeNum] = useState(0);
	const [phItemConsumeMaxNum, setPhItemConsumeMaxNum] = useState(0);
	const {
		onOKBatchMoveModalLoading,
		setOnOKBatchMoveModalLoading
	} = scanPrintStore;
	// 添加在 handleTabChange 函数后面
	const handleViewQuota = () => {
		setQuotaModalVisible(true);
	};

	const handleQuotaModalClose = () => {
		setQuotaModalVisible(false);
	};
	const handleItemMigrationModalVisible = () => {
		handleQuotaModalClose();
		setItemMigrationModalVisible(!ItemMigrationModalVisible);
	};
	// 获取店铺列表
	useEffect(() => {
		if (visible) {
			setSelectedShops([]);
			fetchShopList();
			fetchSubLimitConfig(); // 获取配置
			getNumLimit();
		}
	}, [visible]);
  
	const getNumLimit = async() => {
		const res = await GetNumLimit();
		if (res) {
			setPhItemConsumeNum(res.phItemConsumeNum || 0);
		}
	};
  
	// 获取子账号限制配置
	const fetchSubLimitConfig = async() => {
		try {
			const res = await ItemItemUserConfigQueryConfigApi({
				itemUserConfigBizEnum: "ITEM_DISTRIBUTE_SUB_LIMIT"
			});
			const rule = res?.find((item) => (item?.biz === "item_distribute_sub_limit"));
			if (rule?.value) {
				const config = JSON.parse(rule.value)?.openTbShopLimit || false;
				setSubLimitConfig(config);
			}
		} catch (error) {
			console.log("获取子账号限制配置失败:", error);
		}
	};


	const openTbShopLimitXz = () => {
		// 获取选中的店铺列表
		const selectedShopList = getSelectedShopList();
			
		// 筛选出淘宝/天猫店铺
		const tbShops = selectedShopList.filter(shop => shop.platform === PLAT_TB || shop.platform === PLAT_TM);
			
		// 如果选中的淘宝/天猫店铺超过1个
		if (tbShops.length > 1) {
			// 检查配置开关状态
			if (subLimitConfig) {
				// 配置关闭，不允许多个淘宝店铺 这玩意开关是个反的！
				message.error('应平台规范性要求，暂不支持同时铺货至多淘宝/天猫店铺，请重新选择');
				return false;
			}
			// 配置开启，允许多个淘宝店铺，不受限制
		}
			
		return true;
	};

	const fetchShopList = async() => {
		try {
			setLoading(true);
			const { userId, phItemNumLimit } = await userStore.getUserInfo();
			setPhItemConsumeMaxNum(phItemNumLimit || 0);
			const { list = [] } = await IndexPlatformShopGetPlatformShopsApi({
				userId,
				refresh: 0,
			});
      
			// 筛选只保留、拼多多、抖音平台的店铺，并按指定顺序排序
			const platformOrder = itemMigrationplatform;
			const filteredList = list
				.filter(shop => platformOrder.includes(shop.platform) && shop.status === 1)
				.sort((a, b) => {
					// 根据平台顺序排序
					return platformOrder.indexOf(a.platform) - platformOrder.indexOf(b.platform);
				});
				
			setShopList(filteredList);
			setLoading(false);
		} catch (error) {
			console.error('获取店铺列表失败:', error);
			message.error('获取店铺列表失败');
			setLoading(false);
		}
	};
  
	const handleShopSelect = (shopId: string) => {
		if (selectedShops.includes(shopId)) {
			setSelectedShops(selectedShops.filter(id => id !== shopId));
		} else {
			setSelectedShops([...selectedShops, shopId]);
		}
	};
  
	const getPlatformIcon = (platform: string) => {
		const iconType = PLAT_ICON_MAP[platform];
		return iconType ? <Icon type={ iconType } className={ s.platformIcon } /> : null;
	};

	// 打开配置抽屉
	const handleOpenConfig = () => {
		sendPoint(Pointer['商品_平台商品管理_弹窗搬家铺货配置']);
		// 如果有选中的店铺，则设置第一个选中的店铺为当前店铺
		if (selectedShops.length > 0) {
			const firstSelectedShop = shopList.find(shop => shop.id === selectedShops[0]);
			if (firstSelectedShop) {
				setCurrentShop(firstSelectedShop);
			}
		} else if (shopList.length > 0) {
			// 如果没有选中的店铺，则设置第一个店铺为当前店铺
			setCurrentShop(shopList[0]);
			// 如果没有选中店铺，提示用户
			message.warning('请先选择至少一个目标店铺');
			return;
		}
		setConfigVisible(true);
	};

	// 关闭配置抽屉
	const handleCloseConfig = () => {
		setConfigVisible(false);
	};

	// 确认配置
	const handleConfirmConfig = () => {
		console.log('确认配置:', currentShop);
		// 这里可以添加保存配置的逻辑
		setConfigVisible(false);
	};

	// 处理标签切换
	const handleTabChange = (key: string) => {
		// 根据标签ID找到对应的店铺
		const shop = shopList.find(item => item.id.toString() === key);
		if (shop) {
			setCurrentShop(shop);
		}
	};

	// 获取选中的店铺列表
	const getSelectedShopList = () => {
		if (selectedShops.length === 0) {
			return [];
		}
		return shopList.filter(shop => selectedShops.includes(shop.id));
	};
  
	  
	const handleSubmit = async() => {
		sendPoint(Pointer['商品_平台商品管理_开始复制']);
		try {
			const remainingQuota = (phItemConsumeMaxNum || 0) - (phItemConsumeNum || 0);
			if (remainingQuota <= 0) {
				setQuotaModalVisible(true);
				return;
			}
			if (selectedShops.length === 0) {
				message.warning('请选择至少一个目标店铺');
				return;
			}
			// 检查淘宝店铺限制
			if (!openTbShopLimitXz()) {
				return;
			}
			const data = {
				list: getSelectedShopList()?.map(v => {
					return {
						shopId: v.sellerId,
						platform: v.platform
					};
				})
			};
			const res = await getPlatformShopPuhuoSettingList(data);
			console.log(res, 'resresres');
			const matchType2List = res?.list?.filter(v => v.matchType === 2)?.map(item => ({
				...item,
				sellerId: item.shopId
			}));
			console.log(matchType2List, 'resresresresresresres');
			if (matchType2List && matchType2List.length > 0) {
				setCategoryConfirmModalList(matchType2List);
				setCategoryConfirmVisible(true);
			} else {
				await onOk(getSelectedShopList());
			}

		} catch (error) {
			console.log('铺货操作失败:', error);
			
		} 
	};

	const handleCategoryConfirm = async(categoryData: any) => {
		try {
			// 将categoryData的类目信息合并到selectedShopList中
			const selectedShopListWithCategory = getSelectedShopList().map(shop => {
			// 查找匹配的categoryData
				const matchedCategory = categoryData[`${shop.sellerId}_${shop.platform}`];
			
				// 如果找到匹配的类目数据，则合并
				if (matchedCategory) {
					return {
						...shop,
						categoryIds: matchedCategory.categoryIds,
						categoryNames: matchedCategory.categoryNames
					};
				}
			
				return shop;
			});
			setCategoryConfirmVisible(false);
		 	await onOk(selectedShopListWithCategory);
		} catch (error) {
		  console.log('铺货操作失败:', error);
		}
	  };
	
	const showTitle = () => {
		return (
			<div>
				一键批量铺货
				<span style={ { 
					fontSize: '12px',
					 color: 'rgba(0, 0, 0, 0.45)',
					 marginLeft: '16px'
					 } }
				>
					免责声明：铺货是将ERP系统内的商品数据铺货到授权的目标平台店铺；请遵守相关规定和平台规范，本软件不承担因个人未检查而导致店铺、商品的问题风险
				</span>
			</div>
		);
	};
	return (
		<>
			<CategoryConfirmModal
				visible={ categoryConfirmVisible }
				onCancel={ () => setCategoryConfirmVisible(false) }
				onConfirm={ handleCategoryConfirm }
				selectedShops={ categoryConfirmModalList }
			/>
			<Modal
				title={ showTitle() }
				visible={ visible }
				onCancel={ onCancel }
				width={ 1050 }
				bodyStyle={ { maxHeight: "calc(100vh - 400px)", overflow: "auto" } }
				footer={ (
					<div style={ { display: 'flex', justifyContent: 'space-between' } }>
						<div style={ { height: '32px', marginTop: '5px', backgroundColor: '#F5F5F5', lineHeight: '32px', padding: '0 16px' } }>额度：已用
							<span style={ { margin: '0 4px', color: '#FF4D4F' } }>{(phItemConsumeNum || 0).toLocaleString()}</span>/共
							<span style={ { margin: '0 4px' } }>{(phItemConsumeMaxNum || 0).toLocaleString()}</span>
							<span 
								onClick={ handleViewQuota }
								style={ { color: '#1890FF', cursor: 'pointer', marginLeft: '10px' } }
							>查看
							</span>
						</div>
						<div>
							<Button key="setting" className={ s.settingBtn } onClick={ handleOpenConfig }>
								铺货设置
							</Button>
							<Button 
								key="submit" 
								type="primary" 
								onClick={ handleSubmit }
								loading={ onOKBatchMoveModalLoading } // 添加loading属性
							>
								开始铺货
							</Button>
						</div>
					</div>
				) }
				centered
				maskClosable={ false }
			>
				<div className={ s.content }>
					<div className={ s.sourceInfo }>
						<div className={ s.sourceTitle }>源商品: 本次共选择 <span className={ s.count }>{selectedCount}</span> 个商品铺货</div>
					</div>
        
					<div className={ s.targetShops }>
        
						<div className={ s.targetShops }>
							<div style={ { display: 'flex', justifyContent: 'space-between' } }>
								<div className={ s.targetTitle }>铺货到目标店铺:</div>
								<div className={ s.targetTitle } style={ { color: '#999' } }>支持目标平台:
									{
										itemMigrationplatform.map(v => {
											return <Icon key={ v } type={ PLAT_ICON_MAP[v] } className={ s.platformIcon } style={ { marginLeft: '2px' } } />;
										})
									}
								</div>
							</div>
				
							<div className={ s.shopListHeader }>
								<Checkbox 
									checked={ selectedShops.length > 0 }
									indeterminate={ selectedShops.length > 0 && selectedShops.length < shopList.length }
									onChange={ (e) => {
										if (e.target.checked) {
											setSelectedShops(shopList.map(shop => shop.id));
										} else {
											setSelectedShops([]);
										}
									} }
								>
									已选 <span className={ s.selectedCount }>{selectedShops.length}</span> 个店铺
								</Checkbox>
							</div>
							<Spin spinning={ loading }>
								<div className={ s.shopList }>
									<Row gutter={ [16, 0] }>
										{shopList?.map(shop => (
											<Col span={ 6 } key={ shop.id }>
												<div className={ s.shopItem }>
													<Checkbox 
														checked={ selectedShops.includes(shop.id) }
														onChange={ () => handleShopSelect(shop.id) }
													>
														<Space align="center">
															{getPlatformIcon(shop.platform)}
															<span className={ s.shopName }>{shop.sellerNick}</span>
														</Space>
													</Checkbox>
												</div>
											</Col>
										))}
									</Row>
								</div>
							</Spin>
						</div>
					</div>
				</div>
				{/* 铺货配置抽屉 */}
				<ConfigDrawer
					visible={ configVisible }
					titleList={ getSelectedShopList() }
					onClose={ handleCloseConfig }
					onOk={ handleConfirmConfig }
					onTitleChange={ handleTabChange }
					width={ 1200 }
					zIndex={ 1100 } // 设置一个非常高的z-index值
				/>
				{/* 复制额度弹窗 */}
				<Modal 
					centered
					title="额度"
					visible={ quotaModalVisible } 
					onCancel={ handleQuotaModalClose }
					footer={ [
					// <Button key="cancel" onClick={ handleQuotaModalClose }>
					// 	取消
					// </Button>,
					// <Button key="submit" type="primary" onClick={ handleQuotaModalClose }>
					// 	确定
					// </Button>
					] }
					width={ 700 }
					getContainer={ document.body }
					className={ s.limitModal }
				>
					<div className={ s.limitInfoBox }>
						<div className={ s.infoRow }>
							<div className={ s.infoItem }>
								<div className={ s.infoLabel }>当前账号每月额度</div>
								<div className={ s.infoValue }>{(phItemConsumeMaxNum || 0).toLocaleString()}</div>
							</div>
							<div className={ s.infoItem }>
								<div className={ s.infoLabel }>已用额度</div>
								<div className={ s.infoValue }>{(phItemConsumeNum || 0).toLocaleString()}</div>
							</div>
							<div className={ s.infoItem }>
								<div className={ s.infoLabelExc }>
									剩余额度
									<span
										style={ { 
											position: 'absolute',
											right: '20px',
											color: '#1890ff',
											cursor: 'pointer',
											fontSize: '14px',
										} }
										onClick={ handleItemMigrationModalVisible }
									>
										使用统计
									</span>
								</div>
								<div className={ s.infoValueExc }>{((phItemConsumeMaxNum || 0) - (phItemConsumeNum || 0)).toLocaleString()}</div>
							</div>
						</div>

					</div>

					<div className={ s.tipBox }>
						<div className={ s.tipItem }>
							<div className={ s.tipIcon }>
								<Icon style={ { fontSize: 24 } } type="lianxikefu" />
							</div>
							<div className={ s.tipContent }>
								<div className={ s.tipTitle }>联系销售</div>
								<div className={ s.tipDesc }>如需提高更多额度上限，请联系销售</div>
							</div>
						</div>
					</div>
				</Modal>
				<ItemMigrationModal 
					phItemConsumeNum={ phItemConsumeNum }
					phItemConsumeMaxNum={ phItemConsumeMaxNum }
					visible={ ItemMigrationModalVisible } 
					onCancel={ handleItemMigrationModalVisible }
				/>
			</Modal>
		</>
	);
};

export default observer(BatchMoveModal);
