.colset-opt-container {
	position: absolute;
	z-index: 1001;
	padding: 10px;
	background-color: #fff;
	border-radius: 4px;
	width: 100%;
	bottom: 0;
	left: 0;
	border-top: 1px solid #eee;
	:global {
		.ant-btn {
			margin: 0 5px;
		}
	}
}

.splitLine {
	position: relative;
	z-index: 10000;
	height: 2px;
}

.splitLine::before {
	position: absolute;
	top: 0;
	left: 15px;
	display: block;
	width: calc(100% - 30px);
	border-top: 1px solid #ddd;
	content: "";
}

.colsetContainer {
	position: relative;
	z-index: 1001;
	// width: fit-content;
	// max-height: calc(100vh - 300px);
	// overflow-y: auto;
	user-select: none;
	.colsetItem {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 32px;
		padding: 4px 8px;
		margin: 0 -8px;
		font-size: 14px !important;
		border: 1px solid transparent;
		.colsetLabel {
			margin: 0 8px;
			margin-right: auto;
			user-select: none;
		}
	}
}

.rowDragging {
	z-index: 1052;
	display: flex !important;
	align-items: center !important;
	justify-content: space-between !important;
	height: auto!important;
	padding: 4px 8px;
	margin: 0 -8px;
	background: #fff;
	border: 1px solid #f0f0f0;
	box-shadow: 0px 2px 6px rgba(0,0,0,0.15);
	.colsetLabel {
		margin: 0 8px !important;
		margin-right: auto !important;
	}
	:global {
		.anticon-menu {
			color: #FD8204 !important;
		}
	}
}
.search-setting-drawer {
	z-index: 1001;
	:global {
		.ant-drawer-header-title {
			flex-direction: row-reverse;
		}
		.ant-drawer-close {
			margin-right: 0;
		}
		.ant-drawer-body {
			padding-bottom: 60px;
		}
	}
}