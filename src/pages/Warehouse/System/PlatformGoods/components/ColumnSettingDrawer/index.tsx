import React, { useEffect, useState } from 'react';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';
import { MenuOutlined, PushpinFilled } from '@ant-design/icons';
import { Button, Checkbox, Drawer, Switch, message } from 'antd';
import cs from 'classnames';
import s from './index.module.scss';

export interface ColSortItem {
	collection: any;
	index: number;
	ischecked: boolean;
	isedit: boolean;
	width: any;
	name: any;
	key: string;
}

enum ColFixedEnum {
	固定在左侧 = 'left',
	固定在右侧 = 'right'
}

export interface ColFixItem {
	isfixed: boolean;
	colFixed: ColFixedEnum,
	name: any;
	key: string;
}

export interface IColumnSettingDrawerProps {
	visible: boolean;
	onClose: () => void;
	onSaveSort: (type: string, config: ColSortItem[]) => void;
	colSortList: ColSortItem[] | ColFixItem[];
	isShowSku?: boolean;
	onCheckShowSku?: (value: boolean) => void;
	hideSkuSwitch?: boolean; // 是否显示规格隐藏规格Switch
}

const SortableContainerWap = SortableContainer((props) => <div { ...props } />);

const ColumnSettingDrawer: React.FC<IColumnSettingDrawerProps> = (props) => {
	const { onClose, colSortList, visible, isShowSku = false, onCheckShowSku, hideSkuSwitch = false } = props;
	const [dataSource, setDataSource] = useState<ColSortItem[][]>([]);
	const [fixLeftList, setFixLeftList] = useState([]);
	const [fixRightList, setFixRightList] = useState([]);
	
	const SortableItem = SortableElement((sProps) => {
		const { record, collectionIndex, itemIndex } = sProps;
		return (
			<li className={ s.colsetItem }>
				<Checkbox
					onChange={ () => { handleChange(collectionIndex, itemIndex); } }
					disabled={ !record.isedit }
					checked={ record.ischecked }
				>
					<div className={ s.colsetLabel }>{record.name}</div>
				</Checkbox>
				<DragHandle />
			</li>
		);
	});
	
	const DragHandle = SortableHandle(() => <MenuOutlined style={ { cursor: 'move', color: '#999' } } />);

	const onSortEnd = ({ oldIndex, newIndex, collection }) => {
		const newCollections = [...dataSource];
		newCollections[collection] = arrayMoveImmutable(newCollections[collection], oldIndex, newIndex).filter(el => !!el);
		setDataSource(newCollections);
	};

	useEffect(() => {
		if (visible) {
			let list = [];
			let leftList = [];
			let rightList = [];
			colSortList?.forEach(i => {
				if (i.colFixed == ColFixedEnum.固定在左侧) {
					if (typeof i.name == 'string') {
						leftList.push(i);
					}
				} else if (i.colFixed == ColFixedEnum.固定在右侧) {
					rightList.push(i);
				} else {
					if (!list[i.collection]) {
						list[i.collection] = [];
					}
					list[i.collection].push({ ...i });
				}
			});
			setFixLeftList(leftList);
			setFixRightList(rightList);
			setDataSource(list);
		}
	}, [colSortList, visible]);

	const handleChange = (collectionIndex, itemIndex) => {
		setDataSource(prev => {
			prev[collectionIndex][itemIndex].ischecked = !prev[collectionIndex][itemIndex].ischecked;
			return [...prev];
		});
	};

	const handleOk = (type: string) => {
		let newList = [...dataSource.flat().map((i, index) => ({ ...i, index })), ...fixLeftList, ...fixRightList];
		if (newList.filter(i => i.ischecked).length < 5) {
			message.warning({
				content: '请至少勾选5个字段',
				className: s['message-warning'],
			});
			return;
		}
		props.onSaveSort?.(type, newList);
		onClose();
	};

	const fixColChange = (record) => {
		if (record.colFixed == ColFixedEnum.固定在右侧) {
			setFixRightList(pre => {
				return pre.map(i => {
					if (i.key == record.key) {
						i.isfixed = !i.isfixed;
					}
					return i;
				});
			});
		} else {
			setFixLeftList(pre => {
				return pre.map(i => {
					if (i.key == record.key) {
						i.isfixed = !i.isfixed;
					}
					return i;
				});
			});
		}
	};

	const getSortNode = (fixList) => {
		return fixList.map(record => {
			return (
				<ul key={ record.key }>
					<li className={ s.colsetItem }>
						<Checkbox
							disabled
							checked
						>
							<div className={ s.colsetLabel }>{record.name}</div>
						</Checkbox>
						<PushpinFilled onClick={ () => fixColChange(record) } style={ { color: record.isfixed ? '#ff9900' : '#999' } } />
					</li>
				</ul>
			);
		});
	};

	return (
		<>
			<Drawer
				className={ s["search-setting-drawer"] }
				title={ (
					<div className="r-flex r-ai-c">
						<div>列配置</div>
						{
							!hideSkuSwitch && (
								<div className="r-ml-16"><Switch checked={ isShowSku } onChange={ onCheckShowSku } checkedChildren="显示规格" unCheckedChildren="隐藏规格" /></div>
							)
						}
					</div>
				) }
				visible={ visible }
				onClose={ () => onClose() }
			>
				<div className={ s.colsetContainer }>
					{
						getSortNode(fixLeftList)
					}
					<SortableContainerWap
						onSortEnd={ onSortEnd }
						axis="y"
						lockAxis="y"
						useDragHandle
						helperClass={ s.rowDragging }
					>
						{dataSource?.map((items, collectionIndex) => (
							<React.Fragment key={ collectionIndex }>
								{
									collectionIndex != 0 && items?.length > 0
										? (<div className={ s.splitLine } />)
										: ''
								}
								<ul className="r-mb-0">
									{items?.map((item, itemIndex) => {
										if (!item.name) return null;
										return (
											<SortableItem
												key={ item.key }
												index={ itemIndex }
												collection={ collectionIndex }
												record={ item }
												collectionIndex={ collectionIndex }
												itemIndex={ itemIndex }
											/>
										);
									})}
								</ul>
							</React.Fragment>
						))}
					</SortableContainerWap>
					{
						getSortNode(fixRightList)
					}
				</div>

				<div className={ s["colset-opt-container"] }>
					<Button onClick={ onClose }>取消</Button>
					<Button onClick={ () => { handleOk('reset'); } }>恢复默认</Button>
					<Button onClick={ () => { handleOk('sure'); } } type="primary">确定</Button>
				</div>
			</Drawer>
		</>
	);
};

export default ColumnSettingDrawer; 