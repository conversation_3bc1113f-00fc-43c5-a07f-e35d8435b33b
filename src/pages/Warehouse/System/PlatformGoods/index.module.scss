.noline {
	border-right: none !important;
}
.newIcon {
	background: #ff0000;
	width: 32px;
	line-height: 12px;
	font-size: 12px;
	padding: 2px 2px 2px;
	z-index: 1;
	border-radius: 8px;
	color: #fff;
	text-align: center;
	position: absolute;
	right: -18px;
	top: -9px;
	font-family: PingFangSC-Medium;
}
td {
	vertical-align: top;
}
.emptyText {
	color: #ff4d4f;
	line-height: 22px;
	background: rgba(255, 77, 79, 0.2);
	border-radius: 2px;
}

td.not-show-next-cell + td > label {
	visibility: hidden;
}

.deletePlatGoods,
.log {
	color: #0aa6ec;
	cursor: pointer;
}

.condition2 {
	width: 320px;
}

.lineMax2 {
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-line-clamp: 2;
	display: -webkit-box;
	-webkit-box-orient: vertical;
}

.lineMax1 {
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-line-clamp: 1;
	display: -webkit-box;
	-webkit-box-orient: vertical;
}

.titleIcon {
	color: #999;
	margin-left: 5px;
	font-size: 14px;
	position: relative;
	top: 1px;
}

.platTitle {
	// margin: -5px 0 0 8px;
	line-height: 18px;
}

.platformGoods {
	:global {
		// .ant-table-cell {
		// 	vertical-align: middle;
		// }
		.ant-table-tbody > tr.ant-table-row:hover > td {
			background: unset !important;
		}
		.ant-table-tbody > tr.ant-table-row-selected > td {
			background: unset !important;
		}
		.ant-table-tbody
			> tr.ant-table-row:hover
			> td.ant-table-cell-fix-right {
			background: #fff !important;
		}
		.ant-table-tbody
			> tr.ant-table-row-selected
			> td.ant-table-cell-fix-right {
			background: #fff !important;
		}
		.ant-table-cell-fix-left,
		.ant-table-cell-fix-right {
			z-index: 97;
		}
		.fast-com {
			z-index: 99;
		}
		.search-table-con{
			z-index: 9999 !important;
		}
		.ant-pagination{
			z-index: 9999 !important;
		}
	}
}
.platformBatchSaveLoading {
	:global {
		.ant-modal-close-x {
			display: none;
		}
	}
}

.platformGoodsLogModal {
	:global {
		.r-bg-white {
			padding-bottom: 0;
			&::after {
				display: none;
			}
		}
	}
}

.menuLinkLimitIcon {
    position: absolute;
    font-size: 12px;
    background-color: #FF0000;
    color: #fff;
    border-radius: 8px;
    width: 36px;
    line-height: 14px;
	height: 16px;
    text-align: center;
}
.cell-gap{
	gap: 8px;
}
.text-align{
	justify-content: center;
}
:global{
.lock-right {
		>div {
			justify-content: center !important;
			padding-left: 0px !important;
		}
	}
}
