import React from "react";
import { Drawer } from "antd";
import { useBoolean } from "ahooks";
import CreateSystem from ".";
import { Base } from "@/types/schemas";
import s from "./index.module.scss";

export interface CreateSystemModalProps extends Base{
	onSave?: Function
	archivesType?:string // archivesType
}

const CreateSystemModal: React.FC<CreateSystemModalProps> = ({
	children,
	...reset
}) => {
	const [visible, { setTrue, setFalse }] = useBoolean(false);
	const { archivesType = 'single' } = reset;
	return (
		<>
			<span onClick={ setTrue }>
				{ children }
			</span>
			<Drawer
				visible={ visible }
				title={ `新建${archivesType == 'combined' ? '组合' : '普通'}商品` }
				className={ s["edit-goods-drawer"] }
				bodyStyle={ { overflowY: 'auto', maxHeight: 'calc(100vh - 60px)' } }
				footer={ false }
				getContainer={ document.body }
				onClose={ setFalse }
				width={ 1200 }
				destroyOnClose
				maskClosable={ false }
			>
				<CreateSystem { ...reset } onCancel={ setFalse } />
			</Drawer>
		</>
	);
};

export default CreateSystemModal;
