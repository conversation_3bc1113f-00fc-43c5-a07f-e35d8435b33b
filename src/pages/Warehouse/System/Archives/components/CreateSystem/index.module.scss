.createSystem {
	.weight {
		display      : inline-block;
		margin-right : 30px;
		margin-bottom: 0px;
		overflow-y   : visible;

		.item {
			width: 80px;
		}
	}

	.skuTableContainer {
		// 确保表格可以正常显示
		width: 100%;

		// 如果表格内部有滚动，保留横向滚动
		//overflow-x: auto;
		height: auto;
	}

	.weightContainer {
		display     : flex;
		align-items : center;
		margin-right: 20px;
	}

	.priceContainer {
		display: flex;

		.price {
			margin-right : 10px;
			margin-bottom: 0px;
		}

		.item {
			width: 100px;
		}
	}

	.formBtns {
		position  : absolute;
		left      : 0;
		right     : 0;
		bottom    : 0;
		padding   : 10px;
		border-top: 1px solid #ddd;
		background: #fff;
		z-index   : 10;
	}

	.ant-drawer-wrapper-body {
		height: 20px;
	}
}

.edit-goods-drawer {
	:global {
		.ant-drawer-header-title {
			flex-direction: row-reverse;
		}

		.ant-drawer-body {
			padding-bottom: 60px;
		}

		.ant-drawer-close {
			margin-right: 0;
		}
	}
}