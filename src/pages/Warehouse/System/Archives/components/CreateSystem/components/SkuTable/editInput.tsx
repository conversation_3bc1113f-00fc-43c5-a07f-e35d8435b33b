/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-06-19 19:15:29
 * @Description: 可编辑的Input组件，点击进入编辑模式，点击外部退出编辑模式
 */
import React, { useRef, useState, useEffect } from 'react';
import { useClickAway } from 'ahooks';
import { Form, Input } from 'antd';
import cs from 'classnames';
import s from './editInput.module.scss';

interface EditInputProps {
	value?: string;
	placeholder?: string;
	maxLength?: number;
	name: string;
	onChange?: (value: string) => void;
	onBlur?: (value: string) => void;
	type?: 'text' | 'number';
	disabled?: boolean;
}

export default function EditInput(props: EditInputProps) {
	const [isEditing, setIsEditing] = useState(false);
	const [inputValue, setInputValue] = useState('');
	const [cursorPosition, setCursorPosition] = useState(0);
	const { value, placeholder, maxLength, name, onChange, onBlur, type = 'text', disabled = false } = props;
	const ref = useRef<HTMLDivElement>(null);
	const inputRef = useRef<any>(null);
	const displayRef = useRef<HTMLDivElement>(null);
	
	useClickAway(() => {
		if (isEditing) {
			setIsEditing(false);
			onBlur?.(inputValue);
		}
	}, ref);

	useEffect(() => {
		setInputValue(value || '');
	}, [value]);

	useEffect(() => {
		if (isEditing && inputRef.current) {
			// 进入编辑模式时自动聚焦并设置光标位置
			setTimeout(() => {
				inputRef.current?.focus();
				// 设置光标位置
				inputRef.current?.setSelectionRange(cursorPosition, cursorPosition);
			}, 10);
		}
	}, [isEditing, cursorPosition]);

	// 计算点击位置对应的字符索引
	const getCharacterIndexFromPoint = (e: React.MouseEvent) => {
		if (!displayRef.current || !inputValue) return 0;
		
		const rect = displayRef.current.getBoundingClientRect();
		const clickX = e.clientX - rect.left - 11; // 减去padding-left (11px)
		
		// 创建canvas来测量文本宽度
		const canvas = document.createElement('canvas');
		const ctx = canvas.getContext('2d')!;
		// 使用与div相同的字体样式
		const computedStyle = window.getComputedStyle(displayRef.current);
		ctx.font = `${computedStyle.fontSize} ${computedStyle.fontFamily}`;
		
		let closestIndex = 0;
		let closestDistance = Infinity;
		
		// 遍历每个字符位置
		for (let i = 0; i <= inputValue.length; i++) {
			const textBeforeCursor = inputValue.substring(0, i);
			const textWidth = ctx.measureText(textBeforeCursor).width;
			
			const distance = Math.abs(clickX - textWidth);
			
			if (distance < closestDistance) {
				closestDistance = distance;
				closestIndex = i;
			}
		}
		
		return closestIndex;
	};

	const handleClick = (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!isEditing && !disabled) {
			// 计算点击位置对应的字符索引
			const clickIndex = getCharacterIndexFromPoint(e);
			setCursorPosition(clickIndex);
			setIsEditing(true);
		}
	};

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const newValue = e.target.value;
		setInputValue(newValue);
		onChange?.(newValue);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			e.preventDefault();
			setIsEditing(false);
			onBlur?.(inputValue);
		}
		if (e.key === 'Escape') {
			setIsEditing(false);
			setInputValue(value || '');
		}
	};

	const displayValue = inputValue || placeholder;

	if (disabled) {
		return (
			<div className={ cs(s.editInputContainer, s.disabled) }>
				<div className={ cs(s.editInputDisplay, s.disabled) }>
					<div className={ cs(s.editInputContent, {
						[s.placeholder]: !inputValue
					}) }
					>
						{displayValue}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div ref={ ref } className={ s.editInputContainer }>
			{isEditing ? (
				<Form.Item name={ name } className={ s.editInputFormItem }>
					<Input
						ref={ inputRef }
						value={ inputValue }
						placeholder={ placeholder }
						maxLength={ maxLength }
						onChange={ handleInputChange }
						onKeyDown={ handleKeyDown }
						type={ type }
					/>
				</Form.Item>
			) : (
				<div
					ref={ displayRef }
					onClick={ handleClick }
					className={ s.editInputDisplay }
				>
					<div className={ cs(s.editInputContent, {
						[s.placeholder]: !inputValue
					}) }
					>
						{displayValue}
					</div>
				</div>
			)}
		</div>
	);
} 