.editTextAreaContainer {
    vertical-align       : bottom;
    box-sizing           : border-box;
    margin               : 0;
    font-variant         : tabular-nums;
    list-style           : none;
    font-feature-settings: 'tnum';
    position             : relative;
    display              : inline-block;
    width                : 100%;
    min-width            : 0;
    background-color     : #fff;
    background-image     : none;
    border               : 1px solid #d9d9d9;
    border-radius        : 2px;
    height               : 32px;
    cursor               : text;
    transition           : all 0.2s;
    overflow             : hidden;
    position             : relative;

    &:hover {
        border-color: #FD8204;
        box-shadow  : 0 0 0 2px rgba(253, 130, 4, 0.1);
    }

    &:focus-within {
        border-color: #FD8204;
        box-shadow  : 0 0 0 2px rgba(253, 130, 4, 0.2);
        outline     : 0;
    }
}

.editTextAreaDisplay {
    padding    : 4px 11px;
    color      : rgba(0, 0, 0, 0.85);
    font-size  : 14px;
    word-break : break-all;
    white-space: pre-wrap;
    height     : 100%;
    width      : 100%;
    cursor     : text;
    overflow   : auto;
}

.editTextAreaContent {
    min-height: 22px;
}

.editTextAreaCount {
    position             : absolute;
    bottom               : -22px;
    right                : 0;
    font-size            : 12px;
    color                : rgba(0, 0, 0, 0.45);
    line-height          : 1.5;
    white-space          : nowrap;
    pointer-events       : none;
    font-size            : 14px;
    font-variant         : tabular-nums;
    line-height          : 1.5715;
    list-style           : none;
    font-feature-settings: 'tnum';
}

.editTextAreaFormItem {
    margin: 0;
    height: 100%;

}

.editTextAreaFormItem {
    :global {
        .ant-input {
            height  : 32px !important;
            overflow: auto !important;
        }
    }
}

.placeholder {
    color: rgba(0, 0, 0, 0.25);
}