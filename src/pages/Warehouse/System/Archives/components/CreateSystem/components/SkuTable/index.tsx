/* eslint-disable react/jsx-indent */
/* eslint-disable indent */
import { Button, Input, Form, FormInstance, InputNumber, Row, Select, Image, Checkbox, Switch, message, Tooltip } from "antd";
import React, { forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef, useState, useCallback } from "react";
import cs from 'classnames';
import { useUpdateEffect } from "ahooks";
import { observer } from 'mobx-react';
import { PlusOutlined, QuestionCircleOutlined, ToTopOutlined } from "@ant-design/icons";
import { WeightStatus, SystemItem, AutoCalculate } from "@/utils/enum/productManage";
import s from './index.module.scss';
import { checkNewSkuId, computeFormData, createFactory, getNewSkuId, keyFactory, skuFactory } from "../../helper";
import { SaveSkuItem } from "@/types/schemas/warehouse/system";
import { ItemSysItemGenerateOuterIdApi } from "@/apis/warehouse/system";
import SearchTable from '@/components/SearchTableVirtual';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import sysDeleteHelper from "../../../SysDeleteHelper";
import { useStores } from "@/stores/tool";
import groupStore from '@/stores/warehouse/Group';
import userStore from "@/stores/user";
import CombinedChoose from "../../../CombinedChoose";
import { hasOwn } from '@/utils';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { DEFAULT_IMG } from "@/constants";
import { autoSearch, weightUnit } from '@/pages/Index/Settings/System/constants';
import { ArchivesType } from "@/pages/Warehouse/constants";
import { accMul } from "@/utils/util";
import UpLoadImg from "../UpLoadImg";
import FieldsPermissionCheck, { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import SupplierSelect from "@/components-biz/SupplierSelect";
import { RowContext, useSort, SortColumn, Sort, DragHandle } from '@/components/SearchTableVirtual/drag-table';
import Icon from '@/components/Icon';
import WeightInput from '@/components/Input/InputNumber/WeightInput';
import upLoadListImg from '@/assets/image/index/批量上传.png';
import EditTextArea, { EditInput } from './editForm';


// 初始的表单数据需要这里添加
const templateData = {};
Object.values(SystemItem).forEach((p) => {
	templateData[p] = '';
});

export interface ISupplierInfo {
	supplierId: string | number;
	supplierName: string;
	market: string;
	stall: string;
}

export interface SkuTableProps {
	onChange?: Function
	setTrue?: Function
	systemItem?: SaveSkuItem[]
	sysItemId?: number
	dialogForm?: FormInstance
	getFormData?: (key?: any) => SaveSkuItem[]
	supplierInfo?: ISupplierInfo
}


const RowComponent = React.memo(({ index, row, }: { index: number, row: any, }) => {
	return (
		<div>
			<div>{index + 1}</div>
			<DragHandle className={ cs('sku-drga-handler', s['sku-drga-handler']) } />
		</div>
	);
});

const SkuTable = ({
	onChange,
	systemItem,
	sysItemId,
	dialogForm,
	getFormData,
	supplierInfo,
	setTrue
}: SkuTableProps, ref: React.Ref<any>) => {
	const [form] = Form.useForm();
	const [editImgForm] = Form.useForm();
	const [batchFillForm] = Form.useForm();
	const [dataSource, setDataSource] = useState([]); // 数据源不受控，仅代表sku数量，具体sku内容通过form获取
	const tableRef = useRef<SearchTableRefProps>();
	// const tableRef = useRef<HTMLDivElement>();
	const store: typeof groupStore = useStores('GroupStore');
	const { archivesType } = store; // combined 组合货品档案
	const [infoModalShow, setInfoModalShow] = useState(false); // 转化组合商品弹框
	const [skulist, setSkuList] = useState([]); // 编辑转化组合数据
	const [combinedId, setCombinedId] = useState(null); // 当前要设置成组合货品的skuid
	const [modifySku, setModifySku] = useState({}); // 当前修改过的sku
	const [isKg, setIsKg] = useState(false);
	const [showErr, setShowErr] = useState({});
	const [upLoadImgObj, setUpLoadImgObj] = useState({ visible: false, picUrl: "", picName: "" });
	const { hasCostPricePermission } = userStore;
	
	// 获取当前实际数据的函数，合并 dataSource 和 modifySku 中的最新数据
	const getCurrentData = useCallback(() => {
		return dataSource.map(item => {
			const modifiedData = modifySku[item.sysSkuId] || {};
			return {
				...item,
				...modifiedData,
				key: String(item.sysSkuId), // 使用 sysSkuId 作为 key，确保为字符串类型
			};
		});
	}, [dataSource, modifySku]);
	
	const { sortFlag, handleSort } = useSort(
		{
			dataIndex: null,
			order: Sort.ASC,
		},
		dataSource,
		setDataSource,
		getCurrentData
	);

	// 设置表格里面的表单
	const setFormValue = useCallback((valList: any[]) => {
		let fieldsValue = {};
		valList.forEach((d: SaveSkuItem) => {
			const sku = createFactory('sku')(d, d.sysSkuId);
			fieldsValue = { ...sku, ...fieldsValue };
		});
		console.log('%c [ 111 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', fieldsValue);
		form.setFieldsValue(fieldsValue);
	}, [form]);


	useImperativeHandle(ref, () => ({
		validateFields: () => {
			return validateFields();
		},
		getFieldsValue: () => {
			return getFieldsValue();
		},
		toSetCombinedSkuList: () => {
			setCombinedSkuList();
		}
	}));

	useEffect(() => {
		userStore.getUserSetting().then(res => {
			setIsKg(res?.weightUnit == weightUnit.显示kg);
		});
		userStore.getSystemSetting();
	}, []);

	useEffect(() => {
		if (supplierInfo) {
			let _modifySku = { ...modifySku };
			let fieldsValue = {};
			const keyValuesMap = ['market', 'stall', 'supplierId', 'supplierName'];
			setDataSource(prev => {
				prev.forEach(item => {
					const { sysSkuId, supplierName } = item;
					keyValuesMap.forEach(key => {
						fieldsValue[keyFactory(key)(sysSkuId)] = supplierInfo[key];
						// fieldsValue[keyFactory(key)(supplierName)] = supplierInfo[key];
						item[key] = supplierInfo[key];
						if (_modifySku[sysSkuId]) {
							_modifySku[sysSkuId][key] = supplierInfo[key];
						} else {
							_modifySku[sysSkuId] = {
								...item,
								[key]: supplierInfo[key],
							};
						}
					});
				});
				return [...prev];
			});
			setModifySku(_modifySku);
			form.setFieldsValue(fieldsValue);
		}
	}, [form, supplierInfo]);

	useEffect(() => {
		if (systemItem && systemItem.length && !ref['current']['lock']) {
			ref['current']['lock'] = true;
			setDataSource([...systemItem]);

			setFormValue(systemItem);
			console.log('%c [ 111 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', systemItem);
		}
	}, [form, ref, systemItem, setFormValue]);

	useUpdateEffect(() => {
		// console.log("🚀 ~ file: index.tsx ~ line 480 ~ useUpdateEffect ~ dataSource", dataSource);
		onChange(dataSource.length);
	}, [dataSource, onChange]);

	const generateOuterIdApi = (formItem: string) => {
		// let _dataSource = [...dataSource];
		let [key, sysSkuId] = formItem.split('_');
		// let index = _dataSource.findIndex(d => d.sysSkuId == sysSkuId);
		ItemSysItemGenerateOuterIdApi({
			type: formItem === 'skuOuterId' ? 2 : 1
		}).then(d => {
			// _dataSource[index][key] = d;
			form.setFieldsValue({
				[formItem]: d
			});
			let _modifySku = { ...modifySku };
			let findItem = dataSource.find(v => v.sysSkuId == sysSkuId);
			if (_modifySku[sysSkuId]) {
				_modifySku[sysSkuId][key] = d;
			} else if (findItem) {
				_modifySku[sysSkuId] = { ...findItem, [key]: d };
			} else {
				_modifySku[sysSkuId] = {
					...templateData,
					sysSkuId,
					groupCombinationList: [],
					isCombination: 0,
					[key]: d,
				};
			}
			setModifySku(_modifySku);
		});
	};

	const inputWeightProps = isKg ? {
		formatter: (value: any) => (value === '' ? value : +(value / 1000).toFixed(3)),
		parser: (value: any) => (value === '' ? value : accMul(value, 1000)),
	} : {
		precision: 0
	};

	// 系统根据单品比例自动计算重量或者成本价
	const autoCalculate = ({ e, row, key }) => {
		const { checked } = e.target;
		const { groupCombinationList = [] } = row;
		let _weight = 0;
		let _cost = 0;
		const select = checked ? 1 : 0;
		const generatorParams = ({ cost, weight }: { cost?: number, weight?: number }) => {
			if (key === AutoCalculate.自动计算成本) {
				return {
					[keyFactory('costPrice')(row.sysSkuId)]: cost,
					[keyFactory(key)(row.sysSkuId)]: select,
				};
			}
			if (key === AutoCalculate.自动计算重量) {
				return {
					[keyFactory('weight')(row.sysSkuId)]: weight,
					[keyFactory(key)(row.sysSkuId)]: select,
				};
			}
		};

		if (checked) {
			groupCombinationList.forEach(item => {
				const { groupProportionNum = 0, costPrice = 0, weight = 0 } = item;
				if (key === AutoCalculate.自动计算成本) {
					_cost += Number(groupProportionNum) * Number(costPrice);
				}
				if (key === AutoCalculate.自动计算重量) {
					_weight += Number(groupProportionNum) * Number(weight);
				}
			});
			let params = null;
			if (key === AutoCalculate.自动计算成本) {
				_cost = Number(_cost.toFixed(2));
				params = generatorParams({ cost: _cost });
			}
			if (key === AutoCalculate.自动计算重量) {
				params = generatorParams({ weight: _weight });
			}
			form.setFieldsValue({ ...params });
			onFieldsChange(params);
		} else {
			const { costPrice, weight } = row;
			let params = {};
			if (key === AutoCalculate.自动计算成本) {
				params = generatorParams({ cost: costPrice });
			}
			if (key === AutoCalculate.自动计算重量) {
				params = generatorParams({ weight });
			}
			form.setFieldsValue({ ...params });
			onFieldsChange(params);
		}
	};

	const editImg = (row, picName) => {
		const picUrl = form.getFieldValue(picName);
		setUpLoadImgObj({
			visible: true,
			picUrl: picUrl || row.picUrl,
			picName
		});
	};

	const handleClickggTil = () => {
		 // 打开批量上传图片弹窗
		 setUpLoadImgObj({
			visible: true,
			picUrl: "",
			picName: "batchUpload", // 使用特殊标识表示批量上传
		  });
	};

	const ggTie = () => {
		return (
			<div>
				规格图片
				<Tooltip title="批量上传图片">
					<img 
						alt="批量上传图片" 
						src={ upLoadListImg }
						onClick={ handleClickggTil }
						style={ { cursor: 'pointer', marginLeft: '4px', width: '16px', height: '16px' } }
					/>
				</Tooltip>
			</div>
		);
	};

	// eslint-disable-next-line react-hooks/exhaustive-deps
	const columns = [
		{
			title: ggTie(),
			className: s.colItem,
			width: 100,
			maxWidth: 600,
			dataIndex: 'skuPic',
			render: (t: string, row: SaveSkuItem, index: number) => {
				const picName = keyFactory('picUrl')(row.sysSkuId);
				return (
					<Form.Item
						noStyle
						shouldUpdate={ (prevValues, currentValues) => prevValues.picName !== currentValues.picName }
					>
						{
							({ getFieldValue }) => (
								<div className={ `r-flex r-ai-c ${s.sysPicContainer}` }>
									<Image
										width={ 54 }
										src={ getFieldValue(picName) || DEFAULT_IMG }
										fallback={ DEFAULT_IMG }
									/>
									<ToTopOutlined className={ `r-fs-18 r-c-999 r-ml-6 ${s.toTopOutlinedIcon} toTopOutlinedIcon` } onClick={ () => { editImg(row, picName); } } />
								</div>
							)
						}

					</Form.Item>

				);
			}
		},
		{
			title: <div>
				<span className={ s['sku-table-header'] }>规格</span>
				<span onClick={ () => handleSort(SortColumn.sysSkuName) } style={ { cursor: 'pointer' } }>
					{sortFlag?.dataIndex === SortColumn.sysSkuName
						? (sortFlag.order === Sort.ASC
							? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
							: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
				</span>
          </div>,
			className: s.colItem,
			width: 150,
			maxWidth: 600,
			dataIndex: 'skuId',
			render: (t: string, row: SaveSkuItem, index: number) => {
				const fieldName = keyFactory('sysSkuName')(row.sysSkuId);
				return (
					<EditTextArea
						name={ fieldName }
						value={ form.getFieldValue(fieldName) }
						placeholder="规格"
						maxLength={ 120 }
						onChange={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
						onBlur={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
					/>
				);
			},
		},
		{
			title: <div>
			<><span className={ cs(s.required) }>*</span>货品规格编码</>
			<span onClick={ () => handleSort(SortColumn.skuOuterId) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.skuOuterId
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			dataIndex: 'skuOuterId',
			width: 150,
			maxWidth: 500,
			className: s.colItem,
			render: (t: string, row: SaveSkuItem, index: number) => {
				return (
					<>
						<Form.Item shouldUpdate name={ keyFactory('skuOuterId')(row.sysSkuId) } rules={ [{ required: true, message: '请输入货品规格编码' }] } style={ { marginBottom: 0 } } >
							<Input onPointerDown={ (e) => e.stopPropagation() } placeholder="货品规格编码" maxLength={ 100 } onKeyDown={ e => e.stopPropagation() } onKeyUp={ e => e.stopPropagation() } />
						</Form.Item>
						<Button type="link" style={ { padding: 0 } } onClick={ () => { generateOuterIdApi(keyFactory('skuOuterId')(row.sysSkuId)); } }>自动生成</Button>
					</>
				);
			},
		},
		{
			title: <div>
			<span className={ s['sku-table-header'] }>规格别名</span>
			<span onClick={ () => handleSort(SortColumn.sysSkuAlias) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.sysSkuAlias
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			maxWidth: 600,
			dataIndex: 'sysSkuAlias',
			className: s.colItem,
			width: 150,
			render: (t: string, row: SaveSkuItem, index: number) => {
				const fieldName = keyFactory('sysSkuAlias')(row.sysSkuId);
				return (
					<EditInput
						name={ fieldName }
						value={ form.getFieldValue(fieldName) }
						placeholder="规格别名"
						maxLength={ 60 }
						onChange={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
						onBlur={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
					/>
				);
			}
		},
		{
			title: <div>
			<span className={ s['sku-table-header'] }>颜色</span>
			<span onClick={ () => handleSort(SortColumn.sysColor) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.sysColor
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			maxWidth: 600,
			dataIndex: 'sysColor',
			className: s.colItem,
			width: 150,
			render: (t: string, row: SaveSkuItem, index: number) => {
				const fieldName = keyFactory('sysColor')(row.sysSkuId);
				return (
					<EditInput
						name={ fieldName }
						value={ form.getFieldValue(fieldName) }
						placeholder="颜色"
						maxLength={ 120 }
						onChange={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
						onBlur={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
					/>
				);
			},
		},
		{
			title: <div>
			<span className={ s['sku-table-header'] }>尺码</span>
			<span onClick={ () => handleSort(SortColumn.sysSize) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.sysSize
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			maxWidth: 600,
			dataIndex: 'sysSize',
			className: s.colItem,
			width: 150,
			render: (t: string, row: SaveSkuItem, index: number) => {
				const fieldName = keyFactory('sysSize')(row.sysSkuId);
				return (
					<EditInput
						name={ fieldName }
						value={ form.getFieldValue(fieldName) }
						placeholder="尺码"
						maxLength={ 120 }
						onChange={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
						onBlur={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
					/>
				);
			},
		},
		{
			title:	<div className={ s['sku-table-head'] }>
			<span className={ s['sku-table-header'] }>售价（元）</span>
			<span onClick={ () => handleSort(SortColumn.price) }>
				{sortFlag?.dataIndex === SortColumn.price
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			dataIndex: 'price',
			className: s.colItem,
			width: 100,
			render: (t: string, row: SaveSkuItem, index: number) => {
				return (
					<Form.Item shouldUpdate name={ keyFactory('price')(row.sysSkuId) } >
						<InputNumber
							placeholder="售价"
							precision={ 2 }
							min={ 0 }
							max={ 999999.99 }
							className={ cs(s.numberInput) }
						/>
					</Form.Item>
				);
			},
		},
		{
			title:	<div>
			<span className={ s['sku-table-header'] }>吊牌（元）</span>
			<span onClick={ () => handleSort(SortColumn.tagPrice) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.tagPrice
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			dataIndex: 'tagPrice',
			className: s.colItem,
			width: 100,
			render: (t: string, row: SaveSkuItem, index: number) => {
				return (
					<Form.Item shouldUpdate name={ keyFactory('tagPrice')(row.sysSkuId) }>
						<InputNumber
							placeholder="吊牌价"
							precision={ 2 }
							min={ 0 }
							max={ 999999.99 }
							className={ cs(s.numberInput) }
						/>
					</Form.Item>
				);
			},
		},
		{
			title: <div>
			<span className={ s['sku-table-header'] }>成本价（元）</span>
			<span onClick={ () => handleSort(SortColumn.costPrice) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.costPrice
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			dataIndex: 'costPrice',
			className: s.colItem,
			width: 120,
			render: (t: string, row: SaveSkuItem, index: number) => {
				return (
					<>
						<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 } >
							<Form.Item shouldUpdate name={ keyFactory('costPrice')(row.sysSkuId) } style={ { marginBottom: 6 } }>
								<InputNumber
									disabled={ !hasCostPricePermission || form.getFieldValue(keyFactory(AutoCalculate.自动计算成本)(row.sysSkuId)) }
									placeholder="成本价"
									precision={ 4 }
									min={ 0 }
									max={ 999999.99 }
									className={ cs(s.numberInput) }
								/>
							</Form.Item>
						</FieldsPermissionCheck>
						{
							archivesType === ArchivesType.组合货品 ? (
								<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 }>
									<Form.Item
										shouldUpdate
										valuePropName="checked"
										name={ keyFactory(AutoCalculate.自动计算成本)(row.sysSkuId) }
									>
										<Checkbox disabled={ !hasCostPricePermission } onChange={ (e) => { autoCalculate({ e, row, key: AutoCalculate.自动计算成本 }); } } style={ { display: "flex" } }>
											<div className="r-fs-13 r-c-gray r-flex r-ai-c">
												<div>自动计算</div>
												<Tooltip title="系统根据单品比例自动计算">
													<QuestionCircleOutlined className="r-ml-2" />
												</Tooltip>
											</div>
										</Checkbox>
									</Form.Item>
								</FieldsPermissionCheck>
							) : null
						}
					</>

				);
			},
		},
		{
			title: <div>
			<span className={ s['sku-table-header'] }>重量（{isKg ? 'kg' : 'g'}）</span>
			<span onClick={ () => handleSort(SortColumn.weight) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.weight
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			dataIndex: 'weight',
			className: s.colItem,
			width: 120,
			render: (t: string, row: SaveSkuItem, index: number) => {
				return (
					<>
						{Object.keys(WeightStatus).map((weight) => {
							return (
								<>
									<Form.Item shouldUpdate key={ weight } name={ keyFactory(WeightStatus[weight])(row.sysSkuId) } style={ { marginBottom: 6 } } >
										{/* <InputNumber
											placeholder={ weight }
											min={ 0 }
											max={ 99999999 }
											{ ...inputWeightProps }
											disabled={ weight === "重量" && form.getFieldValue(keyFactory(AutoCalculate.自动计算重量)(row.sysSkuId)) }
										/> */}
										<WeightInput
											isKg={ isKg }
											onChange={ (value) => {
												const allFields = form.getFieldsValue();
												const rowId = row.sysSkuId;
												const getRowFields = (fields, rowId) => {
													const regex = new RegExp(`${rowId}$`);
													return Object.keys(fields)
														.filter(key => regex.test(key))
														.reduce((rowData, key) => {
															rowData[key] = fields[key];
															return rowData;
														}, {});
												};
												const rowData = getRowFields(allFields, rowId);
												form.setFieldsValue({
													...rowData,
													[keyFactory(WeightStatus[weight])(row.sysSkuId)]: value
												});
											} }
											disabled={ weight === "重量" && form.getFieldValue(keyFactory(AutoCalculate.自动计算重量)(row.sysSkuId)) }
										/>
									</Form.Item>

								</>
							);
						})}
						{
							archivesType === ArchivesType.组合货品 ? (
								<Form.Item
									shouldUpdate
									name={ keyFactory(AutoCalculate.自动计算重量)(row.sysSkuId) }
									valuePropName="checked"
								>
									<Checkbox onChange={ (e) => { autoCalculate({ e, row, key: AutoCalculate.自动计算重量 }); } }>
										<div className="r-fs-13 r-c-gray r-flex r-ai-c">
											<div>自动计算</div>
											<Tooltip title="系统根据单品比例自动计算">
												<QuestionCircleOutlined className="r-ml-2" />
											</Tooltip>
										</div>
									</Checkbox>
								</Form.Item>
							) : null
						}
					</>
				);
			},
		},
		{
			title: <div>
			<span className={ s['sku-table-header'] }>货号</span>
			<span onClick={ () => handleSort(SortColumn.itemNo) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.itemNo
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			dataIndex: 'itemNo',
			className: s.colItem,
			width: 120,
			render: (t: string, row: SaveSkuItem, index: number) => {
				const fieldName = keyFactory('itemNo')(row.sysSkuId);
				return (
					<EditInput
						name={ fieldName }
						value={ form.getFieldValue(fieldName) }
						placeholder="货号"
						maxLength={ 32 }
						onChange={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
						onBlur={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
					/>
				);
			},
		},
		{
			title:	<div>
			<span className={ s['sku-table-header'] }>条形码</span>
			<span onClick={ () => handleSort(SortColumn.barCode) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.barCode
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			dataIndex: 'barCode',
			className: s.colItem,
			width: 120,
			render: (t: string, row: SaveSkuItem, index: number) => {
				const fieldName = keyFactory('barCode')(row.sysSkuId);
				return (
					<EditInput
						name={ fieldName }
						value={ form.getFieldValue(fieldName) }
						placeholder="条形码"
						maxLength={ 32 }
						onChange={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
						onBlur={ (value) => {
							form.setFieldsValue({ [fieldName]: value });
							onFieldsChange({ [fieldName]: value });
						} }
					/>
				);
			},
		},
		{
			title: '库存同步',
			dataIndex: 'syncStockFlag',
			width: 100,
			className: s.colItem,
			render: (text, row: SaveSkuItem, index) => {
				if (row.isCollapse) return null;
				const val = row.syncStockFlag;
				const loading = row.syncStockFlagLoading;
				return (
					<Form.Item shouldUpdate name={ keyFactory("syncStockFlag")(row.sysSkuId) } >
						<Switch
							checkedChildren="开启"
							unCheckedChildren="关闭"
							checked={ val > 0 }
							loading={ loading }
							onClick={ (checked) => { handleChangeSyncStockFlag(checked, row, index); } }
						/>
					</Form.Item>

				);
			}
		},

		{
			title: <div>
			<span className={ s['sku-table-header'] }>供应商</span>
			<span onClick={ () => handleSort(SortColumn.supplierId) } style={ { cursor: 'pointer' } }>
				{sortFlag?.dataIndex === SortColumn.supplierId
					? (sortFlag.order === Sort.ASC
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
          </div>,
			dataIndex: 'supplierId',
			className: s.colItem,
			width: 140,
			render: (t: string, row: SaveSkuItem, index: number) => {
				return (
					<div className={ cs(s.supplierContainer) }>
						<Form.Item labelCol={ { span: 8 } } name={ keyFactory('supplierId')(row.sysSkuId) }>
							<SupplierSelect
								value={ keyFactory('supplierId')(row.sysSkuId) }
								supplierName={ form.getFieldValue(keyFactory('supplierName')(row.sysSkuId)) }
								allowClear
								onChange={ (supplier) => {
									sendPoint(Pointer.商品_商品编辑_选择供应商);
									form.setFieldsValue({
										[keyFactory('market')(row.sysSkuId)]: supplier?.market,
										[keyFactory('stall')(row.sysSkuId)]: supplier?.stall,
										[keyFactory('supplierId')(row.sysSkuId)]: supplier?.id,
										[keyFactory('supplierName')(row.sysSkuId)]: supplier?.supplierName,
									});
								} }
							/>
						</Form.Item>
						<div className="r-flex r-c-666 r-fw-w">
							{
								form.getFieldValue(keyFactory('market')(row.sysSkuId))
									? (<span className="r-mr-8">市场:{form.getFieldValue(keyFactory('market')(row.sysSkuId))}</span>)
									: null
							}
							{
								form.getFieldValue(keyFactory('stall')(row.sysSkuId))
									? (<span>档口:{form.getFieldValue(keyFactory('stall')(row.sysSkuId))}</span>)
									: null
							}
						</div>
					</div>
				);
			},
		},
		{
			title: '操作',
			className: s.colItem,
			dataIndex: 'operateAction',
			width: 60,
			render: (t: string, r: SaveSkuItem, index: number) => {
				return (
					<>
						<Button size="small" className="r-mb-2" type="link" onClick={ () => { deleteSku(r); } }>删除</Button>
						<Button size="small" type="link" onClick={ () => { cloneSku(r.sysSkuId, index); } }>克隆</Button>
						{/* 保持规格id */}
						{(r.sysSkuId + "").indexOf('sysSkuId') === -1 && (
							<div style={ { display: 'none' } }>
								<Form.Item shouldUpdate name={ keyFactory('sysSkuId')(r.sysSkuId) }>
									<Input placeholder="sysSkuId" maxLength={ 32 } />
								</Form.Item>
								<Form.Item shouldUpdate name={ keyFactory('sysItemId')(r.sysSkuId) }>
									<Input placeholder="sysItemId" maxLength={ 32 } />
								</Form.Item>
							</div>
						)}
					</>
				);
			}
		}
	];

	const handleChangeSyncStockFlag = (val, item, index) => {
		item.syncStockFlag = val ? 1 : 0;
		setDataSource([...dataSource]);
	};

	// 设置组合货品
	// eslint-disable-next-line react-hooks/exhaustive-deps
	const setCombinedGoods = (row: SaveSkuItem) => {
		setCombinedSkuList(row?.groupCombinationList || [], row?.sysSkuId);
	};

	// 新建页面打开步骤选择弹框
	const setCombinedSkuList = (data: any[] = [], sysSkuId?: any) => {
		setSkuList(data);
		setCombinedId(sysSkuId);
		setInfoModalShow(true);
	};

	// 步骤弹框保存回调
	const onChooseSave = (data: any[] = [], isEdit: boolean) => {
		console.log('onChooseSave', data);
		setInfoModalShow(false);
		// onDone?.();
		if (data.length) { // 如果有值
			let newLlist: any[] = [];
			// let rowData = dataSource.find(item => item.sysSkuId == combinedId);
			// console.log('rowData', rowData);
			data.forEach(item => {
				let newItem = item?.sysSkuList?.[0];
				if (newItem) {
					newItem['groupProportionNum'] = item.groupProportionNumChangeValue ?? newItem.groupProportionNum; // 这里肯定有值
					newLlist.push(newItem);
				}
			});
			// console.log(newLlist);
			// 判断是修改还是新增
			if (combinedId) {
				let dataTable = [...dataSource];
				let index = dataTable.findIndex(item => item.sysSkuId == combinedId);
				if (~index) {
					dataTable[index]['groupCombinationList'] = newLlist;
					dataTable[index]['isCombination'] = 1;
				}
				setDataSource(pre => {
					let tableArr: any[] = [];
					dataTable.forEach((item: any, _index) => {
						if (_index === index) {
							const newPrice: object = item.isCombination ? computeFormData(item.groupCombinationList) : {};
							tableArr.push({
								...item,
								...newPrice
							});
						} else {
							tableArr.push({
								...item
							});
						}

					});
					// 更新计算重量、价格
					getFormDataFun(tableArr, true, index);
					return [...tableArr];
				});

			} else {
				console.log('新增一个组合商品');
				addSku({
					groupCombinationList: newLlist,
					isCombination: 1,
					...computeFormData(newLlist)
				});
			}
		}
	};

	// 步骤弹框取消回调
	const onChooseCancel = () => {
		console.log('onChooseCancel');
		setInfoModalShow(false);
	};

	const getColumns = useMemo(() => {
		const showColumns = [...columns];
		const itemCustomAttributeList = userStore?.systemSetting?.itemCustomAttributeDTOList?.filter((item) => (item?.name));
		if (userStore?.systemSetting?.itemCustomAttribute === autoSearch.开启 && itemCustomAttributeList?.length) {
			const _list = itemCustomAttributeList.map((item) => (
				{
					title: item.name,
					dataIndex: item.key,
					className: s.colItem,
					width: 180,
					render: (t: string, row: SaveSkuItem, index: number) => {
						const fieldName = keyFactory(`${item.key}`)(row.sysSkuId);
						return (
							<EditInput
								name={ fieldName }
								value={ form.getFieldValue(fieldName) }
								placeholder={ item.name }
								maxLength={ 120 }
								onChange={ (value) => {
									form.setFieldsValue({ [fieldName]: value });
									onFieldsChange({ [fieldName]: value });
								} }
								onBlur={ (value) => {
									form.setFieldsValue({ [fieldName]: value });
									onFieldsChange({ [fieldName]: value });
								} }
							/>
						);
					}
				}
			));
			showColumns.splice(11, 0, ..._list);
		}
		if (archivesType == 'combined') {
			const combinedColumn = {
				title: <><span className={ cs(s.required) }>*</span>组合</>,
				width: 100,
				align: 'center',
				dataIndex: 'combined',
				className: s.colItem,
				render: (t: string, row: SaveSkuItem, index: number) => {
					const { groupCombinationList = [], isCombination } = row || {};
					// N种(m件)
					let allCount = groupCombinationList.reduce((prev: number, item: any) => {
						return prev += Number(item?.groupProportionNum || 0);
					}, 0);
					return (
						isCombination == 1 ? (
							<div className="r-flex r-fd-c">
								<span>{groupCombinationList.length}种({allCount}件)</span>
								<Button type="link" onClick={ () => setCombinedGoods(row) } >设置</Button>
							</div>
						) : (
							<div className="r-flex r-fd-c r-ai-c">
								{/* <span>非组合</span> */}
								<Button type="link" onClick={ () => setCombinedGoods(row) } >设置</Button>
								{showErr[row.sysSkuId] ? <span style={ { color: '#f5222d', fontSize: 14 } }>请设置组合</span> : ''}
							</div>
						)
					);
				}
			};
			showColumns.splice(5, 0, combinedColumn);
		}

		// 新增货位
		if (archivesType == 'single' || archivesType == 'singleSku') {
			const warehouseSlotColumn = {
				title: <div>
				<span className={ s['sku-table-header'] }>货位</span>
				<span onClick={ () => handleSort(SortColumn.warehouseSlotName) } style={ { cursor: 'pointer' } }>
					{sortFlag?.dataIndex === SortColumn.warehouseSlotName
						? (sortFlag.order === Sort.ASC
							? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
							: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
				</span>
           </div>,
				maxWidth: 600,
				dataIndex: 'warehouseSlotName',
				className: s.colItem,
				width: 150,
				render: (t: string, row: SaveSkuItem, index: number) => {
					const fieldName = keyFactory('warehouseSlotName')(row.sysSkuId);
					return (
						<EditInput
							name={ fieldName }
							value={ form.getFieldValue(fieldName) }
							placeholder="货位"
							maxLength={ 32 }
							onChange={ (value) => {
								form.setFieldsValue({ [fieldName]: value });
								onFieldsChange({ [fieldName]: value });
							} }
							onBlur={ (value) => {
								form.setFieldsValue({ [fieldName]: value });
								onFieldsChange({ [fieldName]: value });
							} }
						/>
					);
				}
			};
					  // 找到规格别名列的实际位置
			const sysSkuAliasIndex = showColumns.findIndex(col => col.dataIndex === 'sysSkuAlias');
			showColumns.splice(sysSkuAliasIndex + 1, 0, warehouseSlotColumn);
		}
		return showColumns;
	}, [archivesType, columns, setCombinedGoods, showErr, userStore.systemSetting]);

	const getFieldsValue = () => {
		return dataSource;
	};

	const validateFields = async() => {
		// 滚到底部
		const tableBody = document.querySelector('#myBaseTable .ant-table-body');
		// tableBody.scrollTop = tableBody.scrollHeight + 40;
		let keys = Object.keys(modifySku) || [];
		// for (let i = 0; i < keys.length; i++) {
		// 	if (!modifySku[keys[i]].skuOuterId) {
		// 		let index = dataSource.findIndex(d => d.sysSkuId == keys[i]);
		// 		tableBody.scrollTop = 257 * (index);
		// 		break;
		// 	}
		// }

		return new Promise((resolve, reject) => {
			setTimeout(async() => {
				console.log('modifySku', modifySku);
				let _dataSource = [...dataSource];
				keys.forEach(sysSkuId => {
					let newSku = modifySku[sysSkuId];
					let index = _dataSource.findIndex(d => d.sysSkuId == sysSkuId);
					if (index > -1) {
						let { sysItemId, isCombination = 0, groupCombinationList } = _dataSource[index];
						_dataSource.splice(index, 1, { ...newSku, isCombination, groupCombinationList, sysItemId });
					}
				});
				_dataSource.forEach(item => {
					if (!hasOwn(item, 'isCombination')) { // 普通和组合都加新的参数
						item['groupCombinationList'] = item.groupCombinationList || [];
						item['isCombination'] = item.isCombination || 0;
					}
				});
				let hasWrong = false;
				if (archivesType == 'combined') {
					const newShowErr = {};
					_dataSource.forEach(item => {
						newShowErr[item.sysSkuId] = !item.isCombination;
					});
					setShowErr(newShowErr);
					console.log('hasWrong 11:', newShowErr, Object.values(newShowErr));
					hasWrong = Object.values(newShowErr).some(i => i);
				}
				try {
					const data = await form.validateFields(); // 校验表单
					console.log(' form.validateFields', data);
				} catch (error) {
					console.log('hasWrong:', error);
					hasWrong = true;
				}

				console.log('_dataSource', _dataSource);
				if (hasWrong) {
					return reject();
				}
				resolve(_dataSource);
				// return _dataSource;
			}, 200);
		});
	};

	// 添加sku
	const addSku = async(combinedObj: any = {}) => {
		if (combinedObj?.currentTarget) {
			combinedObj = {};
		}
		const dialogFormObj = { ...dialogForm?.getFieldsValue() };
		let sysSkuId = getNewSkuId();
		let newSku = { ...templateData, sysSkuId, ...combinedObj };
		if (dialogFormObj.skuOuterId) {
			newSku.skuOuterId = dialogFormObj.skuOuterId;
		}
		if (dialogFormObj.picUrl) {
			newSku.picUrl = dialogFormObj.picUrl;
		}

		if (supplierInfo) {
			newSku = {
				...newSku,
				[`supplierId_${sysSkuId}`]: supplierInfo.supplierId,
				[`supplierName_${sysSkuId}`]: supplierInfo.supplierName,
				[`stall_${sysSkuId}`]: supplierInfo.stall,
				[`market_${sysSkuId}`]: supplierInfo.market,
			};
		}
		setDataSource(dataSource.concat(newSku)); // combinedObj组合货品内容

		form.setFieldsValue(newSku);

		archivesType == 'combined' && dataSource.length == 0 && getFormDataFun(dataSource.concat(newSku));
		// 滚动到底部
		// const tableBody = tableRef.current.getElementsByClassName("ant-table-body")[0];
		// const tableBody = document.querySelector('#myBaseTable .ant-table-body');
		// setTimeout(() => {
		// 	tableBody.scrollTop = tableBody.scrollHeight + 40;
		// }, 0);


		setModifySku(pre => {
			return {
				...pre,
				[newSku.sysSkuId]: {
					...newSku
				}
			};
		});

		setTimeout(() => {
			tableRef?.current?.scrollToIndex(dataSource.length - 1);
			const tableBody = document.querySelector('#myBaseTable .ant-table-body');
			tableRef?.current?.scrollTo(tableBody.scrollHeight + 40);
		}, 10);
	};

	// 删除单个sku
	const deleteSku = async(r) => {
		const { sysSkuId } = r;
		// 若编辑模式，需要判断当前sku是否可以删除
		if (sysItemId && !checkNewSkuId(sysSkuId as string)) {
			await sysDeleteHelper.deleteCheck(r);
		}
		// if (sysItemId) {
		// 删除当前改动的sku
		if (modifySku[sysSkuId]) {
			let _modifySku = { ...modifySku };
			delete _modifySku[sysSkuId];
			// _modifySku[sysSkuId] = undefined;
			setModifySku(_modifySku);
		}
		// }
		setDataSource([...dataSource.filter(d => d.sysSkuId !== sysSkuId)]);
	};
	// 克隆sku
	const cloneSku = (sysSkuId: number | string, index: number) => {
		// 拿到当前列数据
		const sku = form.getFieldsValue(skuFactory(sysSkuId));
		const clone = { ...dataSource.filter(d => d.sysSkuId === sysSkuId)[0], sysSkuId: getNewSkuId() };
		const newSku = {};
		const _newSku = {};
		dataSource.splice(index + 1, 0, clone);
		setDataSource([...dataSource]);
		// 回填表单数据
		skuFactory(clone.sysSkuId).forEach((item) => {
			const [type, id] = item.split('_');
			newSku[item] = type === 'skuOuterId' ? '' : sku[keyFactory(type)(sysSkuId)];
			_newSku[type] = type === 'skuOuterId' ? '' : sku[keyFactory(type)(sysSkuId)];
		});
		form.setFieldsValue(newSku);

		setModifySku(pre => {
			return {
				...pre,
				[clone.sysSkuId]: {
					...clone,
					..._newSku
				}
			};
		});
	};

	const getFormDataFun = useCallback(async(data, isEdit = false, index = -1) => {
		if (isEdit) {
			const formFieldsVal = form.getFieldsValue();
			let mainFormValObj = {};
			let obj = {};
			data.forEach((item: any, _index) => {
				const { sysSkuId } = item;
				if (index > -1 && item.isCombination && _index == index) {
					const newPrice: object = item.isCombination ? computeFormData(item.groupCombinationList) : {};
					obj[sysSkuId] = {
						sysSkuId,
						...newPrice
					};
				} else {
					obj[sysSkuId] = {
						sysSkuId,
					};
				}

				mainFormValObj[sysSkuId] = {
					...item,
					...modifySku[sysSkuId],
				};
			});

			for (const key in formFieldsVal) {
				let propsArr = key.split('_'); // 形式：weight_sysSkuId1656050647812
				let propsKey = propsArr[0];
				let propsSysSkuId = propsArr[1];
				mainFormValObj[propsSysSkuId][propsKey] = formFieldsVal[key];
			}
			let mainFormVal: any[] = [];
			for (const key in obj) {
				mainFormVal.push({
					...mainFormValObj[key],
					...obj[key]
				});
			}
			console.log(mainFormVal[0].sysSkuId, '组合商品手动回填主表单数据', mainFormVal, modifySku);

			setFormValue(mainFormVal);
			setModifySku(pre => {
				let curModifySku = {};
				for (const key in pre) {
					curModifySku[key] = {
						...pre[key],
						...obj[key]
					};
				}
				return {
					...curModifySku
				};
			});

		} else {

			let curSkuIdKey = data[data.length - 1].sysSkuId.replace('sysSkuId', '');
			let mainFormVal = await getFormData(curSkuIdKey);
			// 价格、重量计算
			data.forEach((item: any) => {
				if (item?.sysSkuId === mainFormVal[0]?.sysSkuId) {
					const typearr = ['weight', 'netWeight', 'price', 'retailPrice', 'tradePrice', 'tagPrice', 'costPrice'];
					typearr.forEach((key: string) => {
						mainFormVal[0][key] = mainFormVal[0][key] ? mainFormVal[0][key] : item[key];
					});
				}
			});

			console.log(mainFormVal[0].sysSkuId, '组合商品手动回填主表单数据', mainFormVal);
			setFormValue(mainFormVal);
		}

	}, [getFormData, setFormValue, form, modifySku]);



	const onFieldsChange = (changedFields: any, allFields?: any) => {
		let keys = Object.keys(changedFields);
		let obj = {};
		let _modifySku = { ...modifySku };
		keys.forEach((key) => {
			let [fields, sysSkuId] = key.split("_");
			if (_modifySku[sysSkuId]) {
				obj = { ..._modifySku[sysSkuId] };
			} else {
				let item = dataSource.find(d => d.sysSkuId == sysSkuId) || {};
				obj = { ...item };
			}
			obj[fields] = changedFields[key] ?? '';
			_modifySku[sysSkuId] = obj;
		});
		setModifySku(pre => {
			return {
				...pre,
				..._modifySku
			};
		});
	};

	// 批量填充
	const batchFill = () => {
		const _dataSource = [...dataSource];
		const obj = batchFillForm.getFieldsValue();
		for (let key in obj) {
			if ([undefined, "", null].includes(obj[key])) delete obj[key];
		}
		if (!_dataSource.length) {
			message.warning("请先添加规格后再进行批量操作");
			return;
		}
		if (!Object.keys(obj).length) {
			message.warning("请输入需要批量填充的内容");
			return;
		}

		if (obj.supplierId) {
			obj.supplierName = obj.supplierName || "";
			obj.market = obj.market || "";
			obj.stall = obj.stall || "";
		}

		const params = {};
		Object.keys(obj).forEach((key) => {
			_dataSource.forEach((item) => {
				item[key] = obj[key];
				params[keyFactory(key)(item.sysSkuId)] = obj[key];
			});
		});
		form.setFieldsValue({ ...params });
		onFieldsChange({ ...params });
		setDataSource([..._dataSource]);
		message.success("批量填充成功");
	};

	const handleUpLoadImgOk = ({ picUrl, picName }) => {
		if (picName === "batchUpload") {
			// 批量设置所有规格图片
			const params = {};
			dataSource?.forEach(item => {
			  const itemPicName = keyFactory('picUrl')(item.sysSkuId);
			  params[itemPicName] = picUrl;
			});
			form.setFieldsValue(params);
			onFieldsChange(params);
		  } else {
			// 原有的单个规格图片设置逻辑
			const params = { [picName]: picUrl };
			form.setFieldsValue(params);
			onFieldsChange(params);
		  }
		  handleUpLoadImgCancel();
	};

	const handleUpLoadImgCancel = () => {
		setUpLoadImgObj({
			visible: false,
			picUrl: "",
			picName: ""
		});
	};
	const totalWidth = getColumns.reduce((sum, column) => sum + (column.width || 0), 0);
	const handleDragEnd = (arr) => {
		arr.forEach((i, idx) => {
			i.sort = idx + 1;
		});
		setDataSource(arr);
	};
	console.log('所有列宽度之和:getColumns', totalWidth);
	return (
		<div>
			<div style={ { textAlign: 'left' } } className="r-flex r-pl-16 r-pt-12">
				<Button onClick={ addSku } className="r-mr-8 r-c-666" type="primary" ghost>添加规格<PlusOutlined /></Button>
				<Form
					form={ batchFillForm }
					name="basic"
					onFinish={ batchFill }
				>
					<Row>
						<Form.Item name="price" >
							<InputNumber
								placeholder="售价（元）"
								precision={ 2 }
								min={ 0 }
								max={ 999999.99 }
								className={ cs(s.batchFillFormInput) }
							/>
						</Form.Item>
						<Form.Item name="tagPrice" >
							<InputNumber
								placeholder="吊牌价（元）"
								precision={ 2 }
								min={ 0 }
								max={ 999999.99 }
								className={ cs(s.batchFillFormInput) }
							/>
						</Form.Item>
						<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 }>
							<Form.Item className={ s.batchFillFormInput } name="costPrice" >
								<InputNumber
									disabled={ !hasCostPricePermission }
									placeholder="成本价（元）"
									precision={ 4 }
									min={ 0 }
									max={ 999999.99 }
									className={ cs(s.batchFillFormInput) }
								/>
							</Form.Item>
						</FieldsPermissionCheck>
						<Form.Item name="weight" >
							<InputNumber
								placeholder={ `重量（${isKg ? 'k' : ''}g）` }
								{ ...inputWeightProps }
								min={ 0 }
								max={ 99999999 }
								className={ s.batchFillFormInput }
							/>
						</Form.Item>
						<Form.Item name="syncStockFlag" className="r-mr-8">
							<Select
								placeholder="库存同步"
								allowClear
								style={ { width: 100 } }
							>
								<Select.Option value={ 1 }>开启</Select.Option>
								<Select.Option value={ 0 }>关闭</Select.Option>
							</Select>
						</Form.Item>
						<Form.Item className="r-mr-8" name="supplierId">
							<SupplierSelect
								value="supplierId"
								allowClear
								onChange={ (supplier) => {
									batchFillForm.setFieldsValue({
										market: supplier?.market,
										stall: supplier?.stall,
										supplierId: supplier?.id,
										supplierName: supplier?.supplierName,
									});
								} }
							/>
						</Form.Item>
						<Form.Item name="supplierName">
							<span />
						</Form.Item>
						<Form.Item name="market">
							<span />
						</Form.Item>
						<Form.Item name="stall">
							<span />
						</Form.Item>
						<Form.Item>
							<Button htmlType="submit">批量填充</Button>
						</Form.Item>
						<Button data-point={ Pointer.商品_商品编辑_新增供应商 } onClick={ () => { setTrue(); } } className={ cs('r-ml-8') }>新增供应商</Button>
					</Row>

				</Form>
			</div>
			<SearchTable
				ref={ tableRef }
				form={ form }
				hidePagination
				baseTableConfig={ {
					headerColSet: {
						resizeId: `SkuTable_${archivesType}_width_${userStore?.userInfo?.userId}_${userStore?.userInfo?.subUserId}`
					},
					dataSource,
					rowKey: 'sysSkuId',
					columns: getColumns,
					pagination: false,
					cachePgination: true,
					innerTableStyle: cs(s.innerSkuTable),
					scroll: {
						scrollToFirstRowOnChange: true,
						x: true, // 确保横向滚动正常工作
						y: undefined // 禁用纵向滚动
					},
					onFieldsChange,
					rowContext: RowContext,
					expandWidth: 200,
					estimatedRowHeight: 95,
					emptyCellHeight: 95,
					useDrag: true,
					offsetY: 1000,
					// openDragSort: true,
					handleDragEnd,
					AUTO_VIRTUAL_THRESHOLD: 10,
					// useVirtual: { horizontal: false, vertical: true, header: false },
				} }
			/>
			<UpLoadImg upLoadImgObj={ upLoadImgObj } onOk={ handleUpLoadImgOk } onCancel={ handleUpLoadImgCancel } />

			{/* 选择组合货品的组成部分 */}
			{
				archivesType == 'combined' && <CombinedChoose onSave={ onChooseSave } onCancel={ onChooseCancel } isVisible={ infoModalShow } list={ skulist } type="add" />
			}
		</div>
	);
};

export default memo(observer(forwardRef(SkuTable)));
