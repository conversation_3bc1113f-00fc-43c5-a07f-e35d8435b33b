.innerSkuTable {
	padding: 0 16px;

	:global {

		.ant-table-body {
			max-height: none !important;
			overflow-y: visible !important;
			overflow-x: auto !important; // 保持横向滚动

		}
	}

	td {
		vertical-align: top;
	}
}

:global {
	.hovered-row {

		.dragHandleIcon,
		.toTopOutlinedIcon {
			visibility: visible !important;
		}

		.dragHandleIcon:hover {
			color: #fd8204;
		}
	}
}

.colItem {
	vertical-align: top;
	padding       : 4px;
}

.textArea {
	textarea {
		resize: none;
	}
}

.required {
	margin-right: 4px;
	color       : #ff4d4f;
	font-size   : 14px;
	font-family : SimSun, sans-serif;
	line-height : 1;
}

.supplierContainer {
	display       : flex;
	flex-direction: column;
}

.batchFillFormInput {
	width       : 120px;
	margin-right: 12px;
}

.toTopOutlinedIcon {
	visibility : hidden;
	margin-left: 8px;
}

.sysPicContainer:hover>.toTopOutlinedIcon {
	visibility: visible
}

.sku-table-head {
	display    : flex;
	align-items: center;

	.sku-table-header {
		font-weight: bold;
	}
}

.sku-drga-handler {
	visibility : hidden !important;
	will-change: visibility;
}

:global {
	.ant-table-row:hover .sku-drga-handler {
		visibility: visible !important;
	}
}