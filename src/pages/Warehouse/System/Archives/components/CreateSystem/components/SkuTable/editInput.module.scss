.editInputContainer {
    vertical-align       : bottom;
    box-sizing           : border-box;
    margin               : 0;
    font-variant         : tabular-nums;
    list-style           : none;
    font-feature-settings: 'tnum';
    position             : relative;
    display              : inline-block;
    width                : 100%;
    min-width            : 0;
    background-color     : #fff;
    background-image     : none;
    border               : 1px solid #d9d9d9;
    border-radius        : 2px;
    height               : 32px;
    min-height           : 32px;
    cursor               : text;
    transition           : all 0.2s;
    overflow             : hidden;
    font-family          : -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;

    &:hover {
        border-color: #FD8204;
        box-shadow  : 0 0 0 2px rgba(253, 130, 4, 0.1);
    }

    &:focus-within {
        border-color: #FD8204;
        box-shadow  : 0 0 0 2px rgba(253, 130, 4, 0.2);
        outline     : 0;
    }

    &.disabled {
        color           : rgba(0, 0, 0, 0.25);
        background-color: #f5f5f5;
        border-color    : #d9d9d9;
        cursor          : not-allowed;

        &:hover {
            border-color: #d9d9d9;
            box-shadow  : none;
        }
    }
}

.editInputDisplay {
    padding      : 4px 11px;
    color        : rgba(0, 0, 0, 0.85);
    font-size    : 14px;
    line-height  : 1.5715;
    word-break   : break-all;
    white-space  : nowrap;
    overflow     : hidden;
    text-overflow: ellipsis;
    height       : 100%;
    width        : 100%;
    cursor       : text;
    display      : flex;
    align-items  : center;

    &.disabled {
        color : rgba(0, 0, 0, 0.25);
        cursor: not-allowed;
    }
}

.editInputContent {
    min-height : 22px;
    padding-top: 0;
    display    : flex;
    align-items: center;
    width      : 100%;

    &.placeholder {
        color: rgba(0, 0, 0, 0.25);
    }
}

.editInputFormItem {
    margin: 0;
    height: 100%;

    :global {
        .ant-form-item-control-input {
            min-height: 100%;
        }

        .ant-form-item-control-input-content {
            height: 100%;
        }

        .ant-input {
            border     : none !important;
            box-shadow : none !important;
            outline    : none !important;
            padding    : 4px 11px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
            background : transparent;
            height     : 100%;

            &:hover,
            &:focus,
            &:focus-within {
                border    : none !important;
                box-shadow: none !important;
            }
        }
    }
}