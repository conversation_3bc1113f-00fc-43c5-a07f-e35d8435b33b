/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-06-19 18:51:29
 * @Description: 可编辑的TextArea组件，点击进入编辑模式，点击外部退出编辑模式
 */
import React, { useRef, useState, useEffect } from 'react';
import { useClickAway } from 'ahooks';
import { Form, Input } from 'antd';
import cs from 'classnames';
import s from './editForm.module.scss';

interface EditTextAreaProps {
	value?: string;
	placeholder?: string;
	maxLength?: number;
	name: string;
	onChange?: (value: string) => void;
	onBlur?: (value: string) => void;
}

export { default as EditInput } from './editInput';

export default function EditTextArea(props: EditTextAreaProps) {
	const [isEditing, setIsEditing] = useState(false);
	const [inputValue, setInputValue] = useState('');
	const [cursorPosition, setCursorPosition] = useState(0);
	const { value, placeholder, maxLength, name, onChange, onBlur } = props;
	const ref = useRef<HTMLDivElement>(null);
	const inputRef = useRef<any>(null);
	const displayRef = useRef<HTMLDivElement>(null);
	
	useClickAway(() => {
		if (isEditing) {
			setIsEditing(false);
			onBlur?.(inputValue);
		}
	}, ref);

	useEffect(() => {
		setInputValue(value || '');
	}, [value]);

	useEffect(() => {
		if (isEditing && inputRef.current) {
			// 进入编辑模式时自动聚焦并设置光标位置
			setTimeout(() => {
				inputRef.current?.focus();
				// 设置光标位置
				if (inputRef.current?.resizableTextArea?.textArea) {
					const textArea = inputRef.current.resizableTextArea.textArea;
					textArea.setSelectionRange(cursorPosition, cursorPosition);
				}
			}, 10);
		}
	}, [isEditing, cursorPosition]);

	// 计算点击位置对应的字符索引
	const getCharacterIndexFromPoint = (e: React.MouseEvent) => {
		if (!displayRef.current || !inputValue) return 0;
		const rect = displayRef.current.getBoundingClientRect();
		const clickX = e.clientX - rect.left - 11; // 减去padding-left (11px)
		// 创建canvas来测量文本宽度
		const canvas = document.createElement('canvas');
		const ctx = canvas.getContext('2d')!;
		// 使用与div相同的字体样式
		const computedStyle = window.getComputedStyle(displayRef.current);
		ctx.font = `${computedStyle.fontSize} ${computedStyle.fontFamily}`;
		let closestIndex = 0;
		let closestDistance = Infinity;
		// 遍历每个字符位置
		for (let i = 0; i <= inputValue.length; i++) {
			const textBeforeCursor = inputValue.substring(0, i);
			const textWidth = ctx.measureText(textBeforeCursor).width;
			const distance = Math.abs(clickX - textWidth);
			if (distance < closestDistance) {
				closestDistance = distance;
				closestIndex = i;
			}
		}
		return closestIndex;
	};

	const handleClick = (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!isEditing) {
			// 计算点击位置对应的字符索引
			const clickIndex = getCharacterIndexFromPoint(e);
			setCursorPosition(clickIndex);
			setIsEditing(true);
		}
	};

	const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		const newValue = e.target.value;
		setInputValue(newValue);
		onChange?.(newValue);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			setIsEditing(false);
			onBlur?.(inputValue);
		}
		if (e.key === 'Escape') {
			setIsEditing(false);
			setInputValue(value || '');
		}
	};

	const displayValue = inputValue || placeholder;

	return (
		<div style={ { position: 'relative' } }>
			<div ref={ ref } className={ s.editTextAreaContainer }>
				{isEditing ? (
					<Form.Item name={ name } className={ s.editTextAreaFormItem }>
						<Input.TextArea
							ref={ inputRef }
							value={ inputValue }
							placeholder={ placeholder }
							showCount={ false }
							maxLength={ maxLength }
							rows={ 1 }
							bordered={ false }
							onChange={ handleInputChange }
							onKeyDown={ handleKeyDown }
							autoSize={ { minRows: 1, maxRows: 3 } }
						/>
					</Form.Item>
				) : (
					<div
						ref={ displayRef }
						onClick={ handleClick }
						className={ s.editTextAreaDisplay }
					>
						<div className={ cs(s.editTextAreaContent, { [s.placeholder]: !inputValue }) } >
							{displayValue}
						</div>
					</div>
				)}
			</div>
			{ maxLength && (
				<div className={ s.editTextAreaCount }>
					{inputValue ? inputValue.length : 0} / {maxLength}
				</div>
			)}
		</div>
	);
}