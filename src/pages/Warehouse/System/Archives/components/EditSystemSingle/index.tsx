import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { Button, Col, Form, Input, Row, Alert, Radio, Tooltip } from "antd";
import cs from "classnames";
import { useBoolean, useRequest } from "ahooks";
import { observer } from 'mobx-react';
import _ from "lodash";
import { QuestionCircleOutlined } from "@ant-design/icons";
import s from './index.module.scss';
import { AutoCalculate, PriceStatus, WeightStatus } from "@/utils/enum/productManage";
import SkuTable, { ISupplierInfo } from "./components/SkuTable";
import { SaveSkuItem, SysItem } from "@/types/schemas/warehouse/system";
import { createFactory, getNewSkuId } from "./helper";
import { BrandSelect } from "@/components-biz/Product/Brand";
import { GroupSelect } from "@/components-biz/Product/Group";
import BrandEditorModal from "@/components-biz/Product/Brand/EditorModal";
import { ItemSysItemGenerateOuterIdApi, ItemSysItemSaveApi } from "@/apis/warehouse/system";
import { EditSystemSingleModalProps } from "./modal";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { useStores } from "@/stores/tool";
import groupStore from '@/stores/warehouse/Group';
import warehouseStore from "@/stores/warehouse";
import SupplierDialog from "@/pages/Warehouse/Supplier/components/SupplierDialog";
import userStore from "@/stores/user";
import { autoSearch, weightUnit } from '@/pages/Index/Settings/System/constants';
import message from "@/components/message";
import { ArchivesTypeEnum } from "../..";
import { ItemPropertyEnum, ItemPropertyOptions } from "../../constant";
import ArchivesTooltip from "../ArchivesTooltip";

interface SystemDataProps extends EditSystemSingleModalProps{
	data?: SysItem,
	onCancel?: Function
}

const EditSystemSingle: React.FC<SystemDataProps> = ({
	data,
	code,
	onCancel,
	onSave
}) => {
	const [singleSku, setSingleSku] = useState<boolean>(true);
	const [systemItem, setSystemItem] = useState<SaveSkuItem[]>([]);
	const [supplierInfo, setSupplierInfo] = useState<ISupplierInfo>(null);
	const [form] = Form.useForm();
	const skuTableRef = useRef(null);
	const store: typeof groupStore = useStores('GroupStore');
	const { archivesType } = store; // combined 组合货品档案
	const [dialogVisible, { setTrue, setFalse }] = useBoolean(false);
	const { refreshSupplierList } = warehouseStore;
	const [isKg, setIsKg] = useState(false);
	const { hasCostPricePermission } = userStore;

	useEffect(() => {
		userStore.getUserSetting().then(res => {
			setIsKg(res?.weightUnit == weightUnit.显示kg);
		});
	}, []);

	useEffect(() => {
		const { classifyId, brandId, sysItemAlias, sysSkuList, sysItemId, outerId, property } = data || {};
		const formData = {
			classifyId,
			brandId,
			sysItemAlias,
			sysItemId,
			outerId,
			property: property || ItemPropertyEnum.成品,
		};
		form.setFieldsValue(formData);
		setSingleSku(false);
		setSystemItem(sysSkuList);
	}, [data, form, archivesType]);

	const isNew = useMemo(() => {
		return !data?.sysItemId;
	}, [data]);

	const { loading, runAsync } = useRequest(ItemSysItemSaveApi, {
		manual: true
	});

	const onFinish = () => {
		const skuTableRefArr = skuTableRef?.current?.getFieldsValue();
		if (!skuTableRefArr.length) {
			message.warning("请先添加规格");
			return;
		}
		Promise.all([skuTableRef?.current?.validateFields(), form.validateFields()]).then((res) => {
			const [innerSku, outerSku] = res;
			console.log('onFinish', res);
			// 判断是否为单个sku模式
			innerSku.forEach(i => {
				// i.outerId = outerSku.outerId || '';
				if (userStore?.systemSetting?.itemCustomAttribute === autoSearch.开启) {
					// 自定义属性
					i.customAttributesList = userStore?.systemSetting?.itemCustomAttributeDTOList?.map((item) => {
						const _item = { ...item };
						if (i[item.key]) {
							_item.value = i[item.key];
						} else {
							_item.value = '';
						}
						delete i[item.key];
						return _item;
					});
					i.customAttributes = JSON.stringify(i.customAttributesList);
				}
			});
			const d = createFactory('item')(outerSku, innerSku);
			d.permission_source = code;
			// 确保 property 值被包含在提交数据中
			d.property = outerSku.property || ItemPropertyEnum.成品;
			// 编辑模式需要增加删除的sku
			if (!isNew) {
				const skuIds: any[] = Array.from(d.sysSkuList, (d: SaveSkuItem) => { return d.sysSkuId; });
				let updateSku = data.sysSkuList.filter(d => !skuIds.includes(d.sysSkuId));
				updateSku = updateSku.map(d => { d.enableStatus = 0; return d; });
				d.sysSkuList = d.sysSkuList.concat(updateSku);
			}
			d.sysSkuList.forEach(item => {
				// hack
				item.supplierId = item.supplierId?.id || item.supplierId || outerSku.supplierId;
				if (!isNew && !item.supplierId) item.supplierId = -1;
				// * 成本价保存的时候 需要判断当前是否为子账号以及是否有字段权限
				// * 没有权限时保存过滤掉自动计算以及成本价字段
				if (!hasCostPricePermission) {
					item.groupCombinationList?.forEach(groupItem => {
						delete groupItem.costPrice;
					});
					delete item[AutoCalculate.自动计算成本];
					delete item.costPrice;
				} else {
					item[AutoCalculate.自动计算成本] = item[AutoCalculate.自动计算成本] ? 1 : 0;
				}
				item[AutoCalculate.自动计算重量] = item[AutoCalculate.自动计算重量] ? 1 : 0;
				item.syncStockFlag = item.syncStockFlag == 1 ? 1 : 0;
				delete d?.market;
				delete d?.stall;
				delete d?.supplierName;
			});
			d.sysItemSaveType = archivesType == ArchivesTypeEnum.组合 ? 1 : 0;
			console.log('onFinish d', d);

			runAsync(d).then(() => {
				sendPoint(isNew ? Pointer.商品_普通货品档案_手工创建_弹窗_手工新建弹窗_成功保存 : Pointer.商品_普通货品档案_编辑_弹窗_编辑普通货品弹窗_成功保存);
				message.success(`货品${isNew ? '创建' : '修改'}成功${isNew ? '' : '，商品变更资料会在几分钟内同步至订单中'}`);
				onSave?.();
				onCancel?.();
			});
		}).catch((e) => {
			console.log("请输入必填字段 e::", e);
			message.warn('请输入必填字段');
		});
	};

	// sku个数变化展现不同显示状态
	const skuChange = (skuLength: number) => {
		// 新建状态回填信息
		if (!data && singleSku && skuLength && archivesType != ArchivesTypeEnum.组合) {
			console.log('新建状态回填信息');
			setSystemItem([{ ...form.getFieldsValue(), sysSkuId: getNewSkuId() }]);
		}
		setSingleSku(!skuLength);
	};

	// 组合货品获取主表单的数据
	const handleGetFormData = (key?:any):SaveSkuItem[] => {
		// 把主表单的信息填充到表格表单项中
		return [{ ...form.getFieldsValue(), sysSkuId: getNewSkuId(key) }];
	};

	const generateOuterIdApi = useCallback((formItem: string) => {
		ItemSysItemGenerateOuterIdApi({
			type: formItem === 'skuOuterId' ? 2 : 1
		}).then(d => {
			form.setFieldsValue({
				[formItem]: d
			});
		});
	}, [form]);

	useEffect(() => {
		const reset = {
			skuOuterId: '',
			itemNo: '',
			netWeight: '',
			weight: '',
		};
		Object.values(WeightStatus).forEach((w) => {
			reset[w] = '';
		});
		Object.values(PriceStatus).forEach((p) => {
			reset[p] = '';
		});
		if (!singleSku) {
			form.setFieldsValue(reset);
		}
	}, [singleSku, form]);

	return (
		<div className={ cs(s.createSystem) }>
			<Form
				form={ form }
				name="basic"
				labelCol={ { span: 8 } }
				wrapperCol={ { span: 16 } }
				style={ { position: 'relative', right: '70px' } }
				autoComplete="off"
			>
				<Row style={ { height: 48 } }>
					<Col span={ 10 }>
						<Form.Item
							label="货品简称"
							name="sysItemAlias"
							rules={ [{ required: true, message: '请输入货品简称' }] }
						>
							<Input maxLength={ 128 } placeholder="货品简称" />
						</Form.Item>
					</Col>
					<div style={ { display: 'none' } }>
						<Form.Item name="sysItemId">
							<Input maxLength={ 100 } placeholder="sysItemId" />
						</Form.Item>
					</div>
					<Col span={ 12 }>
						<Form.Item
							shouldUpdate
							label="货品编码"
						>
							<Row>
								<Col span={ 18 }>
									<Form.Item name="outerId">
										<Input maxLength={ 100 } placeholder="货品编码" />
									</Form.Item>
								</Col>
								<Col span={ 4 }>
									<Button type="link" onClick={ () => { generateOuterIdApi('outerId'); } }>自动生成</Button>
								</Col>
							</Row>
						</Form.Item>
					</Col>
				</Row>
				<Row>
					<Col span={ 10 }>
						<Form.Item
							label="货品分类"
							name="classifyId"
						>
							<GroupSelect
								filterOption={ (input, option) => option.children['toLowerCase']().indexOf(input.toLowerCase()) >= 0 }
								showSearch
								getPopupContainer={ (e) => e.parentElement }
								placeholder="请选择"
								onChange={ (classifyId: string) => form.setFieldsValue({ classifyId }) }
								allowClear
							/>
						</Form.Item>
					</Col>
					<Col span={ 12 }>
						<Form.Item label="品牌">
							<Row>
								<Col span={ 18 }>
									<Form.Item
										name="brandId"
										style={ { marginBottom: 0 } }
									>
										<BrandSelect
											showSearch
											getPopupContainer={ (e) => e.parentElement }
											filterOption={ (input, option) => option.children['toLowerCase']().indexOf(input.toLowerCase()) >= 0 }
											onChange={ (brandId: string) => form.setFieldsValue({ brandId }) }
											allowClear
										/>
									</Form.Item>
								</Col>
								<Col span={ 4 }>
									<BrandEditorModal>
										<Button type="link">编辑品牌</Button>
									</BrandEditorModal>
								</Col>
							</Row>
						</Form.Item>
					</Col>
				</Row>
				<Row>
					<Form.Item
						label={ (
							<div>
								货品属性
							</div>
						) }
						name="property"
						style={ { marginBottom: 0, marginLeft: 90 } }
					>
						<Radio.Group style={ { marginTop: 4 } }>
							{ItemPropertyOptions.map(option => (
								<Radio key={ option.value } value={ option.value }>{option.label}</Radio>
							))}
						</Radio.Group>
					</Form.Item>
					<ArchivesTooltip onClose={ () => onCancel?.() } />
				</Row>
			</Form>
			<div className="r-ml-16 r-mr-16 r-mb-16 r-pb-16" style={ { borderBottom: "1px solid #ddd" } }>
				<Alert message="此处修改货品信息，确认后将修改对应所有规格的货品信息" type="error" showIcon />
			</div>
			<div>
				<SkuTable
					hideAction // 新增
					supplierInfo={ supplierInfo }
					sysItemId={ data?.sysItemId }
					systemItem={ systemItem }
					dialogForm={ form }
					onChange={ skuChange }
					setTrue={ setTrue }
					ref={ skuTableRef }
					getFormData={ handleGetFormData }
				/>
			</div>
			<div className={ cs(s.formBtns) }>
				<Button className={ cs('r-mr-8') } onClick={ () => { onCancel(); } }>取消</Button>
				<Button type="primary" onClick={ onFinish } loading={ loading }>保存</Button>
			</div>



			{dialogVisible && (
				<SupplierDialog
					afterCancel={ () => { setFalse(); } }
					beforeOk={ () => { setFalse(); refreshSupplierList(); } }
					triggerNode={ false }
					visible={ dialogVisible }
				/>
			)}
		</div>
	);
};

export default observer(EditSystemSingle);
