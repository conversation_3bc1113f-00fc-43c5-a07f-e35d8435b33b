/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-06-13 09:30:53
 * @Description: 
 */
import React from "react";
import Modal from "antd/lib/modal/Modal";
import { useBoolean } from "ahooks";
import CreateSystem from ".";
import { Base } from "@/types/schemas";

export interface EditSystemSingleModalProps extends Base{
	onSave?: Function
	archivesType?:string // archivesType
}

const EditSystemSingleModal: React.FC<EditSystemSingleModalProps> = ({
	children,
	...reset
}) => {
	const [visible, { setTrue, setFalse }] = useBoolean(false);
	const { archivesType = 'single' } = reset;
	return (
		<>
			<span onClick={ setTrue }>
				{ children }
			</span>
			<Modal
				centered
				visible={ visible }
				title={ `22新建${archivesType == 'combined' ? '组合' : '普通'}商品` }
				bodyStyle={ { overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' } }
				footer={ false }
				getContainer={ document.body }
				onCancel={ setFalse }
				width={ 1200 }
				destroyOnClose
				maskClosable={ false }
			>
				<CreateSystem { ...reset } onCancel={ setFalse } />
			</Modal>
		</>
	);
};

export default EditSystemSingleModal;
