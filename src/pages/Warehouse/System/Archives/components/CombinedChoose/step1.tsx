import React, { useMemo, useRef, useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Input, Tooltip, Checkbox } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/lib/table';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { useUpdateEffect } from 'ahooks';
import cs from 'classnames';
import { indexOf } from 'lodash';
import SearchTable from '@/components/SearchTable';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { SysItemGetSysItemListRequest, SysItemGetSysItemListResponse } from '@/types/schemas/warehouse/system';
import { SysItemGetSysItemListApi } from '@/apis/warehouse/system';
import WaresInfo from '@/components-biz/WaresInfo';
import WarpText from '@/components-biz/WarpText';
import s from './index.module.scss';
import message from "@/components/message";

// 表单元素定义
const FormFieldList: FormItemConfig[] = [
	{
		name: 'sysItemAlias',
		label: "",
		className: 'r-mb-8',
		children: <Input placeholder="货品简称" style={ { width: 130 } } />,
	},
	{
		name: 'outerId',
		label: '',
		className: 'r-mb-8',
		children: <Input placeholder="货品规格编码" style={ { width: 120 } } />
	}, 
	{
		name: 'sysSkuName',
		label: "",
		className: 'r-mb-8',
		children: <Input placeholder="规格名称" style={ { width: 130 } } />,
	}
];



interface ProductListProps {
	onChange?: Function, // 勾选项变化的行数据数组
	handleSave?: (list:any) => void,
	disabled?: {
		key: string,
		value: any[]
	},
	ref:React.Ref<any>,
	loading?:boolean,
	info?:any,
	selectData?:any[],
	selectKeys?:any[],
	type?:string // choose add
}

const ProductList: React.FC<ProductListProps> = forwardRef((props:ProductListProps, ref) => {
	const { onChange, handleSave, info, selectKeys = [], loading = false, type } = props;
	const [form] = useForm();
	const tableRef = useRef<SearchTableRefProps>();
	const [selectedRowKeys, setSelectedRowKeys] = useState([]); // 选中项
	const [dataSource, setDataSource] = useState({}); // object id
	const [dataTable, setDataTable] = useState([]);

	// 表格列定义，与AntdTable使用一致
	const columns: ColumnsType<unknown> = [
		{
			title: '货品简称',
			dataIndex: 'sysItemAlias',
			key: 'sysItemAlias',
			className: 'table-right-border',
			width: 120,
			render: (t: string, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				return {
					children: row.colSpan && (
						<div className="r-flex">
							<span className="r-mr-6">{row.index}</span>
							<WarpText className="r-flex-1"><Tooltip placement="right" title="货品简称">{t}</Tooltip></WarpText>
						</div>
					),
					props: { }
				};
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.colSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			align: 'center',
			width: 40,
			render: (t: string, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				let id = row?.sysSkuList[0]?.sysSkuId;
				return (
					type == 'choose' && info ? (
						info?.sysSkuList[0]?.sysSkuId != id ? (
							<Checkbox
								value={ row?.sysSkuList[0]?.sysSkuId }
								onChange={ (e) => handleSelectChange(e, row) }
								checked={ selectedRowKeys.includes(row?.sysSkuList[0]?.sysSkuId) }
							/>
						) : (
							<span className="r-c-error">本货品</span>
						)
					) : (
						<Checkbox
							value={ row?.sysSkuList[0]?.sysSkuId }
							onChange={ (e) => handleSelectChange(e, row) }
							checked={ selectedRowKeys.includes(row?.sysSkuList[0]?.sysSkuId) }
						/>
					)
				);
			},
		},
		{
			title: () => {
				return (
					<div>
						<span className="r-mr-32">图片</span>
						<span>规格/货品规格编码</span>
					</div>
				);
			},
			width: 220,
			render: (text, row: SysItemGetSysItemListResponse["data"]["list"][0]) => {
				const { picUrl, sysSkuName, skuOuterId } = row.sysSkuList[0];
				return (
					<div onClick={ () => onClickCheckBox(row) }>
						<WaresInfo imgUrl={ picUrl } skuName={ sysSkuName } wareName={ skuOuterId } />
					</div>
				);
			}
		}
	];

	// 点击当前行
	const onClickCheckBox = (row:SysItemGetSysItemListResponse["data"]["list"][0]) => {
		const { sysSkuId } = row.sysSkuList[0];
		let newValue:any[] = [];
		let isChecked = false;
		// 如果是转化组合商品不选着本商品
		if (type == 'choose' && info && (info?.sysSkuList[0]?.sysSkuId == row?.sysSkuList[0]?.sysSkuId)) {
			return;
		}
		if (selectedRowKeys.includes(sysSkuId)) {
			newValue = selectedRowKeys.filter(k => k !== sysSkuId);
		} else {
			newValue = [...selectedRowKeys, sysSkuId];
			isChecked = true;
		}
		setSelectedRowKeys(newValue);
		onChange?.(newValue, row, isChecked);
		// checkSelectRowsNum(isChecked);
	};

	// 点击选项
	const handleSelectChange = (e: CheckboxChangeEvent, row:any) => {
		console.log(e);
		let newValue:any[] = [];
		const isChecked = e.target.checked;
		const value = e.target.value;
		if (isChecked) {
			newValue = [...selectedRowKeys, value];
		} else {
			newValue = selectedRowKeys.filter(k => k !== value);
		}
		setSelectedRowKeys(newValue);
		onChange?.(newValue, row, isChecked);
		// checkSelectRowsNum(isChecked);
	};

	const clearData = () => {
		setSelectedRowKeys([]);
		setDataSource({});
		setDataTable([]);
	};

	const checkSelectRowsNum = (isChecked:boolean) => {
		if (isChecked && selectedRowKeys.length > 32) {
			message.error('最多支持选择32种进行组合', 2);
		}
	};

	useEffect(() => {
		setSelectedRowKeys(selectKeys || []);
	}, [selectKeys]);



	useEffect(() => {
		return function() {
			clearData();
		};
	}, []);

	const scrollToTop = (data:SysItemGetSysItemListRequest) => {
		if (data?.pageNo) {
			try {
				let v = document.querySelector(".skuProductListStep1 .ant-table-body");
				if (v) {
					v.scrollTop = 0;
				}
			} catch (error) {
				console.log(error);
			}
		}
	};

	// 接口查询、查询参数重装
	const fetchSystemList = (data: SysItemGetSysItemListRequest) => {
		scrollToTop(data);
		let [startCreated = '', endCreated = ''] = data.date || [];
		for (let key in data) {
			if (typeof data[key] === 'string') {
				data[key] = data[key].trim();
			}
		}
		const { sysItemAlias, outerId, pageNo, pageSize, barCode, sysSkuName, itemNo } = data;
		const search: SysItemGetSysItemListRequest = {
			outerId,
			barCode,
			sysSkuName,
			itemNo,
			sysItemAlias,
			startCreated,
			endCreated,
			pageNo,
			pageSize,
			needDeepSelect: true,
			isCombination: 0,
		};
		return SysItemGetSysItemListApi(search);
	};

	// 基于sku拆分维度
	const responseAdapter = (data: SysItemGetSysItemListResponse["data"]) => {
		const sysSkuList: any[] = [];
		const dataS = {};
		data.list.forEach((item, i) => {
			item.sysSkuList.forEach((sku, index) => {
				let one = {
					rowId: `${item.sysItemId}_${index}_${item.sysSkuList.length}`,
					groupId: `groupId${i}`,
					...(index === 0 ? { colSpan: item.sysSkuList.length } : {}),
					...item,
					sysSkuList: [sku],
					index: i + 1,
					sysSkuId: sku?.sysSkuId
				};
				sysSkuList.push(one);
				dataS[sku['sysSkuId']] = one;
			});
		});
		setDataSource(dataS);
		setDataTable(sysSkuList);

		return {
			list: sysSkuList,
			total: data.total
		};
	};

	useImperativeHandle(ref, () => ({

	}), []);

	return (
		<div className="skuProductListStep1">
			<SearchTable<SysItemGetSysItemListResponse["data"]["list"][0]>
				pageSizeId="skuProductListStep1Table"
				ref={ tableRef }
				form={ form }
				fetchData={ fetchSystemList }
				responseAdapter={ responseAdapter }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				searchBtnProps={ {
					size: 'middle'
				} }
				rowFormConfig={ {
					formList: FormFieldList,
					colProps: { },
					style: {
						background: '#ffffff',
						paddingTop: '16px',
						margin: 0
					},
					rowProps: {
						style: {
							margin: 0
						}
					}
				} }
				baseTableConfig={ {
					rowKey: 'rowId',
					groupId: 'groupId',
					cachePgination: true,
					noPadding: true,
					columns,
					scroll: {
						y: 400
					},
					pagination: {
						showSizeChanger: true,
						showQuickJumper: false,
					},
					size: "small",
					loading
				} }
			/>
		</div>

	);
});
export default ProductList;
