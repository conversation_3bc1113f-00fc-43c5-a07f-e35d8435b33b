import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useForm } from 'antd/es/form/Form';
import dayjs from 'dayjs';
import { observer } from 'mobx-react';
import SearchTable from '@/components/SearchTable';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { StockStockNumUploadRecordGetPageListApi } from '@/apis/warehouse/syncstocklog';
import { StockStockNumUploadRecordGetPageListRequest, StockStockNumUploadRecordGetPageListResponse } from "@/types/schemas/warehouse/syncstocklog";
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import WarpText from '@/components-biz/WarpText';
import Input from '@/components/Input/InputSearch';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import EnumSelect from '@/components/Select/EnumSelect';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import { getPlatAndShops, getPlatAndShopsWithFilter } from '@/components-biz/ShopListSelect/shopListUtils';
import Tabs from '@/components/Tabs';
import Pointer from '@/utils/pointTrack/constants';
import sendPoint from '@/utils/pointTrack/sendPoint';
import { PLAT_DW, PLAT_YZ } from '@/constants';

enum UploadType {
	同步成功 = 1,
	同步失败 = 0
}
// 表单元素定义
const FormFieldList: FormItemConfig[] = [
	{
		name: 'shopInfo',
		children: <ShopMultiSelect size="small" hidePlatforms={ [PLAT_DW, PLAT_YZ] } />,
	},
	{
		name: 'date',
		initialValue: [dayjs().subtract(0, 'days').startOf('day'), dayjs().endOf('day')],
		children: <KdzsDateRangePicker1 />
	},
	{
		name: 'uploadResultFlag',
		children: <EnumSelect
			placeholder="同步结果"
			enum={ UploadType }
			size="small"
		/>
	},
	{
		name: 'sysItemName',
		children: <Input placeholder="商品名称" />
	},
	{
		name: 'sysItemSku',
		children: <Input placeholder="规格名称/编码" />
	},
	{
		name: 'itemId',
		children: <Input placeholder="商品ID" />
	},
	{
		name: 'skuId',
		children: <Input placeholder="SkuID" />
	}
];

const SyncStockLog: React.FC = props => {
	const [form] = useForm();
	const ref = useRef<SearchTableRefProps>(); // 获取表单对象SupplierDialog
	const [tabKey, setTabKey] = useState("1");

	const columnsTmp = [
		{
			width: 50,
			align: 'center',
			key: 'index',
			render: (text, record: StockStockNumUploadRecordGetPageListResponse["data"]["list"][0], index) => {
				return index + 1;
			}
		},
		{
			key: 'supplierName',
			title: '同步店铺',
			width: 100,
			dataIndex: 'shopName',
			render: (text, row) => {
				return (
					<div style={ { display: 'flex' } }>
						<PlatformIcon platform={ row['platform'] } />
						<WarpText>
							{text}
						</WarpText>
					</div>
				);
			}
		},
		{
			title: '商品',
			width: 150,
			dataIndex: 'platItemTitle',
			render: (text, { platformItemId }) => {
				return (
					<>
						<WarpText>{text}</WarpText>
						<div>ID:{platformItemId}</div>
					</>
				);
			}
		},
		{
			title: '规格',
			width: 100,
			dataIndex: 'platSkuName',
			render(text, { platformSkuId }) {
				return (
					<>
						<div>{text}</div>
						<div>skuID:{platformSkuId}</div>
					</>
				);
			}
		},
		{
			title: '规格编码',
			width: 100,
			dataIndex: 'platSkuOuterId',
		}, {
			title: '同步规则',
			width: 100,
			dataIndex: 'syncRuleTitle',
			hideTabKey: 1
		}, {
			title: '操作内容',
			width: 100,
			dataIndex: 'operatorContent',
			render: (text, row: StockStockNumUploadRecordGetPageListResponse['data']['list'][0]) => {
				return <WarpText>{{ 0: row.errorMsg, 1: text }[row.uploadResultFlag]}</WarpText>;
			}
		},
		{
			title: '同步结果',
			width: 100,
			dataIndex: 'uploadResultFlag',
			render: (text, row) => {
				return <WarpText>{{ 0: '同步失败', 1: '同步成功' }[text]}</WarpText>;
			}
		},
		{
			title: '同步时间',
			width: 150,
			dataIndex: 'created',
		},
		{
			title: '操作人',
			width: 100,
			dataIndex: 'opUserStr',
		}
	];

	const fetchListRequest = async(info: StockStockNumUploadRecordGetPageListRequest) => {
		if (tabKey === "2") {
			sendPoint(Pointer.库存_库存同步日志_自动同步记录_查询);
		}
		if (info.date) {
			info.startTime = info.date[0] ? dayjs(info.date[0]).format('YYYY-MM-DD HH:mm:ss') : null;
			info.stopTime = info.date[1] ? dayjs(info.date[1]).format('YYYY-MM-DD HH:mm:ss') : null;
		}
		const { shopId, platform } = await getPlatAndShopsWithFilter(info.shopInfo, false, [], [PLAT_DW, PLAT_YZ]);
		info.shopId = shopId;
		info.platform = platform;
		info.syncType = tabKey;
		delete info.date;
		console.log('info', info);
		return StockStockNumUploadRecordGetPageListApi({ ...info });
	};
	const columns = useMemo(() => {
		return columnsTmp.filter(i => (!i.hideTabKey || !(Number(tabKey) === i.hideTabKey)));
	}, [tabKey]);
	const tabsChange = (e) => {
		if (e === "2") sendPoint(Pointer.库存_库存同步日志_自动同步记录_展示);
		setTabKey(e);
	};
	useEffect(() => {
		ref?.current?.refresh(() => {
			console.log('操作完成 刷新页面');
		});
	}, [tabKey]);

	useEffect(() => {
		// setAdParams().then((res) => {
		// 	window.Ads.prototype.getAdShow('syncStockLog');
		// });
	}, []);
	return (
		<NormalLayout>
			<div style={ { marginBottom: '8px' } }>
				<Tabs defaultActiveKey={ tabKey } style={ { paddingTop: 12, background: "white" } } onChange={ (e) => { tabsChange(e); } }>
					<Tabs.TabPane tab="手动同步记录" key={ 1 } />
					<Tabs.TabPane tab="自动同步记录" key={ 2 } />
				</Tabs>
			</div>
			<SearchTable<StockStockNumUploadRecordGetPageListResponse["data"]["list"][0]>
				pageSizeId="supplierTable"
				ref={ ref } // 引用
				form={ form } //
				fetchData={ fetchListRequest } // 接口请求
				searchBtnText="查询"
				resetBtnText="重置"
				additionalFormNode={ tabKey === "2" ? (<div className="r-c-error r-ml-8" style={ { width: 300 } }>仅保留最近15天的库存同步历史记录</div>) : null }
				showSearch // 是否显示查询
				rowFormConfig={ { // 表单配置
					formList: FormFieldList,
					defaultParams: { // 查询表单设置初始值
						date: [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')],
					},
					colProps: { // 表单列配置

					},
					formItemProps: {},
					style: {
						padding: '16px',
					},
					rowProps: {
						style: {
							alignItems: 'unset !important',
							rowGap: '8px'
						}
					},
				} }
				baseTableConfig={ { // 表格基础设置
					rowKey: 'id',
					columns, // 列配置
					scroll: {
					},
					pagination: false,
					expandContextStyle: { marginBottom: 0 },
				} }
			/>
			{/* 广告弹窗 */}
			{/* <div className="sys_position_replace" data-type="middlePopup" data-page="syncStockLog" />
			<div className="sys_position_replace rightBottomPopup" data-type="rightBottomPopup" data-page="syncStockLog" /> */}
		</NormalLayout>

	);
};
export default observer(SyncStockLog);
