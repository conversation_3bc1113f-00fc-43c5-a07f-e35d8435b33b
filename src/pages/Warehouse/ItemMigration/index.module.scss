.container {
  margin-top: 6px;
  padding: 24px;
  background-color: #fff;
  min-height: calc(100vh - 64px);
}



.shopCard {
  // height: 134px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s;
  border-radius: 4px;
  overflow: hidden;

}

.shopInfo {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 80px;
}

.shopIcon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
}

.platformIcon {
  font-size: 32px;
  
}

.shopName {
  flex: 1;
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.configButton {
  text-align: center;
  color: #1890ff;
  cursor: pointer;
  padding: 16px 0;
  font-size: 14px;
  position: relative;
  
  &:hover {
    color: #40a9ff;
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 10%;
    width: 80%;
    height: 1px;
    background-color: #f0f0f0;
  }
}
