
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Spin, Empty } from 'antd';
import { observer } from 'mobx-react';
import PageHeader from '@/components/PageHeader';
import Icon from '@/components/Icon';
import { IndexPlatformShopGetPlatformShopsApi } from '@/apis/user';
import userStore from '@/stores/user';
import styles from './index.module.scss';
import { PLAT_ALI, PLAT_FXG, PLAT_ICON_MAP, PLAT_PDD, PLAT_TB, PLAT_TM } from '@/constants';
import ConfigDrawer from '../components/ConfigDrawer';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { itemMigrationplatform } from '../components/ConfigDrawer/interface';


const ItemMigration: React.FC = () => {
	const [loading, setLoading] = useState(false);
	const [shopList, setShopList] = useState<any[]>([]);
	const [configVisible, setConfigVisible] = useState(false);
	const [currentShop, setCurrentShop] = useState<any>(null);
	// 获取店铺列表
	const fetchShopList = async() => {
		try {
			setLoading(true);
			const { userId } = await userStore.getUserInfo();
			const { list = [] } = await IndexPlatformShopGetPlatformShopsApi({
				userId,
				refresh: 0,
			});
      
			// 筛选只保留拼多多、抖音平台的店铺，并按指定顺序排序，同时过滤掉已关闭的店铺
			const platformOrder = itemMigrationplatform;
			const filteredList = list
				.filter(shop => platformOrder.includes(shop.platform) && shop.status === 1)
				.sort((a, b) => {
					// 根据平台顺序排序
					return platformOrder.indexOf(a.platform) - platformOrder.indexOf(b.platform);
				});
      
			setShopList(filteredList);
		} catch (error) {
			console.error('获取店铺列表失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 初始化加载数据
	useEffect(() => {
		fetchShopList();
		sendPoint(Pointer['商品_搬家铺货配置_页面展现']);
	}, []);
	
	// 处理配置点击
	const handleConfigClick = (shop: any) => {
		console.log('配置点击:', shop);
		sendPoint(Pointer['商品_搬家铺货配置_点击商品铺货配置']);
		setCurrentShop(shop);
		setConfigVisible(true);
	};

	// 处理标签切换
	const handleTabChange = (key: string) => {
		// 根据标签ID找到对应的店铺
		const shop = shopList.find(item => item.id.toString() === key);
		if (shop) {
			setCurrentShop(shop);
		}
	};

	// 关闭配置抽屉
	const handleCloseConfig = () => {
		setConfigVisible(false);
	};

	// 确认配置
	const handleConfirmConfig = () => {
		console.log('确认配置:', currentShop);
		// 这里可以添加保存配置的逻辑
		setConfigVisible(false);
	};

	return (
		<div className={ styles.container }>
			<Spin spinning={ loading }>
				{shopList.length > 0 ? (
					<Row gutter={ [16, 16] } className={ styles.shopGrid }>
						{shopList.map(shop => (
							<Col xs={ 24 } sm={ 12 } md={ 8 } lg={ 6 } key={ shop.id || shop.shopId }>
								<Card className={ styles.shopCard } bordered bodyStyle={ { padding: 0 } }>
									<div className={ styles.shopInfo }>
										<div className={ styles.shopIcon }>
											<Icon 
												type={ PLAT_ICON_MAP[shop.platform] || 'shop' } 
												className={ styles.platformIcon }
												size={ 32 } // 明确指定大小为32px
											/>
										</div>
										<div className={ styles.shopName } title={ shop.sellerNick }>
											{shop.sellerNick}
										</div>
									</div>
									<div className={ styles.configButton } onClick={ () => handleConfigClick(shop) }>
										商品铺货配置
									</div>
								</Card>
							</Col>
						))}
					</Row>
				) : (
					<Empty description="暂无店铺数据" />
				)}
			</Spin>

			{/* 铺货配置抽屉 */}
			{
				configVisible && (
					<ConfigDrawer
						visible={ configVisible }
						activeTitle={ currentShop?.sellerId?.toString() }
						titleList={ shopList }
						onClose={ handleCloseConfig }
						onOk={ handleConfirmConfig }
						onTitleChange={ handleTabChange }
						width={ 1200 }
					/>
				)
			}
			
		</div>
	);
};

export default observer(ItemMigration);

