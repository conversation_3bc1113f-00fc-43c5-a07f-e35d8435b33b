import React, { useState, useEffect, useCallback } from 'react';
import { Form, Checkbox, Radio, Input, Select, Button } from 'antd';
import s from './index.module.scss';

interface ImageConfigProps {
  form: any;
  title?: string;
  initialValues?: {
    deleteFirst?: boolean;
    useFirstImage?: boolean;
    imageSource?: string;
    enableOrderSetting?: boolean;
    orderType?: 'random' | 'custom';
    selectedImageIndex?: number;
  };
}

const ImageConfig: React.FC<ImageConfigProps> = ({ form, title = "商品主图" }) => {
	return (
		<Form.Item label={ title } className="imageConfigContainer">
			<div className={ s.imageConfigContainer }>
				<div className={ s.imageFormRow }>
					<Form.Item name="isDeletePicture" valuePropName="checked" noStyle>
						<Checkbox>删除第</Checkbox>
					</Form.Item>
					<Form.Item name="deletePictureNum" noStyle>
						<Input 
							className={ s.formInput }
							placeholder="多张请用英文逗号隔开" 
							style={ { width: 180, margin: '0 8px' } }
							// onChange={ (e) => {
							// 	const value = e.target.value.replace(/，/g, ',');
							// 	form.setFieldValue('deletePictureNum', value);
							// } }
							// onInput={ (e) => {
							// 	const target = e.target as HTMLInputElement;
							// 	const value = target.value.replace(/，/g, ',');
							// 	target.value = value;
							// 	form.setFieldValue('deletePictureNum', value);
							// } }
						/>
					</Form.Item>
					<span>张主图</span>
				</div>

				<div className={ s.imageFormRow }>
					<Form.Item name="replaceMainPicFlag" valuePropName="checked" noStyle>
						<Checkbox>使用</Checkbox>
					</Form.Item>
					<Form.Item name="replaceMainRule" noStyle initialValue={ 1 }>
						<Select 
							className={ s.formSelect }
							style={ { width: 160, margin: '0 8px' } }
							dropdownStyle={ { zIndex: 1100001 } } 
						>
							<Select.Option value={ 1 }>来源第一张SKU图</Select.Option>
							<Select.Option value={ 2 }>来源最低价SKU图</Select.Option>
						</Select>
					</Form.Item>
					<span>替换首张主图</span>
				</div>

				<div className={ s.imageFormRow }>
					<Form.Item name="mainPicOrderFlag" valuePropName="checked" noStyle>
						<Checkbox>主图顺序设置</Checkbox>
					</Form.Item>
				</div>

				<Form.Item>
					<div className={ s.orderSettingBox }>
						<Form.Item name="mainPicOrderRule" noStyle initialValue={ 0 }>
							<Radio.Group>
								<div className={ s.radioRow }>
									<Radio value={ 0 }>随机打乱图片顺序</Radio>
								</div>
								<div className={ s.radioRow }>
									<Radio value={ 1 }>
										将来源图的第
									</Radio>
									
								</div>
							</Radio.Group>
						</Form.Item>
						<Form.Item name="selectedImageIndex" noStyle>
							<Select
								dropdownStyle={ { zIndex: 1100001 } } 
								style={ { width: 80, margin: '0 8px', position: 'relative', right: '40px' } }
							>
								{[1, 2, 3, 4, 5].map(v => {
									return <Select.Option key={ v } value={ v }>{v}</Select.Option>;
								})}
							</Select>	
						</Form.Item>
						<span style={ { position: 'relative', right: '40px' } }>张放到最前面</span>
					</div>
				</Form.Item>
			</div>
		</Form.Item>
	);
};

export default ImageConfig;
