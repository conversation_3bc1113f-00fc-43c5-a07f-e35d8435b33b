import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { Form, Radio, Input, Button, Tooltip, Select, InputNumber } from 'antd';
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import s from './index.module.scss';

interface QuotationProps {
  form: any;
  onChange?: (values: any) => void;
  value?: any;
}
const selectList = [
	{
		value: -1,
		lable: '根据上面的单价设置自动生成'
	},
	{
		value: 0,
		lable: '自定价'
	}
];

const decimalPlacesTypeList = [
	{
		value: 2,
		lable: '保留2位小数'
	}, {
		value: 1,
		lable: '保留1位小数'
	}, {
		value: 0,
		lable: '四舍五入取整'
	}, {
		value: 4,
		lable: '向上取整'
	}, {
		value: 5,
		lable: '向下取整'
	}
];
const Quotation: React.FC<QuotationProps> = ({ form, onChange, value }) => {
	const [tableRowList, setTableRowList] = useState([]);
	// 优化：使用 useMemo 缓存计算结果，使用 useCallback 稳定 onChange 引用
	const quotationData = useMemo(() => {
		if (tableRowList.length === 0) return null;
		
		const data = {};
		tableRowList.forEach((row, index) => {
			if (index === 0) data.quotationPriceOne = row;
			if (index === 1) data.quotationPriceTwo = row;
			if (index === 2) data.quotationPriceThree = row;
		});
		return data;
	}, [tableRowList]);

	const showError = useMemo(() => {
		if (tableRowList.length === 0) return null;
		 // 检查购买数量是否递增
		 for (let i = 1; i < tableRowList.length; i++) {
			if (Number(tableRowList[i].size) <= Number(tableRowList[i - 1].size)) {
				return 1;
			}
		}
		  // 检查如果价格都是自定义且价格不是递减则返回2
		  const allCustomPrice = tableRowList.every(row => row.priceType === 0);
		  if (allCustomPrice) {
			  for (let i = 1; i < tableRowList.length; i++) {
				  if (Number(tableRowList[i].price) >= Number(tableRowList[i - 1].price)) {
					  return 2;
				  }
			  }
		  }
		return;
	}, [tableRowList]);
	useEffect(() => {
		onChange(quotationData);
	}, [quotationData]);

	useEffect(() => {
		console.log(value, 'valuevaluevalue');
		const list = [];
		if (value?.quotationPriceOne) {
			list.push({ id: 1,
				...value.quotationPriceOne,
				priceType: value?.quotationPriceOne.priceType === 0 ? 0 : -1
		 }); 
		} else {
			list.push({ 
				id: 1,
				priceType: -1
		 }); 
		}
		if (value?.quotationPriceTwo) {
			list.push({ id: 2,
				...value.quotationPriceTwo,
				priceType: value?.quotationPriceTwo.priceType === 0 ? 0 : -1 }); 
		}
		if (value?.quotationPriceThree) {
			list.push({ id: 3,
				...value.quotationPriceThree,
				priceType: value?.quotationPriceThree.priceType === 0 ? 0 : -1 }); 
		}
		setTableRowList(list);
	}, [value]);
	const addTableRow = () => {
		const newRow = {
			id: Date.now(),
			size: '',
			priceType: -1,
			percentage: 100,
			markup: "0",
			reduce: "0",
			decimalPlacesType: 2,
			tailNum: ''
		};
		setTableRowList([...tableRowList, newRow]);
	};

	const removeTableRow = (id: number) => {
		setTableRowList(tableRowList.filter(row => row.id !== id));
	};

	return (
		<>
			<Form.Item label="报价方式：" name="quotationType" className={ s.priceFormItem }>
				<Radio.Group className={ s.priceFormControl }>
					<Radio value={ 1 }>
						与来源商品保持一致
						<Tooltip title="选中后，自动生成与来源商品一致的报价方式（除1688外，其余平台目前都为按产品规格报价，最小起订量为1）">
							<QuestionCircleOutlined style={ { marginLeft: 4, color: '#999' } } />
						</Tooltip>
					</Radio>
					<Radio value={ 2 }>按产品数量报价</Radio>
					<Radio value={ 3 }>按产品规格报价</Radio>
				</Radio.Group>
			</Form.Item>

			<Form.Item 
				noStyle
				shouldUpdate={ (prevValues, currentValues) => prevValues.quotationType !== currentValues.quotationType }
			>
				{({ getFieldValue }) => {
					const quotationType = getFieldValue('quotationType');
					return (quotationType === 2) ? (
						<div>
							<div className={ s.quotationTable }>
								<div className={ s.tableHeader }>
									<span className={ s.headerCell }>档位</span>
									<span className={ s.headerCell }>购买数量</span>
									<span className={ s.headerCell }>价格</span>
									<span className={ s.headerCell }>操作</span>
								</div>
								{tableRowList.map((row, index) => (
									<div key={ row.id } className={ s.tableRow }>
										<span className={ s.cell } style={ { lineHeight: '30px' } }>第{index + 1}档</span>
										<span className={ s.cell } style={ { paddingRight: '8px' } }>
											<InputNumber
												min={ 1 }
												max={ 99999 }
												precision={ 0 }
												placeholder="起批量" 
												style={ { width: '68px', bottom: '0px' } }
												value={ row.size }
												onChange={ (e) => {
													const newList = [...tableRowList];
													newList[index].size = Number(e);
													setTableRowList(newList);
												} }
											/>
										</span>
										<span className={ s.cell } style={ { textAlign: 'left' } }>
											{
												index === 0 ? (
													<>
														<Select 
															placeholder="请选择" 
															style={ { width: '230px', marginRight: '8px', height: '32px' } }
															value={ row.priceType }
															onChange={ (value) => {
																const newList = [...tableRowList];
																newList[index].priceType = value;
																setTableRowList(newList);
															} }
														>
															{selectList.map(v => (
																<Select.Option key={ v.value } value={ v.value }>{v.lable}</Select.Option>
															))}
														</Select>
														{
															row.priceType === -1 ?	<span style={ { color: 'rgba(0, 0, 0, 0.45)' } }>（若有多个SKU价格，取最高价）</span>
																: (
																	<span>
																		<InputNumber
																		
																			value={ row.price }
																			onChange={ (value) => {
																				const newList = [...tableRowList];
																				newList[index].price = value;
																				setTableRowList(newList);
																			} }
																			min={ 0 }
																			precision={ 2 }
																			style={ { marginRight: '5px' } }
																		/>元
																	</span>
																)
														}
													</>
												) : (
													<>
														<Select 
															placeholder="请选择" 
															style={ { width: '120px', } }
														
															value={ row.priceType }
															onChange={ (value) => {
																const newList = [...tableRowList];
																newList[index].priceType = value;
																setTableRowList(newList);
															} }
														>
															{selectList.map(v => (
																<Select.Option
																	key={ v.value } 
																	value={ v.value }
																>
																	{v.value === -1 ? `第${index}档价格` : `${v.lable}`}
																</Select.Option>
															))}
														</Select>
														{
															row.priceType === -1 ?	(
																<span>
																	<span style={ { margin: '0 8px' } }>x</span>
																	<InputNumber
																		min={ 0 }
																		value={ row.percentage }
																	
																		style={ { width: '60px' } }
																		onChange={ (value) => {
																			const newList = [...tableRowList];
																			newList[index].percentage = value;
																			setTableRowList(newList);
																		} }
																	/>
																	<span style={ { margin: '0 8px' } }>%</span>
																	<span style={ { marginRight: '8px' } }>+</span>
																	<InputNumber
																		min={ 0 }
																		precision={ 2 }
																		value={ row.markup }
																	
																		style={ { width: '60px' } }
																		onChange={ (value) => {
																			const newList = [...tableRowList];
																			newList[index].markup = value;
																			setTableRowList(newList);
																		} }
																	/>
																	<span style={ { margin: '0 8px' } }>元</span>
																	<span style={ { marginRight: '8px' } }>-</span>
																	<InputNumber
																		min={ 0 }
																		precision={ 2 }
																		value={ row.reduce }
																	
																		style={ { width: '60px' } }
																		onChange={ (value) => {
																			const newList = [...tableRowList];
																			newList[index].reduce = value;
																			setTableRowList(newList);
																		} }
																	/>
																	<span style={ { margin: '0 8px' } }>元，</span>
																	<Select 
																		placeholder="请选择小数处理方式" 
																		style={ { width: '130px' } }
																		value={ row.decimalPlacesType }
																		onChange={ (value) => {
																			const newList = [...tableRowList];
																			newList[index].decimalPlacesType = value;
																			setTableRowList(newList);
																		} }
																	>
																		{decimalPlacesTypeList.map(v => (
																			<Select.Option key={ v.value } value={ v.value }>{v.value === 0 ? `第${index}档价格` : `${v.lable}`}</Select.Option>
																		))}
																	</Select>
																	<span style={ { margin: '0 8px' } }>，尾数处理</span>
																	<InputNumber
																		min={ 0 }
																		max={ 9 }
																		precision={ 0 }
																		value={ row.tailNum }
																		style={ { width: '68px' } }
																		onChange={ (value) => {
																			const newList = [...tableRowList];
																			newList[index].tailNum = value;
																			setTableRowList(newList);
																		} }
																	/>
																</span>
															)
																: (
																	<span>
																		<InputNumber
																		
																			value={ row.price }
																			onChange={ (value) => {
																				const newList = [...tableRowList];
																				newList[index].price = value;
																				setTableRowList(newList);
																			} }
																			min={ 0 }
																			precision={ 2 }
																			style={ { marginLeft: '8px', marginRight: '5px' } }
																		/>元
																	</span>
																)
														}
													</>
												)
											}
										
									
										</span>
										<span className={ s.cell }>
											{index > 0 && (
												<Button 
													type="link" 
												
													onClick={ () => removeTableRow(row.id) }
												>
													删除
												</Button>
											)}
										</span>
									</div>
								))}
							</div>
							{
								showError === 1 ? (
									<div className={ s.showErrorC }>
										<ExclamationCircleOutlined style={ { marginRight: '5px' } } />‘购买数量’必须后者大于前者
									</div>
								) : showError === 2 ? (
									<div className={ s.showErrorC }>
										<ExclamationCircleOutlined style={ { marginRight: '5px' } } />‘价格’必须下面值小于上面值
									</div>
								) : null
							}
							<Button 
								type="link" 
								className={ s.quotationTableBtn }
								onClick={ addTableRow }
								disabled={ tableRowList?.length >= 3 }
							>
								增加档位
							</Button>
							{
								(tableRowList && tableRowList?.length > 1)	
								&& (
									<div style={ { 
										marginLeft: '100px',
										marginBottom: '16px',
										color: '#999',
										fontSize: '12px'
									 } }
									>
										注：后一档的购买数量必须大于前一档的购买数量，后一档的价格必须小于前一档的价格，否则会导致商品复制失败
									</div>
								)
							}
						</div>
					) : (quotationType === 3) ? (
						<div className={ s.quotationTable2 }>
							<div style={ { display: 'flex', alignItems: 'center', marginBottom: '16px' } }>
								<span style={ { marginRight: '8px' } }>最小起订量</span>
								<Form.Item name="minOrderQuantity" noStyle>
									<InputNumber 
										min={ 1 } 
										max={ 99999 }
										precision={ 0 } 
										style={ { width: '160px' } }
										placeholder="请输入"
									/>
								</Form.Item>
								<span style={ { marginLeft: '8px', color: 'rgba(0, 0, 0, 0.45)', fontSize: '12px' } }>
									注：如设置起批量为1，满足条件时系统会默认支持7天无理由
								</span>
							</div>
						</div>
					) : null;
				}}
			</Form.Item>
		</>
	);
};

export default Quotation;
