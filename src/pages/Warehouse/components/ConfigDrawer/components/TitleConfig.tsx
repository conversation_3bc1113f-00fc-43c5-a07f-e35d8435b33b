import React from 'react';
import { Form, Radio, Input, Button } from 'antd';
import s from './index.module.scss';

interface TitleConfigProps {
  form: any;
  onChange?: (values: any) => void;
  title?: string;
}

const TitleConfig: React.FC<TitleConfigProps> = ({ form, onChange, title = "商品标题" }) => {

	return (
		<Form.Item label={ title } className="titleConfigContainer">
			<div className={ s.titleRadioRow }>
				<Form.Item name="titleType" noStyle>
					<Radio.Group defaultValue={ 1 }>
						<Radio checked value={ 1 }>原标题</Radio>
					</Radio.Group>
				</Form.Item>
			</div>
			<div className={ s.titleConfigContainer }>
				<div className={ s.titleFormRow }>
					<div className={ s.titleFormLabel }>删除关键词：</div>
					<div className={ s.titleFormControl }>
						<Form.Item name="titleDel" noStyle>
							<Input 
								className={ s.formInput } 
								placeholder="请输入需删除的关键词，若多个用逗号分隔" 
							/>
						</Form.Item>
					</div>
				</div>
        
				<div className={ s.titleFormRow }>
					<div className={ s.titleFormLabel }>添加前后缀：</div>
					<div className={ s.titleFormControl }>
						<Form.Item name="pre" noStyle>
							<Input 
								className={ s.formInput } 
								placeholder="前缀" 
								style={ { width: 160 } }
							/>
						</Form.Item>
						<span className={ s.btnAddTitle }>+原标题+</span>
						<Form.Item name="end" noStyle>
							<Input 
								className={ s.formInput } 
								placeholder="后缀" 
								style={ { width: 160 } }
							/>
						</Form.Item>
					</div>
				</div>
        
				<div className={ s.titleFormRow }>
					<div className={ s.titleFormLabel }>替换关键词：</div>
					<div className={ s.titleFormControl }>
						<Form.Item name={ ['replaces', 'before'] } noStyle>
							<Input 
								className={ s.formInput } 
								placeholder="标题关键词" 
								style={ { width: 160 } }
							/>
						</Form.Item>
						<span className={ s.replaceArrow }>替换为</span>
						<Form.Item name={ ['replaces', 'after'] } noStyle>
							<Input 
								className={ s.formInput } 
								placeholder="替换的内容" 
								style={ { width: 160 } }
							/>
						</Form.Item>
					</div>
				</div>
			</div>
		</Form.Item>
	);
};

export default TitleConfig;
