import React, { useState, useEffect } from 'react';
import { Form, Radio, Input, Select, Button } from 'antd';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import s from './index.module.scss';
import { getUserShipAddress } from '@/apis/user'; // 导入获取运费模板的API
import { PLAT_ALI, PLAT_FXG, PLAT_PDD, PLAT_TB, PLAT_TM } from '@/constants';

interface AddressProps {
  form: any;
  onChange?: (values: { shippingAddress: string }) => void;
  platform?: string; // 平台参数
  productId?: string; // 商品ID参数
}

const Address: React.FC<AddressProps> = ({ 
	form, 
	onChange,
	platform,
	productId,
}) => {
	const [templateOptions, setTemplateOptions] = useState<Array<{id?: string, name?: string}>>([]);
	const [loading, setLoading] = useState(false);

	// 获取运费模板列表
	const fetchExpressTemplates = async() => {
		setLoading(true);
		try {
			const data = {
				shopId: productId,
				platform,
				pageSize: 100,
				pageNo: 1
			};
			const response = await getUserShipAddress(data);
			if (response.list && Array.isArray(response.list)) {
				const newList = response.list.map(item => {
					return {
						id: item.shippingAddressId,
						name: (item.shippingAddress || '').replace(/\^+/g, ' ')
					};
				});
				console.log(newList, 'newListnewListnewList');
				setTemplateOptions(newList);
			} 
		} catch (error) {
			// 接口失败时设置默认选项
			console.log(error, 'error');
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		// 获取运费模板列表
		fetchExpressTemplates();
	}, [platform, productId]);


	// 处理添加按钮点击事件
	const handleAddClick = () => {
		// 根据平台打开不同的URL
		let url = '';
    
		if (platform) {
			switch (platform.toLowerCase()) {
				case PLAT_ALI:
					url = `https://wuliu.1688.com/foundation/send_goods_address_manage.htm?spm=a28888.publish.cbuSendAddress.1.74de3793Peqhc9`;
					break;
				default:
					console.warn('未知平台:', platform);
					return;
			}
			// 在新标签页中打开URL
			window.open(url, '_blank');
		} else {
			console.log(productId, 'productId');
		}
	};

	return (
		<div className={ s.serviceConfigContainer }>
			<Form.Item label="发货地址：" className={ s.serviceFormItem }>
				<div className={ s.serviceFormControl }>
					<Form.Item name="shippingAddressId" noStyle>
						<Select 
							style={ { width: 406 } } 
							className={ s.serviceSelect }
							loading={ loading }
							dropdownStyle={ { zIndex: 1100 } } 
							onChange={
								(value, e) => {
									form.setFieldsValue({ shippingAddress: e?.children || '' });
								}
							}
							placeholder="请选择"
						>
							{templateOptions?.map(template => (
								<Select.Option key={ template.id } value={ template.id }>
									{(template.name || '').replace(/\^+/g, ' ')}
								</Select.Option>
							))}
						</Select>
					</Form.Item>
					
					<Button 
						type="link" 
						className={ s.serviceAddBtn }
						onClick={ fetchExpressTemplates }
						title="刷新运费模板"
					>
						<ReloadOutlined style={ { marginTop: '2px' } } />
					</Button>
					<Button 
						type="link" 
						className={ s.serviceAddBtn }
						onClick={ handleAddClick }
					>
						<PlusOutlined style={ { marginTop: '2px' } } /> 添加
					</Button>
				</div>
			</Form.Item>
		</div>
	);
};

export default Address;
