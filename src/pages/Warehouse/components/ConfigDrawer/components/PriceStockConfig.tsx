import React from 'react';
import { Form, Radio, Checkbox, Input, Space, InputNumber, message, Tooltip } from 'antd';
import s from './index.module.scss';

interface PriceStockConfigProps {
  form: any;
  title?: string;
  type?: string;
  name?: string; // 表单字段名
  showWK?: boolean;
  onChange?: (values: any) => void;
  required?: boolean;
}

const PriceStockConfig: React.FC<PriceStockConfigProps> = ({ form, title, type, name, showWK, onChange, required }) => {
	// 处理价格计算逻辑
	const getFieldValue = (fieldName: string) => {
		if (!form || !name) return undefined;
		const formValues = form.getFieldValue(name) || {};
		return formValues[fieldName];
	};

	// 处理价格配置变更
	const handlePriceConfigChange = (key: string, value: any) => {
		if (!form || !name) return;
		
		// 获取当前表单值
		const currentValue = form.getFieldValue(name) || {
			priceType: 0,
			percentage: 100,
			markup: '0',
			decimalPlacesType: 2,
			fixedPointPrice: '',
			removeMethod: 0,
			sellingPriceType: 0,
			reduce: 0,
		};
		
		// 更新表单值
		const newValue = {
			...currentValue,
			[key]: value
		};
		
		form.setFieldsValue({
			[name]: newValue
		});
		
		// 调用外部onChange回调
		if (onChange) {
			onChange(newValue);
		}
	};

	// 创建一个自定义验证函数，用于检查三个字段中是否至少有一个有值
	const validateAtLeastOneField = (rule, value, callback) => {
		if (!form) return callback();
		
		const formValues = form.getFieldsValue() || {};
		const percentage = formValues[name ? `${name}Percentage` : 'percentage'];
		const markup = formValues[name ? `${name}Markup` : "markup"];
		const reduce = formValues[name ? `${name}Reduce` : 'reduce'];
		// 检查三个字段中是否至少有一个有值
		if ((percentage !== undefined && percentage !== null && percentage !== '' && percentage != 0) 
			|| (markup !== undefined && markup !== null && markup !== '' && markup != 0) 
			|| (reduce !== undefined && reduce !== null && reduce !== '' && reduce != 0)) {
			return callback();
		}
		
		// 如果三个字段都没有值，返回错误信息
		return callback('');
	};

	return (
		<div className={ s.priceStockContainer }>
			<Form.Item
				required={ required } 
				label={ title == '建议零售价设置' ? <span >建议零售价设置:</span> : title || "售卖价设置：" } 
				className={ title == '建议零售价设置' ? s.priceFormItemLong : s.priceFormItem }
			>
				<div className={ s.priceFormControl }>
				
					<span className={ s.priceLabel }>价格={type || "来源价格"}*</span>
					{
						title == '拼单价设置' ? (
							<Tooltip title="拼多多规定抓取上传商品限制加价幅度(拼单价)不能超过所在平台价格实际销售价格的8%，超过限价幅度的商品，将自动修改为加价8%" placement="top">
								<div>
									<Form.Item
										name={ name ? `${name}Percentage` : "percentage" }
										rules={ required ? [
											{ 
												validator: validateAtLeastOneField 
											}
										] : [] }
										noStyle
									>
										<InputNumber
											min={ 20 }
											max={ 10000 }
											precision={ 0 }
											className={ s.priceInput }
											style={ { width: '68px' } }
										/>
									</Form.Item>
								</div>
							</Tooltip>
						) : (
							<Form.Item
								name={ name ? `${name}Percentage` : "percentage" }
								rules={ required ? [
									{ 
										validator: validateAtLeastOneField 
									}
								] : [] }
								noStyle
							>
								<InputNumber
									min={ 20 }
									max={ 10000 }
									precision={ 0 }
									className={ s.priceInput }
									style={ { width: '68px' } }
								/>
							</Form.Item>
						)
					}
					
					<span className={ s.priceLabel }>% +</span>
					<Form.Item
						name={ name ? `${name}Markup` : "markup" }
						rules={ [] } // 移除必填验证
						noStyle
					>
						<InputNumber
							min={ 0 }
							precision={ 2 }
							className={ s.priceInput } 
							style={ { width: '68px' } } 
						/>
					</Form.Item>
					<span className={ s.priceLabel }>元 -</span>
					<Form.Item
						name={ name ? `${name}Reduce` : "reduce" }
						rules={ [] } // 移除必填验证
						noStyle
					>
						<InputNumber
							min={ 0 }
							precision={ 2 }
							className={ s.priceInput } 
							style={ { width: '68px' } } 
						/>
					</Form.Item>
					<span className={ s.priceLabel }>元</span>
				</div>
			</Form.Item>
      
			<div className={ s.priceDecimalContainer }>
				{/* <div className={ s.priceDecimalTitle }>小数处理：</div> */}
		
				<Form.Item className={ s.priceFormItem } style={ { position: 'relative', right: '30px' } } label="小数处理">
					<div className={ s.priceFormControl } >
						<Form.Item name={ name ? `${name}decimalPlacesType` : 'decimalPlacesType' } noStyle>
							<Radio.Group>
								<Radio value={ 2 }>保留2位小数</Radio>
								<Radio value={ 1 }>保留1位小数</Radio>
								<Radio value={ 0 }>四舍五入取整</Radio>
								<Radio value={ 3 }>向上取整（如¥10.28{' -> '}¥11）</Radio>
								<Radio value={ 4 }>向下取整（如¥10.88{' -> '}¥10）</Radio>
							</Radio.Group>
						</Form.Item>
					</div>
				
				</Form.Item>
        
				{
					showWK && (
						<div style={ { position: 'relative', right: '30px' } }>
							<Form.Item label="尾数处理：" className={ s.priceFormItem }>
								<div className={ s.priceFormControl }>
									<Form.Item name={ name ? `${name}TailNum` : 'tailNum' } noStyle>
										<InputNumber 
											className={ s.priceInput } 
											style={ { width: '68px' } }
											min={ 0 }
											max={ 9 }
											precision={ 0 }
										/>
									</Form.Item>
									<span className={ s.priceHint }>（说明：不填写不处理尾数，填写后按照填写值生成对应尾数）</span>
								</div>
							</Form.Item>
        
							<div className={ s.priceExample } style={ { marginLeft: '100px' } }>
								示例：若一个商品的价格是70元，此值填写9，则价格会变成79元; 若此值不填写，则仍显示70元
							</div>
						</div>
					)
				}
			
			
			</div>
			{
				title === '拼单价设置' && (
					<div className={ s.priceExample } style={ { marginLeft: '100px' } }>
						注：应平台要求，拼单价不可大于货源价格的1.08倍，否则会导致商品复制失败
					</div>
				)
			}
			{
				title === '单买价设置' && (
					<div className={ s.priceExample } style={ { marginLeft: '100px' } }>
						注：应平台要求，单买价必须大于拼单价，且至少大于1元，若不设置单买价，单买价将在拼单价基础上自动+1元
					</div>
				)
			}
			{
				title === '参考价设置' && (
					<div className={ s.priceExample } style={ { marginLeft: '100px' } }>
						注：应平台要求，参考价不能超过单买价的5倍，若不设置参考价，参考价将在单买价基础上自动+1元
					</div>
				)
			}
		</div>
	);
};

export default PriceStockConfig;
