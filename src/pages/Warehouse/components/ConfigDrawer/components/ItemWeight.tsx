import React from 'react';
import { Form, Radio, Input, Button, Tooltip, List, Checkbox, InputNumber } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import s from './index.module.scss';

interface ItemWeightProps {
  form: any;
  onChange?: (values: any) => void;
}

const ItemWeight: React.FC<ItemWeightProps> = ({ form, onChange }) => {
	// 计算体积
	const calculateVolume = () => {
		const length = form.getFieldValue('length') || 0;
		const width = form.getFieldValue('width') || 0;
		const height = form.getFieldValue('height') || 0;
		const volume = (length * width * height).toFixed(3);
		form.setFieldsValue({ volume: parseFloat(volume) });
	};

	return (
		<>
			<Form.Item label="件重尺：">
				<Form.Item valuePropName="checked" name="prioritySourceLogisticsSet" noStyle>
					<Checkbox className={ s.checkboxItem } style={ { marginBottom: '0px' } }>
						<span className={ s.serviceLabel }>优先使用来源商品设置</span>
					</Checkbox>					
				</Form.Item>
			</Form.Item>	
			<div style={ { marginLeft: '100px', marginBottom: '16px' } }>
				<Form.Item name="showLogisticsCategory" >
					<Radio.Group>
						<Radio value={ 1 }>按规格设置</Radio>
						<Radio value={ 2 }>按商品设置</Radio>
					</Radio.Group>
				</Form.Item>
				<Form.Item className={ s.weightFormItem } style={ { marginLeft: '-100px' } }>
					<div className={ s.quotationTable }>
						<div className={ s.tableHeader }>
							<span className={ s.headerCell2 }>长(cm)</span>
							<span className={ s.headerCell2 }>宽(cm)</span>
							<span className={ s.headerCell2 }>高(cm)</span>
							<span className={ s.headerCell2 }>体积(cm³)</span>
							<span className={ s.headerCell2 }>重量(g)</span>
						</div>
						<div className={ s.tableRow }>
							<span className={ s.cell }>
								<Form.Item name="length" noStyle>
									<InputNumber 
										min={ 0 } 
										precision={ 2 } 
										className={ s.dimensionInput }
										onChange={ calculateVolume }
									/>
								</Form.Item>
							</span>
							<span className={ s.cell }>
								<Form.Item name="width" noStyle>
									<InputNumber 
										min={ 0 } 
										precision={ 2 } 
										className={ s.dimensionInput }
										onChange={ calculateVolume }
									/>
								</Form.Item>
							</span>
							<span className={ s.cell }>
								<Form.Item name="height" noStyle>
									<InputNumber 
										min={ 0 } 
										precision={ 2 } 
										className={ s.dimensionInput }
										onChange={ calculateVolume }
									/>
								</Form.Item>
							</span>
							<span className={ s.cell }>
								<Form.Item name="volume" noStyle>
									<InputNumber min={ 0 } precision={ 3 } disabled className={ s.dimensionInput } />
								</Form.Item>
							</span>
							<span className={ s.cell }>
								<Form.Item name="weight" noStyle>
									<InputNumber min={ 0 } precision={ 0 } className={ s.dimensionInput } />
								</Form.Item>
							</span>
						</div>
					</div>
				</Form.Item>
			</div>	
		</>
	);
};

export default ItemWeight;
