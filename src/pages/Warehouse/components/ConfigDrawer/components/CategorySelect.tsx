import React, { useState, useEffect, useMemo } from 'react';
import { Form, Radio, Cascader } from 'antd';
import { observer } from 'mobx-react';
import _ from 'lodash';
import { CategoryTreeItem, CategoryTreeRequest } from '@/types/schemas/user';
import { getCategoryTreeApi } from '@/apis/user';
import { PLAT_TB, PLAT_FXG } from '@/constants';
import userStore from '@/stores/user';
import s from './index.module.scss';
import { CategoryNoAutoSelectList, CategorySelectList } from '../interface';

interface CategorySelectProps {
  form: any;
  className?: string;
  platform?: string;
  sellerId?: string;
  initialformValues?: any;
}

const CategorySelect: React.FC<CategorySelectProps> = ({ 
	form, 
	className, 
	platform, 
	sellerId, 
	initialformValues,
}) => {
	const [categoryOptions, setCategoryOptions] = useState<CategoryTreeItem[]>([]);
	const [loading, setLoading] = useState(false);
	const [searchOptions, setSearchOptions] = useState<any[]>([]);
	const [isSearching, setIsSearching] = useState(false);
	// const { categoryTbTreeList, getCategoryTreeList } = userStore;
	const [isTb, setIsTb] = useState(false);
	useEffect(() => {		
		if (platform && sellerId) {
			if (CategorySelectList.includes(platform)) {
				setIsTb(true);
				// 淘宝平台只加载第一层
				fetchTbCategoryTree();
			} else {
				setIsTb(false);
				// 其他平台一次性加载全部
				fetchAllCategoryTree();
			}
		}
		return () => {
			setCategoryOptions([]);
			setSearchOptions([]);
			setLoading(false);
			setIsSearching(false);
		};
	}, [platform, sellerId]);
	useEffect(() => {
		if (initialformValues?.showDistribItemVos && categoryOptions.length > 0) {
			const { platformCid, platformCidName } = initialformValues.showDistribItemVos;
			if (platformCid && platformCidName) {
				// 将 platformCid 转换为数组格式给 Cascader 使用
				const cascaderValue = platformCid.split(',');
				
				// 如果是淘宝平台，需要加载对应路径的数据
				if (isTb && cascaderValue.length > 1) {
					loadInitialTbPath(cascaderValue);
				}
				
				// 设置表单初始值
				form.setFieldsValue({
					showDistribItemVos: {
						platformCid,
						platformCidName
					},
					distribItemVos: cascaderValue // 设置 Cascader 的值
				});
			}
		}
	}, [initialformValues, form, isTb, categoryOptions.length]);
	// 构建完整的类目树结构
	const buildFullCategoryTree = (data: CategoryTreeItem[]): any[] => {
		return data.map(item => ({
			value: item.categoryId,
			label: item.name,
			isLeaf: item.isLeaf,
			// 确保 children 存在且不为空数组时才设置
			children: item.children && item.children.length > 0 ? buildFullCategoryTree(item.children) : undefined
		}));
	};

	// 一次性获取全部类目数据
	const fetchAllCategoryTree = async() => {
		try {
			setLoading(true);	
			// 获取全部类目数据（不传parentCategory或传特殊标识）
			const param: CategoryTreeRequest = { 
				platform, 
				sellerId,
			};
			const response = await getCategoryTreeApi(param);
			setCategoryOptions(buildFullCategoryTree(response || []));
		} catch (error) {
			console.error('获取类目失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 淘宝平台分层加载第一层
	const fetchTbCategoryTree = async() => {
		try {
			setLoading(true);	
			const param: CategoryTreeRequest = { 
				platform, 
				sellerId,
				parentCategory: '0'
			};
			const response = await getCategoryTreeApi(param);
			setCategoryOptions(buildTbCategoryTree(response || []));
		} catch (error) {
			console.error('获取类目失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 构建淘宝类目树结构（支持动态加载）
	const buildTbCategoryTree = (data: CategoryTreeItem[]): any[] => {
		return data.map(item => ({
			value: item.categoryId,
			label: item.name,
			isLeaf: item.isLeaf,
			// 非叶子节点设置为可加载
			children: item.isLeaf ? undefined : []
		}));
	};

	// 淘宝平台动态加载子类目
	const loadTbData = async(selectedOptions: any[]) => {
		const targetOption = selectedOptions[selectedOptions.length - 1];
		targetOption.loading = true;
		setSearchOptions([]);
		form.setFieldsValue({
			showDistribItemVos: {
				platformCid: '',
				platformCidName: ''
			},
			distribItemVos: ''
		});
		try {
			const param: CategoryTreeRequest = { 
				platform, 
				sellerId,
				parentCategory: targetOption.value
			};
			const response = await getCategoryTreeApi(param);
			const childrenData = buildTbCategoryTree(response || []);
			
			// 同时更新 targetOption 和 categoryOptions
			targetOption.children = childrenData;
			
			// 更新 categoryOptions 状态中对应的节点
			setCategoryOptions(prevOptions => {
				const updateNodeChildren = (nodes: any[]): any[] => {
					return nodes.map(node => {
						if (node.value === targetOption.value) {
							return { ...node, children: childrenData };
						}
						if (node.children) {
							return { ...node, children: updateNodeChildren(node.children) };
						}
						return node;
					});
				};
				console.log(updateNodeChildren(prevOptions), 'updatedOptionsupdatedOptionsupdatedOptionsupdatedOptionsupdatedOptions');
				return updateNodeChildren(prevOptions);
			});
		} catch (error) {
			console.error('获取子类目失败:', error);
		} finally {
			targetOption.loading = false;
		}
	};

	// 构建搜索结果的树形结构
	const buildSearchCategoryTree = (data: CategoryTreeItem[]): any[] => {
		const map = new Map<string, any>();
		const root: any[] = [];
		
		// 先创建所有节点的映射
		for (const item of data) {
			const newItem = {
				value: item.categoryId,
				label: item.name,
				isLeaf: item.isLeaf,
				children: item.isLeaf ? undefined : [],
				level: item.level,
				parentId: item.parentId
			};
			
			map.set(item.categoryId, newItem);
		}
		
		// 构建父子关系
		for (const item of data) {
			const currentNode = map.get(item.categoryId);
			
			if (item.level === 1 || item.parentId === '0') {
				// 一级类目，添加到根节点
				root.push(currentNode);
			} else {
				// 子级类目，添加到父节点的children中
				const parent = map.get(item.parentId);
				if (parent && parent.children) {
					parent.children.push(currentNode);
				}
			}
		}
		
		return root;
	};

	// 淘宝平台搜索类目
	const searchTbCategory = async(inputValue: string) => {
		if (!inputValue.trim()) {
			setSearchOptions([]);
			return;
		}
		form.setFieldsValue({
			showDistribItemVos: {
				platformCid: '',
				platformCidName: ''
			},
			distribItemVos: ''
		});
		try {
			setIsSearching(true);
			const param: CategoryTreeRequest = { 
				platform, 
				sellerId,
			};
		
			param.cName = inputValue.trim();
		
			const response = await getCategoryTreeApi(param);
			
			// 将搜索结果与原有的类目数据合并
			const searchResults = buildSearchCategoryTree(response || []);
			const originalOptions = searchOptions || [];
			const mergedOptions = mergeSearchWithOriginal(originalOptions, searchResults);
			console.log(searchResults, mergedOptions, 'searchResultssearchResultssearchResultssearchResults');
			setSearchOptions(mergedOptions);
		} catch (error) {
			console.error('搜索类目失败:', error);
			setSearchOptions([]);
		} finally {
			setIsSearching(false);
		}
	};

	// 合并搜索结果与原有数据
	const mergeSearchWithOriginal = (originalOptions: any[], searchResults: any[]): any[] => {
		const merged = [...originalOptions];
		
		// 将搜索结果合并到对应的父节点中
		const mergeNode = (nodes: any[], searchNodes: any[]) => {
			searchNodes.forEach(searchNode => {
				const existingNode = nodes.find(node => node.value === searchNode.value);
				if (existingNode) {
					// 如果节点已存在，合并其子节点
					if (searchNode.children && searchNode.children.length > 0) {
						existingNode.children = existingNode.children || [];
						mergeNode(existingNode.children, searchNode.children);
					}
				} else {
					// 如果节点不存在，直接添加
					nodes.push(searchNode);
				}
			});
		};
		
		mergeNode(merged, searchResults);
		return merged;
	};

	// 防抖搜索函数
	const debouncedSearchTbCategory = useMemo(
		() => _.debounce(searchTbCategory, 300),
		[platform, sellerId]
	);

	// 处理搜索输入
	const handleSearch = (inputValue: string) => {
		if (isTb) {
			debouncedSearchTbCategory(inputValue);
		}
	};

	const handleCascaderChange = (value: any[], selectedOptions: any[]) => {
		if (value && value.length > 0) {
			const platformCid = value?.join(',');
			const platformCidName = selectedOptions?.map(option => option.label).join('>');
			
			const distribItemVos = {
				platformCid,
				platformCidName
			};
			console
				.log(distribItemVos, 'distribItemVosdistribItemVos');
			// 设置表单值
			form.setFieldsValue({
				showDistribItemVos: distribItemVos
			});
		} else {
			// 清空时也要更新表单
			form.setFieldsValue({
				showDistribItemVos: {
					platformCid: '',
					platformCidName: ''
				}
			});
			
			// 清空搜索选项并重新获取初始数据
			if (isTb) {
				setSearchOptions([]);
				fetchTbCategoryTree();
			}
		}
	};

	// 加载淘宝平台初始路径数据
	const loadInitialTbPath = async(cascaderValue: string[]) => {
		try {
			setLoading(true);
			let currentOptions = [...categoryOptions];
			
			// 逐级加载路径数据
			for (let i = 0; i < cascaderValue.length - 1; i++) {
				const parentId = cascaderValue[i];
				const param: CategoryTreeRequest = { 
					platform, 
					sellerId,
					parentCategory: parentId
				};
				const response = await getCategoryTreeApi(param);
				const childrenData = buildTbCategoryTree(response || []);
				
				// 找到对应的父节点并设置children
				const updateOptions = (options: any[]): any[] => {
					return options.map(option => {
						if (option.value === parentId) {
							return { ...option, children: childrenData };
						}
						if (option.children) {
							return { ...option, children: updateOptions(option.children) };
						}
						return option;
					});
				};
				
				currentOptions = updateOptions(currentOptions);
			}
			
			setCategoryOptions(currentOptions);
		} catch (error) {
			console.error('加载初始路径失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 处理失焦事件
	const handleBlur = () => {
		if (isTb && searchOptions.length > 0) {
			// 如果有搜索选项但没有选择，清空已选项
			const currentValue = form.getFieldValue('distribItemVos');
			if (currentValue?.length > 0) {
				loadInitialTbPath(currentValue);
			}
		}
	};

	return (
		<div style={ { display: 'flex', alignItems: 'center', gap: '12px' } }>
			<Form.Item label="商品类目" name="matchType" className={ className }>
		
				<Radio.Group>
					{!CategoryNoAutoSelectList.includes(platform) && <Radio value={ 1 }>自动匹配</Radio>}
					<Radio value={ 2 }>铺货时手动选择</Radio>
					<Radio value={ 3 }>统一匹配</Radio>
				</Radio.Group>
			</Form.Item>
			<Form.Item 
				shouldUpdate={ (prevValues, currentValues) => prevValues.matchType !== currentValues.matchType }
				noStyle
			>
				{({ getFieldValue }) => {
					const matchType = getFieldValue('matchType');
					return matchType == 3 ? (
						<Form.Item name="distribItemVos" noStyle >
							<Cascader
								className={ s.distribItemVosItem }
								style={ { width: '360px', marginBottom: '10px' } }
								placeholder="请选择类目"
								options={ isTb && searchOptions.length > 0 ? searchOptions : categoryOptions }
								loading={ loading || isSearching }
								loadData={ isTb ? loadTbData : undefined }
								displayRender={ (label) => label.join(' > ') }
								onChange={ handleCascaderChange }
								onSearch={ handleSearch }
								onBlur={ handleBlur }
								showSearch={ isTb ? {
									filter: (inputValue, path) => {
										if (!inputValue) return true;
										return path.some(option => option.label 
											&& option.label.toString().toLowerCase().includes(inputValue.toLowerCase()));
									},
									matchInputWidth: false,
									limit: 200
								} : {
									filter: (inputValue, path) => {
										if (!inputValue) return true;
										return path.some(option => option.label 
											&& option.label.toString().toLowerCase().includes(inputValue.toLowerCase()));
									},
									matchInputWidth: false,
									limit: 200
								} }
								popupClassName="cascader-high-zindex"
								getPopupContainer={ (triggerNode) => triggerNode.parentElement }
								changeOnSelect={ false }
							/>
						</Form.Item>
					) : null;
				}}
			</Form.Item>
		</div>
		
	);
};

export default observer(CategorySelect);
