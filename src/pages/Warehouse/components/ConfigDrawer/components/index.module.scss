// 商品标题组件样式
.titleConfigContainer {
  width: 900px;
  height: 136px;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 12px 16px;
}
.titleRadioRow {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  
  :global {
    .ant-radio-wrapper {
      font-weight: 500;
      color: #333;
    }
  }
}

.titleFormRow {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  height: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.titleFormLabel {
  width: 84px;
  text-align: right;

  color: rgba(0, 0, 0, 0.85);
  flex-shrink: 0;
}

.titleFormControl {
  flex: 1;
  display: flex;
  align-items: center;
}

.formInput {
  width: 410px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 4px 11px;
}

.btnAddTitle {
  width:90px;
  text-align: center;
}

.replaceArrow {
  width:90px;
  text-align: center;
}


// 图片配置组件样式
.imageConfigContainer {
  width: 900px;
  height: auto;
  border-radius: 4px;
}

.imageFormRow {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  height: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.formSelect {
  height: 32px;
  border-radius: 2px;
}

.orderSettingBox {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 12px;
  .radioRow {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.imageFormItem {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  
  :global {
    .ant-form-item-label {
      width: 80px;
      text-align: right;
      padding-right: 8px;
      line-height: 32px;
      
      > label {
        color: #666;
      }
    }
    
    .ant-form-item-control {
      flex: 1;
    }
    
    .ant-checkbox-wrapper {
      margin-right: 8px;
    }
  }
}

.imageFormControl {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.imageSelect {
  margin: 0 8px;
}

.imageLink {
  padding: 0;
  height: auto;
}

.imageOrderContainer {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  margin-left: 80px;
}

.imageOrderRow {
  display: flex;
  align-items: center;
  margin-top: 16px;
}

.imageOrderNumbers {
  display: flex;
  margin: 0 16px;
}

.imageOrderBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 0;
  font-size: 16px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  color: rgba(0, 0, 0, 0.85);
  border-radius: 2px;
  
  &:hover, &:focus {
    color: rgba(0, 0, 0, 0.85);
    border-color: #d9d9d9;
    background-color: #fff;
  }
}

.imageOrderTip {
  color: #999;
  font-size: 12px;
  margin-left: 16px;
}

.imageFormText {
  color: #666;
}

// 价格库存组件样式
.priceStockContainer {
  background-color: #fff;
  border-radius: 4px;
  padding: 0;
  margin-bottom: 24px;

  :global {
    .ant-form-item-explain{
      height: 0px !important;
      min-height: 0px !important;
    }
  }
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
}

.priceFormItem {
  margin-bottom: 12px !important;
  display: flex;
  align-items: center;
  
  :global {
    .ant-form-item-label {
      width: 100px;
      text-align: right;
      line-height: 22px;
      
    }
    
    .ant-form-item-control {
      flex: 1;
    }
    
    .ant-radio-wrapper {
      margin-right: 16px;
      line-height: 22px;
    }
    
    .ant-checkbox-wrapper {
      line-height: 22px;
    }
  }
}
.priceFormItemLong {
  margin-bottom: 12px !important;
  display: flex;
  align-items: center;
  
  :global {
    .ant-form-item-label {
     position: relative;
     right: 8px;
      text-align: right;
      line-height: 22px;
      
    }
    
    .ant-form-item-control {
      flex: 1;
    }
    
    .ant-radio-wrapper {
      margin-right: 16px;
      line-height: 22px;
    }
    
    .ant-checkbox-wrapper {
      line-height: 22px;
    }
  }
}

.priceFormControl {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  :global{
    .ant-form-item-with-help{
      .ant-form-item-explain{
        height: 0px !important;
        min-height: 0px !important;
       }
    }
  }
  
   
}

.priceLabel {
  margin: 0 4px;

}

.priceInput {
  height: 30px;
  line-height: 30px;

  
  // :global {
  //   .ant-input {
  //     height: 22px;
  //     line-height: 22px;
  //     padding: 0 8px;
  //   }
  // }
}
.titleConfigContainer{
  margin-bottom: 12px;
}
.priceDecimalContainer {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 16px;
  margin-left: 100px;
  width: calc(100% - 100px);
}

.priceDecimalTitle {
  font-weight: 500;
  margin-bottom: 16px;
}

.priceHint {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

.priceExample {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
  line-height: 1.5;
}

// 服务与履约组件样式
.serviceConfigContainer {
  background-color: #fff;
  border-radius: 4px;
  padding: 0;
}
.draggableOrderContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 12px;
  margin: 0 16px;
}

.draggableOrderItem {
  cursor: move;
}

.serviceFormItem {
  margin-bottom: 12px !important;
  display: flex;
  align-items: center;
  
  :global {
    .ant-form-item-label {
      width: 100px;
      text-align: right;
      line-height: 32px;
      
      > label {
        
        &.ant-form-item-required::before {
          color: #ff4d4f;
          content: '*';
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    
    .ant-form-item-control {
      flex: 1;
    }
    
    .ant-radio-wrapper {
      margin-right: 16px;
    }
  }
}

.serviceFormControl {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.serviceLabel {
  margin-right: 8px;

}

.serviceSelect {
  margin-right: 8px;
}

.serviceAddBtn {
  color: #1890ff;
  padding: 0 4px;
  height: 32px;
  line-height: 32px;
  display: flex;
  align-items: center;
}

.serviceInput {
  height: 32px;
}
.cascader-high-zindex {
  z-index: 1100001 !important;
}
.distribItemVosItem{
  vertical-align: middle;
  :global {
      .cascader-high-zindex {
          top: 36px !important;
          bottom: auto !important;
      }
    //   ant-cascader-menus
      .ant-cascader-dropdown {
        height: 260px !important;
          
          .ant-cascader-menu {
            width: 100%;
              height: 260px !important;
          }
      }
  }
}

.dimensionList {
  width: 750px;
  border: 1px solid #f0f0f0;
  
  :global(.ant-list-header) {
    padding: 9px 0px;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    margin: 0;
  }
  
  :global(.ant-list-item) {
    padding: 8px;
    border-bottom: none;
    border-left: 1px solid #f0f0f0;
    margin: 0;
  }
  
  :global(.ant-list-item:last-child) {
    border-bottom: none;
  }
}

.dimensionHeader {
  display: flex;
  
  span {
    flex: 1;
    text-align: left;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    border-right: 1px solid #f0f0f0;
    margin-left: 4px;
    &:last-child {
      border-right: none;
    }
  }
}

.dimensionItem {
  display: flex;
  gap: 8px;
}

.dimensionInput {
  flex: 1;
  height: 32px;
  width: 134px;
  border: 1px solid #d9d9d9;
}

.quotationTable {
  margin-left: 100px;
  border: 1px solid #f0f0f0;
  margin-top: 8px;
  :global {
    .ant-input-number {
      position: relative;
      bottom: 1px;
    }
}
}
.quotationTableBtn{
  margin-left: 85px;
}
.showErrorC{
  margin-left: 100px;
  color: #F40009;
}
.quotationTable2 {
  margin-left: 100px;
  margin-top: 8px;
  margin-bottom: 16px;
  :global {
    .ant-input-number {
      position: relative;
      bottom: 1px;
    }
}
}
.tableHeader {
  display: flex;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.tableRow {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.headerCell,
.cell {
  padding: 9px 0px 9px 8px;
  text-align: left;
  border-right: 1px solid #f0f0f0;
  &:first-child {
    width: 80px;
  }
  
  &:nth-child(2) {
    width: 92px;
  }
  
  &:nth-child(3) {
    width: 795px;
  }
  
  &:last-child {
    width: 80px;
    border-right: none;
  }
}

// .headerCell {
// }
.addButton {
  margin-left: 12px;
  margin-top: 8px;
  color: #1890ff;
  padding: 0;
}

.weightFormItem {
  .quotationTable {
    border: 1px solid #f0f0f0;
    margin-top: 8px;
    width: 750px; // 5列 * 150px = 750px
    
    .tableHeader {
      display: flex;
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;
     
    }

    .tableRow {
      display: flex;
    }
  
    .headerCell2,
    .cell {
      padding: 9px 8px;
      text-align: left;
      border-right: 1px solid #f0f0f0;
      width: 150px;
      flex: 0 0 150px; // 固定宽度，不允许伸缩
      display: flex;
      align-items: center;
      &:last-child {
        border-right: none;
      }
    }
    .headerCell2{
      padding: 3px 8px;
    }
    .dimensionInput {
      width: 100%;
    }
  }
}
