import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Drawer, Button, Space, Form, Spin, Tabs, Radio, Checkbox, Input, Select, message, InputNumber, Cascader, Tooltip, List } from 'antd';
import { CloseOutlined, ReloadOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import s from './index.module.scss';
import Icon from '@/components/Icon';
import { PLAT_TB, PLAT_PDD, PLAT_FXG, PLAT_ALI, PLAT_ICON_MAP, PLAT_KS } from '@/constants';
import TitleConfig from './components/TitleConfig';
import ImageConfig from './components/ImageConfig';
import PriceStockConfig from './components/PriceStockConfig';
import ServiceConfig from './components/ServiceConfig';
import DatePicker from '@/components/DatePicker';
import { ItemDistributeUpdatePlatformPuhuoSettingRequest, sevenDayList } from './interface';
import { getPlatformShopPuhuoSetting, updatePlatformPuhuoSetting } from '@/apis/user';
import { PlatformShopPuhuoSetting, PlatformShopPuhuoSettingData } from '@/types/schemas/user';
import CategorySelect from './components/CategorySelect';
import userStore from '@/stores/user';
import Address from './components/Address';
import Quotation from './components/Quotation';
import ItemWeight from './components/ItemWeight';

const { TabPane } = Tabs;

interface ConfigDrawerProps {
	visible: boolean;
	title?: React.ReactNode;
	onClose: () => void;
	onOk?: () => void;
	width?: number | string;
	loading?: boolean;
	children?: React.ReactNode;
	footer?: React.ReactNode;
	titleList?: any[];
	activeTitle?: string;
	onTitleChange?: (key: string) => void;
	zIndex?: number; // 添加zIndex属性
}

const ConfigDrawer: React.FC<ConfigDrawerProps> = (props) => {
	const { 
		visible, 
		title = '铺货配置', 
		onClose, 
		onOk, 
		width = 1000, 
		loading = false, 
		children,
		footer,
		titleList = [],
		activeTitle,
		onTitleChange,
		zIndex // 接收zIndex属性
	} = props;

	const [activeKey, setActiveKey] = useState<string>('');
	const [contentActiveTab, setContentActiveTab] = useState('basic');
	const [form] = Form.useForm();
	const [settingLoading, setSettingLoading] = useState<boolean>(false); // 加载铺货设置的loading状态
	const [initialformValues, setInitialformValues] = useState<any>({});
	const [quotationList, setQuotationList] = useState<any>([]);
	// 获取当前选中的店铺
	const getCurrentShop = () => {
		if (!activeKey || !titleList.length) return null;
		return titleList.find(item => item.sellerId === activeKey);
	};

	// 根据店铺信息获取平台标识
	const getPlatformByShop = (shop) => {
		if (!shop) return '';
		return shop?.platform;
	};

	// 将接口返回的数据设置到表单中
	const setFormValues = (data: PlatformShopPuhuoSettingData) => {
		if (!data.list[0]) return;
		const { matchType, extraSetting, platformCid, platformCidName, distribItemVos } = data.list[0];
		console.log(data.list[0], 'extraSettingextraSettingextraSetting');
		const showDistribItemVos = {
			platformCid,
			platformCidName,
		};
		const servicePromiseMap = [
			{ key: 'freshRotRefund', value: 1 },
			{ key: 'brokenRefund', value: 2 },
			{ key: 'allergyRefund', value: 3 }
		];
		const newServicePromise = servicePromiseMap
			.filter(item => extraSetting?.servicePromise?.[item.key])
			.map(item => item.value) || [];
		// 构建扁平化的表单值对象
		const formValues = {
			// 基础信息
			matchType,
			platformCid,
			platformCidName,
			showDistribItemVos,
			distribItemVos,
			// 标题设置
			pre: extraSetting?.titleSettings?.pre || '',
			end: extraSetting?.titleSettings?.end || '',
			moreThanDel: extraSetting?.titleSettings?.moreThanDel,
			replaces: extraSetting?.titleSettings?.replaces[0] || {},
			titleDel: extraSetting?.skipSettingsOss?.titleDel || '',
			
			// 图文信息
			filterForbiddenWord: !!extraSetting?.filterForbiddenWord,
			generateWhitePic: !!extraSetting?.generateWhitePic,
			remove1688OutLink: !!extraSetting?.remove1688OutLink,
			imageVideoUpload: !!extraSetting?.imageVideoUpload,
			uploadDuoduoVideo:	!!extraSetting?.uploadDuoduoVideo,
			isDeletePicture: !!extraSetting?.isDeletePicture,
			deletePictureNum: extraSetting?.deletePictureNum,
			replaceMainPicFlag: !!extraSetting?.replaceMainPicFlag,
			replaceMainRule: extraSetting?.replaceMainRule,
			mainPicOrderFlag: !!extraSetting?.mainPicOrderFlag,
			mainPicOrderRule: extraSetting?.mainPicOrderRule == 0 ? 0 : 1,
			selectedImageIndex: extraSetting?.mainPicOrderRule || 1,
			onlineTrade: extraSetting?.onlineTrade,
			quotationType: extraSetting?.quotationType,
			minOrderQuantity: extraSetting?.minOrderQuantity,
			quotationPriceOne: extraSetting?.quotationPriceOne,
			quotationPriceTwo: extraSetting?.quotationPriceTwo,
			quotationPriceThree: extraSetting?.quotationPriceThree,
			// 价格库存 - 售价设置
			priceType: extraSetting?.sellingPriceSetting?.priceType,
			percentage: extraSetting?.sellingPriceSetting?.percentage,
			markup: extraSetting?.sellingPriceSetting?.markup,
			decimalPlacesType: extraSetting?.sellingPriceSetting?.decimalPlacesType,
			fixedPointPrice: extraSetting?.sellingPriceSetting?.fixedPointPrice,
			removeMethod: extraSetting?.sellingPriceSetting?.removeMethod,
			sellingPriceType: extraSetting?.sellingPriceSetting?.sellingPriceType,
			reduce: extraSetting?.sellingPriceSetting?.reduce,
			tailNum: extraSetting?.sellingPriceSetting?.tailNum,
			// 库存设置
			inventoryNumSettings: extraSetting?.inventoryNumSettings,
			inventoryNums: extraSetting?.inventoryNums,
			inventorySettings: extraSetting?.inventorySettings,
			
			// 服务与履约
			freightTemplate: extraSetting?.freightTemplate != '1' ? extraSetting?.freightTemplate : null,
			freightTemplateType: extraSetting?.freightTemplate == '1' ? '1' : '2',
			sevenDay: extraSetting?.sevenDay,
			mobile: extraSetting?.mobile || '',
			shelfSetting: extraSetting?.shelfSetting,
			upshelfTime: extraSetting?.upshelfTime ? dayjs(extraSetting.upshelfTime) : null,
			supplyType: extraSetting?.supplyType,
			shippingAddress: extraSetting?.shippingAddress,
			shippingAddressId: extraSetting?.shippingAddressId,
			prioritySourceLogisticsSet: !!extraSetting?.prioritySourceLogisticsSet,
			length: extraSetting?.logisticsCategorySet?.length,
			width: extraSetting?.logisticsCategorySet?.width,
			height: extraSetting?.logisticsCategorySet?.height,
			weight: extraSetting?.logisticsCategorySet?.weight,
			volume: extraSetting?.logisticsCategorySet?.volume,
			showLogisticsCategory: extraSetting?.showLogisticsCategory,
			twoPiecesDiscount: extraSetting?.twoPiecesDiscount,
			deliveryTime: extraSetting?.deliveryTime,
			deliveryMethod: extraSetting?.deliveryMethod,
			promiseToShip: extraSetting?.promiseToShip,
			tbDeliveryTime: extraSetting?.tbDeliveryTime,
			deliveryTimeType: extraSetting?.deliveryTimeType,

			marketPrice: extraSetting?.marketPrice || {},
			singlePrice: extraSetting?.singlePrice || {},
			groupPrice: extraSetting?.groupPrice || {},

			marketPricePriceType: extraSetting?.marketPrice?.priceType,
			marketPricePercentage: extraSetting?.marketPrice?.percentage,
			marketPriceMarkup: extraSetting?.marketPrice?.markup,
			marketPricedecimalPlacesType: extraSetting?.marketPrice?.decimalPlacesType,
			marketPriceFixedPointPrice: extraSetting?.marketPrice?.fixedPointPrice,
			marketPriceRemoveMethod: extraSetting?.marketPrice?.removeMethod,
			marketPriceSellingPriceType: extraSetting?.marketPrice?.sellingPriceType,
			marketPriceReduce: extraSetting?.marketPrice?.reduce,

			singlePricePriceType: extraSetting?.singlePrice?.priceType,
			singlePricePercentage: extraSetting?.singlePrice?.percentage,
			singlePriceMarkup: extraSetting?.singlePrice?.markup,
			singlePricedecimalPlacesType: extraSetting?.singlePrice?.decimalPlacesType,
			singlePriceFixedPointPrice: extraSetting?.singlePrice?.fixedPointPrice,
			singlePriceRemoveMethod: extraSetting?.singlePrice?.removeMethod,
			singlePriceSellingPriceType: extraSetting?.singlePrice?.sellingPriceType,
			singlePriceReduce: extraSetting?.singlePrice?.reduce,

			groupPricePriceType: extraSetting?.groupPrice?.priceType,
			groupPricePercentage: extraSetting?.groupPrice?.percentage,
			groupPriceMarkup: extraSetting?.groupPrice?.markup,
			groupPricedecimalPlacesType: extraSetting?.groupPrice?.decimalPlacesType,
			groupPriceFixedPointPrice: extraSetting?.groupPrice?.fixedPointPrice,
			groupPriceRemoveMethod: extraSetting?.groupPrice?.removeMethod,
			groupPriceSellingPriceType: extraSetting?.groupPrice?.sellingPriceType,
			groupPriceReduce: extraSetting?.groupPrice?.reduce,
			groupPriceTailNum: extraSetting?.groupPrice?.tailNum,

			servicePromise: newServicePromise,
		};
		form.setFieldsValue(formValues);
		setInitialformValues(formValues);
		// 如果有预售信息，设置预售类型
		// if (extraSetting?.presaleType !== undefined) {
		// 	setPreSaleType(extraSetting.presaleType);
		// }
		
		console.log('设置表单值:', formValues);
	};

	const handleTabChange = (key: string) => {
		// 防止重复设置相同的 key
		if (key === activeKey) return;
		// 清除表单数据和初始值
		form.resetFields();
		setInitialformValues({});
		// 设置内容标签页为默认值
		setContentActiveTab('basic');
		
		// 设置新的 activeKey
		setActiveKey(key);
		
		// 获取该店铺的铺货设置
		fetchPuhuoSetting(key);
		
		if (onTitleChange) {
			onTitleChange(key);
		}
	};


	// 获取铺货设置
	const fetchPuhuoSetting = async(shopId: string) => {
		if (!shopId) return;
		
		setSettingLoading(true);
		try {
			// 根据shopId找到对应的店铺信息
			const currentShop = titleList.find(shop => shop.sellerId === shopId);
			if (!currentShop) {
				console.error('未找到店铺信息:', shopId);
				return;
			}
			
			// 使用当前选中店铺的platform，而不是依赖getCurrentShop()
			const platform = currentShop.platform;
			
			const response = await getPlatformShopPuhuoSetting({
				shopId,
				platform
			});
			
			if (response) {
				// 将接口返回的数据设置到表单中
				setFormValues(response);
			}
		} catch (error) {
			console.error('获取铺货设置失败:', error);
			// message.error('获取铺货设置失败，请重试');
		} finally {
			setSettingLoading(false);
		}
	};
	// 使用 useMemo 稳定 value 对象引用
	const quotationValue = useMemo(() => ({
		quotationPriceOne: initialformValues?.quotationPriceOne,
		quotationPriceTwo: initialformValues?.quotationPriceTwo,
		quotationPriceThree: initialformValues?.quotationPriceThree 
	}), [initialformValues?.quotationPriceOne, initialformValues?.quotationPriceTwo, initialformValues?.quotationPriceThree]);

	const handleQuotationChange = useCallback((e) => {
		setQuotationList(e);
	}, []);
	// 根据平台类型渲染不同的内容
	const dyRenderContentByPlatform = () => {
		const currentShop = getCurrentShop();
		if (!currentShop) return null;
		
		// 抖音平台的标签页配置（作为锚点导航）
		const tabItems = [
			{ key: 'basic', label: '基础信息' },
			{ key: 'image', label: '图文信息' },
			{ key: 'price', label: '价格库存' },
			{ key: 'service', label: '服务与履约' }
		];
		
		// 处理锚点导航点击
		const handleTabClick = (key) => {
			// 找到对应的元素并滚动到视图
			const element = document.getElementById(`section-${key}`);
			if (element) {
				const elementPosition = element.offsetTop;
				const tabsHeight = 56; // 根据实际标签页高度调整
				const offsetPosition = elementPosition - tabsHeight + 10; // 额外留20px间距
				
				// 获取滚动容器（抽屉内容区域）
				const drawerBody = element.closest('.ant-drawer-body');
				if (drawerBody) {
					drawerBody.scrollTo({
						top: offsetPosition,
						behavior: 'smooth'
					});
				}
			}
			setContentActiveTab(key);
		};

		return (
			<div className={ s.platformContent }>
				<div className={ s.platformTabs }>
					<Tabs 
						activeKey={ contentActiveTab } 
						onChange={ handleTabClick }
						className={ s.contentTabs }
					>
						{tabItems.map(tab => (
							<TabPane key={ tab.key } tab={ tab.label } />
						))}
					</Tabs>
				</div>
				
				<div className={ s.platformForm }>
					<Form form={ form } layout="horizontal" labelCol={ { style: { width: '100px' } } } wrapperCol={ { span: 22 } }>
						{/* 基础信息部分 */}
						<div id="section-basic" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>基础信息</h3>
							
							<CategorySelect
								form={ form }
								className={ s.radioItem }
								initialformValues={ initialformValues }
								platform={ getCurrentShop()?.platform }
								sellerId={ getCurrentShop()?.sellerId }
							/>
							
							<TitleConfig 
								form={ form } 
								title="商品标题设置"
							/>
							
							{/* <Form.Item label="导购短标题" name="shortTitle">
								<Radio className={ s.radioItem } value={ 1 } checked>原短标题，没有短标题显示为空</Radio>
							</Form.Item> */}
							
							<Form.Item valuePropName="checked" label="违禁词" name="filterForbiddenWord">
								<Checkbox disabled className={ s.checkboxItem }>过滤抖店禁止的关键词</Checkbox>
							</Form.Item>
						</div>
						
						{/* 图文信息部分 */}
						<div id="section-image" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>图文信息</h3>
							<ImageConfig title="商品主图" form={ form } />

							<Form.Item label="主图视频：" valuePropName="checked" name="imageVideoUpload" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>来源商品主图视频</Checkbox>
							</Form.Item>
							      
							<Form.Item label="白底图：" valuePropName="checked" name="generateWhitePic" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>若来源商品没有白底图，则自动生成白底图</Checkbox>
							</Form.Item>
      
							{/* <Form.Item label="商品详情：" valuePropName="checked" name="remove1688OutLink" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>过滤带有超链接的图片</Checkbox>
							</Form.Item> */}
						</div>
						
						{/* 价格库存部分 */}
						<div id="section-price" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>价格库存</h3>
							    
							<Form.Item label="发货模式：" name="deliveryMethod" className={ s.priceFormItem }>
								<Radio.Group className={ s.priceFormControl }>
									<Radio
										value={ 0 }
									>现货发货模式
									</Radio>
								</Radio.Group>
							</Form.Item>
      
							{/* <Form.Item label=" " colon={ false } name="priceStockInfo.followSourceDelivery" className={ s.priceFormItem }>
								<div className={ s.priceFormControl }>
									<Checkbox>若源商品为拟店商品，优先和来源发货模式及时间一致</Checkbox>
								</div>
							</Form.Item> */}
      
							<Form.Item label="现货发货时间：" name="deliveryTime" className={ s.priceFormItem }>
								<Radio.Group className={ s.priceFormControl }>
									<Radio value={ 1 }>次日发</Radio>
									<Radio value={ 2 }>48小时</Radio>
									<Radio value={ 999 }>当日发</Radio>
								</Radio.Group>
							</Form.Item>
							<PriceStockConfig
								required
								showWK
								form={ form }
							/>

							<Form.Item label="库存设置：" className={ s.priceFormItem }>
								<div
									className={ s.priceFormControl }
									style={
										{ display: 'flex', alignItems: 'center' }
									}
								>
									<Form.Item name="inventoryNumSettings" noStyle>
										<Radio.Group>
											<Radio value={ 1 }>使用商品来源</Radio>
											<Radio value={ 2 }>统一修改库存为</Radio>
										</Radio.Group>
									</Form.Item>
									
									<Form.Item shouldUpdate={ (prevValues, currentValues) => prevValues.extraSetting?.inventoryNumSettings !== currentValues.extraSetting?.inventoryNumSettings }>
										{({ getFieldValue }) => (
											<Form.Item name="inventoryNums" noStyle>
												<InputNumber
													min={ 1 }
													precision={ 0 }
													className={ s.priceInput } 
													style={ { width: '80px' } } 
													disabled={ getFieldValue('inventoryNumSettings') !== 2 }
												/>
											</Form.Item>
										)}
									</Form.Item>
								</div>
							</Form.Item>
      
							<Form.Item label="订单库存计数：" name="inventorySettings" className={ s.priceFormItem }>
								<Radio.Group className={ s.priceFormControl }>
									<Radio value={ 1 }>下单减库存</Radio>
									<Radio value={ 2 }>付款减库存</Radio>
								</Radio.Group>
							</Form.Item>
						</div>
						
						{/* 服务与履约部分 */}
						<div id="section-service" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>服务与履约</h3>
							<ServiceConfig 
								form={ form } 
								onChange={ (values) => {
									console.log('运费模板已更改:', values.freightTemplate);
									// 处理值变化的逻辑
								} }
								platform={ getCurrentShop().platform }
								productId={ getCurrentShop().sellerId }
							/>
							   
							<Form.Item 
								label="7天无理由退货：" 
								name="sevenDay" 
								className={ s.serviceFormItem }
								labelCol={ {
									style: { 	position: 'relative', right: '6px' }
								} }
							>
								<Radio.Group
									className={ s.serviceFormControl }
								>
									{/* <Radio value={ 0 }>不支持</Radio> */}
									<Radio value={ 1 }>支持</Radio>
									<Radio value={ 5 }>支持(使用后不支持)</Radio>
								</Radio.Group>
							</Form.Item>
      
							<Form.Item 
								label="客服电话：" 
								name="mobile"
								className={ s.serviceFormItem }
								required
								rules={ [
									{
										validator: async(_, value) => {
											if (!value) {
												return Promise.reject(new Error('请输入客服电话'));
											}
											if (!(/^1\d{10}$/.test(value) || /^\d{3,4}-\d{7,8}$/.test(value))) {
												return Promise.reject(new Error('请输入正确客服电话'));
											}
											return Promise.resolve();
										}
									}
								] }
							>
								<Input 
									className={ s.serviceInput } 
									style={ { width: '240px' } } 
								/>
							</Form.Item>
							<Form.Item label="商品上架设置：" name="shelfSetting" className={ s.serviceFormItem }>
								<Radio.Group className={ s.serviceFormControl }>
									<Radio value={ 1 }>立即上架</Radio>
									<Radio value={ 2 }>放入仓库</Radio>
									{/* <Radio value={ 3 }>放入草稿箱</Radio> */}
								</Radio.Group>
							</Form.Item>
						</div>
					</Form>
				</div>
			</div>
		);
	};

	const pddRenderContentByPlatform = () => {
		const currentShop = getCurrentShop();
		if (!currentShop) return null;
		
		// 抖音平台的标签页配置（作为锚点导航）
		const tabItems = [
			{ key: 'basic', label: '基础信息' },
			{ key: 'image', label: '规格与库存' },
			{ key: 'service', label: '服务与承诺' }
		];
		
		// 处理锚点导航点击
		const handleTabClick = (key) => {
			// 找到对应的元素并滚动到视图
			const element = document.getElementById(`section-${key}`);
			if (element) {
				const elementPosition = element.offsetTop;
				const tabsHeight = 56; // 根据实际标签页高度调整
				const offsetPosition = elementPosition - tabsHeight + 10; // 额外留20px间距
				
				// 获取滚动容器（抽屉内容区域）
				const drawerBody = element.closest('.ant-drawer-body');
				if (drawerBody) {
					drawerBody.scrollTo({
						top: offsetPosition,
						behavior: 'smooth'
					});
				}
			}
			setContentActiveTab(key);
		};
		
		return (
			<div className={ s.platformContent }>
				<div className={ s.platformTabs }>
					<Tabs 
						activeKey={ contentActiveTab } 
						onChange={ handleTabClick }
						className={ s.contentTabs }
					>
						{tabItems.map(tab => (
							<TabPane key={ tab.key } tab={ tab.label } />
						))}
					</Tabs>
				</div>
				
				<div className={ s.platformForm }>
					<Form form={ form } layout="horizontal" labelCol={ { style: { width: '100px' } } } wrapperCol={ { span: 22 } }>
						{/* 基础信息部分 */}
						<div id="section-basic" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>基础信息</h3>
							
							<CategorySelect
								initialformValues={ initialformValues }
								platform={ getCurrentShop()?.platform }
								sellerId={ getCurrentShop()?.sellerId }
								form={ form }
								className={ s.radioItem }
							/>

							<ImageConfig title="商品主图" form={ form } />
							<TitleConfig 
								form={ form } 
								title="商品标题"
							/>
							
							<Form.Item valuePropName="checked" label="违禁词" name="filterForbiddenWord">
								<Checkbox disabled className={ s.checkboxItem }>过滤拼多多禁止的关键词</Checkbox>
							</Form.Item>
							<Form.Item label="多多视频：" valuePropName="checked" name="uploadDuoduoVideo" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>来源商品主图视频</Checkbox>
							</Form.Item>
							{/* <Form.Item label="商品详情：" valuePropName="checked" name="remove1688OutLink" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>过滤带有超链接的图片</Checkbox>
							</Form.Item> */}
							<Form.Item label="白底图：" valuePropName="checked" name="generateWhitePic" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>若来源商品没有白底图，则自动生成白底图</Checkbox>
							</Form.Item>
						</div>
						
						{/* 价格库存部分 */}
						<div id="section-image" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>规格与库存</h3>

							<PriceStockConfig
								name="groupPrice"
								showWK
								required
								title="拼单价设置"
								type="来源价格"
								form={ form }
							/>
							<PriceStockConfig 
								name="singlePrice"
								title="单买价设置"
								type="拼单价"
								form={ form }
							/>
							<PriceStockConfig 
								name="marketPrice"
								title="参考价设置"
								type="单买价"
								form={ form }
							/>

							<Form.Item 
								label="满件折扣：" 
								name="twoPiecesDiscount" 
								className={ s.priceFormItem }
								required
								rules={ [
									{
										validator: async(_, value) => {
											if (!value) {
												return Promise.reject(new Error('请设置满减折扣'));
											}
											// if (value < 50 || value > 99) { // 5折到9.9折，存储时乘以10
											// 	return Promise.reject(new Error('折扣必须在5折到9.9折之间'));
											// }
											return Promise.resolve();
										}
									}
								] }
							>
								<div className={ s.priceFormControl }>
									<span>满2件</span>
									<InputNumber
										min={ 5 }
										max={ 9.9 }
										precision={ 1 }
										style={ { width: '80px', margin: '0 5px' } } 
										value={ initialformValues?.twoPiecesDiscount ? (initialformValues.twoPiecesDiscount / 10) : 0 }
										onChange={ (value) => {
											// 将输入值乘以10后存储
											setInitialformValues({
												...initialformValues,
												twoPiecesDiscount: value ? value * 10 : 0
											});
											form.setFieldsValue({ twoPiecesDiscount: value ? value * 10 : 0 });
										} }
									/>
									<span>折</span>
									<span style={ { color: 'rgba(0, 0, 0, 0.45)', marginLeft: '10px' } }>
										(可设置5~9.9折)
									</span>
								</div>
							</Form.Item>
							<div style={ { marginLeft: '90px', marginBottom: '8px', color: 'rgba(0, 0, 0, 0.45)', fontSize: '12px' } }>
								注：商品满件折扣与店铺内设置的优惠券、店铺购物车、新客立减等优惠均不叠加
							</div>
							<Form.Item label="库存设置：" className={ s.priceFormItem }>
								<div
									className={ s.priceFormControl }
									style={
										{ display: 'flex', alignItems: 'center' }
									}
								>
									<Form.Item name="inventoryNumSettings" noStyle>
										<Radio.Group>
											<Radio value={ 1 }>使用商品来源</Radio>
											<Radio value={ 2 }>统一修改库存为</Radio>
										</Radio.Group>
									</Form.Item>
									
									<Form.Item shouldUpdate={ (prevValues, currentValues) => prevValues.extraSetting?.inventoryNumSettings !== currentValues.extraSetting?.inventoryNumSettings }>
										{({ getFieldValue }) => (
											<Form.Item name="inventoryNums" noStyle>
												<InputNumber
													min={ 1 }
													precision={ 0 }
													className={ s.priceInput } 
													style={ { width: '80px' } } 
													disabled={ getFieldValue('inventoryNumSettings') !== 2 }
												/>
											</Form.Item>
										)}
									</Form.Item>
								</div>
							</Form.Item>
      
							
						</div>
						
						{/* 服务与履约部分 */}
						<div id="section-service" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>服务与承诺</h3>
							{/* <Form.Item label="是否预售：" className={ s.serviceFormItem }>
								<div className={ s.serviceFormControl }>
									<Radio.Group onChange={ (e) => setPreSaleType(e.target.value) } value={ preSaleType }>
										<Radio value={ 0 }>非预售</Radio>
										<Radio value={ 1 }>定时预售</Radio>
										<Radio value={ 2 }>时段预售</Radio>
									</Radio.Group>
								</div>
							</Form.Item>

							{preSaleType === 1 && (
								<Form.Item label="预售时间：" className={ s.serviceFormItem }>
									<div className={ s.serviceFormControl }>
										<span>支付成功后</span>
										<Select 
											placeholder="请选择天数" 
											style={ { width: '160px', margin: '0 5px' } }
										>
											{[3, 5, 7, 10, 15, 20, 30].map(day => (
												<Select.Option key={ day } value={ day }>{day}</Select.Option>
											))}
										</Select>
										<span>天内发货</span>
									</div>
								</Form.Item>
							)}

							{preSaleType === 2 && (
								<Form.Item label="预售时间：" className={ s.serviceFormItem }>
									<div className={ s.serviceFormControl }>
										<DatePicker
											style={ { width: 200 } }
											placeholder="请选择日期"
											showTime={ { format: 'HH:mm:ss' } }
											format="YYYY-MM-DD HH:mm:ss"
										/>
									</div>
								</Form.Item>
							)}

							{
								preSaleType !== 2 && (
									<Form.Item label="承诺发货时间：" className={ s.serviceFormItem }>
										<div className={ s.serviceFormControl }>
											<Radio>当日发货及揽收</Radio>
											<Radio>24小时发货及揽收
												<span className={ s.serviceFormItemYel }>获额外流量扶持</span>
											</Radio>
											<Radio>48小时发货及揽收</Radio>
										</div>
									</Form.Item>
								)
							} */}
							<Form.Item label="承诺发货时间：" name="promiseToShip" className={ s.serviceFormItem }>
								<Radio.Group className={ s.serviceFormControl }>
									{/* <Radio value={ 24 }>当日发货及揽收</Radio> */}
									<Radio value={ 24 }>24小时发货及揽收
										<span className={ s.serviceFormItemYel }>获额外流量扶持</span>
									</Radio>
									<Radio value={ 48 }>48小时发货及揽收</Radio>
								</Radio.Group>
							</Form.Item>

							<ServiceConfig 
								form={ form } 
								onChange={ (values) => {
									console.log('运费模板已更改:', values.freightTemplate);
								} }
								platform={ getCurrentShop().platform }
								productId={ getCurrentShop().sellerId }
							/>
							<div className={ s.priceExample } style={ { marginLeft: '100px' } }>
								注：平台暂不支持获取“新疆西藏收费默认模板”，如有需要请至后台调整+1元
							</div>
							<Form.Item label="库存计件：" className={ s.serviceFormItem }>
								<Radio value={ 1 } checked>支付成功减库存</Radio>
							</Form.Item>
      
							<Form.Item label="商品上架设置：" name="shelfSetting" className={ s.serviceFormItem }>
								<Radio.Group className={ s.serviceFormControl }>
									<Radio value={ 1 }>立即上架</Radio>
									{/* <Radio value={ 2 }>放入仓库</Radio> */}
									<Radio value={ 3 }>放入草稿箱</Radio>
								</Radio.Group>
							</Form.Item>
						</div>
					</Form>
				</div>
			</div>
		);
	};

	const tbRenderContentByPlatform = () => {
		const currentShop = getCurrentShop();
		if (!currentShop) return null;
		
		// 抖音平台的标签页配置（作为锚点导航）
		const tabItems = [
			{ key: 'basic', label: '基础信息' },
			{ key: 'image', label: '销售信息' },
			{ key: 'price', label: '物流服务' }
			// { key: 'service', label: '图文描述' }
		];
		
		// 处理锚点导航点击
		const handleTabClick = (key) => {
			// 找到对应的元素并滚动到视图
			const element = document.getElementById(`section-${key}`);
			if (element) {
				const elementPosition = element.offsetTop;
				const tabsHeight = 56; // 根据实际标签页高度调整
				const offsetPosition = elementPosition - tabsHeight + 10; // 额外留20px间距
				
				// 获取滚动容器（抽屉内容区域）
				const drawerBody = element.closest('.ant-drawer-body');
				if (drawerBody) {
					drawerBody.scrollTo({
						top: offsetPosition,
						behavior: 'smooth'
					});
				}
			}
			setContentActiveTab(key);
		};
		
		return (
			<div className={ s.platformContent }>
				<div className={ s.platformTabs }>
					<Tabs 
						activeKey={ contentActiveTab } 
						onChange={ handleTabClick }
						className={ s.contentTabs }
					>
						{tabItems.map(tab => (
							<TabPane key={ tab.key } tab={ tab.label } />
						))}
					</Tabs>
				</div>
				
				<div className={ s.platformForm }>
					<Form form={ form } layout="horizontal" labelCol={ { style: { width: '100px' } } } wrapperCol={ { span: 22 } }>
						{/* 基础信息部分 */}
						<div id="section-basic" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>基础信息</h3>
							
							<CategorySelect
								initialformValues={ initialformValues }
								platform={ getCurrentShop()?.platform }
								sellerId={ getCurrentShop()?.sellerId }
								form={ form }
								className={ s.radioItem }
							/>

							<ImageConfig title="商品主图" form={ form } />
							<TitleConfig 
								form={ form } 
								title="商品标题"
							/>
							
							{/* <Form.Item label="导购标题：">
								<div className={ s.checkboxItem }>
									<Radio checked>原短标题，没有短标题显示为空</Radio>
								</div>
							</Form.Item> */}

							<Form.Item valuePropName="checked" label="违禁词" name="filterForbiddenWord">
								<Checkbox disabled className={ s.checkboxItem }>过滤淘宝/天猫禁止的关键词</Checkbox>
							</Form.Item>
							{/* <Form.Item label="商品视频：" valuePropName="checked" name="imageVideoUpload" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>来源商品主图视频</Checkbox>
							</Form.Item> */}
						</div>
						
						{/* 图文信息部分 */}
						<div id="section-image" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>销售信息</h3>
							<PriceStockConfig
								showWK
								required
								form={ form }
							/>
							      
							<Form.Item label="库存设置：" className={ s.priceFormItem }>
								<div
									className={ s.priceFormControl }
									style={
										{ display: 'flex', alignItems: 'center' }
									}
								>
									<Form.Item name="inventoryNumSettings" noStyle>
										<Radio.Group>
											<Radio value={ 1 }>使用商品来源</Radio>
											<Radio value={ 2 }>统一修改库存为</Radio>
										</Radio.Group>
									</Form.Item>
									
									<Form.Item shouldUpdate={ (prevValues, currentValues) => prevValues.extraSetting?.inventoryNumSettings !== currentValues.extraSetting?.inventoryNumSettings }>
										{({ getFieldValue }) => (
											<Form.Item name="inventoryNums" noStyle>
												<InputNumber
													min={ 1 }
													precision={ 0 }
													className={ s.priceInput } 
													style={ { width: '80px' } } 
													disabled={ getFieldValue('inventoryNumSettings') !== 2 }
												/>
											</Form.Item>
										)}
									</Form.Item>
								</div>
							</Form.Item>
      
							<Form.Item label="订单计件：" name="inventorySettings" className={ s.priceFormItem }>
								<Radio.Group className={ s.priceFormControl }>
									<Radio value={ 1 }>拍下减库存</Radio>
									<Radio value={ 2 }>付款减库存</Radio>
								</Radio.Group>
							</Form.Item>
      
							<Form.Item label="上架时间：" name="shelfSetting" className={ s.serviceFormItem }>
								<Radio.Group className={ s.serviceFormControl }>
									<Radio value={ 1 }>立刻上架</Radio>
									<Radio value={ 2 }>放入仓库</Radio>
									{/* <Radio value={ 3 }>放入草稿箱</Radio> */}
								</Radio.Group>
							</Form.Item>
				
						</div>
						
						{/* 价格库存部分 */}
						<div id="section-price" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>物流服务</h3>
							<Form.Item label="发货时间：" name="deliveryTimeType" className={ s.priceFormItem }>
								<Radio.Group className={ s.priceFormControl }>
									<Radio value={ 3 }>24小时内发货</Radio>
									<Radio value={ 0 }>48小时内发货</Radio>
									<Radio value={ 2 }>大于48小时发货</Radio>
								</Radio.Group>
							</Form.Item>

							<Form.Item 
								noStyle
								shouldUpdate={ (prevValues, currentValues) => prevValues.deliveryTimeType !== currentValues.deliveryTimeType }
							>
								{({ getFieldValue }) => {
									const deliveryTimeType = getFieldValue('deliveryTimeType');
									return deliveryTimeType === 2 ? (
										<Form.Item 
											label="" 
											name="tbDeliveryTime" 
											colon={ false } 
											className={ s.priceFormItem }
											rules={ [
												{ required: true, message: '　　　　　　　　　请输入发货时间' },
												{ 
													validator: (_, value) => {
														if (value && (value < 3 || value > 15)) {
															return Promise.reject(new Error('　　　　　　　　　发货时间必须在3-15天之间'));
														}
														return Promise.resolve();
													}
												}
											] }
										>
											<div className={ s.priceFormControl } style={ { marginLeft: '90px' } }>
												<span>付款后</span>
												<InputNumber
													value={ getFieldValue('tbDeliveryTime') }
													min={ 3 }
													max={ 15 }
													precision={ 0 }
													placeholder="推荐三天内"
													style={ { width: '120px', margin: '0 5px' } } 
													onChange={ (value) => {
														form.setFieldsValue({ tbDeliveryTime: value });
													} }
												/>
												<span>天内发货</span>
												<span style={ { color: 'rgba(0, 0, 0, 0.45)', marginLeft: '10px' } }>
													(可设置3~15天)
												</span>
											</div>
										</Form.Item>
									) : null;
								}}
							</Form.Item>
							<ServiceConfig 
								form={ form } 
								onChange={ (values) => {
									console.log('运费模板已更改:', values.freightTemplate);
									// 处理值变化的逻辑
								} }
								platform={ getCurrentShop().platform }
								productId={ getCurrentShop().sellerId }
							/>
						</div>
						
						{/* 服务与履约部分 */}
						{/* <div id="section-service" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>图文描述</h3>
							<Form.Item label="商品详情：" valuePropName="checked" name="remove1688OutLink" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>过滤带有超链接的图片</Checkbox>
							</Form.Item>
						</div> */}
					</Form>
				</div>
			</div>
		);
	};

	const aLiRenderContentByPlatform = () => {
		const currentShop = getCurrentShop();
		if (!currentShop) return null;
		
		// 抖音平台的标签页配置（作为锚点导航）
		const tabItems = [
			{ key: 'basic', label: '主图视频' },
			{ key: 'image', label: '基础信息' },
			{ key: 'price', label: '销售信息' },
			{ key: 'service', label: '服务与承诺' },
			{ key: 'wuliu', label: '物流信息' },
			{ key: 'tuwenms', label: '图文描述' }
		];
		
		// 处理锚点导航点击
		const handleTabClick = (key) => {
			// 找到对应的元素并滚动到视图
			const element = document.getElementById(`section-${key}`);
			if (element) {
				const elementPosition = element.offsetTop;
				const tabsHeight = 56; // 根据实际标签页高度调整
				const offsetPosition = elementPosition - tabsHeight + 10; // 额外留20px间距
				
				// 获取滚动容器（抽屉内容区域）
				const drawerBody = element.closest('.ant-drawer-body');
				if (drawerBody) {
					drawerBody.scrollTo({
						top: offsetPosition,
						behavior: 'smooth'
					});
				}
			}
			setContentActiveTab(key);
		};

		return (
			<div className={ s.platformContent }>
				<div className={ s.platformTabs }>
					<Tabs 
						activeKey={ contentActiveTab } 
						onChange={ handleTabClick }
						className={ s.contentTabs }
					>
						{tabItems.map(tab => (
							<TabPane key={ tab.key } tab={ tab.label } />
						))}
					</Tabs>
				</div>
				
				<div className={ s.platformForm }>
					<Form form={ form } layout="horizontal" labelCol={ { style: { width: '100px' } } } wrapperCol={ { span: 22 } }>
						{/* 基础信息部分 */}
						<div id="section-basic" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>主图视频</h3>
							<ImageConfig title="商品主图" form={ form } />
							
							<Form.Item label="白底图：" valuePropName="checked" name="generateWhitePic" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>若来源商品没有白底图，则自动生成白底图</Checkbox>
							</Form.Item>

							<Form.Item label="主图视频：" valuePropName="checked" name="imageVideoUpload" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>来源商品主图视频</Checkbox>
							</Form.Item>
							      
						</div>
					
						{/* 图文信息部分 */}
						<div id="section-image" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>基础信息</h3>
							<CategorySelect
								form={ form }
								className={ s.radioItem }
								initialformValues={ initialformValues }
								platform={ getCurrentShop()?.platform }
								sellerId={ getCurrentShop()?.sellerId }
							/>

							<TitleConfig 
								form={ form } 
								title="商品标题设置"
							/>
							
							{/* <Form.Item label="导购短标题" name="shortTitle">
								<Radio className={ s.radioItem } value={ 1 } checked>原短标题，没有短标题显示为空</Radio>
							</Form.Item> */}
							
							<Form.Item valuePropName="checked" label="违禁词" name="filterForbiddenWord">
								<Checkbox disabled className={ s.checkboxItem }>过滤1688禁止的关键词</Checkbox>
							</Form.Item>
      
							{/* <Form.Item label="商品详情：" valuePropName="checked" name="remove1688OutLink" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>过滤带有超链接的图片</Checkbox>
							</Form.Item> */}
						</div>
						
						{/* 价格库存部分 */}
						<div
							id="section-price"
							className={ s.formSection }
							
						>
							<div >
								<h3 className={ s.sectionTitle }>销售信息</h3>
								<Form.Item label="网上订购：" name="onlineTrade" className={ s.priceFormItem }>
									<Radio.Group className={ s.priceFormControl }>
										<Radio value={ 1 }>支持</Radio>
										<Radio value={ 0 }>不支持</Radio>
									</Radio.Group>
								</Form.Item>

								<PriceStockConfig
									showWK
									required
									title="单价设置" 
									type="来源价格"
									form={ form }
								/>

								{/* <PriceStockConfig
									name="jylsjsz"
									title="建议零售价设置"
									type="单买价"
									form={ form }
								/> */}

								<Quotation
									form={ form }
									value={ quotationValue }
									onChange={ handleQuotationChange }
								/>

								<Form.Item label="库存设置：" className={ s.priceFormItem }>
									<div
										className={ s.priceFormControl }
										style={
											{ display: 'flex', alignItems: 'center' }
										}
									>
										<Form.Item name="inventoryNumSettings" noStyle>
											<Radio.Group>
												<Radio value={ 1 }>使用商品来源</Radio>
												<Radio value={ 2 }>统一修改库存为</Radio>
											</Radio.Group>
										</Form.Item>
									
										<Form.Item shouldUpdate={ (prevValues, currentValues) => prevValues.extraSetting?.inventoryNumSettings !== currentValues.extraSetting?.inventoryNumSettings }>
											{({ getFieldValue }) => (
												<Form.Item name="inventoryNums" noStyle>
													<InputNumber
														min={ 1 }
														precision={ 0 }
														className={ s.priceInput } 
														style={ { width: '80px' } } 
														disabled={ getFieldValue('inventoryNumSettings') !== 2 }
													/>
												</Form.Item>
											)}
										</Form.Item>
									</div>
								</Form.Item>

								<Form.Item label="库存扣减设置：" name="inventorySettings" className={ s.priceFormItem }>
									<Radio.Group className={ s.priceFormControl }>
										<Radio value={ 1 }>下单时扣减</Radio>
										<Radio value={ 2 }>支付时扣减</Radio>
									</Radio.Group>
								</Form.Item>
							
								<Form.Item label="上架时间：" name="shelfSetting" className={ s.serviceFormItem }>
									<Radio.Group className={ s.serviceFormControl }>
										<Radio value={ 1 }>立刻上架</Radio>
										<Radio value={ 4 }>定时开售
											<Form.Item 
												noStyle
												name="upshelfTime"
											>
												<DatePicker
													showTime
													format="YYYY-MM-DD HH:mm:ss"
													placeholder="请选择定时开售时间"
													style={ { width: '200px', marginLeft: '10px' } }
												/>
											</Form.Item>
										</Radio>
									</Radio.Group>
								</Form.Item>

							</div>
						</div>
						
						{/* 服务与履约部分 */}
						<div id="section-service" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>服务与承诺</h3>
							<Form.Item label="供货方式" name="supplyType">
								<Checkbox.Group>
									<Checkbox disabled className={ s.checkboxItem } value={ 1 }>现货
										<span style={ { marginLeft: '5px', color: '#FD8204' } }>库存充足商品（现货商品享更多流量）</span>
									</Checkbox>
								</Checkbox.Group>
							</Form.Item>
							<Form.Item label="发货时间：" name="deliveryTime" className={ s.priceFormItem }>
								<Radio.Group className={ s.priceFormControl }>
									<Radio value={ 1 }>24小时内发货</Radio>
									<Radio value={ 2 }>48小时内发货</Radio>
								</Radio.Group>
							</Form.Item>
							<Form.Item label="退货服务：" name="sevenDay" className={ s.priceFormItem }>
								<Radio.Group className={ s.priceFormControl }>
									<Radio value={ 1 }>7天无理由退货</Radio>
									<Radio value={ 0 }>不支持</Radio>
								</Radio.Group>
							</Form.Item>
						</div>

						<div id="section-wuliu" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>物流信息</h3>
							<Address 
								form={ form } 
								onChange={ (values) => {
									console.log('运费模板已更改:', values.shippingAddress);
									// 处理值变化的逻辑
								} }
								platform={ getCurrentShop().platform }
								productId={ getCurrentShop().sellerId }
							/>
							<ServiceConfig 
								form={ form } 
								onChange={ (values) => {
									console.log('运费模板已更改:', values.freightTemplate);
									// 处理值变化的逻辑
								} }
								isAli
								platform={ getCurrentShop().platform }
								productId={ getCurrentShop().sellerId }
							/>
						
							<ItemWeight form={ form } />
							
						</div>

						<div id="section-tuwenms" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>图文描述</h3>
							<Form.Item label="商品详情：" valuePropName="checked" name="remove1688OutLink" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>过滤带有超链接的图片</Checkbox>
							</Form.Item>
						</div>
					</Form>
				</div>
			</div>
		);
	};

	const ksRenderContentByPlatform = () => {
		const currentShop = getCurrentShop();
		if (!currentShop) return null;
		
		// 抖音平台的标签页配置（作为锚点导航）
		const tabItems = [
			{ key: 'basic', label: '基础信息' },
			{ key: 'image', label: '商品图文' },
			{ key: 'price', label: '价格库存' },
			{ key: 'service', label: '服务与售后' }
		];
		
		// 处理锚点导航点击
		const handleTabClick = (key) => {
			// 找到对应的元素并滚动到视图
			const element = document.getElementById(`section-${key}`);
			if (element) {
				const elementPosition = element.offsetTop;
				const tabsHeight = 56; // 根据实际标签页高度调整
				const offsetPosition = elementPosition - tabsHeight + 10; // 额外留20px间距
				
				// 获取滚动容器（抽屉内容区域）
				const drawerBody = element.closest('.ant-drawer-body');
				if (drawerBody) {
					drawerBody.scrollTo({
						top: offsetPosition,
						behavior: 'smooth'
					});
				}
			}
			setContentActiveTab(key);
		};

		return (
			<div className={ s.platformContent }>
				<div className={ s.platformTabs }>
					<Tabs 
						activeKey={ contentActiveTab } 
						onChange={ handleTabClick }
						className={ s.contentTabs }
					>
						{tabItems.map(tab => (
							<TabPane key={ tab.key } tab={ tab.label } />
						))}
					</Tabs>
				</div>
				
				<div className={ s.platformForm }>
					<Form form={ form } layout="horizontal" labelCol={ { style: { width: '100px' } } } wrapperCol={ { span: 22 } }>
						{/* 基础信息部分 */}
						<div id="section-basic" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>基础信息</h3>
							
							<CategorySelect
								form={ form }
								className={ s.radioItem }
								initialformValues={ initialformValues }
								platform={ getCurrentShop()?.platform }
								sellerId={ getCurrentShop()?.sellerId }
							/>
							
							<TitleConfig 
								form={ form } 
								title="商品标题设置"
							/>
							
							{/* <Form.Item label="导购短标题" name="shortTitle">
								<Radio className={ s.radioItem } value={ 1 } checked>原短标题，没有短标题显示为空</Radio>
							</Form.Item> */}
							
							<Form.Item valuePropName="checked" label="违禁词" name="filterForbiddenWord">
								<Checkbox disabled className={ s.checkboxItem }>过滤快手禁止的关键词</Checkbox>
							</Form.Item>
						</div>
						
						{/* 图文信息部分 */}
						<div id="section-image" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>商品图文</h3>
							<ImageConfig title="商品主图" form={ form } />

							<Form.Item label="白底图：" valuePropName="checked" name="generateWhitePic" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>若来源商品没有白底图，则自动生成白底图</Checkbox>
							</Form.Item>

							<Form.Item label="关联视频：" valuePropName="checked" name="imageVideoUpload" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>来源商品主图视频</Checkbox>
							</Form.Item>
      
							{/* <Form.Item label="商品详情：" valuePropName="checked" name="remove1688OutLink" className={ s.imageFormItem }>
								<Checkbox className={ s.imageFormControl }>过滤带有超链接的图片</Checkbox>
							</Form.Item> */}
						</div>
						
						{/* 价格库存部分 */}
						<div id="section-price" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>价格库存</h3>
							    
							<Form.Item label="发货时效模式：" name="deliveryMethod" >
								<Radio.Group>
									<Radio
										value={ 0 }
									>全部现货
									</Radio>
								</Radio.Group>
							</Form.Item>
							<div className={ s.priceDecimalContainer } style={ { marginLeft: '100px' } }>
								<span style={ { marginLeft: '10px' } }>现货承诺发货时间：</span>
								<Form.Item name="promiseToShip" noStyle>
									<Radio.Group style={ { marginLeft: '10px' } }>
										<Radio value={ 24 }>24小时<span className={ s.serviceFormItemYel }>额外流量扶持</span></Radio>
										<Radio value={ 48 }>48小时</Radio>
									</Radio.Group>
								</Form.Item>
							</div>
							<PriceStockConfig
								required
								showWK
								title="单价设置"
								form={ form }
							/>

							<Form.Item label="库存设置：" className={ s.priceFormItem }>
								<div
									className={ s.priceFormControl }
									style={
										{ display: 'flex', alignItems: 'center' }
									}
								>
									<Form.Item name="inventoryNumSettings" noStyle>
										<Radio.Group>
											<Radio value={ 1 }>使用商品来源</Radio>
											<Radio value={ 2 }>统一修改库存为</Radio>
										</Radio.Group>
									</Form.Item>
									
									<Form.Item shouldUpdate={ (prevValues, currentValues) => prevValues.extraSetting?.inventoryNumSettings !== currentValues.extraSetting?.inventoryNumSettings }>
										{({ getFieldValue }) => (
											<Form.Item name="inventoryNums" noStyle>
												<InputNumber
													min={ 1 }
													precision={ 0 }
													className={ s.priceInput } 
													style={ { width: '80px' } } 
													disabled={ getFieldValue('inventoryNumSettings') !== 2 }
												/>
											</Form.Item>
										)}
									</Form.Item>
								</div>
							</Form.Item>
						</div>
						
						{/* 服务与履约部分 */}
						<div id="section-service" className={ s.formSection }>
							<h3 className={ s.sectionTitle }>服务与售后</h3>
							<ServiceConfig 
								form={ form } 
								onChange={ (values) => {
									console.log('运费模板已更改:', values.freightTemplate);
									// 处理值变化的逻辑
								} }
								platform={ getCurrentShop().platform }
								productId={ getCurrentShop().sellerId }
							/>
      
							<Form.Item label="上架设置：" name="shelfSetting" className={ s.serviceFormItem }>
								<Radio.Group className={ s.serviceFormControl }>
									<Radio value={ 1 }>立即上架</Radio>
									<Radio value={ 2 }>不立即上架</Radio>
								</Radio.Group>
							</Form.Item>
							<Form.Item label="退货规则：" className={ s.serviceFormItem }>
								<Form.Item name="sevenDay" noStyle>
									<Select
										placeholder="请选择退货规则"
										style={ { width: 275 } }
									>
										{
											sevenDayList.map(v => {
												return (
													<Select.Option key={ v.value } value={ v.value }>
														{v.label}
													</Select.Option>
												);
											})
										}
									</Select>
								</Form.Item>
								<span style={ { color: '#999', fontSize: '12px', marginLeft: '8px' } }>
									注：如果所在类目不支持该退货规则，铺货时将使用类目支持的第一个退货规则
								</span>
							</Form.Item>
							<Form.Item label="服务承诺" name="servicePromise">
								<Checkbox.Group>
									<Checkbox className={ s.checkboxItem } value={ 1 }>坏了包退
									</Checkbox>
									<Checkbox className={ s.checkboxItem } value={ 2 }>破损包退
									</Checkbox>
									<Checkbox className={ s.checkboxItem } value={ 3 }>过敏包退
									</Checkbox>
								</Checkbox.Group>
							</Form.Item>
						</div>
					</Form>
				</div>
			</div>
		);
	};

	const renderTitle = () => {
		if (titleList && titleList.length > 0) {
			console.log(titleList, activeKey, 'titleListtitleList');
			return (
				<div className={ s.tabsContainer }>
					<Tabs 
						activeKey={ activeKey } 
						onChange={ handleTabChange }
						className={ s.titleTabs }
					>
						{titleList.map(item => (
							<TabPane 
								key={ item.sellerId }
								tab={ (
									<div className="r-flex r-ai-c">
										<Icon 
											type={ PLAT_ICON_MAP[item.platform] || 'shop' } 
											size={ 24 }
											className="r-mr-8"
										/>
										<span style={ { fontWeight: 500 } }>{ item.sellerNick || item.sellerAbbreviation }</span>
									</div>
								) }
							/>
						))}
					</Tabs>
					<div onClick={ onClose } className={ s.closeIcon }>
						<CloseOutlined style={ { fontSize: '20px' } } />
					</div>
				</div>
			);
		}

		return (
			<div className="r-flex r-ai-c r-jc-sb" style={ { height: '56px' } }>
				<div className={ s.drawerTitle }>
					<span className="r-bold">{title}</span>
				</div>
				<div onClick={ onClose } className={ s.closeIcon }>
					<CloseOutlined style={ { fontSize: '20px' } } />
				</div>
			</div>
		);
	};

	// 构建平台铺货设置参数
	const buildDyPuhuoParams = () => {
		const formValues = form.getFieldsValue(true); 
		console.log('表单值:', formValues);
		
		const currentShop = getCurrentShop();
		const freshRotRefund = formValues?.servicePromise?.includes(1) ? 1 : 0;
		const brokenRefund = formValues?.servicePromise?.includes(2) ? 1 : 0;
		const allergyRefund = formValues?.servicePromise?.includes(3) ? 1 : 0;

		// 将扁平化的表单值转换为嵌套结构
		const params: ItemDistributeUpdatePlatformPuhuoSettingRequest = {
			shopList: {
				shopId: currentShop?.sellerId,
				platform: currentShop?.platform,
				// 运费模板id
				freightTemplate: formValues.freightTemplateType == '1' ? '1' : formValues.freightTemplate,
			},
			platformCid: formValues?.showDistribItemVos?.platformCid,
			platformCidName: formValues?.showDistribItemVos?.platformCidName,
			shopName: currentShop?.sellerNick || currentShop?.sellerAbbreviation || '',
			extraSetting: {
				// 基础设置
				shelfSetting: formValues.shelfSetting,
				upshelfTime: formValues.upshelfTime ? dayjs(formValues.upshelfTime).format('YYYY-MM-DD HH:mm:ss') : null,
				supplyType: formValues.supplyType,
				sevenDay: formValues.sevenDay,
				shippingAddress: formValues.shippingAddress,
				shippingAddressId: formValues.shippingAddressId,
				prioritySourceLogisticsSet: formValues.prioritySourceLogisticsSet ? 1 : 0,
				showLogisticsCategory: formValues.showLogisticsCategory,
				logisticsCategorySet: {
					length: formValues.length,
					width: formValues.width,
					height: formValues.height,
					weight: formValues.weight,
					// volume: formValues.volume,
				},
				...quotationList || null,
				sellingPriceSetting: {
					priceType: formValues.priceType,
					percentage: formValues.percentage,
					markup: formValues.markup,
					decimalPlacesType: formValues.decimalPlacesType,
					fixedPointPrice: formValues.fixedPointPrice,
					removeMethod: formValues.removeMethod,
					sellingPriceType: formValues.sellingPriceType,
					reduce: formValues.reduce,
					tailNum: formValues.tailNum,
				},
				marketPrice: {
					priceType: formValues.marketPricePriceType,
					percentage: formValues.marketPricePercentage,
					markup: formValues.marketPriceMarkup,
					decimalPlacesType: formValues.marketPricedecimalPlacesType,
					fixedPointPrice: formValues.marketPriceFixedPointPrice,
					removeMethod: formValues.marketPriceRemoveMethod,
					sellingPriceType: formValues.marketPriceSellingPriceType,
					reduce: formValues.marketPriceReduce,
				},
				singlePrice: {
					priceType: formValues.singlePricePriceType,
					percentage: formValues.singlePricePercentage,
					markup: formValues.singlePriceMarkup,
					decimalPlacesType: formValues.singlePricedecimalPlacesType,
					fixedPointPrice: formValues.singlePriceFixedPointPrice,
					removeMethod: formValues.singlePriceRemoveMethod,
					sellingPriceType: formValues.singlePriceSellingPriceType,
					reduce: formValues.singlePriceReduce,
				},
				groupPrice: {
					priceType: formValues.groupPricePriceType,
					percentage: formValues.groupPricePercentage,
					markup: formValues.groupPriceMarkup,
					decimalPlacesType: formValues.groupPricedecimalPlacesType,
					fixedPointPrice: formValues.groupPriceFixedPointPrice,
					removeMethod: formValues.groupPriceRemoveMethod,
					sellingPriceType: formValues.groupPriceSellingPriceType,
					reduce: formValues.groupPriceReduce,
					tailNum: formValues.groupPriceTailNum,
				},
				inventoryNumSettings: formValues.inventoryNumSettings,
				inventoryNums: formValues.inventoryNums,
				inventorySettings: formValues.inventorySettings,
				deliveryTime: formValues.deliveryTime,
				deliveryMethod: formValues.deliveryMethod,
				generateWhitePic: formValues.generateWhitePic ? 1 : 0,
				remove1688OutLink: formValues.remove1688OutLink ? 1 : 0,
				imageVideoUpload: formValues.imageVideoUpload ? 1 : 0,
				uploadDuoduoVideo: !!formValues.uploadDuoduoVideo,

				isDeletePicture: formValues.isDeletePicture ? 1 : 0,
				deletePictureNum: formValues.deletePictureNum,
				replaceMainPicFlag: formValues.replaceMainPicFlag ? 1 : 0,
				replaceMainRule: formValues.replaceMainRule,
				mainPicOrderFlag: formValues.mainPicOrderFlag ? 1 : 0,
				mainPicOrderRule: formValues.mainPicOrderRule ? formValues.selectedImageIndex : 0,
				onlineTrade: formValues.onlineTrade,
				quotationType: formValues.quotationType,
				minOrderQuantity: formValues.minOrderQuantity,
				filterForbiddenWord: formValues.filterForbiddenWord ? 1 : 0,
				mobile: formValues.mobile,
				freightTemplate: formValues.freightTemplate,
				promiseToShip: formValues.promiseToShip,
				tbDeliveryTime: formValues.tbDeliveryTime,
				deliveryTimeType: formValues.deliveryTimeType,
				twoPiecesDiscount: initialformValues.twoPiecesDiscount,
				titleSettings: {
					pre: formValues.pre || '',
					end: formValues.end || '',
					moreThanDel: formValues.moreThanDel,
					replaces: formValues.replaces ? [formValues.replaces] : [],
				},
				skipSettingsOss: {
					titleDel: formValues.titleDel || '',
				},
				servicePromise: {
					freshRotRefund,
					brokenRefund,
					allergyRefund
				}
			},
			
			// 商品类目匹配方式
			matchType: formValues.matchType,

		};
		console.log('提交参数:', params);
		return params;
	};

	// 提交表单处理函数
	const handleSubmit = async() => {
		try {
			const formValues = form.getFieldsValue(true);
			if (formValues.inventoryNumSettings === 2 && !formValues.inventoryNums) {
				message.error('请填写统一修改库存数量');
				return;
			}
			// 表单验证
			const values = await form.validateFields();
			console.log('表单验证通过，提交数据:', values);
			
			// 验证通过后调用原有的提交逻辑
			const params = buildDyPuhuoParams();
			// 这里调用API保存设置
			const res = await updatePlatformPuhuoSetting(params);
			console.log('设置参数:', params, res);
			
			message.success('铺货设置保存成功');
			onClose();
		} catch (errorInfo) {
			console.log('表单验证失败:', errorInfo);
			// 设置message的zIndex确保错误提示显示在最上层
		}
	};

	const defaultFooter = (
		<div className="r-flex r-jc-sb r-ai-c">
			<Space>
				<Button onClick={ onClose }>取消</Button>
				<Button type="primary" onClick={ handleSubmit }>保存</Button>
			</Space>
		</div>
	);

	

	// 监听activeTitle变化
	useEffect(() => {
		if (visible) {
			setContentActiveTab('basic');
			if (activeTitle) {
				setActiveKey(activeTitle);
				fetchPuhuoSetting(activeTitle);
			} else if (!activeTitle && titleList.length > 0) {
				const initialKey = titleList[0]?.sellerId;
				setActiveKey(initialKey);
				fetchPuhuoSetting(initialKey);
			}
		} else {
			setActiveKey('');
		}
	}, [visible]); // 只在activeTitle或visible变化时执行

	return (
		<Drawer
			title={ renderTitle() }
			placement="right"
			size="large"
			closable={ false }
			keyboard={ false }
			maskClosable
			visible={ visible }
			onClose={ onClose }
			className={ s.configDrawer }
			destroyOnClose
			width={ width }
			footer={ footer || defaultFooter }
			// zIndex={ zIndex } // 应用zIndex属性
		>
			<Spin spinning={ settingLoading }>
				{(getCurrentShop()?.platform === PLAT_FXG ? dyRenderContentByPlatform() : null)}
				{(getCurrentShop()?.platform === PLAT_PDD ? pddRenderContentByPlatform() : null)}
				{(getCurrentShop()?.platform === PLAT_TB ? tbRenderContentByPlatform() : null)}
				{(getCurrentShop()?.platform === PLAT_ALI ? aLiRenderContentByPlatform() : null)}
				{(getCurrentShop()?.platform === PLAT_KS ? ksRenderContentByPlatform() : null)}
			</Spin>
		</Drawer>
	);
};

export default ConfigDrawer;
