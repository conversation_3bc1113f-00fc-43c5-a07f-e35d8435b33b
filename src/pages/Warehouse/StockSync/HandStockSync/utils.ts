import { ItemSysItemListOfItemRelationSysTemItemViewResponse } from "@/types/schemas/warehouse/syncstock";

// 处理数据生成批量修改库存接口所需参数
export const getApiParams = (list:any[], operatorType:number, uploadType:number = 0, isSalableItemStock:boolean = false) => {
	let numHasChange = false;
	let tipsNum = 0; // 库存为0时 二次确认提醒
	let stockNumUploadList:any = [];
	list.forEach(data => {
		data.relationPlatformItemList.forEach((item:any) => {
			const { hasChange = false } = item;
			if (hasChange || isSalableItemStock) {
				numHasChange = true;
			}
			if (Number(item.num) === 0) {
				tipsNum += 1;
			}
			let stockNum = data.salableItemDistributableStock > 0 ? data.salableItemDistributableStock : 0;
			let uploadNum = Number(isSalableItemStock ? stockNum : item.num);
			const pushData = {
				uploadNum, // 上传库存数量
				platform: item.platform, // 平台类型
				shopId: item.sellerId, // 店铺id
				platformItemId: item.numIid, // 平台商品id
				platformSkuId: item.skuId, // 平台规格id
				platItemTitle: item.title, // 平台商品标题
				platSkuName: item.skuName, // 平台规格名称
				platSkuOuterId: item.skuOuterId, // 平台规格商家编码
				platItemOuterId: item.itemOuterId, // 平台商品编码
				sysItemId: data.sysItemId, // 货品id
				sysSkuId: data.sysSkuId, // 货品规格id
				operatorType, // 操作类型
				relationStatus: item.relationStatus, // 商品状态
				uploadType, // 上传类型 0 全量上传 默认 1 增量上传 2 减量上传
			};
			// 快手平台特殊处理， 不支持全量上传
			if (item.platform === 'ksxd' && uploadType == 0) {
				let ksxdNum = uploadNum - item.oldNum;
				pushData.uploadNum = Math.abs(ksxdNum);
				pushData.uploadType = ksxdNum > 0 ? 1 : 2;
			}
			stockNumUploadList.push(pushData);
		});
	});

	return { stockNumUploadList, numHasChange, tipsNum };

};

export const dealWithList = (list: ItemSysItemListOfItemRelationSysTemItemViewResponse['data']['list']) => {
	list.forEach(item => {
		item.sysItemSkuList.forEach((sysItem) => {
			sysItem.relationPlatformItemList.forEach((platformSkuItem) => {
				if (platformSkuItem.platform === 'ksxd') {
					platformSkuItem.oldNum = platformSkuItem.num;
				}
			});
	   });
	});
};


