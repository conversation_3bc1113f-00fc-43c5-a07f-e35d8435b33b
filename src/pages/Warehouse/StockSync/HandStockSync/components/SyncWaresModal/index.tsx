import React, { useState, useEffect, useMemo } from 'react';
import { Modal, Form, Checkbox, Row, Spin, Progress, Button, Tooltip, Radio, Select, Input } from 'antd';
import { useForm } from 'antd/es/form/Form';
import cs from 'classnames';
import { ColumnsType } from 'antd/lib/table/interface';
import { useRequest } from 'ahooks';
import { Link } from 'react-router-dom';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { getAllPlats, getShopsByPlat } from "@/components-biz/ShopListSelect/shopListUtils";
import { ItemSyncItemsApi, ItemGetSyncInfoApi } from '@/apis/warehouse/syncstock';
import { ItemSyncItemsRequest, ItemGetSyncInfoResponse } from '@/types/schemas/warehouse/syncstock';
import PlatformIcon from '../PlatformIcon';
import { IShopInfo } from '@/types/schemas/user';
import Table from '@/components/Table';
import { PLAT_MAP, getShopStatus, PLAT_KTT } from "@/constants";
import message from "@/components/message";
import { parseUrlToGoods } from '@/utils';

const { Option } = Select;
interface SyncWaresModalProps {
	visible:boolean,
	onCancel: () => void,
	afterOK?: () => void,
	isOpenAutoSync?:boolean
	isDownload?: boolean,
	code?:string
}

enum downloadTypeText {
	全量下载 = 'ALL',
	增量下载 = 'INCREMENT',
	按商品状态下载 = 'STATUS',
	指定商品下载 = 'NUMIID',
}

// TODO 与getShopStatus合并处理
export const getShopStatusInfo = (seller:any) => {
	const { status, tokenStatus } = seller;
	let disable = false;
	let statusText = '';
	if (tokenStatus === 0) {
		statusText = '授权失效';
		disable = true;
	}
	if (status === 0) {
		statusText = '已被暂停';
		disable = true;
	}
	if (status === 2) {
		statusText = '店铺失效即订购过期';
		disable = true;
	}
	return { statusText, disable };
};

const SyncWaresModal: React.FC<SyncWaresModalProps> = (props) => {
	const { code, visible, onCancel, afterOK, isOpenAutoSync, isDownload } = props;
	const [tableForm] = useForm();
	const [downForm] = useForm();
	const [tablist, setTableList] = useState([]);
	const [syncProgressVisible, setSyncProgressVisible] = useState(false);
	const [okButtonDisabled, setOkButtonDisabled] = useState(false);
	const [syncProgressInfo, setSyncProgressInfo] = useState<ItemGetSyncInfoResponse['data']>([]);
	const [formValues, setFormValues] = useState({});



	useEffect(() => {
		if (visible) {
			getTableList();
			downForm.setFieldsValue({
				importType: downloadTypeText.增量下载
			});
		}
	}, [downForm, visible]);


	// 获取table数据
	const getTableList = async() => {
		let List:any[] = [];
		// 获取所有平台
		const plats = await getAllPlats();
		const obj = {};
		plats.forEach(plat => {
			obj[plat] = [];
		});

		// 获取平台下对应店铺
		getShopsByPlat({ plats }).then(res => {
			if (res.length === 0) {
				setOkButtonDisabled(true);
			}
			res.forEach(seller => {
				obj[seller.platform].push(seller);
			});

			for (const key in obj) {
				List.push({
					platform: key,
					seller: obj[key]
				});
			}
			setTableList(List);
		});
	};
	// 同步商品
	const { runAsync: startSyncWares, loading } = useRequest(ItemSyncItemsApi, {
		manual: true
	});

	const checkSyncProgress = (list:any[]) => {
		let complete = true;
		let hasError = false;
		list.forEach(seller => {
			if (seller.success && seller.progress !== 100) {
				complete = false;
			}
			if (!seller.success) {
				hasError = true;
			}
		});
		return { complete, hasError };
	};

	// 同步商品进度
	const { run: startSyncProgress, cancel: stopGetSyncProgress } = useRequest(ItemGetSyncInfoApi, {
		manual: true,
		pollingInterval: 3000,
		onSuccess: (res => {
			console.log("startSyncProgress", res);
			const { complete, hasError } = checkSyncProgress(res);
			if (complete) {
				// 取消轮询
				stopGetSyncProgress();
				if (hasError) {
					message.error('商品同步完成，有店铺同步错误');
				} else {
					message.success('商品同步完成');
				}
				setTimeout(() => {
					setSyncProgressVisible(false);
					modalClose();
					afterOK();
				}, 2000);
			}
			setSyncProgressInfo(res);
		})
	});

	const modalClose = () => {
		onCancel();
		onResetModal();
	};

	// 点击开始同步
	const modalOk = () => {
		let values = tableForm.getFieldsValue();
		let params = getSyncParams(values);
		if (params.length === 0) {
			message.warning('请选择需要同步的店铺！');
			return;
		}
		let { importType, numIids = '', statusNum } = downForm.getFieldsValue();
		if (!importType && isDownload) {
			message.warning('请选择下载类型！');
			return;
		}
		if (!isDownload) importType = downloadTypeText.全量下载;
		const newParams:ItemSyncItemsRequest = {
			itemSyncParamDTOList: params,
			importType
		};

		if (importType == downloadTypeText.指定商品下载) {
			if (params.length > 1) {
				message.warning('请选择下载类型！');
				return;
			}
			if (numIids.length == 0) {
				message.warning('指定商品下载时需输入链接或商品ID！');
				return;
			}
			const parsePlat = params[0].platform;
			newParams.numIids = numIids.split(/[，,]/).map((i:any) => {
				return (i.includes('.') ? parseUrlToGoods(i, parsePlat) : i).trim();
			});
		}
		if (importType == downloadTypeText.按商品状态下载) {
			if (!statusNum) {
				message.warning('按商品状态下载时需选择商品状态！');
				return;
			}
			newParams.statusNum = statusNum;
		}
		if (importType == downloadTypeText.增量下载 && moreDisable) {
			message.warning('拼多多和快手暂不支持增量下载');
			return;
		}
		if (importType == downloadTypeText.按商品状态下载 && moreDisable2) {
			message.warning('快团团暂不支持按商品状态下载');
			return;
		}
		startSyncWares({ ...newParams, permission_source: code }).then(res => {
			setSyncProgressVisible(true);
			startSyncProgress(params);
			onResetModal();
			// afterOK();
			// modalClose();
		});

	};

	// 获取同步商品参数
	const getSyncParams = (values:any) => {
		let params:ItemSyncItemsRequest = [];
		for (const key in values) {
			const tableKey = key.split('_')[0];
			const rowIndex = key.split('_')[1];
			if (values[key] && tableKey === 'seller') {
				tablist[rowIndex].seller.forEach(item => {
					if (values[key].includes(item.sellerId)) {
						params.push({
							sellerId: item.sellerId,
							platform: item.platform
						});
					}
				});
			}
		}
		// console.log("getSyncParams -> params", params);
		return params;
	};

	// 重置modal
	const onResetModal = () => {
		tableForm.resetFields();
		downForm.resetFields();
		setFormValues({});
	};

	const isMultiShop = useMemo(() => {
		let num = 0;
		for (let i in formValues) {
			if (i.includes('seller') && Array.isArray(formValues[i])) {
				num += formValues[i]?.length;
			}
		}
		return num > 1;
	}, [formValues]);

	const moreDisable = useMemo(() => {
		const disArr = ['pdd', 'ksxd'];
		let values = tableForm.getFieldsValue();
		let params = getSyncParams(values);
		return params.some(i => disArr.includes(i.platform));
	}, [formValues, getSyncParams, tableForm]);

	const moreDisable2 = useMemo(() => {
		const disArr = [PLAT_KTT];
		let values = tableForm.getFieldsValue();
		let params = getSyncParams(values);
		return params.some(i => disArr.includes(i.platform));
	}, [formValues, getSyncParams, tableForm]);

	// form值改变
	const onValuesChange = (changedValues: any, values: any) => {
		// 处理平台和店铺选中关系
		for (const key in changedValues) {
			const tableKey = key.split('_')[0];
			const rowIndex = key.split('_')[1];
			const sellerIds = tablist[rowIndex].seller.filter(item => !getShopStatus(item).disable).map(item => item.sellerId);
			if (tableKey === 'platform') {
				// 操作平台
				tableForm.setFieldsValue({
					[`seller_${rowIndex}`]: changedValues[key] ? sellerIds : undefined
				});
			}

			if (tableKey === 'seller') {
				// 操作店铺
				if (sellerIds.length === changedValues[key].length) {
					tableForm.setFieldsValue({
						[`platform_${rowIndex}`]: true
					});
				} else {
					tableForm.setFieldsValue({
						[`platform_${rowIndex}`]: undefined
					});
				}
			}
		}
		setFormValues(tableForm.getFieldsValue());

	};
	const tableFormKey = (name:any, index:number) => {
		return `${name}_${index}`;
	};
	const getOptions = (row:any) => {
		let options:any = [];
		row.seller.forEach((item:any) => {
			options.push({
				label: item.sellerNick,
				value: item.sellerId,
				status: item.status,
				tokenStatus: item.tokenStatus

			});
		});
		return options;
	};
	const columns:ColumnsType<unknown> = [
		{
			title: '平台',
			width: 200,
			key: 'platform',
			dataIndex: 'platform',
			render: (text, record: IShopInfo, index) => {
				const sellers = getOptions(record);
				const checkSellers = formValues[tableFormKey('seller', index)];
				const disableSellers = sellers?.filter((item: any) => getShopStatus(item).disable);
				const disabled = disableSellers.length === sellers?.length;
				const checkedAll = checkSellers?.length && checkSellers?.length + disableSellers?.length == sellers?.length;
				let indeterminate = false;
				// 设置半选状态
				if (checkSellers?.length && checkSellers?.length + disableSellers?.length < sellers?.length) {
					indeterminate = true;
				}

				return (
					<Form.Item className={ cs('r-mb-0') } valuePropName="checked" key={ tableFormKey('platform', index) } name={ tableFormKey('platform', index) }>
						<Checkbox indeterminate={ indeterminate } checked={ checkedAll } disabled={ disabled }>{PLAT_MAP[text.toLocaleLowerCase()]}</Checkbox>
					</Form.Item>
				);
			}
		},
		{
			title: '店铺',
			width: 327,
			key: 'seller',
			dataIndex: 'seller',
			render: (text, record, index) => {
				return (
					<Form.Item className={ cs('r-mb-0') } key={ tableFormKey('seller', index) } name={ tableFormKey('seller', index) }>
						<Checkbox.Group>
							{
								getOptions(record).map((item:any) => {
									return (

										<Row key={ item['value'] }>
											<Checkbox disabled={ getShopStatus(item).disable } value={ item['value'] }>{item['label']}</Checkbox>
											<div><Link to="/shops" style={ { width: '100%' } } className={ cs('r-ft-12', 'r-c-error', 'r-pointer') }>{getShopStatus(item).statusText}</Link></div>
										</Row>
									);
								})
							}
						</Checkbox.Group>
					</Form.Item>
				);
			}
		}
	];

	const DownloadNode = (
		<>
			<div className={ cs('r-c-error', 'r-mt-20', 'r-mb-10', 'r-fs-12') }>注：选择多个店铺的时候，不支持按商品 ID、链接下载；拼多多和快手暂不支持增量下载；快团团暂不支持按商品状态下载</div>

			<Form form={ downForm } >
				<Form.Item name="importType" label="下载类型">
					<Radio.Group className="r-mt-4">
						<Radio disabled={ moreDisable } value={ downloadTypeText.增量下载 }>增量下载</Radio>
						<Radio value={ downloadTypeText.全量下载 }>全量下载</Radio>
						<Radio disabled={ moreDisable2 } value={ downloadTypeText.按商品状态下载 }>按商品状态下载</Radio>
						<Radio disabled={ isMultiShop } value={ downloadTypeText.指定商品下载 }>指定商品下载</Radio>
					</Radio.Group>
				</Form.Item>
				<Form.Item
					noStyle
					shouldUpdate={ (prevValues, currentValues) => prevValues.importType !== currentValues.importType }
				>
					{
						({ getFieldValue }) => (
							getFieldValue('importType') == downloadTypeText.按商品状态下载 ? (
								<Form.Item name="statusNum" label="商品状态">
									<Select placeholder="商品状态">
										<Option value={ 1 }>出售中</Option>
										<Option value={ 2 }>已下架</Option>
									</Select>
								</Form.Item>
							) : getFieldValue('importType') == downloadTypeText.指定商品下载 ? (
								<Form.Item name="numIids" label="商品链接">
									<Input.TextArea disabled={ isMultiShop } placeholder="请输入链接或商品 ID、支持多个，多个链接用“,”隔开" />
								</Form.Item>
							) : null
						)
					}
				</Form.Item>
			</Form>
		</>
	);

	// 同步进度弹窗
	const SyncProgress = (
		<Modal
			centered
			// width={ 600 }
			visible={ syncProgressVisible }
			footer={ null }
			closable={ false }

		>
			<div className={ cs('r-ta-c', 'r-pb-20', 'r-pt-20') }>
				<div className={ cs('r-fc-black-65', 'r-ta-c', 'r-pb-16', 'r-fs-18') }>
					<Spin className={ cs('r-mr-8') } />
					<span>商品同步中......</span>
				</div>
				{
					syncProgressInfo.map(seller => {
						return (
							<div key={ seller.sellerId } className={ cs('r-ta-c', 'r-lh-12', 'r-mt-8') } >
								<div className={ cs('r-flex', 'r-ai-c') }>
									<PlatformIcon fontSize={ 18 } platform={ seller.platform } />
									<div >{seller.sellerNick}</div>
								</div>
								<div ><Progress strokeColor="#F5821F" percent={ seller.progress } status={ !seller.success ? 'exception' : 'active' } /></div>
								{(seller.errorMsg || seller.extendInfo) && <div><span className={ cs('r-c-error', 'r-pl-4', 'r-fs-12') }>{seller.errorMsg || seller.extendInfo}</span></div>}
							</div>
						);
					})
				}

			</div>
		</Modal>
	);

	return (
		<Modal
			centered
			width={ 600 }
			title="选择同步店铺"
			visible={ visible }
			onCancel={ modalClose }
			maskClosable={ false }
			footer={ [
				isOpenAutoSync ? (
					<Tooltip title="您已开启库存自动同步，同步商品时不会将店铺库存同步至ERP本地库存">
						<ExclamationCircleOutlined style={ { color: 'red' } } />
					</Tooltip>
				) : null,
				isDownload ? (
					<div style={ { textAlign: 'center' } }>
						<Button key="submit" type="primary" onClick={ modalOk } style={ { marginLeft: '5PX' } }>
							开始下载
						</Button>
						<Button key="back" onClick={ modalClose } style={ { marginLeft: '20PX' } }>
							取消
						</Button>
					</div>
				) : (
					<Button key="submit" type="primary" onClick={ modalOk } style={ { marginLeft: '5PX' } }>
						开始同步
					</Button>
				)
			] }
			onOk={ modalOk }
			confirmLoading={ loading }
			okButtonProps={ { disabled: okButtonDisabled } }

		>
			<Form onValuesChange={ onValuesChange } form={ tableForm } >
				<Table
					size="small"
					rowKey="platform"
					pagination={ false }
					scroll={ { y: 300 } }
					bordered
					columns={ columns }
					dataSource={ tablist }
				/>
			</Form>

			{ isDownload && DownloadNode }


			{/* 同步进度弹窗 */}
			{SyncProgress}

		</Modal>
	);
};

export default SyncWaresModal;
