import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useForm } from 'antd/es/form/Form';
import { Button, Space, Checkbox, InputNumber, Modal, Tooltip, Popover, Form, Radio } from 'antd';
import cs from 'classnames';
import { ColumnsType } from "antd/lib/table";
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { isEmpty, isInteger, debounce } from "lodash";
import { useRequest } from 'ahooks';
import { ExclamationCircleOutlined, FormOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import SearchTable from '@/components/SearchTable';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from "@/components/SearchTable/FormWidthRow";
import WaresInfo from "@/components-biz/WaresInfo";
import FooterBar from '@/components/FooterBar';
import PlatformIcon from "./components/PlatformIcon";
import { ItemSysItemListOfItemRelationSysTemItemViewRequest, ItemSysItemListOfItemRelationSysTemItemViewResponse, StockStockNumUploadForeachUpdateRequest } from '@/types/schemas/warehouse/syncstock';
import { StockStockNumUploadForeachUpdateApi } from '@/apis/warehouse/syncstock';
import { getApiParams, dealWithList } from './utils';
import SyncWaresModal from "./components/SyncWaresModal";
import WarpText from '@/components-biz/WarpText';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import s from './index.module.scss';
import Input from '@/components/Input/InputSearch';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { isValidTableRowClick } from "@/utils/util";
import CombineTag from '@/components-biz/Trade/CombineTag';
import userStore from "@/stores/user";
import message from "@/components/message";
import { Obj } from '@/pages/Trade/interface';
import Icon from '@/components/Icon';
import ProgressModal from '@/components-biz/ProgressModal';
import { ItemAsyncGetProgressApi } from '@/apis/warehouse/system';
import { groupStockNumOutWay } from '@/pages/Index/Settings/System/constants';
import { tradeStore } from '@/stores';
import { belongStartAndEnd, compareRowId } from '../../utils';
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import { PLAT_SCMHAND } from '@/constants';
import { isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import { fetchSysItemViewWithPaginationOptimization } from '@/pages/Warehouse/System/Relation/components/PlatformTable/utils/paginationOptimization';

interface StockSyncProps {

}

interface UploadStockNumProps {
	data?:any,
	uploadType?:number // 0.全量上传，默认,1.增量上传,2.减量上传
	operatorType?:number // 1单个保存 2批量保存 3批量增加 4批量减少
	isSalableItemStock?: boolean // true 使用可配货库存
}

enum relationStatusText {
	已失效商品 = 'EXPIRE',
	被删除商品 = "DELETE"
}

enum EnumTabKey {
	单品 = '0',
	组合 = '1'
}

const MAX_SHOW_COUNT:number = 3;

// 表格部分宽度配置
const columnsWidthConfig = {
	 '图片': '112px',
	 '规格/货品规格编码': '12vw',
	 '条形码/货号': '8vw',
	 '可配货库存': '7vw',
	 '平台店铺': '13vw',
	 '商品': '21vw',
	 '线上库存': '7vw',
	 '操作': '120px'
};

type TableListData = ItemSysItemListOfItemRelationSysTemItemViewResponse['data']['list'] & ItemSysItemListOfItemRelationSysTemItemViewResponse['data']['list'][number]['sysItemSkuList'][number];
interface SearchTableProps {
	pageNo?: number
	pageSize?: number
	itemAlias?: string
	skuOuterId?: string
	skuName?: string
	itemNo?: string
	barCode?: string
}

const FormFieldColProps = {
	xs: 24,
	sm: 12,
	md: 5,
	lg: 3,
	xl: 3,
	xxl: 3,
};

// 获取当前行的合计线上库存
const getRowTotalNum = (row: Partial<TableListData>) => {
	let totalNum = 0;
	row.relationPlatformItemList.forEach(platformItem => {
		if (platformItem.num) {
			totalNum += Number(platformItem.num);
		}
	});
	return totalNum;
};

const HandStockSync:React.FC<StockSyncProps> = (props) => {
	const ref = useRef<SearchTableRefProps>();
	const [form] = useForm();
	const [batchSetForm] = useForm();
	// 保存用户每个货品对应的平台商品的库存相关数据
	const [sysnStockData, setSysnStockData] = useState([]);
	const [dataSource, setDataSource] = useState<TableListData>([]);
	const [loopObj, setLoopObj] = useState({
		asyncCode: '',
		isStart: false,
	});
	// 同步商品弹窗
	const [syncWaresModalVisible, setSyncWaresModalVisible] = useState(false);
	const [isOpenAutoSync, setIsOpenAutoSync] = useState(false);
	const [batchToggleStatus, setBatchToggleStatus] = useState(false);
	const [tabKey, seTabKey] = useState(EnumTabKey.单品);
	const [oldSelectObj, setOldSelectObj] = useState({ index: "", checked: false });
	const { userInfo, systemSetting } = userStore;
	useEffect(() => {
		// setAdParams().then((res) => {
		// 	window.Ads.prototype.getAdShow('syncStock');
		// });
		userStore.getSystemSetting();
	}, []);

	// 表格搜索项
	const FormFieldList: FormItemConfig[] = [
		{
			name: 'isCombination',
			label: '',
			children: (
				<Radio.Group buttonStyle="solid" size="small" defaultValue={ EnumTabKey.单品 } onChange={ e => { onTabChange(e.target.value); } }>
					<Radio.Button value={ EnumTabKey.单品 }>单品</Radio.Button>
					<Radio.Button value={ EnumTabKey.组合 }>组合</Radio.Button>
				</Radio.Group>
			)
		},
		{
			name: 'itemAlias',
			label: "",
			// colProps: FormFieldColProps,
			children: <Input placeholder="货品简称" />,
		},
		{
			name: 'skuOuterId',
			label: "",
			// colProps: FormFieldColProps,
			children: <Input placeholder="货品规格编码" />,
		},
		{
			name: 'skuName',
			label: "",
			// colProps: FormFieldColProps,
			children: <Input placeholder="规格/规格别名" />,
		},
		{
			name: 'itemNo',
			label: "",
			// colProps: FormFieldColProps,
			children: <Input placeholder="货号" />,
		},
		{
			name: 'barCode',
			label: "",
			// colProps: FormFieldColProps,
			children: <Input placeholder="条形码" />,
		}
	];

	useEffect(() => {
		tradeStore.tradeListStore.keyDown();
		return () => {
			tradeStore.tradeListStore.cancelKeyDown();
		};
	}, []);
	// 批量更新线上库存
	const { run: UpdateStockStockNumUpload, loading: actionLoading } = useRequest(StockStockNumUploadForeachUpdateApi, {
		manual: true,
		onSuccess: (res) => {
			const asyncCode = res.cacheKey;
			setLoopObj({
				asyncCode,
				isStart: true,
			});
		}
	});

	// 查询采购入库列表数据
	const getProductListApi = (value: SearchTableProps) => {
		console.log("查询数据", value);
		let params:ItemSysItemListOfItemRelationSysTemItemViewRequest;
		params = {
			pageSize: 10,
			pageNo: 1,
			...value,
			isCombination: tabKey
		};
		return fetchSysItemViewWithPaginationOptimization(params, params.pageSize, { threshold: 100 }).then((res: ItemSysItemListOfItemRelationSysTemItemViewResponse['data']) => {
			setBatchToggleStatus(false);
			dealWithList(res.list);
			setSysnStockData(res.list);
			console.log('res:::', res);
			return res;
		});
	};

	// 基于平台商品拆分维度
	const responseAdapter = (data: ItemSysItemListOfItemRelationSysTemItemViewResponse['data']) => {
		let platformDataList:TableListData = [];
		[...data.list].forEach((item, i) => {
			const addList:any = [];
			item.sysItemSkuList.forEach((sku, index:number) => {
				if (sku?.relationPlatformItemList?.length) {
					const checkDisabled = sku.relationPlatformItemList.every(i => ['EXPIRE', 'DELETE'].includes(i.relationStatus));
					sku.relationPlatformItemList.forEach((platformItem, idx) => {
						let statusError = platformItem['relationStatus'] === 'EXPIRE' || platformItem['relationStatus'] === 'DELETE';
						let one: Record<string, any> = {
							selectRowId: `${i}_${index}`,
							hasplatformItem: true,
							sysItemIndex: i,
							skuIndex: index,
							platformItemIndex: idx,
							statusError,
							...(idx === 0 ? { colSpan2: sku.relationPlatformItemList.length } : {}),
							...item,
							...sku,
							platformItem,
							checkDisabled,
							isItemShow: index < MAX_SHOW_COUNT,
						};
						if (index === 0 && idx === 0 && item.sysItemSkuList.length) {
							// 第一条数据关联了商品
							let num = 0;
							item.sysItemSkuList.forEach((k, j) => {
								num += k.relationPlatformItemList.length || 1;
							});
							one.colSpan = num;
						}

						if (idx === sku.relationPlatformItemList.length - 1) {
							one.showTotalNum = true;
						}
						addList.push(one);
					});
				} else {
					let one = {
						selectRowId: `${i}_${index}`,
						hasplatformItem: false,
						colSpan2: 1,
						sysItemIndex: i,
						skuIndex: index,
						...(index === 0 ? { colSpan: item.sysItemSkuList.length } : {}),
						...item,
						...sku,
						checkDisabled: true,
						isItemShow: index < MAX_SHOW_COUNT,
					};
					if (index === 0) {
						// 第一条数据是空商品
						let num = 0;
						item.sysItemSkuList.forEach((k, j) => {
							num += k.relationPlatformItemList.length || 1;
						});
						one.colSpan = num;
					}
					addList.push(one);
				}
			});
			// 展开规格的点击判断
			if (addList.length > MAX_SHOW_COUNT) {
				addList.push({ isCollapse: true });
			}
			addList.forEach((addItem:any, index:number) => {
				if (index == 0) addItem.indexNum = i + 1;
				addItem.sysItemId = item.sysItemId;
				addItem.rowId = `row_${index}_${item.sysItemId}`;
				addItem['collapseIndex'] = index;
				addItem.collapseShow = item.collapseShow;
			});
			platformDataList = [...platformDataList, ...addList];
		});
		setDataSource(platformDataList);
		return {
			list: data.list,
			total: data.total,
		};
	};

	const tableRowDataSource = useMemo(() => {
		return dataSource.filter(i => i.isCollapse || i.collapseIndex < MAX_SHOW_COUNT || i.collapseShow);
	}, [dataSource]);

	// 展开更多货品
	const toggleList = (row: TableListData[number]) => {
		dataSource.forEach((i, index) => {
			if (i.sysItemId === row.sysItemId) {
				i.collapseShow = !i.collapseShow;
				if (i.collapseShow) {
					i.isItemShow = true;
				} else if (index < MAX_SHOW_COUNT) {
					i.isItemShow = true;
				} else {
					i.isItemShow = false;
				}
			}
		});
		setDataSource([...dataSource]);
	};

	const batchToggleList = () => {
		sendPoint(Pointer.库存_库存同步_批量展开收起);
		dataSource.forEach(i => {
			i.collapseShow = !batchToggleStatus;
		});
		setDataSource([...dataSource]);
		setBatchToggleStatus(!batchToggleStatus);
	};

	// 修改线上库存
	const onInputPlatformItemNum = (value:string, platformItem:any, sysSkuId:string) => {
		console.log("onInputPlatformItemNum -> 输入线上库存数量", value);
		if (Number(value) < 0 || !isInteger(value)) {
			return;
		}
		// console.log("onInputPlatformItemNum -> value,platformItem,sysSkuId", value, platformItem, sysSkuId);
		setSysnStockData(
			sysnStockData.map(item => {
				 item.sysItemSkuList.map((sysItem:any) => {
					if (sysItem.sysSkuId === sysSkuId) {
						sysItem.relationPlatformItemList.map((platformSkuItem:any) => {
							if (platformSkuItem.skuId === platformItem.skuId && platformSkuItem.numIid === platformItem.numIid && platformSkuItem.platform === platformItem.platform) {
								platformSkuItem.hasChange = Number(value) !== Number(platformSkuItem.num);
								console.log("onInputPlatformItemNum -> platformSkuItem.num", platformSkuItem.num);
								platformSkuItem.num = value;
							}
							return platformSkuItem;
						});
					}
					return sysItem;
				});
				return item;
			})
		);
	};
	/**
	 * 操作——库存同步上传
	 */
	const onUploadStockNum = (props:UploadStockNumProps) => {
		console.log("onUploadStockNum -> 库存上传", props);
		const { data, uploadType = 0, operatorType, isSalableItemStock = false } = props;
		let params:StockStockNumUploadForeachUpdateRequest;
		let List: any[] = [];
		if (operatorType > 1) {
			// 批量操作
			// 被选中的货品的信息 当前行和skuId
			let checkedRowInfo: {
				rowIndex: string,
				sysSkuId: string,
			}[] = [];
			dataSource.filter(item => item.isChecked).forEach(item => {
				let obj = {
					rowIndex: item.sysItemIndex,
					sysSkuId: item.sysSkuId,
				};
				checkedRowInfo.push(obj);
			});
			if (isEmpty(checkedRowInfo)) {
				message.warning('请先选中一个货品再进行操作！');
				return;
			}
			console.log('checkedRowInfo:', checkedRowInfo);
			console.log('sysnStockData:', sysnStockData);
			// 获取接口所需参数
			checkedRowInfo.forEach(row => {
				sysnStockData[row.rowIndex]?.sysItemSkuList.forEach((sysItem:any) => {
					if (sysItem.sysSkuId === row.sysSkuId) {
						sysItem.sysItemId = sysnStockData[row.rowIndex]['sysItemId'];
						List.push(sysItem);
					}
				});
			});
		} else {
			// 单个操作
			List.push(data);
		}

		const { stockNumUploadList, numHasChange, tipsNum } = getApiParams(List, operatorType, uploadType, isSalableItemStock);
		params = {
			stockNumUploadList: stockNumUploadList.filter(item => item.relationStatus !== 'DELETE')
		};
		console.log("onUploadStockNum -> 参数", params);
		if (!numHasChange && operatorType !== 3) {
			message.error('库存未发生变更');
			return;
		}
		if (tipsNum > 0 && !isSalableItemStock) {
			onConfirmAction(tipsNum, () => {
				UpdateStockStockNumUpload(params);
			});
		} else {
			UpdateStockStockNumUpload(params);
		}

	};

	const onConfirmAction = (tipsNum:number, cb:()=> void) => {
		Modal.confirm({
			centered: true,
			title: '库存同步',
			okText: '确认',
			cancelText: '取消',
			icon: <ExclamationCircleOutlined />,
			content: (
				<div><div>确定继续同步线上库存吗？</div><div className={ cs('r-c-error') }>有【{tipsNum}】个商品待同步库存为0，继续上传可能造成线上商品下架，请仔细核对后同步</div></div>
			),
			onOk() {
				cb && cb();
			},
			onCancel() {
			},
		});
	};

	const dealShift = (record) => {
		const { selectRowId } = record;
		if (oldSelectObj.index) {
			const { index, checked } = oldSelectObj;
			const [startRowId, endRowId] = compareRowId(selectRowId, index);

			setDataSource(prev => {
				prev.forEach((item) => {
					if (item.selectRowId && belongStartAndEnd({ startRowId, endRowId, compareRowId: item.selectRowId }) && item.isItemShow && !item.checkDisabled) {
						item.isChecked = checked;
					}
				});
				return [...prev];
			});
		}
	};

	// 单选
	const onCheckedItem = (e:CheckboxChangeEvent, record: TableListData[number]) => {
		const { isShiftDown } = tradeStore.tradeListStore;
		if (!isShiftDown) {
			let checked = false;
			setDataSource(prev => {
				let prevIndex = prev.findIndex(d => d.selectRowId === record.selectRowId);
				if (prevIndex > -1) {
					prev[prevIndex].isChecked = e.target.checked;
					checked = e.target.checked;
				}
				return [...prev];
			});
			setOldSelectObj({
				index: record.selectRowId,
				checked
			});
		} else {
			dealShift(record);
		}
	};

	// 全选
	const onCheckAllChange = (e:CheckboxChangeEvent) => {
		setDataSource(prev => {
			prev.forEach(item => {
				if (item.colSpan2 && !item.checkDisabled) item.isChecked = e.target.checked;
			});
			return [...prev];
		});
	};

	// 点击商品同步至本地
	const onSyncPlatformItem = () => {
		console.log('按库存数量 同步商品');
		setSyncWaresModalVisible(true);
	};

	const span2RenderCell = (row:any, index:number) => {
		return {
			style: row.colSpan2 ? {} : { borderTop: 0 }
		};
	};

	const checkedGroup = useMemo(() => {
		let filterList = dataSource.filter(item => !item.isCollapse && item.colSpan2);
		let [checkNum, disNum, totalNum] = [0, 0, 0];
		filterList.forEach(i => {
			checkNum += i.isChecked ? 1 : 0;
			disNum += i.checkDisabled ? 1 : 0;
			totalNum += 1;
		});
		return {
			show: totalNum > 0,
			disabled: disNum == totalNum,
			indeterminate: checkNum > 0 && disNum + checkNum < totalNum,
			checked: checkNum == totalNum || (checkNum > 0 && checkNum + disNum == totalNum)
		};
	}, [dataSource]);

	const span0RenderCell = (record: TableListData[number], obj: {
		children: JSX.Element | string | null;
		props: Obj;
	}) => {
		return record.isCollapse ? {
			colSpan: 0,
		} : obj;
	};

	// 批量设置库存数
	const batchSetStock = () => {
		console.log('userStore?.systemSetting::', userStore?.systemSetting);
		const isVirtualCombined = userStore?.systemSetting.groupStockNumOutWay == groupStockNumOutWay.以子货品为准 && tabKey == EnumTabKey.组合;
		const list = dataSource.filter(item => item.isChecked);
		if (!list.length) {
			message.warning("请选择货品");
			return;
		}
		const handleOk = async() => {
			const { asyncNum } = batchSetForm.getFieldsValue();
			setDataSource(prev => {
				prev.forEach(item => {
					if (item.isChecked) {
						item.relationPlatformItemList?.forEach(_relationItem => {
							let originNum = _relationItem.num;
							let num = 0;
							for (let key of asyncNum) {
								num += +item[key] || 0;
							}
							_relationItem.num = num;
							_relationItem.hasChange = Number(originNum) !== Number(num);
						});
					}
				});
				return [...prev];
			});
		};
		Modal.confirm({
			centered: true,
			title: '批量设置库存数',
			okText: '添加',
			cancelText: '取消',
			content: (
				<div>
					<Form
						form={ batchSetForm }
						className={ s.batchEditForm }
					>
						<Form.Item name="asyncNum" label="同步数量">
							<Checkbox.Group>
								<Checkbox value="salableItemDistributableStock">可配货库存</Checkbox>
								<Checkbox value={ isVirtualCombined ? "fictitiousTransitItemStock" : "transitItemStock" }>在途库存</Checkbox>
							</Checkbox.Group>
						</Form.Item>
					</Form>
				</div>

			),
			onOk() {
				handleOk();
			}
		});
	};

	// 表格头部配置
	const columns: ColumnsType<TableListData[number]> = [
		{
			title: '',
			width: 38,
			align: 'center',
			key: 'index',
			className: cs(s.noline),
			render: (text, record, index) => {
				return record.colSpan && record.indexNum;
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.colSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '货品简称',
			width: 300,
			key: 'sysItemAlias',
			dataIndex: 'sysItemAlias',
			className: cs('table-right-border'),
			render: (text, record) => {
				return record.colSpan && (
					<div className="r-flex r-fd-c">
						<Tooltip title="货品简称" placement="topLeft">
							<WarpText style={ { width: "12vw" } }>{text}</WarpText>
						</Tooltip>

						<Tooltip title="货品编码" placement="topLeft">
							<WarpText style={ { width: "12vw" } }>{record.outerId}</WarpText>
						</Tooltip>
					</div>
				);
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.colSpan ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: (
				<Space>
					{ checkedGroup.show ? (
						<Checkbox
							checked={ checkedGroup.checked }
							indeterminate={ checkedGroup.indeterminate }
							disabled={ checkedGroup.disabled }
							onChange={ onCheckAllChange }
						/>
					) : null }
					<div>图片</div>
				</Space>
			),
			key: 'picUrl',
			width: 112,
			dataIndex: 'picUrl',
			className: cs(s.noline),
			render: (text, record, index) => {
				const obj = {
					children: record.colSpan2 && (
						<Space>
							<Checkbox
								disabled={ record.checkDisabled }
								onChange={ e => onCheckedItem(e, record) }
								checked={ record.isChecked }
							/>
							<WaresInfo imgUrl={ record.picUrl } />
						</Space>
					)
				};

				return record.isCollapse ? {
					children: (
						<span
							onClick={ () => toggleList(record) }
							className={ s.collapseBtn }
						>
							{record?.collapseShow ? '收起' : '展开更多'}货品规格
						</span>
					),
					props: {
						colSpan: 8
					}
				} : obj;
			},
			onCell: (row:any, index:number) => {
				return {
					style: row.isCollapse || row.colSpan2 ? {} : { borderTop: 0 }
				};
			}
		},
		{
			title: '规格/货品规格编码',
			key: 'sysSkuName',
			width: columnsWidthConfig['规格/货品规格编码'],
			dataIndex: 'sysSkuName',
			className: cs(s.noline),
			render: (text, record, index) => {
				const obj = {
					children: record.colSpan2 && (
						<>
							<div className="r-flex r-ai-c">
								<CombineTag visible={ record.isCombination === 1 } />
								{record.sysSkuName}
							</div>
							<div>货品规格编码：{record.skuOuterId}</div>
						</>
					),
					props: {},
				};
				return span0RenderCell(record, obj);
			},
			onCell: span2RenderCell
		},
		{
			title: '条形码/货号',
			key: 'itemNo',
			width: columnsWidthConfig['条形码/货号'],
			dataIndex: 'itemNo',
			className: cs(s.noline),
			render: (text, record, index) => {
				const obj = {
					children: record.colSpan2 && (<><div>{record.barCode}</div><div>{record.itemNo}</div></>),
					props: {},
				};
				return span0RenderCell(record, obj);
			},
			onCell: span2RenderCell
		},
		{
			title: (
				<div className="r-flex">
					可配货库存
					<Tooltip title="如组合货品库存计算方式开启以子货品为准，系统将同步以子货品自动计算后的虚拟库存。">
						<div className="r-ml-4"><QuestionCircleOutlined style={ { color: '#999' } } /></div>
					</Tooltip>
				</div>
			),
			width: columnsWidthConfig['可配货库存'],
			key: 'salableItemDistributableStock',
			dataIndex: 'salableItemDistributableStock',
			className: cs('table-right-border'),
			render: (text, record, index) => {
				const obj = {
					children: record.colSpan2 && text,
					props: {},
				};
				return span0RenderCell(record, obj);
			},
			onCell: span2RenderCell
		},
		{
			title: '平台店铺',
			width: columnsWidthConfig['平台店铺'],
			key: 'sellerNick',
			dataIndex: 'sellerNick',
			render: (text, record, index) => {
				const obj = {
					children: record.platformItem
					&& (
						<div className={ cs('r-flex', 'r-ai-c') }>
							<PlatformIcon platform={ record.platformItem?.platform } /> {record.platformItem?.sellerNick}
						</div>
					),
					props: {},
				};
				return span0RenderCell(record, obj);
			}

		},
		{
			title: '商品',
			width: columnsWidthConfig['商品'],
			key: 'numIid',
			dataIndex: 'numIid',
			render: (text, record, index) => {
				const obj = {
					children: record.platformItem ? (
						<div>
							<WaresInfo
								orderInfo={ record.platformItem }
								align="fs"
								imageClassName="c-wareinfo-image"
								className={ cs(record.statusError ? 'r-c-error' : 'r-lh-22') }
								imgSize={ 48 }
								imgUrl={ record.statusError ? '' : record.platformItem?.picUrl }
								skuName={ record.statusError ? '' : record.platformItem?.skuName }
								wareName={ record.statusError ? '已失效货品，没有对应的平台商品' : record.platformItem?.title }
								showNumIid
								linkDetailId={ !record.statusError ? record.platformItem?.numIid : null }
								linkDetailPlatForm={ !record.statusError ? record.platformItem?.platform : null }
								banJump={ !userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(record.platformItem) }
							/>
							{record.showTotalNum ? (
								<div
									style={ { width: `calc(${columnsWidthConfig['线上库存']} + ${columnsWidthConfig['商品']})` } }
									className={ cs('r-flex', 'r-mt-8', 'r-jc-fs', 'r-lh-22') }
								>
									<div style={ { width: columnsWidthConfig['商品'] } } className={ cs('r-fw-600', s.totalTopBorder) }>合计</div>
								</div>
							) : ''}

						</div>
					) : '没有对应的平台商品',
					props: {
						className: record.platformItem ? null : cs('r-fc-black-45', s.cellCenter)
					}
				};
				return span0RenderCell(record, obj);
			}

		},
		{
			title: (
				<>同步数量<FormOutlined
					onClick={ () => {
						batchSetStock();
					} }
					className="r-ml-6"
				/>
				</>
			),
			width: columnsWidthConfig['线上库存'],
			key: 'num',
			dataIndex: 'num',
			render: (text, record, index) => {
				const obj = {
					children: record.platformItem && (
						<div className={ s.tdAbsoluteDiv }>
							<InputNumber
								disabled={ record.statusError || isOpenAutoSync }
								controls={ false }
								className={ cs('r-ta-r', record.platformItem?.hasChange ? 'r-input-val-change' : '') }
								value={ record.platformItem?.num }
								onChange={ (value) => onInputPlatformItemNum(value, record.platformItem, record.sysSkuId) }
							/>
							{record.showTotalNum ? (
								<div style={ { width: '90px' } } className={ cs('r-ta-r', s.totalTopBorder) }>{getRowTotalNum(record)}</div>
							) : ''}
						</div>
					),
					props: {},
				};
				return span0RenderCell(record, obj);
			}

		},
		{
			title: (
				<div className="r-flex">
					操作
					<Tooltip title="展开/收起所有货品规格">
						<div>
							<Icon onClick={ batchToggleList } size={ 14 } style={ { color: '#FD8204' } } type={ batchToggleStatus ? 'shouqi' : 'zhankai' } />
						</div>
					</Tooltip>
				</div>
			),
			dataIndex: 'action',
			align: 'center',
			key: 'action',
			width: 100,
			render: (text, record, index) => {
				const obj = {
					children: record.platformItem && record.colSpan2 && (
						<Tooltip placement="top" title={ isOpenAutoSync ? "库存已自动同步，无须手动同步" : '' } >
							<Button
								type="primary"
								data-point={ Pointer['库存_库存同步_保存'] }
								size="small"
								onClick={ () => onUploadStockNum({ data: { ...record }, operatorType: 1 }) }
								disabled={ isOpenAutoSync }
							>
								上传至店铺
							</Button>
						</Tooltip>
					),
					props: {},
				};
				return span0RenderCell(record, obj);
			},
			onCell: span2RenderCell
		}
	];

	const refreshPage = () => {
		ref?.current?.refresh();
	};

	const onTabChange = (e) => {
		seTabKey(e);
	};

	useEffect(() => {
		refreshPage();
	}, [tabKey]);


	// 列表页顶部额外的操作内容
	// const expandContext = (
	// 	<Tabs onChange={ onTabChange }>
	// 		<Tabs.TabPane tab="单品" key={ EnumTabKey.单品 } />
	// 		<Tabs.TabPane tab="组合" key={ EnumTabKey.组合 } />
	// 	</Tabs>
	// );

	const renderResultModal = (result:any = {}) => {
		const { success = 0, fail = 0 } = result;
		Modal.info({
			centered: true,
			title: '同步库存',
			closable: true,
			content: (
				<div>
					<div className={ cs('r-fw-500') }>本次批量同步库存：</div>
					<div>同步成功：{success}</div>
					<div>同步失败：{fail}</div>
				</div>),
		});
	};

	return (
		<NormalLayout style={ { paddingBottom: '70px', height: '100%' } }>
			<SearchTable<ItemSysItemListOfItemRelationSysTemItemViewResponse['data']['list'][0]>
				pageSizeId="stockSyncTable"
				searchBtnProps={ { style: { marginLeft: 0, marginRight: '8px' } } }
				ref={ ref }
				form={ form }
				fetchData={ getProductListApi }
				responseAdapter={ responseAdapter }
				searchBtnPoint={ Pointer['库存_库存同步_查询'] }
				rowFormConfig={ {
					formList: FormFieldList,
					style: {
						background: '#ffffff',
						padding: '8px 16px 16px',
					},
					rowProps: {
						style: {
							alignItems: 'unset !important',
							rowGap: '8px'
						}
					}, // 表单行配置
					colProps: {}, // 表单列配置
					formItemProps: {},
				} }
				baseTableConfig={ {
					dataSource: tableRowDataSource,
					rowKey: 'rowId',
					groupId: 'groupId',
					columns,
					loading: actionLoading,
					// expandContext,
					expandContextStyle: 'tabExpand',
					// expandContextStyle: {
					// 	marginBottom: '0px',
					// 	width: "100%"
					// },
					scroll: {
						x: '100%'
					},
					scrollExtraHeight: 70,
					onRow: (record, index) => {
						return {
							onClick: debounce((e) => {
								if (isValidTableRowClick(e)) {
									if (!record.checkDisabled) {
										onCheckedItem({ target: { checked: !record.isChecked } }, record);
									}
								}
							}, 200)
						};
					},
					components: {
						header: {
							row: (row:any) => {
								return (
									<>
										<tr>
											<th style={ { color: '#fff' } } className={ cs('r-bg-F5821F') } colSpan={ 6 }>快递助手货品</th>
											<th style={ { color: '#fff' } } className={ cs('r-bg-52C41A') } colSpan={ 6 }>平台商品</th>
										</tr>
										<tr>
											{row.children.map((item:any) => {
												return item;
											})}
										</tr>
									</>
								);
							}
						}
					},

				} }
			/>
			{/* 批量操作按钮 */}
			<FooterBar>
				<Space size={ 16 }>

					<Button disabled={ isOpenAutoSync } type="primary" data-point={ Pointer['库存_库存同步_批量保存'] } onClick={ () => onUploadStockNum({ operatorType: 2 }) }>批量同步</Button>
					<Button
						disabled={ isOpenAutoSync }
						data-point={ Pointer['库存_库存同步_批量增加'] }
						onClick={ () => onUploadStockNum({ uploadType: 1, operatorType: 3 }) }
					>增量同步
					</Button>
					<Popover content={ (
						<>
							<p>1、批量同步时，系统会将同步库存数量更新线上库存</p>
							<p>2、增量同步时，系统会在当前线上库存数量上增加同步库存数量</p>
						</>
					) }
					>
						<QuestionCircleOutlined />
					</Popover>
				</Space>
			</FooterBar>

			{/* 商品同步至本地 */}
			<SyncWaresModal
				afterOK={ refreshPage }
				visible={ syncWaresModalVisible }
				onCancel={ () => { setSyncWaresModalVisible(false); } }
				isOpenAutoSync={ isOpenAutoSync }
			/>
			<ProgressModal
				loopObj={ {
					...loopObj,
					api: ItemAsyncGetProgressApi
				} }
				modalProps={ {
					title: '同步进度查询'
				} }
				onClose={ () => {
					setLoopObj(prev => ({
						...prev,
						isStart: false,
					}));
				} }
				onSuccess={ (res) => {
					setLoopObj(prev => ({
						...prev,
						isStart: false,
					}));
					renderResultModal(res.result);
					refreshPage();
				} }
			/>
			{/* 广告弹窗 */}
			{/* <div className="sys_position_replace" data-type="middlePopup" data-page="syncStock" />
			<div className="sys_position_replace rightBottomPopup" data-type="rightBottomPopup" data-page="syncStock" /> */}
		</NormalLayout>
	);
};
export default HandStockSync;
