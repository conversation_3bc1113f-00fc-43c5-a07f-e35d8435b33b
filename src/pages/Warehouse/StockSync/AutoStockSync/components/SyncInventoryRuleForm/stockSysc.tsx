import React, { memo, useMemo, useState, useCallback, useEffect } from 'react';
import { Form, Checkbox, InputNumber, Radio, Tooltip, Popover, RadioChangeEvent } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

import userStore from '@/stores/user';
import { groupStockNumOutWay } from '@/pages/Index/Settings/System/constants';
import s from './index.module.scss';
import imgXianHuo from '@/assets/image/img_现货@2x.png';
import imgNewXianHuo from '@/assets/image/img_新现货@2x.png';

interface IStockSync {
	onUpdate: (name: string, value: any) => void;
	initValues: any;
	form?: any;
}

const CheckboxRow: React.FC<{ title: string; items: any[] }> = memo(({ title, items }) => (
	<div>
		<span>{title}：</span>
		{items.map(({ name, label, extra, defaultChecked }) => (
			<Form.Item name={ name } valuePropName="checked" key={ name } noStyle>
				<Checkbox defaultChecked={ defaultChecked }>
					{label} {extra}
				</Checkbox>
			</Form.Item>
		))}
	</div>
));

const StockPercentageInput: React.FC = memo(() => (
	<div className={ s.row9 }>
		<span>库存百分比:</span>
		<Form.Item name="stockPercentage" noStyle>
			<InputNumber placeholder="请填写比例数值，默认100%" addonAfter="%" style={ { width: '328px' } } className="r-ml-4 r-mr-8" min={ 0 } />
		</Form.Item>
		+
		<Form.Item name="stockNum" noStyle>
			<InputNumber className="r-ml-8" placeholder="指定数量" min={ 0 } />
		</Form.Item>
	</div>
));

const OtherSettings: React.FC<{ handleOtherSetting4Change: any; otherSetting4: number; handleAutoCalc: any, form:any, initValues:any }> = memo(
	({ handleOtherSetting4Change, otherSetting4, handleAutoCalc, form, initValues }) => {

		const [queryBackStockFlag, setQueryBackStockFlag] = useState(0);

		// 1688快返库存上传设置0关闭1开启
		const handleQueryBackStockFlag = (e: RadioChangeEvent) => {
			const value = e.target?.value;

			setQueryBackStockFlag(value || 0);
			// 如果开启，要给1688快返库存分配上传比例赋值
			if (value == 1) {
				form.setFieldsValue({ queryBackStockPercentage: initValues?.queryBackStockPercentage });
			} else {
				form.setFieldsValue({ queryBackStockPercentage: undefined });
			}
		};

		useEffect(() => {
			if (initValues) {
				setQueryBackStockFlag(initValues?.queryBackStockFlag || 0);
			}
		}, [initValues]);

		const settingOptions = [
			{ name: 'otherSetting1', label: '上传库存数为负值时，默认同步库存数为0', disable: true },
			{
				name: 'otherSetting2',
				label: (
					<>
						计算库存数大于 0 小于 1 时，同步库存数 = 1
						<span>（未勾选时默认同步库存数为0）</span>
					</>
				),
			},
			{
				name: 'otherSetting3',
				label: '当库存数≤',
				extra: (
					<>
						<Form.Item name="otherSetting3Num" noStyle>
							<InputNumber className="r-ml-4 r-mr-4" style={ { width: 60 } } min={ 2 } />
						</Form.Item>
						时，同步库存数＝0
					</>
				),
			}
		];

		const radioOptions = [
			{ value: 1, label: '只同步现货库存' },
			// {
			// 	value: 2,
			// 	label: '按阶梯同步',
			// 	tooltip: <img src={ imgXianHuo } alt="按阶梯同步说明" style={ { width: 200 } } />,
			// },
			{
				value: 3,
				label: '按时效同步',
				tooltip: <img src={ imgNewXianHuo } alt="按时效同步说明" style={ { width: 200 } } />,
			}
		];

		const radioQueryBackStockOptions = [
			{ value: 0, label: '不同步' },
			{ value: 1, label: '同步快返库存' }
		];

		return (
			<div className={ s.row10 }>
				<span>其他设置：</span>
				<div>
					{settingOptions.map(({ name, label, extra, disable }) => (
						<div key={ name } className="r-flex r-ai-c">
							<Form.Item name={ name } valuePropName="checked">
								<Checkbox disabled={ disable }>{label}</Checkbox>
							</Form.Item>
							{extra}
						</div>
					))}
					<div>抖音店铺库存设置（现货+预售模式）</div>
					<Form.Item name="otherSetting4">
						<Radio.Group className="r-ml-12" onChange={ handleOtherSetting4Change }>
							{radioOptions.map(({ value, label, tooltip }) => (
								<Radio key={ value } value={ value }>
									{tooltip ? (
										<Tooltip title={ tooltip } placement="bottom" overlayInnerStyle={ { backgroundColor: 'white', padding: '8px', borderRadius: '4px' } }>
											<span style={ { borderBottom: '1px dashed #ccc', cursor: 'pointer' } }>{label}</span>
										</Tooltip>
									) : (
										label
									)}
								</Radio>
							))}
						</Radio.Group>
					</Form.Item>
					{otherSetting4 === 2 && <PreSaleInput handleAutoCalc={ handleAutoCalc } />}
					{otherSetting4 === 3 && (
						<div className={ s['row11'] }>
							<Form.Item name="inStockTime" label="现货库存" required style={ { marginTop: '0px' } }>
								<InputNumber addonAfter="%" min={ 0 } style={ { width: 100 } } />
							</Form.Item>
							<TimeAllocation />
						</div>
					)}

					<div className="r-mt-16">
						1688快返库存上传设置
						<span className="r-c-gray">（平台限制，仅在女装类目的商品的时候，进行接口同步调用）</span>
					</div>
					<Form.Item name="queryBackStockFlag">
						<Radio.Group className="r-ml-12" onChange={ handleQueryBackStockFlag }>
							{radioQueryBackStockOptions?.map(({ value, label }) => (
								<Radio key={ value } value={ value }>
									{label}
								</Radio>
							))}
						</Radio.Group>
					</Form.Item>

					{
						queryBackStockFlag == 1 && (
							<div className={ s.rowItem }>
								<Form.Item 
									name="queryBackStockPercentage"
									label="快返库存分配比例"
									rules={
										[{ 
											required: true,
											validator: (_:any, value:any) => {
												if (!value && value !== 0) {
													return Promise.reject(new Error('分配比例不能为空'));
												}
												if (Number(value) < 0) {
													return Promise.reject(new Error('分配比例最少为0'));
												}
												if (Number(value) > 100) {
													return Promise.reject(new Error('分配比例最大为100'));
												}
												return Promise.resolve();
											}
										}]
									}
									style={ { marginBottom: 0 } }
								>
									<InputNumber 
										placeholder="" 
										addonAfter="%" 
										controls
										style={ { width: 130 } } 
										// min={ 0 }
										// max={ 100 }
									/>
								</Form.Item>
							</div>
						)
					}

				</div>
			</div>
		);
	}
);

const PreSaleInput: React.FC<{ handleAutoCalc: any }> = ({ handleAutoCalc }) => (
	<div className={ s['preSaleInput'] }>
		<span>现货库存：</span>
		<Form.Item name="inStock">
			<InputNumber addonAfter="%" min={ 0 } max={ 100 } style={ { width: 100 } } onChange={ (v) => handleAutoCalc(v, 'inStock') } />
		</Form.Item>
		<span className="r-ml-24">阶梯库存：</span>
		<Form.Item name="tierStock">
			<InputNumber addonAfter="%" min={ 0 } max={ 100 } style={ { width: 100 } } onChange={ (v) => handleAutoCalc(v, 'tierStock') } />
		</Form.Item>
	</div>
);

const TimeAllocation: React.FC = () => (
	<div>
		<span>时效库存：</span>
		<div>
			<div className={ s['row11-head'] }>
				<span>时效库存</span>
				<span>分配比例</span>
			</div>
			{[5, 7, 10, 15, 20, 25].map((day) => (
				<div key={ day } className={ s['row11-row'] }>
					<span>{day}天内发货</span>
					<span>
						<Form.Item noStyle name={ `preSale${day}` }>
							<InputNumber addonAfter="%" min={ 0 } style={ { width: 140 } } max={ 10000 } />
						</Form.Item>
					</span>
				</div>
			))}
		</div>
	</div>
);

const StockSync: React.FC<IStockSync> = memo(({ onUpdate, initValues, form }) => {
	const [otherSetting4, setOtherSetting4] = useState(1);
	const { groupStockNumOutWay: _groupStockNumOutWay } = userStore?.systemSetting;

	const cItemFicDisStockText = useMemo(() => (
		_groupStockNumOutWay === groupStockNumOutWay.以子货品为准 ? '虚拟可配货库存' : '实际库存'
	), [_groupStockNumOutWay]);

	const handleOtherSetting4Change = useCallback(({ target: { value } }) => {
		setOtherSetting4(value);
		if (value !== 2) {
			onUpdate('inStock', 100);
			onUpdate('tierStock', 0);
		}
		if (value !== 3) {
			onUpdate('inStockTime', 100);
			[5, 7, 10, 15, 20, 25].forEach((i) => onUpdate(`preSale${i}`, 0));
		}
	}, [onUpdate]);

	const handleAutoCalc = useCallback((v: number, t: 'inStock' | 'tierStock') => {
		const roundedValue = v % 1 === 0 ? v : Math.round(v);
		if (t === 'inStock') {
			onUpdate('inStock', roundedValue);
			onUpdate('tierStock', 100 - roundedValue);
		} else if (t === 'tierStock') {
			onUpdate('inStock', 100 - roundedValue);
			onUpdate('tierStock', roundedValue);
		}
	}, [onUpdate]);

	useEffect(() => {
		if (initValues) {
			if (initValues?.otherSetting4) {
				setOtherSetting4(initValues?.otherSetting4);
			}
		}
	}, [initValues]);

	return (
		<>
			<div className={ s.head }>
				<strong>同步库存数</strong>
				<span>同步库存数 = （可配货库存数+在售库存数）</span>
			</div>
			<div className={ s.row7 }>
				<Form.Item label="指定库存" required>
					<div className={ s.des }>可勾选设置<span>（未勾选不计入）</span></div>
				</Form.Item>
			</div>

			<div className={ s.row8 }>
				<CheckboxRow
					title="普通货品库存"
					items={ [
						{ name: 'nItemDisStockFlag', label: '可配货库存' },
						{ name: 'nItemTransStockFlag', label: '采购在途库存' }
					] }
				/>

				<CheckboxRow
					title="组合货品库存"
					items={ [
						{ name: 'cItemFicDisStockFlag', label: cItemFicDisStockText },
						{
							name: 'cItemFicTransStockFlag',
							label: '采购在途库存',
							extra: (
								<Popover content="组合货品在途库存将根据子商品组合规则进行计算">
									<QuestionCircleOutlined />
								</Popover>
							),
						}
					] }
				/>
			</div>

			<StockPercentageInput />

			<OtherSettings 
				form={ form }
				initValues={ initValues }
				handleOtherSetting4Change={ handleOtherSetting4Change } 
				otherSetting4={ otherSetting4 } 
				handleAutoCalc={ handleAutoCalc } 
			/>
		</>
	);
});

export default StockSync;
