.migrationLog {

  .platTitle {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .fromTitle{
      color: #1890FF;
      font-size: 12px;
      line-height: 20px;
    }

    .categoryNames{
      font-size: 12px;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.45);
    }

    .itemId{
      font-size: 12px;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.85);
    }

    .platform{
      line-height: 16px;
    }
  }

  // 目标平台店铺样式
  .targetPlatform {
    .platformInfo {
      display: flex;
      align-items: center;

      .platformIcon {
        flex-shrink: 0;
      }

      .platformName {
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.85);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .platformItemId{
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .errorMessage{
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.85);
    margin-top: 4px;
  }

  .createTimeInfo{
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .createTime{
      color: rgba(0, 0, 0, 0.85);
    }

    .status{
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .actionInfo{
    display: flex;
    align-items: center;
    gap: 8px;
    
    .actionItem{
      color: #1890FF;
      font-size: 12px;
      line-height: 20px;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
        text-underline-offset: 3px;
      }
    }
  }

  // 表格样式优化
  :global(.ant-table) {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
      font-size: 13px;
    }

    .ant-table-tbody > tr > td {
      font-size: 13px;
      padding: 12px 16px;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f8f9fa;
    }
  }

  // Tab相关样式
  .tabContainer {
    width: 100%;
    margin-bottom: 0;
    
    .migrationTabs {
      width: 100%;
      margin-top: 16px;

      :global {

        .ant-tabs-nav {
          margin: 0;
          padding-left: 0;
          .ant-tabs-tab{
            padding: 8px 16px;
          }

          .ant-tabs-tab-active{
            .ant-tabs-tab-btn{
              color: #FD8204;
            }
          }
        }

        .ant-tabs-content-holder {
          display: none; // 隐藏tab内容区域，因为我们只使用tab切换功能
        }

        
      }
    }
  }

  :global{

    .ant-tag{
      border-radius: 2px;
      font-size: 12px;
      padding: 0px 4px;
    }

    .art-table-row{
      td{
        vertical-align: top;
      }
    }
    .art-table-header-row {

      .art-table-header-cell:last-of-type,
      // .art-table-header-cell:nth-child(1),
      .art-table-header-cell:nth-child(2) {
          .resize-handle {
              display: block !important;
          }
      }
    }
  }

  .lineMax1,
  .lineMax2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  .lineMax1 {
    -webkit-line-clamp: 1;
  }

  .lineMax2 {
    -webkit-line-clamp: 2;
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .platTitle {
      .lineMax2 {
        -webkit-line-clamp: 1;
      }
    }
  }
} 