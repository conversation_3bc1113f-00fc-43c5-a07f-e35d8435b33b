import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { Button, Image, Select, Space, Form, Tag, Tooltip, Input, message, Tabs, Popover } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { CopyOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import cs from 'classnames';
import dayjs from 'dayjs';
import { useActivate } from 'react-activation';
import { useLocation } from 'react-router-dom';
import Icon from '@/components/Icon';
import s from './index.module.scss';
import SearchTable from "@/components/SearchTableVirtual";
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import PlatformMultiSelect from '@/components-biz/ShopListSelect/PlatformMultiSelect';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import { clearParams } from '@/utils/stringHelper';
import { DEFAULT_IMG, PLAT_ICON_MAP, PLAT_MAP, PLAT_ALI, PLAT_FXG, PLAT_KS, PLAT_PDD, PLAT_TB, PLAT_TM } from '@/constants';
import { 
	getPlatAndShops, 
	getPlatAndShopsWithFilter,
	getAllPlats, 
	getMultiShops, 
	isSourceScm, 
	getShopName, 
	isAfterSaleSourceScm 
} from '@/components-biz/ShopListSelect/shopListUtils';
import {
	ItemDistributeQueryLogsApi
} from '@/apis/warehouse/migration';
import { ItemDistributeQueryLogsRequest, ItemDistributeQueryLogsResponse } from '@/types/warehouse/migration';
import { DatePickerKey, getCacheDateRange } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import userStore from '@/stores/user';
import { filterPlatformOptions, MIGRATION_STATUS_CONFIG } from './constants';
import useGetState from '@/utils/hooks/useGetState';
import { copyToPaste } from '@/utils';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { getPlatformDetailLink } from '@/pages/AfterSale/TradeList/utils';
import { choiceItemMigrationplatform, itemMigrationplatform } from '../components/ConfigDrawer/interface';
import { getImageThumbnail } from '@/utils/img.scale';

const { Option } = Select;
const { TabPane } = Tabs;

// 定义搬家日志数据类型，根据接口返回的数据结构调整
interface MigrationLogItem {
	itemId: string; // 1688平台商品id
	platformItemId: string; // 平台商品id
	platform: string; // 平台
	fromPlatform: string; // 上游平台
	fromShopName: string; // 上游店铺名
	shopName: string; // 平台店铺名
	title: string; // 去向平台商品名
	picUrl: string; // 1688平台商品预览图
	status: string; // 铺货状态
	toItemStatus: string; // 下游商品状态
	errorMessage?: string; // 异常信息
	createTime: string; // 铺货时间
	taskId: string; // 子任务id
	shopId: string; // 店铺id
	fromTitle: string; // 原商品title
	platformPicUrl: string; // 平台商品预览图
	fromCategoryName?: string; // 发布类目
	[key: string]: any;
}

// 修改搜索参数类型，对应接口请求参数
interface SearchParams {
	pageNo: number;
	pageSize: number;
	platformInfo?: any; // 发布平台信息
	sourcePlatformInfo?: any; // 来源平台信息
	itemId?: string; // 目标平台商品id，对应toItemId
	title?: string; // 目标平台商品名称，对应toTitle
	outerId?: string; // 目标平台商品编码，对应toOuterId
	skuName?: string; // 目标平台规格名称，对应toSkuName
	skuOuterId?: string; // 目标平台规格编码
	sourceItemId?: string; // 来源商品id，对应fromItemId
	fromTitle?: string; // 来源商品名称
	auditStatus?: string; // 审核状态，对应checkStatus
	copyFailReason?: string; // 复制失败原因
	operateTime?: any[]; // 操作时间范围
}

// 默认最近1个月，可以根据业务需要调整默认值
const initialRangeTime = getCacheDateRange(DatePickerKey.warehouse_migrationLog) || [dayjs().subtract(1, 'M').startOf('day'), dayjs().endOf('day')];
const defaultParams = {
	operateTime: initialRangeTime,
	// 根据业务需要可以添加其他默认值
	platformInfo: undefined,
	sourcePlatformInfo: undefined,
	itemId: undefined,
	// title: undefined,
	// outerId: undefined,
	// skuName: undefined,
	// skuOuterId: undefined,
	sourceItemId: undefined,
	fromTitle: undefined,
	// auditStatus: undefined,
	copyFailReason: undefined,
};

// 商品搬家日志页面
const MigrationLog: React.FC = () => {
	const location = useLocation();

	const [form] = Form.useForm();
	const tableRef = useRef<SearchTableRefProps>();
	const [formData, setFormData] = useState<any>({ ...defaultParams });
	const [curSearchParams, setCurSearchParams] = useState({});
	const [activeTab, setActiveTab, getActiveTab] = useGetState<string>('all'); // 当前选中的tab

	const [tabCounts, setTabCounts] = useState({
		all: 0,
		processing: 0,
		success: 0,
		fail: 0 // 修改为fail
	}); // 各状态的数量统计

	// Tab切换处理函数
	const handleTabChange = (key: string) => {
		setActiveTab(key);
		setTimeout(() => {
			// 清理表格数据并重新查询
			tableRef.current?.refresh();
		}, 200);

	};

	// 获取平台图标
	const getPlatformIcon = (platform: string) => {
		const iconType = PLAT_ICON_MAP[platform];
		return iconType ? <Icon type={ iconType } className={ s.platformIcon } size={ 16 } /> : null;
	};

	const goodsMigrationUrl = (row: MigrationLogItem) => {
		switch (row.platform) {
			case PLAT_TB:
				return `https://item.upload.taobao.com/sell/v2/publish.htm?itemId=${row.platformItemId}`;
			case PLAT_FXG:
				return `https://fxg.jinritemai.com/ffa/g/create?product_id=${row.platformItemId}`;
			case PLAT_PDD:
				return `https://mms.pinduoduo.com/goods/goods_list/transfer?id=${row.platformItemId}`;
			case PLAT_ALI:
				return `https://offer-new.1688.com/popular/publish.htm?spm=a26286.8251493.operation.1.221425b2KsaqRE&id=${row.platformItemId}&operator=edit`;
			case PLAT_KS:
				return `https://s.kwaixiaodian.com/zone/goods/config/release/edit/ordinary?itemId=${row.platformItemId}`;
			default:
				return '';
		}
	};

	// 表单配置
	const FormFieldList: FormItemConfig[] = [
		{
			name: "operateTime",
			children: (
				<KdzsDateRangePicker1
					style={ { width: 159 } }
					cacheQuickChoose
					datePickerKey={ DatePickerKey.warehouse_migrationLog }
					useServeTime
					placeholder={ ['操作时间起', '操作时间止'] }
				/>
			),
		},
		{
			name: "platformInfo",
			children: (
				<ShopMultiSelect
					placeholderArray={ ["发布平台", "发布店铺"] }
					isSendPoint
					style={ { width: 159 } }
					filterOptions={ itemMigrationplatform }
					bgHighLight
					size="small"
				/>
			),
		},
		{
			name: "itemId",
			children: <Input placeholder="商品ID" style={ { width: 160 } } className={ formData.itemId ? 'high-light-bg' : '' } allowClear />,
		},
		// {
		// 	name: "title",
		// 	children: <Input placeholder="商品名称" style={ { width: 160 } } className={ formData.title ? 'high-light-bg' : '' } allowClear />,
		// },
		// {
		// 	name: "outerId",
		// 	children: <Input placeholder="商品编码" style={ { width: 160 } } className={ formData.outerId ? 'high-light-bg' : '' } allowClear />,
		// },
		// {
		// 	name: "skuName",
		// 	children: <Input placeholder="规格名称" style={ { width: 160 } } className={ formData.skuName ? 'high-light-bg' : '' } allowClear />,
		// },
		// {
		// 	name: "skuOuterId",
		// 	children: <Input placeholder="规格编码" style={ { width: 160 } } className={ formData.skuOuterId ? 'high-light-bg' : '' } allowClear />,
		// },
		{
			name: "sourcePlatformInfo",
			children: (
				<PlatformMultiSelect
					size="small"
					placeholder="来源平台"
					onChange={ (value) => console.log(value) }
					bgHighLight
					width={ 159 }
					filterOptions={ choiceItemMigrationplatform }
				/>
			),
		},
		{
			name: "sourceItemId",
			children: <Input placeholder="来源商品ID" style={ { width: 160 } } className={ formData.sourceItemId ? 'high-light-bg' : '' } allowClear />,
		},
		{
			name: "fromTitle",
			children: <Input placeholder="来源商品名称" style={ { width: 160 } } className={ formData.fromTitle ? 'high-light-bg' : '' } allowClear />,
		},
		// {
		// 	name: 'auditStatus',
		// 	children: (
		// 		<Select
		// 			className={ cs('r-w-full', formData.auditStatus ? 'high-light-bg' : '') }
		// 			placeholder="审核状态"
		// 			size="small"
		// 			style={ { width: 160 } }
		// 			allowClear
		// 		>
		// 			<Option value="" key={ 0 }>全部</Option>
		// 			<Option value="pending" key={ 1 }>未提交审核</Option>
		// 			<Option value="processing" key={ 2 }>待审核</Option>
		// 			<Option value="success" key={ 3 }>审核通过</Option>
		// 			<Option value="failed" key={ 4 }>审核不通过</Option>
		// 		</Select>
		// 	)
		// },
		{
			name: "copyFailReason",
			children: <Input placeholder="失败原因" style={ { width: 160 } } className={ formData.copyFailReason ? 'high-light-bg' : '' } allowClear />,
		}
	];

	// 表格列配置
	const getColumns = useMemo((): ColumnsType<MigrationLogItem> => {
		const columns: ColumnsType<MigrationLogItem> = [
			{
				title: '序号',
				align: 'center',
				width: 40,
				render: (text, row, index) => {
					return <>{index + 1}</>;
				}
			},
			{
				title: '来源商品名称/类目',
				width: 424,
				minWidth: 120,
				dataIndex: 'fromTitle', // 原商品title
				render: (text, row) => {
					const getLinkHref = getPlatformDetailLink(row.platform, row.itemId);
					return (
						<div className={ cs("r-flex", 'r-gap-8') }>
							<Popover
								placement="right"
								content={ (
									<Image
										src={ row.picUrl || row.platformPicUrl }
										preview={ false }
										style={ { width: 300, height: 300 } }
									/>
								) }
							>
								<Image
									width={ 48 }
									height={ 48 }
									src={ getImageThumbnail({
										noScale: false,
										url: row.picUrl || row.platformPicUrl || DEFAULT_IMG,
										width: 48,
										height: 48,
									}) }
									fallback={ DEFAULT_IMG }
									preview={ false }
								/>
							</Popover>

							<div className={ cs(s.platTitle, "r-flex-1") }>
								<div title={ row.fromTitle } className={ cs(s.lineMax2) }>

									<a
										// eslint-disable-next-line no-script-url
										href={ getLinkHref || "javascript:void(0)" }
										target={ getLinkHref ? "_blank" : "_self" }
										rel="noopener noreferrer"
										className={ cs(s.fromTitle, 'r-l-preWrap') }
									>
										{row.fromTitle}
									</a>
								</div>

								{
									row.fromCategoryName && (
										<div className={ cs(s.categoryNames, "r-mt-4") }>
											源类目：{row.fromCategoryName}
										</div>
									)
								}

								<div className={ cs("r-mt-4", 'r-flex r-ai-c') }>
									<div className={ cs(s.lineMax1, s.itemId) }>
										源ID: {row.itemId}
									</div>
									<CopyOutlined
										onClick={ (event) => {
											event.stopPropagation();
											copyToPaste(row.itemId);
										} }
										className={ cs('r-fc-black-45 r-ml-2', 'r-pointer') }
									/>
								</div>

								<div className={ cs(s.platform, 'r-fc-black-65', 'r-wb-bw', 'r-flex', 'r-ai-c', 'r-mt-4') }>
									<PlatformIcon platform={ row['fromPlatform'] } fontSize={ 16 } />
									<span className="r-fc-black-85">{row.fromShopName}</span>
								</div>
							</div>
						</div>
					);
				},
			},
			{
				title: '目标平台店铺',
				width: 220,
				minWidth: 120,
				dataIndex: 'platform',
				render: (text, row) => {
					return (
						<div className={ s.targetPlatform }>
							<div className={ s.platformInfo }>
								<PlatformIcon platform={ row['platform'] } fontSize={ 16 } />
								<span className={ s.platformName }>{row.shopName}</span>
							</div>
							{
								row.toCategoryName && (
									<div
										className={ cs(s.categoryNames, "r-mt-4") }
										style={
											{	color: 'rgba(0, 0, 0, 0.45)' }
										}
									>
										发布类目：{row.toCategoryName}
									</div>
								)
							}
							{
								row.platformItemId && (
									<div className={ cs("r-mt-4", 'r-flex r-ai-c') }>
										<div className={ cs(s.lineMax1) }>
											商品ID: {row.platformItemId}
										</div>
										<CopyOutlined
											onClick={ (event) => {
												event.stopPropagation();
												copyToPaste(row.platformItemId);
											} }
											className={ cs('r-ml-2', 'r-pointer') }
										/>
									</div>
								)
							}

						</div>
					);
				}
			},
			{
				title: '复制状态',
				width: 400,
				minWidth: 120,
				dataIndex: 'status',
				render: (status, row) => {
					const config = MIGRATION_STATUS_CONFIG[status] || { text: status, color: '#666' };
					return (
						<div>
							<Tag color={ config.color }>
								{config.text}
							</Tag>
							<div className={ s.errorMessage }>{row.errorMessage}</div>
						</div>
					);
				}
			},
			{
				title: '操作时间',
				width: 180,
				dataIndex: 'createTime',
				render: (time, row) => {
					let toItemStatus = '';
					switch (row.toItemStatus) {
						case 'onsale': // 在售中
							toItemStatus = '发布至上架';
							break;
						case 'instock': // 在售中
							toItemStatus = '发布至仓库中';
							break;
						case 'draft': // 在售中
							toItemStatus = '发布至草稿箱';
							break;
						default:
							break;
					}
					return (
						<div className={ s.createTimeInfo }>
							<div className={ s.createTime }>{time}</div>
							{toItemStatus && (
								<div className={ s.status }>{toItemStatus}</div>
							)}

						</div>
					);
				}
			},
			{
				title: '操作',
				width: 144,
				render: (text, row) => {
					// 如果复制成功，显示去该店铺按钮 - 使用接口的原始状态值
					if (row.status === 'success' && row.platformItemId) {
						const plat = PLAT_MAP[row.platform];
						const actions = {
							name: `去${plat}编辑`,
							url: goodsMigrationUrl(row)
						};
						return (
							<div className={ s.actionInfo }>
								<div className={ s.actionItem } onClick={ () => handleViewTarget(actions.url) }>{actions.name}</div>
							</div>
						);
					} else {
						return null;
					}
				}
			}
		];

		return columns;
	}, []);

	// 查看目标店铺操作 - 跳转到平台商品管理页面
	const handleViewTarget = (targetUrl) => {
		window.open(targetUrl, '_blank');
		sendPoint(Pointer.商品_商品搬家日志_去平台编辑);
	};

	// 接口查询函数 - 直接使用tab的key作为status参数
	const fetchMigrationLogList = async(params: SearchParams) => {
		// 处理搜索参数
		for (let key in params) {
			if (typeof params[key] === 'string') {
				params[key] = params[key].trim();
			}
		}

		const { platformInfo, sourcePlatformInfo, ...restParams } = params;
		const { shopId, platform } = await getPlatAndShopsWithFilter(platformInfo, false, itemMigrationplatform);
		const { shopId: sourceShopId, platform: sourcePlatform } = await getPlatAndShopsWithFilter(sourcePlatformInfo, false, itemMigrationplatform);

		// 处理时间范围参数
		let startTime;
		let endTime;
		if (params.operateTime && params.operateTime.length) {
			startTime = params.operateTime[0]?.format('YYYY-MM-DD HH:mm:ss');
			endTime = params.operateTime[1]?.format('YYYY-MM-DD HH:mm:ss');
		}

		// 根据当前选中的tab添加状态过滤 - 直接使用接口状态值
		let status;
		if (getActiveTab() !== 'all') {
			// 直接使用tab的key作为status，无需映射
			status = getActiveTab();
		}

		// 构建接口请求参数
		const requestParams: ItemDistributeQueryLogsRequest = {
			pageNo: params.pageNo,
			pageSize: params.pageSize,
			startTime,
			endTime,
			// 目标平台参数
			toPlatformList: platform,
			toSellerIdList: shopId,
			toItemId: params.itemId,
			// toTitle: params.title, // 目标商品名称
			// toOuterId: params.outerId, // 商品编码
			// toSkuName: params.skuName, // 规格名称
			// skuOuterId: params.skuOuterId, // 规格编码
			// 来源平台参数
			fromPlatformList: sourcePlatform,
			// fromSellerIdList: sourceShopId, // 来源店铺id
			fromItemId: params.sourceItemId,
			fromTitle: params.fromTitle, // 来源商品名称
			// 状态参数 - 直接使用接口状态值
			status,
			// checkStatus: params.auditStatus, // 审核状态
			copyFailReason: params.copyFailReason, // 失败原因
		};

		// 清理空参数
		const cleanedParams = clearParams(requestParams, true);
		setCurSearchParams(cleanedParams);

		// 调用真实接口
		return ItemDistributeQueryLogsApi(cleanedParams);
	};

	// 数据适配器 - 使用接口返回的原始状态值进行统计
	const responseAdapter = (data: ItemDistributeQueryLogsResponse['data']) => {
		const { list = [], total = 0 } = data || {};

		// 统计各状态的数量 - 直接使用接口返回的状态值
		// const statusCounts = list.reduce((counts, item) => {
		// 	const status = item.status;
		// 	if (status === 'processing') counts.processing++;
		// 	else if (status === 'success') counts.success++;
		// 	else if (status === 'fail') counts.fail++;
		// 	return counts;
		// }, { all: total, processing: 0, success: 0, fail: 0 });

		// setTabCounts(statusCounts);

		return {
			list,
			total
		};
	};

	const onFieldsChange = (changedValues, allValues) => {
		console.log('%c [ changedValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues, allValues);
		setFormData(allValues);
	};

	const _onChange = (pagination, filters, sorter, { action }) => {
		// 切换分页需要重置勾选项
		if (action == 'paginate') {
			// setSelectedRows([]);
			// setSelectedRowKeys([]);
		}
	};

	// Tab组件 - 修改tab的key为接口状态值
	const expandContext = (
		<div className={ s.tabContainer }>
			<Tabs
				activeKey={ activeTab }
				onChange={ handleTabChange }
				className={ s.migrationTabs }
				type="card"
				size="small"
			>
				<TabPane
					tab="全部"
					key="all"
				/>
				<TabPane
					tab="正在复制中"
					key="processing"
				/>
				<TabPane
					tab="已成功复制"
					key="success"
				/>
				<TabPane
					tab="复制失败"
					key="fail"
				/>
			</Tabs>
		</div>
	);

	const onReset = () => {
		form.resetFields();
		setFormData({ ...defaultParams });
	};

	useActivate(() => {
		console.log('%c [ 当前组件激活 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', location);
		// 如果平台商品管理调过来重新请求下
		if (location?.state?.reload) {
			tableRef?.current?.refresh();
		}
	});

	return (
		<NormalLayout className={ cs(s.migrationLog, "r-bg-white") }>
			<SearchTable<MigrationLogItem>
				pageSizeId="migrationLog"
				ref={ tableRef }
				form={ form }
				fetchData={ fetchMigrationLogList }
				responseAdapter={ responseAdapter }
				onReset={ onReset }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				rowFormConfig={ {
					defaultParams, // 默认查询项
					formList: FormFieldList,
					colProps: {
						// span: 3
					},
					size: "small",
				} }
				baseTableConfig={ {
					rowKey: "id",
					columns: getColumns,
					cachePgination: true,
					pagination: {
						defaultPageSize: 200,
						pageSizeOptions: [10, 20, 50, 100, 200, 500],
					},
					expandContext,
					expandContextStyle: {
						marginBottom: '0px',
						padding: '0 16px 8px'
					},
					noGap: true,
					innerTableStyle: {
						paddingTop: 0
					},
					isStickyHeader: true,
					stickyTop: 155,
					headerColSet: {
						resizeId: `MigrationLog_width_${userStore?.userInfo?.userId}`,
					},
					onFieldsChange, // 查询项变更
				} }
				onChange={ ({ pageNo, pageSize }) => {
					_onChange({ current: pageNo, pageSize }, undefined, undefined, { action: 'paginate' });
				} }
			/>
		</NormalLayout>
	);
};

export default observer(MigrationLog);
