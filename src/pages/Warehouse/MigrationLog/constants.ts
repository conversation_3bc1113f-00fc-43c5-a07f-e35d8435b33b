import { PLAT_ICON_MAP, PLAT_TB, PLAT_TM, PLAT_PDD, PLAT_FXG, PLAT_KS, PLAT_SPH, PLAT_XHS } from "@/constants";
import { ItemDistributeQueryLogsResponse } from '@/types/warehouse/migration';
import { platform } from '@/types/schemas/common';

export const filterPlatformOptions: platform[] = [PLAT_PDD, PLAT_FXG];

// 搬家状态配置，使用接口中的真实状态值
export const MIGRATION_STATUS_CONFIG = {
	success: { text: '已成功复制', color: '#52C41A' },
	fail: { text: '复制失败', color: '#FF4D4F' },
	processing: { text: '正在复制中', color: '#FAAD14' },
	examine: { text: '审核中', color: '#1890FF' },
};

// 商品搬家日志Mock数据
export const mockMigrationLogResponse: ItemDistributeQueryLogsResponse = {
	success: true,
	errorCode: 0,
	errorMessage: '',
	data: {
		pageNo: 1,
		pageSize: 200,
		total: 25,
		list: [
			{
				itemId: '1688001',
				platformItemId: 'tb_12345',
				platform: 'fxg',
				fromPlatform: 'tb',
				fromShopName: '源头工厂店',
				shopName: '淘宝旗舰店',
				title: '高端商务笔记本电脑 轻薄便携 长续航',
				picUrl: 'https://qimg.xiaohongshu.com/arkgoods/1040g0o031a648ir76u005ntu2890bu268u50ieo',
				status: 'success',
				toItemStatus: 'onsale',
				errorMessage: '',
				createTime: '2024-01-15 10:30:22',
				taskId: 'task_001',
				shopId: 'shop_001',
				formTitle: '商务办公笔记本电脑 高性能 超薄设计',
				platformPicUrl: 'https://img.alicdn.com/imgextra/i2/123/platform1.jpg',
				fromCategoryId: '1001',
				categoryIds: '2001',
				categoryNames: '电脑办公/笔记本',
				parentTaskId: 'parent_001',
				distribSource: '手动铺货',
				subStatus: 1,
				needOptimize: false,
				optimizeMsg: '',
				aliId: 'ali_001',
				aliAppKey: 'app_key_001',
				shopKey: 'shop_key_001',
				taskType: 'MINOR',
				distribItemCount: 1,
				appKeyName: '',
				explosiveOrderFlag: 1,
				userProductPoolFlag: 0,
				noMatchTag: 0
			},
			{
				itemId: '1688002',
				platformItemId: 'pdd_67890',
				platform: 'pdd',
				fromPlatform: 'ali',
				fromShopName: '厂家直销店',
				shopName: '拼多多专营店',
				title: '时尚女装连衣裙 春夏新款 显瘦修身',
				picUrl: 'https://img.pddpic.com/open-gw/2024-04-24/332d47c9-b5d3-482d-a71d-d02a8a96383e.jpeg',
				status: 'processing',
				toItemStatus: 'draft',
				errorMessage: '',
				createTime: '2024-01-15 14:20:11',
				taskId: 'task_002',
				shopId: 'shop_002',
				formTitle: '女装连衣裙 2024春夏新款 韩版显瘦',
				platformPicUrl: 'https://img.alicdn.com/imgextra/i4/456/platform2.jpg',
				fromCategoryId: '1002',
				categoryIds: '2002',
				categoryNames: '女装/连衣裙',
				parentTaskId: 'parent_002',
				distribSource: '批量铺货',
				subStatus: 0,
				needOptimize: true,
				optimizeMsg: '建议优化商品标题，增加关键词覆盖',
				aliId: 'ali_002',
				aliAppKey: 'app_key_002',
				shopKey: 'shop_key_002',
				taskType: 'MINOR',
				distribItemCount: 1,
				appKeyName: '',
				explosiveOrderFlag: 0,
				userProductPoolFlag: 1,
				noMatchTag: 0,
				twoPiecesDiscount: 0.85
			},
			{
				itemId: '1688003',
				platformItemId: 'tm_11111',
				platform: 'tb',
				fromPlatform: 'ali',
				fromShopName: '品牌授权店',
				shopName: '天猫旗舰店',
				title: '智能手机 5G全网通 大容量电池',
				picUrl: 'https://img.alicdn.com/imgextra/i5/789/product3.jpg',
				status: 'fail',
				toItemStatus: 'draft',
				errorMessage: '商品类目不匹配，请重新选择正确的商品类目',
				createTime: '2024-01-15 09:15:33',
				taskId: 'task_003',
				shopId: 'shop_003',
				formTitle: '5G智能手机 高性能芯片 超长续航',
				platformPicUrl: 'https://img.alicdn.com/imgextra/i6/789/platform3.jpg',
				fromCategoryId: '1003',
				categoryIds: '2003',
				categoryNames: '手机通讯/智能手机',
				parentTaskId: 'parent_003',
				distribSource: '智能铺货',
				subStatus: 1,
				needOptimize: false,
				optimizeMsg: '',
				aliId: 'ali_003',
				aliAppKey: 'app_key_003',
				shopKey: 'shop_key_003',
				taskType: 'MINOR',
				distribItemCount: 1,
				appKeyName: '天猫开放平台',
				explosiveOrderFlag: 0,
				userProductPoolFlag: 0,
				noMatchTag: 1,
				subCode: '100045'
			},
			{
				itemId: '1688004',
				platformItemId: 'tb_22222',
				platform: 'fxg',
				fromPlatform: 'ali',
				fromShopName: '工厂直营店',
				shopName: '淘宝C店',
				title: '家居用品 创意装饰摆件 北欧风格',
				picUrl: 'https://img.pddpic.com/garner-api-new/a453004a694cf929052fcb291edb6294.jpg',
				status: 'success',
				toItemStatus: 'instock',
				errorMessage: '',
				createTime: '2024-01-14 16:45:28',
				taskId: 'task_004',
				shopId: 'shop_004',
				formTitle: '北欧风家居装饰品 创意摆件 简约现代',
				platformPicUrl: 'https://img.alicdn.com/imgextra/i8/012/platform4.jpg',
				fromCategoryId: '1004',
				categoryIds: '2004',
				categoryNames: '家居用品/装饰摆件',
				parentTaskId: 'parent_004',
				distribSource: '定时铺货',
				subStatus: 1,
				needOptimize: false,
				optimizeMsg: '',
				aliId: 'ali_004',
				aliAppKey: 'app_key_004',
				shopKey: 'shop_key_004',
				taskType: 'MINOR',
				distribItemCount: 1,
				appKeyName: '淘宝开放平台',
				explosiveOrderFlag: 0,
				userProductPoolFlag: 0,
				noMatchTag: 0
			}
		]
	}
};

// 按状态筛选的Mock数据生成函数
export const getMockDataByStatus = (status?: string): ItemDistributeQueryLogsResponse => {
	const allData = mockMigrationLogResponse.data?.list || [];
	
	let filteredData = allData;
	if (status && status !== 'all') {
		filteredData = allData.filter(item => item.status === status);
	}
	
	return {
		...mockMigrationLogResponse,
		data: {
			...mockMigrationLogResponse.data!,
			total: filteredData.length,
			list: filteredData
		}
	};
};

// 各状态数量统计
export const getMockStatusCounts = () => {
	const allData = mockMigrationLogResponse.data?.list || [];
	
	return {
		all: allData.length,
		processing: allData.filter(item => item.status === 'processing').length,
		success: allData.filter(item => item.status === 'success').length,
		fail: allData.filter(item => item.status === 'fail').length
	};
};