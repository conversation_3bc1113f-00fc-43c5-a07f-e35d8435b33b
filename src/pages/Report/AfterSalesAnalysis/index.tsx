import React, { useEffect, useMemo, useRef, useState } from "react";
import { Button, Layout, Spin, Tabs, message } from "antd";
import dayjs from "dayjs";
import { cloneDeep, isEqual, isString } from "lodash";
import { SettingOutlined } from "@ant-design/icons";
import Search, { EnumQueryTimeType } from "./components/Search";
import { DEFAULT_END_DATE, DEFAULT_START_DATE, EnumChartYAxisOrder, IAfterSalesAnalysisConditionDefaultConfig, Indicator, IndicatorEnum, SALES_ANALYSIS_TASK_KEY, SESSION_CLEAR_SETTIMEOUT, TabEnum, TabPaneItems, afterSalesAnalysisConditionDefaultConfig } from "./constants";
import styles from "./index.module.scss";
import AnalysisTable from "./components/AnalysisTable";
import Charts from "./components/Charts";
import { getPlatAndShops } from "@/components-biz/ShopListSelect/shopListUtils";
import { PLAT_HAND } from "@/constants";
import userStore from "@/stores/user";
import { RefundAnalysisAsyncGetApi } from "@/apis/report/salesAnalysis";
import useGetState from "@/utils/hooks/useGetState";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import img_calculating from '@/assets/image/数据计算中.png';
import { sleep } from "@/utils/util";
import { session } from "@/libs/db";
import { AutoCompleteDataByDate, getDatesBetween, getSalesAnalysisGroupPageTaskKey, getRefundAnalysisOverviewTaskKey } from "./utils";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import SearchRuleSort, { FromPageEnum } from "@/pages/Trade/components/SearchContainer/SearchRuleSort";
import { TradeSearchConditionConfig } from "@/types/trade/search/search";
import { GetRefundGlobalConfigListApi, UpdateRefundGlobalConfigApi } from "@/apis/aftersale/trade";
import { AftersaleGlobalConfig } from "@/pages/AfterSale/TradeList/constants";
import { RequestLoop } from "@/utils/requestLoop";
import LowVersionControlModal from "@/components-biz/LowVersionControlModal";
import { PageNameControlEnum } from "@/components-biz/LowVersionControlModal/constant";
import RedTestComponent from "@/components/TestComponent/RedTestComponent";

const { TabPane } = Tabs;

const defaultPagination = { pageNo: 1, pageSize: 10 };

const defaultSortInfo = { sortType: null, sortValue: null };

const hiddenPaginationTabs = [
	TabPaneItems[TabEnum.按平台].key,
	TabPaneItems[TabEnum.按店铺].key,
	TabPaneItems[TabEnum.按售后原因].key,
	TabPaneItems[TabEnum.按快递公司].key,
	TabPaneItems[TabEnum.按市场档口].key,
	TabPaneItems[TabEnum.按供应商].key
];

const requestDataLoop = new RequestLoop({ intervalTime: 1000, resolveType: 'process', maxRetryCount: 5 });
const SalesAnalysis = () => {
	const { userInfo } = userStore;
	const [loading, setLoading] = useState(false);
	const [requestedDataTabKeys, setRequestedDataTabKeys, getRequestedDataTabKeys] = useGetState([]); // 同一个条件下当前Tab是否已经请求过数据
	const [tabActiveKey, setTabActiveKey, getTabActiveKey] = useGetState<TabEnum>(TabPaneItems[TabEnum.概览].key); // 当前弹窗tabs
	const [chartData, setChartData] = useState(Indicator); // 指标统计数据
	const [indicatorData, setIndicatorData] = useState(Indicator); // 指标统计数据
	const [originFormValues, setOriginFormValues, getOriginFormValues] = useGetState({});
	const [formValues, setFormValues, getFormValues] = useGetState({}); // 把搜索组件中的表单数据保存一下
	const [dataSource, setDataSource] = useState({}); // 每个tab下的表格数据
	const [pagination, setPagination] = useState({}); // 每个tab下的分页数据
	const [searchParams, setSearchParams] = useState({}); // 每个tab下的查询入参数据
	const [sortInfo, setSortInfo] = useState({});
	const [isCalculating, setIsCalculating, getIsCalculating] = useGetState(true);
	const [spinning, setSpinning] = useState(false);
	const [chartDataLoading, setChartDataLoading] = useState(false);
	const [indicatorRequesting, setIndicatorRequesting] = useState(false);
	const [tableRequesting, setTableRequesting] = useState(false);
	const [tableLoading, setTableLoading] = useState(false);
	const [searchSortVisible, setSearchSortVisible] = useState<boolean>(false);
	const [conditionList, setConditionList] = useState<IAfterSalesAnalysisConditionDefaultConfig[]>([]);
	const [stickyHeight, setStickyHeight] = useState(92);

	// 接口请求时，保存请求参数
	const handleSearchParamsBeforeRequest = (params) => {
		const newSearchParams = {
			...searchParams,
			[getTabActiveKey()]: params
		};
		setSearchParams(newSearchParams);
	};

	// 接口请求完以后，处理分页数据
	const handlePaginationAfterRequested = (response) => {
		const total = response.pageNo == 1 ? response.total : pagination[getTabActiveKey()]?.total;
		let newPagination = {
			...pagination,
			[getTabActiveKey()]: {
				current: response.pageNo,
				pageSize: response.pageSize,
				total,
				showSizeChanger: true,
				pageSizeOptions: ["10", "20", "50", "100", "200"],
			}
		};
		if (hiddenPaginationTabs.includes(getTabActiveKey())) {
			newPagination[getTabActiveKey()] = false;
		}

		setPagination(newPagination);
	};

	// 接口请求完以后，处理表格数据
	const handleDataSourceAfterRequested = (response) => {
		const { pageSize, pageNo, list = [] } = response || {};
		const baseNumber = pageSize * (pageNo - 1);

		const tableData = list.map((i, index) => {
			const curSerialNumber = parseInt(baseNumber + index + 1, 10);
			return ({
				...i,
				rowKey: `${tabActiveKey}_index_${curSerialNumber}`
			});
		});
		const newDataSource = {
			...dataSource,
			[getTabActiveKey()]: tableData
		};
		setDataSource(newDataSource);
	};

	// 处理chart数据
	const handleChartData = (list) => {
		// 前端自动补全后端缺失的日期数据，不然的话，图表渲染会出现bug
		const date2ListItem = {};
		const _indicator = cloneDeep(Indicator);
		list?.forEach(item => {
			date2ListItem[item.date] = item;
		});
		const completeData = AutoCompleteDataByDate(DEFAULT_START_DATE, DEFAULT_END_DATE, list);
		completeData.forEach(dataItem => {
			Object.keys(_indicator).forEach(indicatorKey => {
				const curIndicator = {
					date: { value: dataItem.date },
					value: dataItem[indicatorKey],
					fields: _indicator[indicatorKey].title,
					unit: _indicator[indicatorKey].unit,
				};
				if (_indicator[indicatorKey].chartYAxisOrder === EnumChartYAxisOrder.百分比) {
					curIndicator.value = Number((dataItem[indicatorKey] * 100).toFixed(2));
				}
				if (_indicator[indicatorKey].chartYAxisOrder === EnumChartYAxisOrder.笔) {
					curIndicator.value = Number(dataItem[indicatorKey]);
				}
				if (!_indicator[indicatorKey]["data"]) {
					_indicator[indicatorKey]["data"] = [];
				}
				_indicator[indicatorKey]["data"].push(curIndicator);
			});
		});
		setChartData(_indicator);
	};

	const handleSummaryData = (res) => {
		try {
			const _indicatorData = cloneDeep(indicatorData);
			const { data, preData } = res || {};
			Object.values(indicatorData).forEach(indicator => {
				const value = Number(data?.[indicator.key] || 0);
				let valueText:string|number = value;
				const preValue = Number(preData?.[indicator.key] || 0);
				let increased = true;
				let percentage = ((value - preValue) / (preValue || 1)) * 100;
				if (value < preValue) {
					increased = false;
				}
				valueText = value.toFixed(indicator.precision);
				if (indicator.unit === "%") {
					valueText = (value * 100).toFixed(indicator.precision) + "%";
				}
				_indicatorData[indicator.key] = {
					..._indicatorData[indicator.key],
					value,
					valueText,
					preValue,
					increased,
					percentage
				};
			});
			setIndicatorData(_indicatorData);
		} catch (error) {
			console.error('handleSummaryData', error);
		}

	};


	// 查询概览指标数据
	const requestSummaryData = async(params) => {
		const newParams = {
			...params,
			pageSize: null,
			pageNo: null
		};
		delete newParams.queryGroupType;
		delete newParams.sortValue;
		delete newParams.sortType;
		const key = await getRefundAnalysisOverviewTaskKey(newParams);
		setIndicatorRequesting(true);
		const { promise, stop } = await requestDataLoop.run(() => RefundAnalysisAsyncGetApi({ key }));
		promise.then(res => {
			handleSummaryData(res);
		}).catch((err) => {
			console.log("err:::", err);
			message.error("查询概览失败");
		}).finally(() => setIndicatorRequesting(false));

	};

	// 查询图表数据
	const requestChartData = async(params) => {
		setChartDataLoading(true);
		const newParams = {
			...params,
			startTime: DEFAULT_START_DATE,
			endTime: DEFAULT_END_DATE,
			pageSize: null,
			pageNo: null,
		};
		const key = await getSalesAnalysisGroupPageTaskKey(newParams);
		const { promise, stop } = await requestDataLoop.run(() => RefundAnalysisAsyncGetApi({ key }));
		promise.then(res => {
			handleChartData(res.list);
		}).finally(() => setChartDataLoading(false));
	};

	// 查询表格数据
	const requestTableData = async(params) => {
		setTableRequesting(true);
		handleSearchParamsBeforeRequest(params);
		const key = await getSalesAnalysisGroupPageTaskKey(params);
		const { promise, stop } = await requestDataLoop.run(() => RefundAnalysisAsyncGetApi({ key }));
		promise.then(res => {
			handlePaginationAfterRequested(res);
			handleDataSourceAfterRequested(res);
			// handleChartData(res.list);
		}).finally(() => {
			setTableRequesting(false);
			setTableLoading(false);
		});
	};


	const tabOnChange = async(value:string) => {
		// 如果当前tab没有请求过数据或者form的值发生了变化，才允许重新请求数据
		setTabActiveKey(value);
		const requestedDataTabKeys = [...getRequestedDataTabKeys()];
		if (!requestedDataTabKeys.includes(value)) {
			await sleep(1);
			requestedDataTabKeys.push(value);
			setRequestedDataTabKeys(requestedDataTabKeys);
			const params = handleRequestTableDataParamsByGroup();
			requestTableData(params);
		}
	};

	// 格式化表单数据
	const formatFormValues = async(values) => {
		const { dateRange, platformInfo, item, sku, startTime, endTime, queryTimeType, tidList, isExcludeEmptyTrade } = values;
		const { shopId, platform } = await getPlatAndShops(platformInfo, true);
		const params:any = {
			platform,
			sellerId: shopId,
			shortNameIncluding: item,
			skuIncluding: sku,
			queryTimeType,
			startTime,
			endTime,
			isExcludeEmptyTrade,
			tidList: tidList ? tidList.split(",") : undefined
		};
		if (queryTimeType === EnumQueryTimeType.付款时间) {
			params.payTimeEnd = endTime;
			params.payTimeStart = startTime;
		} else {
			params.consignTimeEnd = endTime;
			params.consignTimeStart = startTime;
		}
		if (params.platform.includes(PLAT_HAND)) {
			params.sellerId.push(userInfo.userId);
		}
		return params;
	};

	// 通过tab组装table数据的查询参数
	const handleRequestTableDataParamsByGroup = () => {
		const tabActiveKey = getTabActiveKey();
		const curTabPagination = pagination[tabActiveKey] || defaultPagination;
		const curTabSortInfo = sortInfo[tabActiveKey] || defaultSortInfo;
		const newParams = {
			...getFormValues(),
			...curTabPagination,
			pageSize: curTabPagination.pageSize,
			pageNo: 1, // curTabPagination.current || defaultPagination.pageNo,
			...curTabSortInfo,
			key: session.get(SALES_ANALYSIS_TASK_KEY),
			sortType: "desc",
			sortValue: "payment",
			queryGroupType: TabPaneItems[tabActiveKey].key
		};
		switch (tabActiveKey) {
			// Tab: 概览
			case TabPaneItems[TabEnum.概览].key:
				// newParams.groupByDate = true;
				newParams.sortType = "desc";
				newParams.sortValue = "date";
				break;
				// Tab: 平台
			case TabPaneItems[TabEnum.按平台].key:
				newParams.pageSize = 300;
				newParams.sortType = "desc";
				newParams.sortValue = "payment";
				break;
				// Tab: 店铺
			case TabPaneItems[TabEnum.按店铺].key:
				newParams.pageSize = 300;
				newParams.sortType = "desc";
				newParams.sortValue = "payment";
				break;
				// Tab: 商品
			case TabPaneItems[TabEnum.按平台商品].key:
				newParams.sortType = "desc";
				newParams.sortValue = "payment";
				break;
			case TabPaneItems[TabEnum.按供应商].key:
				newParams.sortType = "desc";
				newParams.sortValue = IndicatorEnum.销售金额;
				break;
			case TabPaneItems[TabEnum.按市场档口].key:
				newParams.sortType = "desc";
				newParams.sortValue = IndicatorEnum.销售金额;
				break;
			default: break;
		}
		return newParams;
	};

	const onSubmit = async(values, isFirst = false) => {
		sendPoint(Pointer.售后分析_点击_查询);
		setLoading(true);
		let requestedDataTabKeys = [...getRequestedDataTabKeys()];
		const formValuesHasChanged = !isEqual(values, getOriginFormValues());
		if (formValuesHasChanged) {
			requestedDataTabKeys = [tabActiveKey];
		} else {
			requestedDataTabKeys.push(tabActiveKey);
		}
		setOriginFormValues(values);
		const formValues = await formatFormValues(values);
		setFormValues(formValues);
		if (getIsCalculating() && !isFirst) {
			setLoading(false);
			return;
		}
		try {
			await sleep(50);
			const params = handleRequestTableDataParamsByGroup();
			console.log("params:::", params, getTabActiveKey());
			if (getTabActiveKey() === TabPaneItems[TabEnum.概览].key) {
				await requestSummaryData(params);
				await requestChartData(params);
			}
			await requestTableData(params);
			setRequestedDataTabKeys(requestedDataTabKeys);
			setLoading(false);
		} catch (e) {
			console.log("e:::", e);
			message.error(e.errorMessage || "查询失败");
			setLoading(false);
		} finally {
			if (isFirst) setIsCalculating(false);
		}
	};

	const onReset = async(values) => {
		onSubmit(values);
	};

	// 表格分页
	const paginationOnChange = (pagination) => {
		const curTabSearchParams = searchParams[getTabActiveKey()];
		const params = {
			...curTabSearchParams,
			pageSize: pagination.pageSize,
			pageNo: curTabSearchParams.pageSize === pagination.pageSize ? pagination.current : 1
		};
		setTableLoading(true);
		requestTableData(params);
	};

	// 表格排序
	const sortOnChange = (sorter) => {
		const curTabSearchParams = searchParams[getTabActiveKey()];
		const sortType = sorter.order === "ascend" ? "asc" : (sorter.order === "descend" ? "desc" : null);
		const params = {
			...curTabSearchParams,
			sortType,
			pageNo: 1
		};
		if (sortType) {
			params["sortValue"] = sorter.field;
		} else {
			params["sortValue"] = null;
		}
		setTableLoading(true);
		requestTableData(params);
	};

	const verifySuccessCb = async() => {
		sendPoint(Pointer.售后分析_点击_下载);
		await downloadCenter({
			requestParams: searchParams[tabActiveKey] || {},
			fileName: '售后分析报表',
			module: ModulesFunctionEnum.售后分析
		});
	};

	// 获取售后分析指标配置
	const getRefundGlobalConfigListApi = async() => {
		let list = null;
		let res = await GetRefundGlobalConfigListApi({ bizEnumList:
			[AftersaleGlobalConfig.售后分析指标配置]
		});
		const config = res.filter((config) => (config.biz === AftersaleGlobalConfig.售后分析指标配置 && config.value))?.[0];
		if (config) list = JSON.parse(config?.value);
		if (!list) {
			list = afterSalesAnalysisConditionDefaultConfig;
		}
		setConditionList([...list]);
	};
	// 更新用户查询条件
	const handleSearchRuleSortOK = (list) => {
		console.log("handleSearchRuleSortOK queryConditionDTOList:", list);
		UpdateRefundGlobalConfigApi([{
			biz: AftersaleGlobalConfig.售后分析指标配置,
			value: JSON.stringify(list)
		}]).then(() => {
			setConditionList([...list]);
			setSearchSortVisible(false);
			message.success(`保存查询条件设置成功`);
		});
	};
	const handleSetSearchRules = async() => {
		setSearchSortVisible(true);
	};

	useEffect(() => {
		sendPoint(Pointer.售后分析页面展现);
		getRefundGlobalConfigListApi();
		onSubmit(getOriginFormValues(), true);
		return () => {
			requestDataLoop.clear();
		};
	}, []);

	useEffect(() => {
		// 如果是概览页面的table切换页面或者切换分页时，不需要整体Loading
		if ([TabPaneItems[TabEnum.概览].key].includes(tabActiveKey) && tableLoading) {
			setSpinning(false);
			return;
		}
		setSpinning(tableRequesting || indicatorRequesting);
	}, [tableRequesting, indicatorRequesting]);

	useEffect(() => {
		// 计算下吸顶的距离
		const topContainer = document.querySelector(`.header-menu`);
		const height = topContainer?.getBoundingClientRect()?.bottom || 92;
		setStickyHeight(height);
	}, []);

	return (
		<div className={ styles.afterSalesAnalysis }>
			<LowVersionControlModal pageName={ PageNameControlEnum.售后分析 } />

			<Layout className="kdzs-section-small">
				<Search onSubmit={ onSubmit } onReset={ onReset } loading={ loading } />
			</Layout>

			{
				isCalculating ? (
					<Layout className="kdzs-section-small">
						<div className={ styles["calculating-container"] }>
							<div>
								<img width="110" src={ img_calculating } alt="数据计算中..." />
							</div>
							<div className={ styles["calculating-text"] }>
								数据计算中，请稍后…
							</div>
						</div>
					</Layout>
				) : (
					<Layout hidden={ isCalculating } className="kdzs-section-small">
						<Tabs
							className={ styles.tab }
							activeKey={ tabActiveKey }
							type="card"
							onChange={ tabOnChange }
							tabBarGutter={ 10 }
							destroyInactiveTabPane={ false }
							animated={ false }
							tabBarExtraContent={ (
								<Button
									onClick={ verifySuccessCb }
									style={ { padding: '0 35px' } }
								>下载
								</Button>
							) }
						>
							{
								Object.values(TabPaneItems).filter(i => !i.hidden).map(i => {
									return (
										<TabPane tab={ i.label } key={ i.key }>
											<Spin spinning={ spinning } wrapperClassName={ styles["spin-wrapper"] }>
												<Button className="r-mb-20" icon={ <SettingOutlined /> } onClick={ handleSetSearchRules }>指标配置</Button>
												<RedTestComponent />
												{tabActiveKey === TabPaneItems[TabEnum.概览].key && (
													<Charts
														indicatorData={ indicatorData }
														conditionList={ conditionList }
														chartData={ chartData }
														chartDataLoading={ chartDataLoading }
														searchParams={ searchParams }
													/>
												)}
												<AnalysisTable
													showSummary={ i.tableSummary }
													loading={ tableLoading }
													key={ tabActiveKey }
													activeKey={ tabActiveKey }
													pagination={ pagination[tabActiveKey] }
													dataSource={ dataSource[tabActiveKey] }
													paginationOnChange={ paginationOnChange }
													sortOnChange={ sortOnChange }
													searchParams={ searchParams[tabActiveKey] }
													conditionList={ conditionList }
													stickyHeight={ stickyHeight }
												/>
											</Spin>

										</TabPane>
									);
								})
							}
						</Tabs>
					</Layout>
				)
			}
			{searchSortVisible
				? (
					<SearchRuleSort
						visible={ searchSortVisible }
						searchConditions={ conditionList }
						handleOk={ handleSearchRuleSortOK }
						handleCancel={ () => { setSearchSortVisible(false); } }
						fromPage={ FromPageEnum.售后分析 }
						title="指标配置"
						tip=" "
					/>
				) : ''}

		</div>
	);

};

export default SalesAnalysis;
