import { useRequest, useToggle } from "ahooks";
import { Button, Checkbox, Col, Form, Modal, Radio, Row, Select, Spin } from "antd";
import { useForm } from "antd/lib/form/Form";
import React, { useEffect, useState } from "react";
import { TradePrintEditPrintNumSettingApi } from "@/apis/report/kddLog";
import {
	TradePrintEditPrintNumSettingRequest,
	TradePrintEditPrintNumSettingResponse
} from "@/types/schemas/report/kddLog";
import memoFn from '@/libs/memorizeFn';
import Pointer from "@/utils/pointTrack/constants";

interface Iprops {
	type?: "link" | "text" | "dashed" | "default" | "ghost" | "primary";
	size?: "small" | "middle" | "large";
}

const PrintNumSet: React.FC<Iprops> = (props) => {
	const { type = 'link', size = 'middle' } = props;
	const [form] = useForm();
	const [visible, { setRight, setLeft }] = useToggle();

	const handleSaveSet = async() => {
		const value = form.getFieldsValue();
		const params:TradePrintEditPrintNumSettingRequest = {
			printNumEmptyType: value.printNumEmptyType,
			printNumEmptySet: value.printNumEmptySet,
		};
		await TradePrintEditPrintNumSettingApi(params);
		memoFn.updateAdvancedSet(params, true);
		setLeft();
	};

	const handleSetPrintNum = async() => {
		// 获取设置
		const res = await memoFn.getAdvancedSet();
		console.log('res', res);
		form.setFieldsValue({
			printNumEmptyType: Number(res.printNumEmptyType) || 1,
			printNumEmptySet: Number(res.printNumEmptySet) || 1,
		});
		setRight();
	};

	return (
		<>
			<Button data-point={ Pointer['报表_打印记录查询_打印序号设置'] } type={ type } size={ size } onClick={ handleSetPrintNum }>
				打印序号设置
			</Button>
			{visible ? (
				<Modal
					centered
					title="打印序号设置"
					maskClosable={ false }
					width={ 480 }
					visible={ visible }
					bodyStyle={ { paddingTop: "12px 24px" } }
					onCancel={ setLeft }
					onOk={ handleSaveSet }
					getContainer={ () => document.body }
				>
					<Form
						form={ form }
						wrapperCol={ { span: 15 } }
						initialValues={ {
							printNumEmptyType: 1,
							printNumEmptySet: 1
						} }
					>
						<Form.Item name="printNumEmptyType" label="清零类型">
							<Select getPopupContainer={ (e) => e.parentElement }>
								<Select.Option key={ 1 } value={ 1 }>
									同时清零
								</Select.Option>
								<Select.Option key={ 2 } value={ 2 }>
									仅清零快递单
								</Select.Option>
								<Select.Option key={ 3 } value={ 3 }>
									仅清零发货单
								</Select.Option>
							</Select>
						</Form.Item>
						<Form.Item name="printNumEmptySet" label="清零设置">
							<Radio.Group>
								<Radio value={ 1 }>不清零<span className="r-c-999 r-ml-10">打印序号不自动清零</span></Radio>
								<Radio value={ 2 }>日清零<span className="r-c-999 r-ml-10">每日零点自动清零</span></Radio>
								<Radio value={ 3 }>月清零<span className="r-c-999 r-ml-10">每月1日零点自动清零</span></Radio>
							</Radio.Group>
						</Form.Item>
					</Form>
				</Modal>
			) : ''}
		</>
	);
};

export default PrintNumSet;
