import { Col, Row, Input } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { cloneDeep } from 'lodash';
import { observer } from 'mobx-react';
import s from './index.module.scss';
import Icon from '@/components/Icon';
import { handleReportDecrypt } from '@/pages/Trade/utils';
import { DecryptFiledEnum } from '@/pages/Trade/components/ListItem/components/Decrypt';
import { PLAT_FXG, PLAT_JD, PLAT_TB, PLAT_KTT } from '@/constants';
import { TradeQueryScanTradeRecordByExNumberApi } from '@/apis/trade';
import userStore from '@/stores/user';

export interface IBottomComProps {
	data: any;
	changeData: Function;
	expanded?:boolean
}
const TableExpandRow = (props: IBottomComProps) => {
	let { data, changeData } = props;
	const { id, expand, expand_detail, ...rest } = data;
	const [inspectData, setInspectData]:any = useState();
	const decryptData = async(type: DecryptFiledEnum[number] = DecryptFiledEnum['全部']) => {
		let newData:any = rest;
		newData.togetherId = newData.togetherId || newData.orderIds;
		const decryptData = await handleReportDecrypt({ ...data, togetherId: data.togetherId || data.orderIds }, type);
		const { receiverAddress, receiverName, receiverPhone, isVirtualNum, buyerNick } = decryptData;
		newData.receiverMobileMask = receiverPhone || newData.receiverMobileMask;
		newData.receiverAddressMask = receiverAddress || newData.receiverAddressMask;
		newData.receiverNameMask = receiverName || newData.receiverNameMask;
		newData.isTruelData = true;
		if (data.platform === PLAT_FXG) {
			newData[`${type}IsDecrypt`] = decryptData[`${type}IsDecrypt`];
		}
		// 快团团、京东特殊处理 将昵称展示为 收件人姓名中的值
		if ([PLAT_JD, PLAT_KTT].includes(newData.platform)) {
			newData.buyerNick = buyerNick || newData?.buyerNick;
		} else if (newData.platform !== PLAT_TB) {
			newData.buyerNick = receiverName || newData.buyerNick;
		}
		changeData(newData);
	};
	const Lock = (type: DecryptFiledEnum[number] = DecryptFiledEnum['全部']) => {
		let isDecrypted = false;
		if (data.platform === PLAT_FXG) {
			isDecrypted = data[`${type}IsDecrypt`];
		} else {
			isDecrypted = data.isTruelData;
		}
		
		return (
			!isDecrypted
				? (
					<span onClick={ async() => { decryptData(type); } }>
						<Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5 } } />
					</span>
				) : <></>
		); 
	};
	useEffect(() => {
		if (props.expanded) {
			TradeQueryScanTradeRecordByExNumberApi(props.data).then(res => {
				setInspectData(res);
			});
		}
	}, [props.expanded]);
	const hasWaveManagePermission = userStore.hasWaveManagePermission;

	return (
		<div className={ s['table-expand-wrapper'] }>
			<div className={ s['table-expand-content'] }>
				<h4 className={ s['expand-title'] }>
					<span>{data.exName}</span>
					<span className="r-ml-8">{data.exNumber}</span>
				</h4>
				<div className={ s['item'] }>
					<h5 className={ s['item-title'] }>
						发货记录
					</h5>
					<div className={ s['detail-log-wrap'] }>
						<Row className={ s['row'] } align="middle">
							<Col className={ s['label'] } span={ 2 }>
								昵称:
							</Col>
							<Col span={ 8 }>
								<Input disabled value={ data.buyerNick } />
							</Col>
						</Row>
						<Row className={ s['row'] } align="middle">
							<Col className={ s['label'] } span={ 2 }>
								收件人:
							</Col>
							<Col span={ 8 }>
								<Input disabled value={ data.receiverNameMask } />
							</Col>
							<Col span={ 1 }>
								{Lock(DecryptFiledEnum['收件人'])}
							</Col>
							<Col className={ s['label'] } span={ 2 }>
								联系电话:
							</Col>
							<Col span={ 8 }>
								<Input disabled value={ data.receiverMobileMask } />
							</Col>
							<Col span={ 1 }>
								{Lock(DecryptFiledEnum['手机号'])}
							</Col>
						</Row>
						<Row className={ s['row'] } align="middle">
							<Col className={ s['label'] } span={ 2 }>
								收件地址:
							</Col>
							<Col span={ 19 }>
								<Input
									disabled
									value={ (data.receiverProvince || '')
									+ (data.receiverCity || '')
									+ (data.receiverCounty || '')
									+ (data.receiverTown || '')
									+ (data.receiverAddressMask || '') }
								/>
							</Col>
							<Col span={ 1 }>
								{Lock(DecryptFiledEnum['地址'])}
							</Col>
						</Row>
						<Row className={ s['row'] }>
							<Col className={ s['label'] } span={ 2 }>
								发货内容:
							</Col>
							<Col span={ 19 }>
								<Input.TextArea style={ { height: '88px' } } value={ data.info } />
							</Col>
						</Row>
						{
							hasWaveManagePermission && (
								<>
									<Row className={ s['row'] }>
										<Col className={ s['label'] } span={ 2 }>
											波次号:
										</Col>
										<Col span={ 4 }>
											<span style={ { display: 'block', textAlign: 'left' } }> { data.waveNo || ''}</span> 
										</Col>
									</Row>
									<Row className={ s['row'] }>
										<Col className={ s['label'] } span={ 2 }>
											箱号:
										</Col>
										<Col span={ 4 }>
											<span style={ { display: 'block', textAlign: 'left' } }> { data.boxNo || ''}</span> 
										</Col>
									</Row>
								</>
							)
						}
						
					</div>
				</div>

				{
					inspectData?.scanTime
						? (
							<div className={ s['item'] }>
								<h5 className={ s['item-title'] }>
									验货记录
								</h5>
								<div className={ s['detail-log-wrap'] }>
									<Row className={ s['row'] } align="middle">
										<Col className={ s['label'] } span={ 2 }>
											扫描时间:
										</Col>
										<Col span={ 6 }>
											{inspectData.scanTime}
										</Col>
										<Col className={ s['label'] } span={ 2 }>
											验货数量:
										</Col>
										<Col span={ 6 }>
											{inspectData.inspectNum}
										</Col>
										<Col className={ s['label'] } span={ 2 }>
											识别码:
										</Col>
										<Col span={ 6 }>
											{inspectData.productIdCodes}
										</Col>
									</Row>
									<Row className={ s['row'] }>
										<Col className={ s['label'] } span={ 2 }>
											验货内容:
										</Col>
										<Col span={ 19 }>
											<Input.TextArea style={ { height: '88px' } } value={ inspectData.inspectProductInfo } />
										</Col>
									</Row>
								</div>
							</div>
						)
						: null
				}
			</div>
		</div>
	);
};

export default observer(TableExpandRow);
