import { Button, Col, Input, Row, Spin, Tag, Tooltip } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import cs from 'classnames';
import { useRequest } from 'ahooks';
import _, { cloneDeep } from 'lodash';
import { CopyOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import s from './index.module.scss';
import { TradePrintGetBgDetailApi } from '@/apis/report/kddLog';
import { ReportKddLogDetail, ReportKddLogItem } from '@/types/schemas/report/kddLog';
import Icon from '@/components/Icon';
import { handleReportDecrypt, scmPackTips } from '@/pages/Trade/utils';
import { copyToPaste, splitFxgTid } from '@/utils';
import event from '@/libs/event';
import { PLAT_FXG, PLAT_JD, PLAT_TB, PLAT_KTT } from '@/constants';
import { DecryptFiledEnum } from '@/pages/Trade/components/ListItem/components/Decrypt';
import { weightUnit } from '@/pages/Index/Settings/System/constants';
import userStore from '@/stores/user';
import { isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import { formatWeightDisplay } from '@/components/Input/InputNumber/WeightInput';

export interface IBottomComProps {
	ydNo: string,
	ydName: string,
	record:ReportKddLogItem
}

const KddTableExpandRow = (props: IBottomComProps) => {
	const { ydNo, ydName, record } = props;
	const [data, setData] = useState<ReportKddLogDetail>({});
	const [errorMsg, setErrorMsg] = useState<string>();
	const hasWaveManagePermission = userStore.hasWaveManagePermission;

	/**
	 * 重新打印
	 * @param record
	 */
	const reprint = _.debounce(() => {
		event.emit("KddLog.reprint", [record], function(da: any) {
			getDetail({
				ydNoList: [ydNo]
			});
		});
	}, 1000, { leading: true, trailing: false });

	const { run: getDetail, loading } = useRequest(TradePrintGetBgDetailApi, {
		manual: true,
		onSuccess: (res) => {
			setData(res.data?.[0]);
			if (!res.success) {
				setErrorMsg(`数据获取失败:${res.errorMessage}`);
			}
		},
		onError: () => {
			setErrorMsg('数据获取失败');
		}
	});
	const decryptData = async(itemData: any, index: number, type: string, decryptType: DecryptFiledEnum[number]) => {
		let newData = cloneDeep(itemData);
		newData.togetherId = newData.togetherId || newData.orderIds;
		const decryptData = await handleReportDecrypt(newData, decryptType);
		if (!decryptData) return;
		const { receiverAddress, receiverName, receiverPhone, isVirtualNum, buyerNick } = decryptData;
		newData.receiverMobileMask = receiverPhone || newData.receiverMobileMask;
		newData.receiverAddressMask = receiverAddress || newData.receiverAddressMask;
		newData.receiverNameMask = receiverName || newData.receiverNameMask;
		newData.isTruelData = true;
		// 快团团、京东特殊处理 将昵称展示为 收件人姓名中的值
		if ([PLAT_JD, PLAT_KTT].includes(newData.platform)) {
			newData.buyerNick = buyerNick || newData?.buyerNick;
		} else if (newData.platform !== PLAT_TB) {
			newData.buyerNick = receiverName || newData.buyerNick;
		}
		if (newData.platform === PLAT_FXG) {
			newData[`${decryptType}IsDecrypt`] = decryptData[`${decryptType}IsDecrypt`];
		}

		let newdata_2 = cloneDeep(data);
		newdata_2[type][index] = newData;
		setData(newdata_2);
	};
	useEffect(() => {
		getDetail({
			ydNoList: [ydNo]
		});
	}, [getDetail, ydNo]);

	const isShowLockIcon = (record: any, type: DecryptFiledEnum[number]) => {
		let isDecrypt = record.isTruelData;
		if (record.platform === PLAT_FXG) {
			isDecrypt = record[`${type}IsDecrypt`];
		}
		return !isDecrypt;
	};

	const Lock = (LockProps: { itemData: any, index: number, type: string, decryptType: DecryptFiledEnum[number] }) => (
		isShowLockIcon(LockProps.itemData, LockProps.decryptType)
			? (
				<span onClick={ async() => {
					if (isSourceScm(LockProps.itemData)) {
						scmPackTips();
						return;
					}
					decryptData(LockProps.itemData, LockProps.index, LockProps.type, LockProps.decryptType);
				} }
				>
					<Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5 } } />
				</span>
			) : <></>
	);
	return (
		<div className={ s['table-expand-wrapper'] }>
			{!loading ? (
				<div className={ s['table-expand-content'] }>
					{errorMsg ? <div className="r-ta-c">errorMsg</div> : ''}
					{data?.exNumber || ydName ? (
						<h4 className={ s['expand-title'] }>
							<span>{ydName}</span>
							<span className="r-ml-8">{data.exNumber}</span>
						</h4>
					) : ''}
					<div className={ cs(s.item) }>
						<h5 className={ s['item-title'] }>
							单号使用记录
						</h5>
						<div className="r-pt-10 r-pl-20">
							{!data?.getYdLogs?.length ? <div className="r-ta-c">无单号使用记录</div> : ''}
							{data?.getYdLogs?.map((item: ReportKddLogItem, index: number) => (
								<Row key={ item.id } className={ s.row }>
									<Col span={ 15 }>
										系统单号: {item.orderIds}<CopyOutlined onClick={ () => { copyToPaste(item.orderIds); } } className={ cs('r-ml-8 r-mr-16', 'r-pointer') } />
										订单编号: {item.ptOrderIds }<CopyOutlined onClick={ () => { copyToPaste(splitFxgTid(item.ptOrderIds)); } } className={ cs('r-ml-8', 'r-pointer') } />
									</Col>
									<Col span={ 3 }>
										{item.buyerNick}
										{item.platform !== PLAT_TB && <Lock itemData={ item } type="getYdLogs" index={ index } decryptType={ DecryptFiledEnum['收件人'] } />}
									</Col>
									<Col span={ 3 }>
										{item.status == '3' ? '回收单号' : '占用单号'}
									</Col>
									<Col span={ 3 }>
										{item.gmtCreated}
									</Col>
								</Row>
							))}
						</div>
					</div>
					<div className={ s['item'] }>
						<h5 className={ s['item-title'] }>
							打印记录({data?.printLogs?.length || 0}次)
						</h5>
						<div className="r-pd-10 r-pl-20">
							{!data?.printLogs?.length ? <div className="r-ta-c">无打印记录</div> : ''}
							{data?.printLogs?.map((item: ReportKddLogItem, index: number) => (
								<div key={ item.id } className={ s['detail-log-wrap'] }>
									<div className={ s['detail-log-left-wrap'] }>
										<Row className={ s['row'] }>
											<Col className={ s['label'] } span={ 2 }>
												昵称:
											</Col>
											<Col span={ 8 }>
												<Input disabled value={ item.buyerNick || '' } />
											</Col>
											<Col span={ 1 }>
												{item.platform !== PLAT_TB && <Lock type="printLogs" itemData={ item } index={ index } decryptType={ DecryptFiledEnum['收件人'] } />}
											</Col>
										</Row>
										<Row className={ s['row'] }>
											<Col className={ s['label'] } span={ 2 }>
												收件人:
											</Col>
											<Col span={ 8 }>
												<Input disabled value={ item.receiverNameMask || '' } />
											</Col>
											<Col span={ 1 }>
												<Lock type="printLogs" itemData={ item } index={ index } decryptType={ DecryptFiledEnum['收件人'] } />
											</Col>
											<Col className={ s['label'] } span={ 2 }>
												联系电话:
											</Col>
											<Col span={ 8 }>
												<Input disabled value={ item.receiverMobileMask || '' } />
											</Col>
											<Col span={ 1 }>
												<Lock type="printLogs" itemData={ item } index={ index } decryptType={ DecryptFiledEnum['手机号'] } />
											</Col>
										</Row>
										<Row className={ s['row'] }>
											<Col className={ s['label'] } span={ 2 }>
												收件地址:
											</Col>
											<Col span={ 19 }>
												<Input
													disabled
													value={ (item.receiverProvince || '')
														+ (item.receiverCity || '')
														+ (item.receiverCounty || '')
														+ (item.receiverTown || '')
														+ (item.receiverAddressMask || '') }
												/>
											</Col>
											<Col span={ 1 }>
												<Lock type="printLogs" itemData={ item } index={ index } decryptType={ DecryptFiledEnum['地址'] } />
											</Col>
										</Row>
										<Row className={ s['row'] }>
											<Col className={ s['label'] } span={ 2 }>
												发货内容:
											</Col>
											<Col span={ 19 }>
												<Input.TextArea style={ { height: '88px' } } value={ item.info || '' } />
											</Col>
										</Row>
									</div>
									<div className={ s['detail-log-right-wrap'] } >
										<dl>
											<dt>打印时间：</dt>
											<dd>{item.gmtCreated || ''}</dd>
										</dl>
										{
											hasWaveManagePermission && (
												<dl>
													<dt>波次号：</dt>
													<dd>{(item.waveNo || '')}</dd>
												</dl>
											)
										}
										
										<dl>
											<dt>打印批次（顺序号）：</dt>
											<dd>{item.batch || ''}{`（${item.batchOrder || ""}/${item.batchCount || ""}）`}</dd>
										</dl>
										<dl>
											<dt>订单编号：</dt>
											<dd>{item.ptOrderIds || ''}</dd>
										</dl>
										<dl>
											<dt>商品总数：</dt>
											<dd>
												<div className="r-flex r-ai-c">
													<span>{item.goodsNum || ''}</span>
													{
														item?.goodsNumGift > 0 && (
															<Tooltip placement="top" title={ (<span>含赠品{item.goodsNumGift}件</span>) }>
																<span className="r-ml-2">
																	<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
																</span>
															</Tooltip>
														)
													}
												</div>
											</dd>
										</dl>
										<dl>
											<dt>重量：</dt>
											<dd>
												{formatWeightDisplay(userStore?.userSetting?.weightUnit === weightUnit.显示kg, item.weight) + (userStore?.userSetting?.weightUnit === weightUnit.显示kg ? '千克' : '克')}
											</dd>
										</dl>
										<dl>
											<dt>实付金额：</dt>
											<dd>{item.payment || ''}元</dd>
										</dl>
										<dl>
											<dt>发件人：</dt>
											<dd>{item.sendName || ''}</dd>
										</dl>
										<dl>
											<dt>操作人：</dt>
											<dd>{item.operatorNick || ''}</dd>
										</dl>
										{index === (data?.printLogs?.length - 1) && item.kddType !== 2 && (
											<dl>
												<Button type="primary" size="small" onClick={ () => { reprint(); } }>重新打印</Button>
											</dl>
										)}
									</div>
								</div>
							))}
						</div>
					</div>
					<div className={ s['item'] }>
						<h5 className={ s['item-title'] }>
							称重记录
						</h5>
						<div className="r-pd-10 r-pl-20">
							{!data?.weighingLogs?.length ? <div className="r-ta-c">无称重记录</div> : ''}
							{data?.weighingLogs?.map((item: ReportKddLogItem, index: number) => (
								<Row key={ item.id } className={ s.row }>
									<Col span={ 12 }>
										{item.gmtModified ?? item.gmtCreated}
									</Col>

									<Col span={ 8 }>
										重量：

										{/* {userStore?.userSetting?.weightUnit == weightUnit.显示kg ? Number((+item.weight / 1000).toFixed(4)) + 'kg' : item.weight + 'g'} */}
										{Number((+item.weight / 1000).toFixed(4)) + 'kg'}
										{item.weightShipFlag ? <Tag color="#FD8204" className="r-ml-5">发货重量</Tag> : ''}
									</Col>
									<Col span={ 4 }>
										{item.operatorNick || ''}
									</Col>
								</Row>
							))}
						</div>
					</div>
					<div className={ s['item'] }>
						<h5 className={ s['item-title'] }>
							发货记录
						</h5>
						<div className="r-pd-10 r-pl-20">
							{!data?.deliveryLogs?.length ? <div className="r-ta-c">无发货记录</div> : ''}
							{data?.deliveryLogs?.map((item: ReportKddLogItem, index: number) => (
								<Row key={ item.id } className={ s.row }>
									<Col span={ 8 }>
										<span className="r-mr-8">系统单号: {item.orderIds || ''}</span>
										订单编号: {item.ptOrderIds || ''}
									</Col>
									<Col span={ 2 }>
										{item.buyerNick || ''}
										{item.platform !== PLAT_TB && <Lock itemData={ item } type="deliveryLogs" index={ index } decryptType={ DecryptFiledEnum['收件人'] } />}
									</Col>
									<Col span={ 2 }>
										{item.receiverNameMask || ''}
										<Lock itemData={ item } type="deliveryLogs" index={ index } decryptType={ DecryptFiledEnum['收件人'] } />
									</Col>
									<Col span={ 6 }>
										{item.receiverAddressMask || ''}
										<Lock itemData={ item } type="deliveryLogs" index={ index } decryptType={ DecryptFiledEnum['地址'] } />
									</Col>
									<Col span={ 3 }>
										发货时间：{item.gmtCreated || ''}
									</Col>
									<Col span={ 3 }>
										操作人：{item.operatorNick || ''}
									</Col>
								</Row>
							))}
						</div>
					</div>
				</div>
			) : <Spin />}
		</div>
	);
};

export default observer(KddTableExpandRow);
