import React, { useCallback, useEffect, useMemo, useRef, useState, memo } from "react";
import { Empty, Layout, Modal, Pagination, Space, Tooltip, Button } from "antd";
import { useBoolean, useRequest, useSetState } from "ahooks";
import dayjs from "dayjs";
import _, { cloneDeep, debounce } from "lodash";
import cs from 'classnames';
import { observer } from "mobx-react";
import message from '@/components/message';
import SearchContainer from "./components/SearchContainer";
import s from "./index.module.scss";
import { ReportKddLogGetColsConfigApi, TradePrintGetBgDetailApi, TradeQueryBgTradeApi, TradeQueryBgTradeOrderCountApi, TradeQueryBgTradeSummary } from "@/apis/report/kddLog";
import { KddLogColConfigItem, ReportKddLogColConfig, ReportKddLogItem, ReportKddLogList, TradeQueryBgTradeRequest } from "@/types/schemas/report/kddLog";
import { ReportKddLogQueryType } from "../constants/common";
import ExpandTable from "@/components/SearchTable/ExpandTable";
import { SortTableSortData } from "@/components/SearchTable/SortTable/SortTable";
import KddTableExpandRow from "./components/KddTableExpandRow";
import KddBottomCom from "./components/BottomCom";
import Icon from "@/components/Icon";
import KddLogColSetCom from "./components/ColSetCom";
import ShowLogistic from "./components/ShowLogistic";
import { handleReportDecrypt, scmPackTips, getTradeFlag, getTradeFlagTag } from '@/pages/Trade/utils';
import Pointer from "@/utils/pointTrack/constants";
import event from '@/libs/event';
import BuyerNickComp from "@/components-biz/BuyerNickComp";
import { isValidTableRowClick } from "@/utils/util";
import { DecryptFiledEnum } from "@/pages/Trade/components/ListItem/components/Decrypt";
import { PLAT_FXG, PLAT_JD, PLAT_SCMHAND, PLAT_TB, PLAT_KTT } from "@/constants";
import { weightUnit } from "@/pages/Index/Settings/System/constants";
import userStore from "@/stores/user";
import { getShopName, isSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import { SendType } from "@/utils/enum/trade";
import Copy from "@/components/Copy";
import KddStatusCheck from '@/components-biz/kddStatusCheck';
import ExpTable from '@/components/SearchTableVirtual/expandTable';
import { formatWeightDisplay } from '@/components/Input/InputNumber/WeightInput';
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";

const isShowLockIcon = (record: ReportKddLogItem, type: DecryptFiledEnum[number]) => {
	let isDecrypt = record.isTruelData;
	if (record.platform === PLAT_FXG) {
		isDecrypt = record[`${type}IsDecrypt`];
	}
	return !isDecrypt;
};


/**
 * 单个回收
 * @param record
 */
const handleBatchRecycleSingle = _.debounce((record:ReportKddLogItem) => {
	const { exNumber, pYdNo } = record;
	if (pYdNo && pYdNo !== exNumber) {
		Modal.confirm({
			title: (
				<div className="r-fs-14 r-fw-400">
					<p >该数据为子母件的子单号，不可单独回收！</p>
					<p>可回收关联的母单号，但母单号回收会同步回收其下的所有子单号，是否继续？</p>
				</div>
			),
			okText: '知道了，继续',
			onOk() {
				record.exNumber = record.pYdNo;
				event.emit("KddLog.recycleWaybill", [record]);
			}
		});
	} else if (pYdNo && pYdNo === exNumber) {
		Modal.confirm({
			title: (
				<div className="r-fs-14 r-fw-400">
					<p>该数据为子母件的母单号，回收母单号即会同步回收其下的所有子单号，是否继续？</p>
				</div>
			),

			okText: '知道了，继续',
			onOk() {

				event.emit("KddLog.recycleWaybill", [record]);
			}
		});
	} else {
		event.emit("KddLog.recycleWaybill", [record]);
	}

}, 1000, { leading: true, trailing: false });

/**
 * 获取列配置数据
 */
const getConfig = async() => {
	let conf:KddLogColConfigItem[];
	let defaultColumnConfig = [];
	let data : ReportKddLogColConfig = {};
	let id;
	try {
		const res = await ReportKddLogGetColsConfigApi();
		defaultColumnConfig = JSON.parse(res.defaultColumnConfig);
		conf = JSON.parse(res.customColumnConfig || res.defaultColumnConfig);
		id = res.id;
	} catch (e) { console.log(e); }
	conf = conf.sort((v1, v2) => v1.index - v2.index);
	return {
		id,
		config: conf,
		defaultConfig: defaultColumnConfig
	};
};

const KddLogPage = () => {
	const [columnsConfig, setColumnsConfig] = useState<KddLogColConfigItem[]>();
	const [columnsId, setColumnsId] = useState();
	const defaultConfigRef = useRef<KddLogColConfigItem[]>();
	const [visible, { setTrue: showTable, setFalse: hideTable }] = useBoolean(false);
	const [mainData, setMainData] = useState<ReportKddLogList>({
		list: [],
		buyerCount: 0,
		orderCount: 0,
	});
	const [selectStatus, setSelectStatus] = useState(0);
	const [searchParams, setSearchParams] = useSetState<TradeQueryBgTradeRequest>({
		queryType: ReportKddLogQueryType['kddLog'],
		platform: '',
		sellerId: '',
		startTime: dayjs().subtract(1, 'M').startOf('days').format("YYYY-MM-DD HH:mm:ss"),
		endTime: dayjs().endOf('days').format("YYYY-MM-DD HH:mm:ss"),
		operatorNick: '',
		status: '',
		receiverProvince: '',
		receiverCity: '',
		pageNo: 1,
		pageSize: 10
	});
	const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
	const [ydStatusNum, setYdStatusNum] = useState({
		summaryNum: '-',
		shippedNum: '-',
		getYdAndNoPrintNum: '-',
		getYdAndUnShipNum: '-',
		printedAndUnShopNum: '-',
		recycledNum: '-',
	});
	const hasWaveManagePermission = userStore.hasWaveManagePermission;

	/**
	 * 根据列配置获取预渲染的列
	 */
	const getColumns = (_conf: KddLogColConfigItem[], selectRow: (id:string) => void, decryptData:(data:any, idnex:number, type?: DecryptFiledEnum[number])=>void) => {
		const render = (text:string, record:ReportKddLogItem) => (<span>{text}</span>);
		const AllColumns = {
			'sellerNick': {
				title: '店铺名',
				dataIndex: 'sellerNick',
				key: 'sellerNick',
				width: 150,
				render: (text: string, record: ReportKddLogItem) => (<span>{!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(record) ? '****' : getShopName({ plat: record?.platform, sellerNick: text }) }</span>),
			},
			'buyerNick': {
				title: "昵称",
				dataIndex: "buyerNick",
				key: 'buyerNick',
				width: 100,
				render: (text: string, record: ReportKddLogItem, index: number) => {
					const ptTid = record.ptOrderIds?.split(',')?.[0] || '';
					return (
						<BuyerNickComp
							tid={ record.encodeTid || '' }
							ptTid={ ptTid }
							encryptuid={ record.buyerOpenUid }
							platform={ record.platform }
							buyerNick={ record.buyerNick }
							sellerId={ record?.sellerId }
						/>
					);
				},
			},
			'receiverAddress': {
				title: '收件地址',
				dataIndex: 'receiverAddressMask',
				key: 'receiverAddressMask',
				width: 200,
				render: (text:string, record:ReportKddLogItem, index:number) => (
					<>
						{text}
						{isShowLockIcon(record, DecryptFiledEnum['地址']) ? (
							<span onClick={ async() => { decryptData(record, index, DecryptFiledEnum['地址']); } }>
								<Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5, fontSize: '12px' } } />
							</span>
						) : <></>}
					</>
				)
			},
			'receiverName': {
				title: '收件人',
				dataIndex: 'receiverNameMask',
				key: 'receiverNameMask',
				width: 100,
				render: (text:string, record:ReportKddLogItem, index:number) => (
					<>
						{text}
						{isShowLockIcon(record, DecryptFiledEnum['收件人']) ? (
							<span onClick={ async() => { decryptData(record, index, DecryptFiledEnum['收件人']); } }>
								<Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5, fontSize: '12px' } } />
							</span>
						) : <></>}
					</>
				)
			},
			'mobile': {
				title: '联系电话',
				dataIndex: 'receiverMobileMask',
				key: 'receiverMobileMask',
				width: 100,
				render: (text:string, record:ReportKddLogItem, index:number) => (
					<>
						{text}
						{isShowLockIcon(record, DecryptFiledEnum['手机号']) ? (
							<span onClick={ async() => { console.log(record); decryptData(record, index, DecryptFiledEnum['手机号']); } }>
								<Icon type="suo" style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5, fontSize: '12px' } } />
							</span>
						) : <></>}
					</>
				)
			},
			'provinceAndCity': {
				title: '省市',
				dataIndex: 'receiverProvince',
				width: 100,
				key: 'receiverProvince',
				render: (text:string, record:ReportKddLogItem) => <span onClick={ () => selectRow(record.id) }> {(record.receiverProvince || '') + (record.receiverCity || '')}</span>
			},
			'exName': {
				title: "快递",
				dataIndex: "exName",
				width: 210,
				key: 'exName',
				render,
			},
			'exNumber': {
				title: "单号",
				dataIndex: "exNumber",
				key: "exNumber",
				width: 150,
				render: (text:string, record:ReportKddLogItem) => (
					<>
						<ShowLogistic ydNo={ text } exCode={ record.exCode }>{text}</ShowLogistic>
						<Copy copyData={ text } />
						{ record.sendType === SendType.重新发货 ? <div className="r-trade-warn-filled">重新发货</div> : ''}
					</>
				)
			},
			'goodsNum': {
				title: '数量',
				dataIndex: "goodsNum",
				key: "goodsNum",
				width: 60,
				render: (text:string, record:ReportKddLogItem) => (
					<div className="r-flex r-ai-c">
						<span>{text}</span>
						{
							record?.goodsNumGift > 0 && (
								<Tooltip placement="top" title={ (<span>含赠品{record.goodsNumGift}件</span>) }>
									<span className="r-ml-2">
										<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
									</span>
								</Tooltip>
							)
						}
					</div>
				),
			},
			'weight': {
				title: "重量",
				dataIndex: "weight",
				key: "weight",
				width: 60,
				render: (text:string, record:ReportKddLogItem) => (
					<span onClick={ () => selectRow(record.id) }>
						{
							formatWeightDisplay(userStore?.userSetting?.weightUnit === weightUnit.显示kg, record.weight) + (userStore?.userSetting?.weightUnit === weightUnit.显示kg ? 'kg' : 'g')
						}
					</span>
				),
			},
			'payment': {
				title: "实付金额",
				dataIndex: 'payment',
				key: 'payment',
				width: 80,
				render,
			},
			'sendName': {
				title: '发件人',
				dataIndex: 'sendName',
				key: 'sendName',
				width: 100,
				render(text, record) {
					return text;
				}
			},
			'sendTime': {
				title: '发货时间',
				dataIndex: 'gmtCreated',
				key: 'gmtCreated',
				width: 150,
				features: { sortable: true },
				render: (text:string, record:ReportKddLogItem) => {
					let comp = null;
					if (record.status == '3') {
						comp = (<i style={ { color: '#84a905' } }>已申请回收</i>);
					} else if (record.status == '2') {
						comp = (<i>{record.gmtCreated || ''}</i>);
					} else if (record.status == '1') {
						if (record.optionType == 4) {
							comp = (<i style={ { color: '#d60000' } } >已占用单号，手工订单线下发货</i>);
						} else {
							comp = (<i style={ { color: '#d60000' } } >已占用单号，但未发货</i>);
						}
					} else if (record.status == '0') {
						comp = (<i style={ { color: '#d60000' } }>已占用单号，但未打印</i>);
					}
					return <div onClick={ () => selectRow(record.id) }>{comp}</div>;
				}
			},
			'operatorNick': {
				title: "操作人",
				dataIndex: "operatorNick",
				key: "operatorNick",
				width: 100,
				render,
			},
			'info': {
				title: '发货内容',
				dataIndex: 'info',
				key: 'info',
				width: 200,
				render,
			},
			// 添加波次号列定义
			...(hasWaveManagePermission ? {
				'waveNo': {
					title: '波次号',
					dataIndex: 'waveNo',
					key: 'waveNo',
					width: 120,
					render: (text: string, record: ReportKddLogItem) => (
						<span>{text || ''}</span>
					),
				},
			} : {}),
			'batch': {
				title: '打印批次（顺序号）',
				dataIndex: 'batch',
				key: 'batch',
				width: 200,
				features: { sortable: true },
				render: (text, record) => {
					return (
						<>
							<span>{text}</span>
							<span>{record.batchOrder ? `（${record.batchOrder || ""}/${record.batchCount || ""}）` : ""}</span>
						</>
					);
				},
			},
			'messageAndMemo': {
				title: '留言备注',
				key: 'messageAndMemo',
				width: 200,
				render: (text:string, record:ReportKddLogItem) => (
					<>
						{record.buyerMsg ? (
							<div onClick={ () => { selectRow(record.id); } }>
								<Icon type="beizhu" style={ { color: '#FEE771' } } />
								<span>{record.buyerMsg}</span>
							</div>
						) : ''}
						{record.sellerMemo ? (
							<div onClick={ () => { selectRow(record.id); } }>
								<span>{record.sellerMemo}</span>
								{getTradeFlag(0, null, record?.sellerFlag)}
								{getTradeFlagTag(record?.sellerFlag, record?.sellerFlagTag)}
							</div>
						) : ''}
					</>
				)
			}
		};
		const isZhiYingKd = (kdType: string) => {
			let kddArr = ['ZJS', 'SF', 'POSTB', 'EMS', 'EYB', 'DBKD', 'FAST', 'POST', 'POST_5000000007756']; // 宅急送，顺丰，EMS标准，EMS快递包裹，德邦快递，快捷，邮政，邮政国内标快
			return kddArr.includes(kdType);
		};
		const cols = [];
		_conf.forEach((item:any) => {
			if (item.ischecked == 1 && AllColumns[item.key]) {
				cols.push(AllColumns[item.key]);
			}
			// item.ischecked == 1 ? cols.push(AllColumns[item.key]) : '';
		});
		cols.push({
			key: 'action',
			width: 75,
			render: (_text: string, record: ReportKddLogItem) => {
				if (record['expand_detail']) return null;
				return (
					<Space size={ 8 } >
						{/* TODO: 回收单号 */}
						{!isZhiYingKd(record?.exCode) && (
							<Tooltip title="回收单号">
								<span
									onClick={ () => { handleBatchRecycleSingle(record); } }
									data-point={ Pointer['报表_底单查询_回收单号'] }
								><Icon style={ { color: '#6dae6d' } } type="huishou2" size={ 20 } pointer hover />
								</span>
							</Tooltip>
						)}
						<ShowLogistic ydNo={ record.exNumber } exCode={ record.exCode } />
					</Space>
				);
			}
		});
		return cols;
	};

	const handleSearch = useCallback(async(newSearchParams: TradeQueryBgTradeRequest) => {
		setSearchParams(newSearchParams);
		handleQueryOrderCount({
			...searchParams,
			...newSearchParams
		});
		showTable();

	}, [setSearchParams, showTable]);


	const onPageChange = useCallback((cur: number) => {
		setSearchParams({
			pageNo: cur,
		});
	}, [setSearchParams]);

	const onShowSizeChange = useCallback((current: number, pageSize: number) => {
		setSearchParams({
			pageSize,
			pageNo: current,
		});
	}, [setSearchParams]);

	const { runAsync: queryLog, loading } = useRequest(TradeQueryBgTradeApi, {
		manual: true
	});
	const { runAsync: queryOrderCount } = useRequest(TradeQueryBgTradeOrderCountApi, {
		manual: true
	});
	const { runAsync: queryTradeQueryBgTradeSummary } = useRequest(TradeQueryBgTradeSummary, {
		manual: true
	});

	const { runAsync: getDetail } = useRequest(TradePrintGetBgDetailApi, { manual: true });

	const handleQueryTradeOfExNumberCount = (newSearchParams) => {
		let params = {
			...searchParams,
			...newSearchParams,
		};

		queryTradeQueryBgTradeSummary(params).then((res) => {
			setYdStatusNum({
				...ydStatusNum,
				...res.data
			});

		}).catch(err => {
			setYdStatusNum({
				summaryNum: '-',
				shippedNum: '-',
				getYdAndNoPrintNum: '-',
				getYdAndUnShipNum: '-',
				printedAndUnShopNum: '-',
				recycledNum: '-',
			});
		});

	};

	const handleQueryOrderCount = (searchParams) => {
		queryOrderCount(searchParams).then(res => {
			setMainData(prev => ({
				...prev,
				orderCount: res?.data || '-'
			}));

		}).catch(err => {
			setMainData(prev => ({
				...prev,
				orderCount: '-'
			}));
		});
	};

	useEffect(() => {
		if (visible) {
			queryLog(searchParams).then((res) => {
				res.data.list = res.data.list.filter(el => el && el);
				console.log('%c [ res.data.list ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res.data.list);
				// 如果是批次排序，再按顺序号排序
				if (searchParams.sortValue === 'batch' && searchParams.sortType) {
					res.data.list.sort((a, b) => {
						// 先按批次排序
						if (a.batch !== b.batch) {
							return searchParams.sortType === 'desc' 
								? (b.batch || '').localeCompare(a.batch || '') 
								: (a.batch || '').localeCompare(b.batch || '');
						}
						// 批次相同，再按顺序号排序
						const aOrder = parseInt(a.batchOrder) || 0;
						const bOrder = parseInt(b.batchOrder) || 0;
						return searchParams.sortType === 'desc' ? bOrder - aOrder : aOrder - bOrder;
					});
				}
				console.log('%c [ res.data.list ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res.data.list);
				setMainData(prev => ({
					...prev,
					...res.data
				}));
			});
		}
	}, [queryLog, searchParams, visible]);

	useEffect(() => {
		userStore.getUserSetting();
		getConfig().then((conf) => {
			setColumnsId(conf.id);
			setColumnsConfig(conf.config);
			defaultConfigRef.current = conf.defaultConfig;
		});
		// handleQueryTradeOfExNumberCount(searchParams)
	}, []);

	useEffect(() => {
		// setAdParams().then((res) => {
		// 	window.Ads.prototype.getAdShow('kddLog');
		// });
	}, []);

	const onSortChange = (params: SortTableSortData) => {
		setSearchParams({
			sortValue: params.sortField,
			sortType: params.sortOrder
		});
	};
	const selectRow = useCallback((id:string) => {
		const keys = [...selectedRowKeys];
		if (keys.includes(id)) {
			keys.splice(keys.indexOf(id), 1);
		} else {
			keys.push(id);
		}
		setSelectedRowKeys(keys);
	}, [selectedRowKeys]);

	const rowSelection = {
		selectedRowKeys,
		onChange: (selectedRowKeys: string[]) => {
			setSelectedRowKeys(selectedRowKeys);
		},
	};
	const decryptData = useCallback(async(data:any, index:number, type: DecryptFiledEnum[number] = DecryptFiledEnum['全部']) => {
		if (isSourceScm(data)) {
			scmPackTips();
			return;
		}
		let newData = cloneDeep(data);
		newData.togetherId = newData.togetherId || newData.orderIds;
		const detail = await getDetail({ ydNoList: [newData.exNumber] });
		newData.tradeEncodeType = detail?.data?.[0]?.getYdLogs?.[0]?.tradeEncodeType;
		const decryptData = await handleReportDecrypt(newData, type);
		// console.log('%c [ decryptData ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', newData, decryptData);
		if (decryptData) {
			const { receiverAddress, receiverName, receiverPhone, isVirtualNum, buyerNick } = decryptData;
			newData.receiverMobileMask = receiverPhone || newData.receiverMobileMask;
			newData.receiverAddressMask = receiverAddress || newData.receiverAddressMask;
			newData.receiverNameMask = receiverName || newData.receiverNameMask;
			newData.isTruelData = true;
			// 快团团、京东特殊处理 将昵称展示为 收件人姓名中的值
			if ([PLAT_JD, PLAT_KTT].includes(newData.platform)) {
				newData.buyerNick = buyerNick || newData?.buyerNick;
			} else if (newData.platform !== PLAT_TB) {
				newData.buyerNick = receiverName || newData.buyerNick;
			}

			if (newData.platform === PLAT_FXG || newData.platform === PLAT_TB) {
				newData[`${type}IsDecrypt`] = decryptData[`${type}IsDecrypt`];
			}
			//	修改对应的table参数
			let list = [...mainData?.list];
			let dataIndex = list.findIndex(item => item.id === data.id);
			list[dataIndex] = newData;
			setMainData({ ...mainData, list });
		} else {
			message.error("解密失败");
		}

	}, [mainData]);

	const columns = useMemo(() => {
		return columnsConfig ? getColumns(columnsConfig, selectRow, decryptData) : [];
	}, [columnsConfig, selectRow, decryptData]);

	const paginationConf = useMemo(() => ({
		showSizeChanger: true,
		pageSizeOptions: ["10", "20", "50", "100", "200", '500', '1000'],
		current: searchParams?.pageNo || 1,
		pageSize: searchParams?.pageSize || 10,
		onShowSizeChange,
		total: mainData?.total || 0,
		onChange: onPageChange,
	}), [mainData?.total, onPageChange, onShowSizeChange, searchParams?.pageNo, searchParams?.pageSize]);


	const emptyText = useMemo(() => (() => {
		return !visible ? (
			<div style={ { width: '100%', height: '200px', textAlign: 'center' } } className={ cs('r-flex', 'r-fd-c', 'r-jc-c', 'r-ai-c') }>
				<Empty
					description={
						<p>请点击查询，查看报表数据</p>
					}
				/>
			</div>
		) : (
			<div style={ { width: '100%', height: '200px', textAlign: 'center' } } className={ cs('r-flex', 'r-fd-c', 'r-jc-c', 'r-ai-c') }>
				<Empty
					image={ Empty.PRESENTED_IMAGE_SIMPLE }
					description={
						<p>暂无数据</p>
					}
				/>
			</div>
		);
	}), [visible]);

	return (
		<div className={ s["main-container"] }>
			<Layout className="kdzs-section-small">
				<div className={ s["title"] }>
					<h3>底单记录查询（最近3个月）</h3>
				</div>
				<KddStatusCheck
					ydStatusNum={ ydStatusNum }
					selectStatus={ selectStatus }
					setSelectStatus={ setSelectStatus }
				/>
				<SearchContainer
					handleSearch={ handleSearch }
					selectStatus={ selectStatus }
					handleQueryTradeOfExNumberCount={ handleQueryTradeOfExNumberCount }
					loading={ loading }
				/>
			</Layout>

			<Layout className="kdzs-section-small" style={ { padding: '20px 24px 60px' } } >
				{mainData?.list?.length ? (
					<div className="r-flex r-jc-sb r-mb-10">
						<span>
							共
							<i className="k-c-cred">{mainData?.total || 0} </i>快递单单号（
							{/* <i className="k-c-cred">{mainData?.orderCount || 0}</i>买家/ */}
							<i className="k-c-cred">{mainData?.orderCount || 0}</i>订单）
						</span>
						<div className="r-flex">
							<Pagination
								className="r-mr-20"
								size="small"
								{
									...paginationConf
								}
							/>
							<KddLogColSetCom colConfig={ columnsConfig } colId={ columnsId } defaultColConfig={ defaultConfigRef.current } setColConfig={ setColumnsConfig } />
						</div>
					</div>
				) : ''}
				<ExpTable
					rowKey="id"
					dataSource={ mainData?.list }
					columns={ columns }
					expandable={ {
						expandedRowRender: (record: ReportKddLogItem) => (
							<KddTableExpandRow ydNo={ record.exNumber } ydName={ record.exName } record={ record } />
						),
					} }
					onExpand={ () => setMainData(pre => {
						const data = cloneDeep(pre);
						return data;
					}) }
					isLoading={ loading }
					components={ { EmptyContent: emptyText } }
					rowSelection={ rowSelection }
					insertPosition={ -1 }
					sort={
						{
							sortKey: '',
							onSortChange
						}
					}
					paginationStyle={ { bottom: 20 } }
				/>
				{/* <ExpandTable
					expandedKeyId="id"
					rowKey="id"
					loading={ loading }
					dataSource={ mainData?.list }
					columns={ columns }
					expandOpenPoint={ Pointer['报表_底单查询_展开列表信息'] }
					expandable={ {
						expandedRowRender: (record:ReportKddLogItem) => <KddTableExpandRow ydNo={ record.exNumber } ydName={ record.exName } record={ record } />,
						expandIconColumnIndex: columns.length,
					} }
					pagination={ {
						position: ['bottomRight'],
						...paginationConf
					} }
					locale={ {
						emptyText
					} }
					onRow={ (record, index) => {
						return {
							onClick: debounce((e) => {
								if (isValidTableRowClick(e)) {
									selectRow(record.id);
								}
							}, 200)
						};
					} }
					onSortChange={ onSortChange }
					rowSelection={ rowSelection }
				/> */}
			</Layout>

			<KddBottomCom list={ mainData?.list } selectedRowKeys={ selectedRowKeys } paginationConf={ paginationConf } />
			{/* 广告弹窗 */}
			{/* <div className="sys_position_replace" data-type="middlePopup" data-page="kddLog" />
			<div className="sys_position_replace rightBottomPopup" data-type="rightBottomPopup" data-page="kddLog" /> */}
		</div>
	);
};


export default observer(KddLogPage);
