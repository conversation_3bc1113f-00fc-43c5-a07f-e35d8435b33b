.search-wrapper {
	display: flex;
	flex-direction: column;
	width: 100%;
	.form-item-empty-trade {
		display: flex;
		align-items: baseline;
		:global {
			.ant-form-item {
				margin-right: 0px;
			}
		}
	}
	.condition-expand-text {
		display: flex;
		align-items: center;
		cursor: pointer;
		color: #fd8204;
	}
	.condition-expand-icon {
		margin-left: 6px;
		transform: rotate(180deg);
		transition: all 200ms;
	}
	.condition-expand-active {
		transform: rotate(0deg);
	}
	.search-form-container {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		// transition: all 100ms;
		height: 0;
		overflow: hidden;
		gap: 8px 0;
	}
	.search-form-container-active {
		height: auto;
		margin-top: 10px;
	}
}

.table {
	:global {
		thead > tr > th {
			background-color: rgba(0, 0, 0, 0.08);
			padding: 10px;
			.ant-table-column-sorters {
				justify-content: flex-start;
				.ant-table-column-title {
					flex: none;
					color: rgba(0, 0, 0, 0.85);
					font-weight: bold;
				}
			}
		}
		tbody > tr > td {
			vertical-align: middle;
		}
		.ant-table-summary {
			td {
				background-color: #ebebeb;
				font-weight: bold;
			}
		}
	}
}

.goods-info-container {
	display: flex;
	align-items: center;
	min-width: 260px;
	overflow: hidden;
	:global {
		.img-container {
			width: 48px;
			border: 0.8px solid rgba(229, 229, 229, 1);
		}

		.info-container {
			flex: 1;
			width: 0;
			margin-left: 6px;
			& > div:not(:first-child) {
				margin-top: 4px;
			}
			.title {
				display: flex;

				.title-content {
					flex: 1;
					width: 0;
					height: 18px;
					line-height: 18px;
					text-align: left;
					span {
						display: inline-block;
						max-width: 100%;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}

				.icon-content {
				}
			}

			.extra {
				display: flex;
				color: rgba(0, 0, 0, 0.45);
				.extra-title {
					// font-weight: bold;

					white-space: nowrap;
				}

				.extra-content {
					// flex: 1;
					// width: 0;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					cursor: pointer;
				}
			}
		}
	}
}
.title-content {
	flex: 1;
	width: 160px;
	height: 18px;
	line-height: 18px;
	text-align: left;
	span {
		display: inline-block;
		max-width: 100%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}

.analysis-modal-title {
	display: flex;
	align-items: center;
	& > div:last-child {
		font-size: 14px;
		color: rgba(0, 0, 0, 0.65);
		font-weight: 400;
		margin-left: 8px;
	}
}