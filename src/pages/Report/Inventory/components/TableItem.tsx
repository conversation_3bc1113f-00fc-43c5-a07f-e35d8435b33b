import React, { useEffect, useMemo, useState, useCallback } from "react";
import { Table, Tooltip } from "antd";
import dayjs from "dayjs";
import { key } from "localforage";
import { DEFAULT_IMG, PLAT_HAND, PLAT_MAP } from "@/constants";
import PlatformIcon from "@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon";
import styles from "./index.module.scss";
import Image from "@/components/Image";
import FieldsPermissionCheck, {
	FieldsPermissionCheckTypeEnum,
	FieldsPermissionEnum
} from "@/utils/permissionCheck/fieldsPermissionCheck";
import { TabEnum } from "../constants";

export const renderPlatform = (_, record) => {
	const { rewrite } = record;

	return (
		rewrite
			? (
				<div style={ { textAlign: 'center' } }>
					{_}
				</div>
			)
			:		(
				<div className="r-flex r-js-c r-ai-c">
					<PlatformIcon platform={ record.platform } />
					<span className="r-bold r-mr-4">
						{record.platform?.toLowerCase() === PLAT_HAND ? "无店铺" : PLAT_MAP[record.platform]}
					</span>
				</div>
			)
	);
};

export const renderShop = (_, record) => {
	const { rewrite } = record;

	return (
		rewrite
			? (
				<div style={ { textAlign: 'center' } }>
					{_}
				</div>
			)
			:		(
				<div className="r-flex r-js-c r-ai-c">
					<PlatformIcon platform={ record.platform } />
					<span className="r-mr-4">{record.sellerNick}</span>
				</div>
			)
	);
};

export const renderSupplier = (val, record) => {
	const { rewrite } = record;
	return (
		rewrite
			? <div style={ { textAlign: 'center' } }>{val}</div>
			:		(
				<div className="r-flex r-js-c r-ai-c">
					<span className="r-mr-4">{val || "无"}</span>
				</div>
			)
	);
};

// 百分比
export const renderRateVal = (val, record) => {
	const { rewrite } = record;
	if (rewrite) return val;
	if (val == 0) {
		return val;
	}
	if (val && isFinite(Number(val))) {
		return Number(val * 100).toFixed(2) + "%";
	}
	return "-";
};

// 金额
export const renderMoney = (val, record) => {
	if (val == 0) {
		return val;
	}
	if (val && isFinite(Number(val))) {
		return Number(val).toFixed(2);
	}
	return "-";
};

// 数量
export const renderCount = (val, record) => {
	if (val == 0) {
		return val;
	}
	if (!val) {
		return "-";
	}
	return val;
};

// 退款金额
export const renderRefundAmount = (_, record) => {
	if (record.refundAmount == 0) {
		return record.refundAmount;
	}
	if (record.refundAmount && isFinite(Number(record.refundAmount))) {
		return Number(record.refundAmount).toFixed(2);
	}
	return "-";
};

// 笔单价
export const renderPaymentAverage = (_, record) => {
	if (record.paymentAverage == 0) {
		return record.paymentAverage;
	}
	if (record.paymentAverage && isFinite(Number(record.paymentAverage))) {
		return Number(record.paymentAverage).toFixed(2);
	}
	return "-";
};

// 销售金额
export const renderPayment = (_, record) => {
	if (record.payment == 0) {
		return record.payment;
	}
	if (record.payment && isFinite(Number(record.payment))) {
		return Number(record.payment).toFixed(2);
	}
	return "-";
};

export const renderSkuItemInfo = (_, record) => {
	const { rewrite } = record;
	return (
		rewrite
			? <div style={ { textAlign: 'center' } }>{_}</div>
			:		(
				<div className={ styles["goods-info-container"] }>
					<div className="img-container">
						<Image src={ record.skuPicPath } />
					</div>
					<div className="info-container">
						<div className="title">
							<div className="title-content">
								<Tooltip title={ record.itemTitle || "暂无规格名称" }>
									<span className="r-pointer">{record.itemTitle || "无"}</span>
								</Tooltip>

							</div>
						</div>
						<div className="extra">
							<span className="extra-title">规格编码：</span>
							<span className="extra-content">{record.outerSkuId}</span>
						</div>
						<div className="extra">
							<span className="extra-title">skuID：</span>
							<span className="extra-content">{record.skuId}</span>
						</div>
					</div>
				</div>
			)
	);
};

export const renderItemInfo = (_, record) => {
	const { rewrite } = record;
	return (
		rewrite
			? <div style={ { textAlign: 'center' } }>{_}</div>
			:		(
				<div className={ styles["goods-info-container"] }>
					<div className="img-container">
						<Image src={ record.picPath } />
					</div>
					<div className="info-container">
						<div className="title">
							<div className="title-content">
								<Tooltip title={ record.itemTitle || "暂无商品名称" }>
									<span className="r-pointer">{record.itemTitle || "无"}</span>
								</Tooltip>
							</div>
							{/* <div className="icon-content"><PlatformIcon platform={ record.platformAlias } /></div> */}
						</div>
						<div className="extra">
							<span className="extra-title">商家编码：</span>
							<span className="extra-content">{record.outerId}</span>
						</div>
						<div className="extra">
							<span className="extra-title">ID：</span>
							<span className="extra-content">{record.numIid}</span>
						</div>
					</div>
				</div>
			)
	);
};
// 货品信息
export const renderSysItemInfo = (_, record) => {
	const { rewrite } = record;
	return (
		rewrite
			? <div style={ { textAlign: 'center' } }>{_}</div>
			:		(
				<div className={ styles["goods-info-container"] }>
					<div className="img-container">
						<Image src={ record.sysSkuPicPath } />
					</div>
					<div className="info-container">
						<div className="title">
							<div className="title-content">
								<Tooltip title={ record.sysItemAlias || "暂无货品简称" }>
									<span className="r-pointer">{record.sysItemAlias || "无"}</span>
								</Tooltip>

							</div>
						</div>
						<div className="extra">
							<span className="extra-title">货品编码：</span>
							<span className="extra-content">{record.sysOuterId}</span>
						</div>
					</div>
				</div>
			)
	);
};

// 货品SKU信息
export const renderSysSkuItemInfo = (_, record) => {
	const { rewrite } = record;
	return (
		rewrite
			? <div style={ { textAlign: 'center' } }>{_}</div>
			:		(
				<div className={ styles["goods-info-container"] }>
					<div className="img-container">
						<Image src={ record.sysSkuPicPath } />
					</div>
					<div className="info-container">
						<div className="title">
							<div className="title-content">
								<Tooltip title={ record.sysSkuName || "暂无货品规格名称" }>
									<span className="r-pointer">{record.sysSkuName || "无"}</span>
								</Tooltip>

							</div>
						</div>
						<div className="extra">
							<span className="extra-title">货品规格编码：</span>
							<span className="extra-content">{record.sysOuterSkuId}</span>
						</div>
					</div>
				</div>
			)
	);
};

// 所属商品
export const renderBelongItem = (_, record) => {
	const { rewrite } = record;
	return (
		rewrite
			? <div style={ { textAlign: 'center' } }>{_}</div>
			:		(
				<div>
					<div className={ styles["title-content"] }>
						<Tooltip title={ record.itemTitle || "暂无商品名称" }>
							<span className="r-pointer">{record.itemTitle || "无"}</span>
						</Tooltip>
					</div>
					<div className="r-flex r-js-c r-ai-c r-mt-8">
						<PlatformIcon platform={ record.platform } />
						<span className="r-mr-4">{record.sellerNick}</span>
					</div>
				</div>
			)
	);
};

// 所属货品
export const renderBelongSysItemInfo = (_, record) => {
	const { rewrite } = record;
	return (
		rewrite
			? <div style={ { textAlign: 'center' } }>{_}</div>
			:		(
				<div className={ styles["goods-info-container"] }>
					<div className="info-container">
						<div className="title">
							<div className="title-content">
								<Tooltip title={ record.sysItemAlias || "暂无货品规格名称" }>
									<span className="r-pointer">{record.sysItemAlias || "无"}</span>
								</Tooltip>
							</div>
						</div>
					</div>
				</div>
			)
	);
};

// 成本相关
export const renderCost = (text, record) => {
	let _ = text || "--";
	if (text?.split(".")?.[1]?.length > 4) {
		_ = (+text)?.toFixed(4);
	} else if (text?.split(".")?.[1]?.length > 2) {
		_ = (+text)?.toFixed(2);
	}
	return (
		<FieldsPermissionCheck
			fieldsPermission={ FieldsPermissionEnum.成本价 }
			type={ FieldsPermissionCheckTypeEnum.仅展示 }
			noPermissionRender={ <div>***</div> }
		>
			<>
				<div className="line-clamp-2" title={ _ }>{_}</div>
			</>
		</FieldsPermissionCheck>
	);
};
// 期初总成本、期末总成本
export const renderAllCost = (text, record) => {
	const _ = text ? (+text)?.toFixed(2) : "--";
	return (
		<FieldsPermissionCheck
			fieldsPermission={ FieldsPermissionEnum.成本价 }
			type={ FieldsPermissionCheckTypeEnum.仅展示 }
			noPermissionRender={ <div>***</div> }
		>
			<>
				<div className="line-clamp-2" title={ _ }>{ _ }</div>
			</>
		</FieldsPermissionCheck>
	);
};

export const renderImage = (url: string) => (<img src={ url || DEFAULT_IMG } alt="图片" style={ { width: 40, height: 40 } } />);

export const getColumns = ({ searchParams, activeKey, columns }) => {
	const sortField = searchParams?.sortValue;
	const sortOrder = searchParams?.sortType === "asc" ? "ascend" : (searchParams?.sortType === "desc" ? "descend" : null);

	const columnsMap = {
		date: {
			title: "日期",
			dataIndex: "showDate",
			key: "showDate",
			fixed: "left",
			width: 120,
			sorter: true,
			render: (value) => {
				return value ? dayjs(value).format('YYYY-MM-DD') : '--';
			}
		},
		platform: {
			title: "平台",
			dataIndex: "platform",
			fixed: "left",
			width: 120,
			sorter: true,
			render: renderPlatform
		},
		shop: {
			title: "店铺",
			dataIndex: "shop",
			fixed: "left",
			width: 160,
			render: renderShop
		},
		itemInfo: {
			title: "商品信息",
			dataIndex: "itemInfo",
			fixed: "left",
			width: 280,
			render: renderItemInfo
		},
		platformShop: {
			title: "平台店铺",
			dataIndex: "platformShop",
			fixed: "left",
			width: 160,
			render: renderShop
		},
		refundReason: {
			title: "售后原因",
			dataIndex: "refundReason",
			width: 200,
			fixed: "left",
			render(_, record) {
				const { rewrite } = record;
				return rewrite ? <div style={ { textAlign: 'center' } }>{_}</div> : _;
			}
		},
		waybillName: {
			title: "快递公司",
			dataIndex: "waybillName",
			fixed: "left",
			width: 140,
			render(_, record) {
				const { rewrite } = record;
				return rewrite ? <div style={ { textAlign: 'center' } }>{_}</div> : _;
			}
		},
		skuInfo: {
			title: "SKU信息",
			dataIndex: "skuId",
			fixed: "left",
			width: 280,
			render: renderSkuItemInfo
		},
		sysItemInfo: {
			title: "货品信息",
			dataIndex: "sysItemInfo",
			fixed: "left",
			width: 280,
			render: renderSysItemInfo
		},
		belongItem: {
			title: "所属商品",
			dataIndex: "belongItem",
			fixed: "left",
			width: 180,
			render: renderBelongItem
		},
		sysItemSkuInfo: {
			title: "货品SKU信息",
			dataIndex: "sysItemSkuInfo",
			fixed: "left",
			width: 280,
			render: renderSysSkuItemInfo
		},
		belongSysItem: {
			title: "所属货品",
			dataIndex: "belongSysItem",
			fixed: "left",
			width: 280,
			render: renderBelongSysItemInfo
		},
		sysSkuName: {
			title: "货品规格名称",
			dataIndex: "sysSkuName",
			key: "sysSkuName",
			width: 120,
			sorter: true,
			render: (_, record) => {
				return _ || '--';
			}
		},
		supplierName: {
			title: "供应商",
			dataIndex: "supplierName",
			key: "supplierName",
			fixed: "left",
			width: 180,
			sorter: true,
			render: renderSupplier
		},
		costPrice: {
			title: "成本价",
			dataIndex: "costPrice",
			key: "costPrice",
			width: 120,
			sorter: true,
			render: renderCost
		},
		price: {
			title: "售价",
			dataIndex: "price",
			key: "price",
			width: 120,
			sorter: true,
			render: (_, record) => {
				return <div>{_ || '--'}</div>;
			}
		},
		allCostStart: {
			title: "期初总成本",
			dataIndex: "allCostStart",
			key: "allCostStart",
			width: 120,
			sorter: true,
			render: renderAllCost
		},
		allCostEnd: {
			title: "期末总成本",
			dataIndex: "allCostEnd",
			key: "allCostEnd",
			width: 120,
			sorter: true,
			render: renderAllCost
		},
	};

	// 自动为图片字段加上 render
	let columnsWithImageRender = columns.map(col => {
		 if (columnsMap[col.dataIndex]) {
			return columnsMap[col.dataIndex];
		}
		return col;
	});

	// 如果是按天展示，添加日期列
	if (searchParams?.queryType === "BY_DAY") {
		columnsWithImageRender = [columnsMap.date, ...columnsWithImageRender.map(item => {
			return {
				...item,
				sorter: false
			};
		})];
	}
	console.log(columnsWithImageRender);
	return columnsWithImageRender;
};
