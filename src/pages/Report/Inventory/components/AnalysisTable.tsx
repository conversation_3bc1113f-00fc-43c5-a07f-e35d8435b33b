import React, { useEffect, useMemo, useState, useCallback, ReactNode } from "react";
import { Table, Tooltip, Radio, Popover, Image } from "antd";
import { observer } from "mobx-react";
import { DEFAULT_IMG, PLAT_HAND, PLAT_MAP } from "@/constants";
import { EnumChartYAxisOrder, IAfterSalesAnalysisConditionDefaultConfig, Indicator, TabEnum, defaultColumns } from "../constants";
import Icon from "@/components/Icon";
import { getRateNum } from "../utils";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { getColumns, renderCount, renderMoney, renderRateVal } from "./TableItem";
import { BasicTable } from '@/components/SearchTableVirtual';
import { local } from "@/libs/db";
import styles from "./index.module.scss";
import TestComponent from "@/components/TestComponent/RedTestComponent";

interface Props {
	activeKey:string; // 当前tab
	pagination?:{}; // 分页信息
	dataSource:[]; // antd.table.dataSource
	paginationOnChange?:(v:{[k:string]:any})=>void; // 分页变化时
	sortOnChange?:(v:{[k:string]:any})=>void; // 排序变化时
	searchParams?:{[k:string]:any}; // 当前表格数据来源的请求参数（主要用于趋势分析数据请求、还有表格排序的高亮展示）
	loading?:boolean;
	showSummary?:boolean; // 是否展示合计行
	stickyHeight?:number, // 吸顶距离
}

export const INVENTORY_SKU_PIC_CONFIG = "INVENTORY_SKU_PIC_CONFIG";
export const INVENTORY_SKU_PIC_PREVIEW_CONFIG = "INVENTORY_SKU_PIC_PREVIEW_CONFIG";
const picUrlWidth = {
	"0": 66,
	"1": 66,
	"2": 84,
};

const AnalysisTable = (props:Props) => {
	const {
		activeKey,
		loading,
		pagination,
		dataSource,
		paginationOnChange,
		searchParams,
		sortOnChange,
	} = props;
	if (pagination) pagination['align'] = 'end';

	const [columns, setColumns] = useState([]);
	const [_loading, setLoading] = useState(false);
	const [skuPicConfig, setSkuPicConfig] = useState(local.get(INVENTORY_SKU_PIC_CONFIG) || 1);
	const [skuPicPreviewConfig, setSkuPicPreviewConfig] = useState(local.get(INVENTORY_SKU_PIC_PREVIEW_CONFIG) || 1);

	const onSkuPicConfigChange = (value) => {
		local.set(INVENTORY_SKU_PIC_CONFIG, value);
		setSkuPicConfig(value);
	};

	const onSkuPicPreviewConfigChange = (value) => {
		local.set(INVENTORY_SKU_PIC_PREVIEW_CONFIG, value);
		setSkuPicPreviewConfig(value);
	};

	const handleColumnsByTabActiveKey = (activeKey) => {
		const columns = getColumns({ searchParams, activeKey, columns: defaultColumns[activeKey] });
		columns.forEach(item => {
			if (item.dataIndex === 'picUrl') {
				item.render = renderImage;
				item.title = skuPicTitle();
				item.width = picUrlWidth[skuPicConfig];
			}
		});
		setColumns(columns);
	};

	const tableOnChange = (pagination, filters, sorter, { action }) => {
		if (action === 'paginate') {
			paginationOnChange?.(pagination);
		}
		if (action === "sort") {
			sortOnChange?.(sorter);
		}
	};

	useEffect(() => {
		setLoading(loading);
	}, [loading]);

	useEffect(() => {
		console.log(searchParams);
		handleColumnsByTabActiveKey(activeKey);
	}, [activeKey, searchParams, skuPicConfig, skuPicPreviewConfig]);

	const [sortKeys, setSortKeys] = useState<string[] | '-1'>([]);
	const [defaultSort, setDefaultSort] = useState('');
	const [defaultSortOrder, setDefaultSortOrder] = useState('ascend');

	const imgPopover = (img, width = 80, height = 80) => {
		const sizeObj = {
			"0": 300,
			"1": 500,
			"2": 800,
		};
		const popContent: ReactNode = (
			<div>
				<Image width={ sizeObj[skuPicPreviewConfig] } height={ sizeObj[skuPicPreviewConfig] } src={ img || DEFAULT_IMG } fallback={ DEFAULT_IMG } preview={ false } />
			</div>
		);
		return (
			<Popover placement="right" content={ popContent }>
				<Image
					width={ width }
					height={ height }
					src={ img || DEFAULT_IMG }
					fallback={ DEFAULT_IMG }
					preview={ false }
				/>
			</Popover>
		);
	};

	const renderImage = (url: string) => {
		const sizeObj = {
			"0": 32,
			"1": 48,
			"2": 64,
		};
		return imgPopover(url || DEFAULT_IMG, sizeObj[skuPicConfig], sizeObj[skuPicConfig]);
	};

	const skuPicTitle = useCallback(() => {
		const settingArr = [{
			label: '小图',
			value: 0,
		}, {
			label: '中图',
			value: 1,
		}, {
			label: '大图',
			value: 2,
		}];
		const content = (
			<div className="r-pd-2">
				<div style={ { display: 'flex' } }>
					<div className="r-mb-8">缩略图展示：</div>
					<Radio.Group
						value={ skuPicConfig }
						onChange={ (e) => onSkuPicConfigChange(e.target.value) }
					>
						<div style={ { display: 'flex', flexDirection: 'row' } }>
							{settingArr.map(item => (
								<Radio key={ item.value } value={ item.value } style={ { marginRight: '8px' } }>{item.label}</Radio>
							))}
						</div>
					</Radio.Group>
				</div>
			
				<div style={ { display: 'flex' } }>
					<div className="r-mb-2">预览图展示：</div>
					<Radio.Group
						value={ skuPicPreviewConfig }
						onChange={ (e) => onSkuPicPreviewConfigChange(e.target.value) }
					>
						<div style={ { display: 'flex', flexDirection: 'row' } }>
							{settingArr.map(item => (
								<Radio key={ item.value } value={ item.value } style={ { marginRight: '8px' } }>{item.label}</Radio>
							))}
						</div>
					</Radio.Group>
				</div>
			</div>
		);
		const title = (
			<div className="r-flex r-ai-c">
				图片
				<Popover placement="bottom" title={ null } content={ content } trigger="click">
					<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
				</Popover>
			</div>
		);
		return title;
	}, [skuPicConfig, skuPicPreviewConfig]);

	// Update config to lock the first column in "概览"
	const config = useMemo(() => ({
		[TabEnum.按货品]: { filterIndices: ["picUrl", "sysItemAlias", "showDate", "supplierName", "sysSkuName", "skuOuterId"], defaultSort: "sysItemAlias" },
		[TabEnum.按货品SKU]: { filterIndices: ["picUrl", "sysItemAlias", "showDate", "supplierName", "sysSkuName", "skuOuterId"], defaultSort: "sysItemAlias" },
	}), []);

	/**
	 * Processes columns by adding a `lock` property based on `filterIndices`
	 * and aligns locked columns to the start.
	 * @param columns - The array of columns to process.
	 * @param filterIndices - Indices of columns to lock.
	 * @returns Processed columns with `lock` and `align` properties.
	 */
	const processColumns = useMemo(() => {
		return columns.map((column, index) => ({
			...column,
			lock: config[activeKey].filterIndices.includes(column.dataIndex),
			align: config[activeKey].filterIndices.includes(column.dataIndex) ? 'start' : 'end'
		}));
	}, [searchParams, columns]);

	// Update sort keys whenever the activeKey or columns change
	useEffect(() => {
		// Keep the first column sortable in "概览" by excluding it from filterIndices check here
		const sorts = processColumns.filter(_ => _.sorter).map(i => i.key);
		console.log(sorts);
		setSortKeys(sorts.length ? sorts : '-1');
	}, [processColumns, activeKey, config, searchParams]);

	// Update default sort field and order based on activeKey
	useEffect(() => {
		const { defaultSort, defaultSortOrder } = config[activeKey] || {};
		setDefaultSort(searchParams?.queryType === "BY_SUMMARY" ? defaultSort : "showDate");
		setDefaultSortOrder(defaultSortOrder || '');
		setTimeout(() => {
			const paths = document.querySelector('path[fill="#23A3FF"]');
			paths?.setAttribute('fill', '#FF5733');
		}, 0);
	}, [activeKey, config, searchParams]);

	return (
		<>
			<BasicTable
				rowKey="rowKey"
				className={ styles.table }
				columns={ processColumns }
				dataSource={ dataSource || [] }
				pagination={ pagination }
				onChange={ tableOnChange }
				loading={ loading }
				sorts={ sortKeys }
				defaultSort={ defaultSort }
				sticky={ { stickyTop: props.stickyHeight } }
				paddingBottom={ 28 }
				scroll={ { x: 'max-content' } }
			/>
		</>

	);
};

export default observer(AnalysisTable);
