import React, { useEffect, useLayoutEffect, useState } from "react";
import { HistoryOutlined, UpOutlined } from "@ant-design/icons";
import { Button, Checkbox, Form, Input, Select, Tooltip, Radio } from "antd";
import dayjs from "dayjs";
import { show } from "@ebay/nice-modal-react";
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import QuickDatePicker from "./QuickDatePicker";
import styles from "./index.module.scss";
import Icon from "@/components/Icon";
import { useStores } from '@/stores/tool';
import { sleep } from "@/utils/util";
import { RefundAnalysisUpdateTimeApi, SalesAnalysisUpdateTimeApi } from "@/apis/report/salesAnalysis";
import InputMulti from "@/components/Input/InputMulti";
import SupplierSelect from '@/components-biz/SupplierSelect';
import { BrandSelect } from "@/components-biz/Product/Brand";

interface Props {
	loading?:boolean,
	onSubmit:(any)=>void
	onReset:(any)=>void
}
const { Option } = Select;

const initialValues = {
	tradeStatus: null,
	queryType: "BY_SUMMARY",
};

const stockTypeOptions = [
	{ value: 0, label: "未变动" },
	{ value: 1, label: "有变动" }
];

const Search = (props:Props) => {
	const { loading, onSubmit, onReset } = props;
	const groupStore = useStores('GroupStore');
	const { groupList } = groupStore;
	const [form] = Form.useForm();

	// 格式化表单数据
	const formatFormValues = (formValus) => {
		const [startDate, endDate] = formValus.dateRange;
		const validValues = {
			...formValus,
			startTime: dayjs(startDate).startOf("d").format("YYYY-MM-DD HH:mm:ss"),
			endTime: dayjs(endDate).endOf("d").format("YYYY-MM-DD HH:mm:ss"),
		};
		delete validValues.dateRange;
		return validValues;
	};

	const _onSubmit = () => {
		form.validateFields().then(values => {
			const validValues = formatFormValues(values);
			onSubmit?.(validValues);
		});
	};

	const _onReset = async() => {
		form.resetFields();
		await sleep(1);
		const formValues = form.getFieldsValue();
		const validValues = formatFormValues(formValues);
		onReset?.(validValues);
	};

	const quickTypeOnChange = () => {
		_onSubmit();
	};

	// 监听呈现方式变化
	const handleQueryTypeChange = () => {
		_onSubmit();
	};

	// const handleDataUpdateTime = () => {
	// 	RefundAnalysisUpdateTimeApi().then(res => setDataUpdateDate(res));
	// };

	// useEffect(() => {
	// 	handleDataUpdateTime();
	// }, []);

	useLayoutEffect(() => {
		_onSubmit();
		groupStore.getGroupList(true, 'custom');
	}, []);

	return (
		<Form form={ form } size="small" layout="inline" initialValues={ initialValues }>
			<div className={ styles["search-wrapper"] }>
				<div className="r-flex r-ai-c r-jc-sb">
					<div className="r-flex r-ai-c">
						<Form.Item name="dateRange">
							<QuickDatePicker quickTypeOnChange={ quickTypeOnChange } />
						</Form.Item>
						<Form.Item name="queryType" label="呈现方式">
							<Radio.Group onChange={ handleQueryTypeChange }>
								<Radio value="BY_SUMMARY">区间汇总</Radio>
								<Radio value="BY_DAY">按天展示</Radio>
							</Radio.Group>
						</Form.Item>
						<div style={ { color: "#8c8c8d" } }>
							<HistoryOutlined className="r-m-lr-4" />
							数据更新至&nbsp;{dayjs().add(-1, 'd').format("YYYY-MM-DD")}
						</div>
					</div>
				</div>
				<div className={ `${styles["search-form-container"]} ${styles["search-form-container-active"]}` }>
					<Form.Item name="alias">
						<Input placeholder="简称" style={ { width: 168 } } />
					</Form.Item>
					<Form.Item name="skuOuterId">
						<Input placeholder="货品规格编码" style={ { width: 168 } } />
					</Form.Item>
					<Form.Item name="sysSkuName">
						<Input placeholder="货品规格名称" style={ { width: 168 } } />
					</Form.Item>
					<Form.Item name="supplierId">
						<SupplierSelect
							allowClear
							style={ { width: 160 } }
							size="small"
							callBackProp="id"
						/>
					</Form.Item>
					<Form.Item name="classifyIdList">
						<Select
							mode="multiple"
							showSearch
							showArrow
							allowClear
							placeholder="分类"
							optionFilterProp="children"
							style={ { minWidth: 160, maxWidth: 300 } }
							size="small"
							filterOption={ (input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase()) }
							options={
								groupList.map(item => ({ value: item.classifyId, label: item.classifyName }))
							}
						/>
					</Form.Item>
					<Form.Item name="brandIdList">
						<BrandSelect
							mode="multiple"
							showArrow
							filterOption={ (input, option) => option.children["toLowerCase"]().indexOf(
								input.toLowerCase()
							) >= 0 }
							maxTagCount={ 3 }
							maxTagTextLength={ 3 }
							allowClear
							size="small"
							style={ { width: 160 } }
						/>
					</Form.Item>
					<Form.Item name="stockChangeFlag">
						<Select
							showSearch
							showArrow
							allowClear
							placeholder="库存变动情况"
							style={ { minWidth: 160, maxWidth: 300 } }
							size="small"
							options={ stockTypeOptions }
						/>
					</Form.Item>
					<Form.Item >
						<Button onClick={ _onSubmit } type="primary" loading={ loading }>查询</Button>
					</Form.Item>
					<Form.Item >
						<Button onClick={ _onReset }>重置</Button>
					</Form.Item>
					{/* <div className={ styles['form-item-empty-trade'] }>
						<Form.Item name="isExcludeEmptyTrade" valuePropName="checked" className="extra-checkbox">
							<Checkbox>排除已删除货品</Checkbox>
						</Form.Item>
						<Tooltip title="即报表统计时不包含已删除货品">
							<span style={ { color: "#ccc" } }>
								<Icon type="wenhao-xian" size={ 15 } />
							</span>
						</Tooltip>
					</div> */}
				</div>
			</div>
		</Form>
	);

};

export default Search;
