import dayjs from "dayjs";

export enum TabEnum {
	概览 = "DATE",
	按平台 = "PLATFORM",
	按店铺 = "SHOP",
	按售后原因 = "REASON",
	按快递公司 = "COMPANY_NAME",
	按平台商品 = "ITEM",
	按平台SKU = "ITEM_SKU",
	按货品 = "SYS_ITEM",
	按货品SKU = "SYS_SKU",
	按市场档口 = "STALL",
	按供应商 = "SUPPLIER",

	DATE = "概览",
	PLATFORM = "按平台",
	SHOP = "按店铺",
	REASON = "按售后原因",
	COMPANY_NAME = "按快递公司",
	ITEM = "按平台商品",
	ITEM_SKU = "按平台SKU",
	SYS_ITEM = "按货品",
	SYS_ITEM_SKU = "按货品SKU",
	STALL = "按市场档口",
	SUPPLIER = "按供应商",
}

export const TabPaneItems = {
	[TabEnum.按货品SKU]: {
		key: TabEnum.按货品SKU,
		label: TabEnum.SYS_ITEM_SKU,
		tableSummary: true,

	},
	[TabEnum.按货品]: {
		key: TabEnum.按货品,
		label: TabEnum.SYS_ITEM,
		tableSummary: true,

	},
};

export const IndicatorEnum = {
	销售金额: "payment",
	销售订单数: "tidCount",
	售后订单数: "refundTotalCount",
	售后率: "refundRate",
	退款金额: "refundAmount",
	退款订单数: "returnFeeTidCount",
	退款率: "returnFeeRate",
	发货前仅退款金额: "waitSendOnlyRefundAmount",
	发货前仅退款订单数: "waitSendOnlyRefundTidCount",
	发货前仅退款率: "waitSendOnlyRefundRate",
	退货退款金额: "returnAndRefundAmount",
	退货退款订单数: "returnAndRefundTidCount",
	退货退款率: "returnAndRefundRate",
	换货订单数: "exchangeTidCount",
	换货率: "exchangeRate",
};

export enum EnumChartYAxisOrder {
	元 = 1,
	笔 = 2,
	百分比 = 3
}

export const Indicator = {
	// chartYAxisOrder: 双Y轴情况下，数值越低，代表级别越高，则Y轴排在左侧
	[IndicatorEnum.销售金额]: {
		title: "销售金额",
		key: IndicatorEnum.销售金额,
		unit: "元",
		chartYAxisOrder: EnumChartYAxisOrder.元,
		tip: "当前筛选条件下，订单支付成功的订单金额汇总，包含售后订单",
		precision: 2, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 100,
		},
	},
	[IndicatorEnum.销售订单数]: {
		title: "销售订单数",
		key: IndicatorEnum.销售订单数,
		unit: "笔",
		chartYAxisOrder: EnumChartYAxisOrder.笔,
		tip: "当前筛选条件下，订单支付成功的订单数汇总，包含售后订单",
		precision: 0, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 120,
		},
	},
	[IndicatorEnum.售后订单数]: {
		title: "售后订单数",
		key: IndicatorEnum.售后订单数,
		unit: "笔",
		chartYAxisOrder: EnumChartYAxisOrder.笔,
		tip: "当前筛选条件下，产生售后且售后完成的订单数",
		precision: 0, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 120,
		},
	},
	[IndicatorEnum.售后率]: {
		title: "售后率",
		key: IndicatorEnum.售后率,
		unit: "%",
		chartYAxisOrder: EnumChartYAxisOrder.百分比,
		tip: "售后率=售后订单数/销售订单数",
		precision: 2, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 100,
		},
	},
	[IndicatorEnum.退款金额]: {
		title: "退款金额",
		key: IndicatorEnum.退款金额,
		unit: "元",
		chartYAxisOrder: EnumChartYAxisOrder.元,
		tip: "当前筛选条件下，退款成功订单产生的退款金额",
		precision: 2, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 100,
		},
	},
	[IndicatorEnum.退款订单数]: {
		title: "退款订单数",
		key: IndicatorEnum.退款订单数,
		unit: "笔",
		chartYAxisOrder: EnumChartYAxisOrder.笔,
		tip: "当前筛选条件下，产生退款且退款完成的订单数，含仅退款、退货退款",
		precision: 0, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 120,
		},
	},
	[IndicatorEnum.退款率]: {
		title: "退款率",
		key: IndicatorEnum.退款率,
		unit: "%",
		chartYAxisOrder: EnumChartYAxisOrder.百分比,
		tip: "退款率=退款订单数/销售订单数",
		precision: 2, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 100,
		},
	},
	[IndicatorEnum.发货前仅退款金额]: {
		title: "发货前仅退款金额",
		key: IndicatorEnum.发货前仅退款金额,
		unit: "元",
		chartYAxisOrder: EnumChartYAxisOrder.元,
		tip: "当前筛选条件下，仅退款完成且未发货的订单产生的退款金额",
		precision: 2, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 140,
		},
	},
	[IndicatorEnum.发货前仅退款订单数]: {
		title: "发货前仅退款订单数",
		key: IndicatorEnum.发货前仅退款订单数,
		unit: "笔",
		chartYAxisOrder: EnumChartYAxisOrder.笔,
		tip: "当前筛选条件下，仅退款完成且未发货的订单数",
		precision: 0, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 160,
		},
	},
	[IndicatorEnum.发货前仅退款率]: {
		title: "发货前仅退款率",
		key: IndicatorEnum.发货前仅退款率,
		unit: "%",
		chartYAxisOrder: EnumChartYAxisOrder.百分比,
		tip: "发货前仅退款率=发货前仅退款订单数/销售订单数",
		precision: 2, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 140,
		},
	},
	[IndicatorEnum.退货退款金额]: {
		title: "退货退款金额",
		key: IndicatorEnum.退货退款金额,
		unit: "元",
		chartYAxisOrder: EnumChartYAxisOrder.元,
		tip: "当前筛选条件下，退货退款完成的订单产生的退款金额",
		precision: 2, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 120,
		},
	},
	[IndicatorEnum.退货退款订单数]: {
		title: "退货退款订单数",
		key: IndicatorEnum.退货退款订单数,
		unit: "笔",
		chartYAxisOrder: EnumChartYAxisOrder.笔,
		tip: "当前筛选条件下，退货退款完成的订单数",
		precision: 0, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 140,
		},
	},
	[IndicatorEnum.退货退款率]: {
		title: "退货退款率",
		key: IndicatorEnum.退货退款率,
		unit: "%",
		chartYAxisOrder: EnumChartYAxisOrder.百分比,
		tip: "退货退款率=退货退款订单数/销售订单数",
		precision: 2, // 用于小数位格式化
		tableColumnConfig: {
			// 用于表格列配置
			summary: true, // 是否需要展示统计行
			width: 120,
		},
	},
	// [IndicatorEnum.换货订单数]: {
	//     title: "换货订单数",
	//     key: IndicatorEnum.换货订单数,
	//     unit: "笔",
	//     chartYAxisOrder: 3,
	//     tip: "当前筛选条件下，订单支付成功的订单数汇总，包含售后订单",
	//     precision: 0, // 用于小数位格式化
	//     tableColumnConfig: { // 用于表格列配置
	//         summary: true, // 是否需要展示统计行
	//     }
	// },
	// [IndicatorEnum.换货率]: {
	//     title: "换货率",
	//     key: IndicatorEnum.换货率,
	//     unit: "%",
	//     chartYAxisOrder: 3,
	//     tip: "当前筛选条件下，订单支付成功的订单数汇总，包含售后订单",
	//     precision: 0, // 用于小数位格式化
	//     tableColumnConfig: { // 用于表格列配置
	//         summary: true, // 是否需要展示统计行
	//     }
	// }
};

export const defaultColumns = {
	[TabEnum.按货品]: [
		{
			title: "图片",
			dataIndex: "picUrl",
			key: "picUrl",
			width: 100,
			// render: 见 TableItem.tsx 统一处理
		},
		{
			title: "简称",
			dataIndex: "sysItemAlias",
			key: "sysItemAlias",
			sorter: true,
			width: 120,
		},
		// {
		// 	title: "供应商名称",
		// 	dataIndex: "supplierName",
		// 	key: "supplierName",
		// 	sorter: true,
		// 	width: 120,
		// },
		{
			title: "本期出入库",
			dataIndex: "stockInOutNum",
			key: "stockInOutNum",
			sorter: true,
			width: 120,
		},
		{
			title: "期初库存",
			dataIndex: "opBeforeNum",
			key: "opBeforeNum",
			sorter: true,
			width: 120,
		},
		{
			title: "期末库存",
			dataIndex: "opAfterNum",
			key: "opAfterNum",
			sorter: true,
			width: 120,
		},
		// 出库类型
		{ title: "销售出库(线上)", dataIndex: "type4Sum", key: "type4Sum", sorter: true, width: 120 },
		{ title: "销售出库(线下)", dataIndex: "type5Sum", key: "type5Sum", sorter: true, width: 120 },
		{ title: "自动发货出库", dataIndex: "type16Sum", key: "type16Sum", sorter: true, width: 120 },
		{ title: "盘亏出库", dataIndex: "type7Sum", key: "type7Sum", sorter: true, width: 120 },
		{ title: "标签出库", dataIndex: "type13Sum", key: "type13Sum", sorter: true, width: 120 },
		{ title: "手动出库", dataIndex: "type15Sum", key: "type15Sum", sorter: true, width: 120 },
		{ title: "手动出库(扫描)", dataIndex: "type23Sum", key: "type23Sum", sorter: true, width: 120 },
		// { title: "订单预占库存", dataIndex: "type18Sum", key: "type18Sum", sorter: true, width: 120 },
		// { title: "打包移出", dataIndex: "type9Sum", key: "type9Sum", sorter: true, width: 120 },
		// { title: "拆包移出", dataIndex: "type11Sum", key: "type11Sum", sorter: true, width: 120 },
		// 入库类型
		{ title: "初始化入库", dataIndex: "type0Sum", key: "type0Sum", sorter: true, width: 120 },
		{ title: "到货入库", dataIndex: "type1Sum", key: "type1Sum", sorter: true, width: 120 },
		{ title: "到货入库(扫描)", dataIndex: "type21Sum", key: "type21Sum", sorter: true, width: 120 },
		{ title: "到货入库(冲红)", dataIndex: "type24Sum", key: "type24Sum", sorter: true, width: 120 },
		{ title: "采购入库", dataIndex: "type2Sum", key: "type2Sum", sorter: true, width: 120 },
		{ title: "采购入库(扫描)", dataIndex: "type22Sum", key: "type22Sum", sorter: true, width: 120 },
		{ title: "采购入库(冲红)", dataIndex: "type25Sum", key: "type25Sum", sorter: true, width: 120 },
		{ title: "退货入库", dataIndex: "type3Sum", key: "type3Sum", sorter: true, width: 120 },
		{ title: "取消自动发货出库", dataIndex: "type17Sum", key: "type17Sum", sorter: true, width: 120 },
		{ title: "盘盈入库", dataIndex: "type6Sum", key: "type6Sum", sorter: true, width: 120 },
		{ title: "标签入库", dataIndex: "type12Sum", key: "type12Sum", sorter: true, width: 120 },
		{ title: "手动出库退还", dataIndex: "type14Sum", key: "type14Sum", sorter: true, width: 120 }
		// { title: "订单回占库存", dataIndex: "type19Sum", key: "type19Sum", sorter: true, width: 120 },
		// { title: "导入更新", dataIndex: "type20Sum", key: "type20Sum", sorter: true, width: 120 }
		// { title: "打包移入", dataIndex: "type8Sum", key: "type8Sum", sorter: true, width: 120 },
		// { title: "拆包移入", dataIndex: "type10Sum", key: "type10Sum", sorter: true, width: 120 },
		// {
		// 	title: "成本价",
		// 	dataIndex: "costPrice",
		// 	key: "costPrice",
		// 	sorter: true,
		// 	width: 120,
		// },
		// {
		// 	title: "售价",
		// 	dataIndex: "price",
		// 	key: "price",
		// 	sorter: true,
		// 	width: 120,
		// },
		// {
		// 	title: "期初总成本",
		// 	dataIndex: "allCostStart",
		// 	key: "allCostStart",
		// 	sorter: true,
		// 	width: 120,
		// },
		// {
		// 	title: "期末总成本",
		// 	dataIndex: "allCostEnd",
		// 	key: "allCostEnd",
		// 	sorter: true,
		// 	width: 120,
		// }
	],
	[TabEnum.按货品SKU]: [
		{
			title: "图片",
			dataIndex: "picUrl",
			key: "picUrl",
			width: 100,
			// render: 见 TableItem.tsx 统一处理
		},
		{
			title: "简称",
			dataIndex: "sysItemAlias",
			key: "sysItemAlias",
			sorter: true,
			width: 120,
		},
		{
			title: "货品规格编码",
			dataIndex: "skuOuterId",
			key: "skuOuterId",
			sorter: true,
			width: 120,
		},
		{
			title: "货品规格名称",
			dataIndex: "sysSkuName",
			key: "sysSkuName",
			sorter: true,
			width: 120,
		},
		{
			title: "供应商名称",
			dataIndex: "supplierName",
			key: "supplierName",
			sorter: true,
			width: 120,
		},
		{
			title: "本期出入库",
			dataIndex: "stockInOutNum",
			width: 120,
			key: "stockInOutNum",
			sorter: true,
		},
		{
			title: "期初库存",
			dataIndex: "opBeforeNum",
			width: 120,
			key: "opBeforeNum",
			sorter: true,
		},
		{
			title: "期末库存",
			dataIndex: "opAfterNum",
			width: 120,
			key: "opAfterNum",
			sorter: true,
		},
		// 出库类型
		{ title: "销售出库(线上)", dataIndex: "type4Sum", key: "type4Sum", sorter: true, width: 120 },
		{ title: "销售出库(线下)", dataIndex: "type5Sum", key: "type5Sum", sorter: true, width: 120 },
		{ title: "自动发货出库", dataIndex: "type16Sum", key: "type16Sum", sorter: true, width: 120 },
		{ title: "盘亏出库", dataIndex: "type7Sum", key: "type7Sum", sorter: true, width: 120 },
		{ title: "标签出库", dataIndex: "type13Sum", key: "type13Sum", sorter: true, width: 120 },
		{ title: "手动出库", dataIndex: "type15Sum", key: "type15Sum", sorter: true, width: 120 },
		{ title: "手动出库(扫描)", dataIndex: "type23Sum", key: "type23Sum", sorter: true, width: 120 },
		// { title: "订单预占库存", dataIndex: "type18Sum", key: "type18Sum", sorter: true, width: 120 },
		// { title: "打包移出", dataIndex: "type9Sum", key: "type9Sum", sorter: true, width: 120 },
		// { title: "拆包移出", dataIndex: "type11Sum", key: "type11Sum", sorter: true, width: 120 },
		// 入库类型
		{ title: "初始化入库", dataIndex: "type0Sum", key: "type0Sum", sorter: true, width: 120 },
		{ title: "到货入库", dataIndex: "type1Sum", key: "type1Sum", sorter: true, width: 120 },
		{ title: "到货入库(扫描)", dataIndex: "type21Sum", key: "type21Sum", sorter: true, width: 120 },
		{ title: "到货入库(冲红)", dataIndex: "type24Sum", key: "type24Sum", sorter: true, width: 120 },
		{ title: "采购入库", dataIndex: "type2Sum", key: "type2Sum", sorter: true, width: 120 },
		{ title: "采购入库(扫描)", dataIndex: "type22Sum", key: "type22Sum", sorter: true, width: 120 },
		{ title: "采购入库(冲红)", dataIndex: "type25Sum", key: "type25Sum", sorter: true, width: 120 },
		{ title: "退货入库", dataIndex: "type3Sum", key: "type3Sum", sorter: true, width: 120 },
		{ title: "取消自动发货出库", dataIndex: "type17Sum", key: "type17Sum", sorter: true, width: 120 },
		{ title: "盘盈入库", dataIndex: "type6Sum", key: "type6Sum", sorter: true, width: 120 },
		{ title: "标签入库", dataIndex: "type12Sum", key: "type12Sum", sorter: true, width: 120 },
		{ title: "手动出库退还", dataIndex: "type14Sum", key: "type14Sum", sorter: true, width: 120 },
		// { title: "订单回占库存", dataIndex: "type19Sum", key: "type19Sum", sorter: true, width: 120 },
		// { title: "导入更新", dataIndex: "type20Sum", key: "type20Sum", sorter: true, width: 120 },
		// { title: "打包移入", dataIndex: "type8Sum", key: "type8Sum", sorter: true, width: 120 },
		// { title: "拆包移入", dataIndex: "type10Sum", key: "type10Sum", sorter: true, width: 120 },
		{
			title: "成本价",
			dataIndex: "costPrice",
			key: "costPrice",
			sorter: true,
			width: 120,
		},
		{
			title: "售价",
			dataIndex: "price",
			key: "price",
			sorter: true,
			width: 120,
		},
		{
			title: "期初总成本",
			dataIndex: "allCostStart",
			width: 120,
			key: "allCostStart",
			sorter: true,
		},
		{
			title: "期末总成本",
			dataIndex: "allCostEnd",
			width: 120,
			key: "allCostEnd",
			sorter: true,
		}
	],
};