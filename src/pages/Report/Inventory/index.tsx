import React, { useEffect, useMemo, useRef, useState } from "react";
import { Button, Layout, Spin, Tabs, message } from "antd";
import { isEqual } from "lodash";
import Search from "./components/Search";
import { TabEnum, TabPaneItems } from "./constants";
import styles from "./index.module.scss";
import AnalysisTable from "./components/AnalysisTable";
import userStore from "@/stores/user";
import { InventorySelectStockReportPageApi } from "@/apis/report/inventory";
import useGetState from "@/utils/hooks/useGetState";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import img_calculating from '@/assets/image/数据计算中.png';
import { sleep } from "@/utils/util";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { RequestLoop } from "@/utils/requestLoop";
import { PageNameControlEnum } from "@/components-biz/LowVersionControlModal/constant";
import reportInventory from "@/assets/image/reportInventory.png";
import { lowVersionLock } from "@/components-biz/LowVersionControlModal";
import TestComponent from "@/components/TestComponent/RedTestComponent";

const { TabPane } = Tabs;

const defaultPagination = { pageNo: 1, pageSize: 10 };

const defaultSortInfo = { sortType: null, sortValue: null };

const requestDataLoop = new RequestLoop({ intervalTime: 1000, resolveType: 'process', maxRetryCount: 5 });
const ReportInventory = () => {
	const { userInfo } = userStore;
	const [lowLock, setLowLock] = useState(false);
	const [loading, setLoading] = useState(false);
	const [requestedDataTabKeys, setRequestedDataTabKeys, getRequestedDataTabKeys] = useGetState([]); // 同一个条件下当前Tab是否已经请求过数据
	const [tabActiveKey, setTabActiveKey, getTabActiveKey] = useGetState<TabEnum>(TabPaneItems[TabEnum.按货品SKU].key); // 当前弹窗tabs
	const [originFormValues, setOriginFormValues, getOriginFormValues] = useGetState({});
	const [formValues, setFormValues, getFormValues] = useGetState({}); // 把搜索组件中的表单数据保存一下
	const [dataSource, setDataSource] = useState({}); // 每个tab下的表格数据
	const [originalDataSource, setOriginalDataSource] = useState({}); // 每个tab下的原始表格数据
	const [pagination, setPagination] = useState({}); // 每个tab下的分页数据
	const [searchParams, setSearchParams] = useState({}); // 每个tab下的查询入参数据
	const [sortInfo, setSortInfo] = useState({});
	const [isCalculating, setIsCalculating, getIsCalculating] = useGetState(true);
	const [spinning, setSpinning] = useState(false);
	const [indicatorRequesting, setIndicatorRequesting] = useState(false);
	const [tableRequesting, setTableRequesting] = useState(false);
	const [tableLoading, setTableLoading] = useState(false);
	const [stickyHeight, setStickyHeight] = useState(92);

	// 接口请求时，保存请求参数
	const handleSearchParamsBeforeRequest = (params) => {
		const newSearchParams = {
			...searchParams,
			[getTabActiveKey()]: params
		};
		setSearchParams(newSearchParams);
	};

	// 接口请求完以后，处理分页数据
	const handlePaginationAfterRequested = (response) => {
		const total = response.pageNo == 1 ? response.total : pagination[getTabActiveKey()]?.total;
		let newPagination = {
			...pagination,
			[getTabActiveKey()]: {
				current: response.pageNo,
				pageSize: response.pageSize,
				total,
				showSizeChanger: true,
				pageSizeOptions: ["10", "20", "50", "100", "200"],
			}
		};

		setPagination(newPagination);
	};

	// 接口请求完以后，处理表格数据
	const handleDataSourceAfterRequested = (response) => {
		const { pageSize, pageNo, list = [] } = response || {};
		const baseNumber = pageSize * (pageNo - 1);

		const tableData = list.map((i, index) => {
			const curSerialNumber = parseInt(baseNumber + index + 1, 10);
			return ({
				...i,
				rowKey: `${tabActiveKey}_index_${curSerialNumber}`
			});
		});
		const newDataSource = {
			...dataSource,
			[getTabActiveKey()]: tableData
		};
		setDataSource(newDataSource);
		// 保存原始数据
		setOriginalDataSource({
			...originalDataSource,
			[getTabActiveKey()]: [...tableData]
		});
	};

	// 查询表格数据
	const requestTableData = async(params) => {
		sendPoint(Pointer.报表_进销存报表_点击_查询);
		setTableRequesting(true);
		handleSearchParamsBeforeRequest(params);
		const res = await InventorySelectStockReportPageApi({ ...params });
		setTableRequesting(false);
		setTableLoading(false);
		handlePaginationAfterRequested(res);
		handleDataSourceAfterRequested(res);
	};


	const tabOnChange = async(value: TabEnum) => {
		// 如果当前tab没有请求过数据或者form的值发生了变化，才允许重新请求数据
		setTabActiveKey(value);
		const requestedDataTabKeys = [...getRequestedDataTabKeys()];
		// Always re-query data when changing presentation mode
		await sleep(1);
		requestedDataTabKeys.push(value);
		setRequestedDataTabKeys(requestedDataTabKeys);
		const params = handleRequestTableDataParamsByGroup();
		requestTableData(params);
	};

	// 格式化表单数据
	const formatFormValues = async(values) => {
		console.log("values:::", values);
		const { startTime, endTime, queryType, alias, skuOuterId, sysSkuName, supplierId, classifyIdList, brandIdList, stockChangeFlag } = values;
		const params:any = {
			startTime,
			endTime,
			queryType,
			alias,
			skuOuterId,
			sysSkuName,
			supplierId,
			classifyIdList,
			brandIdList,
			stockChangeFlag,
		};
		return params;
	};

	// 通过tab组装table数据的查询参数
	const handleRequestTableDataParamsByGroup = () => {
		const tabActiveKey = getTabActiveKey();
		const curTabPagination = pagination[tabActiveKey] || defaultPagination;
		const curTabSortInfo = sortInfo[tabActiveKey] || defaultSortInfo;
		const newParams = {
			queryType: "BY_SUMMARY",
			...getFormValues(),
			pageSize: curTabPagination.pageSize,
			pageNo: 1, // curTabPagination.current || defaultPagination.pageNo,
			// sortType: "desc",
			// sortValue: "payment",
			groupType: tabActiveKey
		};
		return newParams;
	};

	const onSubmit = async(values, isFirst = false) => {
		setLoading(true);
		let requestedDataTabKeys = [...getRequestedDataTabKeys()];
		const formValuesHasChanged = !isEqual(values, getOriginFormValues());
		if (formValuesHasChanged) {
			requestedDataTabKeys = [tabActiveKey];
		} else {
			requestedDataTabKeys.push(tabActiveKey);
		}
		setOriginFormValues(values);
		const formValues = await formatFormValues(values);
		setFormValues(formValues);
		if (getIsCalculating() && !isFirst) {
			setLoading(false);
			return;
		}
		try {
			await sleep(50);
			const params = handleRequestTableDataParamsByGroup();
			console.log("params:::", params, getTabActiveKey());
			await requestTableData(params);
			setRequestedDataTabKeys(requestedDataTabKeys);
			setLoading(false);
		} catch (e) {
			console.log("e:::", e);
			message.error(e.errorMessage || "查询失败");
			setLoading(false);
		} finally {
			if (isFirst) setIsCalculating(false);
		}

	};

	const onReset = async(values) => {
		onSubmit(values);
	};

	// 表格分页
	const paginationOnChange = (pagination) => {
		const curTabSearchParams = searchParams[getTabActiveKey()];
		const params = {
			...curTabSearchParams,
			pageSize: pagination.pageSize,
			pageNo: curTabSearchParams.pageSize === pagination.pageSize ? pagination.current : 1
		};
		setTableLoading(true);
		requestTableData(params);
	};

	const handleSort = (sorter) => {
		const currentTabKey = getTabActiveKey();
		const currentData = dataSource[currentTabKey] || [];
		const originalData = originalDataSource[currentTabKey] || [];
		
		// 如果没有排序，恢复原始数据
		if (!sorter.order) {
			setDataSource({
				...dataSource,
				[currentTabKey]: [...originalData]
			});
			return;
		}
		
		// 对数据进行排序
		const sortedData = [...currentData].sort((a, b) => {
			const field = sorter.field;
			let aValue = a[field];
			let bValue = b[field];
			
			// 处理数值类型
			if (typeof aValue === 'number' && typeof bValue === 'number') {
				return sorter.order === 'ascend' ? aValue - bValue : bValue - aValue;
			}
			
			// 处理字符串类型
			if (typeof aValue === 'string' && typeof bValue === 'string') {
				aValue = aValue.toLowerCase();
				bValue = bValue.toLowerCase();
				if (sorter.order === 'ascend') {
					return aValue.localeCompare(bValue);
				} else {
					return bValue.localeCompare(aValue);
				}
			}
			
			// 处理null或undefined值
			if (aValue == null && bValue == null) return 0;
			if (aValue == null) return sorter.order === 'ascend' ? -1 : 1;
			if (bValue == null) return sorter.order === 'ascend' ? 1 : -1;
			
			// 默认比较
			if (aValue < bValue) return sorter.order === 'ascend' ? -1 : 1;
			if (aValue > bValue) return sorter.order === 'ascend' ? 1 : -1;
			return 0;
		});
		
		// 更新排序后的数据
		setDataSource({
			...dataSource,
			[currentTabKey]: sortedData
		});
	};

	// 表格排序
	const sortOnChange = (sorter) => {
		if (["supplierName", "costPrice", "retailPrice", "allCostStart", "allCostEnd"].includes(sorter.field)) {
			handleSort(sorter);
			console.log(sorter);
			return;
		}
		const curTabSearchParams = searchParams[getTabActiveKey()];
		const sortType = sorter.order === "ascend" ? "asc" : (sorter.order === "descend" ? "desc" : null);
		const params = {
			...curTabSearchParams,
			sortType,
			pageNo: 1
		};
		if (sortType) {
			params["sortValue"] = sorter.field;
		} else {
			params["sortValue"] = null;
		}
		setTableLoading(true);
		requestTableData(params);
	};

	const verifySuccessCb = async() => {
		sendPoint(Pointer.报表_进销存报表_点击_下载);
		await downloadCenter({
			requestParams: searchParams[tabActiveKey] || {},
			fileName: '进销存报表',
			module: ModulesFunctionEnum.进销存报表
		});
	};

	useEffect(() => {
		setSpinning(tableRequesting || indicatorRequesting);
	}, [tableRequesting, indicatorRequesting]);

	useEffect(() => {
		// 计算下吸顶的距离
		const topContainer = document.querySelector(`.header-menu`);
		const height = topContainer?.getBoundingClientRect()?.bottom || 92;
		setStickyHeight(height);
	}, []);

	useEffect(() => {
		lowVersionLock(PageNameControlEnum.进销存报表).then(res => {
			console.log('res', res);
			sendPoint(res ? Pointer.报表_进销存报表_版本拦截 : Pointer.报表_进销存报表_页面展现);
			setLowLock(res);
			if (!res) {
				onSubmit(getOriginFormValues(), true);
			}
		});
		return () => {
			requestDataLoop.clear();
		};
	}, []);

	return (
		<>
			{
				lowLock ? (
					<div className="r-bg-white r-ta-c r-mt-8 r-flex r-ai-c r-jc-c" style={ { minHeight: "calc(100vh - 100px)" } }>
						<div>
							<div className="r-fs-20 r-c-black r-fw-500">当前功能限高配版可用，可联系销售升级</div>
							<div className="r-fs-14 r-mt-12 r-mb-12 r-c-999">出入库账单清晰可见，避免库存过剩或短缺问题，确保库存与销售平衡</div>
							<div><img src={ reportInventory } height={ 500 } alt="" /></div>
						</div>
					</div>
				) : (
					<div className={ styles.afterSalesAnalysis }>

						<Layout className="kdzs-section-small">
							<Search onSubmit={ onSubmit } onReset={ onReset } loading={ loading } />
						</Layout>

						{
							isCalculating ? (
								<Layout className="kdzs-section-small">
									<div className={ styles["calculating-container"] }>
										<div>
											<img width="110" src={ img_calculating } alt="数据计算中..." />
										</div>
										<div className={ styles["calculating-text"] }>
											数据计算中，请稍后…
										</div>
									</div>
								</Layout>
							) : (
								<Layout hidden={ isCalculating } className="kdzs-section-small">
									<Tabs
										className={ styles.tab }
										activeKey={ tabActiveKey }
										type="card"
										onChange={ tabOnChange }
										tabBarGutter={ 10 }
										destroyInactiveTabPane={ false }
										animated={ false }
										tabBarExtraContent={ (
											<Button
												onClick={ verifySuccessCb }
												style={ { padding: '0 35px' } }
											>下载
											</Button>
										) }
									>
										{
											Object.values(TabPaneItems).map(i => {
												return (
													<TabPane tab={ i.label } key={ i.key }>
														<TestComponent type="2" />
														<Spin spinning={ spinning } wrapperClassName={ styles["spin-wrapper"] }>
															<AnalysisTable
																showSummary={ i.tableSummary }
																loading={ tableLoading }
																key={ tabActiveKey }
																activeKey={ tabActiveKey }
																pagination={ pagination[tabActiveKey] }
																dataSource={ dataSource[tabActiveKey] }
																paginationOnChange={ paginationOnChange }
																sortOnChange={ sortOnChange }
																searchParams={ searchParams[tabActiveKey] }
																stickyHeight={ stickyHeight }
															/>
														</Spin>
													</TabPane>
												);
											})
										}
									</Tabs>
								</Layout>
							)
						}
					</div>
				)
			}
		</>
	);

};

export default ReportInventory;
