import React, { useEffect, useMemo, useRef, useState } from "react";
import { Layout, Ta<PERSON>, But<PERSON> } from "antd";
import { useRequest } from "ahooks";
import { cloneDeep } from "lodash";
import { searchParamsType } from '@/types/schemas/report/grossProfit';
import Charts from "./components/LineCharts/index";
import GrossProfitTable from "./components/GrossProfitTable";
import SearchContainer from "./components/SearchContainer";
import { grossProfitApi } from "@/apis/report/grossProfit";
import userStore from "@/stores/user";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import s from "./index.module.scss";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import { IndicatorEnum, Indicator } from './constants';
import { AutoCompleteDataByDate } from "./utils";
import reportSaleProfit from "@/assets/image/reportSaleProfit.png";
import { lowVersionLock } from "@/components-biz/LowVersionControlModal";
import { PageNameControlEnum } from "@/components-biz/LowVersionControlModal/constant";
import RedTestComponent from "@/components/TestComponent/RedTestComponent";

const { TabPane } = Tabs;

export enum TabEnum {
	概览 = "summary",
	按平台 = "platform",
	按店铺 = "shop",
	按平台商品 = "item",
	按平台sku = "itemSKU",
	按货品 = "sysItem",
	按货品SKU = "sysItemSKU",
	按订单 = "tidList",
	按订单商品 = "order",
}

const TabsMap = {
	platform: 1,
	shop: 2,
	sysItem: 3,
	sysItemSKU: 4,
	item: 5,
	itemSKU: 6,
	tidList: 7,
	order: 8,
	summary: 9,
};

const GrossProfit = () => {
	const { isShowZeroStockVersion } = userStore;
	const ref = useRef<any>();
	const [tabsActiveKey, setTabsActiveKey] = useState<string>('summary'); // 当前弹窗tabs
	const [pageNo, setPageNo] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [grossProfitData, setGrossProfitData] = useState({
		list: [],
		totalObj: {}
	});

	const [lowLock, setLowLock] = useState(false);

	const [chartData, setChartData] = useState(Indicator); // 指标统计数据
	const [indicatorData, setIndicatorData] = useState(Indicator); // 指标统计数据
	const [chartDataLoading, setChartDataLoading] = useState(true);
	const [summaryDataLoading, setSummaryDataLoading] = useState(true);
	const [stickyHeight, setStickyHeight] = useState(92);

	const [searchParams, setSearchParams] = useState({
		queryTimeType: 1,
		queryGroupType: 1,
		pageNo,
		pageSize,
	});
	const { run: getGrossProfit, loading, error } = useRequest(grossProfitApi, {
		debounceWait: 100,
		loadingDelay: 150,
		manual: true,
		throwOnError: true,
		onSuccess: (res: any, params: any[]) => {
			if (res) {
				setGrossProfitData((pre) => {
					let data = {
						...pre,
						...res
					};
					return data;
				});
			}
		},
		onError: (e:Error) => {
			console.log(e);
		}
	} as any); // 请求数据列表

	const clickSearch = (params: searchParamsType, isRequestTotal?: boolean) => {
		let _searchParams:any = {
			queryGroupType: params.queryGroupType || TabsMap[tabsActiveKey],
			pageNo: params.pageNo || pageNo,
			pageSize: params.pageSize || pageSize,
			...params
		};
		// 排除赠品订单 传值转换
		if (_searchParams.isExecGift) {
			_searchParams.isGift = 'false';
		}
		delete _searchParams.isExecGift;
		setGrossProfitData({
			list: [],
			totalObj: {}
		});
		// 如果当前是概览页面需要多调两个接口
		const tabValue = params.tabValue || tabsActiveKey;
		if (tabValue === TabEnum.概览) {
			requestSummaryData(_searchParams);
			requestChartData(_searchParams);
			// 概览页面要求时间排序
			_searchParams.sortType = 'desc';
			_searchParams.sortValue = 'date';
		}


		// 查询表格数据
		getGrossProfit(_searchParams);
		setSearchParams(_searchParams);
		params.pageNo && setPageNo(params.pageNo);
	};
	const clickReset = () => {

	};
	// 切换tab
	const onChangeTab = async(value:string) => {
		setTabsActiveKey(value);
		const params = await ref?.current?.getSearchParams(false);
		clickSearch({
			...params,
			// ...ref?.current?.getSearchParams(false),
			tabValue: value,
			pageNo: 1,
			queryGroupType: TabsMap[value] || 1,
		}, true);
	};
	// const onPageSizeChange = (curPageSize:number, pageSize:number) => {
	// 	clickSearch({
	// 		...searchParams,
	// 		pageSize
	// 	});
	// 	setPageSize(pageSize);
	// };
	const onPageChange = (pageNo:number, pageSize: number) => {
		clickSearch({
			...searchParams,
			pageNo,
			pageSize
		} as any);
		setPageNo(pageNo);
		setPageSize(pageSize);
	};
	const verifySuccessCb = async() => {
		let _params = await ref?.current?.getSearchParams(false);
		let params = {
			..._params,
			queryGroupType: TabsMap[tabsActiveKey],
		};
		sendPoint(Pointer.报表_销售毛利润报表_下载);
		await downloadCenter({
			requestParams: params,
			fileName: '毛利润报表',
			module: ModulesFunctionEnum.毛利润报表
		});
	};

	const TabPaneList = useMemo(() => {
		return [
			{
				tabName: '按概览',
				key: TabEnum.概览,
				pointer: Pointer.报表_销售毛利润报表_Tab_概览,
				rowKey: ['date']
			},
			{
				tabName: '按平台',
				key: TabEnum.按平台,
				pointer: Pointer.报表_销售毛利润报表_Tab_按平台,
				rowKey: ['platformAlias']
			}, {
				tabName: '按店铺',
				key: TabEnum.按店铺,
				pointer: Pointer.报表_销售毛利润报表_Tab_按店铺,
				rowKey: ['platformAlias', 'sellerNick']
			}, {
				tabName: '按平台商品',
				key: TabEnum.按平台商品,
				pointer: Pointer.报表_销售毛利润报表_Tab_按平台商品,
				rowKey: ['platformAlias', 'itemId']
			}, {
				tabName: '按平台sku',
				key: TabEnum.按平台sku,
				pointer: Pointer.报表_销售毛利润报表_Tab_按平台SKU,
				rowKey: ['platformAlias', 'skuId']
			}, {
				tabName: '按货品',
				key: TabEnum.按货品,
				pointer: Pointer.报表_销售毛利润报表_Tab_按货品,
				rowKey: ['sysItemId'],
				hidden: isShowZeroStockVersion
			}, {
				tabName: '按货品SKU',
				key: TabEnum.按货品SKU,
				pointer: Pointer.报表_销售毛利润报表_Tab_按货品SKU,
				hidden: isShowZeroStockVersion,
				rowKey: ['sysItemId', 'sysSkuId']
			}, {
				tabName: '按订单',
				key: TabEnum.按订单,
				rowKey: ['tid']
				// pointer: Pointer.报表_销售毛利润报表_Tab_按货品SKU,
				// hidden: isShowZeroStockVersion
			}, {
				tabName: '按订单商品',
				key: TabEnum.按订单商品,
				pointer: Pointer.报表_销售毛利润报表_Tab_按订单商品,
				rowKey: ['orderId', 'skuId']
				// hidden: isShowZeroStockVersion
			}
		];

	}, [isShowZeroStockVersion]);
	const onTabClick = (key) => {
		const curTabItem = TabPaneList.find(i => i.key === key);
		if (curTabItem?.pointer) {
			sendPoint(curTabItem.pointer);
		}
	};

	// 处理chart数据
	const handleChartData = (list, params) => {
		// 前端自动补全后端缺失的日期数据，不然的话，图表渲染会出现bug
		const date2ListItem = {};
		const _indicator = cloneDeep(Indicator);
		list?.forEach(item => {
			date2ListItem[item.date] = item;
		});
		// const completeData = AutoCompleteDataByDate(params.startTime, params.endTime, list);
		const completeData = AutoCompleteDataByDate(params.startTime, params.endTime, list);
		completeData.forEach(dataItem => {
			Object.keys(_indicator).forEach(indicatorKey => {
				const curIndicator = {
					date: { value: dataItem.date },
					value: dataItem[indicatorKey],
					fields: _indicator[indicatorKey].title,
					unit: _indicator[indicatorKey].unit,
				};
				if ([IndicatorEnum.毛利率, IndicatorEnum.利润率].includes(indicatorKey)) {
					curIndicator.value = Number((dataItem[indicatorKey] * 100).toFixed(2));
				}

				curIndicator.value = Number(dataItem[indicatorKey]);

				if (!_indicator[indicatorKey]["data"]) {
					_indicator[indicatorKey]["data"] = [];
				}
				_indicator[indicatorKey]["data"].push(curIndicator);
			});
		});
		setChartData(_indicator);
	};

	const handleSummaryData = (res) => {
		try {
			const _indicatorData = cloneDeep(indicatorData);
			const { list } = res || {};
			const [data, preData] = list || [];
			Object.keys(indicatorData).forEach(key => {
				const indicator = indicatorData[key];
				const value = Number(data?.[indicator.key] || 0);
				let valueText:string|number = value;
				const preValue = Number(preData?.[indicator.key] || 0);
				let increased = true;
				// 百比率乘以100
				let percentage = ((value - preValue) / (preValue || 1)) * 100;
				if (value < preValue) {
					increased = false;
				} else {
				// 兼容preValue 是负数，val是正数的情况 这样的百分比应该是绝对正式
					percentage = Math.abs(percentage);
				}
				valueText = value.toFixed(indicator.precision);
				if (indicator.unit === "%") {
					valueText = value.toFixed(indicator.precision) + "%";
				}
				_indicatorData[indicator.key] = {
					..._indicatorData[indicator.key],
					value,
					valueText,
					preValue,
					increased,
					percentage
				};
			});

			setIndicatorData(_indicatorData);
		} catch (error) {
			console.error('handleSummaryData', error);
		}

	};

	// 查询概览指标数据
	const requestSummaryData = async(params) => {
		setSummaryDataLoading(true);
		const newParams = {
			...params,
			pageSize: 2,
			pageNo: 1,
			queryGroupType: 10,
		};
		const res = await grossProfitApi(newParams);
		handleSummaryData(res);
		console.log('handleSummaryData', res);
		setSummaryDataLoading(false);
	};

	// 查询折线图数据
	const requestChartData = async(params) => {
		setChartDataLoading(true);
		const newParams = {
			...params,
			pageSize: 93,
			pageNo: 1,
			queryGroupType: 9,
			sortType: 'asc',
			sortValue: 'date',

		};
		const { list } = await grossProfitApi(newParams);
		handleChartData(list, params);
		setChartDataLoading(false);
	};

	const handleSetDataConfigura = (val) => {
		ref?.current?.getSearchParams(); // 重新请求列表
	};


	useEffect(() => {
		sendPoint(Pointer.报表_销售毛利润报表_访问);
		sendPoint(Pointer.报表_销售毛利润报表_Tab_概览);
	}, []);

	useEffect(() => {
		// 计算下吸顶的距离
		const topContainer = document.querySelector(`.header-menu`);
		const height = topContainer?.getBoundingClientRect()?.bottom || 92;
		setStickyHeight(height);
	}, []);

	useEffect(() => {
		lowVersionLock(PageNameControlEnum.销售毛利率报表).then(res => {
			console.log('res', res);
			setLowLock(res);
		});
	}, []);

	return (
		<>
			{
				lowLock ? (
					<div className="r-bg-white r-ta-c r-mt-8 r-flex r-ai-c r-jc-c" style={ { minHeight: "calc(100vh - 100px)" } }>
						<div>
							<div className="r-fs-20 r-c-black r-fw-500">当前功能限高配版可用，可联系销售升级</div>
							<div className="r-fs-14 r-mt-12 r-mb-12 r-c-999">自动化、多维度分析毛利润与成本构成，实时追踪产品盈利能力</div>
							<div><img src={ reportSaleProfit } height={ 500 } alt="" /></div>
						</div>
					</div>
				) : (
					<div className={ s.grossProfit }>
						<Layout className="kdzs-section">
							<SearchContainer handleSearch={ clickSearch } handleReset={ clickReset } handleSetDataConfigura={ handleSetDataConfigura } loading={ loading } tableType={ tabsActiveKey } ref={ ref } />
						</Layout>
						<Layout className="kdzs-section" style={ { position: 'relative', padding: '16px 20px', marginBottom: 0, minHeight: 450 } }>
							<Tabs
								className={ s.tab }
								activeKey={ tabsActiveKey }
								type="card"
								onChange={ onChangeTab }
								onTabClick={ (key) => onTabClick(key) }
								tabBarGutter={ 10 }
								destroyInactiveTabPane
								animated={ false }
								tabBarExtraContent={ (
									<Button
										onClick={ verifySuccessCb }
										style={ { padding: '0 35px' } }
									>下载
									</Button>
								) }
							>
								{
									TabPaneList.filter(i => !i.hidden).map(i => {
										return (
											<TabPane tab={ i.tabName } key={ i.key }>
												<RedTestComponent />
												<div>
													{	i.key === TabEnum.概览 && (
														<Charts
															indicatorData={ indicatorData }
															chartData={ chartData }
															chartDataLoading={ chartDataLoading }
															searchParams={ searchParams }
															summaryDataLoading={ summaryDataLoading }
														/>
													)}
												</div>
												<GrossProfitTable
													searchParams={ searchParams }
													tableData={ grossProfitData }
													tableType={ tabsActiveKey }
													currentKey={ i.key }
													rowKey={ i.rowKey }
													pageSize={ pageSize }
													pageNo={ pageNo }
													loading={ loading }
													// onPageSizeChange={ onPageSizeChange }
													onPageChange={ onPageChange }
													stickyHeight={ stickyHeight }
												/>
											</TabPane>
										);
									})
								}
							</Tabs>
						</Layout>
					</div>
				)
			}
		</>
	);
};

export default GrossProfit;
