import React, { memo, useEffect, useRef, useState, useCallback, useMemo, CSSProperties, ReactNode } from "react";
import { Layout, Table, Tooltip, Typography, Empty, Pagination, Affix } from "antd";
import { ColumnsType } from 'antd/es/table';
import _, { isNumber, values } from "lodash";
import cs from 'classnames';
import { observer } from "mobx-react";
import Icon from "@/components/Icon";
import Image from "@/components/Image";
import GroupTable from '@/components/Table';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import styles from './index.module.scss';
import { TabEnum } from "../..";
import FieldsPermissionCheck, { FieldsPermissionCheckTypeEnum, FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import userStore from "@/stores/user";
import { BasicTable } from '@/components/SearchTableVirtual';
import RedTestComponent from "@/components/TestComponent/RedTestComponent";

interface GrossProfitTableProps {
	tableData: object,
	tableType: string,
	currentKey?: string,
	rowKey?: string[],
	loading?:boolean,
    pageSize: number,
    pageNo: number,
    searchParams:{
        [k:string]:any
    },
    // onPageSizeChange:(curPageSize:number, pageNo:number)=>void,
    onPageChange:(pageNo:number, pageSize:number)=>void,
	stickyHeight?:number,
}

const platformMap = {
	tb: '淘宝'
};

const { Text } = Typography;
const tooltip = (title:string, tip: string) => {
	return (
		<div className="r-flex r-ai-c r-jc-c">
			{title}
			<Tooltip title={ tip }>
				<span className="r-pointer r-c-999 r-ml-4">
					<Icon type="wenhao-xian" size={ 15 } />
				</span>

			</Tooltip>
		</div>
	);
};

const renderGoodsInfo = (record, imageHidden?:boolean) => {
	return (
		<div className={ styles["goods-info-container"] }>
			<div className="img-container" hidden={ imageHidden }>
				<Image src={ record.url } />
			</div>
			<div className="info-container">
				<div className="title">
					<div className="title-content">
						<Tooltip title={ record.itemTitle || "暂无商品名称" }>
							<span className="r-pointer">{record.itemTitle || "无"}</span>
						</Tooltip>

					</div>
					{/* <div className="icon-content"><PlatformIcon platform={ record.platformAlias } /></div> */}
				</div>
				<div className="extra">
					<span className="extra-title">商家编码：</span>
					<span className="extra-content">{record.outerId}</span>
				</div>
				<div className="extra">
					<span className="extra-title">ID：</span>
					<span className="extra-content">{record.itemId}</span>
				</div>
			</div>
		</div>
	);
};

const renderSkuInfo = (record, showOuterSkuId?:boolean) => {
	return (
		<div className={ styles["goods-info-container"] }>
			<div className="img-container">
				<Image src={ record.url } />
			</div>
			<div className="info-container">
				<div className="title">
					<div className="title-content">
						<Tooltip title={ record.skuName || "暂无规格名称" }>
							<span className="r-pointer">{record.skuName || "无"}</span>
						</Tooltip>

					</div>
					{/* <div className="icon-content"><PlatformIcon platform={ record.platformAlias } /></div> */}
				</div>
				{
					showOuterSkuId ? (
						<div className="extra">
							<span className="extra-title">规格编码：</span>
							<span className="extra-content">{record.outerSkuId}</span>
						</div>
					) : (
						<div className="extra">
							<span className="extra-title">商家编码：</span>
							<span className="extra-content">{record.outerId}</span>
						</div>
					)
				}
				<div className="extra">
					<span className="extra-title">skuID：</span>
					<span className="extra-content">{record.skuId}</span>
				</div>
			</div>
		</div>
	);
};

const renderSysGoodsInfo = (_, record) => {
	return (
		<div className={ styles["goods-info-container"] }>
			<div className="img-container">
				<Image src={ record.sysSkuPicPath } />
			</div>
			<div className="info-container">
				<div className="title">
					<div className="title-content">
						<Tooltip title={ record.sysItemAlias || "暂无货品简称" }>
							<span className="r-pointer">{record.sysItemAlias || "无"}</span>
						</Tooltip>

					</div>
					<div className="icon-content">
						{record.isCombination == 1 ? (
							<Tooltip title="组合货品">
								<span className="r-warehouse-combined">组</span>
							</Tooltip>
						) : ''}
					</div>
				</div>
				<div className="extra">
					<span className="extra-title">货品编码：</span>
					<span className="extra-content">{record.sysOuterId}</span>
				</div>
			</div>
		</div>
	);
};


const renderSysGoodsSkuInfo = (_, record) => {
	return (
		<div className={ styles["goods-info-container"] }>
			<div className="img-container">
				<Image src={ record.sysSkuPicPath } />
			</div>
			<div className="info-container">
				<div className="title">
					<div className="title-content">
						<Tooltip title={ record.sysSkuName || "暂无规格名称" }>
							<span className="r-pointer">{record.sysSkuName || "无"}</span>
						</Tooltip>
					</div>
					<div className="icon-content">
						{record.isCombination == 1 ? (
							<Tooltip title="组合货品">
								<span className="r-warehouse-combined">组</span>
							</Tooltip>
						) : ''}
					</div>
					{/* <div className="icon-content"><PlatformIcon platform={ record.platformAlias } /></div> */}
				</div>
				<div className="extra">
					<span className="extra-title">货品规格编码：</span>
					<span className="extra-content">{record.sysOuterSkuId}</span>
				</div>
			</div>
		</div>
	);
};

const renderSkuBelongGoods = (_, record) => {
	return (
		<div>
			<Tooltip title={ record.itemTitle }>
				<div className={ styles["title"] }>{record.itemTitle}</div>
			</Tooltip>
		</div>
	);

};

const renderSysSkuBelongGoods = (_, record) => {
	return (
		<div>
			<Tooltip title={ record.sysItemAlias }>
				<div className={ styles["title"] }>{record.sysItemAlias}</div>
			</Tooltip>
		</div>
	);

};

const getFixed2Num = (text) => {
	if (["", null, undefined, '-'].includes(text)) {
		return "-";
	}
	return Number(text || 0).toFixed(2);
};

const getNum = (text) => {
	if (["", null, undefined, '-'].includes(text)) {
		return "-";
	}
	return text;
};

const renderCostPriceFieldsPermissionComp = (node: ReactNode, noPermissionRenderNode: ReactNode) => {
	const { hasCostPricePermission } = userStore;
	if (!hasCostPricePermission) {
		return (
			<FieldsPermissionCheck
				fieldsPermission={ FieldsPermissionEnum.成本价 }
				type={ FieldsPermissionCheckTypeEnum.仅展示 }
				noPermissionRender={ noPermissionRenderNode }
			>
				{node}
			</FieldsPermissionCheck>
		);
	} else {
		return node;
	}
};

const checkCostPriceFieldsArr: React.Key[] = ['paymentCost', 'paymentProfit', 'paymentProfitMargin', 'actualCost', 'refundCost', 'netSalesCost', 'netSalesProfit', 'netSalesProfitMargin'];

// 表格列定义
const defaultColumns = (tableType) => {
	const isSysItem = [TabEnum.按货品, TabEnum.按货品SKU].includes(tableType);
	const actualNumberText = isSysItem ? '通过助手ERP发货的货品数量' : '通过助手ERP发货的商品数量';
	const hasRefundNumText = isSysItem ? '平台退货成功的订单且在系统确认收货的货品数量' : '平台退货成功的订单产生的商品数量';
	return	[
		{
			title: '',
			width: 6,
			dataIndex: 'index',
			key: 'index',
			align: 'center',
			fixed: "left",
			render: (_, __, index) => index + 1
		},
		{
			title: '销售数量',
			width: 24,
			dataIndex: 'number',
			key: 'number',
			align: 'center',
			render(text) {
				return getNum(text);
			}
		},
		{
			title: '销售订单数',
			width: 18,
			dataIndex: 'tidCount',
			key: 'tidCount',
			align: 'center',
			render(text) {
				return getNum(text);
			}
		},
		{
			title: tooltip('销售金额', '订单支付成功的商家实收金额，包含售后订单'),
			width: 18,
			dataIndex: 'payment',
			key: 'payment',
			align: 'center',
			render(text) {
				return getFixed2Num(text);
			}
		},
		{
			title: tooltip('邮费收入', '订单支付成功的订单邮费收入金额，包含售后订单'),
			width: 18,
			dataIndex: 'postFee',
			key: 'postFee',
			align: 'center',
			render(text) {
				return getFixed2Num(text);
			}
		},
		{
			title: tooltip('平台承担金额', '订单支付成功的平台承担金额，包含售后订单'),
			width: 16,
			dataIndex: 'platformDiscount',
			key: 'platformDiscount',
			align: 'center',
			render(text) {
				return getFixed2Num(text);
			}
		},
		{
			title: tooltip('实发金额', '即实发数量*成本价'),
			width: 18,
			dataIndex: 'actualPayment',
			key: 'actualPayment',
			align: 'center',
			render(text) {
				return getFixed2Num(text);
			}
		},
		{
			title: tooltip('销售成本', '即销售数量*成本价'),
			width: 18,
			dataIndex: 'paymentCost',
			key: 'paymentCost',
			align: 'center',
			render(text) {
				return renderCostPriceFieldsPermissionComp(text, text);
			}
		},
		{
			title: tooltip('销售毛利', '销售毛利=销售金额-销售成本'),
			width: 18,
			dataIndex: 'paymentProfit',
			key: 'paymentProfit',
			align: 'center',
			render(text) {
				return renderCostPriceFieldsPermissionComp(getFixed2Num(text), text);
			}
		},
		{
			title: tooltip('销售毛利率', '销售毛利率=销售毛利/销售金额'),
			width: 22,
			dataIndex: 'paymentProfitMargin',
			key: 'paymentProfitMargin',
			align: 'center',
			render(text) {
				if (["", null, undefined].includes(text)) {
					return "-";
				}
				return renderCostPriceFieldsPermissionComp(text + '%', text);
			}
		},
		{
			title: tooltip('退款金额', '平台退款成功的订单产生的退款金额'),
			width: 18,
			dataIndex: 'refundAmount',
			key: 'refundAmount',
			align: 'center',
			render(text) {
				return getFixed2Num(text);
			}
		},
		{
			title: tooltip('发货前退款金额', '平台退款成功且未发货的订单产生的退款金额'),
			width: 20,
			dataIndex: 'beforeRefundAmount',
			key: 'beforeRefundAmount',
			align: 'center',
			render(text) {
				return getFixed2Num(text);
			}
		},
		{
			title: tooltip('发货后退款金额', '平台退款成功且已发货的订单产生的退款金额'),
			width: 20,
			dataIndex: 'afterRefundAmount',
			key: 'afterRefundAmount',
			align: 'center',
			render(text) {
				return getFixed2Num(text);
			}
		},
		{
			title: tooltip('商品数量', '商品数量=实发数量-退货数量'),
			width: 16,
			dataIndex: 'itemNum',
			key: 'itemNum',
			align: 'center',
			render(text) {
				return getNum(text);
			}
		},
		{
			title: tooltip('实发数量', actualNumberText),
			width: 18,
			dataIndex: 'actualNumber',
			key: 'actualNumber',
			align: 'center',
			render(text) {
				return getNum(text);
			}
		},
		{
			title: tooltip('退货数量', hasRefundNumText),
			width: 18,
			dataIndex: 'hasRefundNum',
			key: 'hasRefundNum',
			align: 'center',
			toFixed: 0,
			render(text) {
				return getNum(text);
			}
		},
		{
			title: tooltip('商品成本', '商品成本=实发商品成本-退货商品成本'),
			width: 24,
			dataIndex: 'netSalesCost',
			key: 'netSalesCost',
			align: 'center',
			toFixed: 4,
			render(text) {
				return renderCostPriceFieldsPermissionComp(getFixed2Num(text), text);
			}
		},
		{
			title: tooltip('实发商品成本', '即实发数量*成本价'),
			width: 18,
			dataIndex: 'actualCost',
			key: 'actualCost',
			align: 'center',
			render(text) {
				return getFixed2Num(text);
			}
		},

		{
			title: tooltip('退货商品成本', '即退货数量*成本价'),
			width: 24,
			dataIndex: 'refundCost',
			key: 'refundCost',
			align: 'center',
			toFixed: 4,
			render(text) {
				return renderCostPriceFieldsPermissionComp(text, text);
			}
		},
		{
			title: tooltip('运费', '根据运费模板设置计算的理论运费，仅计算通过助手ERP发货订单。'),
			width: 18,
			dataIndex: 'postCost',
			key: 'postCost',
			align: 'center',
			render(text, record) {
				return getFixed2Num(text);
			}
		},
		{
			title: tooltip('收入', '收入=销售金额-退款金额'),
			width: 18,
			dataIndex: 'income',
			key: 'income',
			align: 'center',
			render(text) {
				return renderCostPriceFieldsPermissionComp(getFixed2Num(text), text);
			}
		},
		{
			title: tooltip('成本费用', '成本费用=商品成本+运费'),
			width: 18,
			dataIndex: 'costPrice',
			key: 'costPrice',
			align: 'center',
			render(text) {
				return getFixed2Num(text);
			}
		},
		{
			title: tooltip('利润', '利润=收入-成本费用'),
			width: 14,
			dataIndex: 'netSalesProfit',
			key: 'netSalesProfit',
			align: 'center',
			render(text) {
				return renderCostPriceFieldsPermissionComp(getFixed2Num(text), text);
			}
		},
		{
			title: tooltip('利润率', '利润率=利润/收入'),
			width: 14,
			dataIndex: 'netSalesProfitMargin',
			key: 'netSalesProfitMargin',
			align: 'center',
			render: (value, row, index) => {
				return renderCostPriceFieldsPermissionComp(value + '%', value);
			},
		}
	];
};

// 处理多重表头
const handleMoreColumn = tableType => {
	const isSysItem = [TabEnum.按货品, TabEnum.按货品SKU].includes(tableType);
	const actualNumberText = isSysItem ? '通过助手ERP发货的货品数量' : '通过助手ERP发货的商品数量';
	const hasRefundNumText = isSysItem ? '平台退货成功的订单且在系统确认收货的货品数量' : '平台退货成功的订单产生的商品数量';
	return	[
		{
			title: '',
			width: 40,
			dataIndex: 'index',
			key: 'index',
			align: 'center',
			fixed: "left",
			render: (_, { rewrite }, index) => {
				return rewrite ? '' : index + 1;
			}
		},
		{
			title: '销售',
			align: 'center',
			children: [{
				title: '销售数量',
				width: 106,
				dataIndex: 'number',
				key: 'number',
				align: 'right',
				render(text) {
					return getNum(text);
				}
			},
			{
				title: '销售订单数',
				width: 96,
				dataIndex: 'tidCount',
				key: 'tidCount',
				align: 'right',
				render(text) {
					return getNum(text);
				}
			},
			{
				title: tooltip('销售金额', '订单支付成功的商家实收金额，包含售后订单'),
				width: 89,
				dataIndex: 'payment',
				key: 'payment',
				align: 'right',
				render(text) {
					return getFixed2Num(text);
				}
			},
			{
				title: tooltip('邮费收入', '订单支付成功的订单邮费收入金额，包含售后订单'),
				width: 89,
				dataIndex: 'postFee',
				key: 'postFee',
				align: 'right',
				render(text) {
					return getFixed2Num(text);
				}
			},
			{
				title: tooltip('平台承担金额', '订单支付成功的平台承担金额，包含售后订单'),
				width: 106,
				dataIndex: 'platformDiscount',
				key: 'platformDiscount',
				align: 'center',
				render(text) {
					return getFixed2Num(text);
				}
			},
			{
				title: tooltip('实发金额', '当前筛选条件下，通过助手ERP发货的销售金额'),
				width: 89,
				dataIndex: 'actualPayment',
				key: 'actualPayment',
				align: 'right',
				render(text) {
					return getFixed2Num(text);
				}
			}]
		},
		{
			title: tooltip('销售成本', '即销售数量*成本价'),
			width: 89,
			dataIndex: 'paymentCost',
			key: 'paymentCost',
			align: 'right',
			render(text) {
				return renderCostPriceFieldsPermissionComp(text, text);
			}
		},
		{
			title: '销售毛利',
			align: 'center',
			children: [{
				title: tooltip('销售毛利', '销售毛利=销售金额-销售成本'),
				width: 89,
				dataIndex: 'paymentProfit',
				key: 'paymentProfit',
				align: 'right',
				render(text) {
					return renderCostPriceFieldsPermissionComp(getFixed2Num(text), text);
				}
			},
			{
				title: tooltip('销售毛利率', '销售毛利率=销售毛利/销售金额'),
				width: 99,
				dataIndex: 'paymentProfitMargin',
				key: 'paymentProfitMargin',
				align: 'right',
				render(text, { rewrite }) {
					if (["", null, undefined, '-'].includes(text)) {
						return "-";
					}
					return renderCostPriceFieldsPermissionComp(rewrite ? text : text + '%', text);
				}
			}]
		},
		{
			title: '退款',
			align: 'center',
			children: [{
				title: tooltip('退款金额', '平台退款成功的订单产生的退款金额'),
				width: 89,
				dataIndex: 'refundAmount',
				key: 'refundAmount',
				align: 'right',
				render(text) {
					return getFixed2Num(text);
				}
			},
			{
				title: tooltip('发货前退款金额', '平台退款成功且未发货的订单产生的退款金额'),
				width: 119,
				dataIndex: 'beforeRefundAmount',
				key: 'beforeRefundAmount',
				align: 'right',
				render(text) {
					return getFixed2Num(text);
				}
			},
			{
				title: tooltip('发货后退款金额', '平台退款成功且已发货的订单产生的退款金额'),
				width: 119,
				dataIndex: 'afterRefundAmount',
				key: 'afterRefundAmount',
				align: 'right',
				render(text) {
					return getFixed2Num(text);
				}
			}]
		},
		{
			title: '商品成本',
			align: 'center',
			children: [{
				title: tooltip('商品数量', '商品数量=实发数量-退货数量'),
				width: 89,
				dataIndex: 'itemNum',
				key: 'itemNum',
				align: 'right',
				render(text) {
					return getNum(text);
				}
			},
			{
				title: tooltip('实发数量', actualNumberText),
				width: 89,
				dataIndex: 'actualNumber',
				key: 'actualNumber',
				align: 'right',
				render(text) {
					return getNum(text);
				}
			},
			{
				title: tooltip('退货数量', hasRefundNumText),
				width: 89,
				dataIndex: 'hasRefundNum',
				key: 'hasRefundNum',
				align: 'right',
				toFixed: 0,
				render(text) {
					return getNum(text);
				}
			},
			{
				title: tooltip('商品成本', '商品成本=实发商品成本-退货商品成本'),
				width: 89,
				dataIndex: 'netSalesCost',
				key: 'netSalesCost',
				align: 'right',
				toFixed: 4,
				render(text) {
					return renderCostPriceFieldsPermissionComp(getFixed2Num(text), text);
				}
			},
			{
				title: tooltip('实发商品成本', '即实发数量*成本价'),
				width: 109,
				dataIndex: 'actualCost',
				key: 'actualCost',
				align: 'right',
				toFixed: 4,
				render(text) {
					return renderCostPriceFieldsPermissionComp(text, text);
				}
			},
			{
				title: tooltip('退货商品成本', '即退货数量*成本价'),
				width: 109,
				dataIndex: 'refundCost',
				key: 'refundCost',
				align: 'right',
				toFixed: 4,
				render(text) {
					return renderCostPriceFieldsPermissionComp(text, text);
				}
			}]
		},

		{
			title: tooltip('运费', '根据运费模板设置计算的理论运费，仅计算通过助手ERP发货订单。'),
			width: 72,
			dataIndex: 'postCost',
			key: 'postCost',
			align: 'right',
			render(text) {
				return getFixed2Num(text);
			}
		},

		{
			title: '利润',
			align: 'center',
			children: [{
				title: tooltip('收入', '收入=销售金额-退款金额'),
				width: 120,
				dataIndex: 'income',
				key: 'income',
				align: 'right',
				render(text) {
					return renderCostPriceFieldsPermissionComp(getFixed2Num(text), text);
				}
			},
			{
				title: tooltip('成本费用', '成本费用=商品成本+运费'),
				width: 120,
				dataIndex: 'costPrice',
				key: 'costPrice',
				align: 'right',
				render(text) {
					return getFixed2Num(text);
				}
			},
			{
				title: tooltip('利润', '利润=收入-成本费用'),
				width: 120,
				dataIndex: 'netSalesProfit',
				key: 'netSalesProfit',
				align: 'right',
				render(text) {
					return renderCostPriceFieldsPermissionComp(getFixed2Num(text), text);
				}
			},
			{
				title: tooltip('利润率', '利润率=利润/收入'),
				width: 105,
				dataIndex: 'netSalesProfitMargin',
				key: 'netSalesProfitMargin',
				align: 'right',
				render: (value, { rewrite }, index) => {
					return renderCostPriceFieldsPermissionComp(rewrite ? value : value + '%', value);
				},
			}]
		}
	];
};


const addCol:any = {
	summary: [{
		title: "日期",
		dataIndex: "date",
		fixed: "left",
		width: 92,
		render(_, record) {
			return record ? <div>{_}</div> : _;
		}
	}],
	platform: [
		{
			title: '平台',
			width: 108,
			dataIndex: 'platform',
			key: 'platform',
			fixed: 'left',
			align: 'center',
			render: (value:string, row:any) => {
				return (
					<div className="r-flex r-js-c r-ai-c">
						<PlatformIcon platform={ row.platformAlias } />
						<span className="r-bold r-mr-4">{value}</span>
					</div>
				);
			}
		}
	],
	shop: [
		{
			title: '店铺',
			width: 208,
			fixed: 'left',
			dataIndex: 'sellerNick',
			key: 'sellerNick',
			align: 'center',
			render: (value:string, row:any) => {
				return (
					<div className="r-flex r-js-c r-ai-c">
						<PlatformIcon platform={ row.platformAlias } />
						<span className="r-bold r-mr-4">{value}</span>
					</div>
				);
			}
		}
	],
	item: [
		{
			title: '商品信息',
			width: 280,
			fixed: 'left',
			align: 'center',
			render: (_, record) => {
				const { rewrite } = record;
				return rewrite ? _ : renderGoodsInfo(record);
			}
		},
		{
			title: <div>平台店铺</div>,
			width: 208,
			dataIndex: 'sellerNickAndPlatform',
			key: 'sellerNickAndPlatform',
			align: 'center',
			render: (value: string, row: any) => {
				const { rewrite } = row;
				return rewrite ? value || '-' : (
					<div className="r-flex r-js-c r-ai-fs">
						<PlatformIcon platform={ row.platformAlias } />
						<span className="r-mr-4">{row.sellerNick}</span>
					</div>
				);
			}
		}
	],
	itemSKU: [
		{
			title: 'SKU信息',
			width: 280,
			align: 'center',
			fixed: 'left',
			render: (_, record) => {
				const { rewrite } = record;
				return rewrite ? '合计' : renderSkuInfo(record, true);
			}
		},
		{
			title: <div>所属商品</div>,
			width: 140,
			align: 'center',
			render(_, record) {
				const { rewrite } = record;
				return rewrite ? _ : renderSkuBelongGoods(_, record);
			}
		},
		{
			title: <div>平台店铺</div>,
			width: 208,
			dataIndex: 'sellerNickAndPlatform',
			key: 'sellerNickAndPlatform',
			align: 'center',
			render: (value: string, row: any) => {
				const { rewrite } = row;
				return rewrite ? value || '-' : (
					<div className="r-flex r-js-c r-ai-fs">
						<PlatformIcon platform={ row.platformAlias } />
						<span className="r-mr-4">{row.sellerNick}</span>
					</div>
				);
			}
		}
	],
	sysItem: [
		{
			title: '货品信息',
			width: 280,
			dataIndex: 'sysItemAlias',
			key: 'sysItemAlias',
			align: 'center',
			fixed: 'left',
			render(_, record) {
				const { rewrite } = record;
				return rewrite ? '合计' : renderSysGoodsInfo(_, record);
			}
		}
	],
	sysItemSKU: [
		{
			title: '货品规格信息',
			width: 280,
			dataIndex: 'sysItemAlias',
			key: 'sysItemAlias',
			align: 'center',
			fixed: 'left',
			render(_, record) {
				const { rewrite } = record;
				return rewrite ? '合计' : renderSysGoodsSkuInfo(_, record);
			}
		},
		{
			title: <div>所属货品</div>,
			width: 140,
			dataIndex: 'sysSkuBelongGoods',
			key: 'sysSkuBelongGoods',
			align: 'center',
			render(_, record) {
				const { rewrite } = record;
				return rewrite ? _ || '-' : renderSysSkuBelongGoods(_, record);
			}
		}
	],
	tidList: [
		{
			title: '系统编号',
			width: 160,
			dataIndex: 'tid',
			key: 'tid',
			align: 'center',
			fixed: 'left',
			render: (value:string, row:any) => {
				return (
					<div className="r-flex r-js-c r-ai-c">
						<span className="r-bold r-mr-4">{value}</span>
					</div>
				);
			}
		},
		{
			title: <div>平台店铺</div>,
			width: 160,
			dataIndex: 'sellerNickAndPlatform',
			key: 'sellerNickAndPlatform',
			align: 'center',
			render: (value: string, row: any) => {
				const { rewrite } = row;
				return rewrite ? value || '-' : (
					<div className="r-flex r-js-c r-ai-fs">
						<PlatformIcon platform={ row.platformAlias } />
						<span className="r-mr-4">{row.sellerNick}</span>
					</div>
				);
			}
		}
	],
	order: [
		{
			title: '系统编号',
			width: 160,
			dataIndex: 'tid',
			key: 'tid',
			align: 'center',
			fixed: 'left',
			render: (value:string, row:any) => {
				return (
					<div className="r-flex r-js-c r-ai-c">
						<span className="r-bold r-mr-4">{value}</span>
					</div>
				);
			}
		}, {
			title: '子系统编号',
			width: 160,
			dataIndex: 'orderId',
			key: 'orderId',
			align: 'center',
			render: (value: string, row: any) => {
				const { rewrite } = row;
				return rewrite ? '-' : (
					<Tooltip title={ value }>
						<div className="r-flex r-js-c r-ai-c">
							<span className="r-bold r-mr-4 ellipsis">{value}</span>
						</div>
					</Tooltip>

				);
			}
		}, {
			title: 'SKU信息',
			width: 220,
			align: 'center',
			render: (_, record) => {
				const { rewrite } = record;
				return rewrite ? _ : renderSkuInfo(record, true);
			}
		}, {
			title: <div>所属商品</div>,
			width: 180,
			align: 'center',
			render: (_, record) => {
				const { rewrite } = record;
				return rewrite ? '-' : renderGoodsInfo(record, true);
			}
		},
		{
			title: <div>平台店铺</div>,
			width: 160,
			dataIndex: 'sellerNickAndPlatform',
			key: 'sellerNickAndPlatform',
			align: 'center',
			render: (value: string, row: any) => {
				const { rewrite } = row;
				return rewrite ? '-' : (
					<div className="r-flex r-js-c r-ai-fs">
						<PlatformIcon platform={ row.platformAlias } />
						<span className="r-mr-4">{row.sellerNick}</span>
					</div>
				);
			}
		}
	]
};

const GrossProfitTable = observer((props:GrossProfitTableProps) => {
	const { loading = false, tableData, tableType = TabEnum.按平台, searchParams, pageSize, pageNo, onPageSizeChange, onPageChange } = props;
	const { hasCostPricePermission } = userStore;
	const [warnList, setWarnList] = useState([]);
	const [allIndex, setAllIndex] = useState(0);
	const [columns, setColumns] = useState<ColumnsType<unknown>>([]);

	const [columnsHead, setColumnsHead] = useState<ColumnsType<unknown>>([]);
	const [total, setTotal] = useState(0);
	const emptyText = useMemo(() => (() => {
		return !tableData?.['list'] ? (
			<div style={ { width: '100%', height: '200px', textAlign: 'center' } } className={ cs('r-flex', 'r-fd-c', 'r-jc-c', 'r-ai-c') }>
				<Empty
					description={
						<p>请点击查询，查看报表数据</p>
					}
				/>
			</div>
		) : (
			<div style={ { width: '100%', height: '200px', textAlign: 'center' } } className={ cs('r-flex', 'r-fd-c', 'r-jc-c', 'r-ai-c') }>
				<Empty
					image={ Empty.PRESENTED_IMAGE_SIMPLE }
					description={
						<p>暂无数据</p>
					}
				/>
			</div>
		);
	}), [tableData]);
	// const onShowSizeChange = (current: number, pageSize: number) => {
	// 	// setSearchParams({
	// 	// 	pageSize,
	// 	// 	pageNo: current,
	// 	// });
	// };
	// const onPageChange = (cur: number) => {
	// 	// setSearchParams({
	// 	// 	pageNo: cur,
	// 	// });
	// };
	const pageConfig:any = {
		current: pageNo || 1,
		pageSize: pageSize || 10,
		// onShowSizeChange: onPageSizeChange,
		total: tableData['total'] || total,
		onChange: onPageChange,
		showSizeChanger: true,
		pageSizeOptions: ["10", "20", "50", "100", "200"],
		align: 'end'
	};

	const getSummary = (dataSource) => {

		const allColumnDataCount = dataSource.reduce((init, item) => {
			for (let key in item) {
				if (!(key in init)) {
					init[key] = 0;
					init[key] = Number(item[key]);
				} else {
					init[key] += Number(item[key]);
				}
			}
			return init;

		}, {});

		// console.log('allColumnDataCount.actualCost', allColumnDataCount);
		const render = (
			<>
				<Table.Summary.Row>
					{columns.map((columnItem:any, i) => {
						const toFixed = isNumber(columnItem.toFixed) ? columnItem.toFixed : 2;
						if (i == allIndex) {
							return (
								<Table.Summary.Cell index={ 0 } key={ columnItem['key'] || columnItem.dataIndex } colSpan={ 2 }>
									<div className="r-ta-c">
										<Text strong>合计</Text>
									</div>

								</Table.Summary.Cell>
							);
						} else if (['orderId', 'sysSkuBelongGoods', "sellerNickAndPlatform"].includes(columnItem.key)) {
							return (<Table.Summary.Cell index={ i } key={ columnItem['key'] || columnItem.dataIndex } className="r-ta-c">-</Table.Summary.Cell>);
						} else if (checkCostPriceFieldsArr.includes(columnItem.key) && !hasCostPricePermission) {
							return (
								<Table.Summary.Cell index={ i } align="center" key={ columnItem.key || columnItem.dataIndex }>
									<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 } type={ FieldsPermissionCheckTypeEnum.仅展示 } noPermissionRender="***">***</FieldsPermissionCheck>
								</Table.Summary.Cell>
							);
						} else {
							// 利润率
							if (columnItem.key === "netSalesProfitMargin") {
								let netSalesProfitMarginCount = (allColumnDataCount.netSalesProfit / allColumnDataCount.income) * 100;
								if (allColumnDataCount.netSalesProfit == 0 || allColumnDataCount.income == 0) {
									netSalesProfitMarginCount = 0;
								}
								return (
									<Table.Summary.Cell index={ i } align="center" key={ columnItem.key }>
										{
											netSalesProfitMarginCount.toFixed && (netSalesProfitMarginCount.toFixed(2) + "%")
										}
									</Table.Summary.Cell>
								);
							}
							// 销售毛利率
							if (columnItem.key === "paymentProfitMargin") {
								let paymentProfitMarginCount = (allColumnDataCount.paymentProfit / allColumnDataCount.payment) * 100;
								if (allColumnDataCount.paymentProfit == 0 || allColumnDataCount.payment == 0) {
									paymentProfitMarginCount = 0;
								}
								return (
									<Table.Summary.Cell index={ i } align="center" key={ columnItem.key || columnItem.dataIndex }>
										{
											paymentProfitMarginCount.toFixed && (paymentProfitMarginCount.toFixed(2) + "%")
										}
									</Table.Summary.Cell>
								);
							}

							return (
								<Table.Summary.Cell index={ i } colSpan={ i === 1 ? 0 : 1 } align="center" key={ columnItem.key || columnItem.dataIndex }>
									{
										toFixed === 4 && allColumnDataCount[columnItem.key]?.toFixed ? (Number((allColumnDataCount[columnItem.key]?.toFixed && allColumnDataCount[columnItem.key].toFixed(toFixed))) || "-") : (allColumnDataCount[columnItem.key]?.toFixed && allColumnDataCount[columnItem.key].toFixed(toFixed)) || '-'
									}
								</Table.Summary.Cell>
							);
						}
					})}
				</Table.Summary.Row>
			</>
		);
		console.log('columnscolumns', columns);

		return (
			dataSource.length ? render : <></>
		);
	};

	useEffect(() => {
		// 计算不同tableType下的表格渲染数据
		let cols = [...defaultColumns(tableType)];
		// 平台和店铺去掉订单数量
		// if ([TabEnum.按平台, TabEnum.按店铺, TabEnum.按订单, TabEnum.按订单商品].includes(tableType)) {
		// 	cols.splice(9, 1);
		// }
		let colsHead = [...handleMoreColumn(tableType)];
		cols.splice(1, 0, ...addCol[tableType]);
		setColumns(cols);
		// 单独处理表头逻辑，其他逻辑不动
		colsHead.splice(1, 0, ...addCol[tableType]);
		setColumnsHead(colsHead);
		setWarnList(tableData['list']);
		setAllIndex(0);
		tableData['total'] && setTotal(tableData['total']);
	}, [tableData, tableType]);


	const footerData = (dataSource) => {

		const allColumnDataCount = dataSource.reduce((init, item) => {
			for (let key in item) {
				if (!(key in init)) {
					init[key] = 0;
					init[key] = Number(item[key]);
				} else {
					init[key] += Number(item[key]);
				}
			}
			return init;

		}, {});
		allColumnDataCount['rewrite'] = true;
		columns.forEach((col, index) => {
			const toFixed = isNumber(col.toFixed) ? col.toFixed : 2;
			const { key, dataIndex, } = col;
			const columnKey = key || dataIndex;
			if (index == allIndex + 1) {
				allColumnDataCount[columnKey] = '合计';
			} else if (['orderId', 'sysSkuBelongGoods', "sellerNickAndPlatform"].includes(columnKey)) {
				allColumnDataCount[columnKey] = '';
			} else if (checkCostPriceFieldsArr.includes(columnKey) && !hasCostPricePermission) {
				allColumnDataCount[columnKey] = '***';
			} else {
				// 利润率
				if (columnKey === "netSalesProfitMargin") {
					let netSalesProfitMarginCount = (allColumnDataCount.netSalesProfit / allColumnDataCount.income) * 100;
					if (allColumnDataCount.netSalesProfit == 0 || allColumnDataCount.income == 0) {
						netSalesProfitMarginCount = 0;
					}
					allColumnDataCount[columnKey] = netSalesProfitMarginCount.toFixed && (netSalesProfitMarginCount.toFixed(2) + "%");
					return;
				}

				// 销售毛利率
				if (columnKey === "paymentProfitMargin") {
					let paymentProfitMarginCount = (allColumnDataCount.paymentProfit / allColumnDataCount.payment) * 100;
					if (allColumnDataCount.paymentProfit == 0 || allColumnDataCount.payment == 0) {
						paymentProfitMarginCount = 0;
					}
					allColumnDataCount[columnKey] = paymentProfitMarginCount.toFixed && (paymentProfitMarginCount.toFixed(2) + "%");
					return;
				}

				allColumnDataCount[columnKey] = toFixed === 4 && allColumnDataCount[columnKey]?.toFixed ? (Number((allColumnDataCount[columnKey]?.toFixed && allColumnDataCount[columnKey].toFixed(toFixed))) || "-") : (allColumnDataCount[columnKey]?.toFixed && allColumnDataCount[columnKey].toFixed(toFixed)) || '-';
			}
		});
		return dataSource?.length > 0 ? [allColumnDataCount] : [];
	};

	return (
		<div className={ cs("table-container", styles['table-container']) }>
			<BasicTable
				rowKey={ (record: any) => {
					const keys = ['date', 'platformAlias', 'sellerNick', 'itemId', 'skuId', 'sysItemId', 'sysSkuId', 'tid', 'orderId', 'skuId'].map(s => record[s])?.join('_');
					return `${props.currentKey}_${keys}`;
				} }
				columns={ columnsHead }
				dataSource={ warnList }
				loading={ loading }
				sticky={ { stickyTop: props.stickyHeight } }
				pagination={ pageConfig }
				summary={ footerData }
				components={
					{
						EmptyContent: emptyText
					}
				}
				paddingBottom={ 20 }
			/>
			{/* <Table
				className={ styles["table"] }
				// bordered
				// size="small"
				rowKey={ (record: any) => {
					const keys = ['date', 'platformAlias', 'sellerNick', 'itemId', 'skuId', 'sysItemId', 'sysSkuId', 'tid', 'orderId', 'skuId'].map(s => record[s])?.join('_');
					return `${props.currentKey}_${keys}`;
				} }
				scroll={ {
					scrollToFirstRowOnChange: true,
					x: 2600,
					// y: 300,// 设置表格的垂直滚动高度
				} }
				columns={ columnsHead }
				// pagination={ pageConfig }
				dataSource={ warnList }
				loading={ loading }
				locale={ {
					emptyText
				} }
				summary={ getSummary }
				pagination={ false } // 关闭默认的分页器
				sticky={ { offsetHeader: props.stickyHeight, offsetScroll: 52 } }
			/> */}
			{/* <div className={ cs(styles["table-pagination"]) }>
				<Pagination
					{ ...pageConfig }
				/>
			</div> */}
		</div>
	);
});
export default memo(GrossProfitTable);
