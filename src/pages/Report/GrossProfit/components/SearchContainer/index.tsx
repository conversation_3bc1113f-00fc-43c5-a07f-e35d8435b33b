import React, { memo, useEffect, useRef, Fragment, forwardRef, useImperativeHandle, useState, useCallback, useMemo, CSSProperties, useLayoutEffect } from "react";
import { message, Button, Form, Row, Col, Card, Checkbox, Tooltip, Select, ColProps, RowProps, FormItemProps } from "antd";
import dayjs, { Dayjs } from 'dayjs';
import { observer } from "mobx-react";
import cs from "classnames";
import { ExclamationCircleOutlined, UpOutlined } from "@ant-design/icons";
import Icon from "@/components/Icon";
import Input from '@/components/Input/InputSearch';
import HighLightSelect from "@/components-biz/HighLightSelect";
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import EnumSelect from '@/components/Select/EnumSelect';
import ShopMultiSelect, { ShopMultiSelectValue } from '@/components-biz/ShopListSelect/shopMultiSelect';
import EnumStringSelect from '@/components/Select/EnumSelect/EnumStringSelect';
import { BrandSelect } from '@/components-biz/Product/Brand';
import { GroupSelect } from "@/components-biz/Product/Group";
import { IsContainGoodsOrSys } from '@/pages/Trade/LabelAccountCheck/components/SearchContainer/SearchCondition';
import userStore from "@/stores/user";
import { PLAT_HAND } from "@/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { getSaleProfitConfigApi, saveSaleProfitConfigApi } from "@/apis/report/grossProfit";
import { getPlatAndShops } from "@/components-biz/ShopListSelect/shopListUtils";
import { OrderSourceType } from "@/pages/Trade/components/SearchContainer/SearchCondition";
import InputMulti from "@/components/Input/InputMulti";
import QuickDatePicker from "../../../SalesAnalysis/components/QuickDatePicker";
import { SalesAnalysisUpdateTimeApi } from "@/apis/report/salesAnalysis";
import DataConfigurationFrom from "../DataConfigurationFrom";
import s from './index.module.scss';

interface SearchContainerProps {
	handleSearch: (params :any, isRequestTotal?: boolean) => void
	handleReset: (params: any) => void
	handleSetDataConfigura?: (params: any) => void
	loading?:boolean
	tableType: string,
	ref:React.Ref<any>
}
// 查询时间类型
enum TimeType {
    下单时间=1,
    付款时间=2,
    发货时间=3,
}
// 订单状态
const TradeStatus = {
	// 待付款: 'WAIT_BUYER_PAY',
	待发货: 'WAIT_SELLER_SEND_GOODS',
	已发货: 'WAIT_BUYER_CONFIRM_GOODS',
	交易成功: 'TRADE_FINISHED',
	全部订单: 'ALL_STATUS',
};
// const TradeStatus = [
// 	{ name: '待付款', value: 'WAIT_BUYER_PAY' },
// 	{ name: '待发货', value: 'WAIT_SELLER_SEND_GOODS' },
// 	{ name: '已发货', value: 'WAIT_BUYER_CONFIRM_GOODS' },
// 	{ name: '交易成功', value: 'TRADE_FINISHED' },
// 	{ name: '全部订单', value: 'ALL_STATUS' }
// ];
interface DataConfiguraProps {
	pddPlatformDiscount: boolean,
	fxgPlatformDiscount: boolean
}


const initialValues = {
	queryTimeType: 2,
	searchTime: [dayjs().subtract(1, "M").add(1, 'day').startOf('day'), dayjs().endOf('day')]
};
const format = 'YYYY-MM-DD HH:mm:ss';
const SearchContainer:React.FC<SearchContainerProps> = forwardRef((props:SearchContainerProps, ref) => {
	const { handleSearch, handleSetDataConfigura, loading = false, tableType } = props;
	const { isShowZeroStockVersion, userInfo } = userStore;
	const [form] = Form.useForm();
	const [dataUpdateDate, setDataUpdateDate] = useState("---- - -- - -- --:--:--");
	const [expand, setExpand] = useState(false);
	const [dataConfigura, setDataConfigura] = useState<DataConfiguraProps>({ pddPlatformDiscount: true, fxgPlatformDiscount: true });

	const onFinish = async(values:any) => {
		const params = await getParams(values);
		console.log('%c [ params ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
		if (!params.startTime && !params.endTime) {
			message.error("查询时间不能为空");
			return;
		}
		if (!params.startTime) {
			message.error("开始时间不能为空");
			return;
		}
		if (!params.endTime) {
			message.error("结束时间不能为空");
			return;
		}
		params.orderSource = params.orderSource || undefined;
		handleSearch && handleSearch(params, true);
	};
	const getParams = useCallback(async(value:any) => {
		const {
			searchTime,
			shopInfo,
			brandId,
			goodsObj,
			isSplit,
		} = value;
		const [startDate, endDate] = searchTime || [dayjs().subtract(7, 'day'), dayjs().subtract(1, 'day')];
		const { pddPlatformDiscount, fxgPlatformDiscount } = dataConfigura;
		const { shopId, platform } = await getPlatAndShops(value.shopInfo, true);
		const params = {
			...value,
			pddPlatformDiscount,
			fxgPlatformDiscount,
			startTime: startDate ? dayjs(startDate).startOf("d").format(format) : undefined, // 整天0-24
			endTime: endDate ? dayjs(endDate).endOf("d").format(format) : undefined,
			platforms: platform,
			sellerNick: shopId,
			brandIds: brandId?.split(",") || [],
			isSplit: isSplit ? 1 : 0,
			pageNo: 1,
		};
		if (params.platforms.includes(PLAT_HAND)) {
			params.sellerNick.push(userInfo.userId);
		}
		if (goodsObj) {
			switch (goodsObj.type) {
				case "1":
					params["equalFlag"]	 = 1;
					params["shortNameIncluding"] = goodsObj.itemList;
					params["skuIncluding"] = goodsObj.skuList;
					break;
				case "2":
					params["equalFlag"]	 = 0;
					params["shortNameIncluding"] = goodsObj.itemList;
					params["skuIncluding"] = goodsObj.skuList;
					break;
				case "3":
					params["goodsContain"]	 = true;
					params["goodsAliasOrIdStr"] = goodsObj.itemList;
					params["goodsSkuNameOrIdOrAliasStr"] = goodsObj.skuList;
					break;
				case "4":
					params["goodsContain"]	 = false;
					params["goodsAliasOrIdStr"] = goodsObj.itemList;
					params["goodsSkuNameOrIdOrAliasStr"] = goodsObj.skuList;
					break;

				default:
					params["equalFlag"]	 = "";
			}

		}
		return params;
	}, [dataConfigura, userInfo.userId]);

	useImperativeHandle(ref, () => {
		return {
			async getSearchParams(isSearch:boolean = true) {
				const params = await getParams(form.getFieldsValue());
				if (isSearch) {
					handleSearch && handleSearch(params, true);
				} else {
					return params;
				}
			}
		};
	}, [form, handleSearch, getParams]);

	const onReset = () => {
		resetForm();
	};
	//  重置
	const resetForm = () => {
		form.resetFields();
	};

	// 快捷日期范围选择
	const quickTypeOnChange = () => {
		form.validateFields().then(values => {
			onFinish(values);
		});
	};

	const handlDataUpdateTime = () => {
		SalesAnalysisUpdateTimeApi({}).then(res => setDataUpdateDate(res));
	};

	useEffect(() => {
		handlDataUpdateTime();
	}, []);

	// useLayoutEffect(() => {
	// 	quickTypeOnChange();
	// }, []);

	// 数据口径配置
	const getDataConfigura = useCallback(() => {
		getSaleProfitConfigApi().then(data => {
			if (!data) {
				handleSetDataConfigura && handleSetDataConfigura({});
				return;
			}
			const { config } = data;
			const { pddPlatformDiscount, fxgPlatformDiscount } = config;
			setDataConfigura({ pddPlatformDiscount, fxgPlatformDiscount });
			handleSetDataConfigura && handleSetDataConfigura({ pddPlatformDiscount, fxgPlatformDiscount });
		});
	}, []);

	// 打开数据口径配置回调
	const handleOnOpen = useCallback(() => {
		sendPoint(Pointer.报表_销售毛利润报表_数据口径配置);
	}, []);

	const handleDataConfigura = useCallback((val) => {
		setDataConfigura(val);
		// 保存接口
		const { pddPlatformDiscount, fxgPlatformDiscount } = val;
		const params = { pddPlatformDiscount, fxgPlatformDiscount, costPriceType: '0' };
		saveSaleProfitConfigApi({ config: params }).then(data => {
			console.log('data', data);
		});
	}, []);

	useEffect(() => {
		getDataConfigura();
	}, [getDataConfigura]);

	const disabledDate = (current) => {
		if (current > dayjs("2024-01-01") && current < dayjs().add(0, 'day').startOf('day')) {
			return false;
		} else {
			return true;
		}
	};

	return (
		<Form
			name="grossProfitSearch"
			form={ form }
			onFinish={ onFinish }
			initialValues={ initialValues }
			size="small"
			layout="horizontal"
			style={ { paddingTop: 8 } }
			className={ s.form }
		>
			<Row justify="space-between" align="middle">
				<Col>	
					<Row gutter={ 8 }>
						<Col>
							<Form.Item name="queryTimeType">
								<EnumSelect
									placeholder="查询时间"
									enum={ TimeType }
									style={ { width: 160 } }
									size="small"
								/>
							</Form.Item>
						</Col>
						<Col>
							<Row align="middle">
								<Col>
									<Form.Item name="searchTime">
										<QuickDatePicker quickTypeOnChange={ quickTypeOnChange } disabledDate={ disabledDate } />
									</Form.Item>
								</Col>
								<Col>
									<div className="r-fs-12 r-ml-8 r-mb-6" style={ { color: "#8c8c8d" } }>
										<ExclamationCircleOutlined className="r-m-lr-4" />
										数据更新至&nbsp;{dataUpdateDate}，每天凌晨6:00左右更新（首次查看需等待几分钟）
									</div>
								</Col>
							</Row>
						</Col>

						
					</Row>
				</Col>
				<Col>
					<div className={ s["search-expand-text"] } onClick={ () => setExpand(!expand) }>
						<span>{expand ? "收起" : "展开"}筛选</span>
						{expand}
						<UpOutlined className={ `${s["search-expand-icon"]} ${expand && s["search-expand-icon-active"]}` } />
					</div>
				</Col>
			</Row>
			<div className={ `${s["search-expand-container"]} ${expand && s["search-expand-container-active"]}` }>
				<Row gutter={ 8 }>
					<Col>
						<Form.Item name="shopInfo">
							<ShopMultiSelect
								style={ { width: '159px' } }
								isHasHandPlat
								size="small"
							/>
						</Form.Item>
					</Col>
					<Col>
						<Form.Item name="orderSource">
							<OrderSourceType size="small" />
						</Form.Item>
					</Col>
					<Col>
						<Form.Item name="orderStatus">
							<EnumStringSelect
								placeholder="订单状态"
								enum={ TradeStatus }
								style={ { width: 160 } }
								size="small"
							/>
						</Form.Item>
					</Col>
					<Col>
						<Form.Item name="tid">
							<InputMulti maxInputNum={ 500 } placeholder="订单编号" style={ { width: '100%}' } } size="small" />
						</Form.Item>
					</Col>

					<Col>
						<Form.Item name="refundStatusList">
							<HighLightSelect mode="multiple" placeholder="退款状态">
								{/* <Select.Option value="">
									退款状态
								</Select.Option> */}
								<Select.Option value="NOT_REFUND">
									无退款
								</Select.Option>
								<Select.Option value="REFUND_ING">
									退款中
								</Select.Option>
								<Select.Option value="REFUND_SUCCESSED">
									退款成功
								</Select.Option>
							</HighLightSelect>
						</Form.Item>
					</Col>
					<Col>
						<Form.Item name="authorInfo">
							<Input placeholder="达人名称/ID" />
						</Form.Item>
					</Col>
					<Col>
						<Form.Item name="goodsObj">
							<IsContainGoodsOrSys itemMaxNum={ 200 } skuMaxNum={ 200 } multiple={ false } />
						</Form.Item>
					</Col>
					{
						isShowZeroStockVersion ? "" : (
							<>
								<Col>
									<Form.Item
										name="brandId"
										style={ { marginBottom: 0 } }
									>
										<BrandSelect
											showSearch
											placeholder="选择品牌"
											filterOption={ (input, option) => option.children['toLowerCase']().indexOf(input.toLowerCase()) >= 0 }
											onChange={ (brandId: string) => form.setFieldsValue({ brandId }) }
											allowClear
										/>
									</Form.Item>
								</Col>

								<Col>
									<Form.Item
										name="classifyName"
										style={ { marginBottom: 0 } }
									>
										<GroupSelect
											placeholder="选择分类"
											showSearch
											filterOption={ (input, option) => option.children['toLowerCase']().indexOf(input.toLowerCase()) >= 0 }
											onChange={ (classifyId: string, option) => form.setFieldsValue({ classifyName: classifyId }) }
											allowClear
										/>
									</Form.Item>
								</Col>
							</>
						)
					}
					{/* 市场 */}
					<Col>
						<Form.Item name="market">
							<Input placeholder="市场" size="small" />
						</Form.Item>
					</Col>
					{/* 档口 */}
					<Col>
						<Form.Item name="stall">
							<Input placeholder="档口" size="small" />
						</Form.Item>
					</Col>
					{/* 供应商 */}
					<Col>
						<Form.Item name="supplierName">
							<Input placeholder="供应商" size="small" />
						</Form.Item>
					</Col>
					<Col>
						<Form.Item
							style={ {
								display: "flex",
								justifyContent: "flex-start",
							} }
							labelCol={ { span: 0 } }
							wrapperCol={ { span: 24 } }
						>
							<Button data-point={ Pointer.报表_销售毛利润报表_查询 } loading={ loading } type="primary" htmlType="submit" size="small">
								查询
							</Button>
							<Button
								size="small"
								onClick={ onReset }
								style={ { marginLeft: 12 } }
							>
								重置
							</Button>
						</Form.Item>
					</Col>
				</Row>
				<Row>
					{
						!isShowZeroStockVersion && (
							<Col className="r-flex r-ai-b">
								<Form.Item name="isSplit" valuePropName="checked" className="extra-checkbox">
									<Checkbox>组合货品拆分统计</Checkbox>
								</Form.Item>
								<Tooltip title="组合货品拆分统计后，销量、金额、成本等将按比例算到对应单品上。">
									<span className="r-c-ccc">
										<Icon type="wenhao-xian" size={ 15 } />
									</span>
								</Tooltip>
							</Col>
						)
					}

					<Col className="r-ml-16 r-flex r-ai-b">
						<Form.Item name="isExcludeEmptyTrade" valuePropName="checked" className="extra-checkbox">
							<Checkbox onClick={ () => sendPoint(Pointer.报表_销售毛利润报表_查询_排除空包订单) }>排除空包订单</Checkbox>
						</Form.Item>
						<Tooltip title="即报表统计时不包含空包订单">
							<span style={ { color: "#ccc" } }>
								<Icon type="wenhao-xian" size={ 15 } />
							</span>
						</Tooltip>
					</Col>
					<Col className="r-ml-16 r-flex r-ai-b">
						<Form.Item name="isExecGift" valuePropName="checked" className="extra-checkbox">
							<Checkbox>排除赠品订单</Checkbox>
						</Form.Item>
						<Tooltip title="即不统计“商品有赠品标识”的子订单">
							<span style={ { color: "#ccc" } }>
								<Icon type="wenhao-xian" size={ 15 } />
							</span>
						</Tooltip>
					</Col>
					<Col className="r-ml-16 r-flex r-ai-b">
						<Form.Item name="isExcludeCanceledBeforeShip" valuePropName="checked" className="extra-checkbox">
							<Checkbox onClick={ () => sendPoint(Pointer.报表_销售毛利润报表_查询_排除发货前取消订单) }>排除发货前取消订单</Checkbox>
						</Form.Item>
						<Tooltip title="即报表统计时不包含发货前取消的订单">
							<span style={ { color: "#ccc" } }>
								<Icon type="wenhao-xian" size={ 15 } />
							</span>
						</Tooltip>
					</Col>
					<Col className="r-ml-16 r-flex r-ai-b" hidden={ isShowZeroStockVersion }>
						<Form.Item name="isExcludeNotRelateGoodsTrade" valuePropName="checked">
							<Checkbox onClick={ () => sendPoint(Pointer.报表_销售毛利润报表_查询_排除未关联本地货品的订单) }>排除未关联本地货品的订单</Checkbox>
						</Form.Item>
						<Tooltip title="即报表统计时不包含未关联本地货品的订单">
							<span style={ { color: "#ccc" } }>
								<Icon type="wenhao-xian" size={ 15 } />
							</span>
						</Tooltip>
					</Col>

					<div className={ s.dataConfigBox }>
						<DataConfigurationFrom defalutVal={ dataConfigura } onOK={ handleDataConfigura } onOpen={ handleOnOpen } />
					</div>

				</Row>
			</div>
		</Form>
	);
});

export default observer(SearchContainer);
