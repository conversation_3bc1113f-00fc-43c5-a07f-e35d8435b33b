import { Button, Checkbox, Form, Space, Select, Tooltip, Input } from "antd";
import React, { Fragment, memo, useEffect, useMemo, useState } from "react";
import { useLocation } from "react-router-dom";
import { observer } from "mobx-react";
import cs from 'classnames';
import { TradeQueryGatherParams, TradeSearchConditionConfig } from "@/types/trade/search/search";
import { BHD_ORDER_STATUS_OPTIONS, BHD_REFUND_STATUS_OPTIONS } from "../../contants";
import ShopMultiSelect from "@/components-biz/ShopListSelect/shopMultiSelect";
import s from './index.module.scss';
import { OrderSourceType, SearchGoodStockStatus, SearchPrintStatus, SearchRefundStatus, SearchSellAttr, SearchTimeType, SearchTradeStatus, SearchTradeType } from "@/pages/Trade/components/SearchContainer/SearchCondition";
import userStore from "@/stores/user";
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import { DatePickerKey } from "@/components/DateRangeComp/kdzsRangePickerUtil";
import FlagAndMemoSelect from "@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagAndMemoSelect";
import InputMulti from "@/components/Input/InputMulti";
import FlagSelect from "@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagSelect";
import RemainingDeliveryTime from "@/components-biz/RemainingDeliveryTime";
import AddressSelect from "@/pages/Trade/components/SearchContainer/AddressSelect/addressSelect";
import OrderMarkSelect from "@/pages/Trade/components/SearchContainer/OrderMarkSelect";
import { autoMergeConfig, orderPreOccupiedStock } from "@/pages/Index/Settings/System/constants";
import { GOOD_STOCK_STATUS_OPTIONS, TRADE_EXCEPTION_OPTIONS, TRADE_LABEL_OPTIONS } from "@/pages/Trade/components/SearchContainer/constants";
import InputArrayMulti from "@/components/Input/InputArrayMulti";
import RangeSelect from "@/pages/Trade/components/SearchContainer/RangeSelect";
import { DistributorSelect } from "@/components-biz/Distribution";
import InputSelect from "@/components/Input/InputSelect";
import { ConditionIdEnum, supplierOptionsEnum, supplierOptionsNotContainerEnum } from "./contants";
import { getGoodsInfoSelectOptions, itemInfoEnumValue } from "@/pages/Trade/components/SearchContainer/itemInfoUtils";
import EnumSelect, { EnumStringSelect } from "@/components/Select/EnumSelect";
import { StockStorageGetApi } from "@/apis/warehouse/entrepot";

const { Option } = Select;

const supplierRoutes = ['/report/bhd/BhdList', '/stall/bhd/BhdList']; // 供应商的路由
const DistributorRoutes = []; // 分销商的路由

export interface IformStatusObj {waitSendOrFirstSend: boolean, firstSendSelect: boolean}

const SearchItem : React.FC<{
	conditionSet: TradeSearchConditionConfig[];
    formData: TradeQueryGatherParams,
    showAll: boolean,
    formStatusObj: IformStatusObj
}> = (props) => {
	const { pathname } = useLocation();
	const {
		conditionSet,
		formData,
		showAll,
		formStatusObj
	} = props;
	const { firstSendSelect, waitSendOrFirstSend } = formStatusObj;
	const [isCanGoodStock, setIsCanGoodStock] = useState<boolean>(false);
	const [allowBhdAllStatus, setAllowBhdAllStatus] = useState<boolean>(false);
	const { isSupplierAccount, isDistributorAccount, isFreeSupplierAccount } = userStore;
	const isSupplier = supplierRoutes.includes(pathname) || isFreeSupplierAccount; // 能明确的供应商身份的是免费供应商版
	const isDistributor = DistributorRoutes.includes(pathname) || isDistributorAccount; // 能明确的分销商身份的是免费分销商版
	const [storageInfoList, setStorageInfoList] = useState([]);

	useEffect(() => {
		userStore.getSystemSetting().then(res => {
			setIsCanGoodStock(res.inventoryDeduct == 1 && [orderPreOccupiedStock.下单占用库存, orderPreOccupiedStock.付款占用库存].includes(res.orderPreOccupiedStock));
		});
		// 判断用户有没有权限查询订单全部状态
		userStore.getUserInfo().then(res => {
			if (res.whiteListSetting) {
				try {
					const whiteListSetting = JSON.parse(res.whiteListSetting);
					setAllowBhdAllStatus(whiteListSetting.bhdAllStatus == 1);
				} catch (e) {
					console.log(e);
				}
			}
		});
	}, []);

	useEffect(() => {
		StockStorageGetApi({ checkStorageAuthorityFlag: true }).then((res) => {
			if (res && res.length > 0) {
				setStorageInfoList(res);
			}
		});
	}, []);

	const TRADE_LABEL_OPTIONS_1 = useMemo(() => {
		const isMergeClose = userStore.systemSetting?.mergeOrder == autoMergeConfig.关闭;
		const filterArr = ['noMerge', 'merge'];
		TRADE_LABEL_OPTIONS.forEach(op => {
			op.options.forEach(item => {
				item.arr.forEach(i => {
					if (filterArr.includes(i.key)) {
						i.tip = isMergeClose ? '系统未开启自动合单，无法查询' : '';
						i.disabled = isMergeClose;
					}
				});
			});
		});
		const hasWaveManagePermission = userStore.hasWaveManagePermission;
		if (!hasWaveManagePermission) {
			TRADE_LABEL_OPTIONS.forEach(op => {
				op.options.forEach(item => {
					item.arr = item.arr.filter(i => i.value !== '已生成拣货波次');
				});
			});
		}
		console.log(userStore.hasWaveManagePermission, TRADE_LABEL_OPTIONS, 'TRADE_LABEL_OPTIONSTRADE_LABEL_OPTIONS');
		return [...TRADE_LABEL_OPTIONS];
	}, [userStore.systemSetting?.mergeOrder, userStore.hasWaveManagePermission]);

	const TRADE_EXCEPTION_OPTIONS_1 = (useMemo(() => {
		const hasWaveManagePermission = userStore.hasWaveManagePermission;
		if (!hasWaveManagePermission) {
			TRADE_EXCEPTION_OPTIONS.forEach(op => {
				op.options.forEach(item => {
					item.arr = item.arr.filter(i => i.value !== '波次拣货异常');
				});
			});
		}
		return [...TRADE_EXCEPTION_OPTIONS];
	}, [userStore.hasWaveManagePermission]));

	const itemInfoQueryNotIncludeList = useMemo(() => {
		return getGoodsInfoSelectOptions(userStore.userInfo?.version, false);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [userStore.userInfo?.version]);

	const itemInfoQueryIncludeList = useMemo(() => {
		return getGoodsInfoSelectOptions(userStore.userInfo?.version, true);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [userStore.userInfo?.version]);

	const itemInfoQueryIncludePlaceholder = useMemo(() => {
		return itemInfoQueryIncludeList.filter(i => i.value == formData.goodsIncludeStatus)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名'];

		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [itemInfoQueryIncludeList, formData.goodsIncludeStatus]);

	const itemInfoQueryIncludeMaxNum = useMemo(() => {
		if (!('goodsIncludeStatus' in formData) || formData.goodsIncludeStatus == itemInfoEnumValue.商品包含) {
			return [200, 500];
		}
		return [50, 50];
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [formData.goodsIncludeStatus]);

	const itemInfoQueryNotIncludeMaxNum = useMemo(() => {
		if (!('goodsNotIncludeStatus' in formData) || formData.goodsNotIncludeStatus == itemInfoEnumValue.商品不包含) {
			return [200, 500];
		}
		return [50, 50];
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [formData.goodsNotIncludeStatus]);

	const itemInfoQueryNotIncludePlaceholder = useMemo(() => {
		return itemInfoQueryNotIncludeList.filter(i => i.value == formData.goodsNotIncludeStatus)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名'];
	}, [itemInfoQueryNotIncludeList, formData.goodsNotIncludeStatus]);

	return (
		<>
			{
				conditionSet.map((item) => {
					return item?.selected || showAll ? (
						<>
							{/* 平台列表 & 店铺列表  */}
							{item.id == ConditionIdEnum['平台&店铺'] ? (
								<Form.Item className={ cs(s.condition2) } name="platformInfo">
									<ShopMultiSelect
										bgHighLight
										isSendPoint
										style={ { width: '100%', flex: "1 1 0%" } }
										isHasHandPlat
									/>
								</Form.Item>
							) : ''}

							{item.id == ConditionIdEnum.订单来源 ? (
								<Form.Item className={ cs(s.condition1) } name="orderSource">
									<OrderSourceType hideLight bgHighLight isSupplierAccount={ isSupplier } />
								</Form.Item>
							) : ''}

							{/* 订单来源 orderSource, 选择选择分销商  */}
							{item.id == ConditionIdEnum.订单来源 && isSupplier ? (
								<Form.Item className={ cs(s.condition2) } name="distributorUserIds">
									<DistributorSelect hideLight roleType="supplier" />
								</Form.Item>
							) : ''}

							{/* 时间类型 */}
							{ item.id == ConditionIdEnum.时间范围 ? (
								<>
									<Form.Item className={ s.condition1 } name="timeType">
										<SearchTimeType hideLight bgHighLight />
									</Form.Item>
									<Form.Item className={ s.condition2 } name="searchTime">
										<KdzsDateRangePicker1 bgHighLight cacheQuickChoose datePickerKey={ DatePickerKey.report_bhd_list } useServeTime />
									</Form.Item>
								</>
							) : ''}

							{/* 备货单 订单状态 */}
							{ item.id == ConditionIdEnum.订单状态 ? (
								<Form.Item className={ s.condition1 } name="status">
									<Select disabled={ firstSendSelect || waitSendOrFirstSend } className={ `${formData.status ? 'high-light-bg' : ''}` }>
										{BHD_ORDER_STATUS_OPTIONS.options.map(item => (
											<Option value={ item.key } key={ item.key } >{item.value}</Option>
										))}
										{/* <Option value="ALL_STATUS" key="ALL_STATUS" >全部状态</Option> */}
										{
											allowBhdAllStatus ? <Option value="ALL_STATUS" key="ALL_STATUS" >全部状态</Option> : ""
										}
									</Select>
								</Form.Item>
							) : ''}
							{/* 订单类型 */}
							{ item.id == ConditionIdEnum.订单类型 ? (
								<Form.Item className={ s.condition1 } name="tradeTypeTagList">
									<SearchTradeType hideLight bgHighLight />
								</Form.Item>
							) : ''}

							{/* 打印状态 */}
							{ item.id == ConditionIdEnum.打印状态 ? (
								<Form.Item className={ s.condition1 } name="printStatus" >
									<SearchPrintStatus bgHighLight />
								</Form.Item>
							) : ''}

							{/* 退款状态 */}
							{item.id === ConditionIdEnum.退款状态 ? (
								<Form.Item className={ s.condition1 } name="refundStatus" >
									<Select className={ `${formData.refundStatus ? 'high-light-bg' : ''}` }>
										{BHD_REFUND_STATUS_OPTIONS.options.map(item => (
											<Option value={ item.key } key={ item.key } >{item.value}</Option>
										))}
									</Select>
								</Form.Item>
							) : ''}
							{/* 留言备注 留言备注模糊搜索*/}
							{item.id == ConditionIdEnum.留言备注 ? (
								<Form.Item className={ cs(s.condition1) } name="flagValue" >
									<FlagAndMemoSelect hideLight bgHighLight />
								</Form.Item>
							) : ''}
							{/* 留言/备注模糊搜索 */}
							{ item.id == ConditionIdEnum.留言备注 && ['-1'].includes(formData?.flagValue) ? (
								<>
									<Form.Item className={ cs(s.condition1) } name="buyerMessageOrSellerMemo">
										<InputMulti
											placeholder="留言或备注内容"
											maxInputNum={ 50 }
											className={ `${formData.buyerMessageOrSellerMemo ? 'high-light-bg' : ''}` }
											maxInputLength={ 1000 }
											lengthErrorMsg="最多输入1000个字数，请重新输入"
											numErrorMsg="单次查询最多筛选50个请重新输入,"
											style={ { width: '100%}' } }
											size="small"
										/>
									</Form.Item>
									{/* <Form.Item className={ cs(s.condition1) } name="buyerMessage">
										<InputMulti
											placeholder="留言内容"
											maxInputNum={ 50 }
											maxInputLength={ 1000 }
											lengthErrorMsg="留言最多输入1000个字数，请重新输入"
											numErrorMsg="单次查询最多筛选50个留言请重新输入,"
											style={ { width: '100%}' } }
											size="small"
										/>
									</Form.Item>
									<Form.Item className={ cs(s.condition1) } name="sellerMemo">
										<InputMulti
											placeholder="备注内容"
											maxInputNum={ 50 }
											maxInputLength={ 1000 }
											lengthErrorMsg="备注最多输入1000个字数，请重新输入"
											numErrorMsg="单次查询最多筛选50个备注，请重新输入"
											style={ { width: '100%}' } }
											size="small"
										/>
									</Form.Item> */}
									<Form.Item className={ cs(s.condition1) } name="sellerFlag">
										<FlagSelect
											placeholder="旗帜"
											className={ `${formData.sellerFlag?.length ? 'high-light-bg' : ''}` }
											style={ { width: '100%}' } }
											size="small"
										/>
									</Form.Item>
								</>
							) : ''}

							{/* 剩余发货时间 */}
							{item.id === ConditionIdEnum.剩余发货时间 ? (
								<Form.Item className={ cs(s.condition1) } name="customizeResidueSendTime">
									<RemainingDeliveryTime hideLight bgHighLight />
								</Form.Item>
							) : ''}
							{/* 买家昵称 */}
							{item.id == ConditionIdEnum.买家昵称 ? (
								<Form.Item className={ cs(s.condition1) } name="buyerNick">
									<InputMulti className={ `${formData.buyerNick ? 'high-light-bg' : ''}` } placeholder="买家昵称" style={ { width: '100%}' } } size="small" />
								</Form.Item>
							) : ''}

							{/* 省市区 */}
							{ item.id == ConditionIdEnum.省市区 ? (
								<Form.Item className={ cs(s.condition1) } name="addressInfo">
									<AddressSelect hideLight bgHighLight isSendPoint isInitValue />
								</Form.Item>
							) : ''}

							{/* 订单编号 */}
							{item.id == ConditionIdEnum.订单编号 ? (
								<Form.Item className={ cs(s.condition1) } name="ptTid">
									<InputMulti className={ `${formData.ptTid ? 'high-light-bg' : ''} high-placeholder` } maxInputNum={ 2000 } placeholder="订单编号" style={ { width: '100%}' } } size="small" />
								</Form.Item>
							) : ''}

							{/* 订单编号 */}
							{item.id == ConditionIdEnum.系统单号 ? (
								<Form.Item className={ cs(s.condition1) } name="tid">
									<InputMulti className={ `${formData.tid ? 'high-light-bg' : ''} high-placeholder` } maxInputNum={ 2000 } placeholder="系统单号" style={ { width: '100%}' } } size="small" />
								</Form.Item>
							) : ''}

							{/* 快递单号 */}
							{item.id == ConditionIdEnum.快递单号 ? (
								<Form.Item className={ cs(s.condition1) } name="sid">
									<InputMulti className={ `${formData.sid ? 'high-light-bg' : ''} high-placeholder` } maxInputNum={ 1000 } placeholder="快递单号" style={ { width: '100%}' } } size="small" />
								</Form.Item>
							) : ''}

							{/* 订单标记 */}
							{item.id == ConditionIdEnum.订单标记 ? (
								<Form.Item className={ cs(s.condition1) } name="bizMarkObj">
									<OrderMarkSelect hideLight bgHighLight />
								</Form.Item>
							) : ''}

							{/* 缺货状态 */}
							{item.id == ConditionIdEnum.缺货状态 && userStore.inventoryDeduct ? (
								<Form.Item className={ s.condition1 } name="goodStockStatus">
									<SearchGoodStockStatus bgHighLight disable={ !isCanGoodStock } originOptions={ GOOD_STOCK_STATUS_OPTIONS.options.filter(i => i.key != 'PART_STOCK') } />
								</Form.Item>
							) : ''}

							{/* 销售属性 */}
							{item.id == ConditionIdEnum.销售属性 ? (
								<Form.Item className={ s.condition1 } name="sellAttributeList" >
									<SearchSellAttr bgHighLight />
								</Form.Item>
							) : ''}

							{item.id == ConditionIdEnum.商品数量 ? (
								<Form.Item className={ cs(s.condition2) } name="goodsTotalNumRange">
									<RangeSelect bgHighLight rangeType="goodsTotalNumRange" />
								</Form.Item>
							) : ''}

							{item.id == ConditionIdEnum.商品种类 ? (
								<Form.Item className={ cs(s.condition2) } name="goodsTypeNumRange">
									<RangeSelect bgHighLight rangeType="goodsTypeNumRange" />
								</Form.Item>
							) : ''}

							{item.id == ConditionIdEnum.订单金额 ? (
								<Form.Item className={ cs(s.condition2) } name="paymentRange">
									<RangeSelect bgHighLight rangeType="paymentRange" />
								</Form.Item>
							) : ''}

							{item.id == ConditionIdEnum.订单重量 ? (
								<Form.Item className={ cs(s.condition2) } name="weightRange">
									<RangeSelect bgHighLight rangeType="weightRange" />
								</Form.Item>
							) : ''}

							{ item.id == ConditionIdEnum.发货仓库 ? (
								<Form.Item name="storageIdList" style={ { minWidth: 170 } } >
									<Select className={ cs(s.conditionWarehouse, `${formData?.storageIdList?.length ? 'high-light-bg' : ''}`) } placeholder="请选择发货仓库" mode="multiple" allowClear maxTagCount={ 1 }>
										{storageInfoList?.map((item) => {
											return (
												<Select.Option value={ item.id }>
													{item.storageName}（{item.storageTypeDesc}）
												</Select.Option>
											);
										})}
									</Select>
								</Form.Item>
							) : ''}
							{/* 达人 */}
							{item.id == ConditionIdEnum['达人名称/ID'] ? (
								<Form.Item className={ cs(s.condition1) } name="authorIncludingList">
									<InputArrayMulti
										size="small"
										className={ `${formData?.authorIncludingList?.length ? 'high-light-bg' : ''}` }
										placeholder="达人名称/ID"
										maxInputNum={ 500 }
										maxTagCount={ 1 }
										maxTagTextLength={ 1 }
										open={ false }
										enterSearch={ false }
										tokenSeparators={ null }
										style={ { flex: 1 } }
									/>
								</Form.Item>
							) : ''}

							{/* 订单标签（原快捷查询&服务标签） */}
							{item.id == ConditionIdEnum.订单标签 ? (
								<Form.Item className={ cs(s.condition2) } name="tradeLabelList">
									<InputSelect bgHighLight optionsList={ TRADE_LABEL_OPTIONS_1 } size="small" />
								</Form.Item>
							) : ''}

							{/* 订单异常(原异常订单&缺货状态) */}
							{item.id == ConditionIdEnum.订单异常 ? (
								<Form.Item className={ cs(s.condition2) } name="tradeExceptionList">
									<InputSelect bgHighLight optionsList={ TRADE_EXCEPTION_OPTIONS_1 } size="small" />
								</Form.Item>
							) : ''}
							{/* 商品查询 包含*/}
							{item.id == ConditionIdEnum.商品包含 ? (
								<Form.Item className={ cs(s.condition3) } >
									<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
										<Form.Item noStyle name="goodsIncludeStatus" >
											<Select
												className={ `${(formData.shortNameIncludingList?.length || formData.skuIncludingList?.length) ? 'high-light-bg' : ''} ` }
												style={ { flex: 0.6 } }
												options={ itemInfoQueryIncludeList }
												dropdownMatchSelectWidth={ false }
											/>
										</Form.Item>
										{
											itemInfoQueryIncludePlaceholder?.[0] ? (
												<Form.Item noStyle name="shortNameIncludingList">
													<InputArrayMulti
														size="small"
														className={ `${formData.shortNameIncludingList?.length ? 'high-light-bg' : ''}` }
														placeholder={ itemInfoQueryIncludePlaceholder[0] }
														maxInputNum={ itemInfoQueryIncludeMaxNum?.[0] }
														maxTagCount={ 1 }
														maxTagTextLength={ 8 }
														open={ false }
														tokenSeparators={ null }
														style={ { flex: 1, width: 192 } }
													/>
												</Form.Item>
											) : ''
										}
										{
											itemInfoQueryIncludePlaceholder?.[1] ? (
												<Form.Item noStyle name="skuIncludingList" >
													<InputArrayMulti
														size="small"
														className={ `${formData.skuIncludingList?.length ? 'high-light-bg' : ''} ` }
														placeholder={ itemInfoQueryIncludePlaceholder[1] }
														maxInputNum={ itemInfoQueryIncludeMaxNum?.[1] }
														maxTagCount={ 1 }
														maxTagTextLength={ 8 }
														open={ false }
														tokenSeparators={ null }
														style={ { flex: 1, width: 192 } }
													/>
												</Form.Item>
											) : ''
										}
									</Input.Group>
								</Form.Item>
							) : ''}
							{/* 商品查询 不包含 */}
							{item.id == ConditionIdEnum.商品不包含 ? (
								<Form.Item className={ cs(s.condition3) } >
									<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
										<Form.Item noStyle name="goodsNotIncludeStatus" >
											<Select
												className={ `${(formData.shortNameNotIncludingList?.length || formData.skuNotIncludingList?.length) ? 'high-light-bg' : ''}` }
												style={ { flex: 0.6 } }
												options={ itemInfoQueryNotIncludeList }
												dropdownMatchSelectWidth={ false }
											/>
										</Form.Item>
										{
											itemInfoQueryNotIncludePlaceholder?.[0] ? (
												<Form.Item noStyle name="shortNameNotIncludingList">
													<InputArrayMulti
														size="small"
														className={ `${formData.shortNameNotIncludingList?.length ? 'high-light-bg' : ''} ` }
														placeholder={ itemInfoQueryNotIncludePlaceholder[0] }
														maxInputNum={ itemInfoQueryNotIncludeMaxNum?.[0] }
														maxTagCount={ 1 }
														maxTagTextLength={ 8 }
														open={ false }
														tokenSeparators={ null }
														style={ { flex: 1, width: 192 } }
													/>
												</Form.Item>
											) : ''
										}
										{
											itemInfoQueryNotIncludePlaceholder?.[1] ? (
												<Form.Item noStyle name="skuNotIncludingList" >
													<InputArrayMulti
														size="small"
														className={ `${formData.skuNotIncludingList?.length ? 'high-light-bg' : ''}` }
														placeholder={ itemInfoQueryNotIncludePlaceholder[1] }
														maxInputNum={ itemInfoQueryNotIncludeMaxNum?.[1] }
														maxTagCount={ 1 }
														maxTagTextLength={ 8 }
														open={ false }
														tokenSeparators={ null }
														style={ { flex: 1, width: 192 } }
													/>
												</Form.Item>
											) : ''
										}
									</Input.Group>
								</Form.Item>
							) : ''}
							{
								item.id == ConditionIdEnum.供应商包含 ? (
									<Form.Item className={ cs("r-flex") } >
										<Input.Group compact style={ { display: "flex", alignItems: "center", width: '526px' } }>
											<Form.Item noStyle name="supplierIsIncluding">
												<EnumStringSelect className={ `${(formData.supplierIncludingList?.length || formData.marketIncludingList?.length || formData.dangKouIncludingList?.length) ? 'high-light-bg' : ''}` } disabled enum={ supplierOptionsEnum } style={ { width: 114 } } size="small" />
											</Form.Item>
											<Form.Item noStyle name="supplierIncludingList">
												<InputArrayMulti
													size="small"
													placeholder="供应商"
													className={ `${formData.supplierIncludingList?.length ? 'high-light-bg' : ''}` }
													maxInputNum={ 500 }
													maxTagCount={ 1 }
													maxTagTextLength={ 1 }
													open={ false }
													enterSearch={ false }
													tokenSeparators={ null }
													style={ { flex: 1 } }
												/>
											</Form.Item>
											<Form.Item noStyle name="marketIncludingList">
												<InputArrayMulti
													size="small"
													className={ `${formData.marketIncludingList?.length ? 'high-light-bg' : ''}` }
													placeholder="市场"
													maxInputNum={ 500 }
													maxTagCount={ 1 }
													maxTagTextLength={ 1 }
													open={ false }
													enterSearch={ false }
													tokenSeparators={ null }
													style={ { flex: 1 } }
												/>
											</Form.Item>
											<Form.Item noStyle name="dangKouIncludingList">
												<InputArrayMulti
													size="small"
													className={ `${formData.dangKouIncludingList?.length ? 'high-light-bg' : ''}` }
													placeholder="档口"
													maxInputNum={ 500 }
													maxTagCount={ 1 }
													maxTagTextLength={ 1 }
													open={ false }
													enterSearch={ false }
													tokenSeparators={ null }
													style={ { flex: 1 } }
												/>
											</Form.Item>
										</Input.Group>
									</Form.Item>
								) : ""
							}
							{
								item.id == ConditionIdEnum.供应商不包含 ? (
									<Form.Item className={ cs("r-flex") } >
										<Input.Group compact style={ { display: "flex", alignItems: "center", width: '526px' } }>
											<Form.Item noStyle name="supplierNotIncluding" >
												<EnumStringSelect className={ `${(formData.supplierNoIncludingList?.length || formData.marketNoIncludingList?.length || formData.dangKouNoIncludingList?.length) ? 'high-light-bg' : ''}` } disabled enum={ supplierOptionsNotContainerEnum } style={ { width: 114 } } size="small" />
											</Form.Item>
											<Form.Item noStyle name="supplierNoIncludingList">
												<InputArrayMulti
													size="small"
													placeholder="供应商"
													className={ `${formData.supplierNoIncludingList?.length ? 'high-light-bg' : ''}` }
													maxInputNum={ 500 }
													maxTagCount={ 1 }
													maxTagTextLength={ 1 }
													open={ false }
													enterSearch={ false }
													tokenSeparators={ null }
													style={ { flex: 1 } }
												/>
											</Form.Item>
											<Form.Item noStyle name="marketNoIncludingList">
												<InputArrayMulti
													size="small"
													placeholder="市场"
													className={ `${formData.marketNoIncludingList?.length ? 'high-light-bg' : ''}` }
													maxInputNum={ 500 }
													maxTagCount={ 1 }
													maxTagTextLength={ 1 }
													open={ false }
													enterSearch={ false }
													tokenSeparators={ null }
													style={ { flex: 1 } }
												/>
											</Form.Item>
											<Form.Item noStyle name="dangKouNoIncludingList">
												<InputArrayMulti
													size="small"
													className={ `${formData.dangKouNoIncludingList?.length ? 'high-light-bg' : ''}` }
													placeholder="档口"
													maxInputNum={ 500 }
													maxTagCount={ 1 }
													maxTagTextLength={ 1 }
													open={ false }
													enterSearch={ false }
													tokenSeparators={ null }
													style={ { flex: 1 } }
												/>
											</Form.Item>
										</Input.Group>
									</Form.Item>
								) : ""
							}

						</>
					) : null;
				})
			}
		</>
	);
};

export default observer(SearchItem);
