import React, { useEffect, useState } from "react";
import { Button, Modal } from "antd";
import { observer } from "mobx-react";
import bhdStore from "@/stores/report/Bhd";
import { countBhdTotal } from "../BhdOrderList/bhdOrderUitls";

export interface IBatchDeleteBtnProps {
	onOk: (res: {
		supplierGroupItems: typeof bhdStore.bhdData,
		totalCount: typeof bhdStore.bhdData.totalCount,
		totalCost: typeof bhdStore.bhdData.totalCost
	}) => void;
	checkUpdateStatus?: (action: string) => boolean;
}

const BatchDeleteBtn = (props: IBatchDeleteBtnProps) => {
	const { onOk, checkUpdateStatus } = props;
	const { setBhdData, bhdData, setIsDisabledBhdGeneratePurchaseOrderBtn, isDisabledBhdGeneratePurchaseOrderBtn } = bhdStore;
	const batchDelete = () => {
		// 检查变更状态
		if (checkUpdateStatus && checkUpdateStatus('批量删除')) {
			return;
		}
		// * 分两种删除
		// 1: 整个供应商被选中 bhd checkStatus
		// 2: 供应商下面部分商品被选中

		Modal.confirm({
			centered: true,
			title: '提示',
			content: '是否删除已选中的行',
			onOk: () => {
				let _bhdData = bhdData.filter(item => item.checkStatus !== 1);
				for (let index = 0; index < _bhdData.length; index++) {
					if (_bhdData[index].checkStatus === 0.5) {
						_bhdData[index].items = _bhdData[index].items.filter(i => !i.isChecked);
						_bhdData[index].checkStatus = 0;
					}
				}
				const { totalCount, totalCost, totalPaymentCount } = countBhdTotal(_bhdData);
				setBhdData(_bhdData);
				setIsDisabledBhdGeneratePurchaseOrderBtn(true);
				onOk({
					supplierGroupItems: _bhdData,
					totalCount,
					totalCost,
					totalPayment: totalPaymentCount
				});
			}
		});
		
	};


	return (
		<Button disabled={ isDisabledBhdGeneratePurchaseOrderBtn } size="large" type="primary" onClick={ () => { batchDelete(); } }>批量删除</Button>
	);
};

export default observer(BatchDeleteBtn);