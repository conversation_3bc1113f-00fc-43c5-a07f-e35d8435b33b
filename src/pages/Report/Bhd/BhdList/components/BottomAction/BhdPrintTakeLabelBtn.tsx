import { Button, Checkbox, Form, Modal, Select, Tooltip } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useHistory } from 'react-router-dom';
import { observer, Provider, useObserver, Observer, useLocalStore } from 'mobx-react';
// import bhdStore from "@/stores/report/Bhd";
import { ExclamationCircleOutlined, InfoCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { cloneDeep } from "lodash";
import message from "@/components/message";
import { useStores } from "@/stores/tool";
import Pointer from "@/utils/pointTrack/constants";
import bhdStore from "@/stores/report/Bhd";
import PrintCenterAPI from '@/print/index';
import { beforeGeneratorConfirmModal, generatorLabelFinishModal } from "@/pages/Trade/components/BottomCom/components/generatorLabelUtil";
import { TakeGoodsLabelExportTakeGoodsOrder<PERSON><PERSON>, TakeG<PERSON>sLabelGenerate<PERSON><PERSON>, TakeGoodsLabelHotCellGenerateApi } from "@/apis/trade/takeGoodsLabel";
import { TradePrintCheckApi } from "@/apis/trade/search";
import { diffTodayDays, spArr, labelListSort } from "@/pages/Trade/utils";
import c from './index.module.scss';
import { ItemTakeGoodsLabelHotCellGenerateResponse, ItemTakeGoodsLabelSelectWithPageResponse } from "@/types/trade/takeGoodsLabel";
import { tradeStore } from "@/stores";
import event from '@/libs/event';
import scanPrintStore from "@/stores/trade/scanPrint";
import { local, session } from "@/libs/db";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { filterPrintContent } from "@/utils/trade/printContent";
import tradeSetStore from "@/stores/trade/tradeSet";
import GenerateLabelWarningModal from '@/components-biz/GenerateLabelWarningModal';
import { AbnormalType as TradeAbnormalType } from '@/components-biz/GenerateLabelWarningModal/constants';
import { AbnormalType } from '@/pages/Trade/components/BottomCom/constants';
import { TradeStatus, RefundStatus } from '@/utils/enum/trade';
import useGetState from "@/utils/hooks/useGetState";

import { OptTypeEnum } from "../../contants";
import { UnderstockErrorMsgListModalData } from "@/pages/Trade/components/BottomCom";
import UnderstockErrorMsgListModal, { GeneratorLabelType } from "@/pages/Trade/components/BottomCom/components/UnderstockErrorMsgListModal";
import { BhdShowType } from "../../../BhdTableSet/contants";
import { LabelPrintSourceEnum } from '@/constants/labelPush';
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";

interface BhdPrintTakeLabelBtnProps{
	setIsShowControlGuide:any,
	type: OptTypeEnum.仅生成标签 | OptTypeEnum.打印拿货小标签
	searchParams?:any
	checkUpdateStatus?: (action: string) => boolean;
}

export enum hotSellStatisticsTypeEnum {
	平台链接统计 = "PLATFORM_ITEM_LINK",
	备货单合并结果统计 = "BHD_MERGE_RESULT"
}

const { Option } = Select;
const BhdPrintTakeLabelBtn:React.FC<BhdPrintTakeLabelBtnProps> = (prop) => {
	// const bhdCheckList = bhdStore.bhdCheckList;
	const {
		type,
		setIsShowControlGuide, // lodop安装提示弹窗
		searchParams = {},
		checkUpdateStatus
	} = prop;
	const bhdData = bhdStore.bhdData;
	const [labelGenerateProgress, setLabelGenerateProgress] = useState({ show: false, num: 0, sum: 0 });
	const [printLabelList, setPrintLabelList] = useState([]);
	const [loading, setLoading] = useState(false);
	const [printLabelShow, setPrintLabelShow] = useState(false);
	const [printerList, setPrinterList] = useState([]);
	const [hotLabelData, setHotLabelData] = useState(null);
	const [tempObj, setTempObj] = useState(null);
	const [labelNum, setLabelNum] = useState(null);
	const [showGenerateLabelWarningModal, setShowGenerateLabelWarningModal] = useState(false);
	const [labelAbnormalObj, setLabelAbnormalObj] = useState({});
	const [labelAbnormalFinalRes, setLabelAbnormalFinalRes, getLabelAbnormalFinalRes] = useGetState({});
	const [allowGenerateType, setAllowGenerateType, getAllowGenerateType] = useGetState({});
	const [isPrintEnd, setIsPrintEnd] = useState(false);
	const [understockErrorMsgListModalData, setIsShowUnderstockErrorMsgListModal] = useState<UnderstockErrorMsgListModalData>({ show: false, params: {} });

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `备货单标签-打印、生成拿货标签: 【 ${dataType} 】`, 
			data: {
				type,
				searchParams,
				...data
			}
		});
	};

	const [form] = Form.useForm();
	const {
		bhdXbqTempList,
		selectedBxdXbqTemp,
		lodopInstallStatus, // lodop安装状态
		goodsTagListStore: {
			printStatus,
			setPrintStatus,
			setPrintList
		}
	} = tradeStore;

	const {
		printersList,
		getScanPrintSetting
	} = scanPrintStore;

	useEffect(() => {
		let BHDPrinterAndTemp = local.get('BHDPrinterAndTemp');
		form.setFieldsValue({
			sortByMarketStallSupplier: BHDPrinterAndTemp?.sortByMarketStallSupplier || false
		});
	}, []);
	
	useEffect(() => {
		if (printersList.length) {
			setPrinterList(printersList);
		}
		let BHDPrinterAndTemp = local.get('BHDPrinterAndTemp');
		if (printersList?.length && !form.getFieldValue('printer')) {
			let printer = printersList[0];
			let selectPrinter = BHDPrinterAndTemp && BHDPrinterAndTemp.printer;
			if (selectPrinter && printersList.includes(selectPrinter)) {
				printer = selectPrinter;
			}
			form.setFieldsValue({
				printer,
			});
		}
	}, [printersList]);

	useEffect(() => {
		if (!bhdXbqTempList || !bhdXbqTempList.length) return;
		let _hotTempList = [];
		let _tempList = [];
		for (let item of bhdXbqTempList) {
			if (item.ExCode === "BKD") {
				_hotTempList.push(item);
			} else {
				_tempList.push(item);
			}
		}
		setTempObj({
			tempList: _tempList,
			hotTempList: _hotTempList
		});
		let BHDPrinterAndTemp = local.get('BHDPrinterAndTemp');
		let temp = _tempList[0]?.Mode_ListShowId;
		let hotTempId = _hotTempList[0]?.Mode_ListShowId;
		let selectTemp = BHDPrinterAndTemp?.temp;
		let selectHotTemp = BHDPrinterAndTemp?.hotTemp;
		if (selectTemp && _tempList.filter(item => item.Mode_ListShowId === selectTemp).length) {
			temp = selectTemp;
		}
		if (selectHotTemp && _hotTempList.filter(item => item.Mode_ListShowId === selectHotTemp).length) {
			hotTempId = selectHotTemp;
		}
		form.setFieldsValue({
			temp,
			hotTempId
		});
	}, [bhdXbqTempList]);

	useEffect(() => {
		if (isPrintEnd && printStatus && printStatus.isSuccess) {
			Modal.confirm({
				title: '系统提示',
				centered: true,
				icon: <ExclamationCircleOutlined />,
				content: (
					<div>成功：{printStatus.successCount}, 失败：{printStatus.failCount}</div>
				),
				okText: '导出本次打印备货单',
				closable: true,
				zIndex: 9999,
				onOk: () => {
					setPrintStatus({
						isSuccess: false,
						successCount: 0,
						failCount: 0
					});
					setIsPrintEnd(false);
					exportTakeGoods(printStatus.labelIds);
				},
				onCancel: () => {
					setPrintStatus({
						isSuccess: false,
						successCount: 0,
						failCount: 0
					});
					setIsPrintEnd(false);
				},
			});
		}
	}, [printStatus, isPrintEnd]);

	// 导出拿货单
	const exportTakeGoods = async(labelIds?:string[]) => {
		const formatDate = (num:number) => {
			return num < 10 ? `0${num}` : num;
		};
		if (!labelIds) {
			message.info('没有可打印的拿货单');
			return;
		}
		let _searchParams = {};
		_searchParams = {
			labelId: labelIds.join(),
			pageSize: 500,
			pageNo: 1,
		};
		await downloadCenter({
			requestParams: _searchParams,
			fileName: `拿货单`,
			module: ModulesFunctionEnum.商品标签_拿货单导出
		});
		// 前端走下载中心 不走之前接口了
		// let _searchParams = {};
		// if (labelIds) {
		// 	_searchParams = {
		// 		labelId: labelIds.join(),
		// 		pageSize: 500,
		// 		pageNo: 1,
		// 	};
		// }
		
		// TakeGoodsLabelExportTakeGoodsOrderApi(_searchParams).then((res:any) => {
		// 	if (res.type === 'application/json') {
		// 		let blob = new Blob([res]);
		// 		let blobReader = new Response(blob).json();
		// 		blobReader.then(res => {
		// 			if (res.errorMessage) {
		// 				message.error(res.errorMessage);
		// 			} else {
		// 				message.error("导出失败");
		// 			}
		// 		});
		// 	} else {
		// 		let date = dayjs(new Date());
		// 		let _date = `${date.year()}${formatDate(date.month() + 1)}${formatDate(date.date())}${formatDate(date.hour())}${formatDate(date.minute())}${formatDate(date.second())}`;
		// 		let blob = new Blob([res]);
		// 		const aLink = document.createElement('a');
		// 		aLink.download = `拿货单${_date}.xlsx`;
		// 		aLink.href = window.URL.createObjectURL(blob);
		// 		aLink.click();
		// 	}

		// }).catch((error) => {
		// 	message.error("导出失败");
		// 	console.log('error:::', error);
		// });
	};

	const getGeneratorList = async() => {
		const { hotSellStatisticsType = hotSellStatisticsTypeEnum.平台链接统计 } = await getScanPrintSetting();
		let generatorList = [];
		const selectTrade = {};
		let generatorLabelLen = 0;

		// 提取出不允许打印的order
		const noNeedPrintOids = []; // 不需要打印的oid
		const labelAbnormalFinalRes = getLabelAbnormalFinalRes();
		// 过滤没有被勾选的异常order
		const allCheckedAbnormalTypeList = []; // 获取所有勾选的异常类型
		for (let key in labelAbnormalFinalRes) {
			const abnormalItem = labelAbnormalFinalRes[key];
			if (abnormalItem.isChecked) {
				allCheckedAbnormalTypeList.push(abnormalItem.type);
			}
		}

		for (let key in labelAbnormalFinalRes) {
			const abnormalItem = labelAbnormalFinalRes[key];
			abnormalItem.abnormalOrderList.forEach(orderItem => {
				// 判断当前oid的每一项异常状态是不是都在已勾选的异常状态列表中，只有满足情况才允许被生成标签
				const allow = orderItem.abnormalList.every(abnormalType => allCheckedAbnormalTypeList.includes(abnormalType));
				if (!allow && abnormalItem.isChecked) {
					noNeedPrintOids.push(orderItem.oid);
				}
				if (!abnormalItem.isChecked) {
					noNeedPrintOids.push(orderItem.oid);
				}
			});
		}
		// 获得所有选择的商品列表
		bhdData.forEach((items, supplierIndex) => {
			items.items.forEach((order, orderIndex) => {
				if (order.isChecked) {
					generatorLabelLen += order.totalCount;
					order.tradeInfos.forEach((orderItem:any) => {
						// 判断当前order是否在不允许打印的异常池子中
						const noNeedPrint = noNeedPrintOids.includes(orderItem.orderInfo.oid);
						if (noNeedPrint) {
							generatorLabelLen -= 1;
							return;
						}
						let skuIndex = order.skuCounts.findIndex((skuItem) => (skuItem.oidList.includes(orderItem.orderInfo.oid)));
						// 按合并结果统计 需给后端传供应商index 是按照规格纬度进行统计
						if (hotSellStatisticsType === hotSellStatisticsTypeEnum.备货单合并结果统计) {
							orderItem.orderInfo = bhdStore.showType == BhdShowType.规格商品 ? { ...orderItem.orderInfo, batchNumber: `${order._orderIndex}` } : { ...orderItem.orderInfo, batchNumber: `${order._orderIndex}_${skuIndex}` };
						}
						// 如果需要打印的备货单命中了异常池
						if (!selectTrade[orderItem.tid]) {
							selectTrade[orderItem.tid] = { tid: orderItem.tid, orderInfoObj: {} };
							selectTrade[orderItem.tid].orderInfoObj = { [orderItem.orderInfo.oid]: orderItem.orderInfo };
						} else {
							// 如果以前有该oid 将不再放到该数组
							selectTrade[orderItem.tid].orderInfoObj[orderItem.orderInfo.oid] = orderItem.orderInfo;
						}

					});
				}
			});
		});
		// 组装要生成小标签的的数据
		for (let item in selectTrade) {
			let _orderInfo = [];
			const _orderInfoObj = selectTrade[item].orderInfoObj;
			for (let order in _orderInfoObj) {
				_orderInfo.push(_orderInfoObj[order]);
			}
			generatorList.push({ tradeInfos: { tid: item, orderInfos: _orderInfo } });
		}

		return { generatorLabelLen, generatorList };
	};

	// 主要用于将异常子订单的状态收集起来
	const handleOrderAbnormalList = (() => {
		const abnormalOrderObj = {};
		return (oid, abnormalType) => {
			if (!abnormalOrderObj[oid]) {
				abnormalOrderObj[oid] = {
					abnormalList: [abnormalType]
				};
			} else {
				abnormalOrderObj[oid].abnormalList.push(abnormalType);
			}
			return abnormalOrderObj;
		};
	})();


	// 备货单标签前置校验 （这里和订单打印的数据结构不一样）
	const generatorLabelPrecheck = async() => {
		// 检查变更状态
		if (checkUpdateStatus && checkUpdateStatus(type === OptTypeEnum.打印拿货小标签 ? '打印拿货小标签' : '仅生成拿货小标签')) {
			return;
		}

		let hasAbnormal = false; // 存在异常的订单
		const needValidType = [
			TradeAbnormalType.买家申请退款, 
			TradeAbnormalType.交易成功, 
			TradeAbnormalType.已关闭, 
			TradeAbnormalType.已发货,
			TradeAbnormalType.系统赠品异常,
			TradeAbnormalType.订单正在执行自动策略,
			TradeAbnormalType.列表加载商品数据与数据库商品数据不一致
		];
		let abnormalObj = {};
		let abnormalArr:any[] = []; // 收集要请求接口判断异常的参数
		let tradeObjMap = new Map<string, any>();

		needValidType.forEach(type => abnormalObj[type] = {
			type,
			tradeList: [],
			abnormalOrderList: [],
			notAllow: [TradeAbnormalType.订单正在执行自动策略, TradeAbnormalType.列表加载商品数据与数据库商品数据不一致].includes(type), // 是否允许生成
		});

		// 从接口获取异常到异常数据里面
		const tradeInfoCheck = (item:any, abnormalKey: any) => {
			let _trade = tradeObjMap.get(item.tid);
			if (_trade) {
				hasAbnormal = true;
				const orderAbnormalList = handleOrderAbnormalList(item.oid, abnormalKey);
				abnormalObj[abnormalKey]?.abnormalOrderList?.push({
					oid: item.oid,
					abnormalList: orderAbnormalList?.[item.oid]?.abnormalList
				});
				abnormalObj[abnormalKey]?.tradeList?.push({
					..._trade,
					tid: _trade.tid
				});
			}
		};

		console.log('%c [ bhdData ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', bhdData);
		bhdData.forEach(items => {
			items.items.forEach(trades => {
				if (trades.isChecked) {
					trades.tradeInfos.forEach(trade => {
						const orderInfo = trade.orderInfo;

						// 收集要接口请求异常的参数
						// 合并同tid的
						if (tradeObjMap.has(trade.tid)) {
							let tradeObj = tradeObjMap.get(trade.tid);
							tradeObj?.orders?.push(orderInfo);
							tradeObjMap.set(trade.tid, tradeObj);
						} else {
							tradeObjMap.set(trade.tid, { ...trade, tid: trade.tid, orders: [orderInfo] });
						}
						
						// 判断是否是已发货,且不是先发货模式
						const isSend = (orderInfo.status === TradeStatus.等待买家确认收货 && !orderInfo.firstSend) || orderInfo.status === TradeStatus.卖家部分发货;
						if (isSend) {
							hasAbnormal = true;
							const orderAbnormalList = handleOrderAbnormalList(orderInfo.oid, TradeAbnormalType.已发货);
							abnormalObj[TradeAbnormalType.已发货]?.abnormalOrderList?.push({
								oid: orderInfo.oid,
								abnormalList: orderAbnormalList?.[orderInfo.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.已发货]?.tradeList?.push({
								...orderInfo,
								tid: trade.tid
							});
						}
						// 判断是不是交易成功
						const isSuccessed = orderInfo.status === TradeStatus.交易成功 || orderInfo.status === TradeStatus.买家已签收;
						if (isSuccessed) {
							hasAbnormal = true;
							const orderAbnormalList = handleOrderAbnormalList(orderInfo.oid, TradeAbnormalType.交易成功);
							abnormalObj[TradeAbnormalType.交易成功]?.abnormalOrderList?.push({
								oid: orderInfo.oid,
								abnormalList: orderAbnormalList?.[orderInfo.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.交易成功]?.tradeList?.push({
								...orderInfo,
								tid: trade.tid
							});
						}
						// 判断是不是交易关闭
						const isClosed = orderInfo.status === TradeStatus.交易关闭 || orderInfo.status === TradeStatus.交易自动关闭;
						if (isClosed) {
							hasAbnormal = true;
							const orderAbnormalList = handleOrderAbnormalList(orderInfo.oid, TradeAbnormalType.已关闭);
							abnormalObj[TradeAbnormalType.已关闭]?.abnormalOrderList?.push({
								oid: orderInfo.oid,
								abnormalList: orderAbnormalList?.[orderInfo.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.已关闭]?.tradeList?.push({
								...orderInfo,
								tid: trade.tid
							});
						}
						// 判断是不是有退款
						const hasRefund = RefundStatus.退款中 === orderInfo.refundStatus || RefundStatus.退款成功 === orderInfo.refundStatus;
						if (hasRefund) {
							hasAbnormal = true;
							const orderAbnormalList = handleOrderAbnormalList(orderInfo.oid, TradeAbnormalType.买家申请退款);
							abnormalObj[TradeAbnormalType.买家申请退款]?.abnormalOrderList?.push({
								oid: orderInfo.oid,
								abnormalList: orderAbnormalList?.[orderInfo.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.买家申请退款]?.tradeList?.push({
								...orderInfo,
								tid: trade.tid
							});
						}

						// 判断系统赠品异常
						const hasAbnormalSysGift = orderInfo?.isSysGift && orderInfo?.sysGiftStatus == 2;
						if (hasAbnormalSysGift) {
							hasAbnormal = true;
							const orderAbnormalList = handleOrderAbnormalList(orderInfo.oid, TradeAbnormalType.系统赠品异常);
							abnormalObj[TradeAbnormalType.系统赠品异常]?.abnormalOrderList?.push({
								oid: orderInfo.oid,
								abnormalList: orderAbnormalList?.[orderInfo.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.系统赠品异常]?.tradeList?.push({
								...orderInfo,
								tid: trade.tid
							});
						}
					});
				}
			});
		});

		for (const trade of tradeObjMap.values()) {
			const tradeCheckInfo = {
				sellerId: trade.sellerId,
				platform: trade?.platformType?.toLocaleLowerCase(), // 代替 platform
				source: trade.source,
				distributorUserId: trade.distributorUserId, // 无
				tid: trade.tid,
				storageTime: trade.storageTime, // 无
				ptTid: trade.ptTid,
				modified: trade.modified, // 无
				oidList: trade?.orders?.map(o => o.oid) || [], // 系统赠品判断
			};
			abnormalArr.push(tradeCheckInfo);
		}

		if (abnormalArr?.length) {
			// 每次请求 最多能查200个tid
			let splitArr:Array<any> = spArr(abnormalArr, 200);
			const promises = splitArr.map(function(item) {
				let params = {
					tidList: item.map((i) => i.tid),
					tradeCheckInfos: item,
				};
				return TradePrintCheckApi(params);
			});

			try {
				const resArr = await Promise.all(promises);
				console.log('%c [ 从接口获取一些异常 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', resArr);
				resArr?.forEach((res) => {
					res?.data?.abnormalTradeInfoList?.forEach((item: any) => {
						if (item && item !== 'null') {
							if (item.type == AbnormalType.订单正在执行自动策略) {
								tradeInfoCheck(item, TradeAbnormalType.订单正在执行自动策略);
							} 
							// 备货单这边因为数据结构不一样，合并的tid不一定是完整的，没办法判断数据是否一致，未来在扫描打印单独判断
							// if (item.type == AbnormalType.列表加载商品数据与数据库商品数据不一致) {
							// 	tradeInfoCheck(item, TradeAbnormalType.列表加载商品数据与数据库商品数据不一致);
							// }
						}
					});
				});
			} catch (error) {
				console.log('%c [ 接口获取异常失败 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', error);
			}
		}
				
		console.log('%c [ 所有的异常拦截数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', abnormalObj);

		if (hasAbnormal) {
			setLabelAbnormalObj(abnormalObj);
			setShowGenerateLabelWarningModal(true);
			customLogPost('异常状态商品，弹出系统提示', { abnormalObj });
		} else {
			handlePrint();
		}
	};

	const handlePrint = async({ generateLabelParams = {} } = {}) => {
		let labelTimer = null;
		const { hotSellStatisticsType = hotSellStatisticsTypeEnum.平台链接统计 } = await getScanPrintSetting();
		if (!lodopInstallStatus) {
			setIsShowControlGuide(true);
			return;
		}
		try {
			sendPoint(Pointer.报表_备货单_打印拿货小标签);
			setLoading(true);
			// 获取要打印的列表
			const { generatorLabelLen, generatorList } = await getGeneratorList();
			
			if (!generatorList.length) {
				setLoading(false);
				if (showGenerateLabelWarningModal) {
					message.error('没有符合异常状态的订单，请重新选择');
					return;
				}
				message.error('请先选择商品再打印');
				return;
			}

			const generatorConfirm = await beforeGeneratorConfirmModal();
			if (!generatorConfirm) {
				customLogPost('生成标签过程中，取消生成标签', { tids: generatorList?.map(item => item?.tradeInfos?.tid), generatorLabelLen });
				return;
			}
			setShowGenerateLabelWarningModal(false);

			setLabelGenerateProgress({ show: true, num: 0, sum: generatorLabelLen || 0 });

			labelTimer = setInterval(() => {
				let num = 0;
				setLabelGenerateProgress((prev) => {
					num = ++prev.num;
					return { show: true, num, sum: generatorLabelLen || 0 };
				});
				if (num > (generatorLabelLen - 1)) clearInterval(labelTimer);
			}, 500);

			const resArr:any[] = [];
			try {
				let splitArr:Array<any> = spArr(generatorList, 50);
				
				const { includeArrivalOfGoodsAndStockpile = false, includePending = false, queryFirstSend = false, queryWaitSendOrFirstSend = false } = bhdStore?.searchParams || {};
				const handler = async() => {
					if (splitArr.length) {
						let params = {
							goodsLabelTradeInfoList: splitArr.pop(),
							generateMarketLabelWhenUnderstock: false,
							skipAlreadyGenerateLabelTrade: true,
							includePending,
							includeFirstSend: queryFirstSend,
							includeArrivalOfGoodsAndStockpile,
							...getAllowGenerateType(),
							...generateLabelParams

						};
						let _res = await TakeGoodsLabelGenerateApi(params);
						resArr.push(_res);
						await handler();
					}
				};
				await handler();
				clearInterval(labelTimer);
				// api 请求报错时统计订单数量
				const isPrintLableContainerGoods = session.get('bhdReport.isPrintLableContainerGoods');
				let _labelList = [];
				let failCount = 0;
				let hotLabelInfos = [];
				let understockErrorMsgList:any[] = [];
				console.log('resArr:', resArr);
				resArr.forEach((item, index) => {
					if (item.understockErrorMsgList.length > 0) {
						understockErrorMsgList.push(...item.understockErrorMsgList);
					}
					// 如果打印标签不包含已到货、已囤货商品 去掉此类标签  "STOCKPILE" = "已屯货","ARRIVAL_OF_GOODS" = "已到货"
					item.takeGoodsLabels?.forEach(label => {
						const labelInfo:any = {
							labelId: label.labelId,
							numIid: label.numIid,
							hotSellId: label.hotSellId,
							skuId: label.skuId,
							saleProperty: label.saleProperty,
							labelPrintStatus: label.printStatus
						};
						if (hotSellStatisticsType === hotSellStatisticsTypeEnum.备货单合并结果统计) {
							labelInfo.batchNumber = label.batchNumber;
						}
						if (isPrintLableContainerGoods) {
							if (label.labelStatus !== "STOCKPILE" && label.labelStatus !== "ARRIVAL_OF_GOODS") {
								hotLabelInfos.push(labelInfo);
								_labelList.push(label);
							}
						} else {
							hotLabelInfos.push(labelInfo);
							_labelList.push(label);
						}
					});
					if (item.failCount) {
						failCount += Number(item.failCount || 0);
					}
				});
				setLabelGenerateProgress({ show: false, num: 0, sum: 0 });

				if (understockErrorMsgList.length > 0) {
					setLoading(false);
					setIsShowUnderstockErrorMsgListModal({ show: true, understockErrorMsgList });
					customLogPost('勾选订单中以下货品库存不足，是否将库存不足货品自动生成市场标签', { understockErrorMsgList });
					return;
				}

				setPrintLabelList(_labelList);

				console.log('_labelList:', _labelList);
				console.log('hotLabelInfos:', hotLabelInfos);
				// 查询爆款标签
				const hotLabelRes: ItemTakeGoodsLabelHotCellGenerateResponse = await hotSellGenerator(hotLabelInfos);
				setLoading(false);
				let _list = hotLabelRes?.takeGoodsLabels || [];
				const { hotLabelArr, hotLabelObj } = dealHotLabelList(_list);

				setLabelNum(_labelList.length - _list.length);
				setHotLabelData({ hotLabelArr, hotLabelObj });
				// 生成标签结束后提示
				generatorLabelFinishModal({ successCount: _labelList.length, failCount, resArr, generatorList, generatorLabelLen }).then((res) => {
					if (type === OptTypeEnum.打印拿货小标签) {
						setPrintLabelShow(true);
						customLogPost('生成标签结果弹框点击确定，继续打印拿货小标签', { printLabelList: _labelList, hotLabelArr, hotLabelObj });
					}
				}).catch(() => {});
			} catch (error) {
				clearInterval(labelTimer);
				setLabelGenerateProgress({ show: false, num: 0, sum: 0 });
				setLoading(false);
				console.log('error:', error);
				customLogPost('生成拿货小标签出错', { 
					error, 
					generatorLabelLen, 
					tids: generatorList?.map(item => item?.tradeInfos?.tid),
					successTidOids: resArr?.map(item => item?.successTidOids),
					takeGoodsLabels: resArr?.map(item => item?.takeGoodsLabels?.map(d => d?.labelId)),
				 });
			}
		} catch (error) {
			clearInterval(labelTimer);
			setLabelGenerateProgress({ show: false, num: 0, sum: 0 });
			setLoading(false);
			console.log(error);
			customLogPost('打印拿货小标签出错', { error });
		}
	};

	const understockErrorMsgListModalBack = (flag:string) => {
		let params = {
			generateMarketLabelWhenUnderstock: false,
			forceGenerateSortLabelWhenUnderstock: false
		};
		if (flag === GeneratorLabelType.拿货) {
			params.generateMarketLabelWhenUnderstock = true;
			handlePrint({ generateLabelParams: params });
			customLogPost('勾选订单中以下货品库存不足，生成拿货标签', { });
		}
		if (flag === GeneratorLabelType.库存) {
			params.forceGenerateSortLabelWhenUnderstock = true;
			handlePrint({ generateLabelParams: params });
			customLogPost('勾选订单中以下货品库存不足，继续生成库存标签', { });
		}
		if (flag == 'close') {
			customLogPost('勾选订单中以下货品库存不足，取消', { });
		}
		setIsShowUnderstockErrorMsgListModal({ show: false, params: {} });
	};

	const dealHotLabelList = (list) => {
		const _obj = {};
		const hotLabelArr = [];
		const hotLabelObj = {};
		for (let item of list) {
			// 爆款标签过滤词处理
			item.skuName = filterPrintContent(item.skuName);
			
			hotLabelObj[item.labelId] = true;
			if (!_obj[item.hotSellId]) {
				_obj[item.hotSellId] = {
					...item
				};
				hotLabelArr.push(item);
			}
		}
		console.log('hotLabelObj:', hotLabelArr, hotLabelObj);
		return { hotLabelArr, hotLabelObj };
	};

	// 生成爆款标签
	const hotSellGenerator = async(hotLabelInfos) => {
		const { hotSellItemCeilingNum = 2000, hotSellItemDefinitionNum = 0, hotSellStatisticsType = hotSellStatisticsTypeEnum.平台链接统计, generateHotSellLabel = true } = await getScanPrintSetting();
		if (!hotSellItemDefinitionNum || !generateHotSellLabel) return Promise.resolve({});

		try {
			const params = {
				hotSellItemDefinitionNum: parseInt(hotSellItemDefinitionNum, 10),
				hotSellItemCeilingNum: parseInt(hotSellItemCeilingNum, 10),
				hotSellStatisticsType,
				labelInfos: hotLabelInfos
			};
			const res = await TakeGoodsLabelHotCellGenerateApi(params);
			return Promise.resolve(res);
		} catch (error) {
			customLogPost('生成爆款标签错误', { error, labelInfos: hotLabelInfos });
			return Promise.resolve({});
		}
		
	};
	const handlePrintAct = async({ temp, printer, review, hotTempId, sortByMarketStallSupplier }:{temp?:any, printer?: string, hotTempId?:string, review?: boolean, sortByMarketStallSupplier?: boolean}) => {
		console.log('handlePrintAct::', temp, printer, review, hotTempId);
		// 打印时根据备货单市场档口供应商进行分组
		let sortSupplierKeyObj = {};
		if (sortByMarketStallSupplier) {
			bhdData?.forEach((item, index) => {
				sortSupplierKeyObj[item.mallStallSupplierStr] = index;
			});
		}

		let selectList:ItemTakeGoodsLabelSelectWithPageResponse["data"]["list"] = [];
		let takeGoodsLabelIds:number[] = [];
		let sameSupplierGoodsNumObj = {};
		setPrintLabelShow(false);
		printLabelList.forEach(item => {
			const { labelType, supplierName, sysSkuId, skuId, logisticsCompany, skuName, labelId, sortNumCode, sortTradeSn, sortOrderSn } = item;
			// 如果是拣货 需要检验库存
			labelType === "SORT" && takeGoodsLabelIds.push(labelId);
			const diffDays = diffTodayDays(item.gmtCreated);
			// 如果距离今天大于0 就是加急
			diffDays > 0 && (item.faster = diffDays);
			item.id = labelId;
			logisticsCompany && (item.exName = logisticsCompany);
			// 过滤词处理
			item.skuName = filterPrintContent(skuName);
			
			// 普通小标签过滤
			if (!hotLabelData || !hotLabelData.hotLabelObj[labelId]) {
				selectList.push({
					...item,
					isStockPile: item.packageExistsStockpileLabel || (item.labelStatus == "STOCKPILE"),
					sortCode: `${sortNumCode || ""}${sortTradeSn || ""}-${sortOrderSn || ""}`,
				});
				let num = sameSupplierGoodsNumObj[`${supplierName}_${sysSkuId || skuId}`];
				sameSupplierGoodsNumObj[`${supplierName}_${sysSkuId || skuId}`] = num ? ++num : 1;
			}

		});

		if (!selectList.length && !hotLabelData?.hotLabelArr?.length) {
			setLoading(false);
			message.info("没有可打印的标签");
			return;
		}
		if (!temp && selectList.length) {
			message.error('请选择拿货小标签模版');
			return;
		}
		if (!hotTempId && hotLabelData?.hotLabelArr?.length) {
			message.error('请选择爆款小标签模版');
			return;
		}
		if (!printer) {
			message.error('请选择打印机');
			return;
		}

		selectList = await labelListSort({ selectList, sameSupplierGoodsNumObj, sortSupplierKeyObj });
		console.log("selectList::::", selectList.map((item) => `${item.market}-${item.stall}-${item.supplierName}`));
		console.log("selectList::::", selectList.map((item) => `${item.sortName}`));
		setLoading(false);
		batchPrintBhdXbq({ selectList, temp, hotTempId, printer, review });
	};
	const batchPrintBhdXbq = (
		{ selectList, temp, printer, hotTempId, review = false }:{
			selectList:ItemTakeGoodsLabelSelectWithPageResponse["data"]["list"],
			temp?:any,
			printer?: string,
			hotTempId?: string,
			review:boolean}
	) => {
		setPrintList([...(selectList || []), ...(hotLabelData?.hotLabelArr || [])]);
		console.log({
			tempId: temp,
			hotTempId,
			hotOrderList: hotLabelData?.hotLabelArr || [],
			orderList: selectList,
			printer,
			review
		}, 'batchPrintBhdXbq');
		const _hotLabelArr = hotLabelData?.hotLabelArr || [];
		let params = {};
		// 如果只有爆款小标签，就 orderlist = hotorderlist   tempid=hottempID 
		// 因为对于打印那边不分拿货爆款的  本质都是一样的，就是第一数组第二数组一样
		if (selectList.length) {
			params = {
				tempId: temp,
				hotTempId,
				hotOrderList: _hotLabelArr,
				orderList: selectList,
				printer,
				review,
				takeGoodsLabelPrintSource: LabelPrintSourceEnum.备货单标签,
			};
		} else {
			params = {
				tempId: hotTempId,
				orderList: _hotLabelArr,
				printer,
				review,
				takeGoodsLabelPrintSource: LabelPrintSourceEnum.备货单标签,
			};
		}
		PrintCenterAPI.batchPrintBhdXbq(params).finally(() => setIsPrintEnd(true));
		customLogPost('开始打印拿货小标签', { params });
	};
	const handlePrintLabelFinish = (val) => {
		const { temp, printer, hotTempId, sortByMarketStallSupplier } = val;
		if (sortByMarketStallSupplier) sendPoint(Pointer.备货单_调整档口顺序_勾选根据备货单打印);
		local.set('BHDPrinterAndTemp', { temp, printer, hotTempId, sortByMarketStallSupplier });
		handlePrintAct({ temp, printer, hotTempId, review: false, sortByMarketStallSupplier });
	};
	const review = async() => {
		const res = await form.validateFields();
		const { temp, printer, hotTempId, sortByMarketStallSupplier } = res;
		handlePrintAct({ temp, printer, hotTempId, review: true, sortByMarketStallSupplier });
		// console.log('res::', res);
	};

	const handleAllowGenerateType = (data) => {
		const allowObj = {
			allowGenerateAlreadySendTrade: false,
			allowGenerateRefundTrade: false,
			allowGenerateFinishedTrade: false,
			allowGenerateClosedTrade: false,
		};
		for (let key in data) {
			if (data[key].isChecked) {
				switch (key) {
					case TradeAbnormalType.已发货:
						allowObj.allowGenerateAlreadySendTrade = true;
						break;
					case TradeAbnormalType.买家申请退款:
						allowObj.allowGenerateRefundTrade = true;
						break;
					case TradeAbnormalType.交易成功:
						allowObj.allowGenerateFinishedTrade = true;
						break;
					case TradeAbnormalType.已关闭:
						allowObj.allowGenerateClosedTrade = true;
						break;
					default: break;
				}
			}
		}
		setAllowGenerateType(allowObj);
	};

	// 确认生成标签时
	const generateLabelWarningModalOnOk = (data) => {
		// 生成小标签时，告诉后端哪些勾选了
		handleAllowGenerateType(data);
		setLabelAbnormalFinalRes(data);
		setTimeout(() => {
			handlePrint();
		});
	};
	return (
		<>
			<Tooltip title={ bhdStore.bhdSetJs?.baseSet?.fastEditConfig ? "关闭快捷编辑模式后生效" : '' }>
				<Button
					type="primary"
					size="large"
					style={ !lodopInstallStatus ? { background: '#aaa', borderColor: '#aaa' } : {} }
					loading={ loading }
					onClick={ generatorLabelPrecheck }
					disabled={ bhdStore.bhdSetJs?.baseSet?.fastEditConfig }
				>
					{
						type === OptTypeEnum.打印拿货小标签 ? "打印拿货小标签" : "仅生成标签"
					}
				</Button>
			</Tooltip>


			{
				labelGenerateProgress.show && (
					<Modal
						centered
						visible
						footer={ null }
						width={ 300 }
						closable={ false }
						maskClosable={ false }
					>
						<div className={ c['label-progress-title'] }>
							<InfoCircleOutlined className={ c['label-progress-icon'] } />
							<span className="r-ml-12 r-fw-500">提示</span>
						</div>
						<div className={ c['label-progress-ing'] }>生成标签中...<span className="r-ml-8">{labelGenerateProgress.num}/{labelGenerateProgress.sum}</span></div>
					</Modal>
				)
			}

			{
				printLabelShow && (
					<Modal
						centered
						visible
						footer={ null }
						width={ 460 }
						onCancel={ () => { setPrintLabelShow(false); } }
						closable
						maskClosable={ false }
					>
						<div className={ c['label-progress-title'] }>
							<span className="r-fw-500">打印标签</span>
						</div>
						<Form
							form={ form }
							onFinish={ handlePrintLabelFinish }
							{ ...{
								labelCol: { span: 6 },
								wrapperCol: { span: 18 },
							} }
						>
							<Form.Item label="爆款标签" className="r-mt-16 r-flex r-ai-c">
								<span>{hotLabelData?.hotLabelArr?.length}</span>
							</Form.Item>
							<Form.Item label="拿货标签" className="r-mt-16 r-flex r-ai-c">
								<span>{labelNum}</span>
							</Form.Item>
							<Form.Item label="爆款标签模板" className={ c.condition1 } name="hotTempId">
								<Select >
									{tempObj?.hotTempList?.map(item => (
										<Option value={ item.Mode_ListShowId } key={ item.Mode_ListShowId } >{item.ExcodeName}</Option>
									))}
								</Select>
							</Form.Item>
							<Form.Item label="拿货标签模板" className={ c.condition1 } name="temp">
								<Select >
									{tempObj?.tempList?.map(item => (
										<Option value={ item.Mode_ListShowId } key={ item.Mode_ListShowId } >{item.ExcodeName}</Option>
									))}
								</Select>
							</Form.Item>
							<Form.Item label="打印机" className={ c.condition1 } name="printer">
								<Select >
									{printerList.map(printer => (
										<Option key={ printer } value={ printer }>{printer}</Option>
									))}
								</Select>
							</Form.Item>
							<Form.Item label="" className={ c.condition1 } name="sortByMarketStallSupplier" valuePropName="checked">
								<Checkbox
									className="r-w-full"
								>打印时根据备货单市场档口供应商进行分组
								</Checkbox>
							</Form.Item>
							<Form.Item label=" " colon={ false }>
								<Button type="link" onClick={ review }>预览</Button>
								<Button className="r-mr-12" type="primary" htmlType="submit">打印标签</Button>
								<Button onClick={ () => { setPrintLabelShow(false); } }>取消</Button>
							</Form.Item>
						</Form>

					</Modal>
				)
			}

			{
				showGenerateLabelWarningModal && (
					<GenerateLabelWarningModal
						data={ labelAbnormalObj }
						onCancel={ () => {
							setShowGenerateLabelWarningModal(false);
							customLogPost('勾选订单中包含异常，取消生成', {});
						} }
						onOk={ (data) => {
							generateLabelWarningModalOnOk(data);
							customLogPost('勾选订单中包含异常，继续生成', { abnormalData: data });
						} }
					/>
				)
			}
			{
				understockErrorMsgListModalData.show
				&& <UnderstockErrorMsgListModal understockErrorMsgList={ understockErrorMsgListModalData.understockErrorMsgList } onOk={ understockErrorMsgListModalBack } />
			}
		</>
	);
};
export default observer(BhdPrintTakeLabelBtn);
