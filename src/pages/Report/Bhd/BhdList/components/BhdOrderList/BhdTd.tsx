import React, { memo, useEffect, useState } from "react";
import { Button, Form, Input, Modal, Tooltip, message, Popover } from "antd";
import cs from 'classnames';
import { BhdColSet, BhdOrderItem, BhdSkuItem } from "@/types/schemas/report/bhd/bhdList";
import { dealImgHead, findSupplierAndUpdate, updateSupplier } from "./bhdOrderUitls";
import { BhdLabelName } from "../../../contants";
import Icon from "@/components/Icon";
import s from '../../index.module.scss';
import { getPlatformDetailLink } from '@/pages/AfterSale/TradeList/utils';
import { PLAT_JD, PLAT_SCMHAND, PLAT_XHS } from "@/constants";
import { getPurchaseNumSetVal } from "../../../BhdTableSet/utils";
import bhdStore from "@/stores/report/Bhd";
import { BhdShowType } from "../../../BhdTableSet/contants";
import FieldsPermissionCheck, { FieldsPermissionCheckTypeEnum, FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { getCostPrice, getTotalCost } from "@/pages/Trade/utils";
import { EnumBhdBaseConfig } from "../../contants";
import SupplierSearchSelect from "@/pages/Warehouse/System/Archives/components/SupplierSearchSelect";
import { calcSupplierCount } from "../../utils";
import userStore from "@/stores/user";
import { isSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import { getImageThumbnail } from "@/utils/img.scale";

interface BhdTdProps{
    col: BhdColSet;
    oData?: BhdOrderItem;
    skuItem?: BhdSkuItem;
    keyValue:string;
    colIndex?: number;
}

const onClickImg = (data, detailId) => {
	const { platform } = data;
	if (!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(data.tradeInfos[0])) return;
	const goodsDetailUrl = getPlatformDetailLink(platform, detailId);
	if (goodsDetailUrl) {
		window.open(goodsDetailUrl, '_blank');
	}
};

export const ImgPlus = (props) => {
	const [hasError, setHasError] = useState(false);

	const handleError = (e) => {
		if (!hasError) {
			setHasError(true);
			(e.target as HTMLImageElement).src = props.src;
		}
	};

	const popContent = (
		<img alt="图片" { ...props } width={ 500 } height={ 500 } />
	);
	return (
		<Popover placement="right" content={ popContent }>
			<img
				alt="图片"
				{ ...props }
				src={ hasError ? props.src : getImageThumbnail({
					noScale: false,
					url: props.src,
					width: 80,
					height: 80,
					quality: 100
				}) }
				onError={ handleError }
			/>
		</Popover>
	);
};

const getTableLineContent = (key, oData, col) => {
	const keyObj = {
		[BhdLabelName.序号]: "_orderIndex",
		[BhdLabelName.商品名称]: "title",
		[BhdLabelName.货品简称]: "titleAlias",
		[BhdLabelName.商品商家编码]: "outerId",
		[BhdLabelName.商品ID]: "numIid",
		[BhdLabelName.店铺名称]: "sellerNick",
		[BhdLabelName.店铺简称]: "sellerAbbreviation",
		[BhdLabelName.货品总售价]: "priceCount",
		[BhdLabelName.吊牌总价]: "tagPriceCount",
		[BhdLabelName.销售总价]: "paymentCount",
		[BhdLabelName.货品商家编码]: "sysOuterId",
		[BhdLabelName.货品规格名称]: "sysSkuName",
		[BhdLabelName.规格商家编码]: "skuOuterId",
		[BhdLabelName.规格名称]: "_skuName",
		[BhdLabelName.货品规格编码]: "sysSkuOuterId",
		[BhdLabelName.规格数量]: "count",
		[BhdLabelName.实际总库存]: "salableItemStock",
		[BhdLabelName.可配货库存]: "salableItemDistributableStock",
		[BhdLabelName.吊牌价]: "tagPrice",
		[BhdLabelName.销售金额]: "skuPaymentCount",
		[BhdLabelName.货品售价]: "price",
		[BhdLabelName.商品数量]: "count",
		[BhdLabelName.货位]: "warehouseSlotName",
	};
	// console.log(oData);

	let content : React.ReactNode = "";
	if (key === BhdLabelName.商品主图) {
		let id = oData.numIid;
		if ([PLAT_JD, PLAT_XHS].includes(oData.platform)) {
			// 后面的 || 是因为在规格视角的话 前面的取值取不到，直接取 oData?.skuId
			id = oData?.skuCounts?.[0]?.skuId || oData?.skuId;
		}
		content = (
			<ImgPlus
				alt=""
				className={ cs("tb-bb-img r-pointer", s['image-container']) }
				src={ dealImgHead(oData.picUrl) }
				onClick={ () => onClickImg(oData, id) }
				width={ col.imgWidth }
				height={ col.imgHeight }
			/>
		);
	} else if (key === BhdLabelName.货品主图) {
		let index = oData?.skuCounts?.findIndex(sku => sku.sysSkuPicUrl);
		content = ~index ? (
			<ImgPlus
				alt=""
				className="tb-bb-img"
				src={ dealImgHead(oData.skuCounts[index].sysSkuPicUrl) }
				width={ col.imgWidth }
				height={ col.imgWidth }
			/>
		) : '';
	} else if (key === BhdLabelName.货品规格图片) {
		content = oData.sysSkuPicUrl ? (
			<ImgPlus
				alt=""
				className="tb-bb-img"
				src={ dealImgHead(oData.sysSkuPicUrl) }
				width={ col.imgWidth }
				height={ col.imgWidth }
			/>
		) : '';
	} else if (key === BhdLabelName.规格图片) { // 规格图片
		let id = oData.numIid;
		content = oData.skuPic ? (
			<ImgPlus
				alt=""
				className={ cs("tb-bb-img r-pointer", s['image-container']) }
				src={ dealImgHead(oData.skuPic) }
				onClick={ () => onClickImg(oData, id) }
				width={ col.imgWidth }
				height={ col.imgWidth }
			/>
		) : '';
	} else if (key === BhdLabelName.商品数量 && bhdStore.showType !== BhdShowType.规格商品) {
		let total = 0;
		oData.skuCounts?.forEach(skuItem => total += +skuItem.count);
		content = total || '';
	} else if (key === BhdLabelName.规格数量 && bhdStore.showType === BhdShowType.规格商品) {
		let total = 0;
		oData.skuCounts?.forEach(skuItem => total += +(skuItem.count || 0));
		content = total || '';
	} else if (key === BhdLabelName.成本总价 && [BhdShowType.平台商品, BhdShowType.本地货品].includes(bhdStore.showType)) {
		let cost = 0;
		oData.skuCounts?.forEach(skuItem => cost += Number(skuItem.cost || 0) * +(skuItem.count || 0));
		content = (
			<FieldsPermissionCheck
				fieldsPermission={ FieldsPermissionEnum.成本价 }
				type={ FieldsPermissionCheckTypeEnum.仅展示 }
				noPermissionRender="***"
			>
				{getTotalCost(+cost || 0)}
			</FieldsPermissionCheck>
		);
	} else if (key === BhdLabelName.成本总价) {
		content = (
			<FieldsPermissionCheck
				fieldsPermission={ FieldsPermissionEnum.成本价 }
				type={ FieldsPermissionCheckTypeEnum.仅展示 }
				noPermissionRender="***"
			>
				{getTotalCost(+(oData.cost || 0) * +(oData.count || 0))}
			</FieldsPermissionCheck>
		);
	} else if (key === BhdLabelName.成本价) {
		content = (
			<FieldsPermissionCheck
				fieldsPermission={ FieldsPermissionEnum.成本价 }
				type={ FieldsPermissionCheckTypeEnum.仅展示 }
				noPermissionRender="***"
			>
				{getCostPrice(+oData.cost || 0, 0)}
			</FieldsPermissionCheck>
		);
	} else if (key === BhdLabelName.规格别名) {
		content = oData.skuAlias || oData._skuName; // 别名为空时显示规格名称 不能改
	} else if (key === BhdLabelName.在途库存) {
		content = +(oData.transitItemStock ?? 0) + +(oData.refundStockWaitHandNum ?? 0);
	} else if (key === BhdLabelName.销退在途库存) {
		content = oData.refundStockWaitHandNum ?? 0;
	} else if (key === BhdLabelName.采购在途库存) {
		content = oData.transitItemStock ?? 0;
	} else if (key === BhdLabelName.建议采购数量) {
		const curSet = getPurchaseNumSetVal(col);

		const adviceCount = +(oData.count ?? 0)
			- (curSet.isSalableItemStock ? +(oData.salableItemStock ?? 0) : 0)
			- (curSet.isPurchaseInTransitStock ? +(oData.transitItemStock ?? 0) : 0)
			- (curSet.isRefundStockWaitHandNum ? +(oData.refundStockWaitHandNum ?? 0) : 0);
		content = adviceCount > 0 ? adviceCount : 0;
	} else if (key === BhdLabelName.换行) {
		content = <br />;
	} else if (key === BhdLabelName.空格) {
		content = ' ';
	} else {
		content = oData[keyObj[key]] || '';
	}
	return content;
};

// 单行渲染 - 移除内联编辑图标
const singleLine = (oData:BhdOrderItem, col:BhdColSet, keyValue:string) => {
	const colEditDataDictionary = bhdStore.colEditDataDictionarySetting;
	const strLabel = colEditDataDictionary.strLabel;
	const dataTranslate:React.ReactNode[] = [];
	col.dataArray.forEach((key, index) => {
		const isNotCustom = strLabel.includes(key);
		let content : React.ReactNode = "";
		if (isNotCustom) {
			let label = getTableLineContent(key, oData, col);
			content = <span>{label}</span>;
		} else if (key === BhdLabelName.换行) {
			content = <br />;
		} else if (key === BhdLabelName.空格) {
			content = ' ';
		} else {
			content = key;
		}
		// eslint-disable-next-line react/no-array-index-key
		dataTranslate.push((<React.Fragment key={ keyValue + index }>{content}</React.Fragment>));
	});
	return dataTranslate;
};

// 快捷编辑模式下 本来多行展示的合并在一个单元格展示时需要
const singleMultiLine = (oData:BhdOrderItem, col:BhdColSet, keyValue:string) => {
	const dataTranslate:React.ReactNode[] = [];
	let _dataTranslate:React.ReactNode[] = [];
	oData?.skuCounts?.forEach((item) => {
		_dataTranslate.push(multiLine(item, col, keyValue, oData), <br />);
	});
	dataTranslate.push(..._dataTranslate);
	return dataTranslate;
};

// 多行渲染 - 移除内联编辑图标
const multiLine = (skuItem:BhdSkuItem, col:BhdColSet, keyValue:string, oData:BhdOrderItem) => {
	const colEditDataDictionary = bhdStore.colEditDataDictionarySetting;
	const strLabel = colEditDataDictionary.strLabel;
	const dataTranslate:React.ReactNode[] = [];
	col.dataArray.forEach((key, index) => {
		const isNotCustom = strLabel.includes(key);
		let str: React.ReactNode = '';
		if (isNotCustom) {
			skuItem.platform = skuItem.platform || oData.platform;
			skuItem.numIid = skuItem.numIid || oData.numIid;

			let label = getTableLineContent(key, skuItem, col);
			str = <span style={ { margin: '0 3px' } }>{label}</span>;
		} else if (key === BhdLabelName.换行) {
			str = <br />;
		} else {
			str = key;
		}
		dataTranslate.push((
			// eslint-disable-next-line react/no-array-index-key
			<span key={ 'sku' + keyValue + index }>
				{str}
			</span>
		));
	});
	return dataTranslate;
};

const BhdTdContent: React.FC<BhdTdProps> = memo((props) => {
	const {
		col,
		oData,
		skuItem,
		keyValue,
		colIndex
	} = props;

	const renderContent = () => {
		let key = col.showSingleLine ? colIndex : `${colIndex}_${skuItem.itemId}_${skuItem.skuId}`;
		if (oData.editContent && oData.editContent[key]) {
			return (
				<div
					key={ keyValue + 'changed' }
					style={ { minHeight: '12px' } }
					contentEditable
					suppressContentEditableWarning
					// eslint-disable-next-line react/no-danger
					dangerouslySetInnerHTML={ { __html: oData.editContent[key] } }
				/>
			);
		} else {
			return (
				<div key={ keyValue } style={ { minHeight: '12px' } } contentEditable suppressContentEditableWarning>
					{col.showSingleLine ? (col.showSingleMultiLine ? singleMultiLine(oData, col, keyValue) : singleLine(oData, col, keyValue)) : multiLine(skuItem, col, keyValue, oData)}
				</div>
			);
		}
	};

	return renderContent();
});


const BhdTdOperateContent : React.FC<{
	orderItem:BhdOrderItem;
	handleDelete: () => void;
	handleAdd: (any) => void;
	handleShowMessage: () => void;
	selectIndex: { supplierIndex:number, orderIndex:number, bhdData }
}> = (props) => {
	const {
		orderItem,
		handleDelete,
		handleAdd,
		handleShowMessage,
		selectIndex
	} = props;

	return (
		<div className="r-pl-8">
			<Tooltip
				title="删除该行"
				style={ { lineHeight: '25px' } }
				placement="bottom"
			>
				<div onClick={ handleDelete }>
					<Icon className="r-c-999" type="shanjian" pointer hover />
				</div>
			</Tooltip>
			{orderItem._hasMessage ? (
				<Tooltip
					title="查看留言备注"
					style={ { lineHeight: '25px' } }
					placement="bottom"
				>
					<div onClick={ handleShowMessage } >
						<Icon className="k-c-primary" type="liuyan" pointer />
					</div>
				</Tooltip>
			) : ''}
			{/* 添加市场供应商 */}
			{
				bhdStore.bhdSetJs?.baseSet?.[EnumBhdBaseConfig.市场档口供应商修改时更新商品档案] ? (
					// orderItem?.isRelatedSysItemAndSysSku ? (
					// 	<BhdTdMallStallSupplierSelectModal selectIndex={ selectIndex } orderItem={ orderItem } changeSupplierSuccess={ handleAdd } />
					// ) : null
					<BhdTdMallStallSupplierSelectModal selectIndex={ selectIndex } orderItem={ orderItem } changeSupplierSuccess={ handleAdd } />
				) : (
					<td
						contentEditable={ false }
						className="bhd-not-print"
					>
						<Tooltip
							title="在上方插入市场-档口-供应商"
							style={ { lineHeight: '25px' } }
							placement="bottom"
						>
							<div className="r-pl-4" onClick={ handleAdd }>
								<Icon className="r-c-999" type="tianjiabeizhu" pointer hover />
							</div>
						</Tooltip>
					</td>
				)
			}

		</div>
	);
};

// 备货单 供应商 市场 档口 选项选择
// 编辑的时候库存版显示下拉框进行选择 零库存版显示输入框
const BhdTdMallStallSupplierSelect : React.FC<{
	supplierItem:BhdOrderItem;
	changeSupplierSuccess: ({ supplier })=>void
}> = (props) => {
	const { isShowZeroStockVersion } = userStore;
	const { supplierItem, changeSupplierSuccess } = props;
	const [isEdit, setIsEdit] = useState(false);
	const [loading, setLoading] = useState(false);
	const [supplier, setSupplier] = useState<{market?: string, stall?: string, id: string, supplierName: string}>(null);

	useEffect(() => {
		setSupplier({
			id: supplierItem?.supplierId || "",
			market: supplierItem?.market || "",
			stall: supplierItem?.stall || "",
			supplierName: supplierItem?.supplierName || "",
		});
	}, [supplierItem, isEdit]);

	const handleEdit = () => {
		setIsEdit(true);
	};

	const confirmPromise = () => {
		return new Promise<boolean>((resolve, reject) => {
			Modal.confirm({
				title: '提示',
				content: '确定删除供应商吗？',
				onOk() {
					console.log('OK');
					resolve(true);
				},
				onCancel() {
					console.log('Cancel');
					resolve(false);
				},
			});
		});
	};

	const handleUpdate = async() => {
		try {
			const { supplierName = "", market = "", stall = "" } = supplierItem;
			const { supplierName: _supplierName = "", market: _market = "", stall: _stall = "" } = supplier || {};
			// 比较变更 如果没有变更就不进行更新
			// 库存版其实比较 supplierName 就可以了 但零库存版不行
			if (supplierName != _supplierName
				|| market != _market
				|| stall != _stall
			) {
				if (!supplier && !await confirmPromise()) {
					return;
				}
				setLoading(true);
				await updateSupplier({ list: supplierItem.items, supplier });
				message.success("更新供应商成功");
				changeSupplierSuccess?.({ supplier });
			} else {
				message.info("供应商没有变更");
			}
			setIsEdit(false);
			setLoading(false);
		} catch (error) {
			console.error("更新供应商失败:", error);
			setIsEdit(false);
			setLoading(false);
			message.error(`更新供应商失败:${error?.message || error || ""}`);
		}
	};
	const handleChangeSupplier = (supplier) => {
		setSupplier(supplier);
	};
	const handleChangeStallMarket = (e, type) => {
		const { value } = e.target;
		setSupplier((prev) => ({
			...prev,
			[type]: value
		}));
		// console.log("handleChangeStallMarket:", type, e);
	};

	if (bhdStore.bhdSetJs?.baseSet?.[EnumBhdBaseConfig.市场档口供应商修改时更新商品档案]) {
		return (
			<>
				{
					isEdit ? (
						<div className="r-flex r-ai-c">
							<div className="r-flex r-mr-12 r-downLoad-hidden">
								<Input
									disabled={ !isShowZeroStockVersion }
									size="small"
									value={ supplier?.market }
									placeholder="市场"
									onChange={ (e) => { handleChangeStallMarket(e, "market"); } }
								/> -
								<Input
									disabled={ !isShowZeroStockVersion }
									size="small"
									value={ supplier?.stall }
									placeholder="档口"
									onChange={ (e) => { handleChangeStallMarket(e, "stall"); } }
								/> -
								{
									isShowZeroStockVersion ? (
										<Input
											size="small"
											value={ supplier?.supplierName }
											placeholder="供应商"
											onChange={ (e) => { handleChangeStallMarket(e, "supplierName"); } }
										/>
									) : (
										<SupplierSearchSelect
											size="small"
											value={ supplier?.id }
											allowClear
											onChange={ (supplier) => {
												handleChangeSupplier(supplier);
											} }
										/>
									)
								}
							</div>
							<Button type="link" contentEditable={ false } size="small" className="r-downLoad-hidden" onClick={ handleUpdate } loading={ loading }>添加并更新</Button>
							<Button type="link" style={ { marginLeft: 12 } } className="r-pointer r-downLoad-hidden" onClick={ () => { setIsEdit(false); } }>取消</Button>
							<span style={ { visibility: "hidden" } }>{supplierItem.mallStallSupplierStr}</span>
						</div>
					) : (
						<div className="r-pl-8" contentEditable={ false }>
							<span>{supplierItem.mallStallSupplierStr}</span>
							{
								supplierItem.mallStallSupplierStr == "未绑定货品" ? null : (<Button type="link" style={ { marginLeft: 12 } } className="r-pointer r-downLoad-hidden" onClick={ handleEdit }>编辑</Button>)
							}
						</div>
					)
				}

			</>

		);
	} else {
		return (
			<div className="r-pl-8">
				{supplierItem.mallStallSupplierStr}
			</div>
		);
	}
};


const BhdTdMallStallSupplierSelectModal : React.FC<{
	orderItem: BhdOrderItem,
	selectIndex: { supplierIndex:number, orderIndex:number, bhdData },
	changeSupplierSuccess: ({ supplier, bhdData })=>void
}> = (props) => {
	const { isShowZeroStockVersion } = userStore;
	const { changeSupplierSuccess, orderItem, selectIndex } = props;
	const [visible, setVisible] = useState(false);
	const [loading, setLoading] = useState(false);
	const [supplier, setSupplier] = useState<{market?: string, stall?: string, id: string, supplierName: string}>(null);

	useEffect(() => {
		setSupplier(null);
	}, [visible]);


	const handleAddSupplier = () => {
		if (!orderItem.isRelatedSysItemAndSysSku && !isShowZeroStockVersion) {
			Modal.warning({
				title: '系统提示',
				content: <div>商品ID：{orderItem.numIid} 未绑定货品，请前往货品档案绑定关联关系后进行更新<br /></div>,
				okText: '确定'
			});
			return;
		}
		setVisible(true);
	};
	const handleOk = async() => {
		try {
			// if (!supplier.supplierName) {
			// 	message.error("请先选择供应商");
			// 	return;
			// }
			await updateSupplier({ list: [orderItem], supplier });
			message.success("更新供应商成功");
			setVisible(false);
			setLoading(false);
			const { supplierIndex, orderIndex, bhdData } = selectIndex;
			const changeItemsArr = bhdData?.supplierGroupItems?.[supplierIndex]?.items;
			const newSupplierItem = changeItemsArr.splice(orderIndex, 1);
			bhdData.supplierGroupItems[supplierIndex] = calcSupplierCount(bhdData?.supplierGroupItems?.[supplierIndex]);
			const _bhdData = await findSupplierAndUpdate({ bhdData, supplier, addItems: newSupplierItem });
			changeSupplierSuccess?.({ supplier, bhdData: _bhdData });
		} catch (error) {
			setLoading(false);
			setVisible(false);
			message.error(`更新供应商失败:${error?.message || error || ""}`);
		}
	};

	const handleChangeStallMarket = (e, type) => {
		const { value } = e.target;
		setSupplier((prev) => ({
			...prev,
			[type]: value
		}));
	};

	const handleChangeSupplier = (supplier) => {
		setSupplier(supplier);
	};

	return (
		<>
			<Modal
				centered
				visible={ visible }
				width={ 400 }
				maskClosable={ false }
				confirmLoading={ loading }
				onOk={ handleOk }
				onCancel={ () => { setVisible(false); } }
				title="添加市场档口供应商"
				className={ s.addSupplierItemModal }
			>
				<div className="r-mb-12">
					系统将修改商品对应的市场、档口、供应商信息，添加的新档口将展示在表格最下方
				</div>
				<div className="r-flex r-jc-fs r-mb-12">
					<div style={ { width: 50, textAlign: "right", marginRight: 12 } }>市场</div>
					<Input
						disabled={ !isShowZeroStockVersion }
						style={ { width: 220, padding: "0px 4px" } }
						value={ supplier?.market }
						onChange={ (e) => { handleChangeStallMarket(e, "market"); } }
						placeholder="市场"
					/>
				</div>
				<div className="r-flex  r-jc-fs r-mb-12">
					<div style={ { width: 50, textAlign: "right", marginRight: 12 } }>档口</div>
					<Input
						disabled={ !isShowZeroStockVersion }
						value={ supplier?.stall }
						onChange={ (e) => { handleChangeStallMarket(e, "stall"); } }
						placeholder="档口"
						style={ { width: 220, padding: "0px 4px" } }
					/>
				</div>
				<div className="r-flex r-jc-fs r-mb-12">
					<div style={ { width: 50, textAlign: "right", marginRight: 12 } }>供应商</div>
					{
						isShowZeroStockVersion ? (
							<Input
								value={ supplier?.supplierName }
								placeholder="供应商"
								style={ { width: 220, padding: "0px 4px" } }
								onChange={ (e) => { handleChangeStallMarket(e, "supplierName"); } }
							/>
						) : (
							<SupplierSearchSelect
								size="small"
								style={ { width: 220 } }
								value={ supplier?.id }
								allowClear
								onChange={ (supplier) => {
									handleChangeSupplier(supplier);
								} }
							/>
						)
					}
				</div>
				<div className="r-fs-12 r-c-666">
					注意：修改商品市场、档口资料将会在几分钟内同步至商品档案中
				</div>
			</Modal>
			<td
				contentEditable={ false }
				className="bhd-not-print"
			>
				<Tooltip
					title="给该商品添加市场、档口、供应商"
					style={ { lineHeight: '25px' } }
					placement="bottom"
				>
					<div className="r-pl-4" onClick={ () => { handleAddSupplier(); } }>
						<Icon className="r-c-999" type="tianjiabeizhu" pointer hover />
					</div>
				</Tooltip>
			</td>
		</>
	);
};

export {
	BhdTdOperateContent,
	BhdTdContent,
	BhdTdMallStallSupplierSelect,
	BhdTdMallStallSupplierSelectModal
};
