import _, { cloneDeep } from "lodash";
import { message } from "antd";
import { BhdBaseSetType, BhdColSet, BhdOrderItem, ReportQueryBhdItemList, TradeGetBhdGenerateResultResponse } from "@/types/schemas/report/bhd/bhdList";
import { local } from "@/libs/db";
import { TradeSetFilterWord } from "@/types/trade/tradeSet";
import { filterPrintContent } from "@/utils/trade/printContent";
import { BhdLabelName } from "../../../contants";
import bhdStore from "@/stores/report/Bhd";
import { getWarehouseVersion } from "@/pages/AfterSale/TradeList/utils";
import { BhdShowType } from "../../../BhdTableSet/contants";
import userStore from "@/stores/user";
import { BatchUpdateSysItemByPlatformApi, ItemSysItemUpdateSysSkuInfoWithBatchApi } from "@/apis/warehouse/system";
import { calcSupplierCount, getAddNums } from "../../utils";


/**
 * [dealTableContentStyle 备货单，表格样式 处理
 * @param  {[array]} colsSet [表格每列样式]
 * @param  {[object]} basicSet  [基础设置样式]
 */

export const dealTableContentStyle = (colsSet:BhdColSet[], basicSet: BhdBaseSetType) => {
	let data = cloneDeep(colsSet);

	const colEditDataDictionary = bhdStore.colEditDataDictionarySetting;

	const strStar = colEditDataDictionary.strStar;
	const strNoStar = colEditDataDictionary.strNoStar;

	// 首先检测有木有带*,和自定义的item
	data?.forEach(item => {
		item.showSingleMultiLine = false;
		item.showSingleLine = true; // 默认单行显示
		item.hasStar = false; // 没有*
		item.hasNoStar = false; // 没有非*

		if (item.dataArray === undefined) {
			item.dataArray = (item.datas === undefined ? '' : item.datas).split('|');
		}

		let labelItem; let i = 0;
		let len = item.dataArray.length;

		for (; i < len; i++) {
			labelItem = item.dataArray[i];
			// 存在星号的label
			if (strStar.indexOf(labelItem) > -1) {
				item.hasStar = true;
			} else if (strNoStar.indexOf(labelItem) > -1) {
				item.hasNoStar = true;
			}
		}
		if (!item.hasStar && item.hasNoStar) { // 没有*的前提下，有非*
			item.showSingleLine = false;
		}
		// 针对商品ID的特殊处理
		if (bhdStore.showType !== BhdShowType.规格商品 && len == 1 && item.dataArray[0] == BhdLabelName.商品ID) {
			item.showSingleLine = true;
		}
		// 针对按货品规格维度规格数量的特殊处理
		if (bhdStore.showType === BhdShowType.货品规格 && item.dataArray[0] == BhdLabelName.规格数量) {
			item.showSingleLine = true;
		}
		// 各样式赋值
		if (!item.width) { // 默认宽度
			item.width = 150;
		}
		if (!item.font) { // 字体
			item.font = basicSet.fontFamily;
		}
		if (!item.size || item.size == 0) { // 字号
			item.size = basicSet.fontSize;
		}
		if (!item.showSingleLine && basicSet.fastEditConfig) {
			item.showSingleMultiLine = true;
			item.showSingleLine = true;
		}
		// 图片尺寸
		item.imageSize = basicSet.imageSize;
		let imgWH = basicSet.imageSize.split('x');
		// 主图宽度
		item.imgWidth = imgWH[0];
		// 主图高度
		item.imgHeight = imgWH[1];
		// 行间距
		item.lineHeight = basicSet.lineHeight;
	});
	return data;
};


export const dealBhdList = (bhdItemList: BhdOrderItem[], colsSet: BhdColSet[], filterWord: string[]) => {
	// 1. 过滤词处理
	const keyWords:string[] = filterWord;

	// 2. 模版生成
	const operateMap = {};
	bhdItemList.forEach((orderItem, index) => {
		orderItem._cost = 0;
		orderItem.skuCounts.forEach(skuItem => {
			skuItem._skuName = filterPrintContent(skuItem.skuName);
			orderItem._cost += Number(skuItem.cost);
		});

		orderItem._hasMessage = orderItem.tradeInfos.some(item => {
			return item.buyerMessage || (item.sellerFlag && item.sellerFlag != '0') || item.sellerMemo;
		});
		if (orderItem._hasMessage) {
			operateMap[orderItem.platform + orderItem.numIid] = {
				showMsg: local.get('report.bhd.showMsg') == 1
			};
		}
		// orderItem._tdContents = getTranslateDataArray(orderItem, colsSet, index);
	});

	return {
		bhdItemList,
		operateMap
	};
};

interface IdealBhdListNew {
	supplierGroupItems: TradeGetBhdGenerateResultResponse['data']['result']['supplierGroupItems'], 
	colsSet: BhdColSet[], 
	filterWord: string[],
	showType: BhdShowType
}

export const dealBhdListNew = ({ supplierGroupItems, colsSet, filterWord, showType } : IdealBhdListNew) => {
	// 1. 过滤词处理
	// 2. 模版生成
	const operateMap = {};
	let canCheckedBhdTableData:any = [];
	let _index = 1;
	// 规格商品过滤词特殊处理
	supplierGroupItems.forEach((supplierItem, supplierIndex) => {
		supplierItem.items.forEach((orderItem, index) => {
			orderItem._cost = 0;
			orderItem._orderIndex = _index++;
			if ([BhdShowType.规格商品].includes(showType)) {
				orderItem._skuName = filterPrintContent(orderItem.skuName);
			} else {
				orderItem.skuCounts.forEach(skuItem => {
					skuItem._skuName = filterPrintContent(skuItem.skuName);
					orderItem._cost += Number(skuItem.cost || 0) * +(skuItem.count || 0);
				});
			}

			orderItem._hasMessage = orderItem.tradeInfos.some(item => {
				return item.buyerMessage || (item.sellerFlag && item.sellerFlag != '0') || item.sellerMemo;
			});
			if (orderItem._hasMessage) {
				operateMap[orderItem.platform + orderItem.numIid] = {
					showMsg: local.get('report.bhd.showMsg') == 1
				};
			}
		});
	});

	return {
		supplierGroupItems,
		operateMap,
		canCheckedBhdTableData
	};
};

export const dealImgHead = (src:string = '') => {
	const url = src.trim();
	let res;
	if (url == '') {
		return '';
	} else {
		try {
			// 这些开头的统一改为https开头
			res = url.replace('http://', '').replace('https://', '').replace(/^\/\//, '');
			res = 'https://' + res;
			return res;
		} catch (e) {
			return '';
		}
	}
};

export const countBhdTotal = (supplierGroupItems : TradeGetBhdGenerateResultResponse['data']['result']['supplierGroupItems']) => {
	let totalCost:number = 0;
	let totalCount:number = 0;
	let totalPaymentCount:number = 0; // 新增总实付金额
	supplierGroupItems.forEach((supplier) => {
		supplier.items.forEach(order => {
			order.skuCounts.forEach((sku) => {
				totalCost += Number(sku.cost) * sku.count;
				totalCount += sku.count;
				totalPaymentCount = getAddNums(sku.skuPaymentCount || 0, totalPaymentCount);
			});
		});
	});
	return {
		totalCost,
		totalCount,
		totalPaymentCount
	};
};

export const checkSelectList = (bhdData:TradeGetBhdGenerateResultResponse['data']['result']) => {
	const { supplierGroupItems } = bhdData;
	let canSelectList = 0;
	let selectListLen = 0;
	let selectCheckList = [];
	supplierGroupItems.forEach((supplierItem, index) => {
		canSelectList += supplierItem.items.length;
		let selectItemLen = 0;
		supplierItem.items.forEach(item => {
			if (item.isChecked) {
				selectItemLen++;
				selectCheckList.push(item);
			}
		});
		selectListLen += selectItemLen;
		// 判断市场-档口-供应商中是否是全选 半选 或者 未选
		if (selectItemLen === 0) {
			supplierGroupItems[index].checkStatus = 0;
		} else if (selectItemLen === supplierItem.items.length) {
			supplierGroupItems[index].checkStatus = 1;
		} else {
			supplierGroupItems[index].checkStatus = 0.5;
		}
	});
	// 判断总列表是否为全选半选或未选
	if (selectListLen === 0) {
		return 0;
	} else if (canSelectList === selectListLen) {
		return 1;
	} else {
		return 0.5;
	}
	
};

// 更新供应商
export const updateSupplier = async({ list, supplier }) => {
	const { isShowZeroStockVersion } = userStore;
	if (isShowZeroStockVersion) {
		await updateSupplierWithNoStock({ list, supplier });
	} else {
		await updateSupplierWithStock({ list, supplier });
	}
	
};

// 库存版更新供应商
export const updateSupplierWithStock = async({ list, supplier }) => {
	try {
		if (isAllRelatedSysItemAndSysSku(list)) {
			message.error("有未关联商品，请先处理关联关系");
			throw new Error("有未关联商品，请先处理关联关系");
		}
		const updateSysSkuObj = {};
		list.forEach(item => {
			item.tradeInfos.forEach(_trade => {
				const { sysItemId, sysSkuId } = _trade.orderInfo;
				if (!updateSysSkuObj[sysItemId]) {
					updateSysSkuObj[sysItemId] = { [sysSkuId]: true };
				} else {
					updateSysSkuObj[sysItemId] = {
						...updateSysSkuObj[sysItemId],
						[sysSkuId]: true
					};
				}
			});
		});
		// 数据组装 updateSysSkuInfos: [{ sysItemId: string, sysSkuIds: string[] }]
		const updateSysSkuInfos = [];
		for (const key in updateSysSkuObj) {
			const sysSkuIds = [];
			for (const sysId in updateSysSkuObj[key]) {
				sysSkuIds.push(sysId);
			}
			updateSysSkuInfos.push({ sysItemId: key, sysSkuIds });
		}
		const params = {
			supplierId: supplier?.id || -1,
			updateSysSkuInfos
		};
		const res = await ItemSysItemUpdateSysSkuInfoWithBatchApi(params);
		const failItem = res?.find(item => (!item.success));
		if (failItem) {
			throw new Error(`更新失败${failItem.errorMessage || ""}`);
		}
	} catch (error) {
		console.error("updateSupplierWithStock error:", error);
		throw new Error(error);
		
	}
};

// 零库存版更新供应商
export const updateSupplierWithNoStock = async({ list, supplier }) => {
	try {
		const updateSysSkuObj:any = {};
		// 在tradeInfo中将所有要更改的商品查到并去重
		list.forEach(item => {
			item.tradeInfos.forEach(_trade => {
				let { sellerId, platformType } = _trade;
				let { itemId, skuId, sellerId: _sellerId } = _trade.orderInfo;
				const _name = `${itemId}_${skuId}`;
				const _itemIdArr = itemId.split("_");
				if (_itemIdArr?.length > 1) {
					let _skuIdArr = skuId.split("_");
					platformType = _itemIdArr[0].toUpperCase();
					itemId = _itemIdArr[1];
					skuId = _skuIdArr[1];
				}
				if (!updateSysSkuObj[_name]) {
					updateSysSkuObj[_name] = { 
						numIid: itemId,
						skuId,
						sellerId: _sellerId || sellerId,
						platformType,
						operateType: "BIND"
				 };
				}
			});
		});
		const params = [];
		const { supplierName, market, stall } = supplier;
		for (const key in updateSysSkuObj) { 
			params.push({
				...updateSysSkuObj[key],
				supplierName,
				market,
				stall,
				sysItemLogSourceEnum: window?.location?.hash?.includes('report/bhd/BhdList') ? 'BHD_EDIT' : 'BHD_LABEL_EDIT'
			});
		}
		console.log('window?.location?.hash', window?.location?.hash);
		const res = await BatchUpdateSysItemByPlatformApi(params);
		const errorItem = res?.find(item => (!item.success));
		if (errorItem) {
			return Promise.reject(errorItem);
		} else {
			return Promise.resolve();
		}
	} catch (error) {
		console.log("updateSupplierWithNoStock error:", error);
		return Promise.reject(error);
	}
};

// 判断要更新的数据是否都有关联关系
export const isAllRelatedSysItemAndSysSku = (list) => {
	// 库存版 如果没有供应商id的，底下的商品可能是没有关联货品的 
	return list.some(item => !item.isRelatedSysItemAndSysSku);
};

export const findSupplierAndUpdate = async({ bhdData, supplier, addItems }) => {
	let hasSupplierIndex = -1;
	if (!supplier || (!supplier.supplierName && !supplier.market && !supplier.stall)) {
		hasSupplierIndex = bhdData?.supplierGroupItems?.findIndex((item) => (!item.supplierName && !item.market && !item.stall && item.mallStallSupplierStr != "未绑定货品"));
	} else {
		hasSupplierIndex = bhdData?.supplierGroupItems?.findIndex((item) => (item.supplierName == supplier?.supplierName && item.market == supplier?.market && item.stall == supplier?.stall));
	}
	if (hasSupplierIndex > -1) {
		bhdData?.supplierGroupItems?.[hasSupplierIndex]?.items.push(...addItems);
		bhdData.supplierGroupItems[hasSupplierIndex] = await calcSupplierCount({
			...bhdData.supplierGroupItems[hasSupplierIndex]
		});
	} else {
		const { market = "", id = "", stall = "", supplierName = "" } = supplier;
		const addSupplierItem = await calcSupplierCount({
			...supplier,
			supplierId: id,
			items: addItems,
			mallStallSupplierStr: `${market}-${stall}-${supplierName}`
		});
		bhdData?.supplierGroupItems.push(addSupplierItem);
	}
	bhdData = await updateBhdDataIndex(bhdData);
	return bhdData;
};

export const updateBhdDataIndex = (bhdData) => {
	const supplierGroupItems = _.cloneDeep(bhdData?.supplierGroupItems);
	let _index = 1;
	supplierGroupItems.forEach((supplierItem, supplierIndex) => {
		supplierItem.items = supplierItem.items.map((orderItem, index) => {
			return {
				...orderItem,
				_orderIndex: _index++
			};
		});
	});
	const _bhdData = {
		...bhdData,
		supplierGroupItems
	};
	return _bhdData;
};


