import { <PERSON>, Modal, Too<PERSON><PERSON>, Popover, But<PERSON>, message, Dropdown, Menu, Checkbox, Switch } from "antd";
import { CheckboxChangeEvent } from "antd/lib/checkbox/Checkbox";
import React, { memo, useEffect, useState, useMemo, useCallback } from "react";
import { useSetState, useToggle } from "ahooks";
import { useHistory } from "react-router-dom";
import update from "immutability-helper";
import _, { cloneDeep, divide } from "lodash";
import cs from 'classnames';
import { observer } from "mobx-react";
import { runInAction } from "mobx";
import { QuestionCircleOutlined, DownOutlined, SettingOutlined } from "@ant-design/icons";
import { BhdColSet, BhdOrderItem, ReportQueryBhdItemList, TradeGetBhdGenerateResultResponse } from "@/types/schemas/report/bhd/bhdList";
import s from '../../index.module.scss';
import { checkSelectList, countBhdTotal, dealBhdList, dealBhdListNew, dealTableContentStyle, findSupplierAndUpdate, updateBhdDataIndex } from "./bhdOrderUitls";
import Icon from "@/components/Icon";
import BhdMsgDetail from "./BhdMsgDetail";
import { local } from "@/libs/db";
import { useStores } from "@/stores/tool";
import tradeSetStore from "@/stores/trade/tradeSet";
import bhdStore from "@/stores/report/Bhd";
import { BhdTdContent, BhdTdMallStallSupplierSelect, BhdTdMallStallSupplierSelectModal, BhdTdOperateContent } from "./BhdTd";
import { getWarehouseVersion } from "@/pages/AfterSale/TradeList/utils";
import './index.scss';
import { BhdShowType } from "../../../BhdTableSet/contants";
import userStore from "@/stores/user";
import { getTradePlatformLabel } from "@/pages/Trade/utils";
import { calcSupplierCount, StockVersionEnum } from "../../utils";
import { EnumBhdBaseConfig, 显示档口统计数据, 显示档口数量, 显示成本价, 显示实付金额 } from "../../contants";
import FieldsPermissionCheck, { FieldsPermissionCheckTypeEnum, FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { BhdTdMallStallSupplierSortModal } from "./BhdSupplierSort";
import { BhdLabelName } from "../../../contants";
import FilterKeyWordModal from "@/pages/Trade/components/ListItem/components/FilterKeyWordModal";
import PrintCenter from '@/print/index';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import LowVersionControlModal, { lowVersionLock } from '@/components-biz/LowVersionControlModal';
import { PageNameControlEnum } from '@/components-biz/LowVersionControlModal/constant';
import EditGoodsDrawer from '../EditGoodsDrawer';
import AddGoodsDrawer, { AddTypeEnum } from '@/pages/Trade/components/AddGoodsDrawer';
import { filterPrintContent } from "@/utils/trade/printContent";

interface BhdOrderListProps {
	bhdData: TradeGetBhdGenerateResultResponse['data']['result'];
	printSet?: string[];
	onPrintSetChange?: (value: string[]) => void;
	isStallBhd?: boolean;
	isStock?: StockVersionEnum;
	onRefreshData?: () => void;
	onCheckUpdateStatus?: (checkFn: (action: string) => boolean) => void;
}

interface IHandleAddSupplierAndUpdate { supplierIndex: number, orderIndex: number, bhdData: TradeGetBhdGenerateResultResponse['data']['result'], orderItem: any }
interface BhdOperateMap {
	[k: string]: {
		showMsg: boolean
	}
}

// 新增：定义变更单元格的接口
interface UpdatedCell {
	supplierIndex: number;
	orderIndex: number;
	colId: string;
	skuIndex?: number;
}

const BdhTableOperateWidth = 30;

const getTotalJsx = (bhdData: TradeGetBhdGenerateResultResponse['data']['result']) => {
	let res = [];
	const tradeCountMap = bhdData.tradeCountMap;
	for (const key in tradeCountMap) {
		res.push(
			<div className="r-flex r-fw-w" key={ key }>
				<div className="r-mr-8 r-flex r-fw-w">
					<p style={ { width: 16, height: 16, marginRight: 2 } }>{getTradePlatformLabel(key, 16)}</p>
					{
						Object.keys(tradeCountMap[key]).map(item => (
							<p style={ { whiteSpace: "nowrap", paddingTop: 1 } } key={ item }> {item || "无店铺"}:{tradeCountMap[key][item]} </p>
						))
					}
				</div>
			</div>
		);
	}
	return res;
};

let tempEditContent;

const checkCellHasEditableFields = (col: BhdColSet, bhdGoodsDataSwitch: boolean, orderItem: any, skuItem?: any) => {
	if (!bhdGoodsDataSwitch) return false;

	// 检查单元格配置中是否包含可编辑的字段类型
	return col.dataArray.some(key => {
		// 根据字段类型判断是否为可编辑字段
		switch (key) {
			// 商品维度字段 - 总是从 orderItem 获取
			case BhdLabelName.商品名称:
			case BhdLabelName.商品ID:
			case BhdLabelName.货品简称:
			case BhdLabelName.商品商家编码:
				return true;

			// 规格维度字段 - 根据显示模式决定数据源
			case BhdLabelName.规格名称:
			case BhdLabelName.规格商家编码:
			case BhdLabelName.规格图片:
			case BhdLabelName.规格别名:
				return true;
			default:
				return false;
		}
	});
};

// 修改：获取单元格中的字段类型信息 - 修复fastEditConfig导致的数据获取问题
const getCellFieldInfo = (col: BhdColSet, orderItem: any, skuItem?: any, allColsSet?: BhdColSet[]) => {
	const goodsFields = [BhdLabelName.商品名称, BhdLabelName.商品ID, BhdLabelName.货品简称, BhdLabelName.商品商家编码];
	const skuFields = [BhdLabelName.规格名称, BhdLabelName.规格商家编码, BhdLabelName.规格图片, BhdLabelName.规格别名];

	const fieldInfo = {
		hasGoodsFields: false,
		hasSkuFields: false,
		fieldsData: {}
	};

	// 获取商品字段值的辅助函数
	const getGoodsFieldValue = (key: string) => {
		switch (key) {
			case BhdLabelName.商品名称:
				return orderItem?.title;
			case BhdLabelName.商品ID:
				return orderItem?.numIid;
			case BhdLabelName.货品简称:
				return orderItem?.titleAlias;
			case BhdLabelName.商品商家编码:
				return orderItem?.outerId;
			case BhdLabelName.商品主图:
				return orderItem?.picUrl;
			default:
				return null;
		}
	};

	// 获取规格字段值的辅助函数 - 修复fastEditConfig问题
	const getSkuFieldValue = (key: string, col: BhdColSet, skuItem?: any) => {
		switch (key) {
			case BhdLabelName.规格名称:
				// 修复：当有skuItem时优先使用skuItem的数据，避免fastEditConfig影响
				if (skuItem && skuItem.skuName !== undefined) {
					return skuItem.skuName;
				}
				// 降级：使用orderItem的数据
				if (col.showSingleLine) {
					return orderItem?.skuName;
				} else {
					return orderItem?.skuName;
				}

			case BhdLabelName.规格商家编码:
				// 修复：当有skuItem时优先使用skuItem的数据
				if (skuItem && skuItem.skuOuterId !== undefined) {
					return skuItem.skuOuterId;
				}
				// 降级：使用orderItem的数据
				if (col.showSingleLine) {
					return orderItem?.skuOuterId;
				} else {
					return orderItem?.skuOuterId;
				}

			case BhdLabelName.规格图片:
				// 修复：当有skuItem时优先使用skuItem的数据
				if (skuItem && skuItem.skuPic !== undefined) {
					return skuItem.skuPic;
				}
				// 降级：使用orderItem的数据
				if (col.showSingleLine) {
					return orderItem?.skuPic;
				} else {
					return orderItem?.skuPic;
				}

			case BhdLabelName.规格别名:
				// 修复：当有skuItem时优先使用skuItem的数据
				if (skuItem) {
					// 优先使用别名，如果没有别名则使用规格名称
					return skuItem.skuAlias || skuItem.skuName;
				}
				// 降级：使用orderItem的数据
				if (col.showSingleLine) {
					return orderItem?.skuAlias || orderItem?.skuName;
				} else {
					return orderItem?.skuAlias || orderItem?.skuName;
				}

			default:
				return null;
		}
	};

	// 处理当前列的字段
	col.dataArray.forEach(key => {
		let value = null;

		// 检查是否是商品字段
		if (goodsFields.includes(key as BhdLabelName)) {
			value = getGoodsFieldValue(key);
			fieldInfo.hasGoodsFields = true;
			// 始终收集字段数据，不管是否有值
			fieldInfo.fieldsData[key] = value || '';
		}

		// 检查是否是规格字段
		if (skuFields.includes(key as BhdLabelName)) {
			value = getSkuFieldValue(key, col, skuItem);
			fieldInfo.hasSkuFields = true;
			// 始终收集字段数据，不管是否有值
			fieldInfo.fieldsData[key] = value || '';
		}
	});

	// 修正：如果当前列有规格字段，只需要收集所有列中的商品字段，不收集其他规格字段
	if (fieldInfo.hasSkuFields && allColsSet) {
		// 遍历所有列配置，只收集商品字段
		allColsSet.forEach(colSet => {
			colSet.dataArray.forEach(key => {
				// 只处理商品字段，且该字段还没有被添加过
				if (goodsFields.includes(key as BhdLabelName) && !Object.prototype.hasOwnProperty.call(fieldInfo.fieldsData, key)) {
					const value = getGoodsFieldValue(key);
					fieldInfo.hasGoodsFields = true;
					// 始终收集字段数据，不管是否有值
					fieldInfo.fieldsData[key] = value || '';
				}
			});
		});
	}

	return fieldInfo;
};

const BhdOrderList: React.FC<BhdOrderListProps> = observer((props) => {
	const { isShowZeroStockVersion } = userStore;
	const {
		bhdData: bhdDataProps,
		printSet = [],
		onPrintSetChange,
		isStallBhd,
		isStock,
		onRefreshData,
		onCheckUpdateStatus
	} = props;
	const history = useHistory();
	const [visibleFilterModal, { setLeft, setRight }] = useToggle();

	const [colsSet, setColsSet] = useState<BhdColSet[]>();
	const [bhdData, setBhdData] = useSetState<TradeGetBhdGenerateResultResponse['data']['result']>({});
	const [unfoldMsg, { toggle, set: setToggle }] = useToggle<boolean>(local.get('report.bhd.showMsg'));
	const [operateMap, setOperateMap] = useSetState<BhdOperateMap>({});
	const store: typeof tradeSetStore = useStores('tradeSetStore');
	const storeBhdSet: typeof bhdStore = useStores('bhdStore');
	const baseSet = storeBhdSet.bhdSetHasSaved?.baseSet;
	const { showType } = storeBhdSet;
	const [bhdTableCheckBtnStatus, setBhdTableCheckBtnStatus] = useState(0);
	const setIsDisabledBhdGeneratePurchaseOrderBtn = storeBhdSet.setIsDisabledBhdGeneratePurchaseOrderBtn;
	const [showStallNum, setShowStallNum] = useState(false); // 是否显示档口数量
	const [showCost, setShowCost] = useState(false); // 是否显示成本价
	const [showPayment, setShowPayment] = useState(false); // 是否显示实付金额
	const [showContentDropdown, setShowContentDropdown] = useState(false);
	const [showPrintDropdown, setShowPrintDropdown] = useState(false);
	const [showSortModal, setShowSortModal] = useState(false);
	const [bhdGoodsDataSwitch, setBhdGoodsDataSwitch] = useState(false);
	const [showVersionModal, setShowVersionModal] = useState({ visible: false, pageName: '' }); // 版本控制
	const [editGoodsDrawer, setEditGoodsDrawer] = useState({
		visible: false,
		orderItem: null,
		skuItem: null,
		fieldInfo: null,
		currentCol: null
	});

	const [hasAnyUpdates, setHasAnyUpdates] = useState(false); // 全局变更状态，用于拦截操作
	const [updatedCells, setUpdatedCells] = useState<Set<string>>(new Set()); // 单元格变更状态，用于背景色
	const [hasAnyNewItems, setHasAnyNewItems] = useState(false); // 新增商品状态，用于背景色

	const [addGoodsDrawer, setAddGoodsDrawer] = useState({
		visible: false,
	});

	// 新增：生成单元格唯一标识
	const generateCellKey = (supplierIndex: number, orderIndex: number, colId: string, skuIndex?: number) => {
		if (skuIndex !== undefined) {
			return `${supplierIndex}_${orderIndex}_${colId}_${skuIndex}`;
		}
		return `${supplierIndex}_${orderIndex}_${colId}`;
	};

	// 【新增】优化：缓存可编辑字段检查结果，减少重复计算
	const editableFieldsCache = useMemo(() => {
		if (!bhdGoodsDataSwitch) {
			return new Map();
		}

		const cache = new Map<string, boolean>();

		bhdData.supplierGroupItems?.forEach((supplierItem, supplierIndex) => {
			supplierItem.items?.forEach((orderItem, orderIndex) => {
				colsSet?.forEach((col) => {
					// 商品级别缓存
					const goodsKey = `${supplierIndex}_${orderIndex}_${col.id}`;
					cache.set(goodsKey, checkCellHasEditableFields(col, bhdGoodsDataSwitch, orderItem));

					// 规格级别缓存
					orderItem.skuCounts?.forEach((skuItem, skuIndex) => {
						const skuKey = `${supplierIndex}_${orderIndex}_${col.id}_${skuIndex}`;
						cache.set(skuKey, checkCellHasEditableFields(col, bhdGoodsDataSwitch, orderItem, skuItem));
					});
				});
			});
		});

		return cache;
	}, [bhdData, colsSet, bhdGoodsDataSwitch]);

	// 【新增】优化：快速检查函数
	const isFieldEditable = useCallback((supplierIndex: number, orderIndex: number, colId: any, skuIndex?: number) => {
		if (!bhdGoodsDataSwitch) return false;

		const key = skuIndex !== undefined
			? `${supplierIndex}_${orderIndex}_${colId}_${skuIndex}`
			: `${supplierIndex}_${orderIndex}_${colId}`;

		return editableFieldsCache.get(key) || false;
	}, [bhdGoodsDataSwitch, editableFieldsCache]);

	// 修改：变更状态拦截函数 - 基于全局变更状态
	const checkUpdateStatusAndIntercept = (action: string) => {
		if (hasAnyUpdates) {
			Modal.confirm({
				title: '提示',
				content: '存在已更新的商品信息，请重新查询操作',
				okText: '确定',
				cancelText: '取消',
				onOk: () => {
					// 用户确认后不执行任何操作，只关闭弹框
				}
			});
			return true; // 拦截操作
		}
		return false; // 不拦截
	};

	// 修改：重置变更状态 - 重置两个状态
	const resetUpdatedStatus = () => {
		setHasAnyUpdates(false);
		setUpdatedCells(new Set());
		setHasAnyNewItems(false);
	};

	useEffect(() => {
		if (storeBhdSet.isDisabledBhdGeneratePurchaseOrderBtn) {
			setBhdTableCheckBtnStatus(0);
		}
	}, [storeBhdSet.isDisabledBhdGeneratePurchaseOrderBtn]);

	useEffect(() => {
		local.get(显示档口数量) && setShowStallNum(true);
		local.get(显示成本价) && setShowCost(true);
		local.get(显示实付金额) && setShowPayment(true);
	}, []);


	useEffect(() => {
		// 根据 baseSet 处理colsSet 数据
		const colsSet = dealTableContentStyle(storeBhdSet.bhdSetHasSaved.colsSet, storeBhdSet.bhdSetHasSaved.baseSet);
		const {
			supplierGroupItems,
			operateMap
		} = dealBhdListNew({
			supplierGroupItems: bhdDataProps.supplierGroupItems,
			colsSet,
			filterWord: store.filterWord,
			showType: storeBhdSet.showType
		});
		setColsSet(colsSet);
		setBhdData(prev => {
			prev = {
				...bhdDataProps,
				supplierGroupItems
			};
			console.log('%c [ bhdData ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', prev);
			return prev;
		});
		setOperateMap(operateMap);

		// 重置所有变更状态（当数据重新查询时）
		resetUpdatedStatus();

		// 根据 baseSet 处理宝贝排序及合并相关
	}, [bhdDataProps, setBhdData, setOperateMap, store.filterWord, store.filterPatternList, storeBhdSet?.bhdSetHasSaved?.baseSet, storeBhdSet?.bhdSetHasSaved?.colsSet, storeBhdSet.showType]);

	// 修改：删除操作前检查变更状态
	const handleDelete = ({ platform, numIid, supplierIndex, orderIndex }: { platform: string, numIid: string, supplierIndex: number, orderIndex: number }) => {
		if (checkUpdateStatusAndIntercept('删除商品')) {
			return;
		}

		Modal.confirm({
			centered: true,
			title: '提示',
			content: '是否删除该行',
			onOk: () => {
				const newItems = update(bhdData.supplierGroupItems[supplierIndex].items, {
					$splice: [
						[orderIndex, 1]
					]
				});
				bhdData.supplierGroupItems[supplierIndex].items = newItems;
				bhdData.supplierGroupItems[supplierIndex] = calcSupplierCount(bhdData.supplierGroupItems[supplierIndex]);
				const { totalCount, totalCost, totalPaymentCount } = countBhdTotal(bhdData.supplierGroupItems);
				setBhdData((prev) => {
					prev.supplierGroupItems[supplierIndex].items = newItems;
					prev.totalCount = totalCount;
					prev.totalCost = totalCost;
					prev.totalPayment = totalPaymentCount;
					storeBhdSet.setBhdData(prev.supplierGroupItems);
					return prev;
				});
			}
		});
	};

	// 修改：删除供应商操作前检查变更状态
	const handleDeleteSupplier = (supplierIndex: number) => {
		if (checkUpdateStatusAndIntercept('删除供应商')) {
			return;
		}

		Modal.confirm({
			centered: true,
			title: '提示',
			content: '是否删除该行',
			onOk: () => {
				const newItems = update(bhdData.supplierGroupItems, {
					$splice: [
						[supplierIndex, 1]
					]
				});
				bhdData.supplierGroupItems = newItems;
				const { totalCount, totalCost, totalPaymentCount } = countBhdTotal(bhdData.supplierGroupItems);
				setBhdData((prev) => {
					prev.supplierGroupItems = newItems;
					prev.totalCount = totalCount;
					prev.totalCost = totalCost;
					prev.totalPayment = totalPaymentCount;
					storeBhdSet.setBhdData(prev.supplierGroupItems);
					return prev;
				});
			}
		});
	};

	// 添加市场档口供应商
	const handleAddSupplier = ({ supplierIndex, orderIndex, orderItem, bhdData }: { supplierIndex: number, orderIndex: number, orderItem: any, bhdData?: any }) => {
		// 开启 市场档口供应商修改时更新商品档案 插入供应商
		if (bhdData) {
			const { totalCount, totalCost, totalPaymentCount } = countBhdTotal(bhdData.supplierGroupItems);
			setBhdData((prev) => {
				prev.supplierGroupItems = bhdData.supplierGroupItems;
				prev.totalCount = totalCount;
				prev.totalCost = totalCost;
				prev.totalPayment = totalPaymentCount;
				storeBhdSet.setBhdData(prev.supplierGroupItems);
				return prev;
			});
		} else {
			// 未开启 市场档口供应商修改时更新商品档案 插入供应商
			handleAddSupplierNoUpdate({ supplierIndex, orderIndex });
		}
	};

	// 未开启 市场档口供应商修改时更新商品档案 插入供应商
	const handleAddSupplierNoUpdate = ({ supplierIndex, orderIndex }: { supplierIndex: number, orderIndex: number }) => {
		try {
			const addSupplierItem = bhdData.supplierGroupItems[supplierIndex];
			let _addSupplierItem = [
				{
					mallStallSupplierStr: addSupplierItem.mallStallSupplierStr,
					items: addSupplierItem.items.slice(0, orderIndex)
				}, {
					mallStallSupplierStr: "市场-档口-供应商",
					items: addSupplierItem.items.slice(orderIndex)
				}
			];
			_addSupplierItem = _addSupplierItem.map((item) => {
				return calcSupplierCount(item);
			});

			const newItems = update(bhdData.supplierGroupItems, {
				$splice: [
					[supplierIndex, 1, ..._addSupplierItem]
				]
			});

			bhdData.supplierGroupItems = newItems;

			const { totalCount, totalCost, totalPaymentCount } = countBhdTotal(bhdData.supplierGroupItems);
			setBhdData((prev) => {
				prev.supplierGroupItems = newItems;
				prev.totalCount = totalCount;
				prev.totalCost = totalCost;
				prev.totalPayment = totalPaymentCount;
				storeBhdSet.setBhdData(prev.supplierGroupItems);
				return prev;
			});
		} catch (error) {
			console.log("handleAddSupplier error:", error);
		}

	};


	const handleUnfoldAllMsg = (e: CheckboxChangeEvent) => {
		const isChecked = e.target.checked;
		setToggle(isChecked);
		const newOperateMap = cloneDeep(operateMap);
		for (const key in newOperateMap) {
			newOperateMap[key].showMsg = !unfoldMsg;
		}
		local.set('report.bhd.showMsg', !unfoldMsg ? 1 : 0);
		setOperateMap(newOperateMap);
	};

	const handleShowMessage = (platform: string, numIid: string) => {
		const key = platform + numIid;
		setOperateMap({
			[key]: {
				showMsg: !operateMap[key].showMsg
			}
		});
	};
	// 选项选择
	const handleCheckCurTableItem = (tableItemData: object, tableItemIndex: number, supplierIndex: number) => {
		setBhdData(prev => {
			prev.supplierGroupItems[supplierIndex].items[tableItemIndex].isChecked = !prev.supplierGroupItems[supplierIndex].items[tableItemIndex].isChecked;
			const checkStatus = checkSelectList(prev);
			setIsDisabledBhdGeneratePurchaseOrderBtn(checkStatus === 0);
			setBhdTableCheckBtnStatus(checkStatus);
			storeBhdSet.setBhdData(prev.supplierGroupItems);
			return prev;
		});

	};
	const borderStyle = '1px solid ' + baseSet.tableColor;
	let contentWidth = 0;
	let hasSkuLabel = false;
	colsSet?.forEach(item => {
		contentWidth += Number(item.width);
		if (!hasSkuLabel && !item.showSingleLine) {
			hasSkuLabel = true;
		}
	});
	let tableWidth = BdhTableOperateWidth + contentWidth + 1;

	const changeSelect = (type: number) => {
		// type  0 全不选 0.5 半选 1 全选
		setBhdData((prev) => {
			prev.supplierGroupItems.forEach((supplierItem) => {
				supplierItem.items.forEach(item => {
					item.isChecked = (type > 0);
				});
			});
			const checkStatus = checkSelectList(prev);
			setIsDisabledBhdGeneratePurchaseOrderBtn(checkStatus === 0);
			setBhdTableCheckBtnStatus(checkStatus);
			storeBhdSet.setBhdData(prev.supplierGroupItems);
			return prev;
		});
	};

	/**
	 *
	 * 处理半选/全选按钮状态的切换
	 */
	const handleCheckAllBhdTable = (e: any) => {
		if (bhdTableCheckBtnStatus === 1) {
			changeSelect(0);
		} else {
			changeSelect(1);
		}

	};

	const renderInput = () => (
		<label style={ { cursor: 'pointer' } } >
			<Checkbox
				indeterminate={ bhdTableCheckBtnStatus === 0.5 }
				className="input_check packageCheckAll"
				onChange={ handleCheckAllBhdTable }
				checked={ bhdTableCheckBtnStatus === 1 }
			/>
		</label>
	);
	// 供应商下全选与半选
	const handleChangeSupplier = (index: number) => {
		setBhdData((prev) => {
			prev.supplierGroupItems[index].checkStatus = prev.supplierGroupItems[index].checkStatus === 1 ? 0 : 1;
			const type = prev.supplierGroupItems[index].checkStatus;
			prev.supplierGroupItems[index].items.forEach(item => {
				item.isChecked = (type > 0);
			});
			const checkStatus = checkSelectList(prev);
			setIsDisabledBhdGeneratePurchaseOrderBtn(checkStatus === 0);
			setBhdTableCheckBtnStatus(checkStatus);
			storeBhdSet.setBhdData(prev.supplierGroupItems);
			return prev;
		});
	};

	// 删除某个商品
	const handleDeleteSku = ({ platform, numIid, supplierIndex, orderIndex, skuIndex }: { platform: string, numIid: string, supplierIndex: number, orderIndex: number, skuIndex: number }) => {
		Modal.confirm({
			centered: true,
			title: '提示',
			content: '是否删除该行',
			onOk: () => {
				try {
					let deleteSkuIdKey = "skuId";
					if ([BhdShowType.本地货品, BhdShowType.货品规格].includes(bhdStore.showType)) {
						deleteSkuIdKey = "sysSkuId";
					}
					
					const deleteSku = bhdData.supplierGroupItems[supplierIndex].items[orderIndex].skuCounts[skuIndex];
					const deleteSkuId = deleteSku[deleteSkuIdKey];
					let deleteOidList = deleteSku.oidList || []; // 防止新增SKU没有oidList
					
					// 检查是否为新增的SKU（没有订单信息）
					const isNewSku = deleteSku.isNew === true || !deleteOidList.length;
					
					// 使用原来的update方式删除SKU
					const newItems = _.cloneDeep(update(bhdData.supplierGroupItems[supplierIndex].items[orderIndex].skuCounts, {
						$splice: [
							[skuIndex, 1]
						]
					}));
					
					// 如果删除后没有SKU了，需要删除整个商品项
					if (newItems.length === 0) {
						// 删除整个商品项
						const newOrderItems = update(bhdData.supplierGroupItems[supplierIndex].items, {
							$splice: [
								[orderIndex, 1]
							]
						});
						
						bhdData.supplierGroupItems[supplierIndex].items = newOrderItems;
						bhdData.supplierGroupItems[supplierIndex] = calcSupplierCount(bhdData.supplierGroupItems[supplierIndex]);
						const { totalCount, totalCost, totalPaymentCount } = countBhdTotal(bhdData.supplierGroupItems);
						
						setBhdData((prev) => {
							prev.supplierGroupItems[supplierIndex].items = newOrderItems;
							prev.totalCount = totalCount;
							prev.totalCost = totalCost;
							prev.totalPayment = totalPaymentCount;
							storeBhdSet.setBhdData(prev.supplierGroupItems);
							return prev;
						});
						
						return;
					}
					
					// 更新SKU数组
					bhdData.supplierGroupItems[supplierIndex].items[orderIndex].skuCounts = newItems;
					
					// 处理订单信息
					const _tradeInfos = bhdData.supplierGroupItems[supplierIndex].items[orderIndex].tradeInfos;
					let newTradeInfos = [];
					let _totalCount = 0;
					
					if (isNewSku) {
						// 新增的SKU，不需要处理订单信息，保持原有的tradeInfos
						newTradeInfos = _tradeInfos;
						_totalCount = newItems.reduce((total, sku) => total + (sku.count || 0), 0);
					} else {
						// 原有的SKU，需要处理订单信息（保持原来的逻辑）
						const deleteTradeOids = _tradeInfos.filter((item) => (deleteOidList.includes(item.orderInfo?.oid)));
						
						// 如果没有一个商品中多个不同规格 合单的情况 该条件可以用 否则就还是会出现bug 但根据目前数据无法解决
						if (deleteTradeOids?.length == deleteOidList?.length) {
							_tradeInfos.forEach(item => {
								if (!deleteOidList?.includes(item.orderInfo?.oid)) {
									newTradeInfos.push(item);
									_totalCount += item.orderInfo?.num || 1;
								}
							});
						} else {
							_tradeInfos.forEach(item => {
								if (item.orderInfo?.[deleteSkuIdKey] == deleteSkuId) {
									newTradeInfos.push(item);
									_totalCount += item.orderInfo?.num || 1;
								}
							});
						}
					}
					
					// 重新计算供应商统计
					bhdData.supplierGroupItems[supplierIndex] = calcSupplierCount(bhdData.supplierGroupItems[supplierIndex]);
					const { totalCount, totalCost, totalPaymentCount } = countBhdTotal(bhdData.supplierGroupItems);
					
					// 使用原来的数据设置方式
					setBhdData((prev) => {
						prev.supplierGroupItems[supplierIndex].items[orderIndex].skuCounts = newItems;
						prev.supplierGroupItems[supplierIndex].items[orderIndex].tradeInfos = newTradeInfos;
						prev.supplierGroupItems[supplierIndex].items[orderIndex].totalCount = _totalCount;
						prev.totalCount = totalCount;
						prev.totalCost = totalCost;
						prev.totalPayment = totalPaymentCount;
						storeBhdSet.setBhdData(prev.supplierGroupItems);
						return prev;
					});
					
				} catch (error) {
					console.error('删除SKU失败:', error);
				}
			}
		});
	};

	const handleShowStallNum = (e: CheckboxChangeEvent) => {
		const isChecked = e.target.checked;
		setShowStallNum(isChecked);
		local.set(显示档口数量, isChecked);
	};

	const handleShowCost = (e: CheckboxChangeEvent) => {
		const isChecked = e.target.checked;
		setShowCost(isChecked);
		local.set(显示成本价, isChecked);
	};

	const handleShowPayment = (e: CheckboxChangeEvent) => {
		const isChecked = e.target.checked;
		setShowPayment(isChecked);
		local.set(显示实付金额, isChecked);
	};

	// 变更供应商成功后 更新备货单页面显示的供应商
	const changeSupplierSuccess = async({ supplier, supplierIndex }) => {
		try {
			const _addItems = bhdData.supplierGroupItems[supplierIndex].items;
			bhdData.supplierGroupItems.splice(supplierIndex, 1);
			const _bhdData = await findSupplierAndUpdate({ bhdData, supplier, addItems: _addItems });
			setBhdData(_bhdData);
			storeBhdSet.setBhdData(_bhdData.supplierGroupItems);
		} catch (error) {
			console.log('更新供应商失败：', error);
		}
	};

	// 修改排序确定
	const handleSortOk = (data) => {
		setBhdData(data);
		storeBhdSet.setBhdData(data.supplierGroupItems);

		setShowSortModal(false);
	};

	// 备货单做商品资料开关切换事件
	const handleGoodsDataSwitchChange = async(checked: boolean) => {
		// userStore?.userInfo?.version === 2
		// 非高级版或者白名单用户，点击展示升级弹框
		// 检查权限
		if (await lowVersionLock(PageNameControlEnum.备货单做商品资料)) {
			// 如果没有权限，显示版本控制弹窗
			setShowVersionModal({ visible: true, pageName: PageNameControlEnum.备货单做商品资料 });
			return;
		}

		// 更新状态
		setBhdGoodsDataSwitch(checked);

		// 存储子账号级别的开关状态
		local.setBySubUserId(`bhdGoodsDataSwitch`, checked);

		sendPoint(checked ? Pointer.报表_备货单_开启编辑模式 : Pointer.报表_备货单_关闭编辑模式);

		// 如果是关闭编辑状态
		if (!checked && hasAnyUpdates) {
			// 重置所有变更状态
			resetUpdatedStatus();

			// 触发重新查询
			setTimeout(() => {
				if (onRefreshData) {
					onRefreshData();
				}
			}, 500);
		}
	};

	// 拿货小标签模版设置
	const toLabelModalSetting = () => {
		PrintCenter.showTemplateMain({ printType: 'bq' });
	};

	// 打印商品备货标签设置
	const toLabelModalBhdSetting = () => {
		PrintCenter.showTemplateMain({ printType: 'bhbq' });
	};

	// 爆款标签设置
	const toHotLabelSetting = () => {
		history.push("/stall/bhd/BhdHotLabelSet");
	};

	// 修改排序关闭
	const handleSortCancel = () => {
		setShowSortModal(false);
	};

	// 修改：调整顺序按钮点击处理
	const handleSortClick = () => {
		// 检查条件是否满足
		if (!bhdStore.bhdSetJs?.baseSet?.[EnumBhdBaseConfig.市场档口供应商修改时更新商品档案]) {
			message.warning('请在表格样式设置中开启');
			return;
		}

		// 检查变更状态并拦截
		if (checkUpdateStatusAndIntercept('调整顺序')) {
			return;
		}

		sendPoint(Pointer.备货单_调整档口顺序_修改排序);
		setShowSortModal(true);
	};

	// 修改：编辑商品处理 - 记录当前被编辑的列信息
	const handleEditGoods = useCallback((orderItem: any, skuItem?: any, col?: BhdColSet) => {
		if (!bhdGoodsDataSwitch) return;

		const fieldInfo = col ? getCellFieldInfo(col, orderItem, skuItem, colsSet) : null;

		setEditGoodsDrawer({
			visible: true,
			orderItem,
			skuItem,
			fieldInfo,
			currentCol: col
		});
	}, [bhdGoodsDataSwitch, colsSet]);

	// 关闭抽屉
	const handleCloseEditDrawer = () => {
		setEditGoodsDrawer({
			visible: false,
			orderItem: null,
			skuItem: null,
			fieldInfo: null,
			currentCol: null
		});
	};

	// 修改：保存成功回调 - 设置两个状态
	const handleEditSaveSuccess = (hasSaved?: boolean) => {
		console.log('商品资料更新成功');

		// 如果有保存行为，设置两个状态
		if (hasSaved) {
			// 1. 设置全局变更状态（用于拦截操作）
			setHasAnyUpdates(true);

			// 2. 设置当前单元格的变更状态（用于背景色显示）
			const { orderItem, skuItem, currentCol } = editGoodsDrawer;

			if (orderItem && currentCol) {
				// 找到对应的供应商和订单索引
				let supplierIndex = -1;
				let orderIndex = -1;

				bhdData.supplierGroupItems?.forEach((supplier, sIndex) => {
					const oIndex = supplier.items?.findIndex(item => item.platform === orderItem.platform && item.numIid === orderItem.numIid);
					if (oIndex !== -1) {
						supplierIndex = sIndex;
						orderIndex = oIndex;
					}
				});

				if (supplierIndex !== -1 && orderIndex !== -1) {
					let cellKey;

					// 根据列的显示模式和是否有规格项来确定标记的单元格
					if (currentCol.showSingleLine || !skuItem) {
						// 单行模式或没有规格项：标记商品级别的单元格
						cellKey = generateCellKey(supplierIndex, orderIndex, currentCol.id);
					} else {
						// 多行模式且有规格项：标记规格级别的单元格
						const skuIndex = orderItem.skuCounts?.findIndex(sku => sku.skuId === skuItem.skuId);
						if (skuIndex !== -1) {
							cellKey = generateCellKey(supplierIndex, orderIndex, currentCol.id, skuIndex);
						}
					}

					// 标记这个单元格
					if (cellKey) {
						setUpdatedCells(prev => new Set([...prev, cellKey]));
					}
				}
			}
		}
	};

	// 【修改】优化：单元格更新检查函数
	const isCellUpdated = useCallback((supplierIndex: number, orderIndex: number, colId: any, skuIndex?: number) => {
		// 快速路径：如果功能未开启或没有任何更新，直接返回false
		if (!bhdGoodsDataSwitch || !hasAnyUpdates) {
			return false;
		}

		const cellKey = generateCellKey(supplierIndex, orderIndex, colId, skuIndex);
		return updatedCells.has(cellKey);
	}, [bhdGoodsDataSwitch, updatedCells, hasAnyUpdates]);

	// 【新增】优化：预定义样式对象，避免每次创建新对象
	const cellStyles = useMemo(() => ({
		updated: { backgroundColor: '#FFF7E6' },
		normal: { backgroundColor: 'transparent' }
	}), []);

	// 点击添加商品
	const handleAddGoods = async() => {
		if (await lowVersionLock(PageNameControlEnum.备货单添加商品)) {
			// 如果没有权限，显示版本控制弹窗
			setShowVersionModal({ visible: true, pageName: PageNameControlEnum.备货单添加商品 });
			return;
		}

		// 检查变更状态并拦截
		if (checkUpdateStatusAndIntercept('添加商品')) {
			return;
		}

		setAddGoodsDrawer({
			visible: true
		});
	};

	const isNewItem = useCallback((orderItem: any) => {
		// 如果整个商品是新的，返回true
		if (orderItem?.isNew === true) {
			return true;
		}
		// 如果商品下有任何新增的SKU，也返回true
		return orderItem?.skuCounts?.some(sku => sku.isNew === true) || false;
	}, []);

	// 新增：检查是否为新增的SKU
	const isAddNewSku = useCallback((orderItem: any, skuIndex?: number) => {
		if (skuIndex === undefined) return false;
		return orderItem?.skuCounts?.[skuIndex]?.isNew === true;
	}, []);

	// 新增：检查是否为完全新增的商品（所有SKU都是新的）
	const isCompletelyNewItem = useCallback((orderItem: any) => {
		return orderItem?.isNew === true && orderItem?.skuCounts?.every(sku => sku.isNew === true);
	}, []);

	// 添加商品成功
	const handleAddGoodsOk = (selectRows: any[], type: AddTypeEnum) => {
		console.log('%c [ 添加的数据是 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', type == AddTypeEnum.平台商品 ? '平台商品' : '系统货品', selectRows);
		console.time('添加商品数据到备货单');
		if (selectRows.length) {
			setHasAnyNewItems(true); // 设置有新增商品的状态
			const bhdDataTemp = cloneDeep(bhdData);
			const isSupplierGroupEnabled = storeBhdSet.bhdSetHasSaved?.baseSet?.supplierGroup; // 是否开启市场-档口-供应商分组
			
			// 处理添加的商品数据
			let addItems = [];
			
			if (type === AddTypeEnum.平台商品) {
				// 检查当前是否为按规格统计模式
				if (showType === BhdShowType.规格商品) {
					// 规格商品模式：每个规格作为一行数据
					// 在规格统计模式下，selectRows 中每个元素已经是一个规格
					selectRows.forEach((goods) => {
						const platformItemSku = goods.platformItemSkuList?.[0] || {};
						const relationSystemItemInfo = platformItemSku.relationSystemItemList?.[0] || {};
						const skuCount = goods?.num || 1;
						
						// 创建规格级别的数据项
						const newSkuItem = {
							...goods,
							...platformItemSku,
							type: AddTypeEnum.平台商品,
							rowId: `${goods.numIid}_${platformItemSku.skuId}`,
							// 平台商品字段
							numIid: goods.numIid,
							title: goods.title || '',
							titleAlias: goods.titleAlias || relationSystemItemInfo.sysItemAlias || '',
							itemPicUrl: platformItemSku.picUrl || '',
							outerId: goods.outerId || '',
							picUrl: platformItemSku.picUrl || relationSystemItemInfo.picUrl || '',
							itemAlias: relationSystemItemInfo.sysItemAlias,
							// 规格字段 - 在规格商品模式下，这些字段直接存储在行数据的根级别
							skuId: platformItemSku.skuId,
							skuName: platformItemSku.skuName || '', // 规格名称
							_skuName: filterPrintContent(platformItemSku.skuName || ''),
							skuAlias: platformItemSku.skuAlias || '', // 规格别名
							skuPic: platformItemSku.picUrl || relationSystemItemInfo.picUrl || '', // 规格图片
							skuOuterId: platformItemSku.skuOuterId || '',
							color: platformItemSku.color || '',
							size: platformItemSku.size || '',
							barCode: platformItemSku.barCode || '',
							// 供应商信息
							market: relationSystemItemInfo.market || '',
							stall: relationSystemItemInfo.stall || '',
							supplierName: relationSystemItemInfo.supplierName || '',
							supplierId: relationSystemItemInfo.supplierId || '',
							// 状态标识
							isNew: true,
							isChecked: false,
							platform: goods.platform || '',
							sellerId: goods.sellerId || '',
							sellerNick: goods.sellerNick || '',
							sellerAbbreviation: goods.sellerAbbreviation || '',
							// 数量和价格信息
							num: skuCount,
							count: skuCount,
							totalCount: skuCount,
							cost: relationSystemItemInfo.costPrice || '0.00',
							price: platformItemSku.price || relationSystemItemInfo.price || '0.00',
							tagPrice: platformItemSku.tagPrice || relationSystemItemInfo.tagPrice || '0.00',
							_cost: Number(relationSystemItemInfo.costPrice || 0) * skuCount, // 成本价
							priceCount: (relationSystemItemInfo.price || 0) * skuCount, // 销售价
							tagPriceCount: (relationSystemItemInfo.tagPrice || 0) * skuCount, // 标签价
							paymentCount: 0, // 添加的商品没有订单信息，实付金额为0
							// 库存字段
							salableItemStock: String(platformItemSku.salableItemStock || relationSystemItemInfo.salableItemStock || 0),
							salableItemDistributableStock: String(platformItemSku.salableItemDistributableStock || relationSystemItemInfo.salableItemDistributableStock || 0),
							salableItemPreemptedNum: relationSystemItemInfo.salableItemPreemptedNum || 0,
							warehouseSlotId: platformItemSku.warehouseSlotId || relationSystemItemInfo.warehouseSlotId || '',
							warehouseSlotName: platformItemSku.warehouseSlotName || relationSystemItemInfo.warehouseSlotName || '',
							stockNum: platformItemSku.stockNum || 0,
							// 系统货品关联字段
							sysPicUrl: relationSystemItemInfo.picUrl,
							sysSkuId: relationSystemItemInfo.sysSkuId,
							sysSkuName: relationSystemItemInfo.sysSkuName || '', 
							sysSkuOuterId: relationSystemItemInfo.skuOuterId || '',
							isCombination: relationSystemItemInfo.isCombination,
							sysOuterId: relationSystemItemInfo.outerId,
							sysItemAlias: relationSystemItemInfo.sysItemAlias,
							sysItemId: relationSystemItemInfo.sysItemId,
							sysSkuAlias: relationSystemItemInfo.sysSkuAlias,
							// 交易信息
							tradeInfos: [{
								platform: goods.platform || '',
								sellerId: goods.sellerId || '',
								sellerNick: goods.sellerNick || '',
								orderInfo: {
									itemId: goods.numIid,
									skuId: platformItemSku.skuId
								}
							}],
							// 规格商品模式特有：使用itemCounts而不是skuCounts
							itemCounts: [{
								itemId: goods.numIid,
								numIid: goods.numIid,
								outerId: goods.outerId || '',
								title: goods.title,
								titleAlias: goods.titleAlias || relationSystemItemInfo.sysItemAlias || '',
								skuId: platformItemSku.skuId,
								sellerId: platformItemSku.sellerId,
								sellerNick: platformItemSku.sellerNick,
								platform: platformItemSku.platform,
								itemNo: platformItemSku.itemNo,
								count: skuCount,
								cost: relationSystemItemInfo.costPrice || '0.00',
								skuName: platformItemSku.skuName || '',
								_skuName: filterPrintContent(platformItemSku.skuName || ''),
								skuPaymentCount: 0,
								color: platformItemSku.color || '',
								size: platformItemSku.size || '',
								barCode: platformItemSku.barCode || '',
								picUrl: platformItemSku.picUrl || relationSystemItemInfo.picUrl || '',
								skuPic: platformItemSku.picUrl || relationSystemItemInfo.picUrl || '',
								skuOuterId: platformItemSku.skuOuterId || '',
								skuAlias: platformItemSku.skuAlias || '',
								price: platformItemSku.price || relationSystemItemInfo.price || '0.00',
								tagPrice: platformItemSku.tagPrice || relationSystemItemInfo.tagPrice || '0.00',
								salableItemStock: String(platformItemSku.salableItemStock || relationSystemItemInfo.salableItemStock || 0),
								salableItemDistributableStock: String(platformItemSku.salableItemDistributableStock || relationSystemItemInfo.salableItemDistributableStock || 0),
								salableItemPreemptedNum: relationSystemItemInfo.salableItemPreemptedNum || 0,
								warehouseSlotId: platformItemSku.warehouseSlotId || relationSystemItemInfo.warehouseSlotId || '',
								warehouseSlotName: platformItemSku.warehouseSlotName || relationSystemItemInfo.warehouseSlotName || '',
								stockNum: platformItemSku.stockNum || 0,
								sysPicUrl: relationSystemItemInfo.picUrl,
								sysSkuId: relationSystemItemInfo.sysSkuId,
								sysSkuName: relationSystemItemInfo.sysSkuName || '', 
								sysSkuOuterId: relationSystemItemInfo.skuOuterId || '',
								isCombination: relationSystemItemInfo.isCombination,
								sysOuterId: relationSystemItemInfo.outerId,
								sysItemAlias: relationSystemItemInfo.sysItemAlias,
								sysItemId: relationSystemItemInfo.sysItemId,
								sysSkuAlias: relationSystemItemInfo.sysSkuAlias,
								relationSysItem: !!relationSystemItemInfo.sysItemId, // 是否关联本地货品
								isNew: true
							}],
							// 添加缺失的字段，确保与现有数据结构一致
							refundStockWaitHandNum: "0",
							transitItemStock: "0",
							_hasMessage: false
						};
						
						// 如果有 tradeInfos，重新计算 _hasMessage
						if (newSkuItem.tradeInfos && newSkuItem.tradeInfos.length > 0) {
							newSkuItem._hasMessage = newSkuItem.tradeInfos.some(item => {
								return item.buyerMessage || (item.sellerFlag && item.sellerFlag != '0') || item.sellerMemo;
							});
						}
						
						newSkuItem.skuCounts = newSkuItem.itemCounts;
						
						addItems.push(newSkuItem);
					});
				} else {
					// 平台商品普通模式：按商品ID分组
					const goodsMap = new Map();
				
					selectRows.forEach((goods) => {
						const skuInfo = goods?.platformItemSkuList?.[0] || {};
						const relationSystemItemInfo = skuInfo?.relationSystemItemList?.[0] || {};

						const supplierKey = `${relationSystemItemInfo.market || ''}-${relationSystemItemInfo.stall || ''}-${relationSystemItemInfo.supplierName || ''}`;
						const goodsKey = `${goods.numIid}_${supplierKey}`;
						if (!goodsMap.has(goodsKey)) {
						// 创建新的商品项
							const newItem = {
								...goods,
								type: AddTypeEnum.平台商品,
								rowId: goods.rowId,
								// 平台商品字段
								numIid: goods.numIid,
								title: goods.title || '',
								titleAlias: goods.titleAlias || relationSystemItemInfo.sysItemAlias || '',
								itemPicUrl: skuInfo.picUrl || '',
								outerId: goods.outerId || '',
								picUrl: skuInfo.picUrl || relationSystemItemInfo.picUrl || '',
								itemAlias: relationSystemItemInfo.sysItemAlias,
								// 供应商信息
								market: relationSystemItemInfo.market,
								stall: relationSystemItemInfo.stall,
								supplierName: relationSystemItemInfo.supplierName,
								supplierId: relationSystemItemInfo.supplierId,
								// 状态标识
								isNew: true,
								isChecked: false,
								platform: goods.platform || '',
								sellerId: goods.sellerId || '',
								sellerNick: goods.sellerNick || '',
								sellerAbbreviation: goods.sellerAbbreviation || '',
								// 初始化统计字段
								num: 0,
								count: 0,
								totalCount: 0,
								_cost: 0,
								priceCount: 0,
								tagPriceCount: 0,
								paymentCount: 0,
								// 初始化 skuCounts 数组
								skuCounts: [],
								// 交易信息
								tradeInfos: [{
									platform: goods.platform || '',
									sellerId: goods.sellerId || '',
									sellerNick: goods.sellerNick || '',
									orderInfo: {
										itemId: goods.numIid,
									}
								}]
							};
							goodsMap.set(goodsKey, newItem);
						}
					
						// 添加规格到 skuCounts 中
						const item = goodsMap.get(goodsKey);
						const platformItemSkuList = goods.platformItemSkuList || [];
					
						platformItemSkuList.forEach(platformItemSku => {
							const relationSystemItemList = platformItemSku.relationSystemItemList?.[0] || {};
							const skuCount = goods?.num || 1;
						
							// 添加到 skuCounts
							item.skuCounts.push({
								...platformItemSku,
								itemId: goods.numIid,
								numIid: goods.numIid,
								outerId: goods.outerId,
								title: goods.title,
								titleAlias: goods.titleAlias || relationSystemItemInfo.sysItemAlias || '',
								skuId: platformItemSku.skuId,
								sellerId: platformItemSku.sellerId,
								sellerNick: platformItemSku.sellerNick,
								platform: platformItemSku.platform,
								itemNo: platformItemSku.itemNo,
								count: skuCount,
								cost: relationSystemItemList.costPrice || '0.00',
								skuName: platformItemSku.skuName || '',
								_skuName: filterPrintContent(platformItemSku.skuName || ''),
								skuPaymentCount: 0,
								color: platformItemSku.color || '',
								size: platformItemSku.size || '',
								barCode: platformItemSku.barCode || '',
								picUrl: platformItemSku.picUrl || relationSystemItemList.picUrl || '',
								skuPic: platformItemSku.picUrl || relationSystemItemList.picUrl || '',
								skuOuterId: platformItemSku.skuOuterId || '',
								skuAlias: platformItemSku.skuAlias || '',
								price: platformItemSku.price || relationSystemItemList.price || '0.00',
								tagPrice: platformItemSku.tagPrice || relationSystemItemList.tagPrice || '0.00',
								salableItemStock: String(platformItemSku.salableItemStock || relationSystemItemList.salableItemStock || 0),
								salableItemDistributableStock: String(platformItemSku.salableItemDistributableStock || relationSystemItemList.salableItemDistributableStock || 0),
								salableItemPreemptedNum: relationSystemItemList.salableItemPreemptedNum || 0,
								warehouseSlotId: platformItemSku.warehouseSlotId || relationSystemItemList.warehouseSlotId || '',
								warehouseSlotName: platformItemSku.warehouseSlotName || relationSystemItemList.warehouseSlotName || '',
								stockNum: platformItemSku.stockNum || 0,
								sysPicUrl: relationSystemItemInfo.picUrl,
								sysSkuId: relationSystemItemList.sysSkuId,
								sysSkuName: relationSystemItemList.sysSkuName || '', 
								sysSkuOuterId: relationSystemItemList.skuOuterId || '',
								isCombination: relationSystemItemInfo.isCombination,
								sysOuterId: relationSystemItemInfo.outerId,
								sysItemAlias: relationSystemItemInfo.sysItemAlias,
								sysItemId: relationSystemItemInfo.sysItemId,
								sysSkuAlias: relationSystemItemInfo.sysSkuAlias,
								market: relationSystemItemInfo.market,
								stall: relationSystemItemInfo.stall,
								supplierName: relationSystemItemInfo.supplierName,
								supplierId: relationSystemItemInfo.supplierId,
								isNew: true,
								relationSysItem: !!relationSystemItemInfo.sysItemId, // 是否关联本地货品
							});
						
							// 累计商品级别的统计数据
							item.num += skuCount;
							item.count += skuCount;
							item.totalCount += skuCount;
							item._cost += Number(relationSystemItemList.costPrice || 0) * skuCount;
							item.priceCount += (relationSystemItemList.price || 0) * skuCount;
							item.tagPriceCount += (relationSystemItemList.tagPrice || 0) * skuCount;
							item.paymentCount += 0;
						});
					});
				
					addItems = Array.from(goodsMap.values());
				}
				
			} else if (type === AddTypeEnum.系统货品) {
				// 检查当前是否为按规格统计模式
				if (showType === BhdShowType.货品规格) {
					// 货品规格模式：每个规格作为一行数据
					selectRows.forEach((goods) => {
						const sysSkuList = goods.sysSkuList || [];
						
						sysSkuList.forEach(sku => {
							const skuCount = goods?.num || 1;
							
							// 创建规格级别的数据项
							const newSkuItem = {
								...goods,
								...sku,
								type: AddTypeEnum.系统货品,
								rowId: `${goods.sysItemId}_${sku.sysSkuId}`,
								// 系统货品字段
								sysItemId: goods.sysItemId,
								title: goods.sysItemName || goods.title || '',
								titleAlias: goods.sysItemAlias || '',
								sysItemAlias: goods.sysItemAlias || '',
								sysOuterId: goods.outerId || '',
								itemPicUrl: goods.sysItemPicUrl || sku.picUrl || '',
								picUrl: sku.picUrl || '',
								// 规格字段 - 在货品规格模式下，这些字段直接存储在行数据的根级别
								sysSkuId: sku.sysSkuId,
								skuName: sku.sysSkuName || '',
								_skuName: filterPrintContent(sku.sysSkuName || ''),
								skuAlias: sku.sysSkuAlias || '',
								sysSkuPicUrl: sku.sysSkuPicUrl || sku.picUrl || '',
								skuOuterId: sku.skuOuterId || '',
								sysSkuName: sku.sysSkuName || '',
								sysSkuOuterId: sku.skuOuterId || '',
								sysSkuAlias: sku.sysSkuAlias || '',
								color: sku?.sysColor || '',
								size: sku?.sysSize || '',
								// 供应商信息
								market: sku.market || '',
								stall: sku.stall || '',
								supplierName: sku.supplierName || '',
								supplierId: sku.supplierId || '',
								// 状态标识
								isNew: true,
								isChecked: false,
								// 数量和价格信息
								num: skuCount,
								count: skuCount,
								totalCount: skuCount,
								cost: sku.costPrice || '0.00',
								price: sku.price || '0.00',
								tagPrice: sku.tagPrice || '0.00',
								_cost: Number(sku.costPrice || 0) * skuCount,
								priceCount: (sku.price || 0) * skuCount,
								tagPriceCount: (sku.tagPrice || 0) * skuCount,
								paymentCount: 0,
								// 库存字段
								salableItemStock: String(sku.salableItemStock || 0),
								salableItemDistributableStock: String(sku.salableItemDistributableStock || 0),
								salableItemPreemptedNum: sku.salableItemPreemptedNum || 0,
								warehouseSlotId: sku.warehouseSlotId || '',
								warehouseSlotName: sku.warehouseSlotName || '',
								stockNum: sku.stockNum || 0,
								// 交易信息
								tradeInfos: [{
									orderInfo: {
										sysItemId: goods.sysItemId,
										sysSkuId: sku.sysSkuId
									}
								}],
								// 货品规格模式特有：使用skuCounts，但每行只有一个规格
								skuCounts: [{
									sysSkuId: sku.sysSkuId,
									count: skuCount,
									cost: sku.costPrice || '0.00',
									skuName: sku.sysSkuName || '',
									_skuName: filterPrintContent(sku.sysSkuName || ''),
									skuPaymentCount: 0,
									color: sku?.sysColor || '',
									size: sku?.sysSize || '',
									picUrl: sku.sysSkuPicUrl || sku.picUrl || '',
									sysSkuPicUrl: sku.sysSkuPicUrl || sku.picUrl || '',
									skuOuterId: sku.skuOuterId || '',
									skuAlias: sku.sysSkuAlias || '',
									price: sku.price || '0.00',
									tagPrice: sku.tagPrice || '0.00',
									salableItemStock: String(sku.salableItemStock || 0),
									salableItemDistributableStock: String(sku.salableItemDistributableStock || 0),
									salableItemPreemptedNum: sku.salableItemPreemptedNum || 0,
									warehouseSlotName: sku.warehouseSlotName || '',
									sysSkuName: sku.sysSkuName || '',
									sysSkuOuterId: sku.skuOuterId || '',
									market: sku.market || '',
									stall: sku.stall || '',
									supplierName: sku.supplierName || '',
									supplierId: sku.supplierId || '',
									isNew: true,
									relationSysItem: true, // 是否关联本地货品
								}],
								// 添加缺失字段
								refundStockWaitHandNum: "0",
								transitItemStock: "0",
								_hasMessage: false
							};
							
							addItems.push(newSkuItem);
						});
					});
				} else {
					// 系统货品普通模式：按货品ID分组
					const goodsMap = new Map();
				
					selectRows.forEach((goods) => {
						const skuInfo = goods?.sysSkuList?.[0] || {};
						
						const supplierKey = `${skuInfo.market || ''}-${skuInfo.stall || ''}-${skuInfo.supplierName || ''}`;
						const goodsKey = `${goods.sysItemId}_${supplierKey}`;
						if (!goodsMap.has(goodsKey)) {
						// 创建新的商品项
							const newItem = {
								...goods,
								type: AddTypeEnum.系统货品,
								rowId: goods.rowId,
								sysItemId: goods.sysItemId,
								title: goods.sysItemName || goods.title || '',
								titleAlias: goods.sysItemAlias || '',
								sysItemAlias: goods.sysItemAlias || '',
								sysOuterId: goods.outerId || '',
								itemPicUrl: goods.sysItemPicUrl || skuInfo.picUrl || '',
								picUrl: skuInfo.picUrl || '',
								market: skuInfo.market || '',
								stall: skuInfo.stall || '',
								supplierName: skuInfo.supplierName || '',
								supplierId: skuInfo.supplierId || '',
								isNew: true,
								isChecked: false,
								num: 0,
								count: 0,
								totalCount: 0,
								_cost: 0,
								priceCount: 0,
								tagPriceCount: 0,
								paymentCount: 0,
								skuCounts: [],
								tradeInfos: [{
									orderInfo: {
										sysItemId: goods.sysItemId,
									}
								}]
							};
							goodsMap.set(goodsKey, newItem);
						}
					
						// 添加规格到 skuCounts 中
						const item = goodsMap.get(goodsKey);
						const sysSkuList = goods.sysSkuList || [];
					
						sysSkuList.forEach(sku => {
							const skuCount = goods?.num || 1;
						
							item.skuCounts.push({
								...sku,
								"cost": sku.costPrice || '0.00',
								"count": skuCount,
								"price": sku.price || '0.00',
								"refundStockWaitHandNum": sku.refundStockWaitHandNum || "0",
								"salableItemDistributableStock": String(sku.salableItemDistributableStock || 0),
								"salableItemPreemptedNum": sku.salableItemPreemptedNum || 0,
								"salableItemStock": String(sku.salableItemStock || 0),
								"skuAlias": sku.skuAlias || '',
								"skuId": sku.skuId,
								"skuPaymentCount": 0,
								"sysItemAlias": sku.sysItemAlias,
								"sysItemId": sku.sysItemId,
								"sysSkuId": sku.sysSkuId,
								"sysSkuName": sku.sysSkuName || '',
								"sysSkuOuterId": sku.skuOuterId || '',
								"tagPrice": sku.tagPrice || '0.00',
								"transitItemStock": sku.transitItemStock || '0',
								"warehouseSlotId": sku.warehouseSlotId || '',
								"warehouseSlotName": sku.warehouseSlotName || '',
								"_skuName": filterPrintContent(sku.sysSkuName || ''),
								skuName: sku.sysSkuName || '',
								sysColor: sku.sysColor || '',
								sysSize: sku.sysSize || '',
								color: sku?.sysColor || '',
								size: sku?.sysSize || '',
								picUrl: sku.sysSkuPicUrl || sku.picUrl || '',
								sysSkuPicUrl: sku.sysSkuPicUrl || sku.picUrl || '',
								skuOuterId: sku.skuOuterId || '',
								stockTotal: sku.stockTotal || 0,
								market: sku.market || '',
								stall: sku.stall || '',
								supplierName: sku.supplierName || '',
								supplierId: sku.supplierId || '',
								relationSysItem: true, // 是否关联本地货品
								isNew: true,
							});
						
							// 累计商品级别的统计数据
							item.num += skuCount;
							item.count += skuCount;
							item.totalCount += skuCount;
							item._cost += Number(sku.costPrice || 0) * skuCount;
							item.priceCount += (sku.price || 0) * skuCount;
							item.tagPriceCount += (sku.tagPrice || 0) * skuCount;
							item.paymentCount += 0;
						});
					});
				
					addItems = Array.from(goodsMap.values());
				}
			}
			
			// 根据是否开启分组来处理添加逻辑
			if (!isSupplierGroupEnabled) {
				// 1. 未开启分组：直接合并到现有列表
				const firstGroup = bhdDataTemp.supplierGroupItems?.[0];
				if (firstGroup) {
					const existingItems = firstGroup.items || [];
					
					addItems.forEach(newItem => {
						// 在按规格统计模式下，需要特殊处理查找逻辑
						let existingItemIndex = -1;
						
						if (showType === BhdShowType.规格商品) {
							// 规格商品模式：按 numIid + skuId 查找
							existingItemIndex = existingItems.findIndex(item => item.numIid === newItem.numIid && item.skuId === newItem.skuId);
						} else if (showType === BhdShowType.货品规格) {
							// 货品规格模式：按 sysItemId + sysSkuId 查找
							existingItemIndex = existingItems.findIndex(item => item.sysItemId === newItem.sysItemId && item.sysSkuId === newItem.sysSkuId);
						} else {
							// 普通模式：按商品ID查找
							if (type === AddTypeEnum.平台商品) {
								existingItemIndex = existingItems.findIndex(item => item.numIid === newItem.numIid);
							} else {
								existingItemIndex = existingItems.findIndex(item => item.sysItemId === newItem.sysItemId);
							}
						}
						
						if (existingItemIndex > -1 && (showType === BhdShowType.规格商品 || showType === BhdShowType.货品规格)) {
							// 按规格统计模式：直接合并数量（因为每行就是一个规格）
							const existingItem = existingItems[existingItemIndex];
							existingItem.count += newItem.count;
							existingItem.num += newItem.num;
							existingItem.totalCount += newItem.totalCount;
							existingItem._cost += newItem._cost;
							existingItem.priceCount += newItem.priceCount;
							existingItem.tagPriceCount += newItem.tagPriceCount;
							existingItem.paymentCount += newItem.paymentCount;
							
							// 标记为新增
							existingItem.isNew = true;
							
							// 更新对应的itemCounts或skuCounts
							if (showType === BhdShowType.规格商品 && existingItem.itemCounts?.[0]) {
								existingItem.itemCounts[0].count += newItem.itemCounts?.[0]?.count || 0;
								// existingItem.itemCounts[0].skuPaymentCount = existingItem.itemCounts[0].count * (existingItem.itemCounts[0].cost || 0);
								// 同时标记itemCounts中的数据为新增
								existingItem.itemCounts[0].isNew = true;
							} else if (showType === BhdShowType.货品规格 && existingItem.skuCounts?.[0]) {
								existingItem.skuCounts[0].count += newItem.skuCounts?.[0]?.count || 0;
								// existingItem.skuCounts[0].skuPaymentCount = existingItem.skuCounts[0].count * (existingItem.skuCounts[0].cost || 0);
								// 同时标记skuCounts中的数据为新增
								existingItem.skuCounts[0].isNew = true;
							}
						} else if (existingItemIndex > -1) {
							// 普通模式：合并 skuCounts
							const existingItem = existingItems[existingItemIndex];
							
							// 合并 skuCounts 数组
							newItem?.skuCounts?.forEach(newSkuCount => {
								const existingSkuIndex = existingItem?.skuCounts?.findIndex(existingSku => {
									if (type === AddTypeEnum.平台商品) {
										return existingSku.skuId === newSkuCount.skuId;
									} else {
										return existingSku.sysSkuId === newSkuCount.sysSkuId;
									}
								});
								
								if (existingSkuIndex > -1) {
									// 相同规格，合并数量
									const existingSku = existingItem.skuCounts[existingSkuIndex];
									existingSku.count += newSkuCount.count;
									// existingSku.skuPaymentCount = existingSku.count * (existingSku.cost || 0);
									// 标记该规格为新增
									existingSku.isNew = true;
								} else {
									// 新规格，直接添加（保持 isNew: true）
									existingItem.skuCounts.push(newSkuCount);
								}
							});
							
							// 重新计算商品级别统计
							existingItem.num += newItem.num;
							existingItem.count += newItem.count;
							existingItem.totalCount += newItem.totalCount;
							existingItem._cost += newItem._cost;
							existingItem.priceCount += newItem.priceCount;
							existingItem.tagPriceCount += newItem.tagPriceCount;
							existingItem.paymentCount += newItem.paymentCount;
						} else {
							// 不存在，添加到列表最后
							existingItems.push(newItem);
						}
					});
					
					// 使用 calcSupplierCount 重新计算第一个分组的总计
					bhdDataTemp.supplierGroupItems[0] = calcSupplierCount(firstGroup);
				}
			} else {
				// 2. 开启分组：按市场-档口-供应商分组处理
				addItems.forEach(newItem => {
					// 判断是否有市场档口供应商信息
					const hasSupplierInfo = newItem.market || newItem.stall || newItem.supplierName;
					
					let supplierGroupIndex = -1;
					
					if (!hasSupplierInfo) {
						// 没有市场档口供应商信息，查找无供应商信息的分组
						supplierGroupIndex = bhdDataTemp.supplierGroupItems?.findIndex(group => !group.supplierName && !group.market && !group.stall && group.mallStallSupplierStr !== "未绑定货品");
					} else {
						// 有市场档口供应商信息，按正常逻辑查找
						supplierGroupIndex = bhdDataTemp.supplierGroupItems?.findIndex(group => group.market === (newItem.market || '') 
							&& group.stall === (newItem.stall || '') 
							&& group.supplierName === (newItem.supplierName || ''));
					}
					
					if (supplierGroupIndex > -1) {
						// 存在对应分组，在分组内查找相同商品
						const supplierGroup = bhdDataTemp.supplierGroupItems[supplierGroupIndex];
						const groupItems = supplierGroup.items;
						let existingItemIndex = -1;
						
						// 根据统计模式选择查找逻辑
						if (showType === BhdShowType.规格商品) {
							// 规格商品模式：按 numIid + skuId 查找
							existingItemIndex = groupItems.findIndex(item => item.numIid === newItem.numIid && item.skuId === newItem.skuId);
						} else if (showType === BhdShowType.货品规格) {
							// 货品规格模式：按 sysItemId + sysSkuId 查找
							existingItemIndex = groupItems.findIndex(item => item.sysItemId === newItem.sysItemId && item.sysSkuId === newItem.sysSkuId);
						} else {
							// 普通模式：按商品ID查找
							if (type === AddTypeEnum.平台商品) {
								existingItemIndex = groupItems.findIndex(item => item.numIid === newItem.numIid);
							} else {
								existingItemIndex = groupItems.findIndex(item => item.sysItemId === newItem.sysItemId);
							}
						}
						
						if (existingItemIndex > -1 && (showType === BhdShowType.规格商品 || showType === BhdShowType.货品规格)) {
							// 按规格统计模式：直接合并数量
							const existingItem = groupItems[existingItemIndex];
							existingItem.count += newItem.count;
							existingItem.num += newItem.num;
							existingItem.totalCount += newItem.totalCount;
							existingItem._cost += newItem._cost;
							existingItem.priceCount += newItem.priceCount;
							existingItem.tagPriceCount += newItem.tagPriceCount;
							existingItem.paymentCount += newItem.paymentCount;
							
							// 标记为新增
							existingItem.isNew = true;
							
							// 更新对应的itemCounts或skuCounts
							if (showType === BhdShowType.规格商品 && existingItem.itemCounts?.[0]) {
								existingItem.itemCounts[0].count += newItem.itemCounts?.[0]?.count || 0;
								// existingItem.itemCounts[0].skuPaymentCount = existingItem.itemCounts[0].count * (existingItem.itemCounts[0].cost || 0);
								// 同时标记itemCounts中的数据为新增
								existingItem.itemCounts[0].isNew = true;
							} else if (showType === BhdShowType.货品规格 && existingItem.skuCounts?.[0]) {
								existingItem.skuCounts[0].count += newItem.skuCounts?.[0]?.count || 0;
								// existingItem.skuCounts[0].skuPaymentCount = existingItem.skuCounts[0].count * (existingItem.skuCounts[0].cost || 0);
								// 同时标记skuCounts中的数据为新增
								existingItem.skuCounts[0].isNew = true;
							}
						} else if (existingItemIndex > -1) {
							// 普通模式：合并 skuCounts
							const existingItem = groupItems[existingItemIndex];
							
							// 合并 skuCounts 数组
							newItem?.skuCounts?.forEach(newSkuCount => {
								const existingSkuIndex = existingItem?.skuCounts?.findIndex(existingSku => {
									if (type === AddTypeEnum.平台商品) {
										return existingSku.skuId === newSkuCount.skuId;
									} else {
										return existingSku.sysSkuId === newSkuCount.sysSkuId;
									}
								});
								
								if (existingSkuIndex > -1) {
									// 相同规格，合并数量
									const existingSku = existingItem.skuCounts[existingSkuIndex];
									existingSku.count += newSkuCount.count;
									// existingSku.skuPaymentCount = existingSku.count * (existingSku.cost || 0); // 添加的商品没有订单信息，实付金额为0
									// 标记该规格为新增
									existingSku.isNew = true;
								} else {
									// 新规格，直接添加（保持 isNew: true）
									existingItem.skuCounts.push(newSkuCount);
								}
							});
							
							// 重新计算商品级别统计
							existingItem.num += newItem.num;
							existingItem.count += newItem.count;
							existingItem.totalCount += newItem.totalCount;
							existingItem._cost += newItem._cost;
							existingItem.priceCount += newItem.priceCount;
							existingItem.tagPriceCount += newItem.tagPriceCount;
							existingItem.paymentCount += newItem.paymentCount;
						} else {
							// 不存在，添加到当前分组最后
							groupItems.push(newItem);
						}
						
						// 使用 calcSupplierCount 重新计算该分组的总计
						bhdDataTemp.supplierGroupItems[supplierGroupIndex] = calcSupplierCount(supplierGroup);
					} else {
						// 不存在对应分组，创建新的分组
						let newSupplierGroup;
						
						if (!hasSupplierInfo) {
							// 创建无市场档口供应商的分组
							newSupplierGroup = {
								market: "",
								stall: "",
								supplierName: "",
								mallStallSupplierStr: "",
								items: [newItem],
								checkStatus: 0
							};
						} else {
							// 创建有市场档口供应商信息的分组
							const supplierKey = `${newItem.market || ''}-${newItem.stall || ''}-${newItem.supplierName || ''}`;
							newSupplierGroup = {
								market: newItem.market || '',
								stall: newItem.stall || '',
								supplierName: newItem.supplierName || '',
								mallStallSupplierStr: supplierKey,
								items: [newItem],
								checkStatus: 0
							};
							console.log('%c [ supplierKey ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', supplierKey);
						}
						
						// 使用 calcSupplierCount 计算新分组的总计
						const calculatedGroup = calcSupplierCount(newSupplierGroup);
						bhdDataTemp.supplierGroupItems.push(calculatedGroup);
					}
				});
			}
			
			// 使用 updateBhdDataIndex 更新序号
			const updatedBhdData = updateBhdDataIndex(bhdDataTemp);
			
			// 在按规格统计模式下，需要同步更新 supplierGroupSkus
			if (showType === BhdShowType.规格商品 || showType === BhdShowType.货品规格) {
				// 从 supplierGroupItems 重新生成 supplierGroupSkus
				updatedBhdData.supplierGroupSkus = updatedBhdData.supplierGroupItems.map(group => ({
					...group,
					skus: group.items || [],
					items: [] // supplierGroupSkus 中 items 通常为空
				}));
			}
			
			// 使用 countBhdTotal 重新计算总计
			const { totalCount, totalCost, totalPaymentCount } = countBhdTotal(updatedBhdData.supplierGroupItems);
			updatedBhdData.totalCount = totalCount;
			updatedBhdData.totalCost = totalCost;
			updatedBhdData.totalPayment = totalPaymentCount;
			
			// 更新数据
			setBhdData(updatedBhdData);
			storeBhdSet.setBhdData(updatedBhdData.supplierGroupItems);
			
			console.timeEnd('添加商品数据到备货单');
			message.success('添加商品成功');
			console.log('%c [ 添加完成后的数据 ]: ', 'color: #0e93e0; background: #aaefe5; font-size: 13px;', updatedBhdData);
		}
	};

	useEffect(() => {
		// 初始化备货单做商品资料开关状态
		const initializeGoodsDataSwitch = async() => {
			if (isShowZeroStockVersion) {
				// 检查权限
				const hasNoPermission = await lowVersionLock(PageNameControlEnum.备货单做商品资料);

				if (hasNoPermission) {
					// 如果没有权限，清空缓存并设置为false
					local.removeBySubUserId(`bhdGoodsDataSwitch`);
					setBhdGoodsDataSwitch(false);
				} else {
					// 有权限时，使用缓存的状态
					const savedStatus = local.getBySubUserId(`bhdGoodsDataSwitch`) || false;
					setBhdGoodsDataSwitch(savedStatus);
				}
			} else {
				setBhdGoodsDataSwitch(false);
			}
		};

		initializeGoodsDataSwitch();
	}, [isShowZeroStockVersion]);

	// 向父组件传递检查函数
	useEffect(() => {
		if (onCheckUpdateStatus) {
			onCheckUpdateStatus(checkUpdateStatusAndIntercept);
		}
	}, [onCheckUpdateStatus, hasAnyUpdates]); // 依赖hasAnyUpdates，确保状态变化时父组件能获取到最新的检查函数

	// 判断是否显示背景色：新增商品 > 编辑过的商品
	const shouldShowNewItemBackground = useCallback((col: any, orderItem: any, supplierIndex: number, orderIndex: number, skuIndex?: number) => {
		// 快速路径1：没有任何新增商品
		if (!hasAnyNewItems) {
			// 只需要检查编辑状态
			return hasAnyUpdates && isCellUpdated(supplierIndex, orderIndex, col.id, col.showSingleLine ? undefined : skuIndex);
		}
		
		// 快速路径2：有新增商品但没有编辑操作
		if (!hasAnyUpdates) {
			// 只需要检查新增状态
			const hasSkuNameOrAlias = col.dataArray?.some((data: string) => data === BhdLabelName.规格名称 
				|| data === BhdLabelName.规格别名
				|| data === BhdLabelName.货品规格名称);
			
			const isNewItem = isCompletelyNewItem(orderItem) || isAddNewSku(orderItem, col.showSingleLine ? undefined : skuIndex);
			
			return isNewItem && hasSkuNameOrAlias;
		}
		
		// 完整检查：既有新增又有编辑
		const hasSkuNameOrAlias = col.dataArray?.some((data: string) => data === BhdLabelName.规格名称 
			|| data === BhdLabelName.规格别名
			|| data === BhdLabelName.货品规格名称);
		
		const isNewItem = isCompletelyNewItem(orderItem) || isAddNewSku(orderItem, col.showSingleLine ? undefined : skuIndex);
		
		if (isNewItem && !hasSkuNameOrAlias) {
			return false;
		}
		
		return isNewItem || isCellUpdated(supplierIndex, orderIndex, col.id, col.showSingleLine ? undefined : skuIndex);
	}, [hasAnyNewItems, hasAnyUpdates, isCompletelyNewItem, isAddNewSku, isCellUpdated]);

	/** 表格内容需要打印，css写的是内联样式，便于将样式传递给打印控件*/
	return (
		<>
			<div className={ s.bhdSearchContent }>
				<div className="r-flex r-jc-sb r-mb-10">
					<span>
						当前查询：共<i className="k-c-cred">{bhdData.tradeTotal || 0}</i>订单
					</span>
				</div>
				<div className="r-mb-6">
					<Space size={ [10, 10] } className="r-fw-w">
						{
							getTotalJsx(bhdData)
						}
					</Space>
				</div>
				<div className="r-c-error">
					{
						!userStore?.isShowZeroStockVersion && [BhdShowType.平台商品, BhdShowType.规格商品].includes(showType)
							? `结果仅统计平台商品（不含系统货品，如需统计请在表格样式设置中切换为货品视角后查询）；${bhdData.filterItemLessTradeNum ? `已过滤未添加商品手工订单：${bhdData.filterItemLessTradeNum || 0}` : ""}`
							: bhdData.filterItemLessTradeNum ? `已过滤未添加商品手工订单：${bhdData.filterItemLessTradeNum || 0}` : ""
					}
				</div>
			</div>


			{/* 新的功能按钮区域 */}
			<div className={ cs(
				s['bhd-topButton-warp'],
				bhdStore.bhdSetJs?.baseSet?.fixedTableHeader ? s['bhd-topButton-fixed'] : ""
			) }
			>
				<div className="r-flex r-flex-shrink r-ai-c r-gap-8">
					{/* 调整顺序按钮 */}
					<Button type="default" onClick={ handleSortClick } className={ !bhdStore.bhdSetJs?.baseSet?.[EnumBhdBaseConfig.市场档口供应商修改时更新商品档案] ? s['sort-disabled'] : '' }>
						调整顺序
					</Button>

					{
						!isStallBhd && (
							<Button type="default" onClick={ handleAddGoods } data-point={ Pointer.报表_备货单_添加商品_商品查询_点击 }>
								添加商品
							</Button>
						)
					}
				</div>

				<div className="r-flex r-ai-c r-jc-fe r-flex-1" style={ { gap: 8 } }>
					{/* 备货单做商品资料按钮 - 零库存版本且高级版才显示 */}
					{
						userStore?.isShowZeroStockVersion ? (
							<div
								className={ cs(s['ant-btn-div']) }
								onClick={ () => handleGoodsDataSwitchChange(!bhdGoodsDataSwitch) }
							>
								<Popover
									content={ (
										<div style={ { width: 458, lineHeight: '22px', color: 'rgba(0, 0, 0, 0.65)' } }>
											<div className={ s.bhdGoodsDataSwitchInfo }>
												<span>边备货边做资料，备货的同时完成商品资料维护、更新。</span>
												<span style={ { color: '#FD8204' } }>（高配版功能）</span>
											</div>
											<div className="r-mt-8">
												<span>维护资料：</span>
												<span>商品产生订单时再去维护资料，节约维护时间、人力成本；</span>
											</div>
											<div>
												<span>更新资科：</span>
												<span>缺货等情況需要更新商品信息时，可快速进行编辑后进行快递单、标签重打。</span>
											</div>
										</div>
									) }
									placement="top"
									trigger="hover"
								>
									<span>备货单做商品资料: </span>
								</Popover>

								<Popover
									content={ !bhdGoodsDataSwitch ? (
										<div>
											切换到编辑模式
										</div>
									) : (
										<div>切换到商品资料查看模式</div>
									) }
									placement="top"
									trigger="hover"
								>
									<Switch
										checked={ bhdGoodsDataSwitch }
										// onChange={ handleGoodsDataSwitchChange }
										checkedChildren="编辑"
										unCheckedChildren="查看"
										className="r-ml-4"
									/>
								</Popover>
								<span className={ s.newIcon }>new</span>
							</div>
						) : null
					}

					{/* 显示内容设置下拉菜单 */}
					<Dropdown
						overlay={ (
							<div
								className="r-pd-12 r-bg-white"
								style={ { boxShadow: '0 2px 8px rgba(0,0,0,0.15)', borderRadius: 6 } }
								onClick={ (e) => e.stopPropagation() }
							>
								<div className="r-mb-8">
									<Checkbox onChange={ handleUnfoldAllMsg } checked={ unfoldMsg }>
										展开留言备注
									</Checkbox>
								</div>
								<div className="r-mb-8">
									<Checkbox onChange={ handleShowStallNum } checked={ showStallNum }>
										显示档口数量
									</Checkbox>
								</div>
								<div className="r-mb-8">
									<Checkbox onChange={ handleShowCost } checked={ showCost }>
										显示成本价
									</Checkbox>
								</div>
								<div>
									<Checkbox onChange={ handleShowPayment } checked={ showPayment }>
										显示实付金额
									</Checkbox>
								</div>
							</div>
						) }
						trigger={ ['hover'] }
						placement="bottomRight"
						visible={ showContentDropdown }
						onVisibleChange={ (visible) => setShowContentDropdown(visible) }
					>
						<Button
							type="default"
						// icon={ <SettingOutlined /> }
						// onClick={ e => e.preventDefault() }
						>
							显示内容设置 <DownOutlined />
						</Button>
					</Dropdown>

					{/* 打印设置下拉菜单 */}
					<Dropdown
						overlay={ (
							<div
								className="r-pd-12 r-bg-white"
								style={ { boxShadow: '0 2px 8px rgba(0,0,0,0.15)', borderRadius: 6 } }
								onClick={ (e) => e.stopPropagation() }
							>
								{/* <div className="r-mb-8">打印时：</div> */}
								<Checkbox.Group value={ printSet } onChange={ onPrintSetChange }>
									<div className="r-flex r-fd-c">
										{showType === BhdShowType.平台商品 ? (
											<div className="r-mb-8">
												<Checkbox value="printIsHideImg">
													隐藏宝贝图片
												</Checkbox>
											</div>
										) : ''}
										<div className="r-mb-8">
											<Checkbox value="printIsHideMemo">
												隐藏留言备注
											</Checkbox>
										</div>
										<div className="r-mb-8">
											<Checkbox value="hasView">
												显示预览
											</Checkbox>
										</div>
										<div>
											<Checkbox value="printHasTime">
												显示打印时间
											</Checkbox>
										</div>
									</div>
								</Checkbox.Group>
							</div>
						) }
						trigger={ ['hover'] }
						placement="bottomRight"
						visible={ showPrintDropdown }
						onVisibleChange={ (visible) => setShowPrintDropdown(visible) }
					>
						<Button type="default">
							打印设置 <DownOutlined />
						</Button>
					</Dropdown>

					{/* 过滤词设置 */}
					{[BhdShowType.平台商品, BhdShowType.规格商品].includes(showType) ? (
						<Button type="default" onClick={ setRight }>过滤词设置</Button>
					) : ""}

					{/* 模板设置下拉菜单 */}
					{(
						isStallBhd || ([StockVersionEnum.库存版未开启扣库存, StockVersionEnum.库存版开启扣库存].includes(isStock) && !isStallBhd)
					) && (
						<Dropdown
							overlay={ (
								<Menu>
									{[StockVersionEnum.库存版未开启扣库存, StockVersionEnum.库存版开启扣库存, StockVersionEnum.无库存版].includes(isStock) && isStallBhd && (
										<Menu.Item key="labelTemplate" icon={ <SettingOutlined /> } onClick={ toLabelModalSetting }>
											拿货小标签模版设置
										</Menu.Item>
									)}
									{isStallBhd && (
										<Menu.Item key="hotLabel" icon={ <SettingOutlined /> } onClick={ toHotLabelSetting }>
											爆款标签设置
										</Menu.Item>
									)}
									{[StockVersionEnum.库存版未开启扣库存, StockVersionEnum.库存版开启扣库存].includes(isStock) && !isStallBhd && (
										<Menu.Item key="labelTemplate" icon={ <SettingOutlined /> } onClick={ toLabelModalBhdSetting }>
											商品备货标签
										</Menu.Item>
									)}
								</Menu>
							) }
							trigger={ ['hover'] }
							placement="bottomRight"
						>
							<Button type="default">
								模板设置 <DownOutlined />
							</Button>
						</Dropdown>
					)}
				</div>
			</div>

			{/* 过滤词设置模态框 */}
			{visibleFilterModal ? (
				<FilterKeyWordModal
					visible={ visibleFilterModal }
					onCloseModal={ setLeft }
				/>
			) : ''}

			{/* 调整顺序模态框 */}
			{showSortModal && (
				<BhdTdMallStallSupplierSortModal
					visible={ showSortModal }
					onOk={ handleSortOk }
					onCancel={ handleSortCancel }
					bhdData={ bhdData }
				/>
			)}

			{/* 高级版升级弹框 */}
			{showVersionModal.visible && (
				<LowVersionControlModal
					closable
					onCancel={ () => setShowVersionModal({ visible: false, pageName: '' }) }
					pageName={ showVersionModal.pageName }
				/>
			)}

			<div className={ s.bhdPrintContentWarp }>
				<div id="bhdPrintContent" className={ s.bhdContent }>
					<div className="bhd-print-wrapper" >
						<table
							// contentEditable
							suppressContentEditableWarning
							cellSpacing="0"
							cellPadding="0"
							className="bhd-table"
							style={ {
								margin: '0 auto',
								fontSize: '12px',
								textAlign: 'center',
								fontFamily: baseSet.fontFamily,
								lineHeight: '30px',
								borderLeft: borderStyle,
								outline: 'none',
								width: tableWidth + 'px',
								position: "relative"
							} }
						>
							<thead className={ cs(bhdStore.bhdSetJs?.baseSet?.fixedTableHeader ? s["bhd-table-fixed"] : "") }>
								<tr >
									<th
										key="checkBox"
										style={ {
											borderTop: borderStyle,
											borderRight: borderStyle,
											borderBottom: borderStyle,
											// borderLeft: bhdStore.bhdSetJs?.baseSet?.fixedTableHeader ? borderStyle : 'none',
										} }
										onClick={ () => { document.getSelection().empty(); return false; } }
										className="bhd-not-print"
									>{renderInput()}
									</th>
									{colsSet?.map(colSet => (
										<th
											key={ colSet.id }
											style={ {
												width: colSet.width + 'px',
												textAlign: 'center',
												borderRight: borderStyle,
												borderTop: borderStyle,
												borderBottom: borderStyle
											} }
										>
											{colSet.colName}
											{
												colSet.colName == '建议采购数量' && !userStore.isShowZeroStockVersion
													? (
														<Popover className="r-ml-5" content="如未建立关联商品与货品关系，默认建议采购数量与总数量一致。" >
															<QuestionCircleOutlined />
														</Popover>
													) : ''
											}
										</th>
									))}
									<th key="operation" style={ { width: BdhTableOperateWidth + 'px' } } > </th>
								</tr>
							</thead>
							<tbody style={ { marginTop: 30 } }>
								{
									bhdData?.supplierGroupItems?.map((supplierItem: any, supplierIndex: number) => (
										<React.Fragment key={ `Fragment${supplierItem.mallStallSupplierStr}${supplierIndex}` }>
											{
												supplierItem.mallStallSupplierStr && (
													<tr className="mallStallTr" data-mall={ supplierIndex }>
														<td
															style={ {
																borderRight: borderStyle,
																position: 'relative',
																padding: '0px 15px',
																borderBottom: "1px solid rgb(170, 170, 170)"
															} }
															onClick={ () => { document.getSelection().empty(); return false; } }
															className="bhd-not-print"
														>
															<Checkbox
																indeterminate={ supplierItem.checkStatus === 0.5 }
																onChange={ () => { handleChangeSupplier(supplierIndex); } }
																checked={ supplierItem.checkStatus && supplierItem.checkStatus > 0 }
																style={ { position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' } }
															/>
														</td>
														<td
															style={ {
																borderBottom: "1px solid rgb(170, 170, 170)",
																borderRight: "1px solid rgb(170, 170, 170)",
																paddingLeft: "12px",
																fontSize: "18px",
																textAlign: "left",
																fontWeight: 600,
																color: "#e63e35"
															} }
															colSpan={ colsSet?.length }
															contentEditable
															suppressContentEditableWarning
														>
															<div className="r-flex r-jc-sb r-m-tb-4">
																<BhdTdMallStallSupplierSelect supplierItem={ supplierItem } changeSupplierSuccess={ (e) => { changeSupplierSuccess({ ...e, supplierIndex, }); } } />
																{
																	showStallNum || showCost || showPayment
																		? (
																			<div className="r-flex r-ai-c">
																				{showStallNum
																					&& <>共<span className={ cs(s.underlineText, 'supplierItemCount') }>{supplierItem.count}</span>件{(showCost || showPayment) && '，'}</>}

																				{showCost
																					&& <>总成本<span className={ cs(s.underlineText, 'supplierItemCost') }>{supplierItem.cost}</span>{showPayment && '，'}</>}

																				{showPayment && <>实付金额<span className={ cs(s.underlineText, 'supplierItemPaymentCount') }>{supplierItem.paymentCount}</span></>}
																			</div>
																		) : null
																}

															</div>
														</td>
														<td
															suppressContentEditableWarning
															contentEditable={ false }
															className="bhd-not-print"
														>
															<Tooltip
																title="删除该行"
																style={ { lineHeight: '25px' } }
																placement="bottom"
															>
																<div className="r-pl-8" onClick={ () => { handleDeleteSupplier(supplierIndex); } }>
																	<Icon className="r-c-999" type="shanjian" pointer hover />
																</div>
															</Tooltip>
														</td>
													</tr>
												)
											}
											{
												supplierItem?.items?.length ? supplierItem?.items.map((orderItem, orderIndex: number) => (
													<React.Fragment
														key={ `Fragment${orderItem.platform}${orderItem.numIid}${orderItem.skuId || ""}` }
													>
														{orderItem.skuCounts?.map((skuItem, skuIndex) => (
															(hasSkuLabel || (!hasSkuLabel && skuIndex === 0)) ? (
																<tr
																	key={ orderItem.platform + orderItem.numIid + skuItem.skuId + skuIndex }
																	style={ { position: 'relative' } }
																	className="orderTr"
																	data-order={ orderIndex }
																	data-mall={ supplierIndex }
																>
																	{skuIndex === 0 && (
																		<td
																			style={ {
																				borderBottom: skuIndex === 0 ? borderStyle : '',
																				borderRight: borderStyle,
																				position: 'relative',
																				padding: '0px 15px',
																			} }
																			onClick={ () => { document.getSelection().empty(); return false; } }
																			rowSpan={ orderItem.skuCounts.length }
																			className="bhd-not-print"
																		>
																			<div className={ isNewItem(orderItem) ? s.newItemCheckbox : '' }>
																				{isNewItem(orderItem) && (
																					<Tooltip title="新增商品仅当次查询结果有效，重新查询后不展示新增商品" placement="top">
																						<div className={ s.newItemLabel }>新</div>
																					</Tooltip>
																				)}
																				<Checkbox
																					onChange={ () => handleCheckCurTableItem(orderItem, orderIndex, supplierIndex) }
																					checked={ orderItem.isChecked }
																				/>
																			</div>
																		</td>
																	)}
																	{
																		(supplierItem.mallStallSupplierStr !== '未绑定货品' && orderItem.sysItemId !== 'null') ? (
																			colsSet?.map((col, colIndex) => {
																				// 新增商品和编辑过的商品
																				const isNewCol = shouldShowNewItemBackground(col, orderItem, supplierIndex, orderIndex, skuIndex);
																				return (!col.showSingleLine || skuIndex === 0) ? (
																					<td
																						rowSpan={ col.showSingleLine ? orderItem.skuCounts.length : 1 }
																						key={ orderItem.platform + orderItem.numIid + col.orderId + 'td' + col.datas }
																						suppressContentEditableWarning
																						className={ cs('delete-icon-td msoNumberFormat', isNewCol ? 'bhd-new-col' : '') }
																						style={ {
																							position: 'relative',
																							width: col.width + 'px',
																							fontSize: col.size + 'px',
																							fontFamily: col.font,
																							lineHeight: col.lineHeight + '%',
																							fontWeight: +col.bold,
																							color: col.color,
																							textAlign: col.align,
																							borderRight: borderStyle,
																							borderBottom: borderStyle,
																							verticalAlign: 'middle',
																							paddingRight: col.showSingleLine ? '' : '25px',
																							whiteSpace: 'pre-wrap',
																							// 根据单元格变更状态设置背景色
																							...(isNewCol ? cellStyles.updated : cellStyles.normal)
																						} }
																						onBlur={ () => {
																							tempEditContent && setBhdData((prev) => {
																								prev.supplierGroupItems[supplierIndex].items[orderIndex].editContent = tempEditContent;
																								tempEditContent = '';
																								runInAction(() => {
																									storeBhdSet.setBhdData(prev.supplierGroupItems);
																								});
																								return prev;
																							});
																						} }
																						onInput={ e => {
																							tempEditContent = '';
																							let temp1 = bhdData.supplierGroupItems[supplierIndex].items[orderIndex].editContent || {};
																							let key = col.showSingleLine ? colIndex : `${colIndex}_${skuItem.itemId}_${skuItem.skuId}`;
																							temp1[key] = (e.target as HTMLElement).innerHTML;
																							tempEditContent = temp1;
																						} }
																					>
																						<BhdTdContent
																							col={ col }
																							oData={ orderItem }
																							skuItem={ skuItem }
																							colIndex={ colIndex }
																							keyValue={ orderItem.platform + orderItem.numIid + col.orderId }
																						/>

																						{/* 编辑图标 */}
																						{isFieldEditable(supplierIndex, orderIndex, col.id, col.showSingleLine ? undefined : skuIndex) ? (
																							<Tooltip
																								title="编辑商品资料"
																								style={ { lineHeight: '25px' } }
																								placement="top"
																							>
																								<span
																									style={ (!col.showSingleLine && orderItem.skuCounts.length > 1) ? {
																										position: 'absolute',
																										right: '18px',
																										top: '50%',
																										transform: 'translateY(-50%)',
																										display: 'none',
																									} : {
																										position: 'absolute',
																										right: '8px',
																										bottom: '8px',
																										display: 'none',
																									} }
																									className="edit-icon"
																									contentEditable={ false }
																									onClick={ () => {
																										handleEditGoods(orderItem, skuItem, col);
																									} }
																									data-point={ Pointer.报表_备货单_编辑icon_点击 }
																								>
																									<Icon className="r-c-999" type="bianji" pointer hover />
																								</span>
																							</Tooltip>
																						) : null}

																						{/* 原有的删除图标 */}
																						{!col.showSingleLine && orderItem.skuCounts.length > 1 ? (
																							<Tooltip
																								title="删除该行"
																								style={ { lineHeight: '25px' } }
																								placement="top"
																							>
																								<span
																									style={ {
																										position: 'absolute',
																										right: '3px',
																										top: '50%',
																										transform: 'translateY(-50%)',
																										display: 'none',
																									} }
																									className="delete-icon"
																									contentEditable={ false }
																									onClick={ () => {
																										handleDeleteSku({
																											platform: orderItem.platform,
																											numIid: orderItem.numIid,
																											supplierIndex,
																											orderIndex,
																											skuIndex
																										});
																									} }
																								>
																									<Icon className="r-c-999" type="shanjian" pointer hover />
																								</span>
																							</Tooltip>
																						) : null}
																					</td>
																				) : '';
																			})
																		) : (
																			<td
																				style={ {
																					borderRight: borderStyle,
																					borderBottom: borderStyle,
																					verticalAlign: 'middle',
																				} }
																				colSpan={ colsSet?.length }
																			>
																				<div
																					style={ {
																						display: 'flex',
																						justifyContent: 'space-between',
																						padding: '0 12px'
																					} }
																				>
																					<span>未绑定货品</span>
																					<span>总数量：{orderItem?.totalCount}</span>
																				</div>
																			</td>
																		)
																	}

																	{skuIndex === 0 && (
																		<td
																			rowSpan={ orderItem.skuCounts.length }
																			key={ orderItem.platform + orderItem.numIid + 'action' }
																			contentEditable={ false }
																			className="bhd-not-print"
																		>
																			<BhdTdOperateContent
																				orderItem={ orderItem }
																				selectIndex={ { supplierIndex, orderIndex, bhdData } }
																				handleDelete={
																					() => { handleDelete({ platform: orderItem.platform, numIid: orderItem.numIid, supplierIndex, orderIndex }); }
																				}
																				handleAdd={
																					(e) => {
																						handleAddSupplier({ ...e, supplierIndex, orderIndex, orderItem });
																					}
																				}
																				handleShowMessage={ () => handleShowMessage(orderItem.platform, orderItem.numIid) }
																			/>
																		</td>
																	)}
																</tr>
															) : <tr />
														))}
														{orderItem._hasMessage && operateMap[orderItem.platform + orderItem.numIid].showMsg
															? orderItem.tradeInfos.map(item => (
																<BhdMsgDetail
																	colLength={ colsSet.length + 1 }
																	key={ `BhdMsgDetail${orderItem.platform}${orderItem.numIid}${item.tid}` }
																	// key={ `BhdMsgDetail_${supplierIndex}` }
																	id={ orderItem.platform + orderItem.numIid }
																	tradeItem={ item }
																	contentWidth={ contentWidth }
																	orderIndex={ orderIndex }
																	supplierIndex={ supplierIndex }
																/>
															)) : <></>}
													</React.Fragment>
												)) : ''
											}
										</React.Fragment>
									))
								}

								{/* 合计行 */}
								{bhdData?.items && (
									<tr key="total" className="totalTr">
										<td
											style={ {
												borderBottom: borderStyle,
												borderRight: borderStyle,
											} }
											className="bhd-not-print"
										/>

										<td
											contentEditable
											suppressContentEditableWarning
											colSpan={ colsSet?.length }
											className="total-td"
											style={ {
												// borderTop: borderStyle,
												borderBottom: borderStyle,
												borderRight: borderStyle,
												textAlign: 'right',
											} }
										>
											<span
												id="printBHDTime"
												style={ {
													float: 'left',
													marginLeft: '10px'
												} }
											/>
											{showStallNum && <>共<span className="bhd-table-total bhd-table-total-count" >{bhdData?.totalCount}</span>件</>}
											{showCost
												&& (
													<>
														&nbsp;总成本：
														<FieldsPermissionCheck
															fieldsPermission={ FieldsPermissionEnum.成本价 }
															type={ FieldsPermissionCheckTypeEnum.仅展示 }
														>
															<span className="bhd-table-total bhd-table-total-cost" >{bhdData?.totalCost}</span>
														</FieldsPermissionCheck>
													</>
												)}

											{showPayment && <>&nbsp;总实付金额：<span className="bhd-table-total bhd-table-total-payment" >{bhdData?.totalPayment}</span></>}
										</td>
									</tr>
								)}
							</tbody>
						</table>
					</div>
				</div>
			</div>


			{/* 编辑商品资料抽屉 */}
			<EditGoodsDrawer
				visible={ editGoodsDrawer.visible }
				onClose={ handleCloseEditDrawer }
				orderItem={ editGoodsDrawer.orderItem }
				skuItem={ editGoodsDrawer.skuItem }
				fieldInfo={ editGoodsDrawer.fieldInfo }
				onSaveSuccess={ handleEditSaveSuccess }
			/>

			{/* 添加商品抽屉 */}
			<AddGoodsDrawer
				visible={ addGoodsDrawer.visible }
				onClose={ () => setAddGoodsDrawer({ visible: false }) }
				onOk={ handleAddGoodsOk }
				addType={ [BhdShowType.平台商品, BhdShowType.规格商品].includes(showType) ? AddTypeEnum.平台商品 : AddTypeEnum.系统货品 }
				hasCollect={ false }
				hasBatchedNum
				autoSearch={ false }
			/>
		</>
	);
});

export default BhdOrderList;
