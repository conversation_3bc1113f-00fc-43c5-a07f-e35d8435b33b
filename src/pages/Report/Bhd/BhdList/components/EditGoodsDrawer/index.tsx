import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { 
	Drawer, 
	Form, 
	Input, 
	Select, 
	Button, 
	Space, 
	Checkbox, 
	message, 
	Tooltip,
	Image,
	Pagination,
	Alert,
	Popover,
	Radio,
	InputNumber,
	Modal
} from 'antd';
import { useForm } from 'antd/es/form/Form';
import { observer } from 'mobx-react';
import { ExclamationCircleOutlined, CloseOutlined, FormOutlined } from '@ant-design/icons';
import cs from 'classnames';
import BaseTable from '@/components/SearchTable/BaseTable';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import WaresInfo from '@/components-biz/WaresInfo';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import Icon from '@/components/Icon';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import { 
	ItemSysItemListOfItemRelationPlatformItemViewApi,
	BatchUpdateSysItemByPlatformApi2 
} from '@/apis/warehouse/system';
import { TradeDictQueryDictApi, TradeDictInsertDictApi } from '@/apis/trade/search';
import { fetchWithPaginationOptimization } from '@/pages/Warehouse/System/Relation/components/PlatformTable/utils/paginationOptimization';
import { 
	ItemSysItemListOfItemRelationPlatformItemViewRequest,
	ItemSysItemListOfItemRelationPlatformItemViewResponse 
} from '@/types/schemas/warehouse/system';
import { BhdOrderItem } from '@/types/schemas/report/bhd/bhdList';
import { DEFAULT_IMG } from '@/constants';
import userStore from '@/stores/user';
import FieldsPermissionCheck, { 
	FieldsPermissionCheckTypeEnum, 
	FieldsPermissionEnum 
} from '@/utils/permissionCheck/fieldsPermissionCheck';
import BatchEditModal, { addPosMap, getAddRuleContent, getNewContent, nameMap } from '@/pages/Warehouse/System/PlatformGoods/components/BatchEditModal';
import { local } from '@/libs/db';
import { getPlatAndShops, isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import WeightInput from '@/components/Input/InputNumber/WeightInput';
import { weightUnit } from '@/pages/Index/Settings/System/constants';
import { BhdLabelName } from '../../../contants';
import { getPlatformDetailLink } from '@/pages/AfterSale/TradeList/utils';
import { batchApiRequestWithProgress } from '@/utils/batchApiRequestWithProgress';
import history from "@/utils/history";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import s from './index.module.scss';

const { Option } = Select;

interface EditGoodsDrawerProps {
  visible: boolean;
  onClose: () => void;
  orderItem?: BhdOrderItem;
  skuItem?: any;
  fieldInfo?: {
    hasGoodsFields: boolean;
    hasSkuFields: boolean;
    fieldsData: any;
  };
  onSaveSuccess?: (updatedInfo?: any) => void; // 修改回调函数签名
}

interface TableListData {
  rowId: string;
  numIid: string;
  skuId: string;
  title: string;
  platform: string;
  sellerNick: string;
  itemPicUrl?: string;
  picUrl?: string;
  outerId?: string;
  skuOuterId?: string;
  sysItemAlias?: string;
  sysSkuAlias?: string;
  market?: string;
  stall?: string;
  supplierName?: string;
  weight?: string;
  costPrice?: string;
  tagPrice?: string;
  classifyId?: string;
  relationSystemItemList?: any[];
  platformItemSkuList?: any[];
  [key: string]: any;
}

// 规格图片预览配置
const settingArr = [{
	label: '小图',
	value: '0',
}, {
	label: '中图',
	value: '1',
}, {
	label: '大图',
	value: '2',
}];

const Tips = () => (
	<Tooltip 
		title="注：京东、1688、淘工厂、快团团平台暂不支持商家编码/规格编码上传，抖店、快手暂不支持商家编码上传，小红书将上传至货号，视频号仅支持已上架商品"
		placement="top"
		overlayStyle={ { maxWidth: 420 } }
	>
		<ExclamationCircleOutlined style={ { color: '#FAAD14' } } />
	</Tooltip>
);

const EditGoodsDrawer: React.FC<EditGoodsDrawerProps> = observer(({
	visible,
	onClose,
	orderItem,
	skuItem,
	fieldInfo,
	onSaveSuccess
}) => {
	const { isDistributorAccount, hasCostPricePermission, isSupplierAccount, isFreeSupplierAccount, isShowZeroStockVersion, getAuthUserType, userInfo } = userStore;
	const isSupplierUserAndStock = isSupplierAccount && !isShowZeroStockVersion; // 常规版的库存版
	const isSupplierUserAndZeroStock = isSupplierAccount && isShowZeroStockVersion; // 常规版的零库存版
    
	const [form] = useForm();
	const [tableForm] = useForm();
	const tableRef = useRef<any>();
	const [loading, setLoading] = useState(false);
	const [dataSource, setDataSource] = useState<TableListData[]>([]);
	const [dataFormSource, setDataFormSource] = useState<any>({}); // 添加表单数据状态
	const [originData, setOriginData] = useState<any>({ list: [], total: 0 });
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 20,
		total: 0
	});
	const [searchParams, setSearchParams] = useState<any>({});
	const [isUpLoadOuterId, setIsUpLoadOuterId] = useState(false); // 是否上传商家编码
	const [isUpLoadSkuOuterId, setIsUpLoadSkuOuterId] = useState(false); // 是否上传规格编码
	const [groupList, setGroupList] = useState<any[]>([]);

	// 显示状态管理
	const [isShowSku, setIsShowSku] = useState(false);
	const [isShowSkuPic, setIsShowSkuPic] = useState(false);
	const [isShowSupplier, setIsShowSupplier] = useState(false);
	const [skuPicPreviewConfig, setSkuPicPreviewConfig] = useState('1');
	// 添加平台商品图片大小配置状态
	const [skuPicGoodsConfig, setSkuPicGoodsConfig] = useState('1');

	// 添加批量编辑状态
	const [batchEditModalInfo, setBatchEditModalInfo] = useState<any>({ visible: false, type: "" });

	// 添加表单值状态跟踪
	const [formValues, setFormValues] = useState<any>({});
	const [isKg, setIsKg] = useState(false);

	// 在组件状态定义区域添加
	const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

	// 表单值变化处理
	const handleFormValuesChange = (changedValues: any, allValues: any) => {
		setFormValues(allValues);
	};

	// 获取平台商品图片大小配置
	const getSkuPicGoodsConfig = async() => {
		let configValue = "1";
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: 'PLAT_ITEM_PAGE_COLUMN_PIC_CONFIG' });
			console.log('平台商品图片配置API返回:', res);
			if (res?.value) {
				try {
					const parsedValue = JSON.parse(res.value);
					configValue = String(parsedValue.value);
				} catch (e) {
					console.log('Failed to parse config', e);
				}
			}
		} catch (error) {
			console.log(error, 'error');
		}
		console.log('设置平台商品图片配置:', configValue, typeof configValue);
		setSkuPicGoodsConfig(configValue);
	};

	// 获取规格图片大小配置
	const getSkuPicPreviewConfig = async() => {
		let configValue = "1";
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: 'PLAT_ITEM_PAGE_COLUMN_PIC_PREVIEW_CONFIG' });
			console.log('规格图片配置API返回:', res);
			if (res?.value) {
				try {
					const parsedValue = JSON.parse(res.value);
					configValue = String(parsedValue.value);
				} catch (e) {
					console.log('Failed to parse config', e);
				}
			}
		} catch (error) {
			console.log(error, 'error');
		}
		console.log('设置规格图片配置:', configValue, typeof configValue);
		setSkuPicPreviewConfig(configValue);
	};

	// 处理平台商品图片大小配置变化
	const onSkuPicGoodsConfigChange = async(value: string) => {
		try {
			const config = { value };
			console.log('设置平台商品图片配置为:', config, typeof value);
			
			await TradeDictInsertDictApi({
				userDictEnum: 'PLAT_ITEM_PAGE_COLUMN_PIC_CONFIG',
				value: JSON.stringify(config)
			}).then(() => {
				// 直接设置字符串值
				setSkuPicGoodsConfig(value);
			});
			console.log('保存平台商品图片配置成功');
		} catch (error) {
			console.log(error, 'error');
		}
	};

	// 处理规格图片大小配置变化
	const onSkuPicPreviewConfigChange = async(value: string) => {
		try {
			const config = { value };
			console.log('设置规格图片配置为:', config, typeof value);
			
			await TradeDictInsertDictApi({
				userDictEnum: 'PLAT_ITEM_PAGE_COLUMN_PIC_PREVIEW_CONFIG',
				value: JSON.stringify(config)
			}).then(() => {
				// 直接设置字符串值
				setSkuPicPreviewConfig(value);
			});
			console.log('保存规格图片配置成功');
		} catch (error) {
			console.log(error, 'error');
		}
	};

	// 组件挂载时获取图片配置
	useEffect(() => {
		// 获取图片配置
		getSkuPicGoodsConfig();
		getSkuPicPreviewConfig();
	}, []);

	// 初始化显示状态和分页缓存
	useEffect(() => {
		if (visible) {
			// 读取分页缓存配置
			const cachedPageSize = local.get("bhd.editGoodsDrawer.pageSize") || 20;
			setPagination(prev => ({
				...prev,
				pageSize: cachedPageSize
			}));
			
			// 【修改】：不在这里设置显示状态，等待fieldInfo处理
			// 只有在没有fieldInfo时才使用缓存状态
			if (!fieldInfo) {
				setIsShowSku(local.getByUserId("warehouse.platformGoods.isShowSku") || false);
				setIsShowSkuPic(local.getByUserId("warehouse.platformGoods.isShowSkuPic") || false);
				setIsShowSupplier(local.getByUserId("warehouse.platformGoods.isShowSupplier") || false);
			}
		}
	}, [visible, fieldInfo]); // 添加fieldInfo依赖

	// 名称工厂函数
	const nameFactory = (id: string, key: string) => `${id}_${key}`;

	// 显示规格切换 - 修复版本，参考平台商品管理
	const onCheckShowSku = () => {
		const nextVal = !isShowSku;
		setIsShowSku(nextVal);
		if (!nextVal) {
			setIsShowSkuPic(false);
		}
		local.setByUserId('warehouse.platformGoods.isShowSku', nextVal);
		
		// 【新增】切换显示模式时清除选中状态
		setSelectedRowKeys([]);
		
		// 关键修复：切换模式时重新应用数据适配器
		if (originData && originData.list && originData.list.length > 0) {
			if (nextVal) {
				responseAdapterSku(originData);
			} else {
				responseAdapterOut(originData);
			}
		}
	};

	// 显示规格图片切换
	const onCheckShowSkuPic = () => {
		const nextVal = !isShowSkuPic;
		local.setByUserId('warehouse.platformGoods.isShowSkuPic', nextVal);
		setIsShowSkuPic(nextVal);
	};

	// 显示档口/供应商切换
	const onCheckShowSupplier = () => {
		const nextVal = !isShowSupplier;
		local.setByUserId('warehouse.platformGoods.isShowSupplier', nextVal);
		setIsShowSupplier(nextVal);
	};

	// 修改现有的勾选逻辑，确保与selectedRowKeys同步
	const onCheckedItem = (e: any, record: any) => {
		setDataSource(prev => {
			let prevIndex = prev.findIndex(d => d.rowId === record.rowId);
			if (prevIndex > -1) {
				prev[prevIndex].isChecked = e.target.checked;
			}
			return [...prev];
		});
		
		// 同步更新selectedRowKeys
		setSelectedRowKeys(prev => {
			if (e.target.checked) {
				return [...prev, record.rowId];
			} else {
				return prev.filter(key => key !== record.rowId);
			}
		});
	};

	// 修改全选逻辑
	const onCheckAllChange = (e: any) => {
		setDataSource(prev => {
			prev.forEach(item => {
				item.isChecked = e.target.checked;
			});
			return [...prev];
		});
		
		// 同步更新selectedRowKeys
		if (e.target.checked) {
			setSelectedRowKeys(dataSource.map(item => item.rowId));
		} else {
			setSelectedRowKeys([]);
		}
	};

	// 修改组选中逻辑
	const onCheckedGroup = (e: any, record: any) => {
		setDataSource(prev => {
			prev.forEach(item => {
				if (item.groupId == record.groupId) {
					item.isChecked = e.target.checked;
				}
			});
			return [...prev];
		});
		
		// 同步更新selectedRowKeys
		const groupItems = dataSource.filter(item => item.groupId === record.groupId);
		setSelectedRowKeys(prev => {
			if (e.target.checked) {
				// 添加组内所有项
				const groupRowIds = groupItems.map(item => item.rowId);
				return [...prev.filter(key => !groupRowIds.includes(key)), ...groupRowIds];
			} else {
				// 移除组内所有项
				const groupRowIds = groupItems.map(item => item.rowId);
				return prev.filter(key => !groupRowIds.includes(key));
			}
		});
	};

	// 修复选中状态计算 - 这是唯一的 checkedGroup 定义
	const checkedGroup = useMemo(() => {
		const tmp = { num: 0, checked: false, disabled: false, disNum: 0, indeterminate: false };
		if (dataSource.length == 0) return { group_all: tmp };
		
		const map = {
			'group_all': { ...tmp, total: dataSource.length }
		};
		
		dataSource.forEach(item => {
			if (!map[item.groupId]) map[item.groupId] = { ...tmp, total: item.colSpan || 1 };

			if (item.isChecked) {
				map[item.groupId].num++;
				map['group_all'].num++;
			}
		});
		
		for (let key in map) {
			const { total, num } = map[key];
			if (num == total) {
				map[key].checked = true;
			}
			if (num > 0 && num < total) {
				map[key].indeterminate = true;
			}
		}
		return map;
	}, [dataSource]);

	const selectedRows = useMemo(() => {
		return dataSource.filter(item => item.isChecked);
	}, [dataSource]);

	// 数据适配器 - 商品维度 (修复版本)
	const responseAdapterOut = (data: any) => {
		console.log('=== responseAdapterOut 商品模式适配器开始 ===');
		console.log('输入数据:', data);
		
		const formData = {};
		const tableList: any = [];

		data?.list?.forEach((item: any, i: number) => {
			if (!item.platformItemSkuList || item.platformItemSkuList.length === 0) {
				console.warn(`商品 ${i} 没有规格信息，跳过:`, item);
				return;
			}

			const firstSku = item.platformItemSkuList[0];
			console.log(`处理商品 ${i}:`, {
				numIid: item.numIid,
				title: item.title,
				platform: item.platform,
				firstSku
			});
			
			let obj = {
				groupId: i,
				groupIndex: i + 1,
				rowId: item.numIid,
				colSpan: 1,
				isChecked: false, // 添加勾选状态
				...item,
				...firstSku,
				numIid: item.numIid,
				skuId: firstSku.skuId,
				sellerId: item.sellerId || firstSku.sellerId,
				platform: item.platform,
				sellerNick: item.sellerNick || firstSku.sellerNick,
				title: item.title,
				itemPicUrl: item.itemPicUrl,
				picUrl: firstSku.picUrl,
				outerId: item.outerId || '',
				skuOuterId: firstSku.skuOuterId || '',
			};

			// 表单字段处理 - 添加商家编码字段
			['supplierName', 'stall', 'market', 'costPrice', 'tagPrice', 'classifyId', 'weight', 'sysSkuAlias', 'sysItemAlias', 'outerId'].forEach(key => {
				const name = nameFactory(obj.numIid, key);
				if (key === 'sysItemAlias') {
					formData[name] = firstSku?.relationSystemItemList?.[0]?.sysItemAlias || '';
				} else if (key === 'outerId') {
					formData[name] = item.outerId || '';
				} else {
					const firstVal = firstSku?.relationSystemItemList?.[0]?.[key];
					const isConsistent = item.platformItemSkuList.every(sku => sku.relationSystemItemList?.[0]?.[key] === firstVal);
					formData[name] = isConsistent ? (firstVal || '') : '';
				}
				// console.log(`字段 ${key} 处理结果:`, { name, value: formData[name] });
			});

			tableList.push(obj);
		});

		console.log('商品模式适配结果:', {
			tableListCount: tableList.length,
			formDataKeys: Object.keys(formData),
			sampleFormData: Object.keys(formData).slice(0, 5).reduce((acc, key) => {
				acc[key] = formData[key];
				return acc;
			}, {})
		});

		setDataSource(tableList);
		setDataFormSource(formData);
		
		setTimeout(() => {
			tableForm.setFieldsValue(formData);
			console.log('表单数据设置完成');
		}, 0);
		
		console.log('=== responseAdapterOut 商品模式适配器结束 ===');
		
		return {
			list: tableList,
			total: data.total
		};
	};

	// 数据适配器 - 规格维度 (修复版本)
	const responseAdapterSku = (data: any) => {
		console.log('=== responseAdapterSku 规格模式适配器开始 ===');
		console.log('输入数据:', data);
		
		const formData = {};
		const tableList: any = [];
		
		console.log('规格适配器接收到的数据:', data);
		
		// 添加数据有效性检查
		if (!data?.list || !Array.isArray(data.list)) {
			console.warn('数据格式错误或为空:', data);
			setDataSource([]);
			setDataFormSource({});
			
			setTimeout(() => {
				if (tableForm) {
					tableForm.setFieldsValue({});
				}
			}, 0);
			
			return { list: [], total: 0 };
		}
		
		data.list.forEach((item: any, i: number) => {
			if (!item?.platformItemSkuList || item.platformItemSkuList.length === 0) {
				console.warn('商品缺少规格信息:', item);
				return;
			}

			let itemLen = item.platformItemSkuList.length;
			console.log(`处理商品 ${i} (${item.numIid})，规格数量: ${itemLen}`);

			item.platformItemSkuList.forEach((platformItem: any, index: number) => {
				const rowId = `${i}_${index}_${itemLen}`;
				
				const obj = {
					groupId: i,
					groupIndex: i + 1,
					...(index === 0 ? { colSpan: itemLen } : {}),
					...item,
					...platformItem,
					rowId,
					numIid: item.numIid,
					skuId: platformItem.skuId,
					isChecked: false, // 添加勾选状态
					sellerId: item.sellerId || platformItem.sellerId,
					platform: item.platform,
					sellerNick: item.sellerNick || platformItem.sellerNick,
					title: item.title,
					itemPicUrl: item.itemPicUrl,
					picUrl: platformItem.picUrl,
					outerId: item.outerId || '',
					skuOuterId: platformItem.skuOuterId || '',
					skuName: platformItem.skuName || '',
					relationSystemItem: platformItem.relationSystemItemList?.[0] || {},
				};

				console.log(`规格 ${index} 数据:`, {
					rowId,
					skuId: obj.skuId,
					skuName: obj.skuName,
					relationSystemItem: obj.relationSystemItem
				});

				// 处理表单字段
				if (index === 0) {
					let name = nameFactory(obj.numIid, 'sysItemAlias');
					formData[name] = obj.relationSystemItem?.sysItemAlias || '';
					console.log(`商品级字段 sysItemAlias:`, { name, value: formData[name] });
					
					// 添加商家编码字段 - 商品级别
					let outerIdName = nameFactory(obj.numIid, 'outerId');
					formData[outerIdName] = item.outerId || '';
					console.log(`商品级字段 outerId:`, { outerIdName, value: formData[outerIdName] });
				}
				
				// 添加规格编码字段到规格级别字段中
				['supplierName', 'stall', 'market', 'costPrice', 'tagPrice', 'weight', 'sysSkuAlias', 'skuOuterId'].forEach(key => {
					let name = nameFactory(obj.rowId, key);
					if (key === 'skuOuterId') {
						formData[name] = platformItem.skuOuterId || '';
					} else {
						formData[name] = obj.relationSystemItem?.[key] || '';
					}
					console.log(`规格级字段 ${key}:`, { name, value: formData[name] });
				});
				
				tableList.push(obj);
			});
		});
		
		console.log('规格模式适配结果:', {
			tableListCount: tableList.length,
			formDataKeys: Object.keys(formData),
			sampleFormData: Object.keys(formData).slice(0, 10).reduce((acc, key) => {
				acc[key] = formData[key];
				return acc;
			}, {})
		});
		
		setDataSource(tableList);
		setDataFormSource(formData);
		
		setTimeout(() => {
			if (tableForm) {
				tableForm.setFieldsValue(formData);
				console.log('规格模式表单数据设置完成');
			}
		}, 0);
		
		console.log('=== responseAdapterSku 规格模式适配器结束 ===');
		
		// 添加返回值 - 这是关键修复
		return {
			list: tableList,
			total: data.total
		};
	};

	// 数据适配器选择
	const responseAdapter = (data: any) => {
		if (isShowSku) return responseAdapterSku(data);
		else return responseAdapterOut(data);
	};

	// 表单字段变化处理
	const onFieldsChange = (changedValues: any, allValues: any) => {
		console.log('%c [ changedValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues);
		setDataFormSource(pre => ({ ...pre, ...allValues }));
	};

	// 表单字段配置
	const FormFieldList: FormItemConfig[] = [
		{
			name: "platformInfo",
			children: (
				<ShopMultiSelect
					// isHasHandPlat
					bgHighLight
					style={ { width: '159px' } }
				/>
			),
		},
		{
			name: "title",
			children: (
				<Input 
					placeholder="商品名称" 
					style={ { width: 160 } } 
					className={ formValues.title && formValues.title.trim() ? 'high-light-bg' : '' }
				/>
			),
		},
		{
			name: 'skuNameList',
			children: (
				<Input 
					placeholder="规格名称" 
					style={ { width: 160 } } 
					className={ formValues.skuNameList && formValues.skuNameList.trim() ? 'high-light-bg' : '' }
				/>
			),
		},
		{
			name: "numIids",
			children: (
				<Input 
					placeholder="商品ID" 
					style={ { width: 160 } } 
					className={ formValues.numIids && formValues.numIids.trim() ? 'high-light-bg' : '' }
				/>
			),
		},
		{
			name: "sysItemAlias",
			children: (
				<Input 
					placeholder="简称" 
					style={ { width: 160 } } 
					className={ formValues.sysItemAlias && formValues.sysItemAlias.trim() ? 'high-light-bg' : '' }
				/>
			),
		},
		{
			name: "outerId",
			children: (
				<Input 
					placeholder="商家编码" 
					style={ { width: 160 } } 
					className={ formValues.outerId && formValues.outerId.trim() ? 'high-light-bg' : '' }
				/>
			),
		},
		{
			name: "skuOuterId",
			children: (
				<Input 
					placeholder="规格编码" 
					style={ { width: 160 } } 
					className={ formValues.skuOuterId && formValues.skuOuterId.trim() ? 'high-light-bg' : '' }
				/>
			),
		},
		{
			name: 'skuContent',
			children: (
				<Input 
					placeholder="规格名称/规格别名" 
					style={ { width: 160 } } 
					className={ formValues.skuContent && formValues.skuContent.trim() ? 'high-light-bg' : '' }
				/>
			)
		},
		{
			name: "market",
			children: (
				<Input 
					placeholder="市场" 
					style={ { width: 160 } } 
					className={ formValues.market && formValues.market.trim() ? 'high-light-bg' : '' }
				/>
			),
		},
		{
			name: "stall",
			children: (
				<Input 
					placeholder="档口" 
					style={ { width: 160 } } 
					className={ formValues.stall && formValues.stall.trim() ? 'high-light-bg' : '' }
				/>
			),
		},
		{
			name: "supplierName",
			children: (
				<Input 
					placeholder="供应商名称" 
					style={ { width: 160 } } 
					className={ formValues.supplierName && formValues.supplierName.trim() ? 'high-light-bg' : '' }
				/>
			),
		}
	];

	// 修复 EditIcon 组件 - 使用 selectedRows 而不是 selectedRowKeys
	const EditIcon = ({ type }) => (
		<FormOutlined
			onClick={ () => {
				if (selectedRows.length === 0) {
					return message.warn('需先勾选商品');
				}
				setBatchEditModalInfo({ visible: true, type });
			} }
			className="r-c-blue r-pointer r-ml-5"
			style={ { fontSize: '14px' } }
		/>
	);

	// 添加 onCell 处理函数（参考平台商品管理）
	const onCellRender = (row: any, index: number, key:string) => {
		const style: React.CSSProperties = {};
		
		if (!row.colSpan) {
			// 非合并行移除顶部边框
			style.borderTop = 0;
		}

		// 需要处理下商品的边框
		if (['checkCol', 'indexCol', 'platformShop', 'title', 'outerId', 'sysItemAlias'].includes(key)) {
			// style.borderRight = 0;
			style.borderBottom = 0;
		}

		// 最后一行数据的下边框
		if (index == dataSource?.length - 1) {
			style.borderBottom = '1px solid #f0f0f0';
		}
		
		// 在规格模式下，检查是否是商品组的最后一行
		if (isShowSku) {
			// 找到当前商品组的所有行
			const currentGroupRows = dataSource.filter(item => item.groupId === row.groupId);
			// 检查当前行是否是该组的最后一行
			const isLastRowInGroup = currentGroupRows[currentGroupRows.length - 1]?.rowId === row.rowId;
			
			if (isLastRowInGroup && index !== dataSource.length - 1) {
				// 如果是最后一行，移除底部边框
				style.borderBottom = 0;
			}
		}
		
		return {
			style,
			rowSpan: 1,
		};
	};

	// 表格列配置 (现在可以安全使用 EditIcon)
	const columns = useMemo(() => {
		// 商品级勾选列
		const checkCol = {
			title: (
				<Checkbox
					checked={ checkedGroup.group_all?.checked }
					disabled={ checkedGroup.group_all?.disabled }
					indeterminate={ checkedGroup.group_all?.indeterminate }
					onChange={ onCheckAllChange }
				/>
			),
			align: "center",
			width: 50,
			onCell: (row: any, index: number) => onCellRender(row, index, 'checkCol'),
			render: (text: any, row: TableListData) => {
				if (isShowSku) {
					// 规格模式下的商品级勾选 - 添加行合并
					const checkNode = (
						<Checkbox
							disabled={ checkedGroup[row.groupId]?.disabled }
							onChange={ (e) => onCheckedGroup(e, row) }
							checked={ checkedGroup[row.groupId]?.checked }
							indeterminate={ checkedGroup[row.groupId]?.indeterminate }
						/>
					);
					
					return {
						children: row.colSpan ? checkNode : null,
						props: {
							rowSpan: 1,
						}
					};
				} else {
					// 商品模式下的单项勾选
					return (
						<Checkbox
							onChange={ (e) => onCheckedItem(e, row) }
							checked={ row.isChecked }
						/>
					);
				}
			},
		};

		// 序号列
		const indexCol = {
			title: '序号',
			align: 'center',
			width: 60,
			onCell: (row: any, index: number) => onCellRender(row, index, 'indexCol'),
			render: (text: any, row: TableListData, index: number) => {
				// 在规格模式下处理行合并
				if (isShowSku) {
					return {
						children: row.colSpan ? <>{row.groupIndex}</> : null,
						props: {
							rowSpan: 1,
						}
					};
				}
				return <>{index + 1}</>;
			},
		};

		// 基础列配置
		const baseColumns = [
			{
				title: '平台/店铺',
				width: 120,
				dataIndex: 'platformShop',
				onCell: (row: any, index: number) => onCellRender(row, index, 'platformShop'),
				render: (text: any, row: TableListData) => {
					const content = (
						<div className="r-flex">
							<PlatformIcon platform={ row?.platform } />
							{row?.sellerNick}
						</div>
					);

					// 在规格模式下处理行合并
					if (isShowSku) {
						return {
							children: row.colSpan ? content : null,
							props: {
								rowSpan: 1,
							}
						};
					}
					return content;
				},
			},
			{
				title: (
					<div className="r-flex r-ai-c">
						平台商品
						<Popover 
							placement="bottom" 
							title={ null } 
							content={ (
								<div className="r-pd-2" style={ { display: 'flex' } }>
									<div className="r-mb-2">预览图展示：</div>
									<Radio.Group
										value={ skuPicGoodsConfig }
										onChange={ (e) => onSkuPicGoodsConfigChange(e.target.value) }
									>
										<div style={ { display: 'flex', flexDirection: 'row' } }>
											{settingArr.map(item => (
												<Radio key={ item.value } value={ item.value } style={ { marginRight: '8px' } }>{item.label}</Radio>
											))}
										</div>
									</Radio.Group>
								</div>
							) } 
							trigger="click"
						>
							<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
						</Popover>
					</div>
				),
				width: 250,
				dataIndex: "title",
				onCell: (row: any, index: number) => onCellRender(row, index, 'title'),
				render: (text: any, row: TableListData) => {
					const { platform, numIid, skuId, skuUuid } = row;
					const img = row?.itemPicUrl || row?.picUrl || DEFAULT_IMG;
					
					// 生成平台商品详情链接的ID
					let id = numIid;
					if (['XHS', 'JD'].includes(platform)) {
						id = skuId;
					}
					if (['YZ'].includes(platform)) {
						id = skuUuid;
					}
					const getLinkHref = getPlatformDetailLink(platform, id);
					
					// 根据配置设置图片大小
					const imgSizeConfig = {
						'0': 48, // 小图
						'1': 48, // 中图
						'2': 48 // 大图
					}[skuPicGoodsConfig] || 48;

					// 根据配置设置Popover中的大图尺寸
					const imgSizePopover = {
						'0': 300, // 小图对应的Popover大图
						'1': 500, // 中图对应的Popover大图
						'2': 800 // 大图对应的Popover大图
					}[skuPicGoodsConfig] || 500;

					// Popover内容 - 显示大图
					const popContent: React.ReactNode = (
						<Image
							width={ imgSizePopover }
							height={ imgSizePopover }
							src={ img }
							fallback={ DEFAULT_IMG }
							preview={ false }
						/>
					);

					const content = (
						<div className={ cs("r-flex", s['cell-gap']) }>
							<Popover placement="right" content={ popContent }>
								<a
									href={ !userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(row) ? undefined : getLinkHref || "javascript:void(0)" }
									target={ getLinkHref ? "_blank" : "_self" }
									rel="noopener noreferrer"
									className={ cs("r-flex", "r-ai-c") }
								>
									<Image
										width={ imgSizeConfig }
										height={ imgSizeConfig }
										src={ img }
										fallback={ DEFAULT_IMG }
										preview={ false }
										className="r-pointer"
									/>
								</a>
							</Popover>
							<div className={ cs(s.platTitle, "r-flex-1") }>
								<div title={ text } className={ s.lineMax2 }>
									{text}
								</div>
								<div className={ cs(s.lineMax1, "r-mt-4") } title={ row.numIid }>
									商品ID: {row.numIid}
								</div>
							</div>
						</div>
					);

					if (isShowSku) {
						return {
							children: row.colSpan ? content : null,
							props: {
								rowSpan: 1,
							}
						};
					}
					return content;
				},
			},
			// 商家编码列 - 暂时注释
			// {
			// 	title: (
			// 		<>
			// 			<span className="r-mr-4">商家编码</span>
			// 			<Tips />
			// 		</>
			// 	),
			// 	width: 150,
			// 	dataIndex: 'outerId',
			// 	onCell: (row: any, index: number) => onCellRender(row, index, 'outerId'),
			// 	render: (text: any, row: TableListData) => {
			// 		const name = nameFactory(row.numIid, 'outerId');
			// 		const content = (
			// 			<Tooltip title={ dataFormSource?.[name] } trigger="hover">
			// 				<Form.Item
			// 					name={ name }
			// 					className={ cs('r-mb-0', 'r-mt-0') }
			// 				>
			// 					<Input
			// 						maxLength={ 60 }
			// 						style={ { width: '100%' } }
			// 					/>
			// 				</Form.Item>
			// 			</Tooltip>
			// 		);

			// 		if (isShowSku) {
			// 			return {
			// 				children: row.colSpan ? content : null,
			// 				props: {
			// 					rowSpan: 1,
			// 				}
			// 			};
			// 		}
			// 		return content;
			// 	}
			// },
			{
				title: '商家编码',
				width: 150,
				dataIndex: 'outerId',
				onCell: (row: any, index: number) => onCellRender(row, index, 'outerId'),
				render: (text: any, row: TableListData) => {
					return <div>{text}</div>;
				}
			},
			{
				title: (
					<>
						简称
						<EditIcon type="sysItemAlias" />
					</>
				),
				width: 150,
				dataIndex: 'sysItemAlias',
				onCell: (row: any, index: number) => onCellRender(row, index, 'sysItemAlias'),
				render: (text: any, row: TableListData) => {
					const name = nameFactory(row.numIid, 'sysItemAlias');
					const content = (
						<Tooltip title={ dataFormSource?.[name] } trigger="hover">
							<Form.Item
								name={ name }
								className={ cs('r-mb-0', 'r-mt-0') }
							>
								<Input
									maxLength={ 60 }
									style={ { width: '100%' } }
								/>
							</Form.Item>
						</Tooltip>
					);

					if (isShowSku) {
						return {
							children: row.colSpan ? content : null,
							props: {
								rowSpan: 1,
							}
						};
					}
					return content;
				}
			}
		];

		// 规格相关列
		const skuColumns = [
			{
				title: "",
				width: 40,
				className: "table-left-border",
				onCell: (row: any, index: number) => onCellRender(row, index, 'skuCheckBox'),
				render: (text: any, row: TableListData) => {
					return (
						<div className="r-flex r-ai-c">
							<Checkbox
								className="r-mr-10"
								onChange={ (e) => onCheckedItem(e, row) }
								checked={ row.isChecked }
							/>
						</div>
					);
				},
			},
			{
				title: "规格名称",
				width: 150,
				dataIndex: 'skuName',
				onCell: (row: any, index: number) => onCellRender(row, index, 'skuName'),
				render: (text: any, row: TableListData) => {
					let val = text || "无规格";
					if (row.platformItemSkuList?.length === 1 && row.skuId === "0") val = "无规格";
					return (
						<div className="r-flex r-ai-c">
							<div title={ val } className="r-l-preWrap">
								{row.enableStatus === 0 ? (
									<span className="r-c-error">
										(平台已删){" "}
									</span>
								) : (
									""
								)}
								{val}
							</div>
						</div>
					);
				}
			},
			// 规格编码列 - 暂时注释
			// {
			// 	title: (
			// 		<>
			// 			<span className="r-mr-4">规格编码</span>
			// 			<Tips />
			// 		</>
			// 	),
			// 	width: 150,
			// 	dataIndex: 'skuOuterId',
			// 	onCell: (row: any, index: number) => onCellRender(row, index, 'skuOuterId'),
			// 	render: (text: any, row: TableListData) => {
			// 		const name = nameFactory(row.rowId, 'skuOuterId');
			// 		return (
			// 			<Tooltip title={ dataFormSource?.[name] } trigger="hover">
			// 				<Form.Item
			// 					name={ name }
			// 					className={ cs('r-mb-0', 'r-mt-0') }
			// 				>
			// 					<Input
			// 						maxLength={ 60 }
			// 						style={ { width: '100%' } }
			// 					/>
			// 				</Form.Item>
			// 			</Tooltip>
			// 		);
			// 	}
			// },
			{
				title: '规格编码',
				width: 150,
				dataIndex: 'skuOuterId',
				onCell: (row: any, index: number) => onCellRender(row, index, 'skuOuterId'),
				render: (text: any, row: TableListData) => {
					return <div>{text}</div>;
				}
			},
			{
				title: (
					<>
						规格别名
						<EditIcon type="sysSkuAlias" />
					</>
				),
				width: 150,
				dataIndex: 'sysSkuAlias',
				onCell: (row: any, index: number) => onCellRender(row, index, 'sysSkuAlias'),
				render: (text: any, row: TableListData) => {
					const name = nameFactory(row.rowId, 'sysSkuAlias');
					return (
						<Tooltip title={ dataFormSource?.[name] } trigger="hover">
							<Form.Item
								name={ name }
								className={ cs('r-mb-0', 'r-mt-0') }
							>
								<Input
									maxLength={ 60 }
									style={ { width: '100%' } }
								/>
							</Form.Item>
						</Tooltip>
					);
				}
			}
		];

		// 规格图片列
		const skuPicColumn = {
			title: (
				<div className="r-flex r-ai-c">
					规格图
					<Popover 
						placement="bottom" 
						title={ null } 
						content={ (
							<div className="r-pd-2" style={ { display: 'flex' } }>
								<div className="r-mb-2">预览图展示：</div>
								<Radio.Group
									value={ skuPicPreviewConfig }
									onChange={ (e) => onSkuPicPreviewConfigChange(e.target.value) }
								>
									<div style={ { display: 'flex', flexDirection: 'row' } }>
										{settingArr.map(item => (
											<Radio key={ item.value } value={ item.value } style={ { marginRight: '8px' } }>{item.label}</Radio>
										))}
									</div>
								</Radio.Group>
							</div>
						) } 
						trigger="click"
					>
						<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
					</Popover>
				</div>
			),
			dataIndex: 'picUrl',
			width: 70,
			onCell: (row: any, index: number) => onCellRender(row, index, 'picUrl'),
			render: (text: any, row: TableListData) => {
				return (
					<div className="r-flex">
						<WaresInfo imgUrl={ row.picUrl } previewPicSize={ Number(skuPicPreviewConfig) } />
					</div>
				);
			}
		};

		// 重量和成本价列
		const weightCostColumns = [
			{
				title: (
					<>
						重量({isKg ? 'kg' : 'g'})
						<EditIcon type="weight" />
					</>
				),
				width: 150,
				dataIndex: 'weight',
				onCell: (row: any, index: number) => onCellRender(row, index, 'weight'),
				render: (text: any, row: TableListData) => {
					const name = nameFactory(isShowSku ? row.rowId : row.numIid, 'weight');
					return (
						<Form.Item
							shouldUpdate
							name={ name }
							className={ cs('r-mb-0', 'r-mt-0') }
						>
							<WeightInput isKg={ isKg } width="100%" />
						</Form.Item>
					);
				}
			},
			{
				title: (
					<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 }>
						成本价(元)
						<EditIcon type="costPrice" />
					</FieldsPermissionCheck>
				),
				width: 150,
				dataIndex: 'costPrice',
				onCell: (row: any, index: number) => onCellRender(row, index, 'costPrice'),
				render: (text: any, row: TableListData) => {
					const name = nameFactory(isShowSku ? row.rowId : row.numIid, 'costPrice');
					return (
						<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 }>
							<Form.Item
								shouldUpdate
								name={ name }
								className={ cs('r-mb-0', 'r-mt-0') }
							>
								<InputNumber
									disabled={ !hasCostPricePermission }
									min={ 0 }
									max={ 999999.9999 }
									precision={ 4 }
									formatter={ value => {
										const strValue = String(value || '');
										return strValue.includes('.') ? strValue.replace(/\.?0+$/, '') : strValue;
									} }
									style={ { width: '100%' } }
								/>
							</Form.Item>
						</FieldsPermissionCheck>
					);
				}
			}
		];

		// 档口/供应商相关列
		const supplierColumns = [
			{
				title: (
					<>
						市场
						<EditIcon type="market" />
					</>
				),
				width: 150,
				dataIndex: 'market',
				onCell: (row: any, index: number) => onCellRender(row, index, 'market'),
				render: (text: any, row: TableListData) => {
					const name = nameFactory(isShowSku ? row.rowId : row.numIid, 'market');
					return (
						<Tooltip title={ dataFormSource?.[name] } trigger="hover">
							<Form.Item
								name={ name }
								className={ cs('r-mb-0', 'r-mt-0') }
							>
								<Input
									maxLength={ 64 }
									style={ { width: '100%' } }
								/>
							</Form.Item>
						</Tooltip>
					);
				}
			},
			{
				title: (
					<>
						档口
						<EditIcon type="stall" />
					</>
				),
				width: 150,
				dataIndex: 'stall',
				onCell: (row: any, index: number) => onCellRender(row, index, 'stall'),
				render: (text: any, row: TableListData) => {
					const name = nameFactory(isShowSku ? row.rowId : row.numIid, 'stall');
					return (
						<Tooltip title={ dataFormSource?.[name] } trigger="hover">
							<Form.Item
								name={ name }
								className={ cs('r-mb-0', 'r-mt-0') }
							>
								<Input
									maxLength={ 64 }
									style={ { width: '100%' } }
								/>
							</Form.Item>
						</Tooltip>
					);
				}
			},
			{
				title: (
					<>
						供应商
						<EditIcon type="supplierName" />
					</>
				),
				width: 150,
				dataIndex: 'supplierName',
				onCell: (row: any, index: number) => onCellRender(row, index, 'supplierName'),
				render: (text: any, row: TableListData) => {
					const name = nameFactory(isShowSku ? row.rowId : row.numIid, 'supplierName');
					return (
						<Tooltip title={ dataFormSource?.[name] } trigger="hover">
							<Form.Item
								name={ name }
								className={ cs('r-mb-0', 'r-mt-0') }
							>
								<Input
									maxLength={ 64 }
									style={ { width: '100%' } }
								/>
							</Form.Item>
						</Tooltip>
					);
				}
			}
		];

		// 根据显示状态组合列
		let finalColumns: any[] = [checkCol, indexCol, ...baseColumns];

		// 如果显示规格，添加规格相关列
		if (isShowSku) {
			// 先添加规格图片列（如果需要显示）
			let skuColumnsToAdd = [...skuColumns];
			if (isShowSkuPic) {
				// 在规格名称列前面插入规格图片列
				skuColumnsToAdd.splice(1, 0, skuPicColumn);
			}
			
			finalColumns = [checkCol, indexCol, ...baseColumns, ...skuColumnsToAdd];
		}

		// 添加重量和成本价列
		finalColumns = [...finalColumns, ...weightCostColumns];

		// 如果显示档口/供应商，添加相关列
		if (isShowSupplier && (!isDistributorAccount && !isSupplierUserAndStock)) {
			finalColumns = [...finalColumns, ...supplierColumns];
		}

		return finalColumns;
	}, [
		groupList, 
		dataFormSource, 
		isShowSku, 
		isShowSkuPic, 
		isShowSupplier, 
		skuPicPreviewConfig,
		skuPicGoodsConfig,
		isDistributorAccount, 
		isSupplierUserAndStock,
		checkedGroup,
		selectedRows
	]);

	// 获取数据 - 修复版本，保存原始数据
	const fetchData = async(params: any, forceIsShowSku?: boolean) => {
		try {
			setLoading(true);
			
			console.log('=== fetchData 开始执行 ===');
			console.log('传入参数 params:', params);
			console.log('强制显示规格 forceIsShowSku:', forceIsShowSku);
			
			// 使用 getPlatAndShops 处理平台和店铺信息，与平台商品管理保持一致
			const { shopId, platform } = await getPlatAndShops(params.platformInfo, false);
			console.log('处理后的平台店铺信息:', { shopId, platform });
			
			// 构建查询参数，排除 platformInfo，只使用转换后的 platformList 和 sellerIdList
			const { platformInfo, current, ...restParams } = params;
			const searchData = {
				...restParams,
				pageNo: params.current || 1,
				pageSize: params.pageSize || 20,
				// 使用 getPlatAndShops 返回的数据
				platformList: platform,
				sellerIdList: shopId,
				// 添加与平台商品管理一致的默认参数  
				itemType: 1, // 商品类型
			};

			console.log('最终API请求参数:', searchData);
			
			const data = await fetchWithPaginationOptimization(searchData, searchData.pageSize);
			console.log('API响应数据:', data);
		
			if (data && data.list && Array.isArray(data.list) && data.list.length > 0) {
				console.log('数据处理开始，原始数据条数:', data.list.length);
				// 保存原始数据，用于模式切换
				setOriginData(data);
				
				// 【修正】使用传入的 forceIsShowSku 参数或当前的 isShowSku 状态
				const currentIsShowSku = forceIsShowSku !== undefined ? forceIsShowSku : isShowSku;
				console.log('使用的显示模式 currentIsShowSku:', currentIsShowSku);
				
				// 使用数据适配器处理数据
				const result = currentIsShowSku ? responseAdapterSku(data) : responseAdapterOut(data);
				console.log('数据适配器处理结果:', result);
				
				setPagination(prev => ({
					...prev,
					total: result?.total || 0,
					current: params.current || 1,
					pageSize: params.pageSize || 20
				}));
				console.log('分页信息设置完成');
			} else {
				console.log('没有数据或数据格式错误，清空表格');
				// 如果没有数据，清空表格和原始数据
				setOriginData({ list: [], total: 0 });
				setDataSource([]);
				setDataFormSource({});
				setPagination(prev => ({
					...prev,
					total: 0,
					current: 1
				}));
			}
			
		} catch (error) {
			console.error('获取数据失败:', error);
			// 错误时也要清空数据
			setOriginData({ list: [], total: 0 });
			setDataSource([]);
			setDataFormSource({});
		} finally {
			setLoading(false);
			console.log('=== fetchData 执行完成 ===');
		}
	};

	// 查询
	const handleSearch = () => {
		const values = form.getFieldsValue();
		setSearchParams(values);
		
		// 使用 fetchData 函数进行查询，传递当前的 isShowSku 状态
		fetchData({ ...values, current: 1, pageSize: pagination.pageSize }, isShowSku);
	};

	// 重置
	const handleReset = () => {
		form.resetFields();
		
		// 确保重置后清除高亮状态
		setTimeout(() => {
			const resetFormValues = form.getFieldsValue();
			setFormValues(resetFormValues); // 这应该是空对象，清除所有高亮
		}, 0);
		
		setSearchParams({});
		fetchData({ current: 1, pageSize: pagination.pageSize }, isShowSku);
	};

	// 统一的分页处理函数
	const handlePaginationChange = useCallback((page: number, pageSize?: number) => {
		console.log('%c [ 分页变化 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', page, pageSize);
		
		const newPageSize = pageSize || pagination.pageSize;
		
		// 如果页面大小发生变化，保存到缓存
		if (pageSize && pageSize !== pagination.pageSize) {
			local.set('bhd.editGoodsDrawer.pageSize', pageSize);
		}
		
		// 关键：如果是分页大小改变，重置到第一页
		const newPage = pageSize && pageSize !== pagination.pageSize ? 1 : page;
		
		const params = { ...searchParams, current: newPage, pageSize: newPageSize };
		
		setPagination(prev => ({ 
			...prev, 
			current: newPage, 
			pageSize: newPageSize 
		}));
		
		fetchData(params, isShowSku);
	}, [searchParams, pagination.pageSize, isShowSku]);

	// 串行分拆请求
	const batchUpdateData = async(data) => {
		return batchApiRequestWithProgress(
			BatchUpdateSysItemByPlatformApi2,
			data,
			{
				batchSize: 200,
				progressTitle: '信息更新中',
				successMessage: '批量更新完成！',
				showSuccessMessage: true,
				onAllComplete: (batchResult) => {
					// 失败就终止了，所以也无需显示失败结果弹框
				},
				onError: (error, batchIndex, totalBatches) => {
					console.error(`第 ${batchIndex} 批处理失败:`, error);
				}
			}
		);
	};

	// 保存数据
	const handleSave = async() => {
		try {
			// 检查是否有勾选的数据
			if (selectedRows.length === 0) {
				message.warn('请先勾选要保存的商品');
				return;
			}

			setLoading(true);
			const formValues = tableForm.getFieldsValue();
			const nameFactory = (id: string, field: string) => `${id}_${field}`;
			
			// 参照平台商品管理的 getList 逻辑
			const list = isShowSku 
				? selectedRows 
				: selectedRows.reduce((arr, v) => {
					// 【修正】：在商品模式下，需要从原始数据获取完整的 platformItemSkuList
					const originalItem = originData?.list?.find(orig => orig.numIid === v.numIid);
					if (originalItem?.platformItemSkuList) {
						return arr.concat(originalItem.platformItemSkuList.map(i => ({ 
							...originalItem, 
							...i,
							// 保持关键字段
							platform: originalItem.platform,
							sellerId: originalItem.sellerId,
							title: originalItem.title,
							itemPicUrl: originalItem.itemPicUrl,
							outerId: originalItem.outerId
						})));
					}
					return arr;
				}, []);
			
			// 参照平台商品管理的逻辑，处理规格模式下简称修改的情况
			const otherBindObj: any = {};
			const bindedObj: any = {};
			const bindRelations: any[] = [];
			
			list.forEach(j => {
				const factoryId = isShowSku ? j.rowId : j.numIid;
				// 【修正】：根据数据结构正确获取 sysItem
				const sysItem = j.relationSystemItemList?.[0];
				const oldSysItemAlias = sysItem?.sysItemAlias || "";
				const newSysItemAlias = formValues[nameFactory(j.numIid, 'sysItemAlias')];
				
				let bindRelation: any = {
					numIid: j.numIid,
					skuId: j.skuId,
					operateType: 'CREATE',
					sysItemAlias: formValues[nameFactory(j.numIid, 'sysItemAlias')],
					sysSkuAlias: formValues[nameFactory(factoryId, 'sysSkuAlias')],
					weight: formValues[nameFactory(factoryId, 'weight')],
					costPrice: formValues[nameFactory(factoryId, 'costPrice')],
					tagPrice: formValues[nameFactory(factoryId, 'tagPrice')],
					classifyId: formValues[nameFactory(factoryId, 'classifyId')],
					market: formValues[nameFactory(factoryId, 'market')],
					supplierName: formValues[nameFactory(factoryId, 'supplierName')],
					stall: formValues[nameFactory(factoryId, 'stall')],
					sellerId: j.sellerId,
					platformType: j.platform,
					userViewVal: userStore.userInfo.userType,
					sysItemLogSourceEnum: 'BHD_EDIT_ITEM',
					
					// 新增的商家编码和规格编码字段
					// varietyOuterId: formValues[nameFactory(j.numIid, 'outerId')] || '',
					// varietySkuOuterId: formValues[nameFactory(factoryId, 'skuOuterId')] || '',
					// isUpLoadOuterId,
					// isUpLoadSkuOuterId,
				};
				
				// 【新增】：参照平台商品管理的免费分销商处理
				if (userStore.userInfo.userType === 'FREE_DISTRIBUTOR') {
					bindRelation.saleUserId = userStore.userInfo.userId;
				}
				
				// 【新增】：参照平台商品管理的自定义属性处理
				const itemCustomAttributeList = userStore?.systemSetting?.itemCustomAttributeDTOList;
				if (userStore?.systemSetting?.itemCustomAttribute === 'OPEN' && itemCustomAttributeList?.length) {
					bindRelation.customAttributesList = itemCustomAttributeList.map((item) => {
						return {
							key: item.key,
							name: item.name,
							value: formValues[nameFactory(factoryId, item.key)]
						};
					});
					bindRelation.customAttributes = JSON.stringify(bindRelation.customAttributesList);
				}
				
				// 参照平台商品管理：处理规格模式下简称修改的情况
				if (isShowSku && oldSysItemAlias !== newSysItemAlias) {
					// 【修正】：需要从原始数据获取完整的 platformItemSkuList
					const originalItem = originData?.list?.find(orig => orig.numIid === j.numIid);
					const platformItemSkuList = originalItem?.platformItemSkuList;
					
					if (platformItemSkuList) {
						platformItemSkuList.forEach(item => {
							const key = `${j.numIid}_${item.skuId}`;
							const _sysItem = item.relationSystemItemList?.[0] || {};
							const { 
								sysSkuAlias = "", 
								weight = "", 
								costPrice = "", 
								tagPrice = "", 
								classifyId = "", 
								market = "", 
								supplierName = "", 
								stall = "", 
								sysItemId, 
								sysSkuId 
							} = _sysItem;
							
							if (j.skuId !== item.skuId && !otherBindObj?.[key]) {
								otherBindObj[`${j.numIid}_${item.skuId}`] = {
									numIid: j.numIid,
									skuId: item.skuId,
									operateType: _sysItem ? 'BIND' : 'CREATE',
									sysItemAlias: formValues[nameFactory(j.numIid, 'sysItemAlias')],
									sysItemId,
									sysSkuId,
									sysSkuAlias,
									weight,
									costPrice,
									tagPrice,
									classifyId,
									market,
									supplierName,
									stall,
									sellerId: j.sellerId,
									platformType: j.platform,
									userViewVal: userStore.userInfo.userType,
									sysItemLogSourceEnum: 'BHD_EDIT_ITEM',
									// 商家编码保持新值
									// varietyOuterId: formValues[nameFactory(j.numIid, 'outerId')] || '',
									// varietySkuOuterId: item.skuOuterId || '',
									// isUpLoadOuterId,
									// isUpLoadSkuOuterId,
								};
							}
						});
					}
				}
				
				// 参照平台商品管理：处理 null 值
				['weight', 'costPrice'].forEach(i => {
					if (bindRelation[i] == null) bindRelation[i] = '';
				});
				
				// 参照平台商品管理：商品模式下为空的内容不做设值处理
				if (!isShowSku) {
					['sysSkuAlias', 'weight', 'costPrice', 'tagPrice', 'classifyId'].forEach((key) => {
						if (bindRelation[key] === '') {
							bindRelation[key] = sysItem?.[key] || '';
						}
					});
				}
				
				// 参照平台商品管理：设置 sysItemId 和 sysSkuId
				if (sysItem) {
					bindRelation = {
						...bindRelation,
						sysItemId: sysItem.sysItemId,
						sysSkuId: sysItem.sysSkuId,
						operateType: 'BIND'
					};
				}
				
				bindedObj[`${j.numIid}_${j.skuId}`] = bindRelation;
				bindRelations.push(bindRelation);
			});
			
			// 参照平台商品管理：添加其他绑定对象
			Object.keys(otherBindObj).forEach((key) => {
				const item = otherBindObj[key];
				const { numIid, skuId } = item;
				if (!bindedObj[`${numIid}_${skuId}`]) { 
					bindRelations.push(item); 
				}
			});
			
			// 参照平台商品管理：处理成本价权限
			if (!hasCostPricePermission) {
				bindRelations.forEach(bindItem => {
					delete bindItem.costPrice;
				});
			}

			await batchUpdateData(bindRelations);
			console.log(`保存成功，共更新 ${bindRelations.length} 条记录`);
			
			onSaveSuccess?.(true);
			handleDrawerClose();
			
		} catch (error) {
			console.error('保存失败:', error);
		} finally {
			setLoading(false);
		}
	};

	// 初始化数据回填
	useEffect(() => {
		if (visible && orderItem) {
			console.log('=== 编辑抽屉打开，开始数据回显 ===');
			console.log('传入的orderItem:', orderItem);
			console.log('传入的skuItem:', skuItem);
			console.log('传入的fieldInfo:', fieldInfo);
			
			// 先确定显示状态
			let targetIsShowSku = false;
			
			// 【新增】：优先根据fieldInfo设置显示状态
			if (fieldInfo) {
				console.log('根据fieldInfo设置显示状态:', {
					hasGoodsFields: fieldInfo.hasGoodsFields,
					hasSkuFields: fieldInfo.hasSkuFields,
					fieldsData: fieldInfo.fieldsData
				});
				
				if (fieldInfo.hasSkuFields) {
					// 有规格字段，勾选显示规格
					targetIsShowSku = true;
					setIsShowSku(true);
					setIsShowSkuPic(false); // 重置规格图片显示
					local.setByUserId('warehouse.platformGoods.isShowSku', true);
				} else if (fieldInfo.hasGoodsFields && !fieldInfo.hasSkuFields) {
					// 只有商品字段，取消勾选显示规格
					targetIsShowSku = false;
					setIsShowSku(false);
					setIsShowSkuPic(false);
					local.setByUserId('warehouse.platformGoods.isShowSku', false);
					local.setByUserId('warehouse.platformGoods.isShowSkuPic', false);
				}
			} else {
				// 没有fieldInfo时使用缓存状态
				const cachedIsShowSku = local.getByUserId("warehouse.platformGoods.isShowSku") || false;
				targetIsShowSku = cachedIsShowSku;
				setIsShowSku(cachedIsShowSku);
				setIsShowSkuPic(local.getByUserId("warehouse.platformGoods.isShowSkuPic") || false);
			}
			
			// 始终设置供应商显示状态（不受fieldInfo影响）
			setIsShowSupplier(local.getByUserId("warehouse.platformGoods.isShowSupplier") || false);
			
			// 根据当前点击的商品信息回填查询条件
			const initialValues: any = {};
			
			if (orderItem.platform && orderItem.sellerNick) {
				initialValues.platformInfo = [{
					platform: orderItem.platform,
					sellerNick: orderItem.sellerNick
				}];
			}
			
			// 根据字段信息智能回填
			if (fieldInfo?.fieldsData) {
				const { fieldsData } = fieldInfo;
				
				// 商品相关字段
				if (fieldsData[BhdLabelName.商品名称]) {
					initialValues.title = fieldsData[BhdLabelName.商品名称];
				}
				if (fieldsData[BhdLabelName.商品ID]) {
					initialValues.numIids = fieldsData[BhdLabelName.商品ID];
				}
				if (fieldsData[BhdLabelName.货品简称]) {
					initialValues.sysItemAlias = fieldsData[BhdLabelName.货品简称];
				}
				if (fieldsData[BhdLabelName.商品商家编码]) {
					initialValues.outerId = fieldsData[BhdLabelName.商品商家编码];
				}
				
				// 规格相关字段
				if (fieldsData[BhdLabelName.规格名称]) {
					initialValues.skuNameList = fieldsData[BhdLabelName.规格名称];
				}
				if (fieldsData[BhdLabelName.规格商家编码]) {
					initialValues.skuOuterId = fieldsData[BhdLabelName.规格商家编码];
				}
				if (fieldsData[BhdLabelName.规格别名]) {
					initialValues.skuContent = fieldsData[BhdLabelName.规格别名];
				}
			} else {
				// 兜底逻辑，保持原有回填方式
				if (orderItem.title) {
					initialValues.title = orderItem.title;
				}
				if (orderItem.numIid) {
					initialValues.numIids = orderItem.numIid;
				}
				if (orderItem.outerId) {
					initialValues.outerId = orderItem.outerId;
				}
				if (skuItem?.skuOuterId) {
					initialValues.skuOuterId = skuItem.skuOuterId;
				}
				if (skuItem?._skuName) {
					initialValues.skuNameList = skuItem._skuName;
				}
			}

			// 先设置表单值
			form.setFieldsValue(initialValues);
			setSearchParams(initialValues);
			
			// 确保 formValues 状态同步
			setTimeout(() => {
				const actualFormValues = form.getFieldsValue();
				setFormValues(actualFormValues);
			}, 0);
			
			// 【关键修正】使用缓存的分页大小进行自动查询，并传递正确的显示状态
			const cachedPageSize = local.get("bhd.editGoodsDrawer.pageSize") || 20;
			fetchData({ ...initialValues, current: 1, pageSize: cachedPageSize }, targetIsShowSku);
		}
	}, [visible, orderItem, skuItem, fieldInfo]);

	// 修复批量编辑处理函数
	const onBatchEditOK = (val: any, type: any) => {
		console.log('%c [ 111 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', val, type);
		const {
			addContent = "", // 添加内容
			addType = addPosMap.覆盖, // 添加类型
			replaceKeyword = "", // 替换关键字
			replaceWith = "", // 替换为
			deleteKeyword = "", // 删除关键字
			filter = [], // 过滤规则
			prefix = "", // 前缀
			suffix = "", // 后缀
		} = val;
		const newFormValues = { ...dataFormSource, ...tableForm.getFieldsValue() };
		console.log('%c [ newFormValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', dataFormSource, newFormValues);

		// 获取选中的行数据
		const selectedRowsData = dataSource.filter(item => item.isChecked);

		selectedRowsData.forEach(row => {
			const factoryId = isShowSku ? row.rowId : row.numIid;
			let name;
			if (type === 'sysItemAlias') {
				name = nameFactory(row.numIid, type);
			} else {
				name = nameFactory(factoryId, type);
			}
			let currentValue = newFormValues[name] || "";

			let newValue = currentValue;
			let content = getNewContent(addContent, row, type);

			// 关键：区分字段类型和addType来源
			if (
				![nameMap.别名, nameMap.简称].includes(type)
			) {
				// 只处理 addPosMap
				switch (addType) {
					case addPosMap.覆盖:
						newValue = content;
						break;
					case addPosMap.现有前:
						newValue = content + currentValue;
						break;
					case addPosMap.现有后:
						newValue = currentValue + content;
						break;
					default:
						newValue = content;
				}
			} else {
				// 其它弹窗类型，处理 AddType
				switch (addType) {
					case 1: // 覆盖
						newValue = content;
						break;
					case 2: // 添加（前缀+原值+后缀）
						newValue = `${prefix}${currentValue}${suffix}`;
						break;
					case 3: // 替换
						if (replaceKeyword) {
							try {
								const reg = new RegExp(replaceKeyword, "g");
								newValue = currentValue.replace(reg, replaceWith);
							} catch (e) {
								newValue = currentValue.split(replaceKeyword).join(replaceWith);
							}
						}
						break;
					case 4: // 删除
						if (deleteKeyword) {
							try {
								const reg = new RegExp(deleteKeyword, "g");
								newValue = currentValue.replace(reg, "");
							} catch (e) {
								newValue = currentValue.split(deleteKeyword).join("");
							}
						}
						break;
					default:
						newValue = content;
				}
			}

			// 过滤规则
			if (Array.isArray(filter)) {
				if (filter.includes("1")) {
					// 过滤【】及中间内容
					newValue = newValue.replace(/【.*?】/g, "");
				}
				if (filter.includes("2")) {
					// 过滤中英文逗号
					newValue = newValue.replace(/,|，/g, "");
				}
			}

			newFormValues[name] = newValue;
		});

		setDataFormSource(newFormValues);
		tableForm.setFieldsValue(newFormValues);
		setBatchEditModalInfo({ visible: false, type: "" });
	};

	// 抽屉关闭时重置数据
	const handleDrawerClose = useCallback(() => {
		// 重置表单
		form.resetFields();
		tableForm.resetFields();
		
		// 重置查询条件和表单值状态
		setSearchParams({});
		setFormValues({});
		
		// 重置表格数据和原始数据
		setDataSource([]);
		setDataFormSource({});
		setOriginData({ list: [], total: 0 });
		
		// 读取缓存的分页大小，保持用户的分页设置
		const cachedPageSize = local.get("bhd.editGoodsDrawer.pageSize") || 20;
		
		// 重置分页，但保留缓存的pageSize
		setPagination({
			current: 1,
			pageSize: cachedPageSize,
			total: 0
		});
		
		// 重置批量编辑状态
		setBatchEditModalInfo({ visible: false, type: "" });
		
		// 清除选中状态，确保下次打开抽屉时是全新状态
		setSelectedRowKeys([]);
		
		// 调用原来的关闭函数
		onClose();
	}, [form, tableForm, onClose]);

	// 修改现有的勾选逻辑，确保与selectedRowKeys同步
	const handleRowClick = (e: React.MouseEvent, record: any) => {
		// 防止点击行内元素时触发行选择
		const target = e.target as HTMLElement;
		const tagName = target.tagName.toLowerCase();
		
		// 排除点击输入框、按钮、复选框等交互元素
		if (['input', 'button', 'a', 'svg', 'path', 'span'].includes(tagName) 
			|| target.closest('.ant-checkbox') 
			|| target.closest('.ant-input') 
			|| target.closest('.ant-btn')
			|| target.closest('.ant-form-item')
			|| target.closest('.ant-popover')
			|| target.closest('.ant-tooltip')) {
			return;
		}

		// 切换选中状态
		const isCurrentlySelected = selectedRowKeys.includes(record.rowId);
		
		setDataSource(prev => {
			const prevIndex = prev.findIndex(d => d.rowId === record.rowId);
			if (prevIndex > -1) {
				prev[prevIndex].isChecked = !isCurrentlySelected;
			}
			return [...prev];
		});

		// 更新selectedRowKeys
		setSelectedRowKeys(prev => {
			if (isCurrentlySelected) {
				return prev.filter(key => key !== record.rowId);
			} else {
				return [...prev, record.rowId];
			}
		});
	};

	// 确保selectedRowKeys与dataSource保持同步
	useEffect(() => {
		const checkedRowIds = dataSource.filter(item => item.isChecked).map(item => item.rowId);
		setSelectedRowKeys(checkedRowIds);
	}, [dataSource]);

	useEffect(() => {
		userStore.getUserSetting().then(res => {
			setIsKg(res?.weightUnit == weightUnit.显示kg);
		});
	}, []);

	// 使用 ref 来保存最新的 handleDrawerClose 函数，避免依赖问题
	const handleDrawerCloseRef = useRef(handleDrawerClose);
	handleDrawerCloseRef.current = handleDrawerClose;

	// 监听路由变化，自动关闭抽屉
	useEffect(() => {
		if (!visible) return;

		const unlisten = history.listen((location, action) => {
			// 当路由发生变化时，自动关闭抽屉
			console.log('路由变化，自动关闭编辑商品抽屉');
			// 使用 ref 来调用最新的函数，避免循环依赖
			handleDrawerCloseRef.current();
		});

		// 清理监听器
		return () => {
			if (unlisten) {
				unlisten();
			}
		};
	}, [visible]); // 只依赖 visible

	// 跳到对应的系统设置
	const handleNavigateToSettings = () => {
		// handleDrawerClose(); // 通过路由监听关闭
		setTimeout(() => {
			history.push('/settings/system?introName=tradeGoodsInfoUpdateConfig');
		}, 100);
	};

	return (
		<Drawer
			title={ (
				<div className="r-flex r-ai-c r-jc-sb">
					<div className={ s.deawerTitle }>
						<span className="r-bold">编辑商品资料</span>
					</div>
					<div onClick={ handleDrawerClose } className={ s.logClose }>
						<CloseOutlined size={ 14 } className={ s.close } />
					</div>
				</div>
			) }
			placement="right"
			size="large"
			closable={ false }
			keyboard={ false }
			maskClosable
			visible={ visible }
			onClose={ handleDrawerClose }
			className={ s.editGoodsDrawer }
			destroyOnClose
			footer={ (
				<div className="r-flex r-jc-sb r-ai-c">
					<Space>
						<Button onClick={ handleDrawerClose }>取 消</Button>
						<Button type="primary" loading={ loading } onClick={ handleSave } data-point={ Pointer.报表_备货单_编辑商品_保存操作_点击确认 }>
							更新商品资料
						</Button>

						{/* 是否上传商家编码和规格编码 - 暂时注释 */}
						{/* <div className="r-flex r-ai-c r-ml-8">
							<Checkbox 
								checked={ isUpLoadOuterId } 
								onChange={ (e) => {
									setIsUpLoadOuterId(e.target.checked); 
									if (e.target.checked) sendPoint(Pointer.报表_备货单_编辑商品_商家编码上传_勾选);
								} }
							>
								是否上传商家编码
							</Checkbox>
							<Checkbox 
								checked={ isUpLoadSkuOuterId } 
								onChange={ (e) => {
									setIsUpLoadSkuOuterId(e.target.checked);
									if (e.target.checked) sendPoint(Pointer.报表_备货单_编辑商品_商家编码上传_勾选);
								} }
							>
								是否上传规格编码
							</Checkbox>
                            
							<Tips />
						</div> */}
						
					</Space>

					<Space>
						<Pagination
							current={ pagination.current }
							pageSize={ pagination.pageSize }
							total={ pagination.total }
							showSizeChanger
							showQuickJumper
							pageSizeOptions={ ['10', '20', '50', '100', '200', '500'] }
							showTotal={ (total) => `共${total}条` }
							onChange={ handlePaginationChange }
							// onShowSizeChange={ (current, size) => handlePaginationChange(1, size) }
							size="small"
						/>
					</Space>
				</div>
			) }
		>

			{/* Alert提示信息 */}
			<Alert
				message={ (
					<div>
						<div>1. 编辑后会修改平台商品档案中的商品信息并更新至订单{/* ，支持修改商家编码/规格编码会更新平台 <Tips /> */}</div>
						<div>2. 默认更新待发货订单，已打印、先发货订单可通过<span style={ { color: '#1890ff', cursor: 'pointer' } } onClick={ handleNavigateToSettings }>订单商品更新设置</span>进行配置</div>
					</div>
				) }
				type="warning"
				showIcon={ false }
				className="r-mb-16"
			/>
      
			{/* 查询条件 */}
			<Form
				form={ form }
				layout="inline"
				className="r-mb-16"
				size="small"
				onValuesChange={ handleFormValuesChange }
			>
				<div className="r-flex r-fw-w" style={ { gap: '8px 0' } }>
					{FormFieldList.map((item, index) => (
						<Form.Item key={ index } name={ item.name } style={ { marginBottom: 0 } }>
							{item.children}
						</Form.Item>
					))}

					<Space>
						<Button type="primary" onClick={ handleSearch } data-point={ Pointer.报表_备货单_编辑商品_商品查询_点击 }>
							查询
						</Button>
						<Button onClick={ handleReset }>
							重置
						</Button>
						<Checkbox checked={ isShowSku } onChange={ () => onCheckShowSku() } className="r-ml-8 r-wb-ka">显示规格</Checkbox>
						<Checkbox disabled={ !isShowSku } checked={ isShowSkuPic } onChange={ () => onCheckShowSkuPic() } className="r-wb-ka">显示规格图片</Checkbox>
						{(!isDistributorAccount && !isSupplierUserAndStock) ? (
							<Checkbox checked={ isShowSupplier } onChange={ () => onCheckShowSupplier() } className="r-wb-ka">
								<span style={ { whiteSpace: 'nowrap' } }>显示档口/供应商</span>
							</Checkbox>
						) : ''}
					</Space>
				</div>
				

				
			</Form>

			{/* 表格 */}
			<Form form={ tableForm } onFieldsChange={ onFieldsChange }>
				<BaseTable
					ref={ tableRef }
					dataSource={ dataSource }
					columns={ columns }
					loading={ loading }
					rowKey="rowId"
					pagination={ false }
					size="small"
					headerColSetId="bhd_EditGoodsDrawer"
					id="bhd_EditGoodsDrawer"
					scrollExtraHeight={ 0 }
					bordered
					innerTableStyle={ { padding: 0 } }
					rowClassName={ (record: any) => {
						// 使用CSS Modules的局部样式类名
						return selectedRowKeys.includes(record.rowId) ? s.selectedRow : '';
					} }
					onRow={ (record) => ({
						onClick: (e) => handleRowClick(e, record),
						style: {
							cursor: 'pointer'
						}
					}) }
				/>
			</Form>

			{/* 批量编辑弹窗 */}
			<BatchEditModal
				visible={ batchEditModalInfo.visible }
				type={ batchEditModalInfo.type }
				onCancel={ () => setBatchEditModalInfo({ visible: false, type: "" }) }
				onOk={ onBatchEditOK }
			/>
		</Drawer>
	);
});

export default EditGoodsDrawer;