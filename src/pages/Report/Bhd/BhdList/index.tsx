import Layout from "antd/lib/layout/layout";
import React, { useCallback, useEffect, useState, useMemo } from "react";
import { useRequest, useToggle } from "ahooks";
import { Button, Space, Checkbox, Modal, Progress, Form, Input, Tooltip } from "antd";
import { observer } from "mobx-react";
import cs from 'classnames';
import _ from 'lodash';
import s from './index.module.scss';
import BhdListSearchContainer from "./components/SearchContainer";
import BhdOrderList from "./components/BhdOrderList";
import { ReportQueryBhdItemListRequest, TradeGetBhdGenerateResultResponse } from "@/types/schemas/report/bhd/bhdList";
import { TradeGetBhdGenerateResultApi, TradeQueryBhdItemListApi } from "@/apis/report/bhd/bhdList";
import BottomFixedContainer from "@/components/BottomFixedContainer";
import BhdPrintBtn from "./components/BottomAction/BhdPrintBtn";
import { local } from "@/libs/db";
import { StockVersionEnum, calcSupplierCount, getStockVersion } from "./utils";
import FilterKeyWordModal from "@/pages/Trade/components/ListItem/components/FilterKeyWordModal";
import BhdDownBtn from "./components/BottomAction/BhdDownBtn";
import tradeSetStore from "@/stores/trade/tradeSet";
import bhdStore from "@/stores/report/Bhd";
import WarehouseStore from '@/stores/warehouse';
import BhdGeneratePurchaseOrderBtn from './components/BottomAction/BhdGeneratePurchaseOrderBtn';
import { useStores } from "@/stores/tool";
import PrintCenter from '@/print/index';
import message from "@/components/message";
import BhdPrintTakeLabelBtn from "./components/BottomAction/BhdPrintTakeLabelBtn";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import userStore from "@/stores/user";
import CheckLodop from './components/CheckLodop';
import { OptTypeEnum } from "./contants";
import { BhdShowType } from "../BhdTableSet/contants";
import BhdBatchDeleteBtn from "./components/BottomAction/BhdBatchDeleteBtn";
import { BQ_PRINT_TYPE } from "@/pages/Trade/constants";
import Icon from "@/components/Icon";
import BhdPrintGoodsLabelBtn from './components/BottomAction/BhdPrintGoodsLabelBtn';

const BhdList = observer((props) => {
	const [bhdData, setBhdData] = useState<TradeGetBhdGenerateResultResponse['data']['result']>();
	const { exNumberRecordPagination } = bhdStore;
	// const [checkList, setCheckList] = useState([]);
	const [printSet, setPrintSet] = useState<string[]>();
	const [isStock, setIsStock] = useState<StockVersionEnum>(null);
	const [progressObj, setprogressObj] = useState({
		isShow: false,
		progress: 0,
	});
	const [isStallBhd, setIsStallBhd] = useState(false);
	const storeBhdSet: typeof bhdStore = useStores('bhdStore');
	const [searchParams, setSearchParams] = useState({} as any);
	const { showType } = storeBhdSet;
	const [isShowControlGuide, setIsShowControlGuide] = useState(false);
	const [form] = Form.useForm();
	const setIsDisabledBhdGeneratePurchaseOrderBtn = bhdStore.setIsDisabledBhdGeneratePurchaseOrderBtn;

	const [searchErrorMsg, setSearchErrorMsg] = useState('');
	// 添加滚动状态
	const [scrollState, setScrollState] = useState({
		canScrollUp: false,
		canScrollDown: false,
		showButtons: false
	});

	const { getStockStorageInfo } = WarehouseStore;

	// 新增：保存变更状态检查函数
	const [checkUpdateStatusFn, setCheckUpdateStatusFn] = useState<((action: string) => boolean) | null>(null);

	// 接收来自子组件的变更状态检查函数
	const handleCheckUpdateStatus = useCallback((checkFn: (action: string) => boolean) => {
		setCheckUpdateStatusFn(() => checkFn);
	}, []);

	const searchResultLoop = () => {
		const params: any = {};
		if (searchParams.includeYdNoRecord) params.includeYdNoRecord = true;
		TradeGetBhdGenerateResultApi(params).then(async(res) => {
			if (+res?.progress === 1 && res?.success) {
				setprogressObj({
					isShow: false,
					progress: 1,
				});
				await tradeSetStore.getFilterWord();
				setBhdRes(res);
			} else if (!res.errorMsg) {
				setprogressObj({
					isShow: true,
					progress: +res?.progress,
				});
				setTimeout(() => {
					searchResultLoop();
				}, 3000);
			} else if (res?.errorMsg) {
				message.error(res?.errorMsg);
				setSearchErrorMsg(res?.errorMsg);
				setprogressObj({
					isShow: false,
					progress: 0,
				});
			}
		}).catch((err) => {
			setprogressObj({
				isShow: false,
				progress: 0,
			});
			setSearchErrorMsg(err.errorMessage);
		});
	};

	const setBhdRes = (res) => {
		// 规格视角特殊处理
		if ([BhdShowType.规格商品, BhdShowType.货品规格].includes(res.result?.queryVision)) {
			if (res.result?.supplierGroupSkus?.length > 0) {
				// 规格视角且开启了供应商的排序的
				const _data = res.result.supplierGroupSkus.map((item) => {
					const { market, stall, supplierName, supplierId } = item;
					item.skus = item.skus.map((sku) => ({ ...sku, skuCounts: res.result?.queryVision === BhdShowType.规格商品 ? sku.itemCounts : sku.skuCounts, ...(res.result?.queryVision === BhdShowType.规格商品 ? {} : (sku.skuCounts[0] || {})) }));
					return { mallStallSupplierStr: item.mallStallSupplierStr, items: item.skus, market, stall, supplierName, supplierId };
				});
				res.result.supplierGroupItems = _data.map((item) => {
					return calcSupplierCount(item);
				});
			} else if (res.result?.skus?.length > 0) {
				const _data = res.result.skus.map((item) => ({ ...item, skuCounts: res.result?.queryVision === BhdShowType.规格商品 ? item.itemCounts : item.skuCounts, ...(res.result?.queryVision === BhdShowType.规格商品 ? {} : (item.skuCounts[0] || {})) }));
				res.result.supplierGroupItems = [{ items: _data, mallStallSupplierStr: "" }];
			}
		} else if (res.result?.items?.length > 0) {
			// items 如果有 就是没有开启按照市场-档口-供应商排序的
			res.result.supplierGroupItems = [
				{
					items: res.result.items,
					mallStallSupplierStr: ""
				}
			];
		} else {
			// 系统货品和普通商品视角 且开启了按照市场档口供应商排序
			res.result.supplierGroupItems = res.result.supplierGroupItems.map((item) => {
				return calcSupplierCount(item);
			});
		}

		// 总的实付金额、成本价、数量由后端返回，删除添加等操作后的变动由前端处理
	
		setSearchErrorMsg('');
		setBhdData(res.result);
		storeBhdSet.setBhdData(res.result.supplierGroupItems);
	};

	// 新增：保存最后一次查询参数，用于重新查询
	const [lastSearchParams, setLastSearchParams] = useState<any>(null);

	const { run: queryBhdList, loading } = useRequest(TradeQueryBhdItemListApi, {
		manual: true,
		onSuccess: async(res, params) => {
			searchResultLoop();
		},
		onError: err => {
			setprogressObj({
				isShow: false,
				progress: 0,
			});
			setSearchErrorMsg(err?.errorMessage);
		}
	});

	// 修改：保持原有的handleSearch逻辑，只添加保存查询参数
	const handleSearch = useCallback((params:ReportQueryBhdItemListRequest) => {
		setBhdData(null);
		setprogressObj({
			isShow: true,
			progress: 0,
		});
		setSearchErrorMsg('');
		params.countCost = false;
		params.countSalableItemStock = false;
		storeBhdSet.bhdSetJs?.colsSet?.forEach(item => {
			if (item.datas.indexOf('cbzj') > -1 || item.datas.indexOf('cbj') > -1) {
				params.countCost = true;
			}
			if (item.datas.indexOf('stockNum') > -1) {
				params.countSalableItemStock = true;
			}
		});
		
		// 新增：保存查询参数用于重新查询
		setLastSearchParams(params);
		
		setSearchParams(params);
		queryBhdList(params);
		// setBhdCheckList([]);
		sessionStorage.setItem('bhdCheckList', JSON.stringify([]));
		setIsDisabledBhdGeneratePurchaseOrderBtn(true);
	}, [queryBhdList, storeBhdSet.bhdSetJs?.colsSet]);

	// 新增：重新查询函数
	const handleRefreshData = useCallback(() => {
		if (lastSearchParams) {
			console.log('重新查询备货单数据...');
			// 重新执行查询，复用原有的handleSearch逻辑
			handleSearch(lastSearchParams);
		}
	}, [lastSearchParams, handleSearch]);

	useEffect(() => {
		if (storeBhdSet.bhdData === null) {
			setBhdData(null);
		}
	}, [storeBhdSet.bhdData]);

	useEffect(() => {
		if (props.location.pathname.indexOf('stall') > -1) {
			setIsStallBhd(true);
		}
		const printSet = local.get('report.bhd.printSet');
		if (printSet) {
			setPrintSet(printSet);
		}

		setprogressObj({
			isShow: true,
			progress: 0,
		});

		TradeGetBhdGenerateResultApi({}).then(res => {
			if (res?.success && +res?.progress === 1) {
				setprogressObj({
					isShow: false,
					progress: 1,
				});
				Modal.confirm({
					title: '提示',
					content: '有未完成的备货单查询任务，是否继续',
					onOk: async() => {
						await tradeSetStore.getFilterWord();
						setBhdRes(res);
					}
				});
			} else if (res?.success && +res?.progress < 1) {
				message.info('查询任务还未完成，请稍等');
				setprogressObj({
					isShow: true,
					progress: +res.progress,
				});
				setTimeout(() => {
					searchResultLoop();
				}, 3000);
			} else if (res?.errorMsg) {
				setprogressObj({
					isShow: false,
					progress: 1,
				});
				message.error(res?.errorMsg);
				setSearchErrorMsg(res?.errorMsg);
			} else {
				setprogressObj({
					isShow: false,
					progress: 1,
				});
			}
		}).catch((err) => {
			setprogressObj({
				isShow: false,
				progress: 1,
			});
			setSearchErrorMsg(err?.errorMessage);
		});
		bhdStore.queryExNumberRecord({ pageNo: 1, pageSize: exNumberRecordPagination.pageSize });
	}, []);

	useEffect(() => {
		getStockVersion().then(res => {
			setIsStock(res);
		});
		tradeSetStore.getFilterWord();
		// 获取用户配置，用于重量筛选条件
		userStore.getUserSetting();
		bhdStore.getBhdSet();
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.标签);
		getStockStorageInfo();
	}, []);

	const handleChangePrintSet = useCallback((value:string[]) => {
		setPrintSet(value);
		local.set('report.bhd.printSet', value);
	}, []);

	const printConfig = {
		hasView: printSet?.includes('hasView'),
		printIsHideImg: printSet?.includes('printIsHideImg'),
		printIsHideMemo: printSet?.includes('printIsHideMemo'),
		printHasTime: printSet?.includes('printHasTime'),
	};

	const toGeneratedLabel = () => {
		sendPoint(Pointer.报表_备货单_查看已生成拿货标签);
		window.open(`${location.origin}${location.pathname}${location.search}&printStatus=ALREADY_PRINT#/trade/goodsTag`);
	};

	// 将原来的函数改为使用 useCallback 缓存
	const renderFastScrollButtons = useCallback(() => {
		if (!scrollState.showButtons) {
			return null;
		}

		// 缓存DOM查询结果，避免重复查询
		const getContainer = () => {
			return document.querySelector(`.${s.tableDataContainer}`) 
				   || document.getElementById('main');
		};

		// 优化后的滚动处理函数
		const handleScroll = (isTop: boolean) => (e: React.MouseEvent) => {
			e.preventDefault();
			e.stopPropagation();
			
			const container = getContainer();
			if (container) {
				container.scrollTo({ 
					top: isTop ? 0 : container.scrollHeight, 
					behavior: 'smooth' 
				});
			} else {
				window.scrollTo({ 
					top: isTop ? 0 : document.body.scrollHeight, 
					behavior: 'smooth' 
				});
			}
		};

		return (
			<div className="bhd-fast-scroll">
				<div 
					className={ cs("r-mb-4 r-pointer bhd-scroll-btn", { 'bhdScrollBtn': scrollState.canScrollUp }) }
					onClick={ handleScroll(true) } // 置顶
					title="置顶"
				>
					<Icon style={ { fontSize: 30, color: 'rgba(0,0,0,0.1)' } } type="daodingbu" />
				</div>
				
				<div 
					className={ cs("r-pointer bhd-scroll-btn", { 'bhdScrollBtn': scrollState.canScrollDown }) }
					onClick={ handleScroll(false) } // 置底
					title="置底"
				>
					<Icon style={ { fontSize: 30, color: 'rgba(0,0,0,0.1)' } } type="daodibu" />
				</div>
			</div>
		);
	}, [scrollState]); // 只有 scrollState 变化时才重新创建

	// 检查滚动状态
	const checkScrollState = useCallback(() => {
		const scrollContainer = document.querySelector(`.${s.tableDataContainer}`);
		const mainContainer = document.getElementById('main');
		
		// 优先检查tableDataContainer，如果没有则检查main容器
		const container = scrollContainer || mainContainer;
		
		if (!container) {
			setScrollState({
				canScrollUp: false,
				canScrollDown: false,
				showButtons: false
			});
			return;
		}

		const scrollTop = container.scrollTop;
		const scrollHeight = container.scrollHeight;
		const clientHeight = container.clientHeight;
		
		// 判断是否可以向上滚动（距离顶部超过100px才显示）
		const canScrollUp = scrollTop > 100;
		
		// 判断是否可以向下滚动（距离底部超过100px才显示）
		const canScrollDown = scrollTop < scrollHeight - clientHeight - 100;
		
		// 只有当内容高度超过容器高度时才显示按钮
		const hasScrollableContent = scrollHeight > clientHeight + 50;
		
		// 只有当有数据且内容可滚动时才显示按钮
		const showButtons = bhdData && hasScrollableContent && (canScrollUp || canScrollDown);

		setScrollState({
			canScrollUp,
			canScrollDown,
			showButtons
		});
	}, [bhdData]);

	// 使用 useMemo 确保节流函数引用一致
	const throttledCheckScrollState = useMemo(
		() => _.throttle(checkScrollState, 100, { 
			leading: true, // 立即执行第一次
			trailing: true // 确保最后一次也会执行
		}),
		[checkScrollState]
	);

	// 使用 useMemo 确保防抖函数引用一致
	const debouncedCheckScrollState = useMemo(
		() => _.debounce(checkScrollState, 150, {
			leading: false, // 不立即执行
			trailing: true // 确保最后一次会执行
		}),
		[checkScrollState]
	);

	// 监听滚动事件
	useEffect(() => {
		const scrollContainer = document.querySelector(`.${s.tableDataContainer}`);
		const mainContainer = document.getElementById('main');
		const container = scrollContainer || mainContainer;

		if (!container) return;

		// 初始检查（不需要节流）
		checkScrollState();

		// 滚动事件使用节流
		const handleScroll = throttledCheckScrollState;

		// 窗口大小变化使用防抖
		const handleResize = debouncedCheckScrollState;

		container.addEventListener('scroll', handleScroll, { passive: true });
		window.addEventListener('resize', handleResize);

		return () => {
			container.removeEventListener('scroll', handleScroll);
			window.removeEventListener('resize', handleResize);
			// 清理节流和防抖
			throttledCheckScrollState.cancel();
			debouncedCheckScrollState.cancel();
		};
	}, [checkScrollState, throttledCheckScrollState, debouncedCheckScrollState]);

	// 当数据变化时重新检查滚动状态（延迟执行，等待DOM更新）
	useEffect(() => {
		const timer = setTimeout(() => {
			checkScrollState();
		}, 100);

		return () => clearTimeout(timer);
	}, [bhdData, checkScrollState]);
	
	return (
		<div className={ s.tableDataContainer }>
			<Layout className="kdzs-section" style={ { margin: '0' } }>
				<BhdListSearchContainer isStallBhd={ isStallBhd } handleSearch={ handleSearch } loading={ loading || progressObj.isShow } />
			</Layout>

			<Layout className="kdzs-section" style={ { padding: '0px 0 80px 0', display: 'block', margin: '0px' } }>
				{searchErrorMsg && <div className="r-flex r-jc-c r-ai-c r-fd-c" style={ { marginTop: 100 } }>{searchErrorMsg}</div>}
				{progressObj.isShow && (
					<div className="r-flex r-jc-c r-ai-c r-fd-c" style={ { paddingTop: 100, background: '#fff' } }>
						<Progress style={ { width: '200px' } } percent={ Math.floor(progressObj.progress * 100) } strokeColor="#F5821F" status="active" />
						正在统计订单商品，请稍后...
					</div>
				)}
				{
					bhdData ? (
						<div className={ s.bhdContentWarp }>
							<BhdOrderList 
								bhdData={ bhdData } 
								printSet={ printSet }
								onPrintSetChange={ handleChangePrintSet }
								isStallBhd={ isStallBhd }
								isStock={ isStock }
								onRefreshData={ handleRefreshData }
								onCheckUpdateStatus={ handleCheckUpdateStatus }
							/>

							{(isStock === 2 || isStock === 1) && isStallBhd && (
								<div className="r-flex r-ai-c r-jc-c r-mt-20">
									注意：生成标签数量会根据查询结果统计，在表格内修改数量将不生效
								</div>
							)}
						</div>
					) : ''
				}
			</Layout>

			{bhdData ? (
				<BottomFixedContainer>
					<Space size={ 20 }>
						<BhdPrintBtn 
							{ ...printConfig } 
							checkUpdateStatus={ checkUpdateStatusFn }
						/>
						<BhdDownBtn 
							{ ...printConfig } 
							checkUpdateStatus={ checkUpdateStatusFn }
						/>
						<BhdBatchDeleteBtn
							onOk={ res => setBhdData(prev => ({
								...prev,
								...res,
							})) }
							checkUpdateStatus={ checkUpdateStatusFn }
						/>
						
						{[StockVersionEnum.库存版未开启扣库存, StockVersionEnum.库存版开启扣库存].includes(isStock) ? (
							<BhdGeneratePurchaseOrderBtn 
								checkUpdateStatus={ checkUpdateStatusFn }
							/>
						) : ''}

						{/* 打印商品备货标签按钮，只在库存版显示 */}
						{[StockVersionEnum.库存版未开启扣库存, StockVersionEnum.库存版开启扣库存].includes(isStock) && !isStallBhd && (
							<BhdPrintGoodsLabelBtn checkUpdateStatus={ checkUpdateStatusFn } />
						)}
	
						{/* 无库存版 和 未开启扣库存 和 开启扣库存 显示 生成拿货小标签 */}
						{
							[StockVersionEnum.库存版未开启扣库存, StockVersionEnum.库存版开启扣库存, StockVersionEnum.无库存版].includes(isStock) && isStallBhd && (
								<>
									<BhdPrintTakeLabelBtn 
										setIsShowControlGuide={ setIsShowControlGuide } 
										type={ OptTypeEnum.仅生成标签 } 
										searchParams={ searchParams }
										checkUpdateStatus={ checkUpdateStatusFn }
									/>
									<BhdPrintTakeLabelBtn 
										setIsShowControlGuide={ setIsShowControlGuide } 
										type={ OptTypeEnum.打印拿货小标签 } 
										searchParams={ searchParams }
										checkUpdateStatus={ checkUpdateStatusFn }
									/>
									<Tooltip title={ storeBhdSet.bhdSetJs?.baseSet?.fastEditConfig ? "关闭快捷编辑模式后生效" : '' }>
										<Button 
											type="primary" 
											size="large" 
											onClick={ () => {
												// 检查变更状态
												if (checkUpdateStatusFn && checkUpdateStatusFn('查看已生成拿货标签')) {
													return;
												}
												toGeneratedLabel();
											} } 
											disabled={ storeBhdSet.bhdSetJs?.baseSet?.fastEditConfig }
										>
											查看已生成拿货标签
										</Button>
									</Tooltip>
								</>
							)
						}

						
					</Space>
				</BottomFixedContainer>
			) : ''}

			{/* 添加置顶置底按钮 */}
			{renderFastScrollButtons()}

			<div id="bhd-send-print-html" style={ { display: 'none' } } />
			<link
				rel="stylesheet"
				type="text/css"
				href="/static/bhdPrint.css"
			/>
			<CheckLodop isShowControlGuide={ isShowControlGuide } setIsShowControlGuide={ setIsShowControlGuide } />
		</div>
	);
});

export default BhdList;
