
.bhdContentWarp{
	width: 100%;

	.bhdSearchContent{
		width: 100%;
		padding: 16px;
		box-sizing: border-box;
		background-color: #fff;
		border-top: 8px solid rgba(0, 0, 0, 0.06);
		border-bottom: 8px solid rgba(0, 0, 0, 0.06);
	}

	.bhd-topButton-warp{
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		padding: 16px;
		box-sizing: border-box;
		background-color: #fff;

		&.bhd-topButton-fixed {
			position: sticky;
			top: 0px;
			left: 0;
			z-index: 9;
		}

		.ant-btn-div{
			position: relative;
			display: inline-block;
			font-weight: 400;
			white-space: nowrap;
			text-align: center;
			background-image: none;
			border: 1px solid transparent;
			box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
			cursor: pointer;
			transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
			user-select: none;
			touch-action: manipulation;
			height: 32px;
			padding: 0px 15px;
			font-size: 14px;
			border-radius: 2px;
			color: rgba(0, 0, 0, 0.85);
			border-color: #d9d9d9;
			background: #fff;
			display: flex;
			align-items: center;
			justify-content: center;

			&:hover{
				color: #ff9d2e;
    			border-color: #ff9d2e;
			}

			.newIcon {
				background: #ff0000;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 31px;
				height: 16px;
				line-height: 12px;
				font-size: 12px;
				font-weight: 500;
				border-radius: 8px;
				color: #fff;
				text-align: center;
				position: absolute;
				z-index: 1;
				top: -8px;
				left: -16px;
			}
		}
		
	}

	.bhdPrintContentWarp{
		border-top: 8px solid rgba(0, 0, 0, 0.06);
	}

	.sort-disabled{
		background-color: #f5f5f5;
		color: #999;
		border-color: #d9d9d9;
	}

}

.bhdContent {
	min-width: 790px;
	margin: 16px auto 0;
	max-width: 90vw;
	// overflow-x: auto;
}

.choosePrinter {
	width: 100%;
	height: 250px;
	padding: 50px;
	margin: 0 auto;
}

.unfoldOptWrap {
	width: 10%;
	min-width: 60px;
	text-align: right;
}

.bhdTableCheckTooltip {
	transform: translate(-50%, -50%);
}

.image-container {
	border-radius: 6px;
}

.image-container:hover {
	box-shadow: 0 0 7px #dadadacc;
}

.hot-label-modal {
	:global {
		.ant-modal-body {
			padding: 8px 0px;
		}
	}

	.hot-label-modal-bottom {
		display: flex;
		justify-content: center;
		padding-bottom: 10px;
	}
}



.bhd-table-fixed {
	position: sticky;
	top: 64px;
	left: 0;
	z-index: 9;
	background-color: #fff;
	// box-shadow: 0px -2px 0px 0px rgba(0, 0, 0, 0.1);
}

.tableDataContainer {
	position: relative;
	max-height: calc(100vh - 220px);
	overflow: auto;

	

	:global{
		.bhd-fast-scroll {
			position: fixed;
			bottom: 120px;
			right: 20px;
			z-index: 1000;
			display: flex;
			flex-direction: column;

			.bhd-scroll-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				cursor: pointer;
				visibility: hidden;

				&.bhdScrollBtn{
					visibility: visible;
				}
	
				&:hover {
					transform: translateY(-2px);
				}
	
				&:active {
					transform: translateY(0);
				}
			}
		}
	}
}

.underlineText {
	text-decoration: underline;
	margin: 0 4px;
	font-weight: 600;
}

.addSupplierItemModal {
	:global {
		.ant-modal-footer .ant-btn {
			padding: 0 12px;
		}
	}
}


.bhdGoodsDataSwitchInfo{
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);

	border-width: 0px 0px 1px 0px;
	border-style: solid;
	border-color: rgba(0, 0, 0, 0.06);
	padding-bottom: 8px;

	span{
		font-weight: 500;
	}
}

// 新增商品勾选框相关样式
.newItemCheckbox {
	// position: absolute;
	// top: 50%; 
	// left: 50%; 
	// transform: translate(-50%, -50%);
	padding: 4px 0;

	.newItemLabel {
		width: 20px;
		height: 20px;
		border-radius: 2px;
		background: #FFFFFF;
		border: 1px solid #FD8204;
		box-sizing: border-box;
		font-size: 12px;
		color: #FD8204;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 6px;
	}
}
  
