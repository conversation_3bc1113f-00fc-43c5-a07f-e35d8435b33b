import dayjs from "dayjs";
import { toJS } from "mobx";
import { cloneDeep } from "lodash";
import { downloadExcelByBlob } from "@/utils";
import userStore from "@/stores/user";
import bhdStore from "@/stores/report/Bhd";
import { SystemOperateLogUploadApi } from '@/apis/user';

export const bhdDownLoad = async(config: {
	printIsHideImg: boolean,
	printIsHideMemo: boolean,
	printHasTime: boolean
}) => {
	const excelName = `备货单${dayjs().format('YYYY-MM-DD')}.xlsx`;
	const {
		printHtml,
	} = getBhdPrintHtml(config, 'downLoad');
	downExcel({
		downHtml: printHtml,
		fileName: excelName
	});

	SystemOperateLogUploadApi({ operateLogType: 'BHD_EXPORT', operateResult: true });
};

const addMsoNumberFormat = (htmlString) => {
	const tempDiv = document.createElement('div');
	tempDiv.innerHTML = htmlString;

	const elements = tempDiv.querySelectorAll('.msoNumberFormat');
	elements.forEach(element => {
		const currentStyle = element.getAttribute('style') || '';
		element.setAttribute('style', `${currentStyle} MSO-NUMBER-FORMAT:"\\@";`);
	});

	return tempDiv.innerHTML;
};

export const getBhdPrintHtml = (config: {
	printIsHideImg: boolean,
	printIsHideMemo: boolean,
	printHasTime: boolean
}, type = 'print') => {
	const $oldPrintDom = document.getElementById('bhdPrintContent');
	const $printDom = document.getElementById('bhd-send-print-html');
	const isPrint = type === 'print';
	const { bhdData, isDisabledBhdGeneratePurchaseOrderBtn } = bhdStore;

	const _html = addMsoNumberFormat($oldPrintDom.innerHTML);
	$printDom.innerHTML = `
		<div class="bhd-printing-html">
			<link
				rel="stylesheet"
				type="text/css"
				href="${location.origin}/static/bhdPrint.css"
			/>
			${_html}
		</div>`;

	// 有勾选项，打印和下载的时候仅处理勾选的备货单
	if (!isDisabledBhdGeneratePurchaseOrderBtn) {
		console.log('%c [ bhdData ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', toJS(bhdData));
		// 找到要删除的未勾选的数据 tr.orderTr、tr.bhd-tb-msg-box、tr.mallStallTr
		let removeMap = new Set();
		let mallTotalMap = new Map();
		let totalCost: number = 0;
		let totalCount: number = 0;
		let totalPaymentCount: number = 0;
		let newData = cloneDeep(toJS(bhdData));
		newData.forEach((mall, mallIndex) => {
			const { items = [] } = mall;
			let supplierPaymentCount: number = 0;
			let supplierCount: number = 0;
			let supplierCost: number = 0;
			let hasChecked: boolean = false;

			items.forEach((order, orderIndex) => {
				const { isChecked, skuCounts = [], _hasMessage = false } = order;
				if (isChecked) {
					hasChecked = true;
					skuCounts?.forEach(skuItem => {
						const { skuPaymentCount = 0, count = 0, cost = 0 } = skuItem;
						supplierPaymentCount = getAddNums(skuPaymentCount, supplierPaymentCount);
						supplierCount = getAddNums(count, supplierCount);
						supplierCost = getMultiNums(count, cost, supplierCost);
					});
				} else {
					removeMap.add(`tr.orderTr[data-mall]=${mallIndex}[data-order]=${orderIndex}`);
					if (_hasMessage) {
						removeMap.add(`tr.bhd-tb-msg-box[data-mall]=${mallIndex}[data-order]=${orderIndex}`);
					}
				}
			});

			if (hasChecked) {
				totalCost += +supplierCost;
				totalCount += +supplierCount;
				totalPaymentCount += +supplierPaymentCount;

				mallTotalMap.set(`tr.mallStallTr[data-mall]=${mallIndex}`, { supplierPaymentCount, supplierCount, supplierCost });
			} else {
				removeMap.add(`tr.mallStallTr[data-mall]=${mallIndex}`);
			}

		});
		// console.log('%c [ total ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', totalCost, totalCount);
		// console.log('%c [ removeMap ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', removeMap, mallTotalMap);


		// 删除所有的未勾选项
		// 未勾选项的市场档口如果全部未勾选，市场档口也删除
		// 如果有留言备注要一起删除
		const trElements = $printDom.querySelectorAll('tbody tr');
		const toRemove = [];
		for (let i = 0; i < trElements.length; i++) {
			const trElement = trElements[i];
			const className = trElement.getAttribute('class');
			const mallIndex = trElement.getAttribute('data-mall');
			const orderIndex = trElement.getAttribute('data-order');
			if (className == 'mallStallTr' && removeMap.has(`tr.${className}[data-mall]=${mallIndex}`)) {
				toRemove.push(trElement);
			} else if (removeMap.has(`tr.${className}[data-mall]=${mallIndex}[data-order]=${orderIndex}`)) {
				toRemove.push(trElement);
			}
		}
		toRemove.forEach(function(element) {
			element.parentNode.removeChild(element);
		});

		// 修改档口的合计、总成本、实付金额
		const mallDom = $printDom.querySelectorAll('tr.mallStallTr');
		for (let i = 0; i < mallDom.length; i++) {
			const trElement = mallDom[i];
			const mallIndex = trElement.getAttribute('data-mall');
			if (mallTotalMap.has(`tr.mallStallTr[data-mall]=${mallIndex}`)) {
				const { supplierPaymentCount, supplierCount, supplierCost } = mallTotalMap.get(`tr.mallStallTr[data-mall]=${mallIndex}`);
				if (trElement.querySelectorAll('.supplierItemCount')[0]) {
					trElement.querySelectorAll('.supplierItemCount')[0].innerHTML = `${supplierCount}`;
				}
				if (trElement.querySelectorAll('.supplierItemCost')[0]) {
					trElement.querySelectorAll('.supplierItemCost')[0].innerHTML = `${supplierCost}`;
				}
				if (trElement.querySelectorAll('.supplierItemPaymentCount')[0]) {
					trElement.querySelectorAll('.supplierItemPaymentCount')[0].innerHTML = `${supplierPaymentCount}`;
				}
			}
		}

		// 修改合计总成本 totalTr
		let totalDom = $printDom.getElementsByClassName('totalTr')[0];
		// 总数量
		if (totalDom.getElementsByClassName('bhd-table-total-count')[0]) {
			totalDom.getElementsByClassName('bhd-table-total-count')[0].innerHTML = `${totalCount}`;
		}
		// 总成本
		if (totalDom.getElementsByClassName('bhd-table-total-cost')[0]) {
			totalDom.getElementsByClassName('bhd-table-total-cost')[0].innerHTML = `${totalCost}`;
		}
		// 总实付金额
		if (totalDom.getElementsByClassName('bhd-table-total-payment')[0]) {
			totalDom.getElementsByClassName('bhd-table-total-payment')[0].innerHTML = `${totalPaymentCount}`;
		}
	}

	// 打印时 隐藏编辑按钮
	$printDom.querySelectorAll('.r-downLoad-hidden').forEach(item => item.remove());

	// 打印时 隐藏图片
	if (config.printIsHideImg) {
		$printDom.querySelectorAll('.tb-bb-img').forEach(item => item.remove());
	}

	// 打印时 隐藏留言备注
	if (config.printIsHideMemo) {
		$printDom.querySelectorAll('.bhd-tb-msg-box').forEach(item => item.remove());
	}

	// 打印时 显示打印时间
	if (config.printHasTime) {
		document.getElementById('printBHDTime').innerHTML = `打印时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`;
		// 导出打印也显示打印时间
		$printDom.querySelector('#printBHDTime').innerHTML = `打印时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`;

	}


	// 删除一些不需要打印的元素
	$printDom.querySelectorAll('.bhd-not-print').forEach(item => item.remove());
	$printDom.querySelectorAll('.delete-icon').forEach(item => item.remove());
	$printDom.querySelectorAll('.edit-icon').forEach(item => item.remove());

	// 背景色变更
	$printDom.querySelectorAll('.bhd-new-col').forEach(item => {
		item.style.backgroundColor = '#ffffff';
	});

	if (isPrint) {
		$printDom.querySelectorAll('input').forEach(item => {
			const $span = document.createElement('span');
			$span.textContent = item.value;
			$span.style.margin = '0 3px;';
			item.replaceWith($span);
		});
	}

	let printHtml = `
		<!DOCTYPE html>
		<div>
			${$printDom.innerHTML}
		</div>
	`;
	// console.log(printHtml);

	$printDom.innerHTML = '';

	return {
		printHtml
	};
};


export const downExcel = ({
	fileName = 'excelExport',
	downHtml,
}: {
	fileName: string;
	downHtml: string;
}) => {
	const template = `
		<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
			<head>
				<meta name="renderer" content="webkit">
				<!--[if gte mso 9]>
				<xml>
					<x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>
					<x:Name>Worksheet</x:Name>
					<x:WorksheetOptions>
						<x:DoNotDisplayGridlines/>
					</x:WorksheetOptions>
					</x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook>
				</xml>
				<![endif]-->
			</head>
			<body>
				${downHtml}
			</body>
		</html>
	`;
	downloadExcelByBlob(template, fileName);
};

export const enum StockVersionEnum {
	无库存版 = 1,
	库存版未开启扣库存 = 2,
	库存版开启扣库存 = 3,
}

export const getStockVersion = async() => {
	// 1 无库存版 2库存版未开启扣库存 3 库存版开启扣库存
	const { version } = await userStore.getUserInfo();
	await userStore.getSystemSetting();
	const { inventoryDeduct } = userStore;
	if (version === 1 && inventoryDeduct === 1) {
		return StockVersionEnum.库存版开启扣库存;
	}
	if (version === 1) {
		return StockVersionEnum.库存版未开启扣库存;
	}
	return StockVersionEnum.无库存版;
};

export const getAddNums = (num1, num2) => {
	return Number((+num1 + +num2).toFixed(2));
};

const getMultiNums = (count, price, sum) => {
	return getAddNums(sum, (+count * +price).toFixed(2));
};

// 处理多空格
export const preserveSpaces = (text) => {
	if (typeof text !== 'string') return text;
	return text.replace(/ /g, '\u00A0'); // 替换所有普通空格为不换行空格
};

export const calcSupplierCount = (supplierGroupItem) => {
	const { items = [] } = supplierGroupItem;
	let supplierPaymentCount = 0;
	let supplierCount = 0;
	let supplierCost = 0;

	items.forEach(sku => {
		sku.skuCounts?.forEach(skuItem => {
			const { skuPaymentCount = 0, count = 0, cost = 0 } = skuItem;
			supplierPaymentCount = getAddNums(skuPaymentCount, supplierPaymentCount);
			supplierCount = getAddNums(count, supplierCount);
			supplierCost = getMultiNums(count, cost, supplierCost);
		});

	});
	return {
		...supplierGroupItem,
		paymentCount: supplierPaymentCount,
		count: supplierCount,
		cost: supplierCost
	};
};


