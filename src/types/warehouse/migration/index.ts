/**
 * DistribItemLogQuery :DistribItemLogQuery
 */
export interface ItemDistributeQueryLogsRequest {
    /**
     * 开始时间
     */
    startTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 目标平台类型 ,PlatformTypeEnum
     */
    toPlatformList?: {
      platform?: string;
      desc?: string;
      [k: string]: any;
    }[];
    /**
     * 目标平台店铺id ,String
     */
    toSellerIdList?: string[];
    /**
     * 目标平台商品id
     */
    toItemId?: string;
    /**
     * 目标平台商品名称
     */
    toTitle?: string;
    /**
     * 目标平台商品编码
     */
    toOuterId?: string;
    /**
     * 目标平台规格名称
     */
    toSkuName?: string;
    /**
     * 目标平台规格编码
     */
    skuOuterId?: string;
    /**
     * 来源平台类型 ,PlatformTypeEnum
     */
    fromPlatformList?: {
      platform?: string;
      desc?: string;
      [k: string]: any;
    }[];
    /**
     * 来源店铺id ,String
     */
    fromSellerIdList?: string[];
    /**
     * 来源商品id
     */
    fromItemId?: string;
    /**
     * 审核状态,“未提交审核”、“待审核”、“审核通过”、“审核不通过”
     */
    checkStatus?: string;
    /**
     * 复制失败原因
     */
    copyFailReason?: string;
    /**
     * 审核失败原因
     */
    checkFailReason?: string;
    /**
     * 铺货状态,success成功,fail失败,processing处理中
     */
    status?: string;
    /**
     * 下游商品状态,onsale：在售中,instock：仓库中,draft：草稿箱
     */
    toItemStatus?: string;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 每页大小由于订单页面运单号搜索扩大为1000这里也限制为1000
     */
    pageSize?: number;
    [k: string]: any;
  }

/**
 * ResponseBody<PageList<DistribItemLogVo>> :ResponseBody
 */
export interface ItemDistributeQueryLogsResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
      pageNo?: number;
      pageSize?: number;
      total?: number;
      /**
       * T
       */
      list?: {
        /**
         * 1688平台商品id
         */
        itemId?: string;
        /**
         * 平台商品id
         */
        platformItemId?: string;
        /**
         * 平台
         */
        platform?: string;
        /**
         * 上游平台
         */
        fromPlatform?: string;
        /**
         * 上游店铺名
         */
        fromShopName?: string;
        /**
         * 平台店铺名
         */
        shopName?: string;
        /**
         * 去向平台商品名
         */
        title?: string;
        /**
         * 1688平台商品预览图
         */
        picUrl?: string;
        /**
         * 铺货状态,success成功SUCCESS,fail失败FAIL,processing处理中-INITRUNNING,examine审核中EXAMINE,<p>,<p>,INIT("INIT","初始化"),,RUNNING("RUNNING","运行中"),,SUCCESS("SUCCESS","成功"),,EXAMINE("EXAMINE","待审核"),,FAIL("FAIL","失败")
         */
        status?: string;
        /**
         * 子品上货状态,0:不同步状态,1:查看详情
         */
        subStatus?: number;
        /**
         * 下游商品状态
         */
        toItemStatus?: string;
        /**
         * 铺货来源
         */
        distribSource?: string;
        /**
         * 异常信息
         */
        errorMessage?: string;
        /**
         * pdd异常提示为2000001的时候提示警告,00000001智能匹配失败,2000000运费模板,100045假一罚十,100025七天无理由,00000002token失效,00000003默认重新铺货,00000004存在重复铺货-暂时移动端,00000007淘宝人脸
         */
        subCode?: string;
        /**
         * 铺货时间
         */
        createTime?: string;
        /**
         * 每次提交铺货的任务id
         */
        parentTaskId?: string;
        /**
         * 子任务id重试时使用
         */
        taskId?: string;
        /**
         * 店铺id
         */
        shopId?: string;
        /**
         * 1688id
         */
        aliId?: string;
        /**
         * appKey
         */
        aliAppKey?: string;
        shopKey?: string;
        /**
         * 主任务:MAJOR子任务:MINOR
         */
        taskType?: string;
        /**
         * 上货商品数
         */
        distribItemCount?: number;
        appKeyName?: string;
        /**
         * String
         */
        taskIds?: string[];
        /**
         * 原商品title
         */
        formTitle?: string;
        /**
         * 平台商品预览图
         */
        platformPicUrl?: string;
        /**
         * 上游categoryId
         */
        fromCategoryId?: string;
        shopType?: string;
        /**
         * 上货优化提示
         */
        optimizeMsg?: string;
        /**
         * 是否需要上货优化，true代表需要，false代表不需要
         */
        needOptimize?: boolean;
        /**
         * 类目id
         */
        categoryIds?: string;
        /**
         * 类目名称
         */
        categoryNames?: string;
        /**
         * 运费模板id
         */
        expressTemplateId?: string;
        /**
         * 拼多多满减折扣
         */
        twoPiecesDiscount?: number;
        /**
         * 1展示爆单计划标签
         */
        explosiveOrderFlag?: number;
        /**
         * 1展示导入货源标签
         */
        userProductPoolFlag?: number;
        /**
         * 未匹配到可用标品
         */
        noMatchTag?: number;
        /**
         * JSONObject
         */
        extInfo?: {
          /**
           * TimeZone
           */
          defaultTimeZone?: {
            ID?: string;
            /**
             * TimeZone
             */
            defaultTimeZone?: {
              [k: string]: any;
            };
            /**
             * TimeZone
             */
            mainAppContextDefault?: {
              [k: string]: any;
            };
            [k: string]: any;
          };
          /**
           * Locale
           */
          defaultLocale?: {
            /**
             * BaseLocale
             */
            baseLocale?: {
              hash?: number;
              [k: string]: any;
            };
            /**
             * LocaleExtensions
             */
            localeExtensions?: {
              [k: string]: any;
            };
            hashCodeValue?: number;
            /**
             * Locale
             */
            defaultLocale?: {
              [k: string]: any;
            };
            /**
             * Locale
             */
            defaultDisplayLocale?: {
              [k: string]: any;
            };
            /**
             * Locale
             */
            defaultFormatLocale?: {
              [k: string]: any;
            };
            languageTag?: string;
            /**
             * :
             */
            isoLanguages?: string[];
            /**
             * :
             */
            isoCountries?: string[];
            [k: string]: any;
          };
          DEFAULT_TYPE_KEY?: string;
          DEFFAULT_DATE_FORMAT?: string;
          DEFAULT_PARSER_FEATURE?: number;
          DEFAULT_GENERATE_FEATURE?: number;
          [k: string]: any;
        };
        [k: string]: any;
      }[];
      [k: string]: any;
    };
    [k: string]: any;
  }
  
  