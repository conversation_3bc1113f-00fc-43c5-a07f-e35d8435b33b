export const enum ModulesFunctionEnum {
	订单打印 = 'TRADE_PRINT',
	商品标签_拿货单导出 = 'TRADE_TAKE_GOODS_ORDER',
	商品标签_标签明细导出 = 'TRADE_TAKE_GOODS_DETAIL',
	库存看板 = 'STOCK_BANNER',
	售后扫描登记 = 'REFUND_BANNER',
	货品档案 = 'ITEM_RECORD',
	货品规格档案 = 'ITEM_SKU_RECORD',
	组合货品档案 = 'ITEM_COMBINATION_RECORD',
	货品与商品关系_库存版 = 'ITEM_RELATION_STOCK',
	货品与商品关系_0库存版 = 'ITEM_RELATION_ZERO',
	打印记录 = 'REPORT_PRINT_RECORD',
	发货记录 = 'REPORT_SEND_RECORD',
	底单查询 = 'REPORT_BOTTOM_QUERY',
	买家售后统计 = 'REPORT_BUYER_REFUND_STATISTICS',
	快递对账 = 'REPORT_EXPRESS_RECONCILIATION',
	毛利润报表 = 'REPORT_GROSS_PROFIT_STATEMENT',
	货品出入库账单 = 'REPORT_GOODS_IN_OUT_OF_STORAGE',
	货品成本报表 = 'REPORT_GOODS_COST_REPORT',
	物流预警 = 'REPORT_LOGISTICS_WARNING',
	下载中心_采购单 = 'PURCHASE_DETAIL',
	备货单 = 'REPORT_STOCK_LIST', // 前端处理下载了，下载中心不做处理
	标签对账报表 = 'STALL_LABEL_CHECK_STATEMENT',
	标签对账明细='STALL_LABEL_CHECK_DETAIL_STATEMENT',
	售后订单导出 = 'REFUND_INFO',
	异常预警 = 'TRADE_WARNING',
	出入库日志 = "STOCK_IN_OUT_LOG",
	销售分析 = "REPORT_SALES_ANALYSIS",
	售后扫描记录 = 'REFUND_BATCH_SCAN',
	快递拦截单号导出 = 'REFUND_LOGISTICS_INTERCEPT',
	售后分析 = "REPORT_REFUND_ANALYSIS",
	进销存报表 = "REPORT_STOCK",
	验货发货扫描记录 = "INSPECT_SHIP_SCAN_TRADE_RECORD",
	售后管理_售后单日志 = "REFUND_OPERATE_LOG",
	拿货小标签 = 'SMALL_LABELS_FOR_GOODS',
	到货入库 = 'REACH_STOCK',
	采购单 = 'PURCHASE_STOCK',
	手动出库 = 'HANDOUT_BOUND_ORDER_STOCK',
	退货标签记录 = 'REFUND_SCAN_LABEL_EXPORT'	
}

/**
 * DownTaskAddTaskRequest :DownTaskAddTaskRequest
 */
export interface IndexDownloadCenterAddTaskRequest {
	/**
	 * 所属对于模块功能
	 */
	modulesFunctionEnum: ModulesFunctionEnum;
	/**
	 * 请求参数json
	 */
	requestJson: string;
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 子账号用户id
	 */
	subUserId?: number;
	/**
	 * 执行节点IP
	 */
	sourceHost?: string;
	/**
	 * 文件名
	 */
	fileName?: string;
	apiName?: string;
	[k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface IndexDownloadCenterAddTaskResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: number;
	[k: string]: any;
}
/**
 * DownTaskPageListRequest :DownTaskPageListRequest
 */
export interface IndexDownloadCenterGetPageListRequest {
	/**
	 * 模块枚举,TRADE("trade","订单",1),,STOCK("stock","库存",2),,REFUND("refund","售后",3),,ITEM("item","商品",4),,REPORT("report","报表",5),,STALL("stall","档口",6)
	 */
	modulesEnum?: {
		[k: string]: any;
	};
	modulesVal?: number;
	/**
	 * Integer
	 */
	modulesValList?: number[];
	/**
	 * 所属模块功能
	 */
	modulesFunctionEnum?: {
		[k: string]: any;
	};
	modulesFunctionVal?: number;
	/**
	 * Integer
	 */
	modulesFunctionValList?: number[];
	beginTime?: string;
	endTime?: string;
	userId?: number;
	/**
	 * 任务状态,WAIT("队列等待执行",0),,EXECUTE("正在执行中",1),,SUCCESS("执行成功",2),,FAIL("执行失败",3)
	 */
	downTaskStatusEnum?: {
		[k: string]: any;
	};
	downTaskStatusVal?: number;
	/**
	 * Integer
	 */
	downTaskStatusValList?: number[];
	/**
	 * 任务id集合 ,Long
	 */
	taskIdList?: number[];
	/**
	 * 是否拉去执行中的过期任务
	 */
	fetchExecuteExpireTaskFlag?: boolean;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 页面大小
	 */
	pageSize?: number;
	apiName?: string;
	[k: string]: any;
}

export interface DownloadCenterListItem {

	/**
	 * 主键
	 */
	id?: number;
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 文件名
	 */
	fileName?: string;
	/**
	 * 创建时间
	 */
	gmtCreated?: string;
	/**
	 * 修改时间
	 */
	gmtModified?: string;
	/**
	 * 所属模块功能,
	 */
	modulesFunction?: number;
	/**
	 * 所属模块,
	 */
	modules?: number;
	/**
	 * 所属模块名,
	 */
	modulesName?: number;
	/**
	 * 执行状态0等待导出，1导出中，2导出成功，3导出失败,
	 */
	status?: number;
	/**
	 * 执行进度0-100
	 */
	progress?: number;
	/**
	 * 导出结果,导出失败时存储导出失败原因
	 */
	errorMsg?: string;
	/**
	 * 执行节点IP
	 */
	sourceHost?: string;
	/**
	 * 重试次数
	 */
	retryCount?: number;
	/**
	 * 执行开始时间
	 */
	exeStartTime?: string;
	/**
	 * 执行结束时间
	 */
	exeEndTime?: string;
	/**
	 * 操作人
	 */
	operatorName?: string;
	/**
	 * 操作人id
	 */
	operatorUid?: number;
	/**
	 * 下载次数
	 */
	downCnt?: number;
	/**
	 * 删除标识0已删除1未删除
	 */
	enableStatus?: number;
	/**
	 * 请求参数
	 */
	requestJson?: string;
	/**
	 * oss_url
	 */
	ossUrl?: string;
	/**
	 * trace_id
	 */
	traceId?: string;
	/**
	 * 流转日志
	 */
	log?: string;
	[k: string]: any;

}
/**
* ResponseBody<PageList<DownTaskDTO>> :ResponseBody
*/
export interface IndexDownloadCenterGetPageListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: DownloadCenterListItem[];
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * DownTaskDeleteTaskRequest :DownTaskDeleteTaskRequest
 */
export interface IndexDownloadCenterBatchDeleteRequest {
	/**
	 * Long
	 */
	taskIdList: number[];
	/**
	 * 用户id
	 */
	userId?: number;
	apiName?: string;
	[k: string]: any;
}
/**
* ResponseBody<Void> :ResponseBody
*/
export interface IndexDownloadCenterBatchDeleteResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}


export const enum DownloadKeyTypeEnum {
	下载操作 = 'DOWNLOAD',
	删除操作 = 'DELETE'
}
/**
 * DownTaskGetKeyTaskRequest :DownTaskGetKeyTaskRequest
 */
export interface IndexDownloadCenterSendDownloadKeyRequest {
	/**
	 * 手机号
	 */
	mobile: string;
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 操作类型,DOWNLOAD(1,"下载操作")
	 */
	downloadKeyTypeEnum: DownloadKeyTypeEnum;
	apiName?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface IndexDownloadCenterSendDownloadKeyResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * DownLoadOpsRequest :DownLoadOpsRequest
 */
export interface IndexDownloadCenterBatchDownloadRequest {
	/**
	 * Long
	 */
	taskIdList: number[];
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 手机号
	 */
	mobile: string;
	[k: string]: any;
}
/**
* ResponseBody<Void> :ResponseBody
*/
export interface IndexDownloadCenterBatchDownloadResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * DownLoadOpsRequest :DownLoadOpsRequest
 */
export interface IndexDownloadCenterDownloadRequest {
	/**
	 * Long
	 */
	taskIdList: number[];
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 手机号
	 */
	mobile: string;
	[k: string]: any;
}
/**
* ResponseBody<Void> :ResponseBody
*/
export interface IndexDownloadCenterDownloadResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * DownLoadCheckKeyRequest :DownLoadCheckKeyRequest
 */
export interface IndexDownloadCenterCheckDownLoadingKeyRequest {
	/**
	 * 手机号
	 */
	mobile: string;
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 验证码
	 */
	verifyCode: string;
	apiName?: string;
	[k: string]: any;
}
/**
* ResponseBody<Boolean> :ResponseBody
*/
export interface IndexDownloadCenterCheckDownLoadingKeyResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}
