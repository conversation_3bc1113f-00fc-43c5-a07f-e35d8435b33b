/**
 * HKWS
 */
export type Code = string;

export interface IndexMonitoringGetMonitoringDeviceBrandResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  data?: {
    brandCode: Code;
    /**
     * 海康威视
     */
    brandName: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}


export interface IndexMonitoringGetMonitoringDeviceBrandRequest {
  [k: string]: any;
}

export interface IndexMonitoringCheckRepeatDeviceNameResponse {
    success?: boolean;
    /**
     *     MONITORING_DEVICE_NAME_EXIST(1200, "存储设备名称已存在"),
     */
    errorCode?: number;
    errorMessage?: string;
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }

export interface IndexMonitoringCheckRepeatDeviceNameRequest {
  [k: string]: any;
}

export interface IndexMonitoringSaveMonitoringDeviceResponse {
    success?: boolean;
    /**
     *     MONITORING_DEVICE_NAME_EXIST(1200, "存储设备名称已存在"),
     */
    errorCode?: number;
    errorMessage?: string;
    data?: boolean;
    [k: string]: any;
  }

export interface IndexMonitoringSaveMonitoringDeviceRequest {
  [k: string]: any;
}

export interface IndexMonitoringQueryMonitoringDeviceResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    data?: {
      /**
       * 表字段: id
       */
      id?: number;
      /**
       * 开启状态，1开启，0关闭
       */
      status?: number;
      /**
       * 设备名称
       */
      deviceName?: string;
      /**
       * 设备品牌
       */
      brandCode?: string;
      /**
       * 设备品牌名称
       */
      brandName?: string;
      /**
       * 设备ip 地址
       */
      deviceIp?: string;
      /**
       * 设备端口
       */
      devicePort?: string;
      /**
       * 设备登录账号
       */
      deviceAccountName?: string;
      /**
       * 设备登录密码
       */
      deviceAccountPassword?: string;
      /**
       * 创建时间
       */
      gmtCreated?: string;
      /**
       * 设备摄像头数量
       */
      deviceCameraNum?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  }

export interface IndexMonitoringQueryMonitoringDeviceRequest {
  [k: string]: any;
}

export interface IndexMonitoringEditMonitoringDeviceResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    data?: boolean;
    [k: string]: any;
  }

export interface IndexMonitoringEditMonitoringDeviceRequest {
  [k: string]: any;
}

export interface IndexMonitoringCheckRepeatCameraNameResponse {
    success?: boolean;
    /**
     *   MONITORING_DEVICE_CAMERA_NAME_EXIST(1201, "监控设备摄像头名称已存在"),
     */
    errorCode?: number;
    errorMessage?: string;
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }

export interface IndexMonitoringCheckRepeatCameraNameRequest {
  [k: string]: any;
}

export interface IndexMonitoringSaveDeviceCameraResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    data?: boolean;
    [k: string]: any;
  }

export interface IndexMonitoringSaveDeviceCameraRequest {
  [k: string]: any;
}

export interface IndexMonitoringQueryDeviceCameraResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    data?: {
      /**
       * 表字段: id
       */
      id?: number;
      /**
       * 账号用户id
       */
      userId?: number;
      /**
       * 开启状态，1开启，0关闭
       */
      status?: number;
      /**
       * 摄像头名称
       */
      cameraName?: string;
      /**
       * 设备记录id
       */
      deviceId?: number;
      /**
       * 摄像通道
       */
      cameraChannel?: string;
      /**
       * 删除标识 0 已删除 1 未删除
       */
      enableStatus?: number;
      /**
       * 水印是否开启，0：关闭，1开启
       */
      watermark?: number;
      /**
       * 创建时间
       */
      gmtCreated?: string;
      /**
       * 修改时间
       */
      gmtModified?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  }

export interface IndexMonitoringQueryDeviceCameraRequest {
  [k: string]: any;
}

export interface IndexMonitoringEditDeviceCameraResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    data?: boolean;
    [k: string]: any;
  }

export interface IndexMonitoringEditDeviceCameraRequest {
  [k: string]: any;
}

export interface VideoLogMonitorQueryVideoLogRequest {
  /**
   * 页码
   */
  pageNo?: number;
  /**
   * 每页大小  由于订单页面运单号搜索扩大为1000这里也限制为1000
   */
  pageSize?: number;
  /**
   * 操作类型，如 售后扫描登记
   */
  operateType?: number;
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * 操作人
   */
  operateName?: string;
  /**
   * 订单编号
   */
  tid?: string;
  /**
   * 快递单号
   */
  exNumber?: string;
  /**
   * 售后单号
   */
  refundId?: string;
  [k: string]: any;
}

export interface VideoLogMonitorQueryVideoLogResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  data?: {
    pageNo?: number;
    pageSize?: number;
    total?: number;
    list?: {
      /**
       * 操作人id
       */
      operateUserId?: string;
      /**
       * 操作人
       */
      operateName?: string;
      /**
       * 业务单据号
       */
      bizCode?: string;
      tid?: string;
      exNumber?: string;
      refundId?: string;
      operateType?: number;
      videoStartTime?: string;
      videoEndTime?: string;
      /**
       * 视频时长
       */
      videoDuration?: string;
      extend?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}
