export interface AreaItem {
    id: number;
    name: string;
    parentId: number;
    type: number;
    zip: string
}

export interface AreaItemApi {
    id: string;
    name: string;
    parentId: string;
    type: number;
    zip: string
}

export interface TreeShapeAddress extends AreaItem {
    children: Array<TreeShapeAddress>
}

export type platform = 'tb' | 'pdd' | 'fxg' | 'hand' | 'ali' | 'tm' | 'ksxd' | 'jd' | 'sph' | 'other' | 'xhs' | 'c2m' | 'yz' | 'dw' | 'ktt' | '';

export type TPlatform = platform;

export interface ResponseTypeOfKDZS<T> {
    success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	apiName?: string;
    data?: T;
	[k: string]: any;
}

/**
 * ResponseBody<PrintFilterConfigVO> :ResponseBody
 */
export interface TradePrintGetPrintFilterConfigResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PrintFilterConfigVO
     */
    data?: {
      /**
       * 过滤规则列表 ,String
       */
      filterPatternList?: string[];
      /**
       * 过滤关键词列表 ,String
       */
      filterKeywordList?: string[];
      [k: string]: any;
    };
    [k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradePrintSavePrintFilterConfigResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }

  
/**
 * PrintFilterConfigVO :PrintFilterConfigVO
 */
export interface TradePrintSavePrintFilterConfigRequest {
    /**
     * 过滤规则列表 ,String
     */
    filterPatternList?: string[];
    /**
     * 过滤关键词列表 ,String
     */
    filterKeywordList?: string[];
    [k: string]: any;
  }
  
  
