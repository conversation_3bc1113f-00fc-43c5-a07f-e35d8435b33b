import { labelPushPlatformEnum } from "@/constants/labelPush";
import { platform } from "../../common";

/**
 * SysItemQueryRequest :SysItemQueryRequest
 */
export interface SysItemGetSysItemListRequest {
	/**
	 * 是否绑定商品false:无绑定，true：有绑定 不传或者传null 既全部
	 */
	relationItem?: boolean | undefined | null;
	/**
	 * 货品id
	 */
	sysItemId?: number | string;
	/**
	 * 货品分类id
	 */
	classifyId?: number | string;
	/**
	 * 货品简称
	 */
	sysItemAlias?: string;
	/**
	 * 商家编码
	 */
	outerId?: string;
	/**
	 * 品牌id
	 */
	brandId?: string[];
	/**
	 * 创建起始时间
	 */
	startCreated?: string;
	/**
	 * 创建起始时间
	 */
	endCreated?: string;
	/**
	 * 销售状态
	 */
	saleStatus?: number;
	/**
	 * 是否精确查询True：isCombination为1就查00,isCombination为0只查11,False：,如上，但会出现10但情况
	 */
	needDeepSelect?: boolean;
	/**
	* 是否为组合商品，1:1101,0:0001,null:100011
	*/
	isCombination?: number;
	/**
   * 是否查找常用商品
   */
	findCommonItem?: boolean;
	/**
	 * 是否设置常用商品标记
	 */
	setCommonItem?: boolean;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	supplierName?: string;
	stall?: string;
	market?: string;
	[k: string]: any;
}

export interface SysItemSku {
	/**
	 * 货品规格id
	 */
	sysSkuId?: number | string;
	/**
	 * 货品id
	 */
	sysItemId?: number;
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 品牌id
	 */
	brandId?: number;
	/**
	 * 货品分类id
	 */
	classifyId?: number;
	/**
	 * 货品名称
	 */
	sysItemName?: string;
	/**
	 * 货品别名
	 */
	sysItemAlias?: string;
	/**
	 * 货品规格名称
	 */
	sysSkuName?: string;
	/**
	 * 货品规格别名
	 */
	sysSkuAlias?: string;
	/**
	 * 货品规格商家编码
	 */
	skuOuterId?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	/**
	 * 净重
	 */
	netWeight?: string;
	/**
	 * 毛重
	 */
	weight?: string;
	/**
	 * 网销价
	 */
	price?: string;
	/**
	 * 零售价
	 */
	retailPrice?: string;
	/**
	 * 批发价
	 */
	tradePrice?: string;
	/**
	 * 吊牌价
	 */
	tagPrice?: string;
	/**
	 * 成本价
	 */
	costPrice?: string;
	/**
	 * 货品规格图片
	 */
	picUrl?: string;
	/**
	 * 是否一对多，0：非一对多，1：一对多
	 */
	isOneToMany?: number;
	/**
	 * 是否为组合商品，1:组合商品
	 */
	isCombination?: number;
	/**
	* 组合规格信息集合 ,GroupCombinationRelationRecordDTO
	*/
	groupCombinationList?: {
		/**
		 * 关联表记录id主键
		 */
		id?: number;
		/**
		 * 用户ID
		 */
		userId?: number;
		/**
		 * 组合货品id
		 */
		groupSysItemId?: number;
		/**
		 * 组合规格id
		 */
		groupSysSkuId?: number;
		/**
		 * 系统货品id
		 */
		sysItemId?: number;
		/**
		 * 系统规格id
		 */
		sysSkuId?: number;
		/**
		 * 组合比例
		 */
		groupProportionNum?: number;
		/**
		 * 货品规格图片
		 */
		picUrl?: string;
		/**
		 * 货品名称
		 */
		sysItemName?: string;
		/**
		 * 货品别名
		 */
		sysItemAlias?: string;
		/**
		 * 货品规格名称
		 */
		sysSkuName?: string;
		/**
		 * 货品规格别名
		 */
		sysSkuAlias?: string;
		/**
		 * 是否为组合商品，1:组合商品
		 */
		isCombination?: number;
		/**
		 * 品牌id
		 */
		brandId?: number;
		/**
		 * 货品分类id
		 */
		classifyId?: number;
		/**
		 * 货品规格商家编码
		 */
		skuOuterId?: string;
		/**
		 * 货号
		 */
		itemNo?: string;
		/**
		 * 条形码
		 */
		barCode?: string;
		/**
		 * 净重
		 */
		netWeight?: string;
		/**
		 * 毛重
		 */
		weight?: string;
		/**
		 * 排序值
		 */
		sort?: number;
		/**
		 * 网销价
		 */
		price?: string;
		/**
		 * 零售价
		 */
		retailPrice?: string;
		/**
		 * 批发价
		 */
		tradePrice?: string;
		/**
		 * 吊牌价
		 */
		tagPrice?: string;
		/**
		 * 成本价
		 */
		costPrice?: string;
		/**
		 * 单品库存总数
		 */
		stockTotal?: number;
		/**
		 * 销售状态
		 */
		saleStatus?: number;
		/**
		 * 计量单位id
		 */
		measuringUnitId?: number;
		/**
		 * 正品库存
		 */
		salableItemStock?: number;
		/**
		 * 正品预占数量
		 */
		salableItemPreemptedNum?: number;
		/**
		 * 在途商品库存
		 */
		transitItemStock?: number;
		/**
		 * 残次品库存
		 */
		defectiveItemStock?: number;
		/**
		 * 库存预警值
		 */
		stockWarnNum?: number;
		[k: string]: any;
	}[];
	/**
	 * 库存初始化类型,0:未初始化，1：正常初始化，2：入库初始化
	 */
	stockInitType?: number;
	/**
	 * 排序值
	 */
	sort?: number;
	/**
	 * 销售状态
	 */
	saleStatus?: number;
	/**
	 * 计量单位id
	 */
	measuringUnitId?: number;
	/**
	 * 是否开启过期时间，0：不开启，1：开启
	 */
	openExpire?: number;
	/**
	 * 过期类型，0:生产日期,1:保质期
	 */
	expireType?: number;
	/**
	 * 日期单位
	 */
	dateUnit?: string;
	/**
	 * 过期时间
	 */
	date?: string;
	/**
	 * 1:推送,0:不推送
	 */
	isPushDate?: number;
	/**
	 * 单品库存总数
	 */
	stockTotal?: number;
	/**
	 * 正品库存
	 */
	salableItemStock?: number;
	/**
	 * 正品预占数量
	 */
	salableItemPreemptedNum?: number;
	/**
	 * 在途商品库存
	 */
	transitItemStock?: number;
	/**
	 * 残次品库存
	 */
	defectiveItemStock?: number;
	/**
	 * 库存预警值
	 */
	stockWarnNum?: number;
	/**
	 * 1：可用,0：删除
	 */
	enableStatus?: number;
	/**
	 * 添加时间
	 */
	created?: string;
	/**
	 * 修改时间
	 */
	modified?: string;
	/**
         * 是否常用商品
         */
	commonItem?: boolean;
	/**
	 * 常用商品的创建时间
	 */
	commonItemCreate?: string;
	[k: string]: any;
}
export interface SysItem {
	/**
	 * 货品编码
	 */
	outerId?: string;
	/**
	 * 货品id
	 */
	sysItemId?: number;
	/**
	 * 货品分类id
	 */
	classifyId?: number;
	/**
	 * 货品名称
	 */
	sysItemName?: string;
	/**
	 * 货品简称
	 */
	sysItemAlias?: string;
	/**
	 * 品牌id
	 */
	brandId?: number;
	/**
	 * @mbg.generated是否可用
	 */
	enableStatus?: number;
	/**
	 * @mbg.generated添加时间
	 */
	created?: string;
	/**
	 * @mbg.generated修改时间
	 */
	modified?: string;
	/**
	 * 货品规格 ,SysSkuDTO
	 */
	sysSkuList?: SysItemSku[];

	supplier?: string;
	market?: string;
	stall?: string;
	[k: string]: any;
}
/**
 * ResponseBody<PageList<SysItemDTO>> :ResponseBody
 */
export interface SysItemGetSysItemListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total: number;
		/**
		 * T
		 */
		list: SysItem[];
		[k: string]: any;
	};
	[k: string]: any;
}

export interface SysItemGetSysItemDetailResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	data?: {
		total: number;
		/**
		 * T
		 */
		list: SysItem[];
		[k: string]: any;
	};
	[k: string]: any;
}

export interface SaveSkuItem {
	/**
	 * 货品规格id，新建时为null,编辑时不可为空
	 */
	sysSkuId?: number | string;
	/**
	 * 货品id，新建时为null,编辑时不可为空
	 */
	sysItemId?: number;
	/**
	 * 货品规格名称
	 */
	sysSkuName?: string;
	/**
	 * 货品规格别名
	 */
	sysSkuAlias?: string;
	/**
	 * 货位
	 */
	warehouseSlotName?: string;
	/**
	 * 货品规格商家编码
	 */
	skuOuterId?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	/**
	 * 净重
	 */
	netWeight?: string;
	/**
	 * 毛重
	 */
	weight?: string;
	/**
	 * 网销价
	 */
	price?: string;
	/**
	 * 零售价
	 */
	retailPrice?: string;
	/**
	 * 批发价
	 */
	tradePrice?: string;
	/**
	 * 吊牌价
	 */
	tagPrice?: string;
	/**
	 * 成本价
	 */
	costPrice?: string;
	/**
	 * 货品规格图片
	 */
	picUrl?: string;
	/**
	 * 是否为组合商品，1:组合商品,0:非组合
	 */
	isCombination?: number;
	/**
	 * 排序值
	 */
	sort?: number;
	/**
	 * 1：默认值,0：表示是删除此货品规格
	 */
	enableStatus?: number;
	[k: string]: any;
}

/**
 * SysItemSaveRequest :SysItemSaveRequest
 */
export interface SysItemSaveRequest {
	/**
	 * 货品id，新建时为null,编辑时不可为空
	 */
	sysItemId?: number;
	/**
	 * 货品分类id
	 */
	classifyId?: number;
	/**
	 * 货品简称
	 */
	sysItemAlias: string;
	/**
	 * 品牌id
	 */
	brandId?: number;
	/**
	 * 货品规格 ,SysSkuSaveRequest
	 */
	sysSkuList: SaveSkuItem[];
	[k: string]: any;
}

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface SysItemSaveResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}


/**
 * SysSkuDeleteRequest :SysSkuDeleteRequest
 */
export interface SysSkuDeleteRequest {
	/**
	 * 货品id
	 */
	sysItemId: number;
	/**
	 * 货品规格id
	 */
	sysSkuId: number;
	[k: string]: any;
}

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface SysSkuDeleteResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}

/**
 * SysItemOperationLogQueryRequest :SysItemOperationLogQueryRequest
 */
export interface ItemSysSkuGetOperationLogRequest {
	/**
	 * 货品规格id
	 */
	sysSkuId: number;
	/**
	 * 开始时间
	 */
	startCreated?: string;
	/**
	 * 结束时间
	 */
	endCreated?: string;
	/**
	 * 操作人
	 */
	operator?: string;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	[k: string]: any;
}

/**
 * ResponseBody<PageList<SysItemOperationLogDTO>> :ResponseBody
 */
export interface ItemSysSkuGetOperationLogResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: {
			/**
			 * 货品操作日志,表字段:id
			 */
			id?: number;
			userId?: number;
			/**
			 * 货品id,表字段:sys_item_id
			 */
			sysItemId?: number;
			/**
			 * 货品规格id,表字段:sys_sku_id
			 */
			sysSkuId?: number;
			/**
			 * 日志类型,表字段:log_type
			 */
			logType?: number;
			/**
			 * 1：可用,0:删除,表字段:enable_status
			 */
			enableStatus?: number;
			/**
			 * 操作人,表字段:operator_user
			 */
			operatorUser?: string;
			/**
			 * 表字段:operator_user_id
			 */
			operatorUserId?: number;
			/**
			 * 操作时间,表字段:created
			 */
			created?: string;
			/**
			 * 日志内容,表字段:content
			 */
			content?: string;
			/**
			 * 起始操作时间
			 */
			startCreated?: string;
			/**
			 * 截止操作时间
			 */
			endCreated?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

export interface ItemSysItemCloneRequest {
	sysItemId: number;
}
/**
* ResponseBody<Boolean> :ResponseBody
*/
export interface ItemSysItemCloneResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}

/**
 * SysItemSaveRequest :SysItemSaveRequest
 */
export interface ItemSysItemSaveRequest {
	/**
	 * 货品id，新建时为null,编辑时不可为空
	 */
	sysItemId?: number;
	/**
	 * 货品分类id
	 */
	classifyId?: number;
	/**
	 * 货品简称
	 */
	sysItemAlias: string;
	/**
	 * 品牌id
	 */
	brandId?: number;
	/**
	 * 货品规格 ,SysSkuSaveRequest
	 */
	sysSkuList: {
		/**
		 * 货品规格id，新建时为null,编辑时不可为空
		 */
		sysSkuId?: number;
		/**
		 * 货品id，新建时为null,编辑时不可为空
		 */
		sysItemId?: number;
		/**
		 * 货品规格名称
		 */
		sysSkuName?: string;
		/**
		 * 货品规格别名
		 */
		sysSkuAlias?: string;
		/**
		 * 货品规格商家编码
		 */
		skuOuterId: string;
		/**
		 * 货号
		 */
		itemNo?: string;
		/**
		 * 条形码
		 */
		barCode?: string;
		/**
		 * 净重
		 */
		netWeight?: string;
		/**
		 * 毛重
		 */
		weight?: string;
		/**
		 * 网销价
		 */
		price?: string;
		/**
		 * 零售价
		 */
		retailPrice?: string;
		/**
		 * 批发价
		 */
		tradePrice?: string;
		/**
		 * 吊牌价
		 */
		tagPrice?: string;
		/**
		 * 成本价
		 */
		costPrice?: string;
		/**
		 * 货品规格图片
		 */
		picUrl?: string;
		/**
		 * 是否为组合商品，1:组合商品,0:非组合
		 */
		isCombination: number;
		/**
		 * 排序值
		 */
		sort: number;
		/**
		 * 1：默认值,0：表示是删除此货品规格
		 */
		enableStatus?: number;
		/**
		 * 组合规格信息集合 ,GroupCombinationRelationRecordDTO
		 */
		groupCombinationList?: {
			/**
			 * 关联表记录id主键
			 */
			id?: number;
			/**
			 * 用户ID
			 */
			userId?: number;
			/**
			 * 组合货品id
			 */
			groupSysItemId?: number;
			/**
			 * 组合规格id
			 */
			groupSysSkuId?: number;
			/**
			 * 系统货品id
			 */
			sysItemId?: number;
			/**
			 * 系统规格id
			 */
			sysSkuId?: number;
			/**
			 * 组合比例
			 */
			groupProportionNum?: number;
			/**
			 * 货品规格图片
			 */
			picUrl?: string;
			/**
			 * 货品名称
			 */
			sysItemName?: string;
			/**
			 * 货品别名
			 */
			sysItemAlias?: string;
			/**
			 * 货品规格名称
			 */
			sysSkuName?: string;
			/**
			 * 货品规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 是否为组合商品，1:组合商品
			 */
			isCombination?: number;
			/**
			 * 品牌id
			 */
			brandId?: number;
			/**
			 * 货品分类id
			 */
			classifyId?: number;
			/**
			 * 货品规格商家编码
			 */
			skuOuterId?: string;
			/**
			 * 货号
			 */
			itemNo?: string;
			/**
			 * 条形码
			 */
			barCode?: string;
			/**
			 * 净重
			 */
			netWeight?: string;
			/**
			 * 毛重
			 */
			weight?: string;
			/**
			 * 排序值
			 */
			sort?: number;
			/**
			 * 网销价
			 */
			price?: string;
			/**
			 * 零售价
			 */
			retailPrice?: string;
			/**
			 * 批发价
			 */
			tradePrice?: string;
			/**
			 * 吊牌价
			 */
			tagPrice?: string;
			/**
			 * 成本价
			 */
			costPrice?: string;
			/**
			 * 单品库存总数
			 */
			stockTotal?: number;
			/**
			 * 销售状态
			 */
			saleStatus?: number;
			/**
			 * 计量单位id
			 */
			measuringUnitId?: number;
			/**
			 * 正品库存
			 */
			salableItemStock?: number;
			/**
			 * 正品预占数量
			 */
			salableItemPreemptedNum?: number;
			/**
			 * 在途商品库存
			 */
			transitItemStock?: number;
			/**
			 * 残次品库存
			 */
			defectiveItemStock?: number;
			/**
			 * 库存预警值
			 */
			stockWarnNum?: number;
			[k: string]: any;
		}[];
		[k: string]: any;
	}[];
	[k: string]: any;
}


/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface ItemSysItemSaveResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: {
		sysItemId?: string,
		sysSkuResultList?: {
			sysSkuId?: string,
			skuOuterId?: string,
			[k: string]: any;
		}[];
	}
	[k: string]: any;
}


export interface ItemSysItemGenerateOuterIdRequest {
	type: number; // 1货品编码 2货品规格编码
}

/**
 * ResponseBody<String> :ResponseBody
 */
export interface ItemSysItemGenerateOuterIdResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
}
/**
 * ResponseBody<List<SysItemDeleteResponse>> :ResponseBody
 */
/**
 * List<Long>
 */
/**
 * List<Long>
 */
// export type ItemSysItemBatchDeleteRequest = number[];
/**
 * 货品id的数组
 */
export type Id = number[];

export interface ItemSysItemBatchDeleteRequest {
	sysItemIds?: Id;
	/**
	 * 是否忽略库存，默认不忽略
	 */
	ignoreStock?: boolean;
	[k: string]: any;
}


export interface BindPlatItems {
	/**
	 * 商品标题,表字段:title
	 */
	title?: string;
	/**
	 * 表字段:id
	 */
	id?: number;
	/**
	 * 平台skuId,表字段:sku_id
	 */
	skuId?: string;
	/**
	 * 商品id,表字段:num_iid
	 */
	numIid?: string;
	/**
	 * 用户id,表字段:user_id
	 */
	userId?: number;
	/**
	 * 是否关联系统商品1:是0:否,表字段:relation_sys_item
	 */
	relationSysItem?: boolean;
	/**
	 * 店铺昵称,表字段:seller_nick
	 */
	sellerNick?: string;
	/**
	 * 店铺id,表字段:seller_id
	 */
	sellerId?: number;
	/**
	 * 平台TB,PDD,表字段:platform
	 */
	platform?: string;
	/**
	 * 规格名称,表字段:sku_name
	 */
	skuName?: string;
	/**
	 * 规格图片,表字段:pic_url
	 */
	picUrl?: string;
	/**
	 * 规格商家编码,表字段:sku_outer_id
	 */
	skuOuterId?: string;
	/**
	 * 货号,表字段:item_no
	 */
	itemNo?: string;
	/**
	 * 条码,表字段:bar_code
	 */
	barCode?: string;
	/**
	 * 价格，单位分,表字段:price
	 */
	price?: string;
	/**
	 * 数量,表字段:num
	 */
	num?: number;
	/**
	 * 重量，单位克,表字段:weight
	 */
	weight?: string;
	/**
	 * 同步时间,表字段:sync_time
	 */
	syncTime?: string;
	/**
	 * 同步版本号,表字段:sync_version
	 */
	syncVersion?: number;
	/**
	 * 1:可用,0:不可用,表字段:enable_status
	 */
	enableStatus?: number;
	/**
	 * sku创建时间
	 */
	created?: string;
	/**
	 * sku修改时间
	 */
	modified?: string;
	[k: string]: any;
}
/**
 * ResponseBody<SysItemDeleteResponse> :ResponseBody
 */
export interface ItemSysItemBatchDeleteResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * SysItemDeleteResponse
	 */
	data?: {
		/**
		 * 货品id
		 */
		sysItemId?: number;
		/**
		 * 货品规格id
		 */
		sysSkuId?: number;
		/**
		 * 是否删除成功
		 */
		isSuccess?: boolean;
		/**
		 * 失败原因
		 */
		errorMsg?: string;
		/**
		 * 错误码,0时为errorMsg报错,1时关联关系校验报错，需要取list,参考：SysItemDeleteErrorCodeEnum
		 */
		errorCode?: number;
		/**
		 * 已绑定的平台商品 ,SkuDTO
		 */
		bindPlatItems?: BindPlatItems[];
		[k: string]: any;
	};
	[k: string]: any;
}




/**
 * SysSkuDeleteRequest :SysSkuDeleteRequest
 */
export interface ItemSysSkuDeleteRequest {
	/**
	 * 货品id
	 */
	sysItemId: number;
	/**
	 * 货品规格id
	 */
	sysSkuId: number;
	[k: string]: any;
}

/**
 * ResponseBody<SysItemDeleteResponse> :ResponseBody
 */
export interface ItemSysSkuDeleteResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * SysItemDeleteResponse
	 */
	data?: {
		/**
		 * 货品id
		 */
		sysItemId?: number;
		/**
		 * 货品规格id
		 */
		sysSkuId?: number;
		/**
		 * 是否删除成功
		 */
		isSuccess?: boolean;
		/**
		 * 失败原因
		 */
		errorMsg?: string;
		/**
		 * 错误码,0时为errorMsg报错,1时关联关系校验报错，需要取list,参考：SysItemDeleteErrorCodeEnum
		 */
		errorCode?: number;
		/**
		 * 已绑定的平台商品 ,SkuDTO
		 */
		bindPlatItems?: {
			/**
			 * 商品标题,表字段:title
			 */
			title?: string;
			/**
			 * 表字段:id
			 */
			id?: number;
			/**
			 * 平台skuId,表字段:sku_id
			 */
			skuId?: string;
			/**
			 * 商品id,表字段:num_iid
			 */
			numIid?: string;
			/**
			 * 用户id,表字段:user_id
			 */
			userId?: number;
			/**
			 * 是否关联系统商品1:是0:否,表字段:relation_sys_item
			 */
			relationSysItem?: boolean;
			/**
			 * 店铺昵称,表字段:seller_nick
			 */
			sellerNick?: string;
			/**
			 * 店铺id,表字段:seller_id
			 */
			sellerId?: number;
			/**
			 * 平台TB,PDD,表字段:platform
			 */
			platform?: string;
			/**
			 * 规格名称,表字段:sku_name
			 */
			skuName?: string;
			/**
			 * 规格图片,表字段:pic_url
			 */
			picUrl?: string;
			/**
			 * 规格商家编码,表字段:sku_outer_id
			 */
			skuOuterId?: string;
			/**
			 * 货号,表字段:item_no
			 */
			itemNo?: string;
			/**
			 * 条码,表字段:bar_code
			 */
			barCode?: string;
			/**
			 * 价格，单位分,表字段:price
			 */
			price?: string;
			/**
			 * 数量,表字段:num
			 */
			num?: number;
			/**
			 * 重量，单位克,表字段:weight
			 */
			weight?: string;
			/**
			 * 同步时间,表字段:sync_time
			 */
			syncTime?: string;
			/**
			 * 同步版本号,表字段:sync_version
			 */
			syncVersion?: number;
			/**
			 * 1:可用,0:不可用,表字段:enable_status
			 */
			enableStatus?: number;
			/**
			 * sku创建时间
			 */
			created?: string;
			/**
			 * sku修改时间
			 */
			modified?: string;
			[k: string]: any;
		}[];
		/**
		 * 若存在关联关系错误情况，则返回相关关联关系组合规格信息的集合 ,SysItemDeleteDto
		 */
		groupRelationDeleteData?: {
			/**
			 * SysSkuDTO
			 */
			sonSysSkuList?: {
				/**
				 * 商品skuId
				 */
				skuId?: string;
				/**
				 * 货品规格id
				 */
				sysSkuId?: number;
				/**
				 * 货品id
				 */
				sysItemId?: number;
				/**
				 * 用户id
				 */
				userId?: number;
				/**
				 * 品牌id
				 */
				brandId?: number;
				/**
				 * 品牌id ,Long
				 */
				brandIds?: number[];
				/**
				 * 组合规格信息集合 ,GroupCombinationRelationRecordDTO
				 */
				groupCombinationList?: {
					/**
					 * 关联表记录id主键
					 */
					id?: number;
					/**
					 * 用户ID
					 */
					userId?: number;
					/**
					 * 组合货品id
					 */
					groupSysItemId?: number;
					/**
					 * 组合规格id
					 */
					groupSysSkuId?: number;
					/**
					 * 系统货品id
					 */
					sysItemId?: number;
					/**
					 * 系统规格id
					 */
					sysSkuId?: number;
					/**
					 * 组合比例
					 */
					groupProportionNum?: number;
					/**
					 * 货品规格图片
					 */
					picUrl?: string;
					/**
					 * 货品名称
					 */
					sysItemName?: string;
					/**
					 * 货品别名
					 */
					sysItemAlias?: string;
					/**
					 * 货品规格名称
					 */
					sysSkuName?: string;
					/**
					 * 货品规格别名
					 */
					sysSkuAlias?: string;
					/**
					 * 是否为组合商品，1:组合商品
					 */
					isCombination?: number;
					/**
					 * 品牌id
					 */
					brandId?: number;
					/**
					 * 货品分类id
					 */
					classifyId?: number;
					/**
					 * 货品规格商家编码
					 */
					skuOuterId?: string;
					/**
					 * 货号
					 */
					itemNo?: string;
					/**
					 * 条形码
					 */
					barCode?: string;
					/**
					 * 净重
					 */
					netWeight?: string;
					/**
					 * 毛重
					 */
					weight?: string;
					/**
					 * 排序值
					 */
					sort?: number;
					/**
					 * 网销价
					 */
					price?: string;
					/**
					 * 零售价
					 */
					retailPrice?: string;
					/**
					 * 批发价
					 */
					tradePrice?: string;
					/**
					 * 吊牌价
					 */
					tagPrice?: string;
					/**
					 * 成本价
					 */
					costPrice?: string;
					/**
					 * 单品库存总数
					 */
					stockTotal?: number;
					/**
					 * 销售状态
					 */
					saleStatus?: number;
					/**
					 * 计量单位id
					 */
					measuringUnitId?: number;
					/**
					 * 正品库存
					 */
					salableItemStock?: number;
					/**
					 * 正品预占数量
					 */
					salableItemPreemptedNum?: number;
					/**
					 * 在途商品库存
					 */
					transitItemStock?: number;
					/**
					 * 残次品库存
					 */
					defectiveItemStock?: number;
					/**
					 * 库存预警值
					 */
					stockWarnNum?: number;
					[k: string]: any;
				}[];
				/**
				 * 货品分类id
				 */
				classifyId?: number;
				/**
				 * 货品名称
				 */
				sysItemName?: string;
				/**
				 * 货品别名
				 */
				sysItemAlias?: string;
				/**
				 * 货品规格名称
				 */
				sysSkuName?: string;
				/**
				 * 货品规格别名
				 */
				sysSkuAlias?: string;
				/**
				 * 货品规格商家编码
				 */
				skuOuterId?: string;
				/**
				 * 货号
				 */
				itemNo?: string;
				/**
				 * 条形码
				 */
				barCode?: string;
				/**
				 * 净重
				 */
				netWeight?: string;
				/**
				 * 毛重
				 */
				weight?: string;
				/**
				 * 网销价
				 */
				price?: string;
				/**
				 * 零售价
				 */
				retailPrice?: string;
				/**
				 * 批发价
				 */
				tradePrice?: string;
				/**
				 * 吊牌价
				 */
				tagPrice?: string;
				/**
				 * 成本价
				 */
				costPrice?: string;
				/**
				 * 货品规格图片
				 */
				picUrl?: string;
				/**
				 * 关联状态,0:没有关联关系，1：一对一，2：一对多
				 */
				bindType?: number;
				/**
				 * 是否为组合商品，1:组合商品
				 */
				isCombination?: number;
				/**
				 * 库存初始化类型,0:未初始化，1：正常初始化，2：入库初始化
				 */
				stockInitType?: number;
				/**
				 * 排序值
				 */
				sort?: number;
				/**
				 * 销售状态
				 */
				saleStatus?: number;
				/**
				 * 计量单位id
				 */
				measuringUnitId?: number;
				/**
				 * 是否开启过期时间，0：不开启，1：开启
				 */
				openExpire?: number;
				/**
				 * 过期类型，0:生产日期,1:保质期
				 */
				expireType?: number;
				/**
				 * 日期单位
				 */
				dateUnit?: string;
				/**
				 * 过期时间
				 */
				date?: string;
				/**
				 * 1:推送,0:不推送
				 */
				isPushDate?: number;
				/**
				 * 单品库存总数
				 */
				stockTotal?: number;
				/**
				 * 正品库存
				 */
				salableItemStock?: number;
				/**
				 * 正品预占数量
				 */
				salableItemPreemptedNum?: number;
				/**
				 * 在途商品库存
				 */
				transitItemStock?: number;
				/**
				 * 残次品库存
				 */
				defectiveItemStock?: number;
				/**
				 * 库存预警值
				 */
				stockWarnNum?: number;
				/**
				 * 是否存在异常关联关系
				 */
				relationException?: boolean;
				/**
				 * 1：可用,0：删除
				 */
				enableStatus?: number;
				/**
				 * 组合比例
				 */
				groupProportionNum?: number;
				/**
				 * 添加时间
				 */
				created?: string;
				/**
				 * 添加时间-开始
				 */
				startCreated?: string;
				/**
				 * 添加时间-截止
				 */
				endCreated?: string;
				/**
				 * 修改时间
				 */
				modified?: string;
				/**
				 * 是否当前删除操作对象
				 */
				isDeleteOperation?: boolean;
				[k: string]: any;
			}[];
			/**
			 * 商品skuId
			 */
			skuId?: string;
			/**
			 * 货品规格id
			 */
			sysSkuId?: number;
			/**
			 * 货品id
			 */
			sysItemId?: number;
			/**
			 * 用户id
			 */
			userId?: number;
			/**
			 * 品牌id
			 */
			brandId?: number;
			/**
			 * 品牌id ,Long
			 */
			brandIds?: number[];
			/**
			 * 组合规格信息集合 ,GroupCombinationRelationRecordDTO
			 */
			groupCombinationList?: {
				/**
				 * 关联表记录id主键
				 */
				id?: number;
				/**
				 * 用户ID
				 */
				userId?: number;
				/**
				 * 组合货品id
				 */
				groupSysItemId?: number;
				/**
				 * 组合规格id
				 */
				groupSysSkuId?: number;
				/**
				 * 系统货品id
				 */
				sysItemId?: number;
				/**
				 * 系统规格id
				 */
				sysSkuId?: number;
				/**
				 * 组合比例
				 */
				groupProportionNum?: number;
				/**
				 * 货品规格图片
				 */
				picUrl?: string;
				/**
				 * 货品名称
				 */
				sysItemName?: string;
				/**
				 * 货品别名
				 */
				sysItemAlias?: string;
				/**
				 * 货品规格名称
				 */
				sysSkuName?: string;
				/**
				 * 货品规格别名
				 */
				sysSkuAlias?: string;
				/**
				 * 是否为组合商品，1:组合商品
				 */
				isCombination?: number;
				/**
				 * 品牌id
				 */
				brandId?: number;
				/**
				 * 货品分类id
				 */
				classifyId?: number;
				/**
				 * 货品规格商家编码
				 */
				skuOuterId?: string;
				/**
				 * 货号
				 */
				itemNo?: string;
				/**
				 * 条形码
				 */
				barCode?: string;
				/**
				 * 净重
				 */
				netWeight?: string;
				/**
				 * 毛重
				 */
				weight?: string;
				/**
				 * 排序值
				 */
				sort?: number;
				/**
				 * 网销价
				 */
				price?: string;
				/**
				 * 零售价
				 */
				retailPrice?: string;
				/**
				 * 批发价
				 */
				tradePrice?: string;
				/**
				 * 吊牌价
				 */
				tagPrice?: string;
				/**
				 * 成本价
				 */
				costPrice?: string;
				/**
				 * 单品库存总数
				 */
				stockTotal?: number;
				/**
				 * 销售状态
				 */
				saleStatus?: number;
				/**
				 * 计量单位id
				 */
				measuringUnitId?: number;
				/**
				 * 正品库存
				 */
				salableItemStock?: number;
				/**
				 * 正品预占数量
				 */
				salableItemPreemptedNum?: number;
				/**
				 * 在途商品库存
				 */
				transitItemStock?: number;
				/**
				 * 残次品库存
				 */
				defectiveItemStock?: number;
				/**
				 * 库存预警值
				 */
				stockWarnNum?: number;
				[k: string]: any;
			}[];
			/**
			 * 货品分类id
			 */
			classifyId?: number;
			/**
			 * 货品名称
			 */
			sysItemName?: string;
			/**
			 * 货品别名
			 */
			sysItemAlias?: string;
			/**
			 * 货品规格名称
			 */
			sysSkuName?: string;
			/**
			 * 货品规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 货品规格商家编码
			 */
			skuOuterId?: string;
			/**
			 * 货号
			 */
			itemNo?: string;
			/**
			 * 条形码
			 */
			barCode?: string;
			/**
			 * 净重
			 */
			netWeight?: string;
			/**
			 * 毛重
			 */
			weight?: string;
			/**
			 * 网销价
			 */
			price?: string;
			/**
			 * 零售价
			 */
			retailPrice?: string;
			/**
			 * 批发价
			 */
			tradePrice?: string;
			/**
			 * 吊牌价
			 */
			tagPrice?: string;
			/**
			 * 成本价
			 */
			costPrice?: string;
			/**
			 * 货品规格图片
			 */
			picUrl?: string;
			/**
			 * 关联状态,0:没有关联关系，1：一对一，2：一对多
			 */
			bindType?: number;
			/**
			 * 是否为组合商品，1:组合商品
			 */
			isCombination?: number;
			/**
			 * 库存初始化类型,0:未初始化，1：正常初始化，2：入库初始化
			 */
			stockInitType?: number;
			/**
			 * 排序值
			 */
			sort?: number;
			/**
			 * 销售状态
			 */
			saleStatus?: number;
			/**
			 * 计量单位id
			 */
			measuringUnitId?: number;
			/**
			 * 是否开启过期时间，0：不开启，1：开启
			 */
			openExpire?: number;
			/**
			 * 过期类型，0:生产日期,1:保质期
			 */
			expireType?: number;
			/**
			 * 日期单位
			 */
			dateUnit?: string;
			/**
			 * 过期时间
			 */
			date?: string;
			/**
			 * 1:推送,0:不推送
			 */
			isPushDate?: number;
			/**
			 * 单品库存总数
			 */
			stockTotal?: number;
			/**
			 * 正品库存
			 */
			salableItemStock?: number;
			/**
			 * 正品预占数量
			 */
			salableItemPreemptedNum?: number;
			/**
			 * 在途商品库存
			 */
			transitItemStock?: number;
			/**
			 * 残次品库存
			 */
			defectiveItemStock?: number;
			/**
			 * 库存预警值
			 */
			stockWarnNum?: number;
			/**
			 * 是否存在异常关联关系
			 */
			relationException?: boolean;
			/**
			 * 1：可用,0：删除
			 */
			enableStatus?: number;
			/**
			 * 组合比例
			 */
			groupProportionNum?: number;
			/**
			 * 添加时间
			 */
			created?: string;
			/**
			 * 添加时间-开始
			 */
			startCreated?: string;
			/**
			 * 添加时间-截止
			 */
			endCreated?: string;
			/**
			 * 修改时间
			 */
			modified?: string;
			/**
			 * 是否当前删除操作对象
			 */
			isDeleteOperation?: boolean;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * ReqSelectSystemItemListWithPageDTO :ReqSelectSystemItemListWithPageDTO
 */
export interface ItemSysItemListOfItemRelationSysTemItemViewRequest {
	/**
	 * 货品id
	 */
	sysItemId?: number;
	/**
	 * 关联异常异常:true
	 */
	relationException?: boolean;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 货品简称
	 */
	itemAlias?: string;
	/**
	 * 商家编码
	 */
	skuOuterId?: string;
	/**
	 * 规格名称
	 */
	skuName?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	[k: string]: any;
}

/**
 * ResponseBody<PageList<RspListOfItemRelationSystemItemViewDTO>> :ResponseBody
 */
export interface ItemSysItemListOfItemRelationSysTemItemViewResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: {
			/**
			 * 货品id
			 */
			sysItemId?: string;
			/**
			 * 货品简称
			 */
			sysItemAlias?: string;
			/**
			 * 货品规格 ,SysItemSku
			 */
			sysItemSkuList?: {
				/**
				 * 关联平台商品规格列表 ,PlatformItemSku
				 */
				relationPlatformItemList?: {
					/**
					 * 标签
					 */
					title?: string;
					/**
					 * 商品id
					 */
					numIid?: string;
					/**
					 * 商品skuId
					 */
					skuId?: string;
					/**
					 * 平台
					 */
					platform?: string;
					/**
					 * 店铺名
					 */
					sellerNick?: string;
					/**
					 * 商家编码
					 */
					skuOuterId?: string;
					/**
					 * 规格名称
					 */
					sysSkuName?: string;
					/**
					 * 货号
					 */
					itemNo?: string;
					/**
					 * 条形码
					 */
					barCode?: string;
					/**
					 * 图片
					 */
					picUrl?: string;
					/**
					 * 关联状态EXPIRE("已失效商品"),DELETE("被删除商品")
					 */
					relationStatus?: {
						[k: string]: any;
					};
					/**
					 * 库存数量
					 */
					num?: number;
					[k: string]: any;
				}[];
				/**
				 * 货品skuId
				 */
				sysSkuId?: string;
				/**
				 * 货品规格编码
				 */
				skuOuterId?: string;
				/**
				 * 货品编码
				 */
				outerId?: string;
				/**
				 * 规格名称
				 */
				sysSkuName?: string;
				/**
				 * 货号
				 */
				itemNo?: string;
				/**
				 * 条形码
				 */
				barCode?: string;
				/**
				 * 图片
				 */
				picUrl?: string;
				/**
				 * 正品库存数量
				 */
				salableItemStock?: number;
				[k: string]: any;
			}[];
			isCollapse?: boolean;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}
export enum UserViewValEnum {
	供应商 = 1,
	分销商 = 2
}
/**
 * ReqSelectPlatformItemListWithPageDTO :ReqSelectPlatformItemListWithPageDTO
 */
export interface ItemSysItemListOfItemRelationPlatformItemViewRequest {
	/**
	 * 颜色尺码集合 ,String
	 */
	colorOrSizeList?: string[];
	/**
	 * 是否精确查询
	 */
	preciseQuery?: boolean;
	/**
	 * 货品简称
	 */
	sysItemAlias?: string;
	/**
	 * 货品规格名称
	 */
	sysSkuName?: string;
	/**
	 * 货品商家编码
	 */
	sysOuterId?: string;
	/**
	 * 货品规格编码
	 */
	sysSkuOuterId?: string;
	/**
	 * 商品标题
	 */
	title?: string;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 平台s ,String
	 */
	platformList?: string[];
	/**
	 * 店铺名称
	 */
	sellerNick?: string;
	/**
	 * 店铺id
	 */
	sellerId?: string;
	/**
	 * 店铺ids ,Long
	 */
	sellerIdList?: number[];
	/**
	 * 商品id
	 */
	numIid?: string;
	/**
	 * 规格id
	 */
	skuId?: string;
	/**
	 * 商品id集合 ,String
	 */
	numIids?: string[];
	/**
	 * 规格id集合 ,String
	 */
	skuIds?: string[];
	/**
	 * 是否关联系统商品
	 */
	relationSysItem?: boolean;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	/**
	 * 导入类型1勾选导出2查询导出,,SysItemExportTypeEnum[CHOOSE,SELECT,type,desc]
	 */
	exportType?: number;
	/**
	 * 是否零库存商品导出ture-是，false-否
	 */
	isZeroItemExport?: boolean;
	/**
	 * 商品状态,1:onsale在售中,2:instock已下架,3:delete已删除
	 */
	approveStatus?: string;
	/**
	 * 货品类型,1平台商品,2系统商品,3平台商品&系统商品
	 */
	itemType?: number;
	/**
	 * 商品名称简称编码ID
	 */
	itemContent?: string;
	/**
	 * 规格名称规格别名,0：存在,1：不存在
	 */
	skuContent?: string;
	/**
	 * 是否设置简称
	 */
	existItemAlias?: number;
	/**
	 * 是否设置规格别名
	 */
	existSkuAlias?: number;
	/**
	 * 是否设置重量
	 */
	existWeight?: number;
	/**
	 * 是否设置成本价
	 */
	existCostPrice?: number;
	/**
	 * 是否设置市场
	 */
	existMarket?: number;
	/**
	 * 是否设置档口
	 */
	existStall?: number;
	/**
	 * 是否设置供应商
	 */
	existSupplier?: number;
	/**
	 * 档口
	 */
	stall?: string;
	/**
	 * 市场
	 */
	market?: string;
	/**
	 * 供应商名称
	 */
	supplierName?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	/**
	 * 有无货号0-无1-有
	 */
	existItemNo?: number;
	/**
	 * 有无商家编码原始商家编码,0：无商家编码,1：有商家编码,2：无原始商家编码,3：有原始商家编码
	 */
	existOuterId?: number;
	/**
	 * 是否隐藏已删除规格(true:隐藏；false:不隐藏)
	 */
	hideDeleteSku?: boolean;
	/**
	 * 分销商查询是否代发商品T代发商品F不是代发商品Null都查
	 */
	selectDropShippingItemFlag?: boolean;

	/**
	 * 商品来源,,SkuSourceEnums[SCM_SALE,str,desc]
	 */
	skuSourceStr?: string;
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 货品简称
	 */
	itemAlias?: string;
	/**
	 * 商家规格编码
	 */
	skuOuterId?: string;
	/**
	 * 商家编码
	 */
	outerId?: string;
	/**
	 * 规格名称
	 */
	skuName?: string;
	/**
	 * 分销商用户id集合 ,Long
	 */
	saleUserIdList?: number[];
	/**
	 * 用户视角1供应商2分销商
	 */
	userViewVal?: UserViewValEnum;
	/**
	 * 是否查找常用商品
	 */
	findCommonItem?: boolean;
	/**
	 * 是否设置常用商品标记
	 */
	setCommonItem?: boolean;

	/**
	 * 是否返回规格
	 */
	returnSku?: boolean;

	[k: string]: any;
}

export interface RelationSystemItemSku {
	/**
	 * 货品简称
	 */
	sysItemAlias?: string;
	/**
	 * 货品id
	 */
	sysItemId?: string;
	/**
	 * 货品skuId
	 */
	sysSkuId?: string;
	/**
	 * 商家编码
	 */
	skuOuterId?: string;
	/**
	 * 货品编码
	 */
	outerId?: string;
	/**
	 * 规格名称
	 */
	sysSkuName?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	/**
	 * 图片
	 */
	picUrl?: string;
	/**
	 * 是否为组合商品，1:组合货品,0:非组合货品
	 */
	isCombination?: number;
	/**
	 * 是否需要确认，默认为false,当relationSystemItemList为空时，平台sku规格编码与本地货品规格编码匹配上就为true
	 */
	isCheck?: boolean;
	/**
	 * 货品规格别名
	 */
	sysSkuAlias?: string;
	/**
	 * 毛重
	 */
	weight?: string;
	/**
	 * 成本价
	 */
	costPrice?: string;
	/**
	 * 表字段:supplier_id
	 */
	supplierId?: number;
	/**
	 * 市场
	 */
	market?: string;
	/**
	 * 档口
	 */
	stall?: string;
	/**
	 * 供应商名称
	 */
	supplierName?: string;
	[k: string]: any;
}


export interface PlatformItemSku {
	/**
	 * 店铺id
	 */
	sellerId?: number;
	/**
	 * 店铺名称
	 */
	sellerNick?: string;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 关联货品商品规格列表 ,SysItemSku
	 */
	relationSystemItemList?: RelationSystemItemSku[];
	/**
	 * 图片
	 */
	picUrl?: string;
	/**
	 * 商品skuId
	 */
	skuId?: string;
	/**
	 * 商家编码
	 */
	skuOuterId?: string;
	/**
	 * 规格名称
	 */
	skuName?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	/**
	 * 系统变化平台规格商家编码
	 */
	varietySkuOuterId?: string;
	/**
	 * 启用状态
	 */
	enableStatus?: number;
	/**
	 * 颜色
	 */
	color?: string;
	/**
	 * 尺码
	 */
	size?: string;
	/**
	 * 平台规格排序序号
	 */
	skuSort?: number;
	[k: string]: any;
}

export interface ItemRelationPlatformSystem {
	/**
	 * 商品id
	 */
	numIid?: string;
	/**
	 * 商品简称
	 */
	title?: string;
	/**
	 * 商家编码
	 */
	outerId?: string;
	/**
	 * 商品图片
	 */
	itemPicUrl?: string;
	/**
	 * 系统变化平台商家编码
	 */
	varietyOuterId?: string;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺名称
	 */
	sellerNick?: string;
	/**
	 * 商品状态,1:onsale在售中,2:inventory已下架
	 */
	approveStatus?: string;
	/**
	 * 平台商品规格 ,PlatformItemSku
	 */
	platformItemSkuList?: PlatformItemSku[];
	/**
	 * 结算价格，单位分
	 */
	balancePrice?: string;
	/**
	 * 规格变动提醒
	 */
	modification?: string;
	/**
	 *
	 */
	platformItem?: PlatformItemSku;
	[k: string]: any;
}
/**
 * ResponseBody<PageList<RspListOfItemRelationPlatformItemViewDTO>> :ResponseBody
 */
export interface ItemSysItemListOfItemRelationPlatformItemViewResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: ItemRelationPlatformSystem[];
		[k: string]: any;
	};
	[k: string]: any;
}

export enum OperateTypeEnum {
	绑定 = 'BIND',
	生成 = 'CREATE'
}
export interface BindRelationDTO {
	/**
	 * 平台商品Id
	 */
	numIid: string;
	/**
	 * 平台skuId
	 */
	skuId: string;
	/**
	 * 货品id
	 */
	sysItemId?: number;
	/**
	 * 货品skuId
	 */
	sysSkuId?: number;
	/**
	 * BIND:绑定,CREATE:生成
	 */
	operateType?: OperateTypeEnum;
	/**
	 * 货品简称
	 */
	sysItemAlias?: string;
	/**
	 * 规格别名
	 */
	sysSkuAlias?: string;
	/**
	 * 重量
	 */
	weight?: string;
	/**
	 * 成本价
	 */
	costPrice?: string;
	/**
	 * 档口
	 */
	market?: string;
	/**
	 * 供应商
	 */
	supplierName?: string;
	/**
	 * 档口
	 */
	stall?: string;
	[k: string]: any;
}
/**
 * List<ReqBatchUpdateSysItemRequest>
 */
export type ItemItemBatchUpdateSysItemByPlatformRequest = {
	type: string;
	/**
	 * 店铺id
	 */
	sellerId: number;
	/**
	 * 平台,PDD,TB
	 */
	platformType: {
		[k: string]: any;
	};
	/**
	 * 商品-货品绑定关系 ,BindRelationDTO
	 */
	bindRelations: BindRelationDTO[];
	/**
  * 用户视角1供应商2分销商
  */
	userViewVal?: UserViewValEnum;
	/**
	 * 分销商用户id
	 */
	saleUserId?: number;
	[k: string]: any;
}[];

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemItemBatchUpdateSysItemByPlatformResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		asyncCode?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * List<ReqUpdateItemDTO>
 */
export type ItemItemUpdateItemByIdRequest = {
	numIid: string;
	/**
	 * 系统变化平台商家编码
	 */
	varietyOuterId?: string;
	[k: string]: any;
}[];

/**
 * ResponseBody<SysSkuDTO> :ResponseBody
 */
export interface ItemSysItemBindRelationSysSkuResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * SysSkuDTO
   */
  data?: {
    id?: number;
    /**
     * 商品skuId
     */
    skuId?: string;
    sellerId?: number;
    platform?: string;
    /**
     * 货品规格id
     */
    sysSkuId?: number;
    /**
     * 货品规格id集合 ,Long
     */
    sysSkuIdList?: number[];
    /**
     * 是否分页
     */
    needPage?: boolean;
    /**
     * 货品id
     */
    sysItemId?: number;
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 品牌id
     */
    brandId?: number;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌id ,Long
     */
    brandIds?: number[];
    /**
     * 组合规格信息集合 ,GroupCombinationRelationRecordDTO
     */
    groupCombinationList?: {
      /**
       * 关联表记录id主键
       */
      id?: number;
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 组合货品id
       */
      groupSysItemId?: number;
      /**
       * 组合规格id
       */
      groupSysSkuId?: number;
      /**
       * 新增情况零时储存组合item货品id
       */
      saveTempGroupSysItemId?: number;
      /**
       * 新增情况零时储存组合sku规格id
       */
      saveTempGroupSysSkuId?: number;
      /**
       * 系统货品id
       */
      sysItemId?: number;
      /**
       * 系统规格id
       */
      sysSkuId?: number;
      /**
       * 组合比例
       */
      groupProportionNum?: number;
      /**
       * 货品规格图片
       */
      picUrl?: string;
      /**
       * 货品名称
       */
      sysItemName?: string;
      /**
       * 货品别名
       */
      sysItemAlias?: string;
      /**
       * 货品规格名称
       */
      sysSkuName?: string;
      /**
       * 货品规格别名
       */
      sysSkuAlias?: string;
      /**
       * 是否为组合商品，1:组合商品
       */
      isCombination?: number;
      /**
       * 品牌id
       */
      brandId?: number;
      brandName?: string;
      /**
       * 货品分类id
       */
      classifyId?: number;
      classifyName?: string;
      /**
       * 货品规格商家编码
       */
      skuOuterId?: string;
      /**
       * 货号
       */
      itemNo?: string;
      /**
       * 条形码
       */
      barCode?: string;
      /**
       * 净重
       */
      netWeight?: string;
      /**
       * 毛重
       */
      weight?: string;
      /**
       * 排序值
       */
      sort?: number;
      /**
       * 网销价
       */
      price?: string;
      /**
       * 零售价
       */
      retailPrice?: string;
      /**
       * 批发价
       */
      tradePrice?: string;
      /**
       * 吊牌价
       */
      tagPrice?: string;
      /**
       * 成本价
       */
      costPrice?: string;
      /**
       * 单品库存总数
       */
      stockTotal?: number;
      /**
       * 销售状态
       */
      saleStatus?: number;
      /**
       * 计量单位id
       */
      measuringUnitId?: number;
      /**
       * 正品库存
       */
      salableItemStock?: number;
      /**
       * 正品预占数量
       */
      salableItemPreemptedNum?: number;
      /**
       * 可配货库存数量
       */
      salableItemDistributableStock?: string;
      /**
       * 在途商品库存
       */
      transitItemStock?: number;
      /**
       * 残次品库存
       */
      defectiveItemStock?: number;
      /**
       * 库存预警值
       */
      stockWarnNum?: number;
      /**
       * 主货品编码
       */
      outerId?: string;
      /**
       * 警戒状态,1：正常2：警戒
       */
      warnStatus?: number;
      /**
       * 警戒状态str,1：正常2：警戒
       */
      warnStatusStr?: string;
      /**
       * 货位名称
       */
      warehouseSlotName?: string;
      /**
       * 货位id
       */
      warehouseSlotId?: number;
      /**
       * 供应商id
       */
      supplierId?: number;
      /**
       * 供应商名称
       */
      supplierName?: string;
      /**
       * 市场
       */
      market?: string;
      /**
       * 档口
       */
      stall?: string;
      /**
       * 货品颜色
       */
      sysColor?: string;
      /**
       * 货品尺码
       */
      sysSize?: string;
      [k: string]: any;
    }[];
    /**
     * 货品分类id
     */
    classifyId?: number;
    /**
     * Long
     */
    classifyIdList?: number[];
    /**
     * 是否设置分类T设置F未设置
     */
    existClassify?: boolean;
    /**
     * 分类名称
     */
    classifyName?: string;
    /**
     * 货品名称
     */
    sysItemName?: string;
    /**
     * 货品别名
     */
    sysItemAlias?: string;
    oldSysItemAlias?: string;
    /**
     * 货品规格名称
     */
    sysSkuName?: string;
    oldSysSkuName?: string;
    /**
     * 货品规格别名
     */
    sysSkuAlias?: string;
    /**
     * 货品规格商家编码
     */
    skuOuterId?: string;
    oldSkuOuterId?: string;
    /**
     * 主货品编码
     */
    outerId?: string;
    /**
     * 货号
     */
    itemNo?: string;
    /**
     * 条形码
     */
    barCode?: string;
    /**
     * 净重
     */
    netWeight?: string;
    /**
     * 毛重
     */
    weight?: string;
    /**
     * 网销价
     */
    price?: string;
    oldPrice?: string;
    /**
     * 零售价
     */
    retailPrice?: string;
    /**
     * 批发价
     */
    tradePrice?: string;
    /**
     * 吊牌价
     */
    tagPrice?: string;
    /**
     * 成本价
     */
    costPrice?: string;
    /**
     * 货品规格图片
     */
    picUrl?: string;
    oldPicUrl?: string;
    /**
     * 关联状态,0:没有关联关系，1：一对一，2：一对多
     */
    bindType?: number;
    /**
     * 是否为组合商品，1:组合商品,,CombinationEnum[NORMAL(货品与商品关联状态枚举),GROUP,code,desc]
     */
    isCombination?: number;
    /**
     * 库存初始化类型,0:未初始化，1：正常初始化，2：入库初始化
     */
    stockInitType?: number;
    /**
     * 排序值
     */
    sort?: number;
    /**
     * 销售状态
     */
    saleStatus?: number;
    /**
     * 计量单位id
     */
    measuringUnitId?: number;
    /**
     * 是否开启过期时间，0：不开启，1：开启
     */
    openExpire?: number;
    /**
     * 过期类型，0:生产日期,1:保质期
     */
    expireType?: number;
    /**
     * 日期单位
     */
    dateUnit?: string;
    /**
     * 过期时间
     */
    date?: string;
    /**
     * 1:推送,0:不推送
     */
    isPushDate?: number;
    /**
     * 单品库存总数
     */
    stockTotal?: number;
    /**
     * 正品库存
     */
    salableItemStock?: number;
    oldSalableItemStock?: number;
    /**
     * 正品预占数量
     */
    salableItemPreemptedNum?: number;
    /**
     * 可配货库存数量
     */
    salableItemDistributableStock?: number;
    /**
     * 在途商品库存
     */
    transitItemStock?: number;
    /**
     * 残次品库存
     */
    defectiveItemStock?: number;
    /**
     * 库存预警值
     */
    stockWarnNum?: number;
    /**
     * 是否存在异常关联关系
     */
    relationException?: boolean;
    /**
     * 1：可用,0：删除
     */
    enableStatus?: number;
    /**
     * 组合比例
     */
    groupProportionNum?: number;
    /**
     * 添加时间
     */
    created?: string;
    /**
     * 添加时间-开始
     */
    startCreated?: string;
    /**
     * 添加时间-截止
     */
    endCreated?: string;
    /**
     * 修改时间
     */
    modified?: string;
    /**
     * 是否当前删除操作对象
     */
    isDeleteOperation?: boolean;
    /**
     * 市场
     */
    market?: string;
    oldMarket?: string;
    /**
     * 档口
     */
    stall?: string;
    oldStall?: string;
    /**
     * 供应商id
     */
    supplierId?: number;
    /**
     * 供应商名称
     */
    supplierName?: string;
    oldSupplierName?: string;
    /**
     * 供应商地址-省
     */
    storageAddrProvince?: string;
    /**
     * 供应商地址-市
     */
    storageAddrCity?: string;
    /**
     * 供应商地址-区
     */
    storageAddrDistrict?: string;
    /**
     * 供应商地址-详细地址
     */
    storageAddr?: string;
    /**
     * excel导入行数
     */
    index?: number;
    /**
     * excel导入错误信息
     */
    excelImportErrorMsg?: string;
    numIid?: string;
    sellerNick?: string;
    /**
     * 子货品货品编码
     */
    groupOuterId?: string;
    /**
     * 子货品货品简称
     */
    groupSysItemAlias?: string;
    /**
     * 子货品货品规格编码
     */
    groupSkuOuterId?: string;
    /**
     * 子货品货品规格名称
     */
    groupSysSkuName?: string;
    /**
     * 组合比例
     */
    groupProportionNumStr?: string;
    /**
     * 组合货品配置项id
     */
    combinationConfigId?: number;
    /**
     * 是否自动计算成本价0否1是
     */
    autoCostPrice?: number;
    /**
     * 是否自动计算重量0否1是
     */
    autoWeight?: number;
    /**
     * 是否开启库存同步：1开启0不开启-1未设置；默认-1
     */
    syncStockFlag?: number;
    /**
     * 大写的货品规格编码
     */
    upperCaseSysSKuOuterId?: string;
    /**
     * 备注
     */
    memo?: string;
    /**
     * 是否绑定商品false:无绑定，true：有绑定
     */
    relationItem?: boolean;
    /**
     * 自定义属性
     */
    customAttributes?: string;
    /**
     * 商品自定义属性 ,ItemCustomAttributesDto
     */
    customAttributesList?: {
      /**
       * 自定义属性key,itemCustomAttribute1，ItemCustomAttribute2，ItemCustomAttribute3...
       */
      key?: string;
      /**
       * 自定义属性name,example：产地，质地，等级...
       */
      name?: string;
      /**
       * 自定义属性value,example：中国，硬，A...
       */
      value?: string;
      [k: string]: any;
    }[];
    /**
     * 货品颜色
     */
    sysColor?: string;
    /**
     * 货品尺码
     */
    sysSize?: string;
    /**
     * 是否常用商品
     */
    commonItem?: boolean;
    /**
     * 是否设置规格别名true：设置false未设置
     */
    existSysSkuAlias?: boolean;
    /**
     * 是否设置重量true：设置false未设置
     */
    existWeight?: boolean;
    /**
     * 是否设置成本价true：设置false未设置
     */
    existCostPrice?: boolean;
    /**
     * 是否设置供应商true：设置false未设置
     */
    existSupplier?: boolean;
    /**
     * 是否设置库存同步true：设置false未设置
     */
    existSyncStockFlag?: boolean;
    /**
     * 常用商品的创建时间
     */
    commonItemCreate?: string;
    /**
     * 货品规格商家编码集合 ,String
     */
    skuOuterIdList?: string[];
    /**
     * 货品规格备注
     */
    sysSkuMemo?: string;
    /**
     * 操作日志前缀(记录日志用)
     */
    operationLogPrefix?: string;
    /**
     * 仓库id
     */
    storageId?: number;
    /**
     * Long
     */
    sysItemIdList?: number[];
    /**
     * 关联平台商品数量
     */
    relationItemSize?: number;
    /**
     * 平台库存自动初始化开关
     */
    stockNumRuleInitFlag?: boolean;
    /**
     * 货品属性：1:成品2:包材3:半成品4:原材料
     */
    property?: number;
    /**
     * 货位id
     */
    warehouseSlotId?: number;
    /**
     * 货位名称
     */
    warehouseSlotName?: string;
    /**
     * 货位id集合 ,Long
     */
    warehouseSlotIdList?: number[];
    /**
     * 是否设置货位true：设置false未设置
     */
    existWarehouseSlot?: boolean;
    /**
     * 是否需要清空货位信息T清空F不清空
     */
    predicateClearWarehouseSlot?: boolean;
    /**
     * 是否查询条形码重复T查询F不查询默认F不查询
     */
    barCodeRepeatFlag?: boolean;
    /**
     * 货品id集合 ,Long
     */
    sysItemIds?: number[];
    [k: string]: any;
  };
  [k: string]: any;
}


/**
 * BindRelationSysSkuRequest :BindRelationSysSkuRequest
 */
export interface ItemSysItemBindRelationSysSkuRequest {
  platform: string;
  sellerId: number;
  numIid: string;
  skuId: string;
  sysSkuId: number;
  /**
   * 是否拿绑定的货品规格编码更新系统平台商品规格编码
   */
  updateVarietySkuOuterIdFlag?: boolean;
  /**
   * 是否需要上传商家规格编码T需要F不需要
   */
  uploadSkuOuterIdFlag?: boolean;
  [k: string]: any;
}


/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemItemUpdateItemByIdResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * List<ReqUpdateSkuDTO>
 */
export type ItemSkuUpdateSkuRequest = {
	skuId: string;
	numIid: string;
	/**
	 * 系统变化平台规格商家编码
	 */
	varietySkuOuterId?: string;
	userId?: number;
	[k: string]: any;
}[];

/**
 * ReqUpdateSkuDTO :ReqUpdateSkuDTO
 */
export interface ItemSkuUpdVarietySkuOuterIdAndQueryRequest {
  skuId: string;
  numIid: string;
  varietySkuOuterId: string;
  userId?: number;
  [k: string]: any;
}


/**
 * ResponseBody<List<SysSkuDTO>> :ResponseBody
 */
export interface ItemSkuUpdVarietySkuOuterIdAndQueryResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * SysSkuDTO
   */
  data?: {
    id?: number;
    /**
     * 商品skuId
     */
    skuId?: string;
    sellerId?: number;
    platform?: string;
    /**
     * 货品规格id
     */
    sysSkuId?: number;
    /**
     * 货品规格id集合 ,Long
     */
    sysSkuIdList?: number[];
    /**
     * 是否分页
     */
    needPage?: boolean;
    /**
     * 货品id
     */
    sysItemId?: number;
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 品牌id
     */
    brandId?: number;
    /**
     * 品牌名称
     */
    brandName?: string;
    /**
     * 品牌id ,Long
     */
    brandIds?: number[];
    /**
     * 组合规格信息集合 ,GroupCombinationRelationRecordDTO
     */
    groupCombinationList?: {
      /**
       * 关联表记录id主键
       */
      id?: number;
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 组合货品id
       */
      groupSysItemId?: number;
      /**
       * 组合规格id
       */
      groupSysSkuId?: number;
      /**
       * 新增情况零时储存组合item货品id
       */
      saveTempGroupSysItemId?: number;
      /**
       * 新增情况零时储存组合sku规格id
       */
      saveTempGroupSysSkuId?: number;
      /**
       * 系统货品id
       */
      sysItemId?: number;
      /**
       * 系统规格id
       */
      sysSkuId?: number;
      /**
       * 组合比例
       */
      groupProportionNum?: number;
      /**
       * 货品规格图片
       */
      picUrl?: string;
      /**
       * 货品名称
       */
      sysItemName?: string;
      /**
       * 货品别名
       */
      sysItemAlias?: string;
      /**
       * 货品规格名称
       */
      sysSkuName?: string;
      /**
       * 货品规格别名
       */
      sysSkuAlias?: string;
      /**
       * 是否为组合商品，1:组合商品
       */
      isCombination?: number;
      /**
       * 品牌id
       */
      brandId?: number;
      brandName?: string;
      /**
       * 货品分类id
       */
      classifyId?: number;
      classifyName?: string;
      /**
       * 货品规格商家编码
       */
      skuOuterId?: string;
      /**
       * 货号
       */
      itemNo?: string;
      /**
       * 条形码
       */
      barCode?: string;
      /**
       * 净重
       */
      netWeight?: string;
      /**
       * 毛重
       */
      weight?: string;
      /**
       * 排序值
       */
      sort?: number;
      /**
       * 网销价
       */
      price?: string;
      /**
       * 零售价
       */
      retailPrice?: string;
      /**
       * 批发价
       */
      tradePrice?: string;
      /**
       * 吊牌价
       */
      tagPrice?: string;
      /**
       * 成本价
       */
      costPrice?: string;
      /**
       * 单品库存总数
       */
      stockTotal?: number;
      /**
       * 销售状态
       */
      saleStatus?: number;
      /**
       * 计量单位id
       */
      measuringUnitId?: number;
      /**
       * 正品库存
       */
      salableItemStock?: number;
      /**
       * 正品预占数量
       */
      salableItemPreemptedNum?: number;
      /**
       * 可配货库存数量
       */
      salableItemDistributableStock?: string;
      /**
       * 在途商品库存
       */
      transitItemStock?: number;
      /**
       * 残次品库存
       */
      defectiveItemStock?: number;
      /**
       * 库存预警值
       */
      stockWarnNum?: number;
      /**
       * 主货品编码
       */
      outerId?: string;
      /**
       * 警戒状态,1：正常2：警戒
       */
      warnStatus?: number;
      /**
       * 警戒状态str,1：正常2：警戒
       */
      warnStatusStr?: string;
      /**
       * 货位名称
       */
      warehouseSlotName?: string;
      /**
       * 货位id
       */
      warehouseSlotId?: number;
      /**
       * 供应商id
       */
      supplierId?: number;
      /**
       * 供应商名称
       */
      supplierName?: string;
      /**
       * 市场
       */
      market?: string;
      /**
       * 档口
       */
      stall?: string;
      /**
       * 货品颜色
       */
      sysColor?: string;
      /**
       * 货品尺码
       */
      sysSize?: string;
      [k: string]: any;
    }[];
    /**
     * 货品分类id
     */
    classifyId?: number;
    /**
     * Long
     */
    classifyIdList?: number[];
    /**
     * 是否设置分类T设置F未设置
     */
    existClassify?: boolean;
    /**
     * 分类名称
     */
    classifyName?: string;
    /**
     * 货品名称
     */
    sysItemName?: string;
    /**
     * 货品别名
     */
    sysItemAlias?: string;
    oldSysItemAlias?: string;
    /**
     * 货品规格名称
     */
    sysSkuName?: string;
    oldSysSkuName?: string;
    /**
     * 货品规格别名
     */
    sysSkuAlias?: string;
    /**
     * 货品规格商家编码
     */
    skuOuterId?: string;
    oldSkuOuterId?: string;
    /**
     * 主货品编码
     */
    outerId?: string;
    /**
     * 货号
     */
    itemNo?: string;
    /**
     * 条形码
     */
    barCode?: string;
    /**
     * 净重
     */
    netWeight?: string;
    /**
     * 毛重
     */
    weight?: string;
    /**
     * 网销价
     */
    price?: string;
    oldPrice?: string;
    /**
     * 零售价
     */
    retailPrice?: string;
    /**
     * 批发价
     */
    tradePrice?: string;
    /**
     * 吊牌价
     */
    tagPrice?: string;
    /**
     * 成本价
     */
    costPrice?: string;
    /**
     * 货品规格图片
     */
    picUrl?: string;
    oldPicUrl?: string;
    /**
     * 关联状态,0:没有关联关系，1：一对一，2：一对多
     */
    bindType?: number;
    /**
     * 是否为组合商品，1:组合商品,,CombinationEnum[NORMAL(货品与商品关联状态枚举),GROUP,code,desc]
     */
    isCombination?: number;
    /**
     * 库存初始化类型,0:未初始化，1：正常初始化，2：入库初始化
     */
    stockInitType?: number;
    /**
     * 排序值
     */
    sort?: number;
    /**
     * 销售状态
     */
    saleStatus?: number;
    /**
     * 计量单位id
     */
    measuringUnitId?: number;
    /**
     * 是否开启过期时间，0：不开启，1：开启
     */
    openExpire?: number;
    /**
     * 过期类型，0:生产日期,1:保质期
     */
    expireType?: number;
    /**
     * 日期单位
     */
    dateUnit?: string;
    /**
     * 过期时间
     */
    date?: string;
    /**
     * 1:推送,0:不推送
     */
    isPushDate?: number;
    /**
     * 单品库存总数
     */
    stockTotal?: number;
    /**
     * 正品库存
     */
    salableItemStock?: number;
    oldSalableItemStock?: number;
    /**
     * 正品预占数量
     */
    salableItemPreemptedNum?: number;
    /**
     * 可配货库存数量
     */
    salableItemDistributableStock?: number;
    /**
     * 在途商品库存
     */
    transitItemStock?: number;
    /**
     * 残次品库存
     */
    defectiveItemStock?: number;
    /**
     * 库存预警值
     */
    stockWarnNum?: number;
    /**
     * 是否存在异常关联关系
     */
    relationException?: boolean;
    /**
     * 1：可用,0：删除
     */
    enableStatus?: number;
    /**
     * 组合比例
     */
    groupProportionNum?: number;
    /**
     * 添加时间
     */
    created?: string;
    /**
     * 添加时间-开始
     */
    startCreated?: string;
    /**
     * 添加时间-截止
     */
    endCreated?: string;
    /**
     * 修改时间
     */
    modified?: string;
    /**
     * 是否当前删除操作对象
     */
    isDeleteOperation?: boolean;
    /**
     * 市场
     */
    market?: string;
    oldMarket?: string;
    /**
     * 档口
     */
    stall?: string;
    oldStall?: string;
    /**
     * 供应商id
     */
    supplierId?: number;
    /**
     * 供应商名称
     */
    supplierName?: string;
    oldSupplierName?: string;
    /**
     * 供应商地址-省
     */
    storageAddrProvince?: string;
    /**
     * 供应商地址-市
     */
    storageAddrCity?: string;
    /**
     * 供应商地址-区
     */
    storageAddrDistrict?: string;
    /**
     * 供应商地址-详细地址
     */
    storageAddr?: string;
    /**
     * excel导入行数
     */
    index?: number;
    /**
     * excel导入错误信息
     */
    excelImportErrorMsg?: string;
    numIid?: string;
    sellerNick?: string;
    /**
     * 子货品货品编码
     */
    groupOuterId?: string;
    /**
     * 子货品货品简称
     */
    groupSysItemAlias?: string;
    /**
     * 子货品货品规格编码
     */
    groupSkuOuterId?: string;
    /**
     * 子货品货品规格名称
     */
    groupSysSkuName?: string;
    /**
     * 组合比例
     */
    groupProportionNumStr?: string;
    /**
     * 组合货品配置项id
     */
    combinationConfigId?: number;
    /**
     * 是否自动计算成本价0否1是
     */
    autoCostPrice?: number;
    /**
     * 是否自动计算重量0否1是
     */
    autoWeight?: number;
    /**
     * 是否开启库存同步：1开启0不开启-1未设置；默认-1
     */
    syncStockFlag?: number;
    /**
     * 大写的货品规格编码
     */
    upperCaseSysSKuOuterId?: string;
    /**
     * 备注
     */
    memo?: string;
    /**
     * 是否绑定商品false:无绑定，true：有绑定
     */
    relationItem?: boolean;
    /**
     * 自定义属性
     */
    customAttributes?: string;
    /**
     * 商品自定义属性 ,ItemCustomAttributesDto
     */
    customAttributesList?: {
      /**
       * 自定义属性key,itemCustomAttribute1，ItemCustomAttribute2，ItemCustomAttribute3...
       */
      key?: string;
      /**
       * 自定义属性name,example：产地，质地，等级...
       */
      name?: string;
      /**
       * 自定义属性value,example：中国，硬，A...
       */
      value?: string;
      [k: string]: any;
    }[];
    /**
     * 货品颜色
     */
    sysColor?: string;
    /**
     * 货品尺码
     */
    sysSize?: string;
    /**
     * 是否常用商品
     */
    commonItem?: boolean;
    /**
     * 是否设置规格别名true：设置false未设置
     */
    existSysSkuAlias?: boolean;
    /**
     * 是否设置重量true：设置false未设置
     */
    existWeight?: boolean;
    /**
     * 是否设置成本价true：设置false未设置
     */
    existCostPrice?: boolean;
    /**
     * 是否设置供应商true：设置false未设置
     */
    existSupplier?: boolean;
    /**
     * 是否设置库存同步true：设置false未设置
     */
    existSyncStockFlag?: boolean;
    /**
     * 常用商品的创建时间
     */
    commonItemCreate?: string;
    /**
     * 货品规格商家编码集合 ,String
     */
    skuOuterIdList?: string[];
    /**
     * 货品规格备注
     */
    sysSkuMemo?: string;
    /**
     * 操作日志前缀(记录日志用)
     */
    operationLogPrefix?: string;
    /**
     * 仓库id
     */
    storageId?: number;
    /**
     * Long
     */
    sysItemIdList?: number[];
    /**
     * 关联平台商品数量
     */
    relationItemSize?: number;
    /**
     * 平台库存自动初始化开关
     */
    stockNumRuleInitFlag?: boolean;
    /**
     * 货品属性：1:成品2:包材3:半成品4:原材料
     */
    property?: number;
    /**
     * 货位id
     */
    warehouseSlotId?: number;
    /**
     * 货位名称
     */
    warehouseSlotName?: string;
    /**
     * 货位id集合 ,Long
     */
    warehouseSlotIdList?: number[];
    /**
     * 是否设置货位true：设置false未设置
     */
    existWarehouseSlot?: boolean;
    /**
     * 是否需要清空货位信息T清空F不清空
     */
    predicateClearWarehouseSlot?: boolean;
    /**
     * 是否查询条形码重复T查询F不查询默认F不查询
     */
    barCodeRepeatFlag?: boolean;
    /**
     * 货品id集合 ,Long
     */
    sysItemIds?: number[];
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSkuUpdateSkuResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}



/**
 * ReqDeleteSkuDTO :ReqDeleteSkuDTO
 */
export interface ItemSkuBatchDeleteBySkuIdsRequest {
	/**
	 * 商品标题,表字段:title
	 */
	title?: string;
	/**
	 * 表字段:id
	 */
	id?: number;
	/**
	 * 平台skuId,表字段:sku_id
	 */
	skuId?: string;
	/**
	 * 商品id,表字段:num_iid
	 */
	numIid?: string;
	/**
	 * 用户id,表字段:user_id
	 */
	userId?: number;
	/**
	 * 是否关联系统商品1:是0:否,表字段:relation_sys_item
	 */
	relationSysItem?: boolean;
	/**
	 * 店铺昵称,表字段:seller_nick
	 */
	sellerNick?: string;
	/**
	 * 店铺id,表字段:seller_id
	 */
	sellerId?: number;
	/**
	 * 平台TB,PDD,表字段:platform
	 */
	platform?: string;
	/**
	 * 规格名称,表字段:sku_name
	 */
	skuName?: string;
	/**
	 * 规格图片,表字段:pic_url
	 */
	picUrl?: string;
	/**
	 * 规格商家编码,表字段:sku_outer_id
	 */
	skuOuterId?: string;
	/**
	 * 货号,表字段:item_no
	 */
	itemNo?: string;
	/**
	 * 条码,表字段:bar_code
	 */
	barCode?: string;
	/**
	 * 价格，单位分,表字段:price
	 */
	price?: string;
	/**
	 * 数量,表字段:num
	 */
	num?: number;
	/**
	 * 重量，单位克,表字段:weight
	 */
	weight?: string;
	/**
	 * 同步时间,表字段:sync_time
	 */
	syncTime?: string;
	/**
	 * 同步版本号,表字段:sync_version
	 */
	syncVersion?: number;
	/**
	 * 1:可用,0:不可用,表字段:enable_status
	 */
	enableStatus?: number;
	/**
	 * sku创建时间
	 */
	created?: string;
	/**
	 * sku修改时间
	 */
	modified?: string;
	/**
	 * 系统变化平台规格商家编码
	 */
	varietySkuOuterId?: string;
	[k: string]: any;
}[];


/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSkuBatchDeleteBySkuIdsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * ItemImportRequest :ItemImportRequest
 */
export interface ItemImportItemsRequest {
	/**
	 * 平台TB,PDD
	 */
	platform: platform;
	/**
	 * 店铺id
	 */
	sellerId: number | string;
	/**
	 * 类型:ALL:导入全部商品,LINK:按链接导入,NUMIID:按商品Id导入
	 */
	importType: string;
	/**
	 * 商品id或者链接 ,String
	 */
	numIids?: string[];

	/**
	 * 商品状态1出售中2已下架
	 */
	statusNum?: number;
	[k: string]: any;
}

/**
 * ResponseBody<ItemImportResult> :ResponseBody
 */
export interface ItemImportItemsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemImportResult
	 */
	data?: {
		userId?: number;
		/**
		 * 平台TB,PDD
		 */
		platform?: {
			[k: string]: any;
		};
		/**
		 * 店铺id
		 */
		sellerId?: number;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 进度0-100
		 */
		progress?: number;
		/**
		 * 商品总数量(在售+仓库)
		 */
		total?: number;
		/**
		 * 在售商品数量
		 */
		onsaleCount?: number;
		/**
		 * 仓库中商品数量
		 */
		inventoryCount?: number;
		/**
		 * 已同步商品数量
		 */
		completedCount?: number;
		/**
		 * 同步失败时的错误信息
		 */
		errorMsg?: string;
		/**
		 * 开始同步时间
		 */
		startSyncTime?: string;
		/**
		 * 扩展信息
		 */
		extendInfo?: string;
		[k: string]: any;
	};
	[k: string]: any;
}
/**
* ItemImportRequest :ItemImportRequest
*/
export interface ItemGetImportInfoRequest {
	/**
	 * 平台TB,PDD
	 */
	platform: string;
	/**
	 * 店铺id
	 */
	sellerId: number | string;
	/**
	 * 类型:ALL:导入全部商品,LINK:按链接导入,NUMIID:按商品Id导入
	 */
	importType: string;
	/**
	 * 商品id或者链接 ,String
	 */
	items?: string[];
	[k: string]: any;
}

/**
 * ResponseBody<ItemImportResult> :ResponseBody
 */
export interface ItemGetImportInfoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemImportResult
	 */
	data?: {
		userId?: number;
		/**
		 * 平台TB,PDD
		 */
		platform?: {
			[k: string]: any;
		};
		/**
		 * 店铺id
		 */
		sellerId?: number;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 进度0-100
		 */
		progress?: number;
		/**
		 * 商品总数量(在售+仓库)
		 */
		total?: number;
		/**
		 * 在售商品数量
		 */
		onsaleCount?: number;
		/**
		 * 仓库中商品数量
		 */
		inventoryCount?: number;
		/**
		 * 已同步商品数量
		 */
		completedCount?: number;
		/**
		 * 同步失败时的错误信息
		 */
		errorMsg?: string;
		/**
		 * 开始同步时间
		 */
		startSyncTime?: string;
		/**
		 * 扩展信息
		 */
		extendInfo?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ItemMatchQueryDTO :ItemMatchQueryDTO
 */
export interface ItemGetPlatformItemMatchRelationRequest {
	/**
	 * 是否显示有绑定商品,true:显示,false:不显示
	 */
	isShowBindItem: boolean;
	/**
	 * 平台类型:PDD,TB
	 */
	platformType: string;
	/**
	 * 平台店铺id
	 */
	sellerId: string;
	/**
	 * 类型:ALL:导入全部商品,NUMIID:按商品Id导入,STATUS:按商品状态导入
	 */
	importType: string;
	/**
	 * 商品id ,String
	 */
	numIids?: string[];
	/**
	 * 商品状态,1:onsale在售中,2:inventory已下架
	 */
	status?: number;
	/**
	 * 是否创建已删除的平台商品0-否1-是
	 */
	isCreateDelItem?: number;
	/**
	 * order关联商品、商品规格信息集合 ,ItemSkuForOrderDTO
	 */
	orderItemSkuInfos?: {
		/**
		 * 平台商品id
		 */
		numIid?: string;
		/**
		 * 商品标题
		 */
		title?: string;
		/**
		 * 商品图片
		 */
		picPath?: string;
		/**
		 * 商品所属类目
		 */
		cid?: string;
		/**
		 * 商家编码
		 */
		outerId?: string;
		/**
		 * 重量，单位克
		 */
		weight?: string;
		/**
		 * 平台skuId,表字段:sku_id
		 */
		skuId?: string;
		/**
		 * 规格名称
		 */
		skuPropertiesName?: string;
		/**
		 * 规格商家编码
		 */
		skuOuterId?: string;
		/**
		 * 价格，单位分
		 */
		price?: string;
		[k: string]: any;
	}[];
	/**
	 * platformShop ,PlatformShop
	 */
	platformShop?: {
		userId?: number;
		sellerId?: number;
		sellerNick?: string;
		sellerAbbreviation?: string;
		sellerExpireTime?: string;
		/**
		 * TB
		 * TM
		 * PDD
		 * FXG
		 * ALI
		 * KSXD
		 * HAND
		 * JD
		 *
		 */
		platformType?: {
			[k: string]: any;
		};
		accessToken?: string;
		status?: number;
		tokenStatus?: number;
		tbSellerType?: number;
		openId?: string;
		[k: string]: any;
	};
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	[k: string]: any;
}

export interface MatchRelationItem {
	/**
	 * 主键id
	 */
	id?: number;
	/**
	 * 平台商品id
	 */
	numIid?: string;
	/**
	 * 平台类型:tb,pdd
	 */
	platform?: string;
	/**
	 * 商品标题
	 */
	title?: string;
	/**
	 * 商品图片
	 */
	picUrl?: string;
	/**
	 * 商家编码
	 */
	outerId?: string;
	/**
	 * 商品状态
	 */
	approveStatus?: string;
	/**
	 * 平台规格-货品规格对应关系 ,SkuMatchRelationDTO
	 */
	skuRelations?: {
		/**
		 * 主键id
		 */
		id?: number;
		/**
		 * 平台skuId
		 */
		skuId?: string;
		/**
		 * 平台商品标题
		 */
		title?: string;
		/**
		 * 平台商品id
		 */
		numIid?: string;
		/**
		 * 平台规格名称
		 */
		skuName?: string;
		/**
		 * 平台规格图片
		 */
		picUrl?: string;
		/**
		 * 平台规格商家编码
		 */
		skuOuterId?: string;
		/**
		 * 0：没有匹配到货品,1:根据商家编码匹配到货品，2：已经绑定到货品
		 */
		matchType?: number;
		/**
		 * 货品规格id
		 */
		sysSkuId?: number;
		/**
		 * 货品id
		 */
		sysItemId?: number;
		/**
		 * 货品名称
		 */
		sysItemName?: string;
		/**
		 * 货品别名
		 */
		sysItemAlias?: string;
		/**
		 * 货品规格名称
		 */
		sysSkuName?: string;
		/**
		 * 货品规格别名
		 */
		sysSkuAlias?: string;
		/**
		 * 货品规格商家编码
		 */
		sysSkuOuterId?: string;
		/**
		 * 货品货号
		 */
		sysItemNo?: string;
		/**
		 * 货品条形码
		 */
		sysBarCode?: string;
		/**
		 * 货品规格图片
		 */
		sysPicUrl?: string;
		/**
		 * 是否为组合商品，1:组合货品,0:非组合货品
		 */
		isCombination?: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<PageList<ItemMatchRelationDTO>> :ResponseBody
 */
export interface ItemGetPlatformItemMatchRelationResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: MatchRelationItem[];
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * ReqDeleteItemRelationWithBatchDTO :ReqDeleteItemRelationWithBatchDTO
 */
export interface ItemSysItemDeleteItemRelationWithBatchRequest {
	/**
	 * 货品iddeleteAll为true时
	 */
	sysItemId?: number | string;
	/**
	 * 货品skuIddeleteAll为true时
	 */
	sysSkuId?: number | string;
	/**
	 * 是否解除全部
	 */
	deleteAll?: boolean;
	/**
	 * 要解除的平台商品列表 ,ReqDeleteItemRelationDTO
	 */
	reqDeleteItemRelationDTOs?: {
		/**
		 * 货品id
		 */
		sysItemId?: string;
		/**
		 * 货品skuId
		 */
		sysSkuId?: string;
		/**
		 * 平台skuId
		 */
		platformSkuId?: string;
		/**
		 * 平台商品Id
		 */
		platformNumIid?: string;
		/**
		 * 平台
		 */
		platform?: string;
		/**
		 * 店铺id
		 */
		sellerId?: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemDeleteItemRelationWithBatchResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * List<ReqSaveItemRelationDTO>
 */
export type ItemSysItemSaveItemRelationRequest = {
	/**
	 * 货品id
	 */
	sysItemId?: number;
	/**
	 * 货品skuId
	 */
	sysSkuId?: number | string;
	/**
	 * 平台skuId
	 */
	platformSkuId?: string;
	/**
	 * 平台商品Id
	 */
	platformNumIid?: string;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺id
	 */
	sellerId?: number;
	[k: string]: any;
}[];
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemSaveItemRelationResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * ReqChangeItemRelationDTO :ReqChangeItemRelationDTO
 */
export interface ItemSysItemChangeItemRelationRequest {
	/**
	 * 货品id
	 */
	sysItemId?: number;
	/**
	 * 货品skuId
	 */
	sysSkuId?: number;
	/**
	 * 平台商品id
	 */
	platformItemId?: string;
	/**
	 * 平台skuId
	 */
	platformSkuId?: string;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺id
	 */
	sellerId?: string;
	[k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemChangeItemRelationResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * ReqSelectPlatformItemListDTO :ReqSelectPlatformItemListDTO
 */
export interface ItemSysItemQueryPlatformItemListWithPageRequest {
	/**
	 * 是否关联系统商品true:是false:否
	 */
	relationSysItem?: boolean;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺
	 */
	sellerNick?: string;
	/**
	 * 店铺id
	 */
	sellerId?: string;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 货品简称
	 */
	itemAlias?: string;
	/**
	 * 商家编码
	 */
	skuOuterId?: string;
	/**
	 * 货品编码
	 */
	outerId?: string;
	/**
	 * 规格名称
	 */
	skuName?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;

	// * 未绑定商品新增字段
	numIidSkuIdListMap?: {
		[k: string]: string[]
	};
	colorOrSizeList?: string | string[];
	preciseQuery?: boolean;
	[k: string]: any;
}


export interface SelectPlatformItemSku {
	/**
	 * 简称
	 */
	itemAlias?: string;
	/**
	 * 图片
	 */
	picUrl?: string;
	/**
	 * 商家编码
	 */
	skuOuterId?: string;
	/**
	 * skuId
	 */
	skuId?: string;
	/**
	 * 规格名称
	 */
	skuName?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	/**
	 * 系统变化平台规格商家编码
	 */
	varietySkuOuterId?: string;
	[k: string]: any;
}
export interface SelectPlatformItemBase {

	/**
	 * 平台商品sku信息 ,RspBasePlatformSkuDTO
	 */
	platformItemSkuList?: SelectPlatformItemSku[];
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺昵称
	 */
	sellerNick?: string;
	/**
	 * 店铺id、
	 */
	sellerId?: number;
	/**
	 * 商品标题简称
	 */
	itemAlias?: string;
	/**
	 * 商品id
	 */
	numIid?: string;
	/**
	 * 系统变化平台商家编码
	 */
	varietyOuterId?: string;
	/**
	 * 商家编码
	 */
	outerId?: string;
	[k: string]: any;

}

/**
 * ResponseBody<PageList<RspSelectPlatformItemListDTO>> :ResponseBody
 */
export interface ItemSysItemQueryPlatformItemListWithPageResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: SelectPlatformItemBase[];
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * ItemRelationBindingDTO :ItemRelationBindingDTO
 */
export interface ItemBatchImportItemsRequest {
	/**
	 * 店铺id
	 */
	sellerId: string;
	/**
	 * 平台,PDD,TB
	 */
	platformType: string;
	/**
	 * 是否上传商家
	 */
	isUploadOuterId: boolean;
	/**
	 * 是否使用平台商品商家编码做为系统商品简称
	 */
	isUpdateAlias?: boolean;
	/**
	 * 商品-货品绑定关系 ,BindRelationDTO
	 */
	bindRelations: {
		/**
		 * 平台商品Id
		 */
		numIid: string;
		/**
		 * 商家编码
		 */
		outerId?: string;
		/**
		 * 是否需要修改商家编码
		 */
		isUpdateOuterId?: boolean;
		/**
		 * 货品简称
		 */
		sysItemAlias?: string;
		/**
		 * 平台skuId
		 */
		skuId: string;
		/**
		 * 货品id
		 */
		sysItemId?: number;
		/**
		 * 货品skuId
		 */
		sysSkuId?: number;
		/**
		 * BIND:绑定,CREATE:生成
		 */
		operateType?: string;
		/**
		 * 平台商品规格商家编码
		 */
		skuOuterId?: string;
		/**
		 * 系统商家编码,如果是绑定时不能为空
		 */
		sysSkuOuterId?: string;
		/**
		 * operateType为CREATE时,勾选之外的商品都绑定此货品 ,ItemSKu
		 */
		itemSKus?: {
			/**
			 * 平台商品Id
			 */
			numIid: string;
			/**
			 * 平台skuId
			 */
			skuId: string;
			/**
			 * 平台商品规格商家编码
			 */
			skuOuterId?: string;
			/**
			 * 老的平台商家编码，前端不传此参数
			 */
			oldSkuOuterId?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	}[];
	/**
	 * 是否需要同步0.否,1.是
	 */
	needSync?: number;
	/**
	 * 是否创建已删除的平台商品0-否1-是
	 */
	isCreateDelItem?: number;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemBatchImportItemsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * SysOuterIdQueryRequest :SysOuterIdQueryRequest
 */
export interface ItemSysSkuGetSysSkusBySkuOuterIdRequest {
	/**
	 * 根据商家编码查询货品规格 ,String
	 */
	skuOuterIds?: string[];
	/**
	 * 根据商家编码查询系统可变商品货品规格 ,String
	 */
	varietySkuOuterId?: string[];
	[k: string]: any;
}

/**
 * ResponseBody<List<SysSkuDTO>> :ResponseBody
 */
export interface ItemSysSkuGetSysSkusBySkuOuterIdResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * SysSkuDTO
	 */
	data?: {
		/**
		 * 货品规格id
		 */
		sysSkuId?: number;
		/**
		 * 货品id
		 */
		sysItemId?: number;
		/**
		 * 用户id
		 */
		userId?: number;
		/**
		 * 品牌id
		 */
		brandId?: number;
		/**
		 * 品牌id ,Long
		 */
		brandIds?: number[];
		/**
		 * 货品分类id
		 */
		classifyId?: number;
		/**
		 * 货品名称
		 */
		sysItemName?: string;
		/**
		 * 货品别名
		 */
		sysItemAlias?: string;
		/**
		 * 货品规格名称
		 */
		sysSkuName?: string;
		/**
		 * 货品规格别名
		 */
		sysSkuAlias?: string;
		/**
		 * 货品规格商家编码
		 */
		skuOuterId?: string;
		/**
		 * 货号
		 */
		itemNo?: string;
		/**
		 * 条形码
		 */
		barCode?: string;
		/**
		 * 净重
		 */
		netWeight?: string;
		/**
		 * 毛重
		 */
		weight?: string;
		/**
		 * 网销价
		 */
		price?: string;
		/**
		 * 零售价
		 */
		retailPrice?: string;
		/**
		 * 批发价
		 */
		tradePrice?: string;
		/**
		 * 吊牌价
		 */
		tagPrice?: string;
		/**
		 * 成本价
		 */
		costPrice?: string;
		/**
		 * 货品规格图片
		 */
		picUrl?: string;
		/**
		 * 关联状态,0:没有关联关系，1：一对一，2：一对多
		 */
		bindType?: number;
		/**
		 * 是否为组合商品，1:组合商品
		 */
		isCombination?: number;
		/**
		 * 库存初始化类型,0:未初始化，1：正常初始化，2：入库初始化
		 */
		stockInitType?: number;
		/**
		 * 排序值
		 */
		sort?: number;
		/**
		 * 销售状态
		 */
		saleStatus?: number;
		/**
		 * 计量单位id
		 */
		measuringUnitId?: number;
		/**
		 * 是否开启过期时间，0：不开启，1：开启
		 */
		openExpire?: number;
		/**
		 * 过期类型，0:生产日期,1:保质期
		 */
		expireType?: number;
		/**
		 * 日期单位
		 */
		dateUnit?: string;
		/**
		 * 过期时间
		 */
		date?: string;
		/**
		 * 1:推送,0:不推送
		 */
		isPushDate?: number;
		/**
		 * 单品库存总数
		 */
		stockTotal?: number;
		/**
		 * 正品库存
		 */
		salableItemStock?: number;
		/**
		 * 正品预占数量
		 */
		salableItemPreemptedNum?: number;
		/**
		 * 在途商品库存
		 */
		transitItemStock?: number;
		/**
		 * 残次品库存
		 */
		defectiveItemStock?: number;
		/**
		 * 库存预警值
		 */
		stockWarnNum?: number;
		/**
		 * 是否存在异常关联关系
		 */
		relationException?: boolean;
		/**
		 * 1：可用,0：删除
		 */
		enableStatus?: number;
		/**
		 * 添加时间
		 */
		created?: string;
		/**
		 * 添加时间-开始
		 */
		startCreated?: string;
		/**
		 * 添加时间-截止
		 */
		endCreated?: string;
		/**
		 * 修改时间
		 */
		modified?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * SysItemAliasEditRequest :SysItemAliasEditRequest
 */
export interface ItemSysItemEditSysItemAliasRequest {
	/**
	 * 货品id
	 */
	sysItemId: number;
	/**
	 * 货品简称
	 */
	sysItemAlias: string;
	[k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemEditSysItemAliasResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * SysSkuAliasEditRequest :SysSkuAliasEditRequest
 */
export interface ItemSysSkuEditSysSkuAliasRequest {
	/**
	 * 货品规格id
	 */
	sysSkuId: number;
	/**
	 * 规格别名
	 */
	sysSkuAlias: string;
	[k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysSkuEditSysSkuAliasResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}



/**
 * SysSkuRelationQueryDTO :SysSkuRelationQueryDTO
 */
export interface ItemSysItemGetRelationBySysItemRequest {
	/**
	 * 货品id
	 */
	sysItemId: number;
	/**
	 * 货品规格id
	 */
	sysSkuId: number;
	[k: string]: any;
}
/**
 * ResponseBody<List<SkuDTO>> :ResponseBody
 */
export interface ItemSysItemGetRelationBySysItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * SkuDTO
	 */
	data?: {
		/**
		 * 商品标题,表字段:title
		 */
		title?: string;
		/**
		 * 表字段:id
		 */
		id?: number;
		/**
		 * 平台skuId,表字段:sku_id
		 */
		skuId?: string;
		/**
		 * 商品id,表字段:num_iid
		 */
		numIid?: string;
		/**
		 * 用户id,表字段:user_id
		 */
		userId?: number;
		/**
		 * 是否关联系统商品1:是0:否,表字段:relation_sys_item
		 */
		relationSysItem?: boolean;
		/**
		 * 店铺昵称,表字段:seller_nick
		 */
		sellerNick?: string;
		/**
		 * 店铺id,表字段:seller_id
		 */
		sellerId?: number;
		/**
		 * 平台TB,PDD,表字段:platform
		 */
		platform?: string;
		/**
		 * 规格名称,表字段:sku_name
		 */
		skuName?: string;
		/**
		 * 规格图片,表字段:pic_url
		 */
		picUrl?: string;
		/**
		 * 规格商家编码,表字段:sku_outer_id
		 */
		skuOuterId?: string;
		/**
		 * 货号,表字段:item_no
		 */
		itemNo?: string;
		/**
		 * 条码,表字段:bar_code
		 */
		barCode?: string;
		/**
		 * 价格，单位分,表字段:price
		 */
		price?: string;
		/**
		 * 数量,表字段:num
		 */
		num?: number;
		/**
		 * 重量，单位克,表字段:weight
		 */
		weight?: string;
		/**
		 * 同步时间,表字段:sync_time
		 */
		syncTime?: string;
		/**
		 * 同步版本号,表字段:sync_version
		 */
		syncVersion?: number;
		/**
		 * 1:可用,0:不可用,表字段:enable_status
		 */
		enableStatus?: number;
		/**
		 * sku创建时间
		 */
		created?: string;
		/**
		 * sku修改时间
		 */
		modified?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
 * SysSkuUpdateDTO :SysSkuUpdateDTO
 */
export interface ItemSysItemUpdateSysItemFormPlatformRequest {
	/**
	 * 货品id
	 */
	sysItemId: number;
	/**
	 * 货品规格id
	 */
	sysSkuId: number;
	/**
	 * 平商品id
	 */
	numIid: string;
	/**
	 * 平台规格id不能为空
	 */
	skuId: string;
	/**
	 * 店铺Id
	 */
	sellerId: number;
	/**
	 * 平台类型,PDD,TB
	 */
	platformType: {
		[k: string]: any;
	};
	/**
	 * 需要更新的字段，多个字段以英文逗号隔开,sysItemName:名称,sysSkuName:规格,picUrl:图片,price:网销价,weight:毛重
	 */
	fields: string;
	[k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemUpdateSysItemFormPlatformResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * GenerateOuterIdRequest :GenerateOuterIdRequest
 */
export interface ItemSysItemGenerateOuterIdsRequest {
	/**
	 * 生成指定数量的商家编码
	 */
	count: number;
	type: number; // 1货品编码 2货品规格编码
	[k: string]: any;
}

/**
 * ResponseBody<List<String>> :ResponseBody
 */
export interface ItemSysItemGenerateOuterIdsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * String
	 */
	data?: string[];
	[k: string]: any;
}

/**
 * SysItemAliasEditRequestWithBatch :SysItemAliasEditRequestWithBatch
 */
export interface ItemSysItemEditSysItemAliasWithBatchRequest {
	/**
	 * 货品简称编辑信息 ,SysItemAliasEditInfo
	 */
	sysItemAliasEditInfos?: {
		/**
		 * 货品id
		 */
		sysItemId: number;
		/**
		 * 货品简称
		 */
		sysItemAlias: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
* ResponseBody<Void> :ResponseBody
*/
export interface ItemSysItemEditSysItemAliasWithBatchResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}



/**
 * SysItemBatchUpdateClassifyRequest :SysItemBatchUpdateClassifyRequest
 */
export interface ItemSysItemBatchUpdateClassifyRequest {
	/**
	 * 分类id
	 */
	classifyId?: string;
	// 供应商id
	supplierId?: string | number;
	sysItemIdSysSkuIdsMap?: {};
	/**
	 * 货品id ,Long
	 */
	sysItemIds?: number[];
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemBatchUpdateClassifyResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * SysSkuDeleteRequest :SysSkuDeleteRequest
 */
export interface ItemSysSkuDeleteCheckRequest {
	/**
	 * 货品id
	 */
	sysItemId: number;
	/**
	 * 货品规格id
	 */
	sysSkuId: string;
	[k: string]: any;
}
/**
 * ResponseBody<SysItemDeleteResponse> :ResponseBody
 */
export interface ItemSysSkuDeleteCheckResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * SysItemDeleteResponse
	 */
	data?: {
		/**
		 * 货品id
		 */
		sysItemId?: number;
		/**
		 * 货品规格id
		 */
		sysSkuId?: number;
		/**
		 * 是否删除成功
		 */
		isSuccess?: boolean;
		/**
		 * 失败原因
		 */
		errorMsg?: string;
		/**
		 * 错误码,0时为errorMsg报错,1时关联关系校验报错，需要取list,参考：SysItemDeleteErrorCodeEnum
		 */
		errorCode?: number;
		/**
		 * 已绑定的平台商品 ,SkuDTO
		 */
		bindPlatItems?: {
			/**
			 * 商品标题,表字段:title
			 */
			title?: string;
			/**
			 * 表字段:id
			 */
			id?: number;
			/**
			 * 平台skuId,表字段:sku_id
			 */
			skuId?: string;
			/**
			 * 商品id,表字段:num_iid
			 */
			numIid?: string;
			/**
			 * 用户id,表字段:user_id
			 */
			userId?: number;
			/**
			 * 是否关联系统商品1:是0:否,表字段:relation_sys_item
			 */
			relationSysItem?: boolean;
			/**
			 * 店铺昵称,表字段:seller_nick
			 */
			sellerNick?: string;
			/**
			 * 店铺id,表字段:seller_id
			 */
			sellerId?: number;
			/**
			 * 平台TB,PDD,表字段:platform
			 */
			platform?: string;
			/**
			 * 规格名称,表字段:sku_name
			 */
			skuName?: string;
			/**
			 * 规格图片,表字段:pic_url
			 */
			picUrl?: string;
			/**
			 * 规格商家编码,表字段:sku_outer_id
			 */
			skuOuterId?: string;
			/**
			 * 货号,表字段:item_no
			 */
			itemNo?: string;
			/**
			 * 条码,表字段:bar_code
			 */
			barCode?: string;
			/**
			 * 价格，单位分,表字段:price
			 */
			price?: string;
			/**
			 * 数量,表字段:num
			 */
			num?: number;
			/**
			 * 重量，单位克,表字段:weight
			 */
			weight?: string;
			/**
			 * 同步时间,表字段:sync_time
			 */
			syncTime?: string;
			/**
			 * 同步版本号,表字段:sync_version
			 */
			syncVersion?: number;
			/**
			 * 1:可用,0:不可用,表字段:enable_status
			 */
			enableStatus?: number;
			/**
			 * sku创建时间
			 */
			created?: string;
			/**
			 * sku修改时间
			 */
			modified?: string;
			[k: string]: any;
		}[];
		/**
		 * 若存在关联关系错误情况，则返回相关关联关系组合规格信息的集合 ,SysItemDeleteDto
		 */
		groupRelationDeleteData?: {
			/**
			 * SysSkuDTO
			 */
			sonSysSkuList?: {
				/**
				 * 商品skuId
				 */
				skuId?: string;
				/**
				 * 货品规格id
				 */
				sysSkuId?: number;
				/**
				 * 货品id
				 */
				sysItemId?: number;
				/**
				 * 用户id
				 */
				userId?: number;
				/**
				 * 品牌id
				 */
				brandId?: number;
				/**
				 * 品牌id ,Long
				 */
				brandIds?: number[];
				/**
				 * 组合规格信息集合 ,GroupCombinationRelationRecordDTO
				 */
				groupCombinationList?: {
					/**
					 * 关联表记录id主键
					 */
					id?: number;
					/**
					 * 用户ID
					 */
					userId?: number;
					/**
					 * 组合货品id
					 */
					groupSysItemId?: number;
					/**
					 * 组合规格id
					 */
					groupSysSkuId?: number;
					/**
					 * 系统货品id
					 */
					sysItemId?: number;
					/**
					 * 系统规格id
					 */
					sysSkuId?: number;
					/**
					 * 组合比例
					 */
					groupProportionNum?: number;
					/**
					 * 货品规格图片
					 */
					picUrl?: string;
					/**
					 * 货品名称
					 */
					sysItemName?: string;
					/**
					 * 货品别名
					 */
					sysItemAlias?: string;
					/**
					 * 货品规格名称
					 */
					sysSkuName?: string;
					/**
					 * 货品规格别名
					 */
					sysSkuAlias?: string;
					/**
					 * 是否为组合商品，1:组合商品
					 */
					isCombination?: number;
					/**
					 * 品牌id
					 */
					brandId?: number;
					/**
					 * 货品分类id
					 */
					classifyId?: number;
					/**
					 * 货品规格商家编码
					 */
					skuOuterId?: string;
					/**
					 * 货号
					 */
					itemNo?: string;
					/**
					 * 条形码
					 */
					barCode?: string;
					/**
					 * 净重
					 */
					netWeight?: string;
					/**
					 * 毛重
					 */
					weight?: string;
					/**
					 * 排序值
					 */
					sort?: number;
					/**
					 * 网销价
					 */
					price?: string;
					/**
					 * 零售价
					 */
					retailPrice?: string;
					/**
					 * 批发价
					 */
					tradePrice?: string;
					/**
					 * 吊牌价
					 */
					tagPrice?: string;
					/**
					 * 成本价
					 */
					costPrice?: string;
					/**
					 * 单品库存总数
					 */
					stockTotal?: number;
					/**
					 * 销售状态
					 */
					saleStatus?: number;
					/**
					 * 计量单位id
					 */
					measuringUnitId?: number;
					/**
					 * 正品库存
					 */
					salableItemStock?: number;
					/**
					 * 正品预占数量
					 */
					salableItemPreemptedNum?: number;
					/**
					 * 在途商品库存
					 */
					transitItemStock?: number;
					/**
					 * 残次品库存
					 */
					defectiveItemStock?: number;
					/**
					 * 库存预警值
					 */
					stockWarnNum?: number;
					[k: string]: any;
				}[];
				/**
				 * 货品分类id
				 */
				classifyId?: number;
				/**
				 * 货品名称
				 */
				sysItemName?: string;
				/**
				 * 货品别名
				 */
				sysItemAlias?: string;
				/**
				 * 货品规格名称
				 */
				sysSkuName?: string;
				/**
				 * 货品规格别名
				 */
				sysSkuAlias?: string;
				/**
				 * 货品规格商家编码
				 */
				skuOuterId?: string;
				/**
				 * 货号
				 */
				itemNo?: string;
				/**
				 * 条形码
				 */
				barCode?: string;
				/**
				 * 净重
				 */
				netWeight?: string;
				/**
				 * 毛重
				 */
				weight?: string;
				/**
				 * 网销价
				 */
				price?: string;
				/**
				 * 零售价
				 */
				retailPrice?: string;
				/**
				 * 批发价
				 */
				tradePrice?: string;
				/**
				 * 吊牌价
				 */
				tagPrice?: string;
				/**
				 * 成本价
				 */
				costPrice?: string;
				/**
				 * 货品规格图片
				 */
				picUrl?: string;
				/**
				 * 关联状态,0:没有关联关系，1：一对一，2：一对多
				 */
				bindType?: number;
				/**
				 * 是否为组合商品，1:组合商品
				 */
				isCombination?: number;
				/**
				 * 库存初始化类型,0:未初始化，1：正常初始化，2：入库初始化
				 */
				stockInitType?: number;
				/**
				 * 排序值
				 */
				sort?: number;
				/**
				 * 销售状态
				 */
				saleStatus?: number;
				/**
				 * 计量单位id
				 */
				measuringUnitId?: number;
				/**
				 * 是否开启过期时间，0：不开启，1：开启
				 */
				openExpire?: number;
				/**
				 * 过期类型，0:生产日期,1:保质期
				 */
				expireType?: number;
				/**
				 * 日期单位
				 */
				dateUnit?: string;
				/**
				 * 过期时间
				 */
				date?: string;
				/**
				 * 1:推送,0:不推送
				 */
				isPushDate?: number;
				/**
				 * 单品库存总数
				 */
				stockTotal?: number;
				/**
				 * 正品库存
				 */
				salableItemStock?: number;
				/**
				 * 正品预占数量
				 */
				salableItemPreemptedNum?: number;
				/**
				 * 在途商品库存
				 */
				transitItemStock?: number;
				/**
				 * 残次品库存
				 */
				defectiveItemStock?: number;
				/**
				 * 库存预警值
				 */
				stockWarnNum?: number;
				/**
				 * 是否存在异常关联关系
				 */
				relationException?: boolean;
				/**
				 * 1：可用,0：删除
				 */
				enableStatus?: number;
				/**
				 * 组合比例
				 */
				groupProportionNum?: number;
				/**
				 * 添加时间
				 */
				created?: string;
				/**
				 * 添加时间-开始
				 */
				startCreated?: string;
				/**
				 * 添加时间-截止
				 */
				endCreated?: string;
				/**
				 * 修改时间
				 */
				modified?: string;
				/**
				 * 是否当前删除操作对象
				 */
				isDeleteOperation?: boolean;
				[k: string]: any;
			}[];
			/**
			 * 商品skuId
			 */
			skuId?: string;
			/**
			 * 货品规格id
			 */
			sysSkuId?: number;
			/**
			 * 货品id
			 */
			sysItemId?: number;
			/**
			 * 用户id
			 */
			userId?: number;
			/**
			 * 品牌id
			 */
			brandId?: number;
			/**
			 * 品牌id ,Long
			 */
			brandIds?: number[];
			/**
			 * 组合规格信息集合 ,GroupCombinationRelationRecordDTO
			 */
			groupCombinationList?: {
				/**
				 * 关联表记录id主键
				 */
				id?: number;
				/**
				 * 用户ID
				 */
				userId?: number;
				/**
				 * 组合货品id
				 */
				groupSysItemId?: number;
				/**
				 * 组合规格id
				 */
				groupSysSkuId?: number;
				/**
				 * 系统货品id
				 */
				sysItemId?: number;
				/**
				 * 系统规格id
				 */
				sysSkuId?: number;
				/**
				 * 组合比例
				 */
				groupProportionNum?: number;
				/**
				 * 货品规格图片
				 */
				picUrl?: string;
				/**
				 * 货品名称
				 */
				sysItemName?: string;
				/**
				 * 货品别名
				 */
				sysItemAlias?: string;
				/**
				 * 货品规格名称
				 */
				sysSkuName?: string;
				/**
				 * 货品规格别名
				 */
				sysSkuAlias?: string;
				/**
				 * 是否为组合商品，1:组合商品
				 */
				isCombination?: number;
				/**
				 * 品牌id
				 */
				brandId?: number;
				/**
				 * 货品分类id
				 */
				classifyId?: number;
				/**
				 * 货品规格商家编码
				 */
				skuOuterId?: string;
				/**
				 * 货号
				 */
				itemNo?: string;
				/**
				 * 条形码
				 */
				barCode?: string;
				/**
				 * 净重
				 */
				netWeight?: string;
				/**
				 * 毛重
				 */
				weight?: string;
				/**
				 * 排序值
				 */
				sort?: number;
				/**
				 * 网销价
				 */
				price?: string;
				/**
				 * 零售价
				 */
				retailPrice?: string;
				/**
				 * 批发价
				 */
				tradePrice?: string;
				/**
				 * 吊牌价
				 */
				tagPrice?: string;
				/**
				 * 成本价
				 */
				costPrice?: string;
				/**
				 * 单品库存总数
				 */
				stockTotal?: number;
				/**
				 * 销售状态
				 */
				saleStatus?: number;
				/**
				 * 计量单位id
				 */
				measuringUnitId?: number;
				/**
				 * 正品库存
				 */
				salableItemStock?: number;
				/**
				 * 正品预占数量
				 */
				salableItemPreemptedNum?: number;
				/**
				 * 在途商品库存
				 */
				transitItemStock?: number;
				/**
				 * 残次品库存
				 */
				defectiveItemStock?: number;
				/**
				 * 库存预警值
				 */
				stockWarnNum?: number;
				[k: string]: any;
			}[];
			/**
			 * 货品分类id
			 */
			classifyId?: number;
			/**
			 * 货品名称
			 */
			sysItemName?: string;
			/**
			 * 货品别名
			 */
			sysItemAlias?: string;
			/**
			 * 货品规格名称
			 */
			sysSkuName?: string;
			/**
			 * 货品规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 货品规格商家编码
			 */
			skuOuterId?: string;
			/**
			 * 货号
			 */
			itemNo?: string;
			/**
			 * 条形码
			 */
			barCode?: string;
			/**
			 * 净重
			 */
			netWeight?: string;
			/**
			 * 毛重
			 */
			weight?: string;
			/**
			 * 网销价
			 */
			price?: string;
			/**
			 * 零售价
			 */
			retailPrice?: string;
			/**
			 * 批发价
			 */
			tradePrice?: string;
			/**
			 * 吊牌价
			 */
			tagPrice?: string;
			/**
			 * 成本价
			 */
			costPrice?: string;
			/**
			 * 货品规格图片
			 */
			picUrl?: string;
			/**
			 * 关联状态,0:没有关联关系，1：一对一，2：一对多
			 */
			bindType?: number;
			/**
			 * 是否为组合商品，1:组合商品
			 */
			isCombination?: number;
			/**
			 * 库存初始化类型,0:未初始化，1：正常初始化，2：入库初始化
			 */
			stockInitType?: number;
			/**
			 * 排序值
			 */
			sort?: number;
			/**
			 * 销售状态
			 */
			saleStatus?: number;
			/**
			 * 计量单位id
			 */
			measuringUnitId?: number;
			/**
			 * 是否开启过期时间，0：不开启，1：开启
			 */
			openExpire?: number;
			/**
			 * 过期类型，0:生产日期,1:保质期
			 */
			expireType?: number;
			/**
			 * 日期单位
			 */
			dateUnit?: string;
			/**
			 * 过期时间
			 */
			date?: string;
			/**
			 * 1:推送,0:不推送
			 */
			isPushDate?: number;
			/**
			 * 单品库存总数
			 */
			stockTotal?: number;
			/**
			 * 正品库存
			 */
			salableItemStock?: number;
			/**
			 * 正品预占数量
			 */
			salableItemPreemptedNum?: number;
			/**
			 * 在途商品库存
			 */
			transitItemStock?: number;
			/**
			 * 残次品库存
			 */
			defectiveItemStock?: number;
			/**
			 * 库存预警值
			 */
			stockWarnNum?: number;
			/**
			 * 是否存在异常关联关系
			 */
			relationException?: boolean;
			/**
			 * 1：可用,0：删除
			 */
			enableStatus?: number;
			/**
			 * 组合比例
			 */
			groupProportionNum?: number;
			/**
			 * 添加时间
			 */
			created?: string;
			/**
			 * 添加时间-开始
			 */
			startCreated?: string;
			/**
			 * 添加时间-截止
			 */
			endCreated?: string;
			/**
			 * 修改时间
			 */
			modified?: string;
			/**
			 * 是否当前删除操作对象
			 */
			isDeleteOperation?: boolean;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * SysItemListByPlatItemQueryDTO :SysItemListByPlatItemQueryDTO
 */
export interface ItemSysItemByPlatItemGetSysItemListRequest {
	/**
	 * 平台类型:TB,PDD,表字段:platform
	 */
	platform: string;
	/**
	 * 店铺id,表字段:seller_id
	 */
	sellerId: string;
	/**
	 * 平台商品信息 ,PlatItem
	 */
	platItemList: {
		/**
		 * 平台商品id,表字段:num_iid
		 */
		numIid?: string;
		/**
		 * 平台商品id,表字段:sku_id
		 */
		skuId?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<List<ItemRelationDTO>> :ResponseBody
 */
export interface ItemSysItemByPlatItemGetSysItemListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemRelationDTO
	 */
	data?: {
		/**
		 * 平台商品id
		 */
		numIid?: string;
		/**
		 * 平台规格id
		 */
		skuId?: string;
		/**
		 * 货品id
		 */
		sysItemId?: number;
		/**
		 * 货品规格id
		 */
		sysSkuId?: number;
		/**
		 * 毛重
		 */
		weight?: string;
		/**
		 * 货品名称
		 */
		sysItemName?: string;
		/**
		 * 货品简称
		 */
		sysItemAlias?: string;
		/**
		 * 货品规格名称
		 */
		sysSkuName?: string;
		/**
		 * 货品规格别名
		 */
		sysSkuAlias?: string;
		/**
		 * 货品规格编码
		 */
		sysSkuOuterId?: string;
		/**
		 * 货号
		 */
		itemNo?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ShopInfoRequest :ShopInfoRequest
 */
export interface ItemDeleteShopItemsRequest {
	/**
	 * 平台:PDD,TB
	 */
	platform: string;
	/**
	 * 店铺id
	 */
	sellerId: number;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemDeleteShopItemsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * GroupPackageReqDTO :GroupPackageReqDTO
 */
export interface StockGroupPackageRequest {
	/**
	 * 组合商品id
	 */
	groupSysItemId: number;
	/**
	 * 组合商品规格id
	 */
	groupSysSkuId: number;
	/**
	 * 打包类型(0打包,1拆包),参考PackageTypeEnum
	 */
	packageType: number;
	/**
	 * 打包拆包价格
	 */
	packagePrice: string;
	/**
	 * 打包数量
	 */
	packageNum: number;
	[k: string]: any;
}

/**
 * ResponseBody<GroupPackageResDTO> :ResponseBody
 */
export interface StockGroupPackageResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * GroupPackageResDTO
	 */
	data?: {
		/**
		 * 库存都足够
		 */
		allStockNumEnough?: boolean;
		/**
		 * 库存不足的规格信息 ,String
		 */
		notEnoughSkuList?: string[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqExcelErrorDTO :ReqExcelErrorDTO
 */
export interface ItemSysItemExcelExportErrorRequest {
	/**
	 * 时序号
	 */
	version?: string;
	/**
	 * 导出类型枚举,SYS_SKU：本地货品Excel导入
	 */
	errorEnum: string;
	fileUrl: string;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemExcelExportErrorResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * SysSkuExcelImportRequest :SysSkuExcelImportRequest
 */
export interface ItemSysItemExcelImportSkuRequest {
	/**
	 * MultipartFile
	 */
	file?: any;
	importType?: number;
	[k: string]: any;
}

/**
 * ResponseBody<ImportSkuVO> :ResponseBody
 */
export interface ItemSysItemExcelImportSkuResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ImportSkuVO
	 */
	data?: {
		/**
		 * 导入总条数
		 */
		totalCount?: number;
		/**
		 * 导入成功总数
		 */
		successNum?: number;
		/**
		 * 失败数量
		 */
		errorNum?: number;
		/**
		 * 暂存失败信息
		 */
		errorMsg?: string;
		/**
		 * 时序号
		 */
		version?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqSelectPlatformItemListWithPageDTO :ReqSelectPlatformItemListWithPageDTO
 */
export interface ItemItemExcelExportSkuRequest {
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺名称
	 */
	sellerNick?: string;
	/**
	 * 店铺id
	 */
	sellerId?: number;
	/**
	 * 商品id
	 */
	numIid?: string;
	/**
	 * 商品id集合 ,String
	 */
	numIids?: string[];
	/**
	 * 是否关联系统商品
	 */
	relationSysItem?: boolean;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	/**
	 * 导入类型1勾选导出2查询导出,,SysItemExportTypeEnum[CHOOSE,SELECT,type,desc]
	 */
	exportType?: number;
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 货品简称
	 */
	itemAlias?: string;
	/**
	 * 商家编码
	 */
	skuOuterId?: string;
	/**
	 * 规格名称
	 */
	skuName?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemItemExcelExportSkuResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ResponseBody<ImportSkuVO> :ResponseBody
 */
export interface ItemItemExcelImportSkuResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ImportSkuVO
	 */
	data?: {
		/**
		 * 导入总条数
		 */
		totalCount?: number;
		/**
		 * 导入成功总数
		 */
		successNum?: number;
		/**
		 * 暂存失败信息
		 */
		errorMsg?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ExcelImportProgressQueryRequest :ExcelImportProgressQueryRequest
 */
export interface ItemSysSkuExcelGetProgressRequest {
	/**
	 * 1普通货品,2组合货品,3平台货品,,ItemExcelImportTypeEnum[SYS_SKU,COMBINATION,SKU,val,desc,code]
	 */
	queryType?: number;
	[k: string]: any;
}

/**
 * SysItemQueryRequest :SysItemQueryRequest
 */
export interface ItemSysSkuExcelExportSkuRequest {
	/**
	 * 货品分类id
	 */
	classifyId?: number;
	/**
	 * 货品简称
	 */
	sysItemAlias?: string;
	/**
	 * 商家编码
	 */
	outerId?: string;
	/**
	 * 品牌id ,Long
	 */
	brandIds?: number[];
	/**
	 * 创建起始时间
	 */
	startCreated?: string;
	/**
	 * 创建起始时间
	 */
	endCreated?: string;
	/**
	 * 销售状态
	 */
	saleStatus?: number;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	/**
	 * 规格
	 */
	sysSkuName?: string;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysSkuExcelExportSkuResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ResponseBody<ItemExcelImportResultDTO> :ResponseBody
 */
export interface ItemSysSkuExcelGetProgressResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemExcelImportResultDTO
	 */
	data?: {
		userId?: number;
		/**
		 * 进度0-100
		 */
		progress?: number;
		/**
		 * 总数量
		 */
		total?: number;
		/**
		 * 当前处理数量
		 */
		count?: number;
		/**
		 * 导入成功总数
		 */
		successNum?: number;
		/**
		 * 时序号
		 */
		version?: string;
		/**
		 * 失败数量
		 */
		errorNum?: number;
		/**
		 * 同步失败时的错误信息
		 */
		errorMsg?: string;
		/**
		 * 开始同步时间
		 */
		startTime?: string;
		/**
		 * 扩展信息
		 */
		extendInfo?: string;
		/**
		 * key
		 */
		cacheKey?: string;
		/**
		 * 是否完成
		 */
		success?: boolean;
		/**
		 * 变更时间
		 */
		changeTime?: string;
		/**
		 * 错误文件地址
		 */
		errorFileUrl?: string;
		[k: string]: any;
	};
	[k: string]: any;
}



/**
 * ReqGetAsyncResultDTO :ReqGetAsyncResultDTO
 */
export interface ItemAsyncGetProgressRequest {
	asyncCode?: string;
	apiName?: string;
	[k: string]: any;
}


/**
 * ResponseBody<AsyncResult> :ResponseBody
 */
export interface ItemAsyncGetProgressResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * AsyncResult
	 */
	data?: {
		progress?: string;
		errorMsg?: string;
		success?: boolean;
		/**
		 * Object
		 */
		result?: {
			[k: string]: any;
		};
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ResponseBody<ImportSkuVO> :ResponseBody
 */
export interface ItemSysSkuExcelImportCombinationResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ImportSkuVO
	 */
	data?: {
		/**
		 * 导入总条数
		 */
		totalCount?: number;
		/**
		 * 导入成功总数
		 */
		successNum?: number;
		/**
		 * 暂存失败信息
		 */
		errorMsg?: string;
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * ResponseBody<ImportSkuVO> :ResponseBody
 */
export interface ItemSysSkuExcelImportCombinationRequest {
	file?: any;
	importType?: number;
	[k: string]: any;
}

/**
 * ItemPlatformOuterIdUpdateDTO :ItemPlatformOuterIdUpdateDTO
 */
export interface ItemBatchUpdatePlatformOuterIdsRequest {
	/**
	 * 是否上传更新商家编码0-否1-是
	 */
	isUpLoadOuterId?: number;
	/**
	 * 是否上传规格编码0-否1-是
	 */
	isUpLoadSkuOuterId?: number;
	/**
	 * 商品及对应规格集合 ,ItemSkuUpdateDTO
	 */
	itemSkuUpdateDTOList?: {
		/**
		 * numIid
		 */
		numIid?: string;
		/**
		 * skuIds
		 */
		skuId?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<List<OuterIdUploadResultDTO>> :ResponseBody
 */
export interface ItemBatchUpdatePlatformOuterIdsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * OuterIdUploadResultDTO
	 */
	data?: {
		failUploadOuterIds?: any[];
		failUploadSkuOuterIds: any[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * StockInfoListQueryRequest :StockInfoListQueryRequest
 */
export interface StockExportRequest {
	/**
	 * 货品简称\名称
	 */
	sysItemInfo?: string;
	/**
	 * 货品规格商家编码
	 */
	skuOuterId?: string;
	/**
	 * 规格名称\别名
	 */
	sysSkuInfo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 排序方式,1.正品库存升序,2.正品库存降序
	 */
	sort?: number;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	[k: string]: any;
}


/**
 * ResponseBody<Void> :ResponseBody
 */
export interface StockImportResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqUpdateSysSkuInfoWithBatchDTO :ReqUpdateSysSkuInfoWithBatchDTO
 */
export interface ItemSysItemUpdateSysSkuInfoWithBatchRequest {
	/**
	 * 货品规格成本价
	 */
	sysSkuCostPrice?: string;
	/**
	 * 重量
	 */
	weight?: string;
	/**
	 * 售价
	 */
	price?: string;
	/**
	 * 批量修改货品规格成本价信息集合 ,UpdateSysSkuInfo
	 */
	updateSysSkuInfos?: {
		/**
		 * 货品id
		 */
		sysItemId?: number;
		/**
		 * 货品规格id
		 */
		sysSkuId?: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<List<BatchResult>> :ResponseBody
 */
export interface ItemSysItemUpdateSysSkuInfoWithBatchResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchResult
	 */
	data?: {
		operationId?: string;
		success?: boolean;
		errorCode?: number;
		errorMessage?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * BatchChangeSyncStockFlagRequest :BatchChangeSyncStockFlagRequest
 */
export interface ItemSysSkuBatchChangeSyncStockFlagRequest {
	/**
	 * SysSkuInfo
	 */
	sysSkuInfos?: {
		sysSkuId: number;
		/**
		 * 是否开启库存同步：1开启0不开启-1未设置；默认-1
		 */
		syncStockFlag: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<BatchChangeSyncStockFlagResponse> :ResponseBody
 */
export interface ItemSysSkuBatchChangeSyncStockFlagResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchChangeSyncStockFlagResponse
	 */
	data?: {
		/**
		 * SysSkuInfo
		 */
		sysSkuInfos?: {
			sysSkuId?: number;
			/**
			 * 是否开启库存同步：1开启0不开启-1未设置；默认-1
			 */
			syncStockFlag?: number;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqBatchUpdateDropShippingItemFlagRequest :ReqBatchUpdateDropShippingItemFlagRequest
 */
export interface ItemScmSaleBatchUpdateDropShippingItemFlagRequest {
	/**
	 * SkuUpdate
	 */
	skuUpdateList: {
		/**
		 * 平台商品Id
		 */
		numIid: string;
		/**
		 * 平台skuId
		 */
		skuId: string;
		/**
		 * 店铺id
		 */
		sellerId: number;
		/**
		 * 平台
		 */
		platform: string;
		/**
		 * 是否代发商品规格1是0否默认0----分销商使用字段
		 */
		dropShippingFlag: number;
		/**
		 * 老代发商品规格设置
		 */
		oldDropShippingFlag: number;
		/**
		 * 供应商用户id
		 */
		supplierUserId: string;
		sysSkuId: number;
		sysItemId: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
* ResponseBody<Void> :ResponseBody
*/
export interface ItemScmSaleBatchUpdateDropShippingItemFlagResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ResponseBody<PageList<SysSkuDTO>> :ResponseBody
 */
export interface ItemSysSkuGetSysSkuPageListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: {
			id?: number;
			/**
			 * 商品skuId
			 */
			skuId?: string;
			sellerId?: number;
			platform?: string;
			/**
			 * 货品规格id
			 */
			sysSkuId?: number;
			/**
			 * 货品规格id集合 ,Long
			 */
			sysSkuIdList?: number[];
			/**
			 * 是否分页
			 */
			needPage?: boolean;
			/**
			 * 货品id
			 */
			sysItemId?: number;
			/**
			 * 用户id
			 */
			userId?: number;
			/**
			 * 品牌id
			 */
			brandId?: number;
			/**
			 * 品牌名称
			 */
			brandName?: string;
			/**
			 * 品牌id ,Long
			 */
			brandIds?: number[];
			/**
			 * 组合规格信息集合 ,GroupCombinationRelationRecordDTO
			 */
			groupCombinationList?: {
				/**
				 * 关联表记录id主键
				 */
				id?: number;
				/**
				 * 用户ID
				 */
				userId?: number;
				/**
				 * 组合货品id
				 */
				groupSysItemId?: number;
				/**
				 * 组合规格id
				 */
				groupSysSkuId?: number;
				/**
				 * 新增情况零时储存组合item货品id
				 */
				saveTempGroupSysItemId?: number;
				/**
				 * 新增情况零时储存组合sku规格id
				 */
				saveTempGroupSysSkuId?: number;
				/**
				 * 系统货品id
				 */
				sysItemId?: number;
				/**
				 * 系统规格id
				 */
				sysSkuId?: number;
				/**
				 * 组合比例
				 */
				groupProportionNum?: number;
				/**
				 * 货品规格图片
				 */
				picUrl?: string;
				/**
				 * 货品名称
				 */
				sysItemName?: string;
				/**
				 * 货品别名
				 */
				sysItemAlias?: string;
				/**
				 * 货品规格名称
				 */
				sysSkuName?: string;
				/**
				 * 货品规格别名
				 */
				sysSkuAlias?: string;
				/**
				 * 是否为组合商品，1:组合商品
				 */
				isCombination?: number;
				/**
				 * 品牌id
				 */
				brandId?: number;
				/**
				 * 货品分类id
				 */
				classifyId?: number;
				/**
				 * 货品规格商家编码
				 */
				skuOuterId?: string;
				/**
				 * 货号
				 */
				itemNo?: string;
				/**
				 * 条形码
				 */
				barCode?: string;
				/**
				 * 净重
				 */
				netWeight?: string;
				/**
				 * 毛重
				 */
				weight?: string;
				/**
				 * 排序值
				 */
				sort?: number;
				/**
				 * 网销价
				 */
				price?: string;
				/**
				 * 零售价
				 */
				retailPrice?: string;
				/**
				 * 批发价
				 */
				tradePrice?: string;
				/**
				 * 吊牌价
				 */
				tagPrice?: string;
				/**
				 * 成本价
				 */
				costPrice?: string;
				/**
				 * 单品库存总数
				 */
				stockTotal?: number;
				/**
				 * 销售状态
				 */
				saleStatus?: number;
				/**
				 * 计量单位id
				 */
				measuringUnitId?: number;
				/**
				 * 正品库存
				 */
				salableItemStock?: number;
				/**
				 * 正品预占数量
				 */
				salableItemPreemptedNum?: number;
				/**
				 * 可配货库存数量
				 */
				salableItemDistributableStock?: string;
				/**
				 * 在途商品库存
				 */
				transitItemStock?: number;
				/**
				 * 残次品库存
				 */
				defectiveItemStock?: number;
				/**
				 * 库存预警值
				 */
				stockWarnNum?: number;
				/**
				 * 主货品编码
				 */
				outerId?: string;
				[k: string]: any;
			}[];
			/**
			 * 货品分类id
			 */
			classifyId?: number;
			/**
			 * 分类名称
			 */
			classifyName?: string;
			/**
			 * 货品名称
			 */
			sysItemName?: string;
			/**
			 * 货品别名
			 */
			sysItemAlias?: string;
			/**
			 * 货品规格名称
			 */
			sysSkuName?: string;
			/**
			 * 货品规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 货品规格商家编码
			 */
			skuOuterId?: string;
			/**
			 * 主货品编码
			 */
			outerId?: string;
			/**
			 * 货号
			 */
			itemNo?: string;
			/**
			 * 条形码
			 */
			barCode?: string;
			/**
			 * 净重
			 */
			netWeight?: string;
			/**
			 * 毛重
			 */
			weight?: string;
			/**
			 * 网销价
			 */
			price?: string;
			/**
			 * 零售价
			 */
			retailPrice?: string;
			/**
			 * 批发价
			 */
			tradePrice?: string;
			/**
			 * 吊牌价
			 */
			tagPrice?: string;
			/**
			 * 成本价
			 */
			costPrice?: string;
			/**
			 * 货品规格图片
			 */
			picUrl?: string;
			/**
			 * 关联状态,0:没有关联关系，1：一对一，2：一对多
			 */
			bindType?: number;
			/**
			 * 是否为组合商品，1:组合商品,,CombinationEnum[NORMAL(货品与商品关联状态枚举),GROUP,code,desc]
			 */
			isCombination?: number;
			/**
			 * 库存初始化类型,0:未初始化，1：正常初始化，2：入库初始化
			 */
			stockInitType?: number;
			/**
			 * 排序值
			 */
			sort?: number;
			/**
			 * 销售状态
			 */
			saleStatus?: number;
			/**
			 * 计量单位id
			 */
			measuringUnitId?: number;
			/**
			 * 是否开启过期时间，0：不开启，1：开启
			 */
			openExpire?: number;
			/**
			 * 过期类型，0:生产日期,1:保质期
			 */
			expireType?: number;
			/**
			 * 日期单位
			 */
			dateUnit?: string;
			/**
			 * 过期时间
			 */
			date?: string;
			/**
			 * 1:推送,0:不推送
			 */
			isPushDate?: number;
			/**
			 * 单品库存总数
			 */
			stockTotal?: number;
			/**
			 * 正品库存
			 */
			salableItemStock?: number;
			/**
			 * 正品预占数量
			 */
			salableItemPreemptedNum?: number;
			/**
			 * 可配货库存数量
			 */
			salableItemDistributableStock?: number;
			/**
			 * 在途商品库存
			 */
			transitItemStock?: number;
			/**
			 * 残次品库存
			 */
			defectiveItemStock?: number;
			/**
			 * 库存预警值
			 */
			stockWarnNum?: number;
			/**
			 * 是否存在异常关联关系
			 */
			relationException?: boolean;
			/**
			 * 1：可用,0：删除
			 */
			enableStatus?: number;
			/**
			 * 组合比例
			 */
			groupProportionNum?: number;
			/**
			 * 添加时间
			 */
			created?: string;
			/**
			 * 添加时间-开始
			 */
			startCreated?: string;
			/**
			 * 添加时间-截止
			 */
			endCreated?: string;
			/**
			 * 修改时间
			 */
			modified?: string;
			/**
			 * 是否当前删除操作对象
			 */
			isDeleteOperation?: boolean;
			/**
			 * 市场
			 */
			market?: string;
			/**
			 * 档口
			 */
			stall?: string;
			/**
			 * 供应商id
			 */
			supplierId?: number;
			/**
			 * 供应商名称
			 */
			supplierName?: string;
			/**
			 * 供应商地址-省
			 */
			storageAddrProvince?: string;
			/**
			 * 供应商地址-市
			 */
			storageAddrCity?: string;
			/**
			 * 供应商地址-区
			 */
			storageAddrDistrict?: string;
			/**
			 * 供应商地址-详细地址
			 */
			storageAddr?: string;
			/**
			 * excel导入行数
			 */
			index?: number;
			/**
			 * excel导入错误信息
			 */
			excelImportErrorMsg?: string;
			numIid?: string;
			sellerNick?: string;
			/**
			 * 子货品货品编码
			 */
			groupOuterId?: string;
			/**
			 * 子货品货品简称
			 */
			groupSysItemAlias?: string;
			/**
			 * 子货品货品规格编码
			 */
			groupSkuOuterId?: string;
			/**
			 * 子货品货品规格名称
			 */
			groupSysSkuName?: string;
			/**
			 * 组合比例
			 */
			groupProportionNumStr?: string;
			/**
			 * 组合货品配置项id
			 */
			combinationConfigId?: number;
			/**
			 * 是否自动计算成本价0否1是
			 */
			autoCostPrice?: number;
			/**
			 * 是否自动计算重量0否1是
			 */
			autoWeight?: number;
			/**
			 * 是否开启库存同步：1开启0不开启-1未设置；默认-1
			 */
			syncStockFlag?: number;
			/**
			 * 大写的货品规格编码
			 */
			upperCaseSysSKuOuterId?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * SysSkuQueryRequest :SysSkuQueryRequest
 */
export interface ItemSysSkuGetSysSkuPageListRequest {
	/**
	 * 搜索关键字
	 */
	searchKey?: string;
	/**
	 * 货品规格商家编码
	 */
	skuOuterId?: string;
	/**
	 * 货号
	 */
	itemNo?: string;
	/**
	 * 条形码
	 */
	barCode?: string;
	/**
	 * 供应商
	 */
	supplierName?: string;
	/**
	 * 市场
	 */
	market?: string;
	/**
	 * 档口
	 */
	stall?: string;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	[k: string]: any;
}

/**
 * ReqSelectLabelPushPlatformItemDTO :ReqSelectLabelPushPlatformItemDTO
 */
export interface ItemTakeGoodsLabelSelectLabelPush2PlatformItemWithPageRequest {
	/**
	 * 店铺id
	 */
	shopId?: number;
	/**
	 * 标题
	 */
	title?: string;
	/**
	 * 货号
	 */
	codeNum?: string;
	/**
	 * NHB
	 * YP
	 *
	 */
	labelPushPlatform?: string;
	pageNo?: number;
	pageSize?: number;
	[k: string]: any;
  }
  /**
 * ResponseBody<PageList<RspSelectLabelPushPlatformItemDTO>> :ResponseBody
 */
export interface ItemTakeGoodsLabelSelectLabelPush2PlatformItemWithPageResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
	  pageNo?: number;
	  pageSize?: number;
	  total?: number;
	  /**
	   * T
	   */
	  list?: {
		/**
		 * 商品id
		 */
		numIid?: string;
		/**
		 * 商品标题
		 */
		title?: string;
		/**
		 * 货号
		 */
		codeNum?: string;
		/**
		 * 商品图片
		 */
		picUrl?: string;
		/**
		 * 商品规格信息 ,LabelPushPlatformSkuInfo
		 */
		ypSkuInfoList?: {
		  /**
		   * 规格Id
		   */
		  skuId?: string;
		  /**
		   * 规格编码
		   */
		  skuCode?: string;
		  /**
		   * 价格
		   */
		  price?: string;
		  /**
		   * 规格图片
		   */
		  skuPicUrl?: string;
		  /**
		   * 规格名称
		   */
		  skuName?: string;
		  [k: string]: any;
		}[];
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * ReqBindLabelPushPlatformItemDTO :ReqBindLabelPushPlatformItemDTO
 */
export interface ItemTakeGoodsLabelBindLabelPushPlatformItemRequest {
	/**
	 * 标签推送平台
	 */
	labelPushPlatform?: labelPushPlatformEnum;
	/**
	 * BindInfo
	 */
	bindInfoList?: {
	  /**
	   * 商品所属平台商品id
	   */
	  numIid?: string;
	  /**
	   * 商品规格id
	   */
	  skuId?: string;
	  /**
	   * 商品所属店铺id
	   */
	  sellerId?: number;
	  /**
	   * 商品所属卖家昵称
	   */
	  sellerNick?: string;
	  /**
	   * 商品所属平台类型:TB,PDD
	   */
	  platform?: string;
	  /**
	   * 标签推送平台站点id
	   */
	  platformSiteId?: number;
	  /**
	   * 标签推送平台站点名称
	   */
	  platformSiteName?: string;
	  /**
	   * 标签推送平台市场id
	   */
	  platformMarketId?: number;
	  /**
	   * 标签推送平台市场名称
	   */
	  platformMarketName?: string;
	  /**
	   * 标签推送平台商品标题
	   */
	  platformItemTitle?: string;
	  /**
	   * 标签推送平台商品货号
	   */
	  platformItemNo?: string;
	  /**
	   * 标签推送平台商品id
	   */
	  platformNumIid?: string;
	  /**
	   * 标签推送平台商品规格Id
	   */
	  platformSkuId?: string;
	  /**
	   * 标签推送平台商品规格编码
	   */
	  platformSkuOuterId?: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemTakeGoodsLabelBindLabelPushPlatformItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * ReqUnBindLabelPushPlatformItemDTO :ReqUnBindLabelPushPlatformItemDTO
 */
export interface ItemTakeGoodsLabelUnBindLabelPushPlatformItemRequest {
	/**
	 * 标签推送平台
	 */
	labelPushPlatform?: labelPushPlatformEnum;
	/**
	 * UnBindInfo
	 */
	unBindInfoList?: {
	  /**
	   * 商品所属平台商品id
	   */
	  numIid?: string;
	  /**
	   * 商品规格id
	   */
	  skuId?: string;
	  /**
	   * 标签推送平台商品id
	   */
	  platformNumIid?: string;
	  /**
	   * 标签推送平台商品规格Id
	   */
	  platformSkuId?: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemTakeGoodsLabelUnBindLabelPushPlatformItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * ReqChangeBindLabelPushPlatformItemDTO :ReqChangeBindLabelPushPlatformItemDTO
 */
export interface ItemTakeGoodsLabelChangeBindLabelPushPlatformItemRequest {
	/**
	 * 标签推送平台
	 */
	labelPushPlatform?: labelPushPlatformEnum;
	/**
	 * 更换绑定 ,ChangeBindInfo
	 */
	changeBindInfoList?: {
	  /**
	   * 商品所属平台商品id
	   */
	  numIid?: string;
	  /**
	   * 商品规格id
	   */
	  skuId?: string;
	  /**
	   * 更换前标签推送平台商品id
	   */
	  changeBeforePlatformNumIid?: string;
	  /**
	   * 更换前标签推送平台商品规格Id
	   */
	  changeBeforePlatformSkuId?: string;
	  /**
	   * 更换后的商品信息 ,BindInfo
	   */
	  bindInfo?: {
		/**
		 * 商品所属平台商品id
		 */
		numIid?: string;
		/**
		 * 商品规格id
		 */
		skuId?: string;
		/**
		 * 商品所属店铺id
		 */
		sellerId?: number;
		/**
		 * 商品所属卖家昵称
		 */
		sellerNick?: string;
		/**
		 * 商品所属平台类型:TB,PDD
		 */
		platform?: string;
		/**
		 * 标签推送平台站点id
		 */
		platformSiteId?: number;
		/**
		 * 标签推送平台站点名称
		 */
		platformSiteName?: string;
		/**
		 * 标签推送平台市场id
		 */
		platformMarketId?: number;
		/**
		 * 标签推送平台市场名称
		 */
		platformMarketName?: string;
		/**
		 * 标签推送平台商品标题
		 */
		platformItemTitle?: string;
		/**
		 * 标签推送平台商品货号
		 */
		platformItemNo?: string;
		/**
		 * 标签推送平台商品id
		 */
		platformNumIid?: string;
		/**
		 * 标签推送平台商品规格Id
		 */
		platformSkuId?: string;
		/**
		 * 标签推送平台商品规格编码
		 */
		platformSkuOuterId?: string;
		/**
		 * 标签推送平台商品规格名称
		 */
		platformSkuName?: string;
		/**
		 * 标签推送平台商品商品图片
		 */
		platformItemPicUrl?: string;
		/**
		 * 标签推送平台商品规格图片
		 */
		platformSkuPicUrl?: string;
		[k: string]: any;
	  };
	  [k: string]: any;
	}[];
	[k: string]: any;
  }
  /**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemTakeGoodsLabelChangeBindLabelPushPlatformItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * CommonItemDTO :CommonItemDTO
 */
export interface ItemCommonItemGetCommonItemListRequest {
	/**
	 * 表字段:id
	 */
	id?: number;
	/**
	 * 系统用户子账号id,表字段:user_id
	 */
	userId?: number;
	/**
	 * 子账号用户id,表字段:sub_user_id
	 */
	subUserId?: number;
	/**
	 * 平台skuId,表字段:sku_id
	 */
	skuId?: string;
	/**
	 * 商品id,表字段:num_iid
	 */
	numIid?: string;
	/**
	 * 货品规格id,表字段:sys_sku_id
	 */
	sysSkuId?: number;
	/**
	 * 货品id,表字段:sys_item_id
	 */
	sysItemId?: number;
	/**
	 * 添加时间,表字段:gmt_created
	 */
	gmtCreated?: string;
	/**
	 * 修改时间,表字段:gmt_modified
	 */
	gmtModified?: string;
	/**
	 * 1:可用,0:不可用,表字段:enable_status
	 */
	enableStatus?: number;
	/**
	 * 1:新增,2:删除
	 */
	type?: number;
	[k: string]: any;
  }
  /**
 * ResponseBody<PageList<CommonItemDTO>> :ResponseBody
 */
export interface ItemCommonItemGetCommonItemListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
	  pageNo?: number;
	  pageSize?: number;
	  total?: number;
	  /**
	   * T
	   */
	  list?: {
		/**
		 * 表字段:id
		 */
		id?: number;
		/**
		 * 系统用户子账号id,表字段:user_id
		 */
		userId?: number;
		/**
		 * 子账号用户id,表字段:sub_user_id
		 */
		subUserId?: number;
		/**
		 * 平台skuId,表字段:sku_id
		 */
		skuId?: string;
		/**
		 * 商品id,表字段:num_iid
		 */
		numIid?: string;
		/**
		 * 货品规格id,表字段:sys_sku_id
		 */
		sysSkuId?: number;
		/**
		 * 货品id,表字段:sys_item_id
		 */
		sysItemId?: number;
		/**
		 * 添加时间,表字段:gmt_created
		 */
		gmtCreated?: string;
		/**
		 * 修改时间,表字段:gmt_modified
		 */
		gmtModified?: string;
		/**
		 * 1:可用,0:不可用,表字段:enable_status
		 */
		enableStatus?: number;
		/**
		 * 1:新增,2:删除
		 */
		type?: number;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }


/**
 * CommonItemDTO :CommonItemDTO
 */
export interface ItemCommonItemSaveOrDelRequest {
	/**
	 * 表字段:id
	 */
	id?: number;
	/**
	 * 系统用户子账号id,表字段:user_id
	 */
	userId?: number;
	/**
	 * 子账号用户id,表字段:sub_user_id
	 */
	subUserId?: number;
	/**
	 * 平台skuId,表字段:sku_id
	 */
	skuId?: string;
	/**
	 * 商品id,表字段:num_iid
	 */
	numIid?: string;
	/**
	 * 货品规格id,表字段:sys_sku_id
	 */
	sysSkuId?: number;
	/**
	 * 货品id,表字段:sys_item_id
	 */
	sysItemId?: number;
	/**
	 * 添加时间,表字段:gmt_created
	 */
	gmtCreated?: string;
	/**
	 * 修改时间,表字段:gmt_modified
	 */
	gmtModified?: string;
	/**
	 * 1:可用,0:不可用,表字段:enable_status
	 */
	enableStatus?: number;
	/**
	 * 1:新增,2:删除
	 */
	type?: number;
	[k: string]: any;
  }
  /**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface ItemCommonItemSaveOrDelResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
  }

/**
 * SysSkuEditRequest :SysSkuEditRequest
 */
export interface ItemSysSkuEditSysSkuRequest {
	/**
	 * 货品规格id
	 */
	sysSkuId: number;
	/**
	 * 货品规格备注
	 */
	sysSkuMemo?: string;
	[k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysSkuEditSysSkuResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }



/**
 * ReqCreateCombinationBindingOpsRequest :ReqCreateCombinationBindingOpsRequest
 */
export interface ItemSysItemCreateCombinationBindingRequest {
	/**
	 * ItemSku
	 */
	itemSkuList: {
	  sellerId: number;
	  platform: string;
	  skuId: string;
	  numIid: string;
	  [k: string]: any;
	}[];
	/**
	 * 子货品规格信息 ,GroupRelationRecordInfo
	 */
	groupRelationRecordInfoList: {
	  sysItemId: number;
	  sysSkuId: number;
	  /**
	   * 组合比例
	   */
	  groupProportionNum: number;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }
  /**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemCreateCombinationBindingResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }


/**
 * UpdateSysSkuInfoWithBatchByPlatformRequest :UpdateSysSkuInfoWithBatchByPlatformRequest
 */
export interface ItemSysItemUpdateSysSkuInfoWithBatchByPlatformRequest {
  /**
   * 是否为组合商品，0:普通商品1:组合商品,,CombinationEnum[NORMAL(货品与商品关联状态枚举),GROUP,code,desc]
   */
  isCombination?: number;
  /**
   * 货品规格id列表 ,Long
   */
  sysSkuIdList?: number[];
  /**
   * 货品简称填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  sysItemAliasFillModel?: string;
  /**
   * 规格图填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  sysSkuPicUrlFillModel?: string;
  /**
   * 规格图平台填充字段说明,1、先获取规格图片，无时取商品图片（默认）2、取商品图片
   */
  skuPicUrlGetRule?: string;
  /**
   * 规格名称填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  sysSkuNameFillModel?: string;
  /**
   * 规格别名填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  sysSkuAliasFillModel?: string;
  /**
   * 规格别名平台填充字段说明,1、取规格编码（默认）,2、取规格名称,3、先取规格编码，无时取规格名称,4、取商品编码
   */
  sysSkuAliasGetRule?: string;
  /**
   * 售价填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  priceFillModel?: string;
  /**
   * 成本价填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  costFillModel?: string;
  /**
   * 成本价平台填充字段说明商品销售价比例，默认100%
   */
  costPriceGetRule?: number;
  /**
   * 重量填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  weightFillModel?: string;
  /**
   * 货号填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  itemNoFillModel?: string;
  /**
   * 颜色填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  sysColorFillModel?: string;
  /**
   * 尺码填充模式不覆盖已有值：not_cover覆盖已有值：cover
   */
  sysSizeFillModel?: string;
  [k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemUpdateSysSkuInfoWithBatchByPlatformResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ItemQueryRecommendRequest :ItemQueryRecommendRequest
 */
export interface ItemSysItemQueryRecommendItemRequest {
	/**
	 * 推荐合并规则枚举
	 */
	itemRecommendTypeEnum?: {
	  [k: string]: any;
	};
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小由于订单页面运单号搜索扩大为1000这里也限制为1000
	 */
	pageSize?: number;
	[k: string]: any;
  }

/**
 * ResponseBody<ItemQueryRecommendResponse> :ResponseBody
 */
export interface ItemSysItemQueryRecommendItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemQueryRecommendResponse
	 */
	data?: {
	  /**
	   * 推荐合并类型枚举
	   */
	  recommendTypeEnum?: {
		[k: string]: any;
	  };
	  /**
	   * ItemRecommendGroupVo
	   */
	  list?: {
		/**
		 * ItemRecommendVo
		 */
		itemRecommendVoList?: {
		  /**
		   * 商品标题
		   */
		  title?: string;
		  /**
		   * 商品id
		   */
		  numIid?: string;
		  /**
		   * 商品规格id
		   */
		  skuId?: string;
		  /**
		   * 店铺id
		   */
		  sellerId?: number;
		  /**
		   * 店铺名称
		   */
		  sellerNick?: string;
		  /**
		   * 店铺
		   */
		  platform?: string;
		  /**
		   * 平台商品编码
		   */
		  outerId?: string;
		  /**
		   * 平台规格编码
		   */
		  skuOuterId?: string;
		  /**
		   * 平台规格库存
		   */
		  stockNum?: number;
		  /**
		   * 平台售价
		   */
		  price?: string;
		  /**
		   * 平台规格图片
		   */
		  picUrl?: string;
		  /**
		   * 平台商品图片
		   */
		  itemPicUrl?: string;
		  /**
		   * 本地货品信息 ,SysSkuInfo
		   */
		  sysSkuInfo?: {
			/**
			 * 本地货品id
			 */
			sysItemId?: number;
			/**
			 * 本地货品规格id
			 */
			sysSkuId?: number;
			/**
			 * 本地货品简称
			 */
			sysItemAlias?: string;
			/**
			 * 本地货品规格名称
			 */
			sysSkuName?: string;
			/**
			 * 本地货品规格编码
			 */
			sysSkuOuterId?: string;
			/**
			 * 本地货品规格价格
			 */
			sysPrice?: string;
			/**
			 * 本地货品规格正品库存
			 */
			salableItemStock?: number;
			/**
			 * 组合货品
			 */
			isCombination?: number;
			/**
			 * 本地货品规格图片
			 */
			sysPicUrl?: string;
			[k: string]: any;
		  };
		  /**
		   * 分组key
		   */
		  groupKey?: string;
		  [k: string]: any;
		}[];
		/**
		 * 分组key
		 */
		groupKey?: string;
		/**
		 * ITEM_TITLE_SKU_NAME_SAME
		 *
		 */
		itemRecommendTypeEnum?: {
		  [k: string]: any;
		};
		[k: string]: any;
	  }[];
	  total?: number;
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * ItemRecommendMergeRequest :ItemRecommendMergeRequest
 */
export interface ItemSysItemRecommendItemMergeRequest {
	/**
	 * 推荐合并规则枚举
	 */
	itemRecommendTypeEnum: {
	  [k: string]: any;
	};
	/**
	 * ItemGroup
	 */
	groupList: {
	  groupKey: string;
	  /**
	   * ItemInfo
	   */
	  itemInfoList: {
		numIid: string;
		skuId: string;
		sellerId: number;
		platform: string;
		/**
		 * 是否标准商品规格T是F否
		 */
		defaultFlag: boolean;
		[k: string]: any;
	  }[];
	  sysSkuId?: number;
	  sysItemId?: number;
	  /**
	   * 本地货品简称
	   */
	  sysItemAlias?: string;
	  /**
	   * 本地货品规格编码
	   */
	  sysSkuOuterId?: string;
	  /**
	   * 本地货品规格名称
	   */
	  sysSkuName?: string;
	  /**
	   * 本地货品规格正品库存
	   */
	  salableItemStock?: number;
	  /**
	   * 本地货品规格价格
	   */
	  sysPrice?: string;
	  /**
	   * 操作类型新建CREATE更换BIND,,ItemRecommendOpsTypeEnum[CREATE,BIND,opsType,opsName]
	   */
	  opsType: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemSysItemRecommendItemMergeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }
  
/**
 * ResponseBody<UserCustomSetDTO> :ResponseBody
 */
export interface ItemSysItemUserGetItemRelationUserCustomSetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * UserCustomSetDTO
	 */
	data?: {
	  /**
	   * 自增长ID,表字段:id
	   */
	  id?: number;
	  /**
	   * 系统用户子账号id,表字段:user_id
	   */
	  userId?: number;
	  /**
	   * 子账号用户id,表字段:sub_user_id
	   */
	  subUserId?: number;
	  /**
	   * 1表示单列2表示双列,表字段:show_column
	   */
	  showColumn?: number;
	  /**
	   * 是否显示商品标题0为不展示,表字段:show_item_title
	   */
	  showItemTitle?: number;
	  /**
	   * 是否显示商品简称0为不展示,表字段:show_short_title
	   */
	  showShortTitle?: number;
	  /**
	   * 是否显示商家编码0为不展示,表字段:show_outer_id
	   */
	  showOuterId?: number;
	  /**
	   * 是否显示商品图片0为不展示,表字段:show_picture
	   */
	  showPicture?: number;
	  /**
	   * 是否显示规格名称0为不展示,表字段:show_sku_title
	   */
	  showSkuTitle?: number;
	  /**
	   * 是否显示规格别名0为不展示,表字段:show_sku_alias
	   */
	  showSkuAlias?: number;
	  /**
	   * 是否显示规格编码0为不展示,表字段:show_sku_outer_id
	   */
	  showSkuOuterId?: number;
	  /**
	   * 库存预警1开启0不开启,表字段:show_stock_warn
	   */
	  showStockWarn?: number;
	  /**
	   * 删除标记,表字段:enable_status
	   */
	  enableStatus?: number;
	  /**
	   * 创建时间,表字段:created
	   */
	  created?: string;
	  /**
	   * 更新时间,表字段:modified
	   */
	  modified?: string;
	  /**
	   * 是否显示货品图片0为不展示,表字段:show_sys_pic_path
	   */
	  showSysPicPath?: number;
	  /**
	   * 是否显示货品编码0为不展示,表字段:show_sys_outer_id
	   */
	  showSysOuterId?: number;
	  /**
	   * 是否显示货品规格编码0为不展示,表字段:show_sys_outer_sku_id
	   */
	  showSysOuterSkuId?: number;
	  /**
	   * 显示商品选择框true显示false不显示
	   */
	  showGoodsSelect?: boolean;
	  /**
	   * 部分发货商品提醒0为不展示
	   */
	  showPartConsignItems?: number;
	  /**
	   * 图片展示大小0:小图1:中图2:大图
	   */
	  showPicSize?: number;
	  /**
	   * 配置类型，隔离不同服务
	   */
	  type?: number;
	  /**
	   * 扩展字段 ,CustomSetJson
	   */
	  customSetJson?: {
		/**
		 * 显示商品库存默认false
		 */
		showGoodsStockBox?: boolean;
		/**
		 * 显示货品库存情况默认false
		 */
		showSysGoodsStockBox?: boolean;
		/**
		 * 显示平台规格名称
		 */
		showPlatformSkuName?: boolean;
		/**
		 * 显示平台规格id
		 */
		showPlatformSkuId?: boolean;
		/**
		 * 显示平台条形码
		 */
		showPlatformBarCode?: boolean;
		/**
		 * 显示平台货品
		 */
		showPlatformItemNo?: boolean;
		/**
		 * 显示平台规格售价
		 */
		showPlatformSkuPrice?: boolean;
		/**
		 * 显示系统货品规格名称
		 */
		showSysSkuName?: boolean;
		/**
		 * 显示系统货品条形码
		 */
		showSysBarCode?: boolean;
		/**
		 * 显示系统货品货号
		 */
		showSysItemNo?: boolean;
		/**
		 * 显示系统货品规格售价
		 */
		showSysSkuPrice?: boolean;
		[k: string]: any;
	  };
	  [k: string]: any;
	};
	[k: string]: any;
  }
  
/**
 * UserCustomSetDTO :UserCustomSetDTO
 */
export interface ItemSysItemUserAddUserCustomSetRequest {
	/**
	 * 自增长ID,表字段:id
	 */
	id?: number;
	/**
	 * 系统用户子账号id,表字段:user_id
	 */
	userId?: number;
	/**
	 * 子账号用户id,表字段:sub_user_id
	 */
	subUserId?: number;
	/**
	 * 1表示单列2表示双列,表字段:show_column
	 */
	showColumn?: number;
	/**
	 * 是否显示商品标题0为不展示,表字段:show_item_title
	 */
	showItemTitle?: number;
	/**
	 * 是否显示商品简称0为不展示,表字段:show_short_title
	 */
	showShortTitle?: number;
	/**
	 * 是否显示商家编码0为不展示,表字段:show_outer_id
	 */
	showOuterId?: number;
	/**
	 * 是否显示商品图片0为不展示,表字段:show_picture
	 */
	showPicture?: number;
	/**
	 * 是否显示规格名称0为不展示,表字段:show_sku_title
	 */
	showSkuTitle?: number;
	/**
	 * 是否显示规格别名0为不展示,表字段:show_sku_alias
	 */
	showSkuAlias?: number;
	/**
	 * 是否显示规格编码0为不展示,表字段:show_sku_outer_id
	 */
	showSkuOuterId?: number;
	/**
	 * 库存预警1开启0不开启,表字段:show_stock_warn
	 */
	showStockWarn?: number;
	/**
	 * 删除标记,表字段:enable_status
	 */
	enableStatus?: number;
	/**
	 * 创建时间,表字段:created
	 */
	created?: string;
	/**
	 * 更新时间,表字段:modified
	 */
	modified?: string;
	/**
	 * 是否显示货品图片0为不展示,表字段:show_sys_pic_path
	 */
	showSysPicPath?: number;
	/**
	 * 是否显示货品编码0为不展示,表字段:show_sys_outer_id
	 */
	showSysOuterId?: number;
	/**
	 * 是否显示货品规格编码0为不展示,表字段:show_sys_outer_sku_id
	 */
	showSysOuterSkuId?: number;
	/**
	 * 显示商品选择框true显示false不显示
	 */
	showGoodsSelect?: boolean;
	/**
	 * 部分发货商品提醒0为不展示
	 */
	showPartConsignItems?: number;
	/**
	 * 图片展示大小0:小图1:中图2:大图
	 */
	showPicSize?: number;
	/**
	 * 配置类型，隔离不同服务
	 */
	type?: number;
	/**
	 * 扩展字段 ,CustomSetJson
	 */
	customSetJson?: {
	  /**
	   * 显示商品库存默认false
	   */
	  showGoodsStockBox?: boolean;
	  /**
	   * 显示货品库存情况默认false
	   */
	  showSysGoodsStockBox?: boolean;
	  /**
	   * 显示平台规格名称
	   */
	  showPlatformSkuName?: boolean;
	  /**
	   * 显示平台规格id
	   */
	  showPlatformSkuId?: boolean;
	  /**
	   * 显示平台条形码
	   */
	  showPlatformBarCode?: boolean;
	  /**
	   * 显示平台货品
	   */
	  showPlatformItemNo?: boolean;
	  /**
	   * 显示平台规格售价
	   */
	  showPlatformSkuPrice?: boolean;
	  /**
	   * 显示系统货品规格名称
	   */
	  showSysSkuName?: boolean;
	  /**
	   * 显示系统货品条形码
	   */
	  showSysBarCode?: boolean;
	  /**
	   * 显示系统货品货号
	   */
	  showSysItemNo?: boolean;
	  /**
	   * 显示系统货品规格售价
	   */
	  showSysSkuPrice?: boolean;
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * ResponseBody<Long> :ResponseBody
 */
export interface ItemSysItemUserAddUserCustomSetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: number;
	[k: string]: any;
  }
  
/**
 * ReqSkuSimilaritySearchDTO :ReqSkuSimilaritySearchDTO
 */
export interface ItemSkuSimilaritySearchRequest {
	/**
	 * 商品id
	 */
	numIid?: string;
	/**
	 * 规格id
	 */
	skuId?: string;
	/**
	 * 相似度
	 */
	similarity?: number;
	/**
	 * 标题权重
	 */
	titleWeight?: number;
	/**
	 * 规格名称权重
	 */
	skuNameWeight?: number;
	/**
	 * 规格图片权重
	 */
	picUrlWeight?: number;
	[k: string]: any;
  }

/**
 * ResponseBody<List<RspSkuSimilaritySearchDTO>> :ResponseBody
 */
export interface ItemSkuSimilaritySearchResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspSkuSimilaritySearchDTO
	 */
	data?: {
	  numIid?: string;
	  skuId?: string;
	  /**
	   * 相似度
	   */
	  similarity?: number;
	  title?: string;
	  picUrl?: string;
	  /**
	   * 分数
	   */
	  score?: number;
	  skuName?: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }
  
/**
 * ItemMergeCombinationParseRequest :ItemMergeCombinationParseRequest
 */
export interface ItemSysItemItemMergeParseCombinationRequest {
	/**
	 * ItemMergeCombinationParseDTO
	 */
	dataList: {
	  skuName: string;
	  numIid: string;
	  skuId: string;
	  sellerId: number;
	  platform: string;
	  [k: string]: any;
	}[];
	/**
	 * 识别模式,0:不解析1:ai模式2:逻辑模式（默认1）
	 */
	recognitionModeFlag?: number;
	[k: string]: any;
  }

/**
 * ResponseBody<ItemMergeCombinationParseResponse> :ResponseBody
 */
export interface ItemSysItemItemMergeParseCombinationResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemMergeCombinationParseResponse
	 */
	data?: {
	  /**
	   * ItemRecommendVo
	   */
	  itemRecommendVoList?: {
		/**
		 * sku表数据主键
		 */
		id?: number;
		/**
		 * 商品标题
		 */
		title?: string;
		/**
		 * 规格名称
		 */
		skuName?: string;
		/**
		 * 商品id
		 */
		numIid?: string;
		/**
		 * 商品规格id
		 */
		skuId?: string;
		/**
		 * 店铺id
		 */
		sellerId?: number;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 店铺
		 */
		platform?: string;
		/**
		 * 平台商品编码
		 */
		outerId?: string;
		/**
		 * 平台规格编码
		 */
		skuOuterId?: string;
		/**
		 * 平台规格库存
		 */
		stockNum?: number;
		/**
		 * 平台售价
		 */
		price?: string;
		/**
		 * 平台规格图片
		 */
		picUrl?: string;
		/**
		 * 平台商品图片
		 */
		itemPicUrl?: string;
		/**
		 * 本地货品信息 ,SysSkuInfo
		 */
		sysSkuInfo?: {
		  /**
		   * 本地货品id
		   */
		  sysItemId?: number;
		  /**
		   * 本地货品规格id
		   */
		  sysSkuId?: number;
		  /**
		   * 本地货品简称
		   */
		  sysItemAlias?: string;
		  /**
		   * 本地货品规格名称
		   */
		  sysSkuName?: string;
		  /**
		   * 本地货品规格编码
		   */
		  sysSkuOuterId?: string;
		  /**
		   * 本地货品规格价格
		   */
		  sysPrice?: string;
		  /**
		   * 本地货品规格正品库存
		   */
		  salableItemStock?: number;
		  /**
		   * 本地货品规格图片
		   */
		  sysPicUrl?: string;
		  /**
		   * 是否组合货品,0普通，1组合商品
		   */
		  isCombination?: number;
		  /**
		   * 组合货品子货品信息集合 ,GroupCombinationRecord
		   */
		  combinationRecordList?: {
			/**
			 * 货品规格id
			 */
			sysSkuId?: number;
			/**
			 * 货品id
			 */
			sysItemId?: number;
			/**
			 * 货品规格编码
			 */
			skuOuterId?: string;
			/**
			 * 组合比例
			 */
			groupProportionNum?: number;
			/**
			 * 简称
			 */
			sysItemAlias?: string;
			/**
			 * 组合货品id
			 */
			groupSysItemId?: number;
			/**
			 * 组合货品规格id
			 */
			groupSysSkuId?: number;
			/**
			 * 操作类型新建CREATE更换BIND,
			 */
			opsType?: string;
			defaultPlatformItemSkuKey?: string;
			[k: string]: any;
		  }[];
		  [k: string]: any;
		};
		/**
		 * 分组key
		 */
		groupKey?: string;
		/**
		 * 命中的过滤词分组key
		 */
		hitGroupFilterKey?: string;
		/**
		 * 操作类型新建CREATE更换BIND,,ItemRecommendOpsTypeEnum[CREATE,BIND,opsType,opsName]
		 */
		opsType?: string;
		/**
		 * 是否已绑定,BIND已绑定,UNBIND未绑定
		 */
		bindType?: string;
		/**
		 * 商品表数据库id
		 */
		itemDbId?: number;
		bizExtendInfoStr?: string;
		/**
		 * BizExtendInfo
		 */
		bizExtendInfo?: {
		  /**
		   * 组合解析子货品规格缓存记录信息 ,CombinationParseCacheSonRecord
		   */
		  combinationParseCacheSonRecordList?: {
			/**
			 * 货品规格编码
			 */
			skuOuterId?: string;
			/**
			 * 组合比例
			 */
			groupProportionNum?: number;
			/**
			 * 简称
			 */
			sysItemAlias?: string;
			/**
			 * 操作类型新建CREATE更换BIND,
			 */
			opsType?: string;
			[k: string]: any;
		  }[];
		  [k: string]: any;
		};
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

export interface ItemWarehouseSlotGetListRequest{
	[k: string]: any;
}
/**
 * ResponseBody<List<WarehouseSlotDTO>> :ResponseBody
 */
export interface ItemWarehouseSlotGetListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * WarehouseSlotDTO
	 */
	data?: {
	  /**
	   * @mbg.generatedid,表字段:id
	   */
	  id?: number;
	  /**
	   * @mbg.generated用户id,表字段:user_id
	   */
	  userId?: number;
	  /**
	   * @mbg.generated货位名称,表字段:slot_name
	   */
	  slotName?: string;
	  /**
	   * @mbg.generated排序数值,表字段:sort
	   */
	  sort?: number;
	  /**
	   * @mbg.generated1：可用,0:删除,表字段:enable_status
	   */
	  enableStatus?: number;
	  /**
	   * @mbg.generated创建时间,表字段:created
	   */
	  created?: string;
	  /**
	   * @mbg.generated修改时间,表字段:modified
	   */
	  modified?: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }

/**
 * WarehouseSlotDeleteRequest :WarehouseSlotDeleteRequest
 */
export interface ItemWarehouseSlotDeleteRequest {
	/**
	 * Long
	 */
	warehouseSlotIdList: number[];
	[k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemWarehouseSlotDeleteResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
}  

/**
 * WarehouseSlotSaveUpdateRequest :WarehouseSlotSaveUpdateRequest
 */
export interface ItemWarehouseSlotSaveRequest {
	/**
	 * WarehouseSlotSaveUpdateObj
	 */
	list: {
	  /**
	   * 货位id
	   */
	  warehouseSlotId?: number;
	  /**
	   * 货位名称
	   */
	  warehouseSlotName: string;
	  /**
	   * 排序
	   */
	  sort?: number;
	  [k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface ItemWarehouseSlotSaveResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}

