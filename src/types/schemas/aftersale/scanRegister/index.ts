/**
 * RefundScanRecordQueryReqDTO :RefundScanRecordQueryReqDTO
 */
export interface QueryRefundScanRecordListRequest {
	/**
	 * 查询时间类型1-售后扫描时间
	 */
	timeType?: number;
	/**
	 * 开始时间
	 */
	startTime?: string;
	/**
	 * 结束时间
	 */
	endTime?: string;
	/**
	 * 多平台店铺查询 ,MultiShopDTO
	 */
	multiShopS?: {
	  sellerId?: string;
	  platform?: string;
	  [k: string]: any;
	}[];
	/**
	 * 收货状态2-部分收货3-全部收货
	 */
	confirmStatus?: number|string;
	/**
	 * 售后单号，多个逗号分割
	 */
	refundIds?: string;
	/**
	 * 销售单号，多个逗号分割
	 */
	tids?: string;
	ptTids?: string;
	/**
	 * 售后类型
	 */
	afterSaleType?: number|string;
	/**
	 * 快递公司名称
	 */
	companyName?: string;
	/**
	 * 快递单号，多个逗号分割
	 */
	sid?: string;
	/**
	 * 买家昵称，多个逗号分割
	 */
	buyerNick?: string;
	/**
	 * 收件人姓名，多个逗号分割
	 */
	receiverName?: string;
	/**
	 * 手机号，多个逗号分割
	 */
	receiverMobile?: string;
	/**
	 * 操作人名称
	 */
	operatorNick?: string;
	pageNo?: number;
	pageSize?: number;
	[k: string]: any;
}
/**
 * ResponseBody<PageList<RefundScanRecordResDTO>> :ResponseBody
 */
export interface QueryRefundScanRecordListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
	  pageNo?: number;
	  pageSize?: number;
	  total?: number;
	  /**
	   * T
	   */
	  list?: {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 平台类型
		 */
		platform?: string;
		/**
		 * 店铺ID
		 */
		sellerId?: string;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 售后编号
		 */
		refundId?: string;
		/**
		 * 订单编号
		 */
		tid?: string;
		/**
		 * 快递单号
		 */
		sid?: string;
		/**
		 * 快递公司名称
		 */
		companyName?: string;
		/**
		 * 售后类型
		 */
		afterSaleType?: number;
		/**
		 * 收货状态
		 */
		confirmStatus?: number;
		/**
		 * 买家昵称
		 */
		buyerNick?: string;
		/**
		 * 收件人姓名
		 */
		receiverName?: string;
		/**
		 * 手机号
		 */
		receiverMobile?: string;
		/**
		 * 操作人名称
		 */
		operatorNick?: string;
		/**
		 * 扫描时间
		 */
		refundScanTime?: string;
		/**
		 * 收货时间
		 */
		refundConfirmTime?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
}
/**
 * 售后扫描记录导出
 */
export interface ExportRefundScanRecordListRequest {
  /**
   * 查询时间类型1-售后扫描时间
   */
  timeType?: number;
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * 多平台店铺查询 ,MultiShopDTO
   */
  multiShopS?: {
    sellerId?: string;
    platform?: string;
    [k: string]: any;
  }[];
  /**
   * 收货状态2-部分收货3-全部收货
   */
  confirmStatus?: number;
  /**
   * 售后单号，多个逗号分割
   */
  refundIds?: string;
  /**
   * 销售单号，多个逗号分割
   */
  tids?: string;
  ptTids?: string;
  /**
   * 售后类型
   */
  afterSaleType?: number;
  /**
   * 快递公司名称
   */
  companyName?: string;
  /**
   * 快递单号，多个逗号分割
   */
  sid?: string;
  /**
   * 买家昵称，多个逗号分割
   */
  buyerNick?: string;
  /**
   * 收件人姓名，多个逗号分割
   */
  receiverName?: string;
  /**
   * 手机号，多个逗号分割
   */
  receiverMobile?: string;
  /**
   * 操作人名称
   */
  operatorNick?: string;
  pageNo?: number;
  pageSize?: number;
  [k: string]: any;
}

/**
 * ReqGenerateRefundLabelDTO :ReqGenerateRefundLabelDTO
 */
export interface ItemTakeGoodsLabelGenerateRefundLabelRequest {
  /**
   * GenerateRefundLabelInfo
   */
  generateRefundLabelInfoList?: {
    /**
     * 商品标题
     */
    title?: string;
    /**
     * 商品图片
     */
    picUrl?: string;
    /**
     * 主订单编号
     */
    tid?: string;
    /**
     * 子订单编号
     */
    oid?: string;
    /**
     * 商品数量
     */
    goodsNum?: number;
    /**
     * 退款id
     */
    refundId?: string;
    /**
     * 商品id
     */
    numIid?: string;
    /**
     * 商品规格id
     */
    skuId?: string;
    /**
     * 商品商家编码
     */
    outerId?: string;
    /**
     * 商品规格编码
     */
    skuOuterId?: string;
    /**
     * 货品id
     */
    sysItemId?: number;
    /**
     * 货品规格id
     */
    sysSkuId?: number;
    /**
     * 货品简称
     */
    sysItemAlias?: string;
    /**
     * 货品商家编码
     */
    sysOuterId?: string;
    /**
     * 货品规格图片
     */
    sysSkuPicUrl?: string;
    /**
     * 货品规格名称
     */
    sysSkuName?: string;
    /**
     * 货品规格别名
     */
    sysSkuAlias?: string;
    /**
     * 货品规格商家编码
     */
    sysSkuOuterId?: string;
    /**
     * 市场
     */
    market?: string;
    /**
     * 档口
     */
    stall?: string;
    /**
     * 供应商id
     */
    supplierid?: number;
    /**
     * 供应商名称
     */
    supplierName?: string;
    /**
     * 店铺昵称
     */
    sellerNick?: string;
    /**
     * 店铺id
     */
    sellerId?: number;
    [k: string]: any;
  }[];
  userId?: number;
  [k: string]: any;
}

/**
 * ResponseBody<RspGenerateRefundLabelDTO> :ResponseBody
 */
export interface ItemTakeGoodsLabelGenerateRefundLabelResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspGenerateRefundLabelDTO
   */
  data?: {
    /**
     * GenerateRefundLabelInfo
     */
    generateRefundLabelInfoList?: {
      /**
       * 商品标题
       */
      title?: string;
      /**
       * 商品图片
       */
      picUrl?: string;
      /**
       * 主订单编号
       */
      tid?: string;
      /**
       * 子订单编号
       */
      oid?: string;
      /**
       * 商品数量
       */
      goodsNum?: number;
      /**
       * 退款id
       */
      refundId?: string;
      /**
       * 商品id
       */
      numIid?: string;
      /**
       * 商品规格id
       */
      skuId?: string;
      /**
       * 商品商家编码
       */
      outerId?: string;
      /**
       * 商品规格编码
       */
      skuOuterId?: string;
      /**
       * 货品id
       */
      sysItemId?: number;
      /**
       * 货品规格id
       */
      sysSkuId?: number;
      /**
       * 货品简称
       */
      sysItemAlias?: string;
      /**
       * 货品商家编码
       */
      sysOuterId?: string;
      /**
       * 货品规格图片
       */
      sysSkuPicUrl?: string;
      /**
       * 货品规格名称
       */
      sysSkuName?: string;
      /**
       * 货品规格别名
       */
      sysSkuAlias?: string;
      /**
       * 货品规格商家编码
       */
      sysSkuOuterId?: string;
      /**
       * 市场
       */
      market?: string;
      /**
       * 档口
       */
      stall?: string;
      /**
       * 供应商id
       */
      supplierid?: number;
      /**
       * 供应商名称
       */
      supplierName?: string;
      /**
       * 店铺昵称
       */
      sellerNick?: string;
      /**
       * 店铺id
       */
      sellerId?: number;
      /**
       * 操作人id
       */
      opUserId?: number;
      /**
       * 操作人
       */
      opUserStr?: string;
      [k: string]: any;
    }[];
    userId?: number;
    [k: string]: any;
  };
  [k: string]: any;
}

export interface SelectBatchScanRequest {
  pageNo?: number;
  pageSize?: number;
  userId?: number;
  /**
   * 快递单号
   */
  sid?: string;
  /**
   * 快递公司
   */
  companyName?: string;
  /**
   * 是否匹配  1：匹配  2：未匹配
   */
  isMatch?: number;
  multiShopS?: {
    sellerId?: string;
    platform?: string;
    [k: string]: any;
  }[];
  count?: boolean; // true 统计总数 false 不统计总数
  [k: string]: any;
}

export interface SelectBatchScanResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  data?: {
    pageNo?: number;
    pageSize?: number;
    total?: string;
    list?: {
      /**
       * id
       */
      id?: number;
      /**
       * userId
       */
      userId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      /**
       * 退款表id
       */
      refundInfoId?: number;
      /**
       * 售后编码
       */
      refundId?: string;
      /**
       * 订单编号
       */
      tid?: string;
      /**
       * 子订单id
       */
      oid?: string;
      /**
       * 退款状态
       */
      refundStatus?: number;
      /**
       * 平台退款状态
       */
      platformRefundStatus?: string;
      /**
       * 售后类型
       */
      afterSaleType?: number;
      /**
       * 售后渠道
       */
      afterSaleChannel?: number;
      /**
       * 对应的订单交易状态
       */
      orderStatus?: string;
      /**
       * 平台订单状态
       */
      orderStatusDesc?: string;
      /**
       * 退款原因
       */
      refundReason?: string;
      /**
       * 买家昵称
       */
      buyerNick?: string;
      /**
       * 物流公司名称
       */
      companyName?: string;
      /**
       * 运单号
       */
      sid?: string;
      /**
       * 买家旗帜
       */
      sellerFlag?: string;
      /**
       * 卖家备注
       */
      sellerMemo?: string;
      /**
       * 退款创建时间
       */
      refundCreatedTime?: string;
      /**
       * 退款更新时间
       */
      refundModifiedTime?: string;
      /**
       * 操作人id
       */
      operatorId?: number;
      /**
       * 操作人名称
       */
      operatorNick?: string;
      /**
       * 信息创建时间
       */
      gmtCreate?: string;
      /**
       * 信息更新时间
       */
      gmtModified?: string;
      /**
       * 退款金额
       */
      refundAmount?: number;
      /**
       * 订单实付金额
       */
      payment?: number;
      /**
       * 订单总金额
       */
      totalFee?: number;
      /**
       * 售后金额
       */
      price?: number;
      /**
       * 卖家收货地址
       */
      address?: string;
      /**
       * 逻辑删除
       */
      enableStatus?: boolean;
      /**
       * 买家留言
       */
      buyerMessage?: string;
      /**
       * TB 买家备注
       */
      buyerMemo?: string;
      /**
       * 付款时间
       */
      payTime?: string;
      /**
       * 审核状态
       */
      reviewStatus?: boolean;
      /**
       * 异常类型
       */
      exceptionType?: number;
      /**
       * 收件人姓名
       */
      receiverName?: string;
      /**
       * 收件人手机号
       */
      receiverMobile?: string;
      /**
       * 收件人省
       */
      receiverState?: string;
      /**
       * 收件人市
       */
      receiverCity?: string;
      /**
       * 收件人区
       */
      receiverDistrict?: string;
      /**
       * 收件人详细地址
       */
      receiverAddress?: string;
      /**
       * 收件人姓名密文
       */
      encodeReceiverName?: string;
      /**
       * 收件人手机号密文
       */
      encodeReceiverMobile?: string;
      /**
       * 收件人详细地址密文
       */
      encodeReceiverAddress?: string;
      /**
       * erp备注
       */
      erpMemo?: string;
      /**
       * open_uid作为买家唯一标识
       */
      buyerOpenUid?: string;
      /**
       * 售后商品类型 0:退款商品 1:退货商品
       */
      refundItemType?: number;
      /**
       * 工单类型
       */
      refundSystemType?: number;
      /**
       * 来源
       */
      source?: string;
      /**
       * 1匹配到售后单2-匹配到销售单3-手动创建
       */
      buildType?: number;
      /**
       * 1：匹配到 ；2：没匹配到
       */
      isMatch?: number;
      refundScanRegistItemRecordDTOList?: {
        id?: number;
        /**
         * 扫描登记表id
         */
        refundScanRegistId?: number;
        /**
         * 用户ID
         */
        userId?: number;
        /**
         * 子订单id
         */
        field_1?: string;
        /**
         * 商品refund_item_record表唯一id
         */
        itemRefundItemRecordId?: number;
        /**
         * 申请退款的商品数字编号
         */
        numIid?: string;
        /**
         * 商品规格Id
         */
        skuId?: string;
        /**
         * 商品图片路径
         */
        gmtModified?: string;
        /**
         * 商品外部商家编码
         */
        outerId?: string;
        /**
         * 外部网店自己定义的Sku编号
         */
        outerSkuId?: number;
        /**
         * 货号
         */
        itemNo?: string;
        /**
         * 商品标题
         */
        title?: string;
        /**
         * 规格，颜色
         */
        skuName?: string;
        /**
         * 货品refund_item_record表唯一id
         */
        sysRefundItemRecordId?: number;
        /**
         * 货品itemID
         */
        sysItemId?: string;
        /**
         * 货品图片路径
         */
        sysPicUrl?: string;
        /**
         * 货品名称,货品表是sys_item_name
         */
        sysTitle?: string;
        /**
         * 商品外部商家编码
         */
        sysItemAlias?: string;
        /**
         * 货品商品外部商家编码
         */
        sysOutId?: string;
        /**
         * 货品规格id
         */
        sysSkuId?: string;
        /**
         * 货品规格别名
         */
        sysSkuAlias?: number;
        /**
         * 货品sku商家编码
         */
        sysOutSkuId?: string;
        /**
         * 货品商品货号
         */
        sysItemNo?: string;
        /**
         * 规格，颜色
         */
        sysSkuName?: string;
        /**
         * 市场
         */
        market?: string;
        /**
         * 档口
         */
        stall?: string;
        /**
         * 供应商id
         */
        belong?: number;
        /**
         * 供应商名称
         */
        supplierName?: string;
        /**
         * 售后金额
         */
        price?: string;
        /**
         * 订单对应货品数量
         */
        num?: number;
        /**
         * 申请退款的数量
         */
        applyRefundNum?: number;
        /**
         * 已收货数量
         */
        hasRefundNum?: number;
        /**
         * 已处理数量
         */
        hasDisposeNum?: number;
        /**
         * 是否组合货品 1是 0不是
         */
        isCombination?: number;
        /**
         * 商品类型 0:退款，1:退货，2:换货，3:补发
         */
        refundItemType?: number;
        /**
         * 货品类型 0:退款，1:退货，2:换货，3:补发
         */
        sysRefundItemType?: number;
        /**
         * 商品来源 0-平台快照 1-手动添加
         */
        refundItemSource?: number;
        /**
         * 货品来源 0-平台快照 1-手动添加
         */
        sysRefundItemSource?: number;
        /**
         * 货品是否需被过滤
         */
        sysRefundItemIsNeedFilter?: boolean;
        /**
         * 记录创建时间
         */
        gmtCreate?: string;
        /**
         * 记录更新时间
         */
        field_10?: string;
        /**
         * 逻辑删除
         */
        enableStatus?: boolean;
        [k: string]: any;
      }[];
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}


export interface RefundScanRegistrationRequest {
  /**
   * 快递单号
   */
  sid?: string;
  /**
   * 快递公司
   */
  companyName?: string;
  [k: string]: any;
}

/**
 * ResponseBody<RspRefundScanRegistrationDto> :ResponseBody
 */
export interface RefundScanRegistrationResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspRefundScanRegistrationDto
   */
  data?: {
    /**
     * 批量扫描登记1-匹配到2-没匹配到
     */
    refundScanMatch?: number;
    /**
     * 匹配售后销售-商品件数
     */
    matchItemNum?: number;
    /**
     * 匹配售后销售订单数
     */
    matchNum?: number;
    [k: string]: any;
  };
  [k: string]: any;
}


export interface DelBatchScanRequest {
  /**
   * 批量登记表id
   */
  idList?: number[];
  [k: string]: any;
}

export interface DelBatchScanResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  data?: boolean;
  [k: string]: any;
}


export interface SyncBatchScanRequest {
  /**
   * 批量登记表id
   */
  idList?: number[];
  [k: string]: any;
}

export interface SyncBatchScanResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  data?: boolean;
  [k: string]: any;
}

export interface BatchScanConfirmRequest {
  /**
   * 批量登记表id
   */
  idList?: number[];
  ignoreException?: boolean; // 是否忽略异常0-否1-是
  [k: string]: any;
}

export type BatchScanConfirmForSyncRequest = BatchScanConfirmRequest;

export interface BatchScanConfirmForSyncResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BatchScanConfirmResultDTO
   */
  data?: {
    userId?: number;
    /**
     * 进度0-100
     */
    progress?: number;
    /**
     * 总数量
     */
    total?: number;
    /**
     * 成功数量
     */
    successNum?: number;
    /**
     * 失败数量
     */
    failNum?: number;
    /**
     * 错误信息
     */
    errorMsg?: string;
    /**
     * 真实进度
     */
    actualProgress?: number;
    finish?: boolean;
    /**
     * 换货的错误信息（不参与失败数量的统计，仅用于记录） ,ErrorDetails
     */
    errorExchangeMessages?: {
      sid?: string;
      /**
       * 退款id
       */
      refundId?: string;
      errorInfo?: string;
      [k: string]: any;
    }[];
    /**
     * ErrorDetails
     */
    errorList?: {
      sid?: string;
      /**
       * 退款id
       */
      refundId?: string;
      errorInfo?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}


export interface BatchScanConfirmResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * cacheKey
   */
  data?: string;
  [k: string]: any;
}

export interface SaveScanItemRequest {
  refundScanRegistItemRecordDTOList?: {
    /**
     * id
     */
    id?: number;
    /**
     * 扫描登记表id
     */
    refundScanRegistId?: number;
    /**
     * 用户ID
     */
    userId?: number;
    /**
     * 子订单id
     */
    oid?: string;
    /**
     *  商品refund_item_record表唯一id
     */
    itemRefundItemRecordId?: number;
    /**
     * 申请退款的商品数字编号
     */
    numIid?: string;
    /**
     * 商品规格Id
     */
    skuId?: string;
    /**
     * 商品图片路径
     */
    picUrl?: string;
    /**
     * 商品外部商家编码
     */
    outerId?: string;
    /**
     *  外部网店自己定义的Sku编号
     */
    outerSkuId?: string;
    /**
     * 货号
     */
    itemNo?: string;
    /**
     * 商品标题
     */
    title?: string;
    /**
     * 规格，颜色
     */
    skuName?: string;
    /**
     * 货品refund_item_record表唯一id
     */
    sysRefundItemRecordId?: number;
    /**
     * 货品itemID
     */
    sysItemId?: string;
    /**
     *  货品图片路径
     */
    sysPicUrl?: string;
    /**
     * 货品名称,货品表是sys_item_name
     */
    sysTitle?: string;
    /**
     * 货品简称
     */
    sysItemAlias?: string;
    /**
     * 货品商品外部商家编码
     */
    sysOutId?: string;
    /**
     * 货品规格id
     */
    sysSkuId?: string;
    /**
     * 货品规格别名
     */
    sysSkuAlias?: string;
    /**
     *  货品sku商家编码
     */
    sysOutSkuId?: string;
    /**
     * 货品商品货号
     */
    sysItemNo?: string;
    /**
     * 规格，颜色
     */
    sysSkuName?: string;
    /**
     * 市场
     */
    market?: string;
    /**
     * 档口
     */
    stall?: string;
    /**
     * 供应商id
     */
    supplierId?: number;
    /**
     * 供应商名称
     */
    supplierName?: string;
    /**
     * 售后金额
     */
    price?: string;
    /**
     * 订单对应货品数量
     */
    num?: number;
    /**
     *  申请退款的数量
     */
    applyRefundNum?: number;
    /**
     * 已收货数量
     */
    hasRefundNum?: number;
    /**
     * 已处理数量
     */
    hasDisposeNum?: number;
    /**
     * 是否组合货品 1是 0不是
     */
    isCombination?: number;
    /**
     * 商品类型 0:退款，1:退货，2:换货，3:补发
     */
    refundItemType?: number;
    /**
     * 货品类型 0:退款，1:退货，2:换货，3:补发
     */
    sysRefundItemType?: number;
    /**
     * 商品来源 0-平台快照 1-手动添加
     */
    refundItemSource?: number;
    /**
     * 货品来源 0-平台快照 1-手动添加
     */
    sysRefundItemSource?: number;
    /**
     * 货品是否需被过滤
     */
    sysRefundItemIsNeedFilter?: boolean;
    /**
     * 记录创建时间
     */
    gmtCreate?: string;
    /**
     * 记录更新时间
     */
    gmtModified?: string;
    /**
     * 逻辑删除
     */
    enableStatus?: boolean;
    /**
     * 货品是否在登记页面添加 0-别的页面添加 1-登记页面添加
     */
    registItemSource?: number;
    [k: string]: any;
  }[];
  [k: string]: any;
}

export interface SaveScanItemResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  data?: boolean;
  [k: string]: any;
}


export interface GetProgressRequest {
  /**
   * cacheKey
   */
  cacheKey?: string;
  [k: string]: any;
}

export interface GetProgressResponse {
  userId?: number;
  /**
   * 进度0-100
   */
  progress?: number;
  /**
   * 总数量
   */
  total?: number;
  /**
   * 成功数量
   */
  successNum?: number;
  /**
   * 失败数量
   */
  failNum?: number;
  /**
   * 错误信息
   */
  errorMsg?: string;
  /**
   * 错误信息集合
   */
  errorList?: {
    /**
     * 快递单号
     */
    sid: string;
    /**
     * 错误信息
     */
    errorInfo: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ReqRefundExportConfigQueryDto :ReqRefundExportConfigQueryDto
 */
export interface GetRefundExportConfigRequest {
  /**
   * 导出类型，0：售后订单导出类型,1：售后扫描导出类型，2：售后扫描记录导出（快递单纬度），3：售后扫描记录导出（商品纬度）
   */
  latitude?: number;
  [k: string]: any;
}

/**
 * ResponseBody<RspRefundExportConfigDto> :ResponseBody
 */
export interface GetRefundExportConfigResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspRefundExportConfigDto
   */
  data?: {
    /**
     * 纬度，0：订单纬度,1：商品纬度
     */
    latitude?: number;
    /**
     * 可选字段 ,ExportField
     */
    selectableExportFields?: {
      /**
       * 0：售后订单导出类型,1：售后扫描导出类型，2：售后扫描记录导出（快递单纬度），3：售后扫描记录导出（商品纬度）
       */
      latitude?: number;
      /**
       * 字段名称
       */
      name?: string;
      /**
       * 字段描述
       */
      desc?: string;
      /**
       * 系统默认排序值，升序
       */
      sort?: number;
      /**
       * 用户自定义排序值，升序
       */
      rank?: number;
      /**
       * 是否选中
       */
      selected?: boolean;
      /**
       * 维度,0：订单,1：商品
       */
      type?: number;
      [k: string]: any;
    }[];
    /**
     * 导出字段 ,ExportField
     */
    exportFields?: {
      /**
       * 0：售后订单导出类型,1：售后扫描导出类型，2：售后扫描记录导出（快递单纬度），3：售后扫描记录导出（商品纬度）
       */
      latitude?: number;
      /**
       * 字段名称
       */
      name?: string;
      /**
       * 字段描述
       */
      desc?: string;
      /**
       * 系统默认排序值，升序
       */
      sort?: number;
      /**
       * 用户自定义排序值，升序
       */
      rank?: number;
      /**
       * 是否选中
       */
      selected?: boolean;
      /**
       * 维度,0：订单,1：商品
       */
      type?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqBatchScanDto :ReqBatchScanDto
 */
export interface BatchIsPendingRequest {
  /**
   * 批量登记表id ,Long
   */
  idList?: number[];
  /**
   * 确认收货后自动入库
   */
  autoUpStockAfterConfirmReceiverGoods?: boolean;
  /**
   * 是否挂起,0:不挂起,1:挂起
   */
  isPending?: number;
  [k: string]: any;
}
/**
 * ResponseBody<String> :ResponseBody
 */
export interface BatchIsPendingResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * data
   */
  data?: string;
  [k: string]: any;
}


export interface SelectReqSelectRefundScanV2Request {
  /**
   * 用户id
   */
  userId?: number;
  /**
   * 快递单号
   */
  sid?: string;
  /**
   * 收件人手机号
   */
  receiverMobile?: string;
  /**
   * 买家昵称
   */
  buyerNick?: string;
  /**
   * 订单编号售后订单号
   */
  order?: string;
  /**
   * String
   */
  orderList?: string[];
  /**
   * 收件人名字
   */
  receiverName?: string;
  /**
   * 创建时间开始
   */
  createTimeStart?: string;
  /**
   * 创建时间结束
   */
  createTimeEnd?: string;
  /**
   * 收件人手机号集合 ,String
   */
  receiverMobileList?: string[];
  /**
   * 收件人手机号模糊匹配
   */
  receiverMobileLike?: string;
  /**
   * 买家昵称集合 ,String
   */
  buyerNickList?: string[];
  /**
   * 买家昵称集合 ,String
   */
  openUidList?: string[];
  /**
   * 收件人名字集合 ,String
   */
  receiverNameList?: string[];
  /**
   * 快递单号集合 ,String
   */
  sidList?: string[];
  /**
   * 售后编码集合 ,String
   */
  refundIdList?: string[];
  /**
   * tid集合 ,String
   */
  tidList?: string[];
  /**
   * String
   */
  receiverInfoTidList?: string[];
  /**
   * 小标签唯一码
   */
  labelId?: string;
  /**
   * 页面查询，默认false
   */
  pageQuery?: boolean;
  searchBySidAndFilterEmpty?: boolean;
  /**
   * 请求来源,1：pc-单个扫描,2：pc-批量扫描,3：小程序
   */
  requestOrigin?: number;
  /**
   * 请求版本，当前为v2
   */
  requestVersion?: string;
  /**
   * 是否生成售后扫描记录
   */
  isGenerateRefundScanRecord?: boolean;
  /**
   * 搜索内容
   */
  refundScanContent?: string;
  [k: string]: any;
}


export interface SelectReqSelectRefundScanV2Response {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspTotalDataSelectRefundScanDTO
   */
  data?: {
    /**
     * 扫描类型1匹配到售后单2-匹配到销售单3-手动创建
     */
    buildType?: number;
    /**
     * 扫描记录id，未匹配到时返回
     */
    scanRecordId?: number;
    /**
     * 匹配到售后单销售单时返回 ,RspSelectRefundScanDTO
     */
    list?: {
      /**
       * 退款单我们这边的唯一id
       */
      id?: string;
      /**
       * 平台TB、TM（天猫待定）、PDD、FXG（抖店）、HAND（手工单）
       */
      platform?: string;
      /**
       * 买家昵称
       */
      buyerNick?: string;
      /**
       * 买家唯一标识
       */
      buyerOpenUid?: string;
      /**
       * 物流公司名称
       */
      companyName?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * tid
       */
      tid?: string;
      /**
       * oid
       */
      oid?: string;
      /**
       * 退款编号
       */
      refundId?: string;
      /**
       * 退款创建时间
       */
      refundCreatedTime?: string;
      payTime?: string;
      /**
       * 退款更新时间
       */
      refundModifiedTime?: string;
      /**
       * 退款金额
       */
      refundAmount?: string;
      /**
       * 订单实付金额
       */
      payment?: string;
      /**
       * 邮费,表字段:post_fee
       */
      postFee?: string;
      /**
       * 订单总金额
       */
      totalFee?: string;
      receiverName?: string;
      /**
       * 收件人手机号,表字段:receiver_mobile
       */
      receiverMobile?: string;
      /**
       * 收件人省,表字段:receiver_state
       */
      receiverState?: string;
      /**
       * 收件人市,表字段:receiver_city
       */
      receiverCity?: string;
      /**
       * 收件人区,表字段:receiver_district
       */
      receiverDistrict?: string;
      /**
       * 收件人详细地址,表字段:receiver_address
       */
      receiverAddress?: string;
      /**
       * 收件人姓名密文,表字段:encode_receiver_name
       */
      encodeReceiverName?: string;
      /**
       * 收件人手机号密文,表字段:encode_receiver_mobile
       */
      encodeReceiverMobile?: string;
      /**
       * 收件人详细地址密文,表字段:encode_receiver_address
       */
      encodeReceiverAddress?: string;
      /**
       * 换货收件人省
       */
      exchangeReceiverProvince?: string;
      /**
       * 换货收件人市
       */
      exchangeReceiverCity?: string;
      /**
       * 换货收件人区
       */
      exchangeReceiverTown?: string;
      /**
       * 换货收件人详细地址脱敏
       */
      exchangeReceiverAddressMask?: string;
      /**
       * 换货收件人姓名脱敏
       */
      exchangeReceiverNameMask?: string;
      /**
       * 换货收件人手机号脱敏,exchange_receiver_mobile_mask
       */
      exchangeReceiverMobileMask?: string;
      /**
       * 售后类型,空全部,1仅退款,2退货退款,3换货,4补差价,5补发货品
       */
      afterSaleType?: number;
      /**
       * 退款原因
       */
      refundReason?: string;
      /**
       * 退款说明
       */
      desc?: string;
      /**
       * erp备注
       */
      erpMemo?: string;
      /**
       * 退款状态,1等待卖家同意,2等待买家退货,3等待卖家确认收货,4退款成功,5退款关闭,6卖家拒绝退款
       */
      refundStatus?: number;
      /**
       * 退款状态说明
       */
      refundStatusDesc?: string;
      /**
       * 订单交易状态
       */
      orderStatus?: string;
      /**
       * 订单交易状态说明
       */
      orderStatusDesc?: string;
      /**
       * 卖方备注
       */
      sellerMemo?: string;
      /**
       * 买家旗帜
       */
      sellerFlag?: string;
      /**
       * 插旗标签
       */
      sellerFlagTag?: string;
      /**
       * 买家留言
       */
      buyerMessage?: string;
      /**
       * 退货运单号
       */
      sid?: string;
      /**
       * 发货运单号
       */
      invoiceNo?: string;
      /**
       * 售后渠道,1线上同步,2手工录入
       */
      afterSaleChannel?: number;
      /**
       * 异常类型,0无异常,1异常,2忽略异常
       */
      exceptionType?: number;
      /**
       * 审核状态
       */
      reviewStatus?: boolean;
      /**
       * 淘宝商家类型0-淘宝1-天猫
       */
      tbSellerType?: number;
      /**
       * 扫描类型1匹配到售后单2-匹配到销售单3-手动创建
       */
      buildType?: number;
      /**
       * 订单来源HAND：手工单
       */
      source?: string;
      /**
       * 工单类型,RefundSystemTypeEnum,ONLY_REFUND(1,"仅退款"),REFUSE_BACK_GOODS(2,"拒收退货"),RETURN_AND_REFUND(3,"退货退款"),EXCHANGE_ITEM(4,"换货"),REPLENISHMENT(5,"补发")
       */
      refundSystemType?: number;
      /**
       * 是否分销商推送
       */
      isDistributorUserPushRefund?: boolean;
      /**
       * 线下备注
       */
      localContent?: string;
      /**
       * 平台订单编号
       */
      ptTid?: string;
      /**
       * 平台子订单编号
       */
      ptOid?: string;
      /**
       * 运费（快手退款运费）
       */
      freight?: string;
      /**
       * 扫描记录id，匹配到时返回
       */
      scanRecordId?: number;
      /**
       * 退款货品信息 ,RspRefundItemRecordInfo
       */
      refundItemRecordInfos?: {
        /**
         * 子订单id
         */
        orderId?: string;
        /**
         * 平台子订单编号
         */
        ptOid?: string;
        /**
         * tid
         */
        tid?: string;
        /**
         * 平台订单编号
         */
        ptTid?: string;
        /**
         * 商品refund_item_record表唯一id
         */
        itemRefundItemRecordId?: number;
        /**
         * 申请退款的商品数字编号申请退款的商品数字编号
         */
        numIid?: string;
        /**
         * 商品规格id
         */
        skuId?: string;
        /**
         * 商品图片链接
         */
        picUrl?: string;
        /**
         * 商品外部商家编码
         */
        outerId?: string;
        /**
         * 商品规格编码
         */
        outerSkuId?: string;
        /**
         * 商品货号
         */
        itemNo?: string;
        /**
         * 商品标题
         */
        title?: string;
        /**
         * 规格，颜色
         */
        skuName?: string;
        /**
         * 货品refund_item_record表唯一id
         */
        sysRefundItemRecordId?: number;
        /**
         * 申请退款的商品数字编号申请退款的商品数字编号
         */
        sysItemId?: number;
        /**
         * 图片链接
         */
        sysPicUrl?: string;
        /**
         * 货品商品标题
         */
        sysTitle?: string;
        /**
         * 货品简称
         */
        sysItemAlias?: string;
        /**
         * 货品商品外部商家编码
         */
        sysOuterId?: string;
        /**
         * 货品规格id
         */
        sysSkuId?: string;
        /**
         * 货品规格别名
         */
        sysSkuAlias?: string;
        /**
         * 货品sku商家编码
         */
        sysOuterSkuId?: string;
        /**
         * 货品商品货号
         */
        sysItemNo?: string;
        /**
         * 规格，颜色
         */
        sysSkuName?: string;
        /**
         * 市场
         */
        market?: string;
        /**
         * 档口
         */
        stall?: string;
        /**
         * 供应商id
         */
        supplierId?: number;
        /**
         * 供应商名称
         */
        supplierName?: string;
        /**
         * 成本价
         */
        costPrice?: string;
        /**
         * 售后金额
         */
        price?: string;
        /**
         * 订单对应货品数量
         */
        num?: number;
        /**
         * 申请退货数量
         */
        applyRefundNum?: number;
        /**
         * 已收货数量
         */
        hasRefundNum?: number;
        /**
         * 已处理数量
         */
        hasDisposeNum?: number;
        /**
         * 创建时间
         */
        createTime?: string;
        /**
         * 是否为组合商品，1:组合货品,0:非组合货品
         */
        isCombination?: number;
        /**
         * 商品类型
         */
        refundItemType?: number;
        /**
         * 商品类型-货品
         */
        sysRefundItemType?: number;
        /**
         * 商品来源
         */
        refundItemSource?: number;
        /**
         * 商品来源-货品
         */
        sysRefundItemSource?: number;
        /**
         * sys_refund_item_is_need_filter,货品是否需被过滤
         */
        sysRefundItemIsNeedFilter?: boolean;
        /**
         * 异常类型,0无异常,1异常,2忽略异常
         */
        exceptionType?: number;
        [k: string]: any;
      }[];
      /**
       * 仓库id
       */
      storageId?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

export interface TradeGetOrderCountGroupByDetailsRequest {
  userId?: number;
  /**
   * 商品规格列表 ,String
   */
  skuIdList?: string[];
  /**
   * 货品规格id集合 ,String
   */
  sysSkuIdList?: string[];
  /**
   * 创建时间开始
   */
  jdpCreatedTimeStart?: string;
  /**
   * 创建时间结束
   */
  jdpCreatedTimeEnd?: string;
  /**
   * 子订单状态
   */
  orderStatus?: string;
  [k: string]: any;
}

export interface TradeGetOrderCountGroupByDetailsResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RdsOrderGroupQueryResp
   */
  data?: {
    skuId?: string;
    /**
     * 系统skuidsys_sku_id
     */
    sysSkuId?: number;
    /**
     * 数量
     */
    total?: number;
    [k: string]: any;
  }[];
  [k: string]: any;
}

export interface RefundScanRegistrationBatchRequest {
	/**
	 * 快递单号列表
	 */
	sidList: string[];
	/**
	 * 快递公司名称
	 */
	companyName?: string;
	/**
	 * 请求版本
	 */
	requestVersion?: string;
	/**
	 * 批量扫描来源
	 */
	refundBatchScanOrigin?: string;
	[k: string]: any;
}

export interface RefundScanRegistrationBatchResponse {
	/**
	 * 请求是否成功
	 */
	success: boolean;
	/**
	 * 错误码
	 */
	errorCode?: number;
	/**
	 * 错误信息
	 */
	errorMessage?: string;
	/**
	 * 响应数据
	 */
	data?: {
		/**
		 * 错误列表
		 */
		errorList?: {
			/**
			 * 快递单号
			 */
			sid: string;
			/**
			 * 错误信息
			 */
			errorInfo: string;
		}[];
		/**
		 * 扫描匹配状态 1-匹配到 2-没匹配到
		 */
		refundScanMatch?: number;
		/**
		 * 交易信息列表
		 */
		tradeInfoList?: any[];
		/**
		 * 成功数量
		 */
		successNum?: number;
		/**
		 * 失败数量
		 */
		failNum?: number;
		/**
		 * 总数量
		 */
		totalNum?: number;
	};
	[k: string]: any;
}