
/**
 * ReqFindTradeForBuildRefundDTO :ReqFindTradeForBuildRefundDTO
 */
export interface FindRdsTradeForBuildRefundRequest {
	/**
	 * tid
	 */
	tid?: string;
	ptTid?: string;
	/**
	 * 平台所有不传值
	 */
	platform?: string;
	/**
	 * 店铺ID
	 */
	sellerId?: string;
	[k: string]: any;
}


/**
 * ResponseBody<ResRefundNewBuildDTO> :ResponseBody
 */
export interface FindRdsTradeForBuildRefundResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ResRefundNewBuildDTO
	 */
	data?: {
		/**
		 * 退款单我们这边的唯一id
		 */
		id?: string;
		/**
		 * 平台TB、TM（天猫待定）、PDD、FXG（抖店）、HAND（手工单）
		 */
		platform?: string;
		/**
		 * 买家昵称
		 */
		buyerNick?: string;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 店铺ID
		 */
		sellerId?: string;
		/**
		 * tid
		 */
		tid?: string;
		/**
		 * 退款编号
		 */
		refundId?: string;
		/**
		 * 退款创建时间
		 */
		refundCreatedTime?: string;
		/**
		 * 退款更新时间
		 */
		refundModifiedTime?: string;
		/**
		 * 退款金额
		 */
		refundAmount?: string;
		/**
		 * 订单实付金额
		 */
		payment?: string;
		/**
		 * 订单总金额
		 */
		totalFee?: string;
		/**
		 * 收件人姓名
		 */
		receiverName?: string;
		/**
		 * 收件人手机号
		 */
		receiverMobile?: string;
		/**
		 * 收件人省
		 */
		receiverState?: string;
		/**
		 * 收件人市
		 */
		receiverCity?: string;
		/**
		 * 收件人区
		 */
		receiverDistrict?: string;
		/**
		 * 收件人详细地址
		 */
		receiverAddress?: string;
		/**
		 * 售后类型,空全部,1仅退款,2退货退款,3换货,4补差价,5补发货品
		 */
		afterSaleType?: number;
		/**
		 * 退款原因
		 */
		refundReason?: string;
		/**
		 * 退款说明
		 */
		desc?: string;
		/**
		 * erp备注
		 */
		erpMemo?: string;
		/**
		 * 退款阶段,1未完成,2已完成,3已关闭
		 */
		refundStage?: number;
		/**
		 * 退款状态,1等待卖家同意,2等待买家退货,3等待卖家确认收货,4退款成功,5退款关闭,6卖家拒绝退款
		 */
		refundStatus?: number;
		/**
		 * 退款状态说明
		 */
		refundStatusDesc?: string;
		/**
		 * 订单交易状态
		 */
		orderStatus?: string;
		/**
		 * 订单交易状态说明
		 */
		orderStatusDesc?: string;
		/**
		 * 卖方备注
		 */
		sellerMemo?: string;
		/**
		 * 退货运单号
		 */
		sid?: string;
		/**
		 * 发货运单号
		 */
		invoiceNo?: string;
		/**
		 * 售后渠道,1线上同步,2手工录入
		 */
		afterSaleChannel?: number;
		/**
		 * 异常类型,0无异常,1异常,2忽略异常
		 */
		exceptionType?: number;
		/**
		 * 买家留言
		 */
		buyerMessage?: string;
		/**
		 * 审核状态
		 */
		reviewStatus?: boolean;
		/**
		 * 退款货品信息 ,ResRefundItemRecordInfo
		 */
		refundItemRecordInfos?: {
			/**
			 * oid
			 */
			oid?: string;
			/**
			 * 货品id
			 */
			sysItemId?: number;
			/**
			 * 货品规格id
			 */
			sysSkuId?: string;
			/**
			 * 规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 货品简称
			 */
			sysItemAlias?: string;
			/**
			 * 图片链接
			 */
			picUrl?: string;
			/**
			 * 商品标题
			 */
			itemTitle?: string;
			/**
			 * skuid
			 */
			skuId?: string;
			/**
			 * 商品外部商家编码,表字段:outer_id
			 */
			outerId?: string;
			/**
			 * 订单对应货品数量
			 */
			num?: number;
			/**
			 * 申请退款的商品数字编号申请退款的商品数字编号,表字段:num_iid
			 */
			numIid?: string;
			/**
			 * 单价,表字段:price
			 */
			price?: string;
			/**
			 * 申请退货数量
			 */
			applyRefundNum?: number;
			/**
			 * 已收货数量
			 */
			hasRefundNum?: number;
			/**
			 * 已处理数量
			 */
			hasDisposeNum?: number;
			/**
			 * 规格名称
			 */
			skuName?: string;
			/**
			 * sku商家编码
			 */
			outerSkuId?: string;
			/**
			 * 创建时间
			 */
			createTime?: string;
			/**
			 * 是否为组合商品，1:组合货品,0:非组合货品
			 */
			isCombination?: number;
			/**
			 * 属于1平台2我们自己的
			 */
			belong?: number;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * ReqBuildRefundDTO :ReqBuildRefundDTO
 */
export interface BuildRefundRequest {
	/**
	 * 新建类型1匹配到订单2-未匹配到订单
	 */
	buildType?: number;
	/**
	 * tid
	 */
	tid?: string;
	ptTid?: string;
	/**
	 * 平台TB、TM（天猫待定）、PDD、FXG（抖店）、HAND（手工单）
	 */
	platform?: string;
	/**
	 * 店铺ID,表字段:seller_id
	 */
	sellerId?: string;
	/**
	 * 店铺名称
	 */
	sellerNick?: string;
	/**
	 * 售后类型,1仅退款,2退货退款
	 */
	afterSaleType?: number;
	/**
	 * 售后金额
	 */
	refundAmount?: string;
	/**
	 * 买家昵称
	 */
	buyerNick?: string;
	/**
	 * 收件人姓名
	 */
	receiverName?: string;
	/**
	 * 买家手机号
	 */
	receiverMobile?: string;
	/**
	 * 退款原因
	 */
	refundReason?: string;
	/**
	 * 退款说明
	 */
	desc?: string;
	/**
	 * 退货快递
	 */
	companyName?: string;
	/**
	 * 退货运单号
	 */
	sid?: string;
	/**
	 * erp备注
	 */
	erpMemo?: string;
	/**
	 * 退款货品信息组集合 ,List<List<BuildRefundItemReqDTO>>
	 */
	buildRefundItemGroupReqs?: {
		oid?: string;
		/**
		 * 货品id
		 */
		sysItemId: string;
		/**
		 * 货品规格id
		 */
		sysSkuId: string;
		/**
		 * 规格别名
		 */
		sysSkuAlias?: string;
		/**
		 * 货品简称
		 */
		sysItemAlias?: string;
		/**
		 * 货号
		 */
		itemNo?: string;
		/**
		 * 图片地址
		 */
		picUrl?: string;
		/**
		 * 商品标题
		 */
		itemTitle?: string;
		/**
		 * 商品外部商家编码
		 */
		outerId: string;
		/**
		 * 商家编码
		 */
		outerSkuId?: string;
		/**
		 * 规格名称
		 */
		skuInfo?: string;
		/**
		 * 申请退货数量
		 */
		applyRefundNum: number;
		/**
		 * 订单对应货品数量
		 */
		num?: number;
		/**
		 * 是否组合货品 1是 0不是
		 */
		isCombination?: number;
		skuId?: string;
		/**
		 * 属于 1平台 2 我们自己的
		 */
		belong?: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<Integer> :ResponseBody
 */
export interface BuildRefundResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: number;
	[k: string]: any;
}

/**
 * ReqSelectRefundListWithPageDTO :ReqSelectRefundListWithPageDTO
 */
export interface SelectRefundListWithPageRequest {
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 创建时间开始
	 */
	createTimeStart?: string;
	/**
	 * 创建时间结束
	 */
	createTimeEnd?: string;
	/**
	 * 修改时间开始
	 */
	modifiedTimeStart?: string;
	/**
	 * 修改时间结束
	 */
	modifiedTimeEnd?: string;
	/**
	 * 完成时间开始
	 */
	finishTimeStart?: string;
	/**
	 * 完成时间结束
	 */
	finishTimeEnd?: string;
	/**
	 * 平台所有不传值,TB("淘宝"),,TM"天猫"),,PDD("拼多多"),,FXG("抖音")
	 */
	platform?: string;
	/**
	 * 售后类型,空全部,1仅退款,2退货退款,3换货,4补差价,5补发货品
	 */
	afterSaleType?: number;
	/**
	 * 店铺名称
	 */
	sellerNick?: string;
	/**
	 * 审核状态true已审核false未审核
	 */
	reviewStatus?: boolean;
	/**
	 * 退款阶段,0("未完成"),,1("已完成"),,2("已关闭")
	 */
	refundStage?: number;
	/**
	 * 退款状态,空全部,1待卖家同意,2待买家退货,3待卖家确认退货,4退款成功,5退款关闭,6卖家拒绝退款
	 */
	refundStatus?: number;
	/**
	 * 货品包括为true，不包括为false
	 */
	sysItemInclude?: boolean;
	/**
	 * 货品名称或简称
	 */
	sysItemTitleOrAlias?: string;
	/**
	 * 货品商家编码
	 */
	skuOuterId?: string;
	/**
	 * 订单编号
	 */
	tid?: string;
	/**
	 * 退款id
	 */
	refundId?: string;
	/**
	 * 买家名称
	 */
	buyerNick?: string;
	/**
	 * 退货单号
	 */
	sid?: string;
	invoiceStatusEnum?: string;
	sidStatusEnum?: string;
	isIntercept?: string;
	pageNo?: number;
	pageSize?: number;
	 /**
   * 商品查询,TITLE_ITEM_ALIAS("title_item_alias","货品名称简称"),,OUTER_SKU_ID("outer_sku_id","货品规格编码"),,ITEM_AGGREGATION("item_aggregation","商品名称简称编码ID"),,SKU_AGGREGATION("sku_aggregation","规格名称编码、别名"),,key：包含不包含,value对应业务(该参数为map)
   */
	 itemInfoQueryMap?: {
		/**
		 * Boolean
		 */
		mapKey?: {
		  [k: string]: any;
		};
		/**
		 * List<ItemInfoQueryDTO>
		 */
		mapValue?: {
		  [k: string]: any;
		};
		[k: string]: any;
	  };
	/**
	 * 售后列表查询来源,0-正常售后单列表1-分销商的售后单列表
	 */
	scmSelectRefundSource?: number;
	scmRefundType?: string; // 自发代发售后ownSend-自发售后agentSend-代发售后
	scmSupplierUserIdList?: number[]; // scm供应商集合
	scmDistributorUserId?: number[]; // scm分销商集合
	shopIsActualSelect?: number; // 是否实际勾选平台店铺0 1
	[k: string]: any;
}

/**
 * ResponseBody<WebPageResponse<RspSelectRefundListWithPageDTO>> :ResponseBody
 */
export interface SelectRefundListWithPageResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * WebPageResponse
	 */
	data?: {
		/**
		 * 第几页
		 */
		pageNo?: number;
		/**
		 * 一页多少条
		 */
		pageSize?: number;
		/**
		 * 总页数
		 */
		pageCount?: number;
		/**
		 * 总数
		 */
		total?: number;
		/**
		 * 列表 ,T
		 */
		list?: {
			/**
			 * 退款单我们这边的唯一id
			 */
			id?: string;
			/**
			 * 平台TB、TM（天猫待定）、PDD、FXG（抖店）、HAND（手工单）
			 */
			platform?: string;
			/**
			 * 买家昵称
			 */
			buyerNick?: string;
			/**
			 * 买家唯一标识
			 */
			buyerOpenUid?: string;
			/**
			 * 店铺名称
			 */
			sellerNick?: string;
			/**
			 * 店铺ID
			 */
			sellerId?: string;
			/**
			 * tid
			 */
			tid?: string;
			/**
			 * 退款编号
			 */
			refundId?: string;
			/**
			 * 退款创建时间
			 */
			refundCreatedTime?: string;
			/**
			 * 退款更新时间
			 */
			refundModifiedTime?: string;
			/**
			 * 退款金额
			 */
			refundAmount?: string;
			/**
			 * 货物状态,快手[0,"未知"][1,"未收到货"][2,"已收到货"]
			 */
			goodsStatus?: string;
			/**
			 * 订单实付金额
			 */
			payment?: string;
			/**
			 * 订单总金额
			 */
			totalFee?: string;
			/**
			 * 收件人姓名,表字段:receiver_name
			 */
			receiverName?: string;
			/**
			 * 收件人手机号,表字段:receiver_mobile
			 */
			receiverMobile?: string;
			/**
			 * 收件人省,表字段:receiver_state
			 */
			receiverState?: string;
			/**
			 * 收件人市,表字段:receiver_city
			 */
			receiverCity?: string;
			/**
			 * 收件人区,表字段:receiver_district
			 */
			receiverDistrict?: string;
			/**
			 * 收件人详细地址,表字段:receiver_address
			 */
			receiverAddress?: string;
			/**
			 * 售后类型,空全部,1仅退款,2退货退款,3换货,4补差价,5补发货品
			 */
			afterSaleType?: number;
			/**
			 * 退款原因
			 */
			refundReason?: string;
			/**
			 * 退款说明
			 */
			desc?: string;
			/**
			 * erp备注
			 */
			erpMemo?: string;
			/**
			 * 退款说明
			 */
			desc?: string;
			/**
			 * erp备注
			 */
			erpMemo?: string;
			/**
			 * 退款状态,1等待卖家同意,2等待买家退货,3等待卖家确认收货,4退款成功,5退款关闭,6卖家拒绝退款
			 */
			refundStatus?: number;
			/**
			 * 退款状态说明
			 */
			refundStatusDesc?: string;
			/**
			 * 订单交易状态
			 */
			orderStatus?: string;
			/**
			 * 订单交易状态说明
			 */
			orderStatusDesc?: string;
			/**
			 * 卖方备注
			 */
			sellerMemo?: string;
			/**
			 * 买家旗帜
			 */
			sellerFlag?: string;
			/**
			 * 退货运单号
			 */
			sid?: string;
			/**
			 * 发货运单号
			 */
			invoiceNo?: string;
			/**
			 * 存在超时
			 */
			existTimeout?: boolean;
			/**
			 * 超时时间,表字段:time_out
			 */
			timeOut?: string;
			/**
			 * 售后渠道,1线上同步,2手工录入
			 */
			afterSaleChannel?: number;
			/**
			 * 异常类型,0无异常,1异常,2忽略异常
			 */
			exceptionType?: number;
			/**
			 * 买家留言
			 */
			buyerMessage?: string;
			/**
			 * 审核状态
			 */
			reviewStatus?: boolean;
			/**
			 * 存放每个订单能用的按钮AGREE_REFUND-同意退款REFUSE_REFUND-拒绝退款AGREE_RETURN_GOODS-同意退货REFUSE_RETURN_GOODS-拒绝退货 ,String
			 */
			buttonList?: string[];
			/**
			 * 淘宝商家类型0-淘宝1-天猫
			 */
			tbSellerType?: number;
			/**
			 * 售后商品类型0:退款商品1:退货商品
			 */
			refundItemType?: number;
			/**
			 * 工单类型
			 */
			refundSystemType?: number;
			/**
			 * 换货手工订单编号
			 */
			exchangeTid?: string;
			/**
			 * 换货手工子订单id
			 */
			exchangeOid?: string;
			/**
			 * 换货发货单号
			 */
			exchangeInvoiceNo?: string;
			/**
			 * 换货物流公司名称
			 */
			exchangeShipName?: string;
			/**
			 * 换货物流公司code
			 */
			exchangeShipCode?: string;
			/**
			 * 换货收件人省
			 */
			exchangeReceiverProvince?: string;
			/**
			 * 换货收件人市
			 */
			exchangeReceiverCity?: string;
			/**
			 * 换货收件人区
			 */
			exchangeReceiverTown?: string;
			/**
			 * 换货收件人详细地址
			 */
			exchangeReceiverAddress?: string;
			/**
			 * 换货收件人姓名
			 */
			exchangeReceiverName?: string;
			/**
			 * 换货收件人手机号
			 */
			exchangeReceiverMobile?: string;
			/**
			 * 换货收件人详细地址脱敏
			 */
			exchangeReceiverAddressMask?: string;

			/**
			 * 换货收件人姓名脱敏
			 */
			exchangeReceiverNameMask?: string;

			/**
			 * 换货收件人手机号脱敏
			 */
			exchangeReceiverMobileMask?: string;
			/**
			 * 订单来源
			 */
			source?: string;
			/**
			 * 是否无主件
			 */
			isNoTradeMess?: boolean;
			/**
			 * 换货发货信息 ,ExchangeInvoiceDTO
			 */
			exchangeInvoices?: {
				exchangeInvoiceNo?: string;
				exchangeShipName?: string;
				exchangeShipCode?: string;
				exchangeShipId?: string;
				[k: string]: any;
			}[];
			/**
			 * tb收件人ID
			 */
			oaid?: string;
			/**
			 * 换货订单状态，枚举：ExchangeTradeStatusEnum
			 */
			exchangeTradeStatus?: number;
			/**
			 * 退款货品信息 ,RspRefundItemRecordInfo
			 */
			refundItemRecordInfos?: {
				/**
				 * 货品唯一id
				 */
				id?: number;
				/**
				 * 货品id
				 */
				sysItemId?: string;
				/**
				 * 货品规格id
				 */
				sysSkuId?: string;
				/**
				 * 图片链接
				 */
				picUrl?: string;
				/**
				 * 货品简称
				 */
				itemAlias?: string;
				/**
				 * 货品规格简称
				 */
				skuAlias?: string;
				/**
				 * 订单对应货品数量
				 */
				num?: number;
				/**
				 * 申请退货数量
				 */
				applyRefundNum?: number;
				/**
				 * 已收货数量
				 */
				hasRefundNum?: number;
				/**
				 * 已处理数量
				 */
				hasDisposeNum?: number;
				/**
				 * 规格名称
				 */
				skuName?: string;
				/**
				 * sku商家编码
				 */
				outerSkuId?: string;
				/**
				 * 创建时间
				 */
				createTime?: string;
				/**
				 * 是否为组合商品，1:组合货品,0:非组合货品
				 */
				isCombination?: number;
				/**
				 * 商品类型
				 */
				refundItemType?: number;
				/**
				 * 商品来源0-平台快照1-手动添加
				 */
				refundItemSource?: number;
				[k: string]: any;
			}[];
			/**
			 * 退款平台商品信息 ,RspRefundItemRecordInfo
			 */
			refundPlatformItemRecordInfos?: {
				/**
				 * 货品唯一id
				 */
				id?: number;
				/**
				 * 货品id
				 */
				sysItemId?: string;
				/**
				 * 货品规格id
				 */
				sysSkuId?: string;
				/**
				 * 图片链接
				 */
				picUrl?: string;
				/**
				 * 货品简称
				 */
				itemAlias?: string;
				/**
				 * 货品规格简称
				 */
				skuAlias?: string;
				/**
				 * 订单对应货品数量
				 */
				num?: number;
				/**
				 * 申请退货数量
				 */
				applyRefundNum?: number;
				/**
				 * 已收货数量
				 */
				hasRefundNum?: number;
				/**
				 * 已处理数量
				 */
				hasDisposeNum?: number;
				/**
				 * 规格名称
				 */
				skuName?: string;
				/**
				 * sku商家编码
				 */
				outerSkuId?: string;
				/**
				 * 创建时间
				 */
				createTime?: string;
				/**
				 * 是否为组合商品，1:组合货品,0:非组合货品
				 */
				isCombination?: number;
				/**
				 * 商品类型
				 */
				refundItemType?: number;
				/**
				 * 商品来源0-平台快照1-手动添加
				 */
				refundItemSource?: number;
				[k: string]: any;
			}[];
			/**
			 * 退货运单号主物流状态,表字段:sid_status
			 */
			sidStatus?: string;
			/**
			 * 退货运单号子物流状态,表字段:sid_sub_status
			 */
			sidSubStatus?: string;
			/**
			 * 发货运单号主物流状态,表字段:invoice_status
			 */
			invoiceStatus?: string;
			/**
			 * 发货运单号子物流状态,表字段:invoice_sub_status
			 */
			invoiceSubStatus?: string;
			[k: string]: any;
		}[];
		/**
		 * 分销售后单-前往分销商的售后页面处理 ,String
		 */
		distributorPushList?: string[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqignoreMatchSystemItemExceptionRefundDTO :ReqignoreMatchSystemItemExceptionRefundDTO
 */
export interface IgnoreMatchSystemItemExceptionRefundRequest {
	/**
	 * id,售后记录在我们这边的唯一ID ,Long
	 */
	ids?: number[];
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface IgnoreMatchSystemItemExceptionRefundResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqQueryItemRecordParamsDTO :ReqQueryItemRecordParamsDTO
 */
export interface FindSellerRefundForEditRequest {
	/**
	 * 退款列表id
	 */
	id?: number;
	[k: string]: any;
}

/**
 * ResponseBody<RspSelectRefundListWithPageDTO> :ResponseBody
 */
export interface FindSellerRefundForEditResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspSelectRefundListWithPageDTO
	 */
	data?: {
		/**
		 * 退款单我们这边的唯一id
		 */
		id?: string;
		/**
		 * 平台TB、TM（天猫待定）、PDD、FXG（抖店）、HAND（手工单）
		 */
		platform?: string;
		/**
		 * 买家昵称
		 */
		buyerNick?: string;
		/**
		 * 买家唯一标识
		 */
		buyerOpenUid?: string;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 店铺ID
		 */
		sellerId?: string;
		/**
		 * tid
		 */
		tid?: string;
		/**
		 * 退款编号
		 */
		refundId?: string;
		/**
		 * 退款创建时间
		 */
		refundCreatedTime?: string;
		/**
		 * 退款更新时间
		 */
		refundModifiedTime?: string;
		/**
		 * 退款金额
		 */
		refundAmount?: string;
		/**
		 * 货物状态,快手[0,"未知"][1,"未收到货"][2,"已收到货"]
		 */
		goodsStatus?: string;
		/**
		 * 订单实付金额
		 */
		payment?: string;
		/**
		 * 订单总金额
		 */
		totalFee?: string;
		/**
		 * 收件人姓名,表字段:receiver_name
		 */
		receiverName?: string;
		/**
		 * 收件人手机号,表字段:receiver_mobile
		 */
		receiverMobile?: string;
		/**
		 * 收件人省,表字段:receiver_state
		 */
		receiverState?: string;
		/**
		 * 收件人市,表字段:receiver_city
		 */
		receiverCity?: string;
		/**
		 * 收件人区,表字段:receiver_district
		 */
		receiverDistrict?: string;
		/**
		 * 收件人详细地址,表字段:receiver_address
		 */
		receiverAddress?: string;
		/**
		 * 售后类型,空全部,1仅退款,2退货退款,3换货,4补差价,5补发货品
		 */
		afterSaleType?: number;
		/**
		 * 退款原因
		 */
		refundReason?: string;
		/**
		 * 退款说明
		 */
		desc?: string;
		/**
		 * erp备注
		 */
		erpMemo?: string;
		/**
		 * 退款状态,1等待卖家同意,2等待买家退货,3等待卖家确认收货,4退款成功,5退款关闭,6卖家拒绝退款
		 */
		refundStatus?: number;
		/**
		 * 退款状态说明
		 */
		refundStatusDesc?: string;
		/**
		 * 订单交易状态
		 */
		orderStatus?: string;
		/**
		 * 订单交易状态说明
		 */
		orderStatusDesc?: string;
		/**
		 * 卖方备注
		 */
		sellerMemo?: string;
		/**
		 * 买家旗帜
		 */
		sellerFlag?: string;
		/**
		 * 退货运单号
		 */
		sid?: string;
		/**
		 * 发货运单号
		 */
		invoiceNo?: string;
		/**
		 * 存在超时
		 */
		existTimeout?: boolean;
		/**
		 * 超时时间,表字段:time_out
		 */
		timeOut?: string;
		/**
		 * 售后渠道,1线上同步,2手工录入
		 */
		afterSaleChannel?: number;
		/**
		 * 异常类型,0无异常,1异常,2忽略异常
		 */
		exceptionType?: number;
		/**
		 * 买家留言
		 */
		buyerMessage?: string;
		/**
		 * 审核状态
		 */
		reviewStatus?: boolean;
		/**
		 * 存放每个订单能用的按钮AGREE_REFUND-同意退款REFUSE_REFUND-拒绝退款AGREE_RETURN_GOODS-同意退货REFUSE_RETURN_GOODS-拒绝退货 ,String
		 */
		buttonList?: string[];
		/**
		 * 淘宝商家类型0-淘宝1-天猫
		 */
		tbSellerType?: number;
		/**
		 * 售后商品类型0:退款商品1:退货商品
		 */
		refundItemType?: number;
		/**
		 * 工单类型
		 */
		refundSystemType?: number;
		/**
		 * 换货手工订单编号
		 */
		exchangeTid?: string;
		/**
		 * 换货手工子订单id
		 */
		exchangeOid?: string;
		/**
		 * 换货发货单号
		 */
		exchangeInvoiceNo?: string;
		/**
		 * 换货物流公司名称
		 */
		exchangeShipName?: string;
		/**
		 * 换货物流公司code
		 */
		exchangeShipCode?: string;
		/**
		 * 换货收件人省
		 */
		exchangeReceiverProvince?: string;
		/**
		 * 换货收件人市
		 */
		exchangeReceiverCity?: string;
		/**
		 * 换货收件人区
		 */
		exchangeReceiverTown?: string;
		/**
		 * 换货收件人详细地址
		 */
		exchangeReceiverAddress?: string;
		/**
		 * 换货收件人姓名
		 */
		exchangeReceiverName?: string;
		/**
		 * 换货收件人手机号
		 */
		exchangeReceiverMobile?: string;
		/**
		 * 订单来源
		 */
		source?: string;
		/**
		 * 是否无主件
		 */
		isNoTradeMess?: boolean;
		/**
		 * 换货发货信息 ,ExchangeInvoiceDTO
		 */
		exchangeInvoices?: {
			exchangeInvoiceNo?: string;
			exchangeShipName?: string;
			exchangeShipCode?: string;
			exchangeShipId?: string;
			[k: string]: any;
		}[];
		/**
		 * tb收件人ID
		 */
		oaid?: string;
		/**
		 * 换货订单状态，枚举：ExchangeTradeStatusEnum
		 */
		exchangeTradeStatus?: number;
		/**
		 * 退款货品信息 ,RspRefundItemRecordInfo
		 */
		refundItemRecordInfos?: {
			/**
			 * 货品唯一id
			 */
			id?: number;
			/**
			 * 货品id
			 */
			sysItemId?: string;
			/**
			 * 货品规格id
			 */
			sysSkuId?: string;
			/**
			 * 图片链接
			 */
			picUrl?: string;
			/**
			 * 货品简称
			 */
			itemAlias?: string;
			/**
			 * 货品规格简称
			 */
			skuAlias?: string;
			/**
			 * 订单对应货品数量
			 */
			num?: number;
			/**
			 * 申请退货数量
			 */
			applyRefundNum?: number;
			/**
			 * 已收货数量
			 */
			hasRefundNum?: number;
			/**
			 * 已处理数量
			 */
			hasDisposeNum?: number;
			/**
			 * 规格名称
			 */
			skuName?: string;
			/**
			 * sku商家编码
			 */
			outerSkuId?: string;
			/**
			 * 创建时间
			 */
			createTime?: string;
			/**
			 * 是否为组合商品，1:组合货品,0:非组合货品
			 */
			isCombination?: number;
			/**
			 * 商品类型
			 */
			refundItemType?: number;
			/**
			 * 商品来源0-平台快照1-手动添加
			 */
			refundItemSource?: number;
			[k: string]: any;
		}[];
		/**
		 * 退款平台商品信息 ,RspRefundItemRecordInfo
		 */
		refundPlatformItemRecordInfos?: {
			/**
			 * 货品唯一id
			 */
			id?: number;
			/**
			 * 货品id
			 */
			sysItemId?: string;
			/**
			 * 货品规格id
			 */
			sysSkuId?: string;
			/**
			 * 图片链接
			 */
			picUrl?: string;
			/**
			 * 货品简称
			 */
			itemAlias?: string;
			/**
			 * 货品规格简称
			 */
			skuAlias?: string;
			/**
			 * 订单对应货品数量
			 */
			num?: number;
			/**
			 * 申请退货数量
			 */
			applyRefundNum?: number;
			/**
			 * 已收货数量
			 */
			hasRefundNum?: number;
			/**
			 * 已处理数量
			 */
			hasDisposeNum?: number;
			/**
			 * 规格名称
			 */
			skuName?: string;
			/**
			 * sku商家编码
			 */
			outerSkuId?: string;
			/**
			 * 创建时间
			 */
			createTime?: string;
			/**
			 * 是否为组合商品，1:组合货品,0:非组合货品
			 */
			isCombination?: number;
			/**
			 * 商品类型
			 */
			refundItemType?: number;
			/**
			 * 商品来源0-平台快照1-手动添加
			 */
			refundItemSource?: number;
			[k: string]: any;
		}[];
		/**
		 * 退货运单号主物流状态,表字段:sid_status
		 */
		sidStatus?: string;
		/**
		 * 退货运单号子物流状态,表字段:sid_sub_status
		 */
		sidSubStatus?: string;
		/**
		 * 发货运单号主物流状态,表字段:invoice_status
		 */
		invoiceStatus?: string;
		/**
		 * 发货运单号子物流状态,表字段:invoice_sub_status
		 */
		invoiceSubStatus?: string;
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * ReqFinishRefundParamsDTO :ReqFinishRefundParamsDTO
 */
export interface ConfirmFinishRequest {
	/**
	 * id,售后在我们这边的唯一标志 ,Long
	 */
	ids?: number[];
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ConfirmFinishResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqSaveEditedRefundTradeParamsDTO :ReqSaveEditedRefundTradeParamsDTO
 */
export interface SaveEditedRefundTradeRequest {
	/**
	 * 退款记录唯一id
	 */
	id?: string;
	/**
	 * 售后类型,1仅退款,2退货退款
	 */
	afterSaleType?: number;
	/**
	 * 自定义退款货品信息 ,SaveEditedRefundItemDTO
	 */
	saveEditedRefundItemDTOS?: {
		/**
		 * 货品唯一id
		 */
		id?: number;
		/**
		 * 图片地址
		 */
		picUrl?: string;
		/**
		 * 货品标题
		 */
		itemTitle?: string;
		/**
		 * 商家编码
		 */
		outerSkuId?: string;
		/**
		 * 规格名称
		 */
		skuInfo?: string;
		/**
		 * 申请退货数量
		 */
		applyRefundNum?: number;
		/**
		 * 订单对应货品数量
		 */
		num?: number;
		[k: string]: any;
	}[];
	/**
	 * erp备注
	 */
	erpMemo?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface SaveEditedRefundTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * ReqFinishRefundParamsDTO :ReqFinishRefundParamsDTO
 */
export interface CloseRefundRequest {
	/**
	 * id,售后在我们这边的唯一标志 ,Long
	 */
	ids?: number[];
	[k: string]: any;
}


/**
 * ResponseBody<Void> :ResponseBody
 */
export interface CloseRefundResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ResponseBody<List<RspSelectNeedReminderSyncDTO>> :ResponseBody
 */
export interface SelectNeedReminderSyncResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspSelectNeedReminderSyncDTO
	 */
	data?: {
		/**
		 * 店铺昵称
		 */
		sellerNick?: string;
		/**
		 * 平台
		 */
		platform?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
 * ReqSyncRefundTradeParamsDTO :ReqSyncRefundTradeParamsDTO
 */
export interface SyncRefundTradeRequest {
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺ID
	 */
	sellerId?: string;
	/**
	 * 店铺名称
	 */
	sellerNick?: string;
	/**
	 * 同步方式,1指定售后ID,2指定申请时间范围
	 */
	syncWay?: number;
	/**
	 * 退款id
	 */
	refundId?: string;
	/**
	 * tid
	 */
	tid?: string;
	/**
	 * 开始时间
	 */
	startTime?: string;
	/**
	 * 结束时间
	 */
	endTime?: string;
	/**
	 * 平台店铺集合 ,SyncRefundPlatformShop
	 */
	shopList?: {
		/**
		 * 店铺ID
		 */
		sellerId?: number;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 平台
		 */
		platform?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}


/**
 * ResponseBody<List<RspSyncRefundShopDTO>> :ResponseBody
 */
export interface SyncRefundTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspSyncRefundShopDTO
	 */
	data?: {
		/**
		 * 平台
		 */
		platform?: string;
		/**
		 * 店铺ID
		 */
		sellerId?: number;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 错误信息
		 */
		errorMessage?: string;
		/**
		 * 进度
		 */
		process?: string;
		/**
		 * 扩展信息
		 */
		extendInfo?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}


/**
 * ReqReviewRefundTradeParamsDTO :ReqReviewRefundTradeParamsDTO
 */
export interface ReviewRefundTradeRequest {
	/**
	 * id列表 ,Long
	 */
	ids?: number[];
	/**
	 * 操作类型,true审核,false取消审核
	 */
	opType?: boolean;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ReviewRefundTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqHandleReturnItemParamsDTO :ReqHandleReturnItemParamsDTO
 */
export interface HandleReturnItemRequest {
	/**
	 * 处理类型,RU_KU("入库"),,XIAO_HUI("销毁"),,TUI_HUI("退回")
	 */
	handleType?: {
		[k: string]: any;
	};
	/**
	 * 退款id
	 */
	refundInfoId?: string;
	/**
	 * 处理详细 ,HandleDetail
	 */
	handleDetails?: {
		/**
		 * 货品简称
		 */
		itemAlias?: string;
		/**
		 * 退款商品记录id
		 */
		refundItemRecordId?: number;
		/**
		 * 处理数量
		 */
		num?: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<List<BatchResult>> :ResponseBody
 */
export interface HandleReturnItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchResult
	 */
	data?: {
		operationId?: string;
		success?: boolean;
		errorCode?: number;
		errorMessage?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ReqConfirmReturnItemParamsDTO :ReqConfirmReturnItemParamsDTO
 */
export interface ConfirmReturnItemRequest {
	/**
	 * 退款id
	 */
	refundInfoId?: string;
	/**
	 * 确认收货详细的列表 ,ConfirmReturnItemRecord
	 */
	detailList?: {
		/**
		 * 确认的数量
		 */
		num?: number;
		/**
		 * 退款商品记录ID
		 */
		refundItemRecordId?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ConfirmReturnItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * ResponseBody<String> :ResponseBody
 */
export interface SyncRefundAddressResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
}

/**
 * ReqSetDefaultAddressParamsDTO :ReqSetDefaultAddressParamsDTO
 */
export interface SetDefaultRefundAddressRequest {
	/**
	 * 记录id
	 */
	id?: number;
	/**
	 * 店铺ID
	 */
	sellerId?: string;
	/**
	 * 操作类型：0-取消 1-设为默认
	 */
	type?: number;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface SetDefaultRefundAddressResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * AgreeRefundFeeReqDto :AgreeRefundFeeReqDto
 */
export interface AgreeRefundFeeRequest {
	/**
	 * id列表
	 */
	id: number;
	/**
	 * 验证码
	 */
	code?: string;
	/**
	 * 类型true-第二次false-非第二次
	 */
	isSecond: boolean;
	/**
	 * 退货物流运单号
	 */
	sid?: string;
	/**
	 * 退货物流公司编号
	 */
	logisticsCompanyCode?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface AgreeRefundFeeResponse {
	success?: boolean;
	/**
	 * errorCode为5810表示需要填验证码
	 */
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * RefuseRefundReasonReqDto :RefuseRefundReasonReqDto
 */
export interface GetRefuseReasonListRequest {
	/**
	 * id列表
	 */
	id: number;
	[k: string]: any;
}

/**
 * ResponseBody<List<RefuseRefundReasonResDto>> :ResponseBody
 */
export interface GetRefuseReasonListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RefuseRefundReasonResDto
	 */
	data?: {
		/**
		 * 淘宝-原因编号抖音-拒绝原因编码
		 */
		reasonId?: number;
		/**
		 * 淘宝-原因文本抖音-拒绝原因文案
		 */
		reasonText?: string;
		/**
		 * 抖音-凭证描述文案
		 */
		evidenceDesc?: string;
		/**
		 * 抖音-是否需要上传凭证，Y必填，N非必填
		 */
		evidenceNeed?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * RefuseRefundFeeReqDto :RefuseRefundFeeReqDto
 */
export interface RefuseRefundFeeRequest {
	/**
	 * id列表
	 */
	id?: number;
	/**
	 * 拒绝退款凭证图片base64字符串(仅支持jpg,png,2MB以内)抖音-拒绝凭证url
	 */
	proofFile?: string;
	/**
	 * 淘宝-拒绝退款说明抖音-操作评论
	 */
	refuseMessage?: string;
	/**
	 * 淘宝-原因编号抖音-拒绝原因编码
	 */
	reasonId?: number;
	/**
	 * 淘宝-原因文本抖音必传-拒绝原因
	 */
	reasonText?: string;
	/**
	 * 抖音-是否需要上传凭证，Y必填，N非必填
	 */
	evidenceNeed?: string;
	[k: string]: any;
}


/**
 * ResponseBody<Void> :ResponseBody
 */
export interface RefuseRefundFeeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * AgreeRefundCheckAddressReqDto :AgreeRefundCheckAddressReqDto
 */
export interface QueryHasDefaultRefundAddrRequest {
	/**
	 * 平台类型
	 */
	platform: string;
	/**
	 * 店铺id
	 */
	sellerId: string;
	/**
	 * userId
	 */
	userId?: number;
	[k: string]: any;
}


/**
 * ResponseBody<RefundAddressDTO> :ResponseBody
 */
export interface QueryHasDefaultRefundAddrResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RefundAddressDTO
	 */
	data?: {
		/**
		 * @mbg.generated,表字段:id
		 */
		id?: number;
		/**
		 * @mbg.generated,用户ID,表字段:user_id
		 */
		userId?: number;
		/**
		 * @mbg.generated,平台类型,表字段:platform
		 */
		platform?: string;
		/**
		 * @mbg.generated,店铺ID,表字段:seller_id
		 */
		sellerId?: string;
		/**
		 * @mbg.generated,店铺名称,表字段:seller_nick
		 */
		sellerNick?: string;
		/**
		 * @mbg.generated,逻辑删除,表字段:enable_status
		 */
		enableStatus?: boolean;
		/**
		 * @mbg.generated,创建时间,表字段:gmt_created
		 */
		gmtCreated?: string;
		/**
		 * @mbg.generated,更新时间,表字段:gmt_modified
		 */
		gmtModified?: string;
		/**
		 * @mbg.generated,地址库ID,表字段:refund_address_id
		 */
		refundAddressId?: string;
		/**
		 * @mbg.generated,省份,表字段:refund_province
		 */
		refundProvince?: string;
		/**
		 * @mbg.generated,城市,表字段:refund_city
		 */
		refundCity?: string;
		/**
		 * @mbg.generated,区县,表字段:refund_district
		 */
		refundDistrict?: string;
		/**
		 * @mbg.generated,街道,表字段:refund_street
		 */
		refundStreet?: string;
		/**
		 * @mbg.generated,详细地址,表字段:refund_detail
		 */
		refundDetail?: string;
		/**
		 * @mbg.generated,退货收件人名字,表字段:refund_name
		 */
		refundName?: string;
		/**
		 * @mbg.generated,退货收件人手机号,表字段:refund_phone
		 */
		refundPhone?: string;
		/**
		 * @mbg.generated,退货收件人固话,表字段:refund_tel
		 */
		refundTel?: string;
		/**
		 * @mbg.generated,地区邮政编码,表字段:zip_code
		 */
		zipCode?: string;
		/**
		 * @mbg.generated,公司名称,表字段:seller_company
		 */
		sellerCompany?: string;
		/**
		 * @mbg.generated,备注,表字段:memo
		 */
		memo?: string;
		/**
		 * @mbg.generated,发货默认0:否1:是,表字段:send_default
		 */
		sendDefault?: boolean;
		/**
		 * @mbg.generated,退货默认。0:否1:是,表字段:refund_default
		 */
		refundDefault?: boolean;
		/**
		 * @mbg.generated,平台更新时间,表字段:platform_modified
		 */
		platformModified?: string;
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * CheckRefundLogisticsReqDto :CheckRefundLogisticsReqDto
 */
export interface CheckAfterAuthorityRequest {
	/**
	 * 售后id
	 */
	id?: number;
	sellerIdList?: string[];
	sellerRefundNumDtoList?: {
		sellerId: string;
		refundNum: number;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface CheckAfterAuthorityResponse {
	success?: boolean;
	errorCode?: number; // 5925、5926、5927
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		agreeRefundType?: number; // 退款方式 1-TB退款验证 2-分批处理
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqRefundAddressParamsDTO :ReqRefundAddressParamsDTO
 */
export interface QueryRefundAddressRequest {
	/**
	 * 联系人
	 */
	refundName?: string;
	/**
	 * 手机或电话，只有淘宝有
	 */
	refundPhone?: string;
	userId?: number;
	pageNo?: number;
	pageSize?: number;
	[k: string]: any;
}

/**
 * ResponseBody<WebPageResponse<RefundAddressDTO>> :ResponseBody
 */
export interface QueryRefundAddressResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * WebPageResponse
	 */
	data?: {
		/**
		 * 第几页
		 */
		pageNo?: number;
		/**
		 * 一页多少条
		 */
		pageSize?: number;
		/**
		 * 总页数
		 */
		pageCount?: number;
		/**
		 * 总数
		 */
		total?: number;
		/**
		 * 列表 ,T
		 */
		list?: {
			/**
			 * @mbg.generated,表字段:id
			 */
			id?: number;
			/**
			 * @mbg.generated,用户ID,表字段:user_id
			 */
			userId?: number;
			/**
			 * @mbg.generated,平台类型,表字段:platform
			 */
			platform?: string;
			/**
			 * @mbg.generated,店铺ID,表字段:seller_id
			 */
			sellerId?: string;
			/**
			 * @mbg.generated,店铺名称,表字段:seller_nick
			 */
			sellerNick?: string;
			/**
			 * @mbg.generated,逻辑删除,表字段:enable_status
			 */
			enableStatus?: boolean;
			/**
			 * @mbg.generated,创建时间,表字段:gmt_created
			 */
			gmtCreated?: string;
			/**
			 * @mbg.generated,更新时间,表字段:gmt_modified
			 */
			gmtModified?: string;
			/**
			 * @mbg.generated,地址库ID,表字段:refund_address_id
			 */
			refundAddressId?: string;
			/**
			 * @mbg.generated,省份,表字段:refund_province
			 */
			refundProvince?: string;
			/**
			 * @mbg.generated,城市,表字段:refund_city
			 */
			refundCity?: string;
			/**
			 * @mbg.generated,区县,表字段:refund_district
			 */
			refundDistrict?: string;
			/**
			 * @mbg.generated,街道,表字段:refund_street
			 */
			refundStreet?: string;
			/**
			 * @mbg.generated,详细地址,表字段:refund_detail
			 */
			refundDetail?: string;
			/**
			 * @mbg.generated,退货收件人名字,表字段:refund_name
			 */
			refundName?: string;
			/**
			 * @mbg.generated,退货收件人手机号,表字段:refund_phone
			 */
			refundPhone?: string;
			/**
			 * @mbg.generated,退货收件人固话,表字段:refund_tel
			 */
			refundTel?: string;
			/**
			 * @mbg.generated,地区邮政编码,表字段:zip_code
			 */
			zipCode?: string;
			/**
			 * @mbg.generated,公司名称,表字段:seller_company
			 */
			sellerCompany?: string;
			/**
			 * @mbg.generated,备注,表字段:memo
			 */
			memo?: string;
			/**
			 * @mbg.generated,发货默认0:否1:是,表字段:send_default
			 */
			sendDefault?: boolean;
			/**
			 * @mbg.generated,退货默认。0:否1:是,表字段:refund_default
			 */
			refundDefault?: boolean;
			/**
			 * @mbg.generated,平台更新时间,表字段:platform_modified
			 */
			platformModified?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * AgreeRefundGoodsReqDto :AgreeRefundGoodsReqDto
 */
export interface AgreeRefundGoodsRequest {
	/**
	 * id
	 */
	id?: number;
	/**
	 * 卖家退货留言
	 */
	remark?: string;
	/**
	 * 退货地址
	 */
	addressId?: number;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface AgreeRefundGoodsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * RefuseRefundGoodsReqDto :RefuseRefundGoodsReqDto
 */
export interface RefuseRefundGoodsRequest {
	/**
	 * id列表
	 */
	id?: number;
	/**
	 * 拒绝退款凭证图片base64字符串(仅支持jpg,png,2MB以内)抖音-拒绝凭证url
	 */
	proofFile?: string;
	/**
	 * 淘宝-拒绝退款说明抖音-操作评论
	 */
	refuseMessage?: string;
	/**
	 * 淘宝-原因编号抖音-拒绝原因编码
	 */
	reasonId?: number;
	/**
	 * 淘宝-原因文本抖音必传-拒绝原因
	 */
	reasonText?: string;
	/**
	 * 抖音-是否需要上传凭证，Y必填，N非必填
	 */
	evidenceNeed?: string;
	[k: string]: any;
}


/**
 * ResponseBody<Void> :ResponseBody
 */
export interface RefuseRefundGoodsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * CheckRefundLogisticsReqDto :CheckRefundLogisticsReqDto
 */
export interface CheckHasRefundLogisticsRequest {
	/**
	 * 售后id
	 */
	id?: number;
	[k: string]: any;
}

/**
 * ResponseBody<Integer> :ResponseBody
 */
export interface CheckHasRefundLogisticsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: number;
	[k: string]: any;
}


/**
 * RefundReviewTradeReqDto :RefundReviewTradeReqDto
 */
export interface ReviewRefundTradeV2Request {
	/**
	 * id列表，逗号表示多个
	 */
	id?: string;
	/**
	 * 审核结果true审核通过,false取消审核（映射审核不通过或反审核）
	 */
	result?: boolean;
	/**
	 * 审核留言
	 */
	message?: string;
	/**
	 * 退款id
	 */
	refundId?: string;
	[k: string]: any;
}
/**
* ResponseBody<List<BatchResult>> :ResponseBody
*/
export interface ReviewRefundTradeV2Response {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchResult
	 */
	data?: {
		operationId?: string;
		success?: boolean;
		errorCode?: number;
		errorMessage?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}
export interface IBatchModifyMemoDataProps {
	id?: string;
	platform?: string;
	buyerNick?: string;
	sellerNick?: string;
	sellerId?: string;
	tid?: string;
	ptTid?: string;
	refundId?: string;
	refundCreatedTime?: string;
	refundModifiedTime?: string;
	refundAmount?: string;
	payment?: string;
	totalFee?: string;
	receiverName?: string;
	receiverMobile?: string;
	receiverState?: string;
	receiverCity?: string;
	receiverDistrict?: string;
	receiverAddress?: string;
	afterSaleType?: number;
	refundReason?: string;
	refundStage?: number;
	refundStatus?: number;
	platFormRefundStatus?: string;
	orderStatus?: string;
	sellerMemo?: string;
	sellerFlag?: string;
	sid?: string;
	invoiceNo?: string;
	afterSaleChannel?: number;
	exceptionType?: number;
	source?: 'HAND' | '';
	refundItemRecordInfos?: {
		id?: number;
		picUrl?: string;
		itemAlias?: string;
		skuAlias?: string;
		applyRefundNum?: number;
		hasRefundNum?: number;
		hasDisposeNum?: number;
		skuName?: string;
		outerId?: string;
		[k: string]: any;
	},
	isNoTradeMess?: boolean; // 无主件
}

/**
 * ReqUserAgConfigDTO :ReqUserAgConfigDTO
 */
export interface QueryUserAgConfigRequest {
	[k: string]: any;
}

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface QueryUserAgConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * ReqUserAgConfigDTO :ReqUserAgConfigDTO
 */
export interface SaveUserAgConfigRequest {
	/**
	 * 店铺ag开关是否开启
	 */
	isOpen?: boolean;
	[k: string]: any;
}


/**
 * ResponseBody<Void> :ResponseBody
 */
export interface SaveUserAgConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqShopAgConfigDTO :ReqShopAgConfigDTO
 */
export interface QueryShopsOfficialAgConfigRequest {
	/**
	 * 待查询是否开通ag店铺idList ,String
	 */
	sellerIdList?: string[];
	[k: string]: any;
}


/**
 * ResponseBody<List<String>> :ResponseBody
 */
export interface QueryShopsOfficialAgConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * String
	 */
	data?: string[];
	[k: string]: any;
}



/**
 * ReqPlatformShopAgConfigDTO :ReqPlatformShopAgConfigDTO
 */
export interface SavePlatformShopAgConfigRequest {
	/**
	 * 店铺级别ag配置 ,PlatformAgConfig
	 */
	platformAgConfigList?: {
		/**
		 * 店铺ID
		 */
		sellerId?: string;
		/**
		 * 是否开启
		 */
		isOpen?: boolean;
		[k: string]: any;
	}[];
	[k: string]: any;
}


/**
 * ResponseBody<Void> :ResponseBody
 */
export interface SavePlatformShopAgConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

export interface QueryPlatformShopAgConfigRequest {
	[k: string]: any;
}


/**
 * String
 */
export type Seller = string[];

/**
 * ResponseBody<List<String>> :ResponseBody
 */
export interface QueryPlatformShopAgConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	data?: Seller;
	[k: string]: any;
}

/**
 * ReqSyncRefundTradeParamsDTO :ReqSyncRefundTradeParamsDTO
 */
export interface GetSyncRefundResultRequest {
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺ID
	 */
	sellerId?: string;
	/**
	 * 店铺名称
	 */
	sellerNick?: string;
	/**
	 * 同步方式,1指定售后ID,2指定申请时间范围
	 */
	syncWay?: number;
	/**
	 * 退款id
	 */
	refundId?: string;
	/**
	 * tid
	 */
	tid?: string;
	/**
	 * 开始时间
	 */
	startTime?: string;
	/**
	 * 结束时间
	 */
	endTime?: string;
	/**
	 * 平台店铺集合 ,SyncRefundPlatformShop
	 */
	shopList?: {
		/**
		 * 店铺ID
		 */
		sellerId?: number;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 平台
		 */
		platform?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<List<RspSyncRefundShopDTO>> :ResponseBody
 */
export interface GetSyncRefundResultResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspSyncRefundShopDTO
	 */
	data?: {
		/**
		 * 平台
		 */
		platform?: string;
		/**
		 * 店铺ID
		 */
		sellerId?: number;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 错误信息
		 */
		errorMessage?: string;
		/**
		 * 进度
		 */
		process?: string;
		/**
		 * 扩展信息
		 */
		extendInfo?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
 * BatchAgreeRefundFeeReqDto :BatchAgreeRefundFeeReqDto
 */
export interface BatchAgreeRefundFeeRequest {
	/**
	 * 验证码
	 */
	code?: string;
	/**
	 * 类型true-第二次、false-第一次
	 */
	isSecond?: boolean;
	/**
	 * ids ,Long
	 */
	ids?: number[];
	/**
	 * 批量操作类型
	 * AGREE_ONLY_REFUND(0, "同意仅退款"),
	 * AGREE_RETURN_GOODS(1, "同意退货"),
	 * AGREE_RETURN_GOODS_REFUND(2, "同意退货退款"),
	 * REFUSE_ONLY_REFUND(3, "拒绝仅退款"),
	 * REFUSE_RETURN_GOODS(4, "拒绝退货"),
	 * REFUSE_RETURN_GOODS_REFUND(5, "拒绝退货退款"),
	 * CONFIRM_GOODS(6, "确认收货"),
	 * RETURN_STOCK(7, "退货入库");
	 */
	operateType?: string;
	[k: string]: any;
}


/**
 * ResponseBody<BatchAgreeRefundFeeResDto> :ResponseBody
 */
export interface BatchAgreeRefundFeeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchAgreeRefundFeeResDto
	 */
	data?: {
		/**
		 * 返回码
		 */
		code?: string;
		/**
		 * 成功数
		 */
		successNum?: number;
		/**
		 * 失败数
		 */
		failedNum?: number;
		/**
		 * 失败结果集合 ,BatchResult
		 */
		batchResults?: {
			operationId?: string;
			success?: boolean;
			errorCode?: number;
			errorMessage?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * AgreeRefundCheckAddressReqDto :AgreeRefundCheckAddressReqDto
 */
export interface BatchQueryHasDefaultRefundAddrRequest {
	/**
	 * 平台类型
	 */
	platform?: string;
	/**
	 * 店铺id
	 */
	sellerId?: string;
	/**
	 * userId
	 */
	userId?: number;
	/**
	 * 店铺id集合 ,String
	 */
	sellerIdList?: string[];
	[k: string]: any;
}

/**
 * ResponseBody<List<RefundAddressDTO>> :ResponseBody
 */
export interface BatchQueryHasDefaultRefundAddrResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RefundAddressDTO
	 */
	data?: {
		/**
		 * @mbg.generated,表字段:id
		 */
		id?: number;
		/**
		 * @mbg.generated,用户ID,表字段:user_id
		 */
		userId?: number;
		/**
		 * @mbg.generated,平台类型,表字段:platform
		 */
		platform?: string;
		/**
		 * @mbg.generated,店铺ID,表字段:seller_id
		 */
		sellerId?: string;
		/**
		 * @mbg.generated,店铺名称,表字段:seller_nick
		 */
		sellerNick?: string;
		/**
		 * @mbg.generated,逻辑删除,表字段:enable_status
		 */
		enableStatus?: boolean;
		/**
		 * @mbg.generated,创建时间,表字段:gmt_created
		 */
		gmtCreated?: string;
		/**
		 * @mbg.generated,更新时间,表字段:gmt_modified
		 */
		gmtModified?: string;
		/**
		 * @mbg.generated,地址库ID,表字段:refund_address_id
		 */
		refundAddressId?: string;
		/**
		 * @mbg.generated,省份,表字段:refund_province
		 */
		refundProvince?: string;
		/**
		 * @mbg.generated,城市,表字段:refund_city
		 */
		refundCity?: string;
		/**
		 * @mbg.generated,区县,表字段:refund_district
		 */
		refundDistrict?: string;
		/**
		 * @mbg.generated,街道,表字段:refund_street
		 */
		refundStreet?: string;
		/**
		 * @mbg.generated,详细地址,表字段:refund_detail
		 */
		refundDetail?: string;
		/**
		 * @mbg.generated,退货收件人名字,表字段:refund_name
		 */
		refundName?: string;
		/**
		 * @mbg.generated,退货收件人手机号,表字段:refund_phone
		 */
		refundPhone?: string;
		/**
		 * @mbg.generated,退货收件人固话,表字段:refund_tel
		 */
		refundTel?: string;
		/**
		 * @mbg.generated,地区邮政编码,表字段:zip_code
		 */
		zipCode?: string;
		/**
		 * @mbg.generated,公司名称,表字段:seller_company
		 */
		sellerCompany?: string;
		/**
		 * @mbg.generated,备注,表字段:memo
		 */
		memo?: string;
		/**
		 * @mbg.generated,发货默认0:否1:是,表字段:send_default
		 */
		sendDefault?: boolean;
		/**
		 * @mbg.generated,退货默认。0:否1:是,表字段:refund_default
		 */
		refundDefault?: boolean;
		/**
		 * @mbg.generated,平台更新时间,表字段:platform_modified
		 */
		platformModified?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * BatchAgreeRefundGoodsReqDto :BatchAgreeRefundGoodsReqDto
 */
export interface BatchAgreeRefundGoodsRequest {
	/**
	 * ids ,Long
	 */
	ids?: number[];
	/**
	 * 卖家退货留言
	 */
	remark?: string;
	/**
	 * 批量操作类型
	 */
	operateType?: string;
	/**
	 * 退货地址 ,RefundGoodsShopAddress
	 */
	addressList?: {
		sellerId?: number;
		addressId?: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
 * ResponseBody<BatchAgreeRefundGoodsResDto> :ResponseBody
 */
export interface BatchAgreeRefundGoodsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchAgreeRefundGoodsResDto
	 */
	data?: {
		/**
		 * 成功数
		 */
		successNum?: number;
		/**
		 * 失败数
		 */
		failedNum?: number;
		/**
		 * 失败结果集合 ,BatchResult
		 */
		batchResults?: {
			operationId?: string;
			success?: boolean;
			errorCode?: number;
			errorMessage?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * BatchConfirmReturnItemReqDto :BatchConfirmReturnItemReqDto
 */
export interface BatchConfirmReturnItemRequest {
	/**
	 * 退款单id集合 ,Long
	 */
	ids?: number[];
	[k: string]: any;
}


/**
 * ResponseBody<BatchConfirmReturnItemResDto> :ResponseBody
 */
export interface BatchConfirmReturnItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchConfirmReturnItemResDto
	 */
	data?: {
		/**
		 * 成功数
		 */
		successNum?: number;
		/**
		 * 失败数
		 */
		failedNum?: number;
		/**
		 * 失败结果集合 ,BatchResult
		 */
		batchResults?: {
			operationId?: string;
			success?: boolean;
			errorCode?: number;
			errorMessage?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * BatchHandleReturnItemReqDto :BatchHandleReturnItemReqDto
 */
export interface BatchHandleReturnItemRequest {
	/**
	 * 处理类型,RU_KU("入库"),,XIAO_HUI("销毁"),,TUI_HUI("退回")
	 */
	handleType?: {
		[k: string]: any;
	};
	/**
	 * 退款单id集合 ,Long
	 */
	ids?: number[];
	[k: string]: any;
}
/**
 * ResponseBody<BatchHandleReturnItemResDto> :ResponseBody
 */
export interface BatchHandleReturnItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchHandleReturnItemResDto
	 */
	data?: {
		successNum?: number;
		failedNum?: number;
		/**
		 * 失败结果集合 ,BatchResult
		 */
		batchResults?: {
			operationId?: string;
			success?: boolean;
			errorCode?: number;
			errorMessage?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * 填写换货补发运单号
 */
export interface UploadExchangeInvoiceNoRequest {
	/**
	 * id
	 */
	id?: number;
	/**
	 * 快递单号
	 */
	exchangeInvoiceNo?: string;
	/**
	 * 快递公司名称
	 */
	exchangeShipName?: string;
	/**
	 * 快递公司code
	 */
	exchangeShipCode?: string;
	shippingId?: string;
	[k: string]: any;
}


/**
 * 填写换货补发运单号
 */
export interface UploadExchangeInvoiceNoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * AgreeRefundSupplyReqDto :AgreeRefundSupplyReqDto
 */
export interface AgreeRefundSupplyRequest {
	/**
	 * id
	 */
	id?: number;
	/**
	 * 留言
	 */
	remark?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface AgreeRefundSupplyResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * RefuseRefundSupplyReqDto :RefuseRefundSupplyReqDto
 */
export interface RefuseRefundSupplyRequest {
	/**
	 * id列表
	 */
	id?: number;
	/**
	 * 拒绝退款凭证图片base64字符串(仅支持jpg,png,5MB以内)抖音-拒绝凭证url
	 */
	proofFile?: string;
	/**
	 * 淘宝-拒绝退款说明抖音-操作评论快手-拒绝描述
	 */
	refuseMessage?: string;
	/**
	 * 淘宝-原因编号抖音-拒绝原因编码快手-拒绝原因code
	 */
	reasonId?: number;
	/**
	 * 淘宝-原因文本抖音必传-拒绝原因
	 */
	reasonText?: string;
	/**
	 * 抖音-是否需要上传凭证，Y必填，N非必填快手-图片是否必填
	 */
	evidenceNeed?: string;
	/**
	 * 文件凭证
	 */
	mediaId?: string;
	/**
	 * 操作类型
	 */
	operateType?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface RefuseRefundSupplyResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/** 
 * 绑定订单入参
 */
export interface RefundBindTradeRequest {
	/**
	 * tid
	 */
	tid?: string;
	ptTid?: string;
	/**
	 * 退款单唯一id
	 */
	id?: number;
	[k: string]: any;
}

/**
 * 绑定订单出参
 */
export interface RefundBindTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: number;
	[k: string]: any;
}

/**
 * 查询绑定的订单是否存在-入参
 */
export interface BindTradeQueryRequest {
	/**
	 * tid
	 */
	tid?: string;
	ptTid?: string;
	/**
	 * 平台所有不传值
	 */
	platform?: string;
	/**
	 * 店铺ID
	 */
	sellerId?: string;
	/**
	 * 店铺ID ,String
	 */
	tids?: string[];
	/**
	 * 退款单唯一id
	 */
	id?: string[];
	[k: string]: any;
}

/**
 * 查询绑定的订单是否存在-出参
 */
export interface BindTradeQueryResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}

/**
 * ReqUserAgConfigDTO :ReqUserAgConfigDTO
 */
export interface QueryPlatformShopAgConfig2Request {
	/**
	 * TB("淘宝"),,PDD("拼多多"),,FXG("抖音"),,JD("京东"),,ALI("阿里")
	 */
	platformTypeList?: string[];
	[k: string]: any;
}

export interface QueryPlatformShopAgConfig2Response {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: {
		[k: string]: {
			platform: string,
			agMsg: string,
			sellerId: number,
			status: boolean,
			[k: string]: any;
		}[]
	};
	[k: string]: any;
}

/**
 * ReqSelectRefundLabCountDTO :ReqSelectRefundLabCountDTO
 */
export interface SelectRefundTabCountRequest {
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 开始时间
	 */
	startTime?: string;
	/**
	 * 结束时间
	 */
	endTime?: string;
	scmSelectRefundSource?: number; // 0-正常售后单列表1-分销商的售后单列表
	[k: string]: any;
}

/**
 * ResponseBody<RspSelectRefundLabCountDTO> :ResponseBody
 */
export interface SelectRefundTabCountResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspSelectRefundLabCountDTO
	 */
	data?: {
		/**
		 * 未发货仅退款数量
		 */
		noSendOnlyRefundNum?: string;
		/**
		 * 已发货仅退款
		 */
		sendOnlyRefundNum?: string;
		/**
		 * 退货待处理
		 */
		returnWaitHandleNum?: string;
		/**
		 * 买家已退货
		 */
		buyerAlreadyReturnNum?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqQueryOpLogParamsDTO :ReqQueryOpLogParamsDTO
 */
export interface QueryRefundOpLogRequest {
	/**
	 * 退款id
	 */
	refundId?: string;
	/**
	 * String
	 */
	notQueryOpTypes?: string[];
	userId?: number;
	pageNo?: number;
	pageSize?: number;
	[k: string]: any;
}


/**
 * ResponseBody<PageList<RefundOpLogDTO>> :ResponseBody
 */
export interface QueryRefundOpLogResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: {
			/**
			 * 主键自增id
			 */
			id?: number;
			/**
			 * 用户id
			 */
			userId?: number;
			/**
			 * 平台类型
			 */
			platform?: string;
			/**
			 * 店铺ID
			 */
			sellerId?: string;
			/**
			 * 订单号
			 */
			tid?: string;
			/**
			 * 退款id
			 */
			refundId?: string;
			/**
			 * 操作者用户id
			 */
			opUserId?: number;
			/**
			 * 操作者用户nick
			 */
			opNick?: string;
			/**
			 * 操作类型
			 */
			opType?: string;
			/**
			 * 删除标记
			 */
			enableStatus?: boolean;
			/**
			 * 创建时间
			 */
			gmtCreated?: string;
			/**
			 * 更新时间
			 */
			gmtModified?: string;
			/**
			 * ip
			 */
			ip?: string;
			/**
			 * 日志详情
			 */
			detail?: string;
			/**
			 * 操作名称
			 */
			opName?: string;
			/**
			 * 子订单id
			 */
			oid?: string;
			/**
			 * 订单操作类型1:审核通过2:审核不通过3:同意退款4:拒绝退款5:同意退货6:拒绝退货
			 */
			orderType?: number;
			/**
			 * 操作结果0-失败1-成功
			 */
			opResult?: number;
			/**
			 * 错误信息
			 */
			errMsg?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * CreateExchangeTradeReqDto :CreateExchangeTradeReqDto
 */
export interface CreateExchangeTradeRequest {
	/**
	 * 售后信息id
	 */
	refundInfoId?: number;
	/**
	 * 是否修改收件人信息
	 */
	isUpdate?: boolean;
	/**
	 * 收件人姓名
	 */
	exchangeReceiverName?: string;
	/**
	 * 收件人手机号
	 */
	exchangeReceiverMobile?: string;
	/**
	 * 换货收件人省
	 */
	exchangeReceiverProvince?: string;
	/**
	 * 换货收件人市
	 */
	exchangeReceiverCity?: string;
	/**
	 * 换货收件人区
	 */
	exchangeReceiverTown?: string;
	/**
	 * 收件人详细地址
	 */
	exchangeReceiverAddress?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface CreateExchangeTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: {
			/**
			 * 主键自增id
			 */
			id?: number;
			/**
			 * 用户id
			 */
			userId?: number;
			/**
			 * 平台类型
			 */
			platform?: string;
			/**
			 * 店铺ID
			 */
			sellerId?: string;
			/**
			 * 订单号
			 */
			tid?: string;
			/**
			 * 退款id
			 */
			refundId?: string;
			/**
			 * 操作者用户id
			 */
			opUserId?: number;
			/**
			 * 操作者用户nick
			 */
			opNick?: string;
			/**
			 * 操作类型
			 */
			opType?: string;
			/**
			 * 删除标记
			 */
			enableStatus?: boolean;
			/**
			 * 创建时间
			 */
			gmtCreated?: string;
			/**
			 * 更新时间
			 */
			gmtModified?: string;
			/**
			 * ip
			 */
			ip?: string;
			/**
			 * 日志详情
			 */
			detail?: string;
			/**
			 * 操作名称
			 */
			opName?: string;
			/**
			 * 子订单id
			 */
			oid?: string;
			/**
			 * 订单操作类型1:审核通过2:审核不通过3:同意退款4:拒绝退款5:同意退货6:拒绝退货
			 */
			orderType?: number;
			/**
			 * 操作结果0-失败1-成功
			 */
			opResult?: number;
			/**
			 * 错误信息
			 */
			errMsg?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqRefundExportConfigQueryDto :ReqRefundExportConfigQueryDto
 */
export interface GetRefundExportConfigRequest {
  /**
   * 纬度，0：订单纬度,1：商品纬度
   */
  latitude?: number;
  scmSelectRefundSource?: number;
  [k: string]: any;
}


/**
 * ResponseBody<RspRefundExportConfigDto> :ResponseBody
 */
export interface GetRefundExportConfigResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspRefundExportConfigDto
   */
  data?: {
    /**
     * 纬度，0：订单纬度,1：商品纬度
     */
    latitude?: number;
    /**
     * 可选字段 ,ExportField
     */
    selectableExportFields?: {
      /**
       * 0订单维度1商品维度
       */
      latitude?: number;
      /**
       * 字段名称
       */
      name?: string;
      /**
       * 字段描述
       */
      desc?: string;
      /**
       * 系统默认排序值，升序
       */
      sort?: number;
      /**
       * 用户自定义排序值，升序
       */
      rank?: number;
      [k: string]: any;
    }[];
    /**
     * 导出字段 ,ExportField
     */
    exportFields?: {
      /**
       * 0订单维度1商品维度
       */
      latitude?: number;
      /**
       * 字段名称
       */
      name?: string;
      /**
       * 字段描述
       */
      desc?: string;
      /**
       * 系统默认排序值，升序
       */
      sort?: number;
      /**
       * 用户自定义排序值，升序
       */
      rank?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqRefundExportConfigUpdateDto :ReqRefundExportConfigUpdateDto
 */
export interface UpdateRefundExportConfigRequest {
  /**
   * 纬度，0：订单纬度,1：商品纬度
   */
  latitude?: number;
  scmSelectRefundSource?: number;
  /**
   * 导出字段 ,ExportField
   */
  fields?: {
    /**
     * 0订单维度1商品维度
     */
    latitude?: number;
    /**
     * 字段名称
     */
    name?: string;
    /**
     * 字段描述
     */
    desc?: string;
    /**
     * 系统默认排序值，升序
     */
    sort?: number;
    /**
     * 用户自定义排序值，升序
     */
    rank?: number;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface UpdateRefundExportConfigResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqRefundExportConfigUpdateDto :ReqRefundExportConfigUpdateDto
 */
export interface ResetRefundExportConfigRequest {
  /**
   * 纬度，0：订单纬度,1：商品纬度
   */
  latitude?: number;
  scmSelectRefundSource?: number;
  /**
   * 导出字段 ,ExportField
   */
  fields?: {
    /**
     * 0订单维度1商品维度
     */
    latitude?: number;
    /**
     * 字段名称
     */
    name?: string;
    /**
     * 字段描述
     */
    desc?: string;
    /**
     * 系统默认排序值，升序
     */
    sort?: number;
    /**
     * 用户自定义排序值，升序
     */
    rank?: number;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ResponseBody<RspRefundExportConfigDto> :ResponseBody
 */
export interface ResetRefundExportConfigResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspRefundExportConfigDto
   */
  data?: {
    /**
     * 纬度，0：订单纬度,1：商品纬度
     */
    latitude?: number;
    /**
     * 可选字段 ,ExportField
     */
    selectableExportFields?: {
      /**
       * 0订单维度1商品维度
       */
      latitude?: number;
      /**
       * 字段名称
       */
      name?: string;
      /**
       * 字段描述
       */
      desc?: string;
      /**
       * 系统默认排序值，升序
       */
      sort?: number;
      /**
       * 用户自定义排序值，升序
       */
      rank?: number;
      [k: string]: any;
    }[];
    /**
     * 导出字段 ,ExportField
     */
    exportFields?: {
      /**
       * 0订单维度1商品维度
       */
      latitude?: number;
      /**
       * 字段名称
       */
      name?: string;
      /**
       * 字段描述
       */
      desc?: string;
      /**
       * 系统默认排序值，升序
       */
      sort?: number;
      /**
       * 用户自定义排序值，升序
       */
      rank?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqRefundGlobalConfigQueryDto :ReqRefundGlobalConfigQueryDto
 */
export interface GetRefundGlobalConfigListRequest {
  /**
   * 售后全局配置业务类型集合 ,String
   */
  bizEnumList?: string[];
  userId?: number;
  subUserId?: number;
  [k: string]: any;
}

/**
 * ResponseBody<List<RefundGlobalConfigDTO>> :ResponseBody
 */
export interface GetRefundGlobalConfigListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RefundGlobalConfigDTO
   */
  data?: {
    /**
     * id
     */
    id?: number;
    /**
     * @mbg.generated,用户id
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * 业务类型
     */
    biz?: string;
    /**
     * 业务值
     */
    value?: string;
    /**
     * 0:删除,1:可用
     */
    enableStatus?: number;
    /**
     * 添加时间
     */
    gmtCreated?: string;
    /**
     * 修改时间
     */
    gmtModified?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * List<RefundGlobalConfigDTO>
 */
export type UpdateRefundGlobalConfigRequest = {
  /**
   * id
   */
  id?: number;
  /**
   * @mbg.generated,用户id
   */
  userId?: number;
  /**
   * 子账号用户id
   */
  subUserId?: number;
  /**
   * 业务类型
   */
  biz?: string;
  /**
   * 业务值
   */
  value?: string;
  /**
   * 0:删除,1:可用
   */
  enableStatus?: number;
  /**
   * 添加时间
   */
  gmtCreated?: string;
  /**
   * 修改时间
   */
  gmtModified?: string;
  [k: string]: any;
}[];

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface UpdateRefundGlobalConfigResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ResponseBody<List<AutoMemoConfigDTO>> :ResponseBody
 */
export interface SelectResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * AutoMemoConfigDTO
   */
  data?: {
    /**
     * 表字段:id
     */
    id?: number;
    /**
     * 用户id,表字段:user_id
     */
    userId?: number;
    /**
     * 场景,AFTER_CONFIRM_AUTO_MEMO("确认收货后自动备注"),,AFTER_CONFIRM_AUTO_CHANGE_FLAG("确认收货后自动改旗帜"),,AFTER_GENERATE_CHANGE_HAND_ORDER_AUTO_CHANGE_FLAG("生成换货补发手工单后自动备注"),,AFTER_CHANGE_HAND_ORDER_SEND_AUTO_CHANGE_FLAG("换货补发手工单发货后自动备注")
     */
    scene?: {
      [k: string]: any;
    };
    /**
     * 备注方式:,ADD_BEFORE_ORIGINAL("原备注之前"),,REPLACE("覆盖"),,ADD_AFTER_ORIGINAL("原备注之后")
     */
    memoType?: {
      [k: string]: any;
    };
    /**
     * 备注位置：,RELATION_TRADE_SELLER_MEMO("售后订单关联的销售订单的卖家备注")
     */
    memoPlace?: {
      [k: string]: any;
    };
    /**
     * 备注模版json串,[{"autoMemoFillInfo":"REFUND_INFO","template:"[年],[月],[日]"},{"autoMemoFillInfo":"REFUND_ITEM","template:"[标题],[简称]"}],scene为AFTER_CONFIRM_AUTO_CHANGE_FLAG时：[{"afterSaleType":"RETURN_AND_REFUND","sellerMemoFlag:-1},{"afterSaleType":"EXCHANGE_ITEM","sellerMemoFlag:1}]
     */
    template?: string;
    /**
     * 是否开启1:是0:否,表字段:enable
     */
    enable?: boolean;
    /**
     * 创建时间,表字段:gmt_created
     */
    gmtCreated?: string;
    [k: string]: any;
  }[];
}

/**
 * ReqFinishRefundParamsDTO :ReqFinishRefundParamsDTO
 */
export interface BatchCloseRefundRequest {
  /**
   * id,售后在我们这边的唯一标志 ,Long
   */
  ids?: number[];
  /**
   * 处理方式1-关闭2-取消关闭
   */
  handleWay?: number;
  [k: string]: any;
}

/**
 * ReqUpdateAutoMemoConfigDTO :ReqUpdateAutoMemoConfigDTO
 */
export interface UpdateRequest {
  /**
   * AutoMemoConfigDTO
   */
  autoMemoConfigDTOS?: {
    /**
     * 表字段:id
     */
    id?: number;
    /**
     * 用户id,表字段:user_id
     */
    userId?: number;
    /**
     * 场景,AFTER_CONFIRM_AUTO_MEMO("确认收货后自动备注"),,AFTER_CONFIRM_AUTO_CHANGE_FLAG("确认收货后自动改旗帜"),,AFTER_GENERATE_CHANGE_HAND_ORDER_AUTO_MEMO("生成换货补发手工单后自动备注"),,AFTER_CHANGE_HAND_ORDER_SEND_MEMO("换货补发手工单发货后自动备注")
     */
    scene?: {
      [k: string]: any;
    };
    /**
     * 备注方式:,ADD_BEFORE_ORIGINAL("原备注之前"),,REPLACE("覆盖"),,ADD_AFTER_ORIGINAL("原备注之后")
     */
    memoType?: {
      [k: string]: any;
    };
    /**
     * 备注位置：,RELATION_TRADE_SELLER_MEMO("售后订单关联的销售订单的卖家备注")
     */
    memoPlace?: {
      [k: string]: any;
    };
    /**
     * 备注模版json串,[{"autoMemoFillInfo":"REFUND_INFO","template:"[年],[月],[日]"},{"autoMemoFillInfo":"REFUND_ITEM","template:"[标题],[简称]"}],scene为AFTER_CONFIRM_AUTO_CHANGE_FLAG时：[{"afterSaleType":"RETURN_AND_REFUND","sellerMemoFlag:-1},{"afterSaleType":"EXCHANGE_ITEM","sellerMemoFlag:1}]
     */
    template?: string;
    /**
     * 是否开启1:是0:否,表字段:enable
     */
    enable?: boolean;
    /**
     * 创建时间,表字段:gmt_created
     */
    gmtCreated?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface UpdateResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ResponseBody<BatchCloseRefundResDto> :ResponseBody
 */
export interface BatchCloseRefundResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BatchCloseRefundResDto
   */
  data?: {
    /**
     * 成功数
     */
    successNum?: number;
    /**
     * 失败数
     */
    failedNum?: number;
    /**
     * 失败结果集合 ,BatchResult
     */
    batchResults?: {
      operationId?: string;
      success?: boolean;
      errorCode?: number;
      errorMessage?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqFinishRefundParamsDTO :ReqFinishRefundParamsDTO
 */
export interface BatchUpdateRefundSystemStatusRequest {
  /**
   * id,售后在我们这边的唯一标志 ,Long
   */
  ids?: number[];
  /**
   * 处理方式1-关闭2-取消关闭3-待处理4-处理中5-已完成
   */
  handleWay?: number;
  [k: string]: any;
}

/**
 * ResponseBody<BatchCloseRefundResDto> :ResponseBody
 */
export interface BatchUpdateRefundSystemStatusResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BatchCloseRefundResDto
   */
  data?: {
    /**
     * 成功数
     */
    successNum?: number;
    /**
     * 失败数
     */
    failedNum?: number;
    /**
     * 失败结果集合 ,BatchResult
     */
    batchResults?: {
      operationId?: string;
      success?: boolean;
      errorCode?: number;
      errorMessage?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}
/*
 * CommonNoteRequest :CommonNoteRequest
 */
export interface CommonNoteEditCommonNoteRequest {
	/**
	 * id（新增的时候不传，编辑的时候传）
	 */
	id?: number;
	/**
	 * 内容
	 */
	content?: string;
	/**
	 * 时候删除，删除的时候传true，传的时候必须要传id
	 */
	isDelete?: boolean;
	[k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface CommonNoteEditCommonNoteResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
}

/**
 * ResponseBody<List<CommonNoteDTO>> :ResponseBody
 */
export interface CommonNoteSelectByUserResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * CommonNoteDTO
	 */
	data?: {
	  /**
	   * @mbg.generated,表字段:id
	   */
	  id?: number;
	  /**
	   * @mbg.generated,用户id,表字段:user_id
	   */
	  userId?: number;
	  /**
	   * @mbg.generated,内容,表字段:content
	   */
	  content?: string;
	  /**
	   * @mbg.generated,删除标识0已删除1未删除,表字段:enable_status
	   */
	  enableStatus?: number;
	  /**
	   * @mbg.generated,创建时间,表字段:gmt_created
	   */
	  gmtCreated?: string;
	  /**
	   * @mbg.generated,修改时间,表字段:gmt_modified
	   */
	  gmtModified?: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }

/**
 * List<LocalNoteRequest>
 */
export type BatchUpdateLocalNoteRequest = {
	/**
	 * 售后单号
	 */
	refundId?: number;
	/**
	 * 扫描登记id
	 */
	scanRegistId?: number;
	/**
	 * 运单号
	 */
	sid?: string;
	/**
	 * 内容
	 */
	localContent?: string;
	[k: string]: any;
  }[];

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface BatchUpdateLocalNoteResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }
  

/**
 * CreateExchangeTradeReqDto :CreateExchangeTradeReqDto
 */
export interface BatchCreateExchangeTradeRequest {
	/**
	 * 售后信息id
	 */
	refundInfoId?: number;
	/**
	 * 是否修改收件人信息
	 */
	isUpdate?: boolean;
	/**
	 * 收件人姓名
	 */
	exchangeReceiverName?: string;
	/**
	 * 收件人手机号
	 */
	exchangeReceiverMobile?: string;
	/**
	 * 换货收件人省
	 */
	exchangeReceiverProvince?: string;
	/**
	 * 换货收件人市
	 */
	exchangeReceiverCity?: string;
	/**
	 * 换货收件人区
	 */
	exchangeReceiverTown?: string;
	/**
	 * 收件人详细地址
	 */
	exchangeReceiverAddress?: string;
	/**
	 * refundInfoId集合 ,Long
	 */
	refundInfoIdList?: number[];
	/**
	 * userId
	 */
	userId?: number;
	[k: string]: any;
  }
  
/**
 * ResponseBody<RspBatchCreateExchangeTradeProcessDTO> :ResponseBody
 */
export interface BatchCreateExchangeTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspBatchCreateExchangeTradeProcessDTO
	 */
	data?: {
	  /**
	   * 异步查询Key
	   */
	  asyncKey?: string;
	  /**
	   * 进度
	   */
	  process?: string;
	  /**
	   * 执行状态0-进行中1-成功2-失败
	   */
	  processStatus?: number;
	  /**
	   * 错误信息
	   */
	  errorMessage?: string;
	  /**
	   * 更新时间
	   */
	  changeTime?: string;
	  /**
	   * 成功数
	   */
	  successNum?: number;
	  /**
	   * 失败数
	   */
	  failedNum?: number;
	  /**
	   * 失败结果集合 ,ErrorResultCollectDto
	   */
	  errorResultCollectList?: {
		/**
		 * 操作id
		 */
		operationId?: string;
		/**
		 * 这个操作id对应的操作是否成功
		 */
		success?: boolean;
		/**
		 * 这个操作id对应的操作错误编码
		 */
		errorCode?: number;
		/**
		 * 这个操作id对应的操作错误信息
		 */
		errorMessage?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }
  
/**
 * (该参数为map)
 */
export interface CreateExchangeTradeAsyncGetRequest {
	/**
	 * String
	 */
	mapKey?: {
	  [k: string]: any;
	};
	/**
	 * String
	 */
	mapValue?: {
	  hash?: number;
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * ResponseBody<RspBatchCreateExchangeTradeProcessDTO> :ResponseBody
 */
export interface CreateExchangeTradeAsyncGetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspBatchCreateExchangeTradeProcessDTO
	 */
	data?: {
	  /**
	   * 异步查询Key
	   */
	  asyncKey?: string;
	  /**
	   * 进度
	   */
	  process?: string;
	  /**
	   * 执行状态0-进行中1-成功2-失败
	   */
	  processStatus?: number;
	  /**
	   * 错误信息
	   */
	  errorMessage?: string;
	  /**
	   * 更新时间
	   */
	  changeTime?: string;
	  /**
	   * 成功数
	   */
	  successNum?: number;
	  /**
	   * 失败数
	   */
	  failedNum?: number;
	  /**
	   * 失败结果集合 ,ErrorResultCollectDto
	   */
	  errorResultCollectList?: {
		/**
		 * 操作id
		 */
		operationId?: string;
		/**
		 * 这个操作id对应的操作是否成功
		 */
		success?: boolean;
		/**
		 * 这个操作id对应的操作错误编码
		 */
		errorCode?: number;
		/**
		 * 这个操作id对应的操作错误信息
		 */
		errorMessage?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }
  
/**
 * ScmPushRefundToSupplierReqDto :ScmPushRefundToSupplierReqDto
 */
export interface ScmRefundBatchPushSupplierRequest {
	/**
	 * refundInfoId集合 ,Long
	 */
	refundInfoIdList?: number[];
	/**
	 * userId
	 */
	userId?: number;
	[k: string]: any;
  }
  /**
 * ResponseBody<ScmRspBatchAsyncOperateProcessDTO> :ResponseBody
 */
export interface ScmRefundBatchPushSupplierResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ScmRspBatchAsyncOperateProcessDTO
	 */
	data?: {
	  /**
	   * 异步查询Key
	   */
	  asyncKey?: string;
	  /**
	   * 进度
	   */
	  process?: string;
	  /**
	   * 执行状态0-进行中1-成功2-失败
	   */
	  processStatus?: number;
	  /**
	   * 错误信息
	   */
	  errorMessage?: string;
	  /**
	   * 更新时间
	   */
	  changeTime?: string;
	  /**
	   * 成功数
	   */
	  successNum?: number;
	  /**
	   * 失败数
	   */
	  failedNum?: number;
	  /**
	   * 失败结果集合 ,ErrorResultCollectDto
	   */
	  errorResultCollectList?: {
		/**
		 * 操作id
		 */
		operationId?: string;
		/**
		 * 这个操作id对应的操作是否成功
		 */
		success?: boolean;
		/**
		 * 这个操作id对应的操作错误编码
		 */
		errorCode?: number;
		/**
		 * 这个操作id对应的操作错误信息
		 */
		errorMessage?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }
  /**
 * (该参数为map)
 */
export interface ScmRefundBatchPushSupplierAsyncGetRequest {
	/**
	 * String
	 */
	mapKey?: {
	  [k: string]: any;
	};
	/**
	 * String
	 */
	mapValue?: {
	  hash?: number;
	  [k: string]: any;
	};
	[k: string]: any;
  }
  /**
 * ResponseBody<ScmRspBatchAsyncOperateProcessDTO> :ResponseBody
 */
export interface ScmRefundBatchPushSupplierAsyncGetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ScmRspBatchAsyncOperateProcessDTO
	 */
	data?: {
	  /**
	   * 异步查询Key
	   */
	  asyncKey?: string;
	  /**
	   * 进度
	   */
	  process?: string;
	  /**
	   * 执行状态0-进行中1-成功2-失败
	   */
	  processStatus?: number;
	  /**
	   * 错误信息
	   */
	  errorMessage?: string;
	  /**
	   * 更新时间
	   */
	  changeTime?: string;
	  /**
	   * 成功数
	   */
	  successNum?: number;
	  /**
	   * 失败数
	   */
	  failedNum?: number;
	  /**
	   * 失败结果集合 ,ErrorResultCollectDto
	   */
	  errorResultCollectList?: {
		/**
		 * 操作id
		 */
		operationId?: string;
		/**
		 * 这个操作id对应的操作是否成功
		 */
		success?: boolean;
		/**
		 * 这个操作id对应的操作错误编码
		 */
		errorCode?: number;
		/**
		 * 这个操作id对应的操作错误信息
		 */
		errorMessage?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }
  /**
 * ScmRecallPushRefundReqDto :ScmRecallPushRefundReqDto
 */
export interface ScmRefundBatchRecallRequest {
	/**
	 * refundInfoId集合 ,Long
	 */
	refundInfoIdList?: number[];
	/**
	 * userId
	 */
	userId?: number;
	[k: string]: any;
  }
  /**
 * ResponseBody<ScmRspBatchAsyncOperateProcessDTO> :ResponseBody
 */
export interface ScmRefundBatchRecallResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ScmRspBatchAsyncOperateProcessDTO
	 */
	data?: {
	  /**
	   * 异步查询Key
	   */
	  asyncKey?: string;
	  /**
	   * 进度
	   */
	  process?: string;
	  /**
	   * 执行状态0-进行中1-成功2-失败
	   */
	  processStatus?: number;
	  /**
	   * 错误信息
	   */
	  errorMessage?: string;
	  /**
	   * 更新时间
	   */
	  changeTime?: string;
	  /**
	   * 成功数
	   */
	  successNum?: number;
	  /**
	   * 失败数
	   */
	  failedNum?: number;
	  /**
	   * 失败结果集合 ,ErrorResultCollectDto
	   */
	  errorResultCollectList?: {
		/**
		 * 操作id
		 */
		operationId?: string;
		/**
		 * 这个操作id对应的操作是否成功
		 */
		success?: boolean;
		/**
		 * 这个操作id对应的操作错误编码
		 */
		errorCode?: number;
		/**
		 * 这个操作id对应的操作错误信息
		 */
		errorMessage?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }
  /**
 * (该参数为map)
 */
export interface ScmRefundBatchRecallAsyncGetRequest {
	/**
	 * String
	 */
	mapKey?: {
	  [k: string]: any;
	};
	/**
	 * String
	 */
	mapValue?: {
	  hash?: number;
	  [k: string]: any;
	};
	[k: string]: any;
  }
  /**
 * ResponseBody<ScmRspBatchAsyncOperateProcessDTO> :ResponseBody
 */
export interface ScmRefundBatchRecallAsyncGetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ScmRspBatchAsyncOperateProcessDTO
	 */
	data?: {
	  /**
	   * 异步查询Key
	   */
	  asyncKey?: string;
	  /**
	   * 进度
	   */
	  process?: string;
	  /**
	   * 执行状态0-进行中1-成功2-失败
	   */
	  processStatus?: number;
	  /**
	   * 错误信息
	   */
	  errorMessage?: string;
	  /**
	   * 更新时间
	   */
	  changeTime?: string;
	  /**
	   * 成功数
	   */
	  successNum?: number;
	  /**
	   * 失败数
	   */
	  failedNum?: number;
	  /**
	   * 失败结果集合 ,ErrorResultCollectDto
	   */
	  errorResultCollectList?: {
		/**
		 * 操作id
		 */
		operationId?: string;
		/**
		 * 这个操作id对应的操作是否成功
		 */
		success?: boolean;
		/**
		 * 这个操作id对应的操作错误编码
		 */
		errorCode?: number;
		/**
		 * 这个操作id对应的操作错误信息
		 */
		errorMessage?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }
  /**
 * ScmReqSupplierConfirmParamsDTO :ScmReqSupplierConfirmParamsDTO
 */
export interface ScmRefundBatchConfirmOrCancelRequest {
	/**
	 * refundInfoId集合 ,Long
	 */
	refundInfoIdList?: number[];
	/**
	 * 处理方式1-确认2-取消确认
	 */
	handleWay?: number;
	/**
	 * userId
	 */
	userId?: number;
	[k: string]: any;
  }
  /**
 * ResponseBody<ScmRspBatchSyncOperateDto> :ResponseBody
 */
export interface ScmRefundBatchConfirmOrCancelResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ScmRspBatchSyncOperateDto
	 */
	data?: {
	  /**
	   * 成功数
	   */
	  successNum?: number;
	  /**
	   * 失败数
	   */
	  failedNum?: number;
	  /**
	   * 失败结果集合 ,BatchResult
	   */
	  batchResults?: {
		operationId?: string;
		success?: boolean;
		errorCode?: number;
		errorMessage?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * AgreeRefundCheckAddressReqDto :AgreeRefundCheckAddressReqDto
 */
export interface QueryRefundAddressNewRequest {
	/**
	 * 平台类型
	 */
	platform?: string;
	/**
	 * 店铺id
	 */
	sellerId?: string;
	/**
	 * userId
	 */
	userId?: number;
	/**
	 * 店铺id集合 ,String
	 */
	sellerIdList?: string[];
	/**
	 * 售后单唯一id集合 ,Long
	 */
	refundInfoIds?: number[];
	[k: string]: any;
  }
/**
 * ResponseBody<BatchQueryRefundAddressResDto> :ResponseBody
 */
export interface QueryRefundAddressNewResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchQueryRefundAddressResDto
	 */
	data?: {
	  /**
	   * 售后地址集合 ,RefundAddressDTO
	   */
	  refundAddressDTOList?: {
		/**
		 * @mbg.generated,表字段:id
		 */
		id?: number;
		/**
		 * @mbg.generated,用户ID,表字段:user_id
		 */
		userId?: number;
		/**
		 * @mbg.generated,平台类型,表字段:platform
		 */
		platform?: string;
		/**
		 * @mbg.generated,店铺ID,表字段:seller_id
		 */
		sellerId?: string;
		/**
		 * @mbg.generated,店铺名称,表字段:seller_nick
		 */
		sellerNick?: string;
		/**
		 * @mbg.generated,逻辑删除,表字段:enable_status
		 */
		enableStatus?: boolean;
		/**
		 * @mbg.generated,创建时间,表字段:gmt_created
		 */
		gmtCreated?: string;
		/**
		 * @mbg.generated,更新时间,表字段:gmt_modified
		 */
		gmtModified?: string;
		/**
		 * @mbg.generated,地址库ID,表字段:refund_address_id
		 */
		refundAddressId?: string;
		/**
		 * @mbg.generated,省份,表字段:refund_province
		 */
		refundProvince?: string;
		/**
		 * @mbg.generated,城市,表字段:refund_city
		 */
		refundCity?: string;
		/**
		 * @mbg.generated,区县,表字段:refund_district
		 */
		refundDistrict?: string;
		/**
		 * @mbg.generated,街道,表字段:refund_street
		 */
		refundStreet?: string;
		/**
		 * @mbg.generated,详细地址,表字段:refund_detail
		 */
		refundDetail?: string;
		/**
		 * @mbg.generated,退货收件人名字,表字段:refund_name
		 */
		refundName?: string;
		/**
		 * @mbg.generated,退货收件人手机号,表字段:refund_phone
		 */
		refundPhone?: string;
		/**
		 * @mbg.generated,退货收件人固话,表字段:refund_tel
		 */
		refundTel?: string;
		/**
		 * @mbg.generated,地区邮政编码,表字段:zip_code
		 */
		zipCode?: string;
		/**
		 * @mbg.generated,公司名称,表字段:seller_company
		 */
		sellerCompany?: string;
		/**
		 * @mbg.generated,备注,表字段:memo
		 */
		memo?: string;
		/**
		 * @mbg.generated,发货默认0:否1:是,表字段:send_default
		 */
		sendDefault?: boolean;
		/**
		 * @mbg.generated,退货默认。0:否1:是,表字段:refund_default
		 */
		refundDefault?: boolean;
		/**
		 * @mbg.generated,平台更新时间,表字段:platform_modified
		 */
		platformModified?: string;
		[k: string]: any;
	  }[];
	  /**
	   * 售后单关联售后地址 ,RefundRelateAddr
	   */
	  refundRelateAddrList?: {
		/**
		 * 售后单唯一id
		 */
		refundInfoId?: number;
		/**
		 * 地址id
		 */
		addressId?: number;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }
/**
 * ReqRefundReceiveRegisterDto :ReqRefundReceiveRegisterDto
 */
export interface GetRegisterParamListRequest {
	/**
	 * id列表 ,Long
	 */
	idList?: number[];
	/**
	 * 售后列表查询来源,0-正常售后单列表1-分销商的售后单列表
	 */
	scmSelectRefundSource?: number;
	[k: string]: any;
  }
/**
 * ResponseBody<RspRefundReceiveRegisterDto> :ResponseBody
 */
export interface GetRegisterParamListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspRefundReceiveRegisterDto
	 */
	data?: {
	  /**
	   * 产品包装状况 ,SelectContentDTO
	   */
	  packingStateList?: {
		/**
		 * code
		 */
		code?: number;
		/**
		 * desc
		 */
		desc?: string;
		[k: string]: any;
	  }[];
	  /**
	   * 主商品功能状况 ,SelectContentDTO
	   */
	  qualityStateList?: {
		/**
		 * code
		 */
		code?: number;
		/**
		 * desc
		 */
		desc?: string;
		[k: string]: any;
	  }[];
	  /**
	   * 主商品外观详情 ,SelectContentDTO
	   */
	  appearanceStateList?: {
		/**
		 * code
		 */
		code?: number;
		/**
		 * desc
		 */
		desc?: string;
		[k: string]: any;
	  }[];
	  /**
	   * 附件赠品（赠品是否完整） ,SelectContentDTO
	   */
	  accessoryOrGiftList?: {
		/**
		 * code
		 */
		code?: number;
		/**
		 * desc
		 */
		desc?: string;
		[k: string]: any;
	  }[];
	  /**
	   * 发票登记状况 ,SelectContentDTO
	   */
	  invoiceRecordList?: {
		/**
		 * code
		 */
		code?: number;
		/**
		 * desc
		 */
		desc?: string;
		[k: string]: any;
	  }[];
	  /**
	   * 收货登记原因 ,SelectContentDTO
	   */
	  judgmentReasonList?: {
		/**
		 * code
		 */
		code?: number;
		/**
		 * desc
		 */
		desc?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * BatchRefundRegisterReqDto :BatchRefundRegisterReqDto
 */
export interface BatchReceiveRegisterRequest {
	/**
	 * ids ,Long
	 */
	ids?: number[];
	/**
	 * 批量操作类型
	 */
	operateType?: {
	  [k: string]: any;
	};
	/**
	 * 售后列表查询来源,0-正常售后单列表1-分销商的售后单列表
	 */
	scmSelectRefundSource?: number;
	/**
	 * 商品包装状况
	 */
	packingState?: number;
	/**
	 * 主商品功能状况
	 */
	qualityState?: number;
	/**
	 * 发票登记状况
	 */
	invoiceRecord?: number;
	/**
	 * 收货登记原因
	 */
	judgmentReason?: number;
	/**
	 * 附件赠品
	 */
	accessoryOrGift?: number;
	/**
	 * 主商品外观
	 */
	appearanceState?: number;
	/**
	 * 收货备注
	 */
	remark?: string;
	[k: string]: any;
  }
  
/**
 * ResponseBody<BatchRefundRegisterResDto> :ResponseBody
 */
export interface BatchReceiveRegisterResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchRefundRegisterResDto
	 */
	data?: {
	  /**
	   * 成功数
	   */
	  successNum?: number;
	  /**
	   * 失败数
	   */
	  failedNum?: number;
	  /**
	   * 失败结果集合 ,BatchResult
	   */
	  batchResults?: {
		operationId?: string;
		success?: boolean;
		errorCode?: number;
		errorMessage?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * ReqSelectOperateLogListWithPageDTO :ReqSelectOperateLogListWithPageDTO
 */
export interface SelectOperateLogPageRequest {
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 创建时间开始
	 */
	createTimeStart?: string;
	/**
	 * 创建时间结束
	 */
	createTimeEnd?: string;
	/**
	 * 多平台店铺查询 ,MultiShopDTO
	 */
	multiShopS?: {
	  sellerId?: number;
	  platform?: string;
	  [k: string]: any;
	}[];
	/**
	 * 多平台店铺查询，改成sellerId集合 ,String
	 */
	sellerIdList?: string[];
	/**
	 * 操作类型,@seecom.kuaidizs.erp.aftersale.common.enums.OperateTypeEnum
	 */
	opTypeCode?: string;
	/**
	 * String
	 */
	opTypeCodeList?: string[];
	/**
	 * 操作结果0-失败1-成功
	 */
	opResult?: number;
	/**
	 * String
	 */
	refundIdList?: string[];
	/**
	 * 退货单号 ,String
	 */
	sidList?: string[];
	/**
	 * 订单单号
	 */
	ptTid?: string;
	/**
	 * 平台订单编号 ,String
	 */
	ptTidList?: string[];
	/**
	 * 系统编号
	 */
	tid?: string;
	/**
	 * String
	 */
	tidList?: string[];
	/**
	 * 操作内容
	 */
	operateDetail?: string;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	[k: string]: any;
  }

/**
 * ResponseBody<PageList<RspSelectOperateLogListWithPageDTO>> :ResponseBody
 */
export interface SelectOperateLogPageResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
	  pageNo?: number;
	  pageSize?: number;
	  total?: number;
	  /**
	   * T
	   */
	  list?: {
		/**
		 * 创建时间
		 */
		gmtCreated?: string;
		/**
		 * 店铺名称
		 */
		sellerNick?: string;
		/**
		 * 店铺ID
		 */
		sellerId?: string;
		/**
		 * tid
		 */
		tid?: string;
		/**
		 * 平台订单编号
		 */
		ptTid?: string;
		/**
		 * 退款编号
		 */
		refundId?: string;
		/**
		 * 平台TB、TM（天猫待定）、PDD、FXG（抖店）、HAND（手工单）
		 */
		platform?: string;
		/**
		 * 售后类型,空全部,1仅退款,2退货退款,3换货,4补差价,5补发货品
		 */
		afterSaleType?: number;
		/**
		 * 退款金额
		 */
		refundAmount?: string;
		/**
		 * 退货运单号
		 */
		sid?: string;
		/**
		 * 操作类型
		 */
		opType?: string;
		/**
		 * 操作类型
		 */
		opName?: string;
		/**
		 * 操作内容
		 */
		detail?: string;
		/**
		 * 操作结果0-失败1-成功
		 */
		opResult?: number;
		/**
		 * 操作者用户id
		 */
		opUserId?: number;
		/**
		 * 操作者用户nick
		 */
		opNick?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * ReqSelectOperateLogListWithPageDTO :ReqSelectOperateLogListWithPageDTO
 */
export interface SelectOperateLogSummaryRequest {
	/**
	 * 用户id
	 */
	userId?: number;
	/**
	 * 创建时间开始
	 */
	createTimeStart?: string;
	/**
	 * 创建时间结束
	 */
	createTimeEnd?: string;
	/**
	 * 多平台店铺查询 ,MultiShopDTO
	 */
	multiShopS?: {
	  sellerId?: number;
	  platform?: string;
	  [k: string]: any;
	}[];
	/**
	 * 多平台店铺查询，改成sellerId集合 ,String
	 */
	sellerIdList?: string[];
	/**
	 * 操作类型,@seecom.kuaidizs.erp.aftersale.common.enums.OperateTypeEnum
	 */
	opTypeCode?: string;
	/**
	 * String
	 */
	opTypeCodeList?: string[];
	/**
	 * 操作结果0-失败1-成功
	 */
	opResult?: number;
	/**
	 * String
	 */
	refundIdList?: string[];
	/**
	 * 退货单号 ,String
	 */
	sidList?: string[];
	/**
	 * 订单单号
	 */
	ptTid?: string;
	/**
	 * 平台订单编号 ,String
	 */
	ptTidList?: string[];
	/**
	 * 系统编号
	 */
	tid?: string;
	/**
	 * String
	 */
	tidList?: string[];
	/**
	 * 操作内容
	 */
	operateDetail?: string;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	[k: string]: any;
  }

/**
 * ResponseBody<RspSelectOperateLogSummaryDTO> :ResponseBody
 */
export interface SelectOperateLogSummaryResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspSelectOperateLogSummaryDTO
	 */
	data?: {
	  /**
	   * 确认收货未触发退款数(今日)
	   */
	  confirmAndUnRefundNum?: number;
	  /**
	   * 退款失败(今日)
	   */
	  refundFailedNum?: number;
	  /**
	   * 生成换货补发手工单失败数(今日)
	   */
	  createExchangeTradeFailedNum?: number;
	  /**
	   * 退货入库失败数(今日)
	   */
	  returnStockFailedNum?: number;
	  /**
	   * 回填换货补发单号失败数(今日)
	   */
	  refundExchangeShipFailedNum?: number;
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * BatchRefuseRefundReqDto :BatchRefuseRefundReqDto
 */
export interface BatchRefuseRefundRequest {
	/**
	 * 操作类型
	 */
	operateType?: any;
	/**
	 * 售后列表查询来源,0-正常售后单列表1-分销商的售后单列表
	 */
	scmSelectRefundSource?: number;
	/**
	 * 拒绝售后单集合 ,RefuseRefundInfo
	 */
	refuseRefundInfoList?: {
	  /**
	   * id
	   */
	  id?: number;
	  /**
	   * 拒绝凭证 ,String
	   */
	  proofFileList?: string[];
	  /**
	   * 拒绝说明
	   */
	  refuseMessage?: string;
	  /**
	   * 淘宝-原因编号抖音-拒绝原因编码快手-拒绝原因code小红书-拒绝原因id
	   */
	  reasonId?: number;
	  /**
	   * 淘宝-原因文本抖音必传-拒绝原因
	   */
	  reasonText?: string;
	  /**
	   * 抖音-是否需要上传凭证，Y必填，N非必填快手-图片是否必填
	   */
	  evidenceNeed?: string;
	  /**
	   * 文件凭证
	   */
	  mediaId?: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }

/**
 * ResponseBody<BatchRefuseRefundResDto> :ResponseBody
 */
export interface BatchRefuseRefundResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchRefuseRefundResDto
	 */
	data?: {
	  /**
	   * 成功数
	   */
	  successNum?: number;
	  /**
	   * 失败数
	   */
	  failedNum?: number;
	  /**
	   * 失败结果集合 ,BatchResult
	   */
	  batchResults?: {
		operationId?: string;
		success?: boolean;
		errorCode?: number;
		errorMessage?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }
  
  