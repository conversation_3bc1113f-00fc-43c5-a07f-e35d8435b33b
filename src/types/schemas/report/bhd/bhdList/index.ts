import { QueryConditionEnum } from "@/pages/Report/Bhd/BhdList/components/SearchContainer";
import { TradeSearchConditionConfig } from "@/types/trade/search/search";

export interface ReportQueryBhdItemListRequest {
    /**
     * 平台多选 ,MultiShopDTO
     */
    multiShopS?: {
      sellerId?: string;
      platform?: string;
      [k: string]: any;
    }[];
    /**
     * 搜索开始时间
     */
    startTime?: string;
    /**
     * 搜索结束时间
     */
    endTime?: string;
    /**
     * 筛选时间类型1下单时间3打印时间4发货时间
     */
    timeType?: number;
    /**
     * 订单状态ORDER_WAIT_PAY等待付款ORDER_PAID已付款ORDER_SHIPPED已发货,ORDER_SHIPPED_LITTLE部分发货ORDER_COMPLETED交易完成ALL_STATUS全部订单 ,String
     */
    status?: string[];
    /**
     * 1有买家留言2有卖家备注3有买家留言+有卖家备注4没有留言且没有备注5红6黄7绿8蓝色9紫色10有留言或备注11无留言12无备注
     */
    flagValue?: string;
    /**
     * 1,2_1表示包含红黄旗从自定义备注接口找0表示不包含1表示包含
     */
    flagSelValue?: string;
    /**
     * {@codetradeQuery.getSellAttribute},查询销售属性,1:单商品单规格单件,,2:单商品单规格多件,,3:单商品多规格多件,,4:多商品多规格多件
     */
    sellAttribute?: string;
    /**
     * 商品名称编码简称id
     */
    shortNameIncluding?: string;
    /**
     * 规格名称规格别名查询
     */
    skuIncluding?: string;
    countCost?: boolean; // 统计成本价
    countSalableItemStock?: boolean; // 货品库存
    [k: string]: any;
}


export interface BhdTempType{
    baseSet?: BhdBaseSetType;
    colsSet?: BhdColSet[];
    hasInit?: boolean;
    [k: string]: any;
}

export interface BhdColSet{
    "align"?: 'start' | 'end' | 'left' | 'right' | 'center';
    "font"?: string;
    "bold"?: string | 'bold' | 'normal';
    "colName"?: string;
    "color"?: string;
    "datas"?: string;
    "id"?: number;
    "orderId"?: number;
    "size"?: number | string;
    "width"?: number;
    "dataArray"?: string[];
    "showSingleLine"? : boolean;
    "hasNoStar"?: boolean;
    "hasStar"?: boolean;
    "content"?: string;
    [k:string]: any;
}

export interface BhdBaseSetType {
    "firstSortField"?: string;
    "fontFamily"?: string;
    "fontSize"?:number;
    "height"?: number;
    "imageSize"?: string;
    "lineHeight"?: number,
    "orient"?: string;
    "outerIdMerge"?: string;
    "pageType"?: string;
    "shortTitleMerge"?: string;
    "skuAliasMerge"?:string;
    "skuIdMerge"?: string;
    "skuOuteridMerge"?: string;
    "skuPropsMerge"?: string;
    "styleType"?: number;
    "tableColor"?: string;
    [k:string]: any;
}

export interface BhdSkuItem {
    // 规格名
    skuName?: string;
    // 过滤后的规格名称
    _skuName?: string;
    // 规格别名
    skuAlias?: string;
    // 规格Id
    skuId?: string;
    // 规格商家编码
    skuOuterId?: string;
    // sku规格图片
    skuPic?:string;
    // 数量
    count?: number;
    // 成本价
    cost?: number;
    // 商品库存
    salableItemStock?: number;
    salableItemDistributableStock?: number;
    salableItemPreemptedNum?: number;
    sysSkuOuterId?: string;
    sysSkuName?: string;
    // 货位
    warehouseSlotName?: string;
    [k: string]: any;
}

export interface BhdTradeInfo{
    // 买家昵称
    buyerNick?: string;
    // 订单编号
    tid?: string;
    // 买家留言
    buyerMessage?: string;
    // 卖家备注
    sellerMemo?: string;
    // 旗帜
    sellerFlag?: string;
}

export interface BhdOrderItem{
    isChecked: boolean;
    // 商品id
    numIid?: string;
    // 平台
    platform?: string;
    // 商品名称
    title?: string;
    // 商品简称
    titleAlias?: string;
    // 商家编码
    outerId?: string;
    // 商品主图
    picUrl?: string;
    // 总数量
    totalCount?: number;
    // 规格统计 ,SkuCount
    skuCounts?: BhdSkuItem[];
    // 店铺名称
    sellerNick?: string;
    // 订单信息 ,TradeInfo
    tradeInfos?: BhdTradeInfo[];
    // 货位
    warehouseSlotName?: string;
    _tdContents?:string[];
    _hasMessage?:boolean;
    _showMsg?:boolean;
    _cost?:number;
    _orderIndex?: number;
    sysSkuName?: string;
    sysSkuOuterId?: string;
    skuPic?: string;
    skuId?: string;
    editContent?: Record<string, string>,
    // [k: string]: any;
    [k: string]: any;
}

export interface ReportQueryBhdItemList {
    tradeCountMap?: {
        /**
         * String
         */
        mapKey?: {
          [k: string]: any;
        };
        /**
         * Map<String, Integer>
         */
        mapValue?: {
          [k: string]: any;
        };
        [k: string]: any;
    };
    // 商品总数量
    totalCount?: number;
    // 总成本
    totalCost?: number;
    // 商品 ,Item
    items?: BhdOrderItem[];
    supplierGroupItems?: {
      mallStallSupplierStr?: string;
      items?: BhdOrderItem[];
    }
    [k: string]: any;
}

export interface SaveGetBhdSetRequestApi{
    bhdJson:string;
    bhdColJsons:string;
}

/**
 * ResponseBody<BhdGenerateResultDTO> :ResponseBody
 */
export interface TradeGetBhdGenerateResultResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * BhdGenerateResultDTO
     */
    data?: {
      /**
       * 进度条
       */
      progress?: string;
      /**
       * 错误详情
       */
      errorMsg?: string;
      /**
       * 是否成功
       */
      success?: boolean;
      /**
       * 结果 ,result
       */
      result?: {
        /**
         * 订单数统计集合key:platformvalue:count(该参数为map)
         */
        tradeCountMap?: {
          /**
           * String
           */
          mapKey?: {
            [k: string]: any;
          };
          /**
           * Map<String, Integer>
           */
          mapValue?: {
            [k: string]: any;
          };
          [k: string]: any;
        };
        /**
         * 商品总数量
         */
        totalCount?: number;
        /**
         * 总成本
         */
        totalCost?: number;
        /**
         * 总实付金额
         */
        totalPaymentCount?: number;
        /**
         * 商品 ,Item
         */
        items?: {
          /**
           * 商品id
           */
          numIid?: string;
          /**
           * 平台
           */
          platform?: string;
          /**
           * 商品名称
           */
          title?: string;
          /**
           * 商品简称
           */
          titleAlias?: string;
          /**
           * 商家编码
           */
          outerId?: string;
          /**
           * 商品主图
           */
          picUrl?: string;
          /**
           * 总数量
           */
          totalCount?: number;
          /**
           * 规格统计 ,SkuCount
           */
          skuCounts?: {
            /**
             * 商品id
             */
            itemId?: string;
            /**
             * 规格id
             */
            skuId?: string;
            /**
             * 规格名
             */
            skuName?: string;
            /**
             * 数量
             */
            count?: number;
            /**
             * 规格成本
             */
            cost?: string;
            /**
             * 平台商品对应的货品正品库存数量
             */
            salableItemStock?: string;
            /**
             * sku别名
             */
            skuAlias?: string;
            /**
             * sku图片
             */
            skuPic?: string;
            /**
             * sku商家编码
             */
            skuOuterId?: string;
            [k: string]: any;
          }[];
          /**
           * 订单信息 ,TradeInfo
           */
          tradeInfos?: {
            /**
             * 买家昵称
             */
            buyerNick?: string;
            /**
             * 订单编号
             */
            tid?: string;
            /**
             * 买家留言
             */
            buyerMessage?: string;
            /**
             * 卖家备注
             */
            sellerMemo?: string;
            /**
             * 旗帜
             */
            sellerFlag?: string;
            [k: string]: any;
          }[];
          sellerNick?: string;
          [k: string]: any;
        }[];

        supplierGroupItems?: {
          checkStatus?: number;
          items?: {
            checkStatus?: number;
            /**
             * 商品id
             */
            numIid?: string;
            /**
             * 平台
             */
            platform?: string;
            /**
             * 商品名称
             */
            title?: string;
            /**
             * 商品简称
             */
            titleAlias?: string;
            /**
             * 商家编码
             */
            outerId?: string;
            /**
             * 商品主图
             */
            picUrl?: string;
            /**
             * 总数量
             */
            totalCount?: number;
            /**
             * 规格统计 ,SkuCount
             */
            skuCounts?: {
              /**
               * 商品id
               */
              itemId?: string;
              /**
               * 规格id
               */
              skuId?: string;
              /**
               * 规格名
               */
              skuName?: string;
              /**
               * 数量
               */
              count?: number;
              /**
               * 规格成本
               */
              cost?: string;
              /**
               * 平台商品对应的货品正品库存数量
               */
              salableItemStock?: string;
              /**
               * sku别名
               */
              skuAlias?: string;
              /**
               * sku图片
               */
              skuPic?: string;
              /**
               * sku商家编码
               */
              skuOuterId?: string;
              [k: string]: any;
            }[];
            /**
             * 订单信息 ,TradeInfo
             */
            tradeInfos?: {
              /**
               * 买家昵称
               */
              buyerNick?: string;
              /**
               * 订单编号
               */
              tid?: string;
              /**
               * 买家留言
               */
              buyerMessage?: string;
              /**
               * 卖家备注
               */
              sellerMemo?: string;
              /**
               * 旗帜
               */
              sellerFlag?: string;
              [k: string]: any;
            }[];
            sellerNick?: string;
            [k: string]: any;
          }[];
          mallStallSupplierStr?: string;
        }[];
        /**
         * 订单总数
         */
        tradeTotal?: number;
        [k: string]: any;
      };
      [k: string]: any;
    };
    [k: string]: any;
  }

/**
 * ResponseBody<BhdSetVo> :ResponseBody
 */
export interface ItemBhdSetGetBhdAllSetResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BhdSetVo
   */
  data?: {
    /**
     * 备货单商品配置 ,BhdItemSetBase
     */
    bhdSetBase?: {
      /**
       * BhdSetBase
       */
      baseSet?: {
        id?: number;
        userId?: number;
        pageType?: string;
        orient?: string;
        imageSize?: string;
        height?: number;
        lineHeight?: number;
        tableColor?: string;
        fontFamily?: string;
        fontSize?: string | number;
        outerIdMerge?: string;
        shortTitleMerge?: string;
        skuIdMerge?: string;
        skuOuteridMerge?: string;
        skuPropsMerge?: string;
        skuAliasMerge?: string;
        firstSortField?: string;
        created?: string;
        modified?: string;
        enableStatus?: number;
        styleType?: number;
        supplierGroup?: string;
        [k: string]: any;
      };
      /**
       * BhdSetCols
       */
      colsSet?: {
        id?: number;
        colName?: string;
        width?: number;
        align?: string;
        font?: string;
        size?: string | number;
        bold?: string;
        datas?: string;
        color?: string;
        orderId?: number;
        userId?: number;
        created?: string;
        modified?: string;
        enableStatus?: number;
        [k: string]: any;
      }[];
      hasInit?: boolean;
      [k: string]: any;
    };
    /**
     * 备货单货品配置 ,BhdSysSetBase2
     */
    bhdSysSetBase?: {
      /**
       * 备货单表格设置 ,BhdSysJson
       */
      baseSet?: {
        userId?: number;
        /**
         * 纸张规格
         */
        pageType?: string;
        /**
         * 打印方向
         */
        orient?: string;
        /**
         * 图片尺寸
         */
        imageSize?: string;
        /**
         * 行高
         */
        height?: number;
        /**
         * 行距
         */
        lineHeight?: number;
        /**
         * 表格颜色
         */
        tableColor?: string;
        /**
         * 默认字体
         */
        fontFamily?: string;
        /**
         * 默认字号
         */
        fontSize?: string | number;
        /**
         * 货品排序字段(hpjc:货品简称，hpsl:货品数量)
         */
        firstSortField?: string;
        /**
         * 货品规格排序字段(hpggmc:货品规格名称，hpggbm:货品规格编码，hpggAlias:货品规格别名)
         */
        secondSortField?: string;
        /**
         * 是否合并货品简称，0:不合并1:合并
         */
        sysItemAliasMerge?: string;
        /**
         * 是否合并货品规格名称，0:不合并1:合并
         */
        sysSkuNameMerge?: string;
        /**
         * 是否合并货品规格别名，0:不合并1:合并
         */
        sysSkuAliasMerge?: string;
        /**
         * 市场-档口-供应商进行分组0:关闭1:开启
         */
        supplierGroup?: string;
        /**
         * 组合货品拆分统计0:关闭1:开启
         */
        combinationSplit?: string;
        enableStatus?: number;
        [k: string]: any;
      };
      /**
       * 备货单列设置 ,BhdSysColJsons
       */
      colsSet?: {
        id?: number;
        /**
         * 表头名称
         */
        colName?: string;
        /**
         * 列宽度
         */
        width?: number;
        /**
         * 对齐方式
         */
        align?: string;
        /**
         * 字体
         */
        font?: string;
        /**
         * 字号
         */
        size?: string | number;
        /**
         * 表头名称
         */
        bold?: string;
        /**
         * 表格内容
         */
        datas?: string;
        /**
         * 颜色
         */
        color?: string;
        /**
         * 设置列id
         */
        orderId?: number;
        userId?: number;
        created?: string;
        modified?: string;
        enableStatus?: number;
        [k: string]: any;
      }[];
      /**
       * 是否初始化
       */
      hasInit?: boolean;
      [k: string]: any;
    };
    /**
     * 使用中的模板0：货品1；商品
     */
    activeTemplate?: number;
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqSaveBhdSetDTO :ReqSaveBhdSetDTO
 */
export interface ItemBhdSetSaveBhdSysSetRequest {
  /**
   * 备货单货品视角设置 ,BhdSysSetBase
   */
  bhdSysSet?: {
    /**
     * BhdSysJson
     */
    bhdJson?: {
      userId?: number;
      pageType?: string;
      orient?: string;
      imageSize?: string;
      height?: number;
      lineHeight?: number;
      tableColor?: string;
      fontFamily?: string;
      fontSize?: string | number;
      firstSortField?: string;
      secondSortField?: string;
      sysItemAliasMerge?: string;
      sysSkuNameMerge?: string;
      sysSkuAliasMerge?: string;
      supplierGroup?: string;
      combinationSplit?: string;
      enableStatus?: number;
      [k: string]: any;
    };
    /**
     * BhdSysColJsons
     */
    bhdColJsons?: {
      id?: number;
      colName?: string;
      width?: number;
      align?: string;
      font?: string;
      size?: string | number;
      bold?: string;
      datas?: string;
      color?: string;
      orderId?: number;
      userId?: number;
      created?: string;
      modified?: string;
      enableStatus?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemBhdSetSaveBhdSysSetResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqSaveBhdSetDTO :ReqSaveBhdSetDTO
 */
export interface ItemBhdSetSwitchActiveTemplateRequest {
  /**
   * 使用中的模板0：货品1；商品
   */
  activeTemplate?: number;
  [k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemBhdSetSwitchActiveTemplateResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}


/**
 * UserQueryConditionSettingGetRequest :UserQueryConditionSettingGetRequest
 */
export interface IndexGetUserQueryConditionSettingRequest {
  /**
   * BHD_QUERY_CONDITION-备货单查询条件
   * BHD_LABEL_QUERY_CONDITION-备货单标签查询条件
   *
   */
  type: QueryConditionEnum;
  apiName?: string;
  [k: string]: any;
}

/**
 * ResponseBody<List<QueryConditionDTO>> :ResponseBody
 */
export interface IndexGetUserQueryConditionSettingResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * QueryConditionDTO
   */
  data?: TradeSearchConditionConfig[];
  [k: string]: any;
}
/**
 * UserQueryConditionSettingUpdateRequest :UserQueryConditionSettingUpdateRequest
 */
export interface IndexUpdateUserQueryConditionSettingRequest {
  /**
   * BHD_QUERY_CONDITION-备货单查询条件
   * BHD_LABEL_QUERY_CONDITION-备货单标签查询条件
   *
   */
  type: QueryConditionEnum;
  /**
   * QueryConditionDTO
   */
  queryConditionList: TradeSearchConditionConfig[];
  apiName?: string;
  [k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface IndexUpdateUserQueryConditionSettingResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

