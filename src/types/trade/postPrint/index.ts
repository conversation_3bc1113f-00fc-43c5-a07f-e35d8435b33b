
export interface TradeScanPrintConfigVO{
	/**
	 * id
	 */
	id?: number;
	/**
	 * 扫码类型，BAR_CODE:条形码、ITEM_NO:货号、OUTER_ID:商家编码、SKU_OUTER_ID:规格商家编码、SYS_OUTER_ID:货品规格编码、PT_TID:订单编号,,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintScanTypeEnum
	 */
	scanType: string;
	/**
	 * 结果展示，FIRST_PAY_TIME:付款最早订单,,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintMatchResultTypeEnum
	 */
	matchResultType: string;
	/**
	 * 订单销售类型(支持多选)，ITEM_1_SKU_1:单款单件',,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintSellAttributeTypeEnum ,String
	 */
	sellAttributeTypeList: string[];
	/**
	 * 订单状态(支持多选),WAIT_SELLER_SEND_GOODS:待发货、先发货订单：FIRST_SEND,,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintTradeStatusEnum ,String
	 */
	tradeStatusList: string[];
	/**
	 * 退款状态过滤,1:包含退款、0:不包含退款
	 */
	isRefundStatus: boolean;
	/**
	 * 匹配后自动打印,1:开启、0:关闭
	 */
	isAutoPrint: boolean;
	/**
	 * 自动打印拦截(支持多选),PRINTED:已打印订单、SHIPPED:已发货订单、REFUND:退款订单、OUT_OF_STOCK:缺货订单、MSG_REMARKS:有留言备注订单、SELLER_FLAG:订单旗帜,,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintAutoPrintInterceptTypeEnum ,String
	 */
	autoPrintInterceptTypeList: string[];
	/**
	 * 卖家旗帜，多个以逗号拼接,表字段:seller_flag ,String
	 */
	sellerFlagList?: string[];
	msgRemarksTypeList?: string[];
	isPrintItemTag: boolean;
	[k: string]: any;
  }
/**
 * ResponseBody<TradeScanPrintConfigVO> :ResponseBody
 */
export interface TradeScanPrintGetConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeScanPrintConfigVO
	 */
	data?: TradeScanPrintConfigVO;
	[k: string]: any;
  }
/**
 * TradeScanPrintConfigDTO :TradeScanPrintConfigDTO
 */
export interface TradeScanPrintEditConfigRequest {
	/**
	 * id
	 */
	id?: number;
	/**
	 * 扫码类型，BAR_CODE:条形码、ITEM_NO:货号、OUTER_ID:商家编码、SKU_OUTER_ID:规格商家编码、SYS_OUTER_ID:货品规格编码、PT_TID:订单编号,,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintScanTypeEnum
	 */
	scanType: string;
	/**
	 * 结果展示，FIRST_PAY_TIME:付款最早订单,,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintMatchResultTypeEnum
	 */
	matchResultType: string;
	/**
	 * 订单销售类型(支持多选)，ITEM_1_SKU_1:单款单件',,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintSellAttributeTypeEnum ,String
	 */
	sellAttributeTypeList: string[];
	/**
	 * 订单状态(支持多选),WAIT_SELLER_SEND_GOODS:待发货、先发货订单：FIRST_SEND,,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintTradeStatusEnum ,String
	 */
	tradeStatusList: string[];
	/**
	 * 退款状态过滤,1:包含退款、0:不包含退款
	 */
	isRefundStatus: boolean;
	/**
	 * 匹配后自动打印,1:开启、0:关闭
	 */
	isAutoPrint: boolean;
	/**
	 * 自动打印拦截(支持多选),PRINTED:已打印订单、SHIPPED:已发货订单、REFUND:退款订单、OUT_OF_STOCK:缺货订单、MSG_REMARKS:有留言备注订单、SELLER_FLAG:订单旗帜,,@seecom.kuaidizs.erp.common.enums.trade.RdsTradeScanPrintAutoPrintInterceptTypeEnum ,String
	 */
	autoPrintInterceptTypeList: string[];
	/**
	 * 卖家旗帜，多个以逗号拼接,表字段:seller_flag ,String
	 */
	sellerFlagList?: string[];
	[k: string]: any;
  }
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeScanPrintEditConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }
/**
 * TradeScanPrintQueryDTO :TradeScanPrintQueryDTO
 */
export interface TradeScanPrintQueryScanTradeRequest {
	/**
	 * 商品id ,String
	 */
	itemIdList?: string[];
	/**
	 * skuid ,String
	 */
	skuIdList?: string[];
	/**
	 * 系统货品id ,String
	 */
	sysItemIdList?: string[];
	/**
	 * 系统货品规格id ,String
	 */
	sysSkuIdList?: string[];
	/**
	 * 平台订单编号 ,String
	 */
	ptTidList?: string[];
	/**
	 * 系统订单编号 ,String
	 */
	tidList?: string[];
	/**
	 * 店铺id ,Long
	 */
	sellerIdList?: number[];
	/**
	 * 平台类型 ,String
	 */
	platformList?: string[];
	/**
	 * 店铺信息 ,MultiShopDTO
	 */
	multiShopS?: {
	  sellerId?: number;
	  platform?: string;
	  [k: string]: any;
	}[];
	/**
	 * 扫码信息，用于后端日志记录和订单号查询时使用
	 */
	scanContent: string;
	/**
	 * 订单状态
	 */
	status?: string;
	/**
	 * 先发货订单，利用订单标签查询 ,String
	 */
	tradeLabelList?: string[];
	[k: string]: any;
  }
/**
 * ResponseBody<ErpPackageInfoVo> :ResponseBody
 */
export interface TradeScanPrintQueryScanTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ErpPackageInfoVo
	 */
	data?: {
	  buyerNick?: string;
	  /**
	   * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
	   */
	  openId?: string;
	  /**
	   * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
	   */
	  memberId?: string;
	  /**
	   * 买家昵称唯一标识
	   */
	  buyerOpenUid?: string;
	  isCod?: boolean;
	  printInfo?: string;
	  platform?: string;
	  createTime?: string;
	  receiverAddress?: string;
	  receiverCity?: string;
	  receiverDistrict?: string;
	  receiverMobile?: string;
	  receiverName?: string;
	  receiverPhone?: string;
	  receiverState?: string;
	  receiverTown?: string;
	  userId?: string;
	  sellerNick?: string;
	  platformId?: string;
	  togetherId?: string;
	  shipListPrintStatus?: string;
	  waybillPrintStatus?: string;
	  mainTid?: string;
	  orderCode?: string;
	  isSuccess?: boolean;
	  errorMsg?: string;
	  /**
	   * TradeVo
	   */
	  trades?: {
		/**
		 * 手工单系统tid，主要用来申请单号
		 */
		realTid?: string;
		/**
		 * 手工单发货信息
		 */
		shipInfo?: string;
		/**
		 * 发件人
		 */
		senderName?: string;
		/**
		 * 发件人电话
		 */
		senderPhone?: string;
		/**
		 * 发件人手机
		 */
		senderMobile?: string;
		/**
		 * sender_province
		 */
		senderProvince?: string;
		/**
		 * sender_city
		 */
		senderCity?: string;
		/**
		 * sender_county
		 */
		senderCounty?: string;
		/**
		 * 发件人地址
		 */
		senderAddress?: string;
		sellerId?: string;
		buyerMessage?: string;
		buyerNick?: string;
		/**
		 * 买家昵称唯一标识
		 */
		buyerOpenUid?: string;
		createTime?: string;
		invoiceName?: string;
		invoiceTitle?: string;
		invoiceKind?: string;
		invoiceType?: string;
		isChange?: boolean;
		isMendian?: boolean;
		isCod?: boolean;
		isPrintFhd?: string;
		isPrintKdd?: string;
		payTime?: string;
		/**
		 * OrderVo
		 */
		orders?: {
		  hasConnected?: boolean;
		  num?: number;
		  sysNumber?: number;
		  numIid?: string;
		  oid?: string;
		  ptOid?: string;
		  cid?: string;
		  outerId?: string;
		  outerSkuId?: string;
		  payment?: string;
		  picPath?: string;
		  price?: string;
		  refundStatus?: string;
		  skuId?: string;
		  status?: string;
		  actualStockNum?: string;
		  canSellStockNum?: string;
		  tid?: string;
		  title?: string;
		  titleShort?: string;
		  adjustAmount?: string;
		  discount?: string;
		  skuPropertiesName?: string;
		  skuAlias?: string;
		  skuName?: string;
		  divideOrderFee?: string;
		  weight?: string;
		  pid?: string;
		  ydNo?: string;
		  kdName?: string;
		  togetherId?: string;
		  isShShip?: boolean;
		  totalFee?: string;
		  /**
		   * String
		   */
		  ydNoSet?: string[];
		  ydNoStr?: string;
		  /**
		   * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
		   */
		  stockOutHandleStatus?: number;
		  /**
		   * 是否缺货0-无缺货处理1：有缺货处理
		   */
		  isStockOut?: number;
		  /**
		   * 预售时间
		   */
		  preSaleTime?: string;
		  /**
		   * 是否为预售商品1表示是0表示否
		   */
		  isPreSale?: number;
		  printedNum?: number;
		  timingPromise?: string;
		  cutoffMinutes?: string;
		  esTime?: string;
		  deliveryTime?: string;
		  collectTime?: string;
		  dispatchTime?: string;
		  signTime?: string;
		  storeCode?: string;
		  /**
		   * 预计配送时间段
		   */
		  esRange?: string;
		  /**
		   * 预计送达时间
		   */
		  esDate?: string;
		  /**
		   * 预计送达时间
		   */
		  osDate?: string;
		  /**
		   * 预计送达时间
		   */
		  osRange?: string;
		  /**
		   * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
		   */
		  sendType?: string;
		  /**
		   * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
		   */
		  promiseService?: string;
		  noGoods?: boolean;
		  noGoodsLink?: boolean;
		  goodsWarn?: boolean;
		  firstSend?: boolean;
		  sysShipTime?: string;
		  sysPrintTime?: string;
		  /**
		   * 是否忽略，0否，1是
		   */
		  ignore?: number;
		  systemNumIid?: string;
		  systemSkuId?: string;
		  /**
		   * 货品规格名称
		   */
		  systemSkuName?: string;
		  sysStockCount?: string;
		  /**
		   * 库存预占数量
		   */
		  stockPreemptedNum?: string;
		  /**
		   * 已经分配库存数量
		   */
		  alreadyAllotStockNum?: string;
		  /**
		   * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
		   */
		  combinationAllotStockNum?: string;
		  /**
		   * 是否为组合商品，1:组合商品
		   */
		  isCombination?: number;
		  /**
		   * 标签状态0未生成1已生成
		   */
		  labelstatus?: number;
		  /**
		   * 0未打印1已打印
		   */
		  labelPrintStatus?: number;
		  /**
		   * 子商品信息 ,GroupRelationRecordDTO
		   */
		  groupRelationRecordList?: {
			/**
			 * @mbg.generated,表字段:id
			 */
			id?: number;
			/**
			 * @mbg.generated,用户ID,表字段:user_id
			 */
			userId?: number;
			/**
			 * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
			 */
			groupSysItemId?: number;
			/**
			 * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
			 */
			groupSysSkuId?: number;
			/**
			 * @mbg.generated,系统货品id,表字段:sys_item_id
			 */
			sysItemId?: number;
			/**
			 * @mbg.generated,系统规格id,表字段:sys_sku_id
			 */
			sysSkuId?: number;
			/**
			 * @mbg.generated,组合比例,表字段:group_proportion_num
			 */
			groupProportionNum?: number;
			/**
			 * 正品库存
			 */
			salableItemStock?: number;
			/**
			 * 可配货库存数量
			 */
			salableItemDistributableStock?: number;
			/**
			 * 正品预占数量
			 */
			salableItemPreemptedNum?: number;
			/**
			 * 成本价
			 */
			costPrice?: string;
			/**
			 * 货品名称
			 */
			sysItemName?: string;
			/**
			 * 货品别名
			 */
			sysItemAlias?: string;
			/**
			 * 货品规格名称
			 */
			sysSkuName?: string;
			/**
			 * 货品规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 货品规格商家编码
			 */
			skuOuterId?: string;
			/**
			 * 货品商家编码
			 */
			outerId?: string;
			/**
			 * 已分配库存数
			 */
			alreadyAllotStockNum?: number;
			/**
			 * 商品购买数量
			 */
			num?: number;
			/**
			 * @mbg.generated,货品规格图片,表字段:pic_url
			 */
			picUrl?: string;
			/**
			 * 库存情况
			 */
			stockStatusEnum?: {
			  [k: string]: any;
			};
			/**
			 * 库存预警值
			 */
			stockWarnNum?: number;
			/**
			 * 在途库存
			 */
			transitItemStock?: number;
			/**
			 * 毛重
			 */
			weight?: string;
			/**
			 * 净重
			 */
			netWeight?: string;
			/**
			 * 吊牌价
			 */
			tagPrice?: string;
			/**
			 * @mbg.generated网销价,表字段:price
			 */
			price?: string;
			/**
			 * 品牌id
			 */
			brandId?: number;
			/**
			 * 品牌名称
			 */
			brandName?: string;
			/**
			 * 货品分类id
			 */
			classifyId?: number;
			/**
			 * 分类名称
			 */
			classifyName?: string;
			/**
			 * 市场
			 */
			market?: string;
			/**
			 * 档口
			 */
			stall?: string;
			/**
			 * 货号
			 */
			itemNo?: string;
			/**
			 * 条形码
			 */
			barCode?: string;
			/**
			 * 供应商id
			 */
			supplierId?: number;
			/**
			 * 供应商名称
			 */
			supplierName?: string;
			/**
			 * 供应商地址-省
			 */
			storageAddrProvince?: string;
			/**
			 * 供应商地址-市
			 */
			storageAddrCity?: string;
			/**
			 * 供应商地址-区
			 */
			storageAddrDistrict?: string;
			/**
			 * 供应商地址-详细地址
			 */
			storageAddr?: string;
			[k: string]: any;
		  }[];
		  /**
		   * 预占库存标签,1预占;2回占
		   */
		  occupiedStockStatus?: number;
		  /**
		   * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
		   */
		  priceType?: string;
		  /**
		   * 是否忽略（0否，1是）
		   */
		  ignoreExcFlag?: number;
		  sysOuterId?: string;
		  sysPicPath?: string;
		  sysOuterSkuId?: string;
		  /**
		   * 订单修改类型,详情：SysExchangeTypeEnum
		   */
		  sysExchangeType?: number;
		  /**
		   * 是否是赠品
		   */
		  isGift?: string;
		  /**
		   * 吊牌价
		   */
		  tagPrice?: string;
		  /**
		   * 品牌id
		   */
		  brandId?: number;
		  /**
		   * 品牌名称
		   */
		  brandName?: string;
		  /**
		   * 货品分类id
		   */
		  classifyId?: number;
		  /**
		   * 分类名称
		   */
		  classifyName?: string;
		  /**
		   * 市场
		   */
		  market?: string;
		  /**
		   * 档口
		   */
		  stall?: string;
		  /**
		   * 货品规格名称
		   */
		  sysSkuName?: string;
		  /**
		   * 货品规格别名
		   */
		  sysSkuAlias?: string;
		  /**
		   * 货号
		   */
		  itemNo?: string;
		  /**
		   * 条形码
		   */
		  barCode?: string;
		  /**
		   * 成本价
		   */
		  costPrice?: string;
		  /**
		   * 供应商id
		   */
		  supplierId?: number;
		  /**
		   * 供应商名称
		   */
		  supplierName?: string;
		  /**
		   * 供应商地址-省
		   */
		  storageAddrProvince?: string;
		  /**
		   * 供应商地址-市
		   */
		  storageAddrCity?: string;
		  /**
		   * 供应商地址-区
		   */
		  storageAddrDistrict?: string;
		  /**
		   * 供应商地址-详细地址
		   */
		  storageAddr?: string;
		  /**
		   * 店铺id
		   */
		  sellerId?: string;
		  /**
		   * 尺码
		   */
		  platform?: string;
		  /**
		   * 店铺名称
		   */
		  sellerNick?: string;
		  /**
		   * 货品简称
		   */
		  sysItemAlias?: string;
		  /**
		   * 囤货数量
		   */
		  stockpileNum?: number;
		  /**
		   * 缺货数量
		   */
		  stockOutNum?: number;
		  /**
		   * 颜色
		   */
		  color?: string;
		  /**
		   * 尺码
		   */
		  size?: string;
		  /**
		   * 代发订单
		   */
		  dropShipping?: boolean;
		  /**
		   * 验货发货属于这笔运单号的子单
		   */
		  inspectionShippingOrder?: boolean;
		  /**
		   * 模板id验货发货时返回
		   */
		  templateId?: string;
		  /**
		   * 验货发货快递单号
		   */
		  inspectionExNumber?: string;
		  /**
		   * 验货发货快递公司code
		   */
		  inspectionExCode?: string;
		  /**
		   * 验货发货快递单类型
		   */
		  inspectionKddType?: number;
		  /**
		   * 免费服务项条目详情信息
		   */
		  freeEntryServiceInfo?: string;
		  /**
		   * 付费服务项条目信息
		   */
		  payEntryServiceInfo?: string;
		  /**
		   * 判断是否需要上传序列码
		   */
		  needSerialNumber?: boolean;
		  /**
		   * 商品识别码（SN码、IMEI号、ICCID码)
		   */
		  productIdCode?: string;
		  /**
		   * 达人id
		   */
		  authorId?: string;
		  /**
		   * 达人name
		   */
		  authorName?: string;
		  /**
		   * 是否线上发货
		   */
		  onlineShip?: boolean;
		  /**
		   * 商品结算金额
		   */
		  settleAmount?: string;
		  /**
		   * 运费
		   */
		  freightAmount?: string;
		  /**
		   * 总金额
		   */
		  settleAmountSum?: string;
		  /**
		   * 商品所属店铺id
		   */
		  itemSellerId?: string;
		  /**
		   * 商品所属平台
		   */
		  itemPlatform?: string;
		  [k: string]: any;
		}[];
		/**
		 * OrderVo
		 */
		giftOrders?: {
		  hasConnected?: boolean;
		  num?: number;
		  sysNumber?: number;
		  numIid?: string;
		  oid?: string;
		  ptOid?: string;
		  cid?: string;
		  outerId?: string;
		  outerSkuId?: string;
		  payment?: string;
		  picPath?: string;
		  price?: string;
		  refundStatus?: string;
		  skuId?: string;
		  status?: string;
		  actualStockNum?: string;
		  canSellStockNum?: string;
		  tid?: string;
		  title?: string;
		  titleShort?: string;
		  adjustAmount?: string;
		  discount?: string;
		  skuPropertiesName?: string;
		  skuAlias?: string;
		  skuName?: string;
		  divideOrderFee?: string;
		  weight?: string;
		  pid?: string;
		  ydNo?: string;
		  kdName?: string;
		  togetherId?: string;
		  isShShip?: boolean;
		  totalFee?: string;
		  /**
		   * String
		   */
		  ydNoSet?: string[];
		  ydNoStr?: string;
		  /**
		   * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
		   */
		  stockOutHandleStatus?: number;
		  /**
		   * 是否缺货0-无缺货处理1：有缺货处理
		   */
		  isStockOut?: number;
		  /**
		   * 预售时间
		   */
		  preSaleTime?: string;
		  /**
		   * 是否为预售商品1表示是0表示否
		   */
		  isPreSale?: number;
		  printedNum?: number;
		  timingPromise?: string;
		  cutoffMinutes?: string;
		  esTime?: string;
		  deliveryTime?: string;
		  collectTime?: string;
		  dispatchTime?: string;
		  signTime?: string;
		  storeCode?: string;
		  /**
		   * 预计配送时间段
		   */
		  esRange?: string;
		  /**
		   * 预计送达时间
		   */
		  esDate?: string;
		  /**
		   * 预计送达时间
		   */
		  osDate?: string;
		  /**
		   * 预计送达时间
		   */
		  osRange?: string;
		  /**
		   * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
		   */
		  sendType?: string;
		  /**
		   * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
		   */
		  promiseService?: string;
		  noGoods?: boolean;
		  noGoodsLink?: boolean;
		  goodsWarn?: boolean;
		  firstSend?: boolean;
		  sysShipTime?: string;
		  sysPrintTime?: string;
		  /**
		   * 是否忽略，0否，1是
		   */
		  ignore?: number;
		  systemNumIid?: string;
		  systemSkuId?: string;
		  /**
		   * 货品规格名称
		   */
		  systemSkuName?: string;
		  sysStockCount?: string;
		  /**
		   * 库存预占数量
		   */
		  stockPreemptedNum?: string;
		  /**
		   * 已经分配库存数量
		   */
		  alreadyAllotStockNum?: string;
		  /**
		   * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
		   */
		  combinationAllotStockNum?: string;
		  /**
		   * 是否为组合商品，1:组合商品
		   */
		  isCombination?: number;
		  /**
		   * 标签状态0未生成1已生成
		   */
		  labelstatus?: number;
		  /**
		   * 0未打印1已打印
		   */
		  labelPrintStatus?: number;
		  /**
		   * 子商品信息 ,GroupRelationRecordDTO
		   */
		  groupRelationRecordList?: {
			/**
			 * @mbg.generated,表字段:id
			 */
			id?: number;
			/**
			 * @mbg.generated,用户ID,表字段:user_id
			 */
			userId?: number;
			/**
			 * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
			 */
			groupSysItemId?: number;
			/**
			 * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
			 */
			groupSysSkuId?: number;
			/**
			 * @mbg.generated,系统货品id,表字段:sys_item_id
			 */
			sysItemId?: number;
			/**
			 * @mbg.generated,系统规格id,表字段:sys_sku_id
			 */
			sysSkuId?: number;
			/**
			 * @mbg.generated,组合比例,表字段:group_proportion_num
			 */
			groupProportionNum?: number;
			/**
			 * 正品库存
			 */
			salableItemStock?: number;
			/**
			 * 可配货库存数量
			 */
			salableItemDistributableStock?: number;
			/**
			 * 正品预占数量
			 */
			salableItemPreemptedNum?: number;
			/**
			 * 成本价
			 */
			costPrice?: string;
			/**
			 * 货品名称
			 */
			sysItemName?: string;
			/**
			 * 货品别名
			 */
			sysItemAlias?: string;
			/**
			 * 货品规格名称
			 */
			sysSkuName?: string;
			/**
			 * 货品规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 货品规格商家编码
			 */
			skuOuterId?: string;
			/**
			 * 货品商家编码
			 */
			outerId?: string;
			/**
			 * 已分配库存数
			 */
			alreadyAllotStockNum?: number;
			/**
			 * 商品购买数量
			 */
			num?: number;
			/**
			 * @mbg.generated,货品规格图片,表字段:pic_url
			 */
			picUrl?: string;
			/**
			 * 库存情况
			 */
			stockStatusEnum?: {
			  [k: string]: any;
			};
			/**
			 * 库存预警值
			 */
			stockWarnNum?: number;
			/**
			 * 在途库存
			 */
			transitItemStock?: number;
			/**
			 * 毛重
			 */
			weight?: string;
			/**
			 * 净重
			 */
			netWeight?: string;
			/**
			 * 吊牌价
			 */
			tagPrice?: string;
			/**
			 * @mbg.generated网销价,表字段:price
			 */
			price?: string;
			/**
			 * 品牌id
			 */
			brandId?: number;
			/**
			 * 品牌名称
			 */
			brandName?: string;
			/**
			 * 货品分类id
			 */
			classifyId?: number;
			/**
			 * 分类名称
			 */
			classifyName?: string;
			/**
			 * 市场
			 */
			market?: string;
			/**
			 * 档口
			 */
			stall?: string;
			/**
			 * 货号
			 */
			itemNo?: string;
			/**
			 * 条形码
			 */
			barCode?: string;
			/**
			 * 供应商id
			 */
			supplierId?: number;
			/**
			 * 供应商名称
			 */
			supplierName?: string;
			/**
			 * 供应商地址-省
			 */
			storageAddrProvince?: string;
			/**
			 * 供应商地址-市
			 */
			storageAddrCity?: string;
			/**
			 * 供应商地址-区
			 */
			storageAddrDistrict?: string;
			/**
			 * 供应商地址-详细地址
			 */
			storageAddr?: string;
			[k: string]: any;
		  }[];
		  /**
		   * 预占库存标签,1预占;2回占
		   */
		  occupiedStockStatus?: number;
		  /**
		   * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
		   */
		  priceType?: string;
		  /**
		   * 是否忽略（0否，1是）
		   */
		  ignoreExcFlag?: number;
		  sysOuterId?: string;
		  sysPicPath?: string;
		  sysOuterSkuId?: string;
		  /**
		   * 订单修改类型,详情：SysExchangeTypeEnum
		   */
		  sysExchangeType?: number;
		  /**
		   * 是否是赠品
		   */
		  isGift?: string;
		  /**
		   * 吊牌价
		   */
		  tagPrice?: string;
		  /**
		   * 品牌id
		   */
		  brandId?: number;
		  /**
		   * 品牌名称
		   */
		  brandName?: string;
		  /**
		   * 货品分类id
		   */
		  classifyId?: number;
		  /**
		   * 分类名称
		   */
		  classifyName?: string;
		  /**
		   * 市场
		   */
		  market?: string;
		  /**
		   * 档口
		   */
		  stall?: string;
		  /**
		   * 货品规格名称
		   */
		  sysSkuName?: string;
		  /**
		   * 货品规格别名
		   */
		  sysSkuAlias?: string;
		  /**
		   * 货号
		   */
		  itemNo?: string;
		  /**
		   * 条形码
		   */
		  barCode?: string;
		  /**
		   * 成本价
		   */
		  costPrice?: string;
		  /**
		   * 供应商id
		   */
		  supplierId?: number;
		  /**
		   * 供应商名称
		   */
		  supplierName?: string;
		  /**
		   * 供应商地址-省
		   */
		  storageAddrProvince?: string;
		  /**
		   * 供应商地址-市
		   */
		  storageAddrCity?: string;
		  /**
		   * 供应商地址-区
		   */
		  storageAddrDistrict?: string;
		  /**
		   * 供应商地址-详细地址
		   */
		  storageAddr?: string;
		  /**
		   * 店铺id
		   */
		  sellerId?: string;
		  /**
		   * 尺码
		   */
		  platform?: string;
		  /**
		   * 店铺名称
		   */
		  sellerNick?: string;
		  /**
		   * 货品简称
		   */
		  sysItemAlias?: string;
		  /**
		   * 囤货数量
		   */
		  stockpileNum?: number;
		  /**
		   * 缺货数量
		   */
		  stockOutNum?: number;
		  /**
		   * 颜色
		   */
		  color?: string;
		  /**
		   * 尺码
		   */
		  size?: string;
		  /**
		   * 代发订单
		   */
		  dropShipping?: boolean;
		  /**
		   * 验货发货属于这笔运单号的子单
		   */
		  inspectionShippingOrder?: boolean;
		  /**
		   * 模板id验货发货时返回
		   */
		  templateId?: string;
		  /**
		   * 验货发货快递单号
		   */
		  inspectionExNumber?: string;
		  /**
		   * 验货发货快递公司code
		   */
		  inspectionExCode?: string;
		  /**
		   * 验货发货快递单类型
		   */
		  inspectionKddType?: number;
		  /**
		   * 免费服务项条目详情信息
		   */
		  freeEntryServiceInfo?: string;
		  /**
		   * 付费服务项条目信息
		   */
		  payEntryServiceInfo?: string;
		  /**
		   * 判断是否需要上传序列码
		   */
		  needSerialNumber?: boolean;
		  /**
		   * 商品识别码（SN码、IMEI号、ICCID码)
		   */
		  productIdCode?: string;
		  /**
		   * 达人id
		   */
		  authorId?: string;
		  /**
		   * 达人name
		   */
		  authorName?: string;
		  /**
		   * 是否线上发货
		   */
		  onlineShip?: boolean;
		  /**
		   * 商品结算金额
		   */
		  settleAmount?: string;
		  /**
		   * 运费
		   */
		  freightAmount?: string;
		  /**
		   * 总金额
		   */
		  settleAmountSum?: string;
		  /**
		   * 商品所属店铺id
		   */
		  itemSellerId?: string;
		  /**
		   * 商品所属平台
		   */
		  itemPlatform?: string;
		  [k: string]: any;
		}[];
		gifts?: string;
		/**
		 * 实收金额
		 */
		receivedPayment?: string;
		payment?: string;
		discountAmount?: string;
		postFee?: string;
		receiverMobile?: string;
		receiverCity?: string;
		receiverDistrict?: string;
		receiverPhone?: string;
		receiverAddress?: string;
		receiverName?: string;
		receiverState?: string;
		receiverTown?: string;
		receiverZip?: string;
		sellerFlag?: string;
		userId?: string;
		sellerNick?: string;
		platform?: string;
		sellerMemo?: string;
		ignoreType?: number;
		sellerMemoFlag?: string;
		sellerMemoFlagName?: string;
		tid?: string;
		ptTid?: string;
		totalFee?: string;
		status?: string;
		gift?: string;
		type?: string;
		sendRemindFlag?: number;
		sendRemindHour?: string;
		isAddCostSF?: boolean;
		/**
		 * OrderPromiseDetailVo
		 */
		orderPromiseDetailVo?: {
		  orderPromiseKdCode?: string;
		  orderPromiseKdName?: string;
		  /**
		   * 指定包材
		   */
		  orderPromiseBc?: string;
		  orderPromiseDeliveryTime?: number;
		  [k: string]: any;
		};
		freeSF?: number;
		/**
		 * 商家优惠金额
		 */
		sellerDiscount?: string;
		distributorTid?: string;
		/**
		 * 平台折扣金额
		 */
		platformDiscount?: string;
		/**
		 * 宝贝数量：取订单中的商品总数量
		 */
		totalNums?: number;
		/**
		 * 宝贝种类
		 */
		itemCateNum?: number;
		/**
		 * 是否是预发货
		 */
		isPreShip?: boolean;
		isPending?: boolean;
		isCancelSend?: boolean;
		isMatchCancelSend?: boolean;
		imel?: string;
		deviceSn?: string;
		overseaTracing?: string;
		expressName?: string;
		/**
		 * 催发货标记0无催发货，1有催发货；
		 */
		urge?: number;
		/**
		 * 催发货时间
		 */
		urgeShippingTime?: string;
		/**
		 * 订单标签 ,String
		 */
		serviceTagList?: string[];
		/**
		 * 预发货任务id
		 */
		preShipId?: string;
		/**
		 * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单(该参数为map)
		 */
		sellerFlagSys?: {
		  /**
		   * String
		   */
		  mapKey?: {
			[k: string]: any;
		  };
		  /**
		   * Integer
		   */
		  mapValue?: {
			[k: string]: any;
		  };
		  [k: string]: any;
		};
		/**
		 * 标识当前订单是否存在退款，不做存储
		 */
		hasRefund?: boolean;
		refundStatus?: string;
		totalWeight?: string;
		/**
		 * 快递单号 ,String
		 */
		ydNoSet?: string[];
		/**
		 * 多包裹运单数量
		 */
		multiPackYdCount?: number;
		/**
		 * 快递公司编号
		 */
		logisticsId?: string;
		/**
		 * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
		 */
		stockOutHandleStatus?: number;
		/**
		 * 是否缺货0-无缺货处理1：有缺货处理
		 */
		isStockOut?: number;
		/**
		 * 预售时间
		 */
		preSaleTime?: string;
		/**
		 * 是否为预售商品1表示是0表示否
		 */
		isPreSale?: number;
		/**
		 * 风控状态（值为1则为风控订单，值为0则为正常订单）
		 */
		riskControlStatus?: string;
		receiverNameMask?: string;
		receiverPhoneMask?: string;
		receiverAddressMask?: string;
		receiverMobileMask?: string;
		idxEncodeReceiverMobile?: string;
		idxEncodeReceiverName?: string;
		idxEncodeReceiverAddress?: string;
		/**
		 * 订单调fullInfo接口的时间
		 */
		tidFullInfoTime?: string;
		/**
		 * 天猫直送，值true或者false
		 */
		tmallDelivery?: boolean;
		/**
		 * 3PL有时效订单标，值true或者false
		 */
		threePlTiming?: boolean;
		/**
		 * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
		 */
		deliveryCps?: string;
		/**
		 * 取
		 */
		cpCode?: string;
		/**
		 * cpCode转cpName
		 */
		cpName?: string;
		/**
		 * 是否催发货1：催发货0：非催发货
		 */
		isUrgeDelivery?: number;
		/**
		 * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
		 */
		caid?: string;
		/**
		 * trade_fromWAP,JHS交易内部来源
		 */
		tradeFrom?: string;
		/**
		 * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
		 */
		fenxiaos?: number;
		payHours?: number;
		receiverSecret?: string;
		mobileSecret?: string;
		timingPromise?: string;
		promiseService?: string;
		esDate?: string;
		esRange?: string;
		osDate?: string;
		osRange?: string;
		cutoffMinutes?: string;
		esTime?: string;
		deliveryTime?: string;
		collectTime?: string;
		sendTime?: string;
		signTime?: string;
		dispatchTime?: string;
		/**
		 * value=logistics_upgrade为天猫物流升级订单
		 */
		asdpBizType?: string;
		/**
		 * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
		 */
		asdpAds?: string;
		/**
		 * 天猫送货上门快递名称
		 */
		sendExName?: string;
		/**
		 * 天猫送货上门快递编码
		 */
		sendExCode?: string;
		/**
		 * 全渠道商品通相关字段
		 */
		omnichannelParam?: string;
		/**
		 * 是否是海外购
		 */
		isThreePl?: boolean;
		/**
		 * 是否风险留言订单
		 */
		pbly?: boolean;
		/**
		 * 家装订单
		 */
		tmserSpu?: boolean;
		/**
		 * 淘宝后台getShippingType
		 */
		shippingType?: string;
		/**
		 * 淘宝后台getShippingType对应名称，后期
		 */
		shippingName?: string;
		deliveryTypeDesc?: string;
		/**
		 * 订单类型交易方式,一口价,送礼,赠品领取,货到付款,定金预售
		 */
		tradeType?: string;
		/**
		 * 支付类型
		 */
		payType?: string;
		biz?: string;
		/**
		 * 是否为乡镇订单
		 */
		hasTown?: number;
		/**
		 * 打印时间
		 */
		printTime?: string;
		/**
		 * 发货时间
		 */
		shipTime?: string;
		/**
		 * 系统发货时间
		 */
		sysShipTime?: string;
		/**
		 * 提示手动合单的判断标示
		 */
		mergeBuyerNick?: string;
		noGoodsLink?: boolean;
		goodsWarn?: boolean;
		firstSend?: boolean;
		smartSelectExpress?: string;
		labelstatus?: number;
		/**
		 * 0未打印1全部打印
		 */
		labelPrintStatus?: number;
		promiseDeliveryTime?: boolean;
		/**
		 * PromiseLogistics
		 */
		promiseLogisticsList?: {
		  company?: string;
		  exCode?: string;
		  [k: string]: any;
		}[];
		wrapperDescription?: boolean;
		duoduoWholesale?: boolean;
		shipHold?: boolean;
		jdStoreOrderType?: number;
		jdOrderShipType?: number;
		/**
		 * 额外运单 ,ExtraDeliveryVO
		 */
		extraDeliveryList?: {
		  /**
		   * 快递运单号
		   */
		  trackingNumber?: string;
		  /**
		   * 快递公司id
		   */
		  logisticsId?: number;
		  /**
		   * 快递公司名称
		   */
		  logisticsName?: string;
		  [k: string]: any;
		}[];
		/**
		 * true跨境订单false非跨境
		 */
		crossBorder?: boolean;
		/**
		 * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
		 */
		consolidateType?: string;
		/**
		 * 订单来源：HAND手工单
		 */
		source?: string;
		changeAdderFlag?: number;
		/**
		 * 拼团订单未完成true未完成false或者为空为正常订单
		 */
		pOrderUnfinished?: boolean;
		/**
		 * 订单类型
		 */
		tradeTypeTagStr?: string;
		/**
		 * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
		 */
		openAddressId?: string;
		/**
		 * 精选联盟
		 */
		isSelectedAlliance?: boolean;
		/**
		 * 小店自卖
		 */
		isSmallStoreSelfSelling?: boolean;
		/**
		 * 闪电购商品
		 */
		isFlashBuyingProducts?: boolean;
		/**
		 * 重新发货
		 */
		isResend?: boolean;
		/**
		 * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
		 */
		receiverId?: string;
		/**
		 * 回流订单平台类型
		 */
		hlPlatformType?: string;
		/**
		 * 回流订单是否加密
		 */
		hlEncryptOrder?: boolean;
		/**
		 * fxg承诺日达1表示是0表示否
		 */
		appointmentArrival?: number;
		/**
		 * 发货仓编号
		 */
		outWarehouseId?: string;
		/**
		 * 发货仓名称
		 */
		warehouseName?: string;
		/**
		 * 指定时间送达承诺时间送达
		 */
		receiptDate?: string;
		/**
		 * ali定制服务1表示是0表示否
		 */
		aliEntryServiceInfo?: number;
		/**
		 * 判断是否需要上传序列码
		 */
		needSerialNumber?: boolean;
		/**
		 * 达人标签订单
		 */
		isAuthorOrder?: boolean;
		/**
		 * 线下备注
		 */
		sysMemo?: string;
		/**
		 * 标签：加运费发顺丰
		 */
		hasSfExpressService?: boolean;
		/**
		 * 顺丰加价服务费
		 */
		sfExpressFee?: string;
		/**
		 * 送货上门
		 */
		hasDeliveryOnDoor?: boolean;
		/**
		 * 京东POP订单选项
		 */
		jdDeliveryOnDoorOption?: number;
		/**
		 * 是否线上发货
		 */
		onlineShip?: boolean;
		/**
		 * 是否拆单发货
		 */
		isSplit?: boolean;
		/**
		 * 直邮活动标记，拼多多平台
		 */
		directMailActivity?: boolean;
		/**
		 * 回流密闻订单加解密信息，一般用于脱敏信息打单取号等 ,DownstreamEncryptDetails
		 */
		downstreamEncryptDetails?: {
		  sellerNick?: string;
		  sellerId?: string;
		  tid?: string;
		  oaid?: string;
		  hlPlatformType?: string;
		  platformExtraInfo?: string;
		  platformAppKey?: string;
		  receiverProvince?: string;
		  receiverCity?: string;
		  receiverArea?: string;
		  receiverTown?: string;
		  encryptReceiverMobile?: string;
		  encryptReceiverAddress?: string;
		  encryptReceiverName?: string;
		  [k: string]: any;
		};
		storageTime?: string;
		/**
		 * 插旗标签
		 */
		sellerFlagTag?: string;
		[k: string]: any;
	  }[];
	  sendRemindFlag?: number;
	  sendRemindHour?: string;
	  payTime?: string;
	  isPending?: boolean;
	  isAddCostSF?: boolean;
	  freeSF?: number;
	  expressName?: string;
	  sellerId?: string;
	  receiverNameMask?: string;
	  receiverPhoneMask?: string;
	  receiverAddressMask?: string;
	  receiverMobileMask?: string;
	  idxEncodeReceiverMobile?: string;
	  idxEncodeReceiverName?: string;
	  idxEncodeReceiverAddress?: string;
	  encodeTid?: string;
	  caid?: string;
	  /**
	   * 提示手动合单的判断标示
	   */
	  mergeBuyerNick?: string;
	  /**
	   * 多包裹运单数量
	   */
	  multiPackYdCount?: number;
	  /**
	   * 是否包含预发货订单
	   */
	  isPreShip?: boolean;
	  /**
	   * String
	   */
	  preShipIds?: string[];
	  hasRefund?: boolean;
	  receiverZip?: string;
	  /**
	   * (该参数为map)
	   */
	  sellerFlagSys?: {
		/**
		 * String
		 */
		mapKey?: {
		  [k: string]: any;
		};
		/**
		 * Integer
		 */
		mapValue?: {
		  [k: string]: any;
		};
		[k: string]: any;
	  };
	  /**
	   * :
	   */
	  tids?: string[];
	  /**
	   * :
	   */
	  ptTids?: string[];
	  /**
	   * CaiNiaoIntelliExpress
	   */
	  smartExpress?: {
		exCode?: string;
		exName?: string;
		[k: string]: any;
	  };
	  type?: string;
	  /**
	   * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
	   */
	  fenxiaos?: number;
	  /**
	   * 是否合单
	   */
	  isMerge?: string;
	  /**
	   * 总实收金额
	   */
	  totalReceivedPayment?: string;
	  /**
	   * 总支付金额
	   */
	  totalPayment?: string;
	  /**
	   * 总商家优惠金额
	   */
	  totalSellerDiscount?: string;
	  /**
	   * 总平台折扣金额
	   */
	  totalPlatformDiscount?: string;
	  /**
	   * 总邮费
	   */
	  totalPostFee?: string;
	  /**
	   * 总重量
	   */
	  totalWeight?: string;
	  /**
	   * 已发货的快递单号
	   */
	  ydNo?: string;
	  /**
	   * 快递单号 ,String
	   */
	  ydNoSet?: string[];
	  /**
	   * 快递公司 ,String
	   */
	  logisticsCompanySet?: string[];
	  /**
	   * 快递单号 ,String
	   */
	  waybillNoSet?: string[];
	  /**
	   * 商品结算金额
	   */
	  itemSettleAmount?: string;
	  /**
	   * 运费
	   */
	  freightAmount?: string;
	  /**
	   * 总金额=商品结算金额+运费
	   */
	  orderSettleAmountSum?: string;
	  /**
	   * 验单发货订单号搜索先发货订单模板id
	   */
	  verifyOrderListShowId?: number;
	  /**
	   * 模版的扩展字段，里面包含了用户模版id
	   */
	  templateYdAttr?: string;
	  /**
	   * 订单标记（用于黑白订单标记）,0：正常订单（默认）,1：白订单（表明这笔订单是商家请人刷的订单）,2：黑名单（表明这笔订单是别人恶意刷的订单）
	   */
	  tidMark?: number;
	  /**
	   * OrderPromiseVo
	   */
	  orderPromiseVo?: {
		orderPromiseDeliveryTime?: number;
		isManyKd?: boolean;
		[k: string]: any;
	  };
	  /**
	   * Object
	   */
	  orderTagList?: {
		[k: string]: any;
	  }[];
	  payHours?: number;
	  goodsNum?: string;
	  /**
	   * 家装订单
	   */
	  tmserSpu?: boolean;
	  /**
	   * 异常地址
	   */
	  abnormalAddress?: boolean;
	  /**
	   * 智选快递的快递名称
	   */
	  ExpressName?: string;
	  /**
	   * 打印菜鸟面单时是否需要隐私服务
	   */
	  cnPrivacy?: boolean;
	  receiverSecret?: string;
	  mobileSecret?: string;
	  /**
	   * OpenAddresseeID（收件人ID）,基于收件人的信息(姓名、地址、手机号、电话）、订单创建时间、店铺、appkey加密生成的ID。,相同的收件人，在同一家店铺，在固定时间周期内（通常1个自然周）内创建的订单，OAID相同。
	   */
	  oaid?: string;
	  /**
	   * 天猫送货上门快递名称
	   */
	  sendExName?: string;
	  /**
	   * 天猫送货上门快递编码
	   */
	  sendExCode?: string;
	  /**
	   * value=logistics_upgrade为天猫物流升级订单
	   */
	  asdpBizType?: string;
	  /**
	   * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
	   */
	  asdpAds?: string;
	  signTime?: string;
	  deliveryTime?: string;
	  /**
	   * 是否为乡镇订单
	   */
	  hasTown?: number;
	  /**
	   * 催发货标记0无催发货，1有催发货；
	   */
	  urge?: number;
	  /**
	   * 催发货时间
	   */
	  urgeShippingTime?: string;
	  changeAdderFlag?: number;
	  noGoods?: boolean;
	  noGoodsLink?: boolean;
	  goodsWarn?: boolean;
	  firstSend?: boolean;
	  preSell?: boolean;
	  /**
	   * 疫情标记,0：非疫情地区,1：疫情中风险地区,2：疫情高风险地区
	   */
	  epidemicMark?: number;
	  /**
	   * 风控状态（值为1则为风控订单，值为0则为正常订单）
	   */
	  riskControlStatus?: string;
	  /**
	   * 智选快递
	   */
	  smartSelectExpress?: string;
	  /**
	   * 智选快递模板级别推荐结果、是否手动指定
	   */
	  smartExpressTemplate?: string;
	  /**
	   * 合单智选快递模板级别推荐结果、是否手动指定
	   */
	  packSmartExpressTemplate?: string;
	  labelstatus?: number;
	  /**
	   * 0未打印1全部打印2部分打印
	   */
	  labelPrintStatus?: number;
	  promiseDeliveryTime?: boolean;
	  /**
	   * PromiseLogistics
	   */
	  promiseLogisticsList?: {
		company?: string;
		exCode?: string;
		[k: string]: any;
	  }[];
	  wrapperDescription?: boolean;
	  /**
	   * 顺丰包邮
	   */
	  freeSf?: boolean;
	  /**
	   * true跨境订单false非跨境
	   */
	  crossBorder?: boolean;
	  tradeType?: string;
	  /**
	   * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
	   */
	  consolidateType?: string;
	  /**
	   * 订单来源:其他平台订单,Hand手工订单
	   */
	  source?: string;
	  /**
	   * 拼团订单未完成true未完成false或者为空为正常订单
	   */
	  pOrderUnfinished?: boolean;
	  /**
	   * 分销商用户ID
	   */
	  distributorUserId?: number;
	  /**
	   * 分销商账号
	   */
	  distributorAccount?: string;
	  /**
	   * 分销商名称
	   */
	  distributorName?: string;
	  /**
	   * 分销商联系人
	   */
	  distributorLinkMan?: string;
	  /**
	   * 分销商联系电话
	   */
	  distributorMobile?: string;
	  /**
	   * 分销商备注
	   */
	  distributorRemark?: string;
	  /**
	   * 分销状态,0:申请中,1：合作中,2：拒绝,3：终止
	   */
	  distributorStatus?: number;
	  /**
	   * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
	   */
	  openAddressId?: string;
	  /**
	   * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
	   */
	  receiverId?: string;
	  /**
	   * 回流订单平台类型
	   */
	  hlPlatformType?: string;
	  /**
	   * 回流订单是否加密
	   */
	  hlEncryptOrder?: boolean;
	  /**
	   * fxg承诺日达1表示是0表示否
	   */
	  appointmentArrival?: number;
	  /**
	   * 发货仓编号
	   */
	  outWarehouseId?: string;
	  /**
	   * 发货仓名称
	   */
	  warehouseName?: string;
	  /**
	   * 指定时间送达承诺时间送达
	   */
	  receiptDate?: string;
	  /**
	   * ali定制服务1表示是0表示否
	   */
	  aliEntryServiceInfo?: number;
	  /**
	   * 送货上门
	   */
	  hasDeliveryOnDoor?: boolean;
	  /**
	   * 退款状态
	   */
	  refundStatus?: string;
	  /**
	   * 订单状态
	   */
	  status?: string;
	  /**
	   * 直邮活动标记，拼多多平台
	   */
	  directMailActivity?: boolean;
	  /**
	   * 是否线上发货
	   */
	  onlineShip?: boolean;
	  /**
	   * 是否拆单发货
	   */
	  isSplit?: boolean;
	  /**
	   * 订单标签 ,String
	   */
	  serviceTagList?: string[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * TradeScanPrintConfigSubDTO :TradeScanPrintConfigSubDTO
 */
export interface TradeScanPrintEditConfigForSubRequest {
	/**
	 * id
	 */
	id?: number;
	/**
	 * 订单销售类型(支持多选)，ITEM_1_SKU_1:单款单件',,@seeTradeScanPrintSellAttributeTypeEnum ,String
	 */
	sellAttributeTypeList?: string[];
	[k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeScanPrintEditConfigForSubResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }
  