import { TradeRangeSelectValue } from "@/pages/Trade/components/SearchContainer/RangeSelect";
import { IPackage } from "@/pages/Trade/interface";
import { platform } from "@/types/schemas/common";

export type SearchAddrType = "all" | "custom" | "other";

export interface CustomAddressItem {
	/**
	 * @mbg.generated,自增id,表字段:id
	 */
	id?: string;
	/**
	 * @mbg.generated,0表示其他1表示自定义
	 */
	type?: number;
	/**
	 * @mbg.generated,备注，即地址框里面填的备注
	 */
	alias?: string;
	/**
	 * @mbg.generated,省市区是否包含：0-不包含，1-包含
	 */
	areaContain?: number;
	/**
	 * @mbg.generated,自定义地址表示自定义条件下的手输入的关键字
	 */
	selValue?: string;
	/**
	 * @mbg.generated,省市区选中结构,省市区标准地址，如：[{"name":"辽宁省","childList":[]},{"name":"广西壮族自治区","childList":[]}]
	 */
	areaJson?: string;
	[k: string]: any;
}

export interface TradeUserAreaGetAreasResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PrintProvinceUserdefinedWithBLOBsDTO
	 */
	data?: CustomAddressItem[];
	[k: string]: any;
}

/**
 * TradeDTO :TradeDTO
 */
/**
 * TradeQueryDTO :TradeQueryDTO
 */
export interface TradeQueryTradeRequest {
	waveNo?: string;
	/**
	 * TB,淘宝,,TM,天猫,,PDD,拼多多,,FXG,抖音,,HAND,手工单;
	 */
	platform?: string;
	/**
	 * 店铺id
	 */
	sellerId?: string;
	startTime?: string;
	endTime?: string;
	/**
	 * 筛选时间类型1下单时间2付款时间3打印时间4发货时间
	 */
	timeType?: string;
	/**
	 * 订单状态ORDER_WAIT_PAY等待付款ORDER_PAID已付款ORDER_SHIPPED已发货,ORDER_SHIPPED_LITTLE部分发货ORDER_COMPLETED交易完成ALL_STATUS全部订单
	 */
	status?: string;
	/**
	 * 打印状态2仅快递单已打印3仅发货单已打印4均已打印0均未打印
	 */
	printStatus?: number | string;
	/**
	 * 自定义的地址关键字
	 */
	selValue?: string;
	/**
	 * 精准匹配省市区如：[{"name":"辽宁省","childList":[]},{"name":"广西壮族自治区","childList":[]}]
	 */
	areaJson?: string;
	/**
	 * 省市区是否包含：0包含1不包含
	 */
	areaContain?: number;
	/**
	 * 1有买家留言2有卖家备注3有买家留言+有卖家备注4没有留言且没有备注5红6黄7绿8蓝色9紫色
	 */
	flagValue?: string;
	/**
	 * 留言内容，英文逗号分隔，最多支持10条，不得超过100个字符
	 */
	buyerMessage?: string;
	/**
	 * 备注内容，英文逗号分隔，最多支持10条，不得超过100个字符
	 */
	sellerMemo?: string;
	/**
	 * 1,2_1表示包含红黄旗从自定义备注接口找
	 */
	flagSelValue?: string;
	/**
	 * 快捷查询,合并订单merge;,非合并订单noMerge,有运费订单fare;,无运费订单noFare;,乡镇订单town,非乡镇订单noTown,有发票invoice,无发票noInvoice,有挂起pending,无挂起noPending
	 */
	quickQuery?: string;
	/**
	 * {@codetradeQuery.getSellAttribute},查询销售属性,1:单商品单规格单件,,2:单商品单规格多件,,3:单商品多规格多件,,4:多商品多规格多件
	 */
	sellAttribute?: string;
	/**
	 * 商品名称编码简称id
	 */
	shortNameIncluding?: string;
	/**
	 * 规格名称规格别名查询
	 */
	skuIncluding?: string;
	/**
	 * 买家昵称,隔开
	 */
	buyerNick?: string;
	/**
	 * 收件人,隔开
	 */
	receiveName?: string;
	/**
	 * 手机号,隔开
	 */
	receiveMobile?: string;
	/**
	 * 订单编号,隔开
	 */
	tid?: string;
	/**
	 * 运单号,隔开
	 */
	sid?: string;
	/**
	 * 0快递1物流
	 */
	logisticsType?: string;
	/**
	 * REFUND_SUCCESSED("退款成功"),,REFUND_ING("退款中"),,NOT_REFUND("无售后或售后关闭");
	 */
	refundStatus?: string;
	/**
	 * 订单总金额1-100
	 */
	payment?: string;
	/**
	 * 宝贝包含或不包含标识：,0、1：包含,-1：不包含
	 */
	equalFlag?: number;
	/**
	 * 订单重量范围5-10
	 */
	weightRange?: string;
	/**
	 * 订单总数量子订单数量5-10
	 */
	orderRange?: string;
	/**
	 * 商品总数量1-50
	 */
	goodsTotalNum?: string;
	/**
	 * 商品种类1-50
	 */
	goodsTypeNum?: string;
	/**
	 * 是否精确精准就是false非精准就是true
	 */
	isPrecise?: boolean;
	/**
	 * 是否精确到订单精准就是false非精准就是true
	 */
	isPreciseByTrade?: boolean;
	/**
	 * 给mock用,此值传1会有订单
	 */
	testStatus?: number;
	/**
	 * 订单标记已标记hasMark为标记noMark标记查询存储的值
	 */
	bizMark?: string;
	/**
	 * 订单标记0不包含1包含
	 */
	bizMarkContain?: number;
	customizeResidueSendTime?: string;
	smartSelectExpress?: string; // 智选快递
	tradeExpressImportLogSequence?: string;
	/**
	 * true 货品包含
	 * false 货品不包含
	 */
	goodsContain?: boolean;
	/**
	 * 货品简称/编码
	 */
	goodsAliasOrIdStr?: string;
	/**
	 * 货品规格名称/编码/别名
	 */
	goodsSkuNameOrIdOrAliasStr?: string;
	/**
	 * 颜色尺码
	 */
	colorIncludingList?: string;
	sizeIncludingList?: string;
	includeIngColorOrSize?: boolean;
	/**
	 * 订单来源: platfromOrder 平台订单
	 * handOrder 手工订单
	 */
	orderSource?: string;
	distributorUserIds?: string[];
	pageNo?: number;
	pageSize?: number;
	sellerFlag?: string[];
	[k: string]: any;
}

export interface TradeUserAreaAddUserAreasRequest {
	id?: string | number;
	/**
	 * @mbg.generated,0表示其他1表示自定义
	 */
	type?: number;
	/**
	 * @mbg.generated,备注，即地址框里面填的备注
	 */
	alias?: string;
	/**
	 * @mbg.generated,省市区是否包含：0-不包含，1-包含
	 */
	areaContain?: number;
	/**
	 * @mbg.generated,自定义地址表示自定义条件下的手输入的关键字
	 */
	selValue?: string;
	/**
	 * @mbg.generated,省市区选中结构,省市区标准地址，如：[{"name":"辽宁省","childList":[]},{"name":"广西壮族自治区","childList":[]}]
	 */
	areaJson?: string;
	addToSubUser?: number;
	[k: string]: any;
}
export interface TradeUserAreaAddUserAreasResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
}

export interface UserAreaUpdateUserAreasResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	data?: {
		alias?: string;
		id?: string;
		name?: string;
		type?: string;
		value?: string;
		[k: string]: any;
	};
	apiName?: string;
	[k: string]: any;
}

/**
 * ResponseBody<TradeListVo> :ResponseBody
 */
export interface TradeQueryTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeListVo
	 */
	data?: {
		/**
		 * ErpPackageInfoVo
		 */
		list?: IPackage[];
		totalCount?: number;
		total?: number;
		[k: string]: any;
	};
	apiName?: string;
	[k: string]: any;
}

export interface TradeSearchFlagRule {
	/**
	 * @mbg.generated,自增id,表字段:id
	 */
	id: number;
	/**
	 * @mbg.generated,用户id,表字段:user_id
	 */
	userId?: number;
	/**
	 * @mbg.generated,自定义值添加时候也是这个含义,1,2_1表示包含红黄旗1,2_0表示不包含红黄旗
	 */
	selValue?: string;
	/**
	 * @mbg.generated,是否包含0不包含1包含,表字段:is_contains
	 */
	isContains?: number;
	/**
	 * @mbg.generated,-1系统，1自定义,表字段:type
	 */
	type?: number;
	/**
	 * @mbg.generated,表示备注，即这个搜索条件的名称如蓝紫(包含)(测试看下)
	 */
	comment?: string;
	[k: string]: any;
}

export type TradeGetSearchFlagRulesApiResponse = TradeSearchFlagRule[];
export interface TradeAddSearchFlagRulesApiRequest {
	/**
	 * @mbg.generated,用户id,表字段:user_id
	 */
	userId?: number;
	/**
	 * @mbg.generated,自定义值添加时候也是这个含义,1,2_1表示包含红黄旗1,2_0表示不包含红黄旗
	 */
	selValue?: string;
	/**
	 * @mbg.generated,是否包含0不包含1包含,表字段:is_contains
	 */
	isContains?: number;
	/**
	 * @mbg.generated,-1系统，1自定义,表字段:type
	 */
	type?: number;
	/**
	 * @mbg.generated,表示备注，即这个搜索条件的名称如蓝紫(包含)(测试看下)
	 */
	comment?: string;
}

export interface TradeUserGetShopSyncConfigRequest {
	[k: string]: any;
}

/**
 * ResponseBody<List<AllShopSyncTimeVO>> :ResponseBody
 */
export interface ShopSyncTimeItem {
	/**
	 * 最后同步时间
	 */
	lastSyncTime?: string;
	/**
	 * 店铺名称
	 */
	sellerNick?: string;
	/**
	 * 状态0未订购1已暂停2正常
	 */
	enableStatus?: number;
	platform?: string;
	sellerId?: number;
	[k: string]: any;
}
export interface TradeUserGetShopSyncConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * AllShopSyncTimeVO
	 */
	data?: ShopSyncTimeItem[];
	[k: string]: any;
}

/**
 * HighSyncShopConfigDTO :HighSyncShopConfigDTO
 */

export interface HighSyncShopItem {
	userId?: number;
	sellerId?: number;
	sellerNick?: string;
	platform?: string;
	[k: string]: any;
}
export interface TradeUserHighSyncShopRequest {
	/**
	 * 订单号
	 */
	orderId?: string;
	/**
	 * 开始时间
	 */
	startTime?: string;
	/**
	 * 结束时间
	 */
	endTime?: string;
	/**
	 * 订单号
	 */
	shopList?: HighSyncShopItem[];
	[k: string]: any;
}
/**
 * ReqUpdateTradeOccupiedStockDTO :ReqUpdateTradeOccupiedStockDTO
 */
export interface TradeUpdateTradeOccupiedStockRequest {
	/**
	 * OCCUPIED_STOCK-请求来源
	 * STOCK_IN
	 *
	 */
	syncFrom?: string;
	[k: string]: any;
}

/**
 * ResponseBody<String> :ResponseBody
 */
export interface TradeUpdateTradeOccupiedStockResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
}

/**
 * ResponseBody<List<SyncShopResultVO>> :ResponseBody
 */
export interface TradeUserHighSyncShopResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * SyncShopResultVO
	 */
	data?: {
		userId?: number;
		sellerId?: number;
		sellerNick?: string;
		traceId?: string;
		errorMsg?: string;
		platform?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * HighSyncTraceIdDTO :HighSyncTraceIdDTO
 */
export interface TradeUserGetShopSyncResultRequest {
	/**
	 * HighSyncTraceIdReq
	 */
	list?: {
		traceId?: string;
		sellerId?: number;
		platform?: string;
		userId?: number;
		sellerNick?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
 * ReqGetUpdateTradeOccupiedStockProgressDTO :ReqGetUpdateTradeOccupiedStockProgressDTO
 */
export interface TradeGetUpdateTradeOccupiedStockProgressRequest {
	tradeId?: string;
	[k: string]: any;
}

/**
 * ResponseBody<List<ShopSyncConfigResultVo>> :ResponseBody
 */
export interface TradeUserGetShopSyncResultResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ShopSyncConfigResultVo
	 */
	data?: IShopSyncResultItem[];
	[k: string]: any;
}
/**
 * ResponseBody<AsyncResult> :ResponseBody
 */
export interface TradeGetUpdateTradeOccupiedStockProgressResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * AsyncResult
	 */
	data?: {
		progress?: string;
		errorMsg?: string;
		success?: boolean;
		/**
		 * Object
		 */
		result?: {
			[k: string]: any;
		};
		[k: string]: any;
	};
	[k: string]: any;
}

export interface IShopSyncResultItem {
	/**
	 * 平台类型
	 */
	platForm?: string;
	/**
	 * 店铺Id
	 */
	sellerId?: number;
	/**
	 * 店铺名称
	 */
	shopName?: string;
	/**
	 * 同步进度条
	 */
	progress?: number;
	[k: string]: any;
}

export interface TradeSearchConditionConfig {
	id: number;
	condition: string;
	conditionName: string;
	sort: number;
	disabled?: boolean;
	selected: number | boolean;
}

export interface AddressSelectValue {
	selValue: string;
	areaContain: number;
	areaJson: string;
	name: string;
	city?: string;
	id?: string;
}

export enum SysMemoModeEnum {
	"线下备注" = "0",
	"有线下备注" = "1",
	"无线下备注" = "2",
}
export interface TradeQueryGatherParams {
	waveNo?: string;
	allPrintStatus: any;
	/**
	 * 平台 店铺
	 */
	platformInfo?: {
		// multiShops?:{
		// 	platform: platform;
		// 	plat_sellerId: string;
		// }[]
		plats?: platform[];
		plat_sellerIds?: string[];
		plat?: platform;
		shopId?: string;
	};
	sysMemoMode?: SysMemoModeEnum;
	sysMemoList?: any;
	/**
	 * 筛选时间类型1下单时间2付款时间3打印时间4发货时间
	 */
	timeType?: string;
	/**
	 * 订单状态ORDER_WAIT_PAY等待付款ORDER_PAID已付款ORDER_SHIPPED已发货,ORDER_SHIPPED_LITTLE部分发货ORDER_COMPLETED交易完成ALL_STATUS全部订单
	 */
	status?: string;
	sysStatus?: string;
	goodStockStatus?: string;

	/**
	 * 打印状态2仅快递单已打印3仅发货单已打印4均已打印0均未打印
	 */
	/**
	 * 1有买家留言2有卖家备注3有买家留言+有卖家备注4没有留言且没有备注5红6黄7绿8蓝色9紫色
	 */
	flagValue?: string;
	/**
	 * 留言内容，英文逗号分隔，最多支持10条，不得超过100个字符
	 */
	buyerMessage?: string;
	/**
	 * 卖家留言备注内容，英文逗号分隔，最多支持10条，不得超过100个字符
	 */
	buyerMessageOrSellerMemo?: string;
	/**
	 * 备注内容，英文逗号分隔，最多支持10条，不得超过100个字符
	 */
	sellerMemo?: string;
	/**
	 * 快捷查询,合并订单：merge;非合并订单no_merge有运费订单fare;无运费订单no_fare;,乡镇订单town
	 */
	quickQuery?: string;
	/**
	 * 包含宝贝名称编码
	 */
	shortNameIncluding?: string;
	/**
	 * 规格名称规格别名查询
	 */
	skuIncluding?: string;
	// * 新版宝贝名称编码和规格名称规格别名查询
	shortNameIncludingList?: string[];
	skuIncludingList?: string[];
	// * 新版2.0不包含宝贝名称编码和规格名称规格别名查询
	shortNameNotIncludingList?: string[];
	skuNotIncludingList?: string[];
	/**
	 * 收件人
	 */
	buyerNick?: string;
	/**
	 * 订单编号
	 */
	tid?: string;
	/**
	 * 系统编号
	 */
	ptTid?: string;
	/**
	 * 运单号
	 */
	sid?: string;
	/**
	 * {@codetradeQuery.getSellAttribute},查询销售属性,1:1种销售属性且1件,2:1种销售属性多件,3:1种商品且多件,4:多种销售规格
	 */
	sellAttribute?: string;
	sellAttributeList?: string[];
	/**
	 * 0快递1物流
	 */
	logisticsType?: string;
	/**
	 * 0：没有退款1：有退款
	 */
	refundStatus?: string;

	searchTime?: any[];
	goodsIncludeStatus?: string;
	// * 新版2.0不包含
	goodsNotIncludeStatus?: string;
	receiveName?: string;
	mobile?: string;
	printStatus?: string;
	fhdPrintStatus?: string;
	labelPrintStatus?: string;
	addressInfo?: AddressSelectValue;
	precise?: string[];
	marketIncludingList?: string[];
	dangKouIncludingList?: string[];
	supplierIncludingList?: string[];
	authorIncludingList?: string[];
	dangKouNoIncludingList?: string[];
	supplierNoIncludingList?: string[];
	marketNoIncludingList?: string[];
	tradeLabelList?: string[];
	tradeExceptionList?: string[];
	bizMarkObj?: {
		value?: string;
		bizMark?: string;
		bizMarkContain?: number;
	};
	// range?: TradeRangeSelectValue;
	weightRange?: number[];
	paymentRange?: number[];
	goodsTotalNumRange?: number[];
	goodsTypeNumRange?: number[];
	orderRange?: number[];
	customizeResidueSendTime?: string;
	serviePromiseType?: string | number;
	exceptionFlag?: string | number;
	tradeExpressImportLogSequence?: string;
	/**
	 * true 货品包含
	 * false 货品不包含
	 */
	goodsContain?: boolean;
	/**
	 * 货品简称/编码
	 */
	goodsAliasOrIdStr?: string;
	/**
	 * 货品规格名称/编码/别名
	 */
	goodsSkuNameOrIdOrAliasStr?: string;

	distributorUserIds?: any[];
	/**
	 * 订单来源: platfromOrder 平台订单
	 * handOrder 手工订单
	 */
	orderSource?: string;
	tradeType?: string;
	/**
	 * 颜色尺码
	 */
	colorIncludingList?: string;
	sizeIncludingList?: string;
	includeIngColorOrSize?: boolean;
	isColorAndSizePrecise?: boolean;
	includeIngColorOrSizeTwo?: any;

	sellerFlag?: string[];
	smartExpressTemplateId?: string;
	selectTimeType?: any;
	storageIdList?: any;
	labelIdList?: string;
}

/**
 * PrintFlagConfigDTO :PrintFlagConfigDTO
 */
export interface TradeUserMarkLabelItem {
	id?: number;
	/**
	 * @mbg.generated是否包含0不包含1包含,表字段:is_contains
	 */
	isContains?: number;
	/**
	 * @mbg.generated11111表示红黄绿蓝紫1就标示选了这位,0标示没选这位,表字段:comment
	 */
	comment?: string;

	remark?: string;
}

/**
 * UserAdvancedSearchSaveRequest :UserAdvancedSearchSaveRequest
 */
export interface TradeAdvancedSearchSaveOrUpdateRequest {
	id?: number | string;
	/**
	 * 查询规则条件名称
	 */
	ruleName?: string;
	/**
	 * 排序
	 */
	sort?: number;
	/**
	 * 是否可见
	 */
	isVisible?: number;
	/**
	 * 查询规则Json格式
	 */
	ruleContent?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeAdvancedSearchSaveOrUpdateResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * UserAdvancedSearchDeleteRequest :UserAdvancedSearchDeleteRequest
 */
export interface TradeAdvancedSearchDeleteByIdRequest {
	searchId?: number;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeAdvancedSearchDeleteByIdResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ResponseBody<List<UserAdvancedSearchResponseDTO>> :ResponseBody
 */
export interface TradeAdvancedSearchGetListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * UserAdvancedSearchResponseDTO
	 */
	data?: {
		id?: string;
		/**
		 * 查询规则条件名称
		 */
		ruleName?: string;
		/**
		 * 排序
		 */
		sort?: number;
		/**
		 * 是否可见
		 */
		isVisible?: number;
		/**
		 * 查询规则Json格式
		 */
		ruleContent?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
 * UserAdvancedSearchQueryRequest :UserAdvancedSearchQueryRequest
 */
export interface TradeAdvancedSearchGetByIdRequest {
	searchId?: number;
	[k: string]: any;
}

/**
 * ResponseBody<UserAdvancedSearchResponseDTO> :ResponseBody
 */
export interface TradeAdvancedSearchGetByIdResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * UserAdvancedSearchResponseDTO
	 */
	data?: {
		id?: number;
		/**
		 * 查询规则条件名称
		 */
		ruleName?: string;
		/**
		 * 排序
		 */
		sort?: number;
		/**
		 * 是否可见
		 */
		isVisible?: number;
		/**
		 * 查询规则Json格式
		 */
		ruleContent?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * UserAdvancedSearchSwapSortDTO :UserAdvancedSearchSwapSortDTO
 */
export interface TradeAdvancedSearchSwapSortRequest {
	swapId1?: number;
	swapId2?: number;
	[k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeAdvancedSearchSwapSortResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * 剩余发货时间自定义接口
 */
export interface TradeDictInsertDictRequest {
	userDictEnum: string;
	value: string;
}

export interface TradeDictInsertDictResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	data?: number | null | string;
}

export interface TradeDictQueryDictRequest {
	userDictEnum: string;
}

export interface TradeDictQueryDictResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	data?: {
		value?: string;
		[k: string]: any;
	};
}

export interface TradeDictDelDictRequest {
	userDictEnum: string;
	value: string;
}

export interface TradeDictDelDictResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	data?: number | null | string;
}

export interface TradeItemTopQueryRequest {
	/**
	 * 搜索类型,默认：平台商品-按商品,0：平台商品-按商品,1：平台商品-按款,2：货品-按商品,3：货品-按款
	 */
	queryType?: number;
	/**
	 * 按款时，商品搜索
	 */
	content?: string;
	/**
	 * 按款搜索时，输入的商品信息,平台商品：传入numIid,货品：传入sysItemId ,ItemQueryInfo
	 */
	items?: {
		/**
		 * TB,淘宝,,TM,天猫,,PDD,拼多多,,FXG,抖音,,HAND,手工单;
		 */
		platform?: string;
		/**
		 * 店铺id
		 */
		sellerId?: string;
		/**
		 * 平台商品Id
		 */
		numIid?: string;
		/**
		 * 系统商品Id
		 */
		sysItemId?: number;
		[k: string]: any;
	}[];
	topItemShow?: string;
	topSkuShow?: string;
	/**
	 * 商品按款
	 */
	numIIds?: string;
	/**
	 * 货品按款
	 */
	sysItemIds?: string;
	/**
	 * 按款,商品名称,废弃 ,String
	 */
	itemNames?: string[];
	refresh?: number;
	pageNo?: number;
	pageSize?: number;
	[k: string]: any;
}
export interface TradeItemTopQueryResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemTopQueryVo
	 */
	data?: {
		/**
		 * 商品信息,只有在按款的状态下返回 ,TopItem
		 */
		topItems?: {
			/**
			 * TB,淘宝,,TM,天猫,,PDD,拼多多,,FXG,抖音,,HAND,手工单;
			 */
			platform?: string;
			/**
			 * 店铺名称
			 */
			shopName?: string;
			/**
			 * 店铺id
			 */
			sellerId?: number;
			/**
			 * 平台商品id
			 */
			numIid?: string;
			/**
			 * 货品id
			 */
			sysItemId?: number;
			/**
			 * 商品标题
			 */
			title?: string;
			/**
			 * 货品名称
			 */
			sysItemName?: string;
			/**
			 * 订单数量
			 */
			tradeNum?: number;
			/**
			 * 商品数量
			 */
			itemNum?: number;
			[k: string]: any;
		}[];
		/**
		 * 规格商品信息,平台商品-按商品,平台商品-按款的规格搜索,货品-按商品,货品-按款的规格搜索 ,TopSku
		 */
		topSkus?: {
			/**
			 * TB,淘宝,,TM,天猫,,PDD,拼多多,,FXG,抖音,,HAND,手工单;
			 */
			platform?: string;
			/**
			 * 店铺id
			 */
			sellerId?: number;
			/**
			 * 店铺名称
			 */
			shopName?: string;
			/**
			 * 平台商品id
			 */
			numIid?: string;
			/**
			 * 平台商品规格
			 */
			skuId?: string;
			/**
			 * 商品标题
			 */
			title?: string;
			/**
			 * 规格名称
			 */
			skuName?: string;
			/**
			 * 规格图片
			 */
			picUrl?: string;
			/**
			 * 货品id
			 */
			sysItemId?: number;
			/**
			 * 货品规格id
			 */
			sysSkuId?: number;
			/**
			 * 货品名称
			 */
			sysItemName?: string;
			/**
			 * 货品规格名称
			 */
			sysSkuName?: string;
			/**
			 * 货品规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 货品规格商家编码
			 */
			skuOuterId?: string;
			/**
			 * 货品规格图片
			 */
			sysPicUrl?: string;
			/**
			 * 订单数量
			 */
			tradeNum?: number;
			/**
			 * 商品数量
			 */
			itemNum?: number;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

export interface TradeItemTopTotalResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * 总量
	 */
	data?: string;
	[k: string]: any;
}

/**
 * UpdateTradeOccupiedRequest :UpdateTradeOccupiedRequest
 */
export interface TradeCheckTradeOccupiedRequestRequest {
	/**
	 * 货品规格编码 ,String
	 */
	skuOuterIdList: string[];
	[k: string]: any;
}

/**
 * data
 */
export type Null = string;

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeCheckTradeOccupiedRequestResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	data?: Null;
	[k: string]: any;
}

/**
 * PreConditionCustomSortDTO :PreConditionCustomSortDTO
 */
export interface TradeAdvancedSearchBatchUpdateSortRequest {
	/**
	 * 需要变更的排序列表 ,UserAdvancedSearchResponseDTO
	 */
	newSortDtoList?: {
		id: number;
		/**
		 * 排序
		 */
		sort: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeAdvancedSearchBatchUpdateSortResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * WeighingRecordDTO :WeighingRecordDTO
 */
export interface TradeSaveWeighingRecordRequest {
	/**
	 * 底单称重记录ID
	 */
	id?: number;
	/**
	 * 称重重量,切换单位转换为g的值
	 */
	weight?: string;
	/**
	 * 快递单号
	 */
	exNumber?: string;
	/**
	 * 包裹称重场景0:包裹称重1:称重发货
	 */
	weightShipFlag?: number;
	/**
	 * 订单id串值(tid:oid,oid|tid1|tid2)
	 */
	orderIds?: string;
	[k: string]: any;
}
/**
 * ResponseBody<String> :ResponseBody
 */
export interface TradeSaveWeighingRecordResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
}
/**
 * WeighingRecordDTO :WeighingRecordDTO
 */
export interface TradeUpdateWeighingRecordRequest {
	/**
	 * 底单称重记录ID
	 */
	id?: string;
	/**
	 * 称重重量,切换单位转换为g的值
	 */
	weight?: string;
	/**
	 * 快递单号
	 */
	exNumber?: string;
	/**
	 * 包裹称重场景0:包裹称重1:称重发货
	 */
	weightShipFlag?: number;
	/**
	 * 订单id串值(tid:oid,oid|tid1|tid2)
	 */
	orderIds?: string;
	[k: string]: any;
}
/**
 * ResponseBody<String> :ResponseBody
 */
export interface TradeUpdateWeighingRecordResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
}

/**
 * ItemUserConfigRequest :ItemUserConfigRequest
 */
export interface ItemItemUserConfigQueryConfigRequest {
	/**
	 * 自增id
	 */
	id?: number;
	/**
	 * 配置业务类型枚举,STOCK_DYNAMIC_WARN_RULE("stock_dynamic_warn_rule","库存动态预警规则")
	 */
	itemUserConfigBizEnum?: any;
	/**
	 * 字典值的info对象,表字段:value
	 */
	value?: string;
	[k: string]: any;
}

/**
 * ResponseBody<List<ItemUserConfigDTO>> :ResponseBody
 */
export interface ItemItemUserConfigQueryConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemUserConfigDTO
	 */
	data?: {
		/**
		 * 自增id,表字段:id
		 */
		id?: number;
		/**
		 * 创建时间,表字段:gmt_created
		 */
		gmtCreated?: string;
		/**
		 * 更新时间,表字段:gmt_modified
		 */
		gmtModified?: string;
		/**
		 * 逻辑删除,表字段:enable_status
		 */
		enableStatus?: number;
		/**
		 * 系统用户子账号id,表字段:user_id
		 */
		userId?: number;
		/**
		 * 子账号用户id,表字段:sub_user_id
		 */
		subUserId?: number;
		/**
		 * 业务类型,表字段:biz
		 */
		biz?: string;
		/**
		 * 字典值,表字段:value
		 */
		value?: string;
		/**
		 * 字典值的info对象,表字段:value ,Object
		 */
		valueInfo?: {
			[k: string]: any;
		};
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * ItemUserConfigRequest :ItemUserConfigRequest
 */
export interface ItemItemUserConfigUpdateConfigRequest {
	/**
	 * 自增id
	 */
	id?: number;
	/**
	 * 配置业务类型枚举,STOCK_DYNAMIC_WARN_RULE("stock_dynamic_warn_rule","库存动态预警规则")
	 */
	itemUserConfigBizEnum?: any;
	/**
	 * 字典值的info对象,表字段:value
	 */
	value?: string;
	[k: string]: any;
}

/**
 * ResponseBody<List<ItemUserConfigDTO>> :ResponseBody
 */
export interface ItemItemUserConfigUpdateConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemUserConfigDTO
	 */
	data?: {
		/**
		 * 自增id,表字段:id
		 */
		id?: number;
		/**
		 * 创建时间,表字段:gmt_created
		 */
		gmtCreated?: string;
		/**
		 * 更新时间,表字段:gmt_modified
		 */
		gmtModified?: string;
		/**
		 * 逻辑删除,表字段:enable_status
		 */
		enableStatus?: number;
		/**
		 * 系统用户子账号id,表字段:user_id
		 */
		userId?: number;
		/**
		 * 子账号用户id,表字段:sub_user_id
		 */
		subUserId?: number;
		/**
		 * 业务类型,表字段:biz
		 */
		biz?: string;
		/**
		 * 字典值,表字段:value
		 */
		value?: string;
		/**
		 * 字典值的info对象,表字段:value ,Object
		 */
		valueInfo?: {
			[k: string]: any;
		};
		[k: string]: any;
	}[];
	[k: string]: any;
}

export interface TradeAdvancedSearchGetListNewRequest {
	/**
	 * 业务类型 PD :1 批打	REFUND_LIST_QUERY :2 售后单查询
	 * "PD" | "REFUND_LIST_QUERY" | "BHD_LIST_QUERY" | "BHD_LABEL_QUERY"
	 */
	bizTypeEnum?: any;
	[k: string]: any;
}
export interface TradeAdvancedSearchGetListNewResponse {
	/**
	 * 是否成功
	 */
	success?: boolean;
	/**
	 * 返回码
	 */
	errorCode?: number;
	/**
	 * 返回消息
	 */
	errorMessage?: string;
	/**
	 * 返回数据
	 */
	data?: {
		/**
		 * 快捷搜索列表
		 */
		advanceList?: {
			id?: string;
			/**
			 * 查询规则条件名称
			 */
			ruleName?: string;
			/**
			 * 排序
			 */
			sort?: number;
			/**
			 * 是否可见
			 */
			isVisible?: number;
			/**
			 * 查询规则Json格式
			 */
			ruleContent?: string;
			/**
			 * 业务类型
			 */
			bizTypeEnum?: "PD" | "REFUND_LIST_QUERY";
			/**
			 * 分组id
			 */
			groupId?: string;
			[k: string]: any;
		}[];
		id?: string;
		groupName?: string;
		sort?: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}

export interface UserSettingGroupSaveOrUpdateRequest {
	/**
	 * 当前分组的id
	 */
	id?: number;
	/**
	 * 分组名
	 */
	groupName?: string;
	/**
	 * 业务类型
	 */
	bizType?: string;
	[k: string]: any;
}

export interface UserSettingGroupSaveOrUpdateResponse {
	/**
	 * 是否成功
	 */
	success?: boolean;
	/**
	 * 返回码
	 */
	errorCode?: number;
	/**
	 * 返回消息
	 */
	errorMessage?: string;
	/**
	 * 返回数据
	 */
	data?: null;
	[k: string]: any;
}

export interface UserSettingGroupDeleteByIdRequest {
	/**
	 * 当前分组的id
	 */
	id?: number;
	/**
	 * 分组名
	 */
	groupName?: string;
	/**
	 * 业务类型
	 */
	bizType?: string;
	[k: string]: any;
}

export interface UserSettingGroupDeleteByIdResponse {
	/**
	 * 是否成功
	 */
	success?: boolean;
	/**
	 * 返回码
	 */
	errorCode?: number;
	/**
	 * 返回消息
	 */
	errorMessage?: string;
	/**
	 * 返回数据
	 */
	data?: null;
	[k: string]: any;
}
