import { OccupyErrorTypeEnum, WaybillNoSource } from "@/pages/Trade/constants";
import { IGroupRelationRecordList, IPackage } from "@/pages/Trade/interface";
import { TradeSearchConditionConfig } from "../search/search";
import { TradeOptEnum } from "@/utils/enum/trade";

/**
 * SelectRefundFastReqDto :SelectRefundFastReqDto
 */
export interface FastRefundSelectRefundByPtTidListRequest {
  /**
   * String
   */
  ptTidList?: string[];
  [k: string]: any;
}

/**
 * ResponseBody<List<RspRefundFastPtTidDTO>> :ResponseBody
 */
export interface FastRefundSelectRefundByPtTidListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspRefundFastPtTidDTO
   */
  data?: {
    /**
     * 物流预警的id（前端传）
     */
    id?: number;
    /**
     * ptTid（前端传）
     */
    ptTid?: string;
    /**
     * 分组键（后端用）
     */
    groupKey?: string;
    /**
     * 运单号(回收单号用)（前端传）
     */
    waybillNo?: string;
    /**
     * 快递公司code(回收单号用)（前端传）
     */
    exCode?: string;
    /**
     * 手机号，sf物流查询用（前端传）
     */
    tel?: string;
    /**
     * RspRefundFastDTO
     */
    refundFastDTOList?: {
      /**
       * sysSkuIdList,极速退货入库业务用 ,String
       */
      sysSkuIdList?: string[];
      /**
       * 分组key,极速退货入库业务用
       */
      groupKey?: string;
      /**
       * 退款单我们这边的唯一id
       */
      id?: string;
      /**
       * 平台TB、TM（天猫待定）、PDD、FXG（抖店）、HAND（手工单）
       */
      platform?: string;
      /**
       * 买家昵称
       */
      buyerNick?: string;
      /**
       * 买家唯一标识
       */
      buyerOpenUid?: string;
      /**
       * 物流公司名称
       */
      companyName?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * tid
       */
      tid?: string;
      /**
       * oid
       */
      oid?: string;
      /**
       * 退款编号
       */
      refundId?: string;
      /**
       * 退款创建时间
       */
      refundCreated?: string;
      payTime?: string;
      /**
       * 退款更新时间
       */
      refundModified?: string;
      /**
       * 退款金额
       */
      refundFee?: string;
      /**
       * 订单实付金额
       */
      payment?: string;
      /**
       * 邮费,表字段:post_fee
       */
      postFee?: string;
      /**
       * 订单总金额
       */
      totalFee?: string;
      receiverName?: string;
      /**
       * 收件人手机号,表字段:receiver_mobile
       */
      receiverMobile?: string;
      /**
       * 收件人省,表字段:receiver_state
       */
      receiverState?: string;
      /**
       * 收件人市,表字段:receiver_city
       */
      receiverCity?: string;
      /**
       * 收件人区,表字段:receiver_district
       */
      receiverDistrict?: string;
      /**
       * 收件人详细地址,表字段:receiver_address
       */
      receiverAddress?: string;
      /**
       * 收件人姓名密文,表字段:encode_receiver_name
       */
      encodeReceiverName?: string;
      /**
       * 收件人手机号密文,表字段:encode_receiver_mobile
       */
      encodeReceiverMobile?: string;
      /**
       * 收件人详细地址密文,表字段:encode_receiver_address
       */
      encodeReceiverAddress?: string;
      /**
       * 换货收件人省
       */
      exchangeReceiverProvince?: string;
      /**
       * 换货收件人市
       */
      exchangeReceiverCity?: string;
      /**
       * 换货收件人区
       */
      exchangeReceiverTown?: string;
      /**
       * 换货收件人详细地址脱敏
       */
      exchangeReceiverAddressMask?: string;
      /**
       * 换货收件人姓名脱敏
       */
      exchangeReceiverNameMask?: string;
      /**
       * 换货收件人手机号脱敏,exchange_receiver_mobile_mask
       */
      exchangeReceiverMobileMask?: string;
      /**
       * 售后类型,空全部,1仅退款,2退货退款,3换货,4补差价,5补发货品
       */
      afterSaleType?: number;
      /**
       * 退款原因
       */
      refundReason?: string;
      /**
       * 退款说明
       */
      desc?: string;
      /**
       * erp备注
       */
      erpMemo?: string;
      /**
       * 退款状态,1等待卖家同意,2等待买家退货,3等待卖家确认收货,4退款成功,5退款关闭,6卖家拒绝退款
       */
      refundStatus?: number;
      /**
       * 退款状态说明
       */
      refundStatusDesc?: string;
      /**
       * 订单交易状态
       */
      orderStatus?: string;
      /**
       * 订单交易状态说明
       */
      orderStatusDesc?: string;
      /**
       * 卖方备注
       */
      sellerMemo?: string;
      /**
       * 买家旗帜
       */
      sellerFlag?: string;
      /**
       * 插旗标签
       */
      sellerFlagTag?: string;
      /**
       * 买家留言
       */
      buyerMessage?: string;
      /**
       * 退货运单号
       */
      sid?: string;
      /**
       * 发货运单号
       */
      invoiceNo?: string;
      /**
       * 售后渠道,1线上同步,2手工录入
       */
      afterSaleChannel?: number;
      /**
       * 异常类型,0无异常,1异常,2忽略异常
       */
      exceptionType?: number;
      /**
       * 审核状态
       */
      reviewStatus?: boolean;
      /**
       * 淘宝商家类型0-淘宝1-天猫
       */
      tbSellerType?: number;
      /**
       * 订单来源HAND：手工单
       */
      source?: string;
      /**
       * 工单类型,RefundSystemTypeEnum,ONLY_REFUND(1,"仅退款"),REFUSE_BACK_GOODS(2,"拒收退货"),RETURN_AND_REFUND(3,"退货退款"),EXCHANGE_ITEM(4,"换货"),REPLENISHMENT(5,"补发")
       */
      refundSystemType?: number;
      /**
       * 是否分销商推送
       */
      isDistributorUserPushRefund?: boolean;
      /**
       * 线下备注
       */
      localContent?: string;
      /**
       * 平台订单编号
       */
      ptTid?: string;
      /**
       * 平台子订单编号
       */
      ptOid?: string;
      /**
       * 运费（快手退款运费）
       */
      freight?: string;
      /**
       * 售后单来源
       */
      afterSaleSource?: string;
      /**
       * 线下备注图片 ,String
       */
      localContentPicList?: string[];
      /**
       * 售后单扩展信息 ,RefundBizExtendInfoDTO
       */
      refundBizExtendInfo?: {
        /**
         * 线下备注图片，逗号分割
         */
        localContentPic?: string;
        /**
         * 是否使用换货补发新地址1-新地址0-订单原地址
         */
        useNewExchangeAddress?: number;
        [k: string]: any;
      };
      /**
       * 退款货品信息 ,RspRefundFastItemRecordInfo
       */
      refundItemRecordInfos?: {
        /**
         * 子订单id
         */
        oid?: string;
        /**
         * 平台子订单编号
         */
        ptOid?: string;
        /**
         * tid
         */
        tid?: string;
        /**
         * 平台订单编号
         */
        ptTid?: string;
        /**
         * 商品refund_item_record表唯一id
         */
        itemRefundItemRecordId?: number;
        /**
         * 申请退款的商品数字编号申请退款的商品数字编号
         */
        numIid?: string;
        /**
         * 商品规格id
         */
        skuId?: string;
        /**
         * 商品图片链接
         */
        picUrl?: string;
        /**
         * 商品外部商家编码
         */
        outerId?: string;
        /**
         * 商品规格编码
         */
        outerSkuId?: string;
        /**
         * 商品货号
         */
        itemNo?: string;
        /**
         * 商品标题
         */
        title?: string;
        /**
         * 规格，颜色
         */
        skuName?: string;
        /**
         * 货品refund_item_record表唯一id
         */
        sysRefundItemRecordId?: number;
        /**
         * 申请退款的商品数字编号申请退款的商品数字编号
         */
        sysItemId?: number;
        /**
         * 图片链接
         */
        sysPicUrl?: string;
        /**
         * 货品商品标题
         */
        sysTitle?: string;
        /**
         * 货品简称
         */
        sysItemAlias?: string;
        /**
         * 货品商品外部商家编码
         */
        sysOuterId?: string;
        /**
         * 货品规格id
         */
        sysSkuId?: string;
        /**
         * 货品规格别名
         */
        sysSkuAlias?: string;
        /**
         * 货品sku商家编码
         */
        sysOuterSkuId?: string;
        /**
         * 货品商品货号
         */
        sysItemNo?: string;
        /**
         * 规格，颜色
         */
        sysSkuName?: string;
        /**
         * 市场
         */
        market?: string;
        /**
         * 档口
         */
        stall?: string;
        /**
         * 供应商id
         */
        supplierId?: number;
        /**
         * 供应商名称
         */
        supplierName?: string;
        /**
         * 成本价
         */
        costPrice?: string;
        /**
         * 售后金额
         */
        price?: string;
        /**
         * 订单对应货品数量
         */
        num?: number;
        /**
         * 申请退货数量
         */
        applyRefundNum?: number;
        /**
         * 已收货数量
         */
        hasRefundNum?: number;
        /**
         * 已处理数量
         */
        hasDisposeNum?: number;
        /**
         * 创建时间
         */
        createTime?: string;
        /**
         * 是否为组合商品，1:组合货品,0:非组合货品
         */
        isCombination?: number;
        /**
         * 商品类型
         */
        refundItemType?: number;
        /**
         * 商品类型-货品
         */
        sysRefundItemType?: number;
        /**
         * 商品来源
         */
        refundItemSource?: number;
        /**
         * 商品来源-货品
         */
        sysRefundItemSource?: number;
        /**
         * sys_refund_item_is_need_filter,货品是否需被过滤
         */
        sysRefundItemIsNeedFilter?: boolean;
        /**
         * 异常类型,0无异常,1异常,2忽略异常
         */
        exceptionType?: number;
        /**
         * 货品信息 ,RefundItemRecordDTO
         */
        originalRefundSysItemRecordDTO?: {
          /**
           * 表字段:id
           */
          id?: number;
          /**
           * 用户ID,表字段:user_id
           */
          userId?: number;
          /**
           * 退款id,表字段:refund_info_id
           */
          refundInfoId?: number;
          /**
           * 逻辑删除,表字段:enable_status
           */
          enableStatus?: boolean;
          /**
           * 记录创建时间,表字段:gmt_create
           */
          gmtCreate?: string;
          /**
           * 记录更新时间,表字段:gmt_modified
           */
          gmtModified?: string;
          /**
           * 店铺ID,表字段:seller_id
           */
          sellerId?: string;
          /**
           * 申请退款的数量,表字段:apply_refund_num
           */
          applyRefundNum?: number;
          /**
           * 平台退款状态,表字段:platform_refund_status
           */
          platformRefundStatus?: string;
          /**
           * 商品图片路径,表字段:pic_url
           */
          picUrl?: string;
          /**
           * 商品退款状态,表字段:refund_item_status
           */
          refundItemStatus?: string;
          /**
           * 商品标题,表字段:item_title
           */
          itemTitle?: string;
          /**
           * 规格，颜色,表字段:sku_info
           */
          skuInfo?: string;
          /**
           * 商品规格Id,表字段:sku_id
           */
          skuId?: string;
          /**
           * 商品简称,表字段:item_alias
           */
          itemAlias?: string;
          /**
           * 规格别名,表字段:sku_alias
           */
          skuAlias?: string;
          /**
           * 商品外部商家编码,表字段:outer_id
           */
          outerId?: string;
          /**
           * 外部网店自己定义的Sku编号,表字段:outer_sku_id
           */
          outerSkuId?: string;
          /**
           * 商品数量,表字段:num
           */
          num?: number;
          /**
           * 申请退款的商品数字编号申请退款的商品数字编号,表字段:num_iid
           */
          numIid?: string;
          /**
           * 单价,表字段:price
           */
          price?: string;
          /**
           * 订单编号（阿里改为子订单编号）,表字段:tid
           */
          tid?: string;
          /**
           * 已收货数量,表字段:has_refund_num
           */
          hasRefundNum?: number;
          /**
           * 已处理数量,表字段:has_dispose_num
           */
          hasDisposeNum?: number;
          /**
           * 属于1平台2我们自己的,表字段:belong
           */
          belong?: number;
          /**
           * {处理方式:数量},表字段:handle_type
           */
          handleType?: string;
          /**
           * 货号,表字段:item_no
           */
          itemNo?: string;
          /**
           * 是否组合货品1是0不是,表字段:is_combination
           */
          isCombination?: number;
          /**
           * 扫描收货数量
           */
          confirmNum?: number;
          /**
           * 商品类型
           */
          refundItemType?: number;
          /**
           * 商品来源0-平台快照1-手动添加
           */
          refundItemSource?: number;
          /**
           * 货品关联商品规格id
           */
          relateSkuId?: string;
          /**
           * oid
           */
          oid?: string;
          /**
           * 本次需处理售后数量
           */
          currentRefundNum?: number;
          /**
           * 成本价,表字段:cost_price
           */
          costPrice?: string;
          /**
           * 平台订单编号
           */
          ptTid?: string;
          /**
           * 平台子订单编号
           */
          ptOid?: string;
          /**
           * 平台
           */
          platform?: string;
          /**
           * 退款id
           */
          refundId?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 是否删除，同步用
           */
          toDelete?: boolean;
          /**
           * 原售后商品明细id-售后扫描用
           */
          originalRefundItemId?: number;
          /**
           * 原售后货品明细id-售后扫描用
           */
          originalSysRefundItemId?: number;
          /**
           * 编辑类型
           */
          editItemType?: {
            [k: string]: any;
          };
          [k: string]: any;
        };
        [k: string]: any;
      }[];
      /**
       * 仓库id
       */
      storageId?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * FastRefundInStockDto :FastRefundInStockDto
 */
export interface FastRefundFastRefundInStockRequest {
  /**
   * 售后单列表(tid维度) ,RspRefundFastPtTidDTO
   */
  refundFastPtTidDTOList?: {
    /**
     * 物流预警的id（前端传）
     */
    id?: number;
    /**
     * ptTid（前端传）
     */
    ptTid?: string;
    /**
     * 分组键（后端用）
     */
    groupKey?: string;
    /**
     * 运单号(回收单号用)（前端传）
     */
    waybillNo?: string;
    /**
     * 快递公司code(回收单号用)（前端传）
     */
    exCode?: string;
    /**
     * 手机号，sf物流查询用（前端传）
     */
    tel?: string;
    /**
     * RspRefundFastDTO
     */
    refundFastDTOList?: {
      /**
       * sysSkuIdList,极速退货入库业务用 ,String
       */
      sysSkuIdList?: string[];
      /**
       * 分组key,极速退货入库业务用
       */
      groupKey?: string;
      /**
       * 退款单我们这边的唯一id
       */
      id?: string;
      /**
       * 平台TB、TM（天猫待定）、PDD、FXG（抖店）、HAND（手工单）
       */
      platform?: string;
      /**
       * 买家昵称
       */
      buyerNick?: string;
      /**
       * 买家唯一标识
       */
      buyerOpenUid?: string;
      /**
       * 物流公司名称
       */
      companyName?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * tid
       */
      tid?: string;
      /**
       * oid
       */
      oid?: string;
      /**
       * 退款编号
       */
      refundId?: string;
      /**
       * 退款创建时间
       */
      refundCreated?: string;
      payTime?: string;
      /**
       * 退款更新时间
       */
      refundModified?: string;
      /**
       * 退款金额
       */
      refundFee?: string;
      /**
       * 订单实付金额
       */
      payment?: string;
      /**
       * 邮费,表字段:post_fee
       */
      postFee?: string;
      /**
       * 订单总金额
       */
      totalFee?: string;
      receiverName?: string;
      /**
       * 收件人手机号,表字段:receiver_mobile
       */
      receiverMobile?: string;
      /**
       * 收件人省,表字段:receiver_state
       */
      receiverState?: string;
      /**
       * 收件人市,表字段:receiver_city
       */
      receiverCity?: string;
      /**
       * 收件人区,表字段:receiver_district
       */
      receiverDistrict?: string;
      /**
       * 收件人详细地址,表字段:receiver_address
       */
      receiverAddress?: string;
      /**
       * 收件人姓名密文,表字段:encode_receiver_name
       */
      encodeReceiverName?: string;
      /**
       * 收件人手机号密文,表字段:encode_receiver_mobile
       */
      encodeReceiverMobile?: string;
      /**
       * 收件人详细地址密文,表字段:encode_receiver_address
       */
      encodeReceiverAddress?: string;
      /**
       * 换货收件人省
       */
      exchangeReceiverProvince?: string;
      /**
       * 换货收件人市
       */
      exchangeReceiverCity?: string;
      /**
       * 换货收件人区
       */
      exchangeReceiverTown?: string;
      /**
       * 换货收件人详细地址脱敏
       */
      exchangeReceiverAddressMask?: string;
      /**
       * 换货收件人姓名脱敏
       */
      exchangeReceiverNameMask?: string;
      /**
       * 换货收件人手机号脱敏,exchange_receiver_mobile_mask
       */
      exchangeReceiverMobileMask?: string;
      /**
       * 售后类型,空全部,1仅退款,2退货退款,3换货,4补差价,5补发货品
       */
      afterSaleType?: number;
      /**
       * 退款原因
       */
      refundReason?: string;
      /**
       * 退款说明
       */
      desc?: string;
      /**
       * erp备注
       */
      erpMemo?: string;
      /**
       * 退款状态,1等待卖家同意,2等待买家退货,3等待卖家确认收货,4退款成功,5退款关闭,6卖家拒绝退款
       */
      refundStatus?: number;
      /**
       * 退款状态说明
       */
      refundStatusDesc?: string;
      /**
       * 订单交易状态
       */
      orderStatus?: string;
      /**
       * 订单交易状态说明
       */
      orderStatusDesc?: string;
      /**
       * 卖方备注
       */
      sellerMemo?: string;
      /**
       * 买家旗帜
       */
      sellerFlag?: string;
      /**
       * 插旗标签
       */
      sellerFlagTag?: string;
      /**
       * 买家留言
       */
      buyerMessage?: string;
      /**
       * 退货运单号
       */
      sid?: string;
      /**
       * 发货运单号
       */
      invoiceNo?: string;
      /**
       * 售后渠道,1线上同步,2手工录入
       */
      afterSaleChannel?: number;
      /**
       * 异常类型,0无异常,1异常,2忽略异常
       */
      exceptionType?: number;
      /**
       * 审核状态
       */
      reviewStatus?: boolean;
      /**
       * 淘宝商家类型0-淘宝1-天猫
       */
      tbSellerType?: number;
      /**
       * 订单来源HAND：手工单
       */
      source?: string;
      /**
       * 工单类型,RefundSystemTypeEnum,ONLY_REFUND(1,"仅退款"),REFUSE_BACK_GOODS(2,"拒收退货"),RETURN_AND_REFUND(3,"退货退款"),EXCHANGE_ITEM(4,"换货"),REPLENISHMENT(5,"补发")
       */
      refundSystemType?: number;
      /**
       * 是否分销商推送
       */
      isDistributorUserPushRefund?: boolean;
      /**
       * 线下备注
       */
      localContent?: string;
      /**
       * 平台订单编号
       */
      ptTid?: string;
      /**
       * 平台子订单编号
       */
      ptOid?: string;
      /**
       * 运费（快手退款运费）
       */
      freight?: string;
      /**
       * 售后单来源
       */
      afterSaleSource?: string;
      /**
       * 线下备注图片 ,String
       */
      localContentPicList?: string[];
      /**
       * 售后单扩展信息 ,RefundBizExtendInfoDTO
       */
      refundBizExtendInfo?: {
        /**
         * 线下备注图片，逗号分割
         */
        localContentPic?: string;
        /**
         * 是否使用换货补发新地址1-新地址0-订单原地址
         */
        useNewExchangeAddress?: number;
        [k: string]: any;
      };
      /**
       * 退款货品信息 ,RspRefundFastItemRecordInfo
       */
      refundItemRecordInfos?: {
        /**
         * 子订单id
         */
        oid?: string;
        /**
         * 平台子订单编号
         */
        ptOid?: string;
        /**
         * tid
         */
        tid?: string;
        /**
         * 平台订单编号
         */
        ptTid?: string;
        /**
         * 商品refund_item_record表唯一id
         */
        itemRefundItemRecordId?: number;
        /**
         * 申请退款的商品数字编号申请退款的商品数字编号
         */
        numIid?: string;
        /**
         * 商品规格id
         */
        skuId?: string;
        /**
         * 商品图片链接
         */
        picUrl?: string;
        /**
         * 商品外部商家编码
         */
        outerId?: string;
        /**
         * 商品规格编码
         */
        outerSkuId?: string;
        /**
         * 商品货号
         */
        itemNo?: string;
        /**
         * 商品标题
         */
        title?: string;
        /**
         * 规格，颜色
         */
        skuName?: string;
        /**
         * 货品refund_item_record表唯一id
         */
        sysRefundItemRecordId?: number;
        /**
         * 申请退款的商品数字编号申请退款的商品数字编号
         */
        sysItemId?: number;
        /**
         * 图片链接
         */
        sysPicUrl?: string;
        /**
         * 货品商品标题
         */
        sysTitle?: string;
        /**
         * 货品简称
         */
        sysItemAlias?: string;
        /**
         * 货品商品外部商家编码
         */
        sysOuterId?: string;
        /**
         * 货品规格id
         */
        sysSkuId?: string;
        /**
         * 货品规格别名
         */
        sysSkuAlias?: string;
        /**
         * 货品sku商家编码
         */
        sysOuterSkuId?: string;
        /**
         * 货品商品货号
         */
        sysItemNo?: string;
        /**
         * 规格，颜色
         */
        sysSkuName?: string;
        /**
         * 市场
         */
        market?: string;
        /**
         * 档口
         */
        stall?: string;
        /**
         * 供应商id
         */
        supplierId?: number;
        /**
         * 供应商名称
         */
        supplierName?: string;
        /**
         * 成本价
         */
        costPrice?: string;
        /**
         * 售后金额
         */
        price?: string;
        /**
         * 订单对应货品数量
         */
        num?: number;
        /**
         * 申请退货数量
         */
        applyRefundNum?: number;
        /**
         * 已收货数量
         */
        hasRefundNum?: number;
        /**
         * 已处理数量
         */
        hasDisposeNum?: number;
        /**
         * 创建时间
         */
        createTime?: string;
        /**
         * 是否为组合商品，1:组合货品,0:非组合货品
         */
        isCombination?: number;
        /**
         * 商品类型
         */
        refundItemType?: number;
        /**
         * 商品类型-货品
         */
        sysRefundItemType?: number;
        /**
         * 商品来源
         */
        refundItemSource?: number;
        /**
         * 商品来源-货品
         */
        sysRefundItemSource?: number;
        /**
         * sys_refund_item_is_need_filter,货品是否需被过滤
         */
        sysRefundItemIsNeedFilter?: boolean;
        /**
         * 异常类型,0无异常,1异常,2忽略异常
         */
        exceptionType?: number;
        /**
         * 货品信息 ,RefundItemRecordDTO
         */
        originalRefundSysItemRecordDTO?: {
          /**
           * 表字段:id
           */
          id?: number;
          /**
           * 用户ID,表字段:user_id
           */
          userId?: number;
          /**
           * 退款id,表字段:refund_info_id
           */
          refundInfoId?: number;
          /**
           * 逻辑删除,表字段:enable_status
           */
          enableStatus?: boolean;
          /**
           * 记录创建时间,表字段:gmt_create
           */
          gmtCreate?: string;
          /**
           * 记录更新时间,表字段:gmt_modified
           */
          gmtModified?: string;
          /**
           * 店铺ID,表字段:seller_id
           */
          sellerId?: string;
          /**
           * 申请退款的数量,表字段:apply_refund_num
           */
          applyRefundNum?: number;
          /**
           * 平台退款状态,表字段:platform_refund_status
           */
          platformRefundStatus?: string;
          /**
           * 商品图片路径,表字段:pic_url
           */
          picUrl?: string;
          /**
           * 商品退款状态,表字段:refund_item_status
           */
          refundItemStatus?: string;
          /**
           * 商品标题,表字段:item_title
           */
          itemTitle?: string;
          /**
           * 规格，颜色,表字段:sku_info
           */
          skuInfo?: string;
          /**
           * 商品规格Id,表字段:sku_id
           */
          skuId?: string;
          /**
           * 商品简称,表字段:item_alias
           */
          itemAlias?: string;
          /**
           * 规格别名,表字段:sku_alias
           */
          skuAlias?: string;
          /**
           * 商品外部商家编码,表字段:outer_id
           */
          outerId?: string;
          /**
           * 外部网店自己定义的Sku编号,表字段:outer_sku_id
           */
          outerSkuId?: string;
          /**
           * 商品数量,表字段:num
           */
          num?: number;
          /**
           * 申请退款的商品数字编号申请退款的商品数字编号,表字段:num_iid
           */
          numIid?: string;
          /**
           * 单价,表字段:price
           */
          price?: string;
          /**
           * 订单编号（阿里改为子订单编号）,表字段:tid
           */
          tid?: string;
          /**
           * 已收货数量,表字段:has_refund_num
           */
          hasRefundNum?: number;
          /**
           * 已处理数量,表字段:has_dispose_num
           */
          hasDisposeNum?: number;
          /**
           * 属于1平台2我们自己的,表字段:belong
           */
          belong?: number;
          /**
           * {处理方式:数量},表字段:handle_type
           */
          handleType?: string;
          /**
           * 货号,表字段:item_no
           */
          itemNo?: string;
          /**
           * 是否组合货品1是0不是,表字段:is_combination
           */
          isCombination?: number;
          /**
           * 扫描收货数量
           */
          confirmNum?: number;
          /**
           * 商品类型
           */
          refundItemType?: number;
          /**
           * 商品来源0-平台快照1-手动添加
           */
          refundItemSource?: number;
          /**
           * 货品关联商品规格id
           */
          relateSkuId?: string;
          /**
           * oid
           */
          oid?: string;
          /**
           * 本次需处理售后数量
           */
          currentRefundNum?: number;
          /**
           * 成本价,表字段:cost_price
           */
          costPrice?: string;
          /**
           * 平台订单编号
           */
          ptTid?: string;
          /**
           * 平台子订单编号
           */
          ptOid?: string;
          /**
           * 平台
           */
          platform?: string;
          /**
           * 退款id
           */
          refundId?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 是否删除，同步用
           */
          toDelete?: boolean;
          /**
           * 原售后商品明细id-售后扫描用
           */
          originalRefundItemId?: number;
          /**
           * 原售后货品明细id-售后扫描用
           */
          originalSysRefundItemId?: number;
          /**
           * 编辑类型
           */
          editItemType?: {
            [k: string]: any;
          };
          [k: string]: any;
        };
        [k: string]: any;
      }[];
      /**
       * 仓库id
       */
      storageId?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ResponseBody<BatchRefundResultDTO> :ResponseBody
 */
export interface FastRefundFastRefundInStockResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BatchRefundResultDTO
   */
  data?: {
    userId?: number;
    /**
     * 进度0-100
     */
    progress?: number;
    /**
     * 总数量
     */
    total?: number;
    /**
     * 成功数量
     */
    successNum?: number;
    /**
     * 失败数量
     */
    failNum?: number;
    /**
     * 错误信息
     */
    errorMsg?: string;
    /**
     * 真实进度
     */
    actualProgress?: number;
    finish?: boolean;
    /**
     * ErrorDetails
     */
    errorList?: {
      /**
       * 运单号
       */
      sid?: string;
      /**
       * 退款id
       */
      refundId?: string;
      ptTid?: string;
      errorInfo?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}
