/**
 * ReqLiveAuthorPageQueryDTO :ReqLiveAuthorPageQueryDTO
 */
export interface TradeLiveAuthorQueryPageListRequest {
    /**
     * 时间类型,1.创建时间
     */
    timeType?: number;
    /**
     * 开始时间
     */
    startTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 平台
     */
    platform?: string;
    /**
     * 达人名称
     */
    authorName?: string;
    /**
     * 达人id
     */
    authorId?: string;
    pageNo?: number;
    pageSize?: number;
    [k: string]: any;
  }

  
/**
 * ResponseBody<PageList<LiveAuthorRecordDTO>> :ResponseBody
 */
export interface TradeLiveAuthorQueryPageListResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
      pageNo?: number;
      pageSize?: number;
      total?: number;
      /**
       * T
       */
      list?: {
        /**
         * @mbg.generated,id,表字段:id
         */
        id?: number;
        /**
         * @mbg.generated,用户ID,表字段:user_id
         */
        userId?: number;
        /**
         * @mbg.generated,平台,表字段:platform
         */
        platform?: string;
        /**
         * @mbg.generated,达人ID,表字段:author_id
         */
        authorId?: string;
        /**
         * @mbg.generated,达人名称,表字段:author_name
         */
        authorName?: string;
        /**
         * @mbg.generated,操作人,表字段:operator
         */
        operator?: string;
        /**
         * @mbg.generated,创建时间,表字段:gmt_created
         */
        gmtCreated?: string;
        /**
         * @mbg.generated,修改时间,表字段:gmt_modified
         */
        gmtModified?: string;
        /**
         * @mbg.generated,删除标识0已删除1未删除,表字段:enable_status
         */
        enableStatus?: number;
        [k: string]: any;
      }[];
      [k: string]: any;
    };
    [k: string]: any;
  }
  

/**
 * ReqLiveAuthorAddDTO :ReqLiveAuthorAddDTO
 */
export interface TradeLiveAuthorSaveRequest {
    /**
     * 添加方式,1:名称、id添加；,2:订单编号添加
     */
    addType?: number;
    /**
     * 达人ID
     */
    authorId?: string;
    /**
     * 达人名称
     */
    authorName?: string;
    /**
     * 平台
     */
    platform?: string;
    /**
     * 订单编号
     */
    ptTid?: string;
    [k: string]: any;
  }
  
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLiveAuthorSaveResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }
  

/**
 * ReqLiveAuthorDelDTO :ReqLiveAuthorDelDTO
 */
export interface TradeLiveAuthorDeleteRequest {
    /**
     * 记录ID
     */
    id?: number;
    [k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLiveAuthorDeleteResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }
  

/**
 * ResponseBody<List<LiveAuthorRecordDTO>> :ResponseBody
 */
export interface TradeLivePrintGetAuthorListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * LiveAuthorRecordDTO
   */
  data?: {
    /**
     * @mbg.generated,id,表字段:id
     */
    id?: number;
    /**
     * @mbg.generated,用户ID,表字段:user_id
     */
    userId?: number;
    /**
     * @mbg.generated,平台,表字段:platform
     */
    platform?: string;
    /**
     * @mbg.generated,达人ID,表字段:author_id
     */
    authorId?: string;
    /**
     * @mbg.generated,达人名称,表字段:author_name
     */
    authorName?: string;
    /**
     * @mbg.generated,操作人,表字段:operator
     */
    operator?: string;
    /**
     * @mbg.generated,直播状态0未直播1正在直播,表字段:live_status
     */
    liveStatus?: number;
    /**
     * @mbg.generated,直播打印任务ID，live_status=1情况下有值,表字段:live_trade_print_task_id
     */
    liveTradePrintTaskId?: number;
    /**
     * @mbg.generated,创建时间,表字段:gmt_created
     */
    gmtCreated?: string;
    /**
     * @mbg.generated,修改时间,表字段:gmt_modified
     */
    gmtModified?: string;
    /**
     * @mbg.generated,删除标识0已删除1未删除,表字段:enable_status
     */
    enableStatus?: number;
    [k: string]: any;
  }[];
  [k: string]: any;
}

export interface TradeLivePrintGetAuthorListRequest {
    [k: string]: any;
  }
export interface TradeLivePrintGetLiveShopListRequest {
    [k: string]: any;
  }

/**
 * ResponseBody<List<PlatformShopInfo>> :ResponseBody
 */
export interface TradeLivePrintGetLiveShopListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * PlatformShopInfo
   */
  data?: {
    /**
     * 用户ID
     */
    userId?: number;
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 店铺ID
     */
    sellerId?: string;
    /**
     * 店铺名称
     */
    sellerNick?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}
/**
 * ReqLiveTradeSendFakerTradeDTO :ReqLiveTradeSendFakerTradeDTO
 */
export interface TradeLivePrintIntervalFakerTradeToSeeRequest {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  /**
   * 跳过轮询检查
   */
  skipIntervalCheck?: boolean;
  [k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLivePrintIntervalFakerTradeToSeeResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}
/**
 * ReqLiveTradeSendFakerTradeDTO :ReqLiveTradeSendFakerTradeDTO
 */
export interface TradeLivePrintSendFakerTradeToSeeRequest {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  /**
   * 跳过轮询检查
   */
  skipIntervalCheck?: boolean;
  [k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLivePrintSendFakerTradeToSeeResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqLiveCommentDataDTO :ReqLiveCommentDataDTO
 */
export interface TradeLivePrintBatchSaveLiveCommentRequest {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  /**
   * 直播弹幕数据 ,LiveCommentData
   */
  liveCommentDataList?: {
    /**
     * id
     */
    id?: number;
    /**
     * 弹幕序号
     */
    commentNo?: string;
    /**
     * 扣号时间
     */
    commentTime?: string;
    /**
     * 扣号内容
     */
    commentContent?: string;
    /**
     * 买家昵称
     */
    buyerNick?: string;
    /**
     * 买家id，平台用户id
     */
    buyerId?: string;
    /**
     * 弹幕状态1有匹配未打印2有匹配已打印3有匹配打印失败
     */
    commentStatus?: number;
    /**
     * 弹幕匹配扣号规则id
     */
    commentMatchRuleId?: string;
    /**
     * 打印失败原因
     */
    printErrorMsg?: string;
    /**
     * 打印时间
     */
    printTime?: string;
    /**
     * 打印序号
     */
    printNo?: number;
    /**
     * 标签序号
     */
    labelNo?: number;
    /**
     * 是否黑名单0否，1是
     */
    isBlackList?: boolean;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLivePrintBatchSaveLiveCommentResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqLiveCommentRuleUpdateDTO :ReqLiveCommentRuleUpdateDTO
 */
export interface TradeLivePrintUpdateLiveCommentRuleRequest {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  /**
   * 抢单规则更新 ,LiveCommentRuleInfo
   */
  liveCommentRuleList?: {
    /**
     * 规则ID(uuid)
     */
    ruleID?: string;
    /**
     * 抢单类型1:普通商品2:尾货商品3:孤品
     */
    itemType?: number;
    /**
     * 多少秒内打印
     */
    timeRand?: number;
    /**
     * 打印前多少位扣号玩家
     */
    maxPrintUserCount?: number;
    /**
     * 弹幕类型1全部打印（数字、小数、符号）2纯数字3数字+关键词
     */
    numType?: number;
    /**
     * 关键词内容
     */
    keyword?: string;
    /**
     * 数字范围起始值
     */
    numRangeStart?: number;
    /**
     * 数字范围结束值
     */
    numRangeEnd?: number;
    /**
     * 状态1.有效2.无效
     */
    status?: number;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLivePrintUpdateLiveCommentRuleResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  }[];
  [k: string]: any;
}


/**
 * ReqLiveCustomerBlackQueryDto :ReqLiveCustomerBlackQueryDto
 */
export interface TradeLivePrintQueryCustomerBlackListRequest {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  /**
   * 平台
   */
  platform?: string;
  /**
   * 买家唯一标识字段
   */
  buyerOpenUid?: string;
  /**
   * 买家昵称
   */
  buyerNick?: string;
  /**
   * 是否黑名单:0-否;1-是
   */
  blackList?: number;
  [k: string]: any;
}

/**
 * ResponseBody<List<CrmCustomerInfoVo>> :ResponseBody
 */
export interface TradeLivePrintQueryCustomerBlackListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * CrmCustomerInfoVo
   */
  data?: {
    /**
     * 客户编号
     */
    customerNo?: string;
    /**
     * 买家唯一标识字段
     */
    buyerOpenUid?: string;
    /**
     * 收件人姓名
     */
    receiverName?: string;
    /**
     * 买家昵称
     */
    buyerNick?: string;
    /**
     * 所属平台
     */
    platform?: string;
    /**
     * 收货人省份
     */
    receiverProvince?: string;
    /**
     * 收货人城市
     */
    receiverCity?: string;
    /**
     * 收货人的区县;
     */
    receiverCounty?: string;
    /**
     * 收货人街道;
     */
    receiverTown?: string;
    /**
     * 手机人地址
     */
    receiverAddress?: string;
    /**
     * 收件人手机号码;
     */
    receiverMobile?: string;
    /**
     * 是否黑名单:0-否;1-是
     */
    blackList?: number;
    /**
     * 系统创建时间
     */
    gmtCreated?: string;
    /**
     * 系统修改时间
     */
    gmtModified?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ReqLiveCommentRecordQueryDTO :ReqLiveCommentRecordQueryDTO
 */
export interface TradeLivePrintSelectLiveCommentRecordWithPageRequest {
  /**
   * 扣号时间开始
   */
  liveCommentTimeStart?: string;
  /**
   * 扣号时间结束
   */
  liveCommentTimeEnd?: string;
  /**
   * 打印时间开始
   */
  printTimeStart?: string;
  /**
   * 打印时间结束
   */
  printTimeEnd?: string;
  /**
   * 多平台店铺查询 ,MultiShopInfo
   */
  multiShopList?: {
    sellerId?: number;
    platform?: string;
    [k: string]: any;
  }[];
  /**
   * 直播场次
   */
  liveNo?: string;
  /**
   * 买家昵称
   */
  buyerNick?: string;
  /**
   * 扣号内容
   */
  liveCommentContent?: string;
  userId?: number;
  pageNo?: number;
  pageSize?: number;
  [k: string]: any;
}
/**
 * ResponseBody<PageList<RspLiveCommentRecordPageDTO>> :ResponseBody
 */
export interface TradeLivePrintSelectLiveCommentRecordWithPageResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * PageList
   */
  data?: {
    pageNo?: number;
    pageSize?: number;
    total?: number;
    /**
     * T
     */
    list?: {
      /**
       * id
       */
      id?: number;
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      /**
       * 直播场次
       */
      liveNo?: string;
      /**
       * 直播打印任务id
       */
      liveTradePrintTaskId?: number;
      /**
       * 弹幕序号
       */
      commentNo?: string;
      /**
       * 扣号时间
       */
      commentTime?: string;
      /**
       * 扣号内容
       */
      commentContent?: string;
      /**
       * 买家昵称
       */
      buyerNick?: string;
      /**
       * 买家昵称加密
       */
      buyerNickEncrypt?: string;
      /**
       * 买家id，平台用户id
       */
      buyerId?: string;
      /**
       * 弹幕状态1有匹配未打印2有匹配已打印3有匹配打印失败
       */
      commentStatus?: number;
      /**
       * 弹幕匹配扣号规则id
       */
      commentMatchRuleId?: string;
      /**
       * 打印失败原因
       */
      printErrorMsg?: string;
      /**
       * 打印时间
       */
      printTime?: string;
      /**
       * 打印序号
       */
      printNo?: number;
      /**
       * 标签序号
       */
      labelNo?: number;
      /**
       * 是否黑名单0否，1是
       */
      isBlackList?: boolean;
      /**
       * 是否新客户0否，1是
       */
      isNewCustomer?: boolean;
      /**
       * 创建时间
       */
      gmtCreated?: string;
      /**
       * 修改时间
       */
      gmtModified?: string;
      /**
       * 删除标识0已删除1未删除
       */
      enableStatus?: number;
      /**
       * 店铺id与平台组合，查询使用
       */
      indexSellerIdPlatform?: string;
      /**
       * 子账号用户id
       */
      subUserId?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}
