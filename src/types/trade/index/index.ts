import { OccupyErrorTypeEnum, WaybillNoSource } from "@/pages/Trade/constants";
import { IGroupRelationRecordList, IPackage } from "@/pages/Trade/interface";
import { TradeSearchConditionConfig } from "../search/search";
import { TradeOptEnum } from "@/utils/enum/trade";

/**
 * ResponseBody<OrderSplitVo> :ResponseBody
 */
export interface TradeOrderSplitCloseSystemOrderResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * OrderSplitVo
   */
  data?: {
    /**
     * 订单查询对象 ,ErpPackageInfoVo
     */
    packageInfoVos?: {
      buyerNick?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      openId?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      memberId?: string;
      /**
       * 买家昵称唯一标识
       */
      buyerOpenUid?: string;
      isCod?: boolean;
      printInfo?: string;
      platform?: string;
      createTime?: string;
      receiverAddress?: string;
      receiverCity?: string;
      receiverDistrict?: string;
      receiverMobile?: string;
      receiverName?: string;
      receiverPhone?: string;
      receiverState?: string;
      receiverTown?: string;
      userId?: string;
      sellerNick?: string;
      platformId?: string;
      togetherId?: string;
      shipListPrintStatus?: string;
      waybillPrintStatus?: string;
      mainTid?: string;
      orderCode?: string;
      isSuccess?: boolean;
      errorMsg?: string;
      /**
       * TradeVo
       */
      trades?: {
        /**
         * 手工单系统tid，主要用来申请单号
         */
        realTid?: string;
        /**
         * 手工单发货信息
         */
        shipInfo?: string;
        /**
         * 发件人
         */
        senderName?: string;
        /**
         * 发件人电话
         */
        senderPhone?: string;
        /**
         * 发件人手机
         */
        senderMobile?: string;
        /**
         * sender_province
         */
        senderProvince?: string;
        /**
         * sender_city
         */
        senderCity?: string;
        /**
         * sender_county
         */
        senderCounty?: string;
        /**
         * 发件人地址
         */
        senderAddress?: string;
        sellerId?: string;
        buyerMessage?: string;
        buyerNick?: string;
        /**
         * 买家昵称唯一标识
         */
        buyerOpenUid?: string;
        createTime?: string;
        invoiceName?: string;
        invoiceTitle?: string;
        invoiceKind?: string;
        invoiceType?: string;
        isChange?: boolean;
        isMendian?: boolean;
        isCod?: boolean;
        isPrintFhd?: string;
        isPrintKdd?: string;
        payTime?: string;
        /**
         * OrderVo
         */
        orders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          /**
           * 是否线上发货
           */
          onlineShip?: boolean;
          [k: string]: any;
        }[];
        /**
         * OrderVo
         */
        giftOrders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          /**
           * 是否线上发货
           */
          onlineShip?: boolean;
          [k: string]: any;
        }[];
        gifts?: string;
        /**
         * 实收金额
         */
        receivedPayment?: string;
        payment?: string;
        discountAmount?: string;
        postFee?: string;
        receiverMobile?: string;
        receiverCity?: string;
        receiverDistrict?: string;
        receiverPhone?: string;
        receiverAddress?: string;
        receiverName?: string;
        receiverState?: string;
        receiverTown?: string;
        receiverZip?: string;
        sellerFlag?: string;
        userId?: string;
        sellerNick?: string;
        platform?: string;
        sellerMemo?: string;
        ignoreType?: number;
        sellerMemoFlag?: string;
        sellerMemoFlagName?: string;
        tid?: string;
        ptTid?: string;
        totalFee?: string;
        status?: string;
        gift?: string;
        type?: string;
        sendRemindFlag?: number;
        sendRemindHour?: string;
        isAddCostSF?: boolean;
        /**
         * OrderPromiseDetailVo
         */
        orderPromiseDetailVo?: {
          orderPromiseKdCode?: string;
          orderPromiseKdName?: string;
          /**
           * 指定包材
           */
          orderPromiseBc?: string;
          orderPromiseDeliveryTime?: number;
          [k: string]: any;
        };
        freeSF?: number;
        /**
         * 商家优惠金额
         */
        sellerDiscount?: string;
        distributorTid?: string;
        /**
         * 平台折扣金额
         */
        platformDiscount?: string;
        /**
         * 宝贝数量：取订单中的商品总数量
         */
        totalNums?: number;
        /**
         * 宝贝种类
         */
        itemCateNum?: number;
        /**
         * 是否是预发货
         */
        isPreShip?: boolean;
        isPending?: boolean;
        isCancelSend?: boolean;
        isMatchCancelSend?: boolean;
        imel?: string;
        deviceSn?: string;
        overseaTracing?: string;
        expressName?: string;
        /**
         * 催发货标记0无催发货，1有催发货；
         */
        urge?: number;
        /**
         * 催发货时间
         */
        urgeShippingTime?: string;
        /**
         * 预发货任务id
         */
        preShipId?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单(该参数为map)
         */
        sellerFlagSys?: {
          /**
           * String
           */
          mapKey?: {
            [k: string]: any;
          };
          /**
           * Integer
           */
          mapValue?: {
            [k: string]: any;
          };
          [k: string]: any;
        };
        /**
         * 标识当前订单是否存在退款，不做存储
         */
        hasRefund?: boolean;
        refundStatus?: string;
        totalWeight?: string;
        /**
         * 快递单号 ,String
         */
        ydNoSet?: string[];
        /**
         * 多包裹运单数量
         */
        multiPackYdCount?: number;
        /**
         * 快递公司编号
         */
        logisticsId?: string;
        /**
         * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
         */
        stockOutHandleStatus?: number;
        /**
         * 是否缺货0-无缺货处理1：有缺货处理
         */
        isStockOut?: number;
        /**
         * 预售时间
         */
        preSaleTime?: string;
        /**
         * 是否为预售商品1表示是0表示否
         */
        isPreSale?: number;
        /**
         * 风控状态（值为1则为风控订单，值为0则为正常订单）
         */
        riskControlStatus?: string;
        receiverNameMask?: string;
        receiverPhoneMask?: string;
        receiverAddressMask?: string;
        receiverMobileMask?: string;
        idxEncodeReceiverMobile?: string;
        idxEncodeReceiverName?: string;
        idxEncodeReceiverAddress?: string;
        encodeTid?: string;
        encodePtTid?: string;
        /**
         * 订单调fullInfo接口的时间
         */
        tidFullInfoTime?: string;
        /**
         * 天猫直送，值true或者false
         */
        tmallDelivery?: boolean;
        /**
         * 3PL有时效订单标，值true或者false
         */
        threePlTiming?: boolean;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        deliveryCps?: string;
        /**
         * 取
         */
        cpCode?: string;
        /**
         * cpCode转cpName
         */
        cpName?: string;
        /**
         * 是否催发货1：催发货0：非催发货
         */
        isUrgeDelivery?: number;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        caid?: string;
        /**
         * trade_fromWAP,JHS交易内部来源
         */
        tradeFrom?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
         */
        fenxiaos?: number;
        payHours?: number;
        receiverSecret?: string;
        mobileSecret?: string;
        timingPromise?: string;
        promiseService?: string;
        esDate?: string;
        esRange?: string;
        osDate?: string;
        osRange?: string;
        cutoffMinutes?: string;
        esTime?: string;
        deliveryTime?: string;
        collectTime?: string;
        sendTime?: string;
        signTime?: string;
        dispatchTime?: string;
        /**
         * value=logistics_upgrade为天猫物流升级订单
         */
        asdpBizType?: string;
        /**
         * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
         */
        asdpAds?: string;
        /**
         * 天猫送货上门快递名称
         */
        sendExName?: string;
        /**
         * 天猫送货上门快递编码
         */
        sendExCode?: string;
        /**
         * 全渠道商品通相关字段
         */
        omnichannelParam?: string;
        /**
         * 是否是海外购
         */
        isThreePl?: boolean;
        /**
         * 是否风险留言订单
         */
        pbly?: boolean;
        /**
         * 家装订单
         */
        tmserSpu?: boolean;
        /**
         * 淘宝后台getShippingType
         */
        shippingType?: string;
        /**
         * 淘宝后台getShippingType对应名称，后期
         */
        shippingName?: string;
        deliveryTypeDesc?: string;
        /**
         * 订单类型交易方式,一口价,送礼,赠品领取,货到付款,定金预售
         */
        tradeType?: string;
        /**
         * 支付类型
         */
        payType?: string;
        biz?: string;
        /**
         * 是否为乡镇订单
         */
        hasTown?: number;
        /**
         * 打印时间
         */
        printTime?: string;
        /**
         * 发货时间
         */
        shipTime?: string;
        /**
         * 提示手动合单的判断标示
         */
        mergeBuyerNick?: string;
        noGoodsLink?: boolean;
        goodsWarn?: boolean;
        firstSend?: boolean;
        smartSelectExpress?: string;
        labelstatus?: number;
        /**
         * 0未打印1全部打印
         */
        labelPrintStatus?: number;
        promiseDeliveryTime?: boolean;
        /**
         * PromiseLogistics
         */
        promiseLogisticsList?: {
          company?: string;
          exCode?: string;
          [k: string]: any;
        }[];
        wrapperDescription?: boolean;
        duoduoWholesale?: boolean;
        shipHold?: boolean;
        jdStoreOrderType?: number;
        jdOrderShipType?: number;
        /**
         * 额外运单 ,ExtraDeliveryVO
         */
        extraDeliveryList?: {
          /**
           * 快递运单号
           */
          trackingNumber?: string;
          /**
           * 快递公司id
           */
          logisticsId?: number;
          /**
           * 快递公司名称
           */
          logisticsName?: string;
          [k: string]: any;
        }[];
        /**
         * true跨境订单false非跨境
         */
        crossBorder?: boolean;
        /**
         * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
         */
        consolidateType?: string;
        /**
         * 订单来源：HAND手工单
         */
        source?: string;
        changeAdderFlag?: number;
        /**
         * 拼团订单未完成true未完成false或者为空为正常订单
         */
        pOrderUnfinished?: boolean;
        /**
         * 订单类型
         */
        tradeTypeTagStr?: string;
        /**
         * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
         */
        openAddressId?: string;
        /**
         * 精选联盟
         */
        isSelectedAlliance?: boolean;
        /**
         * 小店自卖
         */
        isSmallStoreSelfSelling?: boolean;
        /**
         * 闪电购商品
         */
        isFlashBuyingProducts?: boolean;
        /**
         * 重新发货
         */
        isResend?: boolean;
        /**
         * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
         */
        receiverId?: string;
        /**
         * 回流订单平台类型
         */
        hlPlatformType?: string;
        /**
         * 回流订单是否加密
         */
        hlEncryptOrder?: boolean;
        /**
         * fxg承诺日达1表示是0表示否
         */
        appointmentArrival?: number;
        /**
         * 发货仓编号
         */
        outWarehouseId?: string;
        /**
         * 发货仓名称
         */
        warehouseName?: string;
        /**
         * 指定时间送达承诺时间送达
         */
        receiptDate?: string;
        /**
         * ali定制服务1表示是0表示否
         */
        aliEntryServiceInfo?: number;
        /**
         * 判断是否需要上传序列码
         */
        needSerialNumber?: boolean;
        /**
         * 达人标签订单
         */
        isAuthorOrder?: boolean;
        /**
         * 线下备注
         */
        sysMemo?: string;
        /**
         * 标签：加运费发顺丰
         */
        hasSfExpressService?: boolean;
        /**
         * 顺丰加价服务费
         */
        sfExpressFee?: string;
        /**
         * 是否线上发货
         */
        onlineShip?: boolean;
        /**
         * 是否拆单发货
         */
        isSplit?: boolean;
        [k: string]: any;
      }[];
      sendRemindFlag?: number;
      sendRemindHour?: string;
      payTime?: string;
      isPending?: boolean;
      isAddCostSF?: boolean;
      freeSF?: number;
      expressName?: string;
      sellerId?: string;
      receiverNameMask?: string;
      receiverPhoneMask?: string;
      receiverAddressMask?: string;
      receiverMobileMask?: string;
      idxEncodeReceiverMobile?: string;
      idxEncodeReceiverName?: string;
      idxEncodeReceiverAddress?: string;
      encodeTid?: string;
      caid?: string;
      /**
       * 提示手动合单的判断标示
       */
      mergeBuyerNick?: string;
      /**
       * 多包裹运单数量
       */
      multiPackYdCount?: number;
      /**
       * 是否包含预发货订单
       */
      isPreShip?: boolean;
      /**
       * String
       */
      preShipIds?: string[];
      hasRefund?: boolean;
      receiverZip?: string;
      /**
       * (该参数为map)
       */
      sellerFlagSys?: {
        /**
         * String
         */
        mapKey?: {
          [k: string]: any;
        };
        /**
         * Integer
         */
        mapValue?: {
          [k: string]: any;
        };
        [k: string]: any;
      };
      /**
       * :
       */
      tids?: string[];
      /**
       * :
       */
      ptTids?: string[];
      /**
       * CaiNiaoIntelliExpress
       */
      smartExpress?: {
        exCode?: string;
        exName?: string;
        [k: string]: any;
      };
      type?: string;
      /**
       * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
       */
      fenxiaos?: number;
      /**
       * 是否合单
       */
      isMerge?: string;
      /**
       * 总实收金额
       */
      totalReceivedPayment?: string;
      /**
       * 总支付金额
       */
      totalPayment?: string;
      /**
       * 总商家优惠金额
       */
      totalSellerDiscount?: string;
      /**
       * 总平台折扣金额
       */
      totalPlatformDiscount?: string;
      /**
       * 总邮费
       */
      totalPostFee?: string;
      /**
       * 总重量
       */
      totalWeight?: string;
      /**
       * 已发货的快递单号
       */
      ydNo?: string;
      /**
       * 快递单号 ,String
       */
      ydNoSet?: string[];
      /**
       * 验单发货订单号搜索先发货订单模板id
       */
      verifyOrderListShowId?: number;
      /**
       * 模版的扩展字段，里面包含了用户模版id
       */
      templateYdAttr?: string;
      /**
       * 订单标记（用于黑白订单标记）,0：正常订单（默认）,1：白订单（表明这笔订单是商家请人刷的订单）,2：黑名单（表明这笔订单是别人恶意刷的订单）
       */
      tidMark?: number;
      /**
       * OrderPromiseVo
       */
      orderPromiseVo?: {
        orderPromiseDeliveryTime?: number;
        isManyKd?: boolean;
        [k: string]: any;
      };
      /**
       * Object
       */
      orderTagList?: {
        [k: string]: any;
      }[];
      payHours?: number;
      goodsNum?: string;
      /**
       * 家装订单
       */
      tmserSpu?: boolean;
      /**
       * 异常地址
       */
      abnormalAddress?: boolean;
      /**
       * 智选快递的快递名称
       */
      ExpressName?: string;
      /**
       * 打印菜鸟面单时是否需要隐私服务
       */
      cnPrivacy?: boolean;
      receiverSecret?: string;
      mobileSecret?: string;
      /**
       * OpenAddresseeID（收件人ID）,基于收件人的信息(姓名、地址、手机号、电话）、订单创建时间、店铺、appkey加密生成的ID。,相同的收件人，在同一家店铺，在固定时间周期内（通常1个自然周）内创建的订单，OAID相同。
       */
      oaid?: string;
      /**
       * 天猫送货上门快递名称
       */
      sendExName?: string;
      /**
       * 天猫送货上门快递编码
       */
      sendExCode?: string;
      /**
       * value=logistics_upgrade为天猫物流升级订单
       */
      asdpBizType?: string;
      /**
       * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
       */
      asdpAds?: string;
      signTime?: string;
      deliveryTime?: string;
      /**
       * 是否为乡镇订单
       */
      hasTown?: number;
      /**
       * 催发货标记0无催发货，1有催发货；
       */
      urge?: number;
      /**
       * 催发货时间
       */
      urgeShippingTime?: string;
      changeAdderFlag?: number;
      noGoods?: boolean;
      noGoodsLink?: boolean;
      goodsWarn?: boolean;
      firstSend?: boolean;
      preSell?: boolean;
      /**
       * 疫情标记,0：非疫情地区,1：疫情中风险地区,2：疫情高风险地区
       */
      epidemicMark?: number;
      /**
       * 风控状态（值为1则为风控订单，值为0则为正常订单）
       */
      riskControlStatus?: string;
      /**
       * 智选快递
       */
      smartSelectExpress?: string;
      /**
       * 智选快递模板级别推荐结果、是否手动指定
       */
      smartExpressTemplate?: string;
      /**
       * 合单智选快递模板级别推荐结果、是否手动指定
       */
      packSmartExpressTemplate?: string;
      labelstatus?: number;
      /**
       * 0未打印1全部打印2部分打印
       */
      labelPrintStatus?: number;
      promiseDeliveryTime?: boolean;
      /**
       * PromiseLogistics
       */
      promiseLogisticsList?: {
        company?: string;
        exCode?: string;
        [k: string]: any;
      }[];
      wrapperDescription?: boolean;
      /**
       * 顺丰包邮
       */
      freeSf?: boolean;
      /**
       * true跨境订单false非跨境
       */
      crossBorder?: boolean;
      tradeType?: string;
      /**
       * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
       */
      consolidateType?: string;
      /**
       * 订单来源:其他平台订单,Hand手工订单
       */
      source?: string;
      /**
       * 拼团订单未完成true未完成false或者为空为正常订单
       */
      pOrderUnfinished?: boolean;
      /**
       * 分销商用户ID
       */
      distributorUserId?: number;
      /**
       * 分销商账号
       */
      distributorAccount?: string;
      /**
       * 分销商名称
       */
      distributorName?: string;
      /**
       * 分销商联系人
       */
      distributorLinkMan?: string;
      /**
       * 分销商联系电话
       */
      distributorMobile?: string;
      /**
       * 分销商备注
       */
      distributorRemark?: string;
      /**
       * 分销状态,0:申请中,1：合作中,2：拒绝,3：终止
       */
      distributorStatus?: number;
      /**
       * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
       */
      openAddressId?: string;
      /**
       * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
       */
      receiverId?: string;
      /**
       * 回流订单平台类型
       */
      hlPlatformType?: string;
      /**
       * 回流订单是否加密
       */
      hlEncryptOrder?: boolean;
      /**
       * fxg承诺日达1表示是0表示否
       */
      appointmentArrival?: number;
      /**
       * 发货仓编号
       */
      outWarehouseId?: string;
      /**
       * 发货仓名称
       */
      warehouseName?: string;
      /**
       * 指定时间送达承诺时间送达
       */
      receiptDate?: string;
      /**
       * ali定制服务1表示是0表示否
       */
      aliEntryServiceInfo?: number;
      /**
       * 是否线上发货
       */
      onlineShip?: boolean;
      /**
       * 是否拆单发货
       */
      isSplit?: boolean;
      [k: string]: any;
    }[];
    /**
     * 被取消或隐藏订单编号 ,String
     */
    cancelOrHideTidList?: string[];
    /**
     * 拆分失败的订单对象 ,RdsBatchSplitTradeResultVo
     */
    failedList?: {
      /**
       * 系统订单号
       */
      tid?: string;
      /**
       * 平台订单编号
       */
      ptTid?: string;
      /**
       * 错误信息
       */
      message?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}


/**
 * TradeCloseSystemOrderRequest :TradeCloseSystemOrderRequest
 */
export interface TradeOrderSplitCloseSystemOrderRequest {
  /**
   * 需要关闭订单列表 ,CloseSystemOrderInfo
   */
  closeSystemOrderInfoList?: {
    /**
     * 卖家ID
     */
    sellerId?: number;
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 平台订单号
     */
    ptTid?: string;
    /**
     * 系统订单号
     */
    tid?: string;
    /**
     * 子订单编号 ,String
     */
    oidList?: string[];
    [k: string]: any;
  }[];
  [k: string]: any;
}


/**
 * TradeOrderSplitRequest :TradeOrderSplitRequest
 */
export interface TradeOrderSplitOrderSplitRequest {
  /**
   * 订单拆分集合，需要支持多笔订单拆分 ,OrderSplitInfo
   */
  orderSplitInfos?: {
    /**
     * 卖家ID
     */
    sellerId?: number;
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 订单编号
     */
    tid?: string;
    /**
     * 平台订单编号
     */
    ptTid?: string;
    /**
     * 具体的订单拆分组,数组数量表示整订单需要拆分为几笔,所有的拆分加起来，必须等于当前要拆分订单的总数据量 ,GroupSplitInfo
     */
    groupSplitInfos?: {
      /**
       * 当前这边订单，是由下方哪些子订单商品组成的 ,OidSplitInfo
       */
      oidSplitInfos?: {
        /**
         * 子订单编号
         */
        oid?: string;
        /**
         * 拆分的数量
         */
        number?: number;
        [k: string]: any;
      }[];
      [k: string]: any;
    }[];
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ResponseBody<OrderSplitVo> :ResponseBody
 */
export interface TradeOrderSplitOrderSplitResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * OrderSplitVo
   */
  data?: {
    /**
     * 订单查询对象 ,ErpPackageInfoVo
     */
    packageInfoVos?: {
      buyerNick?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      openId?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      memberId?: string;
      /**
       * 买家昵称唯一标识
       */
      buyerOpenUid?: string;
      isCod?: boolean;
      printInfo?: string;
      platform?: string;
      createTime?: string;
      receiverAddress?: string;
      receiverCity?: string;
      receiverDistrict?: string;
      receiverMobile?: string;
      receiverName?: string;
      receiverPhone?: string;
      receiverState?: string;
      receiverTown?: string;
      userId?: string;
      sellerNick?: string;
      platformId?: string;
      togetherId?: string;
      shipListPrintStatus?: string;
      waybillPrintStatus?: string;
      mainTid?: string;
      orderCode?: string;
      isSuccess?: boolean;
      errorMsg?: string;
      /**
       * TradeVo
       */
      trades?: {
        /**
         * 手工单系统tid，主要用来申请单号
         */
        realTid?: string;
        /**
         * 手工单发货信息
         */
        shipInfo?: string;
        /**
         * 发件人
         */
        senderName?: string;
        /**
         * 发件人电话
         */
        senderPhone?: string;
        /**
         * 发件人手机
         */
        senderMobile?: string;
        /**
         * sender_province
         */
        senderProvince?: string;
        /**
         * sender_city
         */
        senderCity?: string;
        /**
         * sender_county
         */
        senderCounty?: string;
        /**
         * 发件人地址
         */
        senderAddress?: string;
        sellerId?: string;
        buyerMessage?: string;
        buyerNick?: string;
        /**
         * 买家昵称唯一标识
         */
        buyerOpenUid?: string;
        createTime?: string;
        invoiceName?: string;
        invoiceTitle?: string;
        invoiceKind?: string;
        invoiceType?: string;
        isChange?: boolean;
        isMendian?: boolean;
        isCod?: boolean;
        isPrintFhd?: string;
        isPrintKdd?: string;
        payTime?: string;
        /**
         * OrderVo
         */
        orders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          [k: string]: any;
        }[];
        /**
         * OrderVo
         */
        giftOrders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          [k: string]: any;
        }[];
        gifts?: string;
        /**
         * 实收金额
         */
        receivedPayment?: string;
        payment?: string;
        discountAmount?: string;
        postFee?: string;
        receiverMobile?: string;
        receiverCity?: string;
        receiverDistrict?: string;
        receiverPhone?: string;
        receiverAddress?: string;
        receiverName?: string;
        receiverState?: string;
        receiverTown?: string;
        receiverZip?: string;
        sellerFlag?: string;
        userId?: string;
        sellerNick?: string;
        platform?: string;
        sellerMemo?: string;
        ignoreType?: number;
        sellerMemoFlag?: string;
        sellerMemoFlagName?: string;
        tid?: string;
        ptTid?: string;
        totalFee?: string;
        status?: string;
        gift?: string;
        type?: string;
        sendRemindFlag?: number;
        sendRemindHour?: string;
        isAddCostSF?: boolean;
        /**
         * OrderPromiseDetailVo
         */
        orderPromiseDetailVo?: {
          orderPromiseKdCode?: string;
          orderPromiseKdName?: string;
          /**
           * 指定包材
           */
          orderPromiseBc?: string;
          orderPromiseDeliveryTime?: number;
          [k: string]: any;
        };
        freeSF?: number;
        /**
         * 商家优惠金额
         */
        sellerDiscount?: string;
        distributorTid?: string;
        /**
         * 平台折扣金额
         */
        platformDiscount?: string;
        /**
         * 宝贝数量：取订单中的商品总数量
         */
        totalNums?: number;
        /**
         * 宝贝种类
         */
        itemCateNum?: number;
        /**
         * 是否是预发货
         */
        isPreShip?: boolean;
        isPending?: boolean;
        isCancelSend?: boolean;
        isMatchCancelSend?: boolean;
        imel?: string;
        deviceSn?: string;
        overseaTracing?: string;
        expressName?: string;
        /**
         * 催发货标记0无催发货，1有催发货；
         */
        urge?: number;
        /**
         * 催发货时间
         */
        urgeShippingTime?: string;
        /**
         * 预发货任务id
         */
        preShipId?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单(该参数为map)
         */
        sellerFlagSys?: {
          /**
           * String
           */
          mapKey?: {
            [k: string]: any;
          };
          /**
           * Integer
           */
          mapValue?: {
            [k: string]: any;
          };
          [k: string]: any;
        };
        /**
         * 标识当前订单是否存在退款，不做存储
         */
        hasRefund?: boolean;
        refundStatus?: string;
        totalWeight?: string;
        /**
         * 快递单号 ,String
         */
        ydNoSet?: string[];
        /**
         * 多包裹运单数量
         */
        multiPackYdCount?: number;
        /**
         * 快递公司编号
         */
        logisticsId?: string;
        /**
         * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
         */
        stockOutHandleStatus?: number;
        /**
         * 是否缺货0-无缺货处理1：有缺货处理
         */
        isStockOut?: number;
        /**
         * 预售时间
         */
        preSaleTime?: string;
        /**
         * 是否为预售商品1表示是0表示否
         */
        isPreSale?: number;
        /**
         * 风控状态（值为1则为风控订单，值为0则为正常订单）
         */
        riskControlStatus?: string;
        receiverNameMask?: string;
        receiverPhoneMask?: string;
        receiverAddressMask?: string;
        receiverMobileMask?: string;
        idxEncodeReceiverMobile?: string;
        idxEncodeReceiverName?: string;
        idxEncodeReceiverAddress?: string;
        encodeTid?: string;
        /**
         * 订单调fullInfo接口的时间
         */
        tidFullInfoTime?: string;
        /**
         * 天猫直送，值true或者false
         */
        tmallDelivery?: boolean;
        /**
         * 3PL有时效订单标，值true或者false
         */
        threePlTiming?: boolean;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        deliveryCps?: string;
        /**
         * 取
         */
        cpCode?: string;
        /**
         * cpCode转cpName
         */
        cpName?: string;
        /**
         * 是否催发货1：催发货0：非催发货
         */
        isUrgeDelivery?: number;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        caid?: string;
        /**
         * trade_fromWAP,JHS交易内部来源
         */
        tradeFrom?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
         */
        fenxiaos?: number;
        payHours?: number;
        receiverSecret?: string;
        mobileSecret?: string;
        timingPromise?: string;
        promiseService?: string;
        esDate?: string;
        esRange?: string;
        osDate?: string;
        osRange?: string;
        cutoffMinutes?: string;
        esTime?: string;
        deliveryTime?: string;
        collectTime?: string;
        sendTime?: string;
        signTime?: string;
        dispatchTime?: string;
        /**
         * value=logistics_upgrade为天猫物流升级订单
         */
        asdpBizType?: string;
        /**
         * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
         */
        asdpAds?: string;
        /**
         * 天猫送货上门快递名称
         */
        sendExName?: string;
        /**
         * 天猫送货上门快递编码
         */
        sendExCode?: string;
        /**
         * 全渠道商品通相关字段
         */
        omnichannelParam?: string;
        /**
         * 是否是海外购
         */
        isThreePl?: boolean;
        /**
         * 是否风险留言订单
         */
        pbly?: boolean;
        /**
         * 家装订单
         */
        tmserSpu?: boolean;
        /**
         * 淘宝后台getShippingType
         */
        shippingType?: string;
        /**
         * 淘宝后台getShippingType对应名称，后期
         */
        shippingName?: string;
        deliveryTypeDesc?: string;
        /**
         * 订单类型交易方式,一口价,送礼,赠品领取,货到付款,定金预售
         */
        tradeType?: string;
        /**
         * 支付类型
         */
        payType?: string;
        biz?: string;
        /**
         * 是否为乡镇订单
         */
        hasTown?: number;
        /**
         * 打印时间
         */
        printTime?: string;
        /**
         * 发货时间
         */
        shipTime?: string;
        /**
         * 提示手动合单的判断标示
         */
        mergeBuyerNick?: string;
        noGoodsLink?: boolean;
        goodsWarn?: boolean;
        firstSend?: boolean;
        smartSelectExpress?: string;
        labelstatus?: number;
        /**
         * 0未打印1全部打印
         */
        labelPrintStatus?: number;
        promiseDeliveryTime?: boolean;
        /**
         * PromiseLogistics
         */
        promiseLogisticsList?: {
          company?: string;
          exCode?: string;
          [k: string]: any;
        }[];
        wrapperDescription?: boolean;
        duoduoWholesale?: boolean;
        shipHold?: boolean;
        jdStoreOrderType?: number;
        jdOrderShipType?: number;
        /**
         * 额外运单 ,ExtraDeliveryVO
         */
        extraDeliveryList?: {
          /**
           * 快递运单号
           */
          trackingNumber?: string;
          /**
           * 快递公司id
           */
          logisticsId?: number;
          /**
           * 快递公司名称
           */
          logisticsName?: string;
          [k: string]: any;
        }[];
        /**
         * true跨境订单false非跨境
         */
        crossBorder?: boolean;
        /**
         * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
         */
        consolidateType?: string;
        /**
         * 订单来源：HAND手工单
         */
        source?: string;
        changeAdderFlag?: number;
        /**
         * 拼团订单未完成true未完成false或者为空为正常订单
         */
        pOrderUnfinished?: boolean;
        /**
         * 订单类型
         */
        tradeTypeTagStr?: string;
        /**
         * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
         */
        openAddressId?: string;
        /**
         * 精选联盟
         */
        isSelectedAlliance?: boolean;
        /**
         * 小店自卖
         */
        isSmallStoreSelfSelling?: boolean;
        /**
         * 闪电购商品
         */
        isFlashBuyingProducts?: boolean;
        /**
         * 重新发货
         */
        isResend?: boolean;
        /**
         * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
         */
        receiverId?: string;
        /**
         * 回流订单平台类型
         */
        hlPlatformType?: string;
        /**
         * 回流订单是否加密
         */
        hlEncryptOrder?: boolean;
        /**
         * fxg承诺日达1表示是0表示否
         */
        appointmentArrival?: number;
        /**
         * 发货仓编号
         */
        outWarehouseId?: string;
        /**
         * 发货仓名称
         */
        warehouseName?: string;
        /**
         * 指定时间送达承诺时间送达
         */
        receiptDate?: string;
        /**
         * ali定制服务1表示是0表示否
         */
        aliEntryServiceInfo?: number;
        /**
         * 判断是否需要上传序列码
         */
        needSerialNumber?: boolean;
        /**
         * 达人标签订单
         */
        isAuthorOrder?: boolean;
        /**
         * 线下备注
         */
        sysMemo?: string;
        /**
         * 标签：加运费发顺丰
         */
        hasSfExpressService?: boolean;
        /**
         * 顺丰加价服务费
         */
        sfExpressFee?: string;
        /**
         * 是否线上发货
         */
        onlineShip?: boolean;
        /**
         * 是否拆单发货
         */
        isSplit?: boolean;
        [k: string]: any;
      }[];
      sendRemindFlag?: number;
      sendRemindHour?: string;
      payTime?: string;
      isPending?: boolean;
      isAddCostSF?: boolean;
      freeSF?: number;
      expressName?: string;
      sellerId?: string;
      receiverNameMask?: string;
      receiverPhoneMask?: string;
      receiverAddressMask?: string;
      receiverMobileMask?: string;
      idxEncodeReceiverMobile?: string;
      idxEncodeReceiverName?: string;
      idxEncodeReceiverAddress?: string;
      encodeTid?: string;
      caid?: string;
      /**
       * 提示手动合单的判断标示
       */
      mergeBuyerNick?: string;
      /**
       * 多包裹运单数量
       */
      multiPackYdCount?: number;
      /**
       * 是否包含预发货订单
       */
      isPreShip?: boolean;
      /**
       * String
       */
      preShipIds?: string[];
      hasRefund?: boolean;
      receiverZip?: string;
      /**
       * (该参数为map)
       */
      sellerFlagSys?: {
        /**
         * String
         */
        mapKey?: {
          [k: string]: any;
        };
        /**
         * Integer
         */
        mapValue?: {
          [k: string]: any;
        };
        [k: string]: any;
      };
      /**
       * :
       */
      tids?: string[];
      /**
       * CaiNiaoIntelliExpress
       */
      smartExpress?: {
        exCode?: string;
        exName?: string;
        [k: string]: any;
      };
      type?: string;
      /**
       * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
       */
      fenxiaos?: number;
      /**
       * 是否合单
       */
      isMerge?: string;
      /**
       * 总实收金额
       */
      totalReceivedPayment?: string;
      /**
       * 总支付金额
       */
      totalPayment?: string;
      /**
       * 总商家优惠金额
       */
      totalSellerDiscount?: string;
      /**
       * 总平台折扣金额
       */
      totalPlatformDiscount?: string;
      /**
       * 总邮费
       */
      totalPostFee?: string;
      /**
       * 总重量
       */
      totalWeight?: string;
      /**
       * 已发货的快递单号
       */
      ydNo?: string;
      /**
       * 快递单号 ,String
       */
      ydNoSet?: string[];
      /**
       * 验单发货订单号搜索先发货订单模板id
       */
      verifyOrderListShowId?: number;
      /**
       * 模版的扩展字段，里面包含了用户模版id
       */
      templateYdAttr?: string;
      /**
       * 订单标记（用于黑白订单标记）,0：正常订单（默认）,1：白订单（表明这笔订单是商家请人刷的订单）,2：黑名单（表明这笔订单是别人恶意刷的订单）
       */
      tidMark?: number;
      /**
       * OrderPromiseVo
       */
      orderPromiseVo?: {
        orderPromiseDeliveryTime?: number;
        isManyKd?: boolean;
        [k: string]: any;
      };
      /**
       * Object
       */
      orderTagList?: {
        [k: string]: any;
      }[];
      payHours?: number;
      goodsNum?: string;
      /**
       * 家装订单
       */
      tmserSpu?: boolean;
      /**
       * 异常地址
       */
      abnormalAddress?: boolean;
      /**
       * 智选快递的快递名称
       */
      ExpressName?: string;
      /**
       * 打印菜鸟面单时是否需要隐私服务
       */
      cnPrivacy?: boolean;
      receiverSecret?: string;
      mobileSecret?: string;
      /**
       * OpenAddresseeID（收件人ID）,基于收件人的信息(姓名、地址、手机号、电话）、订单创建时间、店铺、appkey加密生成的ID。,相同的收件人，在同一家店铺，在固定时间周期内（通常1个自然周）内创建的订单，OAID相同。
       */
      oaid?: string;
      /**
       * 天猫送货上门快递名称
       */
      sendExName?: string;
      /**
       * 天猫送货上门快递编码
       */
      sendExCode?: string;
      /**
       * value=logistics_upgrade为天猫物流升级订单
       */
      asdpBizType?: string;
      /**
       * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
       */
      asdpAds?: string;
      signTime?: string;
      deliveryTime?: string;
      /**
       * 是否为乡镇订单
       */
      hasTown?: number;
      /**
       * 催发货标记0无催发货，1有催发货；
       */
      urge?: number;
      /**
       * 催发货时间
       */
      urgeShippingTime?: string;
      changeAdderFlag?: number;
      noGoods?: boolean;
      noGoodsLink?: boolean;
      goodsWarn?: boolean;
      firstSend?: boolean;
      preSell?: boolean;
      /**
       * 疫情标记,0：非疫情地区,1：疫情中风险地区,2：疫情高风险地区
       */
      epidemicMark?: number;
      /**
       * 风控状态（值为1则为风控订单，值为0则为正常订单）
       */
      riskControlStatus?: string;
      /**
       * 智选快递
       */
      smartSelectExpress?: string;
      /**
       * 智选快递模板级别推荐结果、是否手动指定
       */
      smartExpressTemplate?: string;
      /**
       * 合单智选快递模板级别推荐结果、是否手动指定
       */
      packSmartExpressTemplate?: string;
      labelstatus?: number;
      /**
       * 0未打印1全部打印2部分打印
       */
      labelPrintStatus?: number;
      promiseDeliveryTime?: boolean;
      /**
       * PromiseLogistics
       */
      promiseLogisticsList?: {
        company?: string;
        exCode?: string;
        [k: string]: any;
      }[];
      wrapperDescription?: boolean;
      /**
       * 顺丰包邮
       */
      freeSf?: boolean;
      /**
       * true跨境订单false非跨境
       */
      crossBorder?: boolean;
      tradeType?: string;
      /**
       * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
       */
      consolidateType?: string;
      /**
       * 订单来源:其他平台订单,Hand手工订单
       */
      source?: string;
      /**
       * 拼团订单未完成true未完成false或者为空为正常订单
       */
      pOrderUnfinished?: boolean;
      /**
       * 分销商用户ID
       */
      distributorUserId?: number;
      /**
       * 分销商账号
       */
      distributorAccount?: string;
      /**
       * 分销商名称
       */
      distributorName?: string;
      /**
       * 分销商联系人
       */
      distributorLinkMan?: string;
      /**
       * 分销商联系电话
       */
      distributorMobile?: string;
      /**
       * 分销商备注
       */
      distributorRemark?: string;
      /**
       * 分销状态,0:申请中,1：合作中,2：拒绝,3：终止
       */
      distributorStatus?: number;
      /**
       * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
       */
      openAddressId?: string;
      /**
       * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
       */
      receiverId?: string;
      /**
       * 回流订单平台类型
       */
      hlPlatformType?: string;
      /**
       * 回流订单是否加密
       */
      hlEncryptOrder?: boolean;
      /**
       * fxg承诺日达1表示是0表示否
       */
      appointmentArrival?: number;
      /**
       * 发货仓编号
       */
      outWarehouseId?: string;
      /**
       * 发货仓名称
       */
      warehouseName?: string;
      /**
       * 指定时间送达承诺时间送达
       */
      receiptDate?: string;
      /**
       * ali定制服务1表示是0表示否
       */
      aliEntryServiceInfo?: number;
      /**
       * 是否线上发货
       */
      onlineShip?: boolean;
      /**
       * 是否拆单发货
       */
      isSplit?: boolean;
      [k: string]: any;
    }[];
    /**
     * 被取消或隐藏订单编号 ,String
     */
    cancelOrHideTidList?: string[];
    /**
     * 拆分失败的订单对象 ,RdsBatchSplitTradeResultVo
     */
    failedList?: {
      /**
       * 系统订单号
       */
      tid?: string;
      /**
       * 平台订单编号
       */
      ptTid?: string;
      /**
       * 错误信息
       */
      message?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}
/**
 * TradeCancelSplitRequest :TradeCancelSplitRequest
 */
export interface TradeOrderSplitCancelSplitRequest {
  /**
   * 需要取消拆分的订单拆分信息 ,CancelSplitInfo
   */
  cancelSplitInfos?: {
    /**
     * 卖家ID
     */
    sellerId?: number;
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 平台订单号
     */
    ptTid?: string;
    /**
     * 订单编号 ,String
     */
    tidList?: string[];
    [k: string]: any;
  }[];
  [k: string]: any;
}
/**
 * ResponseBody<OrderSplitVo> :ResponseBody
 */
export interface TradeOrderSplitCancelSplitResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * OrderSplitVo
   */
  data?: {
    /**
     * 订单查询对象 ,ErpPackageInfoVo
     */
    packageInfoVos?: {
      buyerNick?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      openId?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      memberId?: string;
      /**
       * 买家昵称唯一标识
       */
      buyerOpenUid?: string;
      isCod?: boolean;
      printInfo?: string;
      platform?: string;
      createTime?: string;
      receiverAddress?: string;
      receiverCity?: string;
      receiverDistrict?: string;
      receiverMobile?: string;
      receiverName?: string;
      receiverPhone?: string;
      receiverState?: string;
      receiverTown?: string;
      userId?: string;
      sellerNick?: string;
      platformId?: string;
      togetherId?: string;
      shipListPrintStatus?: string;
      waybillPrintStatus?: string;
      mainTid?: string;
      orderCode?: string;
      isSuccess?: boolean;
      errorMsg?: string;
      /**
       * TradeVo
       */
      trades?: {
        /**
         * 手工单系统tid，主要用来申请单号
         */
        realTid?: string;
        /**
         * 手工单发货信息
         */
        shipInfo?: string;
        /**
         * 发件人
         */
        senderName?: string;
        /**
         * 发件人电话
         */
        senderPhone?: string;
        /**
         * 发件人手机
         */
        senderMobile?: string;
        /**
         * sender_province
         */
        senderProvince?: string;
        /**
         * sender_city
         */
        senderCity?: string;
        /**
         * sender_county
         */
        senderCounty?: string;
        /**
         * 发件人地址
         */
        senderAddress?: string;
        sellerId?: string;
        buyerMessage?: string;
        buyerNick?: string;
        /**
         * 买家昵称唯一标识
         */
        buyerOpenUid?: string;
        createTime?: string;
        invoiceName?: string;
        invoiceTitle?: string;
        invoiceKind?: string;
        invoiceType?: string;
        isChange?: boolean;
        isMendian?: boolean;
        isCod?: boolean;
        isPrintFhd?: string;
        isPrintKdd?: string;
        payTime?: string;
        /**
         * OrderVo
         */
        orders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          [k: string]: any;
        }[];
        /**
         * OrderVo
         */
        giftOrders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          [k: string]: any;
        }[];
        gifts?: string;
        /**
         * 实收金额
         */
        receivedPayment?: string;
        payment?: string;
        discountAmount?: string;
        postFee?: string;
        receiverMobile?: string;
        receiverCity?: string;
        receiverDistrict?: string;
        receiverPhone?: string;
        receiverAddress?: string;
        receiverName?: string;
        receiverState?: string;
        receiverTown?: string;
        receiverZip?: string;
        sellerFlag?: string;
        userId?: string;
        sellerNick?: string;
        platform?: string;
        sellerMemo?: string;
        ignoreType?: number;
        sellerMemoFlag?: string;
        sellerMemoFlagName?: string;
        tid?: string;
        ptTid?: string;
        totalFee?: string;
        status?: string;
        gift?: string;
        type?: string;
        sendRemindFlag?: number;
        sendRemindHour?: string;
        isAddCostSF?: boolean;
        /**
         * OrderPromiseDetailVo
         */
        orderPromiseDetailVo?: {
          orderPromiseKdCode?: string;
          orderPromiseKdName?: string;
          /**
           * 指定包材
           */
          orderPromiseBc?: string;
          orderPromiseDeliveryTime?: number;
          [k: string]: any;
        };
        freeSF?: number;
        /**
         * 商家优惠金额
         */
        sellerDiscount?: string;
        distributorTid?: string;
        /**
         * 平台折扣金额
         */
        platformDiscount?: string;
        /**
         * 宝贝数量：取订单中的商品总数量
         */
        totalNums?: number;
        /**
         * 宝贝种类
         */
        itemCateNum?: number;
        /**
         * 是否是预发货
         */
        isPreShip?: boolean;
        isPending?: boolean;
        isCancelSend?: boolean;
        isMatchCancelSend?: boolean;
        imel?: string;
        deviceSn?: string;
        overseaTracing?: string;
        expressName?: string;
        /**
         * 催发货标记0无催发货，1有催发货；
         */
        urge?: number;
        /**
         * 催发货时间
         */
        urgeShippingTime?: string;
        /**
         * 预发货任务id
         */
        preShipId?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单(该参数为map)
         */
        sellerFlagSys?: {
          /**
           * String
           */
          mapKey?: {
            [k: string]: any;
          };
          /**
           * Integer
           */
          mapValue?: {
            [k: string]: any;
          };
          [k: string]: any;
        };
        /**
         * 标识当前订单是否存在退款，不做存储
         */
        hasRefund?: boolean;
        refundStatus?: string;
        totalWeight?: string;
        /**
         * 快递单号 ,String
         */
        ydNoSet?: string[];
        /**
         * 多包裹运单数量
         */
        multiPackYdCount?: number;
        /**
         * 快递公司编号
         */
        logisticsId?: string;
        /**
         * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
         */
        stockOutHandleStatus?: number;
        /**
         * 是否缺货0-无缺货处理1：有缺货处理
         */
        isStockOut?: number;
        /**
         * 预售时间
         */
        preSaleTime?: string;
        /**
         * 是否为预售商品1表示是0表示否
         */
        isPreSale?: number;
        /**
         * 风控状态（值为1则为风控订单，值为0则为正常订单）
         */
        riskControlStatus?: string;
        receiverNameMask?: string;
        receiverPhoneMask?: string;
        receiverAddressMask?: string;
        receiverMobileMask?: string;
        idxEncodeReceiverMobile?: string;
        idxEncodeReceiverName?: string;
        idxEncodeReceiverAddress?: string;
        encodeTid?: string;
        /**
         * 订单调fullInfo接口的时间
         */
        tidFullInfoTime?: string;
        /**
         * 天猫直送，值true或者false
         */
        tmallDelivery?: boolean;
        /**
         * 3PL有时效订单标，值true或者false
         */
        threePlTiming?: boolean;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        deliveryCps?: string;
        /**
         * 取
         */
        cpCode?: string;
        /**
         * cpCode转cpName
         */
        cpName?: string;
        /**
         * 是否催发货1：催发货0：非催发货
         */
        isUrgeDelivery?: number;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        caid?: string;
        /**
         * trade_fromWAP,JHS交易内部来源
         */
        tradeFrom?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
         */
        fenxiaos?: number;
        payHours?: number;
        receiverSecret?: string;
        mobileSecret?: string;
        timingPromise?: string;
        promiseService?: string;
        esDate?: string;
        esRange?: string;
        osDate?: string;
        osRange?: string;
        cutoffMinutes?: string;
        esTime?: string;
        deliveryTime?: string;
        collectTime?: string;
        sendTime?: string;
        signTime?: string;
        dispatchTime?: string;
        /**
         * value=logistics_upgrade为天猫物流升级订单
         */
        asdpBizType?: string;
        /**
         * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
         */
        asdpAds?: string;
        /**
         * 天猫送货上门快递名称
         */
        sendExName?: string;
        /**
         * 天猫送货上门快递编码
         */
        sendExCode?: string;
        /**
         * 全渠道商品通相关字段
         */
        omnichannelParam?: string;
        /**
         * 是否是海外购
         */
        isThreePl?: boolean;
        /**
         * 是否风险留言订单
         */
        pbly?: boolean;
        /**
         * 家装订单
         */
        tmserSpu?: boolean;
        /**
         * 淘宝后台getShippingType
         */
        shippingType?: string;
        /**
         * 淘宝后台getShippingType对应名称，后期
         */
        shippingName?: string;
        deliveryTypeDesc?: string;
        /**
         * 订单类型交易方式,一口价,送礼,赠品领取,货到付款,定金预售
         */
        tradeType?: string;
        /**
         * 支付类型
         */
        payType?: string;
        biz?: string;
        /**
         * 是否为乡镇订单
         */
        hasTown?: number;
        /**
         * 打印时间
         */
        printTime?: string;
        /**
         * 发货时间
         */
        shipTime?: string;
        /**
         * 提示手动合单的判断标示
         */
        mergeBuyerNick?: string;
        noGoodsLink?: boolean;
        goodsWarn?: boolean;
        firstSend?: boolean;
        smartSelectExpress?: string;
        labelstatus?: number;
        /**
         * 0未打印1全部打印
         */
        labelPrintStatus?: number;
        promiseDeliveryTime?: boolean;
        /**
         * PromiseLogistics
         */
        promiseLogisticsList?: {
          company?: string;
          exCode?: string;
          [k: string]: any;
        }[];
        wrapperDescription?: boolean;
        duoduoWholesale?: boolean;
        shipHold?: boolean;
        jdStoreOrderType?: number;
        jdOrderShipType?: number;
        /**
         * 额外运单 ,ExtraDeliveryVO
         */
        extraDeliveryList?: {
          /**
           * 快递运单号
           */
          trackingNumber?: string;
          /**
           * 快递公司id
           */
          logisticsId?: number;
          /**
           * 快递公司名称
           */
          logisticsName?: string;
          [k: string]: any;
        }[];
        /**
         * true跨境订单false非跨境
         */
        crossBorder?: boolean;
        /**
         * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
         */
        consolidateType?: string;
        /**
         * 订单来源：HAND手工单
         */
        source?: string;
        changeAdderFlag?: number;
        /**
         * 拼团订单未完成true未完成false或者为空为正常订单
         */
        pOrderUnfinished?: boolean;
        /**
         * 订单类型
         */
        tradeTypeTagStr?: string;
        /**
         * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
         */
        openAddressId?: string;
        /**
         * 精选联盟
         */
        isSelectedAlliance?: boolean;
        /**
         * 小店自卖
         */
        isSmallStoreSelfSelling?: boolean;
        /**
         * 闪电购商品
         */
        isFlashBuyingProducts?: boolean;
        /**
         * 重新发货
         */
        isResend?: boolean;
        /**
         * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
         */
        receiverId?: string;
        /**
         * 回流订单平台类型
         */
        hlPlatformType?: string;
        /**
         * 回流订单是否加密
         */
        hlEncryptOrder?: boolean;
        /**
         * fxg承诺日达1表示是0表示否
         */
        appointmentArrival?: number;
        /**
         * 发货仓编号
         */
        outWarehouseId?: string;
        /**
         * 发货仓名称
         */
        warehouseName?: string;
        /**
         * 指定时间送达承诺时间送达
         */
        receiptDate?: string;
        /**
         * ali定制服务1表示是0表示否
         */
        aliEntryServiceInfo?: number;
        /**
         * 判断是否需要上传序列码
         */
        needSerialNumber?: boolean;
        /**
         * 达人标签订单
         */
        isAuthorOrder?: boolean;
        /**
         * 线下备注
         */
        sysMemo?: string;
        /**
         * 标签：加运费发顺丰
         */
        hasSfExpressService?: boolean;
        /**
         * 顺丰加价服务费
         */
        sfExpressFee?: string;
        /**
         * 是否线上发货
         */
        onlineShip?: boolean;
        /**
         * 是否拆单发货
         */
        isSplit?: boolean;
        [k: string]: any;
      }[];
      sendRemindFlag?: number;
      sendRemindHour?: string;
      payTime?: string;
      isPending?: boolean;
      isAddCostSF?: boolean;
      freeSF?: number;
      expressName?: string;
      sellerId?: string;
      receiverNameMask?: string;
      receiverPhoneMask?: string;
      receiverAddressMask?: string;
      receiverMobileMask?: string;
      idxEncodeReceiverMobile?: string;
      idxEncodeReceiverName?: string;
      idxEncodeReceiverAddress?: string;
      encodeTid?: string;
      caid?: string;
      /**
       * 提示手动合单的判断标示
       */
      mergeBuyerNick?: string;
      /**
       * 多包裹运单数量
       */
      multiPackYdCount?: number;
      /**
       * 是否包含预发货订单
       */
      isPreShip?: boolean;
      /**
       * String
       */
      preShipIds?: string[];
      hasRefund?: boolean;
      receiverZip?: string;
      /**
       * (该参数为map)
       */
      sellerFlagSys?: {
        /**
         * String
         */
        mapKey?: {
          [k: string]: any;
        };
        /**
         * Integer
         */
        mapValue?: {
          [k: string]: any;
        };
        [k: string]: any;
      };
      /**
       * :
       */
      tids?: string[];
      /**
       * CaiNiaoIntelliExpress
       */
      smartExpress?: {
        exCode?: string;
        exName?: string;
        [k: string]: any;
      };
      type?: string;
      /**
       * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
       */
      fenxiaos?: number;
      /**
       * 是否合单
       */
      isMerge?: string;
      /**
       * 总实收金额
       */
      totalReceivedPayment?: string;
      /**
       * 总支付金额
       */
      totalPayment?: string;
      /**
       * 总商家优惠金额
       */
      totalSellerDiscount?: string;
      /**
       * 总平台折扣金额
       */
      totalPlatformDiscount?: string;
      /**
       * 总邮费
       */
      totalPostFee?: string;
      /**
       * 总重量
       */
      totalWeight?: string;
      /**
       * 已发货的快递单号
       */
      ydNo?: string;
      /**
       * 快递单号 ,String
       */
      ydNoSet?: string[];
      /**
       * 验单发货订单号搜索先发货订单模板id
       */
      verifyOrderListShowId?: number;
      /**
       * 模版的扩展字段，里面包含了用户模版id
       */
      templateYdAttr?: string;
      /**
       * 订单标记（用于黑白订单标记）,0：正常订单（默认）,1：白订单（表明这笔订单是商家请人刷的订单）,2：黑名单（表明这笔订单是别人恶意刷的订单）
       */
      tidMark?: number;
      /**
       * OrderPromiseVo
       */
      orderPromiseVo?: {
        orderPromiseDeliveryTime?: number;
        isManyKd?: boolean;
        [k: string]: any;
      };
      /**
       * Object
       */
      orderTagList?: {
        [k: string]: any;
      }[];
      payHours?: number;
      goodsNum?: string;
      /**
       * 家装订单
       */
      tmserSpu?: boolean;
      /**
       * 异常地址
       */
      abnormalAddress?: boolean;
      /**
       * 智选快递的快递名称
       */
      ExpressName?: string;
      /**
       * 打印菜鸟面单时是否需要隐私服务
       */
      cnPrivacy?: boolean;
      receiverSecret?: string;
      mobileSecret?: string;
      /**
       * OpenAddresseeID（收件人ID）,基于收件人的信息(姓名、地址、手机号、电话）、订单创建时间、店铺、appkey加密生成的ID。,相同的收件人，在同一家店铺，在固定时间周期内（通常1个自然周）内创建的订单，OAID相同。
       */
      oaid?: string;
      /**
       * 天猫送货上门快递名称
       */
      sendExName?: string;
      /**
       * 天猫送货上门快递编码
       */
      sendExCode?: string;
      /**
       * value=logistics_upgrade为天猫物流升级订单
       */
      asdpBizType?: string;
      /**
       * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
       */
      asdpAds?: string;
      signTime?: string;
      deliveryTime?: string;
      /**
       * 是否为乡镇订单
       */
      hasTown?: number;
      /**
       * 催发货标记0无催发货，1有催发货；
       */
      urge?: number;
      /**
       * 催发货时间
       */
      urgeShippingTime?: string;
      changeAdderFlag?: number;
      noGoods?: boolean;
      noGoodsLink?: boolean;
      goodsWarn?: boolean;
      firstSend?: boolean;
      preSell?: boolean;
      /**
       * 疫情标记,0：非疫情地区,1：疫情中风险地区,2：疫情高风险地区
       */
      epidemicMark?: number;
      /**
       * 风控状态（值为1则为风控订单，值为0则为正常订单）
       */
      riskControlStatus?: string;
      /**
       * 智选快递
       */
      smartSelectExpress?: string;
      /**
       * 智选快递模板级别推荐结果、是否手动指定
       */
      smartExpressTemplate?: string;
      /**
       * 合单智选快递模板级别推荐结果、是否手动指定
       */
      packSmartExpressTemplate?: string;
      labelstatus?: number;
      /**
       * 0未打印1全部打印2部分打印
       */
      labelPrintStatus?: number;
      promiseDeliveryTime?: boolean;
      /**
       * PromiseLogistics
       */
      promiseLogisticsList?: {
        company?: string;
        exCode?: string;
        [k: string]: any;
      }[];
      wrapperDescription?: boolean;
      /**
       * 顺丰包邮
       */
      freeSf?: boolean;
      /**
       * true跨境订单false非跨境
       */
      crossBorder?: boolean;
      tradeType?: string;
      /**
       * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
       */
      consolidateType?: string;
      /**
       * 订单来源:其他平台订单,Hand手工订单
       */
      source?: string;
      /**
       * 拼团订单未完成true未完成false或者为空为正常订单
       */
      pOrderUnfinished?: boolean;
      /**
       * 分销商用户ID
       */
      distributorUserId?: number;
      /**
       * 分销商账号
       */
      distributorAccount?: string;
      /**
       * 分销商名称
       */
      distributorName?: string;
      /**
       * 分销商联系人
       */
      distributorLinkMan?: string;
      /**
       * 分销商联系电话
       */
      distributorMobile?: string;
      /**
       * 分销商备注
       */
      distributorRemark?: string;
      /**
       * 分销状态,0:申请中,1：合作中,2：拒绝,3：终止
       */
      distributorStatus?: number;
      /**
       * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
       */
      openAddressId?: string;
      /**
       * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
       */
      receiverId?: string;
      /**
       * 回流订单平台类型
       */
      hlPlatformType?: string;
      /**
       * 回流订单是否加密
       */
      hlEncryptOrder?: boolean;
      /**
       * fxg承诺日达1表示是0表示否
       */
      appointmentArrival?: number;
      /**
       * 发货仓编号
       */
      outWarehouseId?: string;
      /**
       * 发货仓名称
       */
      warehouseName?: string;
      /**
       * 指定时间送达承诺时间送达
       */
      receiptDate?: string;
      /**
       * ali定制服务1表示是0表示否
       */
      aliEntryServiceInfo?: number;
      /**
       * 是否线上发货
       */
      onlineShip?: boolean;
      /**
       * 是否拆单发货
       */
      isSplit?: boolean;
      [k: string]: any;
    }[];
    /**
     * 被取消或隐藏订单编号 ,String
     */
    cancelOrHideTidList?: string[];
    /**
     * 拆分失败的订单对象 ,RdsBatchSplitTradeResultVo
     */
    failedList?: {
      /**
       * 系统订单号
       */
      tid?: string;
      /**
       * 平台订单编号
       */
      ptTid?: string;
      /**
       * 错误信息
       */
      message?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}


/**
 * UpdateOrderExtDTO :UpdateOrderExtDTO
 */
export interface TradeUpdateOrderSerialNumberRequest {
	/**
	 * 订单编号
	 */
	tid: string;
	/**
	 * 子订单id列表
	 */
	oid: string;
	/**
	 * 店铺id
	 */
	sellerId?: number;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 商品识别码（SN码、IMEI号、ICCID码)
	 */
	productIdCode?: string;
	[k: string]: any;
  }
/**
 * ResponseBody<String> :ResponseBody
 */
export interface TradeUpdateOrderSerialNumberResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
  }

/**

 * ResponseBody<BatchUpdateSysMemoVO> :ResponseBody
 */
export interface TradeBatchUpdateSysMemoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchUpdateSysMemoVO
	 */
	data?: {
	  /**
	   * 拼接错误信息
	   */
	  errorMsg?: string;
	  /**
	   * 订单修改过后的线下备注集合 ,TradeSysMemoVO
	   */
	  tradeSysMemoVOList?: {
		/**
		 * 订单id
		 */
		tid?: string;
		/**
		 * 系统备注
		 */
		sysMemo?: string;
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * BatchUpdateSysMemoDTO :BatchUpdateSysMemoDTO
 */
export interface TradeBatchUpdateSysMemoRequest {
	/**
	 * 快捷备注追加位置,0:添加至原备注之后1:覆盖原备注2:添加至原备注之前,默认是追加至原备注之后
	 */
	appendType?: number;
	/**
	 * 用户快捷备注内容
	 */
	userQuickNotesContent?: string;
	/**
	 * 订单ID列表 ,String
	 */
	tidList?: string[];
	[k: string]: any;
  }

/**
 * UpdateSysMemoPicDTO :UpdateSysMemoPicDTO
 */
export interface TradeUpdateSysMemoPicRequest {
  /**
   * 订单编号
   */
  tid?: string;
  /**
   * 线下备注图片路径
   */
  sysMemoPic?: string;
  [k: string]: any;
}
/**
 * ResponseBody<UpdateSysMemoPicVO> :ResponseBody
 */
export interface TradeUpdateSysMemoPicResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * UpdateSysMemoPicVO
   */
  data?: {
    /**
     * 订单编号
     */
    tid?: string;
    /**
     * 线下备注图片路径
     */
    sysMemoPic?: string;
    [k: string]: any;
  };
  [k: string]: any;
}


/**
 * ResponseBody<String> :ResponseBody
 */
export interface TradeAddUserQuickNotesResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
  }

/**
 * SaveUserQuickNotesDTO :SaveUserQuickNotesDTO
 */
export interface TradeAddUserQuickNotesRequest {
	/**
	 * 快捷备注内容
	 */
	content?: string;
	[k: string]: any;
  }

/**
 * DeleteUserQuickNotesDTO :DeleteUserQuickNotesDTO
 */
export interface TradeDeleteUserQuickNotesRequest {
	/**
	 * 快捷备注ID
	 */
	notesId?: number;
	[k: string]: any;
  }
/**
 * ResponseBody<String> :ResponseBody
 */
export interface TradeDeleteUserQuickNotesResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
  }

/**
 * ResponseBody<List<UserQuickNotesVO>> :ResponseBody
 */
export interface TradeGetUserQuickNotesListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * UserQuickNotesVO
	 */
	data?: {
	  /**
	   * 备注id
	   */
	  id?: number;
	  /**
	   * 排序
	   */
	  sort?: number;
	  /**
	   * 快捷备注内容
	   */
	  content?: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }

/**
 * BatchUpdateUserQuickNotesDTO :BatchUpdateUserQuickNotesDTO
 */
export interface TradeBatchUpdateUserQuickNotesRequest {
	/**
	 * 业务类型1：订单线下备注
	 */
	bizType?: number;
	/**
	 * 变更详情 ,UpdateUserQuickNodesDTO
	 */
	notesDTOList?: {
	  /**
	   * 快捷备注ID
	   */
	  notesId?: number;
	  /**
	   * 快捷备注顺序
	   */
	  sort?: number;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }


/**
 * ResponseBody<String> :ResponseBody
 */
export interface TradeBatchUpdateUserQuickNotesResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
  }

/**
 * TradeEncryptOutOrderInfoRequest :TradeEncryptOutOrderInfoRequest
 */
export interface TradeGetTradeEncryptOutOrderInfoRequest {
	/**
	 * 订单id ,String
	 */
	tid?: string[];
	/**
	 * 店铺id
	 */
	sellerId?: string;
	/**
	 * 平台：TB、TM、PDD、FXG、ALI、KSXD、HAND、JD、INNER、SPH、OTHER、XHS、C2M
	 */
	platform?: {
	  [k: string]: any;
	};
	userId?: number;
	[k: string]: any;
  }
/**
 * ResponseBody<Map<String, JSONObject>> :ResponseBody
 */
export interface TradeGetTradeEncryptOutOrderInfoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Map
	 */
	data?: {
	  [k: string]: any;
	};
	[k: string]: any;
  }
/**
 * ResponseBody<PrintSetDTO> :ResponseBody
 */
export interface TradePrintSetGetPrintSetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PrintSetDTO
	 */
	data?: IPrintSet;
	[k: string]: any;
}

export enum refluxOrderPrintSettingsEnum {
	使用菜鸟面单打印 = 1,
	使用对应平台面单打印 = 2,
}

export interface IPrintSet {
	/**
	 * @mbg.generated,主键自增ID,表字段:id
	 */
	id?: number;
	/**
	 * @mbg.generated,是否回显多单号:1是不回显;2是回显,表字段:show_multi_yd_no
	 */
	showMultiYdNo?: number;
	/**
	 * @mbg.generated,是否回显单号生成个数:1是不回显;2是回显,表字段:display_gen_yd_num
	 */
	displayGenYdNum?: number;
	/**
	 * @mbg.generated,是否展示打印份数设置:1是不展示;2是展示,表字段:display_print_num
	 */
	displayPrintNum?: number;
	/**
	 * @mbg.generated,是否合并订单,1不合并,2合并,表字段:is_merge_order
	 */
	isMergeOrder?: number;
	/**
	 * @mbg.generated,发货完成后是否隐藏订单:1是隐藏;2是不隐藏,表字段:is_hide_order_aftersend
	 */
	isHideOrderAftersend?: number;
	/**
	 * @mbg.generated,用户id,表字段:user_id
	 */
	userId?: number;
	queryConditionDTOList?: TradeSearchConditionConfig[];
	addYdNoMemo?: number;
	addYdNoOverOne?: number;
	isHidePendingTrade?: number;
	isHideRefundTrade?: number;
	isHidePartShipTrade?: number;
	showEpidemicArea?: number; // 异常地区
	groupPrintSetJsonString?: {
		openMergePrint?: number;
		printOrderByOrder?: number;
		refluxOrderPrintSettings?: refluxOrderPrintSettingsEnum;
		orderMatchSetting?: any;
		controlsSortSet?: string;
		isHideClosedGoods?: number;
		isHideRefundingGoods?: number;
		isHideDropShippingGoods?: number;
		isHideAllDropShipTrade?: number;
		[k: string]: any;
	}; // 后续新增字段都存这里
	[k: string]: any;
}

export interface TradePrintSetUpdatePrintSetRequest extends IPrintSet { }

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradePrintSetUpdatePrintSetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: any;
	[k: string]: any;
}
export interface PrintGetGroupInfoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: any;
	[k: string]: any;
}
export interface ISendSubTrades {
	togetherId?: string;
	/**
	 * 母单号，针对快运场景
	 */
	pYdNo?: string;
	/**
	 * 是否拆单发货，默认非拆单
	 */
	split?: boolean;
	/**
	 * 多包裹发货仅pddfxgtb支持
	 */
	multiPack?: boolean;
	/**
	 * 是否为货到付款订单，1标识是，0标识否，默认非货到付款订单
	 */
	cod?: number;
	/**
	 * 发货方式，1自己联系;2在线发货;3虚拟发货；4自动发货;53PL发货;6家装发货
	 */
	sendType?: string;
	/**
	 * 用户id(注册账号)
	 */
	userId?: number;
	/**
	 * 用户id(单号的使用者，也就是说订单的归属者)
	 */
	sellerId?: number;
	/**
	 * 平台类型
	 */
	platform?: string;
	/**
	 * 买家账号
	 */
	buyerNick?: string;
	/**
	 * 收件省
	 */
	receiverProvince?: string;
	/**
	 * 收件市
	 */
	receiverCity?: string;
	/**
	 * 收件区
	 */
	receiverCounty?: string;
	/**
	 * 收件地址
	 */
	receiverAddress?: string;
	/**
	 * 收件街道
	 */
	receiverTown?: string;
	/**
	 * 收件人
	 */
	receiverName?: string;
	/**
	 * 手机
	 */
	mobile?: string;
	/**
	 * 固定电话
	 */
	tel?: string;
	/**
	 * 快递id
	 */
	exId?: number;
	/**
	 * 快递code
	 */
	exCode?: string;
	/**
	 * 快递名
	 */
	exName?: string;
	/**
	 * 快递单号
	 */
	exNumber?: string;
	/**
	 * 多包裹发货的字段 ,String
	 */
	exNumberList?: string[];
	/**
	 * 商品数量
	 */
	goodsNum?: number;
	/**
	 * 重量
	 */
	weight?: string;
	/**
	 * 拆单子订单串。oid1,ords,oid3
	 */
	oids?: string;
	/**
	 * tid
	 */
	tid?: string;
	/**
	 * 买家留言
	 */
	buyerMsg?: string;
	/**
	 * 卖家备注旗帜类型
	 */
	sellerFlag?: number;
	/**
	 * 卖家备注
	 */
	sellerMemo?: string;
	/**
	 * 发货内容
	 */
	info?: string;
	/**
	 * 打印序号
	 */
	printNum?: string;
	/**
	 * 包裹号
	 */
	packageId?: number;
	/**
	 * 快递单类型1普通面单,2网点,3云栈电子面单
	 */
	kddType?: number;
	/**
	 * 是哪个页面的操作0：单打1：批打2：自动发货3：自动自动发货4：手工订单5厂家代发
	 */
	optionType?: number;
	/**
	 * 订单实付金额
	 */
	payment?: string;
	// * 发货来源
	waybillNoSource?: ValueOf<typeof WaybillNoSource>;
	/**
	 * BatchSendOrderDTO
	 */
	orders?: {
		/**
		 * 商品数量
		 */
		num?: number;
		/**
		 * numIid
		 */
		numIid?: string;
		skuId?: string;
		tid?: string;
		oid?: string;
		/**
		 * 子订单实付金额
		 */
		payment?: string;
		systemNumIid?: number;
		systemSkuId?: number;
		[k: string]: any;
	}[];
	/**
	 * overseaTracing（海淘溯源码id）内容。
	 */
	overseaTracing?: string;
	/**
	 * PDD支持imei（手机串号
	 */
	imei?: string;
	/**
	 * PDD支持deviceSn（设备序列号）
	 */
	deviceSn?: string;
	/**
	 * 发货个性内容,<p>,针对pdd平台：支持imei（手机串号），deviceSn（设备序列号），overseaTracing（海淘溯源码id）内容。,形如："imei=识别码1,识别码2;"、"deviceSn=序列号1,序列号2;"、"overseaTracing=溯源码1,溯源码2;"。,以英文逗号","分割串号，以英文分号";"分割不同参数内容。上传时请严格区分imei，deviceSn和overseaTracing，,其中overseaTracing（海淘溯源码id）要求海淘商品在支持溯源的情况下必传。错传漏传将会导致发货失败。,详细参考：https:open.pinduoduo.comapplicationdocumentapi?id=pdd.logistics.online.send,针对淘宝平台：
	 */
	feature?: string;
	/**
	 * 手机号索引串
	 */
	idxEncodeReceiverMobile?: string;
	/**
	 * 加密详情索引串
	 */
	idxEncodeReceiverAddress?: string;
	/**
	 * 加密收件人索引串,,@return
	 */
	idxEncodeReceiverName?: string;
	/**
	 * 收件人姓名脱敏
	 */
	receiverNameMask?: string;
	/**
	 * 收件人手机号脱敏
	 */
	receiverPhoneMask?: string;
	/**
	 * 收件人地址脱敏
	 */
	receiverAddressMask?: string;
	/**
	 * 加密字段，不同字段归属平台字段可能不一致，比如淘宝为oaid
	 */
	caid?: string;
	/**
	 * 加密串tid
	 */
	encodeTid?: string;
	[k: string]: any;
}
/**
 * List<BatchSendTradeDTO>
 */
export type TradeBatchSendRequest = {
	/**
	 * 如果存在合单，那么需要将合并订单分开传入，格式如下,[,[{tid:1,exNumber:444555,exCode:YTO}],,[{tid:2,exNumber:444556,exCode:YTO}],,[{tid:3,exNumber:444556,exCode:YTO},{tid:4,exNumber:444556,exCode:YTO}],] ,BatchSendTradeDTO
	 */
	subTrades?: ISendSubTrades[];
	sendFrom?: 'PD' | 'LABEL',
	stockStatus?: string;
	optionType?: TradeOptEnum;
}[];

export interface ISendOrders {
	/**
	 * 商品数量
	 */
	num?: number;
	/**
	 * numIid
	 */
	numIid?: string;
	tid?: string;
	oid?: string;
	weight?: string;
	/**
	 * 系统商品id
	 */
	sysItemId?: number;
	/**
	 * 系统规格id
	 */
	sysSkuId?: string;
	[k: string]: any;
}

export interface TradeBatchSendData {
	sellerId?: number;
	platform?: string;
	tid?: string;
	oids?: string;
	isSplit?: boolean;
	togetherId?: string;
	/**
	 * 结果标识,100发货成功，101,部分发货成功，102拆单发货成功，103发货失败
	 */
	result?: number;
	isSuccess?: boolean;
	/**
	 * 失败信息
	 */
	message?: string;
	/**
	 * 买家旺旺
	 */
	buyerNick?: string;
	/**
	 * 子订单发货结果记录 ,BatchSendTradeResultDTO
	 */
	subTradeInfos?: ISendSubTrades[];
	[k: string]: any;
}
/**
 * ResponseBody<List<BatchSendTradeResultDTO>> :ResponseBody
 */
export interface TradeBatchSendResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchSendTradeResultDTO
	 */
	data?: TradeBatchSendData[];
	[k: string]: any;
}

export interface TradePddControlDecryptV1ReceiverInfoRequest {
	tid: string;
	mallId: string;
	decryptContent: string;
	[k: string]: any;
}

/**
 * ResponseBody<PddOrderDecryptResponse> :ResponseBody
 */
export interface TradePddControlDecryptV1ReceiverInfoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PddOrderDecryptResponse
	 */
	data?: ITradePddOrderDecryptInfo;
	[k: string]: any;
}

export interface ITradePddOrderDecryptInfo {
	/**
	 * 页面js展示订单列表的唯一健，用于响应时候回填页面表格使用
	 */
	page_table_id?: string;
	/**
	 * 请求id用于排查问题
	 */
	request_id?: string;
	/**
	 * 拼多多开平应用client_id！！！不能为空，必须设置！！！
	 */
	client_id?: string;
	/**
	 * 平多多店铺编号！！！不能为空，必须设置！！！
	 */
	mall_id?: number;
	/**
	 * 拼多多订单号！！！不能为空，必须设置！！！
	 */
	order_sn?: string;
	/**
	 * 拼多多订单明文 ,PddOrderCipher
	 */
	order_info?: {
		/**
		 * 收件人姓名密文
		 */
		receiver_name?: string;
		/**
		 * 收件人电话密文
		 */
		receiver_phone?: string;
		/**
		 * 收件人地址,不拼接省市区密文
		 */
		receiver_address?: string;
		/**
		 * 收件详细地址密文
		 */
		address?: string;
		/**
		 * 身份证姓名密文
		 */
		id_card_name?: string;
		/**
		 * 身份证号密文
		 */
		id_card_num?: string;
		/**
		 * 支付单号密文
		 */
		pay_no?: string;
		/**
		 * 快递运单号密文
		 */
		tracking_number?: string;
		/**
		 * 卡号、卡密密文
		 */
		card_info_list?: string;
		/**
		 * 支付申报订单号密文
		 */
		inner_transaction_id?: string;
		[k: string]: any;
	};
	/**
	 * 拼多多开放平台网关的错误编号
	 */
	error_code?: string;
	/**
	 * 拼多多开放平台网关的错误提示
	 */
	error_msg?: string;
	/**
	 * 应用自定义的错误
	 */
	sub_msg?: string;
	sub_code?: string;
	decrypt_report_type?: number;
	[k: string]: any;
}

export interface TradeUserFastShopSyncRequest {
	syncShopInfos?: {
	  platform?: string;
	  sellerId?: number;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }

/**
 * ResponseBody<List<SyncShopResultVO>> :ResponseBody
 */
export interface TradeUserFastShopSyncResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * SyncShopResultVO
	 */
	data?: IFastShopSyncItem[];
	[k: string]: any;
}

export interface IFastShopSyncItem {
	userId?: number;
	sellerId?: number;
	sellerNick?: string;
	traceId?: string;
	errorMsg?: string;
	platform?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeUserUpdateShopSyncConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}

/**
 * List<UpdateMemoReq>
 */
export type TradeBatchUpdateMemoRequest = {
	tid?: string;
	memo?: string;
	sellerFlag?: string;
	platform?: string;
	sellerId?: string;
	isDistributorUserPushRefund?: boolean;
	[k: string]: any;
  hasPrintTrigger?:boolean
}[];

/**
 * ResponseBody :ResponseBody
 */
export interface TradeBatchUpdateMemoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	[k: string]: any;
}

/**
 * ResponseBody<List<TradeCountForIndexVO>> :ResponseBody
 */
export interface TradeGetCountForIndexResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeCountForIndexVO
	 */
	data?: {
		/**
		 * @mbg.generated自增id,表字段:id
		 */
		platformType?: string;
		/**
		 * @mbg.generated用户id,表字段:user_id
		 */
		sellerNick?: string;
		/**
		 * @mbg.generated待打印快递单数量
		 */
		waitPrintKddCount?: number;
		/**
		 * @mbg.generated待打印发货单数量
		 */
		waitPrintFhdCount?: number;
		/**
		 * @mbg.generated待发货订单数量
		 */
		waitSendCount?: number;
		/**
		 * @mbg.generated今日发货订单数
		 */
		todaySendCount?: number;
		[k: string]: any;
	}[];
}
/**
 * ResponseBody<List<TradeQueryWarningCountForShipFail>> :ResponseBody
 */

export interface TradeQueryWarningCountForShipFailResponse {
  success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeCountForIndexVO
	 */
	data?: string;
}
/**
 * ResponseBody<List<TradeQueryWarningOrderSummaryApi>> :ResponseBody
 */
export interface TradeQueryWarningOrderSummaryResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeCountForIndexVO
	 */
	data?: {
		/**
		 * 总数
		 */
		summaryNum?: string;
		/**
		 * 已申请单号未打印数
		 */
		getYdAndNoPrintNum?: string;
		/**
		 * 已打印未发货数
		 */
		printedAndUnShipNum?: string;
		[k: string]: any;
	}[];
}

export interface TradeQuerySendTimeoutAbnormalInfoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeCountForIndexVO
	 */
	data?: {
		/**
		 * 总数
		 */
		tradeCount?:number,
    timeoutTradeCount?:number,
    waitSendTradeCount?:number,
	};
}

/**
 * ResponseBody<UserColumnConfigVO> :ResponseBody
 */
export interface TradeUserGetUserColumnConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * UserColumnConfigVO
	 */
	data?: {
		customColumnConfig?: string;
		defaultColumnConfig?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * UserColumnConfigDTO :UserColumnConfigDTO
 */
export interface TradeUserAddUserColumnConfigRequest {
	/**
	 * @mbg.generated表字段:id
	 */
	id?: number | string;
	/**
	 * @mbg.generated子账号用户id,表字段:sub_user_id
	 */
	subUserId?: number;
	/**
	 * @mbg.generated用户id,表字段:user_id
	 */
	userId?: number;
	/**
	 * @mbg.generated列字段信息,表字段:config_txt
	 */
	configTxt?: string;
	/**
	 * @mbg.generated逻辑删除,表字段:enable_status
	 */
	enableStatus?: number;
	/**
	 * @mbg.generated创建时间,表字段:created
	 */
	created?: string;
	/**
	 * @mbg.generated修改时间,表字段:modified
	 */
	modified?: string;
	[k: string]: any;
}

/**
 * ResponseBody<List<TradeCountForIndexVO>> :ResponseBody
 */
export interface TradeUserAddUserColumnConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: number;
	[k: string]: any;
}

/**
 * UpdatePendingReq :UpdatePendingReq
 */
export interface TradeBatchUpdatePendingStatusRequest {
	/**
	 * 多个订单号用,隔开 ,String
	 */
	tids?: string[];
	/**
	 * 挂起状态：0：未挂起1：已挂起
	 */
	isPending?: number;
	[k: string]: any;
}

/**
 * ResponseBody :ResponseBody
 */
export interface TradeBatchUpdatePendingStatusResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	[k: string]: any;
}

/**
 * UpdateTradeFlagReq :UpdateTradeFlagReq
 */
export interface TradeBatchUpdateTradeFlagRequest {
	/**
	 * 多个订单号用,隔开 ,String
	 */
	tids?: string[];
	/**
	 * 挂起状态：0：未挂起1：已挂起11111表示红黄绿蓝紫00000表示都没有，顺序和颜色对应01表示是否有
	 */
	bizMark?: string;
	[k: string]: any;
}

/**
 * ResponseBody :ResponseBody
 */
export interface TradeBatchUpdateTradeFlagResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	[k: string]: any;
}

/**
 * ResponseBody<String> :ResponseBody
 */
export interface TradeCodeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
}

export interface IProductSetting {
	/**
	 * @mbg.generated自增长ID,表字段:id
	 */
	id?: number;
	/**
	 * @mbg.generated系统用户子账号id,表字段:user_id
	 */
	userId?: number;
	/**
	 * @mbg.generated子账号用户id,表字段:sub_user_id
	 */
	subUserId?: number;
	/**
	 * @mbg.generated1表示单列2表示双列,表字段:show_column
	 */
	showColumn?: number;
	/**
	 * @mbg.generated是否显示商品标题0为不展示,表字段:show_item_title
	 */
	showItemTitle?: number;
	/**
	 * @mbg.generated是否显示商品简称0为不展示,表字段:show_short_title
	 */
	showShortTitle?: number;
	/**
	 * @mbg.generated是否显示商家编码0为不展示,表字段:show_outer_id
	 */
	showOuterId?: number;
	/**
	 * @mbg.generated是否显示商品图片0为不展示,表字段:show_picture
	 */
	showPicture?: number;
	/**
	 * @mbg.generated是否显示规格名称0为不展示,表字段:show_sku_title
	 */
	showSkuTitle?: number;
	/**
	 * @mbg.generated是否显示规格别名0为不展示,表字段:show_sku_alias
	 */
	showSkuAlias?: number;
	/**
	 * @mbg.generated是否显示规格编码0为不展示,表字段:show_sku_outer_id
	 */
	showSkuOuterId?: number;
	/**
	 * @mbg.generated库存预警1开启0不开启,表字段:show_stock_warn
	 */
	showStockWarn?: number;
	/**
	 * @mbg.generated删除标记,表字段:enable_status
	 */
	enableStatus?: number;
	/**
	 * @mbg.generated创建时间,表字段:created
	 */
	created?: string;
	/**
	 * @mbg.generated更新时间,表字段:modified
	 */
	modified?: string;
	showSysOuterSkuId?: number;
	showSysOuterId?: number;
	showSysPicPath?: number;
  logisticsShipTimeoutStatus?:number;
  logisticsShipTimeoutHour?:number
	[k: string]: any;
}
/**
 * ResponseBody<UserCustomSetDTO> :ResponseBody
 */
export interface TradeUserGetUserCustomSetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * UserCustomSetDTO
	 */
	data?: IProductSetting;
	[k: string]: any;
}

/**
 * UserCustomSetDTO :UserCustomSetDTO
 */
export interface TradeUserAddUserCustomSetRequest {
	/**
	 * @mbg.generated自增长ID,表字段:id
	 */
	id?: number;
	/**
	 * @mbg.generated系统用户子账号id,表字段:user_id
	 */
	userId?: number;
	/**
	 * @mbg.generated子账号用户id,表字段:sub_user_id
	 */
	subUserId?: number;
	/**
	 * @mbg.generated1表示单列2表示双列,表字段:show_column
	 */
	showColumn?: number;
	/**
	 * @mbg.generated是否显示商品标题0为不展示,表字段:show_item_title
	 */
	showItemTitle?: number;
	/**
	 * @mbg.generated是否显示商品简称0为不展示,表字段:show_short_title
	 */
	showShortTitle?: number;
	/**
	 * @mbg.generated是否显示商家编码0为不展示,表字段:show_outer_id
	 */
	showOuterId?: number;
	/**
	 * @mbg.generated是否显示商品图片0为不展示,表字段:show_picture
	 */
	showPicture?: number;
	/**
	 * @mbg.generated是否显示规格名称0为不展示,表字段:show_sku_title
	 */
	showSkuTitle?: number;
	/**
	 * @mbg.generated是否显示规格别名0为不展示,表字段:show_sku_alias
	 */
	showSkuAlias?: number;
	/**
	 * @mbg.generated是否显示规格编码0为不展示,表字段:show_sku_outer_id
	 */
	showSkuOuterId?: number;
	/**
	 * @mbg.generated库存预警1开启0不开启,表字段:show_stock_warn
	 */
	showStockWarn?: number;
	/**
	 * @mbg.generated删除标记,表字段:enable_status
	 */
	enableStatus?: number;
	/**
	 * @mbg.generated创建时间,表字段:created
	 */
	created?: string;
	/**
	 * @mbg.generated更新时间,表字段:modified
	 */
	modified?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Long> :ResponseBody
 */
export interface TradeUserAddUserCustomSetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: number;
	[k: string]: any;
}

/**
 * TradeExportConfigSaveVO :TradeExportConfigSaveVO
 */
export interface TradeExportSaveConfigRequest {
	/**
	 * 纬度，0：订单纬度,1：商品纬度,表字段:latitude
	 */
	latitude: number;
	/**
	 * 导出字段,表字段:content ,ExportField
	 */
	fields: {
		/**
		 * 字段名称
		 */
		name?: string;
		/**
		 * 字段描述
		 */
		desc?: string;
		/**
		 * 排序值，升序
		 */
		sort?: number;
		[k: string]: any;
	}[];
	[k: string]: any;
}


/**
 * TradeQueryDTO :TradeQueryDTO
 */
export interface TradeExportRequest {
	/**
	 * TB,淘宝,,TM,天猫,,PDD,拼多多,,FXG,抖音,,HAND,手工单;
	 */
	platform?: string;
	/**
	 * 店铺id
	 */
	sellerId?: string;
	startTime?: string;
	endTime?: string;
	/**
	 * 筛选时间类型1下单时间2付款时间3打印时间4发货时间
	 */
	timeType?: number;
	/**
	 * 订单状态ORDER_WAIT_PAY等待付款ORDER_PAID已付款ORDER_SHIPPED已发货,ORDER_SHIPPED_LITTLE部分发货ORDER_COMPLETED交易完成ALL_STATUS全部订单
	 */
	status?: string;
	/**
	 * 打印状态2仅快递单已打印3仅发货单已打印4均已打印0均未打印
	 */
	printStatus?: number;
	/**
	 * 自定义的地址关键字
	 */
	selValue?: string;
	/**
	 * 精准匹配省市区如：[{"name":"辽宁省","childList":[]},{"name":"广西壮族自治区","childList":[]}]
	 */
	areaJson?: string;
	/**
	 * 省市区是否包含：0不包含1包含
	 */
	areaContain?: number;
	/**
	 * 1有买家留言2有卖家备注3有买家留言+有卖家备注4没有留言且没有备注5红6黄7绿8蓝色9紫色10有留言或备注11无留言12无备注
	 */
	flagValue?: string;
	/**
	 * 1,2_1表示包含红黄旗从自定义备注接口找0表示不包含1表示包含
	 */
	flagSelValue?: string;
	/**
	 * 快捷查询,合并订单merge;,非合并订单noMerge,有运费订单fare;,无运费订单noFare;,乡镇订单town,非乡镇订单noTown,有发票invoice,无发票noInvoice,有挂起pending,无挂起noPending
	 */
	quickQuery?: string;
	/**
	 * {@codetradeQuery.getSellAttribute},查询销售属性,1:单商品单规格单件,,2:单商品单规格多件,,3:单商品多规格多件,,4:多商品多规格多件
	 */
	sellAttribute?: string;
	/**
	 * 商品名称编码简称id
	 */
	shortNameIncluding?: string;
	/**
	 * 规格名称规格别名查询
	 */
	skuIncluding?: string;
	/**
	 * 买家昵称,隔开
	 */
	buyerNick?: string;
	/**
	 * 收件人,隔开
	 */
	receiveName?: string;
	/**
	 * 手机号,隔开
	 */
	mobile?: string;
	/**
	 * 订单编号,隔开
	 */
	tid?: string;
	/**
	 * 运单号,隔开
	 */
	sid?: string;
	/**
	 * 0快递1物流
	 */
	logisticsType?: string;
	/**
	 * REFUND_SUCCESSED("退款成功"),,REFUND_ING("退款中"),,NOT_REFUND("无售后或售后关闭");
	 */
	refundStatus?: string;
	/**
	 * 订单总金额1-100
	 */
	payment?: string;
	/**
	 * 宝贝包含或不包含标识：,1包含,0不包含
	 */
	equalFlag?: number;
	/**
	 * 订单重量范围5-10
	 */
	weightRange?: string;
	/**
	 * 订单总数量子订单数量5-10
	 */
	orderRange?: string;
	/**
	 * 商品总数量1-50
	 */
	goodsTotalNum?: string;
	/**
	 * 商品种类1-50
	 */
	goodsTypeNum?: string;
	/**
	 * 是否精确精准就是false非精准就是true
	 */
	isPrecise?: boolean;
	/**
	 * 是否精确到订单精准就是false非精准就是true
	 */
	isPreciseByTrade?: boolean;
	/**
	 * 给mock用,此值传1会有订单
	 */
	testStatus?: number;
	pageNo?: number;
	pageSize?: number;
	/**
	 * 1:先付款的在前边,,2后付款的在前边,,3先下单的在前边,,4后下单的在前边,,5.将相同商品挨在一起排列,,6实际支付金额大的在前边，,7实际支付金额小的在前边,,8.数量大的在前边,9.数量小的在前边,10.按订单发货时间，先超时的在前边,11.按订单发货时间，先超时的在后边,12订单修改时间升序，先修改的在前边,13订单修改时间降序，后修改的在前边,14省份首拼字母靠前的在前边,16按商家编码，升序
	 */
	tidOrderType?: number;
	/**
	 * MultiShopDTO
	 */
	multiShopS?: {
		sellerId?: number;
		platform?: string;
		[k: string]: any;
	}[];
	/**
	 * 异常订单0异常1无异常
	 */
	abnormalStatus?: number;
	[k: string]: any;
}


/**
 * ResponseBody<TradeExportConfigQueryVO> :ResponseBody
 */
export interface TradeExportGetConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeExportConfigQueryVO
	 */
	data?: {
		/**
		 * 纬度，0：订单纬度,1：商品纬度
		 */
		latitude?: number;
		/**
		 * 可选字段 ,ExportField
		 */
		selectableExportFields?: {
			/**
			 * 字段名称
			 */
			name?: string;
			/**
			 * 字段描述
			 */
			desc?: string;
			/**
			 * 排序值，升序
			 */
			sort?: number;
			[k: string]: any;
		}[];
		/**
		 * 导出字段 ,ExportField
		 */
		exportFields?: {
			/**
			 * 字段名称
			 */
			name?: string;
			/**
			 * 字段描述
			 */
			desc?: string;
			/**
			 * 排序值，升序
			 */
			sort?: number;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * List<DecryDataReq>
 */
export type TradeBatchDecryDataRequest = {
	/**
	 * 店铺id
	 */
	sellerId?: string;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 平台淘宝必须传递这个
	 */
	caid?: string;
	tid?: string;
	/**
	 * 业务场景批打手动解密展示传递100
	 */
	sceneCode?: string;
	/**
	 * 对应平台的密文
	 */
	encodeReceiverPhone?: string;
	/**
	 * 对应平台的密文
	 */
	encodeMobile?: string;
	/**
	 * 对应平台的密文
	 */
	encodeReceiverName?: string;
	/**
	 * 对应平台的密文
	 */
	encodeReceiverAddress?: string;
	[k: string]: any;
}[];

/**
 * ResponseBody<Map<String, DecryDataVO>> :ResponseBody
 */
export interface TradeBatchDecryDataResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Map
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * ManualMergeTradeRequestVo :ManualMergeTradeRequestVo
 */
export interface TradeManualMergeTradeRequest {
	/**
	 * 合单的主订单号
	 */
	mainTid?: string;
	/**
	 * 订单号串，逗号分隔，包含主订单
	 */
	tids?: string;
	/**
	 * 用户id(单号的使用者，也就是说订单的归属者)
	 */
	sellerId?: string;
	/**
	 * 平台类型
	 */
	platform?: string;
	/**
	 * 合单主单收件省
	 */
	receiverProvince?: string;
	/**
	 * 合单主单收件市
	 */
	receiverCity?: string;
	/**
	 * 合单主单收件区
	 */
	receiverCounty?: string;
	/**
	 * 合单主单收件地址
	 */
	receiverAddress?: string;
	/**
	 * 合单主单收件街道
	 */
	receiverTown?: string;
	/**
	 * 合单主单收件人
	 */
	receiverName?: string;
	/**
	 * 合单主单邮编
	 */
	receiverZip?: string;
	/**
	 * 合单主单手机
	 */
	receiverMobile?: string;
	/**
	 * 合单主单固定电话
	 */
	receiverPhone?: string;
	/**
	 * 合单主单手机号索引串,解密后不传
	 */
	idxEncodeReceiverMobile?: string;
	/**
	 * 合单主单加密详情索引串,解密后不传
	 */
	idxEncodeReceiverAddress?: string;
	/**
	 * 合单主单加密收件人索引串,解密后不传
	 */
	idxEncodeReceiverName?: string;
	/**
	 * 合单主单收件人姓名脱敏,解密后不传
	 */
	receiverNameMask?: string;
	/**
	 * 合单主单收件人手机号脱敏,解密后不传
	 */
	receiverPhoneMask?: string;
	/**
	 * 合单主单收件人地址脱敏,解密后不传
	 */
	receiverAddressMask?: string;
	/**
	 * 合单主单加密字段，不同字段归属平台字段可能不一致，比如淘宝为oaid,如果订单解密了，一定不要再传入此字段
	 */
	caid?: string;
	[k: string]: any;
}

/**
 * ResponseBody<ErpPackageInfoVo> :ResponseBody
 */
export interface TradeManualMergeTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ErpPackageInfoVo
	 */
	data?: IPackage[];
	[k: string]: any;
}


/**
 * TradeOperateLogDTO :TradeOperateLogDTO
 */
export interface TradeOperateLogRequest {
	id?: number;
	/**
	 * TradeOperateTypeEnum,操作类型：1打印快递单2发货3订单合并4卖家备注5收件人信息,表字段:operate_type
	 */
	operateType?: number | string;
	/**
	 * @mbg.generated,操作内容,表字段:operate_content
	 */
	operateContent?: string;
	/**
	 * @mbg.generated,0失败1成功,表字段:operate_result
	 */
	operateResult?: number;
	/**
	 * @mbg.generated,操作IP地址,表字段:operate_ip
	 */
	operateIp?: string;
	/**
	 * @mbg.generated,操作人id,表字段:operate_id
	 */
	operateId?: number;
	/**
	 * @mbg.generated,操作人,表字段:operate_name
	 */
	operateName?: string;
	/**
	 * @mbg.generated,表字段:created
	 */
	created?: string;
	/**
	 * @mbg.generated,操作tid多个英文逗号分隔,表字段:tids
	 */
	tids?: string;
	/**
	 * @mbg.generated,错误信息,表字段:error_msg
	 */
	errorMsg?: string;
	/**
	 * 页码
	 */
	pageNo?: number;
	/**
	 * 每页大小
	 */
	pageSize?: number;
	[k: string]: any;
}

/**
 * ResponseBody<PageList<TradeOperateLogDTO>> :ResponseBody
 */
export interface TradeOperateLogResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PageList
	 */
	data?: {
		pageNo?: number;
		pageSize?: number;
		total?: number;
		/**
		 * T
		 */
		list?: {
			id?: number;
			/**
			 * @mbg.generated,表字段:user_id
			 */
			userId?: number;
			/**
			 * TradeOperateTypeEnum,操作类型：1打印快递单2发货3订单合并4卖家备注5收件人信息,表字段:operate_type
			 */
			operateType?: number;
			/**
			 * @mbg.generated,操作内容,表字段:operate_content
			 */
			operateContent?: string;
			/**
			 * @mbg.generated,0失败1成功,表字段:operate_result
			 */
			operateResult?: number;
			/**
			 * @mbg.generated,操作IP地址,表字段:operate_ip
			 */
			operateIp?: string;
			/**
			 * @mbg.generated,操作人id,表字段:operate_id
			 */
			operateId?: number;
			/**
			 * @mbg.generated,操作人,表字段:operate_name
			 */
			operateName?: string;
			/**
			 * @mbg.generated,表字段:created
			 */
			created?: string;
			/**
			 * @mbg.generated,表字段:modified
			 */
			modified?: string;
			/**
			 * @mbg.generated,操作tid多个英文逗号分隔,表字段:tids
			 */
			tids?: string;
			/**
			 * @mbg.generated,错误信息,表字段:error_msg
			 */
			errorMsg?: string;
			/**
			 * 页码
			 */
			pageNo?: number;
			/**
			 * 每页大小
			 */
			pageSize?: number;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * List<QueryStockInfoReq>
 */
export type TradeQueryStockInfoRequest = {
	/**
	 * 系统商品id
	 */
	sysItemId?: number | string;
	/**
	 * 系统规格id
	 */
	sysSkuId?: number | string;
	[k: string]: any;
}[];

/**
 * ResponseBody<List<QueryStockInfoVO>> :ResponseBody
 */
export interface TradeQueryStockInfoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * QueryStockInfoVO
	 */
	data?: {
		/**
		 * 系统商品id
		 */
		sysItemId?: number;
		/**
		 * 系统规格id
		 */
		sysSkuId?: number;
		/**
		 * 库存数量
		 */
		stockNum?: number;
		/**
		 * 正品库存
		 */
		salableItemStock?: number;
		/**
		 * 可配货库存数量
		 */
		salableItemDistributableStock?: number;
		/**
		 * 正品预占数量
		 */
		salableItemPreemptedNum?: number;
		/**
		 * 是否为组合商品，1:组合商品
		 */
		isCombination?: number;
		/**
		 * 子商品信息 ,GroupRelationRecordDTO
		 */
		groupRelationRecordList?: {
			/**
			 * @mbg.generated,表字段:id
			 */
			id?: number;
			/**
			 * @mbg.generated,用户ID,表字段:user_id
			 */
			userId?: number;
			/**
			 * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
			 */
			groupSysItemId?: number;
			/**
			 * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
			 */
			groupSysSkuId?: number;
			/**
			 * @mbg.generated,系统货品id,表字段:sys_item_id
			 */
			sysItemId?: number;
			/**
			 * @mbg.generated,系统规格id,表字段:sys_sku_id
			 */
			sysSkuId?: number;
			/**
			 * @mbg.generated,组合比例,表字段:group_proportion_num
			 */
			groupProportionNum?: number;
			/**
			 * 正品库存
			 */
			salableItemStock?: number;
			/**
			 * 可配货库存数量
			 */
			salableItemDistributableStock?: number;
			/**
			 * 正品预占数量
			 */
			salableItemPreemptedNum?: number;
			/**
			 * 成本价
			 */
			costPrice?: string;
			/**
			 * 货品名称
			 */
			sysItemName?: string;
			/**
			 * 货品别名
			 */
			sysItemAlias?: string;
			/**
			 * 货品规格名称
			 */
			sysSkuName?: string;
			/**
			 * 货品规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 货品规格商家编码
			 */
			skuOuterId?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	}[];
	[k: string]: any;
}

/**
 * OperateShopReq :OperateShopReq
 */
export interface TradeUserOperateShopRequest {
	/**
	 * 类型1添加2停用3重新启用4删除
	 */
	opType?: number;
	/**
	 * 店铺ID
	 */
	sellerId?: string | number;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺名
	 */
	sellerNick?: string | number;
	[k: string]: any;
}

/**
 * List<TradeOperateLogDTO>
 */
export type TradeOperateLogAddRequest = {
	/**
	 * TradeOperateTypeEnum,操作类型：1打印快递单2发货3订单合并4卖家备注5收件人信息6强制打印7强制发货,表字段:operate_type
	 */
	operateType: number;
	/**
	 * @mbg.generated,操作内容,表字段:operate_content
	 */
	operateContent: string;
	/**
	 * @mbg.generated,0失败1成功,表字段:operate_result
	 */
	operateResult: number;
	/**
	 * @mbg.generated,操作tid多个英文逗号分隔,表字段:tids
	 */
	tids: string;
	[k: string]: any;
}[];

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeOperateLogAddResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}


/**
 * TradeSensitiveInfoDTO :TradeSensitiveInfoDTO
 */
export interface TradeInfoBlurRequest {
	mobile?: string;
	receiver?: string;
	receiverAddress?: string;
	newMobile?: string;
	newReceiver?: string;
	newReceiverAddress?: string;
	[k: string]: any;
}

/**
 * ResponseBody<TradeSensitiveInfoDTO> :ResponseBody
 */
export interface TradeInfoBlurResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeSensitiveInfoDTO
	 */
	data?: {
		mobile?: string;
		receiver?: string;
		receiverAddress?: string;
		newMobile?: string;
		newReceiver?: string;
		newReceiverAddress?: string;
		[k: string]: any;
	};
	[k: string]: any;
}


export interface IColumnItem {
	"key": string,
	"ischecked": boolean,
	"name": string,
	"isedit": boolean,
	"index": Number,
	"width"?: Number,
}

export interface IColumn {
	userConfig: IColumnItem[],
	default: IColumnItem[],
	id: string,
}

/**
 * ResponseBody<TBDecryptDataDTO> :ResponseBody
 */
export interface TradeTbGetYchSignResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TBDecryptDataDTO
	 */
	data?: {
		taobaoId?: number;
		/**
		 * 御城河颁发的appKey
		 */
		appKey?: string;
		/**
		 * 时间戳
		 */
		time?: string;
		/**
		 * 登陆ISV系统的用户ID
		 */
		userId?: string;
		taobaoUserId?: string;
		topAppKey?: string;
		appName?: string;
		userIp?: string;
		sign?: string;
		sessionId?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ResponseBody<String> :ResponseBody
 */
/**
 * ResponseBody<TBDecryptDataDTO> :ResponseBody
 */
export interface TradeTbTopOnceTokenResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TBDecryptDataDTO
	 */
	data?: {
		taobaoId?: number;
		/**
		 * 御城河颁发的appKey
		 */
		appKey?: string;
		/**
		 * 时间戳
		 */
		time?: string;
		/**
		 * 登陆ISV系统的用户ID
		 */
		userId?: string;
		taobaoUserId?: string;
		topAppKey?: string;
		appName?: string;
		userIp?: string;
		/**
		 * 调用淘宝接口需要的sign文档2.23.2步骤
		 */
		sign?: string;
		sessionId?: string;
		/**
		 * 二次获取的top-token
		 */
		topToken?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

export interface TradeCheckInfoItem {
	sellerId?: string,
	platform?: string,
	source?: string,
	distributorUserId?: string,
	tid?: string,
  storageTime?: string,
  ptTid?:string,
	modified?:string,
	oidList?: any[],
  [k: string]: any;
}

export interface TradePrintCheckRequest {
	tradeCheckInfos?: Array<TradeCheckInfoItem>;
	tidList?: Array<string>;
}

export interface TradePrintCheckResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TBDecryptDataDTO
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * ResponseBody<ItemFilterSettingDTO> :ResponseBody
 */
export interface ItemFilterSettingSelectResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ItemFilterSettingDTO
	 */
	data?: {
		/**
		 * 表字段:id
		 */
		id?: number;
		/**
		 * 表字段:user_id
		 */
		userId?: number;
		/**
		 * 是否悬浮展示1:是0:否
		 */
		isFixed?: number;
		/**
		 * 规格展示内容
		 */
		showSku?: string;
		showColumn?: number; // 宝贝展示类型
		/**
		 * 排序
		 */
		sort?: string;
    jsonConfig?: {
      /**
       * 仅展示选中宝贝
       */
      onlyShowSelected?: number;
      [k: string]: any;
    };
		[k: string]: any;
	};
}
/**
 * TradeIgnorerReqDTO :TradeIgnorerReqDTO
 */
export interface TradeTradeIgnoreRequest {
	/**
	 * IgnoreTradeInfo
	 */
	ignoreTradeInfoList?: {
		/**
		 * 平台店铺名
		 */
		sellerId?: number | string;
		/**
		 * 平台类型
		 */
		platform?: string;
		/**
		 * 订单id
		 */
		tid?: string;
		/**
		 * 子订单id
		 */
		oid?: string;
		[k: string]: any;
	}[];

  /**
 * 忽略类型，1=商品绑定异常，2=系统赠品异常；默认=1
 */
  ignoreType?: string;
	[k: string]: any;
}

/**
 * ItemFilterSettingDTO :ItemFilterSettingDTO
 */
export interface ItemFilterSettingSaveRequest {
	/**
	 * 表字段:id
	 */
	id?: number;
	/**
	 * 表字段:user_id
	 */
	userId?: number;
	/**
	 * 是否悬浮展示1:是0:否
	 */
	isFixed?: number;
	/**
	 * 规格展示内容
	 */
	showSku?: string;
	showColumn?: number; // 宝贝展示类型
	/**
	 * 排序
	 */
	sort?: string;
  jsonConfig?: {
    /**
     * 仅展示选中宝贝
     */
    onlyShowSelected?: number;
    /** 新增：规格合并规则 0-默认 1-简称+规格名 2-简称+别名 */
    skuMergeRule?: number;
    newSort?: string;
    [k: string]: any;
  };
}
/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeTradeIgnoreResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}


/**
 * TradeDetailGetDTO :TradeDetailGetDTO
 */
export interface TradeTradeDetailGetRequest {
	/**
	 * 订单集合 ,TradeInfo
	 */
	tradeInfos?: {
		/**
		 * tid集合 ,String
		 */
		tids?: string[];
		/**
		 * 订单所属平台
		 */
		platform?: string;
		/**
		 * 商家店铺编号
		 */
		sellerId?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}


/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemFilterSettingSaveResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  data?: null;
  [k: string]: any;
}


/**
 * ResponseBody<TradeListVo> :ResponseBody
 */
export interface TradeTradeDetailGetResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeListVo
	 */
	data?: {
		/**
		 * ErpPackageInfoVo
		 */
		list?: IPackage[],
		/**
		 * 总订单数
		 */
		totalCount?: number;
		/**
		 * 合单后订单数
		 */
		total?: number;
		buyerCount?: number;
		params?: string;
		[k: string]: any;
	};
	[k: string]: any;
}
/**
 * List<DecryDataReq>
 */
export type TradeBatchSecretExtendRequest = {
	/**
	 * 店铺id
	 */
	sellerId?: string;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 平台淘宝必须传递这个
	 */
	caid?: string;
	tid?: string;
	/**
	 * 业务场景批打手动解密展示传递100
	 */
	sceneCode?: string;
	[k: string]: any;
}[];

/**
 * ResponseBody<Map<String, DecryDataVO>> :ResponseBody
 */
export interface TradeBatchSecretExtendResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Map
	 */
	data?: {
		/**
		 * 订单编码
		 */
		key: {
			/**
			 * 到期时间
			 */
			expireTime: string;
			[k: string]: any;
		};
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * TradeOccupiedOpsRequest :TradeOccupiedOpsRequest
 */
export interface TradeOccupiedOpsRequest {
	/**
	 * 操作类型,1:占用,2:解除
	 */
	opsType: number;
	/**
	 * TidObj
	 */
	list: {
		tid?: string;
		/**
		 * (该参数为map)
		 */
		sellerFlagSys?: {
			/**
			 * String
			 */
			mapKey?: {
				[k: string]: any;
			};
			/**
			 * Integer
			 */
			mapValue?: {
				[k: string]: any;
			};
			[k: string]: any;
		};
		/**
		 * 卖家备注旗帜红、黄、绿、蓝、紫、灰分别对应1、2、3、4、5、0
		 */
		sellerFlag?: number;
		/**
		 * 平台类型
		 */
		platformType?: string;
		/**
		 * 平台店铺id
		 */
		sellerId?: number;
		/**
		 * OrderDTO
		 */
		oidList?: string[];
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
* ResponseBody<TradeOccupiedOpsResp> :ResponseBody
*/
export interface TradeOccupiedOpsResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeOccupiedOpsResp
	 */
	data?: {
		errorMsg?: string;
		/**
		 * 错误的主订单集合 ,String
		 */
		errorTidList?: string[];
		/**
		 * 库存不足的规格集合 ,TradeOccupiedHandOpsErrorSysSkuVo
		 */
		errorSysSkuIdList?: {
			/**
			 * 货品规格id
			 */
			sysSkuId?: number;
			/**
			 * 货品规格别名
			 */
			sysSkuAlias?: string;
			/**
			 * 货品规格商家编码
			 */
			skuOuterId?: string;
			[k: string]: any;
		}[];
		/**
		 * 错误类型枚举,1:订单状态发生改变;,2:订单商品库存不足;,,TradePreOccupiedHandRespErrorTypeEnum[TRADE_STATUS_CHANGE_ERROR,TRADE_ITEM_STOCK_ERROR,type,desc]
		 */
		errorTypeEnum?: ValueOf<typeof OccupyErrorTypeEnum>;
		[k: string]: any;
	};
	[k: string]: any;
}


export interface TradeQueryTradeByExNumberRequest {
	ydNoList?: string[];
	[k: string]: any;
}

export interface TradeQueryTradeByExNumberResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeQueryTradeByExNumberResponse
	 */
	data?: {
		/**
		 * ScanInfoBean
		 */
		scanInfoBean?: {
			/**
			 * 卖家id
			 */
			sellerId?: string;
			/**
			 * 收件人手机号
			 */
			receiverMobile?: string;
			/**
			 * 收件地址
			 */
			receiverAddress?: string;
			userId?: string;
			sellerNick?: string;
			platform?: string;
			tid?: string;
			/**
			 * 发货状态1：未发货2：已发货
			 */
			status?: string;
			/**
			 * 快递公司编号
			 */
			logisticsId?: string;
			/**
			 * 物流名称
			 */
			expressName?: string;
			/**
			 * 快递单号
			 */
			exNumber?: string;
			/**
			 * 是否是拆单发货
			 */
			isSplitSendTrade?: boolean;
			receiverNameMask?: string;
			receiverPhoneMask?: string;
			receiverAddressMask?: string;
			receiverMobileMask?: string;
			[k: string]: any;
		}[];
		/**
		 * 是否有拆单发货
		 */
		hasSplitSendTrade?: boolean;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * UpdateIgnoreReq :UpdateIgnoreReq
 */
export interface TradeBatchUpdateIgnoreRequest {
	/**
	 * String
	 */
	tidList?: string[];
	/**
	 * 是否是空包裹订单,1:自动标记，2：不是，3：手动标记，4：手动取消   这里仅支持3和4
	 */
	ignoreType?: number;
	[k: string]: any;
}
/**
 * TradeDispenseOpsRequest :TradeDispenseOpsRequest
 */
export interface TradeDispenseRequest {
	/**
	 * 源子订单号
	 */
	sourceTid: string;
	/**
	 * 目标子订单号
	 */
	destOid: string;
	[k: string]: any;
}

/**
 * ResponseBody :ResponseBody
 */
export interface TradeBatchUpdateIgnoreResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	[k: string]: any;
}

/**
 * ResponseBody<TradeDispenseOpsResp> :ResponseBody
 */
export interface TradeDispenseResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeDispenseOpsResp
	 */
	data?: {
		/**
		 * 源主订单单号
		 */
		sourceTid?: string;
		/**
		 * 目标子订单单号
		 */
		destOid?: string;
		/**
		 * 目标子订单已分配库存结果值
		 */
		destOidAllotResult?: number;
		/**
		 * 源主订单已分配库存结果值 ,TradeAllotStockNumInfo
		 */
		sourceTidAllotResult?: {
			/**
			 * 订单id
			 */
			tid?: string;
			/**
			 * 子订单信息 ,OrderInfo
			 */
			oidList?: {
				/**
				 * 子订单id
				 */
				oid?: string;
				/**
				 * 子订单待分配数量
				 */
				alreadyAllotStockNum?: number;
				/**
				 * 购买数量
				 */
				num?: number;
				[k: string]: any;
			}[];
			[k: string]: any;
		};
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * TradeAllotStockNumByTidListRequest :TradeAllotStockNumByTidListRequest
 */
export interface TradeGetTradeAllotStockNumByTidListRequest {
	/**
	 * String
	 */
	tidList: string[];
	[k: string]: any;
}

/**
 * ResponseBody<List<TradeAllotStockNumInfo>> :ResponseBody
 */
export interface TradeGetTradeAllotStockNumByTidListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeAllotStockNumInfo
	 */
	data?: {
		/**
		 * 订单id
		 */
		tid?: string;
		/**
		 * 子订单信息 ,OrderInfo
		 */
		oidList?: {
			/**
			 * 子订单id
			 */
			oid?: string;
			/**
			 * 子订单待分配数量
			 */
			alreadyAllotStockNum?: number;
			[k: string]: any;
		}[];
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
 * UpdateTradeOccupiedRequest :UpdateTradeOccupiedRequest
 */
export interface TradeUpdateTradeOccupiedRequest {
	/**
	 * 货品规格编码 ,String
	 */
	skuOuterIdList: string[];
	[k: string]: any;
}
/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeUpdateTradeOccupiedResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
}

export interface TradeBatchChangeTradeItemRequest {
	/**
	 * 订单信息 ,ChangeTradeItem
	 */
	changeTradeItemList?: {
		/**
		 * 订单编号
		 */
		tid?: string;
		/**
		 * 平台类型
		 */
		platform?: string;
    /**
     * 订单来源,,HAND("HAND","手工单"),,SCM("SCM","供分销"),,EXCHANGE("EXCHANGE","售后换货手工单"),,SCMHAND("SCMHAND","分销手工单");
     */
    source?: string;
		/**
		 * 账号id
		 */
		sellerId?: number;
		/**
		 * 子订单信息 ,ChangeOrderItem
		 */
		changeOrderItems?: {
			/**
			 * 子订单编号
			 */
			oid?: string;
			/**
			 * 旧商品标题
			 */
			oldTitle?: string;
			/**
			 * 旧商品id
			 */
			oldItemId?: string;
			/**
			 * 旧规格id
			 */
			oldSkuId?: string;
			/**
			 * 旧规格名称
			 */
			oldSkuProperties?: string;
			/**
			 * 旧商品数量
			 */
			oldNumber?: string;
			/**
			 * 商品图片
			 */
			picPath?: string;
			/**
			 * 商品标题
			 */
			title?: string;
			/**
			 * 商家编码
			 */
			outerId?: string;
			/**
			 * 平台商品id
			 */
			itemId?: string;
			/**
			 * 平台规格id
			 */
			skuId?: string;
			/**
			 * 规格商家编码
			 */
			outerSkuId?: string;
			/**
			 * 商品数量
			 */
			number?: number;
			/**
			 * 规格图片
			 */
			skuUrl?: string;
			/**
			 * 规格名称
			 */
			skuProperties?: string;
			/**
			 * 子订单修改状态0:默认；1:置换商品；2置换货品；3置换数量；4新增的货品；5新增的商品；-1删除
			 */
			sysExchangeType?: number;
			/**
			 * 系统换商品时间
			 */
			sysExchangeTime?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	}[];
	[k: string]: any;
}

export interface TradeBatchChangeTradeItemResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: {
    /**
     * ChangeTradeItemResult
     */
    changeTradeItemResultList?: {
      /**
       * 订单编号
       */
      tid?: string;
      isSuccess?: boolean;
      /**
       * 失败信息
       */
      message?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * LogisticsDetailReq :LogisticsDetailReq
 */
export interface TradeLogisticsGetLogisticsDetailForErpRequest {
	ydNo?: string;
	kdCode?: string;
	userId?: string;
	ptType?: string;
	businessType?: number;
	accessToken?: string;
	[k: string]: any;
}

/**
 * ResponseBody<ErpLogisticsTraceSearchResponse> :ResponseBody
 */
export interface TradeLogisticsGetLogisticsDetailForErpResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ErpLogisticsTraceSearchResponse
	 */
	data?: {
		mailNo?: string;
		cpCode?: string;
		logisticsStatus?: string;
		logisticsStatusDesc?: string;
		logisticsCompanyName?: string;
		theLastMessage?: string;
		theLastTime?: string;
		courier?: string;
		courierPhone?: string;
		takeTime?: string;
		qrCode?: string;
		cpMobile?: string;
		cpUrl?: string;
		/**
		 * LogisticsTraceDetailListBean
		 */
		logisticsTraceDetailList?: {
			time?: string;
			logisticsStatus?: string;
			subLogisticsStatus?: string;
			desc?: string;
			areaCode?: string;
			areaName?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ModifyAddressRequestVo :ModifyAddressRequestVo
 */
export interface TradeModifyAddressRequest {
	/**
	 * 订单号
	 */
	tids: string;
	/**
	 * 用户id(单号的使用者，也就是说订单的归属者)
	 */
	sellerId: number;
	/**
	 * 平台类型
	 */
	platform: string;
	/**
	 * 收件省
	 */
	receiverProvince: string;
	/**
	 * 收件市
	 */
	receiverCity: string;
	/**
	 * 收件区
	 */
	receiverCounty: string;
	/**
	 * 收件地址
	 */
	receiverAddress: string;
	/**
	 * 收件街道
	 */
	receiverTown?: string;
	/**
	 * 收件人
	 */
	receiverName?: string;
	/**
	 * 邮编
	 */
	receiverZip?: string;
	/**
	 * 手机
	 */
	receiverMobile: string;
	/**
	 * 电话
	 */
	receiverPhone?: string;
	[k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeModifyAddressResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * ReqGetLogisticsCompanyInfoByYdNoDTO :ReqGetLogisticsCompanyInfoByYdNoDTO
 */
export interface TradeLogisticsGetLogisticsCompanyInfoByYdNoRequest {
	/**
	 * 运单号 ,String
	 */
	ydNoList?: string[];
	[k: string]: any;
}


/**
 * ResponseBody<RspGetLogisticsCompanyInfoByYdNoDTO> :ResponseBody
 */
export interface TradeLogisticsGetLogisticsCompanyInfoByYdNoResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * RspGetLogisticsCompanyInfoByYdNoDTO
	 */
	data?: {
		/**
		 * 物流公司信息集合 ,LogisticsCompanyInfo
		 */
		logisticsCompanyInfos?: {
			/**
			 * 物流公司code
			 */
			cpCode?: string;
			/**
			 * 物流公司名称
			 */
			companyName?: string;
			[k: string]: any;
		}[];
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * TradeLocalItemSnapshotUpdateDTO :TradeLocalItemSnapshotUpdateDTO
 */
export interface TradeAsyncLocalItemSnapShotRequest {
	/**
	 * 需要更新的tid ,String
	 */
	tid?: string[];
	[k: string]: any;
}

/**
 * ResponseBody<String> :ResponseBody
 */
export interface TradeAsyncLocalItemSnapShotResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: string;
	[k: string]: any;
}


/**
 * LocalItemSnapShotResultDTO :LocalItemSnapShotResultDTO
 */
export interface TradeQueryLocalItemSnapShotResultRequest {
	traceId?: string;
	[k: string]: any;
}

/**
 * ResponseBody<LocalItemSnapShotResultVo> :ResponseBody
 */
export interface TradeQueryLocalItemSnapShotResultResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * LocalItemSnapShotResultVo
	 */
	data?: {
		userId?: number;
		traceId?: string;
		taskNum?: number;
		successNum?: number;
		failedNum?: number;
		finishNum?: number;
		finishBar?: number;
		/**
		 * 失败的订单信息列表 ,String
		 */
		failedTidList?: string[];
		message?: string;
		[k: string]: any;
	};
	[k: string]: any;
}

/**
 * TradeUpdateSnapshotRequest :TradeUpdateSnapshotRequest
 */
export interface TradeBatchUpdateTradeSnapshotRequest {
	/**
	 * String
	 */
	tidList: string[];
	[k: string]: any;
}

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeBatchUpdateTradeSnapshotResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: {
		successNum: number,
		failedNum: number,
		failedTidList: string[]
	};
	[k: string]: any;
}
/**
 * EditSysItemInfoInTradeRequest :EditSysItemInfoInTradeRequest
 */
export interface ItemItemEditSysItemInfoInTradeRequest {
	/**
	 * 店铺id
	 */
	sellerId: number | string;
	/**
	 * 平台,PDD,TB
	 */
	platformType: string;
	/**
	 * tid
	 */
	tid: string;
	/**
	 * oid
	 */
	oid: string;
	/**
	 * editType：1：库存版编辑货品信息。2：库存版编辑规格信息。3：零库存版编辑货品信息。4：零库存版编辑规格信息
	 */
	editType: number;
	/**
	 * 订单来源:其他平台订单,Hand手工订单
	 */
	source?: string;
	/**
	 * BIND:绑定,CREATE:生成
	 */
	operateType?: string;
	/**
	 * 平台商品Id
	 */
	numIid?: string;
	/**
	 * 平台商品Id
	 */
	skuId?: string;
	/**
	 * 平台商品Id
	 */
	sysItemId?: number;
	/**
	 * 货品简称
	 */
	sysItemAlias?: string;
	/**
	 * 货品规格id
	 */
	sysSkuId?: number;
	/**
	 * 规格别名置空传"EMPTY"
	 */
	sysSkuAlias?: string;
	/**
	 * 供应商id置空传-1
	 */
	supplierId?: number;
	/**
	 * 供应商置空传"EMPTY"
	 */
	supplier?: string;
	/**
	 * 档口置空传"EMPTY"
	 */
	stall?: string;
	/**
	 * 市场置空传"EMPTY"
	 */
	market?: string;
	/**
	 * 成本价置空传"EMPTY"
	 */
	costPrice?: string;
	[k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface ItemItemEditSysItemInfoInTradeResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * Void
	 */
	data?: {
		[k: string]: any;
	};
	[k: string]: any;
}


/**
 * List<BatchSendDTO>
 */
export type TradeBatchResendRequest = {
	/**
	 * 如果存在合单，那么数组中分别组装数据传入，类似如下格式,[,[{tid:1,exNumber:444555,exCode:YTO,其他所有字段}],,[{tid:2,exNumber:444556,exCode:YTO,其他所有字段}],,[{tid:3,exNumber:444556,exCode:YTO,其他所有字段},{tid:4,exNumber:444556,exCode:YTO,其他所有字段}],] ,BatchSendTradeDTO
	 */
	subTrades?: {
		togetherId?: string;
		/**
		 * 母单号，针对快运场景
		 */
		pYdNo?: string;
		/**
		 * 是否拆单发货，默认非拆单
		 */
		split?: boolean;
		/**
		 * 多包裹发货仅pddfxgtbsph支持
		 */
		multiPack?: boolean;
		/**
		 * 是否为货到付款订单，1标识是，0标识否，默认非货到付款订单
		 */
		cod?: number;
		/**
		 * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
		 */
		sendType?: string;
		/**
		 * 单号来源
		 */
		waybillNoSource?: string;
		/**
		 * 用户id(注册账号)
		 */
		userId?: number;
		/**
		 * 用户id(单号的使用者，也就是说订单的归属者)
		 */
		sellerId?: number;
		/**
		 * 平台类型
		 */
		platform?: string;
		/**
		 * 订单来源
		 */
		source?: string;
		/**
		 * 买家账号
		 */
		buyerNick?: string;
		/**
		 * 买家唯一标识
		 */
		buyerOpenUid?: string;
		/**
		 * 收件省
		 */
		receiverProvince?: string;
		/**
		 * 收件市
		 */
		receiverCity?: string;
		/**
		 * 收件区
		 */
		receiverCounty?: string;
		/**
		 * 收件地址
		 */
		receiverAddress?: string;
		/**
		 * 收件街道
		 */
		receiverTown?: string;
		/**
		 * 收件人
		 */
		receiverName?: string;
		/**
		 * 手机
		 */
		mobile?: string;
		/**
		 * 固定电话
		 */
		tel?: string;
		/**
		 * 快递id
		 */
		exId?: number;
		/**
		 * 快递code
		 */
		exCode?: string;
		/**
		 * 快递名
		 */
		exName?: string;
		/**
		 * 快递单号
		 */
		exNumber?: string;
		/**
		 * 多包裹发货的字段 ,String
		 */
		exNumberList?: string[];
		/**
		 * 商品数量
		 */
		goodsNum?: number;
		/**
		 * 重量
		 */
		weight?: string;
		/**
		 * 拆单子订单串。oid1,ords,oid3
		 */
		oids?: string;
		/**
		 * tid
		 */
		tid?: string;
		/**
		 * 买家留言
		 */
		buyerMsg?: string;
		/**
		 * 卖家备注旗帜类型
		 */
		sellerFlag?: number;
		/**
		 * 本地标记类型(该参数为map)
		 */
		sellerFlagSys?: {
			/**
			 * String
			 */
			mapKey?: {
				[k: string]: any;
			};
			/**
			 * Integer
			 */
			mapValue?: {
				[k: string]: any;
			};
			[k: string]: any;
		};
		/**
		 * 卖家备注
		 */
		sellerMemo?: string;
		/**
		 * 发货内容
		 */
		info?: string;
		/**
		 * 打印序号
		 */
		printNum?: string;
		/**
		 * 包裹号
		 */
		packageId?: number;
		/**
		 * 快递单类型1普通面单,2网点,3云栈电子面单
		 */
		kddType?: number;
		/**
		 * 是哪个页面的操作0：单打1：批打2：预发货3：自动预发货4：手工订单5厂家代发
		 */
		optionType?: number;
		/**
		 * 订单实付金额
		 */
		payment?: string;
		/**
		 * BatchSendOrderDTO
		 */
		orders?: {
			/**
			 * 商品数量
			 */
			num?: number;
			/**
			 * numIid
			 */
			numIid?: string;
			skuId?: string;
			tid?: string;
			oid?: string;
			/**
			 * 子订单实付金额
			 */
			payment?: string;
			systemNumIid?: number;
			systemSkuId?: number;
			/**
			 * 是否忽略，0否，1是
			 */
			ignore?: number;
			/**
			 * 是否赠品
			 */
			gift?: boolean;
			/**
			 * 换商品逻辑0:默认；1:置换商品；2置换货品；3置换数量；4新增的货品；5新增的商品
			 */
			sysExchangeType?: number;
			[k: string]: any;
		}[];
		/**
		 * overseaTracing（海淘溯源码id）内容。
		 */
		overseaTracing?: string;
		/**
		 * PDD支持imei（手机串号
		 */
		imei?: string;
		/**
		 * PDD支持deviceSn（设备序列号）
		 */
		deviceSn?: string;
		/**
		 * 发货个性内容,<p>,针对pdd平台：支持imei（手机串号），deviceSn（设备序列号），overseaTracing（海淘溯源码id）内容。,形如："imei=识别码1,识别码2;"、"deviceSn=序列号1,序列号2;"、"overseaTracing=溯源码1,溯源码2;"。,以英文逗号","分割串号，以英文分号";"分割不同参数内容。上传时请严格区分imei，deviceSn和overseaTracing，,其中overseaTracing（海淘溯源码id）要求海淘商品在支持溯源的情况下必传。错传漏传将会导致发货失败。,详细参考：https:open.pinduoduo.comapplicationdocumentapi?id=pdd.logistics.online.send,针对淘宝平台：
		 */
		feature?: string;
		/**
		 * 手机号索引串
		 */
		idxEncodeReceiverMobile?: string;
		/**
		 * 加密详情索引串
		 */
		idxEncodeReceiverAddress?: string;
		/**
		 * 加密收件人索引串,,@return
		 */
		idxEncodeReceiverName?: string;
		/**
		 * 收件人姓名脱敏
		 */
		receiverNameMask?: string;
		/**
		 * 收件人电话脱敏
		 */
		receiverPhoneMask?: string;
		/**
		 * 收件人地址脱敏
		 */
		receiverAddressMask?: string;
		/**
		 * 收件人手机号脱敏
		 */
		receiverMobileMask?: string;
		/**
		 * 加密字段，不同字段归属平台字段可能不一致，比如淘宝为oaid
		 */
		caid?: string;
		/**
		 * 加密串tid
		 */
		encodeTid?: string;
		/**
		 * 订单地址是否解密
		 */
		isDecrypted?: number;
		/**
		 * 距离最晚发货时间时长
		 */
		sendRemindHour?: string;
		/**
		 * 退款状态,1-有退款NOT_REFUND,0-无退款HAS_REFUND
		 */
		refundStatus?: string;
		/**
		 * crossBorder拼多多跨境订单
		 */
		tradeType?: string;
		/**
		 * 是否是空包裹订单,1:自动标记，2：不是，3：手动标记，4：手动取消,默认不是
		 */
		ignoreType?: number;
		[k: string]: any;
	}[];
	/**
	 * 发货来源,PD("批量打印发货"),,YFH_REAL_SEND("预发货真正发货"),,LABEL(2"小标签扫描发货"),,,@linkTradeSendSourceEnum
	 */
	sendFrom?: {
		[k: string]: any;
	};
	[k: string]: any;
}[];

/**
 * ResponseBody<List<BatchSendTradeResultDTO>> :ResponseBody
 */
export interface TradeBatchResendResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * BatchSendTradeResultDTO
	 */
	data?: {
		/**
		 * 子订单发货结果记录 ,BatchSendSubTradeResultDTO
		 */
		subTradeInfos?: {
			tid?: string;
			oids?: string;
			isSuccess?: boolean;
			/**
			 * 失败信息
			 */
			message?: string;
			result?: number;
			[k: string]: any;
		}[];
		/**
		 * 结果标识,100发货成功，101,部分发货成功，102拆单发货成功，103发货失败
		 */
		result?: number;
		togetherId?: string;
		isSuccess?: boolean;
		/**
		 * 失败信息
		 */
		message?: string;
		/**
		 * 买家旺旺
		 */
		buyerNick?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}
/**
 * TradeBatchUpdateWarningDto :TradeBatchUpdateWarningDto
 */
export interface TradeBatchUpdateWarningRequest {
	/**
	 * Long
	 */
	ids?: number[];
	[k: string]: any;
  }
  /**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeBatchUpdateWarningResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
  }
/**
 * ResponseBody<Long> :ResponseBody
 */
export interface TradeGetRedDotMarketResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: {
		/**
	   * 打印后地址信息
	   */
		sendWarningNum?: number;
		/**
		 * 留言备注地址信息
		 */
		memoWarningNum?: number;
		/**
		 * 退款信息
		 */
		refundWarningNum?: number;
    /**
		 * 发货失败
		 */
    shipFailWarningNum?: number;
		totalNum: number;
		intervalSeconds?: number;
	};
	[k: string]: any;
  }
  /**
 * TradeWaringQueryDTO :TradeWaringQueryDTO
 */
export interface TradeQueryTradeWarningRequest {
	/**
	 * MultiShopDTO
	 */
	multiShopS?: {
	  sellerId?: number;
	  platform?: string;
	  [k: string]: any;
	}[];
	/**
	 * 订单状态
	 */
	tradeStatus?: string;
	/**
	 * 订单预警状态
	 */
	warnStatus?: string;
	/**
	 * 订单生成时间的开始时间，单位毫秒,不能小于90天前，且需要小于结束时间
	 */
	startTime: string;
	/**
	 * 订单生成时间的截止时间，单位毫秒,不能小于90天前，且与开始时间时间范围不大于7天
	 */
	endTime: string;
	/**
	 * String
	 */
	tids?: string[];
	[k: string]: any;
  }
  /**
 * ResponseBody<TradeWarningPageListVo> :ResponseBody
 */

export interface TradeWarningInfo {
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.id,,@mbg.generated
		 */
		id?: number;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.user_id,,@mbg.generated
		 */
		userId?: number;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.seller_id,,@mbg.generated
		 */
		sellerId?: number;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.platform,,@mbg.generated
		 */
		platform?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.tid,,@mbg.generated
		 */
		tid?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.warning_status,,@mbg.generated
		 */
		warningStatus?: number;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.trade_status,,@mbg.generated
		 */
		tradeStatus?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_name,,@mbg.generated
		 */
		receiverName?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_mobile,,@mbg.generated
		 */
		receiverMobile?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_phone,,@mbg.generated
		 */
		receiverPhone?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_country,,@mbg.generated
		 */
		receiverCountry?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_province,,@mbg.generated
		 */
		receiverProvince?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_city,,@mbg.generated
		 */
		receiverCity?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_county,,@mbg.generated
		 */
		receiverCounty?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_town,,@mbg.generated
		 */
		receiverTown?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_address,,@mbg.generated
		 */
		receiverAddress?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_name_mask,,@mbg.generated
		 */
		receiverNameMask?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_mobile_mask,,@mbg.generated
		 */
		receiverMobileMask?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_phone_mask,,@mbg.generated
		 */
		receiverPhoneMask?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.receiver_address_mask,,@mbg.generated
		 */
		receiverAddressMask?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.oaid,,@mbg.generated
		 */
		oaid?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.created,,@mbg.generated
		 */
		created?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.waybill_no,,@mbg.generated
		 */
		waybillNo?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.print_time,,@mbg.generated
		 */
		printTime?: string;
		/**
		 * ThisfieldwasgeneratedbyMyBatisGenerator.,Thisfieldcorrespondstothedatabasecolumnorder_warning.processing_status,,@mbg.generated
		 */
		processingStatus?: number;
		[k: string]: any;
  }
export interface TradeQueryTradeWarningResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeWarningPageListVo
	 */
	data?: {
	  pageNo?: number;
	  pageSize?: number;
	  totalNum?: number;
	  /**
	   * 打印后地址信息
	   */
	  sendWarningNum?: number;
	  /**
	   * 留言备注地址信息
	   */
	  memoWarningNum?: number;
	  /**
	   * 退款信息
	   */
	  refundWarningNum?: number;
	  /**
	   * RdsTradeWarningVo
	   */
	  list?: TradeWarningInfo[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

export interface TradeNoticeWarnVo {
	/**
	 * 打印后地址信息
	 */
	sendWarningNum?: number;
	/**
	 * 留言备注地址信息
	 */
	memoWarningNum?: number;
	/**
	 * 退款信息
	 */
	refundWarningNum?: number;
	/**
	 * 发货失败
	 */
	shipFailWarningNum?: number;
	/**
	 * 即将超时揽收
	 */
	nearTimeoutAcceptWarningNum?: number;
	/**
	 * 超时未揽收
	 */
	timeoutUnacceptableWarningNum?: number;
	/**
	 * 总的异常信息
	 */
	totalWarningNum?: number;
	[k: string]: any;
}
/**
 * ResponseBody<TradeNoticeWarnVo> :ResponseBody
 */
export interface TradeQueryTradeNoticeWarnResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeNoticeWarnVo
	 */
	data?: TradeNoticeWarnVo;
	[k: string]: any;
  }
/**
 * TradeWaringQueryDTO :TradeWaringQueryDTO
 */
export interface TradeQueryTradeWarningCountRequest {
	/**
	 * MultiShopDTO
	 */
	multiShopS?: {
	  sellerId?: number;
	  platform?: string;
	  [k: string]: any;
	}[];
	/**
	 * 订单状态
	 */
	tradeStatus?: string;
	/**
	 * 订单预警状态
	 */
	warnStatus?: string;
	/**
	 * 订单处理状态
	 */
	processingStatus?: string;
	/**
	 * 订单生成时间的开始时间，单位毫秒,不能小于90天前，且需要小于结束时间
	 */
	startTime: string;
	/**
	 * 订单生成时间的截止时间，单位毫秒,不能小于90天前，且与开始时间时间范围不大于7天
	 */
	endTime: string;
	/**
	 * String
	 */
	tids?: string[];
	pageNo?: number;
	pageSize?: number;
	[k: string]: any;
  }
  /**
 * ResponseBody<TradeWarningCountVo> :ResponseBody
 */
export interface TradeQueryTradeWarningCountResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * TradeWarningCountVo
	 */
	data?: {
	  totalNum?: number;
	  /**
	   * 打印后地址信息
	   */
	  sendWarningNum?: number;
	  /**
	   * 留言备注地址信息
	   */
	  memoWarningNum?: number;
	  /**
	   * 退款信息
	   */
	  refundWarningNum?: number;
	  [k: string]: any;
	};
	[k: string]: any;
  }
/**
 * ResponseBody<Long> :ResponseBody
 */
export interface TradeDeleteTradeWarnTipsCacheResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: number;
	[k: string]: any;
  }
/**
 * GetWarningDetailRequest :GetWarningDetailRequest
 */
export interface TradeGetWarningDetailRequest {
	/**
	 * 告警ID
	 */
	warningId?: string;
	/**
	 * 订单id
	 */
	tid?: string;
	[k: string]: any;
  }

export interface MallTrade {
	isPreShip?: number;
	isPreShipId?: number;
	tid?: string;
	distributorUserId?: number;
	distributorTid?: string;
	realTid?: string;
	userId?: string;
	sellerId?: string;
	buyerMessage?: string;
	salesChannel?: string;
	transportDay?: string;
	buyerNick?: string;
	cod?: number;
	tradeType?: string;
	tradeTypeTagStr?: string;
	couponFee?: string;
	sysDiscountFee?: string;
	shopDiscountFee?: string;
	totalFee?: string;
	payment?: number;
	postFee?: string;
	created?: string;
	endTime?: string;
	payTime?: string;
	modified?: string;
	consignTime?: string;
	totalNum?: number;
	/**
	 * MallOrder
	 */
	orders?: {
	  sellerId?: string;
	  orderId?: string;
	  picPath?: string;
	  skuUrl?: string;
	  sellerNick?: string;
	  buyerNick?: string;
	  refundStatus?: string;
	  refundStatusStr?: string;
	  status?: string;
	  statusDesc?: string;
	  title?: string;
	  outerId?: string;
	  deliverCode?: string;
	  campaignId?: string;
	  itemId?: string;
	  fpsDeliveryFlag?: string;
	  parentItemId?: string;
	  skuId?: string;
	  outerSkuId?: string;
	  number?: number;
	  price?: string;
	  totalFee?: string;
	  discountFee?: string;
	  adjustFee?: string;
	  payment?: number;
	  waybillNo?: string;
	  logisticsId?: string;
	  logisticsName?: string;
	  isbn?: string;
	  endTime?: string;
	  consignTime?: string;
	  itemType?: string;
	  isPreSale?: number;
	  lastShipTime?: string;
	  storey?: string;
	  sendNum?: number;
	  /**
	   * MallProp
	   */
	  saleProps?: {
		key?: string;
		name?: string;
		value?: string;
		[k: string]: any;
	  }[];
	  color?: string;
	  categorySize?: string;
	  size?: string;
	  skuProperties?: string;
	  skuShortProperties?: string;
	  belongProductsPromoID?: number;
	  productItemId?: number;
	  shortName?: string;
	  payType?: string;
	  numIid?: string;
	  itemNo?: string;
	  authorId?: string;
	  authorName?: string;
	  bizType?: string;
	  isGift?: string;
	  orderNoLogisticsSend?: boolean;
	  weight?: number;
	  weightUnit?: string;
	  itemMealName?: string;
	  snapshotUrl?: string;
	  snapshot?: string;
	  timeoutActionTime?: string;
	  buyerRate?: boolean;
	  sellerRate?: string;
	  sellerType?: string;
	  cid?: number;
	  estimateConTime?: string;
	  type?: string;
	  orderFrom?: string;
	  modified?: string;
	  refundId?: string;
	  orderAttr?: string;
	  shippingType?: string;
	  logisticsCompany?: string;
	  divideOrderFee?: string;
	  partMjzDiscount?: string;
	  tmserSpuCode?: string;
	  isShShip?: boolean;
	  esRange?: string;
	  esDate?: string;
	  osDate?: string;
	  osRange?: string;
	  promiseService?: string;
	  tmallPromise?: string;
	  skuAlias?: string;
	  shippingTime?: string;
	  timingPromise?: string;
	  cutoffMinutes?: string;
	  esTime?: string;
	  deliveryTime?: string;
	  collectTime?: string;
	  dispatchTime?: string;
	  signTime?: string;
	  storeCode?: string;
	  sysItemId?: number;
	  sysSkuId?: number;
	  sysSkuName?: string;
	  sysItemName?: string;
	  sysSkuAlias?: string;
	  sysItemAlias?: string;
	  sysOuterId?: string;
	  sysOuterSkuId?: string;
	  sysPicPath?: string;
	  priceType?: string;
	  ignoreExcFlag?: number;
	  isCombination?: number;
	  labelStatus?: string;
	  sendType?: string;
	  mergeTid?: string;
	  occupiedStockStatus?: number;
	  sysShipTime?: string;
	  sysPrintTime?: string;
	  waybillNoSource?: string;
	  sysExchangeOid?: string;
	  sysExchangeType?: number;
	  sysExchangeTime?: string;
	  alreadyAllotStockNum?: number;
	  combinationAllotStockNum?: string;
	  market?: string;
	  stall?: string;
	  costPrice?: string;
	  supplierId?: string;
	  supplier?: string;
	  tagPrice?: string;
	  netPrice?: string;
	  barCode?: string;
	  brand?: string;
	  brandId?: string;
	  preSaleType?: number;
	  dropShipping?: boolean;
	  combinationOrder?: boolean;
	  sysDbChange?: string;
	  [k: string]: any;
	}[];
	/**
	 * MallOrder
	 */
	mallGifts?: {
	  sellerId?: string;
	  orderId?: string;
	  picPath?: string;
	  skuUrl?: string;
	  sellerNick?: string;
	  buyerNick?: string;
	  refundStatus?: string;
	  refundStatusStr?: string;
	  status?: string;
	  statusDesc?: string;
	  title?: string;
	  outerId?: string;
	  deliverCode?: string;
	  campaignId?: string;
	  itemId?: string;
	  fpsDeliveryFlag?: string;
	  parentItemId?: string;
	  skuId?: string;
	  outerSkuId?: string;
	  number?: number;
	  price?: string;
	  totalFee?: string;
	  discountFee?: string;
	  adjustFee?: string;
	  payment?: number;
	  waybillNo?: string;
	  logisticsId?: string;
	  logisticsName?: string;
	  isbn?: string;
	  endTime?: string;
	  consignTime?: string;
	  itemType?: string;
	  isPreSale?: number;
	  lastShipTime?: string;
	  storey?: string;
	  sendNum?: number;
	  /**
	   * MallProp
	   */
	  saleProps?: {
		key?: string;
		name?: string;
		value?: string;
		[k: string]: any;
	  }[];
	  color?: string;
	  categorySize?: string;
	  size?: string;
	  skuProperties?: string;
	  skuShortProperties?: string;
	  belongProductsPromoID?: number;
	  productItemId?: number;
	  shortName?: string;
	  payType?: string;
	  numIid?: string;
	  itemNo?: string;
	  authorId?: string;
	  authorName?: string;
	  bizType?: string;
	  isGift?: string;
	  orderNoLogisticsSend?: boolean;
	  weight?: number;
	  weightUnit?: string;
	  itemMealName?: string;
	  snapshotUrl?: string;
	  snapshot?: string;
	  timeoutActionTime?: string;
	  buyerRate?: boolean;
	  sellerRate?: string;
	  sellerType?: string;
	  cid?: number;
	  estimateConTime?: string;
	  type?: string;
	  orderFrom?: string;
	  modified?: string;
	  refundId?: string;
	  orderAttr?: string;
	  shippingType?: string;
	  logisticsCompany?: string;
	  divideOrderFee?: string;
	  partMjzDiscount?: string;
	  tmserSpuCode?: string;
	  isShShip?: boolean;
	  esRange?: string;
	  esDate?: string;
	  osDate?: string;
	  osRange?: string;
	  promiseService?: string;
	  tmallPromise?: string;
	  skuAlias?: string;
	  shippingTime?: string;
	  timingPromise?: string;
	  cutoffMinutes?: string;
	  esTime?: string;
	  deliveryTime?: string;
	  collectTime?: string;
	  dispatchTime?: string;
	  signTime?: string;
	  storeCode?: string;
	  sysItemId?: number;
	  sysSkuId?: number;
	  sysSkuName?: string;
	  sysItemName?: string;
	  sysSkuAlias?: string;
	  sysItemAlias?: string;
	  sysOuterId?: string;
	  sysOuterSkuId?: string;
	  sysPicPath?: string;
	  priceType?: string;
	  ignoreExcFlag?: number;
	  isCombination?: number;
	  labelStatus?: string;
	  sendType?: string;
	  mergeTid?: string;
	  occupiedStockStatus?: number;
	  sysShipTime?: string;
	  sysPrintTime?: string;
	  waybillNoSource?: string;
	  sysExchangeOid?: string;
	  sysExchangeType?: number;
	  sysExchangeTime?: string;
	  alreadyAllotStockNum?: number;
	  combinationAllotStockNum?: string;
	  market?: string;
	  stall?: string;
	  costPrice?: string;
	  supplierId?: string;
	  supplier?: string;
	  tagPrice?: string;
	  netPrice?: string;
	  barCode?: string;
	  brand?: string;
	  brandId?: string;
	  preSaleType?: number;
	  dropShipping?: boolean;
	  combinationOrder?: boolean;
	  sysDbChange?: string;
	  [k: string]: any;
	}[];
	receiverName?: string;
	receiverCountry?: string;
	receiverProvince?: string;
	receiverCity?: string;
	receiverCounty?: string;
	receiverTown?: string;
	hasTown?: number;
	receiverAddress?: string;
	receiverMobile?: string;
	receiverPhone?: string;
	receiverZip?: string;
	sellerFlag?: number;
	sellerMemo?: string;
	status?: string;
	statusDesc?: string;
	logisticsId?: string;
	logisticsName?: string;
	waybillNo?: string;
	waybillCode?: string;
	wareHouse?: string;
	refundStatus?: string;
	export?: string;
	payMethod?: string;
	payType?: string;
	deliveryType?: string;
	deliveryTypeDesc?: string;
	bizType?: string;
	/**
	 * MallInvoice
	 */
	mallInvoice?: {
	  needinvoiceflag?: string;
	  invoice?: string;
	  invoiceHead?: string;
	  invoiceRecipientAddress?: string;
	  invoiceRecipientHandPhone?: string;
	  invoiceRecipientPhone?: string;
	  invoiceType?: string;
	  [k: string]: any;
	};
	mallInvoiceStr?: string;
	star?: number;
	expressType?: number;
	noLogisticsSend?: boolean;
	invoiceTitle?: string;
	invoiceContent?: string;
	deliveryStartTime?: string;
	deliveryEndTime?: string;
	isPreSale?: number;
	canceling?: boolean;
	encodeReceiverMobile?: string;
	encodeReceiverPhone?: string;
	idxEncodeReceiverPhone?: string;
	idxEncodeReceiverMobile?: string;
	payWayStr?: string;
	supplierId?: string;
	supplierName?: string;
	/**
	 * String
	 */
	tagList?: string[];
	tcpsStart?: string;
	tcpsEnd?: string;
	sendRemindFlag?: number;
	lastShipTime?: string;
	sendRemindHour?: string;
	shopId?: string;
	caid?: string;
	encodeReceiverAddress?: string;
	mainStatus?: string;
	biz?: string;
	idxEncodeReceiverAddress?: string;
	encodeReceiverName?: string;
	idxEncodeReceiverName?: string;
	orderMaskSn?: string;
	mallMaskId?: string;
	mallMaskName?: string;
	receiverId?: string;
	fdsStatus?: string;
	allowTime?: string;
	productSn?: string;
	oaid?: string;
	outerTid?: string;
	outerUid?: string;
	tradeSource?: string;
	orderType?: string;
	ext?: string;
	nzwOrderId?: string;
	ifFx?: boolean;
	childNum?: number;
	isPending?: number;
	singleStatus?: string;
	invoiceStatus?: string;
	red?: number;
	yellow?: number;
	green?: number;
	blue?: number;
	Purple?: number;
	/**
	 * TB
	 * TM
	 * PDD
	 * FXG
	 * ALI
	 * KSXD
	 * HAND
	 * JD
	 * INNER
	 * SPH
	 * OTHER
	 * XHS
	 * C2M
	 *
	 */
	erpPlatform?: {
	  [k: string]: any;
	};
	mergeMd5?: string;
	sellerRate?: boolean;
	/**
	 * MallPromotionDetail
	 */
	promotionDetails?: {
	  id?: string;
	  discountFee?: string;
	  giftItemId?: string;
	  giftItemName?: string;
	  giftItemNum?: string;
	  promotionDesc?: string;
	  promotionId?: string;
	  promotionName?: string;
	  outerId?: string;
	  skuId?: string;
	  skuOuterId?: string;
	  [k: string]: any;
	}[];
	type?: string;
	discountFee?: string;
	platformDiscount?: string;
	sellerDiscount?: string;
	price?: string;
	hasPostFee?: boolean;
	tradeAttr?: string;
	shippingType?: string;
	buyerCodFee?: string;
	adjustFee?: string;
	tradeFrom?: string;
	buyerRate?: boolean;
	esTime?: string;
	deliveryTime?: string;
	collectTime?: string;
	dispatchTime?: string;
	signTime?: string;
	deliveryCps?: string;
	isShShip?: boolean;
	invoiceKind?: string;
	invoiceType?: string;
	payerRegisterNo?: string;
	esRange?: string;
	esDate?: string;
	osDate?: string;
	osRange?: string;
	urgeShippingTime?: string;
	urge?: number;
	/**
	 * ExtraDelivery
	 */
	extraDeliveryList?: {
	  trackingNumber?: string;
	  logisticsId?: number;
	  logisticsCompany?: string;
	  [k: string]: any;
	}[];
	stockOutHandleStatus?: number;
	preSaleTime?: string;
	preSale?: number;
	riskControlStatus?: string;
	freeSF?: number;
	promiseService?: string;
	tmallPromise?: string;
	/**
	 * Object
	 */
	orderTagList?: {
	  [k: string]: any;
	}[];
	receiverNameMask?: string;
	receiverPhoneMask?: string;
	receiverAddressMask?: string;
	receiverMobileMask?: string;
	isStockOut?: number;
	sellerNick?: string;
	platform?: string;
	omnichannelParam?: string;
	/**
	 * ServiceOrder
	 */
	serviceOrders?: {
	  appleCareEmail?: string;
	  appleCareMpn?: string;
	  buyerNick?: string;
	  etPlateNumber?: string;
	  etSerTime?: string;
	  etShopName?: string;
	  etVerifiedShopName?: string;
	  extServiceBizId?: string;
	  itemOid?: number;
	  num?: number;
	  oid?: number;
	  oidStr?: string;
	  payment?: string;
	  picPath?: string;
	  price?: string;
	  refundId?: string;
	  sellerNick?: string;
	  serviceDetailUrl?: string;
	  serviceId?: number;
	  serviceOrderType?: string;
	  serviceOuterId?: string;
	  title?: string;
	  tmserSpuCode?: string;
	  totalFee?: string;
	  [k: string]: any;
	}[];
	timingPromise?: string;
	cutoffMinutes?: string;
	asdpBizType?: string;
	asdpAds?: string;
	shipInfo?: string;
	senderName?: string;
	senderPhone?: string;
	senderMobile?: string;
	senderProvince?: string;
	senderCity?: string;
	senderCounty?: string;
	senderAddress?: string;
	printTime?: string;
	shipTime?: string;
	queryReceiverName?: string;
	changeAdderFlag?: number;
	smartExp?: string;
	smartExpressTemplate?: string;
	packSmartExpressTemplate?: string;
	labelPrintStatus?: string;
	labelStatus?: string;
	promiseDeliveryTime?: string;
	promiseLogisticsCode?: string;
	wrapperDescription?: string;
	jdMergeMd5?: string;
	buyerOpenUid?: string;
	mergeTid?: string;
	packTotalGoodsNum?: number;
	packTotalTradeNum?: number;
	packTotalGoodsCategory?: number;
	packTotalPayment?: number;
	packTotalWeight?: number;
	packTotalType?: string;
	mergeTids?: string;
	abnormalAddress?: boolean;
	receivedPayment?: number;
	packLastShipTime?: string;
	serviceTagType?: string;
	itemCateNum?: number;
	ignoreType?: number;
	crossBorder?: boolean;
	consolidateType?: string;
	source?: string;
	pOrderUnfinished?: boolean;
	tradeStyle?: string;
	openAddressId?: string;
	[k: string]: any;
  }
  /**
 * ResponseBody<GetWarningDetailResponse> :ResponseBody
 */
export interface TradeGetWarningDetailResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * GetWarningDetailResponse
	 */
	data?: {
	  /**
	   * 告警ID ,MallTrade
	   */
	  trade?: MallTrade;
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * WebResponse :WebResponse
 */
export interface PrintPreCheckPrintLimitResponse {
	/**
	 * 响应状态码
	 */
	result?: number;
	/**
	 * 对result的状态文字描述
	 */
	message?: string;
	apiName?: string;
	/**
	 * 实际传送的对象 ,Object
	 */
	data?: {
	  [k: string]: any;
	};
	success?: boolean;
	[k: string]: any;
  }

/**
 * ResponseBody<ScanInspectShipRecordVO> :ResponseBody
 */

/**
 * ResponseBody<UserTradeNumLimitVO> :ResponseBody
 */
export interface PrintPreCheckTradeLimitResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * UserTradeNumLimitVO
   */
  data?: {
    /**
     * 总单量
     */
    tradeLimitNum?: number;
    /**
     * 已消耗单量
     */
    totalConsumeNum?: number;
    /**
     * 是否开启拦截
     */
    openLimit?: boolean;
    /**
     * 是否进行拦截
     */
    isLimit?: boolean;
    [k: string]: any;
  };
  [k: string]: any;
}

export interface TradeQueryScanTradeRecordByExNumberResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * ScanInspectShipRecordVO
   */
  data?: {
    /**
     * 前端生成的唯一码,代表唯一请求ID
     */
    uniqueCode?: string;
    /**
     * 快递单号
     */
    exNumber?: string;
    /**
     * 扫描时间
     */
    scanTime?: string;
    /**
     * 发货时间
     */
    sysShipTime?: string;
    /**
     * 快递模板
     */
    exName?: string;
    /**
     * 验货数量
     */
    inspectNum?: number;
    /**
     * 核验商品
     */
    inspectProductInfo?: string;
    /**
     * 识别码
     */
    productIdCodes?: string;
    [k: string]: any;
  };
  [k: string]: any;
}
/**
 * QueryTradeOrderConfigDTO :QueryTradeOrderConfigDTO
 */
export interface TradeUpdateQueryTradeOrderConfigRequest {
	/**
	 * 选中的排序Key
	 */
	selectedOrderKey: string;
	/**
	 * 正序倒序
	 */
	isDesc: boolean;
	/**
	 * 订单排序 ,QueryTradeOrderDTO
	 */
	queryTradeOrderList?: {
	  /**
	   * 排序唯一键
	   */
	  key?: string;
	  /**
	   * 排序名称
	   */
	  name?: string;
	  /**
	   * 排序类型,1=系统，2=自定义
	   */
	  type?: number;
	  /**
	   * 系统排序
	   */
	  systemOrderRule?: {
		[k: string]: any;
	  };
	  /**
	   * 自定义排序 ,QueryTradeCustomOrderRuleDTO
	   */
	  customOrderRule?: {
		/**
		 * TRADE_PLATFORM
		 * TRADE_SELLER_ID
		 * TRADE_PAY_TIME
		 * TRADE_CREATED_TIME
		 * TRADE_LAST_SHIP_TIME
		 * TRADE_ITEM_NUM
		 * TRADE_PAYMENT_AMOUNT
		 * TRADE_PROVINCE
		 * ORDER_ITEM_ID
		 * ORDER_ITEM_NAME
		 * ORDER_ITEM_ALIAS
		 * ORDER_ITEM_OUTER_ID
		 * ORDER_SKU_ID
		 * ORDER_SKU_NAME
		 * ORDER_SKU_OUTER_ID
		 * ORDER_SKU_ALIAS
		 * ORDER_SKU_COLOR_SIZE
		 * ORDER_SYS_SKU_NAME
		 * ORDER_SYS_SKU_OUTER_ID
		 *
		 */
		customOrderRule1?: {
		  [k: string]: any;
		};
		/**
		 * TRADE_PLATFORM
		 * TRADE_SELLER_ID
		 * TRADE_PAY_TIME
		 * TRADE_CREATED_TIME
		 * TRADE_LAST_SHIP_TIME
		 * TRADE_ITEM_NUM
		 * TRADE_PAYMENT_AMOUNT
		 * TRADE_PROVINCE
		 * ORDER_ITEM_ID
		 * ORDER_ITEM_NAME
		 * ORDER_ITEM_ALIAS
		 * ORDER_ITEM_OUTER_ID
		 * ORDER_SKU_ID
		 * ORDER_SKU_NAME
		 * ORDER_SKU_OUTER_ID
		 * ORDER_SKU_ALIAS
		 * ORDER_SKU_COLOR_SIZE
		 * ORDER_SYS_SKU_NAME
		 * ORDER_SYS_SKU_OUTER_ID
		 *
		 */
		customOrderRule2?: {
		  [k: string]: any;
		};
		/**
		 * TRADE_PLATFORM
		 * TRADE_SELLER_ID
		 * TRADE_PAY_TIME
		 * TRADE_CREATED_TIME
		 * TRADE_LAST_SHIP_TIME
		 * TRADE_ITEM_NUM
		 * TRADE_PAYMENT_AMOUNT
		 * TRADE_PROVINCE
		 * ORDER_ITEM_ID
		 * ORDER_ITEM_NAME
		 * ORDER_ITEM_ALIAS
		 * ORDER_ITEM_OUTER_ID
		 * ORDER_SKU_ID
		 * ORDER_SKU_NAME
		 * ORDER_SKU_OUTER_ID
		 * ORDER_SKU_ALIAS
		 * ORDER_SKU_COLOR_SIZE
		 * ORDER_SYS_SKU_NAME
		 * ORDER_SYS_SKU_OUTER_ID
		 *
		 */
		customOrderRule3?: {
		  [k: string]: any;
		};
		/**
		 * TRADE_PLATFORM
		 * TRADE_SELLER_ID
		 * TRADE_PAY_TIME
		 * TRADE_CREATED_TIME
		 * TRADE_LAST_SHIP_TIME
		 * TRADE_ITEM_NUM
		 * TRADE_PAYMENT_AMOUNT
		 * TRADE_PROVINCE
		 * ORDER_ITEM_ID
		 * ORDER_ITEM_NAME
		 * ORDER_ITEM_ALIAS
		 * ORDER_ITEM_OUTER_ID
		 * ORDER_SKU_ID
		 * ORDER_SKU_NAME
		 * ORDER_SKU_OUTER_ID
		 * ORDER_SKU_ALIAS
		 * ORDER_SKU_COLOR_SIZE
		 * ORDER_SYS_SKU_NAME
		 * ORDER_SYS_SKU_OUTER_ID
		 *
		 */
		customOrderRule4?: {
		  [k: string]: any;
		};
		/**
		 * TRADE_PLATFORM
		 * TRADE_SELLER_ID
		 * TRADE_PAY_TIME
		 * TRADE_CREATED_TIME
		 * TRADE_LAST_SHIP_TIME
		 * TRADE_ITEM_NUM
		 * TRADE_PAYMENT_AMOUNT
		 * TRADE_PROVINCE
		 * ORDER_ITEM_ID
		 * ORDER_ITEM_NAME
		 * ORDER_ITEM_ALIAS
		 * ORDER_ITEM_OUTER_ID
		 * ORDER_SKU_ID
		 * ORDER_SKU_NAME
		 * ORDER_SKU_OUTER_ID
		 * ORDER_SKU_ALIAS
		 * ORDER_SKU_COLOR_SIZE
		 * ORDER_SYS_SKU_NAME
		 * ORDER_SYS_SKU_OUTER_ID
		 *
		 */
		customOrderRule5?: {
		  [k: string]: any;
		};
		[k: string]: any;
	  };
	  [k: string]: any;
	}[];
	[k: string]: any;
  }
/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeUpdateQueryTradeOrderConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
  }


export interface QueryTradeOrderDTO{
	/**
	 * 排序唯一键
	 */
	key?: string;
	/**
	 * 排序名称
	 */
	name?: string;
	/**
	 * 排序类型,1=系统，2=自定义
	 */
	type?: number;
	/**
	 * 系统排序
	 */
	systemOrderRule?: {
	  [k: string]: any;
	};
	/**
	 * 自定义排序 ,QueryTradeCustomOrderRuleDTO
	 */
	customOrderRule?: {
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule1?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule2?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule3?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule4?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule5?: {
		[k: string]: any;
	  };
	  [k: string]: any;
	};
	[k: string]: any;
  }
export interface QueryTradeOrderConfigDTO{
	/**
	 * 选中的排序Key
	 */
	selectedOrderKey: string;
	/**
	 * 正序倒序
	 */
	isDesc: boolean;
	/**
	 * 订单排序 ,QueryTradeOrderDTO
	 */
	queryTradeOrderList?: QueryTradeOrderDTO[];
	[k: string]: any;
  }
/**
 * ResponseBody<QueryTradeOrderConfigDTO> :ResponseBody
 */
export interface TradeGetQueryTradeOrderConfigResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * QueryTradeOrderConfigDTO
	 */
	data?: QueryTradeOrderConfigDTO;
	[k: string]: any;
  }
/**
 * QueryTradeOrderDTO :QueryTradeOrderDTO
 */
export interface TradeSaveCustomQueryTradeOrderRequest {
	/**
	 * 排序唯一键
	 */
	key?: string;
	/**
	 * 排序名称
	 */
	name?: string;
	/**
	 * 排序类型,1=系统，2=自定义
	 */
	type?: number;
	/**
	 * 系统排序
	 */
	systemOrderRule?: {
	  [k: string]: any;
	};
	/**
	 * 自定义排序 ,QueryTradeCustomOrderRuleDTO
	 */
	customOrderRule?: {
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule1?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule2?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule3?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule4?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule5?: {
		[k: string]: any;
	  };
	  [k: string]: any;
	};
	[k: string]: any;
  }
/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeSaveCustomQueryTradeOrderResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
  }
/**
 * QueryTradeOrderDTO :QueryTradeOrderDTO
 */
export interface TradeDelCustomQueryTradeOrderRequest {
	/**
	 * 排序唯一键
	 */
	key?: string;
	/**
	 * 排序名称
	 */
	name?: string;
	/**
	 * 排序类型,1=系统，2=自定义
	 */
	type?: number;
	/**
	 * 系统排序
	 */
	systemOrderRule?: {
	  [k: string]: any;
	};
	/**
	 * 自定义排序 ,QueryTradeCustomOrderRuleDTO
	 */
	customOrderRule?: {
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule1?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule2?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule3?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule4?: {
		[k: string]: any;
	  };
	  /**
	   * TRADE_PLATFORM
	   * TRADE_SELLER_ID
	   * TRADE_PAY_TIME
	   * TRADE_CREATED_TIME
	   * TRADE_LAST_SHIP_TIME
	   * TRADE_ITEM_NUM
	   * TRADE_PAYMENT_AMOUNT
	   * TRADE_PROVINCE
	   * ORDER_ITEM_ID
	   * ORDER_ITEM_NAME
	   * ORDER_ITEM_ALIAS
	   * ORDER_ITEM_OUTER_ID
	   * ORDER_SKU_ID
	   * ORDER_SKU_NAME
	   * ORDER_SKU_OUTER_ID
	   * ORDER_SKU_ALIAS
	   * ORDER_SKU_COLOR_SIZE
	   * ORDER_SYS_SKU_NAME
	   * ORDER_SYS_SKU_OUTER_ID
	   *
	   */
	  customOrderRule5?: {
		[k: string]: any;
	  };
	  [k: string]: any;
	};
	[k: string]: any;
  }
/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface TradeDelCustomQueryTradeOrderResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
  }

/**
 * TradeQueryDTO :TradeQueryDTO
 */
export interface TradeQueryTradeItemCountRequest {
  /**
   * TB,淘宝,,TM,天猫,,PDD,拼多多,,FXG,抖音,,HAND,手工单;
   */
  platform?: string;
  /**
   * 店铺id
   */
  sellerId?: string;
  startTime?: string;
  endTime?: string;
  /**
   * 筛选时间类型1下单时间2付款时间3打印时间4发货时间
   */
  timeType?: number;
  /**
   * 订单状态ORDER_WAIT_PAY等待付款ORDER_PAID已付款ORDER_SHIPPED已发货,ORDER_SHIPPED_LITTLE部分发货ORDER_COMPLETED交易完成ALL_STATUS全部订单
   */
  status?: string;
  /**
   * 打印状态2仅快递单已打印3仅发货单已打印4快递单发货单均已打印0快递单发货单均未打印,商品标签未打印8商品标签已打印9均未打印10均已打印11
   */
  printStatus?: number;
  /**
   * 发货单打印标记0：未打印，1：已打印，null：所有状态
   */
  fhdPrintStatus?: number;
  /**
   * 商品标签打印标记0：未打印，1：已打印，null：所有状态
   */
  labelPrintStatus?: number;
  /**
   * 自定义的地址关键字
   */
  selValue?: string;
  /**
   * 精准匹配省市区如：[{"name":"辽宁省","childList":[]},{"name":"广西壮族自治区","childList":[]}]
   */
  areaJson?: string;
  /**
   * 省市区是否包含：0不包含1包含
   */
  areaContain?: number;
  /**
   * 1有买家留言2有卖家备注3有买家留言+有卖家备注4没有留言且没有备注5红6黄7绿8蓝色9紫色10有留言或备注11无留言12无备注13无留言或无备注0灰旗，橙色-14、浅蓝-15、浅粉-16、深绿-17、桃红-18
   */
  flagValue?: string;
  /**
   * 留言内容(多个用,隔开)
   */
  buyerMessage?: string;
  /**
   * 留言备注1有买家留言2有卖家备注3有买家留言+有卖家备注4没有留言且没有备注10有留言或备注11无留言12无备注13无留言或无备注
   */
  commentsRemarks?: string;
  /**
   * 留言备注内容(多个用,隔开)
   */
  sellerMemo?: string;
  /**
   * 留言内容或者留言备注内容(多个用,隔开)
   */
  buyerMessageOrSellerMemo?: string;
  /**
   * 旗帜5红6黄7绿8蓝色9紫色0灰,橙色-14、浅蓝-15、浅粉-16、深绿-17、桃红-18
   */
  sellerFlag?: string;
  /**
   * 1,2_1表示包含红黄旗从自定义备注接口找0表示不包含1表示包含
   */
  flagSelValue?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品id
   */
  skuId?: string;
  /**
   * 货品id
   */
  sysItemId?: string;
  /**
   * 货品规格id
   */
  sysSkuId?: string;
  /**
   * 货品规格编码 ,String
   */
  sysOuterSkuId?: string[];
  /**
   * 常用标签包含及不包含,{,key:'merge',,value:'合并订单',},,{,key:'pending',,value:'挂起订单',},,{,key:'isFirstSend',,value:'先发货订单',},,{,key:'preSale',,value:'预售订单(含预售商品)',},,{,key:'fare',,value:'含运费',},,{,key:'town',,value:'乡镇',},,{,key:'haveGoodLabel',,value:'已生成商品标签',},,{,key:'urge',,value:'催发货',},{,key:'noMerge',,value:'合并订单',},,{,key:'noPending',,value:'挂起订单',},,{,key:'noPreSale',,value:'预售订单(含预售商品)',},,{,key:'noFare',,value:'含运费',},,{,key:'noTown',,value:'乡镇',},,{,key:'noGoodLabel',,value:'已生成商品标签',},,{,key:'nourge',,value:'催发货',},,{,key:'ptChangeItem',,value:'线上改商品',} ,String
   */
  tradeLabelList?: string[];
  /**
   * 标签包含查询（新） ,String
   */
  includeServiceTagList?: string[];
  /**
   * 标签不包含查询（新） ,String
   */
  excludeServiceTagList?: string[];
  /**
   * 排除异常,unBindingItem ,String
   */
  tradeExceptionList?: string[];
  /**
   * 快捷查询,合并订单merge;,非合并订单noMerge,有运费订单fare;,无运费订单noFare;,乡镇订单town,非乡镇订单noTown,有发票invoice,无发票noInvoice,有挂起pending,无挂起noPending,疫情地区epidemicArea,非疫情地区noEpidemicArea,已生成商品标签haveGoodLabel,未生成商品标签noGoodLabel,京东云仓jdyc,京东京仓jdjc,京东京配jdjp,是否快捷查询preSell,预售订单preSale,非预售订单noPreSale
   */
  quickQuery?: string;
  /**
   * {@codetradeQuery.getSellAttribute},查询销售属性,1:单商品单规格单件,,2:单商品单规格多件,,3:单商品多规格多件,,4:多商品多规格多件
   */
  sellAttribute?: string;
  /**
   * 查询销售属性(多选),1:单商品单规格单件,,2:单商品单规格多件,,3:单商品多规格多件,,4:多商品多规格多件 ,String
   */
  sellAttributeList?: string[];
  /**
   * 商品名称编码简称id
   */
  shortNameIncluding?: string;
  /**
   * 规格名称规格别名查询
   */
  skuIncluding?: string;
  /**
   * 商品名称编码简称id查询逻辑集合,新版查询，逗号已经不用了 ,String
   */
  shortNameIncludingList?: string[];
  /**
   * 规格名称规格别名查询集合,新版查询，逗号已经不用了 ,String
   */
  skuIncludingList?: string[];
  /**
   * 商品名称编码简称id(爆款搜索专用)
   */
  shortNameIncludingForTopItem?: string;
  /**
   * 规格名称规格别名查询即这个查询字段有以上以上字段有一个匹配即可(爆款搜索专用)
   */
  skuIncludingForTopItem?: string;
  /**
   * 订单标记已标记hasMark为标记noMark标记查询存储的值
   */
  bizMark?: string;
  /**
   * 订单标记0不包含1包含
   */
  bizMarkContain?: number;
  /**
   * 买家昵称,隔开
   */
  buyerNick?: string;
  /**
   * 收件人,隔开
   */
  receiveName?: string;
  /**
   * 手机号,隔开
   */
  mobile?: string;
  /**
   * 订单编号,隔开
   */
  tid?: string;
  /**
   * 平台订单编号,隔开
   */
  ptTid?: string;
  /**
   * 供分销推送订单订单编号
   */
  distributorTid?: string;
  /**
   * 运单号,隔开
   */
  sid?: string;
  /**
   * 0快递1物流
   */
  logisticsType?: string;
  /**
   * HAS_REFUND("退款成功"),,NOT_REFUND("无售后或售后关闭");
   */
  refundStatus?: string;
  /**
   * 订单总金额1-100
   */
  payment?: string;
  /**
   * 宝贝包含或不包含标识：,1包含,0不包含
   */
  equalFlag?: number;
  /**
   * 订单重量范围5-10
   */
  weightRange?: string;
  /**
   * 订单总数量子订单数量5-10
   */
  orderRange?: string;
  /**
   * 商品总数量1-50
   */
  goodsTotalNum?: string;
  /**
   * 商品种类1-50
   */
  goodsTypeNum?: string;
  /**
   * 是否精确精准就是false非精准就是true
   */
  isPrecise?: boolean;
  /**
   * 是否精确到订单精准就是false非精准就是true
   */
  isPreciseByTrade?: boolean;
  /**
   * 给mock用,此值传1会有订单
   */
  testStatus?: number;
  pageNo?: number;
  pageSize?: number;
  /**
   * 1:先付款的在前边,,2后付款的在前边,,3先下单的在前边,,4后下单的在前边,,5.将相同商品挨在一起排列,,6实际支付金额大的在前边，,7实际支付金额小的在前边,,8.数量大的在前边,9.数量小的在前边,10.按订单发货时间，先超时的在前边,11.按订单发货时间，先超时的在后边,12订单修改时间升序，先修改的在前边,13订单修改时间降序，后修改的在前边,14省份首拼字母靠前的在前边,16按商家编码，升序
   */
  tidOrderType?: number;
  /**
   * MultiShopDTO
   */
  multiShopS?: {
    sellerId?: number;
    platform?: string;
    [k: string]: any;
  }[];
  /**
   * 平台查询，未选择
   */
  isPlatformEmptyQuery?: boolean;
  /**
   * 异常订单0异常1无异常
   */
  abnormalStatus?: number;
  /**
   * 短信验证码
   */
  verifyCode?: string;
  /**
   * 剩余发货时间搜索,ALL("全部"),ALREADY_DELAY("已超时"),LESS_THAN_SIX_HOURS("剩余时间小于6小时"),LESS_THAN_TWELVE_HOURS("剩余时间小于12小时"),LESS_THAN_TWENTY_FOUR_HOURS("剩余时间小于24小时"),GREATER_THAN_TWENTY_FOUR_HOURS("剩余时间大于24小时")
   */
  residueSendTimeSearch?: {
    [k: string]: any;
  };
  /**
   * 自定义剩余发货时间,范围0-999（例："12-999"；"0-12"）
   */
  customizeResidueSendTime?: string;
  /**
   * 智选快递（废弃）
   */
  smartSelectExpress?: string;
  /**
   * 智选快递模板维度id
   */
  smartExpressTemplateId?: string;
  serviePromiseType?: number;
  /**
   * 导入快递单号生成版本号
   */
  tradeExpressImportLogSequence?: string;
  /**
   * 相同昵称挨在一起true:是false：否
   */
  sortBySameNickname?: boolean;
  /**
   * true货品包含,false货品不包含
   */
  goodsContain?: boolean;
  /**
   * 货品简称编码
   */
  goodsAliasOrIdStr?: string;
  /**
   * 货品规格名称编码别名
   */
  goodsSkuNameOrIdOrAliasStr?: string;
  /**
   * 为了去除逗号，采用数组 ,String
   */
  goodsAliasOrIdStrList?: string[];
  /**
   * 为了去除逗号，采用数组 ,String
   */
  goodsSkuNameOrIdOrAliasStrList?: string[];
  /**
   * 是否先发货
   */
  isFirstSend?: boolean;
  /**
   * 商品库存状态,ALL全部(默认);HAS_STOCK有货;STOCK_WARN警戒;NO_STOCK无货;PART_STOCK部分有货;,,OutOfStockStatusEnum[ALL,HAS_STOCK,STOCK_WARN,NO_STOCK,PART_STOCK,val,desc]
   */
  goodStockStatus?: {
    [k: string]: any;
  };
  /**
   * 隐藏挂起订单
   */
  hidePending?: boolean;
  /**
   * 隐藏已发货的商品订单
   */
  hideAllRefunded?: boolean;
  /**
   * 导出方式，0：导出当前查询结果,1：导出勾选订单（勾选订单记录在tid字段中）
   */
  method?: number;
  /**
   * 纬度，0：订单纬度,1：商品纬度,表字段:latitude
   */
  latitude?: number;
  /**
   * 抖店达人name或者id
   */
  authorInfo?: string;
  /**
   * 跨界订单true为跨界订单
   */
  crossBorder?: boolean;
  /**
   * 异常订单unBindingItem商品未绑定
   */
  exceptionFlag?: string;
  /**
   * 订单来源:platfromOrder平台订单,handOrder手工订单
   */
  orderSource?: string;
  /**
   * 订单类型,详见TradeTypeEnum ,String
   */
  tradeTypeTagList?: string[];
  /**
   * 经销商用户id集合 ,Long
   */
  distributorUserIds?: number[];
  /**
   * String
   */
  marketIncludingList?: string[];
  /**
   * String
   */
  dangKouIncludingList?: string[];
  /**
   * String
   */
  supplierIncludingList?: string[];
  /**
   * String
   */
  authorIncludingList?: string[];
  includeIngColorOrSize?: boolean;
  /**
   * String
   */
  colorIncludingList?: string[];
  /**
   * String
   */
  sizeIncludingList?: string[];
  /**
   * 商品查询,"itemInfoQueryMap":{,"true":[,{,"itemQueryEnum":"ITEM_ID",,"values":[,"商品ID列表",],},,{,"itemQueryEnum":"ITEM_TITLE",,"values":[,"商品名称列表",],},,{,"itemQueryEnum":"ITEM_ALIAS",,"values":[,"简称列表",],},,{,"itemQueryEnum":"OUTER_ID",,"values":[,"商品编码列表",],},,{,"itemQueryEnum":"SYS_SKU_NAME",,"values":[,"规格名称列表",],},,{,"itemQueryEnum":"SYS_SKU_ALIAS",,"values":[,"规格别名列表",],},,{,"itemQueryEnum":"OUTER_SKU_ID",,"values":[,"规格编码列表",],},,{,"itemQueryEnum":"SYS_OUTER_SKU_ID",,"values":[,"货品规格编码列表",],},],}(该参数为map)
   */
  itemInfoQueryMap?: {
    /**
     * Boolean
     */
    mapKey?: {
      [k: string]: any;
    };
    /**
     * List<ItemInfoQueryDTO>
     */
    mapValue?: {
      [k: string]: any;
    };
    [k: string]: any;
  };
  /**
   * 验货查询
   */
  inspectionQuery?: boolean;
  /**
   * 订单号、底单记录map(该参数为map)
   */
  tidMallApplyLogMap?: {
    /**
     * String
     */
    mapKey?: {
      [k: string]: any;
    };
    /**
     * List<MallDzmdApplyLogQueryDTO>
     */
    mapValue?: {
      [k: string]: any;
    };
    [k: string]: any;
  };
  /**
   * 验货发货根据运单号查子单(该参数为map)
   */
  tidOidListMap?: {
    /**
     * String
     */
    mapKey?: {
      [k: string]: any;
    };
    /**
     * List<String>
     */
    mapValue?: {
      [k: string]: any;
    };
    [k: string]: any;
  };
  /**
   * 查询来源,默认：批打,1：分销订单明细
   */
  querySource?: number;
  /**
   * 查询结果是否需要运费等金额数据.,0or默认：不需要,1:需要
   */
  queryFreightResultFlag?: number;
  /**
   * 线下备注查询方式0:线下备注,过滤了线下备注的条件,查询所有数据1:有线下备注场景2:无线下备注场景
   */
  sysMemoMode?: number;
  /**
   * 线下备注的内容,支持多个查询,仅sysMemoMode为1时有内容 ,String
   */
  sysMemoList?: string[];
  /**
   * 货品分类id
   */
  classifyId?: number;
  /**
   * 货品分类id列表 ,Long
   */
  classifyIdList?: number[];
  /**
   * 颜色和尺码是否精确精准就是false非精准就是true
   */
  isColorAndSizePrecise?: boolean;
  /**
   * 预设条件不满足日期条件时，不返回数据,true：不返回数据
   */
  isPresetNotReturnData?: boolean;
  [k: string]: any;
}

/**
 * ResponseBody<TradeItemCountVo> :ResponseBody
 */
export interface TradeQueryTradeItemCountResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * TradeItemCountVo
   */
  data?: {
    /**
     * RdsTradeItemCountDTO
     */
    itemCountList?: {
      itemId?: string;
      itemTitle?: string;
      tradeNum?: number;
      totalNum?: number;
      /**
       * RdsTradeItemSkuCountDTO
       */
      skuCountList?: {
        skuId?: string;
        skuTitle?: string;
        tradeNum?: number;
        totalNum?: number;
        [k: string]: any;
      }[];
      [k: string]: any;
    }[];
    tradeNum?: number;
    totalNum?: number;
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ResponseBody<BatchUpdateOrderSerialNumberRespVO> :ResponseBody
 */
export interface TradeBatchUpdateOrderSerialNumberResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BatchUpdateOrderSerialNumberRespVO
   */
  data?: {
    /**
     * 错误详情集合 ,FailReasonVO
     */
    failReasonVOList?: {
      /**
       * 订单编号
       */
      tid?: string;
      /**
       * 子订单编号
       */
      oid?: string;
      /**
       * 错误信息
       */
      errorMsg?: string;
      [k: string]: any;
    }[];
    /**
     * 更新商品识别码成功的tid集合 ,String
     */
    successTidSet?: string[];
    /**
     * 生成成功的tid-oids集合key:tidvalue:oid集合(该参数为map)
     */
    successTidOidMap?: {
      /**
       * String
       */
      mapKey?: {
        [k: string]: any;
      };
      /**
       * Set<String>
       */
      mapValue?: {
        [k: string]: any;
      };
      [k: string]: any;
    };
    /**
     * 失败数统计
     */
    failCount?: number;
    /**
     * 更新商品识别码失败的tid集合 ,String
     */
    failTidSet?: string[];
    [k: string]: any;
  };
  [k: string]: any;
}


/**
 * List<UpdateOrderExtDTO>
 */
export type TradeBatchUpdateOrderSerialNumberRequest = {
  /**
   * 订单编号
   */
  tid: string;
  /**
   * 子订单id列表
   */
  oid: string;
  /**
   * 店铺id
   */
  sellerId?: number;
  /**
   * 平台
   */
  platform?: string;
  /**
   * 商品识别码（SN码、IMEI号、ICCID码)
   */
  productIdCode?: string;
  [k: string]: any;
}[];

/**
 * List<BatchSendDTO>
 */
export type TradeBatchAppendSendRequest = {
  /**
   * 如果存在合单，那么数组中分别组装数据传入，类似如下格式,[,[{tid:1,exNumber:444555,exCode:YTO,其他所有字段}],,[{tid:2,exNumber:444556,exCode:YTO,其他所有字段}],,[{tid:3,exNumber:444556,exCode:YTO,其他所有字段},{tid:4,exNumber:444556,exCode:YTO,其他所有字段}],] ,BatchSendTradeDTO
   */
  subTrades?: {
    togetherId?: string;
    /**
     * 母单号，针对快运场景
     */
    pYdNo?: string;
    /**
     * 是否拆单发货，默认非拆单
     */
    split?: boolean;
    /**
     * 多包裹发货仅pddfxgtbsph支持
     */
    multiPack?: boolean;
    /**
     * 是否为货到付款订单，1标识是，0标识否，默认非货到付款订单
     */
    cod?: number;
    /**
     * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货；7重新发货；8买家自提(无需物流)；9卖家配送(无需物流),配送人员和联系方式需必填
     */
    sendType?: string;
    /**
     * 单号来源
     */
    waybillNoSource?: string;
    /**
     * 用户id(注册账号)
     */
    userId?: number;
    /**
     * 用户id(单号的使用者，也就是说订单的归属者)
     */
    sellerId?: number;
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 订单来源
     */
    source?: string;
    /**
     * 买家账号
     */
    buyerNick?: string;
    /**
     * 买家唯一标识
     */
    buyerOpenUid?: string;
    /**
     * 收件省
     */
    receiverProvince?: string;
    /**
     * 收件市
     */
    receiverCity?: string;
    /**
     * 收件区
     */
    receiverCounty?: string;
    /**
     * 收件地址
     */
    receiverAddress?: string;
    /**
     * 收件街道
     */
    receiverTown?: string;
    /**
     * 收件人
     */
    receiverName?: string;
    /**
     * 手机
     */
    mobile?: string;
    /**
     * 固定电话
     */
    tel?: string;
    /**
     * 快递id
     */
    exId?: number;
    /**
     * 快递code
     */
    exCode?: string;
    /**
     * 快递名
     */
    exName?: string;
    /**
     * 快递单号
     */
    exNumber?: string;
    /**
     * 多包裹发货的字段 ,String
     */
    exNumberList?: string[];
    /**
     * 商品数量
     */
    goodsNum?: number;
    /**
     * 重量
     */
    weight?: string;
    /**
     * 拆单子订单串。oid1,ords,oid3
     */
    oids?: string;
    ptOids?: string;
    /**
     * tid
     */
    tid?: string;
    /**
     * 平台订单编号
     */
    ptTid?: string;
    /**
     * 买家留言
     */
    buyerMsg?: string;
    /**
     * 卖家备注旗帜类型
     */
    sellerFlag?: number;
    /**
     * 本地标记类型(该参数为map)
     */
    sellerFlagSys?: {
      /**
       * String
       */
      mapKey?: {
        [k: string]: any;
      };
      /**
       * Integer
       */
      mapValue?: {
        [k: string]: any;
      };
      [k: string]: any;
    };
    /**
     * 卖家备注
     */
    sellerMemo?: string;
    /**
     * 发货内容
     */
    info?: string;
    /**
     * 打印序号
     */
    printNum?: string;
    /**
     * 包裹号
     */
    packageId?: number;
    /**
     * 快递单类型1普通面单,2网点,3云栈电子面单
     */
    kddType?: number;
    /**
     * 是哪个页面的操作0：单打1：批打2：预发货3：自动预发货4：手工订单5厂家代发
     */
    optionType?: number;
    /**
     * 订单实付金额
     */
    payment?: string;
    /**
     * BatchSendOrderDTO
     */
    orders?: {
      /**
       * 商品数量
       */
      num?: number;
      /**
       * numIid
       */
      numIid?: string;
      skuUuid?: string;
      skuId?: string;
      tid?: string;
      ptOid?: string;
      oid?: string;
      /**
       * 子订单实付金额
       */
      payment?: string;
      systemNumIid?: number;
      systemSkuId?: number;
      /**
       * 是否忽略，0否，1是
       */
      ignore?: number;
      /**
       * 是否赠品
       */
      gift?: boolean;
      /**
       * 换商品逻辑0:默认；1:置换商品；2置换货品；3置换数量；4新增的货品；5新增的商品;6平台换商品
       */
      sysExchangeType?: number;
      /**
       * 是否需要上传商品识别码
       */
      needSerialNumber?: boolean;
      /**
       * 商品识别码（SN码、IMEI号、ICCID码)
       */
      productIdCode?: string;
      /**
       * 是否是
       */
      isPartConsign?: boolean;
      /**
       * 是否跳过平台发货
       */
      isSkipPlatformSend?: boolean;
      /**
       * 快手小店，发货时区分回传SN和IMEI使用,发货时，无值不传码，有值需传码，有多个值则传多个类型的码，回传平台时需要根据类型回传至不同字段,[1:需要SN],[2:需要IMEI]
       */
      serialType?: string;
      /**
       * 平台商品数量,不是换货商品&&不是改数量商品可能为空，为空取订单的数量num
       */
      platformNum?: number;
      [k: string]: any;
    }[];
    /**
     * overseaTracing（海淘溯源码id）内容。
     */
    overseaTracing?: string;
    /**
     * PDD支持imei（手机串号
     */
    imei?: string;
    /**
     * PDD支持deviceSn（设备序列号）
     */
    deviceSn?: string;
    /**
     * 手机号索引串
     */
    idxEncodeReceiverMobile?: string;
    /**
     * 加密详情索引串
     */
    idxEncodeReceiverAddress?: string;
    /**
     * 加密收件人索引串,,@return
     */
    idxEncodeReceiverName?: string;
    /**
     * 收件人姓名脱敏
     */
    receiverNameMask?: string;
    /**
     * 收件人电话脱敏
     */
    receiverPhoneMask?: string;
    /**
     * 收件人地址脱敏
     */
    receiverAddressMask?: string;
    /**
     * 收件人手机号脱敏
     */
    receiverMobileMask?: string;
    /**
     * 加密字段，不同字段归属平台字段可能不一致，比如淘宝为oaid
     */
    caid?: string;
    /**
     * 加密串tid
     */
    encodeTid?: string;
    /**
     * 订单地址是否解密
     */
    isDecrypted?: number;
    /**
     * 距离最晚发货时间时长
     */
    sendRemindHour?: string;
    /**
     * 订单状态
     */
    status?: string;
    /**
     * 退款状态,1-有退款NOT_REFUND,0-无退款HAS_REFUND
     */
    refundStatus?: string;
    /**
     * crossBorder拼多多跨境订单
     */
    tradeType?: string;
    /**
     * 是否是空包裹订单,1:自动标记，2：不是，3：手动标记，4：手动取消,默认不是
     */
    ignoreType?: number;
    /**
     * 如果是供应商订单，需要传入分销商id
     */
    saleUserId?: number;
    /**
     * 合单销售属性
     */
    packTotalType?: string;
    /**
     * 是否需要上传商品识别码
     */
    needSerialNumber?: boolean;
    /**
     * 发货&拒绝退款
     */
    isRefundReject?: boolean;
    /**
     * 发件人手机号
     */
    sendTel?: string;
    /**
     * 拆分订单，按数量拆分时，未发货的订单，追加发货快递单号，如果平台支持追加快递单号，根据此参数判断即可
     */
    isAppendSendYdNo?: boolean;
    /**
     * 地址库ID.
     */
    refundAddressId?: string;
    /**
     * 是否为整单发货
     */
    fullOrderSend?: boolean;
    [k: string]: any;
  }[];
  /**
   * 发货来源,PD("批量打印发货"),,YFH_REAL_SEND("预发货真正发货"),,LABEL(2"小标签扫描发货"),,SCAN_TRADE_SEND(3,"扫码订单发货"),,@linkTradeSendSourceEnum
   */
  sendFrom?: {
    [k: string]: any;
  };
  /**
   * 页面操作类型，默认为发货，参数OperateLogTypeEnum枚举
   */
  optionType?: number;
  [k: string]: any;
}[];

/**
 * ResponseBody<List<BatchSendTradeResultDTO>> :ResponseBody
 */
export interface TradeBatchAppendSendResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BatchSendTradeResultDTO
   */
  data?: {
    /**
     * 子订单发货结果记录 ,BatchSendSubTradeResultDTO
     */
    subTradeInfos?: {
      tid?: string;
      oids?: string;
      isSuccess?: boolean;
      /**
       * 失败信息
       */
      message?: string;
      result?: number;
      [k: string]: any;
    }[];
    /**
     * 结果标识,100发货成功，101,部分发货成功，102拆单发货成功，103发货失败
     */
    result?: number;
    togetherId?: string;
    isSuccess?: boolean;
    /**
     * 失败信息
     */
    message?: string;
    /**
     * 买家旺旺
     */
    buyerNick?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * PrintBgKddInfoListRequest :PrintBgKddInfoListRequest
 */
export interface TradePrintGetBgKddInfoListByExNumberRequest {
  /**
   * 运单号集合 ,String
   */
  exNumberList?: string[];
  [k: string]: any;
}

/**
 * ResponseBody<List<PrintBgKddInfoDTO>> :ResponseBody
 */
export interface TradePrintGetBgKddInfoListByExNumberResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * PrintBgKddInfoDTO
   */
  data?: {
    id?: number;
    /**
     * 用户Id
     */
    userId?: number;
    /**
     * 取号时间
     */
    ydnoGetTime?: string;
    /**
     * 打印时间
     */
    printTime?: string;
    /**
     * 打印时间
     */
    sendGoodsTime?: string;
    /**
     * 回收时间
     */
    cancelYdnoTime?: string;
    /**
     * 取号状态0未取过号1已取号
     */
    ydnoGetStatus?: number;
    /**
     * 打印状态0未打印1已打印
     */
    printStatus?: number;
    /**
     * 发货状态
     */
    sendGoodsStatus?: number;
    /**
     * 回收状态
     */
    cancelYdnoStatus?: number;
    /**
     * 快递id
     */
    exId?: number;
    /**
     * 快递code
     */
    exCode?: string;
    /**
     * 快递名
     */
    exName?: string;
    /**
     * 快递单号
     */
    exNumber?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ReqLiveTradePrintTaskDetailDTO :ReqLiveTradePrintTaskDetailDTO
 */
export interface TradeLivePrintGetLastTaskV2Request {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  userId?: number;
  requestVersion?: string;
  [k: string]: any;
}

/**
 * ResponseBody<RspLiveTradePrintTaskDTO> :ResponseBody
 */
export interface TradeLivePrintGetLastTaskV2Response {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspLiveTradePrintTaskDTO
   */
  data?: {
    /**
     * 是否有直播打印任务
     */
    hasLivePrintTask?: boolean;
    /**
     * 创建失败提示
     */
    createFailTip?: string;
    /**
     * 共计时长
     */
    liveTimeStr?: string;
    /**
     * id
     */
    id?: number;
    /**
     * 用户ID
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * @mbg.generated,直播场次,表字段:live_no
     */
    liveNo?: string;
    /**
     * @mbg.generated,直播类型，1=店铺直播，2=达人直播,表字段:live_type
     */
    liveType?: number;
    /**
     * 直播时长，单位：小时
     */
    liveTimeLength?: string;
    /**
     * 直播打印开始时间
     */
    livePrintTimeStart?: string;
    /**
     * 直播打印结束时间
     */
    livePrintTimeEnd?: string;
    /**
     * 直播打印实际结束时间
     */
    livePrintTimeActualEnd?: string;
    /**
     * 业务唯一值
     */
    uniqueBizCode?: string;
    /**
     * 标签已打印数量
     */
    alreadyPrintNum?: number;
    /**
     * 过滤订单数量
     */
    filterTradeNum?: number;
    /**
     * 队列中未打印数量
     */
    noPrintNum?: number;
    /**
     * 执行状态0等待执行，1执行中，2成功，3失败4关闭
     */
    taskStatus?: number;
    /**
     * 失败原因
     */
    errorMsg?: string;
    /**
     * 任务创建人
     */
    createTaskUserName?: string;
    /**
     * 任务创建人id
     */
    createTaskUserId?: string;
    /**
     * 任务创建节点IP
     */
    createTaskSourceHost?: string;
    /**
     * 关闭任务人
     */
    closeTaskUserName?: string;
    /**
     * 关闭任务人id
     */
    closeTaskUserId?: number;
    /**
     * 任务关闭节点IP
     */
    closeTaskHost?: string;
    /**
     * 创建时间
     */
    gmtCreated?: string;
    /**
     * 修改时间
     */
    gmtModified?: string;
    /**
     * 请求参数json，用户设置的过滤条件
     */
    requestJson?: string;
    /**
     * 店铺信息集合 ,PlatformShopInfo
     */
    shopList?: {
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      [k: string]: any;
    }[];
    /**
     * 达人信息集合 ,AuthorInfo
     */
    authorList?: {
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 达人ID
       */
      authorId?: string;
      /**
       * 达人名称
       */
      authorName?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}



/**
 * ResponseBody<RspLiveTradePrintTaskDTO> :ResponseBody
 */
export interface TradeLivePrintGetLastTaskResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspLiveTradePrintTaskDTO
   */
  data?: {
    /**
     * 是否有直播打印任务
     */
    hasLivePrintTask?: boolean;
    /**
     * 创建失败提示
     */
    createFailTip?: string;
    /**
     * 共计时长
     */
    liveTimeStr?: string;
    /**
     * id
     */
    id?: number;
    /**
     * 用户ID
     */
    userId?: number;
    /**
     * 直播时长，单位：小时
     */
    liveTimeLength?: string;
    /**
     * 直播打印开始时间
     */
    livePrintTimeStart?: string;
    /**
     * 直播打印结束时间
     */
    livePrintTimeEnd?: string;
    /**
     * 直播打印实际结束时间
     */
    livePrintTimeActualEnd?: string;
    /**
     * 业务唯一值
     */
    uniqueBizCode?: string;
    /**
     * 标签已打印数量
     */
    alreadyPrintNum?: number;
    /**
     * 过滤订单数量
     */
    filterTradeNum?: number;
    /**
     * 队列中未打印数量
     */
    noPrintNum?: number;
    /**
     * 执行状态0等待执行，1执行中，2成功，3失败4关闭
     */
    taskStatus?: number;
    /**
     * 失败原因
     */
    errorMsg?: string;
    /**
     * 任务创建人
     */
    createTaskUserName?: string;
    /**
     * 任务创建人id
     */
    createTaskUserId?: string;
    /**
     * 任务创建节点IP
     */
    createTaskSourceHost?: string;
    /**
     * 关闭任务人
     */
    closeTaskUserName?: string;
    /**
     * 关闭任务人id
     */
    closeTaskUserId?: number;
    /**
     * 任务关闭节点IP
     */
    closeTaskHost?: string;
    /**
     * 创建时间
     */
    gmtCreated?: string;
    /**
     * 修改时间
     */
    gmtModified?: string;
    /**
     * 请求参数json，用户设置的过滤条件
     */
    requestJson?: string;
    /**
     * 店铺信息集合 ,PlatformShopInfo
     */
    shopList?: {
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqLiveTradePrintTaskDetailDTO :ReqLiveTradePrintTaskDetailDTO
 */
export interface TradeLivePrintQueryTaskByIdRequest {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  userId?: number;
  [k: string]: any;
}

/**
 * ResponseBody<RspLiveTradePrintTaskDTO> :ResponseBody
 */
export interface TradeLivePrintQueryTaskByIdResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspLiveTradePrintTaskDTO
   */
  data?: {
    /**
     * 是否有直播打印任务
     */
    hasLivePrintTask?: boolean;
    /**
     * 创建失败提示
     */
    createFailTip?: string;
    /**
     * 共计时长
     */
    liveTimeStr?: string;
    /**
     * id
     */
    id?: number;
    /**
     * 用户ID
     */
    userId?: number;
    /**
     * 直播时长，单位：小时
     */
    liveTimeLength?: string;
    /**
     * 直播打印开始时间
     */
    livePrintTimeStart?: string;
    /**
     * 直播打印结束时间
     */
    livePrintTimeEnd?: string;
    /**
     * 直播打印实际结束时间
     */
    livePrintTimeActualEnd?: string;
    /**
     * 业务唯一值
     */
    uniqueBizCode?: string;
    /**
     * 标签已打印数量
     */
    alreadyPrintNum?: number;
    /**
     * 过滤订单数量
     */
    filterTradeNum?: number;
    /**
     * 队列中未打印数量
     */
    noPrintNum?: number;
    /**
     * 执行状态0等待执行，1执行中，2成功，3失败4关闭
     */
    taskStatus?: number;
    /**
     * 失败原因
     */
    errorMsg?: string;
    /**
     * 任务创建人
     */
    createTaskUserName?: string;
    /**
     * 任务创建人id
     */
    createTaskUserId?: string;
    /**
     * 任务创建节点IP
     */
    createTaskSourceHost?: string;
    /**
     * 关闭任务人
     */
    closeTaskUserName?: string;
    /**
     * 关闭任务人id
     */
    closeTaskUserId?: number;
    /**
     * 任务关闭节点IP
     */
    closeTaskHost?: string;
    /**
     * 创建时间
     */
    gmtCreated?: string;
    /**
     * 修改时间
     */
    gmtModified?: string;
    /**
     * 请求参数json，用户设置的过滤条件
     */
    requestJson?: string;
    /**
     * 店铺信息集合 ,PlatformShopInfo
     */
    shopList?: {
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ResponseBody<List<RspLiveTradePrintTaskDTO>> :ResponseBody
 */
export interface TradeLivePrintQueryTaskListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspLiveTradePrintTaskDTO
   */
  data?: {
    /**
     * 是否有直播打印任务
     */
    hasLivePrintTask?: boolean;
    /**
     * 创建失败提示
     */
    createFailTip?: string;
    /**
     * 共计时长
     */
    liveTimeStr?: string;
    /**
     * id
     */
    id?: number;
    /**
     * 用户ID
     */
    userId?: number;
    /**
     * 直播时长，单位：小时
     */
    liveTimeLength?: string;
    /**
     * 直播打印开始时间
     */
    livePrintTimeStart?: string;
    /**
     * 直播打印结束时间
     */
    livePrintTimeEnd?: string;
    /**
     * 直播打印实际结束时间
     */
    livePrintTimeActualEnd?: string;
    /**
     * 业务唯一值
     */
    uniqueBizCode?: string;
    /**
     * 标签已打印数量
     */
    alreadyPrintNum?: number;
    /**
     * 过滤订单数量
     */
    filterTradeNum?: number;
    /**
     * 队列中未打印数量
     */
    noPrintNum?: number;
    /**
     * 执行状态0等待执行，1执行中，2成功，3失败4关闭
     */
    taskStatus?: number;
    /**
     * 失败原因
     */
    errorMsg?: string;
    /**
     * 任务创建人
     */
    createTaskUserName?: string;
    /**
     * 任务创建人id
     */
    createTaskUserId?: string;
    /**
     * 任务创建节点IP
     */
    createTaskSourceHost?: string;
    /**
     * 关闭任务人
     */
    closeTaskUserName?: string;
    /**
     * 关闭任务人id
     */
    closeTaskUserId?: number;
    /**
     * 任务关闭节点IP
     */
    closeTaskHost?: string;
    /**
     * 创建时间
     */
    gmtCreated?: string;
    /**
     * 修改时间
     */
    gmtModified?: string;
    /**
     * 请求参数json，用户设置的过滤条件
     */
    requestJson?: string;
    /**
     * 店铺信息集合 ,PlatformShopInfo
     */
    shopList?: {
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ReqCreateLiveTradePrintTaskDTO :ReqCreateLiveTradePrintTaskDTO
 */
export interface TradeLivePrintCreateTaskRequest {
  /**
   * 直播类型,1=店铺直播，2=达人直播，3=扣号直播，为空默认=1
   */
  liveType?: number;
  /**
   * 用户ID
   */
  userId?: number;
  subUserId?: number;
  /**
   * 直播时长，单位：小时
   */
  liveTimeLength?: string;
  /**
   * 业务唯一值
   */
  uniqueBizCode?: string;
  /**
   * 退款状态查询0-不过滤1-过滤退款完成2-过滤退款中或退款完成
   */
  refundStatus?: number;
  /**
   * 标签查询 ,String
   */
  tradeLabelList?: string[];
  /**
   * 异常查询 ,String
   */
  tradeExceptionList?: string[];
  /**
   * 打印机
   */
  selectedPrinter?: string;
  /**
   * 打印标签模版
   */
  selectedZbdTemp?: number;
  /**
   * 请求版本，当前为v2
   */
  requestVersion?: string;
  /**
   * 虚拟订单配置 ,LiveTradePrintFakerTradeConfigDTO
   */
  fakerTradeConfig?: {
    /**
     * 间隔时间（秒）
     */
    intervalSec?: number;
    /**
     * 虚拟订单标识
     */
    fakerTradeFlag?: string;
    /**
     * 虚拟订单商品信息 ,ItemInfo
     */
    fakerTradeItemList?: {
      /**
       * 商品ID
       */
      itemId?: string;
      /**
       * 商品SKUID
       */
      skuId?: string;
      /**
       * 商品标题
       */
      itemTitle?: string;
      /**
       * 商家编码
       */
      outerId?: string;
      /**
       * 商品简称
       */
      sysItemAlias?: string;
      /**
       * 规格名称
       */
      skuName?: string;
      /**
       * 规格编码
       */
      skuOuterId?: string;
      /**
       * 规格别名
       */
      sysSkuAlias?: string;
      /**
       * 货品规格编码
       */
      sysOuterSkuId?: string;
      /**
       * 数量
       */
      num?: number;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  /**
   * 是否开启虚拟订单
   */
  isOpenFakerTrade?: boolean;
  /**
   * 开启黑名单标记
   */
  isOpenBlackList?: boolean;
  /**
   * 开启新客户标记
   */
  isOpenNewCustomer?: boolean;
  /**
   * 开启重复下单标记
   */
  isOpenRepeatTrade?: boolean;
  /**
   * 扣号匹配规则 ,LiveCommentRuleInfo
   */
  liveCommentRule?: {
    /**
     * 抢单类型1:普通商品2:尾货商品3:孤品
     */
    itemType?: number;
    /**
     * 多少秒内打印
     */
    timeRand?: number;
    /**
     * 打印前多少位扣号玩家
     */
    maxPrintUserCount?: number;
    /**
     * 弹幕类型1全部打印（数字、小数、符号）2纯数字3数字+关键词
     */
    numType?: number;
    /**
     * 关键词内容
     */
    keyword?: string;
    /**
     * 数字范围起始值
     */
    numRangeStart?: number;
    /**
     * 数字范围结束值
     */
    numRangeEnd?: number;
    /**
     * 状态1.有效2.无效
     */
    status?: number;
    [k: string]: any;
  };
  /**
   * 店铺信息集合 ,PlatformShopInfo
   */
  shopList?: {
    /**
     * 用户ID
     */
    userId?: number;
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 店铺ID
     */
    sellerId?: string;
    /**
     * 店铺名称
     */
    sellerNick?: string;
    [k: string]: any;
  }[];
  /**
   * 达人信息集合 ,AuthorInfo
   */
  authorList?: {
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 达人ID
     */
    authorId?: string;
    /**
     * 达人名称
     */
    authorName?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}



/**
 * ResponseBody<RspLiveTradePrintTaskDTO> :ResponseBody
 */
export interface TradeLivePrintCreateTaskResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspLiveTradePrintTaskDTO
   */
  data?: {
    /**
     * 是否有直播打印任务
     */
    hasLivePrintTask?: boolean;
    /**
     * 创建失败提示
     */
    createFailTip?: string;
    /**
     * 共计时长
     */
    liveTimeStr?: string;
    /**
     * id
     */
    id?: number;
    /**
     * 用户ID
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * @mbg.generated,直播场次,表字段:live_no
     */
    liveNo?: string;
    /**
     * @mbg.generated,直播类型，1=店铺直播，2=达人直播,表字段:live_type
     */
    liveType?: number;
    /**
     * 直播时长，单位：小时
     */
    liveTimeLength?: string;
    /**
     * 直播打印开始时间
     */
    livePrintTimeStart?: string;
    /**
     * 直播打印结束时间
     */
    livePrintTimeEnd?: string;
    /**
     * 直播打印实际结束时间
     */
    livePrintTimeActualEnd?: string;
    /**
     * 业务唯一值
     */
    uniqueBizCode?: string;
    /**
     * 标签已打印数量
     */
    alreadyPrintNum?: number;
    /**
     * 过滤订单数量
     */
    filterTradeNum?: number;
    /**
     * 队列中未打印数量
     */
    noPrintNum?: number;
    /**
     * 执行状态0等待执行，1执行中，2成功，3失败4关闭
     */
    taskStatus?: number;
    /**
     * 失败原因
     */
    errorMsg?: string;
    /**
     * 任务创建人
     */
    createTaskUserName?: string;
    /**
     * 任务创建人id
     */
    createTaskUserId?: string;
    /**
     * 任务创建节点IP
     */
    createTaskSourceHost?: string;
    /**
     * 关闭任务人
     */
    closeTaskUserName?: string;
    /**
     * 关闭任务人id
     */
    closeTaskUserId?: number;
    /**
     * 任务关闭节点IP
     */
    closeTaskHost?: string;
    /**
     * 创建时间
     */
    gmtCreated?: string;
    /**
     * 修改时间
     */
    gmtModified?: string;
    /**
     * 请求参数json，用户设置的过滤条件
     */
    requestJson?: string;
    /**
     * 店铺信息集合 ,PlatformShopInfo
     */
    shopList?: {
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      [k: string]: any;
    }[];
    /**
     * 达人信息集合 ,AuthorInfo
     */
    authorList?: {
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 达人ID
       */
      authorId?: string;
      /**
       * 达人名称
       */
      authorName?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}



/**
 * ReqCloseLiveTradePrintTaskDTO :ReqCloseLiveTradePrintTaskDTO
 */
export interface TradeLivePrintCloseTaskRequest {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  subUserId?: number;
  [k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLivePrintCloseTaskResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqLiveTradePrintRecordQueryDTO :ReqLiveTradePrintRecordQueryDTO
 */
export interface TradeLivePrintSelectLiveTradeRecordWithPageRequest {
  /**
   * 直播任务id集合 ,Long
   */
  liveTradePrintTaskIdList?: number[];
  /**
   * 订单编号 ,String
   */
  ptTidList?: string[];
  /**
   * 查询分组全部：不传已打印：1未打印-不符合：2未打印-异常：3
   */
  queryGroupBy?: number;
  userId?: number;
  pageNo?: number;
  pageSize?: number;
  [k: string]: any;
}

/**
 * ResponseBody<PageList<RspLiveTradePrintRecordPageDTO>> :ResponseBody
 */
export interface TradeLivePrintSelectLiveTradeRecordWithPageResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * PageList
   */
  data?: {
    pageNo?: number;
    pageSize?: number;
    total?: number;
    /**
     * T
     */
    list?: {
      /**
       * id
       */
      id?: number;
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      /**
       * 付款时间
       */
      payTime?: string;
      /**
       * 系统单号
       */
      tid?: string;
      /**
       * 订单编号
       */
      ptTid?: string;
      /**
       * 买家昵称
       */
      buyerNick?: string;
      /**
       * 买家唯一标识
       */
      buyerOpenUid?: string;
      receiverName?: string;
      /**
       * 收件人手机号,表字段:receiver_mobile
       */
      receiverMobile?: string;
      /**
       * 收件人省,表字段:receiver_state
       */
      receiverState?: string;
      /**
       * 收件人市,表字段:receiver_city
       */
      receiverCity?: string;
      /**
       * 收件人区,表字段:receiver_district
       */
      receiverDistrict?: string;
      /**
       * 收件人详细地址,表字段:receiver_address
       */
      receiverAddress?: string;
      /**
       * 商品信息 ,TradeItemInfo
       */
      tradeItemInfoList?: {
        /**
         * 商品图片
         */
        picUrl?: string;
        /**
         * 商品标题
         */
        itemTitle?: string;
        /**
         * 规格名称
         */
        skuName?: string;
        /**
         * 商品数量
         */
        num?: number;
        [k: string]: any;
      }[];
      /**
       * 总数量
       */
      totalNum?: number;
      /**
       * 订单实付金额
       */
      payment?: string;
      /**
       * 邮费,表字段:post_fee
       */
      postFee?: string;
      /**
       * 订单总金额
       */
      totalFee?: string;
      /**
       * 直播打印任务id
       */
      liveTradePrintTaskId?: number;
      /**
       * 直播打印状态0未打印，1已打印，2未打印-不符合，3未打印-异常
       */
      livePrintStatus?: number;
      /**
       * 未打印异常原因
       */
      errorMsg?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqLiveTradePrintRecordQueryDTO :ReqLiveTradePrintRecordQueryDTO
 */
export interface TradeLivePrintSelectLiveTradeRecordCountRequest {
  /**
   * 直播任务id集合 ,Long
   */
  liveTradePrintTaskIdList?: number[];
  /**
   * 订单编号 ,String
   */
  ptTidList?: string[];
  /**
   * 查询分组全部：不传已打印：1未打印-不符合：2未打印-异常：3
   */
  queryGroupBy?: number;
  userId?: number;
  pageNo?: number;
  pageSize?: number;
  [k: string]: any;
}

/**
 * ResponseBody<RspLiveTradePrintRecordCountDTO> :ResponseBody
 */
export interface TradeLivePrintSelectLiveTradeRecordCountResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspLiveTradePrintRecordCountDTO
   */
  data?: {
    /**
     * 全部统计
     */
    allCount?: number;
    /**
     * 未打印统计
     */
    noPrintCount?: number;
    /**
     * 已打印统计
     */
    alreadyPrintCount?: number;
    /**
     * 未打印-不符合统计
     */
    noPrintNoFitCount?: number;
    /**
     * 未打印-异常统计
     */
    noPrintExceptionCount?: number;
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqLiveTradeForPrintDTO :ReqLiveTradeForPrintDTO
 */
export interface TradeLivePrintLiveTradeRelateNumStatisticsRequest {
  /**
   * tid集合 ,String
   */
  tidList?: string[];
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  [k: string]: any;
}

/**
 * ResponseBody<RspLiveTradePrintStaticsDTO> :ResponseBody
 */
export interface TradeLivePrintLiveTradeRelateNumStatisticsResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspLiveTradePrintStaticsDTO
   */
  data?: {
    /**
     * 直播任务id
     */
    liveTradePrintTaskId?: number;
    /**
     * 未打印数量
     */
    noPrintCount?: number;
    /**
     * 标签已打印统计
     */
    alreadyPrintCount?: number;
    /**
     * 过滤订单数量
     */
    filterTradeNum?: number;
    /**
     * 打印开始时间
     */
    livePrintTimeStart?: string;
    /**
     * 直播打印结束时间
     */
    livePrintTimeEnd?: string;
    /**
     * 执行状态0等待执行，1执行中，2成功，3失败4关闭
     */
    taskStatus?: number;
    /**
     * 直播打印实际结束时间
     */
    livePrintTimeActualEnd?: string;
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * List<ReqLiveTradePrintUpdateDTO>
 */
export type TradeLivePrintUpdateLiveTradePrintStatusRequest = {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  /**
   * tid集合
   */
  tid?: string;
  /**
   * 直播打印状态0未打印，1已打印，2未打印-不符合，3未打印-异常
   */
  livePrintStatus?: number;
  /**
   * 异常信息
   */
  errMsg?: string;
  [k: string]: any;
}[];

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLivePrintUpdateLiveTradePrintStatusResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}
/**
 * ManualSplitMergeTradeRequestVo :ManualSplitMergeTradeRequestVo
 */
export interface TradeManualSplitMergeTradeRequest {
  /**
   * 订单号包含主订单 ,String
   */
  tidList?: string[];
  /**
   * 用户id(单号的使用者，也就是说订单的归属者)
   */
  sellerId?: string;
  /**
   * 平台类型
   */
  platform?: string;
  /**
   * 合单md5值
   */
  mergeMd5?: string;
  [k: string]: any;
}

/**
 * ResponseBody<TradeListVo> :ResponseBody
 */
export interface TradeManualSplitMergeTradeResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * TradeListVo
   */
  data?: {
    /**
     * ErpPackageInfoVo
     */
    list?: {
      buyerNick?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      openId?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      memberId?: string;
      /**
       * 买家昵称唯一标识
       */
      buyerOpenUid?: string;
      isCod?: boolean;
      printInfo?: string;
      platform?: string;
      createTime?: string;
      receiverAddress?: string;
      receiverCity?: string;
      receiverDistrict?: string;
      receiverMobile?: string;
      receiverName?: string;
      receiverPhone?: string;
      receiverState?: string;
      receiverTown?: string;
      userId?: string;
      sellerNick?: string;
      platformId?: string;
      togetherId?: string;
      shipListPrintStatus?: string;
      waybillPrintStatus?: string;
      mainTid?: string;
      orderCode?: string;
      isSuccess?: boolean;
      errorMsg?: string;
      /**
       * TradeVo
       */
      trades?: {
        /**
         * 手工单系统tid，主要用来申请单号
         */
        realTid?: string;
        /**
         * 手工单发货信息
         */
        shipInfo?: string;
        /**
         * 发件人
         */
        senderName?: string;
        /**
         * 发件人电话
         */
        senderPhone?: string;
        /**
         * 发件人手机
         */
        senderMobile?: string;
        /**
         * sender_province
         */
        senderProvince?: string;
        /**
         * sender_city
         */
        senderCity?: string;
        /**
         * sender_county
         */
        senderCounty?: string;
        /**
         * 发件人地址
         */
        senderAddress?: string;
        sellerId?: string;
        buyerMessage?: string;
        buyerNick?: string;
        /**
         * 买家昵称唯一标识
         */
        buyerOpenUid?: string;
        createTime?: string;
        invoiceName?: string;
        invoiceTitle?: string;
        invoiceKind?: string;
        invoiceType?: string;
        isChange?: boolean;
        isMendian?: boolean;
        isCod?: boolean;
        isPrintFhd?: string;
        isPrintKdd?: string;
        payTime?: string;
        /**
         * OrderVo
         */
        orders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          skuUuid?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          itemTitleSensitive?: boolean;
          itemLinkSensitive?: boolean;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          /**
           * String
           */
          ydNoSet?: string[];
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货品自定义属性 ,ItemCustomAttributesDto
           */
          customAttributesList?: {
            /**
             * 自定义属性key,itemCustomAttribute1，ItemCustomAttribute2，ItemCustomAttribute3...
             */
            key?: string;
            /**
             * 自定义属性name,example：产地，质地，等级...
             */
            name?: string;
            /**
             * 自定义属性value,example：中国，硬，A...
             */
            value?: string;
            [k: string]: any;
          }[];
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 商品简称
           */
          itemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 验货发货快递名称
           */
          inspectionExName?: string;
          /**
           * 验货发货快递单id
           */
          inspectionExId?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          /**
           * 是否线上发货
           */
          onlineShip?: boolean;
          /**
           * 商品所属店铺id
           */
          itemSellerId?: string;
          /**
           * 商品所属平台
           */
          itemPlatform?: string;
          /**
           * 商品结算金额
           */
          settleAmount?: string;
          /**
           * 运费
           */
          freightAmount?: string;
          /**
           * 总金额
           */
          settleAmountSum?: string;
          /**
           * 是否快照信息不一致
           */
          isSnapshotAbnormal?: boolean;
          /**
           * 快照信息不一致原因
           */
          snapshotAbnormalMsg?: string;
          /**
           * 货品颜色
           */
          sysColor?: string;
          /**
           * 货品尺寸
           */
          sysSize?: string;
          [k: string]: any;
        }[];
        /**
         * OrderVo
         */
        giftOrders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          skuUuid?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          itemTitleSensitive?: boolean;
          itemLinkSensitive?: boolean;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          /**
           * String
           */
          ydNoSet?: string[];
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货品自定义属性 ,ItemCustomAttributesDto
           */
          customAttributesList?: {
            /**
             * 自定义属性key,itemCustomAttribute1，ItemCustomAttribute2，ItemCustomAttribute3...
             */
            key?: string;
            /**
             * 自定义属性name,example：产地，质地，等级...
             */
            name?: string;
            /**
             * 自定义属性value,example：中国，硬，A...
             */
            value?: string;
            [k: string]: any;
          }[];
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 商品简称
           */
          itemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 验货发货快递名称
           */
          inspectionExName?: string;
          /**
           * 验货发货快递单id
           */
          inspectionExId?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          /**
           * 是否线上发货
           */
          onlineShip?: boolean;
          /**
           * 商品所属店铺id
           */
          itemSellerId?: string;
          /**
           * 商品所属平台
           */
          itemPlatform?: string;
          /**
           * 商品结算金额
           */
          settleAmount?: string;
          /**
           * 运费
           */
          freightAmount?: string;
          /**
           * 总金额
           */
          settleAmountSum?: string;
          /**
           * 是否快照信息不一致
           */
          isSnapshotAbnormal?: boolean;
          /**
           * 快照信息不一致原因
           */
          snapshotAbnormalMsg?: string;
          /**
           * 货品颜色
           */
          sysColor?: string;
          /**
           * 货品尺寸
           */
          sysSize?: string;
          [k: string]: any;
        }[];
        gifts?: string;
        /**
         * 实收金额
         */
        receivedPayment?: string;
        payment?: string;
        paymentSensitive?: boolean;
        discountAmount?: string;
        postFee?: string;
        receiverMobile?: string;
        receiverCity?: string;
        receiverDistrict?: string;
        receiverPhone?: string;
        receiverAddress?: string;
        receiverName?: string;
        receiverState?: string;
        receiverTown?: string;
        receiverZip?: string;
        sellerFlag?: string;
        userId?: string;
        sellerNick?: string;
        sellerNickSensitive?: boolean;
        platform?: string;
        sellerMemo?: string;
        ignoreType?: number;
        sellerMemoFlag?: string;
        sellerMemoFlagName?: string;
        tid?: string;
        ptTid?: string;
        totalFee?: string;
        status?: string;
        gift?: string;
        type?: string;
        sendRemindFlag?: number;
        sendRemindHour?: string;
        isAddCostSF?: boolean;
        /**
         * OrderPromiseDetailVo
         */
        orderPromiseDetailVo?: {
          orderPromiseKdCode?: string;
          orderPromiseKdName?: string;
          /**
           * 指定包材
           */
          orderPromiseBc?: string;
          orderPromiseDeliveryTime?: number;
          [k: string]: any;
        };
        freeSF?: number;
        /**
         * 商家优惠金额
         */
        sellerDiscount?: string;
        distributorTid?: string;
        /**
         * 平台折扣金额
         */
        platformDiscount?: string;
        /**
         * 宝贝数量：取订单中的商品总数量
         */
        totalNums?: number;
        /**
         * 宝贝种类
         */
        itemCateNum?: number;
        /**
         * 是否是预发货
         */
        isPreShip?: boolean;
        isPending?: boolean;
        isCancelSend?: boolean;
        isMatchCancelSend?: boolean;
        imel?: string;
        deviceSn?: string;
        overseaTracing?: string;
        expressName?: string;
        /**
         * 催发货标记0无催发货，1有催发货；
         */
        urge?: number;
        /**
         * 催发货时间
         */
        urgeShippingTime?: string;
        /**
         * 订单标签 ,String
         */
        serviceTagList?: string[];
        /**
         * 预发货任务id
         */
        preShipId?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单(该参数为map)
         */
        sellerFlagSys?: {
          /**
           * String
           */
          mapKey?: {
            [k: string]: any;
          };
          /**
           * Integer
           */
          mapValue?: {
            [k: string]: any;
          };
          [k: string]: any;
        };
        /**
         * 标识当前订单是否存在退款，不做存储
         */
        hasRefund?: boolean;
        refundStatus?: string;
        totalWeight?: string;
        /**
         * 快递单号 ,String
         */
        ydNoSet?: string[];
        /**
         * 多包裹运单数量
         */
        multiPackYdCount?: number;
        /**
         * 快递公司编号
         */
        logisticsId?: string;
        /**
         * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
         */
        stockOutHandleStatus?: number;
        /**
         * 是否缺货0-无缺货处理1：有缺货处理
         */
        isStockOut?: number;
        /**
         * 预售时间
         */
        preSaleTime?: string;
        /**
         * 是否为预售商品1表示是0表示否
         */
        isPreSale?: number;
        /**
         * 风控状态（值为1则为风控订单，值为0则为正常订单）
         */
        riskControlStatus?: string;
        receiverNameMask?: string;
        receiverPhoneMask?: string;
        receiverAddressMask?: string;
        receiverMobileMask?: string;
        idxEncodeReceiverMobile?: string;
        idxEncodeReceiverName?: string;
        idxEncodeReceiverAddress?: string;
        /**
         * 订单调fullInfo接口的时间
         */
        tidFullInfoTime?: string;
        /**
         * 天猫直送，值true或者false
         */
        tmallDelivery?: boolean;
        /**
         * 3PL有时效订单标，值true或者false
         */
        threePlTiming?: boolean;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        deliveryCps?: string;
        /**
         * 取
         */
        cpCode?: string;
        /**
         * cpCode转cpName
         */
        cpName?: string;
        /**
         * 是否催发货1：催发货0：非催发货
         */
        isUrgeDelivery?: number;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        caid?: string;
        /**
         * trade_fromWAP,JHS交易内部来源
         */
        tradeFrom?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
         */
        fenxiaos?: number;
        payHours?: number;
        receiverSecret?: string;
        mobileSecret?: string;
        timingPromise?: string;
        promiseService?: string;
        esDate?: string;
        esRange?: string;
        osDate?: string;
        osRange?: string;
        cutoffMinutes?: string;
        esTime?: string;
        deliveryTime?: string;
        collectTime?: string;
        sendTime?: string;
        signTime?: string;
        dispatchTime?: string;
        /**
         * value=logistics_upgrade为天猫物流升级订单
         */
        asdpBizType?: string;
        /**
         * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
         */
        asdpAds?: string;
        /**
         * 天猫送货上门快递名称
         */
        sendExName?: string;
        /**
         * 天猫送货上门快递编码
         */
        sendExCode?: string;
        /**
         * 全渠道商品通相关字段
         */
        omnichannelParam?: string;
        /**
         * 是否是海外购
         */
        isThreePl?: boolean;
        /**
         * 是否风险留言订单
         */
        pbly?: boolean;
        /**
         * 家装订单
         */
        tmserSpu?: boolean;
        /**
         * 淘宝后台getShippingType
         */
        shippingType?: string;
        /**
         * 淘宝后台getShippingType对应名称，后期
         */
        shippingName?: string;
        deliveryTypeDesc?: string;
        /**
         * 订单类型交易方式,一口价,送礼,赠品领取,货到付款,定金预售
         */
        tradeType?: string;
        /**
         * 支付类型
         */
        payType?: string;
        biz?: string;
        /**
         * 是否为乡镇订单
         */
        hasTown?: number;
        /**
         * 打印时间
         */
        printTime?: string;
        /**
         * 发货时间
         */
        shipTime?: string;
        /**
         * 系统发货时间
         */
        sysShipTime?: string;
        /**
         * 提示手动合单的判断标示
         */
        mergeBuyerNick?: string;
        noGoodsLink?: boolean;
        goodsWarn?: boolean;
        firstSend?: boolean;
        smartSelectExpress?: string;
        labelstatus?: number;
        /**
         * 0未打印1全部打印
         */
        labelPrintStatus?: number;
        promiseDeliveryTime?: boolean;
        /**
         * PromiseLogistics
         */
        promiseLogisticsList?: {
          company?: string;
          exCode?: string;
          [k: string]: any;
        }[];
        wrapperDescription?: boolean;
        duoduoWholesale?: boolean;
        shipHold?: boolean;
        jdStoreOrderType?: number;
        jdOrderShipType?: number;
        /**
         * 额外运单 ,ExtraDeliveryVO
         */
        extraDeliveryList?: {
          /**
           * 快递运单号
           */
          trackingNumber?: string;
          /**
           * 快递公司id
           */
          logisticsId?: number;
          /**
           * 快递公司名称
           */
          logisticsName?: string;
          [k: string]: any;
        }[];
        /**
         * true跨境订单false非跨境
         */
        crossBorder?: boolean;
        /**
         * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
         */
        consolidateType?: string;
        /**
         * 订单来源：HAND手工单
         */
        source?: string;
        changeAdderFlag?: number;
        /**
         * 拼团订单未完成true未完成false或者为空为正常订单
         */
        pOrderUnfinished?: boolean;
        /**
         * 订单类型
         */
        tradeTypeTagStr?: string;
        /**
         * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
         */
        openAddressId?: string;
        /**
         * 精选联盟
         */
        isSelectedAlliance?: boolean;
        /**
         * 小店自卖
         */
        isSmallStoreSelfSelling?: boolean;
        /**
         * 闪电购商品
         */
        isFlashBuyingProducts?: boolean;
        /**
         * 重新发货
         */
        isResend?: boolean;
        /**
         * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
         */
        receiverId?: string;
        /**
         * 回流订单平台类型
         */
        hlPlatformType?: string;
        /**
         * 回流订单是否加密
         */
        hlEncryptOrder?: boolean;
        /**
         * fxg承诺日达1表示是0表示否
         */
        appointmentArrival?: number;
        /**
         * 发货仓编号
         */
        outWarehouseId?: string;
        /**
         * 发货仓名称
         */
        warehouseName?: string;
        /**
         * 指定时间送达承诺时间送达
         */
        receiptDate?: string;
        /**
         * ali定制服务1表示是0表示否
         */
        aliEntryServiceInfo?: number;
        /**
         * 判断是否需要上传序列码
         */
        needSerialNumber?: boolean;
        /**
         * 线下备注
         */
        sysMemo?: string;
        /**
         * 达人标签订单
         */
        isAuthorOrder?: boolean;
        /**
         * 线下备注图片
         */
        sysMemoPic?: string;
        /**
         * 标签：加运费发顺丰
         */
        hasSfExpressService?: boolean;
        /**
         * 顺丰加价服务费
         */
        sfExpressFee?: string;
        /**
         * 送货上门
         */
        hasDeliveryOnDoor?: boolean;
        /**
         * 京东POP订单选项
         */
        jdDeliveryOnDoorOption?: number;
        /**
         * 是否线上发货
         */
        onlineShip?: boolean;
        /**
         * 是否拆单发货
         */
        isSplit?: boolean;
        /**
         * 直邮活动标记，拼多多平台
         */
        directMailActivity?: boolean;
        /**
         * 回流密闻订单加解密信息，一般用于脱敏信息打单取号等 ,DownstreamEncryptDetails
         */
        downstreamEncryptDetails?: {
          sellerNick?: string;
          sellerId?: string;
          tid?: string;
          oaid?: string;
          hlPlatformType?: string;
          platformExtraInfo?: string;
          platformAppKey?: string;
          receiverProvince?: string;
          receiverCity?: string;
          receiverArea?: string;
          receiverTown?: string;
          encryptReceiverMobile?: string;
          encryptReceiverAddress?: string;
          encryptReceiverName?: string;
          outPlatformSupplyOrderNo?: string;
          outPlatformSubCode?: string;
          outSupplierId?: string;
          [k: string]: any;
        };
        /**
         * 插旗标签
         */
        sellerFlagTag?: string;
        storageTime?: string;
        /**
         * 指定快递金额
         */
        promiseLogisticsAmount?: string;
        /**
         * 是否快照信息不一致
         */
        isSnapshotAbnormal?: boolean;
        /**
         * 快照信息不一致原因
         */
        snapshotAbnormalMsg?: string;
        /**
         * 代收金额
         */
        waitReceiveAmount?: string;
        /**
         * 仓库Id
         */
        storageId?: number;
        /**
         * 仓库名称
         */
        storageName?: string;
        /**
         * 仓库类型描述
         */
        storageTypeDesc?: string;
        /**
         * 仓库类型
         */
        storageType?: number;
        /**
         * 政府补贴金额
         */
        governmentDiscount?: number;
        /**
         * rds最后同步时间
         */
        rdsLastSyncTime?: string;
        /**
         * rds同步超时标记
         */
        rdsLastSyncTimeout?: boolean;
        modified?: string;
        /**
         * 拆分订单类型<br>,REGULAR_GIFT_ORDER("拆分后普通商品+赠品订单"),<br>,REGULAR_ORDER("拆分后仅普通订单"),<br>,GIFT("拆分后仅赠品订单"),<br>,DEFAULT("默认订单");<br>,对应枚举com.kuaidizs.general.rds.api.common.enums.TradeSplitTypeEnum
         */
        splitTradeType?: string;
        /**
         * 是否已解密,仅针对换货手工单场景下有效
         */
        hasDecrypt?: boolean;
        [k: string]: any;
      }[];
      sendRemindFlag?: number;
      sendRemindHour?: string;
      payTime?: string;
      isPending?: boolean;
      isAddCostSF?: boolean;
      freeSF?: number;
      expressName?: string;
      sellerId?: string;
      receiverNameMask?: string;
      receiverPhoneMask?: string;
      receiverAddressMask?: string;
      receiverMobileMask?: string;
      idxEncodeReceiverMobile?: string;
      idxEncodeReceiverName?: string;
      idxEncodeReceiverAddress?: string;
      encodeTid?: string;
      caid?: string;
      /**
       * 提示手动合单的判断标示
       */
      mergeBuyerNick?: string;
      /**
       * 多包裹运单数量
       */
      multiPackYdCount?: number;
      /**
       * 是否包含预发货订单
       */
      isPreShip?: boolean;
      /**
       * String
       */
      preShipIds?: string[];
      hasRefund?: boolean;
      receiverZip?: string;
      /**
       * (该参数为map)
       */
      sellerFlagSys?: {
        /**
         * String
         */
        mapKey?: {
          [k: string]: any;
        };
        /**
         * Integer
         */
        mapValue?: {
          [k: string]: any;
        };
        [k: string]: any;
      };
      /**
       * :
       */
      tids?: string[];
      /**
       * :
       */
      ptTids?: string[];
      /**
       * CaiNiaoIntelliExpress
       */
      smartExpress?: {
        exCode?: string;
        exName?: string;
        [k: string]: any;
      };
      type?: string;
      /**
       * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
       */
      fenxiaos?: number;
      /**
       * 是否合单
       */
      isMerge?: string;
      /**
       * 总实收金额
       */
      totalReceivedPayment?: string;
      /**
       * 总支付金额
       */
      totalPayment?: string;
      /**
       * 总商家优惠金额
       */
      totalSellerDiscount?: string;
      /**
       * 总平台折扣金额
       */
      totalPlatformDiscount?: string;
      /**
       * 总邮费
       */
      totalPostFee?: string;
      /**
       * 总重量
       */
      totalWeight?: string;
      /**
       * 已发货的快递单号
       */
      ydNo?: string;
      /**
       * 快递单号 ,String
       */
      ydNoSet?: string[];
      /**
       * 快递公司 ,String
       */
      logisticsCompanySet?: string[];
      /**
       * 快递单号 ,String
       */
      waybillNoSet?: string[];
      /**
       * 商品结算金额
       */
      itemSettleAmount?: string;
      /**
       * 运费
       */
      freightAmount?: string;
      /**
       * 总金额=商品结算金额+运费
       */
      orderSettleAmountSum?: string;
      /**
       * 验单发货订单号搜索先发货订单模板id
       */
      verifyOrderListShowId?: number;
      /**
       * 模版的扩展字段，里面包含了用户模版id
       */
      templateYdAttr?: string;
      /**
       * 订单标记（用于黑白订单标记）,0：正常订单（默认）,1：白订单（表明这笔订单是商家请人刷的订单）,2：黑名单（表明这笔订单是别人恶意刷的订单）
       */
      tidMark?: number;
      /**
       * OrderPromiseVo
       */
      orderPromiseVo?: {
        orderPromiseDeliveryTime?: number;
        isManyKd?: boolean;
        [k: string]: any;
      };
      /**
       * Object
       */
      orderTagList?: {
        [k: string]: any;
      }[];
      payHours?: number;
      goodsNum?: string;
      /**
       * 家装订单
       */
      tmserSpu?: boolean;
      /**
       * 异常地址
       */
      abnormalAddress?: boolean;
      /**
       * 智选快递的快递名称
       */
      ExpressName?: string;
      /**
       * 打印菜鸟面单时是否需要隐私服务
       */
      cnPrivacy?: boolean;
      receiverSecret?: string;
      mobileSecret?: string;
      /**
       * OpenAddresseeID（收件人ID）,基于收件人的信息(姓名、地址、手机号、电话）、订单创建时间、店铺、appkey加密生成的ID。,相同的收件人，在同一家店铺，在固定时间周期内（通常1个自然周）内创建的订单，OAID相同。
       */
      oaid?: string;
      /**
       * 天猫送货上门快递名称
       */
      sendExName?: string;
      /**
       * 天猫送货上门快递编码
       */
      sendExCode?: string;
      /**
       * value=logistics_upgrade为天猫物流升级订单
       */
      asdpBizType?: string;
      /**
       * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
       */
      asdpAds?: string;
      signTime?: string;
      deliveryTime?: string;
      /**
       * 是否为乡镇订单
       */
      hasTown?: number;
      /**
       * 催发货标记0无催发货，1有催发货；
       */
      urge?: number;
      /**
       * 催发货时间
       */
      urgeShippingTime?: string;
      changeAdderFlag?: number;
      noGoods?: boolean;
      noGoodsLink?: boolean;
      goodsWarn?: boolean;
      firstSend?: boolean;
      preSell?: boolean;
      /**
       * 疫情标记,0：非疫情地区,1：疫情中风险地区,2：疫情高风险地区
       */
      epidemicMark?: number;
      /**
       * 风控状态（值为1则为风控订单，值为0则为正常订单）
       */
      riskControlStatus?: string;
      /**
       * 智选快递
       */
      smartSelectExpress?: string;
      /**
       * 智选快递模板级别推荐结果、是否手动指定
       */
      smartExpressTemplate?: string;
      /**
       * 合单智选快递模板级别推荐结果、是否手动指定
       */
      packSmartExpressTemplate?: string;
      labelstatus?: number;
      /**
       * 0未打印1全部打印2部分打印
       */
      labelPrintStatus?: number;
      promiseDeliveryTime?: boolean;
      /**
       * PromiseLogistics
       */
      promiseLogisticsList?: {
        company?: string;
        exCode?: string;
        [k: string]: any;
      }[];
      wrapperDescription?: boolean;
      /**
       * 顺丰包邮
       */
      freeSf?: boolean;
      /**
       * true跨境订单false非跨境
       */
      crossBorder?: boolean;
      tradeType?: string;
      /**
       * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
       */
      consolidateType?: string;
      /**
       * 订单来源:其他平台订单,Hand手工订单
       */
      source?: string;
      /**
       * 拼团订单未完成true未完成false或者为空为正常订单
       */
      pOrderUnfinished?: boolean;
      /**
       * 分销商用户ID
       */
      distributorUserId?: number;
      /**
       * 分销商账号
       */
      distributorAccount?: string;
      /**
       * 分销商名称
       */
      distributorName?: string;
      /**
       * 分销商联系人
       */
      distributorLinkMan?: string;
      /**
       * 分销商联系电话
       */
      distributorMobile?: string;
      /**
       * 分销商备注
       */
      distributorRemark?: string;
      /**
       * 分销状态,0:申请中,1：合作中,2：拒绝,3：终止
       */
      distributorStatus?: number;
      /**
       * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
       */
      openAddressId?: string;
      /**
       * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
       */
      receiverId?: string;
      /**
       * 回流订单平台类型
       */
      hlPlatformType?: string;
      /**
       * 回流订单是否加密
       */
      hlEncryptOrder?: boolean;
      /**
       * fxg承诺日达1表示是0表示否
       */
      appointmentArrival?: number;
      /**
       * 发货仓编号
       */
      outWarehouseId?: string;
      /**
       * 发货仓名称
       */
      warehouseName?: string;
      /**
       * 指定时间送达承诺时间送达
       */
      receiptDate?: string;
      /**
       * ali定制服务1表示是0表示否
       */
      aliEntryServiceInfo?: number;
      /**
       * 送货上门
       */
      hasDeliveryOnDoor?: boolean;
      /**
       * 退款状态
       */
      refundStatus?: string;
      /**
       * 订单状态
       */
      status?: string;
      /**
       * 直邮活动标记，拼多多平台
       */
      directMailActivity?: boolean;
      /**
       * 是否线上发货
       */
      onlineShip?: boolean;
      /**
       * 是否拆单发货
       */
      isSplit?: boolean;
      /**
       * 订单标签 ,String
       */
      serviceTagList?: string[];
      /**
       * 分销订单来源
       */
      ptType?: string;
      /**
       * 指定快递金额
       */
      promiseLogisticsAmount?: string;
      /**
       * 代收金额
       */
      waitReceiveAmount?: string;
      /**
       * 是否显示按子订单拆分，即显示按子订单拆分，true:允许,false不允许
       */
      allowSubOrderSplit?: boolean;
      /**
       * 仓库Id
       */
      storageId?: number;
      /**
       * 仓库名称
       */
      storageName?: string;
      /**
       * 仓库类型描述
       */
      storageTypeDesc?: string;
      /**
       * 仓库类型
       */
      storageType?: number;
      /**
       * rds最后同步时间
       */
      rdsLastSyncTime?: string;
      /**
       * rds同步超时标记
       */
      rdsLastSyncTimeout?: boolean;
      /**
       * 黑名单
       */
      blackList?: boolean;
      [k: string]: any;
    }[];
    /**
     * 总订单数
     */
    totalCount?: number;
    /**
     * 合单后订单数
     */
    total?: number;
    buyerCount?: number;
    params?: string;
    /**
     * PromiseLogistics
     */
    promiseLogisticsList?: {
      company?: string;
      exCode?: string;
      [k: string]: any;
    }[];
    message?: string;
    /**
     * 控制返回用户是否需要查询前同步。下次生效
     */
    syncBeforeQuery?: boolean;
    syncBeforeDesc?: string;
    /**
     * 未找到的tid集合<br>,只有在{@codeTradeQueryDTO.getTid()!=null}的情况下，才会返回该值<br> ,String
     */
    missedTids?: string[];
    /**
     * String
     */
    missedYdNos?: string[];
    /**
     * 申请单号并发数,默认4个
     */
    waybillGetConcurrentCount?: number;
    /**
     * 未找到的买家昵称集合 ,String
     */
    missedBuyerNickList?: string[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * List<BatchGetBicOrderCodeDTO>
 */
export type TradeBatchGetBicOrderCodeRequest = {
  /**
   * 系统订单编号集合
   */
  tid?: string;
  /**
   * 店铺ID
   */
  sellerId?: number;
  /**
   * 平台类型
   */
  platform?: string;
  [k: string]: any;
}[];

/**
 * ResponseBody<BatchGetBicOrderCodeRespVO> :ResponseBody
 */
export interface TradeBatchGetBicOrderCodeResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BatchGetBicOrderCodeRespVO
   */
  data?: {
    /**
     * 获取订单码成功的订单编号集合 ,String
     */
    successTidSet?: string[];
    /**
     * 获取订单码成功的订单信息集合 ,OrderCodeVO
     */
    orderCodeVOList?: {
      /**
       * 订单编号
       */
      tid?: string;
      /**
       * bic订单码信息
       */
      orderCode?: string;
      [k: string]: any;
    }[];
    /**
     * 获取订单码失败的订单编号集合 ,String
     */
    failTidSet?: string[];
    /**
     * 错误详情集合 ,FailReasonVO
     */
    failReasonVOList?: {
      /**
       * 订单编号
       */
      tid?: string;
      /**
       * 错误信息
       */
      errorMsg?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * List<BatchSendDTO>
 */
export type TradeBatchBindBicOrderCodeRequest = {
  /**
   * 如果存在合单，那么数组中分别组装数据传入，类似如下格式,[,[{tid:1,exNumber:444555,exCode:YTO,其他所有字段}],,[{tid:2,exNumber:444556,exCode:YTO,其他所有字段}],,[{tid:3,exNumber:444556,exCode:YTO,其他所有字段},{tid:4,exNumber:444556,exCode:YTO,其他所有字段}],] ,BatchSendTradeDTO
   */
  subTrades?: {
    togetherId?: string;
    /**
     * 母单号，针对快运场景
     */
    pYdNo?: string;
    /**
     * 是否拆单发货，默认非拆单
     */
    split?: boolean;
    /**
     * 多包裹发货仅pddfxgtbsph支持
     */
    multiPack?: boolean;
    /**
     * 是否为货到付款订单，1标识是，0标识否，默认非货到付款订单
     */
    cod?: number;
    /**
     * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货；7重新发货；8买家自提(无需物流)；9卖家配送(无需物流),配送人员和联系方式需必填
     */
    sendType?: string;
    /**
     * 单号来源
     */
    waybillNoSource?: string;
    /**
     * 用户id(注册账号)
     */
    userId?: number;
    /**
     * 用户id(单号的使用者，也就是说订单的归属者)
     */
    sellerId?: number;
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 订单来源
     */
    source?: string;
    /**
     * 买家账号
     */
    buyerNick?: string;
    /**
     * 买家唯一标识
     */
    buyerOpenUid?: string;
    /**
     * 收件省
     */
    receiverProvince?: string;
    /**
     * 收件市
     */
    receiverCity?: string;
    /**
     * 收件区
     */
    receiverCounty?: string;
    /**
     * 收件地址
     */
    receiverAddress?: string;
    /**
     * 收件街道
     */
    receiverTown?: string;
    /**
     * 收件人
     */
    receiverName?: string;
    /**
     * 手机
     */
    mobile?: string;
    /**
     * 固定电话
     */
    tel?: string;
    /**
     * 快递id
     */
    exId?: number;
    /**
     * 快递code
     */
    exCode?: string;
    /**
     * 快递名
     */
    exName?: string;
    /**
     * 快递单号
     */
    exNumber?: string;
    /**
     * 多包裹发货的字段 ,String
     */
    exNumberList?: string[];
    /**
     * 商品数量
     */
    goodsNum?: number;
    /**
     * 重量
     */
    weight?: string;
    /**
     * 拆单子订单串。oid1,ords,oid3
     */
    oids?: string;
    ptOids?: string;
    /**
     * tid
     */
    tid?: string;
    /**
     * 平台订单编号
     */
    ptTid?: string;
    /**
     * 买家留言
     */
    buyerMsg?: string;
    /**
     * 卖家备注旗帜类型
     */
    sellerFlag?: number;
    /**
     * 本地标记类型(该参数为map)
     */
    sellerFlagSys?: {
      /**
       * String
       */
      mapKey?: {
        [k: string]: any;
      };
      /**
       * Integer
       */
      mapValue?: {
        [k: string]: any;
      };
      [k: string]: any;
    };
    /**
     * 卖家备注
     */
    sellerMemo?: string;
    /**
     * 发货内容
     */
    info?: string;
    /**
     * 打印序号
     */
    printNum?: string;
    /**
     * 包裹号
     */
    packageId?: number;
    /**
     * 快递单类型1普通面单,2网点,3云栈电子面单
     */
    kddType?: number;
    /**
     * 是哪个页面的操作0：单打1：批打2：预发货3：自动预发货4：手工订单5厂家代发
     */
    optionType?: number;
    /**
     * 订单实付金额
     */
    payment?: string;
    /**
     * BatchSendOrderDTO
     */
    orders?: {
      /**
       * 商品数量
       */
      num?: number;
      /**
       * numIid
       */
      numIid?: string;
      skuUuid?: string;
      skuId?: string;
      tid?: string;
      ptOid?: string;
      oid?: string;
      /**
       * 子订单实付金额
       */
      payment?: string;
      systemNumIid?: number;
      systemSkuId?: number;
      /**
       * 是否忽略，0否，1是
       */
      ignore?: number;
      /**
       * 是否赠品
       */
      gift?: boolean;
      /**
       * 换商品逻辑0:默认；1:置换商品；2置换货品；3置换数量；4新增的货品；5新增的商品;6平台换商品
       */
      sysExchangeType?: number;
      /**
       * 是否需要上传商品识别码
       */
      needSerialNumber?: boolean;
      /**
       * 商品识别码（SN码、IMEI号、ICCID码)
       */
      productIdCode?: string;
      /**
       * 是否是
       */
      isPartConsign?: boolean;
      /**
       * 是否跳过平台发货
       */
      isSkipPlatformSend?: boolean;
      /**
       * 快手小店，发货时区分回传SN和IMEI使用,发货时，无值不传码，有值需传码，有多个值则传多个类型的码，回传平台时需要根据类型回传至不同字段,[1:需要SN],[2:需要IMEI]
       */
      serialType?: string;
      /**
       * 平台商品数量,不是换货商品&&不是改数量商品可能为空，为空取订单的数量num
       */
      platformNum?: number;
      [k: string]: any;
    }[];
    /**
     * overseaTracing（海淘溯源码id）内容。
     */
    overseaTracing?: string;
    /**
     * PDD支持imei（手机串号
     */
    imei?: string;
    /**
     * PDD支持deviceSn（设备序列号）
     */
    deviceSn?: string;
    /**
     * 手机号索引串
     */
    idxEncodeReceiverMobile?: string;
    /**
     * 加密详情索引串
     */
    idxEncodeReceiverAddress?: string;
    /**
     * 加密收件人索引串,,@return
     */
    idxEncodeReceiverName?: string;
    /**
     * 收件人姓名脱敏
     */
    receiverNameMask?: string;
    /**
     * 收件人电话脱敏
     */
    receiverPhoneMask?: string;
    /**
     * 收件人地址脱敏
     */
    receiverAddressMask?: string;
    /**
     * 收件人手机号脱敏
     */
    receiverMobileMask?: string;
    /**
     * 加密字段，不同字段归属平台字段可能不一致，比如淘宝为oaid
     */
    caid?: string;
    /**
     * 加密串tid
     */
    encodeTid?: string;
    /**
     * 订单地址是否解密
     */
    isDecrypted?: number;
    /**
     * 距离最晚发货时间时长
     */
    sendRemindHour?: string;
    /**
     * 订单状态
     */
    status?: string;
    /**
     * 退款状态,1-有退款NOT_REFUND,0-无退款HAS_REFUND
     */
    refundStatus?: string;
    /**
     * crossBorder拼多多跨境订单
     */
    tradeType?: string;
    /**
     * 是否是空包裹订单,1:自动标记，2：不是，3：手动标记，4：手动取消,默认不是
     */
    ignoreType?: number;
    /**
     * 如果是供应商订单，需要传入分销商id
     */
    saleUserId?: number;
    /**
     * 合单销售属性
     */
    packTotalType?: string;
    /**
     * 是否需要上传商品识别码
     */
    needSerialNumber?: boolean;
    /**
     * 发货&拒绝退款
     */
    isRefundReject?: boolean;
    /**
     * 发件人手机号
     */
    sendTel?: string;
    /**
     * 拆分订单，按数量拆分时，未发货的订单，追加发货快递单号，如果平台支持追加快递单号，根据此参数判断即可
     */
    isAppendSendYdNo?: boolean;
    /**
     * 地址库ID.
     */
    refundAddressId?: string;
    /**
     * 是否为整单发货
     */
    fullOrderSend?: boolean;
    /**
     * bic订单码
     */
    orderCode?: string;
    [k: string]: any;
  }[];
  /**
   * 发货来源,PD("批量打印发货"),,YFH_REAL_SEND("预发货真正发货"),,LABEL(2"小标签扫描发货"),,SCAN_TRADE_SEND(3,"扫码订单发货"),,@linkTradeSendSourceEnum
   */
  sendFrom?: {
    [k: string]: any;
  };
  /**
   * 页面操作类型，默认为发货，参数OperateLogTypeEnum枚举
   */
  optionType?: number;
  [k: string]: any;
}[];


/**
 * ResponseBody<BatchBindBicOrderCodeRespVO> :ResponseBody
 */
export interface TradeBatchBindBicOrderCodeResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BatchBindBicOrderCodeRespVO
   */
  data?: {
    /**
     * 发货方式
     */
    sendType?: string;
    /**
     * 快递名
     */
    exName?: string;
    /**
     * 快递单号
     */
    exNumber?: string;
    /**
     * 获取订单码成功的订单编号集合 ,String
     */
    successTidSet?: string[];
    /**
     * 获取订单码失败的订单编号集合 ,String
     */
    failTidSet?: string[];
    /**
     * 错误详情集合 ,FailReasonVO
     */
    failReasonVOList?: {
      /**
       * 平台订单编号
       */
      ptTid?: string;
      /**
       * 订单编号
       */
      tid?: string;
      /**
       * 错误信息
       */
      errorMsg?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
* TradeQueryDTO :TradeQueryDTO
 */
export interface TradeQueryShopTradeCountRequest {
  /**
   * TB,淘宝,,TM,天猫,,PDD,拼多多,,FXG,抖音,,HAND,手工单;
   */
  platform?: string;
  /**
   * 店铺id
   */
  sellerId?: string;
  startTime?: string;
  endTime?: string;
  /**
   * 筛选时间类型1下单时间2付款时间3打印时间4发货时间
   */
  timeType?: number;
  /**
   * 订单状态ORDER_WAIT_PAY等待付款ORDER_PAID已付款ORDER_SHIPPED已发货,ORDER_SHIPPED_LITTLE部分发货ORDER_COMPLETED交易完成ALL_STATUS全部订单
   */
  status?: string;
  /**
   * 打印状态2仅快递单已打印3仅发货单已打印4快递单发货单均已打印0快递单发货单均未打印,商品标签未打印8商品标签已打印9均未打印10均已打印11
   */
  printStatus?: number;
  /**
   * 发货单打印标记0：未打印，1：已打印，null：所有状态
   */
  fhdPrintStatus?: number;
  /**
   * 商品标签打印标记0：未打印，1：已打印，null：所有状态
   */
  labelPrintStatus?: number;
  /**
   * 自定义的地址关键字
   */
  selValue?: string;
  /**
   * 精准匹配省市区如：[{"name":"辽宁省","childList":[]},{"name":"广西壮族自治区","childList":[]}]
   */
  areaJson?: string;
  /**
   * 省市区是否包含：0不包含1包含
   */
  areaContain?: number;
  /**
   * 1有买家留言2有卖家备注3有买家留言+有卖家备注4没有留言且没有备注5红6黄7绿8蓝色9紫色10有留言或备注11无留言12无备注13无留言或无备注0灰旗，橙色-14、浅蓝-15、浅粉-16、深绿-17、桃红-18
   */
  flagValue?: string;
  /**
   * 留言内容(多个用,隔开)
   */
  buyerMessage?: string;
  /**
   * 留言备注1有买家留言2有卖家备注3有买家留言+有卖家备注4没有留言且没有备注10有留言或备注11无留言12无备注13无留言或无备注
   */
  commentsRemarks?: string;
  /**
   * 留言备注内容(多个用,隔开)
   */
  sellerMemo?: string;
  /**
   * 留言内容或者留言备注内容(多个用,隔开)
   */
  buyerMessageOrSellerMemo?: string;
  /**
   * 旗帜5红6黄7绿8蓝色9紫色0灰,橙色-14、浅蓝-15、浅粉-16、深绿-17、桃红-18
   */
  sellerFlag?: string;
  /**
   * 1,2_1表示包含红黄旗从自定义备注接口找0表示不包含1表示包含
   */
  flagSelValue?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品id
   */
  skuId?: string;
  /**
   * 货品id
   */
  sysItemId?: string;
  /**
   * 货品规格id
   */
  sysSkuId?: string;
  /**
   * 货品规格编码 ,String
   */
  sysOuterSkuId?: string[];
  /**
   * 常用标签包含及不包含,ItemLabelQueryEnum[LABEL_QUERY_MERGE,LABEL_QUERY_PENDING,LABEL_QUERY_IS_FIRST_SEND,LABEL_QUERY_FARE,LABEL_QUERY_PRESALE,LABEL_QUERY_TOWN,LABEL_QUERY_HAVE_GOOD_LABEL,LABEL_QUERY_URGE,LABEL_QUERY_PT_CHANGE_ITEM,LABEL_QUERY_EMPTY_BAG,LABEL_QUERY_PART_SHIP,LABEL_QUERY_SYS_CHANGE_ITEM,LABEL_QUERY_PRESENT,LABEL_QUERY_DELAY,LABEL_QUERY_MODIFY_ADDRESS,LABEL_QUERY_COD,LABEL_QUERY_SELECTED_ALLIANCE,LABEL_QUERY_SMALL_STORE_SELF_SELLING,LABEL_QUERY_FLASH_BUYING_PRODUCTS,LABEL_QUERY_MULTI_PACK,LABEL_QUERY_RESEND,LABEL_QUERY_FULL_ORDER_SHIP,LABEL_QUERY_NO_MERGE,LABEL_QUERY_NO_PENDING,LABEL_QUERY_NO_PRE_SALE,LABEL_QUERY_NO_FARE,LABEL_QUERY_NO_TOWN,LABEL_QUERY_NO_GOOD_LABEL,LABEL_QUERY_NO_URGE,LABEL_QUERY_NO_FIRST_SEND,LABEL_QUERY_NO_PT_CHANGE_ITEM,LABEL_QUERY_NO_EMPTY_BAG,LABEL_QUERY_NO_PART_SHIP,LABEL_QUERY_NO_SYS_CHANGE_ITEM,LABEL_QUERY_NO_PRESENT,LABEL_QUERY_NO_DELAY,LABEL_QUERY_NO_MODIFY_ADDRESS,LABEL_QUERY_NO_COD,LABEL_QUERY_NO_SELECTED_ALLIANCE,LABEL_QUERY_NO_SMALL_STORE_SELF_SELLING,LABEL_QUERY_NO_FLASH_BUYING_PRODUCTS,LABEL_QUERY_NO_MULTI_PACK,LABEL_QUERY_NO_RESEND,LABEL_QUERY_NO_FULL_ORDER_SHIP,LABEL_QUERY_DISIGNATED_EXPRESS,LABEL_QUERY_PRIORITY_DELIVERY,LABEL_QUERY_DISIGNATED_PACK_MATERIAL,LABEL_QUERY_DUODUO_WHOLE_SALE,LABEL_QUERY_SUSPEND_DELIVERY,LABEL_QUERY_SF_EXPRESS_FEE,LABEL_QUERY_JD_JC,LABEL_QUERY_JD_YC,LABEL_QUERY_JD_JP,LABEL_QUERY_NO_JD_JC_YC,LABEL_QUERY_JYXJ,LABEL_QUERY_JYXG,LABEL_QUERY_ZZSZ,LABEL_QUERY_ZZGS,LABEL_QUERY_ZZNMG,LABEL_QUERY_ZZNX,LABEL_QUERY_ZZQH,LABEL_QUERY_FXG_LOGISTICS_TRANSIT,LABEL_QUERY_FXG_REMOTE_DERICT,LABEL_QUERY_HL_TX,LABEL_QUERY_HL_PDD,LABEL_QUERY_HL_FXG,LABEL_QUERY_HL_JD,LABEL_QUERY_HL_KSXD,LABEL_QUERY_HL_XHS,LABEL_QUERY_HL_SPH,LABEL_QUERY_HL_OTHER,LABEL_QUERY_HL_PLAINTEXT,LABEL_QUERY_HL_CIPHERTEXT,LABEL_QUERY_FXG_APPOINTMENT_ARRIVAL,LABEL_QUERY_ALI_ENTRY_SERVICE_INFO,LABEL_QUERY_NEED_SERIAL_NUMBER,LABEL_QUERY_AUTHOR_ORDER,LABEL_QUERY_HAS_SF_EXPRESS_SERVICE,LABEL_QUERY_DELIVERY_ON_DOOR,LABEL_QUERY_ONLINE_SHIP,LABEL_QUERY_SPLIT_ORDER,LABEL_QUERY_BIC_ORDER,LABEL_QUERY_SUPER_LUCKY_BAG,LABEL_QUERY_REPLENISH_SEND_ORDER,LABEL_QUERY_NO_DISIGNATED_EXPRESS,LABEL_QUERY_NO_PRIORITY_DELIVERY,LABEL_QUERY_NO_DISIGNATED_PACK_MATERIAL,LABEL_QUERY_NO_DUODUO_WHOLE_SALE,LABEL_QUERY_NO_SUSPEND_DELIVERY,LABEL_QUERY_NO_SF_EXPRESS_FEE,LABEL_QUERY_NO_JD_JC,LABEL_QUERY_NO_JD_YC,LABEL_QUERY_NO_JD_JP,LABEL_QUERY_JD_JC_YC,LABEL_QUERY_NO_JYXJ,LABEL_QUERY_NO_JYXG,LABEL_QUERY_NO_ZZSZ,LABEL_QUERY_NO_ZZGS,LABEL_QUERY_NO_ZZNMG,LABEL_QUERY_NO_ZZNX,LABEL_QUERY_NO_ZZQH,LABEL_QUERY_NO_FXG_LOGISTICS_TRANSIT,LABEL_QUERY_NO_FXG_REMOTE_DERICT,LABEL_QUERY_NO_HL_TX,LABEL_QUERY_NO_HL_PDD,LABEL_QUERY_NO_HL_FXG,LABEL_QUERY_NO_HL_JD,LABEL_QUERY_NO_HL_KSXD,LABEL_QUERY_NO_HL_XHS,LABEL_QUERY_NO_HL_SPH,LABEL_QUERY_NO_HL_OTHER,LABEL_QUERY_NO_HL_PLAINTEXT,LABEL_QUERY_NO_HL_CIPHERTEXT,LABEL_QUERY_NO_FXG_APPOINTMENT_ARRIVAL,LABEL_QUERY_NO_ALI_ENTRY_SERVICE_INFO,LABEL_QUERY_NO_NEED_SERIAL_NUMBER,LABEL_QUERY_NO_AUTHOR_ORDER,LABEL_QUERY_NO_HAS_SF_EXPRESS_SERVICE,LABEL_QUERY_NO_DELIVERY_ON_DOOR,LABEL_QUERY_NO_ONLINE_SHIP,LABEL_QUERY_NO_SPLIT_ORDER,LABEL_QUERY_UN_BINDING_ITEM,LABEL_QUERY_RISK,LABEL_QUERY_ABNORMAL_ADDRESS,LABEL_QUERY_PORDER_FINISHED,LABEL_QUERY_PLATFORM_STOCKOUT,LABEL_QUERY_NO_UN_BINDING_ITEM,LABEL_QUERY_NO_RISK,LABEL_QUERY_NO_ABNORMAL_ADDRESS,LABEL_QUERY_NO_PORDER_FINISHED,LABEL_QUERY_NO_PLATFORM_STOCKOUT,LABEL_QUERY_NO_BIC_ORDER,LABEL_QUERY_NO_SUPER_LUCKY_BAG,LABEL_QUERY_NO_REPLENISH_SEND_ORDER,LABEL_QUERY_COMMON,key,message] ,String
   */
  tradeLabelList?: string[];
  /**
   * 标签包含查询（新） ,String
   */
  includeServiceTagList?: string[];
  /**
   * 标签不包含查询（新） ,String
   */
  excludeServiceTagList?: string[];
  /**
   * 排除异常,unBindingItem ,String
   */
  tradeExceptionList?: string[];
  /**
   * 快捷查询,合并订单merge;,非合并订单noMerge,有运费订单fare;,无运费订单noFare;,乡镇订单town,非乡镇订单noTown,有发票invoice,无发票noInvoice,有挂起pending,无挂起noPending,疫情地区epidemicArea,非疫情地区noEpidemicArea,已生成商品标签haveGoodLabel,未生成商品标签noGoodLabel,京东云仓jdyc,京东京仓jdjc,京东京配jdjp,是否快捷查询preSell,预售订单preSale,非预售订单noPreSale
   */
  quickQuery?: string;
  /**
   * {@codetradeQuery.getSellAttribute},查询销售属性,1:单商品单规格单件,,2:单商品单规格多件,,3:单商品多规格多件,,4:多商品多规格多件
   */
  sellAttribute?: string;
  /**
   * 查询销售属性(多选),1:单商品单规格单件,,2:单商品单规格多件,,3:单商品多规格多件,,4:多商品多规格多件 ,String
   */
  sellAttributeList?: string[];
  /**
   * 商品名称编码简称id
   */
  shortNameIncluding?: string;
  /**
   * 规格名称规格别名查询
   */
  skuIncluding?: string;
  /**
   * 商品名称编码简称id查询逻辑集合,新版查询，逗号已经不用了 ,String
   */
  shortNameIncludingList?: string[];
  /**
   * 规格名称规格别名查询集合,新版查询，逗号已经不用了 ,String
   */
  skuIncludingList?: string[];
  /**
   * 商品名称编码简称id(爆款搜索专用)
   */
  shortNameIncludingForTopItem?: string;
  /**
   * 规格名称规格别名查询即这个查询字段有以上以上字段有一个匹配即可(爆款搜索专用)
   */
  skuIncludingForTopItem?: string;
  /**
   * 订单标记已标记hasMark为标记noMark标记查询存储的值
   */
  bizMark?: string;
  /**
   * 订单标记0不包含1包含
   */
  bizMarkContain?: number;
  /**
   * 买家昵称,隔开
   */
  buyerNick?: string;
  /**
   * 收件人,隔开
   */
  receiveName?: string;
  /**
   * 手机号,隔开
   */
  mobile?: string;
  /**
   * 订单编号,隔开
   */
  tid?: string;
  /**
   * 平台订单编号,隔开
   */
  ptTid?: string;
  /**
   * 供分销推送订单订单编号
   */
  distributorTid?: string;
  /**
   * 运单号,隔开
   */
  sid?: string;
  /**
   * 0快递1物流
   */
  logisticsType?: string;
  /**
   * HAS_REFUND("退款成功"),,NOT_REFUND("无售后或售后关闭");
   */
  refundStatus?: string;
  /**
   * 订单总金额1-100
   */
  payment?: string;
  /**
   * 宝贝包含或不包含标识：,1包含,0不包含
   */
  equalFlag?: number;
  /**
   * 订单重量范围5-10
   */
  weightRange?: string;
  /**
   * 订单总数量子订单数量5-10
   */
  orderRange?: string;
  /**
   * 商品总数量1-50
   */
  goodsTotalNum?: string;
  /**
   * 商品种类1-50
   */
  goodsTypeNum?: string;
  /**
   * 是否精确精准就是false非精准就是true
   */
  isPrecise?: boolean;
  /**
   * 是否精确到订单精准就是false非精准就是true
   */
  isPreciseByTrade?: boolean;
  /**
   * 给mock用,此值传1会有订单
   */
  testStatus?: number;
  pageNo?: number;
  pageSize?: number;
  /**
   * 总订单数
   */
  totalCount?: number;
  /**
   * 1:先付款的在前边,,2后付款的在前边,,3先下单的在前边,,4后下单的在前边,,5.将相同商品挨在一起排列,,6实际支付金额大的在前边，,7实际支付金额小的在前边,,8.数量大的在前边,9.数量小的在前边,10.按订单发货时间，先超时的在前边,11.按订单发货时间，先超时的在后边,12订单修改时间升序，先修改的在前边,13订单修改时间降序，后修改的在前边,14省份首拼字母靠前的在前边,16按商家编码，升序
   */
  tidOrderType?: number;
  /**
   * MultiShopDTO
   */
  multiShopS?: {
    sellerId?: number;
    platform?: string;
    [k: string]: any;
  }[];
  /**
   * 平台查询，未选择
   */
  isPlatformEmptyQuery?: boolean;
  /**
   * 异常订单0异常1无异常
   */
  abnormalStatus?: number;
  /**
   * 短信验证码
   */
  verifyCode?: string;
  /**
   * 剩余发货时间搜索,ALL("全部"),ALREADY_DELAY("已超时"),LESS_THAN_SIX_HOURS("剩余时间小于6小时"),LESS_THAN_TWELVE_HOURS("剩余时间小于12小时"),LESS_THAN_TWENTY_FOUR_HOURS("剩余时间小于24小时"),GREATER_THAN_TWENTY_FOUR_HOURS("剩余时间大于24小时")
   */
  residueSendTimeSearch?: {
    [k: string]: any;
  };
  /**
   * 自定义剩余发货时间,范围0-999（例："12-999"；"0-12"）
   */
  customizeResidueSendTime?: string;
  /**
   * 智选快递（废弃）
   */
  smartSelectExpress?: string;
  /**
   * 智选快递模板维度id
   */
  smartExpressTemplateId?: string;
  serviePromiseType?: number;
  /**
   * 导入快递单号生成版本号
   */
  tradeExpressImportLogSequence?: string;
  /**
   * 相同昵称挨在一起true:是false：否
   */
  sortBySameNickname?: boolean;
  /**
   * true货品包含,false货品不包含
   */
  goodsContain?: boolean;
  /**
   * 货品简称编码
   */
  goodsAliasOrIdStr?: string;
  /**
   * 货品规格名称编码别名
   */
  goodsSkuNameOrIdOrAliasStr?: string;
  /**
   * 为了去除逗号，采用数组 ,String
   */
  goodsAliasOrIdStrList?: string[];
  /**
   * 为了去除逗号，采用数组 ,String
   */
  goodsSkuNameOrIdOrAliasStrList?: string[];
  /**
   * 是否先发货
   */
  isFirstSend?: boolean;
  /**
   * 商品库存状态,ALL全部(默认);HAS_STOCK有货;STOCK_WARN警戒;NO_STOCK无货;PART_STOCK部分有货;,,OutOfStockStatusEnum[ALL,HAS_STOCK,STOCK_WARN,NO_STOCK,PART_STOCK,val,desc]
   */
  goodStockStatus?: {
    [k: string]: any;
  };
  /**
   * 隐藏挂起订单
   */
  hidePending?: boolean;
  /**
   * 隐藏已发货的商品订单
   */
  hideAllRefunded?: boolean;
  /**
   * 导出方式，0：导出当前查询结果,1：导出勾选订单（勾选订单记录在tid字段中）
   */
  method?: number;
  /**
   * 纬度，0：订单纬度,1：商品纬度,表字段:latitude
   */
  latitude?: number;
  /**
   * 抖店达人name或者id
   */
  authorInfo?: string;
  /**
   * 跨界订单true为跨界订单
   */
  crossBorder?: boolean;
  /**
   * 异常订单unBindingItem商品未绑定
   */
  exceptionFlag?: string;
  /**
   * 订单来源:platfromOrder平台订单,handOrder手工订单
   */
  orderSource?: string;
  /**
   * 订单类型,详见TradeTypeEnum ,String
   */
  tradeTypeTagList?: string[];
  /**
   * 经销商用户id集合 ,Long
   */
  distributorUserIds?: number[];
  /**
   * String
   */
  marketIncludingList?: string[];
  /**
   * String
   */
  dangKouIncludingList?: string[];
  /**
   * String
   */
  supplierIncludingList?: string[];
  /**
   * String
   */
  authorIncludingList?: string[];
  includeIngColorOrSize?: boolean;
  /**
   * String
   */
  colorIncludingList?: string[];
  /**
   * String
   */
  sizeIncludingList?: string[];
  /**
   * 商品查询,"itemInfoQueryMap":{,"true":[,{,"itemQueryEnum":"ITEM_ID",,"values":[,"商品ID列表",],},,{,"itemQueryEnum":"ITEM_TITLE",,"values":[,"商品名称列表",],},,{,"itemQueryEnum":"ITEM_ALIAS",,"values":[,"简称列表",],},,{,"itemQueryEnum":"OUTER_ID",,"values":[,"商品编码列表",],},,{,"itemQueryEnum":"SYS_SKU_NAME",,"values":[,"规格名称列表",],},,{,"itemQueryEnum":"SYS_SKU_ALIAS",,"values":[,"规格别名列表",],},,{,"itemQueryEnum":"OUTER_SKU_ID",,"values":[,"规格编码列表",],},,{,"itemQueryEnum":"SYS_OUTER_SKU_ID",,"values":[,"货品规格编码列表",],},],}(该参数为map)
   */
  itemInfoQueryMap?: {
    /**
     * Boolean
     */
    mapKey?: {
      [k: string]: any;
    };
    /**
     * List<ItemInfoQueryDTO>
     */
    mapValue?: {
      [k: string]: any;
    };
    [k: string]: any;
  };
  /**
   * 验货查询
   */
  inspectionQuery?: boolean;
  /**
   * 订单号、底单记录map(该参数为map)
   */
  tidMallApplyLogMap?: {
    /**
     * String
     */
    mapKey?: {
      [k: string]: any;
    };
    /**
     * List<MallDzmdApplyLogQueryDTO>
     */
    mapValue?: {
      [k: string]: any;
    };
    [k: string]: any;
  };
  /**
   * 验货发货根据运单号查子单(该参数为map)
   */
  tidOidListMap?: {
    /**
     * String
     */
    mapKey?: {
      [k: string]: any;
    };
    /**
     * List<String>
     */
    mapValue?: {
      [k: string]: any;
    };
    [k: string]: any;
  };
  /**
   * 查询来源,默认：批打,1：分销订单明细
   */
  querySource?: number;
  /**
   * 查询结果是否需要运费等金额数据.,0or默认：不需要,1:需要
   */
  queryFreightResultFlag?: number;
  /**
   * 线下备注查询方式0:线下备注,过滤了线下备注的条件,查询所有数据1:有线下备注场景2:无线下备注场景
   */
  sysMemoMode?: number;
  /**
   * 线下备注的内容,支持多个查询,仅sysMemoMode为1时有内容 ,String
   */
  sysMemoList?: string[];
  /**
   * 货品分类id
   */
  classifyId?: number;
  /**
   * 货品分类id列表 ,Long
   */
  classifyIdList?: number[];
  /**
   * 颜色和尺码是否精确精准就是false非精准就是true
   */
  isColorAndSizePrecise?: boolean;
  /**
   * 预设条件不满足日期条件时，不返回数据,true：不返回数据
   */
  isPresetNotReturnData?: boolean;
  /**
   * 仓库ID ,Long
   */
  storageIdList?: number[];
  /**
   * 异常预警订单查询;0null-默认不查询；1-已申请单号未打印；2-已打印未发货
   */
  orderWarningQuery?: number;
  [k: string]: any;
}

/**
 * ResponseBody<List<RdsShopTradeCountDTO>> :ResponseBody
 */
export interface TradeQueryShopTradeCountResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RdsShopTradeCountDTO
   */
  data?: {
    platform?: string;
    sellerId?: number;
    sellerNick?: string;
    tradeNum?: number;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ManualMergeSplitTradeRequestDTO :ManualMergeSplitTradeRequestDTO
 */
export interface TradeOrderSplitManualMergeTradeRequest {
  /**
   * 合并的分组订单列表，按店铺+平台维度分组 ,GroupManualMergeSplitTradeDTO
   */
  groupMergeList?: {
    /**
     * 店铺Id
     */
    sellerId?: number;
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 订单来源
     */
    source?: string;
    /**
     * 待合并的分组订单列表 ,String
     */
    groupTidList?: string[];
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ResponseBody<OrderSplitMergeVO> :ResponseBody
 */
export interface TradeOrderSplitManualMergeTradeResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * OrderSplitMergeVO
   */
  data?: {
    /**
     * 订单查询对象 ,ErpPackageInfoVo
     */
    packageInfoVos?: {
      buyerNick?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      openId?: string;
      /**
       * 平台级别店铺唯一标识，与PlatformShopDto中数据一致
       */
      memberId?: string;
      /**
       * 买家昵称唯一标识
       */
      buyerOpenUid?: string;
      isCod?: boolean;
      printInfo?: string;
      platform?: string;
      createTime?: string;
      receiverAddress?: string;
      receiverCity?: string;
      receiverDistrict?: string;
      receiverMobile?: string;
      receiverName?: string;
      receiverPhone?: string;
      receiverState?: string;
      receiverTown?: string;
      userId?: string;
      sellerNick?: string;
      platformId?: string;
      togetherId?: string;
      shipListPrintStatus?: string;
      waybillPrintStatus?: string;
      mainTid?: string;
      orderCode?: string;
      isSuccess?: boolean;
      errorMsg?: string;
      /**
       * TradeVo
       */
      trades?: {
        /**
         * 手工单系统tid，主要用来申请单号
         */
        realTid?: string;
        /**
         * 手工单发货信息
         */
        shipInfo?: string;
        /**
         * 发件人
         */
        senderName?: string;
        /**
         * 发件人电话
         */
        senderPhone?: string;
        /**
         * 发件人手机
         */
        senderMobile?: string;
        /**
         * sender_province
         */
        senderProvince?: string;
        /**
         * sender_city
         */
        senderCity?: string;
        /**
         * sender_county
         */
        senderCounty?: string;
        /**
         * 发件人地址
         */
        senderAddress?: string;
        sellerId?: string;
        buyerMessage?: string;
        buyerNick?: string;
        realReceiverOpenId?: string;
        realReceiverDisplayNick?: string;
        /**
         * 买家昵称唯一标识
         */
        buyerOpenUid?: string;
        createTime?: string;
        invoiceName?: string;
        invoiceTitle?: string;
        invoiceKind?: string;
        invoiceType?: string;
        isChange?: boolean;
        isMendian?: boolean;
        isCod?: boolean;
        isPrintFhd?: string;
        isPrintKdd?: string;
        payTime?: string;
        /**
         * OrderVo
         */
        orders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          skuUuid?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          itemTitleSensitive?: boolean;
          itemLinkSensitive?: boolean;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          /**
           * String
           */
          ydNoSet?: string[];
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 是否系统赠品
           */
          isSysGift?: boolean;
          /**
           * 系统赠品状态
           */
          sysGiftStatus?: number;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货品自定义属性 ,ItemCustomAttributesDto
           */
          customAttributesList?: {
            /**
             * 自定义属性key,itemCustomAttribute1，ItemCustomAttribute2，ItemCustomAttribute3...
             */
            key?: string;
            /**
             * 自定义属性name,example：产地，质地，等级...
             */
            name?: string;
            /**
             * 自定义属性value,example：中国，硬，A...
             */
            value?: string;
            [k: string]: any;
          }[];
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 商品简称
           */
          itemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 验货发货快递名称
           */
          inspectionExName?: string;
          /**
           * 验货发货快递单id
           */
          inspectionExId?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          /**
           * 是否线上发货
           */
          onlineShip?: boolean;
          /**
           * 商品所属店铺id
           */
          itemSellerId?: string;
          /**
           * 商品所属平台
           */
          itemPlatform?: string;
          /**
           * 商品结算金额
           */
          settleAmount?: string;
          /**
           * 运费
           */
          freightAmount?: string;
          /**
           * 总金额
           */
          settleAmountSum?: string;
          /**
           * 是否快照信息不一致
           */
          isSnapshotAbnormal?: boolean;
          /**
           * 快照信息不一致原因
           */
          snapshotAbnormalMsg?: string;
          /**
           * 货品颜色
           */
          sysColor?: string;
          /**
           * 货品尺寸
           */
          sysSize?: string;
          /**
           * 子订单关税税费
           */
          subOrderTaxFee?: string;
          /**
           * 售后id
           */
          refundId?: string;
          [k: string]: any;
        }[];
        /**
         * OrderVo
         */
        giftOrders?: {
          hasConnected?: boolean;
          num?: number;
          sysNumber?: number;
          numIid?: string;
          oid?: string;
          ptOid?: string;
          cid?: string;
          outerId?: string;
          outerSkuId?: string;
          payment?: string;
          picPath?: string;
          price?: string;
          refundStatus?: string;
          skuId?: string;
          skuUuid?: string;
          status?: string;
          actualStockNum?: string;
          canSellStockNum?: string;
          tid?: string;
          title?: string;
          itemTitleSensitive?: boolean;
          itemLinkSensitive?: boolean;
          titleShort?: string;
          adjustAmount?: string;
          discount?: string;
          skuPropertiesName?: string;
          skuAlias?: string;
          skuName?: string;
          divideOrderFee?: string;
          weight?: string;
          pid?: string;
          ydNo?: string;
          kdName?: string;
          togetherId?: string;
          isShShip?: boolean;
          totalFee?: string;
          /**
           * String
           */
          ydNoSet?: string[];
          ydNoStr?: string;
          /**
           * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
           */
          stockOutHandleStatus?: number;
          /**
           * 是否缺货0-无缺货处理1：有缺货处理
           */
          isStockOut?: number;
          /**
           * 预售时间
           */
          preSaleTime?: string;
          /**
           * 是否为预售商品1表示是0表示否
           */
          isPreSale?: number;
          printedNum?: number;
          timingPromise?: string;
          cutoffMinutes?: string;
          esTime?: string;
          deliveryTime?: string;
          collectTime?: string;
          dispatchTime?: string;
          signTime?: string;
          storeCode?: string;
          /**
           * 预计配送时间段
           */
          esRange?: string;
          /**
           * 预计送达时间
           */
          esDate?: string;
          /**
           * 预计送达时间
           */
          osDate?: string;
          /**
           * 预计送达时间
           */
          osRange?: string;
          /**
           * 发货方式，1自己联系;2在线发货;3无需物流发货（tb对应虚拟发货）;4预发货;53PL发货;6家装发货
           */
          sendType?: string;
          /**
           * 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
           */
          promiseService?: string;
          noGoods?: boolean;
          noGoodsLink?: boolean;
          goodsWarn?: boolean;
          firstSend?: boolean;
          sysShipTime?: string;
          sysPrintTime?: string;
          /**
           * 是否忽略，0否，1是
           */
          ignore?: number;
          systemNumIid?: string;
          systemSkuId?: string;
          /**
           * 货品规格名称
           */
          systemSkuName?: string;
          sysStockCount?: string;
          /**
           * 库存预占数量
           */
          stockPreemptedNum?: string;
          /**
           * 已经分配库存数量
           */
          alreadyAllotStockNum?: string;
          /**
           * 组合货品分配信息JSON,,CombinationAllotStock[sysItemId(货品id),sysSkuId(货品规格id),alreadyAllotStockNum: 0(已分配库存数),num: 0(商品购买数量),groupProportionNum(组合比例)]
           */
          combinationAllotStockNum?: string;
          /**
           * 是否为组合商品，1:组合商品
           */
          isCombination?: number;
          /**
           * 标签状态0未生成1已生成
           */
          labelstatus?: number;
          /**
           * 0未打印1已打印
           */
          labelPrintStatus?: number;
          /**
           * 子商品信息 ,GroupRelationRecordDTO
           */
          groupRelationRecordList?: {
            /**
             * @mbg.generated,表字段:id
             */
            id?: number;
            /**
             * @mbg.generated,用户ID,表字段:user_id
             */
            userId?: number;
            /**
             * @mbg.generated,组合货品id,表字段:gorup_sys_item_id
             */
            groupSysItemId?: number;
            /**
             * @mbg.generated,组合规格id,表字段:gorup_sys_sku_id
             */
            groupSysSkuId?: number;
            /**
             * @mbg.generated,系统货品id,表字段:sys_item_id
             */
            sysItemId?: number;
            /**
             * @mbg.generated,系统规格id,表字段:sys_sku_id
             */
            sysSkuId?: number;
            /**
             * @mbg.generated,组合比例,表字段:group_proportion_num
             */
            groupProportionNum?: number;
            /**
             * 正品库存
             */
            salableItemStock?: number;
            /**
             * 可配货库存数量
             */
            salableItemDistributableStock?: number;
            /**
             * 正品预占数量
             */
            salableItemPreemptedNum?: number;
            /**
             * 成本价
             */
            costPrice?: string;
            /**
             * 货品名称
             */
            sysItemName?: string;
            /**
             * 货品别名
             */
            sysItemAlias?: string;
            /**
             * 货品规格名称
             */
            sysSkuName?: string;
            /**
             * 货品规格别名
             */
            sysSkuAlias?: string;
            /**
             * 货品规格商家编码
             */
            skuOuterId?: string;
            /**
             * 货品商家编码
             */
            outerId?: string;
            /**
             * 已分配库存数
             */
            alreadyAllotStockNum?: number;
            /**
             * 商品购买数量
             */
            num?: number;
            /**
             * @mbg.generated,货品规格图片,表字段:pic_url
             */
            picUrl?: string;
            /**
             * 库存情况
             */
            stockStatusEnum?: {
              [k: string]: any;
            };
            /**
             * 库存预警值
             */
            stockWarnNum?: number;
            /**
             * 在途库存
             */
            transitItemStock?: number;
            /**
             * 毛重
             */
            weight?: string;
            /**
             * 净重
             */
            netWeight?: string;
            /**
             * 吊牌价
             */
            tagPrice?: string;
            /**
             * @mbg.generated网销价,表字段:price
             */
            price?: string;
            /**
             * 品牌id
             */
            brandId?: number;
            /**
             * 品牌名称
             */
            brandName?: string;
            /**
             * 货品分类id
             */
            classifyId?: number;
            /**
             * 分类名称
             */
            classifyName?: string;
            /**
             * 市场
             */
            market?: string;
            /**
             * 档口
             */
            stall?: string;
            /**
             * 货号
             */
            itemNo?: string;
            /**
             * 条形码
             */
            barCode?: string;
            /**
             * 供应商id
             */
            supplierId?: number;
            /**
             * 供应商名称
             */
            supplierName?: string;
            /**
             * 供应商地址-省
             */
            storageAddrProvince?: string;
            /**
             * 供应商地址-市
             */
            storageAddrCity?: string;
            /**
             * 供应商地址-区
             */
            storageAddrDistrict?: string;
            /**
             * 供应商地址-详细地址
             */
            storageAddr?: string;
            [k: string]: any;
          }[];
          /**
           * 预占库存标签,1预占;2回占
           */
          occupiedStockStatus?: number;
          /**
           * 手工单专用价格类型1：网销价；2:零售价；3：批发价4：吊牌价5：成本价6自定义价
           */
          priceType?: string;
          /**
           * 是否忽略（0否，1是）
           */
          ignoreExcFlag?: number;
          sysOuterId?: string;
          sysPicPath?: string;
          sysOuterSkuId?: string;
          /**
           * 订单修改类型,详情：SysExchangeTypeEnum
           */
          sysExchangeType?: number;
          /**
           * 是否是赠品
           */
          isGift?: string;
          /**
           * 是否系统赠品
           */
          isSysGift?: boolean;
          /**
           * 系统赠品状态
           */
          sysGiftStatus?: number;
          /**
           * 吊牌价
           */
          tagPrice?: string;
          /**
           * 品牌id
           */
          brandId?: number;
          /**
           * 品牌名称
           */
          brandName?: string;
          /**
           * 货品分类id
           */
          classifyId?: number;
          /**
           * 分类名称
           */
          classifyName?: string;
          /**
           * 市场
           */
          market?: string;
          /**
           * 档口
           */
          stall?: string;
          /**
           * 货品规格名称
           */
          sysSkuName?: string;
          /**
           * 货品规格别名
           */
          sysSkuAlias?: string;
          /**
           * 货品自定义属性 ,ItemCustomAttributesDto
           */
          customAttributesList?: {
            /**
             * 自定义属性key,itemCustomAttribute1，ItemCustomAttribute2，ItemCustomAttribute3...
             */
            key?: string;
            /**
             * 自定义属性name,example：产地，质地，等级...
             */
            name?: string;
            /**
             * 自定义属性value,example：中国，硬，A...
             */
            value?: string;
            [k: string]: any;
          }[];
          /**
           * 货号
           */
          itemNo?: string;
          /**
           * 条形码
           */
          barCode?: string;
          /**
           * 成本价
           */
          costPrice?: string;
          /**
           * 供应商id
           */
          supplierId?: number;
          /**
           * 供应商名称
           */
          supplierName?: string;
          /**
           * 供应商地址-省
           */
          storageAddrProvince?: string;
          /**
           * 供应商地址-市
           */
          storageAddrCity?: string;
          /**
           * 供应商地址-区
           */
          storageAddrDistrict?: string;
          /**
           * 供应商地址-详细地址
           */
          storageAddr?: string;
          /**
           * 店铺id
           */
          sellerId?: string;
          /**
           * 尺码
           */
          platform?: string;
          /**
           * 店铺名称
           */
          sellerNick?: string;
          /**
           * 货品简称
           */
          sysItemAlias?: string;
          /**
           * 商品简称
           */
          itemAlias?: string;
          /**
           * 囤货数量
           */
          stockpileNum?: number;
          /**
           * 缺货数量
           */
          stockOutNum?: number;
          /**
           * 颜色
           */
          color?: string;
          /**
           * 尺码
           */
          size?: string;
          /**
           * 代发订单
           */
          dropShipping?: boolean;
          /**
           * 验货发货属于这笔运单号的子单
           */
          inspectionShippingOrder?: boolean;
          /**
           * 模板id验货发货时返回
           */
          templateId?: string;
          /**
           * 验货发货快递单号
           */
          inspectionExNumber?: string;
          /**
           * 验货发货快递公司code
           */
          inspectionExCode?: string;
          /**
           * 验货发货快递单类型
           */
          inspectionKddType?: number;
          /**
           * 验货发货快递名称
           */
          inspectionExName?: string;
          /**
           * 验货发货快递单id
           */
          inspectionExId?: number;
          /**
           * 免费服务项条目详情信息
           */
          freeEntryServiceInfo?: string;
          /**
           * 付费服务项条目信息
           */
          payEntryServiceInfo?: string;
          /**
           * 判断是否需要上传序列码
           */
          needSerialNumber?: boolean;
          /**
           * 商品识别码（SN码、IMEI号、ICCID码)
           */
          productIdCode?: string;
          /**
           * 达人id
           */
          authorId?: string;
          /**
           * 达人name
           */
          authorName?: string;
          /**
           * 是否线上发货
           */
          onlineShip?: boolean;
          /**
           * 商品所属店铺id
           */
          itemSellerId?: string;
          /**
           * 商品所属平台
           */
          itemPlatform?: string;
          /**
           * 商品结算金额
           */
          settleAmount?: string;
          /**
           * 运费
           */
          freightAmount?: string;
          /**
           * 总金额
           */
          settleAmountSum?: string;
          /**
           * 是否快照信息不一致
           */
          isSnapshotAbnormal?: boolean;
          /**
           * 快照信息不一致原因
           */
          snapshotAbnormalMsg?: string;
          /**
           * 货品颜色
           */
          sysColor?: string;
          /**
           * 货品尺寸
           */
          sysSize?: string;
          /**
           * 子订单关税税费
           */
          subOrderTaxFee?: string;
          /**
           * 售后id
           */
          refundId?: string;
          [k: string]: any;
        }[];
        gifts?: string;
        /**
         * 实收金额
         */
        receivedPayment?: string;
        payment?: string;
        paymentSensitive?: boolean;
        discountAmount?: string;
        postFee?: string;
        prePostFee?: string;
        refundPostFee?: string;
        receiverMobile?: string;
        receiverCity?: string;
        receiverDistrict?: string;
        receiverPhone?: string;
        receiverAddress?: string;
        receiverName?: string;
        receiverState?: string;
        receiverTown?: string;
        receiverZip?: string;
        sellerFlag?: string;
        userId?: string;
        sellerNick?: string;
        sellerNickSensitive?: boolean;
        platform?: string;
        sellerMemo?: string;
        ignoreType?: number;
        sellerMemoFlag?: string;
        sellerMemoFlagName?: string;
        tid?: string;
        ptTid?: string;
        totalFee?: string;
        status?: string;
        gift?: string;
        type?: string;
        sendRemindFlag?: number;
        sendRemindHour?: string;
        isAddCostSF?: boolean;
        /**
         * OrderPromiseDetailVo
         */
        orderPromiseDetailVo?: {
          orderPromiseKdCode?: string;
          orderPromiseKdName?: string;
          /**
           * 指定包材
           */
          orderPromiseBc?: string;
          orderPromiseDeliveryTime?: number;
          [k: string]: any;
        };
        freeSF?: number;
        /**
         * 商家优惠金额
         */
        sellerDiscount?: string;
        distributorTid?: string;
        supplierTid?: string;
        /**
         * 平台折扣金额
         */
        platformDiscount?: string;
        /**
         * 宝贝数量：取订单中的商品总数量
         */
        totalNums?: number;
        /**
         * 宝贝种类
         */
        itemCateNum?: number;
        /**
         * 是否是预发货
         */
        isPreShip?: boolean;
        isPending?: boolean;
        isCancelSend?: boolean;
        isMatchCancelSend?: boolean;
        imel?: string;
        deviceSn?: string;
        overseaTracing?: string;
        expressName?: string;
        /**
         * 催发货标记0无催发货，1有催发货；
         */
        urge?: number;
        /**
         * 催发货时间
         */
        urgeShippingTime?: string;
        /**
         * 订单标签 ,String
         */
        serviceTagList?: string[];
        /**
         * 预发货任务id
         */
        preShipId?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单(该参数为map)
         */
        sellerFlagSys?: {
          /**
           * String
           */
          mapKey?: {
            [k: string]: any;
          };
          /**
           * Integer
           */
          mapValue?: {
            [k: string]: any;
          };
          [k: string]: any;
        };
        /**
         * 标识当前订单是否存在退款，不做存储
         */
        hasRefund?: boolean;
        refundStatus?: string;
        totalWeight?: string;
        /**
         * 快递单号 ,String
         */
        ydNoSet?: string[];
        /**
         * 多包裹运单数量
         */
        multiPackYdCount?: number;
        /**
         * 快递公司编号
         */
        logisticsId?: string;
        /**
         * 缺货处理状态-1:无缺货处理0:缺货待处理1缺货已处理
         */
        stockOutHandleStatus?: number;
        /**
         * 是否缺货0-无缺货处理1：有缺货处理
         */
        isStockOut?: number;
        /**
         * 预售时间
         */
        preSaleTime?: string;
        /**
         * 是否为预售商品1表示是0表示否
         */
        isPreSale?: number;
        /**
         * 风控状态（值为1则为风控订单，值为0则为正常订单）
         */
        riskControlStatus?: string;
        receiverNameMask?: string;
        receiverPhoneMask?: string;
        receiverAddressMask?: string;
        receiverMobileMask?: string;
        idxEncodeReceiverMobile?: string;
        idxEncodeReceiverName?: string;
        idxEncodeReceiverAddress?: string;
        /**
         * 订单调fullInfo接口的时间
         */
        tidFullInfoTime?: string;
        /**
         * 天猫直送，值true或者false
         */
        tmallDelivery?: boolean;
        /**
         * 3PL有时效订单标，值true或者false
         */
        threePlTiming?: boolean;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        deliveryCps?: string;
        /**
         * 取
         */
        cpCode?: string;
        /**
         * cpCode转cpName
         */
        cpName?: string;
        /**
         * 是否催发货1：催发货0：非催发货
         */
        isUrgeDelivery?: number;
        /**
         * 应派送cp，多个用逗号分隔,例如YTO,YUNDA，基本只有一个CP，取第一个即可
         */
        caid?: string;
        /**
         * trade_fromWAP,JHS交易内部来源
         */
        tradeFrom?: string;
        /**
         * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
         */
        fenxiaos?: number;
        payHours?: number;
        receiverSecret?: string;
        mobileSecret?: string;
        timingPromise?: string;
        promiseService?: string;
        esDate?: string;
        esRange?: string;
        osDate?: string;
        osRange?: string;
        cutoffMinutes?: string;
        esTime?: string;
        deliveryTime?: string;
        collectTime?: string;
        sendTime?: string;
        signTime?: string;
        dispatchTime?: string;
        /**
         * value=logistics_upgrade为天猫物流升级订单
         */
        asdpBizType?: string;
        /**
         * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
         */
        asdpAds?: string;
        /**
         * 天猫送货上门快递名称
         */
        sendExName?: string;
        /**
         * 天猫送货上门快递编码
         */
        sendExCode?: string;
        /**
         * 全渠道商品通相关字段
         */
        omnichannelParam?: string;
        /**
         * 是否是海外购
         */
        isThreePl?: boolean;
        /**
         * 是否风险留言订单
         */
        pbly?: boolean;
        /**
         * 家装订单
         */
        tmserSpu?: boolean;
        /**
         * 淘宝后台getShippingType
         */
        shippingType?: string;
        /**
         * 淘宝后台getShippingType对应名称，后期
         */
        shippingName?: string;
        deliveryTypeDesc?: string;
        /**
         * 订单类型交易方式,一口价,送礼,赠品领取,货到付款,定金预售
         */
        tradeType?: string;
        /**
         * 支付类型
         */
        payType?: string;
        biz?: string;
        /**
         * 是否为乡镇订单
         */
        hasTown?: number;
        /**
         * 打印时间
         */
        printTime?: string;
        /**
         * 发货时间
         */
        shipTime?: string;
        /**
         * 系统发货时间
         */
        sysShipTime?: string;
        /**
         * 提示手动合单的判断标示
         */
        mergeBuyerNick?: string;
        noGoodsLink?: boolean;
        goodsWarn?: boolean;
        firstSend?: boolean;
        smartSelectExpress?: string;
        labelstatus?: number;
        /**
         * 0未打印1全部打印
         */
        labelPrintStatus?: number;
        promiseDeliveryTime?: boolean;
        /**
         * PromiseLogistics
         */
        promiseLogisticsList?: {
          company?: string;
          exCode?: string;
          [k: string]: any;
        }[];
        wrapperDescription?: boolean;
        duoduoWholesale?: boolean;
        shipHold?: boolean;
        jdStoreOrderType?: number;
        jdOrderShipType?: number;
        /**
         * 额外运单 ,ExtraDeliveryVO
         */
        extraDeliveryList?: {
          /**
           * 快递运单号
           */
          trackingNumber?: string;
          /**
           * 快递公司id
           */
          logisticsId?: number;
          /**
           * 快递公司名称
           */
          logisticsName?: string;
          [k: string]: any;
        }[];
        /**
         * true跨境订单false非跨境
         */
        crossBorder?: boolean;
        /**
         * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
         */
        consolidateType?: string;
        /**
         * 订单来源：HAND手工单
         */
        source?: string;
        changeAdderFlag?: number;
        /**
         * 拼团订单未完成true未完成false或者为空为正常订单
         */
        pOrderUnfinished?: boolean;
        /**
         * 订单类型
         */
        tradeTypeTagStr?: string;
        /**
         * 商品结算金额
         */
        itemSettleAmount?: string;
        /**
         * 运费
         */
        freightAmount?: string;
        /**
         * 总金额
         */
        orderSettleAmountSum?: string;
        /**
         * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
         */
        openAddressId?: string;
        /**
         * 精选联盟
         */
        isSelectedAlliance?: boolean;
        /**
         * 小店自卖
         */
        isSmallStoreSelfSelling?: boolean;
        /**
         * 闪电购商品
         */
        isFlashBuyingProducts?: boolean;
        /**
         * 重新发货
         */
        isResend?: boolean;
        /**
         * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
         */
        receiverId?: string;
        /**
         * 回流订单平台类型
         */
        hlPlatformType?: string;
        /**
         * 回流订单是否加密
         */
        hlEncryptOrder?: boolean;
        /**
         * fxg承诺日达1表示是0表示否
         */
        appointmentArrival?: number;
        /**
         * 发货仓编号
         */
        outWarehouseId?: string;
        /**
         * 发货仓名称
         */
        warehouseName?: string;
        /**
         * 指定时间送达承诺时间送达
         */
        receiptDate?: string;
        /**
         * ali定制服务1表示是0表示否
         */
        aliEntryServiceInfo?: number;
        /**
         * 判断是否需要上传序列码
         */
        needSerialNumber?: boolean;
        /**
         * 线下备注
         */
        sysMemo?: string;
        /**
         * 达人标签订单
         */
        isAuthorOrder?: boolean;
        /**
         * 线下备注图片
         */
        sysMemoPic?: string;
        /**
         * 标签：加运费发顺丰
         */
        hasSfExpressService?: boolean;
        /**
         * 顺丰加价服务费
         */
        sfExpressFee?: string;
        /**
         * 送货上门
         */
        hasDeliveryOnDoor?: boolean;
        /**
         * 京东POP订单选项
         */
        jdDeliveryOnDoorOption?: number;
        /**
         * 是否线上发货
         */
        onlineShip?: boolean;
        /**
         * 是否拆单发货
         */
        isSplit?: boolean;
        /**
         * 直邮活动标记，拼多多平台
         */
        directMailActivity?: boolean;
        /**
         * 回流密闻订单加解密信息，一般用于脱敏信息打单取号等 ,DownstreamEncryptDetails
         */
        downstreamEncryptDetails?: {
          sellerNick?: string;
          sellerId?: string;
          tid?: string;
          oaid?: string;
          hlPlatformType?: string;
          platformExtraInfo?: string;
          platformAppKey?: string;
          receiverProvince?: string;
          receiverCity?: string;
          receiverArea?: string;
          receiverTown?: string;
          encryptReceiverMobile?: string;
          encryptReceiverAddress?: string;
          encryptReceiverName?: string;
          outPlatformSupplyOrderNo?: string;
          outPlatformSubCode?: string;
          outSupplierId?: string;
          [k: string]: any;
        };
        /**
         * 插旗标签
         */
        sellerFlagTag?: string;
        storageTime?: string;
        /**
         * 指定快递金额
         */
        promiseLogisticsAmount?: string;
        /**
         * 是否快照信息不一致
         */
        isSnapshotAbnormal?: boolean;
        /**
         * 快照信息不一致原因
         */
        snapshotAbnormalMsg?: string;
        /**
         * 代收金额
         */
        waitReceiveAmount?: string;
        /**
         * 仓库Id
         */
        storageId?: number;
        /**
         * 仓库名称
         */
        storageName?: string;
        /**
         * 仓库类型描述
         */
        storageTypeDesc?: string;
        /**
         * 仓库类型
         */
        storageType?: number;
        /**
         * 政府补贴金额
         */
        governmentDiscount?: number;
        /**
         * bic订单码
         */
        orderCode?: string;
        /**
         * rds最后同步时间
         */
        rdsLastSyncTime?: string;
        /**
         * rds同步超时标记
         */
        rdsLastSyncTimeout?: boolean;
        modified?: string;
        /**
         * 拆分订单类型<br>,REGULAR_GIFT_ORDER("拆分后普通商品+赠品订单"),<br>,REGULAR_ORDER("拆分后仅普通订单"),<br>,GIFT("拆分后仅赠品订单"),<br>,DEFAULT("默认订单");<br>,对应枚举com.kuaidizs.general.rds.api.common.enums.TradeSplitTypeEnum
         */
        splitTradeType?: string;
        /**
         * 是否已解密,仅针对换货手工单场景下有效
         */
        hasDecrypt?: boolean;
        /**
         * 黑名单
         */
        blackList?: boolean;
        /**
         * 订单数据加密类型，1：自加密2：平台加密，对应枚举TradeEncodeTypeEnum
         */
        tradeEncodeType?: number;
        [k: string]: any;
      }[];
      sendRemindFlag?: number;
      sendRemindHour?: string;
      payTime?: string;
      isPending?: boolean;
      isAddCostSF?: boolean;
      freeSF?: number;
      expressName?: string;
      sellerId?: string;
      receiverNameMask?: string;
      receiverPhoneMask?: string;
      receiverAddressMask?: string;
      receiverMobileMask?: string;
      idxEncodeReceiverMobile?: string;
      idxEncodeReceiverName?: string;
      idxEncodeReceiverAddress?: string;
      encodeTid?: string;
      caid?: string;
      /**
       * 提示手动合单的判断标示
       */
      mergeBuyerNick?: string;
      /**
       * 多包裹运单数量
       */
      multiPackYdCount?: number;
      /**
       * 是否包含预发货订单
       */
      isPreShip?: boolean;
      /**
       * String
       */
      preShipIds?: string[];
      hasRefund?: boolean;
      receiverZip?: string;
      /**
       * (该参数为map)
       */
      sellerFlagSys?: {
        /**
         * String
         */
        mapKey?: {
          [k: string]: any;
        };
        /**
         * Integer
         */
        mapValue?: {
          [k: string]: any;
        };
        [k: string]: any;
      };
      /**
       * :
       */
      tids?: string[];
      /**
       * :
       */
      ptTids?: string[];
      /**
       * CaiNiaoIntelliExpress
       */
      smartExpress?: {
        exCode?: string;
        exName?: string;
        [k: string]: any;
      };
      type?: string;
      /**
       * 分销串0或1表示是不是分销订单。打单界面标识是否为分销订单
       */
      fenxiaos?: number;
      /**
       * 是否合单
       */
      isMerge?: string;
      /**
       * 总实收金额
       */
      totalReceivedPayment?: string;
      /**
       * 总支付金额
       */
      totalPayment?: string;
      /**
       * 总商家优惠金额
       */
      totalSellerDiscount?: string;
      /**
       * 总平台折扣金额
       */
      totalPlatformDiscount?: string;
      /**
       * 总邮费
       */
      totalPostFee?: string;
      /**
       * 总预付邮费
       */
      totalPrePostFee?: string;
      /**
       * 总退邮费
       */
      totalRefundPostFee?: string;
      /**
       * 总重量
       */
      totalWeight?: string;
      /**
       * 已发货的快递单号
       */
      ydNo?: string;
      /**
       * 快递单号 ,String
       */
      ydNoSet?: string[];
      /**
       * 快递公司 ,String
       */
      logisticsCompanySet?: string[];
      /**
       * 快递单号 ,String
       */
      waybillNoSet?: string[];
      /**
       * 商品结算金额
       */
      itemSettleAmount?: string;
      /**
       * 运费
       */
      freightAmount?: string;
      /**
       * 总金额=商品结算金额+运费
       */
      orderSettleAmountSum?: string;
      /**
       * 验单发货订单号搜索先发货订单模板id
       */
      verifyOrderListShowId?: number;
      /**
       * 模版的扩展字段，里面包含了用户模版id
       */
      templateYdAttr?: string;
      /**
       * 订单标记（用于黑白订单标记）,0：正常订单（默认）,1：白订单（表明这笔订单是商家请人刷的订单）,2：黑名单（表明这笔订单是别人恶意刷的订单）
       */
      tidMark?: number;
      /**
       * OrderPromiseVo
       */
      orderPromiseVo?: {
        orderPromiseDeliveryTime?: number;
        isManyKd?: boolean;
        [k: string]: any;
      };
      /**
       * Object
       */
      orderTagList?: {
        [k: string]: any;
      }[];
      payHours?: number;
      goodsNum?: string;
      /**
       * 家装订单
       */
      tmserSpu?: boolean;
      /**
       * 异常地址
       */
      abnormalAddress?: boolean;
      /**
       * 智选快递的快递名称
       */
      ExpressName?: string;
      /**
       * 打印菜鸟面单时是否需要隐私服务
       */
      cnPrivacy?: boolean;
      receiverSecret?: string;
      mobileSecret?: string;
      /**
       * OpenAddresseeID（收件人ID）,基于收件人的信息(姓名、地址、手机号、电话）、订单创建时间、店铺、appkey加密生成的ID。,相同的收件人，在同一家店铺，在固定时间周期内（通常1个自然周）内创建的订单，OAID相同。
       */
      oaid?: string;
      /**
       * 天猫送货上门快递名称
       */
      sendExName?: string;
      /**
       * 天猫送货上门快递编码
       */
      sendExCode?: string;
      /**
       * value=logistics_upgrade为天猫物流升级订单
       */
      asdpBizType?: string;
      /**
       * value=201里面多个值时用英文逗号隔开,<p>,201为送货上门服务
       */
      asdpAds?: string;
      signTime?: string;
      deliveryTime?: string;
      /**
       * 是否为乡镇订单
       */
      hasTown?: number;
      /**
       * 催发货标记0无催发货，1有催发货；
       */
      urge?: number;
      /**
       * 催发货时间
       */
      urgeShippingTime?: string;
      changeAdderFlag?: number;
      noGoods?: boolean;
      noGoodsLink?: boolean;
      goodsWarn?: boolean;
      firstSend?: boolean;
      preSell?: boolean;
      /**
       * 疫情标记,0：非疫情地区,1：疫情中风险地区,2：疫情高风险地区
       */
      epidemicMark?: number;
      /**
       * 风控状态（值为1则为风控订单，值为0则为正常订单）
       */
      riskControlStatus?: string;
      /**
       * 智选快递
       */
      smartSelectExpress?: string;
      /**
       * 智选快递模板级别推荐结果、是否手动指定
       */
      smartExpressTemplate?: string;
      /**
       * 合单智选快递模板级别推荐结果、是否手动指定
       */
      packSmartExpressTemplate?: string;
      labelstatus?: number;
      /**
       * 0未打印1全部打印2部分打印
       */
      labelPrintStatus?: number;
      promiseDeliveryTime?: boolean;
      /**
       * PromiseLogistics
       */
      promiseLogisticsList?: {
        company?: string;
        exCode?: string;
        [k: string]: any;
      }[];
      wrapperDescription?: boolean;
      /**
       * 顺丰包邮
       */
      freeSf?: boolean;
      /**
       * true跨境订单false非跨境
       */
      crossBorder?: boolean;
      tradeType?: string;
      /**
       * 集运类型0-香港集运、1-新疆集运、fxg_1-新疆中转、fxg_2-西藏中转
       */
      consolidateType?: string;
      /**
       * 订单来源:其他平台订单,Hand手工订单
       */
      source?: string;
      /**
       * 拼团订单未完成true未完成false或者为空为正常订单
       */
      pOrderUnfinished?: boolean;
      /**
       * 分销商用户ID
       */
      distributorUserId?: number;
      /**
       * 分销商账号
       */
      distributorAccount?: string;
      /**
       * 分销商名称
       */
      distributorName?: string;
      /**
       * 供应商名称
       */
      supplierName?: string;
      /**
       * 分销商联系人
       */
      distributorLinkMan?: string;
      /**
       * 分销商联系电话
       */
      distributorMobile?: string;
      /**
       * 分销商备注
       */
      distributorRemark?: string;
      /**
       * 分销状态,0:申请中,1：合作中,2：拒绝,3：终止
       */
      distributorStatus?: number;
      /**
       * 收件人姓名+手机+地址等计算得出，用来查询收件人详情小红书专供
       */
      openAddressId?: string;
      /**
       * 特殊的地址id,,视频小店的：电子面单代发时的订单密文
       */
      receiverId?: string;
      /**
       * 回流订单平台类型
       */
      hlPlatformType?: string;
      /**
       * 回流订单是否加密
       */
      hlEncryptOrder?: boolean;
      /**
       * fxg承诺日达1表示是0表示否
       */
      appointmentArrival?: number;
      /**
       * 发货仓编号
       */
      outWarehouseId?: string;
      /**
       * 发货仓名称
       */
      warehouseName?: string;
      /**
       * 指定时间送达承诺时间送达
       */
      receiptDate?: string;
      /**
       * ali定制服务1表示是0表示否
       */
      aliEntryServiceInfo?: number;
      /**
       * 送货上门
       */
      hasDeliveryOnDoor?: boolean;
      /**
       * 退款状态
       */
      refundStatus?: string;
      /**
       * 订单状态
       */
      status?: string;
      /**
       * 直邮活动标记，拼多多平台
       */
      directMailActivity?: boolean;
      /**
       * 是否线上发货
       */
      onlineShip?: boolean;
      /**
       * 是否拆单发货
       */
      isSplit?: boolean;
      /**
       * 订单标签 ,String
       */
      serviceTagList?: string[];
      /**
       * 分销订单来源
       */
      ptType?: string;
      /**
       * 指定快递金额
       */
      promiseLogisticsAmount?: string;
      /**
       * 代收金额
       */
      waitReceiveAmount?: string;
      /**
       * 是否显示按子订单拆分，即显示按子订单拆分，true:允许,false不允许
       */
      allowSubOrderSplit?: boolean;
      /**
       * 仓库Id
       */
      storageId?: number;
      /**
       * 仓库名称
       */
      storageName?: string;
      /**
       * 仓库类型描述
       */
      storageTypeDesc?: string;
      /**
       * 仓库类型
       */
      storageType?: number;
      /**
       * rds最后同步时间
       */
      rdsLastSyncTime?: string;
      /**
       * rds同步超时标记
       */
      rdsLastSyncTimeout?: boolean;
      /**
       * 黑名单
       */
      blackList?: boolean;
      [k: string]: any;
    }[];
    /**
     * 拆分失败的订单对象 ,RdsBatchSplitMergeTradeResultVo
     */
    failedList?: {
      /**
       * 合单订单号分组，原样返回 ,String
       */
      groupTidList?: string[];
      /**
       * 错误信息
       */
      message?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}
