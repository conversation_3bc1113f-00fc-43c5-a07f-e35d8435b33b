import { TradePrintSetGetPrintSetApi } from "@/apis/trade";
import { IPrintSet } from "@/types/trade/index";
import event from '@/libs/event';
import memo from "@/utils/memo";
import { porinMergePrintOpenNumber } from '@/utils/pointTrack/utils';
import { getExCompanyAll } from "@/apis/user";
import userStore from "@/stores/user";
import { PLAT_HAND, PLAT_SPH, PLAT_XHS, PLAT_DW, PLAT_KTT } from "@/constants";

export enum CacheKeys {
	AddressVersion='common.area.version',
	AddressList = 'common.area.list',
	ShopList = 'common.shop.list',
	AdvancedSet = 'common.advanced.set',
	ExpressList = 'common.express.list'
}

/**
 * 获取地址信息，缓存 localStorage 7天
 * !! 优化：永久缓存，根据nacos配置决定要不要更新地址
 */
// export const getAllAddressListWithCache = memo(getAllAddressApi, {
// 	cacheType: 'local',
// 	cacheKey: () => CacheKeys.AddressList,
// 	// maxAge: 1000 * 24 * 7, // 7 天
// });

/**
 * 获取店铺列表
 */
// 目前没有调用，api/user 中前面被注释掉了，所以将此处也注释掉避免控制台警告
// ! 统一从mobx中获取，不要从这个地方拿值了
// export const getShopListWithCache = memo(async() => {
// 	let res;
// 	try {
// 		res = await PlatformShopGetPlatformShopsApi({});
// 	} catch (e) {
// 		console.warn(e);
// 	}
// 	return res?.list;
// }, {
// 	cacheType: 'session',
// 	cacheKey: () => CacheKeys.ShopList,
// });

const sleep = (time: number) => {
	return new Promise((resolve) => {
		setTimeout(resolve, time);
	});
};

let isGetAdvancedSet = false;
let isGetExpressList = false;
/**
 * 获取物流公司列表
 */
export const getExpressListWithCache = memo(async() => {
	while (isGetExpressList) {
		// eslint-disable-next-line no-await-in-loop
		await sleep(200);
	}

	let res;
	try {
		isGetExpressList = true;
		res = await getExCompanyAll({});
		isGetExpressList = false;
	} catch (e) {
		isGetExpressList = false;
		console.warn(e);
	}
	console.log('isGetExpressList', res);
	return res;
}, {
	cacheType: 'session',
	cacheKey: () => CacheKeys.ExpressList,
});
/**
 * 获取店铺列表
 */
export const getAdvancedSetWithCache = memo(async() => {
	while (isGetAdvancedSet) {
		// eslint-disable-next-line no-await-in-loop
		await sleep(200);
	}

	let res: IPrintSet = {};
	// let res;
	try {
		isGetAdvancedSet = true;
		res = await TradePrintSetGetPrintSetApi({});
		isGetAdvancedSet = false;
	} catch (e) {
		isGetAdvancedSet = false;
		console.warn(e);
	}
	// console.log('getAdvancedSetWithCache', res);
	if (res?.groupPrintSetJsonString && typeof res.groupPrintSetJsonString === 'string') {
		try {
			res.groupPrintSetJsonString = JSON.parse(res.groupPrintSetJsonString);
			if (res?.groupPrintSetJsonString?.printOrderByHandOrder || res?.groupPrintSetJsonString?.handOrderMatchPlatform) {
				res.groupPrintSetJsonString.orderMatchSetting = [// 订单匹配规则
					{
						platform: PLAT_HAND,
						bindControlType: res.groupPrintSetJsonString.handOrderMatchPlatform
					},
					{
						platform: PLAT_DW,
						bindControlType: res.groupPrintSetJsonString.handOrderMatchPlatform
					},
					{
						platform: PLAT_KTT,
						bindControlType: res.groupPrintSetJsonString.handOrderMatchPlatform
					}
				];
				delete res.groupPrintSetJsonString.printOrderByHandOrder;
				delete res.groupPrintSetJsonString.handOrderMatchPlatform;
			}
			// 小红书模板匹配不再通过高级设置
			res.groupPrintSetJsonString.orderMatchSetting = res?.groupPrintSetJsonString?.orderMatchSetting.filter(o => ![PLAT_XHS, PLAT_SPH].includes(o.platform));
			let isIncloudsDw = res?.groupPrintSetJsonString?.orderMatchSetting.find(o => o.platform === PLAT_DW);
			let isIncloudsKtt = res?.groupPrintSetJsonString?.orderMatchSetting.find(o => o.platform === PLAT_KTT);
			let isIncloudsSph = res?.groupPrintSetJsonString?.controlsSortSet.includes('14');
			if (!isIncloudsSph) {
				res.groupPrintSetJsonString.controlsSortSet += ',14';
			}
			if (!isIncloudsDw) {
				res.groupPrintSetJsonString.orderMatchSetting.push({
					platform: PLAT_DW,
					bindControlType: 3
				});
			}
			if (!isIncloudsKtt) {
				res.groupPrintSetJsonString.orderMatchSetting.push({
					platform: PLAT_KTT,
					bindControlType: 7, // 默认拼多多模版
				});
			}
			if (!res.groupPrintSetJsonString.useLocalCatchPrint) {
				res.groupPrintSetJsonString.useLocalCatchPrint = 2;
			}
			// 去除controlsSortSet中的重复数字
			if (res?.groupPrintSetJsonString?.controlsSortSet) {
				const sortSetArr = res.groupPrintSetJsonString.controlsSortSet.split(',');
				const uniqueSortSet = [...new Set(sortSetArr)].join(',');
				res.groupPrintSetJsonString.controlsSortSet = uniqueSortSet;
			}

			console.log('%c [ 高级设置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
		} catch (error) {
			console.error(error);
		}
	} else if (res) {
		res.groupPrintSetJsonString = {
			controlsSortSet: "3,8,7,9,5,13,14,17", // 控件打印顺序
			printOrderByOrder: 1, // 订单打印顺序规则
			openMergePrint: 1, // 融合打印开关
			isNewUser: 1,
			openOddNum: 1,
			numTipVal: 10,
			useLocalCatchPrint: 2,
			orderMatchSetting: [// 订单匹配规则
				{
					platform: PLAT_HAND,
					bindControlType: 3
				},
				{
					platform: PLAT_DW,
					bindControlType: 3
				},
				{
					platform: PLAT_KTT,
					bindControlType: 7
				}
			]
		};
	}
	// 为新用户爆款打单配置默认值
	if (!res?.printSetExpandDTO?.topItemShow) {
		const { version } = await userStore.getUserInfo();
		let topItemShow = "";
		let topSkuShow = "";
		if (version == 2) {
			topItemShow = `{"itemPerspective":["21","24","22","23"]}`;
			topSkuShow = `{"itemPerspective":["21","22","23"]}`;
		} else {
			topItemShow = `{"itemPerspective":["1","2","3"],"sysPerspective":["11","12"]}`;
			topSkuShow = `{"itemPerspective":["2","1"],"sysPerspective":["11","12","13"]}`;
		}
		res.printSetExpandDTO = { topItemShow, topSkuShow, itemTopMode: res.printSetExpandDTO?.itemTopMode || 1, sendTimeoutAbnormalHours: res?.printSetExpandDTO?.sendTimeoutAbnormalHours || 3 };
	}
	if (!res?.printSetExpandDTO?.itemTopMode) {
		res.printSetExpandDTO = { ...res.printSetExpandDTO, itemTopMode: 1 };
	}
	console.log(res?.printSetExpandDTO);
	if (!("multiplePackageDelivery" in res?.printSetExpandDTO)) {
		res.printSetExpandDTO = { ...res.printSetExpandDTO, multiplePackageDelivery: true };
	}

	// 为新的自定义按钮分组做兼容
	if (!res?.customPrintSetJsonString2 && res.customPrintSetJsonString) {
		res.customPrintSetJsonString2 = res.customPrintSetJsonString;
	}
	console.log('resresres', res);
	return res;
}, {
	cacheType: 'session',
	cacheKey: () => CacheKeys.AdvancedSet,
});

export const updateAdvancedSet = async(partialSet: Partial<IPrintSet>, shouldReSearch = false) => {
	const oldAdvancedSet = await getAdvancedSetWithCache();
	const serializedAdvancedSet = JSON.stringify({
		...oldAdvancedSet,
		...partialSet,
	});
	porinMergePrintOpenNumber({
		...oldAdvancedSet,
		...partialSet,
	});
	window.sessionStorage.setItem(CacheKeys.AdvancedSet, serializedAdvancedSet);
	if (shouldReSearch) {
		event.emit('printBatch.reSearch');
	}

};
