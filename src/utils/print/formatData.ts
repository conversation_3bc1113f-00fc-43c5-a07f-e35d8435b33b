import { filterUdbUdc } from '@/pages/Trade/utils';
import { isSourceHand } from '@/components-biz/ShopListSelect/shopListUtils';
import { fhdRuleSortData } from './fhdRuleSortData';

export const formatTradeData = ({
	packages,
	isGetFhdInfo,
	isScan,
	firstSendMode,
	isKg,
	isForcePrint,
	historySids,
	showCombinationInfo,
	printFrom,
}: {
	packages: any,
	isGetFhdInfo: boolean,
	isScan?: boolean,
	firstSendMode?: boolean,
	isKg?: boolean,
	isForcePrint?: boolean,
	historySids?: string[]
	showCombinationInfo?: boolean
	printFrom?: string // 来源页面
}) => {
	let isError = false;
	let orderList = packages.map((pack) => {
		let sellerMemo: string[] = [];
		let buyerMemo: string[] = [];
		let sysMemos: string[] = [];
		let isFenXiao = 0;
		let totalWeight = 0;
		let totalFee = 0;
		let totalCount = 0;
		let totalDiscount = 0;
		let totalAdjustAmount = 0;
		let totalPostFee = 0; // 合计邮费
		let totalPayAmount = 0;
		let tableDataArray: any[] = [];
		let oids = [];
		let ordersInfo: any[] = [];
		let transportTimeList: string[] = [];
		let errorRepeatTid = null;
		let tids: string[] = [];
		let outerTradeId: string[] = [];
		let tidOids: string[] = [];
		let tradeReceiverList: any[] = [];
		let sidTrades: any[] = [];
		let isPartialPrint: boolean = false;
		let tidWithOids: string[] = [];
		let ptTidWithOids: string[] = [];
		let tidOidsObj: object = {};
		let ordersArr: any[] = [];
		// 赠品数量
		let goodsNumGift: number = 0;
		let receiverIds: any[] = [];
		let scmTradeEncryptOutOrderInfoMap = {};
		let sysMemoPic: string = '';
		let printFlag: boolean = false;
		let isAllCheck: boolean = false;
		let tradeEncodeType: any = (pack?.trades || [])[0]?.tradeEncodeType || null;
		let totalRefundPayment: number = 0;
		let dwOrderType = 0; // 1指定快递，2普通履约 一品多仓
		let waveNoList = [];
		let serviceTagList = Object.values(pack.serviceTagList || {}) || [];
		if (serviceTagList?.includes('promiseLogistics')) {
			dwOrderType = 1;
		} else if (serviceTagList?.includes('ordinaryOrder') || serviceTagList?.includes('warehouseOrder')) {
			dwOrderType = 2;
		}
		// 是否分销回流订单
		// let isScmHl = pack.hlPlatformType && pack.source === 'SCM';

		// 送货上门的标记
		let isDeliveryOnDoorTis = false;
		pack.trades?.forEach((trade) => {
			let orderPayment: number = 0;
			// 合单情况 如果该订单下的任意一个商品勾选，就需要处理该订单
			if (!trade.orders.some((i) => i.isChecked)) {
				isPartialPrint = true;
				return;
			}
			tidOidsObj[trade.tid] = [];
			tidOids.push(trade.tid);
			if (trade.platform === 'sph') receiverIds.push(`${trade.tid}:${trade.receiverId}`);
			// if (!pack.isDecrypted) {

			const ReceiverListData = {
				tid: trade.tid,
				receiverTel: pack.isDecrypted ? pack.receiverPhone : trade.receiverPhone,
				receiverMobile: pack.isDecrypted ? pack.receiverMobile : trade.receiverMobile,
				receiverName: pack.isDecrypted ? pack.receiverName : trade.receiverName,
				receiverAddress: pack.isDecrypted ? pack.receiverAddress : trade.receiverAddress,
				caid: trade.caid,
				ptTid: trade.ptTid,
				distributorTid: trade.distributorTid || '',
				// 京东送货上门
				jdDeliveryOnDoorOption: trade?.jdDeliveryOnDoorOption,

			};
			tradeReceiverList.push(ReceiverListData);
			// }
			if (trade.sellerMemo) {
				sellerMemo.push(trade.sellerMemo);
			}
			if (String(trade.isPrintKdd) === '1') {
				printFlag = true;
			}
			if (trade.sysMemo) {
				sysMemos.push(trade.sysMemo);
			}
			// 扫描打印 买家留言
			if (printFrom === 'scanPrintKdd' && trade.buyerMessage) {
				buyerMemo.push(trade.buyerMessage);
			} else if (trade._buyerMessage) {
				buyerMemo.push(trade._buyerMessage);
			}

			sidTrades.push({
				caid: trade.caid || trade.openAddressId,
				tid: trade.tid,
			});

			const checkOrders = trade.orders.filter((order) => order.isChecked);
			if (checkOrders && checkOrders.length) {
				// 子单全选
				isAllCheck = checkOrders?.length === trade?.orders?.length;
				let curOids: string[] = [];
				let curPtOids: string[] = []; // 系统单号

				totalFee += parseFloat(trade.totalFee || '') + parseFloat(trade.postFee || '');
				if (trade.fenxiaos) {
					isFenXiao = 1;
				}
				checkOrders.forEach((order) => {
					if (showCombinationInfo && order?.groupRelationRecordList.length) {
						order.groupRelationRecordList.forEach(item => {
							let o: any = {};
							o.tb_pid = order.numIid || '';
							o.tb_oid = order.oid;
							o.tb_img = order.picPath || '';
							o.tb_tit = order.title || '';
							o.tb_jc = order.titleShort || o.tb_tit; // 简称
							o.tb_sjbm_b = order.outerId || ''; // 宝贝商家编码
							o.tb_huohao = order.productNo || ''; // 货号
							o.tb_isbn = order.isbn || ''; // ISBN编码
							o.tb_cpggAll = order.skuPropertiesFilter || order.skuPropertiesName || ''; // 产品规格 未过滤的
							o.tb_cpggAlias = order.skuAlias || o.tb_cpggAll; // 宝贝规格别名
							o.tb_cpgg = o.tb_cpggAll || ''; // 产品规格  目前这两个字段打印出来的都是产品规格全称。
							o.tb_count = (item.groupProportionNum || 0) * (order.num || 0); // 产品数量
							o.tb_dj = order.price || 0.0; // 产品单价
							o.tb_yh = Math.abs(order.discount) + Math.abs(order.adjustAmount) || 0; // 产品优惠
							o.tb_sf = order.payment || 0; // 实付金额
							o.tb_fx_sf = (trade.fenxiaos ? order.totalFee : ''); // 分销实付金额  TODO
							o.zhongl = +item.weight * item.groupProportionNum; // 重量 TODO
							o.tb_weight = +item.weight * item.groupProportionNum; // 重量 TODO
							o.tb_sjbm_kh = order.outerSkuId || ''; // 款号商家编码
							o.tb_hpsjbm = item.skuOuterId || ''; // 货品商家编码
							// o.tb_bdhp_mc = order.sysItemName || ''; // 货品名称
							o.tb_bdhp_jc = item.sysItemAlias || ''; // 货品简称
							o.tb_bdhp_sjbm = item.skuOuterId || ''; // 发货单货品商家编码
							o.tb_itemNo = item.itemNo || ''; // 货号
							o.tb_hpggbm = item.sysSkuAlias || ''; // 货品规格别名
							o.tb_spyjje = (order.price * order.num) || '0'; // 商品总金额
							o.tb_color = item.color || ''; // 颜色
							o.tb_size = item.size || ''; // 尺码
							o.tb_market = item.market || '';
							o.tb_stall = item.stall || '';
							o.tb_supplier = item.supplierName || '';
							o.tb_zp = item.isGift ? '赠品' : '';
							o.tb_hpggmc = item.sysSkuName || '';
							o.tb_skuName = item.skuPropertiesName || '';
							o.tb_warehouseSlotName = item.warehouseSlotName || '';
							o.tb_tb_txm_txt = item.barCode || '';
							tableDataArray.push(o);
							totalCount += Number(o.tb_count);
						});
					} else {
						let o: any = {};
						o.tb_pid = order.numIid || '';
						o.tb_oid = order.oid;
						o.tb_img = order.picPath || '';
						o.tb_tit = order.title || '';
						o.tb_jc = order.titleShort || o.tb_tit; // 简称
						o.tb_sjbm_b = order.outerId || ''; // 宝贝商家编码
						o.tb_huohao = order.productNo || ''; // 货号
						o.tb_isbn = order.isbn || ''; // ISBN编码
						o.tb_cpggAll = order.skuPropertiesFilter || order.skuPropertiesName || ''; // 产品规格 未过滤的
						o.tb_cpggAlias = order.skuAlias || o.tb_cpggAll; // 宝贝规格别名
						o.tb_cpgg = o.tb_cpggAll || ''; // 产品规格  目前这两个字段打印出来的都是产品规格全称。
						o.tb_count = order.num || 0; // 产品数量
						o.tb_dj = order.price || 0.0; // 产品单价
						o.tb_yh = Math.abs(order.discount) + Math.abs(order.adjustAmount) || 0; // 产品优惠
						o.tb_sf = order.payment || 0; // 实付金额
						o.tb_fx_sf = (trade.fenxiaos ? order.totalFee : ''); // 分销实付金额  TODO
						o.zhongl = order.weight; // 重量 TODO
						o.tb_weight = order.weight; // 重量 TODO
						o.tb_sjbm_kh = order.outerSkuId || ''; // 款号商家编码
						o.tb_color = order.color || ''; // 颜色
						o.tb_size = order.size || ''; // 尺码
						o.tb_hpsjbm = order.sysOuterSkuId || ''; // 货品商家编码
						// o.tb_bdhp_mc = order.sysItemName || ''; // 货品名称
						o.tb_bdhp_jc = order.titleShort || ''; // 货品简称
						o.tb_bdhp_sjbm = order.sysOuterSkuId || ''; // 发货单货品商家编码
						o.tb_itemNo = order.itemNo || ''; // 货号
						o.tb_hpggbm = order.skuAlias || ''; // 货品规格别名
						o.tb_spyjje = (order.price * order.num) || '0'; // 商品总金额
						o.tb_market = order.market || '';
						o.tb_stall = order.stall || '';
						o.tb_supplier = order.supplierName || '';
						o.tb_zp = order.isGift ? '赠品' : '';
						o.tb_hpggmc = order.sysSkuName || '';
						o.tb_skuName = order.skuPropertiesName || ''; // 新的规格名称取值字段，暂时仅用于发货单小标签
						o.tb_warehouseSlotName = order.warehouseSlotName || '';
						o.tb_tb_txm_txt = order.barCode || '';
						tableDataArray.push(o);
						totalCount += Number(order.num);
					}
					ordersArr.push({
						...order,
						splitTradeType: trade.splitTradeType,
					});
					oids.push(order.oid);
					// if (order.waveNo) waveNoList.push(order.waveNo);
					tidOidsObj[trade.tid].push({
						firstSend: order.firstSend,
						oid: order.oid,
						labelStatus: order.labelstatus
					});
					curOids.push(order.oid);
					curPtOids.push(order.ptOid);
					// 计算赠品数量
					if (order.isGift) goodsNumGift += Number(order.num);
					ordersInfo.push({
						name: order.title || '商品', // 手工单可以不需要商品，默认传商品
						count: order.num == "0" ? "1" : order.num, // 手工单可以不需要商品，但是count不能为0
						short: order.titleShort,
						skuId: order.skuId,
						numIid: order.numIid
					});
					if (!trade.discount) {
						totalDiscount += Math.abs(order.discount || 0);
					}
					totalAdjustAmount += Math.abs(order.adjustAmount || 0); // 调整金额  负数全部转化为正数
					totalWeight += parseFloat(order.weight || "0");
					if (['REFUND_SUCCESSED', 'REFUND_ING'].includes(order.refundStatus)) {
						totalRefundPayment += Number(order.payment);
					} else {
						orderPayment += Number(order.payment);
					}
				});

				if (trade.discount) {
					totalDiscount += Number(Number(trade.discount).toFixed(2));
				}
				totalPayAmount += Number(orderPayment);
				totalPostFee += Number(trade.postFee);
				transportTimeList.push(trade.transportDay);


				// 所有商品都被选中的话，无需传入oids
				if (checkOrders.length === trade.orders.length) {
					tidWithOids.push(`${trade.tid}`);
					ptTidWithOids.push(`${trade.ptTid}`);
				} else {
					tidWithOids.push(`${trade.tid}:${curOids.join(',')}`);
					ptTidWithOids.push(`${trade.ptTid}:${curPtOids.join(',')}`);
				}
			}
			if (tids.includes(trade.tid)) {
				errorRepeatTid = trade.tid;
			}
			// 判断是否送货上门
			if (trade.hasDeliveryOnDoor) {
				isDeliveryOnDoorTis = true;
			}
			tids.push(trade.tid);
			if (trade.distributorTid) outerTradeId.push(trade.distributorTid);

			// 线下备注图片取第一个值
			if (trade.sysMemoPic && !sysMemoPic) {
				sysMemoPic = (trade.sysMemoPic.split(',') || [])[0];
			}
			// 组装分销回流下游平台数据
			// if (isScmHl) {
			scmTradeEncryptOutOrderInfoMap[trade.tid] = {
				downstreamEncryptDetails: trade.downstreamEncryptDetails,
				isEncrypted: trade.hlEncryptOrder,
			};
			// }

		});
		if (errorRepeatTid) {
			isError = true;
			// showFail(`订单${errorRepeatTid}数据异常，请刷新后重试，或联系客服`);
		}
		let orderState = pack.trades?.map((trade) => ({
			[trade.tid]: trade.status,
		}));
		let receiverName = filterUdbUdc(pack.receiverName);
		const firstTrade = pack.trades ? pack.trades[0] : {};

		let senderInfo = {};
		if (firstTrade.senderName) {
			senderInfo = {
				f_address: firstTrade.senderAddress,
				f_city: firstTrade.senderCity,
				f_district: firstTrade.senderCounty,
				f_mobile: firstTrade.senderMobile,
				f_name: firstTrade.senderName,
				f_tel: firstTrade.senderPhone,
				f_province: firstTrade.senderProvince
			};
		}
		// 如果是手工单代收货款。需要取waitReceiveAmount
		let isCOD = pack.isCod;
		let codPrice = (pack.isCod ? totalPayAmount.toFixed(2) : 0);
		if (['HAND', 'SCMHAND'].includes(pack.source) && Number(pack.waitReceiveAmount || 0)) {
			isCOD = true;
			codPrice = pack.waitReceiveAmount;
		}
		// 京东全选子单，代收取主单
		if (isAllCheck && pack.platform === 'jd') {
			codPrice = (pack.isCod ? pack.totalPayment : 0);
		}

		let transformId = isNaN(Number(pack.userId)) ? pack.sellerId : pack.userId;

		let formatData: any = {
			// 批打波次号
			// waveNo: waveNoList.join(','),
			tids: pack.tids,
			ptTids: pack.ptTids,
			base64Url: pack.base64Url || '',
			splitTradeType: pack.splitTradeType || '',
			ydNo: pack.ydNo || '',
			dwOrderType,
			orderState,
			printFlag,
			sysMemoPic,
			outerTradeId, // 订单原始单号
			ordersArr, // 吊牌所需要的商品信息
			distributorUserId: pack.distributorUserId || "", // 分销商userID
			// 视频号分店铺使用
			memberId: pack.memberId || "",
			receiverId: receiverIds,
			// 配送时间,取第一个
			transport_time: transportTimeList[0],
			tidOids, // 单打不支持一个包裹的拆单打印，无论勾选与否，默认打印该包裹下的所有订单的信息
			tidOidsObj,
			tradeReceiverList,
			seller_nick: pack.sellerNick || "无店铺",
			platform: pack.platform,
			// 从库userId
			userId2: transformId,
			// 主库userId
			userId: transformId,
			// caid 淘宝试用
			caid: pack.caid || pack.openAddressId,
			// 收件人
			s_name: receiverName,
			s_name_origin: pack.receiverNameMask,
			s_wangwang: receiverName,
			buyerNick: pack.buyerNick,
			buyerNick_origin: pack.receiverNameMask,
			buyerOpenUid: pack.buyerOpenUid,
			// 收件人电话
			s_phone: pack.receiverPhone,
			// 收件人手机
			s_mobile: pack.receiverMobile,
			s_mobile_origin: pack.receiverPhoneMask || pack.receiverMobileMask,
			// 收件人邮编
			s_zip: pack.receiverZip,
			// 收件人省
			s_province: pack.receiverState,
			// 收件人城市
			s_city: pack.receiverCity,
			// 收件人区
			s_county: pack.receiverDistrict,
			// 收件人乡镇
			s_town: pack.receiverTown,
			// 收件人地址
			s_address: pack.receiverAddress,
			s_address_origin: pack.receiverAddressMask,
			// 发票
			invoice: "",
			// 订单编号
			s_tid: firstTrade.tid,
			// 满就送
			mjs: "",
			// 买家备注
			buyerMome: buyerMemo.join(";"),
			// 卖家备注
			sellerMome: sellerMemo.join(";") || pack.sellerMemo || "",
			// 卖家备注旗帜  第一单为主
			sellerFlag: firstTrade.sellerMemoFlag,
			// 打印内容
			print_content: pack.printContent || pack.info || pack.deliveryInfo,
			// 数量
			count: pack.trades ? totalCount : pack.goodsNum,
			// 是否货到付款
			isCOD,
			// 货到付款/代收金额
			codPrice,
			// 货款总计
			hkzj: totalFee.toFixed(2),
			hj_youhui_fee: totalDiscount + totalAdjustAmount,
			// 合计运费
			hj_post_fee: totalPostFee,
			// 合计重量
			log_weight: totalWeight,
			hj_weight: totalWeight,
			zhliang: totalWeight,
			totalWeight: isKg
				? formatDecimal(totalWeight / 1000, 6)
				: formatDecimal(+totalWeight || 0, 3),
			// 店铺名称
			shopName: receiverName,
			// 订单创建时间 第一单
			orderCreateTime: firstTrade.createTime,
			// 支付时间 第一单
			payTime: firstTrade.payTime,
			// 到达网点hdn_ddwd
			ddwd: "",
			// 是否分销
			isfenxiao: isFenXiao,
			packageId: -1,
			// 合单号
			togetherId: pack.togetherId,
			// 运单号
			exnumber: pack.exNumber,
			sidNos: pack.sids,
			// 商品信息
			orders: ordersInfo,
			// 发货单的表格信息
			tableDataArray,
			hasRefund: pack.hasRefund,
			// 发货单是否已打印
			hasPrintedFhd: pack.shipListPrintStatus !== "none",
			// 快递单是否已打印
			hasPrintedKdd: pack.waybillPrintStatus !== "none",
			// 店铺id
			sellerId: pack.sellerId,
			// 快递单打印标记
			waybillPrintStatus: pack.waybillPrintStatus,
			// 发货单打印标记
			shipListPrintStatus: pack.shipListPrintStatus,
			goodsNumGift,
			// sidTrades
			sidTrades,

			// 跨境订单标识

			crossBorder: !!pack.crossBorder,

			// ERP特殊字段
			idxEncodeReceiverMobile: pack.idxEncodeReceiverMobile,
			idxEncodeReceiverAddress: pack.idxEncodeReceiverAddress,
			idxEncodeReceiverName: pack.idxEncodeReceiverName,

			receiverNameMask: pack.receiverNameMask,
			receiverPhoneMask: pack.receiverPhoneMask,
			receiverMobileMask: pack.receiverMobileMask,
			receiverAddressMask: pack.receiverAddressMask,

			receiverMobile: pack.receiverMobile,
			receiverName: pack.receiverName,
			receiverAddress: pack.receiverAddress,
			receiverTown: pack.receiverTown,

			encodeTid: pack.encodeTid,

			isDecrypted: ((isSourceHand(pack) && tradeEncodeType != 2) || pack.platform === 'ktt') ? 1 : pack.isDecrypted ?? 0,
			// 实付金额，字段兼容
			payment: totalPayAmount.toFixed(2),
			payAmount: totalPayAmount.toFixed(2),
			// 实收金额
			receivedPayment: pack?.totalReceivedPayment || 0,
			// 总实付
			totalPayment: pack?.totalPayment || 0,
			// 退款总实付
			totalRefundPayment,
			labelIds: pack.labelIds,
			// 部分打印
			isPartialPrint,
			tidWithOids: pack.needPrintTid || tidWithOids,
			// 系统单号
			ptTidWithOids,
			// 订单来源
			source: pack.source,
			ptType: pack.ptType,
			// 1688回流数据
			// tradeEncryptOutOrderInfoMap: isScmHl ? scmTradeEncryptOutOrderInfoMap : pack.tradeEncryptOutOrderInfoMap,
			tradeEncryptOutOrderInfoMap: scmTradeEncryptOutOrderInfoMap,
			hlPlatformType: pack.hlPlatformType,
			hlEncryptOrder: pack.hlEncryptOrder,
			fhdRuleSortData, // 发货单表格排序方法
			consolidateType: pack.consolidateType, // 抖音订单转运标识
			urge: pack.urge == 1 ? '催发货' : '',
			sysMemo: pack._sysMemo ? pack._sysMemo : (sysMemos?.length ? sysMemos.join(';') : ''), // 线下备注
			noContainGiftsNum: pack._noContainGiftsNum || '', // 商品数量不含赠品
			// 送货上门的信息
			isDeliveryOnDoorTis,
			// 订单数据加密类型
			tradeEncodeType,
			realTid: (pack?.trades || [])[0]?.realTid || null,
			// 增加订单线上已发货标签字段
			onlineShip: pack.onlineShip,
			// 增加发货状态字段
			sendStatus: pack.status,
			// 订单标签是否先发货
			firstSend: pack.firstSend,
			isAllCheck, // 是否全选完整订单
		};

		if (isForcePrint) {
			// 强制补打，传递历史运单号，非强制补打，不需要传递
			formatData["sidList"] = historySids;
		}

		if (isSourceHand(pack, true)) {
			formatData = {
				...formatData,
				...senderInfo
			};
		}

		// 底单重打
		if (pack.exName && !isScan) {
			let uniqueTids = [];
			if (pack.pickItemList?.length) {
				// 收集所有item的tid并去重
				let tidSet = new Set();
				pack.pickItemList.forEach((item: any) => {
					if (item.tid) {
						tidSet.add(item.tid);
					}
					if (tidOidsObj[item.tid]) {
						tidOidsObj[item.tid].push({
							oid: item.oid,
						});
					} else {
						tidOidsObj[item.tid] = [{
							oid: item.oid,
						}];
					}

				});
				// 转换为数组
				uniqueTids = Array.from(tidSet);

			}
			formatData = {
				...formatData,
				kdId: pack.exId,
				kdName: pack.exName,
				kdCode: pack.exCode,

				exId: pack.exId,
				userTemplateId: pack.userTemplateId,
				exName: pack.exName,
				exCode: pack.exCode,

				tids: pack.orderIds,
				ptTids: pack.ptOrderIds,
				encodeTid: pack.encodeTid,

				ydNo: pack.exNumber,
				sellerMome: pack.sellerMemo,
				buyerMome: pack.buyerMsg,
				receiverAddress: pack.receiverAddress,
				receiverTown: pack.receiverTown,
				receiverCity: pack.receiverCity,
				receiverCounty: pack.receiverCounty,
				receiverName: pack.receiverName,
				receiverProvince: pack.receiverProvince,
				receiverMobile: pack.mobile,
				receiverTel: pack.mobile,
				totalWeight: isKg ? (+pack.weight / 1000).toFixed(6) : (+pack.weight || 0).toFixed(3),
				printFlag: Number(pack.printStatus) === 1,
				// 混合打印
				boxNo: pack.boxNo,
				waveNo: pack.waveNo,
				tidOids: uniqueTids,
				tidOidsObj,
				tidWithOids: pack?.orderIds?.split(','),
				ptTidWithOids: pack?.ptTids?.split(','),
				packageNo: pack.packageNo,
				senderCity: pack.senderCity,
				senderCounty: pack.senderCounty,
				senderProvince: pack.senderProvince,
				senderAddress: pack.senderAddress,
				senderName: pack.senderName,
				senderMobile: pack.senderMobile,
				senderPhone: pack.senderPhone,

			};
			// 波次打印需要orderIds
			console.log('formatData', formatData);
		}
		// TODO 发货单收件人单独处理，后续使用高级设置在做处理
		if (isGetFhdInfo) {
			// 收件人
			formatData.s_name = pack.receiverNameMask;
			formatData.s_wangwang = pack.receiverNameMask;
			// formatData.buyerNick = pack.receiverNameMask;
			// 收件人电话
			formatData.s_phone = pack.receiverPhoneMask;
			// 收件人手机
			formatData.s_mobile = pack.receiverMobileMask;
			// 收件人地址
			formatData.s_address = pack.receiverAddressMask;
		}

		console.log(formatData, "--------------forem");

		return formatData;
	});
	return {
		isError,
		orderList,
	};
};

/**
 * 格式化小数，去除末尾的0
 * @param value 原始数值
 * @param maxDecimal 最大小数位数
 */
function formatDecimal(value: number, maxDecimal: number): string {
	// 转换为字符串
	const strValue = value.toString();

	// 如果没有小数点，直接返回
	if (!strValue.includes('.')) {
		return strValue;
	}

	// 分割整数和小数部分
	const [integerPart, decimalPart] = strValue.split('.');

	// 处理小数部分
	let trimmedDecimal = decimalPart;
	// 去除末尾的0
	trimmedDecimal = trimmedDecimal.replace(/0+$/, '');
	// 限制最大小数位数
	if (trimmedDecimal.length > maxDecimal) {
		trimmedDecimal = trimmedDecimal.substring(0, maxDecimal);
	}

	// 如果小数部分为空，只返回整数部分
	return trimmedDecimal.length > 0
		? `${integerPart}.${trimmedDecimal}`
		: integerPart;
}
