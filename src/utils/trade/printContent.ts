import { cloneDeep, isEmpty } from "lodash";
import { TradePrintContentSetRule } from "@/types/trade/tradeSet";
import { IOrders, IPackage, IGroupRelationRecordList } from "@/pages/Trade/interface";
import userStore from "@/stores/user";
import { PRINT_CONTENT_MAX_LENGTH, PRINT_CONTENT_MAX_ROW } from "@/pages/Trade/constants";
import { CombinationShowEnum, ItemSortConfig, ItemSortConfigEnum, ItemSortRuleEnum, MarketStallSupplierEnum, PrintSetXBreakLine, SkuSetFrontBack, isTogetherEnum } from "@/pages/Trade/components/ListItem/components/PrintStyleSetting/constant";
import tradeSetStore from "@/stores/trade/tradeSet";
import { modalFilterPatternList } from "@/pages/Trade/components/ListItem/components/FilterKeyWordModal";

function filterString(key, str) {
	const hasPatternList = modalFilterPatternList.filter((item) => { return item.value == key; });
	if (!hasPatternList.length) return str;
	// 中英文（）特殊处理
	if (key === '()') {
		const regex = new RegExp(`\\（(.*?)\\）`, "g");
		str = str.replaceAll(regex, "");
	}
	const regex = new RegExp(`\\${key.charAt(0)}(.*?)\\${key.charAt(1)}`, "g");
	const _str = str.replaceAll(regex, "");
	return _str;
}

// 过滤打印内容
export function filterPrintContent(str: string, keywords?: string[]) {
	let words: string[] = [];
	let reg;

	str = str || '';
	if (!keywords) keywords = tradeSetStore.getFilterKeyWords();
	const filterPatternList = tradeSetStore.getFilterPatternList();
	if (!keywords?.length && !filterPatternList?.length) return str;
	keywords?.forEach(keyword => {
		if (keyword == '') return;
		// 星号 竖线 下划线 小括号，中括号 大括号 斜线 反斜线 冒号 点号 问号
		// eslint-disable-next-line no-useless-escape
		words.push(keyword.replace(/\^|\$|\?|\+|\(|\)|\.|\:|\[|\]|\*|\||\{|\}|\\/g, function(word) {
			return '\\' + word;
		}));
	});
	filterPatternList?.forEach((key) => {
		str = filterString(key, str);
	});
	reg = new RegExp(words.join('|'), 'g');
	str = str.replace(reg, '');
	return str;
}


function getDefaultRule(): TradePrintContentSetRule {
	return {
		extJson: {
			changeLineConfig: false,
			changeLineGoodsNum: 1,
			childGoodsShowList: '',
			showMarketStall: '1,2,3',
			skuSetFrontBack: SkuSetFrontBack.显示在规格前,
			showCombinationIcon: false,
			itemSortConfig: 1,
			itemSortRule: ItemSortRuleEnum['按商家编码+规格编码升序'],
			showNumFlag: true,
			showType: 1,
			showContent: "",
		},
		'breakline': 2,
		'breaklineNum': 5,
		'goodsNumDisplay': 3,
		'isShowUnitySet': false,
		'isTogether': 1,
		'displayPrintOrderNum': false,
		'isUnityStyle': false,
		'showOrderPrice': false,
		'showSkuAlias': false,
		'showSkuAliasEmpty': false,
		'tradeReward': 1,
		'unitStyle': 0,
		'useOuterIid': false,
		'useOuterIidEmpty': false,
		'useRuleOutIid': false,
		'useRuleOutIidEmpty': false,
		'useShortName': true,
		'useShortNameEmpty': 0,
		'useSkuName': true,
		'useTitle': false,
		'useStall': 0,
		'useSysOuterId': 0,
		'useSysOuterSkuId': 0,
		'printGiftOrder': true,
	};
}
// 数量样式
function getGoodsNumDisplay(rule, num) {
	switch (rule.goodsNumDisplay) {
		case 1:
			return num;
		case 2:
			return '-' + num;
		case 3:
			return '[' + num + ']';
		case 4:
			return '(' + num + ')';
		case 5:
			return '/' + num;
		case 6:
			return '【' + num + '】';
		case 7:
			return ' ' + num;
		case 8:
			return '*' + num;
		default:
			return '' + num;
	}
}

// 数量单位
function getUnitStyle(rule) {
	switch (rule.unitStyle) {
		case 1:
			return '件';
		case 2:
			return '个';
		case 3:
			return '包';
		case 4:
			return '台';
		case 5:
			return '份';
		case 6:
			return '只';
		case 7:
			return '条';
		case 8:
			return '盒';
		case 9:
			return '双';
		case 9999:
			return rule.extJson.customUnitType;
		default:
			return '';
	}
}

// 按规格编码合并数量
function mergeChildListTogether(childList) {
	const map = {};
	childList.forEach(childItem => {
		if (!map[childItem.skuOuterId]) {
			map[childItem.skuOuterId] = childItem;
		} else {
			map[childItem.skuOuterId].calcNum += childItem.calcNum;
		}
	});
	return Object.values(map);
}

// 组合商品的子商品显示
function dealChild(contents, u, rule, separator) {
	let childGoodsShowList = rule?.extJson?.childGoodsShowList ?? '';
	const useSkuOuterId = childGoodsShowList.includes('1'); // 货品规格编码
	const useSysSkuName = childGoodsShowList.includes('2'); // 货品规格名称
	const useSysSkuAlias = childGoodsShowList.includes('3'); // 货品规格别名
	let childList = u.flatMap(i => i.groupRelationRecordList.map(j => ({ ...j, orderNum: i.num })));

	childList.forEach(i => {
		i.calcNum = (i.groupProportionNum * i.orderNum) || 0;
	});
	if (rule.isTogether == 1) {
		childList = mergeChildListTogether(childList);
	}

	if (rule?.extJson?.itemSortConfig === ItemSortConfigEnum.自定义排序规则) {
		sortChildItemByRule(childList, rule?.extJson?.itemSortRule);
	}

	childList.forEach(childItem => {
		let temp = ['(子)'];

		if (useSkuOuterId) {
			temp.push(childItem.skuOuterId);
		}
		if (useSysSkuName) {
			if (useSkuOuterId) temp.push(separator);
			temp.push(childItem.sysSkuName);
		}
		if (useSysSkuAlias) {
			if (useSysSkuName) temp.push(separator);
			temp.push(childItem.sysSkuAlias);
		}
		const calNum = childItem.calcNum;
		let ItemNum = getGoodsNumDisplay(rule, calNum);
		if (ItemNum) temp.push(ItemNum);

		let numUnit = getUnitStyle(rule);
		if (numUnit) temp.push(numUnit);
		temp.push(' ');
		temp.length > 1 && contents.push(temp.join('').replace(/ /g, '#whitespace#'));
	});
}

// 在规格前后添加市场档口供应商
function getMarketStallSkuTemp(skuTemp, rule, uItem) {
	const showMarketStall = rule?.extJson?.showMarketStall ?? '';
	const isMarket = showMarketStall.includes(MarketStallSupplierEnum.市场);
	const isStall = showMarketStall.includes(MarketStallSupplierEnum.档口);
	const isSupplier = showMarketStall.includes(MarketStallSupplierEnum.供应商);
	let tmp = [];
	if (isMarket) {
		tmp.push(uItem.market);
	}
	if (isStall) {
		tmp.push(uItem.stall);
	}
	if (isSupplier) {
		tmp.push(uItem.supplierName);
	}
	if (!tmp.length) {
		return skuTemp;
	}

	let str = ` ${tmp.join(' ')} `;

	if (rule?.extJson?.skuSetFrontBack == SkuSetFrontBack.显示在规格后) {
		return skuTemp + str;
	} else {
		return str + skuTemp;

	}
}

interface GatherPrintContentParams {
	obj: {
		subOrders: IOrders[],
		togetherId: string,
	},
	rule: TradePrintContentSetRule,
	keywords: string[],
	advancedSetRes?: any,
	printSet?: {
		kddSortType?: string
	}
}

const getOrderInfo = (o: IOrders, isGroupRelationRecordList = false) => {
	if (isGroupRelationRecordList) {
		return {
			itemNo: o.itemNo,
			isCombination: o.isCombination,
			groupRelationRecordList: o.groupRelationRecordList,
			isGift: o.isGift,
			title: o.sysItemAlias,
			titleShort: o.sysItemAlias,
			num: Number(o.num) || 0,
			numIid: o.sysItemId,
			outerIid: o.outerId,
			skuId: o.sysSkuId,
			skuAlias: o.sysSkuAlias,
			originSkuName: o.sysSkuName,
			skuPropertiesName: filterPrintContent(o.sysSkuName),
			outerSkuId: o.outerSkuId,
			price: o.price || 0,
			stall: o.stall,
			market: o.market,
			supplierName: o.supplierName,
			isNoOrder: o.numIid == '-1', // 是否是无商品
			handShipInfo: o.shipInfo || '', // 手工订单无商品，发货内容
			sysOuterId: o.outerId || '',
			sysOuterSkuId: o.skuOuterId || '',
			warehouseSlotName: o.warehouseSlotName || '',
		};
	}
	return {
		itemNo: o.itemNo,
		isCombination: o.isCombination,
		groupRelationRecordList: o.groupRelationRecordList,
		isGift: o.isGift,
		title: o.title,
		titleShort: o.titleShort,
		num: Number(o.num || 0) || 0,
		numIid: o.numIid,
		outerIid: o.outerId,
		skuId: o.skuId,
		skuAlias: o.skuAlias,
		originSkuName: o.skuPropertiesName,
		skuPropertiesName: filterPrintContent(o.skuPropertiesName),
		outerSkuId: o.outerSkuId,
		price: o.price || 0,
		stall: o.stall,
		market: o.market,
		supplierName: o.supplierName,
		isNoOrder: o.numIid == '-1', // 是否是无商品
		handShipInfo: o.shipInfo || '', // 手工订单无商品，发货内容
		sysOuterId: o.sysOuterId || '',
		sysOuterSkuId: o.sysOuterSkuId || '',
		warehouseSlotName: o.warehouseSlotName || '',
	};
};

// 组织打印内容数据
export function gatherPrintContent(params: GatherPrintContentParams) {
	let {
		obj,
		rule,
		keywords,
		advancedSetRes,
		printSet = {}
	} = params;

	if (isEmpty(rule)) {
		rule = getDefaultRule();
	}
	if (isEmpty(keywords)) {
		keywords = [];
	}

	// 发货内容宝贝生成顺序设置 二期完善，本期先
	// let _printSet = {};

	// 获取当前宝贝显示字段的序列
	const getGoodsFieldIndex = function(field: string) {
		const sequence = (rule.showSequence || 'outerIid,titleShort,title,stall').split(',');
		// 遇到无法识别的默认返回 4
		const resIndex = sequence.indexOf(field) >= 0 ? sequence.indexOf(field) : 4;
		return resIndex;
	};
	// 获取当前规格字段的序列
	const getSkuFieldIndex = function(field: string) {
		const sequence = (rule.showSequenceSku || 'outerSkuId,skuPropertiesName,skuAlias').split(',');
		// 遇到无法识别的默认返回 3
		const resIndex = sequence.indexOf(field) >= 0 ? sequence.indexOf(field) : 4;
		return resIndex;
	};

	// 获取当前宝贝显示字段的序列
	const getContentFieldIndex = function(field: string) {
		const sequence = rule?.extJson?.showContent?.split(',');
		// 遇到无法识别的默认返回 4
		const resIndex = sequence.indexOf(field) >= 0 ? sequence.indexOf(field) : rule?.extJson?.showContent?.length;
		return resIndex;
	};

	// 发货单相同商品合并 把相同商品挨在一起, 对商品进行排序
	// if (obj.subOrders && obj.subOrders.length) {
	// 	if (printSet.kddSortType == '1') {
	// 		obj.subOrders.sort(function(a, b) {
	// 			if (a.outerId < b.outerId) {
	// 				return -1;
	// 			}
	// 			if (a.outerId > b.outerId) {
	// 				return 1;
	// 			}
	// 			return 0;
	// 		});
	// 	} else if (printSet.kddSortType == '2') {
	// 		obj.subOrders.sort(function(a, b) {
	// 			return (a.titleShort || a.title || '').localeCompare(b.titleShort || b.title || '');
	// 		});
	// 	} else if (printSet.kddSortType == '3') {
	// 		obj.subOrders.sort(function(a, b) {
	// 			if (a.outerSkuId < b.outerSkuId) {
	// 				return -1;
	// 			}
	// 			if (a.outerSkuId > b.outerSkuId) {
	// 				return 1;
	// 			}
	// 			return 0;
	// 		});
	// 	} else if (printSet.kddSortType == '4') {
	// 		// 档口排序
	// 		const emptyValueArr: IOrders[] = []; const
	// 			fullValueArr: IOrders[] = [];
	// 		obj.subOrders.forEach(item => {
	// 			if (item.stall || item.stall === 0) {
	// 				fullValueArr.push(item);
	// 			} else {
	// 				emptyValueArr.push(item);
	// 			}
	// 		});
	// 		obj.subOrders = fullValueArr.sort(function(a, b) {
	// 			if (a.stall < b.stall) {
	// 				return -1;
	// 			}
	// 			if (a.stall > b.stall) {
	// 				return 1;
	// 			}
	// 			return 0;
	// 		}).concat(emptyValueArr);
	// 	}
	// }

	// 1、处理出基本的数据结构，所有可能会展示的字段
	/* {
		title: '',
		titleShort: '',
		outerIid: '商家外部编码',
		skuPropertiesName: '规格名称',
		outerSkuId: '规格对应的商家编码',
		sysOuterSkuId: 货品规格编码,
		sysOuterId: 货品编码
	}*/
	let exchange: any[] = [];
	let same; // [[],[]]
	let union = [];
	let k;
	let sameSku;
	if (obj.subOrders.length > PRINT_CONTENT_MAX_ROW) {
		obj.subOrders = obj.subOrders.splice(0, PRINT_CONTENT_MAX_ROW);
	}
	const groupRelationRecordList = [];
	obj.subOrders.forEach((o) => {
		let item;
		item = getOrderInfo(o);
		if (advancedSetRes?.showCombinationInfo === 2 && rule?.extJson?.showCombinationInfoType === 2 && o.isCombination == 1) {
			for (let i = 0; i < o.num; i++) {
				if (rule.isTogether == isTogetherEnum.不合并打印) {
					o.groupRelationRecordList.forEach(i => {
						for (let j = 0; j < i.groupProportionNum; j++) {
							groupRelationRecordList.push(getOrderInfo({ ...i, num: 1 }, true));
						}
					});
				} else {
					groupRelationRecordList.push(...o.groupRelationRecordList?.map(i => getOrderInfo({ ...i, num: i.groupProportionNum }, true)));
				}
			}
		} else {
			exchange.push(item);
		}
	});
	exchange.push(...groupRelationRecordList);
	// 2、处理订单合并,循环，碰到一样的，加入，移除，最后当前循环体，也是加入，
	for (; exchange.length > 0;) {
		same = [];
		const isNoOrder = exchange[0].isNoOrder;

		if (([isTogetherEnum.同款商品不同规格合并打印, isTogetherEnum.同款商品同规格合并打印].includes(rule.isTogether)) && !isNoOrder) { // 过滤掉无商品
			// 如果是合并订单，进行订单合并操作，
			for (k = 1; k < exchange.length; k++) {
				// 宝贝合并时，有商家编码outIid，先判断商家编码，没有的话判断标题（title）, 17.02.21 改掉此逻辑，改为通过宝贝id来判断
				// 勾选货品简称与不勾选进行不同的判断
				// 勾选货品简称后，需判断货品简称是否一致 没有勾选货品简称的不进行判断
				if (exchange[k].numIid && exchange[k].numIid === exchange[0].numIid && rule.useShortName) {
					if (!exchange[k].titleShort || !exchange[0].titleShort || (exchange[k].titleShort && exchange[k].titleShort === exchange[0].titleShort)) {
						if (rule.isTogether == isTogetherEnum.同款商品不同规格合并打印) {
							same.push(exchange[k]);
							exchange.splice(k--, 1);
						} else if (exchange[k].skuId == exchange[0].skuId) {
							same.push(exchange[k]);
							exchange.splice(k--, 1);
						}
					}
				} else if (exchange[k].numIid && exchange[k].numIid === exchange[0].numIid) {
					if (rule.isTogether == isTogetherEnum.同款商品不同规格合并打印) {
						same.push(exchange[k]);
						exchange.splice(k--, 1);
					} else if (exchange[k].skuId == exchange[0].skuId) {
						same.push(exchange[k]);
						exchange.splice(k--, 1);
					}
				}
			}
		}
		

		// 无论是否是合并订单，都需要拆成数组套数组的结构，交由后续函数按照统一结构继续流动
		// 和当前循环的元素
		same.unshift(exchange[0]);
		exchange.splice(0, 1);
		// 对已经合并过title的子订单，再一次处理，按照skuName进行合并，同名称skuName合并，数量累加
		sameSku = [];
		if ([isTogetherEnum.同款商品不同规格合并打印, isTogetherEnum.同款商品同规格合并打印].includes(rule.isTogether) && !isNoOrder) {
			for (; same.length > 0;) {
				for (k = 1; k < same.length; k++) {
					// 无规格的数量也叠加
					if (same[k].originSkuName === same[0].originSkuName) {
						// skuName相同，数量累加，移除被累加的元素，索引减一
						same[0].num += same[k].num;
						same.splice(k, 1);
						k--;
					}
				}

				sameSku.push(same[0]);
				same.splice(0, 1);
			}
		} else {
			sameSku = same;
		}
		union.push(sameSku);
		// console.log("sameSku::", sameSku);
	}


	const { extJson } = rule;

	if (extJson?.itemSortConfig === ItemSortConfigEnum.自定义排序规则) {
		for (let i = 0; i < union.length; i++) {
			union[i] = sortOrdersByRule(union[i], extJson.itemSortRule);
		}
		union = sortOrdersByRule(union, extJson.itemSortRule, true);
	}

	// 3、组织展示的结构体，数组
	let contents: string[] = [];
	let finalTemp;
	let temp;
	let useSymble;
	let u;
	let separator = '';
	if (rule.showStyle && rule.separatorSign) {
		separator = rule.separatorSign?.includes("userCustomize") ? rule.separatorSign.replace("userCustomize", "") : rule.separatorSign;
	}
	// console.log("union::", union);
	union.forEach((u, index) => {
		let isCombination = u.findIndex(i => i.isCombination) > -1;
		finalTemp = [];
		// 序号

		if (rule.displayPrintOrderNum) {
			finalTemp.push(index + 1 + '.');
		}

		if (u.findIndex(i => i.isGift) > -1) {
			finalTemp.push('【赠】');
		}
		if (isCombination && rule?.extJson?.showCombinationIcon) {
			finalTemp.push('(组)');
		}
		let combinedTitle = null;
		// if ((isCombination && rule?.extJson?.showCombinationIcon) || !isCombination) {
		u.forEach(function(uItem, k) {
			temp = [];
			const isCombination = uItem.isCombination;
			if (uItem.isNoOrder) {
				uItem.handShipInfo && temp.push(uItem.handShipInfo);
			} else if (rule.extJson.showType == 2) { // 打印内容自定义顺序展示
				// 宝贝名称
				if (k === 0) {
					// 只有商家编码
					if (rule.useTitle) {
						// 勾选了title的情况集合
						temp[getContentFieldIndex('title')] = uItem.title;
						if (rule.useOuterIid) { temp[getContentFieldIndex('outerIid')] = uItem.outerIid; }
						if (rule.useShortName) { temp[getContentFieldIndex('titleShort')] = uItem.titleShort; }
						if (rule.useSysOuterId) { temp[getContentFieldIndex('sysOuterId')] = uItem.sysOuterId; }
					} else if (rule.useOuterIid && !rule.useShortName && !rule.useTitle && !rule.useSysOuterId) {
						// 仅勾选了outerIid
						if (uItem.outerIid) {
							temp[getContentFieldIndex('outerIid')] = uItem.outerIid;
						} else if (rule.useNumIid) {
							// 没有商家编码时显示宝贝ID
							if (uItem.numIid) {
								temp[getContentFieldIndex('outerIid')] = uItem.numIid;
							} else {
								// 无宝贝ID，显示标题
								temp[getContentFieldIndex('title')] = uItem.title;
							}

						} else if (!rule.useOuterIidEmpty) {
							// 没有商家编码时显示简称或标题
							if (uItem.titleShort) {
								// 有简称
								temp[getContentFieldIndex('titleShort')] = uItem.titleShort;
							} else {
								// 无简称，显示标题
								temp[getContentFieldIndex('title')] = uItem.title;
							}
						}

					} else if (!rule.useOuterIid && rule.useShortName && !rule.useTitle && !rule.useSysOuterId) {
						// 仅选了titleShort的情况
						// useShortNameEmpty: 0（无简称时显示标题） 1（无简称时不显示内容） 2（无简称时显示商家编码
						if (uItem.titleShort) {
							temp[getContentFieldIndex('titleShort')] = uItem.titleShort;
						} else if (rule.useShortNameEmpty == 0) {
							temp[getContentFieldIndex('title')] = uItem.title;
						} else if (rule.useShortNameEmpty == 2) {
							if (uItem.outerIid) {
								temp[getContentFieldIndex('outerIid')] = uItem.outerIid;
							} else {
								temp[getContentFieldIndex('title')] = uItem.title;
							}

						}
					} else if (!rule.useOuterIid && !rule.useShortName && !rule.useTitle && rule.useSysOuterId) {
						// 仅选了useSysOuterId的情况
						// useSysOuterId: 1 (没有时显示商家编码)  2 (没有时显示简称或标题)  3 (没有时不显示内容)
						if (uItem.sysOuterId) {
							temp[getContentFieldIndex('sysOuterId')] = uItem.sysOuterId;
						} else if (rule.useSysOuterId == 1) {
							temp[getContentFieldIndex('outerIid')] = uItem.outerIid;
						} else if (rule.useSysOuterId == 2) {
							temp[getContentFieldIndex('titleShort')] = uItem.titleShort || uItem.title;
						}
					} else if (rule.useOuterIid && rule.useShortName && !rule.useTitle && !rule.useSysOuterId) {
						// 勾选了ouerIid和titleshort的情况
						if (uItem.outerIid || uItem.titleShort) {
							temp[getContentFieldIndex('outerIid')] = uItem.outerIid;
							temp[getContentFieldIndex('titleShort')] = uItem.titleShort || uItem.title;
						} else if (rule.useOthersWithStall == 0) {
							// ouerIid和titleshort为空值显示title的情况
							temp[getContentFieldIndex('title')] = uItem.title;
						}
					} else if (rule.useOuterIid && !rule.useShortName && !rule.useTitle && rule.useSysOuterId) {
						// 勾选了ouerIid和sysOuterId的情况
						if (uItem.outerIid || uItem.stall) {
							temp[getContentFieldIndex('outerIid')] = uItem.outerIid;
							temp[getContentFieldIndex('sysOuterId')] = uItem.sysOuterId;
						} else if (rule.useOthersWithStall == 0) {
							// ouerIid和sysOuterId为空值显示title的情况
							temp[getContentFieldIndex('title')] = uItem.title;
						}
					} else if (!rule.useOuterIid && rule.useShortName && !rule.useTitle && rule.useSysOuterId) {
						// 勾选了titleShort和sysOuterId的情况
						if (uItem.titleShort || uItem.sysOuterId) {
							temp[getContentFieldIndex('titleShort')] = uItem.titleShort;
							temp[getContentFieldIndex('sysOuterId')] = uItem.sysOuterId;
						} else if (rule.useOthersWithStall == 0) {
							// titleShort和sysOuterId为空值显示title的情况
							temp[getContentFieldIndex('title')] = uItem.title;
						}
					} else if (rule.useOuterIid && rule.useShortName && !rule.useTitle && rule.useSysOuterId) {
						// 勾选了outerIid和titleShort和sysOuterId的情况
						if (uItem.outerIid || uItem.titleShort || uItem.sysOuterId) {
							temp[getContentFieldIndex('outerIid')] = uItem.outerIid;
							temp[getContentFieldIndex('titleShort')] = uItem.titleShort;
							temp[getContentFieldIndex('sysOuterId')] = uItem.sysOuterId;
						} else if (rule.useOthersWithStall == 0) {
							// outerIid和titleShort和sysOuterId为空值显示title的情况
							temp[getContentFieldIndex('title')] = uItem.title;
						}
					}
				}
				// 规格
				if (rule.useRuleOutIid && !rule.useSysOuterSkuId && !rule.useSkuName && !rule.showSkuAlias) {
					// 规格对应的商家编码
					if (uItem.outerSkuId) {
						temp[getContentFieldIndex('outerSkuId')] = uItem.outerSkuId;
					} else if (!rule.useRuleOutIidEmpty) {
						temp[getContentFieldIndex('outerSkuId')] = uItem.skuPropertiesName || '';
					}
				} else if (!rule.useRuleOutIid && rule.useSysOuterSkuId && !rule.useSkuName && !rule.showSkuAlias) {
					// 货品规格编码
					if (uItem.sysOuterSkuId) {
						temp[getContentFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId;
					} else if (rule.useSysOuterSkuId === 1) {
						temp[getContentFieldIndex('sysOuterSkuId')] = uItem.skuPropertiesName || '';
					}
				} else if (!rule.useRuleOutIid && !rule.useSysOuterSkuId && rule.useSkuName && !rule.showSkuAlias) {
					// 只打印规格名称
					temp[getContentFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
				} else if (!rule.useRuleOutIid && !rule.useSysOuterSkuId && !rule.useSkuName && rule.showSkuAlias) {
					// 只打印规格别名
					if (uItem.skuAlias) {
						temp[getContentFieldIndex('skuAlias')] = uItem.skuAlias;
					} else if (!rule.showSkuAliasEmpty) {
						temp[getContentFieldIndex('skuAlias')] = uItem.skuPropertiesName || '';
					}
				} else {
					if (rule.useRuleOutIid)temp[getContentFieldIndex('outerSkuId')] = uItem.outerSkuId || '';
					if (rule.useSysOuterSkuId)temp[getContentFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId || '';
					if (rule.useSkuName)temp[getContentFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
					if (rule.showSkuAlias)temp[getContentFieldIndex('skuAlias')] = uItem.skuAlias || '';
					if (rule.extJson?.showContent?.includes('itemNo'))temp[getContentFieldIndex('itemNo')] = uItem.itemNo || '';
				}

				if (rule.useRuleOutIid && !rule.useOuterIid && !uItem.skuId) {
					temp[getContentFieldIndex('outerSkuId')] = uItem.outerIid || '';
				}
				// 市场档口供应商
				if (rule?.extJson?.showMarketStall) {
					const isMarket = rule.extJson?.showContent?.includes(MarketStallSupplierEnum.市场);
					const isStall = rule.extJson?.showContent?.includes(MarketStallSupplierEnum.档口);
					const isSupplier = rule.extJson?.showContent?.includes(MarketStallSupplierEnum.供应商);
					if (isMarket) {
						temp[getContentFieldIndex('1')] = uItem.market;
					}
					if (isStall) {
						temp[getContentFieldIndex('2')] = uItem.stall;
					}
					if (isSupplier) {
						temp[getContentFieldIndex('3')] = uItem.supplierName;
					}
				}

				if (rule.extJson?.showContent?.includes('orderPrice')) {
					temp[getContentFieldIndex('orderPrice')] = ` 单价: ${uItem.price} `;
				}
				if (rule.extJson?.showContent?.includes('warehouseSlotName')) {
					temp[getContentFieldIndex('warehouseSlotName')] = uItem.warehouseSlotName || '';
				}

				let ItemNum = getGoodsNumDisplay(rule, uItem.num);
				let numUnit = getUnitStyle(rule);
				temp[getContentFieldIndex('itemNum')] = ` ${ItemNum}${numUnit} `;

				temp = [temp.filter(Boolean).join(separator)];
			} else {
				useSymble = false;
				// 宝贝名称
				if (k === 0) {
					// 只有商家编码
					let tpNameArr = [];
					let names: string[] = [];
					if (rule.useTitle) {
						// 勾选了title的情况集合
						tpNameArr[getGoodsFieldIndex('title')] = uItem.title;
						if (rule.useOuterIid) { tpNameArr[getGoodsFieldIndex('outerIid')] = uItem.outerIid; }
						if (rule.useShortName) { tpNameArr[getGoodsFieldIndex('titleShort')] = uItem.titleShort; }
						if (rule.useSysOuterId) { tpNameArr[getGoodsFieldIndex('sysOuterId')] = uItem.sysOuterId; }
						useSymble = true;
					} else if (rule.useOuterIid && !rule.useShortName && !rule.useTitle && !rule.useSysOuterId) {
						// 仅勾选了outerIid
						if (uItem.outerIid) {
							tpNameArr[getGoodsFieldIndex('outerIid')] = uItem.outerIid;
							useSymble = true;
						} else if (rule.useNumIid) {
							// 没有商家编码时显示宝贝ID
							if (uItem.numIid) {
								tpNameArr[getGoodsFieldIndex('outerIid')] = uItem.numIid;
							} else {
								// 无宝贝ID，显示标题
								tpNameArr[getGoodsFieldIndex('title')] = uItem.title;
							}

						} else if (!rule.useOuterIidEmpty) {
							// 没有商家编码时显示简称或标题
							if (uItem.titleShort) {
								// 有简称
								tpNameArr[getGoodsFieldIndex('titleShort')] = uItem.titleShort;
							} else {
								// 无简称，显示标题
								tpNameArr[getGoodsFieldIndex('title')] = uItem.title;
							}
							useSymble = true;
						}

					} else if (!rule.useOuterIid && rule.useShortName && !rule.useTitle && !rule.useSysOuterId) {
						// 仅选了titleShort的情况
						// useShortNameEmpty: 0（无简称时显示标题） 1（无简称时不显示内容） 2（无简称时显示商家编码
						if (uItem.titleShort) {
							tpNameArr[getGoodsFieldIndex('titleShort')] = uItem.titleShort;
							useSymble = true;
						} else if (rule.useShortNameEmpty == 0) {
							tpNameArr[getGoodsFieldIndex('title')] = uItem.title;
							useSymble = true;
						} else if (rule.useShortNameEmpty == 2) {
							if (uItem.outerIid) {
								tpNameArr[getGoodsFieldIndex('outerIid')] = uItem.outerIid;
							} else {
								tpNameArr[getGoodsFieldIndex('title')] = uItem.title;
							}

							useSymble = true;
						}
					} else if (!rule.useOuterIid && !rule.useShortName && !rule.useTitle && rule.useSysOuterId) {
						// 仅选了useSysOuterId的情况
						// useSysOuterId: 1 (没有时显示商家编码)  2 (没有时显示简称或标题)  3 (没有时不显示内容)
						if (uItem.sysOuterId) {
							tpNameArr[getGoodsFieldIndex('sysOuterId')] = uItem.sysOuterId;
							useSymble = true;
						} else if (rule.useSysOuterId == 1) {
							tpNameArr[getGoodsFieldIndex('outerIid')] = uItem.outerIid;
							useSymble = true;
						} else if (rule.useSysOuterId == 2) {
							tpNameArr[getGoodsFieldIndex('titleShort')] = uItem.titleShort || uItem.title;
							useSymble = true;
						}
					} else if (rule.useOuterIid && rule.useShortName && !rule.useTitle && !rule.useSysOuterId) {
						// 勾选了ouerIid和titleshort的情况
						if (uItem.outerIid || uItem.titleShort) {
							tpNameArr[getGoodsFieldIndex('outerIid')] = uItem.outerIid;
							tpNameArr[getGoodsFieldIndex('titleShort')] = uItem.titleShort || uItem.title;
							useSymble = true;
						} else if (rule.useOthersWithStall == 0) {
							// ouerIid和titleshort为空值显示title的情况
							tpNameArr[getGoodsFieldIndex('title')] = uItem.title;
							useSymble = true;
						}
					} else if (rule.useOuterIid && !rule.useShortName && !rule.useTitle && rule.useSysOuterId) {
						// 勾选了ouerIid和sysOuterId的情况
						if (uItem.outerIid || uItem.stall) {
							tpNameArr[getGoodsFieldIndex('outerIid')] = uItem.outerIid;
							tpNameArr[getGoodsFieldIndex('sysOuterId')] = uItem.sysOuterId;
							useSymble = true;
						} else if (rule.useOthersWithStall == 0) {
							// ouerIid和sysOuterId为空值显示title的情况
							tpNameArr[getGoodsFieldIndex('title')] = uItem.title;
							useSymble = true;
						}
					} else if (!rule.useOuterIid && rule.useShortName && !rule.useTitle && rule.useSysOuterId) {
						// 勾选了titleShort和sysOuterId的情况
						if (uItem.titleShort || uItem.sysOuterId) {
							tpNameArr[getGoodsFieldIndex('titleShort')] = uItem.titleShort;
							tpNameArr[getGoodsFieldIndex('sysOuterId')] = uItem.sysOuterId;
							useSymble = true;
						} else if (rule.useOthersWithStall == 0) {
							// titleShort和sysOuterId为空值显示title的情况
							tpNameArr[getGoodsFieldIndex('title')] = uItem.title;
							useSymble = true;
						}
					} else if (rule.useOuterIid && rule.useShortName && !rule.useTitle && rule.useSysOuterId) {
						// 勾选了outerIid和titleShort和sysOuterId的情况
						if (uItem.outerIid || uItem.titleShort || uItem.sysOuterId) {
							tpNameArr[getGoodsFieldIndex('outerIid')] = uItem.outerIid;
							tpNameArr[getGoodsFieldIndex('titleShort')] = uItem.titleShort;
							tpNameArr[getGoodsFieldIndex('sysOuterId')] = uItem.sysOuterId;
							useSymble = true;
						} else if (rule.useOthersWithStall == 0) {
							// outerIid和titleShort和sysOuterId为空值显示title的情况
							tpNameArr[getGoodsFieldIndex('title')] = uItem.title;
							useSymble = true;
						}
					}
					tpNameArr.forEach(val => {
						val && names.push(val);
					});
					
					temp.push(names.join(separator));
					if (isCombination && rule?.extJson?.hideCombination) {
						combinedTitle = names.join(separator);
					}
				}
				if (k > 0 && !isCombination && rule?.extJson?.hideCombination && combinedTitle) {
					temp.push(combinedTitle);
				}
				// 规格颜色
				let skuTemp = '';
				let skuInfoArr = [];
				let skus: string[] = [];
				if (rule.useRuleOutIid && !rule.useSysOuterSkuId && !rule.useSkuName && !rule.showSkuAlias) {
					// 规格对应的商家编码
					if (uItem.outerSkuId) {
						skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.outerSkuId || '';
					} else if (!rule.useRuleOutIidEmpty) {
						skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.skuPropertiesName || '';
					}
				} else if (!rule.useRuleOutIid && rule.useSysOuterSkuId && !rule.useSkuName && !rule.showSkuAlias) {
					// 货品规格编码
					if (uItem.sysOuterSkuId) {
						skuInfoArr[getSkuFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId || '';
					} else if (rule.useSysOuterSkuId === 1) {
						skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
					}
				} else if (!rule.useRuleOutIid && !rule.useSysOuterSkuId && rule.useSkuName && !rule.showSkuAlias) {
					// 只打印规格名称
					skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
				} else if (!rule.useRuleOutIid && !rule.useSysOuterSkuId && !rule.useSkuName && rule.showSkuAlias) {
					// 只打印规格别名
					if (uItem.skuAlias) {
						skuInfoArr[getSkuFieldIndex('skuAlias')] = uItem.skuAlias || '';
					} else if (!rule.showSkuAliasEmpty) {
						skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
					}
				} else if (rule.useRuleOutIid && rule.useSysOuterSkuId && !rule.useSkuName && !rule.showSkuAlias) {
					// 同时打印规格编码和货品规格编码
					skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.outerSkuId || '';
					skuInfoArr[getSkuFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId || '';
				} else if (rule.useRuleOutIid && !rule.useSysOuterSkuId && rule.useSkuName && !rule.showSkuAlias) {
					// 同时打印规格编码和规格名称
					skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.outerSkuId || '';
					skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
				} else if (rule.useRuleOutIid && !rule.useSysOuterSkuId && !rule.useSkuName && rule.showSkuAlias) {
					// 同时打印规格编码和规格别名
					skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.outerSkuId || '';
					skuInfoArr[getSkuFieldIndex('skuAlias')] = uItem.skuAlias || '';
				} else if (!rule.useRuleOutIid && rule.useSysOuterSkuId && rule.useSkuName && !rule.showSkuAlias) {
					// 同时打印货品规格编码和规格名称
					skuInfoArr[getSkuFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId || '';
					skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
				} else if (!rule.useRuleOutIid && rule.useSysOuterSkuId && !rule.useSkuName && rule.showSkuAlias) {
					// 同时打印货品规格编码和规格别名
					skuInfoArr[getSkuFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId || '';
					skuInfoArr[getSkuFieldIndex('skuAlias')] = uItem.skuAlias || '';
				} else if (!rule.useRuleOutIid && !rule.useSysOuterSkuId && rule.useSkuName && rule.showSkuAlias) {
					// 同时打印规格别名和规格别名
					skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
					skuInfoArr[getSkuFieldIndex('skuAlias')] = uItem.skuAlias || '';
				} else if (rule.useRuleOutIid && rule.useSysOuterSkuId && rule.useSkuName && !rule.showSkuAlias) {
					// 同时打印规格编码、货品规格编码和规格名称
					skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.outerSkuId || '';
					skuInfoArr[getSkuFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId || '';
					skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
				} else if (rule.useRuleOutIid && rule.useSysOuterSkuId && !rule.useSkuName && rule.showSkuAlias) {
					// 同时打印规格编码、货品规格编码和规格别名
					skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.outerSkuId || '';
					skuInfoArr[getSkuFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId || '';
					skuInfoArr[getSkuFieldIndex('skuAlias')] = uItem.skuAlias || '';
				} else if (rule.useRuleOutIid && !rule.useSysOuterSkuId && rule.useSkuName && rule.showSkuAlias) {
					// 同时打印规格编码、规格名称和规格别名
					skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.outerSkuId || '';
					skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
					skuInfoArr[getSkuFieldIndex('skuAlias')] = uItem.skuAlias || '';
				} else if (!rule.useRuleOutIid && rule.useSysOuterSkuId && rule.useSkuName && rule.showSkuAlias) {
					// 同时打印货品规格编码、规格名称和规格别名
					skuInfoArr[getSkuFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId || '';
					skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
					skuInfoArr[getSkuFieldIndex('skuAlias')] = uItem.skuAlias || '';
				} else if (rule.useRuleOutIid && rule.useSysOuterSkuId && rule.useSkuName && rule.showSkuAlias) {
					// 同时打印规格编码、货品规格编码、规格名称和规格别名
					skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.outerSkuId || '';
					skuInfoArr[getSkuFieldIndex('sysOuterSkuId')] = uItem.sysOuterSkuId || '';
					skuInfoArr[getSkuFieldIndex('skuPropertiesName')] = uItem.skuPropertiesName || '';
					skuInfoArr[getSkuFieldIndex('skuAlias')] = uItem.skuAlias || '';
				}

				if (rule.useRuleOutIid && !rule.useOuterIid && !uItem.skuId) {
					skuInfoArr[getSkuFieldIndex('outerSkuId')] = uItem.outerIid || '';
				}

				// 货号，兼容以前的格式
				if (rule.showSequenceSku?.includes('itemNo')) {
					skuInfoArr[getSkuFieldIndex('itemNo')] = uItem.itemNo || '';
				}

				if (rule.showSequenceSku?.includes('warehouseSlotName')) {
					skuInfoArr[getSkuFieldIndex('warehouseSlotName')] = uItem.warehouseSlotName || '';
				}

				if (rule.showOrderPrice) {
					if (rule.showSequenceSku?.includes('orderPrice')) {
						skuInfoArr[getSkuFieldIndex('orderPrice')] = uItem.price ? ` 单价：${uItem.price}` : '';
					} else {
						skuInfoArr.push(uItem.price ? ` 单价：${uItem.price}` : '');
					}
				}

				// 将数组信息转化成字符串赋值给skuTemp字段
				skuInfoArr.forEach(val => {
					val && skus.push(val);
				});

				// 兼容规格颜色第一排只选一个 修改顺序后有效
				skuTemp = skus.join(separator);


				// 市场档口供应商
				if (rule?.extJson?.showMarketStall) {
					skuTemp = getMarketStallSkuTemp(skuTemp, rule, uItem);
				}

				if (skuTemp) {
					temp.push((useSymble ? ':' : '') + skuTemp);
				}
				let ItemNum = getGoodsNumDisplay(rule, uItem.num);
				if (ItemNum) temp.push(ItemNum);

				let numUnit = getUnitStyle(rule);
				if (numUnit) temp.push(numUnit);
				temp.push(' ');
			}
			if (isCombination) {
				if (!rule?.extJson?.hideCombination) finalTemp.push(...temp);
			} else {
				finalTemp.push(...temp);
			}
		});
		// }

		// 不显示组合商品，根据 showCombinationIcon 判断
		// const deleteCombinationLine = isCombination && rule?.extJson?.hideCombination;
		// 所有加入的原始数据替换空格
		if (finalTemp.filter(i => i).length) {
			contents.push([...finalTemp].join('').replace(/ /g, '#whitespace#'));
		}
		if (advancedSetRes?.showCombinationInfo === 2 && rule?.extJson?.showCombinationInfoType === 1 && isCombination) {
			dealChild(contents, u, rule, separator);
		}
	});


	// 4、处理换行问题

	let printContent: string;
	// 不换行
	if (rule.breakline === -1 || (contents.length >= rule.breaklineNum && rule.breakline === 2)) {
		printContent = contents.join('   ');
	} else if (rule?.extJson?.changeLineConfig == PrintSetXBreakLine.条件换行 && rule?.extJson?.changeLineGoodsNum > 1) {
		// 每 x 换行
		let colsCount = rule.extJson.changeLineGoodsNum;
		let rowsCount = Math.ceil(contents.length / colsCount);
		let conArr = [];
		for (let i = 0; i < rowsCount; i++) {
			let tmp = [];
			for (let j = 0; j < colsCount; j++) {
				tmp.push(contents[i * colsCount + j]);
			}
			conArr.push(tmp.join('   '));
		}
		printContent = conArr.join('\r\n');
	} else {
		// 换行
		printContent = contents.join('\r\n');
	}

	// 去除由合并产生的多余空格
	printContent = printContent.replace(/^ {1,}/, '').replace(/ {2,}/g, '   ');
	// 还原原有空格
	printContent = printContent.replace(/#whitespace#/g, ' ');

	// 添加满就送内容
	// if (obj.hasReward && rule.tradeReward) {
	// 	printContent += '\r\n送：' + obj.rewards.join('\r\n');
	// }
	return printContent;
}


export const genePrintContent = (pack: IPackage, style: TradePrintContentSetRule = {}, keywords: string[], advancedSetRes?: any) => {
	let subOrders: IOrders[] = [];
	const { printGiftOrder = true } = style || {};
	let totalOrderNum = 0;
	pack.trades.forEach(trade => {
		trade.orders.forEach(order => {
			totalOrderNum++;
			/**
         * splitTradeType拆分订单类型<br>,REGULAR_GIFT_ORDER("拆分后普通商品+赠品订单"),<br>,REGULAR_ORDER("拆分后仅普通订单"),<br>,GIFT("拆分后仅赠品订单"),<br>,DEFAULT("默认订单");<br>,对应枚举com.kuaidizs.general.rds.api.common.enums.TradeSplitTypeEnum
         */
			// const isGiftPrint = (order.isGift && trade.splitTradeType === "GIFT") ? true : printGiftOrder;
			const isGiftPrint = ((order.isSysGift || order.isGift) && trade.splitTradeType !== "GIFT") ? printGiftOrder : true;
			if (order.isChecked && isGiftPrint) {
				// 无商品
				let index: number;
				if (order.numIid == '-1' && trade.shipInfo) {
					index = subOrders.push({
						...order,
						shipInfo: trade.shipInfo
					});
				} else {
					index = subOrders.push({
						...order
					});
				}

				if (subOrders[index - 1].ignore) {
					subOrders[index - 1].titleShort = '';
					subOrders[index - 1].skuAlias = '';
				}
			}
		});
	});

	const { isTogether = 0 } = style;
	if (isTogether == isTogetherEnum.不合并打印) {
		const tempOrder: IOrders[] = [];
		subOrders.forEach(item => {
			if (item.num > 1) {
				for (let i = 0; i < item.num; i++) {
					tempOrder.push({ ...item, num: 1 });
				}
			} else {
				tempOrder.push(item);
			}
		});
		subOrders = tempOrder;
	}

	const togetherId = pack.togetherId;

	let printContent = gatherPrintContent({
		obj: {
			subOrders,
			togetherId
		},
		rule: style,
		keywords,
		advancedSetRes
	});
	if (subOrders.length > PRINT_CONTENT_MAX_ROW) {
		pack.printContentOverflowRow = true;
	}
	if (printContent.length > PRINT_CONTENT_MAX_LENGTH) {
		pack.printContentOverflow = true;
		printContent = printContent.slice(0, PRINT_CONTENT_MAX_LENGTH);
	}
	return printContent;
};

const sortOrdersByRule = (orders: IOrders[], sortRule = ItemSortRuleEnum["按商家编码+规格编码升序"], isDeep = false): IOrders[] => {
	orders.sort((a, b) => {
		let sortLabelFn = (order) => '';
		switch (sortRule) {
			case ItemSortRuleEnum["按商家编码+规格编码升序"]:
				sortLabelFn = (order: IOrders) => `${order.outerId || ''}-${order.outerSkuId || ''}`;
				break;

			case ItemSortRuleEnum["按规格编码升序"]:
				sortLabelFn = (order: IOrders) => `${order.outerSkuId || ''}`;
				break;

			case ItemSortRuleEnum["按规格名称升序"]:
				sortLabelFn = (order: IOrders) => `${order.skuPropertiesName}`;
				break;

			case ItemSortRuleEnum["按货品规格编码升序"]:
				sortLabelFn = (order: IOrders) => `${order.sysOuterSkuId || ''}`;
				break;

			case ItemSortRuleEnum["按货品简称升序"]:
				sortLabelFn = (order: IOrders) => `${order.titleShort || ''}`;
				break;

			case ItemSortRuleEnum["按货品规格名称升序"]:
				sortLabelFn = (order: IOrders) => `${order.systemSkuName || ''}`;
				break;

			case ItemSortRuleEnum["按货品规格别名升序"]:
				sortLabelFn = (order: IOrders) => `${order.skuAlias || ''}`;
				break;

			case ItemSortRuleEnum["按简称+规格别名升序"]:
				sortLabelFn = (order: IOrders) => `${order.titleShort || ''}-${order.skuAlias || ''}`;
				break;
				
			case ItemSortRuleEnum["按规格别名升序"]:
				sortLabelFn = (order: IOrders) => `${order.skuAlias || ''}`;
				break;

			default:
				break;
		}

		let compareA = sortLabelFn(isDeep ? a[0] : a);
		let compareB = sortLabelFn(isDeep ? b[0] : b);

		if (compareA > compareB) return 1;
		if (compareA < compareB) return -1;
		return 0;
	});
	return orders;
};


const sortChildItemByRule = (childList: IGroupRelationRecordList[], sortRule = ItemSortRuleEnum["按商家编码+规格编码升序"]) => {
	const getRuleItemName = (item: IGroupRelationRecordList) => {
		switch (sortRule) {
			case ItemSortRuleEnum["按货品规格编码升序"]:
				return item?.skuOuterId;

			case ItemSortRuleEnum["按货品简称升序"]:
				return item?.sysItemAlias;

			case ItemSortRuleEnum["按货品规格名称升序"]:
				return item?.sysSkuName;

			case ItemSortRuleEnum["按货品规格别名升序"]:
				return item?.sysSkuAlias;

			default:
				return '';
		}
	};
	childList.sort((a, b) => {
		let compareA = getRuleItemName(a);
		let compareB = getRuleItemName(b);
		if (compareA > compareB) return 1;
		if (compareA < compareB) return -1;
		return 0;
	});
};
