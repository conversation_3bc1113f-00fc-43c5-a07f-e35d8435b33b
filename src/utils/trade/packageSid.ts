// import { IOrderDetailItem } from '@/interface/orderPrint';
import { getBatchSids } from '@/apis/trade/index';
import { dealPlatAsHandPlat, transferOtherToHand } from '@/components-biz/ShopListSelect/shopListUtils';
import { HL_PLAT, PRINT_MAP, PLAT_SPH, PLAT_XHS, PLAT_ALI } from '@/constants';
import memoFn from '@/libs/memorizeFn';
import { getHandTempSaveKey } from '@/pages/Trade/components/EditSidModal';
import { tradeStore } from '@/stores';
import { refluxOrderPrintSettingsEnum } from '@/types/trade/index';

// 电子面单获取单号
let timestamp;
let _timestamp;
const getPackageSid = async({ currentTemplate, packageList, isMerge }: {
	currentTemplate: any,
	packageList: any,
	isMerge: boolean
}, updateFn) => {
	// 如果是电子面单，获取电子面单号，没有的填写打印后获取
	if (currentTemplate.KddType === 1) return;
	const { setting } = tradeStore;
	const advancedRes = await memoFn.getAdvancedSet();
	let isUsePlatTemp = advancedRes?.refluxOrderPrintSet == refluxOrderPrintSettingsEnum.使用对应平台面单打印;

	timestamp = new Date().getTime();
	let _len = packageList.length;
	let noCheckedOrderPackList = []; // order未勾选
	let contents = [];
	let _tradesParams = [];
	let _tradeNum = 0;

	let updateList = [];
	let noTempOrder = [];
	for (let i = 0; i < packageList.length; i++) {
		if (packageList[i].sids) {
			packageList[i].sids = [''];
			updateList.push(packageList[i]);
		}
	}

	if (updateList.length) {
		updateFn(updateList);
		// return;
	}
	if (isMerge && (!currentTemplate.userTemplateList || !currentTemplate?.userTemplateList.length)) {
		return;
	}
	let itemType = '';
	let orderBytemp = {};
	if (isMerge) {
		packageList.forEach(it => {
			itemType = PRINT_MAP[it.platform];
			// 1688回流订单聚合模板取值，密文 还有 设定为平台的明文，取平台
			console.log('取平台', it.hlEncryptOrder || (isUsePlatTemp && !it.hlEncryptOrder));

			if (it.hlPlatformType && (it.hlEncryptOrder || (isUsePlatTemp && !it.hlEncryptOrder && it.hlPlatformType !== 'hl-sph'))) {
				const hlPlat = HL_PLAT[it.hlPlatformType];
				itemType = PRINT_MAP[hlPlat] || PRINT_MAP[PLAT_ALI];
			}
			if (dealPlatAsHandPlat(it.platform, null, it)) {
				let orderMatchSetting = setting?.groupPrintSetJsonString?.orderMatchSetting.find(o => o.platform === transferOtherToHand(it.platform, it));
				itemType = orderMatchSetting?.bindControlType;
			}
			if (orderBytemp[itemType]) {
				orderBytemp[itemType].push(it);
			} else {
				orderBytemp[itemType] = [it];
			}
		});
		for (let k in orderBytemp) {
			let temp_tradeNum = 0;
			let trades = [];
			let tempByItemType = currentTemplate.userTemplateList.find((it: any) => {
				if (k == '13') {
					return [13, 16].includes(Number(it.expressType)) || +currentTemplate.id == -901;
				} else {
					return it.expressType == k || +currentTemplate.id == -901;
				}
			});
			if (tempByItemType) {
				orderBytemp[k].forEach((item, i) => {
					let tids = [];
					let tidsWithOids = [];
					let typeLen = orderBytemp[k].length;
					item.trades.forEach((trade, idx) => {
						let orders = trade.orders;
						let checkedOrders = trade.orders.filter(order => order.isChecked);
						let oids = [];

						// 选中订单才需要添加
						if (checkedOrders.length === 0) {
							return;
						}

						tids.push(trade.tid);

						// 所有商品都被选中的话，无需传入oids
						if (checkedOrders.length === trade.orders.length) {
							tidsWithOids.push(`${trade.tid}`);
						} else {
							tidsWithOids.push(`${trade.tid}:${checkedOrders.map(i => i.oid).join(",")}`);
						}
					});

					trades.push({
						// tids: tids,
						togetherId: item.togetherId,
						sellerId: item.sellerId,
						platform: item.platform,
						// encodeTid: item.encodeTid
					});

					if (trades.length) {
						trades = trades.map((i) => {
							return {
								tids: tidsWithOids.join("|"),
								...i,
							};
						});
						temp_tradeNum++;
					} else {
						noCheckedOrderPackList.push(item);
					}

					if (temp_tradeNum !== 0 && (temp_tradeNum % 20 === 0 || i === typeLen - 1)) { // 5单一请求
						contents.push({
							kdCode: tempByItemType.exCode,
							kdId: tempByItemType.logisticsCompanyId,
							kdType: tempByItemType.expressType,
							exid: tempByItemType.exId,
							userTemplateId: tempByItemType.userTemplateId,
							trades: JSON.stringify(trades),
							// sellerId: item.sellerId,
							// platform: item.platform,
						});
						temp_tradeNum = 0;
						trades = [];
					}
				});
			} else {
				noTempOrder.push(...orderBytemp[k]);
			}

		}
	} else {
		let trades = [];
		packageList.forEach((item, i) => {
			let tids = [];
			let tidsWithOids = [];
			item.trades.forEach((trade, idx) => {
				let orders = trade.orders;
				let checkedOrders = trade.orders.filter(order => order.isChecked);
				let oids = [];

				// 选中订单才需要添加
				if (checkedOrders.length === 0) {
					return;
				}

				tids.push(trade.tid);

				// 所有商品都被选中的话，无需传入oids
				if (checkedOrders.length === trade.orders.length) {
					tidsWithOids.push(`${trade.tid}`);
				} else {
					tidsWithOids.push(`${trade.tid}:${checkedOrders.map(i => i.oid).join(",")}`);
				}
			});

			trades.push({
				// tids: tids,
				togetherId: item.togetherId,
				sellerId: item.sellerId,
				platform: item.platform,
				// encodeTid: item.encodeTid
			});

			if (trades.length) {
				// _tradesParams.push({
				// 	togetherId: item.togetherId,
				// 	trades,
				// });
				trades = trades.map((i) => {
					return {
						tids: tidsWithOids.join("|"),
						...i,
					};
				});
				_tradeNum++;
			} else {
				noCheckedOrderPackList.push(item);
			}

			if (_tradeNum !== 0 && (_tradeNum % 20 === 0 || i === _len - 1)) { // 5单一请求


				contents.push({
					kdCode: currentTemplate.ExCode,
					kdId: currentTemplate.logisticsCompanyId,
					kdType: currentTemplate.KddType,
					exid: currentTemplate.Exid,
					userTemplateId: currentTemplate.Mode_ListShowId,
					trades: JSON.stringify(trades),
					// sellerId: item.sellerId,
					// platform: item.platform,
				});

				_tradesParams = [];
				_tradeNum = 0;
				trades = [];
			}
		});
	}


	if (contents.length) {
		getCompanyPackageSid(contents, packageList, updateFn);
	}

	// console.log('noCheckedOrderPackList', noCheckedOrderPackList);


	if (noTempOrder.length) {
		noTempOrder.forEach((item, i) => {
			item.sids = [''];
		});
		updateFn(noTempOrder);
	}
	if (noCheckedOrderPackList.length) {
		noCheckedOrderPackList.forEach((item, i) => {
			item.sids = ['打印后生成'];
		});
		updateFn(noCheckedOrderPackList);
	}
};

const getCompanyPackageSid = (params, packageList, updateFn) => {
	// console.log('getCompanyPackageSid', params);
	_timestamp = timestamp;
	if (!params) {
		return;
	}
	let startTime = new Date().getTime();
	const handler = (p, data) => {
		// 去更新对应的输入框
		// TODO 后续可能需要一些判断
		if (true) {
			// const { packageList } = this.data;
			if (data && data.sids) {
				let updateList = [];
				const isHandSid = tradeStore.isMergePrint ? +tradeStore.selectedTempGroup.id == -901 : +tradeStore.selectedTemp.Exid == -901;
				if (data.sids.length) {
					p.trades = JSON.parse(p.trades);
					data.sids.forEach(sid => {
						let i = packageList.findIndex(item => item.togetherId == sid.togetherId);
						if (i > -1) {
							if (tradeStore.selectedHandTemp[sid.togetherId] && isHandSid) {
								packageList[i].sids = tradeStore.selectedHandTemp[sid.togetherId]?.map((item) => item.sid);
							} else {
								packageList[i].sids = (sid.sids || [{}]).map((item) => item.sid);
								packageList[i].pYdNo = (sid.sids || [{}]).map((item) => item.pYdNo)?.[0];
								packageList[i].pYdNo_sids = packageList[i].pYdNo ? (sid.sids || [{}]).filter((item) => item.pYdNo == packageList[i].pYdNo)?.map((item) => item.sid) : [];
								packageList[i].sidsExNames = (sid.sids || [{}]).map((item) => item.exName);
								packageList[i].sidsExCode = (sid.sids || [{}]).map((item) => item.exCode);
								packageList[i].sendMobile = (sid.sids || [{}]).map((item) => item?.sendMobile);
								updateList.push(packageList[i]);
							}
						}
						i = p.trades.findIndex(item => item.togetherId == sid.togetherId);
						p.trades.splice(i, 1);
					});
					if (p.trades.length) {
						p.trades.forEach(trade => {
							let i = packageList.findIndex(item => item.togetherId == trade.togetherId);
							if (i > -1) {
								if (tradeStore.selectedHandTemp[trade.togetherId] && isHandSid) {
									packageList[i].sids = tradeStore.selectedHandTemp[trade.togetherId]?.map((item) => item.sid);

								} else if (isHandSid) {
									packageList[i].sids = [''];
								} else {
									packageList[i].sids = ['打印后生成'];
								}
								updateList.push(packageList[i]);
							}
						});
					}
				} else {
					p.trades = JSON.parse(p.trades);
					p.trades.forEach(trade => {
						for (let i = 0; i < packageList.length; i++) {
							if (packageList[i].togetherId === trade.togetherId) {
								const saveKey = getHandTempSaveKey(packageList[i]);
								const handTmp = tradeStore.selectedHandTemp[saveKey];
								if (handTmp && isHandSid) {
									packageList[i].sids = handTmp.sid;
									packageList[i].sidsExNames = [handTmp.exName];
									packageList[i].sidsExCode = [handTmp.exCode];
								} else {
									packageList[i].sids = isHandSid ? [''] : ['打印后生成'];
									packageList[i].sidsExNames = [];
									packageList[i].sidsExCode = [];
								}
								updateList.push(packageList[i]);
								break;
							}
						}
					});
				}
				updateFn(updateList);
				// events.emit('tradeItem.update', 'allChange', { list: updateList, changeObj: ['sid', '_newSid'] });
			}
		}
		if (params && params.length) {
			apiCall();
		} else {
			console.log('getBatchSid over', new Date().getTime() - startTime);
		}
	};

	let preExId = tradeStore.isMergePrint ? tradeStore.selectedTempGroup.exCompanyId : tradeStore.selectedTemp.Exid;


	const apiCall = () => {
		let curExId = tradeStore.isMergePrint ? tradeStore.selectedTempGroup.exCompanyId : tradeStore.selectedTemp.Exid;
		if (curExId !== preExId) {
			console.log(`++++ 模板发生变更，旧模板${preExId}, 新模板${curExId}, 回显后续无需请求`);
			return;
		}

		// if (_timestamp != timestamp) {
		//     _timestamp = undefined;
		//     console.error('终止请求接口');
		//     return;
		// }
		if (params.length === 0) {
			return;
		}
		let p = params.shift();
		let { importExpress } = tradeStore;
		if (p.exid == -901 && importExpress && importExpress?.tradeExpressImportLogSequence) {
			p.tradeExpressImportLogSequence = importExpress.tradeExpressImportLogSequence;
		}

		if (!p.trades) { // 没有勾选商品不需要请求
			handler(p, false);
		} else if (+p.exid == -901 && !p.tradeExpressImportLogSequence) { // 手动导入单号，但没有Excel导入单号的情况下，不需要查询接口
			handler(p, { sids: [] });
		} else {
			getBatchSids(p).then(sd => {
				const data = (sd.result == 100 ? sd.data : false);
				handler(p, data);
			}).catch(() => {
				handler(p, false);
			});
		}
	};

	const curCount = params.length < 4 ? params.length : 4;
	for (let key = curCount; key > 0; key--) {
		apiCall();
	}
};

export {
	getPackageSid
};
