/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-07-22 10:59:32
 * @Description: 各平台图片缩略图处理工具
 */

// 定义参数接口
interface ImageThumbnailOptions {
	noScale?: boolean;
	url: string;
	width?: number;
	height?: number;
	quality?: number;
	scale?: number;
}

/**
 * 获取图片缩略图URL
 * @param options 配置选项
 * @param options.noScale 是否不缩放，默认false
 * @param options.url 原始图片URL
 * @param options.width 缩略图宽度，默认100
 * @param options.height 缩略图高度，默认100
 * @param options.quality 图片质量 1-100，默认75
 * @param options.scale 设备像素比，默认window.devicePixelRatio
 * @returns 处理后的缩略图URL
 */
export function getImageThumbnail(options: ImageThumbnailOptions): string {
	const {
		noScale = false,
		width = 100,
		height = 100,
		quality = 75,
		scale = window.devicePixelRatio
	} = options;
	let { url } = options;
	if (!url || typeof url !== 'string') {
		return url || '';
	}
	if (url.startsWith("//")) {
		url = `https:${url}`;
	}
	// 获取设备像素比并计算实际尺寸
	const dpi = scale || window.devicePixelRatio || 1;
	const calculatedWidth = width * dpi;
	const calculatedHeight = height * dpi;

	// 部分CDN服务的宽高只支持整十的，向上取整到最近的10的倍数
	const actualWidth = Math.ceil(calculatedWidth / 10) * 10;
	const actualHeight = Math.ceil(calculatedHeight / 10) * 10;

	try {
		const urlObj = new URL(url);
		const hostname = urlObj.hostname;
		// 1. 淘宝、淘工厂、1688、天猫
		if (hostname.includes('alicdn.com')) {
			// !! 淘宝的上限未知
			if (noScale) {
				return url;
			}
			return `${url}_${actualWidth}x${actualHeight}q${quality}.jpg`;
		}

		// 2. 拼多多
		if (hostname.includes('pddpic.com')) {
			return `${url}?imageView2/2/w/${actualWidth}/q/${quality}/format/jpeg`;
		}

		// 3. 抖音
		if (hostname.includes('ecombdimg.com')) {
			// const baseUrl = url.replace(/^\/obj\/|~240x0\.image/g, "");
			const baseUrl = url.replace('/obj/', '/');

			// 根据不同域名使用不同参数
			if (hostname.includes('p3-infra.elabpic.com')) {
				return `${baseUrl}~tplv-ax5x5hote5-3:${actualWidth}:q${quality}`;
			} else if (['p3-aio.ecombdimg.com', 'p6-aio.ecombdimg.com', 'p9-aio.ecombdimg.com'].some(domain => hostname.includes(domain))) {
				return `${baseUrl}~tplv-qzsgku4lz6-fxg-image:${actualWidth}:q${quality}.jpeg`;
			}
			return baseUrl;
		}

		// 4. 快手、阿里云 oss、 淘宝、淘工厂、1688、天猫
		if (hostname.includes('aliyuncs.com') || hostname.includes('ecukwai.com')) {
			return `${url}?x-oss-process=image/resize,w_${actualWidth},h_${actualHeight}`;
		}

		// 5. 视频号小店、小红书
		if (hostname.includes('wxapp.tc.qq.com') || hostname.includes('xiaohongshu.com')) {
			// !! 硬编码特殊兼容处理
			if (url.includes('/null?itemId=')) {
				return '';
			}
			return `${url}?imageView2/1/w/${actualWidth}/format/jpeg|imageMogr2/auto-orient=`;
		}
		// 6. 京东
		if (hostname.includes('360buyimg.com')) {
			if (url.includes('/jfs/')) {
				return url.replace('/jfs/', `/s${actualWidth}x${actualHeight}_jfs/`);
			}
			return url;
		}
		return url;

	} catch (error) {
		// URL解析失败，返回原始URL
		console.warn('图片URL解析失败:', url, error);
		return url;
	}
}
