export enum PrintStatus {
	已打印 = 'already',
	未打印 = 'partial'
}


export enum TradeStatus {
	全部 = 'ALL_STATUS',
	等待买家付款 = 'WAIT_BUYER_PAY',
	卖家部分发货 = 'SELLER_CONSIGNED_PART',
	等待卖家发货 = 'WAIT_SELLER_SEND_GOODS',
	等待买家确认收货 = 'WAIT_BUYER_CONFIRM_GOODS',
	买家已签收 = 'TRADE_BUYER_SIGNED',
	交易成功 = 'TRADE_FINISHED',
	交易取消 = 'TRADE_CANCELLED',
	交易自动关闭 = 'TRADE_CLOSED',
	交易关闭 = 'TRADE_CLOSED_BY_TAOBAO',
	支付确认中 = 'PAY_PENDING',
	'0元购' = 'WAIT_PRE_AUTH_CONFIRM',
	其他状态 = 'OTHER_STATUS'
}

export enum TradeStatusLabel {
	'WAIT_BUYER_PAY' = '待付款',
	'SELLER_CONSIGNED_PART' = '部分发货',
	'WAIT_SELLER_SEND_GOODS' = '待发货',
	'WAIT_BUYER_CONFIRM_GOODS' = '已发货',
	'TRADE_FINISHED'= '交易成功',
	'TRADE_CLOSED' = '已关闭',
	'TRADE_CLOSED_BY_TAOBAO'= '已关闭',
	'TRADE_CANCELLED' = '已关闭'
}

export enum FilterOrderShowSku {
	规格名称 = 'skuPropertiesName',
	商家编码 = 'outerId',
	规格别名 = 'skuAlias',
	规格编码 = 'outerSkuId',
	货品规格名称 = 'sysSkuName',
	货品规格编码 = 'sysSkuOuterId',
	货品规格别名 = 'sysSkuAlias'
}

export enum FilterOrderSort {
	按数量排序 = 'totalCount',
	相同简称排一起 = 'titleShort',
	"相同简称+规格别名排一起" = 'skuAlias',
	相同规格名称排一起 = 'skuName',
	"相同简称+规格名称排一起" = 'titleShortAndSkuName',
}

export enum FilterOrderSkuMerge {
	默认规则 = 1,
	"相同简称+规格名称合并" = 2,
	"相同简称+规格别名合并" = 3,
}

export enum SendType {
	自己联系 = '1',
	在线发货 = '2',
	无需物流发货 = '3',
	自动发货 = '4',
	'3PL发货' = '5',
	家装发货 = '6',
	重新发货 = '7',
	买家自提 = '8',
	卖家配送 = '9',
	追加包裹 = '11',
	'BIC自行发货' = '12', // BIC质检订单发货方式
	'BIC邮寄' = '13', // BIC质检订单发货方式
}

export enum RefundStatus {
	全部 = "ALL_STATUS",
	退款成功 = "REFUND_SUCCESSED",
	退款中 = "REFUND_ING",
	无退款 = "NOT_REFUND"
}

export enum RefundStatusText {
	ALL_STATUS = "全部",
	REFUND_SUCCESSED = "退款成功",
	REFUND_ING = "退款中",
	NOT_REFUND = "无退款"
}

export enum TradeChoiceType {
	半选 = 0.5,
	勾选可达订单 = 1,
	全选 = 2,
	反选 = 3,
	不选 = 4,
	勾选有货的 = 5,
	勾选未打印快递单 = 6,
	勾选未打印发货单 = 7,
	取消勾选指定订单 = 8,
	勾选指定订单 = 9,
	小标签生成_取消勾选指定订单 = 10,
	取消勾选子订单 = 11,
	勾选子订单 = 12,
	// 勾选非疫情订单 = 'epidemicArea',
	更新小标签标识 = 'changeLabel',
	更新先发货标识 = 'changeFirstSend',
	更新商品识别码 = 'changeProductIdCode',
	勾选前N个 = 'checkFirstN',
}

export enum JDStoreOrderType {
	'京仓订单' = 1,
	'云仓订单' = 2,
}

export enum JDOrderShipType {
	'京配' = 1,
	'京配自提' = 2,
}

export const enum PendingStatus {
	挂起 = 1,
	取消挂起 = 0,
}

export const enum PrintTypeEnum {
	快递单 = 'kdd',
	发货单 = 'fhd',
	运单号 = 'ydh',
	爆款打印 = 'scanPrintKdd',
}

export const enum TradeOptEnum {
	打印快递单 = 1,
	发货 = 2,
	订单合并 = 3,
	卖家备注 = 4,
	收件人信息 = 5,
	新建 = 6,
	复制 = 7,
	编辑= 8,
	强制打印 = 9,
	强制发货 = 10,
	强制申请 = 11,
	编辑商品 = 12,
	处理异常 = 13,
	手写单号 = 14,
	指定供应商发货 = 15,
	撤销代发 = 16,
	重新发货 = 17,
	分销商推送 = 18,
	重算快递 = 19,
	指定快递 = 20,
	清空快递 = 21,
	"编辑商品(自动换商品)" = 22,
	扫描发货 = 41,
	拆单发货 = 42,
	验货发货 = 43,
	验单发货 = 44,
	自动发货 = 45,
	"打印快递单—扫描打印" = 47,
	"打印快递单—扫描发货单打印快递单" = 48,
	退款标签重匹配自动发货 = 51,
	后置打印自动发货 = 59,
	追加包裹 = 62,
	打印订单码 = 63, // BIC打印订单码
	强制生成拣货波次 = 71, // 生成拣货波次

}

export enum IgnoreStatus {
	自动标记 = 1,
	非空单 = 2,
	手动标记 = 3,
	手动取消 = 4,
}
