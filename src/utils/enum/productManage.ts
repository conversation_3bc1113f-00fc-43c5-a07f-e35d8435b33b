export enum ItemStatus {
    出售中 = "onsale",
    仓库中 = "instock",
    已售完的商品 = "soldout",
    违规宝贝 = "violation"
}

export enum PriceStatus {
	售价 = "price",
	// 零售 = "retailPrice",
	// 批发 = "tradePrice",
	吊牌价 = "tagPrice",
	成本价 = "costPrice"
}

export enum WeightStatus {
	// 净重 = "netWeight",
	重量 = "weight"
}

export enum AutoCalculate {
	自动计算重量 = "autoWeight",
	自动计算成本 = "autoCostPrice"
}

export enum SystemItem {
	规格 = "sysSkuName",
	规格别名 = "sysSkuAlias",
	规格图片 = "picUrl",
	商家编码 = "skuOuterId",
	货号 = "itemNo",
	条形码 = "barCode",
	毛重 = "netWeight",
	净重 = "weight",
	网销 = "price",
	零售 = "retailPrice",
	批发 = "tradePrice",
	吊牌 = "tagPrice",
	成本 = "costPrice",
	供应商名称 = 'supplierName',
	供应商ID = 'supplierId',
	市场 = 'market',
	档口 = 'stall',
	自动计算重量 = "autoWeight", 
	自动计算成本价 = "autoCostPrice",
	货位 = "warehouseSlotName",
	货位ID = "warehouseSlotId"
}

export enum Platform {
	PDD = '拼多多',
	TB = '淘宝',
	FXG = '抖店',
	pdd = '拼多多',
	tb = '淘宝',
	fxg = '抖店',
	ALI = '1688',
	ali = '1688',
	JD = "京东",
	jd = "京东",
	KSXD = '快手',
	ksxd = '快手',
	xhs = '小红书',
	XHS = '小红书',
	c2m = '淘工厂',
	C2M = '淘工厂',
	sph = '视频号',
	SPH = '视频号',
	dw = '得物',
	DW = '得物',
	yz = '有赞',
	YZ = '有赞',
	ktt = '快团团',
	KTT = '快团团',
}

export enum ImportType {
	商品状态 = 'STATUS',
	商品链接 = 'LINK',
	商品ID = 'NUMIID',
	全部商品 = 'ALL'
}

export enum DPPrintSource {
	手动出库 = "sdck",
	采购入库 = "cgrk",
	到货入库 = "dhrk",
}