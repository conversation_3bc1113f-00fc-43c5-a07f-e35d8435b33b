/* eslint-disable prefer-promise-reject-errors */
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import Qs from 'qs';
import dayjs from 'dayjs';
import { isViteDevMode } from '@/constants/index';
import { getToken, setToken, logout } from './token';
import { getMd5Str, getNavigatorConnectionDownLink } from './util';
import pageCode from './pageCode';
import events from "@/utils/events";
import message from "@/components/message";
import { handleRequest, handleResponse } from './requestCacheClass';
import WhiteList from './timeoutWhiteList';
import { COUNT_API_LIST, LOOP_API_LIST } from './ignoreRequestList';

interface ProxyHandleProps {
	get: AxiosInstance['get'];
	post: AxiosInstance['post'];
}
const CancelToken = axios.CancelToken;

export const tokens = new Map();


// token 过期错误码
const INVALID_SESSION = [700, 701, 702, 703];

const ERP_VERSION = '1';

const instance = axios.create({
	baseURL: isViteDevMode() ? `/api` : '/',
	timeout: 300000,
});

const refreshCaptcha = (res: any) => {
	if (res.data?.errorCode === 1041) {
		events.emit('refreshCaptcha');
	}
};

const timeout = 2500;

const trackRequestTime = (response: AxiosResponse) => {
	try {
		const requestStartDate = response.config.headers.startDate;
		const sdl = response.config.headers.sdl;
		const edl = `${getNavigatorConnectionDownLink()}`;
		const rtt = `${window?.navigator?.connection?.rtt || -1}`;
		const nowDate = new Date().valueOf();
		const travelTime = nowDate - requestStartDate;
		const isTimeout = travelTime > timeout;
		const isNoWhiteList = !WhiteList.includes(response.config.url);
		if (isTimeout && isNoWhiteList) {
			window?.errorCollection?.dealTimeout(response, {
				travelTime,
				sdl,
				edl,
				rtt,
				requestStartDate: dayjs(requestStartDate).format('YYYY-MM-DD HH:mm:ss:SSS'),
				requestEndDate: dayjs(nowDate).format('YYYY-MM-DD HH:mm:ss:SSS'),
			});
		}
	} catch (e) {
		console.log(e);
	}
};

// 不需要空格过滤的白名单
const trimKeyWhiteList = ['separatorSign'];

// * params层级过深的情况下 需要把递归改为迭代
const trimParams = (params) => {
	if (typeof params === 'string') {
		return params.trim(); // 对字符串进行 trim 操作
	}

	if (Array.isArray(params)) {
		return params.map(trimParams); // 递归处理数组元素
	}

	if (typeof params === 'object' && params !== null) {
		for (let key in params) {
			if (trimKeyWhiteList.includes(key)) {
				continue;
			}
			params[key] = trimParams(params[key]); // 递归处理对象属性值
		}
	}

	return params;
};

// post请求头
instance.defaults.headers.post['Content-Type'] = 'application/json;charset=UTF-8';

// **路由请求拦截**
// http request 拦截器
instance.interceptors.request.use(
	(config) => {
		if (!window?.navigator?.onLine) {
			message.info('网络似乎开小差了');
		}

		// console.log("请求拦截器", config);
		try {
			config.data = trimParams(config.data);
			config.params = trimParams(config.params);

			/** 增加重复请求取消功能 */
			// 如果配置了 cancelToken，则使用 cancelToken 取消请求
			const cancelKey = config?.headers?.cancelKey;
			if (cancelKey) {
				if (tokens.has(cancelKey)) {
					tokens.get(cancelKey).cancel('取消请求');
					tokens.delete(cancelKey);
				}
				let source = CancelToken.source();
				tokens.set(cancelKey, source);
				config.cancelToken = source.token;
			}
		} catch (error) {
			console.error('request error:', error);
		}
		handleRequest({ config });
		if (
			config.headers?.['Content-Type']?.indexOf(
				'application/x-www-form-urlencoded',
			) > -1
		) {
			config.method === 'post' && (config.data = Qs.stringify(config.data));
		}

		// 如果是mock数据请求，直接跳过授权流程
		if (config.baseURL === process.env.APP_MOCK) {
			return config;
		}
		// 判断是否存在token，即判断用户是否登录，如果存在的话，则每个http header都加上token
		if (getToken()) {
			const token = getToken() || '';
			// 每个http header 都加上 token
			config.headers.qnquerystring = token;
		}
		config.headers.startDate = new Date().valueOf();
		config.headers["erp-version"] = ERP_VERSION;
		config.headers.sdl = `${getNavigatorConnectionDownLink()}`;
		return config;
	},
	(error) => {
		return Promise.reject(error.response);
	}
);

// http 响应 拦截器
instance.interceptors.response.use(
	(response: AxiosResponse) => {
		// console.log("拦截", response);
		trackRequestTime(response);
		message.destroy('batchRequest');
		const {
			headers: {
				qnquerystring,
				refresh_auth_token,
				hide_error_message = false,
			},
			config: {
				url,
				headers: {
					hideErrorMessage = false,
				}
			},
			data
		} = response;
		if (!LOOP_API_LIST.includes(url)) {
			window.lastRequestTime = new Date().getTime();
		}

		// 清理已完成请求的 cancelToken
		const cancelKey = response.config?.headers?.cancelKey;
		if (cancelKey && tokens.has(cancelKey)) {
			tokens.delete(cancelKey);
		}

		// console.log("响应拦截器", qnquerystring, response);
		handleResponse({ config: response.config });
		if (url === '/index/user/login' && response.data?.success) {
			if (data.data?.jumpDomain && !isViteDevMode()) {
				// 登录时，如果目标域名与当前域名不同，则在两个域名都写入token
				try {
					const url = new URL(data?.data?.jumpDomain);
					if (window.location.host != url.host) {
						const token = url.searchParams.get('token');
						setToken(token, getMd5Str(`${data.data?.userId}_${data.data?.subUserId}`));
					}
					location.href = data.data?.jumpDomain;
				} catch (error) {
					console.log('error', error);
				}
			}
			setToken(qnquerystring, getMd5Str(`${data.data?.userId}_${data.data?.subUserId}`));
		}
		// 存入token
		if (refresh_auth_token) {
			if (getToken() !== refresh_auth_token || !getToken()) {
				setToken(refresh_auth_token);

				// 打印中心也需要更新token
				// @ts-ignore
				comp.Print.Data.qnkdzsToken = refresh_auth_token;
			}
		} else if ((!getToken() || getToken() !== qnquerystring) && qnquerystring) {
			setToken(qnquerystring);

			// 打印中心也需要更新token
			// @ts-ignore
			comp.Print.Data.qnkdzsToken = qnquerystring;
		}

		// Do something with response data
		if (INVALID_SESSION.includes(response.data?.errorCode)) {
			logout();
			return Promise.reject(response.data);
		}
		// blob类型处理
		if (response.request.responseType.toLowerCase() === 'blob') {
			if (response.request._header_.returnResponse) return response;
			return response.data;
		}
		// 全局报错提示
		if (url === '/print/center/elec/cancelYdNo' && !response.data?.success) {
			return Promise.reject(response.data);
		}
		// console.log('response', response);
		if (url == '/trade/oss/image/upload') {
			return response.data;
		}
		if ((response.data && !response.data.success) || response.data?.errorCode) {
			refreshCaptcha(response);
			// response.data?.data 兼容打印中心
			
			let requestParams:any = {};
			try {
				requestParams = JSON.parse(response?.config?.data || '{}');
			} catch (error) {
				console.log('%c [ error ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', error);
			}
			if (response.data?.isHiddenErrorMessage || hideErrorMessage || hide_error_message || requestParams?.hideErrorMessage) {
				// axios通过`transformResponse` 在传递给 then/catch 前，允许修改响应数据
				// 这里增加 isHiddenErrorMessage配置 当为true时 隐藏错误提示 默认不隐藏
			} else {
				message.error(response.data?.subMessage || response.data?.errorMessage || response.data?.data || '请求失败', 5);
			}

			return Promise.reject({
				...(response.data || {}),
				requestUrl: response?.config?.url
			});
		}
		return response.data;
	},
	(error) => {
		const { response } = error;
		if (response) {
			// 清理已取消或错误请求的 cancelToken
			const cancelKey = response.config?.headers?.cancelKey;
			if (cancelKey && tokens.has(cancelKey)) {
				tokens.delete(cancelKey);
			}

			handleResponse({ config: response.config });
			const { status } = response;
			const requestStartDate = response.config.headers.startDate;
			const sdl = response.config.headers.sdl;
			const edl = `${getNavigatorConnectionDownLink()}`;
			const rtt = `${window?.navigator?.connection?.rtt || -1}`;
			const nowDate = new Date().valueOf();
			const travelTime = nowDate - requestStartDate;
			const extraInfo = {
				travelTime,
				sdl,
				edl,
				rtt,
				requestStartDate: dayjs(requestStartDate).format('YYYY-MM-DD HH:mm:ss:SSS'),
				requestEndDate: dayjs(nowDate).format('YYYY-MM-DD HH:mm:ss:SSS'),
			};

			// * 针对统计接口、轮训接口可不抛错
			const url = response.config?.url;
			if (!LOOP_API_LIST.includes(url)) {
				window.lastRequestTime = new Date().getTime();
			}
			if (COUNT_API_LIST.some(api => api.indexOf(url) > -1) || LOOP_API_LIST.some(api => api.indexOf(url) > -1)) {
				return Promise.reject(response.data);
			}
			if ([500, 502, 503].includes(status)) {
				message.error('服务暂不可用，请稍后再试，或联系客服处理');
			} else if ([302].includes(status)) {
				message.error('出错了！服务器返回了一个重定向错误。请检查您的请求是否正确，并稍后再试。');
			} else if (status == 404) {
				message.error(`${response.config.url}接口不存在`);
			} else {
				const data = response.data || {};
				const errorMsg = data.message || data.errorMessage || data.error;
				if (errorMsg) {
					message.error(errorMsg, 5);
				}
				// 接口请求错误监控
				window.errorCollection && window.errorCollection.dealHttpError(response.data, extraInfo);
			}
			return Promise.reject(response.data);
		} else {
			// * 当网络问题导致的response中没有config时如何处理
			handleResponse({
				config: {
					clear: true
				}
			});
		}
		// 主动取消和拦截的请求都不提示错误
		if (error.message && error.message.indexOf('重复的请求被主动拦截:') === -1 && !error.message.includes('取消请求')) {
			message.error(error.message, 5);
			return Promise.reject(error);
		}
		return Promise.reject(JSON.stringify(error));
	},
);


// export default instance;
export { instance };

const MAX_SPLIT_NUM = 200;

const handleParmas = (params: Array<unknown> | Object, maxSplitNum = MAX_SPLIT_NUM) => {
	if (Array.isArray(params)) {
		if (params.length > maxSplitNum) {
			let result = [];
			for (let i = 0; i < params.length; i += maxSplitNum) {
				result.push(params.slice(i, i + maxSplitNum));
			}
			return result;
		} else {
			return [params];
		}
	} else {
		let result = [];
		for (const key in params) {
			if (Object.prototype.hasOwnProperty.call(params, key)) {
				const element = params[key];
				if (Array.isArray(element) && element.length > maxSplitNum) {
					// * 理论上不应该存在两个数组需要同时进行分批处理
					// * 换而言之就是不支持
					for (let i = 0; i < element.length; i += maxSplitNum) {
						result.push({
							...params,
							[key]: element.slice(i, i + maxSplitNum)
						});
					}
				}
			}
		}

		return result.length ? result : [params];
	}
};

// * 多个result需要拼成批量时的返回
const dealHttpResponse = (result) => {
	console.log(result, 'dealHttpResponse');
	return result;
};


// * axios.post 代理
// * splitParams: 批量操作开启分批处理
// * notBatchRequest: 一个请求完毕之后再发起另一个请求 防止qps过高/只能单请求/有顺序要求的场景
// * maxSplitNum: 多少数量一批
// * 是否需要loading
const proxyHandle: ProxyHandleProps = {
	post: (...reset: any) => {
		return pageCode.wait().then(async() => {
			const [url = '', params = {}, options = { headers: {} }] = reset;
			const { headers: {
				splitParams,
				notBatchRequest,
				maxSplitNum,
				showLoading = true,
			} } = options;

			// * 目前暂不支持聚合接口返回的数据
			// * 所以依赖接口返回的场景 暂时不能通过统一分批处理
			if (splitParams) {
				// * 分割参数
				const paramsArr = handleParmas(params, maxSplitNum);
				let arrLen = paramsArr.length;
				let messageLoadingKey = `${url}_${new Date().getTime()}`;
				showLoading && message.open({
					type: 'loading',
					content: `正在处理中...${notBatchRequest ? `${1}/${arrLen}` : ''}`,
					key: messageLoadingKey,
					duration: 0,
				});
				if (notBatchRequest) {
					let result = [];
					for (const temp of paramsArr) {
						try {
							// eslint-disable-next-line no-await-in-loop
							const res = await instance.post.apply(this, [
								url,
								temp,
								options
							]);
							result.push(res);

							showLoading && message.open({
								type: 'loading',
								content: `正在拼命请求中... ${result.length}/${arrLen}`,
								key: messageLoadingKey,
								duration: 0,
							});
						} catch (error) {
							console.log("catch Error:::", error);
							showLoading && message.destroy(messageLoadingKey);
							result.push(error);
						}
					}
					showLoading && message.destroy(messageLoadingKey);
					return dealHttpResponse(result);
				} else {
					try {
						let result = await Promise.all(paramsArr.map(item => instance.post.apply(this, [
							url,
							item,
							options
						])));
						console.log("result::::::::", result);
						showLoading && message.destroy(messageLoadingKey);
						return dealHttpResponse(result);
					} catch (error) {
						showLoading && message.destroy(messageLoadingKey);
					}
				}
			} else {
				return instance.post.apply(this, reset);
			}
		});
	},
	get: (...reset: any) => {
		return pageCode.wait().then(() => {
			return instance.get.apply(this, reset);
		});
	}
};

export default proxyHandle;
