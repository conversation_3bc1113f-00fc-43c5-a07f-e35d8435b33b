/*
 * @Author: try <EMAIL>
 * @Date: 2024-04-09 17:11:29
 * @Description:
 */
import dayjs from "dayjs";
import Ads, { Ads as ADs } from '@raycloud/ads/dist-px/ads.js';
import { configure } from "mobx";
import createTatami from "./Tatami";
import events from './events';
import { initPointTrack } from "./pointTrack";
import { ErrorCollection } from "./errLogCollection";
import { porinMergePrintOrderNumber, porinMergePrintNumber, porinAddTempGroup, otherPorint } from '@/utils/pointTrack/utils';
import message from "@/components/message";
import { isViteDevMode } from "@/constants";

export const initApp = () => {
	dayjs?.locale?.('zh-cn');
	message.config({
		duration: 3,
		top: 100,
		maxCount: 5,
	});
	if (isViteDevMode()) {
		window.Ads = ADs; // 挂载到window上方便全局使用
	} else {
		window.Ads = Ads; // 挂载到window上方便全局使用
	}
	if (!window.eventBus) {
		window.eventBus = events;
		events.sub('porinMergePrintOrderNumber', porinMergePrintOrderNumber, false);
		events.sub('porinMergePrintNumber', porinMergePrintNumber, false);
		events.sub('porinAddTempGroup', porinAddTempGroup, false);
		events.sub('otherPorint', otherPorint, false);
	}
	if (!window.Tatami) {
		window.Tatami = createTatami();
	}

	if (!window.erpData) {
		window.erpData = {};
	}

	initPointTrack();
	const errorCollection = new ErrorCollection('ERP');
	window.errorCollection = errorCollection;
	// 防止浏览器路由后退
	window.addEventListener('popstate', e => {
		history.pushState(null, null, location.href);
	});

	configure({
		enforceActions: "never",
	});
};
