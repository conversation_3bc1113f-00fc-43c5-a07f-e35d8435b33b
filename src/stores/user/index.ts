import { makeAutoObservable, runInAction } from "mobx";
import _, { isEmpty } from "lodash";
import dayjs from "dayjs";
import { platform } from '@/types/schemas/common';
import {
	getUserInfoApi,
	IndexPlatformShopGetPlatformShopsApi,
	AuthorityGetAuthorityTemplateApi,
	IndexSettingEditPopupSettingApi,
	SettingGetUserSettingApi,
	SettingGetSystemSettingApi,
	IndexSettingGetSenderSettingApi,
	IndexSettingGetInvoicesSenderSettingApi,
	IndexNewUserGuideSelectApi,
	IndexNewUserGuideUpdateApi,
	ItemGetNotImportShopsApi,
	ItemSysSkuGetGroupPackageOperationLogApi,
	IndexVirtualPlatformShopGetPlatformShopsApi,
	getCategoryTreeApi
} from "@/apis/user";
import {
	UserInfoInf,
	IndexPlatformShopGetPlatformShopsRequest,
	IndexPlatformShopGetPlatformShopsResponseObj,
	SettingGetSystemSettingResponse,
	IndexSettingGetSenderSettingResponse,
	IndexSettingGetSenderSettingRequest,
	SettingGetUserSettingRequest,
	IndexSettingGetInvoicesSenderSettingResponse,
	IndexSettingGetInvoicesSenderSettingRequest,
	IndexNewUserGuideSelectResponse,
	ItemGetNotImportShopsResponse,
	IndexAuthorityGetAuthorityTemplateResponse,
	CategoryTreeItem,
	CategoryTreeRequest
} from "@/types/schemas/user";
import { TradeUserAreaGetAreasResponse } from "@/types/trade/search/search";
import { TradeDictQueryDictApi, TradeUserAreaGetAreasApi, UserCheckBlackFlag, TradeDictInsertDictApi } from "@/apis/trade/search";
import { avoidRepeatReq, getMd5Str } from "@/utils/util";
import { selectMergeConfigApi } from "@/apis/warehouse/syncstock";
import { stockVersion, HEADER_TABS_TOFIXED_LIST, PLAT_OTHER, USER_TYPE, isViteDevMode, authorizeMap, authUserName } from '@/constants';
import { orderPreOccupiedStock, weightUnit } from '@/pages/Index/Settings/System/constants';
import { MenuBean, MenuType } from "@/components-biz/layouts/HeaderMenu/menuBean";
import { local } from "@/libs/db";
import Pointer from "@/utils/pointTrack/constants";
import { MenuData } from "@/components-biz/layouts/HeaderMenu/menu";
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { FunctionPermissionEnum } from "@/utils/permissionCheck/functionPermissionCheck";
import { shopTypeEnum } from "@/pages/Index/Shops/components/addShop";
import { getFXGToken } from "@/apis/trade";
import { TradeLivePrintGetAuthorListApi, TradeLivePrintGetLiveShopListApi } from "@/apis/trade/live";
import { TradeLivePrintGetAuthorListResponse, TradeLivePrintGetLiveShopListResponse } from "@/types/trade/index/live";

export class UserStore {
	constructor() {
		makeAutoObservable(this);
	}

	getUserInfoIng = false;

	/** version 1库存版 2零库存版
	 * versionType 0 旧版零库存版  1 新版零库存版
	 * 用户信息
	 */
	userInfo: UserInfoInf = {
		mobile: null,
		userId: null,
		version: null,
	};

	get isStockVersion() {
		return +this.userInfo?.version === stockVersion.库存版;
	}

	get isShowAutoDecode() {
		let whiteListSetting = JSON.parse(this.userInfo?.whiteListSetting || '{}');
		return whiteListSetting?.showAutoDecode === 1;
	}

	// 库存版且开启扣减库存
	get isStockAllocationVersion() {
		return +this.userInfo?.version === stockVersion.库存版 && this.inventoryDeduct && this.systemSetting?.orderPreOccupiedStock != orderPreOccupiedStock.不占用库存;
	}

	// isShowZeroStockVersion为true则代表为零库存版本 fasle为库存版本
	get isShowZeroStockVersion() {
		return +this.userInfo?.version === 2 && +this.userInfo?.versionType === 1;
	}

	get inventoryDeduct() {
		return this.systemSetting?.inventoryDeduct || 0;
	}

	get remainDays() {
		return this.userInfo?.remainingTime || 100;
	}

	get isShowKg() {
		return this.userSetting?.weightUnit == weightUnit.显示kg;
	}

	// 这是常规版本，注意不要使用这个判断是不是供应商
	get isSupplierAccount() {
		return this.userInfo?.userType === USER_TYPE.常规版; // 供应商&分销商
	}

	get isDistributorAccount() {
		return this.userInfo?.userType === USER_TYPE.免费分销商;
	}

	get isFreeSupplierAccount() {
		return this.userInfo?.userType === USER_TYPE.免费版供应商用户;
	}

	get isDefaultAccount() {
		return this.userInfo?.userType === USER_TYPE.默认; // 既不是供应商又不是分销商
	}

	get hasCostPricePermission() {
		return this.hasFieldsPermission(FieldsPermissionEnum.成本价);
	}

	// 类目树列表
	categoryTbTreeList: CategoryTreeItem[] = null;

	// 获取类目树列表
	async getCategoryTreeList(params?: CategoryTreeRequest) {
		try {
			const response = await getCategoryTreeApi(params);
			console.log(response, 'getCategoryTreeApigetCategoryTreeApi');
			runInAction(() => {
				this.categoryTbTreeList = response || [];
			});
			return this.categoryTbTreeList;
		} catch (error) {
			runInAction(() => {
				this.categoryTbTreeList = [];
			});
			return [];
		}
	}

	// 获取细分的身份版本
	get getAuthUserType() {
		let matchedKey = null; // AUTH_USER_TYPE 是字符串
		const { version, userType } = this.userInfo;
		if (version !== null && userType !== null) {
			for (const key in authorizeMap) {
				if (authorizeMap[key].version == version && authorizeMap[key].userType == userType) {
					matchedKey = key;
				}
			}
			console.log('%c [ 用户版本 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', matchedKey, authUserName[matchedKey]);
		}
		return matchedKey;
	}

		// 缓存波次管理权限值
		_waveManagePermission: boolean = undefined;

		// 异步获取波次管理权限
		async getWaveManagePermission() {
			try {
				// 如果已经有缓存值且不是强制刷新，直接返回
				if (this._waveManagePermission !== undefined) {
					return this._waveManagePermission;
				  }
				const response = await TradeDictQueryDictApi({ userDictEnum: "WAVE_PDA_PICKING" });

				// 根据接口返回值设置权限
				if (response && response.value) {
					const permissionValue = JSON.parse(response.value)?.enabled;
					runInAction(() => {
						this._waveManagePermission = !!permissionValue;
						window.erpData._waveManagePermission = this._waveManagePermission;
					});
					return this._waveManagePermission;
				} else {
					runInAction(() => {
						this._waveManagePermission = false;
					});
					return false;
				}
			} catch (error) {
				console.error('Get wave manage permission error:', error);
				runInAction(() => {
					this._waveManagePermission = false;
				});
				return false;
			}
		}

		/**
		 * 判断是否有波次管理权限
		 * 条件：
		 * 1. 不是零库存版本
		 * 2. 有权限
		 */
		get hasWaveManagePermission() {
			// 零库存版本没有波次管理权限
			if (this.isShowZeroStockVersion) {
				return false;
			}

			// 使用缓存的权限值
			if (this._waveManagePermission !== undefined) {

				return this._waveManagePermission;
			}

			// 异步获取权限并缓存
			this.getWaveManagePermission();

			// 默认返回 false，等待异步获取完成后更新
			return false;
		}

	hasFieldsPermission = (fields: FieldsPermissionEnum) => {
		const { dataAuthority, subUserId } = userStore.userInfo;
		if (!subUserId) return true;
		try {
			if (dataAuthority) {
				const limitPermissions = JSON.parse(dataAuthority);
				return !limitPermissions.includes(fields);
			} else {
				return true;
			}
		} catch (error) {
			console.log('解析字段权限报错', error);

		}
	};

	hasFunctionPermission = (permission: FunctionPermissionEnum) => {
		const { funcAuthority, subUserId } = userStore.userInfo;

		if (!subUserId) return true;
		try {
			// console.log('%c [ funcAuthority ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', funcAuthority);
			if (funcAuthority) {
				const limitPermissions = JSON.parse(funcAuthority);
				// console.log('%c [ limitPermissions ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', limitPermissions);
				return !limitPermissions.includes(permission);
			} else {
				return true;
			}
		} catch (error) {
			console.log('解析功能权限报错', error);

		}
	}

	shopType = -1;

	setShopType(type: shopTypeEnum) {
		this.shopType = type;
	}

	/**
	 * 店铺列表
	 */
	shopList: IndexPlatformShopGetPlatformShopsResponseObj[] = null;

	/**
	 * 所有达人列表
	 */
	authorList: TradeLivePrintGetAuthorListResponse['data'] = [];

	/**
	 * 正在直播中的店铺列表
	 */
	liveShopList: TradeLivePrintGetLiveShopListResponse['data'] = [];

	/**
	 * 面单店铺列表
	 */
	ebillShopList: IndexPlatformShopGetPlatformShopsResponseObj[] = null;

	sortShopListVisible: boolean = false; // 店铺排序

	setSortShopListVisible = (val: boolean) => {
		this.sortShopListVisible = val;
	}

	/**
	 * 权限模版
	 */
	permTemp: IndexAuthorityGetAuthorityTemplateResponse['data'] = null;

	kddSenderTableVisible: boolean = false;

	fhdSenderTableVisible: boolean = false;

	// 当前选中的shopCard
	curShopCardData: IndexPlatformShopGetPlatformShopsResponseObj = null;

	// 初始化版本步骤
	initStep: number = null;

	// 用户个性化配置
	userSetting: SettingGetUserSettingRequest = null;

	statusList: Array<any> = [
		{
			title: '全部',
			activeKey: 0,
			noDataTips: '您还没有绑定店铺，赶紧绑定店铺，让我们帮您提高运营效率',
			tabDataList: [],
			isCanAddNewShop: true
		},
		{
			title: '正常',
			activeKey: 1,
			noDataTips: '未找到有效店铺，赶紧绑定店铺，让我们帮您提高运营效率',
			tabDataList: [],
			isCanAddNewShop: true

		},
		{
			title: '已暂停',
			activeKey: 3,
			noDataTips: '未找到已暂停的店铺',
			tabDataList: [],
			isCanAddNewShop: false
		},
		{
			title: '已失效',
			activeKey: 2,
			noDataTips: '未找到已失效的店铺',
			tabDataList: [],
			isCanAddNewShop: false
		}
	]

	tabAcitiveKey: string = null

	searchParams: any = {
		platformType: null,
		sellerNick: null,
		// status: null
	}

	// 首页弹窗展示顺序控制 1:新手引导 2:版本到期提醒 3:同步 4:店铺异常
	homeModalVisibleControl: number = 1;

	setSearchParams(data) {
		this.searchParams = data;
	}

	filterShop(dataSource: Array<any>) {
		this.statusList.forEach(statusListItem => {
			if (statusListItem.activeKey === 0) statusListItem.tabDataList = dataSource;
			else if (statusListItem.activeKey === 1) statusListItem.tabDataList = dataSource.filter((item: any) => item.tokenStatus === 1 && item.status === 1);
			else if (statusListItem.activeKey === 2) statusListItem.tabDataList = dataSource.filter((item: any) => item.tokenStatus === 0 || item.status === 2);
			else if (statusListItem.activeKey === 3) statusListItem.tabDataList = dataSource.filter((item: any) => item.tokenStatus === 1 && item.status === 0);
		});
	}

	setUserInfo(val: UserInfoInf) {
		this.userInfo = val;
		sessionStorage.setItem("erp_userInfo", JSON.stringify(val));
	}

	sleep(time: number) {
		return new Promise((resolve) => {
			setTimeout(resolve, time);
		});
	}

	setGetUserInfoIng(val: boolean) {
		this.getUserInfoIng = val;
	}

	/**
	* 获取用户信息
	* @returns
	*/
	async getUserInfo(refresh: boolean = false) {
		try {
			while (this.getUserInfoIng) {
				// eslint-disable-next-line no-await-in-loop
				await this.sleep(200);
			}
			if (this.userInfo?.userId && !refresh) {
				return this.userInfo;
			} else {
				this.setGetUserInfoIng(true);
				console.log("updateUserInfo", refresh);
				const ret: UserInfoInf = await getUserInfoApi();
				try {
					if (ret?.popupSetting) {
						ret.popupSetting = JSON.parse(ret.popupSetting);
					}
				} catch (error) {
					console.log(error);
				}
				// 没有version 或者初始化仓库
				if (!(ret?.version === 1 || ret?.version === 2)) {
					location.href = `?userId=${getMd5Str(`${ret.userId}_${ret?.subUserId}`)}#/initialize`;
				}
				if (!ret.hasExpired && !ret?.popupSetting?.initStock) {
					location.href = `?userId=${getMd5Str(
						`${ret.userId}_${ret?.subUserId}`
					)}#/initialize`;
				}
				this.setGetUserInfoIng(false);
				if (ret.hasExpired) {
					const _userInfo: UserInfoInf = {
						expireTime: ret?.expireTime,
						userId: ret?.userId,
						hasExpired: ret?.hasExpired,
						userName: ret?.userName,
						version: ret?.version,
					};
					if (ret?.subUserId) {
						_userInfo.subUserId = ret?.subUserId;
					}
					this.setUserInfo(_userInfo);
					return _userInfo;
				} else {
					this.setUserInfo(ret);
					return ret;
				}
			}
		} catch (error) {
			this.setGetUserInfoIng(false);
			console.error("getUserInfo报错： ", error);
		}
	}

	userAuthoritySetting = null;

	getUserAuthoritySettingLoading = false;

	// 获取用户页面权限配置 - 高级版白名单
	async getUserAuthoritySet(refresh: boolean = false) {
		try {
			if (this.getUserAuthoritySettingLoading) {
				await this.sleep(200);
			}
			if (this.userAuthoritySetting && !refresh) {
				return this.userAuthoritySetting;
			}
			this.getUserAuthoritySettingLoading = true;
			const res = await TradeDictQueryDictApi({ userDictEnum: "USER_AUTHORITY_SET" });
			this.getUserAuthoritySettingLoading = false;
			if (res.value) {
				this.userAuthoritySetting = JSON.parse(res.value);
				return this.userAuthoritySetting;
			}
		} catch (error) {
			this.getUserAuthoritySettingLoading = false;
			console.log("getUserAuthoritySet error:", error);
		}

	}

	BlackUserAuthority:boolean = false

	// 获取用户页面权限配置 - 黑名单
	async getUserAuthorityBlacklist() {
		try {
			if (this.BlackUserAuthority) {
				return this.BlackUserAuthority;
			}
			const res = await UserCheckBlackFlag();
			this.BlackUserAuthority = !!res;
			return this.BlackUserAuthority;
		} catch (error) {
			console.log("getUserAuthoritySet error:", error);
		}

	}

	/**
	 * 组合打包日志数量查询 此配置用于组合货品库存下线 判断菜单中是否展示组合货品打包
	 * @returns
	 */
	groupPackageNum = null;

	async getItemSysSkuGetGroupPackageOperationLog() {
		try {
			if (!isNaN(this.groupPackageNum) && this.groupPackageNum !== null) {
				return this.groupPackageNum;
			} else {
				let num = await ItemSysSkuGetGroupPackageOperationLogApi();
				this.groupPackageNum = num;
				return num;
			}
		} catch (error) {
			console.log("getItemSysSkuGetGroupPackageOperationLog error:", error);
			return 2;
		}
	}

	/**
	 * 获取用户个性化设置
	 * @returns
	 */
	isGetUserSetting = false;

	async getUserSetting() {
		// console.log('getUserSetting');

		while (this.isGetUserSetting) {
			// eslint-disable-next-line no-await-in-loop
			await this.sleep(200);
		}
		if (this.userSetting) {
			// console.log('getUserSetting mobx');
			window.erpData.userSetting = this.userSetting;
			return this.userSetting;
		}
		this.isGetUserSetting = true;
		let res = await this.getUserInfo();
		const { userId } = res;
		const data = await SettingGetUserSettingApi({ userId });
		runInAction(() => {
			this.isGetUserSetting = false;
			this.userSetting = data;
			window.erpData.userSetting = data;
		});
		return data;
	}


	bindShopsList: ItemGetNotImportShopsResponse['data'];

	otherShopList: ItemGetNotImportShopsResponse['data'];

	setBindShopsList(val: ItemGetNotImportShopsResponse['data']) {
		this.bindShopsList = val;
		this.otherShopList = val.filter((item) => item.platform?.toLocaleLowerCase() === PLAT_OTHER);
	}

	eBillBindShopsList: ItemGetNotImportShopsResponse['data'];

	setEbillBindShopsList(val: ItemGetNotImportShopsResponse['data']) {
		this.eBillBindShopsList = val;
	}

	shopTokenCache = {

	}

	getShopToken = (list) => {
		const fxgShop = list.filter(s => s.platform == 'fxg');
		console.log('getShopToken', list);

		// 先从localStorage中获取缓存的token
		fxgShop.forEach(shop => {
			if (!this.shopTokenCache[shop.sellerId]) {
				const cacheKey = `fxgToken_${shop.sellerId}`;
				const cachedData = local.get(cacheKey);
				if (cachedData && cachedData.encryptToken) {
					this.shopTokenCache[shop.sellerId] = cachedData.encryptToken;
				}
			}
		});

		// 收集需要获取token的店铺ID（缓存中没有的）
		const sellerIdsToFetch = fxgShop
			.filter(element => !this.shopTokenCache[element.sellerId])
			.map(element => element.sellerId);

		if (sellerIdsToFetch.length > 0) {
			getFXGToken({ sellerIdList: sellerIdsToFetch }).then(res => {
				// 处理返回的数组格式数据
				if (Array.isArray(res)) {
					res.forEach(item => {
						if (item.sellerId && item.encryptToken) {
							// 更新内存中的缓存
							this.shopTokenCache[item.sellerId] = item.encryptToken;

							// 根据expireTime设置localStorage缓存
							const cacheKey = `fxgToken_${item.sellerId}`;
							const cacheData = {
								encryptToken: item.encryptToken,
								sellerId: item.sellerId,
								isOpenFxgMC: item.isOpenFxgMC
							};

							// 如果有过期时间，则使用过期时间设置缓存，比实际过期时间短8秒
							if (item.expireTime) {
								// 计算从现在到过期时间的毫秒数
								const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
								const expiresIn = (item.expireTime - now) * 1000; // 转换为毫秒

								// 确保过期时间是有效的正数，并比接口返回的过期时间短8秒
								if (expiresIn > 0) {
									// 使用local.set方法设置带过期时间的缓存，比实际过期时间提前8秒过期
									const safeExpiresIn = expiresIn - 8000; // 提前8秒过期（8000毫秒）
									local.set(cacheKey, cacheData, safeExpiresIn > 0 ? safeExpiresIn : 0);
								}
							}
						}
					});
				}
			});
		}
	}

	// 有效的平台店铺数量
	validPlatformShopSize: number = 0;

	setValidPlatformShopSize(size: number) {
		this.validPlatformShopSize = size;
	}
   
	/**
	 * 获取店铺列表
	 * @param params
	 */
	async updateShopList(params?: IndexPlatformShopGetPlatformShopsRequest, notUpdateList: boolean = false) {
		const { userId } = await this.getUserInfo();

		const { list, validPlatformShopSize } = await avoidRepeatReq(IndexPlatformShopGetPlatformShopsApi, {
			...params,
			userId,
			refresh: 1,
		});
		const _list = this.UpdateShopsRemainDays(list);
		 // 更新有效的平台店铺数量
		 this.setValidPlatformShopSize(Number(validPlatformShopSize || '0'));
		// this.getShopToken(list);
		const ret = await avoidRepeatReq(ItemGetNotImportShopsApi, {});
		runInAction(() => {
			if (!notUpdateList) {
				this.shopList = _list;
				// 向window变量中挂载店铺数据，打印中心需要
				if (!window.erpData) {
					window.erpData = {};
				}
				window.erpData.shopList = _list;
			}
			this.filterShop(_list);
			this.setShopsLoading(false);
			// this.bindShopsList = ret;
			this.setBindShopsList(ret);
		});

		/**
		 * 如果有一个店铺授权成功，需要更新newUserGuide.shopAuth。
		 * 解决新用户从服务市场跳转过来以后，在新人引导中，依然展示店铺绑定步骤的bug
		 */
		const shopAuth = list.some(item => item.status === 1);
		if (shopAuth) {
			this.setNewUserGuide({
				shopAuth: true,
			});
		}
		return _list;
	}

	async updateEbillShopList({ params, notUpdateList = false, isUpdateTab = false }:{params?: IndexPlatformShopGetPlatformShopsRequest, notUpdateList?: boolean, isUpdateTab?: boolean}) {
		const { userId } = await this.getUserInfo();

		const { list } = await avoidRepeatReq(IndexVirtualPlatformShopGetPlatformShopsApi, {
			...params,
			userId,
			refresh: 1,
		});
		const _list = this.UpdateShopsRemainDays(list);
		runInAction(() => {
			if (!notUpdateList) {
				this.ebillShopList = _list;
				// if (!window.erpData) {
				// 	window.erpData = {};
				// }
				// window.erpData.ebillShopList = _list;
			}
			this.filterShop(_list);
			this.setEbillBindShopsList(_list);
			this.setShopsLoading(false);
		});
		return _list;
	}


	// 店铺剩余时间 在店铺选项中增加 软件即将过期判断
	UpdateShopsRemainDays(list: any) {
		let _shopList: Array<any> = [];
		const _now = new Date();
		list.forEach((data: any) => {
			const _remainDays = dayjs(data.sellerExpireTime).diff(dayjs(_now), 'days');
			if (_remainDays > 0 && _remainDays < 15) {
				_shopList.push({
					...data,
					_status: 3,
					_remainDays
				});
			} else {
				_shopList.push({
					...data,
					_status: data.status,
					_remainDays
				});
			}
		});
		return _shopList;
	}

	getShopList = async(params?: IndexPlatformShopGetPlatformShopsRequest) => {
		const list = [];
		if (params?.isHasEbillPlat) {
			if (!this.eBillBindShopsList) {
				const res = await this.updateEbillShopList(params);
				list.push(...res);
			} else {
				list.push(...this.eBillBindShopsList);
			}
		}
		if (this.shopList) {
			list.push(...this.shopList);
		} else {
			const res = await this.updateShopList(params);
			list.push(...res);
		}
		return list;
	}

	setKddSenderTableVisible(visible: boolean) {
		this.kddSenderTableVisible = visible;
	}

	setFhdSenderTableVisible(visible: boolean) {
		this.fhdSenderTableVisible = visible;
	}

	setCurShopCardData(value: IndexPlatformShopGetPlatformShopsResponseObj) {
		this.curShopCardData = value;
	}

	/**
	 * 获取权限模版
	 */
	async getPermTemp(refresh: boolean = false) {
		if (this.permTemp && !refresh) {
			return this.permTemp;
		} else {
			const data = await AuthorityGetAuthorityTemplateApi();
			this.permTemp = data;
			return data;
		}
	}

	* updateSignConfig(config: { [k: string]: any }) {
		const param = { popupSetting: config };
		if (this.userInfo?.popupSetting) {
			param.popupSetting = {
				...this.userInfo.popupSetting,
				...param.popupSetting,
			};
		}
		yield IndexSettingEditPopupSettingApi(param);
	}

	/**
	 * 获取一些前端标识信息
	 * @returns
	 */
	getSignConfig() {
		let config: { [k: string]: any } = {};
		try {
			if (this.userInfo?.popupSetting) {
				config = JSON.parse(this.userInfo.popupSetting);
			}
		} catch (error) {
			console.log(error);
		}
		return config;
	}

	shopsLoading: boolean = false;

	setShopsLoading(val: boolean) {
		this.shopsLoading = val;
	}

	/**
	 * 设置用户高级设置
	 * @param userSetting
	 */
	setUserSetting(userSetting: any) {
		this.userSetting = {
			...this.userSetting,
			...userSetting,
		};
		window.erpData.userSetting = this.userSetting;
	}

	/** 系统设置 */
	systemSetting: SettingGetSystemSettingResponse["data"] = null;

	setSystemSetting(data: SettingGetSystemSettingResponse["data"]) {
		this.systemSetting = {
			...this.systemSetting,
			...data
		};
		window.erpData.systemSetting = this.systemSetting;
	}

	async getSystemSetting(refresh: boolean = false) {
		console.log(this.systemSetting, refresh);
		if (this.systemSetting && !refresh) {
			// console.log('getSystemSetting', this.systemSetting);
			window.erpData.systemSetting = this.systemSetting;
			return this.systemSetting;
		}
		const { userId } = await this.getUserInfo();
		let data = await avoidRepeatReq(SettingGetSystemSettingApi, { userId });
		const selectMergeData = await avoidRepeatReq(selectMergeConfigApi, {});
		if (selectMergeData) {
			data.mergeOrder = selectMergeData.mergeOrder || 1; // 是否合并订单,1不合并,2合并
			data.mergeMaxSize = selectMergeData.mergeMaxSize || ""; // 最大合单数量
			data.mergeOrderLastChangeTime = selectMergeData.mergeOrderLastChangeTime || ""; // 自动合单上次修改时间
			data.mergeStrategyConfig = selectMergeData.mergeStrategyConfig || {}; // 合单策略
		}
		if (data?.groupStockNumOutWay === undefined) {
			data.groupStockNumOutWay = 0;
		}
		if (data?.inventoryDeductTime) {
			data.inventoryDeductTime = dayjs(data.inventoryDeductTime, 'YYYY-MM-DD HH:mm:ss');
		}
		if (data?.extendInfo) {
			data = {
				...data,
				...data.extendInfo
			};
			if (data?.extendInfo?.itemCustomAttribute === undefined) {
				data.extendInfo.itemCustomAttribute = 0;
			}
		}

		runInAction(() => {
			this.systemSetting = data || {};
			window.erpData.systemSetting = this.systemSetting;
		});
		return data;
	}

	/** 快递单发件人设置 */
	senderSetting: IndexSettingGetSenderSettingResponse["data"] = null;

	setSenderSetting(val: IndexSettingGetSenderSettingResponse["data"]) {
		this.senderSetting = val;
	}

	async getSenderSetting(params?: IndexSettingGetSenderSettingRequest) {
		if (this.senderSetting && !params) {
			return this.senderSetting;
		}
		const data = await IndexSettingGetSenderSettingApi(params || {
			pageNo: 1,
			pageSize: 300,
			userId: this.userInfo?.userId
		});
		const canUpdate = !params || (!params.senderName && !params.senderMobile && params.pageSize >= 300);
		canUpdate && runInAction(() => {
			this.senderSetting = data || {};
		});

		return data || {};
	}

	/** 发货单发件人设置 */
	invoicesSenderSetting: IndexSettingGetInvoicesSenderSettingResponse["data"] = null;

	setInvoicesSenderSetting(val: IndexSettingGetInvoicesSenderSettingResponse["data"]) {
		this.invoicesSenderSetting = val;
	}

	async getInvoicesSenderSetting(params?: IndexSettingGetInvoicesSenderSettingRequest) {
		if (this.invoicesSenderSetting && !params) {
			return this.invoicesSenderSetting;
		}
		const data = await IndexSettingGetInvoicesSenderSettingApi(params || {
			pageNo: 1,
			pageSize: 300,
			userId: this.userInfo?.userId
		});
		runInAction(() => {
			this.invoicesSenderSetting = data || {};
		});
		return data || {};
	}

	/** userAreas */
	userArea: TradeUserAreaGetAreasResponse["data"] = null;

	async getUserArea(isUpdate = false) {
		if (this.userArea && !isUpdate) {
			return this.userArea;
		} else {
			const data = await TradeUserAreaGetAreasApi();
			runInAction(() => {
				this.userArea = data || [];
			});
			return data || [];
		}
	}

	newUserGuide: IndexNewUserGuideSelectResponse['data'] = null;

	setNewUserGuide(guide: IndexNewUserGuideSelectResponse['data'], updateApi = true) {
		if (this.newUserGuide) {
			let hasChange = false;

			for (const key in guide) {
				if (Object.prototype.hasOwnProperty.call(guide, key)) {
					const element = guide[key];
					if (element !== this.newUserGuide[key]) {
						hasChange = true;
					}
				}
			}
			if (updateApi && hasChange) {
				IndexNewUserGuideUpdateApi({ ...this.newUserGuide, ...guide });
			}

			this.newUserGuide = {
				...this.newUserGuide,
				...guide,
			};
		} else if (this.userInfo?.showNewUserGuide) {
			this.getNewUserGuide().then(res => {
				this.setNewUserGuide(guide, updateApi);
			});
		}
	}


	async getNewUserGuide() {
		const res = await avoidRepeatReq(IndexNewUserGuideSelectApi, {});
		runInAction(() => {
			this.newUserGuide = res;
			if (this.isShowZeroStockVersion) {
				this.setNewUserGuide({
					itemSync: true,
				});
			}
		});
		return res;
	}

	getHomeModalVisibleControl() {
		return this.homeModalVisibleControl;
	}

	setHomeModalVisibleControl(control: number) {
		this.homeModalVisibleControl = control;
	}

	// * 导航栏tab页
	cacheTabList: MenuBean[] = (() => {
		let list: MenuBean[] = [...this.defaultCacheTabList];
		let headerTabsToFixedList = local.get(HEADER_TABS_TOFIXED_LIST) || [];
		if (headerTabsToFixedList.length) {
			list = list.concat([...headerTabsToFixedList]);
		}
		return list;
	})();

	setCacheTabListToDefault = (additionalTabList: MenuBean[]) => {
		let temp = [...this.defaultCacheTabList, ...local.get(HEADER_TABS_TOFIXED_LIST) || [], ...additionalTabList];
		this.cacheTabList = _.uniqWith(temp, _.isEqual);
	}

	get defaultCacheTabList() {
		if (!this.userInfo?.userId) return [];
		if (this.userInfo.subUserId) {
			let authorityDetail = JSON.parse(this.userInfo.authorityDetail || '[]')?.filter((o: string) => o);
			if (authorityDetail.length === 0) {
				return [];
			} else {
				MenuData?.forEach(item => {
					if (authorityDetail.includes(item.id) && item.path) {
						return [item];
					}
					item?.children?.forEach(citem => {
						if (authorityDetail.includes(citem.id) && citem.path) {
							return [citem];
						}
					});
				});
			}
			return [];
		} else {
			return [{
				name: '首页',
				path: '/',
				id: '1',
				type: MenuType.Item,
				version: ['1', '2'],
				point: Pointer.顶部菜单栏_首页
			}];
		}
	}

	isShowSmsVerifyModal: boolean = false;

	setIsShowSmsVerifyModal = (val: boolean) => {
		this.isShowSmsVerifyModal = val;
	}

	switchEnvObj = {
		env: '',
		currentTime: -1,
		// 0: 登陆跳转1：弹框提醒2: 弹框提醒强制
		cutType: '',
		cutMsg: '',
		notFirst: false, // 是否是第一次
	}

	setSwitchEnvObj = (val: typeof this.switchEnvObj) => {
		this.switchEnvObj = val;
	}

	NacosConfig : any= {
		platform: '',
		global: false,
		showSplitOrder: false
	}

	get isShowSplitMenu() {
		return true;
		// return this.NacosConfig.showSplitOrder == 'true' || this.NacosConfig.showSplitOrder == true || this.NacosConfig.showSplitOrder == 1;
	}

	getNacosConfig = () => {
		return this.NacosConfig;
	}

	setNacosConfig = (config) => {
		return this.NacosConfig = config;
	}

	feNacosConfig = {}

	setFeNacosConfig(config = {}) {
		this.feNacosConfig = config;
	}

	getFeNacosConfig() {
		if (!isEmpty(this.feNacosConfig)) return this.feNacosConfig;
		const url = isViteDevMode() ? "/api/index/fe/nacos/getConfig" : '/index/fe/nacos/getConfig';
		return new Promise((resolve) => {
			fetch(url)
				.then((response) => {
					if (!response.ok) {
						throw new Error("Fetch Request failed");
					}
					return response.json();
				})
				.then((res) => {
					const data = JSON.parse(res);
					console.log("nacos getConfig:", data);
					resolve(data);
					this.setFeNacosConfig(data);
				})
				.catch((error) => {
					console.error("Fetch Request failed:", error);
				});
		});
	}

	/**
	 * 获取达人列表
	 * @param refresh 是否刷新
	 * @returns
	 */
	async getAuthorList(refresh: boolean = false) {
		if (this.authorList?.length && !refresh) {
			return this.authorList;
		}
		try {
			const res = await TradeLivePrintGetAuthorListApi({});
			runInAction(() => {
				this.authorList = res;
			});
		} catch (error) {
			console.error('获取达人列表失败:', error);
			runInAction(() => {
				this.authorList = [];
			});
		}
	}

	// 获取正在直播中的店铺
	async getLiveShopList(refresh: boolean = false) {
		if (this.liveShopList?.length && !refresh) {
			return this.liveShopList;
		}

		try {
			const res = await TradeLivePrintGetLiveShopListApi({});
			runInAction(() => {
				this.liveShopList = res;
			});
		} catch (error) {
			console.error('获取正在直播中的店铺失败:', error);
			runInAction(() => {
				this.liveShopList = [];
			});
		}
	}

	fakerTradeConfig: any = null;

	// 获取直播虚拟订单配置-子账号级别
	async getFakerTradeConfig() {
		const defaultConfig = {
			intervalSec: 30,
			fakerTradeFlag: '',
			fakerTradeItemList: []
		};
		try {
			const res = await TradeDictQueryDictApi({ userDictEnum: 'LIVE_TRADE_PRINT_FAKER_TRADE_CONFIG' });

			let userConfig = {
				...defaultConfig,
			};
			try {
				userConfig = JSON.parse(res?.value || '{}');
			} catch (e) {
				console.log('解析用户配置失败:', e);
			}
			runInAction(() => {
				this.fakerTradeConfig = userConfig;
			});
			return userConfig;
		} catch (error) {
			console.log('获取列配置失败:', error);
			return { ...defaultConfig };
		}
	}

	async setFakerTradeConfig(config:any) {
		try {
			await TradeDictInsertDictApi({ userDictEnum: 'LIVE_TRADE_PRINT_FAKER_TRADE_CONFIG', value: JSON.stringify(config) });
			runInAction(() => {
				this.fakerTradeConfig = config;
			});
		} catch (error) {
			console.log('保存列配置失败:', error);
		}
	}
}

const userStore = new UserStore();

export default userStore;
