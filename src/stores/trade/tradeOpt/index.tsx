import { makeAutoObservable, runInAction } from "mobx";
import _, { cloneDeep, uniq } from "lodash";
import React from "react";
import { Modal } from "antd";
import message from "@/components/message";
import event from "@/libs/event";
import {
	IOrders,
	IPackage,
	IScanningLabelInfo,
	IShipFuncOnOk,
	ISubTrade,
	Obj,
	ReceiverInfo
} from "@/pages/Trade/interface";
import { omitProps, Store } from "..";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import {
	TradeBatchSendApi,
	TradeBatchUpdateMemoApi,
	TradeBatchUpdatePendingStatusApi,
	TradeBatchUpdateTradeFlagApi,
	TradeOccupiedOpsApi,
	TradeBatchUpdateIgnoreApi,
	TradeTradeIgnoreApi,
	TradeAsyncLocalItemSnapShotApi,
	TradeTradeDetailGetApi,
	BatchUpdateSysMemoApi,
	UpdateSysMemoPicApi
} from "@/apis/trade";
import {
	getMultiPackNum,
	calcBizMark,
	calcPackTotalNum,
	findTradeFromList,
	noGoodsLinkWarning,
	tradeEditableKeyArr,
	handlePackageList
} from "@/pages/Trade/utils";
import {
	ISendSubTrades,
	TradeBatchSendData,
	TradeBatchSendRequest,
	TradeBatchUpdateMemoRequest,
	TradeBatchUpdateSysMemoResponse,
	TradeBatchUpdateTradeFlagRequest,
	TradeDispenseResponse,
	TradeOccupiedOpsRequest,
	TradeTradeIgnoreRequest
} from "@/types/trade/index";
import { TradeSellerInfoGetSellerFlagListApi } from "@/apis/user";
import tradeSetStore from "../tradeSet";
import { genePrintContent } from "@/utils/trade/printContent";
import { PLAT_PDD, PLAT_TB, PLAT_ALI, GIFT_NORMAL_PLATFORM, GIFT_SPECIAL_PLATFORM, PLAT_KS, PLAT_ALL_SCM, PLAT_KTT } from "@/constants";
import userStore from "@/stores/user";
import scanPrintStore from "../scanPrint";
import { PendingStatus, SendType, TradeStatus, IgnoreStatus } from "@/utils/enum/trade";
import { OccupyErrorTypeEnum, OccupyTradeOptType, IgnoreTypeEnum } from "@/pages/Trade/constants";
import { dealFormatResOrders, mapAllotNumToList, mapAllotNumToOrder } from "@/utils/trade/dealStockAllot";
import timerUtil, { logTypeEnum } from "@/libs/timer";
import { pageLoading } from "@/components/PageLoading";
import memoFn from "@/libs/memorizeFn";
import { CLEAR_PAGE_DATA } from "@/pages/Trade/RefundLabelScanPrint/constants";
import { CLEAR_PAGE_DATA as CLEAR_PAGE_DATA2 } from "@/pages/Trade/PostPrint/constants";
import { InspectSendEventBus } from "@/pages/Trade/InspectSend/constants";
import { tradeStore } from "@/stores";
import { WeighSendEventBus } from "@/pages/Trade/PackWeight/constants";
import packWeightStore from "../packWeight";
import { ModifyMemoType } from "@/pages/Trade/components/BatchModifyMemoModal";
import { ModifySysMemoType } from "@/pages/Trade/components/BatchModifyLocalMemoModal";
import { playAudio } from "@/pages/Trade/ScanPrint/utils";
import voice发货失败 from '@/assets/mp3/发货失败.mp3';
import voice成功 from '@/assets/mp3/成功.mp3';

const customLogPost = (type: string, dataType: string, data:any = {}) => {
	if (type === 'scanPrint') {
		window.errorCollection?.customMessageUpload({
			type: `扫描打印【${dataType}】`,
			data
		});
	} else {
		window.errorCollection?.customMessageUpload({
			type: `${dataType}`,
			data
		});
	}
};

class TradeOptStore {
	constructor(parentStore: omitProps) {
		makeAutoObservable(this);
		this.parentStore = parentStore;
	}

	parentStore: Partial<Store>;

	memoUpdating: boolean = false;

	setMemoUpdating = (val:boolean) => {
		this.memoUpdating = val;
	};

	/**
	 * 批量忽略异常
	 * @param list 数据
	 * @param ignoreType 忽略类型，1=商品绑定异常，2=系统赠品异常；默认=1
	 */
	batchIgnoreAbnormal = (list: IPackage[], ignoreType = IgnoreTypeEnum.商品绑定异常) => {
		let ignoreList:TradeTradeIgnoreRequest['ignoreTradeInfoList'] = [];
		let tidMap = {};
		list.forEach(pack => {
			pack.trades.forEach(trade => {
				let orderList = [];
				if (ignoreType == IgnoreTypeEnum.商品绑定异常) {
					orderList = trade.orders.filter(order => order.noGoodsLink);
				} else if (ignoreType == IgnoreTypeEnum.系统赠品异常) {
					// isSysGift 系统赠品
					// sysGiftStatus 1-正常，2-异常，3-忽略异常
					orderList = trade.orders.filter(order => order?.isSysGift && order?.sysGiftStatus == 2) || [];
				}
				orderList.forEach(order => {
					ignoreList.push({
						sellerId: trade.sellerId,
						platform: trade.platform,
						tid: trade.tid,
						oid: order.oid,
					});

					const mapKey = `${trade.platform}-${trade.sellerId}`;
					if (!tidMap[mapKey]) {
						tidMap[mapKey] = {
							platform: trade.platform,
							sellerId: trade.sellerId,
							tids: []
						};
					}
					tidMap[mapKey].tids.push(trade.tid);
				});
			});
		});

		if (!ignoreList?.length) {
			message.error('没有勾选可忽略异常的子订单');
			return;
		}

		TradeTradeIgnoreApi({
			ignoreTradeInfoList: ignoreList,
			ignoreType
		}).then(async res => {
			message.info('已成功忽略订单');
			const { data: { list } } = await TradeTradeDetailGetApi({
				tradeInfos: Object.values(tidMap),
			});
			if (list.length) {
				this.parentStore.tradeListStore.handleUpdateList({
					type: "updateTradesByDealAbnormal",
					data: list
				});
			}
		});
	}

	batchUpdateIgnore = (list: IPackage[], ignoreStatus: IgnoreStatus) => {
		const tidList = [];
		list.forEach(i => {
			i.tids.forEach(tid => {
				tidList.push(...(tid?.split('|') || []));
			});
		});
		const params = {
			tidList,
			ignoreType: ignoreStatus,
		};
		TradeBatchUpdateIgnoreApi(params).then((res) => {
			const resOrdersArr = res?.orders;
			runInAction(() => {
				if ([IgnoreStatus.手动标记].includes(ignoreStatus)) {
					message.success("标记成功");
				} else {
					message.success("取消标记成功");
				}
				let { list: allList, setList } = this.parentStore.tradeListStore;
				list.forEach((pack) => {
					let index = allList.findIndex((item) => findTradeFromList(item, pack));
					if (index > -1) {
						allList[index].trades?.forEach(trade => {
							trade.ignoreType = ignoreStatus;
							const resOrders = dealFormatResOrders(trade, resOrdersArr);
							mapAllotNumToOrder(trade?.orders, resOrders);
							mapAllotNumToOrder(pack?.productOrders, resOrders, { trade, pack });
						});
					}
				});
				setList([...allList]);
			});
		});
	};

	handleTradePending = (list: IPackage[], pendingStatus: PendingStatus) => {
		if (pendingStatus === PendingStatus.挂起 && list.length === 1) {
			sendPoint(Pointer.订单_订单打印_订单操作_挂起_单笔挂起);
		} else if (pendingStatus === PendingStatus.挂起 && list.length > 1) {
			sendPoint(Pointer.订单_订单打印_订单操作_挂起_批量挂起);
		} else if (pendingStatus === PendingStatus.取消挂起) {
			sendPoint(Pointer.订单_订单打印_订单操作_挂起_取消挂起);
		}
		let tids: string[] = [];
		list.forEach((pack) => {
			pack.togetherId.split("|").forEach((item) => {
				tids.push(item);
			});
		});
		let params = {
			tids,
			isPending: pendingStatus,
		};
		TradeBatchUpdatePendingStatusApi(params).then((res) => {
			runInAction(() => {
				message.success("操作成功");
				let { list: allList, setList } = this.parentStore.tradeListStore;
				const { setting } = this.parentStore;
				list.forEach((pack) => {
					let index = allList.findIndex((item) => findTradeFromList(item, pack));
					if (index > -1) {
						allList[index].isPending = !!pendingStatus;
					}
				});

				setList([...allList]);
			});
		});
	};

	// * 订单标记
	handleTradeFlag = (sellerFlagSys: {}, tids: string[]) => {
		const { modifyFlagPackage } = this.parentStore;
		let params = {
			tids,
			bizMark: calcBizMark(sellerFlagSys),
		};
		TradeBatchUpdateTradeFlagApi(params).then((res) => {
			runInAction(() => {
				message.success("操作成功");
				modifyFlagPackage.forEach((pack) => {
					pack.sellerFlagSys = sellerFlagSys;
					pack.trades.forEach((trade) => {
						trade.sellerFlagSys = sellerFlagSys;
					});
				});

				const resOrdersArr = res?.data?.orders;
				let allList = this.parentStore.tradeListStore.list;
				mapAllotNumToList(allList, resOrdersArr);
			});
		});
	};

	tagShopData = {}; // 旗帜标签,以店铺区分

	getTagData = async(list:any[]) => {
		const uniqueSellers = list.filter((seller, index, self) => index === self.findIndex((s) => (
			s.sellerId === seller.sellerId && s.platform === seller.platform
		)));
		if (uniqueSellers.every(item => this.tagShopData?.[item.sellerId])) {
			return this.tagShopData;
		}
		let tagRes = await TradeSellerInfoGetSellerFlagListApi({ sellerFlagInfoList: uniqueSellers });
		let tagShopData = tagRes?.reduce((acc, seller) => {
			// 如果acc中还没有这个sellerFlag的键，先创建一个空数组
			if (!acc[seller.sellerId]) {
				acc[seller.sellerId] = [];
			}
			// 将当前的seller对象添加到对应的数组中
			acc[seller.sellerId].push(seller);
			return acc;
		}, {});
		this.tagShopData = tagShopData;
		return tagShopData;
	}

	// * 打印后自动追加快递单号备注 已经添加的订单号不再重复添加
	addYdNoToMemo = async(data: {dealedOrderList: IPackage[]}) => {
		console.log('%c [ addYdNoToMemo ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', data);
		let allList = this.parentStore.tradeListStore.list;
		let params: TradeBatchUpdateMemoRequest = [];
		let updateFn: Function[] = [];
		data.dealedOrderList.forEach(item => {
			let pack = allList.find(i => i.togetherId === item.togetherId);
			for (let i = 0; i < pack.trades.length; i++) {
				const trade = pack.trades[i];
				if (trade.orders.some(order => order.isChecked)) {
					let memo = trade.sellerMemo || '';
					let flag = item.sellerFlag || trade.sellerMemoFlag; // 如果是1688无旗帜会变红旗
					item?.allYdNos?.forEach(no => {
						if (memo.indexOf(no) === -1) {
							memo = memo ? `${memo},${no}` : `${no}`;
						}
					});
					params.push({
						tid: trade.tid,
						ptTid: trade.ptTid,
						memo,
						sellerFlag: flag,
						platform: pack.platform,
						sellerId: pack.sellerId,
						saleUserId: pack.distributorUserId,
						source: pack.source || "",
						hasPrintTrigger: true,
						afterSalesFlag: pack.afterSalesFlag || false
						// sellerFlagTag: item.sellerFlagTag || trade.sellerFlagTag || trade.sellerMemoFlagTag, // 这里不需要改，只有1688可能会改旗帜
					});
					updateFn.push(() => {
						trade.sellerMemo = memo;
						trade._sellerMemo = memo;
						trade.sellerMemoFlag = flag;
						trade._sellerMemoFlag = flag;
					});
				}
			}
		});
		TradeBatchUpdateMemoApi(params).then(() => {
			runInAction(() => {
				updateFn.forEach(fn => {
					fn();
				});
			});
		});
	}

	// * 更新备注
	updateMemo = (params: Obj, isBatch: boolean = true, callback?: Function) => {
		sendPoint(Pointer.订单_订单打印_订单操作_订单备注_保存备注);
		const { type, flag, thisFlagTagList = [] } = params;
		const { modifyMemoPackage, setIsShowBatchModifyMemoModal, setMemoUpdating } = this.parentStore;
		let data: TradeBatchUpdateMemoRequest = [];
		modifyMemoPackage.forEach((pack) => {
			// 这里要找到对应的店铺的旗帜标签备注, 没有变动就不需要更新
			let thisShopAndThisFlagTagInfo = thisFlagTagList?.find(item => item?.sellerId == pack?.sellerId);

			pack.trades.forEach((trade) => {
				let _memo = params.memo;
				let content = trade.sellerMemo || "";
				if (isBatch) {
					if (type === 1) {
						_memo = content + _memo;
					} else if (type === 3) {
						_memo += content;
					}
				}
				// 批量修改备注需要时，如果选择了灰旗或者不设置旗帜，则需要过滤掉1688平台的订单
				if (pack.platform === PLAT_ALI && [0, -1].includes(params.flag)) {
					return;
				}
				let newFlag = flag;
				if (flag === -1) {
					if (pack.platform === PLAT_KS) {
						newFlag = '';
					} else {
						newFlag = trade.sellerMemoFlag;
					}
				}

				// 快团团平台不保存旗帜数据，无论是否设置了旗帜
				if (pack.platform === PLAT_KTT) {
					newFlag = '';
				}

				let obj:any = {
					tid: trade.tid,
					ptTid: trade.ptTid,
					memo: _memo,
					sellerFlag: newFlag,
					platform: pack.platform,
					sellerId: pack.sellerId,
					refundStatus: trade.refundStatus || "",
					source: pack.source || "",
					isDistributorUserPushRefund: pack?.isDistributorUserPushRefund, // 分销商的售后单
					supplierUserId: pack.supplierUserId,
					saleUserId: pack.distributorUserId || "",
					afterSalesFlag: pack.afterSalesFlag || false
				};

				// 如果更改了旗帜，需要更改对应的店铺的旗帜标签
				if (thisShopAndThisFlagTagInfo) {
					obj.sellerFlagTag = thisShopAndThisFlagTagInfo?.sellerFlagTag || '';
				} else {
					obj.sellerFlagTag = "";
				}

				data.push(obj);
			});
		});

		this.memoUpdating = true;
		TradeBatchUpdateMemoApi(data).then((res) => {
			runInAction(() => {
				const resOrdersArr = res?.data?.orders;
				if (res?.data) {
					message.success("修改成功");
				}
				if (callback) {
					callback(data);
				} else {
					let allList = this.parentStore.tradeListStore.list;
					modifyMemoPackage.forEach((pack, i) => {
						let index = allList.findIndex((item) => findTradeFromList(item, pack));
						if (index > -1) {
							pack._sellerMemo = "";
							// 这里要找到对应的店铺的旗帜标签备注, 没有变动就不需要更新
							let thisShopAndThisFlagTagInfo = thisFlagTagList?.find(item => item?.sellerId == pack?.sellerId);
							allList[index].trades.forEach((trade) => {
								let dataIndex = data.findIndex(dataTemp => dataTemp.tid === trade.tid);
								if (dataIndex > -1) {
									let sellerMemoFlag = flag;
									if (flag === -1) {
										if (pack.platform === PLAT_KS) {
											sellerMemoFlag = 0;
										} else {
											sellerMemoFlag = trade.sellerMemoFlag;
										}
									}

									// 快团团平台不更新旗帜数据，无论是否设置了旗帜
									if (pack.platform === PLAT_KTT) {
										sellerMemoFlag = '';
									}

									trade.sellerMemo = data[dataIndex].memo;
									trade._sellerMemo = data[dataIndex].memo;
									trade.sellerMemoFlag = sellerMemoFlag;
									trade._sellerMemoFlag = sellerMemoFlag;

									if (thisShopAndThisFlagTagInfo) {
										trade.sellerFlagTag = thisShopAndThisFlagTagInfo?.sellerFlagTag || '';
									} else {
										trade.sellerFlagTag = "";
									}

									let resOrders = dealFormatResOrders(trade, resOrdersArr);
									mapAllotNumToOrder(trade?.orders, resOrders);
									mapAllotNumToOrder(pack?.productOrders, resOrders, { trade, pack });
									pack._sellerMemo += trade.sellerMemo || "";
								}
							});
						}
					});
					setIsShowBatchModifyMemoModal(false);
				}
			});
		}).finally(() => this.memoUpdating = false);
	};

	updateSysMemo = async(params, callback?:any) => {
		const res: TradeBatchUpdateSysMemoResponse = await BatchUpdateSysMemoApi(params);
		const { errorMsg, tradeSysMemoVOList } = res?.data;
		if (errorMsg) {
			message.warn(errorMsg);
		} else {
			message.success(`共${tradeSysMemoVOList?.length}条线下备注成功`);
		}
		if (tradeSysMemoVOList?.length) {
			const map = new Map();
			tradeSysMemoVOList.forEach(i => {
				if (i.tid) map.set(i.tid, i.sysMemo);
			});

			let allList = this.parentStore.tradeListStore.list;
			allList.forEach(pack => {
				let _packSysMemo = '';
				pack.trades.forEach(trade => {
					if (map.has(trade.tid)) {
						trade.sysMemo = map.get(trade.tid);
						trade._sysMemo = map.get(trade.tid);
						_packSysMemo += trade._sysMemo;
					}
				});
				pack._sysMemo = _packSysMemo;
			});
		}
		callback?.(res);
		return res;
	}

	updateSysMemoPic = async(params) => {
		const res: TradeBatchUpdateSysMemoResponse = await UpdateSysMemoPicApi(params);
		const { tid, sysMemoPic } = res;
		if (tid) {
			let allList = this.parentStore.tradeListStore.list;
			allList.forEach(pack => {
				pack.trades.forEach(trade => {
					if (trade?.tid == tid) {
						trade.sysMemoPic = sysMemoPic;
					}
				});
			});
		}
		return res;
	}


	// * 详情中修改线下备注
	packageDetailUpdateSysMemo = async(
		packInfo: IPackage,
		tradeIndex: number
	) => {
		let trade = packInfo.trades[tradeIndex];
		const params = {
			appendType: ModifySysMemoType.覆盖原备注,
			userQuickNotesContent: trade._sysMemo,
			tidList: [trade.tid]
		};
		const res = await BatchUpdateSysMemoApi(params);
		const { errorMsg, tradeSysMemoVOList } = res?.data;
		if (errorMsg) {
			message.warn(errorMsg);
		} else {
			message.success('线下备注保存成功');
		}
		if (tradeSysMemoVOList?.length) {
			trade.sysMemo = trade._sysMemo;
		}
	};

	// * 详情中修改备注
	packageDetailUpdateMemo = async(
		packInfo: IPackage,
		tradeIndex: number,
		flagIndex?: string
	) => {
		let trade = packInfo.trades[tradeIndex];
		let flag = flagIndex;
		if (flagIndex === undefined) {
			flag = trade.sellerMemoFlag;
		}
		let data:any[] = [
			{
				tid: trade.tid,
				ptTid: trade.ptTid,
				memo: trade._sellerMemo || '',
				sellerFlag: flag,
				platform: packInfo.platform,
				sellerId: packInfo.sellerId,
				source: packInfo.source || "",
				saleUserId: packInfo.distributorUserId || "",
				afterSalesFlag: packInfo.afterSalesFlag || false
			}
		];

		// 以下平台修改对应的旗帜备注
		const allowList = ['tb', 'tm'];
		if (allowList.includes(packInfo?.platform) && !PLAT_ALL_SCM.includes(packInfo.source)) {
			let tagShopData = await this.getTagData([{ sellerId: packInfo.sellerId, platform: packInfo.platform, }]);
			let tagData = tagShopData?.[packInfo.sellerId]?.find(item => item?.sellerFlag == flag);
			if (tagData) {
				data[0].sellerFlagTag = tagData?.sellerFlagTag || '';
			} else {
				data[0].sellerFlagTag = '';
			}
		}
		TradeBatchUpdateMemoApi(data).then((res) => {
			if (!res) return;
			runInAction(() => {
				trade.buyerMessage = trade._buyerMessage;
				trade.sellerMemoFlag = flag;
				trade._sellerMemoFlag = flag;
				trade.sellerMemo = trade._sellerMemo || '';

				// 更新下旗帜标签
				if (data?.[0]?.sellerFlagTag) {
					trade.sellerFlagTag = data[0].sellerFlagTag;
				} else {
					trade.sellerFlagTag = "";
				}

				let resOrders = res?.data?.orders;
				if (!resOrders?.length) {
					resOrders = trade.orders;
				}
				mapAllotNumToOrder(trade?.orders, resOrders);
				mapAllotNumToOrder(packInfo?.productOrders, resOrders, { trade, pack: packInfo });
			});
		});
	};

	// 扫描打印中修改备注
	scanPrintUpdateMemo = (
		params: {
			type: ModifyMemoType,
			flag: number,
			memo: string,
			memoListIndex: number,
			source: string,
			thisFlagTagList?: any[],
		},
		packList: IPackage[] | IScanningLabelInfo[]
	) => {
		let { type, memo = '', source, flag, thisFlagTagList = [] } = params;
		// 快手允许旗帜传空
		const getNewFlag = (packInfo) => {
			let newFlag:any = flag + '';
			if (flag === -1) {
				if (packInfo?.takeGoodsLabelInfos?.[0]?.platform === PLAT_KS) {
					newFlag = 0;
				} else {
					newFlag = packInfo.sellerFlag;
				}
			}

			// 快团团平台不保存旗帜数据，无论是否设置了旗帜
			if (packInfo?.takeGoodsLabelInfos?.[0]?.platform === PLAT_KTT) {
				newFlag = '';
			}
			return newFlag;
		};
		if (type === ModifyMemoType.添加至原备注之前) {
			packList.forEach(packInfo => {
				packInfo._sellerMemo = `${memo || ''}${packInfo.sellerMemo || ''}`;
				packInfo._sellerFlag = getNewFlag(packInfo);
			});
		} else if (type === ModifyMemoType.添加至原备注之后) {
			packList.forEach(packInfo => {
				packInfo._sellerMemo = `${packInfo.sellerMemo || ''}${memo || ''}`;
				packInfo._sellerFlag = getNewFlag(packInfo);
			});
		} else if (type === ModifyMemoType.覆盖原备注) {
			packList.forEach(packInfo => {
				packInfo._sellerMemo = `${memo || ''}`;
				packInfo._sellerFlag = getNewFlag(packInfo);
			});
		}

		// 修改旗帜的时候
		if (flag > -1) {
			packList.forEach(packInfo => {
				// 这里要找到对应的店铺的旗帜标签备注, 没有变动就不需要更新
				let thisShopAndThisFlagTagInfo = thisFlagTagList?.find(item => item?.sellerId == packInfo?.takeGoodsLabelInfos?.[0].sellerId);
				if (thisShopAndThisFlagTagInfo) {
					packInfo._sellerFlagTag = thisShopAndThisFlagTagInfo?.sellerFlagTag || '';
				} else {
					packInfo._sellerFlagTag = "";
				}
			});
		}

		let data = packList.map(packInfo => ({
			tid: packInfo.tid,
			ptTid: packInfo?.trades?.map(s => s.ptTid)?.join(','),
			memo: packInfo._sellerMemo || '',
			sellerFlag: packInfo._sellerFlag || '', // 0 传 ''
			platform: packInfo.takeGoodsLabelInfos[0].platform,
			sellerId: packInfo.takeGoodsLabelInfos[0].sellerId,
			source,
			refundStatus: packInfo.takeGoodsLabelInfos[0].refundStatus || "",
			sellerFlagTag: packInfo?._sellerFlagTag || '',
			afterSalesFlag: packInfo.afterSalesFlag || false
		}));
		return TradeBatchUpdateMemoApi(data);
	};

	// * 库存不足时 取消勾选订单
	cancelStockWarningPack = (
		cancelCheckedPackArr: {
			subTrades?: ISendSubTrades[];
			stockStatus?: string;
		}[]
	) => {
		const {
			tradeListStore: { list: allList, calcListCheckStatus },
		} = this.parentStore;
		cancelCheckedPackArr.forEach((cancelCheckedItem) => {
			// * 把批打列表中的这笔订单取消勾选
			let index = allList.findIndex(
				(item) => item.togetherId
					=== cancelCheckedItem.subTrades[0].togetherId
			);
			if (index > -1) {
				allList[index].isChecked = false;
			}
		});
		calcListCheckStatus();
	};

	// * 发货
	shipFuncOnOk = (shipFuncParams: IShipFuncOnOk) => {
		// 1249243 用户的
		// 1234568 8615测试账号的
		const whiteUserId = ["1249243", "1234568"];
		// 默认一次性发布10个数量请求
		const noLimitUserId = ["1283701"];
		const {
			setting,
			selectedTemp,
			isMergePrint,
			selectedTempGroup,
			refundLabelScanPrintStore: {
				setPrintRes, resetStore
			},
			postPrintStore,
			tradeListStore: {
				list: allList,
				setList,
			},
		} = this.parentStore;
		const { userInfo } = userStore;
		const {
			params,
			totalTrade,
			templateName,
			sendType = SendType.自己联系,
			shipFinishFn = () => {},
			type = 'trade',
			afterPrint,
			inspectSendCallback
		} = shipFuncParams;
		timerUtil.start('发货');
		let timer = 0;
		let threadNum = 4;
		let totalSend = 0;
		let successResult = [];
		let failResult = [];
		let showMessage: {
			tid: string;
			buyerNick: string;
			message: string;
		}[] = [];
		// 只针对部分用户发货并发做处理


		const totalRequests = Math.ceil((params?.length || 0) / 5); // 总的请求次数
		let hasRequestTotal = 0; // 已经请求完成的数量

		if (whiteUserId.includes(userInfo?.userId)) {
			threadNum = 1;
		} else if (noLimitUserId.includes(userInfo?.userId)) {
			threadNum = 10;
		}

		// 添加发货类型
		(params && params.length > 0) && params.forEach(pack => {
			pack.subTrades.forEach(trade => {
				trade.sendType = sendType;
			});
		});

		const shipOnFinish = (sd, ApiParams) => {
			if (type === 'scanPrint') {
				const { id } = scanPrintStore.searchScanPrintFormSetting;
				if (ApiParams[0].subTrades[0].labelIds && ApiParams[0].subTrades[0].labelIds.includes(id)) {
					scanPrintStore.updateInfoStatus();
					scanPrintStore.scanPrintTempInfo.scanInfoMsg = '打印成功； 发货成功；';
					// 100发货成功，101, 部分发货成功，102 拆单发货成功，103发货失败
					let result = sd?.[0]?.result;
					if (result == '103') {
						scanPrintStore.scanPrintTempInfo.scanInfoMsg = '打印成功； 发货失败；';
						playAudio(voice发货失败);
						customLogPost(type, '打印成功； 发货失败；', { ApiParams, sd, id });
					} else {
						let text = '发货成功';
						if (result == '100') {
							text = '发货成功';
						} else if (result == '101') {
							text = '部分发货成功';
						} else if (result == '102') {
							text = '拆单发货成功';
						}
						scanPrintStore.scanPrintTempInfo.scanInfoMsg = `打印成功； ${text}；`;
						customLogPost(type, text, { sd, id });
					}
				} else {
					customLogPost(type, 'shipOnFinish errpr', { ApiParams, sd, id });
				}
			}
			if (type === 'inspectSend') {
				inspectSendCallback?.(sd);
				event.emit(InspectSendEventBus.HANDLE_SEND_RES, sd);
			}

			if (type === 'weighSend') {
				event.emit(WeighSendEventBus.HANDLE_SEND_RES, sd);
			}
			if (type === 'refundLabel') {
				setPrintRes({
					showRes: true,
					expressBillSuccess: true,
					sendSuccess: true
				});
				pageLoading.destroy();
				resetStore();
				event.emit(CLEAR_PAGE_DATA);
			}
			if (type === 'postPrintSend') {
				postPrintStore.setPostPrintRes({
					showRes: true,
					successText: '打印成功，发货成功'
				});
				playAudio(voice成功);
				pageLoading.destroy();
				postPrintStore.resetStore({ isSendSuccess: true });
				event.emit(CLEAR_PAGE_DATA2);
			}
		};

		const callApi = async(
			ApiParams: TradeBatchSendRequest,
			callback: any
		) => {
			const { InspectSendStore: { setSendPending } } = tradeStore;
			try {
				console.log('ApiParams==发货', ApiParams);
				const filterData = ApiParams.filter(s => s.subTrades.length);
				const sd = await TradeBatchSendApi(filterData);
				if (type === 'trade') {
					pageLoading.destroy();
				}
				if (type === "inspectSend") {
					setSendPending(false);
				}
				if (type === 'weighSend') {
					packWeightStore.setSendPending(false);
				}
				userStore.setNewUserGuide({ tradePrintAndSend: true });
				runInAction(() => {
					// 100发货成功，101, 部分发货成功，102 拆单发货成功，103发货失败
					for (let i = 0; i < sd.length; i++) {
						let resultCode = sd[i].result;
						if (resultCode !== 103) {
							successResult.push(sd[i]);
						} else {
							failResult.push(sd[i]);
						}

						if (resultCode === 101 || resultCode === 103) {
							sd[i].subTradeInfos.forEach((item) => {
								if (!item.isSuccess) {
									showMessage.push({
										tid: item.tid,
										buyerNick: sd[i].buyerNick,
										message: item.message,
									});
								}
							});
						}
					}

					shipOnFinish(sd, ApiParams);

					callback && callback(sd, ApiParams);
				});
			} catch (err) {
				if (type === 'trade') {
					pageLoading.destroy();
				}
				if (type === "inspectSend") {
					setSendPending(false);
				}
				if (type === "weighSend") {
					packWeightStore.setSendPending(false);
				}
				console.log('err: ', err);
				runInAction(() => {
					for (let i = 0; i < ApiParams.length; i++) {
						showMessage.push({
							tid: ApiParams[i].subTrades[0].tid,
							buyerNick: ApiParams[i].subTrades[0].buyerNick,
							message: err?.message || err?.errorMessage || '发生错误',
						});
					}
					callback && callback(false, ApiParams);
				});
				console.log('err.success', err?.success, err?.message, err?.errorMessage);
				customLogPost(type, '发货失败', { ApiParams, err });
			}
		};

		const handler = () => {
			if (!params.length) {
				timer++; // 资源已经耗尽，所有的请求发送出，等待所有发出的请求结束
				if (timer !== threadNum) {
					// 所有发出的请求已经结束，可以执行请求结束处理程序，当前同时异步数是4
					return;
				}
				console.log('over');
				timerUtil.stop('发货', {
					desc: `${totalTrade}单`,
					type: type === 'scanPrint' ? logTypeEnum['小标签'] : logTypeEnum['订单模块'],
				});
				setList([...allList]);
				return;
			}
			// 发货进行中
			callApi(
				params.splice(0, 5),
				(
					sd: TradeBatchSendData[],
					pms: TradeBatchSendRequest
				) => {
					pms.forEach((item) => {
						totalSend += item.subTrades.length;
					});

					// 单笔发货的结束
					if (totalTrade !== 1) {
						shipFinishFn({
							showMessage,
							totalSend,
							totalTrade,
						});
					} else if (successResult.length) {
						message.success("发货成功");
					} else {
						message.warning(
							"发货失败："
								+ ((showMessage[0] || { message: "" }).message
									|| "")
						);
						// playAudio(voice发货失败);
					}

					// 去更新对应的包裹
					for (let i = 0; i < sd.length; i++) {
						let {
							result,
							togetherId,
							subTradeInfos,
						} = sd[i];
						let hasFirstSend = false;
						// 100发货成功，101, 部分发货成功，102 拆单发货成功，103发货失败
						if (result !== 103) {
							let index = allList.findIndex(
								(pack) => pack.togetherId === togetherId
							);
							const checkOrderStatus = (pack) => {
								return pack.trades?.every(trade => trade.orders?.every(order => order.status === 'WAIT_BUYER_CONFIRM_GOODS'));
							};
							// eslint-disable-next-line no-continue
							if (index === -1) continue;
							let pack = allList[index];
							if (result === 100) {
								// 发货后隐藏订单
								if (setting.isHideOrderAftersend === 2) {
									// * 判断是否属于是子订单
									if (subTradeInfos.map(item => item.tid).join('|') === togetherId) {
										allList.splice(index, 1);
										// eslint-disable-next-line no-continue
										continue;
									}
								}
							} else if (setting.isHideOrderAftersend === 2 && setting?.printSetExpandDTO?.hidePartSendOrderAfterSendFlag === 0) {
								allList.splice(index, 1);
								continue;
							}

							console.log('sd', sd);
							console.log('sendType', sendType);
							if (sendType === SendType.无需物流发货) { // 无需物流发货
								pack.ydNoSet.push('无需物流单号发货');
							} else if (sendType === SendType.卖家配送) {
								pack.ydNoSet.push('卖家配送');
							} else {
								if (sendType === SendType.自动发货) {
									pack.isPreShip = true;
								}
								if (sendType === SendType.自己联系 && pms[i].subTrades[0].exNumberList) {
									pack.multiPackYdCount = getMultiPackNum(pms[i].subTrades[0].exNumberList);
								}
								// 如果是扫描发货的时候，快递单模板从对应订单中获取，而不是从store中获取
								if (['inspectSend'].includes(type)) {
									pack.ydNoSet.push(
										`${pms[i].subTrades[0].exName}: ${pms[i].subTrades[0].exNumberList || pms[i].subTrades[0].exNumber}`
									);
								} else {
									pack.ydNoSet.push(
										`${templateName}: ${pms[i].subTrades[0].exNumberList || pms[i].subTrades[0].exNumber}`
									);
								}
							}
							pack.isChecked = false;
							pack.isPartShipped = false;
							if (result === 102 && pack?.trades?.length > 1) pack.isPartShipped = true;
							// 合单 拆单发货的时候 isPartShipped部分发货默认为true
							subTradeInfos.forEach((item) => {
								let tradeIndex = pack.trades.findIndex(
									(trade) => trade.tid === item.tid
								);
								if (tradeIndex > -1) {
									let trade = pack.trades[tradeIndex];
									if (sendType === SendType.无需物流发货) { // 无需物流发货
										if (!trade.ydNoSet.some(item => /[0-9]/.test(item))) {
											trade.ydNoSet.push('无需物流单号发货');
											trade.orders.forEach((order) => {
												order?.ydNoSet?.push('无需物流单号发货');
											});
										}
									} else if (sendType === SendType.卖家配送) {
										pack.ydNoSet.push('卖家配送');
										trade.orders.forEach((order) => {
											order?.ydNoSet?.push('卖家配送');
										});
									} else if (['inspectSend', 'postPrintSend'].includes(type)) {
										trade.ydNoSet.push(
											`${pms[i].subTrades[0].exName}: ${pms[i].subTrades[0].exNumberList || pms[i].subTrades[0].exNumber}`
										);
										// 多包裹ydNoSet精确到order级别
										trade.orders.forEach((order) => {
											order?.ydNoSet?.push(
												`${pms[i].subTrades[0].exName}: ${pms[i].subTrades[0].exNumberList || pms[i].subTrades[0].exNumber}`
											);
										});
									} else {
										trade.ydNoSet.push(
											`${templateName}: ${pms[i].subTrades[0].exNumberList || pms[i].subTrades[0].exNumber}`
										);
										// 多包裹ydNoSet精确到order级别
										trade.orders.forEach((order) => {
											order?.ydNoSet?.push(
												`${templateName}: ${pms[i].subTrades[0].exNumberList || pms[i].subTrades[0].exNumber}`
											);
										});
									}

									trade.ydNoSet = uniq(trade.ydNoSet);
									trade.orders.forEach(
										(order) => {
											order.ydNoSet = uniq(order.ydNoSet);
											if ((item.oids && item.oids.includes(order.oid)) || !item.oids) {
												order.status = "WAIT_BUYER_CONFIRM_GOODS";
												if (!afterPrint
													&& ![SendType.自动发货, SendType.无需物流发货, SendType.卖家配送].includes(sendType as SendType)
													&& !order.sysPrintTime
													&& ((!isMergePrint && +selectedTemp?.Exid !== -901) || (isMergePrint && +selectedTempGroup?.id !== -901))) order.firstSend = true;
											}
										}
									);
									let sendOrderCount = 0;
									let noSendOrderCount = 0;
									let firstSendOrderCount = 0;
									console.log('99999900088', trade);
									trade.orders.forEach((order) => {
										if (
											order.status
												=== "WAIT_BUYER_CONFIRM_GOODS"
										) {
											if (order.firstSend) {
												firstSendOrderCount++;
											}
											sendOrderCount++;
										} else if (
											order.status
												=== "WAIT_SELLER_SEND_GOODS"
										) {
											noSendOrderCount++;
										}
									});

									if (
										sendOrderCount > 0
											&& noSendOrderCount > 0
									) {
										pack.isPartShipped = true;
										trade.status = "SELLER_CONSIGNED_PART";
									} else if (noSendOrderCount === 0) {
										trade.status = "WAIT_BUYER_CONFIRM_GOODS";
									}
									// 只要订单中有先发货 就是先发货
									firstSendOrderCount > 0 && (trade.firstSend = true);
									firstSendOrderCount > 0 && (hasFirstSend = true);
								}
								pack.productOrders.forEach((order) => {
									if (item.tid == order.tid && (!item.oids || item.oids.includes(order.oid))) {
										order.status = "WAIT_BUYER_CONFIRM_GOODS";
									}
								});
							});
							pack.ydNoSet = uniq(pack.ydNoSet);
							!['postPrintSend'].includes(type) && hasFirstSend && (pack.firstSend = true);
							if (pack.trades.every(trade => trade.status === 'WAIT_BUYER_CONFIRM_GOODS')) {
								pack.shipStatus = "alreadyShipped";
							}
							if (checkOrderStatus(pack) && setting.isHideOrderAftersend === 2) {
								allList.splice(index, 1);
								continue;
							}
						}
					}

					hasRequestTotal += 1; // 请求完成的请求数
					if (hasRequestTotal >= totalRequests && (showMessage?.length || failResult?.length)) {
						customLogPost(type, '发货结束，有发货失败的请求', { failResult, successResult, showMessage, totalRequests });
					}

					// 继续去调用api查询数据，直到结束
					handler();
				}
			);
		};
		if (type === 'trade') {
			pageLoading.loading();
		}
		// 资源竞争型，until deplete all the params，如增加同时异步数需要修改计数器
		for (let index = 0; index < threadNum; index++) {
			handler();
		}
	};

	handleOrderCheck = async(
		packInfo: IPackage,
		tradeIndex: number,
		orderIndex: number,
		order?: any
	) => {
		if (!packInfo) return;
		if (tradeIndex === -1 && order) {
			tradeIndex = packInfo.trades.findIndex(
				(trade: any) => trade.tid === order.tid
			);
		}
		let _trade = packInfo.trades[tradeIndex];
		let _order = _trade.orders[orderIndex];
		_order.isChecked = !_order.isChecked;
		_trade.orders.forEach(order => {
			if (order.isGift && !GIFT_NORMAL_PLATFORM.includes(_trade.platform) && order.status !== TradeStatus.等待买家确认收货) {
				if (GIFT_SPECIAL_PLATFORM.includes(_trade.platform)) {
					order.isChecked = _order.isChecked;
				} else {
					order.isChecked = !_trade.orders.filter(order => !order.isGift).every(order => !order.isChecked);
				}
			}
			// !! 快手平台不允许拆单发货
			if (PLAT_KS == _trade.platform) {
				order.isChecked = _order.isChecked;
			}
		});

		let len = _trade.orders.filter(
			(item) => item.isChecked && item.status === "WAIT_SELLER_SEND_GOODS"
		).length;
		if (len && len !== _trade.orders.length) {
			packInfo.isSplitSend = true;
		} else {
			packInfo.isSplitSend = false;
		}

		calcPackTotalNum(packInfo);
		const storeTradeSet = tradeSetStore;
		const keyWords = storeTradeSet.filterWord;
		const advancedSetRes = await memoFn.getAdvancedSet();

		packInfo.printContent = genePrintContent(
			packInfo,
			storeTradeSet.printContentSet,
			keyWords,
			advancedSetRes
		);
		if (packInfo.isChecked) {
			this.parentStore.tradePrintStore.waybillEcho([packInfo]);
		}
	};

	handlePackInfoPrintContent = (packInfo: IPackage) => {
		const storeTradeSet = tradeSetStore;
		const keyWords = storeTradeSet.filterWord;
		memoFn.getAdvancedSet().then(advancedSetRes => {
			packInfo.printContent = genePrintContent(
				packInfo,
				storeTradeSet.printContentSet,
				keyWords,
				advancedSetRes
			);
		});
	}

	handleProductOrders = (pack: IPackage) => {
		let productOrders: IOrders[] = [];
		let _trades = pack.trades;
		_trades.forEach((trade, tradeIndex) => {
			trade.orders.forEach((order, orderIndex) => {
				!order.isHideByPartShip && !order.isHideFinishedGoods && productOrders.push({ ...order, tradeIndex });
			});
		});
		pack.productOrders = productOrders;
	}

	recycleElecNo = (pack: IPackage, res: any, pYdNo_sids:any = []) => {
		pack.isChecked = true;
		pack.sids = pack.sids.filter((v) => {
			if (pYdNo_sids?.length)	return (!pYdNo_sids.includes(v));
			else return res.indexOf(v) == -1;
		});
	};

	modifySysMemo = (
		packInfo: IPackage,
		type: "trade" | "pack",
		flag: string,
		tradeIndex = 0
	) => {
		let prev = packInfo;
		let flagValue: number;
		if (type === "trade") {
			let _trade = prev.trades[tradeIndex];
			flagValue = _trade.sellerFlagSys[flag] === 2 ? 0 : 2;
			_trade.sellerFlagSys[flag] = flagValue;
			if (prev.trades.every((trade) => trade.sellerFlagSys[flag] === 0)) {
				prev.sellerFlagSys[flag] = 0;
			} else if (
				prev.trades.every((trade) => trade.sellerFlagSys[flag] === 2)
			) {
				prev.sellerFlagSys[flag] = 2;
			} else {
				prev.sellerFlagSys[flag] = 1;
			}
			this.handleTradeFlag(_trade.sellerFlagSys, [_trade.tid]);
		} else if (type === "pack") {
			flagValue = prev.sellerFlagSys[flag] === 2 ? 0 : 2;
			prev.sellerFlagSys[flag] = flagValue;
			prev.trades.forEach((trade) => {
				trade.sellerFlagSys[flag] = flagValue;
			});
			this.handleTradeFlag(
				prev.sellerFlagSys,
				prev.togetherId.split("|")
			);
		}
	};

	setPackageAndEditValueEmpty = (pack: IPackage, key: string[]) => {
		key.forEach((i) => {
			pack[i] = "";
			pack[`_${i}`] = "";
		});
	};

	setPackageFromEditValue = (pack: IPackage, key: string[]) => {
		key.forEach((i) => {
			pack[i] = pack[`_${i}`];
		});
	};

	setEditValueFromPackage = (pack: IPackage, key: string[]) => {
		key.forEach((i) => {
			pack[`_${i}`] = pack[i];
		});
	};

	setPackageVal = (pack: IPackage, key: string | string[], value: any) => {
		if (Array.isArray(key)) {
			key.forEach((k, index) => {
				pack[k] = value[index];
			});
		} else {
			pack[key] = value;
		}
	};

	setTradeVal = (trade: ISubTrade, key: string, value: any) => {
		trade[key] = value;
	};

	modifyAddr = (packInfo: IPackage) => {
		this.setPackageAndEditValueEmpty(packInfo, [
			"receiverPhoneMask",
			"receiverPhone"
		]);
		this.setPackageFromEditValue(packInfo, [...tradeEditableKeyArr]);
		let buyerNick = packInfo.platform === PLAT_PDD
			? packInfo._receiverNameMask
			: packInfo.buyerNick;
		let caid = packInfo.platform === PLAT_TB ? "" : packInfo.caid || "";
		const { _receiverNameMask, _receiverAddressMask, _receiverMobileMask, _receiverState, _receiverCity, _receiverDistrict, _receiverTown } = packInfo;
		this.setPackageVal(
			packInfo,
			[
				"receiverName",
				"receiverNameMask",
				"receiverMobile",
				"receiverMobileMask",
				"receiverAddress",
				"receiverAddressMask",
				"idxEncodeReceiverName",
				"idxEncodeReceiverMobile",
				"idxEncodeReceiverAddress",
				"buyerNick",
				"caid",
				"receiverState",
				"receiverCity",
				"receiverDistrict",
				"receiverTown"
			],
			[
				_receiverNameMask,
				_receiverNameMask,
				_receiverMobileMask,
				_receiverMobileMask,
				_receiverAddressMask,
				_receiverAddressMask,
				_receiverNameMask,
				_receiverMobileMask,
				_receiverAddressMask,
				buyerNick,
				caid,
				_receiverState,
				_receiverCity,
				_receiverDistrict,
				_receiverTown
			]
		);
		// console.log(packInfo, "packInfo");
	};

	updateAllotStockNum = (res: TradeDispenseResponse['data']) => {
		let { list, setList } = this.parentStore.tradeListStore;
		const { destTid, destOid, destOidAllotResult, sourceTid, sourceTidAllotResult } = res;
		if (destTid && destOid) {
			const sourceOrders = [{ alreadyAllotStockNum: destOidAllotResult, oid: destOid }];
			const pack = list.find(item => item.trades.map(i => i.tid).includes(destTid));
			const trade = pack?.trades.find(i => i.tid == destTid);
			mapAllotNumToOrder(trade?.orders, sourceOrders);
			mapAllotNumToOrder(pack?.productOrders, sourceOrders, { pack });
		}

		if (sourceTid && sourceTidAllotResult) {
			const pack = list.find(item => item.trades.map(i => i.tid).includes(sourceTid));
			const trade = pack?.trades.find(i => i.tid == sourceTid);
			mapAllotNumToOrder(trade?.orders, sourceTidAllotResult.oidList);
			mapAllotNumToOrder(pack?.productOrders, sourceTidAllotResult.oidList, { pack });
		}
		setList([...list]);
	};

	// * 解除/占用预占库存
	updateTradeOccupied = (opsType: ValueOf<typeof OccupyTradeOptType>, list: IPackage[]) => {
		let _list = [];
		let ignoreList = [];
		let statusNotSupportList = [];
		list.forEach(pack => {
			if (pack.trades.every(trade => ![TradeStatus.卖家部分发货, TradeStatus.等待买家付款, TradeStatus.等待卖家发货].includes((trade.status as TradeStatus)))) {
				statusNotSupportList.push(pack.togetherId);
			} else {
				pack.trades.forEach(trade => {
					let item: TradeOccupiedOpsRequest['list'][number] = {
						tid: trade.tid,
						sellerFlagSys: _.cloneDeep(trade.sellerFlagSys),
						sellerFlag: +trade.sellerFlag,
						platformType: pack.platform,
						sellerId: trade.sellerId,
					};
					let oidList: TradeOccupiedOpsRequest['list'][number]['oidList'] = [];
					let tradeIsIgnore = false;
					trade.orders.forEach(order => {
						if ((order.occupiedStockStatus && order.occupiedStockStatus !== opsType) || !order.occupiedStockStatus) {
							oidList.push(order.oid);
						}

						if (order.ignore) {
							tradeIsIgnore = true;
						}
					});

					if (tradeIsIgnore) {
						ignoreList.push(trade.tid);
					} else if (oidList.length) {
						item.oidList = oidList;
						_list.push(item);
					}
				});
			}
		});
		if (statusNotSupportList.length) {
			Modal.warning({
				title: '提示',
				content: (
					<div style={ { maxHeight: '400px', overflowY: 'auto' } }>
						<div>
							以下订单状态异常，无法手动占用或释放库存。
						</div>
						{statusNotSupportList.map(tid => (
							<div>{tid}</div>
						))}
					</div>
				)
			});
		} else if (ignoreList.length) {
			Modal.warning({
				title: '提示',
				content: (
					<div style={ { maxHeight: '400px', overflowY: 'auto' } }>
						<div>
							以下订单未建立关联关系或者无需扣减库存，无法手动占用或释放库存。
						</div>
						{ignoreList.map(tid => (
							<div>{tid}</div>
						))}
					</div>
				)
			});
		} else if (!_list.length) {
			Modal.warning({
				title: '提示',
				content: (
					<div>{`当前操作订单${opsType === OccupyTradeOptType.占用 ? '已占用' : '已释放或未占用'}`}</div>
				)
			});
		} else {
			TradeOccupiedOpsApi({
				opsType,
				list: _list,
			}).then(res => {
				let errorTypeEnum;
				let errorTidList = [];
				let errorSysSkuIdList = [];
				let errorMsg = '';
				res.forEach(item => {
					errorMsg = item?.data?.errorMsg;
					errorTypeEnum = item?.data?.errorTypeEnum;
					errorTidList = errorTidList.concat(item?.data?.errorTidList);
					errorSysSkuIdList = errorSysSkuIdList.concat(item?.data?.errorSysSkuIdList);
				});

				if (errorMsg) {
					Modal.warning({
						title: '提示',
						content: (
							<div style={ { maxHeight: '400px', overflowY: 'auto' } }>
								{errorMsg}
							</div>
						)
					});
					return;
				}

				if (errorTypeEnum === OccupyErrorTypeEnum.订单状态发生改变) {
				// * 订单状态发生变化
					Modal.warning({
						title: '以下订单状态发生变化，请刷新后再试',
						content: (
							<div style={ { maxHeight: '400px', overflowY: 'auto' } }>
								{errorTidList?.map(tid => (
									<p>{tid}</p>
								))}
							</div>
						)
					});
				} else if (errorTypeEnum === OccupyErrorTypeEnum.订单商品库存不足) {
					// * 库存不足
					Modal.warning({
						title: '以下商品库存不足，订单无法占用库存',
						content: (
							<div style={ { maxHeight: '400px', overflowY: 'auto' } }>
								{errorSysSkuIdList?.map(item => (
									<p>{item.sysSkuAlias}（{item.skuOuterId}）</p>
								))}
							</div>
						)
					});
				} else {
					let messageText = '';
					if (opsType === OccupyTradeOptType.占用) {
						messageText = list.length > 1 ? '您选择的订单库存占用成功' : '订单库存占用成功';
					} else if (opsType === OccupyTradeOptType.释放) {
						messageText = list.length > 1 ? '您选择的订单库存已全部释放' : '订单库存释放成功，释放成功的订单在发货时仍然会扣除库存';
					}

					messageText && message.success(messageText);

					runInAction(() => {
						list.forEach(pack => {
							pack.occupiedStockStatus = opsType;
							pack.trades.forEach(trade => {
								trade.orders.forEach(order => {
									order.occupiedStockStatus = opsType;
								});
							});
						});
					});
				}
			});
		}
	}

	// * 批量更新订单
	batchUpdateTrade = (list: IPackage[]) => {
		message.loading({
			content: '订单信息更新中',
			duration: 0,
			key: 'batchUpdateTrade',
		});
		TradeTradeDetailGetApi({
			tradeInfos: list.map(item => ({
				tids: item.tids[0].split('|')
			})),
		}).then(res => {
			this.parentStore.tradeListStore.handleUpdateList({
				type: 'batchUpdateTrade',
				data: res.data.list,
			});
		}).finally(() => {
			message.destroy('batchUpdateTrade');
			setTimeout(() => {
				message.success('订单信息更新完成');
			}, 1000);
		});
	}
}

export default TradeOptStore;
