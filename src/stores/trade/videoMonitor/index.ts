import { makeAutoObservable, runInAction } from "mobx";
import { notification } from "antd";
import { 
	IndexMonitoringQueryDeviceCameraApi, 
	IndexMonitoringQueryMonitoringDeviceApi,
	IndexMonitoringGetMonitoringDeviceBrandApi
} from '@/apis/setting/monitoringDevice';
import PrintCenter from '@/print/index';

// 发送消息的接口
interface SendMessageData {
	api: string;
	data?: any;
}

// 接收消息的接口
interface ReceiveMessageData {
	api: string;
	result?: number;
	msg?: string;
	data?: any;
}

// 消息回调接口
interface MessageCallback {
	api: string;
	callback: (response: ReceiveMessageData) => void;
	timestamp: number;
	timeout?: any;
}

export class VideoMonitor {
	constructor() {
		makeAutoObservable(this);
	}

	isIpcModel: boolean = true; // 是否是IPC模式

	isKdzsPrintComponent: boolean = false; // 是否安装了快递助手ERP聚合控件

	// WebSocket相关 - 改为public，方便外部访问
	webSocket: WebSocket | null = null;

	reconnectAttempts: number = 0; // 重连次数

	reconnectTimer: any = null; // 重连定时器

	isManualClose: boolean = false; // 是否手动关闭

	// 添加可观察的连接状态
	connected: boolean = false;

	// 消息回调管理 - 改为public
	messageCallbacks: Map<string, MessageCallback> = new Map();

	ipcDeviceList: any[] = [];

	nvrDeviceList: any[] = [];

	brandList: any[] = [];

	// 是否连接成功 - 使用可观察的connected状态
	get isSocketConnected() {
		return this.connected && this.webSocket?.readyState === WebSocket.OPEN;
	}

	setIsIpcModel = (isIpc: boolean = true) => {
		this.isIpcModel = isIpc;
	}

	// 检查快递助手ERP聚合控件
	checkKdzsPrintComponent = async() => {
		const res = await PrintCenter.checkKdzsPrintComponent(false);
		runInAction(() => {
			this.isKdzsPrintComponent = res;
		});
		return res;
	}

	// 检查快递助手ERP聚合控件, 未安装弹出弹框
	checkKdzsPrintComponentStatus = async() => {
		await PrintCenter.checkKdzsPrintComponent(true);
		runInAction(() => {
			this.isKdzsPrintComponent = true;
		});
		return true;
	}

	connectWs = () => {
		// 如果已经有连接或者没有安装控件，则不连接
		if (this.isSocketConnected || !this.isKdzsPrintComponent) {
			console.log('WebSocket已连接或控件未安装，跳过连接');
			return;
		}

		// 先断开现有连接，确保清理干净
		this.disconnectWs();

		try {
			console.log('ERP聚合控件开始连接: ws://127.0.0.1:55555');
			
			// 重置手动关闭标志，允许自动重连
			this.isManualClose = false;
			
			this.webSocket = new WebSocket("ws://127.0.0.1:55555");

			// 连接成功事件
			this.webSocket.onopen = () => {
				console.log('ERP聚合控件连接成功');
				
				// 使用runInAction更新可观察状态
				runInAction(() => {
					this.connected = true;
				});

				notification.success({
					message: "系统提示",
					description: "ERP聚合控件连接成功",
					duration: 3
				});

				// 重置重连计数
				this.reconnectAttempts = 0;
			};

			// 接收消息事件
			this.webSocket.onmessage = (event) => {
				this.handleMessage(event?.data);
			};

			// 连接断开事件
			this.webSocket.onclose = (event) => {
				console.log('ERP聚合控件连接关闭:', event.code, event.reason);
				
				// 使用runInAction更新可观察状态
				runInAction(() => {
					this.connected = false;
				});
				
				// 清空回调映射表
				this.clearMessageCallbacks();

				// 自动重连 - 只有在非手动关闭的情况下才重连
				if (!this.isManualClose && this.reconnectAttempts < 4) {
					this.scheduleReconnect();
				} else if (this.isManualClose) {
					console.log('手动关闭连接，不进行自动重连');
				}
			};

			// 连接错误事件
			this.webSocket.onerror = (error) => {
				console.log('ERP聚合控件连接失败:', error);
				
				// 使用runInAction更新可观察状态
				runInAction(() => {
					this.connected = false;
				});

				notification.warning({
					message: "系统提示",
					description: "ERP聚合控件连接失败",
					duration: 3
				});
			};

		} catch (error) {
			console.error('创建WebSocket连接失败:', error);
			
			// 使用runInAction更新可观察状态
			runInAction(() => {
				this.connected = false;
			});

			notification.error({
				message: "系统提示",
				description: "ERP聚合控件连接失败",
				duration: 3
			});
		}
	}

	// 处理接收到的消息 - 改为public
	handleMessage = (data: string) => {
		try {
			const messageData: ReceiveMessageData = JSON.parse(data);
			console.log('ERP聚合控件收到消息:', messageData);
			// 检查是否有对应的回调
			if (messageData.api && this.messageCallbacks.has(messageData.api)) {
				const callbackInfo = this.messageCallbacks.get(messageData.api)!;
				callbackInfo?.timeout && clearTimeout(callbackInfo.timeout);
				this.messageCallbacks.delete(messageData.api);
				callbackInfo.callback(messageData);
			} else {
				// 没有回调，根据api类型处理消息
				this.handleMessageByApi(messageData);
			}
		} catch (error) {
			// 如果不是JSON格式，按原始数据处理
			console.log('ERP聚合控件收到原始消息:', data);
		}
	}

	// 根据api类型处理消息 - 改为public
	handleMessageByApi = (messageData: ReceiveMessageData) => {
		const { api, result, msg, data } = messageData;
		
		// 根据不同的api类型处理消息
	
	}

	// 断开连接
	disconnectWs = () => {
		console.log('开始断开WebSocket连接');
		
		// 设置手动关闭标志，防止自动重连
		this.isManualClose = true;
		
		// 清理重连定时器
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer);
			this.reconnectTimer = null;
		}

		// 关闭WebSocket连接
		if (this.webSocket) {
			// 先移除事件监听器，避免重复触发
			this.webSocket.onopen = null;
			this.webSocket.onmessage = null;
			this.webSocket.onclose = null;
			this.webSocket.onerror = null;
			
			// 关闭连接
			this.webSocket.close();
			this.webSocket = null;
		}

		// 使用runInAction更新可观察状态
		runInAction(() => {
			this.connected = false;
		});

		// 清空消息回调
		this.clearMessageCallbacks();
		
		console.log('ERP聚合控件已断开连接');
	}

	// 发送消息 - 支持可选回调
	sendMessage = (api: string, data: any = {}, callback?: (response: ReceiveMessageData) => void) => {
		if (!this.isSocketConnected) {
			console.log('WebSocket未连接，无法发送消息');
			if (callback) {
				callback({ api, result: -1, msg: '聚合控件未连接', data: {} });
			}
			return;
		}

		try {
			// 构造发送的消息
			const messageToSend: SendMessageData = {
				api,
				data
			};

			// 如果有回调，保存到映射表中
			if (callback) {
				let timeout: any = null;

				// 有些没有response的接口，不设置超时
				// if (!['start_nvr_preview', 'close_nvr_preview', 'start_record', 'stop_record', 'start_nvr_record', 'stop_nvr_record']?.includes(api)) {
				// 	timeout = setTimeout(() => {
				// 		// 超时处理
				// 		if (this.messageCallbacks.has(api)) {
				// 			this.messageCallbacks.delete(api);
				// 			callback({ api, result: -1, msg: '请求超时', data: {} });
				// 		}
				// 	}, 10000); // 10秒超时
				// }

				this.messageCallbacks.set(api, {
					api,
					callback,
					timestamp: Date.now(),
					timeout
				});
			}

			// 发送消息
			this.webSocket!.send(JSON.stringify(messageToSend));
			console.log('ERP聚合控件发送消息:', messageToSend);

		} catch (error) {
			console.error('发送消息失败:', error);
			if (callback) {
				callback({ api, result: -1, msg: '发送消息失败', data: {} });
			}
		}
	}

	// 清空消息回调
	clearMessageCallbacks = () => {
		this.messageCallbacks.forEach((callbackInfo) => {
			callbackInfo?.timeout && clearTimeout(callbackInfo.timeout);
		});
		this.messageCallbacks.clear();
	}

	// 安排重连
	private scheduleReconnect = () => {
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer);
		}

		this.reconnectAttempts++;
		const delay = 3000 * Math.pow(2, this.reconnectAttempts - 1); // 指数退避

		console.log(`ERP聚合控件 ${this.reconnectAttempts}/4 次重连，延迟 ${delay}ms`);

		this.reconnectTimer = setTimeout(() => {
			this.connectWs();
		}, delay);
	}

	setIpcDeviceList = (list: any[]) => {
		this.ipcDeviceList = list;
	}

	// 获取摄像头设备列表
	getDeviceCameraList = async() => {
		if (this.ipcDeviceList.length) {
			return this.ipcDeviceList;
		}
		try {
			const res = await IndexMonitoringQueryDeviceCameraApi({});
			this.ipcDeviceList = res;
			return res;
		} catch (error) {
			console.error('获取拍摄设备列表失败:', error);
			return [];
		}
	}

	setNvrDeviceList = (list: any[]) => {
		this.nvrDeviceList = list;
	}

	// 获取设备列表
	getDeviceList = async() => {
		if (this.nvrDeviceList.length) {
			return this.nvrDeviceList;
		}
		try {
			const res = await IndexMonitoringQueryMonitoringDeviceApi({});
			this.nvrDeviceList = res;
			return res;
		} catch (error) {
			console.error('获取设备列表失败:', error);
			return [];
		}
	}

	getBrandList = async() => {
		if (this.brandList.length) {
			return this.brandList;
		}
		try {
			const res = await IndexMonitoringGetMonitoringDeviceBrandApi({});
			this.brandList = res;
			return res;
		} catch (error) {
			console.error('获取设备品牌列表失败:', error);
			return [];
		}
	}
}
const videoMonitorStore = new VideoMonitor();
export default videoMonitorStore;
