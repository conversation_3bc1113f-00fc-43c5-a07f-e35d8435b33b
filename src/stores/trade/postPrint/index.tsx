import React from "react";
import { computed, makeAutoObservable } from "mobx";
import { notification } from "antd";
import { isEmpty } from "lodash";
import dayjs from "dayjs";
import { CloseCircleOutlined } from "@ant-design/icons";
import { omitProps, Store } from "..";
import { playAudio } from "@/pages/Trade/ScanPrint/utils";
import voice合单 from '@/assets/mp3/合单.mp3';
import voice多件 from '@/assets/mp3/多件.mp3';
import voice扫描成功 from '@/assets/mp3/扫描.mp3';
import voice未匹配 from '@/assets/mp3/未匹配.mp3';
import { TradeUserAddUserCustomSetApi } from "@/apis/trade";
import { editConfigApi, getConfigApi, queryScanTradeApi, TradeUserGetProductContentCustomSetApi } from "@/apis/trade/postPrint";
import { TradeScanPrintConfigVO } from "@/types/trade/postPrint";
import message from "@/components/message";
import { getTradeFlag, handlePackageList } from "@/pages/Trade/utils";
import { getUniqueKeyByOrderInfo } from "@/pages/Trade/PostPrint/utils";

class PostPrintStore {
	constructor(parentStore: omitProps) {
		makeAutoObservable(this);
		this.parentStore = parentStore;
	}

	parentStore: Partial<Store>;

	packWsHandler = null;

	setPackWsHandler = (v) => {
		this.packWsHandler = v;
	}

	packWsHandlerSendFail = (msg) => {
		if (!this.packWsHandler?.webSocket) return;
		notification.open({
			message: "发送打包机验货失败指令",
			description: msg,
			icon: <CloseCircleOutlined />,
			style: {
				color: "#f00"
			}
		});
		this.packWsHandler?.webSocket?.send(JSON.stringify({ "Code": "0", "Msg": msg }));
	}

	packWsHandlerLogs = [];

	packWsHandlerLogsAdd = (msg) => {
		this.packWsHandlerLogs.unshift({ msg, time: dayjs().format("YYYY-MM-DD HH:mm:ss") });
	}

	packWsHandlerLogsAddClear = () => {
		this.packWsHandlerLogs = [];
	}

	// 匹配到的订单列表
	postPrintList = [];

	setPostPrintList = (v:{[k:string]:any}[]) => {
		this.postPrintList = v;
	}


	// 页面是否loading
	isLoading = false;

	setLoadingFalse = () => {
		this.isLoading = false;
		this.setTip();
	}

	setLoadingTrue = (tip) => {
		this.isLoading = true;
		this.setTip(tip);
	}

	// loading时的tip
	tip = "";

	setTip = (v?:string) => {
		this.tip = v;
	}

	// 打印结果
	printRes:{[k:string]:any} = {
		successText: null, // 成功信息
		errorText: null, // 失败信息
		showRes: null, // 是否展示结果
	}

	setPostPrintRes = (v:{[k:string]:any}) => {
		this.printRes = v;
	}


	// 查询订单列表
	getPostPrintTradeList = async(params) => {
		this.setLoadingTrue("正在查询订单，请稍等...");
		let list = [];
		let errorText = '';
		try {
			const res = await queryScanTradeApi(params);
			this.setLoadingFalse();
			if (res.isMerge) {
				res.mergeTradeCount = res.trades.length;
			}
			if (res) {
				list = [res];
			} else {
				this.packWsHandlerSendFail("扫描失败");
				message.warn('扫描失败');
			}
		} catch (e) {
			this.setLoadingFalse();
			errorText = e.errorMessage || '未匹配到订单';
			this.packWsHandlerSendFail(errorText);
			console.log('error', e);
		}
		if (list.length) {
			this.resetStoreNew();
			handlePackageList(list, true, null, false).then(res => {
				this.setPostPrintList(res);
			});
			if (list[0].isMerge) {
				playAudio(voice合单);
			} else if (list[0]?.trades[0]?.orders?.length > 1 || +list[0]?.trades[0]?.orders[0]?.num > 1) {
				playAudio(voice多件);
			} else {
				playAudio(voice扫描成功);
			}
		} else {
			this.setPostPrintRes({ showRes: true, errorText });
			playAudio(voice未匹配);
			this.packWsHandlerSendFail(errorText);
		}
		return list;
	}

	// 扫描设置

	settingConfig:TradeScanPrintConfigVO = {};

	defaultSettingConfig = () => {
		return {
			scanType: "BAR_CODE",
			matchResultType: "FIRST_PAY_TIME",
			sellAttributeTypeList: ["ITEM_1_SKU_1"],
			tradeStatusList: ["WAIT_SELLER_SEND_GOODS"],
			isRefundStatus: false,
			isAutoPrint: true,
			isAutoSend: false,
			isPrintItemTag: false,
			autoPrintInterceptTypeList: ["PRINTED", "SHIPPED", "REFUND", "OUT_OF_STOCK"],
			msgRemarksTypeList: [],
			sellerFlagList: [],
			tradeLabelSellerFlagList: [],
			markScanPrintSet: {
				onlySkip: true,
				skipAndMark: false,
				skipAndMarkSet: {
					autoSellerFlag: false,
					autoPending: true,
					sellerFlagList: [],
				}
			},
		};
	}

	getSettingConfig = async() => {
		let config = { ...this.settingConfig }; 
		let res = {};
		if (isEmpty(this.settingConfig)) {
			config = this.defaultSettingConfig();
			res = await getConfigApi();
		}
		Object.keys(res)?.forEach(key => {
			if (res[key]) {
				config[key] = res[key];
			}
		});
		// config = { ...config, ...res };
		if (config?.autoPrintInterceptTypeList?.includes("MSG_REMARKS")) {
			config?.autoPrintInterceptTypeList?.push(...config.msgRemarksTypeList);
			config.autoPrintInterceptTypeList = config.autoPrintInterceptTypeList.filter(item => item !== "MSG_REMARKS");
		}
		config.autoPrintInterceptTypeList = Array.from(new Set(config?.autoPrintInterceptTypeList));
		this.settingConfig = config;
		return config;
	}

	saveSettingConfig = async(params, noApi = false) => {
		if (!noApi) {
			await editConfigApi(params);
		}
		this.settingConfig = {
			...this.settingConfig,
			...params,
		};
	}

	resetStoreNew = () => {
		this.allGoodOrderList = [];
		this.allGoodOrderProgress = {};
		this.postPrintList = [];
		this.curScanGoodInfo = {};
		this.selectedRowKeys = [];
		this.curOrderPrintSuccess = false;
	}

	// 重置store
	resetStore = () => {
		this.setCurOrderPrintSuccess(true);
	}

	allGoodOrderList = []

	allGoodOrderProgress = {};

	selectedRowKeys = []

	curScanGoodInfo:{[k: string]: any} = {};

	curOrderPrintSuccess = false;

	setCurOrderPrintSuccess = (v) => {
		this.curOrderPrintSuccess = v;
	}

	setCurScanGoodInfo = (v) => {
		this.curScanGoodInfo = v;
	}
	
	setSelectedRowKeys = (keys) => {
		this.selectedRowKeys = keys;
	}

	setAllGoodOrderProgress = (v) => {
		this.allGoodOrderProgress = v;
	}

	updateAllGoodOrderProgress = (order, num = 0) => {
		const allGoodOrderProgress = { ...this.allGoodOrderProgress };
		if (num) {
			allGoodOrderProgress[order.oid].current = +num;
		} else {
			allGoodOrderProgress[order.oid].current++;
		}
		if (allGoodOrderProgress[order.oid].current === allGoodOrderProgress[order.oid].total) {
			this.setSelectedRowKeys([...this.selectedRowKeys, getUniqueKeyByOrderInfo(order)]);
		}
		this.setAllGoodOrderProgress(allGoodOrderProgress);
	}

	setAllGoodOrderList = (v) => {
		this.allGoodOrderList = v;
		const allGoodOrderProgress = {};
		v.forEach(item => {
			allGoodOrderProgress[item.oid] = {
				total: +item.num,
				current: 0,
			};
		});
		this.setAllGoodOrderProgress(allGoodOrderProgress);
	};

	@computed get scanedCount() {
		let total = 0;
		let current = 0;
		
		for (let key in this.allGoodOrderProgress) {
			const curGoodScanInfo = this.allGoodOrderProgress[key];
			total += curGoodScanInfo?.total;
			current += curGoodScanInfo?.current;
		}
		return {
			total,
			current,
			isForce: this.isForce,
		};
	}

	// 原有设置
	oldSetting = {}

	// 显示高级设置
	advancedSettingModalVisible = false;

	// 产品内容设置
	contentSettingModalVisible = false;

	setContentSettingModalVisible = (v) => {
		this.contentSettingModalVisible = v;
	}

	contentSetting :any= {};

	getContentSettingConfig = async() => {
		// const config = await memoFn.getContentSet();
		const config = await TradeUserGetProductContentCustomSetApi({ });
		this.contentSetting = config;
		return config;
	}

	updateContentSettingConfig = async(v) => {
		await TradeUserAddUserCustomSetApi(v);
		this.contentSetting = v;
	}

	isForce:boolean = false;

	// 是否手动备齐

	ignoreScanOrder = {};

	setIgnoreScanOrder = (v) => {
		this.ignoreScanOrder = v;
	}

	getIgnoreInspectOrder = () => {
		return this.ignoreScanOrder;
	}

	// 页面整体loading

	showLoading = false;

	setShowLoading = (v:boolean) => {
		this.showLoading = v;
	}

	// 是否处于发货中

	sendPending = false;

	setSendPending= (v:boolean) => {
		this.sendPending = v;
	}

}

export default PostPrintStore;
