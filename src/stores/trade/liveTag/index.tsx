import { makeAutoObservable, runInAction, computed } from "mobx";
import dayjs from "dayjs";
import message from "@/components/message";
import { omitProps, Store } from "..";
import { ItemTakeGoodsLabelSelectWithPageResponse, ItemTakeGoodsLabelUpdateWithBatchRequest } from "@/types/trade/takeGoodsLabel";
import { TradeLiveTagUpdatePrintStatusApi } from "@/apis/trade";
import scanPrint from "@/stores/trade/scanPrint";
import { tradeStore } from "@/stores";
import { labelTypeEnum } from "@/pages/Trade/GoodsTag/components/BottomCom/constants";

interface IPrintStatus {
	isSuccess: boolean,
	successCount: number,
	failCount: number,
	labelIds?: string[],
	printType?: labelTypeEnum | string
}
class LiveTagListStore {
	constructor(parentStore: omitProps) {
		makeAutoObservable(this);
		this.parentStore = parentStore;
	}

	parentStore: Partial<Store>

	getIsFirstSend= async() => {
		const { getScanPrintSetting } = scanPrint;
		const res = await getScanPrintSetting();
		// todo yzy
		return res?.firstSendMode || false;
	}

	@computed get checkedCount() {
		let checkedList = this.list.filter((item:any) => item.isChecked);
		let checkedListLen = checkedList.length;
		runInAction(() => {
			let status: number = 0.5;
			if (checkedListLen && checkedListLen === this.list.filter((item:any) => !item.noGoodsLink).length) {
				status = 1;
			} else if (checkedListLen === 0) {
				status = 0;
			}

			this.listCheckStatus = status;
		});
		return checkedListLen;
	}

	isSingle = false;

	singleItem = [];

	setSingleItem = (singleItem) => {
		this.singleItem = singleItem;
		this.isSingle = Array.isArray(singleItem) && singleItem.length > 0;
	}

	list:ItemTakeGoodsLabelSelectWithPageResponse["data"]["list"] = [];

	listCheckStatus: number = 0;

	setListCheckStatus = (status: number) => {
		this.listCheckStatus = status;
	}

	setList = (list: ItemTakeGoodsLabelSelectWithPageResponse["data"]["list"]) => {
		this.list = list;
	}

	printList = [];
	
	setPrintList = (list: ItemTakeGoodsLabelSelectWithPageResponse["data"]["list"]) => {
		this.printList = list;
	}

	handleChoiceChange = (choiceItem:any) => {
		const noTradeTipFn = (checkCount: number) => {
			if (checkCount === 0) {
				message.info('当前没有符合勾选的订单');
			}
		};
		let _list = this.list;
		if (_list.length) {
			let checkCount = 0;
			switch (choiceItem.type) {
				case 0.5:
					// 半选 过滤挂起订单
					_list.forEach((item:any) => {
						item.isChecked = true;
						checkCount++;
					});
					noTradeTipFn(checkCount);
					break;
				case 2:
					// 全选
					_list.forEach((item:any) => {
						item.isChecked = true;
						checkCount++;
					});
					noTradeTipFn(checkCount);
					break;
				case 4:
					// 反选
					_list.forEach((item:any) => {
						item.isChecked = !item.isChecked;
					});
					break;
				case "cancelLabel":
					_list.forEach((item:any) => {
						if (choiceItem.cancelList.includes(item.ptTid)) {
							item.isChecked = false;
						}
					});
					break;
				default:
					break;
			}

		}
	}

	// 记录上一次列表点击的index
	oldIndex: number = -1;

	togglePackageSelectStatus = (pack) => {
		pack.isChecked = !pack.isChecked;
	}

	cancelKeyDown = () => {
		this.oldIndex = -1;
	}

	dealShift = ({ pack, index }) => {
		const { isShiftDown } = tradeStore.tradeListStore;
		// 按下shift 时选择
		if (isShiftDown) {
			let start = -1;
			let end = -1;
			// 找到开始和结束位置
			if (index > this.oldIndex) {
				start = this.oldIndex;
				end = index;
			} else {
				start = index;
				end = this.oldIndex;
			}
			// 判断是选中还是取消选中
			const checked = this.list[this.oldIndex].isChecked;
			// 将shift选中的部分批量选中或取消选中
			for (let i = start; i <= end; i++) {
				this.list[i].isChecked = checked;
			}
		} else {
			this.oldIndex = index;
			this.togglePackageSelectStatus(pack);
		}
	}

	printStatus:IPrintStatus = {
		isSuccess: false,
		successCount: 0,
		failCount: 0,
		labelIds: [],
		printType: 'zbd'
	};

	setPrintStatus = (data:IPrintStatus) => {
		this.printStatus = data;
	}

	isPrinting = false;

	setIsPrinting = (val) => {
		this.isPrinting = val;
	}

	afterSavePrint = ({ list, selectList, printType }:{list:any[], selectList: [], printType?:string}) => {
		this.updateOrderStatusWithBatch({ list, selectList, printType });
		const failCount = 0;
		if (this.singleItem[0] || this.listCheckStatus !== 0) {
			this.setPrintStatus({ isSuccess: true, printType, successCount: list.length || 0, failCount });
		}
	}

	updateOrderStatusWithBatch = ({ list, selectList, printType }:{list:ItemTakeGoodsLabelUpdateWithBatchRequest["updateLabelInfos"]}) => {
		console.log(list, selectList, printType);
		if (list.length) {
			TradeLiveTagUpdatePrintStatusApi(selectList.map(item => {
				return {
					liveTradePrintTaskId: item.liveTradePrintTaskId,
					tid: item.tid,
					livePrintStatus: 1,
					requestVersion: "v2",
				}; 
			})).then((res) => {
				console.log("打印成功 TradeLiveTagUpdatePrintStatusApi:", res);
				this.handleChoiceChange({
					type: "cancelLabel",
					cancelList: selectList.map(item => item.ptTid)
				});
			}).finally(() => {
				this.setIsPrinting(false);
			});
		} else {
			this.setIsPrinting(false);
		}
	}
}
export default LiveTagListStore;