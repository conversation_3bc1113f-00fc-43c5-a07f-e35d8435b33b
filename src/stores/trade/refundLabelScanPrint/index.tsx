import { makeAutoObservable } from "mobx";
import { isEmpty } from "lodash";
import { omitProps, Store } from "..";
import { TradeDictQueryDictApi } from "@/apis/trade/search";
import { ItemTakeGoodsLabelRefundLabelMatchApi } from "@/apis/trade/refundLabelScanPrint";
import { session } from "@/libs/db";
import { ItemMatchType, MatchDimensions, MatchModel, REFUND_LABEL_TAKE_LABEL_SETTING, SysItemMatchType } from "@/pages/Trade/RefundLabelScanPrint/constants";
import { ItemTakeGoodsLabelConfigSelectApi } from "@/apis/trade/takeGoodsLabel";
import userStore from "@/stores/user";
import { playAudio } from "@/pages/Trade/ScanPrint/utils";
import voice标签错误 from '@/assets/mp3/标签错误.mp3';
import voice扫描成功 from '@/assets/mp3/扫描.mp3';
import AlertMsg from "@/pages/Trade/RefundLabelScanPrint/components/AlertMsg";

class RefundLabelScanPrintStore {
	constructor(parentStore: omitProps) {
		makeAutoObservable(this);
		this.parentStore = parentStore;
	}

	parentStore: Partial<Store>;

	// 是否可以自动打印

	isAutoPrintEnabled = false;

	setIsAutoPrintEnabled = (v:boolean) => {
		this.isAutoPrintEnabled = v;
	}

	// 拿货标签模板设置Modal可见性

	takeLabelTemplateModalVisible = false;

	setTakeLabelTemplateModalVisible = (v:boolean) => {
		this.takeLabelTemplateModalVisible = v;
	}

	// 用户之前保存的拿货标签模板
	userSetTakeLabelTemplate:string|number = "";

	// 用户之前保存的拿货标签打印机
	userSetTakeLabelPrinter = "";


	// 拿货标签模板
	takeLabelTemplate:any = undefined;

	setTakeLabelTemplate = (v) => {
		this.takeLabelTemplate = v;
	}

	// 拿货标签打印机
	takeLabelPrinter = undefined;

	setTakeLabelPrinter = (v) => {
		this.takeLabelPrinter = v;
	}

	// 拿货标签是否正在打印
	isTakeLabelPrinting = false;

	setIsTakeLabelPrinting = (v) => {
		this.isTakeLabelPrinting = v;
	}

	// 打印后自动发货
	autoSend = false;

	setAutoSend = (v:boolean) => {
		this.autoSend = v;
	}

	// 当整个store中的备货单列表或打印机列表更新的时候，需要重新计算拿货标签模板和打印机。因为可能用户之前设置的打印机或者模板已经不存在了

	updateTemplateAndPrint = () => {
		// const { bhdXbqTempList, scanPrintStore: { printersList } } = this.parentStore;
		// bhdXbqTempList.forEach(template => {
		// 	if (template.Mode_ListShowId == this.userSetTakeLabelTemplate) {
		// 		this.setTakeLabelTemplate(template.Mode_ListShowId);
		// 	}
		// });
		// printersList.forEach(printer => {
		// 	if (printer == this.userSetTakeLabelPrinter) {
		// 		this.setTakeLabelPrinter(this.userSetTakeLabelPrinter);
		// 	}
		// });
	}

	// 获取用户拿货标签模板配置
	getUserTakeLabelTemplateConfig = async() => {
		let refundLabelTakeLabelSettingTemp = session.get(REFUND_LABEL_TAKE_LABEL_SETTING);
		if (!refundLabelTakeLabelSettingTemp) {
			await TradeDictQueryDictApi({
				userDictEnum: "REFUND_LABEL_REMATCH_SET"
			}).then(res => {
				if (res.value) {
					session.set(REFUND_LABEL_TAKE_LABEL_SETTING, res.value);
					try {
						const value = JSON.parse(res.value || "{}");
						refundLabelTakeLabelSettingTemp = value;
					} catch (e) {
						console.log(e);
					}
				}
			});
		}
		const { bhdXbqTempList, scanPrintStore: { printersList } } = this.parentStore;
		const bhdXbqTempIsExist = bhdXbqTempList.find(template => template.Mode_ListShowId == refundLabelTakeLabelSettingTemp.defaultReturnLabelTemplate);
		const printerIsExist = printersList.includes(refundLabelTakeLabelSettingTemp.defaultPrinter);
		if (bhdXbqTempIsExist) {
			this.setTakeLabelTemplate(bhdXbqTempIsExist.Mode_ListShowId);
		} else {
			this.setTakeLabelTemplate(undefined);
		}
		if (printerIsExist) {
			this.setTakeLabelPrinter(refundLabelTakeLabelSettingTemp.defaultPrinter);
		} else {
			this.setTakeLabelPrinter(undefined);
		}
	}

	// 匹配到的订单列表
	refundLabelMatchList = [];

	setRefundLabelMatchList = (v:{[k:string]:any}[]) => {
		this.refundLabelMatchList = v;
	}

	// 选择的订单列表
	selectedRows = [];

	setSelectedRows = (v:[]) => {
		this.selectedRows = v;
	}

	// 页面是否loading
	isLoading = false;

	setLoadingFalse = () => {
		this.isLoading = false;
		this.setTip();
	}

	setLoadingTrue = (tip) => {
		this.isLoading = true;
		this.setTip(tip);
	}

	// loading时的tip
	tip = "";

	setTip = (v?:string) => {
		this.tip = v;
	}

	// 打印结果
	printRes:{[k:string]:any} = {
		expressBillSuccess: null, // 快递单打印成功
		labelSuccess: null, // 标签打印成功
		sendSuccess: null, // 发货成功
		showRes: null, // 是否展示结果
	}

	setPrintRes = (v:{[k:string]:any}) => {
		this.printRes = v;
	}

	// 是否匹配
	isMatching:boolean = true;

	setIsMatching = (v:boolean) => {
		this.isMatching = v;
	}

	// 分页信息
	pagination = {
		current: 1,
		pageSize: 10,
		total: 0
	}

	setPagination = (v) => {
		this.pagination = v;
	}

	// 查询订单列表
	getTakeLabelMathTradeList = (params, fn) => {
		this.setLoadingTrue("正在查询订单，请稍等...");
		let { refundLabelMatch } = this.settingConfig;
		ItemTakeGoodsLabelRefundLabelMatchApi(params).then(res => {
			if (res.list) {
				const refundLabelMatchList = [];
				res.list.forEach(i => {
					// 按照产品要求，需要过滤掉当前唯一码对应的那笔订单
					if (i.isCurrentLabelTrade) {
						return;
					}
					refundLabelMatchList.push({
						...i,
						currentScanLabelId: params.labelId, // 把当前扫描的唯一码先存一份，不要从输入框获取，其他地方需要用到这个字段
					});
				});
				if (refundLabelMatch?.matchModel === MatchModel.一笔订单 && refundLabelMatchList[0]) {
					const { kddPrintedNotAutoPrint = false, buyerMessageNotAutoPrint = false, sellerCommentNotAutoPrint = false } = refundLabelMatch || {};
					if (!(kddPrintedNotAutoPrint && refundLabelMatchList[0].waybillPrintStatus != "none")
						&& !(buyerMessageNotAutoPrint && refundLabelMatchList[0].buyerMessage)
						&& !(sellerCommentNotAutoPrint && refundLabelMatchList[0].sellerMemo)) {
						this.selectedRows = refundLabelMatchList;
						this.isAutoPrintEnabled = true;
					}
				}
				this.setRefundLabelMatchList(refundLabelMatchList);
				if (res.pageNo == 1) {
					this.setPagination({
						current: res.pageNo,
						pageSize: res.pageSize,
						total: res.total
					});
				} else {
					this.setPagination({
						current: res.pageNo,
						pageSize: res.pageSize,
						total: this.pagination.total
					});
				}
				this.setIsMatching(!!refundLabelMatchList.length);
				if (refundLabelMatchList.length < 1 && fn) {
					fn();
				} else {
					playAudio(voice扫描成功);
				}
			}
		}).catch(e => {
			console.log('error', e);
			playAudio(voice标签错误);
		}).finally(() => this.setLoadingFalse());
	}

	// 扫描设置

	settingConfig = {};

	defaultSettingConfig = () => {
		const { isShowZeroStockVersion } = userStore;
		const refundLabelMatch = {
			matchWay: isShowZeroStockVersion ? MatchDimensions.商品 : MatchDimensions.货品,
			matchBy: isShowZeroStockVersion ? ItemMatchType["商品ID+规格ID"] : SysItemMatchType["货品ID+货品规格ID"],
			matchModel: MatchModel.所有订单,
			excludeServiceTagList: ['noPending'],
			excludeTradeExceptionList: [
				"noRisk",
				"pOrderNotUnfinished",
				"noOnlineShip"
			],
			fhdPrintStatus: [1, 2],
			labelPrintStatus: [1, 2],
			printStatus: [1, 2],
		};
		return refundLabelMatch;
	}

	getSettingConfig = async() => {
		if (!isEmpty(this.settingConfig)) {
			return this.settingConfig;
		}
		let refundLabelMatch = this.defaultSettingConfig();
		await ItemTakeGoodsLabelConfigSelectApi({}).then(async res => {
			try {
				if (res.scanSet) {
					const scanSetObj = JSON.parse(res.scanSet);
					if (!isEmpty(scanSetObj.refundLabelMatch)) {
						refundLabelMatch = {
							...refundLabelMatch,
							...scanSetObj.refundLabelMatch
						};
					}
					let isShowZeroStockVersion = false;
					await userStore.getUserInfo().then(async res => {
						isShowZeroStockVersion = +res?.version === 2 && +res?.versionType === 1;
					});

					if (isShowZeroStockVersion) {
						refundLabelMatch.matchWay = MatchDimensions.商品;
					}
					this.settingConfig = {
						...scanSetObj,
						refundLabelMatch,
					};
				}
			} catch (e) {
				console.log(e);
			}
		});
		return refundLabelMatch;
		// 通过接口获取配置

	}

	updateSettingConfig = (config) => {
		this.settingConfig = config;
	}

	// 重置store
	resetStore = () => {
		this.setRefundLabelMatchList([]);
		this.setSelectedRows([]);
		this.setLoadingFalse();
	}
}

export default RefundLabelScanPrintStore;
