import { action, computed, makeAutoObservable, runInAction } from "mobx";
import _ from "lodash";
import { message } from "antd";
import { TradePrintDelPrintFilterWordApi, TradePrintGetPrintFilterWordListApi, TradePrintGetPrintStyleApi, TradePrintSavePrintFilterConfigApi, TradePrintGetPrintFilterConfigApi, TradePrintSavePrintFilterWordApi, TradePrintSavePrintStyleApi } from "@/apis/trade/tradeSet";
import { TradePrintContentSetRule, TradeSetFilterWord } from "@/types/trade/tradeSet";
import { local } from "@/libs/db";
import { defaultCopySetting } from "@/pages/Trade/constants";
import { ICopySettingItem, IFilterOrderItem, IFilterOrderObj, IOrders } from "@/pages/Trade/interface";
import { tradeStore } from "@/stores";
import StallStore from "./stall";
import { calcFilterOrder } from "@/pages/Trade/utils";
import { FilterOrderSort } from "@/utils/enum/trade";
import { ItemFilterSettingSelectResponse } from "@/types/trade/index";

export class TradeSetStore {
	constructor() {
		makeAutoObservable(this);
	}

	// 格式过滤内容 中英文括号、【】、[]、{}、「」以及中间的内容过滤
	filterPatternList: string[];
	
	getFilterPatternList = () => {
		return this.filterPatternList;
	}

	/** 过滤词设置 */
	filterWord: string[];

	getFilterKeyWords(): string[] {
		return this.filterWord;
	}

	getFilterWord = async() => {
		if (this.filterWord) {
			return this.filterWord;
		}
		const res = await TradePrintGetPrintFilterConfigApi();
		runInAction(() => {
			this.filterWord = res?.filterKeywordList;
			this.filterPatternList = res?.filterPatternList;
		});
		return res?.extJson?.filterKeywordList;
	}

	addFilterWord = async(keywords:string) => {
		// 对要加入的多个关键词判断是否已经存在
		const repeatKeys = _.intersection(keywords, this.filterWord);
		if (repeatKeys.length) {
			message.error(`已经存在[${repeatKeys.join(',')}]`);
			return;
		}
		const keyArr = [...this.filterWord, ...keywords.split(",")];
		this.saveFilterWord({ filterKeywordList: keyArr });
	}

	saveFilterWord = async(saveObj:{filterKeywordList?: string[], filterPatternList?: string[]}) => {
		const _printContentSet = {
			...saveObj
		};
		this.setPrintContentSet({
			...this.printContentSet,
			extJson: { 
				...this.printContentSet?.extJson, 
				...saveObj
			} 
		});
		await TradePrintSavePrintFilterConfigApi(_printContentSet);
		runInAction(() => {
			this.filterWord = saveObj?.filterKeywordList || this.filterWord;
			this.filterPatternList = saveObj?.filterPatternList || this.filterPatternList;
		});
		return _printContentSet;
	}


	/** 打印内容设置 */
	printContentSet:TradePrintContentSetRule;

	getPrintContentSet = async() => {
		if (this.printContentSet) {
			// window.errorCollection?.customMessageUpload({
			// 	type: `获取打印内容设置缓存`, 
			// 	data: this.printContentSet
			// });
			return this.printContentSet;
		}
		const data = await TradePrintGetPrintStyleApi();
		// window.errorCollection?.customMessageUpload({
		// 	type: `获取打印内容设置api`, 
		// 	data
		// });
		runInAction(() => {
			this.printContentSet = data;
		});
		return data;
	}

	setPrintContentSet = (set: TradePrintContentSetRule) => {
		window.errorCollection?.customMessageUpload({
			type: `设置打印内容`, 
			data: set
		});
		this.printContentSet = set;
	}

	copySetting: ICopySettingItem[] = local.get('printBatch.copySetting') || defaultCopySetting;

	setCopySetting = (setting: ICopySettingItem[]) => {
		this.copySetting = setting;
		local.set('printBatch.copySetting', setting);
	}

	filterOrderSetting: ItemFilterSettingSelectResponse['data'];

	setFilterOrderSetting = (setting: ItemFilterSettingSelectResponse['data']) => {
		if (setting.showColumn === undefined) {
			setting.showColumn = 1;
		}
		this.filterOrderSetting = {
			...setting,
			...(setting?.jsonConfig || {}), // 这里把对象展开，方便展示
		};
	}

	@computed get filterOrderStickyTop(): number {
		if (this.filterOrderSetting.isFixed) {
			const { fixedTemplateComHeight } = tradeStore;
			let headerMenu = document.querySelector('.header-menu');
			return (headerMenu?.clientHeight || 0) + fixedTemplateComHeight;
		}
		return 0;
	}

	@computed get filterOrderArr(): IFilterOrderItem[] {
		const { tradeListStore: { list }, setting } = tradeStore;
		if (setting.filterOrder === 2 && list.length) {
			let _filterOrderObj: IFilterOrderObj = {};
			let singleItemNum = 0;
			let singleStyleMultiNum = 0;
			let multiStyleMultiNum = 0;
			list.forEach(pack => {
				pack.trades.forEach(trade => {
					trade.orders.forEach(order => {
						calcFilterOrder(order, _filterOrderObj, pack);
					});
				});
				if (pack.totalOrderNum == 1 && pack.totalGoodsNum == 1) {
					singleItemNum += 1;
				} else if (pack.totalOrderNum > 1 && pack.totalGoodsNum == 1) {
					singleStyleMultiNum += 1;
				} else {
					multiStyleMultiNum += 1;
				}
			});
			tradeStore.tradeListStore.setDiffStyleNumMap({ singleItemNum, singleStyleMultiNum, multiStyleMultiNum });
			for (const key in _filterOrderObj) {
				if (Object.prototype.hasOwnProperty.call(_filterOrderObj, key)) {
					const item = _filterOrderObj[key];
					item.checkStatus = '';
					if (item.checkedCount > 0) {
						item.checkStatus = 'checkHalf';
					}
					if (item.normalOrderTotalCount > 0 && item.normalOrderCheckCount === item.normalOrderTotalCount) {
						item.checkStatus = 'checkAll';
					}
					if (item.totalCount > 0 && item.normalOrderTotalCount === 0 && item.checkedCount === item.totalCount) {
						item.checkStatus = 'checkAll';
					}
				}
			}
			let arr: IFilterOrderItem[] = Object.values(_filterOrderObj);
			const sortType = this.filterOrderSetting?.jsonConfig?.newSort;
			if (sortType === FilterOrderSort["相同简称+规格名称排一起"]) {
				arr = _.sortBy(arr, item => item.order["skuPropertiesName"]);
				arr = _.sortBy(arr, item => item.order[FilterOrderSort.相同简称排一起]);
			} else if (sortType === FilterOrderSort["相同规格名称排一起"]) {
				arr = _.sortBy(arr, item => (item.order["skuPropertiesName"]));
			} else if (sortType === FilterOrderSort["相同简称+规格别名排一起"]) {
				arr = _.sortBy(arr, item => item.order[FilterOrderSort["相同简称+规格别名排一起"]]);
				arr = _.sortBy(arr, item => item.order[FilterOrderSort.相同简称排一起]);
			} else if (sortType === FilterOrderSort.相同简称排一起) {
				arr = _.sortBy(arr, item => item.totalCount);
				arr = arr.reverse();
				arr = _.sortBy(arr, item => item.order[FilterOrderSort.相同简称排一起]);
			} else if (sortType === FilterOrderSort.按数量排序) {
				arr = _.sortBy(arr, item => item[FilterOrderSort.按数量排序]);
				arr = arr.reverse();
			}
			return arr;
		}
		return [];
	}

	stallStore = new StallStore(this);
}

export type omitProps = Omit<Partial<TradeSetStore>, 'stallStore'>;
const tradeSetStore = new TradeSetStore();
export default tradeSetStore;