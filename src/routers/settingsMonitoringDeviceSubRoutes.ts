import { lazy } from 'react';
import { RouteType } from "@/types/schemas";

const settingsMonitoringDeviceSubRoutes: RouteType[] = [
	{
		path: "/settings/monitoringDevice/ipc",
		exact: true,
		name: 'IPC摄像头设备',
		component: lazy(() => import(/* webpackChunkName: "SettingsMonitoringDeviceIpc" */ "@/pages/Index/Settings/MonitoringDevice/Ipc")),
	},
	{
		path: "/settings/monitoringDevice/nvr",
		exact: true,
		name: 'NVR存储设备',
		component: lazy(() => import(/* webpackChunkName: "SettingsMonitoringDeviceNvr" */ "@/pages/Index/Settings/MonitoringDevice/Nvr")),
	},
	{
		path: "/settings/monitoringDevice/record",
		exact: true,
		name: '监控视频记录',
		component: lazy(() => import(/* webpackChunkName: "SettingsMonitoringDeviceRecord" */ "@/pages/Index/Settings/MonitoringDevice/Record")),
	}
];

export default settingsMonitoringDeviceSubRoutes;
