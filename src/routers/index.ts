import { Children, lazy } from "react";
import { RoutePointType, RouteType } from "@/types/schemas/index";
import {
	HomePage,
	LoginPage,
	RegisterPage,
	ForgetPwdPage,
	ShopsPage,
	InitializePage,
	SystemPage,
	AccoutAndPerPage,
	SenderPage,
	RecipientPage,
	MemoPage,
	Page404,
	ExpressCanUpPage,
	SmartExpressPage,
	ExpressTemplate,
	SystemLogPage,
	GoodsLabelPage
} from "@/pages/Index/index";
import TableExample from "@/pages/Example/Table";
import Demo from "@/pages/Example/Demo";
import Pointer from "@/utils/pointTrack/constants";
import { PageError } from "@/pages/Index/Error";

const indexRoutes: RouteType[] = [
	{
		path: "/",
		exact: true,
		// component: HomePage,
		component: lazy(() => import(/* webpackChunkName: "Home" */ '@/pages/Index/Home')),
		point: {
			point: Pointer.首页_首页_总展现,
		},
	},
	{
		path: "/404/:type?", // 菜单跳转的无权限（暂时不动）
		exact: true,
		// component: Page404,
		component: lazy(() => import(/* webpackChunkName: "404Page" */ '@/pages/Index/404')),
	},
	{
		path: "/shops",
		exact: true,
		// component: ShopsPage,
		component: lazy(() => import(/* webpackChunkName: "Shops" */ '@/pages/Index/Shops')),
		point: {
			point: Pointer.店铺_店铺管理_总展现,
		},
	},
	{
		path: "/downloadCenter",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "Shops" */ '@/pages/Index/DownloadCenter')),
		point: {
			point: '',
		},
	},	{
		path: "/taskCenter",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "Shops" */ '@/pages/Index/TaskCenter')),
		point: {
			point: '',
		},
	},
	{
		path: "/settings/system",
		exact: true,
		// component: SystemPage,
		component: lazy(() => import(/* webpackChunkName: "System" */ '@/pages/Index/Settings/System')),
		point: {
			point: Pointer.设置_系统设置_系统设置_展现,
		},
	},
	{
		path: "/settings/perm",
		exact: true,
		// component: AccoutAndPerPage,
		component: lazy(() => import(/* webpackChunkName: "AccountAndPer" */ '@/pages/Index/Settings/AccountAndPer')),
	},
	{
		path: "/settings/sender",
		exact: true,
		// component: SenderPage,
		component: lazy(() => import(/* webpackChunkName: "Sender" */ '@/pages/Index/Settings/Sender')),
		point: {
			point: Pointer.设置_常用发件人_常用发件人_展现,
		},
	},
	{
		path: "/settings/recipient",
		exact: true,
		// component: RecipientPage,
		component: lazy(() => import(/* webpackChunkName: "Recipient" */ '@/pages/Index/Settings/Recipient')),
		point: {
			point: Pointer.设置_常用收件人_常用收件人_展现,
		},
	},
	{
		path: "/settings/memo",
		exact: true,
		// component: MemoPage,
		component: lazy(() => import(/* webpackChunkName: "Memo" */ '@/pages/Index/Settings/Memo')),
		point: {
			point: Pointer.设置_常用备注短语_常用备注短语_展现,
		},
	},
	{
		path: "/settings/LabelManage",
		exact: true,
		// component: MemoPage,
		component: lazy(() => import(/* webpackChunkName: "Memo" */ '@/pages/Index/Settings/LabelManage')),
	},
	{
		path: "/settings/expressCanUp",
		exact: true,
		// component: ExpressCanUpPage,
		component: lazy(() => import(/* webpackChunkName: "ExpressCanUp" */ '@/pages/Index/Settings/ExpressCanUp')),
		// point: {
		// 	point: Pointer.设置_快递可达设置_快递可达区域_展现
		// },
	},
	{
		path: "/settings/smartExpress",
		exact: true,
		// component: SmartExpressPage,
		component: lazy(() => import(/* webpackChunkName: "SmartExpress" */ '@/pages/Index/Settings/SmartExpress'))
		// point: {
		// 	point: Pointer.设置_快递可达设置_快递可达区域_展现
		// },
	},
	{
		path: "/settings/systemLog",
		exact: true,
		// component: SystemLogPage,
		component: lazy(() => import(/* webpackChunkName: "SystemLogPage" */ '@/pages/Index/Settings/SystemLog')),
	},
	{
		path: "/settings/goodsLabel",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "GoodsLabel" */ '@/pages/Index/Settings/GoodsLabel')),
	},
	{
		path: "/settings/expressTemplate",
		exact: true,
		// component: ExpressTemplate,
		component: lazy(() => import(/* webpackChunkName: "ExpressTemplate" */ '@/pages/Index/Settings/ExpressTemplate')),
	},
	{
		path: "/settings/AuthorizeFeeReturn",
		exact: true,
		// component: AuthorizeFeeReturn,
		component: lazy(() => import(/* webpackChunkName: "AuthorizeFeeReturn" */ '@/pages/Index/Settings/AuthorizeFeeReturn')),
	},
	{
		path: "/settings/customer",
		exact: true,
		// component: Customer,
		component: lazy(() => import(/* webpackChunkName: "Customer" */ '@/pages/Index/Settings/Customer')),
	},
	{
		path: "/settings/monitoringDevice",
		exact: false,
		name: '监控设备管理',
		component: lazy(() => import(/* webpackChunkName: "SettingsMonitoringDeviceOrder" */ '@/pages/Index/Settings/MonitoringDevice/Order')),
	}
];

export default indexRoutes;
