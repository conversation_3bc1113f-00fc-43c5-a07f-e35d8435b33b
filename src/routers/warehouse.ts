import { lazy } from 'react';
import { RoutePointType, RouteType } from '@/types/schemas/index';
import WarehousePage from '@/pages/Warehouse';
import Supplier from '@/pages/Warehouse/Supplier';
import PurchaseOrder from '@/pages/Warehouse/PurchaseOrder';
import StockLog from '@/pages/Warehouse/StockLog';
import StockInit from '@/pages/Warehouse/StockInit';
import StockInfo from '@/pages/Warehouse/StockInfo';
import StockWarnRemind from '@/pages/Warehouse/StockWarnRemind';
import SystemArchives from '@/pages/Warehouse/System/Archives';
import EntrepotPage from '@/pages/Warehouse/Entrepot';
import PurchaseWarehouse from '@/pages/Warehouse/PurchaseWarehouse';
import ScanWarehouse from '@/pages/Warehouse/ScanWarehouse';
import ArriveWarehouse from '@/pages/Warehouse/ArriveWarehouse';
import StockSync from "@/pages/Warehouse/StockSync";
import SystemRelations from '@/pages/Warehouse/System/Relation';
import SyncStockLog from '@/pages/Warehouse/SyncStockLog';
import StockCheck from '@/pages/Warehouse/StockCheck';
import Pointer from '@/utils/pointTrack/constants';
import SystemCombinedGoods from '@/pages/Warehouse/System/CombinedGoods';
import PlatformGoods from '@/pages/Warehouse/System/PlatformGoods';
import OutputStock from '@/pages/Warehouse/OutputStock';

const warehouseRoutes: RouteType[] = [
	{
		path: "/warehouse/stockLog",
		exact: true,
		// component: StockLog,
		component: lazy(() => import(/* webpackChunkName: "StockLog" */ "@/pages/Warehouse/StockLog")),
		point: {
			point: Pointer.库存_出入库日志_出入库日志_展现
		}
	},
	{
		path: "/warehouse/stockInit",
		exact: true,
		// component: StockInit,
		component: lazy(() => import(/* webpackChunkName: "StockInit" */ "@/pages/Warehouse/StockInit")),
		point: {
			point: Pointer.库存_库存初始化_库存初始化_展现
		}
	},
	{
		path: "/warehouse/stockInfo",
		exact: true,
		// component: StockInfo,
		component: lazy(() => import(/* webpackChunkName: "StockInfo" */ "@/pages/Warehouse/StockInfo")),
		point: {
			point: Pointer.库存_库存看板_库存看板_展现
		}
	},
	{
		path: "/warehouse/stockWarnRemind",
		exact: true,
		// component: StockWarnRemind,
		component: lazy(() => import(/* webpackChunkName: "StockWarnRemind" */ "@/pages/Warehouse/StockWarnRemind")),
		point: {
			point: Pointer.库存_库存预警阈值_库存预警阈值_展现
		}
	},
	{
		path: "/warehouse/archives/:from?",
		exact: true,
		// component: SystemArchives,
		component: lazy(() => import(/* webpackChunkName: "Archives" */ "@/pages/Warehouse/System/Archives")),
		point: {
			point: Pointer.商品_普通货品档案_普通货品档案_展现
		}
	},
	{
		path: "/warehouse/combinedArchives",
		exact: true,
		// component: SystemArchives,
		component: lazy(() => import(/* webpackChunkName: "Archives" */ "@/pages/Warehouse/System/Archives")),
		point: {
			point: Pointer.商品_组合货品_组合货品页面 // 商品-组合货品档案
		}
	},
	{
		path: "/warehouse/platformGoods",
		exact: true,
		// component: PlatformGoods, // 商品-平台商品管理
		component: lazy(() => import(/* webpackChunkName: "PlatformGoods" */ "@/pages/Warehouse/System/PlatformGoods")),
		point: {
			point: Pointer.商品_平台商品管理_页面展现
		}
	},
	{
		path: "/warehouse/stockCombinedGoods",
		exact: true,
		// component: SystemCombinedGoods,
		component: lazy(() => import(/* webpackChunkName: "CombinedGoods" */ "@/pages/Warehouse/System/CombinedGoods")),
		point: {
			point: '' // 库存-组合货品打包
		}
	},
	{
		path: "/warehouse/entrepot",
		exact: true,
		// component: EntrepotPage,
		component: lazy(() => import(/* webpackChunkName: "Entrepot" */ "@/pages/Warehouse/Entrepot")),
		point: {
			point: Pointer.仓库_仓库信息_仓库信息_展现
		}
	},
	{
		path: "/warehouse/purchaseWarehouse",
		exact: true,
		// component: PurchaseWarehouse,
		component: lazy(() => import(/* webpackChunkName: "PurchaseWarehouse" */ "@/pages/Warehouse/PurchaseWarehouse")),
		point: {
			point: Pointer.库存_采购入库_采购入库_展现
		}
	},
	{
		path: "/warehouse/scanWarehouse",
		exact: true,
		// component: ScanWarehouse,
		component: lazy(() => import(/* webpackChunkName: "ScanWarehouse" */ "@/pages/Warehouse/ScanWarehouse")),
		point: {
			point: Pointer.库存_采购入库_扫描入库_展现
		}
	},
	{
		path: "/warehouse/arriveWarehouse",
		exact: true,
		// component: ArriveWarehouse,
		component: lazy(() => import(/* webpackChunkName: "ArriveWarehouse" */ "@/pages/Warehouse/ArriveWarehouse")),
		point: {
			point: Pointer.库存_到货入库_到货入库_展现
		}
	},
	{
		path: "/warehouse/supplier",
		exact: true,
		// component: Supplier,
		component: lazy(() => import(/* webpackChunkName: "Supplier" */ "@/pages/Warehouse/Supplier")),
		point: {
			point: Pointer.采购_供应商_供应商_展现
		}
	},
	{
		path: "/warehouse/purchaseOrder",
		exact: true,
		// component: PurchaseOrder,
		component: lazy(() => import(/* webpackChunkName: "PurchaseOrder" */ "@/pages/Warehouse/PurchaseOrder")),
		point: {
			point: Pointer.采购_采购单_采购单_展现
		}
	},
	{
		path: "/warehouse/syncStock",
		exact: true,
		// component: StockSync,
		component: lazy(() => import(/* webpackChunkName: "StockSync" */ "@/pages/Warehouse/StockSync")),
		point: {
			point: Pointer.库存_库存同步_库存同步_展现
		}
	},
	{
		path: "/warehouse/relation/:param?",
		exact: true,
		// component: SystemRelations, // 商品-货品与商品关系
		component: lazy(() => import(/* webpackChunkName: "Relation" */ "@/pages/Warehouse/System/Relation")),
	},
	{
		path: "/warehouse/syncStockLog",
		exact: true,
		// component: SyncStockLog,
		component: lazy(() => import(/* webpackChunkName: "SyncStockLog" */ "@/pages/Warehouse/SyncStockLog")),
		point: {
			point: Pointer.库存_库存同步日志_库存同步日志_展现
		}
	},
	{
		path: "/warehouse/stockCheck",
		exact: true,
		// component: StockCheck,
		component: lazy(() => import(/* webpackChunkName: "StockCheck" */ "@/pages/Warehouse/StockCheck")),
		point: {
			point: '' // 库存-按货品盘库
		}
	},
	{
		path: "/warehouse/outputWarehouse",
		exact: true,
		// component: OutputStock,
		component: lazy(() => import(/* webpackChunkName: "OutputStock" */ "@/pages/Warehouse/OutputStock")),
		point: {
			point: '' // 手动出库
		}
	},
	{
		path: "/warehouse/itemMigration",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "ItemMigration" */ "@/pages/Warehouse/ItemMigration")),
		point: {
			point: ''
		}
	},
	{
		path: "/warehouse/migrationLog",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "MigrationLog" */ "@/pages/Warehouse/MigrationLog")),
		point: {
			point: Pointer.商品_商品搬家日志_页面展现
		}
	},
	{
		path: "/warehouse/:param?", // 波次管理
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "WarehouseOrder" */ '@/pages/Warehouse/Order')),
	}
];

export default warehouseRoutes;
