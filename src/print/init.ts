// @ts-nocheck
/* eslint-disable */
import { TEST, LOCAL, GRAY, PRE, STAGING, ONLINE, DOMAIN, P1, getVersionUrl } from "./env";
import { PRINT_CENTER_ENV } from "@/constants";
import {
	SettingSaveUserSettingApi,
} from "@/apis/user";
import axios from "axios";
import { tradeStore } from '@/stores';
import scanPrintStore from "@/stores/trade/scanPrint";
import userStore from '@/stores/user';
import message from "@/components/message";
import { TEMP_MAP, TEMP_NAME } from '@/constants';
import Pointer from '@/utils/pointTrack/constants';
import sendPoint from "@/utils/pointTrack/sendPoint";
import events from "@/utils/events";
import memoFn from '@/libs/memorizeFn';
import PrintCenterAPI from '@/print/index';
import { cancelRequest } from '@/print/src/common/http';

declare global {
	interface Window {
		printAPI: any,
	}
}

export default {
	async initPrintAPI(userInfo: any, kdzsToken: string) {
		console.info("initPrintAPI-moudule.basicIit", new Date());
		const platform = "erp";
		let setting = window.erpData?.advancedSetting?.groupPrintSetJsonString
		let isMerge = setting?.openMergePrint == 2
		// 没有版本，视为高级版
		// if (!userInfo.level) {
		// 	userInfo.level = 5;
		// }
		const params = {
			platform,
			isHander: false,
			appkey: "",
			//TODO 临时处理 后续优化
			userInfo: {
				subUserId: '',
				subUserName: '',
				...userInfo
			},
			userId: userInfo.userId,
			tokenName: "kdzsMallToken",
			kdzsToken: kdzsToken,
			qnkdzsToken: kdzsToken,
		};
		const __printAPI = new window.PrintAPI(params);
		window.__printAPI = Object.assign(__printAPI, window.__printAPI || {});
		window.__printAPI.compHookObj = {
			//变更快递单模板后：新增，删除，改名，调整顺序
			afterChangedKddTemp: (temp, type) => {
				// 添加模板直接会触发afterBranchChangeHook
				if (type == "addTemp" || !temp) {
					return
				}
				let tempList = null
				switch (temp.ExCode) {
					case 'FHD':
						tempList = tradeStore.fhdTempList
						break
					case 'BHD':
						tempList = tradeStore.bhdXbqTempList
						break
					case 'BKD':
						tempList = tradeStore.bhdXbqTempList
						break
					case 'THD':
						tempList = tradeStore.thdXbqTempList
						break
					default:
						tempList = tradeStore.kddTempList
				}
				if (temp.Mode_ListShowId !== undefined && !isMerge) {
					let index = tempList.findIndex((i) => i.Mode_ListShowId == temp.Mode_ListShowId)
					console.log(index, '当前模板索引');
					//TODO styleId
					tempList[index] = {
						...temp,
						styleId: tempList[index]?.styleId
					}
				}
				// TODO 引用类型的根属性直接赋值会导致Proxy缓存不更新
				// tradeStore.setKddTempList(cloneDeep(comp.Print.Data.kddTemplates.ModeListShows))
			},
			afterBranchChangeHook: async (updateExid) => {

				// window.Tatami.pub("printCenter.noTempUpdateTemp", {
				// 	updateExCode,
				// });
				const ModeListShows = tradeStore.kddTempList

				if (!!isMerge) {
					const tempArr = []
					let wdArr = []
					ModeListShows.forEach(o => {
						tempArr.push(...o.userTemplateList)
					})
					// 添加请求中止逻辑
					if (tradeStore.abortController) {
						tradeStore.abortController.abort();
					}
					const controller = new AbortController();
					tradeStore.abortController = controller;
					const wdList = await window.printAPI.batchGetBranchSet({
						kddList: tempArr,
						updateExid
					});
					ModeListShows.forEach((it) => {
						wdArr = []
						it.userTemplateList.forEach(o => {
							if (wdList[o.userTemplateId] && !!wdList[o.userTemplateId]?.branchName) {
								o.wd = wdList[o.userTemplateId]
								wdList[o.userTemplateId].tempType = TEMP_NAME[o.expressType]
								wdArr.push(wdList[o.userTemplateId])
							}
						})
						it.wd = wdArr.sort((a, b) => a.quantity - b.quantity)
					})
					tradeStore.setKddTempList(ModeListShows)
				} else {
					// 剩余网点展示
					const wdList = await window.printAPI.batchGetBranchSet({
						kddList: ModeListShows,
						updateExid
					});
					ModeListShows.forEach((i) => {
						let id = i.Mode_ListShowId
						if (wdList[id]) {

							i.wd = wdList[id]
						}
					})
					tradeStore.setKddTempList([...ModeListShows])
					tradeStore.setExpressTemplateList([...ModeListShows])
				}
			},
			afterLoadedKddTemp: async (temps, userId) => {
				if (!!isMerge) {
					// if (!!localDefaultId  && localDefaultId != tradeStore.selectedTempGroup?.id) {
					// 	let index = temps.findIndex((i => i.id === localDefaultId))
					// 	if (index > -1) {
					// 		tradeStore.setSelectedTemp(temps[index])
					// 	}
					// }
					let localDefaultId = localStorage.getItem('defaultChoosedKddTempGroup' + userId)
					// 中止可能存在的未完成请求
					if (tradeStore.currentCancelKey) {
						// 取消上一次未完成的请求
						cancelRequest(tradeStore.currentCancelKey);
						delete tradeStore.currentCancelKey;
					}
					const tempArr = []
					temps.forEach(o => {
						tempArr.push(...o.userTemplateList)
					})
					const wdList = await window.printAPI.batchGetBranchSet({
						kddList: tempArr
					});
					let wdArr = []
					temps.forEach((it) => {
						wdArr = []

						it.userTemplateList.forEach(o => {
							if (wdList[o.userTemplateId] && !!wdList[o.userTemplateId]?.branchName) {
								o.wd = wdList[o.userTemplateId]
								wdList[o.userTemplateId].tempType = TEMP_NAME[o.expressType]
								wdArr.push(wdList[o.userTemplateId])
							}
						})
						it.wd = wdArr.sort((a, b) => a.quantity - b.quantity)
					})
					if (!!localDefaultId || !!tradeStore.selectedTempGroup.id) {
						let defaultItem = temps.find(o => o.id == (tradeStore.selectedTempGroup.id ? tradeStore.selectedTempGroup.id : localDefaultId))
						defaultItem && tradeStore.setSelectedTempGroup(defaultItem)
					}

					tradeStore.setKddTempList(temps)
				} else {
					let { ModeListShowId, ModeListShows } = temps
					// 默认选中
					let localDefaultId = localStorage.getItem('defaultChoosedKddTemp' + userId)
					if (!!localDefaultId && ModeListShowId && ModeListShowId !== tradeStore.selectedTemp?.Mode_ListShowId) {
						let index = ModeListShows.findIndex((i => i.Mode_ListShowId === ModeListShowId))
						if (index > -1) {
							tradeStore.setSelectedTemp(ModeListShows[index])
						}
					}

					ModeListShows = ModeListShows.map((i) => {
						return {
							...i,
							platform: TEMP_MAP[i.KddType]
						}
					})
					tradeStore.setKddTempList([...ModeListShows])
					tradeStore.setExpressTemplateList([...ModeListShows])
					const cancelKey = `batchGetBranchSet_${Date.now()}`;

					tradeStore.currentCancelKey = cancelKey;
					// 剩余网点展示 这个接口很慢，响应需要二十秒
					window.printAPI.batchGetBranchSet({
						kddList: ModeListShows,
						cancelKey: cancelKey  // 传递取消标识
					}).then((wdList) => {
						if (!isMerge) {
							ModeListShows.forEach((i) => {
							let id = i.Mode_ListShowId
							if (wdList[id]) {

								i.wd = wdList[id]
							}
						})
						tradeStore.setKddTempList([...ModeListShows])
						tradeStore.setExpressTemplateList([...ModeListShows])
						}
					}).finally(() => {
						if (tradeStore.currentCancelKey) {
							delete tradeStore.currentCancelKey;
						}
					})

				}
				tradeStore.setShowAddGroup(true)

			},
			afterLoadedFhdTemp: async (temps) => {
				let { ModeListShowId, ModeListShows } = temps
				// // 默认选中
				// if (ModeListShowId && !tradeStore.selectedTemp?.Mode_ListShowId) {
				let defaultItem = ModeListShows.find((i => i.Mode_ListShowId == ModeListShowId))
				// 	if (index > -1) {
				// 		tradeStore.setSelectedTemp(ModeListShows[index])
				// 	}
				// }

				// ModeListShows = ModeListShows.map((i) => {
				// 	return {
				// 		...i,
				// 		platform:TEMP_MAP[i.KddType]
				// 	}
				// })

				tradeStore.setFhdTempList(ModeListShows)
				tradeStore.setSelectedFhdTemp(defaultItem)
			},
			// 获取小标签模板
			afterLoadedbqXbqTemp: async (temps) => {
				let { ModeListShowId, ModeListShows } = temps
				// 默认选中
				if (ModeListShowId && ModeListShowId !== tradeStore.selectedBxdXbqTemp?.Mode_ListShowId) {
					let currentTemp = ModeListShows.find((i => i.Mode_ListShowId == ModeListShowId))
					if (currentTemp) {
						tradeStore.setSelectedBxdXbqTemp(currentTemp)
					}
				}

				// ModeListShows = ModeListShows.map((i) => {
				// 	return {
				// 		...i,
				// 		platform: TEMP_MAP[i.KddType]
				// 	}
				// })
				tradeStore.setBhdXbqTempList([...ModeListShows])
			},
			// 获取直播标签模板
			afterLoadedzbdXbqTemp: async (temps) => {
				console.log(temps)
				let { ModeListShowId, ModeListShows } = temps
				// // 默认选中
				// if (ModeListShowId && !tradeStore.selectedTemp?.Mode_ListShowId) {
				// let defaultItem = ModeListShows.find((i => i.Mode_ListShowId == ModeListShowId))
				// 	if (index > -1) {
				// 		tradeStore.setSelectedTemp(ModeListShows[index])
				// 	}
				// }

				// ModeListShows = ModeListShows.map((i) => {
				// 	return {
				// 		...i,
				// 		platform:TEMP_MAP[i.KddType]
				// 	}
				// })

				tradeStore.setZbdXbqTempList(ModeListShows)
				// tradeStore.setSelectedZbdXbqTemp(defaultItem)
			},
			// 获取退货单小标签模板
			afterLoadedthdXbqTemp: async (temps) => {
				let { ModeListShowId, ModeListShows } = temps
				// 默认选中
				if (ModeListShowId && ModeListShowId !== tradeStore.selectedthdXbqTemp?.Mode_ListShowId) {
					let currentTemp = ModeListShows.find((i => i.Mode_ListShowId == ModeListShowId))
					if (currentTemp) {
						tradeStore.setSelectedthdXbqTemp(currentTemp)
					}
				}

				// ModeListShows = ModeListShows.map((i) => {
				// 	return {
				// 		...i,
				// 		platform: TEMP_MAP[i.KddType]
				// 	}
				// })
				tradeStore.setThdXbqTempList([...ModeListShows])
			},
			afterLoadeddpdXbqTemp: async (temps) => {
				let { ModeListShowId, ModeListShows } = temps
				// 默认选中
				// if (ModeListShowId && ModeListShowId !== tradeStore.selectedBxdXbqTemp?.Mode_ListShowId) {
				// 	let currentTemp = ModeListShows.find((i => i.Mode_ListShowId == ModeListShowId))
				// 	if (currentTemp) {
				// 		tradeStore.setSelectedBxdXbqTemp(currentTemp)
				// 	}
				// }

				// ModeListShows = ModeListShows.map((i) => {
				// 	return {
				// 		...i,
				// 		platform: TEMP_MAP[i.KddType]
				// 	}
				// })
				console.log('dpList', ModeListShows);

				tradeStore.setDpTempList([...ModeListShows])
			},
			operatingRecord: (pointName) => {
				sendPoint(Pointer[pointName])
			},
			//更新用户设置
			updateUserSettingHook: async (remindShip) => {
				let params = Object.assign({}, userStore.userSetting, { remindShip });
				await SettingSaveUserSettingApi(params);
				userStore.setUserSetting(params);

			},
			// clodop加载完成
			afterLoadClodopFile: () => {
				const printersList = scanPrintStore.printersList;
				if (!printersList?.length) {
					PrintCenterAPI.getPrinterList("lodop")
				}
				tradeStore.setLodopInstallStatus(true)
			},
			afterGetPrintersHook: (res) => {
				const { printers, dPrinter } = res;
				if (printers?.length) {
					const printersList = scanPrintStore.printersList;
					if (!printersList?.length) {
						scanPrintStore.setPrintersList(printers);
					}
				}
				scanPrintStore.setDefaultPrinter(dPrinter);
			},
			loadClodopFileError: () => {
				tradeStore.setLodopInstallStatus(false)
			}
		};
		__printAPI.initPrint()
		PrintCenterAPI.getKddTemplateList();
		events.emit('printCenterInitDone');
		events.on('updateKddTemplates', (obj = {}) => {
			const { key, value, tempId } = obj
			const tempTypeId = isMerge ? 'id' : 'Mode_ListShowId'
			let newKddTempList = tradeStore.getKddTempList().map(o => {
				if (o[tempTypeId] == tempId) o[key] = value
				return o
			})
			tradeStore.setKddTempList([...newKddTempList])
		});
		tradeStore.setPrintCenterInitStatus(true);
		const areaData = await memoFn.getAllAddressList();
		comp.Print.unRegisterMethod('getAddrDropdown');
		comp.Print.registerMethod('getAddrDropdown', function ($dom, options) {
			let $address;
			$address = $dom.find('.J_Send_Area');
			let formatArea = {}
			for (let i = 0; i < areaData.length; i++) {
				let item = areaData[i]
				let itemArr = [item.name, item.parentId, '']
				formatArea[item.id] = itemArr

			}
			let component = new window.component.AddrDropDown('', { addrData: formatArea });
			component.renderUI($address, [
				{ name: 'province', value: options.province },
				{ name: 'city', value: options.city },
				{ name: 'district', value: options.district },
			]);
		});

		comp.Print.Data.controlDownData = tradeStore.controlDonloadData
	},
	loadPrintCenter(userInfo: any, kdzsToken: string) {
		console.log('1312312');

		// 如果打印中心资源已经初始化只需要更新数据
		if (window.PrintAPI) {
			const apiCalledMap = new Map();
			// 当打印中心未加载完时，调用了打印中心的相关api，被中断后，在打印中心加载完成后，需要帮用户自动重新触发的方法
			const autoRecallApiMap = {
				getKddTempList: ({ hash }) => {
					switch (hash) {
						case "#/orderManagement/orderPrint":
							window.__printAPI.compHookObj.afterChangedKddTemp();
							break;
						default:
							break;
					}
				},
			};
			const getHash = () => window.location.hash.split("?")[0];
			// 打印中心未加载完成时的友好处理
			window.printAPI = new Proxy(
				{},
				{
					get: (_, key: string) => {
						const fn = () => {
							const msg = "打印功能正在拼命加载中，请稍后再试...";
							// 当所触发的方法在autoRecallApiMap中被注册时，保存这些方法
							// 多次触发同一个api时，先触发的会被后触发的覆盖
							if (Object.keys(autoRecallApiMap).includes(key))
								apiCalledMap.set(key, {
									hash: getHash(),
								});
							// 通过主动抛错中断后续流程
							// throw new Error(msg);
							message.warning(msg);
						};
						return window.__printAPI ? window.__printAPI[key] : fn;
					},
					set: (target, key, value) => {
						window.__printAPI[key] = value;
						return true;
					},
				}
			);
			this.initPrintAPI(userInfo, kdzsToken);
			return
		}

		// // 加载js资源
		// const loadJs = (jsSrc) => {
		// 	const script = document.createElement("script");
		// 	script.src = jsSrc;
		// 	// 必须指定为false，通过此方式加载的js文件默认为async模式，即谁先加载完成谁先被处理，
		// 	// 但是html文档上不会显示出async，而打印中心js执行必须先执行resource.js再执行print.js
		// 	script.async = false;
		// 	const bodyEle = document.querySelector("body");
		// 	bodyEle.appendChild(script);
		// 	return script;
		// };
		// // 加载css资源
		// const loadCss = (cssHref) => {
		// 	const link = document.createElement("link");
		// 	link.type = "text/css";
		// 	link.rel = "stylesheet";
		// 	link.href = cssHref;
		// 	const bodyEle = document.querySelector("body");
		// 	bodyEle.appendChild(link);
		// 	return link;
		// };

		// const getVersion = () => {
		// 	return new Promise((resolve, reject) => {
		// 		axios
		// 			.get(getVersionUrl)
		// 			.then((res) => resolve(res.data))
		// 			.catch(() => {
		// 				// 发现部分用户网络状态下会出现中台接口超时的情况
		// 				// 当中台接口获取版本失败后，尝试走平台接口
		// 				axios
		// 					.get(
		// 						`/print/center/config/jsversion?platform=${PRINT_CENTER_ENV}`
		// 					)
		// 					.then((res) => resolve(res));
		// 			});
		// 	});
		// };

		// // 用于保存被调用过的api方法名，以api方法名为key，需要保存的对象信息为value
		// // 需要保存的对象指得是触发此api的路由之类的信息
		// // 只有在autoRecallApiMap对象中作为key的api才会被保存
		// const apiCalledMap = new Map();
		// // 当打印中心未加载完时，调用了打印中心的相关api，被中断后，在打印中心加载完成后，需要帮用户自动重新触发的方法
		// const autoRecallApiMap = {
		// 	getKddTempList: ({ hash }) => {
		// 		switch (hash) {
		// 			case "#/orderManagement/orderPrint":
		// 				window.__printAPI.compHookObj.afterChangedKddTemp();
		// 				break;
		// 			default:
		// 				break;
		// 		}
		// 	},
		// };
		// // 获取版本号并加载资源
		// const loadPrintCenterResource = async () => {
		// 	let version = await getVersion();
		// 	let baseUrl;
		// 	// debugger
		// 	switch (PRINT_CENTER_ENV) {
		// 		case STAGING:
		// 		case TEST:
		// 			version = "testERP";
		// 			baseUrl = `${DOMAIN}/${version}`;
		// 			baseUrl = `https://printcentertest.kuaidizs.cn/testERP`;
		// 			break;
		// 		case LOCAL:
		// 			baseUrl = DOMAIN;
		// 			break;
		// 		default:
		// 			baseUrl = `${DOMAIN}/${version}`;
		// 	}

		// 	// 强制更改打印中心版本，方便用于线上问题查找，平时测试
		// 	const qsVersion = new URL(location.href).searchParams.get(
		// 		"printCenterVersion"
		// 	);

		// 	if (qsVersion) {
		// 		baseUrl = `${DOMAIN}/${qsVersion}`;
		// 	}

		// 	loadCss(`${baseUrl}/print.css`);
		// 	const p0 = new Promise((resolve, reject) => {
		// 		loadJs(
		// 			`https://static.kuaidizs.cn/resources/js/jquery.min.js`
		// 		).onload = resolve;
		// 	});
		// 	const p1 = new Promise((resolve, reject) => {
		// 		loadJs(`${baseUrl}/resources.js`).onload = resolve;
		// 	});
		// 	const p2 = new Promise((resolve, reject) => {
		// 		loadJs(`${baseUrl}/print.js`).onload = resolve;
		// 	});
		// 	const p3 = new Promise((resolve, reject) => {
		// 		loadJs(
		// 			"https://printcenter.kuaidizs.cn/resource/grid.min.js"
		// 		).onload = resolve;
		// 	});
		// 	// 在打印中心资源加载完成后，初始化打印中心
		// 	Promise.all([p0, p1, p2]).then(() => {
		// 		this.initPrintAPI(userInfo, kdzsToken);
		// 		// 当加载完成后，重新调用因为被中断而未被调用的缓存方法
		// 		apiCalledMap.forEach((value, key) =>
		// 			autoRecallApiMap[key](value)
		// 		);
		// 		// message.destroy()
		// 		// 执行打印中心的加载完成后的钩子
		// 		window.afterPrintCenterLoadedCbQueue.forEach((fn) => fn());
		// 	});
		// };

		// loadPrintCenterResource();

		// // 打印中心的加载完成后的钩子队列，可以把一些待处理的方法加载到这个队列里，供业务方使用
		// window.afterPrintCenterLoadedCbQueue = [];

		// // 打印中心未加载完成时的友好处理
		// window.printAPI = new Proxy(
		// 	{},
		// 	{
		// 		get: (_, key: string) => {
		// 			const fn = () => {
		// 				const msg = "打印功能正在拼命加载中，请稍后再试...";
		// 				// 当所触发的方法在autoRecallApiMap中被注册时，保存这些方法
		// 				// 多次触发同一个api时，先触发的会被后触发的覆盖
		// 				if (Object.keys(autoRecallApiMap).includes(key))
		// 					apiCalledMap.set(key, {
		// 						hash: getHash(),
		// 					});
		// 				// 通过主动抛错中断后续流程
		// 				// throw new Error(msg);
		// 				message.warning(msg);
		// 			};
		// 			return window.__printAPI ? window.__printAPI[key] : fn;
		// 		},
		// 		set: (target, key, value) => {
		// 			window.__printAPI[key] = value;
		// 			return true;
		// 		},
		// 	}
		// );
		// // 对于间接引用打印中心所导致的流程错误，需要主动调用此方法去检测
		// // 此方法不会抛出错误，建议配合catch中使用，已确保整个流程不会出错
		// window.checkPrinterAPI = () => {
		// 	// debugger
		// 	if (!window.__printAPI) {
		// 		const msg = "打印功能正在拼命加载中，请稍后再试...";
		// 		console.log(msg);
		// 		// window.Tatami.showFail(msg, 3);
		// 	}
		// };
	},
};
