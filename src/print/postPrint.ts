import dayjs from "dayjs";
import tradeSetStore from "@/stores/trade/tradeSet";
import { tradeStore } from '@/stores';
import { formatTradeData } from "@/utils/print/formatData";
import { sendDelivery } from "@/pages/Trade/components/BottomCom/utils";
import event from "@/libs/event";
import { pageLoading } from "@/components/PageLoading";
import { TradeOptEnum } from "@/utils/enum/trade";
import voice打印失败 from '@/assets/mp3/打印失败.mp3';
import voice打印成功 from '@/assets/mp3/打印成功.mp3';
import { playAudio } from "@/pages/Trade/ScanPrint/utils";
import { CLEAR_PAGE_DATA } from "@/pages/Trade/PostPrint/constants";


// 获取发货内容打印样式
async function _getPrintStyle() {
	try {
		let printContentSet = await tradeSetStore.getPrintContentSet();
		return printContentSet;
	} catch (error) {
		window.errorCollection?.customMessageUpload({
			type: `后置打印: 【 获取发货内容打印样式异常 】`,
			data: {
				error
			}
		});
	}
}
// 打印快单
const PrintExpressBill = async(params:{[k:string]:any}) => {
	const { setting, postPrintStore: {
		settingConfig,
		setPostPrintRes,
		resetStore,
		packWsHandlerSendFail
	} } = tradeStore;
	const autoSend = settingConfig?.isAutoSend;
	const { orderList, templateInfo, tagTempId, printer, senderInfo, isMergePrint, isForcePrint, historySids, firstSendMode, logReprint, isMustUseOldCode, tagPrinter, } = params;
	const printStyle = await _getPrintStyle();
	let formatTradeResult:any = formatTradeData({
		packages: orderList,
		isGetFhdInfo: false,
		isScan: true,
		isForcePrint,
		historySids,
		printFrom: 'scanPrintKdd', // 传个参数告诉来源
	});
	console.log(formatTradeResult);
	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `后置打印: 【 ${dataType} 】`,
			data: {
				settingConfig,
				isMergePrint,
				...data
			}
		});
	};
	if (formatTradeResult.isError) {
		console.log('formatTradeResult Error');
		packWsHandlerSendFail('formatTradeData 获取打印数据异常');
		playAudio(voice打印失败);
		customLogPost('formatTradeData 获取打印数据异常', { formatTradeResult });
		return;
	}
	const scanPrintData = {
		printType: 'kdd',
		orderList: formatTradeResult.orderList,
		templateInfo,
		tagTempId,
		printStyle,
		choosedPrinter: printer,
		choosedTagPrinter: tagPrinter,
		fjrInfo: senderInfo,
		isPrintTag: !!settingConfig.isPrintItemTag,
		printOp: 6,
		isMerge: isMergePrint,
		canUseNewWaybillCodeSet: false, // 能使用新单号打印
		canChooseWaybillCodeNumSet: false, // 可以选择多单号
		displayPrintNumSet: setting.displayPrintNum == 2, // 显示打印机打印份数
		sendAfterPrint: true,
		isAutoAddPrintNum: setting.addYdNoMemo == 2,
		isNotAddPrintNumOnlyOne: setting.addYdNoOverOne == 2,
		scanType: 'postScan',
		isForcePrint,
		logReprint,
		isMustUseOldCode,
		// templateInfoDetail,
		// defaultAddress,
	};
	// if (true) {
	// 	return;
	// }
	window.printAPI.scanPrintKdd(scanPrintData, {
		afterCheckComponentHook: ({ isSuccess }) => {
			if (!isSuccess) {
				setPostPrintRes({
					errorText: '打印失败',
					showRes: true,
				});
				pageLoading.destroy();
			} else {
				customLogPost('控件连接异常', { });
			}
		},
		afterSavePrintMarkHook: ({ markTradeList }) => {

		},
		afterGetWaybillCodeHook: async(waybillCoedList, temp, isNewEleNo) => {
			customLogPost('获取单号后，返回单号数据', { waybillCoedList });
		},
		// 获取运单号报错的订单
		afterGetElecErrorHook: errorOrderList => {
			setPostPrintRes({
				showRes: true,
				errorText: '订单申请单号失败'
			});
			pageLoading.destroy();
			packWsHandlerSendFail('订单申请单号失败');
			customLogPost('订单申请单号失败', { errorOrderList });
		},
		// results 打印成功或失败 true 成功 false 失败
		afterSavePrintLogHook: async(orderList: any, results) => {
			// 打印成功以后，把打印中心传递过来的allYdNos赋值到orderList的sids,因为发货需要
			params.orderList.forEach(pack => {
				pack.sids = orderList?.[0]?.allYdNos;
			});
			if (!results) {
				playAudio(voice打印失败);
				packWsHandlerSendFail('afterSavePrintLogHook 打印失败');
				customLogPost('afterSavePrintLogHook 打印失败', { orderList });
			}
			const isFirstSend = orderList.every(pack => pack.ordersArr.every(order => order.firstSend));
			const hasWaitSend = orderList.some(pack => pack.ordersArr.some(order => order.status == 'WAIT_SELLER_SEND_GOODS'));
			// 已发货的不自动发货
			console.log('orderListorderList', orderList)
			if ((!autoSend && results) || (autoSend && isFirstSend) ||(autoSend && !hasWaitSend)) {
				setPostPrintRes({
					showRes: true,
					successText: '打印成功'
				});
				playAudio(voice打印成功);
				resetStore();
				pageLoading.destroy();
				event.emit(CLEAR_PAGE_DATA);
				customLogPost(`afterSavePrintLogHook 打印成功，不自动发货`, { orderList, results, isFirstSend, autoSend });
			}

			if (autoSend && hasWaitSend && !isFirstSend) {
				sendDelivery({
					isSendAbnormal: true,
					type: 'postPrintSend',
					scanPrintList: params.orderList,
					noTemplate: true,
					optionType: TradeOptEnum.后置打印自动发货,
				});
				customLogPost('保存打印标记后，自动发货', { orderList });
			}
		},
	});
};


export { PrintExpressBill };
