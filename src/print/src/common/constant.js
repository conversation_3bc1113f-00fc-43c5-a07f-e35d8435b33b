/**
 * 业务相关常量声明：
 * 打印类型
 *
 */


/** 打印操作 printOp
 * 0：单打
 * 1：批打
 * 2：预发货
 * 3：自动预发货
 * 4：手工订单
 * 5: 厂家代打
 * 7: 菜鸟裹裹
 * 9999:wps插件
 */
const SINGLE_PRINTOP = 0;
const BATCH_PRINTOP = 1;
const PRESHIP_PRINTOP = 2;
const AUTO_PRESHIP_PRINTOP = 3;
const MANUAL_ORDER_PRINTOP = 4;
const CJDD_PRINTOP = 5;
const WPS_PRINTOP = 9999;



/** 操作类型 optionType
 * 0:未设置
 * 1：批打
 * 2：单打
 * 4：扫描打印
 */
 const UNSET_OPTIONTYPE = 0;
 const BATCH_OPTIONTYPE = 1;
 const SINGLE_OPTIONTYPE = 2;
 const SCAN_OPTIONTYPE = 4;

/**
 * 云栈授权方式
 * 0: 店铺代码授权
 * 1: 账号授权（比如淘外拼外）
 * 2: 授权token
 * 3: erp添加店铺授权
 */
const YUNZHAN_AUTH_TYPE = {
    SHOP_CODE: 0,        // 店铺代码授权
    ACCOUNT: 1,         // 账号授权（比如淘外拼外）
    AUTH_TOKEN: 2,      // 授权token
    ERP_SHOP: 3,        // erp添加店铺授权
    ERP_KDD_SHOP: 4,        // erp添加面单店铺授权
}

/**
 * kddtype对应平台
 */
const KDD_TYPE_NAME = {
    2:'网点面单',
    3:'菜鸟',
    5:'京东',
    7:'拼多多',
    8:'抖音',
    9:'快手',
    13:'小红书',
    14:'视频号',
    15:'有赞',
    16:'小红书（新版）',
}
// 回流订单对应平台

const ORDER_REFLUX_TYPE= {
    // ⅰ. 淘系回流：hl-tx
    // ⅱ. 拼多多回流订单：hl-pdd
    // ⅲ. 抖音回流订单：hl-fxg
    // ⅳ. 京东回流订单：hl-jd
    // ⅴ. 快手回流订单：hl-ksxd
    // ⅵ. 小红书回流订单：hl-xhs
    // ⅶ. 微信回流订单：hl-sph
    // ⅷ. 其他回流订单：hl-other
    tx:3,
    jd:5,
    pdd:7,
    fxg:8,
    ksxd:9,
    xhs:13,
    sph:14,
}
const platByKddTypeFnc = function({templateInfo,setting,groupInfo}){
	let xhsType =13
	if(groupInfo){
		let xhsTemp = groupInfo?.userTemplateList.find(o=> [13,16].includes(Number(o.expressType )))
		if(xhsTemp) xhsType = xhsTemp?.expressType
	}
	let handBindControlType = setting.orderMatchSetting?.find(o => o.platform === 'hand')?.bindControlType
	if(handBindControlType == 13) handBindControlType = xhsType
    return {
        tb: 3,
        tm: 3,
        jd: 5,
        pdd: 7,
        fxg: 8,
        ali: 3,
        hand: templateInfo?.KddType || handBindControlType,
		ktt: templateInfo?.KddType || setting.orderMatchSetting?.find(o => o.platform === 'ktt')?.bindControlType,
        other: templateInfo?.KddType || handBindControlType,
        sph: 14,// templateInfo?.KddType || setting.orderMatchSetting?.find(o=>o.platform ==='sph')?.bindControlType,
        xhs: xhsType,
        c2m: 3,
        ksxd: 9,
        yz: 15,
        dw: templateInfo?.KddType || setting.orderMatchSetting?.find(o=>o.platform ==='dw')?.bindControlType,
      }
}
export {
    platByKddTypeFnc,
    KDD_TYPE_NAME,
    SINGLE_PRINTOP,
    BATCH_PRINTOP,
    PRESHIP_PRINTOP,
    AUTO_PRESHIP_PRINTOP,
    MANUAL_ORDER_PRINTOP,
    CJDD_PRINTOP,
    WPS_PRINTOP,
    UNSET_OPTIONTYPE,
    BATCH_OPTIONTYPE,
    SINGLE_OPTIONTYPE,
    SCAN_OPTIONTYPE,
    YUNZHAN_AUTH_TYPE,
    ORDER_REFLUX_TYPE
}
