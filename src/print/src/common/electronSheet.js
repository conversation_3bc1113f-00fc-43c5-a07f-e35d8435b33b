/**
 * 电子面单类型 常量申明文件
 */

/** 电子面单：kddType
 * 普通面单(kddType == 1)，
 * 网点面单(kddType == 2)，
 * 菜鸟面单(kddType == 3)
 * 拿货小标签 kdddType == 4
 * 京东面单 kddType ==5
 * 拼多多面单 kddType == 7
 * 抖音面单 kddType == 8
 * 快手面单 kddType == 9
 * (美团)团好货面单 kddType == 10
 */

const KDDTYPE_ENUM = {
  COMMON_SHEET: 1,
  NET_SHEET: 2,
  CN_SHEET: 3,
  NHTIP_SHEET: 4,
  JD_SHEET: 5,
  PDD_SHEET: 7,
  DY_SHEET: 8,
  KS_SHEET: 9,
  MT_SHEET: 10,
  VIP_SHEET: 11,
  HYK_SHEET: 12,
  XHS_SHEET: 13,
  SPH_SHEET: 14,
  YZ_SHEET: 15,
	NEWXHS_SHEET: 16,
};

export {
  KDDTYPE_ENUM,
};
