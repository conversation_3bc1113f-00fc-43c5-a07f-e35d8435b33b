import axios from 'axios';
import API from './api';
// 添加CancelToken
const CancelToken = axios.CancelToken;
// 创建tokens Map存储取消源
const tokens = new Map();
/**
 *
 * 将数据格式转化成一个以&拼接的URL格式
 * @param {Object} data		需要插入的查询内容，如果是多维将压缩成一维
 * @param {Boolean} notEncode 是否会需要编码，默认编码
 */
function dataToQuerystr(data, notEncode) {
    let i, it, query = data || {}, str = [];

    const funcFilter = notEncode
        ? function(it) {
        // 第二层必须被编码
            return typeof it === 'object' ? encodeURIComponent(JSON.stringify(it)) : it;
        }
        : function(it) {
            return encodeURIComponent(typeof it === 'object' ? (JSON.stringify(it)) : it);
        };

    for (i in query) {
        it = query[i];
        str.push(i + '=' + funcFilter(it));
    }
    return str.join('&');
}
const instance = axios.create({
	timeout: 300000,
});
instance.defaults.withCredentials = false;
// 测试环境用
// if(location.origin.includes('erp')){
    instance.defaults.baseURL = location.origin;
// }else{
//     instance.defaults.baseURL = 'http://erptest.kuaidizs.cn/';
// }
// 当有这个值时，将其作为接口请求的主域名
if(window.$$requestUrl) {
    instance.defaults.baseURL = window.$$requestUrl;
}


// post请求头
instance.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';


//axios拦截器
instance.interceptors.request.use(config => {
    config.url = API[config.url] || config.url;
    console.log(config.url,window.printSourceInfo);
    config.headers.qnquerystring = comp.Print.getKdzsToken();

	// 添加取消token逻辑
	const cancelKey = config.cancelKey;
	if (cancelKey) {
		// 取消已存在的相同key请求
		if (tokens.has(cancelKey)) {
			tokens.get(cancelKey).cancel('请求已取消');
			tokens.delete(cancelKey);
		}
		// 创建新的取消源
		const source = CancelToken.source();
		tokens.set(cancelKey, source);
		config.cancelToken = source.token;
	}

    if(comp.Print.Data.platform === 'erp' && config['Content-Type']){
        config.headers['Content-Type'] = config['Content-Type']

    } else {
        if (config.method === 'post') config.data = dataToQuerystr(config.data);
    }
    if(comp.Print.Data.platform === 'erp' && (config['x-jdcloud-nonce'] || config['x-jdcloud-date'])){
        // 京东店子面单调用外部接口
        config.headers['x-jdcloud-date'] = config['x-jdcloud-date']
        config.headers['x-jdcloud-nonce'] = config['x-jdcloud-nonce']
        config.headers['Authorization'] = config['Authorization']
        delete config.headers.qnquerystring
    }
    if(window.printSourceInfo && config.url.includes('/print/center')){
        config.headers['scene'] = window.printSourceInfo.scene
        config.headers['processType'] = window.printSourceInfo.processType
        config.headers['processBatch'] = window.printSourceInfo.processBatch
    }

    // 拼多多设备指纹
    // 刚初始化 platform为空 需要额外再判断
    let isPddHost = false;
    try {
        isPddHost = location.host.split('.')[0].includes('pdd');
    } catch (error) {
        console.log(error);
    }
    if(comp.Print.Data.platform === 'pdd' || isPddHost){
        config.headers['X-PDD-Pati'] = window.Tatami.request._headers['X-PDD-Pati'];
        config.headers['X-PDD-PageCode'] =
        window.Tatami.request._headers['X-PDD-PageCode'];
    }

    return config;
});

function getApiStatus(resultData, requestAddr){
    const result = resultData.result;
    const message = resultData.message;
    let apiStatus = 'commonError';
    // 100 表示我们的成功
    if (/^1(\d{2})?$/.test(result)) {
        apiStatus = 'success';
    } else if ( result == 700 ){
        const rcp = Tatami.pages;
        //session失效
        if(rcp && rcp.getHashKey('root') == 'login'){
            return;
        }
        Tatami.pub('REQUEST.SESSION.INVALID');
    } else if ( result == 701 ){		//拼多多当前店铺授权过期
        // 如果是打印中心接口返回701那么就不拦截了
        if(requestAddr.startsWith('/print/center')) {
            apiStatus = 'otherError';
        } else {
            Tatami.pub('SHOP.SESSION.INVALID');
        }
    } else if( result == 801 ){		//没有权限提示
        Tatami.pub('REQUEST.PERMISSION.INVALID');
    } else if( result == 810 ){
        //系统繁忙
        Tatami.pub('REQUEST.HEAVY.LOAD');
    } else if( result == 900){
        Tatami.pub('REQUEST.SERVER.Fail', message);//蘑菇街服务失效，重新购买服务
    }else {
        apiStatus = 'otherError';
    }
    return apiStatus;
}

// 响应拦截器
instance.interceptors.response.use(
    response => {
        const _d = response.data;
        const apiStatus = getApiStatus(_d); //apiStatus: success|otherError|commonError , commonError:由平台处理
        _d.apiStatus = apiStatus;
        if (response.status === 200 && ( apiStatus === 'success' || apiStatus === 'otherError')) {
            return Promise.resolve(_d);
        } else {
            return Promise.reject(_d);
        }
    },
    error => {
        console.log('errorerrorerror',error)
        if(error.toString().includes('timeout')) {
            Tatami.showFail('接口超时');
            return Promise.reject(error);
        } else if (error.response.status) {
            switch (error.response.status) {
                // 404请求不存在
                case 404:
                    Tatami.showFail(`${error.config.url}接口不存在`);
                    break;
                default:
                    Tatami.showFail(error.response.data.message);
            }
            return Promise.reject(error.response.data);
        }
    },
);

const _request = function(url,param,method){
    const {
        isHideLoading,
        isHideError,
        params,
        headers
    } = param;
    return new Promise((resolve, reject) => {
        !isHideLoading && Tatami.showLoading({
            key:'print'
        });
        instance[method]( url, method === 'get' ? {params:params} : params, headers )
        .then(response => {
            !isHideLoading && Tatami.clearShow('print');
            resolve(response);
        }).catch( error => {
            !isHideLoading && Tatami.clearShow('print');
            const apiStatus = (error || {}).apiStatus;
            if(!isHideError && apiStatus === 'otherError'){
                Tatami.showFail( ( error || {}).message || '接口请求异常');
            }
            reject(error);
        });
    });
};

const get = function(url,param){
    return _request(url,param,'get');
};

const post = function(url,param){
    return _request(url,param,'post');
};


// 添加取消请求方法
function cancelRequest(cancelKey) {
	if (tokens.has(cancelKey)) {
		tokens.get(cancelKey).cancel('请求已取消');
		tokens.delete(cancelKey);
	}
}

export { instance, post, get, cancelRequest };
