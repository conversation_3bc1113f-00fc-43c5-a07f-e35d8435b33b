import * as printItem from '../printItem';

class ConCatPrintBhdXbq {
  constructor(hook = {}) {
    const {
      afterCheckComponentHook,
      afterChooseTempHook,
      afterChooseFjrHook,
      afterSavePrintMarkHook,
      afterSavePrintLogHook,
    } = hook || {};

    afterCheckComponentHook && (this.afterCheckComponentHook = afterCheckComponentHook);
    afterChooseTempHook && (this.afterChooseTempHook = afterChooseTempHook);
    afterChooseFjrHook && (this.afterChooseFjrHook = afterChooseFjrHook);
    afterSavePrintMarkHook && (this.afterSavePrintMarkHook = afterSavePrintMarkHook);
    afterSavePrintLogHook && (this.afterSavePrintLogHook = afterSavePrintLogHook);
  }

  // 具体流程实现
  async print(data) {
    console.log('打印备货单小标签 传给打印中心的数据');
    console.log({ data });
    let {
      choosedPrinter,
		choosedTagPrinter,
      orderList,
      xbqTemplateDetailInfo,
		isShowProgress = true,
		onlyTag,
		tagTempId,//交替打印吊牌id,仅打印吊牌才会存在

    } = data;
    let isConcatXbq = true
    this.params = data;
	  if (!comp.Print.Data.printTagSet) {
		  let systemSetting = window?.erpData?.systemSetting || {}
		  let printTagSet = JSON.parse(systemSetting?.printTagSet || '{}');
		  comp.Print.Data.printTagSet = printTagSet

	  }
    let xbqPrintSet = comp.Print.Data.printTagSet
    let printNum = xbqPrintSet.printTagNumSwitch == 1 ? (xbqPrintSet.printTagNum || 1) : 1
	  comp.Print.setPrintCopies(printNum);
    let templateInfo = xbqPrintSet.tagTemplate
	  if (!choosedPrinter) {
		  choosedPrinter = choosedTagPrinter
	  }
	  // 如果是仅打印吊牌，需要增加一下操作,使用传入的模板并且覆盖吊牌设置的模板
	  if (onlyTag) {
		  templateInfo = comp.Print.Data.bqTemplateListdpd.ModeListShows.find((item) => {
			  return tagTempId === item.Mode_ListShowId
		  })
		  // // lodop控件校验
		  const installResult = printItem.checkLodopProgress();
		  this.afterCheckComponentHook(installResult);
		  if (!installResult.isSuccess) {
			  return;
		  }
	  }

    let printType = templateInfo.Modeid

    // // lodop控件校验
    // const installResult = printItem.checkLodopProgress();
    // this.afterCheckComponentHook(installResult);
    // if (!installResult.isSuccess) {
    //     return;
    // }

    // 获取模板详情
    let templateDetailInfo = xbqTemplateDetailInfo
    if(!templateDetailInfo){
      templateDetailInfo =  await printItem.getTempDetailInfo({
        printType,
        templateId: templateInfo.Mode_ListShowId,
        isHideloading:true
      });
	} else {
		templateInfo = templateDetailInfo.ModeListShow;
	}


    // const templateInfo = templateDetailInfo.ModeListShow;



    //获取打印序号
    // orderList = await printItem.dealWithPrintNum({
    //   orderList: orderList,
    //   printType: printType,
    // });

    // let startSerialNo = 0;
    // startSerialNo = await printItem._getSerialNo({ total, templateDetailInfo });
    // 组装打印数据
    const printDatas = printItem.getFormatConcatDpdPrintData({
      orderList,
      printType: templateInfo.Modeid,
      xbqPrintSet
    });
    //打印进度弹窗
    let progress = null


    if(isShowProgress){
      if(window.progress) {
        progress = window.progress
      } else {
        progress = new printItem.getPrintProgress({
          showType: 'mark',
          isConcatXbq
        });
        window.progress = progress
      }
    }
    const extraType = 'xbq';
    // // 拼多多打印小标签标记



    isShowProgress && progress.showType('progress');
    if (isShowProgress && isConcatXbq) {
      progress.init({
        type: 'xbq',
        totalCount: printDatas.length
      })
    }
	  // 店铺标识数据处理 业务方做完了
    // printDatas = await printIt em.useSelfShopSign(printDatas,userInfo)
    //打印
    this._doPrint({
      orderList,
      printDatas,
      progress,
      printType,
      choosedPrinter,
      templateInfo,
      templateDetailInfo,
      extraType,
      printNum,
      isShowProgress
    });
  }

  _doPrint({
    orderList,
    printDatas,
    progress,
    printType,
    choosedPrinter,
    templateInfo,
    templateDetailInfo,
    printNum,
    isShowProgress
  }) {
    const self = this;
    let printParams = {
      printDatas: printDatas,
      tempId: templateInfo.Mode_ListShowId,
      ptype: printType,
      printBoxIsShow: 0,
      selectedPrinter: choosedPrinter,
      templateSet: templateDetailInfo,
      hasView:false,
      printNum
    };
    const p = new comp.Print();
    //打印回调methods获取
    const methodsCb = {
      printSynFunc: function (curCount) {
        isShowProgress &&  progress.updateProgress({ curCount,type:'xbq' });
      },
      printOkFunc: async function () {
        //底单日志存储
        isShowProgress &&  progress.showType('log');
        isShowProgress &&  progress.remove('xbq');
        self.afterSavePrintLogHook([...printDatas]);
      },
    };

    p.setDefaultPrinter(printType, choosedPrinter, templateInfo.Mode_ListShowId, function () {
      p.printTemplate(printParams, methodsCb);
    });

  }

  // --------------钩子函数的初始化--------------------
  // 钩子函数
  /**
   * 检测控件安装结果后执行的钩子函数
   * type:'cainiao'|'lodop'
   * result:'update|noInstall'
   */
  afterCheckComponentHook({ type, result }) {
    console.log('afterCheckComponentHook:' + type + result);
  }

  // 选择模板后执行的钩子函数
  afterChooseTempHook(tempId) {
    console.log(tempId);
  }

  // 选择发件人后执行的钩子函数
  afterChooseFjrHook(modifyDefaultFjr) {
    // console.log('afterChooseFjrHook');
    console.log(modifyDefaultFjr);
  }

  // 打印标记完成后执行的钩子函数
  afterSavePrintMarkHook() {
  }

  //保存打印日志后的钩子函数
  afterSavePrintLogHook() {
    console.log('afterSavePrintLogHook', arguments);
  }
  // --------------钩子函数的初始化--------------------

}

export default ConCatPrintBhdXbq;
