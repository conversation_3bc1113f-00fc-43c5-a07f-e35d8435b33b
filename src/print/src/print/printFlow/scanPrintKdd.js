import * as printItem from '../printItem';
import model from './../../common/model.ts';
import { previewConfig, previewConfig_plat } from '../config';
import { printWithEncryptPackages, printFhdEncrypt } from './crypto';
import { platformConfig } from './platformConfig';
import { get, post } from './../../common/http';
import { printWithEncryptOrder } from './printEncrypt'
import concatPrintDpd from './concatPrintDpd';
import {ORDER_REFLUX_TYPE,platByKddTypeFnc} from '../../common/constant'
import urlHelper from '../../common/urlhelp.js';

class ScanPrintKdd {

    constructor(hook) {
        const {
            afterCheckComponentHook,
            afterChooseTempHook,
            afterChooseFjrHook,
            afterGetWaybillCodeHook,
            afterSavePrintMarkHook,
            afterSavePrintLogHook,
            afterBranch<PERSON>hange<PERSON>ook,
            httpWhiteListHook,
            afterContinuePrintHook,
            notifyPrintHook,
            afterGetElecErrorHook,
            afterAutoAddPrintNum,
            printInterruptNotify
        } = hook || {};
        afterCheckComponentHook && (this.afterCheckComponentHook = afterCheckComponentHook);
        afterChooseTempHook && (this.afterChooseTempHook = afterChooseTempHook);
        afterChooseFjrHook && (this.afterChooseFjrHook = afterChooseFjrHook);
        afterGetWaybillCodeHook && (this.afterGetWaybillCodeHook = afterGetWaybillCodeHook);
        afterSavePrintMarkHook && (this.afterSavePrintMarkHook = afterSavePrintMarkHook);
        afterSavePrintLogHook && (this.afterSavePrintLogHook = afterSavePrintLogHook);
        afterBranchChangeHook && (this.afterBranchChangeHook = afterBranchChangeHook);
        httpWhiteListHook && (this.httpWhiteListHook = httpWhiteListHook);//把用户加入 http 白名单的钩子（加入白名单后，用户下次进入系统就直接是 http 环境了）
        afterContinuePrintHook && (this.afterContinuePrintHook = afterContinuePrintHook);
        notifyPrintHook && (this.notifyPrintHook = notifyPrintHook);
        afterGetElecErrorHook && (this.afterGetElecErrorHook = afterGetElecErrorHook);
        afterAutoAddPrintNum && (this.afterAutoAddPrintNum = afterAutoAddPrintNum);
        printInterruptNotify && (this.printInterruptNotify = printInterruptNotify)
        this.timer = window.timer
    }



    /**
     * 开始打印
     * @param {Object} data
     * 模板信息，可以不传
     * 订单数据
     * 用户信息（考虑初始化的时候传，这时候就可以不传了）
     * 打印
     */
    async print(data) {
        console.info('批打快递单 传给打印中心的数据', data);
        this.printTraceId = printItem.getUUID(16,16)
        const userInfo = comp.Print.Data.userInfo;
        const printType = 'kdd';
        const isScanPrint = true; // 是否是小标签扫描
        let {
            templateInfo: orangeTemp,
            orderList,
            printOp,
            choosedPrinter,
            isMerge,
            // ,shopNewPrintNum
            fjrInfoMap,
            fhdFjrInfoMap,
            printStyle,
            fjrInfo,
            defaultAddress,
            templateInfoDetail,
            platformId, // 业务方传来的平台id
            choosedTagPrinter,//吊牌打印机
            isPrintTag,
            packagingDescription, // 包装方式
            goodsDescription, // 物品描述
            waybillCodeCount = 1, // 单号数量
            printNums, // 打印份数
            scanType,//扫描打印来源
            isMustUseOldCode,//是否强制原单号
            logRePrint =false,
			printWebSource,
			tagTempId,//交替打印吊牌id
			onlyTag,//仅打印吊牌
            ...othersSet
        } = data;
        this.data = {
            printOp,
            printType,
        };
        this.printTraceId = printItem.generatePrintInfo({printSource:'scan',isMerge:isMerge,scanType})
        comp.Print.Data.fjrInfoMap = fjrInfoMap;
        comp.Print.Data.platformId = platformId || '147317';
        let xbqTempisInvalid = false
        let systemSetting = window?.erpData?.systemSetting || {}
        let printTagSet = JSON.parse(systemSetting?.printTagSet || '{}');
		let xbqTemplateDetailInfo = null
        comp.Print.Data.printTagSet = printTagSet
        let originalOrderList = [...orderList]
        // 获取传入的其他配置
        const {
            canUseNewWaybillCodeSet, //可以使用新单号
            canChooseWaybillCodeNumSet, // 可以选择单号数量
            choosePrinterNotiSet, // 可以弹出选择打印机
            displayPrintNumSet, // 可以选择打印机打印份数
            // openPrintNotifySet, // 出纸进度
        } = othersSet;
		printItem.uploadPrintLog('scan', {
			orderList: orderList,
			fjrInfoMap: fjrInfoMap,
			printTraceId: this.printTraceId,
			templateInfo: orangeTemp,
		})
        let templateInfo, templateDetailInfo, sonElecNoCount, isZiMuJian = false;
        if (templateInfoDetail) {
            templateDetailInfo = templateInfoDetail;
            templateInfo = templateDetailInfo.ModeListShow;
            templateInfo.styleId = templateDetailInfo.ModeList.StyleId;
        } else if (isMerge) {
            const setting = window.erpData?.advancedSetting?.groupPrintSetJsonString;  // 全局高级设置
            // 回流订单是否使用菜鸟打印还是平台打印
            const isUseCainiao = window.erpData?.advancedSetting?.refluxOrderPrintSet == '1'

            // 是否包含小红书模板
            // let orangeTemp = templateInfoData.userTemplateList.find(o=>o.expressType === 13)
            const platByKddType = platByKddTypeFnc({setting,groupInfo:orangeTemp})

            const order = orderList[0];
            let useKddType = platByKddType[order.platform]
			const isDyEncodeHand = order.platform === 'fxg' && order.source === 'HAND' && order.tradeEncodeType == 1
			// 抖音明文手工单需要改写一下绑定，跟手工单保持一致`
			if (isDyEncodeHand) useKddType = platByKddType['hand']


            if(orderList[0].hlPlatformType && !['hl-other','hl-tx'].includes(orderList[0].hlPlatformType)  && (orderList[0].hlEncryptOrder || !isUseCainiao)) {
                let hlType = orderList[0].hlPlatformType.replace('hl-','')
                useKddType = ORDER_REFLUX_TYPE[hlType]
				if(hlType === 'xhs') {
					let xhsTemp = orangeTemp.userTemplateList.find(it=>it.expressType === useKddType)
					useKddType = !!xhsTemp ? 13 : 16
				}
            }
            const temp = orangeTemp.userTemplateList.find(o => o.expressType == useKddType);
            templateDetailInfo = await printItem.getTempDetailInfo({
                printType,
                templateId: temp.userTemplateId,
            });
            templateInfo = templateDetailInfo.ModeListShow;
            templateInfo.styleId = templateDetailInfo.ModeList.StyleId;
        } else {
            templateInfo = orangeTemp;
            templateDetailInfo = await printItem.getTempDetailInfo({
                printType,
                templateId: templateInfo.Mode_ListShowId,
            });
        }

        // 检查吊牌交替打印模板是否存在
        if(isPrintTag){
			if (printTagSet?.tagTemplateSwitch == '1' && !tagTempId) {
                let temps = comp.Print.Data.bqTemplateListdpd.ModeListShows.find((item) => {
                    return printTagSet?.tagTemplate?.Mode_ListShowId === item.Mode_ListShowId
                })
                xbqTempisInvalid = !temps
			} else if (tagTempId) {
				// 获取模板详情
				let temps = comp.Print.Data.bqTemplateListdpd.ModeListShows.find((item) => {
					return tagTempId === item.Mode_ListShowId
				})
				if (temps) {
					xbqTemplateDetailInfo = await printItem.getTempDetailInfo({
						printType: temps.Modeid,
						templateId: temps.Mode_ListShowId,
						isHideloading: true
					});
				} else {
					xbqTempisInvalid = true

				}
			} else {
				xbqTempisInvalid = true
			}


        }

        // 设置一下打印份数
        comp.Print.setPrintCopies(printNums || 1);
        // 检查是否子母件或快运
        isZiMuJian = await printItem.checkIsZiMuJianAndKuaiyun(templateInfo, isMerge);
        if(isZiMuJian)sonElecNoCount = waybillCodeCount
        // 检查一下模板有没有禁止打印,有没有无需物流发货，有没有https下韵达网点打印
        await printItem.checkTemplate(templateInfo, this.httpWhiteListHook, data);
        console.info('模板是否可打印检查');

        //针对网点面单订单未解密临时处理过滤
        if(templateInfo.KddType == 2){
        orderList =  await printItem.checkoutIsDecrypted({
                temp:templateInfo,
                orderList,
                afterContinuePrintHook:this.afterContinuePrintHook
            })
        }
        // 获取模板详情

        console.info('获取模板详情', templateDetailInfo);
        const installResult = await printItem.checkPrintComponent(templateInfo, printType);
        this.afterCheckComponentHook(installResult);
        if (!installResult.isSuccess) {
            this.printInterruptNotify()
            return;
        }
        if (isPrintTag && !xbqTempisInvalid) {
            let lodopinstallResult = printItem.checkLodopProgress();
            if (!lodopinstallResult.isSuccess) {
                return;
            }
        }


        // 第二次检查一下模板能否打印 是否有退款
        // erp平台检查能否打印
        // await printItem.checkCanPrint({
        //     printType,
        //     templateInfo,
        //     orderList,
        //     printOp,
        //     printInterruptNotify: this.printInterruptNotify
        // });
        printItem.setTradeFjr({ orderList, fjrInfo, printType });

        // 检查一下发件人省市区（大头笔）有没有
        await printItem.checkFjrAddress(fjrInfo,null,this.printInterruptNotify);

        // 获取大头笔，快递五连单需要
        // eslint-disable-next-line require-atomic-updates
        orderList = await printItem.getBigHead(
            templateInfo,
            orderList,
            printOp,
        );
        console.assert(Array.isArray(orderList) && orderList?.length > 0, 'orderList处理错误');

        /**
         * 获取打印时必要的数据信息
         * 例如极兔（龙邦）模板需要获取商品信息
         * @param templateInfo
         * @returns {Object}
         */

        const otherPrintInfo = await printItem.getOtherPrintInfo(templateDetailInfo);
        console.assert(otherPrintInfo, 'otherPrintInfo');

        /**
         * 快递单号获取逻辑,重打提示框，多单号选择框，子母件选择框
         * @param {Object} orderList
         * @param {Object} templateInfo
         * @param {Object} templateDetailInfo
         * @returns {boolean} isMustUseNewWaybillCode 是否必须用新单号
         * @returns {number} waybillCodeCount 申请单号的总数量
         * @returns {Array} oldWaybillCodeList 要使用的旧单号
         * @returns {number} sfZDQuantity 子母件数量
         */
        const infoAboutWaybillCodeNums = await this._getInfoAboutWaybillCodeNums({
            orderList,
            isMustUseOldCode
        });

        /** 获取网点信息
         * @param {object} templateInfo
         */
        const wdSet = defaultAddress || await printItem.getYunBranchSet(templateInfo);

        console.assert(wdSet, '未获取到网点信息');
        // if(templateInfo.KddType !== 13)orderList = await printWithEncryptOrder({ orderList, afterContinuePrintHook: this.afterContinuePrintHook });
        if(templateInfo.KddType == 13){
            let xhsorder = orderList.find(o =>{
                return  ['xhs'].includes(o.platform)
            })
            if(!xhsorder)  orderList = await printWithEncryptOrder({orderList,afterContinuePrintHook:this.afterContinuePrintHook})
        }else if(templateInfo.KddType == 14){
            let sphorder = orderList.find(o =>{
                return  ['sph'].includes(o.platform)
            })
            if(!sphorder)  orderList = await printWithEncryptOrder({orderList,afterContinuePrintHook:this.afterContinuePrintHook})
        }else{
            orderList = await printWithEncryptOrder({orderList,afterContinuePrintHook:this.afterContinuePrintHook})

        }
        orderList = await printWithEncryptPackages(orderList, templateInfo);
        //获取单号流程，获取后会把数据放入 orderList 中
        // eslint-disable-next-line require-atomic-updates
        this.timer.start('申请单号')
        if(logRePrint){
            const otherPrintDataMap  = await this._getOtherPrintData(orderList)
            orderList.map( item => {
                const ydAttr = JSON.parse(item.ydAttr || '{}');
                let printDataInfo = '' ;
                if(templateDetailInfo.ModeListShow.KddType == 14){
                    printDataInfo =  otherPrintDataMap[item.ydNo] || '' ;
                }else{
                    printDataInfo =  JSON.parse(otherPrintDataMap[item.ydNo] || {}) ;
                }
                let PrintDatas = '', wayBillNos = '' , geturl = '',customUrl; //京东云打印控件数据处理
                if(templateDetailInfo.ModeListShow.KddType == 5){
                    if(printDataInfo == '' || !printDataInfo){
                        jdl_zmj = true;
                    }else{
                        const printPdfData = printDataInfo
                        const prePrintDatas = printPdfData.pullDataRespDTO && printPdfData.pullDataRespDTO.prePrintDatas;
                        geturl = printPdfData.standardTemplate && printPdfData.standardTemplate.standardTemplates[0];
                        customUrl = printPdfData.customUrl;
                        prePrintDatas && $.each(prePrintDatas, function (i, it) {
                            PrintDatas = it.perPrintData;
                            wayBillNos = it.wayBillNo;

                        });
                    }
                }
                Object.assign(item, {
                    cloudData: printDataInfo,
                    datoubi: printDataInfo.shortAddress,
                    jibaodi: printDataInfo.packageCenterName,
                    codPrice: ydAttr.dsje,
                    cplx: printDataInfo.proName,
                    route: printDataInfo.route,
                    sfwd: printDataInfo.shippingBranchName,
                    txm_jbm: printDataInfo.packageCenterCode,
                    jibaoma: printDataInfo.packageCenterCode,
                    ddwd: printDataInfo.consigneeBranchName,
                    packageYdId: printDataInfo.packageYdId,
                    hkzj: ydAttr.hkzj || '',
                    goods_title: ydAttr.goods_title || '',

                    zy_sfwd: printDataInfo.codingMapping,
                    zy_sfwdbm: printDataInfo.consigneeBranchCode,
                    zy_mdwd: printDataInfo.sourceTransferCode,
                    zy_mdwdbm: printDataInfo.shippingBranchCode,
                    txm_number_package: printDataInfo.packageYdId,
                    fhdexnumber_package: printDataInfo.packageYdId,
                    sortCode: printDataInfo.sortCode,

                    printPdfData:printDataInfo.printData, //京东云一联单加密数据
                    jdprintData:PrintDatas,
                    geturl:geturl,
                    customUrl:customUrl,
                    wayBillNo:wayBillNos,

                });
            });
        }else{
            orderList = await printItem.getWaybillCodeDialog({
                orderList,
                temp: templateInfo,
                elecNoCount: waybillCodeCount, // 单号数量
                sfZDQuantity: isZiMuJian ? waybillCodeCount : null, // 子母件生成个数(不仅仅是顺丰，只是后端字段名是这个而已)
                sonElecNoCount: isZiMuJian ? waybillCodeCount : null, //子单号数量（菜鸟快运子母件）
                oldYdNos: infoAboutWaybillCodeNums.oldWaybillCode ? [infoAboutWaybillCodeNums.oldWaybillCode] : null, //要使用的旧单号
                printOp, // [操作来源  0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单]
                wdSet, // [网点地址选择对象]
                isUseOrderFjr: false, //是否使用订单里面设置的发件人信息：发件人信息在每一笔订单数据中，无需传递kddFjr 或者 fhdFjr
                fjrInfo, // 发件人信息，兼容单店铺和多店铺
                isNewEleNo: infoAboutWaybillCodeNums.getNew, //是否必须使用新单号
                loginUserId: userInfo.userId,//登陆用户userId
                isRepeat: false,// [针对安能盲区添加的安能盲区订单重试的措施 true:代表安能盲区重试]
                modeInfo: templateDetailInfo,
                hasWWChat: false,//[true/false:是否需要联系旺旺提示]
                goodsDescription: otherPrintInfo.goodsDescription || goodsDescription, //物品描述，获取单号时需要
                afterGetWaybillCodeHook: this.afterGetWaybillCodeHook,//获取单号后执行的钩子函数
                packagingDescription: packagingDescription, //物品描述，获取单号时需要
                afterContinuePrintHook: this.afterContinuePrintHook,
                isZiMuJian: isZiMuJian,
                afterGetElecErrorHook: this.afterGetElecErrorHook,
                printInterruptNotify: this.printInterruptNotify,
                processBatch:this.printTraceId,
                isScan: true,
            });
        }

        this.timer.stop('申请单号',{
            type: 'xbq',
        })

        if (isPrintTag && !xbqTempisInvalid) {
            new concatPrintDpd().print({
                choosedPrinter: choosedTagPrinter,
                orderList: originalOrderList,
				xbqTemplateDetailInfo,
                isShowProgress: false
            });
        }
        // 多单号处理逻辑
        // eslint-disable-next-line require-atomic-updates
        orderList = await printItem.moreWaybillCodeProcess({
            orderList,
            sfZDQuantity: sonElecNoCount,
            waybillCodeCount: waybillCodeCount,
            temp: templateInfo,
            isZiMuJian: isZiMuJian,
        });
        /*
        * 这里还能放一个检查是否发货内容打印到另一张上的逻辑
        * 行为仍然是修改 orderList 的内容
        * 暂时只有淘宝需要这个逻辑
        */

        // 本次打印是否向用户展示预览按钮
        const isHasPreview = false;

        //获取打印序号，把打印序号信息放入 orderList 中
        // eslint-disable-next-line require-atomic-updates
        // orderList = await printItem.dealWithPrintNum({
        //     orderList,
        //     printType: 'kdd',
        // });
        // //拼多多获取打印批次
        // // 整合平台获取打印批次 0818 放心购厂家代打没有批次功能 0901
        // orderList = await printItem.dealWithPrintBatch({
        //     orderList,
        // });
        // 判断打印内容是否需要打印在另一张纸上
        // 对于「需要在第二张纸上打印发货内容的 order 中」加上 isNeedBreakPage:true
        const checkedBreakPageResult = printItem.checkedBreakPage({
            orderList,
            modeInfo: templateDetailInfo,
            printStyle,
        });
        orderList = checkedBreakPageResult.orderList;
        const extraPrintContentPageNum = checkedBreakPageResult.extraPrintContentPageNum;


        //打印进度弹窗
        // const progress = new printItem.getPrintProgress({
        //     showType: 'mark',
        //     totalCount: orderList.length,
        // });

        // const client = comp.Print.getClientType(templateDetailInfo);
        // const isCloudPrint = client == 'pdd' || client === 'cainiao';

        //打印标记
        // 如果某个订单获取单号报错了，则对应的订单不进行标记
        originalOrderList = originalOrderList.reduce((acc, item) => {
            if (orderList.find(order => order.togetherId === item.togetherId)) {
                acc.push(item);
            }
            return acc;
        }, []);
        // await printItem.markPrintStatus({
        //     orderList: originalOrderList, //此处需要原始的 orderList 数据
        //     printType: 'kdd',
        //     templateInfo,
        //     printer: choosedPrinter,
        //     isCloudPrint,
        //     // isCloudPrint,
        //     wdSet,
        //     printCopies: 1,
        // });
        this.timer.start('打印标记')
        orderList = await  printItem.handleScanPrint({
            originalOrderList:originalOrderList,
			orderList: orderList,
			printWebSource
        })
        //完成「打印标记」后的钩子
        this.afterSavePrintMarkHook({ markTradeList: orderList });
        this.timer.stop('打印标记',{
            type: 'xbq',
        })

        // progress.showType('progress');
        // 美团需要鉴权
        // orderList = await printItem.getAuthPrintDevice(templateInfo, orderList);
        this.timer.start('任务发送')
        //组装打印数据
        const printDatas = await printItem.getFormatPrintData({
            orderList,
            templateInfo,
            kddFjrMap: fjrInfo,
            printType,
            modeInfo: templateDetailInfo,
            isGetFhdFjr: false,
            fhdFjrMap: [],
        });

        console.assert(printDatas, '组装数据');

        // 打印步骤
        const params_doPrint = {
            orderList,
            printDatas,
            // progress,
            printType,
            choosedPrinter,
            templateInfo,
            templateDetailInfo,
            othersSet,
            extraPrintContentPageNum,
            wdSet,
            printNums,
			printWebSource
        };

        this._doPrint(params_doPrint);
    }

    // 打印 && 底单逻辑
    _doPrint({
        orderList,
        printDatas,
        // progress,
        printType,
        choosedPrinter,
        templateInfo,
        templateDetailInfo,
        othersSet,
        extraPrintContentPageNum,
        wdSet,
        printNums,
		printWebSource

    }) {
        const self = this;
        console.log('打印 && 底单逻辑----');
        const printParams = {
            printDatas: printDatas,
            tempId: templateInfo.Mode_ListShowId,
            ptype: printType,
            printBoxIsShow: 0,
            selectedPrinter: choosedPrinter,
            templateSet: templateDetailInfo,
            showPrinting: othersSet.showPrinting, // 显示出纸进度 暂时只针对快递单开放，开关设置走高级设置
            wdSet: wdSet,
        };
        console.info(printParams);
        //打印回调methods获取
        const methodsCb = {
            printSynFunc: function (curCount) {
                // progress.updateProgress(curCount);
            },
            printOkFunc: async function () {
                    self.timer.stop('任务发送',{
                    type: 'xbq',
                })
                //底单日志存储
                // progress.showType('log');
                const results = await printItem.saveKddLog({
                    orderList,
                    templateInfo,
                    printOp: self.data.printOp, //
                    templateDetailInfo,
                    printNums,
                    isScan: true,
                    processBatch:self.printTraceId,
					printWebSource
                });
                // if(!!templateDetailInfo.ModeTempPrintcfg.IsConcatFhd){
                //     const tempId = await printItem.getDefaultTempId('fhd');
                //     templateInfo = await printItem.getTempById(tempId, 'fhd');
                //     await printItem.saveFhdLog({
                //         orderList,
                //         templateInfo,
                //     });
                // }
                console.log('saveKddLog 方法的返回值', results);
                self.afterSavePrintLogHook(orderList, results?.isSuccess);
                //TODO 底单补偿。
                // progress.remove();
                console.log(printParams, 'printParams');
                // 清除打印来源信息
                window.printSourceInfo = null
                // const isYilianTemp = comp.base.isYilianTemp(templateInfo.styleId);
                const isYilianTemp = printParams?.templateSet?.ModeList?.WidthPaper === 760;
                const tempType = isYilianTemp ? 'yilian' : 'common';
                new comp.Print().setIsConcatFhd(tempType, false);
            },
            /**
             *
             * @param {object} data  // 打印控件返回的内容
             * @param {string} compType //是哪种控件返回的提示
             */
            notifyPrintCb: function (data, compType) {
                console.log('--notifyPrintCb---');
                console.info(data);
                console.info(compType);
                self.notifyPrintHook(data, compType);
            },
        };

        const p = new comp.Print();

        p.printTemplate(printParams, methodsCb);
    }

    // 封装：通过单号选择框获取单号的相关信息
    async _getInfoAboutWaybillCodeNums({
        orderList,
        isMustUseOldCode,
    }) {

        const { sidList } = orderList[0] || {};
        if (!sidList) {
            return {
                getNew :  isMustUseOldCode ? 2 :0
            };
        }
        /**
         * 判断是否弹出已打印弹窗并执行弹框；如果已打印，允许用户选择使用原单号还是新单号打印，新单号可以选择新单号个数
         * 会返回一个对象，包含三个值：
         * isMustUseNewWaybillCode 是否是用新单号 Boolean
         * oldWaybillCodeList 旧单号列表 Array 缺省为空数组
         * waybillCodeCount 本次需要的单号数量 Number
         */
        this.timer.start('强制补打')
        let chooseWaybillInfos = await printItem.scanPrintAgainDialog({
            oldWaybillCodeList: sidList,
            printInterruptNotify: this.printInterruptNotify
        });
        this.timer.stop('强制补打',{
            type: 'xbq',
        })
        if(chooseWaybillInfos?.isMustUseNewWaybillCode){
            chooseWaybillInfos.getNew =1
        }else{
            chooseWaybillInfos.getNew =2
        }
        return chooseWaybillInfos
    }

  //获取其他打印数据
  async _getOtherPrintData(orderList){
    const params = this._getBgPrintDataParams(orderList);
    let listParam =  {
        jsonParam: JSON.stringify(params),
    };


    const results = await urlHelper.threadRequest({
        list: [listParam],
        threadCount:4,
        apiUrl: 'PRINT_GET_BG_PRINTDATA_ERP',
    });
    if( !results.isSuccess ){
        const failYdNo = [];
        results.errorResults.map( ()=>{
            // 拼多多取的是elecNo字段
            failYdNo.push(params[0].ydNo || params[0].elecNo);
        });
        Tatami.showFail(`检测如下运单号数据异常，建议去订单页面打印该订单${ failYdNo }`);
        return Promise.reject();
    }

    const modifyYdNo = [];
    let printDatas = {};

    results.successResults.map( item => {
        const data = item.resultJson.data;
        printDatas = Object.assign(printDatas,data)
    });

    if( modifyYdNo.length ){
        Tatami.showFail(`检测如下运单号收件信息已修改，建议去订单页面打印该订单${ modifyYdNo }`);
        return Promise.reject();
    }
    return Promise.resolve( printDatas );
    }
    _getBgPrintDataParams( orderList ){
        return orderList.map( item => {
            let data =  {
                elecNo:item.ydNo,
                userId:item.userId, //TODO
                kdCode:item.kdCode,
                // kddType: item.type,
                receiverProvince:item.receiverProvince,
                receiverCity: item.receiverCity,
                receiverCounty:item.receiverCounty,
                receiverAddress:item.receiverAddress,
                receiverName:item.receiverName,
                receiverTel:item.tel,
                receiverMobile: (item.mobile || item.tel),
                oaid: item.caid || '',

                // 加解密需要覆盖的参数
                ...(item.bgParams || {}),

                sellerId:item.sellerId,

                platform:item.platform,

                exId:item.kdId,

                tids:item.tids,

                encodeTid:item.encodeTid,

                ydNo:item.exNumber,

                receiverMobile:item.receiverMobile,
            };

            return data;
        });
    }
    // --------------钩子函数的初始化--------------------
    /**
     * 检测控件安装结果后执行的钩子函数
     * type: 'cainiao'|'lodop'
     * isSuccess: true|false
     */
    afterCheckComponentHook() {
        console.log('afterCheckComponentHook', arguments);
    }

    //选择模板后执行的钩子函数
    afterChooseTempHook() {
        console.log('afterChooseTempHook', arguments);
    }

    //选择发件人后执行的钩子函数
    afterChooseFjrHook() {
        console.log('afterChooseFjrHook', arguments);
    }

    notifyPrintHook() {
        console.log('afterChooseFjrHook', arguments);
    }

    afterGetWaybillCodeHook() {
        console.log('afterGetWaybillCodeHook', arguments);
    }

    afterSavePrintMarkHook() {
        console.log('afterSavePrintMarkHook', arguments);
    }

    //保存打印日志后的钩子函数
    afterSavePrintLogHook() {
        console.log('afterSavePrintLogHook', arguments);
    }

    afterBranchChangeHook() {
        console.log('afterBranchChangeHook', arguments);
    }

    httpWhiteListHook() {
        console.log('httpWhiteListHook', arguments);
    }
    // 获取单号有失败的订单时的回调
    afterGetElecErrorHook() {
        console.log('afterGetElecErrorHook', arguments);
    }
    printInterruptNotify (){
        console.log('打印中断', arguments);
    }
    // 备注多单号完成后的回调
    afterAutoAddPrintNum(dealedOrderList, waybillCodeCount) {
        // 打印时自动追加单号到备注
        if (advancedSet.isAutoAddPrintNum) {
            const memos = [];
            if (waybillCodeCount > 1 || (waybillCodeCount === 1 && !advancedSet.isNotAddPrintNumOnlyOne)) {
                dealedOrderList.forEach(order => {
                    memos.push({
                        togetherId: order.togetherId,
                        tid: order.tids[0],
                        flag: order.sellerFlag || '',
                        memo: order.sellerMome || order.allYdNos.join(','),
                        userId: order.userId,
                    });
                });
            }
            // 调用保存备注接口
            this.getCtrl('com.printSetting', 'singleModifyMemo', memos, false, true);
        }
    }

    // 获取单号有失败的订单时的回调
    afterContinuePrintHook() {
        console.log('afterContinuePrintHook', arguments);
    }

    // --------------钩子函数的初始化--------------------
}
export default ScanPrintKdd;
