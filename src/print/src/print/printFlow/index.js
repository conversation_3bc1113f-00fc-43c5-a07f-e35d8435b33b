import * as printItem from '../printItem';
import batchPrintFhd from './batchPrintFhd';
import batchPrintKdd from './batchPrintKdd';
import concentratePrint from './concentratePrint';
import mergePatchPrintKdd from './mergePatchPrintKdd';
import asyncPrintKdd from './asyncPrintKdd';
import singlePrintKdd from './singlePrintKdd';
import scanPrintKdd from './scanPrintKdd';
import concatPrintDpd from './concatPrintDpd';
import singlePrintFhd from './singlePrintFhd';
import printBhdXbq from './printBhdXbq';
import logReprint from './logReprint';
import { configHasElecNum } from '../config';
import '../../css/combine.less';
import proxyPolyfill from '../../common/polyfill';
import { isRecyclable } from '../utils/tpl';
import { platformConfig } from './platformConfig';

console.log('print 就绪', new Date());

class PrintAPI {

    constructor(arg) {
        console.log(arg, '获取平台信息');
        platformConfig.crypto.api.decryptFunction = arg.decryptFunction;
        platformConfig.crypto.api.bgDecryptFunction = arg.bgDecryptFunction;
        platformConfig.platform = (arg.platform || '').toLowerCase();

        comp.Print.Data = Object.assign(comp.Print.Data, {
            isHander: arg.isHander,      //是否是手工单 除手工单无需传递
            platform: (arg.platform || '').toLowerCase(),     //平台： 阿里：ali/京东：jd/淘宝：tb/自助版：zzb
            appkey: arg.appkey,
            exuid: arg.userInfo.userId,
            sellerNick: arg.userInfo.userNick,
            kdzsToken: arg.kdzsToken,    //token
            tokenName: arg.tokenName,
            qnkdzsToken: arg.qnkdzsToken,     //header qnquerystring  有些平台qnquerystring的组装方式不一样
            userInfo: arg.userInfo,
            userId: arg.userId,
        });


        // 获取聚合打印分发模式类型
        let whiteListSetting =  comp.Print.Data.userInfo.whiteListSetting || '{}'
        let localMergeMode = localStorage.getItem('newPrintMode')
        comp.Print.Data.newPrintMode = localMergeMode ||  JSON.parse(whiteListSetting).newPrintMode == 1
        comp.Print.Data.showAutoDecode = JSON.parse(whiteListSetting).showAutoDecode == 1
    }
	initPrint(){
		comp.Print.init();
        comp.Print.doWork();
	}
    batchPrintFhd(data, hook) {
        new batchPrintFhd(hook).print(data);
    }

    batchPrintKdd(data, hook) {
        let setting = window.erpData?.advancedSetting?.groupPrintSetJsonString
        const isMerge = setting?.openMergePrint == 2
        let applyAndPrint = window.erpData?.advancedSetting?.applyAndPrint == 1
        if (applyAndPrint && !data.firstSendMode && !data.scanFjrInfo) { //爆款扫描暂不支持边申请边打印
            new asyncPrintKdd(hook).print({ ...data, isMerge })
        } else {
			!!isMerge ? new mergePatchPrintKdd(hook).print(data) : new batchPrintKdd(hook).print(data);
        }

    }
	concentratePrint(data, hook) {
		new concentratePrint(hook).print(data);
	}
    singlePrintKdd(data, hook) {
        console.log('======= front==========');
        new singlePrintKdd(hook).print(data);
    }
    scanPrintKdd(data, hook) {
		data.onlyTag ? new concatPrintDpd(hook).print(data) : new scanPrintKdd(hook).print(data);
	}
	concatPrintDpd(data, hook) {
		new concatPrintDpd(hook).print(data);
	}

    async singlePrintFhd(data, hook) {
        new singlePrintFhd(hook).print(data);
    }

    logReprint(data, hook = {}) {
        new logReprint(hook).print(data);
    }

    printBhdXbq(data, hook = {}) {
        new printBhdXbq(hook).print({
            ...data,
            printType: 'bhd'
        });
    }
	printBhdBhbq(data, hook = {}) {
		new printBhdXbq(hook).print({
			...data,
			printType: 'bhbq'
		});
	}
    printThdXbq(data, hook = {}) {
        new printBhdXbq(hook).print({
            ...data,
            printType: 'thd'
        });
    }
    printGoodsBarCodeOrTag(data, hook = {}) {
        new printBhdXbq(hook).print({
            ...data
        });
    }
    batchPrintPurchaseOrder(data, hook = {}) {
        new printBhdXbq(hook).print({
            ...data,
            printType:'cgd'
        });
    }
    batchPrintWarehousingEntry(data, hook = {}) {
        new printBhdXbq(hook).print({
            ...data,
            printType:'rkd'
        });
    }
    batchPrintOutboundOrder(data, hook = {}) {
        new printBhdXbq(hook).print({
            ...data,
            printType:'ckd'
        });
    }
    batchPrintThqdList(data,hook={}){
        new printBhdXbq(hook).print({
            ...data,
            printType:'thqd'
        });
    }
    batchPrintBicOrderCodeList(data,hook={}){
        new printBhdXbq(hook).print({
            ...data,
            printType:'bic'
        });
    }
    batchPrintZbd(data, hook = {}) {
        new printBhdXbq(hook).print({
            ...data,
            printType: 'zbd'
        });
    }
    /**
     * 超文本打印
     * @param {object} params
     */
    printHtml(params) {
        printItem.printHtml(params);
    }

    /**
     * lodop保存表格HTML为excel
     * @param {object} params
     */
    lodopSaveAsExcel(params) {
        printItem.lodopSaveAsExcel(params);
    }

    /**
     * 模板设置
     * @param {object} param0
     * printType {string}模板类型，取值 kdd|fhd|bhd
     * tempId {string 可不传递,不传递展示默认模板} 展示模板id
     */
    showTemplateMain({ printType, tempId, fjrMap,groupTempID }) {
        if (comp.Print.Data.platform === 'fxg') {
            Tatami.pub('Tatami.clickPoint.manualTrigger', {
                'point': '24629.45235.50491.50492.56296',
                '_fm': '10193',
            });
        }

        printItem.showTemplateMain({ printType, tempId, fjrMap,groupTempID });
    }

    /**
     * 添加快递单
     */
    addKddTemplate() {
		if( comp.Print.chenkAuthor()) return
        printItem.addKddTemplate();
    }

    /**
     * 删除发货单
     */
    delKddTemplate() {
		if( comp.Print.chenkAuthor()) return
        printItem.delKddTemplate();
    }
    /**
     * 添加发货单
     */
    addFhdTemplate() {
        printItem.addFhdTemplate();
    }

    /**
     * 删除发货单
     */
    delFhdTemplate() {
        printItem.delFhdTemplate();
    }
    /**
     * @param {String} printType 'kdd','fhd'
     * @param {boolean} isRequestApi  是否强制请求 API，不使用缓存
    */
    async getTemplateList(printType = 'kdd', isRequestApi = false) {
        return await printItem.getTemplateList(printType, isRequestApi);
    }

    /**
     * 获取标签模板列表
     * @param {String} printType 'kdd','fhd'
     * @param {boolean} isRequestApi  是否强制请求 API，不使用缓存
    */
    async getBqTempList(printType = 'bq', isRequestApi = true) {
        return new Promise( (resolve, reject) =>{
            comp.print.data.getBqTempList({templateType:printType, isRequest:isRequestApi}, (data)=>{
                resolve (data)
             });
        })

    }
    /**
     * 获取快递单列表数组
     * @param {boolean 可不传递 默认是false} isRequestApi true: 不走缓存，false：可以走缓存
     * @return 返回快递单列表 无默认模板
     */
    async getKddTempList(isRequestApi = false,isOutRequest = false) {
        return await printItem.getKddTempList(isRequestApi,isOutRequest);
    }

    async batchGetBranchSet(arg) {
        return await printItem.batchGetBranchSet(arg);
    }

    async chooseTempalteDialog(isMerge = false, tempList, orderList) {
        const templateInfo = isMerge ? await printItem.chooseTemplateGroupDialog(tempList) : await printItem.chooseTemplateDialog('kdd', tempList, orderList);
        if (!templateInfo) {
            return false;
        }
        return templateInfo;
    }



    /**
     * 获取打印机列表
     * @param {boolean} isCloud
     */
    async getPrinterList(printClient = 'lodop') {
        return await printItem.getPrinterList(printClient);
    }

    /**
     *
     * @param {object} temp 模板数据 可不传递
     * @param {string} printType 模板类型 kdd||fhd||bhd
     */
    async checkPrintComponent(temp, printType) {
        return await printItem.checkPrintComponent(temp, printType);
    }
    async checkLodopProgress(isShow) {
        return await printItem.checkLodopProgress(isShow);
    }
	async checkPrintComponentByKdzs(data) {
		return await printItem.checkPrintComponentByKdzs(data);
    }
    /**
     * 获取【发货单，快递单】模板详细信息
     * @param {object} param 具体参数如下
     * temppalteId {number 必传 默认为}
     * printType {string 可不传 默认为‘kdd’} 模板类型，取值 kdd|fhd
     * isHideloading {boolean 可不穿 默认为false} 请求中是否展示loading
     */
    async getTempDetailInfo(param) {
        return printItem.getTempDetailInfo(param);
    }

    /**
     * 清除模板相关缓存
     * @param {string} key 清除缓存的类型 目前取值有：
     * 'fhdGlobalSet' 发货单全局设置
     */
    cleanCache(key) {
        printItem.cleanCache(key);
    }

    getCompData(key) {
        printItem.getCompData(key, { isRequest: true });
    }

    async recycleWaybillCode(arg) {
        return await printItem.recycleWaybillCode(arg);
    }

    configHasElecNum(kddType, ExCode) {
        return configHasElecNum(kddType, ExCode);
    }

    getPlatform() {
        return comp.Print.Data.platform;
    }

    // ---------- 通用方法 ------------
    isRecyclable(opt) {
        // 转换成 number
        opt.kddType = +opt.kddType;
        return isRecyclable(opt);
    }

    async batchGetWdBranchSet(...arg) {
        return await printItem.batchGetWdBranchSet(...arg);
    }

    /**
     * 视频号模板同步
     * @param {string 可不传递 默认是ALL} downType 全量-ALL，增量-ADD 废弃字段只有ALL
     * @param {Array} sellerInfo 结构：[{"kddType":14, "sellerId": 2812929679}]
     * @return 返回快递单列表
     */
    async sphUserTemplateSyn(sellerInfo = []) {
        return await printItem.sphUserTemplateSyn(sellerInfo);
    }


    /**
     * 视频号模板校验
     * @param {string 必传} exCode
     * @param {string 平台模板id 必传} platformUserTemplate
     * @return true or false
     */
    async sphUserTemplateCheck(exCode,platformUserTemplate,token) {
        return await printItem.sphUserTemplateCheck(exCode,platformUserTemplate,token);
    }
    /**
     * 视频号删除店铺并更新模板
     * @param {string 必传} sellerId
     * @return true or false
     */
    async sphUserTemplateDelete(sellerId) {
        return await printItem.sphUserTemplateDelete(sellerId);
    }
}


const PrintAPIProxy = new proxyPolyfill(PrintAPI, {
    get: function (target, key, receiver) {
        if (key in target) {
            return Reflect.get(target, key, receiver);
        } else {
            throw new ReferenceError(`不存在${key}这个方法或属性`);
        }
    },
    set: function () {

    },
});

// window.onerror = function (message, source, lineno, colno, error) {
//     //报错收集，这里可以用 api 上传错误信息
//     console.info('捕获到了错误');
//     console.log('message', message);
//     console.log('source', source);
//     console.log('lineno,colno', lineno, colno);
//     console.log('error', error);
// };

window.PrintAPI = PrintAPIProxy;

export default PrintAPIProxy;
