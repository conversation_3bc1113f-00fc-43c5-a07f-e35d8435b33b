/* eslint-disable require-atomic-updates */
import * as printItem from '../printItem';
import { isSmallTag } from '../config';
import { printFhdEncrypt } from './crypto';
class PrintFhd {
    constructor(hook = {}) {
        const {
            afterCheckComponentHook,
            afterChooseTempHook,
            afterChooseFjrHook,
            afterSavePrintMarkHook,
            afterSavePrintLogHook,
        } = hook || {};

        afterCheckComponentHook && (this.afterCheckComponentHook = afterCheckComponentHook);
        afterChooseTempHook && (this.afterChooseTempHook = afterChooseTempHook);
        afterChooseFjrHook && (this.afterChooseFjrHook = afterChooseFjrHook);
        afterSavePrintMarkHook && (this.afterSavePrintMarkHook = afterSavePrintMarkHook);
        afterSavePrintLogHook && (this.afterSavePrintLogHook = afterSavePrintLogHook);
    }

    // 具体流程实现
    async print(data) {
        console.log('批打发货单 传给打印中心的数据', data);
        this.params = data;
        const printType = 'fhd';
        let {
            templateInfo:tempInfo
            , orderList = []
            , printOp
            , fjrInfoMap
            , isBIC = false
            ,fhdDecrypt
            , ...othersSet
        } = data;
		// 商品排序方法
        // interface FhdRuleSortDataProps {
        //     tableDataArray: TableData[];
        //     rule?: FhdRuleEnum; 123456
        // }
        // 执行是否解密逻辑
        orderList = await printFhdEncrypt(orderList, fhdDecrypt);
        let templateInfo = null
        const {
            choosePrinterNotiSet, // 可以弹出选择打印机
            displayPrintNumSet, // 可以选择打印机打印份数
            showCombinationInfo, // 打印子货品
        } = othersSet;
        comp.Print.Data.showCombinationInfo = showCombinationInfo
        // 当前打印的发货单模板取默认模板
        // BIC订单需要单独获取一下打印模板的id
        if (isBIC) {
            templateInfo = await printItem.getBICTempInfo();
        } else {
            const tempId = tempInfo ? tempInfo.Mode_ListShowId :  await printItem.getDefaultTempId(printType);
            templateInfo = await printItem.getTempById(tempId, printType);
        }


        // 如果没有传入模板信息，则弹出模板选择框
        if (!templateInfo || Object.keys(templateInfo).length === 0) {
            templateInfo = await printItem.chooseTemplateDialog(printType);
            if (!templateInfo) {
                return false;
            }
            // 执行选择模板后钩子函数
            this.afterChooseTempHook(templateInfo.Mode_ListShowId);
        }

        // lodop控件校验
        const installResult = printItem.checkLodopProgress();
        this.afterCheckComponentHook(installResult);
        if (!installResult.isSuccess) {
            return;
        }

        // 检查订单是否支持打印
        // await printItem.checkCanPrint({
        //     printType,
        //     templateInfo,
        //     orderList,
        //     printOp,
        // });
        // 获取模板详情
        const templateDetailInfo = await printItem.getTempDetailInfo({
            printType,
            templateId: templateInfo.Mode_ListShowId,
        });
        if(templateDetailInfo.ModeTempPrintcfg?.ItemSortRule){
            orderList = orderList.map(o=>{
                if(o.fhdRuleSortData)o.fhdRuleSortData({ tableDataArray:o.tableDataArray,rule:templateDetailInfo.ModeTempPrintcfg?.ItemSortRule })
                delete o.fhdRuleSortData
                return o
            })
        }
        // 获取本次打印使用的的发件人
        let fjrInfo, modifyDefaultFjr;
		// if (!isSmallTag(templateInfo)) {
            ({ fjrInfo, modifyDefaultFjr } = await printItem.chooseFjr(fjrInfoMap, printType,null,{orderList}));
            modifyDefaultFjr && modifyDefaultFjr.length && this.afterChooseFjrHook(modifyDefaultFjr);
		// }
        // 重打提醒
        const hasPrinted = await printItem.checkOrderHasPrint(orderList, 'fhd');
        await printItem.surePrintAgain({
            printType,
            templateInfo,
            hasPrinted,
            orderList,
        });

        // 选择打印机
        const { choosedPrinter, printNums } = await printItem.choosePrinter({
            orderList,
            closeChoosePrinterNoti: choosePrinterNotiSet && printOp == 1,
            defaultPrinter: ((templateDetailInfo || {}).ModeTempPrintcfg || {}).DefaultPrinter,
            printType,
            templateInfo,
            templateDetailInfo,
            displayPrintNum: displayPrintNumSet,
            orderNum: orderList.length,
            isHasAutoShip: false, //暂时写死不显示自动发货
            shipAfterPrint: false,
            isCloudPrint: false,
            isHasPreview: true,
            previewFunc: async ({ choosedPrinter }) => {
                comp.Print.Data.batchPreviewFhd = true;
                const printDatas = await printItem.getFormatPrintData({
                    orderList,
                    templateInfo,
                    fhdFjrMap: fjrInfo,
                    printType,
                    modeInfo: templateDetailInfo,
                    isBIC,
                });

                this._doPreview({
                    // orderList,
                    printDatas,
                    // progress,
                    printType,
                    choosedPrinter,
                    templateInfo,
                    templateDetailInfo,
                    othersSet,
                });
            },
        });
        //获取打印序号
        orderList = await printItem.dealWithPrintNum({
            orderList: orderList,
            printType: printType,
        });
        //打印进度弹窗
        const progress = new printItem.getPrintProgress({
            showType: 'mark',
            totalCount: orderList.length,
        });
        let markPrintResults =  await printItem.markPrintStatus({
            orderList,
            printType: printType,
            templateInfo,
            printer: choosedPrinter,
            printCopies: printNums,
        });
        if(!markPrintResults.isSuccess){
			Tatami.showFail('订单标记打印失败，请重试，或联系在线客服');
            progress.remove()
            return
        }
        this.afterSavePrintMarkHook({ markTradeList: orderList });

        progress.showType('progress');


        //组装打印数据
        const printDatas = await printItem.getFormatPrintData({
            orderList,
            templateInfo,
            fhdFjrMap: fjrInfo,
            printType,
            modeInfo: templateDetailInfo,
            isBIC,
        });
        //打印
        this._doPrint({
            orderList,
            printDatas,
            progress,
            printType,
            choosedPrinter,
            templateInfo,
            templateDetailInfo,
            othersSet,
        });
    }
    _doPreview({
        printDatas,
        printType,
        choosedPrinter,
        templateInfo,
        templateDetailInfo,
        othersSet,
    }) {
        const printParams = {
            printDatas: printDatas,
            tempId: templateInfo.Mode_ListShowId,
            ptype: printType,
            printBoxIsShow: 0,
            selectedPrinter: choosedPrinter,
            templateSet: templateDetailInfo,
            showPrinting: othersSet.showPrinting, // 显示出纸进度 暂时只针对快递单开放，开关设置走高级设置
            hasView: true,
        };
        //打印回调methods获取
        const methodsCb = {
            printSynFunc: function () {
            },
            printOkFunc: async function () {
            },
        };

        const p = new comp.Print();
        p.printTemplate(printParams, methodsCb);
    }
    _doPrint({
        orderList,
        printDatas,
        progress,
        printType,
        choosedPrinter,
        templateInfo,
        templateDetailInfo,
        othersSet,
    }) {
        const self = this;
        const printParams = {
            printDatas: printDatas,
            tempId: templateInfo.Mode_ListShowId,
            ptype: printType,
            printBoxIsShow: 0,
            selectedPrinter: choosedPrinter,
            templateSet: templateDetailInfo,
            showPrinting: othersSet.showPrinting, // 显示出纸进度 暂时只针对快递单开放，开关设置走高级设置
        };
        // debugger
        //打印回调methods获取
        const methodsCb = {
            printSynFunc: function (curCount) {
                progress.updateProgress({curCount});
            },
            printOkFunc: async function () {
                //底单日志存储
                progress.showType('log');

                await printItem.saveFhdLog({
                    orderList,
                    templateInfo,
                });
                self.afterSavePrintLogHook();
                progress.remove();
            },
        };

        const defaultPrinter = (templateDetailInfo.ModeTempPrintcfg || {}).DefaultPrinter;
        const p = new comp.Print();
		// debugger
        if (choosedPrinter != defaultPrinter) {
            p.setDefaultPrinter(printType, choosedPrinter, templateInfo.Mode_ListShowId, function () {
                p.printTemplate(printParams, methodsCb);
            });
        } else {
            p.printTemplate(printParams, methodsCb);
        }
    }


    // --------------钩子函数的初始化--------------------
    // 钩子函数
    /**
     * 检测控件安装结果后执行的钩子函数
     * type:'cainiao'|'lodop'
     * result:'update|noInstall'
     */
    afterCheckComponentHook({ type, result }) {
        console.log('afterCheckComponentHook:' + type + result);
    }

    // 选择模板后执行的钩子函数
    afterChooseTempHook(tempId) {
        console.log(tempId);
    }

    // 选择发件人后执行的钩子函数
    afterChooseFjrHook(modifyDefaultFjr) {
        // console.log('afterChooseFjrHook');
        console.log(modifyDefaultFjr);
    }

    // 打印标记完成后执行的钩子函数
    afterSavePrintMarkHook() {
    }

    // 打印日志存储完成后执行的钩子函数
    afterSavePrintLogHook() {
    }
    // --------------钩子函数的初始化--------------------

}

export default PrintFhd;