//底单重打
import * as printItem from '../printItem';
import {getNoSupportLogPrint} from '../config';
import urlHelper from '../../common/urlhelp.js';
import  model  from '../../common/model.ts';
import { previewConfig, previewConfig_plat } from '../config';
import { printWithEncryptPackages, printWithPrintLog } from './crypto';
import { isDYEncryptTpl , isKsEncryptTpl,isTHHEncryptTpl } from '../utils/tpl';
import { platformConfig } from './platformConfig';

let setting = window.erpData?.advancedSetting?.groupPrintSetJsonString
let isMerge = setting?.openMergePrint == 2
class logReprint{
    /**
     *
     * @param {object} hook
     * @param {(printDatas: object) => void } hook.afterSavePrintLogHook
     */
    constructor( hook ){
        const {
            afterSavePrintLogHook,
            updateSenderAddress,
        } = hook;
        afterSavePrintLogHook && (this.afterSavePrintLogHook = afterSavePrintLogHook);
        updateSenderAddress && (this.updateSenderAddress = updateSenderAddress);
    }

    /**
     * 底单重打流程
     * @param {object} data 具体参数如下
     * @param {'kdd'|'fhd'} data.printType 'kdd'|'fhd' 目前支持支快递单底单重打
     * @param {Array} data.orderList   打印数据, orderList 数据结构参考 [淘宝交易返回结果](https://open.taobao.com/api.htm?cid=1&docId=47&docType=2) 返回结果
     * @param {object} data.fjrInfoMap 各店铺快递单发件人信息
     */
    async print(data){
        this.params = data;
        const {
            printType,
        } = data;
        let {
            orderList,
            fjrInfoMap,
        } = data;
        if(!orderList.length){
            Tatami.showFail('请勾选打印对象');
            return;
        }

         setting = window.erpData?.advancedSetting?.groupPrintSetJsonString
         isMerge = setting?.openMergePrint == 2
        // 获取底单重打的模板ExId 和 模板名称
        const {kdId,kdName} =  await this._getTempId(orderList);
        this.printTraceId = printItem.generatePrintInfo({printSource:'reprint',isMerge})

        // 获取模板
        const templateInfo = await this._getTempInfo({
            kdId,
            kdName,
        });

        // 不支持底单重打模板判断
        await this._isSupportPrint(templateInfo);

        // 控件校验
        const installResult = isMerge ? await printItem.checkPrintComponentByMerge(templateInfo.expressType) : await printItem.checkPrintComponent(templateInfo,printType);
        console.log(installResult);
        if( !installResult.isSuccess){
            return;
        }
        const printClient = installResult.type;
        console.assert(printClient,'未获取打印要使用的客户端');
        if( printClient === 'cainiao' ){
            comp.Print.Data.modeType = 1;
        }

        // 获取模板详情
        const templateDetailInfo = await printItem.getTempDetailInfo({
            printType,
            templateId:isMerge ? templateInfo.userTemplateId : templateInfo.Mode_ListShowId,
        });
        const wdSet = await printItem.getYunBranchSet(templateDetailInfo?.ModeListShow);
        const bindFjrId = templateDetailInfo.ModeListShow.bindFjrId ;

        // 获取发件人
        const {fjrInfo} = (await printItem.chooseFjr(fjrInfoMap,printType,bindFjrId,data));
        printItem.setTradeFjr({ orderList, fjrInfo, printType });

        //拼多多获取打印批次
        // 整合平台获取打印批次 0818 放心购厂家代打没有批次功能 0901
        if(comp.Print.Data.platform !== 'fxgfds') {
            orderList = await printItem.dealWithPrintBatch({
                orderList,
            });
        }
        orderList = await printWithPrintLog(orderList);
        // 获取打印需要的额外数据
        if([3, 5, 7, 8, 9, 10, 13,14,15,16,17].includes(templateDetailInfo.ModeListShow.KddType) ) {
            const otherPrintDataMap = await this._getOtherPrintData({orderList},templateDetailInfo.ModeListShow);

            // const decryptInfo = !isDYEncryptTpl(templateInfo.KddType, templateInfo.Exid);
            // orderList = await printWithEncryptPackages(orderList, templateInfo, { force: decryptInfo });

            let jdl_zmj = false;
			orderList = orderList.filter(item => otherPrintDataMap[item.ydNo])
            orderList.map( item => {
                const ydAttr = JSON.parse(item.ydAttr || '{}');
                let printDataInfo = '' ;
                if(templateDetailInfo.ModeListShow.KddType == 14){
                    printDataInfo =  otherPrintDataMap[item.ydNo] || '' ;
                }else{
                    printDataInfo =  JSON.parse(otherPrintDataMap[item.ydNo] || {}) ;
                }
                let PrintDatas = '', wayBillNos = '' , geturl = '',customUrl; //京东云打印控件数据处理
                if(templateDetailInfo.ModeListShow.KddType == 5){
                    if(printDataInfo == '' || !printDataInfo){
                        jdl_zmj = true;
                    }else{
                        const printPdfData = printDataInfo
                        const prePrintDatas = printPdfData.pullDataRespDTO && printPdfData.pullDataRespDTO.prePrintDatas;
                        geturl = printPdfData.standardTemplate && printPdfData.standardTemplate.standardTemplates[0];
                        customUrl = printPdfData.customUrl;
                        prePrintDatas && $.each(prePrintDatas, function (i, it) {
                            PrintDatas = it.perPrintData;
                            wayBillNos = it.wayBillNo;

                        });
                    }
                }
                Object.assign(item, {
                    cloudData: printDataInfo,
                    datoubi: printDataInfo.shortAddress,
                    jibaodi: printDataInfo.packageCenterName,
                    codPrice: ydAttr.dsje,
                    cplx: printDataInfo.proName,
                    route: printDataInfo.route,
                    sfwd: printDataInfo.shippingBranchName,
                    txm_jbm: printDataInfo.packageCenterCode,
                    jibaoma: printDataInfo.packageCenterCode,
                    ddwd: printDataInfo.consigneeBranchName,
                    packageYdId: printDataInfo.packageYdId,
                    hkzj: ydAttr.hkzj || '',
                    goods_title: ydAttr.goods_title || '',

                    zy_sfwd: printDataInfo.codingMapping,
                    zy_sfwdbm: printDataInfo.consigneeBranchCode,
                    zy_mdwd: printDataInfo.sourceTransferCode,
                    zy_mdwdbm: printDataInfo.shippingBranchCode,
                    txm_number_package: printDataInfo.packageYdId,
                    fhdexnumber_package: printDataInfo.packageYdId,
                    sortCode: printDataInfo.sortCode,
                    encrypted:true,
                    printPdfData:printDataInfo.printData, //京东云一联单加密数据
                    jdprintData:PrintDatas,
                    geturl:geturl,
                    customUrl:customUrl,
                    wayBillNo:wayBillNos,

                });
            });
            if(jdl_zmj){
                Tatami.showFail('京东云一联单子母件不支持底单重打');
                return;
            }
        }
		if (!orderList.length) {
			window.Tatami.showFail('无可打印的订单，请重新勾选订单');
			return
		}
        // 修改地址后打印，修改其encryptedData
        if(this.updateSenderAddress) {
            const result = await this.updateSenderAddress(data, {
                fjrInfo,
                templateInfo,
            });
            if(result) {
                orderList = result.params.dataList;
                // 修改地址只能单个修改，所以直接改下标为0的数据即可
                if(orderList[0] && orderList[0].cloudData) {
                    orderList[0].cloudData = JSON.parse(result.params.printData);
                }
            }

        }

        // 本次打印是否向用户展示预览按钮
        const isHasPreview = previewConfig(printClient) && previewConfig_plat(); // 类型支持预览且平台支持预览

        // 选择打印机
        const { choosedPrinter, printNums, ...choosedPrinterSet } = await printItem.choosePrinter({
            orderList,
            defaultPrinter:((templateDetailInfo || {}).ModeTempPrintcfg || {}).DefaultPrinter,
            printType,
            templateInfo,
            templateDetailInfo,
            orderNum: orderList.length,
            isCloudPrint:true,
			isHasPreview: true,
            isHasAutoShip:false, //暂时写死不显示自动发货
            printClient,
            printerList: installResult.printers,
        });


        // /**
        //  * 快递单号获取逻辑,重打提示框，多单号选择框，子母件选择框
        //  * @param {Object} orderList
        //  * @param {Object} templateInfo
        //  * @param {Object} templateDetailInfo
        //  * @param {}
        //  * @param {}
        //  * @param {}
        //  * @returns {boolean} isMustUseNewWaybillCode 是否必须用新单号
        //  * @returns {number} waybillCodeCount 申请单号的总数量
        //  * @returns {Array} oldWaybillCodeList 要使用的旧单号
        //  * @returns {number} sfZDQuantity 子母件数量
        //  */
        // const infoAboutWaybillCodeNums = await this._getInfoAboutWaybillCodeNums({
        //     orderList,
        //     templateInfo,
        //     templateDetailInfo,
        //     printType,
        //     canUseNewWaybillCodeSet: false,
        //     canChooseWaybillCodeNumSet: false,
        // });

        // 本次行为是否是预览
        const isPreview = choosedPrinterSet.isPreview;
        let userId = comp.Print.Data.platform === 'erp' ? data.orderList[0].sellerId : data.orderList[0].userId;
        // let userId = data.orderList[0].userId;
        const curUserId = comp.Print.Data.userInfo.userId || ''; //当前登陆的店铺id
        if(fjrInfoMap.kddFjrMap.isUseCommon){ // 统一发件人开启
            userId = curUserId
        }

        // 打印预览部分：直到在预览的面单上点击「打印」按钮，才离开本逻辑
        if(isPreview){
            const temporaryOrderList = printItem.deepClone(orderList);
            try{
                await printItem.previewTemplate({
                    isPreview,
                    printType,
                    orderList:temporaryOrderList,
                    total: printNums * orderList.length * ( templateInfo.isConcatFhd ? 2 : 1),
                    printer:choosedPrinter,
                    templateInfo: templateDetailInfo.ModeListShow,
                    templateDetailInfo,
                    orderNum: orderList.length,
                    elecNoCount: 1,
                    kddFjrMap: fjrInfo,
                    kddFjr: fjrInfo.sender[userId]?.info || fjrInfo.sender[curUserId]?.info,
                });

            }catch(err){
                console.log(err);
            }
        }

        //打印进度弹窗
        const progress = new printItem.getPrintProgress({
            showType:'progress',
            totalCount:orderList.length,
        });
        progress.showType('progress');

        // 美团需要鉴权
        orderList = await printItem.getAuthPrintDevice(templateInfo,orderList);
        //组装打印数据
        const printDatas = await printItem.getFormatPrintData({
            orderList,
            templateInfo:templateDetailInfo.ModeListShow,
            modeInfo: templateDetailInfo,
            kddFjrMap: fjrInfo,
            kddFjr: fjrInfo.sender[userId]?.info || fjrInfo.sender[curUserId]?.info,
            printType: 'kdd',
            isReprint: true,
        });

        //打印
        this._doPrint({
            printDatas,
            progress,
            printType,
            choosedPrinter,
            templateInfo: templateDetailInfo.ModeListShow,
            templateDetailInfo,
            wdSet,
            orderList,
            fjrInfo:fjrInfo.sender[userId]?.info || fjrInfo.sender[curUserId]?.info
        });
    }

    // 封装：通过单号选择框获取单号的相关信息
    async _getInfoAboutWaybillCodeNums({
        orderList,
        templateInfo,
        templateDetailInfo,
        printType,
        canUseNewWaybillCodeSet,
        canChooseWaybillCodeNumSet,
    }){
        // 有没有打印过快递单
        const hasPrinted = await printItem.checkOrderHasPrint(orderList, 'kdd');

        // 判断本次打印是否是子母件
        let isZiMuJian = false;
        // 判断本次打印能不能用原单号打印
        let canUseOldNo = false;

        isZiMuJian  = await printItem.checkIsZiMuJian(templateDetailInfo);
        canUseOldNo = await printItem.checkIsUseOldNo({templateInfo, isZiMuJian, orderLength:orderList.length});

        // 单号数量和使用新/旧单号信息
        let chooseWaybillInfos = {};


        /**
         * 判断是否弹出已打印弹窗并执行弹框；如果已打印，允许用户选择使用原单号还是新单号打印，新单号可以选择新单号个数
         * 会返回一个对象，包含三个值：
         * isMustUseNewWaybillCode 是否是用新单号 Boolean
         * oldWaybillCodeList 旧单号列表 Array 缺省为空数组
         * waybillCodeCount 本次需要的单号数量 Number
         */
        chooseWaybillInfos = await printItem.surePrintAgain({
            printType,
            templateInfo,
            hasPrinted,
            canUseOldNo,
            canUseNewWaybillCodeSet,
            canChooseWaybillCodeNumSet,
            orderList,
        }) || chooseWaybillInfos;

        // 如果订单没打印过，并且高级设置中允许选择多个单号 || 打印的订单数量超过两个  => 会有多单号选择框
        // 子母件不会有多单号选择框
        // 关于「是否是用新单号」如果有重打提示框，则由重打提示框获取该值，如果未弹出重打提示框，本逻辑会把「是否是用新单号」设置为 true
        chooseWaybillInfos = await printItem.chooseWaybillCodeNum({
            hasPrinted,
            canChooseWaybillCodeNumSet,
            templateInfo,
            isZiMuJian,
            orderListLength: orderList.length,
            isMustUseNewWaybillCode:chooseWaybillInfos.isMustUseNewWaybillCode,//来自重打提示框的「是否申请新单号」
        }) || chooseWaybillInfos;

        // 获取子母件单号数量等相关信息，菜鸟子母件和非菜鸟子母件
        let zmjRelativeInfos = {};
        if(isZiMuJian){
            zmjRelativeInfos = await printItem.getZmjRelativeInfos(templateInfo);
        }

        return {
            ...zmjRelativeInfos,
            isMustUseNewWaybillCode:chooseWaybillInfos.isMustUseNewWaybillCode,
            waybillCodeCount: chooseWaybillInfos.waybillCodeCount,
            oldWaybillCodeList:chooseWaybillInfos.oldWaybillCodeList,
        };
    }


    //--私有方法---//

    _doPrint({
        printDatas,
        progress,
        printType,
        choosedPrinter,
        templateInfo,
        templateDetailInfo,
        wdSet,
        orderList,
        fjrInfo
    }){
        const self = this;
        const printParams = {
            printDatas:printDatas,
            tempId: templateInfo.Mode_ListShowId,
            ptype: printType,
            printBoxIsShow: 0,
            selectedPrinter: choosedPrinter,
            templateSet: templateDetailInfo,
            isReprint: true,
            wdSet:wdSet,
            speedPrint:true
        };
        //打印回调methods获取
        const methodsCb = {
            printSynFunc: function(curCount){
                progress.updateProgress({curCount});
            },
            printOkFunc: async function(){
                // self.afterSavePrintLogHook(printDatas);明天来改
                self.afterSavePrintLogHook(orderList,fjrInfo);
                setTimeout(function(){
                    progress.remove();
                    window.printSourceInfo = null;
                },1000);
            },
        };
        const defaultPrinter = (templateDetailInfo.ModeTempPrintcfg || {}).DefaultPrinter;
        const p = new comp.Print();
        if (choosedPrinter != defaultPrinter) {
            p.setDefaultPrinter(printType, choosedPrinter, templateInfo.Mode_ListShowId, function(){
                p.printTemplate(printParams, methodsCb);
            });
        } else {
            console.log('printParams',printParams);
            p.printTemplate(printParams, methodsCb);
        }
    }

    _getTempId(orderList){
        let kdId,kdName;
        for (let index = 0; index < orderList.length; index++) {
            const item = orderList[index];
            if(index === 0){
                kdId = item.kdId;
                kdName = item.kdName;
            }else if(kdId != item.kdId || kdName != item.kdName){
                kdId = null;
                kdName = null;
                break;
            }
        }
        if( !kdId ){
            Tatami.showFail('勾选订单中包含多个快递模版，无法批量重打');
            return Promise.reject();
        }else{
            return Promise.resolve({
                kdId,
                kdName,
            });
        }
    }

    _isSupportPrint(temp){
        const configs = getNoSupportLogPrint();
        let isSupport = true;
        let msg = '';
        for (let index = 0; index < configs.length; index++) {
            const item = configs[index];
            if(isMerge && temp.exCode == item.ExCode && temp.expressType == item.KddType){
                isSupport = false;
                msg = temp.msg;
                break;
            }else if( temp.ExCode == item.ExCode && temp.KddType == item.KddType ){
                isSupport = false;
                msg = temp.msg;
                break;
            }
        }
        if(!isSupport){
            Tatami.showFail( msg );
            return Promise.reject();
        }else{
            return Promise.resolve();
        }
    }

    async _getTempInfo ({kdId, kdName}) {
        let isSetTemp = false
            , isDiffName = false
            , tempList
            , chooseTemp;
        tempList = await printItem.getKddTempList();
        if(isMerge){
            for (let index = 0; index < tempList.length; index++) {
                const v = tempList[index];
                let temp = v.userTemplateList?.find(it => it.exId == kdId)
                if(temp && temp.exName == kdName){
                    isSetTemp = true;
                    chooseTemp = temp;
                    break;
                }else if(temp){
                    isDiffName = true;
                }
            }
        }else{
            for (let index = 0; index < tempList.length; index++) {
                const v = tempList[index];
                if (v.Exid == kdId && v.ExcodeName == kdName) {
                    isSetTemp = true;
                    chooseTemp = v;
                    break;
                }else if(v.Exid == kdId && v.ExcodeName != kdName){
                    isDiffName = true;
                }
            }
        }

        if (!isSetTemp) {
            model({
                type:'confirm',
                title: '请先添加' + kdName + '模版!',
                width: 500,
                cancelHide: true,
                content: `<div class="note_content"><p>原模版不存在${ isDiffName ? '(模板的名称需要一样)' : ''}，请复制订单号到批打页面打印。</p></div>`,
            });
            return Promise.reject();
        }else{
            return Promise.resolve( chooseTemp );
        }
    }

    //获取其他打印数据
    async _getOtherPrintData({orderList},templateInfo){
        const params = this._getBgPrintDataParams(orderList);
        let listParam = {};
        if(comp.Print.Data.platform == 'erp'){
            listParam = {
                jsonParam: JSON.stringify(params),
            };
        }else{
            listParam = {
                printDataParam: JSON.stringify(params),
            };
        }
        const [orderInfo] = orderList;

        if(['fxg', 'pdd','ksshop','ksshopg', 'ksshop-pre', 'mtm', 'ks'].includes(platformConfig.platform)) {
            Object.assign(listParam, {
                elecNos: orderInfo.ydNo,
                userId: orderInfo.userId,
                exid: orderInfo.exid || orderInfo.exId,
                kddType: orderInfo.kddtype || orderInfo.kddType,
            });
        }

        // if(['yz'].includes(platformConfig.platform)){
        //     Object.assign(listParam, {
        //         // 保留加密的谜文
        //         mobileEncrypt:obj.s_encode_mobile , //  收件人手机号加密串
        //         receiverNameEncrypt: obj.s_encode_name , //  收件人加密串
        //         receiverAddressEncrypt: obj.s_encode_address , //  收件人详细地址加密串
        //     });
        // }

        const isEncryptTpl = ()=>{
            return (
                isDYEncryptTpl(listParam.kddType, templateInfo.customTop) ||
                isKsEncryptTpl(listParam.kddType, listParam.exid) ||
                isTHHEncryptTpl(listParam.kddType, templateInfo.customTop)
            );
        };

        const results = await urlHelper.threadRequest({
            list: [listParam],
            threadCount:4,
            apiUrl: comp.Print.Data.platform === 'erp' ? 'PRINT_GET_BG_PRINTDATA_ERP' : (isEncryptTpl() ? 'PRINT_GET_BG_PRINTDATA' : 'GET_BG_PRINTDATA'),
        });
        if( !results.isSuccess ){
            const failYdNo = [];
            results.errorResults.map( ()=>{
                // 拼多多取的是elecNo字段
                failYdNo.push(params[0].ydNo || params[0].elecNo);
            });
            Tatami.showFail(`检测如下运单号数据异常，建议去订单页面打印该订单${ failYdNo }`);
            return Promise.reject();
        }

        const modifyYdNo = [];
        let printDatas = {};

        results.successResults.map( item => {
            const data = item.resultJson.data;
            printDatas = Object.assign(printDatas,data)
        });

        if( modifyYdNo.length ){
            Tatami.showFail(`检测如下运单号收件信息已修改，建议去订单页面打印该订单${ modifyYdNo }`);
            return Promise.reject();
        }
        return Promise.resolve( printDatas );
    }

    _getBgPrintDataParams( orderList ){
        const mallType = window.sessionStorage.getItem('mallType');
        return orderList.map( item => {
            let data =  {
                elecNo:item.ydNo,
                userId:item.userId, //TODO
                kdCode:item.kdCode,
                // kddType: item.type,
                receiverProvince:item.receiverProvince,
                receiverCity: item.receiverCity,
                receiverCounty:item.receiverCounty,
                receiverAddress:item.receiverAddress,
                receiverName:item.receiverName,
                receiverTel:item.tel,
                receiverMobile: mallType == 'BBW' ? item.encodeReceiverMobile : (item.mobile || item.tel), //贝贝网底单重打手机号传加密后的给后端
                oaid: item.caid || '',

                // 加解密需要覆盖的参数
                ...(item.bgParams || {}),
            };

            if(comp.Print.Data.platform == 'erp'){
                data = {
                    ...data,

                    sellerId:item.sellerId,

                    platform:item.platform,

                    exId:item.kdId,

                    tids:item.tids,

                    encodeTid:item.encodeTid,

                    ydNo:item.exNumber,

                    receiverMobile:item.receiverMobile,
                };
            }

            return data;
        });
    }
    // --------------钩子函数的初始化--------------------
    // 打印日志存储完成后执行的钩子函数
    afterSavePrintLogHook(){
        console.log('afterSavePrintLogHook',arguments);
    }
    // --------------钩子函数的初始化--------------------
}

export default logReprint;
