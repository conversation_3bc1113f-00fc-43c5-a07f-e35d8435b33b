import * as printItem from '../printItem';
import model from './../../common/model.ts';
import { previewConfig, previewConfig_plat } from '../config';
import { printWithEncryptPackages, printFhdEncrypt } from './crypto';
import { platformConfig } from './platformConfig';
import { get, post } from './../../common/http';
import urlHelper from '../../common/urlhelp.js';
import { printWithEncryptOrder } from './printEncrypt'
import concatPrintDpd from './concatPrintDpd';
import {ORDER_REFLUX_TYPE,platByKddTypeFnc} from '../../common/constant'
class PrintKdd {

    constructor(hook) {
        const {
            afterCheckComponentHook,
            afterChooseTempGroupHook,
            afterChooseFjrHook,
            afterGetWaybillCodeHook,
            afterSavePrintMarkHook,
            afterSavePrintLogHook,
            afterBranch<PERSON>hangeHook,
            httpWhiteListHook,
            afterContinuePrintHook,
            notifyPrintHook,
            afterGetElecErrorHook,
            afterAutoAddPrintNum,
            afterShipFirstGetWaybillCodeHook
        } = hook || {};
        afterCheckComponentHook && (this.afterCheckComponentHook = afterCheckComponentHook);
        afterChooseTempGroupHook && (this.afterChooseTempGroupHook = afterChooseTempGroupHook);
        afterChooseFjrHook && (this.afterChooseFjrHook = afterChooseFjrHook);
        afterGetWaybillCodeHook && (this.afterGetWaybillCodeHook = afterGetWaybillCodeHook);
        afterShipFirstGetWaybillCodeHook && (this.afterShipFirstGetWaybillCodeHook = afterShipFirstGetWaybillCodeHook);
        afterSavePrintMarkHook && (this.afterSavePrintMarkHook = afterSavePrintMarkHook);
        afterSavePrintLogHook && (this.afterSavePrintLogHook = afterSavePrintLogHook);
        afterBranchChangeHook && (this.afterBranchChangeHook = afterBranchChangeHook);
        httpWhiteListHook && (this.httpWhiteListHook = httpWhiteListHook);//把用户加入 http 白名单的钩子（加入白名单后，用户下次进入系统就直接是 http 环境了）
        afterContinuePrintHook && (this.afterContinuePrintHook = afterContinuePrintHook);
        notifyPrintHook && (this.notifyPrintHook = notifyPrintHook);
        afterGetElecErrorHook && (this.afterGetElecErrorHook = afterGetElecErrorHook);
        afterAutoAddPrintNum && (this.afterAutoAddPrintNum = afterAutoAddPrintNum);
        this.timer = window.timer;
        this.printTraceId = ''
    }



    /**
     * 开始打印
     * @param {Object} data
     * 模板信息，可以不传
     * 订单数据
     * 用户信息（考虑初始化的时候传，这时候就可以不传了）
     * 打印
     */
    async print(data) {
        console.info('合并批打快递单 传给打印中心的数据', data);

        // NOTE 生成打印的traceId
        this.printTraceId = printItem.generatePrintInfo({ printSource: 'merge', isMerge: true })

        this.timer.start('打印前置处理')

        const userInfo = comp.Print.Data.userInfo;

        const printType = 'kdd';

        const documentNoInfo = {
            allDocumentCount : 0,
            startDocumentIdx : 0
        };
        // 重置一下全局打印序号计数
        comp.Print.Data.totalDocumentCount = 0
        comp.Print.Data.firstDocumentNumber = 1
        let isSingleTemp = false

        comp.Print.Data.globalPrintTraceId = this.printTraceId
        // 融合打印设置
        //openMergePrint = 2 开启
        //printOrderByOrder = 1 订单顺序  2 控件顺序
        //printOrderByHandOrder = 2 手动匹配  1 自动匹配单号最多的  已移除
        //handOrderMatchPlatform  手工单匹配平台  已移除
        // orderMatchSetting 订单绑定平台模板 [	{platform: 'hand',bindControlType: 3}]
        //controlsSortSet 控件顺序

        const advancedSetting = window.erpData?.advancedSetting

        const setting = advancedSetting?.groupPrintSetJsonString

        // 回流订单是否使用菜鸟打印还是平台打印
        const isUseCainiao = advancedSetting?.refluxOrderPrintSet == '1'

        // 是否开启交替打印吊牌
        let isConcatXbq = advancedSetting?.showGoodsTag == '2'

        let {
            templateInfo: groupTemplateInfo,//模板组信息
            orderList, // 订单
            printOp,
            // ,shopNewPrintNum
            fjrInfoMap, //发件人信息
            fhdFjrInfoMap, //发货单发件人
            printStyle,
            platformId, // 业务方传来的平台id
            firstSendMode,
            scanFjrInfo, // 爆款扫描打印
            choosedPrinter: scanChoosedPrinter,
            ...othersSet
        } = data;

		printItem.uploadPrintLog('merge', {
			orderList: orderList,
			fjrInfoMap: fjrInfoMap,
			printTraceId: this.printTraceId,
			templateInfo: groupTemplateInfo,
		})
        // 如果没有传入模板信息，则弹出模板选择框

        // 吊牌交替打印
        let systemSetting = window?.erpData?.systemSetting || {}
        let xbqTempisInvalid = false
        let printTagSet = JSON.parse(systemSetting?.printTagSet || '{}');
        comp.Print.Data.printTagSet = printTagSet

        if (!groupTemplateInfo.userTemplateList) {
            // eslint-disable-next-line require-atomic-updates
            groupTemplateInfo = await printItem.chooseTemplateGroupDialog();
            if (!groupTemplateInfo) {
                return false;
            }
            // 提供出一个钩子，供外部选择是否要继续打印
            const result = this.afterChooseTempGroupHook(groupTemplateInfo.id, groupTemplateInfo);
            if (result && result.isBreak === true) {
                return;
            }
        }
        let templateInfoData = comp.Print.Data.kddTemplates.find(o => o.id == groupTemplateInfo.id)
        let defaultTemp = null
        if (setting.printOrderByHandOrder === 1) {
            let maxQuantity = !templateInfoData?.wd.length ? null : Math.max.apply(Math, templateInfoData.wd.map(item => { return Number(item.quantity) }))
            defaultTemp = !maxQuantity ? templateInfoData.userTemplateList[0].expressType : templateInfoData.userTemplateList.find(o => o.wd?.quantity == maxQuantity)?.expressType
        } else {
            defaultTemp = setting.handOrderMatchPlatform
        }
        const platByKddType = platByKddTypeFnc({setting,groupInfo:templateInfoData})



        this.data = {
            printOp,
            printType,
        };
        const needUseTemp = []
        orderList.forEach(o => {
            // 给订单加上匹配的模板类型
            let kddType = null
            if (o.hlPlatformType && !['hl-other', 'hl-tx'].includes(o.hlPlatformType) && (o.hlEncryptOrder || !isUseCainiao)) {
                let hlType = o.hlPlatformType.replace('hl-', '')
                if (['sph'].includes(hlType)) { // 视频号小红书聚合模板组暂未接入平台面单
                    kddType = platByKddType[o.platform]
                } else {
                    kddType = ORDER_REFLUX_TYPE[hlType]
					// 新版小红书兼容，新版旧版模板都可匹配

					if(hlType === 'xhs') {
						let xhsTemp = groupTemplateInfo.userTemplateList.find(it=>it.expressType === kddType)
						kddType = !!xhsTemp ? 13 : 16
					}
                }
            } else {
				const isDyEncodeHand = o.platform === 'fxg' && o.source === 'HAND' && o.tradeEncodeType == 1
				// 抖音明文手工单需要改写一下绑定，跟手工单保持一致`
				isDyEncodeHand ? (kddType = platByKddType['hand']) : (kddType = platByKddType[o.platform])
            }
            o.kddType = kddType
            // 判断是否已有该类型模板
            if (!needUseTemp.includes(kddType)) {
                needUseTemp.push(kddType)
            }
        })
        //判断当前打印订单是否是单平台，如果是单平台需要走极速模式
        isSingleTemp = needUseTemp.length === 1
        const printTemplateInfo = comp.Print.Data.kddTemplates.find( o => o.id==templateInfoData.id)
        const templateInfo = Object.assign({},templateInfoData,printTemplateInfo)
        templateInfo.userTemplateList = needUseTemp.map(o =>{
            let tempInfo = templateInfo.userTemplateList.find(it=>it.expressType === o)
            return tempInfo
        })
        let tempArr = templateInfo.userTemplateList
        let originalOrderList = orderList; //源订单数据
        comp.Print.Data.fjrInfoMap = fjrInfoMap;
        comp.Print.Data.platformId = platformId;
        // 获取传入的其他配置
        let {
            canUseNewWaybillCodeSet, //可以使用新单号
            canChooseWaybillCodeNumSet, // 可以选择单号数量
            choosePrinterNotiSet, // 可以弹出选择打印机
            displayPrintNumSet, // 可以选择打印机打印份数
            // openPrintNotifySet, // 出纸进度
            showCombinationInfo
        } = othersSet;
        comp.Print.Data.showCombinationInfo = showCombinationInfo
        // 先发货默认一个单号
        if (firstSendMode) {
            canChooseWaybillCodeNumSet = false
        }
        console.info('模板已选中', templateInfo);
        // 检查一下模板有没有禁止打印,有没有无需物流发货，有没有https下韵达网点打印
        // TODO  await printItem.checkTemplate(templateInfo, this.httpWhiteListHook, data);
        console.info('模板是否可打印检查');
        // 获取模板详情
        const templateDetailInfo = {}
        for (let i = 0; i < tempArr.length; i++) {
            templateDetailInfo[tempArr[i].expressType] = await printItem.getTempDetailInfo({
                printType,
                templateId: tempArr[i].userTemplateId,
            });
        }

        console.info('获取模板详情', templateDetailInfo);
        // if(needUseTemp.includes(platByKddType['sph']) && platByKddType['sph'] != 14){
        //     orderList =  await printItem.checkoutSphIsDecrypted({
        //         temp:templateDetailInfo[platByKddType['sph']]?.ModeListShow,
        //         orderList,
        //         afterContinuePrintHook:this.afterContinuePrintHook
        //     })
        // }
        // 回流订单暂不处理判断是否手动解密的逻辑，后续完整接入再处理
        // if(needUseTemp.includes(platByKddType['sph'])){
        //     orderList =  await printItem.checkoutSphIsDecrypted({
        //         temp:templateDetailInfo[platByKddType['sph']]?.ModeListShow,
        //         orderList,
        //         afterContinuePrintHook:this.afterContinuePrintHook
        //     })
        // }
        // 检查吊牌交替打印模板是否存在
        if (isConcatXbq) {
            if (printTagSet?.tagTemplateSwitch == '1') {
                let temps = comp.Print.Data.bqTemplateListdpd.ModeListShows.find((item) => {
                    return printTagSet?.tagTemplate.Mode_ListShowId === item.Mode_ListShowId
                })
                xbqTempisInvalid = !temps
            } else {
                xbqTempisInvalid = true
            }

        }
        let isConcatFhd = false;
        // 判断是否需要发货单快递单交替打印
        // if (templateInfo.KddType != '1' && templateDetailInfo.ModeTempPrintcfg.IsConcatFhd) {
        //     const fhdTemplateList = new comp.Print().getFhdTemplateList(false);
        //     comp.Print.Data.hasNoRM_common = !(fhdTemplateList.ModeListShows || []).some(item => item.Exid == 666);
        //     comp.Print.Data.hasNoRM_yilian = !(fhdTemplateList.ModeListShows || []).some(item => item.Exid == 670);
        //     // const isYilianTemp = comp.base.isYilianTemp(templateInfo.styleId);
        //     const isYilianTemp = templateDetailInfo?.ModeList?.WidthPaper === 760;
        //     const tempType = isYilianTemp ? 'yilian' : 'common';
        //     // 1.本模板是一联模板，并且一联热敏发货单存在
        //     // 2.本模板是普通模板，并且普通热敏发货单存在（目前非一联模板就是普通模板，2019.9）
        //     if ((isYilianTemp && !comp.Print.Data.hasNoRM_yilian) || (!isYilianTemp && !comp.Print.Data.hasNoRM_common)) {
        //         isConcatFhd = true;
        //         new comp.Print().setIsConcatFhd(tempType, true);
        //     }
        // }
        // NOTE 先发货不需要控件校验
        let installResult = null
        if (!firstSendMode) {
			comp.Print.Data.isNewXhsPrint = needUseTemp.includes(16)
            installResult = await printItem.checkPrintComponentByKdzs();
            // 抖音交替打印需要lodop校验
            // if (templateInfo.KddType == '8' && isConcatFhd) {
            //     installResult = printItem.checkLodopProgress();
            // }
            // 新版聚合控件不需要单独校验控件状态，直接通过聚合控件校验链接状态
            // comp.Print.Data.newPrintMode
            if(comp.Print.Data.newPrintMode){
				// 如果使用有赞的，替换为校验菜鸟控件，如果同时存在菜鸟有赞，保留菜鸟的
				let checkNeedUseTemp = [...needUseTemp]
				let yzIndex = needUseTemp.indexOf(15)
				if(yzIndex>-1){
					checkNeedUseTemp.includes(3) ? checkNeedUseTemp.splice(yzIndex,1) : checkNeedUseTemp.splice(yzIndex,1,3)
				}
                let connection = await printItem.checkKdzsConnetStatus(checkNeedUseTemp)
                if (!connection.isSuccess) {
                    this.timer.stop('打印前置处理', {
                        type: 'mergePrint',
                        printTraceId: this.printTraceId
                    })
                    return
                }
            }else{
                let allInstallResult = null
                for (let i = 0; i < templateInfo.userTemplateList.length; i++) {
                    allInstallResult = await printItem.checkPrintComponentByMerge(templateInfo.userTemplateList[i].expressType)
                    if (!allInstallResult.isSuccess) {
                        this.timer.stop('打印前置处理', {
                            type: 'mergePrint',
                            printTraceId: this.printTraceId
                        })
                        return
                    }
                }
            }

            console.info(templateInfo);
            // 检查打印组件：用哪个，并检查准备好了没有
            console.info('控件检测结果', installResult);
            this.afterCheckComponentHook(installResult);
            if (!installResult.isSuccess) {
                this.timer.stop('打印前置处理', {
                    type: 'mergePrint',
                    printTraceId: this.printTraceId
                })
                return;
            }
        }
        if (isConcatXbq && !xbqTempisInvalid) {
            let lodopInstallResult = null
            lodopInstallResult = printItem.checkLodopProgress();
            if (!lodopInstallResult.isSuccess) {
                this.timer.stop('打印前置处理', {
                    type: 'mergePrint',
                    printTraceId: this.printTraceId
                })
                return;
            }
        }

        const printClient = firstSendMode ? '' : installResult.type;
        console.assert(printClient, '未获取打印要使用的客户端');
        // 第二次检查一下模板能否打印 是否有退款
        // erp平台检查能否打印
        await printItem.checkCanPrint({
            printType,
            templateInfo,
            orderList,
            printOp,
            firstSendMode
        });

        // 拼多多有绑定发件人的功能
        const bindFjrId = templateInfoData.bindFjrId || undefined;
        // 获取本次打印使用的的发件人
        // TODO 融合模式暂时不需要模板发件人
        let fjrInfo = null
        if (!scanFjrInfo) {
            const { fjrInfo: kddFjrInfo, modifyDefaultFjr } = await printItem.mergePrintChooseFjr(fjrInfoMap, printType, bindFjrId, data);
            modifyDefaultFjr.length && this.afterChooseFjrHook(modifyDefaultFjr);
            fjrInfo = kddFjrInfo
        } else {
            fjrInfo = scanFjrInfo
        }
        printItem.setTradeFjr({ orderList, fjrInfo, printType });
        let fhdFjrInfo;
        if (isConcatFhd) {
            ({ fjrInfo: fhdFjrInfo } = await printItem.chooseFjr(fhdFjrInfoMap, 'fhd'));
        }

        // 检查一下发件人省市区（大头笔）有没有
        await printItem.checkFjrAddress(fjrInfo);


        // const otherPrintInfo = {}
        this.timer.stop('打印前置处理', {
            type: 'mergePrint',
            printTraceId: this.printTraceId
        })
        /**
         * 快递单号获取逻辑,重打提示框，多单号选择框，子母件选择框
         * @param {Object} orderList
         * @param {Object} templateInfo
         * @param {Object} templateDetailInfo
         * @returns {boolean} isMustUseNewWaybillCode 是否必须用新单号
         * @returns {number} waybillCodeCount 申请单号的总数量
         * @returns {Array} oldWaybillCodeList 要使用的旧单号
         * @returns {number} sfZDQuantity 子母件数量
         */
        // this.timer.start('选择单号数量')
        const infoAboutWaybillCodeNums = await this._getInfoAboutWaybillCodeNumsByMergePrint({
            orderList,
            templateInfo,
            templateDetailInfo,
            platByKddType,
            printType,
            canUseNewWaybillCodeSet,
            canChooseWaybillCodeNumSet,
            firstSendMode
        });
        /**
      * 获取打印时必要的数据信息
      * 例如极兔（龙邦）模板需要获取商品信息
      * @param templateInfo
      * @returns {Object}
      */
        const otherPrintInfo = {}
        for (let k in templateDetailInfo) {
            otherPrintInfo[k] = await printItem.getOtherPrintInfo(templateDetailInfo[k], infoAboutWaybillCodeNums.goodsDescription);
        }
        console.assert(otherPrintInfo, 'otherPrintInfo');

        // this.timer.stop('选择单号数量',{
        //     type:'mergePrint'
        // })
        this.timer.start('获取网点与解密')
        /** 获取网点信息
         * @param {object} templateInfo
         */
        let wdSet = {}  //模板网点集合，kddtype:obj
        let errTip = '' //网点错误提示
        let errWd = [] //错误网点模板类型
        for (let k = 0; k < tempArr.length; k++) {
            wdSet[tempArr[k].expressType] = await printItem.getYunBranchSet(templateDetailInfo[tempArr[k].expressType]?.ModeListShow).catch(err => {
                errTip += tempArr[k].exName + '：' + err + '<br/>'
                errWd.push(tempArr[k].expressType)
            });
        }

        if (!!errTip) {
            await new Promise((resolve) => {
                const modelConfig = {
                    type: 'confirm',
                    content: errTip + '<br/><br/>是否过滤模板类型订单继续打印？',
                    width: 426,
                    height: 200,
                    okName: '是，继续打印',
                    cancelName: '取消',
                    okCb: () => {
                        //过滤打印数据
                        let successOrder = []
                        let errorOrder = []

                        orderList.forEach((item) => {
                            if (errWd.includes(item.kddType)) {
                                errorOrder.push(item)
                            } else {
                                successOrder.push(item)
                            }

                        })
                        const errorOrderIdList = errorOrder.map(item => item.togetherId);
                        orderList = [...successOrder];
                        this.afterContinuePrintHook(errorOrderIdList)
                        if (!orderList.length) {
                            Tatami.showNoti('请选择订单重新打印');
                            return false
                        }
                        resolve();
                    },
                    cancelCb: () => {
                        return false;
                    },
                };
                model(modelConfig);
            });
        }
        console.assert(wdSet, '未获取到网点信息');
        console.log(data);
        orderList = await printWithEncryptOrder({ orderList, afterContinuePrintHook: this.afterContinuePrintHook, needUseTemp })
        if (!orderList.length) return
        // 暂不需要解密
        // if (templateInfo.KddType === 8 && !!data.fhdDecodeSwitch && isConcatFhd) { //
        //     orderList = await printFhdEncrypt(orderList, data.fhdDecodeSwitch);
        // } else {
        //     orderList = await printWithEncryptPackages(orderList, templateInfo);
        // }
        this.timer.stop('获取网点与解密', {
            type: 'mergePrint',
            printTraceId: this.printTraceId
        })
        if (firstSendMode) {
            this.timer.start('先发货弹窗')
            await printItem._getWaybillConfirmDialog({
                isHasAutoShip: printOp == 1, //暂时写死不显示自动发货
                kdName: templateInfo.groupName,
                orderNum: orderList.length,
                templateInfo,
                templateDetailInfo,
                isMerge: true
            })
            this.timer.stop('先发货弹窗', {
                type: 'mergePrint',
                printTraceId: this.printTraceId
            })
        }
        this.timer.start('申请单号')

        //获取单号流程，获取后会把数据放入 orderList 中
        // eslint-disable-next-line require-atomic-updates
		orderList = await printItem.getWaybillCodeNew({
            orderList,
            temp: templateInfo,
			originTempGroup: groupTemplateInfo,
            elecNoCount: infoAboutWaybillCodeNums.waybillCodeCount, // 单号数量
            sfZDQuantity: infoAboutWaybillCodeNums.sonElecNoCount, // 子母件生成个数(不仅仅是顺丰，只是后端字段名是这个而已)
            sonElecNoCount: infoAboutWaybillCodeNums.sonElecNoCount, //子单号数量（菜鸟快运子母件）
            oldYdNos: infoAboutWaybillCodeNums.oldWaybillCodeList, //要使用的旧单号
            printOp, // [操作来源  0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单]
            wdSet, // [网点地址选择对象]
            isUseOrderFjr: false, //是否使用订单里面设置的发件人信息：发件人信息在每一笔订单数据中，无需传递kddFjr 或者 fhdFjr
            fjrInfo, // 发件人信息，兼容单店铺和多店铺
            isNewEleNo: infoAboutWaybillCodeNums.isMustUseNewWaybillCode, //是否必须使用新单号
            loginUserId: userInfo.userId,//登陆用户userId
            isRepeat: false,// [针对安能盲区添加的安能盲区订单重试的措施 true:代表安能盲区重试]
            modeInfo: templateDetailInfo,
            hasWWChat: false,//[true/false:是否需要联系旺旺提示]
            goodsDescription: otherPrintInfo.goodsDescription || infoAboutWaybillCodeNums.goodsDescription, //物品描述，获取单号时需要
            otherPrintInfo,
            afterGetWaybillCodeHook: this.afterGetWaybillCodeHook,//获取单号后执行的钩子函数
            packagingDescription: infoAboutWaybillCodeNums.packagingDescription, //物品描述，获取单号时需要
            afterContinuePrintHook: this.afterContinuePrintHook,
            isZiMuJian: infoAboutWaybillCodeNums.isZiMuJian,
            weight: infoAboutWaybillCodeNums.weight,
            afterGetElecErrorHook: this.afterGetElecErrorHook,
            processBatch: this.printTraceId,
        });
        this.timer.stop('申请单号', {
            type: 'mergePrint',
            printTraceId: this.printTraceId
        })
        // 当天使用融合控件人数打点
        Tatami.pub('porinMergePrintNumber');
        if (firstSendMode) {
            this.afterShipFirstGetWaybillCodeHook(orderList, templateInfo)
            return
        }
        // 当天打单量打点
        let totalN = 0;
        orderList.forEach((order = {}) => {
            totalN += order?.allYdNos?.length || 0;
        });
        new Array(totalN || 0).fill('').forEach(() => {
            Tatami.pub('porinMergePrintOrderNumber');
        });

        // 备注多单号
        const ydnumCount = infoAboutWaybillCodeNums.waybillCodeCount || (infoAboutWaybillCodeNums.sfZDQuantity + 1) || 0;
        const checkResult = printItem.checkAutoAddPrintNum({
            orderList,
            othersSet,
            ydnumCount,
        });
        orderList = checkResult.orderList;
        if (checkResult.isAutoAdd) {
            this.afterAutoAddPrintNum && this.afterAutoAddPrintNum(orderList, ydnumCount || 0);
        }




        /*
        * 这里还能放一个检查是否发货内容打印到另一张上的逻辑
        * 行为仍然是修改 orderList 的内容
        * 暂时只有淘宝需要这个逻辑
        */

        // 本次打印是否向用户展示预览按钮
        // const isHasPreview = previewConfig(printClient) && previewConfig_plat(); // 类型支持预览且平台支持预览
        // this.timer.start('选择打印机')

        // 选择打印机流程

        const { choosedPrinter, printNums, xbqChoosedPrinter, ...choosedPrinterSet } = await printItem.choosePrinter({
            orderList,
            closeChoosePrinterNoti: choosePrinterNotiSet && printOp == 1,
            defaultPrinter: templateInfo.defaultPrinter,
            scanChoosedPrinter,
            printType,
            templateInfo,
            templateDetailInfo,
            displayPrintNum: displayPrintNumSet,
            orderNum: orderList.length * (infoAboutWaybillCodeNums?.waybillCodeCount || 1),
            isHasAutoShip: printOp == 1, //暂时写死不显示自动发货
            shipAfterPrint: true,
            isHasPreview: false,
            printClient,
            printerList: installResult.printers,
            isMerge: true,
            isConcatXbq,
            xbqTempisInvalid
        });
        if (!xbqChoosedPrinter) isConcatXbq = false
        // this.timer.stop('选择打印机',{
        //     type:'mergePrint'
        // })
        console.assert(choosedPrinter, '未获取打印机');
        if (isConcatXbq && !xbqTempisInvalid) {
            new concatPrintDpd().print({
                choosedPrinter: xbqChoosedPrinter,
                orderList: originalOrderList,
                isShowProgress: true
            });
        }
        // 设置状态 正在打印中
        comp.Print.Data.isPrintNow = true
        this.timer.start('打印标记')

        //获取打印序号，把打印序号信息放入 orderList 中
        // eslint-disable-next-line require-atomic-updates
        orderList = await printItem.dealWithPrintNum({
            orderList,
            printType: 'kdd',
        });
        //获取打印批次
        // 整合平台获取打印批次 0818 放心购厂家代打没有批次功能 0901
        orderList = await printItem.dealWithPrintBatch({
            orderList,
        });
        // TODO 暂时隐藏 判断打印内容是否需要打印在另一张纸上
        // 对于「需要在第二张纸上打印发货内容的 order 中」加上 isNeedBreakPage:true
        const checkedBreakPageResult = printItem.checkedBreakPage({
            orderList,
            modeInfo: templateDetailInfo,
            printStyle,
            isMerge: true,
        });
        orderList = checkedBreakPageResult.orderList;
        // const extraPrintContentPageNum = checkedBreakPageResult.extraPrintContentPageNum;


        //打印进度弹窗
        let progress = null
        if (isConcatXbq && window.progress) {
            progress = window.progress
        } else {
            progress = new printItem.getPrintProgress({
                showType: 'mark',
                totalCount: orderList.length * (infoAboutWaybillCodeNums?.waybillCodeCount || 1),
                isConcatXbq
            });
            if (isConcatXbq) window.progress = progress
        }
        const client = comp.Print.getClientType(templateDetailInfo);
        const isCloudPrint = client == 'pdd' || client === 'cainiao';

        //打印标记
        // 如果某个订单获取单号报错了，则对应的订单不进行标记
        originalOrderList = originalOrderList.reduce((acc, item) => {
            if (orderList.find(order => order.togetherId === item.togetherId)) {
                acc.push(item);
            }
            return acc;
        }, []);

        let markPrintResults = await printItem.markPrintStatus({
            orderList: originalOrderList, //此处需要原始的 orderList 数据
            printType: 'kdd',
            templateInfo: templateDetailInfo,
            printer: choosedPrinter,
            isCloudPrint,
            // isCloudPrint,
            wdSet,
            printCopies: printNums,
        });
        if (!markPrintResults.isSuccess) {
            Tatami.showFail('订单标记打印失败，请重试，或联系在线客服');
            progress.remove()
            return
        }
        //完成「打印标记」后的钩子
        this.afterSavePrintMarkHook({ markTradeList: orderList });
        if (isConcatXbq) {
            progress.init({
                type: 'kdd',
                totalCount: orderList.length * (infoAboutWaybillCodeNums?.waybillCodeCount || 1)
            })
        }
        progress.showType('progress');
        this.timer.stop('打印标记', {
            type: 'mergePrint',
            desc: `快递单数量：${orderList.length},打印批次号：${orderList[0].print_batch}`,
            printTraceId: this.printTraceId
        })
        // 美团需要鉴权
        // orderList = await printItem.getAuthPrintDevice(templateInfo, orderList);
        // r融合打印数据处理
        this.timer.start('任务发送')

        let allPrintDatas = []
        let allOrder = []
        let splitNum = localStorage.getItem('documentPrintNum') || 50
        if (setting.printOrderByOrder == 2) {
            let conSort = setting.controlsSortSet.split(',')
            let orderTypeObj = {}
            orderList.forEach(o => {
                if (orderTypeObj[o.kddType]) {
                    orderTypeObj[o.kddType].push(o)
                } else {
                    orderTypeObj[o.kddType] = [o]
                }
            })
            for (let o of conSort) {
                // let pData = orderList.filter(it => platByKddType[it.platform] === o)
				if(o== 13){
					o = orderTypeObj[o]? 13: 16
				}
                if (orderTypeObj[o]) {
                    // 多单号处理逻辑
                    // eslint-disable-next-line require-atomic-updates
                    orderTypeObj[o] = await printItem.moreWaybillCodeProcess({
                        orderList: orderTypeObj[o],
                        sfZDQuantity: infoAboutWaybillCodeNums.sonElecNoCount,
                        waybillCodeCount: infoAboutWaybillCodeNums.waybillCodeCount,
                        temp: templateDetailInfo[o]?.ModeListShow,
                        isZiMuJian: infoAboutWaybillCodeNums.isZiMuJian,
                    });
                    documentNoInfo.allDocumentCount += orderTypeObj[o].length
                    //组装打印数据
                    const printDatas = await printItem.getFormatPrintData({
                        orderList: orderTypeObj[o],
                        templateInfo: templateDetailInfo[o]?.ModeListShow,
                        kddFjrMap: fjrInfo,
                        printType,
                        modeInfo: templateDetailInfo[o],
                        isGetFhdFjr: isConcatFhd,
                        fhdFjrMap: fhdFjrInfo,
                    });
                    allOrder.push({ data: orderTypeObj[o], type: o })
                    allPrintDatas.push({ data: printDatas, orderList: { data: orderTypeObj[o], type: o }, type: o })
                }
                // 暂时写死，有赞的跟菜鸟一起
                if(o ==3 &&  orderTypeObj[15]){
                                 // 多单号处理逻辑
                    // eslint-disable-next-line require-atomic-updates
                    orderTypeObj[15] = await printItem.moreWaybillCodeProcess({
                        orderList:orderTypeObj[15],
                        sfZDQuantity: infoAboutWaybillCodeNums.sonElecNoCount,
                        waybillCodeCount: infoAboutWaybillCodeNums.waybillCodeCount,
                        temp: templateDetailInfo[15]?.ModeListShow,
                        isZiMuJian:infoAboutWaybillCodeNums.isZiMuJian,
                    });
                    documentNoInfo.allDocumentCount +=  orderTypeObj[15].length
                    //组装打印数据
                    const printDatas = await printItem.getFormatPrintData({
                        orderList: orderTypeObj[15],
                        templateInfo: templateDetailInfo[15]?.ModeListShow,
                        kddFjrMap: fjrInfo,
                        printType,
                        modeInfo: templateDetailInfo[15],
                        isGetFhdFjr: isConcatFhd,
                        fhdFjrMap: fhdFjrInfo,
                    });
                    allOrder.push({ data: orderTypeObj[15], type: 15 })
                    allPrintDatas.push({ data: printDatas,orderList: { data: orderTypeObj[15], type: 15 }, type: 15 })
                }
            }
        } else {
            let itemOrder = []
            let isNewArr = false
            orderList.forEach((o, i) => {
                if (!itemOrder.length) {
                    itemOrder.push(o)
                } else {
                    isNewArr = o.kddType !== itemOrder[0].kddType
                    // 每50个一组打印 切割数量改成变量的形式存在 默认50
                    isNewArr = o.kddType !== itemOrder[0].kddType || (itemOrder.length === splitNum && !isSingleTemp)
                    if (isNewArr) {
                        allOrder.push({
                            data: itemOrder,
                            type: itemOrder[0].kddType
                        })
                        itemOrder = [o]
                    } else {
                        itemOrder.push(o)
                    }

                }
                if (itemOrder.length && i === orderList.length - 1) {
                    allOrder.push({
                        data: itemOrder,
                        type: itemOrder[0].kddType
                    })
                }
            })
            for (const o of allOrder) {
                // 多单号处理逻辑
                // eslint-disable-next-line require-atomic-updates
                o.data = await printItem.moreWaybillCodeProcess({
                    orderList: o.data,
                    sfZDQuantity: infoAboutWaybillCodeNums.sonElecNoCount,
                    waybillCodeCount: infoAboutWaybillCodeNums.waybillCodeCount,
                    temp: templateDetailInfo[o.type]?.ModeListShow,
                    isZiMuJian: infoAboutWaybillCodeNums.isZiMuJian,
                });
                documentNoInfo.allDocumentCount += o.data.length
                //组装打印数据
                const printDatas = await printItem.getFormatPrintData({
                    orderList: o.data,
                    templateInfo: templateDetailInfo[o.type]?.ModeListShow,
                    kddFjrMap: fjrInfo,
                    printType,
                    modeInfo: templateDetailInfo[o.type],
                    isGetFhdFjr: isConcatFhd,
                    fhdFjrMap: fhdFjrInfo,
                });
                allPrintDatas.push({ data: printDatas, orderList: o, type: o.type })

            }
        }

        // 给打全局印序号总数赋值
        comp.Print.Data.totalDocumentCount = documentNoInfo.allDocumentCount
        // 打印步骤
        const params_doPrint = {
            allOrder,
            allPrintDatas,
            progress,
            printType,
            choosedPrinter,
            templateInfo,
            templateDetailInfo,
            othersSet,
            printNums,
            documentNoInfo,
            // extraPrintContentPageNum,
            wdSet,
        };

       comp.Print.Data.newPrintMode ? this._newDoPrint(params_doPrint) : this._doPrint(params_doPrint)  ;
    }

    // 打印 && 底单逻辑
    async _doPrint({
        allOrder,
        allPrintDatas,
        progress,
        printType,
        choosedPrinter,
        templateInfo,
        templateDetailInfo,
        othersSet,
        printNums,
        documentNoInfo,
        // extraPrintContentPageNum,
        wdSet,
    }) {
        // 定义打印规则 printSort:1订单顺序，0控件顺序
        // console.assert(printDatas, '组装数据');
        const self = this;
        console.log('打印 && 底单逻辑----');
        const p = new comp.Print();
        let printData = allPrintDatas.shift()
        let beforeType = printData.type
        let tempId = templateDetailInfo[printData.type].ModeListShow.Mode_ListShowId
        let templateSet = templateDetailInfo[printData.type]
        let tempWdSet = wdSet[printData.type]
        let saveData = null
        let allTypeCount = 0
        documentNoInfo.allDocumentCount = documentNoInfo.allDocumentCount * printNums
        let printParams = {
            printDatas: printData.data,
            tempId,
            ptype: printType,
            printBoxIsShow: 0,
            selectedPrinter: choosedPrinter,
            templateSet,
            showPrinting: othersSet.showPrinting, // 显示出纸进度 暂时只针对快递单开放，开关设置走高级设置
            wdSet: tempWdSet,
            documentNoInfo,
        };
        console.info(printParams);
        //打印回调methods获取
        const methodsCb = {
            printStartFunc: function (task) {
                documentNoInfo.startDocumentIdx += printData.data.length * printNums
                printData = allPrintDatas.shift()
                if (!printData) return
                tempId = templateDetailInfo[printData.type].ModeListShow.Mode_ListShowId
                templateSet = templateDetailInfo[printData.type]
                tempWdSet = wdSet[printData.type]
                printParams = {
                    printDatas: printData.data,
                    tempId,
                    ptype: printType,
                    printBoxIsShow: 0,
                    selectedPrinter: choosedPrinter,
                    templateSet,
                    showPrinting: othersSet.showPrinting, // 显示出纸进度 暂时只针对快递单开放，开关设置走高级设置
                    wdSet: tempWdSet,
                    documentNoInfo
                };
                p.listenPrinterDocument(task, printParams, beforeType, methodsCb, progress)
                beforeType = printData.type
            },
            printSynFunc: function (curCount, singleCount) {
                // console.log(curCount,printData.data.length,'curCount');
                // if(curCount == printData?.data.length){
                //     allTypeCount += printData.data.length
                // }
                allTypeCount += singleCount
                // // currentCount = curCount
                // if(countData[0] && curCount == countData[0]?.data.length){
                //     allTypeCount += curCount
                //     currentCount = allTypeCount
                //     countData.shift()
                // }else{
                //     currentCount = curCount < currentCount ? currentCount : (allTypeCount + curCount)
                // }
                // console.log(curCount,allTypeCount,'curCount+++');

                console.log(allTypeCount, 'curCount----');
                progress.updateProgress({ curCount: allTypeCount, type: 'kdd' });

            },
            printFailCb: function () {
                saveData = allOrder.shift()
            },
            printOkFunc: async function () {
                saveData = allOrder.shift()
                let saveTemp = templateDetailInfo[saveData.type]?.ModeListShow
                progress.showType('log');
                if (allOrder.length === 0) {
                    self.timer.stop('任务发送', {
                        type: 'mergePrint',
                        printTraceId: self.printTraceId
                    })
                    comp.Print.Data.isPrintNow = false
                    self.afterSavePrintLogHook(othersSet, templateInfo)
                    // 清除打印来源信息
                    window.printSourceInfo = null
                }
                //底单日志存储
                const results = await printItem.saveKddLog({
                    orderList: saveData.data,
                    templateInfo: saveTemp,
                    printOp: self.data.printOp, //
                    templateSet,
                    printNums,
                    processBatch: self.printTraceId
                });
                // 发货单保存底单
                // if (!!templateSet.ModeTempPrintcfg.IsConcatFhd) {
                //     const tempId = await printItem.getDefaultTempId('fhd');
                //     templateInfo = await printItem.getTempById(tempId, 'fhd');
                //     await printItem.saveFhdLog({
                //         orderList,
                //         templateInfo,
                //     });
                // }
                console.log('saveKddLog 方法的返回值', results);

                //TODO 底单补偿。
                if (allOrder.length === 0) {
                    progress.remove('kdd');
                }
                // console.log(printParams, 'printParams');
                // // const isYilianTemp = comp.base.isYilianTemp(templateInfo.styleId);
                // const isYilianTemp = printParams?.templateSet?.ModeList?.WidthPaper === 760;
                // const tempType = isYilianTemp ? 'yilian' : 'common';
                // new comp.Print().setIsConcatFhd(tempType, false);
            },
            /**
             *
             * @param {object} data  // 打印控件返回的内容
             * @param {string} compType //是哪种控件返回的提示
             */
            notifyPrintCb: function (data, compType) {
                console.log('--notifyPrintCb---');
                console.info(data);
                console.info(compType);
                self.notifyPrintHook(data, compType);
            },
        };
        p.printTemplate(printParams, methodsCb);
    }
    // 新版聚合打印分发逻辑
    async _newDoPrint({
        allOrder,
        allPrintDatas,
        progress,
        printType,
        choosedPrinter,
        templateInfo,
        templateDetailInfo,
        othersSet,
        printNums,
        documentNoInfo,
        // extraPrintContentPageNum,
        wdSet,
    }) {
        // 定义打印规则 printSort:1订单顺序，0控件顺序
        // console.assert(printDatas, '组装数据');
        const self = this;
        console.log('打印 && 底单逻辑----',allPrintDatas);
        const p = new comp.Print();
        let printParams = {
            printDatas: allPrintDatas,
            selectedPrinter: choosedPrinter,
            templateDetailInfo,
            useNewMode:true,
            printNums
        };
        console.info(printParams);
        //打印回调methods获取
        const methodsCb = {
            printStartFunc:async function (data) {
                const {
                    type,
                    printData,
                    orderList = []
                } = data
                console.log(data,'已发送的数据');
                progress.updateProgress({ addCount: printData.length, type: 'kdd' });
                progress.showType('log');
                let saveTemp = templateDetailInfo[type]?.ModeListShow
                let exnumberKey = [5,14].includes(Number(orderList?.type))? 'exnumber' : 'documentID'
                // 过滤当前任务批次的订单保存底单
                let saveOrders = (orderList?.data || []).filter((item) => {
                        let order = printData.find(o=> o[exnumberKey] === item.exnumber)
                        return !!order
                })
                //底单日志存储
                const results = await printItem.saveKddLog({
                    orderList: saveOrders,
                    templateInfo: saveTemp,
                    printOp: 2, //
                    templateSet:templateDetailInfo[type],
                    printNums,
                    processBatch: self.printTraceId
                });
                console.log('saveKddLog 方法的返回值', results);
            },

            printFailCb: function () {
                saveData = allOrder.shift()
            },
            printOkFunc: async function () {

                self.timer.stop('任务发送', {
                    type: 'mergePrint',
                    printTraceId: self.printTraceId
                })
                window.printSourceInfo = null
                comp.Print.Data.isPrintNow = false
                self.afterSavePrintLogHook(othersSet, templateInfo)
                // 发货单保存底单
                // if (!!templateSet.ModeTempPrintcfg.IsConcatFhd) {
                //     const tempId = await printItem.getDefaultTempId('fhd');
                //     templateInfo = await printItem.getTempById(tempId, 'fhd');
                //     await printItem.saveFhdLog({
                //         orderList,
                //         templateInfo,
                //     });
                // }
                progress.remove('kdd');

            },
            /**
             *
             * @param {object} data  // 打印控件返回的内容
             * @param {string} compType //是哪种控件返回的提示
             */
            notifyPrintCb: function (data, compType) {
                console.log('--notifyPrintCb---');
                console.info(data);
                console.info(compType);
                self.notifyPrintHook(data, compType);
            },
        };
        p.printTemplate(printParams, methodsCb);
    }
    // 融合打印封装：通过单号选择框获取单号的相关信息
    async _getInfoAboutWaybillCodeNumsByMergePrint({
        orderList,
        templateInfo,
        templateDetailInfo,
        platByKddType,
        printType,
        canUseNewWaybillCodeSet,
        canChooseWaybillCodeNumSet,
        firstSendMode
    }) {
        // return {
        //     isMustUseNewWaybillCode: false,
        //     waybillCodeCount: 1,
        //     oldWaybillCodeList: 1,

        // }
        // 有没有打印过快递单
        const hasPrinted = await printItem.checkOrderHasPrint(orderList, 'kdd', firstSendMode);

        // 判断本次打印是否是子母件
        let isZiMuJian = templateInfo?.modeZmj == '1';
        // 判断本次打印能不能用原单号打印
        let canUseOldNo = false;


        canUseOldNo = (orderList.length == 1 && !isZiMuJian)

        // 单号数量和使用新/旧单号信息
        let chooseWaybillInfos = {};


        /**
         * 判断是否弹出已打印弹窗并执行弹框；如果已打印，允许用户选择使用原单号还是新单号打印，新单号可以选择新单号个数
         * 会返回一个对象，包含三个值：
         * isMustUseNewWaybillCode 是否是用新单号 Boolean
         * oldWaybillCodeList 旧单号列表 Array 缺省为空数组
         * waybillCodeCount 本次需要的单号数量 Number
         */
        chooseWaybillInfos = await printItem.surePrintAgain({
            printType,
            templateInfo,
            templateDetailInfo,
            hasPrinted,
            canUseOldNo,
            canUseNewWaybillCodeSet,
            canChooseWaybillCodeNumSet,
            orderList,
            isZiMuJian,
            isMerge: true,
            firstSendMode
        }) || chooseWaybillInfos;

        // 如果订单没打印过，并且高级设置中允许选择多个单号 || 打印的订单数量超过两个  => 会有多单号选择框
        // 子母件不会有多单号选择框
        // 关于「是否是用新单号」如果有重打提示框，则由重打提示框获取该值，如果未弹出重打提示框，本逻辑会把「是否是用新单号」设置为 true
        chooseWaybillInfos = await printItem.chooseWaybillCodeNum({
            hasPrinted,
            canChooseWaybillCodeNumSet,
            templateInfo,
            isZiMuJian,
            orderListLength: orderList.length,
            isMustUseNewWaybillCode: chooseWaybillInfos.isMustUseNewWaybillCode,//来自重打提示框的「是否申请新单号」
        }) || chooseWaybillInfos;

        // 获取子母件单号数量等相关信息，菜鸟子母件和非菜鸟子母件
        let zmjRelativeInfos = {};
        if (isZiMuJian) {
            zmjRelativeInfos = await printItem.getZmjRelativeInfosByGroup(templateInfo, chooseWaybillInfos);
        }

        // 抖音顺丰非字母件需要传 sfZDQuantity: undefined 用于后端区分
        // let dyExtraParams = {};
        // if (templateInfo.KddType === 8 && templateInfo.ExCode === 'SF' && !isZiMuJian) {
        //     dyExtraParams = {
        //         sfZDQuantity: undefined,
        //     };
        // }

        return {
            isMustUseNewWaybillCode: chooseWaybillInfos.isMustUseNewWaybillCode,
            waybillCodeCount: chooseWaybillInfos.waybillCodeCount,
            oldWaybillCodeList: chooseWaybillInfos.oldWaybillCodeList,
            isZiMuJian,
            ...zmjRelativeInfos,
            // sfZDQuantity:zmjRelativeInfos.sfZDQuantity,
            // ...dyExtraParams,
        };
    }

    // --------------钩子函数的初始化--------------------
    /**
     * 检测控件安装结果后执行的钩子函数
     * type: 'cainiao'|'lodop'
     * isSuccess: true|false
     */
    afterCheckComponentHook() {
        console.log('afterCheckComponentHook', arguments);
    }

    //选择模板后执行的钩子函数
    afterChooseTempGroupHook() {
        console.log('afterChooseTempGroupHook', arguments);
    }

    //选择发件人后执行的钩子函数
    afterChooseFjrHook() {
        console.log('afterChooseFjrHook', arguments);
    }

    notifyPrintHook() {
        console.log('afterChooseFjrHook', arguments);
    }

    afterGetWaybillCodeHook() {
        console.log('afterGetWaybillCodeHook', arguments);
    }
    afterShipFirstGetWaybillCodeHook() {
        console.log('afterShipFirstGetWaybillCodeHook', arguments);
    }
    afterSavePrintMarkHook() {
        console.log('afterSavePrintMarkHook', arguments);
    }

    //保存打印日志后的钩子函数
    afterSavePrintLogHook() {
        console.log('afterSavePrintLogHook', arguments);
    }

    afterBranchChangeHook() {
        console.log('afterBranchChangeHook', arguments);
    }

    httpWhiteListHook() {
        console.log('httpWhiteListHook', arguments);
    }
    // 获取单号有失败的订单时的回调
    afterGetElecErrorHook() {
        console.log('afterGetElecErrorHook', arguments);
    }

    // 备注多单号完成后的回调
    afterAutoAddPrintNum(dealedOrderList, waybillCodeCount) {
        // 打印时自动追加单号到备注
        if (advancedSet.isAutoAddPrintNum) {
            const memos = [];
            if (waybillCodeCount > 1 || (waybillCodeCount === 1 && !advancedSet.isNotAddPrintNumOnlyOne)) {
                dealedOrderList.forEach(order => {
                    memos.push({
                        togetherId: order.togetherId,
                        tid: order.tids[0],
                        flag: order.sellerFlag || '',
                        memo: order.sellerMome || order.allYdNos.join(','),
                        userId: order.userId,
                    });
                });
            }
            // 调用保存备注接口
            this.getCtrl('com.printSetting', 'singleModifyMemo', memos, false, true);
        }
    }

    // 获取单号有失败的订单时的回调
    afterContinuePrintHook() {
        console.log('afterContinuePrintHook', arguments);
    }

    // --------------钩子函数的初始化--------------------
}
export default PrintKdd;
