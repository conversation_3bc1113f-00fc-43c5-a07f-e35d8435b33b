import * as printItem from '../printItem';
import model from './../../common/model.ts';
import { previewConfig, previewConfig_plat } from '../config';
import { printWithEncryptPackages, printFhdEncrypt } from './crypto';
import { platformConfig, isJDNewWjTpl } from './platformConfig';
import { get, post } from './../../common/http';
import { deepClone } from '../../print/printItem/common';
import urlHelper from '../../common/urlhelp.js';
import { printWithEncryptOrder } from './printEncrypt'
import axios from 'axios';
import concatPrintDpd from './concatPrintDpd';

class PrintKdd {

	constructor(hook) {
		const {
			afterCheckComponentHook,
			afterChooseTempHook,
			afterChooseFjrHook,
			afterGetWaybillCodeHook,
			afterSavePrintMarkHook,
			afterSavePrint<PERSON>ogHook,
			afterBranch<PERSON>hange<PERSON>ook,
			httpWhiteListHook,
			afterContinuePrintHook,
			notifyPrintHook,
			afterGetElecErrorHook,
			afterAutoAddPrintNum,
			afterShipFirstGetWaybillCodeHook
		} = hook || {};
		afterCheckComponentHook && (this.afterCheckComponentHook = afterCheckComponentHook);
		afterChooseTempHook && (this.afterChooseTempHook = afterChooseTempHook);
		afterChooseFjrHook && (this.afterChooseFjrHook = afterChooseFjrHook);
		afterGetWaybillCodeHook && (this.afterGetWaybillCodeHook = afterGetWaybillCodeHook);
		afterShipFirstGetWaybillCodeHook && (this.afterShipFirstGetWaybillCodeHook = afterShipFirstGetWaybillCodeHook);
		afterSavePrintMarkHook && (this.afterSavePrintMarkHook = afterSavePrintMarkHook);
		afterSavePrintLogHook && (this.afterSavePrintLogHook = afterSavePrintLogHook);
		afterBranchChangeHook && (this.afterBranchChangeHook = afterBranchChangeHook);
		httpWhiteListHook && (this.httpWhiteListHook = httpWhiteListHook);//把用户加入 http 白名单的钩子（加入白名单后，用户下次进入系统就直接是 http 环境了）
		afterContinuePrintHook && (this.afterContinuePrintHook = afterContinuePrintHook);
		notifyPrintHook && (this.notifyPrintHook = notifyPrintHook);
		afterGetElecErrorHook && (this.afterGetElecErrorHook = afterGetElecErrorHook);
		afterAutoAddPrintNum && (this.afterAutoAddPrintNum = afterAutoAddPrintNum);
		this.timer = window.timer
		this.printTraceId = ''
	}



	/**
	 * 开始打印
	 * 集成单模板，多模板，取号，底单重打
	 * @param {Object} data
	 * 模板信息，可以不传
	 * 订单数据
	 * 用户信息（考虑初始化的时候传，这时候就可以不传了）
	 * 打印
	 */
	async print(data) {
		console.info('波次打印数据', data);
		this.printTraceId = printItem.generatePrintInfo({ printSource: 'wavePrint', isMerge: false })
		this.timer.start('打印前置处理')
		const userInfo = comp.Print.Data.userInfo;
		const advancedSetting = window.erpData?.advancedSetting
		let isShowMorePrinter = advancedSetting?.multiPrinterConfig == '1';
		const printType = 'kdd';
		let isConcatXbq = advancedSetting?.showGoodsTag == '2'
		const documentNoInfo = {
			allDocumentCount: 0,
			startDocumentIdx: 0
		};
		// 重置一下全局打印序号计数
		comp.Print.Data.totalDocumentCount = 0
		comp.Print.Data.firstDocumentNumber = 1
		const allTempLateList = comp.Print.Data.kddTemplates?.ModeListShows || [];
		let {
			templateInfo,
			orderList,
			printOp,
			// ,shopNewPrintNum
			fjrInfoMap,
			fhdFjrInfoMap,
			printStyle,
			platformId, // 业务方传来的平台id
			firstSendMode,
			scanFjrInfo,
			choosedPrinter: scanChoosedPrinter,
			isMixedPrint, //是否混合打印
			...othersSet
		} = data;
		this.data = {
			printOp,
			printType,
		};
		// 支持混合打印与指定快递，指定快递包含多个模板，指定快递手动选择模板打印机,波次打印暂不接收传入的模板，从订单信息里面获取

		// 从订单中获取模板信息，然后根据模板获取对应模板的默认打印机，没有绑定打印机的使用默认打印机
		const tempInfoObj = {}
		let orderListByExNumber = []
		let needUseTempList = [];
		let needUseTemKddType = [];
		let printerMap = JSON.parse(localStorage.getItem('__printersMap__') || '{}');
		let tempList = await printItem.getKddTempList();
		needUseTempList = orderList.map(item => {
			// 顺便处理下将orderlist处理成包裹维度
			let exnumberArr = item.exnumber.split(',');
			exnumberArr.forEach(exnumber => {
				orderListByExNumber.push({
					...item,
					exnumber: exnumber,
					ydNo: exnumber,
				})
			})

			return item.userTemplateId
		})
		// 根据使用的模板校验控件
		let installResult = null
		installResult = await printItem.checkPrintComponentByKdzs({ checkVersion: '1.2.14.01' });
		let yzIndex = needUseTemKddType.indexOf(15)
		if (yzIndex > -1) {
			`needUseTemKddType`.includes(3) ? needUseTemKddType.splice(yzIndex, 1) : needUseTemKddType.splice(yzIndex, 1, 3)
		}
		// 选择模板默认打印机
		let { defaultPrinter } = await printItem.chooseTemplateByTplIdDialog();
		for (let i = 0; i < needUseTempList.length; i++) {
			let templateDetailInfo = await printItem.getTempDetailInfo({
				printType,
				templateId: needUseTempList[i],
			});
			let kddType = templateDetailInfo?.ModeListShow.KddType;
			if (!needUseTemKddType.includes(kddType)) needUseTemKddType.push(kddType)
			tempInfoObj[needUseTempList[i]] = {
				printer: printerMap[needUseTempList[i]] || defaultPrinter,
				templateDetailInfo: templateDetailInfo,
				kddType: kddType,
			}
		}

		let connection = await printItem.checkKdzsConnetStatus(needUseTemKddType)
		if (!connection.isSuccess) {
			// this.timer.stop('打印前置处理', {
			// 	type: 'mergePrint',
			// 	printTraceId: this.printTraceId
			// })
			return
		}

		// 获取发件人
		// const { fjrInfo } = (await printItem.chooseFjr(fjrInfoMap, printType, bindFjrId, data));
		printItem.getFjrInfo({ orderList: orderListByExNumber, fjrInfoMap, tempInfoObj });

		// 获取打印数据
		const printDataMap = await printItem._getOtherPrintData({ orderList: orderListByExNumber, tempInfoObj });
		// const decryptInfo = !isDYEncryptTpl(templateInfo.KddType, templateInfo.Exid);
		// orderList = await printWithEncryptPackages(orderList, templateInfo, { force: decryptInfo });

		let jdl_zmj = false;
		orderListByExNumber = orderListByExNumber.filter(item => {
			if (!printDataMap[item.exnumber]) return false
			const ydAttr = JSON.parse(item.ydAttr || '{}');
			let printDataInfo = '';
			let templateDetailInfo = tempInfoObj[item.userTemplateId]?.templateDetailInfo || {};
			if (templateDetailInfo.ModeListShow.KddType == 14) {
				printDataInfo = printDataMap[item.exnumber] || '';
			} else {
				printDataInfo = JSON.parse(printDataMap[item.exnumber] || {});
			}
			let PrintDatas = '', wayBillNos = '', geturl = '', customUrl; //京东云打印控件数据处理
			if (templateDetailInfo.ModeListShow.KddType == 5) {
				if (printDataInfo == '' || !printDataInfo) {
					jdl_zmj = true;
				} else {
					const printPdfData = printDataInfo
					const prePrintDatas = printPdfData.pullDataRespDTO && printPdfData.pullDataRespDTO.prePrintDatas;
					geturl = printPdfData.standardTemplate && printPdfData.standardTemplate.standardTemplates[0];
					customUrl = printPdfData.customUrl;
					prePrintDatas && $.each(prePrintDatas, function (i, it) {
						PrintDatas = it.perPrintData;
						wayBillNos = it.wayBillNo;

						});
				}
			}
			Object.assign(item, {
				cloudData: printDataInfo,
				datoubi: printDataInfo.shortAddress,
				jibaodi: printDataInfo.packageCenterName,
				codPrice: ydAttr.dsje,
				cplx: printDataInfo.proName,
				route: printDataInfo.route,
				sfwd: printDataInfo.shippingBranchName,
				txm_jbm: printDataInfo.packageCenterCode,
				jibaoma: printDataInfo.packageCenterCode,
				ddwd: printDataInfo.consigneeBranchName,
				packageYdId: printDataInfo.packageYdId,
				hkzj: ydAttr.hkzj || '',
				goods_title: ydAttr.goods_title || '',

					zy_sfwd: printDataInfo.codingMapping,
					zy_sfwdbm: printDataInfo.consigneeBranchCode,
					zy_mdwd: printDataInfo.sourceTransferCode,
					zy_mdwdbm: printDataInfo.shippingBranchCode,
					txm_number_package: printDataInfo.packageYdId,
					fhdexnumber_package: printDataInfo.packageYdId,
					sortCode: printDataInfo.sortCode,
					encrypted: true,
					printPdfData: printDataInfo.printData, //京东云一联单加密数据
					jdprintData: PrintDatas,
					geturl: geturl,
					customUrl: customUrl,
					wayBillNo: wayBillNos,

				});
			return true;

		});
		documentNoInfo.allDocumentCount = orderListByExNumber.length
		orderList = await printItem.dealWithPrintNum({
			orderList: orderListByExNumber,
			printType: 'kdd',
		});
		// 整合平台获取打印批次
		orderListByExNumber = await printItem.dealWithPrintBatch({
			orderList: orderListByExNumber,
		});

		//打印进度弹窗
		const progress = new printItem.getPrintProgress({
			showType: 'progress',
			totalCount: orderListByExNumber.length,
		});

		let markPrintResults = await printItem.markPrintStatus({
			orderList: orderList, //此处需要原始的 orderList 数据
			printType: 'kdd',
		});
		if (!markPrintResults.isSuccess) {
			Tatami.showFail('订单标记打印失败，请重试，或联系在线客服');
		}
		let allPrintDatas = []
		for (const o of orderListByExNumber) {
			let printData = await printItem.getFormatPrintData({
				orderList: [o],
				templateInfo: tempInfoObj[o.userTemplateId]?.templateDetailInfo?.ModeListShow,
				modeInfo: tempInfoObj[o.userTemplateId]?.templateDetailInfo,
				printType: 'kdd',
				isReprint: true,
			});
			allPrintDatas.push({
				orderList: [o],
				data: printData,
				type: tempInfoObj[o.userTemplateId]?.kddType,
				tempId: o.userTemplateId,
				choosedPrinter: tempInfoObj[o.userTemplateId]?.printer,
			})
		}
		comp.Print.Data.totalDocumentCount = documentNoInfo.allDocumentCount
		progress.showType('progress');

		//打印
		this._doPrint({
			allOrder: orderListByExNumber,
			allPrintDatas,
			progress,
			printType,
			tempInfoObj,
			orderList,
			documentNoInfo
		});
	}

	// 新版聚合打印分发逻辑
	async _doPrint({
		allOrder,
	   allPrintDatas,
	   progress,
	   printType,
	   choosedPrinter,
		tempInfoObj,
		othersSet,
	   documentNoInfo,
	   // extraPrintContentPageNum,
	   wdSet,
   }) {
	   // 定义打印规则 printSort:1订单顺序，0控件顺序
	   // console.assert(printDatas, '组装数据');
	   const self = this;
	   console.log('打印 && 底单逻辑----', allPrintDatas);
	   const p = new comp.Print();
	   let printParams = {
		   printDatas: allPrintDatas,
		   tempInfoObj,
		   useNewMode: true,
		   printNums: 1
	   };
	   console.info(printParams);
		// 收集已成功发送的订单
		const doneOrders = []
	   //打印回调methods获取
	   const methodsCb = {
		printStartFunc: async function (data) {
			const {
				type,
				tempId,
				printData,
				orderList = []
			} = data
			console.log(data, '已发送的数据');
			progress.updateProgress({ addCount: printData.length, type: 'kdd' });
			progress.showType('log');
			   let saveTemp = tempInfoObj[tempId]?.templateDetailInfo
			let exnumberKey = [5, 14].includes(Number(orderList?.type)) ? 'exnumber' : 'documentID'
			// 过滤当前任务批次的订单保存底单
			   let saveOrders = (orderList || []).filter((item) => {
				let order = printData.find(o => o[exnumberKey] === item.exnumber)
				return !!order
			})
			   doneOrders.push(...saveOrders)

		},

		printFailCb: function () {
			saveData = allOrder.shift()
		},
		printOkFunc: async function () {
			progress.showType('log');
			//底单日志存储
			const results = await printItem.reSaveKDDLogs({
				orderList: doneOrders,
				// printOp: 2, //
				// templateSet: saveTemp,
				// printNums: 1,
				// processBatch: self.printTraceId
			});
			console.log('saveKddLog 方法的返回值', results);

			self.timer.stop('任务发送', {
				type: 'mergePrint',
				printTraceId: self.printTraceId
			})
			window.printSourceInfo = null
			comp.Print.Data.isPrintNow = false
			progress.remove('kdd');
			self.afterSavePrintLogHook()
			// 发货单保存底单
			// if (!!templateSet.ModeTempPrintcfg.IsConcatFhd) {
			//     const tempId = await printItem.getDefaultTempId('fhd');
			//     templateInfo = await printItem.getTempById(tempId, 'fhd');
			//     await printItem.saveFhdLog({
			//         orderList,
			//         templateInfo,
			//     });
			// }

		},
		/**
		 *
		 * @param {object} data  // 打印控件返回的内容
		 * @param {string} compType //是哪种控件返回的提示
		 */
		notifyPrintCb: function (data, compType) {
			console.log('--notifyPrintCb---');
			console.info(data);
			console.info(compType);
			self.notifyPrintHook(data, compType);
		},
	};
	   p.printTemplate(printParams, methodsCb);
   }


	// --------------钩子函数的初始化--------------------
	/**
	 * 检测控件安装结果后执行的钩子函数
	 * type: 'cainiao'|'lodop'
	 * isSuccess: true|false
	 */
	afterCheckComponentHook() {
		console.log('afterCheckComponentHook', arguments);
	}

	//选择模板后执行的钩子函数
	afterChooseTempHook() {
		console.log('afterChooseTempHook', arguments);
	}

	//选择发件人后执行的钩子函数
	afterChooseFjrHook() {
		console.log('afterChooseFjrHook', arguments);
	}

	notifyPrintHook() {
		console.log('afterChooseFjrHook', arguments);
	}

	afterGetWaybillCodeHook() {
		console.log('afterGetWaybillCodeHook', arguments);
	}
	afterShipFirstGetWaybillCodeHook() {
		console.log('afterShipFirstGetWaybillCodeHook', arguments);
	}

	afterSavePrintMarkHook() {
		console.log('afterSavePrintMarkHook', arguments);
	}

	//保存打印日志后的钩子函数
	afterSavePrintLogHook() {
		console.log('afterSavePrintLogHook', arguments);
	}

	afterBranchChangeHook() {
		console.log('afterBranchChangeHook', arguments);
	}

	httpWhiteListHook() {
		console.log('httpWhiteListHook', arguments);
	}
	// 获取单号有失败的订单时的回调
	afterGetElecErrorHook() {
		console.log('afterGetElecErrorHook', arguments);
	}

	// 备注多单号完成后的回调
	afterAutoAddPrintNum(dealedOrderList, waybillCodeCount) {
		// 打印时自动追加单号到备注
		if (advancedSet.isAutoAddPrintNum) {
			const memos = [];
			if (waybillCodeCount > 1 || (waybillCodeCount === 1 && !advancedSet.isNotAddPrintNumOnlyOne)) {
				dealedOrderList.forEach(order => {
					memos.push({
						togetherId: order.togetherId,
						tid: order.tids[0],
						flag: order.sellerFlag || '',
						memo: order.sellerMome || order.allYdNos.join(','),
						userId: order.userId,
					});
				});
			}
			// 调用保存备注接口
			this.getCtrl('com.printSetting', 'singleModifyMemo', memos, false, true);
		}
	}

	// 获取单号有失败的订单时的回调
	afterContinuePrintHook() {
		console.log('afterContinuePrintHook', arguments);
	}

	// --------------钩子函数的初始化--------------------
}
export default PrintKdd;
