import { get, post } from './../../common/http';
import model from './../../common/model.ts';
import urlHelper from '../../common/urlhelp.js';

export async function printWithEncryptOrder({ orderList, afterContinuePrintHook, needUseTemp = [], templateInfo }) {
	// 解密方式只分两种，京东、其他平台
	let decodePlat = {
		jdOrder: [],
		pddOrder: [],
		conmonOrder: [],
	}
	// 获取高级设置配置
	let isUseCainiao = window.erpData?.advancedSetting?.refluxOrderPrintSet == '1'
	// 聚合打印视频号是否需要解密
	let isUseSph = needUseTemp.includes(14)
	let orageOrderList = [...orderList]
	// orderList.forEach(o => {
	//     if (o.platform === 'jd' && o.source !=='HAND') decodePlat.jdOrder.push(o)
	//     // if (o.platform === 'xhs' && o.source !=='HAND' && (!needUseTemp.length || !needUseTemp.includes(13))){
	//     //     decodePlat.xhsOrder.push(o)
	//     // }
	//     if (o.platform === 'sph' && o.source !=='HAND' && (!needUseTemp.length || !needUseTemp.includes(14))) decodePlat.sphOrder.push(o)
	//     // if (o.platform === 'c2m') decodePlat.c2mOrder.push(o)
	// })

	// NOTE 新增网点面单自动解密，根据系统设置区分需要自动解密或者手动解密，如已经手动解密则无需再次自动解密
	// 聚合打印不支持网点
	// 根据后天设置showAutoDecode 与订单高级设置branchDzmdAutoDecode判断是否需要解密
	const advancedSetting = window.erpData?.advancedSetting

	if (templateInfo?.KddType == 2 && comp.Print.Data.showAutoDecode && advancedSetting?.printSetExpandDTO?.branchDzmdAutoDecode) {
		orderList.forEach(o => {
			if (o.platform === 'pdd' && !['HAND', 'SCMHAND'].includes(o.source) && !o.isDecrypted) {
				decodePlat.pddOrder.push(o)
			} else if (!['HAND', 'SCMHAND'].includes(o.source) && o.platform !== 'jd' && !o.isDecrypted) {
				decodePlat.conmonOrder.push(o)
			}
			// 解密兼容，存在caid的无需解密打印，没有caid的老数据保留解密逻辑
			// 分销代发订单不需要解密
			if (o.platform === 'jd' && !o.isDecrypted && !['HAND', 'SCMHAND'].includes(o.source) && o.ptType !== 'FXDF') decodePlat.jdOrder.push(o)

		})
	} else {
		orderList.forEach(o => {
			// 明文回流订单是否需要解密需要通过高级设置配置决定

			let isHlCommon = o.hlPlatformType && !['hl-other', 'hl-tx'].includes(o.hlPlatformType) && !o.hlEncryptOrder && !isUseCainiao
			if (!['HAND', 'SCMHAND'].includes(o.source) && (isHlCommon || (o.platform === 'sph' && !isUseSph) || (o.platform === 'dw' && templateInfo?.KddType != 17))) {
				decodePlat.conmonOrder.push(o)
			}
			// 解密兼容，存在caid的无需解密打印，没有caid的老数据保留解密逻辑
			// 分销代发订单不需要解密
			if (o.platform === 'jd' && !o.caid && !o.isDecrypted && !['HAND', 'SCMHAND'].includes(o.source) && o.ptType !== 'FXDF') decodePlat.jdOrder.push(o)

		})
	}
	// 收集所有解密失败的订单
	// 失败的订单id
	let errprTids = []
	// 失败的订单
	let errorOrderList = []
	if (decodePlat.jdOrder.length) {
		let formatOrder = {}
		let scmUserIds = []
		let shopList = window.erpData.shopList

		for (const o of decodePlat.jdOrder) {
			if (o.source === 'SCM' && !scmUserIds.includes(o.distributorUserId)) {
				scmUserIds.push(o.distributorUserId)
			}
			if (formatOrder[o.sellerId]) {
				formatOrder[o.sellerId].push(o)
			} else {
				formatOrder[o.sellerId] = [o]
			}
			if (o.source !== 'SCM') {
				o.jdEncodeTid = await jdQueryTradeVo(o.encodeTid)
			}
		}
		// 获取来自所有分销商的店铺信息
		let shopParams = scmUserIds.map(o => {
			let params = {
				refresh: 1,
				userId: o,
				queryType: 'scm'
			}
			return post('/index/platformShop/getPlatformShops', {
				params: params,
				headers: {
					'Content-Type': 'application/json;charset=UTF-8'
				},
				isHideLoading: true,
			})
		})
		const shopResults = await Promise.all(shopParams);
		let scmShop = []
		shopResults.forEach(o => {
			scmShop.push(...(o?.data?.list || []))
		})
		let allPrams = []
		for (let k in formatOrder) {
			let shopInfo = shopList.find(o => o.sellerId === k)
			if (!shopInfo) {
				shopInfo = scmShop.find(o => o.sellerId === k)
			}
			let params = {
				apiName: "getSign",
				orderNos: formatOrder[k].map(o => {
					return o.source === 'SCM' ? (o.outerTradeId || [])[0] : o.jdEncodeTid
				}).join(','),
				token: shopInfo.token,
				extendProps: {
					scenesType: "1003"
				}
			}
			allPrams.push(post('/trade/hufu/getSign', {
				params: params,
				headers: {
					'Content-Type': 'application/json;charset=UTF-8'
				},
				isHideLoading: false,
			}))
		}
		const results = await Promise.all(allPrams);
		let decodeRequst = []
		results.forEach(o => {
			if (o.success) {
				let { data = {} } = o
				decodeRequst.push((post(data.url, {
					params: JSON.parse(data.body),
					headers: {
						'x-jdcloud-date': data.xJdcloudDate || '',
						'x-jdcloud-nonce': data.xJdcloudNonc || '',
						'Authorization': data.authorization || '',
						'Content-Type': 'application/json'
					},
					isHideLoading: false,
				})))
			}
		})

		const decodeResult = await Promise.all(decodeRequst);
		const decodeDataArr = []
		decodeResult.forEach(o => {
			let { orderSensitiveInfo } = o
			if (orderSensitiveInfo) decodeDataArr.push(...orderSensitiveInfo.orderList)
		})
		orderList = orderList.map(o => {
			let decodeOrder = decodeDataArr.find(it => (it.ordersNo === o.jdEncodeTid || o.outerTradeId.includes(it.ordersNo)))
			if (!decodeOrder) return o
			o.isDecrypted = 1
			let obj = {
				receiverName: decodeOrder.receiverName,
				receiverMobile: o.idxEncodeReceiverMobile || decodeOrder.receiverMobile,
				receiverAddress: decodeOrder.receiverAddress,
				s_name: decodeOrder.receiverName,
				s_mobile: decodeOrder.receiverMobile,
				s_phone: decodeOrder.receiverTelephone

			}
			return Object.assign({}, o, obj)

		})

	}
	if (decodePlat.conmonOrder.length) {
		let decodeList = [],
			itemArr = [],
			len = decodePlat.conmonOrder.length,
			decodeObj = {},
			decodeFailArr = [],
			errorOrderList = [];
		decodePlat.conmonOrder.map((o, i) => {
			itemArr.push({
				sellerId: o.sellerId,
				platform: o.platform,
				caid: o.caid,
				tid: o.encodeTid,
				sceneCode: 100,
				encodeReceiverPhone: o.receiverPhone,
				encodeMobile: o.receiverMobile,
				encodeReceiverName: o.receiverName,
				encodeReceiverAddress: o.receiverAddress,
				userId: o.distributorUserId || '',
				source: o.source
			})
			if (i % 50 == 0 || (i + 1) === len) {
				decodeList.push([...itemArr])
				itemArr = []
			}
		})
		let results = await urlHelper.threadRequest({
			list: decodeList,
			threadCount: 1,
			apiUrl: '/trade/batchDecryData',
			headers: {
				'Content-Type': 'application/json;charset=UTF-8'
			},
			type: 'success',
			paramsIsArr: true,
			isHideLoading: true,
			isHideError: true,
		});
		results.successResults.forEach(item => {
			decodeObj = {
				...decodeObj,
				...(item.resultJson?.data || {})
			}
		})
		// 筛选掉解密失败的订单
		for (let k in decodeObj) {
			if (decodeObj[k].errorMessage || decodeObj[k].errorCode) {
				errprTids.push({
					tid: k,
					errMessage: decodeObj[k].errorMessage || ''
				})
				delete decodeObj[k]
			}
		}
		if (results.errorResults.length) {
			// 请求结果处理  结果先不处理，稍后统一处理
			results.errorResults.map(errItem => {
				errItem.requestParam.map(it => {
					errprTids.push({
						tid: it.tid,
						errMessage: errItem?.resultJson?.errorMessage || ''
					})
				})
			})

		}
		// 失败与成功结果中的解密失败订单数
		if (errprTids.length) {
			orderList = orderList.filter(o => {
				let isInclouds = errprTids.find(it => o.encodeTid == it.tid)
				if (isInclouds) errorOrderList.push(o.togetherId)
				return !isInclouds
			})
		}

		orderList = orderList.map(orderItem => {
			if (!decodeObj[orderItem.encodeTid]) return orderItem
			orderItem.isDecrypted = 1
			let obj = {
				receiverName: decodeObj[orderItem.encodeTid].receiverName,
				receiverMobile: decodeObj[orderItem.encodeTid].mobile || decodeObj[orderItem.encodeTid].receiverMobile || decodeObj[orderItem.encodeTid].receiverPhone,
				receiverAddress: decodeObj[orderItem.encodeTid].receiverAddress,
				// s_phone: decodeObj[orderItem.encodeTid].mobile || decodeObj[orderItem.encodeTid].receiverPhone,
				s_name: decodeObj[orderItem.encodeTid].receiverName,
				s_address: decodeObj[orderItem.encodeTid].receiverAddress,
			}
			if (templateInfo?.KddType == 5) {
				// 数据问题，phone返回的事虚拟号，mobile返回的不是虚拟号，无法取号，京东将两个值互换
				obj.s_phone = decodeObj[orderItem.encodeTid].mobile;
				obj.receiverMobile = decodeObj[orderItem.encodeTid].receiverPhone;

			}
			if (orderItem.platform == 'xhs') obj.s_phone = ''
			if (templateInfo?.KddType == 2) {
				obj.s_mobile = decodeObj[orderItem.encodeTid].receiverMobile || decodeObj[orderItem.encodeTid].mobile || decodeObj[orderItem.encodeTid].receiverPhone
			}
			// 如果是视频号的订单使用其他平台打印，还需要替换掉tradeReceiverList里面的数据
			// if (orderItem.platform == 'sph' || orderItem.platform == 'dw' || templateInfo?.KddType == 2) {
			orderItem.tradeReceiverList = orderItem.tradeReceiverList?.map(it => {
				it.receiverMobile = decodeObj[orderItem.encodeTid].mobile || decodeObj[orderItem.encodeTid].receiverMobile || decodeObj[orderItem.encodeTid].receiverPhone;
				it.receiverName = decodeObj[orderItem.encodeTid].receiverName;
				it.receiverAddress = decodeObj[orderItem.encodeTid].receiverAddress;
				it.receiverTel = decodeObj[orderItem.encodeTid].receiverPhone;
				if (templateInfo?.KddType == 5) {
					// 数据问题，phone返回的事虚拟号，mobile返回的不是虚拟号，无法取号，京东将两个值互换
					it.receiverMobile = decodeObj[orderItem.encodeTid].receiverPhone || decodeObj[orderItem.encodeTid].receiverMobile || decodeObj[orderItem.encodeTid].receiverPhone;
					it.receiverTel = decodeObj[orderItem.encodeTid].mobile;
				}
				if (orderItem.platform == 'xhs') it.receiverTel = ''
				return it
			})
			// }
			// 如果是明文回流订单解密打印，解密后明文取号平台需要走手工单逻辑，更改平台为手工单
			if (orderItem.hlPlatformType && !orderItem.hlEncryptOrder && orderItem.platform !== 'sph') orderItem.platform = 'hand'
			return Object.assign({}, orderItem, obj)
		})

	}
	if (decodePlat.pddOrder.length) {
		let queryType = [{
			type: 'receiverPhone',
			valueKey: 'receiver_phone',
			oringKey: 'receiverMobile',
		}, {
			type: 'receiverName',
			valueKey: 'receiver_name',
			oringKey: 'receiverName',
		}, {
			type: 'receiverAddress',
			valueKey: 'receiver_address',
			oringKey: 'receiverAddress',
		}];

		for (const o of decodePlat.pddOrder) {
			let params = {
				mallId: o.sellerId,
				tid: o.encodeTid,
				userId: o.userId,
			};
			try {
				// 循环调用解密接口
				for (const it of queryType) {
					let currentParams = {
						encryptContent: o[it.oringKey],
						...params
					};

					let res = await _requestDecrypt(it.type, currentParams);
					if (!res || res?.error_msg) {
						// 如果接口请求失败，抛出异常中断所有循环
						throw new Error(`解密失败: ${res.data.error_msg}`);
					}

					o.isDecrypted = 1;
					o[it.oringKey] = (res?.order_info || {})[it.valueKey];
				}
			} catch (error) {
				// 处理解密失败的情况
				errprTids.push({
					tid: o.encodeTid,
					errMessage: error || ''
				})
			}
		}
		orderList = orderList.filter(o => {
			let isInclouds = errprTids.find(it => o.encodeTid == it.tid)
			if (isInclouds) errorOrderList.push(o.togetherId)
			if (o.platform === 'pdd') {
				let pddOrder = decodePlat.pddOrder.find(it => it.encodeTid === o.encodeTid)
				if (pddOrder) {
					o.receiverName = pddOrder.receiverName
					o.receiverMobile = pddOrder.receiverMobile
					o.receiverAddress = pddOrder.receiverAddress
					o.s_name = pddOrder.receiverName
					o.s_address = pddOrder.receiverAddress
					o.tradeReceiverList = o.tradeReceiverList?.map(trade => {
						trade.receiverMobile = pddOrder.receiverMobile;
						trade.receiverName = pddOrder.receiverName;
						trade.receiverAddress = pddOrder.receiverAddress;
						// if (orderItem.platform == 'xhs') trade.receiverTel = ''
						return trade
					})
				}
			}
			return !isInclouds
		})

	}
	// 解密失败跳过移到最后面
	if (errprTids.length) {
		// TODO 这里逻辑存在问题，需要优化
		if (errprTids.length === orageOrderList.length) {
			afterContinuePrintHook && afterContinuePrintHook(errorOrderList);
			await allEncryptErrFnc()
			return
		}
		let isContinue = await encryptErrFnc(errprTids)
		if (isContinue) {
			afterContinuePrintHook && afterContinuePrintHook(errorOrderList);
		}
	}
	return orderList
}
async function jdQueryTradeVo(tid) {
	let result = await post('/trade/queryTradeVo', {
		params: { tid },
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
		isHideLoading: true,
	})
	return result?.data.ptTid || ''
}
async function _requestDecrypt(type, params, sellerNick) {
	let res = null;
	// if (type === 'receiverPhone') {
	// 	res = await TradePddControlDecryptV1ReceiverPhoneApi({
	// 		...params,
	// 	});
	// } else if (type === 'receiverName') {
	// 	res = await TradePddControlDecryptV1ReceiverNameApi({
	// 		...params,
	// 	});
	// } else if (type === 'receiverAddress') {
	// 	res = await TradePddControlDecryptV1ReceiverAddressApi({
	// 		...params,
	// 	});
	// }
	res = await axios({
		method: 'post',
		baseURL: `https://pdd-fangzhou.kuaidizs.cn`,
		url: `/pdd/control/decrypt/v1/${type}`,
		headers: {
			'X-PDD-VerifyAuthToken': window.localStorage.getItem(`verifyAuthToken_${params.mallId}`) || '',
			'Content-Type': 'application/json;charset=UTF-8',
		},
		data: JSON.stringify(params),
	});
	console.log(res, 'res');

	if (res.data.error_msg) {
		return new Promise((resolve, reject) => {
			reject();
		});
	}
	return res.data;
}
function encryptErrFnc(errprTids) {
	return new Promise((resolve) => {
		let errHtml = `
            <div>
                <p>以下订单解密失败：</p>
                ${errprTids.map(o => {
			return `<p>${o.tid}:${o.errMessage}</p>`
		})}
                <p>是否跳过继续打印</p>
            </div>
        `
		model({
			type: 'confirm',
			content: errHtml,
			width: 426,
			minHeight: 200,
			okName: '跳过',
			okCb: () => {
				resolve(true)
			}
		});
	})
}
function allEncryptErrFnc() {
	return new Promise((resolve) => {
		model({
			type: 'confirm',
			content: `订单解密失败，请重新选择订单`,
			width: 426,
			height: 200,
			okName: '确定',
			cancelHide: true,
		});
	})
}
