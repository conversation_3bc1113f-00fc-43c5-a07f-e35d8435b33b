import  { post } from '../../common/http';
import BICtempInfo from '../../resources/comp/templateCfg/BICtempInfo';

function getTemplateList(printType = 'kdd',isRequestApi,isOutRequest){
    return new Promise( (resolve, reject) =>{
        let isNeedRequestApi = true;
        const printData = comp.Print.Data;

        const templistName = printType === 'kdd' ? 'kddTemplates' : 'fhdTemplateList';
        //如果有缓存值并且不要求强制请求，则使用缓存
        if( !isEmpty(printData[templistName]) && !isRequestApi){
            isNeedRequestApi = false;
        }else{
            isNeedRequestApi = true;
        }

        if(!isNeedRequestApi){
            resolve(printData[templistName]);
        }else{
            const api = printType === 'kdd' ? 'get_kdd_TemplateList' : 'GET_FHD_TEMPLATELIST';
            post(api,{
                params: {
                    modeId:printType,
                    exuserId:printData.exuid,
                },
            })
            .then(function(res){
                // 屏蔽BIC订单打印模板，发货单item.Exid === 671为BIC模板
                if(printType === 'fhd') {
                    comp.Print.Data.BICTemp = res.data.ModeListShows.find(item => item.Exid === 671);
                    res.data.ModeListShows = res.data.ModeListShows.filter(item => item.Exid !== 671);
                }
                if(!isOutRequest) printData[templistName] = res.data;
                resolve(res.data);
            })
            .catch(function(err){
                reject(err);
            });
        }
    }).catch(function(reason) {
        console.log('_getTemplateListCatch:', reason);
    });
}

/**
  * 获取【发货单，快递单】模板详细信息
  * @param {*} param0
  * temppalteId {number 必传 默认为}
  * printType {string 可不传 默认为‘kdd’} 模板类型，取值 kdd|fhd
  * isHideloading {boolean 可不穿 默认为false} 请求中是否展示loading 默认都是展示loading的
  */
function getTempDetailInfo({
    templateId,
    printType = 'kdd',
    isHideloading = false,
}){
    return new Promise( (resolve) =>{
        if(printType === 'bic') {
            resolve(BICtempInfo)
        }else{
            comp.Print.getTemplateInfoAsync({
                ptype: printType || 'kdd',
                tempId:templateId,
                isLoading:!isHideloading,
            },(res)=>{
                resolve(res);
            });
        }

    }).catch(function(reason) {
        console.log('catch:', reason);
    });
}

// 获取默认模板Id，如果没有设置默认模板，使用第一个模板当作默认模板
async function getDefaultTempId(printType = 'kdd'){
    const tempObj = await getTemplateList(printType);
    return tempObj.ModeListShowId || tempObj.ModeListShows[0].Mode_ListShowId;
}
// 同步视频号订单
async function sphUserTemplateSyn(sellerInfo){
    return new Promise( (resolve, reject) =>{
        const params = {
            params:{
                downType:'ALL',
                sonVOS:sellerInfo,
            }
        }
            post('sph_user_template_syn',{
                params,
            })
            .then(async function(res){
                let isHasSuccess = res.data.find(o=> o.success)
                if(!isHasSuccess){
                    resolve(res)
                    return
                }
                const tempObj = await getTemplateList('kdd',true);
                comp.Print.Data.kddTemplates = tempObj;
                const funcHook = (window.printAPI.compHookObj || {}).afterLoadedKddTemp;
                funcHook && funcHook(tempObj,comp.Print.Data.userInfo.userId);
                console.log(res,tempObj);
                resolve(res)
            })
            .catch(function(err){
                reject(err);
            });
    }).catch(function(reason) {
        console.log('sphUserTemplateSyn:', reason);
    });
}
// 校验视频号订单
async function sphUserTemplateCheck(exCode,platformUserTemplate,sellerId){
    return true
    // return new Promise( (resolve, reject) =>{
    //     post('sph_user_template_check',{
    //         params:{
    //             platformUserTemplate:platformUserTemplate,
    //             exCode:exCode,
    //             sellerId
    //         }
    //     })
    //     .then(async function(res){
    //         resolve(res.data)
    //     })
    //     .catch(function(err){
    //         reject(err);
    //     });
    // }).catch(function(reason) {
    //     console.log('_getTemplateListCatch:', reason);
    // });
}
//视频号删除店铺同时删除模板
async function sphUserTemplateDelete(sellerId){
    return new Promise( (resolve, reject) =>{
        const temps = comp.Print.Data?.kddTemplates?.ModeListShows || []
        const deleteIds = []
        temps.forEach(it => {
            if(it.sellerId == sellerId) deleteIds.push(it.Mode_ListShowId)
        });
        if(!deleteIds.length){
            resolve('success')
            return
        }
        post('sph_user_template_delete',{
            params:{
                params:deleteIds
            }
        })
        .then(async function(res){
            const tempObj = await getTemplateList('kdd',true);
            comp.Print.Data.kddTemplates = tempObj;
            const funcHook = (window.printAPI.compHookObj || {}).afterLoadedKddTemp;
            funcHook && funcHook(tempObj,comp.Print.Data.userInfo.userId);
            console.log(res,tempObj);
            resolve(res.data)
        })
        .catch(function(err){
            reject(err);
        });
    }).catch(function(reason) {
        console.log('_getTemplateListCatch:', reason);
    });
}
// 获取BIC订单模板的id
async function getBICTempInfo() {
    await getTemplateList('fhd');
    return comp.Print.Data.BICTemp;
}

function checkIsZiMuJian( modeInfo ){
    let isZiMuJian = false;

    const template = modeInfo.ModeList || {};
    const ExCode = template.Excode || template.ExCode;
    const isKuaiYun = template.kuaiYun ||
        comp.base.getTempStyle('kuaiyun', template.StyleId, template) ||
        comp.base.getTempStyle('wdkuaiyun', template.StyleId, template) ||
        comp.base.getTempStyle('DYkuaiyun', template.StyleId, template) ||
        comp.base.getPddTempStyle(
            'pddKuaiYunZmj',
            template.StyleId,
            template.Excode || template.ExCode,
        );

	if (isKuaiYun || ['3108002701_1011', 'SFKY', 'XFWL', 'SXJD', 'CN7000001021040', 'ANEKY', 'BESTQJT', 'YMDD', 'JDKY', 'DISTRIBUTOR_13468074'].includes(ExCode.toString())) {  //菜鸟快运，子母件
        isZiMuJian = true;
    } else {
        modeInfo.ModeAdvancedServices && modeInfo.ModeAdvancedServices.map( v => {
            if (v.serviceKey == 'zmj') {
                isZiMuJian = true;
                return false;
            }
        });
        // 拼多多子母单
        modeInfo.ModeLogisticsItems && modeInfo.ModeLogisticsItems.map( v => {
            if (v.serviceCode == 'ZMD' || v.serviceCode == 'ZMJ' ) {
                isZiMuJian = true;
                return false;
            }
        });
    }

	if (['SFKY'].includes(ExCode.toString()) && template.KddType == 5) isZiMuJian = false
    return isZiMuJian;
}
function checkIsZiMuJianNew( {modeInfo,isMerge} ){
    let zmjType = 1; // 1非子母件，2快运子母件，3子母件
    if(isMerge){
        if(modeInfo.modeZmj == 1){
            ['SF','JD','DBKD','SZKKE'].includes(modeInfo.exCode) ? zmjType = 3 :zmjType = 2
        }
    }else{
        if(modeInfo.dictStatus === 'WITH_ZMJ') zmjType = 3
        if(modeInfo.companyType == 2) zmjType = 2
        // 临时更改，京东中通快运改为非子母件
        if(modeInfo.KddType == 5 && ['3108002701_1011','SFKY','XFWL','CN7000001021040','ANEKY','BESTQJT','YMDD'].includes(modeInfo.ExCode ) ) zmjType = 1
    }

    return zmjType;
}
function checkIsZiMuJianAndKuaiyun( tempInfo,isMerge ){
    if(isMerge){
        return !!tempInfo.modeZmj
    }else{
        return( tempInfo.companyType == 2 || tempInfo.dictStatus == 'WITH_ZMJ')
    }
}

function checkIsUseOldNo({templateInfo,isZiMuJian,orderLength} ){
    const kddType = templateInfo.KddType;
    const canUseOldNo = !isZiMuJian && orderLength == 1 && kddType != 1;
    return canUseOldNo;
}


async function getTempById(tempId,printType = 'kdd'){
    if(!tempId){
        return null;
    }
    const tempList = await getTemplateList(printType);
    return tempList.ModeListShows.find( item => item.Mode_ListShowId == tempId );
}

async function getKddTempList(isRequestApi,isOutRequest){
    const tempObj = await getTemplateList('kdd', isRequestApi,isOutRequest);
    return tempObj.ModeListShows || tempObj;
}

/**
 * 判断数组或对象是空
 * 额外的：如果对象的每一个 key 的 value 都是空数组，则也返回 true
 * @param {Object} target
 *
 * []  true
 * {a:[],b:undfined} true
 * {a:{},b:undfined} false
 */
function isEmpty(target){

    if(!target){
        return true;
    }else if(Array.isArray(target)){ // 数组
        if(target.length === 0){
            return true;
        }
    }else if(Object.prototype.toString.call(target) === '[object Object]'){ // 对象
        let isEmptyObject = true;
        for(const key in target){
            const item = target[key];
            if(item && item.length != 0){
                isEmptyObject = false;
                break;
            }
        }
        return isEmptyObject;
    }
}

export {
    getTemplateList,
    getTempDetailInfo,
    getDefaultTempId,
    getKddTempList,
    checkIsZiMuJian,
    checkIsZiMuJianNew,
    checkIsZiMuJianAndKuaiyun,
    checkIsUseOldNo,
    getTempById,
    getBICTempInfo,
    sphUserTemplateSyn,
    sphUserTemplateCheck,
    sphUserTemplateDelete
};
