/**
 * @desc 判断当前状态下，打印内容是否需要打印在另一张纸上
 * @desc 对于「需要在第二张纸上打印发货内容的 order 中」加上 isNeedBreakPage:true
 * @param { Object } 以下为对象解构值
 * @param { Array } orderList 打印数据
 * @param { Object } modeInfo 模版数据
 * @param { Object } printStyle 后端返回的打印样式
 * @return { Array } orderList
 */
function checkedBreakPage({ orderList, modeInfo, printStyle = {}, isMerge}) {
    if (!isMerge && modeInfo.ModeList?.KddType === 1) { //五联单不需要分页
        return { orderList, extraPrintContentPageNum: 0 };
    }

    let platform = window.printAPI.getPlatform(),
        userId = Tatami.localcache.get(
            `${platform === 'tb' ? 'taobaoId' : 'userId'}`,
            'session',
        ),
        thisStyle = (printStyle[userId] || {});
    let canBreakPage, autoBreakPage;
    if (comp.Print.Data.platform === 'erp') {
        thisStyle = printStyle
    }

    if (comp.Print.Data.platform === 'pdd') {
        const ids = userId.split('-');
        userId = (ids && ids[0]) || userId;
        thisStyle = (printStyle[userId] || {});
        canBreakPage = thisStyle.showPages != 0;
    } else {
        canBreakPage = thisStyle.breakpage != -1;
        autoBreakPage = !!thisStyle?.breakPageInfinite
    }

    // 当前模板是否有「发货内容」 f_info 这个输入框
    let hasFINfoInput = false;
	let fInfoFontSize = 12;   // 发货内容字号,默认为12
    let WidthPaper, HeightPaper // 模板的宽高

    try {
        void function () {
            if (isMerge) {
                for (const k in modeInfo) {
                    for (const input of modeInfo[k].ModeInputs) {
                        for (const item of input.proArray) {
                            if (item.Dataname == 'f_info') {
                                hasFINfoInput = true;
								fInfoFontSize = Number(input.Fontsize) || 12;
                                return;
                            }
                        }
                    }
                }
            } else {
                for (const input of modeInfo.ModeInputs) {
                    for (const item of input.proArray) {
                        if (item.Dataname == 'f_info') {
                            hasFINfoInput = true;
							fInfoFontSize = Number(input.Fontsize) || 12;
                            return;
                        }
                    }
                }
            }
        }();
    } catch (e) {
        console.error(e);
    }
	// 抖音面单打印使用的pt，最终换算会跟软件的px存在误差，修正一下px与pt的换算
	if(modeInfo.ModeList?.KddType == 8 || modeInfo.ModeList?.KddType == 3){
		let fontsizePt = Math.round(fInfoFontSize * 0.75);
		fInfoFontSize = Math.ceil(fontsizePt/0.75);
	}
    let extraPrintContentPageNum = 0;
    let newOrderList = orderList;
    if (canBreakPage && hasFINfoInput) { // 「打印内容」设置可以分页，并且模板本身有「发货内容」这个数据框
        let breakpageNum = thisStyle.breakpageNum || thisStyle.showPagesNum;
        if (thisStyle.breakpageNum === 0) breakpageNum = 0
        newOrderList = orderList.map(order => {
            const thisPrintContentLength = (order.orders || []).length;
            if (thisPrintContentLength > breakpageNum) {
                order.isNeedBreakPage = true;
                if (autoBreakPage && canUseAutoBreakPage({ isMerge, order, modeInfo })) {
                    const { ModeList = {} } = isMerge ? (modeInfo[3] || {}) : modeInfo;
                    const { WidthPaper, HeightPaper } = ModeList; // 模板的宽高
                    // 自动分页处理
                    const { pageNum, printContentArr } = getBreakPageData({ fInfoFontSize, WidthPaper, HeightPaper }, order.print_content)
                    extraPrintContentPageNum += pageNum;
                    order.pageNum = pageNum;    // 快递单的分页数
                    order.printContentArr = printContentArr; // 自动分页的发货内容
                } else {
                    // 仅分一页
                    extraPrintContentPageNum += 1;
                    order.pageNum = 1;
                }
            }
            return order;
        });
    }

    return {
        orderList: newOrderList,
        extraPrintContentPageNum,
    };
}
function canUseAutoBreakPage({ isMerge, order, modeInfo }) {
    if (isMerge) {
        return order?.kddType == 3 || order?.kddType == 8
    }
    return ['tb', 'hand', 'other', 'sph', 'ali','fxg'].includes(order?.platform) && [3,8].includes(modeInfo.ModeList?.KddType)
}
/**
* 分页方法——计算发货内容长度以实现自动分页【目前分页模板字体】
* @param {*}  tempInfo 模板详情 {fInfoFontSize: 发货内容字号，WidthPaper: 模板宽度, HeightPaper: 模板高度}
* @param {*} printContent 发货内容
* @returns {*} 分页后的发货内容数组，需要增加的分页页数
*/
function getBreakPageData(tempInfo, printContent) {
    // 1. 分页模板每行的全角占位数，每页的行数，每页可容纳的字符数
    const { rowMaxNums, rows, charNumsPerPage } = getBreakPageConfigs(tempInfo);
    // 2. 根据换行符将发货信息分割成宝贝信息数组
    const maxEmptyRows = 3,     // 每页底部最多留白3行
        printContentArr = printContent.split(/\n/g);
    let strRowsSumTemp = 0, // 临时存储最后一页（即当前页）的行数
        pageList = [];  // 分页数组

    for (let i = 0; i < printContentArr.length; i++) {
        const currentStr = printContentArr[i];  // 当前条宝贝信息
        if (!currentStr) continue;
        // 若最后一页的行数为0，且当条宝贝信息存在内容，本次需新增分页
        strRowsSumTemp === 0 && pageList.push('');

        let currentPageIndex = pageList.length - 1,   // 当前页的索引
            currentPageStr = pageList[currentPageIndex];   // 当前页的字符串
        const restRows = rows - strRowsSumTemp, // 当前页剩余可排列的行数
            strRows = Math.ceil(caculateCharLength(currentStr) / rowMaxNums);  // 当前宝贝所占的行数

        // ——————⬇️ ⬇️ 宝贝信息未溢出当前页 ⬇️ ⬇️ ——————
        // 1. 若当前宝贝信息未溢出当前页，则直接追加
        if (strRows <= restRows) {
            strRowsSumTemp += strRows;
            pageList[currentPageIndex] = currentPageStr + currentStr + '\n';
            // 如果刚好本页排满，将strRowsSumTemp重置代表下次需新增分页
            if (strRowsSumTemp === rows) strRowsSumTemp = 0;
            continue;
        }
        //  ——————⬇️ ⬇️ 宝贝信息行数溢出当前页、宝贝信息行数不超过「每页底部最多留白的行数」 ⬇️ ⬇️ ——————
        // 不做截断处理，直接将该条宝贝信息追加到新分页中
        if (strRows <= maxEmptyRows) {
            strRowsSumTemp = strRows; // 新分页的行数即当前宝贝信息的行数
            pageList[++currentPageIndex] = currentStr + '\n';
            continue;
        }
        // ——————⬇️ ⬇️ 宝贝信息行数溢出当前页、宝贝信息超过「每页底部最多留白的行数」、当前页面的剩余行数不超过「每页底部最多留白的行数」 ⬇️ ⬇️ ——————
        // 3. 若宝贝信息小于一个标准页，则新增分页，将该条宝贝信息推到新分页去处理。
        if (restRows <= maxEmptyRows && strRows <= rows) {
            // 将该条宝贝信息推到新分页
            pageList[++currentPageIndex] = currentStr + '\n';
            strRowsSumTemp = strRows;
            continue;
        }
        // 4.宝贝信息大于一个标准页，
        // 4.1 为避免底部留白过多，将宝贝信息进行部分截取填满本页，截取剩余的字符串排入新分页
        const [slicedStrForCurrentPage, restStr] = sliceStr(currentStr, 0, restRows * rowMaxNums);
        pageList[currentPageIndex] = currentPageStr + slicedStrForCurrentPage; // 截断后的字符串拼接到当前页
        // 4.2 对截取剩余的字符串，根据其所占用的分页数循环分页
        const restStrLength = caculateCharLength(restStr),
            restStrRows = Math.ceil(restStrLength / rowMaxNums),    // 剩余字符串所占用的行数
            restStrPages = Math.ceil(restStrLength / charNumsPerPage);    // 剩余字符串所需占用的分页数
        if (restStrPages === 1) {
            pageList[++currentPageIndex] = restStr + '\n';
            strRowsSumTemp = restStrRows;
            continue;
        }
        let slicedStr,      // 截取得到的字符串
            nextSliceStr = restStr;   // 下一串需要截取的字符串
        // 4.3 循环分页数，截取整页的字符数量追加到新分页中
        for (let i = 0; i < restStrPages; i++) {
            [slicedStr, nextSliceStr] = sliceStr(nextSliceStr, 0, charNumsPerPage);
            pageList[currentPageIndex + i + 1] = slicedStr;
        }
        strRowsSumTemp = caculateCharLength(slicedStr);   // 最后一页的行数值为本次循环分割剩余的字符行数
    }
    return {
        pageNum: pageList.length,
        printContentArr: pageList
    }
}
function getBreakPageConfigs(tempInfo) {
    let { fInfoFontSize, WidthPaper, HeightPaper } = tempInfo;
    const modeSize = `${WidthPaper / 100}_${HeightPaper / 100}`;
    // 模板尺寸和分页模板发货内容宽高的对应关系(单位为mm) mm * 3.78 = px， px / 3.78 = mm
    const beakPageFInfoSizeObj = {
        '7.6_13': { pageWidth: 268, pageHeight: 100 * 3.78 },        // 底部留白5cm pageHeight: ( 130 - 30 ) * 3.78
        '10_15': { pageWidth: 96 * 3.78, pageHeight: 120 * 3.78 },
        '10_18': { pageWidth: 96 * 3.78, pageHeight: 150 * 3.78 },
        '10_20': { pageWidth: 96 * 3.78, pageHeight: 170 * 3.78 },
        '10_21': { pageWidth: 96 * 3.78, pageHeight: 170 * 3.78 },
    }
    const { pageWidth, pageHeight } = beakPageFInfoSizeObj[modeSize] || beakPageFInfoSizeObj['7.6_13'];
    // 2. 计算当前分页模板可以容纳行和列
    const { lineHeight, letterSpacing } = getFontConfigs(fInfoFontSize);
	const rowMaxNums = Math.floor(pageWidth / (fInfoFontSize * letterSpacing)) - 2;    // 每行的全角占位数 留两个字符的余量，避免打印字体偏差换行
    const rows = Math.floor(pageHeight / (fInfoFontSize * lineHeight)) - 2;   // 每页的行数, 留白2行
    const charNumsPerPage = rows * rowMaxNums; // 每页的全角占位数
    console.log(`行字符数: ${rowMaxNums}, 行数: ${rows}`)
    return { rowMaxNums, rows, charNumsPerPage }
}

/**
 * 分页子方法——以一个全角字符的像素宽度为基准，计算出全角字符的长度
 * @param {string} str 需要计算的字符串
  * @returns {number} 字符长度
 */
function caculateCharLength(str = '') {
    if (typeof str != "string") return 0;
    // 中文汉字占位1个字符宽度；中文符号占位2个 字符宽度；英文字符占位0.5个字符宽度
    const configs = {
        // 全角字符
        fullWidthChar: { reg: /[^\x00-\xff]/g, widthScale: 1 },
        // 半角字符
        halfWidthChar: { reg: /[\x00-\xff]/g, widthScale: 0.5 },
    }
    let length = 0;
    for (let key in configs) {
        let item = configs[key];
        length += (str.match(item.reg) || []).length * item.widthScale;
    }
    // 存在小数点时，向上取整
    return Math.ceil(length);
}

/**
 * 分页子方法——从指定位置，截取指定占用的全角字符长度的字符串
 * @param {string} str 需要截取的字符串
 * @param {number} start 截取起始处的索引
 * @param {number} length 需要截取的全角字符长度
 * @returns {array} [截取的结果， 剩余的字符串]
 */
function sliceStr(str = '', start = 0, length = 0) {
    function getCharLength(char) {
        return char.match(/[\x00-\xff]/g) ? 0.5 : 1;
    }
    const arr = Array.from(str);    // 将字符串分割为单个字符数组
    let charNumSum = 0;  // 转换为全角字符的总数
    let index;
    for (index = start; index < arr.length - 1; index++) {
        let charLength = getCharLength(arr[index]); // 当前字符的占位
        charNumSum += charLength;
        if (Math.ceil(charNumSum) > length) break;  // 如果达到需要截取的全角字符长度，则记录当前索引作为截断位置
    }
    return [arr.slice(start, index).join(''), arr.slice(index).join('')];
}

/**
   * 分页子方法——根据字号获取对应的行高系数和字宽系数
   * @param {number}  fontSize 字号
    * @returns {lineHeight, letterSpacing} 对应的行高系数和字间距系数
   */
function getFontConfigs(fontSize = 12) {
    const LINEHEIGHTS = {
        DEFAULT: 1.05,  // 默认行高为1.05
        SPECIAL: 1.15   // 特殊情况行高为1.15
    }
    const LETTERSPACINGS = {
        DEFAULT: 1, // 默认字间距为1
        SPECIAL: 0.95   // 特殊情况字间距为0.95
    }
    let lineHeight = LINEHEIGHTS['DEFAULT'], letterSpacing = LETTERSPACINGS['DEFAULT'];
    // 当字号是2的倍数 或者 除4余1时，为特殊字间距情况
    if (fontSize % 2 === 0 || fontSize % 4 === 1) {
        letterSpacing = LETTERSPACINGS['SPECIAL'];
    }
    // 当字号是4的倍数，行高取1.15
    if (fontSize % 4 === 0) {
        lineHeight = LINEHEIGHTS['SPECIAL'];
    }
    return { lineHeight, letterSpacing }
}
export { checkedBreakPage };
