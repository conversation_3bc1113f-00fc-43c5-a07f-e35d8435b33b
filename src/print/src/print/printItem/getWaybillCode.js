/**
 * 1. 批量获取单号接口：参数组装，批量接口调用，数据处理。
 * 2. 多单号选择设置
 * 3. 原单号选择设置
 */
import * as printItem from './index';
import model from './../../common/model.ts';
import { post } from '../../common/http';
import { showTemplateMain, addKddTemplate } from './compPrint';
import { template } from './template';
import { getDefaultFjrInfo } from './getFjr';
import { isEncrypted } from '../utils/crypto';
import urlHelper from '../../common/urlhelp.js';

import {
	isDYEncryptTpl,
	isKsEncryptTpl,
	isYzEncryptTpl,
	isPddEncryptTpl,
	isCNEncryptTpl,
	isXHSEncryptTpl,
	isSPHEncryptTpl
} from '../utils/tpl';
import { platformConfig, isJDNewWjTpl } from '../printFlow/platformConfig';
import { getVirtualMobileAddress } from './format';

/**
 * 多线程处理
 *
 * @template T
 * @param {{
 *  params: T[]
 *  process: (param: T, threadOption: any) => any
 *  done: (config: any) => any
 * }} param0 参数以及回调
 * @param {{
 *  max: number
 * }} param1 多线程配置
 */
function MultiThread(
	{ params = [], process, fail, done } = {},
	{ max = 4 } = {},
) {
	const conf = {
		// 线程最大个数
		max,
		// 当前处理的参数下标
		idx: 0,
		cancel: false,
		cancelReason: '',
		// 错误的线程结果和下标
		errors: [],
		// 成功的线程结果和下标
		done: [],
		// 线程实例
		threads: [],
	};
	conf.max = window?.erpData?.waybillGetConcurrentCount || 4
	async function Thread(opt) {
		if (conf.idx >= params.length || conf.cancel) {
			return;
		}

		const idx = conf.idx++;
		const param = params[idx];

		try {
			const res = await process(param, opt);
			conf.done.push({
				idx,
				res,
			});
		} catch (error) {
			const opt = {
				idx,
				error,
			};
			conf.errors.push(opt);
			await fail(param, opt);
		}

		await Thread(opt);
	}

	return {
		// 多线程相关配置以及结果
		conf,
		// 开始创建多线程并执行
		async start() {
			for (let index = 0; index < conf.max; index++) {
				conf.threads.push(
					Thread({
						name: 'Thread:' + index,
					}),
				);
			}

			await Promise.all(conf.threads);
			done && done(conf);
		},
		// 取消多线程，并给出取消原因
		cancel(reason) {
			if (conf.cancel) {
				return;
			}

			conf.cancel = true;
			conf.cancelReason = reason;
		},
	};
}
// NOTE 批量与单个申请混合
async function getWaybillCodeNew(arg) {
	const {
		orderList,
		temp,
		originTempGroup,
		elecNoCount,
		sfZDQuantity,
		oldYdNos,
		printOp,
		wdSet,
		isUseOrderFjr = false,
		isNewEleNo,
		loginUserId,
		modeInfo,
		hasWWChat,
		goodsDescription = '',
		packagingDescription,
		afterGetWaybillCodeHook,
		afterContinuePrintHook,
		afterGetElecErrorHook,
		printInterruptNotify,
		isZiMuJian,
		processBatch,
		isScan,
		ordinary,
		weight,
		printSource,
	} = arg;
	// 优先区分来源单打还是批打，如果是打单需要将模板处理成模板组，如果是批打则根据配置判断是否需要批量取号
	let groupTemp = {}
	let tempDetailObj = {}
	let wdInfo = {}
	let tempObj = {}
	if (printSource == 'printSingle') {
		// 单打
		groupTemp = {
			enableStatus: temp.enableStatus,
			exCode: temp.ExCode,
			groupName: temp.ExcodeName,
			userId: temp.Exuserid,
			userTemplateList: [
				{
					exCode: temp.ExCode,
					exId: temp.Exid,
					exName: temp.ExcodeName,
					expressType: temp.KddType,
					subUserId: 0,
					userId: temp.Exuserid,
					userTemplateId: temp.Mode_ListShowId
				}
			]
		}
		tempDetailObj[temp.KddType] = modeInfo
		wdInfo[temp.KddType] = wdSet
		tempObj[temp.KddType] = temp
	} else {
		groupTemp = temp
		tempDetailObj = modeInfo
		wdInfo = wdSet
		tempObj[temp.KddType] = temp

	}
	let batchGetYdnoConfigList = (window?.erpData?.batchGetYdnoConfigList || {})

	// 先获取批量取号配置，将订单分成两部分，批量取号一部分，常规取号一部分
	const batchOrder = []
	const singleOrder = []
	orderList.forEach(item => {
		let batchGetYdnoConfig = batchGetYdnoConfigList[item.kddType]
		if (batchGetYdnoConfig?.batchGetElecSwitch) {
			batchOrder.push(item)
		} else {
			singleOrder.push(item)
		}
	})



	const kddFjrMap = arg.fjrInfo;

	let _tempService = {}
	for (let k in tempDetailObj) {
		tempDetailObj[k].ModeListShow.styleId = tempDetailObj[k].ModeList.StyleId
		let _modeLogisticsItems = (tempDetailObj[k] || {}).ModeLogisticsItems //模板的服务
		//托寄物自己加的，不传给平台
		_modeLogisticsItems = _modeLogisticsItems?.filter(o => o.serviceCode != 'tjw')
		if (
			!arg.tempService &&
			(k == '3' || k == '7') &&
			_modeLogisticsItems &&
			_modeLogisticsItems.length
		) {
			//收集菜鸟推荐模板的服务
			_tempService[k] = [];
			_modeLogisticsItems.map((_v) => {
				_tempService[k].push({
					serviceValue: _v.serviceValue,
					serviceCode: _v.serviceCode,
				});
			});
		}
	}
	arg.tempService = _tempService;

	const htmlStr = template.getWaybillCodeHtmlByMerge(groupTemp, orderList, hasWWChat);
	// 定义好需要处理的数据，并收集结果
	let batchGroupTemp = {};
	let batchTempDetailObj = {};
	let batchWdInfo = {};
	let batchTempObj = {};
	let singleGroupTemp = {};
	let singleTempDetailObj = {};
	let singleWdInfo = {};
	let singleTempObj = {};
	// 模板分一下
	for (let k in tempDetailObj) {
		if (batchGetYdnoConfigList[k]?.batchGetElecSwitch) {
			batchGroupTemp[k] = groupTemp[k]
			batchTempDetailObj[k] = tempDetailObj[k]
			batchWdInfo[k] = wdInfo[k]
			batchTempObj[k] = tempDetailObj[k].ModeListShow
		} else {
			singleGroupTemp[k] = groupTemp[k]
			singleTempDetailObj[k] = tempDetailObj[k]
			singleWdInfo[k] = wdInfo[k]
			singleTempObj[k] = tempDetailObj[k].ModeListShow
		}
	}
	return new Promise((resolve) => {
		async function getYdnoProcess(modelDom) {
			let elecNoArr = []
			let ydnoList = []
			let errorOrderIds = []
			let errMsgList = []
			let errOrderInfos = []
			// 批量取号
			if (batchOrder.length) {
				const { elecNolist, list, errorMessageList, errorOrderIdList, errorOrderList } = await getWaybillCodes({ ...arg, orderList: batchOrder, htmlStr, modeInfos: batchTempDetailObj, tempObj: batchTempObj, wdSetObj: batchWdInfo, tempInfo: batchTempObj, modelDom })
				errMsgList = errMsgList.concat(errorMessageList || [])
				errorOrderIds = errorOrderIds.concat(errorOrderIdList || [])
				ydnoList = ydnoList.concat(list || [])
				elecNoArr = elecNoArr.concat(elecNolist || [])
				errOrderInfos = errOrderInfos.concat(errorOrderList || [])
			}
			if (singleOrder.length) {
				// 单线程处理
				const { elecNolist, list, errorMessageList, errorOrderIdList, errorOrderList } = await singleGetWaybillCode({ ...arg, orderList: singleOrder, htmlStr, modeInfo: tempDetailObj, tempObj: batchTempObj, wdSet: wdInfo, tempInfo: batchTempObj, kddFjrMap, tempService: _tempService, modelDom })
				errMsgList = errMsgList.concat(errorMessageList || [])
				errorOrderIds = errorOrderIds.concat(errorOrderIdList || [])
				ydnoList = ydnoList.concat(list || [])
				elecNoArr = elecNoArr.concat(elecNolist || [])
				errOrderInfos = errOrderInfos.concat(errorOrderList || [])
			}
			model.remove(modelDom);
			if (elecNoArr.length > 0) {
				afterGetWaybillCodeHook(elecNoArr, (originTempGroup || temp), isNewEleNo); //更新订单电子面单号
			}
			let allSuccess = ydnoList.length == orderList.length
			// 对取号完的订单恢复初始顺序,将取号完成的ydnoList按照传入的orderList的顺序排列
			// 对取号完的订单恢复初始顺序,将取号完成的ydnoList按照传入的orderList的顺序排列
			const ydnoMap = {};
			// 创建映射，时间复杂度O(n)
			ydnoList.forEach(ydnoItem => {
				ydnoMap[ydnoItem.togetherId] = ydnoItem;
			});

			// 按原始顺序重建列表，时间复杂度O(n)
			let newOrderList = [];
			orderList.forEach(item => {
				const ydnoItem = ydnoMap[item.togetherId];
				if (ydnoItem) {
					newOrderList.push(ydnoItem);
				}
			});

			if (allSuccess) {
				resolve(newOrderList)
			} else {
				afterGetElecErrorHook && afterGetElecErrorHook(errOrderInfos);
				printInterruptNotify && printInterruptNotify()
				// 取号异常信息收集
				window.errorCollection?.customMessageUpload({
					type: '模板取号失败',
					data: {
						errorMsgList: errMsgList,
					}
				});
				let msgStr = errMsgList.join('<br>')
				const isContinue = await _getWaybillCodeFailInfo(
					msgStr,
					{
						// templateId: temp.Mode_ListShowId,
						showContinue: newOrderList.length > 0,
					},
				);

				if (isContinue) {
					afterContinuePrintHook && afterContinuePrintHook(errorOrderIds);
					resolve(newOrderList);
				}
			}



		}
		!!isScan ? getYdnoProcess() : model({
			width: 500,
			height: 300,
			content: htmlStr,
			cssType: 'yellow',
			shown: getYdnoProcess,
		});
	})

}
// NOTE 单个申请流程
async function singleGetWaybillCode(arg) {
	const {
		orderList,
		temp,
		elecNoCount,
		sfZDQuantity,
		oldYdNos,
		printOp,
		wdSet,
		isUseOrderFjr = false,
		isNewEleNo,
		loginUserId,
		modeInfo,
		hasWWChat,
		goodsDescription = '',
		packagingDescription,
		kddFjrMap,
		isZiMuJian,
		processBatch,
		isScan,
		ordinary,
		modelDom,
		tempService,
		weight
	} = arg;

	return new Promise((resolve) => {
		function processShown() {
			const $progressDom = modelDom && modelDom.querySelector('.req_express_num');
			const $nickDom = modelDom && modelDom.querySelector('.req_express_nick');
			// TODO 改造：提前单独处理kdInfo，再统一处理tradeInfo
			// tradeInfo为数组，上限50个订单


			let overIndex = Number($progressDom?.textContent || '0'),
				errorMsgList = [], //获取单号错误信息收集
				elecNolist = [];
			const multiThread = MultiThread({
				params: orderList,
				async process(v) {
					const isBlindFlag = false;

					const getWaybillOption = {
						isBlindFlag,
						temp: modeInfo[v.kddType]?.ModeListShow,
						modeInfo: modeInfo[v.kddType],
						elecNoCount,
						sfZDQuantity,
						oldYdNos,
						printOp,
						wdSet: wdSet[v.kddType],
						isUseOrderFjr,
						kddFjrMap,
						isNewEleNo,
						loginUserId,
						goodsDescription,
						packagingDescription,
						tempService: tempService[v.kddType],
						isZiMuJian,
						processBatch,
						weight,
						isScan

					};

					const waybillCodeJson =
						(await _getWaybillCode(v, getWaybillOption)) || {};

					//更改获取单号进度
					$progressDom && ($progressDom.textContent = overIndex + 1);
					// 昵称的字段需要做个加密的兼容处理
					$nickDom && ($nickDom.textContent = v.buyerNick_origin || v.buyerNick);

					if (waybillCodeJson.result != 100) {
						throw waybillCodeJson;
					}

					const data = waybillCodeJson.data;
					const requestParams = waybillCodeJson.params;

					// 德邦用户是否权限打印字母件判断 字母件大于0&&没有子单号的情况阻止打印
					// Todo, 需要确认这里怎么处理
					if (modeInfo[v.kddType]?.ModeListShow.ExCode == 'DBKD' && modeInfo[v.kddType]?.ModeListShow.KddType == 2) {
						if (
							arg.sfZDQuantity > 0 &&
							(data.sfZDydId == '' || !data.sfZDydId)
						) {
							const errorMsg =
								'账号尚未开通德邦子母件服务，请联系快递网点开通服务后重试';
							throw new Error(errorMsg);
						}
					}

					v = _dealWithElecData({
						data,
						temp: modeInfo[v.kddType]?.ModeListShow,
						v,
						elecNoCount,
						isZiMuJian
					});
					let allYdNos = [...data.ydId.split(','), ...(data.sfZDydId ? data.sfZDydId.split(',') : [])]
					if (data.pYdNo && !allYdNos.includes(data.pYdNo)) {
						allYdNos.unshift(data.pYdNo)
					} else if (data.pYdNo && allYdNos.includes(data.pYdNo)) {
						let pYdNoIndex = allYdNos.indexOf(data.pYdNo)
						allYdNos.splice(pYdNoIndex, 1); // 删除元素
						allYdNos.unshift(data.pYdNo);
					}
					elecNolist.push({
						cnpName: data.cpName, //菜鸟配模板 推荐的快递公司
						cnpCode: data.cpCode, //菜鸟配模板 推荐的快递公司TmsCode
						ydNo: allYdNos.join(','),
						pYdNo: data.pYdNo || '',
						togetherId: v.togetherId,
						packageId: data.packageId,
						intelliYdNo: data.intelliYdNo, //标识订单号是否是智选单号
						senderMobile: requestParams?.tradeInfo?.senderMobile
					});

					overIndex++;
				},
				// 失败处理
				fail(v, { error } = {}) {
					console.log(error);
					const errorMsg = error.message || error.errorMessage || '';

					const cancelReasons = ['可用快递单号不足', '没有足够的快递单号', '电子面单账户余额不足', '单号余额不足'];


					const msg = _dealWithElecMsg({
						buyerNick: v.buyerNick || v.receiverName,
						msg: errorMsg,
						resultCode: error.result,
						printOp: printOp,
						orderMsg: v,
					});
					errorMsgList.push(`【订单编号：${v.togetherId}】：<br/>${msg}`);
					overIndex++;
				},
				// 所有申请结束
				async done(conf) {

					// 筛选成功的订单
					const list = orderList.filter((_, idx) =>
						conf.done.find((t) => t.idx === idx),
					);

					const isAllSuccess = conf.done.length === orderList.length;
					if (isAllSuccess) {
						resolve({
							list,
							elecNolist
						});
					} else {
						// 筛选失败的订单
						const errorOrderList = orderList.filter((_, idx) =>
							conf.errors.find(t => t.idx === idx),
						);
						const errorOrderIdList = errorOrderList.map(item => item.togetherId);
						// afterGetElecErrorHook && afterGetElecErrorHook(errorOrderList);
						// printInterruptNotify && printInterruptNotify()
						// todo: 需要选择是否继续

						resolve({
							list,
							elecNolist,
							errorMessageList: errorMsgList,
							errorOrderIdList,
							errorOrderList
						});
					}
				},
			});

			multiThread.start();

			modelDom && modelDom.addEventListener('click', function (e) {
				const target = e.target;
				if (target.classList.contains('closeModel')) {
					multiThread.cancel('Canceled by user');
				}
			});

			//TODO 联系客服处理：各平台处理方式不一致
			// if(arg.hasWWChat){
			// }
		}
		processShown()
		// !!isScan ? processShown() : model({
		// 	width: 500,
		// 	height: 300,
		// 	content: htmlStr,
		// 	cssType: 'yellow',
		// 	shown: processShown,
		// });
	});
}
// NOTE 批量获取取号的快递信息
function getKdInfo(arg) {
	const {
		wdSetInfo = {},
		tempDetailInfo,
		tempDetailInfo: {
			ModeCustomerTemplate,
			ModeServiceItems,
			ModeAdvancedServices
		},
		kddTemp = {},
		singleSempService,
		sfZDQuantity = undefined,
		isZiMuJian,
		processBatch,
		weight,
	} = arg
	let kdInfo;
	if (kddTemp.KddType == 5 && tempDetailInfo.jdQl == 1) { //JD青龙面单的接口字段和菜鸟的差距很大
		kdInfo = {
			kdCode: kddTemp.ExCode,
			kdId: kddTemp.Exid,
			kdName: kddTemp.ExcodeName,
			styleId: kddTemp.styleId,
			modeListShowId: ~~kddTemp.Mode_ListShowId,
			// goodsDescription: arg.goodsDescription,
			//菜鸟面单获取单号所传递的信息
			useTaoBaoId: wdSetInfo.Exuserid,
			addreddId: wdSetInfo.AddressId,
			branchCode: wdSetInfo.Branch_code || wdSetInfo.branchCode || '',
			branchName: wdSetInfo.Branch_name || '',
			shared: wdSetInfo.shared,
			shareId: wdSetInfo.shareId,
			ownerId: wdSetInfo.ownerId,
			sonElecNoCount: arg.sonElecNoCount || arg.sfZDQuantity || arg.elecNoCount,
			processBatch,
			authodV2: true,
			customerCode: wdSetInfo.logisticsAccount,
		};
	} else {
		kdInfo = {
			processBatch,
			kdCode: kddTemp.ExCode,
			kdId: kddTemp.Exid,
			kdName: kddTemp.ExcodeName,
			styleId: kddTemp.styleId,

			//菜鸟面单获取单号所传递的信息
			useTaoBaoId: wdSetInfo.Exuserid,
			addreddId: wdSetInfo.AddressId,
			branchCode: wdSetInfo.Branch_code || '',
			branchName: wdSetInfo.Branch_name || '',
			sonElecNoCount: arg.sonElecNoCount || arg.sfZDQuantity || arg.elecNoCount,
			// goodsDescription: arg.goodsDescription,
			packagingDescription: arg.packagingDescription,

			//单号分享字段
			shared: wdSetInfo.shared,
			relationUserId: wdSetInfo.sharedUserId || wdSetInfo.relationUserId,
			ownerId: wdSetInfo.ownerId,
			isRelation: wdSetInfo.isRelation,
			wpType: wdSetInfo.wpType,
			shareId: wdSetInfo.shareId,
			paymentType: wdSetInfo?.paymentType,
			customerCode: wdSetInfo.logisticsAccount,
			weight,
			// 网点和抖音面单需要，所以提前了；
			modeListShowId: ~~kddTemp.Mode_ListShowId,
			sfZDQuantity,
			authodV2: true,

			subShareUserCode: wdSetInfo.subShareUserCode || '',
			// 视频号模板id
			...(kddTemp.KddType === 14 ? {
				platformUserTemplate: kddTemp.platformUserTemplate,
				merchantAccount: wdSetInfo.merchantAccount,
				sphBranchId: wdSetInfo.SellerId,
				branchCode: wdSetInfo.Branch_code,
				branchName: wdSetInfo.Branch_name
			} : {})
		};
	}
	// if(['SF'].includes(kddTemp.ExCode)){
	//     if(kdInfo.goodsDescription?.includes('订单商品作为托寄物')) kdInfo.goodsDescription = _getGoodsDescription(v)
	//     if(!kdInfo.goodsDescription) kdInfo.goodsDescription = '商 品'
	//  }
	//TODO 注意修改后styleId 76 49的快运面单
	if (!isZiMuJian && kddTemp.styleId != 76 && kddTemp.styleId != 49) {
		delete kdInfo.sonElecNoCount;
	}

	singleSempService && (kdInfo.modeLogisticsItems = singleSempService); //传递模板勾选的服务
	if (kddTemp.KddType == '3') {
		kdInfo.templateUrl = 'https://cloudprint.cainiao.com/template'; //TODO 预留
		if (kddTemp.ExCode == 'SF') {    // 菜鸟顺丰添加 productCode
			kdInfo.productCode = ModeCustomerTemplate?.ServiceValue || '';
		}
	} else if (kddTemp.KddType == '8') {
		kdInfo.templateUrl = tempDetailInfo?.ModeListShow?.templateUrl || ''; //TODO 预留
	}
	if (kddTemp.KddType == '2' || kddTemp.KddType == '5') {
		//非菜鸟网点电子面单（韵达网点除外）及 菜鸟配模板 的发件人信息 从基础设置中获取
		kdInfo = Object.assign(
			kdInfo,
			{
				//网点面单所传递的信息
				customPassword: wdSetInfo.Password || '',
				customName: wdSetInfo.Username || '',
				monthCardNumber: wdSetInfo.Custid || '',
				companyName: wdSetInfo.F_com || '', // 暂时仅韵达网点有 2019.3.25
			},
			true,
		);

		if ((kddTemp.KddType == '5' && kddTemp.ExCode != 'JD') || isJDNewWjTpl(kddTemp)) {
			//jd面单需要取基础设置里的发件人和手机
			kdInfo = $.extend(
				kdInfo,
				{
					monthCardNumber: wdSetInfo.settlementCode || '',
					jdShopCode: wdSetInfo.JdShopCode || '', //京东快递新增京东店铺code，无该code无需传递
				},
				true,
			);
		}
	}


	const { ServiceValue = '', Svctypename = '', Svctypecode = '' } = ModeCustomerTemplate || {}; //erp顺丰需要传产品类型
	// const  serviceType = ModeServiceItems.find(o=> o.Dataname === 'yslx')
	let serviceType, jdTransTypeStr
	ModeServiceItems.forEach(o => {
		if (o.Dataname === 'pslx') {
			serviceType = o
		}
		if (o.Dataname === 'yslx') {
			jdTransTypeStr = o
		}
	})
	const sfKey = {
		'fkfs': 'payMethodStr',
		'yjkh': 'customerCode',
		'khbm': 'isvClientCode'
	}
	const modeServiceItems = []
	kdInfo.printSellerId = wdSetInfo.sellerId;
	kdInfo.printPlatform = wdSetInfo.printPlatform;
	ModeAdvancedServices.length && ModeAdvancedServices.forEach(it => {
		let kdinfoKey = sfKey[it.serviceKey]
		if (!!kdinfoKey) kdInfo[kdinfoKey] = it.serviceValue
	})
	ModeServiceItems.length && ModeServiceItems.forEach(it => {
		let kdinfoKey = sfKey[it.Dataname]
		if (!!kdinfoKey) kdInfo[kdinfoKey] = it.Defaultval
		if (kddTemp.KddType == '9') {
			modeServiceItems.push({
				serviceName: it.Itemname,
				serviceValue: JSON.stringify({ name: it.Defaultval, show: true, type: '', value: it.Reserve1 })
			})
		}
	})
	if (kddTemp.KddType == '16') {
		kdInfo.customerCode = wdSetInfo.logisticsAccount || '';
	}
	if (modeServiceItems.length) {
		kdInfo.modeLogisticsItems = modeServiceItems
	}
	if (['SF', 'DBKD', 'SFKY'].includes(kddTemp.ExCode) || (kddTemp.ExCode === 'JD' && kddTemp.KddType !== '7')) kdInfo.productCode = ServiceValue; //拼多多京东暂不传
	// 小红书、视频号圆通也需要productCode
	if (['YTO'].includes(kddTemp.ExCode) && [16, 14].includes(Number(kddTemp.KddType))) kdInfo.productCode = ServiceValue;
	if (['SF'].includes(kddTemp.ExCode) && kddTemp.KddType == '7') kdInfo.productName = Svctypename; //拼多多外顺丰
	if (kddTemp.ExCode === 'JD' && kddTemp.KddType == '5' && !isJDNewWjTpl(kddTemp)) {
		kdInfo.goodsType = serviceType.Defaultval; //京东传配送类型
		kdInfo.jdTransTypeStr = jdTransTypeStr?.Defaultval || ''; //京东传运输类型
	}
	// 后端接口迭代版本，避免线上不兼容
	kdInfo.printVersion = 'V2'
	return kdInfo
}
// NOTE 批量获取tradeInfo信息
function getTradeInfo(v, arg) {
	let {
		printOp,
		wdSetInfo = {},
		tempDetailInfo,
		kddTemp = {},
		kddFjrMap = {},
	} = arg;


	const _orders = (v.orders || []).map((k) => {
		// 订单标题判断顺丰-抖音电子面单，如果有简称，就传简称
		const isSFAndDY = kddTemp.KddType === 8 && kddTemp.ExCode === 'SF';

		const orderObj = {
			name:
				kddTemp.ExCode === 'BESTQJT'
					? k.skuPropertiesName + '_' + k.name //百世快运商品信息改成规格+商品标题
					: isSFAndDY ? (k.short || k.name) : k.name, // 订单标题判断顺丰-抖音电子面单，如果有简称，就传简称
			count: k.count,
			price: k.price,
			weight: k.weight,
		};
		if (kddTemp.KddType == 14) {
			orderObj.skuId = k.skuId
			orderObj.spuId = k.numIid
		}
		// 优先用简称，然后取标题
		const shouldUseShortFirst =
			// 顺丰网点, 满足顺丰用户： 希望用户不要买家看见商品全称的需求
			(kddTemp.KddType == 2 && kddTemp.ExCode == 'SF') ||
			// 抖音京东面单
			(kddTemp.KddType == 8 && kddTemp.ExCode == 'JD');

		if (shouldUseShortFirst) {
			orderObj.name = k.short || k.name;
		}

		return orderObj;
	});


	let kddFjr = Object.keys(v.kddFjr).length ? v.kddFjr : getDefaultFjrInfo(kddFjrMap, v.userId2); //发件人相关的 userId 是用的从库 id

	// FIXME:如果拼多多平台下，绑定了本店铺的默认发件人，但是打印的是非本店铺的订单，则会走到这部分逻辑
	// 这里的业务逻辑本就是有问题的，但是为了兼容线上版本，只能这样处理
	if (
		JSON.stringify(v.kddFjr) === '{}' &&
		comp.Print.Data.platform === 'pdd'
	) {
		kddFjr = getDefaultFjrInfo(kddFjrMap, comp.Print.Data.userInfo.userId);
	}

	let kdInfo;
	let tradeInfo;


	const encrypted = ((isEncrypted(v.s_mobile) || isEncrypted(v.s_phone)) && v.caid) || (kddTemp.KddType == 13 && !v.isDecrypted);
	const oaids = encrypted ? v.sidTrades.map(t => `${t.tid}:${t.caid}`) : [];
	const caids = encrypted ? v.sidTrades.map(t => `${t.caid}`) : [];
	const commonTradeInfo = {
		// 加密字段
		oaid: oaids[0] || '',
		oaids: oaids.join('|') || '',
		outerUserId: v.outerUserId,
		// outerTradeId: v.outerTradeId,
		// 订单来源
		tradeSource: v.tradeSource || '',
		// 加解密涉及的字段
		mobile: v.s_mobile || v.s_phone || '',
		tel: v.s_phone || '',
		receiverName: (v.s_name || '').replace(/\n/g, ''),
		receiverAddress: getVirtualMobileAddress(v.s_address, v.s_mobile, kddTemp, v.isVirtualTel),

		// 保留加密的谜文
		mobileEncrypt: v.s_encode_mobile, //  收件人手机号加密串
		receiverNameEncrypt: v.s_encode_name, //  收件人加密串
		receiverAddressEncrypt: v.s_encode_address, //  收件人详细地址加密串


		// 通用
		receiverCity: v.s_city,
		receiverCounty: v.s_county,
		receiverProvince: v.s_province,
		receiverTown: v.s_town,    //目前这个字段慎用，避免打印出来的面单纸上地址信息打印两份镇。（目前只有信丰用，获取大头笔使用）


		// pdd 字段
		mainTid: v.mainTid, // 手动合单时，主订单号
	};

	// 存在则赋值
	$.extend(commonTradeInfo, {
		sendToHomeKey: v.sendToHomeKey,     // 送货上门
	});

	if (encrypted && v.s_encode_mobile) {
		commonTradeInfo.mobile = v.s_encode_mobile;
		commonTradeInfo.tel = v.s_encode_phone;
		commonTradeInfo.receiverName = v.s_encode_name;
		commonTradeInfo.receiverAddress = getVirtualMobileAddress(v.s_encode_address, v.s_mobile, kddTemp, v.isVirtualTel);
	}

	if (kddTemp.KddType == 5 && tempDetailInfo.jdQl == 1) { //JD青龙面单的接口字段和菜鸟的差距很大
		tradeInfo = {
			...commonTradeInfo,
			buyerMessage: v.buyerMome || '', //买家留言
			buyerNick: v.buyerNick || '',
			nums: v.count || 1, //商品数量
			tids: v.tidOids.join('|'), //订单id串值(tid:oid,oid|tid1|tid2)
			orders: _orders,
			weight: v.hj_weight || 0,
			sellerFlag: v.sellerFlag || 0,
			taobaoId: v.userId || v.taobaoId,
			optionType: printOp, //操作来源    0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单
			packageId: (v.packageId || -1), //包裹号
			senderAddressDetail: wdSetInfo.address, //网点的地址
			senderArea: wdSetInfo.countryName,
			senderCity: wdSetInfo.cityName,
			senderProvince: wdSetInfo.provinceName,
			senderTown: wdSetInfo.countrysideName,
			senderMobile: wdSetInfo.F_mobile || wdSetInfo.F_tel,
			senderName: wdSetInfo.F_name,
			kddType: kddTemp.KddType,
			isCod: v.isCOD ? 1 : 0, //是否货到付款
			codPrice: parseFloat(v.codPrice), //代收金额
			payAmount: v.payAmount, //实付金额
			info: v.print_content || '', //sku信息
			sourceId: 1, //  1 快递助手 2 进销存
			sellerMome: v.sellerMome,    //卖家备注  韵达网点面单需要，生成韵达打印密串
			printSenderName: kddFjr.name,   //传递打印的发件人姓名， 用于底单查询时显示发件人姓名，后端要求获取单号接口 和 保存底单日志接口 都传递该字段 TODO
			tradeSource: v.source, // 订单来源
			sellerNick: v.seller_nick, //平台商家名称
			outerTradeId: (v.outerTradeId || []).join('|'),
			tradeReceiverList: v.tradeReceiverList,
			saleUserId: v.distributorUserId || '',
			tradeEncodeType: v.tradeEncodeType,
			goodsNumGift: v.goodsNumGift,
			hlPlatformType: v.hlPlatformType,
			isEncrypted: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.isEncrypted,
			hlTradeInfo: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.downstreamEncryptDetails,
		};
	} else {

		tradeInfo = {
			...commonTradeInfo,
			buyerMessage: v.buyerMome || '', //买家留言
			buyerNick: v.buyerNick || '',
			buyerOpenUid: v.buyerOpenUid || '',
			nums: v.count || 1, //商品数量
			tids: v.tidOids.join('|'), //订单id串值(tid:oid,oid|tid1|tid2)
			tradeReceiverList: v.tradeReceiverList,
			orders: _orders,
			weight: v.hj_weight || 0,
			sellerFlag: v.sellerFlag || 0,
			taobaoId: v.userId || v.taobaoId,
			threePlTiming: v.threePlTiming,//天猫直送申请订单号相关属性, 天猫直送必须使用才年电子面单，申请单号时必须传递这个值，不是的不能传递
			optionType: printOp, //操作来源    0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单
			packageId: (v.packageId || -1), //包裹号
			senderAddressDetail: wdSetInfo.F_addr, //网点的地址
			senderArea: wdSetInfo.F_q,
			senderCity: wdSetInfo.F_c,
			senderProvince: wdSetInfo.F_p || wdSetInfo.F_s,
			senderTown: wdSetInfo.F_town,
			senderMobile: wdSetInfo.F_mobile || wdSetInfo.F_tel,
			senderName: wdSetInfo.F_name,
			kddType: kddTemp.KddType,
			isCod: v.isCOD ? 1 : 0,
			codPrice: parseFloat(v.codPrice), //代收金额
			payAmount: v.payAmount, //实付金额
			postFee: parseFloat(v.hj_post_fee || 0),          //到付金额需要该字段
			info: v.print_content || '', //sku信息
			sourceId: 1, //  1 快递助手 2 进销存
			sellerMome: v.sellerMome,    //卖家备注  韵达网点面单需要，生成韵达打印密串
			printSenderName: kddFjr.name,   //传递打印的发件人姓名， 用于底单查询时显示发件人姓名，后端要求获取单号接口 和 保存底单日志接口 都传递该字段 TODO
			buyerNickSensitive: v.buyerNick_origin || '', // 加密数据的脱敏值
			receiverNameSensitive: v.s_name_origin || '', // 加密数据的脱敏值
			receiverAddressSensitive: v.s_address_origin || '', // 加密数据的脱敏值
			mobileSensitive: v.s_mobile_origin || v.s_mobile || '', // 加密数据的脱敏值 s_mobile_origin字眼没有，做个兼容
			tradeSource: v.source, // 订单来源
			sellerNick: v.seller_nick, //平台商家名称
			outerTradeId: (v.outerTradeId || []).join('|'),
			saleUserId: v.distributorUserId || '',
			receiverId: (v.receiverId || []).join('|'),
			memberId: v.memberId,
			goodsNumGift: v.goodsNumGift,
			// 回流订单数据
			hlPlatformType: v.hlPlatformType,
			isEncrypted: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.isEncrypted,
			hlTradeInfo: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.downstreamEncryptDetails,
			consolidateType: v.consolidateType || '',
			tradeEncodeType: v.tradeEncodeType,

		};
	}
	// 托寄物
	tradeInfo.goodsDescription = arg.goodsDescription;
	// 顺丰托寄物特殊处理
	if (['SF'].includes(kddTemp.ExCode)) {
		if (tradeInfo.goodsDescription?.includes('订单商品作为托寄物')) tradeInfo.goodsDescription = _getGoodsDescription(v)
		if (!tradeInfo.goodsDescription) tradeInfo.goodsDescription = '商 品'
	}

	// 测试
	tradeInfo.senderTown == '<nil>' ? tradeInfo.senderTown = '' : null;

	// if (printOp == 4) { //手工订单申请单号，需要传递
	tradeInfo.realTid = v.realTid || '';
	//     tradeInfo.insurePrice = parseFloat(v.bjje || 0);
	// }else{
	tradeInfo.insurePrice = parseFloat(v.payAmount || 0);
	// }


	if (kddTemp.KddType == '2' || kddTemp.KddType == '5') {
		v.cutid = wdSetInfo.Custid || ''; //SF 速尔 月结卡号
		if (kddTemp.ExCode != 'YUNDA' && kddTemp.KddType != '5') {
			//非韵达网点面单和jd电子面单 获取单号的发件信息走基础设置里面的
			tradeInfo = Object.assign(
				tradeInfo,
				{
					senderAddressDetail: kddFjr.address,
					senderArea: kddFjr.district,
					senderCity: kddFjr.city,
					senderProvince: kddFjr.province,
					senderMobile: kddFjr.phone,
					senderName: kddFjr.name,
				},
				true,
			);
		} else if ((kddTemp.KddType == '5' && kddTemp.ExCode != 'JD') || !isJDNewWjTpl(kddTemp)) {
			//jd面单需要取基础设置里的发件人和手机
			tradeInfo = Object.assign(
				tradeInfo,
				{
					senderMobile: kddFjr.phone,
					senderName: kddFjr.name,
					senderAddressDetail:
						kddFjr.province +
						kddFjr.city +
						kddFjr.district +
						kddFjr.address,
				},
				true,
			);
			v.sf_city = tradeInfo.senderCity; //JD面单添加的 始发城市 字段
		}
	} else if (kddTemp.KddType == '7') {
		tradeInfo.senderName = kddFjr.name || '';
		tradeInfo.senderMobile = kddFjr.phone || '';
	} else if (!tradeInfo.senderMobile) {
		//解释：电子面单 网点设置中发件人电话信息不完善时 用基础设置中发件人信息补充
		tradeInfo.senderName = tradeInfo.senderName || kddFjr.name || '';
		tradeInfo.senderMobile = kddFjr.phone || '';
	}


	// sellerId可能会因为数据没同步导致为空
	tradeInfo.sellerId = v.sellerId;
	tradeInfo.platform = v.platform;

	const sender = kddFjrMap.sender?.[v.sellerId]?.info || kddFjr;
	if (sender?.name && sender?.phone) {
		tradeInfo.senderMobile = sender.phone;
		tradeInfo.senderName = sender.name;
	}
	if (kddTemp.KddType == '14') {
		tradeInfo.senderAddressDetail = sender?.address || kddFjr.address || ''; //网点的地址
		tradeInfo.senderArea = sender?.district || kddFjr?.district || '';
		tradeInfo.senderCity = sender?.city || kddFjr?.city || '';
		tradeInfo.senderProvince = sender?.province || kddFjr.province || '';
		// tradeInfo.senderTown = sender.F_town ,
		// tradeInfo.senderMobile = sender.F_mobile || sender.F_tel,
		// tradeInfo.senderName = sender.F_name,
	}
	// 来源是手工单的一律优先取订单发件人
	if (v.source === 'HAND') {
		// tradeInfo.senderAddressDetail = kddFjr.address; //网点的地址
		// tradeInfo.senderArea = kddFjr.district;
		// tradeInfo.senderCity = kddFjr.city;
		// tradeInfo.senderProvince = kddFjr.province;
		// tradeInfo.senderTown = kddFjr.town || '' ;
		tradeInfo.senderMobile = kddFjr.phone || '';
		tradeInfo.senderName = kddFjr.name || '';
	}

	tradeInfo.tids = v.tidWithOids.join('|');
	tradeInfo.ptTids = v.ptTidWithOids.join('|');

	tradeInfo.caid = caids[0] || '';
	tradeInfo.caids = caids.join('|') || '';

	delete tradeInfo.oaid;
	delete tradeInfo.oaids;

	tradeInfo.idxEncodeReceiverMobile = v.idxEncodeReceiverMobile;
	tradeInfo.idxEncodeReceiverAddress = v.idxEncodeReceiverAddress;
	tradeInfo.idxEncodeReceiverName = v.idxEncodeReceiverName;

	tradeInfo.receiverNameMask = v.receiverNameMask;
	tradeInfo.receiverPhoneMask = v.receiverPhoneMask;
	tradeInfo.receiverMobileMask = v.receiverMobileMask;
	tradeInfo.receiverAddressMask = v.receiverAddressMask;

	tradeInfo.receiverMobile = checkReceiverMobile(kddTemp.KddType, v.receiverMobile);
	tradeInfo.receiverTel = v.s_phone;
	tradeInfo.receiverName = v.receiverName;
	tradeInfo.receiverAddress = v.receiverAddress;

	tradeInfo.encodeTid = v.encodeTid;

	tradeInfo.isDecrypted = v.isDecrypted;
	tradeInfo.payment = v.payment;

	for (const tItem in tradeInfo) {
		if (tradeInfo[tItem] == 'null') {
			delete tradeInfo[tItem];
		}
	}

	if (printOp != 4 && (!v.orders || v.orders.length === 0)) {
		Tatami.showFail(
			(v.buyerNick || '') +
			'无可打印的宝贝,请在订单列表右侧点击详情，勾选要打印的宝贝再试',
		);
		return;
	}



	return tradeInfo;
}
// NOTE 批量申请流程
async function getWaybillCodes(arg) {
	let {
		orderList,
		elecNoCount,
		oldYdNos,
		printOp,
		wdSetObj = {},
		modeInfos,
		tempObj = [],
		tempInfo,
		isNewEleNo,
		modelDom,
		isZiMuJian,
		tempService = {}
		// loginUserId,
	} = arg;
	// 整合聚合打印与单打，传入的信息都以模板组的形式，单打视为只存在一个模板的模板组
	// 多模板打印需要先细分订单
	let orderObj = {}
	orderList.forEach(order => {
		let otherInfo = {
			tempDetailInfo: modeInfos[order.kddType],
			kddTemp: tempObj[order.kddType],
			wdSetInfo: wdSetObj[order.kddType],
			...arg
		}
		let tradeInfo = getTradeInfo(order, otherInfo)
		if (orderObj[order.kddType]) {
			orderObj[order.kddType].push({
				tradeInfo,
				order
			})
		} else {
			orderObj[order.kddType] = [{
				tradeInfo,
				order
			}]
		}
	})
	let paramsArr = []
	// 按照模板分类
	for (const key in modeInfos) {
		const tempDetail = modeInfos[key]
		const tempInfo = tempDetail?.ModeListShow
		const isPddKuaiYun = comp.base.getPddTempStyle(
			'pddKuaiYunZmj',
			tempInfo.styleId,
			tempInfo.ExCode,
		);

		const isCaiNiaoKuaiYun = comp.base.getTempStyle('kuaiyun', tempInfo.styleId, tempInfo);

		const isDYKuaiYun = comp.base.getTempStyle('DYkuaiyun', tempInfo.styleId, tempInfo);
		if (isPddKuaiYun || isCaiNiaoKuaiYun || isDYKuaiYun) {      // 拼多多、菜鸟、抖音
			elecNoCount = Math.max(
				elecNoCount || 0,
				arg.sfZDQuantity || 0,
				arg.sonElecNoCount || 0,
			);
		}
		const currentArr = orderObj[key]
		const kdInfo = getKdInfo({
			tempDetailInfo: modeInfos[key],
			kddTemp: tempObj[key],
			wdSetInfo: wdSetObj[key],
			singleSempService: tempService[key],
			...arg
		})
		let batchGetYdnoConfigList = (window?.erpData?.batchGetYdnoConfigList)[tempInfo.KddType]
		let batchGetYdNoTradeCount = Number(batchGetYdnoConfigList?.batchGetYdNoTradeCount || 20)
		for (let i = 0; i < currentArr.length; i += batchGetYdNoTradeCount) {
			const tradeInfos = currentArr.slice(i, i + batchGetYdNoTradeCount)
			if (oldYdNos && oldYdNos.length) {     //用户选择申请的原单号
				kdInfo.ydNos = oldYdNos
			}
			const params = {
				kdInfo: kdInfo,
				tradeInfos: tradeInfos.map(o => o.tradeInfo),
				kddType: key,
				getNew: isNewEleNo ? 1 : 0,
				elecNoCount: elecNoCount || 1,
				ydNos: oldYdNos?.length ? oldYdNos : ''
			}
			paramsArr.push({ params, orders: tradeInfos.map(o => o.order) })
		}
	}

	return new Promise((resolve) => {
		function processShown() {
			const $progressDom = modelDom && modelDom.querySelector('.req_express_num');
			const $nickDom = modelDom && modelDom.querySelector('.req_express_nick');
			// TODO 改造：提前单独处理kdInfo，再统一处理tradeInfo
			// tradeInfo为数组，上限50个订单

			let overIndex = Number($progressDom?.textContent || '0'),
				errorMsgList = {}, //获取单号错误信息收集
				elecNolist = {};
			const multiThread = MultiThread({
				params: paramsArr,
				async process(v) {
					const api = 'get_wayBillCodes';
					const { params, orders } = v
					const waybillCodeJson = await post(api, {
						params: params,
						headers: {
							'Content-Type': 'application/json'
						},
						isHideLoading: true,
						isHideError: true,
					});
					//更改获取单号进度
					if ($progressDom) {
						overIndex += orders.length || 0;  // 等于o NaN时都赋值0
						$progressDom.textContent = overIndex;
					}
					// 昵称的字段需要做个加密的兼容处理
					// $nickDom && ($nickDom.textContent = v.buyerNick_origin || v.buyerNick );

					if (waybillCodeJson.result != 100) {
						throw waybillCodeJson;
					}

					const data = waybillCodeJson.data;
					// 获取单号失败处理
					for (const key in data?.errorMap) {

						errorMsgList[key] = data?.errorMap[key] || ''
					}
					console.log('errorMsgList____', errorMsgList);


					// 获取单号成功处理
					for (const key in data?.successMap) {
						if (Object.keys(data?.successMap).length !== 0) {
							let currentV = orders.find((item) => {
								const { tidWithOids = [] } = item || {};
								const tidOidsStr = tidWithOids.join('|');
								return tidOidsStr === key;
							});
							const targetV = data['successMap'][currentV.tidWithOids.join('|')];
							// 昵称的字段需要做个加密的兼容处理
							if ($nickDom) {
								($nickDom.textContent = currentV.buyerNick_origin || currentV.buyerNick);
							}
							// 处理取号回来数据的方法新的
							currentV = newDealWithElecData({
								data: targetV,
								temp: tempObj[params.kddType],
								v: currentV,
								elecNoCount,
								isZiMuJian,
								ydNoOperationLogs: data.ydNoOperationLogs
							});
							// elecNolist.push({
							//     ydNo: targetV.parentTrackingNo || targetV.trackingNos,
							//     togetherId: currentV.togetherId,
							//     realCpCode : targetV.realCpCode,
							//     orderNumber: key,
							// });
							let allYdNos = targetV.trackingNos.split(',')
							if (targetV.parentTrackingNo && !allYdNos.includes(targetV.parentTrackingNo)) {
								allYdNos.unshift(targetV.parentTrackingNo)
							}                            // 获取当前参数的发件人手机号
							let currentParam = params.tradeInfos.find(order => key == order.tids)
							elecNolist[currentV.tidWithOids.join('|')] = {
								ydNo: allYdNos.join(','),
								pYdNo: targetV.parentTrackingNo || '',
								togetherId: currentV.togetherId,
								realCpCode: targetV.realCpCode,
								orderNumber: key,
								senderMobile: currentParam?.senderMobile,
								successNo: Object.keys(data?.successMap[key]?.printDataInfo || {})
							}
						}
					}

					// let allYdNos = Object.keys(data.printDataInfo || {})
					// if(data.pYdNo && !allYdNos.includes(data.pYdNo)){
					//     allYdNos.unshift(data.pYdNo)
					// }
					// elecNolist.push({
					//     cnpName: data.cpName, //菜鸟配模板 推荐的快递公司
					//     cnpCode: data.cpCode, //菜鸟配模板 推荐的快递公司TmsCode
					//     ydNo: allYdNos.join(','),
					//     pYdNo:  data.pYdNo || '',
					//     togetherId: v.togetherId,
					//     packageId: data.packageId,
					//     intelliYdNo: data.intelliYdNo, //标识订单号是否是智选单号
					//     senderMobile: requestParams?.tradeInfo?.senderMobile
					// });

				},
				// 失败处理
				fail(v, { error } = {}) {
					const { params, orders } = v
					console.log(error);
					const errorMsg = error.message || error.errorMessage || '';
					orders.forEach(it => {
						const msg = _dealWithElecMsg({
							buyerNick: it.buyerNick || it.receiverName,
							msg: errorMsg,
							resultCode: error.result,
							printOp: printOp,
							orderMsg: it,
						});
						// errorMsgList.push({
						//     errorOrderNo:item.tidOids.join('|'),
						//     errorMessage:`【订单编号：${ v.togetherId }】：<br/>${ msg }`
						// });
						errorMsgList[it.tidWithOids.join('|')] = `【订单编号：${it.togetherId}】：<br/>${msg}`

						overIndex++;
					})

				},
				// 所有申请结束
				async done(conf) {
					// 筛选成功的订单
					const list = []
					// 筛选失败的订单
					const errorOrderList = []
					orderList.forEach(item => {
						let orderTids = item.tidWithOids.join('|')
						if (!!elecNolist[orderTids] && !errorMsgList[orderTids]) {
							list.push(item)
						}
						if (!!errorMsgList[orderTids]) {
							errorOrderList.push(item)
						}
					})
					const isAllSuccess = errorOrderList.length === 0;

					if (isAllSuccess) {
						resolve({
							list,
							elecNolist: Object.values(elecNolist),
						});

					} else {
						const errorOrderIdList = errorOrderList.map(item => item.togetherId);
						// printInterruptNotify && printInterruptNotify()
						let errStr = []
						// todo: 需要选择是否继续
						Object.keys(errorMsgList).forEach(o => {
							let successNumStr = ''
							if (elecNolist[o]) {
								successNumStr = `部分失败 成功${elecNolist[o]?.successNo?.length || 0}个  失败${(elecNoCount || 0) - (elecNolist[o]?.successNo?.length || 0)}个`
							}
							let tidsStr = `【订单编号】：${o} ${successNumStr}`
							let errorStr = `【失败原因】：${errorMsgList[o]}`
							if (!!successNumStr) {
								errStr.unshift(tidsStr + '<br>' + errorStr)
							} else {
								errStr.push(tidsStr + '<br>' + errorStr)
							}
						})

						resolve({
							list,
							elecNolist: Object.values(elecNolist),
							errorMessageList: errStr,
							errorOrderIdList,
							errorOrderList
						});
					}
				},
			});

			multiThread.start();

			modelDom && modelDom.addEventListener('click', function (e) {
				const target = e.target;
				if (target.classList.contains('closeModel')) {
					multiThread.cancel('Canceled by user');
				}
			});

		}
		processShown()

	});
}
// NOTE 旧 单个申请弹窗
async function getWaybillCodeDialog(arg) {
	const {
		orderList,
		temp,
		elecNoCount,
		sfZDQuantity,
		oldYdNos,
		printOp,
		wdSet,
		isUseOrderFjr = false,
		isNewEleNo,
		loginUserId,
		modeInfo,
		hasWWChat,
		goodsDescription = '',
		packagingDescription,
		afterGetWaybillCodeHook,
		afterContinuePrintHook,
		afterGetElecErrorHook,
		printInterruptNotify,
		isZiMuJian,
		processBatch,
		isScan,
		ordinary,
		weight
	} = arg;

	if (temp.KddType === 1) {
		return orderList;
	}
	const kddFjrMap = arg.fjrInfo;

	let _modeLogisticsItems = (modeInfo || {}).ModeLogisticsItems, //模板的服务
		_tempService;
	//托寄物自己加的，不传给平台
	_modeLogisticsItems = _modeLogisticsItems?.filter(o => o.serviceCode != 'tjw')
	if (
		!arg.tempService &&
		(temp.KddType == 3 || temp.KddType == 7) &&
		_modeLogisticsItems &&
		_modeLogisticsItems.length
	) {
		//收集菜鸟推荐模板的服务
		_tempService = [];
		_modeLogisticsItems.map((_v) => {
			_tempService.push({
				serviceValue: _v.serviceValue,
				serviceCode: _v.serviceCode,
			});
		});
		arg.tempService = _tempService;
	}

	const htmlStr = template.getWaybillCodeHtml(temp, orderList, hasWWChat);

	return new Promise((resolve) => {
		function processShown(modelDom) {
			const $progressDom = modelDom && modelDom.querySelector('.req_express_num');
			const $nickDom = modelDom && modelDom.querySelector('.req_express_nick');
			// TODO 改造：提前单独处理kdInfo，再统一处理tradeInfo
			// tradeInfo为数组，上限50个订单


			let overIndex = 0,
				errorMsgList = [], //获取单号错误信息收集
				elecNolist = [];
			const multiThread = MultiThread({
				params: orderList,
				async process(v) {
					const isBlindFlag = false;

					const getWaybillOption = {
						isBlindFlag,
						temp,
						modeInfo,
						elecNoCount,
						sfZDQuantity,
						oldYdNos,
						printOp,
						wdSet,
						isUseOrderFjr,
						kddFjrMap,
						isNewEleNo,
						loginUserId,
						goodsDescription,
						packagingDescription,
						tempService: _tempService,
						isZiMuJian,
						processBatch,
						weight,
						isScan

					};

					const waybillCodeJson =
						(await _getWaybillCode(v, getWaybillOption)) || {};

					//更改获取单号进度
					$progressDom && ($progressDom.textContent = overIndex + 1);
					// 昵称的字段需要做个加密的兼容处理
					$nickDom && ($nickDom.textContent = v.buyerNick_origin || v.buyerNick);

					if (waybillCodeJson.result != 100) {
						throw waybillCodeJson;
					}

					const data = waybillCodeJson.data;
					const requestParams = waybillCodeJson.params;

					// 德邦用户是否权限打印字母件判断 字母件大于0&&没有子单号的情况阻止打印
					// Todo, 需要确认这里怎么处理
					if (temp.ExCode == 'DBKD' && temp.KddType == 2) {
						if (
							arg.sfZDQuantity > 0 &&
							(data.sfZDydId == '' || !data.sfZDydId)
						) {
							const errorMsg =
								'账号尚未开通德邦子母件服务，请联系快递网点开通服务后重试';
							throw new Error(errorMsg);
						}
					}

					v = _dealWithElecData({
						data,
						temp,
						v,
						elecNoCount,
						isZiMuJian
					});
					let allYdNos = [...data.ydId.split(','), ...(data.sfZDydId ? data.sfZDydId.split(',') : [])]
					if (data.pYdNo && !allYdNos.includes(data.pYdNo)) {
						allYdNos.unshift(data.pYdNo)
					} else if (data.pYdNo && allYdNos.includes(data.pYdNo)) {
						let pYdNoIndex = allYdNos.indexOf(data.pYdNo)
						allYdNos.splice(pYdNoIndex, 1); // 删除元素
						allYdNos.unshift(data.pYdNo);
					}
					elecNolist.push({
						cnpName: data.cpName, //菜鸟配模板 推荐的快递公司
						cnpCode: data.cpCode, //菜鸟配模板 推荐的快递公司TmsCode
						ydNo: allYdNos.join(','),
						pYdNo: data.pYdNo || '',
						togetherId: v.togetherId,
						packageId: data.packageId,
						intelliYdNo: data.intelliYdNo, //标识订单号是否是智选单号
						senderMobile: requestParams?.tradeInfo?.senderMobile
					});

					overIndex++;
				},
				// 失败处理
				fail(v, { error } = {}) {
					console.log(error);
					const errorMsg = error.message || error.errorMessage || '';

					const cancelReasons = ['可用快递单号不足', '没有足够的快递单号', '电子面单账户余额不足', '单号余额不足'];

					const shouldCancel = !!cancelReasons.find(n => errorMsg.indexOf(n) > -1);

					if (shouldCancel) {
						multiThread.cancel();
					}

					const msg = _dealWithElecMsg({
						buyerNick: v.buyerNick || v.receiverName,
						msg: errorMsg,
						resultCode: error.result,
						printOp: printOp,
						orderMsg: v,
					});
					errorMsgList.push(`【订单编号：${v.togetherId}】：<br/>${msg}`);
					overIndex++;
				},
				// 所有申请结束
				async done(conf) {
					// 确保申请单号的框消失
					!isScan && model.remove(modelDom);

					if (elecNolist.length > 0) {
						afterGetWaybillCodeHook(elecNolist, temp, isNewEleNo); //更新订单电子面单号
					}

					// 筛选成功的订单
					const list = orderList.filter((_, idx) =>
						conf.done.find((t) => t.idx === idx),
					);

					const isAllSuccess = conf.done.length === orderList.length;
					if (isAllSuccess) {
						resolve(list);
					} else {
						// 筛选失败的订单
						const errorOrderList = orderList.filter((_, idx) =>
							conf.errors.find(t => t.idx === idx),
						);
						const errorOrderIdList = errorOrderList.map(item => item.togetherId);
						afterGetElecErrorHook && afterGetElecErrorHook(errorOrderList);
						printInterruptNotify && printInterruptNotify()
						// todo: 需要选择是否继续
						const isContinue = await _getWaybillCodeFailInfo(
							errorMsgList.join('<br>'),
							{
								templateId: temp.Mode_ListShowId,
								showContinue: list.length > 0,
							},
						);

						// 取号异常信息收集
						window.errorCollection?.customMessageUpload({
							type: '单模板取号失败',
							data: {
								errorMsgList: errorMsgList,
							}
						});
						if (isContinue) {
							afterContinuePrintHook && afterContinuePrintHook(errorOrderIdList);
							resolve(list);
						}
					}
				},
			});

			multiThread.start();

			modelDom && modelDom.addEventListener('click', function (e) {
				const target = e.target;
				if (target.classList.contains('closeModel')) {
					multiThread.cancel('Canceled by user');
				}
			});

			//TODO 联系客服处理：各平台处理方式不一致
			// if(arg.hasWWChat){
			// }
		}

		!!isScan ? processShown() : model({
			width: 500,
			height: 300,
			content: htmlStr,
			cssType: 'yellow',
			shown: processShown,
		});
	});
}

// NOTE 旧 边申请单号边打印弹窗
async function getWaybillCodeByAsync(arg) {
	const {
		orderList,
		temp,
		elecNoCount,
		sfZDQuantity,
		oldYdNos,
		printOp,
		wdSet,
		isUseOrderFjr = false,
		isNewEleNo,
		loginUserId,
		modeInfo,
		hasWWChat,
		goodsDescription = '',
		packagingDescription,
		afterGetWaybillCodeHook,
		afterContinuePrintHook,
		afterGetElecErrorHook,
		isZiMuJian,
		isScan,
		processBatch,
		weight
	} = arg;

	if (temp.KddType === 1) {
		return orderList;
	}
	const kddFjrMap = arg.fjrInfo;

	let _modeLogisticsItems = (modeInfo || {}).ModeLogisticsItems, //模板的服务
		_tempService;
	//托寄物自己加的，不传给平台
	_modeLogisticsItems = _modeLogisticsItems?.filter(o => o.serviceCode != 'tjw')
	if (
		!arg.tempService &&
		(temp.KddType == 3 || temp.KddType == 7) &&
		_modeLogisticsItems &&
		_modeLogisticsItems.length
	) {
		//收集菜鸟推荐模板的服务
		_tempService = [];
		_modeLogisticsItems.map((_v) => {
			_tempService.push({
				serviceValue: _v.serviceValue,
				serviceCode: _v.serviceCode,
			});
		});
		arg.tempService = _tempService;
	}
	2
	const htmlStr = template.getWaybillCodeHtml(temp, orderList, hasWWChat);

	return new Promise((resolve) => {
		function processShown(modelDom) {
			let overIndex = 0,
				errorMsgList = [], //获取单号错误信息收集
				elecNolist = [];
			const multiThread = MultiThread({
				params: orderList,
				async process(v) {
					const isBlindFlag = false;

					const getWaybillOption = {
						isBlindFlag,
						temp,
						modeInfo,
						elecNoCount,
						sfZDQuantity,
						oldYdNos,
						printOp,
						wdSet,
						isUseOrderFjr,
						kddFjrMap,
						isNewEleNo,
						loginUserId,
						goodsDescription,
						packagingDescription,
						tempService: _tempService,
						isZiMuJian,
						processBatch,
						weight
					};

					const waybillCodeJson =
						(await _getWaybillCode(v, getWaybillOption)) || {};

					// //更改获取单号进度
					// $progressDom && ($progressDom.textContent = overIndex + 1);
					// // 昵称的字段需要做个加密的兼容处理
					// $nickDom && ($nickDom.textContent = v.buyerNick_origin || v.buyerNick );

					if (waybillCodeJson.result != 100) {
						throw waybillCodeJson;
					}

					const data = waybillCodeJson.data;

					// 德邦用户是否权限打印字母件判断 字母件大于0&&没有子单号的情况阻止打印
					// Todo, 需要确认这里怎么处理
					if (temp.ExCode == 'DBKD' && temp.KddType == 2) {
						if (
							arg.sfZDQuantity > 0 &&
							(data.sfZDydId == '' || !data.sfZDydId)
						) {
							const errorMsg =
								'账号尚未开通德邦子母件服务，请联系快递网点开通服务后重试';
							throw new Error(errorMsg);
						}
					}

					v = _dealWithElecData({
						data,
						temp,
						v,
						elecNoCount,
						isZiMuJian
					});
					let allYdNos = [...data.ydId.split(','), ...(data.sfZDydId ? data.sfZDydId.split(',') : [])]
					if (data.pYdNo && !allYdNos.includes(data.pYdNo)) {
						allYdNos.unshift(data.pYdNo)
					} else if (data.pYdNo && allYdNos.includes(data.pYdNo)) {
						let pYdNoIndex = allYdNos.indexOf(data.pYdNo)
						allYdNos.splice(pYdNoIndex, 1); // 删除元素
						allYdNos.unshift(data.pYdNo);
					}
					elecNolist.push({
						cnpName: data.cpName, //菜鸟配模板 推荐的快递公司
						cnpCode: data.cpCode, //菜鸟配模板 推荐的快递公司TmsCode
						ydNo: allYdNos.join(','),
						pYdNo: data.pYdNo || '',
						togetherId: v.togetherId,
						packageId: data.packageId,
						intelliYdNo: data.intelliYdNo, //标识订单号是否是智选单号
					});

					overIndex++;
				},
				// 失败处理
				fail(v, { error } = {}) {
					console.log(error);
					const errorMsg = error.message || error.errorMessage || '';

					const cancelReasons = ['可用快递单号不足', '没有足够的快递单号', '电子面单账户余额不足', '单号余额不足'];

					const shouldCancel = !!cancelReasons.find(n => errorMsg.indexOf(n) > -1);

					// if (shouldCancel) {
					//     multiThread.cancel();
					// }

					const msg = _dealWithElecMsg({
						buyerNick: v.buyerNick || v.receiverName,
						msg: errorMsg,
						resultCode: error.result,
						printOp: printOp,
						orderMsg: v,
					});
					errorMsgList.push({
						togetherId: v.togetherId,
						buyerNick: v.buyerNick || v.receiverName,
						msg
					})
					overIndex++;
				},
				// 所有申请结束
				async done(conf) {

					if (elecNolist.length > 0) {
						afterGetWaybillCodeHook(elecNolist, temp, isNewEleNo); //更新订单电子面单号
					}

					// 筛选成功或者失败的订单
					const successList = []
					const errorOrderList = []
					const errorOrderIdList = []
					orderList.map((it, idx) => {
						let successOrder = conf.done.find((t) => t.idx === idx)
						if (successOrder) {
							successList.push(it)
						} else {
							errorOrderList.push(it)
							errorOrderIdList.push(it.togetherId)
						}
					});

					// const isAllSuccess = conf.done.length === orderList.length;

					afterGetElecErrorHook && afterGetElecErrorHook(errorOrderList);
					afterContinuePrintHook && afterContinuePrintHook(errorOrderIdList);
					resolve({
						errorList: errorOrderList,
						successList: successList,
						errorMsgList
					});
				}
			});

			multiThread.start();

			modelDom && modelDom.addEventListener('click', function (e) {
				const target = e.target;
				if (target.classList.contains('closeModel')) {
					multiThread.cancel('Canceled by user');
				}
			});

			//TODO 联系客服处理：各平台处理方式不一致
			// if(arg.hasWWChat){
			// }
		}

		processShown()
	})

}
async function _getWaybillCode(v, arg) {
	let {
		printOp,
		// isBlindFlag = false,
		wdSet = {},
		modeInfo,
		modeInfo: {
			ModeCustomerTemplate,
			ModeServiceItems,
			ModeAdvancedServices
		},
		temp = {},
		tempService,
		isNewEleNo,
		elecNoCount,
		sfZDQuantity = undefined,
		oldYdNos,
		kddFjrMap = {},
		isZiMuJian,
		processBatch,
		weight,
		isScan
		// loginUserId,
	} = arg;
	console.log('get_wayBillCode--');
	console.info(arg);

	// 判断是否为抖音厂家代打
	const isDYDD = platformConfig.isDYDAPlatform();

	const isPddKuaiYun = comp.base.getPddTempStyle(
		'pddKuaiYunZmj',
		temp.styleId,
		temp.ExCode,
	);

	const isCaiNiaoKuaiYun = comp.base.getTempStyle('kuaiyun', temp.styleId, temp);

	const isDYKuaiYun = comp.base.getTempStyle('DYkuaiyun', temp.styleId, temp);
	if (isPddKuaiYun || isCaiNiaoKuaiYun || isDYKuaiYun) {      // 拼多多、菜鸟、抖音
		elecNoCount = Math.max(
			elecNoCount || 0,
			arg.sfZDQuantity || 0,
			arg.sonElecNoCount || 0,
		);
	}

	const _orders = (v.orders || []).map((k) => {
		// 订单标题判断顺丰-抖音电子面单，如果有简称，就传简称
		const isSFAndDY = temp.KddType === 8 && temp.ExCode === 'SF';

		const orderObj = {
			name:
				temp.ExCode === 'BESTQJT'
					? k.skuPropertiesName + '_' + k.name //百世快运商品信息改成规格+商品标题
					: isSFAndDY ? (k.short || k.name) : k.name, // 订单标题判断顺丰-抖音电子面单，如果有简称，就传简称
			count: k.count,
			price: k.price,
			weight: k.weight,
		};
		if (temp.KddType == 14) {
			orderObj.skuId = k.skuId
			orderObj.spuId = k.numIid
		}
		// 优先用简称，然后取标题
		const shouldUseShortFirst =
			// 顺丰网点, 满足顺丰用户： 希望用户不要买家看见商品全称的需求
			(temp.KddType == 2 && temp.ExCode == 'SF') ||
			// 抖音京东面单
			(temp.KddType == 8 && temp.ExCode == 'JD');

		if (shouldUseShortFirst) {
			orderObj.name = k.short || k.name;
		}

		return orderObj;
	});

	const params = {};

	let kddFjr = Object.keys(v.kddFjr).length ? v.kddFjr : getDefaultFjrInfo(kddFjrMap, v.userId2); //发件人相关的 userId 是用的从库 id

	// FIXME:如果拼多多平台下，绑定了本店铺的默认发件人，但是打印的是非本店铺的订单，则会走到这部分逻辑
	// 这里的业务逻辑本就是有问题的，但是为了兼容线上版本，只能这样处理
	if (
		JSON.stringify(v.kddFjr) === '{}' &&
		comp.Print.Data.platform === 'pdd'
	) {
		kddFjr = getDefaultFjrInfo(kddFjrMap, comp.Print.Data.userInfo.userId);
	}

	let kdInfo;
	let tradeInfo;

	// localStorage.getItem('isXHSEncrypted') 临时处理新版小红书没传caid的问题
	const encrypted = ((isEncrypted(v.s_mobile) || isEncrypted(v.s_phone)) && v.caid) || ([13, 16].includes(Number(temp.KddType)) && !v.isDecrypted);
	const oaids = encrypted ? v.sidTrades.map(t => `${t.tid}:${t.caid}`) : [];
	const caids = encrypted ? v.sidTrades.map(t => `${t.caid}`) : [];
	const commonTradeInfo = {
		// 加密字段
		oaid: oaids[0] || '',
		oaids: oaids.join('|') || '',
		outerUserId: v.outerUserId,
		// outerTradeId: v.outerTradeId,
		// 订单来源
		tradeSource: v.tradeSource || '',
		// 加解密涉及的字段
		mobile: checkReceiverMobile(temp.KddType, v.s_mobile || v.s_phone || ''),
		tel: v.s_phone || '',
		receiverName: (v.s_name || '').replace(/\n/g, ''),
		receiverAddress: getVirtualMobileAddress(v.s_address, v.s_mobile, temp, v.isVirtualTel),

		// 保留加密的谜文
		mobileEncrypt: v.s_encode_mobile, //  收件人手机号加密串
		receiverNameEncrypt: v.s_encode_name, //  收件人加密串
		receiverAddressEncrypt: v.s_encode_address, //  收件人详细地址加密串


		// 通用
		receiverCity: v.s_city,
		receiverCounty: v.s_county,
		receiverProvince: v.s_province,
		receiverTown: v.s_town,    //目前这个字段慎用，避免打印出来的面单纸上地址信息打印两份镇。（目前只有信丰用，获取大头笔使用）


		// pdd 字段
		mainTid: v.mainTid, // 手动合单时，主订单号
	};

	// 存在则赋值
	$.extend(commonTradeInfo, {
		sendToHomeKey: v.sendToHomeKey,     // 送货上门
	});

	if (encrypted && v.s_encode_mobile) {
		commonTradeInfo.mobile = v.s_encode_mobile;
		commonTradeInfo.tel = v.s_encode_phone;
		commonTradeInfo.receiverName = v.s_encode_name;
		commonTradeInfo.receiverAddress = getVirtualMobileAddress(v.s_encode_address, v.s_mobile, temp, v.isVirtualTel);
	}
	v.tradeReceiverList.forEach((k) => {
		k.receiverMobile = checkReceiverMobile(temp.KddType, k.receiverMobile);
	})
	if (temp.KddType == 5 && modeInfo.jdQl == 1) { //JD青龙面单的接口字段和菜鸟的差距很大
		kdInfo = {
			kdCode: temp.ExCode,
			kdId: temp.Exid,
			kdName: temp.ExcodeName,
			styleId: temp.styleId,
			modeListShowId: ~~temp.Mode_ListShowId,
			goodsDescription: arg.goodsDescription,
			//菜鸟面单获取单号所传递的信息
			useTaoBaoId: wdSet.Exuserid,
			addreddId: wdSet.AddressId,
			branchCode: wdSet.Branch_code || wdSet.branchCode || '',
			branchName: wdSet.Branch_name || '',
			shared: wdSet.shared,
			shareId: wdSet.shareId,
			ownerId: wdSet.ownerId,
			sonElecNoCount: arg.sonElecNoCount || arg.sfZDQuantity || arg.elecNoCount,
			processBatch,
			customerCode: wdSet.logisticsAccount,
			authodV2: true
		};

		tradeInfo = {
			...commonTradeInfo,
			buyerMessage: v.buyerMome || '', //买家留言
			buyerNick: v.buyerNick || '',
			nums: v.count || 1, //商品数量
			tids: v.tidOids.join('|'), //订单id串值(tid:oid,oid|tid1|tid2)
			orders: _orders,
			weight: v.hj_weight || 0,
			sellerFlag: v.sellerFlag || 0,
			taobaoId: v.userId || v.taobaoId,
			optionType: printOp, //操作来源    0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单
			packageId: (v.packageId || -1), //包裹号
			senderAddressDetail: wdSet.address, //网点的地址
			senderArea: wdSet.countryName,
			senderCity: wdSet.cityName,
			senderProvince: wdSet.provinceName,
			senderTown: wdSet.countrysideName,
			senderMobile: wdSet.F_mobile || wdSet.F_tel,
			senderName: wdSet.F_name,
			kddType: temp.KddType,
			isCod: v.isCOD ? 1 : 0, //是否货到付款
			codPrice: parseFloat(v.codPrice), //代收金额
			payAmount: v.payAmount, //实付金额
			info: v.print_content || '', //sku信息
			sourceId: 1, //  1 快递助手 2 进销存
			sellerMome: v.sellerMome,    //卖家备注  韵达网点面单需要，生成韵达打印密串
			printSenderName: kddFjr.name,   //传递打印的发件人姓名， 用于底单查询时显示发件人姓名，后端要求获取单号接口 和 保存底单日志接口 都传递该字段 TODO
			tradeSource: v.source, // 订单来源
			sellerNick: v.seller_nick, //平台商家名称
			outerTradeId: (v.outerTradeId || []).join('|'),
			tradeReceiverList: v.tradeReceiverList,
			saleUserId: v.distributorUserId || '',
			tradeEncodeType: v.tradeEncodeType,
			goodsNumGift: v.goodsNumGift,
			hlPlatformType: v.hlPlatformType,
			isEncrypted: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.isEncrypted,
			hlTradeInfo: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.downstreamEncryptDetails,
			outPlatformSupplyOrderNo: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.outPlatformSupplyOrderNo,
			outSupplierId: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.outSupplierId,
			outPlatformSubCode: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.outPlatformSubCode,
			consolidateType: v.consolidateType || '',
			tradeEncodeType: v.tradeEncodeType,
		};
	} else {
		kdInfo = {
			processBatch,
			kdCode: temp.ExCode,
			kdId: temp.Exid,
			kdName: temp.ExcodeName,
			styleId: temp.styleId,

			//菜鸟面单获取单号所传递的信息
			useTaoBaoId: wdSet.Exuserid,
			addreddId: wdSet.AddressId,
			branchCode: wdSet.Branch_code || '',
			branchName: wdSet.Branch_name || '',
			sonElecNoCount: arg.sonElecNoCount || arg.sfZDQuantity || arg.elecNoCount,
			goodsDescription: arg.goodsDescription,
			packagingDescription: arg.packagingDescription,
			//单号分享字段
			shared: wdSet.shared,
			relationUserId: wdSet.sharedUserId || wdSet.relationUserId,
			ownerId: wdSet.ownerId,
			isRelation: wdSet.isRelation,
			wpType: wdSet.wpType,
			shareId: wdSet.shareId,
			paymentType: wdSet?.paymentType,
			weight,
			// 网点和抖音面单需要，所以提前了；
			modeListShowId: ~~temp.Mode_ListShowId,
			sfZDQuantity,
			authodV2: true,
			customerCode: wdSet.logisticsAccount,
			subShareUserCode: wdSet.subShareUserCode || '',
			// 视频号模板id
			...(temp.KddType === 14 ? {
				platformUserTemplate: temp.platformUserTemplate,
				merchantAccount: wdSet.merchantAccount,
				sphBranchId: wdSet.SellerId,
				branchCode: wdSet.Branch_code,
				branchName: wdSet.Branch_name
			} : {})
		};
		tradeInfo = {
			...commonTradeInfo,
			buyerMessage: v.buyerMome || '', //买家留言
			buyerNick: v.buyerNick || '',
			buyerOpenUid: v.buyerOpenUid || '',
			nums: v.count || 1, //商品数量
			tids: v.tidOids.join('|'), //订单id串值(tid:oid,oid|tid1|tid2)
			tradeReceiverList: v.tradeReceiverList,
			orders: _orders,
			weight: v.hj_weight || 0,
			sellerFlag: v.sellerFlag || 0,
			taobaoId: v.userId || v.taobaoId,
			threePlTiming: v.threePlTiming,//天猫直送申请订单号相关属性, 天猫直送必须使用才年电子面单，申请单号时必须传递这个值，不是的不能传递
			optionType: printOp, //操作来源    0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单
			packageId: (v.packageId || -1), //包裹号
			senderAddressDetail: wdSet.F_addr, //网点的地址
			senderArea: wdSet.F_q,
			senderCity: wdSet.F_c,
			senderProvince: wdSet.F_p || wdSet.F_s,
			senderTown: wdSet.F_town,
			senderMobile: wdSet.F_mobile || wdSet.F_tel,
			senderName: wdSet.F_name,
			kddType: temp.KddType,
			isCod: v.isCOD ? 1 : 0,
			codPrice: parseFloat(v.codPrice), //代收金额
			payAmount: v.payAmount, //实付金额
			postFee: parseFloat(v.hj_post_fee || 0),          //到付金额需要该字段
			info: v.print_content || '', //sku信息
			sourceId: 1, //  1 快递助手 2 进销存
			sellerMome: v.sellerMome,    //卖家备注  韵达网点面单需要，生成韵达打印密串
			printSenderName: kddFjr.name,   //传递打印的发件人姓名， 用于底单查询时显示发件人姓名，后端要求获取单号接口 和 保存底单日志接口 都传递该字段 TODO
			buyerNickSensitive: v.buyerNick_origin || '', // 加密数据的脱敏值
			receiverNameSensitive: v.s_name_origin || '', // 加密数据的脱敏值
			receiverAddressSensitive: v.s_address_origin || '', // 加密数据的脱敏值
			mobileSensitive: v.s_mobile_origin || v.s_mobile || '', // 加密数据的脱敏值 s_mobile_origin字眼没有，做个兼容
			tradeSource: v.source, // 订单来源
			sellerNick: v.seller_nick, //平台商家名称
			outerTradeId: (v.outerTradeId || []).join('|'),
			saleUserId: v.distributorUserId || '',
			receiverId: (v.receiverId || []).join('|'),
			memberId: v.memberId,
			goodsNumGift: v.goodsNumGift,
			// 回流订单数据
			hlPlatformType: v.hlPlatformType,
			isEncrypted: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.isEncrypted,
			hlTradeInfo: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.downstreamEncryptDetails,
			outPlatformSupplyOrderNo: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.outPlatformSupplyOrderNo,
			outSupplierId: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.outSupplierId,
			outPlatformSubCode: (v.tradeEncryptOutOrderInfoMap || {})[v.s_tid]?.outPlatformSubCode,
			consolidateType: v.consolidateType || '',
			tradeEncodeType: v.tradeEncodeType,

		};
		// b. 新增字段：
		// encryptOutOrderInfo下的outPlatformSupplyOrderNo -抖音供应链采购订单号
		// encryptOutOrderInfo下的outSupplierId - 抖音供应链供应商id
		// encryptOutOrderInfo下的outPlatformSubCode字段，抖音供应链业务取值为101
	}
	if (['SF'].includes(temp.ExCode) || (['JD'].includes(temp.ExCode) && isJDNewWjTpl(temp))) {
		if (kdInfo.goodsDescription?.includes('订单商品作为托寄物')) kdInfo.goodsDescription = _getGoodsDescription(v)
		if (!kdInfo.goodsDescription) kdInfo.goodsDescription = '商 品'
	}
	//todo 菜鸟顺丰暂时删除此字段 后续优化子母件传参逻辑
	if ([3, 15].includes(temp.KddType) && !isZiMuJian) {
		delete kdInfo.sonElecNoCount;
	}
	// 网点删除此字段
	if (temp.KddType === 2 && !isZiMuJian) {
		delete kdInfo.sonElecNoCount;
	}
	// 京东非子母件删除
	if (temp.KddType === 5 && !isZiMuJian && ['JD'].includes(temp.ExCode)) {
		delete kdInfo.sonElecNoCount;
	}
	// 抖音顺丰非子母件与抖音非快运删除此字段
	if ([5, 8, 9, 13, 14, 16].includes(temp.KddType) && !isZiMuJian && temp.styleId != 76) {
		delete kdInfo.sonElecNoCount;
	}

	// TODO 拼多多电子面单非快运模式取号不传子母件数量 需要加一个条件，快运模式必传，kddtype==7 && ！isZiMuJian&& 不是快运模式
	if (temp.KddType === 7 && !isZiMuJian && temp.styleId != '49') {
		delete kdInfo.sonElecNoCount
	}
	// 测试
	tradeInfo.senderTown == '<nil>' ? tradeInfo.senderTown = '' : null;

	// if (printOp == 4) { //手工订单申请单号，需要传递
	tradeInfo.realTid = v.realTid || '';
	// tradeInfo.insurePrice = parseFloat(v.bjje || 0);
	// }else{
	tradeInfo.insurePrice = parseFloat(v.payAmount || 0);
	// }

	tempService && (kdInfo.modeLogisticsItems = tempService); //传递模板勾选的服务
	if (temp.KddType == '3') {
		kdInfo.templateUrl = 'https://cloudprint.cainiao.com/template'; //TODO 预留
		if (temp.ExCode == 'SF') {    // 菜鸟顺丰添加 productCode
			kdInfo.productCode = ModeCustomerTemplate?.ServiceValue || '';
		}
	} else if (temp.KddType == '8') {
		kdInfo.templateUrl = modeInfo?.ModeListShow?.templateUrl || ''; //TODO 预留
	}

	if (temp.ExCode == 'CNTMS') {
		//菜鸟配模板的发件人信息 从基础设置中获取
		tradeInfo = Object.assign(
			tradeInfo,
			{
				senderAddressDetail: kddFjr.address,
				senderArea: kddFjr.district,
				senderCity: kddFjr.city,
				senderProvince: kddFjr.province,
				senderMobile: kddFjr.phone,
				senderName: kddFjr.name,
			},
			true,
		);
		kdInfo.useTaoBaoId = arg.loginUserId;
	} else if (temp.KddType == '2' || temp.KddType == '5') {
		//非菜鸟网点电子面单（韵达网点除外）及 菜鸟配模板 的发件人信息 从基础设置中获取
		kdInfo = Object.assign(
			kdInfo,
			{
				//网点面单所传递的信息
				customPassword: wdSet.Password || '',
				customName: wdSet.Username || '',
				monthCardNumber: wdSet.Custid || '',
				companyName: wdSet.F_com || '', // 暂时仅韵达网点有 2019.3.25
			},
			true,
		);
		if (temp.ExCode == 'UAPEX') {
			v.customeid = wdSet.Custid || ''; //全一客户编码
		} else {
			v.cutid = wdSet.Custid || ''; //SF 速尔 月结卡号
		}
		if (temp.ExCode != 'YUNDA' && temp.KddType != '5') {
			//非韵达网点面单和jd电子面单 获取单号的发件信息走基础设置里面的
			tradeInfo = Object.assign(
				tradeInfo,
				{
					senderAddressDetail: kddFjr.address,
					senderArea: kddFjr.district,
					senderCity: kddFjr.city,
					senderProvince: kddFjr.province,
					senderMobile: kddFjr.phone,
					senderName: kddFjr.name,
				},
				true,
			);
		} else if ((temp.KddType == '5' && temp.ExCode != 'JD') || isJDNewWjTpl(temp)) {
			//jd面单需要取基础设置里的发件人和手机
			kdInfo = $.extend(
				kdInfo,
				{
					monthCardNumber: wdSet.settlementCode || '',
					jdShopCode: wdSet.JdShopCode || '', //京东快递新增京东店铺code，无该code无需传递
				},
				true,
			);
			if (!isJDNewWjTpl(temp)) {
				tradeInfo = Object.assign(
					tradeInfo,
					{
						senderMobile: kddFjr.phone,
						senderName: kddFjr.name,
						senderAddressDetail:
							kddFjr.province +
							kddFjr.city +
							kddFjr.district +
							kddFjr.address,
					},
					true,
				);
			}
			v.sf_city = tradeInfo.senderCity; //JD面单添加的 始发城市 字段
		}
	} else if (temp.KddType == '7') {
		tradeInfo.senderName = kddFjr.name || '';
		tradeInfo.senderMobile = kddFjr.phone || '';
		kdInfo.modeListShowId = ~~temp.Mode_ListShowId;
	} else if (temp.KddType == '10') { // 美团的电子面单需要logisticsAccount
		kdInfo = Object.assign(
			kdInfo,
			{
				merchantAccount: wdSet.logisticsAccount,
			},
		);

	} else if (!tradeInfo.senderMobile) {
		//解释：电子面单 网点设置中发件人电话信息不完善时 用基础设置中发件人信息补充
		tradeInfo.senderName = tradeInfo.senderName || kddFjr.name || '';
		tradeInfo.senderMobile = kddFjr.phone || '';
	}


	if (comp.Print.Data.platform === 'erp') {
		const { ServiceValue = '', Svctypename = '', Svctypecode = '' } = ModeCustomerTemplate || {}; //erp顺丰需要传产品类型
		// const  serviceType = ModeServiceItems.find(o=> o.Dataname === 'yslx')
		let serviceType, jdTransTypeStr
		ModeServiceItems.forEach(o => {
			if (o.Dataname === 'pslx') {
				serviceType = o
			}
			if (o.Dataname === 'yslx') {
				jdTransTypeStr = o
			}
		})
		const sfKey = {
			'fkfs': 'payMethodStr',
			'yjkh': 'customerCode',
			'khbm': 'isvClientCode'
		}
		const modeServiceItems = []
		// sellerId可能会因为数据没同步导致为空
		tradeInfo.sellerId = v.sellerId;
		tradeInfo.platform = v.platform;
		kdInfo.printSellerId = wdSet.sellerId || v.sellerId;
		kdInfo.printPlatform = wdSet.printPlatform;
		ModeAdvancedServices.length && ModeAdvancedServices.forEach(it => {
			let kdinfoKey = sfKey[it.serviceKey]
			if (!!kdinfoKey) kdInfo[kdinfoKey] = it.serviceValue
		})
		ModeServiceItems.length && ModeServiceItems.forEach(it => {
			let kdinfoKey = sfKey[it.Dataname]
			if (!!kdinfoKey) kdInfo[kdinfoKey] = it.Defaultval
			if ([9, 14, 16].includes(Number(temp.KddType))) {
				modeServiceItems.push({
					serviceName: it.Itemname,
					serviceValue: JSON.stringify({ name: it.Defaultval, show: true, type: '', value: it.Reserve1 })
				})
			}
		})
		if (modeServiceItems.length) {
			kdInfo.modeLogisticsItems = modeServiceItems
		}
		if (temp.KddType == '16') {
			kdInfo.customerCode = wdSet.logisticsAccount || '';
		}
		if (['SF', 'DBKD', 'SFKY'].includes(temp.ExCode) || (['JD', 'JDKY'].includes(temp.ExCode) && temp.KddType !== '7')) kdInfo.productCode = ServiceValue; //拼多多京东暂不传
		// 小红书、视频号圆通也需要productCode
		if (['YTO'].includes(temp.ExCode) && [16, 14].includes(Number(temp.KddType))) kdInfo.productCode = ServiceValue;
		if (['SF'].includes(temp.ExCode) && temp.KddType == '7') kdInfo.productName = Svctypename; //拼多多外顺丰
		if (temp.ExCode === 'JD' && temp.KddType == '5') {
			kdInfo.goodsType = serviceType?.Defaultval; //京东传配送类型
			kdInfo.jdTransTypeStr = jdTransTypeStr?.Defaultval || ''; //京东传运输类型
		}
		const sender = kddFjrMap.sender?.[v.sellerId]?.info || kddFjr;
		if (sender?.name && sender?.phone) {
			tradeInfo.senderMobile = sender.phone;
			tradeInfo.senderName = sender.name;
		}
		if (temp.KddType == '14') {
			tradeInfo.senderAddressDetail = sender?.address || kddFjr.address || ''; //网点的地址
			tradeInfo.senderArea = sender?.district || kddFjr?.district || '';
			tradeInfo.senderCity = sender?.city || kddFjr?.city || '';
			tradeInfo.senderProvince = sender?.province || kddFjr.province || '';
			// tradeInfo.senderTown = sender.F_town ,
			// tradeInfo.senderMobile = sender.F_mobile || sender.F_tel,
			// tradeInfo.senderName = sender.F_name,
		}
		// 来源是手工单的一律优先取订单发件人
		if (v.source === 'HAND') {
			// tradeInfo.senderAddressDetail = kddFjr.address; //网点的地址
			// tradeInfo.senderArea = kddFjr.district;
			// tradeInfo.senderCity = kddFjr.city;
			// tradeInfo.senderProvince = kddFjr.province;
			// tradeInfo.senderTown = kddFjr.town || '' ;
			tradeInfo.senderMobile = kddFjr.phone || '';
			tradeInfo.senderName = kddFjr.name || '';
		}

		tradeInfo.tids = v.tidWithOids.join('|');
		tradeInfo.ptTids = v.ptTidWithOids.join('|');

		tradeInfo.caid = caids[0] || '';
		tradeInfo.caids = caids.join('|') || '';

		delete tradeInfo.oaid;
		delete tradeInfo.oaids;

		tradeInfo.idxEncodeReceiverMobile = v.idxEncodeReceiverMobile;
		tradeInfo.idxEncodeReceiverAddress = v.idxEncodeReceiverAddress;
		tradeInfo.idxEncodeReceiverName = v.idxEncodeReceiverName;

		tradeInfo.receiverNameMask = v.receiverNameMask;
		tradeInfo.receiverPhoneMask = v.receiverPhoneMask;
		tradeInfo.receiverMobileMask = v.receiverMobileMask;
		tradeInfo.receiverAddressMask = v.receiverAddressMask;

		tradeInfo.receiverMobile = checkReceiverMobile(temp.KddType, v.receiverMobile);
		tradeInfo.receiverTel = v.s_phone;
		tradeInfo.receiverName = v.receiverName;
		tradeInfo.receiverAddress = v.receiverAddress;

		tradeInfo.encodeTid = v.encodeTid;

		tradeInfo.isDecrypted = v.isDecrypted;
		tradeInfo.payment = v.payment;

		for (const tItem in tradeInfo) {
			if (tradeInfo[tItem] == 'null') {
				delete tradeInfo[tItem];
			}
		}
	}

	if (isScan) {
		params.getNew = isNewEleNo
	} else {
		params.getNew = isNewEleNo ? 1 : 0;
	}
	params.elecNoCount = elecNoCount || 1;
	if (oldYdNos && oldYdNos.length) {     //用户选择申请的原单号
		params.ydNos = oldYdNos;
		kdInfo.ydNos = oldYdNos

	} else if (v.ydNo) {
		params.ydNos = [v.ydNo];
		kdInfo.ydNos = [v.ydNo]
	}

	// 抖音顺丰字母件原单号重打，如果有原单号，需要把原单号传给后端
	if (temp.KddType === 8 && temp.ExCode === 'SF' && isZiMuJian) {
		// params.ydNos = v.sidNos.filter(it=> it !== '打印后生成')
		params.ydNos = ''
	}

	// 后端接口迭代版本，避免线上不兼容
	kdInfo.printVersion = 'V2'
	params.kdInfo = JSON.stringify(kdInfo);
	params.tradeInfo = JSON.stringify(tradeInfo);

	params.taobaoNick = wdSet.username; // 选中网点的 taobaoNick
	if (printOp == 4) { //手工单多店铺授权
		params.taobaoNick = wdSet.twTaobaoId;
	}

	if (printOp != 4 && (!v.orders || v.orders.length === 0)) {
		Tatami.showFail(
			(v.buyerNick || '') +
			'无可打印的宝贝,请在订单列表右侧点击详情，勾选要打印的宝贝再试',
		);
		return;
	}

	// substituteUserId：抖音厂家代打店铺id substituteShopName: 代打店铺名
	if (isDYDD) {
		params.substituteUserId = v.mallMaskId;
		params.substituteShopName = v.mallMaskName;
	}

	const api = isDYDD ? 'get_DYDD_wayBillCode' : 'get_wayBillCode';

	const json = await post(api, {
		params: params,
		isHideLoading: true,
		isHideError: true,
	});

	// 拼多多存底单需要用到这个字段
	if (json.data) {
		json.data.useTaoBaoId = kdInfo.useTaoBaoId;
	}
	json.params = {
		kdInfo,
		tradeInfo
	}
	return json;
}
/**
 * getGoodsDescription 获取商品描述，托寄物取值优先级：
 * 1. 商品简称
 * 2. 商品标题
 * 取值规则：不能超过20个字符，不能与商品名称一模一样
 * @param {*} orderInfo
 * @return {string}
 */
function _getGoodsDescription(orderInfo) {
	const orderNameList = orderInfo?.orders?.map((o, i) => {
		let getGoodsDescription = null
		if (orderInfo.platform === 'hand' && !o.short) {
			getGoodsDescription = (i + 1) + (o?.print_content || '商品')
		} else if (o.short) {
			getGoodsDescription = (i + 1) + o.short
		} else {
			getGoodsDescription = (i + 1) + '商品'
		}
		return getGoodsDescription
	}) || [];
	return orderNameList.join('|').substr(0, 20);
}

function newDealWithElecData({ v, temp, data, ydNoOperationLogs }) {
	const isKuaiYun =
		comp.base.getTempStyle('kssxKuaiYun', null, temp) ||
		comp.base.getTempStyle('kuaiyun', temp.styleId) ||
		comp.base.getPddTempStyle('pddKuaiYunZmj', temp.styleId, temp.ExCode);

	v.exnumber = data.trackingNos || ''; // 运单号列表
	v.electronData = data;
	if (temp.KddType == 14) v.platformUserTemplateMap = data.platformUserTemplateMap || {}

	if (data?.parentTrackingNo) {
		v.pYdNo = data.parentTrackingNo || ''; // 母单号仅子母件的场景下有值
		v.sfZDydId = data.trackingNos; //子单号，多个子单号以“,”分隔
		if (['SF', 'SZKKE'].includes(temp?.ExCode) && [8].includes(temp.KddType)) v.exnumber = data.parentTrackingNo || ''; // 运单号列表

		if (isKuaiYun) {// 这个反序，后端要重新联调
			data.trackingNos = data.trackingNos.split(',').reverse().join(',');
			v.pYdNo = data.parentTrackingNo || ''; // 母单号仅子母件的场景下有值
		}
	}

	// 所有运单号的集合，快递，子母件，快运的所有单号都会返回在一起
	v.allYdNos = [...(data.trackingNos ? data.trackingNos.split(',') : []), ...(data.parentTrackingNo ? data.parentTrackingNo.split(',') : [])];
	v.allYdNos = [...new Set(v.allYdNos)];
	v.ydNoOperationLogs = ydNoOperationLogs
	console.log(' 所有运单号的集合', v.allYdNos);
	if (v.allYdNos.length === 1) {
		let original = ydNoOperationLogs.find(o => o.ydId === data.trackingNos)
		if (original) v.original = original?.original
	}
	// 抖音电子面单
	const isNewDyTpl = isDYEncryptTpl(temp.KddType, temp.customTop);
	const isNewKsTpl = isKsEncryptTpl(temp.KddType, temp.customTop);
	const isNewYzTpl = isYzEncryptTpl(temp.KddType);
	const isNewPddTpl = isPddEncryptTpl(temp.KddType, temp.customTop);
	const isCNTpl = isCNEncryptTpl(temp.KddType);
	const isXHStTpl = isXHSEncryptTpl(temp.KddType, temp.customTop);
	const isSPH = isSPHEncryptTpl(temp.KddType, temp.customTop);
	const dealPrintData = isNewDyTpl || isNewKsTpl || isNewYzTpl || isNewPddTpl || isCNTpl || isXHStTpl || isSPH;

	if (dealPrintData) {
		if (data.trackingNos) {
			if (data.printDataInfo[data.parentTrackingNo] && ['SF', 'SZKKE'].includes(temp?.ExCode) && [8].includes(temp.KddType)) {
				v.cloudData = JSON.parse(data.printDataInfo[data.parentTrackingNo]);
			} else if (data.trackingNos.split(',').length > 1) {
				v.cloudData = data.printDataInfo;
			} else {
				Object.keys(data.printDataInfo).forEach(key => {
					if (temp.KddType === 14) {
						v.cloudData = data.printDataInfo[key]
					} else {
						v.cloudData = JSON.parse(data.printDataInfo[key]);
					}
				});
			}
		}
		v.encrypted = true;
	}
	return v;
}
/**
 * 获取单号数据处理
 * @param {object} param0
 */
function _dealWithElecData({ v, temp, data, isZiMuJian }) {
	//这个处理我很不想写 ------快运子单号返回顺序就是个坑 ------顺序倒叙处理下
	const isKuaiYun =
		comp.base.getTempStyle('kuaiyun', temp.styleId) ||
		comp.base.getPddTempStyle('pddKuaiYunZmj', temp.styleId, temp.ExCode);

	if (data.pYdNo && isKuaiYun) {
		data.ydId = data.ydId
			.split(',')
			.reverse()
			.join(',');
	}
	v.useTaoBaoId = data.useTaoBaoId;

	v.electronData = data;
	v.exnumber = data.ydId || '';
	v.sfZDydId = data.sfZDydId || ''; //子单号，多个子单号以“,”分隔
	v.packageId = data.packageId || -1;
	v.pYdNo = data.pYdNo;
	v.allYdNos = [...(v.exnumber ? v.exnumber.split(',') : []), ...(v.sfZDydId ? v.sfZDydId.split(',') : [])];

	// 抖音电子面单
	// const isNewDyTpl = isDYEncryptTpl(temp.KddType, temp.Exid);
	const isNewDyTpl = isDYEncryptTpl(temp.KddType, temp.customTop);
	// 视频号面单原单号打印平台id
	if (temp.KddType == 14) v.platformUserTemplateMap = data.platformUserTemplateMap
	if (temp.KddType == 2 || temp.KddType == 5 || (temp.KddType == 8 && !isNewDyTpl) || temp.KddType == 3 && temp.ExCode == 'CNTMS') { //网点的数据处理  或者 菜鸟配模板,抖音电子面单
		if (data.isDeadZone == 1 && temp.ExCode == 'DISTRIBUTOR_12017865') {       //安能盲区 大头笔加上需转单几个字
			v.datoubi = (data.shortAddress || '') + '需转单';
		} else {
			v.datoubi = data.shortAddress || '';
		}
		v.routeCode = v.datoubi; //存储底单日志所用
		v.jibaoma = data.packageCenterCode || '';
		v.jibaodi = data.packageCenterName || '';
		// 始发网点 名称
		v.sfwd = data.shippingBranchName || '';
		// 始发网点 code
		v.dzmd_sf = data.shippingBranchCode || '';
		// 到达网点
		v.ddwd = data.consigneeBranchName || '';

		//---圆通网点到时达  特殊数据 -----//
		v.sxcp = data.orderBussinessTypeName || '';
		v.pay_type = data.pay_type || '';
		//---圆通网点到时达  特殊数据 -----//

		//---安能快运网点----//
		v.zd_exnumber = data.zdYdId || '';
		v.route = data.route || '';
		//---安能快运网点----//



		// 极兔速递
		if (temp.ExCode === 'JTSD' || temp.ExCode === 'LB') {
			//极兔速递，三段码中第二段码作为水印内容
			v.watermark = (data.shortAddress || '').split(' ')[1];
		}
		//极兔速递  运费
		v.freight = data.freight;

		//---京东自营特殊处理 运单号需要增加包裹号-----//
		v.packageYdId = data.packageYdId;
		if (temp.KddType == 5) {
			v.servicetype = data.orderBussinessTypeName;  //京东降级处理时，打印服务类型取接口返回值
			// 水印
			v.watermark = data.waterMark || '';
		}
		//---京东自营特殊处理 运单号需要增加包裹号-----//

		// 圆通圆准达 到达时间
		v.yzd_time = data.estimatedTrrivalTime || '';

		// 顺丰云打印数据
		if (comp.base.isSFCloudPrint(temp.Exid)) {
			let pdfUrlObj = {};
			let ydIdArr = data.ydId.split(',')
			ydIdArr.map(it => {
				const sfPrintDatastr = data.printDataInfo[it]
				const sfPrintDataObj = JSON.parse(sfPrintDatastr || '{}')
				const sfCloudPrintDataString = sfPrintDataObj?.cloudPrintData;
				if (sfCloudPrintDataString) {
					const sfCloudPrintDatas = JSON.parse(sfCloudPrintDataString);
					if (Array.isArray(sfCloudPrintDatas)) {
						sfCloudPrintDatas.forEach(sfCloudPrintData => {
							const { waybillNo, token, url } = sfCloudPrintData;
							pdfUrlObj[waybillNo] = `${window.location.origin}/print/center/elec/getCloudPrintPdf?token=${token}&url=${url}`;
						});

					}
				}
			})
			v.pdfUrlObj = pdfUrlObj;
		}
		//SF定制
		if (temp.ExCode === 'SF') {
			v.ewm_sfdz_map = data.twoDimensionCodeMap;
			data.ydId.split(',').length == 1 &&
				(v.ewm_sfdz = data.twoDimensionCodeMap[data.ydId]);
			v.fm_day_unit = data.sfkyDayUnit; //SF丰密 时间单位
			v.fm_day = data.sfkyDay; //SF丰密 时间天数
			v.fm_jgxx = data.codingMapping; //SF丰密 进港信息
			v.fm_cgzc = data.sourceTransferCode; //SF丰密 出港信息
			v.abFlag = data.abFlag; //A|B icon
			v.printIcon = data.printIcon; //icon打印图标标识，由0或1组成的8位字符串，顺序对应的icon分别是： 重货、蟹类、生鲜、易碎、医药类、Z标、备用、备用
			v.newAbflag = data.newAbflag;
			v.cplx = data.proName;  //产品类型
		}

		// JD 字段
		if (temp.KddType == 5) {
			v.cplx = data.proName;  //产品类型
			v.trans_type = data.transType; // 运输类型
			let printDatas = []
			v.allYdNos.forEach((o, i) => {
				if (data.printDataInfo[o]) printDatas.push(data.printDataInfo[o])
			})
			v.printPdfData = printDatas

		}

		// 众邮新增字段
		else if (temp.ExCode === 'ZYKD') {
			v.zy_sfwd = data.codingMapping;
			v.zy_sfwdbm = data.shippingBranchCode;
			v.zy_mdwd = data.sourceTransferCode;
			v.zy_mdwdbm = data.consigneeBranchCode;
			v.zy_ddsx = data.proName;
			v.watermark = data.waterMark; //水印
		} else if (temp.ExCode === 'SZKKE') {
			if (data.printDataInfo) { // 京广快运
				let tempPrintDataInfo = JSON.parse(data.printDataInfo[data.ydId] || '{}')
				v.goods_name = tempPrintDataInfo.goods_name;
				v.payment_type = tempPrintDataInfo.payment_type;
				v.spec_employee = tempPrintDataInfo.spec_employee;
				// v.weight = tempPrintDataInfo.weight;
				v.piece_number = tempPrintDataInfo.piece_number;
				v.send_company = tempPrintDataInfo.send_company;
				v.accete_company = tempPrintDataInfo.accete_company;
				v.big_char_name = tempPrintDataInfo.big_char_name;
				v.big_char_code = tempPrintDataInfo.big_char_code;
				v.spec_site = tempPrintDataInfo.spec_site
			}
			v.printDataInfo = data.printDataInfo || {}
		} else if (temp.ExCode == 'FENGWANG') {
			v.jibaodi = data.packageCenterCode;
			v.ddwd = data.consigneeBranchCode;
			v.fw_sortCode = data.shortAddress;
			v.ewm_str = data.twoDimensionCode;
			v.advertising = data.advertising;
		}
	} else {
		if (
			comp.base.getPddTempStyle(
				'pddKuaiYunZmj',
				temp.styleId,
				temp.ExCode,
			)
		) {
			v.cloudData = data.printDataInfo;
		} else if (temp.KddType === 14) {
			v.cloudData = data.ydId?.split(',')?.length > 1 ? data.printDataInfo : data.printDataInfo[data.ydId];
		} else {
			// v.cloudData =
			//     data.ydId.split(',').length > 1
			//         ? data.printDataInfo
			//         : JSON.parse(data.printDataInfo[data.ydId]);
			if (data?.ydId.split(',').length > 1) {
				v.cloudData = data.printDataInfo;
			} else if (data.printDataInfo[data.ydId]) {
				v.cloudData = JSON.parse(data.printDataInfo[data.ydId]);
			} else {
				Object.keys(data.printDataInfo).forEach(key => {
					v.cloudData = JSON.parse(data.printDataInfo[key]);
				})
			}
		}
		v.encrypted = true;
	}

	//韵达网点电子面单 --打印的数据加密串
	v.yundaPdfInfo = data.yundaPdfInfo;
	//分拣码 三个快递公司有这个值
	v.sortCode = data.sortCode || '';
	v.hasIntelliYdNo = data.intelliYdNo && data.intelliYdNo.length ? 1 : 0;
	// 原单号新单号标识
	// 单个单号直接赋值，多个单号多单号处理时再赋值
	if (data.ydNoOperationLogs && (data.ydNoOperationLogs.length === 1)) {
		let ydNoOperationLog = data?.ydNoOperationLogs[0] || {}
		v.original = ydNoOperationLog.original
	} else {
		v.ydNoOperationLogs = data?.ydNoOperationLogs || []
	}
	//菜鸟配 配的快递公司code
	if (temp.ExCode == 'CNTMS') {
		v.cnpKdCode = data.cpCode;
		v.cnpKdName = data.cpName = data.cpName.replace('菜鸟园区揽配--', '');
	}
	return v;
}

function _dealWithElecMsg({ buyerNick, msg, resultCode, printOp, orderMsg }) {
	let _msg = msg;
	if (resultCode == 511 || resultCode == 510) {
		//顺丰模板错误处理
		_msg = msg;
	} else if (resultCode == 701) {
		_msg =
			'<p>登录店铺授权失效，请登录当前店铺账号（注意是主账号）<br><a target="_blank" href="//oauth.taobao.com/authorize?response_type=code&client_id=12158997&redirect_uri=http://route.kuaidizs.cn/forward.jsp">淘宝后台-卖家中心</a>，再进入快递助手后即可</p>';
	} else if (resultCode == 702) {
		_msg =
			'手工订单淘外菜鸟账号授权已失效，<a href="javascript:void(0);" data-act-name="kddTempSet">点我去设置</a> <br>淘外菜鸟授权会有突然失效的情况出现，并不影响批打/单打页面的操作，重新授权即可正常使用。';
	} else if (resultCode == 514) {
		//顺丰老模板错误处理
		_msg =
			'您当前使用的是顺丰老模板，为确保顺丰电子面单数据准确，现将顺丰老模板下线，请将老模板右侧账号(客户编码、校验码、月结卡号)填写至新模板，<a href="javascript:;" data-act-name="addTemp">点击此处</a>添加顺丰新模板';
	} else if (msg.indexOf('非法的XML') > -1) {
		_msg =
			'买家:' +
			buyerNick +
			' 的收件人信息【姓名、电话、地址以及商品名称等】含有特殊字符，如"&,|,\\,=",请修改后再试';
	} else if (resultCode == 300 && msg.indexOf('商家没有订购') > -1) {
		//TODO 临时将服务Code提示转化为服务名称提示。后续不需要replace可以去掉。
		_msg = msg.replace(/(DELIVERY-HEAVY|SVC-COD|SVC-INSURE|SVC-INTERNATIONAL|SVC-PRIOR-DELIVERY|SVC-RECEIVER-PAY|SVC-VIP|YTO_B)/, function (match) {
			const ser = {
				'DELIVERY-HEAVY': '大件快递3.60',
				'SVC-COD': '代收货款',
				'SVC-INSURE': '保价',
				'SVC-INTERNATIONAL': '海外直送',
				'SVC-PRIOR-DELIVERY': '优先送',
				'SVC-RECEIVER-PAY': '到付',
				'SVC-VIP': 'VIP专享',
				'YTO_B': '同城配送',
			};
			return '【' + ser[match] + '】';
		});
	} else if (msg.indexOf('申请多个网点') > -1) {
		_msg = msg.replace(
			/(DELIVERY-HEAVY|SVC-COD|SVC-INSURE|SVC-INTERNATIONAL|SVC-PRIOR-DELIVERY|SVC-RECEIVER-PAY|SVC-VIP|YTO_B)/,
			function (match) {
				const ser = {
					'DELIVERY-HEAVY': '大件快递3.60',
					'SVC-COD': '代收货款',
					'SVC-INSURE': '保价',
					'SVC-INTERNATIONAL': '海外直送',
					'SVC-PRIOR-DELIVERY': '优先送',
					'SVC-RECEIVER-PAY': '到付',
					'SVC-VIP': 'VIP专享',
					YTO_B: '同城配送',
				};
				return '【' + ser[match] + '】';
			},
		);
	} else if (
		msg.indexOf('可用快递单号不足') > -1 ||
		msg.indexOf('没有足够的快递单号') > -1 ||
		msg.indexOf('电子面单账户余额不足') > -1
	) {
		_msg =
			'无可用单号！单号已经用完，请联系当地网点，充值电子面单账户;或更换其它网点';
	} else if (
		msg.indexOf('收货人详细地址信息不能为空') > -1 ||
		msg.indexOf('收货人地址省份信息不能为空') > -1
	) {
		_msg =
			'买家' +
			(buyerNick || '') +
			'订单中的收件人详细地址信息填写不完整，请点击右侧详情，填写详细地址信息后再处理';
	} else if (msg.indexOf('网点账户密码信息不完整') > -1) {
		if (printOp == 4) {
			_msg = '请前往批打页面填写快递单模板上的电子面单账户名及密码';
		} else {
			_msg =
				'请填写<a style="text-decoration: none;" href="javascript:void(0);" data-act-name="kddTempSet">快递单模板</a>上的电子面单账户名及密码';
		}
	} else if (msg.endsWith('业务服务错误不能超越母件总数')) {
		_msg =
			'当前申请子单号数量大于第一次申请的数量，请重新选择子单号数量或回收母单号重新申请新单号';
	} else if (msg.includes('虚拟号') && comp.Print.Data.platform === 'pdd') { // pdd 平台虚拟号的报错优化
		_msg = `
          ${msg}
          <p>
            <span>虚拟号分为两种：</span></br>
            <span>（1）如果收件人信息中带有分机号，在复制到手工订单页面打印时请确保收件人或地址中有完整的分机号，可正常打印。</span></br>
            <span>（2）如果收件人信息中不带有分机号，收件人手机号码为11位数字，则需要联系买家获取真实的手机号码。</span></br>
          <p>
      `;
	} else {
		_msg = msg;
	}

	if (['pdd', 'supplier'].includes(comp.Print.Data.platform)) {
		const tid = orderMsg.s_tid;
		_msg = (tid ? '【订单：' + tid + '】' : '') + '获取快递单号失败!<br>' + _msg || '';
	} else {
		_msg = (buyerNick ? '【买家：' + buyerNick + '】' : '') + '获取快递单号失败!<br>' + _msg || '';
	}

	if (resultCode == 511 || resultCode == 510) {
		_msg +=
			printOp != 4
				? ' <a href="javascript:;" data-act-name="addTemp">点击添加顺丰模板</a> ，如有问题请联系右上角客服旺旺'
				: '请前往<a href="#/printBatch/" class="close-btn">批打页面</a>重新添加一个顺丰模板，有问题请联系右上角客服旺旺';
	}
	return _msg;
}

async function _getWaybillCodeFailInfo(msg, { templateId, showContinue }) {
	return new Promise((resolve) => {
		const modelConf = showContinue
			? {
				okName: '继续',
				okCb() {
					resolve(true);
				},
			}
			: {
				okName: '我知道了',
				okCb() {
					resolve(false);
				},
			};

		model({
			...modelConf,
			type: 'confirm',
			title: '以下订单申请单号失败',
			width: 485,
			minHeight: 200,
			content: msg,
			shown: (modelDom) => {
				modelDom.addEventListener('click', function (e) {
					const actName = e.target.dataset.actName;
					if (actName === 'kddTempSet') {
						model.remove(modelDom);
						showTemplateMain({
							tempId: templateId,
							printType: 'kdd',
						});
					} else if (actName === 'addTemp') {
						model.remove(modelDom);
						addKddTemplate();
					}
					//TODO 后续对接手工单，此处呼起模板设置方式需要补充。
				});
			},
		});
	});
}
async function _getWaybillConfirmDialog(data) {
	return new Promise((resolve) => {
		const {
			// orderList,
			orderNum,
			kdName,
			isHasAutoShip,
			templateInfo,
			templateDetailInfo,
			isMerge,
		} = data;
		const waybillConfirmHtml = printItem.template.getWaybillConfirmHtml({ orderNum, kdName, isHasAutoShip });

		model({
			width: 500,
			height: 300,
			content: waybillConfirmHtml,
			cssType: 'yellow',
			shown: (modelDom) => {
				if (isHasAutoShip) {
					// 增加判断，根据后端配置判断是否支持自动发货功能
					let isOpenShip = window.erpData.userSetting.remindShip
					if (!isOpenShip) {
						modelDom.querySelector('[name=\'shipAfterPrint\'][value="0"]').checked = true;
					} else {
						modelDom.querySelector('[name=\'shipAfterPrint\'][value="1"]').checked = true;
					}
					// _$dom.off('change').on('change',function(){
					// _this.getCtrl('com.printSetting','saveUserCustomSet',{
					//     shipAfterPrint:_$dom.is(":checked")?1:0
					// // });
					// $checked = modelDom.find('[name="shipAfterPrint"]:checked');

					// if ($checked.val() == '1') {
					//     $autoShipTypeWrapper.removeClass('hide');
					// } else  {
					//     $autoShipTypeWrapper.addClass('hide');
					// }

					// argData.autoShipChangeFunc && argData.autoShipChangeFunc( $checked.val() == '1' ? 1 : 0 );
					// });
					// }
					modelDom.addEventListener('mouseover', function (event) {
						const target = event.target;
						if (target.dataset.actName === 'wenhao_img') {
							let _this = $(this),
								style,
								content = target.dataset.tipContent,
								x = event.clientX - 12,
								y = event.clientY + 22,
								width = 200;
							if (!content) {
								return;
							}
							style =
								'width:' +
								(width ? width + 'px' : 'auto') +
								';left:' +
								x +
								'px' +
								';top:' +
								y +
								'px';

							//鼠标移入
							$('body').append(
								'<div class="tip-tool-model" style="' +
								style +
								'"><img src="https://static.kuaidizs.cn/resources/img/templateEdit/icon-up.png" style="position: absolute;top: -6px;width: 15px;">' +
								content +
								'</div>',
							);
						} else {
							$('.tip-tool-model').remove();

						}
					})
				}
				modelDom.addEventListener('click', async function (e) {
					const target = e.target;
					//是否开启发货
					if (target.dataset.actName == 'shipAfterPrint') {
						console.log(e.target.value);
						const funcHook = (window.printAPI.compHookObj || {}).updateUserSettingHook
						funcHook(Number(e.target.value))
					}
					// 点击打印
					if (target.dataset.actName == 'print_confirm_submit') {

						// const result = {
						//     choosedPrinter,
						//     printNums,
						//     isPreview: _isPreview,
						// };

						// 预览发货单需要保留弹窗，所以不resolve
						resolve();
						model.remove(modelDom);
					}
				});
			},
		});
	});
}
//获取其他打印数据
async function _getOtherPrintData({ orderList, tempInfoObj }) {
	const params = _getBgPrintDataParams(orderList, tempInfoObj);
	const logList = _dealWithlogRePrintParam(params)
	// if(['yz'].includes(platformConfig.platform)){
	//     Object.assign(listParam, {
	//         // 保留加密的谜文
	//         mobileEncrypt:obj.s_encode_mobile , //  收件人手机号加密串
	//         receiverNameEncrypt: obj.s_encode_name , //  收件人加密串
	//         receiverAddressEncrypt: obj.s_encode_address , //  收件人详细地址加密串
	//     });



	const results = await urlHelper.threadRequest({
		list: logList,
		threadCount: 4,
		apiUrl: 'PRINT_GET_BG_PRINTDATA_ERP',
	});
	if (!results.isSuccess) {
		const failYdNo = [];
		results.errorResults.map(() => {
			// 拼多多取的是elecNo字段
			failYdNo.push(params[0].ydNo || params[0].elecNo);
		});
		Tatami.showFail(`检测如下运单号数据异常，建议去订单页面打印该订单${failYdNo}`);
		return Promise.reject();
	}

	const modifyYdNo = [];
	let printDatas = {};

	results.successResults.map(item => {
		const data = item.resultJson.data;
		printDatas = Object.assign(printDatas, data)
	});

	if (modifyYdNo.length) {
		Tatami.showFail(`检测如下运单号收件信息已修改，建议去订单页面打印该订单${modifyYdNo}`);
		return Promise.reject();
	}
	return Promise.resolve(printDatas);
}
function _dealWithlogRePrintParam(logParams, type = 'kdd') {
	const BATCHNUM = 10;  //5单一批量
	let logList = [],
		logItem = [],
		len = logParams.length;
	logParams.map((item, idx) => {
		idx = idx + 1;
		const isReset = idx % BATCHNUM === 0;
		logItem.push(item);
		if (isReset || idx === len) {
			type !== 'kdd' ? logList.push({ jsonParam: [...logItem] }) : logList.push({ jsonParam: JSON.stringify([...logItem]) });
			logItem = [];
		}
	});
	return logList;
}
function _getBgPrintDataParams(orderList) {
	const mallType = window.sessionStorage.getItem('mallType');
	return orderList.map(item => {
		let data = {
			elecNo: item.ydNo,
			userId: item.userId, //TODO
			kdCode: item.kdCode,
			// kddType: item.type,
			receiverProvince: item.receiverProvince,
			receiverCity: item.receiverCity,
			receiverCounty: item.receiverCounty,
			receiverAddress: item.receiverAddress,
			receiverName: item.receiverName,
			receiverTel: item.tel,
			receiverMobile: mallType == 'BBW' ? item.encodeReceiverMobile : (item.mobile || item.tel), //贝贝网底单重打手机号传加密后的给后端
			oaid: item.caid || '',

			// 加解密需要覆盖的参数
			...(item.bgParams || {}),
			sellerId: item.sellerId,

			platform: item.platform,

			exId: item.kdId,

			tids: item.tids,

			encodeTid: item.encodeTid,

			ydNo: item.exNumber,

			receiverMobile: item.receiverMobile,
		};
		return data;
	});
}
/**
 * 检查并处理收件人手机号，如果是虚拟号则去除后缀
 * @param {number|string} kddType 快递类型
 * @param {string} receiverMobile 收件人手机号
 * @returns {string} 处理后的手机号
 */
function checkReceiverMobile(kddType, receiverMobile) {
	console.log('手机号', receiverMobile);

	// 参数验证
	if (!receiverMobile) return '';

	// 快递类型不为8时，不处理虚拟号
	if (kddType != 8) return receiverMobile;

	// 检查是否是虚拟号格式（手机号-4位数字），如果是则去除后缀
	if (/^1\d{10}-\d{4}$/.test(receiverMobile)) {
		return receiverMobile.split('-')[0];
	}

	// 其他情况直接返回原手机号
	return receiverMobile;
}
export { getWaybillCodeDialog, _getWaybillConfirmDialog, getWaybillCodeByAsync, _getOtherPrintData, getWaybillCodeNew };
