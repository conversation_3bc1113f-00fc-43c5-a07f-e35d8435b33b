import * as printItem from './index';
import model from '../../common/model.ts';
import { savePrintersStorage } from '../../utils/common';

/**
 * 选择打印机
 */
async function choosePrinter(arg) {
    const {
        orderList,
        closeChoosePrinterNoti,
        defaultPrinter,
        scanChoosedPrinter,
        printType,
        templateInfo,
        templateDetailInfo,
        displayPrintNum,
        inputPrintNum,
        orderNum,
        isHasAutoShip,
        shipAfterPrint,
        isHasPreview,
        elecNoCount,
        printClient,
        previewFunc,
        printerList,
        isMerge,
        isConcatXbq,
        xbqTempisInvalid,
        source,
        isShowMorePrinter,
        xbqTemplateDetailInfo
    } = arg,
        temp = arg.templateInfo,
        that = new comp.Print.FN();
    // const isCloud = true;
    let printers;
    let dPrinter = null //从模板信息中，获取本默认应该默认选中哪个打印机
    let setting = window.erpData?.advancedSetting?.groupPrintSetJsonString
    const useLocalCatchPrint = setting?.useLocalCatchPrint == 2
    // 如果有 printerList 传入，则优先使用 printerList
    if (printerList) {
        printers = printerList;
    } else {
        ({ printers } = await printItem.getPrinterList(printClient));
    }

    const printersMap = useLocalCatchPrint ?  (Tatami.localcache.get('__printersMap__') || {}) : {};
    //融合打印
    if (isMerge) {
        //取融合打印的打印机缓存
        const id = `group${templateInfo?.id}`;
        dPrinter = printersMap[id] || defaultPrinter || that.getDefaultPrinter()
    } else {
        //取打印机缓存
        const id = templateDetailInfo?.ModeListShow?.Mode_ListShowId;
        dPrinter = printersMap[id] || templateDetailInfo.ModeTempPrintcfg.DefaultPrinter
    }
    // 业务侧有传递打印机的话，优先取业务打印机
    if(scanChoosedPrinter)dPrinter = scanChoosedPrinter
    // 判断要不要弹出选择打印机
    if (closeChoosePrinterNoti && defaultPrinter && printers.includes(dPrinter)) {
        if (defaultPrinter.match(window.DZJProve.getDZPrinter(true)) && !printItem.checkLodopProgress()) {
            return;
        }
        return {
            printer: defaultPrinter,
            printerSet: {
                isAutoShip: isHasAutoShip && shipAfterPrint,
            },
        };
    } else {
        const _data = {
            isMerge,
            orderList,
            orderNum,
            printers,
            printType: '快递单',
            kdName: temp ? temp.ExcodeName : '',
            fhName: temp ? temp.ExcodeName : '',
            bqName: temp ? temp.ExcodeName : '',
            dpName: temp ? temp.ExcodeName : '',
            cgName: temp ? temp.ExcodeName : '',
            displayPrintNum: displayPrintNum,
            inputPrintNum,
            isHasPreview: isHasPreview,
            isHasAutoShip: isHasAutoShip,
            isConcatFhd: temp.isConcatFhd,
            elecNoCount,
            templateInfo,
            templateDetailInfo,
            dPrinter: dPrinter,
            previewFunc,
            isConcatXbq,
            xbqTempisInvalid,
            source,
            isShowMorePrinter,
            useLocalCatchPrint,
            xbqTemplateDetailInfo
        };
        const tempInfoObj = {
            kdd: {
                title: '快递模板：',
                type: '快递单'
            },
            fhd: {
                title: '发货单模板：',
                type: '发货单'
            },
            bhd: {
                title: '小标签模板：',
                type: '小标签'
            },
            dpd: {
                title: '吊牌模板：',
                type: '吊牌'
            },
            tmd: {
                title: '吊牌模板：',
                type: '条码'
            },
            cgd: {
                title: '采购单模板：',
                type: '采购单'
            },
            rkd: {
                title: '入库单模板：',
                type: '入库单'
            },
            ckd:{
                title:'出库单模板：',
                type:'出库单'
            },
            thqd:{
                title:'退货清单模板：',
                type:'退货清单'
            }
        }
        let printDiglogInfo = null
        if (['thd'].includes(printType)) {
            printDiglogInfo = tempInfoObj['bhd']
        } else {
            printDiglogInfo = tempInfoObj[printType]
        }
        printDiglogInfo.tempName = temp ? (temp.ExcodeName || temp.exName) : ''
        if(printType === 'kdd' && isMerge){
            printDiglogInfo.tempName = temp ? temp.groupName : ''
        }
        _data.printType = printDiglogInfo.type
		_data.printDiglogInfo = printDiglogInfo
        return await _choosePrinterDialog(_data);
    }
}

async function _choosePrinterDialog(data) {
    return new Promise((resolve) => {
        let {
            // orderList,
            orderNum,
            printers,
            printType,
            kdName,
            fhName,
            bqName,
            dpName,
            displayPrintNum,
            inputPrintNum,
            isHasAutoShip,
            isHasPreview,
            // isConcatFhd,
            // elecNoCount,
            templateInfo,
            templateDetailInfo,
            isMerge,
            dPrinter,
            previewFunc,
            printDiglogInfo,
            isConcatXbq,
            xbqTempisInvalid,
            source,
            isShowMorePrinter,
            useLocalCatchPrint,
            xbqTemplateDetailInfo
        } = data;
        let maxMultiPrinterNum = 5;
        const choosePrinterHtmlStr = printItem.template.choosePrinterHtml({ printDiglogInfo, orderNum, kdName, fhName, bqName, dpName, printers, displayPrintNum, inputPrintNum, isHasAutoShip, isHasPreview, printType, dPrinter, isConcatXbq, xbqTempisInvalid, source, isShowMorePrinter });

        model({
            width: 500,
            minHeight: 300,
            content: choosePrinterHtmlStr,
            cssType: 'yellow',
			cancelCb:()=>{
                // 取号异常信息收集
                window.errorCollection?.customMessageUpload({
                    type: '打印机弹窗点击取消打印'
                });
            },
            shown: (modelDom) => {
                if (!source) {
                    $(modelDom).find('.input_print_num').show()
                }
                //自动发货回显
                if (isHasAutoShip) {
                    // 增加判断，根据后端配置判断是否支持自动发货功能
                    let isOpenShip = window.erpData.userSetting.remindShip
                    if (!isOpenShip) {
                        modelDom.querySelector('[name=\'shipAfterPrint\'][value="0"]').checked = true;
                    } else {
                        modelDom.querySelector('[name=\'shipAfterPrint\'][value="1"]').checked = true;
                    }
                    // _$dom.off('change').on('change',function(){
                    // _this.getCtrl('com.printSetting','saveUserCustomSet',{
                    //     shipAfterPrint:_$dom.is(":checked")?1:0
                    // // });
                    // $checked = modelDom.find('[name="shipAfterPrint"]:checked');

                    // if ($checked.val() == '1') {
                    //     $autoShipTypeWrapper.removeClass('hide');
                    // } else  {
                    //     $autoShipTypeWrapper.addClass('hide');
                    // }

                    // argData.autoShipChangeFunc && argData.autoShipChangeFunc( $checked.val() == '1' ? 1 : 0 );
                    // });
                    // }
                    modelDom.addEventListener('mouseover', function (event) {
                        const target = event.target;
                        if (target.dataset.actName === 'wenhao_img') {
                            let _this = $(this),
                                style,
                                content = target.dataset.tipContent,
                                x = event.clientX - 12,
                                y = event.clientY + 22,
                                width = 200;
                            if (!content) {
                                return;
                            }
                            style =
                                'width:' +
                                (width ? width + 'px' : 'auto') +
                                ';left:' +
                                x +
                                'px' +
                                ';top:' +
                                y +
                                'px';

                            //鼠标移入
                            $('body').append(
                                '<div class="tip-tool-model" style="' +
                                style +
                                '"><img src="https://static.kuaidizs.cn/resources/img/templateEdit/icon-up.png" style="position: absolute;top: -6px;width: 15px;">' +
                                content +
                                '</div>',
                            );
                        } else {
                            $('.tip-tool-model').remove();

                        }
                    })
                }
				// 发货单打印后发货回显
				if (printType === '发货单') {
					// 增加判断，根据后端配置判断是否支持自动发货功能
					let shipAfterPrintFhd = localStorage.getItem('shipAfterPrintFhd') === 'true'
					if (!shipAfterPrintFhd) {
						modelDom.querySelector('[name=\'shipAfterPrintFhd\'][value="0"]').checked = true;
					} else {
						modelDom.querySelector('[name=\'shipAfterPrintFhd\'][value="1"]').checked = true;
					}

					modelDom.addEventListener('mouseover', function (event) {
						const target = event.target;
						if (target.dataset.actName === 'wenhao_img') {
							let _this = $(this),
								style,
								content = target.dataset.tipContent,
								x = event.clientX - 12,
								y = event.clientY + 22,
								width = 200;
							if (!content) {
								return;
							}
							style =
								'width:' +
								(width ? width + 'px' : 'auto') +
								';left:' +
								x +
								'px' +
								';top:' +
								y +
								'px';

							//鼠标移入
							$('body').append(
								'<div class="tip-tool-model" style="' +
								style +
								'"><img src="https://static.kuaidizs.cn/resources/img/templateEdit/icon-up.png" style="position: absolute;top: -6px;width: 15px;">' +
								content +
								'</div>',
							);
						} else {
							$('.tip-tool-model').remove();

						}
					})
				}
                // 多台打印机初始化
                let printList = localStorage.getItem('morePrinterList')
                let addPrintBox = $(modelDom).find('.choose_print_box') //多个选择打印机容器
                let $morePrinterTip = $(modelDom).find('.choose_print_tip');

                //如果存在打印机列表并且开启了多台打印机 需要进行初始化
                if (isShowMorePrinter && !!printList && useLocalCatchPrint) { //多打印机初始化选择的列表
                    printList = printList.split(',')
                    printList.forEach((o, i) => {
                        if (i === 0) {
                            // 如果已经删除了，无需在绑定
                            if (!printers.find(i => i === o)) {
                                return;
                            }
                            addPrintBox.find('.select_system_printer').val(o)
                        } else {
                            let pLeng = addPrintBox.children() // 当前选择数量
                            // 逐个添加本地缓存的打印机
                            let { printHtml } = morePrintAddHtml(pLeng, printers, o,modelDom)
                            addPrintBox.append(printHtml)
                            updateMorePrinterTip($morePrinterTip, printList.length);
                        }
                    });
                    // 打印机数量超过设定最大打印机数，隐藏添加打印机按钮
                    if (printList.length >= maxMultiPrinterNum) $(modelDom).find('.add_chooseprint_btn').hide();
                    updateChooseItemDisab(modelDom)
                }
                modelDom.addEventListener('click', async function (e) {
                    const target = e.target;
                    //是否开启发货
                    if (target.dataset.actName == 'shipAfterPrint') {
                        console.log(e.target.value);
                        const funcHook = (window.printAPI.compHookObj || {}).updateUserSettingHook
                        funcHook(Number(e.target.value))
                    }
					if (target.dataset.actName == 'shipAfterPrintFhd') {
						if (e.target.value == '1') {
							localStorage.setItem('shipAfterPrintFhd', 'true')
						} else {
							localStorage.setItem('shipAfterPrintFhd', 'false')
						}
					}
                    // 点击打印
                    if (target.dataset.actName == 'print_confirm_submit') {
                        let choosedPrinter = null
                        if(isShowMorePrinter){
                            choosedPrinter = []
                            let addPrintBox = $(modelDom).find('.choose_print_box') //多个选择打印机容器
                            let pLeng = addPrintBox.children() // 当前选择数量
                            pLeng.each((i,o)=>{
                                let printerName = $(o).find('.select_system_printer').val()
                               if(printerName !== '无可用打印机') choosedPrinter.push(printerName)
                            })
                        }else{
                            choosedPrinter =  (modelDom.querySelector('.select_system_printer') || {}).value;
                        }
                        let printNums = parseInt((modelDom.querySelector('.select_print_count') || {}).value || 1, 10);
                        if (['吊牌', '条码'].includes(printType)) {
                            printNums = parseInt((modelDom.querySelector('.input_print_count') || {}).value || 1);
                        }
                        comp.Print.setPrintCopies(printNums);

                        // const _isAutoShip =
                        const _isPreview = target.dataset.type === 'preview';
                        const dpdPrintType = modelDom.querySelector('input[name="dpPrintNumType"]:checked')?.value

                        // if(dPrinter != choosedPrinter){
                        //     (new comp.Print()).setDefaultPrinter(printType, choosedPrinter, templateInfo.Mode_ListShowId);
                        // }
                        // 如果开启是多台打印机，需要换成当前选择打印机，下次打印回显
                        if(isShowMorePrinter) {
                            localStorage.setItem('morePrinterList',choosedPrinter.join(','));
                        }
						if (_isPreview && printType === '快递单') {
							if ([14, 15, 2, 13, 16, 17].includes(templateInfo.KddType)) {
								Tatami.showNoti('当前模板暂不支持预览')
								return
							}
						}
                        let result = {
                            choosedPrinter,
                            printNums,
                            isPreview: _isPreview,
                            dpdPrintType: dpdPrintType
                        };
                        if (isConcatXbq) {
                            const xbqChoosedPrinter = (modelDom.querySelector('.select_xbq_printer') || {}).value;
                            result = {
                                ...result,
                                xbqChoosedPrinter
                            }
                        }


                        // 预览发货单需要保留弹窗，所以不resolve
                        if (_isPreview && printType === '发货单') {
                            previewFunc && previewFunc(result);
                        } else {
							window.errorCollection?.customMessageUpload({
                                type: '打印机弹窗点击打印',
                                data: {
                                    printNums: `打印份数${printNums}`,
                                }
                          });
                            resolve(result);
                            model.remove(modelDom);
                        }
                    }
                    // 调转吊牌设置
                    if (target.dataset.actName == 'dp_rule_set') {
                        window.location.hash = '/settings/system?introName=printTagSet'
                        model.remove(modelDom);
                    }
                    // 临时关闭吊牌交替打印 关闭是否需要重新更新打印机禁用？ 待定
                    if (target.dataset.actName == 'concatPrintDp') {
                        if (e.target.value == '1') {
                            $(modelDom).find('.xbq_content_box').show()
                            isConcatXbq = true
                        } else {
                            $(modelDom).find('.xbq_content_box').hide()
                            isConcatXbq = false
                        }

                    }
                    // 吊牌数量计算规则
                    if (target.dataset.actName == 'dpPrintNumType') {
                        if (e.target.value == '0') {
                            $(modelDom).find('.input_print_num').show()
                        } else {
                            $(modelDom).find('.input_print_num').hide()
                        }

                    }
                    // 添加打印机
                    if (target.dataset.actName == 'add_chooseprint_btn') {
                        //打点：添加打印机
                        //    Tatami.pub('Tatami.clickPoint.manualTrigger', {
                        //        point: '24629.45235.50491.50492.59469.74563.74564',
                        //        _fm: '10905',
                        //    });
                        let pLeng = addPrintBox.children() // 当前选择数量
                        let { printHtml, currentPrints } = morePrintAddHtml(pLeng, printers,null,modelDom)
                        addPrintBox.append(printHtml)
                        updateMorePrinterTip($morePrinterTip, pLeng.length + 1)
                        if (pLeng.length >= maxMultiPrinterNum - 1) $(e.target).hide();
                        updateChooseItemDisab(modelDom)
                    }
                    // 删除打印机
                    if (target.dataset.actName == 'delete_choose_print') {
                        //打点：删除打印机
                        // Tatami.pub('Tatami.clickPoint.manualTrigger', {
                        //     point: '24629.45235.50491.50492.59469.74563.74565',
                        //     _fm: '10906',
                        // });
                        $(e.target).parent().remove();
                        let addPrintBox = $(modelDom).find('.choose_print_box')
                        let pLeng = addPrintBox.children()
                        let currentPrints = []
                        pLeng.each((i, o) => {
                            let printerName = $(o).find('.select_system_printer').val()
                            currentPrints.push(printerName)
                        })
                        if (pLeng.length < maxMultiPrinterNum) addPrintBox.find('.add_chooseprint_btn').show();
                        updateMorePrinterTip($morePrinterTip, pLeng.length)
                        updateChooseItemDisab(modelDom)
                    }
                });
                // 切换打印机操作，对其他select联动禁用对应打印机
                 $(modelDom).on('change', '.select_system_printer', function (e) {
                    console.log('打印机切换');
                    // 如果不存在多台打印机或者未开启吊牌打印，此操作无效，只存在一台打印机
                    const target = e.target;
                    let pLeng = $(modelDom).find('.select_system_printer') // 当前选择数量
                    let currentPrints = []
                    pLeng.each((i, o) => {
                        let printerName = $(o).val()
                        currentPrints.push(printerName)
                    })
                    updateChooseItemDisab(modelDom)
                    // 切换打印机 非多台打印机需存储模板对应绑定的打印机
                    if (target.dataset.actName === 'selectPrinter' && !isShowMorePrinter) {
                        const printerName = target.value;
                        let id = '';
                        //融合打印
                        if (isMerge) {
                            id = `group${templateInfo?.id}`;
                        } else {
                            id = templateDetailInfo?.ModeListShow?.Mode_ListShowId;
                        }
                        //对选择的打印机进行本地存储
                        savePrintersStorage(id, printerName);
                    }
                    // 切换打印机 非多台打印机需存储模板对应绑定的打印机
                    if (target.dataset.actName === 'xbqSlectPrinter') {
                        const printerName = target.value;
                        let id = xbqTemplateDetailInfo?.ModeListShow?.Mode_ListShowId;
                        //对选择的打印机进行本地存储
                        localStorage.setItem(`xpqPrint_${id|| 0}`,printerName)
                    }

                })
                // 开启交替打印吊牌时初始化
                if (isConcatXbq) {
                    // 吊牌名称回显
                    let dpNameBox = modelDom.querySelector('.dp_name')
                    let dpPrintNum = modelDom.querySelector('.dp_print_num')
                    let printTagSet = comp.Print.Data.printTagSet

                    // 吊牌规则设置初始化
                    if (printTagSet?.printTagNumSwitch == 1) {
                        dpPrintNum.innerHTML = printTagSet?.printTagNum || '未设置'
                    } else {
                        dpPrintNum.innerHTML = '与订单商品所需的数量一致'
                    }
                    if (printTagSet?.tagTemplateSwitch == 1) {
                        let dpNameBoxHtml = printTagSet?.tagTemplate?.ExcodeName || ''
                        if (xbqTempisInvalid) dpNameBoxHtml += `<span style="font-size:12px;color:red">（已删除）</span>`
                        dpNameBox.innerHTML = dpNameBoxHtml
                    } else {
                        dpNameBox.innerHTML = '无'
                        dpNameBox.style.color = 'red'
                    }
                    // 标签打印机选择初始化 模板绑定打印机》上次使用打印机》默认打印机
                    // 并且需要先确认快递单打印机是否占用，优先保障快递单打印机
                    let dpDefaultPrint = xbqTemplateDetailInfo?.ModeTempPrintcfg?.DefaultPrinter
                    //获取快递单已经展示的打印机
                    let addPrintBox = $(modelDom).find('.choose_print_box')
                    let pLeng = addPrintBox.children() // 当前选择数量
                    let currentPrints = []
                    pLeng.each((i, o) => {
                        let printerName = $(o).find('.select_system_printer').val()
                        currentPrints.push(printerName)
                    })
                    if(currentPrints.includes(dpDefaultPrint) || !printers.includes(dpDefaultPrint)) dpDefaultPrint = localStorage.getItem(`xpqPrint_${xbqTemplateDetailInfo?.ModeListShow?.Mode_ListShowId || 0}`)
                    if(currentPrints.includes(dpDefaultPrint)) dpDefaultPrint = null
                    if(!!dpDefaultPrint)localStorage.setItem(`xpqPrint_${xbqTemplateDetailInfo?.ModeListShow?.Mode_ListShowId || 0}`,dpDefaultPrint)
                    $(modelDom).find('.select_xbq_printer').val(dpDefaultPrint)
                    //改造统一一个方法进行打印机初始化及更改禁用处理
                    updateChooseItemDisab(modelDom)
                }


            },
        });
    });
    // 添加选择打印机框
    function morePrintAddHtml(pLeng, printers, dPrinter,modelDom) {
        let currentPrints = []
        // 2、吊牌打印机
        let bqPrinterDom =  $(modelDom).find('.select_xbq_printer')
        let bqPrinter =  bqPrinterDom?.val()
        let isDisab = printers.length <= (data.isConcatXbq ? pLeng.length +1 :pLeng.length )//打印机缺失是否禁用

        const disabledHtml = `<option  value="无可用打印机" selected } >无可用打印机</option>` // 禁用展示
        pLeng.each((i, o) => {
            let printerName = $(o).find('.select_system_printer').val()
            currentPrints.push(printerName)
        })
        if(bqPrinter && !currentPrints.includes(bqPrinter)){
            currentPrints.push(bqPrinter)
        }
        let selectPrint = dPrinter || printers.find(o => !currentPrints.includes(o)) // 回显or新增
        let choosePtintHtml = printers.map(printerName => {
            // let isIncludes = currentPrints.includes(printerName)
            return `<option  value="${printerName}" ${selectPrint == printerName ? 'selected' : ''} > ${printerName} </option>`
        }).join('')
        currentPrints.push(selectPrint) // 先放进去丢出去更新可选择项
        let printHtml = `<p class="mt_10">
    <label >选择打印机${pLeng.length + 1}：</label>
    <select ${isDisab ? 'disabled' : ''}  style="width: 200px;margin-left: -6px;" class="select_system_printer" >
        ${isDisab ? disabledHtml : choosePtintHtml}
    </select>
    <button typr="text" class="delete_choose_print" style="text-decoration: underline;color:#3595e1" data-act-name="delete_choose_print">删除</button>
    </p>`
        return {
            printHtml,
            currentPrints
        }
    }
    // function updateChooseItemDisab(printBox, currentPrinter) {
    //     let pLeng = $(printBox).children() // 当前选择数量
    //     pLeng.each((i, o) => {
    //         let itName = $(o).val()
    //         if (itName === currentPrinter) {
    //             $(o).attr('disabled', 'disabled')
    //             $(o).html(itName + '（不可选中）')
    //         } else {
    //             $(o).removeAttr('disabled')
    //             $(o).html(itName)
    //         }
    //     })
    // }
    // 暂定，目前需求需要每次增加或者更改打印机，可选择项联动
    function updateChooseItemDisab(modelDom) {
        // 打印机盒子
        // 1、快递打印机
        let addPrintBox = $(modelDom).find('.choose_print_box')
        // 2、吊牌打印机
        let bqPrinterDom =  $(modelDom).find('.select_xbq_printer')
        let bqPrinter =  bqPrinterDom?.val()
        let pLeng = addPrintBox.children() // 当前选择数量
        let currentPrints = []
        pLeng.each((i, o) => {
            let printerName = $(o).find('.select_system_printer').val()
            currentPrints.push(printerName)
        })
        // 先判断是否默认展示的吊牌打印机跟快递单打印机重叠
        // 如果打印机本身已经是快递单打印机先清空，然后重新设置
        if(currentPrints.includes(bqPrinter)) bqPrinter = null;
        // 如果未清空代表不重叠，需要加入到当前使用打印机列表里面
        if(bqPrinter && !currentPrints.includes(bqPrinter)){
            currentPrints.push(bqPrinter)
        }
        pLeng.each((i, o) => {
            let selectBox = $(o).find('.select_system_printer')
            i !== 0 && $(o).find('label').html(`选择打印机${i + 1}：`)
            let currentPrint = selectBox.val()
            let options = selectBox.children()
            options.each((inx, it) => {
                let itName = $(it).val()
                if (itName !== currentPrint && currentPrints.includes(itName)) {
                    $(it).attr('disabled', 'disabled')
                    $(it).html(itName + '（不可选中）')
                } else {
                    $(it).removeAttr('disabled')
                    $(it).html(itName)
                }
            })
        })
        // 吊牌打印机选项的联动禁用
        if(data.isConcatXbq) {
            let options = bqPrinterDom.children()
            options.each((inx, it) => {
                let itName = $(it).val()
                if (itName !== bqPrinter && currentPrints.includes(itName)) {
                    $(it).attr('disabled', 'disabled')
                    $(it).html(itName + '（不可选中）')
                } else {
                    // 如果不存在打印机或者已经被清空，赋值第一个选择的打印机成为默认选择
                    //需要考虑先开启多台打印机并且占用了所有打印机再开启吊牌打印机情况
                    if (!bqPrinter) {
                        $(it).attr('selected', 'selected')
                        bqPrinter = itName
                    }
                    $(it).removeAttr('disabled')
                    $(it).html(itName)
                }
            })
        }
    }

}
/**
 *
 * @param {*} dom
 * @param {*} printerNum
 * @returns
 *
 * 选择1台，不提示
选择2台，提示建议超过10单
选择3台，提示建议超过10单
选择4单，提示建议超过16单
选择5单，提示建议超过25单
 */
function updateMorePrinterTip(dom, printerNum) {
    let printOrderNum = 0;
    switch (printerNum) {
        case 1:
            dom.html('');
            return;
        case 2:
        case 3:
            printOrderNum = 10;
            break;
        default:
            printOrderNum = Math.pow(printerNum, 2);


    }
    dom.html(`*注意: 交替打印暂不支持多台打印机同步打印功能。当前选择${printerNum}台打印机，建议打印超过${printOrderNum}单，避免部分打印机未参与到多台打印机一起打印中`);
}
export { choosePrinter };
