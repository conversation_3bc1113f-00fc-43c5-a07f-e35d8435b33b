// 打印关键信息上报
const uploadPrintLog = (type, { orderList, printTraceId, fjrInfoMap, templateInfo }) => {
	const sourceObj = {
		merge: '聚合',
		single: '单模板',
		async: '边申请',
		scan: '扫描',
	}
	const uploadList = orderList.map((item) => {
		return {
			isDecrypted: item.isDecrypted,
			tids: item.tids,
			tradeReceiverList: item.tradeReceiverList,
			receiverMobile: item.receiverMobile,
			receiverName: item.receiverName,
			receiverAddress: item.receiverAddress,
			receiverTown: item.receiverTown,
		}
	})

	window.errorCollection?.customMessageUpload({
		type: `${sourceObj[type]}打印接收的数据`,
		data: {
			uploadList: uploadList,
			// fjrInfoMap: fjrInfoMap,
			spanId: printTraceId,
			templateInfo: templateInfo,
		}
	});
};
export {
	uploadPrintLog
}
