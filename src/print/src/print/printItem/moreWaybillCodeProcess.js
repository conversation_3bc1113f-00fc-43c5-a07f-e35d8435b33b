import * as printItem from './index';

const JdTempForUrl ={
	20090:100001,
	20091:100002,
	20092:43,
}
async function moreWaybillCodeProcess(arg) {
    return new Promise((resolve) => {
        const { orderList, sfZDQuantity, waybillCodeCount, temp,isZiMuJian } = arg;

        const isPdd = comp.base.getPddTempStyle(
            'pddKuaiYunZmj',
            temp.styleId,
            temp.ExCode,
        );
        if (isPdd) {
            resolve(_moreWaybillCodeDealData(orderList, sfZDQuantity,temp));
		} else if ((sfZDQuantity || sfZDQuantity === 0) && (![3, 5, 7, 9, 13, 14, 16].includes(temp?.KddType) && ['SF', 'SZKKE'].includes(temp?.ExCode))) {
            //子母件申请多单号数据处理
            resolve(_zimujianMoreWaybillCode(orderList, sfZDQuantity, temp));
        } else if (waybillCodeCount && waybillCodeCount != 1 &&  temp?.KddType != 5) {
            //批打、手工单 单打多面单号数据处理
            resolve(_moreWaybillCodeDealData(orderList, waybillCodeCount, temp));
        } else {
            if( temp.KddType == 5){ // 京东云一联单单独处理
                resolve(_jdYunYilianDealData(orderList,waybillCodeCount,temp) );
            }else{
                resolve(orderList);
            }
        }
    });
}

function _zimujianMoreWaybillCode(orderList, sfZDQuantity, temp) {
    let tradeList = [],
        packNum = sfZDQuantity,
        _v;
    if (!temp || /^(SF|CN7000001003751|SURE|DBKD|SZKKE)$/.test(temp.ExCode)) {
        //子母件格式：单独打印母单号,目前有：SF/跨越子母件/速尔子母件 德邦;
        orderList?.each(function (i, v) {
            v.baoguoCount = sfZDQuantity + 1;

            //暂时都取值母单号的
            // v.ewm_sfdz = (v.ewm_sfdz_map || {})[v.exnumber];

            _v = Object.assign({}, v);
            _v.sfZDydId = '';
            _v.baoguoID = 1;
            _v.bgjs = _v.zmj_sum = _v.baoguoID + '/' + packNum;
            _v.isZiMuJian = true;
            _v.hj_weight ? ( _v.hj_weight = v.hj_weight / packNum ) : '';
            _v.log_weight = parseInt(_v.log_weight / packNum);

            // 京广快运重量
            if(temp.ExCode === 'SZKKE'){
                _v.hj_weight ? ( _v.hj_weight = v.hj_weight / sfZDQuantity ) : '';
            }

            //母单号：顺定制二维码取值母单号的
            _v.ewm_sfdz = (_v.ewm_sfdz_map || {})[_v.exnumber];

            if('SZKKE' === temp.ExCode){
                if( _v.printDataInfo[_v.pYdNo]) tradeList.push(_v);
            }else{
                tradeList.push(_v);
            }
            const sfZDydIdList = v.sfZDydId ? v.sfZDydId.split(',').filter(id=>{
                if('SZKKE' === temp.ExCode){
                    return (id !== _v.exnumber && _v.printDataInfo[id]);
                }else{
                    return (id !== _v.exnumber);
                }
            }) : [];
            if (sfZDydIdList.length > 0) {
                sfZDydIdList.each(function (key, ydNo) {
                    if (key >= sfZDQuantity) {
                        return false;
                    }
                    _v = Object.assign({}, v);
                    if(temp.ExCode === 'DBKD' || temp.ExCode === 'SZKKE'){  //德邦一联单字母件
                        _v.sfZDydId = ydNo ;//子单号
                        _v.txm_number = ydNo ;//运单号条形码打印子单号
                        _v.fhdexnumber = _v.exnumber; // 母单号打印
                        _v.exnumber = ydNo; //子单号

                    }else{
                        _v.exnumber = ydNo; //子单号

                        //子单号：顺丰定制二维码取值子单号的
                        _v.ewm_sfdz = (_v.ewm_sfdz_map || {})[ydNo];

                        _v.sfZDydId = v.exnumber; //母单号
                    }
                    _v.baoguoID = key + 2;
                    _v.bgjs = _v.zmj_sum = _v.baoguoID + '/' + packNum;
                    _v.isZiMuJian = true;
                    _v.hj_weight
                        ? (_v.hj_weight = _v.hj_weight / packNum)
                        : ''; //重量需要除以单号申请个数

                    //京广快运重量
                    if(temp.ExCode === 'SZKKE'){
                        _v.hj_weight ? ( _v.hj_weight = _v.hj_weight / sfZDQuantity ) : '';
                    }
                    if (_v.log_weight) {
                        //日志中存储的打印重量
                        if (key == sfZDQuantity - 1) {
                            _v.log_weight =
                                    _v.log_weight -
                                    parseInt(_v.log_weight / packNum) *
                                        sfZDQuantity;
                        } else {
                            _v.log_weight = parseInt(
                                _v.log_weight / packNum,
                            );
                        }
                    }


                    // 抖音顺丰子母件处理
                    {
                        const printData = _v.electronData.printDataInfo?.[ydNo];
                        if (printData) {
                            _v.cloudData = JSON.parse(printData);
                        }
                    }

                    // 当前单号是否原单号  1原单号 0新单号
                    if(v.ydNoOperationLogs && v.ydNoOperationLogs.length){
                        let ydNoOperationLog = v.ydNoOperationLogs.find(o=> o.ydId === ydNo)
                        if(ydNoOperationLog) _v.original = ydNoOperationLog?.original
                    }
                    tradeList.push(_v);
                });
            }
        });
    } else if (/^(ANEKY|DISTRIBUTOR_13413991)$/.test(temp.ExCode)) {
        //子母件格式：不单独打印母单号,目前有：安能子母件/京东快递包裹号;
        tradeList = _zmjMoreElecNoOther(orderList, sfZDQuantity);
    } else if (temp.ExCode === 'JD') {
        //子母件格式：不单独打印母单号,目前有：安能子母件/京东快递包裹号;
        tradeList = _zmjMoreElecNoOther(orderList, sfZDQuantity, 'packageYdId',temp);
    } else {
        tradeList = orderList;
    }
    return tradeList;
}

/**
 * [_zmjMoreElecNoOther 不需要单独打印母单号的子母件数据处理]
 * @param  {array} orderList   [订单列表]
 * @param  {number} sfZDQuantity [面单号个数]
 * @param  {[type]} zYdNoName   [子单号变量的名称，鉴于不同模板子单号的dataName可能会不一样]
 * @return {[type]}             [description]
 */
function _zmjMoreElecNoOther(orderList, elecNoCount, zYdNoName,temp) {
    let tradeList = [],
        zYdNo;
    zYdNoName = zYdNoName || 'sfZDydId';
    orderList &&
        orderList.each(function (i, v) {
            v.isZiMuJian = true;
            v.baoguoCount = elecNoCount;
            zYdNo = (v[zYdNoName] || '').split(',');
            let PrintDatas = [] , wayBillNos = [] , geturl = '',customUrl = ''; //京东云一联单数据处理
            if(comp.base.getTempStyle('isJDL',temp?.styleId,temp)){
                if(v.printPdfData && v.printPdfData !== ''){
                    const printPdfData_ = v.printPdfData;
                    const printPdfData = JSON.parse(printPdfData_ || {});
                    const prePrintDatas = printPdfData.pullDataRespDTO.prePrintDatas;
                    geturl = printPdfData.standardTemplate.standardTemplates[0];
                    customUrl = printPdfData.customUrl; //京东云一联单自定义区域url
                    prePrintDatas && $.each(prePrintDatas, function (i, it) {
                        PrintDatas.push(it.perPrintData);
                        wayBillNos.push(it.wayBillNo);

                    });
                }
            }
            if (zYdNo.length) {
                zYdNo.each(function (key, ydNo) {
                    v.bgjs = key + 1 + '/' + elecNoCount;
                    if (key >= elecNoCount) {
                        return false;
                    }
                    const _v = printItem.deepClone(v);
                    _v[zYdNoName] = ydNo;
                    _v.baoguoID = key + 1;
                    if(PrintDatas.length > 0){
                        _v.jdprintData = PrintDatas[key];
                    }
                    if(wayBillNos.length > 0 ){
                        _v.wayBillNo = wayBillNos[key];
                    }
                    _v.geturl = geturl; //京东云一联单的url
                    _v.customUrl = customUrl; // 京东云一联单自定义区域url
                    tradeList.push(_v);
                });
            } else {
                tradeList.push(v);
            }
        });
    return tradeList;
}

function _moreWaybillCodeDealData(orderList, elecNoCount,temp) {
    let tradeList = [],
        zdExnumber = null, //针对安能快运的子单号
        yundaPdfInfo = [],
        sendBranchList = [],
        packageYdIdArray;
    orderList &&
        orderList.map((v) => {
            v.baoguoCount = elecNoCount;
            if (v.exnumber && v.exnumber.split(',').length > 0) {
                if (v.packageYdId && v.packageYdId.split(',').length > 0) {
                    //处理JD的带包裹号的运单号数据项 在多单号打印的时候
                    packageYdIdArray = v.packageYdId.split(',');
                }

                //寄件网点逻辑
                if (v.sendBranch && v.sendBranch.split(',').length > 0) {
                    sendBranchList = v.sendBranch.split(',');
                }
                let PrintDatas = [] , wayBillNos = [] , geturl = '',customUrl = ''; //京东云一联单数据处理
                if(temp.KddType == 5 ){
                    // if(comp.base.getTempStyle('isJDL',temp.styleId,temp)){
                    if(v.printPdfData && v.printPdfData !== ''){
                        const printPdfData_ = v.printPdfData;
                        const printPdfData = printPdfData_.split(';') || [];
                        printPdfData.map((item,i)=>{
                            const item_ = JSON.parse(item || {}); //转json
                            const prePrintDatas = item_.pullDataRespDTO.prePrintDatas;
                            if(item_.pullDataRespDTO.code == 1){ //返回数据成功

                                PrintDatas.push(prePrintDatas[0].perPrintData);
                                wayBillNos.push(prePrintDatas[0].wayBillNo);
                            }

                            geturl = item_.standardTemplate.standardTemplates[0];
                            customUrl = item_.customUrl; //京东云一联单自定义区域url
                        });

                    }
                }
                yundaPdfInfo = []; //韵达打印内容----加密字符串，申请多单号，打印内容处理
                if (v.yundaPdfInfo) {
                    yundaPdfInfo = v.yundaPdfInfo.split(',');
                }
                v.sfZDydId && (zdExnumber = v.sfZDydId.split(','));
                v.exnumber.split(',').each((key, ydNo) => {
                    v.bgjs = key + 1 + '/' + elecNoCount;
                    if (key >= elecNoCount) {
                        return false;
                    }
                    const _v = printItem.deepClone(v);
                    _v.exnumber = ydNo;
                    zdExnumber && (_v.sfZDydId = zdExnumber[key] || '');
                    if (packageYdIdArray) {
                        _v.packageYdId = packageYdIdArray[key]; //处理JD的带包裹号的运单号数据项 在多单号打印的时候
                    }
                    if (sendBranchList) {
                        //寄件网点
                        _v.jjwd = sendBranchList[key];
                    }
                    if(PrintDatas.length > 0){ //京东云一联单数据
                        _v.jdprintData = PrintDatas[key];
                    }
                    if(wayBillNos.length > 0 ){
                        _v.wayBillNo = wayBillNos[key];
                    }
                    _v.baoguoID = key + 1;
                    _v.ewm_sfdz = (_v.ewm_sfdz_map || {})[ydNo];
                    _v.geturl = geturl; //京东云一联单的url
                    _v.customUrl = customUrl; // 京东云一联单自定义区域url
                    if(temp.KddType == 14){
                        _v.platformUserTemplate = v.platformUserTemplateMap[ydNo]
                    }
                    if(v.ydNoOperationLogs && v.ydNoOperationLogs.length){
                        let ydNoOperationLog = v.ydNoOperationLogs.find(o=> o.ydId === ydNo)
                        if(ydNoOperationLog) _v.original = ydNoOperationLog?.original
                    }
                    if (v.cloudData) {
                        //多个单号，更改云打印数据里面的单号
                        if (_v.encrypted) {
                            try {
                                _v.cloudData = v.cloudData[ydNo]? JSON.parse(v.cloudData[ydNo]):v.cloudData;
                            } catch (error) {
                                _v.cloudData = v.cloudData[ydNo]
                            }
                        } else {
                            _v.cloudData.data.waybillCode = ydNo;
                            _v.cloudData.data.packageInfo &&
                                (_v.cloudData.data.packageInfo.currentPackageSequence =
                                    key + 1);
                        }
                    }
                    _v.yundaPdfInfo = yundaPdfInfo[key] || '';
                    _v.hj_weight
                        ? (_v.hj_weight = parseFloat(
                            _v.hj_weight / elecNoCount,
                        ))
                        : '';
                    if (_v.log_weight) {
                        if (key == elecNoCount - 1) {
                            _v.log_weight =
                                _v.log_weight -
                                parseInt(_v.log_weight / elecNoCount) *
                                    (elecNoCount - 1);
                        } else {
                            _v.log_weight = parseInt(
                                _v.log_weight / elecNoCount,
                            );
                        }
                    }
                    tradeList.push(_v);
                });
            } else {
                tradeList.push(v);
            }
        });
    return tradeList;
}

function _jdYunYilianDealData(orderList, elecNoCount,temp){
    const tradeList = [];
    let baoguoID = 0
    orderList && orderList.each(function(i, v) {
        v.baoguoCount = elecNoCount;
        let PrintDatas = [] , wayBillNos = [] , geturl = '',customUrl; //京东云打印控件数据处理
        if(v.printPdfData && v.printPdfData !== ''){
            const printPdfData_ = v.printPdfData;
            printPdfData_.forEach((it,idx) => {
                const printPdfData = JSON.parse(it || {});
                const prePrintDatas = printPdfData.pullDataRespDTO && printPdfData.pullDataRespDTO.prePrintDatas;
				if(JdTempForUrl[temp?.Exid]){
					geturl = printPdfData.standardTemplate && printPdfData.standardTemplate.standardTemplates.find(o=> o.standardTemplateId == JdTempForUrl[temp?.Exid]);
				}else{
					geturl = printPdfData.standardTemplate && printPdfData.standardTemplate.standardTemplates[0];
				}
                customUrl = printPdfData.customUrl;
                prePrintDatas && $.each(prePrintDatas, function (index,item) {
                    // PrintDatas.push(item.perPrintData);
                    // wayBillNos.push(item.wayBillNo);
                    const _v = printItem.deepClone(v);
                    _v.jdprintData = item.perPrintData;
                    _v.wayBillNo = item.wayBillNo;
                    _v.geturl = geturl;
                    _v.exnumber = _v.allYdNos[idx]
                    _v.customUrl = customUrl;
                    baoguoID++
                    _v.baoguoID = baoguoID;
                    _v.bgjs = baoguoID + '/' + printPdfData_.length
                    _v.hj_weight ? ( _v.hj_weight = parseFloat(_v.hj_weight / elecNoCount) ) : '';
                    if( _v.log_weight ){
                        if( i == elecNoCount - 1 ){
                            _v.log_weight =  _v.log_weight - parseInt(_v.log_weight / elecNoCount ) * (elecNoCount - 1);
                        }else{
                            _v.log_weight =  parseInt(_v.log_weight / elecNoCount );
                        }
                    }
                    // 顺丰云打印
                    _v.pdfUrlObj && (_v.pdfUrl = _v.pdfUrlObj[item.wayBillNo] || '');
                    // 当前单号是否原单号  1原单号 0新单号
                    if(v.ydNoOperationLogs && v.ydNoOperationLogs.length){
                        let ydNoOperationLog = v.ydNoOperationLogs.find(o=> o.ydId === item.wayBillNo)
                        if(ydNoOperationLog) _v.original = ydNoOperationLog?.original
                    }
                    tradeList.push(_v);
                });
            });
        }

    });
    return tradeList;

}

export { moreWaybillCodeProcess };
