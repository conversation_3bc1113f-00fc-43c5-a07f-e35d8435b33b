import  model  from './../../common/model.ts';
import  { get ,post } from './../../common/http';
import {showTemplateMain} from '../printItem/compPrint';
import { configWdHasElecNum } from '../config';
import { isJDNewWjTpl } from '../printFlow/platformConfig';

/**
 * 1. 获取菜鸟网点设置
 * 2. 获取网点账户密码设置
 * 3. 获取京东设置
 * 4. 获取拼多多网点设置
 * 5. 获取淘外菜鸟设置
 * 6. 获取物流云网点设置
 */
function getYunBranchSet(temp){
    if(temp.KddType === 1 || temp.ExCode == 'CNTMS'){ //一联单
        return Promise.resolve({});
    }else if(temp.KddType == 3){
        return getCnWdSet(temp);
    }else if(temp.KddType == 7){
        if(['pdd'].includes(comp.Print.Data.platform)) return getPddWdSet(temp);
        else return getPwdzWdSet(temp);
    }else if(temp.KddType == 2 || (temp.KddType == 5 && temp.ExCode === 'JD' && !isJDNewWjTpl(temp))){
        return getWdSet(temp);
    }else if(temp.KddType == 8){ // 抖音电子面单
        if(['nzw'].includes(comp.Print.Data.platform)) return getDYOutSet(temp);
        else  return getDYSet(temp);
    }else if(temp.KddType == 9){ // 快手电子面单
        return getKsSet(temp);
    }else if(temp.KddType == 10){ // 美团电子面单
        return getTHHSet(temp);
    }else if(temp.KddType == 5){ // 美团电子面单
        return getJDSet(temp);
    }else if(temp.KddType == 13){
        return getXhsSet(temp);
    }else if(temp.KddType == 14){
        return getSphSet(temp);
    }else if(temp.KddType == 15){
        return getYzSet(temp);
    }else if(temp.KddType == 16){
        return getXhsSet(temp);
    }

}
function getPwdzWdSet(temp){
    return new Promise( (resolve, reject) =>{
        const api = comp.Print.Data.platform === 'erp' ? 'get_wd_set_pwdz_erp' : 'get_wd_set_pwdz';
        get(api , {
            params:{
                modeListShowId: temp.Mode_ListShowId,
                exId: temp.Exid,
            },
        },
        ).then( json =>{
            if(json.result == 100 && json.data){
                resolve(json.data);
            }else{
                const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
                const title_wdSet = `[${(temp.ExcodeName || '')}]尚未设置网点信息`;
                if(json.errorMessage=== '拼外地址未设置！'){
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                    reject(json.message);
                }else{
                    Tatami.showFail('模板网点地址信息获取错误：' + (json.errorMessage|| ''));
                    reject('模板网点地址信息获取错误：' + (json.errorMessage|| ''));
                }
            }
        }).catch(err=>{
            console.log('获取电子面单账户失败：' + (err.message || ''));
            reject('获取电子面单账户失败：' + (err.message || ''))
        });
    });
}

function getPddWdSet(temp) {
    return new Promise( (resolve, reject) =>{
        let appKey = '';
        try{
            appKey = JSON.parse(sessionStorage.getItem('com.userInfo')).sellers.appKey;
        } catch(e) {
            console.log(e);
        }
        get('get_wd_set_pdd', {
            params:{
                templateId: temp.Mode_ListShowId,
                pdd_control_type: 'pdd_std_mall_senderinfo',
                client_id: 'a1332323d5b54277956d908616edd182',
                mall_id: appKey,
            },
        }).then(json =>{
            if(json.result == 100 && json.data){
                resolve(json.data);
            }else{
                const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
                const title_wdSet = `[${(temp.ExcodeName || '')}]尚未设置网点信息`;
                if(json.errorMessage=== '拼多多电子面单地址未设置！'){
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                }else{
                    Tatami.showFail('模板网点地址信息获取错误：' + (json.errorMessage|| ''));
                }
            }
        }).catch(err=>{
            console.log('获取电子面单账户失败：' + (err.message || ''));
            reject(err);
        });
    });
}

function getCnWdSet(temp){
    return new Promise( (resolve,reject) =>{
        const api = comp.Print.Data.platform === 'erp' ? 'get_wd_set_erp_cn' : 'get_defaultAddress_cn';
        get(api , {
            params:{
                modeListShowId: temp.Mode_ListShowId,
                exId: temp.Exid,
                modeType:1,
            },
        },
        ).then( json =>{
            const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
            const title_wdSet = `[${(temp.ExcodeName || '')}]尚未设置网点信息`;
            if(json.result == 100 || json.result == 102){
                if(!json || json.result == 102){
                    const title = json.result == 102 ? '' : title_wdSet;

                    const htmlStr = (json.result == 102 ? `您使用的对方店铺代码已失效，无法获取电子面单号，请确认后再试。<br>` : ``) + htmlStr_wdSet;

                    _infoSetCNbranch(title, htmlStr, temp.Mode_ListShowId);
                    reject(htmlStr)
                }else{
                    resolve(json.data);
                }
            }else{

                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));

                if(json.errorMessage=== '淘外菜鸟地址未设置！'){
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                    reject(json.message)
                }else{
                    Tatami.showFail('模板网点地址信息获取错误：' + (json.errorMessage|| ''));
                    reject('模板网点地址信息获取错误：' + (json.errorMessage|| ''))
                }

            }

        }).catch(err=>{
            reject('获取电子面单账户失败：' + (err.message || ''))
            console.log('获取电子面单账户失败：' + (err.message || ''));
        });

    });
}

function getDYSet(temp){
    return new Promise( (resolve) =>{
        let modeType = comp.Print.Data.platform ==='erp' ? 3 : 1
        get('get_defaultAddress_dy' , {
            params:{
                modeListShowId: temp.Mode_ListShowId,
                exId: temp.Exid,
                modeType
            },
        },
        ).then( json =>{
            const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
            const title_wdSet = `[${(temp.ExcodeName || '')}]尚未设置网点信息`;
            if(json.result == 100 || json.result == 102){
                if(!json || json.result == 102){
                    const title = json.result == 102 ? '' : title_wdSet;

                    const htmlStr = (json.result == 102 ? `您使用的对方店铺代码已失效，无法获取电子面单号，请确认后再试。<br>` : ``) + htmlStr_wdSet;

                    _infoSetCNbranch(title, htmlStr, temp.Mode_ListShowId);
                    reject(htmlStr)
                }else{
                    resolve(json.data);
                }
            }else{
                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));

                if(json.errorMessage=== '地址未设置！'){
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                    reject('模板网点地址信息获取错误：' + (json.errorMessage|| ''))
                }else{
                    Tatami.showFail('模板网点地址信息获取错误：' + (json.errorMessage|| ''));
                    reject(json.message)
                }
            }

        }).catch(err=>{
            console.log('获取电子面单账户失败：' + (err.message || ''));
            reject('获取电子面单账户失败：' + (err.message || ''))
        });

    });
}
// 抖外
function getDYOutSet(temp){
    return new Promise( (resolve) =>{
        get('get_defaultAddress_dy_out' , {
            params:{
                modeListShowId: temp.Mode_ListShowId,
                exId: temp.Exid,
                modeType:1,
            },
        },
        ).then( json =>{
            const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
            const title_wdSet = `[${(temp.ExcodeName || '')}]尚未设置网点信息`;
            if(json.result == 100 || json.result == 102){
                if(!json || json.result == 102){
                    const title = json.result == 102 ? '' : title_wdSet;

                    const htmlStr = (json.result == 102 ? `您使用的对方店铺代码已失效，无法获取电子面单号，请确认后再试。<br>` : ``) + htmlStr_wdSet;

                    _infoSetCNbranch(title, htmlStr, temp.Mode_ListShowId);
                }else{
                    resolve(json.data);
                }
            }else{
                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));

                if(json.errorMessage=== '地址未设置！'){
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                }else{
                    Tatami.showFail('模板网点地址信息获取错误：' + (json.errorMessage|| ''));
                }
            }

        }).catch(err=>{
            console.log('获取电子面单账户失败：' + (err.message || ''));
        });

    });
}
function getKsSet(temp) {
    return new Promise((resolve, reject) => {
        let api,
            modeType
        if(comp.Print.Data.platform === 'erp') {
            api = 'get_wd_set_erp_ks'
            modeType = 3
        }else{
            api = 'get_defaultAddress_ks'
            modeType = 1
        }
        get(api, {
            params: {
                modeListShowId: temp.Mode_ListShowId,
                exId: temp.Exid,
                modeType
            },
        })
        .then(json => {
            const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
            const title_wdSet = `[${temp.ExcodeName || ''}]尚未设置网点信息`;
            if (json.result == 100 || json.result == 102) {
                if (!json || json.result == 102) {
                    const title = json.result == 102 ? '' : title_wdSet;

                    const htmlStr =
              (json.result == 102
                  ? `您使用的对方店铺代码已失效，无法获取电子面单号，请确认后再试。<br>`
                  : ``) + htmlStr_wdSet;

                    _infoSetCNbranch(title, htmlStr, temp.Mode_ListShowId);
                    reject(htmlStr)
                } else {
                    resolve(json.data);
                }
            } else {
                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));

                if (json.errorMessage=== '地址未设置！') {
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                } else {
                    Tatami.showFail(
                        '模板网点地址信息获取错误：' + (json.errorMessage|| ''),
                    );
                }
                reject('获取电子面单账户失败：' + (json.errorMessage || ''))
            }
        })
        .catch(err => {
            console.log('获取电子面单账户失败：' + (err.message || ''));
            reject('获取电子面单账户失败：' + (err.message || ''))
        });

    });
}

// 小红书
function getXhsSet(temp) {
    return new Promise((resolve, reject) => {
        let api,
            modeType
        if(comp.Print.Data.platform === 'erp') {
            api = 'get_xhs_default_address'
            modeType = 3
        }else{
            api = 'get_defaultAddress_ks'
            modeType = 1
        }
        get(api, {
            params: {
                modeListShowId: temp.Mode_ListShowId,
                exId: temp.Exid,
                modeType
            },
        })
        .then(json => {
            const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
            const title_wdSet = `[${temp.ExcodeName || ''}]尚未设置网点信息`;
            if (json.result == 100 || json.result == 102) {
                if (!json || json.result == 102) {
                    const title = json.result == 102 ? '' : title_wdSet;

                    const htmlStr =
              (json.result == 102
                  ? `您使用的对方店铺代码已失效，无法获取电子面单号，请确认后再试。<br>`
                  : ``) + htmlStr_wdSet;

                    _infoSetCNbranch(title, htmlStr, temp.Mode_ListShowId);
                    reject(htmlStr)
                } else {
                    resolve(json.data);
                }
            } else {
                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));

                if (json.errorMessage=== '地址未设置！') {
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                } else {
                    Tatami.showFail(
                        '模板网点地址信息获取错误：' + (json.errorMessage || ''),
                    );
                }
                reject('获取电子面单账户失败：' + (json.errorMessage || ''))
            }
        })
        .catch(err => {
            console.log('获取电子面单账户失败：' + (err.message || ''));
            reject('获取电子面单账户失败：' + (err.message || json.errorMessage || ''))
        });

    });
}
// 小红书
function getSphSet(temp) {
    return new Promise((resolve, reject) => {
        let api,
            modeType
        if(comp.Print.Data.platform === 'erp') {
            api = 'get_wdInfoDetail_erp_sph'
            modeType = 3
        }else{
            api = 'get_defaultAddress_ks'
            modeType = 1
        }
        get(api, {
            params: {
                modeListShowId: temp.Mode_ListShowId,
                exId: temp.Exid,
                modeType
            },
        })
        .then(json => {
            const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
            const title_wdSet = `[${temp.ExcodeName || ''}]尚未设置网点信息`;
            if (json.result == 100 || json.result == 102) {
                if (!json || json.result == 102) {
                    const title = json.result == 102 ? '' : title_wdSet;

                    const htmlStr =
              (json.result == 102
                  ? `您使用的对方店铺代码已失效，无法获取电子面单号，请确认后再试。<br>`
                  : ``) + htmlStr_wdSet;

                    _infoSetCNbranch(title, htmlStr, temp.Mode_ListShowId);
                    reject(htmlStr)
                } else {
                    resolve(json.data);
                }
            } else {
                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));

                if (json.errorMessage=== '地址未设置！') {
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                } else {
                    Tatami.showFail(
                        '模板网点地址信息获取错误：' + (json.errorMessage|| ''),
                    );
                }
                reject('获取电子面单账户失败：' + (json.errorMessage || ''))
            }
        })
        .catch(err => {
            console.log('获取电子面单账户失败：' + (err.message || ''));
            reject('获取电子面单账户失败：' + (err.message || ''))
        });

    });
}// 有赞
function getYzSet(temp) {
    return new Promise((resolve, reject) => {
        let api,
            modeType
            api = 'get_yz_default_address'
            modeType = 3
        get(api, {
            params: {
                modeListShowId: temp.Mode_ListShowId,
                exId: temp.Exid,
                modeType
            },
        })
        .then(json => {
            const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
            const title_wdSet = `[${temp.ExcodeName || ''}]尚未设置网点信息`;
            if (json.result == 100 || json.result == 102) {
                if (!json || json.result == 102) {
                    const title = json.result == 102 ? '' : title_wdSet;

                    const htmlStr =
              (json.result == 102
                  ? `您使用的对方店铺代码已失效，无法获取电子面单号，请确认后再试。<br>`
                  : ``) + htmlStr_wdSet;

                    _infoSetCNbranch(title, htmlStr, temp.Mode_ListShowId);
                    reject(htmlStr)
                } else {
                    resolve(json.data);
                }
            } else {
                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));

                if (json.errorMessage=== '地址未设置！') {
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                } else {
                    Tatami.showFail(
                        '模板网点地址信息获取错误：' + (json.errorMessage|| ''),
                    );
                }
                reject('获取电子面单账户失败：' + (json.errorMessage || ''))
            }
        })
        .catch(err => {
            console.log('获取电子面单账户失败：' + (err.message || ''));
            reject('获取电子面单账户失败：' + (err.message || ''))
        });

    });
}
// 美团
function getTHHSet(temp){
    console.log('获取美团网点设置====');
    return new Promise( (resolve) =>{
        get('get_mtDefaultAddress_thh' , {
            params:{
                modeListShowId: temp.Mode_ListShowId,
                exId: temp.Exid,
                modeType:1,
            },
        },
        ).then( json =>{
            const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
            const title_wdSet = `[${(temp.ExcodeName || '')}]尚未设置网点信息`;
            if(json.result == 100 || json.result == 102){
                if(!json || json.result == 102){
                    const title = json.result == 102 ? '' : title_wdSet;
                    const htmlStr = (json.result == 102 ? `您使用的对方店铺代码已失效，无法获取电子面单号，请确认后再试。<br>` : ``) + htmlStr_wdSet;

                    _infoSetCNbranch(title, htmlStr, temp.Mode_ListShowId);
                }else{
                    resolve(json.data);
                }
            }else{
                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));

                if(json.errorMessage=== '地址未设置！'){
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                }else{
                    Tatami.showFail('模板网点地址信息获取错误：' + (json.errorMessage|| ''));
                }
            }

        }).catch(err=>{
            console.log('获取电子面单账户失败：' + (err.message || ''));
        });

    });
}
//京东
function getJDSet(temp){
    console.log('获取美团网点设置====');
    return new Promise( (resolve) =>{
        get('get_wd_set_erp_jd' , {
            params:{
                templateId: temp.Mode_ListShowId,
            },
        },
        ).then( json =>{
            const htmlStr_wdSet = `办法：请打开 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单设置</a> 更换网点地址`;
            const title_wdSet = `[${(temp.ExcodeName || '')}]尚未设置网点信息`;
            if(json.result == 100 || json.result == 102){
                if(!json || json.result == 102){
                    const title = json.result == 102 ? '' : title_wdSet;
                    const htmlStr = (json.result == 102 ? `您使用的对方店铺代码已失效，无法获取电子面单号，请确认后再试。<br>` : ``) + htmlStr_wdSet;

                    _infoSetCNbranch(title, htmlStr, temp.Mode_ListShowId);
                }else{
                    resolve(json.data);
                }
            }else{
                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));

                if(json.errorMessage=== '地址未设置！'){
                    _infoSetCNbranch(title_wdSet, htmlStr_wdSet, temp.Mode_ListShowId);
                }else{
                    Tatami.showFail('模板网点地址信息获取错误：' + (json.errorMessage|| ''));
                }
            }

        }).catch(err=>{
            console.log('获取电子面单账户失败：' + (err.message || ''));
        });

    });
}
function getWdSet(temp){
    return new Promise( (resolve) =>{
        get('get_branchSetting' , {
            params:{
                templateId: temp.Mode_ListShowId,
                exId: temp.Exid,
                modeType:1,
            },
        },
        ).then( json =>{
            if (json.result == 100) {
                if(!json){
                    const htmlStr = `请填写 <a style='color:#0000ff;text-decoration: none;' href='javascript:void(0);' data-act-name='kddTempSet'> 快递单模板</a> 上的电子面单账户名及密码`;
                    _infoSetCNbranch('', htmlStr, temp.Mode_ListShowId);
                }else{
                    resolve(json.data);
                }
            }else{
                console.log('获取电子面单账户失败：' + (json.errorMessage || ''));
            }
        }).catch(err=>{
            console.log('获取电子面单账户失败：' + (err.message || ''));
        });
    });
}

function _infoSetCNbranch(title,content,tempId){
    model({
        title:title,
        content:content,
        type:'confirm',
        width:500,
        shown:function(modelDom){
            modelDom.addEventListener('click',function(e){
                const target = e.target;
                if(target.dataset.actName === 'kddTempSet'){
                    showTemplateMain({
                        printType:'kdd',
                        tempId,
                    });
                    model.remove(modelDom);
                }
            });
        },
    });
}

function _gatherParamBranch(kddList){
    // 【内心独白】看着这代码👋很痒 一定要改造他
    // const exIds = [];
    // const excodes = [];
    // const modeListShowIds = [];
    // const pddModeListShowIds = [];
    // const dyModeListShowIds = [];
    // // 快手剩余单号获取传参
    // const ksModeListShowIds = [];

    // kddList.forEach(item => {
    //     if (item.KddType == 3 ) {
    //         exIds.push(item.Exid);
    //         excodes.push(item.ExCode);
    //         modeListShowIds.push(item.Mode_ListShowId);
    //     } else if (item.KddType == 7){
    //         pddModeListShowIds.push(item.Mode_ListShowId);
    //     } else if (item.KddType == 8) {
    //         dyModeListShowIds.push(item.Mode_ListShowId);
    //     } else if (item.KddType == 9) {
    //         ksModeListShowIds.push(item.Mode_ListShowId);
    //     }
    // });

    // return {
    //     exIds: exIds.join(','),
    //     excodes: excodes.join(','),
    //     modeListShowIds: modeListShowIds.join(','),
    //     pddModeListShowIds: pddModeListShowIds.join(','),
    //     dyModeListShowIds: dyModeListShowIds.join(','),
    //     ksModeListShowIds: ksModeListShowIds.join(','),
    // };

    // 菜鸟面单(kddType == 3)拼多多面单 kddType == 7抖音面单 kddType == 8 快手面单 kddType == 9
    const CN_TYPE = 3, PDD_TYPE = 7, DY_TYPE = 8, KS_TYPE = 9;
    // 数组转字符串方法
    const arrayToStr = (arr = []) => Array.isArray(arr) && arr.join(',');
    const outputParams = {
        [CN_TYPE]: {
            exIds: [],
            excodes: [],
            modeListShowIds: [],
        },
        [PDD_TYPE]: [],
        [DY_TYPE]: [],
        [KS_TYPE]: [],
    };

    kddList.forEach(item => {
        const { KddType, Exid, ExCode, Mode_ListShowId: ModeListShowId } = item || {};
        if ( KddType === CN_TYPE) {
            outputParams[CN_TYPE].exIds.push(Exid);
            outputParams[CN_TYPE].excodes.push(ExCode);
            outputParams[CN_TYPE].modeListShowIds.push(ModeListShowId);
        } else {
            outputParams[KddType]?.push(ModeListShowId);
        }
    });

    const { exIds, excodes, modeListShowIds } = outputParams[CN_TYPE] || {};

    return {
        exIds: arrayToStr(exIds),
        excodes: arrayToStr(excodes),
        modeListShowIds: arrayToStr(modeListShowIds),
        pddModeListShowIds: arrayToStr(outputParams[PDD_TYPE]),
        dyModeListShowIds: arrayToStr(outputParams[DY_TYPE]),
        ksModeListShowIds: arrayToStr(outputParams[KS_TYPE]),
    };
}


/**
 *
 * @param {Array}  kddList 快递单模板列表
 * @param {Boolean} wdUseCache 是否用缓存
 * @returns {Record<string, any>}
 */
async function batchGetBranchSet(arg){
    let {
        kddList,
        wdUseCache,
        updateExid,  // 需要更新的exCode
		cancelKey
    } = arg;
    const isMerge = window.erpData?.advancedSetting?.groupPrintSetJsonString.openMergePrint == 2
    if (!Array.isArray(kddList)){
        throw new Error(`batchGetBranchSet 方法需要传入一个数组`);
    }

    // 如果有缓存且不是必须调用接口
    const cachedBranchList = comp.Print.Data.branchList;
    if (cachedBranchList && JSON.stringify(cachedBranchList) != '{}' && wdUseCache){
        return cachedBranchList;
    }

    const isPartUpdate = cachedBranchList && updateExid;
    if (isPartUpdate){
        kddList = kddList.filter(item => {
            return  isMerge ? item.exId == updateExid : item.Exid === updateExid;
        });
    }

    let params = null;



    if(comp.Print.Data.platform === 'erp'){
        if(isMerge) {
            const platObj = {
                3:'tb',
                7:'pdd',
                8:'fxg',
                9:'ksxd',
                5:'jd',
                13:'xhs',
                14:'sph',
                16:'xhs',
            }
            params = kddList.map((i)=>{
                return {'exId':i.exId,'exCode':i.exCode,'modeListShowId':i.userTemplateId,'platform':platObj[i.expressType],kddType: i.expressType};
            });
        }else{
            params = kddList.map((i)=>{
                return {'exId':i.Exid,'exCode':i.ExCode,'modeListShowId':i.Mode_ListShowId,'platform':i.platform,kddType: i.KddType};
            });
        }

        params = {jsonParam:params};
    }else{
        params = _gatherParamBranch(kddList);
            // 如果所有参数都为空，则参数为空
        const isEmpty = Object.keys(params).filter(key => params[key]).length === 0;

        if (isEmpty) return cachedBranchList || {};

    }

    const api = comp.Print.Data.platform === 'erp' ? 'batch_get_waybill_branch_erp' : 'batch_get_waybill_branch';

    const json = await post(api,{
        params,
        isHideLoading:true,
		headers: {       // 添加请求头参数
			cancelKey   // 传递取消标识
		}
    });
    params.pddModeListShowIds?.split(',')?.forEach((v,i)=>{ //清空干扰数据
        if (cachedBranchList) {
            cachedBranchList[v] = {};
        }
    });
    // 合并数据
    const combinedBranchList = Object.assign(cachedBranchList || {}, json.data);

    // 缓存
    comp.Print.Data.branchList = combinedBranchList;

    return combinedBranchList;
}

/**
 * @desc 获取网点剩余单号
 * @param {*} kddList 快递单列表
 */
async function batchGetWdBranchSet(kddList) {
    // 获取网点模板的账号
    const wdKddList = kddList.filter(item => configWdHasElecNum(item.KddType, item.ExCode));
    const promiseList = wdKddList.map(item => getYunBranchSet(item));
    // 获取到账号后去获取网点剩余单号信息
    const wdAccountInfos = await Promise.all(promiseList);

    const wdBalanceList = wdKddList.map((item, index) => ({
        exCode: item.ExCode,
        modelistshowId: item.Mode_ListShowId,
        customerCode: wdAccountInfos[index].Username,
        password: wdAccountInfos[index].Password,
    }));
    const { data } = await post('get_balance', {
        params: {
            wdBalanceList,
        },
        isHideLoading: true,
    });
    return data;
}


export {
    getYunBranchSet,
    batchGetBranchSet,
    batchGetWdBranchSet,
};
