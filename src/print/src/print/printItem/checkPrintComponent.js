/**
 *
 * @param {Object} temp 模板的简略信息
 * 判断是否安装了模板对应的打印组件，并进行提示
 */

import model from '../../common/model.ts';
import * as printItem from './index';
import NewYunDaPrinter from '../../resources/comp/comp.print.newYunDaPrint';
import { raw } from '../../utils';
import JDLprint from '../../resources/comp/comp.print.jdlprint';
import kdzsPrint from '../../resources/comp/comp.print.kdzsPrint';
import { isDYEncryptTpl } from '../utils/tpl';
import { get } from '../../common/http';

// 控件下载帮助页面
const ComponentDownUrl = {
    'pdd':'http://meta.pinduoduo.com/api/one/app/v1/lateststable?appId=com.xunmeng.pddprint&platform=windows&subType=main',
    'cainiao':'https://support-cnkuaidi.taobao.com/doc.htm#?docId=108934&docType=1',
    'lodop_cainiao':'http://static.kuaidizs.cn/notInstallCaiNiao/cainiao_down2.html',
    'clodop':'http://static.kuaidizs.cn/notInstallCaiNiao/clodop_down.html',
    'c_lodopfor84':'//static.kuaidizs.cn/notInstallCaiNiao/cainiao_downFor84Chrome.html',  //为chrome升级到84的下载帮助页
    'jdl': 'https://static.kuaidizs.cn/noInstallCloud/down/JDLPrintSetup.zip', //京东云控件
    // 'dy': '//static.kuaidizs.cn/noInstallCloud/down/DYPrintSetup.zip', // 抖音控件
    'dy': 'https://logistics.douyinec.com/davinci/index', // 抖音控件
    'meituan':'https://portal-portm.meituan.com/klfe/mtob/prod/downloadConfig?version=2.0.6' , // 美团控件
    'pdd_c_lodopfor84':'//static.kuaidizs.cn/noInstallClodop/CLodop_Setup_for_Win32NT-forChrom84.zip',
    'pdd_lodop_cainiao':'//static.kuaidizs.cn/noInstallClodop/CLodop_Setup_for_Win32NT_https_3.090Extend.zip',
    'ks':'https://s1-11586.kwimgs.com/kos/nlav11586/kuaishou-print-installer.exe' , // 快手控件
    'kdzs' : 'https://cdn-erp-qd.superboss.cc/print_control/ExpressHelper_Setup.exe',
    'xiaohongshu':'https://xhswaybill-printer-1251524319.cos.ap-shanghai.myqcloud.com/XHPrintTool/prod/win/xiaohongshu-win-86-1.4.8.XH.exe',
    'xiaohongshu_new':'https://xhswaybill-printer-1251524319.cos.ap-shanghai.myqcloud.com/XHSPrintClient/prod/windows/xhs-electron-printer_x64-setup.exe',
    'shipinhao' : 'https://res.wx.qq.com/shop/print/ChannelsShopPrintClient-setup.exe',
    'dewu' : 'https://h5static.dewu.com/print-app/client/win/%E5%BE%97%E7%89%A9%E6%89%93%E5%8D%B0%20Setup.exe'
};
// 控件最低安装版本号
const InstallMinVersion = {
    'pdd':'1.2.51',
    'cainiao':'*******',
    'kuaishou': '1.2.4',
};
// 控件exe文件下载链接
const ExeDownUrl = {
    'pdd': 'http://meta.pinduoduo.com/api/one/app/v1/lateststable?appId=com.xunmeng.pddprint&platform=windows&subType=main',
    'cainiao':'https://cdn-cloudprint.cainiao.com/waybill-print/client/CNPrintSetup.exe',
    // TODO 1.2.4版本以上支持快手分页
    'kuaishou':'https://s1-11586.kwimgs.com/kos/nlav11586/kuaishou-print-installer.exe',
};



/**
 *
 * @param {*} temp 电子模版信息
 * @param {*} printType
 * @return {object} type: cainiao/lodop/clodop/pdd ;isSuccess: true/false
 */
async function checkPrintComponent(temp,printType,isLoading = true){
    let checkPrintComponentResult = {}; // 存放控件检查的结果
    const componentType = checkUseComponentType(temp,printType);
    if(componentType === 'mustUseLodop'){ // 必须使用lodop
        checkPrintComponentResult = checkLodopProgress();
    } else if(componentType === 'mustUseNewYunDa') {
        checkPrintComponentResult = await checkNewYunDaProgress();
    } else if (componentType === 'mustUseDy') {
        checkPrintComponentResult = await checkDYProgress(isLoading);
    } else if(componentType === 'mustUseJDL') {
        checkPrintComponentResult = await checkJDLProgress();
    }  else if(componentType === 'mustUseKs'){ // 快手
        checkPrintComponentResult = await checkKsProgress(isLoading);
        if(checkPrintComponentResult.isSuccess){
            await checkComponentVersion(checkPrintComponentResult.type);
        }
    } else if(componentType === 'mustUseThh'){ // 美团
        checkPrintComponentResult = await checkTHHProgress(isLoading);
    }else if(componentType === 'mustUseXhs'){
        checkPrintComponentResult = await checkXhsProgress(isLoading);
    }else if(componentType === 'mustUseSph'){
        checkPrintComponentResult = await checkSphProgress(isLoading);
    }else if(componentType === 'mustUseNewXhs'){
        checkPrintComponentResult = await checkNewXhsProgress(isLoading);
	}else if(componentType === 'mustUseDw'){
        checkPrintComponentResult = await checkDwsProgress(isLoading);
	}else{
        if(/^(mustUseCainiao|cainiao_lodop)$/.test(componentType)){
            checkPrintComponentResult = await checkCaiNiaoProgress(componentType);
        }else if( componentType === 'mustUsePdd'){ //必须使用拼多多控件
            checkPrintComponentResult = await checkPddProgress(isLoading);
        }
        console.log('point12', checkPrintComponentResult);
        // 控件版本校验
        if(checkPrintComponentResult.isSuccess){
            await checkComponentVersion(checkPrintComponentResult.type);
        }
    }
    return checkPrintComponentResult || {};

}
/**
 *
 * @param {*} temp 电子模版信息
 * @param {*} printType
 * @return {object} type: cainiao/lodop/clodop/pdd ;isSuccess: true/false
 */
 async function checkPrintComponentByMerge(printType,isLoading = true){

    let checkPrintComponentResult = {}; // 存放控件检查的结果
     if (printType === 8) {
        checkPrintComponentResult = await checkDYProgress(isLoading);
    } else if(printType === 5) {
        checkPrintComponentResult = await checkJDLProgress();
    }else if(printType === 9){ // 快手
        checkPrintComponentResult = await checkKsProgress(isLoading);
    }else if(printType === 13){ // 快手
        checkPrintComponentResult = await checkXhsProgress(isLoading);
    }else if(printType === 14){ // 快手
        checkPrintComponentResult = await checkSphProgress(isLoading);
    }else if(printType === 16){ // 新版小红书
        checkPrintComponentResult = await checkNewXhsProgress(isLoading);
    }else if(printType === 17){
        checkPrintComponentResult = await checkDwsProgress(isLoading);
	}else{
        if(printType === 3 ||  printType === 15){
            checkPrintComponentResult = await checkCaiNiaoProgress('mustUseCainiao');
        }else if( printType === 7){ //必须使用拼多多控件
            checkPrintComponentResult = await checkPddProgress(isLoading);
        }
        // 控件版本校验
        if(checkPrintComponentResult.isSuccess){
            await checkComponentVersion(checkPrintComponentResult.type);
        }
    }
    return checkPrintComponentResult || {};

}
async function checkPrintComponentByKdzs({ isShow = true, checkVersion } = {}) {

    let checkPrintComponentResult = {}; // 存放控件检查的结果
	checkPrintComponentResult = checkKdzsProgress({ isShow, checkVersion })
    return checkPrintComponentResult || {};

}
async function checkKdzsConnetStatus(data){
    return new Promise(resolve =>{
        comp.Print.checkKdzsConnetStatus({data},arg => {
            console.log(arg);
            if(arg?.length){
                let result =  kdzsConnetTipsHtml(arg)
                resolve(result)
            }else{
                resolve({
                    isSuccess:true
                })
            }

        });
    });
}
async function kdzsConnetTipsHtml(data){
    return new Promise( ( resolve) =>{
        let result;
        const keyByName = {
            cainiao:'菜鸟控件',
            jd:'京东控件',
            pdd:'拼多多控件',
            doudian:'抖店控件',
            kuaishou:'快手控件',
            xhs:'小红书控件',
            shph:'视频号控件',
            xhs2:'小红书（新版）控件'
        }


        let contentHTML = data.map(o=>{
            return `<p style="font-size:13px;margin:0;">${keyByName[o]}未连接</p>`
        }).join('');
		contentHTML += `<p style="font-size:13px;margin:0;">请确认<span style="color:red;">【快递助手控件】</span>中平台控件安装路径是否正确后重启控件，或<a style="color:rgb(0, 157, 255);" href="${ComponentDownUrl['kdzs']}">下载最新版本</a>后控件重新安装。</p>`
        result = {
            isSuccess:false,
        };
        const modelConfig = {
            type:'confirm',
            title:'控件未连接',
            content:contentHTML,
            cancelHide:true,
            width:480,
            minHeight:180,
            okCb:() => {
                resolve(result);
            },
        };
        model(modelConfig);
    });
}
/**
 * 检查 快递助手融合控件
 * @return 返回一个 boolean true 表示检查通过， false 表示检查未通过
 */
async function checkKdzsProgress({ isShow, checkVersion }) {
    const arg = await checkKdzsPrint();

    console.log('------arg',arg);

    let result = {
        type:'kdzs',
        isSuccess:true,
    };
    if(!arg.isSupport && !isShow) return false
    if(!arg.isSupport){
        if( arg.status == 4 ){ //尝试重新连接
            const reConnectResult = await compKdzsDoConnect(true);
            if (!reConnectResult.isSupport) {
                result = await checkKdzsFailStep(arg.status);
            }
        }else if(arg.status == 2){
            result = await checkKdzsFailStep(arg.status);
        }else{ // websocket 连接失败
            result = await checkKdzsFailStep(arg.status);
        }
    }
    // 校验一下版本
	 if (comp.Print.Data.newPrintMode || checkVersion) {
		//先判断有没有正常获取到版本号,如果当前版本号不存在，则重新获取
		if(!comp.Print.Data?.kdzsPrintVersion){
		 	await checkKdzsVersion()
		}
        let kdzsVersion = comp.Print.Data?.kdzsPrintVersion || '0.0.0'
		if(comp.Print.Data.isNewXhsPrint){
			let isNewContorl = window?.tplHelp?.compareVer(kdzsVersion,'1.2.14.01')
        	if(!isNewContorl) result =  await checkKdzsFailStep(arg.status , '当前版本过低，请更新至最新版本的快递助手控件')
		} else if (checkVersion) {
			let isNewContorl = window?.tplHelp?.compareVer(kdzsVersion, checkVersion)
			if (!isNewContorl) result = await checkKdzsFailStep(arg.status, '当前版本过低，请更新至最新版本的快递助手控件')
		}else{
			let isNewContorl = window?.tplHelp?.compareVer(kdzsVersion,'1.2.12')
			comp.Print.Data.newPrintMode = !!isNewContorl
		}

    }
    return result;
    function checkKdzsPrint(){
        return new Promise(resolve =>{
            comp.Print.checkKdzsPrint(arg => {
                resolve(arg);
            });
        });
    }
    function checkKdzsVersion(){
        return new Promise(resolve =>{
            kdzsPrint.getKdzsVersion(arg => {
                resolve(arg);
            });
        });
    }
    function compKdzsDoConnect(isLoading = false){
        return new Promise(resolve =>{
            kdzsPrint.doConnect(()=>{
                resolve(kdzsPrint.checkKdzsPrintStatus);
            },null,isLoading);
        });
    }

    async function checkKdzsFailStep(status,title){
        return new Promise( ( resolve) =>{
            let result;
            const donwUrl = ComponentDownUrl['kdzs'];
            const contentHTML = printItem.template.kdzsFailStepHtml(status,donwUrl);
            // const footerHtml = printItem.template.pddFailFooterHtml(status,donwUrl);
            result = {
                type:'kdzs',
                isSuccess:false,
                downUrl:donwUrl,
            };
            const modelConfig = {
                type:'confirm',
                title:title || '您未安装最新版本的快递助手控件',
                content:contentHTML,
                customFooter: '',
                width:620,
                minHeight:220,
                okNotClose:true,
                shown:(modelDom) =>{

                },
                cancelCb:() => {
                    resolve(result);
                },
            };
            if(status == 2){
                modelConfig.width = 460;
                modelConfig.hiddenFooter = true;
            }
            if(title){
                modelConfig.hiddenFooter = true;
            }
            model(modelConfig);
        });
    }
}

/**
 * 检查 京东云控件
 * @return 返回一个 boolean true 表示检查通过， false 表示检查未通过
 */
async function checkJDLProgress(isNotShowInfo){
    const arg = await checkJDLPrint();

    console.log('------arg',arg);

    let result = {
		type: 'jdl',
        isSuccess:true,
    };

    if(!arg.isSupport){
        if( arg.status == 4 ){ //尝试重新连接
            const reConnectResult = await compJDLDoConnect(true);
            if (!reConnectResult.isSupport) {
                result = await checkJDLFailStep(arg.status);
            }
        }else if(arg.status == 2){
            result = await checkJDLFailStep(arg.status);
        }else{ // websocket 连接失败
            result = await checkJDLFailStep(arg.status);
        }
    }
    return result;
    function checkJDLPrint(){
        return new Promise(resolve =>{
            comp.Print.checkJDLPrint(arg => {
                resolve(arg);
            });
        });
    }

    function compJDLDoConnect(isLoading = false){
        return new Promise(resolve =>{
            JDLprint.doConnect(()=>{
                resolve(JDLprint.checkJdlPrintStatus);
            },null,isLoading);
        });
    }

    async function checkJDLFailStep(status){
        return new Promise( ( resolve) =>{
            let result;
            const donwUrl = ComponentDownUrl['jdl'];
            const contentHTML = printItem.template.checkFailStepHtml({
                platform:'京东',
                controlName:'京东官方打印控件'
            });
            const footerHtml = printItem.template.checkFailFooterHtml({
                url:donwUrl
            });
            result = {
                type:'jdl',
                isSuccess:false,
                downUrl:donwUrl,
            };
            const modelConfig = {
                type:'confirm',
                title:'打印该模板需京东打印控件',
                content:contentHTML,
                customFooter: footerHtml,
                width:620,
                minHeight:180,
                okNotClose:true,
                shown:(modelDom) =>{
                    modelDom.addEventListener('click',async function(e){
                        const target = e.target;
                        // 重新和控件建立连接
                        if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                            target.disabled = true;

                            const connectInfoDom = modelDom.querySelector('.errorInfo');
                            connectInfoDom.textContent = '连接中……';
                            connectInfoDom.style.display = 'block';

                            const connected = await checkJDLPrint();

                            target.disabled = false;
                            if (!connected?.isSupport){
                                connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                            } else {
                                model.remove(modelDom);
                                resolve({
                                    type:'jdl',
                                    isSuccess:true,
                                });
                            }
                        }
                    });                },
                cancelCb:() => {
                    resolve(result);
                },
            };
            if(status == 2){
                modelConfig.width = 460;
                modelConfig.hiddenFooter = true;
            }
            model(modelConfig);
        });
    }
}


/**
 * 检查 美团控件
 * @return 返回一个 boolean true 表示检查通过， false 表示检查未通过
 */
async function checkTHHProgress(isLoading){
    console.log('--check_mt_device---');
    //arg.status:  1： 浏览器不支持，2：未安装，3：ie浏览器设置问题SecurityError  4: 检测控件关闭
    const arg = await checkTHHPrint();
    function checkTHHPrint(){
        return new Promise(resolve =>{
            comp.Print.checkTHHPrint( arg =>{
                resolve(arg);
            });
        });
    }

    function ThhDoConnect(isLoading = false){
        return new Promise(resolve =>{
            comp.print.thhprint.doConnect(()=>{
                resolve(comp.Print.Data.checkedThhPrintStatus);
            },null,isLoading);
        });
    }
    console.info(arg);
    let result = {
        type:'meituan',
        isSuccess:true,
    };

    if(!arg?.isSupport){ // arg 有时候取不到
        if( arg?.status == 4 ){ // 尝试重新连接
            const reConnectResult = await ThhDoConnect(true);
            if (!reConnectResult?.isSupport) {
                result = await checkThhFailStep(arg?.status);
            }
        }else if(arg?.status){
            result = await checkThhFailStep(arg?.status);
        }else{ // socket 连接失败
            result = await checkThhFailStep(arg?.status);
        }
    }


    return result;

    async function checkThhFailStep(status){
        return new Promise( ( resolve) =>{
            let result;
            const donwUrl = ComponentDownUrl['meituan'];

            const contentHTML = printItem.template.thhFailStepHtml();
            const footerHtml = printItem.template.thhFailFooterHtml(status,donwUrl);

            result = {
                type:'meituan',
                isSuccess:false,
                downUrl:donwUrl,
            };
            const modelConfig = {
                type:'confirm',
                title:'打印该模板需美团打印控件',
                content:contentHTML,
                customFooter: footerHtml,
                width:630,
                minHeight:180,
                okNotClose:true,
                shown:(modelDom) =>{
                    modelDom.addEventListener('click',async function(e){
                        const target = e.target;
                        // 重新和控件建立连接
                        if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                            target.disabled = true;

                            const connectInfoDom = modelDom.querySelector('.errorInfo');
                            connectInfoDom.textContent = '连接中……';
                            connectInfoDom.style.display = 'block';

                            const connected = await checkTHHPrint();
                            console.info(connected);
                            target.disabled = false;
                            if (!connected?.isSupport){
                                connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                            } else {
                                model.remove(modelDom);
                                resolve({
                                    type:'meituan',
                                    isSuccess:true,
                                });
                            }
                        }
                    });

                },
                cancelCb:() => {
                    resolve(result);
                },
            };
            if(status == 2){
                modelConfig.width = 460;
                modelConfig.hiddenFooter = true;
            }
            model(modelConfig);
        });
    }
}

/**
 * 检查 美团控件
 * @return 返回一个 boolean true 表示检查通过， false 表示检查未通过
 */
async function checkXhsProgress(isLoading){
    console.log('--check_mt_device---');
    //arg.status:  1： 浏览器不支持，2：未安装，3：ie浏览器设置问题SecurityError  4: 检测控件关闭
    const arg = await checkXhsPrint();
    function checkXhsPrint(){
        return new Promise(resolve =>{
            comp.Print.checkXhsPrint( arg =>{
                resolve(arg);
            });
        });
    }

    function XhsDoConnect(isLoading = false){
        return new Promise(resolve =>{
            comp.print.xhsprint.doConnect(()=>{
                resolve(comp.Print.Data.checkXhsPrintStatus);
            },null,isLoading);
        });
    }
    console.info(arg);
    let result = {
        type:'xhs',
        isSuccess:true,
    };

    if(!arg?.isSupport){ // arg 有时候取不到
        if( arg?.status == 4 ){ // 尝试重新连接
            const reConnectResult = await XhsDoConnect(true);
            if (!reConnectResult?.isSupport) {
                result = await checkXhsFailStep(arg?.status);
            }
        }else if(arg?.status){
            result = await checkXhsFailStep(arg?.status);
        }else{ // socket 连接失败
            result = await checkXhsFailStep(arg?.status);
        }
    }


    return result;

    async function checkXhsFailStep(status){
        return new Promise( ( resolve) =>{
            let result;
            const donwUrl = ComponentDownUrl['xiaohongshu'];

            const contentHTML = printItem.template.xhsFailStepHtml();
            const footerHtml = printItem.template.xhsFailFooterHtml(status,donwUrl);

            result = {
                type:'xhs',
                isSuccess:false,
                downUrl:donwUrl,
            };
            const modelConfig = {
                type:'confirm',
                title:'打印该模板需小红书打印控件',
                content:contentHTML,
                customFooter: footerHtml,
                width:630,
                minHeight:180,
                okNotClose:true,
                shown:(modelDom) =>{
                    modelDom.addEventListener('click',async function(e){
                        const target = e.target;
                        // 重新和控件建立连接
                        if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                            target.disabled = true;

                            const connectInfoDom = modelDom.querySelector('.errorInfo');
                            connectInfoDom.textContent = '连接中……';
                            connectInfoDom.style.display = 'block';

                            const connected = await checkXhsPrint();

                            console.info(connected);
                            target.disabled = false;
                            if (!connected?.isSupport){
                                connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                            } else {
                                model.remove(modelDom);
                                resolve({
                                    type:'meituan',
                                    isSuccess:true,
                                });
                            }
                        }
                    });

                },
                cancelCb:() => {
                    resolve(result);
                },
            };
            if(status == 2){
                modelConfig.width = 460;
                modelConfig.hiddenFooter = true;
            }
            model(modelConfig);
        });
    }
}
// 新版小红书控件
async function checkNewXhsProgress(isLoading){
    console.log('--check_mt_device---');
    //arg.status:  1： 浏览器不支持，2：未安装，3：ie浏览器设置问题SecurityError  4: 检测控件关闭
    const arg = await checkXhsPrint();
    function checkXhsPrint(){
        return new Promise(resolve =>{
            comp.Print.checkNewXhsPrint( arg =>{
                resolve(arg);
            });
        });
    }

    function XhsDoConnect(isLoading = false){
        return new Promise(resolve =>{
            comp.print.newXhsPrint.doConnect(()=>{
                resolve(comp.Print.Data.checkNewXhsPrintStatus);
            },null,isLoading);
        });
    }
    console.info(arg);
    let result = {
        type:'xhs',
        isSuccess:true,
    };

    if(!arg?.isSupport){ // arg 有时候取不到
        if( arg?.status == 4 ){ // 尝试重新连接
            const reConnectResult = await XhsDoConnect(true);
            if (!reConnectResult?.isSupport) {
                result = await checkXhsFailStep(arg?.status);
            }
        }else if(arg?.status){
            result = await checkXhsFailStep(arg?.status);
        }else{ // socket 连接失败
            result = await checkXhsFailStep(arg?.status);
        }
    }


    return result;

    async function checkXhsFailStep(status){
        return new Promise( ( resolve) =>{
            let result;
            const donwUrl = ComponentDownUrl['xiaohongshu_new'];

            const contentHTML = printItem.template.xhsFailStepHtml({
				name:'小红书新版打印控件',
			});
            const footerHtml = printItem.template.xhsFailFooterHtml(status,donwUrl);

            result = {
                type:'xhs',
                isSuccess:false,
                downUrl:donwUrl,
            };
            const modelConfig = {
                type:'confirm',
                title:'打印该模板需小红书打印控件',
                content:contentHTML,
                customFooter: footerHtml,
                width:630,
                minHeight:180,
                okNotClose:true,
                shown:(modelDom) =>{
                    modelDom.addEventListener('click',async function(e){
                        const target = e.target;
                        // 重新和控件建立连接
                        if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                            target.disabled = true;

                            const connectInfoDom = modelDom.querySelector('.errorInfo');
                            connectInfoDom.textContent = '连接中……';
                            connectInfoDom.style.display = 'block';

                            const connected = await checkXhsPrint();

                            console.info(connected);
                            target.disabled = false;
                            if (!connected?.isSupport){
                                connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                            } else {
                                model.remove(modelDom);
                                resolve({
                                    type:'meituan',
                                    isSuccess:true,
                                });
                            }
                        }
                    });

                },
                cancelCb:() => {
                    resolve(result);
                },
            };
            if(status == 2){
                modelConfig.width = 460;
                modelConfig.hiddenFooter = true;
            }
            model(modelConfig);
        });
    }
}
// 得物控件
async function checkDwsProgress(isLoading){
    console.log('--check_mt_device---');
    //arg.status:  1： 浏览器不支持，2：未安装，3：ie浏览器设置问题SecurityError  4: 检测控件关闭
    const arg = await checkDwPrint();
    function checkDwPrint(){
        return new Promise(resolve =>{
            comp.Print.checkDwPrint( arg =>{
                resolve(arg);
            });
        });
    }

    function dwDoConnect(isLoading = false){
        return new Promise(resolve =>{
            comp.print.dwPrint.doConnect(()=>{
                resolve(comp.Print.Data.checkDwPrintStatus);
            },null,isLoading);
        });
    }
    console.info(arg);
    let result = {
        type:'xhs',
        isSuccess:true,
    };

    if(!arg?.isSupport){ // arg 有时候取不到
        if( arg?.status == 4 ){ // 尝试重新连接
            const reConnectResult = await dwDoConnect(true);
            if (!reConnectResult?.isSupport) {
                result = await checkDwFailStep(arg?.status);
            }
        }else if(arg?.status){
            result = await checkDwFailStep(arg?.status);
        }else{ // socket 连接失败
            result = await checkDwFailStep(arg?.status);
        }
    }


    return result;

    async function checkDwFailStep(status){
        return new Promise( ( resolve) =>{
            let result;
            const donwUrl = ComponentDownUrl['dewu'];

            const contentHTML = printItem.template.dwFailStepHtml({
				name:'得物打印控件',
			});
            const footerHtml = printItem.template.dwFailFooterHtml(status,donwUrl);

            result = {
                type:'dw',
                isSuccess:false,
                downUrl:donwUrl,
            };
            const modelConfig = {
                type:'confirm',
                title:'打印该模板需得物打印控件',
                content:contentHTML,
                customFooter: footerHtml,
                width:630,
                minHeight:180,
                okNotClose:true,
                shown:(modelDom) =>{
                    modelDom.addEventListener('click',async function(e){
                        const target = e.target;
                        // 重新和控件建立连接
                        if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                            target.disabled = true;

                            const connectInfoDom = modelDom.querySelector('.errorInfo');
                            connectInfoDom.textContent = '连接中……';
                            connectInfoDom.style.display = 'block';

                            const connected = await checkDwPrint();

                            console.info(connected);
                            target.disabled = false;
                            if (!connected?.isSupport){
                                connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                            } else {
                                model.remove(modelDom);
                                resolve({
                                    type:'meituan',
                                    isSuccess:true,
                                });
                            }
                        }
                    });

                },
                cancelCb:() => {
                    resolve(result);
                },
            };
            if(status == 2){
                modelConfig.width = 460;
                modelConfig.hiddenFooter = true;
            }
            model(modelConfig);
        });
    }
}
async function checkSphProgress(isLoading){
    // console.log('--check_mt_device---');
    // //arg.status:  1： 浏览器不支持，2：未安装，3：ie浏览器设置问题SecurityError  4: 检测控件关闭
    // const arg = await checkXhsPrint();
    // function checkXhsPrint(){
    //     return new Promise(resolve =>{
    //         comp.Print.checkXhsPrint( arg =>{
    //             resolve(arg);
    //         });
    //     });
    // }

    // function XhsDoConnect(isLoading = false){
    //     return new Promise(resolve =>{
    //         comp.print.xhsprint.doConnect(()=>{
    //             resolve(comp.Print.Data.checkXhsPrintStatus);
    //         },null,isLoading);
    //     });
    // }
    // console.info(arg);
    // let result = {
    //     type:'xhs',
    //     isSuccess:true,
    // };

    // if(!arg?.isSupport){ // arg 有时候取不到
    //     if( arg?.status == 4 ){ // 尝试重新连接
    //         const reConnectResult = await XhsDoConnect(true);
    //         if (!reConnectResult?.isSupport) {
    //             result = await checkXhsFailStep(arg?.status);
    //         }
    //     }else if(arg?.status){
    //         result = await checkXhsFailStep(arg?.status);
    //     }else{ // socket 连接失败
    //         result = await checkXhsFailStep(arg?.status);
    //     }
    // }


    // return result;

    async function checkXhsFailStep(status){
        return new Promise( ( resolve) =>{
            let result;
            const donwUrl = ComponentDownUrl['shipinhao'];

            const contentHTML = printItem.template.sphFailStepHtml();
            const footerHtml = printItem.template.sphFailFooterHtml(status,donwUrl);

            result = {
                type:'sph',
                isSuccess:false,
                downUrl:donwUrl,
            };
            const modelConfig = {
                type:'confirm',
                title:'打印该模板需视频号打印控件',
                content:contentHTML,
                customFooter: footerHtml,
                width:630,
                minHeight:180,
                okNotClose:true,
                shown:(modelDom) =>{
                    modelDom.addEventListener('click',async function(e){
                        const target = e.target;
                        // 重新和控件建立连接
                        if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                            target.disabled = true;

                            const connectInfoDom = modelDom.querySelector('.errorInfo');
                            connectInfoDom.textContent = '连接中……';
                            connectInfoDom.style.display = 'block';

                            const connected = await comp.print.sphprint.doConnect();
                            console.info(connected);
                            target.disabled = false;
                            if (!connected?.isSupport){
                                connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                            } else {
                                model.remove(modelDom);
                                resolve({
                                    type:'sph',
                                    isSuccess:true,
                                });
                            }
                        }
                    });

                },
                cancelCb:() => {
                    resolve(result);
                },
            };
            if(status == 2){
                modelConfig.width = 460;
                modelConfig.hiddenFooter = true;
            }
            model(modelConfig);
        });
    }

    const clientInstance = comp.print.sphprint;
    if(!clientInstance) return {isSuccess: false};

    const stausObj = await clientInstance.check();
    let result = {
        type: 14,
        isSuccess: stausObj.available,
    };
    if(!stausObj.available){
        result = await checkXhsFailStep(stausObj?.status);
    }
    return result;
}
/**
 * 检查 lodop
 * @return 返回一个 boolean true 表示检查通过， false 表示检查未通过
 */
function checkLodopProgress(isNotShowInfo){
    const lodop = comp.Print.checkCainiao() || sessionStorage.getItem('mac') ? {} : undefined;
    const isUseNewBrowser = comp.print.lodop.getChrome();  //检测chrome版本
    // [临时方案]pdd下掉菜鸟链接
    const isPdd = window.platformConfig.platform === 'pdd';

    if(!lodop){ // 如果没有检查到 lodop
        isNotShowInfo ? '' : _noInstallLodopDialog(isUseNewBrowser) ;
        return {
            type:'clodop',
            isSuccess:false,
            downUrl: isPdd ? ComponentDownUrl[isUseNewBrowser ? 'pdd_c_lodopfor84' : 'pdd_lodop_cainiao'] : ComponentDownUrl[isUseNewBrowser ? 'c_lodopfor84' : 'lodop_cainiao'],
        };
    }else{ // 如果检查到了
        if(!lodop.CVERSION){ // 安装的 lodop 不是 clodop
            //显示提示框，升级 clodop
            // 如果不愿意升级，也可以，也返回 true （lodop 也能打印对应的内容，但是我们要建议用户用 clodop）
            return {
                type:'lodop',
                isSuccess:true,
                downUrl: ComponentDownUrl['clodop'],
            };
        }else {
            return {
                type:'clodop',
                isSuccess:true,
            };
        }

    }
}

function checkNewYunDaProgress() {
    return new Promise(resolve => {
        NewYunDaPrinter.initNewYundaPrinter({
            failCb: (params) => {
                const { webapp_urlprotocol_startup, _grwebapp_url } = params;  //  未安装控件或者未启动程序

                model({
                    type: 'confirm',
                    title: '提示',
                    content: '创建WebSocket失败，可能是WEB报表客户端程序没有启动或本机没有安装客户端程序',
                    width: 426,
                    height: 200,
                    okName: '启动',
                    cancelName: '下载',
                    okCb: function () {
                        webapp_urlprotocol_startup();  //启动
                    },
                    cancelCb: function () {
                        window.open(_grwebapp_url);  //下载
                    },
                });
                resolve({
                    isSuccess: false,
                });
            },
            printBeginCb: () => {
                console.log('打印开始');
            },
            printEndCb: () => {
                console.log('打印完成');
            },
            getPrintersCb: ({ printers }) => {
                comp.Print.Data.newYunDaPrinters = printers;
                console.log('加载打印机列表', printers);
                resolve({
                    isSuccess: true,
                    type: 'new_yunda',
                    printers: printers.map(item => item.name),
                });
            },
        });
    });

}

/**
 * 检查 快手控件
 * @return 返回一个 boolean true 表示检查通过， false 表示检查未通过
 */
async function checkKsProgress(isLoading){
    console.log('--检查 快手控件---');
    //arg.status:  1： 浏览器不支持，2：未安装，3：ie浏览器设置问题SecurityError  4: 检测控件关闭
    const arg = await checkKsPrint();
    console.info(arg);
    let result = {
        type:'kuaishou',
        isSuccess:true,
    };

    if(!arg.isSupport){
        if( arg.status == 4 ){ // 尝试重新连接
            const reConnectResult = await KsDoConnect(true);
            if (!reConnectResult.isSupport) {
                result = await checkKsFailStep (arg.status);
            }
        }else if(arg.status){
            result = await checkKsFailStep(arg.status);
        }else{ // socket 连接失败
            result = await checkKsFailStep(arg.status);
        }
    }

    return result;

    function checkKsPrint(){
        return new Promise(resolve =>{
            comp.Print.checkKsPrint(arg => {
                resolve(arg);
            });
        });
    }

    function KsDoConnect(isLoading = false){
        return new Promise(resolve =>{
            comp.print.ksprint.doConnect(()=>{
                resolve(comp.Print.Data.checkedKsPrintStatus);
            },null,isLoading);
        });
    }

    async function checkKsFailStep(status){
        return new Promise( ( resolve) =>{
            let result;
            const donwUrl = ComponentDownUrl['ks'];
            const contentHTML = printItem.template.checkFailStepHtml({
                platform:'快手',
                controlName:'快手官方打印控件'
            });
            const footerHtml = printItem.template.checkFailFooterHtml({
                url:donwUrl
            });

            result = {
                type:'kuaishou',
                isSuccess:false,
                downUrl:donwUrl,
            };
            const modelConfig = {
                type:'confirm',
                title:'打印该模板需快手打印控件',
                content:contentHTML,
                customFooter: footerHtml,
                width:620,
                minHeight:180,
                okNotClose:true,
                shown:(modelDom) =>{
                    modelDom.addEventListener('click',async function(e){
                        const target = e.target;
                        // 重新和控件建立连接
                        if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                            target.disabled = true;

                            const connectInfoDom = modelDom.querySelector('.errorInfo');
                            connectInfoDom.textContent = '连接中……';
                            connectInfoDom.style.display = 'block';

                            const connected = await checkKsPrint();

                            target.disabled = false;
                            if (!connected?.isSupport){
                                connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                            } else {
                                model.remove(modelDom);
                                resolve({
                                    type:'kuaishou',
                                    isSuccess:true,
                                });
                            }
                        }
                    });
                },
                cancelCb:() => {
                    resolve(result);
                },
            };
            if(status == 2 || status == 4){
                modelConfig.width = 460;
                modelConfig.hiddenFooter = true;
            }
            model(modelConfig);
        });
    }
}

function _noInstallLodopDialog(isUseNewBrowser){
    // [临时方案]pdd下掉菜鸟链接
    const isPdd = window.platformConfig.platform === 'pdd';
    const downUrl = isPdd ? ComponentDownUrl[isUseNewBrowser ? 'pdd_c_lodopfor84' : 'pdd_lodop_cainiao'] : ComponentDownUrl[isUseNewBrowser ? 'c_lodopfor84' : 'lodop_cainiao'];
    model({
        width:450,
        height:190,
        content:`
                <h3 class="warn-title"><span class="icon-warn-orange"></span>您未安装最新的c-lodop打印控件</h3>
                <p class="warn-content">安装完成请<a class=" reflash_view" href="##">刷新页面</a>或重启浏览器</p>
                <div  class="btn-ground">
                    <a href="${downUrl}" target="_blank" class="pbtn pbtn-default btn-lg download-now-btn">立即下载</a>
                    <button class="pbtn btn-normal btn-lg close-btn">取消</button>
                </div>
        `,
        contentClassName: 'noLodopDialog_content',
        shown:function(modelDom){
            modelDom.addEventListener('click', (e)=>{
                const classList = e.target.classList;

                // 关闭
                if(classList.contains('close-btn') ) {
                    model.remove(modelDom);
                }

                // 刷新
                if(classList.contains('reflash_view')){
                    e.preventDefault();
                    location.reload();
                }

            });

        },
    });
}

/**
 *
 * @param {string} componentType  使用的控件类型
 */
async function checkCaiNiaoProgress(componentType){
    const isMustUseCaiNiao = componentType == 'mustUseCainiao' ;

    //arg.status:  1： 浏览器不支持，2：未安装，3：ie浏览器设置问题SecurityError  4: 检测控件关闭
    const arg = await compCheckCloudPrint();
    let result = {
        type:'cainiao',
        isSuccess:true,
    };

    if(!arg.isSupport){
        if( arg.status == 4 ){ //尝试重新连接
            const reConnectResult = await compDoConnect(true);
            if (!reConnectResult.isSupport) {
                result = await cainiaoFailStep(isMustUseCaiNiao , arg.status);
            }
        }else if(arg.status){
            result = await cainiaoFailStep(isMustUseCaiNiao,arg.status);
        }else{ // websocket 连接失败
            result = await cainiaoFailStep(isMustUseCaiNiao,arg.status);
        }
    }
    // eslint-disable-next-line require-atomic-updates
    if( result.isSuccess && result.type === 'cainiao'){
        comp.Print.Data.modeType = 1;
    }
    return result;

    function compDoConnect(isLoading = false){
        return new Promise(resolve =>{
            comp.print.cloudprint.doConnect(()=>{
                resolve(comp.Print.Data.checkCloudPrintStatus);
            },null,isLoading);
        });
    }

    function compCheckCloudPrint(){
        return new Promise(resolve =>{
            comp.Print.checkCloudPrint( arg =>{
                resolve(arg);
            });
        });
    }

    /**
     * 菜鸟控件检查失败后的行为，如果不是必须用菜鸟，则再去测试 lodop 能不能用
     * @param {boolean} isMustUseCaiNiao 是否必须使用菜鸟控件
     */
    async function cainiaoFailStep(isMustUseCaiNiao, status){
        return new Promise( ( resolve) =>{
            let result;
            if(isMustUseCaiNiao){
                // 如果检测菜鸟控件是未安装状态 => 并且lodop也未安装  =>  提示下载lodop && cainiao 两个安装包
                // const noInstallCaiNiao = (status == 2 || status == 3);
                // if(noInstallCaiNiao){
                //     result = checkLodopProgress();
                //     if(!result.isSuccess){
                //         resolve(result);
                //         return;
                //     }
                // }

                // 如果菜鸟控件是断开状态 或者 lodop已安装 => 提示下载cainiao安装包
                // const contentHTML = printItem.template.cainiaoFailStepHtml(status);
                // const footerHtml = printItem.template.cainiaoFailFooterHtml(status);
                const contentHTML = printItem.template.checkFailStepHtml({
                    platform:'菜鸟',
                    controlName:'菜鸟官方打印控件'
                });
                const footerHtml = printItem.template.checkFailFooterHtml({
                    url:ComponentDownUrl['cainiao']
                });
                result = {
                    type:'cainiao',
                    isSuccess:false,
                    downUrl:ComponentDownUrl['cainiao'],
                };
                model({
                    type:'confirm',
                    title:'打印该模板需菜鸟打印控件',
                    content:contentHTML,
                    customFooter: footerHtml,
                    width:460,
                    minHeight:180,
                    okNotClose:true,
                    shown:(modelDom) =>{
                        modelDom.addEventListener('click',async function(e){
                            const target = e.target;
                            // 重新和控件建立连接
                            if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                                target.disabled = true;

                                const connectInfoDom = modelDom.querySelector('.errorInfo');
                                connectInfoDom.textContent = '连接中……';
                                connectInfoDom.style.display = 'block';

                                const result = await compDoConnect(false);

                                target.disabled = false;
                                if( !result.isSupport ){
                                    connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                                }else{
                                    model.remove(modelDom);
                                    resolve({
                                        type:'cainiao',
                                        isSuccess:true,
                                    });
                                }
                            }
                        });
                    },
                    cancelCb:() => {
                        resolve(result);
                    },
                });
            }else{
                result = checkLodopProgress();
                resolve(result);
            }
        });
    }
}

/**
 * 只有拼多多快递模板使用 pdd 控件
 */
async function checkPddProgress(isLoading){
    console.log('point5', isLoading);
    const isPdd = ['pdd'].includes(comp.Print.Data.platform)
    if(isPdd){
        //获取拼多多下载链接
        const resultData = await get('get_pdd_downloadurl', { params: {} });
        const pddDownloadUrl = resultData?.data?.pddDownloadUrl;
        comp.print.data.pddDownloadUrl = pddDownloadUrl;
        //更新顶部控件下载帮助页面、控件exe文件下载链接中pdd的映射值
        ComponentDownUrl.pdd = pddDownloadUrl;
        ExeDownUrl.pdd = pddDownloadUrl;
    }
    //arg.status:  1： 浏览器不支持，2：未安装，3：ie浏览器设置问题SecurityError  4: 检测控件关闭
    const arg = await compPddCheck();
    let result = {
        type:'pdd',
        isSuccess:true,
    };

    console.log('point6', arg);
    if(!arg?.isSupport){
        if( arg.status == 4 ){ //尝试重新连接
            const reConnectResult = await compPddDoConnect(true);
            if (!reConnectResult?.isSupport) {
                result = await checkPddFailStep(arg.status);
            }
        }else if(arg.status == 2){
            result = await checkPddFailStep(arg.status);
        }else{ // websocket 连接失败
            result = await checkPddFailStep(arg.status);
        }
    }
    return result;

    function compPddDoConnect(isLoading = false){
        return new Promise(resolve =>{
            comp.print.pddcloudprint.doConnect(()=>{
                resolve(comp.Print.Data.checkPddPrintStatus);
            },null,isLoading);
        });
    }

    function compPddCheck(){
        return new Promise(resolve =>{
            comp.Print.checkPddCloudPrint(arg => {
                resolve(arg);
            },isLoading);
        });
    }

    async function checkPddFailStep(status){
        return new Promise( ( resolve) =>{
            console.log('point11');
            let result;
            const donwUrl = ComponentDownUrl['pdd'];
            // const contentHTML = printItem.template.pddFailStepHtml(status,donwUrl);
            // const footerHtml = printItem.template.pddFailFooterHtml(status,donwUrl);
            const contentHTML = printItem.template.checkFailStepHtml({
                platform:'拼多多',
                controlName:'拼多多官方打印控件'
            });
            const footerHtml = printItem.template.checkFailFooterHtml({
                url:donwUrl
            });
            result = {
                type:'pdd',
                isSuccess:false,
                downUrl:donwUrl,
            };
            const modelConfig = {
                type:'confirm',
                title:'打印该模板需拼多多打印控件',
                content:contentHTML,
                customFooter: footerHtml,
                width:620,
                minHeight:220,
                okNotClose:true,
                shown:(modelDom) =>{
                    modelDom.addEventListener('click',async function(e){
                        const target = e.target;
                        // 重新和控件建立连接
                        if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                            target.disabled = true;

                            const connectInfoDom = modelDom.querySelector('.errorInfo');
                            connectInfoDom.textContent = '连接中……';
                            connectInfoDom.style.display = 'block';

                            const result = await compPddCheck();

                            target.disabled = false;
                            if( !result.isSupport ){
                                connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                            }else{
                                model.remove(modelDom);
                                resolve({
                                    type:'cainiao',
                                    isSuccess:true,
                                });
                            }
                        }else if(target.dataset.actName == 'closeModel'){
                            resolve(result);
                        }
                    });
                },
                cancelCb:() => {
                    resolve(result);
                },
            };
            if(status == 2){
                modelConfig.width = 460;
                modelConfig.hiddenFooter = true;
            }
            model(modelConfig);
        });
    }
}

async function checkDYProgress(isLoading) {
    let result = {
        type: 'douyin',
        isSuccess: false,
        downUrl: ComponentDownUrl['dy'],
    };

    function checkConnected() {
        return new Promise((resolve) => {
            comp.print.douyin.doConnect((status) => {
                resolve(comp.Print.Data.checkDyCloudPrintStatus);
            }, null , isLoading);
        });
    }

    result = await checkConnected();

    // MC M1芯片无法使用抖音控件解开下面代码，跳过抖音控件简称，查看参数
    // result.isSuccess = true || result.isSupport;
    // mac是留的后门，直接免校验看数据
    result.isSuccess = sessionStorage.getItem('mac') ||  result.isSupport;
    result.type = 'douyin';

    return new Promise((resolve) => {
        if (!result.isSuccess) {
        // 如果抖音控件是断开状态 或者 lodop已安装 => 提示下载抖音安装包
            const contentHTML = printItem.template.checkFailStepHtml({
                platform:'抖音',
                controlName:'抖音官方打印控件'
            });
            const footerHtml = printItem.template.checkFailFooterHtml({
                url:ComponentDownUrl.dy
            });

            model({
                type:'confirm',
                title:'打印该模板需抖音打印控件',
                content:contentHTML,
                customFooter: footerHtml,
                width:460,
                minHeight:180,
                okNotClose:true,
                shown:(modelDom) =>{
                    modelDom.addEventListener('click',async function(e){
                        const target = e.target;
                        // 重新和控件建立连接
                        if(target.dataset.actName == 'reConnectCloud' && !target.disabled ){
                            target.disabled = true;

                            const connectInfoDom = modelDom.querySelector('.errorInfo');
                            connectInfoDom.textContent = '连接中……';
                            connectInfoDom.style.display = 'block';

                            const connected = await checkConnected();

                            target.disabled = false;
                            if (!connected?.isSupport){
                                connectInfoDom.textContent = '连接出错，请检测控件是否安装并开启，或刷新重试';
                            } else {
                                model.remove(modelDom);
                                resolve({
                                    type:'douyin',
                                    isSuccess:true,
                                });
                            }
                        }
                    });
                },
                cancelCb:() => {
                    resolve(result);
                },
            });
        } else {
            resolve(result);
        }
    });
}




/**
 *
 * @param {Object} temp
 * @param {*} printType
 * @return {string} 使用控件类型:
 * mustUseLodop : 必须使用lodop控件
 * mustUseCainiao: 必须使用 cainiao控件
 * mustUsePdd: 必须使用 pdd 控件
 * cainiao_lodop： 优先用cainiao控件，无cainiao控件使用lodop控件
 */
function checkUseComponentType(temp,printType){
    console.log(1000000);
    console.info(temp);
    let useComponent;
    // 抖音控件kdd=8,并且返回custom自定义相关的指
    if ((printType === 'kdd' && temp.KddType == 8 &&  isDYEncryptTpl(temp.KddType,temp.customTop)) || comp.base.isSFCloudPrint(temp.Exid)) {
        useComponent = 'mustUseDy';
    } else if (printType === 'kdd' && [11027, 11028].includes(temp.Exid)) {
        useComponent = 'mustUseNewYunDa';
    }else if(printType === 'kdd' && temp.KddType == 5){ //京东云一联单  使用京东云控件
        useComponent = 'mustUseJDL';
    } else if(printType != 'kdd'  || temp.ExCode == 'CNTMS'){
        useComponent = 'mustUseLodop';
    }else if(temp.KddType ==  1 || (temp.KddType ==  2 && !comp.base.getTempStyle('wdButUseCN',temp.styleId,temp)) ){ // 五联单，网点面单优先使用菜鸟控件 ,未安装菜鸟控件使用lodop
        useComponent = 'cainiao_lodop';
    }else if(temp.KddType ==  3 || temp.KddType ==  15  || temp.KddType == 8 || comp.base.getTempStyle('wdButUseCN',temp.styleId,temp)){ // 菜鸟面单必须使用菜鸟控件
        useComponent = 'mustUseCainiao';
    }else if(temp.KddType == 7){  // 拼多多面单必须使用拼多多控件
        useComponent = 'mustUsePdd';
    }else if(temp.KddType == 9){  //快手面单必须使用快手控件
        useComponent = 'mustUseKs';
    }else if(temp.KddType == 10){ // 美团面单必须使用美团控件
        useComponent = 'mustUseThh';
    }else if(temp.KddType == 13){
        useComponent = 'mustUseXhs';
    }else if(temp.KddType == 14){
        useComponent = 'mustUseSph';
    }else if(temp.KddType == 16){
        useComponent = 'mustUseNewXhs';
    }else if(temp.KddType == 17){
        useComponent = 'mustUseDw';
    }
    return useComponent;

}


/**
 * 检测控件版本
 * @param {string} type  取值如下，默认是菜鸟控件
 * ‘cainiao’：‘菜鸟控件’
 * ‘pdd’：‘拼多多控件’
 */
async function checkComponentVersion(type = 'cainiao'){
    console.log('point13', type);
    const minVer = InstallMinVersion[type];
    if(type == 'cainiao'){
        return new Promise((resolve) => {
            comp.print.cloudprint.getAgentInfo((version) => {
                if(version && version >= minVer){
                    resolve();
                }else{
                    _updateComponentVesion(type);
                }
            });
        });
    }else if( type == 'pdd' ){
        return new Promise((resolve) => {
            console.log('point14');
            comp.print.pddcloudprint.getAgentInfo((version) => {
                console.log('point15', version, '&', minVer, '&', version && version >= minVer);
                if(version && version >= minVer){
                    resolve();
                }else{
                    _updateComponentVesion(type);
                }
            });
        });
    }else if( type === 'kuaishou' ){
        return new Promise((resolve) => {
            console.log('point14');
            comp.print.ksprint.getAgentInfo((version) => {
                console.log('point15', version, '&', minVer, '&', version && version >= minVer);
                if(version && version >= minVer){
                    resolve();
                }else{
                    _updateComponentVesion(type);
                }
            });
        });
    } else {
        //TODO lodop 控件版本判断
        return ;
    }

}

function _updateComponentVesion(type){
    const url = ExeDownUrl[type];
    const contentHtml = `<p>系统检测到您的打印控件版本低，
        <a href="${url}" download target="_blank" >请升级</a>，升级安装完成后请重新打印。
        </p>`;
    const footerHtml = `<a href="javascript:;" class="button cancel-btn" >取消</a>
                        <a href="${url}" download target="_blank" class="button ok-btn">升级</a>`;
    model({
        width:600,
        height:200,
        type:'confirm',
        content:contentHtml,
        customFooter:footerHtml,
    });
}

export {
    checkLodopProgress,
    checkPrintComponent,
    checkPrintComponentByKdzs,
    checkKdzsConnetStatus,
    checkPrintComponentByMerge
};
