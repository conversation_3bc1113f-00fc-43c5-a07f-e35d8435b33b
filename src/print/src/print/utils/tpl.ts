import { modalGlobalConfig } from 'antd/lib/modal/confirm';
import { Tpl } from '../../typings';
import { KDDTYPE_ENUM } from '../../common';
import { tempConf } from '../printFlow/platformConfig';

export interface IRecyclableOption {
	kddType: number
	exCode: string
	/**
	 * 是否是京东青龙
	 */
	jdQl?: number
}

/**
 * 快递模板 的单号 是否可回收
 */
export function isRecyclable(opt: IRecyclableOption) {
	const { kddType, exCode } = opt;

	if (kddType === 3 || kddType === 7 || kddType === 9 || kddType === 10 || kddType === 13 || kddType == 14 || kddType === 15 || kddType === 16) {
		return true;
	}

	if (kddType === 8) {
		const exclude = ['SF', 'HTKY', 'DBKD'];
		if (!exclude.includes(exCode)) {
			return true;
		}
	}

	if (kddType === 5) {
		if (opt.jdQl === 1) {
			return true;
		} else if (exCode !== 'JD') {
			return true;
		}
	}

	return false;
}

/**
 * 是否是菜鸟电子面单
 * @param kddType
 */
export function isCNTpl(kddType: Tpl.IKddType) {
	return kddType === 3;
}
/**
 * 是否是有赞电子面单
 * @param kddType
 */
export function isYzTpl(kddType: Tpl.IKddType) {
	return Number(kddType) === KDDTYPE_ENUM.YZ_SHEET;
}

/**
 * 是否是抖音电子面单
 * @param kddType
 */
export function isDYTpl(kddType: Tpl.IKddType) {
	return kddType === 8;
}

/**
 * 美团控件的电子面单打印任务需要鉴权
 * @param kddType
 */
export function ThhTplNeedAuth(kddType: Tpl.IKddType) {
	if (kddType == 10) {

		return true;
	}
}

// 抖音加密模板列表 这个条件暂时不需要。通过后端返回的customTop有值就判断不需要解密
/**
 * 是否是快手电子面单
 * @param kddType
 */
export function isKsTpl(kddType: Tpl.IKddType) {
	return kddType === 9;
}
/**
 * 是否是京东电子面单
 * @param kddType
 */
export function isJDLTpl(kddType: Tpl.IKddType) {
	return kddType === 5;
}
/**
 * 是否是拼多多电子面单
 * @param kddType
 */
export function isPDDTpl(kddType: Tpl.IKddType) {
	return kddType === 7;
}

/**
 * 是否是小红书电子面单
 * @param kddType
 */
export function isXHSTpl(kddType: Tpl.IKddType) {
	return kddType === KDDTYPE_ENUM.XHS_SHEET || kddType === KDDTYPE_ENUM.NEWXHS_SHEET;
}
// 抖音加密模板列表
const dyEncryptTplExidList = [
	// SF
	11024,
	// SF
	11084,
	// LB
	11015,
	// LB
	11016,
	// ZTO
	11033,
	// ZTO
	11041,
	// EMS
	11036,
	// EMS
	11045,
	// HTKY
	11032,
	// HTKY
	11042,
	// YTO
	11030,
	// YTO
	11039,
	// YUNDA
	11034,
	// YUNDA
	11043,
	// POSTB
	11035,
	// POSTB
	11044,
	// JD
	11038,
	// STO
	11031,
	// STO
	11040,
	// FENGWANG
	11082,
	// DBKD
	11083,
];

/**
 * 是否是抖音控件支持的加密电子面单
 * @param kddType
 * @param customTop 自定义区域的值
 */
// export function isDYEncryptTpl(kddType: Tpl.IKddType, exid: number) {
//     // todo: 更新模板判断条件

//     return tempConf.enableDyPrinter() && isDYTpl(kddType) && dyEncryptTplExidList.includes(exid);
// }



// 信息脱敏

function desensitizedName(name: any) {
	if (!name) return ''
	let arr = Array.from(name)
	let result = ''
	if (arr.length === 1) {
		return name
	} else {
		// 只保留第一个字符，其余全部替换为星号
		result = arr[0] ? arr[0].toString() : '';
		for (let i = 1; i < arr.length; i++) {
			result += '*'
		}
		return result
	}
}
// function desensitizedPhone(phone) {
//     return phone.replace(/^(?:\d+)(.{4})$/, "*******$1")
// }
function desensitizedPhone(phoneNumber: string | null): string {
	if (!phoneNumber) return ''
	// 假设手机号格式为 1XXXXXXXXXXX，固话格式为 XXXX-XXXXXXXX 或 XXX-XXXXXXX
	// 手机号脱敏：保留前3位和后4位，中间用*替换
	// 固话脱敏：保留区号和最后一位，中间用*替换
	const phoneRegex = /^(?:(\d{3,4})-)?([\d*]{0,7})\d*(\d{4})$|^(\d{11})-(\d+)$/;
	const match = phoneNumber.match(phoneRegex);

	if (match) {
		const [, areaCode, prefix, suffix, mainNumber, extension] = match;
		if (mainNumber) {
			// 虚拟号码格式：主号码只保留后四位，分机号只保留一位
			return `****${mainNumber.slice(-4)}-***${extension.slice(-1)}`;
		} else if (areaCode) {
			// 固话格式 - 只保留区号和最后一位
			return areaCode + '-' + prefix.replace(/\d/g, '*') + suffix.slice(0, 3).replace(/\d/g, '*') + suffix.slice(-1);
		} else {
			// 手机号格式
			return prefix.replace(/\d/g, '*') + suffix;
		}
	}

	// 如果不是手机号或固话格式，则不进行脱敏处理
	return phoneNumber;
}
// function desensitizedAddress(phone) {
//     return '**省**市**区'
// }
// 模板发件人是否脱敏
export function fjrInfoencryp(printData: any, temoInfo: any) {
	const { ModeListShow, ModeTempPrintcfg } = temoInfo
	const { userTemplateCfgConfigJson = {} } = ModeTempPrintcfg
	if (ModeListShow.KddType == 2 && ModeListShow.ExCode == 'JTSD') {
		userTemplateCfgConfigJson.senderNameEncrypted = 1;
		userTemplateCfgConfigJson.senderPhoneEncrypted = 1;
		userTemplateCfgConfigJson.recipientNameEncrypted = 1;
		userTemplateCfgConfigJson.recipientPhoneEncrypted = 1;
	}
	printData = printData.map((item) => {
		if (userTemplateCfgConfigJson?.senderNameEncrypted || userTemplateCfgConfigJson?.senderPhoneEncrypted || userTemplateCfgConfigJson?.senderAddressEncrypted) {
		// 京东跟网点的单独处理下
			if (ModeListShow.KddType == 5 || (ModeListShow.KddType == 2 && ModeListShow.ExCode !== 'SF')) {

				if (userTemplateCfgConfigJson?.senderNameEncrypted) {
					item.f_name = desensitizedName(item.f_name)
				}
				if (userTemplateCfgConfigJson?.senderPhoneEncrypted) {
					item.f_tel = desensitizedPhone(item.f_tel)
				}
				if (userTemplateCfgConfigJson?.senderAddressEncrypted) {
					item.f_addr = '**省**市**区****'
				}
			} else if (item.kddFjr) {
				if (userTemplateCfgConfigJson?.senderNameEncrypted) {
					item.kddFjr.name = desensitizedName(item.kddFjr.name)
				}
				if (userTemplateCfgConfigJson?.senderPhoneEncrypted) {
					item.kddFjr.phone = desensitizedPhone(item.kddFjr.phone)
				}
				if (userTemplateCfgConfigJson?.senderAddressEncrypted) {
					item.kddFjr.city = "**市";
					item.kddFjr.district = "**区";
					item.kddFjr.address = "****";
					item.kddFjr.province = "**省"
				}

			} else {
				let { content } = item
				let senderInfo = content?.addData?.senderInfo || {}
				let sender = content?.addData?.sender || {}
				// 处理两种不同的通用加密
				if (Object.keys(senderInfo).length) {
					if (userTemplateCfgConfigJson?.senderNameEncrypted) {
						senderInfo.contact.name = desensitizedName(senderInfo.contact.name)
					}
					if (userTemplateCfgConfigJson?.senderPhoneEncrypted) {
						senderInfo.contact.mobile = desensitizedPhone(senderInfo.contact.mobile)
					}
					if (userTemplateCfgConfigJson?.senderAddressEncrypted) {
						senderInfo.address.provinceName = '**省';
						senderInfo.address.cityName = '**市';
						senderInfo.address.districtName = '**区';
						senderInfo.address.detailAddress = '****';
					}
				}
				if (Object.keys(sender).length) {
					if (userTemplateCfgConfigJson?.senderNameEncrypted) {
						sender.name = desensitizedName(sender.name)
					}
					if (userTemplateCfgConfigJson?.senderPhoneEncrypted) {
						sender.mobile = desensitizedPhone(sender.mobile)
					}
					if (userTemplateCfgConfigJson?.senderAddressEncrypted) {
						sender.address.province = '**省'
						sender.address.city = '**市'
						sender.address.district = '**区'
						sender.address.detail = '****'
					}
				}
			}
			// 仅加密电话
		}


		if (userTemplateCfgConfigJson?.recipientNameEncrypted) {
			item.s_name = desensitizedName(item.s_name || '')
		}
		if (userTemplateCfgConfigJson?.recipientPhoneEncrypted) {
			item.s_phone = desensitizedPhone(item.s_phone || '')
			item.s_tel = desensitizedPhone(item.s_tel || '')
		}
		if (userTemplateCfgConfigJson?.recipientAddressEncrypted) {
			item.s_p = '**省'
			item.s_city = '**市'
			item.s_q = '**区'
			item.s_addr = '****'
			item.s_addrall = '**省**市**区****'
		}






		return item
	})
	return printData

}
export function isDYEncryptTpl(kddType: Tpl.IKddType, customTop?: {}) {
	return tempConf.enableDyPrinter() && isDYTpl(kddType) && Boolean(customTop);
}

/**
 * 是否是快手控件支持的加密电子面单
 * @param kddType
 */
export function isKsEncryptTpl(kddType: Tpl.IKddType, customTop?: {}) {

	return tempConf.enableKsPrinter() && isKsTpl(kddType);
}

/**
 * 是否是有赞控件支持的加密电子面单
 * @param kddType
 */
export function isYzEncryptTpl(kddType: Tpl.IKddType,) {
	return isYzTpl(kddType);
}

// 是美团电子面单并且是用美团控件打印直接控件解密
export function isTHHEncryptTpl(kddType: Tpl.IKddType, customTop?: {}) {
	return tempConf.enableThhPrinter() && ThhTplNeedAuth(kddType) && Boolean(customTop);
}
// 是京东无界面单（除京东面单外）并且是用京东控件打印直接控件解密
export function isJDLEncryptTpl(kddType: Tpl.IKddType, exid: any) {
	const isJDModel = [11360, 11081, 11397, 11005, 11008, 7100, 997, 998, 999].includes(exid); // 京东自己的面单用lodop打
	return tempConf.enableOPENPrinter() && isJDLTpl(kddType) && !isJDModel;
}

// 是拼多多电子面单并且是用拼多多控件打印直接控件解密
export function isPddEncryptTpl(kddType: Tpl.IKddType, customTop?: {}) {
	return tempConf.enablePDDPrinter() && isPDDTpl(kddType);
}
// 是小红书电子面单并且是用小红书控件打印直接控件解密
export function isXHSEncryptTpl(kddType: Tpl.IKddType, customTop?: {}) {
	return tempConf.enableXHSPrinter() && isXHSTpl(kddType) && Boolean(customTop);
}

// 是菜鸟电子面单并且是用菜鸟控件打印直接控件解密
export function isCNEncryptTpl(kddType: Tpl.IKddType, customTop?: {}) {
	return isCNTpl(kddType);
}

// 视频号电子面单
export function isSPHEncryptTpl(kddType: Tpl.IKddType) {
	return kddType == KDDTYPE_ENUM.SPH_SHEET;
}
