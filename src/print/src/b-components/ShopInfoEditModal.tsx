import React, { useState, useEffect } from 'react';
import { useRequest } from "ahooks";
import ReactDOM from 'react-dom';
import { Modal, Button, Checkbox, Row, Col, Form, Space } from 'antd';
import './theme/shopInfoEdit.less';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { TradePrintSavePrintStyleApi } from "../../../apis/trade/tradeSet";
import tradeSetStore from "../../../stores/trade/tradeSet";
import { message } from 'antd';
interface ShopInfoEditModalProps {
	visible: boolean;
	onCancel: () => void;
	onOk: (data: any) => void;
	initialData?: any;
}

// 定义选项配置（仅用于展示分组）
const optionConfig = {
	'商品信息': [
		{ label: '商品名称', value: 'itemTitle' },
		{ label: '商家编码', value: 'outerId' },
		{ label: '简称', value: 'sysItemAlias' },
	],
	'规格信息': [
		{ label: '规格名称', value: 'skuName' },
		{ label: '规格编码', value: 'skuOuterId' },
		{ label: '规格别名', value: 'sysSkuAlias' },
		{ label: '货品规格编码', value: 'sysOuterSkuId' },
	],
	'数量': [
		{ label: '数量', value: 'num' },
	]
};


// 获取所有选项
const getAllOptions = () => {
	const allOptions: { label: string; value: string }[] = [];
	Object.values(optionConfig).forEach(group => {
		group.forEach(option => {
			allOptions.push(option);
		});
	});
	return allOptions;
};

const ShopInfoEditModal: React.FC<ShopInfoEditModalProps> = (props) => {
	const { visible, onCancel, onOk, initialData = {} } = props;
	const [form] = Form.useForm();

	// 使用单一的选中状态数组
	const [checkedValues, setCheckedValues] = useState<CheckboxValueType[]>([]);
	// 显示顺序状态
	const [displayOrder, setDisplayOrder] = useState<CheckboxValueType[]>([]);

	// 初始化数据
	useEffect(() => {
		if (visible && initialData) {
			// 默认选中值
			const storeTradeSet = tradeSetStore;
			const goodsInfoSorted = storeTradeSet?.printContentSet?.extJson?.goodsInfoSorted || '';
			const defaultChecked = goodsInfoSorted.split(',');
			const initialChecked = initialData.checkedValues || defaultChecked || [];
			const initialOrder = initialData.displayOrder || [...initialChecked];

			setCheckedValues(initialChecked);
			setDisplayOrder(initialOrder);

			form.setFieldsValue({
				...initialData,
				checkedValues: initialChecked,
				displayOrder: initialOrder
			});
		}
	}, [visible, initialData, form]);
	const { run: savePrintStyleSet, loading } = useRequest(TradePrintSavePrintStyleApi, {
		manual: true,
		onSuccess: (res, params) => {
			if (res.success) {
				message.success('保存成功');
			} else {
				message.error('保存发货内容设置失败');
			}
		}
	});
	const handleOk = async () => {
		try {
			const values = await form.validateFields();
			const storeTradeSet = tradeSetStore;
			storeTradeSet.printContentSet.extJson.goodsInfoSorted = checkedValues.join(',');
			savePrintStyleSet({
				...storeTradeSet?.printContentSet,
			})
			onOk({
				...values,
				checkedValues,
				displayOrder
			});
		} catch (error) {
			console.error('表单验证失败:', error);
		}
	};

	// 处理复选框变化
	const handleCheckboxChange = (groupKey: string, groupValues: CheckboxValueType[]) => {
		// 获取该分组所有选项的值
		const groupOptionValues = optionConfig[groupKey as keyof typeof optionConfig].map(opt => opt.value);

		// 保留其他分组的选中状态
		const otherGroupValues = checkedValues.filter(value =>
			!groupOptionValues.includes(value as string)
		);

		// 合并新旧状态
		const newCheckedValues = [...otherGroupValues, ...groupValues];
		setCheckedValues(newCheckedValues);

		// 更新显示顺序
		const remainingOrder = displayOrder.filter(item => newCheckedValues.includes(item));
		const newItems = newCheckedValues.filter(item => !displayOrder.includes(item));
		setDisplayOrder([...remainingOrder, ...newItems]);
	};

	// 从显示顺序中移除项
	const removeFromOrder = (value: CheckboxValueType) => {
		// 从显示顺序中移除
		const newOrder = displayOrder.filter(item => item !== value);
		setDisplayOrder(newOrder);

		// 同步取消上方的勾选
		const newChecked = checkedValues.filter(item => item !== value);
		setCheckedValues(newChecked);
	};

	// 根据值获取标签
	const getLabelByValue = (value: string) => {
		const allOptions = getAllOptions();
		const option = allOptions.find(opt => opt.value === value);
		return option ? option.label : value;
	};

	return (
		<Modal
			title="商品信息编辑"
			visible={visible}
			width={600}
			zIndex={9999}
			onCancel={onCancel}
			footer={[
				<Button key="cancel" onClick={onCancel}>取消</Button>,
				<Button key="submit" type="primary" onClick={handleOk}>确定</Button>
			]}
			destroyOnClose
			className="shop-info-edit-modal"
		>
			<Form
				form={form}
				initialValues={initialData}
			>
				{/* 商品信息 */}
				<Form.Item label="商品信息:" className="info-section">
					<Checkbox.Group
						value={checkedValues}
						onChange={(values) => handleCheckboxChange('商品信息', values)}
					>
						{optionConfig['商品信息'].map(option => (
							<Checkbox key={option.value} value={option.value}>{option.label}</Checkbox>
						))}
					</Checkbox.Group>
				</Form.Item>

				{/* 规格信息 */}
				<Form.Item label="规格信息:" className="info-section">
					<Checkbox.Group
						value={checkedValues}
						onChange={(values) => handleCheckboxChange('规格信息', values)}
					>
						{optionConfig['规格信息'].map(option => (
							<Checkbox key={option.value} value={option.value}>{option.label}</Checkbox>
						))}
					</Checkbox.Group>
				</Form.Item>

				{/* 数量 */}
				<Form.Item label="数量:" className="info-section">
					<Checkbox.Group
						value={checkedValues}
						onChange={(values) => handleCheckboxChange('数量', values)}
					>
						{optionConfig['数量'].map(option => (
							<Checkbox key={option.value} value={option.value}>{option.label}</Checkbox>
						))}
					</Checkbox.Group>
				</Form.Item>

				{/* 显示顺序 */}
				<Form.Item label="显示顺序:" className="display-order-section">
					<div className="order-box">
						{displayOrder.map((value) => (
							<div key={value} className="order-item" onClick={() => removeFromOrder(value)}>
								{getLabelByValue(value as string)}
							</div>
						))}
					</div>
				</Form.Item>
			</Form>
		</Modal>
	);
};

// 创建一个函数用于渲染弹窗
export const showShopInfoEditModal = (config: {
	initialData?: any;
	onOk?: (data: any) => void;
}) => {
	const { initialData, onOk } = config;
	const div = document.createElement('div');
	document.body.appendChild(div);

	const close = () => {
		ReactDOM.unmountComponentAtNode(div);
		div.remove();
	};

	const render = () => {
		ReactDOM.render(
			<ShopInfoEditModal
				visible={true}
				initialData={initialData}
				onCancel={close}
				onOk={(data) => {
					if (onOk) {
						onOk(data);
					}
					close();
				}}
			/>,
			div
		);
	};

	render();

	return {
		close,
	};
};

export default ShopInfoEditModal;
