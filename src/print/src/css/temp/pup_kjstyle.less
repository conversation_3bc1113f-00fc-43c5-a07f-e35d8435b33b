@charset "utf-8";
/*遮罩*/

.pup_fullscreen {
    background-color: #000;
    display: block;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    /*FF IE7*/
    filter: alpha(opacity=45);
    /*IE*/
    filter: progid:DXImageTransform.Microsoft.Alpha(opacity=45);
    opacity: 0.45;
    /*FF*/
    z-index: 1;
    position: fixed !important;
    /*FF IE7*/
    position: absolute;
    /*IE6*/
}

/*IE6*/
*html body {
    margin: 0;
    height: 100%;
}

/*快递单-发货网点设置*/
.pup_express_box {
    width: 870px;
    // border: 4px solid rgba(183, 183, 183, 0.3);
    border-radius: 5px;
    *border: 4px solid #969393;
    border: 4px solid #969393\9;
    position: relative;
    z-index: 3;

    dl {
        margin: 0;
        padding: 0;
    }

    .loading {
        padding: 35px 0;
        color: #aaa;
    }

    input[type=checkbox]:checked::after {
        content: none;
    }

    input,
    select {
        color: black;
    }

    .input_check {
        margin-top: -2px;
        margin-right: 4px;
        vertical-align: middle;
    }

    .search-drop-down {
        position: relative;
        font-size: 12px;
        display: inline-block;
        min-width: 100px;
        width: 100%;
        font-family: 'Microsoft Yahei';

        .down-arrow-icon {
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAGCAYAAADgzO9IAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6Qzg4NzA0RTg0N0UwMTFFNkI1OUY4NjFFMjJCOEVDRkYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6Qzg4NzA0RTk0N0UwMTFFNkI1OUY4NjFFMjJCOEVDRkYiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpDODg3MDRFNjQ3RTAxMUU2QjU5Rjg2MUUyMkI4RUNGRiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpDODg3MDRFNzQ3RTAxMUU2QjU5Rjg2MUUyMkI4RUNGRiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PhWiI5AAAABKSURBVHjaYkyv7vp//uotBmRgqK3GwJQa4ceADkBiTMY66owgFciqgWJKTDAVyKqB4D4LiITq+g9jg2jG///BfIazV26iSAAEGABJSRZCrzBifAAAAABJRU5ErkJggg==) no-repeat 6px 9px;
            width: 16px;
            height: 22px;
            cursor: pointer;
            position: absolute;
            right: 2px;
        }

        .drop-down-choose {
            background: #fff;
            height: 21px;
            border: 1px solid #bbbbbb;
            line-height: 21px;
            text-align: left;
            font-weight: normal;
            text-indent: 4px;
            cursor: pointer;
        }

        .drop-down-search {
            text-indent: 4px;
            line-height: 23px;
            height: 23px;
            width: 99%;
            border: 1px solid #bbbbbb;
            border-top: 0px;
        }

        .search-icon {
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAARVBMVEUAAACZmZmZmZmampqampqenp6ampqenp6bm5uampqbm5uampq3t7eZmZmampqZmZmZmZmZmZmampqampqcnJyZmZmZmZmXe3fsAAAAF3RSTlMA5N3BtxJZHKmHQAsD1Z50a1FKMyXKmInC4fIAAACBSURBVBjTVY5ZDsQgDENJQtnKUpbO/Y86AZpK9YdNXiwRxbKeMLmmRAbhzhrhdzwzUOc4Pei4+onOvbngmuGhq62oca7oZpNKYMf8ggCGPekX1NVwGAV4sOvX8swDd9lhXWk1tP0gyHWEggDEt+4bp6hpIcqaYnpUhxDRJE59iRt/9LwDf7K4F5cAAAAASUVORK5CYII=) left top no-repeat;
            width: 23px;
            height: 23px;
            display: inline-block;
            border: none;
            position: absolute;
            right: 0px;
            top: 3px;
        }

        .drop-down-select {
            position: absolute;
            margin: 0px;
            z-index: 1000;
            background: white;
            border: 1px solid #d0c7c7;
            max-height: 300px;
            overflow-y: auto;
            overflow-x: hidden;
            width: 99%;
            cursor: default;

            li {
                max-width: 200px;
                padding: 2px 0 2px 4px;

                &:hover {
                    background-color: #1e90ff;
                    color: white;
                }

                &.active {
                    background-color: #1e90ff;
                    color: white;
                }

                &.disabled {
                    color: #999999;

                    &:hover {
                        background-color: #ffffff;
                        color: #999999;
                    }
                }
            }
        }
    }

}

/*快递单设置-传统面单*/
/*按钮*/
.expr_btn_green,
.expr_btn_gray,
.expr_btn_js {
    border-radius: 2px;
    padding: 0 38px;
    height: 24px;
    text-decoration: none;
    color: #ffffff;
    display: inline-block;
    line-height: 24px;
    margin: 8px 10px 0 0;
    cursor: pointer;
}

.expr_btn_green {
    background: #00bb9c;
    padding: 0;
    width: 100px;
    text-align: center;
}

.expr_btn_green:hover,
.expr_btn_gray:hover {
    color: #ffffff;
}

.expr_btn_green:hover {
    background: #33c9b0;
}

.expr_btn_gray {
    background: #a2a9ab;
}

.expr_btn_gray:hover {
    background: #bbc0c1;
}

.expr_btn_js {
    background: #f39f27;
    color: #ffffff;
    padding: 0 16px;
}

.expr_btn_js:hover {
    color: #ffffff;
    background: #f7a25f;
}

.expr_btn_red,
.expr_btn_orange,
a.expr_btn_orange {
    background: #e53939;
    border: none;
    border-radius: 2px;
    padding: 0 28px;
    color: #ffffff;
    height: 24px;
    line-height: 24px;
    cursor: pointer;
    font-family: 'Microsoft Yahei';
    *width: 80px;
    _width: 80px;
    _padding: 0px;
    text-decoration: none;
}

.expr_btn_red:hover {
    background: #ec4141;
}

.expr_btn_orange,
a.expr_btn_orange {
    background: #f39f27;
    text-decoration: none;
}

.expr_btn_orange:hover {
    background: #f7a25f;
}

a.expr_btn_orange form {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
}

.kdd_side_right .expr_btn_red,
.kdd_side_right .expr_btn_orange {
    color: #ffffff;
}

.kdd_side_right {
    .fhdDelAddTr {
        display: none;
        position: relative;
        top: 8px;
        text-align: right;
        padding-bottom: 10px;
        padding-right: 15px;

        a {
            color: #d7d71b;
        }

        .delTableTr {
            position: absolute;
            bottom: -7px;
            background: black;
            display: none;
            width: 845px;
            text-align: right;
            padding-right: 15px;
        }
    }
}

.btn_white_middle,
.btn_white_middle_js {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) -1px -64px;
    border: none;
    border-radius: 2px;
    height: 24px;
    /*padding: 0 14px;*/
    color: #365064;
    margin-top: 20px;
    cursor: pointer;
    min-width: 92px;
    font-family: 'Microsoft Yahei';
    _width: 88px;
}

/*上传按钮*/
a.btn_white_middle {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) -1px -64px;
    border: none;
    border-radius: 2px;
    height: 24px;
    /*padding: 0 14px;*/
    color: #365064;
    margin-top: 20px;
    cursor: pointer;
    min-width: 92px;
    font-family: 'Microsoft Yahei';
    _width: 88px;
    display: inline-block;
    line-height: 24px;
    text-decoration: none;
    text-align: center;
    margin-top: 0;
}

.btn_white_middle form {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
}

.input_file {
    display: inline-block;
    opacity: 0;
    filter: alpha(opacity=0);
    width: 92px;
    height: 24px;
    cursor: pointer;
}

.posi_re {
    position: relative;
}

.btn_white_middle:hover {
    color: #738a9b;
}

.btn_white_middle_js {
    background-position: left -343px;
}

.pupexpr_tlt {
    background: #333333;
    height: 40px;
    line-height: 40px;
}

.pupexpr_tlt h1 {
    font-size: 14px;
    color: #fefefe;
    float: left;
    margin: 0px 0 0 20px;
    font-weight: normal;
    line-height: 40px;
}

.kdd_set_main {
    background: #4e5159;
    padding: 10px 40px 10px 40px;
    height: 460px;
    *position: relative;
    clear: both;
    box-sizing: content-box;

    // display: flex;
    .kdd_set_item_box {
        width: 522px;
        padding: 20px 40px;
        border-right: 1px dashed rgba(119, 122, 130, 1);

    }

    .kdd_prview_box {
        position: relative;
        width: 345px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .kdd_prview_title {
            position: absolute;
            top: 20px;
            left: 20px;
            font-family: PingFangSC-Semibold;
            font-size: 14px;
            color: #B9CEDE;
            font-weight: 600;
        }

        .prview_image_box {
            // flex: 1;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            padding: 20px 10px;
            max-width: 270px;
            max-width: 270px;
            height: auto;
            background: #9AD4BA;
            border-radius: 2px;

            img {
                max-height: 300px;
            }

        }

        .prview_image_box_double {
            padding: 10px 5px;
            width: 270px;
        }

        img {
            border-radius: 4px;
        }
    }

    .fhd_set_item_box {
        width: 100%;
        padding: 20px 40px;

        .kdd_ctmd_bz {
            margin-bottom: 30px;
        }
    }
}

.kdd_set_main_box {
    display: flex;
}

.kdd_set_main_box .kdd_set_box {
    width: 60%;
}

.kdd_set_main_box .kdd_set_img {
    width: 40%;
    overflow-y: auto;

    .kdd_set_img_view {
        width: 100%;
    }
}

.kdd_set_main p {
    margin: 0px;
}

.kdd_ctmd_bz {
    padding-bottom: 30px;
    clear: both;
}

.kdd_ctmd_bz dt {
    color: #b9cede;
    margin-bottom: 10px;
}

.kdd_ctmd_bz dt span {
    color: #ffffff;
    font-weight: bold;
    display: inline-block;
    margin-right: 10px;
}

.kdd_ctmd_bz dd {
    padding-left: 18px;
    color: #999999;
}

.xz_dzms_dl dd {
    float: left;
    text-align: center;
    color: #999999;
    margin-top: 6px;
}

.xz_dzms_dl .xbq_edit_dd {
    float: none;
    text-align: left;
    padding: 0;
}

.xz_dzms_dl dd span {
    display: inline-block;
    margin-top: 10px;
}

.kdd_img_box,
.fhd_img_box {
    width: 160px;
    background: #424242;
    border: 1px solid #333333;
    text-align: center;
    padding: 9px 0;
    margin-bottom: 10px;
    cursor: pointer;
}

.fhd_img_box {
    padding: 9px 8px;

    img {
        height: 90px;
    }
}

.xz_dzms_dl dd.cur .kdd_img_box {
    /*border: 3px solid #00bb9c;*/
    position: relative;
}

.xz_dzms_dl dd.cur {
    color: #ffffff;
}

.xz_border_green {
    border: 3px solid #00bb9c;
    width: 160px;
    height: 90px;
    position: absolute;
    top: -1px;
    left: -3px;
    *height: 94px;
    _height: 96px;
}

.kdd_ctmd_bz dd label {
    cursor: pointer;
}

.xz_dt_form label {
    display: block;
    margin: 20px 0 0px 0;
}

.kdd_ctmd_bz dd label.on {
    color: #ffffff;
}

.kdd_ctmd_bz dd label:hover {
    color: #ffffff;
}

.dzmd_up_dtimg_box {
    float: left;
    margin: 20px 0 0 20px;
}

.dzmd_up_dtimg {
    position: relative;
}

.dzmd_up_dtimg img,
.dzmd_up_dtimg {
    width: 305px;
    height: 170px;
}

.xz_right_icon {
    display: inline-block;
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -1px -23px;
    width: 16px;
    height: 16px;
    position: absolute;
    right: 10px;
    bottom: 10px;
}

.dzmd_up_dtimg_box p a {
    color: #999999;
    text-decoration: none;
    margin-right: 16px;
    display: inline-block;
}

.dzmd_up_dtimg_box p a:hover {
    color: #ffffff;
}

.dzmd_up_dtimg_box p span {
    margin-right: 16px;
    display: inline-block;
}

.dzmd_up_dtimg_box p {
    margin-top: 7px;
}

.form_box_la span {
    width: 185px;
    display: block;
    float: left;
    margin: 4px 0 2px 0;
}

// .temp-size-list span {
//     width: auto;
//     margin: 4px 46px 2px 0;
// }
.form_box_la_em {
    display: block;
    font-style: normal;
    padding-left: 16px;
    margin-top: 8px;
    overflow: hidden;
}

/*快递模板主页面*/

.pup_kdd_sy_box {
    width: 878px;
    position: relative;
    z-index: 9;
}

.w1120 {
    width: 1120px;
}

.pup_tzsx_list {
    background: #333333;
    float: left;
    padding: 8px 0 8px 10px;
    width: 760px;
    min-height: 60px;
    border-top-left-radius: 2px;
}

.pup_tzsx_list li {
    float: left;
    width: 100px;
    padding: 0px 18px 0px 21px;
    margin: 0 10px 12px 0;
    color: #f39f27;
    height: 30px;
    background: #484848;
    line-height: 30px;
    position: relative;
    border: 1px dashed rgba(151, 151, 151, 1);
    border-radius: 2px;
    cursor: pointer;
    user-select: none;
    *padding: 0px 24px 0px 24px;
    _padding: 0px 16px 0px 16px;
}

.pup_tzsx_btn_box {
    background: #4f4f4f;
    float: left;
    text-align: center;
    width: 110px;
    min-height: 76px;
    border-top-right-radius: 2px;
    _height: 76px;
    position: absolute;
    height: 100%;
    right: 0;
}

.pup_tzsx_btn_box .expr_btn_gray {
    padding: 0 28px;
    margin-right: 0;
}

.pup_tzsx_btn_box .expr_btn_js {
    margin-right: 0;
}

.pup_tzsx_box {
    margin-bottom: 10px;
    _clear: both;
    _overflow: hidden;
    position: relative;
    width: 880px;
}

.pup_tzsx_list li:hover {
    background: #484848;
    border-radius: 2px;
}

.pup_tzsx_list li.cur {
    border: 1px dashed rgba(243, 159, 39, 1);
    z-index: 8;
    cursor: default;
}

.pup_tzsx_list li .temp_text {
    width: 100%;
    overflow: hidden;
    white-space: nowrap; // 文字不换行
    text-overflow: ellipsis;
}

.tx_left_icon,
.tx_right_icon {
    background: url(../../resources/img/print/icon_group_expr.png) no-repeat;
    display: inline-block;
    position: absolute;
    width: 16px;
    height: 30px;
    cursor: pointer;
    z-index: 2;
    zoom: 1;
}

.tx_left_icon {
    background-position: -56px top;
    top: 0;
    left: -8px;
}

.tx_left_icon:hover {
    background-position: -131px -343px;
}

.tx_right_icon {
    background-position: -76px top;
    right: -13px;
    top: 0;
}

.tx_right_icon:hover {
    background-position: -150px -343px;
}

.delete_icon_little {
    background: url(../../resources/img/print/icon_group_expr.png) no-repeat -172px -343px;
    ;
    width: 13px;
    height: 13px;
    position: absolute;
    top: 0px;
    right: 0px;
    transform: translate(50%, -50%);
    cursor: pointer;
    // _background: url(../../resources/img/print/icon_group_expr.gif) no-repeat -24px -10px;
}

// .delete_icon_little:hover {
//     background-position: -172px -343px;
// }
.pup_kdd_menu li {
    display: inline;
    margin-right: 2px;
    float: left;
}

.pup_kdd_menu li.tag a {
    width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.pup_kdd_menu li a {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat left -164px;
    display: block;
    float: left;
    text-align: center;
    color: white;
    height: 35px;
    line-height: 35px;
    text-decoration: none;
    padding: 0 9px 0 12px;
}

.pup_kdd_menu li.change_qj {
    font-family: '宋体';
}

.pup_kdd_menu li label {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -150px -164px;
    display: block;
    float: left;
    width: 3px;
    height: 35px;
}

.pup_kdd_menu li.prev a {
    background-position: -98px top;
    width: 18px;
    padding: 0;
}

.pup_kdd_menu li.prev a:hover {
    background-position: -146px -91px;
}

.pup_kdd_menu li.next a {
    background-position: -121px top;
    width: 18px;
    padding: 0;
}

.pup_kdd_menu li.next a:hover {
    background-position: -169px -91px;
}

.pup_kdd_menu li.add a {
    background-position: -144px top;
    width: 18px;
    padding: 0;
}

.pup_kdd_menu li.add a:hover {
    background-position: -192px -91px;
}

.pup_kdd_menu li a.no_radius {
    background-position: -3px -164px;
}

.pup_kdd_menu li a.line {
    background-position: -3px -164px;
    padding: 0;
}

.pup_kdd_menu li a.line:hover {
    color: #b9cede;
}

.pup_kdd_menu li a:hover {
    color: #dd9424;
}

.pup_kdd_menu li.cur a {
    width: 130px;
    color: #d65600;
    background: #fff;
    border-top-left-radius: 2px;
}

.pup_kdd_menu li.cur label {
    background-position: -148px -300px;
}

.pup_menu_box {
    position: relative;
    _clear: both;
    _width: 870px;
    _margin-top: 14px;

    ul {
        margin: 0;
        padding: 0;
    }
}

.pup_menu_box {
    display: flex;
}

// .pup_menu_box .pup_kdd_menu {
//     width: calc(878px - 200px);
//     display: inline-block;
// }

.pup_menu_box .change_qj {
    font-family: '宋体';
    background: #333333;
    border-radius: 3px 3px 0 0;
    display: inline-block;
    text-align: center;
    color: white;
    height: 35px;
    line-height: 35px;
    width: 206px;

    a {
        text-decoration: none;
    }
}

.pup_menu_box .change_qj a {
    color: #b9cede;

    &:hover {
        color: #dd9424;
    }
}

// .pup_menu_box .change_qj a.pad_r {
//     padding-right: 4px;
// }
// .pup_menu_box .change_qj a.pad_l {
//     padding-left: 4px;
// }
// .pup_menu_box .change_qj a.pad_r {
//     background-position: -2px -164px;
//     padding-left: 4px;
// }
// .pup_menu_box .change_qj a.ch_add {
//     padding-right: 4px;
// }

.delete_mid_icon {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat left top;
    width: 20px;
    height: 20px;
    position: absolute;
    top: 4px;
    right: 10px;
    cursor: pointer;
    // _background: url(../img/print/icon_group_expr.gif) no-repeat left top;
}

.ch_add i {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -150px -9px;
    display: inline-block;
    width: 10px;
    height: 7px;
}

.pup_kdd_menu li a:hover.ch_add i {
    background-position: -198px -100px;
}

.pup_kdd_menu li.change_qj a.pad_r {
    background-position: -2px -164px;
    padding-left: 4px;
    padding-right: 4px;
}

.pup_kdd_menu li.change_qj a.pad_l {
    padding-left: 4px;
}

.pup_kdd_menu li.change_qj a.ch_add {
    padding-right: 4px;
}

.pup_dykj_box {
    height: 560px !important;
    background: #ffffff;
    position: relative;
    width: 880px;
}

.kdddy_group_btn {
    position: absolute;
    left: 880px;
    top: 20px;
    font-family: '宋体';
}

.merge_kd_type {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    left: -100px;
    top: 91px;
    width: 98px;
    border: 1px solid #E4E7ED;
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
    background: #fff;
    font-family: '宋体';
}

.merge_kd_type .zmj_tip {
    width: 52px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 2px;
    color: #fff;
    transform: translateY(-110%);
    background: #F7B500;
    user-select: none;
    cursor: pointer;
}

.merge_kd_type .merge_kd_type_btn {
    margin-bottom: 1px;
    width: 100%;
    height: 38px;
    text-align: center;
    line-height: 35px;
    color: rgba(0, 0, 0, 0.65);
    border-radius: 4px 0 0 4px;
    font-size: 12px;
    cursor: pointer;

}

.merge_kd_type .active_merge_btn {
    background: #FFF5EB;
    color: #FD8204;
}

.merge_kd_type .merge_kd_set_btn {
    margin: 13px 0;
    width: 80px;
    height: 24px;
    background: #FD8204;
    border-radius: 2px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    cursor: pointer;

}

.btn_xzdy_yellow_big,
.btn_xzdy_gray {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat;
    width: 103px;
    display: block;
    text-decoration: none;
    margin-bottom: 2px;
    cursor: pointer;
}

.btn_xzdy_gray {
    background-position: right -56px;
    height: 30px;
    text-align: center;
    width: 103px;
    color: #666666;
    line-height: 30px;
}

.btn_xzdy_gray:hover {
    color: #858585;
    background-position: right -146px;
}

.btn_xzdy_yellow_big {
    background-position: top right;
    height: 49px;
    line-height: 49px;
    text-align: center;
    color: #3f4757;
    font-size: 16px;
    font-weight: bold;
}

.btn_xzdy_yellow_big:hover {
    background-position: right -91px;
    color: #656c79;
}

.select_icon_little {
    display: inline-block;
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -168px -2px;
    width: 9px;
    height: 8px;
    float: left;
    margin-top: 12px;
}

.mn_select_box {
    position: relative;

    &.select_fjr_box dd {
        height: auto;
        white-space: normal;
        border-bottom: 1px solid #bbb;
        padding-right: 29px;

        .xz_right_icon {
            right: 18px;
        }
    }
}

.mn_select_box .select_print_box_tip {
    display: none;
    position: absolute;
    white-space: nowrap;
    top: 50%;
    padding: 4px;
    border-radius: 4px;
    background-color: #fdd451;
    right: 0;
    transform: translate(105%, -50%);

}

.mn_select_box:hover .select_print_box_tip {
    display: block;
}

.mn_select_box dl {
    position: absolute;
    background: #ffffff;
    min-width: 228px;
    border: 1px solid #999999;
    font-family: '宋体';
    _width: 228px;
    top: 29px;
    max-height: 313px;
    overflow-y: auto;
    z-index: 100;

    .wenhao_img {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url(/src/print/src/resources/img/print/group.png) no-repeat -222px -60px;
    }
}

.mn_select_box dl dt {
    padding: 0 10px;
    height: 26px;
    line-height: 26px;
    border-bottom: 1px solid #dddddd;
    color: #333333;
    *width: 228px;
}

.mn_select_box dl dd {
    height: 26px;
    line-height: 26px;
    padding: 0 10px;
    cursor: pointer;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 230px;
    position: relative;
}

.mn_select_box dl dd:hover {
    background: #2ea5ff;
    color: #ffffff;
}

.mn_select_box .xz_right_icon {
    bottom: 5px;
    right: 3px;
}

.mn_select_box .btn_xzdy_gray span {
    width: 78px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: inline-block;
    display: inline-block;
    float: left;
    margin-left: 8px;
}

/*编辑模板-内容及位置设置*/
.kdd_side_left,
.kdd_side_left1 {
    width: 600px;
    float: left;
    background: #ffffff;
    height: 560px;
    overflow: auto;
}

.pup_kdd_content_box {
    background: #4e5159;
    clear: both;
    overflow: hidden;
    zoom: 1;
}

.kdd_side_right {
    float: left;
    width: 240px;
    color: #999999;
    padding: 16px 30px 0 30px;
    font-family: '宋体';
    background: #4e5159;
    height: 540px;
    _height: 544px;
}

.kdd_side_right_default {
    display: block;
    overflow-y: auto;
    padding: 16px 20px 0 20px;
    width: 260px;
}

.kdd_side_right select {
    color: #999999;
}

.hr_line {
    border-bottom: 1px solid #656870;
    margin: 16px 0;
}

.mar_span {
    display: inline-block;
    margin: 0 10px 0 5px;
}

.kdd_side_right h2 {
    color: #e79725;
    font-size: 12px;
    font-family: 'Microsoft Yahei';
    padding-left: 5px;
}

.dyset_box_part {
    padding: 14px 0 0 7px;
}

.dyset_box_part span.name {
    display: inline-block;
    width: 48px;
}

.dyset_box_part select {
    margin-left: 6px;
}

.dyset_box_part input {
    margin-left: 10px;
    color: #999;
}

.py_bar_l {
    float: left;
    margin-top: 14px;
    padding-left: 6px;
    position: relative;
    width: 45px;
}

.py_bar_m {
    float: left;
    padding: 0 8px 0 7px;
}

.py_bar_r {
    float: left;
    margin-top: 14px;
}

.py_bar_r span {
    display: block;
    margin-bottom: 10px;
}

.py_bar_r span input {
    margin-left: 10px;
}

.dyset_box_part input.input_check {
    margin-left: 0px;
}

.dyset_box_part input.input_radio {
    margin-left: 0px;
}

.dyset_box_part form span {
    display: inline-block;
    width: 108px;
}

.py_border {
    border: 2px solid #00bb9c;
    width: 30px;
    height: 50px;
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
}

.py_bar_l img {
    width: 30px;
    height: 50px;
    position: absolute;
    top: 2px;
    left: 2px;
}

.tz_pybtn_box {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -2px -207px;
    width: 80px;
    height: 80px;
    position: relative;
    _background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -2px -207px;
}

.dj_top,
.dj_bottom,
.dj_left,
.dj_right {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat;
    display: block;
    position: absolute;
    _background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat;
}

.dj_top {
    background-position: -99px -206px;
    width: 50px;
    height: 18px;
    top: 3px;
    left: 15px;
}

/*.dj_top.active{background-position: -254px -206px;}*/
/*.dj_top:hover{background-position: -177px -206px;}*/
.dj_top:hover {
    background-position: -254px -206px;
}

.dj_bottom {
    background-position: -99px -272px;
    width: 50px;
    height: 18px;
    bottom: 3px;
    left: 15px;
}

/*.dj_bottom.active{background-position: -254px -272px;}*/
/*.dj_bottom:hover{background-position: -177px -272px;}*/
.dj_bottom:hover {
    background-position: -254px -272px;
}

.dj_left {
    background-position: -87px -223px;
    width: 18px;
    height: 50px;
    bottom: 15px;
    left: 3px;
}

/*.dj_left.active{background-position: -242px -223px;}*/
/*.dj_left:hover{background-position: -165px -223px;}*/
.dj_left:hover {
    background-position: -242px -223px;
}

.dj_right {
    background-position: -143px -223px;
    width: 18px;
    height: 50px;
    bottom: 15px;
    right: 3px;
}

/*.dj_right.active{background-position: -298px -223px;}*/
/*.dj_right:hover{background-position: -221px -223px;}*/
.dj_right:hover {
    background-position: -298px -223px;
}

.edit_set_btn_group {
    padding-top: 0;
    /*padding-bottom: 10px;*/
    clear: both;
    overflow: hidden;
}

.edit_set_btn_group span {
    display: inline-block;
    margin-right: 5px;
    float: left;
}

.edit_set_btn_group span input {
    margin: 0 0 0px 0;
}

.edit_set_btn_group span input.btn_white_middle,
.btn_white_middle_js {
    min-width: 106px;
    _width: 106px;
    color: #365064;
}

.edit_set_btn_group span input.btn_white_middle:hover {
    color: #738a9b;
}

.mar_bot6 {
    margin-bottom: 6px;
}

.mar_bot10 {
    margin-bottom: 10px;
}

.dyset_box_part_nop {
    padding: 0px 0 0 7px;
    clear: both;
    overflow: hidden;
    zoom: 1;
}

.dyset_box_part_nop span {
    display: inline-block;
    width: 108px;
    margin: 3px 0;
    float: left;
}

.dyset_box_part label,
.dyset_box_part_nop label {
    cursor: pointer;
}

.dyset_box_part label.on,
.dyset_box_part_nop label.on {
    color: #fff;
}

.dyset_box_part label:hover,
.dyset_box_part_nop label:hover {
    color: #fff;
}

/*新增数据框*/
.f_white {
    color: #ffffff;
}

.templet_scroll {
    padding: 0 10px 0 30px;
    width: 260px;
}

.templet_list {
    border-top: 1px solid #656870;
    padding: 8px 0 1px 0;
    *padding: 8px 0 8px 0;
    _clear: both;
    _overflow: hidden;
    zoom: 1;
}

.templet_list li {
    min-width: 80px;
    float: left;
    margin-bottom: 8px;
}

.templet_list li.on {
    color: #ffffff;
}

.templet_list li label:hover {
    color: #ffffff;
}

.templet_list li label.on {
    color: #fff;
}

.templet_scroll .hr_line {
    margin: 8px 0;
}

.templet_list li.li_w120 {
    width: 128px;
}

.templet_list .f_orange {
    color: #f39f27;
}

.psfs_more_box {
    position: relative;
}

.psfs_more_main ul {
    clear: both;
    overflow: hidden;
}

.psfs_more_main {
    position: absolute;
    top: -134px;
    *top: -150px;
    right: 7px;
    *right: 0px;
    background: #656870;
    padding: 18px 20px 8px 20px;
    width: 100px;
    *width: 108px;
}

.psfs_more_main .hr_line {
    border-bottom: 1px solid #868789;
}

.psfs_more_box .delete_mid_icon {
    right: 10px;
    top: 10px;
}

.add_sj_font_set label {
    margin-right: 8px;
}

.kdd_side_right input {
    color: #999999;
}

.scroll_bg {
    background: #333333;
    width: 8px;
    position: absolute;
    right: 3px;
    height: 515px;
    border-radius: 6px;
}

#boxscroll {
    height: 560px;
    width: 240px;
    overflow: auto;
    margin-bottom: 20px;
}

#boxframe {
    position: absolute;
    top: 28px;
    left: 420px;
    width: 400px;
    height: 300px;
    overflow: auto;
    border: 2px solid #0f0;
}

#boxscroll4 {
    height: 300px;
    margin-top: 40px;
    background-color: #00ff66;
    font-family: Georgia, 'Times New Roman', Times, serif;
    font-size: 18px;
    padding: 20px;
    color: #006633;
    overflow: auto;
}

.add_sj_font_set p span {
    display: inline-block;
    width: 50px;
    text-align: right;
    margin-right: 6px;
}

.add_sj_font_set textarea {
    resize: none;
}

.mar_r6 {
    margin-right: 6px;
}

.kdd_side_right .input_check {
    *margin-right: 0;
}

.kdd_side_right .f_orange {
    color: #f39f27;
}

.add_sj_font_set {
    padding: 10px 0;
    border-top: 1px solid #656870;
}

.pad_bot60 {
    padding-bottom: 60px;
}

.pad_bot20 {
    padding-bottom: 20px;
}

.xzsj_btn_box {
    background: #656870;
    padding: 10px 30px;
    text-align: center;
}

.xzsj_btn_box .expr_btn_red,
.xzsj_btn_box .expr_btn_orange {
    width: 100px;
    padding: 0;
    margin: 0 8px 0 8px;
}

#content {
    height: 506px;
    width: 240px;
    position: relative;
}

.kdd_side_r270 {
    width: 300px;
    padding: 0;
    padding-top: 16px;
}

.delete_little_white_icon {
    width: 10px;
    height: 10px;
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -187px 0;
    _background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -187px 0;
    top: 13px;
    right: 11px;
    z-index: 999;
    display: inline-block;
    /*position: absolute;*/
    cursor: pointer;
    transition: All 0.4s ease-in-out;
    -webkit-transition: All 0.4s ease-in-out;
    -moz-transition: All 0.4s ease-in-out;
    -o-transition: All 0.4s ease-in-out;
    margin-top: 5px;
    margin-left: 5px;
}

.delete_little_white_icon:hover {
    transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
}

.selete_bg {
    display: inline-block;
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -182px -12px;
    width: 20px;
    height: 20px;
    position: absolute;
    top: 8px;
    right: 8px;
    border-radius: 50%;
}

/*上传底图*/
.up_down_boximg {
    width: 770px;
    height: 400px;
    background: #3e4147;
    margin: 0 auto;
    text-align: center;
}

.up_down_boximg img {
    width: 680px;
    height: 380px;
    margin-top: 10px;
}

.kdd_ctmd_big dd {
    padding-left: 14px;
}

.kdd_ctmd_big dd label {
    margin-right: 60px;
}

.down_pages {
    text-align: center;
    color: #ffffff;
    margin-top: 10px;
}

.down_pages a {
    color: #ffffff;
    text-decoration: none;
    display: inline-block;
    margin: 0 10px;
}

.down_pages a:hover {
    text-decoration: underline;
}

/*添加线条*/
.w220 {
    width: 120px;
    height: 544px;
    padding: 0;
}

.w780 {
    width: 780px;
}

.w60 {
    width: 60px;
}

.add_line_box dt {
    color: #b9cede;
    font-family: 'Microsoft Yahei';
    padding: 10px 0 0 4px;
}

.add_line_box dd span {
    display: inline-block;
    width: 50px;
}

.add_line_box dd {
    margin-top: 10px;
    padding-left: 34px;
}

.add_line_btn {
    text-align: center;
    margin-top: 30px;
}

.add_line_btn .expr_btn_orange {
    margin-right: 8px;
}

/*选中店标*/
.add_line_box .expr_btn_orange,
.add_line_box .expr_btn_gray,
.add_line_box .expr_btn_red {
    width: 70px;
    padding: 0 0 0 0;
    text-align: center;
    margin: 0 0 10px 0;
}

.text_c {
    text-align: center;
}

/*快递单全局设置*/
.all_set_left {
    float: left;
    width: 390px;
    border-right: 1px solid #6b6e76;
    height: 475px;
}

.all_set_left select {
    color: #999999;
}

.all_set_right {
    float: left;
}

.delete_little_gray {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -204px 3px;
    display: inline-block;
    width: 6px;
    height: 10px;
    cursor: pointer;
    *height: 14px;
    right: 4px;
    top: 4px;
    position: absolute;
}

/*.all_set_kdlist .delete_little_gray{position: absolute;top: 4px;right: 8px;}*/

.all_set_kdlist li {
    background: #eee;
    padding: 0 10px 0 10px;
    float: left;
    min-height: 20px;
    line-height: 20px;
    border-radius: 2px;
    color: #425b6d;
    margin: 0 10px 10px 0;
    position: relative;
}

.all_set_kdlist {
    float: left;
    width: 250px;
    padding-left: 4px;
    height: 120px;
    overflow-y: auto;
}

.all_set_left,
.all_set_right {
    .kdd_ctmd_bz dd {
        margin-bottom: 14px;
        padding-left: 42px;
    }

    .kdd_ctmd_bz dd span {
        display: inline-block;
        margin-right: 20px;
    }
}

.add_icon {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -44px -38px;
    width: 80px;
    height: 20px;
    display: inline-block;
    color: #fff;
    text-decoration: none;
    padding-left: 18px;
    line-height: 20px;
    cursor: pointer;
    margin: 4px 0 0 70px;
    *margin: 16px 0 0 70px;
    _background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -44px -38px;
}

.add_icon:hover {
    color: #ffffff;
}

.all_set_right {
    padding-left: 28px;
    *width: 400px;
}

.all_set_right h2 {
    color: #b9cede;
    font-size: 12px;
    font-weight: normal;
}

.all_set_right h2 span {
    font-weight: bold;
    color: #ffffff;
    display: inline-block;
    margin-right: 10px;
}

.slideControlContainer {
    height: 18px;
    background: #424242;
    /* Old browsers */
    padding: 0 9px;
    border: 1px solid #333333;
    border-radius: 7px;
    cursor: pointer;
    width: 170px;
    float: left;
    margin: 15px 10px 0 0;
}

.slideControlFill {
    background: #f39f27;
    /* Old browsers */

    display: block;
    height: 4px;
    border-radius: 7px;
    position: relative;
    z-index: 1;
    overflow: visible !important;
    /*margin-top: 7px;*/
}

.slideControlHandle {
    display: block;
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -1px -43px;
    _background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -1px -43px;
    width: 16px;
    height: 16px;
    position: absolute;
    right: 0;
    top: -6px;
    z-index: 5;
    cursor: pointer;
}

.ieShadow {
    zoom: 1;
    filter: progid:DXImageTransform.Microsoft.Shadow(color=#aaaaaa, direction=0, strength=1),
        progid:DXImageTransform.Microsoft.Shadow(color=#aaaaaa, direction=45, strength=1),
        progid:DXImageTransform.Microsoft.Shadow(color=#aaaaaa, direction=90, strength=1),
        progid:DXImageTransform.Microsoft.Shadow(color=#aaaaaa, direction=135, strength=1),
        progid:DXImageTransform.Microsoft.Shadow(color=#aaaaaa, direction=180, strength=1),
        progid:DXImageTransform.Microsoft.Shadow(color=#aaaaaa, direction=225, strength=1),
        progid:DXImageTransform.Microsoft.Shadow(color=#aaaaaa, direction=270, strength=1),
        progid:DXImageTransform.Microsoft.Shadow(color=#aaaaaa, direction=315, strength=1);
    top: -8px;
    right: -4px;
}

.slideControlInput {
    width: 35px;
    font-size: 1.5em;
    padding: 3px 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    text-align: center;
    color: #333;
    font-weight: bold;
}

.jdt_box {
    padding-left: 40px;
    _clear: both;
    _overflow: hidden;
    zoom: 1;
}

.slide_control_bg {
    display: block;
    background: #f39f27;
    display: block;
    height: 4px;
    border-radius: 7px;
    margin-top: 7px;
}

.all_set_right i {
    color: #999999;
    font-style: normal;
    margin: 15px 20px 0 0;
    *margin: 15px 16px 0 0;
}

/*快递单发货网点设置*/
.w1000 {
    width: 1009px;
}

.kdd_goods_netbox {
    height: 560px;
    border: 4px solid rgba(183, 183, 183, 0.3);
    border-radius: 5px;
    filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#7F000000, endcolorstr=#7F000000);
    *border: 4px solid #969393;
    border: 4px solid #969393\9;
}

.goods_net_left {
    width: 580px;
    background: #ffffff;
    height: 560px !important;
    float: left;
}

.goods_net_right {
    width: 260px;
    background: #4e5159;
    height: 530px;
    float: left;
    padding: 30px 20px 0 20px;
    overflow-y: auto;

    h2 {
        color: #eb9e26;
        font-size: 12px;
    }

    .fc-b9cede {
        color: #b9cede;
    }

    .info-msg {
        color: #d75fd2;
    }

    a.info-msg {
        color: #b9cede;
    }

    .notYet_tips_block {
        height: 100%;
        display: flex;
        justify-content: center;
    }

    .dw_tips_block {
        box-sizing: border-box;
        height: 100%;
        padding-top: 80px;

        .tips_content {
            background-color: #3e4147;
            color: #edeeef;
            font-size: 14px;
            padding: 25px 10px;
            border-radius: 5px;
        }

        .link_block {
            margin-top: 30px;
            display: flex;
            flex-direction: column;
            color: #1a91ca;

            a {
                margin: 0;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                padding: 10px 0;
            }
        }
    }
}

.mar_l4 {
    margin-left: 4px;
}

.btn_net_box a {
    margin-bottom: 14px;
}

.yellow_btn_icon,
.gray_btn_icon {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat left -91px;
    width: 140px;
    height: 30px;
    display: inline-block;
    text-align: center;
    line-height: 30px;
    color: #3e4147;
    text-decoration: none;
    _padding: 9px 0;
}

.yellow_btn_icon:hover {
    color: #3e4147;
    background-position: left -124px;
}

.dy_icon00 {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -144px -42px;
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 4px;
}

.edit_icon00 {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -168px -41px;
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 4px;
}

.gray_btn_icon {
    background-position: -208px -293px;
}

.gray_btn_icon:hover {
    color: #5f6164;
}

.btn_net_box {
    text-align: center;
    padding-bottom: 36px;
}

.address_scroll {
    color: #999999;
    /*overflow-y: auto;*/
    width: 260px;
    *width: 260px;
}

.address_scroll i {
    font-style: normal;
}

.address_scroll .bind_shop,
.newPointHead .link {
    text-decoration: underline;
    color: #eb9e26;
    cursor: pointer;
    padding-left: 4px;
}

.newPointHead {
    .input_radio {
        margin-right: 4px;
    }
}

/*.address_scroll p{color: #FFFFFF;}*/
.dp_adres_box {
    box-sizing: content-box;
    width: 229px;
    border-top: 1px solid #36383d;
    background: #3e4147;
    border-radius: 4px;
    border-left: 1px solid #3a3d43;
    border-right: 1px solid #3a3d43;
    padding: 10px 10px 10px 10px;
    line-height: 18px;
    position: relative;
    _width: 229px;
}

.dp_adres_box span {
    display: inline-block;
    width: 200px;
    // *width: 200px;
}

.dp_adres_box .input_radio {
    vertical-align: top;
    margin-top: 4px;
    *margin-top: 0px;
    _margin-top: -3px;
}

.address_scroll label {
    margin: 20px 0 4px 0;
    display: inline-block;
}

.address_scroll label.on {
    color: #ffffff;
}

.address_scroll label:hover {
    color: #ffffff;
}

.address_scroll .sort_label {
    margin: 0px 0 4px 0;
    cursor: pointer;
}

.address_scroll .sort_label input {
    margin-right: 5px;
}

.dp_adres_box {
    margin-bottom: 2px;
    cursor: pointer;
}

.dp_adres_box:hover {
    background: #44464c;
    color: #fff;
}

.add_list_adrs label {
    color: #ffffff;
    border-bottom: 1px solid #6e7178;
    display: inline-block;
    padding: 0 0 8px 0;
    width: 97%;
}

.dp_adres_box p em {
    font-style: normal;
    color: #00bb9c;
    display: block;
}

.dp_adres_box .xz_right_icon {
    top: 50%;
    margin-top: -8px;
    right: 4px;
}

.kdd_font-set {
    padding: 20px 0 0 10px;
    border-top: none;
}

.pos_kdd_netset {
    left: 270px;
}

/*发货单编辑*/
.dyset_box_part form span.w150 {
    width: 150px;
}

.all_set_btn {
    display: inline-block;
    border: 1px solid #6b6b6b;
    width: 74px;
    height: 22px;
    color: #ffffff;
    text-align: center;
    line-height: 22px;
    text-decoration: none;
    border-radius: 2px;
    margin: 8px 10px 0 0;
    cursor: pointer;
    font-family: '宋体';
}

.all_set_btn:hover {
    color: #ffffff;
    background: #3e3c3c;
}

.fhd_ctmd {
    padding: 80px 0 0 6px;
}

.fhd_ctmd dt {
    margin-bottom: 18px;
    padding-left: 16px;
}

.fhd_ctmd dd {
    margin-bottom: 18px;
}

.pupexpr_tlt .all_set_btn {
    margin-right: 42px;
}

.pupexpr_tlt .delete_mid_icon {
    top: 10px;
}

/*.address_scroll .add_net_set label:hover{color:#999;}*/
.add_list_adrs label {
    color: #fff;
}

/*.add_list_adrs  label:hover{}*/

/*发货单全局设置*/
.fhd_all_set {
    .kdd_ctmd_bz dt {
        color: #f39f27;
        margin-bottom: 14px;
    }

    .all_set_right h2 {
        color: #f39f27;
    }

    .all_set_left,
    .all_set_right {
        .kdd_ctmd_bz dd span {
            width: 70px;
            text-align: right;
        }

        .kdd_ctmd_bz dd {
            padding-left: 20px;
        }
    }
}

.all_set_left,
.all_set_right {
    input {
        color: #999999;
    }
}

.all_set_left,
.all_set_right {
    textarea {
        width: 188px;
        height: 38px;
        vertical-align: top;
        padding: 6px;
    }
}

/*全局设置-融合模式*/
.merge_all_set {
    .kdd_ctmd_bz dd {
        margin-bottom: 14px;
        padding: 0;
        display: flex;

        span {
            min-width: 50px;
            margin-right: 31px;
            font-family: PingFangSC-Semibold;
            font-size: 12px;
            color: #B9CEDE;
            letter-spacing: 0;
            font-weight: 600;
        }

        .group_select {
            width: 200px;
            height: 23px;
            background: #FFFFFF;
            border: 0.5px solid rgba(187, 187, 187, 1);
            border-radius: 2px;
        }

        .group_list_box {
            width: 100%;

            .all_set_group_list {
                display: flex;
                flex-wrap: wrap;
                // margin-bottom: 23px;
                max-height: 310px;
                overflow-y: auto;

                li {
                    width: 35%;
                    margin-bottom: 20px;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;

                    .temp_name {
                        display: flex;
                        align-items: center;
                        width: 200px;
                        height: 23px;
                        background: #FFFFFF;
                        border: 0.5px solid #BBBBBB;
                        border-radius: 2px;
                        color: #425b6d;
                        // text-align: center;
                        // line-height: 23px;
                        font-family: PingFangSC-Regular;
                        font-size: 12px;
                        letter-spacing: 0;
                        font-weight: 400;
                        cursor: pointer;

                        .zmj_tips {
                            margin: 0 5px;
                            width: 52px;
                            height: 20px;
                            border-radius: 2px;
                            background: #F7B500;
                            color: #fff;
                            text-align: center;
                        }

                        .group_name_text {
                            margin: 0;
                            padding: 0 5px;
                            color: rgba(0, 0, 0, 0.85);
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }

                    .delete_group_temp {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-left: 5px;
                        width: 20px;
                        height: 20px;
                        background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat left top;
                        border-radius: 50%;
                        // color: rgba(0,0,0,0.85);
                        // background: url(../../resources/img/templateEdit/icon_group_expr.png)
                        // no-repeat -204px 3px;
                        cursor: pointer;
                    }
                }
            }

            .add_btn {
                margin-bottom: 12px;
                width: 116px;
                height: 32px;
                background: #FD8204;
                border-radius: 2px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #FFFFFF;
                text-align: center;
                line-height: 32px;
                font-weight: 400;
            }

            .default_box {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #FFFFFF;
                letter-spacing: 0;
                text-align: left;
                line-height: 22px;
                font-weight: 400;

                .default_checkbox {
                    margin-right: 8px;
                    width: 16px;
                    height: 16px;
                    background: #FFFFFF;
                    border: 1px solid rgba(217, 217, 217, 1);
                    border-radius: 2px;
                }
            }
        }
    }
}

/*分组设置样式*/
.group_all_set {
    .kdd_ctmd_bz dd {
        margin-bottom: 14px;
    }

    .kdd_ctmd_bz dd span {
        display: inline-block;
        margin-right: 20px;
    }

    .all_group_kdlist {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .all_group_kdlist li {
        width: 35%;
        margin-bottom: 20px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .temp_name {
            width: 200px;
            height: 23px;
            background: #FFFFFF;
            border: 0.5px solid #BBBBBB;
            border-radius: 2px;
            color: #425b6d;
            text-align: center;
            line-height: 23px;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.85);
            letter-spacing: 0;
            font-weight: 400;
        }

        .delete_group_temp {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: 5px;
            width: 20px;
            height: 20px;
            background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat left top;
            border-radius: 50%;
            // color: rgba(0,0,0,0.85);
            // background: url(../../resources/img/templateEdit/icon_group_expr.png)
            // no-repeat -204px 3px;
            cursor: pointer;
        }
    }

    .add_new_temp {
        margin-right: 16px;
        width: 102px;
        height: 32px;
        background: #FD8204;
        border-radius: 2px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #FFFFFF;
        text-align: center;
        line-height: 22px;
        font-weight: 400;
    }

    .replace_old_temp {
        width: 122px;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid #000000;
        border-radius: 2px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        text-align: center;
        line-height: 22px;
        font-weight: 400;
    }
}

.model {
    .add_temp_container {
        background: #FFFFFF;
        box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2);
        border-radius: 2px;

        .model__head {
            height: 55px;
            padding: 0 24px;
            font-family: PingFangSC-Medium;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 55px;
            font-weight: 500;
            border-bottom: 1px solid rgba(0, 0, 0, 0.09);
        }

        .select_temp_box {
            padding: 0 24px;
            height: 226px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.09);

            .select_temp_title {
                padding: 16px 0;
                font-family: PingFangSC-Semibold;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
                letter-spacing: 0;
                font-weight: 600;
            }

            .temp_list_box {
                height: 145px;
                display: flex;
                flex-direction: column;
                overflow-y: auto;

                .temp_item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 8px;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.65);
                    letter-spacing: 0;
                    text-align: left;
                    line-height: 22px;
                    font-weight: 400;

                    .temp_name {
                        display: flex;
                        align-items: center;

                        .temp_text {
                            width: 85px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }

                    .temp_checkbox {
                        margin-right: 8px;
                        width: 16px;
                        height: 16px;
                        background: #FFFFFF;
                        border: 1px solid #D9D9D9;
                        border-radius: 2px;
                    }

                    span {
                        margin-left: 9px;
                        font-family: PingFangSC-Regular;
                        font-size: 12px;
                        color: #FF4D4F;
                        letter-spacing: 0;
                        font-weight: 400;
                    }
                }
            }

            .add_temp_btn {
                display: block;
                padding-top: 5px;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #1890FF;
                letter-spacing: 0;
                font-weight: 400;
                text-decoration: underline;
            }
        }

        .save_btns {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 52px;
            padding: 0 16px;

            .ok-btn {
                margin-left: 8px;
                width: 65px;
                height: 32px;
                background: #FD8204;
                border-radius: 2px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #FFFFFF;
                text-align: center;
                line-height: 32px;
                font-weight: 400;
            }

            .cancel-btn {
                width: 65px;
                height: 32px;
                background: #FFFFFF;
                border: 1px solid #D9D9D9;
                border-radius: 2px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
                text-align: center;
                line-height: 32px;
                font-weight: 400;
            }
        }

        .select_kdd_box {
            padding: 0 24px;
            height: 226px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.09);

            .select_kdd {
                display: flex;
                align-items: baseline;
            }

            .select_temp_title {
                width: 120px;
                margin-right: 10px;
                padding: 16px 0;
                font-family: PingFangSC-Semibold;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
                letter-spacing: 0;
                font-weight: 500;
            }

            .group_select {
                width: 170px;
                height: 23px;
                background: #FFFFFF;
                border: 0.5px solid rgba(187, 187, 187, 1);
                border-radius: 2px;
            }

            .select_size,
            .select_mode {
                width: 310px;
                display: flex;
                flex-wrap: wrap;
            }
        }
    }
}

/*电子菜鸟*/
.goods_net_right_cn {
    width: 270px;
    padding: 9px 0px 0 0px;
    font-family: '宋体';
    position: relative;
}

.cn_sell_qhdp span {
    display: inline-block;
    height: 30px;
    line-height: 30px;
    color: #999999;
    border-top: 2px solid #4e5159;
    text-align: center;
    cursor: pointer;
}

.cn_sell_qhdp {
    clear: both;
    overflow: hidden;
}

.cn_sell_qhdp span.w114 {
    width: 110px;
    float: left;
}

.cn_sell_qhdp span.w156 {
    width: 160px;
    float: left;
}

.cn_sell_qhdp i {
    font-style: normal;
    color: #00bb9c;
}

.cn_sell_qhdp span.cur {
    color: #fff;
    background: #5c5f67;
    border-top: 2px solid #f39f27;
}

.cn_sell_main_box {
    background: #5c5f67;
    padding: 30px 12px 0 20px;
}

.cn_sell_main_box p {
    color: #ffffff;
}

.cn_sell_main_box p span {
    display: inline-block;
    margin-left: 4px;
    font-family: 'Microsoft Yahei';
}

.dp_code_dm {
    width: 225px;
    height: 28px;
    border: 1px solid #dddddd;
    color: #999999;
    padding-left: 5px;
    margin-top: 8px;
    line-height: 28px;
    font-family: '宋体';
}

.note_text_question {
    padding: 80px 0 0 0;
    height: 312px;
}

.note_text_question p {
    color: #999999;
    line-height: 22px;
}

.note_text_question p i {
    float: left;
    font-style: normal;
}

.note_text_question p em {
    font-style: normal;
    display: inline-block;
    width: 204px;
}

.note_text_question h2 {
    font-weight: normal;
}

.h620 {
    height: 620px;
}

.h620 .goods_net_left {
    height: 620px;
}

.h620 .goods_net_right {
    height: 590px;
}

.cn_why_icon {
    background: url(../../resources/img/templateEdit/icon_group_expr.png) no-repeat -146px -63px;
    display: inline-block;
    width: 14px;
    height: 14px;
    position: absolute;
    right: 12px;
    top: 16px;
    _right: 22px;
}

.ht404 {
    height: 489px;
}

.cn_sjx_box label {
    display: block;
}

.goods_net_right p {
    margin-top: 10px;
    padding-left: 10px;
}

/*.goods_net_right p.f_gray{color: #999999;}*/
.add_list_adrs {
    width: 260px;
}

.add_list_adrs .unlock {
    cursor: pointer;
    border: none;
    background: #f68c1e;
    color: #fff;
    font-size: 12px;
    border-radius: 2px;
    font-family: 'Microsoft Yahei';
    padding: 0 5px;
    float: right;
}

/*编辑表格*/
.edit_table_set dl {
    margin-bottom: 20px;
    _clear: both;
    _overflow: hidden;
    zoom: 1;
}

.edit_table_set dl dt {
    color: #f39f27;
    margin-bottom: 12px;
    font-family: 'Microsoft Yahei';
}

.edit_table_set dl dd {
    margin-bottom: 6px;
    margin-left: 6px;
}

.edit_table_set dl dd span {
    display: inline-block;
    width: 55px;
    text-align: right;
    margin-right: 6px;
}

.label_sj_box {
    width: 195px;
    margin-left: 6px;
}

.kdd_table_label {
    display: flex;
    flex-direction: column;
}

.edit_table_set dl dd span.w40 {
    width: 40px;
}

.edit_table_set dl dd label.on {
    color: #ffffff;
}

.label_sj_box label {
    margin-bottom: 6px;
    display: inline-block;
}

.edit_table_set dl dd.marlt_0 {
    margin-left: 0;
}

.edit_table2_sjbox .label_sj_box {
    float: left;
    margin-left: 6px;
}

.edit_table2_sjbox .label_sj_box label {
    margin-bottom: 7px;
    margin-top: 0;
}

.edit_table_set dl dd.edit_table2_sjbox {
    margin-top: 18px;
    _clear: both;
    _overflow: hidden;
    zoom: 1;
}

.dd_pad_l dd {
    padding-left: 4px;
}

.dyset_box_part p {
    color: #dadada;
    margin-bottom: 10px;
    padding-left: 8px;
    margin-top: 0;
}

.dyset_box_part p.f_gray {
    color: #999999;
    line-height: 16px;
}

.add_list_adrs h4 {
    /*color: #ffffd1;*/
    font-size: 12px;
}

.add_net_set {
    /*padding-left: 16px;*/
    line-height: 18px;
    margin-bottom: 4px;

    .segment_code {
        outline: 1px solid #eb9e26;
        padding: 0 4px;
        line-height: 16px;
        margin-left: 5px;
        color: #eb9e26;
        width: 80px;
        font-weight: 400;

        i {
            font-size: 14px;
            padding-right: 5px;
        }
    }

    h4 {
        font-size: 14.04px;
        line-height: 21.06px;
        color: #999999;
    }
}

.add_net_box {
    margin-top: 5px;
}

.dyset_box_part p label {
    display: inline-block;
    margin-right: 20px;
}

.dyset_box_part p label input {
    margin-right: 2px;
}

/*.dyset_box_part p label.on{color: #FFFFFF;}*/
/*.add_list_adrs{margin-bottom: 14px;}*/

/* 新加入的地址级联选择框*/
.dyset_box_part .J_Send_Area {
    display: inline-block;
}

.dyset_box_part .J_Send_Area select {
    width: 70px;
    margin-left: 5px;
}

/*编辑表格-table*/
.edit_table_box {
    background: #7c7d82;
    padding-top: 50px;
    padding-left: 40px;
    width: 560px;
}

.edit_table_box table {
    background: #ffffff;
    border-collapse: collapse;
}

.edit_table_box table th {
    border-right: 1px solid #7c7d82;
    color: #365064;
}

.edit_table_box table td {
    border: 1px solid #333333;
    text-align: center;
    height: 28px;
    color: #365064;
}

.left_side,
.right_side,
.add_line,
.delete_icon_white,
.tuo_icon {
    background: url(../../resources/img/print/edit_icon_table.png) no-repeat;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    _background: url(../../resources/img/print/edit_icon_table.gif) no-repeat;
}

.tuo_icon {
    background-position: -20px -19px;
    width: 16px;
    height: 17px;
    cursor: pointer;
}

.left_side {
    background-position: left top;
    width: 8px;
    height: 13px;
    margin-right: 6px;
}

.right_side {
    background-position: -11px top;
    width: 8px;
    height: 13px;
}

.add_line {
    background-position: -21px top;
    width: 15px;
    height: 15px;
}

.delete_icon_white {
    background-position: 1px -16px;
    width: 10px;
    height: 10px;
}

.edit_dec_box span {
    color: #ffffff;
    margin-left: 34px;
}

.edit_dec_box span i {
    margin-right: 6px;
}

.edit_dec_box {
    height: 26px;
    background: #7c7d82;
    padding-right: 20px;
    text-align: right;
    padding-top: 10px;
}

.move_box {
    background: #636468;
    width: 100%;
    height: 26px;
    position: relative;
    padding: 10px 0;
}

/*.move_box .left_side{position: absolute;left: 50%;top: 50%;margin-top:-6px;}
.move_box .right_side{position: absolute;left: 50%;top: 50%;margin-top:-6px;}*/
.resizeDivClass {
    position: absolute;
    right: -8px;
    top: -7px;
    z-index: 6;
}

/*.edit_table_box table td.tit{position: relative;}*/
.edit_table_box table .bot_total td {
    text-align: left;
    padding-left: 20px;
}

.edit_table_box table .bot_total td span {
    display: inline-block;
    margin-right: 25px;
}

.edit_table_box table .bot_total td i {
    font-style: normal;
}

.fh_add_line_box {
    background: #636468;
    width: 42px;
    float: left;
    margin-top: 41px;
    height: 117px;
    text-align: center;
    /*position: relative;*/
    z-index: 0;
}

.edit_table_box table td.last .add_line {
    position: absolute;
    top: 50%;
    margin-top: -8px;
    left: 50%;
    margin-left: -8px;
}

.move_box .delete_icon_white {
    position: absolute;
    right: 6px;
    top: 6px;
}

.fh_add_line_box .add_line {
    position: absolute;
    top: 50%;
    margin-top: -8px;
    left: 50%;
    margin-left: -8px;
}

.edit_table_box table th.last {
    background: #7c7d82;
    border: none;
    width: 42px;
}

.edit_table_box table td.last {
    background: #636468;
    border: 1px solid #636468;
    width: 22px;
    position: relative;
}

.resize_theone {
    left: -8px;
    position: absolute;
    top: -7px;
}

.td_title {
    position: relative;
    width: 100%;
    height: 100%;
}

/*快递单韵达电子面单*/
.kddset_yunda {
    width: 810px;
    /*height:440px;*/
    padding: 20px 0 0 60px;
}

.kddset_yunda h4 {
    font-size: 16px;
    font-weight: normal;
    color: #555;
    margin: 40px 0 0px 0;
}

.kddset_yunda ul {
    padding-left: 20px;
    margin: 20px 0;
    font-family: '宋体';
}

.kddset_yunda ul li {
    margin-bottom: 8px;
}

.kddset_yunda .input_text {
    width: 120px;
}

.kddset_yunda .kddset_yunda_w1 {
    width: 80px;
}

.kddset_yunda .kddset_yunda_w2 {
    width: 280px;
}

.kddset_yunda_note {
    padding-left: 20px;
    color: red;
    margin-top: 20px;
    line-height: 20px;
}

.kddset_yunda_btn {
    text-align: center;
    padding-top: 20px;
}

.dzmd_load_box {
    color: #fff;
    padding: 230px 0 0 50px;
    position: relative;
}

.dzmd_load_box img {
    margin-left: 3px;
    position: absolute;
    bottom: -1px;
}

.f_geen_blue {
    color: #00bb9c;
    font-weight: bold;
}

/*尚未开通电子面单服务*/
.dzmd_qh_box {
    padding-top: 20px;
    border-top: 1px solid #60636a;
    margin-top: 30px;
}

.dzmd_qh_box label.on {
    color: #ffffff;
}

.set_link_dp {
    border-bottom: 1px solid #60636a;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.set_link_dp a {
    color: #b8cfde;
    display: inline-block;
    margin-top: 6px;
}

.set_link_dp p,
.use_dpdm_box p {
    padding: 10px;
    background: #44474e;
    line-height: 18px;
}

.use_dpdm_box {
    margin-top: 6px;
}

.use_dpdm_box input {
    width: 226px;
    height: 24px;
    padding-left: 4px;
    color: #ff7e00;
}

.goods_net_right p.pad_l0 {
    padding-left: 0;
}

.goods_net_right .dp_adres_box p {
    margin: 0;
    padding-left: 0;
}

.dp_adres_box p.on {
    color: #ffffff;
}

.expr_green_loading {
    background: #00bb9c url(../../resources/img/print/loading_small.gif) no-repeat;
    display: inline-block;
    width: 15px;
    height: 15px;
    vertical-align: middle;
    margin-top: -3px;
    margin-right: 4px;
    margin-top: -2px/8;
}

.expr_yellow_loading {
    background: url(../../resources/img/print/loading_small.gif) no-repeat;
    display: inline-block;
    width: 15px;
    height: 15px;
    vertical-align: middle;
    margin-top: -3px;
    margin-right: 4px;
    margin-top: -2px/8;
}

.red {
    border: 1px solid #f8c987;
    background: #f8c987;
    color: #d00;
}

/*表格*/
.xzsj_pos_btn {
    position: absolute;
    width: 210px;
    text-align: center;
    bottom: -4px;
    left: 0;
}

.sj_show_input {
    width: 100px;
    float: left;
}

.sh_click_input {
    clear: both;
    overflow: hidden;
    zoom: 1;
    margin-left: 18px;
    width: 167px;
    margin-bottom: 4px;
}

.edit_table_set dl dd .sh_click_input span {
    float: left;
    width: auto;
    margin-top: 4px;
    margin-right: 4px;
}

.mar_bot20 {
    margin-bottom: 20px;
}

.edit_table_set dl dd select {
    margin-right: 5px;
}

p.databox_show {
    line-height: 21px;
}

p.databox_show span {
    width: 230px;
    text-align: left;
    padding-left: 13px;
    display: inline-block;
}

p.databox_show span label:hover {
    color: #fff;
}

p.databox_show span label.on {
    color: #fff;
}

.edit_mb_name {
    color: #e79725;
    font-weight: bold;
    font-family: Microsoft Yahei;
}

/*
    发货单样式,拷贝旧版的
*/

.jhdmaindiv {
    border: 0px solid #f00;
    text-align: center;
    line-height: 40px;
    font-size: 12px;
    font-weight: bold;
}

.mydiv_title {}

td.w_td1,
td.w_td2,
td.w_td3,
td.w_td4 {
    height: 40px;
    text-align: center;
    font-size: 12px;
    font-weight: 600;
    color: #323232;
}

.w_table h1 {
    float: left;
    margin: 10px 0px 10px 10px;
    width: 82px;
    height: 82px;
    border: 1px solid #dfdfdf;
}

.w_tk_xh {
    text-align: center;
}

.w_tk_tr01 {
    border-top: 3px solid #dfdfdf;
}

.w_tk_right h1 {
    height: 30px;
    background: url(../../resources/img/print/w_tk02.png) no-repeat;
    cursor: pointer;
    margin: 0;
}

.w_tk_right dl dt {
    color: #f0ab00;
    font-size: 14px;
    text-align: left;
}

.w_tk_right dl dd {
    color: #d9d9d9;
    line-height: 24px;
    text-align: left;
    margin: 0;
}

.w_tk_right dl {
    margin-top: 15px;
}

.w_tk_right p {
    width: 110px;
    color: #fff;
    text-align: center;
    height: 26px;
    line-height: 26px;
    background: #ff9a32;
    border-radius: 4px;
    margin-top: 31px;
    margin-left: 8px;
    cursor: pointer;
}

.w_tk_right .w_tk_rm {
    margin-left: 15px;
}

.w_tk_right .w_tk_rm input {
    margin-right: 8px;
}

.w_tk_p1 {
    text-align: right;
    width: 697px;
    font-size: 14px;
    margin-top: 12px;
}

.w_tk_p1 span {
    font-size: 16px;
    color: #ff0000;
}

.w_close {
    height: 40px;
}

.w_close img {
    float: right;
    cursor: pointer;
}

/*20161021-打印方式选择*/
.dyfs-xz-box {
    margin-bottom: 12px;
}

.dyfs-xz-box label {
    color: #adadad;
}

.dyfs-xz-box label.on {
    color: #fff;
}

.dyfs-xz-box .fhd_choose_box {
    display: flex;
    padding: 10px 0;

    select {
        width: 130px;
    }
}

.dyfs-xz-p span {
    display: block;
    line-height: 17px;
    color: #adadad;
}

p.dyfs-xz-p {
    margin-top: 6px;
    margin-left: 10px;
}

.sdfs-xz-span {
    padding-left: 36px;
}

.sdfs-xz-span a {
    color: #2981f0;
    text-decoration: none;
}

.newaction {
    width: 21px;
    height: 9px;
    background: url(../../resources/img/print/group.png) -349px -319px no-repeat;
    position: absolute;
    top: -1px;
    right: 5px;
}

/*快递单弹出窗主体内容*/
.kddset_main {
    /*float:left;*/
    width: 100%;
    background: white;
    box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.2);
    height: 460px;
    overflow-y: auto;
    *position: relative;
}

.kddset_paper {
    float: left;
    width: 400px;
    height: 630px;
    overflow: auto;
}

.kddset_main .btn_orange,
.kddset_main .btn_darkyellow {
    right: 6px;
    top: 38px;
}

.kddset_main h3 {
    font-size: 18px;
    color: #555;
    font-weight: normal;
}

.kddset_main .btn_orange:hover {
    color: #ffffff;
    text-decoration: none;
}

/*快递单弹出窗右侧-圆通电子*/
.kddset_option {
    /*float:left;*/
    padding: 20px 10px 36px 30px;
    line-height: 18px;
}

.pupbatch_tlt {
    height: 36px;
    background: #0d0d0d;
}

.pupbatch_tlt h1 {
    float: left;
    text-indent: 30px;
    line-height: 36px;
    font-size: 15px;
    color: #e5e33f;
}

.pupbatch_tlt .btn_gray,
.pupbatch_tlt .btn_yellowsml {
    float: right;
    margin: 7px 10px 0 0;
}

.wholeset_box {
    width: 860px;
    box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.2);
}

.wholeset {
    background: #666666;
    zoom: 1;
    padding: 20px 30px;
    color: white;
    min-height: 460px;
    _height: 460px;
}

.kddset_main {
    .tip_hand {
        background: url(../../resources/img/templateEdit/tip_hand.png) no-repeat;
        width: 41px;
        height: 34px;
        vertical-align: middle;
        position: absolute;
        right: 141px;
        bottom: 43px;
    }

    .branch-list-content {
        padding: 30px 20px 36px 30px;
        line-height: 18px;

        a {
            color: #0069ca;
            font-size: 12px;
            display: inline-block;
            margin-left: 6px;

            &.btn_orange_new {
                margin: 14px auto;
                width: 102px;
                text-decoration: none;
                display: block;
                border: none;
                height: 30px;
                line-height: 30px;
                background: #f68c1e;
                text-align: center;
                background: #f68c1e;
                color: #ffffff;
                font-size: 12px;
                border-radius: 4px;
                font-family: 'Microsoft Yahei';
                padding: 0 14px;
            }
        }

        h3 {
            font-size: 14px;
            color: #f68c1e;
            font-weight: normal;
            margin: 0 0 4px 0;
            line-height: 18px;
        }
    }

    .kdd_address_boxlist {
        padding-top: 24px;

        p {
            font-weight: normal;
            font-size: 12px;
            color: #333333;
            line-height: 20px;
            font-family: 宋体;
            margin-right: 16px;
            line-height: 20px;
            margin: 0;
        }
    }

    .kdd_address_box {
        padding-bottom: 6px;
        margin-left: 22px;
        margin-right: 20px;
    }

    .kdd_set_net {
        padding-top: 26px;

        li {
            margin-bottom: 4px;
            background: #f5f5f5;
            border-radius: 4px;
            padding: 8px 0 8px 5px;
            width: 94%;
            position: relative;
            border-top: 1px solid #efefef;
            display: block;
            cursor: pointer;

            .default-net {
                display: none;
            }

            &.active {
                .default-net {
                    display: inline-block;
                }
            }
        }

        .input_radio {
            vertical-align: top;
            margin-top: 3px;
        }

        .kdd_em_text {
            display: inline-block;
            width: 400px;
            line-height: 18px;
        }
    }
}

.dandadianzidiv {
    display: none;
    text-align: center;
    padding: 100px;
}

.dandadianzicenter {
    text-align: left;
    width: 100%;
    height: 100px;
    line-height: 40px;
    font-family: 'Microsoft YaHei';
    font-weight: normal;
}

.dandadianzicenter_fhd {
    width: 600px;
    margin-left: 140px;
    margin-top: 100px;
}

.dandadianzicenter h3 {
    color: #d65600;
    font-size: 21px;
}

.dandadianzicenter h4 {
    color: #999;
    font-size: 21px;
}

.dandadianzicenter a.btn-orange {
    width: 255px;
    height: 50px;
    line-height: 45px;
    font-size: 18px;
}

.icon-load {
    display: inline-block;
    background: url(../../resources/img/print/loading_small.gif) no-repeat center center;
    width: 16px;
    height: 16px;
    vertical-align: middle;
}

.fhdTempTable {
    td {
        span {
            display: inline-block;
        }
    }
}

.add_temp_content {
    .kdd_ctmd_bz {
        padding-bottom: 10px;

        &.add-kdd-company {
            padding-bottom: 30px;
        }

        &.xz_dzms_dl {
            padding-bottom: 20px;
        }
    }

    .form_box_la {
        form {
            overflow: hidden;
        }
    }
}

.not-match-info {
    left: 0px;
    color: #3e4147;
    padding: 5px;
    border: 1px solid #4e5159;
    border-radius: 4px;
    color: #ffffff;
    margin-top: 5px;

    ul {
        list-style: disc;
        list-style-position: inside;
    }

    a {
        color: #00bb9b;
    }
}

.tip-tool-model {
    display: block;
    padding: 5px 10px;
    position: fixed;
    z-index: 99999999;
    color: #bebebe;
    background: #3e4147;
    border-radius: 2px;
    max-width: 400px;
}

.pup_dykj_box {
    .goods_net_right {
        .dyfs-xz-box {

            .concatFhd,
            .on {
                display: inline;
            }
        }
    }
}