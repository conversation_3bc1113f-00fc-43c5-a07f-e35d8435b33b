class JDLprint {
    isInit = false;
    socket = null
    checkJdlPrintStatus = {
        isSupport: false,
        status: 1,
    }
    isConnecting = false
    handlerObj = {};


    init(callback) {
        this.isInit = true;
        setTimeout(() => {
            this.doConnect((isSupport) => {
                console.log('--------isSupport', isSupport);
            }, (checkJdlPrintStatus) => {
                console.log('+++++++checkJdlPrintStatus', checkJdlPrintStatus);
            });
        }, 2000);
        callback();
        window.onbeforeunload = () => {
            this.socket && this.socket.close();
        };
    }

    doConnect = (cb, checkCb, isLoading) => {
        let socketUrl = ''
            , errorHander
            , isCallBack = false;
        if (this.socket) {
            cb && cb(this.checkJdlPrintStatus);
            return;
        }
        // if (window.location.protocol == 'https:') {
        //     socketUrl = "wss://localhost:9113";
        // } else {
        socketUrl = 'ws://localhost:9113';
        // }
        if (!window.WebSocket) {
            console.info('京东云控件提示：The browser does not support websocket');
            this.checkJdlPrintStatus = {
                isSupport: false,
                status: 1,
            };
            cb && cb(false);
            return;
        }
        if (this.isConnecting) {
            console.error('京东云打印控件连接中……');
            return;
        }
        this.isConnecting = true;
        // isLoading && Tatami.showLoading();
        errorHander = () => {
            let _statusObj = this.checkJdlPrintStatus || {},
                _status;
            if (_statusObj && (_statusObj.isSupport || _statusObj.status == 4)) {
                _status = 4;    //控件是关闭状态
            } else {
                _status = 2;
            }
            this.checkJdlPrintStatus = {
                isSupport: false,
                status: _status,
            };
            // comp.Print.Data.modeType = 0;
            this.socket = null;
        };
        try {
            this.socket = new WebSocket(socketUrl);
            console.log('this.socket888this.socketthis.socket', this.socket);
            this.socket.onopen = (arg) => {
                this.socket.onmessage = (arg) => {
                    const data = JSON.parse(arg.data);

                    const handler = this.handlerObj[data.key || 'notifyPrintResult'];
                    if ($.isFunction(handler)) {
                        handler(data);

                    }


                };
                this.getVersion()
                this.checkJdlPrintStatus = {
                    isSupport: true,
                    status: 0,
                };
                if (!isCallBack) {
                    isCallBack = true;
                    cb && cb(true);
                    checkCb && checkCb(this.checkJdlPrintStatus);
                    // isLoading && Tatami.clearShow('print');
                }
                this.isConnecting = false;
            };
            //关闭云打印组件，改变是否可使用云打印表示变量
            this.socket.onclose = (arg) => {
                errorHander();
                this.isConnecting = false;
                if (!isCallBack) {
                    isCallBack = true;
                    cb && cb(true);
                    checkCb && checkCb(this.checkJdlPrintStatus);
                    // isLoading && Tatami.clearShow('print');
                }
                console.log('JDLprint closed');
            };

        } catch (e) {
            this.checkJdlPrintStatus = {
                isSupport: false,
                status: e.message && e.message.indexOf('SecurityError') > -1 ? 3 : 2,
            };
            if (!isCallBack) {
                isCallBack = true;
                cb && cb(true);
                checkCb && checkCb(this.checkJdlPrintStatus);
                // isLoading && Tatami.clearShow('print');
            }
            console.log('doConnect websocket connected error for :' + e);
            this.isConnecting = false;
        }
    }

    send = function (request, handler, isNoRepeat) {
        if (this.socket && request) {

            if (this.socket.readyState == 1) {
                // 打印通过key区分，其他内容结果通知
                const requestID = request.key || 'notifyPrintResult';
                this.handlerObj[requestID] = handler; //回调方法
                const sendJson = JSON.stringify(request);
                this.socket.send(sendJson);
            } else {
                //检测websocket状态是已关闭状态，只重新连接1次 不成功退出报错
                if (!isNoRepeat) {
                    this.socket.close();
                    this.socket = null;
                    this.doConnect((isSupport) => {
                        // isSupport && (comp.Print.Data.modeType = 1);
                        this.send(request, handler, true);
                    });
                } else {
                    console.log('close');
                    // Tatami.showFail('与打印控件连接断开，请确认控件是否正常开启。<a href="http://www.kuaidizs.cn/helpMap/getDetail?detailId=279" target="_blank">详见帮助</a>。如有问题，请联系客服');
                }
            }
        } else {
            console.error('京东云控件与客户端通信尚未准备就绪!');

        }
    }



    //webSocket请求对象
    Request = function (cmd, printer) {
        // this.version = '1.0';
        this.orderType = cmd;
        this.pin = '用户名';
    }

    /**
     * [printTemplate 快递单云打印]
     * @param {object}  arg  [打印所需的配置参数]具体格式如下：
     * {array}   printDatas      [打印数据]
     * {object}  templateSet     [模板详情 ]
     * {string}  selectedPrinter [打印机]
     * {Boolean} hasView         [是否预览，true预览，false打印]
     * {string}  viewType        [预览的方式：pdf/image,默认image形式]
     * {boolean} isPrintTest     [模板设计测试数据打印]
     * @param  {function}  printStartFunc    [开始打印打印回调]
     * @param  {function}  printSynFunc    [打印进度回调]
     * @param  {function}  printOkFunc     [打印完成回调函数]
     * @param  {function}  printFailCb     [打印完成回调函数]
     * @param  {function}  previewCb       [预览完成回调函数 {data:预览的URL数组array, message:'预览失败原因','isSuccess':true/false 是否预览成功}]
     * @param  {function}  notifyPrintCb   [监听打印结果的回调函数]
     */
    printTemplate = function (arg, printSynFunc, printOkFunc, printFailCb, previewCb, notifyPrintCb, compData, printStartFunc) {
        let copies = compData.printCopies || 1
            , dealData = []
            , _total = {}
            , _last = 0
            , _curCount = 0
            , _eIndex = 0
            , SIZE = 1000    //序号分页SIZE
			, DOCNUM = arg.isCncatFhd ? 1 : 10  // 一个任务:10个文档 10个订单数据
            , TASKNUM = 50  //  SIZE/DOCNUM   一批序号的任务数
            , ModeTempPrintcfg = arg.templateSet.ModeTempPrintcfg
            , printerName = ModeTempPrintcfg.DefaultPrinter
            , param = {}
            , documentNoInfo = arg.documentNoInfo;

        this.isCncatFhd =  arg.isCncatFhd; // 快速打印标识
        this.handlerObj['notifyPrintResult'] = notifyPrintCb || null;
        arg.printDatas.forEach((k, v) => {
            for (let i = copies; i > 0; i--) {
                dealData.push(k);
            }
        });


        _total = dealData.length;
        printerName = arg.selectedPrinter || ModeTempPrintcfg.DefaultPrinter;
        param.name = printerName;
        param.horizontalOffset = ModeTempPrintcfg.Leftright;
        param.verticalOffset = ModeTempPrintcfg.Updown;
        param.needTopLogo = !!ModeTempPrintcfg.Qslogo;
        param.needBottomLogo = !!ModeTempPrintcfg.Lclogo;
        param.paperSize = {};
        param.paperSize.width = ~~(ModeTempPrintcfg.Width);
        param.paperSize.height = ~~(ModeTempPrintcfg.Height);
        // jdlObj.setPrinterConfig(param, function (res) {
        const batchs = [];
        let curData = [];
        for (let i = 0; i < dealData.length; i++) {
            curData.push(dealData[i]);
            if (curData.length == DOCNUM) {
                batchs.push(curData);
                curData = [];
            }
        }
        if (curData.length > 0) {
            batchs.push(curData);
        }

        const sendBatchData = (index, batchs) => {
            if (index % TASKNUM == 0) {
                _last = _total - (index / TASKNUM) * SIZE;
                _eIndex = _last < SIZE ? _last : SIZE;
            }
            const task = this.getPrintTask({
                printDatas: batchs[index],
                templateSet: arg.templateSet,
                hasView: arg.hasView,
                printerName: printerName,
                viewType: arg.viewType,
                firstDocumentNumber: (documentNoInfo?.startDocumentIdx || 0) + ((index % TASKNUM) * DOCNUM + 1),
                totalDocumentCount: documentNoInfo?.allDocumentCount || _eIndex,
                isPrintTest: arg.isPrintTest,
                isCncatFhd:arg.isCncatFhd,
                wdSet: arg.wdSet,
            });
            index === batchs.length - 1 && printStartFunc && printStartFunc(task)
            this.print(task, (event) => {
                this.printFhdBylodop(arg, batchs[index], function () {
                    index++;
                    if (arg.hasView) {
                        if (event.code == 8 && event.status == 500) {
                            if (index == batchs.length) {
                                previewCb && previewCb({
									data: [`data:image/png;base64,${event.content}`],
                                    isSuccess: true,
                                });
                            } else {
                                sendBatchData(index, batchs);
                            }

                        } else {
                            previewCb && previewCb({
                                message: event.message || '预览失败，请重试',
                                isSuccess: false,
                            });
                            return true;
                        }
                    } else {

                        if (printSynFunc && 2 == event.code && event.status == 500 && index) {
                            let singleCount = DOCNUM
                            _curCount = index * DOCNUM;
                            if (_curCount > _total) {
                                singleCount = _total % DOCNUM
                                _curCount = _total
                            }
                            _curCount = _curCount / copies;
                            _curCount = ~~_curCount;
                            printSynFunc(_curCount, singleCount);
                        }
                        if (index == batchs.length) {
                            if (500 == event.status) {
                                printOkFunc(arg.printDatas);
                            } else {
                                alert('京东云控件提示打印失败：' + (event.message || ''));
                                printFailCb && printFailCb();
                            }
                        } else {
                            sendBatchData(index, batchs);
                        }
                    }
                    return true;

                })
            }
            );
        };

        sendBatchData(0, batchs);
        return true;
        // })
        // return true;
    }




    TaskCount = 0;
    //生成TaskID
    createTaskId = function () {
        this.TaskCount++;
        return (+new Date) + '_' + this.TaskCount + '_jd';
    }
    /**
     *
     * lodop交替打印发货单
     *
     * @param {object} arg
     * @param {object} orderList
     * @param {number} index
     * @param {callback} cb
     *
     */
    printFhdBylodop = function (arg, printDatas, cb) {

        if (!arg.isCncatFhd ) {
            cb && cb();
            return;
        }

        const printData = printDatas.map(t => {
            let newData = [];
            // 顺丰云打印的数据格式有点不一样
            // if(comp.base.isSFCloudPrint(tempModeListShow?.Exid)){
            //     // 发货单加解密开关 0 :1
            //     // if(!arg.fhdDecodeSwitch){
            //     //     t.s_phone = t.s_phone_encry
            //     //     t.s_tel = t.s_tel_encry
            //     //     t.s_name = t.s_name_encry
            //     //     t.s_ww = t.s_name_encry
            //     //     t.s_addrall = t.s_address_encry
            //     // }
            //     t.tableDataArray && t.tableDataArray.forEach(s => {
            //         newData = newData.concat(s.row);

            //     });
            //     t.tableDataArray = newData;
            //     return t;
            // } else {
            // 发货单加解密开关 0 :1
            // if(!arg.fhdDecodeSwitch){
            //     t.custom.s_phone = t.custom.s_phone_encry
            //     t.custom.s_tel = t.custom.s_tel_encry
            //     t.custom.s_name = t.custom.s_name_encry
            //     t.custom.s_ww = t.custom.s_name_encry
            //     t.custom.s_addrall = t.custom.s_address_encry
            // }
            t.tableDataArray && t.tableDataArray.forEach(s => {
                newData = newData.concat(s.row);

            });
            t.tableDataArray = newData;
            return t;
            // }
        });
        const printParams = {
            printDatas: printData,
            tempId: arg.thermalFhdId, //发货单id
            ptype: 'fhd',
            printBoxIsShow: 0,
            selectedPrinter: arg.selectedPrinter,  //选择的打印机
            templateSet: arg.fhdTemplateDetail, // 发货单详情
            // showPrinting: othersSet.showPrinting, // 显示出纸进度 暂时只针对快递单开放，开关设置走高级设置
        };
        //打印回调methods获取
        const methodsCb = {
			printOkFunc: function () {
				cb && cb();
            },
        };
        const defaultPrinter = (arg.fhdTemplateDetail.ModeTempPrintcfg || {}).DefaultPrinter;  //默认打印机
        const p = new comp.Print();
        if (arg.selectedPrinter != defaultPrinter) {
            p.setDefaultPrinter('fhd', arg.selectedPrinter, arg.thermalFhdId, function () {
                p.printTemplate(printParams, methodsCb);
            });
            // cb()
        } else {
            p.printTemplate(printParams, methodsCb);
        }

    };
    //组织打印任务
    getPrintTask = function (arg) {
        const task = {}, templateSet = arg.templateSet;
        task.taskID = this.createTaskId();
        task.preview = arg.hasView;
        arg.hasView && (task.previewType = (arg.viewType == 'pdf' ? 'pdf' : 'image'));
        task.printName = arg.printerName;
        task.tradeCode = arg.wdSet?.Username;
        task.printData = [];
        task.waybillCodes = [];

        let PrintDatas = [], wayBillNos = [], geturl = '', customUrl = '', customData = [];
        arg.printDatas.forEach((item, key) => {
            geturl = item.geturl;
            PrintDatas.push(item.jdprintData);
            wayBillNos.push(item.wayBillNo);
            customUrl = item.customUrl;//自定义区域url
            customData.push({
                ...item,
                addData: {
                    sender: {
                        name: item.f_name,
                        mobile: item.f_tel,
                        address: item.f_addr,
                    }
                }
            }); // 自定义区域对数据

        });

        task.printData = PrintDatas;
        task.waybillCodes = wayBillNos;
        task.tempUrl = geturl?.standardTemplateUrl;
        task.customData = customData;
        task.customTempUrl = 'http://kdzs-vpc.oss-cn-zhangjiakou.aliyuncs.com/' + customUrl;
        // task.customTempUrl =  "http://**************:8080/test.txt"
        // try {
        //     task.addData = {
        //         sender: {
        //             name: arg.printDatas[0].f_name,
        //             mobile: arg.printDatas[0].f_tel,
        //             address: arg.printDatas[0].f_addr,
        //         }
        //     };
        // }
        // catch(e) {
        //     console.log(e)
        // }
        // task.customTempUrl =  "http://**************:8080/test.txt"
        return task;
    }

    //获取打印机列表
    getPrinters = function (callback) {
        const request = new this.Request('GET_Printers', null);
        request.key = 'getPrinters'
        this.send(request, (response) => {
            callback && callback(response);
            return true;
        });
    }
    //获取版本号
    getVersion = function (callback) {
        const request = new this.Request('GET_Version', null);
        request.key = 'getVersion'
        this.send(request, (response) => {
            comp.Print.Data.jdVersion = response?.content
            callback && callback(response);
            return true;
        });
    }

    print = (task, callback) => {
        const request = task.preview ? new this.Request('PRE_View', task.printName) : new this.Request('PRINT', task.printName);
        request.parameters = task;
        request.key = task.taskID;
        this.send(request, (response) => {
            callback && callback(response);
            return true;
        });
    }

}
export default new JDLprint();
