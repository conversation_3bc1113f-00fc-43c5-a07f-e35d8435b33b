/// <reference path="comp.print.base.js" />
/// <reference path="comp.print.js" />
import {deepClone} from '../../print/printItem/common';
/**
 * 暴露 comp.print.ksprint，和 comp.print.cloudprint 很相似
 *
 * 功能：
 *      通过 socket 连接本地打印服务，发送指令给本地打印服务
 *      指令包括：print, getPrinters, getAgentInfo, setPrinterConfig
 *
 */
export default (function (window) {
    // var socketUrl = "ws://localhost:13528";
    let socket = null;
    const cloudObj = nameSpace('comp.print.ksprint');
    const handlerObj = {};
    let previewHandlerObj = {};
    let isConnecting = false;

    // 来自拼多多开放平台的应用的信息
    const requestID = '4058212b1c5d4b3e98a2dae19d015385';
    const ISVName = '快手电子面单';

    //初始化
    cloudObj.init = function (callback) {
        cloudObj.isInit = true;
        setTimeout(function () {
            cloudObj.doConnect();
        }, 2500);
        callback();
    };

    //打开链接
    //status :0 成功，1：浏览器不支持，2：未安装，3：浏览器设置原因
    cloudObj.doConnect = function (cb, checkCb, isLoading) {
        let socketUrl,
            errorHander,
            isCallBack = false;
        console.log('point8');
        if (socket) {
            cb && cb((comp.Print.Data.checkedKsPrintStatus || {}).isSupport);
            return;
        }

        socketUrl = 'ws://localhost:16888/ks/printer';

        if (!window.WebSocket) {
            console.info('The browser does not support websocket');
            comp.Print.Data.checkedKsPrintStatus = {
                isSupport: false,
                status: 1,
            };
            cb && cb(false);
            return;
        }
        if (isConnecting) {
            return;
        }
        isConnecting = true;
        isLoading && Tatami.showLoading({
            key:'print'
        });
        console.log('point9');
        errorHander = function () {
            let _statusObj = comp.Print.Data.checkedKsPrintStatus || {},
                _status;
            if (_statusObj && (_statusObj.isSupport || _statusObj.status == 4)) {
                _status = 4; //控件是关闭状态
            } else {
                _status = 2;
            }
            comp.Print.Data.checkedKsPrintStatus = {
                isSupport: false,
                status: _status,
            };
            comp.Print.Data.pddModeType = 0;
            socket = null;
        };
        try {
            comp.Print.Data.pddVersion = null;
            console.log('point103');
            socket = new WebSocket(socketUrl);
            console.log('point104');
            socket.onopen = function (arg) {
                socket.onmessage = function (arg) {
                    const data = JSON.parse(arg.data);
                    const requestID = data.requestID;
                    let handler = handlerObj[requestID];
                    const printSynFunc = handlerObj['printSynFunc'];
                    const previewHandler = previewHandlerObj[data.taskID];
                    if ($.isFunction(handler) && !$.isFunction(previewHandler)) {
                        const res = handler(data);
                        if (res) {
                            delete handlerObj[requestID];
                        }
                    }

					if( data.cmd === 'notifyPrintResult' ){         //监听打印状态返回消息 处理返回 提示反馈给用户
                        if( printSynFunc && (data.taskStatus === 'printed' || data.taskStatus === 'failed') ){
                            printSynFunc && printSynFunc(data);
                        }else if(data.taskStatus === 'failed'){ //失败
                            handler = handlerObj['notifyPrintResult'];
                            handler && handler(data);
                        }
                    }
                    previewHandler && previewHandler(data);
                };
                cloudObj.getAgentInfo()
                comp.Print.Data.checkedKsPrintStatus = {
                    isSupport: true,
                    status: 0,
                };
                if (!isCallBack) {
                    isCallBack = true;
                    cb && cb(true);
                    checkCb && checkCb(comp.Print.Data.checkedKsPrintStatus);
                    isLoading && Tatami.clearShow('print');
                }
                isConnecting = false;
            };
            //关闭云打印组件，改变是否可使用云打印表示变量
            socket.onclose = function (arg) {
                errorHander();
                isConnecting = false;
                if (!isCallBack) {
                    isCallBack = true;
                    cb && cb(true);
                    checkCb && checkCb(comp.Print.Data.checkedKsPrintStatus);
                    isLoading && Tatami.clearShow('print');
                }
                console.log('cloudprint closed');
            };
            socket.onerror = function (arg) {
                errorHander();
                isConnecting = false;
                if (!isCallBack) {
                    isCallBack = true;
                    cb && cb(true);
                    checkCb && checkCb(comp.Print.Data.checkedKsPrintStatus);
                    isLoading && Tatami.clearShow('print');
                }
                console.log('cloudprint error:' + arg);
            };


        } catch (e) {
            comp.Print.Data.checkPddPrintStatus = {
                isSupport: false,
                status: e.message && e.message.indexOf('SecurityError') > -1 ? 3 : 2,
            };
            if (!isCallBack) {
                isCallBack = true;
                cb && cb(true);
                checkCb && checkCb(comp.Print.Data.checkedKsPrintStatus);
                isLoading && Tatami.clearShow('print');
            }
            console.log('doConnect websocket connected error for :' + e);
            isConnecting = false;
        }
    };

    //发送数据
    cloudObj.send = function (request, handler, isNoRepeat) {
        console.log('point19', socket);
        console.log('point20', request);

        if (socket && request) {
            if (socket.readyState == 1) {
                const requestID = request.requestID;
                handlerObj[requestID] = handler;
                const sendJson = JSON.stringify(request);
                // 缓存打印预览
                if (request.task && request.task.preview) {
                    previewHandlerObj = {};
                    previewHandlerObj[request.task.taskID] = handler;
                }

                socket.send(sendJson);
            } else {
                //检测websocket状态是已关闭状态，只重新连接1次 不成功退出报错
                if (!isNoRepeat) {
                    console.log('point21');
                    socket.close();
                    socket = null;
                    cloudObj.doConnect(function (isSupport) {
                        isSupport && (comp.Print.Data.pddModeType = 1);
                        cloudObj.send(request, handler, true);
                    });
                } else {
                    Tatami.showFail(
                        '与打印控件连接断开，请确认控件是否正常开启。<a href="https://helptb.kuaidizs.cn/helpMap/getDetail?detailId=279" target="_blank">详见帮助</a>。如有问题，请联系客服',
                    );
                }
            }
        }
    };

    //获取请求的UUID，指定长度和进制
    cloudObj.getUUID = function (len, radix) {
        const chars =
      '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split(
          '',
      );
        let uuid = [],
            i;
        radix = radix || chars.length;
        if (len) {
            for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)];
        } else {
            let r;
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
            uuid[14] = '4';
            for (i = 0; i < 36; i++) {
                if (!uuid[i]) {
                    r = 0 | (Math.random() * 16);
                    uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r];
                }
            }
        }
        return uuid.join('');
    };

    //webSocket请求对象
    cloudObj.Request = function (cmd, printer) {
        this.requestID = cloudObj.getUUID(8, 16);
        this.version = '1.0';
        this.cmd = cmd;
        printer && (this.printer = printer);
    };

    /**
   * [printTemplate 快递单云打印]
   * @param {object}  arg  [打印所需的配置参数]具体格式如下：
   * {array}   printDatas      [打印数据]
   * {object}  templateSet     [模板详情 ]
   * {string}  selectedPrinter [打印机]
   * {Boolean} hasView         [是否预览，true预览，false打印]
   * {string}  viewType        [预览的方式：pdf/image,默认image形式]
   * {boolean} isPrintTest     [模板设计测试数据打印]
   * @param  {function}  printSynFunc    [打印进度回调]
   * @param  {function}  printOkFunc     [打印完成回调函数]
   * @param  {function}  printFailCb     [打印完成回调函数]
   * @param  {function}  previewCb       [预览完成回调函数 {data:预览的URL数组array, message:'预览失败原因','isSuccess':true/false 是否预览成功}]
   * @param  {function}  notifyPrintCb   [监听打印结果的回调函数]
   * @param  {function}  printStartFunc   [开始打印的回调函数]
   */
    cloudObj.printTemplate = function (
        arg,
        printSynFunc,
        printOkFunc,
        printFailCb,
        previewCb,
        notifyPrintCb,
        printStartFunc
    ) {
        let copies = comp.Print.Data.printCopies || 1,
            dealData = [],
            _total,
            _curCount,
            // 【临时方案】 快手大单量漏单严重，平台方建议我们改成10，我们偏不，先改成20看下
            DOCNUM = arg.isCncatFhd ? 1 : 10,  // 一个任务:10个文档 10个订单数据
            ModeTempPrintcfg = arg.templateSet.ModeTempPrintcfg,
            printerName = ModeTempPrintcfg.DefaultPrinter,
            previewUrlArr = [],
            param = {},
            documentNoInfo = arg.documentNoInfo;

        // 交替打印 、自动打印 聚合打印,   (安全版本，走 notifyPrintResult 响应)
        // 其他的场景 ; (快速版本，走 print 响应)
        cloudObj.isSpeedPrint = Boolean(arg?.speedPrint); // 快速打印标识
        cloudObj.isCncatFhd =  arg.isCncatFhd; // 快速打印标识
        if(( arg.isCncatFhd || arg.hasView )){
            cloudObj.isSafePrint = false; // 交替打印不走快速模式
        }
        handlerObj['notifyPrintResult'] = notifyPrintCb || null;
        handlerObj['printSynFunc'] = arg.showPrinting ? printSynFunc || null : null;
        /* <<< 测试用start */
        if(sessionStorage.getItem('testNum')){
            copies = sessionStorage.getItem('testNum');
        }
        /* <<<end */
        $.each(arg.printDatas, function (k, v) {
            for (let i = copies; i > 0; i--) {
                dealData.push(deepClone(v));
            }
        });

        _total = dealData.length;
        printerName = arg.selectedPrinter || ModeTempPrintcfg.DefaultPrinter;
        param.name = printerName;
        param.horizontalOffset = ModeTempPrintcfg.Leftright;
        param.verticalOffset = ModeTempPrintcfg.Updown;
        param.PrintTopLogo = !!ModeTempPrintcfg.Qslogo;
        param.PrintBottomLogo = !!ModeTempPrintcfg.Lclogo;
        param.paperSize = {};
        param.paperSize.width = ~~ModeTempPrintcfg.Width;
        param.paperSize.height = ~~ModeTempPrintcfg.Height;
        cloudObj.setPrinterConfig(param, function (res) {
            const batchs = [];
            let curData = [];
            for (let i = 0; i < dealData.length; i++) {
                curData.push(dealData[i]);
                if (curData.length == DOCNUM) {
                    batchs.push(curData);
                    curData = [];
                }
            }
            if (curData.length > 0) {
                batchs.push(curData);
            }

            function sendBatchData(index, batchs) {
                const task = cloudObj.getPrintTask({
                    printDatas: batchs[index],
                    templateSet: arg.templateSet,
                    hasView: arg.hasView,
                    printerName: printerName,
                    viewType: arg.viewType,
                    firstDocumentNumber: (documentNoInfo?.startDocumentIdx || 0) + (index * DOCNUM + 1),
                    totalDocumentCount: documentNoInfo?.allDocumentCount ||  _total,
                    isPrintTest: arg.isPrintTest,
                });
                index === batchs.length - 1 && printStartFunc && printStartFunc(task)
                cloudObj.print(task, function (event) {
                    cloudObj.printFhdBylodop(arg,batchs[index],function(){
                    index++;
						if (arg.hasView) {
							if (event.previewImage.length && event.status == 'success') {
								// previewUrlArr.push(event.previewURL);
								previewCb &&
									previewCb({
										data: event.previewImage,
										isSuccess: true,
										// isOnlyone: true,
									});
							} else {
								previewCb &&
									previewCb({
										message: '预览失败，' + (event.msg || '联系客服处理'),
										isSuccess: false,
									});
							}

                        // if(event.status === 'success' ){
                        //     if(!event.previewURL && !event.previewImage){
                        //         previewCb && previewCb({
                        //             message:'预览失败，'+ (event.msg || "联系客服处理"),
                        //             isSuccess:false
                        //         });
                        //         return true;
                        //     }
                        //     if(arg.viewType === 'pdf'){
                        //         previewUrlArr.push(event.previewURL);
                        //     }else{
                        //         previewUrlArr = previewUrlArr.concat(event.previewImage);
                        //     }
                        //     if(index == batchs.length){
                        //         previewCb && previewCb({
                        //             data:previewUrlArr,
                        //             isSuccess:true
                        //         });
                        //     }else{
                        //         sendBatchData(index, batchs);
                        //     }
                        // }else{
                        //     previewCb && previewCb({
                        //         message:event.msg||'预览失败，请重试',
                        //         isSuccess:false
                        //     });
                        //     return true;
                        // }
                    } else {
                        if (printSynFunc && 'success' == event.status && index) {
                            let singleCount = DOCNUM
                            _curCount = index * DOCNUM;
                            if(_curCount > _total){
                                singleCount = _total % DOCNUM
                                _curCount = _total
                            }
                            _curCount = _curCount / copies;
                            _curCount = ~~_curCount;
                            printSynFunc(_curCount,singleCount);
                        }
                        if (index == batchs.length) {
                            if ('success' == event.status) {
                                // $.isFunction(printOkFunc) && printOkFunc(event, task); 明天来改
                                typeof printOkFunc === 'function' && printOkFunc(arg.printDatas);
                            } else {
                                Tatami.showFail('打印失败：' + (event.msg || ''));
                                printFailCb && printFailCb();
                            }
                        } else {
                            sendBatchData(index, batchs);
                        }
                    }
                    return true;
                });
            })
            }

            sendBatchData(0, batchs);
            return true;
        });
        return true;
    };

    cloudObj.TaskCount = 0;
    //生成TaskID
    cloudObj.createTaskId = function () {
        cloudObj.TaskCount++;
        return +new Date() + '_' + cloudObj.TaskCount + '_ks';
    };

    cloudObj.documentID = 0;
    cloudObj.getDocumentID = function () {
        return '' + new Date().getTime() + ++cloudObj.documentID;
    };
        /**
     *
     * lodop交替打印发货单
     *
     * @param {object} arg
     * @param {object} orderList
     * @param {number} index
     * @param {callback} cb
     *
     */
        cloudObj.printFhdBylodop = function (arg,printDatas,cb) {
            if(!arg.isCncatFhd){
                cb && cb();
                return;
            }

            const printData = printDatas.map( t => {
                let newData = [];
                // 顺丰云打印的数据格式有点不一样
                // if(comp.base.isSFCloudPrint(tempModeListShow?.Exid)){
                //     // 发货单加解密开关 0 :1
                //     // if(!arg.fhdDecodeSwitch){
                //     //     t.s_phone = t.s_phone_encry
                //     //     t.s_tel = t.s_tel_encry
                //     //     t.s_name = t.s_name_encry
                //     //     t.s_ww = t.s_name_encry
                //     //     t.s_addrall = t.s_address_encry
                //     // }
                //     t.tableDataArray && t.tableDataArray.forEach(s => {
                //         newData = newData.concat(s.row);

                //     });
                //     t.tableDataArray = newData;
                //     return t;
                // } else {
                    // 发货单加解密开关 0 :1
                    // if(!arg.fhdDecodeSwitch){
                    //     t.custom.s_phone = t.custom.s_phone_encry
                    //     t.custom.s_tel = t.custom.s_tel_encry
                    //     t.custom.s_name = t.custom.s_name_encry
                    //     t.custom.s_ww = t.custom.s_name_encry
                    //     t.custom.s_addrall = t.custom.s_address_encry
                    // }
                    t.custom.tableDataArray && t.custom.tableDataArray.forEach(s => {
                        newData = newData.concat(s.row);

                    });
                    t.custom.tableDataArray = newData;
                    return t.custom;
                // }
            });
            const printParams = {
                printDatas:printData,
                tempId: arg.thermalFhdId, //发货单id
                ptype: 'fhd',
                printBoxIsShow: 0,
                selectedPrinter: arg.selectedPrinter,  //选择的打印机
                templateSet: arg.fhdTemplateDetail, // 发货单详情
                // showPrinting: othersSet.showPrinting, // 显示出纸进度 暂时只针对快递单开放，开关设置走高级设置
            };
            //打印回调methods获取
            const methodsCb = {
				printOkFunc: function () {
					cb && cb();
                },
            };
            const defaultPrinter = (arg.fhdTemplateDetail.ModeTempPrintcfg || {}).DefaultPrinter;  //默认打印机
            const p = new comp.Print();
            if (arg.selectedPrinter != defaultPrinter) {
                p.setDefaultPrinter('fhd', arg.selectedPrinter, arg.thermalFhdId, function () {
                    p.printTemplate(printParams, methodsCb);
                });
                // cb()
            } else {
                p.printTemplate(printParams, methodsCb);
            }

        };
    //组织打印任务
    //printDatas
    //hasView
    //printerName
    //firstDocumentNumber
    //totalDocumentCount
    //viewType
    //isPrintTest 模板设计测试数据打印
    cloudObj.getPrintTask = function (arg) {
        let task = {},
            templateSet = arg.templateSet,
            _printContentUrl;
        task.taskID = cloudObj.createTaskId();
        task.preview = arg.hasView;
        arg.hasView && (task.previewType = arg.viewType == 'pdf' ? 'pdf' : 'image');
        task.printer = arg.printerName;
        task.firstDocumentNumber = arg.firstDocumentNumber;
        task.totalDocumentCount = arg.totalDocumentCount;
        const documents = [];
        const exuserId = templateSet.ModeListShow.Exuserid;
        const modeListShowId = templateSet.ModeListShow.Mode_ListShowId;
        const kddType = templateSet.ModeList.KddType;
        const styleId = templateSet.ModeList.StyleId;
        const Excode = templateSet.ModeList.Excode;
        const origin = comp.base.getOrigin();
        let url =
            arg.hasView || arg.isPrintTest
                ? '/modeListshow/getCustomTemplateFile/preview'
                : '/modeListshow/getCustomTemplateFile';
        if(comp.Print.Data.platform === 'erp'){
            url = `/print/center${url}`;
        }
        const kdzsToken = comp.Print.getKdzsToken();
        const tokenName = 'kdzsTaobaoToken',
            _t = templateSet.modified;
        _printContentUrl = new URL(
            comp.base.pagePrint({
                styleId: styleId,
                Excode: Excode,
                kddType: kddType,
            }),
        );
        _printContentUrl.protocol = location.protocol; // 把该地址的协议设置为当前的协议

        _printContentUrl.searchParams.set('modeListshowId', modeListShowId);
        if(comp.Print.Data.platform === 'erp'){
            _printContentUrl.searchParams.set('userId', exuserId);
        }else{
            _printContentUrl.searchParams.set('taobaoId', exuserId);
        }
        if (comp.Print.Data.platform === 'pdd') {
            _printContentUrl.searchParams.set('kdzsTaobaoToken', kdzsToken);
        } else {
            _printContentUrl.searchParams.set('tokenName', kdzsToken);
        }
        _printContentUrl.searchParams.set('t', _t);

        _printContentUrl = _printContentUrl.toString();
        $.each(arg.printDatas, function (key, item) {
            const eleSurfaceArea = item.content;
            const {isNeedBreakPage = false, custom = {} } = item || {};
            eleSurfaceArea.encryptedData = eleSurfaceArea.printData;
            eleSurfaceArea.templateURL = eleSurfaceArea.templateUrl;
            eleSurfaceArea.ver = 'waybill_print_secret_version_1';

            delete eleSurfaceArea.templateUrl;
            delete eleSurfaceArea.printData;
            delete eleSurfaceArea.parentWaybillCode;
            delete eleSurfaceArea.waybillCode;
            delete eleSurfaceArea.version;

            let customArea = null;
            let dataItem = null;
            if (styleId != 41 && eleSurfaceArea.isCustom != 0) {
                // 41 是拼多多标准三联
                let currentUrl = url;
                const BREAK_PAGE_URL = '/print/center/modeListshow/getCustomTemplateFile/paging';
                if (isNeedBreakPage) {
                    currentUrl =  BREAK_PAGE_URL; // 分页打印用分页打印的自定义区域xml
                    const info = custom.f_info;
                    custom.f_info = '';
                    custom.f_info_paging = info;
                }
                customArea = {
                    templateURL:
                        origin +
                        currentUrl +
                        '?modeListshowId=' +
                        modeListShowId +
                        '&userId=' +
                        exuserId +
                        '&' +
                        tokenName +
                        '=' +
                        kdzsToken +
                        '&_t=' +
                        _t,
                    customData: custom,
                };
            }

            delete eleSurfaceArea.isCustom; // 删除多余字段
            delete eleSurfaceArea.recipient;
            dataItem = {
                documentID: item.documentID || cloudObj.getDocumentID(),
                contents: [eleSurfaceArea],
                waybillCode: item.waybillCode,
                ksOrderFlag: true,
            };
            if (customArea) {
                dataItem.contents.push(customArea);
            }
            documents.push(dataItem);
            // if (item.isNeedBreakPage) {
            // 发货内容需要打印在另一张纸上
            // const obj_ = {
            //     templateUrl: _printContentUrl,
            // };
            // dataItem = {
            //     documentID:'printContent_' + (item.documentID || eleSurfaceArea.data.waybillCode || cloudObj.getDocumentID()), //TODO 优化保证
            //     contents: [obj_],
            // };
            // const obj = {
            //     data: {
            //         1: item.custom['f_count'] ? item.custom['count'] + '件' : '', //件数  必须为string类否则打印不出第二页
            //         3: item.custom['txm_number'] || '', // 单号
            //         4: item.custom['f_info'] || '', // 发货内容
            //         txm_number: item.custom['txm_number'] || '', // 单号
            //     },
            // };
            // dataItem.contents.push(obj);
            // documents.push(dataItem);
            // }
            if (arg.isCncatFhd && arg.thermalFhdId && !_isKsTemp) {   //发货单快递单交替打印，发货单任务穿插
                dataItem = {
                    'documentID': 'fhd_' + ((customArea || {}).fhdexnumber || cloudObj.getDocumentID()),   //TODO 优化保证
                    'contents': [{
                        templateURL: _fhdUrl,
                        data: customArea,
                    }],
                };
                documents.push(dataItem);
            }
        });
        task.documents = documents;
        return task;
    };

    //获取打印机列表
    cloudObj.getPrinters = function (callback) {
        const request = new cloudObj.Request('getPrinters', null);
        cloudObj.send(request, function (response) {
            callback && callback(response);
            return true;
        });
    };

    cloudObj.getAgentInfo = function (callback) {
        let AppVersion = comp.Print.Data.ksVersion;
        console.log('point15', AppVersion);
        if (AppVersion) {
            callback && callback(AppVersion);
            return true;
        }
        const request = new cloudObj.Request('getClientInfo', null);
        cloudObj.send(request, function (response) {
            // 快手控件1.2.4版本及以下不支持获取控件版本，获取不到版本默认为1.0.0
            if(!(response || {}).currentVersion){
                AppVersion = '1.0.0';
                callback && callback(AppVersion);
                comp.Print.Data.ksVersion = AppVersion;
                return true;
            }else{
                console.log('point17', response);
                AppVersion = (response || {}).currentVersion;
                // V1.2.4 => 1.2.4，如果后续还有其他格式此处需要修改
                const formatVersion = AppVersion.match(/[\d+.]+/g);
                AppVersion = Array.isArray(formatVersion) ? formatVersion[0] : formatVersion;
                console.log('point18', AppVersion);

                callback && callback(AppVersion);
                comp.Print.Data.ksVersion = AppVersion;
                return true;

            }
        });
    };

    //设置全局设置
    cloudObj.setGlobalConfig = function (arg, callback) {
        const request = new cloudObj.Request('setGlobalConfig', null);
        request.TaskFailedNotify = true;
        cloudObj.send(request, function (response) {
            callback && callback();
            return true;
        });
    };
    // //获取打印控件的版本号
    // cloudObj.getAgentInfo = function(callback){
    //     //版本做个缓存吧
    //     let ver = comp.Print.Data.cnVersion;
    //     if(ver){
    //         callback && callback( ver ) ;
    //     }else{
    //         const request = new cloudObj.Request('getAgentInfo', null);
    //         cloudObj.send(request, function (response) {
    //             ver = (response || {}).version;
    //             comp.Print.Data.ksVersion = ver;
    //             callback && callback( ver ) ;
    //             return true;
    //         });
    //     }
    // };
    //设置纸张大小和便宜
    cloudObj.setPrinterConfig = function (param, callback) {
         // TODO 快手打印设置还未上线，暂时去掉，减少一次cmd请求
         callback && callback({});
         return true;
        let printer = {
            name: '拼多多模板专用打印机',
            PrintTopLogo: true,
            PrintBottomLogo: true,
            horizontalOffset: 0,
            verticalOffset: 0,
            paperSize: { width: 100, height: 180 },
        };
        printer = $.extend(printer, param);
        const request = new cloudObj.Request('setPrinterConfig', printer);
        cloudObj.send(request, function (response) {
            callback && callback(response);
            return true;
        });
    };

    //打印
    cloudObj.print = function (task, callback) {
        const request = new cloudObj.Request('print', task.printer);
        request.task = task;
        request.ISVName = ISVName;
        request.ERPId = requestID;
        request.version = '1.0';

        cloudObj.send(request, function (response) {
			if ((cloudObj.isSpeedPrint && !cloudObj.isCncatFhd) || response.previewImage) {
                callback && callback(response)
                return true;
            // 需要做交替打印的判断
            }else if(response.cmd === 'notifyPrintResult' ) {
                callback && callback(response)
                return true;
            }
        });
    };
})(window);
