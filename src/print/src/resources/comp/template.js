import printCenter_API from '../../common/api';
import CNTplLimitInfo from './templateCfg/CNTplLimitInfo';
import PddTplLimitInfo from './templateCfg/PddTplLimitInfo';
import DYTplLimitInfo from './templateCfg/DYTplLimitInfo';
import model from '../../common/model';
import { platformConfig } from '../../print/printFlow/platformConfig';
import { showShopInfoEditModal } from '../../b-components/ShopInfoEditModal';
const  getkddTempName = kddType => {
    switch(kddType){
        case 8 :
            return 'douyin';
        case 7 :
            return 'pdd';
        default :
            return 'cainiao';
    }
};
/**
 * 暴露 PrintTemplate
 *
 * 功能：打印单模板编辑功能
 */
(function ($) {
    let htmlStore,
        pxScale = 3.78,
        editShowMoreSet,
        outerConf,
        isMerge = false,
        xbqPaperSheet = 1;
    function getHeaderToken() {
        const kdzsToken = comp.Print.getKdzsToken();
        return {
            qnquerystring: kdzsToken,
        };
    }

    outerConf = {
        templateLimitInfo: {
            //模板的编辑限制情况
            cainiao: CNTplLimitInfo,
            pdd: PddTplLimitInfo,
            douyin:DYTplLimitInfo,
        },

        isForbiddenTemp: function (modeInfo) {
            let exCode = modeInfo.Excode,
                styleId = modeInfo.StyleId,
                kddType = modeInfo.KddType,
                isForbiddenTemp = false;

            if (kddType == 7) {
                isForbiddenTemp = exCode == 'CN7000001003751' && styleId == 41;
            }
            return isForbiddenTemp;
        },

        uploadBgImg: function ($input, fileName, param, success, fail) {
            //上传底图

            //更新正确的图片上传url 带token
            const url =
                '/modeListimg/uploadCustomBgImg?' +
                comp.Print.getKdzsToken(true);
            $input.fileUpload(
                url,
                fileName,
                function (data) {
                    data.companyId = param.companyId;
                    data.kddType = 1;
                    data.styleId = param.styleId;
                    data.exuserId = param.exuserId;
                    data.action = 'UploadCustomBgImg';
                },
                function (data) {
                    success && success(data.ImgSrc);
                },
                fail,
            );
        },
        uploadEleImg: function ($input, fileName, param, success, fail) {
            const querystring = comp.Print.Data.platform === 'pdd' ? `kdzsTaobaoToken=${ comp.Print.getKdzsToken(true) }` : comp.Print.getKdzsToken(true);
            //上传拖拽元素图
            let url = `print/center/modeListimg/uploadCustomImg`;
            // 供应商上传图片，接口不一样，并且需要传kddType
            let kddType = '';
            if(comp.Print.Data.platform === 'supplier') {
                try {
                    kddType = String(comp.Print.Data.currentTempInfo.ModeList.KddType);
                } catch(e) {
                    console.log(e);
                }
                url = `/modeListimg/uploadCustomImgSupply?${ querystring }&kddType=${ kddType }`;
            }
            $input.fileUpload(
                url,
                fileName,
                function (data) {
                    data.modeListShowId = param.modeListShowId;
                    data.userId = param.userId;
                    data.action = 'UploadCustomBgImg';
                },
                function (data) {
                    // 供应商获取图片，需要走接口获取
                    if(comp.Print.Data.platform === 'supplier') {
                        data.data = `${ window.$$requestUrl }/modeListimg/getCustomImgSupply?imgPath=${ data.data }&kddType=${ kddType }&${ querystring }`;
                    }
                    success && success(data.data);
                },
                fail,
            );
        },

        //通过接口获取底图列表，是一个数组类型
        getBgImgs: function (params, cb) {
            params.action = 'GetKddBgImgBy';

            $.ajax({
                type: 'get',
                dataType: 'json',
                url: printCenter_API.get_tmpl_bg_img,
                data: params,
                headers: getHeaderToken(),
                success: function (data) {
                    cb && cb(data.data || []);
                },
                error: function () {
                    alert('获取底图失败');
                },
            });

            // Tatami.request.forceAPICall({
            //     api: 'get_tmpl_bg_img',
            //     params: params,
            // }, function (data) {
            //     cb && cb(data.data || []);
            // }, function () {
            //     alert('获取底图失败');
            // });
        },
        //删除本地上传的图片
        delBgImg: function (id, success, fail) {
            const params = {
                action: 'DeleteCustomBgImg',
                bgImgId: id,
            };
            $.ajax({
                type: 'post',
                dataType: 'json',
                url: printCenter_API.del_tmpl_bg_img,
                data: params,
                headers: getHeaderToken(),
                success: function (data) {
                    success && success();
                },
                error: function () {
                    fail & fail();
                },
            });
            // Tatami.request.forceAPICall({
            //     api: 'del_tmpl_bg_img',
            //     params: {
            //         action: 'DeleteCustomBgImg',
            //         bgImgId: id,
            //     },
            // }, function () {
            //     success && success();
            // }, function () {
            //     fail & fail();
            // });
        },
        //存储模板数据
        saveTemplate: function (templateData,type, success, fail) {
            const bqType = templateData.ModeListShow.ExCode;
            // const bqType = {
            //     'bhd':'BHD',
            //     'bq':'BHD',
            //     'thd':'THD'
            // }
            const params = {
                action: 'SaveTemplate',
                // templateType : bqType,
                jsonParam: JSON.stringify(templateData),
            };
            $.ajax({
                type: 'post',
                dataType: 'json',
                url:
                    type == 'kdd'
                        ? printCenter_API.save_template
                        : (['bq','thd','bhd','bkd','dpd','tmd','wdpd','wtmd','cgd','rkd','ckd','thqd','zbd'].includes(type) ? printCenter_API.save_bhd_template : printCenter_API.save_fhd_template),
                data: params,
                headers: getHeaderToken(),
				success: function (data) {
                    if(['bq','thd','bhd','bkd','dpd','tmd','wdpd','wtmd','cgd','rkd','ckd','thqd','zbd'].includes(type)){
                        let tempList = comp.Print.Data['bqTemplateList' + type]?.ModeListShows || []
                        if(['wdpd','wtmd','tmd'].includes(type)){
                            tempList = comp.Print.Data['bqTemplateListdpd' ]?.ModeListShows || []
                        }
                        if(type === 'bkd'){
                            tempList = comp.Print.Data['bqTemplateListbhd' ]?.ModeListShows || []
                        }
                        for(let i = 0; i < tempList.length; i++ ){
                            if(tempList[i].Mode_ListShowId === templateData?.ModeListShow?.Mode_ListShowId) tempList[i] = templateData?.ModeListShow
                        }
                    }
					if (type == 'kdd' || type == 'fhd') {
						let tempKey = {
							kdd: 'kddTempInfos',
							fhd: 'fhdTempInfos',
						}
						let index = comp.Print.Data[tempKey[type]].findIndex(function (item) {
                            if (item.ModeListShow.Mode_ListShowId == templateData.ModeListShow.Mode_ListShowId) {
                                return true;
                            }
                            return false;
                        });
                        if (index >= 0) {
							const arr = comp.Print.Data[tempKey[type]];
                            templateData.modified = new Date().getTime()
                            arr.splice(index, 1, templateData);
                        }
                    }
                    success && success();
                },
                error: function () {
                    fail & fail(error ? error.message : '');
                },
            });
            // Tatami.request.forceAPICall({
            //     api: type == 'kdd' ? 'save_tmpl_data' : 'save_fhd_tmpl_data',
            //     params: {
            //         action: 'SaveTemplate',
            //         jsonParam: JSON.stringify(templateData),
            //     },
            // }, function () {
            //     success && success();
            // }, function (error) {
            //     fail & fail(error ? error.message : '');
            // });
        },
        //获得默认模板数据
        getDefaultTplData: function (exId, type, cb) {
            Tatami.showLoading({
            key:'print'
        });
            const params = {
                exid: exId,
            };
            $.ajax({
                type: 'post',
                dataType: 'json',
                url:
                    type == 'kdd'
                        ? printCenter_API.recovery_defaultTemplate
                        : printCenter_API.get_fhd_default_temp,
                data: params,
                headers: getHeaderToken(),
                success: function (data) {
                    Tatami.clearShow('print');
                    cb && cb(data.data || []);
                },
                error: function () {
                    Tatami.showFail(
                        error.message ||
                            '获取打印标签错误，请稍后再试，或联系客服处理',
                    );
                    Tatami.clearShow('print');
                },
            });
            // Tatami.request.forceAPICall({
            //     api: type == 'kdd' ? 'get_default_template' : 'get_fhd_default_temp',
            //     params: {
            //         exid: exId,
            //     },
            // }, function (data) {
            //     Tatami.clearShow('print');
            //     cb && cb(data.data || []);
            // },function(error){
            //     Tatami.showFail(error.message || '获取打印标签错误，请稍后再试，或联系客服处理');
            //     Tatami.clearShow('print');
            // });
        },
        isewm: function (dataName) {
            return dataName == 'ewm' || /ewm_|_ewm/.test(dataName);
        },

        istxm: function (dataName) {

            return /txm_|_txm/.test(dataName);
        },
        isWaterLabel: function(dataName){
            return /_care_label/.test(dataName);
        },
        getObjectFormProValue: function (val) {
            const arr = val.split(/,|，/);
            const obj = {};
            arr.each(function (ind, inv) {
                const itemArr = inv.split(/＝|=/);
                const proName = itemArr[0];
                const proVal = itemArr[1];
                if (proVal) {
                    obj[proName] = proVal;
                } else {
                    obj[proName] = '';
                }
            });
            obj.toString = function () {
                const str = [];
                for (const name in this) {
                    if (!name) {
                        continue;
                    }
                    const ins = [];
                    ins.push(name);
                    const val = this[name];
                    if (typeof val === 'function') {
                        continue;
                    }
                    if (typeof val !== 'undefined') {
                        ins.push(val);
                    } else {
                        ins.push('');
                    }
                    str.push(ins.join('='));
                }
                return str.join('，');
            };
            return obj;
        },

        sortInputArr: function (inputArr) {
            let i, j, inputTemp;
            for (i = 0; i < inputArr.length - 1; i++) {
                for (j = i + 1; j < inputArr.length; j++) {
                    if (inputArr[i].conf.Y_ > inputArr[j].conf.Y_) {
                        inputTemp = inputArr[i];
                        inputArr[i] = inputArr[j];
                        inputArr[j] = inputTemp;
                    }
                }
            }
            return inputArr;
        },
        /**
         * 对发货内容数据框双击时，打开发货内容打印样式设置
         * @param {Object} $target 目标元素
         * @param {Object} that this
         */
        handleDataBoxDoubleClick: function ($target, that) {
            // TODO:暂时只对有赞和快手做打印设置处理
            const platform = window.printAPI.getPlatform();
            // if(!/^((yz)|(ks))$/.test(platform)) return;  // 放开只分页打印有赞和快手的限制

            //如果当前点击的是「发货内容」且模板是菜鸟模板
            if (
                $target.find('.wakeUpFInfoSetting').length &&
                [3,7,8,9].includes(that.templateData.ModeListShow.KddType)
            ) {
                const now = +new Date();
                const intervalTime = 500; // 两次间隔小于 500ms 视为双击
                if (now - window.lastMouseDownTime < intervalTime) {
                    Tatami.pub('Tatami.clickPoint.manualTrigger', {
                        point: '11194.11195.11236.12123.32400',
                        _fm: '7325',
                    });

                    const userId = Tatami.localcache.get(
                        `${ platform === 'tb' ? 'taobaoId' : 'userId' }`,
                        'session',
                    );
                    Tatami.pub('openFhSet')
                    Tatami.pub('printBatch.showPrintStyleSetting', userId);
                    window.lastMouseDownTime = null;
                } else {
                    window.lastMouseDownTime = +new Date();
                }
            }
        },
    };

    /**
     * 模版整体
     * @param conf 配置信息，templateData, globalSetting, exuid, mockInputData, isNotSaveBackImg, viewMode(字符串) = '0' 查看状态，= '1' 编辑状态，= '2' 预览状态 tmpType: kdd快递单 fhd发货单; isRevise: 是否需要进行发货单修正
     * @param templateData 模版数据
     * @param globalSetting 全局设置
     * @param exuid	用户信息
     * @param outerConf 外部挂载函数
     * @constructor
     */
    function PrintTemplate(conf, outerConfFunc) {
        let setting = window.erpData?.advancedSetting?.groupPrintSetJsonString
        isMerge = setting?.openMergePrint == 2
        console.log('模版整体');
        console.info(conf);
        let _service, _serviceList;
        this.templateData = conf.templateData;
        this.globalSetting = conf.globalSetting;
        this.exuid = conf.exuid;
        this.isNotSaveBackImg = !!conf.isNotSaveBackImg;
        this.dragElements = [];

        this.tmpType = conf.tmpType || 'kdd';
        this.isFhd = conf.tmpType === 'fhd';

        this.fhdType = this.isFhd
            ? comp.base.getFhdType(conf.templateData)
            : '';
        this.isRevise = !!conf.isRevise;

        this.isCloudPrint = !!conf.isCloudPrint;
        this.onlyLodopPrint = (conf.tmpType === 'fhd' || conf.tmpType === 'bhd');
		this.isSmallTag = (conf.tmpType === 'fhd' && conf.templateData.ModeList.KddType === 4) || (['bhd', 'bq', 'thd', 'bkd', 'dpd', 'tmd', 'cgd', 'rkd', 'ckd', 'thqd', 'zbd', 'zbxp', 'bhbq'].includes(conf.tmpType)); // 备货单小标签
        this.isRevise = !!conf.isRevise;
        this.isThdSmallTag = conf.tmpType === 'thd';  //退货单小标签
        this.dragPosition = {}
        this.record_x = 0;
        this.record_y = 0;
        //打印数据处理
        _service = conf.templateData.ModeCustomerTemplate || {};
        _serviceList = conf.templateData.ModeServiceItems;
        conf.mockInputData.servicetype = _service.Svctypename || '';
        conf.mockInputData.servicename = _service.ServiceValue || '';
        if (_serviceList && _serviceList.length) {
            conf.mockInputData.servicelist = htmlStore.createServiceHtml(
                _serviceList,
                conf.mockInputData,
                conf.templateData.ModeList.Excode,
            );
        }

        this.mockInputData = conf.mockInputData; //mock数据

        this.singlePData = conf.singlePData; //单打存储的订单数据 ----单打预览界面所需要的预览数据，目前只用于发货单编辑表格之后的回调函数
        //是否是编辑状态、预览状态和查看状态
        // 如果是编辑状态 则dragitem不展示背景色，不能编辑内容，不能拖动，没有左侧右侧和顶部状态内容，只有中间部分，
        // 预览状态可以编辑文字框和数据框
        this.viewMode = conf.viewMode || '0'; //conf.viewMode || '0';
        if (outerConfFunc) {
            outerConf = $.extend(true, outerConf, outerConfFunc);
        }
    }

    //每个拖动项目，提供渲染、编辑、拖拽、收集数据的功能   isRevise: 是否需要对发货单某些元素位置进行修正
    function DragElement(conf, globalSetting, type, isRevise, mockInputData) {
        let dataName, _len;

        this.conf = conf;
        this.globalSetting = globalSetting;
        this.isDragging = false; //是否在拖拽
        this.isScaling = false; //是否在缩放

        //判断类型
        if (type) {
            this.dragType = type;
            return;
        }

        _len = conf.proArray.length;
        if (_len) {
            dataName = conf.proArray[0].Dataname;
        }
        if (_len == 1 && dataName === 'txta') {
            this.dragType = 'textInput';
        } else if (_len == 1 && (dataName === 'line' || dataName === 'line1')) {
            this.dragType = 'line';
        } else if (_len == 1 && /^img_logo/.test(dataName)) {
            this.dragType = 'logo';
        } else if (_len == 1 && (/^img/.test(dataName) || dataName === 'pic' || dataName === 'logo' || dataName === 'bgm' )) {
            this.dragType = 'image';
        } else if (_len == 1 && dataName === 'table') { //dataName.indexOf("table") > -1 || dataType.indexOf("hj") > -1 || dataType.indexOf("tb") > -1
            this.dragType = 'tableHtml';
        } else if (_len == 1 && dataName === 'rect') {
            this.dragType = 'rect';
        } else if(outerConf.isWaterLabel(dataName)){
            this.dragType = 'icon';
        }else {
            this.dragType = 'dataInput';
        }
        if (!isRevise) {
            return;
        }
        if (/^(f_memo|s_message|o_info|mjs)$/.test(dataName) && _len && conf.proArray[0].ProValue.indexOf('_nu＝0') > -1 && !mockInputData[dataName]) {
            this.isReviseHeight = '2';      //高度为0 name == "s_message" || name == "f_memo" || name == "o_info" || name == "mjs"
        } else if ((/^(f_memo|s_message|o_info|f_info|table|mjs)$/).test(dataName)) {       //针对单打发货单的打印预览的编辑的 该drag是否需要根据mockInputData计算高度
            this.isReviseHeight = '1';      //高度需要重新计算
        }
    }

    /**
     * 节流阀
     */
    function throttle(fn, threshhold) {
        let timer; //定时器
        let last; //记录上次时间
        threshhold || (threshhold = 250); //默认间隔为250ms
        return function () {
            // 保存函数调用时的上下文和参数，传递给 fn
            const context = this;
            const args = arguments;

            const now = +new Date();

            // 如果上次调用距本次调用的时间间隔不够，则不执行 fn，并重新计时
            if (last && now < last + threshhold) {
                clearTimeout(timer);

                // 保证在当前时间区间结束后，再执行一次 fn
                timer = setTimeout(function () {
                    last = now;
                    fn.apply(context, args);
                }, threshhold);
            } else {
                //如果时间间隔够了，则立刻执行 fn
                last = now;
                fn.apply(context, args);
            }
        };
    }

    /**
     * 预览模式下，获取用户编辑后的模版数据（仅限data数据框和text文本框）
     */
    PrintTemplate.prototype.getInputData = function () {
        //调用drag方法，收集这些数据 mockInputData
        let content,
            mockData = {};

        $(this.dragElements).each(function () {
            if (this.dragType === 'textInput' || this.dragType == 'dataInput') {
                content = this.getContentData();
                mockData[this.conf.InputID] = content;
            } else if (this.dragType === 'tableHtml') {
                //收集发货单表格html代码
                mockData['fhdTableHtml'] = this.getDom()
                .find('.dragEdit')
                .html();
            }
        });
        return mockData;
    };

    DragElement.prototype.getContentData = function () {
        let content = '';
        if (this.dragType === 'textInput' || this.dragType == 'dataInput') {
            // content = this.$dom.find('.dragEdit').text();
            content = this.$dom.find('.dragEdit')[0];
            content = content ? content.innerText : '';
        }

        return content;
    };

    /**
     * 设置新增拖拽组件的项目
     * 编辑模版必须
     *
     * @param item {Array}
     */
    PrintTemplate.prototype.setTplDefaultItem = function (item) {
        //预处理，按照Group字段分组
        let mapTree = {},
            i,
            newItem = [];

        for (i = 0; i < item.length; i++) {
            if (!mapTree[item[i].Group]) {
                mapTree[item[i].Group] = [];
            }
            mapTree[item[i].Group].push(item[i]);
        }

        for (i in mapTree) {
            newItem = newItem.concat(mapTree[i]);
        }

        this.tplDefaultItem = newItem;
    };
    /**
     * 设置活跃的拖拽元素
     * @param drag {object}
     */
    PrintTemplate.prototype._setActiveDrag = function (drag) {
        let _dName;
        //drag设置为激活状态, 基本上，设置他的样式是激活状态，设置startDrag，设置startScale，
        this.activeDragElement = drag;
        drag.startScale(true);
        drag.startDrag(true);

        _dName = ((drag.conf.proArray || [])[0] || {}).Dataname;
        if (
            _dName != 'servicelist' &&
            _dName != 'table' &&
            drag.dragType != 'logo'
        ) {
            //服务选项、表格元素，无需更改右侧
            this._showDragSet(drag.conf, drag.dragType);
        }
        if (_dName === 'table') {
            // 展示右侧编辑栏
            this.$dom.find('.operate_panel').show();
            //表格元素聚焦操作
            this.$dom.find('.operate_panel .editTableDrag').first().shake(); //编辑表格按钮 shake突出显示
            setTimeout(()=>{
                this.tmpType==='kdd' && comp.print.resources.editKddTable({
                    dom:this.$dom,
                    tempData: this.mockInputData,
                    modeInfo: this.templateData,
                    deleteTableFun: this._deleteTable.bind(this),
                    dragElements:this.dragElements,
                    addTableItem:this._addTableItem.bind(this),
                    addTableItemDrag:this._addTableItemDrag.bind(this)
                });
            },10)
        }
        if (_dName === 'line') {
            if (drag.conf.H_ > 1 && drag.conf.W_ > 1) {
                // 如果长和宽都不为 0 或 1 则说明该线是斜线；强制把斜线转为水平或竖直的线
                if (drag.conf.H_ > drag.conf.W_) {
                    drag.conf.W_ = 1;
                    drag.$dom.css('transform', 'rotate(0deg)');
                } else {
                    drag.conf.H_ = 1;
                    drag.$dom.css('transform', 'rotate(90deg)');
                }
            }
        }

        this.$dom.find('.drag_play_ground').append(drag.getDom());

        //聚焦下这个拖拽dom
        drag.$dom.find('.dragEdit').focus();
        this._genDragNoti(drag);
    };
    /**
     * 生成拖拽元素提示性文字
     * @param drag
     * @private
     */
    PrintTemplate.prototype._genDragNoti = function (drag) {
        // console.log('拖拽元素大小需要优化')
        // let maxDragHeight = (this.templateData.ModeListShow.customHeight)*3.78;
        // let maxDragWidth = (this.templateData.ModeListShow.customWidth)*3.78;

        let $noti = this.$dom.find('.template_noti'),
            td,
            text = [];

        if (!drag) {
            $noti.find('.main_title').css('visibility', 'hidden');
            td = {
                W_: this.templateData.ModeTempPrintcfg.Width + 'mm',
                H_: this.templateData.ModeTempPrintcfg.Height + 'mm',
                Y_: '0px',
                X_: '0px',
            };
        } else {
            $noti.find('.main_title').css('visibility', 'visible');

            td = {
                W_: (+drag.conf.W_).toFixed(0) + 'px',
                H_: (+drag.conf.H_).toFixed(0) + 'px',
                Y_: (+drag.conf.Y_).toFixed(0) + 'px',
                X_: (+drag.conf.X_).toFixed(0) + 'px',
            };
        }

        text.push('Top:' + td.Y_);
        text.push('Left:' + td.X_);
        text.push('Width:' + td.W_);
        text.push('Height:' + td.H_);

        $noti.find('.main_msg').html(text.join(';&nbsp;&nbsp;') + ';');
    };
    /**
     * 移除活跃的元素
     * @private
     */
    PrintTemplate.prototype._removeActiveDrag = function (isBack = true) {
        let temp = '';
        //上一个拖拽元素停止拖拽
        if (this.activeDragElement) {
            const {W_,H_,X_,Y_,InputID,scripConvertCode} = this.activeDragElement.conf
            this.gather_drag_position(InputID,{W_,H_,X_,Y_})
            this.activeDragElement.startDrag(false);
            this.activeDragElement.startScale(false);
            //移除右侧元素对应的面板
            this.$dom.find('.drag_setting_panel').remove();
            isBack && this.$dom.find('.operate_panel').show();

            //判断是不是内容二维码，是的话修改宽高为相等

            if (this.activeDragElement.conf.proArray.length == 1 && (scripConvertCode === 'ewm') ) {
                temp = this.activeDragElement.conf.proArray[0].Dataname;

                // if(temp == 'ewm_number' || temp == 'ewm' || temp == 'ewm_str'){
                if (outerConf.isewm(temp) || (scripConvertCode === 'ewm') || outerConf.isWaterLabel(temp) ) {
                    temp = this.activeDragElement.conf;
                    //根据编辑框大小调整二维码尺寸
                    temp = Number(temp.W_) > Number(temp.H_) ? temp.H_ : temp.W_;
                    // temp = temp.W_ > temp.H_ ? temp.H_ : temp.W_;
                    this.activeDragElement.applySetConf(
                        'height',
                        Math.abs(temp),
                        'px',
                    );
                    this.activeDragElement.applySetConf(
                        'width',
                        Math.abs(temp),
                        'px',
                    );
                }
                // else if(/image/.test(temp)){
                //     this.activeDragElement.applySetConf(
                //         'height',
                //         Math.abs(temp),
                //         'px',
                //     );
                //     this.activeDragElement.applySetConf(
                //         'width',
                //         Math.abs(temp),
                //         'px',
                //     );
                // }
            }

            this.activeDragElement = '';
        }

        this._genDragNoti();
    };

    //展示模版
    PrintTemplate.prototype.showTemplate = function () {
        //生成模版dom
        this._genDom();
        //初始化可拖拽组件
        this._initDrags(this.templateData.ModeInputs || []);
        //渲染出来可拖拽组件
        this._renderDrags();

        this._bindAutoChangeHeightEvent();

        return this.$dom;
    };

    //保存事件
    PrintTemplate.prototype.listenFinish = function (cb) {
        this.finishCb = cb;
    };
    //取消事件
    PrintTemplate.prototype.listenCancel = function (cb) {
        this.cancelCb = cb;
    };
    PrintTemplate.prototype._beforeSaveTemplate = function(templateData){
        //寻找dragElement
        const inputs = templateData.ModeInputs;
        let errInputId;
        const that = this;
        for(let i = 0; i < inputs.length; i ++){
            if( ( (inputs[i].proArray || [])[0] || {} ).Dataname == 'img' && inputs[i].Str_q === ''){
                errInputId = (inputs[i] || {}).InputID;
                break;
            }
        }
        if(errInputId){
            //寻找dragElement
            let  dragElement = '';
            for(let j = 0; j < that.dragElements.length; j ++){
                if(that.dragElements[j].conf.InputID == errInputId){
                    dragElement = that.dragElements[j];
                    break;
                }
            }

            if(dragElement){
                that._removeActiveDrag();
                that._setActiveDrag(dragElement);
            }
            Tatami.showFail('您添加的图片框未上传图片，请删除模板中的图片框或上传图片');
            return false;
        }
        return true;
    };
    //获取模板数据
    PrintTemplate.prototype.getTemplateData = function () {
        this._gatherDragElementConf();
        return this.templateData;
    };
    //生成模版dom
    // PrintTemplate.prototype._genDom = function () {
    //     let _tempData = this.templateData,
    //         $html = $(htmlStore.getTemplateHtml(_tempData, this.tmpType)),
    //         that = this,
    //         temp,
    //         style,
    //         isSaving = false,
    //         _height;

    //     const $dragPlayGround = $html.find('.drag_play_ground'),
    //         $templateGroundAddition = $html.find('.template_ground_addition'); // 模板底部附加的部分（菜鸟自留的不可编辑区域）

    //     //服务选项
    //     const styleId = _tempData.ModeList.StyleId;
    //     const { Excode } = _tempData.ModeList;
    //     const kddType = _tempData.ModeList.KddType;
    //     if (
    //         (this.viewMode == '1' &&
    //             ((kddType == 3 && comp.base.getTempStyle('cainiao', styleId)) ||
    //                 (kddType == 7 &&
    //                     comp.base.getPddTempStyle(
    //                         'pddKuaiYunZmj',
    //                         styleId,
    //                         Excode,
    //                     )))) ||
    //         comp.base.isNeedFwCaiNiaoPdd(kddType, Excode)
    //     ) {
    //         $html
    //         .find('.service_con')
    //         .html(
    //             comp.print.resources.showCainiaoServiceHtml(
    //                 _tempData.DynamicServiceInfoDtoVos,
    //                 _tempData.ModeLogisticsItems,
    //                 kddType,
    //             ),
    //         );
    //     }

    //     // if (this.fhdType === 'fhd-nh-bq') {
    //     //     temp = this.templateData;
    //     //     temp.ModeList.WidthPaper = 1000; //方便打印 在显示上显示50的宽度 但是打印的时候纸张宽度仍然是100
    //     //     temp.ModeTempPrintcfg.Width = '50';
    //     //     style = {
    //     //         width: 50 * pxScale + 'px', //这里拿货小标签的宽高前端固定死为 30 50 这样不用修改后端老模版里的数据
    //     //         height: 30 * pxScale + 'px',
    //     //     };
    //     // } else {
    //     temp = this.templateData.ModeTempPrintcfg;
    //     _height = ~~temp.Height;
    //     _height = !_height && temp.Direction == 3 ? 180 : _height;

    //     style = {
    //         width: ~~temp.Width * pxScale + 'px',
    //         height: _height * pxScale + 'px',
    //     };
    //     // }

    //     let _picSrc = this.templateData.ModeListShow.ImgSrc;
    //     if (_picSrc) {
    //         temp = this.templateData.ModeList;
    //         if (
    //             temp.KddType == 7 &&
    //             this.viewMode == '1' &&
    //             !comp.base.getCustomArea({
    //                 styleId: temp.StyleId,
    //                 type: 'kdd',
    //                 exCode: temp.Excode,
    //                 kddType: temp.KddType,
    //                 exId: temp.Exid,
    //             })
    //         ) {
    //             _picSrc = _picSrc.replace(/.png$/, '_edit.png');
    //         }
    //         const backgroundUrl = comp.base.dealBackgroundUrl(_picSrc);
    //         style['background-image'] = 'url(' + backgroundUrl + ')';
    //         style['background-size'] =
    //             'auto ' + (temp.HeightImg ? temp.HeightImg + 'px' : 'auto');
    //     } else {
    //         style['background-color'] = 'white';
    //     }
    //     style['font-weight'] =
    //         this.globalSetting.ModeSet.Isb == 1 ? 'bold' : 'normal';

    //     //小标签居中
    //     if (this.fhdType === 'fhd-sm-bq') {
    //         style['transform'] = 'translate(-50%, -50%)';
    //         style['top'] = '40%';
    //         style['left'] = '50%';
    //     }
    //     $dragPlayGround.css(style);

    //     //对底部附加区域设定样式
    //     $templateGroundAddition.css({
    //         width: style.width,
    //     });

    //     // 额外附加的底部菜鸟自留区域字样，只有菜鸟快递且是二联且是指定的一批快递才显示
    //     if (
    //         comp.base.getTempStyle('CNkuaidi', styleId) &&
    //         comp.base.getTempStyle('erlian', styleId) &&
    //         comp.base.isUseNewCainiaoTemp(Excode)
    //     ) {
    //         $templateGroundAddition.css('display', 'block');
    //     }

    //     //保存
    //     $html.find('.finishEditTemplate').on('click', function () {
    //         let $t = $(this),
    //             _service,
    //             modeList = that.templateData.ModeList || {},
    //             Exid = modeList.Exid,
    //             type = that.tmpType,
    //             _styleId = modeList.StyleId;
    //         if (isSaving) {
    //             return;
    //         }

    //         if(!that.templateData.ModeListShow.ExcodeName.trim()) {
    //             return window.dialog.show({
    //                 width: 300,
    //                 height: 170,
    //                 cancelName: '关闭',
    //                 okHide: true,
    //                 title: ' 提示',
    //                 content: `<div class="wrap-alert"><div class="title"><i class="icon-warn-big-orange"></i><h4>模版名称不能为空</h4></div><div class="body"></div></div>`,
    //             });
    //         }

    //         //先收集数据
    //         that._gatherDragElementConf();

    //         // 发货小标签 商品是否合并
    //         if (type == 'fhd' && Exid == 668) {
    //             that.templateData.ModeListShow.fhdIsMerge = $html
    //             .find('[name=fhdIsMerge]')
    //             .prop('checked')
    //                 ? 1
    //                 : 0;
    //         }

    //         //收集服务参数 菜鸟推荐和 三联模板
    //         if (comp.base.getTempStyle('cainiao', _styleId) || kddType == 7) {
    //             _service = comp.print.resources.getChooseService(
    //                 $html.find('.service_con .kdd_ctmd_bz_form'),
    //                 {
    //                     excode: modeList.Excode,
    //                     excodeName: modeList.Excodename_small,
    //                     expressType: modeList.KddType,
    //                     expressStyle: modeList.StyleId,
    //                 },
    //             );
    //             if (!_service) {
    //                 return;
    //             }
    //             that.templateData.ModeLogisticsItems = _service;
    //         }

    //         if(!that._beforeSaveTemplate(that.templateData) ){
    //             return;
    //         }

    //         isSaving = true;

    //         $t.val('保存中...');
    //         if (that.isNotSaveBackImg) {
    //             //if it's cloud print, then clear background
    //             that.templateData.ModeListShow.ImgSrc = '';
    //         }
    //         Tatami.pub('Tatami.clickPoint.manualTrigger', {
    //             point: '11194.11720.12047.12048',
    //             _fm: '2664',
    //         });
    //         if (that.templateData.ModeInputs) {
    //             //保存之前对modeInputs排序
    //             that.templateData.ModeInputs.sort(function (input1, input2) {
    //                 return input1.Y_ - input2.Y_;
    //             });
    //         }
    //         outerConf.saveTemplate(
    //             that.templateData,
    //             function () {
    //                 isSaving = false;
    //                 $t.val('保存成功');
    //                 that.$dom.remove();
    //                 const funcHook = (window.printAPI.compHookObj || {})
    //                 .afterChangedKddTemp;
    //                 funcHook && funcHook();
    //                 that.finishCb && that.finishCb(that.templateData);
    //             },
    //             function (message) {
    //                 Tatami.showFail(
    //                     message
    //                         ? '保存失败：' + message
    //                         : '保存失败，请稍后再试或联系客服。',
    //                 );
    //                 isSaving = false;
    //                 $t.val('保存');
    //             },
    //         );
    //     });
    //     //取消
    //     $html.find('.cancelEditTemplate').on('click', function () {
    //         that.$dom.remove();
    //         that.cancelCb && that.cancelCb();
    //         Tatami.pub('Tatami.clickPoint.manualTrigger', {
    //             point: '11194.11720.12047.12049',
    //             _fm: '2665',
    //         });
    //     });

    //     //监听预览模式下的数据框
    //     $html.on('click', '.dragElement', function (e) {
    //         const $target = $(e.target);
    //         outerConf.handleDataBoxDoubleClick($target, that);
    //     });

    //     this.$dom = $html;

    //     // = '0' 查看状态，= '1' 编辑状态，= '2' 预览状态
    //     if (this.viewMode == '0' || this.viewMode == '2') {
    //         this.$dom
    //         .find('.template_header,.template_operate,.template_noti')
    //         .hide();
    //         temp = {
    //             width: '880px',
    //             height: '560px',
    //         };
    //         this.$dom.css(temp).find('.template_content').css(temp);

    //         //temp = this.templateData.ModeList.KddType; 1五联单 2网点电子面单 3菜鸟电子面单
    //         //普通模版和自由模版可以进行缩放
    //         this.$dom.find('.template_ground').css({
    //             'transform-origin': 'top left 0px',
    //             transform:
    //                 'scaleY(' +
    //                 this.globalSetting.ModeSet.SliderH / 100 +
    //                 ') scaleX(' +
    //                 this.globalSetting.ModeSet.SliderW / 100 +
    //                 ')',
    //         });

    //         //非编辑状态不展示模版 并处理相关样式
    //         return;
    //     } else {
    //         /*temp = this.templateData.ModeTempPrintcfg;
    //     //宽设置为模版长宽，不超过范围内
    //         var height = temp.Height * pxScale;

    //         if(height < 500){
    //             height = 500
    //         }else if(height > 580){
    //             height = 580
    //         }
    //         this.$dom.find('.template_ground').css('height', height + 'px');
    //         this.$dom.find('.template_content, .template_operate').css('height', height + 20 + 'px')*/
    //     }
    //     //绑定drag事件
    //     this._bindDragEvent();
    //     //绑定缩放事件
    //     this._bindScaleEvent();
    //     //绑定点击空白复原事件
    //     this._bindResetEvent();
    //     //绑定新增拖拽项事件
    //     this._bindAddDragEvent();
    //     //绑定右侧操作模版的相关事件
    //     this._bindOperateEvent();
    //     //绑定右侧操作面板相关事件
    //     this._bindSettingEvent();
    // };

    //todo
    //生成模版dom
    PrintTemplate.prototype._genDom = function () {
        let _tempData = this.templateData,
            tmpType = this.tmpType,
            $html = $(htmlStore.getTemplateHtml(_tempData,tmpType)),
            that = this, temp, style, boxStyle = {},isSaving = false, _height;
        comp.base.toolTip( $html );

        const $dragPlayGround = $html.find('.drag_play_ground')
            , $templateGroundAddition = $html.find('.template_ground_addition'); // 模板底部附加的部分（菜鸟自留的不可编辑区域）

        //服务选项
        const styleId = _tempData.ModeList.StyleId;
        const { Excode } = _tempData.ModeList;
        // 所有面单均需展示开通的服务
        if (this.viewMode == '1' && this.tmpType === 'kdd' ) {
            $html.find('.service_con').html(comp.print.resources.showServiceHtml(_tempData));
        }

        temp = this.templateData.ModeTempPrintcfg;
        _height = ~~temp.Height;
        _height = (!_height && temp.Direction == 3) ? 180 : _height;

        style = {
            width: ~~temp.Width * pxScale + 'px',
            height: _height * pxScale + 'px',
        };

        let _picSrc = this.templateData.ModeListShow.ImgSrc;

        if (_picSrc) {
            temp = this.templateData.ModeList;
            // if (
            //     temp.KddType == 7 &&
            //     this.viewMode == '1' &&
            //     !comp.base.getCustomArea({
            //         styleId: temp.StyleId,
            //         type: 'kdd',
            //         exCode: temp.Excode,
            //         kddType: temp.KddType,
            //         exId: temp.Exid,
            //     })
            // ) {
            //     // _picSrc = _picSrc.replace(/.png$/, '_edit.png');
            // }
            //放心购快手电子面单快速上线
            const isDynamicFxg = ['fxg', 'ksshop', 'ksshopg', 'ksshop-pre','erp'].includes(comp.Print.Data.platform) && this.templateData.ModeListShow?.dynamicImgSrc
            const backgroundUrl = isDynamicFxg ? _picSrc : comp.base.dealBackgroundUrl(_picSrc);
            style['background-image'] = 'url(' + backgroundUrl + ')';
            style['background-size'] =
                'auto ' + (temp.HeightImg ? temp.HeightImg + 'px' : 'auto');
        } else {
            style['background-color'] = 'white';
        }
        style['font-weight'] = this.globalSetting?.ModeSet?.Isb == 1 ? 'bold' : 'normal';


        // 小标签居中
        // if(_tempData.ModeListShow.Modeid === 'fhd' && _tempData.ModeListShow.KddType === 4){
        if ((this.isSmallTag || this.isThdSmallTag) && !['tmd','dpd'].includes(tmpType)) {
            const exshowid = that.templateData?.ModeTempPrintcfg?.Exshowid;
            const modeId = that.templateData?.ModeListShow?.Modeid;
            let size = localStorage.getItem(`smallTage_${exshowid}`) || 2;
            if(size > 2) size = 2
            if( ['fhd','zbxp'].includes(tmpType) ||['fhd','zbxp'].includes(modeId) )  size = 1
            // style['transform'] = `translate(-50%, -50%) scale(${size})`;
            // style['top'] = '40%';
            // style['left'] = '50%';
            boxStyle['transform'] = `scale(${size})`
        }
        $html.find('.drag_play_ground').css(style);
        $html.find('.drag_ground_box').css(boxStyle);
        //保存


        $dragPlayGround.css(style);

        //对底部附加区域设定样式
        $templateGroundAddition.css({
            width: style.width,
        });

        // 额外附加的底部菜鸟自留区域字样，只有菜鸟快递且是二联且是指定的一批快递才显示
        if (comp.base.getTempStyle('CNkuaidi', styleId) && comp.base.getTempStyle('erlian', styleId) && comp.base.isUseNewCainiaoTemp(Excode)) {
            $templateGroundAddition.css('display', 'block');
        }


        //保存
        $html.find('.finishEditTemplate').on('click', function () {
            let $t = $(this), _service
                , modeList = that.templateData.ModeList || {}
                , Exid = modeList.Exid
                // 兼容处理发货单类型：that.templateData?.ModeSet?.Modeid
                , type = that.templateData?.ModeListShow?.Modeid || that.tmpType
                , _styleId = modeList.StyleId;

            // 抖音、快手、通用平台：如果是二维码，需要判空
            const activeDrag = that.activeDragElement;
            if(activeDrag?.conf?.proArray?.length >= 1 &&
                activeDrag?.conf?.proArray?.[0]?.Dataname === 'ewm' &&
                !activeDrag?.conf?.Str_q) {
                Tatami.showFail('请输入二维码内容后再保存!');
                return;
            }
            if (isSaving) {
                return;
            }
            //先收集数据
            that._gatherDragElementConf();
            if (!that.templateData.ModeListShow.ExcodeName.trim()) {
                Tatami.showFail('模版名称不能为空');
                return;
            }

            // 发货小标签 商品是否合并
            if (type == 'fhd' && Exid == 668) {
                that.templateData.ModeListShow.fhdIsMerge = $html.find('[name=fhdIsMerge]').prop('checked') ? 1 : 0;
            }

			if (['bq', 'bhd', 'thd', 'bkd', 'zbd', 'zbxp', 'bhbq'].includes(type)) {
                const {smallTagSize} = that;
                const exshowid = that.templateData?.ModeTempPrintcfg?.Exshowid;
                smallTagSize && localStorage.setItem(`smallTage_${exshowid}`,smallTagSize);
            }
            // TODO 已不支持修改服务选项，不修改值
            //收集服务参数 菜鸟推荐和 三联模板
            // if (comp.base.getTempStyle('cainiao', _styleId)) {
            //     _service = comp.print.resources.getChooseService($html.find('.service_con .kdd_ctmd_bz_form'), {
            //         excode: modeList.Excode,
            //         excodeName: modeList.Excodename_small,
            //         expressType: modeList.KddType,
            //         expressStyle: modeList.StyleId,
            //     });
            //     if (!_service) {
            //         return;
            //     }
            //     let tjwItem = that.templateData.ModeLogisticsItems?.find(o=> o.serviceCode === 'tjw')
            //     that.templateData.ModeLogisticsItems = !!tjwItem ? [tjwItem,..._service] : _service;
            // }

            if (that.isCloudPrint) {
                //if it's cloud print, then clear background
                that.templateData.ModeListShow.ImgSrc = '';
            }
            if (!that._beforeSaveTemplate(that.templateData)) {
                return;
            }

            // // 拼多多的扫描小标签必要商品唯一码和条形码
            // const isScanTip = that.templateData.ModeList.KddType == 4 && that.templateData.ModeList.Excode.toLowerCase() == 'bhd';

            // const NO_TXM_NUM = JSON.stringify(that.templateData).indexOf('fj_txm_number') == -1;
            // const NO_FJ_NUM = JSON.stringify(that.templateData).indexOf('fj_number') == -1;

            // if(isScanTip && (NO_TXM_NUM || NO_FJ_NUM)){
            //     model({
            //         type: 'confirm',
            //         content:`【商品唯一码】和【商品唯一码条码】为必选数据项，不勾选则无法正常使用小标签`,
            //         okName:'关闭',
            //         cancelHide:true,
            //     });
            //     return;
            // }


            isSaving = true;
            $t.val('保存中...');

            Tatami.pub('Tatami.clickPoint.manualTrigger', {
                point: '11194.11720.12047.12048',
                _fm: '2664',
            });
            if (that.templateData.ModeInputs) {   //保存之前对modeInputs排序
                that.templateData.ModeInputs.sort(function (input1, input2) {
                    return input1.Y_ - input2.Y_;
                });
            }
            outerConf.saveTemplate(
                that.templateData,
                type,
                function () {
                    if (type == 'kdd') {
                        //打点：编辑模板 快递单
                        Tatami.pub('Tatami.clickPoint.manualTrigger', {
                            point: '11194.11195.11236.12123',
                            _fm: '2702',
                        });
                    } else if (type == 'fhd' || type == 'thd') { //退货单也打点
                        //打点：编辑模板 发货单
                        Tatami.pub('Tatami.clickPoint.manualTrigger', {
                            point: '11194.11195.11237.12136',
                            _fm: '2715',
                        });
                    }
                    isSaving = false;
                    $t.val('保存成功');
                    that.$dom.remove();
                    const funcHook = (window.printAPI.compHookObj || {})
                    .afterChangedKddTemp;
                    funcHook && funcHook(that.templateData?.ModeListShow);
                    that.finishCb && that.finishCb(that.templateData);
                },
                function (message) {
                    Tatami.showFail(
                        message
                            ? '保存失败：' + message
                            : '保存失败，请稍后再试或联系客服。',
                    );
                    isSaving = false;
                    $t.val('保存');
                },
            );
        });
        //取消
        $html.find('.cancelEditTemplate').on('click', function () {
            that.$dom.remove();
            that.cancelCb && that.cancelCb();
            Tatami.pub('Tatami.clickPoint.manualTrigger', {
                point: '11194.11720.12047.12049',
                _fm: '2665',
            });
        });

        //监听预览模式下的数据框
        $html.on('click', '.dragElement', function (e) {
            const $target = $(e.target);
            if ($target.find('.wakeUpFInfoSetting').length && that.templateData.ModeListShow.KddType == 3) {
                const now = +new Date();
                const intervalTime = 500; // 两次间隔小于 500ms 视为双击
                if (now - window.lastMouseDownTime < intervalTime) {
                    Tatami.pub('Tatami.clickPoint.manualTrigger', {
                        point: '11194.11195.11236.12123.32400',
                        _fm: '7325',
                    });

                    const taobaoId = Tatami.localcache.get('taobaoId', 'session');
                    Tatami.pub('printBatch.showPrintStyleSetting', taobaoId);
                    window.lastMouseDownTime = null;
                } else {
                    window.lastMouseDownTime = +new Date();
                }
            }
        });

        this.$dom = $html;

        // = '0' 查看状态，= '1' 编辑状态，= '2' 预览状态
        if (this.viewMode == '0' || this.viewMode == '2') {
            this.$dom.find('.template_header,.template_operate,.template_noti').hide();
            temp = {
                width: '100%',
                height: '560px',
            };
            this.$dom.css(temp).find('.template_content').css(temp);

            //temp = this.templateData.ModeList.KddType; 1五联单 2网点电子面单 3菜鸟电子面单
            //普通模版和自由模版可以进行缩放
            this.$dom.find('.template_ground').css({
                'transform-origin': 'top left 0px',
                'transform': 'scaleY(' + (this.globalSetting.ModeSet.SliderH / 100) + ') scaleX(' + (this.globalSetting.ModeSet.SliderW / 100) + ')',
            });

            //非编辑状态不展示模版 并处理相关样式
            return;
        } else {
            /*temp = this.templateData.ModeTempPrintcfg;
            //宽设置为模版长宽，不超过范围内
            var height = temp.Height * pxScale;

            if(height < 500){
                height = 500
            }else if(height > 580){
                height = 580
            }
            this.$dom.find('.template_ground').css('height', height + 'px');
            this.$dom.find('.template_content, .template_operate').css('height', height + 20 + 'px')*/
        }
        //绑定drag事件
        this._bindDragEvent();
        //绑定缩放事件
        this._bindScaleEvent();
        //绑定点击空白复原事件
        this._bindResetEvent();
        //绑定新增拖拽项事件
        this._bindAddDragEvent();
        //绑定右侧操作模版的相关事件
        this._bindOperateEvent();
        //绑定右侧操作面板相关事件
        this._bindSettingEvent();
    };

    //更改itemH，保留老的itemH-->oldH
    //计算Y轴位置

    /**
     *初始化可拖拽元素
     *@params drags {array}
     */
    PrintTemplate.prototype._initDrags = function (dragData) {
        //获得templateData，生成每一个Drag，挂载
        //获得里面的data，然后挨个渲染成drags
        let drags = [],
            i,
            _oldH,
            _newH,
            _$content,
            _removeY,
            _$dom,
            _lastY,
            _lastRemoveY,
            drag;

        for (i = 0; i < dragData.length; i++) {
            const {W_,H_,X_,Y_,InputID} = dragData[i]
            this.gather_drag_position(InputID,{W_,H_,X_,Y_})
            drag = new DragElement(
                dragData[i],
                this.globalSetting,
                '',
                this.isRevise,
                this.mockInputData,
            );
            drag.init(this.tplDefaultItem, this.viewMode, this.mockInputData);
            drags.push(drag);
        }

        if (this.tmpType !== 'fhd' || !this.isRevise) {
            this.dragElements = drags;
            return;
        }

        _$content = $('body'); //用于动态添加body中计算高度
        //修改conf的高度 计算该元素导致的偏移量isRemoveY
        for (i = 0; i < drags.length; i++) {
            drag = drags[i];
            if (drag.dragType == 'tableHtml') {
                _newH = drag.mockInputData.tableHeight;
                drag.isRemoveY = _newH - drag.conf.H_;
                drag.conf.H_ = _newH;
            } else if (drag.isReviseHeight === '1') {
                _oldH = drag.conf.H_;
                _$dom = drag.$dom.appendTo(_$content);
                _newH = _$dom.height();
                _$dom.remove();
                if (_newH > _oldH) {
                    drag.isRemoveY = _newH - _oldH;
                    drag.conf.H_ = _newH;
                } else {
                    drag.$dom.css('height', _oldH);
                }
            } else if (drag.isReviseHeight === '2') {
                drag.isRemoveY = -drag.conf.H_;
                drag.conf.H_ = 0;
            }
        }

        //排序
        drags = outerConf.sortInputArr(drags);

        //修改Y
        _removeY = 0;
        _lastRemoveY = 0;
        for (i = 0; i < drags.length; i++) {
            drag = drags[i];

            if (drag.isRemoveY) {
                //需要累加移动值_removeY的drag 要加上之前存在的 _removeY
                if (_removeY) {
                    if (drag.conf.Y_ > _lastY) {
                        _lastY = drag.conf.Y_; //保留移动之前的Y 便于下面未移动元素对比
                        drag.applySetConf('top', drag.conf.Y_ + _removeY, 'px');
                        _lastRemoveY = _removeY;
                        _removeY += drag.isRemoveY;
                    } else if (drag.conf.Y_ == _lastY) {
                        drag.applySetConf(
                            'top',
                            drag.conf.Y_ + _lastRemoveY,
                            'px',
                        ); //之前上次的移动值
                        if (_lastRemoveY + drag.isRemoveY > _removeY) {
                            _removeY = _lastRemoveY + drag.isRemoveY;
                        }
                    }
                } else {
                    _lastY = drag.conf.Y_;
                    _removeY += drag.isRemoveY;
                }
            } else {
                //无需产生移动
                if (_removeY != 0) {
                    if (drag.conf.Y_ > _lastY) {
                        drag.applySetConf('top', drag.conf.Y_ + _removeY, 'px');
                    } else if (drag.conf.Y_ == _lastY && _lastRemoveY) {
                        drag.applySetConf(
                            'top',
                            drag.conf.Y_ + _lastRemoveY,
                            'px',
                        );
                    }
                }
            }
        }

        this.dragElements = drags;

        //计算处哪一部分是表格，，然后

        // if(this.tmpType !== 'fhd' || !this.isRevise || !tableDrag){
        //     return;
        // }
        // //得知表格的起点和高度
        // var containerH,
        //     actualH,
        //     posY,
        //     tempY,
        //     tempDrag,
        //     revise;

        // containerH = tableDrag.mockInputData.tableItem.H_;
        // actualH = tableDrag.mockInputData.tableHeight;
        // posY = tableDrag.conf.Y_;

        // revise = actualH - containerH;

        // // if(actualH <= containerH){
        // //     //实际高度小于容器高度，无需调整
        // //     return;
        // // }

        // //计算哪些drag在此起点以后，他的y轴增加（表格的真实高度 减去 表格容器的真实高度）
        // for(i = 0; i < this.dragElements.length; i ++){
        //     tempDrag = this.dragElements[i];

        //     tempY = tempDrag.conf.Y_;
        //     if(tempY > posY){
        //         //修改temp 的dom和值，warning: 此数据只供展示和打印，不建议进行数据存储
        //         tempDrag.applySetConf('top', tempY + revise, 'px');
        //     }
        // }
    };

    //重新初始化拖拽元素
    PrintTemplate.prototype._reInitDrags = function (dragData) {
        let tempType = this.templateData?.ModeListShow?.Modeid
        if(tempType === 'kdd') comp.print.resources.creatKddTableHtml( [this.mockInputData],this.templateData, dragData)
        this.templateData.ModeInputs = dragData;
        //清除数据和dom
        for (let i = 0; i < this.dragElements.length; i++) {
            this.dragElements[i].destroy();
        }
        //更新mockInput数据里面的tableHtml
        if(this.isFhd && this.templateData.ModeList.KddType != 4 ){
            outerConf['getFhdTableHtml'](
                [this.mockInputData],
                this.templateData,
            );
        }else if(['cgd','rkd','ckd','thqd','zbxp'].includes(tempType)){
            outerConf['getFhdTableHtml'](
                [this.mockInputData],
                this.templateData,
                tempType,
            );
        }


        this.dragElements = [];
        //重新渲染初始化拖拽和元素合适的
        this._initDrags(dragData);
        //渲染出来可拖拽组件
        this._renderDrags();
    };

    //渲染可拖拽组件
    PrintTemplate.prototype._renderDrags = function () {
        const drags = this.dragElements,
            dragDom = [];

        for (let i = 0; i < drags.length; i++) {
            dragDom.push(drags[i].getDom());
        }

        this.$dom.find('.drag_play_ground').append(dragDom);
        this._doublePermutation(this.$dom);
    };
     // 第一步，收集各个元素的信息
     PrintTemplate.prototype.gather_drag_position = function(id,conf){
        if(conf){
            this.dragPosition[id] = conf
        }else{
            delete this.dragPosition[id]
        }
    }

    /*
    *
    */
    PrintTemplate.prototype.moveChange = function({left=0,top=0,w=0,h=0}){
            // 第三步
            const limit_x = 8; // 显示x轴标注线距离
            const adsorption_x = 5; // x轴吸附距离
            const limit_y = 8; //显示Y轴标准线距离
            const adsorption_y = 3;  // y轴吸附距离

            // 第五步 标准线吸附后，要移动一阵距离才能继续移动  避免鼠标移动惯性让元素超出标准线
            if(this.pauseMove_x){
                    this.record_x += Math.abs(left - this.pre_x) || 0;
                    if(this.record_x <= adsorption_x){
                        return {x:this.pre_x, y:top}
                    }else{
                        this.record_x = 0
                        this.pauseMove_x = false
                    }
            }

            if(this.pauseMove_y){
                this.record_y += Math.abs(top - this.pre_y) || 0
                if(this.record_y <= adsorption_y){
                    return {x:left, y: this.pre_y}
                }else{
                    this.record_y = 0
                    this.pauseMove_y = false
                }
            }

            if(!this.pre_line_x){
                this.hideMoveLine('X')
            }
            if(!this.pre_line_y){
                this.hideMoveLine('Y')
            }
            const pre_x = this.pre_x || 0;
            const pre_y = this.pre_y || 0;

            const {xList,YList} = this;

            // 第四步，判断方向
            if(left-pre_x<0){ // 往左移
                let index = xList.length;
                xList.some((x,i)=>{
                    if(left-x<=0){
                        index = i
                        return true;
                    }
                })
                // 生成标识线
                if(left - xList[index-1]<=limit_x){
                    this.showMoveLine('X',xList[index-1]) ;
                    this.pre_line_x= xList[index-1]
                }else{
                    this.pre_line_x= null
                }
                // 自动吸附
                if(left - xList[index-1]<adsorption_x){
                    left = xList[index-1]
                    this.pauseMove_x = true;
                }
            }else if(left-pre_x>0){ // 往右移
                // 找出最近的元素
                let index = xList.length;
                xList.some((x,i)=>{
                    if((left+w)-x<=0){
                        index = i
                        return true;
                    }
                })
                // 生成标识线
                if( xList[index]-(left+w)<=limit_x){
                    this.showMoveLine('X',xList[index]) ;
                    this.pre_line_x= xList[index]
                }else{
                    this.pre_line_x= null
                }
                // 自动吸附
                if(xList[index]-(left+w)<adsorption_x){
                    left = xList[index]-w
                    this.pauseMove_x = true;
                }
            }
            // 往上移
            if(top-pre_y<0){
                let index = YList.length;
                YList.some((x,i)=>{
                    if(top-x<=0){
                        index = i
                        return true;
                    }
                })
                // 生成标识线
                if(top - YList[index-1]<=limit_y){
                    this.showMoveLine('Y',YList[index-1]) ;
                    this.pre_line_y= YList[index-1]
                }else{
                    this.pre_line_y= null
                }
                // 自动吸附
                if(top - YList[index-1]<adsorption_y){
                    top = YList[index-1]
                    this.pauseMove_y = true;
                }
            }else if(top-pre_y>0){
                // 找出最近的元素
                let index = YList.length;
                YList.some((y,i)=>{
                    if((top+h)-y<=0){
                        index = i
                        return true;
                    }
                })
                // 生成标识线
                if( YList[index]-(top+h)<=limit_y){
                    this.showMoveLine('Y',YList[index]) ;
                    this.pre_line_y= YList[index]
                }else{
                    this.pre_line_y= null
                }
                // 自动吸附
                if(YList[index]-(top+h)<adsorption_y){
                    top = YList[index]-h
                    this.pauseMove_y = true;
                }
            }

            this.pre_x = left;
            this.pre_y = top;
            return {x:left, y:top}
    }
    PrintTemplate.prototype.hideMoveLine = function(direction){
            if(direction){
                this.$dom.find(`.markLine${direction}`).css({opacity: 0})
            }else{
                this.$dom.find(`.markLineX`).css({opacity: 0})
                this.$dom.find(`.markLineY`).css({opacity: 0})
            }
    }
    PrintTemplate.prototype.showMoveLine = function(direction, value){
        const directionMap = {'X':{top:0,left:value+'px',width:'0',height: '100%',transform: 'scaleX(.5)'}, 'Y': {top:value+'px',left:0,width:'100%',height: '0',transform: 'scaleY(.5)'}}
        this.$dom.find(`.markLine${direction}`).css({opacity:0.8,position: 'absolute','z-index':9,...directionMap[direction],border:'1px dashed #B620E0'})
    }
    PrintTemplate.prototype._getAutoLimitInfo = function(){
        let ModeList = this.templateData?.ModeList || {},
            styleId = ModeList.StyleId,
            kddType = ModeList.KddType,
            exCode = ModeList.Excode,
            exId = ModeList.Exid,
            autoLimitInfo;
        const { ModeListShow: { customHeight, customLeft, customTop, customWidth, dynamicImgSrc } } = this.templateData;
         // 如果有dynamicImgSrc || customHeight || customWidth返回，说明是极速上线模式
        const isSpeedOnline = Boolean(dynamicImgSrc || customHeight || customWidth);
        if(isSpeedOnline) {
            autoLimitInfo = {
                topMin: (~~customTop) * pxScale,
                topMax: (~~customHeight + ~~customTop) * pxScale,
                leftMin: (~~customLeft) * pxScale,
                leftMax: (~~customLeft + ~~customWidth) * pxScale,
            };
        }else {
            // 以下为原逻辑，根据模板信息 获取前端写死的模板自定义区域配置
            const kddTypeName = getkddTempName(kddType);
            for (
                let i = 0;
                i < outerConf.templateLimitInfo[kddTypeName].length;
                i++
            ) {
                const item = outerConf.templateLimitInfo[kddTypeName][i];
                if (
                    (item.exCode &&
                        item.exCode == exCode &&   //根据exCode && styleId 判断
                        item.styleId == styleId ) ||
                    (item.exId &&
                        exId == item.exId &&
                        styleId == item.styleId) ||
                    (!item.exCode && !item.exId && item.styleId == styleId)
                ) {
                    autoLimitInfo = item;
                    break;
                }
            }
        }
        return autoLimitInfo;
    }
    //绑定拖拽元素整体拖拽事件
    PrintTemplate.prototype._bindDragEvent = function () {
        //主体拖动
        let startTop = 2,
            startLeft = 2,
            eventX = 0,
            eventY = 0,
            startDrag = false,
            that = this,
            inputId = '',
            $drag,
            dragElement;

        this.$dom
        .find('.drag_play_ground')
        .on('mousedown', '.dragEdit, .dragLine, dragRect', function (e) {
            //'.dragEdit[data-is-draggable=true], .dragLine[data-is-draggable=true]'
            const $target = $(e.target);
            e.preventDefault();
            //上一个拖拽元素停止拖拽
            that._removeActiveDrag(false);

            inputId = $target.attr('data-input-id');
            if (!inputId) {
                $drag = $target.closest('.dragEdit');
                $drag.hasClass('dragHTML') &&
                        (inputId = $drag.attr('data-input-id'));
            }
            if (!inputId) {
                return;
            }
            // 第二步 计算出所有X和Y的位置
            // 所有元素x、y坐标集合
            const xList = [];
            const YList = [];
            Object.keys(that.dragPosition).forEach(id=>{
                // 排除拖拽元素自己。
                if(id == inputId){
                    return;
                }
                const {H_,W_,X_,Y_} = that.dragPosition[id];
                // +1-1是因为没有box-sizing属性，导致对齐后边框叠进元素内
                xList.push(X_);
                xList.push(X_+W_);
                YList.push(Y_);
                YList.push(Y_+H_);
            })
            // 按顺序重新排序
            xList.sort((x1,x2)=>(x1-x2))
            YList.sort((y1,y2)=>(y1-y2))
            that.xList = xList;
            that.YList = YList;
            console.log(xList,YList)
            //寻找dragElement
            dragElement = '';
            for (let i = 0; i < that.dragElements.length; i++) {
                if (that.dragElements[i].conf.InputID == inputId) {
                    dragElement = that.dragElements[i];
                    break;
                }
            }

            if (dragElement) {
                // 暂时只对京广做限制
                // 若配置为false，则不可拖动
                if(['SZKKE'].includes(that.templateData.ModeList.Excode) && !dragElement.conf.IsDraggable) return;

                that._setActiveDrag(dragElement);
                startDrag = true;
                startTop = dragElement.conf.Y_;
                startLeft = dragElement.conf.X_;

                eventX = e.clientX;
                eventY = e.clientY;
            }
            outerConf.handleDataBoxDoubleClick($target, that);
        });
        // 通过接口去获取自定义区域范围
        let autoLimitInfo;
        const { ModeListShow: { customHeight, customLeft, customTop, customWidth } } = that.templateData;
        // 如果有customHeight返回，说明走的是tj发布新模板的流程
        if(customHeight) {
            autoLimitInfo = {
                topMin: (~~customTop) * pxScale,
                topMax: (~~customHeight + ~~customTop) * pxScale,
                leftMin: (~~customLeft) * pxScale,
                leftMax: (~~customLeft + ~~customWidth) * pxScale,
            };
        }

        this.$dom.find('.drag_play_ground').on('mousemove', function (e) {
            let top,
                left,
                dw,
                dh,
                groundWidth,
                groundHeight,
                styleId = that.templateData.ModeList.StyleId,
                kddType = that.templateData.ModeList.KddType,
                exCode = that.templateData.ModeList.Excode,
                exId = that.templateData.ModeList.Exid;

            if (!startDrag) {
                return;
            }

            dw = Number(dragElement.conf.W_);
            dh = Number(dragElement.conf.H_);
            groundWidth = that.templateData.ModeTempPrintcfg.Width * pxScale ;
            groundHeight = that.templateData.ModeTempPrintcfg.Height * pxScale ;

            top = startTop + e.clientY - eventY;
            left = startLeft + e.clientX - eventX;

            function limitedTopAndBottom() {
                // 通过接口去获取自定义区域范围
                if (autoLimitInfo) {
                    top = top > autoLimitInfo.topMin ? top : autoLimitInfo.topMin;

                    if (autoLimitInfo.topMax) {
                        top =
                            top < autoLimitInfo.topMax - dh ? top : autoLimitInfo.topMax - dh;
                    }

                    if (autoLimitInfo.leftMin) {
                        left = left > autoLimitInfo.leftMin ? left : autoLimitInfo.leftMin;
                    } else {
                        left = left > 0 ? left : 0;
                    }

                    if (autoLimitInfo.leftMax) {
                        left =
                            left < autoLimitInfo.leftMax - dw
                                ? left
                                : autoLimitInfo.leftMax - dw;
                    }
                    return [top, left];
                }

                // 以下内容为旧逻辑
                const kddTypeName = getkddTempName(kddType);

                for (
                    let i = 0;
                    i < outerConf.templateLimitInfo[kddTypeName].length;
                    i++
                ) {
                    const item = outerConf.templateLimitInfo[kddTypeName][i];

                    const hit =
                    // 优先匹配 exId, 根据exId styleId判断
                    item.exId ?
                        item.exId == exId && item.styleId == styleId :
                        (item.exCode ?
                            // 根据exCode styleId 判断
                            item.exCode == exCode && item.styleId == styleId :
                            // 只根据styleId判断
                            item.styleId == styleId);

                    if (hit) {
                        top = top > item.topMin ? top : item.topMin;

                        if (item.topMax) {
                            top =
                                top < item.topMax - dh ? top : item.topMax - dh;
                        }

                        if (item.leftMin) {
                            left = left > item.leftMin ? left : item.leftMin;
                        } else {
                            left = left > 0 ? left : 0;
                        }

                        if (item.leftMax) {
                            left =
                                left < item.leftMax - dw
                                    ? left
                                    : item.leftMax - dw;
                        }
                        break;
                    } else {
                        top = top > 2 ? top : 2;
                        left = left > 2 ? left : 2;
                    }
                }

                return [top, left];
            }

            const limitResult = limitedTopAndBottom();

            top = limitResult[0];
            left = limitResult[1];

            if (groundHeight === 0) {
                //发货单自适应高，模板的高度为0，默认给一个容器高度范围为297高 A4纸张
                groundHeight = 297 * pxScale;
            }

            //top最大不能大于父容器高度-自身高度，left最大不能大于父容器宽度-自身宽度
            if (top > (groundHeight - dh - 2)) {
                top = (groundHeight - dh - 2);
            }
            if (left > (groundWidth - dw - 2)) {
                left = (groundWidth - dw - 2);
            }


            //修改drag元素的内部配置, 内部会自动应用到dom中去  第六步
            const {x,y} =  that.moveChange({top,left,inputId,w:dw,h:dh}) || {}
            console.log(x,top,y)
            if(isNaN(parseInt(x)) || isNaN(parseInt(y))){
                return
            }
            startLeft = startLeft + (x - left)
            startTop = startTop + (y - top)
            dragElement.applySetConf('top', y, 'px');
            dragElement.applySetConf('left', x, 'px');
        });

        this.$dom
        .find('.drag_play_ground')
        .on('mouseup mouseleave', function () {
            that.hideMoveLine();
            startDrag = false;
            if (dragElement) {
                dragElement.startDrag(false);
                //将宽高信息更新到右侧输入框里面，防止点击确定按钮恢复到初始状态
                that.$dom
                .find('.w_h[name=W_]')
                .val((+dragElement.conf.W_).toFixed(0));
                that.$dom
                .find('.w_h[name=H_]')
                .val((+dragElement.conf.H_).toFixed(0));
                //更新下方状态栏
                that._genDragNoti(dragElement);
            }
        });

        //上下左右键微调
        this.$dom.find('.drag_play_ground').on('keyup', function (e) {
            const keyCode = e.keyCode; // 37 38 39 40

            let top = 0,
                left = 0,
                dw,
                dh,
                dge = that.activeDragElement,
                groundWidth,
                groundHeight,
                styleId = that.templateData.ModeList.StyleId,
                kddType = that.templateData.ModeList.KddType,
                exCode = that.templateData.ModeList.Excode,
                exId = that.templateData.ModeList.Exid;

            if (
                (keyCode != 37 &&
                    keyCode != 38 &&
                    keyCode != 39 &&
                    keyCode != 40) ||
                !dge
            ) {
                return;
            }
            e.preventDefault();

            //38 40 是top
            if (keyCode == 38 || keyCode == 40) {
                top = keyCode == 38 ? -1 : 1;
            }
            //37 39 是left
            if (keyCode == 37 || keyCode == 39) {
                left = keyCode == 37 ? -1 : 1;
            }

            dw = dge.conf.W_;
            dh = dge.conf.H_;
            groundWidth = that.templateData.ModeTempPrintcfg.Width * pxScale;
            groundHeight = that.templateData.ModeTempPrintcfg.Height * pxScale;

            top = dge.conf.Y_ + top;
            left = dge.conf.X_ + left;

            const kddTypeName = getkddTempName(kddType);

            for (
                let i = 0;
                i < outerConf.templateLimitInfo[kddTypeName].length;
                i++
            ) {
                const item = outerConf.templateLimitInfo[kddTypeName][i];

                if (
                    (item.exCode &&
                        exCode == item.exCode &&  //根据exCode && styleId 判断
                        styleId == item.styleId ) ||
                    (item.exId &&
                        exId == item.exId &&
                        styleId == item.styleId) ||
                    (!item.exCode && !item.exId && item.styleId == styleId)
                ) {
                    top = top > item.topMin ? top : item.topMin;

                    if (item.topMax) {
                        top = top < item.topMax - dh ? top : item.topMax - dh;
                    }

                    if (item.leftMin) {
                        left = left > item.leftMin ? left : item.leftMin;
                    } else {
                        left = left > 0 ? left : 0;
                    }

                    if (item.leftMax) {
                        left =
                            left < item.leftMax - dw ? left : item.leftMax - dw;
                    }
                    break;
                } else {
                    top = top > 0 ? top : 0;
                    left = left > 0 ? left : 0;
                }
            }

            //top最大不能大于父容器高度-自身高度，left最大不能大于父容器宽度-自身宽度
            if (top > groundHeight - dh -2) {
                top = groundHeight - dh -2;
            }
            if (left > groundWidth - dw -2) {
                left = groundWidth - dw -2;
            }

            //修改drag元素的内部配置, 内部会自动应用到dom中去
            dge.applySetConf('top', top, 'px');
            dge.applySetConf('left', left, 'px');
        });
    };

    //绑定拖拽元素缩放事件
    PrintTemplate.prototype._bindScaleEvent = function () {

        var dragElement,
            eventX = 0, eventY = 0,
            startScale = false,
            scaleType = '',
            that = this,
            elementMinest = 5,      // 数据项最小尺寸值
            groundWidth = this.templateData.ModeTempPrintcfg.Width * pxScale,   // 模板宽高
            groundHeight = this.templateData.ModeTempPrintcfg.Height * pxScale;

        this.$dom.find('.drag_play_ground').on('mousedown', '.dragTrigger', function (e) {
            var $target = $(this);
            e.preventDefault();
            scaleType = $target.attr('data-scale-type');

            dragElement = that.activeDragElement;

            startScale = true;
            eventX = e.clientX;
            eventY = e.clientY;

            e.stopPropagation();
        });

        // 获取自定义区域范围
        let autoLimitInfo = this._getAutoLimitInfo();

        this.$dom.find('.drag_play_ground').on('mousemove', function (e) {

            var dw, dh, dT, dL,
                width, height, top, left,
                topMin = 2,
                topMax = groundHeight - 4,
                heightMax = groundHeight,
                widthMax = groundWidth - 4 , // 保留部分安全拖拽距离
                leftMin = 2,
                leftMax = groundWidth - 4,
                kddType, styleId, exCode, exId;
            if (!startScale) {
                return;
            }

            // scaleType = $(this).attr('data-scale-type');
            dw = getElementLimitedData({width:+ dragElement.conf.W_ ,resVal:'width'}) ;
            dh = getElementLimitedData({height:+ dragElement.conf.H_ ,resVal:'height'}) ;
            dT = getElementLimitedData({top:+ dragElement.conf.Y_ ,resVal:'top'}) ;
            dL = getElementLimitedData({left:+ dragElement.conf.X_ ,resVal:'left'}) ;
            // dh = + dragElement.conf.H_;
            // dT = + dragElement.conf.Y_;
            // dL = + dragElement.conf.X_;
            const inputId =  dragElement.conf.InputID;

            styleId = that.templateData.ModeList.StyleId
            kddType = that.templateData.ModeList.KddType
            exCode = that.templateData.ModeList.Excode
            exId = that.templateData.ModeList.Exid

            if(autoLimitInfo){
                topMin = autoLimitInfo.topMin || 0;

                leftMin = autoLimitInfo.leftMin || 0;

                if(autoLimitInfo.topMax){
                    topMax = autoLimitInfo.topMax;
                    heightMax = Math.max(autoLimitInfo.topMax - (dT - eventY + e.clientY), elementMinest);
                }
                if(autoLimitInfo.leftMax){
                    leftMax = autoLimitInfo.leftMax;
                    widthMax = Math.max(autoLimitInfo.leftMax - (dL - eventX + e.clientX), elementMinest);
                }
            }

            // 根据用户鼠标操作所得的位置或元素大小参数 和 自定义区域的操作限制区域，得出最终设置数据项的参数值
            function getElementLimitedData({width, height, top, left,resVal}){
                let limitedData = {};
                typeof width == 'number' && (limitedData.width =  Math.min(Math.max(width, elementMinest), widthMax));
                typeof height == 'number' && (limitedData.height =  Math.min(Math.max(height, elementMinest), heightMax));
                typeof top == 'number' && (limitedData.top =  Math.max(Math.min(top, topMax), topMin));
                typeof left == 'number' && (limitedData.left = Math.min(Math.max(left, leftMin), leftMax));
                return resVal ? limitedData[resVal] : limitedData;
            }

            if (scaleType === 'bottom') {
                height = dh + e.clientY - eventY;
                if (height > heightMax) {
                    height = heightMax
                }
                let {height:h} = getElementLimitedData({height})
                height = h;
                const {y} = that.moveChange({top:dT+height,inputId})
                dragElement.applySetConf('height',y-dT, 'px');
            } else if (scaleType === 'right') {
                width = dw + e.clientX - eventX;
                console.log(width);
                if (width > (widthMax)) {
                    width = widthMax;
                }
                // 第七步
                let {width:w} =  getElementLimitedData({width});
                width = w;
                const {x} = that.moveChange({left:dL+width})
                dragElement.applySetConf('width',x-dL, 'px');
            } else if (scaleType === 'top') {
                height = dh + eventY - e.clientY;
                top = dT - eventY + e.clientY;
                if (top < topMin) {
                    top = topMin;
                    height = dh;
                }
                const {top:t,height:h} = getElementLimitedData({top, height});
                const {y} = that.moveChange({top:t,inputId})
                dragElement.applySetConf('top', y, 'px');   // 数据项不能超出topMax
                dragElement.applySetConf('height', h+(t-y), 'px');  // 限制数据项高度不能小于最小尺寸
            } else if (scaleType === 'left') {
                left = dL - eventX + e.clientX;
                width = dw - e.clientX + eventX;
                if (width > widthMax) {
                    width = widthMax
                }
                const {width:w,left:l} = getElementLimitedData({width, left});
                const {x} = that.moveChange({left:l,inputId})
                dragElement.applySetConf('width', w+(l-x), 'px');
                dragElement.applySetConf('left', x, 'px');
            } else if (scaleType === 'bottomLeft') {
                left = dL - eventX + e.clientX;
                width = dw - e.clientX + eventX;
                height = dh + e.clientY - eventY;
                if (height > heightMax) {
                    height = heightMax
                }
                const {width:w, height:h, left:l }= getElementLimitedData({width, height, left});
                const {x,y} = that.moveChange({left:l,top:dT+h,inputId})
                dragElement.applySetConf('height', y-dT, 'px');
                dragElement.applySetConf('width', w+(l-x), 'px');
                dragElement.applySetConf('left', x, 'px');
            } else if (scaleType === 'bottomRight') {
                left = dL - eventX + e.clientX;
                height = dh + e.clientY - eventY;
                width = dw + e.clientX - eventX;
                if (height > heightMax) {
                    height = heightMax
                }
                if (width > widthMax) {
                    width = widthMax
                }
                const {width:w, height:h }= getElementLimitedData({width, height});
                const {x,y} = that.moveChange({left:dL+w,top:dT+h,inputId})
                dragElement.applySetConf('width', x-dL, 'px');
                dragElement.applySetConf('height', y-dT, 'px');
            } else if (scaleType === 'topLeft') {
                left = dL - eventX + e.clientX;
                top = dT - eventY + e.clientY;
                width = dw - e.clientX + eventX;
                height = dh + eventY - e.clientY;

                if (top < topMin) {
                    top = topMin;
                    height = dh;
                }

                const {width:w, height:h ,top:t, left:l} = getElementLimitedData({width, height, top, left});
                const {x,y} = that.moveChange({left:l,top:t,inputId})

                dragElement.applySetConf('top', t, 'px');
                dragElement.applySetConf('left', l, 'px');
                dragElement.applySetConf('height', h+(t-y), 'px');
                dragElement.applySetConf('width', w+(l-x), 'px');

            } else if (scaleType === 'topRight') {
                height = dh + eventY - e.clientY;
                width = dw + e.clientX - eventX;
                top = dT - eventY + e.clientY;

                if (top < topMin) {
                    top = topMin;
                    height = dh;
                }
                if (width > widthMax) {
                    width = widthMax;
                }

                const {width:w, height:h ,top:t} = getElementLimitedData({width, height, top});
                const {x,y} = that.moveChange({left:dL+w,top:t,inputId})

                dragElement.applySetConf('height', h+(t-y), 'px');
                dragElement.applySetConf('top',  t, 'px');
                dragElement.applySetConf('width', x-dL, 'px');

            }

            //对于缩放的处理，每次mousemove更新最新的坐标点，防止累加
            eventX = e.clientX;
            eventY = e.clientY;
            const  table = dragElement.getDom().find('.tempTable')
            // 待处理left保存、 以及不允许更改高度
            if(table.length && width){
                that._tableWidthChange(table,width,left)
            }
        });

        this.$dom.find('.drag_play_ground').on('mouseup mouseleave', function () {
            var temp = '';
            startScale = false;

            if (!that.activeDragElement) {
                return;
            }

            temp = that.activeDragElement;
            if (temp.conf.proArray.length == 1) {
                temp = temp.conf.proArray[0].Dataname;

                if (temp == 'ewm_number' || temp == 'ewm' || temp == 'ewm_str') {
                    temp = that.activeDragElement.conf;
                    temp = temp.W_ > temp.H_ ? temp.H_ : temp.W_;
                    that.activeDragElement.applySetConf('height', Math.abs(temp), 'px');
                    that.activeDragElement.applySetConf('width', Math.abs(temp), 'px');
                }
            }
        });

    };

    //绑定点击空白重置事件
    PrintTemplate.prototype._bindResetEvent = function () {
        const that = this;
        this.$dom.find('.drag_play_ground').on('click', function (e) {
            const type = $(e.target).attr('data-type');

            if (type !== 'drag') {
                that._removeActiveDrag();
            }
        });
    };
    PrintTemplate.prototype._deleteTable = function(){
        var that = this, $dom = this.$dom;
        var $tableDom = $dom.find('.tempTable');
        if ($tableDom.length) {
            for(let i = that.dragElements.length-1; i>=0; i--){
                const drag = that.dragElements[i];
                const {Dataname} = drag.conf.proArray[0] || {}
                if(Dataname?.includes('table')){
                    const {InputID} = drag.conf;
                    that._removeDragElement(InputID,false);
                }
            }
            $tableDom.closest('.dragElement').remove()
        }
    };
    PrintTemplate.prototype._tableWidthChange = function(table,width,left){
        const pre_width = table.width()
        const title_td = $(table.find('tr')[0]).find('td')
        const diffW = width - pre_width
        const map = {}
        this.dragElements.forEach(item=>{
            const obj = item.conf.proArray[0] || {};
            const dataName = obj.Dataname?.match(/table_r_(\d+)/)
            if(dataName&&dataName[1]){
                const i = dataName[1]
                const td = title_td[i]
                const tdW = $(td).attr('width').match(/\d+/)[0]-0;
                let diffTdW =tdW +  Math.round((tdW/pre_width)*diffW)
                diffTdW = diffTdW<30?30:diffTdW
                $(td).attr('width',diffTdW)
                obj.ProValue = obj.ProValue.replace(/rgW=[\d,.]+/,'rgW='+tdW)
            }
        })
        table.css('width',width)
        title_td.map((i,td)=>{
            const tdW = $(td).attr('width').match(/\d+/)[0]-0;
            const diffTdW =tdW +  Math.round((tdW/pre_width)*diffW)
            $(td).attr('width',diffTdW<30?30:diffTdW)
        })
    }
    //绑定右侧模板设置信息
    PrintTemplate.prototype._bindSettingEvent = function () {
        const that = this,
            $dom = this.$dom;
        $dom.find('.template_operate')
        .on('click', 'input[name=applyTextBox]', function () {
            let temp, activeDrag;
            activeDrag = that.activeDragElement;
            if (!activeDrag) {
                return;
            }
            temp = that.$dom.find('[name=Fontname]').val();
            activeDrag.applySetConf('font-family', temp);
            temp = that.$dom.find('[name=Fontsize]').val();
            activeDrag.applySetConf('font-size', temp);
            temp = that.$dom.find('[name=Isb_n]').val();
            activeDrag.applySetConf('font-weight', temp);
            temp = that.$dom.find('[name=Zjj]').val();
            activeDrag.applySetConf('letter-spacing', temp);
            temp = that.$dom.find('[name=Hjj]').val();
            activeDrag.applySetConf('line-height', temp);
            temp = that.$dom.find('[name=Str_q]').val();
            activeDrag.applySetConf('Str_q', temp);
            temp = that.$dom.find('[name=W_]').val();
            activeDrag.applySetConf('width', temp, 'px');
            temp = that.$dom.find('[name=H_]').val();
            activeDrag.applySetConf('height', temp, 'px');

            //并且取消active，面板还原
            that._removeActiveDrag();
        })
        .on(
            'click',
            'input[name=removeDataBox], input[name=removeTextBox], input[name=removeLine], input[name=removeRect], input[name=removeSet], input[name=deleteImg], input[name=deleteTable]',
            function () {
                //移除文本框，移除线条，移除图片
                const $this = $(this);
                let inputId = $this.attr('data-input-id');
                if ($this.attr('name') == 'deleteTable') {
                    //如果是删除表格
                    const $tableDom = $dom.find('.tempTable');
                    inputId = $tableDom.parent().attr('data-input-id');
                    $dom.find('input[name="editTable"]')
                    .attr('disabled', 'disabled')
                    .css('cursor', 'not-allowed'); //禁止编辑表格
                    if ($tableDom.length) {
                        that._removeDragElement(inputId);
                        $tableDom.closest('.dragElement').remove();
                    } else {
                        message =
                                '当前模板不存在表格，请确认后操作！\r\n可以使用【恢复默认布局】恢复表格';
                        alert(message);
                    }
                } else {
                    that._removeDragElement(inputId);
                }
            },
        )
        .on('click', 'input[name=applyLine]', function () {
            //应用线的规则 fangxiang changdu xiantiaoleixing
            let temp, activeDrag;
            activeDrag = that.activeDragElement;
            if (!activeDrag) {
                return;
            }
            temp = that.$dom.find('[name=fangxiang]').val();
            activeDrag.applySetConf('fangxiang', temp);
            temp = that.$dom.find('[name=changdu]').val();
            activeDrag.applySetConf('changdu', temp);
            temp = that.$dom.find('[name=xiantiaoleixing]').val();
            activeDrag.applySetConf('xiantiaoleixing', temp);

            that._removeActiveDrag();
        })
        .on('click', 'input[name=applyRect]', function () {
            //应用线的规则 fangxiang changdu xiantiaoleixing
            let temp, activeDrag;
            activeDrag = that.activeDragElement;
            if (!activeDrag) {
                return;
            }

            temp = that.$dom.find('[name=W_]').val();
            activeDrag.applySetConf('width', temp, 'px');
            temp = that.$dom.find('[name=H_]').val();
            activeDrag.applySetConf('height', temp, 'px');
            temp = that.$dom.find('[name=xiantiaoleixing]').val();
            activeDrag.applySetConf('xiantiaoleixing', temp);

            that._removeActiveDrag();
        })
        .on('click', 'input[name=applyDataBox]', function () {
            //数据项的应用
            let temp, activeDrag;
            activeDrag = that.activeDragElement;
            if (!activeDrag) {
                return;
            }
            // 抖音、快手、通用平台：如果是二维码，需要判空
            if(activeDrag?.conf?.proArray?.length >= 1 &&
                activeDrag?.conf?.proArray?.[0]?.Dataname === 'ewm' &&
                !activeDrag?.conf?.Str_q) {
                Tatami.showFail('请输入二维码内容后再保存!');
                return;
            }
            temp = that.$dom.find('[name=Fontname]').val();
            activeDrag.applySetConf('font-family', temp);
            temp = that.$dom.find('[name=Fontsize]').val();
            activeDrag.applySetConf('font-size', temp);
            temp = that.$dom.find('[name=Isb_n]').val();
            activeDrag.applySetConf('font-weight', temp);
            temp = that.$dom.find('[name=Zjj]').val();
            activeDrag.applySetConf('letter-spacing', temp);
            temp = that.$dom.find('[name=Hjj]').val();
            activeDrag.applySetConf('line-height', temp);
            //如果是二维码，则不应用Str_q规则，自定义二维码内容存储在Str_q中
            if (
                activeDrag.conf.proArray.length >= 1 &&
                    activeDrag.conf.proArray[0].Dataname != 'ewm' &&
                    activeDrag.conf.proArray[0].Dataname != 'ewm_str'
            ) {
                temp = that.$dom.find('[name=Str_q]').val();
                activeDrag.applySetConf('Str_q', temp);
            }

            temp = that.$dom.find('[name=Str_h]').val();
            activeDrag.applySetConf('Str_h', temp);
            temp = that.$dom.find('[name=W_]').val();
            activeDrag.applySetConf('width', temp, 'px');
            temp = that.$dom.find('[name=H_]').val();
            activeDrag.applySetConf('height', temp, 'px');
            console.log(activeDrag, '---activeDrag');
            console.log(temp,'--temp');
            //并且取消active，面板还原
            that._removeActiveDrag();
        })
        .on('change', '.databox_show input:radio', function () {
            // 判断是否显示买家备注,卖家留言,友情提示
            const activeDrag = that.activeDragElement;
            if (!activeDrag) {
                return;
            }
            $(this)
            .closest('.databox_show')
            .find('label.on')
            .removeClass('on');
            $(this).parent().addClass('on');
            activeDrag.applySetConfPro(
                '_nu',
                $(this).val() == 1 ? '' : '0',
            );
        });

        $dom.on('keyup', '.dragEdit:focus', function (e) {
            if (e.keyCode == 8 || e.keyCode == 46) {
                // delete 键
                const inputId = $(this).attr('data-input-id');
                that._removeDragElement(inputId);
            }
        });

        $dom.on('click', '.deleteIcon', function (e) {
            const inputId = $(this)
            .closest('.dragElement')
            .find('.dragEdit')
            .attr('data-input-id');
            that._removeDragElement(inputId);
        });
    };

    //绑定表格高度改变，自动调整其他元素的事件
    PrintTemplate.prototype._bindAutoChangeHeightEvent = function () {
        let that = this,
            $dom = this.$dom,
            needReGetHeight = true;
        let tableHeight = 0,
            changeHeight = 0, // 规定向下为正方向，表格变高 changeHeight 为正，变矮为负
            tableId;
        const $tableDragHTML = $dom.find('.tempTable').closest('.dragHTML');
        if ($tableDragHTML.attr('contenteditable')) {
            $dom.find('.tempTable')
            .parent()
            .on('click', function () {
                const $table = $(this).find('table');
                tableId = $tableDragHTML.attr('data-input-id');
                if ($tableDragHTML.attr('contenteditable')) {
                    if (needReGetHeight) {
                        //如果table 没有被 focus
                        tableHeight = $table.height();
                        needReGetHeight = false;
                    }
                }
            })
            .on(
                'keyup',
                throttle(function () {
                    const $table = $(this).find('table');
                    changeHeight = $table.height() - tableHeight;
                    if (changeHeight) {
                        $.each($dom.find('.dragElement'), function (
                            index,
                            el,
                        ) {
                            const $el = $(el),
                                currentTop = parseInt($el.css('top'), 10);
                            if (
                                currentTop >
                                    parseInt(
                                        $table
                                        .closest('.dragElement')
                                        .css('top'),
                                        10,
                                    )
                            ) {
                                // 如果该元素低于表格，则会受到影响
                                $el.css(
                                    'top',
                                    currentTop + changeHeight + 'px',
                                ); //调整页面上被影响的元素的高度
                                const inputId = $el
                                .find('.dragEdit')
                                .attr('data-input-id');

                                $.each(
                                    that.templateData.ModeInputs,
                                    function (index, item) {
                                        //手动修改因为表格变化导致高度变化的元素的打印高度（Y_）
                                        if (inputId == item.InputID) {
                                            item.Y_ += changeHeight;
                                        }
                                    },
                                );
                            }
                        });
                        $.each(that.templateData.ModeInputs, function (
                            index,
                            item,
                        ) {
                            // 修改表格的Y_
                            if (tableId == item.InputID) {
                                item.H_ += changeHeight;
                            }
                        });
                        $table
                        .closest('.dragElement')
                        .css('height', $table.height() + 6); //6px 是容器高度和实际高度的差值，为保证视觉的一致增加 6px
                        tableHeight = $table.height();
                    }
                }, 500),
            )
            .on('blur', function () {
                needReGetHeight = true;
            });
        }
    };

    /**
     * 收集此模版下所有拖拽元素的数据，通过拖拽元素本身方法实现，
     * 模板中的数据项，新增和删除都是改的drags，只有修改是直接修改了templateData，此处再收集一次
     * @private
     */
    PrintTemplate.prototype._gatherDragElementConf = function (isNotSave) {
        const drags = this.dragElements,
            conf = [];

        for (let i = 0; i < drags.length; i++) {
            conf.push(drags[i]._gatherConf());
        }

        !isNotSave && (this.templateData.ModeInputs = conf);
        return conf;
    };

    PrintTemplate.prototype._setDataInputIsShow = function (
        $domSetShow,
        isShow,
        ChooseVal,
        txt,
    ) {
        if (isShow) {
            $domSetShow
            .show()
            .find('span:eq(0)')
            .text('无' + txt + '时是否显示');
            $domSetShow.find('labels').removeClass('on');
            $domSetShow
            .find('input[name=_nu][value=' + ChooseVal + ']')
            .prop('checked', true)
            .parent()
            .addClass('on');
        } else {
            $domSetShow.hide();
        }
    };

    //展示拖拽元素设定  并勾选中对应的勾选框，生成对应的内容
    PrintTemplate.prototype._showDragSet = function (dragConf, dragType) {
        let $dom = this.$dom,
            html = '',
            $temp,
            that = this,
            _len,
            _$t,
            _obj,
            _flag,
            kddType,
            editFh;

        kddType = this.templateData.ModeListShow.KddType;
        //隐藏操作面板
        $dom.find('.operate_panel').hide();
        //移除其他拖拽元素的面板
        $dom.find('.drag_setting_panel').remove();
        //设置处理，生成右侧面板，并勾选中对应的勾选框，生成对应的内容
        if (dragType === 'dataInput' || dragType === 'icon') {
            _len = dragConf.proArray.length;
            // 添加拖拽数据元素配套设置信息
            // 解决小标签的侧边数据框展示
            html = $(
                htmlStore.getNewDataDragSet(this.tplDefaultItem, dragConf, {
                    isFhd: this.isFhd,
                    kddType: kddType,
                    fhdType: this.fhdType,
                    tmpType: this.tmpType,
                    onlyLodopPrint: this.onlyLodopPrint,
                    isSmallTag: this.isSmallTag,
                    isThdSmallTag: this.isThdSmallTag,
                    dragType, // 加一个 dragType ，后续在数据框选中，不显示二维码下拉框。
                }),
            );

            // 若Isedit为0，则不可编辑
            // 暂时只对京广做限制
            if(['SZKKE'].includes(that.templateData.ModeList.Excode) && dragConf.Isedit === 0) return;

            //checkbox
            $temp = html.find('.dragDataItem');
            $(dragConf.proArray).each(function () {
                let currentDom = $temp
                .filter(
                    '[data-key="' +
                            (this.Dataname == 'ewm_str'
                                ? 'ewm'
                                : this.Dataname) +
                            '"]',
                )
                .prop('checked', true)
                _$t =currentDom
                .next()
                .css('color', '#fff');
                if (this.Dataname == 'f_info') {
                    //展示发货内容提示信息
                    html.find('.f_info_noti').show();
                }
            });
            if (
                _len == 1 &&
                (dragConf.proArray[0].Dataname == 'ewm' ||
                    dragConf.proArray[0].Dataname == 'ewm_str')
            ) {
                html.find('.ewm_input').show().val(dragConf.Str_q);
                //清空前文字
                html.find('.item_blur[name=Str_q]').val('');
            } else {
                html.find('.ewm_input').hide();
            }

            if (
                that.isFhd &&
                _len == 1 &&
                /^(s_message|f_memo|o_info|mjs)$/.test(
                    dragConf.proArray[0].Dataname,
                )
            ) {
                _obj = outerConf.getObjectFormProValue(
                    dragConf.proArray[0].ProValue,
                );
                this._setDataInputIsShow(
                    html.find('.databox_show'),
                    true,
                    _obj._nu || _obj._nu === 0 ? '0' : '1',
                    _$t.text(),
                );
                _flag = true;
            } else if (that.isFhd) {
                this._setDataInputIsShow(html.find('.databox_show'), false);
            }
        } else if (dragType === 'textInput') {
            html = htmlStore.getNewTextDragSet(dragConf, kddType,this.isFhd,this.tmpType);
            //添加拖拽文字元素配套设置信息
        } else if (dragType === 'line') {
            //fixme 重构代码结构
            html = htmlStore.getNewLineDragSet(dragConf, kddType);
        } else if (dragType === 'image') {
            html = htmlStore.getNewImgSet(dragConf);
        } else if (dragType === 'rect') {
            html = htmlStore.getNewRectDragSet(dragConf);
        }

        html = $(html);

        if (dragType === 'dataInput' || dragType === 'textInput') {
            //select归置
            if (dragConf.Fontname) {
                html.find('select[name=FontName]').val(dragConf.Fontname);
            }
            html.find('select[name=Fontsize]').val(dragConf.Fontsize);
            html.find('select[name=Isb_n]').val(dragConf.Isb_n);
            html.find('select[name=Zjj]').val(dragConf.Zjj || -1);
            html.find('select[name=Hjj]').val(dragConf.Hjj || -1);
        } else if (dragType == 'image') {
            //上传新底图
            outerConf.uploadEleImg(
                html.find('.uploadEleImgCon'),
                'uploadcustomimg',
                {
                    modeListShowId:
                        that.templateData.ModeListShow.Mode_ListShowId,
                    userId: that.templateData.ModeListShow.Exuserid,
                },
                function (imgUrl) {
                    const activeDrag = that.activeDragElement;
                    if (!activeDrag) {
                        return;
                    }
                    activeDrag.applySetConf('Str_q', imgUrl);
                },
                function () {
                    alert('图片上传失败');
                },
            );
        }
        //编辑发货内容
        html.find('#editFh').on('click',function (){
            Tatami.pub('openFhSet')
            Tatami.pub('Tatami.clickPoint.manualTrigger', {
                point: '11194.11195.11236.12123.32399',
                _fm: '7324',
            });
        })
		//编辑发货内容
		html.find('#editShopInfo').on('click', async function () {
			const currentData = {
				goodsName: '',
				goodsCode: '',
				goodsQuantity: '',
				goodsSpec: '',
				showOptions: ['goodsName', 'goodsQuantity']
			};
			try {
				// 使用商品信息编辑弹窗组件
				const result = await new Promise((resolve) => {
					showShopInfoEditModal({
						initialData: currentData,
						onOk: (data) => {
							resolve(data);
						}
					});
				});

				// 处理用户确认后的操作
				console.log('用户确认的数据:', result);

			} catch (error) {
				console.error('打开商品信息编辑弹窗失败:', error);
			}
		})
        //面板上输入框值增减控制
        html.find('.arrow-down,.arrow-up').on('click', function () {
            let $target = $(this),
                _$input = $target.closest('.controlls-input').find('input'),
                _isAdd = $target.hasClass('arrow-up') ? 1 : -1,
                _size = _isAdd * (_$input.attr('data-gains') || 1),
                _min = +_$input.attr('data-min'),
                _max = _$input.attr('data-max'),
                _oldVal = +_$input.val(),
                name = _$input.attr('name')
            if(name === 'alpha' && that?.templateData?.ModeListShow?.KddType == 9){
                _size = _size * 10
            }
             let _newVal = _oldVal + _size;
            if ((_max && _newVal > _max) || _newVal < _min) {
                _newVal = _oldVal;
            } else {
                _newVal = Math.round(_newVal * 100) / 100; //最多保留两位小数
            }
            _$input.val(_newVal).keyup();
        });
        // 黑底白字选择框
        html.find('.black_white_check').on('change',function(){
            let val = this.checked,
                activeDrag = that.activeDragElement;
            let dataName = activeDrag.conf.proArray[0]?.Dataname
            if(['txm','ewm'].includes(activeDrag.conf.scripConvertCode) || /image/.test(dataName)) return
            if(val){
                activeDrag.applySetConf('bColor', 'black');
                activeDrag.applySetConf('color', 'white');
                activeDrag.conf.style = 'BLACK_WHITE'
            }else{
                activeDrag.applySetConf('bColor', 'white');
                activeDrag.applySetConf('color', 'black');
                activeDrag.conf.style = 'DEFAULT'
            }
        })
        html.find('.item_wrap_change').on('change',function(){
            let val = this.value,
            activeDrag = that.activeDragElement;
            activeDrag.conf.hideWrap = (val == '1' ? 1 : 0)
        })
        //每个drag对应的面板上的输入框和下拉框
        html.find('.item_blur').on('keyup', function () {
            //宽 高 前文字 后文字
            const name = $(this).attr('name')
            let val = $(this).val(),
                activeDrag = that.activeDragElement;
            if(name === 'alpha' && that?.templateData?.ModeListShow?.KddType == 9){
                val = val?.replace('.','')
                 $(this).val(val)
            }
            if (name == 'W_') {
                activeDrag.applySetConf('width', val, 'px');
            } else if (name == 'H_') {
                activeDrag.applySetConf('height', val, 'px');
            } else if (name == 'Zjj') {
                activeDrag.applySetConf('letter-spacing', val);
            } else if (name == 'Hjj') {
                activeDrag.applySetConf('line-height', val);
			} else if (name == 'deg') {
				activeDrag.applySetConf('deg', val, 'deg');
			} else if (
                name == 'Str_q' ||
                name == 'Str_h' ||
                name == 'changdu'
            ) {
                activeDrag.applySetConf(name, val);
            }else if(  /^(alpha)$/.test(name) ){
                activeDrag.applySetConf(name, val);
            }
        });
        // NOTE 切换数据项设置
        html.find('.item_change').on('change', function (e) {
            //字体 字号 是否加粗 字间距 行间距
            let name = $(this).attr('name'),
                val = $(this).val(),
                activeDrag = that.activeDragElement;
            if(/image/.test(activeDrag.conf.proArray[0]?.Dataname)) return
            if (name == 'Fontname') {
                activeDrag.applySetConf('font-family', val);
            } else if (name == 'Fontsize') {
                activeDrag.applySetConf('font-size', val);
            } else if (name == 'Isb_n') {
                activeDrag.applySetConf('font-weight', val);
            } else if (name == 'Zjj') {
                activeDrag.applySetConf('letter-spacing', val);
            } else if (name == 'Hjj') {
                activeDrag.applySetConf('line-height', val);
            } else if (name == 'fangxiang' || name == 'xiantiaoleixing') {
                activeDrag.applySetConf(name, val);
            } else if (name == 'IsBorder') {
                activeDrag.applySetConf('border-width', val);
            } else if (name == 'alpha') {
                activeDrag.applySetConf(name, val);
            }else if (name == 'italic' || name == 'under') {
                val = $(this).is(":checked") ? 1 : 0;
                activeDrag.applySetConf(name, val);
            } else if(name == 'direct'){
                activeDrag.applySetConf(name, val);
                // $dom.find('.w_h[name=W_]').val((+activeDrag.conf.W_).toFixed(0));
                // $dom.find('.w_h[name=H_]').val((+activeDrag.conf.H_).toFixed(0));
            }else if (/^(fangxiang|xiantiaoleixing|blackWhite|bColor|align|valign|direct)$/.test(name)) {
                activeDrag.applySetConf(name, val);
            }else if(name == 'dataType'){
                if(activeDrag.conf.proArray.length > 1) {
                    // e.preventDefualt()
                    $(this).val("txt");
                    Tatami.showNoti('二维码或条形码不支持同时勾选多个数据项')
                    return
                }
				let rotateSet = html.find('.rotate-set')
                activeDrag.conf.scripConvertCode = val
                if(val === 'txm' || val ==='ewm'){
					$(rotateSet).hide()
					$(rotateSet).find('input[name="deg"]').val(0)
					activeDrag.conf.deg = 0

                    // activeDrag.$dom.find('.dragEdit').css('opacity','0')
                    activeDrag.applySetConf('color', 'rgba(0,0,0,0)');
					activeDrag.applySetConf('deg', 0);
                    activeDrag.applySetConf('background', `url(https://static.kuaidizs.cn/resources/img/templateEdit/${val === 'txm' ? 'barcode_h' : 'qrcode'}.png) no-repeat`);
                    activeDrag.applySetConf('background-size', '100% 100%');
                    activeDrag.$dom.addClass('scripConvertCode')
                    activeDrag.$dom.attr('scripConvertCodeUrl',`https://static.kuaidizs.cn/resources/img/templateEdit/${val === 'txm' ? 'barcode_h' : 'qrcode'}.png`)

					//
                    if(val === 'txm'){
                        $('.barCodeTypeDiv').show()
                        $('.erCodeTypeDiv').hide()
                    }else if(val === 'ewm'){
                        $('.barCodeTypeDiv').hide()
                        $('.erCodeTypeDiv').show()
                    }
                }else{
					$(rotateSet).show()
                    activeDrag.$dom.removeClass('scripConvertCode')
                    activeDrag.$dom.removeAttr('scripConvertCodeUrl')
                    if( activeDrag.conf.style === 'BLACK_WHITE'){
                        activeDrag.applySetConf('color', 'rgba(255,255,255,1)');
                        activeDrag.applySetConf('background', 'black');
                    }else{
                        activeDrag.applySetConf('color', 'rgba(0,0,0,1)');
                        activeDrag.applySetConf('background', 'white');
                    }

                    // activeDrag.applySetConf('background-size', '100% 100%');

                    // 隐藏：条形码、二维码
                    $('.barCodeTypeDiv').hide()
                    $('.erCodeTypeDiv').hide()
                }

            }else if(name === 'barcodeType'){
                activeDrag.conf.barCodeType = val
            }else if(name === 'qrcodeType'){
				// activeDrag.conf.qrCodeType = val //
                activeDrag.conf.barCodeType = val // 后端统一用 barCodeType 字段
            }
        });
        html.find('.checkbox-label').on('click', function (e) {
            if (!$(e.target).is("input")) {
                $(this)[$(this).hasClass('active') ? 'removeClass' : 'addClass']('active');
            }

        })
        html.find('.radio-label').on('click', function (e) {
            var $target;
            if (!$(e.target).is("input")) {
                return;
            }
            $target = $(this);
            if ($target.hasClass('active')) {
                return;
            }
            $target.closest('.radio-list').find('.radio-label.active').removeClass('active');
            $target.addClass('active');
        })
        // 数据项搜索
        html.find('.item_search_btn').on('click',searchItem)
        html.find('.item_search_input').change(searchItem)
        function searchItem(){
            let searchText = html.find('.item_search_input').val().trim()
            let dataTypeList = html.find('.dragItemList')
            $.each(dataTypeList,function(i,it){
               let dataItemList = $(this).find('li')
               let hideNum = 0
                $.each(dataItemList,function(i,it){
                    if(!$(this).find('span').text().includes(searchText)){
                        $(this).hide()
                        hideNum ++
                    }else{
                        $(this).show()
                    }
                })
                if(hideNum == dataItemList.length){
                    $(this).parent('.dragGroupBox').hide().next('hr').hide()
                }else{
                    $(this).parent('.dragGroupBox').show().next('hr').show()
                }


            })
        }
        $dom.find('.template_operate').append(html);
        // 交互完善
        if(dragConf.scripConvertCode === 'txm'){
            $('.barCodeTypeDiv').show()
            $('.erCodeTypeDiv').hide()
        }else if(dragConf.scripConvertCode === 'ewm'){
            $('.barCodeTypeDiv').hide()
            $('.erCodeTypeDiv').show()
        }else{
            $('.barCodeTypeDiv').hide()
            $('.erCodeTypeDiv').hide()
        }
    };

    //删除拖拽元素
    PrintTemplate.prototype._removeDragElement = function (inputId) {
        //移除活跃元数据，取消了激活状态，同时也移除了右侧操作面板的dom
        this._removeActiveDrag();

        for (let i = 0; i < this.dragElements.length; i++) {
            if (this.dragElements[i].conf.InputID == inputId) {
                //删除dom
                this.dragElements[i].getDom().remove();
                //删除元素
                this.dragElements.splice(i, 1);
                break;
            }
        }
    };

    PrintTemplate.prototype._genUniqueKey = function () {
        let inputId = ~~(10000000 * Math.random()),
            inputs = this.templateData.ModeInputs || [],
            i = 0,
            flag = false;

        for (; i < inputs.length; i++) {
            if (inputs[i].DataKey != inputId && inputs[i].InputID != inputId) {
                flag = true;
                break;
            }
        }

        if (flag || !inputs.length) {
            return inputId;
        } else {
            return this._genUniqueKey();
        }
    };
    //新增一个拖拽元素元素, 并且生成对应的dom
    PrintTemplate.prototype._addDragElement = function (type) {
        let drag,
            dragConf,
            proVal,
            top,
            left,
            inputID,
            styleId = this.templateData.ModeList.StyleId,
            kddType = this.templateData.ModeList.KddType,
            exCode = this.templateData.ModeList.Excode,
            exId = this.templateData.ModeList.Exid,
            addHeight;
        if(exCode === 'BHD') {
            Tatami.pub('xbqSettingPoint','新增数据框');
        }
        // 通过接口去获取自定义区域范围
        let autoLimitInfo;
        const { ModeListShow: { customHeight, customLeft, customTop, customWidth } } = this.templateData;
        // 如果有customHeight返回，说明走的是tj发布新模板的流程
        if(customHeight) {
            autoLimitInfo = {
                topMin: (~~customTop) * pxScale,
                topMax: (~~customHeight + ~~customTop) * pxScale,
                leftMin: (~~customLeft) * pxScale,
                leftMax: (~~customLeft + ~~customWidth) * pxScale,
            };
        }

        // 自定义区域是通过接口去获取
        if (autoLimitInfo) {
            top = autoLimitInfo.topMin + 10;
            autoLimitInfo.leftMin ? (left = autoLimitInfo.leftMin) : (left = 72);
        }
        // 以下else中的内容为旧的逻辑
        else {
            //判断模版类型是不是闪打，如果是闪打，xy设置为闪打的区域
            const kddTypeName = getkddTempName(kddType);
            for (
                let i = 0;
                i < outerConf.templateLimitInfo[kddTypeName].length;
                i++
            ) {
                const item = outerConf.templateLimitInfo[kddTypeName][i];
                if (
                    (item.exCode &&
                        item.exCode == exCode &&  //根据exCode && styleId判断
                        item.styleId == styleId ) ||
                    (item.exId && exId == item.exId && styleId == item.styleId) ||
                    (!item.exCode && !item.exId && item.styleId == styleId)
                ) {
                    top = item.addTop || item.topMin + 10;
                    item.addHeight && (addHeight = item.addHeight);

                    item.leftMin ? (left = item.leftMin) : (left = 72);

                    break;
                } else {
                    top = 48;
                    left = 72;
                }
            }
        }

        inputID = this._genUniqueKey();

        dragConf = {
            DataKey: inputID, //fixme 只有特定模板才会需要这个，先统一生成 ,稍后修复
            Exlistshow_mfhdId: 0,
            Exlistshow_mkddId: 0,
            Exuserid: this.exuid,
            Fontname: '',
            Fontsize: 0,
            H_: addHeight || 21,
            Hjj: -1,
            InputID: inputID, //新增的这个值是前端生成的，后端同学会从新生成，并且每次保存，所有的InputID都会改变（后端重新生成）
            Inputtype: 'kdd',
            IsDraggable: true,
            IsEditable: true,
            Isb_n: -1,
            Isedit: 1,
            Status: 1,
            Str_h: '',
            Str_q: '',
            W_: 100,
            X_: left,
            Y_: top,
            Zjj: -1,
            proArray: [],
            style:'DEFAULT'
        };

        if (type === 'textInput') {
            proVal = {
                InputProID: 0,
                InputID: dragConf.InputID,
                Exuserid: 0,
                Inputtype: 'kdd',
                Mode_listShow_kddId: 0,
                Mode_listShow_fhdId: 0,
                Dataname: 'txta',
                Str_q: '',
                Str_h: '',
                ProValue: '',
                Adddate: '',
            };
            dragConf.proArray.push(proVal);
        }

        if (type === 'line') {
            dragConf = {
                InputID: inputID,
                Exuserid: this.exuid,
                Inputtype: 'kdd',
                Exlistshow_mkddId: 0,
                Exlistshow_mfhdId: 0,
                W_: 100,
                H_: 1,
                X_: left,
                Y_: top,
                IsDraggable: true,
                IsEditable: true,
                Isedit: 1,
                Fontname: '黑体',
                Fontsize: 0,
                Isb_n: -1,
                Hjj: -1,
                Zjj: -1,
                Str_q: '0',
                Str_h: '',
                Status: 1,
                DataKey: inputID,
                proArray: [],
            };

            proVal = {
                InputProID: 0,
                InputID: dragConf.InputID,
                Exuserid: 0,
                Inputtype: 'kdd',
                Mode_listShow_kddId: 0,
                Mode_listShow_fhdId: 0,
                Dataname: 'line',
                Str_q: '',
                Str_h: '',
                ProValue: '',
                Adddate: '',
            };

            dragConf.proArray.push(proVal);
        }

        if (type == 'image') {
            dragConf = {
                InputID: inputID,
                Exuserid: this.exuid,
                Inputtype: 'kdd',
                Exlistshow_mkddId: 0,
                Exlistshow_mfhdId: 0,
                W_: 80,
                H_: 80,
                X_: left,
                Y_: top,
                IsDraggable: true,
                IsEditable: true,
                Isedit: 1,
                Fontname: '黑体',
                Fontsize: 0,
                Isb_n: -1,
                Hjj: -1,
                Zjj: -1,
                Str_q: '',
                Str_h: '',
                Status: 1,
                DataKey: inputID,
                proArray: [],
            };

            proVal = {
                InputProID: 0,
                InputID: dragConf.InputID,
                Exuserid: 0,
                Inputtype: 'kdd',
                Mode_listShow_kddId: 0,
                Mode_listShow_fhdId: 0,
                Dataname: 'img',
                Str_q: '',
                Str_h: '',
                ProValue: '',
                Adddate: '',
            };

            dragConf.proArray.push(proVal);
        }

        if (type == 'rect') {
            dragConf = {
                InputID: inputID,
                Exuserid: this.exuid,
                Inputtype: 'kdd',
                Exlistshow_mkddId: 0,
                Exlistshow_mfhdId: 0,
                W_: 100,
                H_: 50,
                X_: left,
                Y_: top,
                IsDraggable: true,
                IsEditable: true,
                Isedit: 1,
                Fontname: '黑体',
                Fontsize: 0,
                Isb_n: -1,
                Hjj: -1,
                Zjj: -1,
                Str_q: '0',
                Str_h: '',
                Status: 1,
                DataKey: inputID,
                proArray: [],
            };

            proVal = {
                InputProID: 0,
                InputID: dragConf.InputID,
                Exuserid: 0,
                Inputtype: 'kdd',
                Mode_listShow_kddId: 0,
                Mode_listShow_fhdId: 0,
                Dataname: 'rect',
                Str_q: '',
                Str_h: '',
                ProValue: '',
                Adddate: '',
            };

            dragConf.proArray.push(proVal);
        }

        //根据数据分析，这个单独的拖拽元素，所有的数据完全是前端产生的，直接存储就好
        drag = new DragElement(
            dragConf,
            this.globalSetting,
            type,
            false,
            this.mockInputData,
        );
        drag.init(this.tplDefaultItem, this.viewMode, this.mockInputData);
        //将拖拽元素加到模版列表中
        this.dragElements.push(drag);

        this._setActiveDrag(drag);
    };

    //修改底图
    PrintTemplate.prototype._modifyBgImg = function (url) {
        this.templateData.ModeListShow.ImgSrc = url;

        const backgroundUrl = comp.base.dealBackgroundUrl(url);
        this.$dom.find('.drag_play_ground').css({
            'background-image': 'url(' + backgroundUrl + ')',
        });
    };

    //绑定新增拖拽元素触发
    PrintTemplate.prototype._bindAddDragEvent = function () {
        const $dom = this.$dom,
            that = this;
        const StyleId = this.templateData.ModeList.StyleId,
            Excode = this.templateData.ModeList.Excode;
        $dom.find('.addDrag').on('click', function () {
            const type = $(this).attr('data-drag-type');
            const modeInfo = that.templateData.ModeList;
            if (outerConf.isForbiddenTemp(modeInfo)) {
                Tatami.showFail('该模板不可编辑');
                return;
            }

            if (type === 'table') {
                // 检查是否存在table
                var isHaveTable;
                // 检查是否有发货内容
                var isHaveFInfo,FInfoId;

                that.dragElements.some(item=>{
                    const {conf={},dragType} = item;
                    if(dragType === "tableHtml"){
                        isHaveTable = true;
                        return true
                    }
                    if(conf.proArray){
                        conf.proArray.forEach((obj={})=>{
                            if(obj.Dataname === "f_info"){
                                isHaveFInfo = true;
                                FInfoId = obj.InputID;
                            }
                        })
                    }
                })

                if(isHaveTable){
                    // const dialog = window.Tatami.controls.get('control.dialog')
                    // dialog.getMethod('confirmAlert',{
                    //     contentMsg:`<div>已存在表格数据，请勿重复添加！</div>`,
                    //     width: 400,
                    //     height: 160,
                    //     cancelHide:true,
                    //     okName:'确定',
                    // })
                    model({
                        type: 'confirm',
                        content:`<div>已存在表格数据，请勿重复添加！</div>`,
                        okName:'确定',
                        width:500,
                        cancelHide:true
                    });
                    return false;
                }
                if(isHaveFInfo){
                    // const dialog = window.Tatami.controls.get('control.dialog')
                    model({
                        type: 'confirm',
                        content:'<div style="line-height:20px">检测到存在发货内容数据项，是否使用表格替换发货内容展示？</div>',
                        okName:'确定',
                        width:500,
                        cancelName: '取消',
                        okCb: function(){
                            that._removeDragElement(FInfoId);
                            that._addTable()
                        },
                    });
                    return false;
                }
                if(modeInfo.Excode === 'FHD'){
                    that._addFhdTable()
                }else if(['CGD','RKD','CKD','THQD','ZBXP'].includes(modeInfo.Excode)){
                    that._addOtherTable()
                }else{
                    that._addTable()
                }
            }else {
                //生成元素
                that._addDragElement(type);
            }
        });
    };

    //刷新快递服务
    PrintTemplate.prototype._refreshServiceList = function () {
        let _obj, _arr;
        for (let i = 0; i < this.dragElements.length; i++) {
            _obj = this.dragElements[i];
            _arr = _obj.conf.proArray;
            if (_arr && _arr[0] && _arr[0].Dataname == 'servicelist') {
                _obj.$dom
                .find('.dragEdit')
                .html(
                    htmlStore.createServiceHtml(
                        this.templateData.ModeServiceItems,
                        this.mockInputData,
                        this.templateData.ModeList.Excode,
                    ),
                );
                break;
            }
        }
    };

    PrintTemplate.prototype._doublePermutation = function($dom){
        // 要放置进的容器节点
        const $xbqDoubleBox = $dom.find('#xbqDouble');
        // 清空以前的
        if(parseInt(xbqPaperSheet) == 1){
            $xbqDoubleBox.empty();
            return
        }
        // $xbqDoubleBox.empty();
        const xbqDoubleBox = $xbqDoubleBox[0]
        let newXbqDoubleBoxHtml = `
        <div class = "copy_img_box">
           ${['0','2'].includes(this.viewMode) ? '' : '<div class="xbq_zz" >自动同步左侧模板</div>'}
        </div>`;
        let newXbqDoubleBox  = $(newXbqDoubleBoxHtml)
         // 要复制的节点
         const darg_ground_node = $dom.find('.drag_play_ground')[0]
         const xbqDomCloneNode = darg_ground_node.cloneNode(true);
         if(!(parseInt(xbqPaperSheet)===2||parseInt(xbqPaperSheet)===0)){
            $($xbqDoubleBox).css('z-index','-1');
            return
         }
        // $(darg_ground_node).css('left','40%')
        /** 处理样式 */
        // const styleList = xbqDomCloneNode.getAttribute('style').split(';').filter(txt=>!/transform|left|top/.test(txt))
        // const styleList2 = xbqDomCloneNode.getAttribute('style').split(';').filter(txt=>/left|top|width|height/.test(txt))
        // const transformStyle =  xbqDomCloneNode.getAttribute('style').split(';').filter(txt=>/transform/.test(txt))[0]
        // const scaleRes = transformStyle.match(/scale\((.+)\)/);
        // const scale = (scaleRes&&scaleRes[1])? scaleRes[1] :1;
        // const transformString = `transform: translate(${50 + (scale-1)*100}%, -150%) scale(${scale})`
        // xbqDomCloneNode.setAttribute('style',styleList.join(';'))
        xbqDoubleBox.setAttribute('style',['position: relative;overflow: hidden;cursor: no-drop;z-index:1;border-radius: 4px;margin-left:5px'].join(';'))
        const w = parseInt(xbqDomCloneNode.style.width) || 0;
        const h = (parseInt(xbqDomCloneNode.style.height) || 0);

        /* 将img提取出来，用html展示 */
        const imgList = []
        // NOTE 暂时保留，后续移除该逻辑
        xbqDomCloneNode.querySelectorAll('img').forEach(imgNode=>{
            const imgDiv = imgNode.parentElement.parentElement;
            const cloneImgDIv = imgDiv.cloneNode(true);
            cloneImgDIv.setAttribute('class','')
            $(cloneImgDIv).css('position','absolute')
            imgDiv.remove();
            imgList.push(cloneImgDIv)
        })
        xbqDomCloneNode.querySelectorAll('.scripConvertCode').forEach(imgNode=>{
            const imgDiv = imgNode;
            const cloneImgDIv = imgDiv.cloneNode(true);
            cloneImgDIv.setAttribute('class','')
            $(cloneImgDIv).css('position','absolute')
            $(cloneImgDIv).html = `<img src="${$(cloneImgDIv).attr('scripConvertCode')}"/>`
            imgDiv.remove();
            imgList.push(cloneImgDIv)
        })
        /* 将节点转成string， 放置进svg */
        const xmlSerializer = new XMLSerializer();
        let html = xmlSerializer.serializeToString(xbqDomCloneNode);
        const svg = `<svg xmlns='http://www.w3.org/2000/svg' width='${w}' height='${h}'>
        <style>
            .drag_play_ground{
                width: 100%;
                height: 100%;
                overflow: hidden;
                position: relative;
                background-repeat: no-repeat;
            }
            .dragElement{
                width: 150px;
                height: 20px;
                position: absolute;
                word-break: break-all;
                z-index: 1;
            }
            .dragEdit{
                width: 100%;
                height: 100%;
                overflow: hidden;
                cursor: move;
            }
            .dragImg {
                width:135px;
                height:32px;
            }
        </style>
        <foreignObject
                x='0'
                y='0'
                width='${w}'
                height='${h}'
            >${html}</foreignObject></svg>
        `
        const img = new Image();
        img.onload = () => {
            newXbqDoubleBox.append(img)
            imgList.forEach(node=>{
                newXbqDoubleBox.append(node)
            })
            // newXbqDoubleBox.appendTo()
            $(xbqDoubleBox).html(newXbqDoubleBox)
        };
        img.src = `data:image/svg+xml;charset=utf-8,${svg}`;
}

    //绑定操作面板的相关内容事件
    // NOTE 勾选数据项
    PrintTemplate.prototype._bindOperateEvent = function () {
        let that = this,
            dragElement,
            $dom = this.$dom;
        $dom.find('.template_operate')
        .on('change', '.dragDataItem', function (e) {
            let $t = $(this),
                $next = $t.next(),
                dataKey = $t.attr('data-key'),
                isCheck = $t.prop('checked'),
                obj = JSON.parse(unescape($t.attr('data-obj'))),
                $domSetShow;
                dragElement = that.activeDragElement

            if (isCheck) {
                if(that.activeDragElement.conf.proArray.length && ['txm','ewm'].includes(that.activeDragElement.conf.scripConvertCode)){
                    Tatami.showNoti('二维码或条形码不支持同时勾选多个数据项')
                    $(this).attr('checked',false)
                    return
                }

                if(that.activeDragElement.conf.proArray.length && /image/.test(dataKey)  || /image/.test(that.activeDragElement.conf.proArray[0]?.Dataname)){
                    Tatami.showNoti('当前内容为图片数据项，不支持同时勾选多个数据项')
                    $(this).attr('checked',false)
                    return
                }
                if(that.isFhd){
                    const funcHook = (window.printAPI.compHookObj || {})
                    .operatingRecord;
                    funcHook && funcHook('订单_订单打印_发货单设置_编辑模板_新增数据项');

                }else{
                    const funcHook = (window.printAPI.compHookObj || {})
                    .operatingRecord;
                    funcHook && funcHook('订单_订单打印_快递单设置_编辑模板_新增数据项');
                }
                if(['urgent_tag','pay_days'].includes(dataKey)){
                    dragElement.conf['style'] = 'BLACK_WHITE'
                }
                //活跃元素添加数据项，需要去重的
                that.activeDragElement.addProData(
                    obj,
                    that.exuid,
                    that.templateData.ModeListShow.Mode_ListShowId,
                );
                if (dataKey == 'ewm' || dataKey == 'ewm_str') {
                    $dom.find('.ewm_input').show();
                } else if (dataKey == 'f_info') {
                    $dom.find('.f_info_noti').show();
                }
                $next.css('color', '#fff');

                // if(dataKey == 'dshk_black' || (obj.ProValue||"").indexOf('isBlack=1')>-1 ){
                //     hasBlack = true;
                //     that.activeDragElement.modifyStyle({'color':'#fff','background-color':'#000000'})
                // }else if(hasBlack){
                //     hasBlack = false;
                //     that.activeDragElement.modifyStyle({'color':'#000000','background-color':'transparent'})
                // }
            } else {

                //活跃元素移除，根据Dataname进行移除
                that.activeDragElement.removeProData(obj.Key);
                if (dataKey == 'ewm' || dataKey == 'ewm_str') {
                    $dom.find('.ewm_input').hide();
                } else if (dataKey == 'f_info') {
                    $dom.find('.f_info_noti').hide();
                }
                $next.css('color', '#999');
            }
            if (
                that.isFhd &&
                    /^(s_message|f_memo|o_info|mjs)$/.test(dataKey)
            ) {
                that._setDataInputIsShow(
                    $dom.find('.databox_show'),
                    isCheck,
                    '1',
                    $t.next().text(),
                );
            }
            const _pro = (that.activeDragElement.conf || {}).proArray || [];
            if (_pro.length == 1 && (_pro[0] || {}).Dataname == 'count') {
                //数量需要显示水印设置信息
                $dom.find('.alphaSet').show();
            } else {
                $dom.find('.alphaSet:visible').hide();
            }
        })
        .on('change', '.ewm_input', function () {
            //二维码输入文字变化
            const val = $(this).val();
            that.activeDragElement.conf.Str_q = val;
            //$dom.find('input[name=Str_q]').val(val);
        });

        const modifyBackgroundImg = function (ImgSrc, Eximgid) {
            const backgroundUrl = comp.base.dealBackgroundUrl(ImgSrc);
            $dom.find('.bg_img')
            .attr('src', backgroundUrl)
            .attr('data-url', ImgSrc || '')
            .attr('data-cur-id', Eximgid);
        };
        let isRequesting = false,
            userBgImgs,
            sysBgImgs;
        //点击修改底图
        $dom.find('.modifyBgImg').on('click', function () {
            let sysFunc, params;

            if (isRequesting) {
                return;
            }

            sysFunc = function () {
                const img = sysBgImgs[0] || {};
                $dom.find('.switchBgImg').attr('data-cur-index', '0');
                $dom.find('.useBgImgType[value=default]').prop('checked', true);
                $dom.find('.uploadFileCon, .delLocalImg').hide();
                modifyBackgroundImg(img.ImgSrc, img.Eximgid);
                // $dom.find('.bg_img').attr('src', img.ImgSrc || '').attr('data-url', img.ImgSrc || '').attr('data-cur-id', img.Eximgid || '');
                $dom.find('.bgImgIndex').text(sysBgImgs.length ? '1' : '0');
                $dom.find('.bgImgTotal').text(sysBgImgs.length);

                $dom.find('.template_edit').hide();
                $dom.find('.template_bg').show();

                $dom.find('.bg_img').css(
                    'height',
                    !sysBgImgs.length ? '400px' : 'auto',
                );
            };

            if (sysBgImgs) {
                sysFunc();
            } else {
                isRequesting = true;
                params = {
                    companyId: that.templateData.ModeList.CompanyId,
                    kddtype: that.templateData.ModeList.KddType,
                    styleid: that.templateData.ModeList.StyleId,
                    isDefault: 0, //默认图传递0，非默认图传递exuserid
                    exuserid: that.templateData.ModeListShow.Exuserid,
                };
                outerConf.getBgImgs(params, function (imgs) {
                    //此处切换到另一个设置模板的页面，可以选择默认底图，也可以进行本地上传
                    sysBgImgs = imgs || [];
                    isRequesting = false;
                    sysFunc();
                });
            }
        });

        //重设默认布局
        $dom.find('.resetTemplateInput').on('click', async function () {
            const isContinue = await window.dialog.showAsync({
                width: 400,
                height: 170,
                title: ' 提示',
                content: `<div class="wrap-alert"><div class="title"><i class="icon-warn-big-orange"></i><h4>请确认是否恢复成默认模板？</h4></div><div class="body"></div></div>`,
            });

            if (!isContinue) {
                return;
            }

            const exId = $(this).attr('data-ex-id'),
                type = $(this).attr('data-type'),
                $this = $(this);
            $dom.find('input[name="editTable"]')
            .removeAttr('disabled')
            .css('cursor', 'point'); //允许编辑表格

            if ($this.val() == '恢复默认布局中……') {
                return;
            }
            $this.val('恢复默认布局中……');
            outerConf.getDefaultTplData(exId, type, function (ModeInputs) {
                $this.val('恢复默认布局');
                that._reInitDrags(ModeInputs);
            });
        });

        //复制自定义区域
        $dom.find('.copyTempInfo').on('click', function () {
            let type = that.templateData?.ModeListShow?.Modeid
            const modeInput = that._gatherDragElementConf(true);
            let $target = $(this);
            let _date = new Date().format('yyyy-MM-dd hh:mm:ss')
            if(!modeInput.length) Tatami.showNoti('无可复制自定义区域内容');
                let copyData = modeInput.filter(o=>{
                    // 如果存在表格数据项需要过滤，目前不支持复制表格
                    if(o.proArray&&o.proArray.length){
                        let isTable = o.proArray.find(it=>{
                          return  it.Dataname.includes('table') || it.Dataname.includes('tb')
                        })
                        return !isTable
                    }else{
                        return false
                    }
                })
                // 获许当前缓存的复制信息
                let localCopyData = localStorage.getItem('customCopyData') || '{}'
                localCopyData = JSON.parse(localCopyData)
                let copyInfo = {
                    copyTime:_date, // 复制的时间
                    copyInput:copyData
                }
                localCopyData[type] = copyInfo
                // 保存复制数据
                localStorage.setItem('customCopyData',JSON.stringify(localCopyData))
                Tatami.showSuccess('复制成功')
                $target
                .parent()
                .find('.info-msg').show()
                .text('版本' + _date + '复制成功');
        });
        // 粘贴自定义区域
        $dom.find('.pasteTempInfo').on('click', function () {
            let type = that.templateData?.ModeListShow?.Modeid
            let customCopyData = localStorage.getItem('customCopyData') || '{}';
                customCopyData = JSON.parse(customCopyData);
            let pasteData = customCopyData[type]?.copyInput || [];
            if(!pasteData.length) {
                Tatami.showNoti('当前没有可粘贴模板，请先操作复制模板')
                return
            }
            // 先判定新模板的自定义区域高度
             // 通过接口去获取自定义区域范围
            let autoLimitInfo,top = 0,left = 0;
            const { ModeListShow: { customHeight, customLeft, customTop, customWidth } } = that.templateData;
            // 如果有customHeight返回，说明走的是tj发布新模板的流程
            if(customHeight) {
                autoLimitInfo = {
                    topMin: (~~customTop) * pxScale,
                    topMax: (~~customHeight + ~~customTop) * pxScale,
                    leftMin: (~~customLeft) * pxScale,
                    leftMax: (~~customLeft + ~~customWidth) * pxScale,
                };
            }

            // 自定义区域是通过接口去获取
            if (autoLimitInfo) {
                top = autoLimitInfo.topMin + 10;
                (autoLimitInfo.leftMin === 0 || autoLimitInfo.leftMin) ? (left = autoLimitInfo.leftMin) : (left = 72);
            }
            // 以下else中的内容为旧的逻辑
            else if(type === 'kdd') {
                let styleId = that.templateData.ModeList.StyleId,
                kddType = that.templateData.ModeList.KddType,
                exCode = that.templateData.ModeList.Excode,
                exId = that.templateData.ModeList.Exid;
                //判断模版类型是不是闪打，如果是闪打，xy设置为闪打的区域
                const kddTypeName = getkddTempName(kddType);
                for (
                    let i = 0;
                    i < outerConf.templateLimitInfo[kddTypeName].length;
                    i++
                ) {
                    const item = outerConf.templateLimitInfo[kddTypeName][i];
                    if (
                        (item.exCode &&
                            item.exCode == exCode &&  //根据exCode && styleId判断
                            item.styleId == styleId ) ||
                        (item.exId && exId == item.exId && styleId == item.styleId) ||
                        (!item.exCode && !item.exId && item.styleId == styleId)
                    ) {
                        top = item.addTop || item.topMin + 10;
                        item.addHeight && (addHeight = item.addHeight);

                        item.leftMin ? (left = item.leftMin) : (left = 72);

                        break;
                    } else {
                        top = 48;
                        left = 72;
                    }
                }
            }
            // 记录一下原始top值，方便重置高度
            let inputIndex  = 0
            let formateInputData = pasteData.map((o)=>{
                o.W_ = 100;
                o.H_ = 21;
                o.X_ = left;
                o.Y_ = top + (inputIndex *21);
                o.InputID =  that._genUniqueKey();
                inputIndex ++
                if(autoLimitInfo?.topMax && (o.Y_>= autoLimitInfo?.topMax -21)){
                    inputIndex = 0
                    left+=100
                }
                return o
            })
            const modeInput = that._gatherDragElementConf(true);
            const newModeInputs =   [...modeInput,...formateInputData]
            //模板重新渲染，显示粘贴的版本号
            that._reInitDrags(newModeInputs);

        });
        //粘贴自定义区域  旧版 暂时保留做参考
        // $dom.find('.pasteTempInfo').on('click', function () {
        //     const params = that.templateData.ModeList || {},
        //         tempConfig = that.templateData.ModeTempPrintcfg || {},
        //         $target = $(this);
        //     comp.print.data.pasteTempInfo(params, function (result) {
        //         let _erroMsg;
        //         if (!result) {
        //             _erroMsg = '当前没有可粘贴模板，请先操作复制模板';
        //         } else {
        //             if (params.KddType != result.KddType) {
        //                 //模板类型不一致
        //                 _erroMsg =
        //                     '当前模板与备份内容模板类型不同，请确认后再试！';
        //             } else if (
        //                 params.KddType != 1 &&
        //                 !(
        //                     params.KddType == 3 &&
        //                     comp.base.getTempStyle('cainiao', params.StyleId) &&
        //                     comp.base.getTempStyle('cainiao', result.StyleId)
        //                 )
        //             ) {
        //                 //自由模板 非相同exCode 非相同styleId 模板宽高不同 且不允许复制
        //                 if (params.Excode != result.Excode) {
        //                     _erroMsg =
        //                         '当前模板与备份内容模板类型不同，菜鸟自由模板只允许同快递模板之间复制，';
        //                 } else if (params.StyleId != result.StyleId) {
        //                     _erroMsg =
        //                         '当前模板与备份内容模板类型不同，菜鸟自由模板只允许同面单样式模板之间复制，';
        //                 } else if (
        //                     tempConfig.Height != result.Height ||
        //                     tempConfig.Width != result.Width
        //                 ) {
        //                     _erroMsg =
        //                         '当前模板与备份内容模板类型不同，菜鸟自由模板只允许纸张宽高设置相同模板之间复制，';
        //                 }
        //             }
        //             _erroMsg &&
        //                 (_erroMsg +=
        //                     '<a target=\'_blank\' href=\'//www.kuaidizs.cn/helpMap/getDetail?detailId=458\'>详见帮助</a>');
        //         }
        //         if (_erroMsg) {
        //             Tatami.showFail(_erroMsg);
        //         } else {
        //             //模板重新渲染，显示粘贴的版本号
        //             that._reInitDrags(result.ModeInputs);
        //             $target
        //             .parent()
        //             .find('.info-msg')
        //             .text('版本' + result.ver + '粘贴成功');
        //         }
        //     });
        // });

        const bgTypeEvent = function (needRefresh, needReverse, needSwitch) {
            let val = $dom.find('.useBgImgType:checked').val(),
                $button = $dom.find('.uploadFileCon, .delLocalImg'),
                sysFunc,
                userFunc,
                params;

            $button[val == 'local' ? 'show' : 'hide']();

            if (isRequesting) {
                return;
            }

            sysFunc = function () {
                const img = sysBgImgs[0] || {};
                $dom.find('.switchBgImg').attr('data-cur-index', '0');
                modifyBackgroundImg(img.ImgSrc, img.Eximgid);
                // $dom.find('.bg_img').attr('src', img.ImgSrc || '').attr('data-url', img.ImgSrc || '').attr('data-cur-id', img.Eximgid || '');
                $dom.find('.bgImgIndex').text(sysBgImgs.length ? '1' : '0');
                $dom.find('.bgImgTotal').text(sysBgImgs.length);
                $dom.find('.bg_img').css(
                    'height',
                    !sysBgImgs.length ? '400px' : 'auto',
                );
            };

            userFunc = function () {
                const img = userBgImgs[0] || {};
                $dom.find('.switchBgImg').attr('data-cur-index', '0');
                modifyBackgroundImg(img.ImgSrc, img.Eximgid);
                // $dom.find('.bg_img').attr('src', img.ImgSrc || '').attr('data-url', img.ImgSrc || '').attr('data-cur-id', img.Eximgid || '');
                $dom.find('.bgImgIndex').text(userBgImgs.length ? '1' : '0');
                $dom.find('.bgImgTotal').text(userBgImgs.length);
                $dom.find('.bg_img').css(
                    'height',
                    !userBgImgs.length ? '400px' : 'auto',
                );
            };

            params = {
                companyId: that.templateData.ModeList.CompanyId,
                kddtype: that.templateData.ModeList.KddType,
                styleid: that.templateData.ModeList.StyleId,
                isDefault: that.templateData.ModeListShow.Exuserid, //默认图传递0，非默认图传递exuserid
                exuserid: that.templateData.ModeListShow.Exuserid,
            };

            if (val == 'default') {
                if (sysBgImgs && needRefresh == false) {
                    sysFunc();
                } else {
                    isRequesting = true;
                    outerConf.getBgImgs(params, function (imgs) {
                        if (needReverse) {
                            imgs = imgs.reverse();
                        }
                        //此处切换到另一个设置模板的页面，可以选择默认底图，也可以进行本地上传
                        sysBgImgs = imgs || [];
                        isRequesting = false;
                        sysFunc();
                    });
                }
            } else if (val == 'local') {
                if (userBgImgs && needRefresh == false) {
                    userFunc();
                } else {
                    isRequesting = true;
                    outerConf.getBgImgs(params, function (imgs) {
                        if (needReverse) {
                            imgs = imgs.reverse();
                        }
                        //此处切换到另一个设置模板的页面，可以选择默认底图，也可以进行本地上传
                        userBgImgs = imgs || [];
                        isRequesting = false;
                        userFunc();
                        if (!userBgImgs.length && needSwitch) {
                            $dom.find('.useBgImgType[value=default]')
                            .prop('checked', true)
                            .trigger('change');
                        }
                    });
                }
            }
        };

        //切换背景图片使用方式
        $dom.find('.useBgImgType').on('change', function () {
            bgTypeEvent(false, false);
        });

        //切换底图
        $dom.find('.switchBgImg').on('click', function () {
            let type = $(this).attr('data-type'),
                url = '',
                arrType,
                imgs,
                index = parseInt($(this).attr('data-cur-index')) || 0;

            //读取radio判断是哪个数组
            arrType = $dom.find('.useBgImgType:checked').val();

            imgs = arrType == 'local' ? userBgImgs : sysBgImgs;

            if (type === 'prev') {
                index--;
            } else {
                index++;
            }

            if (index < 0) {
                index = 0;
            } else if (index > imgs.length - 1) {
                index = imgs.length;
            }

            modifyBackgroundImg(imgs[index].ImgSrc, imgs[index].Eximgid);
            // $dom.find('.bg_img').attr('src', imgs[index].ImgSrc).attr('data-url', imgs[index].ImgSrc).attr('data-cur-id', imgs[0].Eximgid);

            $dom.find('.switchBgImg').attr('data-cur-index', index);
            $dom.find('.bgImgIndex').text(index + 1);
        });

        //上传底图，获取新底图列表，然后进行渲染 FIXME 需要将最新的主图放到第一张，所以获取local底图的排序需要时按照时间倒序或者顺序

        const pm = {
            companyId: that.templateData.ModeList.CompanyId,
            styleId: that.templateData.ModeList.StyleId,
            exuserId: that.templateData.ModeListShow.Exuserid,
        };

        outerConf.uploadBgImg(
            $dom.find('.uploadBgCon'),
            'editkdduploadimg',
            pm,
            function () {
                bgTypeEvent(true, true);
            },
            function () {
                alert('图片上传失败');
            },
        );

        //删除上传的底图，猜测是根据id
        $dom.find('.delLocalImg').on('click', function () {
            //从img上读取id
            const id = $dom.find('.bg_img').attr('data-cur-id');

            if (!id) {
                alert('当前无底图可以删除');
                return;
            }

            outerConf.delBgImg(
                id,
                function () {
                    alert('删除底图成功');
                    bgTypeEvent(true, true, true);
                    //重新渲染一下
                    //$dom.find('.useBgImgType:checked').trigger('change');
                },
                function () {
                    alert('删除底图失败');
                },
            );
        });

        //保存背景图片
        $dom.find('.saveBgImg').on('click', function () {
            let url;
            $dom.find('.template_edit').show();
            $dom.find('.template_bg').hide();
            //获取url，进行更新主图
            //2、获得到url，更改数据，更改dom
            url = $dom.find('.bg_img').attr('data-url');
            if (url) {
                that._modifyBgImg(url);
            }

            userBgImgs = undefined;
            sysBgImgs = undefined;
        });
        //取消设置背景图片
        $dom.find('.cancelBgImg').on('click', function () {
            $dom.find('.template_edit').show();
            $dom.find('.template_bg').hide();

            userBgImgs = undefined;
            sysBgImgs = undefined;
            isRequesting = false;
        });

        $dom.find('.xbqsize').on('change',function(e){
            const size = $dom.find('.xbqsize option:selected').val();
            $('.drag_ground_box').css('transform' ,`scale(${size})`);
            that.smallTagSize = size;
            that._doublePermutation($dom)
        });
        //处理整体偏移事件
        $dom.find('.shift_wheel_ctrl').on('click', function () {

            if(that.isFhd){
                const funcHook = (window.printAPI.compHookObj || {})
                .operatingRecord;
                funcHook && funcHook('订单_订单打印_发货单设置_编辑模板_偏移调整');

            }else{
                const funcHook = (window.printAPI.compHookObj || {})
                .operatingRecord;
                funcHook && funcHook('订单_订单打印_快递单设置_编辑模板_偏移调整');
            }

            let type = $(this).attr('data-type'),
                $input,
                val;
            //获取方向即可，接下来使用封装方法，处理偏移
            if (type == 'top' || type == 'bottom') {
                $input = $dom.find('.top_bottom');
            } else {
                $input = $dom.find('.left_right');
            }
            val = parseInt($input.val()) || 0;
            if (type == 'top' || type == 'left') {
                val--;
            } else if (type == 'bottom' || type == 'right') {
                val++;
            }
            $input.val(val);
            $dom.find('.shift_border').css(
                type == 'top' || type == 'bottom' ? 'top' : 'left',
                val,
            );
            //修改数据
            if (type == 'top' || type == 'bottom') {
                that.templateData.ModeTempPrintcfg.Updown = val;
            } else {
                that.templateData.ModeTempPrintcfg.Leftright = val;
            }
        });
        //处理模版名称，宽高，上下 左右的blur事件
        $dom.find('.top_bottom, .left_right').on('blur', function () {

            if(that.isFhd){
                const funcHook = (window.printAPI.compHookObj || {})
                .operatingRecord;
                funcHook && funcHook('订单_订单打印_发货单设置_编辑模板_偏移调整');

            }else{
                const funcHook = (window.printAPI.compHookObj || {})
                .operatingRecord;
                funcHook && funcHook('订单_订单打印_快递单设置_编辑模板_偏移调整');
            }

            const type = $(this).attr('data-type'),
                val = parseInt($(this).val()) || 0;
            $dom.find('.shift_border').css(type, val + 'px');
            //修改数据
            if (type == 'top') {
                that.templateData.ModeTempPrintcfg.Updown = val;
            } else {
                that.templateData.ModeTempPrintcfg.Leftright = val;
            }
            $(this).val(val);
        });
        $dom.find('input.ExcodeName').on('blur', function () {
            //更改模版名称
            that.templateData.ModeListShow.ExcodeName = $(this).val();
        });
        $dom.find('.printPaperSheet').on('click', function () {
            const _this = this,
                paperSheet = $(_this).data('papersheet'),
                paperSheetBtn = $dom.find('.printPaperSheet');

            paperSheetBtn.removeClass('checked');
            $(_this).addClass('checked');
            that.templateData.ModeTempPrintcfg.paperSheet = paperSheet;
            xbqPaperSheet = paperSheet;
            // $dom.find('#drag_ground').css('left','50%')
            that._doublePermutation($dom)
        });

        $dom.find('.tpl_width, .tpl_height').on('blur', function () {
            const type = $(this).attr('data-type'),
                // val = parseFloat($(this).val()).toFixed(2) || 0,
                val = parseInt($(this).val()) || 0,
                $ground = $dom.find('.drag_play_ground');

            if (type == 'width') {
                $ground.css('width', val * pxScale + 'px');
                that.templateData.ModeTempPrintcfg.Width = val;
            } else {
                $ground.css('height', val * pxScale + 'px');
                that.templateData.ModeTempPrintcfg.Height = val;
            }
            $(this).val(val);
            that._doublePermutation($dom)
        });
        //切换模版方向
        $dom.find('.tpl_direction').on('change', function () {
            let _direction = ~~$(this).val(),
                $height = $dom.find('.tpl_height');
            if (_direction == 3) {
                $height.val(0).attr('disabled', 'disabled');
                that.templateData.ModeTempPrintcfg.Height = 0;
            } else {
                if (that.templateData.ModeTempPrintcfg.Height == 0) {
                    $height = $dom.find('.tpl_height').val(297).blur();
                }
                $height.attr('disabled', false);
            }
            that.templateData.ModeTempPrintcfg.Direction = _direction;
        });
        //是否打印留存联和签收联logo
		$dom.find('.encrypted').on('change', function () {
			const name = $(this).attr('name'),
				status = $(this).is(':checked');
			if (name === 'printEncrypted') {
				if (status) {
					$(this).closest('.template_edit').find('.sendEncryptedBox').show();
				} else {
					$(this).closest('.template_edit').find('.sendEncryptedBox').hide();
					// 清空sendEncryptedBox内的多选框
					$(this).closest('.template_edit').find('.sendEncryptedBox input[type="checkbox"]').prop('checked', false);
					// 同时更新配置对象中的值
					that.templateData.ModeTempPrintcfg.userTemplateCfgConfigJson.senderNameEncrypted = 0;
					that.templateData.ModeTempPrintcfg.userTemplateCfgConfigJson.senderPhoneEncrypted = 0;
					that.templateData.ModeTempPrintcfg.userTemplateCfgConfigJson.senderAddressEncrypted = 0;
				}
			} else if (name === 'recipientEncrypted') {
				if (status) {
					$(this).closest('.template_edit').find('.reEncryptedBox').show();
				} else {
					$(this).closest('.template_edit').find('.reEncryptedBox').hide();
					// 清空reEncryptedBox内的多选框
					$(this).closest('.template_edit').find('.reEncryptedBox input[type="checkbox"]').prop('checked', false);
					// 同时更新配置对象中的值
					that.templateData.ModeTempPrintcfg.userTemplateCfgConfigJson.recipientNameEncrypted = 0;
					that.templateData.ModeTempPrintcfg.userTemplateCfgConfigJson.recipientPhoneEncrypted = 0;
					that.templateData.ModeTempPrintcfg.userTemplateCfgConfigJson.recipientAddressEncrypted = 0;
				}
			} else {
				that.templateData.ModeTempPrintcfg.userTemplateCfgConfigJson[name] = status ? 1 : 0;
			}
		});
		//是否打印留存联和签收联logo
		$dom.find('.displayLogo').on('change', function () {
            const name = $(this).attr('name'),
                status = $(this).is(':checked');
            if( ['printEncrypted'].includes(name)){
                let value = $(this).val()
                that.templateData.ModeTempPrintcfg[name] = Number(value)
            }else{
                that.templateData.ModeTempPrintcfg[name] = status ? 1 : 0;
            }
            //控制模板logo展示隐藏
            if (that.templateData.ModeList.KddType == 2) {
                $(this)
                .closest('.template_edit')
                .find(
                    `#${name === 'Qslogo' ? 'img_logo_qs' : 'img_logo_lc'}`,
                )
                [status ? 'show' : 'hide']();
            }
        });

        //发货单是否打印分隔线 商品是否合并设置
        $dom.find('.fhdModeSet').on('change', function () {
            let $t = $(this),
                _$dom,
                name = $t.attr('name'),
                status = $t.is(':checked');
            if (name == 'A4mode') {
                _$dom = $t
                .closest('.fhd_mode_set')
                .find('.a4linefhd')
                [status ? 'show' : 'hide']();
                if (!status) {
                    _$dom.find('[name="A4line"]').prop('checked', false);
                    that.templateData.ModeTempPrintcfg['A4line'] = 0;
                }
            }
            if(name === 'contentOverflow'){
                that.templateData.ModeTempPrintcfg.contentOverflow = status ? 'HIDE' : 'SUPPORT';
            }else{
                that.templateData.ModeTempPrintcfg[name] = status ? 1 : 0;
            }

        });
        //电子面单增值服务
        $dom.find('.ex_service_item:not(.disable)').on('change', function () {
            let $t = $(this),
                checkStatus = $t.prop('checked'),
                dataName = $t.attr('name'),
                $tarParentNext = $t.parent().next(),
                _temp = that.templateData,
                date,
                objTemp,
                obj;

            if (!_temp.ModeServiceItems) {
                _temp.ModeServiceItems = [];
            }
            if (checkStatus) {
                $(
                    _temp.DefaultServiceItems[0] &&
                        _temp.DefaultServiceItems[0].ExServiceItems,
                ).each(function () {
                    if (this.Name == dataName) {
                        objTemp = this;
                        return false;
                    }
                });

                if (objTemp) {
                    date = new Date();
                    obj = {
                        Addtime:
                            date.getFullYear() +
                            '-' +
                            (date.getMonth() + 1) +
                            '-' +
                            date.getDate() +
                            ' ' +
                            date.getHours() +
                            ':' +
                            date.getMinutes() +
                            ':' +
                            date.getSeconds(),
                        Defaultval: '',
                        Exshowid: _temp.ModeTempPrintcfg.Exshowid, //this is templateid
                        Dataname: objTemp.DataName,
                        Issetval: objTemp.IsSetVal,
                        Itemcode: objTemp.Key,
                        Itemname: objTemp.Name,
                        Reserve1: objTemp.Value, //fixme 这个正确吗？
                        Reserve2: '',
                        Reserve3: '',
                        Reserve4: '',
                        Unit: '',
                        Updatetime: '',
                        id: '',
                        taobaoId: that.exuid,
                    };
                    _temp.ModeServiceItems.push(obj);
                }
            } else {
                //移除一个
                for (let i = 0; i < _temp.ModeServiceItems.length; i++) {
                    //
                    if (_temp.ModeServiceItems[i].Itemname == dataName) {
                        _temp.ModeServiceItems.splice(i, 1);
                        break;
                    }
                }
            }
            $tarParentNext.length
                ? $tarParentNext[checkStatus ? 'show' : 'hide']()
                : '';

            that._refreshServiceList();
        });

        //电子面单默认增值服务
        $dom.find('.ex_service_default').on('change', function () {
            const tar = $(this);
            // lbls.removeClass("on");
            if (tar.is(':checked')) {
                // tar.parent().addClass("on");
                let index = -1;
                const arr = that.templateData.ModeServiceItems;
                for (var i = 0; i < arr.length; i++) {
                    if (arr[i].Itemname == tar.attr('name')) {
                        index = i;
                        break;
                    }
                }
                if (index > -1) {
                    arr[i].Defaultval = tar.val();
                }
            }
            that._refreshServiceList();
        });

        //编辑表格
        $dom.find('.editTableDrag').on('click', function () {
            const _this = this,
                $this = $(this),
                dragType = $(_this).attr('data-drag-type');
            if (dragType == 'tableEdit') {
                const $tableDom = $dom.find('.tempTable');
                if ($tableDom.length < 1) {
                    $this
                    .attr('disabled', 'disabled')
                    .css('cursor', 'not-allowed'); //禁止编辑表格
                    return false;
                }
                //当前编辑模板的数据收集
                that._gatherDragElementConf();
                //只关闭上一个模板编辑设计弹窗
                that.cancelCb && that.cancelCb(true);
                //调用外部挂在函数 编辑表格
                outerConf.editFhdTable({
                    modeInfo: that.templateData,
                    tempData: that.mockInputData,
                    singlePData: that.singlePData,
                });
            }
        });
        //切换平台模板
        $dom.find('.merge_kd_type').on('click', '.merge_kd_type_btn' , function(e){
            const that = comp.Print.FN()
            const tarDom = $(this);
            const userTemplateId = tarDom.attr('userTemplateId')
            tarDom.addClass('active_merge_btn').siblings('span').removeClass("active_merge_btn");
            that.getTemplateInfo(userTemplateId, true, function(res){
                that.editKdd(res,function(data){
                    if(data){
                        that.setGroupSet(null,true)
                    }else{
                        that.showkddMain(domData.defId, domData.printDatas,sellerData);
                    }
                })
            });

        })
        //分组设置
        $dom.find('.merge_kd_type').on('click', '.merge_kd_set_btn' , function(e){
            // const tarDom = $(this);
            that.$dom.remove();
            that.cancelCb && that.cancelCb('groupSet');
            userBgImgs = undefined;
            sysBgImgs = undefined;
            isRequesting = false;
        })
    };
    PrintTemplate.prototype._addTableItem = function({ key,ProValue }){
        var that = this;
        var  dragConf, top, left, inputID
            , styleId = that.templateData.ModeList.StyleId
            , kddType = that.templateData.ModeList.KddType
            , exCode = that.templateData.ModeList.Excode
            , exId = that.templateData.ModeList.Exid
            , addHeight


        if (kddType != 7) {
            for (let i = 0; i < outerConf.templateLimitInfo.cainiao.length; i++) {
                let item = outerConf.templateLimitInfo.cainiao[i];
                if ((item.exCode && item.exCode == exCode && item.styleId == styleId)
                    || (item.exId && exId == item.exId && styleId == item.styleId)
                    || (!item.exCode && !item.exId && item.styleId == styleId)) {

                    top = item.addTop || item.topMin + 10;
                    item.addHeight && (addHeight = item.addHeight);

                    item.leftMin ? left = item.leftMin : left = 72;

                    break;

                } else {
                    top = 48;
                    left = 72;
                }
            }
        }
        dragConf = {
            "DataKey": '',         //fixme 只有特定模板才会需要这个，先统一生成 ,稍后修复
            "Exlistshow_mfhdId": 0,
            "Exlistshow_mkddId": 0,
            "Exuserid": that.exuid,
            "Fontname": "",
            "Fontsize": 0,
            "H_": 10,
            "Hjj": -1,
            "InputID": '',//新增的这个值是前端生成的，后端同学会从新生成，并且每次保存，所有的InputID都会改变（后端重新生成）
            "Inputtype": "kdd",
            "IsDraggable": true,
            "IsEditable": true,
            "Isb_n": -1,
            "Isedit": 1,
            "Status": 1,
            "Str_h": "",
            "Str_q": "",
            "W_": 10,
            "X_": -100,
            "Y_": -100,
            "Zjj": -1,
            "proArray": []
        };
        const defaultItemObj = [
            {
                "InputProID": 6873414,
                "InputID": 6873414,
                "Exuserid": 1893301994,
                "Inputtype": "kdd",
                "Mode_listShow_kddId": 0,
                "Mode_listShow_fhdId": 40335,
                "Dataname": "table_r_0",
                "Str_q": "",
                "Str_h": "",
                "ProValue": ProValue,
                "Adddate": "2022-06-06 15:21:30",
                "Exlistshow_mfhdId": 7397419
            },
            {
                "InputProID": 637340,
                "InputID": 637340,
                "Exuserid": 1893301994,
                "Inputtype": "kdd",
                "Mode_listShow_kddId": 0,
                "Mode_listShow_fhdId": 40335,
                "Dataname": key,
                "Str_q": "",
                "Str_h": "",
                "ProValue": "",
                "Adddate": "2016-06-17 10:46:17",
                "Exlistshow_mfhdId": 7397419,
                "proValueObj": {
                    "": ""
                }
            }
        ]
        inputID = that._genUniqueKey();
        return  {...dragConf,InputID:inputID,proArray:defaultItemObj}
    }

    PrintTemplate.prototype._addTable = function(){
        let that = this
        ,tableDrag
        ,dragConf
        ,drag
        ,drag_table_list = []
        ,left = 5
        ,top = 48
        ,width = that.templateData.ModeTempPrintcfg.Width * pxScale - 10
        ,inputID;

        const autoLimitInfo = this._getAutoLimitInfo();

        // 自定义区域配置
        if (autoLimitInfo) {
            const {topMin, topMax, leftMin} = autoLimitInfo;
            top = topMin + 10 > topMax ? topMin : topMin + 10;  // (topMin + 10)超出topMax时，取topMin
            left = leftMin ?? 72;
        }
        dragConf = {
            "DataKey": '',         //fixme 只有特定模板才会需要这个，先统一生成 ,稍后修复
            "Exlistshow_mfhdId": 0,
            "Exlistshow_mkddId": 0,
            "Exuserid": that.exuid,
            "Fontname": "",
            "Fontsize": 0,
            "H_": 10,
            "Hjj": -1,
            "InputID": '',//新增的这个值是前端生成的，后端同学会从新生成，并且每次保存，所有的InputID都会改变（后端重新生成）
            "Inputtype": "kdd",
            "IsDraggable": true,
            "IsEditable": true,
            "Isb_n": -1,
            "Isedit": 1,
            "Status": 1,
            "Str_h": "",
            "Str_q": "",
            "W_": 10,
            "X_": 5,
            "Y_": top,
            "Zjj": -1,
            "proArray": []
        };
        const defaultArr = [
            [
                {
                    "Adddate": "2022-06-06 10:44:35",
                    "Dataname": "table_title",
                    "Exuserid": 1893301994,
                    "InputID": 1178041,
                    "InputProID": 1149533,
                    "Inputtype": "kdd",
                    "Mode_listShow_fhdId": 7397419,
                    "Mode_listShow_kddId": 0,
                    "ProValue": "iss＝1",
                    "Str_h": "",
                    "Str_q": "",
                    "enableStatus": true,
                    "modified": "2022-06-02 13:58:23",
                    "status": 1
                }
            ],
            [
                {
                    "Adddate": "2022-06-06 10:44:37",
                    "Dataname": "table",
                    "Exuserid": 1893301994,
                    "InputID": 1178046,
                    "InputProID": 1149545,
                    "Inputtype": "kdd",
                    "Mode_listShow_fhdId": 7397419,
                    "Mode_listShow_kddId": 0,
                    "ProValue": "lineBroder=1,rgH=15",
                    "Str_h": "",
                    "Str_q": "",
                    "enableStatus": true,
                    "modified": "2022-06-02 13:58:23",
                    "status": 1
                }
            ],
            [
                {
                    "InputProID": 6873414,
                    "InputID": 6873414,
                    "Exuserid": 1893301994,
                    "Inputtype": "kdd",
                    "Mode_listShow_kddId": 0,
                    "Mode_listShow_fhdId": 40335,
                    "Dataname": "table_r_0",
                    "Str_q": "",
                    "Str_h": "",
                    "ProValue": `tit=序号，rgW=${width * 12 / 100}`,
                    "Adddate": "2022-06-06 15:21:30",
                    "Exlistshow_mfhdId": 7397419
                },
                {
                    "InputProID": 637340,
                    "InputID": 637340,
                    "Exuserid": 1893301994,
                    "Inputtype": "kdd",
                    "Mode_listShow_kddId": 0,
                    "Mode_listShow_fhdId": 40335,
                    "Dataname": "tb_xh1",
                    "Str_q": "",
                    "Str_h": "",
                    "ProValue": "",
                    "Adddate": "2016-06-17 10:46:17",
                    "Exlistshow_mfhdId": 7397419,
                    "proValueObj": {
                        "": ""
                    }
                }
            ],
            [
                {
                    "InputProID": 9908211,
                    "InputID": 9908211,
                    "Exuserid": 1893301994,
                    "Inputtype": "kdd",
                    "Mode_listShow_kddId": 0,
                    "Mode_listShow_fhdId": 40335,
                    "Dataname": "table_r_1",
                    "Str_q": "",
                    "Str_h": "",
                    "ProValue": `tit=宝贝标题，rgW=${width * 50 / 100}`,
                    "Adddate": "2022-06-06 16:21:20",
                    "Exlistshow_mfhdId": 7397419
                },
                {
                    "InputProID": 173565,
                    "InputID": 173565,
                    "Exuserid": 1893301994,
                    "Inputtype": "kdd",
                    "Mode_listShow_kddId": 0,
                    "Mode_listShow_fhdId": 40335,
                    "Dataname": "tb_tit",
                    "Str_q": "",
                    "Str_h": "",
                    "ProValue": "",
                    "Adddate": "2016-06-17 10:46:17",
                    "Exlistshow_mfhdId": 7397419,
                    "proValueObj": {
                        "": ""
                    }
                }
            ],
            [
                {
                    "InputProID": 7024558,
                    "InputID": 7024558,
                    "Exuserid": 1893301994,
                    "Inputtype": "kdd",
                    "Mode_listShow_kddId": 0,
                    "Mode_listShow_fhdId": 40335,
                    "Dataname": "table_r_2",
                    "Str_q": "",
                    "Str_h": "",
                    "ProValue": `tit=规格名称，rgW=${width * 24 / 100}`,
                    "Adddate": "2022-06-06 16:21:48",
                    "Exlistshow_mfhdId": 7397419
                },
                {
                    "InputProID": 1813277,
                    "InputID": 1813277,
                    "Exuserid": 1893301994,
                    "Inputtype": "kdd",
                    "Mode_listShow_kddId": 0,
                    "Mode_listShow_fhdId": 40335,
                    "Dataname": "tb_cpgg",
                    "Str_q": "",
                    "Str_h": "",
                    "ProValue": "",
                    "Adddate": "2016-06-17 10:46:17",
                    "Exlistshow_mfhdId": 7397419,
                    "proValueObj": {
                        "": ""
                    }
                }
            ],
            [
                {
                    "InputProID": 4056106,
                    "InputID": 4056106,
                    "Exuserid": 1893301994,
                    "Inputtype": "kdd",
                    "Mode_listShow_kddId": 0,
                    "Mode_listShow_fhdId": 40335,
                    "Dataname": "table_r_3",
                    "Str_q": "",
                    "Str_h": "",
                    "ProValue": `tit=数量，rgW=${width * 14 / 100}`,
                    "Adddate": "2022-06-06 16:22:01",
                    "Exlistshow_mfhdId": 7397419
                },
                {
                    "InputProID": 3811875,
                    "InputID": 3811875,
                    "Exuserid": 1893301994,
                    "Inputtype": "kdd",
                    "Mode_listShow_kddId": 0,
                    "Mode_listShow_fhdId": 40335,
                    "Dataname": "tb_count",
                    "Str_q": "",
                    "Str_h": "",
                    "ProValue": "",
                    "Adddate": "2016-06-17 10:46:17",
                    "Exlistshow_mfhdId": 7397419,
                    "proValueObj": {
                        "": ""
                    }
                }
            ]
        ]
        drag_table_list = defaultArr.map((list)=>{
            inputID = that._genUniqueKey();
            if(list[0].Dataname === 'table'){
                return {
                    ...dragConf,
                    InputID:inputID,
                    "W_": width,
                    "X_":5,
                    "Y_": top,
                    "H_":80,
                    proArray:list,
                    isTable:true
                }
            }
            return  {...dragConf,InputID:inputID,proArray:list}
        })
        comp.print.resources.creatKddTableHtml( [that.mockInputData],that.templateData, drag_table_list)
        drag_table_list.forEach((obj)=>{
            drag = new DragElement(
                obj,
                that.globalSetting,
                '', false, that.mockInputData
            );
            drag.init(that.tplDefaultItem, that.viewMode, that.mockInputData);
            that.dragElements.push(drag);
            obj.isTable && (tableDrag = drag)
        })
        that._setActiveDrag(tableDrag)
        that._bindAutoChangeHeightEvent()
    }
    PrintTemplate.prototype._addFhdTable = function(){
        let that = this
        ,$dom = this.$dom
        ,tableDrag
        ,dragConf
        ,drag
        ,drag_table_list = []
        ,left = 5
        ,top = 48
        ,width = that.templateData.ModeTempPrintcfg.Width * pxScale - 10
        ,inputID = that._genUniqueKey();

        const autoLimitInfo = this._getAutoLimitInfo();

        // 自定义区域配置
        if (autoLimitInfo) {
            const {topMin, topMax, leftMin} = autoLimitInfo;
            top = topMin + 10 > topMax ? topMin : topMin + 10;  // (topMin + 10)超出topMax时，取topMin
            left = leftMin ?? 72;
        }

        dragConf = {
            "DataKey": "",
            "Exlistshow_mfhdId": 0,
            "Exlistshow_mkddId": 0,
            "Exuserid": that.exuid,
            "Fontname": "宋体",
            "Fontsize": 14,
            "H_": 99,
            "Hjj": -1,
            "InputID": inputID,
            "Inputtype": "FHD",
            "IsDraggable": true,
            "IsEditable": true,
            "isTable":true,
            "Isb_n": -1,
            "Isedit": 1,
            "Status": 1,
            "Str_h": "",
            "Str_q": "前文字",
            "W_": width,
            "X_": 3,
            "Y_": top,
            "H_":80,
            "Zjj": -1,
            "created": null,
            "modified": null,
            "proArray": [
              {
                "Adddate": "2016-08-05 09:51:22",
                "Dataname": "table",
                "Exuserid": null,
                "InputID": 1039554,
                "InputProID": 1015816,
                "Inputtype": "FHD",
                "Mode_listShow_fhdId": 41463,
                "Mode_listShow_kddId": 41463,
                "ProValue": "rgCount＝5，rgH＝15，lineBroder＝2，lineTitle＝2，lineHj＝2，lineRow＝2，lineRg＝2",
                "Str_h": "",
                "Str_q": "",
                "modified": "2023-01-31 16:39:51"
              }
            ]
          }
       that.templateData.ModeInputs.push(dragConf)
        comp.print.resources.getFhdTableHtml( [that.mockInputData],that.templateData)
        drag_table_list.forEach((obj)=>{
            drag = new DragElement(
                obj,
                that.globalSetting,
                '', false, that.mockInputData
            );
            drag.init(that.tplDefaultItem, that.viewMode, that.mockInputData);
            that.dragElements.push(drag);
            obj.isTable && (tableDrag = drag)
        })
        drag = new DragElement(
            dragConf,
            that.globalSetting,
            '', false, that.mockInputData
        );
        $dom.find('input[name="editTable"]')
        .removeAttr('disabled')
        .css('cursor', 'pointer'); //编辑表格
        drag.init(that.tplDefaultItem, that.viewMode, that.mockInputData);
        that.dragElements.push(drag);
        dragConf.isTable && (tableDrag = drag)
        that._setActiveDrag(tableDrag)
        that._bindAutoChangeHeightEvent()
    }
    PrintTemplate.prototype._addOtherTable = function(){
        let that = this
        ,$dom = this.$dom
        ,tableDrag
        ,dragConf
        ,drag
        ,drag_table_list = []
        ,left = 5
        ,top = 48
        ,width = that.templateData.ModeTempPrintcfg.Width * pxScale - 10
        ,inputID = that._genUniqueKey();

        const autoLimitInfo = this._getAutoLimitInfo();

        // 自定义区域配置
        if (autoLimitInfo) {
            const {topMin, topMax, leftMin} = autoLimitInfo;
            top = topMin + 10 > topMax ? topMin : topMin + 10;  // (topMin + 10)超出topMax时，取topMin
            left = leftMin ?? 72;
        }

        dragConf = {
            "DataKey": "",
            "Exlistshow_mfhdId": 0,
            "Exlistshow_mkddId": 0,
            "Exuserid": that.exuid,
            "Fontname": "宋体",
            "Fontsize": 14,
            "H_": 99,
            "Hjj": -1,
            "InputID": inputID,
            "Inputtype": "CGD",
            "IsDraggable": true,
            "IsEditable": true,
            "isTable":true,
            "Isb_n": -1,
            "Isedit": 1,
            "Status": 1,
            "Str_h": "",
            "Str_q": "前文字",
            "W_": width,
            "X_": 3,
            "Y_": top,
            "H_":80,
            "Zjj": -1,
            "created": null,
            "modified": null,
            "proArray": [
              {
                "Adddate": "2016-08-05 09:51:22",
                "Dataname": "table",
                "Exuserid": null,
                "InputID": 1039554,
                "InputProID": 1015816,
                "Inputtype": "FHD",
                "Mode_listShow_fhdId": 41463,
                "Mode_listShow_kddId": 41463,
                "ProValue": "rgCount＝5，rgH＝15，lineBroder＝2，lineTitle＝2，lineHj＝2，lineRow＝2，lineRg＝2",
                "Str_h": "",
                "Str_q": "",
                "modified": "2023-01-31 16:39:51"
              }
            ]
          }
    //   let aa = [
    //         {
    //             "DataKey":"",
    //             "Exlistshow_mfhdId":51,
    //             "Exlistshow_mkddId":51,
    //             "Exuserid":"1234568",
    //             "Fontname":"",
    //             "Fontsize":0,
    //             "H_":10,
    //             "Hjj":-1,
    //             "InputID":25779,
    //             "Inputtype":"FHD",
    //             "IsDraggable":true,
    //             "IsEditable":true,
    //             "Isb_n":-1,
    //             "Isedit":1,
    //             "Status":1,
    //             "Str_h":"",
    //             "Str_q":"",
    //             "W_":10,
    //             "X_":-100,
    //             "Y_":0,
    //             "Zjj":-1,
    //             "created":null,
    //             "modified":null,
    //             "proArray":[
    //                 {
    //                     "Adddate":"2023-07-17 14:29:35",
    //                     "Dataname":"table_title",
    //                     "Exuserid":"1234568",
    //                     "InputID":25779,
    //                     "InputProID":27319,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"iss＝1，dq＝2",
    //                     "Str_h":"",
    //                     "Str_q":"",
    //                     "modified":"2023-07-17 14:34:58"
    //                 }
    //             ]
    //         },
    //         {
    //             "DataKey":"",
    //             "Exlistshow_mfhdId":51,
    //             "Exlistshow_mkddId":51,
    //             "Exuserid":"1234568",
    //             "Fontname":"",
    //             "Fontsize":0,
    //             "H_":10,
    //             "Hjj":-1,
    //             "InputID":25780,
    //             "Inputtype":"FHD",
    //             "IsDraggable":true,
    //             "IsEditable":true,
    //             "Isb_n":-1,
    //             "Isedit":1,
    //             "Status":1,
    //             "Str_h":"",
    //             "Str_q":"",
    //             "W_":10,
    //             "X_":-100,
    //             "Y_":0,
    //             "Zjj":-1,
    //             "created":null,
    //             "modified":null,
    //             "proArray":[
    //                 {
    //                     "Adddate":"2023-07-17 14:29:35",
    //                     "Dataname":"table_hj",
    //                     "Exuserid":"1234568",
    //                     "InputID":25780,
    //                     "InputProID":27320,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"iss=0",
    //                     "Str_h":"",
    //                     "Str_q":"",
    //                     "modified":"2023-07-17 14:34:58"
    //                 },
    //                 {
    //                     "Adddate":"2023-07-17 14:29:35",
    //                     "Dataname":"hj_sl",
    //                     "Exuserid":"1234568",
    //                     "InputID":25780,
    //                     "InputProID":27321,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"iss＝1",
    //                     "Str_h":"",
    //                     "Str_q":"数量:",
    //                     "modified":"2023-07-17 14:34:58"
    //                 },
    //                 {
    //                     "Adddate":"2023-07-17 14:29:35",
    //                     "Dataname":"hj_yf",
    //                     "Exuserid":"1234568",
    //                     "InputID":25780,
    //                     "InputProID":27322,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"iss＝1",
    //                     "Str_h":"",
    //                     "Str_q":"运费:",
    //                     "modified":"2023-07-17 14:34:58"
    //                 },
    //                 {
    //                     "Adddate":"2023-07-17 14:29:35",
    //                     "Dataname":"hj_yh",
    //                     "Exuserid":"1234568",
    //                     "InputID":25780,
    //                     "InputProID":27323,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"iss＝1",
    //                     "Str_h":"",
    //                     "Str_q":"优惠:",
    //                     "modified":"2023-07-17 14:34:58"
    //                 },
    //                 {
    //                     "Adddate":"2023-07-17 14:29:35",
    //                     "Dataname":"hj_sf",
    //                     "Exuserid":"1234568",
    //                     "InputID":25780,
    //                     "InputProID":27324,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"iss＝1",
    //                     "Str_h":"",
    //                     "Str_q":"实付金额:",
    //                     "modified":"2023-07-17 14:34:58"
    //                 }
    //             ]
    //         },
    //         {
    //             "DataKey":"",
    //             "Exlistshow_mfhdId":51,
    //             "Exlistshow_mkddId":51,
    //             "Exuserid":"1234568",
    //             "Fontname":"",
    //             "Fontsize":0,
    //             "H_":10,
    //             "Hjj":-1,
    //             "InputID":25781,
    //             "Inputtype":"FHD",
    //             "IsDraggable":true,
    //             "IsEditable":true,
    //             "Isb_n":-1,
    //             "Isedit":1,
    //             "Status":1,
    //             "Str_h":"",
    //             "Str_q":"",
    //             "W_":10,
    //             "X_":-100,
    //             "Y_":0,
    //             "Zjj":-1,
    //             "created":null,
    //             "modified":null,
    //             "proArray":[
    //                 {
    //                     "Adddate":"2023-07-17 14:29:35",
    //                     "Dataname":"table_r_0",
    //                     "Exuserid":"1234568",
    //                     "InputID":25781,
    //                     "InputProID":27325,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"tit=序号，rgW=30，dq=2",
    //                     "Str_h":"",
    //                     "Str_q":"",
    //                     "modified":"2023-07-17 14:34:58"
    //                 },
    //                 {
    //                     "Adddate":"2023-07-17 14:29:35",
    //                     "Dataname":"tb_xh",
    //                     "Exuserid":"1234568",
    //                     "InputID":25781,
    //                     "InputProID":27326,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"",
    //                     "Str_h":"",
    //                     "Str_q":"",
    //                     "modified":"2023-07-17 14:34:58",
    //                     "proValueObj":{
    //                         "":""
    //                     }
    //                 }
    //             ]
    //         },
    //         {
    //             "DataKey":"",
    //             "Exlistshow_mfhdId":51,
    //             "Exlistshow_mkddId":51,
    //             "Exuserid":"1234568",
    //             "Fontname":"",
    //             "Fontsize":0,
    //             "H_":10,
    //             "Hjj":1,
    //             "InputID":25782,
    //             "Inputtype":"FHD",
    //             "IsDraggable":true,
    //             "IsEditable":true,
    //             "Isb_n":-1,
    //             "Isedit":1,
    //             "Status":1,
    //             "Str_h":"",
    //             "Str_q":"",
    //             "W_":10,
    //             "X_":-100,
    //             "Y_":0,
    //             "Zjj":0,
    //             "created":null,
    //             "modified":null,
    //             "proArray":[
    //                 {
    //                     "Adddate":"2023-07-17 14:29:36",
    //                     "Dataname":"table_r_1",
    //                     "Exuserid":"1234568",
    //                     "InputID":25782,
    //                     "InputProID":27327,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"dq=-1，tit=商品商家编码，rgW=120",
    //                     "Str_h":"",
    //                     "Str_q":"",
    //                     "modified":"2023-07-17 14:34:58"
    //                 },
    //                 {
    //                     "Adddate":"2023-07-17 14:29:36",
    //                     "Dataname":"tb_spmc",
    //                     "Exuserid":"1234568",
    //                     "InputID":25782,
    //                     "InputProID":27328,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":51,
    //                     "Mode_listShow_kddId":51,
    //                     "ProValue":"",
    //                     "Str_h":"",
    //                     "Str_q":"",
    //                     "modified":"2023-07-17 14:34:58",
    //                     "proValueObj":{
    //                         "":""
    //                     }
    //                 }
    //             ]
    //         },
    //         {
    //             "DataKey":"",
    //             "Exlistshow_mfhdId":0,
    //             "Exlistshow_mkddId":0,
    //             "Exuserid":"1234568",
    //             "Fontname":"宋体",
    //             "Fontsize":14,
    //             "H_":80,
    //             "Hjj":-1,
    //             "InputID":4143422,
    //             "Inputtype":"FHD",
    //             "IsDraggable":true,
    //             "IsEditable":true,
    //             "isTable":true,
    //             "Isb_n":-1,
    //             "Isedit":1,
    //             "Status":1,
    //             "Str_h":"",
    //             "Str_q":"前文字",
    //             "W_":859.4,
    //             "X_":3,
    //             "Y_":48,
    //             "Zjj":-1,
    //             "created":null,
    //             "modified":null,
    //             "proArray":[
    //                 {
    //                     "Adddate":"2016-08-05 09:51:22",
    //                     "Dataname":"table",
    //                     "Exuserid":null,
    //                     "InputID":1039554,
    //                     "InputProID":1015816,
    //                     "Inputtype":"FHD",
    //                     "Mode_listShow_fhdId":41463,
    //                     "Mode_listShow_kddId":41463,
    //                     "ProValue":"rgCount＝5，rgH＝15，lineBroder＝2，lineTitle＝2，lineHj＝2，lineRow＝2，lineRg＝2",
    //                     "Str_h":"",
    //                     "Str_q":"",
    //                     "modified":"2023-01-31 16:39:51"
    //                 }
    //             ]
    //         }
    //     ]
       that.templateData.ModeInputs.push(dragConf)
    //    that.templateData.ModeInputs = aa
    //    drag_table_list = aa
        comp.print.resources.getFhdTableHtml( [that.mockInputData],that.templateData,that.templateData.ModeListShow.Modeid)
        drag_table_list.forEach((obj)=>{
            drag = new DragElement(
                obj,
                that.globalSetting,
                '', false, that.mockInputData
            );
            drag.init(that.tplDefaultItem, that.viewMode, that.mockInputData);
            that.dragElements.push(drag);
            obj.isTable && (tableDrag = drag)
        })
        drag = new DragElement(
            dragConf,
            that.globalSetting,
            '', false, that.mockInputData
        );
        $dom.find('input[name="editTable"]')
        .removeAttr('disabled')
        .css('cursor', 'pointer'); //编辑表格
        drag.init(that.tplDefaultItem, that.viewMode, that.mockInputData);
        that.dragElements.push(drag);
        dragConf.isTable && (tableDrag = drag)
        that._setActiveDrag(tableDrag)
        that._bindAutoChangeHeightEvent()
    }
    PrintTemplate.prototype._addTableItemDrag = function(objList){
        const that = this;
        let tableDrag;
        objList.forEach((obj)=>{
            const  drag = new DragElement(
                obj,
                that.globalSetting,
                '', false, that.mockInputData
            );
            drag.init(that.tplDefaultItem, that.viewMode, that.mockInputData);
            that.dragElements.push(drag);
            if(obj.proArray[0].Dataname==='table'){
                tableDrag = drag

            }
        })
        that._setActiveDrag(tableDrag)
    }
    /**
     * 模版默项
     * @param tplDefaultItem
     * @param viewMode (字符串) = '0' 查看状态，= '1' 编辑状态，= '2' 预览状态
     * @param mockInputData
     */
    DragElement.prototype.init = function (
        tplDefaultItem,
        viewMode,
        mockInputData,
    ) {
        this.tplDefaultItem = tplDefaultItem;
        this.mockInputData = mockInputData; //   //TODO fixme不用挂在到这里 这样会太浪费存储了
        //生成dom
        this._genDom(viewMode);
    };

    // DragElement.prototype.heightRevise = function(tplDefaultItem, viewMode, mockInputData){
    //     this.$dom.appendTo('')
    // };

    //销毁这个拖拽元素
    DragElement.prototype.destroy = function () {
        //1、移除模版数组中对应的数据
        //
        //2、删除dom，解绑事件
        this.getDom().off('click').remove();
    };

    //收集数据
    DragElement.prototype._gatherConf = function () {
        return this.conf;
    };

    DragElement.prototype.applySetConfPro = function (key, value) {
        let _pro = (this.conf.proArray || [])[0],
            _obj;
        if (!_pro) {
            return;
        }
        _obj = outerConf.getObjectFormProValue(_pro.ProValue);
        _obj[key] = value;
        this.conf.proArray[0].ProValue = _obj.toString();
    };

    //应用改动设置，针对右侧字体字号字体粗细等设置，
    DragElement.prototype.applySetConf = function (key, value, suffix) {
        var mapping = {
            width: 'W_',
            height: 'H_',
            top: 'Y_',
            left: 'X_',
            'font-family': 'Fontname',
            'font-size': 'Fontsize',
            'line-width': 'Str_h',
            'font-weight': 'Isb_n',
            'letter-spacing': 'Zjj',
            'line-height': 'Hjj',
            'changdu': (this.conf.H_ <= this.conf.W_ ? 'W_' : 'H_'),
            'xiantiaoleixing': 'Str_q',
        }, $dom, style = {}, style2 = {}, temp_h, temp_w, temp, _arr,
            defaultConf = this.globalSetting.ModeSet;

        $dom = this.getDom();

        //没有mapping取原值
        if (key != 'fangxiang' && key != 'blackWhite') {
            this.conf[mapping[key] || key] = value;
        }

        if (key === 'font-family') {
            if (!value) {
                style['font-family'] = defaultConf.Fontname;
            } else {
                style['font-family'] = value;
            }
        } else if (key === 'font-size') {
            if (value <= 1) {
                // style['font-size'] = defaultConf.Fontsize   'px';
                value = defaultConf.Fontsize;
            }
            //ie浏览器 或者字体大于12
            if (value >= 12 || window.navigator.userAgent.indexOf("MSIE") >= 1) {
                style['font-size'] = value + 'px';
                style['line-height'] = parseInt(value) + parseInt(this.conf.Hjj == -1 ? 0 : this.conf.Hjj) + 'px';
                style2['transform'] = 'scale(1)';
                style2['width'] = '100%';
                style2['height'] = '100%';
            } else {
                temp = (value / 12).toFixed(2);
                style['font-size'] = '12px';
                style['line-height'] = parseInt(value) + parseInt(this.conf.Hjj == -1 ? 0 : this.conf.Hjj) + 'px';
                style['transform-origin'] = 'left top 0px';
                style2['transform'] = 'scale(' + temp + ')';
                style2['width'] = (1 / temp) * 100 + '%';
                style2['height'] = (1 / temp) * 100 + '%';
            }
        } else if (key === 'font-weight') {
            style['font-weight'] = (value == '0' ? 'normal' : (value == '1' ? 'bold' : (defaultConf.Isb == 1 ? 'bold' : 'normal')));
        } else if (key === 'letter-spacing') {
            if (value == '-1') {
                style['letter-spacing'] = 'initial';
            } else {
                style['letter-spacing'] = value + 'px';
            }
        } else if (key === 'line-height') {
            if (value == '-1') {
                // style['line-height'] = 'initial';//fixme 这个有默认吗？
                value = 0;
            }
            //计算真实的行高 -------
            style['line-height'] = parseInt(this.conf.Fontsize > 0 ? this.conf.Fontsize : (defaultConf.Fontsize)) + parseInt(value || 0) + 'px';
        } else if ((key === 'Str_q' || key === 'Str_h') && (this.dragType === 'textInput' || this.dragType === 'dataInput')) {

            let dataName = (this.conf.proArray[0] || {}).Dataname || '';
            const dataNameList = this.conf.proArray.map(item => item.Dataname)
            //不是dataInput下的二维码条形码类目
            if (!outerConf.istxm(dataName) && !outerConf.isWaterLabel(dataName) && !outerConf.isewm(dataName)) {
                //if( dataName == 'ewm' || /(ewm_|txm_)/.test(dataName) ){
                // if(dataName != 'ewm_number' && dataName != 'ewm' && dataName != 'ewm_str' && dataName != 'txm_number' && dataName != 'txm_tid' && dataName != 'txm_jbm'){

                //重新生成 文字
                if (dataNameList.includes('f_info')) { //发货内容里有 html 片段用 html() 方法，其他的用 text()
                    $dom.find('.dragEdit').html(htmlStore.getDragText(this.conf, this.tplDefaultItem, this.mockInputData));
                } else {
                    $dom.find('.dragEdit').text(htmlStore.getDragText(this.conf, this.tplDefaultItem, this.mockInputData));
                }

            }

        } else if (key === 'Str_q' && this.dragType === 'image') {
            //图像
            $dom.find('.dragImg').attr('src', value.replace('?from=oss','')).show();
        } else if (key === 'fangxiang') {
            //线独有
            //fangxiang = 1是竖着
            temp_h = this.conf['H_'];
            temp_w = this.conf['W_'];
            //切换方向，则将宽高对换,

            style[value == 1 ? 'height' : 'width'] = (value == 1 ? temp_w : temp_h) + 'px';
            style[value == 1 ? 'width' : 'height'] = "auto";

            if (value == 1) {
                this.conf['H_'] = (temp_h > temp_w ? temp_h : temp_w);
                this.conf['W_'] = 0;
            } else {
                this.conf['H_'] = 0;
                this.conf['W_'] = (temp_h < temp_w ? temp_w : temp_h);
            }
            $dom.find('.dragEdit').removeClass('vertical horizontal').addClass(value == 1 ? 'vertical' : 'horizontal');

            if (this.conf.H_ < this.conf.W_) {
                style2['border-width'] = (+this.conf.Str_h || 1) + "px 0 0 0";
            } else {
                style2['border-width'] = "0 0 0 " + (+this.conf.Str_h || 1) + "px";
            }

            $dom.find('.dragTrigger').remove();
            // $dom.append(htmlStore.getDragTrigger(this.conf, 'line')).css('padding', value == 1 ? '0 5px' : '5px 0');
        } else if (key === 'changdu') {
            //conf.H_ == 1 横着的 < conf.W_横着的
            style[this.conf.H_ <= this.conf.W_ ? 'width' : 'height'] = value + 'px';
        } else if (key === 'xiantiaoleixing') {
            //线独有 0 实线 2 虚线
            //2018.6.13矩形也有该属性
            //0 实线   1：虚线 2： 点线
            value = +value || 0;
            _arr = ['solid', 'dashed', 'dotted'];
            // if(this.dragType == 'line'){

            //     style2[this.conf.H_ < this.conf.W_ ? 'border-top-style' : 'border-left-style'] = _arr[value]||'solid';
            // }
            // if(this.dragType == 'rect'){
            style2['border-style'] = _arr[value] || 'solid';
            // }
        } else if (key === 'deg') {
            style['transform'] = 'rotate(' + parseInt(value % 360) + 'deg)';
        } else if (key === 'blackWhite') {
            if (value == 1) {
                this.conf['bColor'] = '#000000';
                this.conf['color'] = '#ffffff';
            } else {
                this.conf['bColor'] = '';
                this.conf['color'] = '';
            }
            style2['background-color'] = value == 1 ? '#000000' : '#ffffff';
            style2['color'] = value == 1 ? '#ffffff' : '#000000';
        } else if (key === 'bColor') {
            style2['background-color'] = value || '#ffffff';
        } else if (key === 'alpha') {
            style2['opacity'] = value;
        } else if (key === 'italic') {
            style['font-style'] = value == 1 ? "italic" : "";
        } else if (key === 'under') {
            style2['text-decoration'] = value == 1 && this.conf.direct != 1 ? "underline" : "none";

        } else if (key === 'align') {
            _arr = ['flex-start', 'center', 'flex-end'];
            style2['display'] = 'flex';
            style2['justify-content'] = _arr[+value];
        } else if (key === 'valign') {
            _arr = ['flex-start', 'center', 'flex-end'];
            style2['display'] = 'flex';
            style2['align-items'] = _arr[+value];
        } else if (key === 'direct') {
            style['-ms-writing-mode'] = value == 1 ? 'tb-rl' : 'lr-tb';
            style['writing-mode'] = value == 1 ? 'vertical-rl' : 'horizontal-tb';
            style2['text-decoration'] = value != 1 && this.conf.under ? "underline" : "none";   //竖版不显示下滑线样式。
            //宽高对换
            var _w = this.conf['W_'];
            this.conf['W_'] = this.conf['H_'];
            this.conf['H_'] = _w;
            style['width'] = (this.conf['W_'] || '') + 'px';
            style['height'] = (this.conf['H_'] || '') + 'px';
        } else if (key === 'line-width') {
            // if(this.conf.H_ < this.conf.W_){
            //     style2['border-top-width'] = (value || 1)+"px";
            //     style2['border-left-width'] = "0px";
            // }else{
            //     style2['border-left-width'] = (value || 1)+"px";
            //     style2['border-top-width'] = "0px";
            // }
            // style2['border-width'] = "0px";
            // style2[this.conf.H_ < this.conf.W_? 'border-top-width':'border-left-width'] = (value || 1)+"px"
            if (this.conf.H_ < this.conf.W_) {
                style2['border-width'] = (value || 1) + "px 0 0 0";
            } else {
                style2['border-width'] = "0 0 0 " + (value || 1) + "px";
            }
        } else if (key === 'encry') { // 保密按钮
            $dom.find('.dragEdit').text(htmlStore.getDragText(this.conf, this.tplDefaultItem, this.mockInputData));
        }
        else {
            style[key] = (value || (['top','left'].includes(key) ? 0: '' )) + (suffix || '');
        }



        //dom中对应应用这个规则，基本上是改
        $dom.css(style).find('.dragEdit').css(style2);

    };

    //收集dom数据到conf，针对拖动导致的位移和宽高的变化
    DragElement.prototype._gatherDragConf = function () {
        //this.dom 收集数据，存储到this.conf
    };

    //根据配置，生成dom节点
    DragElement.prototype._genDom = function (viewMode) {
        const $html = $(
            htmlStore.getDrag(
                this.conf,
                this.globalSetting,
                this.tplDefaultItem,
                this.dragType,
                this.mockInputData,
                this.isReviseHeight,
            ),
        );

        //viewMode(字符串) = '0' 查看状态，= '1' 编辑状态，= '2' 预览状态
        if (viewMode == '0') {
            $html
            .css({
                border: 0,
                // 'background-color': 'transparent',
            })
            .find('.dragEdit')
            .removeAttr('contenteditable')
            .css({
                cursor: 'default',
                overflow: 'visible',
            });
        } else if (viewMode == '2') {
            $html.find('.dragEdit').attr('contenteditable', 'true').css({
                cursor: 'text',
                overflow: 'visible',
            });
        }
        this.$dom = $html;
    };

    //获得dom
    DragElement.prototype.getDom = function () {
        return this.$dom;
    };

    //设置拖拽状态
    DragElement.prototype.startDrag = function (boolStatus) {
        this.isDragging = !!boolStatus;
    };
    //设置拖拽状态
    DragElement.prototype.startScale = function (boolStatus) {
        const dragTrigger = htmlStore.getDragTrigger(this.conf, this.dragType);
        this.isScaling = !!boolStatus;

        if (boolStatus) {
            this.$dom.append(dragTrigger);
        } else {
            this.$dom.find('.dragTrigger').remove();
            const boxNode = this.$dom.parent().parent();
            setTimeout(()=>{PrintTemplate.prototype._doublePermutation(boxNode)},100)

        }
    };

    DragElement.prototype.modifyStyle = function (style) {
        this.$dom.find('[data-type="drag"]').css(style);
    };
    //添加打印数据项
    DragElement.prototype.addProData = function (obj, exuid, kddId) {
        let proItem,
            text,
            _proValue = {};
        //判断obj 二维码 ewm_number 条形码，和当前的
        //ItemType=barcode 是二维码和条形码
        //Key = ewm_number ewm txm_number txm_tid txm_jbm      gx_jbm 个性集包码 这个ItemType是barcode 但是是文字

        proItem = {
            Adddate: '',
            Dataname: obj.Key,
            Exuserid: exuid,
            InputID: this.conf.InputID,
            InputProID: ~~(10000000 * Math.random()), //新增的这个值是前端生成的，后端同学会从新生成，并且每次保存，所有的InputID都会改变（后端重新生成）,
            Inputtype: 'kdd',
            Mode_listShow_fhdId: 0,
            Mode_listShow_kddId: kddId,
            ProValue: '',
            Str_h: '',
            Str_q: '',
            modified: '',
        };
        this.conf.proArray.push(proItem);
        //重新生成对应的数据
        //fixme 此出这两个，从数据上已经保证了切换文字和切换二维码条形码proArray的正确，但是没有同步清除dom上checkBox，需要同步处理掉，设置正确的checkbox选择关系
        // if(obj.Key == 'ewm_number' || obj.Key == 'ewm' || obj.Key == 'ewm_str' || obj.Key == 'txm_number' || obj.Key == 'txm_tid' || obj.Key == 'txm_jbm'){
        // if( obj.key == 'ewm' || /(ewm_|txm_)/.test(obj.key) ){
        obj.ProValue &&
            (_proValue = outerConf.getObjectFormProValue(obj.ProValue));

        if (outerConf.istxm(obj.Key) || outerConf.isewm(obj.Key)) {
            //清空原有数组
            this.removeProData('', true);
            this.conf.proArray = [proItem];
            //按照图片类型进行处理

            // 早期模板使用了 'direction=1' 来设置数据框是否旋转
            // 后来有模板（百世快递网点一联单）要求单号条形码旋转，但此时必须使用 barcode_h.png 的图片，并且 'direction=1'也必须存在，所以加入了如下 imgName 取值的逻辑
            let imgName = 'barcode_v.png';
            if (this.conf.deg == 90) {
                imgName = 'barcode_h.png';
            }
            let txmName = obj.Key.includes('with_code') ? 'barcode_text.png' : 'barcode_h.png'

            text =
                '<img class="dragImg" draggable="false" src="https://static.kuaidizs.cn/resources/img/templateEdit/' +
                (obj.Key.indexOf('ewm') > -1
                    ? 'qrcode.png'
                    : _proValue.direction == 1
                        ? imgName
                        : txmName) +
                '" data-input-id="' +
                this.conf.InputID +
                '" style="width: 100%;height: 100%;" alt=""/>';
            this.$dom.find('.dragEdit').html(text);
        } else if(outerConf.isWaterLabel(obj.Key)){
            //清空原有数组
            this.removeProData('', true);
            this.conf.proArray = [proItem];
            let imgObj = {
                no_bleach_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/no_bleach_care_label.png',
                dry_flat_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/dry_flat_care_label.png',
                dry_clean_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/dry_clean_care_label.png',
                ironing_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/ironing_care_label.png',
                dry_washa_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/dry_washa_care_label.png',
                not_dry_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/not_dry_care_label.png',
                washable_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/washable_care_label.png',
                bleaching_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/bleaching_care_label.png',
                hand_wash_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/hand_wash_care_label.png',
                th_wash_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/thirty_water_wash_care_label.png',
                vertical_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/vertical_drying_care_label.png'
            }
            text =
                '<img class="dragImg" draggable="false" src="'+ imgObj[obj.Key] +'" data-input-id="' +
                this.conf.InputID +
                '" style="width: 100%;height: 100%;" alt=""/>';
            this.$dom.find('.dragEdit').html(text);
        } else {
            //清除二维码条形码类数据，只留下数据框
            this.removeProData('ewm_number');
            this.removeProData('ewm');
            this.removeProData('ewm_str');
            this.removeProData('txm_number');
            this.removeProData('txm_tid');
            this.removeProData('txm_jbm');
        }
        // if(_proValue && _proValue.isBlack || obj.Key == 'dshk_black'){
        //     this.modifyStyle({'color':'#fff','background-color':'#000000'})
        // }else{
        //     this.modifyStyle({'color':'#000000','background-color':'transparent'})
        // }
    };
    //移除打印数据项
    DragElement.prototype.removeProData = function (dataName, isAll) {
        let text = '',
            i;
        //移除
        if (dataName || isAll) {
            for (i = 0; i < this.conf.proArray.length; i++) {
                if (this.conf.proArray[i].Dataname == dataName || isAll) {
                    if (dataName) {
                        this.$dom
                        .find(`.dragDataItem[data-key=${dataName}]`)
                        .prop('checked', false);
                    } else {
                        this.$dom.find(`.dragDataItem`).prop('checked', false);
                    }
                    this.conf.proArray.splice(i, 1);
                    break;
                }
            }
        }

        if (this.conf.proArray.length) {
            text = htmlStore.getDragText(
                this.conf,
                this.tplDefaultItem,
                this.mockInputData,
            );
        }

        this.$dom.find('.dragEdit').html(text);
    };

    htmlStore = {
        getDragOtherSet: function (dataName) {
            let isHide = false;
            const cfg = comp.Print.Data.currentTempInfo.ModeTempPrintcfg;
            if (
                ('img_logo_qs' === dataName && !cfg.Qslogo) ||
                ('img_logo_lc' === dataName && !cfg.Lclogo)
            ) {
                isHide = true;
            }
            return { isHide };
        },

        getDragText: function (conf, tplDefaultItem, mockInputData) {
            let text = '',
                i,
                j,
                k,
                _item;

            /**修改数据
             * 用于加密数据和菜鸟模板发货内容 Text 的改变
             *
             * @param {*} text  需要加密的内容
             * @param {*} conf 配置信息
             */
            const changeTextFn = function (text, conf, dataName) {
                let changedText = '',
                    length = (text || '').length;
                const currentTempInfo = comp.Print.Data.currentTempInfo;

                changedText = text;
                // 模板上的加密数据
                if (
                    text &&
                    Number(conf.encry) &&
                    comp.Print.Data.encryList.indexOf(dataName) > -1
                ) {
                    if (length >= 8) {
                        //大于8位，则加密倒数5位到倒数第8位
                        changedText =
                            text.slice(0, text.length - 8) +
                            '****' +
                            text.slice(-4);
                    } else {
                        // 如果小于8位，则加密后四位
                        changedText = text.replace(/\S{4}$/, '****');
                    }
                }

                // 发货内容 如果是菜鸟模板的话，修改text\
                if (
                    dataName == 'f_info' &&
                    [3,7,8,9].includes(currentTempInfo.ModeListShow.KddType)
                ) {
                    changedText = `发货内容<span class="wakeUpFInfoSetting" style="color:#FF0000;display:block;pointer-events: none;">(宝贝大于5个时,宝贝信息打印在第二张面单上,双击查看)</span>`;
                }

                return changedText;
            };

            for (i = 0; i < conf.proArray.length; i++) {
                _item = conf.proArray[i];
                if (i > 0) {
                    text += ' ';
                }
                text +=
                    _item.Str_q +
                    (changeTextFn(
                        mockInputData[_item.Dataname],
                        conf,
                        _item.Dataname,
                    ) || '') +
                    _item.Str_h;
            }

            text = (conf.Str_q || '') + text + (conf.Str_h || '');

            return text;
        },

        createServiceHtml: function (serviceItems, printData, exCode) {
            let content = '';
            if (exCode == 'SF') {
                serviceItems &&
                    $.each(serviceItems, function (j, item) {
                        if (item.Issetval == 1) {
                            if (
                                item.Itemname == '保价金额' &&
                                item.Reserve1 &&
                                item.Reserve1 != '保价金额'
                            ) {
                                //保价金额特殊处理
                                content +=
                                    item.Itemname +
                                    ':' +
                                    item.Reserve1 +
                                    item.Unit;
                            } else {
                                content +=
                                    item.Itemname +
                                    ':' +
                                    (printData[item.Dataname] || '') +
                                    item.Unit;
                            }
                        } else {
                            content +=
                                item.Itemname + ':' + (item.Defaultval || '');
                        }
                        content +=
                            (j + 1) % 2 == 0
                                ? '<br>'
                                : '&nbsp;&nbsp;&nbsp;&nbsp;';
                    });
            } else {
                content =
                    '<div style=\'font-size:10px;\'><ul style=\'list-style:none;margin:0;padding:0;\'>';
                $.each(serviceItems, function (j, item) {
                    content +=
                        '<li style=\'width:110px;float:left;margin:0;padding:0\'>';
                    if (item.Issetval == 1) {
                        if (
                            item.Itemname == '保价金额' &&
                            item.Reserve1 &&
                            item.Reserve1 != '保价金额'
                        ) {
                            //保价金额特殊处理
                            content +=
                                item.Itemname + ':' + item.Reserve1 + item.Unit;
                        } else {
                            content +=
                                item.Itemname +
                                ':' +
                                (printData[item.Dataname] || '') +
                                item.Unit;
                        }
                    } else {
                        content += item.Itemname + ':' + item.Defaultval;
                    }
                    content += '</li>';
                });
                content += '</ul></div>';
            }

            return content;
        },

        getDrag: function (
            conf,
            globalSetting,
            tplDefaultItem,
            dragType,
            mockInputData,
            isRevise,
        ) {
            let html,
                style,
                style2 = '',
                fontFamily,
                fontSize,
                lineHeight,
                fontWeight,
                letterSpacing,
                text,
                defaultConf,
                dataName,
                temp,
                _proValue,
                style2_disPlay,
                blackAndWhite = '',
                scripConvertCode='',
                _arrCss;
            let img_url;
            if (isRevise === '2' && dragType === 'dataInput') {
                return '';
            }
            if(conf.style === 'BLACK_WHITE'){
                blackAndWhite = 'background-color:black;color:white;'
            }
            if(['txm','ewm'].includes(conf.scripConvertCode)){
                scripConvertCode = `color:rgba(0,0,0,0);background:url(https://static.kuaidizs.cn/resources/img/templateEdit/${conf.scripConvertCode === 'txm' ? 'barcode_h' : 'qrcode'}.png) no-repeat;background-size:100% 100%;`
            }
            defaultConf = globalSetting.ModeSet;

            fontFamily = conf.Fontname ? conf.Fontname : defaultConf.Fontname;
            fontSize =
                conf.Fontsize > 1 || conf.Fontsize == -99
                    ? conf.Fontsize
                    : defaultConf.Fontsize;
            lineHeight = ~~fontSize + ~~(conf.Hjj == -1 ? 0 : conf.Hjj);
            letterSpacing = conf.Zjj >= 0 ? conf.Zjj : 0;
            fontWeight =
                conf.Isb_n == '0'
                    ? 'normal'
                    : conf.Isb_n == '1'
                        ? 'bold'
                        : defaultConf.Isb == 1
                            ? 'bold'
                            : 'normal';
            style2_disPlay = conf.align ? 'display:flex;' : '';
            _proValue =
                outerConf.getObjectFormProValue(
                    (conf.proArray[0] || {}).ProValue || '',
                ) || {};

            style =
                'font-family: ' +
                fontFamily +
                '; font-weight:' +
                fontWeight +
                ';' +
                ' letter-spacing:' +
                letterSpacing +
                'px;' +
                'width: ' +
                conf['W_'] +
                'px;top: ' +
                conf['Y_'] +
                'px; left: ' +
                conf['X_'] +
                'px;' + blackAndWhite +scripConvertCode;
            if (dragType == 'tableHtml') {
                style +=
                    'height:' +
                    (conf.Inputtype==="kdd" ? 'auto;' : (mockInputData.tableHeight || conf['H_'])) +
                    'px;';
            } else if (dragType == 'dataInput' && isRevise === '1') {
                //要不针对指定可计算项
                style += 'height:auto;';
            } else {
                style += 'height: ' + conf['H_'] + 'px;';
            }
            style2 += style2_disPlay;

            //ie浏览器 或者字体大于12
            if (fontSize == -99) {
                style2 +=
                    'transform-origin: left top 0px; transform: scale(1);';
            } else if (
                fontSize >= 12 ||
                window.navigator.userAgent.indexOf('MSIE') >= 1
            ) {
                style +=
                    'font-size: ' +
                    fontSize +
                    'px;line-height: ' +
                    lineHeight +
                    'px;';
                style2 +=
                    'transform-origin: left top 0px; transform: scale(1);';
            } else {
                //chrome不支持小于12像素问题处理
                temp = (fontSize / 12).toFixed(2);
                style += 'font-size: 12px; line-height: ' + lineHeight + 'px;';
                style2 +=
                    'font-size: 12px;transform-origin: left top 0px;transform: scale(' +
                    temp +
                    ');width: ' +
                    (1 / temp) * 100 +
                    '%;height: ' +
                    (1 / temp) * 100 +
                    '%;';
            }

            if (dragType === 'line') {
                // 宽是1是竖线，H_=1是横线 改为宽小于高是横线
                //- 6 -1 是为了修复margin和padding引起的偏移工作
                const _isHorizontal = conf.H_ < conf.W_;
                _arrCss = ['solid', 'dashed', 'dotted'];
                html = [
                    '<div class="dragElement dragLine" data-input-id="' +
                    conf.InputID +
                    '" ' + //data-is-draggable="true"
                        'style="width: ' +
                        (_isHorizontal ? conf['W_'] + 'px;' : 'auto;') +
                        ' height: ' +
                        (!_isHorizontal ? conf['H_'] + 'px;' : 'auto;') +
                        ' top:' +
                        conf['Y_'] +
                        'px;' +
                        ' left:' +
                        conf['X_'] +
                        'px;' +
                        // +' top:'+(_isHorizontal ? conf['Y_'] - 6 : conf['Y_'] - 1) +'px;'
                        // +' left:'+(_isHorizontal? conf['X_'] - 1 : conf['X_'] - 6) +'px;'
                        (conf.W_ > 1 && conf.H_ > 1
                            ? 'transform:rotate(' +
                              (Math.atan(conf.H_ / conf.W_) / (2 * Math.PI)) *
                                  360 +
                              'deg);'
                            : '') +
                        '">' +
                        // +( !_isHorizontal ? 'padding: 0 5px;' : 'padding: 5px 0;')  +'">'
                        '<div tabindex="0" class="dragEdit ' +
                        (!_isHorizontal ? 'vertical' : 'horizontal') +
                        '" data-input-id="' +
                        conf['InputID'] +
                        '" style="border-style:' +
                        _arrCss[+conf.Str_q || 0] +
                        ';border-width:' +
                        (_isHorizontal
                            ? (+conf.Str_h || 1) + 'px 0 0 0;'
                            : '0 0 0 ' + (+conf.Str_h || 1) + 'px;') +
                        '" >' +
                        '</div>' +
                        '<span class="deleteIcon"></span>',
                    +'</div>',
                ];
            } else if (dragType === 'logo') {
                dataName = (conf.proArray[0] || {}).Dataname || '';
                img_url = this.getDragText(conf, tplDefaultItem, mockInputData);
                const { isHide } = this.getDragOtherSet(dataName);
                style += isHide ? 'display:none;' : '';
                html = [
                    '<div class="dragElement" id="' +
                        dataName +
                        '" style="' +
                        style +
                        '">',
                    '<div tabindex="0" class="dragEdit" data-type="drag" data-input-id="' +
                        conf['InputID'] +
                        '">', //data-is-draggable="true"
                    '<img class="dragImg" draggable="false" src="' +
                        mockInputData[dataName] +
                        '" data-input-id="' +
                        conf['InputID'] +
                        '" style="width: 100%;height: 100%;" alt=""/>',
                    '</div>',
                    '<span class="deleteIcon"></span>',
                    '</div>',
                ];
            } else if (dragType === 'image') {
                img_url = (conf.Str_q || '')
                .replace(
                    /^(\/resources\/img\/print|\/img\/print)/,
                    '/resources/img/print',
                )
                .replace('print/Eximg', 'print/ExImg');
                const iswholeUrl = /^http|https$/.test(img_url);
                console.log('isholeUrl--',iswholeUrl);
                html = [
                    '<div class="dragElement" style="' + style + '"  >',
                    '<div tabindex="0" class="dragEdit" data-type="drag" data-input-id="' +
                        conf['InputID'] +
                        '">', //data-is-draggable="true"
                    '<img class="dragImg" draggable="false" src="' + (iswholeUrl ? '' : 'https://static.kuaidizs.cn') + //完整的图片路径就不走static静态资源
                        img_url +
                        '" data-input-id="' +
                        conf['InputID'] +
                        '" style="width: 100%;height: 100%;' +
                        (!img_url ? 'display:none;' : '') +
                        '" alt=""/>',
                    '</div>',
                    '<span class="deleteIcon"></span>',
                    '</div>',
                ];
            } else if (dragType === 'rect') {
                _arrCss = ['solid', 'dashed', 'dotted'];
                style2 +=
                    (conf.bColor
                        ? 'background-color:' + conf.bColor + ';'
                        : '') +
                    ('border:1px ' + _arrCss[+conf.Str_q || 0] + ' #000;');
                html = [
                    '<div class="dragElement dragRect" style="' + style + '">',
                    '<div tabindex="0" style="' +
                        style2 +
                        '" class="dragEdit" ' +
                        (conf.IsEditable ? 'contenteditable="false"' : '') +
                        ' data-type="drag" data-input-id="' +
                        conf['InputID'] +
                        '">', //data-is-draggable="'+ conf.IsEditable +'"
                    '</div>',
                    '<span class="deleteIcon"></span>',
                    '</div>',
                ];
            } else if (dragType === 'tableHtml') {
                html = [
                    '<div class="dragElement" style="' + style + '">',
                    '<div tabindex="0" class="dragEdit dragHTML" data-type="drag" data-input-id="' +
                        conf['InputID'] +
                        '">', //data-is-draggable="true"
                    mockInputData.tableHtml,
                    '</div>',
                    '</div>',
                ];
            } else {
                dataName = (conf.proArray[0] || {}).Dataname || '';
                if (
                    conf.proArray.length == 1 &&
                    (outerConf.isewm(dataName) || outerConf.istxm(dataName))
                ) {
                    // 早期模板使用了 'direction=1' 来设置数据框是否旋转
                    // 后来有模板（百世快递网点一联单）要求单号条形码旋转，但此时必须使用 barcode_h.png 的图片，并且 'direction=1'也必须存在，所以加入了如下 imgName 取值的逻辑
                    let imgName = 'barcode_v.png';
                    if (conf.deg == 90) {
                        imgName = 'barcode_h.png';
                    }
                    let txmName = dataName.includes('with_code') ? 'barcode_text.png' : 'barcode_h.png'
                    text =
                        '<img class="dragImg" draggable="false" src="https://static.kuaidizs.cn/resources/img/templateEdit/' +
                        (dataName.indexOf('ewm') > -1
                            ? 'qrcode.png'
                            : _proValue.direction == 1
                                ? imgName
                                : txmName) +
                        '" data-input-id="' +
                        conf.InputID +
                        '" style="width: 100%;height: 100%;" alt=""/>';
                }else if(outerConf.isWaterLabel(dataName)){
                    let imgObj = {
                        no_bleach_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/no_bleach_care_label.png',
                        dry_flat_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/dry_flat_care_label.png',
                        dry_clean_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/dry_clean_care_label.png',
                        ironing_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/ironing_care_label.png',
                        dry_washa_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/dry_washa_care_label.png',
                        not_dry_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/not_dry_care_label.png',
                        washable_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/washable_care_label.png',
                        bleaching_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/bleaching_care_label.png',
                        hand_wash_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/hand_wash_care_label.png',
                        th_wash_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/thirty_water_wash_care_label.png',
                        vertical_care_label:'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/vertical_drying_care_label.png'
                    }
                    text = `<img class="dragImg" draggable="false" src="${imgObj[dataName]}" data-input-id="${conf.InputID}" style="width: 100%;height: 100%;" alt=""/>`

                } else {
                    text = this.getDragText(
                        conf,
                        tplDefaultItem,
                        mockInputData,
                    );
                }
                // 字体大小自适应
                if (fontSize == -99) {
                    fontSize = comp.base.getAutoFontSize({
                        width: conf.W_,
                        content: text,
                        isBold: fontWeight === 'bold',
                        fontName: fontFamily,
                    });
                    style += 'font-size: ' + fontSize + 'px;line-height: 100%;';
                }
                if (
                    dataName == 'dshk_black' ||
                    _proValue.isBlack ||
                    (conf.bColor === '#000000' && conf.color === '#ffffff')
                ) {
                    style2 += ' background-color:black;color:white;';
                }

                style +=
                    'transform: rotate(' +
                    (conf.deg || 0) +
                    'deg);' +
                    (conf.italic == 1 ? 'font-style: italic;' : '') +
                    (conf.direct == 1
                        ? 'writing-mode: vertical-rl;-ms-writing-mode:tb-rl;'
                        : '');


                _arrCss = ['flex-start', 'center', 'flex-end'];

                style2 +=
                    'opacity: ' +
                    (conf.alpha || 1) +
                    ';' +
                    (conf.under == 1 && conf.direct != 1
                        ? 'text-decoration:underline;'
                        : '') +
                    (conf.valign
                        ? 'align-items:' + _arrCss[conf.valign] + ';'
                        : '') +
                    (conf.align
                        ? 'justify-content:' + _arrCss[conf.align] + ';'
                        : '');

                let isShowEdit = true;
                try {
                    if(['SZKKE'].includes(comp.Print.Data.currentTempInfo?.ModeList.Excode) && conf.Isedit === 0) {
                        isShowEdit = false;
                    }
                } catch(e) {
                    console.log(e);
                }

                html = [
                    `<div class="dragElement ${['txm','ewm'].includes(conf.scripConvertCode) ? 'scripConvertCode' : '' }" style="` +
                        style +
                        '" data-name="' +
                        dataName +
                        '">',
                    '<div tabindex="0" style="' +
                        style2 +
                        '" class="dragEdit" ' +
                        (conf.IsEditable ? 'contenteditable="false"' : '') +
                        ' data-type="drag" data-input-id="' +
                        conf['InputID'] +
                        '">', //data-is-draggable="'+ conf.IsEditable +'"
                    text.replace(/\n/g, '<br>'),
                    '</div>',
                    // Isedit为0时，不可编辑
                    // 暂时只对京广做限制
                    isShowEdit ? '<div class="deleteIcon iconfont ifont-delete"></div>' : '',
                    '</div>',
                ];
            }

            return html.join('');
        },

        //拖拽缩放触发点
        getDragTrigger: function (conf, dragType) {
            let dragTrigger,
                act =
                    'data-type="drag" data-input-id="' + conf['InputID'] + '"';

            dragTrigger = [
                '<div ' +
                    act +
                    ' class="dragTrigger" data-scale-type="top"></div>',
                '<div ' +
                    act +
                    ' class="dragTrigger" data-scale-type="bottom"></div>',
                '<div ' +
                    act +
                    ' class="dragTrigger" data-scale-type="left"></div>',
                '<div ' +
                    act +
                    ' class="dragTrigger" data-scale-type="right"></div>',
                '<div ' +
                    act +
                    ' class="dragTrigger" data-scale-type="topLeft"></div>',
                '<div ' +
                    act +
                    ' class="dragTrigger" data-scale-type="topRight"></div>',
                '<div ' +
                    act +
                    ' class="dragTrigger" data-scale-type="bottomLeft"></div>',
                '<div ' +
                    act +
                    ' class="dragTrigger" data-scale-type="bottomRight"></div>',
            ];
            if(dragType === 'tableHtml'){
                dragTrigger = [
                    '<div ' + act + ' class="dragTrigger" data-scale-type="top-disabled" style="top: -5px;left: 50%;margin-left: -3px;cursor:not-allowed"></div>',
                    '<div ' + act + ' class="dragTrigger" data-scale-type="top-disabled" style="bottom: -5px;left: 50%;margin-left: -3px;cursor:not-allowed"></div>',
                    '<div ' + act + ' class="dragTrigger" data-scale-type="left"></div>',
                    '<div ' + act + ' class="dragTrigger" data-scale-type="right"></div>',
                    '<div ' + act + ' class="dragTrigger" data-scale-type="left" style="top: -5px;left: -5px;margin-top:0"></div>',
                    '<div ' + act + ' class="dragTrigger" data-scale-type="right" style="top: -5px;right: -5px;margin-top:0"></div>',
                    '<div ' + act + ' class="dragTrigger" data-scale-type="left" style="bottom: -5px;left: -5px;top:100%" ></div>',
                    '<div ' + act + ' class="dragTrigger" data-scale-type="right" style="bottom: -5px;right: -5px;top:100%"></div>'
                ];
            }
            if (dragType == 'line') {
                //如果是线条，只展示两个，并且确定是横着的还是竖着的
                dragTrigger.length = 4;
                if (conf.W_ < conf.H_) {
                    //竖着
                    dragTrigger.length = 2;
                } else {
                    //横着
                    dragTrigger.splice(0, 2);
                }
            }

            return dragTrigger.join('');
        },
        //生成电子面单的某些服务选项
        _getExService: function (tplData) {
            // 屏蔽抖音服务
            if (tplData.ModeListShow.KddType === 8) {
                return '';
            }

            // ERP暂时不需要展示
            if(comp.Print.Data.platform === 'erp'){
                return '';
            }

            let service,
                html = '',
                exCode,
                findValue,
                _vals,
                _ohtml,
                _isDisabled = false,
                _isCheck,
                _kddType = tplData.ModeList.KddType,
                _styleid = tplData.ModeList.StyleId;

            //   if(  tplData.ModeList.KddType == 1 || !(_styleid <= 2||_styleid==14||_styleid==16) || (_styleid == 2 || _styleid == 15) ){
            // return '';
            //   }
            //
            if (
                tplData.ModeList.KddType == 1 ||
                comp.base.getTempStyle('cainiao', _styleid)
            ) {
                return '';
            }

            if (
                tplData.DefaultServiceItems &&
                tplData.DefaultServiceItems[0] &&
                tplData.DefaultServiceItems[0].ExServiceItems
            ) {
                service = tplData.DefaultServiceItems[0]; //.ExServiceItems[0] || [];
            } else {
                return '';
            }
            exCode = tplData.ModeList.Excode;
            findValue = function (dataName) {
                let has = false;
                $(tplData.ModeServiceItems || []).each(function (_i, _v) {
                    if (this.Itemname == dataName) {
                        has = _v;
                        return false;
                    }
                });
                return has;
            };

            $.each(service.ExServiceItems, function (idx, item) {
                _ohtml = '';
                _isCheck = findValue(this.Name);
                if (
                    (_kddType == 2 || _kddType == 8)  &&
                    /^(SF|CN7000001003751|SURE|YTO|DBKD)$/.test(exCode) &&
                    this.Name.match(/^(保价金额|月结卡号|付款方式)$/)
                ) {
                    _isDisabled = true;
                } else {
                    _isDisabled = false;
                }

                // 极兔速递
                if (
                    _kddType == 2 &&
                    /^(JTSD|LB)$/.test(exCode) &&
                    this.Name.match(/^(服务类型|派送类型)$/)
                ) {
                    _isDisabled = true;
                }

                // 暂时针对圆通额外增加一个限制，不久之后会有产品会整理一下此处的限制规范 2018.8.28
                if (
                    _kddType == 2 &&
                    /^(YTO)$/.test(exCode) &&
                    this.Name.match(/^(结算方式|付款对象)$/)
                ) {
                    _isDisabled = true;
                }

                html +=
                    '<span><label><input type="checkbox" ' +
                    (_isCheck ? 'checked' : '') +
                    ' name="' +
                    this.Name +
                    '" ' +
                    (_isDisabled ? 'disabled' : '') +
                    ' class="ex_service_item ' +
                    (_isDisabled ? 'disable' : 'not_disable') +
                    '" data-is-enable="' +
                    this.IsEnable +
                    '" data-is-set-val="' +
                    this.IsSetVal +
                    '">&nbsp;' +
                    this.Name +
                    '</label>';
                _vals = (item.Value || '').split('|');
                if (_vals && _vals.length > 1) {
                    _vals = item.Value.split('|');
                    _ohtml =
                        '<div ' +
                        (_isCheck ? '' : 'style=\'display:none\'') +
                        '>';
                    for (let j = 0; j < _vals.length; j++) {
                        _ohtml +=
                            '<em class="form_box_la_em">' +
                            '<label><input ' +
                            (_isCheck && _isCheck.Defaultval == _vals[j]
                                ? 'checked'
                                : '') +
                            ' type="radio" disabled  name="' +
                            item.Name +
                            '" class="input_radio ex_service_default" value="' +
                            _vals[j] +
                            '">' +
                            _vals[j] +
                            '</label>';
                        // if (_vals[j] == "声明价值保价") {
                        //     _ohtml += '<div id="kddservicetype_baojia" style="line-height:25px;margin-top:3px;"><input type="text" style="width:90px" name="Tips" /><br/>(1元-20000元)</div>';
                        // }
                        _ohtml += '</em>';
                    }
                    _ohtml += '</div>';
                }
                html += _ohtml + '</span>';
            });
            return html;
        },

        //模版
        getTemplateHtml: function (tplData, type) {
            let currentCopyData = localStorage.getItem('customCopyData') || '{}';
            currentCopyData = JSON.parse(currentCopyData)
            let cfg = tplData.ModeTempPrintcfg,
                _height = ~~cfg.Height,
                kddType = tplData.ModeList.KddType,
                fhdIsMerge = tplData.ModeListShow.fhdIsMerge, // 发货单商品是否合并
                Exid = tplData.ModeList.Exid,
                _isHasCopy = ['kdd','dpd','wdpd','tmd','wtmd'].includes(type),
                printcfg = tplData.ModeTempPrintcfg || {},
                templateHtml,
                fhdModeSet,
				printEncryptedHtml,
                styleId,
                kddTypeHtml = '',
                customCopyTime = currentCopyData[type]?.copyTime,
                currentGroup = comp.Print.Data.currentGroup,
                clientType = comp.Print.getClientType(tplData, type);

			xbqPaperSheet = ['fhd', 'fhd-nh-bq', 'bq', 'fhd-sm-bq', 'bhd', 'bkd', 'thd', 'dpd', 'tmd', 'wdpd', 'wtmd', 'zbd', 'bhbq'].includes(type) && kddType == 4 ? cfg.paperSheet : null;  // 控件类型

            if(type == 'kdd' && !!isMerge && currentGroup  ) {
                const kddTypeObj = comp.base.getKddNameObj()
                let itemClass = ''
                currentGroup?.userTemplateList.forEach(o => {
                    if(o.userTemplateId == tplData.ModeListShow.Mode_ListShowId){
                        itemClass = 'merge_kd_type_btn active_merge_btn'
                    }else{
                        itemClass = 'merge_kd_type_btn'
                    }
                    kddTypeHtml += `<span tempKddType=${o.expressType} userTemplateId=${o.userTemplateId} class="${itemClass}">${ kddTypeObj[o.expressType] }</span>`
                });
            }
            let templateSize;
			if (['bq', 'bhd', 'thd', 'bkd', 'zbd', 'bhbq'].includes(type)) {
                const exshowid = tplData?.ModeTempPrintcfg?.Exshowid;
                templateSize = localStorage.getItem(`smallTage_${exshowid}`) || 2;
            }
            if(templateSize > 2) templateSize = 2
            if (type === 'fhd') {
                type = comp.base.getFhdType(tplData);
            }
            if (type === 'fhd') {
                _height = !_height && cfg.Direction == 3 ? 180 : _height; //自适高 无高度时 给一个高度默认值
            } else {
                styleId = tplData.ModeList.StyleId;
                // 只有淘宝才能拷贝自定义区域
                // _isHasCopy = ( kddType == 1 ||
                //             kddType == 3 && !comp.base.getTempStyle('sanlian', styleId) && !comp.base.getTempStyle('bianxie', styleId) );
            }
            console.log(type,'typetypetypetypetype');
            if (type == 'fhd-nh-bq') {
                fhdModeSet =
                '<div class="fhd_mode_set">' +
                '<span><label><input type="checkbox" ' +
                (printcfg.sameSkuMerge == 1 ? 'checked ' : '') +
                `name="sameSkuMerge" class="fhdModeSet">&nbsp;同规格的商品合并打印</label>
                    <i class="iconfont ifont-wenhao" style="font-size:12px;cursor:pointer;" title="开启后，同规格的商品打印时仅生成一张标签\n添加数据项【包含商品数】可展示对应商品数"></i>
                </span>` +
                '</div>';
            }
            if (type == 'fhd') {
                let tempHtml = '';
                if (kddType != 4) {
                    tempHtml =
                        '<span><label><input type="checkbox" ' +
                        (printcfg.A4mode == 1 ? 'checked' : '') +
                        ' name="A4mode" class="fhdModeSet">&nbsp;发货单拼到同一张纸上</label></span>' +
                        '<span class="a4linefhd" style=' +
                        (printcfg.A4mode != 1 ? 'display:none' : '') +
                        '><label><input type="checkbox" ' +
                        (printcfg.A4line == 1 ? 'checked' : '') +
                        ' name="A4line" class="fhdModeSet">&nbsp;是否打印分隔线</label></span>';
                }
                if (Exid == '668') {
                    fhdModeSet =
                        '<div class="fhd_mode_set">' +
                        tempHtml +
                        '<span><label><input type="checkbox" ' +
                        (fhdIsMerge == 1 ? 'checked' : '') +
                        ' name="fhdIsMerge" class="fhdModeSet">&nbsp;同款商品是否合并</label></span>' +
                        '</div>';
                } else {
                    fhdModeSet =
                        '<div class="fhd_mode_set">' +
                        tempHtml +
                        '<span><label><input type="checkbox" ' +
                        (printcfg.Fhdishb == 1 ? 'checked' : '') +
                        ' name="Fhdishb" class="fhdModeSet">&nbsp;同款商品是否合并</label></span>' +
                        '</div>';
                }

            }else if(['bq','bhd','bkd','thd','dpd','tmd','wdpd','wtmd'].includes(type)){
                let contentOverflow =  tplData.ModeTempPrintcfg?.contentOverflow || 'SUPPORT'
                fhdModeSet =
                '<div class="fhd_mode_set">' +
                '<span><label><input type="checkbox" ' +
                (contentOverflow == 'HIDE' ? 'checked' : '') +
                ' name="contentOverflow" class="fhdModeSet">&nbsp;隐藏超出范围打印</label></span>' +
                '</div>';
            }else if( type =='ckd'){
				let tempHtml =
					'<span><label><input type="checkbox" ' +
					(printcfg.A4mode == 1 ? 'checked' : '') +
					' name="A4mode" class="fhdModeSet">&nbsp;出库单拼到同一张纸上</label></span>' +
					'<span class="a4linefhd" style=' +
					(printcfg.A4mode != 1 ? 'display:none' : '') +
					'><label><input type="checkbox" ' +
					(printcfg.A4line == 1 ? 'checked' : '') +
					' name="A4line" class="fhdModeSet">&nbsp;是否打印分隔线</label></span>';

				fhdModeSet =
					'<div class="fhd_mode_set">' +
					tempHtml +
					'</div>';
			}
			if (type == 'kdd') {
				let { userTemplateCfgConfigJson = {} } = cfg
				// recipientNameEncrypted、recipientAddressEncrypted、recipientPhoneEncrypted、
				// senderNameEncrypted、senderAddressEncrypted、senderPhoneEncrypted
				let isJTwd = tplData.ModeListShow.ExCode == 'JTSD' && kddType == '2';
				if (isJTwd) {
					userTemplateCfgConfigJson.senderNameEncrypted = 1;
					userTemplateCfgConfigJson.senderPhoneEncrypted = 1;
					userTemplateCfgConfigJson.recipientNameEncrypted = 1;
					userTemplateCfgConfigJson.recipientPhoneEncrypted = 1;
				}
				let hasreEncrypted = userTemplateCfgConfigJson.recipientNameEncrypted || userTemplateCfgConfigJson.recipientAddressEncrypted || userTemplateCfgConfigJson.recipientPhoneEncrypted;
				let hasSendEncrypted = userTemplateCfgConfigJson.senderNameEncrypted || userTemplateCfgConfigJson.senderAddressEncrypted || userTemplateCfgConfigJson.senderPhoneEncrypted;

				printEncryptedHtml = `
					<div class="printEncryptedBox">
						<div>
							<label>
								<input type="checkbox" ${hasSendEncrypted ? 'checked' : ''} ${isJTwd ? 'disabled' : ''} name="printEncrypted" value="1" class="encrypted">
									&nbsp;发件人打印加密
							</label>
							<div class="sendEncryptedBox" style="${hasSendEncrypted ? '' : 'display:none;'}">
								<label>
									<input type="checkbox" ${userTemplateCfgConfigJson.senderNameEncrypted === 1 ? 'checked' : ''}  ${isJTwd ? 'disabled' : ''} name="senderNameEncrypted" value="1" class="encrypted">
										&nbsp;发件人名称
								</label>
								<label>
									<input type="checkbox" ${userTemplateCfgConfigJson.senderPhoneEncrypted === 1 ? 'checked' : ''}  ${isJTwd ? 'disabled' : ''} name="senderPhoneEncrypted" value="1" class="encrypted">
										&nbsp;发件人电话
								</label>
								<label>
									<input type="checkbox" ${userTemplateCfgConfigJson.senderAddressEncrypted === 1 ? 'checked' : ''} name="senderAddressEncrypted" value="1" class="encrypted">
										&nbsp;发件人地址
								</label>
							</div>
						</div>
						<div  style="${kddType == 2 ? '' : 'display:none;'}">
							<label>
								<input type="checkbox" ${hasreEncrypted ? 'checked' : ''}  ${isJTwd ? 'disabled' : ''} name="recipientEncrypted" class="encrypted">
									&nbsp;收件人打印加密
							</label>
							<div class="reEncryptedBox" style="">
								<label>
									<input type="checkbox" ${userTemplateCfgConfigJson.recipientNameEncrypted === 1 ? 'checked' : ''}  ${isJTwd ? 'disabled' : ''} name="recipientNameEncrypted" value="1" class="encrypted">
										&nbsp;收件人名称
								</label>
								<label>
									<input type="checkbox" ${userTemplateCfgConfigJson.recipientPhoneEncrypted === 1 ? 'checked' : ''}  ${isJTwd ? 'disabled' : ''} name="printErecipientPhoneEncryptedncrypted" value="1" class="encrypted">
										&nbsp;收件人电话
								</label>
								<label>
									<input type="checkbox" ${userTemplateCfgConfigJson.recipientAddressEncrypted === 1 ? 'checked' : ''} name="recipientAddressEncrypted" value="1" class="encrypted">
										&nbsp;收件人地址
								</label>
							</div>
						</div>
					</div>

				`
			}
            templateHtml = [
                '<div class="template_con">',
                '<div class="template_edit">',
                '<div class="template_header">',
                '<span class="header_title">编辑模板</span>',
                '<div class="header_btn_con">',
                '<input type="button" class="finishEditTemplate button aqua" value="保存"/> &nbsp;&nbsp;',
                '<input type="button" class="cancelEditTemplate button gray" value="取消"/>',
                '</div>',
                '</div>',
                '<div class="template_content">',
                `<div class="template_ground" style="${['kdd','fhd','cgd','rkd','ckd','thqd'].includes(type)?'align-items: flex-start;':''} ${['cgd','rkd','ckd','thqd'].includes(type) ? 'padding: 40px 0;box-sizing:border-box' :''}"> `,
				`<div class="${['bq', 'bhd', 'bkd', 'thd', 'dpd', 'tmd', 'wtmd', 'wdpd', "fhd-nh-bq", 'zbd', 'zbxp', 'bhbq'].includes(type) ? 'drag_ground_box' : ''}"  ><div id="drag_ground" class="drag_play_ground">`,
                '<div class="markLineX"></div>',
                '<div class="markLineY"></div>',
                `</div>`,
                '<div id="xbqDouble"></div>',
                '</div>',
                '<div class="template_ground_addition" style="">菜鸟预留区域，不可编辑</div>',
                '</div>',
                '<div class="template_noti"><span class="main_title" style="visibility:hidden;">&nbsp;&nbsp;左、上边距固定</span><span class="main_msg">Top:0px;&nbsp;&nbsp;Left:0px;&nbsp;&nbsp;Width:' +
                    tplData.ModeTempPrintcfg.Width +
                    'mm;&nbsp;&nbsp;Height:' +
                    _height +
                    'mm;</span></div>',
                '</div>',
                '<div class="template_operate">',
                '<div class="operate_panel">',
				'<p class="operate_title">模版名称<input type="text" maxlength="20" class="ExcodeName" value="' +
                      tplData.ModeListShow.ExcodeName +
                      '"></p><hr class="hr_divider"/>'
				,
				(['bq', 'bhd', 'thd', 'bkd', 'zbd', 'bhbq'].includes(type))
                    ? `<p class="operate_title">缩放比例
                    <select class="xbqsize " style="width:133px">,
                        <option value="1" ${templateSize==1?'selected':''}>100%</option>,
                        <option value="1.5" ${templateSize==1.5?'selected':''}>150%</option>,
                        <option value="2" ${templateSize==2?'selected':''}>200%</option>,
                    </select>
                    <img src="https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/wenhao.png" class="toolTip"  data-width="160" data-tip-content="缩放编辑区模板展示大小，方便编辑模板。实际打印尺寸不受影响。" data-tip-title="11" style="position: relative;top: 0px;left: -2px;cursor: pointer;">
                    </p><hr class="hr_divider"/>`
                    : '',
                '<p class="operate_title">打印设置</p>',
                '<p>',
                '方向<select class="mar_l5 tpl_direction">',
                '<option value="0" ' +
                    (cfg.Direction == 0 ? 'selected' : '') +
                    '>默认</option>',
                '<option value="1" ' +
                    (cfg.Direction == 1 ? 'selected' : '') +
                    '>纵向</option>',
                '<option value="2" ' +
                    (cfg.Direction == 2 ? 'selected' : '') +
                    '>横向</option>',
				['fhd', 'thqd'].includes(type)
                    ? '<option value="3" ' +
                      (cfg.Direction == 3 ? 'selected' : '') +
                      ' >自适高</option>'
                    : '',
                '</select>',
                '</p>',
                '<p>',
                '宽(mm)<input class="w_h mar_l5 tpl_width" data-type="width" value="' +
                    cfg.Width +
                    '" type="text"> &nbsp;&nbsp;&nbsp;&nbsp;',
                '高(mm)<input class="w_h mar_l5 tpl_height" data-type="height" value="' +
                    cfg.Height +
                    '" type="text">',
                '</p>',
                '<div class="shift_con">',
                '整体偏移',
                '<div class="shift_temp">',
                '<div class="shift_border" style="left: ' +
                    cfg.Leftright +
                    'px; top: ' +
                    cfg.Updown +
                    'px;"></div>',
                '<img src="https://static.kuaidizs.cn/resources/img/print/dzmd_img_icon02.jpg" alt="">',
                '</div>',
                '<div class="shift_wheel">',
                '<a class="shift_wheel_ctrl dj_top" target="javascript: void(0)" data-type="top"></a>',
                '<a class="shift_wheel_ctrl dj_left" target="javascript: void(0)" data-type="left"></a>',
                '<a class="shift_wheel_ctrl dj_bottom" target="javascript: void(0)" data-type="bottom"></a>',
                '<a class="shift_wheel_ctrl dj_right" target="javascript: void(0)" data-type="right"></a>',
                '</div>',
                '<div class="shift_input">',
                '上下<input class="top_bottom" data-type="top" value="' +
                    cfg.Updown +
                    '" type="text"><br/>',
                '左右<input class="left_right" data-type="left" value="' +
                    cfg.Leftright +
                    '" type="text">',
                '</div>',
                type == 'kdd' && kddType != 1
                    ? '<div class="logo_con">' +
                      '<span><label><input type="checkbox" ' +
                      (cfg.Qslogo ? 'checked' : '') +
                      ' name="Qslogo" class="displayLogo">&nbsp;签收联logo</label></span>' +
                      '<span><label><input type="checkbox" ' +
                      (cfg.Lclogo ? 'checked' : '') +
					' name="Lclogo" class="displayLogo">&nbsp;留存联logo</label></span>' +
                      '</div>'
                    : '',
                '</div>',
                fhdModeSet,
				printEncryptedHtml,
                type == 'kdd' && kddType != 1
                    ? '<div class="service_con" >' +
                      this._getExService(tplData) +
                      '</div>' +
                      '<hr class="hr_divider mt_10"/>'
                    : '',

				['fhd', 'fhd-nh-bq', 'fhd-sm-bq', 'bhd', 'bkd', 'thd', 'dpd', 'wdpd', 'tmd', 'wtmd', 'bhbq'].includes(type) && kddType == 4
                    ? '<div>' +
                      '<span>纸张样式</span>' +
                      '<span class="template_option ' +
                      (cfg.paperSheet == 1 ? 'checked' : '') +
                      ' printPaperSheet" data-papersheet="1">单排</span>'
                      +
                      '<span class="template_option ' +
                      (cfg.paperSheet == 2 || cfg.paperSheet == 0
                          ? 'checked'
                          : '') +
                      ' printPaperSheet" data-papersheet="2">双排</span>' +
                      '</div>'
                    : '',
                '<div class="add_drag_con">',
                '<input type="button" class="addDrag" data-drag-type="dataInput" value="新增数据框">',
                '<input type="button" class="addDrag" data-drag-type="textInput" value="新增文字框"><br/>',
                '<input type="button" class="addDrag" data-drag-type="line" value="新增线条">',
                '<input type="button" class="addDrag" data-drag-type="image" value="新增图片">',
                // '<input type="button" class="addDrag" data-drag-type="rect" value="新增矩形">',
                (type == "kdd" && platformConfig.kdd.canPrintTable(clientType) ?
                    '<input type="button" class="addDrag" data-drag-type="table" value="新增表格">': ''
                ),
                '</div>',
                '<hr class="hr_divider"/>',
                ((type == 'fhd' && kddType != 4) || ['cgd','rkd','ckd','thqd','zbxp'].includes(type))
                    ? '<div class="add_drag_con">' +
                     '<input type="button" class="addDrag" data-drag-type="table"  value="新增表格">'+
                      '<input type="button" class="editTableDrag btn_white_middle" data-drag-type="tableEdit" name="editTable" value="编辑表格">' +
                      '<input type="button" class="editTableDrag btn_white_middle" name="deleteTable" value="删除表格" >' +
                      '</div>' +
                      '<hr class="hr_divider"/>'
                    : '',
                '<div class="reset_con" >',
                _isHasCopy
                    ? '<input type="button" class="copyTempInfo J-clickPoint" data-clickData-points="point=24346.31386.31387&_fm=7142"  value="复制自定义区域">' +
                      '<input type="button" class="pasteTempInfo J-clickPoint"  data-clickData-points="point=24346.31386.31388&_fm=7143"  value="粘贴自定义区域" >' +
                      `<p class="mt_0 info-msg" style="${customCopyTime ? '' : 'display:none;'}margin:0;">版本${customCopyTime}复制成功</p>`
                    : '',
                // 备货单模板  恢复默认布局 暂时不做
                '<input type="button" class="resetTemplateInput"  data-type="' +
                    type +
                    '" data-ex-id="' +
                    tplData.ModeList.Exid +
                    '" value="恢复默认布局">',
                type != 'fhd'
                    ? '<input type="button" class="modifyBgImg" value="更改底图" style="' +
                      (kddType != 1 ? 'display: none;' : '') +
                      '">'
                    : '',
                '</div>',
                '</div>',
                '</div>',
                '</div>',
                '<div class="template_bg">',
                '<div class="bg_header">',
                '<span class="header_title">设置模版</span>',
                '<div class="header_btn_con">',
                '<input type="button" value="保存" class="saveBgImg button aqua">&nbsp;&nbsp;',
                '<input type="button" value="取消" class="cancelBgImg button gray">',
                '</div>',
                '</div>',
                '<div class="bg_center">',
                '<div class="bg_radio">',
                '<label style="margin-left: 50px"><input type="radio" class="useBgImgType" name="useBgImgType" value="default" checked>使用默认底图</label>',
                '<label style="margin-left: 50px"><input type="radio" class="useBgImgType" name="useBgImgType" value="local">本地上传底图</label>',
                '<label>',
                '<div class="uploadFileCon uploadBgCon" style="position: relative;display: inline-block;width: 80px;height: 17px;margin-left: 20px;">',
                '<label>',
                '<div class="button mini white" style="position: absolute;z-index:2;display: inline-block;padding: 2px 6px;font-size: 12px;top: 1px; height: 18px">上传新底图</div>',
                '<form>',
                '<input type="file" class="uploadLocalImg" value="上传新底图" style="position: absolute;z-index: 1;width: 0;height: 0"/>',
                '</form>',
                '</label>',
                '</div>',
                '</label>',
                '<input type="button" class="delLocalImg button mini white" style="margin-left: 25px" value="删除">',
                '</div>',
                '<img class="bg_img" data-url="" data-cur-id="" src="" alt="">',
                '<div class="bg_switch">',
                '<a href="javascript:void(0);" class="switchBgImg" data-type="prev" data-cur-index="1">上一页</a> ',
                '<span class="bgImgIndex">1</span>/<span class="bgImgTotal">0</span> ',
                '<a href="javascript:void(0);" class="switchBgImg" data-type="next" data-cur-index="1">下一页</a>',
                '</div>',
                '</div>',
                '</div>',
                isMerge && type == 'kdd' ? '<div class="merge_kd_type">'+
                kddTypeHtml +
                '<span class="merge_kd_set_btn">分组设置</span>'+
                '</div>':'',
                '</div>',
            ];

            return templateHtml.join('');
        },

        //arg: min,max,defaultVal,holder
        getControlInput: function (name, value, gains, arg) {
            arg = arg || {};
            return (
                '<span class="controlls-input">' +
                '<input class="w_h item_blur item-limit-number"  type="number" ' +
                (arg.min ? ' data-min=' + arg.min : '') +
                (arg.max ? ' data-max=' + arg.max : '') +
                (arg.default ? ' data-default=' + arg.default : '') +
                ' data-gains=' +
                gains +
                ' placeholder="' +
                (arg.holder || '') +
                '" name="' +
                name +
                '" value="' +
                value +
                '"/>' +
                '<span class="controlls">' +
                '<font class="arrow-up"></font>' +
                '<font class="arrow-down"></font>' +
                '</span>' +
                '</span>'
            );
        },
        /**
        * isContinue 过滤不符合条件的数据框
        * @param {*} item  TagType=1 快递单 2 发货单  3 拿货小标签  4 退款单
        * @param {*} isFhd
        * @param {*} isThdSmallTag
        *
        * 快递单显示1，过滤配送方式
        * 发货单非小标签 显示1和2，过滤配送方式
        * 拿货小标签 备货单 显示3
        * 退货单显示4
        * 仅在快递单显示5
        */
        isContinue(item, isFhd, isThdSmallTag, isSmallTag, tmpType) {
            let flag = false;
            switch (item.TagType) {
                case 1:
                    if ((tmpType === 'kdd' || isFhd) && item.Options == '') {   //快递单、发货单都显示1，都不显示“配送方式”
                        flag = true;
                    }
                    break;
                case 2:
                    if (isFhd) {
                        flag = true;
                    }
                    break;
                case 3:
                    if (tmpType == 'bhd' && !isSmallTag) {
                        flag = true;
                    }
                    break;
                case 4:
                    if (isThdSmallTag) { // 应该是淘宝特有的
                        flag = true;
                    }
                    break;
                case 5:
                    if (tmpType === 'kdd') {
                        flag = true;
                    }
                    break;
                case 10: // 备货单小标签
                    if (tmpType === 'bhd' || isSmallTag) {
                        flag = true;
                    }
                    break;
                case 11: // 退货单小标签
                    if (tmpType === 'thd' || isThdSmallTag) {
                        flag = true;
                    }
                    break;
                case 12: // 退货单小标签
                    if (tmpType === 'bhd' || isSmallTag) {
                        flag = true;
                    }
                    break;
                case 13: // 吊牌
                    if (['dpd'].includes(tmpType) || isSmallTag) {
                        flag = true;
                    }
                    break;
                case 14: // 条码
                    if (['tmd'].includes(tmpType) || isSmallTag) {
                        flag = true;
                    }
                    break;
                case 17:
                    if (['cgd'].includes(tmpType) || isSmallTag) {
                        flag = true;
                    }
                case 18:
                    if (['rkd'].includes(tmpType) || isSmallTag) {
                        flag = true;
                    }
                case 19:
                    if (['ckd'].includes(tmpType) || isSmallTag) {
                        flag = true;
                    }
                case 20:
                    if (['thqd'].includes(tmpType) || isSmallTag) {
                        flag = true;
                    }
                case 15: // 吊牌
                    if (['wdpd'].includes(tmpType) || isSmallTag) {
                        flag = true;
                    }
                    break;
                case 16: // 条码
                    if (['wtmd'].includes(tmpType) || isSmallTag) {
                        flag = true;
                    }
                case 21: // 直播标签
                    if (['zbd'].includes(tmpType) || isSmallTag) {
                        flag = true;
                    }
                    break;
				case 23: // 直播标签
					if (['bhbq'].includes(tmpType) || isSmallTag) {
						flag = true;
					}
					break;
                default: flag = false;
                    break;

            }

            return flag;
        },
        //数据框模版
        getNewDataDragSet: function (tplItem, conf, tempConf) {
            let html,
                str = '',
                i,
                item,
                isNeedNew = true,
                tempGroup,
                tempItem,
                isShowTip = false,
                isFhd = tempConf.isFhd,
                isThdSmallTag = tempConf.isThdSmallTag,
                isSmallTag = tempConf.isSmallTag,
                tmpType = tempConf.tmpType,
                kddType = tempConf.kddType,
                groupType = 'tagGroup',// ['kdd','fhd','dpd','tmd','wdpd','wtmd','txm','cgd','rkd','ckd'].includes(tmpType) ? 'tagGroup' : 'Group',
                itemGroupName = {
                    TRADE:'订单信息',
                    GOODS:'货品信息',
					BH_INFO: '备货信息',
                    PRINT:'打印信息',
                    CODE:'条形码&二维码',
                    QR_CODE:'二维码',
                    BAR_CODE:'条形码',
                    SENDER:'发件信息',
                    RECEIVER:'收件信息',
                    NH_SMALL_TAG:'发货小标签使用',
                    CARE_LABEL:'水洗标',
                    TAG:'标签信息',
                    CUSTOM:'自定义属性'
                }
                tplItem = this.customTempItem(tplItem)
                if(tmpType === 'bhd'){
                    itemGroupName.GOODS = '商品信息'
                }
			if (tmpType === 'kdd' && !window?.erpData?._waveManagePermission) {
				tplItem = tplItem.filter(o => {
					return !['wave_packing_no', 'wave_no', 'wave_package_no'].includes(o.Key)
				})
			}

                let tplItemObj = {
                    TRADE:[],
                    GOODS:[],
					BH_INFO: [],
                    PRINT:[],
                    CODE:[],
                    SENDER:[],
                    RECEIVER:[],
                    NH_SMALL_TAG:[],
                    CARE_LABEL:[],
                    TAG:[],
                    QR_CODE:[],
                    BAR_CODE:[],
                    CUSTOM:[],
                    DEFAULT_TAG:[],
                }
                let newTplItem = []
                tplItem.forEach(o=>{
                    if(o.tagGroup){
                        tplItemObj[o.tagGroup]?.push(o)
                    }else{
                        tplItemObj['DEFAULT_TAG']?.push(o)
                    }
                })
                for(let k in tplItemObj){
                    newTplItem.push(...tplItemObj[k])
                }

            for (i = 0; i < newTplItem.length; i++) {
                item = newTplItem[i];
                if(item.Key === 'urge_shipments' && ![3,7,8].includes(kddType)){
                    continue;
                }

                // if (!isFhd && (item.TagType != 1 || item.Options)) {
                //     continue;
                // }
                // 解决小标签侧边数据框
                if (!this.isContinue(item, isFhd, isThdSmallTag,isSmallTag, tmpType)) {
                    continue;
                }

                if (isNeedNew) {
                    str += `<div class="dragGroupBox"><div style = "display:flex;align-items:center"><span class="dragItemName">${itemGroupName[item.tagGroup] || ''}</span>${item.tagGroup === 'CODE' ? `<i class="iconfont ifont-tixing1" style="cursor:pointer" title="请注意，条形码不支持中文，同时请控制条形码长度在20位以内，否则容易造成扫描枪不易识别"></i>` : ''}</div><ul class="dragItemList">`;
                    isNeedNew = false;
                }
                if (item.Key == 'f_info' && !isSmallTag) { //需要单独处理一下
                    tempGroup = item.Group;
                    tempItem = newTplItem[i];
                }else{
                    str +=
                    '<li class="' +
                    (item.Key == 'ewm' || item.Key == 'ewm_str'
                        ? 'wider'
                        : '') +
                    '">' +
                    '<label>' +
                    '<input type="checkbox" data-item-type="' +
                    item.ItemType +
                    '" class="dragDataItem" data-key="' +
                    item.Key +
                    '" data-input-id="' +
                    conf.InputID +
                    '" data-obj=\'' +
                    escape(JSON.stringify(item)) +
                    '\'>' +
                    '<span class="drag_item_name">' +
                    item.Name +
                    '</span>' +
                    (['dy_nick','s_nick_image'].includes(item.Key)  ? `
                         <i class="iconfont ifont-wenhao customTips" style="font-size:12px;cursor:pointer;">
                        </i>
                        ` : '')+
                    '</label>' +
					(item.Key == 'shop_info' ? `<span id="editShopInfo" style="color:blue;margin-left:5px;cursor: pointer;">编辑</span>` : '') +
                    (item.Key == 'ewm' || item.Key == 'ewm_str'
                        ? '&nbsp;&nbsp;<input type="text" class="ewm_input" placeholder="请输入二维码内容">'
                        : '') +
                    '</li>';
                }
                if (
                    i !== 0 &&
                    i !== newTplItem.length - 1 &&
                    item[groupType] != newTplItem[i + 1][groupType]
                ) {
                    //f_info这个特殊处理 fixme，展示发货内容设置提示
                    if (tempGroup) {
                        tempGroup = '';
                        // str += '<div><ul class="dragItemList">';
                        str +=
                            '<li style="width:95px;float:none"><label><input type="checkbox" data-item-type="' +
                            tempItem.ItemType +
                            '" class="dragDataItem" data-key="' +
                            tempItem.Key +
                            '" data-input-id="' +
                            conf.InputID +
                            '" data-obj=\'' +
                            escape(JSON.stringify(tempItem)) +
                            '\'><span class="drag_item_name">' +
                            (tempItem.Key == 'f_info'
                                ? '发货内容'
                                : tempItem.Name) +
                            '</span></label><span id="editFh" style="color:blue;margin-left:5px;cursor: pointer;">编辑</span>';
                        // str += '</ul></div>';
                        str +=
                            '<div class="f_info_noti" style="clear: both;padding-left: 15px;display: none;white-space: nowrap"><a href="http://www.kuaidizs.cn/helpMap/getDetail?detailId=496" target="_blank">如何在快递单上打印商家编码，设置打印内容？</a></div></li>';
                        // str += '<hr class="hr_divider"/>';
                    }
                    str += '</ul></div><hr class="hr_divider"/>';
                    isNeedNew = true;

                    // if (item.Group == 5 && isFhd && !isShowTip) {
                    //     //发货单小标签此处需要添加文案 后续可能会去掉 //fixme
                    //     str +=
                    //         '<p class="fhd_xbq_tip">该部分数据项仅用于拿货小标签使用</p>';
                    //     isShowTip = true;
                    // }


                }
            }
            if (!isNeedNew) {
                str += '</ul></div>';
                if (tempConf.fhdType === 'fhd-sm-bq') {
                    str +=
                        '<a href="//helptb.kuaidizs.cn/helpMap/getDetail?detailId=536" class="scanPrintTip" target="_blank" style="padding-left: 15px;color: #b9cede;">扫描打印教程</a>';
                }
            }
            //先隐藏
            let searchHtml = ['kdd','fhd','bhd','bkd','dpd','tmd','wdpd','wtmd'].includes(tmpType) ? `
                <div class="item_search_box">
                    <input class="item_search_input" type="text" placeholder="搜索数据项（例如：付款时间）"/>
                    <button class="item_search_btn">搜索</button>
                </div>` : '';

            let showWrapHtml = [3,8].includes(tempConf.kddType ) ? `<p>
            <span class="edit-lable">内容溢出</span>
            <select class="item_wrap_change" name="wrap" style="width:182px">
                 <option ${conf.hideWrap=='1' ? 'selected' : '' } value="1">自动隐藏超出部分</option>
                 <option ${conf.hideWrap=='0' ? 'selected' : '' } value="0">自动换行</option>
            </select>
       </p>` : '';
            html = [
                '<div class="drag_setting_panel" style="padding-bottom: 100px" data-input-id="' +
                    conf.InputID +
                    '">',
                '<div class="drag_setting_title">数据框设置</div>',
                '<div>' + this.getStyleSet(conf, 2, isFhd,tempConf?.kddType,tempConf?.tmpType,tempConf?.dragType), //暂时全部展示
                showWrapHtml,
                '<p>',
                '<p>',
				'<span class="edit-lable">前文字</span>',
                '<input name="Str_q" type="text" class="item_blur edit-input" value="' +
                    conf.Str_q +
                    '" />',
                '</p>',
                '<p>',
                '<span class="edit-lable">后文字</span>',
                '<input name="Str_h" type="text" class="item_blur edit-input" value="' +
                    conf.Str_h +
                    '"/>',
                '</p>',
                '<p class="databox_show" style="display: none;">',
                '<span>是否在没有卖家备注的时显示</span>',
                '<span>',
                '<label class="mr_10">',
                '<input name="_nu"  value="1" type="radio" class="input_check" />是</label>',
                '<label class="on">',
                '<input name="_nu" value="0" type="radio" class="input_check" />否</label>',
                '</span>',
                '</p>',
                '<hr class="hr_divider"/>',
                '</div>',
                searchHtml,
                str,
                '<div class="add_line_btn" style="padding-left:28px;box-sizing: content-box;">',
                '<input type="button" class="button orange" name="applyDataBox" value="确认" data-input-id="' +
                    conf.InputID +
                    '"/>&nbsp;&nbsp;',
                '<input type="button" class="button red" name="removeDataBox" value="删除" data-input-id="' +
                    conf.InputID +
                    '" /></div>',
                '</div>',
            ];

            //生成dom
            html = $(html.join(''));
            this.renderStyleSet(html, conf, isFhd);
			// 修改事件处理部分
            html.find('.customTips').on('mouseenter', function(e) {
                const $this = $(this);

                // 移除可能存在的其他提示框
                $('.custom_tips_box').remove();
				let contentHtml = ''
				if(comp.Print.Data?.userInfo?.level == 2){
					contentHtml = `	<p class="tips_title">抖音买家昵称<span>[高配版功能]</span></p>
						<p>1.订单列表点击👁可显示买家昵称，查看昵称会消耗店铺解密额度</p>
						<p>2.快递单模板设置【抖音昵称】数据项可打印昵称</p>`
				}else{
					contentHtml = `<p>付费功能</p>`
				}
                // 创建提示框
                const tipsHtml = `
                    <div class="custom_tips_box" style="position: absolute; z-index: 9999; background: #fff; border: 1px solid #e4e7ed; padding: 10px; border-radius: 4px; max-width: 300px; box-shadow: 0 2px 12px 0 rgba(0,0,0,.1); ">
					${contentHtml}
                    </div>
                `;

                const $tips = $(tipsHtml);
                $('body').append($tips);

                // 计算位置
                const offset = $this.offset();
                const tipsWidth = $tips.outerWidth();
                const tipsHeight = $tips.outerHeight();

                // 定位提示框
                $tips.css({
                    left: offset.left - (tipsWidth / 2) + ($this.width() / 2),
                    top: offset.top - tipsHeight - 10
                });
            }).on('mouseleave', function() {
                $('.custom_tips_box').remove();
            });
            const isShowWater = (conf.proArray && conf.proArray.length && conf.proArray[0].Dataname == 'count');
            if(isShowWater){
                html.find('.alphaSet').show();
                if(conf.alpha){
                    html.find('select[name=alpha]').val(conf.alpha);
                }
            }
            return html;
        },
        customTempItem: function(tplItem){
            let systemSetting = window.erpData?.systemSetting || {};
            if(!systemSetting?.itemCustomAttribute) return tplItem.filter(o => o.tagGroup !== 'CUSTOM')
            let customTempSetting = systemSetting?.itemCustomAttributeDTOList || []
            let newTplItem = tplItem.filter(o=>{
                if(o.tagGroup === 'CUSTOM'){
                    let customItem = customTempSetting.find(it => o.Key === it.key)
                    if(customItem && customItem.name) {
                        o.Name = customItem.name;
                        o.Tips = customItem.name;
                        o.Value = customItem.name;
                    }
                }
                 return o.Name
            })
            return newTplItem
        },
		customTipsHtml:function(customTemp,data){
			let contentHtml = ''
			if(customTemp){

			}else{
				contentHtml = `span class="custom_tips_content">${data}</span>`
			}
			return `
				<div class="custom_tips_box">
					${contentHtml}
				</div>`
		},
        getStyleSet: function (conf, source, isFhd,kddType,getStyleSet,isDataInput= '') {
            const isShowMoreSet = source === 2 || editShowMoreSet; //是否显示更多样式设置：曾经点击过显示更多，增加文本框
			const isRotate = ['dpd', 'tmd', 'wdpd', 'wtmd', 'bhd', 'bkd'].includes(getStyleSet); //是否显示更多样式设置：曾经点击过显示更多，增加文本框
            const isAutoFontSize = ['bhd','bkd'].includes(getStyleSet)
			const isShowStyleSet = ['fhd', 'bhd', 'bkd', 'thd', 'dpd', 'tmd', 'wdpd', 'wtmd', 'zbd', 'zbxp', 'ckd', 'thqd', 'bhbq'].includes(getStyleSet)
            const isShowbarCodeType = !['kdd'].includes(getStyleSet)
            const isShowErCodeType = !['kdd'].includes(getStyleSet)
            const directHtm = `<p>
                <span class="edit-lable">排版方向</span>
                    <span class="radio-list">
                    <label class="radio-label mr_16"><input type="radio" class="item_change" name="direct" value="0" /><span class="edit-icon icon-horizontal"></span>横排文本</label>
                    <label class="radio-label"><input type="radio" class="item_change" name="direct" value="1" /><span class="edit-icon icon-vertical"></span>竖排文本</label>
                </span>
            </p>`

            // 垂直或者居中html
            const alginHtml =`<div class="over-hidden">
            <span class="edit-lable f-lt">排列</span>
            <ul class="f-li ul-tile radio-list" style="margin-right:12px;">
              <li><label class="radio-label w_24" title="水平居左"><input type="radio" class="item_change" name="align"
                    value="0" /><span class="edit-icon icon-left"></span></label></li>
              <li><label class="radio-label w_24" title="水平居中"><input type="radio" class="item_change" name="align"
                    value="1" /><span class="edit-icon icon-center"></span></label></li>
              <li><label class="radio-label w_24" title="水平居右"><input type="radio" class="item_change" name="align"
                    value="2" /><span class="edit-icon icon-right"></span></label></li>
            </ul>
            <ul class="f-li ul-tile radio-list">
              <li><label class="radio-label w_24" title="垂直居上"><input type="radio" class="item_change" name="valign"
                    value="0" /><span class="edit-icon icon-top"></span></label></li>
              <li><label class="radio-label w_24" title="垂直居中"><input type="radio" class="item_change" name="valign"
                    value="1" /><span class="edit-icon icon-middle"></span></label></li>
              <li><label class="radio-label w_24" title="垂直居下"><input type="radio" class="item_change" name="valign"
                    value="2" /><span class="edit-icon icon-bottom"></span></label></li>
            </ul>
          </div>
          <p>
            <span class="edit-lable">样式</span>
            <label class="checkbox-label mr_16 ${conf.italic ? 'active' : ''}"><input type="checkbox" class="item_change" name="italic" ${conf.italic ? 'checked' : ''} /><span
                class="edit-icon icon-italic"></span>倾斜</label>
            <label class="checkbox-label ${conf.under ? 'active' : ''}"><input type="checkbox" class="item_change" name="under" ${conf.under ? 'checked' : ''} /><span
                class="edit-icon icon-underline"></span>下划线</label>
          </p>`
            // 是否黑底白字
          const blackAndWhiteHtml =  `<p style="padding-left:64px">
            <input class="black_white_check" ${conf.style === 'BLACK_WHITE' ? 'checked' : ''} type="checkbox" value="BLACK_WHITE" > 黑底白字 </input>
          </p>`
        //   展示数据框文本类型
          const selectDataType = `
          <p style="display:flex">
            <span class="edit-lable controlls-input">展现样式</span>
            <select class="item_change" name="dataType" style="width:60px;">
                <option value="txt">文字</option>
                <option value="ewm">二维码</option>
                <option value="txm">条形码</option>
            </select>
            <i class="iconfont ifont-wenhao" style="cursor:pointer;margin-left:5px;font-size:13px" title="请注意，条形码不支持中文，同时请控制条形码长度在20位以内，否则容易造成扫描枪不易识别"></i>
        </p>
          `
          const barCodeTypeHtml = `
            <div class="barCodeTypeDiv" style="display:none">
                <p style="display:flex;">
                        <span class="edit-lable controlls-input">条形码类型</span>
                        <select class="item_change" name="barcodeType" style="width:60px;">
                            <option value="128Auto">code 128auto</option>
                            <option value="EAN13">ean 13</option>
                            <option value="Code39">code 39</option>
                            <option value="Code93">code 93</option>
                        </select>
                        <i class="iconfont ifont-wenhao" style="cursor:pointer;margin-left:5px;font-size:13px" title="不同条形码类型生成的条形码格式不同，请根据自己的诉求选择"></i>
                </p>
            </div>
                `
			//
          const erCodeTypeHtml = `
            <div class="erCodeTypeDiv" style="display:none">
                <p style="display:flex">
                    <span class="edit-lable controlls-input">二维码版本</span>
                    <select class="item_change" name="qrcodeType" style="width:60px;">
                        <option value="0">Auto</option>
                        <option value="3">3</option>
                        <option value="5">5</option>
                        <option value="7">7</option>
                        <option value="10">10</option>
                        <option value="14">14</option>
                    </select>
                    <i class="iconfont ifont-wenhao" style="cursor:pointer;margin-left:5px;font-size:13px" title="不同二维码类型生成的条形码格式不同，请根据自己的诉求选择"></i>
                </p>
            </div>
                `
			//

            const html = [
                '<p class="">',
                '<span class="edit-lable">框宽</span>' +
                    this.getControlInput('W_', conf.W_, 3, {
                        min: 1,
                        default: 1,
                    }),
                '<span class="edit-lable">框高</span>' +
                    this.getControlInput('H_', conf.H_, 3, {
                        min: 1,
                        default: 1,
                    }),
                '</p>',
                '<hr class="hr_divider"/>',
                (kddType == 3 && !isFhd) ? directHtm : '',
                '<p class="">',
                '<span class="edit-lable controlls-input">字体</span>',
                '<select class="item_change" name="Fontname" style="width:60px;">',
                '<option value="">默认</option>',
                '<option value="宋体">宋体</option>',
                '<option value="黑体">黑体</option>',
                '<option value="微软雅黑">微软雅黑</option>',
                '<option value="新宋体">新宋体</option>',
                '<option value="幼圆">幼圆</option>',
                '<option value="华文细黑">华文细黑</option>',
                '<option value="隶书">隶书</option>',
                '<option value="Arial">Arial</option>',
                '<option value="Arial Narro">Arial Narro</option>',
                '</select>',
                '<span class="edit-lable">字号</span>',
                '<select class="item_change" name="Fontsize" style="width:60px;margin-left">',
                '<option value="0">默认</option>',
                ((kddType == 3 || isAutoFontSize ) ? '<option value="1">自动缩放</option>' : ''),
                this.genOption(8, 94),
                '</select>',
                '</p>',
                '<p>',
                '<span class="edit-lable">字体粗细</span>',
                '<select class="item_change" name="Isb_n" style="width:60px">',
                '<option value="-1">默认</option>',
                '<option value="1">加粗</option>',
                '<option value="0">不加粗</option></select>',
                // '<span class="edit-lable">颜色</span>',
                // '<select class="item_change" name="blackWhite" style="width:60px">',
                // '<option value="0">默认</option>',
                // '<option value="1">黑底白字</option>',
                // '</select>',
                '<span class="edit-lable">透明度</span>' + this.getControlInput('alpha',conf.alpha || 1,0.1,{holder:'0-1',step:'0.1'}),
                '<span class="alphaSet" style="display:none;">',
                '<span class="edit-lable">水印</span>',
                '<select class="item_change" name="alpha" style="width:60px;"><option value="1">否</option><option value="0.65">是</option></select>',
                '</span>',
                '</p>',
                '<a href="javascript:;" class="' +
                    (isShowMoreSet ? 'hide' : '') +
                    ' showMoreSet">显示更多</a>',
                '<div class="' +
                    (isShowMoreSet ? '' : 'hide') +
                    ' editMoreSet">',
                '<p>',
                '<span class="edit-lable">字间距</span>',
                this.getControlInput('Zjj', conf.Zjj == -1 ? 0 : conf.Zjj, 1, {
                    min: '0',
                    holder: 'px',
                }), //name,value,gains,min,max,defaultVal,holder
                '<span class="edit-lable">行间距</span>',
                this.getControlInput('Hjj', conf.Hjj == -1 ? '' : conf.Hjj, 1, {
                    min: '0',
                    holder: 'px',
                }), //name,value,gains,min,max,defaultVal,holder
                '</select>',
                '</p>',
				isRotate ? `<p class="rotate-set" style="${['txm', 'ewm'].includes(conf.scripConvertCode) ? 'display:none;' : ''}"><span class="edit-lable">旋转</span>${this.getControlInput('deg', conf.deg || 0, 1, { holder: '0-360', min: '0', max: '360', step: '1' })}</p>` : '',
                isShowStyleSet?selectDataType : '',
                isShowbarCodeType ? barCodeTypeHtml  : '',
                isShowErCodeType && isDataInput != 'dataInput' ? erCodeTypeHtml  : '',



                // '<p>',
                // '<span class="edit-lable">旋转角度</span>' + this.getControlInput('deg',conf.deg || 0,1),
                // isFhd  ? '' : '<span class="edit-lable">透明度</span>' + this.getControlInput('alpha',conf.alpha || 1,0.1,{holder:'0-1'}),
                // '</p>',
                ((kddType == 3 && !isFhd) ? alginHtml : ''),
                (([3,4,8].includes(kddType) || isFhd)? blackAndWhiteHtml : ''),
                '<p class="encryBlock" style="display:none;">',
                '<span class="edit-lable">加密</span>',
                '<label class="checkbox-label mr_16 ' +
                    (Number(conf.encry) ? 'active' : '') +
                    '"><input type="checkbox" class="item_change" name="encry" ' +
                    (Number(conf.encry) ? 'checked' : '') +
                    ' /><span class="edit-icon icon-encry"></span>开启</label>',
                '</p>',
                '</div>',
            ].join('');
            return html;
        },

        renderStyleSet: function (html, conf, isFhd) {
            //生成dom
            if (conf.Fontname) {
                html.find('select[name=Fontname]').val(conf.Fontname);
            }
            if (conf.scripConvertCode) {
                html.find('select[name=dataType]').val(conf.scripConvertCode);
            }
            if (conf.barCodeType) {
                html.find('select[name=barcodeType]').val(conf.barCodeType);
            }
            // 二维码赋值
            if (conf.barCodeType && conf.scripConvertCode === 'ewm') {
                html.find('select[name=qrcodeType]').val(conf.barCodeType);
            }
			//
            const $fontSize = html.find('select[name=Fontsize]');

            if (conf.Fontsize) {
                if (conf.Fontsize == -99) {
                    //自适应自提大小
                    $fontSize.append(`<option value="-99">自适应</option>`);
                }
                $fontSize.val(conf.Fontsize);
            }
            $fontSize.attr('disabled', conf.Fontsize == -99);
            if (conf.bColor === '#000000' && conf.color === '#ffffff') {
                html.find('select[name=blackWhite]').val(1);
            }

            html.find(
                '.item_change[name="direct"][value=' + (conf.direct || 0) + ']',
            )
            .prop('checked', true)
            .closest('label')
            .addClass('active');
            html.find(
                '.item_change[name="align"][value=' + (conf.align || 0) + ']',
            )
            .prop('checked', true)
            .closest('label')
            .addClass('active');

            Number(conf.encry || '0') &&
                html
                .find('.item_change[name="encry"]')
                .prop('checked', true)
                .closest('label')
                .addClass('active');
            !Number(conf.encry || '0') &&
                html
                .find('.item_change[name="encry"]')
                .prop('checked', false)
                .closest('label')
                .removeClass('active');
            !isFhd &&
                html
                .find(
                    '.item_change[name="valign"][value=' +
                            (conf.valign || 0) +
                            ']',
                )
                .prop('checked', true)
                .closest('label')
                .addClass('active');
            return html;
        },

        genOption: function (start, length) {
            let str = '';
            for (let i = 0; i < length; i++) {
                str +=
                    '<option value="' +
                    (start + i) +
                    '">' +
                    (start + i) +
                    '</option>';
            }
            return str;
        },
        //文本框模板
        getNewTextDragSet: function (conf, kddType,isFhd,tmpType) {
            let html;

            html = [
                '<div class="drag_setting_panel" style="margin-left: 10px;" data-input-id="' +
                    conf.InputID +
                    '">',
                '<div class="drag_setting_title">文本框设置</div>',
                this.getStyleSet(conf, 2, isFhd,kddType,tmpType),
                '<p>',
                '<span class="edit-lable">固定字</span>',
                '<textarea class="item_blur" name="Str_q" style="vertical-align:top;width:165px;height:70px;padding:6px;resize:none">',
                conf.Str_q,
                '</textarea>',
                '</p>',
                '<div style="padding-left: 59px;margin-top: 30px;">',
                '<input type="button" class="button orange mini mr_15" name="applyTextBox" value="确认"/>',
                '<input type="button" class="button red mini" name="removeTextBox" value="删除" data-input-id="' +
                    conf.InputID +
                    '" /></div>',
                '</div>',
            ];

            html = $(html.join(''));
            this.renderStyleSet(html, conf, isFhd);
            return html;
        },
        //线条框
        getNewLineDragSet: function (conf) {
            let html;

            html = [
                '<div index="2" class="drag_setting_panel line_type" style="height: 499px;">',
                '<div class="drag_setting_title">线条设置</div>',
                '<dl class="add_line_box1">',
                // '<dt class="line_type_title">线条设置</dt>',
                '<dd class="mar_t10">',
                '<span class="w70">方向</span>',
                '<select class="item_change w60" name="fangxiang">',
                '<option value="1">竖</option>',
                '<option value="2" ' +
                    (conf.H_ < conf.W_ ? 'selected' : '') +
                    '>横</option></select>',
                '</dd>',
                '<dd class="mar_t10">',
                '<span class="w70">长度(px)</span>' +
                    this.getControlInput(
                        'changdu',
                        conf.H_ <= conf.W_ ? conf.W_ : conf.H_,
                        3,
                        { min: 1, default: 1 },
                    ),
                '</dd>',
                // '<dd class="mar_t10">',
                // '<span class="w70">线宽(px)</span>' + this.getControlInput('line-width',conf.Str_h || 1 ,1,{min:1,default:1}),
                // '</dd>',
                '<dd class="mar_t10">',
                '<span class="w70">线条类型</span>',
                '<select class="item_change w60" name="xiantiaoleixing" >',
                '<option value="0">实线</option>',
                // '<option value="1" ' + (conf.Str_q == 1 ? 'selected' : '') + '>虚线</option>',
                // '<option value="2" ' + (conf.Str_q == 2 ? 'selected' : '') + '>点线</option></select>',
                '<option value="2" ' +
                    (conf.Str_q == 2 ? 'selected' : '') +
                    '>虚线</option></select>',
                '</dd>',
                '</dl>',
                '<div style="margin-left: 30px;margin-top: 30px;">',
                '<input type="button" name="applyLine" data-input-id="' +
                    conf.InputID +
                    '" value="确认" class="button orange mini">&nbsp;&nbsp;',
                '<input type="button" name="removeLine" data-input-id="' +
                    conf.InputID +
                    '" class="button red mini" value="删除"></div>',
                '</div>',
            ];

            return html.join('');
        },
        //图片
        getNewImgSet: function (conf) {
            let html;

            html = [
                '<div class="drag_setting_panel" style="height: 499px; text-align: center">',
                '<div class="img_type_title mar_l20 mar_t10">编辑图片</div>',
                '<div class="mar_t20">',

                '<div class="uploadFileCon uploadEleImgCon" style="position: relative;display: inline-block;width: 80px;height: 22px;">',
                '<label>',
                '<div class="button mini white" style="box-sizing: content-box;position: absolute;z-index:2;display: inline-block;padding: 2px 5px;font-size: 12px;top: 0; left: 0;border: 0; height: 18px;width: 70px;height: 22px;line-height: 22px;color: white;background-color: #f7a25f;">上传新图</div>',
                '<form>',
                '<input type="file" name="uploadImg" value="上传" style="position: absolute;z-index: 1;width: 0;height: 0;left:0;"/>',
                '</form>',
                '</label>',
                '</div>',
                '</div>',
                '<div class="mar_t20">',
                '<input type="button" class="button red mini" name="deleteImg" data-input-id="' +
                    conf.InputID +
                    '" value="删除"/>',
                '</div>',
                '<div class="mar_t20">',
                '<input type="button" class="button gray mini" name="removeSet" value="返回"/>',
                '</div>',
                '</div>',
            ];

            return html.join('');
        },
        //矩形框
        getNewRectDragSet: function (conf) {
            let html;

            html = [
                '<div index="2" class="drag_setting_panel rect_type" style="height: 499px;">',
                '<div class="drag_setting_title">矩形设置</div>',
                '<dl class="add_line_box1">',
                // '<dt class="rect_type_title">矩形设置</dt>',
                '<dd class="mar_t10">',
                '<span class="w70">长度(px)</span>' +
                    this.getControlInput('W_', conf.W_, 3, {
                        min: 1,
                        default: 1,
                    }),
                '</dd>',
                '<dd class="mar_t10">',
                '<span class="w70">宽度(px)</span>' +
                    this.getControlInput('H_', conf.H_, 3, {
                        min: 1,
                        default: 1,
                    }),
                '</dd>',
                // '<dd class="mar_t10">',
                // '<span class="w70">长度(px)</span>',
                // '<input class="item_blur" name="W_" value="'+ conf.W_  +'" placeholder="0" style="width:58px"></dd>',
                // '<dd class="mar_t10">',
                // '<span class="w70">宽度(px)</span>',
                // '<input class="item_blur" name="H_" value="'+ conf.H_   +'" placeholder="0" style="width:58px"></dd>',
                '<dd class="mar_t10">',
                '<span class="w70">线条类型</span>',
                '<select class="item_change w60" name="xiantiaoleixing">',
                '<option value="0">实线</option>',
                '<option value="1" ' +
                    (conf.Str_q == 1 ? 'selected' : '') +
                    '>虚线</option>',
                '<option value="2" ' +
                    (conf.Str_q == 2 ? 'selected' : '') +
                    '>点线</option></select>',
                '</dd>',
                '<dd class="mar_t10">',
                '<span class="w70">背景类型</span>',
                '<select class="item_change w60" name="bColor">',
                '<option value="" ' +
                    (conf.bColor !== '#000000' ? 'selected' : '') +
                    '>无填充</option>',
                '<option value="#000000" ' +
                    (conf.bColor == '#000000' ? 'selected' : '') +
                    '>有填充</option></select>',
                '</dd>',
                '</dl>',
                '<div style="margin-left: 30px;margin-top: 30px;">',
                '<input type="button" name="applyRect" data-input-id="' +
                    conf.InputID +
                    '" value="确认" class="button orange mini">&nbsp;&nbsp;',
                '<input type="button" name="removeRect" data-input-id="' +
                    conf.InputID +
                    '" class="button red mini" value="删除"></div>',
                '</div>',
            ];

            return html.join('');
        },
    };

    window.PrintTemplate = PrintTemplate;
})(window.jQuery);
