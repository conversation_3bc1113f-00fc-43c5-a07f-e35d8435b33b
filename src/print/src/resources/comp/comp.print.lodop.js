﻿/// <reference path="comp.print.base.js" />
/// <reference path="comp.print.js" />
/// <reference path="comp.print.resources.js" />
// 引入qrcode库
import QRCode from 'qrcode-generator';
/**
 * 暴露 comp.print.lodop
 *
 * 封装与打印相关数据和 Dom 操作
 */
(function (window) {
	const lodopObj = nameSpace('comp.print.lodop');
	const lodopCallbackMap = {};
	let hiddenLodop = null;
	let isInitLodop = 0;
	const pxScale = 3.78;

	//初始化方法
	lodopObj.init = function (callback) {
		const id = 'sysHidden_' + (+new Date);
		const fn = new comp.Print.FN();
		hiddenLodop = fn.createLodopDom(null, id, 0, 0);
		isInitLodop = 1;
		lodopObj.dom = hiddenLodop;
		lodopObj.isInit = true;
	};
	//引入clodop
	lodopObj.initClodop = function () {

		const head = document.head || document.getElementsByTagName('head')[0] || document.documentElement;
		let oscript = document.createElement('script');
		if (location.protocol == 'https:') {
			const isUseNewBrowser = this.getChrome();
			// 发现6.5.6.6lodop版本与拼多多商家后台不兼容，经排查是与chorme旧内核不兼容导致，所以默认走1逻辑。   但新lodop的js有可能缺失功能，所以配置了个能切到旧lodop的配置，以供临时解决
			if (isUseNewBrowser || !localStorage.getItem('useOldLodop')) {
				oscript.src = 'https://localhost.lodop.net:8443/CLodopfuncs.js'; // chrome升级到83以上
			} else {
				oscript.src = 'https://localhost:8443/CLodopfuncs.js';
			}
			oscript.onerror = (event) => {
				loadCLodop_ws();
			};
			oscript.onload = function () {
				const funcHook = (window.printAPI.compHookObj || {}).afterLoadClodopFile;
				funcHook && funcHook();
			}
			head.insertBefore(oscript, head.firstChild);
		} else {
			let scriptLoadErr = false;  // 当8000与18000端口的js都引入失败时，使用ws进行链接
			oscript.src = 'http://localhost:8000/CLodopfuncs.js?priority=1';
			oscript.onerror = (...arg) => {
				if (scriptLoadErr) {
					loadCLodop_ws();
				}
				scriptLoadErr = true;
			};
			head.insertBefore(oscript, head.firstChild);
			//引用双端口(8000和18000）避免其中某个被占用：
			oscript = document.createElement('script');
			let oscript2 = document.createElement('script');
			oscript2.src = 'http://localhost:18000/CLodopfuncs.js?priority=0';
			oscript2.onerror = (...arg) => {
				if (scriptLoadErr) {
					loadCLodop_ws();
				}
				scriptLoadErr = true;
			};
			oscript2.onload = function () {
				const funcHook = (window.printAPI.compHookObj || {}).afterLoadClodopFile;
				funcHook && funcHook();
			}
			head.insertBefore(oscript2, head.firstChild);

		}
		// setTimeout(()=>{
		//     try {
		//         if(window?.LODOP){
		//             window.LODOP.altMessageWebSocketInvalid = 'WS连接失败，请重启控件或重刷页面';
		//         }
		//         comp.print.lodop.getVersion()

		//     } catch (error) {
		//         console.log(error);
		//     }
		// },1000);
		// script引入lodop的js文件失败时，使用ws的方式引入lodop。    仅高版本lodop控件可用
		function loadCLodop_ws() {
			try {
				const MainJS = 'CLodopfuncs.js';
				const URL_WS1 = 'ws://localhost:8000/' + MainJS;              //ws用8000/18000
				const URL_WS2 = 'ws://localhost:18000/' + MainJS;
				var WSK1 = new WebSocket(URL_WS1);
				WSK1.onopen = function (e) {
					const funcHook = (window.printAPI.compHookObj || {}).afterLoadClodopFile;
					funcHook && funcHook();
				};
				WSK1.onmessage = function (e) { ; if (!window.getCLodop) eval(e.data); };
				WSK1.onerror = function (e) {
					var WSK2 = new WebSocket(URL_WS2);
					WSK2.onopen = function (e) {
						const funcHook = (window.printAPI.compHookObj || {}).afterLoadClodopFile;
						funcHook && funcHook();
					};
					WSK2.onmessage = function (e) { if (!window.getCLodop) eval(e.data); };
					WSK1.onerror = function (e) {
						console.log('lodop链接不上');
					};
				};
			} catch (err) {
				console.log('try err内部错误，lodop链接不上');
			}
		}
	};
	// 获取浏览器版本信息
	lodopObj.getChrome = function () {
		const browsermap = [
			'Chrome/83',
			'Firefox/25',
		];
		let isUseNewBrowser = false; //是否是新版浏览器
		const arr = navigator.userAgent.split(' ');
		let chromeVersion = '';
		for (let i = 0; i < arr.length; i++) {
			if (/(chrome|Firefox)/i.test(arr[i])) {
				chromeVersion = arr[i];
			}
		}
		let chromeVersion_ = '', browserType = '';
		if (chromeVersion) {
			browsermap.map((v, i) => {
				browserType = chromeVersion.split('/')[0];  //当前浏览器的类型
				chromeVersion_ = Number(chromeVersion.split('/')[1].split('.')[0]); //当前浏览器版本

				if (v.split('/')[0] === browserType && chromeVersion_ > Number(v.split('/')[1])) {  //浏览器版本大于browsermap对应的版本，则为新版浏览器
					isUseNewBrowser = true;
				}
			});


		}
		return isUseNewBrowser;
	};
	//获取检测隐藏的Lodop
	lodopObj.getHiddenLodop = function () {
		if (lodopObj.dom) {
			return lodopObj.dom;
		}
		if (isInitLodop < 3) { // 重连三次后还是没链接上，则不再重连
			this.init();
		}
		return lodopObj.dom;
	};

	//获取最新ID
	// lodopObj.getNewLodopKey = function (lodop) {
	//     let key = getRandomNum();
	//     with (lodop.GET_VALUE("ItemExist", key)) {
	//         key = getRandomNum();
	//     }
	//     return key;
	// };

	//指定lodop背景图
	lodopObj.setLodopBgImg = function (lodop, imgSrc) {
		lodop.ADD_PRINT_SETUP_BKIMG('<img border=\'0\' src=\'' + imgSrc + '\'>'); //指定背景图
	};

	//获取所有打印机
	lodopObj.getPrinters = function () {
		let arr = []
			, i
			, printname
			, PrinterCount;
		if (!hiddenLodop) {
			hiddenLodop = this.getHiddenLodop();
		}
		if (!hiddenLodop) {
			return [];
		}
		try {
			PrinterCount = hiddenLodop.GET_PRINTER_COUNT();
			for (i = 0; i < PrinterCount; i++) {
				printname = hiddenLodop.GET_PRINTER_NAME(i);
				if (printname != 'Fax' && printname != 'Microsoft Office Document Image Writer') {
					arr.push(printname);
				}
			}
			return arr;
		} catch (error) {
			console.log(error);
			return arr;
		}
	};
	//获取所有打印机
	lodopObj.getVersion = function () {
		if (!hiddenLodop) {
			hiddenLodop = this.getHiddenLodop();
		}

		try {
			comp.Print.Data.lodopVersion = hiddenLodop?.VERSION
		} catch (error) {
			console.log(error);
		}
	};
	// //认证
	// lodopObj.signProvePrint = function(serial,selectedPrinter ){
	//     //设置默认打印机
	//     if(!hiddenLodop){
	//         hiddenLodop = this.getHiddenLodop();
	//     }
	//     hiddenLodop.SET_PRINT_MODE("WINDOW_DEFPRINTER", selectedPrinter);
	//     hiddenLodop.SET_PRINTER_INDEX(selectedPrinter || -1 );

	//     hiddenLodop.SET_PRINT_MODE("SEND_RAW_DATA_ENCODE","UTF-8");//UTF-8 UTF-7 UNICODE ANSI UTF-16 UTF-16BE GBK BIG5 EUC-JP
	//     if (hiddenLodop.SEND_PRINT_RAWDATA(serial)){
	//         console.log("发送命令成功！");
	//     }else{
	//         console.log("发送命令失败！");
	//     }
	// }


	//获取默认打印机
	lodopObj.getDefaultPrinter = function () {
		if (!hiddenLodop) {
			hiddenLodop = this.getHiddenLodop();
		}
		if (!hiddenLodop) {
			return '未安装控件';
		}
		return hiddenLodop.GET_PRINTER_NAME(-1);
	};

	//获取客户端信息
	lodopObj.getLodopInfo = function (info) {
		if (!hiddenLodop) {
			hiddenLodop = this.getHiddenLodop();
		}
		return hiddenLodop.GET_SYSTEM_INFO(info);
	};

	//保存Html到Excel
	lodopObj.saveAsExcel = function (name, width, height, html) {
		if (!hiddenLodop) {
			hiddenLodop = this.getHiddenLodop();
		}
		hiddenLodop.PRINT_INIT('');
		hiddenLodop.ADD_PRINT_TABLE(0, 0, width, height, html);
		hiddenLodop.SET_SAVE_MODE('Orientation', 1); //Excel文件的页面设置：横向打印   1-纵向,2-横向;
		hiddenLodop.SET_SAVE_MODE('PaperSize', 9); //Excel文件的页面设置：纸张大小   9-对应A4
		hiddenLodop.SET_SAVE_MODE('Zoom', 100); //Excel文件的页面设置：缩放比例
		hiddenLodop.SET_SAVE_MODE('CenterHorizontally', true); //Excel文件的页面设置：页面水平居中
		hiddenLodop.SET_SAVE_MODE('CenterVertically', true); //Excel文件的页面设置：页面垂直居中
		hiddenLodop.SAVE_TO_FILE(name);
	};

	//保存Html到文件----避免提示excel未安装问题
	lodopObj.saveAsFile = function (filePath, width, height, html) {
		let strResult;
		if (!hiddenLodop) {
			hiddenLodop = this.getHiddenLodop();
		}
		if (hiddenLodop.CVERSION) {
			CLODOP.On_Return = function (TaskID, Value) {
				if (Value !== 'ok') {
					Tatami.showFail('导出失败：' + Value);
				} else {
					Tatami.showSuccess('导出成功');
				}
			};
		}
		strResult = hiddenLodop.WRITE_FILE_TEXT(0, filePath, html);
		if (!hiddenLodop.CVERSION) {
			if (strResult !== 'ok') {
				Tatami.showFail('导出失败：' + strResult);
			} else {
				Tatami.showSuccess('导出成功');
			}
		}
	};

	/**
	   * 打印Html
	   * html:html内容
	   * w:宽度
	   * h:高度
	   * intorient:1---纵(正)向打印，固定纸张； 2---横向打印，固定纸张；  3---纵(正)向打印，宽度固定，高度按打印内容的高度自适应；0(或其它)----打印方向由操作者自行选择或按打印机缺省设置；
	   * pageSize: let pageSize = { intPageWidth: 0, intPageHeight: 0, strPageName: "A4" } (默认值可以不传)；
	   * hasView:打印前是否预览 true:预览 (不传直接打印)；
	   * defPrinter:默认打印机
	   * modeid:打印类型
	   * isNeedDef:是否需要延迟
	   * update: ck  2016-3-01
	   */
	lodopObj.printHTML = function (lodopDom, html, width, height, intOrient, pageSize, hasView, defPrinter, isNeedDef) {
		lodopDom.PRINT_INIT('打印-快递助手');
		// if (modeid == 'jhd') {
		//     lodopDom.SET_PRINTER_INDEX(defPrinter);
		// }
		// if (modeid == 'bhd') {
		//     lodopDom.SET_PRINTER_INDEX(defPrinter);
		// }
		lodopDom.SET_PRINTER_INDEX(defPrinter);
		if (pageSize) {
			lodopDom.SET_PRINT_PAGESIZE(intOrient, pageSize.intPageWidth, pageSize.intPageHeight, pageSize.strPageName);
		} else {
			lodopDom.SET_PRINT_PAGESIZE(intOrient, 0, 0, 'A4');
		}
		lodopDom.ADD_PRINT_HTM(0, 0, '100%', '100%', html);
		if (isNeedDef) {   //图片超过 延时处理
			lodopDom.SET_PRINT_STYLEA(0, 'HtmWaitMilSecs', 1000);
		}
		if (hasView) {
			lodopDom.SET_PRINT_MODE('RESELECT_ORIENT', true);
			lodopDom.SET_PRINT_MODE('RESELECT_PAGESIZE', true);
			lodopDom.SET_PRINT_MODE('RESELECT_COPIES', true);
			lodopDom.SET_PRINT_MODE('RESELECT_PRINTER', true);
			lodopDom.SET_PRINT_MODE('AUTO_CLOSE_PREWINDOW', true); //设置打印完毕是否自动关闭预览窗口
			lodopDom.PREVIEW();
		} else {
			lodopDom.PRINT();
		}
	};

	/*打印模板
		* lodop : lodop 控件
		* printdatas:要打印的数据
		* templateSet:模板内容
		* ptype:快递单还是发货单 kdd/fhd
		* hasView:是否打印预览
		* printBoxIsShow:是否显示系统打印机选择框
		* selectedPrinter:已选择的打印机
		 * printSynFunc:打印异步执行方法，进度条等等
		  * clearFunc:选择打印机取消事件
		   * printOkFunc:打印成功后调用
		*/
	lodopObj.printTemplate = function (lodop, printdatas, templateSet, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc, arg) {
		const dataObj = comp.Print.Data;
		dataObj.isFirstPrint = true;
		dataObj.templateSet = templateSet;
		// dataObj.isLinkedItem = false;
		if (ptype == 'kdd') {
			this.printKdd(lodop, printdatas, hasView, 0, printBoxIsShow, printSynFunc, clearFunc, printOkFunc, selectedPrinter);
		} else {
			let newPrintData = printdatas;
			if (hasView && templateSet.ModeTempPrintcfg.paperSheet > 1) {
				printdatas.push(printdatas[0])
				newPrintData = this.addPlaceholder(printdatas);
			}
			if (!hasView && ptype == 'bhd' && templateSet.ModeTempPrintcfg.paperSheet > 1) {
				newPrintData = this.addPlaceholder(printdatas);
			}
			this.printFhd({
				lodop,
				printdatas: newPrintData,
				hasView,
				valtem: 0,
				printBoxIsShow,
				printSynFunc,
				clearFunc,
				printOkFunc,
				selectedPrinter,
				ptype: ptype,
				arg
			});
		}

	};

	lodopObj.addPlaceholder = function (printdatas) {
		let idx = 0,
			newPrintData = [];
		printdatas.map(function (v, i) {
			if (v.isPrintDivider && (idx) % 2 === 1) {
				newPrintData.push({
					isPlaceholder: true,
				});
				idx++;
			}
			newPrintData.push(v);
			idx++;
		});
		return newPrintData;
	};


	lodopObj.singleLodopPrint = function (arg, printOkFunc) {
		//全局设置
		this.setGlobal_lodop(arg.printType, arg.lodop, false, arg.templateSet);
		//绘制  lodop, printDataItem, templateSet, templateType, isShowTempImg, showType, starTop, starLeft,hasView,isNotEditTable
		this.loadPrintItem(arg.lodop, arg.printData, arg.templateSet, arg.printType, false, false, 0, 0, false, false, arg.printType == 'fhd');
		if (arg.printType == 'fhd') {
			this.printTasksDraw(0, arg.lodop);
		}
		//打印
		this.printData(arg.lodop, arg.hasView, arg.printBoxIsShow, arg.selectedPrinter, null, printOkFunc, arg.printType, arg.drawAgin, arg.templateSet);
	};

	//打印的全局设置
	lodopObj.setGlobal_lodop = function (type, lodop, isDef, templateSet) {
		let dataObj = comp.Print.Data,
			that = new comp.Print.FN(),
			_direction = 0,
			_upDown = 0,
			_leftRight = 0,
			_OFFSET = 0,
			_tempSet = templateSet || dataObj.templateSet,
			_modeTempPrintcfg,
			_modeList,
			_trueWidth;
		if (!_tempSet) {
			alert('模板数据错误');
			return;
		}
		_modeTempPrintcfg = _tempSet.ModeTempPrintcfg,
			_modeList = _tempSet.ModeList;
		_OFFSET = (type == 'fhd' && _modeList.KddType < 4 ? Math.ceil(dataObj.marginTop / dataObj.mmToPxUnit) : 0);
		if (_modeList.Exid === 671) {
			_OFFSET = 0;
		}
		//重置纸张
		dataObj.currentPage = 1;
		//读取默认纸张
		// 修复单双排小标签逻辑
		_trueWidth = type == 'fhd' && (comp.base.getFhdType(_tempSet) === 'fhd-sm-bq' || comp.base.getFhdType(_tempSet) === 'fhd-nh-bq') || ['bhd', 'thd', 'dpd', 'wdpd', 'tmd', 'wtmd', 'bhbq'].includes(type)
			? _modeTempPrintcfg.Width * (_modeTempPrintcfg.paperSheet ? _modeTempPrintcfg.paperSheet : 2)
			: _modeTempPrintcfg.Width;

		if (_modeTempPrintcfg) {
			dataObj.pageHMmLodop = parseFloat(_modeTempPrintcfg.Height);
			dataObj.pageWMmLodop = parseFloat(_trueWidth);
			_direction = ~~(_modeTempPrintcfg.Direction);
			_upDown = ~~(_modeTempPrintcfg.Updown);
			_leftRight = ~~(_modeTempPrintcfg.Leftright);
		} else {
			//if ( _tempSet) {
			if (_modeList.KddType > 1) {
				dataObj.pageWMmLodop = 100;
			} else {
				dataObj.pageWMmLodop = _modeList.WidthPaper / 10;
			}
			dataObj.pageHMmLodop = _modeList.HeightPaper / 10;
			// }
			// else {
			//     dataObj.pageWMmLodop = lodop.GET_VALUE("PRINTSETUP_PAGE_WIDTH", "selected") / 10;
			//     dataObj.pageHMmLodop = lodop.GET_VALUE("PRINTSETUP_PAGE_HEIGHT", "selected") / 10;
			// }
		}

		if (!lodop) {
			alert('控件未安装，请联系客服');
			return;
		} else if (lodop.VERSION && lodop.CVERSION) {
			//检查是否已连接上clodop
		}
		if (!isDef) {       //打印
			if (_direction == 3 && dataObj.isSinglePrint) {
				lodop.PRINT_INIT('快递助手-' + (type === 'kdd' ? '快递单' : '发货单') + '打印任务');
				lodop.SET_PRINT_STYLEA('PRINT_INIT', 'Top', _upDown + _OFFSET + 'mm');
				lodop.SET_PRINT_STYLEA('PRINT_INIT', 'Left', _leftRight + _OFFSET + 'mm');
			} else {
				lodop.PRINT_INITA(_upDown + _OFFSET + 'mm', _leftRight + _OFFSET + 'mm', (dataObj.pageWMmLodop + 4) + 'mm', dataObj.pageHMmLodop + 'mm', '快递助手-' + (type === 'kdd' ? '快递单' : '发货单') + '打印任务');
			}
			lodop.SET_PRINT_PAGESIZE(_direction, dataObj.pageWMmLodop + 'mm', _direction == 3 ? 20 : dataObj.pageHMmLodop + 'mm', '');
			if (_direction == 2) {
				lodop.SET_PRINT_PAGESIZE(_direction, dataObj.pageHMmLodop + 'mm', _direction == 3 ? 20 : dataObj.pageWMmLodop + 'mm', '');
			}
		} else {        //编辑
			lodop.PRINT_INITA(0, 0, (dataObj.pageWMmLodop + 5) + 'mm', (_direction == 3 ? 180 : dataObj.pageHMmLodop) + 'mm', '');
			lodop.SET_PRINT_PAGESIZE(_direction != 3 ? 0 : _direction, dataObj.pageWMmLodop + 'mm', _direction == 3 ? 20 : dataObj.pageHMmLodop + 'mm', '');
		}
		lodop.SET_SHOW_MODE('LANGUAGE', 0);
		//设置是否进行对后台服务的打印状态进行捕获
		lodop.SET_PRINT_MODE('CATCH_PRINT_STATUS', false);
		lodop.SET_PRINT_MODE('POS_BASEON_PAPER', true); //设置输出位置以纸张边缘为基点
		lodop.SET_PRINT_MODE('NOCLEAR_AFTER_PRINT', true); //打印或预览后会清空所有内容
		lodop.SET_PRINT_MODE('AUTO_CLOSE_PREWINDOW', true); //设置打印完毕是否自动关闭预览窗口
		lodop.SET_SHOW_MODE('BKIMG_PRINT', 0); //设置打印时是否包含背景图

		lodop.SET_LICENSES('北京格玩科技有限公司', '653587569718688748719056235623', 'kuaidizs.cn', '2F71232D415F674063F2FD9EFD7C6BBE75');
		//菜鸟官方模板不走全局设置
		if (type == 'kdd' && !(_modeList.KddType == 3 && _modeList.StyleId == 2)) {
			dataObj.tempDefaultFontSize = that.getFontSize_lodop(dataObj.globalSettingKdd.ModeSet.Fontsize);
			dataObj.tempDefaultFontName = dataObj.globalSettingKdd.ModeSet.Fontname;
			lodop.SET_PRINT_MODE('PRINT_PAGE_PERCENT', 'Width:' + dataObj.globalSettingKdd.ModeSet.SliderW + '%;Height:' + dataObj.globalSettingKdd.ModeSet.SliderH + '%'); //指定整页缩放打印的比例
			lodop.SET_PRINT_STYLE('FontName', dataObj.tempDefaultFontName); //设定纯文本打印项的字体名称
			lodop.SET_PRINT_STYLE('FontSize', dataObj.tempDefaultFontSize); //设定纯文本打印项的字体大小
			lodop.SET_PRINT_STYLE('Bold', ((dataObj.globalSettingKdd.ModeSet.Isb == true || dataObj.globalSettingKdd.ModeSet.Isb == '1' || dataObj.globalSettingKdd.ModeSet.Isb == 1) ? 1 : 0)); //设定纯文本打印项是否粗体
		} else if (type == 'fhd') {
			// debugger
			dataObj.tempDefaultFontSize = that.getFontSize_lodop(dataObj.globalSettingFhd.ModeSet.Fontsize);
			dataObj.tempDefaultFontName = dataObj.globalSettingFhd.ModeSet.Fontname;
			lodop.SET_PRINT_MODE('PRINT_PAGE_PERCENT', 'Width:' + dataObj.globalSettingFhd.ModeSet.SliderW + '%;Height:' + dataObj.globalSettingFhd.ModeSet.SliderH + '%'); //指定整页缩放打印的比例
			lodop.SET_PRINT_STYLE('FontName', dataObj.globalSettingFhd.ModeSet.Fontname); //设定纯文本打印项的字体名称
			lodop.SET_PRINT_STYLE('FontSize', dataObj.tempDefaultFontSize); //设定纯文本打印项的字体大小
			lodop.SET_PRINT_STYLE('Bold', ((dataObj.globalSettingFhd.ModeSet.Isb == true || dataObj.globalSettingFhd.ModeSet.Isb == '1' || dataObj.globalSettingFhd.ModeSet.Isb == 1) ? 1 : 0)); //设定纯文本打印项是否粗体
			// lodop.SET_PRINT_STYLEA(0, "LineSpacing", that.getLineSpacing_lodop(dataObj.tempDefaultFontSize, -1)); //纯文本的行间距
		}
	};

	//打印快递单
	lodopObj.printKdd = function (lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc, selectedPrinter) {
		console.log('打印快递单');
		const that = this;
		const dataObj = comp.Print.Data;
		//打印
		if (!selectedPrinter) {
			selectedPrinter = dataObj.templateSet.ModeTempPrintcfg ? dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter : that.getDefaultPrinter();
		}
		if (dataObj.templateSet.ModeList.Exid == 89) {              //韵达网点 ---打印需要安装客户端，并且用极速模式
			let copies = comp.Print.Data.printCopies
				, values = [];
			if (copies > 1) {           //韵达客户端 打印多份 数据处理
				$.each(printdatas, function (k, v) {
					for (let i = copies; i > 0; i--) {
						values.push(v);
					}
				});
			} else {
				values = $.extend(true, [], printdatas);
			}
			// let values = [];
			// for (let i = 0; i < printdatas.length; i++) {
			//     // printdatas[i].sfjc = _p30_getSfjcVal(printdatas[i].s_p);
			//     values.push(printdatas[i]);
			// }
			const parameter = {};
			parameter['tname'] = 'mailtmp_s2';
			parameter['docname'] = 'mailpdfm1';
			parameter['t'] = new Date().getTime();
			parameter['value'] = values.join('@');
			$.ajax({
				type: 'Post',
				dataType: 'json', //返回json格式的数据
				url: '//localhost:9090/ydecx/service/mailpx/printDirect.pdf',
				data: parameter,
				complete: function (request) {
					if (request.responseText != 'success') {
						let errorMessage = '';
						request.readyState != 4  //如果韵达打印客户端没准备好
							? errorMessage = '打印失败,请检查韵达快递打印客户端服务是否已安装并开启'
							: errorMessage = '请联系网点确认配置是否正确';

						Tatami.showFail(errorMessage);
					}
				},
			});
			(typeof printOkFunc === 'function') ? printOkFunc() : '';
		} else {
			dataObj.printConfig = printdatas[0].printconfig;
			const plPrints = that.getPrintModelsPL(printdatas, dataObj.kddGroupCount);
			if (valtem < plPrints.length) {
				that.setGlobal_lodop('kdd', lodop);
				(typeof printSynFunc === 'function') ? printSynFunc(valtem * dataObj.kddGroupCount) : ''; //回调

				for (let datai = 0; datai < plPrints[valtem].length; datai++) {
					if (datai > 0) {
						if (dataObj.IsCncatFhd && dataObj.templateSet.ModeTempPrintcfg.IsConcatFhd == 1 && dataObj.templateSet.ModeList.KddType == 3 && dataObj.templateSet.ModeList.StyleId == 2) {
							that.setGlobal_lodop('kdd', lodop);
						} else {
							lodop.NewPage();
						}
					}
					//开始绘制
					this.loadPrintItem(lodop, plPrints[valtem][datai], dataObj.templateSet, 'kdd', false, false, 0, 0, hasView);

					if (dataObj.IsCncatFhd && dataObj.templateSet.ModeTempPrintcfg.IsConcatFhd == 1 && dataObj.templateSet.ModeList.KddType == 3 && dataObj.templateSet.ModeList.StyleId == 2) {
						that.printData(lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc);
					}

					//连打发货单
					if (dataObj.IsCncatFhd && dataObj.templateSet.ModeTempPrintcfg.IsConcatFhd == 1) {
						if (dataObj.templateSet.ModeList.KddType == 3 && dataObj.templateSet.ModeList.StyleId == 2) {
							that.setGlobal_lodop('kdd', lodop);
						} else {
							lodop.NewPage();
						}
						this.loadPrintItem(lodop, plPrints[valtem][datai], dataObj.cncatFhdtemplate, 'fhd', false, false, 0, 0, hasView);
						this.printTasksDraw(0, lodop);

						if (dataObj.templateSet.ModeList.KddType == 3 && dataObj.templateSet.ModeList.StyleId == 2) {
							that.printData(lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc);
						}
					}
				}

				if (!dataObj.IsCncatFhd || dataObj.templateSet.ModeTempPrintcfg.IsConcatFhd != 1 || dataObj.templateSet.ModeList.KddType != 3 || dataObj.templateSet.ModeList.StyleId != 2) {
					//打印
					that.printData(lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc);
				}
				valtem++;
			}

			if (valtem < plPrints.length) {
				setTimeout(function (lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc) {
					return function () {
						that.printKdd(lodop,
							printdatas,
							hasView,
							valtem,
							printBoxIsShow,
							printSynFunc,
							clearFunc,
							printOkFunc,
							selectedPrinter);
					};
				}(lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc), 100);
			} else {
				(typeof printOkFunc === 'function') ? printOkFunc(printdatas) : ''; //回调
			}
		}

	};

	//打印发货单
	lodopObj.printFhd = function ({
		lodop,
		printdatas,
		hasView,
		valtem,
		printBoxIsShow,
		printSynFunc,
		clearFunc,
		printOkFunc,
		selectedPrinter,
		printedNum,
		ptype,
		otherParams = {},
		arg
	}) {
		console.log('lodop打印发货单');
		let that = this,
			_taskNum,    //一次打印的任务数
			dataObj = comp.Print.Data,
			_modeSet = dataObj.templateSet.ModeSet || {},
			_modeConfig = dataObj.templateSet.ModeTempPrintcfg,
			starLeft = 0,
			starLeftUnit = _modeConfig.Width * pxScale + 6,
			isSmallTag = dataObj.templateSet.ModeList.KddType == 4 || ptype === 'bhd',
			hasStartLeft = isSmallTag && _modeConfig.paperSheet > 1, //退货单不双排打印
			lodopId;
		const { printAction } = otherParams;

		//表格列数量，每一行的高度，四边框线条，标题行线条，合计行线条，产品行线条产品列线条
		//1:黑 2：深灰 3：浅灰 4：无
		if (_modeConfig) {
			dataObj.pageHMmLodop = parseFloat(_modeConfig.Height);
			dataObj.pageWMmLodop = parseFloat(_modeConfig.Width);
		}
		const pw = parseInt(dataObj.pageWMmLodop * dataObj.mmToPxUnit);
		const ph = parseInt(dataObj.pageHMmLodop * dataObj.mmToPxUnit) - (0.01 * parseInt(dataObj.pageHMmLodop * dataObj.mmToPxUnit));
		let printDataItem = null;
		let printItemArr = []; //当前当前打印项的坐标数组
		let contentH = 0; //每一页的内容高度 当在一张纸上打印多个发货单的时候可以用来确定下一个发货单的top值
		let minusH = 0; //当某一项不显示的时候需要把高度减掉
		printedNum = printedNum || 0;
		if (valtem === 0) {
			dataObj.overH = 0;
			dataObj.pTop = 0;
			dataObj.currentPage = 1;
			// dataObj.linkedIndex = 0;
			that.setGlobal_lodop(ptype, lodop);
		}
		//打印
		if (!selectedPrinter) {
			selectedPrinter = dataObj.templateSet.ModeTempPrintcfg != null ? dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter : that.getDefaultPrinter();
		}
		let newPrintdatas = []
		if (['dpd', 'tmd', 'wdpd', 'wtmd'].includes(ptype) && dataObj.printCopies > 1) {
			// 复制打印数据，打印多份
			let printCopies = dataObj.printCopies
			printdatas.forEach(it => {
				for (let i = 0; i < printCopies; i++) {
					newPrintdatas.push(it)
				}
			});
		} else {
			newPrintdatas = printdatas
		}

		if (_modeConfig.A4mode == 0 || hasStartLeft) {
			// 本地缓存的值
			let localTotalPage = localStorage.getItem('localTotalPage')
			let fhdGroupCount = dataObj.fhdGroupCount
			if (localTotalPage && !isNaN(Number(localTotalPage))) fhdGroupCount = Number(localTotalPage)
			//连续纸 自定义高度需单任务发送任务
			_taskNum = dataObj.templateSet.ModeTempPrintcfg.Direction == 3 ? 1 : fhdGroupCount; // dataObj.fhdGroupCount;
			const plPrints = that.getPrintModelsPL(newPrintdatas, _taskNum);
			if (valtem < plPrints.length) {
				if (valtem > 0) {
					that.setGlobal_lodop(ptype, lodop);
				}
				if (typeof printSynFunc === 'function') {
					printSynFunc(printedNum || valtem * _taskNum); //回调
				}
				plPrints[valtem].map(function (pItem, datai) {
					hasStartLeft && (starLeft = datai % 2 === 0 ? 0 : starLeftUnit);
					if (datai > 0 && starLeft == 0) {
						lodop.NewPageA();
					}
					if (!pItem.isPlaceholder) {
						//开始绘制
						printedNum++;
						that.loadPrintItem(lodop, pItem, dataObj.templateSet, ptype, false, false, 0, starLeft);
						that.printTasksDraw(0, lodop);
					}
				});

				lodopId = that.printData(lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc, null, ptype, null, null, printAction, arg);
				valtem++;
			}
			if (valtem < plPrints.length) {
				lodop.On_Return = function (TaskID, Value) {
					lodopObj.printFhd({
						lodop,
						printdatas,
						hasView,
						valtem,
						printBoxIsShow,
						printSynFunc,
						clearFunc,
						printOkFunc,
						selectedPrinter,
						printedNum,
						ptype,
						otherParams,
					})
				};
				// lodopObj.printReturn({lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc,otherParams})
			} else {
				// that.printOver();

				if (lodopId) lodopCallbackMap[lodopId] = printOkFunc;
				lodop.On_Return_Remain = true;
				// 交替打印的lodop发货单打印完成的状态
				lodop.On_Return = function (TaskID, Value) {
					if (typeof lodopCallbackMap[TaskID] === 'function') {
						lodopCallbackMap[TaskID]({ lodop, Value }); //回调
						delete lodopCallbackMap[TaskID];
					}
				};
			}
		} else {
			//拼接
			console.log(printdatas);
			if (valtem < printdatas.length) {
				if (typeof printSynFunc === 'function') {
					printSynFunc(valtem); //回调
				}

				let localTotalPage = localStorage.getItem('localTotalPage')
				let totalPages = 10 // Math.ceil((dataObj.A4Height * dataObj.BatchPages) / ph);
				if (localTotalPage && !isNaN(Number(localTotalPage))) totalPages = Number(localTotalPage)

				printDataItem = printdatas[valtem];
				printItemArr = []; //当前当前打印项的坐标数组
				contentH = 0; //每一页的内容高度 当在一张纸上打印多个发货单的时候可以用来确定下一个发货单的top值
				minusH = 0; //当某一项不显示的时候需要把高度减掉

				// let dataHight = this.loadPrintItem(lodop, printDataItem, dataObj.templateSet, ptype, false, false, 0,0);
				// contentH += (dataHight > ph ? dataHight : ph)

				contentH += this.loadPrintItem(lodop, printDataItem, dataObj.templateSet, ptype, false, false, 0, 0);

				contentH += 20;

				// contentH += 20;
				//let tempPTop = dataObj.pTop;
				let lineFlag = true;
				let IsCalHeight = false;
				let contentMoveH = 1; //打印当前发货单总体需要下移的高度
				if (dataObj.pTop !== 0) {
					dataObj.overH = ph - dataObj.pTop % ph - (dataObj.templateSet.ModeTempPrintcfg.Updown + dataObj.marginTop); //当前页剩余的高度
					IsCalHeight = true;
				}
				if (IsCalHeight) {
					if (contentH >= dataObj.overH) {
						if (contentH - dataObj.overH > 20) {
							dataObj.pTop = 0;
							// dataObj.linkedIndex = 0;
							lodop.NewPageA();
							dataObj.currentPage++;
						} else {
							contentH = contentH - (contentH - dataObj.overH) - 1;
							contentMoveH = 1; //不让ptop加10
							lineFlag = false;
						}

					} else {
						contentMoveH = 0;
					}
					dataObj.pTop += contentMoveH;
				}

				if (lineFlag && !isSmallTag && dataObj.templateSet.ModeList.KddType != 4 && dataObj.templateSet.ModeList.Exid !== 671) { //小标签总是不打印分割线，BIC订单模板也不显示分割线
					// A4纸拼接的时候是否打印分隔线0|1
					if (_modeConfig.A4line == 1) {
						const task = {
							itemKey: '123',//Key
							dataType: 'line',
							itemY: contentH,
							itemX: (0 - dataObj.marginLeft),
							itemW: (pw - dataObj.marginLeft),
							itemH: 1,
							lineStyle: 2,
						};
						dataObj.printTaskArr_lodop.push(task);
					}
				}
				//按纸张传任务打印
				if (dataObj.currentPage > totalPages || valtem == printdatas.length - 1) {
					if (dataObj.currentPage > totalPages) {
						valtem--;
						dataObj.printTaskArr_lodop = [];
					} else {
						that.printTasksDraw((dataObj.pTop + contentMoveH), lodop);
					}
					// that.printData(lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc, undefined, 'fhd');
					lodopId = that.printData(lodop, hasView, printBoxIsShow, selectedPrinter || '', clearFunc, null, 'fhd', null, null, printAction, arg);
					dataObj.overH = 0;
					dataObj.pTop = 0;
					dataObj.currentPage = 1;
					// dataObj.linkedIndex = 0;
					that.setGlobal_lodop(ptype, lodop);
				} else {
					//将打印任务发送给打印机
					that.printTasksDraw((dataObj.pTop + contentMoveH), lodop);
					dataObj.pTop += contentH;
					//打印-下一页需要下移的偏移
					dataObj.pTop += contentMoveH > 0 ? 0 : 10;
				}
			}
			valtem++;
			if (valtem < printdatas.length) {
				setTimeout(function (lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc, otherParams) {
					return function () {
						that.printFhd({
							lodop,
							printdatas,
							hasView,
							valtem,
							printBoxIsShow,
							printSynFunc,
							clearFunc,
							printOkFunc,
							selectedPrinter,
							printedNum,
							ptype,
							otherParams,
						});
					};
				}(lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc, otherParams), 100);
			} else {
				if (lodopId) lodopCallbackMap[lodopId] = printOkFunc;
				lodop.On_Return_Remain = true;
				// 交替打印的lodop发货单打印完成的状态
				lodop.On_Return = function (TaskID, Value) {
					if (typeof lodopCallbackMap[TaskID] === 'function') {
						/* lodopCallbackMap[TaskID]里的传参都是为了printZbXbq(打印直播标签)需求，  其他打印流程无需返回参数 */
						lodopCallbackMap[TaskID]({ Value, lodop }); //回调
						delete lodopCallbackMap[TaskID];
					}
				};
			}
		}


	};
	/*绘制打印项
	topY：初始高度
   lodop：打印控件对象
  */
	lodopObj.printTasksDraw = function (topY, lodop) {
		const dataObj = comp.Print.Data;
		const that = new comp.Print.FN();
		$.each(dataObj.printTaskArr_lodop, function (j, item) {
			that.drawItem(lodop, item, topY, 'print');//发送打印任务
		});
		dataObj.printTaskArr_lodop = [];
	};

	//计算Table高度
	lodopObj.getTableHeight = function (tableHtml) {
		let that = new comp.Print.FN()
			, outBorderWidth = 0
			, brower = that.getBrower();
		const parent = $('<div style=\'position:absolute;width:16000px;height:100px;overflow:auto;left:-18000px;top:-200px;z-index:9999\'></div>').appendTo('body');
		let zoom = 3;
		if (brower.name == 'ie') {
			zoom = 1;
		}
		const table = $(tableHtml.replace(/((\d)+)px/g, function (ind, inv, grind) {
			const val = inv * zoom;
			return val + 'px';
		})).appendTo(parent);

		let height = Math.ceil(table.height() / zoom) + zoom;

		if (table.css('border-style') != 'none') {
			outBorderWidth = $(tableHtml).css('border-width') == 'bold' ? 2 : 1;
		}
		if (brower.name == 'ie') {
			if (brower.version < 9) {
				height = height + outBorderWidth * 2;
			} else {
				height = height + outBorderWidth;
			}
		}
		//提供足够空间冗余
		height += 4;
		return height;
	};




	//生成发货单表格td 的打印任务
	lodopObj.getFhdTdPrintTask = function ($span, parentX, parentY) {
		const printTask = {};
		const $td = $span.closest('td');
		const parentH = $td.height();
		printTask.content = $span.text();
		printTask.itemKey = $span.attr('data-key');
		printTask.dataType = 'txt';
		//printTask.itemY =  $span.position().top + parentY;//Y
		printTask.itemX = $td.position().left + parentX + 3;//X
		printTask.itemW = $td.width() - 2;//W

		if (!printTask.content) {
			$span.text('1');
			printTask.itemH = $span.height() || 20;//H
			printTask.itemY = $span.position().top + parentY;//Y

			$span.text('');
		} else {
			printTask.itemH = $span.height() || 20;//H
			printTask.itemY = $span.position().top + parentY;//Y
			if (printTask.itemY < $td.position().top + parentY) {
				printTask.itemY = $td.position().top + parentY;
			}
		}

		if (parentH > printTask.itemH + 5) {
			printTask.itemH += 5;
		} else {
			printTask.itemH = parentH;
		}

		printTask.fontSize = $td.attr('data-size') || 0;//字体大小
		printTask.fontSize = (printTask.fontSize == 0 ? comp.Print.Data.tempDefaultFontSize : new comp.Print.FN().getFontSize_lodop(printTask.fontSize));//字体大小
		printTask.fontName = $span.css('font-family');//字体
		printTask.readOnly = 0;//纯文本内容在打印维护时，是否禁止修改
		printTask.bold = $span.css('font-weight') == 'bold' ? 1 : 0;
		printTask.alignment = $td.css('text-align');
		if (printTask.alignment == 'center') {
			printTask.alignment = 2;
		} else if (printTask.alignment == 'right') {
			printTask.alignment = 3;
		} else {
			printTask.alignment = 1;
		}
		printTask.lineSpacing = -1;
		printTask.letterSpacing = -2;//字间距 TODO
		printTask.isEdit = 1;//是否允许控件被修改删除
		printTask.isMove = 0;//是否允许控件被移动
		printTask.direction = 0;//打印方向 默认横向
		return printTask;
	};
	lodopObj.getXbqDriverInput = function (printDataItem) {
		return [{
			'DataKey': '',
			'Exlistshow_mfhdId': 0,
			'Exlistshow_mkddId': 47440,
			'Fontname': '黑体',
			'Fontsize': 18,
			'H_': 60,
			'Hjj': -1,
			'InputID': 22971,
			'Inputtype': 'kdd',
			'IsDraggable': null,
			'IsEditable': null,
			'Isb_n': 1,
			'Status': 1,
			'Str_h': '',
			'Str_q': printDataItem.dividerStr,
			'W_': 157,
			'X_': 13,
			'Y_': 35,
			'Zjj': -1,
			'align': 1,
			'proArray': [],
		}];
	};

	/*绘制打印项
	  lodop：打印控件对象
	  printDataItem：打印数据
	  templateSet：模板设置
	  templateType：面单类型 kdd||fhd
	  isShowTempImg：是否显示底图
	  showType: setup 主页面，design 编辑页面
	 */
	lodopObj.loadPrintItem = function (lodop, printDataItem, templateSet, templateType, isShowTempImg, showType, starTop, starLeft, hasView, isNotEditTable, isNotAccountY) {
		let dataObj = comp.Print.Data,
			cp_code = templateSet.ModeList.Excode,
			exId = templateSet.ModeList.Exid,
			that = new comp.Print.FN(),
			isCompatible = templateType !== 'kdd',
			modeH = 0,
			_styleId = templateSet.ModeList.StyleId,
			_kddType = templateSet.ModeList.KddType,//模板高度
			isCainiao = (templateType === 'kdd' && _kddType == 3 && comp.base.getTempStyle('cainiao', _styleId)),
			isSanlianCainiao = (templateType === 'kdd' && _kddType == 3 && comp.base.getTempStyle('sanlian', _styleId)),
			isAddMargin = false,
			_top = templateSet.ModeTempPrintcfg.Updown * dataObj.mmToPxUnit + dataObj.marginTop,
			_contentHeight = templateSet.ModeTempPrintcfg.Height * dataObj.mmToPxUnit - _top - 5,
			_isCountTop = (isCompatible && _kddType == 1),
			nowY, _page, _addY;

		if (isShowTempImg) {//预览编辑
			// this.printOver();
			dataObj.templateSet = templateSet;
			dataObj.printConfig = printDataItem.printconfig;
			that.setGlobal_lodop(templateType, lodop, (dataObj.isSinglePrint && showType == 'setup') ? false : true);
		}
		//处理省份简称
		printDataItem.sfjc = _p30_getSfjcVal(printDataItem.s_p);

		if (!isSanlianCainiao) { //普通模板  非菜鸟官方三联模板 菜鸟官方三联模板用户不可编辑
			let inputArr = $.extend([], templateSet.ModeInputs, true); //模板文本框数组

			// 小标签
			if (printDataItem.isPrintDivider) {
				//针对只打印分割线补充打印项
				inputArr = this.getXbqDriverInput(printDataItem);
			}

			//针对安能需转单 个性化需求 手动添加一个打印任务  打印出需转单几个字
			if (!isCompatible && cp_code == 'DISTRIBUTOR_12017865' && printDataItem.mdd_dzmd.indexOf('需转单') > -1) {
				printDataItem.mdd_dzmd = printDataItem.mdd_dzmd.replace('需转单', '');
				inputArr.push({
					'DataKey': '',
					'Exlistshow_mfhdId': 0,
					'Exlistshow_mkddId': 47440,
					'Exuserid': 1721500167,
					'Fontname': '黑体',
					'Fontsize': 28,
					'H_': 36,
					'Hjj': -1,
					'InputID': 22971,
					'Inputtype': 'kdd',
					'IsDraggable': null,
					'IsEditable': null,
					'Isb_n': 1,
					'Isedit': 1,
					'Status': 1,
					'Str_h': '',
					'Str_q': '需转单',
					'W_': 131,
					'X_': 125,
					'Y_': 9,
					'Zjj': -1,
					'proArray': [],
				});
			}
			for (let i = 0; i < inputArr.length; i++) {
				const printTask = this.getPrintTaskByInput(inputArr[i], printDataItem, templateSet, isCompatible);
				if (starLeft && starLeft > 0) {
					printTask.itemX += starLeft;
				}

				if (printTask != null) {
					if (isCompatible && (!isShowTempImg || dataObj.isSinglePrint && showType != 'DESIGN')) { //非快递单业务处理
						if (!printTask.oldH) {
							printTask.oldH = printTask.itemH;
						}
						printTask.oldY = printTask.itemY;
						if (isNotAccountY) {  //不需要计算Y值
							nowY = printTask.itemY;
							if (_isCountTop && nowY > _contentHeight) {
								_addY = ~~(~~(nowY / _contentHeight) * _top);
								nowY += _addY;
								console.log('增加的偏移量' + _addY);
								printTask.itemY = nowY;
							}
						} else {
							nowY = that.getPrintItemXY(dataObj.printTaskArr_lodop, printTask);
							//跨页计算位置 需要加上页边距  只对普通发货单计算该偏移距离
							if (_isCountTop && nowY > _contentHeight && !isAddMargin) {
								isAddMargin = true;
								_page = parseInt(nowY / _contentHeight);
								console.log('计算页数' + _page);
								nowY += parseInt(_page * _top) + (parseInt(_page / 5) * 10);   // 1/5概率出现 TableRowThickNess分页颗粒度下边距
							}
							printTask.itemY = nowY;
						}
						if (modeH < nowY + printTask.itemH) {
							modeH = nowY + printTask.itemH;
						}
						dataObj.printTaskArr_lodop.push(printTask);
					}
					//当是快递单或者是测试打印则立即绘制
					if (!isCompatible || isShowTempImg) {
						that.drawItem(lodop, printTask, starTop, showType); //发送打印任务
						// dataObj.linkedIndex = 0;
					}
				}
			}

			if (!isCompatible || isShowTempImg) {
				dataObj.printTaskArr_lodop = [];
			}
		}

		if (isShowTempImg || isCainiao) {
			//云打印预览
			let ImgSrc = null;
			if (isSanlianCainiao || isCainiao) {
				if (hasView) {
					lodop.SET_SHOW_MODE('BKIMG_PRINT', 1); //打印时是否包含背景图
				}
				ImgSrc = (comp.print.resources.getBackGroundImage(_styleId, showType !== 'setup' && !hasView, cp_code, exId) || {}).imgSrc;
			}
			let imageUrl = ImgSrc || templateSet.ModeListShow.ImgSrc;
			if (imageUrl) {
				imageUrl = comp.base.dealBackgroundUrl(imageUrl);
				lodop.ADD_PRINT_SETUP_BKIMG('<img border=\'0\' src=\'' + imageUrl + '\'>'); //指定背景图
				lodop.SET_SHOW_MODE('BKIMG_IN_PREVIEW', 1); //打印预览时是否包含背景图
				lodop.SET_SHOW_MODE('BKIMG_LEFT', 0); //设置背景图位置X值
				lodop.SET_SHOW_MODE('BKIMG_TOP', 0); //设置背景图位置Y值
				lodop.SET_SHOW_MODE('BKIMG_WIDTH', templateSet.ModeList.WidthImg); //设置背景图宽度
				lodop.SET_SHOW_MODE('BKIMG_HEIGHT', templateSet.ModeList.HeightImg); //设置背景图高度
			}
		}
		if (isShowTempImg) {
			lodop.SET_SHOW_MODE('HIDE_GROUND_LOCK', 1);  //隐藏纸钉按钮
			lodop.SET_SHOW_MODE('HIDE_TOOLS_DESIGN', 1); //隐藏整个工具栏
			if (showType == 'setup') {
				if (!dataObj.isSinglePrint) {
					lodop.SET_PRINT_MODE('POS_BASEON_PAPER', false);
					lodop.SET_PREVIEW_WINDOW(1, 3, 0, 400, 680, '预览查看.打印前预览');
					lodop.SET_SHOW_MODE('PREVIEW_IN_BROWSE', 1);
					const divLodop = $(lodop).parent('div');
					if (dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter !== '' && this.checkPrinter(dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter)) {
						lodop.SET_PRINTER_INDEX(dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter || -1);
					}
					lodop.PREVIEW();
					divLodop.height(divLodop.height() - 1);
					divLodop.height(divLodop.height() + 1);
				} else {
					lodop.SET_SHOW_MODE('SETUP_ENABLESS', '00000000000000');
					lodop.SET_SHOW_MODE('SETUP_IN_BROWSE', 1);
					lodop.PRINT_SETUP();
				}
			} else {
				lodop.SET_SHOW_MODE('DESIGN_IN_BROWSE', 1);
				lodop.PRINT_DESIGN();
			}

		}

		return modeH;
	};

	//检验是否存在该打印机
	lodopObj.checkPrinter = function (printerName) {
		let ret = false;
		const printers = this.getPrinters();
		for (let i = 0; i < printers.length; i++) {
			if (printers[i] === printerName) {
				ret = true;
				break;
			}
		}
		return ret;
	};

	/*根据input对象返回打印任务
	 inputJson：intput对象
	 printDataItem：打印数据
	 templateSet：模板设置
	*/
	lodopObj.getPrintTaskByInput = function (inputJson, printDataItem, templateSet, isCompatible) {
		let dataObj = comp.Print.Data
			, that = new comp.Print.FN()
			, printTask = {}
			, dataType = '' //如果是店标的话是图片
			, val = ''
			, pStr = ''
			, isSet = true
			, citem
			, _dName
			, citemArr
			, _zmjStyle, _zjStrq = '', _zmStrq = ''
			, _exCode = templateSet.ModeList.Excode
			, _kddType = templateSet.ModeList.KddType
			, _styleId = templateSet.ModeList.StyleId //单个文本框中数据项数组
			, tempWidth = templateSet.ModeTempPrintcfg.Width * pxScale //模板宽度
			, tempHeight = templateSet.ModeTempPrintcfg.Height * pxScale; //模板高度

		printTask.content = inputJson.Str_q; //文本框
		printTask.itemKey = inputJson.InputID;//Key
		//兼容菜鸟
		if (inputJson.DataKey) {
			printTask.itemKey = inputJson.DataKey;
		}
		printTask.dataType = 'txt';
		printTask.itemY = ~~inputJson.Y_;//Y
		printTask.itemX = ~~inputJson.X_;//X
		printTask.itemW = ~~inputJson.W_;//W
		printTask.itemH = ~~inputJson.H_;//H
		printTask.originFontSize = inputJson.Fontsize;
		if (inputJson.Fontsize == -99) {
			printTask.fontSize = inputJson.Fontsize;
		} else {
			printTask.fontSize = inputJson.Fontsize == 0 ? dataObj.tempDefaultFontSize : that.getFontSize_lodop(inputJson.Fontsize);//字体大小
		}
		printTask.fontName = inputJson.Fontname;//字体
		printTask.readOnly = inputJson.Isedit == 1 ? 0 : 1;//纯文本内容在打印维护时，是否禁止修改
		printTask.bold = inputJson.Isb_n;//是否加粗
		// printTask.lineSpacing = that.getLineSpacing_lodop(printTask.fontSize, inputJson.Hjj);//行间距
		printTask.lineSpacing = +inputJson.Hjj; //行间距
		printTask.letterSpacing = +inputJson.Zjj;//字间距
		printTask.isEdit = 1;//是否允许控件被修改删除
		printTask.isMove = 1;//是否允许控件被移动
		printTask.direction = 0;//打印方向 默认横向
		printTask.underLine = inputJson.under;  //设定纯文本打印项是否下滑线。 数字型，1代表有下划线，0代表无下划线，缺省值是0。
		printTask.italic = inputJson.italic;       //设定纯文本打印项是否斜体。 数字型，1代表斜体，0代表非斜体，缺省值是0。
		printTask.alignment = inputJson.align ? +inputJson.align + 1 : ''; //设定纯文本打印项的内容左右靠齐方式。 1--左靠齐 2--居中 3--右靠齐，缺省值是1。
		printTask.angle = parseInt(inputJson.deg || 0);       //数字型，逆时针旋转角度数，单位是度，0度表示不旋转。
		printTask.bColor = inputJson.bColor;
		printTask.color = inputJson.color;
		printTask.encry = Number(inputJson.encry);
		printTask.alpha = inputJson.alpha || '1'; //0完全透明，1完全不透明； 缺省为 1
		printTask.isBlack = inputJson.style === 'BLACK_WHITE' && !['txm', 'ewm'].includes(inputJson.scripConvertCode)  //|| '1';
		//垂直对齐设置 //垂直横向文本框//透明度设置
		// 设置文本项是否展示，默认展示，溢出隐藏数据项
		printTask.isHidden = false;

		citemArr = inputJson?.proArray;
		_dName = ((citemArr || [])[0] || {}).Dataname;
		const fontSizeAutoDeal = function (ptask) {
			let fontSize = ptask.fontSize;
			if (fontSize == 1 && ptask.dataType == 'txt') {
				fontSize = comp.base.getAutoFontSize({
					width: ptask.itemW,
					fontName: ptask.fontName,
					isBold: ptask.bold,
					content: ptask.content,
				});
				fontSize = that.getFontSize_lodop(fontSize);
			}
			return fontSize;
		};
		if (dataObj.isSinglePrint && printDataItem.gatherData) {
			// if( !/^(ewm_number|ewm|ewm_str|txm_number|txm_tid|txm_jbm|table)$/.test(_dName) && printDataItem.gatherData[printTask.itemKey] !== undefined ){
			if (!that.isewm(_dName) && !that.istxm(_dName) && _dName !== 'table' && printDataItem.gatherData[printTask.itemKey] !== undefined) {
				printTask.content = printDataItem.gatherData[printTask.itemKey];
				printTask.fontSize = fontSizeAutoDeal(printTask);
				return printTask;
			} else if (_dName == 'table') {
				printTask.dataType = 'table';
				printTask.content = printDataItem.gatherData['fhdTableHtml'];
				printTask.oldH = printTask.itemH;
				// printTask.itemH = printDataItem.tableHeight;
				printTask.fontSize = fontSizeAutoDeal(printTask);
				return printTask;
			}
		}
		for (let j = 0; j < citemArr.length; j++) {
			if (j > 0) {
				printTask.content += ' ';
			}
			citem = citemArr[j];
			dataType = citem.Dataname;
			printTask.content += citem.Str_q; //数据项
			if (citem.ProValue) {
				pStr = that.getObjectFormProValue(citem.ProValue);
				printTask.direction = pStr.direction ? 1 : 0;
				// printTask.isBlack = pStr.isBlack ? 1 : 0;
				printTask.ShowBarText = pStr.isTxt ? 1 : 0;
			}
			if (dataType == 'logo' || dataType == 'pic' || dataType == 'bgm' || /^img/.test(dataType) || /image/.test(dataType) || /care_label/.test(dataType)) { //pic的数据源在
				printTask.dataType = 'img';
				printTask.content = '';
				printTask.stretch = 2; //图片缩放
				if (dataType == 'logo') {
					printTask.content = this.getValueByTypeName_lodop(printDataItem, dataType);
				} else if (/^img_v/.test(dataType) || dataType == 'img_yzd') { //圆准达
					printTask.content = printDataItem[dataType];
				} else if (/^img/.test(dataType) || dataType === 'pic' || dataType == 'bgm') {
					printTask.content = inputJson.Str_q.replace(/^(\/resources\/img\/print|\/img\/print|\/res\/eximg)/, '/resources/img/print').replace('print/Eximg', 'print/ExImg');
				} else if (/care_label/.test(dataType)) {
					let imgObj = {
						no_bleach_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/no_bleach_care_label.png',
						dry_flat_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/dry_flat_care_label.png',
						dry_clean_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/dry_clean_care_label.png',
						ironing_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/ironing_care_label.png',
						dry_washa_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/dry_washa_care_label.png',
						not_dry_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/not_dry_care_label.png',
						washable_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/washable_care_label.png',
						bleaching_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/bleaching_care_label.png',
						hand_wash_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/hand_wash_care_label.png',
						th_wash_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/thirty_water_wash_care_label.png',
						vertical_care_label: 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/resources/img/templateEdit/vertical_drying_care_label.png'
					}
					printTask.content = imgObj[dataType]
				} else if (/image/.test(dataType)) {
					printTask.content = printDataItem[dataType];
					printTask.isNeedWidth = true;

				}
				break;
			}
			//else if (dataType == "txm_tid" || dataType == "txm_number" || dataType == "txm_jbm" || dataType == "fhd_txm_number") { //条形码
			else if (that.istxm(dataType) || inputJson.scripConvertCode === 'txm') { //条形码
				printTask.dataType = 'barcode';
				printTask.content = '';
				printTask.txmType = inputJson.barCodeType ? inputJson.barCodeType : '128Auto';
				printTask.content += this.getValueByTypeName_lodop(printDataItem, dataType); //数据项内容
				if (dataType.includes('with_code')) {
					printTask.ShowBarText = 1
				}
				break;
			} else if (dataType == 'gx_jbm') { //集包码
				printTask.dataType = 'txt';
				printTask.content += this.getValueByTypeName_lodop(printDataItem, dataType); //数据项内容
				break;
			} else if (dataType == 'fjm') { //分拣码
				printTask.dataType = 'txt';
				printTask.content += this.getValueByTypeName_lodop(printDataItem, dataType); //数据项内容
				break;
			} else if (that.isewm(dataType) || inputJson.scripConvertCode === 'ewm') { //二维码
				printTask.dataType = 'img';
				printTask.content = '';
				printTask.isEwmImg = true
				printTask.isNeedWidth = true;

				if (/^ewm/.test(dataType)) {
					printTask.content += inputJson.Str_q;  //ewm ewm_str数据在里
				} else {
					printTask.content += this.getValueByTypeName_lodop(printDataItem, dataType); //数据项内容
				}

				// 25.06.26 上面的处理，没匹配到，兜底处理
				if(inputJson.scripConvertCode === 'ewm'){
					printTask.content += inputJson.Str_q;  //ewm ewm_str数据在里
				}
				// 
				// 生成二维码的时候，增加一个入参。QrcodeVersion 代表二维码的 QRCode 版本号，值越大，容纳的字符越多。目前取值 Auto、 3、5、7、10、14 (Auto就是 0)
				const QrcodeVersion = inputJson.barCodeType || 0;
				printTask.content = lodopObj.getQrcodeImg(printTask,QrcodeVersion);
				// if ( /^(fj_ewm_number|ewm_number|ewm_bigben_number|ewm_sfdz|bk_ewm|dp_code_ewm|dp_no_ewm)$/.test(dataType) ) {   //二维码运单号
				//     printTask.content += this.getValueByTypeName_lodop(printDataItem, dataType); //数据项内容
				// }else {
				//     printTask.content += inputJson.Str_q;  //ewm ewm_str数据在里
				// }
				break;
			} else if (/^line/.test(dataType)) {
				// else if ( dataType == "line1" || dataType == "line2" || dataType == "line3" || dataType == "line") { //线
				printTask.dataType = 'line';
				printTask.lineStyle = parseInt(inputJson.Str_q || printTask.lineStyle || 0);
				printTask.lineWidth = parseInt(inputJson.Str_h) || 1;
				break;
			} else if (dataType == 'rect') { //矩形
				printTask.dataType = 'rect';
				printTask.lineStyle = parseInt(inputJson.Str_q || printTask.lineStyle || 0);
				printTask.lineWidth = parseInt(inputJson.Str_h) || 1;
				break;
			} else if (dataType == 'f_ww') { //如果是发件旺旺的话
				printTask.dataType = 'txt';
				val = this.getValueByTypeName_lodop(printDataItem, dataType);
				if (citem.ProValue) {
					// pStr = citem.ProValue; //单个数据项组成部分数组  _show=1|_fx=1";
					if (pStr._fx == 1) { //显示
						if (pStr._show == 1) { //主旺旺部分
							val = val.substring(0, val.indexOf(':'));
						} else if (pStr._show == 2) { //子旺旺部分
							val = val.indexOf(':') > -1 ? val.substring(val.indexOf(':') + 1) : val;
						}
						printTask.content += val; //数据项内容
					}
				} else {
					printTask.content += val;
				}
				printTask.content += citem.Str_h; //数据项后文字
			} else if (dataType == 'f_date') { //如果是发件日期的话
				printTask.dataType = 'txt';
				const date = new Date();
				let isshowH = false;
				if (citem.ProValue) {
					// pStr = citem.ProValue; //单个数据项组成部分数组  _date=1|_time=1";
					if (pStr._date == 1) { //明天
						date.setDate(date.getDate() + 1);
					}
					if (pStr._time == 1) { //显示时分秒
						isshowH = true;
					}
				}
				val = date.format('yyyy-MM-dd');
				if (isshowH) {
					val = date.format('yyyy-MM-dd hh:mm:ss');
				}
				printTask.content += val; //数据项内容
				printTask.content += citem.Str_h; //数据项后文字
			} else if (dataType == 'gx_mdd' || dataType == 'mdd_dzmd') { //集包目的地or大头笔
				printTask.dataType = 'txt';
				if (!printDataItem[dataType]) {
					if ((dataType == 'gx_mdd' || dataType == 'mdd_dzmd') && (_kddType == 1 || _kddType == 3)) {     //五联单的大头笔 以及 所有面单集包地、无值用省市区拼接
						printTask.content += printDataItem['s_p'] + ' ' + printDataItem['s_city'] + ' ' + printDataItem['s_q'];
					}
				} else {
					printTask.content += this.getValueByTypeName_lodop(printDataItem, dataType);
				}
				printTask.content += citem.Str_h; //数据项后文字
			} else if (dataType == 's_phone' || dataType == 's_tel') { //单打不需要解密
				printTask.dataType = 'txt';
				printTask.content += this.getValueByTypeName_lodop(printDataItem, dataType, printTask.encry);
				printTask.content += citem.Str_h; //数据项后文字
			} else if (dataType == 'servicetype') {
				printTask.dataType = 'txt';
				printTask.content += printDataItem[dataType] || (templateSet.ModeCustomerTemplate || {}).Svctypename || '';

				printTask.content += citem.Str_h; //数据项后文字
				// printTask.isBlackWhite =  printTask.isBlack;
				// if( printTask.isBlack ){  //黑底白字的打印判断 走html打印
				//     printTask.dataType = "htm";
				//     printTask.content = this.createBlackGround(inputJson, printTask.content);
				// }
			} else if (dataType == 'servicename') {
				printTask.dataType = 'txt';
				printTask.content += (templateSet.ModeCustomerTemplate || {}).ServiceValue || '';
				printTask.content += citem.Str_h; //数据项后文字
			} else if (dataType == 'servicelist') {//服务选项
				dataObj.serviceListWidth = printTask.itemW;
				printTask.dataType = 'htm';
				printTask.itemKey = 'servicelist';
				if (templateSet.ModeList.Excode == 'SF') {
					//顺丰斑马打印机打印HTM会有问题 因此特殊处理
					printTask.dataType = 'txt';
				}
				if (printTask.itemH < 15) {        //防止多页问题
					printTask.itemH = 15;
				}
				printTask.content = this.createServceListHTML(templateSet.ModeServiceItems, printDataItem, false, templateSet.ModeList.Excode, printTask.itemW);
			} else if (dataType == 'dshk_black') {
				printTask.dataType = 'htm';
				printTask.content += ((printDataItem[dataType] || '') + inputJson.Str_h);
				printTask.isBlack = 1;
				// printTask.bColor = "#000000";
				// printTask.color = "#ffffff";
				// printTask.content = this.createBlackGround(inputJson, printTask.content);
			} else if (dataType.indexOf('table') > -1 || dataType.indexOf('hj') > -1 || dataType.indexOf('tb') > -1) {//表格属性
				if (dataType == 'table') {
					printTask.dataType = 'table';
					printTask.content = printDataItem.tableHtml;
					printTask.oldH = printTask.itemH;
					// if (!dataObj.isLinkedItem) {
					printTask.itemH = printDataItem.tableHeight;
					// }
				} else {
					return null;
				}
			} else {
				printTask.dataType = 'txt';
				val = this.getValueByTypeName_lodop(printDataItem, dataType, printTask.encry);
				if (citem.ProValue) {
					// pStr = citem.ProValue;
					// if (pStr.indexOf("nu=0") >= 0 || pStr.indexOf("nu＝0") >= 0) { //nu=0表示无内容时不显示内容框 默认为显示  只有contentType=="mjs"|"s_message"|"f_memo"|"o_info"时才有可能有这个值
					if (pStr._nu === '0' || pStr.nu === '0') {
						if ($.trim(val) == '') {
							printTask.content = '';
							printTask.oldH = printTask.itemH;
							printTask.itemH = 0;
							isSet = false;
						}
					}
				}

				//子母件处理-- 第一张母件处理
				if (printDataItem.zmj_sum && !printDataItem.zmj_mnum && (dataType === 'zmj_mnum' || dataType === 'fhdexnumber')) {
					if (_exCode === 'SF') {  //【顺丰】【子单号纸张打印母单号 ：子单号：123123 母单号：12323123】
						_zmjStyle = 1;
						_zmStrq = '母单号：';
					} else if ((_exCode === 'CN7000001003751' && _styleId != 16 && _styleId != 14)) {      //【速尔；跨越新三联，二联】【子单号纸张打印母单号 ：子件：123123 母单号：12323123】
						_zmjStyle = 1;
						_zmStrq = '';
					} else if (_exCode === 'SURE') {   //母单号 子单号都打印母单号
						_zmjStyle = 2;
						val = printDataItem['fhdexnumber'];
					}
					if (_zmjStyle === 1) {
						if (dataType == 'zmj_mnum') {   //子单号也不打印出来
							printTask.content = '';
							printTask.oldH = printTask.itemH;
							printTask.itemH = 0;
							isSet = false;
						} else if (dataType == 'fhdexnumber') {  //母单号处理  ----- 只有母单号打印的纸张
							printTask.content = _zmStrq;
						}
					}
				}

				if (isSet) {
					printTask.content += val; //数据项内容
					printTask.content += citem.Str_h; //数据项后文字
				}
			}
			//content.push(citem.Str_h);//数据项后文字
		} //单个文本框中数据项数组循环结束

		if (printTask.dataType == 'txt') {
			if (printTask.content != '' || isSet) {
				printTask.content += inputJson.Str_h; //文本框后文字
			}
			if ((isCompatible || dataType == 'f_info') && printTask.content.length > 0) {
				if (dataType == 'f_memo' || dataType == 's_message' || dataType == 'o_info' || dataType == 'f_info') {
					//重新计算文本框行高以及换行要求
					const nowCs = that.getInputHeight_lodop(printTask.itemW, printTask.itemH, inputJson.Zjj, inputJson.Hjj, printTask.fontSize, inputJson.Isb_n, printTask.content, true, true);
					printTask.oldH = printTask.itemH;
					printTask.itemH = nowCs.nowH;
					printTask.content = nowCs.content;
					//快递单发件详情高度不能超过纸张
					if (!isCompatible && dataType == 'f_info') {
						const ph = parseInt(dataObj.pageHMmLodop * dataObj.mmToPxUnit);
						if (printTask.itemY + printTask.itemH > ph) {
							printTask.itemH = ph - (printTask.itemY + 5);
						}
					}
				}
			}
		}
		if (_exCode !== 'FHD' && templateSet.ModeTempPrintcfg?.contentOverflow === 'HIDE') {
			//分场景控制
			// 数据框全部超出范围，直接隐藏
			// 数据框部分出界，自适应到模板范围内

			// 1、如果x方向或者y方向全部超出边界，直接隐藏
			// 2、x方向与y方向超出部分，缩小数据框
			if (printTask.itemY > tempHeight || printTask.itemX > tempWidth) {
				printTask.isHidden = true
			} else if (printTask.itemY + printTask.itemH > tempHeight) {
				let _h = printTask.itemY + printTask.itemH - tempHeight
				printTask.itemH = printTask.itemH - (_h + 5)
			} else if (printTask.itemX + printTask.itemW > tempWidth) {
				let _W = printTask.itemX + printTask.itemW - tempWidth
				printTask.itemW = printTask.itemW - (_W + 5)
			}

		}
		printTask.content = (printTask.content || '').toString().replace('＆', '&').replace('＝', '=');//内容
		printTask.fontSize = fontSizeAutoDeal(printTask);
		return printTask;
	};


	//生成黑底白字的超文本打印项
	// lodopObj.createBlackGround = function(inputJson,value){
	//     let globelSet = comp.Print.Data.globalSettingKdd.ModeSet||{},
	//     _fontSize = inputJson.Fontsize || comp.Print.Data.tempDefaultFontSize;
	//     let html = '<div style="background-color:#000000;color:#fff;'
	//                 +'width:'+ inputJson.W_ +'px;height:'+ inputJson.H_ +'px;'
	//                 +'font-size:'+ _fontSize +'px;font-family:'+ (inputJson.Fontname||globelSet.Fontname) +';'
	//                 +'font-weight:'+ (inputJson.Isb_n == 1?'bold':'nomarl') +';line-height:'+ (~~_fontSize +inputJson.Hjj) +'px;'
	//                 +'font-style:'+ (inputJson.italic == 1?'italic;':'normal;')
	//                 +'text-decoration：'+
	//                 +'letter-spacing:'+(~~(inputJson.Zjj ||0) )+'px;">'+ value +'</div>';
	//     return html;
	// };

	//无法用纯文本实现的功能用超文本打印项
	lodopObj.createPrintHtml = function (printTask) {
		const globelSet = comp.Print.Data.globalSettingKdd.ModeSet || {},
			_addCss = ['left', 'center', 'right'],
			_fontSize = +printTask.originFontSize || globelSet.Fontsize || comp.Print.Data.tempDefaultFontSize;
		// 无内容时不展示黑底
		if (!printTask?.content) return ''
		const html = '<div style="overflow:hidden;background-color:#000000;color:#fff;'
			// +'background-color:'+ (printTask.bColor||"transparent") + ";"
			// + (printTask.color? 'color:'+printTask.color+";": "")
			+ 'width:100%;height:100%;'
			+ 'font-size:' + _fontSize + 'px;font-family:' + (printTask.fontName || globelSet.Fontname) + ';'
			+ 'font-weight:' + (printTask.bold == 1 ? 'bold' : 'nomarl') + ';'
			+ 'font-style:' + (printTask.italic == 1 ? 'italic;' : 'normal;')
			+ 'text-decoration:' + (printTask.underLine == 1 ? 'underLine;' : 'none;')
			+ 'text-align:' + (_addCss[(printTask.alignment || 1) - 1]) + ';'
			+ (printTask.lineSpacing != -1 ? 'line-height:' + (+_fontSize + printTask.lineSpacing) + 'px;' : '')
			+ 'transform:rotate(' + (printTask.angle || 0) + ')deg;'
			+ (printTask.letterSpacing != -1 ? 'letter-spacing:' + (+printTask.letterSpacing || 0) + 'px;' : '') + '">' + printTask.content + '</div>';
		return html;
	};
	lodopObj.createPrintLineHtml = function (printTask) {
		const html = '<div style="'
			+ 'border-style:' + (printTask.lineStyle == 1 ? 'dashed;' : 'dotted;')
			+ 'border-width:' + printTask.lineWidth + 'px 0px 0px;'
			+ 'width:' + (printTask.itemW || 0) + 'px;'
			+ 'height:' + (printTask.itemH || 0) + 'px;'
			+ '"></div>';
		return html;
	};

	/*************************
	*创建服务选项的html
	*serviceItems  服务选项实体
	*printData 打印数据实体
	*isCainiao 是否是菜鸟官方模板
	************************/
	lodopObj.createServceListHTML = function (serviceItems, printData, isCainiao, exCode, rootWidth) {
		const dataObj = comp.Print.Data;
		let content = '';
		const that = this;
		if (exCode == 'SF') {
			$.each(serviceItems, function (j, item) {
				if (item.Issetval == 1) {
					if (item.Itemname == '保价金额' && item.Reserve1 && item.Reserve1 != '保价金额') {//保价金额特殊处理
						content += item.Itemname + ':' + item.Reserve1 + item.Unit;
					} else {
						content += item.Itemname + ':' + that.getValueByTypeName_lodop(printData, item.Dataname) + item.Unit;
					}
				} else {
					content += item.Itemname + ':' + item.Defaultval;
				}
				content += ((j + 1) % 2 == 0 ? '\r\n' : '    ');
			});
		} else if (serviceItems != null) {
			if (isCainiao) {
				$.each(serviceItems, function (j, item) {
					if (item.Issetval == 1) {
						content += item.Itemcode + '=' + that.getValueByTypeName_lodop(printData, item.Dataname) + item.Unit + ';';
					} else {
						content += item.Itemcode + '=' + item.Defaultval + ';';
					}
				});
			} else {
				content = '<div style=\'font-size:10px\' ' + (dataObj.globalSettingKdd.ModeSet.Fontname != '' ? 'font-family:' + dataObj.globalSettingKdd.ModeSet.Fontname + ';' : '') + 'font-weight: bold;\'><ul style=\'list-style:none;margin:0;padding:0;width:' + rootWidth + 'px;\'>';
				$.each(serviceItems, function (j, item) {
					content += '<li style="width:110px;float:left;margin:0;padding：0">';
					if (item.Issetval == 1) {
						if (item.Itemname == '保价金额' && item.Reserve1 && item.Reserve1 != '保价金额') {//保价金额特殊处理
							content += item.Itemname + ':' + item.Reserve1 + item.Unit;
						} else {
							content += item.Itemname + ':' + that.getValueByTypeName_lodop(printData, item.Dataname) + item.Unit;
						}
					} else {
						content += item.Itemname + ':' + item.Defaultval;
					}
					content += '</li>';
				});
				content += '</ul></div>';
			}

		}
		return content;
	};

	/*************************
	*通过字段类型名从用户传过来的打印数据中获取字段值
	*dataJson  M_printdata实例对象
	*typename 属性名
	************************/
	lodopObj.getValueByTypeName_lodop = function (data, dataName, needEncry) {
		if (data[dataName]) {
			let value = data[dataName];
			if (needEncry && comp.Print.Data.encryList.indexOf(dataName) > -1) {
				value = this.encryText(value);
			}
			return value;
		} else {
			if (dataName.indexOf('tb_hh1') > -1) {
				return '(nrt)';
			} else if (dataName == 'tb_xh') {
				return '0';
			}
			return '';
		}
	};

	lodopObj.getSinCosVal = function (width, height, deg, type) {
		const _deg = deg * (Math.PI * 2) / 360,
			_sin = Math.sin(_deg),
			_cos = Math.cos(_deg),
			_w = width / 2,
			_h = height / 2;
		if (type == 'txt') {
			return {
				_y: -(_sin * _w + _cos * _h - _h),
				_x: _w - _cos * _w + _sin * _h,
			};
		} else if (type == 'barcode') {
			return {
				_y: -(_sin * _w - _cos * _h - _h),
				_x: _w - _cos * _w - _sin * _h,
			};
		}
	};

	/*打印当前任务数组中的任务
	lodop:控件对象
	printTask:打印任务对象
	startTop:起始高度
	*/
	lodopObj.drawItem = function (lodop, printTask, startTop, showType) {
		const dataObj = comp.Print.Data,
			ph = ~~(dataObj.pageHMmLodop * dataObj.mmToPxUnit);
		printTask.itemY += startTop;

		// 数据框超区隐藏
		if (printTask.isHidden) {
			// 1:仅预览不打印、2不预览不打印
			// lodop.SET_PRINT_STYLEA(0,"PreviewOnly",2)
			return
		}
		// 黑底白字--- 加粗线条样式打印转化为超文本实现。
		if (printTask.bColor && printTask.color || printTask.isBlack) {
			printTask.dataType = 'htm';
			printTask.content = this.createPrintHtml(printTask);
		} else if (printTask.dataType == 'line' && printTask.lineWidth >= 2 && printTask.lineStyle != 0 && printTask.itemW > printTask.itemH) {
			printTask.dataType = 'htm';
			printTask.content = this.createPrintLineHtml(printTask);
		}
		//图片
		if (printTask.dataType == 'img' && printTask.content) {   //URL不存在的图片就不打印出来了
			if (printTask.itemW === 0 && printTask.itemH === 0) {
				printTask.itemW = 80;
				printTask.itemH = 80;
			}
			let imgHtml = ''

			if (printTask.isEwmImg) {
				imgHtml = `<img style="width:100%;heigt:100%;" src="${printTask.content}"/>`
				lodop.ADD_PRINT_HTM(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, imgHtml);

			} else {
				imgHtml = `<img style="${printTask.isNeedWidth ? `width:${printTask.itemW}px;` : ''}" src="${printTask.content}"/>`
				lodop.ADD_PRINT_IMAGE(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, imgHtml);
			}
			lodop.SET_PRINT_STYLEA(0, 'Stretch', printTask.stretch); //图片缩放
			lodop.SET_PRINT_STYLEA(0, 'ItemName', printTask.itemKey); //图片Key
		}
		//文本
		else if (printTask.dataType == 'txt') {
			if (printTask.itemW === 0 && printTask.itemH === 0) {
				printTask.itemW = 100;
				printTask.itemH = 21;
			}
			if (printTask.angle != 0) {
				const _xy = this.getSinCosVal(printTask.itemW, printTask.itemH, printTask.angle, 'txt');
				printTask.itemX += _xy._x;
				printTask.itemY += _xy._y;
			}
			lodop.ADD_PRINT_TEXTA(printTask.itemKey, printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, `${printTask.content}`);
			if (printTask.fontName != '') {
				lodop.SET_PRINT_STYLEA(0, 'FontName', printTask.fontName); //设定纯文本打印项的字体名称
			}
			if (printTask.fontSize != 0) {
				lodop.SET_PRINT_STYLEA(0, 'FontSize', printTask.fontSize); //设定纯文本打印项的字体大小
				lodop.SET_PRINT_STYLE("AutoFontSize", true)
			}
			if (printTask.bold != -1) {
				lodop.SET_PRINT_STYLEA(0, 'Bold', printTask.bold); //设定纯文本打印项是否粗体
			}
			if (printTask.alignment) {
				lodop.SET_PRINT_STYLEA(0, 'Alignment', printTask.alignment); //对齐方式 Alignment的值：数字型，1--左靠齐 2--居中 3--右靠齐，缺省值是1。
			}
			if (printTask.lineSpacing != 0 && printTask.lineSpacing != -1) {
				lodop.SET_PRINT_STYLEA(0, 'LineSpacing', printTask.lineSpacing); //纯文本的行间距
			}
			if (printTask.letterSpacing != 0 && printTask.letterSpacing != -1) {
				lodop.SET_PRINT_STYLEA(0, 'LetterSpacing', printTask.letterSpacing); //纯文本的字间距 `
			}
			if (printTask.readOnly) {
				lodop.SET_PRINT_STYLEA(0, 'ReadOnly', 0); //纯文本内容在打印维护时，是否禁止修改
			}

			if (printTask.angle) {
				lodop.SET_PRINT_STYLEA(0, 'Angle', 360 - printTask.angle);   //数字型，逆时针旋转角度数，单位是度，0度表示不旋转。
			}

			printTask.underLine && lodop.SET_PRINT_STYLEA(0, 'Underline', 1); //数字型，1代表有下划线，0代表无下划线，缺省值是0。
			printTask.italic && lodop.SET_PRINT_STYLEA(0, 'Italic', 1); //数字型，1代表斜体，0代表非斜体，缺省值是0

			// 透明度
			if (printTask.alpha) {
				const LODOP_ALPHA_RATIO = 255;//lodop 透明范围值0--255(默认值是255=不透明);
				const lodop_alpha = LODOP_ALPHA_RATIO * printTask.alpha;
				lodop.SET_PRINT_STYLEA(0, 'Alpha', lodop_alpha || LODOP_ALPHA_RATIO); //避免出现 NaN
			}

		}
		//线
		else if (printTask.dataType == 'line') {
			if (printTask.itemW === 0 && printTask.itemH === 0) {
				printTask.itemW = 100;
				printTask.itemH = 1;
			}
			let tempbY = printTask.itemY; //线Y
			let tempLH = printTask.itemH; //线高
			let tempZ = 1; //第几张纸
			if (tempLH > 1 && ph > 0 && showType !== 'print') { //竖线
				tempZ = Math.ceil(tempbY / ph);
				while (tempLH > 0) {
					const tempZH = ph * tempZ; //总体高度
					const lsH = tempZH - tempbY >= tempLH ? tempLH : tempZH - tempbY;
					lodop.ADD_PRINT_LINE(tempbY, printTask.itemX, tempbY + lsH, (printTask.itemX + (printTask.itemW == 0 ? 1 : printTask.itemW)), printTask.lineStyle, printTask.lineWidth || 0);
					tempbY += (lsH + 15);
					tempLH -= (lsH + 15);
					tempZ++;
				}
			} else {
				//lodop.ADD_PRINT_LINE(tempbY, printTask.itemX, (printTask.itemY + (printTask.itemH == 0 ? 1 : printTask.itemH)), (printTask.itemX + (printTask.itemW == 0 ? 1 : printTask.itemW)), printTask.lineStyle , printTask.lineWidth||0);
				lodop.ADD_PRINT_LINE(tempbY, printTask.itemX, (printTask.itemY + (printTask.itemH == 0 ? 1 : printTask.itemH)), (printTask.itemX + (printTask.itemW == 0 ? 1 : printTask.itemW)), printTask.lineStyle, printTask.lineWidth);
			}
			lodop.SET_PRINT_STYLEA(0, 'ItemName', printTask.itemKey);
		}
		//矩形
		else if (printTask.dataType == 'rect') {
			if (printTask.bColor) {
				lodop.ADD_PRINT_SHAPE(4, printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, printTask.lineStyle, printTask.lineWidth || 0);
			} else {
				lodop.ADD_PRINT_RECT(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, printTask.lineStyle, printTask.lineWidth || 0);
			}
			lodop.SET_PRINT_STYLEA(0, 'ItemName', printTask.itemKey);
		}
		//二维码/条形码
		else if (printTask.dataType == 'barcode' && printTask.content) { //条形码或者二维码
			if (printTask.itemW === 0 && printTask.itemH === 0) {
				printTask.itemW = 80;
				printTask.itemH = 80;
			}
			if (printTask.angle == 90) {
				const _xy = this.getSinCosVal(printTask.itemW, printTask.itemH, printTask.angle, 'barcode');
				printTask.itemX += _xy._x;
				printTask.itemY += _xy._y;

				const _w = printTask.itemW;
				printTask.itemW = printTask.itemH;
				printTask.itemH = _w;
			}
			// clodop生成二维码，若内容为空，会打印出错误信息，所以在clodop下，空字符串追加打印空字符
			// lodop.ADD_PRINT_BARCODE(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, printTask.txmType,
			//    (printTask.txmType == 'QRCode' && lodop.VERSION && lodop.CVERSION ) ? (printTask.content || " ") : printTask.content);
			lodop.ADD_PRINT_BARCODE(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, printTask.txmType, printTask.content);
			// 更改二维码内容字符集
			if (printTask.txmType === 'QRCode') lodop.SET_PRINT_STYLEA(0, "DataCharset", "UTF-8");
			// lodop.SET_PRINT_STYLEA(0, 'ShowBarText', printTask.ShowBarText ? 1 : 0);
			lodop.SET_PRINT_STYLEA(0, 'ShowBarText', (printTask.ShowBarText || 0));
			lodop.SET_PRINT_STYLEA(0, "AlignJustify", 2);
			lodop.SET_PRINT_STYLEA(0, "FontSize", 8)
			lodop.SET_PRINT_STYLEA(0, 'ItemName', printTask.itemKey);
			if (printTask.angle == 90) {
				// lodop.SET_PRINT_STYLEA(0, "Angle", 90);
				lodop.SET_PRINT_STYLEA(0, 'Angle', 360 - printTask.angle);   //数字型，逆时针旋转角度数，单位是度，0度表示不旋转。
			}
		}
		//htm
		else if (printTask.dataType == 'htm') { //  HTM
			if (printTask.itemH < 10) {
				printTask.itemH = 10;
			}
			lodop.ADD_PRINT_HTM(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, printTask.content);
			lodop.SET_PRINT_STYLEA(0, 'ItemName', printTask.itemKey);
		}
		//table
		else if (printTask.dataType == 'table') { //  表格
			lodop.ADD_PRINT_HTM(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH + 50, printTask.content);
			lodop.SET_PRINT_STYLEA(0, 'ItemName', printTask.itemKey);
			lodop.SET_PRINT_STYLEA(0, 'Offset2Top', (0 - printTask.itemY));
			// lodop.SET_PRINT_STYLEA(0,'HtmWaitMilSecs',1000)
		}

		if (printTask.isMove == 0) {
			lodop.SET_PRINT_STYLEA(0, 'HOrient', 3);
			lodop.SET_PRINT_STYLEA(0, 'VOrient', 3);
		}
		if (printTask.direction == 1 && !printTask.angle) {
			lodop.SET_PRINT_STYLEA(0, 'Angle', 90);
		}
		// lodop.SET_PRINT_STYLEA(0, "ReadOnly", 0);
		// if (dataObj.isLinkedItem && dataObj.linkedIndex > 0) {
		//     lodop.SET_PRINT_STYLEA(0, "LinkedItem", dataObj.linkedIndex)
		// }
	};

	//计算打印的数据需要分成几个批次打印
	lodopObj.getPrintModelsPLByCount = function (prints, pageCount) {
		const plPrintdatas = [];
		if (prints.length > 0) {
			if (prints.length <= pageCount) {
				plPrintdatas.push(prints);
			} else {
				for (let i = 0; i < prints.length; i++) {
					if (i < pageCount) {
						if ((typeof (plPrintdatas[0]) === 'undefined')) {
							plPrintdatas[0] = Array();
						}
						plPrintdatas[0].push(prints[i]);
					} else {
						const a = Math.floor(i / pageCount);
						if ((typeof (plPrintdatas[a]) === 'undefined')) {
							plPrintdatas[a] = Array();
						}
						plPrintdatas[a].push(prints[i]);
					}
				}
			}
		}
		return plPrintdatas;
	};

	lodopObj.sortInputArr_lodop = function (inputArr) {
		for (let i = 0; i < inputArr.length - 1; i++) {
			for (let j = i + 1; j < inputArr.length; j++) {
				if (inputArr[i].Y_ > inputArr[j].Y_) {
					const inputTemp = inputArr[i];
					inputArr[i] = inputArr[j];
					inputArr[j] = inputTemp;
				}
			}
		}
		return inputArr;
	};

	//菜鸟批打分组
	lodopObj.getPrintModelsPL = function (prints, pageCount, flag) {
		const plPrintdatas = [];
		if (flag) {
			plPrintdatas.push(prints);
		} else {
			if (prints.length > 0) {
				if (prints.length <= pageCount) {
					plPrintdatas.push(prints);
				} else {
					for (let i = 0; i < prints.length; i++) {
						if (i < pageCount) {
							if ((typeof (plPrintdatas[0]) === 'undefined'))
								plPrintdatas[0] = Array();
							plPrintdatas[0].push(prints[i]);
						} else {
							const a = Math.floor(i / pageCount); //+1;
							if ((typeof (plPrintdatas[a]) === 'undefined'))
								plPrintdatas[a] = Array();
							plPrintdatas[a].push(prints[i]);
						}
					}
				}
			}
		}
		return plPrintdatas;
	};


	//打印数据
	lodopObj.printData = function (lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc, printOkFunc, printType, drawAgin, templateSet, printAction, arg) {
		let dataObj = comp.Print.Data
			, modeListShow, lodopId = '';

		if (selectedPrinter !== '' && this.checkPrinter(selectedPrinter)) {
			// printBoxIsShow == 1 && lodop.SET_PRINT_MODE("WINDOW_DEFPRINTER", selectedPrinter);
			lodop.SET_PRINTER_INDEX(selectedPrinter || -1);
		}
		if (hasView) {
			lodop.SET_PRINT_MODE('RESELECT_ORIENT', true);
			lodop.SET_PRINT_MODE('RESELECT_PAGESIZE', true);
			lodop.SET_PRINT_MODE('RESELECT_COPIES', true);
			lodop.SET_PRINT_MODE('RESELECT_PRINTER', true);
			if (lodop.CVERSION && lodop.blOneByone == true) {
				console.log('正在打印中，不要多次点击');
				return;
			}

			if (printType === 'fhd' && comp.Print.Data.batchPreviewFhd) { // 只有批打行为使用这个预览模式
				comp.Print.Data.batchPreviewFhd = false;
				lodop.SET_SHOW_MODE('HIDE_PBUTTIN_PREVIEW', true); //预览窗口-隐藏打印按钮
				lodop.SET_SHOW_MODE('PREVIEW_NO_MINIMIZE', true); //预览窗口-禁止最小化

				let pageWidth = 0, pageHeight = 0;
				try {
					// 30 和 130 是抵消预览窗口本身占据的宽度和高度
					pageWidth = lodop.PageData.width.slice(0, -2) * pxScale + 30;
					pageHeight = lodop.PageData.height.slice(0, -2) * pxScale + 130;
					if (pageHeight >= (screen.height - 30)) { // 留出 30 使预览框的关闭按钮能展示出来
						pageHeight = screen.height - 30;
					}
				} catch (e) {

					console.log('发货单预览处理预览窗口宽高时出现异常', e);
				}

				isNaN(pageWidth) && (pageWidth = 0);
				isNaN(pageHeight) && (pageHeight = 0);
				lodop.SET_PREVIEW_WINDOW(1, 0, 0, pageWidth, pageHeight, '预览查看.打印前预览');
			}
			lodop.PREVIEW();
		} else {
			dataObj.printCopies = arg?.printNum || ~~dataObj.printCopies;
			if (dataObj.printCopies == 0) {
				dataObj.printCopies = 1;
			}
			modeListShow = (templateSet || dataObj.templateSet).ModeListShow;
			// if (dataObj.printCopies > 1) {
			//     new comp.Print.FN().addLog(modeListShow.Mode_ListShowId, dataObj.printCopies, "printCopies", "");
			// }
			// 吊牌条码单不使用lodop自带的打印份数设置，改为复制打印数据手动多份
			if (!['dpd', 'tmd', 'wdpd', 'wtmd'].includes(modeListShow?.Modeid)) lodop.SET_PRINT_COPIES(dataObj.printCopies);
			if (printBoxIsShow == 1) { //打印时是否显示系统打印机选择框判断
				if (dataObj.isFirstPrint) {
					function choosePrinter(cPrinter, printCopies) {
						cPrinter = cPrinter || lodop.GET_VALUE('PRINTSETUP_PRINTER_NAME', 0);
						if (comp.Print.Data.isSinglePrint && printType) {
							comp.Print.printerProve(1, cPrinter, function () {  //打印机认证 定制打印机功能
								//去掉表格绘制的input 和 datakey 打印。 或者在这里重新绘制 然后打印
								drawAgin && drawAgin();
								const eventObj = comp.Print.eventObj;
								if (eventObj && eventObj.beforePrintMark) {
									const list = eventObj.beforePrintMark;
									list[0] && list[0]({
										printer: cPrinter,
										templateInfo: modeListShow,
										printCopies: printCopies,
									}, function () {
										lodop.SET_SHOW_MODE('NP_NO_RESULT', false);
										lodopId = lodop.PRINT();
										dataObj.isFirstPrint = false;
										if (typeof printOkFunc === 'function') {
											printOkFunc(); //回调
											dataObj.isFirstPrint = true;
										}
										return;
									});
								}
							});
						} else {
							lodop.SET_SHOW_MODE('NP_NO_RESULT', false);
							lodopId = lodop.PRINT();
							dataObj.isFirstPrint = false;
							if (typeof printOkFunc === 'function') {
								printOkFunc(); //回调
								dataObj.isFirstPrint = true;
							}
						}
					}
					function noChoosePrint() {
						typeof clearFunc === 'function' && clearFunc();
						return false;
					}
					//区分clodop与lodop
					if (lodop.VERSION && lodop.CVERSION) {
						lodop.KDZS_SELECT_PRINTER(selectedPrinter, function (arg) {
							comp.Print.setPrintCopies(arg.printNums);
							choosePrinter(arg.cPrinter, arg.printNums);
						}, function () {
							noChoosePrint();
						});
					} else {
						if (lodop.SELECT_PRINTER() >= 0) {
							choosePrinter();
						} else {
							noChoosePrint();
						}
					}

				} else {
					lodop.SET_SHOW_MODE('NP_NO_RESULT', false);
					lodopId = lodop.PRINT();
					typeof printOkFunc === 'function' && printOkFunc(); //回调
				}
			} else {
				lodop.SET_SHOW_MODE('NP_NO_RESULT', false);
				lodopId = lodop.PRINT();
				typeof printOkFunc === 'function' && printOkFunc(); //回调
			}
		}
		return lodopId;
	};

	lodopObj.computeCountNewLines = function (str) {
		var regex = /\n/g;
		var matches = str.match(regex);
		return matches ? matches.length : 0;
	}
	//计算文本框的应有的高度   针对发货单
	lodopObj.getInputHeight_lodop = function (width, height, letterspace, lineh, fontsize, isbn, str, isDefault, isjx, fhdFhdishb) {
		const that = new comp.Print.FN();
		const horCList = {};
		horCList.nowH = height;
		horCList.content = str;
		fontsize = that.getFontSizeByFlashSize_lodop(fontsize);//转换到打印出来实际的像素
		if (height > 0) {
			letterspace = letterspace == -1 ? 0 : letterspace;
			lineh = Math.round(fontsize * 0.25) + (lineh == -1 ? 1 : lineh); //getLineSpacing_lodop(fontsize,lineh);//实际的行间距
			if (isbn == -1) {
				const fhdIsb = fhdFhdishb;
				if (fhdIsb == true || fhdIsb == '1' || fhdIsb == 1) {
					isbn = true;
				} else {
					isbn = false;
				}
			}

			let nowWidth = 0;
			const pheight = fontsize + lineh;
			if (isjx) {
				for (let i = 0; i < horCList.content.length; i++) {
					const strCode = horCList.content.charCodeAt(i);
					let fz = 0;
					if ((strCode > 255)) {
						fz = fontsize;
					} else {
						fz = Math.round(fontsize / 2);
					}
					nowWidth = nowWidth + fz + letterspace;
				}
				const rowCount = Math.ceil(nowWidth / width);
				const hs = this.computeCountNewLines(horCList.content); //判断多少个换行符
				// const hs = (horCList.content.toString().split('(nrt)')).length - 1; //判断多少个换行符
				if (hs > 0) {
					horCList.content = horCList.content.toString().replace(/\(nrt\)/g, '\r\n');
				}
				horCList.nowH = pheight * (rowCount + hs);
			} else {
				if (horCList.content != '') {
					let cenindex = 0;
					const rowcount = Math.floor(horCList.nowH / pheight) == 0 ? 1 : Math.floor(horCList.nowH / pheight); //当前高度能打几行
					const gdw = width * rowcount; //当前打印总像素
					for (let i = 0; i < horCList.content.length; i++) {
						const strCode = horCList.content.charCodeAt(i);
						let fz = 0;
						if ((strCode > 255)) {
							fz = fontsize;
						} else {
							fz = Math.round(fontsize / 2);
						}
						nowWidth = nowWidth + fz + letterspace + 1;
						if (gdw - 5 > nowWidth) { //如果当前像素小于总像素 累加
							cenindex = i + 1;
						} else break;
					}
					if (cenindex < horCList.content.length) {
						horCList.content = horCList.content.substring(0, cenindex);
					}
				}
			}

			if (isDefault != false) {
				if (horCList.nowH < height) {
					horCList.nowH = height;
				}
			}
		}
		return horCList;
	};

	//重写打印机弹框样式 clodop
	lodopObj.resetPrinterBox_clodop = function () {

		CLODOP.KDZS_SELECT_PRINTER = function (defaultPrint, fnChoose, fnNoChoose) {
			this.SelectBox.kdzs_create(388, 240, true, 'kdzs', defaultPrint, fnChoose, fnNoChoose);
			return true;
		};
		CLODOP.KDZS_Create_Printer_List = function (oElement, blNoDetailForAO, defaultPrint) {
			while (oElement.childNodes.length > 0) {
				const children = oElement.childNodes;
				for (let i = 0; i < children.length; i++)
					oElement.removeChild(children[i]);
			}
			const iCount = this.GET_PRINTER_COUNT();
			let iIndex = 0;
			// if(!this.PageData.window_defprinter){
			//     let fn = new comp.Print.FN();
			//     defaultPrint = fn.lodopGetDefaultPrinter();
			// }else{
			//     defaultPrint = this.GET_PRINTER_NAME(this.PageData.printerindex);
			// }
			for (let i = 0; i < iCount; i++) {
				let option = document.createElement('option');
				option.value = option.innerHTML = this.GET_PRINTER_NAME(i);
				// option.value=iIndex;
				if (defaultPrint == this.GET_PRINTER_NAME(i)) {
					option.selected = true;
				}
				oElement.appendChild(option); iIndex++;
				if (this.VERSION_EXT) {
					if (this.Printers['list'][i]['subdevlist'].length > 0) {
						if (blNoDetailForAO) option.innerHTML = option.innerHTML + ',..'; else {
							const strDrivename = option.innerHTML;
							for (let j = 0; j < this.Printers['list'][i]['subdevlist'].length; j++) {
								const strDevID = this.Printers['list'][i]['subdevlist'][j].id;
								const strDevName = this.Printers['list'][i]['subdevlist'][j].name;
								if (j == 0) {
									option.innerHTML = strDrivename + ',' + strDevName;
									option.value = strDrivename + ',' + strDevID + ';' + strDevName;
								} else {
									option = document.createElement('option');
									option.innerHTML = strDrivename + ',' + strDevName;
									option.value = strDrivename + ',' + strDevID + ';' + strDevName;
									oElement.appendChild(option); iIndex++;
									if (iIndex >= 100) return;
								}
							}
						}
					}
				}
			}

		};
		CLODOP.SelectBox.kdzs_create = function (iW, iH, onlySelect, isSelf, defaultPrint, fnChoose, fnNoChoose) {
			if (CLODOP.SelectBox.PopDiv) this.closeit();
			const obody = document.body || document.getElementsByTagName('body')[0] || document.documentElement;
			const Boxdiv = document.createElement('div');
			obody.appendChild(Boxdiv);
			Boxdiv.style.cssText = 'position:fixed;z-index:91100;display:block;top:50%;left:50%;border:1px solid #6B97C1;background:#F5F5F5;color:#000;font-size:13px;';
			Boxdiv.style.width = iW + 'px';
			Boxdiv.style.marginLeft = '-' + (iW) / 2 + 'px';
			Boxdiv.style.marginTop = '-' + (iH) / 2 + 'px';
			Boxdiv.style.height = iH + 'px';
			this.PopDiv = Boxdiv;
			const titleDiv = document.createElement('div');
			Boxdiv.appendChild(titleDiv);
			titleDiv.style.cssText = 'font: bold 13px Arial;line-height:25px;height:27px;text-indent:5px;color: white;background:#8BACCF';
			titleDiv.innerHTML = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;打印';
			const icoButton = document.createElement('button');
			titleDiv.appendChild(icoButton);
			icoButton.style.cssText = 'background:transparent url(' + CLODOP.strHostURI + '/c_favicon.ico) no-repeat scroll 0 0px;margin-left:5px;position:absolute;height:20px;line-height:100px;width:34px;left:3px;border:0;top:5px';
			const CloseButton = document.createElement('button');
			titleDiv.appendChild(CloseButton);
			CloseButton.style.cssText = 'background:transparent url(' + CLODOP.strHostURI + '/images/c_winclose.png) no-repeat scroll 0 0px;margin-right:5px;position:absolute;height:20px;line-height:100px;width:34px;right:3px;border:0;top:4px';
			CloseButton.onclick = function () {
				CLODOP.SelectBox.closeit();
				fnNoChoose && fnNoChoose();
			};
			const areaDiv = document.createElement('div');
			Boxdiv.appendChild(areaDiv);
			areaDiv.style.cssText = 'background:#F5F5F5;color:#000;border:0px;left:0px;top:0px;';
			areaDiv.style.width = iW - 2 + 'px';
			areaDiv.style.height = (iH - 27) + 'px';
			const OKButton = CLODOP.creatMyButtonElement('button', '确定');
			Boxdiv.appendChild(OKButton);
			OKButton.style.cssText = 'position:absolute;width:80px;f';
			OKButton.style.left = '110px'; OKButton.style.top = (iH - 64) + 'px';
			OKButton.onclick = function () {
				CLODOP.SelectBox.closeit();
				CLODOP.SET_PRINTER_INDEX(CLODOP.SelectBox.selPrinter.value);
				CLODOP.SET_PRINT_COPIES(CLODOP.SelectBox.selCopies.value);
				fnChoose && fnChoose({ cPrinter: CLODOP.SelectBox.selPrinter.value, printNums: CLODOP.SelectBox.selCopies.value });
			};
			const CancelButton = CLODOP.creatMyButtonElement('button', '取消');
			Boxdiv.appendChild(CancelButton);
			CancelButton.style.cssText = 'position:absolute;width:80px;';
			CancelButton.style.left = '240px'; CancelButton.style.top = (iH - 64) + 'px';
			CancelButton.onclick = function () {
				CLODOP.SelectBox.closeit();
				fnNoChoose && fnNoChoose();
			};
			areaDiv.appendChild(CLODOP.creatLabelElement('span', '选打印机：', 200, 46, 67));
			const oSelect = document.createElement('select');
			Boxdiv.appendChild(oSelect);
			this.selPrinter = oSelect;
			oSelect.style.cssText = 'position:absolute;size:1;width:212px;left:110px;top:62px;';
			CLODOP.KDZS_Create_Printer_List(oSelect, '', defaultPrint);
			areaDiv.appendChild(CLODOP.creatLabelElement('span', '打印份数：', 200, 46, 121));
			const oCopies = CLODOP.creatMyButtonElement('text', '1');
			Boxdiv.appendChild(oCopies);
			this.selCopies = oCopies;
			oCopies.style.cssText = 'position:absolute;size:1;width:30px;left:110px;top:117px;';
			this.FrantDiv = document.createElement('div');
			obody.appendChild(this.FrantDiv);
			this.FrantDiv.style.cssText = 'border:0px;left:0px;top:0px;filter: alpha(opacity=20); position: fixed; opacity: 0.2;-moz-opacity: 0.2; _position: absolute;z-index:91009; over-flow: hidden;';
			if (CLODOP.Browser.IE && (document.compatMode == 'BackCompat' || navigator.userAgent.indexOf('MSIE 6.0') > 0)) {
				this.FrantDiv.style.width = obody.scrollWidth + 'px';
				this.FrantDiv.style.height = obody.scrollHeight + 'px';
			} else {
				this.FrantDiv.style.width = '100%';
				this.FrantDiv.style.height = '100%';
			}
		};
	};

	// 加密方法
	lodopObj.encryText = function (text) {
		text = text || '';
		if (text.length >= 8) {
			text = text.slice(0, text.length - 8) + '****' + text.slice(-4);
		} else {
			text = text.replace(/\S{4}$/, '****');
		}
		return text;

	};
	lodopObj.getQrcodeImg = function (data,v=0) {
		// return 'data:image/png;base64,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'
		// 创建一个二维码
		// let qrcode = new QRCode('lodop', {
		// 	text: data.content || '',
		// 	width: data.itemW || '',
		// 	height: data.itemH || '',
		// 	colorDark : "#000000",
		// 	colorLight : "#ffffff",
		// });
		// 做一下兜底处理
		if(!['0','3','5','7','10','14'].includes(String(v))){
			v = 0
		}
		var qr = QRCode(0, 'M');
		let qrCodeContent = comp.base.toUtf8(data.content || '')
		qr.addData(qrCodeContent);
		qr.make();
		let dataUrl = qr.createDataURL(10, 0)
		// 输出二维码图片标签
		return dataUrl
	}
	lodopObj.printReturn = function (data) {
		const { lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc, otherParams } = data
		// 注册 PRINT_FINISH_EVENT 事件
		lodop.On_Return = function (TaskID, Value) {
			lodopObj.printFhd({
				lodop,
				printdatas,
				hasView,
				valtem,
				printBoxIsShow,
				printSynFunc,
				clearFunc,
				printOkFunc,
				selectedPrinter,
				printedNum,
				ptype,
				otherParams,
			})
		};
	}

})(window);
