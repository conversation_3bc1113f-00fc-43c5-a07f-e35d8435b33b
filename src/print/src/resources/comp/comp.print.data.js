import { YUNZHAN_AUTH_TYPE } from '../../common';
import API from '../../common/api';
import modelDialog from '../../common/model';
import { isZeroStockVersion } from '../../print/printItem/checkErpStockVersion';
/**
 * 本文件暴露 com.print.data
 *
 * 实现跟订单相关的数据请求，以及处理跟订单相关的数据
 *
 * 简写
 * Fhd: 发货单
 * Kdd: 快递单
 * Jhd: 拣货单
 *
 */
(function (window) {

	const dataObj = nameSpace('comp.print.data');
	let ajaxInitTemplates = false;
	let ajaxGlobalSettingKdd = false;
	let ajaxGlobalSettingBhd = false;
	let ajaxGlobalSettingFhd = false;
	let isMerge = false
	let isTargetMode = 1
	function getHeaderToken() {
		const kdzsToken = comp.Print.getKdzsToken();
		return {
			'qnquerystring': kdzsToken,
		};
	}

	function changeData(data) {
		return {
			Data: data.data,
			IsError: (data.result != 100),
			Msg: (data.message || ''),
		};
	}
	function checkState() {
		if (ajaxInitTemplates && ajaxGlobalSettingKdd && (!dataObj.isInit)) {
			dataObj.isInit = true;
			dataObj.initCallBack();
		}
	}

	//扩展快递单打印类型 TODO 暂时保留，后期去掉
	function extendPrintModeType(params) {
		// return $.extend({}, params, {
		//     modeType: parseInt(comp.Print.Data.modeType) || 0
		// });
		params.modeType = 1;
		return params;
	}

	//封装使用Tatami的showFial事件 组件移植 改写该提示方法即可
	// function showInfoMsg(mes,type){
	//     // window.Tatami.showFail(mes);
	//     if ( !type || type == 'loadding') {
	//         Tatami.showLoading({
	//     key:'print'
	// });
	//     }else if( type == "fail"){
	//         Tatami.showFail(mes);
	//     }else if( type == "success"){
	//         Tatami.showSuccess(mes);
	//     }
	// }

	//模块初始化方法
	dataObj.init = function (callback) {
		dataObj.initCallBack = callback;
		let setting = window.erpData?.advancedSetting?.groupPrintSetJsonString
		isMerge = setting?.openMergePrint == 2
		if (!ajaxInitTemplates || isTargetMode != setting?.openMergePrint) {
			dataObj.initTemplates(checkState);
			dataObj.initFhdTemplates();
			let isTest = localStorage.getItem('disabledInitTemp')
			if (isTest) {
				dataObj.getBqTempList({ templateType: 'bq', isRequest: true }, null);
				dataObj.getBqTempList({ templateType: 'thd', isRequest: true }, null);
				dataObj.getBqTempList({ templateType: 'dpd', isRequest: true }, null);
				dataObj.getBqTempList({ templateType: 'cgd', isRequest: true }, null);
				dataObj.getBqTempList({ templateType: 'rkd', isRequest: true }, null);
				dataObj.getBqTempList({ templateType: 'ckd', isRequest: true }, null);
				dataObj.getBqTempList({ templateType: 'thqd', isRequest: true }, null);
				dataObj.getBqTempList({ templateType: 'zbd', isRequest: true }, null);
			}
			dataObj.getTemplateDefaultItem({ type: 1 })
		}

		if (!ajaxGlobalSettingKdd) {
			dataObj.getKddGlobalSetting(comp.Print.Data.exuid, comp.Print.Data.exsubuid, checkState);
		}
		if (!ajaxGlobalSettingBhd) {
			dataObj.getBqGlobalSetting({
				isShowLoadding: false,
				templateType: 'bhd'
			});
		}
	};


	dataObj.updateFhdTableHeight = function (modeInputId, width, height) {
		const parameter = { action: 'UpdateFhdTableHeight', modeInputId: modeInputId, w: width, h: height };
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/modeInput/fixTableH',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				if (data.IsError) {
					console.error('comp.Print.data.updateFhdTableHeight data msg:' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.print.data.updateFhdTableHeight is error:' + (textStatus || errorThrown));
			},
		});
	};


	//保存发货单默认模版样式
	dataObj.saveFhdDefaultTemplate = function (modeListShowId, callback, errback) {
		const userid = comp.Print.Data.exuid;
		const printData = comp.Print.Data;
		if (!modeListShowId) {
			callback();
			return;
		}
		const parameter = { action: 'SaveFhdDefaultTemplate', exuserId: userid, modeListShowId: modeListShowId };
		const api = comp.Print.Data.platform === 'erp' ? `/print/center/modeListshow/saveFhdDefaultTemplate` : '/modeListshow/saveFhdDefaultTemplate';
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: parameter,
			async: true,
			headers: getHeaderToken(),
			success: function (data) {
				var data = changeData(data);
				if (!data.IsError) {
					// for (let i = 0; i < printData.fhdTemplateList.ModeListShows.length; i++) {
					//     let temObj = printData.fhdTemplateList.ModeListShows[i];
					//     temObj.IsDef = 0;
					//     if (temObj.Mode_ListShowId == modeListShowId) {
					//         temObj.IsDef = 1;
					//     }
					// }
					// printData.fhdTemplateList.ModeListShowId = modeListShowId;
					callback(data);
				} else {
					console.error('comp.Print.data.saveFhdDefaultTemplate data msg:' + data.Msg);
					if ($.isFunction(errback)) {
						errback(data.Msg);
					}
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.print.data.saveFhdDefaultTemplate is error:' + (textStatus || errorThrown));
				if ($.isFunction(errback)) {
					errback((textStatus || errorThrown));
				}
			},
		});
	};

	//获取发货单模版详细信息
	dataObj.getFhdTemplateInfo = function (modeListShowId, isAsync, cb, isShowLoadding, isMustReq) {
		const arr = comp.Print.Data.fhdTempInfos;
		// 用于判断交替打印，交替打印下，必须要调一次后端接口
		if (!isMustReq) {
			for (let i = 0; i < arr.length; i++) {
				const item = arr[i];
				if (item.ModeListShow.Mode_ListShowId == modeListShowId && comp.Print.Data.globalSettingFhd) {
					item.ModeSet = comp.Print.Data.globalSettingFhd?.ModeSet;
					cb && cb(item);
					return item;
				}
			}
		}
		isShowLoadding && Tatami.showLoading({
			key: 'print'
		});
		isAsync = !!isAsync;
		let ret = null;
		const userid = comp.Print.Data.exuid;
		const parameter = { templateType: 'FHD', exuserId: userid, modeListShowId: modeListShowId };
		$.ajax({
			type: 'post',
			dataType: 'json',
			// 拼多多平台发货单接了打印中心
			url: API.get_fhd_temp_info,
			data: parameter,
			async: isAsync,
			headers: getHeaderToken(),
			success: function (data) {
				isShowLoadding && Tatami.clearShow('print');
				data = changeData(data);
				// BIC订单数据项打印居中
				if (data.Data.ModeList.Excode === 'FHD' && data.Data.ModeList.Exid === 671) {
					data.Data.ModeInputs.forEach(item => item.align = 1);
				}
				if (!data.IsError) {
					arr.push(data.Data);
					ret = data.Data;
					(comp.Print.Data.globalSettingFhd || {}).ModeSet = ret.ModeSet;
					cb && cb(ret);
				} else {
					Tatami.showFail('获取发货单模版详细信息出错：' + data.Msg);
					console.error('comp.Print.data.getFhdTemplateInfo data msg:' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				isShowLoadding && Tatami.clearShow('print');
				Tatami.showFail('获取发货单模版详细信息出错：' + (textStatus || errorThrown));
				console.error('comp.print.data.getFhdTemplateInfo is error:' + (textStatus || errorThrown));
			},
		});
		return ret;
	};
	dataObj.delCacheTempInfo = function (tempid) {
		const arr = comp.Print.Data.kddTempInfos;
		arr && $.each(arr, function (_i, _v) {
			if (_v.ModeListShow.Mode_ListShowId == tempid) {
				arr.splice(_i, 1);
				return false;
			}
		});
	};
	// 获取模板组详情
	dataObj.getGroupInfo = function (groupId, isAsync, cb, isCache) {
		let arr = comp.Print.Data.kddTempInfos,
			item;
		if (!isCache) {
			for (let i = 0; i < arr.length; i++) {
				item = arr[i];
				if (item.id == groupId && item.userTemplateList) {
					cb && cb(item.userTemplateList);
					return item.userTemplateList;
				}
			}
		}
		let ret = null;
		isAsync = !!isAsync;
		const parameter = { groupId };
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: API.get_group_getGroupInfo,
			data: extendPrintModeType(parameter),
			async: isAsync,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					for (let i = 0; i < arr.length; i++) {
						item = arr[i];
						if (item.id == groupId) {
							item.userTemplateList = data?.Data?.userTemplateList
						}
					}
					comp.Print.Data.currentGroup = data.Data
					ret = data.Data;
					cb && cb(ret);
				} else {
					console.error('comp.Print.data.getTemplateInfo data msg:' + data.Msg);
					Tatami.showFail('获取模版组详细信息出错' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				isLoading && Tatami.clearShow('print');
				console.error('comp.print.data.getTemplateInfo is error:' + (textStatus || errorThrown));
				Tatami.showFail('获取模版组详细信息出错' + (textStatus || errorThrown));
			},
		});
		return ret;

	}
	//获取模版详细信息
	dataObj.getTemplateInfo = function (tempid, isAsync, cb, isLoading) {
		console.log('--获取模版详细信息--');
		let arr = comp.Print.Data.kddTempInfos,
			item;
		for (let i = 0; i < arr.length; i++) {
			item = arr[i];
			if (item.ModeListShow.Mode_ListShowId == tempid) {
				cb && cb(item);
				return item;
			}
		}
		isAsync = !!isAsync;
		let ret = null;
		const userid = comp.Print.Data.exuid;
		const parameter = { action: 'GetTemplateInfoByShowId', exuserId: userid, modeListShowId: tempid };

		isLoading && Tatami.showLoading({
			key: 'print'
		});
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: API.get_templateInfo,
			data: extendPrintModeType(parameter),
			async: isAsync,
			headers: getHeaderToken(),
			success: function (data) {
				isLoading && Tatami.clearShow('print');
				data = changeData(data);
				if (!data.IsError) {
					let modified = new Date().getTime()
					data.Data.modified = modified
					arr.push(data.Data);
					ret = data.Data;
					cb && cb(ret);
				} else {
					console.error('comp.Print.data.getTemplateInfo data msg:' + data.Msg);
					Tatami.showFail('获取模版详细信息出错' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				isLoading && Tatami.clearShow('print');
				console.error('comp.print.data.getTemplateInfo is error:' + (textStatus || errorThrown));
				Tatami.showFail('获取模版详细信息出错' + (textStatus || errorThrown));
			},
		});
		return ret;
	};

	//获取热敏发货单id
	// 之前通过 styleId 进行判断是否是一联单, 存在bug, 现在通过 WidthPaper 该字段进行判断
	dataObj.getThermalFhdId = function (tempId, cb, isHideLoading) {
		let isLoading = !isHideLoading;
		// isYiLian = WidthPaper === 760;

		this.getFhdTemplateList(true, function (d) {

			// for( const temp of d.ModeListShows){
			//     if(isYiLian){ // 本模板是一联模板
			//         if(temp.Exid == 670){ //热敏一联单
			//             thermalFhdId = temp.Mode_ListShowId;
			//             break;
			//         }
			//     }else{// 本模板不是一联模板
			//         if(temp.Exid == 666){ //热敏普通单
			//             thermalFhdId = temp.Mode_ListShowId;
			//             break;
			//         }
			//     }
			// }
			// 交替打印时必须要先请求一下发货单详情接口生成xml
			// if(thermalFhdId){  //获取模板详情---- 为了生成XML TODO 后端优化 todo todo
			dataObj.getFhdTemplateInfo(tempId, true, function (res) {
				cb && cb(tempId, res);
			}, isLoading, true);
		}, isLoading);
	};

	//获取发货单模版列表 isAsync=true 异步请求 cb为异步请求的回调函数,isShowLoadding 异步请求是否显示loadding
	dataObj.getFhdTemplateList = function (isAsync, cb, isShowLoadding, isRequest) {
		const printData = comp.Print.Data;
		if (printData.fhdTemplateList && !isRequest) {
			cb && cb(printData.fhdTemplateList);
			return printData.fhdTemplateList;
		}
		isAsync = !!isAsync;
		let ret = null;
		this.requestAPI({
			url: '/print/center/smallTag/getBhdTemplateList',
			params: {
				exUserId: printData.exuid,
				templateType: 'FHD'
			},
		}, function (json) {
			if (json.result == 100) {
				const d = json.data;
				if (!(d.ModeListShows || []).length) {
					Tatami.showFail('获取发货单模板列表为空，请联系客服');
					return;
				}
				printData.fhdTemplateList = d;
				if (typeof cb === 'function') {
					cb(d);
				}

				const funcHook = (window.printAPI.compHookObj || {}).afterLoadedFhdTemp;
				funcHook && funcHook(printData.fhdTemplateList);
			} else {
				Tatami.showFail(json.message || '获取发货单模板列表出错，请稍后再试或联系客服');
			}
		}, function (d) {
			Tatami.showFail(d || '获取发货单模板列表出错，请稍后再试或联系客服');
		});
		if (!isAsync) {
			return ret;
		}
	};

	dataObj.getXbqTemplateList = function (isAsync, cb, isShowLoadding) {
		const printData = comp.Print.Data;
		if (printData.xbqTempList) {
			cb && cb(printData.xbqTempList);
			return printData.xbqTempList;
		}
		let ret = null;
		isAsync = !!isAsync;
		const params = {
			exuserId: printData.exuid,
		};
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: API.GET_BQ_TEMPLATELIST,
			data: params,
			async: isAsync,
			headers: getHeaderToken(),
			success: function (data) {
				isShowLoadding && Tatami.clearShow('print');
				data = changeData(data);
				if (!data.IsError) {
					printData.bqTemplateList = data.Data;
					ret = data.Data;
					if (!(ret.ModeListShows || []).length) {
						Tatami.showFail('获取标签模版列表为空，请联系客服');
						return;
					}
					ret.ModeListShowId = ret.ModeListShows[0].Mode_ListShowId;
					printData.bqTemplateList = ret;
					cb && cb(ret);
				} else {
					Tatami.showFail('获取小标签模版列表出错：' + data.Msg);
					console.error('comp.Print.data.getFhdTemplateList data msg' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				isShowLoadding && Tatami.clearShow('print');
				Tatami.showFail('获取小标签模版列表出错：' + (textStatus || errorThrown));
				console.error('comp.Print.data.getFhdTemplateList is error！' + (textStatus || errorThrown));
			},
		});
		if (!isAsync) {
			return ret;
		}
	};

	//恢复默认布局
	dataObj.recoveryDefaultTemplate = function (exid, ptype) {
		if (ptype == 'kdd') {
			return dataObj.recoveryDefaultKddTemplate(exid);
		} else {
			return dataObj.recoveryDefaultFhdTemplate(exid);
		}
	};

	//获取快递模版默认布局
	dataObj.recoveryDefaultKddTemplate = function (exid) {
		let ret = null;
		const printData = comp.Print.Data;
		if (printData.defaultLayoutTemplate && printData.defaultLayoutTemplate.length > 0) {
			const len = printData.defaultLayoutTemplate.length;
			for (let i = 0; i < len; i++) {
				const temObj = printData.defaultLayoutTemplate[i];
				if (temObj.exid == exid) {
					return Object.Copy(temObj.data);
				}
			}
		}
		const parameter = {};
		parameter.action = 'RecoveryDefaultTemplate';
		parameter.exid = exid;
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeListshow/recoveryDefaultTemplate',
			url: API.recovery_defaultTemplate,
			data: parameter,
			async: false,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					ret = Object.Copy(data.Data);
					const temObj = {};
					temObj.exid = exid;
					temObj.data = data.Data;
					printData.defaultLayoutTemplate.push(temObj);
				} else {
					console.error('comp.Print.data.recoveryDefaultKddTemplate data msg' + data.Msg);
					alert('获取快递模版默认布局出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.recoveryDefaultKddTemplate is error！' + (textStatus || errorThrown));
				alert('获取快递模版默认布局出错');
			},
		});
		return ret;
	};

	//获取发货单模版默认布局
	dataObj.recoveryDefaultFhdTemplate = function (exid) {
		let ret = null;
		const printData = comp.Print.Data;
		if (printData.defaultLayoutFhdTemplate && printData.defaultLayoutFhdTemplate.length > 0) {
			const len = printData.defaultLayoutTemplate.length;
			for (let i = 0; i < len; i++) {
				const temObj = printData.defaultLayoutFhdTemplate[i];
				if (temObj.exid == exid) {
					return Object.Copy(temObj.data);
				}
			}
		}
		const parameter = {};
		parameter.action = 'RecoveryDefaultFhdTemplate';
		parameter.exid = exid;
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/modeListshow/recoveryDefaultFhdTemplate',
			data: parameter,
			async: false,
			headers: getHeaderToken(),
			success: function (data) {
				var data = changeData(data);
				if (!data.IsError) {
					ret = Object.Copy(data.Data);
					const temObj = {};
					temObj.exid = exid;
					temObj.data = data.Data;
					printData.defaultLayoutTemplate.push(temObj);
				} else {
					console.error('comp.Print.data.recoveryDefaultFhdTemplate data msg' + data.Msg);
					alert('获取发货单模版默认布局出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.recoveryDefaultFhdTemplate is error！' + (textStatus || errorThrown));
				alert('获取发货单模版默认布局出错');
			},
		});
		return ret;
	};

	let initTypeObject = null;

	//初始化某一个元素中带有initType的标记
	dataObj.initTypeDom = function (dom) {
		if (!initTypeObject) {
			const that = new comp.Print.FN();
			initTypeObject = {};
			let fontfamilyHtml = '<option value=\'\'>默认</option>';
			const fonts = that.getSystemFonts();
			$.each(fonts, function (ind, inv) {
				fontfamilyHtml += '<option value=\'' + inv + '\'>' + inv + '</option>';
			});
			initTypeObject.fontfamily = function (dom) {
				dom.html(fontfamilyHtml);
			};

			let fontnumHtml = '<option value=\'0\'>默认</option>';
			for (var i = 2; i <= 96; i++) {
				fontnumHtml += '<option value=\'' + i + '\'>' + i + '</option>';
			}
			initTypeObject.fontnum = function (dom) {
				dom.html(fontnumHtml);
			};

			const fontboldHtml = '<option value="-1">默认</option> <option value="1">加粗</option><option value="0">不加粗</option>';
			initTypeObject.fontbold = function (dom) {
				dom.html(fontboldHtml);
			};

			const textalignHtml = '<option value="-1">默认</option> <option value="1">左</option><option value="2">中</option><option value="3">右</option>';
			initTypeObject.textalign = function (dom) {
				dom.html(textalignHtml);
			};

			const linestyleHtml = '<option value="0">实线</option><option value="2">虚线</option>';
			initTypeObject.linestyle = function (dom) {
				dom.html(linestyleHtml);
			};
			let linestyle2Html = '<option value="1">实线</option><option value="2">虚线</option><option value="3">无线</option>';
			initTypeObject.linestyle2 = function (dom) {
				dom.html(linestyle2Html);
			};

			const lineboldHtml = '<option value="1">不加粗</option><option value="2">加粗</option>';
			initTypeObject.linebold = function (dom) {
				dom.html(lineboldHtml);
			};

			let fontspaceHtml = '<option value=\'-1\'>默认</option>';
			let linespaceHtml = '<option value=\'-1\'>默认</option>';

			for (var i = 1; i <= 10; i++) {
				fontspaceHtml += '<option value=\'' + i + '\'>' + i + '</option>';
				linespaceHtml += '<option value=\'' + i + '\'>' + i + '</option>';
			}

			initTypeObject.fontspace = function (dom) {
				dom.html(fontspaceHtml);
			};

			initTypeObject.linespace = function (dom) {
				dom.html(linespaceHtml);
			};

			const printdirectionHtml = '<option value="0">默认</option> <option value="1">纵向</option><option value="2">横向</option>';
			initTypeObject.printdirection = function (dom) {
				dom.html(printdirectionHtml);
			};

			const linedirectionHtml = '<option value="1">竖</option><option value="2">横</option>';
			initTypeObject.linedirection = function (dom) {
				dom.html(linedirectionHtml);
			};
		}

		const doms = dom.find('select[inittype]');
		doms.each(function (ind, inv) {
			const tarDom = $(inv);
			const attrName = tarDom.attr('inittype').toLowerCase();
			const fun = initTypeObject[attrName];
			if (fun) {
				fun(tarDom);
			}
		});
	};

	//获取系统字体
	dataObj.getSystemFonts = function (isAsync) {
		const printData = comp.Print.Data;
		if (printData.systemFonts && printData.systemFonts.length > 0) {
			return printData.systemFonts;
		}
		isAsync = !!isAsync;
		const parameter = {};
		parameter.action = 'GetSystemFonts';
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/modeSet/getSystemFonts',
			data: parameter,
			async: false,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					printData.systemFonts = data.Data;
				} else {
					console.error('comp.Print.data.getSystemFonts data msg' + data.Msg);
					alert('获取系统字体出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getSystemFonts is error！' + (textStatus || errorThrown));
				alert('获取系统字体出错');
			},
		});
		return printData.systemFonts;
	};


	//初始化当前登录用户的快递模版
	dataObj.initTemplates = function (callback) {
		let printData = comp.Print.Data
			, parameter = {}
			, list;
		let setting = window.erpData?.advancedSetting?.groupPrintSetJsonString
		if (!!isMerge) {
			ajaxInitTemplates = false;
		} else if (isTargetMode == setting?.openMergePrint && printData.kddTemplates && !printData.kddTemplates.ModeListShows && !printData.kddTemplates.ModeListShows.length) {
			ajaxInitTemplates = true;
			return;
		}
		parameter.action = 'GetTemplateList';
		parameter.exuserId = printData.exuid;
		parameter.subUserId = printData.exsubuid;
		parameter.modeId = 'kdd';
		parameter.pageIndex = 0;
		parameter.pageSize = 0;
		const url = !!isMerge ? API.get_group_getGroupList : API.get_kdd_TemplateList
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeListshow/getTemplateList',
			url: url,
			data: extendPrintModeType(parameter),
			headers: getHeaderToken(),
			success: function (data) {
				isTargetMode = setting?.openMergePrint
				data = changeData(data);
				if (!data.IsError) {
					printData.kddTemplates = data.Data;
					ajaxInitTemplates = true;
					const funcHook = (window.printAPI.compHookObj || {}).afterLoadedKddTemp;
					funcHook && funcHook(printData.kddTemplates, comp.Print.Data.userInfo.userId);

					if (typeof callback === 'function') {
						callback();
					}
					list = comp.Print.eventObj.KddTemplateListChanged;
					if (list) {
						list.each(function (index, item) {
							// item(data.Data);
							item();
						});
					}
				} else {
					console.error('comp.Print.data. data msg' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.initTemplates is error！' + (textStatus || errorThrown));
			},
		});
	};

	//初始化当前登录用户的发货单模版
	dataObj.initFhdTemplates = function (callback) {
		let printData = comp.Print.Data
			, list;
		this.requestAPI({
			url: '/print/center/smallTag/getBhdTemplateList',
			params: {
				exUserId: printData.exuid,
				templateType: 'FHD'
			},
		}, function (json) {
			if (json.result == 100) {
				const d = json.data;
				if (!(d.ModeListShows || []).length) {
					Tatami.showFail('获取发货单模板列表为空，请联系客服');
					return;
				}
				printData.fhdTemplateList = d;
				if (typeof callback === 'function') {
					callback(d);
				}
				list = comp.Print.eventObj.FhdTemplateListChanged;
				if (list) {
					list.each(function (index, item) {
						item(d);
					});
				}
				const funcHook = (window.printAPI.compHookObj || {}).afterLoadedFhdTemp;
				funcHook && funcHook(printData.fhdTemplateList);
			} else {
				Tatami.showFail(json.message || '获取发货单模板列表出错，请稍后再试或联系客服');
			}
		}, function (d) {
			Tatami.showFail(d || '获取发货单模板列表出错，请稍后再试或联系客服');
		});

	};

	//保存拣货单设置
	dataObj.updateJhdSet = function (jhdSet, callback, errorcallback) {
		const printData = comp.Print.Data;
		const parameter = {};
		parameter['action'] = 'updatejhdset';
		parameter['ptype'] = printData.ptype;
		parameter['exuid'] = printData.exuid;
		// parameter["uk"] = printData.uk;
		parameter['jhdset'] = JSON.stringify(jhdSet);
		$.ajax({
			type: 'post',
			dataType: 'json', //返回json格式的数据
			url: '/jhdSet/updateJhdSet',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				var data = changeData(data);
				if (!data.IsError) {
					comp.Print.Data.jhdSet = jhdSet;
					callback(jhdSet);
				} else {
					console.error('comp.Print.data.updateJhdSet data msg' + data.mess);
					errorcallback(data.mess);
				}
			},
			error: function (request, textStatus, errorThrown) {
				const mess = (textStatus || errorThrown);
				errorcallback(mess);
				console.error('comp.Print.data.updateJhdSet is error！' + (textStatus || errorThrown));
			},
		});
	};

	//获取拣货单设置
	dataObj.getJhdSet = function (callback, isNotLoading, isRequest) {
		const printData = comp.Print.Data;
		if (!isRequest && printData.jhdSet) {
			setTimeout(function () {     //避免出现 页面js操作卡死的情况 如弹窗关闭不了的情况
				callback && callback(printData.jhdSet);
			}, 0);
			return;
		}
		!isNotLoading && Tatami.showLoading({
			key: 'print'
		});
		$.ajax({
			type: 'post',
			dataType: 'json', //返回json格式的数据
			url: '/jhdSet/getJhdSet',
			data: {
				exuid: printData.exuid,
			},
			headers: getHeaderToken(),
			success: function (data) {
				!isNotLoading && Tatami.clearShow('print');
				data = changeData(data);
				if (!data.IsError) {
					printData.jhdSet = data.Data;
					setTimeout(function () {     //避免出现 loadding 关闭不了的情况
						callback && callback(data.Data);
					}, 100);
				} else {
					console.error('comp.Print.data.getJhdSet data msg' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				!isNotLoading && Tatami.clearShow('print');
				console.error('comp.Print.data.initTemplates is error！' + (textStatus || errorThrown));
			},
		});
	};

	dataObj.getBqGlobalSetting = function (arg, callback) {
		const bqType = {
			'bhd': 'BHD',
			'bq': 'BHD',
			'thd': 'THD',
			'dpd': 'DPD',
			'tmd': 'DPD',
			'cgd': 'BHD',
			'rkd': 'BHD',
			'ckd': 'BHD',
			'wdpd': 'DPD',
			'wtmd': 'DPD',
			'thqd': 'BHD',
			'zbd': 'ZBD',
			'bhbq': 'BHBQ',
		}
		// if(isZeroStockVersion()){
		//     bqType['wdpd'] = 'WDPD'
		//     bqType['wtmd'] = 'WTMD'
		// }
		const _set = comp.Print.Data['globalSettingBq' + arg.templateType];
		arg = arg || { isShowLoadding: true };
		// todo
		// if(!arg.isRequest && (!$.isEmptyObject(_set) || !_set) ){
		//     callback && callback(_set);
		//     return;
		// }
		this.requestAPI({
			url: '/print/center/smallTag/getBhdGlobalSetting',
			params: {
				// exuserId:comp.Print.Data.exuid,
				// subUserId:comp.Print.Data.exsubuid,
				// modeid:'bhd',
				templateType: bqType[arg.templateType]
			},
			isShowLoadding: arg.isShowLoadding,
		}, function (json) {
			if (json.result == 100) {
				if (!ajaxGlobalSettingBhd) {
					ajaxGlobalSettingBhd = true;
				}
				if (['bq', 'bhd'].includes(arg.templateType)) {
					comp.Print.Data['globalSettingBqbq'] = json.data;
					comp.Print.Data['globalSettingBqbhd'] = json.data;
					comp.Print.Data['globalSettingBqcgd'] = json.data;
					comp.Print.Data['globalSettingBqrkd'] = json.data;
					comp.Print.Data['globalSettingBqckd'] = json.data;
					comp.Print.Data['globalSettingBqthqd'] = json.data;
					comp.Print.Data['globalSettingBqbic'] = json.data;
					comp.Print.Data['globalSettingBqzbd'] = json.data;
					comp.Print.Data['globalSettingBqzbxp'] = json.data;
					comp.Print.Data['globalSettingBqbhbq'] = json.data;
				} else if (['dpd', 'tmd', 'wdpd', 'wtmd'].includes(arg.templateType)) {
					comp.Print.Data['globalSettingBqdpd'] = json.data;
				} else {
					comp.Print.Data['globalSettingBq' + arg.templateType] = json.data;
				}
				callback && callback(json.data);
			} else {
				Tatami.showFail('获取标签全局设置出错：' + json.message);
			}
		}, function (d) {
			Tatami.showFail('获取标签全局设置出错：' + d);
		});
	};
	// 获取小标签模板列表
	dataObj.getBqTempList = function ({ templateType, isRequest }, cb) {
		console.log('[打印中心的获取小标签模板列表]', '--', templateType);
		const bqType = {
			'bhd': 'BHD,BKD',
			'bq': 'BHD,BKD',
			'thd': 'THD',
			'dpd': 'TMD,DPD',
			'cgd': 'CGD',
			'rkd': 'RKD',
			'ckd': 'CKD',
			'fhd': 'FHD',
			'thqd': 'THQD',
			'zbd': 'ZBD,ZBXP',
			'zbxp': 'ZBD,ZBXP',
			'bhbq': 'BHBQ',
		}
		if (isZeroStockVersion()) {
			bqType['dpd'] = 'WDPD,WTMD'
		}
		const printData = comp.Print.Data;
		if (!isRequest && printData['bqTemplateList' + templateType]) {
			cb && cb(printData['bqTemplateList' + templateType]);
			return printData['bqTemplateList' + templateType];
		}
		this.requestAPI({
			url: '/print/center/smallTag/getBhdTemplateList',
			params: {
				exUserId: printData.exuid,
				templateType: bqType[templateType]
			},
		}, function (json) {
			if (json.result == 100) {
				const d = json.data;
				if (!(d.ModeListShows || []).length) {
					Tatami.showFail('获取标签模版列表为空，请联系客服');
					return;
				}
				if (['bq', 'bhd'].includes(templateType)) {
					printData['bqTemplateListbq'] = d;
					printData['bqTemplateListbhd'] = d;

				} else {
					printData['bqTemplateList' + templateType] = d;
				}
				const funcHook = (window.printAPI.compHookObj || {})[`afterLoaded${templateType}XbqTemp`];
				funcHook && funcHook(printData['bqTemplateList' + templateType]);
				cb && cb(d);
			} else {
				Tatami.showFail(json.message || '获取标签模版列表出错，请稍后再试或联系客服');
			}
		}, function (d) {
			Tatami.showFail(d || '获取标签模版列表出错，请稍后再试或联系客服');
		});

	};

	//获取标签模板详细信息
	dataObj.getBqTemplateInfo = function (modeListShowId, cb, isShowLoadding) {
		let arr = comp.Print.Data.bqTempInfos;
		// todo
		arr = [];
		let ret = null;
		for (let i = 0; i < arr.length; i++) {
			const item = arr[i];
			if (item.ModeListShow.Mode_ListShowId == modeListShowId) {
				// item.ModeSet = comp.Print.Data.globalSettingBq.ModeSet ;
				cb && cb(item);
				return item;
			}
		}
		this.requestAPI({
			url: '/print/center/smallTag/getBhdTemplateInfo',
			params: {
				exuserId: comp.Print.Data.exuid,
				modeListShowId: modeListShowId,
			},
			isShowLoadding: isShowLoadding,
		}, function (json) {
			if (json.result == 100) {
				json.data.ModeSet.Fhdishb = 0
				arr.push(json.data);
				ret = json.data;
				(comp.Print.Data.bqTempInfos || {}).ModeSet = ret.ModeSet;
				cb && cb(json.data);
			} else {
				Tatami.showFail(json.message || '获取标签模版详情出错，请稍后再试或联系客服');
			}
		}, function (d) {
			Tatami.showFail(d || '获取标签模版详情出错，请稍后再试或联系客服');
		});
	};

	//新增小标签
	dataObj.saveAddBq = function (obj, callback, tempType) {
		const bqType = {
			'bhd': 'BHD',
			'bq': 'BHD',
			'thd': 'THD'
		}
		// if(obj.type === 'TMD') obj.type = 'DPD'
		this.requestAPI({
			url: '/print/center/smallTag/addBhdTemplate',
			params: {
				jsonParam: JSON.stringify(obj),
				templateType: obj.type
			},
		}, function (json) {
			if (json.result === 100) {
				if (tempType === 'fhd') {
					dataObj.getFhdTemplateList(false, function () {
						callback(json.data);
					}, true, true);
				} else {
					dataObj.getBqTempList({ templateType: tempType, isRequest: true }, function () {
						callback(json.data);
					}, true);
				}
			} else {
				Tatami.showFail(json.message || '新增模版失败，请稍后再试或联系客服');
			}
		}, function (d) {
			Tatami.showFail(d || '新增模版失败，请稍后再试或联系客服');
		});
	};

	//保存全局设置
	dataObj.saveFhdBqGlobalSet = function (obj, callback, type) {
		// 【临时方案】如果是扫描小标签模版，从comp.Print.Data获取bqTemplateList数据重组
		const bqType = {
			'bhd': 'BHD',
			'bq': 'BHD',
			'thd': 'THD'
		}
		if (type === 'bq') {
			obj.ModeListShows = comp.Print.Data['bqTemplateList' + type] && comp.Print.Data['bqTemplateList' + type].ModeListShows;
		}

		this.requestAPI({
			url: '/print/center/smallTag/saveBhdGlobalSetting',
			params: {
				jsonParam: JSON.stringify(obj),
				templateType: bqType[type]
			},
		}, function (json) {
			if (json.result === 100) {
				if (type === 'fhd') {
					dataObj.getFhdGlobalSetting(null, function () {
						dataObj.initFhdTemplates(function () {
							callback(json.data);
						});
					});
					comp.Print.Data.globalSettingFhd = obj;
					const eventObj = comp.Print.eventObj; //保存发货单全局设置注册函数

					if (eventObj && eventObj.saveFhdGlobalEvent) {
						eventObj.saveFhdGlobalEvent[0]();
					}
				} else {
					comp.Print.Data['globalSettingBq' + type] = obj;
					dataObj.getBqTempList({ templateType: type, isRequest: true }, function () {
						callback(json.data);
					});
				}
			} else {
				Tatami.showFail('保存全局设置出错：' + json.message);
			}
		}, function (d) {
			Tatami.showFail(d || '保存全局设置出错,请稍后再试，或联系客服');
		});
	};

	//保存排序和删除的快递模版
	dataObj.saveSortKdd = function (obj, callback, errorback) {
		const parameter = {};
		obj.kddType = 'kdd';
		parameter.action = 'SaveSrotAndDelete';
		parameter.jsonParam = JSON.stringify(obj);
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: isMerge ? API.save_group_sortAndDelete : API.save_sortAndDelete,
			// url: '/modeListshow/saveSortAndDelete',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					dataObj.initTemplates(function () {
						callback(data.Data);
					});
					const eventObj = comp.Print.eventObj;
					const list = eventObj.dataChanged;
					if (list) {
						list.each(function (index, item) {
							item('saveSortKdd');
						});
					}
				} else {
					console.error('comp.Print.data.saveSortKdd data msg' + data.Msg);
					if ($.isFunction(errorback)) {
						errorback(data.Msg);
					}
				}
			},
			error: function (request, textStatus, errorThrown) {
				const msg = (textStatus || errorThrown);
				console.error('comp.Print.data.saveSortKdd is error！' + msg);
				if ($.isFunction(errorback)) {
					errorback(msg);
				}
			},
		});
	};

	//保存排序和删除的发货单模版
	dataObj.saveSortFhd = function (obj, callback, errorback) {
		const parameter = {};
		obj.kddType = 'fhd';
		parameter.action = 'SaveSrotAndDelete';
		parameter.jsonParam = JSON.stringify(obj);
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: API.save_sortAndDelete,
			// url: '/modeListshow/saveSortAndDelete',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					dataObj.initFhdTemplates(function () {
						callback(data.Data);
					});
					const eventObj = comp.Print.eventObj;
					const list = eventObj.dataChanged;
					if (list) {
						list.each(function (index, item) {
							item('saveSortKdd');
						});
					}
				} else {
					console.error('comp.Print.data.saveSortKdd data msg' + data.Msg);
					if ($.isFunction(errorback)) {
						errorback(data.Msg);
					}
				}
			},
			error: function (request, textStatus, errorThrown) {
				const msg = (textStatus || errorThrown);
				console.error('comp.Print.data.saveSortKdd is error！' + msg);
				if ($.isFunction(errorback)) {
					errorback(msg);
				}
			},
		});
	};

	// 保存排序
	dataObj.saveSortBq = function (obj, callback, errorback, templateType) {
		obj.kddType = 'fhd';
		this.requestAPI(
			{
				url: '/print/center/smallTag/saveSortAndDelete',
				params: {
					jsonParam: JSON.stringify(obj),
				},
			},
			function (data) {
				if (data.result == 100) {
					dataObj.getBqTempList({ templateType, isRequest: true }, (newTemplist) => {
						callback(newTemplist);
					});
				} else {
					Tatami.showFail('扫描小标签排序错误' + data.message);
				}
			},
			function (d) {
				errorback && errorback(d.message);
			},
		);
	};

	//获取快递单全局设置
	dataObj.getKddGlobalSetting = function (exuserId, subUserId, callback) {
		const parameter = {};
		parameter.exuserId = exuserId;
		parameter.subUserId = subUserId;
		parameter.modeid = 'kdd';
		parameter.action = 'GetKddGlobalSetting';
		const kddGlobalSetting = window.sessionStorage.getItem('kddGlobalSetting');
		function handleData(data) {
			data = changeData(data);
			if (!data.IsError) {
				if (!ajaxGlobalSettingKdd) {
					ajaxGlobalSettingKdd = true;
				}
				comp.Print.Data.globalSettingKdd = data.Data;
				comp.Print.Data.systemFonts = data.Data.SystemFonts;
				callback(data.Data);
			} else {
				console.error('comp.Print.data.getKddGlobalSetting data msg' + data.Msg);
			}
		}
		if (kddGlobalSetting) {
			handleData(kddGlobalSetting);
			return;
		}
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: API.get_kddGlobalSetting,
			// url: '/modeSet/getKddGlobalSetting',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				handleData(data);
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getKddGlobalSetting is error！' + (textStatus || errorThrown));
			},
		});
	};

	//获取发货单全局设置
	dataObj.getFhdGlobalSetting = function (arg, callback) {
		const _set = comp.Print.Data.globalSettingFhd;
		arg = arg || {};
		if (!arg.isRequest && (JSON.stringify(_set || {}) !== '{}') && !Object.keys(_set).length) {
			callback && callback(_set);
			return;
		}
		arg.isShowLoadding && Tatami.showLoading({
			key: 'print'
		});
		const url = comp.Print.Data.platform == 'erp' ? `/print/center/smallTag/getBhdGlobalSetting` : API.get_fhd_global_setting;
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: url,
			data: {
				// exuserId:arg.exuserId || comp.Print.Data.exuid,
				// subUserId:arg.subUserId || comp.Print.Data.exsubuid,
				// modeid:'fhd',
				templateType: 'FHD'

			},
			headers: getHeaderToken(),
			success: function (data) {
				arg.isShowLoadding && Tatami.clearShow('print');
				data = changeData(data);
				if (!data.IsError) {
					if (!ajaxGlobalSettingFhd) {
						ajaxGlobalSettingFhd = true;
					}
					comp.Print.Data.globalSettingFhd = data.Data;
					callback && callback(data.Data);
				} else {
					Tatami.showFail('获取发货单全局设置出错：' + data.Msg);
					console.error('comp.Print.data.getKddGlobalSetting data msg' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				arg.isShowLoadding && Tatami.clearShow('print');
				Tatami.showFail('获取发货单全局设置出错：' + (textStatus || errorThrown));
				console.error('comp.Print.data.getKddGlobalSetting is error！' + (textStatus || errorThrown));
			},
		});
		!arg.isNotUpUseFhdTime && localStorage.setItem('comp.isUsePrintFhd', new Date().getTime());      //最近一次获取发货单详情的时间
	};

	//保存快递单全局设置
	dataObj.saveKddGlobalSetting = function (obj, callback) {
		const printData = comp.Print.Data;
		const parameter = {};
		parameter.jsonParam = JSON.stringify(obj);
		parameter.action = 'SaveKddGlobalSetting';
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeSet/saveKddGlobalSetting',
			url: API.save_kddGlobalSetting,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				window.sessionStorage.removeItem('kddGlobalSetting');
				data = changeData(data);
				if (!data.IsError) {
					dataObj.getKddGlobalSetting(printData.exuid, printData.exsubuid, function () {
						dataObj.initTemplates(function () {
							callback(data.Data);
						});
					});
					const eventObj = comp.Print.eventObj;
					const list = eventObj.dataChanged;
					if (list) {
						list.each(function (index, item) {
							item('saveKddGlobalSetting');
						});
					}
				} else {
					console.error('comp.Print.data.saveKddGlobalSetting data msg' + data.Msg);
					alert('保存快递单全局设置出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveKddGlobalSetting is error！' + (textStatus || errorThrown));
				alert('保存快递单全局设置出错');
			},
		});
	};
	//保存快递分组全局设置
	dataObj.saveKddGroupGlobalSetting = function (obj, callback) {
		const printData = comp.Print.Data;
		const parameter = {};
		parameter.jsonParam = JSON.stringify(obj);
		parameter.action = 'save_kddGroupGlobalSetting';
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeSet/saveKddGlobalSetting',
			url: API.save_kddGroupGlobalSetting,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					dataObj.initTemplates(function () {
						callback(data.Data);
					});
					// const eventObj = comp.Print.eventObj;
					// const list = eventObj.dataChanged;
					// if (list) {
					//     list.each(function (index, item) {
					//         item('saveKddGlobalSetting');
					//     });
					// }
				} else {
					console.error('comp.Print.data.saveKddGlobalSetting data msg' + data.Msg);
					alert('保存快递单全局设置出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveKddGlobalSetting is error！' + (textStatus || errorThrown));
				alert('保存快递单全局设置出错');
			},
		});
	};
	// 保存分组设置
	dataObj.saveTemplateGroup = function (obj, callback) {
		const parameter = {};
		parameter.jsonParam = JSON.stringify(obj);
		parameter.action = 'saveTemplateGroup';
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeSet/saveKddGlobalSetting',
			url: API.get_group_addTemplateGroup,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					dataObj.initTemplates(function () {
						callback();
					});
				} else {
					console.error('comp.Print.data.saveKddGlobalSetting data msg' + data.Msg);
					Tatami.showFail(data.errorMessage || "保存快递单分组设置出错")
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveTemplateGroup is error！' + (textStatus || errorThrown));
				Tatami.showFail(data.errorMessage || '保存快递单分组设置出错')
			},
		});
	};
	//保存发货单全局设置
	dataObj.saveFhdGlobalSetting = function (obj, callback) {
		const printData = comp.Print.Data;
		const parameter = {};
		parameter.jsonParam = JSON.stringify(obj);
		parameter.templateType = 'FHD';
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/print/center/smallTag/saveBhdGlobalSetting',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				var data = changeData(data);
				if (!data.IsError) {
					dataObj.getFhdGlobalSetting({
						exuserId: printData.exuid,
						subUserId: printData.exsubuid,
					}, function () {
						dataObj.initFhdTemplates(function () {
							callback(data.Data);
						});
					});
					comp.Print.Data.globalSettingFhd = obj;
					const eventObj = comp.Print.eventObj;
					// var list = eventObj.dataChanged;
					// if (list) {
					//     list.each(function (index, item) {
					//         item("saveFhdGlobalSetting");
					//     });
					// }
					//保存发货单全局设置注册函数
					// if ( eventObj && eventObj.saveFhdGlobalEvent) {
					//     eventObj.saveFhdGlobalEvent[0]();
					// }
					sessionStorage.setItem('isGetFjrByApi', 1);

				} else {
					console.error('comp.Print.data.saveKddGlobalSetting data msg' + data.Msg);
					alert('保存发货单全局设置出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveKddGlobalSetting is error！' + (textStatus || errorThrown));
				alert('保存发货单全局设置出错');
			},
		});
	};

	//保存新增的快递模版
	dataObj.saveAddKdd = function (obj, callback, groupObj) {
		const parameter = {};
		const url = !!isMerge ? API.get_group_addTemplate : API.add_Template
		parameter.action = 'AddTemplate';
		obj.authodV2 = true
		parameter.jsonParam = JSON.stringify(obj);
		!!groupObj && (parameter.jsonParamGroup = JSON.stringify(groupObj));
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeListshow/addTemplate',
			url: url,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);

				// for test
				// data.IsError = true
				// data.Msg = "订单号：" + 'STO12314214' +  "[中通]代收货款对总调用接口发生错误，错误信息:" + 'Somthing wrong';
				if (!data.IsError) {
					dataObj.initTemplates(function () {
						callback(data.Data);
					});
					const eventObj = comp.Print.eventObj;
					const list = eventObj.dataChanged;
					if (list) {
						list.each(function (index, item) {
							item('saveAddKdd');
						});
					}
				} else {
					console.error('comp.Print.data.saveAddKdd data msg' + data.Msg);
					if (/\[中通\]代收货款对总调用接口发生错误/.test(data.Msg)) {
						const id = /订单号：([\d\w]+)/.exec(data.Msg)[1];

						modelDialog({
							type: 'confirm',
							title: '<span style="font-size: larger">申请代收货款（对总模式）出错<span>',
							content: `<span style="font-size: larger; line-height: 1.5em;">订单号：${id} <br> 申请代收货款（对总模式）接口时出错，请联系客服处理！<span>`,
							height: 200,
							width: 500,
							cancelHide: true,
						});
					} else {
						alert('新增模版失败');
					}
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveAddKdd is error！' + (textStatus || errorThrown));
				alert('新增模版失败');
			},
		});
	};

	//获取发货单模板类型
	dataObj.getFhdType = function (callback) {
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/modeListshow/getFhdType',
			data: {},
			headers: getHeaderToken(),
			success: function (data) {
				callback && callback(data);
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getFhdType is error！' + (textStatus || errorThrown));
				alert('获取模版类型失败');
			},
		});
	};
	//新增模版时上传图片
	dataObj.uploadImgAddKdd = function (btnId, fileName, beforeFun, successFun) {
		const kdzsToken = comp.Print.getKdzsToken();
		$('#' + btnId).fileUpload('/modeListimg/uploadCustomBgImg?kdzsTaobaoToken=' + kdzsToken, fileName, function (data) {
			data.action = 'UploadCustomBgImg';
			beforeFun(data);
		}, function (data) {
			var data = changeData(data);
			if (data.IsError) {
				alert('模版上传图片失败');
				console.error('comp.Print.data.uploadImgAddKdd is error :' + data.Msg);
				return;
			}
			successFun(data.Data);
		}, function (err) {
			alert('模版上传图片失败');
			console.error('comp.Print.data.uploadImgAddKdd is error :' + err);
		});
	};

	//上传店标  TODO接口可去掉 貌似该功能去掉了
	// dataObj.uploadShopLogo = function (btnId, fileName, beforeFun, successFun) {
	//     var kdzsToken = comp.Print.getKdzsToken();
	//     $("#" + btnId).fileUpload("/modeLogo/uploadShopLogo?kdzsTaobaoToken="+kdzsToken, fileName, function (data) {
	//         data.action = "UploadShopLogo";
	//         beforeFun(data);
	//     }, function (data) {
	//         var data = changeData(data);
	//         if (data.IsError) {
	//             alert("上传店标失败");
	//             console.error("comp.Print.data.uploadShopLogo is error :" + data.Msg);
	//             return;
	//         }
	//         successFun(data.Data);
	//     }, function (err) {
	//         alert("上传店标失败");
	//         console.error("comp.Print.data.uploadShopLogo is error :" + err);
	//     })
	// }

	//上传图片
	dataObj.uploadCustomImg = function (btnId, fileName, beforeFun, successFun) {
		const kdzsToken = comp.Print.getKdzsToken();
		$('#' + btnId).fileUpload('/modeListimg/uploadCustomImg?kdzsTaobaoToken=' + kdzsToken, fileName, function (data) {
			data.action = 'UploadCustomImg';
			beforeFun(data);
		}, function (data) {
			var data = changeData(data);
			if (data.IsError) {
				Tatami.showFail('模版中图片上传失败' + data.Msg);
				return;
			}
			successFun(data.Data);
		}, function (err) {
			Tatami.showFail('模版中图片上传失败');
			console.error('comp.Print.data.uploadCustomImg is error :' + err);
		});
	};

	//保存模版底图
	dataObj.saveTemplateBgImg = function (modeListShowId, bgImgId, imgSrc, callback) {
		const parameter = {};
		parameter.action = 'SaveTemplateBgImg';
		parameter.modeListShowId = modeListShowId;
		parameter.bgImgId = bgImgId;
		parameter.imgSrc = imgSrc;
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/modeListshow/saveTemplateBgImg',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.saveTemplateBgImg data msg' + data.Msg);
					alert('保存模版底图失败');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveTemplateBgImg is error！' + (textStatus || errorThrown));
				alert('保存模版底图失败');
			},
		});
	};

	//删除上传的图片
	dataObj.delAddKddImg = function (bgImgId, callback) {
		const parameter = {};
		parameter.action = 'DeleteCustomBgImg';
		parameter.bgImgId = bgImgId;
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/modeListimg/deleteCustomBgImg',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.delAddKddImg data msg' + data.Msg);
					alert('删除上传的图片失败');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.delAddKddImg is error！' + (textStatus || errorThrown));
				alert('删除上传的图片失败');
			},
		});
	};

	//初始化快递
	dataObj.initExCompany = function (callback) {
		const printData = comp.Print.Data;
		const url = !!isMerge ? API.get_group_initExCompany : API.get_initExCompany
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: url,
			data: {},
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					printData.exCompanys = data.Data;
					callback(data.Data);
				} else {
					console.error('comp.Print.data.initexcompany data msg:' + data.Msg);
					alert('初始化快递失败');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.print.data.initexcompany is error:' + (textStatus || errorThrown));
			},
		});
	};


	//设置模版默认打印机
	dataObj.saveDefaultPrinter = function (mkddid, configId, printerName, callback, isGroup) {
		let parameter,
			url;
		if (isGroup) {
			url = API.save_group_default_printer
			parameter = { groupId: mkddid, printName: printerName };

		} else {
			parameter = { action: 'SaveDefaultPrinter', configId: configId, printerName: printerName };
			parameter.modeListShowId = mkddid;
			parameter.exuserId = comp.Print.Data.exuid;
			url = API.save_default_printer
		}

		$.ajax({
			type: 'post',
			dataType: 'json',
			url: url,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					callback(data?.Data);
					console.error('comp.Print.data.saveDefaultPrinter data msg:' + data.Msg);
					Tatami.showFail('设置模版默认打印机出错')
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.print.data.saveDefaultPrinter is error:' + (textStatus || errorThrown));
				Tatami.showFail('设置模版默认打印机出错')
			},
		});
	};
	//获取面单底图
	dataObj.getGroupSelectAllowUserTemplate = function (data, callback) {
		const parameter = { exCompanyId: data.exCompanyId, paperHeight: data.paperHeight, paperWidth: data.paperWidth, modeZmj: data?.modeZmj || '0' };
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: API.get_group_selectAllowUserTemplate,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.getKddBgImgBy is error' + data.Msg);
					alert('获取快递模板出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getKddBgImgBy is error！' + (textStatus || errorThrown));
				alert('获取快递模板出错');
			},
		});
	};

	//获取面单底图
	dataObj.getKddBgImgBy = function (companyId, kddtype, styleId, isDefault, callback) {
		const parameter = { action: 'GetKddBgImgBy', companyId: companyId, kddtype: kddtype, styleid: styleId };
		parameter.isDefault = isDefault;
		parameter.exuserid = comp.Print.Data.exuid;
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/modeListimg/getKddBgImgBy',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.getKddBgImgBy is error' + data.Msg);
					alert('获取面单底图出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getKddBgImgBy is error！' + (textStatus || errorThrown));
				alert('获取面单底图出错');
			},
		});
	};

	// 模版右侧标签 批打的发货单type=1；备货单小标签的type=2；备货单的扫描小标签type=10
	dataObj.getTemplateDefaultItem = function (config, cb) {
		const parameter = { action: 'GetTemplateDefaultItem', type: config.type || 1 },
			isLoading = !config.isNoLoading,
			printData = comp.Print.Data,
			keyObj = {
				1: 'kddPrintItem',
				2: 'fhdPrintItem',
				10: 'scanBqPrintItem',
				11: 'scanThdPrintItem',
				12: 'scanbkdPrintItem',
				13: 'scandpdPrintItem',
				14: 'scantmdPrintItem',
				17: 'scancgdPrintItem',
				15: 'scanwdpdPrintItem',
				16: 'scanwtmdPrintItem',
				18: 'rkdPrintItem',
				19: 'ckdPrintItem',
				20: 'thqdPrintItem',
				20: 'zbdPrintItem',
				21: 'zbxpPrintItem',
				23: 'bhbqPrintItem',
			},
			key = keyObj[config.type] || 'templateDefaultItem';
		if (printData[key] && printData[key].length > 0) {
			cb && cb(printData[key]);
			return printData[key];
		}
		let ret = null;
		// 兼容pdd的线上调用默认模版的接口不加打印中心的前缀
		let url = API.get_temp_default_item;
		comp.Print.Data.platform == 'pdd' && (url = '/modeTemplateTag/getTemplateDefaultItem');
		// 判断是否零库存版本,isShowZeroStockVersion为true是零库存版本
		const userInfo = comp.Print.Data.userInfo
		const isShowZeroStockVersion = (userInfo?.version == 2 && userInfo?.versionType == 1)

		isLoading && Tatami.showLoading({
			key: 'print'
		});
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: url,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				isLoading && Tatami.clearShow('print');
				data = changeData(data);
				if (!data.IsError) {
					ret = data.Data;
					// 零库存版去除货品规格编码
					if (isShowZeroStockVersion && config.type === 10) {
						ret = ret.filter(o => o.Key !== 'goods_code')
					}
					comp.Print.Data[key] = ret;
					cb && cb(ret);
				} else {
					console.error('comp.Print.data.getKddBgImgBy is error' + data.Msg);
					alert('获取模版右侧标签出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				isLoading && Tatami.clearShow('print');
				console.error('comp.Print.data.getKddBgImgBy is error！' + (textStatus || errorThrown));
				alert('获取模版右侧标签出错');
			},
		});
		return ret;
	};

	//模版右侧标签::Promise
	dataObj.getTemplateDefaultItem_promise = function (config) {
		return new Promise((resolve, reject) => {
			const parameter = { action: 'GetTemplateDefaultItem', type: config.type || 1 },
				isLoading = !config.isNoLoading,
				printData = comp.Print.Data,
				key = config.type == 2 ? 'bqPrintItem' : 'templateDefaultItem';
			if (printData[key] && printData[key].length > 0) {
				resolve(printData[key]);
				return printData[key];
			}
			let ret = null;
			isLoading && Tatami.showLoading({
				key: 'print'
			});
			$.ajax({
				type: 'post',
				dataType: 'json',
				url: API.get_temp_default_item,
				data: parameter,
				headers: getHeaderToken(),
				success: function (data) {
					isLoading && Tatami.clearShow('print');
					data = changeData(data);
					if (!data.IsError) {
						ret = data.Data;
						comp.Print.Data[key] = ret;
						resolve(ret);
					} else {
						console.error('comp.Print.data.getKddBgImgBy is error' + data.Msg);
						alert('获取模版右侧标签出错');
					}
				},
				error: function (request, textStatus, errorThrown) {
					isLoading && Tatami.clearShow('print');
					console.error('comp.Print.data.getKddBgImgBy is error！' + (textStatus || errorThrown));
					alert('获取模版右侧标签出错');
					reject();
				},
			});
			// return ret;
		});
	};

	//获取面单类型
	dataObj.getKddTypeByExCode = function (companyId, callback) {
		const parameter = { action: 'GetKddTypeByExCode', companyId: companyId };
		const url = API.get_kddTypeByExcode
		parameter.exuserId = comp.Print.Data.exuid;

		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeList/getKddTypeByExCode',
			url: url,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					// 供应商根据订单平台来筛选能创建的模板
					if (comp.Print.Data.platform === 'supplier') {
						const filterMap = {
							PDD: [3, 8],
						};
						data.Data = data.Data.filter(item => !(filterMap[window.$$supplierPlat] || filterMap[window.location.hostname] || []).includes(item));
					}
					if (comp.Print.Data.platform === 'fxg') {
						// 【临时方案】放心购区分环境；fxgg、fxg(前端下线 菜鸟、拼多多电子面单)，fxgg2 保持非抖音电子面单可选（与原来一致）[不弹窗，不去掉3.7]
						const FXG = 'fxg.kuaidizs.cn';
						const FXGG = 'fxgg.kuaidizs.cn';
						const filterMap = {
							PDD: [3, 8],
							[FXG]: [3, 7],
							[FXGG]: [3, 7],
						};
						data.Data = data.Data.filter(item => !(filterMap[window.location.hostname] || []).includes(item));
					}
					callback(data.Data);
				} else {
					console.error('comp.Print.data.getKddTypeByExCode is error' + data.Msg);
					alert('获取面单类型出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getKddTypeByExCode is error！' + (textStatus || errorThrown));
				alert('获取面单类型出错');
			},
		});
	};

	//获取业务类型
	dataObj.getKddWorkType = function (parameter, callback) {
		// var parameter = { action: "GetKddWorkType", excode: excode,kddtype:kddtype };
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeList/getKddServiceType',
			url: API.get_kddServiceType,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.getKddWorkType is error' + data.Msg);
					Tatami.showFail('获取业务类型出错:' + (data.Msg || ''));
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getKddWorkType is error！' + (textStatus || errorThrown));
				Tatami.showFail('获取业务类型出错,请稍后再试，或联系客服' + (textStatus || errorThrown));
			},
		});
	};

	//获取服务类型
	dataObj.getKddServiceType = function (excode, kddtype, styleId, callback) {
		const parameter = { action: 'GetKddServiceType', excode: excode, kddtype: kddtype, styleId: styleId };
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeList/getKddServiceItem',
			url: API.get_kddServiceItem,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.getKddServiceType is error' + data.Msg);
					Tatami.showFail('获取服务类型出错,' + (data.Msg || ''));
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getKddServiceType is error！' + (textStatus || errorThrown));
				Tatami.showFail('获取服务类型出错,' + (textStatus || errorThrown));
			},
		});
	};

	//获取增值服务类型
	dataObj.getExAdvancedServices = function (parameter, callback) {
		// var parameter = { action: "GetExAdvancedServices", excode: excode };
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeList/getExAdvancedServices',
			url: API.get_kddExAdvancedSerices,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.getExAdvancedServices is error' + data.Msg);
					Tatami.showFail('获取增值服务类型出错，' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getExAdvancedServices is error！' + (textStatus || errorThrown));
				Tatami.showFail('获取增值服务类型出错，' + (textStatus || errorThrown));
			},
		});
	};

	//获取面单样式
	dataObj.getKddStyle = function (companyId, kddtype, height, callback) {
		const parameter = { action: 'GetKddStyle', companyId: companyId, kddtype: kddtype, height: height };
		const url = API.get_kddStyle

		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeKddStyle/getKddStyle',
			url: url,
			data: extendPrintModeType(parameter),
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.getKddStyle is error' + data.Msg);
					alert('获取面单样式出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getKddStyle is error！' + (textStatus || errorThrown));
				alert('获取面单样式出错');
			},
		});
	};

	//获取面单尺寸
	dataObj.getKddSize = function (companyId, kddtype, callback) {
		const parameter = { action: 'GetKddSize', companyId: companyId, kddtype: kddtype };
		const url = !!isMerge ? API.get_group_getKddSize : API.get_kddSize

		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeList/getKddSize',
			url: url,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.getKddSize is error' + data.Msg);
					alert('获取面单尺寸出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getKddSize is error！' + (textStatus || errorThrown));
				alert('获取面单尺寸出错');
			},
		});
	};
	//获取新增模板组面单尺寸
	dataObj.getHeightPaperByCompanyId = function (companyId, callback) {
		const parameter = { action: 'GetKddSize', exCompanyId: companyId };
		const url = API.get_height_paper_by_companyId

		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeList/getKddSize',
			url: url,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					callback(data.Data);
				} else {
					console.error('comp.Print.data.getKddSize is error' + data.Msg);
					alert('获取面单尺寸出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getKddSize is error！' + (textStatus || errorThrown));
				alert('获取面单尺寸出错');
			},
		});
	};
	//更改是否拼接发货单打印状态
	dataObj.changeConcatState = function (modeListShowId, status, callback) {
		const parameter = { action: 'ChangeConcatState', exShowId: modeListShowId, isConcatFhd: status };
		let requestUrl = comp.Print.Data.platform === 'erp' ? '/print/center/modeTempPrintCfg/changeConcatState' : '/modeTempPrintCfg/changeConcatState'
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: requestUrl,
			data: extendPrintModeType(parameter),
			headers: getHeaderToken(),
			success: function (json) {
				const data = changeData(json);
				if (data.IsError) {
					console.error('comp.Print.data.changeConcatState is error' + data.Msg);
				} else {
					if ($.isFunction(callback)) {
						callback(data);
					}
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.changeConcatState is error！' + (textStatus || errorThrown));
			},
		});
	};
	//快递单绑定交替打印发货单
	dataObj.kddbindrmfhd = function (data, callback) {
		const parameter = data;
		let requestUrl = '/print/center/modeListshow/saveRotationUserTemplate'
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: requestUrl,
			data: extendPrintModeType(parameter),
			headers: getHeaderToken(),
			success: function (json) {
				const data = changeData(json);
				if (data.IsError) {
					console.error('comp.Print.data.changeConcatState is error' + data.Msg);
				} else {
					if ($.isFunction(callback)) {
						callback(data);
					}
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.changeConcatState is error！' + (textStatus || errorThrown));
			},
		});
	};
	// 承诺达可达服务 勾选保存
	dataObj.reachableServiceRequest = function (params) {
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/userCustomSet/saveReacheExtend',
			data: params,
			headers: getHeaderToken(),
			success: function (json) {
				if (json.result != '100') {
					Tatami.showFail('承诺达可达服务保存失败!');
				}
			},
		});
	};
	//获取ModeTempPrintCfg
	// dataObj.getIsConcatFhd = function (modeListShowId, callback) {
	//     var ret = null;
	//     var printData = comp.Print.Data;
	//     var parameter = { action: "GetIsConcatFhd", exShowId: modeListShowId };
	//     $.ajax({
	//         type: "post",
	//         dataType: "json",
	//         url: "/modeTempPrintCfg/getPrintCfgByExshowId",
	//         data: extendPrintModeType(parameter),
	//         async:false,
	//         headers: getHeaderToken(),
	//         success: function (json) {
	//             var data = changeData(json);
	//             if (data.IsError) {
	//                 console.error("comp.Print.data.getIsConcatFhd is error" + data.Msg);
	//             } else {
	//                 printData.printCfg = data.Data;
	//                 ret = data.Data;
	//                 if ($.isFunction(callback)) {
	//                     callback(data);
	//                 }
	//             }
	//         },
	//         error: function (request, textStatus, errorThrown) {
	//             console.error("comp.Print.data.getIsConcatFhd is error！" + (textStatus || errorThrown));
	//         }
	//     });
	//     return ret;
	// }

	//---mm-modify----//
	//获取云栈设置信息 东方：2016.05.18 mm-modify
	dataObj.getYunZhanSetting = function (exuserId, subUserId, excode, modeListShowId, exid, callback, callback2) {
		const printData = comp.Print.Data,
			page = 0 // 当前页码
			, pageSize = 100 // 每页获取的数量
			, type = 0;  //0、获取主店铺和默认店铺; 1、分页获取店铺信息
		const parameter = {
			exId: exid,
			modeListShowId: modeListShowId,
			excode: excode,
			page: page,
			pageSize: pageSize,
			type: type,
		};
		const api = '/dzmdYzDefaultAddress/getYunZhanSetting';
		$.ajax({
			type: 'get',
			dataType: 'json',
			url: api,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				//先渲染第一次获取的主店铺和默认店铺云栈

				data = changeData(data);
				data.Data.Users && $.each(data.Data.Users, function (i, v1) {
					v1 && v1.WangDianList && $.each(v1.WangDianList, function (j, v2) {
						v2 && v2.AddressList && $.each(v2.AddressList, function (k, v3) {
							v3 && (v3.WaybillAddressId = encodeURIComponent(v2.SellerId + (v3.BranchCode || '') + (v3.SendProvince || '') + (v3.SendCity || '') + (v3.SendDistrict || '') + (v3.SendAddress || '')));
						});
					});
				});
				printData.yunzhanSetting = data.Data;

				const firstData = data;
				const firstStepUser = data.Data.Users || [];
				const firstStepSuccessUserList = [], firstStepFailUserList = [];

				for (let i = 0; i < firstStepUser.length; i++) { // 最多有两项，最少有一项
					if (firstStepUser[i].isSuccess) {
						firstStepSuccessUserList.push(firstStepUser[i]);
					} else {
						firstStepFailUserList.push(firstStepUser[i]);
					}
				}

				data.Data.Users = firstStepSuccessUserList;

				if (data.Data.Users.length > 0) {
					if ($.isFunction(callback)) {
						callback(data, exuserId, subUserId, modeListShowId, exid);
					}
				}

				if ($.isFunction(callback2)) {
					// 进度加载
					callback2(0, modeListShowId);
				}

				// 开多个线程获取其他关联店铺的云栈

				const size = data.Data.size;
				const threadNum = Math.ceil((size - firstStepUser.length) / pageSize); // 需要完成的线程数
				let dataList = []; //存放获取到的店铺
				let hasFinishThreadNum = 0; //已经完成了的线程数
				let progress = 0; //进度

				const maxThreadLimit = 5; //最大并发数
				let currentIndex = 0; //并发了多少次
				let counter = 0 // 单词并发的计数器
					;
				(function requestApi() {

					if ((counter == 0 || counter == maxThreadLimit) && hasFinishThreadNum < threadNum) { //
						counter = 0;
						currentIndex += 1;

						const surplusThreadNum = threadNum - hasFinishThreadNum;

						for (var i = 0; i < Math.min(maxThreadLimit, surplusThreadNum); i++) {
							$.ajax({
								type: 'get',
								dataType: 'json',
								url: api,
								data: $.extend(parameter, { page: maxThreadLimit * (currentIndex - 1) + i, type: 1 }),
								headers: getHeaderToken(),
								success: function () {
								},
								error: function (request, textStatus, errorThrown) {
									console.error('comp.Print.data.getYunZhanSetting is error！' + (textStatus || errorThrown));
								},
								complete: function (res, textStatus) {
									console.log(modeListShowId);
									counter++; // 本次请求完成，计数器加一
									const resData = res.responseJSON.data;
									const shopCode = resData.ShopCode;

									resData.Users && $.each(resData.Users, function (i, v1) {
										v1 && v1.WangDianList && $.each(v1.WangDianList, function (j, v2) {
											v2 && v2.AddressList && $.each(v2.AddressList, function (k, v3) {
												v3 && (v3.WaybillAddressId = encodeURIComponent(v2.SellerId + (v3.BranchCode || '') + (v3.SendProvince || '') + (v3.SendCity || '') + (v3.SendDistrict || '') + (v3.SendAddress || '')));
											});
										});
									});

									dataList = dataList.concat(resData.Users);
									hasFinishThreadNum += 1;
									progress = hasFinishThreadNum / threadNum; // 关联店铺的加载进度

									if (hasFinishThreadNum === threadNum) { //全部完成

										//对所有数据进行排序,把成功的放在前面
										const successList = []
											, failList = firstStepFailUserList || []; //如果第一步有失败的，直接塞入失败的List
										for (i in dataList) {
											if (dataList[i].isSuccess) {
												successList.push(dataList[i]);
											} else if (dataList[i].isSuccess == false) {
												failList.push(dataList[i]);
											}
										}
										const secondStepUsers = successList.concat(failList);

										printData.yunzhanSetting.Users = firstStepUser.concat(secondStepUsers);

										const model = {
											Data: {
												ShopCode: shopCode,
												Users: firstStepSuccessUserList.concat(secondStepUsers),
											},
											IsError: (res.responseJSON.result != 100),
											Msg: (resData.message || ''),
										};

										if ($.isFunction(callback)) {
											callback(model, exuserId, subUserId, modeListShowId, exid);
										}
									} else { // 未完成，还在继续请求数据
										if ($.isFunction(callback2)) {
											// 进度加载
											callback2(progress, modeListShowId);
										}

									}

									requestApi();
								},
							});
						}
					} else if (counter == 0) {

						if (firstStepFailUserList.length > 0) {
							firstData.Data.Users = firstStepSuccessUserList.concat(firstStepFailUserList);
							if ($.isFunction(callback)) {
								callback(firstData, exuserId, subUserId, modeListShowId, exid);
							}
						}
						if ($.isFunction(callback2)) {
							// 进度加载
							callback2(100, modeListShowId);
						}
					}
				})();

			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getYunZhanSetting is error！' + (textStatus || errorThrown));
				//alert("获取云栈设置信息出错");
			},
		});
	};

	//获取用户的云栈设置信息 东方：2016.05.18 mm-modify
	dataObj.getYunZhanUserSetting = function (exuserId, subUserId, modeListShowId, exid, callback) {
		const printData = comp.Print.Data;
		const parameter = {
			modeListShowId: modeListShowId,
			exId: exid,
		};
		$.ajax({
			type: 'get',
			dataType: 'json',
			url: API.get_defaultAddress_cn,
			// url: '/dzmdYzDefaultAddress/getYunZhanUserSetting',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				const d = data.data;
				if (data.result == 100 || data.result == 102 && d) {
					if (d) {
						d.AddressId = encodeURIComponent(d.Exuserid + (d.Branch_code || '') + (d.F_p || '') + (d.F_c || '') + (d.F_q || '') + (d.F_addr || ''));
					}
					printData.yunzhanUserSetting = d;
					if ($.isFunction(callback)) {
						callback();
					}
				} else {
					console.error('comp.Print.data.getYunZhanUserSetting is error' + data.message);
					alert(data.message || '获取云栈设置信息出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.getYunZhanUserSetting is error！' + (textStatus || errorThrown));
				alert('获取云栈设置信息出错：' + (textStatus || errorThrown));
			},
		});
	};

	//保存云栈网点店铺代码信息  mm-modify
	dataObj.saveYunZhanShopCode = function (opt, callback) {
		console.log('保存云栈网点店铺');
		const { modeListShowId, shopCode, exId, type } = opt;

		let url, parameter;

		if (type === 'pddWd') {
			url = '/pddDzmdLinkShop/savePddElecShopCode';
			parameter = { templateId: modeListShowId, linkCode: shopCode };
		} else {
			// TODO
			const isDy = comp.base.isDYTemp();
			// 是美团电子面单
			const isTHH = comp.base.isTHHTemp();
			const isKs = comp.base.isKsTemp();
			const isDyOut = ['nzw'].includes(comp.Print.Data.platform);

			if (isDy || isTHH || isKs) {
				parameter = { templateId: modeListShowId, linkCode: shopCode, exId };
			} else {
				parameter = { modeListShowId: modeListShowId, linkCode: shopCode };
			}

			if (isDy) {
				url = API.save_wd_linkCode_dy;
			} else if (isKs) {
				url = API.save_wd_linkCode_ks;
			} else if (isDyOut) {
				url = API.save_wd_linkCode_dy_out;
			} else {
				url = '/dzmdYzLinkShop/saveYunZhanShopCode';
			}
		}

		$.ajax({
			type: 'post',
			dataType: 'json',
			url: url,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				if (data.result == 100) {
					if ($.isFunction(callback)) {
						callback(data);
					}
				} else {
					console.error('comp.Print.data.saveYunZhanShopCode is error' + data.message);
					alert(data.message || '保存云栈网点店铺代码信息出错,请刷新重试');
					callback(data);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveYunZhanShopCode is error！' + (textStatus || errorThrown));
				alert('保存云栈网点店铺代码信息出错,请刷新重试');
			},
		});
	};

	//删除云栈店铺代码      mm-modify
	dataObj.deleteYunZhanShopCode = function (exuserId, modeListShowId, exId, callback) {
		let parameter, url;

		if (comp.Print.Data.platform === 'pdd') {
			parameter = {
				templateId: modeListShowId,
			};
			url = API.del_pdd_shopcode;
		} else {
			const isDy = comp.base.isDYTemp();
			const isKs = comp.base.isKsTemp();
			parameter = (isDy | isKs) ? { templateId: modeListShowId, exId: exId }
				: { modeListShowId: modeListShowId, exId: exId };

			if (isDy) {
				url = API.delete_wd_linkCode_dy;
			} else if (isKs) {
				url = API.delete_wd_linkCode_ks;
			} else {
				url = '/dzmdYzLinkShop/deleteYunZhanShopCode';
			}
		}

		$.ajax({
			type: 'post',
			dataType: 'json',
			url: url,
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				if (data.result == 100) {
					if ($.isFunction(callback)) {
						callback(data);
					}
				} else {
					console.error('comp.Print.data.deleteYunZhanShopCode is error' + data.Msg);
					alert('删除云栈网点店铺代码信息出错,请刷新重试');
					callback(data);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.deleteYunZhanShopCode is error！' + (textStatus || errorThrown));
				alert('删除云栈网点店铺代码信息出错,请刷新重试');
			},
		});
	};

	//删除用户的云栈设置信息 东方：2016.05.18   mm-modify
	dataObj.deleteYunZhanSetting = function (exuserId, subUserId, modeListShowId, exid, callback) {
		const parameter = { modeListShowId: modeListShowId, exId: exid };
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/dzmdYzDefaultAddress/deleteYunZhanSetting',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				const ndata = changeData(data);
				if (!ndata.IsError) {
					if ($.isFunction(callback)) {
						callback(ndata);
					}
				} else {
					console.error('comp.Print.data.deleteYunZhanSetting is error' + ndata.Msg);
					alert('删除用户的云栈设置信息出错,请刷新重试');
					callback(ndata);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.deleteYunZhanSetting is error！' + (textStatus || errorThrown));
				alert('删除用户的云栈设置信息出错,请刷新重试');
			},
		});
	};

	//获取面单云栈授权方式
	dataObj.getYunZhanMethod = function ({ modeListShowId, exid }, successCb, failCb) {
		let api = API.get_wd_auth_type_cn;
		if (comp.Print.Data.platform == 'erp') {
			if (comp.base.isCNTemp()) { // 目前erp仅菜鸟与拼多多面单支持多种授权方式
				api = API.get_wd_auth_type_erp_cn;
			} else if (comp.base.isPWTemp()) {
				api = API.get_wd_auth_type_erp_pdd;
			} else if (comp.base.isDYTemp()) {
				api = API.get_dy_read_method;
			} else if (comp.base.isKsTemp()) {
				api = API.get_ks_read_method;
			} else if (comp.base.isXhsTemp() || comp.base.isNewXhsTemp()) {
				api = API.get_xhs_read_method;
			} else if (comp.base.isSphTemp() || comp.base.isJDTemp()) {
				api = API.get_user_template_authMethod;
			} else {
				successCb({ method: YUNZHAN_AUTH_TYPE['ERP_SHOP'] }); // ERP 其他面单暂时默认是绑定店铺授权 3
				return;
			}
		} else if (comp.base.isPWTemp()) {
			api = API.get_wd_auth_type_pwdz;
		} else if (comp.base.isDYTemp() || comp.base.isTHHTemp()) { // 美团跟抖音一样
			if (['nzw'].includes(comp.Print.Data.platform)) {
				// 女装网
				api = API.get_wd_auth_type_dwdz;

			} else {
				// 抖音目前不需要调用这个接口，因此，造一个假数据返回即可.
				const mockData = {
					method: 1,
				};
				successCb(mockData);
				return;
			}

			// api = API.get_wd_auth_type_pwdz;
		}
		$.ajax({
			type: 'get',
			dataType: 'json',
			url: api,
			data: { userTemplateId: modeListShowId, exid },
			headers: getHeaderToken(),
			success: function (json) {
				if (json.result == 100) {
					if (!json.data) json.data = { method: 3 }
					successCb && successCb(json.data);
				} else {
					failCb && failCb();
					alert('获取云栈设置方式出错:' + json.message);
				}
			},
			error: function (request, textStatus, errorThrown) {
				failCb && failCb();
				alert('获取云栈设置方式出错' + (textStatus || errorThrown));
			},
		});
	};

	//更新淘外菜鸟读取方式
	dataObj.setYunZhanMethod = function (params, cb) {
		const isPw = comp.base.isPWTemp(),
			isXhsTemp = comp.base.isXhsTemp(), // 是拼外电子模版
			isDY = comp.base.isDYTemp(),// 是抖音电子模板
			isKs = comp.base.isKsTemp(),// 快手电子模板
			isNewXhsTemp = comp.base.isNewXhsTemp(), // 是拼外电子模版
			isSph = comp.base.isSphTemp(), // 是拼外电子模版
			isJd = comp.base.isJDTemp(); // 是拼外电子模版

		let api = API.update_wd_auth_type_cn;

		if (comp.Print.Data.platform == 'erp') {
			api = API.update_wd_auth_type_erp_cn;
			if (isPw) api = API.update_wd_auth_type_erp_pdd;
			if (isDY) api = API.update_dy_read_method;
			if (isKs) api = API.update_ks_read_method;
			if (isXhsTemp || isNewXhsTemp) api = API.update_xhs_read_method;
			if (isSph || isJd) api = API.save_user_template_authMethod;
		} else if (isPw) {
			api = API.update_wd_auth_type_pwdz;
		}
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: {
				userTemplateId: params.modeListShowId,
				exid: params.exid,
				exId: params.exId,
				method: params.method,
				expressType: params.expressType,
				exCode: params.exCode
			},
			headers: getHeaderToken(),
			success: function (json) {
				cb && cb(json);
			},
			error: function (request, textStatus, errorThrown) {
				alert('comp.Print.data.getYunZhanSetting is error！' + (textStatus || errorThrown));
				cb && cb({});
			},
		});
	};

	//获取菜鸟网点list
	dataObj.getYunZhanList = function (obj, cb) {
		console.log('获取菜鸟网点list');
		console.info(obj);
		let api,
			params;

		// 拼外
		if (obj.kddType == 7) {
			// erp拼多多面单，接口结构与拼外类似，实际是拼多多，后端做了处理
			if (comp.Print.Data.platform == 'erp') {
				api = API.get_wdInfoList_erp_pdd;
			} else {
				api = API.get_wdInfoList_pwdz;
			}
			params = {
				exid: obj.exid,
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
				method: obj.methodType,
			};
		} else if (obj.kddType == 8 && ['nzw'].includes(comp.Print.Data.platform)) {
			api = API.get_wdInfoList_dy_out;
			params = {
				exid: obj.exid,
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
				method: obj.methodType,
			};
		} else if (obj.kddType == 13) {
			api = API.get_wdInfoList_xhsdz;
			params = {
				exid: obj.exid,
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
				method: obj.methodType,
			};
		} else if (obj.kddType == 14) {
			api = API.get_wdInfoList_erp_sph;
			params = {
				exid: obj.exid,
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
				method: obj.methodType,
			};
		} else if (obj.kddType == 15) {
			api = API.get_wdInfoList_yzdz;
			params = {
				exid: obj.exid,
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
				method: obj.methodType,
			};
		} else if (obj.kddType == 16) {
			api = API.get_wdInfoList_Newxhsdz;
			params = {
				exid: obj.exid,
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
				method: obj.methodType,
			};
		} else {
			// 菜鸟电子面单
			api = comp.Print.Data.platform == 'erp' ? API.get_wdInfoList_erp_cn : API.get_wdInfoList_cn;
			params = {
				exid: obj.exid,
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
				method: obj.methodType,
			};
		}
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: params,
			headers: getHeaderToken(),
			success: function (json) {
				// 先洗一下数据，保持结构和拼外一致，方便处理
				if (obj.kddType == 8) {
					try {
						const data = json.data.data;
						data.forEach(d => {
							d.branchList = d.branchList.map((b) => {
								return {
									branchAccountCols: [
										{
											allocatedQuantity:
												b.AllocatedQuantity,
											branchCode: b.BranchCode,
											branchName: b.BranchName,
											cancelQuantity: b.CancelQuantity,
											cpCode: b.CpCode,
											printQuantity: b.PrintQuantity,
											quantity: b.Quantity,
											sellerId: b.SellerId,
											shippAddressCols: b.AddressList.map(
												(a) => ({
													branchCode: a.BranchCode,
													detail: a.SendAddress,
													city: a.SendCity,
													district: a.SendDistrict,
													mobile: a.SendMobile,
													name: a.SendName,
													phone: a.SendPhone,
													province: a.SendProvince,
													town: a.SendTown,
													zip: a.SendZip,
													...a,
												}),
											),
											...b,
										},
									],
									cpType: b.CpType,
								};
							});
						});
					} catch (error) {
						console.log('洗数据失败', error);
					}
				}
				cb && cb(json);
			},
			error: function (request, textStatus, errorThrown) {
				console.error('获取网点列表出错' + (textStatus || errorThrown));
				cb && cb({});
			},
		});
	};

	dataObj.getPddWdList = function (obj, cb) {
		const api = API.get_wd_auth_type_pdd,
			params = {
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
			};
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: params,
			headers: getHeaderToken(),
			success: function (json) {
				cb && cb(json);
			},
			error: function (request, textStatus, errorThrown) {
				console.error('获取网点列表出错' + (textStatus || errorThrown));
				cb && cb({});
			},
		});
	};

	dataObj.getDYWdList = function (obj, cb) {
		let api,
			params;
		// TODO
		const isDyOut = ['nzw'].includes(comp.Print.Data.platform);
		api = isDyOut ? API.get_wdInfoList_dy_out : API.get_wdInfoList_dydz;
		params = {
			exCode: obj.exCode,
			templateId: obj.modeListShowId,
			method: obj.methodType
		};

		const mock_wdInfo_list = {
			'dyBranchInfoResult': {//商家已开通的网点列表信息
				'netsites': [
					{
						'netsiteCode': '',//快递公司编码
						'netsiteName': '',//网点名称
						'amount': null,//电子面单余额数量
						'senderAddress': [
							{
								'provinceName': '浙江省',
								'cityName': '杭州市',
								'districtName': '西湖区',
								'streetName': '<nil>',
								'detailAddress': '文三西路',
							},
							{
								'provinceName': '河北省',
								'cityName': '唐山市',
								'districtName': '路南区',
								'streetName': '<nil>',
								'detailAddress': '文俊山西大同城区小店地址线下',
							},
						],
					},
				],
				'logisticsCode': 'SF',//快递公司code
				'companyType': '1',//物流服务商业务类型 1：直营  2：加盟 3：落地配 4：直营带网点
			},
			'defaultAddress': {
				'address': '',//详细地址
				'area': '',//区
				'branchAddressId': 0,//网点地址id
				'branchCode': '',//网点编码
				'branchName': '',//网点名称
				'branchUserId': 0,//网点所属用户id
				'city': '',//城市
				'created': 1607656270454,//创建时间
				'enableStatus': 0,//是否可用，1可0不可
				'exCode': '',//快递code
				'exId': 0,//exid
				'id': 0,//主键id前端没用
				'method': 0,//授权方式，1账号，0店铺代码
				'modified': 1607656270454,//修改时间
				'platformName': '',//平台
				'province': '',//省
				'shopName': '',//店铺名称
				'templateId': 0,//模版id
				'town': '',//街道
				'userId': 0,//用户ID
			},
			'result': 100,//ok👌
		};

		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: params,
			headers: getHeaderToken(),
			success: function (json) {
				cb && cb(json);
			},
			error: function (request, textStatus, errorThrown) {
				console.error('获取网点列表出错' + (textStatus || errorThrown));
				cb && cb({});
			},
		});
	};
	// 美团网点列表
	dataObj.getTHHWdList = function (obj, cb) {
		console.log('获取美团网点列表');
		let api,
			params;
		api = API.get_wdInfoList_thh;
		params = {
			exCode: obj.exCode,
			templateId: obj.modeListShowId,
		};

		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: params,
			headers: getHeaderToken(),
			success: function (json) {
				cb && cb(json);
			},
			error: function (request, textStatus, errorThrown) {
				console.error('获取网点列表出错' + (textStatus || errorThrown));
				cb && cb({});
			},
		});
	};

	dataObj.getKsWdList = function (obj, cb) {
		let api,
			params;
		if (comp.Print.Data.platform === 'erp') {
			api = API.get_wdInfoList_ksdz;
			params = {
				exid: obj.exid,
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
				method: obj.methodType,
			};
		} else {
			api = API.get_wdInfoList_ks;
			params = {
				exCode: obj.exCode,
				templateId: obj.modeListShowId,
			};
		}

		const mock_wdInfo_list = {
			'dyBranchInfoResult': {//商家已开通的网点列表信息
				'netsites': [
					{
						'netsiteCode': '',//快递公司编码
						'netsiteName': '',//网点名称
						'amount': null,//电子面单余额数量
						'senderAddress': [
							{
								'provinceName': '浙江省',
								'cityName': '杭州市',
								'districtName': '西湖区',
								'streetName': '<nil>',
								'detailAddress': '文三西路',
							},
							{
								'provinceName': '河北省',
								'cityName': '唐山市',
								'districtName': '路南区',
								'streetName': '<nil>',
								'detailAddress': '文俊山西大同城区小店地址线下',
							},
						],
					},
				],
				'logisticsCode': 'SF',//快递公司code
				'companyType': '1',//物流服务商业务类型 1：直营  2：加盟 3：落地配 4：直营带网点
			},
			'defaultAddress': {
				'address': '',//详细地址
				'area': '',//区
				'branchAddressId': 0,//网点地址id
				'branchCode': '',//网点编码
				'branchName': '',//网点名称
				'branchUserId': 0,//网点所属用户id
				'city': '',//城市
				'created': 1607656270454,//创建时间
				'enableStatus': 0,//是否可用，1可0不可
				'exCode': '',//快递code
				'exId': 0,//exid
				'id': 0,//主键id前端没用
				'method': 0,//授权方式，1账号，0店铺代码
				'modified': 1607656270454,//修改时间
				'platformName': '',//平台
				'province': '',//省
				'shopName': '',//店铺名称
				'templateId': 0,//模版id
				'town': '',//街道
				'userId': 0,//用户ID
			},
			'result': 100,//ok👌
		};

		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: params,
			headers: getHeaderToken(),
			success: function (json) {
				cb && cb(json);
			},
			error: function (request, textStatus, errorThrown) {
				console.error('获取网点列表出错' + (textStatus || errorThrown));
				cb && cb({});
			},
		});
	};

	dataObj.saveLinkCode = function (params, cb) {
		const isPw = comp.base.isPWTemp();
		const isDy = comp.base.isDYTemp();
		const isKs = comp.base.isKsTemp();

		let api = API.save_wd_linkCode_cn;
		//TODO
		if (isDy) {
			const isDyOut = ['nzw'].includes(comp.Print.Data.platform);

			api = isDyOut ? API.save_wd_linkCode_dy_out : API.save_wd_linkCode_dy;
		}

		if (isKs) {
			api = API.save_wd_linkCode_ks;
		}

		if (isPw) {
			api = API.save_wd_linkCode_pwdz;
		}

		this.requestAPI({
			url: api,
			params: params,
		}, function (json) {
			if (json.result == 100) {
				cb && cb(json.data);
			} else {
				Tatami.showFail('店铺代码保存出错:' + (json.message || '请稍后再试，或联系客服'));
			}

		}, function (d) {
			Tatami.showFail('店铺代码保存出错：' + (d || '请稍后再试，或联系客服'));
		});
	};

	//获取JD青龙网点list和库存
	dataObj.getJDYunZhanList = function (obj, cb) {
		let params = {
			exid: obj.exid,
			exCode: obj.exCode,
			templateId: obj.modeListShowId,
			method: obj.methodType,
		};
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: API.get_wdInfoList_erp_jd,
			data: params,
			headers: getHeaderToken(),
			success: function (data) {
				//数据处理
				cb && cb(data);
			},
			error: function (request, textStatus, errorThrown) {
				console.error('获取JD青龙网点列表出错' + (textStatus || errorThrown));
				cb && cb({});
			},
		});
	};

	dataObj.saveJDYunZhanSet = function (params, cb) {
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/print/center/jdCloudPrint/updateDefaultAddress',
			data: params,
			headers: getHeaderToken(),
			success: function (json) {
				if (json.result == 100) {
					cb && cb(json.data);
				}
			},
			error: function (request, textStatus, errorThrown) {
				alert('保存JD青龙云栈信息出错：' + (textStatus || errorThrown));
			},
		});
	};

	dataObj.savePddWdSet = function (params, cb, failCb) {
		const api = comp.Print.Data.platform === 'erp' ? API.save_wd_defaultAddress_pwdz_erp : API.save_default_addr_type_pdd;
		this.requestAPI({
			url: api,
			params: params,
		}, function (json) {
			if (json.result == 100) {
				cb && cb(json.data);
			} else {
				failCb && failCb();
				Tatami.showFail('保存默认网点出错：' + json.message);
			}
		}, function (d) {
			failCb && failCb();
			Tatami.showFail('保存默认网点出错：' + d);
		});
	};

	dataObj.saveYunZhanSet = function (params, cb) {
		const isErp = comp.Print.Data.platform == 'erp';
		let api;
		if (params.kddType == 7) {
			if (isErp) {
				api = API.save_wd_defaultAddress_pwdz_erp;
			} else {
				api = API.save_wd_defaultAddress_pwdz;
			}
		} else if (params.kddType == 8) {
			// TODO
			const isDyOut = ['nzw'].includes(comp.Print.Data.platform);
			api = isDyOut ? API.save_wd_defaultAddress_yd_out : API.save_wd_defaultAddress_yd;
		} else if (params.kddType == 9) {
			api = isErp ? API.save_wd_defaultAddress_erp_ks : API.save_wd_defaultAddress_ks;
		} else if (params.kddType == 10) { // 美团
			api = API.save_mtDefaultAddress_thh;
		} else if (params.kddType == 13) {
			api = API.save_xhs_defaule_address_erp
		} else if (params.kddType == 14) {
			api = API.sace_wdInfo_erp_sph
		} else if (params.kddType == 15) {
			api = API.save_yz_defaule_address_erp
		} else if (params.kddType == 16) {
			api = API.save_xhs_defaule_address_erp
		} else {
			if (isErp) {
				api = API.save_wd_defaultAddress_erp_cn;
			} else {
				api = API.save_wd_defaultAddress_cn;
			}
		}
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: params,
			headers: getHeaderToken(),
			success: function (json) {
				if (json.result == 100) {
					cb && cb(json.data);
				} else {
					cb && cb('error');
					Tatami.showFail('保存云栈信息出错：' + json.message);
				}
			},
			error: function (request, textStatus, errorThrown) {
				cb && cb('error');
				Tatami.showFail('保存云栈信息出错：' + (textStatus || errorThrown));
			},
		});
	};

	//删除账号
	dataObj.deleteAccount = function (param, cb) {
		let api = API.delete_wd_auth_cn;

		if (comp.Print.Data.platform == 'erp') {
			api = API.delete_wd_auth_erp_cn;
		}
		if (param.kddType == 7) {
			api = API.delete_wd_auth_pwdz;
		}

		delete param.kddType;

		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: param,
			headers: getHeaderToken(),
			success: function (json) {
				cb && cb(json);
			},
			error: function (request, textStatus, errorThrown) {
				alert('删除账号' + (textStatus || errorThrown));
			},
		});
	};

	//授权刷新
	dataObj.refreshAuthorization = function (arg, cb) {
		let api = API.refresh_Author_cn;
		let params = {
			exid: arg.exid,
		};
		if (comp.Print.Data.platform === 'erp') {
			switch (arg.kddType) {
				case 7:
					api = API.refresh_Author_erp_pdd;
					break;
				default:
					api = API.refresh_Author_erp_cn;
			}

		} else if (arg.kddType == 7) {
			api = API.refresh_Author_pwdz;
		}
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: api,
			data: params,
			headers: getHeaderToken(),
			success: function (json) {
				cb && cb(json);
			},
			error: function (request, textStatus, errorThrown) {
				alert('刷新授权失败' + (textStatus || errorThrown));
			},
		});
	};


	//保存用户的云栈设置信息 东方：2016.05.18  mm-modify
	dataObj.saveYunZhanSetting = function (obj, callback) {
		const printData = comp.Print.Data;
		const parameter = { jsonParam: JSON.stringify(obj) };
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/dzmdYzDefaultAddress/saveYunZhanSetting',
			data: parameter,
			headers: getHeaderToken(),
			success: function (data) {
				if (data.result == 100) {
					printData.yunzhanUserSetting = obj;
					if ($.isFunction(callback)) {
						callback(data);
					}
				} else {
					console.error('comp.Print.data.saveYunZhanSetting is error' + data.message);
					alert('保存用户的云栈设置信息出错,请刷新重试');
					callback(data);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveYunZhanSetting is error！' + (textStatus || errorThrown));
				alert('保存用户的云栈设置信息出错,请刷新重试');
			},
		});
	};


	//获取网点电子面单的设置信息 东方：2016.05.18
	dataObj.getBranchSetting = function (exid, exuserId, subUserId, modeListShowId, exCode, callback) {
		const printData = comp.Print.Data;
		const parameter = { action: 'GetBranchSetting', exId: exid, exuserId: exuserId, subUserId: subUserId, templateId: modeListShowId, modeType: 0 };
		$.ajax({
			type: 'get',
			dataType: 'json',
			// url: '/dzmdWdUseSet/getBranchSetting',
			url: API.get_branchSetting,
			data: extendPrintModeType(parameter),
			headers: getHeaderToken(),
			success: function (json) {
				const data = changeData(json);
				if (!data.IsError || json.result == 701) {
					if (json.result == 701) {
						data.Msg += 'Invalid session';
					}
					printData.netPointSetting = data.Data;
					if ($.isFunction(callback)) {
						callback(data, exCode, exid);
					}
				} else {
					console.error('comp.Print.data.getBranchSetting is error' + data.Msg);
					data.Msg = data.Msg || '';
					if (data.Msg.indexOf('Invalid session') > -1 || data.Msg.indexOf('授权已失效') > -1) {
						alert('获取电子面单设置信息出错，授权已过期');
					} if (data.Msg.indexOf('获取发货地址失败') > -1) {
						alert('获取电子面单信息出错，请前往基础设置设置发件人信息或者淘宝后台地址库设置默认发货地址。');
					} else {
						alert(data.Msg || '获取电子面单设置信息出错');
					}
				}
			},
			error: function (request, textStatus, errorThrown) {
				const m = textStatus || errorThrown || '';
				if (m.indexOf('Invalid session') > -1 || m.indexOf('授权已失效') > -1) {
					alert('获取电子面单设置信息出错，授权已过期');
				} if (m.indexOf('获取发货地址失败') > -1) {
					alert('获取电子面单信息出错，请前往基础设置设置发件人信息或者淘宝后台地址库设置默认发货地址。');
				} else {
					alert(m || '获取电子面单设置信息出错');
				}
				console.error('comp.Print.data.getBranchSetting is error！' + m);
				// alert("获取网点电子面单的设置信息出错");
			},
		});
	};

	//保存网点电子面单的设置信息 东方：2016.05.20
	dataObj.saveBranchSetting = function (obj, callback) {
		const printData = comp.Print.Data;
		const parameter = { action: 'SaveBranchSetting', jsonParam: JSON.stringify(obj), modeType: 0 };
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/dzmdWdUseSet/saveBranchSetting',
			url: API.save_branchSetting,
			data: extendPrintModeType(parameter),
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					printData.netPointSetting = obj;
					if ($.isFunction(callback)) {
						callback();
						Tatami.pub('printCenter.noTempUpdateTemp', {
							wdUseCache: true,
						});
					}
				} else {
					console.error('comp.Print.data.saveBranchSetting is error' + data.Msg);
					Tatami.showFail('保存网点电子面单的设置信息出错：' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveBranchSetting is error！' + (textStatus || errorThrown));
				Tatami.showFail('保存网点电子面单的设置信息出错：' + (textStatus || errorThrown));
			},
		});
	};

	//编辑模板保存 无忌：2016.05.31
	dataObj.SaveTemplate = function (obj, callback) {
		if (obj.ModeListShow.Modeid == 'kdd') {
			dataObj.SaveKddTemplate(obj, callback);
		} else if (obj.ModeListShow.Modeid == 'fhd') {
			dataObj.SaveFhdTemplate(obj, callback);
		} else {
			dataObj.SaveXbqTemplate(obj, callback);
		}
	};

	//编辑模板保存 无忌：2016.05.31
	dataObj.SaveKddTemplate = function (obj, callback) {
		let index,
			eventObj,
			that = new comp.Print.FN();
		$.ajax({
			type: 'post',
			dataType: 'json',
			// url: '/modeListshow/saveTemplate',
			url: API.save_template,
			data: { action: 'SaveTemplate', jsonParam: JSON.stringify(obj) },
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					index = comp.Print.Data.kddTempInfos.findIndex(function (item) {
						if (item.ModeListShow.Mode_ListShowId == obj.ModeListShow.Mode_ListShowId) {
							return true;
						}
						return false;
					});
					if (index >= 0) {
						const arr = comp.Print.Data.kddTempInfos;
						const oldObj = arr[index];
						obj.modified = new Date().getTime()
						arr.splice(index, 1, obj);
						const modeName = obj.ModeListShow.ExcodeName;
						const modeId = obj.ModeListShow.Mode_ListShowId;

						const list = that.getKddList();
						index = list.findIndex(function (item) {
							return item.Mode_ListShowId == modeId;
						});
						const lItem = list[index];
						if (lItem.ExcodeName != modeName) {
							lItem.ExcodeName = modeName;
							// eventObj = comp.Print.eventObj;
							// let evs = eventObj.KddTemplateListChanged;
							// if (evs) {
							//     evs.each(function (_index, item) {
							//         item();
							//     });
							// }
						}
					} else {
						console.error('application is error ,because of it fined ,but not find');
					}
					if ($.isFunction(callback)) {
						callback();
					}
					eventObj = comp.Print.eventObj;
					eventObj.dataChanged && eventObj.dataChanged.each(function (_index, item) {
						item('SaveKddTemplate');
					});
				} else {
					console.error('comp.Print.data.saveEditModel is error' + data.Msg);
					alert('保存编辑模版出错');
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.saveEditModel is error！' + (textStatus || errorThrown));
				alert('保存编辑模版出错');
			},
		});
	};

	//发货单保存
	dataObj.SaveFhdTemplate = function (obj, callback) {

		let index,
			eventObj,
			that = new comp.Print.FN();
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/print/center/smallTag/saveBhdTemplate',
			data: { action: 'SaveFhdTemplate', jsonParam: JSON.stringify(obj) },
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					let index = comp.Print.Data.fhdTempInfos.findIndex(function (item) {
						if (item.ModeListShow.Mode_ListShowId == obj.ModeListShow.Mode_ListShowId) {
							return true;
						}
						return false;
					});
					if (index >= 0) {
						const arr = comp.Print.Data.fhdTempInfos;
						arr.splice(index, 1, obj);
						(comp.Print.Data.globalSettingFhd || {}).ModeSet = obj.ModeSet;

						arr.splice(index, 1, obj);
						const modeName = obj.ModeListShow.ExcodeName;
						const modeId = obj.ModeListShow.Mode_ListShowId;

						const fhdTemplateList = that.getFhdTemplateList();
						const list = fhdTemplateList.ModeListShows;
						index = list.findIndex(function (item) {
							return item.Mode_ListShowId == modeId;
						});
						const lItem = list[index];
						if (lItem.ExcodeName != modeName) {
							lItem.ExcodeName = modeName;
							eventObj = comp.Print.eventObj;
							const evs = eventObj.FhdTemplateListChanged;
							if (evs) {
								evs.each(function (_index, item) {
									item();
								});
							}
						}
					}
					callback && callback();
				} else {
					console.error('comp.Print.data.SaveFhdTemplate is error' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.SaveFhdTemplate is error！' + (textStatus || errorThrown));
			},
		});
		//保存 发货单 日志
		// dataObj.addSaveFhdLog(obj);
	};
	//小标签保存
	dataObj.SaveXbqTemplate = function (obj, callback) {

		let index,
			eventObj,
			that = new comp.Print.FN();
		let type = obj.ModeListShow.Modeid
		const params = {
			action: 'SaveTemplate',
			// templateType : obj.ModeListShow.ExCode,
			jsonParam: JSON.stringify(obj),
		}
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: '/print/center/smallTag/saveBhdTemplate',
			data: params,
			headers: getHeaderToken(),
			success: function (data) {
				data = changeData(data);
				if (!data.IsError) {
					if (['bq', 'thd', 'bhd', 'bkd', 'dpd', 'tmd', 'cgd', 'rkd', 'ckd', 'thqd', 'zbd', 'zbxp'].includes(type)) {
						let tempList = comp.Print.Data['bqTemplateList' + type]?.ModeListShows || []
						if (type === 'bkd') {
							tempList = comp.Print.Data['bqTemplateListbhd']?.ModeListShows || []
						}
						if (type === 'zbxp') {
							tempList = comp.Print.Data['bqTemplateListzbd']?.ModeListShows || []
						}
						for (let i = 0; i < tempList.length; i++) {
							if (tempList[i].Mode_ListShowId === obj?.ModeListShow?.Mode_ListShowId) tempList[i] = obj?.ModeListShow
						}
					}
					callback && callback();
				} else {
					console.error('comp.Print.data.SaveFhdTemplate is error' + data.Msg);
				}
			},
			error: function (request, textStatus, errorThrown) {
				console.error('comp.Print.data.SaveFhdTemplate is error！' + (textStatus || errorThrown));
			},
		});
		//保存 发货单 日志
		// dataObj.addSaveFhdLog(obj);
	};


	//获取模拟数据
	dataObj.getPrintData = function (modeid) {
		const obj = new comp.print.desc.PrintData();
		let systemSetting = window.erpData?.systemSetting || {};
		if (systemSetting?.itemCustomAttribute) {
			let customTempSetting = systemSetting?.itemCustomAttributeDTOList || []
			customTempSetting.forEach(o => obj[o.key] = o.name)
		}
		// 设置发件人信息
		obj.f_name = '发件人名称';
		obj.f_tel = '发件人电话';
		obj.f_addr = '发件人地址';
		obj.f_zip = '发件人邮编';
		obj.f_qm = '发件人签名';

		obj.f_ww = '卖家旺旺';
		obj.logow = '卖家旺旺LOGO宽度';
		obj.logoh = '卖家旺旺LOGO高度';
		const date = new Date();

		const yy = date.getFullYear();
		let mm = date.getMonth() + 1;
		let dd = date.getDate();
		mm = mm < 9 ? ('0' + mm) : mm;
		dd = dd < 9 ? ('0' + dd) : dd;
		obj.f_date = yy + '-' + mm + '-' + dd;
		obj.print_time = mm + '-' + dd; //打印时间
		// 发货单时设置发货单信息
		// if (modeid == 'fhd') {
		obj.o_shop = '店铺名称';
		obj.o_title = '发货单标题';
		obj.o_tel = '联系电话';
		obj.o_name = '经办人';
		obj.o_info = '友情提示';
		// }
		// else {
		// 快递单时设置 发货信息
		obj.f_info = '发货内容';
		// }

		// 收件人部分从页面上提取，用户可能做了编辑
		obj.s_name = '收件人姓名';
		obj.s_phone = '13388888888';
		obj.s_tel = '010-12345678';
		obj.s_p = '北京';
		obj.s_city = '北京市';
		obj.s_q = '测试区';
		obj.s_addr = '测试地址测试地址测试地址测试地址测试地址测试地址测试地址';
		obj.s_addrall = '北京市 测试区 测试地址测试地址测试地址测试地址测试地址测试地址测试地址';
		obj.s_zip = '123456';
		obj.s_ww = '买家旺旺';
		obj.xdate = '2000-10-10 12:00:00';
		obj.fdate = '2000-10-10 13:00:05';
		obj.mjs = '满就送内容';
		obj.ssje = '100'
		// 合计_数量
		obj.hj_sl = '10';
		obj.hj_yf = '0.00';
		obj.hj_yh = '65';
		obj.hj_sf = '1200.00';

		obj.txm_tid = '0123456789';
		obj.txm_number = '8011110001';
		obj.ewm_number = '8011110001';    //运单号二维码

		obj.fhdexnumber = '8011110001';
		obj.s_fare = '运费';

		// 临时变量，存储当前地址所有订单中对应的产品数据
		const tempPidAry = new Array();

		// 记录当前所有订单中对应的产品ID，有重复的只记录一个，用来把相同的产品放到一起
		const pidAry = new Array();

		/////将当前地址组中的多个订单中的以下数据项合并，合并前先把数据清除
		obj.count = '0';
		obj.s_tid = '';
		obj.lc = '';
		obj.dsje = '0';
		obj.ddje = '0';
		obj.hkzj = '0';
		obj.bjje = '0';
		obj.bjfy = '0';
		obj.f_memo = '';
		obj.s_message = '';
		obj.tb_zhongl = '0';

		// 圆准达;
		obj.yzd_time = '承诺送达日期: 2019-07-01';

		// 个性目的地  (快递间用英文的;分隔，快递CODE与值间用英文的,分隔) TTKD,值;YTO,值
		obj.gx_mdd = '集包目的地';
		obj.mdd = '北京市 测试区';

		// 个性集包码   (快递间用英文的;分隔，快递CODE与值间用英文的,分隔) TTKD,值;YTO,值
		obj.gx_jbm = '集包码';
		obj.mdd_dzmd = '021D-123';

		// 条形码_集包码  (快递间用英文的;分隔，快递CODE与值间用英文的,分隔) TTKD,值;YTO,值
		obj.txm_jbm = 'AUTO,8011110001';
		// 备注、留言处理
		obj.f_memo = '卖家备注的内容信息';
		obj.trade_remarks_image = '线下备注图片';
		obj.s_message = '买家留言的内容信息';
		obj.s_nick_image = '抖音昵称';
		obj.zb_nick_image = '抖音昵称';
		obj.fh_first_order_id = '平台单号（单个）';
		obj.dy_nick = '抖音昵称';
		// 二维码
		obj.ewm = '二维码';
		//分拣码
		obj.fjm = '分拣码';
		//字母件数量
		obj.zmj_sum = '子件数量';
		obj.bgjs = '1/1';
		//字母件子单号
		obj.zmj_mnum = 'P801234567';
		//月结卡号
		// obj.cutid = "月结单号";
		obj.cutid = '0123456789';
		obj.sfwd = '始发网点';
		obj.ddwd = '到达网点';

		obj.shop_title = '店铺名称';
		obj.shop_abbreviation = '店铺简称';

		obj.sjcm = '012345678912'; //手机串码
		// 中运全速
		obj.sf_fjm = 'B22';
		obj.md_fjm = 'A30';
		obj.sf_hdh = 'P';
		obj.md_hdh = 'C5';
		obj.zyqs_cplx = '次日达';

		// 总数量，读取当前订单中包含的产品项，将每一项产品的数量相加(去掉退款中的数量)
		let newCount = 0;
		for (let j = 0; j < 4; j++) {
			const tb_count = 1;
			// 记录数量
			newCount += tb_count;
			// 记录产品数据
			tempPidAry[tempPidAry.length] = {};
			const temTitJ = j % 2;
			tempPidAry[tempPidAry.length - 1].tb_pid = '90000001' + temTitJ;
			tempPidAry[tempPidAry.length - 1].tb_img = 'https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/img.jpg';
			tempPidAry[tempPidAry.length - 1].tb_tit = '商品的标题' + temTitJ;
			tempPidAry[tempPidAry.length - 1].tb_jc = '商品的简称' + temTitJ;

			const temj = (j + 1) % 3;
			tempPidAry[tempPidAry.length - 1].tb_sjbm_b = 'AX' + temj;
			tempPidAry[tempPidAry.length - 1].tb_spggbm = '规格' + temj;
			tempPidAry[tempPidAry.length - 1].tb_sjbm_kh = 'B0' + temj;
			tempPidAry[tempPidAry.length - 1].tb_bdhp_mc = '货品名称' + temj;
			tempPidAry[tempPidAry.length - 1].tb_bdhp_jc = '货品简称' + temj;
			tempPidAry[tempPidAry.length - 1].tb_bdhp_sjbm = '货品规格编码' + temj;
			tempPidAry[tempPidAry.length - 1].tb_itemNo = '货号' + temj;
			tempPidAry[tempPidAry.length - 1].tb_sys_sku_alias = '货品规格别名' + temj;
			tempPidAry[tempPidAry.length - 1].tb_hpsjbm = '编码' + temj;
			tempPidAry[tempPidAry.length - 1].tb_hpggbm = '规格别名' + temj;
			tempPidAry[tempPidAry.length - 1].tb_sjbm = '编码' + temj;
			tempPidAry[tempPidAry.length - 1].tb_spmc = '商品' + temj;
			tempPidAry[tempPidAry.length - 1].tb_ggmc = '规格' + temj;
			tempPidAry[tempPidAry.length - 1].tb_ggbm = '编码' + temj;
			tempPidAry[tempPidAry.length - 1].tb_warehouseSlotName = '货位' + temj;
			tempPidAry[tempPidAry.length - 1].tb_cpgg = '红' + temj + '码';
			tempPidAry[tempPidAry.length - 1].tb_cpggAll = '颜色:红;产品规格:' + temj + '码';
			tempPidAry[tempPidAry.length - 1].tb_cpggAlias = '红2' + temj + '码';
			tempPidAry[tempPidAry.length - 1].tb_count = tb_count;
			tempPidAry[tempPidAry.length - 1].tb_weight = '10';
			tempPidAry[tempPidAry.length - 1].tb_dj = '126.50';
			tempPidAry[tempPidAry.length - 1].tb_yh = '6.50';
			tempPidAry[tempPidAry.length - 1].tb_spje = '120.00';
			tempPidAry[tempPidAry.length - 1].tb_spyjje = '126.50';
			tempPidAry[tempPidAry.length - 1].tb_sf = '120.00';
			tempPidAry[tempPidAry.length - 1].tb_hpjc = '简称' + temj;
			tempPidAry[tempPidAry.length - 1].tb_hpggmc = '规格名称' + temj;
			tempPidAry[tempPidAry.length - 1].tb_cgsl = '数量' + temj;
			tempPidAry[tempPidAry.length - 1].tb_cbj = '成本价' + temj;
			tempPidAry[tempPidAry.length - 1].tb_rksl = '数量' + temj;
			tempPidAry[tempPidAry.length - 1].tb_yrksl = 10 + temTitJ;
			tempPidAry[tempPidAry.length - 1].tb_rksj = '2000-01-01 00:00:00';
			tempPidAry[tempPidAry.length - 1].tb_brandName = '品牌';
			tempPidAry[tempPidAry.length - 1].tb_fx_sf = '90.00';
			tempPidAry[tempPidAry.length - 1].tb_stall = '档口';
			tempPidAry[tempPidAry.length - 1].tb_color = '黑色';
			tempPidAry[tempPidAry.length - 1].tb_size = 'L';
			tempPidAry[tempPidAry.length - 1].tb_sj = '售价' + temj;
			tempPidAry[tempPidAry.length - 1].tb_tagprice = '吊牌价' + temj;
			tempPidAry[tempPidAry.length - 1].tb_cksl = '数量' + temj;
			tempPidAry[tempPidAry.length - 1].tb_market = '市场' + temj;
			tempPidAry[tempPidAry.length - 1].tb_stall = '档口' + temj;
			tempPidAry[tempPidAry.length - 1].tb_supplier = '供应商' + temj;
			tempPidAry[tempPidAry.length - 1].tb_tagprice_total = '吊牌价总价' + temj;
			tempPidAry[tempPidAry.length - 1].tb_cbj_total = '成本价总价' + temj;
			tempPidAry[tempPidAry.length - 1].tb_sj_total = '售价总价' + temj;
			tempPidAry[tempPidAry.length - 1].tb_tb_txm_txt = '条形码' + temj;
			tempPidAry[tempPidAry.length - 1].tb_zp = '赠品';
			tempPidAry[tempPidAry.length - 1].tb_goodsNum = temj;


			// 记录产品ID，如果已经记录过则不记录
			let ispid = false;
			for (var k = 0; k < pidAry.length; k++) {
				if (pidAry[k] == tempPidAry[tempPidAry.length - 1].tb_pid) {
					ispid = true;
					break;
				}
			}
			if (!ispid) {
				pidAry[pidAry.length] = tempPidAry[tempPidAry.length - 1].tb_pid;
			}
		}

		obj.count = '数量';
		// 订单编号
		obj.s_tid = '系统单号';
		// LC编号
		obj.lc = 'LC编号';
		// 代收金额
		obj.dsje = '200';
		// 实付金额
		obj.ddje = '100';
		// 到付金额
		obj.dfje = '10';
		// 货款总计
		obj.hkzj = '货款总计';

		// 将当前地址中的所有订单中的产品加入打印数据
		obj.tableDataArray = new Array();
		for (var k = 0; k < pidAry.length; k++) {
			for (let i = 0; i < tempPidAry.length; i++) {
				if (tempPidAry[i].tb_pid != pidAry[k]) {
					continue;
				}
				const addi = obj.tableDataArray.length;
				obj.tableDataArray[addi] = {};
				obj.tableDataArray[addi].tb_pid = tempPidAry[i].tb_pid;
				obj.tableDataArray[addi].tb_img = tempPidAry[i].tb_img;
				obj.tableDataArray[addi].tb_tit = tempPidAry[i].tb_tit;
				obj.tableDataArray[addi].tb_jc = tempPidAry[i].tb_jc;
				obj.tableDataArray[addi].tb_sjbm_b = tempPidAry[i].tb_sjbm_b;
				obj.tableDataArray[addi].tb_spggbm = tempPidAry[i].tb_spggbm;
				obj.tableDataArray[addi].tb_hpsjbm = tempPidAry[i].tb_hpsjbm;
				obj.tableDataArray[addi].tb_hpggbm = tempPidAry[i].tb_hpggbm;
				obj.tableDataArray[addi].tb_sjbm = tempPidAry[i].tb_sjbm;
				obj.tableDataArray[addi].tb_spmc = tempPidAry[i].tb_spmc;
				obj.tableDataArray[addi].tb_ggmc = tempPidAry[i].tb_ggmc;
				obj.tableDataArray[addi].tb_ggbm = tempPidAry[i].tb_ggbm;
				obj.tableDataArray[addi].tb_sys_sku_alias = tempPidAry[i].tb_sys_sku_alias;
				obj.tableDataArray[addi].tb_warehouseSlotName = tempPidAry[i].tb_warehouseSlotName;
				obj.tableDataArray[addi].tb_sjbm_kh = tempPidAry[i].tb_sjbm_kh;
				obj.tableDataArray[addi].tb_bdhp_mc = tempPidAry[i].tb_bdhp_mc;
				obj.tableDataArray[addi].tb_bdhp_jc = tempPidAry[i].tb_bdhp_jc;
				obj.tableDataArray[addi].tb_bdhp_sjbm = tempPidAry[i].tb_bdhp_sjbm;
				obj.tableDataArray[addi].tb_itemNo = tempPidAry[i].tb_itemNo;
				obj.tableDataArray[addi].tb_cpgg = tempPidAry[i].tb_cpgg;
				obj.tableDataArray[addi].tb_cpggAll = tempPidAry[i].tb_cpggAll;
				obj.tableDataArray[addi].tb_cpggAlias = tempPidAry[i].tb_cpggAlias;
				obj.tableDataArray[addi].tb_count = tempPidAry[i].tb_count;
				obj.tableDataArray[addi].tb_weight = tempPidAry[i].tb_weight;
				obj.tableDataArray[addi].tb_dj = tempPidAry[i].tb_dj;
				obj.tableDataArray[addi].tb_yh = tempPidAry[i].tb_yh;
				obj.tableDataArray[addi].tb_sf = tempPidAry[i].tb_sf;
				obj.tableDataArray[addi].tb_spje = tempPidAry[i].tb_spje;
				obj.tableDataArray[addi].tb_spyjje = tempPidAry[i].tb_spyjje;
				obj.tableDataArray[addi].tb_fx_sf = tempPidAry[i].tb_fx_sf;
				obj.tableDataArray[addi].tb_hpjc = tempPidAry[i].tb_hpjc;
				obj.tableDataArray[addi].tb_hpggmc = tempPidAry[i].tb_hpggmc;
				obj.tableDataArray[addi].tb_cgsl = tempPidAry[i].tb_cgsl;
				obj.tableDataArray[addi].tb_rksl = tempPidAry[i].tb_rksl;
				obj.tableDataArray[addi].tb_cbj = tempPidAry[i].tb_cbj;
				obj.tableDataArray[addi].tb_tagprice = tempPidAry[i].tb_tagprice;
				obj.tableDataArray[addi].tb_yrksl = tempPidAry[i].tb_yrksl;
				obj.tableDataArray[addi].tb_rksj = tempPidAry[i].tb_rksj;
				obj.tableDataArray[addi].tb_brandName = tempPidAry[i].tb_brandName;

				obj.tableDataArray[addi].tb_stall = tempPidAry[i].tb_stall;
				obj.tableDataArray[addi].tb_color = tempPidAry[i].tb_color;
				obj.tableDataArray[addi].tb_size = tempPidAry[i].tb_size;
				obj.tableDataArray[addi].tb_market = tempPidAry[i].tb_market;
				obj.tableDataArray[addi].tb_stall = tempPidAry[i].tb_stall;
				obj.tableDataArray[addi].tb_supplier = tempPidAry[i].tb_supplier;
				obj.tableDataArray[addi].tb_sj = tempPidAry[i].tb_sj;
				obj.tableDataArray[addi].tb_cksl = tempPidAry[i].tb_cksl;
				obj.tableDataArray[addi].tb_tagprice_total = tempPidAry[i].tb_tagprice_total;
				obj.tableDataArray[addi].tb_cbj_total = tempPidAry[i].tb_cbj_total;
				obj.tableDataArray[addi].tb_sj_total = tempPidAry[i].tb_sj_total;
				obj.tableDataArray[addi].tb_tb_txm_txt = tempPidAry[i].tb_tb_txm_txt;
				obj.tableDataArray[addi].tb_zp = tempPidAry[i].tb_zp;
				obj.tableDataArray[addi].tb_goodsNum = tempPidAry[i].tb_goodsNum;
			}
		}
		// obj.bgm = "/img/UserPackCode/packcode_" + comp.Print.Data.exuid + ".png";

		// obj.is_multi = '单';
		// obj.goods_count = '1';
		// obj.goods_color = '白色';
		obj.goods_sku = '规格名称';
		obj.goods_number = '1';
		// obj.goods_jc = '货品简称';
		// obj.goods_size = 'XL';
		// obj.goods_code = '货品规格编码';
		// obj.fhd_txm_number = '123456';
		obj.fhd_number = obj.fhd_txm_number;
		obj.dshk_black = '100';

		obj.zd_exnumber = '8011110001001';  //子单号 ---目前安能快运有
		obj.txm_zd_number = '8011110001001'; //子单号条形码 ---目前安能快运有
		obj.route = '南京-杭州-萧山-广州';  //中转路由  --- 目前安能快运有
		obj.item_title = '商品名称';   //商品名称  --- 目前安能快运有

		//顺丰 丰密 && 顺丰快运 增加的字段
		obj.fm_day_unit = 'T';
		obj.sfky_day = '24';  //顺丰快运 时效
		obj.water_mark = '001';
		obj.route_key1 = '755AF';
		obj.route_val1 = 'D1';
		obj.route_key2 = '769WF';
		obj.route_val2 = 'A3';
		obj.route_key3 = '731WS';
		obj.route_val3 = 'A3';
		obj.route_key4 = '020WD';
		obj.route_val4 = '5D';
		obj.zhliang = 1;
		obj.wave_no = '波次号'
		obj.wave_packing_no = '波次拣选号'
		obj.wave_package_no = '波次包裹号'
		obj.fm_day = '4';
		obj.fm_jgxx = 'V25';
		obj.fm_cgzc = '576WA';
		obj.cgxx = '576WA';
		const _protocol = location.protocol || 'http:';
		//货到付款图片链接
		obj.stall = '宝贝档口';
		obj.goods_title = '标题';
		obj.print_time = '打印时间';
		obj.market = '市场';
		obj.goods_content = '商家编码';
		obj.gg_content = '规格编码';
		obj.sku_alias = '规格别名';
		obj.ex_company = '快递公司';
		obj.exnumber = 'SF111111xxxx1111';
		obj.cost_price = '成本价';
		obj.refund_reason = '退款原因';
		obj.total_weight = '总重量';
		obj.urge_shipments = '催发货';
		obj.p_count_no_gifts = '1'; //商品数量不含赠品
		obj.offline_remarks = '线下备注';
		obj.shop_plat = '店铺平台';
		obj.delivery_to_door = '送货上门';
		obj.pt_order_id = '平台单号';

		obj.vip_bq = '会员标签';

		obj.goods_name = '商品名称';
		obj.img_v_cod = _protocol + '//img.alicdn.com/imgextra/i3/69942425/O1CN011TmgumhvzT4yxRe_!!69942425.png';
		obj.img_v_fmAb = _protocol + '//img.alicdn.com/imgextra/i1/69942425/O1CN011TmgunqmMKOEzpn_!!69942425.jpg';
		obj.img_v_fm1 = _protocol + '//img.alicdn.com/imgextra/i3/69942425/O1CN011TmguoGMKl8gPZm_!!69942425.png';
		obj.img_v_fm2 = _protocol + '//img.alicdn.com/imgextra/i3/69942425/O1CN011TmgunRSfdoWHQ3_!!69942425.png';
		obj.img_v_fm3 = _protocol + '//img.alicdn.com/imgextra/i4/69942425/O1CN011TmguoP8o00Izyl_!!69942425.png';
		obj.img_v_fm4 = _protocol + '//img.alicdn.com/imgextra/i1/69942425/O1CN011Tmguoyk7oV6rOn_!!69942425.png';

		//标签数据项
		obj.is_multi = '单';
		obj.goods_count = '1'; // 总数量
		obj.goods_count_xh = '3-1'; // 总序号
		obj.goods_color = '白色';
		obj.goods_jc = '货品简称';
		obj.goods_size = 'XL';
		obj.goods_code = '货品规格编码';
		obj.fh_goods_count = '1';

		obj.nh_goods_code = '商品货号';
		obj.specifications_name = '规格名称';
		obj.fh_product_sku_name = '货品规格名称';
		obj.specifications_code = '规格编码';
		obj.goods_content = '平台商家编码';
		obj.goods_sku = '白色;XL'; // 商品规格
		obj.fhd_txm_number = '123456';
		obj.fhd_number = obj.fhd_txm_number;
		obj.fj_number = '商品唯一码';
		obj.fjm_code = '分拣号';
		obj.stock_sign = '屯'; // 屯货标识
		obj.create_date = mm + '-' + dd; //生成时间
		obj.gg_content = '规格编码';
		obj.shop_sign = '店标';
		obj.urgent_sign = '缺X天';
		obj.water_number = 1; // 流水号
		obj.stall = '档口';
		obj.market = '市场';
		obj.sku_alias = '货品规格别名';
		obj.sf_price = '实付单价';
		obj.sku_name = '规格名称';
		obj.nh_dp_position = '货位';
		obj.nh_syssku_name = '货品规格名称';
		obj.supplier = '供应商';
		obj.urgent_tag = '缺货1天'
		obj.tag_type = '拿货'
		obj.specs_alias = '货品规格别名'
		obj.store = '档口'
		obj.goods_no = '货品规格货号'
		obj.pay_days = '1'
		obj.ewm_bb = '二维码';
		obj.nh_goods_color = '颜色';
		obj.nh_goods_size = 'XL';
		obj.nh_reciever_name = '收件人';
		obj.nh_reciever_mobile = '收件人手机号';
		obj.nh_tel = '收件人固话';
		obj.nh_p = '收件省';
		obj.nh_city = '收件市';
		obj.nh_q = '收件区';
		obj.nh_addr = '收件街道';
		obj.nh_addrall = '收件地址';
		obj.nh_ww = '买家旺旺';
		obj.nh_zip = '收件邮编';
		obj.nh_sku_name = '规格名称';
		obj.goods_specs_code = '货品规格条形码';
		obj.amount_for_goods = '实付金额';
		obj.leave_word = '留';
		obj.remark = '备';
		obj.flag = '旗帜';
		obj.print_batch = '打印批次';
		obj.batch_index = '第1/1个';
		obj.company_name = '快递公司';
		obj.yd_no = 'SF111111xxxx1111';
		obj.platform_goods_name = '平台商品名称'
		obj.cargo_code = '对货码';
		obj.fh_goods_name = '商品名称';
		obj.fh_seller_content = '商家编码';
		obj.fh_goods_jc = '简称';
		obj.fh_specs_alias = '规格别名';
		obj.fh_product_code = '货品规格编码';

		//退货单数据项
		obj.title = '标题';
		obj.product_jc = '货品简称';
		obj.sku = '规格';
		obj.seller_content = '商家编码';
		obj.th_sku_name = '规格名称';
		obj.th_goods_code = '货品规格编码';
		obj.th_goods_name = '货品规格名称';
		obj.th_number = '退货唯一码';
		obj.th_cost_price = '成本价';
		obj.th_aftermarket_no = '售后单号';
		obj.th_offline_emarks = '线下备注';
		obj.th_buyers_message = '买家留言';
		obj.th_sellers_notes = '卖家备注';
		obj.th_dp_position = '货位';
		// 爆款标签
		obj.bk_print_time = '2023-01-01 00:00:00';
		obj.bk_pdate = '2023-01-01 00:00:00';
		obj.bk_water_number = '流水号';
		obj.bk_txm = '爆款码';
		obj.bk_m = '爆款码';
		obj.bk_ewm = '爆款二维码';
		obj.bk_goods_count = '商品总数量';
		obj.bk_goods_title = '商品标题';
		obj.bk_seller_content = '商家编码';
		obj.bk_product_jc = '货品简称';
		obj.bk_product_code = '货品编码';
		obj.bk_sku_name = '规格名称';
		obj.bk_gg_content = '规格编码';
		obj.bk_goods_color = '白色';
		obj.bk_goods_size = 'XL';
		obj.bk_goods_code = '货品规格编码';
		obj.bk_market = '市场';
		obj.bk_store = '档口';
		obj.bk_supplier = '供应商';
		obj.bk_shop_abbreviation = '店铺简称';
		obj.bk_specs_alias = '货品规格别名';
		obj.bk_goods_no = '商品货号';
		obj.bk_goods_size = 'XL';
		obj.bk_goods_color = '白色';
		// obj.txm_tid = '1111111'; //订单条形码

		// NOTE 吊牌数据项
		obj.dp_brand_name = "货品品牌名";
		obj.dp_goods_classify = "货品分类";
		obj.dp_goods_market = "货品市场";
		obj.dp_goods_store = "货品档口";
		obj.dp_product_jc = "货品简称";
		obj.dp_product_code = "货品编码";
		obj.dp_goods_sku = "货品规格";
		obj.dp_specs_alias = "货品规格别名";
		obj.dp_goods_code = "货品规格编码";
		obj.dp_goods_no = "货号";
		obj.dp_txm_number = "条形码";
		obj.dp_zhliang = "重量";
		obj.dp_code = "条形码";
		obj.dp_selling_price = "售价";
		obj.dp_tag_price = "吊牌价";
		obj.dp_cost_price = "成本价";
		obj.dp_supplier = "供应商";
		obj.dp_supplier_address = "供应商地址";
		obj.dp_is_multi = "组合/单品";
		obj.dp_txm_goods_number = "货品条形码";
		obj.dp_txm_no_number = "货号条形码";
		obj.dp_txm_sku_number = "货品规格编码条形码";
		obj.dp_no_ewm = "货号二维码";
		obj.dp_code_ewm = "货品规格编码二维码";
		obj.dp_printid = "打印序号";
		obj.dp_print_time = "2023-01-01 00:00:00"
		obj.dp_merchant_code = "商家编码";
		obj.dp_title = "商品标题";
		obj.dp_platform = "对应平台";
		obj.dp_shop = "对应店铺";
		obj.dp_form_name_sku = "规格名称"
		obj.dp_merchandise_marke = "商品市场"
		obj.dp_merchandise_store = "商品档口"
		obj.dp_abbreviation_jc = "商品简称"
		obj.dp_goods_color = '白色';
		obj.dp_goods_size = 'XL';
		obj.dp_position = '货位'

		//采购单
		obj.cgd_name_goods = '名称';
		obj.cgd_no_goods = '202301010001';
		obj.cgd_supplier_goods = '供应商';
		obj.cgd_state_goods = '采购状态';
		obj.cgd_total_goods = '100';
		obj.cgd_money_goods = '10000';
		obj.cgd_freight_goods = '采购运费';
		obj.cgd_other_charges_go = '其它费用';
		obj.cgd_founder_goods = '创建人';
		obj.cgd_order_time_goods = '2023-01-01 00:00:00';
		obj.cgd_notes_goods = '备注';
		obj.cgd_no_txm_with_code = '采购单号';
		obj.cgd_no_ewm = '采购单号';
		obj.actual_quantity_in_s = '1';


		// 入库单
		obj.rkd_founder_goods = "创建人";
		obj.rkd_freight_goods = "入库运费";
		obj.rkd_money_goods = "10000";
		obj.rkd_name_goods = "名称";
		obj.rkd_no_ewm = "入库单号";
		obj.rkd_no_goods = "202301010001";
		obj.rkd_no_txm_with_code = "入库单号";
		obj.rkd_notes_goods = "备注";
		obj.rkd_order_time_goods = "2023-01-01 00:00:00";
		obj.rkd_other_charges_go = "其它费用";
		obj.rkd_state_goods = "入库状态";
		obj.rkd_supplier_goods = "供应商";
		obj.rkd_total_goods = "1000";

		// 出库单
		obj.ckd_rno_goods = "1141024xxxxxxx00000";
		obj.ckd_state_goods = "出库状态";
		obj.ckd_total_goods = "100";
		obj.ckd_money_goods = "1000";
		obj.ckd_freight_goods = "运费";
		obj.ckd_other_charges_go = "其它费用";
		obj.ckd_founder_goods = "创建人";
		obj.ckd_create_time_good = "2023-01-01 00:00:00";
		obj.ckd_notes_goods = "备注";
		obj.ckd_recipient_goods = "收件人";
		obj.ckd_contact_details_ = "联系方式";
		obj.ckd_no_txm_with_code = "出库单号";
		obj.ckd_no_ewm = "出库单号"
		obj.ckd_addrall = "xx省xx市xx区xxxxx"

		// 退货清单
		obj.thqd_market = '市场';
		obj.thqd_stall = '档口';
		obj.thqd_supplier = '供应商';
		obj.thqd_total_count = '总数量';
		obj.thqd_total_amount = '总金额';
		obj.thqd_print_time = '打印时间';
		obj.thqd_print_num = '打印序号';
		obj.hj_thje = '1000';
		// 直播标签
		obj.zb_shop_sign = "店铺标识";
		obj.zb_tid = "系统单号";
		obj.zb_pt_tid = "平台单号";
		obj.zb_pay_date = "付款时间";
		obj.zb_buyer_message = "买家留言";
		obj.zb_saler_remark = "卖家备注";
		obj.zb_shop_abbreviation = "店铺简称";
		obj.zb_reciever_name = "收件人";
		obj.zb_reciever_mobile = "收件人手机号";
		obj.zb_tel = "收件人固话";
		obj.zb_amount_for_goods = "商品实付金额";
		obj.zb_p = "收件省";
		obj.zb_city = "收件市";
		obj.zb_q = "收件区";
		obj.zb_addr = "收件街道";
		obj.zb_addrall = "收件地址";
		obj.zb_ww = "买家旺旺";
		obj.zb_zip = "收件邮编";
		obj.zb_tag_number = "1";
		obj.zb_daren_name = "达人姓名";
		obj.zb_daren_id = "达人ID";
		obj.shop_info = '商品信息';
		obj.shop_count = '商品总数';
		obj.zb_virtual_sign = '■';
		obj.zb_blacklist_id = '黑';
		obj.zb_first_order_id = '新';
		obj.zb_rep_order_sign = '★';

		// 备货标签
		obj.bhbq_product_jc = "货品简称";
		obj.bhbq_goods_name = "货品规格名称";
		obj.bhbq_goods_code = "货品规格编码";
		obj.bhbq_specs_alias = "货品规格别名";
		obj.bhbq_tm = "条形码";
		obj.bhbq_goods_no = "货号";
		obj.bhbq_position = "货位";
		obj.bhbq_market = "市场";
		obj.bhbq_store = "档口";
		obj.bhbq_supplier = "供应商";
		obj.bhbq_goods_number = "货品数量";
		obj.bhbq_specs_number = "规格数量";
		obj.bhbq_print_num = "打印序号";
		obj.bhbq_print_time = "打印时间"
		return obj;
	};

	///////////////////////////////////////////////////////////////////////////

	// 获取拼多多模版打印数据字段contents
	dataObj.getPrintPddContentData = function (params, cb, errorCb) {

		params.isShowLoadding && Tatami.showLoading({
			key: 'print'
		});

		let paramsData = {};
		if (comp.Print.Data.platform == 'erp') {
			paramsData = params;
		} else {
			paramsData = { cpCode: params.cpCode, styleId: params.styleId, shopName: params.shopName };
		}
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: API.get_pdd_template_url,
			data: paramsData,
			headers: getHeaderToken(),
			success: function (data) {
				if (data.result == 100) {
					const obj = {
						'data': {
							'recipient': {
								'address': {
									'city': 'XX市',
									'detail': '收件人详细地址 XXXXXXX',
									'district': 'XX区',
									'province': 'XX市',
								},
								'mobile': '***********',
								'name': '收件人姓名',
								'phone': '**********',
							},
							'routingInfo': {
								'bigShotName': '上海',
								'originBranchCode': 'shanghai',
								'originBranchName': '上海',
								'endBranchCode': 'shanghai',
								'endBranchName': '上海',
								'threeSegmentCode': '987 654 XXX',
							},
							'sender': {
								'address': {
									'city': 'XX市',
									'detail': '发件人详细地址XXXXXX',
									'district': 'XX区',
									'province': 'XX省',
								},
								'mobile': '***********',
								'name': '发件人姓名',
								'phone': '**********',
							},
							'waybillCode': '**************',
							'signature': '电子签名',
							'templateUrl': data.data.templateUrl,
							'wpCode': 'STO',
						},
						'isCustom': data.data.isCustom, // 是否需要自定义区域
					};
					try {
						if (!data.data.templateUrl) {
							Tatami.showFail('未能获取模板底图，请尝试重新授权或联系客服');
							return;
						}
					} catch (e) {
						//
					}

					cb && cb(obj);
					params.isShowLoadding && Tatami.clearShow('print');
				} else {
					console.error('获取拼多多模版地址失败！');
					params.isShowLoadding && Tatami.clearShow('print');
					errorCb && errorCb();
				}

			},
			error: function () {
				console.error('获取拼多多模版地址失败！');
				params.isShowLoadding && Tatami.clearShow('print');
				errorCb && errorCb();
			},
		});

	};

	///////////////////////////////////////////////////////////////////////////

	//获取连打设置
	dataObj.getLDSet = function (mkdid) {
		const printData = comp.Print.Data;
		let retdata = null;
		for (let i = 0; i < printData.ldPtrintSets.length; i++) {
			if (mkdid == printData.ldPtrintSets[i].Mkddid) {
				retdata = printData.ldPtrintSets[i];
				break;
			}
		}
		return retdata;
	};

	//获取快递单设置
	dataObj.getKddSet = function (mkddid) {
		const printData = comp.Print.Data;
		let retdata = null;
		for (let i = 0; i < printData.kddArray.length; i++) {
			if (mkddid == printData.kddArray[i].mkddId) {
				retdata = printData.kddArray[i];
				break;
			}
		}
		return retdata;
	};

	//获取当前所选快递
	dataObj.getNowKddSet = function () {
		const printData = comp.Print.Data;
		let retdata = null;
		for (let i = 0; i < printData.kddArray.length; i++) {
			if (printData.now_kdd_mkddId == printData.kddArray[i].mkddId) {
				retdata = dataObj.kddArray[i];
				break;
			}
		}
		return retdata;
	};
	dataObj.requestAPI = function (p, successCb, failCb) {
		p.isShowLoadding && Tatami.showLoading({
			key: 'print'
		});
		$.ajax({
			type: 'post',
			dataType: 'json',
			url: p.url,
			data: p.params,
			headers: getHeaderToken(),
			success: function (data) {
				p.isShowLoadding && Tatami.clearShow('print');
				successCb && successCb(data);
			},
			error: function (request, textStatus, errorThrown) {
				p.isShowLoadding && Tatami.clearShow('print');
				failCb && failCb(errorThrown);
			},
		});
	};

	//修改模板发件人
	dataObj.modifyTempFjr = function (senderId, modeListshowId, cb) {
		// pdd用的是不带/print/center的接口
		const prefix = isMerge ? '/print/center/group' : '/print/center';
		let objKeyType = isMerge ? 'groupId' : 'modeListshowId'
		let params = {
			senderId: senderId || -1,
			[objKeyType]: modeListshowId
		}
		this.requestAPI({
			url: `${prefix}/modeListshow/saveBindModeFjrId`,
			params,
		}, function (data) {
			if (data.result == 100) {
				cb && cb();
			} else {
				Tatami.showFail('绑定发件人出错：' + data.message);
			}
		}, function (d) {
			Tatami.showFail(d || '绑定发件人出错,请稍后再试，或联系客服');
		});
	};

	/**
	 * [getKddServiceCaiNiao 获取推荐 三联模板服务列表 区别于其他模板获取的服务列表接口]
	 * @param  {obejct} params [kddType/styleId/exCompanyId/heightPaper]
	 * @param {function} cb [回调]
	 */
	dataObj.getKddServiceCaiNiao = function (params, cb) {
		this.requestAPI({
			// url:'/modeLogisticsItemCommon/getModeLogisticsDynamicCommon',
			url: API.get_kddLogisticsDynamicCommon,
			params: {
				exCompanyId: params.exCompanyId,
				heightPaper: params.heightPaper,
				kddType: params.kddType,
				styleId: params.styleId,
			},
		}, function (json) {
			if (json.result == 100) {
				cb && cb(json.data);
			} else {
				Tatami.showFail(json.message || '获取服务列表出错，请稍后再试或联系客服');
			}
		}, function (d) {
			Tatami.showFail(d || '获取服务列表出错，请稍后再试或联系客服');
		});
	};

	//复制模板
	dataObj.copyTempInfo = function (params, cb) {
		params = JSON.stringify(params);
		this.requestAPI({
			url: '/modeListshow/copyTemplate',
			params: {
				jsonParam: params,
			},
			isShowLoadding: true,
		}, function () {
			comp.Print.Data.copyTemp = params;   //缓存
			cb && cb();
		});
	};


	//粘贴模板
	dataObj.pasteTempInfo = function (params, cb) {
		const _temp = comp.Print.Data.copyTemp;
		if (_temp) {
			cb(JSON.parse(_temp));
		} else {
			this.requestAPI({
				url: '/modeListshow/pasteTemplate',
				isShowLoadding: true,
			}, function (json) {
				const _temp = json.data || '{}';
				comp.Print.Data.copyTemp = _temp;
				cb(JSON.parse(_temp));
			});
		}
	};
	// 打印自定义区域 发货内容 日志存储
	dataObj.printCustomAreaSave = function (params) {
		var params = JSON.stringify(params);
		this.requestAPI({
			url: '/print/savePrintData',
			params: {
				log: params,
			},
			isShowLoadding: true,
		}, function (json) {
			if (json.result == 100) {
				console.log('发货内容日志存储成功');
			}
		});
	};

	/**
	 *  tableDataArray 因闪打需要，重新组织结构
	 * @param printDatas  printDatas
	 */
	dataObj.resetTableDataArray = function (printDatas, isfhdhb) {
		let formattedArr = [];
		const fixFloatFunc = function (_obj, serialNo, xh1SerialNo) {
			$.each(_obj, function (key1, value1) {
				if (/^(tb_sf|tb_spje|tb_spyjje|tb_fx_sf|tb_yh|tb_weight)$/.test(key1)) {
					_obj[key1] = parseFloat(value1).toFixed(2);
				}
			});
			_obj['tb_xh'] = serialNo;
			_obj['tb_xh1'] = xh1SerialNo ? xh1SerialNo : serialNo;
			_obj['tb_weight'] = _obj['tb_weight'] || ''; //对于为 0 的重量，打印时显示为0 -> 不显示
			return _obj;
		};
		if (isfhdhb) {
			printDatas.map(function (data, index) {
				data = data.custom || data;
				formattedArr = []; //置空
				const arr = data.tableDataArray || [];
				const tree = {};
				const treeArr = [];
				let tb_xh = 0, tb_idx = 0, tb_xh1 = 0;

				arr.each(function (ind, arrVal) {
					const inv = Object.Copy(arrVal);
					const pid = inv.tb_pid;
					if (!tree[pid]) {
						let temObj = {};
						temObj = {};
						temObj.pid = pid;
						temObj.rowspan = 0;
						temObj.tb_xh = ++tb_xh;
						// temObj.tb_idx = tb_idx;
						temObj.rows = {};
						tree[pid] = temObj;
						treeArr.push(temObj);
					}

					const pidObj = tree[pid];
					if (!pidObj.rows[inv.tb_cpgg]) {
						pidObj.rowspan++;
						inv.tb_count = +inv.tb_count;
						inv.tb_weight = +inv.tb_weight;
						inv.tb_yh = +inv.tb_yh;
						inv.tb_sf = +inv.tb_sf;
						inv.tb_spje = +inv.tb_spje;
						inv.tb_spyjje = +inv.tb_spyjje;
						inv.tb_fx_sf = +inv.tb_fx_sf;
						pidObj.rows[inv.tb_cpgg] = inv;
					} else {
						const rowobj = pidObj.rows[inv.tb_cpgg];
						rowobj.tb_count += +inv.tb_count;
						rowobj.tb_weight += +inv.tb_weight;
						rowobj.tb_yh += +inv.tb_yh;
						rowobj.tb_sf += +inv.tb_sf;
						rowobj.tb_spje += +inv.tb_spje;
						rowobj.tb_spyjje += +inv.tb_spyjje;
						rowobj.tb_fx_sf += +inv.tb_fx_sf;
					}
				});

				treeArr.map(function (item, index) {
					const rowArr = [];

					Object.keys(item.rows).map(function (key) {
						rowArr.push(item.rows[key]);
					});

					rowArr.map(function (row, i) {
						tb_xh1 += 1
						row = fixFloatFunc(row, item.tb_xh, tb_xh1);
					});

					formattedArr.push({ 'row': rowArr, 'rowspan': item.rowspan });
				});

				data.tableDataArray = formattedArr;
			});
		} else {
			printDatas.map(function (data, index) {
				data = data.custom || data;
				formattedArr = [];
				const arr = data.tableDataArray || [];
				arr.map(function (val, ind) {
					val = fixFloatFunc(val, ind + 1);
					formattedArr.push({ row: [val], rowspan: 1 });
				});
				data.tableDataArray = formattedArr;

			});
		}
		return printDatas;
	};

	//打印数据处理
	dataObj.getServicesListValue = function (templateSet, printData) {
		let content = '',
			bjje, fkfs;
		if (templateSet.ModeServiceItems && templateSet.ModeServiceItems.length) {
			$.each(templateSet.ModeServiceItems, function (j, v) {
				if (v.Issetval == 1) {
					if (v.Itemname == '保价金额' && v.Reserve1 && v.Reserve1 != '保价金额') {//保价金额特殊处理
						bjje = v.Reserve1 || '';
						content += v.Itemname + ':' + bjje + v.Unit;
					} else {
						content += v.Itemname + ':' + (printData[v.Dataname] || '') + v.Unit;
					}
				} else {
					content += v.Itemname + ':' + (v.Defaultval || '');
				}
				if (templateSet.ModeList.Excode === 'SF') {
					content += ((j + 1) % 2 == 0 ? '\n' : '    ');
				} else {
					content += '\n';
				}
				if (v.Itemname === '付款方式' || v.Itemname === '运费支付方式') {
					fkfs = v.Defaultval;
				}
			});
		}
		return {
			servicelist: content,
			bjje: bjje || '',
			fkfs: fkfs || '',
		};
	};

	/**
	 * @param exCode {string}
	 * @param type {string} 取值：lc|qs 留存联logo|签收联logo
	 * return 快递logo路径
	 */
	dataObj.getLogoByExcode = function (exCode, type) {
		if (!exCode) {
			return '';
		}
		type = type ? '-' + type : '';
		return `${location.origin}/resources/img/print/ExImg/exlogo/${exCode}${type}.png`;
	};

	dataObj.dealWithPddData = function (list, templateSet) {
		let item;
		const getDealData = function (orderItem, index) {
			let inputMap = {},
				_val, _pro;
			templateSet.ModeInputs && $.each(templateSet.ModeInputs, function (i, v) {
				_pro = v.proArray;
				inputMap[v.InputID] = v.Str_q || '';
				$.each(_pro, function (i1) {
					if (list && list[index].isNeedBreakPage) { //分页：去掉发货内容
						if (this.Dataname == 'f_info') {  // 发货内容
							_val = '';
						} else {
							_val = orderItem[this.Dataname] || '';
						}
					} else { //不分页
						_val = orderItem[this.Dataname] || '';
					}
					if (this.Dataname == 'sfjc') {
						orderItem.sfjc = _p30_getSfjcVal(orderItem.s_p);  //省份简称
					}
					if (_val && i1 > 0) {
						inputMap[v.InputID] += ' ';   //多个数据项 中间以空格分割
					}
					inputMap[v.InputID] += _val;

					//兼容历史数据处理
					if (i1 == 0) {
						inputMap[this.Dataname] = (v.Str_q || '') + orderItem[this.Dataname] + (v.Str_h || '');
					}
				});
				inputMap['txm_number'] = orderItem['txm_number'] || '';
				inputMap['count'] = orderItem['count'] || '';  //用于分页打印 “数量”
				inputMap['f_info'] = orderItem['f_info'] || '';
				inputMap[v.InputID] += v.Str_h || '';
			});
			return inputMap;
		};
		$.each(list, function (i, v) {
			let custom = {};
			item = v.custom;
			if (item) {
				item.mdd = item['s_city'] + ' ' + item['s_q']; //目的地 市+区
				custom = getDealData(v.custom, i);
				custom.f_count = custom.count;
				list[i].custom = custom;
			}
		});
		return list;
	};

	//根据模板服务 处理打印数据
	dataObj.dealWithData = function (list, templateSet, isCloudPrint) {
		let servicename = (templateSet.ModeCustomerTemplate || {}).ServiceValue || '',
			servicetype = (templateSet.ModeCustomerTemplate || {}).Svctypename || '',
			kddType = templateSet.ModeList.KddType,
			styleId = templateSet.ModeList.StyleId,
			isCainiao = kddType == 3 && comp.base.getTempStyle('cainiao', styleId),
			item = null, obj, that = this;
		const cfg = templateSet.ModeTempPrintcfg || {};


		let img_logo_lc, img_logo_qs;
		if (kddType == 2 && (cfg.Lclogo || cfg.Qslogo)) {
			const logoUrl = that.getLogoByExcode(templateSet.ModeList.Excode);
			cfg.Lclogo && (img_logo_lc = logoUrl);
			cfg.Qslogo && (img_logo_qs = logoUrl);
		}
		$.each(list, function (i, v) {
			item = list[i];
			isCainiao && (item = item.custom);
			if (item && typeof item === 'object') {
				item.sfjc = _p30_getSfjcVal(item.s_p);  //省份简称
				item.mdd = item['s_city'] + ' ' + item['s_q']; //目的地 市+区
				if (!isCainiao) {
					if (kddType == 1 || kddType == 3) {   //普通五联单模板  自由模板 大头笔 省市区拼接
						item.mdd_dzmd = item.mdd_dzmd || (item['s_p'] + ' ' + item['s_city'] + ' ' + item['s_q']);      //大头笔无用省市区拼接
						item.gx_mdd = item.gx_mdd || (item['s_p'] + ' ' + item['s_city'] + ' ' + item['s_q']);    //个性目的地 没有的时候用省市区拼接
					}
					if (kddType == 3 || kddType == 2 || kddType == 8) {    //自由模板 网点模板  需要服务选项
						item.servicename = servicename;
						obj = that.getServicesListValue(templateSet, item);
						item.servicelist = obj.servicelist;
						item.bjje = obj.bjje || item.bjje || '';
						item.fkfs = item.fkfs || obj.fkfs;
					}
					item.servicetype = item.servicetype || servicetype;
					item.img_logo_qs = img_logo_qs;
					item.img_logo_lc = img_logo_lc;
				}
				if (isCloudPrint) {
					// 遍历模板上有数据框，如果对应的 dataname 没有内容，就设置为空字符串
					templateSet.ModeInputs.map(function (input) {
						try {
							const thisDataName = (input.proArray[0] || {}).Dataname;

							if (!item[thisDataName] && item[thisDataName] !== 0) {
								item[thisDataName] = '';
							}
						} catch (error) {
							console.log(error);
						}

					});
				}
			}
		});
		// 圆准达时间字段单独处理
		list.map((item) => {
			if (typeof item === 'object') {
				if (!item.yzd_time || item.yzd_time == '') {
					item.img_yzd = '';
				} else {
					item.img_yzd = 'http://kdzs-jxc-vpc.oss-cn-zhangjiakou.aliyuncs.com//jxc/img/print/ExImg/user/1721500167/7374123/6807aa1f-b098-496e-973a-daf0284ea5a8.jpg?from=oss';
					item.yzd_time = '承诺送达日期:' + item.yzd_time.replace('承诺送达日期: ', '');
				}
			}
		});
		return list;
	};

	//处理模板编辑测试数据
	dataObj.dealWithTestData = function (pData, templateSet) {
		const kddType = templateSet.ModeList.KddType;
		if (kddType == 2) {
			const logoUrl = this.getLogoByExcode(templateSet.ModeList.Excode);
			pData.img_logo_lc = logoUrl;
			pData.img_logo_qs = logoUrl;
		}
		return pData;
	};
	//获取小标签模板创建类型::Promise
	dataObj.getXbqAddStandardInfo_promise = function (data) {
		return new Promise((resolve, reject) => {
			let { type, categoryCode } = data
			const parameter = { templateType: type, categoryCode: categoryCode },
				isLoading = true,
				printData = comp.Print.Data,
				key = 'xbqAddStandardInfo' + type;
			if (printData[key] && printData[key].length > 0 && type !== 'FHD') {
				resolve(printData[key]);
				return printData[key];
			}
			let ret = null;
			isLoading && Tatami.showLoading({
				key: 'print'
			});
			$.ajax({
				type: 'post',
				dataType: 'json',
				url: API.get_xbq_add_standardInfo,
				data: parameter,
				headers: getHeaderToken(),
				success: function (data) {
					isLoading && Tatami.clearShow('print');
					data = changeData(data);
					if (!data.IsError) {
						ret = data.Data;
						comp.Print.Data[key] = ret;
						resolve(ret);
					} else {
						console.error('comp.Print.data.getKddBgImgBy is error' + data.Msg);
						Tatami.showFail('获取添加模板详情出错')
					}
				},
				error: function (request, textStatus, errorThrown) {
					isLoading && Tatami.clearShow('print');
					console.error('comp.Print.data.getKddBgImgBy is error！' + (textStatus || errorThrown));
					Tatami.showFail('获取添加模板详情出错')
					reject();
				},
			});
			// return ret;
		});
	};
	//获取小标签模板创建类型::Promise
	dataObj.getFhdTypeList = function (type) {
		return new Promise((resolve, reject) => {
			const parameter = { templateType: type },
				printData = comp.Print.Data;
			if (printData['fhdCategory'] && printData['fhdCategory'].length > 0) {
				resolve(printData['fhdCategory']);
				return printData['fhdCategory'];
			}
			let ret = null;
			Tatami.showLoading({
				key: 'print'
			});
			$.ajax({
				type: 'post',
				dataType: 'json',
				url: API.get_st_category,
				data: parameter,
				headers: getHeaderToken(),
				success: function (data) {
					Tatami.clearShow('print');
					data = changeData(data);
					if (!data.IsError) {
						ret = data.Data;
						comp.Print.Data['fhdCategory'] = ret;
						resolve(ret);
					} else {
						console.error('comp.Print.data.getKddBgImgBy is error' + data.Msg);
						Tatami.showFail('获取添加模板详情出错')
					}
				},
				error: function (request, textStatus, errorThrown) {
					Tatami.clearShow('print');
					console.error('comp.Print.data.getKddBgImgBy is error！' + (textStatus || errorThrown));
					Tatami.showFail('获取添加模板详情出错')
					reject();
				},
			});
			// return ret;
		});
	};
	//获取小标签模板创建数据::Promise
	dataObj.getXbqAddInitItem_promise = function (tempType) {
		return new Promise((resolve, reject) => {
			let templateTypes = {
				'bq': 'BHD,BKD',
				'dpd': 'DPD,TMD',
				'thd': 'THD',
				'cgd': 'CGD',
				'rkd': 'RKD',
				'ckd': 'CKD',
				'thqd': 'THQD',
				'fhd': 'FHD',
				'zbd': 'ZBD,ZBXP',
				'bhbq': 'BHBQ',
			}
			if (isZeroStockVersion()) {
				templateTypes['dpd'] = 'WDPD,WTMD'
			}
			const parameter = { templateType: templateTypes[tempType] },
				isLoading = true,
				printData = comp.Print.Data,
				key = 'xbqAddInitItem' + tempType;
			if (printData[key] && printData[key].length > 0) {
				resolve(printData[key]);
				return printData[key];
			}
			let ret = null;
			isLoading && Tatami.showLoading({
				key: 'print'
			});
			$.ajax({
				type: 'post',
				dataType: 'json',
				url: API.get_xbq_add_init_item,
				data: parameter,
				headers: getHeaderToken(),
				success: function (data) {
					isLoading && Tatami.clearShow('print');
					data = changeData(data);
					if (!data.IsError) {
						ret = data.Data;
						comp.Print.Data[key] = ret;
						resolve(ret);
					} else {
						console.error('comp.Print.data.getKddBgImgBy is error' + data.Msg);
						Tatami.showFail('获取添加模板类型出错')
					}
				},
				error: function (request, textStatus, errorThrown) {
					isLoading && Tatami.clearShow('print');
					console.error('comp.Print.data.getKddBgImgBy is error！' + (textStatus || errorThrown));
					Tatami.showFail('获取添加模板类型出错')
					reject();
				},
			});
			// return ret;
		});
	};

})(window);
