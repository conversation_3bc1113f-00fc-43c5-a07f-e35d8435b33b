/* eslint-disable indent */
import PinyinMatch from 'pinyin-match';
import axios from 'axios';
import { configPlatFuncs } from '../../print/config';
import * as printItem from '../../print/printItem';

import { mdlxList } from './templateCfg/ENUM';
import { raw } from '../../utils';
import { isValidUrl } from '../../print/printItem/utils';
import { get } from '../../common/http';
import model from "../../common/model.ts";
import { KDDTYPE_ENUM, YUNZHAN_AUTH_TYPE } from '../../common';
import { savePrintersStorage, removePrintersStorage } from '../../utils/common';

const segmentCodeUtils = {
    getName(code) {
        const codeMap = comp.Print.Data.segmentCodeNameMap || {};
        return codeMap[code] || '';
    },
};

/**
 * 暴露 comp.print.resourceMergeModel
 *
 * 快递单模板相关 dom 操作
 */
(function() {

    // 创建命名空间
    const resObj = nameSpace('comp.print.resourceMergeModel');

    // 缓存对象
    const mergeCacheObj = resObj.mergeCacheObj = {};

    let addGroupCache = null;

    // 初始化
    resObj.init = function(callback) {
        resObj.isInit = true;
        // console.info("comp.print.resourceMergeModel.init");
        callback();
    };

    resObj.closeTemplateInfo = function(ptype) {
        if (ptype == 'kdd') {
            if (mergeCacheObj.showKddMainObj) {
                mergeCacheObj.showKddMainObj.qlrr.close();
            }
        } else if (ptype == 'fhd') {
            if (mergeCacheObj.showFhdMainObj) {
                mergeCacheObj.showFhdMainObj.qlrr.close();
            }
        } else if (ptype == 'jhd') {
            if (mergeCacheObj.showJHDMainObj) {
                mergeCacheObj.showJHDMainObj.qlrr.close();
            }
        }
    };

    // 安装Lodop组件
    resObj.install = {
        strHtmInstall: '[install]|http://ex24.kuaidizs.cn/exprint/help/cainiao_down.html',
        strHtmUpdate: '[update]|http://ex24.kuaidizs.cn/exprint/help/cainiao_down.html',
        strHtm64_Install: '[install]|http://ex24.kuaidizs.cn/exprint/help/cainiao_down.html',
        strHtm64_Update: '[update]|http://ex24.kuaidizs.cn/exprint/help/cainiao_down.html',
        strHtmFireFox: '[firefox]|http://ex24.kuaidizs.cn/exprint/help/cainiao_down.html',
    };

    // 拼多多下载链接
    let pddDownloadUrl = 'https://dl.pddpic.com/windows_dev/2022-02-08/6b2e68605a803ae23dbebfd5f7da91fa.exe';

    // 新增快递单
    resObj.addKdd = function(callback, params = {}) {
        const that = new comp.Print.FN();
        const defaultExcode = params.exCode;
        let isCancel = null;
        // if (mergeCacheObj.addKddObj) {
        //     mergeCacheObj.addKddObj.qlrr.init(callback, params);
        //     mergeCacheObj.addKddObj.qlrr.show(defaultExcode);
        //     return mergeCacheObj.addKddObj;
        // }
        const html = '<div style="display:none" class="pup_express_box">\
        <div class="pupexpr_tlt clearfix">\
        <h1>快递单</h1>\
        <a  class="expr_btn_gray float_r">取消</a>\
        <a  class="expr_btn_green float_r">保存</a>\
        </div>\
        <div  class="kdd_set_main add_temp_content" style="overflow:hidden;overflow-y:auto">\
        </div>\
        </div>';
        const dom = $(html).appendTo($('body'));
        const domid = 'div_' + (+new Date());
        dom.attr('id', domid);
        const btnOk = dom.find('.expr_btn_green');
        const btnNo = dom.find('.expr_btn_gray');
        const kdd_set_main = dom.find('.kdd_set_main');
        dom.qlrr = {};
        const domData = {};

        dom.qlrr.init = function(callback, params) {
            isCancel = params.isCancel;
            btnOk.html('保存');
            domData.callback = callback;
            kdd_set_main.empty();
            dom.qlrr.addKd(params.exCode);
        };

        dom.qlrr.show = function(defaultExcode) {
            that.showDialog(domid);
            if (defaultExcode) {
                // 半秒钟能保证 dom 的挂载
                setTimeout(() => {
                    kdd_set_main.find('#addExCompany .drop-down-choose').trigger('click');
                    kdd_set_main.find(`#addExCompany .drop-down-select li[excode="${defaultExcode}"]`).click();
                }, 500);
            }
        };

        dom.qlrr.close = function(msg) {
            that.closeDialog(domid);
            if (isCancel) {
                that.setGroupSet();
            } else if (typeof domData.callback === 'function') {
                domData.callback(msg);
            }
            //  else {
            //     const defId = +msg;
            //     if (!isNaN(defId) && !isCancel) {
            //         resObj.showkddMain(defId);
            //     }
            // }
        };

        dom.qlrr.clearDom = function(action) {
            kdd_set_main.find('dl.kdd_ctmd_bz[do=' + action + ']').nextAll().remove();
        };

        // 新增快递
        dom.qlrr.addKd = function(groupCode) {
            const funcHook = (window.printAPI.compHookObj || {})
                .operatingRecord;
            funcHook && funcHook('订单_订单打印_快递单设置_添加模板');

            const dl = $('<dl  class="kdd_ctmd_bz add-kdd-company" do="0">'
                + '<dt>添加快递公司</dt>'
                + '<dd>'
                + '<div name="add_kdd_kd"  id="addExCompany" style="width: 200px;color:black">'
                + '<span class="loading ml_10"><i class="icon-load"></i>加载中…</span>'
                + '</div>'
                + '</dd>'
                + '</dl>').appendTo(kdd_set_main);
            that.getExCompany(function(exCompanys) {
                if (groupCode) exCompanys = exCompanys.filter(it => it.ExCode === groupCode);
                dl.find('#addExCompany').html(
                    window.comDropDown.init({
                        list: exCompanys,
                    }, function(seachVal, data) {
                        let html = '';
                        let filteArr = [];
                        if (!seachVal) {
                            filteArr = data.list;
                        } else {
                            data.list && $.each(data.list, function(index, v) {
                                if (PinyinMatch.match(v.CompanyName, seachVal).length) {
                                    filteArr.push(v);
                                }
                            });
                        }
                        filteArr && $.each(filteArr, function(_i, item) {
                            if (_i == 0) {
                                if (item.ExCode == 'EYB') { // "EMS快递包裹"
                                    html += '<s class="template-forbidden-container"><div class="fixed-position-EYB"><div class="area-expand">EMS快递包裹模板已下线，请切换至邮政快递包裹模板打单！<a target="_blank" href="//helptb.kuaidizs.cn/helpMap/getDetail?detailId=513">点此查看通知</a></div></div><li excode=' + item.ExCode + ' value=' + item.Id + ' class="active">' + item.CompanyName + '</li></s>';
                                } else {
                                    html += '<li excode=' + item.ExCode + ' value=' + item.Id + ' type=' + item.companyType + ' class="active">' + item.CompanyName + '</li>';
                                }

                            } else if (item.ExCode == 'EYB') { // "EMS快递包裹"
                                    html += '<s class="template-forbidden-container"><div class="fixed-position-EYB"><div class="area-expand">EMS快递包裹模板已下线，请切换至邮政快递包裹模板打单！<a target="_blank" href="//helptb.kuaidizs.cn/helpMap/getDetail?detailId=513">点此查看通知</a></div></div><li excode=' + item.ExCode + ' value=' + item.Id + '>' + item.CompanyName + '</li></s>';
                                } else {
                                    html += '<li excode=' + item.ExCode + ' value=' + item.Id + ' type=' + item.companyType + '>' + item.CompanyName + '</li>';
                                }
                        });
                        if (!html) {
                            html = '<li class="disabled">无输入结果</li>';
                        }
                        return html;
                    }, function($target) {
                        console.log($target, '$target$target');
                        domData.companyId = $target.attr('value');
                        domData.excode = $target.attr('excode');
                        domData.excodeName = $target.text();
                        domData.companyType = $target.attr('type');
                        dom.qlrr.clearDom(0);
                        if (domData.companyId.length > 0) {
                            dom.qlrr.addMdlx();
                        }
                    }),
                );
            });
        };

        // 面单类型
        dom.qlrr.addMdlx = function() {
            // 根据平台判断模板后面是否加小火苗
            // 放心购平台：抖音电子面单加；
            // 其他平台：拼多多面单加
            const PLAT = comp.Print.Data.platform;
            const iconUrl = 'https://static.kuaidizs.cn/resources/img/print/hot.png';

            let html = '<dl  class="kdd_ctmd_bz xz_dzms_dl clearfix" do=1>\
            <dt>选择面单类型</dt><form></form></dl>';
            const dl = $(html).appendTo(kdd_set_main);
            const form = dl.find('form');
            $(form).css({
                display: 'flex',
                flexWrap: 'wrap',
            });
            that.getKddTypeByExCode(domData.companyId, function(data) {
                // data = data.filter(o=>o!==2)
                const hasCainiaoType = data.includes(3); // 这个快递公司有菜鸟电子这个类型（比如顺丰暂时就没有菜鸟面单）
                switch (PLAT) {
                    case 'fxg':// 放心购平台
                        (mdlxList['8'] || {}).icon = iconUrl; // 给「抖音面单」加小火苗
                        break;
                    case 'ks':
                    case 'ksshopg':
                    case 'ksshop-pre':
                    case 'ksshop': // 快手平台
                        (mdlxList['9'] || {}).icon = iconUrl;// 给「快手电子」加小火苗
                        break;
                    case 'erp': // erp去掉小火苗
                        break;
                    default: // 其他平台
                        // 有菜鸟给菜鸟加小火苗，没有菜鸟给拼多多加
                        if (hasCainiaoType) {
                            (mdlxList['3'] || {}).icon = iconUrl;// 给「菜鸟电子」加小火苗
                        } else {
                            (mdlxList['7'] || {}).icon = iconUrl;// 给「拼多多电子」加小火苗
                        }
                        break;
                }
                $.each(data, function() {
                    let isDisabledObj = {
                        3: ['JD'],
                        5: ['DBKD'],
                        7: ['JD', 'DBKD'],
                        8: ['DBKD', 'JD'],
                        9: ['JD', 'DBKD'],
                    };
					let isDisabled = (params.modeZmj == 1 && isDisabledObj[this]?.includes(domData.excode));
                    if (this == 2 && comp.Print.Data.newPrintMode) isDisabled = true;
                    let mdlx; let htmldd = ''; let
dd;
                    if (domData.excode === 'JD' && this == 5) { // 这里的修改是因为 当初京东的类型为了方便，全部定成了5，后续的逻辑也是全部走5，然后根据详情接口里面的值来判定是走京东自营还是无界面单，所以现在这里暂时先这么处理，只修改这一个接口，后续再分开为5和6统一处理
                        mdlx = mdlxList[6];
                        htmldd = '<dd><div class="kdd_img_box"><img src="{0}"/><div  class=""></div></div><label><input type="radio" name="kddtype" class="input_radio" value="{2}" />{1}</label></dd>';
                        html = htmldd.format(mdlx.src, mdlx.text, 5); // FIXME
                        dd = $(html).appendTo(form).attr('kddtype', 5); // FIXME
                    } else {
                        mdlx = mdlxList[this];
                        if (mdlx) {
                            if (mdlx.icon) {
                                htmldd = '<dd><div class="kdd_img_box"><img src="{0}"/><div  class=""></div></div><label><input type="radio" name="kddtype" class="input_radio" value="{2}" />{1}<img src="{3}" style="vertical-align: text-bottom;width:13px;"/></label></dd>';
                                html = htmldd.format(mdlx.src, mdlx.text, this, mdlx.icon);
                            } else {
                                htmldd = `<dd><div class="kdd_img_box"><img src="{0}"/><div  class=""></div></div><label><input type="radio" name="kddtype" class="input_radio" value="{2}" ${(isDisabled || this === 2) ? 'disabled' : ''} />{1}</label></dd>`;
                                html = htmldd.format(mdlx.src, mdlx.text, this);
                            }

                            dd = $(html).appendTo(form).attr('kddtype', this);
                        }
                        if (this == 2) {
                            dd.attr('title', '聚合打印模版组暂时不支持网点面单打印；');
                        } else if (this == 3) {
                            dd.attr('title', '淘宝后台申请单号');
                        } else if (this == 7) {
                            dd.attr('title', '拼多多后台申请单号');
                        }
                    }
                });
                form.find('.input_radio').click(function() {
                    form.find('.cur').removeClass('cur');
                    form.find('.input_radio').prop('checked', false);
                    form.find('.xz_border_green').removeClass('xz_border_green');
                    const target = $(this).closest('dd');
                    target.addClass('cur');
                    target.find('.input_radio').prop('checked', true);
                    target.find('.kdd_img_box').find('div').addClass('xz_border_green');
                    dom.qlrr.clearDom(1);
                    domData.kddType = target.attr('kddtype');
                    if (domData.kddType == 1) {
                        domData.height = 0;
                        dom.qlrr.addYS(3);
                    } else {
                        dom.qlrr.addCC(3);
                    }
                });

                /**
                 * 1、data长度为1，默认选中第一个
                 * 2、data长度为2，data[0]==1:data[1]==0？选中第一个：选择第二个，data[0]!==1:选data[0]
                 * 3、data长度大于2，data[0]==1?有五联单，按data[1]来选,data[0]!==1:无五联单,选data[0]或不选，
                 */
                /**
                 * 【快手平台begin  】：ksshop
                 * 1、创建模板如果存在快手电子面单则默认选中快手模板
                 * 2、如果模板存在一联单默认选中一联单
                 */
                // 快手平台
                const KSPlatfrom = ['ksshop', 'ksshopg'];
                if (comp.Print.Data.platform === 'erp') {
                    console.log(form.find('.input_radio:enabled')[0]);
                    return form.find('.input_radio:enabled')[0].click();
                    // return form.find(`[kddtype=${data[0]}] .input_radio`).trigger('click');
                }
                if (data && data.length == 1) { // data长度为1，默认选中
                    form.find(`[kddtype=${data[0]}] .input_radio`).trigger('click');
                } else if (data && data.length == 2) { // data 长度为2，且第二个为0，默认选中第一个
                    if (KSPlatfrom.includes(PLAT) && data?.includes(9)) { // 创建模板如果存在快手电子面单则默认选中快手模板
                        const index = data?.findIndex(item => item === 9);
                        return form.find(`[kddtype=${data[index]}] .input_radio`).trigger('click');
                    }
                    if (data[0] == 0) { // 不存在五联单
                        return form.find(`[kddtype=${data[1]}] .input_radio`).trigger('click');
                    } else if (data[0] == 1) { // 存在五联单
                        if (data[1] == 0) {
                            return form.find(`[kddtype=${data[0]}] .input_radio`).trigger('click');
                        } else {
                            return form.find(`[kddtype=${data[1]}] .input_radio`).trigger('click');
                        }
                    } else { // 没有五联单
                        return form.find(`[kddtype=${data[0]}] .input_radio`).trigger('click');
                    }
                } else { // data长度大于2
                    if (KSPlatfrom.includes(PLAT) && data?.includes(9)) { // 创建模板如果存在快手电子面单则默认选中快手模板
                        const index = data?.findIndex(item => item === 9);
                        return form.find(`[kddtype=${data[index]}] .input_radio`).trigger('click');
                    }
                    data.map((v, i) => {
                        if (data[0] == 0) { // 不存在五联单 都不选
                            return;
                        } else if (data[0] == 1) { // 存在五联单
                            if (i == 1 && v == 0) { // 第二个值为0默认不选中
                                return;
                            } else if (i == 1 && v != 0) { // 否则选择第二个值的快递单 ,根据第二个值来判断选中快递单
                                return form.find(`[kddtype=${v}] .input_radio`).trigger('click');
                            }
                        } else { // 不存在五联单
                            return form.find(`[kddtype=${data[0]}] .input_radio`).trigger('click');
                        }


                    });
                }
            });
        };

        // 选择样式
        dom.qlrr.addYS = function(action) {
            const $dlDom = $('<dl class="kdd_ctmd_bz add-kddTemp-list" do=' + action + ' >' + this.getLoadingHtml() + '</dl>').appendTo(kdd_set_main);
            that.getKddStyle(domData.companyId, domData.kddType, domData.height, function(data) {
                let $html; let _html; let
form;

                const list = (data || {}).ExKddStyleList;
                if (!list || !list.length) {
                    $dlDom.remove();
                    return;
                }

                $html = $('<dt>选择面单样式</dt>'
                    + '<dd class="form_box_la">'
                    + '<form>'
                    + '</form>'
                    + '</dd>');
                form = $html.find('form');

                _html = '';
                $.each(list, function() {
                    _html += '<span styleid="' + this.StyleId + '"><label><input name="kddstyle" type="radio" class="input_check" value="' + this.StyleId + '"  stylename="' + this.StyleName + '"/>' + this.StyleName + '</label></span>';
                });
                form.html(_html);
                $dlDom.html($html);
                form.find('input[type=\'radio\']').change(function() {
                    const target = $(this);
                    domData.styleId = ~~target.val();
                    dom.qlrr.clearDom(action);
                    if (comp.Print.Data.platform == 'erp') {
                        // if(domData.excode == 'SF'){
                        //     dom.qlrr.addYwlx(action + 1);
                        //    if(domData.styleId === 45) dom.qlrr.addZzfw(action + 2);

                        // }
                        dom.qlrr.addYwlx(action + 1);
                        const needAddZzfwArr = ['SF', 'JD', 'EMS', 'POSTB', 'DBKD'];
                        const sfKddType = ['3', '5', '8', '9'];
                        // if (needAddZzfwArr.includes(domData.excode)) {
                            switch (domData.excode) {
                                case 'SF':
                                    dom.qlrr.addZzfw(action + 2);
                                    if (domData.kddType == '5') dom.qlrr.addFwlx(action + 2);
                                    break;
                                case 'JD':
                                    dom.qlrr.addZzfw(action + 1);
                                    break;
                                default:
                                     dom.qlrr.addZzfw(action + 1);
                            }
                            // if (domData.excode == 'SF') {
                            //     sfKddType.includes(domData.kddType) && dom.qlrr.addZzfw(action + 2)
                            //     if( domData.kddType == '5') dom.qlrr.addFwlx(action + 2)
                            // } else {
                            //     domData.kddType === '9' && dom.qlrr.addZzfw(action + 1);
                            // }
                        // }
                        if ((domData.excode == 'JD' && domData.kddType == 7) || domData.kddType == 3) {
                            dom.qlrr.addFwCaiNiaoPdd(domData.styleId, domData.excode);
                        } else if (domData.excode == 'JD' && ['5', '8'].includes(domData.kddType)) {
                            dom.qlrr.addFwlx();
                        } else if (domData.excode != 'JD' && domData.kddType == 7) {
                            dom.qlrr.addFwCaiNiaoPdd(domData.styleId, domData.excode);
						} else if (['2', '8', '9', '14', '16'].includes(domData.kddType)) {
                            if (domData.kddType == 9 && domData.excode == 'SF') {
                                dom.qlrr.addFwlx(7);
                            } else {
                                dom.qlrr.addFwlx();
                            }
                        }
                    } else if (domData.kddType == 1) {
                        dom.qlrr.addDT();
                    } else if (domData.kddType == 3 && comp.base.getTempStyle('cainiao', domData.styleId)) {
                        if (domData.excode == 'SF') { // 菜鸟顺丰添加业务类型选择
                            dom.qlrr.addYwlx(action + 1);
                        }
                        dom.qlrr.addFwCaiNiaoPdd(domData.styleId, domData.excode);
                    } else if (domData.kddType == 7) {
                        if (domData.excode == 'SF') {
                            dom.qlrr.addYwlx(action + 1);
                        }
                        dom.qlrr.addFwCaiNiaoPdd(domData.styleId, domData.excode);
                    } else if (domData.kddType != 7 && (domData.styleId <= 2 || domData.styleId == 14 || domData.styleId >= 16)) {
                        dom.qlrr.addYwlx();
                        dom.qlrr.addFwlx();
                        dom.qlrr.addZzfw();
                    } else if (domData.kddType == '2' && domData.styleId == '10') {
                        dom.qlrr.addFwlx(action + 1);
                        dom.qlrr.addZzfw(action + 2);
                    }
                    if (!params?.exCode) dom.qlrr.addFzsz(0);
                    form.find('label').removeClass('on');
                    target.parent().addClass('on');
                });

                if (list.length == 1) {
                    form.find('input[type=\'radio\']').first().prop('checked', true).change();
                } else if (list.length > 1) {
                    if (domData.excode == 'SF') {
                        form.find('span[styleid=\'62\']').find('input').prop('checked', true).change();
                    } else if (domData.excode == 'JD') {
                        form.find('span[styleid=\'56\']').find('input').prop('checked', true).change();
                    } else {
                        form.find('span[styleid=\'2\']').find('input').prop('checked', true).change();
                    }
                }
            });
        };

        // 选择底图
        dom.qlrr.addDT = function() {
            const html = '<dl class="kdd_ctmd_bz clearfix"><dt><span>4</span>选择底图</dt><dd class="xz_dt_form float_l"><form><label class="on"><input value="default" name="add_kdd_dt_group" type="radio" class="input_radio" checked="checked" />使用默认底图</label><label><input type="radio" value="local" name="add_kdd_dt_group" class="input_radio" />本地上传底图</label></form><a name="uploadnewdt" class="btn_white_middle posi_re margtop_10" style="display: none;">上传新底图<form><input type="file" class="input_file"></form></a><p><input type="button" style="display:none" class="btn_white_middle btn_delete" value="删除" style="margin-top:10px;"></p></dd><dd class="dzmd_up_dtimg_box"><div class="dzmd_up_dtimg"><img class="kddbgimg" src="#" /><i class="xz_right_icon"></i></div><p style="display:block;"><a class="prepage" href="javascript:void(0)">上一张</a><span class="pagespan">1&nbsp;/&nbsp;10</span><a class="nextpage" href="javascript:void(0)">下一张</a></p></dd></dl>';
            const dl = $(html).appendTo(kdd_set_main).attr('do', 4);
            let imgindex = 0;
            let dtImgs = [];
            const uploadData = {};
            dl.find('.input_radio').change(function() {
                const target = $(this);
                const val = target.val();
                if (val == 'default') {
                    dom.find('.btn_white_middle').hide();
                    loadDefault();
                } else {
                    dom.find('.btn_white_middle').show();
                    loadUser();
                }
            });
            const btn_upload_id = 'btn_' + (+new Date());
            const btn_upload = dl.find('[name=\'uploadnewdt\']').attr('id', btn_upload_id);
            const btn_delete = dl.find('.btn_delete');
            that.uploadImgAddKdd(btn_upload_id, 'addkdduploadimg', function(data) {
                data.companyId = domData.companyId;
                data.kddType = 1;
                data.styleId = domData.styleId;
                data.exuserId = comp.Print.Data.exuid;
            }, function(data) {
                dtImgs.push(data);
                imgindex = dtImgs.length - 1;
                showImg();
            });


            btn_delete.click(function() {
                const bgImgId = dl.find('.dzmd_up_dtimg').find('img').attr('key');
                that.delAddKddImg(bgImgId, function() {
                    dtImgs.splice(imgindex, 1);
                    imgindex--;
                    if (imgindex < 0) {
                        imgindex = 0;
                    }
                    showImg();
                });
            });

            dl.find('.prepage').click(function() {
                if (dtImgs.length == 0) {
                    return;
                }
                if (imgindex > 0) {
                    imgindex--;
                }
                showImg();
            });

            dl.find('.nextpage').click(function() {
                if (imgindex < (dtImgs.length - 1)) {
                    imgindex++;
                }
                showImg();
            });

            function loadDefault() {
                imgindex = 0;
                that.getKddBgImgBy(domData.companyId, domData.kddType, domData.styleId, 0, function(data) {
                    dtImgs = data;
                    showImg();
                });
            }

            function loadUser() {
                imgindex = 0;
                that.getKddBgImgBy(domData.companyId, domData.kddType, domData.styleId, comp.Print.Data.exuid, function(data) {
                    dtImgs = data;
                    showImg();
                });
            }

            function showImg() {
                if (imgindex >= 0 && imgindex < dtImgs.length) {
                    const obj = dtImgs[imgindex];
                    const backgroundUrl = comp.base.dealBackgroundUrl(obj.ImgSrc);
                    dl.find('.dzmd_up_dtimg').find('img').attr('src', backgroundUrl).attr('data-url', obj.ImgSrc)
.attr('key', obj.Eximgid);
                } else {
                    dl.find('.dzmd_up_dtimg').find('img').attr('src', '').attr('key', '');
                }
                dl.find('.pagespan').html((imgindex + 1) + '&nbsp;/&nbsp;' + (dtImgs.length));
            }

            loadDefault();
        };

        // 选择面单尺寸
        dom.qlrr.addCC = function(action) {
            const html = '<dl class="kdd_ctmd_bz add-kddTemp-list" do=' + action + '>'
                + '<dt>选择面单尺寸</dt>'
                + '<dd class="form_box_la"><form class="temp-size-list"></form></dd>'
                + '</dl>';
            const dl = $(html).appendTo(kdd_set_main);
            const form = dl.find('form');
            const htmltem = '<span><label><input name="kddsize" type="radio" kddSize="{4}" class="input_radio" value="{0}" />{3}*{1}cm{2}</label></span>';
            that.getKddSize(domData.companyId, domData.kddType, function(data) {
                let defaultDom = null;
                if (params.exCode) {
                    data = data.filter(o => {
                        return o.HeightPaper == params.paperHeight && o.WidthPaper == params.paperWidth;
                    });

                    if (!data.length) {
                        Tatami.showFail(`当前平台模板不存在${params.paperWidth / 100}*${params.paperHeight / 100}cm尺寸面单，请更换模板类型`);
                        return;
                    }
                }
                $.each(data, function() {
                    let parperName = '';
                        let _w;
                    // parperName赋值
                    if (this.IsDefault) {
                        parperName = '(推荐)';
                    } else if (this.HeightPaper == 1790 && this.WidthPaper == 750) { // TODO后期考虑放在后端配置。
                        parperName = '(便携式)';
                    }
                    // 纸张宽度赋值
                    if (this.WidthPaper) {
                        _w = this.WidthPaper / 100;
                    } else {
                        _w = (domData.excode == 'UAPEX' ? 9 : 10);
                    }
                    const html = htmltem.format(this.HeightPaper, this.HeightPaper / 100, parperName, _w, this.WidthPaper);
                    // 韵达的尺寸特殊处理
                    // if (domData.kddType == 2 && domData.excode.toLowerCase() == 'yunda') {
                    //     html = htmltem.format(this.HeightPaper,20, parperName,_w);
                    // }
                    const span = $(html).appendTo(form);
                    if (this.IsDefault) {
                        defaultDom = span;
                    }
                });
                const inputs = form.find('.input_radio');
                inputs.change(function() {
                    const target = $(this);
                    domData.height = target.val();
                    domData.width = target.attr('kddSize');
                    dom.qlrr.clearDom(action);
                    dom.qlrr.addYS(action + 1);
                    form.find('label').removeClass('on');
                    target.parent().addClass('on');
                });

                if (data.length == 1) {
                    form.find('.input_radio').first().prop('checked', true).change();
                } else if (defaultDom) {
                    defaultDom.find('.input_radio').first().prop('checked', true).change();
                }
            });
        };

        dom.qlrr.isServiceRadio = function(kddType, excode, styleId) {
            const isRadio = ['SF', 'JD', 'JDKY', 'SFKY'].includes(excode)
				|| (['JD', 'ZTO'].includes(domData.excode) && domData.kddType == 5)
                || (kddType == 2 && /^(SURE|CN7000001003751|ANEKY|DBKD)$/.test(excode))
                || (styleId == 47 && excode === 'ZTO')
                || (kddType == 8)||
				([16,14].includes(Number(kddType)) && excode === 'YTO');
            return isRadio;
        };

        // 快递业务类型
        dom.qlrr.addYwlx = function() {
            const $dlDom = $('<dl class="kdd_ctmd_bz add-kddTemp-list" do=5 >' + this.getLoadingHtml() + '</dl>').appendTo(kdd_set_main);
                const isRadio = dom.qlrr.isServiceRadio(domData.kddType, domData.excode, domData.styleId);
            that.getKddWorkType({
                excode: domData.excode,
                kddtype: domData.kddType,
                height: domData.height,
                styleId: domData.styleId,
            }, function(data) {
                const arr = ((data || [])[0] || {}).ExServiceTypes;
                if (!arr || !arr.length) {
                    $dlDom.remove();
                    return;
                }
                const $html = $('<dt>' + (isRadio ? '快递业务类型' : '快递业务类型(选填)') + '</dt><dd class="form_box_la"><form></form></dd>');
                const form = $html.find('form');
                const htmltem = '<span><label><input name="kddworktype" type="{2}" class="input_check" value="{0}"/>{1}</label></span>';

                let domType; let defaultInputs = []; let
_input;
                domType = isRadio ? 'radio' : 'checkbox';
                $.each(arr, function() {
                    if (this.Key == 'lywxj' || this.Key == 'hkwxj') { //  微小件增加 tooltip 提示
                        const $span = $(htmltem.format(this.Key, this.Name, domType)).appendTo(form);
                        $span.find('label').addClass('toolTip').attr('data-tip-content', '使用微小件要求重量必须小于等于0.5kg，软件默认重量0.5kg');
                        _input = $span.find('input').data('data', this);

                    } else {
                        _input = $(htmltem.format(this.Key, this.Name, domType)).appendTo(form).find('input').data('data', this);
                    }

                    if (this.IsDefault) {
                        defaultInputs.push(_input);
                    }
                });

                // 最后一步：添加dom到body 减少dom渲染。
                $dlDom.html($html);

                comp.base.toolTip($dlDom);

                function recalWorkType() {
                    domData.workTypes = [];
                    form.find('.input_check').each(function(ind, inv) {
                        const target = $(inv);
                        if (target.prop('checked')) {
                            const tarData = target.data('data');
                            domData.workTypes.push(tarData);
                            target.parent().addClass('on');
                            if (tarData.Name == '代收货款') {
                                dom.find('input:checkbox[value=\'ali_waybill_serv_cod_amount\']').prop('checked', true).change();
                            }
                        } else {
                            target.parent().removeClass('on');
                        }
                    });
                }
                form.find('input:checkbox').change(recalWorkType);
                if (arr.length && isRadio) {
                    form.find('.input_check:eq(0)').prop('checked', true);
                    form.click();
                } else if (defaultInputs.length) {
                    defaultInputs.each(function(ind, input) {
                        input.prop('checked', true);
                    });
                    recalWorkType();
                }
                dom.qlrr.limitSelectWorktype($dlDom);
            });
        };

        /**
         * 关于快递业务类型的一些限制
         * - 中通二联单冷链业务，会限制其它的所有选项
         */
        dom.qlrr.limitSelectWorktype = ($dom) => {
            $dom.change((evt) => {
                const $el = $(evt.target);
                const value = $el.prop('value');
                const isSelectValue = $el.prop('type') === 'checkbox' && (value === 'wd_ll' || value === 'wd_wj');
                if (!isSelectValue) {
                    return;
                }

                const $container = $('.kdd_ctmd_bz[do=5],.kdd_ctmd_bz[do=6],.kdd_ctmd_bz[do=7]');
                const isChecked = $el.prop('checked');

                const otherSelector = `[type="checkbox"][value!=${value}]`;
                if (isChecked) {
                    $container.find(otherSelector).prop('checked', false).prop('disabled', true).parent()
.removeClass('on');
                } else {
                    $container.find(otherSelector).prop('disabled', false);
                }
            });
        };

        dom.qlrr.getLoadingHtml = function() {
            return '<span class="loading add-temp-loading"><i class="icon-load"></i>加载中…</span>';
        };

        // 菜鸟|拼多多 物流服务
        dom.qlrr.addFwCaiNiaoPdd = function(styleId, exCode) {
            let $dlDom = $('<dl class="kdd_ctmd_bz add-kddTemp-list">' + this.getLoadingHtml() + '</dl>').appendTo(kdd_set_main);
                 let isNotKuaiyun;
                 let kddType = domData.kddType;
            if (kddType == 7) {
                isNotKuaiyun = !comp.base.getPddTempStyle('pddKuaiYunZmj', styleId, exCode);
            } else {
                isNotKuaiyun = !comp.base.getTempStyle('kuaiyun', styleId);
            }
            that.getKddServiceCaiNiao({
                exCompanyId: domData.companyId,
                heightPaper: domData.height,
                kddType,
                styleId,
            }, function(data) {
                let _hasCodService = false; let
_$html;
                if (!data || !data.length) {
                    $dlDom.remove();
                    return;
                }
                $.each(data, function(_i, _v) {
                    if (_v && (_v.serviceInfoDto || {}).serviceCode === 'SVC-COD' && isNotKuaiyun) { // 代收货款增加文字说明
                        _hasCodService = true;
                        return false;
                    }
                });

                let serialNumber = 5; // 本项的序号是多少，默认是是5，顺丰 5已经有了，所以改为 6
                if (exCode == 'SF') {
                    serialNumber = 7;
                }

                _$html = $('<dt>快递服务选项<span class="svc-cod-info" style="' + (_hasCodService ? '' : 'display:none;') + 'color: #d75fd2;font-weight: normal;margin-left: 16px;">(货到付款服务由订单类型决定，货到付款订单默认打印货到付款服务)</span></dt>'
                    + '<dd class="form_box_la"><div class="kdd_ctmd_bz_form"></div></dd>');
                _$html.find('.kdd_ctmd_bz_form').html(resObj.getCainiaoServiceHtml(data, true, isNotKuaiyun, false, kddType));
                $dlDom.html(_$html).attr('do', serialNumber);
                $dlDom.find('.input_check[isdefault=\'1\']').prop('checked', true).change();
            });
        };


        dom.qlrr.isMustChooseWf = function(name) {
            let isDefault = false; let
isDisabled = false;
            if (name == '付款方式') {
                isDisabled = /^(SURE|CN7000001003751|ANEKY|DBKD)$/.test(domData.excode) && domData.kddType == 2;
                isDefault = domData.excode == 'SF' || /^(SURE|CN7000001003751|ANEKY|DBKD)$/.test(domData.excode) && domData.kddType == 2;
            } else if (name == '送货方式' || name == '包装类型') {
                isDisabled = (/^ANEKY|DBKD$/.test(domData.excode) && domData.kddType == 2)
                || (/CN7000001021040/.test(domData.excode) && domData.kddType == 8 && name === '送货方式');
                isDefault = isDisabled;
            } else if (name == '服务类型' || name == '派送类型') {
                isDisabled = /^JTSD|LB$/.test(domData.excode) && domData.kddType == 2; // 极兔速递网点
                isDefault = isDisabled;
            }
            // 众邮快递的几个服务
            else if (name === '运费支付方式' || name === '揽收方式' || name === '派送方式') {
                isDisabled = /^(ZYKD)$/.test(domData.excode) && domData.kddType == 2; // 众邮网点
                isDefault = /^(ZYKD)$/.test(domData.excode);
            } else if (name === '配送类型' || name == '运输类型') {
                isDisabled = domData.kddType == 5 && /^(JD)/.test(domData.excode) && /^(54|56|65|70|57|120)/.test(domData.styleId);
                isDefault = isDisabled;
            } else if (name === '支付方式') { // 加运美
                isDisabled = /^SZKKE$/.test(domData.excode) && domData.kddType == 2;
                isDefault = isDisabled;
            } else if (name === '代收货款' || name === '保价金额') { // ,京广
                isDisabled = (/^SZKKE$/.test(domData.excode) && domData.kddType == 2)
                || (/CN7000001021040/.test(domData.excode) && domData.kddType == 8 && name === '保价金额') || (/JD/.test(domData.excode) && domData.kddType == 5 && name === '代收货款');
                isDefault = isDisabled;
            } else if (name === '业务类型') {
                isDisabled = /CN7000001021040/.test(domData.excode) && domData.kddType == 8;
                isDefault = isDisabled;
            }
            return {
                isDisabled,
                isDefault: isDefault ? 1 : 0,
            };
        };

        // 快递服务选项
        dom.qlrr.addFwlx = function(action) {
            const num = action || 6;
            let $dlDom = $('<dl class="kdd_ctmd_bz add-kddTemp-list" do=' + num + ' >' + this.getLoadingHtml() + '</dl>').appendTo(kdd_set_main);
                let $html;
                let tipInfo;
                let isRadio;
            isRadio = domData.excode == 'SF'
                || (/^(SURE|CN7000001003751|ANEKY|DBKD|YUNDA|YTO|JTSD|LB|ZYKD|SZKKE)$/.test(domData.excode) && domData.kddType == 2) // 顺丰 速尔网点 跨越网点等 走快递业务服务必选
                || (domData.kddType == 5 && /^(JD)/.test(domData.excode))
                || (domData.kddType == 8 && /^(CN7000001021040)/.test(domData.excode));
            that.getKddServiceType(domData.excode, domData.kddType, domData.styleId, function(data) {
                $html = $('<dt>' + (isRadio ? '快递服务选项' : '快递服务选项(选填)') + '</dt><dd class="form_box_la"><div class="kdd_ctmd_bz_form"></div></dd>');
                let form = $html.find('.kdd_ctmd_bz_form'); let _html; let _flag;
                    let arr = ((data || [])[0] || {}).ExServiceItems;
                if (!arr || !arr.length) {
                    $dlDom.remove();
                    return;
                }
                $.each(arr, function() {
                    const name = this.Name.contain(':') ? this.Name.split(':')[0] : this.Name;
                    const key = this.Key;
                    let ohtml = '';
                    if (this.Value && this.Value.contain('|')) {
                        const vals = this.Value.split('|');
                        ohtml = '<div style=\'display:none\'>';
                        vals.each(function(ind, inv) {
                            const label = inv.contain(':') ? inv.split(':')[0] : inv;
                            if (inv == '声明价值保价') {
                                ohtml += '<em class="form_box_la_em"><label><input type="radio" name="' + key + '" class="input_radio" value="' + inv + '">' + label + '</label><div id="kddservicetype_baojia" style="display:none;line-height:25px;margin-top:3px;"><input type="text" style="width:90px" name="Tips" /><br/>(1元-20000元)</div></em>';
                            } else if (inv.match(/\[(.+)\]/)) { // 下拉选择示例数据=》 指定托寄物:['服装鞋包','美妆饰品']
                                let options = inv.match(/\[(.+)\]/)[1].split(',');
                                ohtml += `<em class="form_box_la_em">
                                    <label><input type="radio" name="${key}" class="input_radio" value="${inv}">${label}</label>
                                    <div class="form_box_la_em" id="kddservicetype_zdtjw" style="display: none;">
                                        <select style="width: 90px;" name=${key}>`;
                                let optionChilds = '';
                                options && options.length && options.each(function(index, option) {
                                    if (name === '托寄物' && option === '自定义内容') {
                                        optionChilds = `<div class="form_box_la_em">
                                            <input name=${name}${option} id="kddservicetype_zdtjw_zdy" style="display:none"
                                            placeholder="请输入托寄物内容" maxlength="20"/>
                                        </div>`;
                                    }
                                    ohtml += `<option value=${option}>${option}</option>`;
                                });
                                ohtml += `</select></div>${optionChilds}</em>`;
                            } else {
                                const labelToolTip = label.includes('订单商品作为托寄物')
                                ? `<img src="https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/wenhao.png"
                                class="toolTip v-middle" data-tip-content="选择后将按订单商品信息作为托寄物，\n商品有简称传简称，没有简称传标题。"/>` : '';
                            ohtml += `<em class="form_box_la_em"><label>
                                <input type="radio" name="${key}" class="input_radio" value="${inv}">${label}</label>
                                ${labelToolTip}</em>`;
}
                        });
                        ohtml += '</div>';
                    }
                    if (isRadio) {
                        _flag = dom.qlrr.isMustChooseWf(name);
                        tipInfo = name == '保价金额' ? '用于贵重物品，如有丢损，快递将按声明价值和损失比例赔偿，详情需咨询网店' : (name == '代收货款' ? '代收货款服务由订单类型决定，代收货款订单默认打印代收货款服务' : '');
                        this.IsDefault = _flag.isDefault; // 后期优化 这个值控制有后端来做
                        this.isDisabled = _flag.isDisabled;
                    }
                    _html = '<span><label class="toolTip" data-tip-content=' + (tipInfo || '') + ' ><input isdefault="' + this.IsDefault + '"  ' + (this.isDisabled ? 'disabled' : '') + '  name="kddservicetype"  type="checkbox" class="input_check" value="' + key + '"/>' + name + '</label>' + ohtml + '</span>';
                    $(_html).appendTo(form).find('input').data('data', this);
                });
                $dlDom.html($html);
                // 注册提示事件
                comp.base.toolTip($html);
                const chks = form.find('.input_check');
                // 韵达保价金额和代收贷款 二选一
                kdd_set_main.on('click', '.input_check', function(e) {
                    if (domData.kddType == '2' && domData.styleId == '10') {
                        if ($(this).prop('value') == 'ali_waybill_serv_insure_amount') {
                            if (kdd_set_main.find('[value=dshk]').prop('checked')) e.preventDefault();
                        } else if ($(this).prop('value') == 'dshk') {
                            if (kdd_set_main.find('[value=ali_waybill_serv_insure_amount]').prop('checked')) e.preventDefault();
                        }
                    }
                });
                chks.unbind().change(function(e) {
                    domData.serviceTypes = [];
                    chks.each(function(ind, inv) {
                        const target = $(inv);
                        const tarParent = target.parent();
                        const nextDom = tarParent.next();
                            const _isCheck = target.prop('checked');
                        if (_isCheck) {
                            const tarData = target.data('data');
                            domData.serviceTypes.push(tarData);
                            tarParent.addClass('on');
                            if (nextDom.length) {
                                nextDom.myShow();
                                const inputs = nextDom.find('input:radio');
                                tarData.Value = '';
                                inputs.each(function(ind, inv) {
                                    const tarInput = $(inv);
                                    if (tarInput.prop('checked')) {
                                        tarData.Value = tarInput.val();
                                    }
                                });

                                if (!tarData.Value) {
                                    const firstInput = inputs.first();
                                    tarData.Value = firstInput.val();
                                    firstInput.prop('checked', true).change();
                                }
                            }
                        } else {
                            tarParent.removeClass('on');
                            if (nextDom.length) {
                                nextDom.myHide();
                                nextDom.find(' input').prop('checked', false);
                            }
                        }
                    });
                    // 根据付款方式判断月结卡号是否disabled
                    const _isFkfs = /^(SF|CN7000001003751)$/.test(domData.excode) && $(this).val() == 'ali_waybill_ext_payment_type';
                    _isFkfs ? $(this).closest('.kdd_ctmd_bz_form').find('[value="ali_waybill_ext_custom_code"]').attr('disabled', !!$(this).prop('checked')) : '';

                    // 顺丰抖音面单，付款方式不能取消勾选
                    if (/^(SF)$/.test(domData.excode)) {
                        (domData.kddType == 8) && $(this).closest('.kdd_ctmd_bz_form').find('[value="ali_waybill_ext_payment_type"]').prop('checked', true)
.attr('disabled', true);
                    }
                });

                form.find('input[type=\'radio\']').change(function() {
                    chks.change(); // 可优化
                    let tarDom = $(this);
                        let _$cusId = tarDom.closest('.form_box_la').find('input[value="ali_waybill_ext_custom_code"]');
                        let _name;
                        let _cusIdCheck = false;
                        let _exCode = domData.excode;
                    tarDom.parent().find('input[type=\'radio\']').removeClass('on');
                    tarDom.addClass('on');
                    _name = tarDom.val();
                    if (tarDom.attr('name') == 'ali_waybill_serv_insure_amount') {
                        if (_name == '声明价值保价') {
                            $('#kddservicetype_baojia').show();
                        } else {
                            $('#kddservicetype_baojia').hide();
                        }
                    } else if (tarDom.attr('name') === 'ali_waybill_serv_tjw') { // 托寄物
                        if (_name.includes('指定托寄物')) { // 指定托寄物下拉选择
                            $('#kddservicetype_zdtjw').show();
                            form.find('select').get(0).selectedIndex = 0;
                            form.find('select').change();
                        } else {
                            // 隐藏指定托寄物下拉选择框和自定义托寄物输入框
                            $('#kddservicetype_zdtjw').hide();
                            $('#kddservicetype_zdtjw_zdy').hide().val('').removeAttr('class');
                        }
                    } else {
                        // 月结卡号服务是否勾选处理  顺丰，跨越
                        if (_exCode === 'SF') { // 顺丰的寄付现结 到付现结
                            _cusIdCheck = /^(寄付月结|转第三方付)$/.test(_name);
                        } else if (_exCode === 'CN7000001003751') { // 跨越的寄付现结 到付现结
                            _cusIdCheck = /^(寄付|寄付月结|转第三方付款)$/.test(_name);
                        }
                        _$cusId.prop('checked', _cusIdCheck);
                    }
                });
                form.find('.form_box_la_em select').change(function() { // 三级下拉选择框数据处理
                    let value = $(this).val();
                    // 获取对应父级复选框，并更新data数据
                    const parents = $(this).parentsUntil('div.kdd_ctmd_bz_form');
                    const parentCheckbox = $(parents[parents.length - 1]).find("input[name='kddservicetype']");
                    let parentData = parentCheckbox.data('data');
                    if (parentData.Name === '托寄物' && value === "自定义内容") { // 指定托寄物自定义内容，以下级文本输入框内容为准
                        $('#kddservicetype_zdtjw_zdy').show().attr('class', 'input_noempty');
                    } else {
                        parentData.Value = value;
                        $('#kddservicetype_zdtjw_zdy').hide().val().removeAttr('class');
                    }
                });
                form.find('#kddservicetype_zdtjw_zdy').change(function() { // 指定托寄物自定义内容，文本输入框
                    let value = $(this).val();
                    // 获取对应父级复选框，并更新data数据
                    const parents = $(this).parentsUntil('div.kdd_ctmd_bz_form');
                    const parentCheckbox = $(parents[parents.length - 1]).find("input[name='kddservicetype']");
                    let parentData = parentCheckbox.data('data');
                    parentData.Value = value;
                });
                form.find('.input_check[isdefault=\'1\']').prop('checked', true).change();
            });
        };

        // 增值服务
        dom.qlrr.addZzfw = function(action) {
            const num = action || 7;
            const $dlDom = $('<dl class="kdd_ctmd_bz addTemp-fuwu-accretion" do=' + num + '>' + this.getLoadingHtml() + '</dl>').appendTo(kdd_set_main);
            that.getExAdvancedServices({
                excode: domData.excode,
                kddtype: domData.kddType, // 不传递默认为网点
                styleId: domData.styleId,
            }, function(data) {
                let arr = ((data || [])[0] || {}).ExServiceItems;
                const resCode = ((data || [])[0] || {}).Code;
                if (!arr || !arr.length) {
                    $dlDom.remove();
                    return;
                }
                const $html = $('<dt>增值服务</dt><dd class="form_box_la"><form></form></dd>');
                const form = $html.find('form');
                let htmltem = '';
                // arr = arr.filter(o => o.Key !== 'zmj')
                $.each(arr, function() {
                    const name = this.Name;
                    const key = this.Key;
                    const isKHBM = key === 'khbm' || name === '客户编码';
                    const isFKFS = key === 'fkfs' || name === '付款方式';
                    const isYJKH = key === 'yjkh' || name === '月结卡号';
                    htmltem = '<span style="{4}"><label><input idDefault="{3}" name="AdvancedServices"  type="checkbox" class="input_check" value="{0}"/>{1}</label>{2}</span>';
                    if (isKHBM || (domData.kddType == 2 && domData.excode == 'SZKKE')) { // 顺丰客户编码 必填
                        let isChecked = 'disabled checked';
                        if (isKHBM && ['3'].includes(domData.kddType)&&['SF'].includes(resCode)) isChecked = '';
                        htmltem = `<span style="{4}"><label><input idDefault="{3}" name="AdvancedServices"  type="checkbox" class="input_check" value="{0}" ${isChecked}/>{1}</label>{2}</span>`;
                    }
                    if (['3', '9', '13'].includes(domData.kddType) && ['SF', 'DBKD', 'JD', 'JDKY'].includes(resCode) && (isFKFS || isYJKH)) {
                        let isChecked = 'disabled checked';
                        if (['3'].includes(domData.kddType) && isFKFS) isChecked = '';
                        if(isYJKH && ['3'].includes(domData.kddType) &&  ['SF'].includes(resCode)) isChecked = ''
                        htmltem = `<span style="{4}"><label><input idDefault="{3}" name="AdvancedServices"  type="checkbox" class="input_check" value="{0}" ${isChecked}/>{1}</label>{2}</span>`;

                    }
                    if (params.exCode && key == 'zmj') {
                        htmltem = `<span style="{4}"><label><input idDefault="{3}" name="AdvancedServices"  type="checkbox" class="input_check" value="{0}" disabled ${params?.modeZmj == '1' ? 'checked' : ''}/>{1}</label>{2}</span>`;

                    }
					// 新版小红书全部禁用并提示
					if(domData.kddType == 16 && isYJKH){
						 let tipsHtml = `<i class="iconfont ifont-wenhao" style="font-size:12px;cursor:pointer;padding-left:5px;" title="自动获取网点关联月结卡号"></i>`
                        htmltem = `<span style="{4}"><label><input idDefault="{3}" name="AdvancedServices" disabled  type="checkbox" class="input_check" value="{0}"checked />{1}${tipsHtml}</label>{2}</span>`;
					}
                    let ohtml = ''; let
_html;
                    if (this.Value.contain('|')) {
                        const vals = this.Value.split('|');
                        ohtml = '<div class=\'serverContent\' style=\'display:none\'>';
                        if (isFKFS && domData.kddType === '9' && resCode === 'SF') {
                            ohtml = '<div class=\'serverContent\' style=\'display:block\'>';
                        }
                        vals.each(function(ind, inv) {

                            if (inv.contain(':') || inv.contain('#')) {
                                let arr = inv.split('#');
                                     let content = '';
                                     let hasInput = false;
                                     let canChecked = true;
                                     let itemName;
                                     let subItem;
                                     let subItemList;
                                     let subItemHtml = '';

                                arr[0] && (content = arr[0]);
                                arr[1] && (canChecked = Number(arr[1]));// 第一个 # 后的数字表示能否被选中
                                arr[2] && (hasInput = Number(arr[2])); // 第二个 # 后的数字表示有没有输入框

                                itemName = content.split(':')[0];
                                if (content.contain(':')) { // 有子项
                                    subItem = content.split(':')[1];
                                    subItemList = subItem.split(',');
                                    $.each(subItemList, function(index, item) {
                                        subItemHtml += '<label style="display:block;"><input class="input_radio" type="radio"  name="refundType" value="' + item + '">' + item + '</label>';
                                    });
                                }

                                ohtml
                                    += '<em class="form_box_la_em">'

                                    + '<label>'
                                    + (canChecked
                                        ? ('<input type="radio" name="' + key + '" class="input_radio" value="' + itemName + '">' + itemName)
                                        : ('<div>' + itemName + '</div>'))
                                    + (hasInput
                                        ? ('<input class="input_check input_noempty" style="' + (itemName == '声明价值保价' ? 'display: none;' : '') + 'margin-top:3px;" name="' + itemName + '" type="text">')
                                        : (''))
                                    + (itemName == '声明价值保价'
                                        ? ('<div style="display:none; margin-top:3px;" name="tips">(1元-20000元)</div>')
                                        : ('')
                                    )
                                    + subItemHtml
                                    + '</label>'
                                    + '</em>';
                            } else if (name == '包装费' || name == '标准化包装服务') {
                                    ohtml += '<em class="form_box_la_em"><label>'
                                        + '<input type="radio" name="' + key + '" class="input_radio" value="' + inv + '">' + inv
                                        + '</label>'
                                        + '<div class="money-input" style="display:none;"><input type="text" style="width:90px" name="bzfTip" /><br>(1元-500元)</div>'
                                        + '</em>';
                                } else if (isFKFS && domData.kddType === '9' && resCode === 'SF' && ind === 0) {
                                        ohtml += '<em class="form_box_la_em"><label><input type="radio" name="' + key + '" class="input_radio" value="' + inv + '" checked>' + inv + '</label></em>';
                                    } else {
                                        ohtml += '<em class="form_box_la_em"><label><input type="radio" name="' + key + '" class="input_radio" value="' + inv + '">' + inv + '</label></em>';
                                    }

                        });
                        ohtml += '</div>';
                    } else if (this.Value == '#1#1') {
                        ohtml = '<div class=\'serverContent\' style=\'display:none\'>';
                        if (isKHBM) { // 顺丰客户编码 必填
                            let isDisplay = 'block';
                            if (['3'].includes(domData.kddType) && ['SF'].includes(resCode)) isDisplay = 'none';
                            ohtml = `<div class=\'serverContent\' style=\'display:${isDisplay}\'>`;
                        }
                        if (isYJKH && ['3', '9', '13'].includes(domData.kddType) && ['JD', 'SF', 'DBKD', 'JDKY'].includes(resCode)) { // 顺丰客户编码 必填
                            let isDisplay = 'block';
                            if (['3'].includes(domData.kddType) && ['SF'].includes(resCode)) isDisplay = 'none';
                            ohtml = `<div class=\'serverContent\'  style=\'display:${isDisplay}\'>`;
                        }
						if(isYJKH && domData.kddType == 16){
                            ohtml = '<div class=\'serverContent\'  style=\'display:block\'>';
						}
                        ohtml+=`<input class="input_check input_noempty" ${domData.kddType == 16 ? 'disabled' : ''}  style="margin-top:3px;" name="${name}" type="text">`

                        ohtml += '</div>';

                    }

                    if (name == '包装费' || name == '标准化包装服务') {
                        _html = htmltem.format(key, name + '<font style=\'color: gray; margin-left: 5px;\'>(与顺丰销售确认)</font>', ohtml, this.IsDefault, 'width:160px');
                    } else if (name === '代收货款(对总模式)' && domData.excode === 'ZTO') {
                            ohtml = `<img src="/resources/img/print/wenhao.png" class="toggle_tip" data-toggle-tip="accurate-tip" data-tip-title="代收货款（对总模式）" data-tip-content="请联系快递开通服务后再使用，联系方式：021-31080220" style="position: relative;top: 0px;cursor: pointer;left: 5px;">`;
                            const $content = $('.add_temp_content');
                            $content.change(function(e) {
                                const $this = $(e.target);

                                const $dshkRadio = $content.find('[name=kddworktype][value=wd_dshk]');
                                const $dshkCheckbox = $content.find('[name=kddworktype][value=dshk]');
                                const $dshk = $dshkRadio.length ? $dshkRadio : $dshkCheckbox;
                                const $checkbox = $content.find('[name=AdvancedServices][value=dshk]');

                                const isKddworktype = $this.attr('name') === 'kddworktype';
                                const isCheckbox = $this.is($checkbox);

                                const el1300 = $content.find('[name=kddsize][value=1300]')[0];
                                const is1300 = el1300 && el1300.checked;
                                // const el1800 = $this.find('[name=kddsize][value=1800]')[0];
                                // const is1800 = el1800 && el1800.checked;

                                if (is1300) {
                                    if (isCheckbox) {
                                        if ($this[0].checked) {
                                            $content.find('[name=kddworktype][type=radio]').attr('checked', false).attr('disabled', true);
                                            const el = $content.find('[name=kddworktype][type=radio][value=wd_ptdd]')[0];
                                            el && (el.checked = true);
                                        } else {
                                            $content.find('[name=kddworktype][type=radio]').attr('disabled', false);
                                        }
                                    }
                                } else if (isCheckbox) {
                                        if ($this[0].checked) {
                                            $dshk.attr('disabled', true);
                                            $dshk.attr('checked', false);
                                        } else {
                                            $dshk.attr('disabled', false);
                                        }
                                    } else if (isKddworktype && $this.attr('type') === 'radio') {
                                        if ($this.val() === 'wd_dshk') {
                                            $checkbox.attr('disabled', true);
                                            $checkbox.attr('checked', false);
                                        } else {
                                            $checkbox.attr('disabled', false);
                                        }
                                    } else if (isKddworktype && $this.attr('type') === 'checkbox' && $this.val() === 'dshk') {
                                        if ($this[0].checked) {
                                            $checkbox.attr('disabled', true);
                                            $checkbox.attr('checked', false);
                                        } else {
                                            $checkbox.attr('disabled', false);
                                        }
                                    }
                            });

                            _html = htmltem.format(key, name, ohtml, this.IsDefault, 'width: 140px');
                        } else {
                            _html = htmltem.format(key, name, ohtml, this.IsDefault);
                        }
                    const $el = $(_html);
                    $el.appendTo(form).find('input').data('data', this);

                    if (dom.qlrr.defaultIsCheck(domData, this)) {
                        $el.find('input').prop('checked', true);
                    }

                    $el.find('.toggle_tip').toastTip();
                });

                $dlDom.html($html);

                const chks = form.find('.input_check');
                chks.unbind().change(function() {
                    domData.advancedServices = [];
                    chks.each(function(ind, inv) {
                        const target = $(inv);
                        const tarParent = target.parent();
                        let nextDom = tarParent.next();
                        if (nextDom.length === 1 && nextDom[0].tagName === 'IMG') {
                            nextDom = $();
                        }
                        if (target.prop('checked')) {
                            const tarData = target.data('data');
                            domData.advancedServices.push(tarData);
                            tarParent.addClass('on');
                            if (nextDom.length) {
                                nextDom.myShow();
                                if (nextDom.find('input[type=\'radio\']:checked').length == 0) {
                                    nextDom.find('input[type=\'radio\']').first().prop('checked', true);
                                }
                                const inputs = nextDom.find('input:radio,input:text'); // 获取两种 input
                                tarData.Value = '';
                                const valueList = [];
                                inputs.each(function(ind, inv) {
                                    const tarInput = $(inv);
                                    if ((tarInput.attr('type') == 'radio' && tarInput.prop('checked')) || (tarInput.attr('type') == 'text') && tarInput.val()) {

                                        valueList.push(tarInput.val());
                                    }
                                });
                                tarData.Value = valueList.join('|');

                                if (!tarData.Value) {
                                    const firstInput = inputs.first();
                                    tarData.Value = firstInput.val();
                                    firstInput.prop('checked', true).closest('em').find('.money-input').show();
                                }
                            }
                        } else {
                            tarParent.removeClass('on');
                            if (nextDom.length) {
                                nextDom.myHide();
                                nextDom.find('input').prop('checked', false);
                            }
                        }

                    });
                });

                form.find('input[type=\'radio\']').change(function() {
                    chks.change();
                    const tarDom = $(this);
                    tarDom.parent().find('input[type=\'radio\']').removeClass('on');
                    tarDom.addClass('on');
                    form.find('.money-input').hide();

                    if (tarDom.val() === '个性包装费') { // 只有个性包装费才展示输入框
                        tarDom.closest('em').find('.money-input').show();
                    }


                    if (tarDom.val() == '声明价值保价') {
                        tarDom.closest('label').children().show();
                    } else {
                        tarDom.closest('span').find('[name="tips"]').hide();
                        tarDom.closest('span').find('[name="声明价值保价"]').hide();
                    }
                });

                let defchks = form.find('.input_check[isdefault=\'1\']');
                if (domData.excode == 'SF' && defchks.length == 0) {
                    defchks = form.find('.input_check[name=\'kddservicetype\']:eq(0)');
                }
                if (defchks.length) {
                    defchks.prop('checked', true).change();
                }
            });
        };
        // 分组设置
        dom.qlrr.addFzsz = function() {
            // let action = kdd_set_main.children(':last-child')
            console.log(domData);
            let currentSelectExCode = domData.excode;
            const html = '<dl class="kdd_ctmd_bz add-kddTemp-list" >'
            + '<dt>分组设置</dt>'
            + '<dd class="form_box_la"><form style="display:flex">'
            + '</form></dd>'
            + '</dl>';
            const dl = $(html).appendTo(kdd_set_main);
            const form = dl.find('form');
            const tempGroupList = comp.Print.Data.kddTemplates || [];
            let groups = tempGroupList.filter(o => {
                return (o.exCode === currentSelectExCode) && (o.paperHeight == domData.height) && (o.paperWidth == domData.width);
            });
            let groupSelectHtml = '';
            if (groups.length) {
                groups.forEach(o => {
                    groupSelectHtml += `<option value="${o.id}">${o.groupName}</option>`;
                });
            } else {
                groupSelectHtml = `<option value="null">暂无有效模板组</option>`;
            }

            let domHtml = `
                <div style="width:210px">
                    <label>
                        <input  name='kddFzsz'  type="radio" class="input_radio" value="1" ${groups.length ? '' : 'checked'} />加入新的模板组
                    </label>
                </div>
                <div>
                    <label>
                        <input ${groups.length ? '' : 'disabled'}  name='kddFzsz' type="radio" class="input_radio" value="2" ${groups.length ? 'checked' : ''} />加入已有模板组
                    </label>
                    <select style="width:120px;height:20px;margin-left:10px" class="fzsz-select-group" ${groups.length ? '' : 'disabled'} >
                        ${groupSelectHtml}
                    </select>
                </div>
              `;

            $(domHtml).appendTo(form);
        };

        // 检测是否默认勾选
        dom.qlrr.defaultIsCheck = function(domData, serviceItem) {
            // 众邮
            if (domData.excode == 'ZYKD') {
                // 代收货款
                if (serviceItem.Key == 'dshk') {
                    return true;
                }
            }

            return false;
        };

        btnOk.click(function() {
            let obj = {};
                 let groupObj = {};
                 let isError;
                 let invDom;
                 let tem;
                 let temDom;
            if (kdd_set_main.find('.add-temp-loading').length) {
                Tatami.showFail('数据加载中，请稍后重试'); // 或者更改为异步等待重试模式，等待数据加载完成, 取消保存取消循环重试
                return;
            }
            let isGroupAddTemp = dom.find('input[name=\'kddFzsz\']:checked').val();
            obj.ExUserId = comp.Print.Data.exuid;
            obj.SubUserId = comp.Print.Data.exsubuid;
            obj.ExCompanyId = dom.find('#addExCompany .drop-down-value').attr('data-value');
            obj.KddType = dom.find('input[name=\'kddtype\']:checked').val();
            obj.StyleId = dom.find('input[name=\'kddstyle\']:checked').val();
            obj.ExpressStyleName = dom.find('input[name=\'kddstyle\']:checked').attr('stylename'); // 众邮双条码需要根据stylename判断
            obj.Height = dom.find('input[name=\'kddsize\']:checked').val();
            obj.Width = dom.find('input[name=\'kddsize\']:checked').attr('kddSize');
            groupObj.groupId = '';
            groupObj.exCompanyId = obj.ExCompanyId;
            groupObj.paperHeight = obj.Height;
            groupObj.paperWidth = obj.Width;
            groupObj.modeZmj = domData.companyType == 2 ? '1' : '0';
            temDom = dom.find('.kddbgimg');
            obj.BgImg = temDom.attr('data-url');
            obj.BgImgId = temDom.attr('key');
            obj.ExServiceType = [];
            let companyType = domData.companyType;
            // 京东模板部分快运无法子母件处理
            if (obj.KddType == 5 && ['ANEKY', '3108002701_1011', 'CN7000001021040', 'SFKY'].includes(domData.excode)) {
                groupObj.modeZmj = '0';
                companyType = 1;
            }
            // // TODO 视频号暂不支持添加模板组
            // if(obj.KddType == 14){
            //     Tatami.showFail('视频号面单暂不支持添加到模板组')
            //     return
            // }
            if (params.exCompanyId) {
                groupObj.groupId = 0; // params.id
            } else if (isGroupAddTemp != '1') {
                groupObj.groupId = dom.find('.fzsz-select-group').val();

            }
            dom.find('input[name=\'kddworktype\']:checked').each(function(ind, inv) {
                tem = $(inv).data('data');
                obj.ExServiceType.push(tem);
            });

            if (obj.KddType == 3 && comp.base.getTempStyle('cainiao', obj.StyleId)
                || obj.KddType == 7
            ) {
                obj.ModeLogisticsItems = resObj.getChooseService(dom.find('.cnservice_content'), {
                    excode: domData.excode,
                    excodeName: domData.excodeName,
                    expressType: obj.KddType,
                    expressStyle: obj.StyleId,
                });
                if (obj.ModeLogisticsItems === false) {
                    return;
                }

            } else {
                obj.ExServiceItems = [];
                dom.find('input[name=\'kddservicetype\']:checked').each(function(ind, inv) {
                    invDom = $(inv);
                    invDom.find();

                    tem = invDom.data('data');
                    obj.ExServiceItems.push(tem);

                    if (tem.Value == '声明价值保价') {
                        tem.Tips = (~~invDom.parent().next().find('input:text').val()) + '';
                    }
                });
            }


            if (dom.find('.addTemp-fuwu-accretion').length) {
                obj.AdvancedServices = [];
                dom.find('input[name=\'AdvancedServices\']:checked').each(function(ind, inv) {
                    invDom = $(inv);
                    tem = invDom.data('data');
                    if (tem.Name == '包装费' || tem.Name == '标准化包装服务') {
                        const showBzfTip = invDom.closest('span').find('[name=bzfTip]:visible').length > 0; // 是否展示了金额输入框
                        tem.Tips = ~~invDom.closest('span').find('[name=bzfTip]:visible').val();
                        if (showBzfTip && (tem.Tips < 1 || tem.Tips > 500)) { // 如果展示了输入框，必须填入一个有效金额
                            Tatami.showFail('金额输入有误,请输入1元-500元区间有效金额');
                            isError = true;
                        } else {
                            isError = false;
                        }
                    }
                    if (tem.Key === 'zmj') {
                        groupObj.modeZmj = '1';
                    }
                    obj.AdvancedServices.push(tem);
                });
            }

            $.each(dom.find('.input_noempty'), function(index, el) {
                const $this = $(this);
                     const value = $this.val();
                     const name = $this.attr('name');

                if (name == '声明价值保价' && $this.prev('input').prop('checked')) {
                    if (!(Number(value) >= 1 && Number(value) <= 20000)) {
                        Tatami.showFail('声明价值保价输入有误,请输入1元-20000元区间有效金额');
                        isError = true;
                        return;
                    }
                }

                if ($this.closest('.serverContent').css('display') != 'none' && ($this.closest('label').find('input[type="radio"]').length == 0 || $this.closest('label').find('input[type="radio"]:checked').length > 0) && !value && !(domData.kddType ==16 && name === '月结卡号')) {
                    if (name === '客户编码') {
                        const { excode, kddType, excodeName } = domData;
                        let tipText = `<div style="line-height:24px;">应${excodeName}安全要求，使用${excodeName}电子面单需要绑定${excodeName}客户编码。<a href="https://helptb.kuaidizs.cn/helpMap/getDetail?detailId=1373" target="_blank">如何获取？</a></div>`;
                        // 快手邮政
                        if (['POSTB', 'EMS'].includes(excode) && kddType == '9') {
                            tipText = `<div style="line-height:24px;">客户编码不能为空，请输入后重试！</div>`;
                        }
                        model({
                            type: 'confirm',
                            title: '提示',
                            width: 350,
                            minHeight: 160,
                            content: tipText,
                            cancelHide: true,
                        });
                       } else {
                        Tatami.showFail(name + '不能为空');
                       }
                    isError = true;
                }
            });
            console.log(comp.Print.Data.currentGroup);
            let isInGroup = null;
            let isZmjGroup = false;
            // 需要特殊处理的京东快运
            let isJDkuaiyun = ['ANEKY', '3108002701_1011', 'CN7000001021040', 'SFKY'].includes(params?.exCode?.toString());
            if (params.exCode) {
                let currentAddGroup = addGroupCache || comp.Print.Data.currentGroup;
                isInGroup = currentAddGroup?.userTemplateList?.find(o =>{
					if([13,16].includes(Number(obj.KddType))){
						return [13,16].includes(Number(o.expressType))
					}else{
						return  o.expressType == obj.KddType
					}
				});
            } else if (isGroupAddTemp != '1') {
                let selectAddGroup = comp.Print.Data.kddTemplates.find(o => o.id == groupObj.groupId);
                if ((groupObj.modeZmj == '1' && selectAddGroup.modeZmj != '1') || (groupObj.modeZmj != '1' && selectAddGroup.modeZmj == '1')) isZmjGroup = true;
                isInGroup = selectAddGroup?.userTemplateList?.find(o =>{
					if([13,16].includes(Number(obj.KddType))){
						return [13,16].includes(Number(o.expressType))
					}else{
						return  o.expressType == obj.KddType
					}
				});
            }
            if (isZmjGroup && companyType != 2) {
                let warnText = groupObj.modeZmj == '1' ? '该模版组不支持子母件服务，请重新选择' : '您尚未开启子母件服务，无法加入该模版组';
                model({
                    type: 'confirm',
                    title: '提示',
                    width: 350,
                    minHeight: 160,
                    content: warnText,
                    cancelHide: true,
                });
            } else if (isInGroup) {

                model({
                    type: 'confirm',
                    title: '提示',
                    width: 350,
                    minHeight: 160,
                    content: '该组内已有此类型模板，保存将替换原模板',
                    okCb: () => {
                        isError ? '' : dom.qlrr.save(obj, groupObj);
                    }
                });

			}
			// else if (isJDkuaiyun && ((obj.KddType == 5) || params.modeZmj == '0')) {
			//     let warnText = params.modeZmj == '1' ? "当前模板暂不支持子母件服务，请重新选择" : '该模版组不支持子母件服务，请重新选择';
			//     model({
			//         type: 'confirm',
			//         title: '提示',
			//         width: 350,
			//         minHeight: 160,
			//         content: warnText,
			//         cancelHide:true,
			//     })
			// }
			else {
                isError ? '' : dom.qlrr.save(obj,groupObj);
            }
        });

        // 保存
        dom.qlrr.save = function(obj, groupObj) {

            if (!obj.ExCompanyId) {
                Tatami.showFail('请选择快递');
                return;
            }

            obj.ExCompanyId = parseInt(obj.ExCompanyId);

            if (!obj.KddType) {
                Tatami.showFail('请选择面单类型');
                return;
            }

            obj.KddType = parseInt(obj.KddType);

            if (obj.KddType == 1) {
                if (!obj.StyleId) {
                    Tatami.showFail('请选择面单样式');
                    return;
                }

                obj.StyleId = parseInt(obj.StyleId);
                if (!obj.BgImgId) {
                    Tatami.showFail('请选择底图!');
                    return;
                }

                obj.BgImgId = parseInt(obj.BgImgId);
            } else {
                if (!obj.Height) {
                    Tatami.showFail('请选择面单尺寸');
                    return;
                }

                if (!obj.StyleId) {
                    Tatami.showFail('请选择面单样式');
                    return;
                }
                obj.StyleId = parseInt(obj.StyleId);
            }

            if (obj.ExServiceItems) {
                let isOk = true;
                obj.ExServiceItems.each(function(ind, item) {
                    if (item.Value == '声明价值保价') {
                        if (item.Tips < 1 || item.Tips > 20000) {
                            Tatami.showFail('声明价值保价输入有误,请输入1元-20000元区间有效金额');
                            isOk = false;
                            return false;
                        }
                    }
                });
                if (!isOk) {
                    return;
                }
            }

            // 模板不能建立提醒
            // if( dom.qlrr.isMustUpdate(obj.ExCompanyId,obj.KddType,obj.StyleId) ){
            //     alert("使用该模板需升级新版打印控件，或联系客服");
            //     return;
            // }

            // 防止重复点击
            if (btnOk.find('i').length > 0) {
                return;
            }
            btnOk.html('<i class="expr_green_loading"></i>保存中');
            that.saveAddKdd(obj, function(data) {
                const funcHook = (window.printAPI.compHookObj || {}).afterChangedKddTemp;
                funcHook && funcHook(data, 'addTemp');
                btnOk.html('保存');
                domData.result = data;
                if (params.exCode)comp.Print.Data.addNewKdd = data;
                dom.qlrr.close(data.Mode_ListShowId);
            }, groupObj);
        };

        btnNo.click(function() {
            dom.qlrr.close();
        });

        dom.qlrr.init(callback, params);
        dom.qlrr.show(defaultExcode);
        mergeCacheObj.addKddObj = dom;
        return mergeCacheObj.addKddObj;
    };
    // 快递单主界面
    resObj.showkddMain = function(defId, printDatas, sellerData, groupTempID) {
        console.log('快递单主界面');
        // 存一下，避免缓存取不到
        comp.Print.Data.groupTempID = groupTempID;
        let that = new comp.Print.FN();
             let _showCacheObj = mergeCacheObj.showKddMainObj;
             let _printTemplateObj;
             let PLAT = comp.Print.Data.platform;



        if (_showCacheObj) {
            if (!comp.Print.Data.isSinglePrint) {
                _showCacheObj.qlrr.loadData();
                _showCacheObj.qlrr.initTabs(defId, printDatas);
                _showCacheObj.qlrr.show();
                _showCacheObj.qlrr.initSellers(sellerData, false);
                return _showCacheObj;
            } else {
                // 清除缓存
                const zhezhaoid = mergeCacheObj.showKddMainObj.attr('zhezhaoid');
                $('#' + zhezhaoid).remove();
                mergeCacheObj.showKddMainObj.remove();
                mergeCacheObj.showKddMainObj = null;
            }
        }
        // 模板设计外框
        const html = '<div class="pup_kdd_sy_box">'
            + '<div class="clearfix pup_tzsx_box" style="display:none;margin:0;">'
            + '<ul id="groupTempSortable" class="pup_tzsx_list ui-sortable"></ul>'
            + '<div class="pup_tzsx_btn_box">'
            + '<a href="javascript:void(0)" class="expr_btn_js">保存修改</a><br />'
            + '<a href="javascript:void(0)" class="expr_btn_gray">取消</a>'
            + '</div>'
            + '</div>'
            + '<div class="pup_menu_box">'
            + '<ul class="pup_kdd_menu clearfix">'
            + '<li class="first  tag-tobothend tag-tobothend-first" ></li>'
            + '<li class="prev"><a href="javascript:void(0)"></a></li>'
            + '<li class="next"><a href="javascript:void(0)"></a></li>'
            + '<li class="last  tag-tobothend tag-tobothend-last"></li>'
            + '<li class="change_qj">'
            + '<a href="javascript:void(0)" class="ch_add kdd"><i></i>添加模版</a>'
            + '<a href="javascript:void(0)" class="line">|</a>'
            + '<a href="javascript:void(0)" class="pad_r kdd">调整顺序</a>'
            + '<a href="javascript:void(0)" class="line">|</a>'
            + '<a href="javascript:void(0)" class="no_radius pad_l kdd">全局设置</a>'
            + '<label></label>'
            + '</li>'
            + '</ul>'
            + '</div>'
            + '<div class="pup_dykj_box">'
            + '<div id="loaddingKdd" style="font-size: 14px;position: absolute;top: 230px;left: 250px;display:none;"></div>'
            + '<i class="delete_mid_icon" style="top:-31px;right: -25px;"></i>'
            + '<div class="goods_net_left"></div>'
            + '<div class="goods_net_right" style="position: relative;box-sizing: content-box;"></div>'
            + '<div class="kddset_yunda" id="yundaSettingDiv" style="display:none;"></div>'
            + '<div id="DandadianziDiv" class="dandadianzidiv" >'
            + '</div>'
            + '<div class="kdddy_group_btn">'
            + '<a href="javascript:void(0)" class="btn_xzdy_yellow_big">预览打印</a>'
            + '<a href="javascript:void(0)" class="btn_xzdy_gray btn_edit_temp">编辑此模板</a>'
            // 暂时隐藏绑定发件人跟打印机
            + '<div class="mn_select_box select_print_box">'
            + '<div class="select_print_box_tip">设置模板组默认打印机</div>'
            + '<a href="javascript:void(0)" class="btn_xzdy_gray btn_set_printer">'
            + '<span class="temp-choose-printer">选择打印机</span><i class="select_icon_little"></i>'
            + '</a>'
            + '<dl class="selectPinter"></dl>'
            + '</div>'
            + `<div class="mn_select_box select_fjr_box" style="${['pdd'].includes(comp.Print.Data.platform) ? '' : 'display:none;'}">`
            + '<a href="javascript:void(0)" class="btn_xzdy_gray btn_bind_fjr">'
            + '<span class="temp-choose-fjr">绑定发件人</span><i class="select_icon_little"></i>'
            + '</a>'
            + '<dl class="selectBindFjr" style="display:none;" ></dl>'
            + '</div>'
            + '</div>'
            + '<div class="merge_kd_type">'
            + '<div class="zmj_tip" style="display:none" title="该模版组已开启子母件服务"> 子母件</div>'
            + '<span class="merge_kd_set_btn">分组设置</span>'
            + '</div>'
            + '</div>'
            + '</div>';

        const tagCount = 6;
        const body = $('body');
        const domData = {};
        const domid = 'div_' + (+new Date());
        const dom = $(html).appendTo(body).attr('id', domid);
        const printData = comp.Print.Data;
        const temDom = dom.find('.goods_net_left');
        const $choosePirnter = dom.find('.temp-choose-printer');
        const lodopDefaultWidth = dom.find('.pup_dykj_box').width();
        // 地址点击标识位
        let addressClickFlag = true;


        temDom.css('overflow', 'hidden');
        domData.lodopDomParent = temDom;
        domData.contentDom = temDom.parent();
        domData.netPointDiv = dom.find('.goods_net_right');
        domData.yundaDiv = dom.find('#yundaSettingDiv');
        let scroll = dom.find('.address_scroll');
        dom.qlrr = {};
        dom.qlrr.data = {};


        // 显示
        dom.qlrr.show = function(data) {
            that.showDialog(domid);
        };

        // 初始化标签
        dom.qlrr.initTabs = async function(defId, printDatas) {
            const arr = printData.kddTemplates.filter(o => o.id !== '-901');
            // 判断是否有顺序
            if (arr.length == 0) {
                const ul = dom.find('.pup_kdd_menu.clearfix');
                ul.find('.tag').remove();
                dom.qlrr.selectCurrentTag(null);
                return null;
            }
            domData.currentTemp = null;
            dom.qlrr.currentTemp = null;
            domData.defId = defId || domData.defId || printData.kddTemplates[0].id;
            domData.pageIndex = 0;
            if (printDatas && printDatas[0]) {
                domData.tempData = printDatas[0];
                domData.printDatas = printDatas;
            } else {
                domData.tempData = await that.getPrintData('kdd');
                domData.printDatas = [];
                domData.printDatas.push(domData.tempData);
            }
            let indexOf = -1;
            for (let i = 0; i < arr.length; i++) {
                const mode = arr[i];
                if (mode.id == domData.defId) {
                    indexOf = i;
                    break;
                }
            }
            if (indexOf >= 0) {
                domData.pageIndex = Math.floor(indexOf / tagCount);
                dom.qlrr.showTagPage();
            } else if (arr[0].id) {

                dom.qlrr.initTabs(arr[0].id);
            }

            if (comp.Print.Data.isSinglePrint) {
                const tarDom = $('#DandadianziDiv');
                let temHtml = '';
                if (comp.Print.Data.userVersion === 'trial') {
                    temHtml = '<div class="dandadianzicenter">'
                        + '<h3>提示:</h3>'
                        + '<div class="ft-center mt_20">'
                        + '<p class="ft_20 mb_0">升级标准版，即可开通电子面单打印，畅享更多功能</p>'
                        + '<p class="ft_14 mt_0" >(标准版，单打、批打均可使用电子面单打印)</p>'
                        + '<a class="btn btn-orange ft_20 mt_40 J-clickPoint" data-clickData-points="point=11194.11200.13968.13970&_fm=3219" href="https://tb.cn/HmJhtWw" target="_blank" >立即升级</a>'
                        + '</div>';
                } else {
                    temHtml = '<div class="dandadianzicenter"><h3>提示:</h3><h4>单打模式下不支持该面单打印预览</h4></div>';
                }
                tarDom.html(temHtml);
            }
        };

        // 初始化打印机
        dom.qlrr.initPrinter = function() {
            let dl = dom.find('.selectPinter');
                let _tempInfo = domData.currentTemp || {};
                let clientType;
            // clientType = comp.Print.getClientType(_tempInfo);
            // 美团控件新建面单获取打印机列表用lodop
            // if (clientType == 'meituan') {
            //     clientType = 'lodop';
            // }
            // that.getPrinters(function (printerObj) {
            //     const printers = printerObj.printers;
            //     if (printers.length) {
            //         $.each(printers, function (ind, inv) {
            //             $('<dd/>').appendTo(dl).html(inv);
            //         });
            //     } else {
            $choosePirnter.text('无打印机');
            //     }
            // }, clientType);
            dl.on('click', 'dd', function() {
                if ($(this).find('i').length == 0) {
                    dom.qlrr.setDefaultPrinter($(this).text());
                    // 把选择的打印机进行本地缓存
                    const temId = domData?.currentTemp?.id;
                    if (!temId) return;
                    savePrintersStorage(`group${temId}`, $(this).text());
                }
                dl.hide();
            });
            dom.click(function() {
                dl.hide();
            });
            dl.hide();
        };

        // 初始化绑定发件人 淘宝平台模板新增绑定发件人需求 各个平台按需看是否需要该功能 platform
        dom.qlrr.rendSellerList = function(sellerData) {
            let tipsHtml = `<span  class="wenhao_img toolTip" name="wenhao_img" data-act-name="wenhao_img" data-tip-content="发件人使用顺序：快递模板组已绑定发件人=>店铺已绑定发件人=>默认发件人" data-tip-title="发件人使用顺序"></span>`;
            let _html = '<dd data-fjrid=\'-1\' data-name=\'绑定发件人\'>不绑定发件人' + tipsHtml + '</dd>';
            if (comp.Print.Data.isSinglePrint || !sellerData || !sellerData.isKddBindFjr || !sellerData.senderInfo.length) {
                dom.find('.select_fjr_box').hide();
                return;
            }
            $.each(sellerData.senderInfo, function(_i, _v) {
                _v.list && _v.list.length && $.each(_v.list, function(_i1, _v1) {
                    _html += '<dd data-fjrid=' + _v1.id + ' data-name=' + _v1.name + ' >' + _v1.name + ', ' + _v1.address + '</dd>';
                });
            });
            dom.find('.select_fjr_box').show().find('.selectBindFjr').html(_html);
            dom.on('mouseover', function(event) {
                const target = event.target;
                if (target.dataset.actName === 'wenhao_img') {
                    let _this = $(this);
                    let style;
                    let content = target.dataset.tipContent;
                    let x = event.clientX - 12;
                    let y = event.clientY + 22;
                    let width = 200;
                if (!content) {
                    return;
                }
                style = 'width:'
                        + (width ? width + 'px' : 'auto')
                        + ';left:'
                        + x
                        + 'px'
                        + ';top:'
                        + y
                        + 'px';

                    // 鼠标移入
                    $('body').append(
                        '<div class="tip-tool-model" style="'
                                + style
                                + '"><img src="https://static.kuaidizs.cn/resources/img/templateEdit/icon-up.png" style="position: absolute;top: -6px;width: 15px;">'
                                + content
                                + '</div>',
                    );
                } else {
                    $('.tip-tool-model').remove();

                }
            });
        };

        dom.find('.selectBindFjr').on('click', 'dd', function() {
            // const currentTagDom = dom.find('.cur')
            const funcHook = (window.printAPI.compHookObj || {})
                .operatingRecord;
            funcHook && funcHook('订单_订单打印_快递单设置_绑定发件人');
            const _$dd = $(this);
                const _senderId = ~~_$dd.attr('data-fjrid');
                const _mkddid = domData.currentTemp.id;
            comp.print.data.modifyTempFjr(_senderId, _mkddid, function() {
                domData.currentTemp.bindFjrId = _senderId;
                Tatami.pub('updateKddTemplates', {
                    key: 'bindFjrId',
                    value: _senderId,
                    tempId: _mkddid
                });
                // console.log(printDatas);
                _$dd.closest('dl').find('.xz_right_icon').remove();
                _$dd.append('<i class="xz_right_icon"></i>');
                dom.find('.temp-choose-fjr').text(_$dd.attr('data-name') || '绑定发件人');
                dom.find('.selectBindFjr').hide();
            });
        });
        dom.find('.btn_bind_fjr').click(function() {
			if( comp.Print.chenkAuthor()) return
            dom.find('.selectPinter').hide();
            $(this).closest('.select_fjr_box').find('.selectBindFjr').show();
            return false;
        });

        $('body').click(function() {
            dom.find('.selectBindFjr').hide();
        });
        dom.find('.merge_kd_type').on('click', '.merge_kd_type_btn', function(e, i) {
            const tarDom = $(this);
            const userTemplateId = tarDom.attr('userTemplateId');
            const tempGroup = comp.Print.Data?.currentGroup?.userTemplateList.find(o => o.userTemplateId == userTemplateId);
            tarDom.addClass('active_merge_btn').siblings('span').removeClass("active_merge_btn");
            dom.qlrr.selectChangedTag(tempGroup);

        });
        // 分组设置
        dom.find('.merge_kd_type').on('click', '.merge_kd_set_btn', function(e) {
			if( comp.Print.chenkAuthor()) return
            // const tarDom = $(this);
            dom.qlrr.close();
            resObj.setGroupSet();
        });
        // 绑定发件人初始化
        dom.qlrr.initSellers = function(sellerData, isFirst) {

            if (comp.Print.Data.platform === 'erp' && !sellerData) {
                const fjr = window.erpData?.allKddFjr;
                if (fjr) {
                    sellerData = {
                        'isKddBindFjr': true,
                        'senderInfo': fjr,
                        'isUseMore': true,
                        'isUseCommon': false,
                    };
                }
            }

            if (sellerData) {
                dom.qlrr.rendSellerList(sellerData, isFirst);
            }
        };

        // 渲染选中默认发件人
        dom.qlrr.renderDefaultFjr = function(bindFjrId) {
            let _$dd = dom.find('.selectBindFjr dd[data-fjrid="' + bindFjrId + '"]');
            if (!_$dd.length) {
                _$dd = dom.find('.selectBindFjr dd[data-fjrid="-1"]');
            }
            _$dd.closest('dl').find('.xz_right_icon').remove();
            _$dd.append('<i class="xz_right_icon"></i>');
            dom.find('.temp-choose-fjr').text(_$dd.attr('data-name') || '绑定发件人');
        };

        // 设置默认打印机
        dom.qlrr.setDefaultPrinter = function(printerName) {

            const funcHook = (window.printAPI.compHookObj || {})
                .operatingRecord;
            funcHook && funcHook('订单_订单打印_快递单设置_选择打印机');

            // const temObj = domData.currentTempInfo.ModeTempPrintcfg;
            const temObj = domData.currentTemp;
            if (temObj) {
                that.saveDefaultPrinter(temObj.id, null, printerName, function() {
                    const div = dom.find('.select_print_box');
                    const a = div.find('.btn_xzdy_gray');
                    const span = a.find('span').html(printerName);
                    temObj.defaultPrinter = printerName;
                    Tatami.pub('updateKddTemplates', {
                        key: 'defaultPrinter',
                        value: printerName,
                        tempId: temObj.id
                    });
                    const dl = dom.find('.selectPinter');
                    const dds = dl.find('dd');
                    dds.each(function(ind, inv) {
                        const dd = $(inv);
                        dd.find('i').remove();
                        const dhtml = dd.html();
                        if (dhtml == printerName) {
                            dd.html(dhtml + '<i class=\'xz_right_icon\'></i>');
                        }
                    });
                }, true);
            }
        };

        // 展示当前页标签
        dom.qlrr.showTagPage = function() {
            const ul = dom.find('.pup_kdd_menu.clearfix');
            ul.find('.tag').remove();
            const arr = printData.kddTemplates.filter(o => o.id !== '-901');
            const start = domData.pageIndex * tagCount;
            let end = start + tagCount;
            if (end > arr.length) {
                end = arr.length;
            }
            const beDom = ul.find('.next');
            for (let i = start; i < end; i++) {
                const mode = arr[i];
                const exName = mode.groupName;
                let temExName = exName;
                if (exName.length > 5) {
                    temExName = exName.substr(0, 4) + '..';
                }
                const li = $('<li title=\'' + exName + '\' class=\'tag\'><a subtitle=\'' + temExName + '\' href=\'javascript:void(0)\'>' + temExName + '</a><label></label></li>');
                li.insertBefore(beDom).data('mode', mode);
                if (mode.id == domData.defId) {
                    dom.qlrr.selectCurrentTag(mode);
                }
            }



            const lis = ul.find('.tag');
            lis.click(function() {
                const mode = $(this).data('mode');
                dom.qlrr.selectCurrentTag(mode);
            });

            dom.find('li.prev')[domData.pageIndex > 0 ? 'show' : 'hide']();
            dom.find('li.first')[domData.pageIndex > 0 ? 'show' : 'hide']();

            const pageCount = Math.ceil(arr.length / tagCount) - 1;
            dom.find('li.next')[domData.pageIndex < pageCount ? 'show' : 'hide']();
            dom.find('li.last')[domData.pageIndex < pageCount ? 'show' : 'hide']();
        };

        // 选中当前项
        dom.qlrr.selectCurrentTag = function(mode) {
            mode = comp.Print.Data.kddTemplates.find(o => o.id == mode.id);
            comp.Print.Data.currentGroup = mode;
            let $span = dom.find('.select_print_box').find('.btn_xzdy_gray').find('span').html('选择打印机');
            let defPrint;
            if (mode == null) {
                domData.currentTemp = null;
                domData.defId = null;
                dom.qlrr.selectChangedTag(null);
                dom.qlrr.currentTemp = null;
            }
            if (mode.modeZmj == '1') {
                dom.find('.zmj_tip').show();
            } else {
                dom.find('.zmj_tip').hide();
            }
            sellerData && dom.qlrr.renderDefaultFjr(mode?.bindFjrId || -1);
            // 美团控件新建面单获取打印机列表用lodop
            // 打印机列表重新渲染
            that.getPrinters(function(printerObj) {
                let hander;
                const printers = printerObj.printers;
                hander = function(_dePrint) {
                    $span.html(_dePrint || '选择打印机');
                    let _ddHtml = '';
                    printers && $.each(printers, function(ind, inv) {
                        _ddHtml += '<dd>' + inv + (inv == _dePrint ? '<i class=\'xz_right_icon\'></i>' : '') + '</dd>';
                    });
                    _ddHtml ? dom.find('.selectPinter').html(_ddHtml) : $choosePirnter.text('无打印机');
                };
                // 模版存在绑定的打印机并且在本地打印机列表存在
                if (mode?.defaultPrinter && printerObj?.printers?.includes(mode.defaultPrinter)) {
                    defPrint = mode.defaultPrinter;
                } else {
                    defPrint = printerObj.dPrinter;
                }
                hander(defPrint);
            }, 'lodop');
            const ul = dom.find('.pup_kdd_menu.clearfix');
            const lis = ul.find('.tag');
            lis.each(function(ind, inv) {
                const tem = $(inv);
                const temData = tem.data('mode');
                if (temData == mode) {
                    tem.addClass('cur');
                    const title = tem.attr('title');
                    tem.find('a').text(title);
                } else if (tem.hasClass('cur')) {
                    tem.removeClass('cur');
                    const adom = tem.find('a');
                    const subtitle = adom.attr('subtitle');
                    adom.text(subtitle);
                }
            });

            if ((!domData.currentTemp) || domData.currentTemp.id != mode.id || !dom.qlrr.currentTemp) {
                dom.qlrr.loadding('数据加载中……');
                domData.currentTemp = mode;
                dom.qlrr.currentTemp = mode; // 在渲染网点列表方法：showYunZhanSetting 中获取不到 domData ，所以在 dom.qlrr 上挂一个 currentTemp
                domData.defId = mode.id;
                // 暂时去掉groupinfo
                // that.getGroupInfo(mode.id, true, function(data){
                    let selectMode = null;
                if (mode.userTemplateList.length) {
                    selectMode = mode.userTemplateList.find(o => o.userTemplateId == comp.Print.Data.groupTempID);
                    dom.qlrr.selectChangedTag(selectMode || mode.userTemplateList[0]);
                } else {
                    temDom.find('#loaddingKdd').html('当前分组没有可用模板，请点击分组设置添加模板');
                    dom.qlrr.loadEmpty();
                }
                dom.qlrr.renderKddType(mode.userTemplateList, selectMode);

                // });
            }
        };

        dom.qlrr.loadding = function(msg) {
            temDom.html('<p id="loaddingKdd" style="font-size: 14px; text-align: center;margin-top: 250px;">' + msg + '</p>');
        };
        dom.qlrr.selectChangedTag = function(mode) {
            that.getTemplateInfo(mode.userTemplateId, true, function(data) {
                let temDomHtml = temDom.find('#loaddingKdd').html();
                if (temDomHtml && temDomHtml.includes('添加模板')) return;
                temDom.removeClass('transparent');
                dom.qlrr.selectChangedTagOp(mode, data);
            });
        };

        // 选中模版改变
        dom.qlrr.selectChangedTagOp = function(mode, modeInfo) {
            if (mode != null) {
                const data = modeInfo || that.getTemplateInfo(mode.Mode_ListShowId);
                domData.currentTempInfo = data;
                comp.Print.Data.currentTempInfo = data;
            }
            dom.find('.btn_xzdy_yellow_big').text('预览打印');
            domData.isYunDa = false;
            // 显示隐藏网点设置 //东方
            if (mode && domData.currentTempInfo && domData.currentTempInfo.ModeList.KddType > 1 && !comp.Print.Data.isSinglePrint) { // 单打不需要调用这个接口 减少请求 TODO
                // 控制主页面层的显示隐藏
                if (domData.currentTempInfo.ModeList.Excode == 'YUNDA' && domData.currentTempInfo.ModeList.KddType == 2) {
                    domData.lodopDomParent.hide(); // 隐藏控件展示层
                    domData.netPointDiv.hide(); // 隐藏右侧网点层
                    domData.yundaDiv.show();
                    domData.isYunDa = true;
                    dom.find('.kdddy_group_btn').hide();
                } else {
                    domData.lodopDomParent.show();
                    domData.netPointDiv.show();
                    domData.yundaDiv.hide();
                    domData.netPointDiv.css('display', 'block');
                    domData.lodopDomParent.width(lodopDefaultWidth - 300);
                    dom.find('.kdddy_group_btn').show();
                }

                const exuserId = domData.currentTempInfo.ModeListShow.Exuserid;
                const subUserId = domData.currentTempInfo.ModeListShow.ExSubId;
                const exCode = domData.currentTempInfo.ModeList.Excode;
                const modeListShowId = domData.currentTempInfo.ModeListShow.Mode_ListShowId;
                const exid = domData.currentTempInfo.ModeList.Exid;

                const loadingHtml = `<div class="dzmd_load_box" style="color: #fff;padding: 230px 0 0 50px;">
                                            <img src="https://static.kuaidizs.cn/resources/img/print/loading_wd.gif" style="transform: scale(0.5);margin-bottom: -15px;margin-left: -5px;">
                                            <span>正在读取电子面单设置</span><img src="https://static.kuaidizs.cn/resources/img/print/loading_icon.gif" style="vertical-align: bottom;">
                                    </div>`;
                // 获取是否拼接打印发货单
                const kddType = domData.currentTempInfo.ModeList.KddType;

                const isShowWdList = comp.base.isShowWdList();// 右侧是否是「网点列表」（菜鸟、拼多多、京东无界），与之对应的补集是「网点的账号密码输入框」
                if (isShowWdList) {
                    domData.netPointDiv.html(loadingHtml);
                    // 获取云栈网点设置信息
                    const arg = {
                        exuserId,
                        subUserId,
                        exCode,
                        modeListShowId,
                        exid,
                        kddType,
                        jdQl: domData.currentTempInfo.jdQl,
                        templateId: domData.currentTempInfo.ModeListShow.Mode_ListShowId,
                    };
                    dom.qlrr.getWdInfo(arg); // 多平台 菜鸟网点右侧信息整合
                } else {
                    // 获取网点电子面单的设置信息
                    domData.netPointDiv.html(loadingHtml);
                    that.getBranchSetting(exid, exuserId, subUserId, modeListShowId, exCode, dom.qlrr.showBranchSetting);
                }
            } else {
                domData.lodopDomParent.show();
                domData.netPointDiv.css('display', 'none');
                domData.lodopDomParent.width(lodopDefaultWidth);
                domData.yundaDiv.hide();// 隐藏韵达的层
            }

            let height = domData.currentTempInfo.ModeList.HeightImg + 25;
            if (height === 0) {
                height = 560;
            }
            if (height > 560) {
                height = 560;
            }
            domData.contentDom.height(height);
            domData.lodopDomParent.height(height);

            // 如果单打，在selectCurrentTag 之前执行，实时获取大头笔，分拣码等信息。
            const eventObj = comp.Print.eventObj;
            if (comp.Print.Data.isSinglePrint && domData.currentTemp.KddType == 1 && domData.currentTemp.Exid !== 900) {
                if (eventObj && eventObj.signPrintChangeBigHead) {
                    const list = eventObj.signPrintChangeBigHead;
                    if (list[0]) {
                        list[0](mode, domData.tempData, function() {
                            dom.qlrr.draw();
                        });
                    } else {
                        dom.qlrr.draw();
                    }
                } else {
                    dom.qlrr.draw();
                }
                window.sessionStorage.setItem('singleCurKddTempId', domData.currentTempInfo.ModeListShow.Mode_ListShowId);
            } else {
                dom.qlrr.draw();
            }
            // if (eventObj && eventObj.kddMainSelectChanged) {
            //     var list = eventObj.kddMainSelectChanged;
            //     list.each(function (ind, fun) {
            //         fun(domData.currentTempInfo);
            //     });
            // }
        };
        // 渲染左侧快递类型
        dom.qlrr.renderKddType = function(data, mode) {
            const kddTypeObj = comp.base.getKddNameObj();
            let itemHtml = '';
            let box = dom.find('.merge_kd_type');
            let itemClass = '';
            box.find('.merge_kd_type_btn').remove();
            data.forEach((o, i) => {
                if (mode && mode?.userTemplateId === o?.userTemplateId) {
                    itemClass = 'merge_kd_type_btn active_merge_btn';
                } else if (!mode && i === 0) {
                    itemClass = 'merge_kd_type_btn active_merge_btn';

                } else {
                    itemClass = 'merge_kd_type_btn';
                }
                itemHtml += `<span tempKddType=${o.expressType} userTemplateId=${o.userTemplateId} class="${itemClass}">${kddTypeObj[o.expressType]}</span>`;
            });
            $(itemHtml).prependTo(box);
        };
        dom.qlrr.getWdInfo = function(arg) { // 多平台 菜鸟网点右侧信息整合的方法
            const exuserId = arg.exuserId;
                const subUserId = arg.subUserId;
                const exCode = arg.exCode;
                const modeListShowId = arg.modeListShowId;
                const exid = arg.exid;
                const jdQl = arg.jdQl;

            arg.isMoreStore = comp.Print.configFunc('WDMoreStore');
            if (PLAT === 'tb') { // 淘宝
                that.getYunZhanSetting(exuserId, subUserId, exCode, modeListShowId, exid, dom.qlrr.showYunZhanSetting, dom.qlrr.loadProgress);
            } else if (PLAT == 'zzb') { // 自助版

                if (jdQl == 1) {
                    that.getJDYunZhanList({ templateId: modeListShowId }, dom.qlrr.loadJDYunZhan);
                } else {
                    // 展示云栈列表
                    dom.qlrr.loadYunZhanMethod(arg);
                    that.getYunZhanList({
                        exid,
                        methodType: 1,
                    }, function(json) {
                        dom.qlrr.loadYunZhan(json, arg);
                    });
                }

            } else if (/^(pdd|ali)$/.test(PLAT)) {
                arg.PLAT = PLAT;
                if (jdQl == 1) {
                    that.getJDYunZhanList({ templateId: modeListShowId }, dom.qlrr.loadJDYunZhan);
                } else if (arg.kddType == 3) {
                    that.getYunZhanSetting(exuserId, subUserId, exCode, modeListShowId, exid, dom.qlrr.showYunZhanSetting, dom.qlrr.loadProgress);
                } else if (arg.kddType == 7) {
                    // TODO ERP处理逻辑已移至下方，该逻辑待优化
                    that.getYunZhanList(arg, function(json) {
                        try {
                            // 网点出错不要影响模板展示
                            // TODO ERP使用拼外模式渲染站点
                            if (comp.Print.Data.platform == 'erp') {
                                dom.qlrr.loadYunZhanPdd(json, arg);
                            } else {
                                dom.qlrr.loadPddWd(json, arg);
                            }
                        } catch (e) {
                            console.log(e);
                        }
                    });
                } else {
                    that.getYunZhanMethod({ modeListShowId, exid }, function(d) {
                        arg.methodType = d.method;
                        // 展示云栈列表
                        dom.qlrr.loadYunZhanMethod(arg);
                        that.getYunZhanList(arg, function(json) {
                            dom.qlrr.loadYunZhan(json, arg);

                            // 下面的方法只针对 pdd 生效
                            if (PLAT == 'pdd') {
                                Tatami.controls.get('control.getLoginUser').getMethod('getUserInfo', function(data) { // 获取当前是标准版还是高级版
                                    const level = data.level;
                                        const authorizedShopNum = json.data && json.data.data && json.data.data.length; // 已经授权的店铺数量
                                    if (level != 5 && authorizedShopNum >= 1) { // 非高级版且已经授权了一个或以上的店铺，此时授权会弹出高级版订购框
                                        $('#authroizedButton').attr('href', ''); // 隐藏href
                                        $('body').off('click').on('click', '#authroizedButton', function(e) {
                                            e.preventDefault();
                                            Tatami.pub('getUpgradeDialog', JSON.parse(window.sessionStorage.getItem('com.userInfo')), 'print');
                                            $('.upgradeFrame').css('z-index', 1002); // 把订购高级版的弹框放在快递单设置弹框的前面
                                        });
                                    }
                                });
                            }

                        });
                    }, function() {
                        dom.qlrr.loadYunZhanErrorHtml();
                    });
                }
            } else if (PLAT === 'erp') {
                    // 获取云栈方式
                    that.getYunZhanMethod({modeListShowId,exid}, function(d) {
                        arg.methodType = d.method;
						if(jdQl == 1) {
							dom.qlrr.loadJDYunZhan(arg)

						} else {
   							//展示云栈列表
   							dom.qlrr.loadYunZhanMethod(arg);
   							that.getYunZhanList(arg, function(json){
								   dom.qlrr.loadYunZhan(json, arg);
   							});
						}

                    }, function(){
                        dom.qlrr.loadYunZhanErrorHtml();
                    });
            } else if (jdQl == 1) {
                    that.getJDYunZhanList({ templateId: modeListShowId }, dom.qlrr.loadJDYunZhan);
                }
                // else if( arg.kddType == 7){
                //     that.getYunZhanList(arg, function(json){
                //         dom.qlrr.loadPddWd(json, arg);
                //     });
                // }
                else {
                    that.getYunZhanMethod(exid, function(d) {
                        arg.methodType = d.method;
                        // 展示云栈列表
                        dom.qlrr.loadYunZhanMethod(arg);
                        that.getYunZhanList(arg, function(json) {
                            dom.qlrr.loadYunZhan(json, arg);
                        });
                    }, function() {
                        dom.qlrr.loadYunZhanErrorHtml();
                    });
                }
        };
        // erp加载未绑定店铺提示
        dom.qlrr.loadBindShop = function(arg) {
            let $dom = '';
                let shopByKdd = {
                    3: '淘宝',
                    5: '京东',
                    7: '拼多多',
                    8: '抖音',
                    9: '快手',
                    13: '小红书',
                    14: '视频号'
                };
                let bindShopHtml = '<h2>设置发货网点</h2>' + '<div class="address_scroll" style="color:#fff">您还未添加' + shopByKdd[arg.kddType] + '店铺，请前往店铺管理添加店铺后设置网点 <span class="bind_shop">前往添加店铺</span></div>';
            $dom = domData.netPointDiv.html(bindShopHtml);
            $dom.find('.bind_shop').click(function() {
                dom.qlrr.close();
                window.location.hash = '#/shops';
            });

        };
        // 无模板加载空
        dom.qlrr.loadEmpty = function(arg) {
            let $dom = '';
                let bindShopHtml = '<h2>设置发货网点</h2>';
            $dom = domData.netPointDiv.html(bindShopHtml);


        };
        // 加载云栈设置html
        dom.qlrr.loadPddWd = function(json, arg) {
            let $dom;
                let _$net;
                let _defAddr;
                var yunZhanSettingHtml;
                let _html = '';
                let exid = (arg || {}).exid || domData.currentTempInfo.ModeListShow.Exid;
                let _data = json.data;
                let addressClickFlag = true;
                let exuserId = (arg || {}).exuserId || domData.currentTempInfo.ModeListShow.ExCode;
                let modeListShowId = (arg || {}).modeListShowId || domData.currentTempInfo.ModeListShow.Mode_ListShowId;

                var yunZhanSettingHtml = '<h2>设置发货网点</h2><div class="address_scroll"></div> ';
            if (!comp.Print.Data.platform == 'erp') {
                yunZhanSettingHtml
                    += '<div class="f_gray dzmd_qh_box add_list_adrs">\
                                    <!--使用关联店铺电子面单账号-->\
                                    <div id="is_use_link_shop" style="display:none;">\
                                        <p class="pad_l0">\
                                        <label class="on"><input type="checkbox" class="input_check useLinkShop" >使用关联店铺电子面单账号(<span class="f_geen_blue">荐</span>)</label>\
                                        </p>\
                                        <div class="set_link_dp" style="display:none;">\
                                            <a href="' + window.location.search.split('#')[0] + '#/sygj/connectStore/ " target="_blank">设置关联店铺</a>\
                                            <p class="">\
                                                如果希望多店铺共用一个拼多多电子面单账号，推荐使用关联店铺；同时可实现一键打印所有店铺订单，效率翻倍!\
                                            </p>\
                                        </div>\
                                    </div>\
                                </div>\
                                <!--使用其他店铺电子面单账号-->\
                                <div class="useOtherShopcontainer f_gray add_list_adrs">\
                                    <p class="pad_l0"><label><input type="checkbox" class="input_check" id="useOtherShop">使用其他店铺电子面单账号</label></p>\
                                    <div class="use_dpdm_box" style="display:none;">\
                                        <input type="text" placeholder="输入对方店铺代码" id="otherShopCode"><br>\
                                        <a href="javascript:void(0)" class="expr_btn_js" id="submitShopCodeBtn">确认</a>';
                // if( invalidCodeErro ){
                // yunZhanSettingHtml += '<span style="color: #d75fd2;" class="shopCodeErroInfo">对方店铺代码已失效</span>';
                // }

                yunZhanSettingHtml += '<p>店铺代码可在“实用工具-管理关联店铺”中找到，由对方店铺发送给您。</p>\
                                    </div>\
                                </div>';
            }


            $dom = domData.netPointDiv.html(yunZhanSettingHtml);

            if (json.result != 100) {
                dom.qlrr.loadYunZhanErrorHtml($dom.find('.address_scroll'));
                return;
            }
            if (_data.data && json.result == 100) {
                comp.Print.Data.yunzhanSetting = _data.data;
                $.each(_data.data, function(_i, _v) {
                    _html += dom.qlrr.loadPddWdlistHtml(_v);
                });
                // 绑定云栈是否使用关联店铺事件
                if (_data.data.length <= 1) {
                    $dom.find('#is_use_link_shop').show();
                }
            } else {
                _html += dom.qlrr.loadPddWdlistHtml(_data);
            }
            $dom.find('.address_scroll').html(_html);


            // 保存默认网点
            $dom.find('[data-act-name="choose-default"]').off('click').click(function() {
				if( comp.Print.chenkAuthor()) return false
                let yunZhan;
                    let params = null;
                    let $this = $(this).find('.input_yzSet');
                    let branchcodeFlag = false; // 邮政编码是否相同
                    let _isShared;
                    let _isRelation;
                    let _relationUserId;
                    let _id;
                    let _branchcode;
                if (!addressClickFlag) {
                    return;
                }
                addressClickFlag = false;
                // 当前地址已启用
                if ($this.next('span').children('em').length > 0) {
                    addressClickFlag = true;
                    return;
                }
                yunZhan = comp.Print.Data.yunzhanSetting;
                _id = $this.val();
                _isShared = !(($this.data('shared') == '' || $this.data('shared') == undefined || $this.data('shared') == false));
                _isRelation = !(($this.data('isrelation') == '' || $this.data('isrelation') == undefined || $this.data('isrelation') == false));
                _relationUserId = $this.data('relationuserid') == 'undefined' ? undefined : $this.data('relationuserid');
                _branchcode = $this.attr('data-branchcode');
                yunZhan && $.each(yunZhan, function(index, _d) {
                    _d.branchList && $.each(_d.branchList, function(_i, _v1) {
                        _v1.AddressList && $.each(_v1.AddressList, function(_i2, _v2) {
                            // 当有邮政编码时要多一层验证，以免相同网点保存不了
                            if (_branchcode != '' && _branchcode) {
                                if ((_v2.BranchCode == _branchcode)) {
                                    branchcodeFlag = true;
                                } else {
                                    branchcodeFlag = false;
                                }
                            } else {
                                branchcodeFlag = false;

                            }

                            // 分享单号验证一次，避免选中异常
                            if (_isShared == _d.shared && (_isRelation == _d.isRelation) && (_relationUserId == _d.relationUserId)) {
                                branchcodeFlag = true;
                            } else {
                                branchcodeFlag = false;
                            }

                            if (_v2.id == _id && branchcodeFlag) {
                                params = {
                                    exid,
                                    branchCode: _v1.BranchCode,
                                    province: _v2.SendProvince,
                                    city: _v2.SendCity,
                                    area: _v2.SendDistrict,
                                    town: _v2.SendTown,
                                    addressDetail: _v2.SendAddress,
                                    templateId: (arg || {}).modeListShowId || modeListShowId,
                                    exCode: _v1.CpCode,
                                    branchName: _v1.BranchName,
                                    branchUserId: _d.exuserid,
                                    shared: _d.shared,
                                    isRelation: _d.isRelation,
                                    relationUserId: _d.relationUserId,
                                    subShareUserCode: _d.subShareUserCode,
                                    ww: _d.ww,
                                    wpType: parseInt(_v1.CpType), // 网点类型
                                };
                                return false;
                            }
                        });
                    });
                });
                // 保存默认
                if (!params) {
                    addressClickFlag = true;
                } else {
                    comp.print.data.savePddWdSet(params, function() {
                        addressClickFlag = true;
                        dom.qlrr.chooseYunZhan($this, $dom);
                        dom.qlrr.branchChanged((arg || {}).exCode || exuserId);
                    }, function() {
                        addressClickFlag = true;
                    });
                }
            });
            // 是否使用关联店铺事件
            dom.find('.useLinkShop').click(function() {
                if ($('.useLinkShop').is(':checked')) {
                    $('.set_link_dp').show();
                } else {
                    $('.set_link_dp').hide();
                }
            });

            $('#useOtherShop').off('click').click(function() {
                if ($('#useOtherShop').is(':checked')) {
                    $('.use_dpdm_box').show();
                    const scrollDom = dom.find('.goods_net_right .address_scroll');
                    const sh = scrollDom[0].scrollHeight;
                    scrollDom[0].scrollTop = sh;
                } else {
                    // 取消店铺代码
                    if (_data.accountKey != null) {
                        // if (confirm("确定要取消该店铺的关联？")) {
                        // 删除关联店铺代码
                        that.deleteYunZhanShopCode(domData.currentTempInfo.ModeListShow.Exuserid, domData.currentTempInfo.ModeListShow.Mode_ListShowId, domData.currentTempInfo.ModeListShow.Exid, function() {
                            // $(".use_dpdm_box").hide();
                            // $("#otherShopCode").val("");
                            that.getPddWdList({
                                exCode: domData.currentTempInfo.ModeListShow.ExCode,
                                modeListShowId: domData.currentTempInfo.ModeListShow.Mode_ListShowId,
                            }, dom.qlrr.loadPddWd);
                            const eventObj = comp.Print.eventObj;
                            if (eventObj && eventObj.branchChanged) {
                                const list = eventObj.branchChanged;
                                list.each(function(ind, fun) {
                                    // fun(domData.currentTempInfo.ModeListShow.Mode_ListShowId);
                                    fun(domData.currentTempInfo.ModeListShow.ExCode);
                                });
                            }
                        }, 'pddWd');

                    } else {
                        $('.use_dpdm_box').hide();
                    }
                }
            });
            // 标识为添加了店铺代码 回填店铺代码

            if (_data.accountKey != null) {
                $('#useOtherShop').trigger('click');
                $('#otherShopCode').val(_data.accountKey.replace(/(.{5}).*(.{5})/, '$1**********$2'));// 回填加密的店铺代码
            }

            // 提交店铺代码确认事件
            $('#submitShopCodeBtn').click(function() {
                const shopCodeFromDom = $('#otherShopCode').val();
                if (!_data.accountKey && shopCodeFromDom.indexOf('*') > -1) { // 如果用户自己输入的 code 就包含星号
                    Tatami.showFail('店铺代码不含*，请重新输入');
                    return;
                }
                const shopCode = shopCodeFromDom.indexOf('*') > -1 ? _data.accountKey : shopCodeFromDom;// 店铺代码如果表单中的 value 不包含 * 则用表单里的，否则用 model.Data.ShopCode.ShopCode
                $('#otherShopCode').val(shopCode.replace(/(.{5}).*(.{5})/, '$1**********$2')); // 加密店铺代码并填充进去
                if (shopCode == '') {
                    Tatami.showFail('店铺代码不能为空');
                    return;
                }
                // 保存 回调函数：刷新获取云栈网点设置方法
                that.saveYunZhanShopCode({
                    exuserId,
                    modeListShowId,
                    shopCode,
                    type: 'pddWd',
                }, function() {
                    that.getYunZhanList({
                        exCode: domData.currentTempInfo.ModeListShow.ExCode,
                        modeListShowId: domData.currentTempInfo.ModeListShow.Mode_ListShowId,
                        kddType: domData.currentTempInfo.ModeListShow.KddType,
                    }, dom.qlrr.loadPddWd);
                });
            });

            // 假如没有默认网点或为匹配到默认网点，则保存第一个网点为默认网点
            if (_data) {
                if (_data.address && !$.isEmptyObject(_data.address)) {
                    _defAddr = _data.address;
                    _defAddr.id = encodeURIComponent((_defAddr.ww || '') + (_defAddr.f_s || '') + (_defAddr.f_c || '') + (_defAddr.f_q || '') + (_defAddr.town || '') + (_defAddr.f_addr || ''));
                    _$net = $dom.find('.input_yzSet[value="' + _defAddr.id + '"]');
                    if (_defAddr.shared && _defAddr.isRelation) { // 加一层分享单号过滤 && 关联店铺分享单号
                        _$net = _$net.filter('[data-shared=' + _defAddr.shared + '][data-relationUserId=' + _defAddr.relationUserId + ']:eq(0)');
                    } else if (_defAddr.shared) { // 加一层分享单号过滤
                        _$net = _$net.filter('[data-shared=' + _defAddr.shared + ']:eq(0)');
                    }
                    if (_defAddr.branchCode) { // 加一层branchCode过滤
                        _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']:eq(0)');
                    }
                    dom.qlrr.chooseYunZhan(_$net, $dom);
                }
                const _$radio = $dom.find('.input_yzSet');
                if (_$radio.length && _$radio.filter(':checked').length == 0) {
                    $dom.find('[data-act-name="choose-default"]:eq(0)')[0].click();
                }
            }
        };

        dom.qlrr.loadPddWdlistHtml = function(data) {
            const level = sessionStorage.getItem('level');
            let netPointHtml = '<div class="add_list_adrs">';
            const isShared = data.shared;
            const sharedIconStr = data.shared
                ? (data.relationShopName
                    ? '<span >（' + data.relationShopName + '）</sapn>'
                    : '')
                + '<i class="iconfont ifont-fenxiang ml-5"></i>'
                : '';
            if (data.ww) {
                netPointHtml += '<label exuserId="' + data.exuserid + '" class="yzShopName">' + data.ww + sharedIconStr + '</label>';
            }

            // 如果relationUserLevel不存在，则是直接分享
            const isDirectShare = isShared && (data.relationUserLevel === undefined);

            // 如果relationUserLevel存在，则是来自关联店铺的分享
            const isRelationShare = isShared && (data.relationUserLevel !== undefined);

            let showUpdateLevelTips = false; // 是否展示高级版订购文案

            // 如果是直接分享的网点，本身必须是高级版
            if (isDirectShare && level < 5 && data.isAdvancedCheck) {
                netPointHtml += '<p style="color:#eb9e26;padding-left:0;">订购高级版后可继续使用，<a href="https://mms.pinduoduo.com/service-market/service-detail?detailId=162" target="_blank" style="color:#eb9e26;">前往订购</a></p>';
                showUpdateLevelTips = true;
            }

            // 如果是关联分享的网点，关联的店铺必须是高级版
            if (data.isRelationUserExpire) {
                netPointHtml += '<p style="color:#eb9e26;padding-left:0;">【' + data.relationShopName + '】的订购已过期，请续费</p>';
                showUpdateLevelTips = true;
            } else if (isRelationShare && data.relationUserLevel < 5 && data.isAdvancedCheck && !data.isRelationUserExpire) {
                netPointHtml += '<p style="color:#eb9e26;padding-left:0;">如需使用，请把【' + data.relationShopName + '】升级至高级版</p>';
                showUpdateLevelTips = true;
            }

            if (data.branchList && data.branchList.length) {
                data.branchList && $.each(data.branchList, function(i1, v1) {
                    const stopSharedBranchCodes = data.stopSharedBranchCodes || [];
                    // 通过 BranchName 判断直营快递是否停止分享，直营没有 BranchCode。
                    const isThisBranchCodeStopShared = v1.BranchCode ? stopSharedBranchCodes.includes(v1.BranchCode) : stopSharedBranchCodes.includes(v1.BranchName); // 本 branch 是否停止了分享
                    netPointHtml += '<div class="add_net_box net_point" >';
                    netPointHtml += '<div class="add_net_set" >';
                    // 如果是非直营，或者是分享单号(分享单号只能分享有限个单号)
                    if (v1.CpType == 1 || isShared) {
                        // 其它账号剩余单号展示
                        let accountHtml = '';

                        if (v1.vasAccountCols) {
                            accountHtml = v1.vasAccountCols.map(function(account) {
                                return '<br/><span class="f_gray">' + account.accountTypeDesc + '：可用单号 <i>' + account.quantity + '</i> / 已用单号 <i>' + account.allocatedQuantity + '</i></span>';
                            }).join('');
                        }

                        netPointHtml
                            // '<h4>' + (v1.BranchName||"")  + '(' + (v1.BranchCode||"") + ')</h4>'+
                            += (v1.CpType == 1 // CpType == 1 是非直营 ，其他情况是直营
                                ? `<h4>${v1.BranchName || ''} ${v1.BranchCode ? `(${v1.BranchCode})` : ''}</h4>`
                                : `<h4>${comp.base.getBranchName(v1.CpCode || '')}</h4>`
                            )
                            + ((isThisBranchCodeStopShared && !showUpdateLevelTips) // 如果停止分享，且外层没有高级版订购提示，才展示停止分享文案
                                ? ('<span style="color:#eb9e26;">当前使用的网点已停止分享</span>')
                                : ('<span class="f_gray">可用单号 <i>' + v1.Quantity + '</i> / 已用单号 <i>' + v1.AllocatedQuantity + '</i></span>' + accountHtml)
                            );
                    } else { // 直营快递 可直接使用
                        netPointHtml += '<h4>' + comp.base.getBranchName(v1.CpCode || '') + '</h4>'
                            + ((isThisBranchCodeStopShared && !showUpdateLevelTips) // 如果停止分享，且外层没有高级版订购提示，才展示停止分享文案
                                ? ('<span style="color:#eb9e26;">当前使用的网点已停止分享</span>')
                                : ('<span class="f_gray">【直营快递,可直接使用】</span>')
                            );

                    }
                    netPointHtml += '</div>';
                    netPointHtml += '<div>';
                    // 遍历网点地址列表
                    v1.AddressList && $.each(v1.AddressList, function(i3, v3) {
                        const _addr = (v3.SendProvince || '') + (v3.SendCity || '') + (v3.SendDistrict || '') + (v3.SendTown || '') + (v3.SendAddress || '');
                        v3.id = encodeURIComponent((data.ww || '') + _addr);
                        netPointHtml += '<div class="dp_adres_box"><p class="netPointAddress yzAddr" data-act-name="choose-default">'
                            + '<input class="input_radio input_yzSet" type="radio" name="input_yzSetName" value="' + v3.id + '"  data-branchcode=' + v1.BranchCode + ' data-shared=' + data.shared + ' data-relationuserid=' + data.relationUserId + ' data-isrelation=' + data.isRelation + (isThisBranchCodeStopShared ? ' disabled="disabled"' : '') + '><span>' + _addr + '</span>'
                            + '</p></div>';
                    });
                    netPointHtml += '</div>';
                    netPointHtml += '</div>';
                });
            } else { // 其他错误
                if (data.ErrMsg && data.ErrMsg.indexOf('Invalid ShopCode') > -1) {
                    netPointHtml += '<p style="" class="f_gray outDateShop" data-erro="invalidShopCode" exuserId="' + data.exuserid + '">当前使用的网点<span style="color:#d75fd2;">店铺代码已失效</span>，如继续使用，重新添加关联店铺代码</p>';
                } else if (data.ErrMsg) {
                    netPointHtml += '<p style="" class="f_gray">' + (data.ErrMsg || data.message || '当前店铺尚未开通此快递的电子面单服务') + '</p>';
                } else {
                    netPointHtml += '<p style="" class="f_gray">' + (data.ErrMsg || data.message || '当前店铺尚未开通此快递的电子面单服务') + '</p>'
                        + '<p><a href="//mms.pinduoduo.com/logistics/open" style="display: inline-block;color:#b9cede;" target="_blank">申请开通</a></p>';
                }
            }
            return netPointHtml + '</div>';
        };

        // 展示云栈设置信息 东方
        dom.qlrr.showYunZhanSetting = function(model, exuserId, subUserId, modeListShowId, exid) {
            if (dom.qlrr.currentTemp.Mode_ListShowId == modeListShowId) {
                if (model.IsError == false) {
                    // if (model.Code=="200") {
                    // 加载云栈设置html
                    dom.qlrr.loadYunZhanSettingHtml(model, exuserId, subUserId, modeListShowId, exid);
                    // 获取用户的云栈网点设置信息
                    that.getYunZhanUserSetting(exuserId, subUserId, modeListShowId, exid, dom.qlrr.showYunZhanUserSetting);
                    // } else {
                    //    alert("请输入正确的店铺代码");
                    // }
                } else {
                    dom.qlrr.loadYunZhanErrorHtml();
                }
            }
        };

        // 如果全部完成，则会调用 showYunZhanSetting ，进度条会被重新渲染为 display：none；
        dom.qlrr.loadProgress = function(progress, modeListShowId) {
            if (dom.qlrr.currentTemp.Mode_ListShowId == modeListShowId) {
                dom.find('#progressBar').show();
                if (progress == 0) {
                    dom.find('#progressBar span').text('');
                } else if (progress == 100) {
                    dom.find('#progressBar').hide();
                } else {
                    dom.find('#progressBar span').text(~~(progress * 100) + '%');
                }
            }
        };

        dom.on('click', '.concatFhd', function() {
			if( comp.Print.chenkAuthor()) return false
            let _this = this;
                let concatStatus = 0;
            if ($(_this).is(':checked') == true) {
                const isYilianTemp = comp.Print.Data.currentTempInfo.ModeList.WidthPaper === 760;
                if (isYilianTemp) {
                    if (comp.Print.Data.hasNoRM_yilian) { // 如果没有热敏发货单
                        $(_this).prop('checked', false);
                        // Tatami.showFail('请先创建一联热敏发货单！')
                        window.dialog.show({
                            width: 600,
                            height: 400,
                            okName: '点此新建',
                            content: `<div style="text-align:center;"><h3>请先创建一联热敏发货单</h3><img style="padding-top: 50px;" src="https://static.kuaidizs.cn/resources/img/print/create_yilianRMFhd_guide.gif"" alt="操作示意图"><div>`,
                            okCb() {
                                dom.qlrr.close();
                                // 帮助用户选中热敏一联单
                                that.addFhd(() => {
                                    resObj.showkddMain();
                                }, ($dom) => {
                                    $dom.find('input[value="669"]').prop('checked', true);
                                });
                            },
                        });

                        return;
                    }
                } else if (comp.Print.Data.hasNoRM_common) { // 如果没有热敏发货单
                        Tatami.showFail('请先创建普通热敏发货单！');
                        $(_this).prop('checked', false);
                        return;
                    }

                concatStatus = 1;
                $('.concatFhdTips').show();
            } else {
                $('.concatFhdTips').hide();
            }
            domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd = concatStatus;
            that.changeConcatState(domData.currentTempInfo.ModeListShow.Mode_ListShowId, concatStatus, function(data) {
            });
        });
        // 承诺达可达服务 勾选
        dom.on('click', '.reachableService', function() {
            const modeListshowId = domData.currentTempInfo.ModeListShow.Mode_ListShowId;
            const status = $(this).is(':checked') == true ? 1 : 0;
            const params = {
                modeListshowId,
                status,
            };
            domData.currentTempInfo.ModeListShow.isCnd = status;
            comp.print.data.reachableServiceRequest(params);
            // 承诺达樱桃模版 isCnd字段变更
            Tatami.pub('printBatch.modeListIsCndChange', modeListshowId, status);
        });
        dom.qlrr.getConcatFhdHtml = function() {
            // 供应商版不能打印发货单，暂时屏蔽此功能
            if (['supplier'].includes(comp.Print.Data.platform)) return '';

            if (!comp.Print.configFunc('IsConcatFhd')) {
                return '';
            } else {
                return '';
                // TODO 融合打印暂时隐藏交替打印选项
                // return `<div class="dyfs-xz-box">
                //             <label class="on"><input type="checkbox" class="input_radio concatFhd">一张快递单，一张发货单交替打印<i class="newaction" style="position: static;top: 0;right: 0;display: inline-block;margin-left: 4px;"></i></label>
                //             <p class="dyfs-xz-p concatFhdTips" style="display:none;">
                //                 <span>注意：1）请选择热敏打印机打印</span>
                //                 <span class="sdfs-xz-span">2）发货单按热敏样式打印</span>
                //                 <span class="sdfs-xz-span">3）如果有多个热敏发货单，默认打印第一个热敏发货单</span>
                //             </p>
                //         </div>`;
            }
        };

        // 加载云栈设置html
        dom.qlrr.loadYunZhanSettingHtml = function(model, exuserId, subUserId, modeListShowId, exid) {
            const advancedSet = JSON.parse(sessionStorage.getItem('advancedSet'));
            let yunZhanSettingHtml = '';
                 let _isConcatFhd = domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd;
                 let _kddType = domData.currentTempInfo.ModeList.KddType;
                 let _exCode = domData.currentTempInfo.ModeList.Excode;
                 let isCnd = domData.currentTempInfo.ModeListShow.isCnd;
                 let _styleId = domData.currentTempInfo.ModeList.StyleId;

            if (_kddType > 1) {
                yunZhanSettingHtml += dom.qlrr.getConcatFhdHtml();
            }
            // 承诺达樱桃可达
            if (advancedSet['cndYtReache'] == 1 && _exCode == 'CP468398') {
                if (isCnd) {
                    yunZhanSettingHtml += '<div class="dyfs-xz-box"><label style="color:#fff;"><input checked type="checkbox" class="input_radio reachableService">使用承诺达（樱桃项目）地址可达判断服务</label></div>';
                } else {
                    yunZhanSettingHtml += '<div class="dyfs-xz-box"><label style="color:#fff;"><input type="checkbox" class="input_radio reachableService">使用承诺达（樱桃项目）地址可达判断服务</label></div>';
                }
            }
            yunZhanSettingHtml += '<h2>选择发货网点'
                + (_exCode === 'SNWL' ? '<a href="//www.kuaidizs.cn/helpMap/getDetail?detailId=489" target="_blank" class="fc-b9cede ft-normal ml_10">如何申请苏宁电子面单号？</a>' : '')
                + '</h2><div class="address_scroll" style=""></div>';
            // height: 480px;_height:450px;overflow:hidden;overflow-y:auto
            domData.netPointDiv.html(yunZhanSettingHtml);
            scroll = dom.find('.address_scroll');
            that.niceScroll(scroll);

            let netPointHtml = '<div class="add_list_adrs">';
            let invalidCodeErro = false;

            // 遍历店铺
            $.each(model.Data.Users, function(i, v) {
                netPointHtml += '<label exuserId="' + v.ExUserID + '" class="yzShopName">' + v.Nick + '</label>';
                if (v.isSuccess && v.WangDianList != null) { // mm-modify
                    // 遍历店铺的网点信息
                    $.each(v.WangDianList, function(i1, v1) { // v1.Quantity:可用单号  v1.AllocatedQuantity:已用单号
                        const _service = [];
                        netPointHtml += '<div class="add_net_box">';
                        netPointHtml += '<div class="add_net_set">';
                        if (parseInt(v1.CpType) != 1 && parseInt(v1.CpType) != 4) { // 非直营快递
                            netPointHtml += '<h4>' + (v1.BranchName || comp.base.getBranchName(v1.BranchCode) || v1.BranchCode) + '(' + v1.BranchCode + ')</h4><span class="f_gray">可用单号 <i>' + v1.Quantity + '</i> / 已用单号 <i>' + v1.AllocatedQuantity + '</i></span>';
                        } else { // 直营快递 可直接使用
                            netPointHtml += '<h4>' + (v1.BranchName || comp.base.getBranchName(v1.BranchCode) || v1.BranchCode) + '(' + v1.BranchCode + ')</h4><span class="f_gray">【直营快递,可直接使用】</span>';
                        }
                        netPointHtml += '</div>';

                        // 获取服务
                        v1.serviceInfoCols && $.each(v1.serviceInfoCols, function(j, k) {
                            k && _service.push(k.serviceCode);
                        });
                        netPointHtml += '<div class="net_point" data-service="' + _service.join(',') + '">';
                        // 遍历网点地址列表
                        $.each(v1.AddressList, function(i2, v2) {
                            const addressInfo = (v2.SendProvince || '') + (v2.SendCity || '') + (v2.SendDistrict || '') + (v2.SendAddress || ''); // mm_modify   ExuserId -> SellerId
                            netPointHtml += '<div class="dp_adres_box">'
                                + '<p class="netPointAddress yzAddr" data-id="' + v2.WaybillAddressId + '" data-userId="' + v1.SellerId + '"><input class="input_radio input_yzSet" type="radio" name="input_yzSetName"><span>' + addressInfo + '</span></p>'
                                + '</div>';
                        });
                        netPointHtml += '</div>';
                        netPointHtml += '</div>';
                    });
                } else { // Invalid session
                    if (v.ErrMsg && (v.ErrMsg.indexOf('Invalid session') > -1 || v.ErrMsg.indexOf('SessionKey 不能为空') > -1)) {
                        // session过期
                        netPointHtml += '<p style="" class="f_gray outDateShop" exuserId="' + v.ExUserID + '">当前店铺授权已过期，请<a href="https://oauth.taobao.com/authorize?response_type=code&client_id=12158997&redirect_uri=http://route.kuaidizs.cn/forward.jsp" target="_blank" style="color:#d75fd2;">重新授权</a></p>';
                    } else if (v.ErrMsg.indexOf('获取发货地址失败') > -1) { // 请先在淘宝后台物流宝服务中设置发货地
                        netPointHtml += '<p style="" class="f_gray outDateShop" data-erro="notHasShipAddr" exuserId="' + v.ExUserID + '">当前使用的网点所在店铺授权未设置发货地址，请先在淘宝后台物流宝服务中<a href="https://wuliu.taobao.com/user/logis_tools.htm" style="color:#d75fd2;" target="_blank">设置发货地址</a></p>';
                    } else if (v.ErrMsg.indexOf('Invalid ShopCode') > -1) {
                        invalidCodeErro = true;
                        netPointHtml += '<p style="" class="f_gray outDateShop" data-erro="invalidShopCode" exuserId="' + v.ExUserID + '">当前使用的网点店铺代码已失效，如继续使用，重新添加关联店铺代码</p>';
                    } else {
                        netPointHtml += '<p style="" class="f_gray">当前店铺尚未开通此快递的电子面单服务</p>\
                    <a href="//wlmart.wuliu.taobao.com/user/service_frame.htm?spm=a1z0f.7.0.0.zteMX4&temp=1&serviceType=28" style="padding-left: 10px;display: inline-block;margin-top: 10px;color:#b9cede;" target="_blank">申请开通</a><br />';
                    }
                }
            });

            // 加载进度
            netPointHtml += '<div id="progressBar" style="margin-top:20px;text-align:left;display:none;"><img src="/resources/img/print/loading_small.gif" style="vertical-align: bottom;">读取中,请耐心等待<img src="/resources/img/print/loading_icon.gif" style="vertical-align: bottom;"><span></span></div>';


            // 如果没有关联店铺  提示去添加关联店铺
            netPointHtml += '<div class="f_gray dzmd_qh_box">\
                        <!--使用关联店铺电子面单账号-->\
                        <div style="display:none;" id="is_use_link_shop">\
                            <p class="pad_l0">\
                            <label class="on"><input type="checkbox" class="input_check" id="useLinkShop" >使用关联店铺电子面单账号(<span class="f_geen_blue">荐</span>)</label>\
                            </p>\
                            <div class="set_link_dp" style="display:none;">\
                                <a href="' + window.location.search.split('#')[0] + '#/sygj/connectStore/ " target="_blank">设置关联店铺</a>\
                                <p class="">\
                                    如果希望多店铺共用一个菜鸟电子面单账号，推荐使用关联店铺；同时可实现一键打印所有店铺订单，效率翻倍!\
                                </p>\
                            </div>\
                        </div>\
                        <!--使用其他店铺电子面单账号-->\
                        <div class="useOtherShopcontainer">\
                        <p class="pad_l0"><label><input type="checkbox" class="input_check" id="useOtherShop">使用其他店铺电子面单账号</label></p>\
                        <div class="use_dpdm_box" style="display:none;">\
                            <input type="text" placeholder="输入对方店铺代码" id="otherShopCode"><br>\
                            <a href="javascript:void(0)" class="expr_btn_js" id="submitShopCodeBtn">确认</a>';
            if (invalidCodeErro) {
                netPointHtml += '<span style="color: #d75fd2;" class="shopCodeErroInfo">对方店铺代码已失效</span>';
            }

            netPointHtml += '<p>店铺代码可在“实用工具-管理关联店铺”中找到，由对方店铺发送给您。</p>\
                        </div>\
                        </div>\
                    </div>\
                    </div>';


            scroll.html(netPointHtml);

            const styleId = comp.Print.Data.currentTempInfo.ModeList.StyleId;
            const isYilianTemp = comp.base.isYilianTemp(styleId);
            if (isYilianTemp) {
                if (_isConcatFhd == 1 && !comp.Print.Data.hasNoRM_yilian) {
                    $('.concatFhd').prop('checked', true);
                    $('.concatFhdTips').show();
                } else {
                    $('.concatFhd').removeAttr('checked');
                    $('.concatFhdTips').hide();
                }
            } else if (_isConcatFhd == 1 && !comp.Print.Data.hasNoRM_common) {
                    $('.concatFhd').prop('checked', true);
                    $('.concatFhdTips').show();
                } else {
                    $('.concatFhd').removeAttr('checked');
                    $('.concatFhdTips').hide();
                }


            // 绑定云栈是否使用关联店铺事件
            if (model.Data.Users.length <= 1) {
                $('#is_use_link_shop').show();
            }
            // 是否使用关联店铺事件
            $('#useLinkShop').click(function() {
                if ($('#useLinkShop').is(':checked')) {
                    $('.set_link_dp').show();


                } else {
                    $('.set_link_dp').hide();
                }
            });
            $('#useOtherShop').off('click').click(function() {
                if ($('#useOtherShop').is(':checked')) {
                    $('.use_dpdm_box').show();
                    const scrollDom = dom.find('.goods_net_right .address_scroll');
                    const sh = scrollDom[0].scrollHeight;
                    scrollDom[0].scrollTop = sh;
                } else {
                    // 取消店铺代码
                    if (model.Data.ShopCode != null) {
                        // if (confirm("确定要取消该店铺的关联？")) {
                        // 删除关联店铺代码
                        that.deleteYunZhanShopCode(domData.currentTempInfo.ModeListShow.Exuserid, domData.currentTempInfo.ModeListShow.Mode_ListShowId, domData.currentTempInfo.ModeListShow.Exid, function() {
                            // $(".use_dpdm_box").hide();
                            // $("#otherShopCode").val("");
                            that.getYunZhanSetting(exuserId, subUserId, domData.currentTempInfo.ModeList.Excode, modeListShowId, exid, dom.qlrr.showYunZhanSetting);

                            dom.qlrr.branchChanged(domData.currentTempInfo.ModeListShow.ExCode);

                        });

                    } else {
                        $('.use_dpdm_box').hide();
                    }
                }
            });
            // 标识为添加了店铺代码 回填店铺代码

            if (model.Data.ShopCode != null) {
                $('#useOtherShop').trigger('click');
                $('#otherShopCode').val(model.Data.ShopCode.ShopCode.replace(/(.{5}).*(.{5})/, '$1**********$2'));// 回填加密的店铺代码
            }

            // 提交店铺代码确认事件
            $('#submitShopCodeBtn').click(function() {
                const shopCodeFromDom = $('#otherShopCode').val();
                if (!model.Data.ShopCode && shopCodeFromDom.indexOf('*') > -1) { // 如果用户自己输入的 code 就包含星号
                    Tatami.showFail('店铺代码不含*，请重新输入');
                    return;
                }
                const shopCode = shopCodeFromDom.indexOf('*') > -1 ? model.Data.ShopCode.ShopCode : shopCodeFromDom;// 店铺代码如果表单中的 value 不包含 * 则用表单里的，否则用 model.Data.ShopCode.ShopCode
                $('#otherShopCode').val(shopCode.replace(/(.{5}).*(.{5})/, '$1**********$2')); // 加密店铺代码并填充进去
                if (shopCode == '') {
                    Tatami.showFail('店铺代码不能为空');
                    return;
                }
                // 保存 回调函数：刷新获取云栈网点设置方法
                that.saveYunZhanShopCode({ exuserId, modeListShowId, shopCode }, function() {
                    that.getYunZhanSetting(exuserId, subUserId, domData.currentTempInfo.ModeList.Excode, modeListShowId, exid, dom.qlrr.showYunZhanSetting);
                });
            });
            // 加载点击地址事件--保存或者删除用户设置
            $('.input_yzSet').click(function(event) {
				if( comp.Print.chenkAuthor()) return false
                // scroll.on('click', '.input_yzSet',function(event){
                event.stopPropagation();
                if (!addressClickFlag) {
                    return;
                }
                addressClickFlag = false;
                const $this = $(this);
                // 当前地址已启用
                if ($this.next('span').children('em').length > 0) {
                    addressClickFlag = true;
                    return;
                } else {
                    let userSetting = comp.Print.Data.yunzhanUserSetting;// 当前用户网点设置
                    const addressId = $this.parent('p').attr('data-id');
                    const settingExuserId = $this.parent('p').attr('data-userId');
                    if (userSetting != null) {
                        $('.netPointAddress[data-id=\'' + userSetting.AddressId + '\']').children('span').children('em').remove();
                        // $(".netPointAddress[data-id='" + userSetting.AddressId + "']").next('i').remove();
                        // 删除启用的 保存当前点击的
                        dom.qlrr.deleteYunZhanSetting(userSetting.Exuserid, subUserId, modeListShowId, exid, function() {
                            if (comp.Print.Data.yunzhanSetting.Users != null) {
                                $.each(comp.Print.Data.yunzhanSetting.Users, function(i, v) {
                                    !v.ErrMsg && (v.ErrMsg = '');
                                    if (v.isSuccess && v.ErrMsg.indexOf('Invalid session') <= 0 && v.ErrMsg.indexOf('设置发货地') <= 0) {
                                        $.each(v.WangDianList, function(i1, v1) {
                                            $.each(v1.AddressList, function(i2, v2) {
                                                if (v2.WaybillAddressId == addressId) {
                                                    userSetting.Branch_code = v2.BranchCode; // 网点代码
                                                    userSetting.F_p = v2.SendProvince; // 发件省
                                                    userSetting.F_c = v2.SendCity; // 发件市
                                                    userSetting.F_q = v2.SendDistrict; // 发件区
                                                    userSetting.F_addr = v2.SendAddress; // 发件详细地址
                                                    userSetting.F_mobile = v2.SendMobile; // 发件人手机
                                                    userSetting.F_name = v2.SendName; // 发件人姓名
                                                    userSetting.F_tel = v2.SendPhone;
                                                    userSetting.F_town = v2.SendTown;
                                                    userSetting.F_zip = v2.SendZip;
                                                    userSetting.AddressId = v2.WaybillAddressId;
                                                    userSetting.Branch_name = v1.BranchName;
                                                    // 拿到当前点击的所在的userid
                                                    userSetting.Exuserid = settingExuserId;
                                                }
                                            });
                                        });
                                    }
                                });
                            }
                            dom.qlrr.saveYunZhanSetting(userSetting, dom.qlrr.showYunZhanUserSetting);
                        });
                    } else { // 之前没有设置过 直接保存
                        userSetting = {};
                        userSetting.Exuserid = settingExuserId;
                        userSetting.Exsubid = subUserId;
                        userSetting.Exid = exid;
                        userSetting.ListShowId = modeListShowId;
                        if (comp.Print.Data.yunzhanSetting.Users != null) {
                            $.each(comp.Print.Data.yunzhanSetting.Users, function(i, v) {
                                !v.ErrMsg && (v.ErrMsg = '');
                                if (v.isSuccess && v.ErrMsg.indexOf('Invalid session') <= 0 && v.ErrMsg.indexOf('设置发货地') <= 0) {
                                    $.each(v.WangDianList, function(i1, v1) {
                                        $.each(v1.AddressList, function(i2, v2) {
                                            if (v2.WaybillAddressId == addressId) {
                                                userSetting.Branch_name = v1.BranchName;
                                                userSetting.Branch_code = v2.BranchCode; // 网点代码
                                                userSetting.F_p = v2.SendProvince; // 发件省
                                                userSetting.F_c = v2.SendCity; // 发件市
                                                userSetting.F_q = v2.SendDistrict; // 发件区
                                                userSetting.F_addr = v2.SendAddress; // 发件详细地址
                                                userSetting.F_mobile = v2.SendMobile; // 发件人手机
                                                userSetting.F_name = v2.SendName; // 发件人姓名
                                                userSetting.F_tel = v2.SendPhone;
                                                userSetting.F_town = v2.SendTown;
                                                userSetting.F_zip = v2.SendZip;
                                                userSetting.AddressId = v2.WaybillAddressId;
                                                userSetting.Type = parseInt(v1.CpType);
                                            }
                                        });
                                    });
                                }
                            });
                        }
                        dom.qlrr.saveYunZhanSetting(userSetting, dom.qlrr.showYunZhanUserSetting);
                    }
                }
            });

            $('.dp_adres_box').click(function() {
                $(this).children().children('.input_yzSet').trigger('click');
            });
        };

        dom.qlrr.checkNetService = function(netService) {
            let _ser = (netService || '').split(',');
                let _curSer;
                let _noMatch = [];
                let isCaiNiao;

            isCaiNiao = comp.base.getTempStyle('CNkuaidi', (domData.currentTempInfo.ModeList || {}).StyleId);
            if (!isCaiNiao) {
                return '';
            }

            _curSer = domData.currentTempInfo.ModeLogisticsItems || [];
            // 模板尚未勾选服务，无需服务校验
            if (!_curSer.length) {
                return '';
            }

            // 移除其它异常提示
            domData.netPointDiv.find('.not-match-info').remove();

            // 中通菜鸟判断是否需要通过serviceProperty来进行服务校验匹配
            const hasServiceProperty = _curSer.some(item => item.serviceProperty);

            $.each(_curSer, function(i1, v1) {
                const { serviceProperty } = v1;
                // 对于serviceProperty中segmentCode和serviceCode都与网点相匹配的，则校验通过
                const segmentCode = serviceProperty && JSON.parse(serviceProperty).segmentCode;
                const isMatchService = hasServiceProperty
                    ? (segmentCode && _ser.includes(segmentCode) && $.inArray(v1.serviceCode, _ser) > -1)
                    : $.inArray(v1.serviceCode, _ser) > -1;
                // 货到服务，不做是否匹配提示，因为货到付款服务只有货到付款订单会应用。
                if (v1.serviceCode !== 'SVC-COD' && !isMatchService && v1.serviceCode !== 'tjw') {
                    _noMatch.push(v1.serviceName);
                }
            });

            // 网点服务校验通过
            if (!_noMatch.length) {
                return '';
            }

            return '<div class="not-match-info">'
                + '<p style="overflow: hidden;">'
                + '<i class="icon-warn" style="margin-top: 7px;"></i>'
                + '<span style="color: #EB9E26;width: 193px;float: right;">添加的模板服务与菜鸟后台开通的网点服务不匹配：</span>'
                + '</p>'
                + '<ul>'
                + '<li>请切换其他网点</li>'
                + '<li>编辑模板，修改模板服务</li>'
                + '<li><a href="//waybill.wuliu.taobao.com/first_page.htm" target="_blank">点此开通网点服务:' + _noMatch.join(',') + '</a></li>'
                + '</ul>'
                + '</div>';


            // 网点服务校验不通过
            // 1)收集展示未开通服务
            // 2)展示当前其他校验通过的服务
            // _wdList = (comp.Print.Data.yunzhanSetting||{}).Users;
            // if(!_wdList){
            //     return "";
            // }
            // _matchBranch = [];
            // _curSer = _curSer.sort().join(',');
            // $.each(_wdList, function(i1, v1) {
            //     v1.WangDianList && $.each(v1.WangDianList, function() {
            //         var _ser = [];
            //         this.serviceInfoCols && $.each(function() {
            //             _ser.push(this.serviceCode);
            //         });
            //         if(_ser.sort().join(',') === _curSer){
            //             _matchBranch.push( (this.BranchName || "") + "("+this.BranchCode+")" );
            //         }
            //     });
            // });
            // var serial = 0;
            // if(_matchBranch.length){
            //     serial = 1;
            // }

            // return '<div class="not-match-info">'
            //         +'<p style="color: #EB9E26;">模板服务与网点服务不匹配，可任选以下一个方式处理：</p>'
            //         +'<ul>'
            //             + (serial?'<li>'+ (serial++) +')请切换到其他网点：'+ _matchBranch.join('或') +'</li>':'')
            //             +'<li>'+ (serial++) +')编辑模板，修改模板服务与网点服务一致 ：'+ _noMatch.join(',') +'</li>'
            //             +'<li>'+ (serial++) +')<a href="//waybill.wuliu.taobao.com/first_page.htm" target="_blank">点此开通网点服务:'+ _noMatch.join(',') +'</a></li>'
            //         +'</ul>'
            //        +'</div>'
        };
        // 加载云栈用户设置html
        dom.qlrr.showYunZhanUserSetting = function($target) {
            let noMatchHtml;

            if (printData.yunzhanUserSetting != null) { // 库里存了用户设置的云栈网点信息
                // printData.yunzhanUserSetting.AddressId:用户选择的地址id
                $('.netPointAddress').removeClass('on');

                // 过期的店铺
                const yzUserSetting = printData.yunzhanUserSetting;
                let isOutDate = true;
                $('.outDateShop').each(function(i, v) {
                    // 匹配到了当前选中的为过期的店铺
                    if ($(this).attr('exuserId') == yzUserSetting.Exuserid) {
                        isOutDate = false;
                        if ($(this).attr('data-erro') == 'notHasShipAddr') { // 当前网点 未设置发货地址
                            $(this).html('当前使用的网点所在店铺授权未设置发货地址，请先在淘宝后台物流宝服务中<a target=\'_blank\' href=\'https://wuliu.taobao.com/user/logis_tools.htm\' style=\'color:#d75fd2;\' >设置发货地址</a>');
                        } else if ($(this).attr('data-erro') == 'invalidShopCode') {
                            $(this).html('当前使用的网点<font style=\'color:#d75fd2;\'>店铺代码已失效</font>，如继续使用，重新添加关联店铺代码');
                        } else {
                            $(this).html('当前使用的网点所在店铺授权已过期，请<a href=\'https://oauth.taobao.com/authorize?response_type=code&client_id=12158997&redirect_uri=http://route.kuaidizs.cn/forward.jsp\' target=\'_blank\' style=\'color:#d75fd2;\'>重新授权</a>');
                        }
                        $(this).after('<div class="add_net_box"><div class="dp_adres_box"><p class="netPointAddress"><input class="input_radio input_yzSet" type="radio" name="input_yzSetName" checked="checked" disabled="disabled"><span>' + (yzUserSetting.F_p || '') + (yzUserSetting.F_c || '') + (yzUserSetting.F_q || '') + (yzUserSetting.Branch_name || '') + '</span></p></div></div>');
                    }
                });

                if (isOutDate) {
                    // 未过期的店铺
                    domData.netPointDiv.find('.netPointAddress').each(function(i, v) {
                        if ($(this).attr('data-id') == yzUserSetting.AddressId && $(this).attr('data-userId') == yzUserSetting.Exuserid) {
                            if ($(this).addClass('on').children('span').find('em').length < 1) {
                                $(this).addClass('on').children('span').append('<em>[使用中]</em>');// .after('<i class="xz_right_icon"></i>'); //<!--选中-->
                            }
                            $(this).children('input').attr('checked', 'checked');

                            noMatchHtml = dom.qlrr.checkNetService($(this).closest('.net_point').attr('data-service'));
                            noMatchHtml && $(this).append(noMatchHtml);
                            return false;
                        }
                    });

                }

                addressClickFlag = true;

                dom.qlrr.branchChanged(domData.currentTempInfo.ModeListShow.ExCode);

            } else {
                // 如果该模版 没有存用户的网点设置 加一条默认的网点设置信息
                if ($('.netPointAddress').length > 0) {
                    const defaultAddress = $($('.netPointAddress')[0]);

                    const defaultAddressId = defaultAddress.attr('data-id');
                    const userSetting = {};
                    if (comp.Print.Data.yunzhanSetting.Users != null) {
                        $.each(comp.Print.Data.yunzhanSetting.Users, function(i, v) {
                            !v.ErrMsg && (v.ErrMsg = '');
                            if (v.isSuccess && v.ErrMsg.indexOf('Invalid session') < 0 && v.ErrMsg.indexOf('获取发货地址失败') < 0) {
                                $.each(v.WangDianList, function(i1, v1) {
                                    $.each(v1.AddressList, function(i2, v2) {
                                        if (v2.WaybillAddressId == defaultAddressId && v.ExUserID == defaultAddress.attr('data-userid')) {
                                            userSetting.Branch_name = v1.BranchName;
                                            userSetting.Branch_code = v2.BranchCode; // 网点代码
                                            userSetting.F_p = v2.SendProvince; // 发件省
                                            userSetting.F_c = v2.SendCity; // 发件市
                                            userSetting.F_q = v2.SendDistrict; // 发件区
                                            userSetting.F_addr = v2.SendAddress; // 发件详细地址
                                            userSetting.F_mobile = v2.SendMobile; // 发件人手机
                                            userSetting.F_name = v2.SendName; // 发件人姓名
                                            userSetting.F_tel = v2.SendPhone;
                                            userSetting.F_town = v2.SendTown;
                                            userSetting.F_zip = v2.SendZip;
                                            userSetting.AddressId = v2.WaybillAddressId;
                                            userSetting.Type = parseInt(v1.CpType);

                                            userSetting.Exuserid = v.ExUserID;
                                            userSetting.Exsubid = v.ExSubID;
                                            userSetting.Exid = domData.currentTempInfo.ModeList.Exid;
                                            userSetting.ListShowId = domData.currentTempInfo.ModeListShow.Mode_ListShowId;
                                        }
                                    });
                                });
                            }
                        });
                    }
                    if (!$.isEmptyObject(userSetting)) {
                        dom.qlrr.saveYunZhanSetting(userSetting, function() {
                            // 保存完设置为已启用
                            if (defaultAddress.children('span').find('em').length < 1) {
                                defaultAddress.children('span').append('<em>[使用中]</em>');// .after('<i class="xz_right_icon"></i>');
                            }
                            defaultAddress.children('input').attr('checked', 'checked');

                            noMatchHtml = dom.qlrr.checkNetService(defaultAddress.closest('.net_point').attr('data-service'));
                            noMatchHtml && defaultAddress.append(noMatchHtml);

                            dom.qlrr.branchChanged(domData.currentTempInfo.ModeListShow.ExCode);
                        });
                    }
                }
            }
        };

        // 删除用户的云栈设置信息
        dom.qlrr.deleteYunZhanSetting = function(exuserId, subUserId, modeListShowId, exid, callback) {
            that.deleteYunZhanSetting(exuserId, subUserId, modeListShowId, exid, callback);
        };

        // 修改网点相关内容后的回调方法
        dom.qlrr.branchChanged = function(exCode) {

            const funcHook = window.printAPI.compHookObj?.afterBranchChangeHook;
            funcHook && funcHook(domData.currentTempInfo.ModeListShow.Exid);
        };

        // 保存用户的云栈设置信息
        dom.qlrr.saveYunZhanSetting = function(obj, callback) {
            that.saveYunZhanSetting(obj, callback);
        };

        dom.qlrr.getShouquanHtml = function() {
            // 由当前模板 kddType 和 PLAT 共同判断
            let netPointHtml = '';
            const isPw = comp.base.isPWTemp();
            const isDY = comp.base.isDYTemp();// 是抖音电子模板
            const isKs = comp.base.isKsTemp(); // 快手电子模板

            const kddType = comp.Print.Data.currentTempInfo.ModeList.KddType;

            const _href = comp.Print.getShouQuanUrl(kddType);

            const msg_tip = '如果您已经有淘宝账号，建议使用此选项';
            let msg_link = '登录淘宝授权快递助手使用菜鸟物流';

            if (comp.Print.Data.platform === 'erp') {
                return netPointHtml; // erp不需要额外授权提示
            }

            if (isPw) {
                msg_link = '登录拼多多账号授权快递助手使用电子面单';
                netPointHtml = '<p class="pad_l0">'
                    + '<a target="_blank" href="' + _href + '" class="cursor_p info-msg pwAuthorizeLink">' + msg_link + '</a></p>'
                    + '<i class="tip_hand"></i>'
                    + '</div>';
            } else if (isDY | isKs) {
                netPointHtml = '<div>'
                    + '<p class="pad_l0">'
                    + '</p>'
                    + '<i class="tip_hand"></i>'
                    + '</div>';
            } else {
                netPointHtml = '<div><p class="f_gray pad_l0" >' + msg_tip + '</p>'
                    + '<p class="pad_l0">'
                    + '<a target="_blank" href="' + _href + '" class="cursor_p info-msg">' + msg_link + '</a></p>'
                    + '<i class="tip_hand"></i>'
                    + '</div>';
            }
            return netPointHtml;
        };

        dom.qlrr.loadYunZhanlistHtml = function(resultCode, data, methodType, linkCode) {
            // 把linkCode 存起来
            dom.qlrr.data.shopCode = linkCode;
            // 加密要展示的 linkCOde
            linkCode = linkCode ? linkCode.replace(/(.{5}).*(.{5})/, '$1**********$2') : '';

            let _href; let
kddType;
            let netPointHtml = '<div class="add_list_adrs">';
            // 由当前模板 kddType 和 PLAT 共同判断
            const isPw = comp.base.isPWTemp();
            // 是抖音电子面单
            const isDY = comp.base.isDYTemp();
            const isKs = comp.base.isKsTemp(); // 快手电子模板
            const isXhs = comp.base.isXhsTemp(); // 小红书电子模板
			const isNewXhs = comp.base.isNewXhsTemp();
            const isSph = comp.base.isSphTemp(); // 小红书电子模板
            const isYz = comp.base.isYzTemp(); // 小红书电子模板

            // 是美团电子面单
            const isTHH = comp.base.isTHHTemp();
            //  是否是erp
            const isERP = comp.Print.Data.platform === 'erp';
            kddType = comp.Print.Data.currentTempInfo.ModeList.KddType;

            _href = comp.Print.getShouQuanUrl(kddType);

            const getPlatInfo = (params) => {
                const { isPw, isDY, isKs } = params;
                let platName = '';
                let helpMessageLink = ''; // 帮助链接
                if (isPw) {
                    platName = '拼多多';
                    helpMessageLink = '//www.kuaidizs.cn/helpMap/getDetail?detailId=1160';
                } else if (isDY) {
                    platName = '抖音';
                } else if (isKs) {
                    platName = '快手';
                } else if (isTHH) {
                    platName = '美团';
                } else {
                    platName = '淘宝';
                    helpMessageLink = '//www.kuaidizs.cn/helpMap/getDetail?detailId=466';
                }

                return { platName, helpMessageLink };
            };
            const platInfo = getPlatInfo({ isPw, isDY, isKs });
            const platName = platInfo.platName;
            const helpMessageLink = platInfo.helpMessageLink;

            if (methodType == YUNZHAN_AUTH_TYPE['SHOP_CODE']) {
                netPointHtml += '<div class="f_gray dzmd_qh_box">'
                    + '<div>'
                    + '<p class="pad_l0"><span>输入' + platName + '快递助手店铺代码</span></p>'
                    + '<div class="use_dpdm_box" >'
                    + '<input type="text" placeholder="输入对方店铺代码" id="otherShopCode" data-content="店铺代码" data-shopcode="' + (linkCode || '') + '" value="' + (linkCode || '') + '"><br>'
                    + '<font style="font-size:12px;color:#aaaaaa;line-height: 18px;">'
                    + '(<font style="font-size:12px;" class="info-msg">查找方式</font>'
                    + '：进入' + platName + '平台的快递助手=&gt;实用工具=&gt;管理关联店铺=&gt;找到店铺代码,'
                    + '<a href=' + helpMessageLink + ' class="info-msg" target="_blank">查看帮助</a>)</font>'
                    + '<a href="javascript:void(0)" class="expr_btn_js" id="compSubmitShopCodeBtn" data-methodtype="' + methodType + '">保存</a>'
                    + '</div>'
                    + '</div>'
                    + '</div>'
                    + '</div>';
            } else if (methodType == YUNZHAN_AUTH_TYPE['AUTH_TOKEN']) {
                netPointHtml += '<div class="f_gray dzmd_qh_box">'
                    + '<div>'
                    + '<div class="use_dpdm_box" >'
                    + '<input type="text" placeholder="请填写授权 token" id="otherShopCode" data-content="授权 token" data-shopcode="' + (linkCode || '') + '" value="' + (linkCode || '') + '"><br>'
                    + '<font style="font-size:12px;color:#aaaaaa;line-height: 18px;word-break: break-word;">'
                    + '<a href="javascript:void(0)" class="expr_btn_js" id="compSubmitShopCodeBtn" data-methodtype="' + methodType + '">保存</a>'
                    + '</div>'
                    + '</div>'
                    + '</div>';

            }
            if (data.ww) {
                // netPointHtml +=
                //     '<label exuserId="' + data.exuserid + '" class="yzShopName">'
                //     + data.ww + (methodType == 1 ?
                //         ' <a href="javascript:;" class="info-msg" data-ww='
                //         + (data.ww || '') +
                //         ' data-act-name="deleteAccount">删除账号</a>'
                //         : '') + '</label>';

                const deleteHtml = (isDY | isKs | isTHH | isXhs | isNewXhs | isSph|isYz) ? '' : ((methodType == YUNZHAN_AUTH_TYPE['ACCOUNT'] && !data.shared)
                    ? `<a href="javascript:;" class="info-msg" data-ww=${data.ww || ''} data-act-name="deleteAccount">删除账号</a>`
                    : '');
                netPointHtml
                    += `<label exuserId="${data.exuserid}" class="yzShopName">${data.ww}
                        ${!data.isSuccess && !data.branchList && data.errMsg == '帐号授权过期，请重新进行授权！'
                        ? `<span style="color:#FF0000;font-size:14px;font-weight:600">(授权过期)</span>` : ''
                    }
                        ${data.shared
                        ? (data.sharedUserNick
                            ? `<span>（${data.sharedUserNick}）</span>`
                            : ``
                        )
                        + `<i class="iconfont  ifont-fenxiang"></i>`
                        : ``
                    }
                        ${deleteHtml}
                    </label>`;
            }


            if (resultCode == 100) {
                // 抖音电子面单模板
                if (isDY && !['nzw'].includes(comp.Print.Data.platform) || isKs || isXhs || isNewXhs || isSph || isYz) {
                    netPointHtml += dom.qlrr.getDYNetPointHtml(data);
                } else if (isTHH) { // 美团电子面单模版
                    netPointHtml += dom.qlrr.getTHHNetPointHtml(data);
                } else {
                    data.branchList && $.each(data.branchList, function(i1, v1) {
                        // 淘外或ERP
                        v1.branchAccountCols && $.each(v1.branchAccountCols, function(i2, v2) {

                            // 获取服务
                            const _service = [];
                            v2.serviceInfoCols && $.each(v2.serviceInfoCols, function(j, k) {
                                k && _service.push(k.serviceCode);
                            });

                            netPointHtml += '<div class="add_net_box net_point" data-service="' + _service.join(',') + '">';
                            netPointHtml += '<div class="add_net_set" >';

                            const segmentName = segmentCodeUtils.getName(v2.segmentCode);
                            const segmentHtml = segmentName ? `<span class="segment_code" data-code="${v2.segmentCode}"> <i class="iconfont ifont-tixing1"></i>${segmentName}</span>` : '';
                            const shouldShowSegmentCode = v2.segmentCode && v2.segmentCode != 'NORMAL' && v2.segmentCode != 'SVC-COD'; //

                            // 淘宝直营加盟的status与拼多多抖音相反
                            if (comp.Print.Data.platform == 'erp' && kddType != 3) {
								if (parseInt(v1.wpType || v1.cpType) == 1 || (!!data.shared && (data.limitType == 1))) {//非直营快递
									if (!!data.shared) netPointHtml += '<span class="f_gray">可用单号 <i>' + (data.shared && v2.quantity == '-1' ? '不限量' : v2.quantity) + '</i> / 已用单号 <i>' + v2.allocatedQuantity + '</i></span>'
									else netPointHtml += '<h4>' + (v2.branchName || comp.base.getBranchName(v2.branchCode) || v2.branchCode) + '(' + v2.branchCode + ')' + segmentHtml + '</h4><span class="f_gray">可用单号 <i>' + (data.shared && v2.quantity == '-1' ? '不限量' : v2.quantity) + '</i> / 已用单号 <i>' + v2.allocatedQuantity + '</i></span>';
                                } else { // 直营快递 可直接使用
                                    netPointHtml += '<h4>' + (shouldShowSegmentCode ? ((v2.branchName || comp.base.getBranchName(v2.branchCode) || v2.branchCode) + '(' + v2.branchCode + ')' + segmentHtml) : '') + '</h4><span class="f_gray">【直营快递,可直接使用】</span>';
                                }
							} else if (parseInt(v1.wpType) == 1 || (parseInt(v1.cpType) != 1 && parseInt(v1.cpType) != 4) || (!!data.shared && (data.limitType == 1))) { // 非直营快递
                                    netPointHtml += '<h4>' + (v2.branchName || comp.base.getBranchName(v2.branchCode) || v2.branchCode) + '(' + v2.branchCode + ')' + segmentHtml + '</h4><span class="f_gray">可用单号 <i>' + (data.shared && v2.quantity == '-1' ? '不限量' : v2.quantity) + '</i> / 已用单号 <i>' + v2.allocatedQuantity + '</i></span>';
                                } else { // 直营快递 可直接使用
                                    netPointHtml += '<h4>' + (shouldShowSegmentCode ? ((v2.branchName || comp.base.getBranchName(v2.branchCode) || v2.branchCode) + '(' + v2.branchCode + ')' + segmentHtml) : '') + '</h4><span class="f_gray">【直营快递,可直接使用】</span>';
                                }

                            netPointHtml += '</div>';
                            netPointHtml += '<div>';
                            // 遍历网点地址列表
                            v2.shippAddressCols && $.each(v2.shippAddressCols, function(i3, v3) {
                                const _addr = (v3.province || '') + (v3.city || '') + (v3.district || '') + (v3.detail || '');
                                v3.id = encodeURIComponent((data.ww || '') + _addr);
                                // netPointHtml += '<div class="dp_adres_box"><p class="netPointAddress yzAddr" data-act-name="choose-default">'
                                //                 + '<input class="input_radio input_yzSet" type="radio" name="input_yzSetName" value="' + v3.id + '"  data-branchcode=' + (v2.branchCode || '') + ' ><span>' + _addr + '</span>'
                                //                 + '</p></div>';
                                netPointHtml
                                    += `<div class="dp_adres_box">
                                        <p class="netPointAddress yzAddr" data-act-name="choose-default" >
                                        <input class="input_radio input_yzSet" type="radio" name="input_yzSetName" value="${v3.id}"
                                            data-user-idx="${data._idx}"
                                            data-branch-idx="${i1}"
                                            data-account-idx="${i2}"
                                            data-address-idx="${i3}"
                                            data-segmentcode="${v2.segmentCode || ''}"
                                            data-branchcode="${(v2.branchCode || '')}"
                                            data-shared="${!!data.shared}"
                                            data-shareid="${data.shareId || ''}"
                                            data-ownerid="${data.ownerId || ''}"
                                            data-shareduserId="${data.sharedUserId || ''}"
                                            data-isrelation="${!!data.sharedUserNick}"
                                        ><span> ${_addr}</span>
                                        </p>
                                    </div>`;
                            });
                            netPointHtml += '</div>';
                            netPointHtml += '</div>';
                        });
                        // 拼外
                        v1.branch_account_cols && $.each(v1.branch_account_cols, function(i2, v2) {

                            netPointHtml += '<div class="add_net_set" >';
                            if (parseInt(v1.wp_type) == 1) { // 非直营快递
                                // 其它账号剩余单号展示
                                let accountHtml = '';

                                if (v2.vas_account_cols) {
                                    accountHtml = v2.vas_account_cols.map(function(account) {
                                        return '<br/><span class="f_gray">' + account.account_type_desc + '：可用单号 <i>' + account.quantity + '</i> / 已用单号 <i>' + account.allocated_quantity + '</i></span>';
                                    }).join('');
                                }

                                netPointHtml += '<h4>' + (v2.branch_name || comp.base.getBranchName(v2.branch_code) || v2.branch_code || v1.wp_code) + '(' + (v2.branch_code || v1.wp_code) + ')</h4><span class="f_gray">可用单号 <i>' + v2.quantity + '</i> / 已用单号 <i>' + v2.allocated_quantity + '</i></span>' + accountHtml;
                            } else { // 直营快递 可直接使用
                                netPointHtml += '<h4>' + (v2.branch_name || comp.base.getBranchName(v2.branch_code) || v2.branch_code || v1.wp_code) + '(' + (v2.branch_code || v1.wp_code) + ')</h4><span class="f_gray">【直营快递,可直接使用】</span>';
                            }
                            netPointHtml += '</div>';
                            netPointHtml += '<div>';
							const customerCodeMap = JSON.parse(v2.customerCodeMap || '{}');
                            // 遍历网点地址列表
                            v2.shipp_address_cols && $.each(v2.shipp_address_cols, function(i3, v3) {
                                const _addr = (v3.province || '') + (v3.city || '') + (v3.district || '') + (v3.detail || '');
								const customerCode = customerCodeMap[v3.waybillAddressId];
                                v3.id = encodeURIComponent((data.ww || '') + _addr);
                                // netPointHtml += '<div class="dp_adres_box"><p class="netPointAddress yzAddr" data-act-name="choose-default">'
                                //            + '<input class="input_radio input_yzSet" type="radio" name="input_yzSetName" value="' + v3.id + '"  data-branchcode=' + (v2.branch_code || '') + ' ><span>' + _addr + '</span>'
                                //            + '</p></div>';
                                netPointHtml
                                    += `<div class="dp_adres_box">
                                        <p class="netPointAddress yzAddr" data-act-name="choose-default" >
                                        <input class="input_radio input_yzSet" type="radio" name="input_yzSetName" value="${v3.id}"
                                            data-segmentcode="${v2.segmentCode || ''}"
                                            data-branchcode="${(v2.branch_code || '')}"
                                            data-shared="${!!data.shared}"
                                            data-shareid="${data.shareId || ''}"
                                            data-ownerid="${data.ownerId || ''}"
                                            data-shareduserId="${data.sharedUserId || ''}"
                                            data-isrelation="${!!data.sharedUserNick}"
                                            data-customerCode="${ customerCode }"
                                        ><span> ${_addr}</span>
                                        </p>
                                    </div>`;
                            });
                            netPointHtml += '</div>';
                            netPointHtml += '</div>';
                        });

                    });
                }
            } else if (resultCode == 101) { // 授权失效
                let msg_tip; let
msg_link;
                if (isPw) {
                    msg_link = '登录拼多多账号授权快递助手使用电子面单';
                    netPointHtml
                        += '<p class="pad_l0">'
                        + '<a target="_blank" href="' + _href + '" class="cursor_p info-msg pwAuthorizeLink">' + msg_link + '</a></p>'
                        + '<i class="tip_hand"></i>'
                        + '</div>';
                } else if (isDY | isKs) {
                    netPointHtml += '<p class="pad_l0">'
                        + '网点信息异常，请稍后重试</p>'
                        + '<i class="tip_hand"></i>'
                        + '</div>';
                } else if (!isERP) { // erp不需要以下提示
                    msg_link = '登录淘宝授权快递助手使用菜鸟物流';
                    msg_tip = '如果您已经有淘宝账号，建议使用此选项';
                    netPointHtml += '<div><p class="f_gray pad_l0" >' + msg_tip + '</p>'
                        + '<p class="pad_l0">'
                        + '<a target="_blank" href="' + _href + '" class="cursor_p info-msg">' + msg_link + '</a></p>'
                        + '<i class="tip_hand"></i>'
                        + '</div>';
                }

            } else if (!isPw && resultCode == 102) { // 发货地址未设置
                netPointHtml += '<p class="f_gray outDateShop" data-erro="notHasShipAddr" >当前使用的网点所在店铺授权未设置发货地址，请先在淘宝后台物流宝服务中<a href="javascript:;" style="color:#d75fd2;" target="_blank">设置发货地址</a></p>';
            } else if (resultCode == 104) {
                netPointHtml += raw`
                    <div>
                        <p class="f_gray pad_l0">${data.ErrMsg || ''}</p>
                    </div>
                `;
            } else { // 其他错误
                if (methodType == 2) { // 菜鸟物流云账号代码授权
                    netPointHtml += '<p style="" class="f_gray">' + (data.errMsg || data.message || '当前物流云账号尚未建立电子面单订购关系') + '</p>\
                    <a href="https://dayin.cainiao.com/miandan/subscribe/subscribe.htm" style="display: inline-block;color:#b9cede;" target="_blank">新建订购关系</a>  <a class="refresh_cainiaoyun" href="###" style="display: inline-block;color:#b9cede;">刷新</a><br />';// 刷新按钮用于重新获取菜鸟云物流账号的云栈
                } else {
                    let _href_kaitou = '//wlmart.wuliu.taobao.com/user/service_frame.htm?spm=a1z0f.7.0.0.zteMX4&temp=1&serviceType=28';

                    if (isPw) {
                        methodType == 0
                            ? _href_kaitou = '//mms.pinduoduo.com/login?redirectUrl=https://mms.pinduoduo.com/logistics/home'
                            : _href_kaitou = '//wb.pinduoduo.com/logistics/open-service';
                    }
                    if (isKs) {
                        _href_kaitou = 'https://s.kwaixiaodian.com/zone-origin/express/electronic-sheet';
                    } else if (isXhs || isNewXhs) {
                        _href_kaitou = 'https://ark.xiaohongshu.com/app-order/waybill/apply';
                    } else if (isSph) {
                        _href_kaitou = 'https://channels.weixin.qq.com/shop/waybill/admin';
                    }

                    netPointHtml += '<p style="" class="f_gray">' + (data.ErrMsg || data.errMsg || data.message || '当前店铺尚未开通此快递的电子面单服务') + '</p>\
                    <a href="' + _href_kaitou + '" style="display: inline-block;color:#b9cede;" target="_blank">申请开通</a><br />';
                }


            }

            return netPointHtml + '</div>';
        };

        // 生成抖音电子模板右侧网点部分 HTML
        // todo  快手目前也使用此逻辑
        dom.qlrr.getDYNetPointHtml = function(data) {
            const branches = data.branchList || [];
            let dyHtml = '';

            branches.forEach(branch => {

				dyHtml += raw`<div class="add_net_set" >${generateBranchHtml(branch, data.shared, data)}</div>`;

                // 遍历网点地址列表
                (branch.AddressList || branch.waybillBranchAddressList || []).forEach(address => {
                    const addr = (address.SendProvince || '') + (address.SendCity || '') + (address.SendDistrict || '') + (address.SendAddress || '');
					address.id = encodeURIComponent((data.ww || '') + addr + (address?.BranchCode || '') + (branch?.logisticsAccount || '') + (branch?.paymentType || ''));
                    dyHtml += raw`
                        <div class="dp_adres_box">
                            <p class="netPointAddress yzAddr" data-act-name="choose-default" >
                                <input class="input_radio input_yzSet" type="radio" name="input_yzSetName" value="${address.id}"
                                    data-segmentcode="${branch.segmentCode || ''}"
                                    data-branchcode="${(branch.BranchCode || '')}"
                                    data-shared="${!!data.shared}"
                                    data-shareid="${data.shareId || ''}"
                                    data-ownerid="${data.ownerId || ''}"
                                    data-shareduserId="${data.sharedUserId || ''}"
                                    data-isrelation="${!!data.sharedUserNick}"
                                >
                                <span> ${addr}</span>
                            </p>
                        </div>
                    `;
                });

                function generateBranchHtml(branch, shared) {
                    /**
                     * 物流服务商业务类型 1：直营  2：加盟 3：落地配 4：直营带网点
                     **/
                    const companyType = branch.CpType;
					if ((companyType != 1) && (companyType != 4 && branch?.paymentType !== '0') || (shared && (data?.limitType == 1))) { //非直营
                        const quantity = parseInt(branch.Quantity);
                        const allocatedQuantity = parseInt(branch.AllocatedQuantity);
                        const currentCount = !isNaN(quantity)
                            ? raw`<span class="f_gray">可用单号 <i>${(shared && branch.Quantity == '-1') ? '不限量' : branch.Quantity}</i></span>` : '';
                        const totalCount = !isNaN(allocatedQuantity)
                            ? raw`<span class="f_gray">已用单号 <i>${branch.AllocatedQuantity}</i></span>` : '';

                        return raw`
                            <h4>${branch.BranchName || branch.BranchCode || '快递公司'} ${branch.BranchCode || ''}</h4>
                            ${currentCount}/${totalCount}
                        `;
                    }

                    // 视为直营，暂时不考虑 落地配
                    return raw`
                        <h4>${(branch.BranchName || branch.BranchCode || '快递公司')}${branch.BranchCode || ''}</h4>
                        <span class="f_gray">【直营快递,可直接使用】</span>
                        `;
                }
            });

            // 抖音快手
            let kaitong_href = comp.base.isDYTemp() ? '//fuwu.jinritemai.com/detail?service_id=21770&from=gy' : 'https://s.kwaixiaodian.com/zone-origin/express/electronic-sheet';
            const isXhs = comp.base.isXhsTemp();
            const isSphTemp = comp.base.isSphTemp()
			const isNewXhs = comp.base.isNewXhsTemp()
            if(isXhs || isNewXhs){
				kaitong_href = 'https://ark.xiaohongshu.com/app-order/waybill/apply';
            } // 如果 barnches 是空数组，则视为未开通抖音面单，给一个去开通的链接
			if(isSphTemp) kaitong_href = 'https://store.weixin.qq.com/shop/waybill/admin';
            if (!branches.length) {
                dyHtml += raw`
                    <div>
                        <p class="f_gray pad_l0">当前店铺尚未开通此快递的电子面单服务</p>
                        <p class="pad_l0">
                            <a target="_blank" href="${kaitong_href}" class="cursor_p info-msg${comp.Print.Data.platform === 'fxg' ? ' J-clickPoint" data-clickData-points="point=24629.45235.50491.50492.56296.56300&_fm=10196' : ''}">申请开通</a>
                        </p>
                        <i class="tip_hand"></i>
                    </div>
                `;
            }

            return dyHtml;
        };



        // 生成美团电子模板右侧网点部分 HTML
        dom.qlrr.getTHHNetPointHtml = function(data) {
            console.log('生成美团电子模板右侧网点部分 HTML');
            const branches = data.branchList || [];
            let thhHtml = '';

            branches.forEach(branch => {

                thhHtml += raw`<div class="add_net_set" >${generateBranchHtml(branch)}</div>`;

                // 遍历网点地址列表
                (branch.AddressList || []).forEach(address => {
                    const addr = (address.SendProvince || '') + (address.SendCity || '') + (address.SendDistrict || '') + (address.SendAddress || '');
                    address.id = encodeURIComponent((data.ww || '') + addr);

                    thhHtml += raw`
                        <div class="dp_adres_box">
                            <p class="netPointAddress yzAddr" data-act-name="choose-default" >
                                <input class="input_radio input_yzSet" type="radio" name="input_yzSetName" value="${address.id}"
                                    data-segmentcode="${branch.segmentCode || ''}"
                                    data-branchcode="${(branch.BranchCode || '')}"
                                    data-shared="${!!data.shared}"
                                    data-shareid="${data.shareId || ''}"
                                    data-ownerid="${data.ownerId || ''}"
                                    data-shareduserId="${data.sharedUserId || ''}"
                                    data-isrelation="${!!data.sharedUserNick}"
                                >
                                <span> ${addr}</span>
                            </p>
                        </div>
                    `;
                });

                function generateBranchHtml(branch) {
                    /**
                     * 物流服务商业务类型 1：直营  2：加盟 3：落地配 4：直营带网点
                     **/
                    const companyType = branch.CpType;
                    if ((companyType != 1) && (companyType != 4)) { // 非直营
                        const currentCount = typeof branch.Quantity === 'number'
                            ? raw`<span class="f_gray">可用单号 <i>${branch.Quantity}</i></span>` : '';
                        const totalCount = typeof branch.AllocatedQuantity === 'number'
                            ? raw`<span class="f_gray">/已用单号 <i>${branch.AllocatedQuantity}</i></span>` : '';

                        return raw`
                            <h4>${branch.BranchName || branch.BranchCode || '快递公司'} ${branch.BranchCode || ''}</h4>
                            ${currentCount} ${totalCount}
                        `;
                    }

                    // 视为直营，暂时不考虑 落地配
                    return raw`
                        <h4>${(branch.BranchName || branch.BranchCode || '快递公司')}${branch.BranchCode || ''}</h4>
                        <span class="f_gray">【直营快递,可直接使用】</span>
                        `;
                }
            });
            // 美团商家管理后台
            const THH_href = '//mtm.meituan.com/shipping/ele-face-sheet';
            // 如果 barnches 是空数组，则视为未开通抖音面单，给一个去开通的链接
            if (branches.length === 0) {
                thhHtml += raw`
                    <div>
                        <p class="f_gray pad_l0">当前店铺尚未开通此快递的电子面单服务</p>
                        <p class="pad_l0">
                            <a target="_blank" href="${THH_href}" class="cursor_p info-msg${comp.Print.Data.platform === 'fxg' ? ' J-clickPoint" data-clickData-points="point=24629.45235.50491.50492.56296.56300&_fm=10196' : ''}">申请开通</a>
                        </p>
                        <i class="tip_hand"></i>
                    </div>
                `;
            }

            return thhHtml;
        };
        dom.qlrr.loadJDYunZhanlistHtml = function(resultCode, data, modeListShow) {
            let linkCode;
            let netPointHtml = '<div class="add_list_adrs">';
            let kdzsToken = comp.Print.getKdzsToken();
            let origin = new comp.Print().getLocationOrigin();
            let tokenArr = kdzsToken.split('_');
            let enableStatus = data.ShopCode ? data.ShopCode.enableStatus : null; // 关联店铺的代码是否被刷新更改
            let curShopcode = data.ShopCode ? data.ShopCode.shopCode : null; // 被刷新后的关联店铺

            let invalidCodeErro = false;

            scroll = dom.find(".address_scroll");

            if (resultCode == 100 && data.data ) {
                comp.Print.Data.yunzhanSetting = data;
                $.each(data.data, function(i, v) {
                    netPointHtml += '<label data-exuserId="' + v.userId + '" class="yzShopName">' + v.ww + `${v.shared ? '<i class="iconfont  ifont-fenxiang"></i>' : ''}` + '</label>';
                    if (enableStatus == 0 && !v.IsLoginUser && v.shopCode == curShopcode) {
                        netPointHtml += '<div>当前店铺代码已失效，请重新填写店铺代码</div>';
                        return;
                    }
                    if (v.isSuccess && v.branchList && v.branchList.length) { // ql-modify
                            v.branchList && $.each(v.branchList, function(i1, v1) {

                            netPointHtml += '<div class="add_net_box">';
                            netPointHtml += '<div class="add_net_set">';
                            if (parseInt(v1.operationType) != 1) { // 非直营快递
                                netPointHtml += '<h4><span name="branchName">' + (v1.branchName || '') + '</span>(' + v1.branchCode + ')</h4><span class="f_gray">可用单号 <i>' + v1.amount + '</span>';
                            } else { // 直营快递 可直接使用
                                netPointHtml += '<h4>财务结算编码(' + v1.settlementCode + ')</h4><span class="f_gray">【直营快递,可直接使用】</span>';
                            }
                            netPointHtml += '</div>';
                            netPointHtml += '<div>';
                            // 网点地址 JD每个网点只有一个地址 不存在便利
                            let wdAddr = v1.jdAddress;
                            let _addr = wdAddr.address;
                            let _addrID = v.sellerId + ',' + (v1.branchCode || '') + (v1?.jdAddress?.address || '') + ',' + (v1.settlementCode || ''); // jd使用这两个字符串拼接起来作为网点的唯一标识符
                            netPointHtml += '<div class="dp_adres_box"><p class="netPointAddress yzAddr" data-act-name="choose-default">'
                                        + '<input class="input_radio input_yzSet" type="radio" name="input_yzSetName" data-sellerId="' + v.sellerId + '"  data-userId="' + v.userId + '" value="' + _addrID + '"><span>' + _addr + '</span>'
                                        + '</p></div>';
                            netPointHtml += '</div>';
                            netPointHtml += '</div>';
                        });
                    } else { // 未开通
                        netPointHtml += '<p style="" class="f_gray">当前店铺尚未开通此快递的电子面单服务</p>\
                            <a href="//order.shop.jd.com/eps/eps_electronicSurface.action" style="display: inline-block;color:#b9cede;padding-left: 10px;" target="_blank">申请开通</a><br />';
                    }
                });

                // 如果没有关联店铺  提示去添加关联店铺
                // netPointHtml += '<div class="f_gray dzmd_qh_box">\
                //             <!--使用关联店铺电子面单账号-->\
                //             <div style="display:none;" id="is_use_link_shop">\
                //                 <p class="pad_l0">\
                //                 <label class="on"><input type="checkbox" class="input_check" id="useLinkShop" >使用关联店铺电子面单账号(<span class="f_geen_blue">荐</span>)</label>\
                //                 </p>\
                //                 <div class="set_link_dp" style="display:none;">\
                //                     <a href="' + window.location.search.split("#")[0] + '#/sygj/connectStore/ " target="_blank">设置关联店铺</a>\
                //                     <p class="">\
                //                         如果希望多店铺共用一个菜鸟电子面单账号，推荐使用关联店铺；同时可实现一键打印所有店铺订单，效率翻倍!\
                //                     </p>\
                //                 </div>\
                //             </div>\
                //             <!--使用其他店铺电子面单账号-->\
                //             <div>\
                //             <p class="pad_l0"><label><input type="checkbox" class="input_check" id="useOtherShop">使用其他店铺电子面单账号</label></p>\
                //             <div class="use_dpdm_box" style="display:none;">\
                //                 <input type="text" placeholder="输入对方店铺代码" id="otherShopCode"><br>\
                //                 <a href="javascript:void(0)" class="expr_btn_js" id="submitShopCodeBtn">确认</a>';
                // if( invalidCodeErro ){
                //     netPointHtml += '<span style="color: #d75fd2;" class="shopCodeErroInfo">对方店铺代码已失效</span>';
                // }

                // netPointHtml += '<p>店铺代码可在“实用工具-管理关联店铺”中找到，由对方店铺发送给您。</p>\
                //             </div>\
                //             </div>\
                //             </div>\
                //             </div>';
            } else {
                dom.qlrr.loadYunZhanErrorHtml();
            }

            netPointHtml + '</div>';

            scroll.html(netPointHtml);


            // 绑定云栈是否使用关联店铺事件
            if (data.data.length <= 1) {
                $("#is_use_link_shop").show();
            }
            // 加载点击地址事件--保存或者删除用户设置
            $(".input_yzSet").click(function(event) {
				if( comp.Print.chenkAuthor()) return false

                event.stopPropagation();
                if (!addressClickFlag) {
                    return;
                }
                addressClickFlag = false;
                let $this = $(this);
                // 当前地址已启用

                let branchName = $this.closest('.add_net_box').find('.add_net_set [name="branchName"]').text();

                if ($this.next('span').children('em').length > 0) {
                    addressClickFlag = true;
                    return;
                } else {
                    let address = data.address;// 当前用户网点设置
                    if (address != null) {
                        let _addrID = address.sellerId + ',' + (address.branchCode || '') + (address.address || '') + ',' + (address.settlementCode || '');
                        $(".netPointAddress[value='" + _addrID + "']").children('span').children('em').remove();
                    }
                    // $(".netPointAddress[data-id='" + userSetting.AddressId + "']").next('i').remove();
                    // 删除启用的 保存当前点击的
                        let _id = $this.val();
                        let params;
                        $.each(data.data, function(i, v) {
                            let branchList = v.branchList || [];
                            $.each(branchList, function(_i, _v) {
                                if (v.sellerId + ',' + (_v.branchCode || '') + (_v?.jdAddress?.address || '') + ',' + (_v.settlementCode || '') == _id) {
                                    params = {
                                        address: _v.jdAddress.address,
                                        branchCode: _v.branchCode,
                                        cityName: _v.jdAddress.cityName,
                                        countryName: _v.jdAddress.countryName,
                                        countrysideName: _v.jdAddress.countrysideName,
                                        providerCode: _v.providerCode,
                                        providerId: _v.providerId,
                                        providerName: _v.providerName,
                                        providerType: _v.providerType,
                                        provinceName: _v.jdAddress.provinceName,
                                        settlementCode: _v.settlementCode,
                                        templateId: modeListShow.Mode_ListShowId,
                                        branchUserId: v.userId,
                                        sellerId: v.sellerId,
                                        branchName, // 寄件网点，不是选中的网点，注意区分
                                        shared: v.shared,
                                        ownerId: v.ownerId,
                                        sharedUserId: v.sharedUserId,
                                        userId: comp?.Print?.Data?.userId || '',
                                        shareId: v.shareId, };
                                }
                            });
                        });
                        that.saveJDYunZhanSet(params, function() {
                            addressClickFlag = true;
                            let dataParams = {
                                exid: modeListShow.Exid,
                                exCode: modeListShow.ExCode,
                                modeListShowId: modeListShow.Mode_ListShowId,
                            };
                            // that.getJDYunZhanList(dataParams, dom.qlrr.loadJDYunZhan);
                            dom.qlrr.branchChanged();
                        });
                }
            });

            $(".dp_adres_box").click(function() {
                $(this).find(".input_yzSet").trigger("click");
            });


            if (!$.isEmptyObject(data.address) && (data.address || {}).address) {
                $(".netPointAddress").each(function(i, v) {
                    // let sellerId = $(this).find('input.input_yzSet').attr('data-sellerId')
                    if ($(this).find('input.input_yzSet').val() == data.address.sellerId + ',' + (data.address.branchCode || '') + (data.address.address || '') + ',' + (data.address.settlementCode || '')) {
                        $(this).addClass('on').find("input.input_yzSet").next()
.append('<em class="chooseYunZhan">使用中</em>');// .after('<i class="xz_right_icon"></i>'); //<!--选中-->
                        $(this).find("input.input_yzSet").attr("checked", "checked");
                    }
                });
            }
			const _$radio = $(".input_yzSet")
			if ($(".netPointAddress").length > 0 &&  _$radio.filter(':checked').length == 0 ) {
				_$radio[0].click();
                //     var _id = $(".netPointAddress").eq(0).find('input.input_yzSet').val();
                //     var params;
                //     $.each(data.data, function(i, v) {
                //        let branchList =  v.branchList || []
                //         $.each(branchList, function(_i, _v) {
                //             if(v.sellerId + ',' + _v.branchCode + ',' + _v.settlementCode == _id){
                //                 params = {
                //                     address: _v.jdAddress.address,
                //                     branchCode: _v.branchCode,
                //                     cityName: _v.jdAddress.cityName,
                //                     countryName: _v.jdAddress.countryName,
                //                     countrysideName: _v.jdAddress.countrysideName,
                //                     providerCode: _v.providerCode,
                //                     providerId: _v.providerId,
                //                     providerName : _v.providerName,
                //                     providerType : _v.providerType,
                //                     provinceName : _v.jdAddress.provinceName,
                //                     settlementCode  :_v.settlementCode,
                //                     templateId: modeListShow.Mode_ListShowId,
                //                     branchUserId: v.userId,
                //                     sellerId: v.sellerId,
                //                     branchName: _v.branchName, // 寄件网点，不是选中的网点，注意区分
                //                 }
                //             }
                //         });
                //     })
                //     that.saveJDYunZhanSet(params,function(){
                //         addressClickFlag = true;
                //         let dataParams = {
                //             exid:modeListShow.Exid,
                //             exCode:modeListShow.ExCode,
                //             modeListShowId:modeListShow.Mode_ListShowId,
                //         }
                //         that.getJDYunZhanList(dataParams, dom.qlrr.loadJDYunZhan);
                //         dom.qlrr.branchChanged();
                // })
                }
        };
        dom.qlrr.loadYunZhanMethod = function(arg) {
            let yunZhanSettingHtml = '';
                let exid = arg.exid;
                let modeListShowId = arg.modeListShowId;
                let kddType = arg.kddType;
                let $dom;
                let isPW = comp.base.isPWTemp(); // 是拼外电子模版
                let isDY = comp.base.isDYTemp(); let // 是抖音电子模板
                isKs = comp.base.isKsTemp(); let // 快手电子模板
                isTHH = comp.base.isTHHTemp(); let // 是美团电子模版
                isCN = comp.base.isCNTemp(); let //  是菜鸟电子面单
                isXhs = comp.base.isXhsTemp(); let //  是菜鸟电子面单
                isNewXhs = comp.base.isNewXhsTemp(),//  是菜鸟电子面单
				isSph = comp.base.isSphTemp(),//  是视频号电子面单
                isJd = comp.base.isJDTemp(),//  是京东电子面单
                methodType = arg.methodType;
                let noShopLink = '';
            // ERP 优先判断
            if (comp.Print.Data.platform == 'erp') {
                if (kddType > 1 && arg.exCode != 'CNGG') {
                    yunZhanSettingHtml = dom.qlrr.getConcatFhdHtml();
                }
                // if(kddType === 8 ){
                //     noShopLink = `<a style="color:#eb9e26;font-size:11px;text-decoration-line:underline;" href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/bwkc03i04wz24vx4?singleDoc#" target="_blank">无店铺授权教程</a>`
                // }
                yunZhanSettingHtml += `<h2>设置发货网点  ${noShopLink}</h2>`;
                if (isCN) {
                    yunZhanSettingHtml += '<div class="newPointHead"><label style="margin: 0 0 6px 0;display: block;color: #fff;"><input type="radio" name="twcnAuthAccount" wayid="' + YUNZHAN_AUTH_TYPE['ERP_SHOP'] + '" class="input_radio">淘宝店铺授权'
                    + '<a href="#/shops" class="bind_shop link">前往添加店铺</a>'
                    + '</label>'
                    + '<label style="margin: 0 0 6px 0;display: block;color: #fff;"><input type="radio" name="twcnAuthAccount" wayid="' + YUNZHAN_AUTH_TYPE['ACCOUNT'] + '" class="input_radio"/><span>无淘宝店铺授权</span>'
                    + '<a class="link" target="_blank"  href="' + comp.Print.getShouQuanUrl(kddType) + '">新增账号授权</a></label>'
                    + '<p><input type="button" id="refreshAuthorizeRet" value="授权后点我刷新" class="btn_orange_jd"></p></div>';
                } else if (isPW) {
                    yunZhanSettingHtml += '<div class="newPointHead"><label style="margin: 0 0 6px 0;display: block;color: #fff;"><input type="radio" name="twcnAuthAccount" wayid="' + YUNZHAN_AUTH_TYPE['ERP_SHOP'] + '" class="input_radio">拼多多店铺授权'
                    + '<a href="#/shops" class="bind_shop link">前往添加店铺</a>'
                    + '</label>'
                    + '<label style="margin: 0 0 6px 0;display: block;color: #fff;"><input type="radio" name="twcnAuthAccount" wayid="' + YUNZHAN_AUTH_TYPE['ACCOUNT'] + '" class="input_radio">无拼多多店铺授权'
                    + '<a class="link" target="_blank"  href="' + comp.Print.getShouQuanUrl(kddType) + '">新增账号授权</a></label>'
                    + '<p><input type="button" id="refreshAuthorizeRet" value="授权后点我刷新" class="btn_orange_jd"></p></div>';

                }else if(isDY||isKs||isXhs||isNewXhs || isSph || isJd){
                    yunZhanSettingHtml += '<div class="newPointHead"><label style="margin: 0 0 6px 0;display: block;color: #fff;"><input type="radio" name="twcnAuthAccount" wayid="' + YUNZHAN_AUTH_TYPE['ERP_SHOP'] + '" class="input_radio">店铺网点授权'
                    + '<a href="#/shops" class="bind_shop link">前往添加店铺</a>'
                    + '</label>'
                    + '<label style="margin: 0 0 6px 0;display: block;color: #fff;"><input type="radio" name="twcnAuthAccount" wayid="' + YUNZHAN_AUTH_TYPE['ERP_KDD_SHOP'] + '" class="input_radio">面单店铺授权'
                    + '<a class="bind_shop link" href="#/shops?shopType=1">新增面单店铺</a></label>'
                    + '<p><input type="button" id="refreshAuthorizeRet" value="授权后点我刷新" class="btn_orange_jd"></p></div>';

                } yunZhanSettingHtml += '<div class="address_scroll" style=""></div>';
            } else if (isDY) {

                const isDyOut = ['nzw'].includes(comp.Print.Data.platform);
                const dYoutHtml = isDyOut ? `
                <label style="margin-bottom: 6px;display: block;">
                <input
                    type="radio"
                    name="twcnAuthAccount"
                    wayid="0"
                    class="input_radio"
                />
                使用抖音店铺代码
                <span class="info-msg">(需订购抖音快递助手)</span>
            </label>
            <p>
                <input
                    type="button"
                    id="refreshAuthorizeRet"
                    value="授权后点我刷新"
                    class="btn_orange_jd"
                />
            </p> ` : '';
                // TODO
                yunZhanSettingHtml += dom.qlrr.getConcatFhdHtml();
                yunZhanSettingHtml += raw`
                    <h2>设置发货网点</h2>
                    <div style="color:#fff">
                       <!-- <label style="margin-bottom: 6px;display: block;">
                            <input
                                type="radio"
                                name="twcnAuthAccount"
                                wayid="1"
                                class="input_radio"
                            />
                            使用抖音电子面单账号授权
                            <a
                                target="_blank"
                                href="${comp.Print.getShouQuanUrl(kddType)}"
                            >
                                新增店铺授权
                            </a>
                        </label> -->
                       ${dYoutHtml}
                    </div>
                    <div class="address_scroll"></div>
                `;
            } else if (isKs) {
                yunZhanSettingHtml = raw`
                    <h2>设置发货网点</h2>
                    <div style="color:#fff">
                        <!-- <label style="margin-bottom: 6px;display: block;">
                            <input
                                type="radio"
                                name="twcnAuthAccount"
                                wayid="1"
                                class="input_radio"
                            />
                            使用快手电子面单账号授权
                            <a
                                target="_blank"
                                href="${comp.Print.getShouQuanUrl(kddType)}"
                            >
                                新增店铺授权
                            </a>
                        </label>
                        <label style="margin-bottom: 6px;display: block;">
                            <input
                                type="radio"
                                name="twcnAuthAccount"
                                wayid="0"
                                class="input_radio"
                            />
                            使用快手店铺代码
                            <span class="info-msg">(需订购抖音快递助手)</span>
                        </label>
                        <p>
                            <input
                                type="button"
                                id="refreshAuthorizeRet"
                                value="授权后点我刷新"
                                class="btn_orange_jd"
                            />
                        </p> -->
                    </div>
                    <div class="address_scroll"></div>
                `;
            } else if (isTHH) {
                yunZhanSettingHtml = raw`
                <h2>设置发货网点</h2>
                <div style="color:#fff">
                <!-- <label style="margin-bottom: 6px;display: block;">
                    <input type="radio" name="twcnAuthAccount" wayid="1" class="input_radio"/>
                    使用美团电子面单账号授权
                    <a target="_blank" href="${comp.Print.getShouQuanUrl(kddType)}">
                        新增店铺授权
                    </a>
                </label>
                <label style="margin-bottom: 6px;display: block;">
                    <input type="radio" name="twcnAuthAccount" wayid="0" class="input_radio"/>
                    使用抖音店铺代码
                    <span class="info-msg">(需订购抖音快递助手)</span>
                </label>
                <p>
                    <input type="button" id="refreshAuthorizeRet" value="授权后点我刷新" class="btn_orange_jd"/>
                </p> -->
            </div>
            <div class="address_scroll"></div>
            `;
            } else if (isPW) {
                yunZhanSettingHtml = '<h2>设置发货网点</h2>'
                    + '<div style="color:#fff">'
                    + '<label style="margin-bottom: 6px;display: block;"><input type="radio" name="twcnAuthAccount" wayid="1" class="input_radio">使用拼多多电子面单账号授权'
                    + '<a target="_blank" href="' + comp.Print.getShouQuanUrl(kddType) + '">新增店铺授权</a>'
                    + '</label>'
                    + '<label style="margin-bottom: 6px;display: block;"><input type="radio" name="twcnAuthAccount" wayid="0" class="input_radio">使用拼多多店铺代码<font class="info-msg">(需订购拼多多快递助手)</font></label>'
                    + '<p><input type="button" id="refreshAuthorizeRet" value="授权后点我刷新" class="btn_orange_jd"></p>'
                    + '</div>'
                    + '<div class="address_scroll" style=""></div>';
            } else {
                if (kddType > 1 && arg.exCode != 'CNGG') {
                    yunZhanSettingHtml = dom.qlrr.getConcatFhdHtml();
                }
                yunZhanSettingHtml += '<h2>设置发货网点</h2>'
                    + '<div style="color:#fff">'
                    + '<label style="margin-bottom: 6px;display: block;"><input type="radio" name="twcnAuthAccount" wayid="1" class="input_radio">使用淘宝账号授权（推荐）'
                    + '<a target="_blank" href="' + comp.Print.getShouQuanUrl(kddType) + '">新增店铺授权</a>'
                    + '</label>'
                    + '<label><input type="radio" name="twcnAuthAccount" wayid="0" class="input_radio">使用店铺代码<font class="info-msg">(需订购淘宝快递助手)</font></label>'
                    + '<p><input type="button" id="refreshAuthorizeRet" value="授权后点我刷新" class="btn_orange_jd"></p>'
                    + '</div>'
                    + '<div class="address_scroll" style=""></div>';
            }
            $dom = domData.netPointDiv.html(yunZhanSettingHtml);
            if (comp.Print.Data.platform == 'erp') {
                $dom.find('.bind_shop').click(function() {
                    dom.qlrr.close();
                    window.location.hash = '#/shops';
                });
            }
            $dom.find('[name=twcnAuthAccount][wayid="' + methodType + '"]').prop('checked', true);
            // methodType :1 淘宝账号授权 0 店铺代码授权
            $dom.find('#refreshAuthorizeRet')[methodType == '1' ? 'show' : 'hide']();

            // 更改获取云栈方式
            $dom.find('[name=twcnAuthAccount]').off('click').click(function() {
				if( comp.Print.chenkAuthor()) return false
                const wayid = $(this).attr('wayid');
                // isMoreStore && $dom.find('#refreshAuthorizeRet')[wayid == '1' ? 'show' : 'hide']();
                $dom.find('#refreshAuthorizeRet')[wayid == '1' ? 'show' : 'hide']();
                $dom.find('.address_scroll').html('数据加载中……');
                that.setYunZhanMethod({
                    modeListShowId,
					exid:exid,
                    exId:exid,
					expressType:kddType,
					exCode:arg.exCode,                    method: wayid,
                }, function() {
                    arg.methodType = wayid;
                    that.getYunZhanList(arg, function(json) {
                        dom.qlrr.loadYunZhan(json, arg);
                    });
                });
            });

            const _isConcatFhd = domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd;
            const styleId = comp.Print.Data.currentTempInfo.ModeList.StyleId;
            const isYilianTemp = comp.base.isYilianTemp(styleId);
            if (isYilianTemp) {
                if (_isConcatFhd == 1 && !comp.Print.Data.hasNoRM_yilian) {
                    $('.concatFhd').prop('checked', true);
                    $('.concatFhdTips').show();
                } else {
                    $('.concatFhd').removeAttr('checked');
                    $('.concatFhdTips').hide();
                }
            } else if (_isConcatFhd == 1 && !comp.Print.Data.hasNoRM_common) {
                    $('.concatFhd').prop('checked', true);
                    $('.concatFhdTips').show();
                } else {
                    $('.concatFhd').removeAttr('checked');
                    $('.concatFhdTips').hide();
                }

        };

        dom.qlrr.loadYunZhan = function(json, arg) {
            const isPW = comp.base.isPWTemp();// 是拼外电子模版
            const isDY = comp.base.isDYTemp();// 是抖音电子模版
            const isKs = comp.base.isKsTemp();// 是抖音电子模版
            const isTHH = comp.base.isTHHTemp();// 是美团电子模版
            const isXhs = comp.base.isXhsTemp();// 是美团电子模版
            const isNewXhs = comp.base.isNewXhsTemp();// 是美团电子模版
            const isSph = comp.base.isSphTemp();// 是美团电子模版
            const isYz = comp.base.isYzTemp();// 是美团电子模版

            if (isDY) {
                const isDyOut = ['nzw'].includes(comp.Print.Data.platform);
                isDyOut ? dom.qlrr.loadYunZhanTwOld(json, arg) : dom.qlrr.loadYunZhanDY(json, arg);
            } else if (isKs) {
                dom.qlrr.loadYunZhanKs(json, arg);
            } else if (isTHH) {
                dom.qlrr.loadYunZhanTHH(json, arg);
            } else if (isPW) {
                dom.qlrr.loadYunZhanPdd(json, arg);
            } else if (isXhs || isSph || isYz || isNewXhs) {
                dom.qlrr.loadYunZhanKs(json, arg);
            } else {
                dom.qlrr.loadYunZhanTwOld(json, arg);
            }
        };


        // 加载云栈设置html
        dom.qlrr.loadYunZhanTwOld = function(json, arg) {
            let $dom = domData.netPointDiv;
                let _$net;
                let errorCode;
                let _defAddr;
                let _html = '';
                let methodType = arg.methodType;
                let exCode = arg.exCode;
                let templateId = arg.modeListShowId;
                let exid = arg.exid;
                let _data = json.data;
                let noMatchHtml;

            if (json.result != 100 && json.result != 101) {
                dom.qlrr.loadYunZhanErrorHtml($dom.find('.address_scroll'), json.message);
                return;
            }

            if (_data.data && _data.data.length && json.result == 100) {
                comp.Print.Data.segmentCodeNameMap = _data.segmentCodeNameMap;
                comp.Print.Data.yunzhanSetting = _data.data;
                $.each(_data.data, function(_i, _v) {
                    _v._idx = _i;
                    if (!_v.isSuccess) {
                        if (_v.errMsg.indexOf('发货地址失败') > -1) {
                            errorCode = 102;
                        } else {
                            errorCode = 103;
                        }
                    } else {
                        errorCode = 100;
                    }
                    _html += dom.qlrr.loadYunZhanlistHtml(errorCode, _v, methodType, _data.accountKey);
                });
            } else {
                _html += dom.qlrr.loadYunZhanlistHtml(json.result, _data, methodType, _data.accountKey);
            }
            $dom.find('.address_scroll').html(_html);

            // 隐去不需要的店铺代码输入框
            $dom.find('.dzmd_qh_box').addClass('hidden');
            // 显示第一个输入框
            $dom.find('.dzmd_qh_box').first().removeClass('hidden');

            // 授权
            $dom.find('#refreshAuthorizeRet').off('click').click(function() {
                const $error = $(this).next('.error-msg');
                const _$load = $dom.find('.address_scroll').html('数据加载中……');

                that.refreshAuthorization(arg, function(json) {
                    if (json.data && json.data.length > 0) { // 已授权一个或以上的店铺
                        if (PLAT == 'pdd') {
                            Tatami.controls.get('control.getLoginUser').getMethod('getUserInfo', function(data) { // 获取当前是标准版还是高级版
                                const level = data.level;
                                if (level != 5) { // 非高级版
                                    $('body').on('click', '#authroizedButton', function(e) {
                                        e.preventDefault();
                                        Tatami.pub('getUpgradeDialog', JSON.parse(window.sessionStorage.getItem('com.userInfo')), 'print');
                                        $('.upgradeFrame').css('z-index', 1002); // 把订购高级版的弹框放在快递单设置弹框的前面
                                    });
                                }
                            });
                        }
                    }
                    if (json.result != 100 || !json.data) {
                        const sqHtml = dom.qlrr.getShouquanHtml();
                        _$load.html(`<p class="error-msg info-msg"> ${json.message || '授权关系无效，重新进入授权处理！'} </p>${sqHtml}`);
                        return;
                    }
                    arg.methodType = 1;
                    that.getYunZhanList(arg, function(json) {
                        dom.qlrr.loadYunZhan(json, arg);
                    });
                });
            });


            // 删除账号
            $dom.find('[data-act-name="deleteAccount"]').off('click').click(function() {
                const _$target = $(this);
                that.deleteAccount({
                    exid,
                    taobaoNick: $(this).attr('data-ww'),
                }, function() {
                    dom.qlrr.branchChanged(arg.exCode);
                    _$target.closest('.add_list_adrs').remove();
                    if (!$dom.find('.add_list_adrs').length) {
                        $dom.find('.address_scroll').html(dom.qlrr.loadYunZhan({ result: 101, data: {} }, arg));
                    }
                });
            });

            // 保存默认网点
            $dom.find('[data-act-name="choose-default"]').off('click').click(async function(e) {
				if( comp.Print.chenkAuthor()) return false
                e?.preventDefault();

                let yunZhan;
                    let params = null;
                    let $this = $(this).find('.input_yzSet');
                    let _id;
                    let shared;
                    let shareId;
                    let sharedUserId;
                if (!addressClickFlag) {
                    return;
                }
                await updateAddResswarning($this, $dom);

                addressClickFlag = false;
                // 当前地址已启用
                if ($this.next('span').children('em').length > 0) {
                    addressClickFlag = true;
                    return;
                }
                yunZhan = comp.Print.Data.yunzhanSetting;
                _id = $this.val();
                shared = $this.data('shared');
                sharedUserId = $this.data('shareduserid');
                shareId = $this.data('shareid');

                if (yunZhan) {
                    const userIdx = $this.data('user-idx');
                    const branchIdx = $this.data('branch-idx');
                    const accountIdx = $this.data('account-idx');
                    const addressIdx = $this.data('address-idx');

                    const user = yunZhan[userIdx] || {};
                    const branchList = user.branchList || [];

                    const branch = branchList[branchIdx] || {};
                    const branchAccountCols = branch.branchAccountCols || [];

                    const account = branchAccountCols[accountIdx] || {};
                    const shippAddressCols = account.shippAddressCols || [];

                    const address = shippAddressCols[addressIdx];
					const customerCodeMap = JSON.parse(account.customerCodeMap || '{}');

                    if (address) {
                        params = {
                            ww: user.ww,
                            shared: user.shared,
                            isRelation: !!user.sharedUserNick,
                            sharedUserId: user.sharedUserId,
                            ownerId: user.ownerId,
                            shareId: user.shareId,

                            exCode: branch.cpCode,

                            segmentCode: account.segmentCode,
                            branchCode: account.branchCode,

                            province: address.province,
                            city: address.city,
                            area: address.district,
                            town: address.town,
                            addressDetail: address.detail,

                            wayid: '',
                            exid,
                            templateId: arg.modeListShowId,
							logisticsAccount: customerCodeMap[address.waybillAddressId],
                        };
                    }
                    if (comp.Print.Data.platform == 'erp') {
                        params = {
                            ...params,
                            sellerId: user.sellerId,
                        };
                    }
                }

                // 保存默认
                if (!params) {
                    addressClickFlag = true;
                } else {
                    params.method = methodType;
                    params.kddType = arg.kddType;
                    that.saveYunZhanSet(params, function() {
                        addressClickFlag = true;
                        dom.qlrr.chooseYunZhan($this, $dom);
                        noMatchHtml = dom.qlrr.checkNetService($this.closest('.net_point').attr('data-service'));
                        noMatchHtml && $this.closest('.netPointAddress').append(noMatchHtml);
                        dom.qlrr.branchChanged(arg.exCode);
                    });
                }
            });

            // 提交店铺代码确认事件
            $dom.find('#compSubmitShopCodeBtn').off('click').click(function() {
                const $code = $('#otherShopCode');
                let shopCode = $code.val();// 店铺代码
                const realShopCode = dom.qlrr.data.shopCode;

                if (shopCode == '') {
                    alert('店铺代码不能为空');
                    return;
                }
                if (shopCode == $code.attr('data-shopcode')) {
                    return;
                }

                if (shopCode.indexOf('*') > -1) { // 有 * 号
                    alert('店铺代码不含 *，请重新输入');
                    return;
                }

                shopCode = shopCode.indexOf('*') > -1 ? realShopCode : shopCode;// 如果表单中 value 不包含 * 则用表单里的，否则用存在 dom.qlrr.data 里的

                arg.methodType = 0;
                that.saveLinkCode({
                    exid,
                    linkCode: shopCode,

                }, function() {
                    that.getYunZhanList(arg, function(json) {
                        dom.qlrr.loadYunZhan(json, arg);
                    });
                });
            });

            // 没有默认网点保存第一个网点为默认网点
            if (_data) {
                if (_data.address && !$.isEmptyObject(_data.address)) {
                    _defAddr = _data.address;
                    _defAddr.id = encodeURIComponent((_defAddr.ww || '') + (_defAddr.f_s || '') + (_defAddr.f_c || '') + (_defAddr.f_q || '') + (_defAddr.f_addr || ''));
                    _$net = $dom.find('.input_yzSet[value="' + _defAddr.id + '"]');
                    // const $preparatoryBranch = _$net.first(); //保底的 branch

                    if (_defAddr.branchCode) { // 加一层branchCode过滤
                        // _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']:eq(0)');
                        _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']');
                    }

                    // 号段过滤
                    if (_defAddr.segmentCode) {
                        _$net = _$net.filter(`[data-segmentcode=${_defAddr.segmentCode}]`);
                    }

                    // shared 过滤
                    _$net = _$net.filter(`[data-shared=${!!_defAddr.shared}]`);
                    // shareId 过滤
                    if (_defAddr.shareId) {
                        _$net = _$net.filter(`[data-shareid=${_defAddr.shareId}]`);
                    }
                    // sharedUserId 过滤
                    if (_defAddr.sharedUserId) {
                        _$net = _$net.filter(`[data-shareduserid=${_defAddr.sharedUserId}]`);
                    }

                    // 如果筛选完后，没有符合条件的，则使用保底的 branch
                    // if( _$net.length == 0){
                    //     _$net = $preparatoryBranch
                    // }

                    // if (!_$net.get(0)) {
                    //     _$net = getDefaultAddress();
                    //     _$net.click();
                    // }

                    // dom.qlrr.chooseYunZhan( _$net, $dom );
                    dom.qlrr.chooseYunZhan(_$net.first(), $dom);

                    noMatchHtml = dom.qlrr.checkNetService(_$net.closest('.net_point').attr('data-service'));
                    noMatchHtml && _$net.closest('.netPointAddress').append(noMatchHtml);
                }
                const _$radio = $dom.find('.input_yzSet');
                if (_$radio.length && _$radio.filter(':checked').length == 0) {
                    $dom.find('[data-act-name="choose-default"]:eq(0)')[0].click();
                }

            }

            function getDefaultAddress() {
                let defaultAddress = null;
                try {
                    let serviceCodeList;
                    const ModeLogisticsItems = comp.Print.Data.currentTempInfo.ModeLogisticsItems || [];
                    // 新逻辑：当有serviceProperty字段时，会优先匹配对应号段的网点
                    serviceCodeList = ModeLogisticsItems.reduce((acc, { serviceProperty }) => {
                        serviceProperty && acc.push(JSON.parse(serviceProperty).segmentCode);
                        return acc;
                    }, []);
                    // 当找不到对应的serviceProperty时，走原有的逻辑
                    if (serviceCodeList.length === 0) {
                        // 原来的取第一个服务的旧逻辑
                        serviceCodeList = ModeLogisticsItems.map(item => item.serviceCode); // 本模板拥有的号段服务
                    }

                    // 取第一个服务

                    for (const thisTempserviceCode of serviceCodeList) {
                        const $thisDefaultAddress = $(`[data-code="${thisTempserviceCode}"]`).first().closest('.add_net_box').find('.netPointAddress')
.first();
                        if ($thisDefaultAddress.length > 0) {
                            defaultAddress = $thisDefaultAddress;
                            break;
                        }
                    }
                } catch (e) {
                    console.error('getDefaultAddress 错误', e);
                }

                // 如果上面一系列选择器没获取到想要的，则需要一个保底的选项：第一个网点地址
                const theFirstNetPointAddress = $($('.netPointAddress')[0]); // 第一个网点是保底选项

                if (!defaultAddress || defaultAddress.length == 0) {
                    defaultAddress = theFirstNetPointAddress;
                }

                return defaultAddress;
            }
        };

        // 拼多多 && 多账号淘宝菜鸟授权
        dom.qlrr.loadYunZhanPdd = function(json, arg) {
            let $dom = domData.netPointDiv;
                let _$net;
                let errorCode;
                let _defAddr;
                let _html = '';
                let methodType = arg.methodType;
                let exid = arg.exid;
                let _data = json.data;
                let noMatchHtml;

            if (json.result != 100 && json.result != 101) {
                dom.qlrr.loadYunZhanErrorHtml($dom.find('.address_scroll'), json.message);
                return;
            }


            if (_data.data && json.result == 100) {
                comp.Print.Data.segmentCodeNameMap = _data.segmentCodeNameMap;
                comp.Print.Data.yunzhanSetting = _data.data;
                $.each(_data.data, function(_i, _v) {
                    _v._idx = _i;
                    if (!_v.isSuccess) {
                        if (_v.errMsg.indexOf('授权过期') > -1) {
                            errorCode = 101;
                        } else if (_v.errMsg.indexOf('发货地址失败') > -1) {
                            errorCode = 102;
                        } else {
                            errorCode = 103;
                        }
                    } else {
                        errorCode = 100;
                    }
                    _html += dom.qlrr.loadYunZhanlistHtml(errorCode, _v, methodType, _data.accountKey, (_data.address || {}).ww);
                });
            } else {
                _html += dom.qlrr.loadYunZhanlistHtml(json.result, _data, methodType, _data.accountKey, (_data.address || {}).ww);
            }

            $dom.find('.address_scroll').html(_html);

            // 隐去不需要的店铺代码输入框
            $dom.find('.dzmd_qh_box').addClass('hidden');
            // 显示第一个输入框
            $dom.find('.dzmd_qh_box').first().removeClass('hidden');

            // 授权
            $dom.find('#refreshAuthorizeRet').off('click').click(function() {
                const _$load = $dom.find('.address_scroll').html('数据加载中……');

                that.refreshAuthorization(arg, function(json) {
                    if (json.result != 100 || !json.data) {
                        const sqHtml = dom.qlrr.getShouquanHtml();
                        _$load.html(`<p class="error-msg info-msg"> ${json.message || '授权关系无效，重新进入授权处理！'} </p>${sqHtml}`);
                        return;
                    }

                    arg.methodType = $dom.find('[name="twcnAuthAccount"]:checked').attr('wayid');

                    that.getYunZhanList(arg, function(json) {
                        dom.qlrr.loadYunZhan(json, arg);
                    });
                });
            });


            // 删除账号
            $dom.find('[data-act-name="deleteAccount"]').off('click').click(function() {
                const _$target = $(this);
                that.deleteAccount({
                    exid,
                    shopName: $(this).attr('data-ww'),
                    taobaoNick: $(this).attr('data-ww'),
                    kddType: arg.kddType,
                }, function() {
                    _$target.closest('.add_list_adrs').remove();
                    if (!$dom.find('.add_list_adrs').length) {
                        $dom.find('.address_scroll').html(dom.qlrr.loadYunZhanPdd({ result: 101, data: {} }, arg));
                    }
                });
            });


            // 保存默认网点
            $dom.find('[data-act-name="choose-default"]').off('click').click(async function() {
				if( comp.Print.chenkAuthor()) return false
                let yunZhan;
                    let params = null;
                    let $this = $(this).find('.input_yzSet');
                    let _id;
                    let noMatchHtml;
                    let shared;
                    let shareId;
                    let sharedUserId;
                if (!addressClickFlag) {
                    return;
                }
                await updateAddResswarning($this, $dom);

                addressClickFlag = false;
                // 当前地址已启用
                if ($this.next('span').children('em').length > 0) {
                    addressClickFlag = true;
                    return;
                }
                yunZhan = comp.Print.Data.yunzhanSetting;
                _id = $this.val();

                shared = $this.data('shared');
                sharedUserId = $this.data('shareduserid');
                shareId = $this.data('shareid');

                yunZhan && $.each(yunZhan, function(index, _d) {
                    _d.branchList && $.each(_d.branchList, function(_i, _v) {
                        // 拼外
                        _v.branch_account_cols && $.each(_v.branch_account_cols, function(_i1, _v1) {
                            _v1.shipp_address_cols && $.each(_v1.shipp_address_cols, function(_i2, _v2) {
                                // 判断是否为分享单号（兼容处理）
                                const judgeIsShared = (shared == !!_d.shared) && (sharedUserId == (_d.sharedUserId || ''));
                                if (_v2.id == _id && judgeIsShared) {
                                    params = {
                                        province: _v2.province,
                                        city: _v2.city,
                                        area: _v2.district,
                                        town: _v2.town,
                                        addressDetail: _v2.detail,
                                        branchCode: _v1.branch_code,
                                        exCode: _v.cpCode,
                                        wayid: '',
                                        ww: _d.ww,
                                        exid,
                                        templateId: arg.modeListShowId,
                                        segmentCode: _v1.segmentCode,
                                        shared: _d.shared,
                                        isRelation: !!_d.sharedUserNick,
                                        sharedUserId: _d.sharedUserId,
                                        ownerId: _d.ownerId,
                                        shareId: _d.shareId,
                                        sellerId: _d.sellerId,
                                    };
                                    return false;
                                }
                            });
                        });

                        if (comp.Print.Data.platform == 'erp') {
                            _v.branchAccountCols && $.each(_v.branchAccountCols, function(_i1, _v1) {
                                _v1.shippAddressCols && $.each(_v1.shippAddressCols, function(_i2, _v2) {
                                    // 判断是否为分享单号（兼容处理）
                                    const judgeIsShared = (shared == !!_d.shared) && (sharedUserId == (_d.sharedUserId || ''));
                                    if (_v2.id == _id && judgeIsShared) {
                                        params = {
                                            province: _v2.province,
                                            city: _v2.city,
                                            area: _v2.district,
                                            town: _v2.town,
                                            addressDetail: _v2.detail,
                                            branchCode: _v1.branchCode,
                                            branchName: _v1.branchName,
                                            exCode: _v.wpCode,
                                            ww: _d.ww,
                                            exid,
                                            templateId: arg.modeListShowId,
                                            segmentCode: _v1.segmentCode,
                                            shared: _d.shared,
                                            isRelation: !!_d.sharedUserNick,
                                            sharedUserId: _d.sharedUserId,
                                            ownerId: _d.ownerId,
                                            shareId: _d.shareId,
                                            sellerId: _d.sellerId,
                                            platform: _d.platform,
                                        };
                                        return false;
                                    }
                                });
                            });
                        }
                    });
                });
                // 保存默认
                if (!params) {
                    addressClickFlag = true;
                } else {
                    params.method = methodType;
                    params.kddType = arg.kddType;
                    that.saveYunZhanSet(params, function() {
                        addressClickFlag = true;
                        dom.qlrr.chooseYunZhan($this, $dom);
                        if (params.method != 2) { // 物流云授权无需校验服务是否匹配
                            noMatchHtml = dom.qlrr.checkNetService($this.closest('.net_point').attr('data-service'));
                            noMatchHtml && $this.closest('.netPointAddress').append(noMatchHtml);
                        }
                        dom.qlrr.branchChanged();
                    });
                }
            });

            // 提交店铺代码确认事件
            $dom.find('#compSubmitShopCodeBtn').off('click').click(function() {
                const $code = $('#otherShopCode');
                const shopCode = $code.val().trim();// 店铺代码
                if (shopCode == '') {
                    alert($('#otherShopCode').data('content') + ' 不能为空');
                    return;
                }
                if (shopCode == $code.attr('data-shopcode')) {
                    return;
                }
                $code.attr('data-shopcode', shopCode);
                arg.methodType = $dom.find('[name="twcnAuthAccount"]:checked').attr('wayid');
                that.saveLinkCode({
                    exid,
                    linkCode: shopCode,
                    method: arg.methodType,
                }, function() {
                    that.getYunZhanList(arg, function(json) {
                        dom.qlrr.loadYunZhanPdd(json, arg);
                    });
                });
            });

            // 没有默认网点保存第一个网点为默认网点
            if (_data) {
                if (_data.address && !$.isEmptyObject(_data.address)) {
                    _defAddr = _data.address;
                    const ww = _data.address.ww;
                    _defAddr.id = encodeURIComponent((_defAddr.ww || ww || '') + (_defAddr.f_s || '') + (_defAddr.f_c || '') + (_defAddr.f_q || '') + (_defAddr.f_addr || ''));
                    _$net = $dom.find('.input_yzSet[value="' + _defAddr.id + '"]');
                    if (_defAddr.branchCode) { // 加一层branchCode过滤
                        // _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']:eq(0)');
                        _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']');
                    }

                    // const $preparatoryBranch = _$net.first(); //保底的 branch

                    // 号段过滤
                    if (_defAddr.segmentCode) {
                        _$net = _$net.filter(`[data-segmentcode=${_defAddr.segmentCode}]`);
                    }

                    // shared 过滤
                    _$net = _$net.filter(`[data-shared=${!!_defAddr.shared}]`);
                    // shareId 过滤
                    if (_defAddr.shareId) {
                        _$net = _$net.filter(`[data-shareid=${_defAddr.shareId}]`);
                    }
                    // sharedUserId 过滤
                    if (_defAddr.sharedUserId) {
                        _$net = _$net.filter(`[data-shareduserid=${_defAddr.sharedUserId}]`);
                    }

                    // 如果筛选完后，没有符合条件的，则使用保底的 branch
                    // if( _$net.length == 0){
                    //     _$net = $preparatoryBranch
                    // }

                    dom.qlrr.chooseYunZhan(_$net.first(), $dom);

                    // dom.qlrr.chooseYunZhan( _$net, $dom );
                    if (methodType != 2) { // 物流云授权无需校验服务是否匹配
                        noMatchHtml = dom.qlrr.checkNetService(_$net.closest('.net_point').attr('data-service'));
                        noMatchHtml && _$net.closest('.netPointAddress').append(noMatchHtml);
                    }
                }
                const _$radio = $dom.find('.input_yzSet');
                if (_$radio.length && _$radio.filter(':checked').length == 0) {
                    $dom.find('[data-act-name="choose-default"]:eq(0)')[0].click();
                }
            }
        };

        // 抖音网点列表
        dom.qlrr.loadYunZhanDY = function(json, arg) {
            console.info(json);
            let $dom = domData.netPointDiv;
                let _$net;
                let errorCode = 100; // 假定：抖音电子面单在抖音平台只会在两种情况下出现错误：授权失效、服务未订购
                let _data = json.data || {};
                let _defAddr = _data.defaultAddress;
                let _dyBranchInfo = _data.data;
                let _html = '';
                let methodType = arg.methodType;
                let exid = arg.exid;
                // _data = json.dyBranchInfoResult,
                let noMatchHtml;
            const _shopCode = _data.accountKey;
            if (json.result != 100 && json.result != 101) {
                dom.qlrr.loadYunZhanErrorHtml($dom.find('.address_scroll'), json.message);
                return;
            }

            if (_dyBranchInfo && json.result == 100) {
                comp.Print.Data.yunzhanSetting = _data;
                $.each(_dyBranchInfo, function(_i, _v) {
                    if (!_v.isSuccess) {
                        const msg = _v.errMsg || _v.ErrMsg || '';
                        if (msg.indexOf('授权过期') > -1) {
                            errorCode = 101;
                        } else if (msg.indexOf('发货地址失败') > -1) {
                            errorCode = 102;
                        } else if (msg.indexOf('授权失效') > -1) {
                            errorCode = 104;
                        } else {
                            errorCode = 103;
                        }
                    } else {
                        errorCode = 100;
                    }
                    _html += dom.qlrr.loadYunZhanlistHtml(errorCode, _v, methodType, _data.accountKey, (_data.address || {}).ww);
                });
            } else {
                // Todo
                _html += dom.qlrr.loadYunZhanlistHtml(json.result, _dyBranchInfo, methodType, _dyBranchInfo.accountKey, (_dyBranchInfo.address || {}).ww);
            }

            // 使用其他店铺电子面单账号
            // 如果是抖音厂家代打，则不需要他店铺电子面单/  erp不展示其他店铺
            if (!window.globalPlatformConfig.isDYDAPlatform() && comp.Print.Data.platform !== 'erp') {
                _html += raw`
                <div class="useOtherShopcontainer">
                    <p class="pad_l0">
                        <label><input type="checkbox" class="input_check" id="useOtherShop">使用其他店铺电子面单账号</label>
                    </p>
                    <div class="use_dpdm_box" style="display:none;">
                        <input type="text" placeholder="输入对方店铺代码" id="otherShopCode"><br>
                        <a href="javascript:void(0)" class="expr_btn_js" id="submitShopCodeBtn">确认</a>
                        <p>店铺代码可在“实用工具-管理关联店铺”中找到，由对方店铺发送给您。</p>
                    </div>
                </div>
            `;
            }


            $dom.find('.address_scroll').html(_html);
            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
            $dom.find('#useOtherShop').off('click').click(function() {
                if ($('#useOtherShop').is(':checked')) {
                    $('.use_dpdm_box').show();
                    const scrollDom = dom.find('.goods_net_right .address_scroll');
                    const sh = scrollDom[0].scrollHeight;
                    scrollDom[0].scrollTop = sh;
                } else {
                    // 取消店铺代码
                    if (_shopCode) {
                        that.deleteYunZhanShopCode(domData.currentTempInfo.ModeListShow.Exuserid, domData.currentTempInfo.ModeListShow.Mode_ListShowId, domData.currentTempInfo.ModeListShow.Exid, function() {

                            that.getYunZhanList(arg, function(json) {
                                dom.qlrr.loadYunZhanDY(json, arg);
                            });

                            dom.qlrr.branchChanged(domData.currentTempInfo.ModeListShow.ExCode);

                        });

                    } else {
                        $('.use_dpdm_box').hide();
                    }
                }
            });
            // 标识为添加了店铺代码 回填店铺代码

            if (_shopCode) {
                $dom.find('#useOtherShop').trigger('click');
                $dom.find('#otherShopCode').val(_shopCode.replace(/(.{5}).*(.{5})/, '$1**********$2'));// 回填加密的店铺代码
            }

            // 提交店铺代码确认事件
            $dom.find('#submitShopCodeBtn').click(function() {
                const shopCodeFromDom = $('#otherShopCode').val();
                if (!_shopCode && shopCodeFromDom.indexOf('*') > -1) { // 如果用户自己输入的 code 就包含星号
                    Tatami.showFail('店铺代码不含*，请重新输入');
                    return;
                }
                const shopCode = shopCodeFromDom.indexOf('*') > -1 ? _shopCode : shopCodeFromDom;// 店铺代码如果表单中的 value 不包含 * 则用表单里的，否则用 model.Data.ShopCode.ShopCode
                $('#otherShopCode').val(shopCode.replace(/(.{5}).*(.{5})/, '$1**********$2')); // 加密店铺代码并填充进去
                if (shopCode == '') {
                    Tatami.showFail('店铺代码不能为空');
                    return;
                }
                // 保存 回调函数：刷新获取云栈网点设置方法
                const params = {
                    shopCode,
                    exuserId: arg.exuserId,
                    modeListShowId: arg.modeListShowId,
                    exId: exid,
                };
                that.saveYunZhanShopCode(params, function() {
                    that.getYunZhanList(arg, function(json) {
                        dom.qlrr.loadYunZhanDY(json, arg);
                    });
                });
            });

            // 保存默认网点
            $dom.find('[data-act-name="choose-default"]').off('click').click(async function() {
				if( comp.Print.chenkAuthor()) return false
                let yunZhan;
                    let params = null;
                    let $this = $(this).find('.input_yzSet');
                    let _id;
                    let noMatchHtml;
                    let shared;
                    let shareId;
                    let sharedUserId;
                if (!addressClickFlag) {
                    return;
                }
                await updateAddResswarning($this, $dom);

                addressClickFlag = false;
                // 当前地址已启用
                if ($this.next('span').children('em').length > 0) {
                    addressClickFlag = true;
                    return;
                }
                yunZhan = comp.Print.Data.yunzhanSetting;
                _id = $this.val();

                shared = $this.data('shared');
                sharedUserId = $this.data('shareduserid');
                shareId = $this.data('shareid');
                (yunZhan.data || []).forEach(user => {
                    user.branchList && user.branchList.forEach(branch => {
                        branch.AddressList && branch.AddressList.forEach(address => {
                            const judgeIsShared = (shared == !!user.shared) && (sharedUserId == (user.sharedUserId || ''));
                            if (address.id == _id && judgeIsShared) {
                                params = {
                                    province: address.SendProvince,
                                    city: address.SendCity,
                                    area: address.SendDistrict,
                                    town: address.SendTown,
                                    addressDetail: address.SendAddress,

                                    exCode: branch.CpCode,
                                    branchCode: branch.BranchCode,
                                    segmentCode: branch.segmentCode,

                                    exid: arg.exid,
                                    templateId: arg.modeListShowId,

                                    ww: user.ww,
                                    branchUserId: user.exuserid,

                                    shared: user.shared,
                                    isRelation: !!user.sharedUserNick,
                                    sharedUserId: user.sharedUserId,
                                    ownerId: user.ownerId,
                                    shareId: user.shareId,

                                    wayid: '',
                                };
                                if (comp.Print.Data.platform == 'erp') {
                                    params = {
                                        ...params,
                                        sellerId: branch.SellerId,
                                    };
                                }
                            }
                        });
                    });
                });
                // 保存默认
                if (!params) {
                    addressClickFlag = true;
                } else {
                    params.method = methodType;
                    params.kddType = arg.kddType;
                    that.saveYunZhanSet(params, function() {
                        addressClickFlag = true;
                        dom.qlrr.chooseYunZhan($this, $dom);
                        if (params.method != 2) { // 物流云授权无需校验服务是否匹配
                            noMatchHtml = dom.qlrr.checkNetService($this.closest('.net_point').attr('data-service'));
                            noMatchHtml && $this.closest('.netPointAddress').append(noMatchHtml);
                        }
                        dom.qlrr.branchChanged();
                    });
                }
            });

            // 根据接口返回的数据选中网点，没有默认网点时保存第一个网点为默认网点
            if (_defAddr) {
                if (_defAddr && !$.isEmptyObject(_defAddr)) {
                    const ww = _defAddr.ww;
                    _defAddr.id = encodeURIComponent((_defAddr.ww || ww || '') + (_defAddr.f_s || '') + (_defAddr.f_c || '') + (_defAddr.f_q || '') + (_defAddr.f_addr || '') + (_defAddr.branchCode || ''));
                    _$net = $dom.find('.input_yzSet[value="' + _defAddr.id + '"]');
                    if (_defAddr.branchCode) { // 加一层branchCode过滤
                        // _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']:eq(0)');
                        _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']');
                    }

                    // const $preparatoryBranch = _$net.first(); //保底的 branch

                    // 号段过滤
                    if (_defAddr.segmentCode) {
                        _$net = _$net.filter(`[data-segmentcode=${_defAddr.segmentCode}]`);
                    }

                    // shared 过滤
                    _$net = _$net.filter(`[data-shared=${!!_defAddr.shared}]`);
                    // shareId 过滤
                    if (_defAddr.shareId) {
                        _$net = _$net.filter(`[data-shareid=${_defAddr.shareId}]`);
                    }
                    // sharedUserId 过滤
                    if (_defAddr.sharedUserId) {
                        _$net = _$net.filter(`[data-shareduserid=${_defAddr.sharedUserId}]`);
                    }

                    // 如果筛选完后，没有符合条件的，则使用保底的 branch
                    // if( _$net.length == 0){
                    //     _$net = $preparatoryBranch
                    // }

                    dom.qlrr.chooseYunZhan(_$net.first(), $dom);

                    // dom.qlrr.chooseYunZhan( _$net, $dom );
                    if (methodType != 2) { // 物流云授权无需校验服务是否匹配
                        noMatchHtml = dom.qlrr.checkNetService(_$net.closest('.net_point').attr('data-service'));
                        noMatchHtml && _$net.closest('.netPointAddress').append(noMatchHtml);
                    }
                }
                const _$radio = $dom.find('.input_yzSet');
                if (_$radio.length && _$radio.filter(':checked').length == 0) {
                    $dom.find('[data-act-name="choose-default"]:eq(0)')[0].click();
                }
            }
        };

        // 美团网点列表
        dom.qlrr.loadYunZhanTHH = function(json, arg) {
            let $dom = domData.netPointDiv;
                let _$net;
                let errorCode = 100;
                let _data = json.data || {}; // 数据
                let _defAddr = _data.defaultAddress; // 默认地址
                let _thhBranchInfo = _data.data;
                let _html = '';
                let methodType = arg.methodType;
                let exid = arg.exid;
                // _data = json.dyBranchInfoResult,
                let noMatchHtml;
            const _shopCode = _data.accountKey;

            if (json.result != 100 && json.result != 101) {
                dom.qlrr.loadYunZhanErrorHtml($dom.find('.address_scroll'), json.message);
                return;
            }

            if (_thhBranchInfo && json.result == 100) {
                comp.Print.Data.yunzhanSetting = _data;
                $.each(_thhBranchInfo, function(_i, _v) {
                    if (!_v.isSuccess) {
                        const msg = _v.errMsg || _v.ErrMsg || '';
                        if (msg.indexOf('授权过期') > -1) {
                            errorCode = 101;
                        } else if (msg.indexOf('发货地址失败') > -1) {
                            errorCode = 102;
                        } else {
                            errorCode = 103;
                        }
                    } else {
                        errorCode = 100;
                    }
                    _html += dom.qlrr.loadYunZhanlistHtml(errorCode, _v, methodType, _data.accountKey, (_data.address || {}).ww);
                });
            } else {
                // Todo
                _html += dom.qlrr.loadYunZhanlistHtml(json.result, _thhBranchInfo, methodType, _thhBranchInfo.accountKey, (_thhBranchInfo.address || {}).ww);
            }

            // 使用其他店铺电子面单账号 美团暂时不做这个
            $dom.find('.address_scroll').html(_html);

            $dom.find('#useOtherShop').off('click').click(function() {
                if ($('#useOtherShop').is(':checked')) {
                    $('.use_dpdm_box').show();
                    const scrollDom = dom.find('.goods_net_right .address_scroll');
                    const sh = scrollDom[0].scrollHeight;
                    scrollDom[0].scrollTop = sh;
                } else {
                    // 取消店铺代码
                    if (_shopCode) {
                        that.deleteYunZhanShopCode(domData.currentTempInfo.ModeListShow.Exuserid, domData.currentTempInfo.ModeListShow.Mode_ListShowId, domData.currentTempInfo.ModeListShow.Exid, function() {

                            that.getYunZhanList(arg, function(json) {
                                dom.qlrr.loadYunZhanTHH(json, arg);
                            });

                            dom.qlrr.branchChanged(domData.currentTempInfo.ModeListShow.ExCode);

                        });

                    } else {
                        $('.use_dpdm_box').hide();
                    }
                }
            });
            // 标识为添加了店铺代码 回填店铺代码

            if (_shopCode) {
                $dom.find('#useOtherShop').trigger('click');
                $dom.find('#otherShopCode').val(_shopCode.replace(/(.{5}).*(.{5})/, '$1**********$2'));// 回填加密的店铺代码
            }

            // 提交店铺代码确认事件
            $dom.find('#submitShopCodeBtn').click(function() {
                const shopCodeFromDom = $('#otherShopCode').val();
                if (!_shopCode && shopCodeFromDom.indexOf('*') > -1) { // 如果用户自己输入的 code 就包含星号
                    Tatami.showFail('店铺代码不含*，请重新输入');
                    return;
                }
                const shopCode = shopCodeFromDom.indexOf('*') > -1 ? _shopCode : shopCodeFromDom;// 店铺代码如果表单中的 value 不包含 * 则用表单里的，否则用 model.Data.ShopCode.ShopCode
                $('#otherShopCode').val(shopCode.replace(/(.{5}).*(.{5})/, '$1**********$2')); // 加密店铺代码并填充进去
                if (shopCode == '') {
                    Tatami.showFail('店铺代码不能为空');
                    return;
                }
                // 保存 回调函数：刷新获取云栈网点设置方法
                const params = {
                    shopCode,
                    exuserId: arg.exuserId,
                    modeListShowId: arg.modeListShowId,
                    exId: exid,
                };
                that.saveYunZhanShopCode(params, function() {
                    that.getYunZhanList(arg, function(json) {
                        dom.qlrr.loadYunZhanTHH(json, arg);
                    });
                });
            });

            // 保存默认网点
            $dom.find('[data-act-name="choose-default"]').off('click').click(function() {
				if( comp.Print.chenkAuthor()) return false
                let yunZhan;
                    let params = null;
                    let $this = $(this).find('.input_yzSet');
                    let _id;
                    let noMatchHtml;
                    let shared;
                    let shareId;
                    let sharedUserId;
                if (!addressClickFlag) {
                    return;
                }
                addressClickFlag = false;
                // 当前地址已启用
                if ($this.next('span').children('em').length > 0) {
                    addressClickFlag = true;
                    return;
                }
                yunZhan = comp.Print.Data.yunzhanSetting;
                _id = $this.val();

                shared = $this.data('shared');
                sharedUserId = $this.data('shareduserid');
                shareId = $this.data('shareid');

                (yunZhan.data || []).forEach(user => {
                    user.branchList && user.branchList.forEach(branch => {
                        branch.AddressList && branch.AddressList.forEach(address => {
                            const judgeIsShared = (shared == !!user.shared) && (sharedUserId == (user.sharedUserId || ''));
                            if (address.id == _id && judgeIsShared) {
                                params = {
                                    province: address.SendProvince,
                                    city: address.SendCity,
                                    area: address.SendDistrict,
                                    town: address.SendTown,
                                    addressDetail: address.SendAddress,

                                    exCode: branch.CpCode,
                                    branchCode: branch.BranchCode,
                                    segmentCode: branch.segmentCode,

                                    exid: arg.exid,
                                    templateId: arg.modeListShowId,

                                    ww: user.ww,
                                    branchUserId: user.exuserid,

                                    shared: user.shared,
                                    isRelation: !!user.sharedUserNick,
                                    sharedUserId: user.sharedUserId,
                                    ownerId: user.ownerId,
                                    shareId: user.shareId,

                                    wayid: '',
                                    logisticsAccount: address.logisticsAccount, // 美团需要月结单号
                                };
                            }
                        });
                    });
                });
                // 保存默认
                if (!params) {
                    addressClickFlag = true;
                } else {
                    params.method = methodType;
                    params.kddType = arg.kddType;
                    that.saveYunZhanSet(params, function() {
                        addressClickFlag = true;
                        dom.qlrr.chooseYunZhan($this, $dom);
                        if (params.method != 2) { // 物流云授权无需校验服务是否匹配
                            noMatchHtml = dom.qlrr.checkNetService($this.closest('.net_point').attr('data-service'));
                            noMatchHtml && $this.closest('.netPointAddress').append(noMatchHtml);
                        }
                        dom.qlrr.branchChanged();
                    });
                }
            });

            // 根据接口返回的数据选中网点，没有默认网点时保存第一个网点为默认网点
            if (_defAddr) {
                if (_defAddr && !$.isEmptyObject(_defAddr)) {
                    const ww = _defAddr.ww;
                    _defAddr.id = encodeURIComponent((_defAddr.ww || ww || '') + (_defAddr.f_s || '') + (_defAddr.f_c || '') + (_defAddr.f_q || '') + (_defAddr.f_addr || ''));
                    _$net = $dom.find('.input_yzSet[value="' + _defAddr.id + '"]');
                    if (_defAddr.branchCode) { // 加一层branchCode过滤
                        // _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']:eq(0)');
                        _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']');
                    }

                    // const $preparatoryBranch = _$net.first(); //保底的 branch

                    // 号段过滤
                    if (_defAddr.segmentCode) {
                        _$net = _$net.filter(`[data-segmentcode=${_defAddr.segmentCode}]`);
                    }

                    // shared 过滤
                    _$net = _$net.filter(`[data-shared=${!!_defAddr.shared}]`);
                    // shareId 过滤
                    if (_defAddr.shareId) {
                        _$net = _$net.filter(`[data-shareid=${_defAddr.shareId}]`);
                    }
                    // sharedUserId 过滤
                    if (_defAddr.sharedUserId) {
                        _$net = _$net.filter(`[data-shareduserid=${_defAddr.sharedUserId}]`);
                    }

                    // 如果筛选完后，没有符合条件的，则使用保底的 branch
                    // if( _$net.length == 0){
                    //     _$net = $preparatoryBranch
                    // }

                    dom.qlrr.chooseYunZhan(_$net.first(), $dom);

                    // dom.qlrr.chooseYunZhan( _$net, $dom );
                    if (methodType != 2) { // 物流云授权无需校验服务是否匹配
                        noMatchHtml = dom.qlrr.checkNetService(_$net.closest('.net_point').attr('data-service'));
                        noMatchHtml && _$net.closest('.netPointAddress').append(noMatchHtml);
                    }
                }
                const _$radio = $dom.find('.input_yzSet');
                if (_$radio.length && _$radio.filter(':checked').length == 0) {
                    $dom.find('[data-act-name="choose-default"]:eq(0)')[0].click();
                }
            }
        };

        // 快手网点列表
        dom.qlrr.loadYunZhanKs = function(json, arg) {
            let $dom = domData.netPointDiv;
                let _$net;
                let errorCode = 100; // 假定：抖音电子面单在抖音平台只会在两种情况下出现错误：授权失效、服务未订购
                let _data = json.data || {};
                let _defAddr = _data.defaultAddress;
                let _dyBranchInfo = _data.data;
                let _html = '';
                let methodType = arg.methodType;
                let exid = arg.exid;
                // _data = json.dyBranchInfoResult,
                let noMatchHtml;
            const _shopCode = _data.accountKey;

            if (json.result != 100 && json.result != 101) {
                dom.qlrr.loadYunZhanErrorHtml($dom.find('.address_scroll'), json.message);
                return;
            }

            if (_dyBranchInfo && json.result == 100) {
                comp.Print.Data.yunzhanSetting = _data;
                $.each(_dyBranchInfo, function(_i, _v) {
                    if (!_v.isSuccess) {
                        const msg = _v.errMsg || _v.ErrMsg || '';
                        if (msg.indexOf('授权过期') > -1) {
                            errorCode = 101;
                        } else if (msg.indexOf('发货地址失败') > -1) {
                            errorCode = 102;
                        } else if (msg.indexOf('授权失效') > -1) {
                            errorCode = 104;
                        } else {
                            errorCode = 103;
                        }
                    } else {
                        errorCode = 100;
                    }
                    _html += dom.qlrr.loadYunZhanlistHtml(errorCode, _v, methodType, _data.accountKey, (_data.address || {}).ww);
                });
                // _html += dom.qlrr.loadYunZhanlistHtml( errorCode, _dyBranchInfo ,methodType,_dyBranchInfo.accountKey,(_dyBranchInfo.address || {}).ww);
            } else {
                // Todo
                _html += dom.qlrr.loadYunZhanlistHtml(json.result, _dyBranchInfo, methodType, _dyBranchInfo.accountKey, (_dyBranchInfo.address || {}).ww);
            }

            // 使用其他店铺电子面单账号
            if (comp.Print.Data.platform !== 'erp') {
                _html += raw`
                    <div class="useOtherShopcontainer">
                        <p class="pad_l0">
                            <label><input type="checkbox" class="input_check" id="useOtherShop">使用其他店铺电子面单账号</label>
                        </p>
                        <div class="use_dpdm_box" style="display:none;">
                            <input type="text" placeholder="输入对方店铺代码" id="otherShopCode"><br>
                            <a href="javascript:void(0)" class="expr_btn_js" id="submitShopCodeBtn">确认</a>
                            <p>店铺代码可在“实用工具-管理关联店铺”中找到，由对方店铺发送给您。</p>
                        </div>
                    </div>
                `;
            }

            $dom.find('.address_scroll').html(_html);

            $dom.find('#useOtherShop').off('click').click(function() {
                if ($('#useOtherShop').is(':checked')) {
                    $('.use_dpdm_box').show();
                    const scrollDom = dom.find('.goods_net_right .address_scroll');
                    const sh = scrollDom[0].scrollHeight;
                    scrollDom[0].scrollTop = sh;
                } else {
                    // 取消店铺代码
                    if (_shopCode) {
                        that.deleteYunZhanShopCode(domData.currentTempInfo.ModeListShow.Exuserid, domData.currentTempInfo.ModeListShow.Mode_ListShowId, domData.currentTempInfo.ModeListShow.Exid, function() {

                            that.getYunZhanList(arg, function(json) {
                                dom.qlrr.loadYunZhanKs(json, arg);
                            });

                            dom.qlrr.branchChanged(domData.currentTempInfo.ModeListShow.ExCode);

                        });

                    } else {
                        $('.use_dpdm_box').hide();
                    }
                }
            });
            // 标识为添加了店铺代码 回填店铺代码

            if (_shopCode) {
                $dom.find('#useOtherShop').trigger('click');
                $dom.find('#otherShopCode').val(_shopCode.replace(/(.{5}).*(.{5})/, '$1**********$2'));// 回填加密的店铺代码
            }

            // 提交店铺代码确认事件
            $dom.find('#submitShopCodeBtn').click(function() {
                const shopCodeFromDom = $('#otherShopCode').val();
                if (!_shopCode && shopCodeFromDom.indexOf('*') > -1) { // 如果用户自己输入的 code 就包含星号
                    Tatami.showFail('店铺代码不含*，请重新输入');
                    return;
                }
                const shopCode = shopCodeFromDom.indexOf('*') > -1 ? _shopCode : shopCodeFromDom;// 店铺代码如果表单中的 value 不包含 * 则用表单里的，否则用 model.Data.ShopCode.ShopCode
                $('#otherShopCode').val(shopCode.replace(/(.{5}).*(.{5})/, '$1**********$2')); // 加密店铺代码并填充进去
                if (shopCode == '') {
                    Tatami.showFail('店铺代码不能为空');
                    return;
                }
                // 保存 回调函数：刷新获取云栈网点设置方法
                const params = {
                    shopCode,
                    exuserId: arg.exuserId,
                    modeListShowId: arg.modeListShowId,
                    exId: exid,
                };
                that.saveYunZhanShopCode(params, function() {
                    that.getYunZhanList(arg, function(json) {
                        dom.qlrr.loadYunZhanKs(json, arg);
                    });
                });
            });

            // 保存默认网点
            $dom.find('[data-act-name="choose-default"]').off('click').click(async function() {
				if( comp.Print.chenkAuthor()) return false

                let yunZhan;
                    let params = null;
                    let $this = $(this).find('.input_yzSet');
                    let _id;
                    let noMatchHtml;
                    let shared;
                    let shareId;
                    let sharedUserId;
                if (!addressClickFlag) {
                    return;
                }
                await updateAddResswarning($this, $dom);

                addressClickFlag = false;
                // 当前地址已启用
                if ($this.next('span').children('em').length > 0) {
                    addressClickFlag = true;
                    return;
                }
                yunZhan = comp.Print.Data.yunzhanSetting;
                _id = $this.val();

                shared = $this.data('shared');
                sharedUserId = $this.data('shareduserid');
                shareId = $this.data('shareid');

                (yunZhan.data || []).forEach(user => {
                    user.branchList && user.branchList.forEach(branch => {
                        branch.AddressList && branch.AddressList.forEach(address => {
                            const judgeIsShared = (shared == !!user.shared) && (sharedUserId == (user.sharedUserId || ''));
                            if (address.id == _id && judgeIsShared) {
                                params = {
                                    province: address.SendProvince,
                                    city: address.SendCity,
                                    area: address.SendDistrict,
                                    town: address.SendTown,
                                    addressDetail: address.SendAddress,

                                    exCode: branch.CpCode,
                                    branchCode: branch.BranchCode,
                                    segmentCode: branch.segmentCode,
									paymentType: branch.paymentType,
                                    exid: arg.exid,
                                    templateId: arg.modeListShowId,

                                    ww: user.ww,
                                    branchUserId: user.exuserid,

                                    shared: user.shared,
                                    isRelation: !!user.sharedUserNick,
                                    sharedUserId: user.sharedUserId,
                                    ownerId: user.ownerId,
                                    shareId: user.shareId,
                                    branchName: branch.BranchName,
                                    wayid: '',
                                    sellerId: branch.SellerId
                                };

                                if (domData.currentTempInfo.ModeListShow.KddType == 14) {
                                    params = {
                                        ...params,
                                        merchantAccount: branch.logisticsAccount,
                                        sphBranchId: branch.sphBranchId
                                    };
								}
								if (params && branch.logisticsAccount) {
									params.logisticsAccount = branch.logisticsAccount
								}
							}
						});

                    });
                });
                // 保存默认
                if (!params) {
                    addressClickFlag = true;
                } else {
                    params.method = methodType;
                    params.kddType = arg.kddType;
                    that.saveYunZhanSet(params, function() {
                        addressClickFlag = true;
                        dom.qlrr.chooseYunZhan($this, $dom);
                        if (params.method != 2) { // 物流云授权无需校验服务是否匹配
                            noMatchHtml = dom.qlrr.checkNetService($this.closest('.net_point').attr('data-service'));
                            noMatchHtml && $this.closest('.netPointAddress').append(noMatchHtml);
                        }
                        dom.qlrr.branchChanged();
                    });
                }
            });

            // 根据接口返回的数据选中网点，没有默认网点时保存第一个网点为默认网点
            if (_defAddr) {
                if (_defAddr && !$.isEmptyObject(_defAddr)) {
                    const ww = _defAddr.ww;
					_defAddr.id = encodeURIComponent((_defAddr.ww || ww || '') + (_defAddr.f_s || '') + (_defAddr.f_c || '') + (_defAddr.f_q || '') + (_defAddr.f_addr || '') + (_defAddr.branchCode || '') + (_defAddr.merchantAccount || '') + (_defAddr.paymentType || ''));
                    _$net = $dom.find('.input_yzSet[value="' + _defAddr.id + '"]');
                    if (_defAddr.branchCode) { // 加一层branchCode过滤
                        // _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']:eq(0)');
                        _$net = _$net.filter('[data-branchcode=' + _defAddr.branchCode + ']');
                    }

                    // const $preparatoryBranch = _$net.first(); //保底的 branch

                    // 号段过滤
                    if (_defAddr.segmentCode) {
                        _$net = _$net.filter(`[data-segmentcode=${_defAddr.segmentCode}]`);
                    }

                    // shared 过滤
                    _$net = _$net.filter(`[data-shared=${!!_defAddr.shared}]`);
                    // shareId 过滤
                    if (_defAddr.shareId) {
                        _$net = _$net.filter(`[data-shareid=${_defAddr.shareId}]`);
                    }
                    // sharedUserId 过滤
                    if (_defAddr.sharedUserId) {
                        _$net = _$net.filter(`[data-shareduserid=${_defAddr.sharedUserId}]`);
                    }

                    // 如果筛选完后，没有符合条件的，则使用保底的 branch
                    // if( _$net.length == 0){
                    //     _$net = $preparatoryBranch
                    // }

                    dom.qlrr.chooseYunZhan(_$net.first(), $dom);

                    // dom.qlrr.chooseYunZhan( _$net, $dom );
                    if (methodType != 2) { // 物流云授权无需校验服务是否匹配
                        noMatchHtml = dom.qlrr.checkNetService(_$net.closest('.net_point').attr('data-service'));
                        noMatchHtml && _$net.closest('.netPointAddress').append(noMatchHtml);
                    }
                }
                const _$radio = $dom.find('.input_yzSet');
                if (_$radio.length && _$radio.filter(':checked').length == 0) {
                    $dom.find('[data-act-name="choose-default"]:eq(0)')[0].click();
                }
            }
        };
         // 加载JD青龙云栈设置html
         dom.qlrr.loadJDYunZhan = function ( arg) {
            let $dom = domData.netPointDiv;
                let errorCode;
                let _defAddrID;
                let modeListShow = domData.currentTempInfo.ModeListShow;
				let {methodType,modeListShowId,exid,kddType}  = arg
            let yunZhanSettingHtml = '';
				yunZhanSettingHtml +='<h2>选择发货网点</h2>';
				yunZhanSettingHtml += '<div class="newPointHead"><label style="margin: 0 0 6px 0;display: block;color: #fff;"><input type="radio" name="twcnAuthAccount" wayid="'+ YUNZHAN_AUTH_TYPE['ERP_SHOP'] +'" class="input_radio">店铺网点授权'
							+'<a href="#/shops" class="bind_shop link">前往添加店铺</a>'
							+'</label>'
							+'<label style="margin: 0 0 6px 0;display: block;color: #fff;"><input type="radio" name="twcnAuthAccount" wayid="'+ YUNZHAN_AUTH_TYPE['ERP_KDD_SHOP'] +'" class="input_radio">面单店铺授权'
							+'<a class="bind_shop link"   href="#/shops?shopType=1">新增面单店铺</a></label>'
							+'<p><input type="button" id="refreshAuthorizeRet" value="授权后点我刷新" class="btn_orange_jd"></p></div>'
				yunZhanSettingHtml += `<div class="address_scroll" style="">
							</div>`

            domData.netPointDiv.html(yunZhanSettingHtml);
			that.getJDYunZhanList(arg,function(json){
				if(json.result != 100 && json.result != 101){
					dom.qlrr.loadYunZhanErrorHtml( $dom.find('.address_scroll') );
					return
				}
				//渲染列表
				errorCode = json.result;
				dom.qlrr.loadJDYunZhanlistHtml( errorCode, json.data, modeListShow);
			} );

			$dom.find('[name=twcnAuthAccount][wayid="' + methodType + '"]').prop('checked',true);
      		//更改获取云栈方式
            $dom.find('[name=twcnAuthAccount]').off('click').click(function(){
                const wayid = $(this).attr('wayid');
                // isMoreStore && $dom.find('#refreshAuthorizeRet')[wayid == '1' ? 'show' : 'hide']();
                $dom.find('#refreshAuthorizeRet')[wayid == '1'  ? 'show' : 'hide']();
                $dom.find('.address_scroll').html('数据加载中……');
                that.setYunZhanMethod({
                    modeListShowId:modeListShowId,
                    exId:exid,
					expressType:kddType,
					exCode:arg.exCode,
                    method: wayid,
                },function(){
                    arg.methodType = wayid;
                    that.getJDYunZhanList(arg, function(json){
						if(json.result != 100 && json.result != 101){
							dom.qlrr.loadYunZhanErrorHtml( $dom.find('.address_scroll') );
							return
						}
						//渲染列表
						errorCode = json.result;
						dom.qlrr.loadJDYunZhanlistHtml( errorCode, json.data, modeListShow);
					});
                });
            });
			$dom.find('.bind_shop').click(function () {
				dom.qlrr.close()
				window.location.hash= '#/shops'
			})
        };
        dom.qlrr.chooseYunZhan = function($this, $dom) {
            const currentNet = $this.first(); // 做一个兼容，如果这里的 $this 包含了多个元素，那么使用第一个
            console.log($this);
            const parentBox = $this.parents('.add_list_adrs');
            const shopTitle = parentBox.find('.yzShopName');

            function targetWdSet() {
                $dom.find('.lock_text').remove();
                $dom.find('.unlock').remove();
                let lockHtml = `<span class="lock_text"><i class="iconfont ifont-lock" style="font-size:14px"></i>使用中</span>`;
                let unlockHtml = `<span class="unlock">解锁</span>`;
                shopTitle.append(lockHtml);
                shopTitle.append(unlockHtml);
                currentNet.prop('checked', true);
                $dom.find('.chooseYunZhan').remove();
                currentNet.next().append('<em class="chooseYunZhan">使用中</em>');
                $(shopTitle).find('.unlock').click(function() {
                    console.log('dasda');
                    $(this).remove();
                    Tatami.showSuccess('已解锁');
                });
            }
            // if(!shopTitle.find('.lock_text').length && $dom.find('.lock_text').length){
            //     model({
            //         type: 'confirm',
            //         title: '提示',
            //         width: 350,
            //         minHeight: 160,
            //         content: '您更换不同店铺的网点后，使用该网点的单号将暂时无法打印，为避免重复申请单号打印，请您确认后更换',
            //         okCb:targetWdSet
            //     });
            // }else{
            targetWdSet();
            // }
        };


        // 加载云栈设置的错误信息
        dom.qlrr.loadYunZhanErrorHtml = function($dom, msg) {
            let errHtml;
            if (!$dom) {
                $dom = domData.netPointDiv;
            }
            errHtml = `<p style="" class="f_gray">获取网点设置信息出错，请联系在线客服<br/>${msg || ''}</p>`;
            errHtml += dom.qlrr.getShouquanHtml();
            $dom.html(errHtml);
        };

        // 展示网点电子面单的设置信息 domData.currentTempInfo:当前点击的模版详细信息
        dom.qlrr.showBranchSetting = function(model, exCode, exid) {
            if (model.IsError || model.Msg != '') {
                if (model.Msg.indexOf('Invalid session') > -1) { // 授权过期
                    if (exCode == 'YUNDA') {
                        // alert("该店铺授权已过期");
                        // dom.qlrr.loadYUNDASettingHtml(model, exCode);
                        domData.yundaDiv.html('<p style=\'padding: 240px 0 0 315px;color:red;\'>该店铺授权已过期</p>').show();
                    } else {
                        // 提示错误信息 session过期
                        const errHtml = '<div class="dzmd_load_box" style="color: #fff;padding: 230px 0 0 50px;">\
                                            <p style="color:red;" class="f_gray" >该店铺授权已过期</p>\
                                    </div>';
                        domData.netPointDiv.html(errHtml);
                    }
                    return;
                } else {
                    // 加载错误信息
                    dom.qlrr.loadYunZhanErrorHtml();
                }
            } else {
                // 根据excode判断右侧加载框
                switch (exCode) {
                    case 'CP468398': // 承诺达独立
                        dom.qlrr.loadCNDSettingHtml(model, exCode);
                        break;
                    case 'YTO':// 圆通 100,103,165,254
                        dom.qlrr.loadYTOSettingHtml(model, exCode);
                        break;
                    case 'YUNDA':// 韵达客户端 89
                        dom.qlrr.loadYUNDASettingHtml(model, exCode);
                        break;
                    case 'HTKY':// 百世汇通电子 132,174
                        dom.qlrr.loadHTKYSettingHtml(model, exCode);
                        break;
                    case 'STO':// 申通电子 114 131 133 156 169 232
                        dom.qlrr.loadSTOSettingHtml(model, exCode);
                        break;
                    case 'ZTO':// 中通电子 134 135 177 181
                        dom.qlrr.loadZTOSettingHtml(model, exCode);
                        break;
                    case 'ZTOGJ': // 中通国际直邮
                        dom.qlrr.loadZTOGJSettingHtml(model, exCode);
                        break;
                    case 'TTKDEX':// 天天电子面单 139 149 172 229
                        dom.qlrr.loadTTKDEXSettingHtml(model, exCode);
                        break;
                    case 'UC':// todo 优速电子 138 目前无
                        break;
                    case 'SF':// 顺丰电子 分到付和非到付
                        dom.qlrr.loadSFSettingHtml(model, exCode);
                        break;
                    case 'DBKD':
                        dom.qlrr.loadDBKDSettingHtml(model, exCode);
                        break;
                    case 'SURE': // 速尔
                    case 'DISTRIBUTOR_12017865':// 安能电子
                    case 'UAPEX':
                    case 'CN7000001003751': // 跨越
                    case 'ANEKY': // 安能快运
                    case 'DISTRIBUTOR_13413991': // 安鲜达
                        dom.qlrr.loadANSettingHtml(model, exCode);
                        break;
                    case 'JD':
                        dom.qlrr.loadJDSettingHtml(model, exCode, exid);
                        break;
                    case 'LB':
                    case 'JTSD': // 极兔速递
                        dom.qlrr.loadJTSDSettingHtml(model, exCode);
                        break;
                    case 'ZYKD': // 众邮
                        dom.qlrr.loadZYKDSettingHtml(model, exCode);
                        break;
                    case 'SZKKE': // 京广快递
                        dom.qlrr.loadJGSettingHtml(model, exCode);
                        break;
                    case 'FENGWANG': // 丰网速运
                        dom.qlrr.loadFWSYSettingHtml(model, exCode);
                        break;
                    default: // 还未对接的模板给出一个提示
                        dom.qlrr.loadNotYetTipsHtml(model, exCode);
                        break;
                }
                // 网点电子面单的保存
                $('.netPointSave').click(function() {
					if( comp.Print.chenkAuthor()) return

                    const obj = printData.netPointSetting;
                    if (obj.Exsubid == null) {
                        obj.Exsubid = 0;
                    }
                    if (obj.TemplateId == null) {
                        obj.TemplateId = domData.currentTempInfo.ModeListShow.Mode_ListShowId || '';
                    }
                    const exCode = $(this).attr('data-exCode');
                    let parentDiv = $(this).parent().parent();
                        let _infoMsg;
                        let $dom;
                    switch (exCode) {
                        case 'CP468398':// 承诺达
                        case 'YTO': // 圆通
                        case 'HTKY': // 百世汇通
                        case 'ZTO': // 中通
                            obj.Username = parentDiv.find('.data-userName').val();
                            obj.Password = parentDiv.find('.data-password').val();
                            $dom = $('#netPointMsg');
                            if (obj.Username == '' || obj.Password == '') {
                                $dom.html('用户名或密码不能为空');
                                $dom.css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', 'green').show();
                                });
                            }
                            break;
                        case 'STO': // 申通
                        case 'TTKDEX': // 天天
                        case 'SF': // 顺丰 全一
                        case 'DBKD': // 德邦
                        case 'UAPEX':
                        case 'SURE': // 速尔
                        case 'ANEKY': // 安能快运
                        case 'ZTOGJ': // 中通国际
                        case 'DISTRIBUTOR_13413991': // 安鲜达
                            obj.Username = parentDiv.find('.data-userName').val();
                            obj.Password = parentDiv.find('.data-password').val();
                            obj.Custid = parentDiv.find('.data-custid').val();
                            if (exCode == 'SF') {
                                obj.Area1 = $('input[name=pay_type]:checked').val();
                            }

                            if (exCode == 'DBKD') {
                                // obj.customName = obj.Custid
                                obj.Username = obj.Custid;
                                obj.Password = obj.Custid;
                            }
                            if (exCode == 'DISTRIBUTOR_13413991') {
                                obj.Password = '后端统一的强校验'; // 哎
                            }

                            if (obj.Username === '' || obj.Password === '') {
                                if (exCode == 'SF') {
                                    _infoMsg = '用户名或密码不能为空';
                                } else if (exCode == 'SURE') {
                                    _infoMsg = '客户编码或仓库代码不能为空';
                                } else if (exCode == 'DBKD') {
                                    _infoMsg = '月结卡号不能为空';
                                } else if (exCode == 'ZTOGJ') {
                                    _infoMsg = 'ID 或密码不能为空';
                                } else if (exCode == 'DISTRIBUTOR_13413991') {
                                    _infoMsg = '货主编码不能为空';
                                } else {
                                    _infoMsg = '客户标识和密钥不能为空';
                                }
                                $('#netPointMsg').html(_infoMsg).css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', 'green').show();
                                });
                            }
                            break;
                        case 'CN7000001003751':
                            obj.Username = parentDiv.find('.data-userName').val();
                            obj.Password = '后端统一的强校验';
                            obj.Custid = parentDiv.find('.data-custid').val();
                            if (!obj.Username) {
                                $('#netPointMsg').html('客户编码不能为空').css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', 'green').show();
                                });
                            }
                            break;
                        case 'YUNDA': // 韵达
                            obj.Username = parentDiv.find('.data-userName').val();
                            obj.Password = parentDiv.find('.data-password').val();
                            obj.F_name = parentDiv.find('.data-fName').val();
                            obj.F_tel = parentDiv.find('.data-fTel').val();
                            obj.F_mobile = parentDiv.find('.data-fMobile').val();
                            obj.F_com = parentDiv.find('.data-fCom').val();
                            obj.F_s = parentDiv.find('.data-fs').val();
                            obj.F_c = parentDiv.find('.data-fc').val();
                            obj.F_q = parentDiv.find('.data-fq').val();
                            obj.F_addr = parentDiv.find('.data-fAddr').val();
                            obj.F_zip = parentDiv.find('.data-fZip').val();
                            var area1 = ''; var
area2 = '';
                            $('.area1_chk').each(function(i, v) {
                                if ($(this).is(':checked')) {
                                    area1 += $(this).val();
                                }
                            });
                            $('.area2_chk').each(function(i, v) {
                                if ($(this).is(':checked')) {
                                    area2 += $(this).val();
                                }
                            });
                            if (parentDiv.find('.data-area1-custom').val() != '' && parentDiv.find('.data-area1-custom').val() != '自定义内容') {
                                area1 += '&' + parentDiv.find('.data-area1-custom').val();
                            }
                            if (parentDiv.find('.data-area2-custom').val() != '' && parentDiv.find('.data-area2-custom').val() != '自定义内容') {
                                area2 += '&' + parentDiv.find('.data-area2-custom').val();
                            }
                            obj.Area1 = area1;
                            obj.Area2 = area2;

                            if (obj.Username == '' || obj.Password == '') {
                                $('#netPointMsg').html('帐号或密码不能为空').css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', 'green').show();
                                });
                            }
                            break;
                        case 'DISTRIBUTOR_12017865':
                            obj.Username = '安能' + comp.Print.Data.sellerNick;
                            obj.Password = parentDiv.find('.data-userName').val();
                            if (obj.Password == '') {
                                $('#netPointMsg').html('客户编码不能为空').css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', '#eb9e26').show();
                                });
                            }
                            break;
                        case 'JD':
                            obj.Username = parentDiv.find('.data-userName').val();
                            obj.Password = parentDiv.find('.data-password').val();
                            obj.JdShopCode = parentDiv.find('.data-jdShopCode').val();
                            obj.F_s = parentDiv.find('select[name=province] option:selected').attr('data-addr');
                            obj.F_c = parentDiv.find('select[name=city] option:selected').attr('data-addr');
                            obj.F_q = parentDiv.find('select[name=district] option:selected').attr('data-addr');
                            obj.F_addr = parentDiv.find('.data-address').val(); // 详细地址
                            if (obj.Username == '') {
                                $('#netPointMsg').html('客户编码不能为空').css('color', 'red').show();
                            } else if (exid != '999' && obj.Password == '') {
                                $('#netPointMsg').html('客户编码或发货仓库不能为空').css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', '#eb9e26').show();
                                });
                            }
                            break;
                        case 'LB':
                        case 'JTSD':
                            obj.Username = parentDiv.find('.data-custid').val();
                            obj.Password = parentDiv.find('.data-password').val();
                            if (obj.Username == '' || obj.Password == '') {
                                $('#netPointMsg').html('客户编号或密码不能为空').css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', '#eb9e26').show();
                                });
                            }
                            break;
                        case 'ZYKD':
                            obj.Username = parentDiv.find('.data-username').val();
                            obj.Password = parentDiv.find('.data-password').val();
                            obj.Custid = parentDiv.find('.data-custid').val();
                            if (obj.Username == '' || obj.Custid == '' || obj.Password == '') {
                                $('#netPointMsg').html('用户账号、名称或网点编码不能为空').css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', '#eb9e26').show();
                                });
                            }
                            break;
                        case 'SZKKE': // 京广快递
                            obj.Username = parentDiv.find('.data-customName').val();
                            obj.Password = parentDiv.find('.data-customPassword').val();
                            if (obj.Username == '' || obj.Password == '') {
                                $('#netPointMsg').html('客户编码或快递员编码不能为空').css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', '#eb9e26').show();
                                });
                            }
                            break;
                        case 'FENGWANG': // 丰网
                            obj.Username = parentDiv.find('.data-username').val();
                            obj.Password = parentDiv.find('.data-password').val();
                            obj.Custid = parentDiv.find('.data-custid').val();
                            if (obj.Username === '' || obj.Password === '' || obj.Custid === '') {
                                $('#netPointMsg').html('用户编码、用户名称或加密秘钥不能为空').css('color', 'red').show();
                            } else {
                                $('#netPointMsg').hide();
                                // 保存
                                that.saveBranchSetting(obj, function() {
                                    $('#netPointMsg').html('保存成功').css('color', '#eb9e26').show();
                                });
                            }
                            break;
                        default:
                            break;
                    }
                });
            }
        };

        // 加载京广快递网点电子面单的设置html
        dom.qlrr.loadJGSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置京广快递电子面单账号信息</h2>\
                                    <div class="dyset_box_part">'
                + '<p><span  style="letter-spacing: 1px;display:inline-block;width:80px;">客 户 编 码</span><input type="text" class="input_text data-customName" style="width: 146px;" value="' + (model.Data.Username == null ? '' : model.Data.Username) + '"></p>'
                + '<p><span  style="letter-spacing: 1px;display:inline-block;width:80px;">快递员编码</span><input type="text" class="input_text data-customPassword" style="width: 146px;" value="' + (model.Data.Password == null ? '' : model.Data.Password) + '"></p>'
                + '</div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">客户编码不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载承诺达网点电子面单的设置html
        dom.qlrr.loadCNDSettingHtml = function(model, exCode) {
            const advancedSet = JSON.parse(sessionStorage.getItem('advancedSet'));
            const isCnd = domData.currentTempInfo.ModeListShow.isCnd;
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            if (advancedSet['cndYtReache'] == 1) {
                if (isCnd) {
                    netPointHtml += '<div class="dyfs-xz-box"><label style="color:#fff;"><input checked type="checkbox" class="input_radio reachableService">使用承诺达（樱桃项目）地址可达判断服务</label></div>';
                } else {
                    netPointHtml += '<div class="dyfs-xz-box"><label style="color:#fff;"><input type="checkbox" class="input_radio reachableService">使用承诺达（樱桃项目）地址可达判断服务</label></div>';
                }
            }
            netPointHtml += '<h2>设置承诺达特快电子面单账号信息</h2>\
                                    <div class="dyset_box_part">\
                                        <p>商家代码<input type="text" class="input_text data-userName" value="' + (model.Data.Username == null ? '' : model.Data.Username) + '"></p>\
                                        <p>商家密钥<input type="text" class="input_text data-password" value="' + (model.Data.Password == null ? '' : model.Data.Password) + '"></p>\<p class="f_gray">承诺达特快系统现在的接口不支持剩余单号<br>数量的显示</p>\
                                    </div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载圆通网点电子面单的设置html
        dom.qlrr.loadYTOSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置圆通电子面单账号信息</h2>\
                                    <div class="dyset_box_part">\
                                        <p>商家代码<input type="text" class="input_text data-userName" value="' + (model.Data.Username == null ? '' : model.Data.Username) + '"></p>\
                                        <p>商家密钥<input type="text" class="input_text data-password" value="' + (model.Data.Password == null ? '' : model.Data.Password) + '"></p>\<p class="f_gray">圆通系统现在的接口不支持剩余单号<br>数量的显示</p>\
                                    </div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载韵达网点电子面单的设置html
        dom.qlrr.loadYUNDASettingHtml = function(model, exCode) {
            const temp = model.Data;

            // 处理区域一 区域二的选中状态和文字信息 [fh][memo]&123
            let area1_fh = ''; let area1_memo = ''; let area1_msg = ''; let area2_fh = ''; let area2_memo = ''; let area2_msg = ''; let area1_ww = ''; let area2_ww = ''; let area1_custom = temp.Area1_customText == null ? '' : temp.Area1_customText; let
area2_custom = temp.Area2_customText == null ? '' : temp.Area2_customText;// 区域二自定义内容

            if (temp.Area1 != null) {
                if (temp.Area1.indexOf('[fh]') > -1) {
                    area1_fh = ' checked';
                }
                if (temp.Area1.indexOf('[memo]') > -1) {
                    area1_memo = ' checked';
                }
                if (temp.Area1.indexOf('[msg]') > -1) {
                    area1_msg = ' checked';
                }
                if (temp.Area1.indexOf('[ww]') > -1) {
                    area1_ww = ' checked';
                }
            }
            if (temp.Area2 != null) {
                if (temp.Area2.indexOf('[fh]') > -1) {
                    area2_fh = ' checked';
                }
                if (temp.Area2.indexOf('[memo]') > -1) {
                    area2_memo = ' checked';
                }
                if (temp.Area2.indexOf('[msg]') > -1) {
                    area2_msg = ' checked';
                }
                if (temp.Area2.indexOf('[ww]') > -1) {
                    area2_ww = ' checked';
                }
            }


            // 新建一个韵达的层
            const yundaHtml = '<h4>1. 设置 韵达电子面单 帐号及发件人信息：</h4>\
                <ul>\
                    <li>二维码帐号：<input type="text" class="input_text data-userName" value="' + temp.Username + '">&nbsp;&nbsp;接口密码：<input type="text" class="input_text data-password" value="' + temp.Password + '"></li>\
                    <li>姓名：<input type="text" class="input_text data-fName" value="' + temp.F_name + '">&nbsp;&nbsp;电话：<input type="text" class="input_text data-fTel" value="' + (temp.F_tel == null ? '' : temp.F_tel) + '">&nbsp;&nbsp;手机：<input type="text" class="input_text data-fMobile" value="' + (temp.F_mobile == null ? '' : temp.F_mobile) + '">&nbsp;&nbsp;公司：<input type="text" class="input_text data-fCom" value="' + (temp.F_com == null ? '' : temp.F_com) + '"></li>\
                    <li>地址：<input type="text" class="input_text kddset_yunda_w1 data-fs" value="' + temp.F_s + '"> <input type="text" class="input_text kddset_yunda_w1 data-fc" value="' + temp.F_c + '"> <input type="text" class="input_text kddset_yunda_w1 data-fq" value="' + (temp.F_q == null ? '' : temp.F_q) + '"> <input type="text" class="input_text kddset_yunda_w2 data-fAddr" value="' + temp.F_addr + '"> &nbsp;&nbsp;邮编：<input type="text" class="input_text kddset_yunda_w1 data-fZip" value="' + temp.F_zip + '"></li>\
                </ul>\
                <h4>2. 设置在电子面单上打印的附加信息：</h4>\
                <ul>\
                    <li>区域一：<label><input type="checkbox" class="input_check area1_chk" ' + area1_fh + ' value="[fh]">发货信息</label>&nbsp;&nbsp;<label><input type="checkbox" class="input_check area1_chk" ' + area1_memo + ' value="[memo]">卖家备注</label>&nbsp;&nbsp;<label><input type="checkbox" class="input_check area1_chk" ' + area1_msg + ' value="[msg]">买家留言</label>&nbsp;&nbsp;<label><input type="checkbox" class="input_check area1_chk" ' + area1_ww + ' value="[ww]">买家旺旺</label>&nbsp;&nbsp;<label><input type="text" class="input_text data-area1-custom" value="' + (area1_custom == '' ? '' : area1_custom) + '" placeholder="自定义内容"></label></li>\
                    <li>区域二：<label><input type="checkbox" class="input_check area2_chk" ' + area2_fh + ' value="[fh]">发货信息</label>&nbsp;&nbsp;<label><input type="checkbox" class="input_check area2_chk" ' + area2_memo + ' value="[memo]">卖家备注</label>&nbsp;&nbsp;<label><input type="checkbox" class="input_check area2_chk" ' + area2_msg + ' value="[msg]">买家留言</label>&nbsp;&nbsp;<label><input type="checkbox" class="input_check area2_chk" ' + area2_ww + ' value="[ww]">买家旺旺</label>&nbsp;&nbsp;<label><input type="text" class="input_text data-area2-custom" value="' + (area2_custom == '' ? '' : area2_custom) + '" placeholder="自定义内容"></label></li>\
                </ul>\
                <p class="kddset_yunda_note"><span class="f_green">说明：</span>1.电子面单需要当地快递网点给分配帐号和密码后才可以使用<br>　　　2.模板的样子是由网点给确定好的，不能自己调整<br>　　　3.附加信息区域一和区域二的打印位置由网点给设置的模板来控制</p>\
                <div class="kddset_yunda_btn"><a href="javascript:void(0)" class="expr_btn_js netPointSave" data-exCode="' + exCode + '" style="width: 58px;">保 存</a><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p></div>';
            domData.yundaDiv.html(yundaHtml);
        };

        // 加载百世汇通网点电子面单的设置html
        dom.qlrr.loadHTKYSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置百世汇通面单账号信息</h2>\
                                    <div class="dyset_box_part">\
                                        <p>站点编号<input type="text" class="input_text data-userName" value="' + model.Data.Username + '"></p>\
                                        <p>商家密码<input type="text" class="input_text data-password" value="' + model.Data.Password + '"></p>\<p class="f_gray">(请填写百世汇通大客户系统中的账号和密码)</p>\
                                    </div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载申通网点电子面单的设置html
        dom.qlrr.loadSTOSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置申通电子面单账号信息</h2>\
                                    <div class="dyset_box_part">\
                                        <p>客户简称<input type="text" class="input_text data-userName" value="' + model.Data.Username + '" style="width: 146px;"></p>\
                                        <p>网点名称<input type="text" class="input_text data-custid" style="width: 146px;" value="' + (model.Data.Custid || '') + '"></p>\
                                        <p>密　　码<input type="text" class="input_text data-password" value="' + (model.Data.Password || '') + '" style="width: 146px;"></p>\<p class="f_gray">请填写申通大客户系统中的客户简称<br />网点名称、密码</p>\
                                    </div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载中通网点电子面单的设置html
        dom.qlrr.loadZTOSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置中通电子面单账号信息</h2>\
                                    <div class="dyset_box_part">\
                                        <p><span  style="letter-spacing: 4px;">用户名</span><input type="text" class="input_text data-userName" value="' + model.Data.Username + '" style="width: 146px;" ></p>\
                                        <p>密　　码<input type="text" class="input_text data-password" value="' + model.Data.Password + '" style="width: 146px;" ></p>\<p class="f_gray">(请填写中通大客户系统中的账号和密码)</p>\
                                    </div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载中通国际直邮面单的设置html
        dom.qlrr.loadZTOGJSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置中通国际直邮电子面单账号信息</h2>\
                                    <div class="dyset_box_part">\
                                        <p><span  style="letter-spacing: 1px;display:inline-block;width:54px;">客户 ID</span><input type="text" class="input_text data-custid" value="' + (model.Data.Custid == null ? '' : model.Data.Custid) + '" style="width: 146px;" ></p>\
                                        <p><span  style="letter-spacing: 1px;display:inline-block;width:54px;">合作商ID</span><input type="text" class="input_text data-userName" value="' + model.Data.Username + '" style="width: 146px;" ></p>\
                                        <p><span  style="letter-spacing: 1px;display:inline-block;width:54px;">密码</span><input type="text" class="input_text data-password" value="' + model.Data.Password + '" style="width: 146px;" ></p>\<p class="f_gray">(请填写中通大客户系统中的账号和密码)</p>\
                                    </div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载天天网点电子面单的设置html
        dom.qlrr.loadTTKDEXSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置天天电子面单账号信息</h2>\
                                    <div class="dyset_box_part">\
                                        <p>合作网点<input type="text" class="input_text data-custid" value="' + (model.Data.Custid == null ? '' : model.Data.Custid) + '" style="width: 146px;" ></p>\
                                        \<p>客户名称<input type="text" class="input_text data-userName" value="' + model.Data.Username + '" style="width: 146px;" ></p>\
                                        <p>客户密码<input type="text" class="input_text data-password" value="' + model.Data.Password + '" style="width: 146px;" ></p>\<p class="f_gray">请填写天天网中的合作网点、客户名称、<br>客户密码</p>\
                                    </div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载顺丰网点电子面单的设置html
        dom.qlrr.loadSFSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            let flag = true;
                 let isCusId = true;
            if (domData.currentTempInfo.ModeAdvancedServices != null) {
                $.each(domData.currentTempInfo.ModeAdvancedServices, function(i, v) {
                    if (v.serviceKey == 'zmj') {
                        flag = false;
                        return false;
                    }
                });
                $.each(domData.currentTempInfo.ModeServiceItems, function(_i, _v) {
                    if (_v.Defaultval.indexOf('现结') > -1) {
                        isCusId = false;
                        return false;
                    }
                });
            }
            if (flag) {
                netPointHtml += dom.qlrr.getConcatFhdHtml();
            }
            netPointHtml += '<h2>设置顺丰电子面单账号信息</h2>\
                        <div class="dyset_box_part">\
                            <p>客户编码<input type="text" class="input_text data-userName" style="width: 146px;" value="' + model.Data.Username + '"></p>\
                            <p>校验字段<input type="text" class="input_text data-password" style="width: 146px;" value="' + model.Data.Password + '"></p>\
                            <p class="f_gray">请填写顺丰大客户系统中的客户编码<br>和校验字段</p>'
                + '<p>月结卡号<input type="text"  ' + (!isCusId ? 'disabled' : '') + ' class="input_text data-custid" style="width: 146px;" value="' + (model.Data.Custid == null ? '' : model.Data.Custid) + '"></p>'
                + '</div>'
                + '<div class="add_line_btn">'
                + '<input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>'
                + '</div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载德邦网点电子面单的设置html
        dom.qlrr.loadDBKDSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置德邦电子面单账号信息</h2>\
                                    <div class="dyset_box_part">'
                + '<p>月结卡号<input type="text" class="input_text data-custid" style="width: 146px;" value="' + (model.Data.Custid == null ? '' : model.Data.Custid) + '"></p>'
                + '</div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">月结卡号不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        dom.qlrr.getWDCommonHTML = function(exCode, exName, data, nameArr) {
            const _html = '<h2>设置' + exName + '电子面单账号信息</h2>'
                + '<div class="dyset_box_part">'
                + '<p><span class="name">' + nameArr[0].name + '</span><input type="text" class="input_text data-userName" value="' + (data.Username || '') + '" style="width: 146px;" ></p>'
                + ((nameArr[1] || {}).name ? '<p><span class="name">' + nameArr[1].name + '</span><input type="text" class="input_text data-password" style="width: 146px;" value="' + (data.Password || '') + '"></p>' : '')
                + ((nameArr[2] || {}).name ? '<p><span class="name">' + nameArr[2].name + '</span><input type="text" class="input_text data-custid" ' + (nameArr[2].isDisabled ? 'disabled' : '') + ' style="width: 146px;" value="' + (data.Custid || '') + '"></p>' : '')
                + '</div>'
                + '<div class="add_line_btn">'
                + '<input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">客户编码不能为空</span></p>'
                + '</div>';
            return _html;
        };

        // 加载安能网点电子面单的设置html
        dom.qlrr.loadANSettingHtml = function(model, exCode) {
            let netPointHtml = '';
                let isCusId;
                let _service;
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            if (exCode == 'DISTRIBUTOR_12017865') {
                netPointHtml += '<h2>设置安能电子面单账号信息</h2>\
                                    <div class="dyset_box_part">\<p>客户编码<input type="text" class="input_text data-userName" value="' + model.Data.Password + '" style="width: 146px;" ></p>\
                                        \<p class="f_gray">（请填写安能网点的客户编码）</p>\
                                    </div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">客户编码不能为空</span></p>\
                                    </div>';
            } else if (exCode == 'UAPEX') {
                netPointHtml += '<h2>设置全一电子面单账号信息</h2>'
                    + '<div class="dyset_box_part">'
                    + '<p>客户标识<input type="text" class="input_text data-userName" value="' + model.Data.Username + '" style="width: 146px;" ></p>'
                    + '<p>客户密钥<input type="text" class="input_text data-password" style="width: 146px;" value="' + model.Data.Password + '"></p>'
                    + '<p>客户编码<input type="text" class="input_text data-custid" style="width: 146px;" value="' + model.Data.Custid + '"></p>'
                    + '</div>'
                    + '<div class="add_line_btn">'
                    + '<input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">客户编码不能为空</span></p>'
                    + '</div>';
            } else if (exCode == 'SURE') {
                netPointHtml += dom.qlrr.getWDCommonHTML(exCode, '速尔', model.Data, [{ name: '客户编码' }, { name: '仓库代码' }, { name: '取件员' }]);
            } else if (exCode == 'CN7000001003751') {
                _service = domData.currentTempInfo.ModeServiceItems;
                $.each(_service, function(_i, _v) {
                    if (_v.Itemcode === 'ali_waybill_ext_payment_type' && /^(寄付|转第三方付款)/.test(_v.Defaultval)) {
                        isCusId = true;
                        return false;
                    }
                });
                netPointHtml += dom.qlrr.getWDCommonHTML(exCode, '跨越', model.Data, [{ name: '客户编码' }, {}, { name: '月结卡号', isDisabled: !isCusId }]);
            } else if (exCode == 'ANEKY') {
                netPointHtml += dom.qlrr.getWDCommonHTML(exCode, '安能快运', model.Data, [{ name: '客户编码' }, { name: '客户秘钥' }]);
            } else if (exCode == 'DISTRIBUTOR_13413991') {
                netPointHtml += dom.qlrr.getWDCommonHTML(exCode, '安鲜达', model.Data, [{ name: '货主编码' }]);
            }

            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').attr('checked', 'checked');
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };


        // 加载京东网点电子面单的设置html
        dom.qlrr.loadJDSettingHtml = function(model, exCode, exid) {
            let netPointHtml = '';
                 let _isHasJdShopCode = comp.Print.configFunc('JDShopCode');
            const options = {
                province: model.Data.F_s,
                city: model.Data.F_c,
                district: model.Data.F_q,
            };
            // 京东先不接交替打印
            // netPointHtml +=  dom.qlrr.getConcatFhdHtml();

            if (_isHasJdShopCode) {
                netPointHtml += '<div class="mb_20"><h2 class="pb_0">设置店铺代码和账户信息</h2>'
                    + (/^(11009|11048|11081)$/.test(exid) ? '' : '<input type="text" placeholder="输入京东快递助手的店铺代码" class="input_text data-jdShopCode" style="width:89%" value="' + (model.Data.JdShopCode || '') + '"></div>');
            } else {
                netPointHtml += '<h2>设置京东电子面单账号信息</h2>';
            }

            netPointHtml += '<div class="dyset_box_part">\
                                        <p>客户编码：<input type="text" class="input_text data-userName" value="' + (model.Data.Username == null ? '' : model.Data.Username) + '"></p>';

            if (exid == 999) {
                netPointHtml += '<p>发货仓库：<input type="text" class="input_text data-password" placeholder="选填，仅用于提示送达" value="' + (model.Data.Password == null ? '' : model.Data.Password) + '"></p>\
                                        <p>商家地址：</p>\
                                        <div class="J_Send_Area"></div>\
                                        <div class="clearfix"></div>\
                                    </div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>\
                                    </div>';
            } else if (/^(998|997|7100|11005|11008|11009|11048|11081)$/.test(exid)) {
                netPointHtml += '<p>发货仓库：<input type="text" class="input_text data-password"  value="' + (model.Data.Password == null ? '' : model.Data.Password) + '">&nbsp;&nbsp;<a onclick="alert(\'京东发货仓库在此模板上为必填，发货仓库编码需要您咨询与您签订合同的京东业务员（不是快递小哥），由他们进行配置后您再填入。如无法获取，可暂时更换为京东的另外一个模板，更多帮助请咨询QQ群181486426（仅针对此问题）\')" style="color: #f39f27;cursor: pointer;text-decoration: none;">(?)</a></p>'
                    + '<p>商家地址：</p>'
                    + '<div class="J_Send_Area"></div>'
                    + (/^(11009|11048|11081)$/.test(exid) ? '<p style="margin-top:10px"><label class="label-info ">详细地址:</label> <input type="text" class="input_text data-address" value="' + model.Data.F_addr + '"></input></p>' : '')
                    + '<div class="clearfix"></div>'
                    + ' </div>'
                    + '<div class="add_line_btn">'
                    + '<input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户名或密码不能为空</span></p>'
                    + '</div>';
            }

            if (_isHasJdShopCode) {
                netPointHtml += '<div class="dyset_box_part mt_10">'
                    + '<h2 class="pb_0">操作说明：<a href="//helptb.kuaidizs.cn/helpMap/getDetail?detailId=395" class="info-msg" target="_blank">查看详细使用教程</a></h2>'
                    + '</div>';
            }
            domData.netPointDiv.html(netPointHtml);

            const _isConcatFhd = domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd;
            const styleId = comp.Print.Data.currentTempInfo.ModeList.StyleId;
            const isYilianTemp = comp.base.isYilianTemp(styleId);
            if (isYilianTemp) {
                if (_isConcatFhd == 1 && !comp.Print.Data.hasNoRM_yilian) {
                    $('.concatFhd').prop('checked', true);
                    $('.concatFhdTips').show();
                } else {
                    $('.concatFhd').removeAttr('checked');
                    $('.concatFhdTips').hide();
                }
            } else if (_isConcatFhd == 1 && !comp.Print.Data.hasNoRM_common) {
                    $('.concatFhd').prop('checked', true);
                    $('.concatFhdTips').show();
                } else {
                    $('.concatFhd').removeAttr('checked');
                    $('.concatFhdTips').hide();
                }

            const funArr = comp.Print.eventObj.getAddrDropdown;
            if (funArr) {
                funArr.each(function(index, item) {
                    item(domData.netPointDiv, options); // 加入地址级联下拉框
                });
            }
        };

        // 加载极兔速度网点电子面单设置 html
        dom.qlrr.loadJTSDSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置极兔电子面单账号信息</h2>\
                                    <div class="dyset_box_part">'
                + '<p>客户编号<input type="text" class="input_text data-custid" style="width: 146px;" value="' + (model.Data.Username == null ? '' : model.Data.Username) + '"></p>'
                + '<p>密　　码<input type="text" class="input_text data-password" value="' + model.Data.Password + '" style="width: 146px;" ></p>'
                + '</div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">客户编号或密码不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        // 加载众邮网点电子面单设置 html
        dom.qlrr.loadZYKDSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置众邮网点面单账号信息</h2>\
                                    <div class="dyset_box_part">'
                + '<p><span  style="letter-spacing: 1px;display:inline-block;width:100px;">用户账号</span><input type="text" class="input_text data-username" style="width: 130px;" value="' + (model.Data.Username == null ? '' : model.Data.Username) + '"></p>'
                + '<p><span  style="letter-spacing: 1px;display:inline-block;width:100px;">用户名称</span><input type="text" class="input_text data-password" value="' + model.Data.Password + '" style="width: 130px;" ></p>'
                + '<p><span  style="letter-spacing: 1px;display:inline-block;width:100px;">寄件网点编码</span><input type="text" class="input_text data-custid" style="width: 130px;" value="' + (model.Data.Custid == null ? '' : model.Data.Custid) + '"></p>'
                + '</div>\
                                    <div class="add_line_btn">\
                                        <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户账号不能为空</span></p>\
                                    </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };

        dom.qlrr.loadNotYetTipsHtml = (model, exCode) => {
            let netPointHtml = '';
            netPointHtml += `
                <div class="notYet_tips_block" >
                    <h2>当前快递尚未对接，敬请期待</h2>
                </div>
            `;
            domData.netPointDiv.html(netPointHtml);
        };

        // 加载丰网速运网点电子面单的设置html
        dom.qlrr.loadFWSYSettingHtml = function(model, exCode) {
            let netPointHtml = '';
            netPointHtml += dom.qlrr.getConcatFhdHtml();
            netPointHtml += '<h2>设置丰网速运网点面单账号信息</h2>\
                <div class="dyset_box_part">'
                + '<p>用户名称<input type="text" class="input_text data-username" style="width: 146px;" value="' + (model.Data.Username == null ? '' : model.Data.Username) + '"></p>'
                + '<p>密　　码<input type="text" class="input_text data-password" value="' + model.Data.Password + '" style="width: 146px;" ></p>'
                + '</div>\
                <div class="add_line_btn">\
                    <input type="button" value="保存" class="expr_btn_orange netPointSave" data-exCode="' + exCode + '"><p><span style="display:none;color:red;" id="netPointMsg">用户账号不能为空</span></p>\
                </div>';
            domData.netPointDiv.html(netPointHtml);

            if (domData.currentTempInfo.ModeTempPrintcfg.IsConcatFhd == 1) {
                $('.concatFhd').prop('checked', true);
                $('.concatFhdTips').show();
            } else {
                $('.concatFhd').removeAttr('checked');
                $('.concatFhdTips').hide();
            }
        };


        // 绘制模版
        dom.qlrr.draw = function() {
            const pdata = comp.Print.Data;
            if (pdata.isSinglePrint && (domData.currentTemp.KddType > 1 || domData.currentTemp.Exid == 900)) {
                domData.lodopDomParent.myHide();
                if (pdata.userVersion === 'trial' && pdata.platform === 'tb') { // TODO
                    Tatami.pub('Tatami.clickPoint.manualTrigger', {
                        point: '11194.11200.13968.13969',
                        _fm: '3218',
                    });
                }
                dom.find('#DandadianziDiv').myShow();
                domData.yundaDiv.myHide();
                dom.find('.kdddy_group_btn').myHide();
                dom.find('.goods_net_right').myHide();
                return;
            }
            dom.find('#DandadianziDiv').myHide();
            if (domData.currentTempInfo.ModeList.Excode == 'YUNDA' && domData.currentTempInfo.ModeList.KddType == 2) {
                dom.find('.kdddy_group_btn').myHide();
            } else {
                dom.find('.kdddy_group_btn').myShow();
            }
            if (!domData.isYunDa) {
                domData.lodopDomParent.myShow();
                _printTemplateObj = that.drawHtml({
                    templateData: domData.currentTempInfo,
                    globalSetting: printData.globalSettingKdd,
                    exuid: printData.exuid,
                    // isEdit: false,
                    viewMode: comp.Print.Data.isSinglePrint ? '2' : '0',
                    mockInputData: domData.tempData,
                });
                domData.lodopDomParent.html(_printTemplateObj.showTemplate());
            }
        };

        // 取消SaveTag
        dom.qlrr.cannelSaveTag = function() {
            dom.find('.pup_menu_box').show();
            dom.find('.pup_tzsx_box').hide();
            dom.find('.delete_mid_icon').show();
        };

        // 关闭
        dom.qlrr.close = function() {
            ((mergeCacheObj || {}).dom || {}).sellerData = null; // 关闭清除掉的缓存数据
            that.closeDialog(domid);
        };

        // 向前一页
        dom.qlrr.tagPagePrev = function() {
            if (domData.pageIndex > 0) {
                domData.pageIndex--;
                dom.qlrr.showTagPage();
            }
        };

        // 向后一页
        dom.qlrr.tagPageNext = function() {
            const arr = printData.kddTemplates;
            const pageCount = Math.ceil(arr.length / tagCount);
            if (domData.pageIndex < (pageCount - 1)) {
                domData.pageIndex++;
                dom.qlrr.showTagPage();
            }
        };

        // 翻到最前页
        dom.qlrr.tagPageFirst = function() {
            domData.pageIndex = 0;
            dom.qlrr.showTagPage();
        };

        // 翻到最后页
        dom.qlrr.tagPageLast = function() {
            const arr = printData.kddTemplates;
            const pageCount = Math.ceil(arr.length / tagCount);
            domData.pageIndex = pageCount - 1;
            dom.qlrr.showTagPage();
        };


        // saveTag
        dom.find('.expr_btn_js').click(function() {
            const parent = dom.find('.pup_tzsx_list');
            const obj = {};
            obj.DelIds = [];
            obj.SortTempList = [];
            obj.ExUserId = printData.exuid;
            let sortId = 1;
            parent.find('li').each(function(ind, inv) {
                const dli = $(inv);
                const item = dli.data('data');
                const item_id = item.id;
                if (dli.css('display').toLowerCase() == 'none') {
                    obj.DelIds.push(item_id);
                } else {
                    const tem = {};
                    tem.SortId = sortId;
                    tem.Id = item_id;
                    obj.SortTempList.push(tem);
                    sortId++;
                }
            });
            const temDom = parent.find('li');
            if (temDom.length == obj.DelIds.length) {
                Tatami.showFail('不能删除所有快递组!');
                return;
            }

            const tarDom = $(this);
            if (tarDom.find('i').length > 0) {
                return;
            }
            tarDom.html('<i class="expr_yellow_loading"></i>保存中');
            that.saveSortKdd(obj, function() {
                tarDom.html('保存修改');
                dom.qlrr.cannelSaveTag();
                dom.qlrr.initTabs(domData.defId);
                // 删除模版缓存的打印机
                removePrintersStorage(obj.DelIds, true);

                // const funcHook = (window.printAPI.compHookObj || {}).afterChangedKddTemp ;
                // funcHook && funcHook();
            }, function(msg) {
                Tatami.showFail('保存失败,请重试!');
                tarDom.html('保存修改');
            });
        });

        // Tag Cannel
        dom.find('.expr_btn_gray').click(function() {
            dom.qlrr.cannelSaveTag();
        });

        // Tag Sort
        dom.find('.pad_r.kdd').click(function() {
			if( comp.Print.chenkAuthor()) return

            // const funcHook = (window.printAPI.compHookObj || {})
            // .operatingRecord;
            // funcHook && funcHook('订单_订单打印_快递单设置_调整顺序');

            const parent = dom.find('.pup_tzsx_list');
            parent.empty();
            $.each(printData.kddTemplates, function(ind, inv) {
                if (inv.id != '-900') {
                    $('<li/>').appendTo(parent).html(`<div class="temp_text">${inv.groupName}</div>`).data('data', inv)
.attr('title', inv.groupName);
                }
            });
            $("#groupTempSortable").sortable({ containment: "parent", cursor: "move" }, {
                start(event, ui) {
                    const current = $(event.target).find('.cur');
                    const li = $(ui.item);
                    const data = li.data('data');
                    if (current.length > 0) {
                        const currentData = current.data('data');
                        current.empty().removeClass('cur').html(`<div class="temp_text">${currentData.groupName}</div>`);
                    }
                    const lihtml = `<div class="temp_text">${data.groupName}</div><i class="delete_icon_little"></i>`;
                    if (data?.id > 0)li.html(lihtml);
                    li.addClass('cur');
                    const delico = li.find('.delete_icon_little');
                    delico?.click(function() {
                        const parent = $(this).parent();
                        parent.hide(200);
                    });
                },
stop(event, ui) { console.count("拖拽结束"); }
              });
            const lis = parent.find('li');
            lis.click(function() {
                const li = $(this);
                const data = li.data('data');
                const current = parent.find('.cur');
                if (current.length > 0) {
                    const currentData = current.data('data');
                    current.empty().removeClass('cur').html(`<div class="temp_text">${currentData.groupName}</div>`);
                }
                li.addClass('cur');
                const lihtml = `<div class="temp_text">${data.groupName}</div><i class="delete_icon_little"></i>`;
                if (data?.id > 0)li.html(lihtml);
                const delico = li.find('.delete_icon_little');
                delico.click(function() {
                    const parent = $(this).parent();
                    parent.hide(200);
                });
            });
            if (lis.length > 0) {
                $(lis[0]).click();
            }
            dom.find('.pup_menu_box').hide();
            dom.find('.pup_tzsx_box').show();
            dom.find('.delete_mid_icon').hide();
        });

        // 全局设置
        dom.find('.no_radius.pad_l.kdd').click(function() {
			if( comp.Print.chenkAuthor()) return
            if (comp.Print.Data.platform === 'fxg') {
                Tatami.pub('Tatami.clickPoint.manualTrigger', {
                    'point': '24629.45235.50491.50492.56296.56299',
                    '_fm': '10195',
                });
            }

            dom.qlrr.close();
            resObj.setGolbalSet(function() {
                resObj.showkddMain(domData.defId, domData.printDatas, sellerData);
            });
        });


        // 模板的「现在打印」淘宝版预览界面
        dom.qlrr.showPreview = function(previewUrl, _cloudParam, clientType) {
            // 预览弹窗
            const _html = '<div class="previewCloudContent" style="overflow:auto;">'
                + '<p style="position: absolute;font-size: 18px; color: #3C3C3C; line-height: 20px; margin: 19px 0 0 24px;font-weight: bold;">打印预览</p>'
                + '<div style="position: absolute; font-size: 18px; right: 0; margin: 19px 0 0 24px;">'
                + '<button class="ok" style="color: #ffffff; background-color: #f5821f; border: 1px solid #f5933f; cursor: pointer; border-radius: 3px; margin-right: 10px; padding: 3px 12px; text-align: center; min-width: 40px; line-height: 20px;">打印</button>'
                + '<button class="cancel" style="border: 1px solid #b4b4b4;cursor: pointer;border-radius: 3px; margin-right: 10px; padding: 3px 12px; text-align: center; min-width: 40px; line-height: 20px;background-color: #F3F3F3; ">取消</button>'
                + '</div>'
                + '<div id="previewCloud" style=" margin-top:19px;">'
                + '<img  id="previewCloud_img" src="' + previewUrl + '"   style="display:block;margin:0 auto;">'
                + '</div>'
                + '<p style="text-align:center;color:#6C6C6C;margin-bottom:50px;"> * 普通面单预览，可能有偏差，以实际打印为准</p>'

                + '</div>';
            let _w; let
_h;
            _w = $(window).width() - 300 > 880 ? $(window).width() - 300 : 880;
            _h = $(window).height() - 100 > 630 ? $(window).height() - 100 : 630;
            window.dialog.show({
                width: _w,
                height: _h,
                content: _html,
                notHasMask: false,
                isNoPadding: true,
                hideFooter: true,
                okName: '打印',
                shown($dialog) {
                    previewCloud_img.onload = function() {
                        Tatami.clearShow('print');
                        const image = $dialog.find('#previewCloud img');
                            const image_h = image.height();
                            const image_w = image.width();

                        if (image_w > image_h) {
                            const w = ($(window).width() - 100 > 880 ? $(window).width() - 100 : 880) * 2 / 3; // 取当前 dialog 宽度的 2/3
                            image.attr('width', w < 645 ? 645 : w);
                            image.attr('height', 'auto');
                            image.css('margin-top', '90px');
                        } else {
                            const h = ($(window).height() - 100 > 630 ? $(window).height() - 100 : 630) - 50; // 取当前 dialog 高度-50
                            // image.attr('width',298);
                            image.attr('width', 'auto');
                            image.attr('height', h);
                        }
                        // 如果快递单过高，补偿一下 dialog container 的高度
                        const $container = $dialog.find('.ctrl-dialog-container');
                        const $content = $dialog.find('.ctrl-dialog-content');
                        if ($content.height() > $container.height()) {
                            $container.height($content.height());
                        }

                    };
                    $dialog.find('.ok').click(function() {
                        _cloudParam.hasView = false;
                        if (clientType == 'cainiao') {
                            comp.print.cloudprint.printTemplate(_cloudParam);
                        } else {
                            comp.print.pddcloudprint.printTemplate(_cloudParam);
                        }
                    });
                    $dialog.find('.cancel').click(function() {
                        $dialog.off('click').remove();
                    });

                },
            });
        };

        // 现在打印
        dom.find('.btn_xzdy_yellow_big').click(async function() {

            const funcHook = (window.printAPI.compHookObj || {})
                .operatingRecord;
            funcHook && funcHook('订单_订单打印_快递单设置_现在打印');

            let _d = domData.printDatas;
                 let _func;
                 let _lodop;
let _cloudParam;
let $target = $(this);
                 let _this = this;
            // 默认打印机
            const printersMap = Tatami.localcache.get('__printersMap__') || {};
            const id = `group${domData?.currentTemp?.id}`;
            const defPrinter = printersMap[id] || domData?.currentTemp?.defaultPrinter || dom.find('.btn_set_printer').text();

            // 拼多多控件打印预览
            const kddType = domData.currentTempInfo?.ModeListShow?.KddType;
            if (kddType == '7') {
                // if($target.prop('lock') == true) return;
                // $target.prop('lock', true);

                comp.Print.checkPddCloudPrint(async function(install) {
                    if (!install.isSupport) {
                        // 接口获取拼多多下载链接
                        const isPdd = ['pdd'].includes(comp.Print.Data.platform);
                        if (isPdd) {
                            // 获取拼多多下载链接
                            const resultData = await get('get_pdd_downloadurl', { params: {} });
                            pddDownloadUrl = resultData?.data?.pddDownloadUrl;
                            comp.print.data.pddDownloadUrl = resultData?.data?.pddDownloadUrl;
                        }
                        $(_this).text('未安装控件').attr('disabled', false);
                        resObj.installDialog('pdd');
                        // $target.prop('lock', false);
                        return;
                    }
                    comp.print.pddcloudprint.getAgentInfo(function(version) {
                        console.log(version);
                        if (version && window.tplHelp.compareVer(version, '1.2.51')) {
                            // 在此添加打印必须字段，来自模拟数据

                            // 拼外才需要 shopName 字端，目前只有自助版才有这个逻辑
                            if (!['pdd', 'erp'].includes(comp.Print.Data.platform)) {
                                var shopName = dom.find('.yzShopName .info-msg').data('ww'); // 只为了获取预览图片，能用的就行，不是默认网点的也行
                                const pwAuthorizeLink = comp.Print.getShouQuanUrl(kddType);
                                const _html = '<div style="margin-top:30px;">'
                                    + '<div class="content-box"> <h1 class="warn_title"> <span class=""></span> 打印该模板需要先授权 </h1>'
                                    + '<p class="pl_50" style="line-height:1.5;font-family: Microsoft Yahei, Verdana, sans-serif;">为了保证拼多多电子面单正常预览，需您先 <a href="' + pwAuthorizeLink + '" target="_blank">点击授权</a> ，授权完成后即可预览该模板。如有问题，请联系客服。</p>'
                                    + '</div>'
                                    + '</div>';
                                // 如果没有取到 shopName ，就无法获取对应的模板url，就不让用户预览
                                if (!shopName) {
                                    window.dialog.show({
                                        width: 500,
                                        height: 220,
                                        content: _html,
                                        notHasMask: true,
                                        isNoPadding: true,
                                        okName: '去授权',
                                        okCb() {
                                            window.open(pwAuthorizeLink);
                                        },
                                    });
                                    // $target.prop('lock', false);
                                    return;
                                }
                            }

                            const pddPlat = window.erpData?.shopList.filter((i) => i.platform === 'pdd') || [];

                            comp.print.data.getPrintPddContentData({
                                cpCode: domData.currentTempInfo?.ModeListShow?.ExCode,
                                styleId: domData.currentTempInfo?.ModeList?.StyleId,
                                sellerId: pddPlat[0]?.sellerId || '',
                                exCode: domData.currentTempInfo?.ModeListShow?.ExCode,
                                exid: domData.currentTempInfo?.ModeListShow?.Exid,
                                shopName,
                                isShowLoadding: true,
                            }, function(data) {
                                _d[0].content = data;
                                _d[0].custom = _d[0];
                                // 自定数据处理
                                _d = comp.print.data.dealWithPddData(_d, domData.currentTempInfo);
                                _cloudParam = {
                                    printDatas: _d,
                                    hasView: true,
                                    selectedPrinter: defPrinter,
                                    templateSet: domData.currentTempInfo,
                                };
                                comp.print.pddcloudprint.printTemplate(_cloudParam, null, null, null, function(result) {
                                    dom.qlrr.showPreview(result.data[0], _cloudParam, 'pdd');
                                    // $target.prop('lock', false);
                                }, null);
                            }, function() {
                                // $target.prop('lock', false);
                            });
                        } else {
                            resObj.updatePddDialog();
                        }
                    });
                });
                return;
            } else if (['5', '8', '9'].includes(kddType.toString())) {
                let installResult = await printItem.checkPrintComponentByMerge(kddType);
                if (!installResult?.isSuccess) {
                    $(_this).text('未安装控件').attr('disabled', false);
                    return;
                }
            }
            // 菜鸟控件打印预览
            if (configPlatFuncs('nowPrintCloud') && !comp.Print.Data.isSinglePrint) {
                that.creatCaiNiaoWebSocket(function(install) {
                    if (install.isSupport) {
                        _d = comp.print.data.dealWithData(domData.printDatas, domData.currentTempInfo, true);
                        // 特殊处理丰网速运网点电子面单，快递单设置中打印事件给控件的数据,获取模拟数据中的 servicetype 为 "服务类型",该数据项会在该次打印事件中展示出来
                        if (domData?.currentTemp?.ExCode === 'FENGWANG' && domData?.currentTemp?.KddType === 2) {
                            _d[0].servicetype = '';
                        }
                        _cloudParam = {
                            printDatas: _d,
                            hasView: true,
                            viewType: 'image',
                            selectedPrinter: defPrinter,
                            templateSet: domData.currentTempInfo,
                            isPrintTest: true,
                        };
                        Tatami.showLoading({
                            content: '数据加载中……',
                            key: 'print'
                        });
                        that.cloudPrintTemp(_cloudParam, null, null, null, function(info) {
                            if (!info.isSuccess) {
                                Tatami.showFail(info.message);
                            } else {
                                dom.qlrr.showPreview(info.data[0], _cloudParam, 'cainiao');
                            }
                        });
                    } else if (kddType == '3') {
                            $(_this).text('未安装控件').attr('disabled', false);
                            resObj.installDialog('cainiao');

                        } else {
                            model({
                                type: 'confirm',
                                title: '提示',
                                width: 350,
                                minHeight: 160,
                                content: '当前打印控件不支持预览打印（目前仅支持菜鸟打印控件预览）',
                                cancelHide: true
                            });
                        }
                });
                return;
            }

            // lodop控件打印预览
            _lodop = that.getHiddenLodop();

            if (!_lodop) {
                $(this).text('未安装控件').attr('disabled', false);
                const isUseNewBrowser = comp.print.lodop.getChrome();
                if (isUseNewBrowser) {
                    resObj.installClodopFor84ChromeDialog();
                } else {
                    resObj.installDialog();
                }
                return;
            }
            if (comp.Print.Data.isSinglePrint) {
                _func = (comp.Print.eventObj.nowPrint || [])[0];
                // 收集打印数据
                _d[0].gatherData = _printTemplateObj.getInputData();
                that.singleLodopPrint({
                    lodop: _lodop,
                    printData: _d[0],
                    templateSet: domData.currentTempInfo, // html收集到位置改动后的模板详情
                    printType: 'kdd',
                    hasView: false,
                    printBoxIsShow: 1,
                    selectedPrinter: defPrinter,
                }, function() {
                    _func && _func(domData.currentTempInfo.ModeListShow.Mode_ListShowId);
                });

            } else {
                that.printLodopTemplate(_lodop, _d, domData.currentTempInfo, 'kdd', true, 0, defPrinter);
            }
        });

        // 编辑模版
        dom.find('.btn_xzdy_gray.btn_edit_temp').click(function() {
			if( comp.Print.chenkAuthor()) return
            if (domData.currentTempInfo != null) {
                dom.qlrr.close();
                that.editKdd(domData.currentTempInfo, function(data) {
                    if (data) {
                        resObj.setGroupSet(null, true);
                    } else {
                        resObj.showkddMain(domData.defId, domData.printDatas, sellerData);
                    }
                });
            }
        });

        // 默认打印机
        dom.find('.select_print_box .btn_xzdy_gray').click(function() {
			if( comp.Print.chenkAuthor()) return
            const dl = dom.find('.selectPinter');
            if (dl.html()) {
                dl.show();
            }
            dom.find('.selectBindFjr').hide();
            return false;
        });

        // 新增模版
        dom.find('a.ch_add.kdd').click(function() {
			if( comp.Print.chenkAuthor()) return
            if (comp.Print.Data.platform === 'fxg') {
                Tatami.pub('Tatami.clickPoint.manualTrigger', {
                    'point': '24629.45235.50491.50492.56296.56297',
                    '_fm': '10194',
                });
            }

            dom.qlrr.close();
            that.addKdd(function(msg) {
                domData.defId = domData.defId || msg;
                resObj.showkddMain(domData.defId, domData.printDatas, sellerData);
            });
        });

        // 向前一页
        dom.find('li.prev').click(function() {
            dom.qlrr.tagPagePrev();
        });

        // 向后一页
        dom.find('li.next').click(function() {
            dom.qlrr.tagPageNext();
        });

        // 第一页
        dom.find('li.first').click(function() {
            dom.qlrr.tagPageFirst();
        });

        // 最后一页
        dom.find('li.last').click(function() {
            dom.qlrr.tagPageLast();
        });

        // 关闭
        dom.find('.delete_mid_icon').click(function() {
            dom.qlrr.close();
        });

        dom.qlrr.loadData = function() {
            const fhdTemplateList = that.getFhdTemplateList();
                const fhdList = fhdTemplateList.ModeListShows;

            comp.Print.Data.hasNoRM_common = true;
            comp.Print.Data.hasNoRM_yilian = true;

            $.each(fhdList, (index, item) => {
                if (item.Exid == 666) {
                    comp.Print.Data.hasNoRM_common = false;
                    return;
                } else if (item.Exid == 670) {
                    comp.Print.Data.hasNoRM_yilian = false;
                    return;
                }
            });
        };

        // 初次执行
        dom.qlrr.loadData();// 载入必要的数据
        dom.qlrr.initTabs(defId, printDatas);
        dom.qlrr.initPrinter();
        dom.qlrr.show();
        dom.qlrr.initSellers(sellerData, true);
        mergeCacheObj.showKddMainObj = dom;
        return dom;
    };

    resObj.installDialog = function(name) {
        let _html;
        // [临时方案]pdd下掉菜鸟链接
        const isPdd = ['pdd'].includes(comp.Print.Data.platform);
        const pddResultDownloadUrl = isPdd ? pddDownloadUrl : 'https://dl.pddpic.com/windows_dev/2022-02-08/6b2e68605a803ae23dbebfd5f7da91fa.exe';
        if (name == 'pdd') {
            _html = '<div>'
                + '<div class="content-box"> <h1 class="warn_title"> <span class=""></span> 您未安装打印控件 </h1>'
                + '<p class="pl_50">安装完成后,请确认<font class="fc-red">拼多多打印控件是否正常开启</font>，开启后请重新打印</p>'
                + '<p class="ft-center jn-download ">'
                + '<a class="jn-download-sure" href="' + pddResultDownloadUrl + '" download >立即下载</a>'
                + '<a class="jn-download-can close-btn ml_10"  href="javascript:void(0);" >取消</a>'
                + '</p>'
                + '</div>';
        } else if (name == '') {
            _html = '<div>'
                + '<div class="content-box"> <h1 class="warn_title"> <span class=""></span> 您未安装打印控件 </h1>'
                + '<p class="pl_50">安装完成后,请确认<font class="fc-red">美团打印控件是否正常开启</font>，开启后请重新打印</p>'
                + '<p class="ft-center jn-download ">'
                + '<a class="jn-download-sure" href="https://api.pinduoduo.com/api/app/v1/latest/com.xunmeng.pddprint/windows" download >立即下载</a>'
                + '<a class="jn-download-can close-btn"  href="javascript:void(0);" >取消</a>'
                + '</p>'
                + '</div>';
        } else {
            _html = '<div class="ctrl-dialog-content">'
                + '<div class="content-box"> <h1 class="warn_title"> <span class=""></span> 您未安装打印控件 </h1>'
                + '<div>'
                + '<p class="pl_50">安装完成请 <a href="javascript:void(0);" style="color: #0069ca;" data-act-name="reflash_view">刷新页面</a> 或重启浏览器</p>'
                + '<p class="ft-center jn-download ">'

                + '<a class="jn-download-sure" href= "'
                + `${isPdd ? '//static.kuaidizs.cn/noInstallClodop/CLodop_Setup_for_Win32NT_https_3.090Extend.zip' : '//static.kuaidizs.cn/notInstallCaiNiao/cainiao_downFor84Chrome.html'}`
                + '" target="_blank">立即下载</a>'
                + '<a class="jn-download-can close-btn ml_5"  href="javascript:void(0);" >取消</a>'
                + '</p>'
                + '</div></div></div>';
        }

        window.dialog.show({
            width: 500,
            height: 220,
            content: _html,
            notHasMask: true,
            hideFooter: true,
            shown($dialog) {
                $dialog.find('[data-act-name=reflash_view]').on('click', function() {
                    location.reload();
                });
            },
        });
    };
    // 点击现在打印，未安装最新版clodop控件提示
    resObj.installClodopFor84ChromeDialog = function() {
        let _html;
        // [临时方案]pdd下掉菜鸟链接
        const isPdd = window.platformConfig.platform === 'pdd';

        _html = '<div class="ctrl-dialog-content">'
            + '<div class="content-box"> <h1 class="warn_title"> <span class=""></span> 您未安装最新的打印控件 </h1>'
            + '<div>'
            + '<p class="pl_50">安装完成请 <a href="javascript:void(0);" style="color: #0069ca;" data-act-name="reflash_view">刷新页面</a> 或重启浏览器</p>'
            + '<p class="ft-center jn-download ">'
            + '<a class="jn-download-sure" href="'
            + `${isPdd ? '//static.kuaidizs.cn/noInstallClodop/CLodop_Setup_for_Win32NT_https_3.090Extend.zip' : '//static.kuaidizs.cn/notInstallCaiNiao/cainiao_downFor84Chrome.html'}`
            + '" target="_blank">立即下载</a>'
            + '<a class="jn-download-can close-btn ml_5"  href="javascript:void(0);" >取消</a>'
            + '</p>'
            + '</div></div></div>';
        window.dialog.show({
            width: 500,
            height: 220,
            content: _html,
            notHasMask: true,
            hideFooter: true,
            shown($dialog) {
                $dialog.find('[data-act-name=reflash_view]').on('click', function() {
                    location.reload();
                });
            },
        });
    };
    // 点击现在打印，拼多多模版预览提示
    resObj.updatePddDialog = function() {
        // 拼多多使用链接下载，其他平台写死
        const isPdd = ['pdd'].includes(comp.print.data.platform);
        const pddDownloadUrl = isPdd ? comp.print.data.pddDownloadUrl : 'https://dl.pddpic.com/windows_dev/2022-02-08/6b2e68605a803ae23dbebfd5f7da91fa.exe';
        const _html = '<div class="ctrl-dialog">'
            + '<div class="ctrl-dialog-mask ">'
            + '</div>'
            + '<div class="ctrl-dialog-container" style="width: 600px; min-height: 220px; margin-top: -110px; margin-left: -300px;">'
            + '<div class="ctrl-dialog-content">'
            + '<div class="content-box">'
            + '<h1 class="warn_title">'
            + '<span class="yellow-askIcon"></span>提示</h1>'
            + '<div class="note_content ft_14" style="font-family: \'microsoft yahei\';">'
            + '<p>系统检测到您的打印控件版本低， <a href="' + pddDownloadUrl + '" download="" target="_blank">请升级</a> ，升级安装完成后请重新打印。 </p>'
            + '</div>'
            + '<div class="ctrl-dialog-footer" style="display: block;"> <a href="' + pddDownloadUrl + '" download="" target="_blank" class="confirm dialog-footer-btn closeDialog" data-act-name="confirm">升级</a>'
            + '<a href="javascript:void(0)" class="dialog-footer-btn cancel" data-act-name="cancel">取消</a>'
            + '</div>'
            + '</div>'
            + '</div>'
            + '<div class="ctrl-dialog-footer"> <button class="confirm" data-act-name="confirm"> 确定 </button> <button class="cancel" data-act-name="cancel"> 取消 </button> </div>'
            + '</div>'
            + '</div>';
        window.dialog.show({
            width: 500,
            height: 220,
            content: _html,
            notHasMask: true,
            hideFooter: true,
            shown($dialog) {
                $dialog.find('[data-act-name=cancel]').on('click', function() {
                    $dialog.remove();
                });
            },
        });
    };

    // 全局设置
    resObj.setGolbalSet = function(callback) {
        let that;
            let html;
        if (mergeCacheObj.setGolbalSetGroupObj) {
            mergeCacheObj.setGolbalSetGroupObj.qlrr.init(callback);
            mergeCacheObj.setGolbalSetGroupObj.qlrr.show();
            return mergeCacheObj.setGolbalSetGroupObj;
        }
        that = new comp.Print.FN();
        html = `
        <div class="pup_express_box">
            <div class="pupexpr_tlt clearfix">
                <h1>快递单</h1>
                <a href="javascript:void(0)" class="expr_btn_gray float_r">取消</a>
                <a href="javascript:void(0)" class="expr_btn_green float_r">保存</a></div>
            <div class="kdd_set_main clearfix">
                <div class="merge_all_set">
                    <dl class="kdd_ctmd_bz">
                    <dd>
                        <span>默认分组</span>
                        <select class="group_select" name="defaultkdd" ></select>
                    </dd>
                    <dd>
                        <span>现有分组</span>
                        <div class="group_list_box">
                            <ul class="all_set_group_list"></ul>
                            <button class="add_btn">新增快递分组</button>
                            <div class="default_box">
                                <input class="default_checkbox" type="checkbox" name="defaultChoosed" style="vertical-align:top;">每次打开或刷新后选中默认快递
                            </div>
                        </div>
                    </dd>
                    </dl>
                </div>
            </div>
        </div>
        `;
        const body = $('body');
        const printData = comp.Print.Data;
        const domData = {};
        const domId = 'setGolbalSet_' + (+new Date());
        const dom = $(html).appendTo(body).attr('id', domId);
        // let obj = {};
        // obj.name = '横向缩放';
        // obj.value = 180;
        // dom.find('div[name=\'hengxiangsuofang\']').QlrrSilder(obj);
        // obj = {};
        // obj.name = '纵向缩放';
        // obj.value = 180;
        // dom.find('div[name=\'zongxiangsuofang\']').QlrrSilder(obj);
        dom.qlrr = {};
        const btnOk = dom.find('a.expr_btn_green.float_r');
        // 显示
        dom.qlrr.show = function() {
            that.showDialog(domId);
        };

        // 初始化
        dom.qlrr.init = function(callback) {
            btnOk.html('保存');
            domData.result = 'init';
            domData.callback = callback;
            domData.currentData = that.getKddList();
            dom.qlrr.loadData();
        };

        // 装载数据
        dom.qlrr.loadData = function() {
            const data = domData.currentData;
            // 清空
            const defaultkdd = dom.find('select[name=\'defaultkdd\']').empty();
            const kdlist = dom.find('.all_set_group_list').empty();
            const defaultChoosed = dom.find('input[name="defaultChoosed"]');
            // if(comp.Print.Data.platform === 'erp'){
            // defaultkdd.on('change', (event) => {
            //     const funcHook = (window.printAPI.compHookObj || {})
            //     .operatingRecord;
            //     funcHook && funcHook('订单_订单打印_快递单设置_全局设置_默认快递');
            //   });
            // }

            // 设置候选项
            let tem = '';
            let arr = (data || []).filter(o => o.id !== '-901');
            const userId = comp.Print.Data.userInfo.userId;
            const isDefaultChoosed = Tatami.localcache.get('defaultChoosedKddTempGroup' + userId);
            tem = '';
            $.each(arr, function(ind, inv) {
                tem += `<li value="${inv.id}"><div class="temp_name" title="${inv.groupName}"><span class="zmj_tips" style="${inv?.modeZmj ? '' : 'visibility:hidden;'}" >子母件</span><span class="group_name_text">${inv.groupName}</span></div><i class="delete_group_temp"></i></li>`;
            });
            kdlist.html(tem);
            const delKdLists = kdlist.find('li');
            delKdLists.find('i').click(function() {
                let deLen = 0;
                delKdLists.each(function(ind, inv) {
                    if ($(inv).css('display') == 'none') {
                        deLen++;
                    }
                });
                if (deLen == delKdLists.length - 1) {
                    Tatami.showFail('不能删除所有模版');
                    return;
                }
                const tar = $(this);
                tar.parent().myHide();
            });
            tem = '<option value=\'\'>请选择</option>';
            $.each(arr, function(ind, inv) {
                tem += '<option value=\'' + inv.id + '\'>' + inv.groupName + '</option>';
            });
            defaultkdd.html(tem);
            // 设置选中值
            if (isDefaultChoosed) {
                defaultkdd.val(isDefaultChoosed);
            }
            !!isDefaultChoosed && defaultChoosed.prop('checked', true);

        };

        // 保存
        dom.qlrr.save = function() {
            const obj = {};
            obj.DefaultGroupId = dom.find('select[name=\'defaultkdd\']').val();
            obj.DelIds = [];
            if (!obj.DefaultGroupId) {
                Tatami.showFail('请设置默认模版');
                return;
            }
            obj.DefaultGroupId = (+obj.DefaultGroupId);
            const kdlist = dom.find('.all_set_group_list');
            kdlist.find('li').each(function(ind, inv) {
                const tar = $(inv);
                if (tar.css('display').toLowerCase() == 'none') {
                    obj.DelIds.push(~~tar.attr('value'));
                }
            });
            if (obj.DelIds.includes(obj.DefaultGroupId)) {
                Tatami.showFail('当前设置的默认模版组已被删除，请重新设置默认模板');
                return;
            }
            let defaultChoosed = obj.DefaultGroupId;
            // 【彩🥚】其他平台 没淘宝id咋玩
            const userId = comp.Print.Data.userInfo.userId;
            const isDefaultChoosed = dom.find('input[name="defaultChoosed"]').prop('checked');

            // 防止重复点击
            if (btnOk.find('i').length > 0) {
                return;
            }
            btnOk.html('<i class="expr_green_loading"></i>保存中');
            // 检测 defaultChoosed 是否被删除,
            // if (obj.DelIds.includes(defaultChoosed)) {
            //     defaultChoosed = '';
            // }
            if (!isDefaultChoosed) {
                defaultChoosed = '';
            }
            // 【彩🥚】没有默认选择 不配保存吗？
            Tatami.localcache.set('defaultChoosedKddTempGroup' + userId, defaultChoosed); // 默认快递存入localstorage，供渲染kdd模板时使用
            // Tatami.localcache.set('defaultChoosedKddTemp' + taobaoId, isDefaultChoosed ? defaultChoosed : ''); //默认快递存入localstorage，供渲染kdd模板时使用
            that.saveKddGroupGlobalSetting(obj, function(data) {
                // 开启了闪打，需要重新生成模板的xml 后台生成xml 是在获取模板详情的时候，所以清除掉缓存中的xml信息
                // if( comp.Print.Data.modeType == 1 ){
                comp.Print.Data.kddTempInfos = []; // 清除掉已获取的模板详情
                // }
                // 删除掉本地储存中模版默认的打印机
                removePrintersStorage(obj.DelIds, true);

                const funcHook = (window.printAPI.compHookObj || {}).afterChangedKddTemp;
                funcHook && funcHook(data);
                domData.result = 'success';
                dom.qlrr.close();
            });
        };

        // 关闭
        dom.qlrr.close = function(iscall) {
            that.closeDialog(domId);
            if (typeof domData.callback === 'function' && !iscall) {
                domData.callback(domData.result);
            }
        };
        // 新增快递分组
        dom.find('.add_btn').click(function() {
            Tatami.pub('porinAddTempGroup');
            let tempHtml = `
                <div class="select_kdd_box">
                    <div class="select_kdd">
                        <div class="select_temp_title">
                            选择快递公司：
                        </div>
                        <select class="group_select" name="defaultkdd" ></select>
                    </div>
                    <div class="select_kdd size_box" >
                        <div class="select_temp_title">
                            选择模板组尺寸：
                        </div>
                        <div class="select_size">

                        </div>
                    </div>
                    <div class="select_kdd size_box" >
                    <div class="select_temp_title">
                        子母件模式：
                    </div>
                    <div class="select_mode">
                        <label style="width:100px;margin-bottom:8px" >
                            <input type="radio" name="kdd_mode" checked value="0" />
                            关闭
                        </label>
                        <label style="width:100px;margin-bottom:8px" >
                            <input type="radio" name="kdd_mode"  value="1" />
                            开启
                        </label>
                    </div>
                </div>
                </div>
                 <div class="save_btns">
                    <button class="cancel-btn" >取消</button>
                    <button class="ok-btn" >保存</button>
                </div>`;
            model({
                title: '新增模板组',
                width: 483,
                height: 315,
                // content: '确定移除【菜鸟中通一联单】模板吗？',
                className: 'add_temp_container',
                content: tempHtml,
                okName: '保存',
                cancelName: '取消',
                okCb: (doms) => {
                    const $doms = $(doms);
                    const defaultkdd = $doms.find('select[name=\'defaultkdd\']');
                    const exCompanyId = defaultkdd.val();
                    const exName = defaultkdd.find("option:selected").text();
                    const exCode = defaultkdd.find("option:selected").attr('ExCode');
                    const paperHeight = $doms.find('input[name=kdd_size]:checked').val();
                    const paperWidth = $doms.find('input[name=kdd_size]:checked').attr('data_name_paperWidth');
                    const kddMode = $doms.find('input[name=kdd_mode]:checked').val();
                    if (!exCompanyId || !paperHeight) {
                        let showText = !exCompanyId ? '快递公司' : '面单尺寸';
                        Tatami.showFail('请选择' + showText);
                        return;
                    }
                    dom.qlrr.close(true);
                    that.setGroupSet(null, null, { exCompanyId, paperHeight, paperWidth, exName, exCode, userTemplateList: [], modeZmj: kddMode });

                },
                shown: (tempDom) => {
                    const $tempDom = $(tempDom);
                    const defaultkdd = $tempDom.find('select[name=\'defaultkdd\']').empty();
                    const sizeSelect = $tempDom.find('.select_size').empty();
                    that.getExCompany(function(exCompanys) {
                        let tem = '<option value=\'\'>请选择</option>';
                        $.each(exCompanys, function(ind, inv) {
                            tem += `<option value= "${inv.Id}" ExCode="${inv.ExCode}" companyType="${inv.companyType}" companyName="${inv.CompanyName}">${inv.CompanyName}</option>`;
                        });
                        defaultkdd.html(tem);
                        defaultkdd.on('change', function(e) {
                            let selectId = $(this).val();
                            let companyType = $(e.target).find('option:selected').attr('companyType');
                            let exCode = $(e.target).find('option:selected').attr('ExCode');
                            if (!selectId) {
                                sizeSelect.html('');
                                return;
                            }
                            let inputDom = $tempDom.find('input[name=kdd_mode]');
                            $.each(inputDom, function(ind, inv) {
                                let inputVal = $(inv).val();
                                $(inv).removeAttr('disabled');
                                $(inv).removeAttr('checked');

                                if (companyType == 2) {
                                    if (inputVal == 0) {
                                        $(inv).attr('disabled', 'disabled');
                                    }
                                    if (inputVal == 1) {
                                        $(inv).prop("checked", true);
                                    }
                                } else if (['JD', 'SF', 'DBKD'].includes(exCode)) {
                                        if (inputVal == 0) $(inv).prop("checked", true);
                                    } else {
                                        if (inputVal == 0) {
                                            $(inv).prop("checked", true);
                                        }
                                        if (inputVal == 1) $(inv).attr('disabled', 'disabled');
                                    }
                            });

                            that.getHeightPaperByCompanyId(selectId, function(data) {
                                let formatText = '';
                                tem = '';
                                $.each(data, function(ind, inv) {
                                    formatText = `${inv.paperWidth / 100}*${inv.paperHeight / 100}cm`;
                                    tem += `<label style="width:100px;margin-bottom:8px" >
                                        <input type="radio" name="kdd_size" data_name_paperWidth = "${inv.paperWidth}" value="${inv.paperHeight}" />
                                        ${formatText}
                                    </label>`;
                                });
                                sizeSelect.html(tem);
                            });
                        });
                    });

                }
            });
        });
        // 取消
        dom.find('a.expr_btn_gray.float_r').click(function() {
            dom.qlrr.close();
        });

        // 保存
        dom.find('a.expr_btn_green.float_r').click(function() {
            dom.qlrr.save();
        });

        dom.find('.add_icon').click(function() {
            that.closeDialog(domId);
            that.setGroupSet();
        });

        // 执行
        mergeCacheObj.setGolbalSetGroupObj = dom;
        mergeCacheObj.setGolbalSetGroupObj.qlrr.init(callback);
        mergeCacheObj.setGolbalSetGroupObj.qlrr.show();
        return mergeCacheObj.setGolbalSetGroupObj;
    };
    // HTML版本编辑快递模版
    resObj.editHtml = async function(tempInfo, callback, tempData, singlePData) {
        let that = new comp.Print.FN();
            let printData = comp.Print.Data;
            let domId = 'editkdddiv';
            let $dom;
            let _type = tempInfo.ModeListShow.Modeid;
            let styleId = tempInfo.ModeList.StyleId;
            let ExCode = tempInfo.ModeList.Excode;
            let KddType = tempInfo.ModeList.KddType;
            let exId = tempInfo.ModeList.Exid;
            let func = {};

        if (!tempData) {
            tempData = await that.getPrintData();
            if (_type == 'fhd') {
                this.getFhdTableHtml([tempData], tempInfo);
            }
        }

        $dom = $('#editkdddiv');
        if (!$('#editkdddiv').length) {
            $dom = $('<div name="editkdddiv" id="editkdddiv" style="width:1100px;display:none;" class="pup_express_box"></div>').appendTo('body');
        }
        func.closeFunc = function(data) {
            $dom.find('.drag_play_ground').removeClass('isEditing');
            $dom.find('.template_ground_addition').removeClass('isEditing');
            that.closeDialog(domId);
            // data为布尔值并且是true  直接关闭弹窗
            if (typeof data === 'boolean' && data) {
                return;
            }
            callback && callback(data);
        };
        func.saveFunc = function(tempDetail) {
            // 数据处理
            // var type = tempDetail.ModeListShow.Modeid;
            if (_type == 'kdd') { // 快递单的编辑
                let _listShow = printData.kddTemplates.ModeListShows;
                     let _chooseId = tempDetail.ModeListShow.Mode_ListShowId;
                     let _chooseName = tempDetail.ModeListShow.ExcodeName;
                     let _evs;
                printData.kddTempInfos && $.each(printData.kddTempInfos, function(_i, _v) {
                    if (_v.ModeListShow.Mode_ListShowId == _chooseId) {
                        if (tempDetail.ModeListShow.KddType == 7) { // 如果拼多多电子面单 重新从接口中获取模板数据
                            printData.kddTempInfos.splice(_i, 1);
                        } else {
                            printData.kddTempInfos[_i] = tempDetail;
                        }
                        return false;
                    }
                });
                // 模板列表数据处理
                _listShow && $.each(_listShow, function(_i, _v) {
                    if (_v.Mode_ListShowId == _chooseId) {
                        if (_v.ExcodeName != _chooseName) { // 修改了模板名称
                            _listShow[_i].ExcodeName = _chooseName;
                            // 触发模板改变事件
                            _evs = comp.Print.eventObj.KddTemplateListChanged;
                            if (_evs) {
                                _evs.each(function(_index, _item) {
                                    _item(_v.Mode_ListShowId);
                                });
                            }
                        }
                        return false;
                    }
                });
            } else if (_type == 'fhd') {
                let _listShow = printData.fhdTemplateList && printData.fhdTemplateList.ModeListShows;
                     let _chooseId = tempDetail.ModeListShow.Mode_ListShowId;
                     let _chooseName = tempDetail.ModeListShow.ExcodeName;
                     let _evs;
                printData.fhdTempInfos && $.each(printData.fhdTempInfos, function(_i, _v) {
                    if (_v.ModeListShow.Mode_ListShowId == tempDetail.ModeListShow.Mode_ListShowId) {
                        printData.fhdTempInfos[_i] = tempDetail;
                        (comp.Print.Data.globalSettingFhd || {}).ModeSet = tempDetail.ModeSet;
                        return false;
                    }
                });
                // 模板列表数据处理
                _listShow && $.each(_listShow, function(_i, _v) {
                    if (_v.Mode_ListShowId == _chooseId) {
                        if (_v.ExcodeName != _chooseName) { // 修改了模板名称
                            _listShow[_i].ExcodeName = _chooseName;
                            // 触发模板改变事件
                            const fhdTemplateList = that.getFhdTemplateList();
                            Tatami.pub('printBatch.updateFhdTempList', fhdTemplateList.ModeListShows);
                        }
                        return false;
                    }
                });

            }
            that.closeDialog(domId);
            callback && callback();
        };
        // 备货单小标签的type=2, 备货单扫描小标签 type =10,
        let configTypeName = comp.base.getFhdType(tempInfo);
        let configType = null;
        switch (configTypeName) {
            case 'fhd-sm-bq':
                configType = 99; // 暂不生效
                break;
            case 'fhd-sm-bq-new':
                configType = 10;
                break;
            case 'fhd-sm-thd-new':
                configType = 11;
                break;
            case 'fhd-sm-bkd-new':
                configType = 12;
                break;
            case 'fhd':
                configType = 2;
                break;
            case 'fhd-nh-bq':
                configType = 2;
                break;
            default:
                configType = 1;
                break;
        }
        that.getTemplateDefaultItem({
            type: configType,
        }, function(item) {
            $dom.html(that.drawHtml({
                templateData: tempInfo,
                globalSetting: resObj.getCacheGlobSet(_type),
                exuid: printData.exuid,
                // isEdit: true,
                viewMode: '1',
                mockInputData: tempData,
                defaultItem: item,
                type: _type,
                singlePData,
            }, func.saveFunc, func.closeFunc).showTemplate());
            that.showDialog(domId);
            $dom.find('.drag_play_ground').addClass('isEditing');
            // 加个不可编译遮罩
            comp.base.notEditAreaMask($dom, {
                styleId,
                type: _type,
                exCode: ExCode,
                kddType: KddType,
                exId,
                tempInfo,
            });
            $dom.find('.template_ground_addition').addClass('isEditing');
        }, true);
    };
    // NOTE 分组设置
    // 全局设置
    resObj.setGroupSet = function(callback, isEditTemp, groupData) {
        if (groupData) addGroupCache = groupData;
        const kddSizeName = {
            1300: '一联单组',
            1130: '一联单组',
            1500: '二联单组',
            1800: '三联单组',
            2100: '三联单组',
        };
        let that;
            let currentGroup = addGroupCache || comp.Print.Data.currentGroup;
            let html;
            let tempName = currentGroup.groupName || currentGroup.exName + kddSizeName[currentGroup.paperHeight];
        // if (mergeCacheObj.setGroupSetObj) {
        //     mergeCacheObj.setGroupSetObj.qlrr.init(callback);
        //     mergeCacheObj.setGroupSetObj.qlrr.show();
        //     return mergeCacheObj.setGroupSetObj;
        // }
        that = new comp.Print.FN();
        html = `
        <div class="pup_express_box">
            <div class="pupexpr_tlt clearfix">
                <h1>编辑模板组</h1>
                <a href="javascript:void(0)" class="expr_btn_gray float_r">取消</a>
                <a href="javascript:void(0)" class="expr_btn_green float_r">保存</a></div>
            <div class="kdd_set_main clearfix">
                <div class="group_all_set">
                    <dl class="kdd_ctmd_bz">
                        <dt>
                            <span>1</span>模板组名称</dt>
                        <dd>
                            <input name="tempGroupName" value = "${tempName}" style="width:180px;height:23px;padding:0 10px;"/>
                            ${currentGroup?.modeZmj == 1 ? '<span style="width:52px;height:20px;border-radius:2px;background:#F7B500;color:#fff;text-align:center" >子母件</span>' : ''}
                        </dd>
                    </dl>
                    <dl class="kdd_ctmd_bz">
                    <dt>
                    <span>2</span>快递单模板设置<i>（一个模板组内不能添加多个同类型的电子面单模板）</i></dt>
                    <dd class="clearfix" >
                        <ul class="all_group_kdlist"></ul>
                    </dd>
					<dd class="jdTips">
					</dd>
                    <dd>
                        <button class="add_new_temp">添加新模板</button>
                    </dd>
                </dl>
                </div>
            </div>
        </div>
        `;
        const body = $('body');
        const printData = comp.Print.Data;
        const domData = {};
        const domId = 'setGolbalSet_' + (+new Date());
        const dom = $(html).appendTo(body).attr('id', domId);
        // let obj = {};
        // obj.name = '横向缩放';
        // obj.value = 180;
        // dom.find('div[name=\'hengxiangsuofang\']').QlrrSilder(obj);
        // obj = {};
        // obj.name = '纵向缩放';
        // obj.value = 180;
        // dom.find('div[name=\'zongxiangsuofang\']').QlrrSilder(obj);
        dom.qlrr = {};
        const btnOk = dom.find('a.expr_btn_green.float_r');
        // 显示
        dom.qlrr.show = function() {
            that.showDialog(domId);
        };

        // 初始化
        dom.qlrr.init = function(callback) {
            btnOk.html('保存');
            domData.result = 'init';
            domData.callback = callback;
            domData.currentData = Object.Copy(comp.Print.Data.globalSettingKdd);
            console.log(!comp.Print.Data.addNewKdd);
            console.log(addGroupCache, 'addGroupCache');
            if (!comp.Print.Data.addNewKdd) {
                dom.qlrr.loadData();
                return;
            }
            let addTempInfo = comp.Print.Data.addNewKdd;
            let newTempObj = {
                exName: addTempInfo.ExcodeName,
                expressType: addTempInfo.KddType,
                userTemplateId: addTempInfo.Mode_ListShowId
            };
            const userTemplateList = addGroupCache?.userTemplateList || comp.Print.Data.currentGroup?.userTemplateList || [];
            const newTempArr = comp.Print.Data.cacheNewTempArr || [...userTemplateList];
            let newIndex = newTempArr.findIndex(it => {
				if([13,16].includes(Number(newTempObj.expressType))){
					return [13,16].includes(Number(it.expressType))
				}else{
					return it.expressType == newTempObj.expressType
				}
			});
            if (newIndex === -1) {
                newTempArr.push(newTempObj);
            } else {
                newTempArr[newIndex] = newTempObj;
            }
            comp.Print.Data.addNewKdd = null;
            comp.Print.Data.cacheNewTempArr = newTempArr;
            dom.qlrr.loadData(newTempArr);
        };

        // 装载数据
        dom.qlrr.loadData = function(data) {
            console.log(domData,'domData');
            // 清空
            const kdlist = dom.find('.all_group_kdlist').empty();
			const jdTips = dom.find('.jdTips');
            let arr = data || currentGroup?.userTemplateList || [];
            let tem = '';
			let jdTipsHtml = `<span class="tip" style="color:red;">注意：京东无界电子面单暂不支持申请多单号，使用此模版组打印时仅可申请一个单号</span>`;
            $.each(arr, function(ind, inv) {
				if([20090,20091,20092].includes(Number(inv.exId))) jdTips.html(jdTipsHtml)
                tem += '<li  value="' + inv.userTemplateId + '"><div class="temp_name" tempName = "' + inv.exName + '">' + inv.exName + '</div><i class="delete_group_temp"></i></li>';
            });
            kdlist.html(tem);
            const delKdLists = kdlist.find('li');
            delKdLists.find('i').click(function() {
                const tar = $(this);
                const tempName = tar.parent().find('.temp_name').attr('tempName');
                model({
                    type: 'confirm',
                    title: '提示',
                    width: 433,
                    height: 192,
                    content: `确定移除【${tempName}】模板吗？`,
                    // content:  `<div style="line-height:24px;">应顺丰快递安全要求，使用顺丰电子面单需要绑定顺丰客户编码。<a href="https://helptb.kuaidizs.cn/helpMap/getDetail?detailId=1373" target="_blank">如何获取？</a></div>`,
                    okName: '确定',
                    cancelName: '取消',
                    okCb: () => {
                        // let deLen = 0;
                        // delKdLists.each(function (ind, inv) {
                        //     if ($(inv).css('display') == 'none') {
                        //         deLen++;
                        //     }
                        // });
                        if (delKdLists.length === 1) {
                            Tatami.showFail('不能删除所有模版');
                            return;
                        }
                        tar.parent().remove();
                    }

                });

            });
        };
        // 保存
        dom.qlrr.save = function() {
            let groupName = dom.find('input[name = "tempGroupName"]').val();
            const obj = {
                groupId: currentGroup.id,
                groupName,
                exCompanyId: currentGroup.exCompanyId,
                paperHeight: currentGroup.paperHeight,
                paperWidth: currentGroup.paperWidth,
                modeZmj: currentGroup?.modeZmj || '0',
                userTemplateIdList: []
            };
            const kdlist = dom.find('.all_group_kdlist');
            kdlist.find('li').each(function(inx, it) {
                const target = $(it);
                obj.userTemplateIdList.push(target.val());
            });
            // if(['ANEKY','3108002701_1011','CN7000001021040','SFKY'].includes(ExCode.toString()) && template.KddType == 5) isZiMuJian =false
            // 防止重复点击
            if (btnOk.find('i').length > 0) {
                return;
            }
            btnOk.html('<i class="expr_green_loading"></i>保存中');

            // Tatami.localcache.set('defaultChoosedKddTemp' + taobaoId, isDefaultChoosed ? defaultChoosed : ''); //默认快递存入localstorage，供渲染kdd模板时使用
            that.saveTemplateGroup(obj, function(data) {
                addGroupCache = null;
                // comp.Print.Data.currentGroup =null
                // const funcHook = (window.printAPI.compHookObj || {}).afterChangedKddTemp ;
                // funcHook && funcHook(data);
                domData.result = 'success';
                dom.qlrr.close();
                resObj.showkddMain();

            });
        };

        // 关闭
        dom.qlrr.close = function(showKdd) {
            that.closeDialog(domId);
            if (showKdd)resObj.showkddMain();
            if (typeof domData.callback === 'function') {
                domData.callback(domData.result);
            }
        };

        // 取消
        dom.find('a.expr_btn_gray.float_r').click(function() {
            addGroupCache = null;
            comp.Print.Data.cacheNewTempArr = null;
            dom.qlrr.close(true);
            if (isEditTemp) {
                that.editKdd(comp.Print.Data.currentTempInfo, function(data) {
                    if (data) {
                        resObj.setGroupSet(null, isEditTemp);
                    } else {
                        resObj.showkddMain(domData.defId, domData.printDatas, sellerData);
                    }
                });
            }
        });

        // 保存
        dom.find('a.expr_btn_green.float_r').click(function() {
            comp.Print.Data.cacheNewTempArr = null;
            dom.qlrr.save();
        });

        dom.find('.add_icon').click(function() {
            that.closeDialog(domId);
            that.addKdd();
        });
        // 添加模板到组
        dom.find('.add_new_temp').click(function() {

            let tempHtml = `
                <div class="select_temp_box">
                    <div class="select_temp_title">
                        选择快递模板
                    </div>
                    <ul class="temp_list_box">
                    </ul>
                    <a class="add_temp_btn">新建模板</a>
                </div>
                 <div class="save_btns">
                    <button class="cancel-btn" >取消</button>
                    <button class="ok-btn" >保存</button>
                </div>`;
            model({
                title: '添加模板到组',
                width: 483,
                height: 315,
                // content: '确定移除【菜鸟中通一联单】模板吗？',
                className: 'add_temp_container',
                content: tempHtml,
                okName: '保存',
                cancelName: '取消',
                okCb: (doms) => {
                    const userTemplateList = currentGroup.userTemplateList || [];
                    const newTempArr = comp.Print.Data.cacheNewTempArr || [...userTemplateList];
                    const kdlist = dom.find('.all_group_kdlist').empty();
                    let obj = null;
                    $(doms).find('.temp_checkbox').each(function(ind, inv) {
                        const target = $(inv);
                        if (target.prop('checked')) {
                            obj = {
                                exName: target.attr('exName'),
                                expressType: target.attr('name'),
                                userTemplateId: target.val(),
                                exId: target.attr('exId')
                            };
                            let newIndex = newTempArr.findIndex(it => {
								if([13,16].includes(Number(obj.expressType))) return [13,16].includes(Number(it.expressType))
								return it.expressType == obj.expressType
							});
                            if (newIndex === -1) {
                                newTempArr.push(obj);
                            } else {
                                newTempArr[newIndex] = obj;
                            }
                        }
                    });
                    comp.Print.Data.cacheNewTempArr = newTempArr;
                    dom.qlrr.loadData(newTempArr);
                },
                shown: (tempDom) => {
                    const $tempDom = $(tempDom);
                    const kddTypeObj = comp.base.getKddNameObj();
                    let ul = $tempDom.find('.temp_list_box');
                    ul.remove('.temp_item');
                    let itemHtml = '';
                    let item = null;
                    let toolTipText = (type) => {
						if(type == 16 || type == 13){
							return `<span>*该组内已有小红书模板，勾选将替换原模板</span>`
						}
                        return `<span>*该组内已有${kddTypeObj[type]}模板，勾选将替换原模板</span>`
                    }

                    that.getGroupSelectAllowUserTemplate(currentGroup, function (res) {
                        let isDisabledZmj =  ['ANEKY','3108002701_1011','CN7000001021040','SFKY'].includes(currentGroup.exCode) && currentGroup.modeZmj == '1'
                        res.length && res.forEach(o => {
                            item = currentGroup.userTemplateList && currentGroup.userTemplateList.find(it =>{
								if([13,16].includes(Number(it.expressType))){
									return [13,16].includes(Number(o.expressType))
								}else{
									return it.expressType === o.expressType
								}
							})

                            itemHtml += `
                            <li class="temp_item">
                                <div class="temp_name"  ${isDisabledZmj && o.expressType == 5 ?'title="平台暂不支持子母件服务"' : `title="${o.exName}"`}>
                                    <input class="temp_checkbox" ${isDisabledZmj && o.expressType == 5 } type="checkbox" exName = "${o.exName}" exId="${o.exId}"  name="${o.expressType}" value="${o.userTemplateId}">
                                    <label class="temp_text">
                                        ${o.exName}
                                    </label>
                                    （${kddTypeObj[o.expressType]}）
                                </div>
                                ${!!item  ? toolTipText(o.expressType) : ''}
                            </li>`
                        })
                        $(itemHtml).appendTo(ul)
                        $(ul).find('.temp_checkbox').change(function (e) {
                            let type = $(this).attr('name')
							if([13,16].includes(Number(type))){
								if ($(this).is(':checked')) {
									$(ul).find("input[name='" + 13 + "']:not(:checked)").attr("disabled", true);
									$(ul).find("input[name='" + 16 + "']:not(:checked)").attr("disabled", true);
								} else {
									$(ul).find("input[name='" + 13 + "']").attr("disabled", false);
									$(ul).find("input[name='" + 16 + "']").attr("disabled", false);
								}
							}else{
								if ($(this).is(':checked')) {
									$(ul).find("input[name='" + type + "']:not(:checked)").attr("disabled", true);
								} else {
									$(ul).find("input[name='" + type + "']").attr("disabled", false);
								}
							}

                        });
                    });

                    $tempDom.find('.add_temp_btn').click(function() {
                        that.closeDialog(domId);
                        dom.qlrr.close();
                        // that.addKdd();
                        that.addKdd(null, { isCancel: true, ...currentGroup });
                        tempDom.parentNode.removeChild(tempDom);
                    });
                    // const btnOk = $tempDom.find('.btn_save');
                    // const btnNo = $tempDom.find('.btn_cancel');
                    // btnNo.click(function(){
                    //     console.log(1111);
                    // })
                    // btnOk.click(function(){
                    //     console.log(222);
                    // })
                }
            });
        });
        // 执行
        mergeCacheObj.setGroupSetObj = dom;
        mergeCacheObj.setGroupSetObj.qlrr.init(callback);
        mergeCacheObj.setGroupSetObj.qlrr.show();
        return mergeCacheObj.setGroupSetObj;
    };

    // todo
    resObj.getCacheGlobSet = function(type) {
        let key = '';

        if (type === 'kdd') {
            key = 'globalSettingKdd';
        } else if (type === 'fhd') {
            key = 'globalSettingFhd';
        } else if (type === 'bhd') {
            key = 'globalSettingBq';
        } else if (type === 'thd') {
            key = 'globalSettingThd';
        }

        return comp.Print.Data[key];
    };

    resObj.getChildServiceHtml = function(ser, isCheck, excode) {
        let _detailHtml = ''; let _type; let _desc; let _code; let _hideClass; let mix_desc; let _$li; let
scopeHtml;
        if (!ser.serviceAttributes && !ser.value) {
            _detailHtml = '';
        } else if (ser.serviceName === '托寄物') {
            // _code = ser.serviceCode;
            _detailHtml = $('<ul class="service_value_list" style="' + (!isCheck ? 'display:none' : '') + '" ></ul>');
            const serviceAttributes = ser?.value.split('|') || [];
            $.each(serviceAttributes, function(i1, v1) {
                _$li = $('<li class="form_box_la_em service_value ' + _hideClass + ' " ></li>');
                let ohtml = '';
                if (v1.match(/\[(.+)\]/)) {
                    let itemName = v1.split(':')[0] || '';
                    let options = v1.match(/\[(.+)\]/)[1].split(',');
                    ohtml += `<div class="form_box_la_em">
                        <label style="display:inline"><input type="radio" name="ali_waybill_serv_tjw" class="input_radio input_service serviceValue" value="${itemName}">${itemName}</label>
                        <div class="form_box_la_em" id="kddservicetype_zdtjw" style="display: none;">
                            <select style="width: 90px;" name="ali_waybill_serv_tjw">`;
                    let optionChilds = '';
                    options && options.length && options.each(function(index, option) {
                        if (option === '自定义内容') {
                            optionChilds = `<div class="form_box_la_em">
                                <input name=${ser.serviceName}${option} id="kddservicetype_zdtjw_zdy" style="display:none"
                                placeholder="请输入托寄物内容" maxlength="20"/>
                            </div>`;
                        }
                        ohtml += `<option value=${option}>${option}</option>`;
                    });
                    ohtml += `</select></div>${optionChilds}</div>`;
                } else {
                    const labelToolTip = v1.includes('订单商品作为托寄物')
                    ? `<img src="https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/wenhao.png"
                    class="toolTip v-middle" data-tip-content="选择后将按订单商品信息作为托寄物，\n商品有简称传简称，没有简称传标题。"/>` : '';
                ohtml += `<div class="form_box_la_em"><label style="display:inline">
                    <input type="radio" name="ali_waybill_serv_tjw" class="input_radio input_service serviceValue" value="${v1}">${v1}</label>
                    ${labelToolTip}</div>`;
                }
                _$li.append(ohtml);
                _detailHtml.append(_$li);

            });
        } else {
            _code = ser.serviceCode;
            _detailHtml = $('<ul class="service_value_list" style="' + (!isCheck ? 'display:none' : '') + '" ></ul>');
            $.each(ser.serviceAttributes, function(i1, v1) {
                _type = v1.attributeType; // 服务类型
                _hideClass = v1.attributeHide ? 'hide' : ''; // 是否隐藏服务明细
                // 一级服务
                _$li = $('<li class="form_box_la_em service_value ' + _hideClass + ' " >' + v1.attributeName + '<ul class="service_value_list" ></ul></li>');
                if (_code == 'COD' && excode == 'DBKD') {
                    _$li = $('<li class="form_box_la_em service_value ' + _hideClass + ' " >'
                        + '<label><input type="checkbox" checked disabled data-code="' + v1.attributeCode + '" '
                        + ' name="' + v1.attributeName + '" class="input_check attributeName '
                        + (v1.attributeCode == 'codvalue' ? 'input_service serviceValue' : '')
                        + '" value="' + v1.attributeName + '">'
                        + v1.attributeName
                        + '</label><ul class="service_value_list" ></ul></li>');
                }
                // 二级服务
                let _$list = _$li.find('ul');
                    let _listLi;
                    let saveSer;
                if (_type === 'string' || _type === 'number') {
                    saveSer = {
                        code: v1.attributeCode,
                        name: v1.attributeName,
                    };
                    _listLi = $('<li class="form_box_la_em service_value" >'
                    + (v1.attributeCode == 'codvalue' && _code == 'COD' && excode == 'DBKD'
                        ? '' : '<label><input type="' + (_type === 'number' ? 'number' : 'text')
                    + '" class="w100 input_service serviceValue"  name="'
                    + _code + v1.attributeName + '"  data-name="' + saveSer.name + '"></label>')
                    + '</li>');
                    _listLi.find('.serviceValue').data('service', saveSer);
                    _$list.append(_listLi);
                } else if (_type === 'enum') {
                    _desc = JSON.parse(v1.typeDesc || '{}');
                    _desc.desc && $.each(_desc.desc, function(i2, v2) {
                        mix_desc = (_desc.mix_desc || {})[i2];

                        scopeHtml = '';
                        if (mix_desc && mix_desc.max && (mix_desc.min || mix_desc.min === 0)) {
                            scopeHtml = '<div class="pt_5">(' + mix_desc.min + '元-' + mix_desc.max + '元)</div>';
                        }
                        saveSer = {
                            code: v1.attributeCode,
                            name: v2 || v1.attributeName,
                            type: i2,
                            value: mix_desc ? 'mixInputVal' : i2,
                        };

                        _listLi = $('<li class="form_box_la_em service_value" >'
                            + '<label>'
                            + '<input type="radio" name="' + (_code + v1.attributeName) + '"  data-name="' + saveSer.type + '"  class="input_radio serviceValue" value="' + i2 + '"  >' + v2
                            + '</label>'
                            + (mix_desc ? '<div class="mt_5 mix-desc"><input type="' + (mix_desc.type == 'number' ? 'number' : 'text') + '"  data-min="' + mix_desc.min + '"  data-max="' + (mix_desc.max || '') + '"  class="w85 inputService" name="' + (_code + v1.attributeName) + '" />' + scopeHtml + '</div>' : '')
                            + '</li>');
                        _listLi.find('.serviceValue').data('service', saveSer);
                        _$list.append(_listLi);
                    });
                }
                _detailHtml.append(_$li);
            });
        }
        return _detailHtml;
    };

    /**
     *  获取菜鸟服务HTML --新增模板 编辑模板
     * @param  {[type]}  serviceList    [服务列表数据]
     * @param  {Boolean} isAdd          [是否新增模板功能]
     * @param  {[type]}  defaultService [模板勾选的服务]
     * @param  {Boolean} isShowPurchase [是否需要显示未开通 true:显示，false:不显示]
     * @param { Number } kddType
     * @return {[type]}                 [description]
     */
    resObj.getCainiaoServiceHtml = function(serviceList, isAdd, defaultService, isShowPurchase, kddType) {
        let _len; let _$html; let _html = ''; let _isDefault; let _$ul; let _isAudit;
            let _needOpenSer = [];
            let getServiceHtml = this.getChildServiceHtml;
        _len = (serviceList || []).length;
        if (!_len) {
            return;
        }

        _$html = $('<div><ul class=\'kdd_ctmd_bz_form cnservice_content\'></ul></div>');
        _$ul = _$html.find('ul');

        let linkUrl = '';
        if (kddType == 7) {
            linkUrl = '//mms.pinduoduo.com/logistics/open';
        } else {
            linkUrl = '//waybill.wuliu.taobao.com/myServiceProviders.htm';
        }

        // 服务html拼接
        $.each(serviceList, function(i, item) {
            const _service = item.customStatus == 2 ? item.customServiceInfoDto : item.serviceInfoDto;

            if (!_service || (!isAdd && _service?.serviceCode === 'tjw')) {
                return true;
            }
            _isDefault = _service.required ? 1 : 0;
            _isAudit = item.serviceStatus == 1;
            if (_isAudit && isAdd) { // serviceStatus==1 表示服务未审核 新增模板会需要区分
                _needOpenSer.push(comp.Print.Data.platform === 'pdd' ? _service.service_name : _service.serviceName);
            } else {
                _html = '<li class="service" >'
                    + '<div class="service-label-box">'
                    + '<label class="on service-label toolTip"  data-tip-content="' + (_isAudit ? '即将推出该服务，如需使用，请联系客服了解更多详情' : (item.serviceDesc || _service.serviceDesc)) + '">'
                    + '<input name="kddservicetypeNew" isdefault="' + (_isDefault ? 1 : 0) + '"  ' + (_isAudit || _isDefault ? 'disabled' : '') + ' data-segmentcode="' + (item.segmentCode || '') + '" type="checkbox" class="input_check"  data-servicename="' + _service.serviceName + '"  value="' + _service.serviceCode + '">'
                    + _service.serviceName
                    + '</label>'

                    // 新增模板，模板服务未开通提示
                    // 菜鸟快运不显示是否开通情况，因为快运不存在开通服务逻辑
                    // 编辑模板的时候也不显示服务开通情况
                    + (isAdd && isShowPurchase && !item.purchase ? `<a class="green-fc ml_10" href="${linkUrl}" target="_blank">未开通</a>` : '')
                    + '</div>'
                    + '</li>';
                $(_html).append(getServiceHtml(_service, _isDefault, item?.excode)).appendTo(_$ul);
            }
        });

        // 系统未审核服务展示
        if (_needOpenSer.length) {
            _$html.append('<p class="gray-fc"><i class="icon-info mr_5"></i>即将推出' + _needOpenSer.join('、') + '，如需使用，<font class="green-fc">请联系客服了解更多详情</font></p>');
        }

        // 事件绑定
        _$html.find('[name=kddservicetypeNew]').change(function(event) {
            let $target = $(this); let $serList = $target.closest('.service').find('.service_value_list'); let
_isCheck;
            _isCheck = $target.prop('checked');

            // 拼多多电子面单 - 快递服务选项互斥
            const excode = $('#addExCompany .active').attr('excode');
            const kddType = $('.input_radio[name=kddtype]:checked').val();
            if (kddType == 7 && excode == 'ZTO') {
                const allCheckbox = _$html.find('[name=kddservicetypeNew]');
                allCheckbox.prop('checked', false);

                allCheckbox.prop('disabled', _isCheck);
                allCheckbox.closest('label').removeClass('on');

                $target.prop('checked', _isCheck);
                $target.prop('disabled', false);
            }
            $target.closest('label')[_isCheck ? 'addClass' : 'removeClass']('on');
            if ($serList.find('.service_value').length) {
                if (_isCheck) {
                    $serList.show();
                    $serList.find('.input_radio:eq(0)').prop('checked', true).change(); // 默认选中第一个
                } else {
                    $serList.hide();
                }
            }
        });

        _$html.find('.service_value .input_radio').change(function(event) {
            let $target = $(this); let
_$dom;
            _$dom = $target.closest('.service').find('.mix-desc').hide();
            if ($target.attr('name') === 'ali_waybill_serv_tjw') { // 托寄物
                let _name = $target.val();
                if (_name.includes('指定托寄物')) { // 指定托寄物下拉选择
                    $('#kddservicetype_zdtjw').show();
                    _$html.find('select').get(0).selectedIndex = 0;
                    _$html.find('select').change();
                } else {
                    // 隐藏指定托寄物下拉选择框和自定义托寄物输入框
                    $('#kddservicetype_zdtjw').hide();
                    $('#kddservicetype_zdtjw_zdy').hide().val('').removeAttr('class');
                }
            }
            if (_$dom.length) {
                $target.closest('.service_value').find('.mix-desc').show();
            }
        });
        _$html.find('.form_box_la_em select').change(function() { // 三级下拉选择框数据处理
            let value = $(this).val();
            // 获取对应父级复选框，并更新data数据
            // const parents = $(this).parentsUntil('div.kdd_ctmd_bz_form');
            // const parentCheckbox = $(parents[parents.length - 1]).find("input[name='kddservicetype']");
            // let parentData = parentCheckbox.data('data');
            if (value === "自定义内容") { // 指定托寄物自定义内容，以下级文本输入框内容为准
                $('#kddservicetype_zdtjw_zdy').show().attr('class', 'input_noempty');
            } else {
                // parentData.Value = value;
                $('#kddservicetype_zdtjw_zdy').hide().val().removeAttr('class');
            }
        });
        _$html.find('#kddservicetype_zdtjw_zdy').change(function() { // 指定托寄物自定义内容，文本输入框
            let value = $(this).val();
            // 获取对应父级复选框，并更新data数据
            const parents = $(this).parentsUntil('div.kdd_ctmd_bz_form');
            const parentCheckbox = $(parents[parents.length - 1]).find("input[name='kddservicetype']");
            let parentData = parentCheckbox.data('data');
            parentData.Value = value;
        });
        // 注册提示事件
        comp.base.toolTip(_$html);
        return _$html;
    };

    resObj.showCainiaoServiceHtml = function(serviceList, defaultService, kddType) {
        let $input; let $service; let $html; let _serList; let _ser; let
_$dom;
        $html = this.getCainiaoServiceHtml(serviceList, false, defaultService, false, kddType);
        $.each(defaultService, function(_i, _v) {
            $service = $html.find('[name=kddservicetypeNew]').filter('[value="' + _v.serviceCode + '"]').prop('checked', true).change()
.closest('.service');
            if (_v.serviceValue && _v.serviceCode !== 'tjw') {
                // comp.prin;
                _serList = JSON.parse(_v.serviceValue);
                $.each(_serList, function() {
                    _ser = this;
                    if (_ser) {
                        $input = $service.find('input.serviceValue[data-name="' + (_ser.type || _ser.name) + '"]');
                        if ($input.hasClass('input_service')) { // 文本框
                            $input.val(_ser.value);
                        } else { // 枚举类型
                            $input.prop('checked', true).change();
                            // mix-desc 处理
                            if (_ser.value !== _ser.type) {
                                _$dom = $service.find('.inputService[name=' + $input.attr('name') + ']');
                                _$dom.length && _$dom.val(_ser.value);
                            }
                        }
                    }
                });
            }
        });
        return $html;
    };

    resObj.getChooseService = function(dom, kddObj) {
        let _service = []; let
isError;
        dom.find('input[name=\'kddservicetypeNew\']:checked').each(function(ind, inv) {
            let _$service = $(inv).closest('.service'); let _max; let _min;
                let _$dom = _$service.find('.input_radio:checked, .input_service');
                let _list = [];
            if ($(inv).val() === 'tjw') {
                _$dom = _$service.find('.input_radio:checked');
                let serviceValue = '';
                let ratioValue = _$dom.val();
                if (ratioValue === '订单商品作为托寄物') {
                    serviceValue = '订单商品作为托寄物';
                } else {
                    let selectVal = _$service.find('#kddservicetype_zdtjw select').val();
                    if (selectVal === '自定义内容') {
                        serviceValue = _$service.find('#kddservicetype_zdtjw_zdy').val();
                    } else {
                        serviceValue = selectVal;
                    }
                }
                _service.push({
                    excode: kddObj.excode,
                    excodeName: kddObj.excodeName,
                    expressType: kddObj.expressType,
                    expressStyle: kddObj.expressStyle,
                    serviceCode: $(inv).val(),
                    serviceName: $(inv).attr('data-servicename'),
                    serviceValue,
                });
            } else {
                _$dom.length && $.each(_$dom, function() {
                    let $this = $(this); let
_val;
                    // 不要修改_d;
                    const _d = $this.data('service') || {};
                    _val = _d.value;

                    // value 等于输入值
                    if ($this.hasClass('input_service') || _val === 'mixInputVal') {
                        if (_val === 'mixInputVal') {
                            $this = _$service.find('.inputService[name=' + $this.attr('name') + ']');
                        }
                        _val = $this.val();
                        _max = parseFloat($this.attr('data-max'));
                        _min = $this.attr('data-min');
                        if (!_val || ((_min || _min === 0) && _val < parseFloat(_min)) || (_max && _val > parseFloat(_max))) {
                            Tatami.showFail('模板服务中' + _d.name + '输入值不正确！');
                            isError = true;
                            return false;
                        }
                    }
                    _list.push({
                        code: _d.code || $this.attr('data-code'),
                        name: _d.name,
                        type: _d.type,
                        value: _val,
                    });
                });
                if (isError) {
                    return false;
                }

                // 当有多个服务都属于同一种号段下时，如：星联，冷链，同城，文件都属于SVC-STAR号段
                // 将此信息保存下来，在默认选择的时候优先选择SVC-STAR匹配的网点
                const segmentCode = $(inv).attr('data-segmentcode');
                const serviceProperty = segmentCode ? JSON.stringify({
                    segmentCode,
                }) : undefined;

                _service.push({
                    excode: kddObj.excode,
                    excodeName: kddObj.excodeName,
                    expressType: kddObj.expressType,
                    expressStyle: kddObj.expressStyle,
                    serviceCode: $(inv).val(),
                    serviceName: $(inv).attr('data-servicename'),
                    serviceValue: JSON.stringify(_list),
                    serviceProperty,
                });
            }

        });
        return isError ? false : _service;
    };

    // 使Table进入编辑模式
    resObj.setTableForEdit = function(dom) {
        if (dom.length == 0) {
            return;
        }
        dom.attr('class', 'float_l');
        const table = dom[0];
        const len = table.rows[0].cells.length;
        let html = '<tr>';
        const row = table.rows[0];
        for (let i = 0; i < len; i++) {
            const td = row.cells[i];
            if (td.width) {
                html += '<th width=\'' + td.width + '\' index=\'' + i + '\'><div class=\'move_box\'>';
            } else {
                html += '<th index=\'' + i + '\'><div class=\'move_box\'>';
            }
            if (i != 0) {
                html += '<i class=\'left_side\'></i> ';
            }
            if (i != (len - 1)) {
                html += '<i class=\'right_side\'></i> ';
            }
            html += '<i class=\'delete_icon_white\'></i> ';
            html += '</div></th>';
            let tdHtml = td.innerHTML;
            tdHtml = '<div class="td_title"><i class="tuo_icon resizeDivClass"></i>' + tdHtml + '</div>';
            td.innerHTML = tdHtml;
        }
        html += '</tr>';
        const firstRow = dom.find('tr:first');
        $(html).insertBefore(firstRow);
        const div = $('<div class="fh_add_line_box"><div class="td_title"><i class="add_line"></i></div></div>');
        div.insertAfter(dom);
    };

    // 将ProValue转化成对象
    resObj.getObjectFormProValue = function(val) {
        const arr = (val || '').split(/,|，/);
        const obj = {};
        arr.each(function(ind, inv) {
            const itemArr = inv.split(/＝|=/);
            const proName = itemArr[0];
            const proVal = itemArr[1];
            if (proVal) {
                obj[proName] = proVal;
            } else {
                obj[proName] = '';
            }
        });
        obj.toString = function() {
            const str = [];
            for (const name in this) {
                if (!name) {
                    continue;
                }
                const ins = [];
                ins.push(name);
                const val = this[name];
                if (typeof (val) === 'function') {
                    continue;
                }
                if (typeof (val) !== 'undefined') {
                    ins.push(val);
                } else {
                    ins.push('');
                }
                str.push(ins.join('='));
            }
            return str.join('，');
        };
        return obj;
    };

    // 拣货单主界面
    resObj.showJHDMain = function(isNoShow) {
        const that = new comp.Print.FN();
        if (mergeCacheObj.showJHDMainObj) {
            if (isNoShow) {
                return;
            }
            const currentObj = mergeCacheObj.showJHDMainObj;
            currentObj.qlrr.init();
            currentObj.qlrr.show();
            return currentObj;
        }
        const html = '<div id="div_jhd" class="jhdmaindiv" style="display: none;"><div class="w_tk clearfix"><div class="w_close"><i name="close" class="delete_mid_icon" style="right:0;"></i></div><div class="w_middle"><div class="w_tk_left" id="div_jhd_left">'
            + '<style type="text/css">ul,li{list-style: none;} em,i{font-style: normal;} .w_tk{width:936px;} .w_tk_left{height: 518px;float: left;padding:0.71cm; background-color: #fff; overflow-y: scroll;width:727px} .w_tk_right{float: right;width: 155px;background-color: #323232;height: 571px;*height: 571px;} .w_table{width: 710px;border-collapse: collapse; } td.w_td1{width:6%;} td.w_td2{width:38%;} td.w_td3{width:46%;} td.w_td1,td.w_td2,td.w_td3,td.w_td4{height: 40px;text-align: center;font-size: 12px;font-weight: 600;color: #323232;} .w_table h1{float: left;margin: 10px 0px 10px 10px;width: 82px;height: 82px;border: 1px solid #dfdfdf;} .w_tk_ul{ width:161px;float: left;margin: 10px 0px 0px 10px;padding:0px;} .w_tk_ul li{font-size: 12px;color: #000000;;line-height: 24px;word-break: break-word; word-wrap: break-word;} .w_tk_ul2{margin-left: 20px;padding:0px;} .w_tk_ul2 li{line-height: 26px;font-size: 12px;color: #000000;} '
            + '.w_tk_ul2 li .w_tk_span1{display: inline-block;width: 40%;text-align: left;} .w_tk_ul2 li .w_tk_span1.jhd-three-cols{width: 26%;}  .w_tk_ul2 li .w_tk_span2{display: inline-block;width:40%;text-align: left;}   .w_tk_ul2 li .w_tk_span2.jhd-three-cols{width:26%;}  .w_tk_ul2 li .w_tk_span3{display: inline-block;width:43px;text-align: right;} .w_tk_ul2 li em,.w_tk_em{font-size: 16px;color: #e85f1c; margin-right: 2px;} .w_tk_td01{text-align: center;color: #000000;} .w_tk_xh{text-align: center;} .w_tk_tr01{border-top: 3px solid #dfdfdf;} .w_tk_p1{text-align: right;width:697px;font-size:14px;margin-top: 12px;} .w_tk_p1 span{font-size: 16px;color: #ff0000;display: inline-block; } .w_tk_sx{border: 1px solid #dddddd;} .w_tk_xx{border:1px dashed #dddddd;} .w_close{height: 40px;} .w_close img{float: right;cursor: pointer;} /*热敏*/ .w_rm_tk .w_tk_ul2 li .w_tk_span3{width: 42px;} .w_rm_tk .w_tk_ul {width: 61px;margin:0px;} .w_rm_tk .w_tk_ul2 li .w_tk_span1{width:84px;float:left;} .w_rm_tk .w_tk_ul2 li{clear:both;overflow:hidden;} .w_rm_tk .w_tk_ul2 li .w_tk_span2{width:84px;float:left;overflow:hidden;} .w_rm_tk td.w_td1{width:42px;font-size:12px;} .w_rm_tk td.w_td2{width:64px;font-size:12px;} .w_rm_tk td.w_td3{width:139px;font-size:12px;} .w_rm_tk td.w_td4{width:57px;font-size:12px;} .w_rm_tk .w_tk_p1{width:227px;} .w_rm_tk{width: 300px;} .w_rm_tk .w_tk_ul2{width:135px;margin:0;} /*实线*/ .w_tk_sx{border:1px solid #000000;} .w_tk_sx tr{border:1px solid #ddd;border-collapse:collapse;} .w_tk_sx .w_tk_tr01{border-top:3px solid #000000;} .w_tk_sx tr td{border:1px solid #000000;border-collapse:collapse;} /*虚线*/ .w_tk_xx{border:none;} .w_tk_xx td{border-bottom:1px dashed rgb(0,0,0);} .w_tk_xx .w_tk_tr01{border:none;} .w_tk_xx{border:none;} .w_tk_xx .w_tk_tr02{border-bottom: 1px dashed rgb(0,0,0);} /*无线*/ .w_wx_tk1 tr td{border:none;} .w_wx_tk1{border:none;} .w_wx_tk1 tr{border:none;} .w_wx_tk1 .w_tk_tr01{border:none;}</style><table cellspacing="0" cellpadding="0" class="w_table  w_tk_sx"><tr class="w_tk_tr01 w_tk_tr02"><td class="w_td1">序号</td><td class="w_td2">宝贝标题</td><td class="w_td3">宝贝规格</td><td class="w_td4">总数量</td></tr><tbody id="tb_jhd_list"></tbody></table><p class="w_tk_p1" id="p_jhd_pl">共&nbsp;<span id="sp_jhd_goodssum">0</span>件&nbsp;</p></div><div class="w_tk_right"><h1 id="h_jhdprint"></h1><div class="w_tk_rm"><dl><dt>打印纸张样式：</dt><dd><label for="8"><input type="radio" class="ra_jhdset" name="ra_zz" id="ra_zz_1" value="1" tvalue="1" na="zz" val="1">A4纸</label></dd><dd><label for="9"><input type="radio" class="ra_jhdset" name="ra_zz" id="ra_zz_0" tvalue="0" value="0" na="zz" val="0">热敏纸</label></dd></dl><dl><dt>打印内容：</dt><dd><label for="1"><input type="checkbox" class="ra_jhdset" name="ck_nr" id="ck_name" value="1" na="nr" val="name">宝贝名称</label></dd><dd><label for="1"><input type="checkbox" class="ra_jhdset" name="ck_nr" id="ck_jc" value="1" na="nr" val="jc">宝贝简称</label></dd><dd><label for="2"><input type="checkbox" class="ra_jhdset" name="ck_nr" id="ck_bm" value="1" na="nr" val="bm">商家编码</label></dd><dd><label for="3"><input type="checkbox" class="ra_jhdset" name="ck_nr" id="ck_img" value="1" na="nr" val="img">宝贝图片</label></dd>'
            + '<dd><label for="4"><input type="checkbox" class="ra_jhdset" name="ck_nr" id="ck_ggname" value="1" na="nr" val="ggname">宝贝规格名称</label></dd>'
            + '<dd><label for="4"><input type="checkbox" class="ra_jhdset" name="ck_nr" id="ck_ggAlias" value="1" na="nr" val="ggAlias">宝贝规格别名</label></dd>'
            + '<dd><label for="4"><input type="checkbox" class="ra_jhdset" name="ck_nr" id="ck_gg" value="1" na="nr" val="gg">宝贝规格编码</label></dd>'
            + '</dl><dl><dt>打印样式：</dt><dd><label for="5"><input type="radio" class="ra_jhdset" name="ra_line" value="0" id="ra_line_0" na="line" val="0">无</label></dd><dd><label for="6"><input type="radio" class="ra_jhdset" name="ra_line" value="1" id="ra_line_1" na="line" val="1">实线</label></dd><dd><label for="7"><input type="radio" class="ra_jhdset" name="ra_line" value="2" id="ra_line_2" na="line" val="2">虚线</label></dd></dl><p id="p_jhdupdate">保存</p></div></div></div></div></div>';
        const tagCount = 8;
        const body = $('body');
        const domData = {};
        const domid = 'div_jhd';
        let dom = $('#' + domid);
        if (dom.length == 0) {
            dom = $(html).appendTo(body).attr('id', domid);
        }

        dom.qlrr = {};

        dom.qlrr.init = function() {
            domData.jhdSet = comp.Print.Data.jhdSet;
            dom.qlrr.bindData();
        };

        dom.qlrr.bindData = function() {
            const jhdSet = domData.jhdSet;
            $('input[name=\'ra_zz\'][value=' + jhdSet.PrintType + ']').prop('checked', true);
            $('#ck_name').prop('checked', jhdSet.IsName == 1);
            $('#ck_img').prop('checked', jhdSet.IsImg == 1);
            $('#ck_bm').prop('checked', jhdSet.IsBM == 1);
            $('#ck_jc').prop('checked', jhdSet.IsJC == 1);
            $('#ck_gg').prop('checked', jhdSet.IsGG == 1);
            $('#ck_ggname').prop('checked', jhdSet.IsGGName == 1);
            $('#ck_ggAlias').prop('checked', jhdSet.IsSkuAlias == 1);
            $('input[name=\'ra_line\'][value=' + jhdSet.LineType + ']').prop('checked', true);
            dom.qlrr.bindTable();
        };

        dom.qlrr.bindTable = function() {
            const goodsJHDs = [];
            for (let i = 0; i < 4; i++) {
                const goods = {};
                goods.PID = '123456' + i;
                goods.Name = '淘宝商品淘宝商品' + i;
                goods.Jc = '商品' + i;
                goods.Img = '/resources/img/print/w_tk01.png';
                goods.Bm = 'TBSP' + i;
                goods.Skus = [];
                for (let j = 0; j < 3; j++) {
                    const goodsku = {};
                    goodsku.SID = '123456' + i + j;
                    goodsku.Name = '红色 M' + i + j;
                    goodsku.ggAlias = '红1 M' + i + j;
                    goodsku.Bm = 'TBREDM' + i + j;
                    goodsku.Sum = i + j;
                    goods.Skus.push(goodsku);
                }
                goodsJHDs.push(goods);
            }
            resObj.createTableHtml(goodsJHDs, domData.jhdSet, 1);
        };




        dom.qlrr.show = function() {
            that.showDialog(domid);
        };

        dom.qlrr.close = function() {
            that.closeDialog(domid);
        };



        $('.delete_mid_icon').click(function() {
            dom.qlrr.close();
        });

        $('#p_jhdupdate').bind('click', function() {
            const tar = $(this);
            if (tar.html() == '保存中') {
                return;
            }
            tar.html('保存中');
            const tempjhd = {};
            tempjhd.PrintType = $('input[name=ra_zz]:checked').val();
            tempjhd.IsName = $('#ck_name').prop('checked') ? 1 : 0;
            tempjhd.IsImg = $('#ck_img').prop('checked') ? 1 : 0;
            tempjhd.IsBM = $('#ck_bm').prop('checked') ? 1 : 0;
            tempjhd.IsJC = $('#ck_jc').prop('checked') ? 1 : 0;
            tempjhd.IsGG = $('#ck_gg').prop('checked') ? 1 : 0;
            tempjhd.IsGGName = $('#ck_ggname').prop('checked') ? 1 : 0;
            tempjhd.IsSkuAlias = $('#ck_ggAlias').prop('checked') ? 1 : 0;
            tempjhd.LineType = $('input[name=ra_line]:checked').val();
            that.updateJhdSet(tempjhd, function(jhdSet) {
                domData.jhdSet = jhdSet; // mm-modify
                Tatami.showFail('保存成功');
                tar.html('保存');
            }, function(msg) {
                Tatami.showFail('保存失败请重试!');
                tar.html('保存');
            });
        });

        $('.ra_jhdset').bind('click', function() {
            resObj.setJhdClass($(this).attr('na'), $(this).attr('val'), domData.jhdSet);
        });

        $('#h_jhdprint').bind('click', function() {
            // 打印拣货单内容
            const h = document.getElementById('div_jhd_left').clientHeight + 400;
            if (!that.getHiddenLodop()) {
                const isUseNewBrowser = comp.print.lodop.getChrome();
                if (isUseNewBrowser) {
                    resObj.installClodopFor84ChromeDialog();
                } else {
                    resObj.installDialog();
                }
            } else {
                that.printHtml(document.getElementById('div_jhd_left').innerHTML, (document.getElementById('div_jhd_left').clientWidth + 50), h, 1, false, true, 'jhd', null);
            }
        });

        if (isNoShow) {
            return;
        }

        dom.qlrr.init();
        dom.qlrr.show();
        mergeCacheObj.showJHDMainObj = dom;
        return dom;
    };

    // 创建拣货单或备货单的Html
    resObj.createTableHtml = function(goodsJHDs, jhdSet, isTest) {
        resObj.setJhdClass('line', jhdSet.LineType, jhdSet);
        $('#tb_jhd_list').html('');
        let jhdgoodssum = 0;
        let temHtml = '';
        const isGGName = jhdSet.IsGGName == 1;
        const isImg = jhdSet.IsImg == 1;
        const isName = jhdSet.IsName == 1;
        const isJC = jhdSet.IsJC == 1;
        const isBM = jhdSet.IsBM == 1;
        const isGG = jhdSet.IsGG == 1;
        const isPrintType = jhdSet.PrintType == 1;

        const isThreeCols = jhdSet.IsSkuAlias && isGGName && isGG || isTest ? 'jhd-three-cols' : '';

        let styleGGName = '';
        if (!isGGName) {
            styleGGName = 'style="display:none"';
        }

        let styleImg = '';
        if ((!isImg) || (!isPrintType)) {
            styleImg = 'style="display:none"';
        }

        let styleName = '';
        if (!isName) {
            styleName = 'style="display:none"';
        }

        let styleJC = '';
        if (!isJC) {
            styleJC = 'style="display:none"';
        }

        let styleBM = '';
        if (!isBM) {
            styleBM = 'style="display:none"';
        }

        let styleGG = '';
        if (!isGG) {
            styleGG = 'style="display:none"';
        }
        let styleGGAlias = '';
        if (!jhdSet.IsSkuAlias) {
            styleGGAlias = 'style="display:none"';
        }

        goodsJHDs.each(function(j, data) {
            let goodsum = 0;
            temHtml += '<tr><td class="w_tk_xh">' + (j + 1) + '</td><td>';
            temHtml += '<h1 class="p_img"' + styleImg + '><img  width="84" height="84" src="' + data.Img + '"></h1>';
            temHtml += '<ul class="w_tk_ul">';
            temHtml += '<li class="p_name"' + styleName + '>' + data.Name + '</li>';
            temHtml += '<li class="p_jc"' + styleJC + '>' + data.Jc + '</li>';
            temHtml += '<li class="p_bm"' + styleBM + '>' + data.Bm + '</li>';
            temHtml += '</ul></td>';
            temHtml += '<td><ul class="w_tk_ul2">';

            data.Skus.each(function(ind, item) {
                temHtml += '<li><span class=\'w_tk_span1 p_ggname ' + isThreeCols + '  \'' + styleGGName + '>' + item.Name + '</span>'
                    + '<span class=\'w_tk_span1 p_ggAlias  ' + isThreeCols + ' \'' + styleGGAlias + '>' + item.ggAlias + '</span>'
                    + '<span class=\'w_tk_span2 p_gg ' + isThreeCols + ' \'' + styleGG + '>' + item.Bm + '</span>'
                    + '<span class=\'w_tk_span3\' ><em>' + item.Sum + '</em>件</span></li>';
                goodsum += item.Sum;
            });
            temHtml += '</ul></td>';
            temHtml += '<td class="w_tk_td01"><span><em class="w_tk_em">' + goodsum + '</em>件</span></td></tr>';
            jhdgoodssum += goodsum;
        });

        resObj.setJhdClass('zz', jhdSet.PrintType, jhdSet);
        $('#tb_jhd_list').html(temHtml);
        $('#sp_jhd_goodssum').html(jhdgoodssum);
    };

    // 设置拣货单类
    resObj.setJhdClass = function(type, key, jhdSet) {
        if (type == 'zz') {
            const $img = $('#ck_img');
            if (key == '0') {
                $img.is(':checked') ? $img.attr('ischk', 1) : $img.attr('ischk', 0);
                $img.prop('checked', false).prop('disabled', true);
                $('.p_img').hide();
                $('.w_table').addClass('w_rm_tk');
                $('#p_jhd_pl').addClass('w_rm_tk');
                $('.w_table').attr('style', 'width:300px;');
            } else {
                $img.prop('disabled', false);
                if ($img.attr('ischk') == 1) {
                    $img.prop('checked', true);
                    $('.p_img').show();
                }
                $('.w_table').removeClass('w_rm_tk');
                $('#p_jhd_pl').removeClass('w_rm_tk');
                $('.w_table').removeAttr('style');
            }
        } else if (type == 'nr') {
            if ($('#ck_' + key).prop('checked')) {
                $('.p_' + key).show();
            } else {
                $('.p_' + key).hide();
            }
        } else if (type == 'line') {
            $('.w_table').removeClass('w_wx_tk1 w_tk_sx w_tk_xx');
            if (key == '0') {
                $('.w_table').addClass('w_wx_tk1');
            } else if (key == '1') {
                $('.w_table').addClass('w_tk_sx');
            } else if (key == '2') {
                $('.w_table').addClass('w_tk_xx');
            }
        }
    };

    // 获取模板的背景图
    resObj.getBackGroundImage = function(styleId, isEdit, exCode, exId) {
        let _img; let
isNotSaveBackImg = false;
        // 历史数据兼容，底图路径需要前端指定底图路径的模板: 菜鸟二联 2 +菜鸟三连 15 + 菜鸟便携 21 + 菜鸟快运 22,23
        // 尽量不再加其它styleId了!!! 新增模板底图路径都走接口数据
        if (/^(2|15|21|22|23|31)$/.test(styleId) && exCode != 'CP570969' && exId != '11361') { // CP570969 芝麻开门
            const _func = comp.base.getTempStyle;
            if (_func('bianxie', styleId)) {
                _img = '/resources/img/print/ExImg/bianxie.png';
            } else if (_func('kuaiyun', styleId)) {
                _img = '/resources/img/print/ExImg/' + (isEdit ? 'B_' : '') + 'KuaiYun.png';
            } else if (_func('CNkuaidi', styleId) || styleId == 31) {
                const isSanlian = _func('sanlian', styleId);
                _img = '/resources/img/print/ExImg/' + (isEdit ? 'B' : 'W') + '_Cainiao' + (isSanlian ? 'Sanlian' : '_New') + '.png';
            }
            isNotSaveBackImg = true;
        } else if (exId == '9010' && exCode === 'STO') {
            _img = '/resources/img/print/ExImg/pddYiLian-STO.png';
            isNotSaveBackImg = false;
        }
        return {
            imgSrc: _img,
            isNotSaveBackImg, // 是否 不保存指定的底图路径 ，true：不保存，false：保存
        };
    };
    function updateAddResswarning(currentDom, dom) {
        return new Promise((resolve, reject) => {
            const parentBox = currentDom.parents('.add_list_adrs');
            const shopTitle = parentBox.find('.yzShopName');
            const unlockBtn = dom.find('.unlock');
            if (!shopTitle.find('.unlock').length && unlockBtn.length) {
                model({
                    type: 'confirm',
                    title: '温馨提示',
                    width: 350,
                    minHeight: 160,
                    content: '请先解锁目前正在使用的网点，再进行店铺网点的切换。',
                    cancelHide: true
                });
            } else if (!shopTitle.find('.lock_text').length && dom.find('.lock_text').length) {
                model({
                    type: 'confirm',
                    title: '温馨提示',
                    width: 350,
                    minHeight: 160,
                    content: '您更换不同店铺的网点后，使用该网点的单号将暂时无法打印，为避免重复申请单号打印，请您确认后更换',
                    okCb: () => {
                        resolve();
                    }
                });
            } else {
                resolve();
            }

        });
    }
}());
