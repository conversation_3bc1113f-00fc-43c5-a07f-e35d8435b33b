import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import antdDayjs from 'antd-dayjs-vite-plugin';
import inject from '@rollup/plugin-inject';
import * as fs from 'fs';

const path = require('path');

export default defineConfig(({ command, mode }) => {
	const env = loadEnv(mode, process.cwd(), '');
	// 用来本地环境开启vite https
	let httpsOptions:any = false; // 默认http
	if (env.VITE_HTTPS === 'true') {
		httpsOptions = {
			key: fs.readFileSync('/Users/<USER>/kuaidizs.cn.key'), // https证书统一地址
			cert: fs.readFileSync('/Users/<USER>/kuaidizs.cn.pem'),
		};
	}

	// 保留原来vite代理形式
	let serverObject:any = {
		host: '0.0.0.0', // 开启局域网访问
		proxy: null, // proxy
		open: false, // 自动打开浏览器
	};
	if (env.VITE_HOST === 'localhost') {
		serverObject = {
			host: true,
			proxy: {
				"/api": {
					target: "http://erptest.kuaidizs.cn/",
					changeOrigin: true,
					secure: false,
					rewrite: (path) => path.replace(/^\/api/, ""),
				},
				"/print/center": {
					target: "http://erptest.kuaidizs.cn/",
					changeOrigin: true,
					secure: false,
					rewrite: (path) => {
						return path.replace('', "");
					},
				},
				"/item": {
					target: "http://erptest.kuaidizs.cn/",
					changeOrigin: true,
					secure: false,
					rewrite: (path) => {
						return path;
					},
				},
				"/index": {
					target: "http://erptest.kuaidizs.cn/",
					changeOrigin: true,
					secure: false,
					rewrite: (path) => {
						return path;
					},
				},
				// 添加对erppre.kuaidizs.cn的代理
				// "/erppre-api": {
				// 	target: "https://erppre.kuaidizs.cn/",
				// 	changeOrigin: true,
				// 	secure: false,
				// 	rewrite: (path) => path.replace(/^\/erppre-api/, ""),
				// },
			},
			open: true,
		};
	}

	return {
		build: {
			rollupOptions: {
				input: {
					main: './index.html',
				},
			},
		},
		envPrefix: ['APP_'],
		plugins: [
			react({
				babel: {
					plugins: [
						["@babel/plugin-proposal-decorators", { legacy: true }],
						["@babel/plugin-proposal-class-properties", { loose: true }]
					],
				},
			}),
			antdDayjs(),
			inject({
				'jQuery': 'jquery',
				'$': 'jquery',
				'window.jQuery': 'jquery',
			})
		],
		server: {
			host: serverObject['host'], // 开启局域网访问
			port: 8088,
			open: serverObject['open'], // 自动打开浏览器
			https: httpsOptions, // 是否开启https
			historyApiFallback: true,
			proxy: serverObject['proxy'], // 根据VITE_HOST判断是否走代理
		},
		resolve: {
			// 配置路径别名
			alias: {
				'@': path.resolve(__dirname, './src')
			},

		},
		css: {
			preprocessorOptions: {
				scss: { 
					api: 'modern-compiler',
					silenceDeprecations: ['legacy-js-api']
				},
				less: {
					javascriptEnabled: true, // 注意，这一句是在less对象中，写在外边不起作用
					modifyVars: { // 在这里进行主题的修改，参考官方配置属性
						'primary-color': '#FD8204',
						'link-color': '#FD8204',
						'form-item-margin-bottom': '16px',
						'border-radius-base': '2px'
					},
				}
			}
		},
		define: {
			__APP_ENV__: env.APP_ENV,
			'process.env': env
		},
		assetsInclude: "",
		esbuild: {
			logOverride: { 'this-is-undefined-in-esm': 'silent' }
		},
		optimizeDeps: {
			exclude: ['./large-screen', 'react-dom/client']
		},
	};
});
